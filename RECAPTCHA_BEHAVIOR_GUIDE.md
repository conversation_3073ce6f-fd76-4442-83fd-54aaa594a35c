# Guia do ReCaptchaBehavior

## Transformação Realizada

O componente `ReCaptchaPanel` foi transformado em um `ReCaptchaBehavior` para maior flexibilidade e reutilização.

### ✅ Vantagens do Behavior

1. **Mais Flexível**: Pode ser adicionado a qualquer componente Wicket
2. **Menos Intrusivo**: Não requer estrutura específica de Panel
3. **Reutilizável**: Um behavior pode ser usado em múltiplos componentes
4. **Configurável**: IDs de elementos HTML podem ser customizados
5. **Eventos JavaScript**: Dispara eventos customizados para integração

## 🔧 Como Usar

### Método 1: Usand<PERSON> (Recomendado)

```java
public class MinhaPage extends WebPage {
    private ReCaptchaBehavior reCaptchaBehavior;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form form = new Form("form");
        
        // Adicionar reCAPTCHA ao formulário (método mais simples)
        reCaptchaBehavior = ReCaptchaHelper.addToForm(form);
        
        add(form);
    }
    
    private void validarFormulario() throws ValidacaoException {
        // Validar reCAPTCHA
        ReCaptchaHelper.validate(reCaptchaBehavior);
    }
}
```

### Método 2: Usando Behavior Diretamente

```java
public class MinhaPage extends WebPage {
    private ReCaptchaBehavior reCaptchaBehavior;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form form = new Form("form");
        
        // Configuração manual do behavior
        reCaptchaBehavior = new ReCaptchaBehavior("meu-recaptcha-container", "meu_recaptcha_response");
        form.add(reCaptchaBehavior);
        
        add(form);
    }
    
    private void validarFormulario() throws ValidacaoException {
        if (reCaptchaBehavior.isEnabled() && !reCaptchaBehavior.isCompleted()) {
            throw new ValidacaoException("Complete o reCAPTCHA");
        }
    }
}
```

### Método 3: Adicionando a Qualquer Componente

```java
// Adicionar a um botão
Button button = new Button("meuBotao");
ReCaptchaBehavior behavior = ReCaptchaHelper.addToComponent(button);

// Adicionar a um div
WebMarkupContainer div = new WebMarkupContainer("meuDiv");
ReCaptchaBehavior behavior = ReCaptchaHelper.addToComponent(div);
```

## 📝 HTML Necessário

### HTML Básico (IDs padrão)

```html
<form wicket:id="form">
    <!-- Seus campos aqui -->
    
    <!-- Container para reCAPTCHA -->
    <div class="field">
        <input type="hidden" id="recaptcha_response" name="recaptcha_response" />
        <div id="recaptcha-container"></div>
        <p style="font-size: 12px; color: #666; margin-top: 5px;">
            <span id="recaptcha-status">Carregando verificação...</span>
        </p>
    </div>
    
    <button type="submit">Enviar</button>
</form>
```

### HTML com IDs Customizados

```html
<form wicket:id="form">
    <!-- Container personalizado -->
    <div class="minha-classe-recaptcha">
        <input type="hidden" id="meu_recaptcha_response" name="meu_recaptcha_response" />
        <div id="meu-recaptcha-container"></div>
        <p><span id="meu-recaptcha-status">Carregando...</span></p>
    </div>
</form>
```

```java
// Configurar behavior para IDs customizados
ReCaptchaBehavior behavior = new ReCaptchaBehavior("meu-recaptcha-container", "meu_recaptcha_response");
behavior.setStatusId("meu-recaptcha-status");
form.add(behavior);
```

## 🎯 Exemplos de Uso

### Exemplo 1: Formulário de Contato

```java
public class ContatoPage extends WebPage {
    private ReCaptchaBehavior reCaptchaBehavior;
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        Form<ContatoDTO> form = new Form<>("form", new CompoundPropertyModel<>(new ContatoDTO()));
        
        form.add(new TextField<>("nome"));
        form.add(new TextField<>("email"));
        form.add(new TextArea<>("mensagem"));
        
        // Adicionar reCAPTCHA
        reCaptchaBehavior = ReCaptchaHelper.addToForm(form);
        
        form.add(new Button("enviar") {
            @Override
            public void onSubmit() {
                try {
                    ReCaptchaHelper.validate(reCaptchaBehavior);
                    // Processar formulário...
                    info("Mensagem enviada com sucesso!");
                } catch (Exception e) {
                    error("Erro: " + e.getMessage());
                }
            }
        });
        
        add(form);
    }
}
```

### Exemplo 2: Múltiplos reCAPTCHAs na Mesma Página

```java
public class MultiplosRecaptchaPage extends WebPage {
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        
        // Formulário 1
        Form form1 = new Form("form1");
        ReCaptchaBehavior recaptcha1 = ReCaptchaHelper.addToForm(form1, "recaptcha1-container", "recaptcha1_response");
        add(form1);
        
        // Formulário 2
        Form form2 = new Form("form2");
        ReCaptchaBehavior recaptcha2 = ReCaptchaHelper.addToForm(form2, "recaptcha2-container", "recaptcha2_response");
        add(form2);
    }
}
```

## 🔧 Configuração Avançada

### Eventos JavaScript Customizados

O behavior dispara eventos JavaScript que você pode escutar:

```javascript
// Escutar quando reCAPTCHA é completado
document.addEventListener('recaptchaCompleted', function(event) {
    console.log('reCAPTCHA completado:', event.detail.token);
    console.log('Componente:', event.detail.componentId);
    
    // Habilitar botão de envio, por exemplo
    document.getElementById('btnEnviar').disabled = false;
});

// Escutar quando reCAPTCHA expira
document.addEventListener('recaptchaExpired', function(event) {
    console.log('reCAPTCHA expirado para componente:', event.detail.componentId);
    
    // Desabilitar botão de envio
    document.getElementById('btnEnviar').disabled = true;
});
```

### Configuração Dinâmica

```java
ReCaptchaBehavior behavior = new ReCaptchaBehavior();

// Configurar IDs dinamicamente
behavior.setContainerId("meu-container-" + getId());
behavior.setHiddenFieldId("recaptcha_" + getId());
behavior.setStatusId("status_" + getId());

// Configurar chave específica (sobrescreve configuração do sistema)
behavior.setSiteKey("minha-chave-especifica");

// Habilitar/desabilitar dinamicamente
behavior.setEnabled(isRecaptchaRequired());

component.add(behavior);
```

## 📋 Métodos Disponíveis

### ReCaptchaBehavior

- `isCompleted()` - Verifica se foi completado
- `getRecaptchaResponse()` - Obtém o token de resposta
- `isEnabled()` / `setEnabled(boolean)` - Controla se está habilitado
- `getSiteKey()` / `setSiteKey(String)` - Configura chave do site
- `getContainerId()` / `setContainerId(String)` - ID do container HTML
- `getHiddenFieldId()` / `setHiddenFieldId(String)` - ID do campo oculto
- `getStatusId()` / `setStatusId(String)` - ID do elemento de status

### ReCaptchaHelper

- `addToForm(Form)` - Adiciona ao formulário (IDs padrão)
- `addToForm(Form, String, String)` - Adiciona com IDs customizados
- `addToComponent(Component)` - Adiciona a qualquer componente
- `validate(ReCaptchaBehavior)` - Valida (lança exceção se inválido)
- `validate(ReCaptchaBehavior, boolean)` - Valida com controle de exceção
- `generateHtml()` - Gera HTML necessário
- `createHiddenField(String)` - Cria campo oculto configurado

## 🔄 Migração do ReCaptchaPanel

### Antes (Panel)
```java
ReCaptchaPanel reCaptcha = new ReCaptchaPanel("reCaptcha");
form.add(reCaptcha);

if (reCaptcha.isEnabled() && !reCaptcha.isCompleted()) {
    throw new ValidacaoException("Complete o reCAPTCHA");
}
```

### Depois (Behavior)
```java
ReCaptchaBehavior reCaptchaBehavior = ReCaptchaHelper.addToForm(form);

ReCaptchaHelper.validate(reCaptchaBehavior);
```

## ✅ Benefícios da Transformação

1. **Menos Código**: Helper reduz código boilerplate
2. **Mais Flexível**: Pode ser usado em qualquer componente
3. **Melhor Separação**: Lógica separada da apresentação
4. **Eventos JavaScript**: Integração mais rica com frontend
5. **Configuração Dinâmica**: Todos os aspectos são configuráveis
6. **Múltiplas Instâncias**: Suporte nativo a múltiplos reCAPTCHAs
