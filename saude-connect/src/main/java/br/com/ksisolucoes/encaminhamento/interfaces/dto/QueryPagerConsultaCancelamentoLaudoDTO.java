package br.com.ksisolucoes.encaminhamento.interfaces.dto;

import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaCancelamentoLaudoDTO implements Serializable {
    
    public static final String PROP_LAUDO_TFD = "laudoTfd";

    private LaudoTfd laudoTfd;

    public LaudoTfd getLaudoTfd() {
        return laudoTfd;
    }

    public void setLaudoTfd(LaudoTfd laudoTfd) {
        this.laudoTfd = laudoTfd;
    }

}
