package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoEncaminhamentoProtese;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDocumentoEncaminhamentoProteseDTO implements Serializable{
    
    private DocumentoEncaminhamentoProtese documentoEncaminhamentoProtese;
    private Atendimento atendimento;
    private UsuarioCadsus usuarioCadsus;

    public DocumentoEncaminhamentoProtese getDocumentoEncaminhamentoProtese() {
        return documentoEncaminhamentoProtese;
    }

    public void setDocumentoEncaminhamentoProtese(DocumentoEncaminhamentoProtese documentoEncaminhamentoProtese) {
        this.documentoEncaminhamentoProtese = documentoEncaminhamentoProtese;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
    
}
