package br.com.ksisolucoes.report.hospital.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioReservaLeitoAihDTOParam;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.*;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.RelatorioImpressaoGuiasTissDTOParam;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.report.hospital.HospitalReportBO")
public interface HospitalReportFacade {

    public DataReport relatorioRelacaoAtendimentosHpt(RelatorioRelacaoAtendimentosDTOParam param) throws ReportException;

    public DataReport relatorioMapaDietas(RelatorioMapaDietasDTOParam param) throws ReportException;

    public DataReport relatorioOcupacaoHospitalar(RelatorioOcupacaoHospitalarDTOParam param) throws ReportException;

    public DataReport relatorioLiberacaoLeitos(RelatorioLiberacaoLeitoDTOParam param) throws ReportException;

    public DataReport relatorioMapaLeitos(RelatorioMapaLeitosDTOParam param) throws ReportException;

    public DataReport relatorioFaturamento(RelatorioFaturamentoDTOParam param) throws ReportException;

    public DataReport relatorioAgendamentosCirurgicosAsync(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException;

    public DataReport relatorioAgendamentosCirurgicos(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException;

    public DataReport relatorioExtratoContaFinanceira(RelatorioExtratoContaFinanceiraDTOParam param) throws ReportException;

    public DataReport comprovanteAgendamentosCirurgicos(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoGuiasTiss(RelatorioImpressaoGuiasTissDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoGuiasTissPortrait(RelatorioImpressaoGuiasTissDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoEspelhoAIH(RelatorioImpressaoEspelhoAIHDTOParam param) throws ReportException;

    public DataReport relatorioFilaAih(RelatorioFilaAihDTOParam param) throws ReportException;

    public DataReport relatorioReservaLeitoAih(RelatorioReservaLeitoAihDTOParam param) throws ReportException;

}
