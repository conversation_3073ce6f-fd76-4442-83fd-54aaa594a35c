/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.text.ParseException;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoCartaoIdentificacaoPacienteDTO implements Serializable {
    private EnderecoDomicilio enderecoDomicilio;
    private UsuarioCadsus usuarioCadsus;
    private UsuarioCadsusCns usuarioCadsusCns;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Empresa empresa;
    private EquipeMicroArea equipeMicroArea;
    private EquipeArea equipeArea;
    private String rg;
    private String uf;
    private String cidade;
    private String matricula;
    private Long codigoCidade;
    private Long numeroCartao;


    public String getCodigoCidade() {
        return  new DecimalFormat("000000").format(Coalesce.asLong(codigoCidade));
    }

    public void setCodigoCidade(Long codigoCidade) {
        this.codigoCidade = codigoCidade;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getNumeroCartao() {
        try{
            MaskFormatter m = new MaskFormatter("### #### #### ####");
            m.setValueContainsLiteralCharacters(false);

            return m.valueToString(numeroCartao);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return numeroCartao.toString();
    }

    public void setNumeroCartao(Long numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }
}
