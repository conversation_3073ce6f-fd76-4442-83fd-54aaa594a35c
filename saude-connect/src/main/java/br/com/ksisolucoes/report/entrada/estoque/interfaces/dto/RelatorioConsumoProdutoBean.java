/*
 * RelatorioConsumoProdutoBean .java
 *
 * Created on 25 de Outubro de 2005, 17:06
 */
package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioConsumoProdutoBean implements Comparable, Serializable {

    private String codigoEmpresa;
    private String descricaoEmpresa;
    private String codigoProduto;
    private String descricaoEmpresaDestino;
    private String codigoEmpresaDestino;
    private String descricaoProduto;
    private Long codigoGrupoProduto;
    private String descricaoGrupoProduto;
    private Long codigoSubGrupoProduto;
    private String descricaoSubGrupoProduto;
    private Long codigoCentroCusto;
    private String descricaoCentroCusto;
    private String unidade;
    private Double valorTotalMes1;
    private Double quantidadeMes1;
    private Double valorTotalMes2;
    private Double quantidadeMes2;
    private Double valorTotalMes3;
    private Double quantidadeMes3;
    private Double valorTotalMes4;
    private Double quantidadeMes4;
    private Double valorTotalMes5;
    private Double quantidadeMes5;
    private Double valorTotalMes6;
    private Double quantidadeMes6;
    private Double valorTotalMes7;
    private Double quantidadeMes7;
    private Double valorTotalMes8;
    private Double quantidadeMes8;
    private Double valorTotalMes9;
    private Double quantidadeMes9;
    private Double valorTotalMes10;
    private Double quantidadeMes10;
    private Double valorTotalMes11;
    private Double quantidadeMes11;
    private Double valorTotalMes12;
    private Double quantidadeMes12;
    /**
     * Retorna a descricao do produto formatada no seguinte formato:<br>
     * (<codigo> ) <descricao>
     * @return <code>String</code>
     */
    public String getDescricaoFormatado() {
        return Util.getDescricaoFormatado(
                Coalesce.asString(this.getCodigoProduto()),
                Coalesce.asString(this.getDescricaoProduto()));

    }

    public int compareTo(Object obj) {
        RelatorioConsumoProdutoBean relatorioMapaRecebimentoBean = (RelatorioConsumoProdutoBean) obj;
        int comparacao = this.codigoEmpresa.compareTo(relatorioMapaRecebimentoBean.codigoEmpresa);
        if (comparacao == 0) {
            comparacao = this.descricaoProduto.compareToIgnoreCase(relatorioMapaRecebimentoBean.descricaoProduto);
        }

        return comparacao;
    }

    public int compareToEmpresaDestino(Object obj) {
        RelatorioConsumoProdutoBean relatorioMapaRecebimentoBean = (RelatorioConsumoProdutoBean) obj;
        int comparacaoDestino = this.codigoEmpresaDestino.compareTo(relatorioMapaRecebimentoBean.codigoEmpresaDestino);
        if (comparacaoDestino == 0) {
            comparacaoDestino = this.descricaoProduto.compareToIgnoreCase(relatorioMapaRecebimentoBean.descricaoProduto);
        }

        return comparacaoDestino;
    }

    public Double getValorTotalMes1() {
        return Coalesce.asDouble(valorTotalMes1);
    }

    public void setValorTotalMes1(Double valorTotalMes1) {
        this.valorTotalMes1 = valorTotalMes1;
    }

    public Double getValorTotalMes10() {
        return Coalesce.asDouble(valorTotalMes10);
    }

    public void setValorTotalMes10(Double valorTotalMes10) {
        this.valorTotalMes10 = valorTotalMes10;
    }

    public Double getValorTotalMes11() {
        return Coalesce.asDouble(valorTotalMes11);
    }

    public void setValorTotalMes11(Double valorTotalMes11) {
        this.valorTotalMes11 = valorTotalMes11;
    }

    public Double getValorTotalMes12() {
        return Coalesce.asDouble(valorTotalMes12);
    }

    public void setValorTotalMes12(Double valorTotalMes12) {
        this.valorTotalMes12 = valorTotalMes12;
    }

    public Double getValorTotalMes2() {
        return Coalesce.asDouble(valorTotalMes2);
    }

    public void setValorTotalMes2(Double valorTotalMes2) {
        this.valorTotalMes2 = valorTotalMes2;
    }

    public Double getValorTotalMes3() {
        return Coalesce.asDouble(valorTotalMes3);
    }

    public void setValorTotalMes3(Double valorTotalMes3) {
        this.valorTotalMes3 = valorTotalMes3;
    }

    public Double getValorTotalMes4() {
        return Coalesce.asDouble(valorTotalMes4);
    }

    public void setValorTotalMes4(Double valorTotalMes4) {
        this.valorTotalMes4 = valorTotalMes4;
    }

    public Double getValorTotalMes5() {
        return Coalesce.asDouble(valorTotalMes5);
    }

    public void setValorTotalMes5(Double valorTotalMes5) {
        this.valorTotalMes5 = valorTotalMes5;
    }

    public Double getValorTotalMes6() {
        return Coalesce.asDouble(valorTotalMes6);
    }

    public void setValorTotalMes6(Double valorTotalMes6) {
        this.valorTotalMes6 = valorTotalMes6;
    }

    public Double getValorTotalMes7() {
        return Coalesce.asDouble(valorTotalMes7);
    }

    public void setValorTotalMes7(Double valorTotalMes7) {
        this.valorTotalMes7 = valorTotalMes7;
    }

    public Double getValorTotalMes8() {
        return Coalesce.asDouble(valorTotalMes8);
    }

    public void setValorTotalMes8(Double valorTotalMes8) {
        this.valorTotalMes8 = valorTotalMes8;
    }

    public Double getValorTotalMes9() {
        return Coalesce.asDouble(valorTotalMes9);
    }

    public void setValorTotalMes9(Double valorTotalMes9) {
        this.valorTotalMes9 = valorTotalMes9;
    }

    public String getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getDescricaoEmpresa() {
        return descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public String getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Double getQuantidadeMes1() {
        return Coalesce.asDouble(quantidadeMes1);
    }

    public void setQuantidadeMes1(Double quantidadeMes1) {
        this.quantidadeMes1 = quantidadeMes1;
    }

    public Double getQuantidadeMes10() {
        return Coalesce.asDouble(quantidadeMes10);
    }

    public void setQuantidadeMes10(Double quantidadeMes10) {
        this.quantidadeMes10 = quantidadeMes10;
    }

    public Double getQuantidadeMes11() {
        return Coalesce.asDouble(quantidadeMes11);
    }

    public void setQuantidadeMes11(Double quantidadeMes11) {
        this.quantidadeMes11 = quantidadeMes11;
    }

    public Double getQuantidadeMes12() {
        return Coalesce.asDouble(quantidadeMes12);
    }

    public void setQuantidadeMes12(Double quantidadeMes12) {
        this.quantidadeMes12 = quantidadeMes12;
    }

    public Double getQuantidadeMes2() {
        return Coalesce.asDouble(quantidadeMes2);
    }

    public void setQuantidadeMes2(Double quantidadeMes2) {
        this.quantidadeMes2 = quantidadeMes2;
    }

    public Double getQuantidadeMes3() {
        return Coalesce.asDouble(quantidadeMes3);
    }

    public void setQuantidadeMes3(Double quantidadeMes3) {
        this.quantidadeMes3 = quantidadeMes3;
    }

    public Double getQuantidadeMes4() {
        return Coalesce.asDouble(quantidadeMes4);
    }

    public void setQuantidadeMes4(Double quantidadeMes4) {
        this.quantidadeMes4 = quantidadeMes4;
    }

    public Double getQuantidadeMes5() {
        return Coalesce.asDouble(quantidadeMes5);
    }

    public void setQuantidadeMes5(Double quantidadeMes5) {
        this.quantidadeMes5 = quantidadeMes5;
    }

    public Double getQuantidadeMes6() {
        return Coalesce.asDouble(quantidadeMes6);
    }

    public void setQuantidadeMes6(Double quantidadeMes6) {
        this.quantidadeMes6 = quantidadeMes6;
    }

    public Double getQuantidadeMes7() {
        return Coalesce.asDouble(quantidadeMes7);
    }

    public void setQuantidadeMes7(Double quantidadeMes7) {
        this.quantidadeMes7 = quantidadeMes7;
    }

    public Double getQuantidadeMes8() {
        return Coalesce.asDouble(quantidadeMes8);
    }

    public void setQuantidadeMes8(Double quantidadeMes8) {
        this.quantidadeMes8 = quantidadeMes8;
    }

    public Double getQuantidadeMes9() {
        return Coalesce.asDouble(quantidadeMes9);
    }

    public void setQuantidadeMes9(Double quantidadeMes9) {
        this.quantidadeMes9 = quantidadeMes9;
    }

    public Double getSomaQuantidade() {
        return (getQuantidadeMes1() + getQuantidadeMes2() + getQuantidadeMes3() + getQuantidadeMes4() + getQuantidadeMes5() + getQuantidadeMes6()
                + getQuantidadeMes7() + getQuantidadeMes8() + getQuantidadeMes9() + getQuantidadeMes10() + getQuantidadeMes11() + getQuantidadeMes12());
    }

    public Double getSomaValorTotal() {
        return (getValorTotalMes1() + getValorTotalMes2() + getValorTotalMes3() + getValorTotalMes4() + getValorTotalMes5() + getValorTotalMes6()
                + getValorTotalMes7() + getValorTotalMes8() + getValorTotalMes9() + getValorTotalMes10() + getValorTotalMes11() + getValorTotalMes12());
    }

    public Long getCodigoGrupoProduto() {
        return codigoGrupoProduto;
    }

    public void setCodigoGrupoProduto(Long codigoGrupoProduto) {
        this.codigoGrupoProduto = codigoGrupoProduto;
    }

    public String getDescricaoGrupoProduto() {
        return descricaoGrupoProduto;
    }

    public void setDescricaoGrupoProduto(String descricaoGrupoProduto) {
        this.descricaoGrupoProduto = descricaoGrupoProduto;
    }

    public Long getCodigoSubGrupoProduto() {
        return codigoSubGrupoProduto;
    }

    public void setCodigoSubGrupoProduto(Long codigoSubGrupoProduto) {
        this.codigoSubGrupoProduto = codigoSubGrupoProduto;
    }

    public String getDescricaoSubGrupoProduto() {
        return descricaoSubGrupoProduto;
    }

    public void setDescricaoSubGrupoProduto(String descricaoSubGrupoProduto) {
        this.descricaoSubGrupoProduto = descricaoSubGrupoProduto;
    }

    public String getDescricaoEmpresaDestino() {
        return descricaoEmpresaDestino;
    }

    public String getCodigoEmpresaDestino() {
        return codigoEmpresaDestino;
    }

    public void setDescricaoEmpresaDestino(String descricaoEmpresaDestino) {
        this.descricaoEmpresaDestino = descricaoEmpresaDestino;
    }

    public void setCodigoEmpresaDestino(String codigoEmpresaDestino) {
        this.codigoEmpresaDestino = codigoEmpresaDestino;
    }

    public Long getCodigoCentroCusto() {
        return codigoCentroCusto;
    }

    public void setCodigoCentroCusto(Long codigoCentroCusto) {
        this.codigoCentroCusto = codigoCentroCusto;
    }

    public String getDescricaoCentroCusto() {
        return descricaoCentroCusto;
    }

    public void setDescricaoCentroCusto(String descricaoCentroCusto) {
        this.descricaoCentroCusto = descricaoCentroCusto;
    }

    public String getDescricaoGrupoProdutoFormatado() {
        return Util.getDescricaoFormatado(this.getCodigoGrupoProduto(), Coalesce.asString(this.getDescricaoGrupoProduto()));
    }

    public String getDescricaoSubGrupoProdutoFormatado() {
        return Util.getDescricaoFormatado(this.getCodigoSubGrupoProduto(), Coalesce.asString(this.getDescricaoSubGrupoProduto()));
    }

    public String getDescricaoEmpresaDestinoFormatado() {
        return Util.getDescricaoFormatado(this.getCodigoEmpresaDestino(), Coalesce.asString(this.getDescricaoEmpresaDestino()));
    }

    public String getDescricaoEmpresaOrigemFormatado() {
        return Util.getDescricaoFormatado(this.getCodigoEmpresa(), Coalesce.asString(this.getDescricaoEmpresa()));
    }

    public String getDescricaoCentroCustoFormatado() {
        return Util.getDescricaoFormatado(this.getCodigoCentroCusto(), this.getDescricaoCentroCusto());
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }
}
