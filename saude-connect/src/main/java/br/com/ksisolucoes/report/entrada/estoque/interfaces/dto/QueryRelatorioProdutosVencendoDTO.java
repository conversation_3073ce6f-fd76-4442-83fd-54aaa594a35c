package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProdutosVencendoDTO implements Serializable{

    private GrupoEstoque grupoEstoque;
    private Double valor;
    private Double quantidade;

    public GrupoEstoque getGrupoEstoque() {
        return grupoEstoque;
    }

    public void setGrupoEstoque(GrupoEstoque grupoEstoque) {
        this.grupoEstoque = grupoEstoque;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
    
}
