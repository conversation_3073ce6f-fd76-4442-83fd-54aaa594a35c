package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoTuberculose;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameTuberculoseDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Long nivelEscolaridade;
    private ExameRequisicao exameRequisicao;
    private RequisicaoTuberculose requisicaoTuberculose;

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public Long getNivelEscolaridade() {
        return nivelEscolaridade;
    }

    public void setNivelEscolaridade(Long nivelEscolaridade) {
        this.nivelEscolaridade = nivelEscolaridade;
    }

    public RequisicaoTuberculose getRequisicaoTuberculose() {
        return requisicaoTuberculose;
    }

    public void setRequisicaoTuberculose(RequisicaoTuberculose requisicaoTuberculose) {
        this.requisicaoTuberculose = requisicaoTuberculose;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
}
