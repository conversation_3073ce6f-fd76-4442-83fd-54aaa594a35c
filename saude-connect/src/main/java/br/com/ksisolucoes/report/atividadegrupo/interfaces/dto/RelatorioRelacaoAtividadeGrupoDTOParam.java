package br.com.ksisolucoes.report.atividadegrupo.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.esus.EsusFicha;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.List;

import static br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo.SITUACAO_PENDENTE;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoAtividadeGrupoDTOParam implements Serializable {
    
    public enum FormaApresentacao {
        UNIDADE,
        TIPO_ATIVIDADE,
        PROFISSIONAL,
        CBO;

        @Override
        public String toString() {
            if (this.equals(UNIDADE)) {
                return Bundle.getStringApplication("rotulo_empresa");
            } else if (this.equals(TIPO_ATIVIDADE)) {
                return Bundle.getStringApplication("rotulo_tipo_atividade");
            } else if (this.equals(PROFISSIONAL)) {
                return Bundle.getStringApplication("rotulo_profissional");
            } else if (this.equals(CBO)) {
                return Bundle.getStringApplication("rotulo_cbo");
            }
            return "";
        }

    }
    
    private OperadorValor<List<Empresa>> empresa;
    private List<TipoAtividadeGrupo> tipoAtividadeGrupo;
    private List<Profissional> profissional;
    private List<TabelaCbo> tabelaCbo;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private Long situacao;
    private Long esusFichaSituacao;
    private Long codigo;
    private boolean exibirParticipantes;
    private UsuarioCadsus usuarioCadsus;
    private Long numeroInep;
    private LocalAtividadeGrupo localAtividadeGrupo;
    private ProgramaSaude programaSaude;
    private Long situacaoParticipante;
    private TipoRelatorio tipoArquivo;

    @DescricaoParametro("rotulo_situacao_esus")
    public String getDescricaoEsusFichaSituacao() {
        if (getEsusFichaSituacao() != null) {
            return EsusFicha.EsusFichaSituacao.valueOf(getEsusFichaSituacao()).descricao();
        }
        return "";
    }

    public Long getEsusFichaSituacao() {
        return esusFichaSituacao;
    }

    public void setEsusFichaSituacao(Long esusFichaSituacao) {
        this.esusFichaSituacao = esusFichaSituacao;
    }

    @DescricaoParametro("rotulo_unidade")
    public OperadorValor<List<Empresa>> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(OperadorValor<List<Empresa>> empresa) {
        this.empresa = empresa;
    }
    
    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_tipo_atividade")
    public List<TipoAtividadeGrupo> getTipoAtividadeGrupo() {
        return tipoAtividadeGrupo;
    }

    public void setTipoAtividadeGrupo(List<TipoAtividadeGrupo> tipoAtividadeGrupo) {
        this.tipoAtividadeGrupo = tipoAtividadeGrupo;
    }

    @DescricaoParametro("rotulo_profissional")
    public List<Profissional> getProfissional() {
        return profissional;
    }

    public void setProfissional(List<Profissional> profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_cbo")
    public List<TabelaCbo> getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(List<TabelaCbo> tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }
    
    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        if(getSituacao() != null){
            if(SITUACAO_PENDENTE.equals(getSituacao())){
                return Bundle.getStringApplication("rotulo_pendente");
            }else {
                return Bundle.getStringApplication("rotulo_concluida");
            }
        } else {
            return Bundle.getStringApplication("rotulo_ambos");
        }
    }

    @DescricaoParametro("rotulo_codigo")
    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    @DescricaoParametro("rotulo_exibir_participantes")
    public boolean isExibirParticipantes() {
        return exibirParticipantes;
    }

    public void setExibirParticipantes(boolean exibirParticipantes) {
        this.exibirParticipantes = exibirParticipantes;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @DescricaoParametro("rotulo_paciente")
    public String getNomePaciente() {
        if(usuarioCadsus != null){
            return usuarioCadsus.getNome();
        } else {
            return "";
        }
    }

    @DescricaoParametro("rotulo_numero_inep")
    public Long getNumeroInep() {
        return numeroInep;
    }

    public void setNumeroInep(Long numeroInep) {
        this.numeroInep = numeroInep;
    }

    public LocalAtividadeGrupo getLocalAtividadeGrupo() {
        return localAtividadeGrupo;
    }

    public void setLocalAtividadeGrupo(LocalAtividadeGrupo localAtividadeGrupo) {
        this.localAtividadeGrupo = localAtividadeGrupo;
    }

    @DescricaoParametro("rotulo_grupo_paciente")
    public ProgramaSaude getProgramaSaude() {
        return programaSaude;
    }

    public void setProgramaSaude(ProgramaSaude programaSaude) {
        this.programaSaude = programaSaude;
    }

    @DescricaoParametro("rotulo_situacao_participante")
    public String getDescricaoSituacaoParticipante() {
        if(getSituacaoParticipante() != null){
            if(AtividadeGrupoPaciente.Situacao.PRESENTE.value().equals(getSituacaoParticipante())){
                return Bundle.getStringApplication("rotulo_presente");
            }else {
                return Bundle.getStringApplication("rotulo_ausente");
            }
        } else {
            return Bundle.getStringApplication("rotulo_ambos");
        }
    }

    public Long getSituacaoParticipante() {
        return situacaoParticipante;
    }

    public void setSituacaoParticipante(Long situacaoParticipante) {
        this.situacaoParticipante = situacaoParticipante;
    }

    @DescricaoParametro("rotulo_tipo_arquivo")
    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }
}
