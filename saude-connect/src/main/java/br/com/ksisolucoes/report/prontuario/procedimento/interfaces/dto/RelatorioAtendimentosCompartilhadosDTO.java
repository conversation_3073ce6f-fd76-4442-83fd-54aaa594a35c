/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAtendimentosCompartilhadosDTO implements Serializable{

    private Long numeroAtendimento;
    private Empresa empresa;
    private Date dataAtendimento;
    private UsuarioCadsus usuarioCadsus;
    private Profissional profissional;
    private Profissional profissionalAuxiliar;
    private TabelaCbo tabelaCbo;
    private TabelaCbo tabelaCboAuxiliar;

    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Profissional getProfissionalAuxiliar() {
        return profissionalAuxiliar;
    }

    public void setProfissionalAuxiliar(Profissional profissionalAuxiliar) {
        this.profissionalAuxiliar = profissionalAuxiliar;
    }

    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public TabelaCbo getTabelaCboAuxiliar() {
        return tabelaCboAuxiliar;
    }

    public void setTabelaCboAuxiliar(TabelaCbo tabelaCboAuxiliar) {
        this.tabelaCboAuxiliar = tabelaCboAuxiliar;
    }
}
