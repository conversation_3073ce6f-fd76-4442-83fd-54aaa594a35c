/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.geral.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.basico.pesquisa.dto.GraficoResultadoPesquisaDTOParam;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.dto.ReciboProdutoSolicitadoDTO;
import br.com.ksisolucoes.report.basico.interfaces.dto.ReciboProdutoSolicitadoDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.geral.despesa.RelatorioDespesasDTOParam;
import br.com.ksisolucoes.report.geral.interfaces.dto.*;
import br.com.ksisolucoes.report.geral.pesquisa.interfaces.dto.GraficoResultadoPesquisaPorRespostaDTOParam;
import br.com.ksisolucoes.report.geral.pesquisa.interfaces.dto.RelatorioCruzamentoPerguntasDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisao;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.report.geral.GeralReportBO")
public interface GeralReportFacade extends FacadeBO {

    public DataReport getRelatorioIdentificacaoNivelSuperior(RelatorioIdentificacaoNivelSuperiorDTO bean) throws ReportException;

    public DataReport getRelatorioEstruturaEquipamento(RelatorioEstruturaEquipamentoDTO bean) throws ReportException;

    public List<EstruturaEquipamentoRevisao> getListControleRevisaoEstruturaDTO(Produto produto) throws DAOException, ValidacaoException;

    public DataPagingResult<ReciboProdutoSolicitadoDTO> consultaProdutoSolicitadoMovimento(DataPaging<ReciboProdutoSolicitadoDTOParam> param) throws DAOException, ValidacaoException;

    public DataReport reciboEntregaMedicamentoSolicitado(ReciboProdutoSolicitadoDTOParam param) throws ReportException;

    public DataReport relacaoFamiliasCadastradas(QueryRelacaoFamiliasCadastradasDTOParam param) throws ReportException;

    public DataReport relacaoMovimentacaoFamilias(QueryRelacaoMovimentacaoFamiliasDTOParam param) throws ReportException;

    public DataReport relacaoDespesas(RelatorioDespesasDTOParam param) throws ReportException;

    public DataReport graficoResultadoPesquisa(GraficoResultadoPesquisaDTOParam param) throws ReportException;

    public DataReport graficoResultadoPesquisaPorResposta(GraficoResultadoPesquisaPorRespostaDTOParam param) throws ReportException;

    public DataReport cruzamentoPerguntas(RelatorioCruzamentoPerguntasDTOParam param) throws ReportException;

    public DataReport relatorioPacientesSemCns(QueryRelatorioPacientesSemCnsDTOParam param) throws ReportException;

    public DataReport impressaoEtiquetaPaciente(ImpressaoEtiquetaPacienteDTOParam param) throws ReportException;

    public DataReport impressaoMensagemInterna(String mensagem) throws ReportException;
}
