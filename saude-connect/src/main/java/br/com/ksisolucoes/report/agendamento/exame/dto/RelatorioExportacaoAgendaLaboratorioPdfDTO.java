package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;

import java.io.Serializable;

public class RelatorioExportacaoAgendaLaboratorioPdfDTO implements Serializable {

    private String estabelecimento;
    private Long codigoUnidade;
    private String descricaoUnidade;
    private String dataInicio;
    private String dataFim;
    private String unidadeExecutante;
    private Long solicitacao;
    private String codigoInterno;
    private String codigoUnificado;
    private String descricaoProcedimento;
    private String nomeProfissionalExecutante;
    private String dataAgendamento;
    private String hrAgendamento;
    private Long cns;
    private String nome;
    private String dtNascimento;
    private Long idade;
    private String tipoLogradouro;
    private String logradouro;
    private String complemento;
    private String numeroLogradouro;
    private String bairro;
    private String cep;
    private String telefone;
    private String municipio;
    private String munSolicitante;
    private String unidadeFantasia;
    private String sexo;
    private String dataSolicitacao;
    private String dataAutorizacao;
    private String valorProcedimento;
    private String situacao;
    private String cpfProfissionalSolicitante;
    private String nomeProfissionalSolicitante;
    private String line;
    private Long codigoRequisicaoExame;
    private String nomeSocial;
    private String empresaSolicitante;
    private ExameProcedimento exameProcedimento;

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public Long getCodigoUnidade() {
        return codigoUnidade;
    }

    public void setCodigoUnidade(Long codigoUnidade) {
        this.codigoUnidade = codigoUnidade;
    }

    public String getDescricaoUnidade() {
        return descricaoUnidade;
    }

    public void setDescricaoUnidade(String descricaoUnidade) {
        this.descricaoUnidade = descricaoUnidade;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public String getUnidadeExecutante() {
        return unidadeExecutante;
    }

    public void setUnidadeExecutante(String unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
    }

    public Long getSolicitacao() {
        return solicitacao;
    }

    public void setSolicitacao(Long solicitacao) {
        this.solicitacao = solicitacao;
    }

    public String getCodigoInterno() {
        return codigoInterno;
    }

    public void setCodigoInterno(String codigoInterno) {
        this.codigoInterno = codigoInterno;
    }

    public String getCodigoUnificado() {
        return codigoUnificado;
    }

    public void setCodigoUnificado(String codigoUnificado) {
        this.codigoUnificado = codigoUnificado;
    }

    public String getDescricaoProcedimento() {
        return descricaoProcedimento;
    }

    public void setDescricaoProcedimento(String descricaoProcedimento) {
        this.descricaoProcedimento = descricaoProcedimento;
    }

    public String getNomeProfissionalExecutante() {
        return nomeProfissionalExecutante;
    }

    public void setNomeProfissionalExecutante(String nomeProfissionalExecutante) {
        this.nomeProfissionalExecutante = nomeProfissionalExecutante;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getHrAgendamento() {
        return hrAgendamento;
    }

    public void setHrAgendamento(String hrAgendamento) {
        this.hrAgendamento = hrAgendamento;
    }

    public Long getCns() {
        return cns;
    }

    public void setCns(Long cns) {
        this.cns = cns;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDtNascimento() {
        return dtNascimento;
    }

    public void setDtNascimento(String dtNascimento) {
        this.dtNascimento = dtNascimento;
    }

    public Long getIdade() {
        return idade;
    }

    public void setIdade(Long idade) {
        this.idade = idade;
    }

    public String getTipoLogradouro() {
        return tipoLogradouro;
    }

    public void setTipoLogradouro(String tipoLogradouro) {
        this.tipoLogradouro = tipoLogradouro;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getNumeroLogradouro() {
        return numeroLogradouro;
    }

    public void setNumeroLogradouro(String numeroLogradouro) {
        this.numeroLogradouro = numeroLogradouro;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getMunicipio() {
        return municipio;
    }

    public void setMunicipio(String municipio) {
        this.municipio = municipio;
    }

    public String getMunSolicitante() {
        return munSolicitante;
    }

    public void setMunSolicitante(String munSolicitante) {
        this.munSolicitante = munSolicitante;
    }

    public String getUnidadeFantasia() {
        return unidadeFantasia;
    }

    public void setUnidadeFantasia(String unidadeFantasia) {
        this.unidadeFantasia = unidadeFantasia;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getDataSolicitacao() {
        return dataSolicitacao;
    }

    public void setDataSolicitacao(String dataSolicitacao) {
        this.dataSolicitacao = dataSolicitacao;
    }

    public String getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(String dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public String getValorProcedimento() {
        return valorProcedimento;
    }

    public void setValorProcedimento(String valorProcedimento) {
        this.valorProcedimento = valorProcedimento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getCpfProfissionalSolicitante() {
        return cpfProfissionalSolicitante;
    }

    public void setCpfProfissionalSolicitante(String cpfProfissionalSolicitante) {
        this.cpfProfissionalSolicitante = cpfProfissionalSolicitante;
    }

    public String getNomeProfissionalSolicitante() {
        return nomeProfissionalSolicitante;
    }

    public void setNomeProfissionalSolicitante(String nomeProfissionalSolicitante) {
        this.nomeProfissionalSolicitante = nomeProfissionalSolicitante;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public Long getCodigoRequisicaoExame() {
        return codigoRequisicaoExame;
    }

    public void setCodigoRequisicaoExame(Long codigoRequisicaoExame) {
        this.codigoRequisicaoExame = codigoRequisicaoExame;
    }

    public String getNomeSocial() {
        return nomeSocial;
    }

    public void setNomeSocial(String nomeSocial) {
        this.nomeSocial = nomeSocial;
    }

    public String getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(String empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }
}
