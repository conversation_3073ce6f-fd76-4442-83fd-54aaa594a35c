package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoImunologia;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameImunologiaDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private ExameRequisicao exameRequisicao;
    private RequisicaoImunologia requisicaoImunologia;

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public RequisicaoImunologia getRequisicaoImunologia() {
        return requisicaoImunologia;
    }

    public void setRequisicaoImunologia(RequisicaoImunologia requisicaoImunologia) {
        this.requisicaoImunologia = requisicaoImunologia;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
}