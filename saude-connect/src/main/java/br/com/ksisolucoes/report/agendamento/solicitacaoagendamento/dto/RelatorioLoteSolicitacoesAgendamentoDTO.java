/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */




package br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.dto;

import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioLoteSolicitacoesAgendamentoDTO implements Serializable {

    private SolicitacaoAgendamento solicitacaoAgendamento;
    private LoteSolicitacaoAgendamento loteSolicitacaoAgendamento;
    private TipoProcedimento tipoProcedimento;
    private Profissional profissional;
    private Empresa empresa;
    private Empresa empresaDestino;
    private LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem;
    private UsuarioCadsus usuarioCadsus;
    private Double quantidade;

    public SolicitacaoAgendamento getSolicitacaoAgendamento() {
        return solicitacaoAgendamento;
    }

    public void setSolicitacaoAgendamento(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    public LoteSolicitacaoAgendamento getLoteSolicitacaoAgendamento() {
        return loteSolicitacaoAgendamento;
    }

    public void setLoteSolicitacaoAgendamento(LoteSolicitacaoAgendamento loteSolicitacaoAgendamento) {
        this.loteSolicitacaoAgendamento = loteSolicitacaoAgendamento;
    }

    public LoteSolicitacaoAgendamentoItem getLoteSolicitacaoAgendamentoItem() {
        return loteSolicitacaoAgendamentoItem;
    }

    public void setLoteSolicitacaoAgendamentoItem(LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem) {
        this.loteSolicitacaoAgendamentoItem = loteSolicitacaoAgendamentoItem;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public Profissional getProfissional() {
        return profissional;
    } 

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Empresa getEmpresaDestino() {
        return empresaDestino;
    }

    public void setEmpresaDestino(Empresa empresaDestino) {
        this.empresaDestino = empresaDestino;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

}
