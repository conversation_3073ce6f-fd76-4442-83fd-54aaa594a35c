/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioBoletimProcedimentoDTOParam implements Serializable {

    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<Profissional>> profissionals;
    private OperadorValor<List<Procedimento>> procedimento;
    private OperadorValor<List<UsuarioCadsus>> usuariosCadsus;
    private OperadorValor<List<TabelaCbo>> tabelasCbo;
    private TabelaCboSubGrupo tabelaCboSubGrupo;
    private String formaApresentacao;
    private Date mesInicial;
    private Date mesFinal;
    private String agrupar;
    private String tipoProcedimento;
    private String agruparEmpresa;
    private Long apenasDengue;
    private OperadorValor<List<Convenio>> convenio;
    private TipoRelatorio tipoArquivo;
    private SituacaoProducao situacaoProducao;

    public static enum SituacaoProducao {
        
        FECHADA(Bundle.getStringApplication("rotulo_fechada")),
        ABERTA(Bundle.getStringApplication("rotulo_aberta")),
        AMBOS(Bundle.getStringApplication("rotulo_ambas"));

        private final String descricao;

        private SituacaoProducao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_situacao_producao")
    public SituacaoProducao getSituacaoProducao() {
        return situacaoProducao;
    }

    public void setSituacaoProducao(SituacaoProducao situacaoProducao) {
        this.situacaoProducao = situacaoProducao;
    }

    @DescricaoParametro("rotulo_convenio")
    public OperadorValor<List<Convenio>> getConvenio() {
        return convenio;
    }

    public void setConvenio(OperadorValor<List<Convenio>> convenio) {
        this.convenio = convenio;
    }

    @DescricaoParametro("rotulo_agrupar")
    public String getAgrupar() {
        return agrupar;
    }

    public void setAgrupar(String agrupar) {
        this.agrupar = agrupar;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_procedimento")
    public OperadorValor<List<Procedimento>> getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(OperadorValor<List<Procedimento>> procedimento) {
        this.procedimento = procedimento;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissionals() {
        return profissionals;
    }

    public void setProfissionals(OperadorValor<List<Profissional>> profissionals) {
        this.profissionals = profissionals;
    }

    @DescricaoParametro("rotulo_usuario_cadsus")
    public OperadorValor<List<UsuarioCadsus>> getUsuariosCadsus() {
        return usuariosCadsus;
    }

    public void setUsuariosCadsus(OperadorValor<List<UsuarioCadsus>> usuariosCadsus) {
        this.usuariosCadsus = usuariosCadsus;
    }

    @DescricaoParametro("rotulo_cbo")
    public OperadorValor<List<TabelaCbo>> getTabelasCbo() {
        return tabelasCbo;
    }

    public void setTabelasCbo(OperadorValor<List<TabelaCbo>> tabelasCbo) {
        this.tabelasCbo = tabelasCbo;
    }

    public TabelaCboSubGrupo getTabelaCboSubGrupo() {
        return tabelaCboSubGrupo;
    }

    public void setTabelaCboSubGrupo(TabelaCboSubGrupo tabelaCboSubGrupo) {
        this.tabelaCboSubGrupo = tabelaCboSubGrupo;
    }

    public Date getMesFinal() {
        return mesFinal;
    }

    public void setMesFinal(Date mesFinal) {
        this.mesFinal = mesFinal;
    }

    public Date getMesInicial() {
        return mesInicial;
    }

    public void setMesInicial(Date mesInicial) {
        this.mesInicial = mesInicial;
    }
    
    @DescricaoParametro("rotulo_competencia")
    public String getPeriodoFormatado(){
        if (getMesInicial()!=null && getMesFinal()!=null) {
            return Bundle.getStringApplication("rotulo_inicial")+": "+Data.formatarMesAno(getMesInicial())
                  +" "+Bundle.getStringApplication("rotulo_final")+": "+Data.formatarMesAno(getMesFinal());
        }
        return Data.formatarMesAno(getMesInicial());
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")
    public String getTipoProcedimentoFormatado() {
        if (RepositoryComponentDefault.SIM.equals(getTipoProcedimento())) {
            return Bundle.getStringApplication("rotulo_faturavel");
        } else if (RepositoryComponentDefault.NAO.equals(getTipoProcedimento())) {
            return Bundle.getStringApplication("rotulo_nao_faturavel");
        }
        return Bundle.getStringApplication("rotulo_ambos");
    }

    @DescricaoParametro("rotulo_agrupar_unidade")
    public String getAgruparEmpresa() {
        return agruparEmpresa;
    }

    public void setAgruparEmpresa(String agruparEmpresa) {
        this.agruparEmpresa = agruparEmpresa;
    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    @DescricaoParametro("rotulo_apenas_dengue")
    public String getDescricaoApenasDengue() {
        return RepositoryComponentDefault.SIM_LONG.equals(getApenasDengue()) ? Bundle.getStringApplication("rotulo_sim") : Bundle.getStringApplication("rotulo_nao");
    }

    public Long getApenasDengue() {
        return apenasDengue;
    }

    public void setApenasDengue(Long apenasDengue) {
        this.apenasDengue = apenasDengue;
    }


    public boolean isApenasDengue() {
        return RepositoryComponentDefault.SIM_LONG.equals(getApenasDengue());
    }
}
