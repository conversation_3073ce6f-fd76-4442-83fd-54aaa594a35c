/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.celk.util.Util;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPerfilAtendimentoDTO implements Serializable {
    private Long codigo;
    private Long codigoMotivoAlta;
    private String codigoString;
    private String descricao;
    private Long quantidade;
    private Double quantidadeDouble;
    private Long total;
    private Double totalDouble;
    private Long sequencia;
    private List<RelatorioPerfilAtendimentoDTO> usuariosSexo;
    private List<RelatorioPerfilAtendimentoDTO> usuariosTipoDemanda;
    private List<RelatorioPerfilAtendimentoDTO> usuariosIdade;
    private List<RelatorioPerfilAtendimentoDTO> usuariosCidade;
    private List<RelatorioPerfilAtendimentoDTO> usuariosPrioridade;
    private List<RelatorioPerfilAtendimentoDTO> usuariosUnidade;
    private List<RelatorioPerfilAtendimentoDTO> usuariosMicroArea;
    private List<RelatorioPerfilAtendimentoDTO> usuariosUnidadesAtendimento;
    private List<RelatorioPerfilAtendimentoDTO> procedimentos;
    private List<RelatorioPerfilAtendimentoDTO> classificacaoRisco;
    private List<RelatorioPerfilAtendimentoDTO> tiposAtendimento;
    private List<RelatorioPerfilAtendimentoDTO> classificaoAtendimento;
    private List<RelatorioPerfilAtendimentoDTO> cids;
    private List<RelatorioPerfilAtendimentoDTO> exames;
    private List<RelatorioPerfilAtendimentoDTO> encaminhamentos;
    private List<RelatorioPerfilAtendimentoDTO> encaminhamentoEspecialista;
    private List<RelatorioPerfilAtendimentoDTO> medicamentos;
    private List<RelatorioPerfilAtendimentoDTO> dispensacoesIdade;
    private List<RelatorioPerfilAtendimentoDTO> doencas;
    private List<RelatorioPerfilAtendimentoDTO> atividadeGrupo;
    private List<RelatorioPerfilAtendimentoDTO> vacinasAplicadas;
    private List<RelatorioPerfilAtendimentoDTO> cidades;
    private List<RelatorioPerfilAtendimentoDTO> cidadesBairros;
    private List<RelatorioPerfilAtendimentoDTO> racasAtendimento;
    private List<RelatorioPerfilAtendimentoDTO> atendimentoAlta;

    private String custom;

    public void setCodigoMotivoAlta(Long codigoMotivoAlta) {
        this.codigoMotivoAlta = codigoMotivoAlta;
        if (Util.isNotNull(codigoMotivoAlta)) {
            descricao = AtendimentoAlta.MotivoAlta.valeuOf(codigoMotivoAlta).descricao();
        }

    }

    public List<RelatorioPerfilAtendimentoDTO> getEncaminhamentoEspecialista() {
        return encaminhamentoEspecialista;
    }

    public void setEncaminhamentoEspecialista(List<RelatorioPerfilAtendimentoDTO> encaminhamentoEspecialista) {
        this.encaminhamentoEspecialista = encaminhamentoEspecialista;
    }

    public List<RelatorioPerfilAtendimentoDTO> getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public void setClassificacaoRisco(List<RelatorioPerfilAtendimentoDTO> classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }

    public List<RelatorioPerfilAtendimentoDTO> getMedicamentos() {
        return medicamentos;
    }

    public void setMedicamentos(List<RelatorioPerfilAtendimentoDTO> medicamentos) {
        this.medicamentos = medicamentos;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosUnidade() {
        return usuariosUnidade;
    }

    public void setUsuariosUnidade(List<RelatorioPerfilAtendimentoDTO> usuariosUnidade) {
        this.usuariosUnidade = usuariosUnidade;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosMicroArea() {
        return usuariosMicroArea;
    }

    public void setUsuariosMicroArea(List<RelatorioPerfilAtendimentoDTO> usuariosMicroArea) {
        this.usuariosMicroArea = usuariosMicroArea;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosPrioridade() {
        return usuariosPrioridade;
    }

    public void setUsuariosPrioridade(List<RelatorioPerfilAtendimentoDTO> usuariosPrioridade) {
        this.usuariosPrioridade = usuariosPrioridade;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosCidade() {
        return usuariosCidade;
    }

    public void setUsuariosCidade(List<RelatorioPerfilAtendimentoDTO> usuariosCidade) {
        this.usuariosCidade = usuariosCidade;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosIdade() {
        return usuariosIdade;
    }

    public void setUsuariosIdade(List<RelatorioPerfilAtendimentoDTO> usuariosIdade) {
        this.usuariosIdade = usuariosIdade;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosSexo() {
        return usuariosSexo;
    }

    public void setUsuariosSexo(List<RelatorioPerfilAtendimentoDTO> usuariosSexo) {
        this.usuariosSexo = usuariosSexo;
    }

    public List<RelatorioPerfilAtendimentoDTO> getCids() {
        return cids;
    }

    public void setCids(List<RelatorioPerfilAtendimentoDTO> cids) {
        this.cids = cids;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if ("usu.sexo".equals(custom)) {
            return getDescricaoSexo();
        } else if ("a.tipoDemanda".equals(custom)) {
            return getDescricaoTipoDemanda();
        }
        return descricao;
    }

    public String getDescricaoSexo(){
        if (descricao.equals("F")) {
            return Bundle.getStringApplication("rotulo_feminino");
        }
        return Bundle.getStringApplication("rotulo_masculino");
    }

    public String getDescricaoTipoDemanda() {
        if (descricao.equals(Atendimento.TIPO_ATENDIMENTO_AGENDADA.toString())) {
            return Bundle.getStringApplication("rotulo_agendamento");
        } else if (descricao.equals(Atendimento.TIPO_ATENDIMENTO_IMEDIATA.toString())) {
            return Bundle.getStringApplication("rotulo_espontanea");
        }
        return null;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public void setDescricao(Date descricao) {
        this.descricao = descricao.toString();
    }

    public void setDescricao(Long descricao) {
        this.descricao = descricao.toString();
    }

    public List<RelatorioPerfilAtendimentoDTO> getEncaminhamentos() {
        return encaminhamentos;
    }

    public void setEncaminhamentos(List<RelatorioPerfilAtendimentoDTO> encaminhamentos) {
        this.encaminhamentos = encaminhamentos;
    }

    public List<RelatorioPerfilAtendimentoDTO> getExames() {
        return exames;
    }

    public void setExames(List<RelatorioPerfilAtendimentoDTO> exames) {
        this.exames = exames;
    }

    public List<RelatorioPerfilAtendimentoDTO> getTiposAtendimento() {
        return tiposAtendimento;
    }

    public void setTiposAtendimento(List<RelatorioPerfilAtendimentoDTO> tiposAtendimento) {
        this.tiposAtendimento = tiposAtendimento;
    }

    public List<RelatorioPerfilAtendimentoDTO> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<RelatorioPerfilAtendimentoDTO> procedimentos) {
        this.procedimentos = procedimentos;
    }

    public Long getQuantidade() {
        return quantidade;
        }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getTotal() {
        return total;
        }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Double getTotalDouble() {
        if (getTotal()!=null) {
            return getTotal().doubleValue();
        }
        return totalDouble;
    }

    public void setTotalDouble(Double totalDouble) {
        this.totalDouble = totalDouble;
    }

    public Double getPorcentagem() {
        return (getQuantidade().doubleValue() * 100 / getTotal().doubleValue());
    }

    public Double getQuantidadeDouble() {
        if (getQuantidade()!=null) {
            return getQuantidade().doubleValue();
        }
        return quantidadeDouble;
    }

    public void setQuantidadeDouble(Double quantidadeDouble) {
        this.quantidadeDouble = quantidadeDouble;
    }

    public String getCodigoString() {
        if (getCodigo()!=null) {
            return getCodigo().toString();
        }
        return codigoString;
    }

    public void setCodigoString(String codigoString) {
        this.codigoString = codigoString;
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom;
    }

    public Long getSequencia() {
        return sequencia;
    }

    public void setSequencia(Long sequencia) {
        this.sequencia = sequencia;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosUnidadesAtendimento() {
        return usuariosUnidadesAtendimento;
    }

    public void setUsuariosUnidadesAtendimento(List<RelatorioPerfilAtendimentoDTO> usuariosUnidadesAtendimento) {
        this.usuariosUnidadesAtendimento = usuariosUnidadesAtendimento;
    }

    public List<RelatorioPerfilAtendimentoDTO> getDispensacoesIdade() {
        return dispensacoesIdade;
    }

    public void setDispensacoesIdade(List<RelatorioPerfilAtendimentoDTO> dispensacoesIdade) {
        this.dispensacoesIdade = dispensacoesIdade;
    }

    public List<RelatorioPerfilAtendimentoDTO> getDoencas() {
        return doencas;
    }

    public void setDoencas(List<RelatorioPerfilAtendimentoDTO> doencas) {
        this.doencas = doencas;
    }

    public List<RelatorioPerfilAtendimentoDTO> getClassificaoAtendimento() {
        return classificaoAtendimento;
    }

    public void setClassificaoAtendimento(List<RelatorioPerfilAtendimentoDTO> classificaoAtendimento) {
        this.classificaoAtendimento = classificaoAtendimento;
    }

    public List<RelatorioPerfilAtendimentoDTO> getUsuariosTipoDemanda() {
        return usuariosTipoDemanda;
    }

    public void setUsuariosTipoDemanda(List<RelatorioPerfilAtendimentoDTO> usuariosTipoDemanda) {
        this.usuariosTipoDemanda = usuariosTipoDemanda;
    }

    public List<RelatorioPerfilAtendimentoDTO> getAtividadeGrupo() {
        return atividadeGrupo;
    }

    public void setAtividadeGrupo(List<RelatorioPerfilAtendimentoDTO> atividadeGrupo) {
        this.atividadeGrupo = atividadeGrupo;
    }

    public List<RelatorioPerfilAtendimentoDTO> getVacinasAplicadas() {
        return vacinasAplicadas;
    }

    public void setVacinasAplicadas(List<RelatorioPerfilAtendimentoDTO> vacinasAplicadas) {
        this.vacinasAplicadas = vacinasAplicadas;
    }

    public List<RelatorioPerfilAtendimentoDTO> getCidades() {
        return cidades;
    }

    public void setCidades(List<RelatorioPerfilAtendimentoDTO> cidades) {
        this.cidades = cidades;
    }

    public List<RelatorioPerfilAtendimentoDTO> getCidadesBairros() {
        return cidadesBairros;
    }

    public void setCidadesBairros(List<RelatorioPerfilAtendimentoDTO> cidadesBairros) {
        this.cidadesBairros = cidadesBairros;
    }

    public List<RelatorioPerfilAtendimentoDTO> getRacasAtendimento() {
        return racasAtendimento;
    }

    public void setRacasAtendimento(List<RelatorioPerfilAtendimentoDTO> racasAtendimento) {
        this.racasAtendimento = racasAtendimento;
    }

    public List<RelatorioPerfilAtendimentoDTO> getAtendimentoAlta() {
        return atendimentoAlta;
    }

    public void setAtendimentoAlta(List<RelatorioPerfilAtendimentoDTO> atendimentoAlta) {
        this.atendimentoAlta = atendimentoAlta;
    }
}