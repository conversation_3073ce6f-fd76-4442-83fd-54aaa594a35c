package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoMovimentacoesDTO implements Serializable{

    private Integer mes;
    private Integer ano;
    private Double saldoAnterior;
    private Double credito;
    private Double debito;
    private String descricaoEmpresa;
    private String descricaoTipoConta;
    private String frequencia;

    public String getDescricaoFrequencia() {
        if(RelatorioResumoMovimentacoesDTOParam.Frequencia.MENSAL.toString().equals(frequencia)){
            Calendar c = GregorianCalendar.getInstance();
            c.set(Calendar.MONTH, mes-1);
            c.set(Calendar.YEAR, ano);

            return new SimpleDateFormat("MMMM - yyyy", Bundle.getLocale()).format(c.getTime());
        }
        return ano.toString();
    }

    public String getDescricaoTipoConta() {
        return descricaoTipoConta;
    }

    public void setDescricaoTipoConta(String descricaoTipoConta) {
        this.descricaoTipoConta = descricaoTipoConta;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }
    
    public String getDescricaoEmpresa() {
        return descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Double getSaldoAnterior() {
        return saldoAnterior;
    }

    public void setSaldoAnterior(Double saldoAnterior) {
        this.saldoAnterior = saldoAnterior;
    }

    public Double getCredito() {
        return credito;
    }

    public void setCredito(Double credito) {
        this.credito = credito;
    }

    public Double getDebito() {
        return debito;
    }

    public void setDebito(Double debito) {
        this.debito = debito;
    }

}
