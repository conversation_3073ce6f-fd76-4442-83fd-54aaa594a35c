package br.com.ksisolucoes.report.basico.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.TipoEquipe;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoEquipesDTOParam implements Serializable {

    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<TipoEquipe>> tipoEquipes;
    private OperadorValor<List<Profissional>> profissionais;
    private OperadorValor<List<TabelaCbo>> tabelaCbo;
    private EquipeArea equipeArea;
    private String formaApresentacao;
    private Integer tipoRelatorio;
    private String situacao;

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_equipe_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissionais() {
        return profissionais;
    }

    public void setProfissionais(OperadorValor<List<Profissional>> profissionais) {
        this.profissionais = profissionais;
    }

    @DescricaoParametro("rotulo_cbo")
    public OperadorValor<List<TabelaCbo>> getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(OperadorValor<List<TabelaCbo>> tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    @DescricaoParametro("rotulo_tipo_equipe")
    public OperadorValor<List<TipoEquipe>> getTipoEquipes() {
        return tipoEquipes;
    }

    public void setTipoEquipes(OperadorValor<List<TipoEquipe>> tipoEquipes) {
        this.tipoEquipes = tipoEquipes;
    }

    public Integer getTipoRelatorio() {
        return tipoRelatorio;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public String getTipoRelatorioFormatado() {
        if (ReportProperties.DETALHADO == getTipoRelatorio()) {
            return Bundle.getStringApplication("rotulo_detalhado");
        } else if (ReportProperties.RESUMIDO == getTipoRelatorio()) {
            return Bundle.getStringApplication("rotulo_resumido");
        }
        return "";
    }

    public void setTipoRelatorio(Integer tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public String getSituacao() {
        return situacao;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getSituacaoFormatado() {
        if (RepositoryComponentDefault.SIM.equals(getSituacao())) {
            return Bundle.getStringApplication("rotulo_ativo");
        } else if (RepositoryComponentDefault.NAO.equals(getSituacao())) {
            return Bundle.getStringApplication("rotulo_inativo");
        }
        return Bundle.getStringApplication("rotulo_ambo");
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

}
