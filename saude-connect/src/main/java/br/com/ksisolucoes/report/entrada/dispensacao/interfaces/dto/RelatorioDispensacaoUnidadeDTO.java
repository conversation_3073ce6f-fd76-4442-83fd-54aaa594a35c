/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDispensacaoUnidadeDTO implements Serializable {

    private Double quantidadeTotal;
    private Double valorTotal;
    private Long numeroDispensacoes;
    private Long numeroPacientes;
    private String referencia;
    private String descricao;
    private Unidade unidade;
    private Long codigoUsuarioCadsus;
    private List<DispensacaoMedicamentoItem> produtoList;

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    public List<DispensacaoMedicamentoItem> getProdutoList() {
        return produtoList;
    }

    public void setProdutoList(List<DispensacaoMedicamentoItem> produtoList) {
        this.produtoList = produtoList;
    }

    public Unidade getUnidade() {
        return unidade;
    }

    public void setUnidade(Unidade unidade) {
        this.unidade = unidade;
    }

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoFormatado() {
        if(getReferencia() != null){
            return Util.getDescricaoFormatado(getReferencia(), getDescricao());
        }
        return getDescricao();
    }

    public Long getNumeroDispensacoes() {
        return numeroDispensacoes;
    }

    public void setNumeroDispensacoes(Long numeroDispensacoes) {
        this.numeroDispensacoes = numeroDispensacoes;
    }

    public Long getNumeroPacientes() {
        return numeroPacientes;
    }

    public void setNumeroPacientes(Long numeroPacientes) {
        this.numeroPacientes = numeroPacientes;
    }

    public Double getQuantidadeTotal() {
        return quantidadeTotal;
    }

    public void setQuantidadeTotal(Double quantidadeTotal) {
        this.quantidadeTotal = quantidadeTotal;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

}
