package br.com.ksisolucoes.report.relacaoidosos.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCriancasDTOParam implements Serializable {

    private List<Empresa> estabelecimento;
    private EquipeMicroArea equipeMicroArea;
    private EquipeArea equipeArea;
    private List<Long> tiposUnidade;

    @DescricaoParametro("rotulo_estabelecimento")
    public List<Empresa> getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(List<Empresa> estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    @DescricaoParametro("rotulo_equipe_micro_area")
    public String getDescricaoEquipeMicroArea() {
        return equipeMicroArea != null ? (equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : Bundle.getStringApplication("rotulo_sem_profissional"))) : "";
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    @DescricaoParametro("rotulo_equipe_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    public List<Long>  getTipoUnidades() {
        return tiposUnidade;
    }

    public void setTipoUnidades(List<Long> tiposUnidade) {
        this.tiposUnidade = tiposUnidade;
    }

}
