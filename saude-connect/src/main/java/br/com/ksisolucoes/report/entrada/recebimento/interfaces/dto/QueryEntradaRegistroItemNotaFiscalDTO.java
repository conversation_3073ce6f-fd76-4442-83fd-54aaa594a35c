/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalHelper;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QueryEntradaRegistroItemNotaFiscalDTO implements Serializable{

    public static String PROP_NUMERO_NOTA_FISCAL = "numeroNotaFiscal";
    public static String PROP_CODIGO_SERIE = "serie";
    public static String PROP_CODIGO_EMPRESA = "codigoEmpresa";
    public static String PROP_CODIGO_FORNECEDOR = "codigoFornecedor";
    public static String PROP_ITEM = "item";
    public static String PROP_DATA_EMISSAO = "dataEmissao";
    public static String PROP_CODIGO_PRODUTO = "codigoProduto";
    public static String PROP_DESCRICAO_PRODUTO = "descricaoProduto";
    public static String PROP_DESCRICAO_PRODUTO_OCASIONAL = "descricaoProdutoOcasional";
    public static String PROP_QUANTIDADE = "quantidade";
    public static String PROP_QUANTIDADE_DEVOLVIDA = "quantidadeDevolvida";
    public static String PROP_QUANTIDADE_ESTOQUE = "quantidadeEstoque";
    public static String PROP_QUANTIDADE_EXTRA = "quantidadeExtra";
    public static String PROP_PRECO_UNITARIO = "precoUnitario";
    public static String PROP_PERCENTUAL_IPI = "percentualIpi";
    public static String PROP_VALOR_IPI = "valorIpi";
    public static String PROP_PERCENTUAL_ICMS = "percentualIcms";
    public static String PROP_VALOR_ICMS = "valorIcms";
    public static String PROP_DIVERGENCIA_ENTRADA = "divergenciaEntrada";
    public static String PROP_DESCRICAO_CENTRO_CUSTO = "descricaoCentroCusto";
    public static String PROP_CFO = "cfo";
    public static String PROP_DESCRICAO_PRODUTO_FORMATADO = "descricaoProdutoFormatado";
    public static String PROP_DESCRICAO_FORNECEDOR_FORMATADO = "descricaoFornecedorFormatado";
    public static String PROP_VALOR_TOTAL_ITEM = "valorTotalItem";
    public static String PROP_DESCRICAO_TIPO = "descricaoTipo";
    public static String PROP_OBSERVACAO = "observacao";
    public static String PROP_VALOR_FRETE_UNITARIO_SEM_CONHECIMENTO = "valorFreteUnitarioSemConhecimento";
    public static String PROP_VALOR_TOTAL_NOTA = "valorTotalNota";
    public static String PROP_OUTRAS_DESPESAS_NOTA = "outrasDespesasNota";
    public static String PROP_VALOR_FRETE_NOTA = "valorFreteNota";
    public static String PROP_PRECO_VENDA_ESTOQUE_EMPRESA = "precoVendaEstoqueEmpresa";
    private Long numeroNotaFiscal;
    private String serie;
    private Long codigoEmpresa;
    private Long codigoFornecedor;
    private String descricaoFornecedor;
    private Long item;
    private Long sequencia;
    private Date dataEmissao;
    private String codigoProduto;
    private String descricaoProduto;
    private String descricaoProdutoOcasional;
    private Double quantidade;
    private Double quantidadeDevolvida;
    private Double quantidadeEstoque;
    private Double quantidadeExtra;
    private Double quantidadeBalanca;
    private Double precoUnitario;
    private Double percentualIpi;
    private Double valorIpi;
    private Double percentualIcms;
    private Double valorIcms;
    private Long codigoCentroCusto;
    private String descricaoCentroCusto;
    private Long codigoCfo;
    private String descricaoCfo;
    private Long cfo;
    private Long codigoClassificacao;
    private String classificacao;
    private String divergenciaEntrada;
    private String flagPessoaFisicaJuridica;
    private Double desconto;
    private Long tipo;
    private Long status;
    private String observacao;
    private Double valorFreteUnitarioSemConhecimento;
    private Double valorTotalNota;
    private Double outrasDespesasNota;
    private Double valorFreteNota;
    private Double precoVendaEstoqueEmpresa;
    private Produto produto;
    private Double valorMercadoria;
    private Double valorItem;
    private Long versionItem;
    private Long codigoItem;

    public Double getPrecoVendaEstoqueEmpresa() {
        return precoVendaEstoqueEmpresa;
    }

    public void setPrecoVendaEstoqueEmpresa(Double precoVendaEstoqueEmpresa) {
        this.precoVendaEstoqueEmpresa = precoVendaEstoqueEmpresa;
    }

    public Double getValorFreteNota() {
        return valorFreteNota;
    }

    public void setValorFreteNota(Double valorFreteNota) {
        this.valorFreteNota = valorFreteNota;
    }

    public Double getOutrasDespesasNota() {
        return outrasDespesasNota;
    }

    public void setOutrasDespesasNota(Double outrasDespesasNota) {
        this.outrasDespesasNota = outrasDespesasNota;
    }

    public Long getCodigoEmpresa() {
        return this.codigoEmpresa;
    }

    public void setCodigoEmpresa(Long codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Long getCodigoFornecedor() {
        return this.codigoFornecedor;
    }

    public void setCodigoFornecedor(Long codigoFornecedor) {
        this.codigoFornecedor = codigoFornecedor;
    }

    public String getCodigoProduto() {
        return this.codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Date getDataEmissao() {
        return this.dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getDescricaoCentroCusto() {
        return this.descricaoCentroCusto;
    }

    public void setDescricaoCentroCusto(String descricaoCentroCusto) {
        this.descricaoCentroCusto = descricaoCentroCusto;
    }

    public String getDescricaoFornecedor() {
        return this.descricaoFornecedor;
    }

    public void setDescricaoFornecedor(String descricaoFornecedor) {
        this.descricaoFornecedor = descricaoFornecedor;
    }

    public String getDescricaoProduto() {
        return this.descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getDescricaoProdutoOcasional() {
        return this.descricaoProdutoOcasional;
    }

    public void setDescricaoProdutoOcasional(String descricaoProdutoOcasional) {
        this.descricaoProdutoOcasional = descricaoProdutoOcasional;
    }

    public Long getItem() {
        return this.item;
    }

    public void setItem(Long item) {
        this.item = item;
    }

    public Long getSequencia() {
        return this.sequencia;
    }

    public void setSequencia(Long sequencia) {
        this.sequencia = sequencia;
    }

    public Long getNumeroNotaFiscal() {
        return this.numeroNotaFiscal;
    }

    public void setNumeroNotaFiscal(Long numeroNotaFiscal) {
        this.numeroNotaFiscal = numeroNotaFiscal;
    }

    public Double getPercentualIcms() {
        return this.percentualIcms;
    }

    public void setPercentualIcms(Double percentualICMS) {
        this.percentualIcms = percentualICMS;
    }

    public Double getPercentualIpi() {
        return this.percentualIpi;
    }

    public void setPercentualIpi(Double percentualIPI) {
        this.percentualIpi = percentualIPI;
    }

    public Double getPrecoUnitario() {
        return this.precoUnitario;
    }

    public String getPrecoUnitarioQuatroCasas() {
        return Valor.adicionarFormatacaoMonetaria(this.precoUnitario, 4);
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public Double getQuantidade() {
        return this.quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public String getSerie() {
        return this.serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public Double getValorIcms() {
        return this.valorIcms;
    }

    public void setValorIcms(Double valorICMS) {
        this.valorIcms = valorICMS;
    }

    public Double getValorIpi() {
        return this.valorIpi;
    }

    public void setValorIpi(Double valorIPI) {
        this.valorIpi = valorIPI;
    }

    public Long getCfo() {
        return this.cfo;
    }

    public void setCfo(Long cfo) {
        this.cfo = cfo;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public Double getValorFreteUnitarioSemConhecimento() {
        return valorFreteUnitarioSemConhecimento;
    }

    public void setValorFreteUnitarioSemConhecimento(Double valorFreteUnitarioSemConhecimento) {
        this.valorFreteUnitarioSemConhecimento = valorFreteUnitarioSemConhecimento;
    }

    public Double getValorTotalNota() {
        return valorTotalNota;
    }

    public void setValorTotalNota(Double valorTotalNota) {
        this.valorTotalNota = valorTotalNota;
    }

    public Produto getProduto() {

        if (this.produto == null && this.getCodigoProduto() != null) {
            this.produto = new Produto(this.getCodigoProduto());
            this.produto.setDescricao(this.getDescricaoProduto());
        }

        return this.produto;
    }

    public String getDescricaoProdutoFormatado() {
        if (this.getCodigoProduto() != null) {
            return Produto.getDescricaoFormatado(getCodigoProduto(), getDescricaoProduto());
        } else {
            return getDescricaoProdutoOcasional();
        }
    }

    public String getDescricaoFornecedorFormatado() {
        return Pessoa.getDescricaoFormatado(getCodigoFornecedor(), getDescricaoFornecedor());
    }

    public Double getValorTotalItem() {
        return getValorMercadoriaItem();
    }

    public RegistroItemNotaFiscal getRegistroItemNotaFiscal() {
        RegistroItemNotaFiscal registroItemNotaFiscal = new RegistroItemNotaFiscal();
        registroItemNotaFiscal.setCodigo(getCodigoItem());

        registroItemNotaFiscal.setValorItem(getValorTotalItem());
        registroItemNotaFiscal.setDesconto(getDesconto());
        registroItemNotaFiscal.setDescricaoProduto(getDescricaoProduto());
        registroItemNotaFiscal.setPercentualIpi(getPercentualIpi());
        registroItemNotaFiscal.setPrecoUnitario(getPrecoUnitario());

        Produto produto = getProduto();

        registroItemNotaFiscal.setProduto(produto);
        registroItemNotaFiscal.setQuantidade(getQuantidade());
        registroItemNotaFiscal.setQuantidadeDevolvida(getQuantidadeDevolvida());
        registroItemNotaFiscal.setQuantidadeEstoque(getQuantidadeEstoque());
        registroItemNotaFiscal.setQuantidadeExtra(getQuantidadeExtra());
        registroItemNotaFiscal.setValorIcms(getValorIcms());
        registroItemNotaFiscal.setValorIpi(getValorIpi());
        registroItemNotaFiscal.setStatus(getStatus());
        registroItemNotaFiscal.setVersion(getVersionItem());
        registroItemNotaFiscal.setRegistroNotaFiscal(new RegistroNotaFiscal());
        registroItemNotaFiscal.getRegistroNotaFiscal().setValorMercadoria(this.getValorMercadoria());
        registroItemNotaFiscal.getRegistroNotaFiscal().setValorTotal(this.getValorTotalNota());
        registroItemNotaFiscal.getRegistroNotaFiscal().setValorFrete(this.getValorFreteNota());

        return registroItemNotaFiscal;
    }

    public Double getPercentualLucro() {
//        Double precoVenda = this.getPrecoVendaEstoqueEmpresa();
//        Double percentualLucro = 0D;
//        if (precoVenda != null && precoVenda.doubleValue() > 0) {
//            RegistroItemNotaFiscal registroItemNotaFiscal = this.getRegistroItemNotaFiscal();
//        }

        return 0D;
    }

    public Long getTipo() {
        return tipo;
    }

    public void setTipo(Long tipo) {
        this.tipo = tipo;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getCodigoCfo() {
        return codigoCfo;
    }

    public void setCodigoCfo(Long codigoCfo) {
        this.codigoCfo = codigoCfo;
    }

    public String getDescricaoCfo() {
        return descricaoCfo;
    }

    public void setDescricaoCfo(String descricaoCfo) {
        this.descricaoCfo = descricaoCfo;
    }

    public Double getQuantidadeDevolvida() {
        return quantidadeDevolvida;
    }

    public void setQuantidadeDevolvida(Double quantidadeDevolvida) {
        this.quantidadeDevolvida = quantidadeDevolvida;
    }

    public Double getQuantidadeEstoque() {
        return quantidadeEstoque;
    }

    public void setQuantidadeEstoque(Double quantidadeEstoque) {
        this.quantidadeEstoque = quantidadeEstoque;
    }

    public Double getQuantidadeExtra() {
        return quantidadeExtra;
    }

    public void setQuantidadeExtra(Double quantidadeExtra) {
        this.quantidadeExtra = quantidadeExtra;
    }

    public Double getQuantidadeBalanca() {
        return quantidadeBalanca;
    }

    public void setQuantidadeBalanca(Double quantidadeBalanca) {
        this.quantidadeBalanca = quantidadeBalanca;
    }

    public Long getCodigoCentroCusto() {
        return codigoCentroCusto;
    }

    public void setCodigoCentroCusto(Long codigoCentroCusto) {
        this.codigoCentroCusto = codigoCentroCusto;
    }

    public Long getCodigoClassificacao() {
        return codigoClassificacao;
    }

    public void setCodigoClassificacao(Long codigoClassificacao) {
        this.codigoClassificacao = codigoClassificacao;
    }

    public String getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }

    public String getDivergenciaEntrada() {
        return divergenciaEntrada;
    }

    public void setDivergenciaEntrada(String divergenciaEntrada) {
        this.divergenciaEntrada = divergenciaEntrada;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Double getValorMercadoria() {
        return valorMercadoria;
    }

    public void setValorMercadoria(Double valorMercadoria) {
        this.valorMercadoria = valorMercadoria;
    }

    public String getFlagPessoaFisicaJuridica() {
        return flagPessoaFisicaJuridica;
    }

    public void setFlagPessoaFisicaJuridica(String flagPessoaFisicaJuridica) {
        this.flagPessoaFisicaJuridica = flagPessoaFisicaJuridica;
    }

    public Double getValorMercadoriaItem() {
        return RegistroItemNotaFiscalHelper.getValorMercadoria(getValorItem(), getDesconto());
    }

    public Double getValorItem() {
        return valorItem;
    }

    public void setValorItem(Double valorItem) {
        this.valorItem = valorItem;
    }

    public Long getVersionItem() {
        return versionItem;
    }

    public void setVersionItem(Long versionItem) {
        this.versionItem = versionItem;
    }

    public Long getCodigoItem() {
        return codigoItem;
    }

    public void setCodigoItem(Long codigoItem) {
        this.codigoItem = codigoItem;
    }

}
