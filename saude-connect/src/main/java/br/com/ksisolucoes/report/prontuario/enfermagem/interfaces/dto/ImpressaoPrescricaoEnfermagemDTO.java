package br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemAtendimento;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemAtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemGrupo;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoPrescricaoEnfermagemDTO {

    private PrescricaoEnfermagem prescricaoEnfermagem;
    private PrescricaoEnfermagemGrupo prescricaoEnfermagemGrupo;
    private PrescricaoEnfermagemAtendimento prescricaoEnfermagemAtendimento;
    private PrescricaoEnfermagemAtendimentoItem prescricaoEnfermagemAtendimentoItem;
    private Atendimento atendimento;

    public PrescricaoEnfermagem getPrescricaoEnfermagem() {
        return prescricaoEnfermagem;
    }

    public void setPrescricaoEnfermagem(PrescricaoEnfermagem prescricaoEnfermagem) {
        this.prescricaoEnfermagem = prescricaoEnfermagem;
    }

    public PrescricaoEnfermagemGrupo getPrescricaoEnfermagemGrupo() {
        return prescricaoEnfermagemGrupo;
    }

    public void setPrescricaoEnfermagemGrupo(PrescricaoEnfermagemGrupo prescricaoEnfermagemGrupo) {
        this.prescricaoEnfermagemGrupo = prescricaoEnfermagemGrupo;
    }

    public PrescricaoEnfermagemAtendimento getPrescricaoEnfermagemAtendimento() {
        return prescricaoEnfermagemAtendimento;
    }

    public void setPrescricaoEnfermagemAtendimento(PrescricaoEnfermagemAtendimento prescricaoEnfermagemAtendimento) {
        this.prescricaoEnfermagemAtendimento = prescricaoEnfermagemAtendimento;
    }

    public PrescricaoEnfermagemAtendimentoItem getPrescricaoEnfermagemAtendimentoItem() {
        return prescricaoEnfermagemAtendimentoItem;
    }

    public void setPrescricaoEnfermagemAtendimentoItem(PrescricaoEnfermagemAtendimentoItem prescricaoEnfermagemAtendimentoItem) {
        this.prescricaoEnfermagemAtendimentoItem = prescricaoEnfermagemAtendimentoItem;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }
}
