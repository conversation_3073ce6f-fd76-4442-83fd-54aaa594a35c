package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import br.com.ksisolucoes.vo.consorcio.TipoMovimentacao.TipoMovimento;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDetalhamentoMovimentacoesDTOParam implements Serializable{

    public enum MovimentacaoOrcamento{
        AMBOS(Bundle.getStringApplication("rotulo_ambos")),
        SOMENTE_MOVIMENTA_ORCAMENTO(Bundle.getStringApplication("rotulo_movimenta_orcamento")),
        SOMENTE_NAO_MOVIMENTA_ORCAMENTO(Bundle.getStringApplication("rotulo_nao_movimenta_orcamento"));

        private String name;

        private MovimentacaoOrcamento(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    public enum FormaApresentacao{
        CONSORCIADO(Bundle.getStringApplication("rotulo_consorciado")),
        TIPO_CONTA(Bundle.getStringApplication("rotulo_tipo_conta")),
        TIPO_MOVIMENTO(Bundle.getStringApplication("rotulo_tipo_movimento")),
        MOVIMENTACAO(Bundle.getStringApplication("rotulo_movimentacao")),
        DIARIO(Bundle.getStringApplication("rotulo_diario")),
        MENSAL(Bundle.getStringApplication("rotulo_mensal"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    public enum TipoResumo{
        CONSORCIADO(Bundle.getStringApplication("rotulo_consorciado")),
        TIPO_CONTA(Bundle.getStringApplication("rotulo_tipo_conta")),
        TIPO_MOVIMENTO(Bundle.getStringApplication("rotulo_tipo_movimento")),
        MOVIMENTACAO(Bundle.getStringApplication("rotulo_movimentacao")),
        DIARIO(Bundle.getStringApplication("rotulo_diario")),
        MENSAL(Bundle.getStringApplication("rotulo_mensal"));

        private String name;

        private TipoResumo(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private String tipoOrdenacao;
    private DatePeriod periodo;
    private TipoConta tipoConta;
    private Empresa consorciado;
    private Empresa empresa;
    private MovimentacaoOrcamento classificacao;
    private TipoMovimento tipoMovimento;

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_tipo_movimento")
    public TipoMovimento getTipoMovimento() {
        return tipoMovimento;
    }

    public void setTipoMovimento(TipoMovimento tipoMovimento) {
        this.tipoMovimento = tipoMovimento;
    }

    @DescricaoParametro("rotulo_tipo_conta")
    public TipoConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(TipoConta tipoConta) {
        this.tipoConta = tipoConta;
    }

    @DescricaoParametro("rotulo_consorciado")
    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    @DescricaoParametro("rotulo_classificacao")
    public MovimentacaoOrcamento getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(MovimentacaoOrcamento classificacao) {
        this.classificacao = classificacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }
}
