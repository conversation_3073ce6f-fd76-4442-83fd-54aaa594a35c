package br.com.ksisolucoes.report.vigilancia.dengue.armadilha.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioArmadilhaDTOParam implements Serializable {

    private DengueTipoImovel tipoImovel;
    private DengueAreaVigilancia area;
    private DengueMicroAreaVigilancia microArea;
    private DengueLocalidade localidade;
    private DatePeriod periodo;
    private Long situacao;
    private FormaApresentacao formaApresentacao;

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        TIPO(Bundle.getStringApplication("rotulo_tipo_imovel")),
        AREA(Bundle.getStringApplication("rotulo_area")),
        MICROAREA(Bundle.getStringApplication("rotulo_microarea")),
        LOCALIDADE(Bundle.getStringApplication("rotulo_localidade"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    @DescricaoParametro("rotulo_tipo_imovel")
    public DengueTipoImovel getTipoImovel() {
        return tipoImovel;
    }

    public void setTipoImovel(DengueTipoImovel tipoImovel) {
        this.tipoImovel = tipoImovel;
    }

    @DescricaoParametro("rotulo_area")
    public DengueAreaVigilancia getArea() {
        return area;
    }

    public void setArea(DengueAreaVigilancia area) {
        this.area = area;
    }

    @DescricaoParametro("rotulo_microarea")
    public DengueMicroAreaVigilancia getMicroArea() {
        return microArea;
    }

    public void setMicroArea(DengueMicroAreaVigilancia microArea) {
        this.microArea = microArea;
    }

    @DescricaoParametro("rotulo_localidade")
    public DengueLocalidade getLocalidade() {
        return localidade;
    }

    public void setLocalidade(DengueLocalidade localidade) {
        this.localidade = localidade;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getSituacao() {
        return situacao;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        return DenguePontoEstrategico.Situacao.valeuOf(getSituacao()).descricao();
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }
}
