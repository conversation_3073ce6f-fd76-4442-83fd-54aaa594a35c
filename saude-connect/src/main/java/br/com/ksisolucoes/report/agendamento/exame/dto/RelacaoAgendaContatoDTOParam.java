/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelacaoAgendaContatoDTOParam implements Serializable {


    public enum Ordenacao {

        PACIENTE(Bundle.getStringApplication("rotulo_paciente")),
        PACIENTE_DATA(Bundle.getStringApplication("rotulo_paciente_data"));

        private String label;

        private Ordenacao(String label) {
            this.label = label;
        }

        @Override
        public String toString() {
            return this.label;
        }
    }

    private OperadorValor<List<TipoProcedimento>> tipoProcedimentos;
    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<Profissional>> profissionals;
    private Convenio convenio;
    private DatePeriod datePeriodo;
    private String ordenacao;

    @DescricaoParametro("rotulo_ordenacao")
    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")
    public OperadorValor<List<TipoProcedimento>> getTipoProcedimentos() {
        return tipoProcedimentos;
    }

    public void setTipoProcedimentos(OperadorValor<List<TipoProcedimento>> tipoProcedimentos) {
        this.tipoProcedimentos = tipoProcedimentos;
    }

    @DescricaoParametro("rotulo_unidade_executante")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissionals() {
        return profissionals;
    }

    public void setProfissionals(OperadorValor<List<Profissional>> profissionals) {
        this.profissionals = profissionals;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getDatePeriodo() {
        return datePeriodo;
    }

    public void setDatePeriodo(DatePeriod datePeriodo) {
        this.datePeriodo = datePeriodo;
    }

    @DescricaoParametro("rotulo_convenio")
    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }
}
