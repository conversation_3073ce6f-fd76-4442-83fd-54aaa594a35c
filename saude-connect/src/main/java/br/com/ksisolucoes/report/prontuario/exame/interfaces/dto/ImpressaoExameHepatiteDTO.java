package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoHepatite;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameHepatiteDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private ExameRequisicao exameRequisicao;
    private RequisicaoHepatite requisicaoHepatite;

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public RequisicaoHepatite getRequisicaoHepatite() {
        return requisicaoHepatite;
    }

    public void setRequisicaoHepatite(RequisicaoHepatite requisicaoHepatite) {
        this.requisicaoHepatite = requisicaoHepatite;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
}