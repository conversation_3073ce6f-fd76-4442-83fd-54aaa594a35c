package br.com.ksisolucoes.report.basico.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.AssistenteSocial;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProdutoSolicitadoDTOParam implements Serializable {

    private OperadorValor<List<AssistenteSocial>> assistenteSocial;
    private OperadorValor<List<UsuarioCadsus>> usuarioCadsus;
    private OperadorValor<List<Produto>> produto;
    private OperadorValor<List<Profissional>> profissional;
    private OperadorValor<List<Empresa>> empresa;
    private String formaApresentacao;
    private String apenasSemEstoque;
    private String visualizarFA;
    private String listarCancelados;
    private Integer tipoRelatorio;
    private DatePeriod periodo;
    private String visualizarEntregas;
    private Long origem;

    public Long getOrigem() {
        return origem;
    }

    public void setOrigem(Long origem) {
        this.origem = origem;
    }

    public String getListarCancelados() {
        return listarCancelados;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getVisualizarEntregas() {
        return visualizarEntregas;
    }

    public void setVisualizarEntregas(String visualizarEntregas) {
        this.visualizarEntregas = visualizarEntregas;
    }

    public void setListarCancelados(String listarCancelados) {
        this.listarCancelados = listarCancelados;
    }

    @DescricaoParametro("rotulo_apenas_sem_estoque")
    public String getApenasSemEstoque() {
        return apenasSemEstoque;
    }

    public void setApenasSemEstoque(String apenasSemEstoque) {
        this.apenasSemEstoque = apenasSemEstoque;
    }

    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public Integer getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(Integer tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public String getVisualizarFA() {
        return visualizarFA;
    }

    public void setVisualizarFA(String visualizarFA) {
        this.visualizarFA = visualizarFA;
    }

    @DescricaoParametro("rotulo_assistente_social")
    public OperadorValor<List<AssistenteSocial>> getAssistenteSocial() {
        return assistenteSocial;
    }

    public void setAssistenteSocial(OperadorValor<List<AssistenteSocial>> assistenteSocial) {
        this.assistenteSocial = assistenteSocial;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(OperadorValor<List<Empresa>> empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_produto")
    public OperadorValor<List<Produto>> getProduto() {
        return produto;
    }

    public void setProduto(OperadorValor<List<Produto>> produto) {
        this.produto = produto;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissional() {
        return profissional;
    }

    public void setProfissional(OperadorValor<List<Profissional>> profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_paciente")
    public OperadorValor<List<UsuarioCadsus>> getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(OperadorValor<List<UsuarioCadsus>> usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
}
