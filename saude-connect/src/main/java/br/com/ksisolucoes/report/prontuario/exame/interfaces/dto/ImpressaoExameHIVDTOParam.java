package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameHIVDTOParam implements Serializable {

    private Atendimento atendimento;
    private Long codigoExame;

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Long getCodigoExame() {
        return codigoExame;
    }

    public void setCodigoExame(Long codigoExame) {
        this.codigoExame = codigoExame;
    }
}
