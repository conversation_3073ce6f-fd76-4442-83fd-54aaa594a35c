package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoAtendimentoInternacaoDTO implements Serializable {

    private Empresa empresaInformacao;
    private Empresa empresa;
    private Long numeroAtendimento;
    private Long numeroAtendimentoPrincipal;
    private Date dataAtendimento;
    private Date dataAlta;
    private Date dataChegada;
    private AtendimentoProntuario atendimentoProntuario;
    private AtendimentoAlta atendimentoAlta;
    private TipoAtendimento tipoAtendimento;
    private Profissional profissional;
    private TabelaCbo tabelaCbo;
    private UsuarioCadsus usuarioCadsus;
    private EvolucaoProntuario evolucaoProntuario;
    private LeitoQuarto leitoQuarto;
    private QuartoInternacao quartoInternacao;
    private Convenio convenio;
    private TabelaCbo cboProfissional;
    private Atendimento atendimento;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Cidade cidade;
    private Estado estado;
    private String tituloOriginal;
    private String descricaoCid;
    private String descricaoCipe;
    private Date dataInicial;
    private Date dataFinal;
    private String descricao;
    private String situacao;
    private List<GrupoProblemasCondicoes> grupoProblemasCondicoes;

    public Empresa getEmpresaInformacao() {
        return empresaInformacao;
    }

    public void setEmpresaInformacao(Empresa empresaInformacao) {
        this.empresaInformacao = empresaInformacao;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Long getNumeroAtendimentoPrincipal() {
        return numeroAtendimentoPrincipal;
    }

    public void setNumeroAtendimentoPrincipal(Long numeroAtendimentoPrincipal) {
        this.numeroAtendimentoPrincipal = numeroAtendimentoPrincipal;
    }

    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public Date getDataAlta() {
        return dataAlta;
    }

    public void setDataAlta(Date dataAlta) {
        this.dataAlta = dataAlta;
    }

    public Date getDataChegada() {
        return dataChegada;
    }

    public void setDataChegada(Date dataChegada) {
        this.dataChegada = dataChegada;
    }

    public AtendimentoProntuario getAtendimentoProntuario() {
        return atendimentoProntuario;
    }

    public void setAtendimentoProntuario(AtendimentoProntuario atendimentoProntuario) {
        this.atendimentoProntuario = atendimentoProntuario;
    }

    public AtendimentoAlta getAtendimentoAlta() {
        return atendimentoAlta;
    }

    public void setAtendimentoAlta(AtendimentoAlta atendimentoAlta) {
        this.atendimentoAlta = atendimentoAlta;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EvolucaoProntuario getEvolucaoProntuario() {
        return evolucaoProntuario;
    }

    public void setEvolucaoProntuario(EvolucaoProntuario evolucaoProntuario) {
        this.evolucaoProntuario = evolucaoProntuario;
    }

    public LeitoQuarto getLeitoQuarto() {
        return leitoQuarto;
    }

    public void setLeitoQuarto(LeitoQuarto leitoQuarto) {
        this.leitoQuarto = leitoQuarto;
    }

    public QuartoInternacao getQuartoInternacao() {
        return quartoInternacao;
    }

    public void setQuartoInternacao(QuartoInternacao quartoInternacao) {
        this.quartoInternacao = quartoInternacao;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public TabelaCbo getCboProfissional() {
        if(cboProfissional == null){
            return tabelaCbo;
        }
        return cboProfissional;
    }

    public void setCboProfissional(TabelaCbo cboProfissional) {
        this.cboProfissional = cboProfissional;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Estado getEstado() {
        return estado;
    }

    public void setEstado(Estado estado) {
        this.estado = estado;
    }

    public String getTituloOriginal() {
        return tituloOriginal;
    }

    public void setTituloOriginal(String tituloOriginal) {
        this.tituloOriginal = tituloOriginal;
    }

    public String getDescricaoCipe() {
        return descricaoCipe;
    }

    public void setDescricaoCipe(String descricaoCipe) {
        this.descricaoCipe = descricaoCipe;
    }

    public String getDescricaoCid() {
        return descricaoCid;
    }

    public void setDescricaoCid(String descricaoCid) {
        this.descricaoCid = descricaoCid;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public List<GrupoProblemasCondicoes> getGrupoProblemasCondicoes() {
        return grupoProblemasCondicoes;
    }

    public void setGrupoProblemasCondicoes(List<GrupoProblemasCondicoes> grupoProblemasCondicoes) {
        this.grupoProblemasCondicoes = grupoProblemasCondicoes;
    }
}
