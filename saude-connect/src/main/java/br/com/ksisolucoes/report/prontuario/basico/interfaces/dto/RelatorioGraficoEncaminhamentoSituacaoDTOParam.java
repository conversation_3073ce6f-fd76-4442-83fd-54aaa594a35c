package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoEncaminhamentoSituacaoDTOParam implements Serializable {

    private List<Empresa> empresa;
    private List<Profissional> profissional;
    private List<TipoEncaminhamento> tipoEncaminhamento;
    private FormaApresentacao formaApresentacao;
    private DatePeriod periodo;
    private TipoDado tipoDado;

    public enum FormaApresentacao{
        TIPO_ENCAMINHAMENTO(1L),
        UNIDADE(2L);

        private Long value;

        private FormaApresentacao(Long value) {
            this.value = value;
        }

        public Long value() {
            return this.value;
        }

        public String getDescricao() {
            if (TIPO_ENCAMINHAMENTO.equals(this)) {
                return Bundle.getStringApplication("rotulo_tipo_encaminhamento");
            } else if (UNIDADE.equals(this)) {
                return Bundle.getStringApplication("rotulo_unidade");
            }
            return "";
        }

        @Override
        public String toString() {
            return getDescricao();
        }

    }

    public enum TipoDado{
        PERCENTUAL(1L),
        QUANTIDADE(2L);

        private Long value;

        private TipoDado(Long value) {
            this.value = value;
        }

        public Long value() {
            return this.value;
        }

        public String getDescricao() {
            if (PERCENTUAL.equals(this)) {
                return Bundle.getStringApplication("rotulo_percentual");
            } else if (QUANTIDADE.equals(this)) {
                return Bundle.getStringApplication("rotulo_quantidade");
            }
            return "";
        }

        @Override
        public String toString() {
            return getDescricao();
        }

    }

    @DescricaoParametro("rotulo_empresa")
    public List<Empresa> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(List<Empresa> empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_profissional")
    public List<Profissional> getProfissional() {
        return profissional;
    }

    public void setProfissional(List<Profissional> profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_tipo_encaminhamento")
    public List<TipoEncaminhamento> getTipoEncaminhamento() {
        return tipoEncaminhamento;
    }

    public void setTipoEncaminhamento(List<TipoEncaminhamento> tipoEncaminhamento) {
        this.tipoEncaminhamento = tipoEncaminhamento;
    }

    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getDescricaoFormaApresentacao() {
        if (getFormaApresentacao()!=null) {
            return getFormaApresentacao().getDescricao();
        }
        return "";
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodoAjustado() {
        return Data.adjustRangeDay(periodo);
    }

    public void setPeriodo(DatePeriod mesAno) {
        this.periodo = mesAno;
    }

    public TipoDado getTipoDado() {
        return tipoDado;
    }

    @DescricaoParametro("rotulo_tipo_dado")
    public String getDescricaoTipoDado() {
        if (getTipoDado()!=null) {
            return getTipoDado().getDescricao();
        }
        return "";
    }

    public void setTipoDado(TipoDado tipoDado) {
        this.tipoDado = tipoDado;
    }

}
