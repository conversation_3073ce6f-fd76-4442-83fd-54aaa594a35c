/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioResumoPedidoEstoqueDTO implements Serializable {

    private SubGrupo subGrupo;
    private Empresa unidadeOrigem;
    private Deposito deposito;
    private Produto produto;
    private Double quantidadeSolicitada;
    private Double estoqueDisponivel;

    public Deposito getDeposito() {
        return deposito;
    }

    public void setDeposito(Deposito deposito) {
        this.deposito = deposito;
    }

    public Double getEstoqueDisponivel() {
        return estoqueDisponivel;
    }

    public void setEstoqueDisponivel(Double estoqueDisponivel) {
        this.estoqueDisponivel = estoqueDisponivel;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Double getQuantidadeSolicitada() {
        return quantidadeSolicitada;
    }

    public void setQuantidadeSolicitada(Double quantidadeSolicitada) {
        this.quantidadeSolicitada = quantidadeSolicitada;
    }

    public SubGrupo getSubGrupo() {
        return subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    public Double getSaldo(){
        return Coalesce.asDouble(this.estoqueDisponivel) - Coalesce.asDouble(this.quantidadeSolicitada);
    }



}
