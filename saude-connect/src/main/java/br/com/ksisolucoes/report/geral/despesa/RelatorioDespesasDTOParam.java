package br.com.ksisolucoes.report.geral.despesa;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.TipoMovimentacao;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDespesasDTOParam implements Serializable{

    public enum FormaApresentacao{
        TIPO_DESPESA(Bundle.getStringApplication("rotulo_tipo_despesa")),
        BLOCO_PROGRMA(Bundle.getStringApplication("rotulo_bloco_programa")),
        SETOR(Bundle.getStringApplication("rotulo_setor"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    private Empresa empresa;
    private TipoMovimentacao tipoMovimentacao;
    private Pessoa pessoa;
    private ProgramaSaude programaSaude;
    private CentroCusto centroCusto;
    private FormaApresentacao formaApresentacao;
    private DatePeriod periodo;


    @DescricaoParametro("rotulo_estabelecimento")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_tipo_despesa")
    public TipoMovimentacao getTipoMovimentacao() {
        return tipoMovimentacao;
    }

    public void setTipoMovimentacao(TipoMovimentacao tipoMovimentacao) {
        this.tipoMovimentacao = tipoMovimentacao;
    }

    @DescricaoParametro("rotulo_fornecedor")
    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    @DescricaoParametro("rotulo_bloco_programa")
    public ProgramaSaude getProgramaSaude() {
        return programaSaude;
    }

    public void setProgramaSaude(ProgramaSaude programaSaude) {
        this.programaSaude = programaSaude;
    }

    @DescricaoParametro("rotulo_setor")
    public CentroCusto getCentroCusto() {
        return centroCusto;
    }

    public void setCentroCusto(CentroCusto centroCusto) {
        this.centroCusto = centroCusto;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }
}
