package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioDeclaracaoTfdDTO implements Serializable {

    private LaudoTfd laudoTfd;

    public LaudoTfd getLaudoTfd() {
        return laudoTfd;
    }

    public void setLaudoTfd(LaudoTfd laudoTfd) {
        this.laudoTfd = laudoTfd;
    }
}
