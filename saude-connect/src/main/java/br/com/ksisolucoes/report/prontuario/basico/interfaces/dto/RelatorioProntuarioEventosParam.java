package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class RelatorioProntuarioEventosParam implements Serializable {

    public RelatorioProntuarioEventosParam() {
        super();
    }
    /**
     * Holds value of property listaEmpresa.
     */
    private List<Empresa> listaEmpresa;

    /**
     * Getter for property listaEmpresa.
     * @return Value of property listaEmpresa.
     */
    public List<Empresa> getListaEmpresa() {
        return this.listaEmpresa;
    }

    /**
     * Setter for property listaEmpresa.
     * @param listaEmpresa New value of property listaEmpresa.
     */
    public void setListaEmpresa(List<Empresa> listaEmpresa) {
        this.listaEmpresa = listaEmpresa;
    }
    /**
     * Holds value of property listaUsuarioCadSus.
     */
    private List listaUsuarioCadSus;

    /**
     * Getter for property listaUsuarioCadSus.
     * @return Value of property listaUsuarioCadSus.
     */
    public List getListaUsuarioCadSus() {
        return this.listaUsuarioCadSus;
    }

    /**
     * Setter for property listaUsuarioCadSus.
     * @param listaUsuarioCadSus New value of property listaUsuarioCadSus.
     */
    public void setListaUsuarioCadSus(List listaUsuarioCadSus) {
        this.listaUsuarioCadSus = listaUsuarioCadSus;
    }
    /**
     * Holds value of property nomeUsuarioCadSus.
     */
    private String nomeUsuarioCadSus;

    /**
     * Getter for property nomeUsuarioCadSus.
     * @return Value of property nomeUsuarioCadSus.
     */
    public String getNomeUsuarioCadSus() {
        return this.nomeUsuarioCadSus;
    }

    /**
     * Setter for property nomeUsuarioCadSus.
     * @param nomeUsuarioCadSus New value of property nomeUsuarioCadSus.
     */
    public void setNomeUsuarioCadSus(String nomeUsuarioCadSus) {
        this.nomeUsuarioCadSus = nomeUsuarioCadSus;
    }
    /**
     * Holds value of property dataComeco.
     */
    private Date dataComeco;

    /**
     * Getter for property dataComeco.
     * @return Value of property dataComeco.
     */
    public Date getDataComeco() {
        return this.dataComeco;
    }

    /**
     * Setter for property dataComeco.
     * @param dataComeco New value of property dataComeco.
     */
    public void setDataComeco(Date dataComeco) {
        this.dataComeco = dataComeco;
    }
    /**
     * Holds value of property dataFim.
     */
    private Date dataFim;

    /**
     * Getter for property dataFim.
     * @return Value of property dataFim.
     */
    public Date getDataFim() {
        return this.dataFim;
    }

    /**
     * Setter for property dataFim.
     * @param dataFim New value of property dataFim.
     */
    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
}
