/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerAtendimentoEnfermagemDTOParam implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private Empresa empresa;
    private Long numeroAtendimento;
    private DatePeriod periodo;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

}
