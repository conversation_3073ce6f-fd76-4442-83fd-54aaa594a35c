/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomaticoExames;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ImpressaoNotificacaoTuberculoseDTO implements Serializable {

    private TuberculoseSintomatico tuberculoseSintomatico;
    private UsuarioCadsus paciente;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Long numeroSinan;
    private Date dataRegistroSinan;
    private Atendimento atendimento;
    private Empresa empresa;
    private String numeroCartao;
    private Long zona;
    private TuberculoseAcompanhamento tuberculoseAcompanhamento;
    private String resultadoExame;
    private String tipoEntrada;
    private String populacoesEspeciais;
    private String doencasAgravosAssociados;
    private String beneficiarioBolsaFamilia;
    private String descricaoFormaClinicaPulmonar;
    private String descricaoFormaClinicaExtraPulmonar;
    private String descricaoRxTorax;
    private String descricaoHIV;
    private String descricaoTerapiaAntirretroviral;
    private String descricaoHistopatologia;
    private String descricaoBaciloscopia;
    private String descricaoCultura;
    private String descricaoTrmTb;
    private String descricaoSensibilidade;

    public String getZona() {
        EnderecoDomicilioEsus.Localizacao localizacao = EnderecoDomicilioEsus.Localizacao.valueOf(zona);
        if (localizacao != null && localizacao.descricao() != null) {
            return localizacao.descricao();
        }
        return "";
    }

    public void setZona(Long zona) {
        this.zona = zona;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public String getNumeroCartao() {
        if (numeroCartao != null) {
            return StringUtilKsi.getNumeroCartaoFormatado(numeroCartao);
        }
        return "";
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Date getDataRegistroSinan() {
        return dataRegistroSinan;
    }

    public void setDataRegistroSinan(Date dataRegistroSinan) {
        this.dataRegistroSinan = dataRegistroSinan;
    }

    public Long getNumeroSinan() {
        return numeroSinan;
    }

    public void setNumeroSinan(Long numeroSinan) {
        this.numeroSinan = numeroSinan;
    }

    public TuberculoseSintomatico getTuberculoseSintomatico() {
        return tuberculoseSintomatico;
    }

    public void setTuberculoseSintomatico(TuberculoseSintomatico tuberculoseSintomatico) {
        this.tuberculoseSintomatico = tuberculoseSintomatico;
    }

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public TuberculoseAcompanhamento getTuberculoseAcompanhamento() {
        return tuberculoseAcompanhamento;
    }

    public void setTuberculoseAcompanhamento(TuberculoseAcompanhamento tuberculoseAcompanhamento) {
        this.tuberculoseAcompanhamento = tuberculoseAcompanhamento;
    }

    public String getResultadoExame() {
        return resultadoExame;
    }

    public void setResultadoExame(String resultadoExame) {
        this.resultadoExame = resultadoExame;
    }

    public String getTipoEntrada() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoTipoEntrada();
        }
        return "";
    }

    public String getPopulacoesEspeciais() {
        StringBuilder populacoesEspeciais = new StringBuilder();

        if (tuberculoseAcompanhamento != null) {
            if (tuberculoseAcompanhamento.getPopulacaoPrivadaLiberdade() != null && tuberculoseAcompanhamento.getPopulacaoPrivadaLiberdade() == 1L) {
                populacoesEspeciais.append("Populacao Privada Liberdade; ");
            }
            if (tuberculoseAcompanhamento.getPopulacaoSituacaoRua() != null && tuberculoseAcompanhamento.getPopulacaoSituacaoRua() == 1L) {
                populacoesEspeciais.append("Populacao Situacao Rua; ");
            }
            if (tuberculoseAcompanhamento.getPopulacaoProfissionaisSaude() != null && tuberculoseAcompanhamento.getPopulacaoProfissionaisSaude() == 1L) {
                populacoesEspeciais.append("Populacao Profissionais Saude; ");
            }
            if (tuberculoseAcompanhamento.getPopulacaoImigrante() != null && tuberculoseAcompanhamento.getPopulacaoImigrante() == 1L) {
                populacoesEspeciais.append("Populacao Imigrante; ");
            }
        }

        return populacoesEspeciais.toString();
    }

    public String getDoencasAgravosAssociados() {
        StringBuilder doencasAgravos = new StringBuilder();

        if (tuberculoseAcompanhamento != null) {
            if (tuberculoseAcompanhamento.getDoencasAgravosAids() != null && tuberculoseAcompanhamento.getDoencasAgravosAids() == 1L) {
                doencasAgravos.append("Aids; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosUsoDrogasIlicitas() != null && tuberculoseAcompanhamento.getDoencasAgravosUsoDrogasIlicitas() == 1L) {
                doencasAgravos.append("Uso Drogas Ilicitas; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosAlcoolismo() != null && tuberculoseAcompanhamento.getDoencasAgravosAlcoolismo() == 1L) {
                doencasAgravos.append("Alcoolismo; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosDiabetes() != null && tuberculoseAcompanhamento.getDoencasAgravosDiabetes() == 1L) {
                doencasAgravos.append("Diabetes; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosDoencaMental() != null && tuberculoseAcompanhamento.getDoencasAgravosDoencaMental() == 1L) {
                doencasAgravos.append("Doenca Mental; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosTabagismo() != null && tuberculoseAcompanhamento.getDoencasAgravosTabagismo() == 1L) {
                doencasAgravos.append("Tabagismo; ");
            }
            if (tuberculoseAcompanhamento.getDoencasAgravosOutro() != null && tuberculoseAcompanhamento.getDoencasAgravosOutro() == 1L) {
                String descricaoOutros = tuberculoseAcompanhamento.getDoencasAgravosDescricaoOutros();
                if (descricaoOutros != null && !descricaoOutros.isEmpty()) {
                    doencasAgravos.append(descricaoOutros).append("; ");
                }
            }
        }

        return doencasAgravos.toString();
    }

    public String getBeneficiarioBolsaFamilia() {
        if (paciente != null && paciente.getBeneficiarioBolsaFamilia() != null) {
            return paciente.getBeneficiarioBolsaFamilia() == 1L ? "Sim" : "Não";
        }
        return "";
    }

    public String getDescricaoFormaClinicaPulmonar() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoFormaClinicaPulmonar();
        }
        return "";
    }

    public String getDescricaoFormaClinicaExtraPulmonar() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoFormaClinicaExtraPulmonar();
        }
        return "";
    }

    public String getDescricaoRxTorax() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoRxTorax();
        }
        return "";
    }

    public String getDescricaoHIV() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoHIV();
        }
        return "";
    }

    public String getDescricaoTerapiaAntirretroviral() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoTerapiaAntirretroviral();
        }
        return "";
    }

    public String getDescricaoHistopatologia() {
        if (tuberculoseAcompanhamento != null) {
            return tuberculoseAcompanhamento.getDescricaoHistopatologia();
        }
        return "";
    }

    private String getDescricoesPorExame(TuberculoseSintomaticoExames.Exame tipoExame) {
        StringBuilder descricoes = new StringBuilder();

        if (resultadoExame != null && !resultadoExame.isEmpty()) {
            String[] resultados = resultadoExame.split(",\\s*");

            for (String resultado : resultados) {
                Long resultadoLong = Long.parseLong(resultado.trim());
                TuberculoseSintomaticoExames.ResultadoExame resultadoEnum = TuberculoseSintomaticoExames.ResultadoExame.valueOf(resultadoLong);

                if (resultadoEnum != null && resultadoEnum.isTipoExame(tipoExame)) {
                    descricoes.append(resultadoEnum.descricao()).append("\n ");
                }
            }

            return descricoes.toString().trim();
        }

        return descricoes.toString();
    }

    public String getDescricaoBaciloscopia() {
        return getDescricoesPorExame(TuberculoseSintomaticoExames.Exame.BACILOSCOPIA);
    }

    public String getDescricaoCultura() {
        return getDescricoesPorExame(TuberculoseSintomaticoExames.Exame.CULTURA);
    }

    public String getDescricaoTrmTb() {
        return getDescricoesPorExame(TuberculoseSintomaticoExames.Exame.TRM_TB);
    }

    public String getDescricaoSensibilidade() {
        return getDescricoesPorExame(TuberculoseSintomaticoExames.Exame.SENSIBILIDADE);
    }

}
