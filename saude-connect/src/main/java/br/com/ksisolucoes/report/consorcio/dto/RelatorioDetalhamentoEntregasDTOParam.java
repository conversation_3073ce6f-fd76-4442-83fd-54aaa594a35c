package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDetalhamentoEntregasDTOParam implements Serializable {

    private Long numeroGuia;
    private Empresa consorciado;
    private Produto produto;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private Ordenacao ordenacao;
    private TipoOrdenacao tipoOrdenacao;
    private TipoRelatorio tipoRelatorio;
    private TipoConta tipoConta;
    private Licitacao licitacao;
    private String licitacaoDescricao;
    @DescricaoParametro("rotulo_tipo_conta")
    public TipoConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(TipoConta tipoConta) {
        this.tipoConta = tipoConta;
    }

    public Licitacao getLicitacao() {
        return licitacao;
    }

    public void setLicitacao(Licitacao licitacao) {
        this.licitacao = licitacao;
    }

    @DescricaoParametro("rotulo_consorcio")
    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_produto")
    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_n_guia")
    public Long getNumeroGuia() {
        return numeroGuia;
    }

    public void setNumeroGuia(Long numeroGuia) {
        this.numeroGuia = numeroGuia;
    }

    public static enum FormaApresentacao implements IEnum<FormaApresentacao> {

        GUIA(1L, Bundle.getStringApplication("rotulo_guia")),
        CONSORCIADO(1L, Bundle.getStringApplication("rotulo_consorciado")),
        PRODUTO(2L, Bundle.getStringApplication("rotulo_produto")),
        MENSAL(3L, Bundle.getStringApplication("rotulo_mensal")),
        DIARIO(4L, Bundle.getStringApplication("rotulo_diario")),;

        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoResumo implements IEnum<TipoResumo> {

        GUIA(1L, Bundle.getStringApplication("rotulo_guia")),
        CONSORCIADO(1L, Bundle.getStringApplication("rotulo_consorciado")),
        PRODUTO(2L, Bundle.getStringApplication("rotulo_produto")),
        MENSAL(3L, Bundle.getStringApplication("rotulo_mensal")),
        DIARIO(4L, Bundle.getStringApplication("rotulo_diario")),;

        private Long value;
        private String descricao;

        private TipoResumo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum Ordenacao implements IEnum<Ordenacao> {

        GUIA(1L, Bundle.getStringApplication("rotulo_guia")),
        DATA(2L, Bundle.getStringApplication("rotulo_data")),
        CONSORCIADO(3L, Bundle.getStringApplication("rotulo_consorciado")),
        PRODUTO(4L, Bundle.getStringApplication("rotulo_produto")),
        VALOR(5L, Bundle.getStringApplication("rotulo_valor")),;

        private Long value;
        private String descricao;

        private Ordenacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoOrdenacao implements IEnum<TipoOrdenacao> {

        CRESCENTE("asc", Bundle.getStringApplication("rotulo_crescente")),
        DECRESCENTE("desc", Bundle.getStringApplication("rotulo_decrescente")),;

        private String value;
        private String descricao;

        private TipoOrdenacao(String value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public enum TipoRelatorio implements IEnum<TipoRelatorio> {

        DETALHADO(0L, Bundle.getStringApplication("rotulo_detalhado")),
        RESUMIDO(1L, Bundle.getStringApplication("rotulo_resumido")),;

        private Long value;
        private String descricao;

        private TipoRelatorio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public TipoRelatorio getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(TipoRelatorio tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_licitacao")
    public String getLicitacaoDescricao() {
        return licitacao.getDescricaoLicitacaoFormatado();
    }

    public void setLicitacaoDescricao(String licitacaoDescricao) {
        this.licitacaoDescricao = licitacaoDescricao;
    }
}
