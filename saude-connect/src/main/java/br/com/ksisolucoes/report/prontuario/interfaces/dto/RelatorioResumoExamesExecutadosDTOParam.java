package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoExamesExecutadosDTOParam implements Serializable {

    private Convenio convenio;
    private DatePeriod periodo;
    private Long formaApresentacao;
    private Empresa estabelecimento;
    private TipoExame tipoExame;
    private Profissional profissionalExecutante;
    private Profissional profissionalResponsavel;

    public enum FormaApresentacao implements IEnum<FormaApresentacao> {

        GERAL(0L, Bundle.getStringApplication("rotulo_geral")),
        CONVENIO(1L, Bundle.getStringApplication("rotulo_convenio")),
        TIPO_EXAME(2L, Bundle.getStringApplication("rotulo_tipo_exame")),
        PROFISSIONAL_EXECUTANTE(3L, Bundle.getStringApplication("rotulo_profissional_executante")),
        PROFISSIONAL_RESPONSAVEL(4L, Bundle.getStringApplication("rotulo_profissional_responsavel"));

        private String descricao;
        private Long value;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static FormaApresentacao valueOf(Long value) {
            for (FormaApresentacao formaApresentacao : FormaApresentacao.values()) {
                if (formaApresentacao.value().equals(value)) {
                    return formaApresentacao;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public Empresa getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Empresa estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    @DescricaoParametro("rotulo_tipo_exame")
    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public Long getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(Long formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_convenio")
    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_profissional_executante")
    public Profissional getProfissionalExecutante() {
        return profissionalExecutante;
    }

    public void setProfissionalExecutante(Profissional profissionalExecutante) {
        this.profissionalExecutante = profissionalExecutante;
    }

    @DescricaoParametro("rotulo_profissional_responsavel")
    public Profissional getProfissionalResponsavel() {
        return profissionalResponsavel;
    }

    public void setProfissionalResponsavel(Profissional profissionalResponsavel) {
        this.profissionalResponsavel = profissionalResponsavel;
    }

}
