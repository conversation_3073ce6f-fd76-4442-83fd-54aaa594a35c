package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Util;

import java.io.Serializable;
import java.util.Date;

public class RelatorioRelacaoProdutoDTO implements Serializable {

    private String empresaCodigo;
    private String empresaDescricao;
    private Long grupoCodigo;
    private String grupoDescricao;
    private Long subGrupoCodigo;
    private String subGrupoDescricao;
    private String produtoCodigo;
    private String produtoDescricao;
    private String produtoReferencia;
    private String unidadeUnidade;
    private Double estoqueFisico;
    private Double preco;
    private Double estoqueMinimo;
    private Double quantidadeMultipla;
    private Double quantidadeIdeal;
    private Long codigoDeposito;
    private String descricaoDeposito;
    private Long codigoDepositoPadrao;
    private String descricaoDepositoPadrao;
    private String grupoEstoque;
    private Date dataValidade;
    private Long codigoLocalizacaoEstrutura;
    private String mascaraLocalizacaoEstrutura;

    public RelatorioRelacaoProdutoDTO(String empresaCodigo, String empresaDescricao, Long grupoCodigo, String grupoDescricao, Long subGrupoCodigo, String subGrupoDescricao, String produtoCodigo, String produtoDescricao, String produtoReferencia, String unidadeUnidade, Double estoqueFisico, Double estoqueNaoConforme, Double preco, Double totalPreco, Double estoqueMinimo, Double quantidadeMultipla, Double quantidadeIdeal, Long codigoDeposito, String descricaoDeposito) {
        super();
        this.empresaCodigo = empresaCodigo;
        this.empresaDescricao = empresaDescricao;
        this.grupoCodigo = grupoCodigo;
        this.grupoDescricao = grupoDescricao;
        this.subGrupoCodigo = subGrupoCodigo;
        this.subGrupoDescricao = subGrupoDescricao;
        this.produtoCodigo = produtoCodigo;
        this.produtoDescricao = produtoDescricao;
        this.produtoReferencia = produtoReferencia;
        this.unidadeUnidade = unidadeUnidade;
        this.estoqueFisico = estoqueFisico;
        this.estoqueNaoConforme = estoqueNaoConforme;
        this.preco = preco;
        this.totalPreco = totalPreco;
        this.estoqueMinimo = estoqueMinimo;
        this.quantidadeIdeal = quantidadeIdeal;
        this.quantidadeMultipla = quantidadeMultipla;
        this.codigoDeposito = codigoDeposito;
        this.descricaoDeposito = descricaoDeposito;
    }

    public RelatorioRelacaoProdutoDTO() {
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public String getGrupoEstoque() {
        return grupoEstoque;
    }

    public void setGrupoEstoque(String grupoEstoque) {
        this.grupoEstoque = grupoEstoque;
    }

    public void setEmpresaCodigo(String empresaCodigo) {
        this.empresaCodigo = empresaCodigo;
    }

    public void setEmpresaDescricao(String empresaDescricao) {
        this.empresaDescricao = empresaDescricao;
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public void setGrupoCodigo(Long grupoCodigo) {
        this.grupoCodigo = grupoCodigo;
    }

    public void setGrupoDescricao(String grupoDescricao) {
        this.grupoDescricao = grupoDescricao;
    }

    public void setPreco(Double preco) {
        this.preco = preco;
    }

    public void setProdutoCodigo(String produtoCodigo) {
        this.produtoCodigo = produtoCodigo;
    }

    public void setProdutoDescricao(String produtoDescricao) {
        this.produtoDescricao = produtoDescricao;
    }

    public void setSubGrupoCodigo(Long subGrupoCodigo) {
        this.subGrupoCodigo = subGrupoCodigo;
    }

    public void setSubGrupoDescricao(String subGrupoDescricao) {
        this.subGrupoDescricao = subGrupoDescricao;
    }

    public void setUnidadeUnidade(String unidadeUnidade) {
        this.unidadeUnidade = unidadeUnidade;
    }

    public String getEmpresaCodigo() {
        return empresaCodigo;
    }

    public String getEmpresaDescricao() {
        return empresaDescricao;
    }

    public String getEmpresaDescricaoFormatado() {
        return Util.getDescricaoFormatado(this.empresaCodigo, this.empresaDescricao);
    }

    public Long getGrupoCodigo() {
        return grupoCodigo;
    }

    public String getGrupoDescricao() {
        return grupoDescricao;
    }

    public String getGrupoDescricaoFormatado() {
        return Util.getDescricaoFormatado(this.grupoCodigo, this.grupoDescricao);
    }

    public Long getSubGrupoCodigo() {
        return subGrupoCodigo;
    }

    public String getSubGrupoDescricao() {
        return subGrupoDescricao;
    }

    public String getSubGrupoDescricaoFormatado() {
        return Util.getDescricaoFormatado(this.subGrupoCodigo, this.subGrupoDescricao);
    }

    public String getProdutoCodigo() {
        return produtoCodigo;
    }

    public String getProdutoDescricao() {
        return produtoDescricao;
    }

    public String getProdutoReferencia() {
        return produtoReferencia;
    }

    public void setProdutoReferencia(String produtoReferencia) {
        this.produtoReferencia = produtoReferencia;
    }

    public String getProdutoDescricaoFormatado() {
        return Util.getDescricaoFormatado(this.produtoReferencia, this.produtoDescricao);
    }

    public String getUnidadeUnidade() {
        return unidadeUnidade;
    }

    public Double getEstoqueFisico() {
        return estoqueFisico;
    }

    /**
     * Holds value of property estoqueNaoConforme.
     */
    private Double estoqueNaoConforme;

    /**
     * Getter for property estoqueNaoConforme.
     *
     * @return Value of property estoqueNaoConforme.
     */
    public Double getEstoqueNaoConforme() {
        return this.estoqueNaoConforme;
    }

    /**
     * Setter for property estoqueNaoConforme.
     *
     * @param estoqueNaoConforme New value of property estoqueNaoConforme.
     */
    public void setEstoqueNaoConforme(Double estoqueNaoConforme) {
        this.estoqueNaoConforme = estoqueNaoConforme;
    }

    public Double getPreco() {
        return preco;
    }
//        public Double getTotalPreco() {
//            return Coalesce.asDouble( this.preco ) * this.estoqueFisico;
//        }
    /**
     * Mantm o valor da propriedade totalPreco.
     */
    private Double totalPreco;

    /**
     * "Getter" para a propriedade totalPreco.
     *
     * @return Valor para a propriedade totalPreco.
     */
    public Double getTotalPreco() {
        return Coalesce.asDouble(this.totalPreco);
    }

    /**
     * "Setter" para a propriedade totalPreco.
     *
     * @param totalPreco Novo valor para a propriedade totalPreco.
     */
    public void setTotalPreco(Double totalPreco) {
        this.totalPreco = totalPreco;
    }

    public Double getEstoqueMinimo() {
        return estoqueMinimo;
    }

    public void setEstoqueMinimo(Double estoqueMinimo) {
        this.estoqueMinimo = estoqueMinimo;
    }

    public Double getQuantidadeMultipla() {
        return quantidadeMultipla;
    }

    public void setQuantidadeMultipla(Double quantidadeMultipla) {
        this.quantidadeMultipla = quantidadeMultipla;
    }

    public Double getQuantidadeIdeal() {
        return quantidadeIdeal;
    }

    public void setQuantidadeIdeal(Double quantidadeIdeal) {
        this.quantidadeIdeal = quantidadeIdeal;
    }

    public Long getCodigoDeposito() {
        return codigoDeposito;
    }

    public void setCodigoDeposito(Long codigoDeposito) {
        this.codigoDeposito = codigoDeposito;
    }

    public String getDescricaoDeposito() {
        return descricaoDeposito;
    }

    public void setDescricaoDeposito(String descricaoDeposito) {
        this.descricaoDeposito = descricaoDeposito;
    }

    public Long getCodigoDepositoPadrao() {
        return codigoDepositoPadrao;
    }

    public void setCodigoDepositoPadrao(Long codigoDepositoPadrao) {
        this.codigoDepositoPadrao = codigoDepositoPadrao;
    }

    public String getDescricaoDepositoPadrao() {
        return descricaoDepositoPadrao;
    }

    public void setDescricaoDepositoPadrao(String descricaoDepositoPadrao) {
        this.descricaoDepositoPadrao = descricaoDepositoPadrao;
    }

    public String getDescricaoDepositoFormatado() {
        if (this.codigoDeposito == null || this.descricaoDeposito == null) {
            return Util.getDescricaoFormatado(this.codigoDepositoPadrao, this.descricaoDepositoPadrao);
        } else {
            return Util.getDescricaoFormatado(this.codigoDeposito, this.descricaoDeposito);
        }
    }

    public Long getCodigoLocalizacaoEstrutura() {
        return codigoLocalizacaoEstrutura;
    }

    public void setCodigoLocalizacaoEstrutura(Long codigoLocalizacaoEstrutura) {
        this.codigoLocalizacaoEstrutura = codigoLocalizacaoEstrutura;
    }

    public String getMascaraLocalizacaoEstrutura() {
        return mascaraLocalizacaoEstrutura;
    }

    public void setMascaraLocalizacaoEstrutura(String mascaraLocalizacaoEstrutura) {
        this.mascaraLocalizacaoEstrutura = mascaraLocalizacaoEstrutura;
    }

    public String getDataValidadeFormatada() {
        return Data.formatar(getDataValidade());
    }

    public boolean getDataProximaValidade() {
        if (this.getDataValidade() != null && EstoqueHelper.getSinalizacaoCoresSaldoEstoque()
                && EstoqueHelper.getTempoProdutoProximoVencimento() != null
                && EstoqueHelper.getTempoProdutoProximoVencimento() >0
                && !(DataUtil.isDateAfter(DataUtil.getDataAtual(), this.getDataValidade()))){
            int date = DataUtil.calcularDiferencaEmDias(DataUtil.getDataAtual(), this.getDataValidade());
            return date <= EstoqueHelper.getTempoProdutoProximoVencimento();
        }
        return false;
    }

    public boolean getDataVencido() {
        if (this.getDataValidade() != null && EstoqueHelper.getSinalizacaoCoresSaldoEstoque()){
            return DataUtil.isDateAfter(DataUtil.getDataAtual(), this.getDataValidade());
        }
        return false;
    }

}
