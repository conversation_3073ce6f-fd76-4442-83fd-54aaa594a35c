package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.cadsus.Escolaridade;
import br.com.ksisolucoes.vo.cadsus.EtniaIndigena;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.prontuario.avaliacao.EstadoNutricional;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;

import java.io.Serializable;

/**
 * <AUTHOR> Americo
 */
public class RelatorioEstadoNutricionalDTOParam implements Serializable {

    private Empresa empresa;
    private DatePeriod periodo;
    private Long tipoRelatorio;
    private Long formaApresentacao;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    private String sexo;
    private Raca raca;
    private EtniaIndigena etniaIndigena;
    private Long escolaridade;
    private Long faseVida;
    private EstadoNutricional estadoNutricional;
    private String descricaoEstadoNutricional;

    public static enum FaseVida implements IEnum<FaseVida> {
//        todos (default) 0 - 5 anos, 5 - 19, Adulto (20-60 anos), Idoso (acima de 60 anos)
        CRIANCA(0L, 0L, 60L, "0 - 5 Anos"),
        ADOLESCENTE(1L, 61L, 240L, "5 - 19 Anos"),
        ADULTO(2L, 241L, 720L, "Adulto 20 - 60"),
        IDOSO(3L, 720L, 1800L, "Idoso"),

        ;

        private Long value;
        private Long idadeInicial;
        private Long idadeFinal;
        private String descricao;

        private FaseVida(Long value, Long idadeInicial ,Long idadeFinal, String descricao) {
            this.value = value;
            this.idadeInicial = idadeInicial;
            this.idadeFinal = idadeFinal;
            this.descricao = descricao;
        }

        public static FaseVida valueOf(Long value) {
            for (FaseVida faseVida : FaseVida.values()) {
                if (faseVida.value().equals(value)) {
                    return faseVida;
                }
            }
            return null;
        }

        public Long getIdadeInicial() {
            return idadeInicial;
        }

        public Long getIdadeFinal() {
            return idadeFinal;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public String getTipoRelatorioDescricao() {
        if (tipoRelatorio != null) {
            return TipoRelatorio.valueOf(tipoRelatorio).descricao();
        }
        return null;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresetacaoDescricao() {
        if (formaApresentacao != null) {
            return FormaApresentacao.valueOf(formaApresentacao).descricao();
        }
        return null;
    }

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    @DescricaoParametro("rotulo_equipe_micro_area")
    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    @DescricaoParametro("rotulo_raca")
    public Raca getRaca() {
        return raca;
    }

    public void setRaca(Raca raca) {
        this.raca = raca;
    }

    @DescricaoParametro("rotulo_etnia")
    public EtniaIndigena getEtniaIndigena() {
        return etniaIndigena;
    }

    public void setEtniaIndigena(EtniaIndigena etniaIndigena) {
        this.etniaIndigena = etniaIndigena;
    }

    @DescricaoParametro("rotulo_escolaridade")
    public Long getEscolaridade() {
        return escolaridade;
    }

    public void setEscolaridade(Long escolaridade) {
        this.escolaridade = escolaridade;
    }


    @DescricaoParametro("rotulo_fase_vida")
    public String getFaseVidaDescricao() {
        if (getFaseVida() != null) {
            return FaseVida.valueOf(getFaseVida()).descricao;
        } else {
            return null;
        }
    }

    public Long getFaseVida() {
        return faseVida;
    }

    public void setFaseVida(Long faseVida) {
        this.faseVida = faseVida;
    }

    public Long getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(Long formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public EstadoNutricional getEstadoNutricional() {
        return estadoNutricional;
    }

    public void setEstadoNutricional(EstadoNutricional estadoNutricional) {
        this.estadoNutricional = estadoNutricional;
    }

    @DescricaoParametro("rotulo_estado_nutricional")
    public String getDescricaoEstadoNutricional() {
        return descricaoEstadoNutricional;
    }

    public void setDescricaoEstadoNutricional(String descricaoEstadoNutricional) {
        this.descricaoEstadoNutricional = descricaoEstadoNutricional;
    }

    public static enum TipoRelatorio implements IEnum<TipoRelatorio> {

        RESUMIDO(0L, Bundle.getStringApplication("rotulo_resumido")),
        DETALHADO(1L, Bundle.getStringApplication("rotulo_detalhado")),;

        private Long value;
        private String descricao;

        private TipoRelatorio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

        public static TipoRelatorio valueOf(Long v) {
            for (TipoRelatorio tipoRelatorio : TipoRelatorio.values()) {
                if (tipoRelatorio.value.equals(v)) {
                    return tipoRelatorio;
                }
            }
            return null;
        }

    }

    public static enum FormaApresentacao implements IEnum<FormaApresentacao> {

        UNIDADE(0L, Bundle.getStringApplication("rotulo_unidade")),
        ESTADO_NUTRICIONAL(1L, Bundle.getStringApplication("rotulo_estado_nutricional")),
        ESCOLARIDADE(2L, Bundle.getStringApplication("rotulo_escolaridade")),
        RACA(3L, Bundle.getStringApplication("rotulo_raca")),;

        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

        public static FormaApresentacao valueOf(Long v) {
            for (FormaApresentacao formaApresentacao : FormaApresentacao.values()) {
                if (formaApresentacao.value.equals(v)) {
                    return formaApresentacao;
                }
            }
            return null;
        }

    }


}
