package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorEdital;
import br.com.ksisolucoes.vo.consorcio.ContratoEdital;
import br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital;

import java.io.Serializable;

public class ConsorcioPrestadorDTO implements Serializable {

    private ConsorcioPrestadorEdital consorcioPrestadorEdital;

    private TabelaPrecoEdital tabelaPrecoEdital;

    private ContratoEdital contratoEdital;

    private ConsorcioPrestador consorcioPrestador;

    public ConsorcioPrestador getConsorcioPrestador() {
        return consorcioPrestador;
    }

    public void setConsorcioPrestador(ConsorcioPrestador consorcioPrestador) {
        this.consorcioPrestador = consorcioPrestador;
    }

    public ContratoEdital getContratoEdital() {
        return contratoEdital;
    }

    public void setContratoEdital(ContratoEdital contratoEdital) {
        this.contratoEdital = contratoEdital;
    }

    public TabelaPrecoEdital getTabelaPrecoEdital() {

        return tabelaPrecoEdital;
    }

    public void setTabelaPrecoEdital(TabelaPrecoEdital tabelaPrecoEdital) {
        this.tabelaPrecoEdital = tabelaPrecoEdital;
    }

    public ConsorcioPrestadorEdital getConsorcioPrestadorEdital() {
        return consorcioPrestadorEdital;
    }

    public void setConsorcioPrestadorEdital(ConsorcioPrestadorEdital consorcioPrestadorEdital) {
        this.consorcioPrestadorEdital = consorcioPrestadorEdital;
    }
}