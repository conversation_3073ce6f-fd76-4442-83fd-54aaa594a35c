/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioEstruturaEquipamentoDTO implements Serializable{

    private List lstProduto;
    private int niveis;
    private int totalEstrututras = -1;
    private Long revisao;
    private Long tipoRelatorio;
    private boolean cancelados;
    private Double quantidadeInicial;
    private String apenasPeso;
    private Long codigoProcedencia;

    public Long getCodigoProcedencia() {
        return codigoProcedencia;
    }

    public void setCodigoProcedencia(Long codigoProcedencia) {
        this.codigoProcedencia = codigoProcedencia;
    }

    public List getLstProduto() {
        return lstProduto;
    }

    public void setLstProduto(List lstProduto) {
        this.lstProduto = lstProduto;
    }

    public int getNiveis() {
        return niveis;
    }

    public void setNiveis(int niveis) {
        this.niveis = niveis;
    }

    public int getTotalEstrututras() {
        return totalEstrututras;
    }

    public void setTotalEstrututras(int totalEstrututras) {
        this.totalEstrututras = totalEstrututras;
    }

    public Long getRevisao() {
        return Coalesce.asLong(revisao, -1L);
    }

    public void setRevisao(Long revisao) {
        this.revisao = revisao;
    }

    public Long getTipoRelatorio() {
        return Coalesce.asLong(tipoRelatorio);
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public boolean isCancelados() {
        return cancelados;
    }

    public void setCancelados(boolean cancelados) {
        this.cancelados = cancelados;
    }

    public Double getQuantidadeInicial() {
        return quantidadeInicial;
    }

    public void setQuantidadeInicial(Double quantidadeInicial) {
        this.quantidadeInicial = quantidadeInicial;
    }

    public String getApenasPeso() {
        return apenasPeso;
    }

    public void setApenasPeso(String apenasPeso) {
        this.apenasPeso = apenasPeso;
    }
}
