package br.com.ksisolucoes.report.vacina.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;

import java.io.Serializable;
import java.util.List;

public class RelacaoVacinasNaoAplicadasDTOParam implements Serializable {

    private Empresa empresa;
    private Calendario calendario;
    private List<VacinaCalendario> vacinas;
    private Long dose;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    private FaixaEtaria faixaEtaria;
    private FaixaEtariaItem faixaEtariaItem;
    private FormaApresentacao formaApresentacao = FormaApresentacao.VACINA;
    private Ordenacao ordenacao;
    private TipoRelatorio tipoArquivo;
    private boolean filtrarEmpresasUsuarios;
    private List<Long> listCodigosEmpresasUsuario;

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }
    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_estrategia")
    public Calendario getCalendario() {
        return calendario;
    }
    public void setCalendario(Calendario calendario) {
        this.calendario = calendario;
    }

    @DescricaoParametro("rotulo_vacina")
    public List<VacinaCalendario> getVacinas() {
        return vacinas;
    }

    public void setVacinas(List<VacinaCalendario> vacinas) {
        this.vacinas = vacinas;
    }

    public String getDescricaoVacinas() {
        if (getVacinas() != null && !getVacinas().isEmpty()) {
            return getVacinas().get(0).getTipoVacina().getDescricao();
        }
        return null;
    }
    @DescricaoParametro("rotulo_dose")
    public String getDescricaoDose() {
        VacinaCalendario.Doses dose = VacinaCalendario.Doses.valueOf(getDose());
        if (dose != null) {
            return dose.descricao();
        }
        return null;
    }

    public Long getDose() {
        return dose;
    }
    public void setDose(Long dose) {
        this.dose = dose;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getArea() {
        return equipeArea;
    }
    public void setArea(EquipeArea area) {
        this.equipeArea = area;
    }

    @DescricaoParametro("rotulo_microarea")
    public String getDescricaoEquipeMicroArea() {
        return equipeMicroArea != null ? (equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : Bundle.getStringApplication("rotulo_sem_profissional"))) : "";
    }
    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }
    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    @DescricaoParametro("rotulo_faixa_etaria_padrao")
    public FaixaEtaria getFaixaEtaria() {
        return faixaEtaria;
    }
    public void setFaixaEtaria(FaixaEtaria faixaEtaria) {
        this.faixaEtaria = faixaEtaria;
    }


    public FaixaEtariaItem getFaixaEtariaItem() {
        return faixaEtariaItem;
    }
    public void setFaixaEtaria(FaixaEtariaItem faixaEtariaItem) {
        this.faixaEtariaItem = faixaEtariaItem;
    }

    @DescricaoParametro("rotulo_faixa_etaria")
    public String getDescricaoFaixaEtariaItem() {
        return faixaEtariaItem != null ? faixaEtariaItem.getDescricao() : "";
    }

    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }
    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }
    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getDescricaoFormaApresentacao() {
        return formaApresentacao.descricao();
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }
    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }
    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    public boolean isFiltrarEmpresasUsuarios() {
        return filtrarEmpresasUsuarios;
    }

    public void setFiltrarEmpresasUsuarios(boolean filtrarEmpresasUsuarios) {
        this.filtrarEmpresasUsuarios = filtrarEmpresasUsuarios;
    }

    public List<Long> getListCodigosEmpresasUsuario() {
        return listCodigosEmpresasUsuario;
    }

    public void setListCodigosEmpresasUsuario(List<Long> listCodigosEmpresasUsuario) {
        this.listCodigosEmpresasUsuario = listCodigosEmpresasUsuario;
    }

    public enum FormaApresentacao implements IEnum<FormaApresentacao> {
        VACINA(Bundle.getStringApplication("rotulo_vacina"));

        private String label;

        FormaApresentacao(String label) {
            this.label = label;
        }

        @Override
        public Object value() {
            return this;
        }

        @Override
        public String descricao() {
            return label;
        }

        public static FormaApresentacao valeuOf(Long value) {
            for (FormaApresentacao formaApresentacao : FormaApresentacao.values()) {
                if (formaApresentacao.value().equals(value)) {
                    return formaApresentacao;
                }
            }
            return null;
        }
    }

    public enum Ordenacao implements IEnum<Ordenacao> {
        PACIENTE(Bundle.getStringApplication("rotulo_paciente"));

        private String label;

        Ordenacao(String label) {
            this.label = label;
        }

        @Override
        public Object value() {
            return this;
        }

        @Override
        public String descricao() {
            return label;
        }

        public static Ordenacao valeuOf(Long value) {
            for (Ordenacao ordenacao : Ordenacao.values()) {
                if (ordenacao.value().equals(value)) {
                    return ordenacao;
                }
            }
            return null;
        }
    }

    @Override
    public String toString() {
        return "RelacaoVacinasNaoAplicadasDTOParam{" +
                "empresa=" + empresa +
                ", calendario=" + calendario +
                ", vacinas=" + vacinas +
                ", dose=" + dose +
                ", equipeArea=" + equipeArea +
                ", equipeMicroArea=" + equipeMicroArea +
                ", faixaEtaria=" + faixaEtaria +
                ", faixaEtariaItem=" + faixaEtariaItem +
                ", formaApresentacao=" + formaApresentacao +
                ", ordenacao=" + ordenacao +
                ", tipoArquivo=" + tipoArquivo +
                ", filtrarEmpresasUsuarios=" + filtrarEmpresasUsuarios +
                ", listCodigosEmpresasUsuario=" + listCodigosEmpresasUsuario +
                '}';
    }
}
