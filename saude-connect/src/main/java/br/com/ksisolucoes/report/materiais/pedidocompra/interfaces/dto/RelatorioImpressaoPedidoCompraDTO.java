package br.com.ksisolucoes.report.materiais.pedidocompra.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoPedidoCompraDTO implements Serializable {

    private PedidoCompra pedidoCompra;
    private Empresa emitente;
    private List<PedidoCompraItem> pedidoCompraItem;

    public PedidoCompra getPedidoCompra() {
        return pedidoCompra;
    }

    public void setPedidoCompra(PedidoCompra pedidoCompra) {
        this.pedidoCompra = pedidoCompra;
    }

    public Empresa getEmitente() {
        return emitente;
    }

    public void setEmitente(Empresa emitente) {
        this.emitente = emitente;
    }

    public List<PedidoCompraItem> getPedidoCompraItem() {
        return pedidoCompraItem;
    }

    public void setPedidoCompraItem(List<PedidoCompraItem> pedidoCompraItem) {
        this.pedidoCompraItem = pedidoCompraItem;
    }
}
