package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoEtiquetaVolumeDTO implements Serializable {

    private Empresa remetente;
    private Empresa destinatario;
    private Integer numeroEtiqueta;

    public Integer getNumeroEtiqueta() {
        return numeroEtiqueta;
    }

    public void setNumeroEtiqueta(Integer numeroEtiqueta) {
        this.numeroEtiqueta = numeroEtiqueta;
    }

    public Empresa getRemetente() {
        return remetente;
    }

    public void setRemetente(Empresa remetente) {
        this.remetente = remetente;
    }

    public Empresa getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(Empresa destinatario) {
        this.destinatario = destinatario;
    }
}
