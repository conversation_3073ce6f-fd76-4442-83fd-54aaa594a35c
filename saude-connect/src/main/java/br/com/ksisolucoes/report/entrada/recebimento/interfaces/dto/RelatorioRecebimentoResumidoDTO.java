/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.recebimento.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRecebimentoResumidoDTO extends RegistroItemNotaFiscal {

    private Double valorTotal;

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }
}
