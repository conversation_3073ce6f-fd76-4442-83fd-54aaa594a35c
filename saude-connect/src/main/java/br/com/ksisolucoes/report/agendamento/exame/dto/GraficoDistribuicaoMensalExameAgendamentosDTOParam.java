package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GraficoDistribuicaoMensalExameAgendamentosDTOParam implements Serializable{

    public enum FormaApresentacao{
        UNIDADE(Bundle.getStringApplication("rotulo_empresa")),
        UNIDADE_ORIGEM(Bundle.getStringApplication("rotulo_unidade_origem")),
        SEXO(Bundle.getStringApplication("rotulo_sexo")),
        TIPO_PROCEDIMENTO(Bundle.getStringApplication("rotulo_tipo_procedimento")),
        FAIXA_ETARIA(Bundle.getStringApplication("rotulo_faixa_etaria")),
        AREA(Bundle.getStringApplication("rotulo_area"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum TipoDado{
        PORCENTACEM(Bundle.getStringApplication("rotulo_porcentagem")),
        QUANTIDADE(Bundle.getStringApplication("rotulo_quantidade"));

        private String name;

        private TipoDado(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }
    
    public enum TipoAgendamento {
        
        AMBOS(Bundle.getStringApplication("rotulo_ambos")),
        TFD(Bundle.getStringApplication("rotulo_tfd")),
        ENCAMINHAMENTO(Bundle.getStringApplication("rotulo_encaminhamento"));
        
        private String descricao;
        
        private TipoAgendamento(String descricao){
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    private TipoDado tipoDado;
    private FormaApresentacao formaApresentacao;
    private OperadorValor<List<Empresa>> empresa;
    private List<Empresa> empresaOrigem;
    private OperadorValor<List<TipoProcedimento>> tipoProcedimento;
    private FaixaEtaria faixaEtaria;
    private Long maximoSerie;
    private DatePeriod periodo;
    private EquipeArea equipeArea;
    private TipoAgendamento tipoAgendamento;

    @DescricaoParametro("rotulo_tipo_dado")
    public TipoDado getTipoDado() {
        return tipoDado;
    }

    public void setTipoDado(TipoDado tipoDado) {
        this.tipoDado = tipoDado;
    }

    @DescricaoParametro("rotulo_numero_maximo_serie_abv")
    public Long getMaximoSerie() {
        return maximoSerie;
    }

    public void setMaximoSerie(Long maximoSerie) {
        this.maximoSerie = maximoSerie;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(OperadorValor<List<Empresa>> empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_unidade_origem")
    public List<Empresa> getEmpresaOrigem() {
        return empresaOrigem;
    }

    public void setEmpresaOrigem(List<Empresa> empresaOrigem) {
        this.empresaOrigem = empresaOrigem;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_faixa_etaria")
    public FaixaEtaria getFaixaEtaria() {
        return faixaEtaria;
    }

    public void setFaixaEtaria(FaixaEtaria faixaEtaria) {
        this.faixaEtaria = faixaEtaria;
    }
    
    @DescricaoParametro("rotulo_tipo_procedimento")
    public OperadorValor<List<TipoProcedimento>> getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(OperadorValor<List<TipoProcedimento>> tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    public TipoAgendamento getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamento tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    
}
