/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelacaoVagasAgendaDTO implements Serializable{
    
    private Empresa empresa;
    private Profissional profissional;
    private TipoProcedimento tipoProcedimento;
    private ExameProcedimento exameProcedimento;
    private Long agendaPrimeiraVez;
    private Long agendaRegulada;
    private Long agendaRetorno;
    private Long agendaInterna;

    private Long ativaPrimeiraVez;
    private Long ativaRegulada;
    private Long ativaRetorno;
    private Long ativaInterna;

    private Long bloqueadaPrimeiraVez;
    private Long bloqueadaRegulada;
    private Long bloqueadaRetorno;
    private Long bloqueadaInterna;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Long getAgendaPrimeiraVez() {
        return agendaPrimeiraVez;
    }

    public void setAgendaPrimeiraVez(Long agendaPrimeiraVez) {
        this.agendaPrimeiraVez = agendaPrimeiraVez;
    }

    public Long getAgendaRegulada() {
        return agendaRegulada;
    }

    public void setAgendaRegulada(Long agendaRegulada) {
        this.agendaRegulada = agendaRegulada;
    }

    public Long getAgendaRetorno() {
        return agendaRetorno;
    }

    public void setAgendaRetorno(Long agendaRetorno) {
        this.agendaRetorno = agendaRetorno;
    }

    public Long getAgendaInterna() {
        return agendaInterna;
    }

    public void setAgendaInterna(Long agendaInterna) {
        this.agendaInterna = agendaInterna;
    }

    public Long getAtivaPrimeiraVez() {
        return ativaPrimeiraVez;
    }

    public void setAtivaPrimeiraVez(Long ativaPrimeiraVez) {
        this.ativaPrimeiraVez = ativaPrimeiraVez;
    }

    public Long getAtivaRegulada() {
        return ativaRegulada;
    }

    public void setAtivaRegulada(Long ativaRegulada) {
        this.ativaRegulada = ativaRegulada;
    }

    public Long getAtivaRetorno() {
        return ativaRetorno;
    }

    public void setAtivaRetorno(Long ativaRetorno) {
        this.ativaRetorno = ativaRetorno;
    }

    public Long getAtivaInterna() {
        return ativaInterna;
    }

    public void setAtivaInterna(Long ativaInterna) {
        this.ativaInterna = ativaInterna;
    }

    public Long getBloqueadaPrimeiraVez() {
        return bloqueadaPrimeiraVez;
    }

    public void setBloqueadaPrimeiraVez(Long bloqueadaPrimeiraVez) {
        this.bloqueadaPrimeiraVez = bloqueadaPrimeiraVez;
    }

    public Long getBloqueadaRegulada() {
        return bloqueadaRegulada;
    }

    public void setBloqueadaRegulada(Long bloqueadaRegulada) {
        this.bloqueadaRegulada = bloqueadaRegulada;
    }

    public Long getBloqueadaRetorno() {
        return bloqueadaRetorno;
    }

    public void setBloqueadaRetorno(Long bloqueadaRetorno) {
        this.bloqueadaRetorno = bloqueadaRetorno;
    }

    public Long getBloqueadaInterna() {
        return bloqueadaInterna;
    }

    public void setBloqueadaInterna(Long bloqueadaInterna) {
        this.bloqueadaInterna = bloqueadaInterna;
    }

    public boolean descricaoProcedimentoVazia() {
        return StringUtils.isBlank(getExameProcedimento().getDescricaoProcedimento());
    }
 }
