package br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoOrdemCompraDTOParam implements Serializable{

    private Pessoa fornecedor;
    private Produto produto;
    private DatePeriod periodo;
    private BaseCalculo baseCalculo;
    private String nrPregao;
    private FormaApresentacao formaApresentacao;
    private RelatorioRelacaoOrdemCompraDTOParam.Ordenacao ordenacao;
    private RelatorioRelacaoOrdemCompraDTOParam.TipoOrdenacao tipoOrdenacao;

    private boolean situacaoTodas;
    private boolean situacaoPendente;
    private boolean situacaoRecebida;
    private boolean situacaoCancelada;



    public String getNrPregao() {
        return nrPregao;
    }

    public void setNrPregao(String nrPregao) {
        this.nrPregao = nrPregao;
    }

    public boolean isSituacaoTodas() {
        return situacaoTodas;
    }

    public void setSituacaoTodas(boolean situacaoTodas) {
        this.situacaoTodas = situacaoTodas;
    }

    public boolean isSituacaoPendente() {
        return situacaoPendente;
    }

    public void setSituacaoPendente(boolean situacaoPendente) {
        this.situacaoPendente = situacaoPendente;
    }

    public boolean isSituacaoRecebida() {
        return situacaoRecebida;
    }

    public void setSituacaoRecebida(boolean situacaoRecebida) {
        this.situacaoRecebida = situacaoRecebida;
    }

    public boolean isSituacaoCancelada() {
        return situacaoCancelada;
    }

    public void setSituacaoCancelada(boolean situacaoCancelada) {
        this.situacaoCancelada = situacaoCancelada;
    }

    public List<Long> getInSituacao(){
        List<Long> situacoes = new ArrayList<Long>();
        if (situacaoPendente) {
            situacoes.add(OrdemCompraItem.Status.PENDENTE.value());
        }
        if (situacaoRecebida) {
            situacoes.add(OrdemCompraItem.Status.RECEBIDA.value());
        }
        if (situacaoCancelada) {
            situacoes.add(OrdemCompraItem.Status.CANCELADA.value());
        }

        return situacoes;
    }

    @DescricaoParametro("rotulo_base_calculo_total")
    public BaseCalculo getBaseCalculo() {
        return baseCalculo;
    }

    public void setBaseCalculo(BaseCalculo baseCalculo) {
        this.baseCalculo = baseCalculo;
    }

    @DescricaoParametro("rotulo_produto")
    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_fornecedor")
    public Pessoa getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(Pessoa fornecedor) {
        this.fornecedor = fornecedor;
    }



    public static enum FormaApresentacao implements IEnum<RelatorioRelacaoOrdemCompraDTOParam.FormaApresentacao>{
        GERAL(0L, Bundle.getStringApplication("rotulo_geral")),
        FORNECEDOR(1L, Bundle.getStringApplication("rotulo_fornecedor")),
        PRODUTO(2L, Bundle.getStringApplication("rotulo_produto"));

        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum Ordenacao implements IEnum<RelatorioRelacaoOrdemCompraDTOParam.Ordenacao>{

        ORDEM_COMPRA(0L, Bundle.getStringApplication("rotulo_ordem_compra")),
        DATA(1L, Bundle.getStringApplication("rotulo_data")),
        PRODUTO(2L, Bundle.getStringApplication("rotulo_produto"));

        private Long value;
        private String descricao;

        private Ordenacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoOrdenacao implements IEnum<RelatorioRelacaoOrdemCompraDTOParam.TipoOrdenacao>{

        CRESCENTE("asc", Bundle.getStringApplication("rotulo_crescente")),
        DECRESCENTE("desc", Bundle.getStringApplication("rotulo_decrescente"));

        private String value;
        private String descricao;

        private TipoOrdenacao(String value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum BaseCalculo implements IEnum<RelatorioRelacaoOrdemCompraDTOParam.BaseCalculo>{

        QUANTIDADE(1L, Bundle.getStringApplication("rotulo_quantidade")),
        SALDO(0L, Bundle.getStringApplication("rotulo_saldo"));

        private Long value;
        private String descricao;

        private BaseCalculo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
}
