package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;

import java.io.Serializable;

public class RelatorioGiroEstoqueDTOParam implements Serializable {
    private Empresa empresa;
    private CentroCusto centroCusto;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private Ordenacao ordenacao;
    private TipoOrdenacao tipoOrdenacao;
    private Boolean estoqueAtualAbaixoEstoqueMinimo;
    private TipoRelatorio tipoArquivo;

    @DescricaoParametro("rotulo_estoque_atual_abaixo_estoque_minimo")
    public Boolean getEstoqueAtualAbaixoEstoqueMinimo() {
        return estoqueAtualAbaixoEstoqueMinimo;
    }

    public void setEstoqueAtualAbaixoEstoqueMinimo(Boolean estoqueAtualAbaixoEstoqueMinimo) {
        this.estoqueAtualAbaixoEstoqueMinimo = estoqueAtualAbaixoEstoqueMinimo;
    }
    
    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }
    
    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(RelatorioGiroEstoqueDTOParam.Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_empresa")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_centro_custo")
    public CentroCusto getCentroCusto() {
        return centroCusto;
    }

    public void setCentroCusto(CentroCusto centroCusto) {
        this.centroCusto = centroCusto;
    }

    @DescricaoParametro("rotulo_grupo_produto")
    public GrupoProduto getGrupoProduto() {
        return grupoProduto;
    }

    public void setGrupoProduto(GrupoProduto grupoProduto) {
        this.grupoProduto = grupoProduto;
    }

    @DescricaoParametro("rotulo_sub_grupo")
    public SubGrupo getSubGrupo() {
        return subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }
    
    public static enum FormaApresentacao implements IEnum<FormaApresentacao>{
        GERAL(0L, Bundle.getStringApplication("rotulo_geral")),
        GRUPO_PRODUTO(1L, Bundle.getStringApplication("rotulo_grupo_produto")),
        CENTRO_CUSTO(2L,Bundle.getStringApplication("rotulo_centro_custo"));
        
        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
    
    public enum Ordenacao {
        DESCRICAO_PRODUTO(Bundle.getStringApplication("rotulo_descricao_produto")),
        QTD_SAIDA(Bundle.getStringApplication("rotulo_quantidade_de_saida"));

        private String name;

        private Ordenacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }
    
    public enum TipoOrdenacao {
        CRESCENTE(Bundle.getStringApplication("rotulo_crescente"),"asc"),
        DECRESCENTE(Bundle.getStringApplication("rotulo_decrescente"),"desc");

        private String name;
        private String command;

        private TipoOrdenacao(String name, String command) {
            this.name = name;
            this.command = command;
        }

        @Override
        public String toString() {
            return name;
        }

        public String getCommand() {
            return command;
        }

    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }
}
