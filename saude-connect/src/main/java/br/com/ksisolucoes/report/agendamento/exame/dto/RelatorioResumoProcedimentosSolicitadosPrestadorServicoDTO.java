package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.commons.lang.StringUtils;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;

public class RelatorioResumoProcedimentosSolicitadosPrestadorServicoDTO implements Serializable {

    private Empresa empresaSolicitante;
    private Empresa localExame;
    private Profissional profissional;
    private TipoExame tipoExame;
    private TipoProcedimento tipoProcedimento;
    private UsuarioCadsus usuarioCadsus;
    private ExamePrestador examePrestador;

    private ExameProcedimento exameProcedimento;
    private ExameRequisicao exameRequisicao;
    private Double quantidade;
    private Double valorSUS;
    private Double valorComplemento;
    private Double valorRecursoProprio;
    private Procedimento procedimento;
    private String procedimentoReferenciaFormatado;
    private Date dataAtendimento;
    private String cnsPaciente;
    private Long numeroSolicitacao;
    private Double valorTotal;
    private Date dataAgendamento;

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorSUS() {
        return valorSUS;
    }

    public void setValorSUS(Double valorSUS) {
        this.valorSUS = valorSUS;
    }

    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public Empresa getLocalExame() {
        return localExame;
    }

    public void setLocalExame(Empresa localExame) {
        this.localExame = localExame;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Double getValorRecursoProprio() {
        return getValorComplemento() > 0.0 ? 0.0 : valorRecursoProprio;
    }

    public void setValorRecursoProprio(Double valorRecursoProprio) {
        this.valorRecursoProprio = valorRecursoProprio;
    }

    public ExamePrestador getExamePrestador() {
        return examePrestador;
    }

    public void setExamePrestador(ExamePrestador examePrestador) {
        this.examePrestador = examePrestador;
    }

    public Double getValorComplemento() {
    	return valorComplemento;
    }

    public void setValorComplemento(Double valorComplemento) {
        this.valorComplemento = valorComplemento;
    }

    public Procedimento getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public String getProcedimentoReferenciaFormatado() {
        return procedimentoReferenciaFormatado;
    }

    public void setProcedimentoReferenciaFormatado(String referencia) {
        try {
            if (referencia != null && !referencia.isEmpty() && StringUtils.isNumeric(referencia.trim())) {
                referencia = String.format("%010d", new Long(referencia));
                MaskFormatter m = new MaskFormatter("##.##.##.###-#");
                m.setValueContainsLiteralCharacters(false);

                referencia = m.valueToString(referencia);
            }
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        procedimentoReferenciaFormatado = referencia;
    }

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public Long getNumeroSolicitacao() {
        return numeroSolicitacao;
    }

    public void setNumeroSolicitacao(Long numeroSolicitacao) {
        this.numeroSolicitacao = numeroSolicitacao;
    }

    public Double getValorTotal() {
        return getValorSUS() + getValorComplemento() + getValorRecursoProprio();
    }

    public Date getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(Date dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }
}
