package br.com.ksisolucoes.report.vigilancia.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.DenunciaOcorrencia;
import java.io.Serializable;
import java.util.Date;

public class RelatorioImpressaoTermoDenunciaDTO implements Serializable {

    private Denuncia denuncia;
    private DenunciaOcorrencia denunciaOcorrencia;
    private Profissional profissional;
    private Estabelecimento estabelecimento;
    private String nomeProfissional;
    private String descricaoOcorrencia;
    private Date dataOcorrencia;

    public String getDescricaoOcorrencia() {
        return descricaoOcorrencia;
    }

    public void setDescricaoOcorrencia(String descricaoOcorrencia) {
        this.descricaoOcorrencia = descricaoOcorrencia;
    }

    public Date getDataOcorrencia() {
        return dataOcorrencia;
    }

    public void setDataOcorrencia(Date dataOcorrencia) {
        this.dataOcorrencia = dataOcorrencia;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public DenunciaOcorrencia getDenunciaOcorrencia() {
        return denunciaOcorrencia;
    }

    public void setDenunciaOcorrencia(DenunciaOcorrencia denunciaOcorrencia) {
        this.denunciaOcorrencia = denunciaOcorrencia;
    }

    public RelatorioImpressaoTermoDenunciaDTO() {
    }

    public Denuncia getDenuncia() {
        return denuncia;
    }

    public void setDenuncia(Denuncia denuncia) {
        this.denuncia = denuncia;
    }
}
