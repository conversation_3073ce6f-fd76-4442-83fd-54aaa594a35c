package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.util.Valor;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoDistribuicaoEncaminhamentoDTO implements Serializable {

    private String descricaoFormaApresentacao;
    private Long quantidade;
    private Long total;

    public String getDescricaoFormaApresentacao() {
        return descricaoFormaApresentacao;
    }

    public void setDescricaoFormaApresentacao(String descricaoFormaApresentacao) {
        this.descricaoFormaApresentacao = descricaoFormaApresentacao;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public String getLabel(){
        String label = "Quantidade: "+getQuantidade()+" - ";

        Double percentual = getQuantidade().doubleValue()*100D/getTotal().doubleValue();

        label += Valor.adicionarFormatacaoMonetaria(percentual)+"%";

        return label;
    }

}
