package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoCadastro;
import java.io.Serializable;
import java.util.List;

public class RelatorioProcedimentoServicoParam implements Serializable {

    private List<Procedimento> procedimentos;
    private List<ProcedimentoServicoCadastro> procedimentoServicoCadastros;

    public List<ProcedimentoServicoCadastro> getProcedimentoServicoCadastros() {
        return procedimentoServicoCadastros;
    }

    public void setProcedimentoServicoCadastros(List<ProcedimentoServicoCadastro> procedimentoServicoCadastros) {
        this.procedimentoServicoCadastros = procedimentoServicoCadastros;
    }

    public List<Procedimento> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<Procedimento> procedimentos) {
        this.procedimentos = procedimentos;
    }
}
