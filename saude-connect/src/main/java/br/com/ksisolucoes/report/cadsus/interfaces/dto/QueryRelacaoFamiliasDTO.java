package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryRelacaoFamiliasDTO implements Serializable{
    
    private UsuarioCadsus usuarioCadsus;
    private EquipeMicroArea equipeMicroArea;
    private Profissional profissional;
    private FaixaEtariaItem faixaEtariaItem;
    private Doenca doenca;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus; 
    private String agrupamentoEndereco ="";
    private EnderecoDomicilioEsus enderecoDomicilioEsus;
    private Equipe equipe;

    public Equipe getEquipe() {
        return equipe;
    }

    public void setEquipe(Equipe equipe) {
        this.equipe = equipe;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public FaixaEtariaItem getFaixaEtariaItem() {
        return faixaEtariaItem;
    }

    public void setFaixaEtariaItem(FaixaEtariaItem faixaEtariaItem) {
        this.faixaEtariaItem = faixaEtariaItem;
    }

    public Doenca getDoenca() {
        return doenca;
    }

    public void setDoenca(Doenca doenca) {
        this.doenca = doenca;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
    
    public String getAgrupamentoEndereco(){
        if (getEnderecoUsuarioCadsus() != null) {
            agrupamentoEndereco = getEnderecoUsuarioCadsus().getTipoLogradouro().getDescricao()+getEnderecoUsuarioCadsus().getLogradouro();
            agrupamentoEndereco = StringUtils.upperCase(agrupamentoEndereco.replaceAll(" ", ""));
        }
        return agrupamentoEndereco;
    }

    public EnderecoDomicilioEsus getEnderecoDomicilioEsus() {
        return enderecoDomicilioEsus;
    }

    public void setEnderecoDomicilioEsus(EnderecoDomicilioEsus enderecoDomicilioEsus) {
        this.enderecoDomicilioEsus = enderecoDomicilioEsus;
    }
}
