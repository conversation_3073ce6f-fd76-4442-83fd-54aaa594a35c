package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.prontuario.basico.*;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoExameMultipatogenoISTDTO implements Serializable {

    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private UsuarioCadsusCns usuarioCadsusCns;
    private UsuarioCadsusDado usuarioCadsusDado;
    private ExameRequisicao exameRequisicao;
    private UsuarioCadsus usuarioCadsus;
    private RequisicaoMultipatogenosIST requisicaoMultipatogenosIST;
    private Cid cid;
    private Profissional profissional;
    private Exame exame;
    private Empresa empresaSolicitante;
    private Long idadeGestacional;
    private Long escolaridade;
    private Long gestante;
    private Long identidadeGenero;
    private Long orientacaoSexual;
    private Date dataSolicitacaoFormatada;

    public Long getEscolaridade() {
        return escolaridade;
    }

    public void setEscolaridade(Long escolaridade) {
        this.escolaridade = escolaridade;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public RequisicaoMultipatogenosIST getRequisicaoMultipatogenosIST() {
        return requisicaoMultipatogenosIST;
    }

    public void setRequisicaoMultipatogenosIST(RequisicaoMultipatogenosIST requisicaoMultipatogenosIST) {
        this.requisicaoMultipatogenosIST = requisicaoMultipatogenosIST;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        return usuarioCadsusDado;
    }

    public void setUsuarioCadsusDado(UsuarioCadsusDado usuarioCadsusDado) {
        this.usuarioCadsusDado = usuarioCadsusDado;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Exame getExame() {
        return exame;
    }

    public void setExame(Exame exame) {
        this.exame = exame;
    }

    public Long getIdadeGestacional() {
        return idadeGestacional;
    }

    public void setIdadeGestacional(Long idadeGestacional) {
        this.idadeGestacional = idadeGestacional;
    }

    public Long getGestante() {
        return gestante;
    }

    public void setGestante(Long gestante) {
        this.gestante = gestante;
    }

    public Long getIdentidadeGenero() {
        return identidadeGenero;
    }

    public void setIdentidadeGenero(Long identidadeGenero) {
        this.identidadeGenero = identidadeGenero;
    }

    public Long getOrientacaoSexual() {
        return orientacaoSexual;
    }

    public void setOrientacaoSexual(Long orientacaoSexual) {
        this.orientacaoSexual = orientacaoSexual;
    }

    public String getDataSolicitacaoFormatada() {
        if (requisicaoMultipatogenosIST != null && requisicaoMultipatogenosIST.getDataCadastro() != null) {
            return new SimpleDateFormat("dd/MM/yyyy").format(requisicaoMultipatogenosIST.getDataCadastro());
        }
        return "";
    }

    public void setDataSolicitacaoFormatada(String dataStr) {
        if (dataStr != null && !dataStr.isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                this.dataSolicitacaoFormatada = sdf.parse(dataStr);
                if (requisicaoMultipatogenosIST != null) {
                    requisicaoMultipatogenosIST.setDataCadastro(this.dataSolicitacaoFormatada);
                }
            } catch (ParseException e) {
                // Log error or handle appropriately
                throw new IllegalArgumentException("Data inválida. Use o formato dd/MM/yyyy");
            }
        }
    }
}