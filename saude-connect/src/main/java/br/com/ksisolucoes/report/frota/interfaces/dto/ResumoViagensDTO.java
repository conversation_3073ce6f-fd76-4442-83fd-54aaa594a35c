package br.com.ksisolucoes.report.frota.interfaces.dto;

import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ResumoViagensDTO implements Serializable {

    private RoteiroViagem roteiroViagem;
    private Long nrViagens;
    private Long nrPassageiros;

    public RoteiroViagem getRoteiroViagem() {
        return roteiroViagem;
    }

    public void setRoteiroViagem(RoteiroViagem roteiroViagem) {
        this.roteiroViagem = roteiroViagem;
    }

    public Long getNrViagens() {
        return nrViagens;
    }

    public void setNrViagens(Long nrViagens) {
        this.nrViagens = nrViagens;
    }

    public Long getNrPassageiros() {
        return nrPassageiros;
    }

    public void setNrPassageiros(Long nrPassageiros) {
        this.nrPassageiros = nrPassageiros;
    }
}
