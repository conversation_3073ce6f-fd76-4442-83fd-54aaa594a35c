/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacienteDispensacaoAtrasoDTO implements Serializable {

    private UsuarioCadsus paciente;
    private Empresa unidadeOrigem;
    private DispensacaoMedicamentoItem dispensacaoMedicamentoItem;

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    public DispensacaoMedicamentoItem getDispensacaoMedicamentoItem() {
        return dispensacaoMedicamentoItem;
    }

    public void setDispensacaoMedicamentoItem(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        this.dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
    }
}
