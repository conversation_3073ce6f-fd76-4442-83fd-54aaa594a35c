package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.entradas.estoque.ControleInventario;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.io.Serializable;
import java.util.Date;

public class RelatorioControleInventarioDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private String codigoEmpresa;
    private String nomeEmpresa;
    private Date dataLancamento;
    private Date dataProcessamento;
    private Double quantidade;
    private Long codigoGrupoProduto;
    private String descricaoGrupoProduto;
    private Long codigoSubGrupo;
    private String descricaoSubGrupo;
    private String descricaoProduto;
    private String nomeUsuario;
    private String nomeUsuarioProcessamento;
    private Long codigoUsuarioProcessamento;
    private String grupoEstoque;
    private Double estoqueFisico;
    private Long codigoDeposito;
    private String descricaoDeposito;
    private LocalizacaoEstrutura localizacaoEstrutura;
    private Double estoqueDivergente;
    private Double estoque;

    public RelatorioControleInventarioDTO(String codigoEmpresa,
            String nomeEmpresa,
            Long status,
            Double quantidade,
            Double estoqueDivergente,
            Double estoque,
            String codigoProduto,
            String descricaoProduto,
            Date dataLancamento,
            Long codigoUsuario,
            String nomeUsuario,
            Long codigoSubGrupo,
            String descricaoSubGrupo,
            Long codigoGrupoProduto,
            String descricaoGrupoProduto,
            Long codigoLocalizacao,
            String descricaoLocalizacao) {

        this.setCodigoEmpresa(codigoEmpresa);
        this.setNomeEmpresa(nomeEmpresa);
        this.setStatus(status);
        this.setQuantidade(quantidade);
        this.setCodigoProduto(codigoProduto);
        this.setDescricaoProduto(descricaoProduto);
        this.setDataLancamento(dataLancamento);
        this.setCodigoUsuario(codigoUsuario);
        this.setNomeUsuario(nomeUsuario);
        this.setCodigoSubGrupo(codigoSubGrupo);
        this.setDescricaoSubGrupo(descricaoSubGrupo);
        this.setCodigoGrupoProduto(codigoGrupoProduto);
        this.setDescricaoGrupoProduto(descricaoGrupoProduto);
        this.setCodigoLocalizacao(codigoLocalizacao);
        this.setDescricaoLocalizacao(descricaoLocalizacao);
        this.setEstoqueDivergente(estoqueDivergente);
        this.setEstoque(estoque);
    }

    public RelatorioControleInventarioDTO(
            String codigoProduto,
            String descricaoProduto,
            Long codigoSubGrupo,
            String descricaoSubGrupo,
            Long codigoGrupoProduto,
            String descricaoGrupoProduto,
            Long codigoLocalizacao,
            String descricaoLocalizacao) {

        this.setCodigoProduto(codigoProduto);
        this.setDescricaoProduto(descricaoProduto);
        this.setCodigoSubGrupo(codigoSubGrupo);
        this.setDescricaoSubGrupo(descricaoSubGrupo);
        this.setCodigoGrupoProduto(codigoGrupoProduto);
        this.setDescricaoGrupoProduto(descricaoGrupoProduto);
        this.setCodigoLocalizacao(codigoLocalizacao);
        this.setDescricaoLocalizacao(descricaoLocalizacao);
    }

    public RelatorioControleInventarioDTO() {
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public Long getCodigoGrupoProduto() {
        return codigoGrupoProduto;
    }

    public String getDescricaoGrupoProduto() {
        return descricaoGrupoProduto;
    }

    public Long getCodigoSubGrupo() {
        return codigoSubGrupo;
    }

    public String getDescricaoSubGrupo() {
        return descricaoSubGrupo;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public String getDescricaoProdutoFormatado() {
        return Util.getDescricaoFormatado(this.codigoProduto, this.descricaoProduto);
    }

    public String getDescricaoGrupo() {
        return this.descricaoGrupoProduto;
    }

    public String getDescricaoEmpresa() {
        return this.nomeEmpresa;
    }

    public String getDescricaoUsuario() {
        return this.nomeUsuario;
    }

    public String getStatusFormatado() {
        return ControleInventario.getStatusFormatado(this.status);
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }
    /**
     * Holds value of property codigoProduto.
     */
    private String codigoProduto;

    /**
     * Getter for property codigoProuto.
     *
     * @return Value of property codigoProuto.
     */
    public String getCodigoProduto() {
        return this.codigoProduto;
    }

    /**
     * Setter for property codigoProuto.
     *
     * @param codigoProuto New value of property codigoProuto.
     */
    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }
    /**
     * Holds value of property codigoUsuario.
     */
    private Long codigoUsuario;

    /**
     * Getter for property codigoUsuario.
     *
     * @return Value of property codigoUsuario.
     */
    public Long getCodigoUsuario() {
        return this.codigoUsuario;
    }

    /**
     * Setter for property codigoUsuario.
     *
     * @param codigoUsuario New value of property codigoUsuario.
     */
    public void setCodigoUsuario(Long codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }
    /**
     * Holds value of property codigoLocalizacao.
     */
    private Long codigoLocalizacao;

    /**
     * Getter for property codigoLocalizacao.
     *
     * @return Value of property codigoLocalizacao.
     */
    public Long getCodigoLocalizacao() {
        return this.codigoLocalizacao;
    }

    /**
     * Setter for property codigoLocalizacao.
     *
     * @param codigoLocalizacao New value of property codigoLocalizacao.
     */
    public void setCodigoLocalizacao(Long codigoLocalizacao) {
        this.codigoLocalizacao = codigoLocalizacao;
    }
    /**
     * Holds value of property descricaoLocalizacao.
     */
    private String descricaoLocalizacao;

    /**
     * Getter for property descricaoLocalizacao.
     *
     * @return Value of property descricaoLocalizacao.
     */
    public String getDescricaoLocalizacao() {
        return this.descricaoLocalizacao;
    }

    /**
     * Setter for property descricaoLocalizacao.
     *
     * @param descricaoLocalizacao New value of property descricaoLocalizacao.
     */
    public void setDescricaoLocalizacao(String descricaoLocalizacao) {
        this.descricaoLocalizacao = descricaoLocalizacao;
    }
    /**
     * Holds value of property status.
     */
    private Long status;

    /**
     * Getter for property status.
     *
     * @return Value of property status.
     */
    public Long getStatus() {
        return this.status;
    }

    /**
     * Setter for property status.
     *
     * @param status New value of property status.
     */
    public void setStatus(Long status) {
        this.status = status;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void setCodigoGrupoProduto(Long codigoGrupoProduto) {
        this.codigoGrupoProduto = codigoGrupoProduto;
    }

    public void setCodigoSubGrupo(Long codigoSubGrupo) {
        this.codigoSubGrupo = codigoSubGrupo;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public void setDescricaoGrupoProduto(String descricaoGrupoProduto) {
        this.descricaoGrupoProduto = descricaoGrupoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public void setDescricaoSubGrupo(String descricaoSubGrupo) {
        this.descricaoSubGrupo = descricaoSubGrupo;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public String getGrupoEstoque() {
        return grupoEstoque;
    }

    public void setGrupoEstoque(String grupoEstoque) {
        this.grupoEstoque = grupoEstoque;
    }

    public Long getCodigoUsuarioProcessamento() {
        return codigoUsuarioProcessamento;
    }

    public void setCodigoUsuarioProcessamento(Long codigoUsuarioProcessamento) {
        this.codigoUsuarioProcessamento = codigoUsuarioProcessamento;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public String getNomeUsuarioProcessamento() {
        return nomeUsuarioProcessamento;
    }

    public void setNomeUsuarioProcessamento(String nomeUsuarioProcessamento) {
        this.nomeUsuarioProcessamento = nomeUsuarioProcessamento;
    }

    public String getDescricaoUsuarioProcessamento() {
        return getNomeUsuarioProcessamento();
    }

    public Double getEstoqueFisico() {
        return estoqueFisico;
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public Long getCodigoDeposito() {
        return codigoDeposito;
    }

    public void setCodigoDeposito(Long codigoDeposito) {
        this.codigoDeposito = codigoDeposito;
    }

    public String getDescricaoDeposito() {
        return descricaoDeposito;
    }

    public void setDescricaoDeposito(String descricaoDeposito) {
        this.descricaoDeposito = descricaoDeposito;
    }

    public LocalizacaoEstrutura getLocalizacaoEstrutura() {
        return localizacaoEstrutura;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura) {
        this.localizacaoEstrutura = localizacaoEstrutura;
    }

    public Double getEstoqueDivergente() {
        return estoqueDivergente;
    }

    public void setEstoqueDivergente(Double estoqueDivergente) {
        this.estoqueDivergente = estoqueDivergente;
    }

    public Double getEstoque() {
        return estoque;
    }

    public void setEstoque(Double estoque) {
        this.estoque = estoque;
    }
}
