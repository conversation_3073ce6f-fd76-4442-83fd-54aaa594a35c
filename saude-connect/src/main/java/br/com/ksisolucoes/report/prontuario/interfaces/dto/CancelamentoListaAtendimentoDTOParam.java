package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.io.Serializable;
import java.util.List;

/**
 * Created by mauricley on 15/05/18.
 */
public class CancelamentoListaAtendimentoDTOParam implements Serializable {
    private Long diasSemMovimentacao;
    private DatePeriod periodo;
    private List<Empresa> empresas;
    private TipoAtendimento tipoAtendimento;
    private UsuarioCadsus usuarioCadsus;
    private Long numeroAtendimento;
    private String motivoCancelamento;

    @DescricaoParametro("rotulo_dias_sem_movimentacao")
    public Long getDiasSemMovimentacao() {
        return diasSemMovimentacao;
    }

    public void setDiasSemMovimentacao(Long diasSemMovimentacao) {
        this.diasSemMovimentacao = diasSemMovimentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_unidade")
    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_tipo_atendimento")
    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @DescricaoParametro("rotulo_numero_atendimento_abv")
    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public String getMotivoCancelamento() {
        return motivoCancelamento;
    }

    public void setMotivoCancelamento(String motivoCancelamento) {
        this.motivoCancelamento = motivoCancelamento;
    }
}
