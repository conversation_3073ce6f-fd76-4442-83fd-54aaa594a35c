/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoAtendimentosDTO implements Serializable{

    private Long numeroAtendimento;
    private Empresa empresa;
    private Date dataAtendimento;
    private UsuarioCadsus usuarioCadsus;
    private Procedimento procedimento;
    private TipoAtendimento tipoAtendimento;
    private Profissional profissional;
    private Profissional profissionalItem;
    private TabelaCbo tabelaCbo;
    private TabelaCboGrupo tabelaCboGrupo;
    private Long item;
    private Procedimento procedimentoItem;
    private Date dataHora;
    private Convenio convenio;
    private Cidade cidade;
    private Bairro bairro;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Convenio getConvenio(){
        return convenio;
    }
    
    public void setConvenio(Convenio convenio){
        this.convenio = convenio;
    }

    public Profissional getProfissionalItem() {
        return profissionalItem;
    }

    public void setProfissionalItem(Profissional profissionalItem) {
        this.profissionalItem = profissionalItem;
    }

    public Date getDataHora() {
        return dataHora;
    }

    public void setDataHora(Date dataHora) {
        this.dataHora = dataHora;
    }

    public Long getItem() {
        return item;
    }

    public void setItem(Long item) {
        this.item = item;
    }

    public Procedimento getProcedimentoItem() {
        return procedimentoItem;
    }

    public void setProcedimentoItem(Procedimento procedimentoItem) {
        this.procedimentoItem = procedimentoItem;
    }

    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Procedimento getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public TabelaCboGrupo getTabelaCboGrupo() {
        return tabelaCboGrupo;
    }

    public void setTabelaCboGrupo(TabelaCboGrupo tabelaCboGrupo) {
        this.tabelaCboGrupo = tabelaCboGrupo;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }
    public Bairro getBairro() {
        return bairro;
    }

    public void setBairro(Bairro bairro) {
        this.bairro = bairro;
    }
}
