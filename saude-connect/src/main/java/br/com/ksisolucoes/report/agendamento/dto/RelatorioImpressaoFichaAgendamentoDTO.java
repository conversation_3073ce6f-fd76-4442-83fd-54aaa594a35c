package br.com.ksisolucoes.report.agendamento.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoFichaAgendamentoDTO implements Serializable {
    
    private Empresa empresa;
    private AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
    private AgendaGradeAtendimento agendaGradeAtendimento;
    private TipoProcedimento tipoProcedimento;
    private Profissional profissional;
    private UsuarioCadsus usuarioCadsus;
    private String numeroProntuario;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public AgendaGradeAtendimento getAgendaGradeAtendimento() {
        return agendaGradeAtendimento;
    }

    public void setAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaGradeAtendimento = agendaGradeAtendimento;
    }

    public AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorario() {
        return agendaGradeAtendimentoHorario;
    }

    public void setAgendaGradeAtendimentoHorario(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getNumeroProntuario() {
        return numeroProntuario;
    }

    public void setNumeroProntuario(String numeroProntuario) {
        this.numeroProntuario = numeroProntuario;
    }
    
    public String getDataAtendimentoFormatadoDiaHora() {
        Date data = getAgendaGradeAtendimento().getAgendaGrade() != null && getAgendaGradeAtendimento().getAgendaGrade().getData() != null
                ? getAgendaGradeAtendimento().getAgendaGrade().getData() : getAgendaGradeAtendimentoHorario().getDataAgendamento();
        Calendar c = Calendar.getInstance();
        if(data == null){
            return "";
        }
        c.setTime(data);

        return Data.getDescricaoDiaSemanaAbv(c.get(Calendar.DAY_OF_WEEK)) + " - " + Data.formatar(data);
    }    
    
}
