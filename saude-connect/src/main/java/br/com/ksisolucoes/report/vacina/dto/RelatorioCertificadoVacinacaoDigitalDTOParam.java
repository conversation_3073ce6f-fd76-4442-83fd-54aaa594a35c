package br.com.ksisolucoes.report.vacina.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.TipoVacina;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioCertificadoVacinacaoDigitalDTOParam implements Serializable {

    private TipoVacina tipoVacina;
    private UsuarioCadsus usuarioCadsus;
    private String urlCertificado;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public TipoVacina getTipoVacina() {
        return tipoVacina;
    }

    public void setTipoVacina(TipoVacina tipoVacina) {
        this.tipoVacina = tipoVacina;
    }

    public String getUrlCertificado() {
        return urlCertificado;
    }

    public void setUrlCertificado(String urlCertificado) {
        this.urlCertificado = urlCertificado;
    }
}
