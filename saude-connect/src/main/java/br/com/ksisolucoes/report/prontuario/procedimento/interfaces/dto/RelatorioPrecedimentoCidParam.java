package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCid;
import java.io.Serializable;
import java.util.List;

public class RelatorioPrecedimentoCidParam implements Serializable {

    private List<Procedimento> procedimentos;
    private List<ProcedimentoCid> procedimentoCid;

    @DescricaoParametro(value = "rotulo_procedimento_cid")
    public List<ProcedimentoCid> getProcedimentoCid() {
        return procedimentoCid;
    }

    public void setProcedimentoCid(List<ProcedimentoCid> procedimentoCid) {
        this.procedimentoCid = procedimentoCid;
    }

    @DescricaoParametro(value = "rotulo_procedimento")
    public List<Procedimento> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<Procedimento> procedimentos) {
        this.procedimentos = procedimentos;
    }
}
