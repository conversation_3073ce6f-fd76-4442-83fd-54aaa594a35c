/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoDTO implements Serializable{

    private Double valorProcedimento;
    private Empresa empresa;
    private Empresa empresaSolicitante;
    private Empresa empresaAtendimento;
    private Profissional profissional;
    private Procedimento procedimento;
    private UsuarioCadsus usuarioCadsus;
    private TabelaCbo tabelaCbo;
    private Double quantidade;
    private Long quantidadeCount;
    private Convenio convenio;
    private Timestamp dataAtendimento;
    private ProcedimentoSubGrupo procedimentoSubGrupo;
    private Cidade cidade;
    private EquipeProfissional equipeProfissional;
    private Double valorDiferenciado;
    private Double valorTotalProcedimento;
    private Double valorTotalDiferenciado;
    private Double valorTotalItens;

    public Timestamp getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Timestamp dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public Convenio getConvenio(){
        if(this.convenio == null || this.convenio.getCodigo() == null){
            this.convenio = new Convenio();
            this.convenio.setCodigo(0L);
            this.convenio.setDescricao("Desconhecido");
        }
        return convenio;
    }

    public Double getValorProcedimento() {
        return valorProcedimento;
    }

    public void setValorProcedimento(Double valorProcedimento) {
        this.valorProcedimento = valorProcedimento;
    }

    public void setConvenio(Convenio convenio){
        this.convenio = convenio;
    }

    public Empresa getEmpresa() {
        if (this.empresa == null) {
            this.empresa = new Empresa();
        }
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        if (this.profissional == null || this.profissional.getCodigo() == null) {
            this.profissional = new Profissional();
            this.profissional.setCodigo(0L);
            this.profissional.setNome("Sem Profissional");
        }
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Procedimento getProcedimento() {
        if (this.procedimento == null || this.procedimento.getCodigo() == null) {
            this.procedimento = new Procedimento();
            this.procedimento.setCodigo(0L);
            this.procedimento.setDescricao("Procedimento Desconhecido");
        }
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public Double getQuantidade() {
        if(getQuantidadeCount() != null){
            return Coalesce.asLong(getQuantidadeCount()).doubleValue();
        }
        return Coalesce.asDouble(quantidade);
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        if (this.usuarioCadsus == null || this.usuarioCadsus.getCodigo() == null) {
            this.usuarioCadsus = new UsuarioCadsus();
            this.usuarioCadsus.setCodigo(0L);
            this.usuarioCadsus.setNome("Sem Paciente");
        }
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public TabelaCbo getTabelaCbo() {
        if (this.tabelaCbo == null) {
            this.tabelaCbo = new TabelaCbo();
            this.tabelaCbo.setCbo("");
            this.tabelaCbo.setDescricao("Sem Cadastro");
        }
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public ProcedimentoSubGrupo getProcedimentoSubGrupo() {
        if(procedimentoSubGrupo == null) {
            procedimentoSubGrupo = new ProcedimentoSubGrupo();
            procedimentoSubGrupo.setId(new ProcedimentoSubGrupoPK());
            procedimentoSubGrupo.setRoGrupo(new ProcedimentoGrupo());
        }
            
        return procedimentoSubGrupo;
    }

    public void setProcedimentoSubGrupo(ProcedimentoSubGrupo procedimentoSubGrupo) {
        this.procedimentoSubGrupo = procedimentoSubGrupo;
    }

    public Long getQuantidadeCount() {
        return quantidadeCount;
    }

    public void setQuantidadeCount(Long quantidadeCount) {
        this.quantidadeCount = quantidadeCount;
    }

    public Cidade getCidade() {
        if(this.cidade == null){
            this.cidade = new Cidade();
            this.cidade.setCodigo(0L);
            this.cidade.setDescricao("Sem Município");
        }
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        if (cidade.getCodigo()==null){
            cidade.setCodigo(0L);
            cidade.setDescricao("Sem Município");
        }

        this.cidade = cidade;
    }

    public EquipeProfissional getEquipeProfissional() {
        if (equipeProfissional == null) {
            equipeProfissional = new EquipeProfissional();
            equipeProfissional.setCodigo(0L);
            equipeProfissional.setEquipe(new Equipe());
            equipeProfissional.getEquipe().setReferencia("Sem equipe");
            equipeProfissional.getEquipe().setEquipeArea(new EquipeArea());
            equipeProfissional.getEquipe().getEquipeArea().setDescricao("Sem equipe");
        }
        return equipeProfissional;
    }

    public void setEquipeProfissional(EquipeProfissional equipeProfissional) {
        this.equipeProfissional = equipeProfissional;
    }

    public Empresa getEmpresaSolicitante() {
        if (Objects.isNull(this.empresaSolicitante) || Objects.isNull(this.empresaSolicitante.getCodigo())) {
            if (Objects.isNull(this.empresa) || Objects.isNull(this.empresa.getCodigo()) ){
                this.empresaSolicitante = new Empresa();
                this.empresaSolicitante.setCodigo(0L);
                this.empresaSolicitante.setDescricao("Unidade não Informada");
            }else{
                this.setEmpresaSolicitante(this.empresa);
            }
        }
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public Double getValorDiferenciado() {
        return Coalesce.asDouble(valorDiferenciado);
    }

    public void setValorDiferenciado(Double valorDiferenciado) {
        this.valorDiferenciado = valorDiferenciado;
    }


    public Double getValorTotalProcedimento() {
        return valorTotalProcedimento;
    }

    public void setValorTotalProcedimento(Double valorTotalProcedimento) {
        this.valorTotalProcedimento = valorTotalProcedimento;
    }

    public Double getValorTotalDiferenciado() {
        return valorTotalDiferenciado;
    }

    public void setValorTotalDiferenciado(Double valorTotalDiferenciado) {
        this.valorTotalDiferenciado = valorTotalDiferenciado;
    }


    public Double getValorTotalItens() {
        if(getValorTotalDiferenciado()!=0){
            valorTotalItens=getValorTotalDiferenciado();
        } else {
            valorTotalItens = getValorTotalProcedimento();
        }
        return valorTotalItens;
    }

    public void setValorTotalItens(Double valorTotalItens) {
        this.valorTotalItens = valorTotalItens;
    }

}
