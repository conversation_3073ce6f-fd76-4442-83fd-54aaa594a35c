package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Ciap;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioResumoAtendimentoCidDTOParam implements Serializable {

    private Empresa unidade;
    private TipoAtendimento tipoAtendimento;
    private Profissional profissional;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    private TipoCid tipoCid;
    private FormaApresentacao formaApresentacao;
    private CidNotificavel cidNotificavel;
    private DatePeriod periodo;
    private Ordenacao ordenacao;
    private TipoOrdenacao tipoOrdenacao;
    private TipoRelatorio tipoArquivo;
    private String tipoConsultaCidCiap;
    private Cid cid;
    private Ciap ciap;

    public enum TipoOrdenacao {

        DESC(Bundle.getStringApplication("rotulo_decrescente"), "desc"),
        ASC(Bundle.getStringApplication("rotulo_crescente"), "asc");

        private String name;
        private String command;

        private TipoOrdenacao(String name, String command) {
            this.name = name;
            this.command = command;
        }

        @Override
        public String toString() {
            return name;
        }

        public String getCommand() {
            return command;
        }

    }

    public enum Ordenacao {

        QUANTIDADE(Bundle.getStringApplication("rotulo_quantidade")),
        CID(Bundle.getStringApplication("rotulo_cid")),
        CIAP(Bundle.getStringApplication("rotulo_ciap"));

        private String name;

        private Ordenacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        UNIDADE(Bundle.getStringApplication("rotulo_unidade")),
        TIPO_ATENDIMENTO(Bundle.getStringApplication("rotulo_tipo_atendimento")),
        AREA(Bundle.getStringApplication("rotulo_area")),
        MICRO_AREA(Bundle.getStringApplication("rotulo_microarea")),
        FAIXA_ETARIA(Bundle.getStringApplication("rotulo_faixa_etaria")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum CidNotificavel {

        AMBOS(Bundle.getStringApplication("rotulo_ambos")),
        SIM(Bundle.getStringApplication("rotulo_sim")),
        NAO(Bundle.getStringApplication("rotulo_nao"));

        private String name;

        private CidNotificavel(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum TipoCid {

        PRINCIPAL(Bundle.getStringApplication("rotulo_principal")),
        SECUNDARIO(Bundle.getStringApplication("rotulo_secundario"));

        private String name;

        private TipoCid(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum CidCiap {

        CID(Bundle.getStringApplication("rotulo_cid_enum")),
        CIAP(Bundle.getStringApplication("rotulo_ciap"));

        private String name;

        private CidCiap(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    @DescricaoParametro("rotulo_cid")
    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    @DescricaoParametro("rotulo_unidade")
    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    @DescricaoParametro("rotulo_tipo_atendimento")
    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    @DescricaoParametro("rotulo_micro_area")
    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    @DescricaoParametro("rotulo_tipo_cid")
    public TipoCid getTipoCid() {
        return tipoCid;
    }

    public void setTipoCid(TipoCid tipoCid) {
        this.tipoCid = tipoCid;
    }

    @DescricaoParametro("rotulo_ciap")
    public Ciap getCiap() {
        return ciap;
    }

    public void setCiap(Ciap ciap) {
        this.ciap = ciap;
    }

    @DescricaoParametro("rotulo_cid_notificavel")
    public CidNotificavel getCidNotificavel() {
        return cidNotificavel;
    }

    public void setCidNotificavel(CidNotificavel cidNotificavel) {
        this.cidNotificavel = cidNotificavel;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public String getTipoConsultaCidCiap() {
        return tipoConsultaCidCiap;
    }

    public void setTipoConsultaCidCiap(String tipoConsultaCidCiap) {
        this.tipoConsultaCidCiap = tipoConsultaCidCiap;
    }
}
