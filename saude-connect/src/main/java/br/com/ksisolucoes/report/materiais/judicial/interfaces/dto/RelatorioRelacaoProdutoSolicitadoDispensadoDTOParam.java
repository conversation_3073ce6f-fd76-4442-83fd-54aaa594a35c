package br.com.ksisolucoes.report.materiais.judicial.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.TipoSolicitacaoProduto;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoProdutoSolicitadoDispensadoDTOParam implements Serializable {

    public enum FormaApresentacao {

        PACIENTE(Bundle.getStringApplication("rotulo_paciente")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        UNIDADE_SOLICITANTE(Bundle.getStringApplication("rotulo_unidade_solicitante")),
        UNIDADE_DISPENSACAO(Bundle.getStringApplication("rotulo_unidade_dispensadora")),
        PRODUTO(Bundle.getStringApplication("rotulo_produto"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    private Empresa empresaSolicitante;
    private Empresa empresaDispensacao;
    private Profissional profissional;
    private UsuarioCadsus usuarioCadsus;
    private TipoSolicitacaoProduto tipoSolicitacaoProduto;
    private Produto produto;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;

    @DescricaoParametro("rotulo_unidade_solicitante")
    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    @DescricaoParametro("rotulo_unidade_dispensadora")
    public Empresa getEmpresaDispensacao() {
        return empresaDispensacao;
    }

    public void setEmpresaDispensacao(Empresa empresaDispensacao) {
        this.empresaDispensacao = empresaDispensacao;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @DescricaoParametro("rotulo_produto")
    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }


    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_tipo_solicitacao_produtos")
    public TipoSolicitacaoProduto getTipoSolicitacaoProduto() {
        return tipoSolicitacaoProduto;
    }

    public void setTipoSolicitacaoProduto(TipoSolicitacaoProduto tipoSolicitacaoProduto) {
        this.tipoSolicitacaoProduto = tipoSolicitacaoProduto;
    }

}
