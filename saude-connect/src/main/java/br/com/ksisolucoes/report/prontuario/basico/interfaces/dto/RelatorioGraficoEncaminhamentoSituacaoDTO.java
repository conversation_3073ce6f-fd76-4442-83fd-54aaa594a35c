package br.com.ksisolucoes.report.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGraficoEncaminhamentoSituacaoDTO implements Serializable {

    private String descricaoFormaApresentacao;
    private Long status;
    private Long quantidade;
    private Long total;

    public String getDescricaoFormaApresentacao() {
        return descricaoFormaApresentacao;
    }

    public void setDescricaoFormaApresentacao(String descricaoFormaApresentacao) {
        this.descricaoFormaApresentacao = descricaoFormaApresentacao;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Double getPercentual(){
        return getQuantidade().doubleValue()*100/getTotal().doubleValue();
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getStatus() {
        return status;
    }

    public String getDescricaoStatus() {
        if(Encaminhamento.STATUS_PENDENTE.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_aguardando_autorizacao");
        }else if(Encaminhamento.STATUS_AUTORIZADO.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_aguardando_agendamento");
        }else if(Encaminhamento.STATUS_AGENDADO.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_agendado");
        }else if(Encaminhamento.STATUS_NAO_AUTORIZADO.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_nao_autorizado");
        }else if(Encaminhamento.STATUS_CONCLUIDO.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_concluido");
        }else if(Encaminhamento.STATUS_CONCLUIDO_COM_RETORNO.equals(getStatus())){
            return Bundle.getStringApplication("rotulo_concluido_com_retorno");
        }else{
            return Bundle.getStringApplication("rotulo_cancelado");
        }
    }

    public void setStatus(Long status) {
        this.status = status;
    }

}
