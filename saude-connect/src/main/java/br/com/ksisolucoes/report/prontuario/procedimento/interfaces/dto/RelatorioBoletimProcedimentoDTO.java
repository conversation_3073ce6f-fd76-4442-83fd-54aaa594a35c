/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioBoletimProcedimentoDTO implements Serializable{

    private Empresa empresa;
    private Profissional profissional;
    private Procedimento procedimento;
    private UsuarioCadsus usuarioCadsus;
    private TabelaCbo tabelaCbo;
    private Long quantidade;
    private Date data;
    private Convenio convenio;
    
    public Convenio getConvenio(){
        if(this.convenio == null || this.convenio.getCodigo() == null){
            this.convenio = new Convenio();
            this.convenio.setCodigo(0L);
            this.convenio.setDescricao("Desconhecido");
        }
        return convenio;
    }
    
    public void setConvenio(Convenio convenio){
        this.convenio = convenio;
    }

    public Empresa getEmpresa() {
        if (this.empresa == null) {
            this.empresa = new Empresa();
        }
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        if (this.profissional == null || this.profissional.getCodigo() == null) {
            this.profissional = new Profissional();
            this.profissional.setCodigo(0L);
            this.profissional.setNome("Sem Profissional");
        }
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Procedimento getProcedimento() {
        if (this.procedimento == null || this.procedimento.getCodigo() == null) {
            this.procedimento = new Procedimento();
            this.procedimento.setCodigo(0L);
            this.procedimento.setDescricao("Procedimento Desconhecido");
        }
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        if (this.usuarioCadsus == null || this.usuarioCadsus.getCodigo() == null) {
            this.usuarioCadsus = new UsuarioCadsus();
            this.usuarioCadsus.setCodigo(0L);
            this.usuarioCadsus.setNome("Sem Paciente");
        }
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getUsuarioCadsusFormatado() {
        if (this.usuarioCadsus != null && this.usuarioCadsus.getNome() != null) {
            return this.usuarioCadsus.getNomeSocial();
        }
        return "Sem Paciente";
    }

    public TabelaCbo getTabelaCbo() {
        if (this.tabelaCbo == null) {
            this.tabelaCbo = new TabelaCbo();
            this.tabelaCbo.setDescricao("Sem Cadastro");
        }
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public Date getData() {
        return Data.parserDate(Data.formatar(data));
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getDataFormatada() {
        return Data.formatar(getData());
    }

    public String getCompetenciaFormatada() {
        return Data.formatarMesAno(getData());
    }

    public String getDescricaoFormatado(){
        if (this.getProcedimento()!=null) {
            return this.getProcedimento().getDescricao();
        }
        
        return "";
    }

    public String getCodigoProcedimento(){
        try {
            MaskFormatter m = new MaskFormatter("##.##.##.###-#");
            m.setValueContainsLiteralCharacters(false);
            return m.valueToString(this.getProcedimento().getReferencia());
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return "";
    }

}
