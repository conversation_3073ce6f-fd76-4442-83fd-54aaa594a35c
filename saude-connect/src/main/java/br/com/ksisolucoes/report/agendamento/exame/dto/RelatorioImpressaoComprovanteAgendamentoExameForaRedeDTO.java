package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import java.io.Serializable;
import java.text.DecimalFormat;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTO implements Serializable{
    
    private ExameRequisicao exameRequisicao;
    private UsuarioCadsusCns usuarioCadsusCns;

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }
    
    public String getNomeEmpresaRelatorio(){
        Empresa empresa = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (StringUtils.trimToNull(empresa.getNomeRelatorio())!=null) {
            return empresa.getNomeRelatorio();
        }

        return empresa.getDescricao();
    }

    public String getNumeroExame(){
        return new DecimalFormat("00000000").format(exameRequisicao.getExame().getCodigo());
    }

    public String getNomeUsuario(){
        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();

        return usuario.getNome();
    }
}
