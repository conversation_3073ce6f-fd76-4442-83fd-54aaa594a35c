package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoExamesExecutadosDTO implements Serializable {

    private ExameProcedimento exameProcedimento;
    private Long quantidade;
    private Double precoUnitario;
    private Convenio convenio;
    private TipoExame tipoExame;
    private Profissional prof;
    private Profissional profissionalLaudo;

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Double getPrecoUnitario() {
        return precoUnitario;
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Profissional getProf() {
        return prof;
    }

    public void setProf(Profissional prof) {
        this.prof = prof;
    }

    public Profissional getProfissionalLaudo() {
        return profissionalLaudo;
    }

    public void setProfissionalLaudo(Profissional profissionalLaudo) {
        this.profissionalLaudo = profissionalLaudo;
    }

}
