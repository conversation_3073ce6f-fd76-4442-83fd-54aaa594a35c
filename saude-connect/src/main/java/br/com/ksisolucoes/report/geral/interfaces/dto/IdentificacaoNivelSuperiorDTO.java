package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoPK;

public final class IdentificacaoNivelSuperiorDTO extends EstruturaEquipamento {

    public static final String PROP_ESTOQUE_FISICO = "estoqueFisico";
    public static final String PROP_ESTOQUE_ENCOMENDADO = "estoqueEncomendado";
    public static final String PROP_ESTOQUE_RESERVADO = "estoqueReservado";
    private Double estoqueFisico;
    private Double estoqueEncomendado;
    private Double estoqueReservado;

    @Override
    public EstruturaEquipamentoPK getId() {
        return super.getId();
    }

    @Override
    public void setId(EstruturaEquipamentoPK id) {
        super.setId(id);
    }

    public Double getEstoqueEncomendado() {
        return Coalesce.asDouble(estoqueEncomendado);
    }

    public void setEstoqueEncomendado(Double estoqueEncomentdado) {
        this.estoqueEncomendado = estoqueEncomentdado;
    }

    public Double getEstoqueFisico() {
        return Coalesce.asDouble(estoqueFisico);
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public Double getEstoqueReservado() {
        return Coalesce.asDouble(estoqueReservado);
    }

    public void setEstoqueReservado(Double estoqueReservado) {
        this.estoqueReservado = estoqueReservado;
    }

    public Double getEstoqueDisponivel() {
        return Coalesce.asDouble(estoqueFisico) + Coalesce.asDouble(estoqueEncomendado) - Coalesce.asDouble(estoqueReservado);
    }
}
