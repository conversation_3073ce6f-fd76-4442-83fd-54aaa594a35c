package br.com.ksisolucoes.report.hospital.interfaces.dto.tiss;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoGuiasTissDTOParam implements Serializable {

    private Long codigoContaPaciente;
    private Long codigoConvenio;
    private Long tipoLayout;
    
    private boolean landscape;

    public Long getCodigoContaPaciente() {
        return codigoContaPaciente;
    }

    public void setCodigoContaPaciente(Long codigoContaPaciente) {
        this.codigoContaPaciente = codigoContaPaciente;
    }

    public Long getCodigoConvenio() {
        return codigoConvenio;
    }

    public void setCodigoConvenio(Long codigoConvenio) {
        this.codigoConvenio = codigoConvenio;
    }

    public boolean isLandscape() {
        return landscape;
    }

    public void setLandscape(boolean landscape) {
        this.landscape = landscape;
    }    

    public Long getTipoLayout() {
        return tipoLayout;
    }

    public void setTipoLayout(Long tipoLayout) {
        this.tipoLayout = tipoLayout;
    }
}
