package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import java.io.Serializable;

public class BalancoMedicamentosDTO implements Serializable {

    private Long numLanctoInicial;
    private Long numLanctoFinal;

    public BalancoMedicamentosDTO(String codigoDcb, String nomeDcb, String codigoMedicamento, String descricaoMedicamento, String produtoApresentacao, String concentracao, Double estoqueInicial, Double entradaAquisicao, Double saidaVendas, Double inventarioEntrada, Double inventarioSaida, Double estoqueFinal, Long numLanctoInicial, Long numLanctoFinal) {
        super();
        this.codigoDcb = codigoDcb;
        this.nomeDcb = nomeDcb;
        this.codigoMedicamento = codigoMedicamento;
        this.descricaoMedicamento = descricaoMedicamento;
        this.produtoApresentacao = produtoApresentacao;
        this.concentracao = concentracao;
        this.estoqueInicial = estoqueInicial;
        this.entradaAquisicao = entradaAquisicao;
        this.saidaVendas = saidaVendas;
        this.inventarioEntrada = inventarioEntrada;
        this.inventarioSaida = inventarioSaida;
        this.estoqueFinal = estoqueFinal;
        this.numLanctoInicial = numLanctoInicial;
        this.numLanctoFinal = numLanctoFinal;
    }

    public BalancoMedicamentosDTO() {
    }

    private static final long serialVersionUID = 1L;

    /*  Apresentacao/Concentracao montar a partir do cadastro de produto,
    Concentracao + descricao produto apresentacao.
     */
    public String getFormatadoApresentacaoConcentracao() {
        if ((this.concentracao == null) && (this.produtoApresentacao == null)) {
            return "";
        } else if ((this.concentracao != null) && (this.produtoApresentacao == null)) {
            return this.concentracao;
        } else if ((this.concentracao == null) && (this.produtoApresentacao != null)) {
            return this.produtoApresentacao;
        } else {
            return this.concentracao + " / " + this.produtoApresentacao;
        }
    }
    /* Perda - As perdas devem ser calculada ( Saidas -  Entradas).
    Perdas sera totalizacao dos lanctos de perdas.*/
    private Double perdas = 0.0;
    private Double perdaEntrada;
    private Double perdaSaida;

    public Double getPerdas() {
        if ((this.perdaEntrada == null) && (this.perdaSaida == null)) {
            return perdas;
        } else {
            if (this.getPerdaEntrada() == null) {
                perdas = this.getPerdaSaida() - 0.0;
            } else if (this.getPerdaSaida() == null) {
                perdas = 0.0 - this.getPerdaEntrada();
            } else if ((this.perdaEntrada != null) && (this.perdaSaida != null)) {
                perdas = this.getPerdaSaida() - this.getPerdaEntrada();
            }
        }
        return perdas;
    }

    public Double getPerdaEntrada() {
        return perdaEntrada;
    }

    public void setPerdaEntrada(Double perdaEntrada) {
        this.perdaEntrada = perdaEntrada;
    }

    public Double getPerdaSaida() {
        return perdaSaida;
    }

    public void setPerdaSaida(Double perdaSaida) {
        this.perdaSaida = perdaSaida;
    }
    /**
     * Holds value of property codigoDcb.
     */
    private String codigoDcb;

    /**
     * Getter for property codigoDcb.
     * @return Value of property codigoDcb.
     */
    public String getCodigoDcb() {
        return this.codigoDcb;
    }

    /**
     * Setter for property codigoDcb.
     * @param codigoDcb New value of property codigoDcb.
     */
    public void setCodigoDcb(String codigoDcb) {
        this.codigoDcb = codigoDcb;
    }
    /**
     * Holds value of property nomeDcb.
     */
    private String nomeDcb;

    /**
     * Getter for property nomeDcb.
     * @return Value of property nomeDcb.
     */
    public String getNomeDcb() {
        return this.nomeDcb;
    }

    /**
     * Setter for property nomeDcb.
     * @param nomeDcb New value of property nomeDcb.
     */
    public void setNomeDcb(String nomeDcb) {
        this.nomeDcb = nomeDcb;
    }
    /**
     * Holds value of property codigoMedicamento.
     */
    private String codigoMedicamento;

    /**
     * Getter for property codigoProduto.
     * @return Value of property codigoProduto.
     */
    public String getCodigoMedicamento() {
        return this.codigoMedicamento;
    }

    /**
     * Setter for property codigoProduto.
     * @param codigoProduto New value of property codigoProduto.
     */
    public void setCodigoMedicamento(String codigoMedicamento) {
        this.codigoMedicamento = codigoMedicamento;
    }
    /**
     * Holds value of property descricaoMedicamento.
     */
    private String descricaoMedicamento;

    /**
     * Getter for property descricaoProduto.
     * @return Value of property descricaoProduto.
     */
    public String getDescricaoMedicamento() {
        return this.descricaoMedicamento;
    }

    /**
     * Setter for property descricaoProduto.
     * @param descricaoProduto New value of property descricaoProduto.
     */
    public void setDescricaoMedicamento(String descricaoMedicamento) {
        this.descricaoMedicamento = descricaoMedicamento;
    }
    /**
     * Holds value of property produtoApresentacao.
     */
    private String produtoApresentacao;

    /**
     * Getter for property produtoApresentacao.
     * @return Value of property produtoApresentacao.
     */
    public String getProdutoApresentacao() {
        return this.produtoApresentacao;
    }

    /**
     * Setter for property produtoApresentacao.
     * @param produtoApresentacao New value of property produtoApresentacao.
     */
    public void setProdutoApresentacao(String produtoApresentacao) {
        this.produtoApresentacao = produtoApresentacao;
    }
    /**
     * Holds value of property concentracao.
     */
    private String concentracao;

    /**
     * Getter for property concentracao.
     * @return Value of property concentracao.
     */
    public String getConcentracao() {
        return this.concentracao;
    }

    /**
     * Setter for property concentracao.
     * @param concentracao New value of property concentracao.
     */
    public void setConcentracao(String concentracao) {
        this.concentracao = concentracao;
    }
    /**
     * Holds value of property estoqueInicial.
     */
    private Double estoqueInicial;

    /**
     * Getter for property estoqueInicial.
     * @return Value of property estoqueInicial.
     */
    public Double getEstoqueInicial() {
        return this.estoqueInicial;
    }

    /**
     * Setter for property estoqueInicial.
     * @param estoqueInicial New value of property estoqueInicial.
     */
    public void setEstoqueInicial(Double estoqueInicial) {
        this.estoqueInicial = estoqueInicial;
    }
    /**
     * Holds value of property estoqueFinal.
     */
    private Double estoqueFinal;

    /**
     * Getter for property estoqueFinal.
     * @return Value of property estoqueFinal.
     */
    public Double getEstoqueFinal() {
        return this.estoqueFinal;
    }

    /**
     * Setter for property estoqueFinal.
     * @param estoqueFinal New value of property estoqueFinal.
     */
    public void setEstoqueFinal(Double estoqueFinal) {
        this.estoqueFinal = estoqueFinal;
    }
    /**
     * Holds value of property inventarioSaida.
     */
    private Double inventarioSaida;

    /**
     * Getter for property perda.
     * @return Value of property perda.
     */
    public Double getInventarioSaida() {
        return this.inventarioSaida;
    }

    /**
     * Setter for property perda.
     * @param perda New value of property perda.
     */
    public void setInventarioSaida(Double inventarioSaida) {
        this.inventarioSaida = inventarioSaida;
    }
    /**
     * Holds value of property entradaAquisicao.
     */
    private Double entradaAquisicao;

    /**
     * Getter for property entradaAquisicao.
     * @return Value of property entradaAquisicao.
     */
    public Double getEntradaAquisicao() {
        return this.entradaAquisicao;
    }

    /**
     * Setter for property entradaAquisicao.
     * @param entradaAquisicao New value of property entradaAquisicao.
     */
    public void setEntradaAquisicao(Double entradaAquisicao) {
        this.entradaAquisicao = entradaAquisicao;
    }
    /**
     * Holds value of property saidaVendas.
     */
    private Double saidaVendas;

    /**
     * Getter for property saidaVendas.
     * @return Value of property saidaVendas.
     */
    public Double getSaidaVendas() {
        return this.saidaVendas;
    }

    /**
     * Setter for property saidaVendas.
     * @param saidaVendas New value of property saidaVendas.
     */
    public void setSaidaVendas(Double saidaVendas) {
        this.saidaVendas = saidaVendas;
    }
    /**
     * Holds value of property inventarioEntrada.
     */
    private Double inventarioEntrada;

    /**
     * Getter for property inventarioEntrada.
     * @return Value of property inventarioEntrada.
     */
    public Double getInventarioEntrada() {
        return this.inventarioEntrada;
    }

    /**
     * Setter for property inventarioEntrada.
     * @param inventarioEntrada New value of property inventarioEntrada.
     */
    public void setInventarioEntrada(Double inventarioEntrada) {
        this.inventarioEntrada = inventarioEntrada;
    }

    public Long getNumLanctoInicial() {
        return numLanctoInicial;
    }

    public void setNumLanctoInicial(Long numLanctoInicial) {
        this.numLanctoInicial = numLanctoInicial;
    }

    public Long getNumLanctoFinal() {
        return numLanctoFinal;
    }

    public void setNumLanctoFinal(Long numLanctoFinal) {
        this.numLanctoFinal = numLanctoFinal;
    }
}
