/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameBpai;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoExameBpaiDTO implements Serializable {

    private ExameRequisicao exameRequisicao;
    private ExameBpai exameBpai;
    private Exame exame;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Long codigoSolicitacaoAgendamento;
    
    private Long numeroCartao;
    private String numeroProntuario;

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public Exame getExame() {
        return exame;
    }

    public void setExame(Exame exame) {
        this.exame = exame;
    }

    public ExameBpai getExameBpai() {
        return exameBpai;
    }

    public void setExameBpai(ExameBpai exameBpai) {
        this.exameBpai = exameBpai;
    }

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }

    public Long getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(Long numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getNumeroProntuario() {
        return numeroProntuario;
    }

    public void setNumeroProntuario(String numeroProntuario) {
        this.numeroProntuario = numeroProntuario;
    }
    
    public String getNumeroCartaoFormatado(){
        return StringUtilKsi.getNumeroCartaoFormatado(getNumeroCartao());
    }

    public Long getCodigoSolicitacaoAgendamento() {
        return codigoSolicitacaoAgendamento;
    }

    public void setCodigoSolicitacaoAgendamento(Long codigoSolicitacaoAgendamento) {
        this.codigoSolicitacaoAgendamento = codigoSolicitacaoAgendamento;
    }   
    
    public String getDescricaoProcedimentoFormatado() {
        if(getExameRequisicao() != null && getExameRequisicao().getComplemento() != null){
            return getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()
                    + " (" + getExameRequisicao().getComplemento() + ")";
        }
    
        return getExameRequisicao().getExameProcedimento().getDescricaoProcedimento();
    }
}
