/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.TipoPreco;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioMovimentacaoDiariaDTOParam implements Serializable, ReportProperties {

    private static final long serialVersionUID = 1L;
    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<Produto>> produtos;
    private OperadorValor<List<CentroCusto>> centroCustos;
    private SubGrupo subGrupo;
    private OperadorValor<List<TipoDocumento>> tipoDocumentoList;
    private Date dataInicio;
    private Date dataFinal;
    private DatePeriod periodo;
    private String ordenacao;
    private Long formaApresentacao;
    private Long tipoRelatorio;
    private GrupoProduto grupoProdutoSubGrupo;
    private TipoPreco tipoPreco;

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
        if (this.periodo.getDataFinal() != null && this.periodo.getDataInicial() != null) {
            setDataFinal(this.periodo.getDataFinal());
            setDataInicio(this.periodo.getDataInicial());
        }
    }

    public GrupoProduto getGrupoProdutoSubGrupo() {
        return grupoProdutoSubGrupo;
    }

    public void setGrupoProdutoSubGrupo(GrupoProduto grupoProdutoSubGrupo) {
        this.grupoProdutoSubGrupo = grupoProdutoSubGrupo;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_produto")
    public OperadorValor<List<Produto>> getProdutos() {
        return produtos;
    }

    public void setProdutos(OperadorValor<List<Produto>> produtos) {
        this.produtos = produtos;
    }

    @DescricaoParametro("rotulo_subgrupo")
    public SubGrupo getSubGrupo() {
        return subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public String getOrdenacaoFormatado() {
        if(getTipoRelatorio().intValue() != RESUMIDO_TIPO){
            if( getOrdenacao().equals(Produto.PROP_CODIGO ) ) {
                return Bundle.getStringApplication( "rotulo_codigo" );
            } else if( getOrdenacao().equals(Produto.PROP_DESCRICAO ) ) {
                return Bundle.getStringApplication( "rotulo_descricao" );
            } else {
                return Bundle.getStringApplication( "rotulo_documento" );
            }
        }
        return "";
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public Long getFormaApresentacao() {
        return formaApresentacao;
    }

    public String getFormaApresentacaoFormatado() {
        if(getTipoRelatorio().intValue() != RESUMIDO_TIPO){
            if (getFormaApresentacao().intValue() == AGRUPAR_GRUPO){
                return Bundle.getStringApplication( "rotulo_grupo" );
            }else{
                return Bundle.getStringApplication( "rotulo_geral" );
            }
        }
        return "";
    }

    public void setFormaApresentacao(Long formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public Long getTipoRelatorio() {
        return tipoRelatorio;
    }

    public String getTipoRelatorioFormatado() {
        if(getTipoRelatorio().intValue() == RESUMIDO_PRODUTO){
            return Bundle.getStringApplication( "rotulo_resumido_produto" );
        }else if(getTipoRelatorio().intValue() == RESUMIDO_TIPO){
            return Bundle.getStringApplication( "rotulo_resumido_tipo" );
        }else if(getTipoRelatorio().intValue() == DETALHADO){
            return Bundle.getStringApplication( "rotulo_detalhado" );
        }
        return "";
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }
    /**
     * Holds value of property localizacao.
     */
    private OperadorValor<List<Localizacao>> localizacao;

    /**
     * Getter for property localizacao.
     * @return Value of property localizacao.
     */
    @DescricaoParametro("rotulo_localizacao")
    public OperadorValor<List<Localizacao>> getLocalizacao() {
        return this.localizacao;
    }

    /**
     * Setter for property localizacao.
     * @param localizacao New value of property localizacao.
     */
    public void setLocalizacao(OperadorValor<List<Localizacao>> localizacao) {
        this.localizacao = localizacao;
    }

    @DescricaoParametro("rotulo_tipo_documento")
    public OperadorValor<List<TipoDocumento>> getTipoDocumentoList() {
        return tipoDocumentoList;
    }

    public void setTipoDocumentoList(OperadorValor<List<TipoDocumento>> tipoDocumentoList) {
        this.tipoDocumentoList = tipoDocumentoList;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo(){
        return new DatePeriod(dataInicio, dataFinal);
    }

    @DescricaoParametro("rotulo_centro_custo")
    public OperadorValor<List<CentroCusto>> getCentroCustos() {
        return centroCustos;
    }

    public void setCentroCustos(OperadorValor<List<CentroCusto>> centroCustos) {
        this.centroCustos = centroCustos;
    }

    @DescricaoParametro("rotulo_tipo_preco")
    public TipoPreco getTipoPreco() {
        return tipoPreco;
    }

    public void setTipoPreco(TipoPreco tipoPreco) {
        this.tipoPreco = tipoPreco;
    }
}

