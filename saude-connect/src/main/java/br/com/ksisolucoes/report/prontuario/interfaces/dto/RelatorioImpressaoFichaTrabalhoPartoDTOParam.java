package br.com.ksisolucoes.report.prontuario.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoFichaTrabalhoPartoDTOParam implements Serializable{

    private Long codigoAtendimentoPrincipal;

    public Long getCodigoAtendimentoPrincipal() {
        return codigoAtendimentoPrincipal;
    }

    public void setCodigoAtendimentoPrincipal(Long codigoAtendimentoPrincipal) {
        this.codigoAtendimentoPrincipal = codigoAtendimentoPrincipal;
    }

}
