package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.basico.ProducaoCadastroEquipe;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioAcompanhamentoFamiliasDTO implements Serializable {

    private ProducaoCadastroEquipe producaoCadastroEquipe;
    private Long periodoPrimeiraSemana;
    private Long periodoSegundaSemana;
    private Long periodoTerceiraSemana;
    private Long periodoQuartaQuintaSemana;
    private Long periodoAposQuintaSemana;
    private Long somaIndividuos;
    private Long totalDomicilios;
    private Long cadAnteriores;
    private BigDecimal concluido;

    public void setConcluido(BigDecimal concluido) {
        this.concluido = concluido;
    }

    public BigDecimal getConcluido() {
        Dinheiro dTotalDomicilios = new Dinheiro(getTotalDomicilios());
        Dinheiro dTotalFamilia = new Dinheiro(getProducaoCadastroEquipe().getTotalFamilia());
        this.concluido = ((dTotalDomicilios.dividir(dTotalFamilia)).multiplicar(100D)).bigDecimalValue();
        return concluido;
    }

    public Long getTotalDomicilios() {
        totalDomicilios = periodoPrimeiraSemana + periodoSegundaSemana + periodoTerceiraSemana + periodoQuartaQuintaSemana + periodoAposQuintaSemana + cadAnteriores;
        return totalDomicilios;
    }

    public void setTotalDomicilios(Long totalDomicilios) {
        this.totalDomicilios = totalDomicilios;
    }

    public Long getPeriodoPrimeiraSemana() {
        return periodoPrimeiraSemana;
    }

    public void setPeriodoPrimeiraSemana(Long periodoPrimeiraSemana) {
        this.periodoPrimeiraSemana = periodoPrimeiraSemana;
    }

    public Long getPeriodoSegundaSemana() {
        return periodoSegundaSemana;
    }

    public void setPeriodoSegundaSemana(Long periodoSegundaSemana) {
        this.periodoSegundaSemana = periodoSegundaSemana;
    }

    public Long getPeriodoTerceiraSemana() {
        return periodoTerceiraSemana;
    }

    public void setPeriodoTerceiraSemana(Long periodoTerceiraSemana) {
        this.periodoTerceiraSemana = periodoTerceiraSemana;
    }

    public Long getPeriodoQuartaQuintaSemana() {
        return periodoQuartaQuintaSemana;
    }

    public void setPeriodoQuartaQuintaSemana(Long periodoQuartaQuintaSemana) {
        this.periodoQuartaQuintaSemana = periodoQuartaQuintaSemana;
    }

    public Long getPeriodoAposQuintaSemana() {
        return periodoAposQuintaSemana;
    }

    public void setPeriodoAposQuintaSemana(Long periodoAposQuintaSemana) {
        this.periodoAposQuintaSemana = periodoAposQuintaSemana;
    }

    public Long getSomaIndividuos() {
        return somaIndividuos;
    }

    public void setSomaIndividuos(Long somaIndividuos) {
        this.somaIndividuos = somaIndividuos;
    }

    public ProducaoCadastroEquipe getProducaoCadastroEquipe() {
        return producaoCadastroEquipe;
    }

    public void setProducaoCadastroEquipe(ProducaoCadastroEquipe producaoCadastroEquipe) {
        this.producaoCadastroEquipe = producaoCadastroEquipe;
    }

    public Long getCadAnteriores() {
        return cadAnteriores;
    }

    public void setCadAnteriores(Long cadAnteriores) {
        this.cadAnteriores = cadAnteriores;
    }
}
