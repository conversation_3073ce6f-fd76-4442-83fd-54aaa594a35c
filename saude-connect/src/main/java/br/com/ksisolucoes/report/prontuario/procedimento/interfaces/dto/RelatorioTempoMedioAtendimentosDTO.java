/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RelatorioTempoMedioAtendimentosDTO implements Serializable {

    private List<GrupoConsolidado> lstRisco;
    private List<GrupoConsolidado> lstProfissional;
    private List<GrupoConsolidado> lstTempoMedio;
    private List<GrupoConsolidado> lstTipoAtendimento;

    public static class GrupoConsolidado implements Serializable {
        private Long codigo;
        private String descricao;
        private Long quantidade;
        private String tempo;
        private Long nivelGravidade;
        private String formatado;
        private Atendimento atendimento;
        private TipoAtendimento tpAtendimento;
        private TabelaCbo tabelaCbo;

        public enum NivelGravidade {

            VERMELHO(0L, Bundle.getStringApplication("rotulo_vermelho")),
            LARANJA(1L, Bundle.getStringApplication("rotulo_laranja")),
            AMARELO(2L, Bundle.getStringApplication("rotulo_amarelo")),
            VERDE(3L, Bundle.getStringApplication("rotulo_verde")),
            AZUL(4L, Bundle.getStringApplication("rotulo_azul")),
            CINZA(5L, Bundle.getStringApplication("rotulo_cinza"));

            private Long nivel;
            private String caminhoImagem;

            private NivelGravidade(Long nivel, String caminhoImagem) {
                this.nivel = nivel;
                this.caminhoImagem = caminhoImagem;
            }

            public Long value() {
                return nivel;
            }

            public String caminhoImagem() {
                return caminhoImagem;
            }

            public static ClassificacaoRisco.NivelGravidade valeuOf(Long value) {
                for (ClassificacaoRisco.NivelGravidade nivelGravidade : ClassificacaoRisco.NivelGravidade.values()) {
                    if (nivelGravidade.value().equals(value)) {
                        return nivelGravidade;
                    }
                }
                return null;
            }
        }

        public TipoAtendimento getTpAtendimento() {
            return tpAtendimento;
        }

        public void setTpAtendimento(TipoAtendimento tpAtendimento) {
            this.tpAtendimento = tpAtendimento;
        }

        public TabelaCbo getTabelaCbo() {
            return tabelaCbo;
        }

        public void setTabelaCbo(TabelaCbo tabelaCbo) {
            this.tabelaCbo = tabelaCbo;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public Long getQuantidade() {
            return quantidade;
        }

        public void setQuantidade(Long quantidade) {
            this.quantidade = quantidade;
        }

        public String getTempo() {
            return tempo;
        }

        public void setTempo(String tempo) {
            this.tempo = tempo;
        }

        public Long getNivelGravidade() {
            return nivelGravidade;
        }

        public void setNivelGravidade(Long nivelGravidade) {
            this.nivelGravidade = nivelGravidade;
        }

        public String getFormatado() {
            return getClassificacaoFormatado();
        }

        public void setFormatado(String formatado) {
            this.formatado = formatado;
        }

        public Atendimento getAtendimento() {
            return atendimento;
        }

        public Long getCodigo() {
            return codigo;
        }

        public void setCodigo(Long codigo) {
            this.codigo = codigo;
        }

        public void setAtendimento(Atendimento atendimento) {
            this.atendimento = atendimento;
        }

        public String getClassificacaoFormatado(){
            if (ClassificacaoRisco.NivelGravidade.VERMELHO.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_vermelho") + " )";
            } else if (ClassificacaoRisco.NivelGravidade.LARANJA.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_laranja") + " )";
            }  else if (ClassificacaoRisco.NivelGravidade.AMARELO.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_amarelo") + " )";
            } else if (ClassificacaoRisco.NivelGravidade.VERDE.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_verde") + " )";
            } else if (ClassificacaoRisco.NivelGravidade.AZUL.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "(" + Bundle.getStringApplication("rotulo_azul") + ")";
            } else if (ClassificacaoRisco.NivelGravidade.CINZA.value().equals(getNivelGravidade())){
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_cinza") + " )";
            } else if (ClassificacaoRisco.NivelGravidade.CINZA2.value().equals(getNivelGravidade())) {
                return getDescricao() + " " + "( " + Bundle.getStringApplication("rotulo_cinza") + " )";
            }
            else{
                return "";
            }
        }
    }

    public List<GrupoConsolidado> getLstRisco() {
        return lstRisco;
    }

    public void setLstRisco(List<GrupoConsolidado> lstRisco) {
        this.lstRisco = lstRisco;
    }

    public List<GrupoConsolidado> getLstProfissional() {
        return lstProfissional;
    }

    public void setLstProfissional(List<GrupoConsolidado> lstProfissional) {
        this.lstProfissional = lstProfissional;
    }

    public List<GrupoConsolidado> getLstTempoMedio() {
        return lstTempoMedio;
    }

    public void setLstTempoMedio(List<GrupoConsolidado> lstTempoMedio) {
        this.lstTempoMedio = lstTempoMedio;
    }

    public List<GrupoConsolidado> getLstTipoAtendimento() {
        return lstTipoAtendimento;
    }

    public void setLstTipoAtendimento(List<GrupoConsolidado> lstTipoAtendimento) {
        this.lstTipoAtendimento = lstTipoAtendimento;
    }
}
