package br.com.ksisolucoes.report.agendamento.exame.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.Procedimento;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExamesAutorizadosDTOParam.TipoResumo;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.io.Serializable;
import java.util.Comparator;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoExamesAutorizadosDTO implements Serializable {
    
    private ExameProcedimento exameProcedimento;
    private Exame exame;
    private Empresa unidadeSolicitante;
    private Empresa unidadeExecutante;
    private TipoExame tipoExame;
    private Profissional profissional;
    private Long quantidade;
    private Double valor;
    private String periodo;
    private Integer mes;
    private Integer ano;
    private TipoResumo tipoResumo;
    private Double valorSUS;
    private Double valorComplementoParcial;
    private Double valorComplemento;
    private Procedimento procedimento;
    private Double valorRecursoProprio;

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Exame getExame() {
        return exame;
    }

    public void setExame(Exame exame) {
        this.exame = exame;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getValorSUS() {
        return valorSUS;
    }

    public void setValorSUS(Double valorSUS) {
        this.valorSUS = valorSUS;
    }

    public Double getValorComplementoParcial() {
        return valorComplementoParcial;
    }

    public void setValorComplementoParcial(Double valorComplementoParcial) {
        this.valorComplementoParcial = valorComplementoParcial;
    }

    public Double getValorComplemento() {
        if (getValorComplementoParcial() != null && getValorSUS() != null && getValorComplementoParcial() >= getValorSUS()) {
            return new Dinheiro(getValorComplementoParcial() - getValorSUS()).doubleValue();
        }
        return new Double(0.0);
    }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }


    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Empresa getUnidadeSolicitante() {
        return unidadeSolicitante;
    }

    public void setUnidadeSolicitante(Empresa unidadeSolicitante) {
        this.unidadeSolicitante = unidadeSolicitante;
    }

    public Empresa getUnidadeExecutante() {
        return unidadeExecutante;
    }

    public void setUnidadeExecutante(Empresa unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    public String getDescricaoTipoResumo() {
        if (RelatorioResumoExamesAutorizadosDTOParam.TipoResumo.PROCEDIMENTO.equals(getTipoResumo())) {
            return getExameProcedimento().getProcedimento().getDescricaoFormatado();
        } else if (RelatorioResumoExamesAutorizadosDTOParam.TipoResumo.SOLICITANTE.equals(getTipoResumo())) {
            return getUnidadeSolicitante().getDescricao();
        } else if (RelatorioResumoExamesAutorizadosDTOParam.TipoResumo.EXECUTANTE.equals(getTipoResumo())) {
            return getUnidadeExecutante().getDescricao();
        } else if (RelatorioResumoExamesAutorizadosDTOParam.TipoResumo.TIPO_EXAME.equals(getTipoResumo())) {
            return getTipoExame().getDescricao();
        } else {
            if(getProfissional().getNome() == null || "".equals(getProfissional().getNome().trim())){
                return Bundle.getStringApplication("msg_profissional_nao_definido").toUpperCase();
            } else {
                return getProfissional().getNome();
            }
        }
    }
    
    /* Comparators utilizados pelo crosstab */
    public static class ComparatorPeriodo implements Comparator<String> {

        @Override
        public int compare(String o1, String o2) {
            o1 = Coalesce.asString(o1);
            String[] data1 = o1.split("/");
            if (data1.length == 2) {
                String mes = data1[0];
                String ano = data1[1];

                o2 = Coalesce.asString(o2);
                String[] data2 = o2.split("/");
                if (data2.length == 2) {
                    String mes2 = data2[0];
                    String ano2 = data2[1];

                    if (ano.equals(ano2)) {
                        if (mes.equals(mes2)) {
                            return Coalesce.asLong(mes).compareTo(Coalesce.asLong(mes2));
                        } else {
                            return Coalesce.asLong(mes).compareTo(Coalesce.asLong(mes2));
                        }
                    } else {
                        return Coalesce.asLong(ano).compareTo(Coalesce.asLong(ano2));
                    }
                }
            }
            return 0;
        }
    };

    public Procedimento getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public Double getValorRecursoProprio() {
        return valorRecursoProprio;
    }

    public void setValorRecursoProprio(Double valorRecursoProprio) {
        this.valorRecursoProprio = valorRecursoProprio;
    }
}
