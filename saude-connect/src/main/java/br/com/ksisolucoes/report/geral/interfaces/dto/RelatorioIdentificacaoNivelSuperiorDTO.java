package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.List;

public class RelatorioIdentificacaoNivelSuperiorDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * Holds value of property estruturas.
     */
    private List<Produto> estruturas;
    private Empresa empresa;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    /**
     * Getter for property estruturas.
     * @return Value of property estruturas.
     */
    public List<Produto> getEstruturas() {
        return this.estruturas;
    }

    /**
     * Setter for property estruturas.
     * @param estruturas New value of property estruturas.
     */
    public void setEstruturas(List<Produto> estruturas) {
        this.estruturas = estruturas;
    }
    /**
     * Holds value of property componentes.
     */
    private List<Produto> componentes;

    /**
     * Getter for property componentes.
     * @return Value of property componentes.
     */
    public List<Produto> getComponentes() {
        return this.componentes;
    }

    /**
     * Setter for property componentes.
     * @param componentes New value of property componentes.
     */
    public void setComponentes(List<Produto> componentes) {
        this.componentes = componentes;
    }
}
