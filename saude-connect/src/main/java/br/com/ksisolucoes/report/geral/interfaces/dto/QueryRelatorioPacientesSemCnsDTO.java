package br.com.ksisolucoes.report.geral.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioPacientesSemCnsDTO implements Serializable {
    
    private UsuarioCadsus usuarioCadsus;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private Empresa empresa;
    private EquipeMicroArea equipeMicroArea;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }
    
    public String getDescricaoUnidade() {
        StringBuilder descricao = new StringBuilder();
        descricao.append(Bundle.getStringApplication("rotulo_unidade"));
        descricao.append(": ");

        if (getEmpresa() != null && getEmpresa().getCodigo() != null) {
            descricao.append(getEmpresa().getDescricao());
        } else {
            descricao.append(Bundle.getStringApplication("rotumo_sem_unidade_definida_endereco"));
        }

        return descricao.toString();
    }
    
    public String getDescricaoArea() {
        StringBuilder descricao = new StringBuilder();

        if (getEquipeMicroArea() != null && getEquipeMicroArea().getEquipeArea() != null && getEquipeMicroArea().getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeMicroArea().getEquipeArea().getDescricao());
        } else {
            descricao.append("Sem Área");
        }

        return descricao.toString();
    }

    public String getDescricaoMicroArea() {
        StringBuilder descricao = new StringBuilder();

        if (getEquipeMicroArea() != null && getEquipeMicroArea().getEquipeArea() != null && getEquipeMicroArea().getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeMicroArea().getEquipeArea().getDescricao());

            descricao.append(" / ");
            if (getEquipeMicroArea().getMicroArea() != null) {
                descricao.append(getEquipeMicroArea().getMicroArea());
            } else {
                descricao.append("Sem Microárea");
            }
        } else {
            descricao.append("Sem Área / Microárea");
        }

        return descricao.toString();
    }
}
