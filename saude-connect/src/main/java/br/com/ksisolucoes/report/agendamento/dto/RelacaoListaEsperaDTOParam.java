package br.com.ksisolucoes.report.agendamento.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelacaoListaEsperaDTOParam implements Serializable {
    
    public static enum FormaApresentacao {        
        GERAL(Bundle.getStringApplication("rotulo_geral")),
        TIPO_PROCEDIMENTO(Bundle.getStringApplication("rotulo_tipo_procedimento")),
        UNIDADE(Bundle.getStringApplication("rotulo_unidade"));
        
        private String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
        
    }
    
    public static enum Ordenacao {
        DATA_SOLICITACAO(Bundle.getStringApplication("rotulo_data_solicitacao")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente"));
        
        private String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }
        
        @Override
        public String toString() {
            return descricao;
        }
    }
    
    private Empresa unidade;
    private UsuarioCadsus paciente;
    private TipoProcedimento tipoProcedimento;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private Ordenacao ordenacao;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    
    @DescricaoParametro("rotulo_unidade")
    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")    
    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    @DescricaoParametro("rotulo_microarea")
    public String getDescricaoEquipeMicroArea() {
        return equipeMicroArea != null
                ? (equipeMicroArea.getMicroArea().toString() + " - "
                + (equipeMicroArea.getEquipeProfissional() != null
                ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome()
                : Bundle.getStringApplication("rotulo_sem_profissional")
        )
        )
                : null;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }
}
