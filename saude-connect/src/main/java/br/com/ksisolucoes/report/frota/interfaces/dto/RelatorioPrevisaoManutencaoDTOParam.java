package br.com.ksisolucoes.report.frota.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.frota.TipoVeiculo;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPrevisaoManutencaoDTOParam implements Serializable{
    
    public enum SituacaoKm implements IEnum<SituacaoKm>{
        SITUACAO_500KM(500L, Bundle.getStringApplication("rotulo_500km")),
        SITUACAO_1000KM(1000L, Bundle.getStringApplication("rotulo_1000km")),
        SITUACAO_2000KM(2000L, Bundle.getStringApplication("rotulo_2000km")),
        SITUACAO_5000KM(5000L, Bundle.getStringApplication("rotulo_5000km")),
        ;

        private Long valor;
        private String descricao;
        
        private SituacaoKm(Long valor, String descricao){
            this.valor = valor;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        public Long getValor() {
            return valor;
        }
        
    }
    
    public enum SituacaoPeriodo implements IEnum<SituacaoPeriodo>{
        SITUACAO_1_MES(1L, Bundle.getStringApplication("rotulo_1_mes")),
        SITUACAO_2_MESES(2L, Bundle.getStringApplication("rotulo_2_meses")),
        SITUACAO_3_MESES(3L, Bundle.getStringApplication("rotulo_3_meses")),
        SITUACAO_6_MESES(6L, Bundle.getStringApplication("rotulo_6_meses")),
        ;

        private Long valor;
        private String descricao;
        
        private SituacaoPeriodo(Long valor, String descricao){
            this.valor = valor;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        public Long getValor() {
            return valor;
        }
        
    }
    
    private ProgramaSaude programaSaude;
    private TipoVeiculo tipoVeiculo;
    private Boolean somenteVencidos;
    private SituacaoKm situacaoKm;
    private SituacaoPeriodo situacaoPeriodo;

    @DescricaoParametro("rotulo_programa_saude")
    public ProgramaSaude getProgramaSaude() {
        return programaSaude;
    }

    public void setProgramaSaude(ProgramaSaude programaSaude) {
        this.programaSaude = programaSaude;
    }

    @DescricaoParametro("rotulo_somente_vencidos")
    public Boolean getSomenteVencidos() {
        return somenteVencidos;
    }

    public void setSomenteVencidos(boolean somenteVencidos) {
        this.somenteVencidos = somenteVencidos;
    }

    @DescricaoParametro("rotulo_tipo_veiculo")
    public TipoVeiculo getTipoVeiculo() {
        return tipoVeiculo;
    }

    public void setTipoVeiculo(TipoVeiculo tipoVeiculo) {
        this.tipoVeiculo = tipoVeiculo;
    }

    public SituacaoKm getSituacaoKm() {
        return situacaoKm;
    }
    
    @DescricaoParametro("rotulo_situacao_km")
    public String getDescricaoSituacaoKm() {
        if (getSituacaoKm() != null) {
            return getSituacaoKm().descricao();
        }
        return null;
    }

    public void setSituacaoKm(SituacaoKm situacaoKm) {
        this.situacaoKm = situacaoKm;
    }

    public SituacaoPeriodo getSituacaoPeriodo() {
        return situacaoPeriodo;
    }
    
    @DescricaoParametro("rotulo_situacao_periodo")
    public String getDescricaoSituacaoPeriodo() {
        if (getSituacaoPeriodo()!=null) {
            return getSituacaoPeriodo().descricao();
        }
        return null;
    }

    public void setSituacaoPeriodo(SituacaoPeriodo situacaoPeriodo) {
        this.situacaoPeriodo = situacaoPeriodo;
    }

}
