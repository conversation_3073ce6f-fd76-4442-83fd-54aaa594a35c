package br.com.ksisolucoes.report.vacina.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelacaoVacinasAtrasadasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vacina.dto.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.report.vacina.VacinaReportBO")
public interface VacinaReportFacade {

    public DataReport relatorioEstoqueVacinas(RelatorioEstoqueVacinasDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoPedidoVacina(Long codigoPedido) throws ReportException;
    
    public DataReport relatorioImpressaoPedidoInsumo(Long codigoPedido) throws ReportException;
    
    public DataReport relatorioPedidoVacinaRotina(RelatorioPedidoVacinaRotinaDTOParam param) throws ReportException;
    
    public DataReport relatorioPedidoInsumo(RelatorioPedidoInsumoDTOParam param) throws ReportException;
    
    public DataReport relatorioRecebimentoVacinas(RelatorioRecebimentoVacinasDTOParam param) throws ReportException;
    
    public DataReport relatorioImpressaoCalendarioVacinacao(RelatorioImpressaoCalendarioVacinacaoDTOParam param) throws ReportException;

    public DataReport relatorioImpressaoHistoricoVacinacao(RelatorioImpressaoHistoricoVacinacaoDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoVacinasAplicadas(RelatorioRelacaoVacinasAplicadasDTOParam param) throws ReportException;
    
    public DataReport relacaoPacientesVacinasAtrasadas(QueryRelacaoVacinasAtrasadasDTOParam param) throws ReportException;

    public DataReport relacaoVacinasNaoAplicadas(RelacaoVacinasNaoAplicadasDTOParam param) throws ReportException;

    public DataReport relacaoPacientesVacinasAtrasadasSync(QueryRelacaoVacinasAtrasadasDTOParam param) throws ReportException;

    public DataReport relatorioCarteiraVacinacao(RelatorioCarteiraVacinacaoDTOParam param) throws ReportException;

    public List<RelatorioImpressaoHistoricoVacinacaoDTO> consultaHistoricoVacinacao(RelatorioImpressaoHistoricoVacinacaoDTOParam param) throws DAOException, ValidacaoException;

    public DataReport relatorioRegistrosTemperatura(RelatorioRegistrosTemperaturaDTOParam param) throws ReportException;

    public DataReport relatorioPacientesGrupoVacinacao(RelatorioPacientesGrupoVacinacaoDTOParam param) throws ReportException;

    public DataReport relatorioCarteiraVacinacaoDigital(RelatorioCertificadoVacinacaoDigitalDTOParam param) throws ReportException;
}
