package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.lgpd.LgpdFilterRandomNumberAsText;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoProntuarioDTO implements Serializable {

    private Empresa empresa;
    private Atendimento atendimento;
    private Profissional profissional;
    private TabelaCbo cboProfissional;
    private UsuarioCadsus usuarioCadsus;
    private TipoAtendimento tipoAtendimento;
    private AtendimentoProntuario atendimentoProntuario;
    private String cns;
    private List<UsuarioCadsusOcorrencia> usuarioCadsusOcorrenciaList;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private AtendimentoPrimario atendimentoPrimario;

    private String tempoPermanencia;
    private Date dataChegada;
    private Date dataSaida;
    private List<GrupoProblemasCondicoes> grupoProblemasCondicoes;

    private EvolucaoProntuario evolucaoProntuario;

    public List<UsuarioCadsusOcorrencia> getUsuarioCadsusOcorrenciaList() {
        return usuarioCadsusOcorrenciaList;
    }

    public void setUsuarioCadsusOcorrenciaList(List<UsuarioCadsusOcorrencia> usuarioCadsusOcorrenciaList) {
        this.usuarioCadsusOcorrenciaList = usuarioCadsusOcorrenciaList;
    }

    public String getCns() {
        return StringUtilKsi.getNumeroCartaoFormatado(new LgpdFilterRandomNumberAsText().value(cns));
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public AtendimentoProntuario getAtendimentoProntuario() {
        return atendimentoProntuario;
    }

    public void setAtendimentoProntuario(AtendimentoProntuario atendimentoProntuario) {
        this.atendimentoProntuario = atendimentoProntuario;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public String getTempoPermanencia() {
        return tempoPermanencia;
    }

    public void setTempoPermanencia(String tempoPermanencia) {
        this.tempoPermanencia = tempoPermanencia;
    }

    public Date getDataChegada() {
        return dataChegada;
    }

    public void setDataChegada(Date dataChegada) {
        this.dataChegada = dataChegada;
    }

    public Date getDataSaida() {
        return dataSaida;
    }

    public void setDataSaida(Date dataSaida) {
        this.dataSaida = dataSaida;
    }

    public EvolucaoProntuario getEvolucaoProntuario() {
        return evolucaoProntuario;
    }

    public void setEvolucaoProntuario(EvolucaoProntuario evolucaoProntuario) {
        this.evolucaoProntuario = evolucaoProntuario;
    }

    public TabelaCbo getCboProfissional() {
        return cboProfissional;
    }

    public void setCboProfissional(TabelaCbo cboProfissional) {
        this.cboProfissional = cboProfissional;
    }

    public List<GrupoProblemasCondicoes> getGrupoProblemasCondicoes() {
        return grupoProblemasCondicoes;
    }

    public void setGrupoProblemasCondicoes(List<GrupoProblemasCondicoes> grupoProblemasCondicoes) {
        this.grupoProblemasCondicoes = grupoProblemasCondicoes;
    }

    public AtendimentoPrimario getAtendimentoPrimario() {
        return atendimentoPrimario;
    }

    public void setAtendimentoPrimario(AtendimentoPrimario atendimentoPrimario) {
        this.atendimentoPrimario = atendimentoPrimario;
    }
}
