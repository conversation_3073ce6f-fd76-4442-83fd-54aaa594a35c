package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupo;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR> Américo
 */
public class RelatorioRelacaoEntradaPacienteDTO implements Serializable{

    private Atendimento atendimento;
    private Long totalAtendimento;


    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Long getTotalAtendimento() {
        return totalAtendimento;
    }

    public void setTotalAtendimento(Long totalAtendimento) {
        this.totalAtendimento = totalAtendimento;
    }
}
