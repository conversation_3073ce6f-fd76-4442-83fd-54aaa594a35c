package br.com.ksisolucoes.report.hospital.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioMapaLeitosDTO implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private LeitoQuarto leitoQuarto;
    private Empresa empresa;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public LeitoQuarto getLeitoQuarto() {
        return leitoQuarto;
    }

    public void setLeitoQuarto(LeitoQuarto leitoQuarto) {
        this.leitoQuarto = leitoQuarto;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
