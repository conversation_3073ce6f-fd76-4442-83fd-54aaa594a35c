package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoCargaHorariaDTO implements Serializable {

    private Long profissionais;
    private Long chAmb;
    private Long chHosp;
    private Long chOut;
    private String tipoVinculo;
    private TabelaCbo tabelaCbo;
    private Empresa empresa;

    public Long getProfissionais() {
        return profissionais;
    }

    public void setProfissionais(Long profissionais) {
        this.profissionais = profissionais;
    }

    public Long getChAmb() {
        return chAmb;
    }

    public void setChAmb(Long chAmb) {
        this.chAmb = chAmb;
    }

    public Long getChHosp() {
        return chHosp;
    }

    public void setChHosp(Long chHosp) {
        this.chHosp = chHosp;
    }

    public Long getChOut() {
        return chOut;
    }

    public void setChOut(Long chOut) {
        this.chOut = chOut;
    }

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
