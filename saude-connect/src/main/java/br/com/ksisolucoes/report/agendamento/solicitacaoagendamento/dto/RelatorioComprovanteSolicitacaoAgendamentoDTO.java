package br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioComprovanteSolicitacaoAgendamentoDTO implements Serializable {

    private SolicitacaoAgendamento solicitacaoAgendamento;
    private UsuarioCadsusCns usuarioCadsusCns;
    private List<SolicitacaoAgendamentoExame> exames;

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public SolicitacaoAgendamento getSolicitacaoAgendamento() {
        return solicitacaoAgendamento;
    }

    public void setSolicitacaoAgendamento(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }
    
    public String getDescricaoProcedimentoSolicitado(){
        String codigo = solicitacaoAgendamento.getProcedimento().getCodigoFormatado();
        String descricao = solicitacaoAgendamento.getTipoProcedimento().getTipoProcedimentoClassificacao().pertenceClassificacaoExame()
            ?
                solicitacaoAgendamento.getProcedimento().getDescricao()
            :
                solicitacaoAgendamento.getTipoProcedimento().getDescricao();
        return codigo+" - "+descricao;
    }

    public String getDescricaoPrioridade(){
        return solicitacaoAgendamento.getDescricaoPrioridade();
    }

    public String getDescricaoPrioridadeComSolicitacao(){
        return  solicitacaoAgendamento.getDescricaoPrioridadeComSolicitacao();
    }

    public String getDescricaoTipoProcedimento(){
        return solicitacaoAgendamento.getTipoProcedimento().getTipoProcedimentoClassificacao().getDescricao()+" - "+solicitacaoAgendamento.getTipoProcedimento().getDescricao();
    }

    public List<SolicitacaoAgendamentoExame> getExames() {
        return exames;
    }

    public void setExames(List<SolicitacaoAgendamentoExame> exames) {
        this.exames = exames;
    }
}
