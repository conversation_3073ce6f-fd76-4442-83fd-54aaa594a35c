package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProcedimentoDetalhadoDTOParam implements Serializable {

    public static final String PROP_ORDENACAO = "ordenacao";
    public static final String PROP_TIPO_ORDENACAO = "tipoOrdenacao";
    public static final String PROP_FORMA_APRESENTACAO = "formaApresentacao";

    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<Profissional>> profissionals;
    private OperadorValor<List<Procedimento>> procedimento;
    private UsuarioCadsus usuariosCadsus;
    private OperadorValor<List<TabelaCbo>> tabelasCbo;
    private OperadorValor<List<Convenio>> convenio;
    private TabelaCboSubGrupo tabelaCboSubGrupo;
    private String agruparUnidade;
    private DatePeriod periodo;
    private String tipoProcedimento;
    private TipoOrdenacao tipoOrdenacao;
    private Ordenacao ordenacao;
    private FormaApresentacao formaApresentacao;
    private Cidade cidade;
    private SituacaoProducao situacaoProducao;
    private Empresa empresaSolicitante;
    private TipoRelatorio tipoArquivo;
    
    public static enum SituacaoProducao {
        
        FECHADA(Bundle.getStringApplication("rotulo_fechada")),
        ABERTA(Bundle.getStringApplication("rotulo_aberta")),
        AMBOS(Bundle.getStringApplication("rotulo_ambas"));

        private final String descricao;

        private SituacaoProducao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public enum Ordenacao {

        PACIENTE(Bundle.getStringApplication("rotulo_paciente")),
        DATA(Bundle.getStringApplication("rotulo_data")),
        PROCEDIMENTO(Bundle.getStringApplication("rotulo_procedimento")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional"));

        private String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

    }

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente")),
        CBO(Bundle.getStringApplication("rotulo_cbo")),
        CONVENIO(Bundle.getStringApplication("rotulo_convenio")),
        PROCEDIMENTO(Bundle.getStringApplication("rotulo_procedimento")),
        MUNICIPIO(Bundle.getStringApplication("rotulo_municipio")),
        UNIDADE_ORIGEM(Bundle.getStringApplication("rotulo_unidade_origem"));

        private String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

    }

    public enum TipoOrdenacao {

        CRESCENTE(Bundle.getStringApplication("rotulo_crescente")),
        DECRESCENTE(Bundle.getStringApplication("rotulo_decrescente"));

        private String descricao;

        private TipoOrdenacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }


    @DescricaoParametro("rotulo_situacao_producao")
    public SituacaoProducao getSituacaoProducao() {
        return situacaoProducao;
    }

    public void setSituacaoProducao(SituacaoProducao situacaoProducao) {
        this.situacaoProducao = situacaoProducao;
    }

    @DescricaoParametro("rotulo_unidade")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissionals() {
        return profissionals;
    }

    public void setProfissionals(OperadorValor<List<Profissional>> profissionals) {
        this.profissionals = profissionals;
    }

    public OperadorValor<List<Procedimento>> getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(OperadorValor<List<Procedimento>> procedimento) {
        this.procedimento = procedimento;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getUsuariosCadsus() {
        return usuariosCadsus;
    }

    public void setUsuariosCadsus(UsuarioCadsus usuariosCadsus) {
        this.usuariosCadsus = usuariosCadsus;
    }

    public OperadorValor<List<TabelaCbo>> getTabelasCbo() {
        return tabelasCbo;
    }

    public void setTabelasCbo(OperadorValor<List<TabelaCbo>> tabelasCbo) {
        this.tabelasCbo = tabelasCbo;
    }

    public OperadorValor<List<Convenio>> getConvenio() {
        return convenio;
    }

    public void setConvenio(OperadorValor<List<Convenio>> convenio) {
        this.convenio = convenio;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_agrupar_unidade")
    public String getDescricaoAgruparUnidade() {
        if(RepositoryComponentDefault.SIM.equals(agruparUnidade)){
            return Bundle.getStringApplication("rotulo_sim");
        }else{
            return Bundle.getStringApplication("rotulo_nao");
        }
    }
    
    public String getAgruparUnidade() {
        return agruparUnidade;
    }

    public void setAgruparUnidade(String agruparUnidade) {
        this.agruparUnidade = agruparUnidade;
    }

    public TabelaCboSubGrupo getTabelaCboSubGrupo() {
        return tabelaCboSubGrupo;
    }

    public void setTabelaCboSubGrupo(TabelaCboSubGrupo tabelaCboSubGrupo) {
        this.tabelaCboSubGrupo = tabelaCboSubGrupo;
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    @DescricaoParametro("rotulo_tipo_procedimento")
    public String getTipoProcedimentoFormatado() {
        if (RepositoryComponentDefault.SIM.equals(getTipoProcedimento())) {
            return Bundle.getStringApplication("rotulo_faturavel");
        } else if (RepositoryComponentDefault.NAO.equals(getTipoProcedimento())) {
            return Bundle.getStringApplication("rotulo_nao_faturavel");
        }
        return Bundle.getStringApplication("rotulo_ambos");
    }

    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }
    @DescricaoParametro("rotulo_municipio")
    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }


    @DescricaoParametro("rotulo_unidade_origem")
    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    @DescricaoParametro("rotulo_tipo_arquivo")
    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }
}