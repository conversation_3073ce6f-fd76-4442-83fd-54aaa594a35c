/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDemonstrativoProdutoDTOParam implements Serializable {

    private List<Empresa> empresas;
    private List<Produto> produtos;

    @DescricaoParametro("rotulo_empresa")
    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_produto")
    public List<Produto> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<Produto> produtos) {
        this.produtos = produtos;
    }

}
