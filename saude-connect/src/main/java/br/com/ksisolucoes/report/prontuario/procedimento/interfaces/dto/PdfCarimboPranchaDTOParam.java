package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class PdfCarimboPranchaDTOParam implements Serializable {

    private Long codigo;
    private boolean preVisualizar;
    private String urlQRcode;
    private String chaveQrcode;
    private TipoSolicitacao.TipoDocumento tipoDocumento;
    private RequerimentosProjetosEnums.TipoAprovacao tipoAprovacao;

    public String getUrlQRcode() {
        return new StringBuilder().append(urlQRcode).append("?CHQRC=").append(getChaveQrcode()).toString();
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public boolean isPreVisualizar() {
        return preVisualizar;
    }

    public void setPreVisualizar(boolean preVisualizar) {
        this.preVisualizar = preVisualizar;
    }

    public void setUrlQRcode(String urlQRcode) {
        this.urlQRcode = urlQRcode;
    }

    public String getChaveQrcode() {
        return chaveQrcode;
    }

    public void setChaveQrcode(String chaveQrcode) {
        this.chaveQrcode = chaveQrcode;
    }

    public TipoSolicitacao.TipoDocumento getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(TipoSolicitacao.TipoDocumento tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public RequerimentosProjetosEnums.TipoAprovacao getTipoAprovacao() {
        return tipoAprovacao;
    }

    public void setTipoAprovacao(RequerimentosProjetosEnums.TipoAprovacao tipoAprovacao) {
        this.tipoAprovacao = tipoAprovacao;
    }

}