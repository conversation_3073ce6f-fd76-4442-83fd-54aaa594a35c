/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaExameProcedimentoDTOParam implements Serializable {

    private TipoExame tipoExame;
    private String descricaoProcedimento;
    private Long situacao;
    private Long apenasApac;
    private String propSort;
    private boolean ascending;

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public String getDescricaoProcedimento() {
        return descricaoProcedimento;
    }

    public void setDescricaoProcedimento(String descricaoProcedimento) {
        this.descricaoProcedimento = descricaoProcedimento;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public Long getApenasApac() {
        return apenasApac;
    }

    public void setApenasApac(Long apenasApac) {
        this.apenasApac = apenasApac;
    }
}
