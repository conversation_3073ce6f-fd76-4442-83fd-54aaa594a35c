package br.com.ksisolucoes.agendamento.dto;

import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoAtendimento;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by su<PERSON><PERSON> on 30/01/19.
 */
public class ConsultaAgendamentosDTOParam implements Serializable {

    private AgendaGradeAtendimento agendaGradeAtendimento;
    private TipoProcedimentoAtendimento tipoProcedimentoAtendimento;
    private String nomePaciente;

    public AgendaGradeAtendimento getAgendaGradeAtendimento() {
        return agendaGradeAtendimento;
    }

    public void setAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaGradeAtendimento = agendaGradeAtendimento;
    }

    public TipoProcedimentoAtendimento getTipoProcedimentoAtendimento() {
        return tipoProcedimentoAtendimento;
    }

    public void setTipoProcedimentoAtendimento(TipoProcedimentoAtendimento tipoProcedimentoAtendimento) {
        this.tipoProcedimentoAtendimento = tipoProcedimentoAtendimento;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        ConsultaAgendamentosDTOParam that = (ConsultaAgendamentosDTOParam) obj;
        return Objects.equals(agendaGradeAtendimento, that.agendaGradeAtendimento) &&
                Objects.equals(tipoProcedimentoAtendimento, that.tipoProcedimentoAtendimento) &&
                Objects.equals(nomePaciente, that.nomePaciente);
    }

    @Override
    public int hashCode() {
        return Objects.hash(agendaGradeAtendimento, tipoProcedimentoAtendimento, nomePaciente);
    }
}
