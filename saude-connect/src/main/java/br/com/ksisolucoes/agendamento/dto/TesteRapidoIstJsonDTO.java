package br.com.ksisolucoes.agendamento.dto;

public class TesteRapidoIstJsonDTO {

    Long [] QuaisMotivosProcura;
    Long [] PraticaSexual;
    Long [] SintomasHivUltimosSeisMeses;

    public TesteRapidoIstJsonDTO(Long[] quaisMotivosProcura, Long[] praticaSexual, Long[] sintomasHivUltimosSeisMeses) {
        QuaisMotivosProcura = quaisMotivosProcura;
        PraticaSexual = praticaSexual;
        SintomasHivUltimosSeisMeses = sintomasHivUltimosSeisMeses;
    }
    public TesteRapidoIstJsonDTO(){

    }

    public Long[] getQuaisMotivosProcura() {
        return QuaisMotivosProcura;
    }

    public void setQuaisMotivosProcura(Long[] quaisMotivosProcura) {
        QuaisMotivosProcura = quaisMotivosProcura;
    }

    public Long[] getPraticaSexual() {
        return PraticaSexual;
    }

    public void setPraticaSexual(Long[] praticaSexual) {
        PraticaSexual = praticaSexual;
    }

    public Long[] getSintomasHivUltimosSeisMeses() {
        return SintomasHivUltimosSeisMeses;
    }

    public void setSintomasHivUltimosSeisMeses(Long[] sintomasHivUltimosSeisMeses) {
        SintomasHivUltimosSeisMeses = sintomasHivUltimosSeisMeses;
    }
}
