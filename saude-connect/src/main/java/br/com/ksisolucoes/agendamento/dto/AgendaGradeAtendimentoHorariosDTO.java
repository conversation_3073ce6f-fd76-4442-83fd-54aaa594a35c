package br.com.ksisolucoes.agendamento.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeExame;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AgendaGradeAtendimentoHorariosDTO implements Serializable {

    private static final long serialVersionUID = -3432671531350659705L;

    private AgendaGradeAtendimento agendaGradeAtendimento;
    private List<AgendaGradeHorario> agendaGradeHorarioList;
    private List<AgendaGradeHorario> agendaGradeHorarioToRemoveList;
    private List<AgendaGradeHorario> agendaGradeHorarioToSaveList;
    private List<AgendaGradeExame> agendaGradeExameList;
    private CopiaAgendaDTO copiaBase;
    private boolean copia;
    private Long idPrestador;
    private Long idTipoProcedimnto;

    private Long totalVagasAgendadas;

    public AgendaGradeAtendimento getAgendaGradeAtendimento() {
        return agendaGradeAtendimento;
    }

    public void setAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaGradeAtendimento = agendaGradeAtendimento;
    }

    public List<AgendaGradeHorario> getAgendaGradeHorarioList() {
        return agendaGradeHorarioList;
    }

    public void setAgendaGradeHorarioList(List<AgendaGradeHorario> agendaGradeHorarioList) {
        this.agendaGradeHorarioList = agendaGradeHorarioList;
    }

    public List<AgendaGradeHorario> getAgendaGradeHorarioToRemoveList() {
        return agendaGradeHorarioToRemoveList;
    }

    public void setAgendaGradeHorarioToRemoveList(List<AgendaGradeHorario> agendaGradeHorarioToRemoveList) {
        this.agendaGradeHorarioToRemoveList = agendaGradeHorarioToRemoveList;
    }

    public List<AgendaGradeHorario> getAgendaGradeHorarioToSaveList() {
        return agendaGradeHorarioToSaveList;
    }

    public void setAgendaGradeHorarioToSaveList(List<AgendaGradeHorario> agendaGradeHorarioToSaveList) {
        this.agendaGradeHorarioToSaveList = agendaGradeHorarioToSaveList;
    }

    public CopiaAgendaDTO getCopiaBase() {
        return copiaBase;
    }

    public void setCopiaBase(CopiaAgendaDTO copiaBase) {
        this.copiaBase = copiaBase;
    }

    public boolean isCopia() {
        return copia;
    }

    public void setCopia(boolean copia) {
        this.copia = copia;
    }

    public List<AgendaGradeExame> getAgendaGradeExameList() {
        if (agendaGradeExameList == null) {
            agendaGradeExameList = new ArrayList<>();
        }
        return agendaGradeExameList;
    }

    public void setAgendaGradeExameList(List<AgendaGradeExame> agendaGradeExameList) {
        this.agendaGradeExameList = agendaGradeExameList;
    }

    public String getDescricaoPossuiExames() {
        if (CollectionUtils.isNotNullEmpty(getAgendaGradeExameList())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public Long getTotalAgendadas() {
        return new Long(Lambda.select(getAgendaGradeHorarioList(),
                Lambda.having(Lambda.on(AgendaGradeHorario.class).getStatus(), Matchers.equalTo(AgendaGradeHorario.Status.AGENDADO.value()))).size());
    }

    public Long getIdPrestador() {
        return idPrestador;
    }

    public void setIdPrestador(Long idPrestador) {
        this.idPrestador = idPrestador;
    }

    public Long getIdTipoProcedimnto() {
        return idTipoProcedimnto;
    }

    public void setIdTipoProcedimnto(Long idTipoProcedimnto) {
        this.idTipoProcedimnto = idTipoProcedimnto;
    }

    public Long getTotalVagasAgendadas() {
        return totalVagasAgendadas;
    }

    public void setTotalVagasAgendadas(Long totalVagasAgendadas) {
        this.totalVagasAgendadas = totalVagasAgendadas;
    }
}
