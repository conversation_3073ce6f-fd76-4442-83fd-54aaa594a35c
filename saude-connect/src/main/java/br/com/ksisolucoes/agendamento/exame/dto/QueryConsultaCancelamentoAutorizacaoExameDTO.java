/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaCancelamentoAutorizacaoExameDTO implements Serializable {

    public static final String PROP_CODIGO_EXAME = "codigoExame";
    public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
    public static final String PROP_NOME_PACIENTE = "nomePaciente";
    public static final String PROP_DATA_SOLICITACAO = "dataSolicitacao";
    public static final String PROP_TIPO_EXAME = "tipoExame";
    public static final String PROP_DATA_AUTORIZACAO = "dataAutorizacao";
    public static final String PROP_USUARIO_AUTORIZACAO = "usuarioAutorizacao";
    public static final String PROP_EMPRESA_SOLICITANTE="empresaSolicitante";

    private Long codigoExame;
    private String nomePaciente;
    private Date dataSolicitacao;
    private TipoExame tipoExame;
    private Date dataAutorizacao;
    private Usuario usuarioAutorizacao;
    private Empresa empresaSolicitante;
    private Atendimento atendimento;
    private UsuarioCadsus usuarioCadsus;

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Empresa getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }
    
    public Long getCodigoExame() {
        return codigoExame;
    }

    public void setCodigoExame(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public Date getDataSolicitacao() {
        return dataSolicitacao;
    }

    public void setDataSolicitacao(Date dataSolicitacao) {
        this.dataSolicitacao = dataSolicitacao;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Usuario getUsuarioAutorizacao() {
        return usuarioAutorizacao;
    }

    public void setUsuarioAutorizacao(Usuario usuarioAutorizacao) {
        this.usuarioAutorizacao = usuarioAutorizacao;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
}
