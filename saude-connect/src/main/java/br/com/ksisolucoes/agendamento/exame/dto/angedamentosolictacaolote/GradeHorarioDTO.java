package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeHorarioDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;

import java.util.Date;

public class GradeHorarioDTO implements AgendaGradeHorarioDTO {

    private static final long serialVersionUID = -2073097373098948195L;

    private Long codigo;
    private Date horario;
    private AgendaGradeHorario.Status status;

    public Long getCodigo() {
        return codigo;
    }

    public GradeHorarioDTO setCodigo(Long codigo) {
        this.codigo = codigo;
        return this;
    }

    public Date getHorario() {
        return horario;
    }

    public GradeHorarioDTO setHorario(Date horario) {
        this.horario = horario;
        return this;
    }

    public AgendaGradeHorario.Status getStatus() {
        return status;
    }

    public GradeHorarioDTO setStatus(AgendaGradeHorario.Status status) {
        this.status = status;
        return this;
    }

    @Override
    public boolean isStatusPendente() {
        return AgendaGradeHorario.Status.PENDENTE.equals(status);
    }
}
