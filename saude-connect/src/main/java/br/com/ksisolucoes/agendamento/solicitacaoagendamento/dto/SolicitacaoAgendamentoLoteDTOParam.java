package br.com.ksisolucoes.agendamento.solicitacaoagendamento.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class SolicitacaoAgendamentoLoteDTOParam implements Serializable {

    private DatePeriod datePeriod;
    private Profissional profissional;
    private TipoProcedimento tipoProcedimento;
    private String nomePaciente;

    public DatePeriod getDatePeriod() {
        return datePeriod;
    }

    public void setDatePeriod(DatePeriod datePeriod) {
        this.datePeriod = datePeriod;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }
}
