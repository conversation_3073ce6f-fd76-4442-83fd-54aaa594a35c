/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaExamePrestadorCompetenciaDTOParam implements Serializable{

    private List<Long> codigoExameProcedimentoList;
    private Date dataCompetencia;
    private Double saldoMinimo;
    private Long codigoExamePrestadorCompetencia;
    private Empresa prestador;

    public Long getCodigoExamePrestadorCompetencia() {
        return codigoExamePrestadorCompetencia;
    }

    public void setCodigoExamePrestadorCompetencia(Long codigoExamePrestadorCompetencia) {
        this.codigoExamePrestadorCompetencia = codigoExamePrestadorCompetencia;
    }

    public List<Long> getCodigoExameProcedimentoList() {
        return codigoExameProcedimentoList;
    }

    public void setCodigoExameProcedimentoList(List<Long> codigoExameProcedimentoList) {
        this.codigoExameProcedimentoList = codigoExameProcedimentoList;
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public Double getSaldoMinimo() {
        return saldoMinimo;
    }

    public void setSaldoMinimo(Double saldoMinimo) {
        this.saldoMinimo = saldoMinimo;
    }

    public Empresa getPrestador() {
        return prestador;
    }

    public void setPrestador(Empresa prestador) {
        this.prestador = prestador;
    }
}
