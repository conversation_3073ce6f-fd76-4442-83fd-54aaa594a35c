package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class AutorizacaoExameSusDTOParam implements Serializable {
    
    private TipoExame tipoExame;
    private Double cotaUnidade = 0D;
    private Double cotaUtilizada;
    private Double saldo;
    private Double totalExame = 0D;

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Double getCotaUnidade() {
        return cotaUnidade;
    }

    public void setCotaUnidade(Double cotaUnidade) {
        this.cotaUnidade = cotaUnidade;
    }

    public Double getCotaUtilizada() {
        return cotaUtilizada;
    }

    public void setCotaUtilizada(Double cotaUtilizada) {
        this.cotaUtilizada = cotaUtilizada;
    }

    public Double getSaldo() {
        return saldo;
    }

    public void setSaldo(Double saldo) {
        this.saldo = saldo;
    }

    public Double getTotalExame() {
        return totalExame;
    }

    public void setTotalExame(Double totalExame) {
        this.totalExame = totalExame;
    }
    
}
