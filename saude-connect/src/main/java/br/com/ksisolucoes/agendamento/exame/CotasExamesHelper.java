package br.com.ksisolucoes.agendamento.exame;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.exame.ExameProfissionalCompetencia;
import br.com.ksisolucoes.vo.exame.ExameProfissionalSemana;
import br.com.ksisolucoes.vo.prontuario.basico.ExameCotaPpi;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 *
 * <AUTHOR>
 */
public class CotasExamesHelper {

    public static boolean isTipoTetoFisico(TipoExame tipoExame) {
        return LoadManager.getInstance(ExameCotaPpi.class)
                .addProperty(ExameCotaPpi.PROP_TIPO_TETO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), tipoExame.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameCotaPpi.PROP_TIPO_TETO, ExameCotaPpi.TipoTeto.FISICO.value()))
                .exists();
    }

    public static Double saldoInicioAnoOuSaldoAnterior(Double tetoSemanal, ExameProfissionalCompetencia exameProfissionalCompetencia, boolean saldoCompetencia) {
        Calendar primeiroDiaAno = new GregorianCalendar();
        primeiroDiaAno.set(Calendar.YEAR, DataUtil.getAno());
        primeiroDiaAno.set(Calendar.MONTH, Calendar.JANUARY);
        primeiroDiaAno.set(Calendar.DAY_OF_MONTH, 1);

        Calendar dataAux = new GregorianCalendar();
        dataAux.setTime(DataUtil.getDataAtual());
        int semanaAtual = dataAux.get(Calendar.WEEK_OF_YEAR);
        int semanaAux = dataAux.get(Calendar.WEEK_OF_YEAR);
        int diaSemana = dataAux.get(Calendar.DAY_OF_WEEK);
        int diaMes = dataAux.get(Calendar.DAY_OF_MONTH);
        boolean isDecember = (DataUtil.getMes(DataUtil.getDataAtual()) - 1 == Calendar.DECEMBER);

        Calendar aux = new GregorianCalendar();

        //Para calcular corretamente quando final do ano, a última semana do ano pode ser a semana 1
        if (semanaAtual == 1 && isDecember) {
            Date dataAtual = Data.addDias(DataUtil.getDataAtual(), -7);
            aux.setTime(dataAtual);
            semanaAtual = aux.get(Calendar.WEEK_OF_YEAR) + 1;
        }

        ExameProfissionalSemana exameProfissionalSemanaAnterior = LoadManager.getInstance(ExameProfissionalSemana.class)
                .addProperties(new HQLProperties(ExameProfissionalSemana.class).getProperties())
                .addProperties(new HQLProperties(ExameProfissionalCompetencia.class, ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_PROFISSIONAL), exameProfissionalCompetencia.getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_EMPRESA), exameProfissionalCompetencia.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_TIPO_EXAME), exameProfissionalCompetencia.getTipoExame()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_DATA_COMPETENCIA), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, primeiroDiaAno.getTime()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameProfissionalSemana.PROP_SEMANA, ExameProfissionalCompetencia.PROP_CODIGO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .setMaxResults(1).start().getVO();

        if (exameProfissionalSemanaAnterior == null && semanaAux == 1 && isDecember) {
            primeiroDiaAno = new GregorianCalendar();
            primeiroDiaAno.set(Calendar.YEAR, aux.get(Calendar.YEAR));
            primeiroDiaAno.set(Calendar.MONTH, Calendar.JANUARY);
            primeiroDiaAno.set(Calendar.DAY_OF_MONTH, 1);

            exameProfissionalSemanaAnterior = LoadManager.getInstance(ExameProfissionalSemana.class)
                    .addProperties(new HQLProperties(ExameProfissionalSemana.class).getProperties())
                    .addProperties(new HQLProperties(ExameProfissionalCompetencia.class, ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_PROFISSIONAL), exameProfissionalCompetencia.getProfissional()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_EMPRESA), exameProfissionalCompetencia.getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_TIPO_EXAME), exameProfissionalCompetencia.getTipoExame()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA, ExameProfissionalCompetencia.PROP_DATA_COMPETENCIA), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, primeiroDiaAno.getTime()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameProfissionalSemana.PROP_SEMANA, ExameProfissionalCompetencia.PROP_CODIGO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();
        }
        tetoSemanal = Coalesce.asDouble(tetoSemanal);
        Double saldoSemana = tetoSemanal;
        if (exameProfissionalSemanaAnterior != null) {
            dataAux.setTime(exameProfissionalSemanaAnterior.getExameProfissionalCompetencia().getDataCompetencia());
            // -1 para descontar primeira semana, já incluso no dataAux.get(Calendar.WEEK_OF_YEAR).
            int semanaAnterior = dataAux.get(Calendar.WEEK_OF_YEAR) + exameProfissionalSemanaAnterior.getSemana().intValue() - 1;
            int diferencaSemanas = semanaAtual - semanaAnterior;

            if ((1 == semanaAtual && diaSemana == 1) || (semanaAtual == 2 && diaMes != 1)) {
                if (saldoCompetencia) {
                    return 0D;
                }
                return tetoSemanal;
            }
            if (diferencaSemanas == 0) {
                return exameProfissionalSemanaAnterior.getSaldo();
            } else {
                return new Dinheiro(tetoSemanal).multiplicar(diferencaSemanas).somar(exameProfissionalSemanaAnterior.getSaldo()).doubleValue();
            }
        }
        return Coalesce.asDouble(saldoSemana);
    }
}
