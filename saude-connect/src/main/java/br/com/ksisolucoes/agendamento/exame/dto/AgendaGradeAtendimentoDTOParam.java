/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.celk.agendamento.agendamentofilaespera.DiasLimiteAgendamento;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.agendamento.AgendaCota;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeExame;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AgendaGradeAtendimentoDTOParam implements Serializable {

    private Profissional profissional;
    private Profissional profissionalVagaInterna;
    private TipoProcedimento tipoProcedimento;
    private List<Long> tipoProcedimentoClassificacaoList;
    private DatePeriod datePeriod;
    private Long situacao;
    private Long status;
    private Empresa empresa;
    private Empresa empresaOrigem;
    private Empresa empresaAgenda;
    private Empresa localAgendamento;
    private Empresa unidadeReferencia;
    private List<Long> localAgendamentoList;
    private List<Long> empresas;
    private boolean validarEmpresaOrigem = true;
    private UsuarioCadsus usuarioCadsus;
    private Usuario usuario;
    private AgendaGradeAtendimento agendaExameGradeAtendimento;
    private AgendaGradeAtendimento notAgendaGradeAtendimento;
    private boolean apenasAgendasComVagas;
    private boolean adjustRangeHour;
    private boolean apenasAgendasComEncaminhamentosEmAberto;
    private Long vagasNecessarias;
    private boolean exibirCancelados = false;
    private boolean validarUnidadeInformatizada = false;
    private boolean semAgendamento = false;
    private boolean apenasComSaldoVagasPpi = false;
    private List<Long> tipoAtendimentoAgendaList;
    private String nomeUsuarioCadsus;
    private String tipoOrdenacao;
    private String tipoAgenda;
    private Date dataMinimaAgendamento;
    private String agendamentoAutomatico;
    private TipoData tipoData;
    private boolean validaSemConfirmacaoPaciente;
    private boolean validaDataReabertura;
    private boolean validaTipoAgenda = true;
    private boolean somenteRegistroSolicitacao = false;
    private boolean somenteRegistroSemAgendaGradeAtendimento = false;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private boolean permission = false;
    private List<Long> tipoAgendamentoList;
    private List<Integer> diaDaSemanaList;
    private OperadorValor<List<Empresa>> estabelecimento;
    private Date dataAgendasRemanejamento;
    private AgendaCota agendaCota;
    private Long filtroConfirmacaoPresenca;
    private Long codigoAgenda;
    private Long codigoPaciente;
    private String campoOrdenacao;
    private String referencia;
    private String regulado;
    private Date dataNascimento;
    private List<Profissional> profissionalList;
    private boolean aguardaProcedimento = false;
    private boolean visualizaAtendimentoAtencaoBasica = false;
    private boolean apenasUmDiaAgenda;
    private List<TipoProcedimento.TipoAgenda> tipoAgendaList;
    private List<ExameProcedimento> exameProcedimentoList;
    private boolean somenteVagaInterna;
    private boolean customProcess = true;
    private List<AgendaGradeAtendimentoDTO> listaDiasDisponiveisAux;
    private List<AgendaGradeExame> agendaExamesSolicitados;
    private DiasLimiteAgendamento diasLimiteAgendamentoListasEspera;
    private boolean permiteEncaixarPaciente;
    private Long cns;

    private boolean fazerSelectNumeroProtocolo = false;

    private DTOParamConfigureDefault configureParam;

    private boolean validarExamesDaSolicitacao = true;

    private Long numeroSolicitacao;

    public DTOParamConfigureDefault getConfigureParam() {
        if (configureParam == null) {
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public enum GroupBy {
        EMPRESA,
        PROFISSIONAL
    }


    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }


    public String getRegulado() {
        return regulado;
    }

    public void setRegulado(String regulado) {
        this.regulado = regulado;
    }

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public List<Long> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Long> empresas) {
        this.empresas = empresas;
    }

    private String flagTfd;

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getFlagTfd() {
        return flagTfd;
    }

    public void setFlagTfd(String flagTfd) {
        this.flagTfd = flagTfd;
    }

    public String getAgendamentoAutomatico() {
        return agendamentoAutomatico;
    }

    public void setAgendamentoAutomatico(String agendamentoAutomatico) {
        this.agendamentoAutomatico = agendamentoAutomatico;
    }

    public Date getDataMinimaAgendamento() {
        return dataMinimaAgendamento;
    }

    public void setDataMinimaAgendamento(Date dataMinimaAgendamento) {
        this.dataMinimaAgendamento = dataMinimaAgendamento;
    }

    public List<Long> getTipoProcedimentoClassificacaoList() {
        return tipoProcedimentoClassificacaoList;
    }

    public void setTipoProcedimentoClassificacaoList(List<Long> tipoProcedimentoClassificacaoList) {
        this.tipoProcedimentoClassificacaoList = tipoProcedimentoClassificacaoList;
    }

    public String getTipoAgenda() {
        return tipoAgenda;
    }

    public void setTipoAgenda(String tipoAgenda) {
        this.tipoAgenda = tipoAgenda;
    }

    public List<Long> getTipoAtendimentoAgendaList() {
        return tipoAtendimentoAgendaList;
    }

    public void setTipoAtendimentoAgendaList(List<Long> tipoAtendimentoAgendaList) {
        this.tipoAtendimentoAgendaList = tipoAtendimentoAgendaList;
    }

    public boolean isApenasComSaldoVagasPpi() {
        return apenasComSaldoVagasPpi;
    }

    public void setApenasComSaldoVagasPpi(boolean apenasComSaldoVagasPpi) {
        this.apenasComSaldoVagasPpi = apenasComSaldoVagasPpi;
    }

    public boolean isSemAgendamento() {
        return semAgendamento;
    }

    public void setSemAgendamento(boolean semAgendamento) {
        this.semAgendamento = semAgendamento;
    }

    public boolean isValidarUnidadeInformatizada() {
        return validarUnidadeInformatizada;
    }

    public void setValidarUnidadeInformatizada(boolean validarUnidadeInformatizada) {
        this.validarUnidadeInformatizada = validarUnidadeInformatizada;
    }

    public boolean isExibirCancelados() {
        return exibirCancelados;
    }

    public void setExibirCancelados(boolean exibirCancelados) {
        this.exibirCancelados = exibirCancelados;
    }

    public boolean isValidarEmpresaOrigem() {
        return validarEmpresaOrigem;
    }

    public void setValidarEmpresaOrigem(boolean validarEmpresaOrigem) {
        this.validarEmpresaOrigem = validarEmpresaOrigem;
    }

    public boolean isApenasAgendasComEncaminhamentosEmAberto() {
        return apenasAgendasComEncaminhamentosEmAberto;
    }

    public void setApenasAgendasComEncaminhamentosEmAberto(boolean apenasAgendasComEncaminhamentosEmAberto) {
        this.apenasAgendasComEncaminhamentosEmAberto = apenasAgendasComEncaminhamentosEmAberto;
    }

    public String getNomeUsuarioCadsus() {
        return nomeUsuarioCadsus;
    }

    public void setNomeUsuarioCadsus(String nomeUsuarioCadsus) {
        this.nomeUsuarioCadsus = nomeUsuarioCadsus;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    public boolean isApenasAgendasComVagas() {
        return apenasAgendasComVagas;
    }

    public void setApenasAgendasComVagas(boolean apenasAgendasComVagas) {
        this.apenasAgendasComVagas = apenasAgendasComVagas;
    }

    public AgendaGradeAtendimento getAgendaGradeAtendimento() {
        return agendaExameGradeAtendimento;
    }

    public void setAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaExameGradeAtendimento = agendaGradeAtendimento;
    }

    public AgendaGradeAtendimento getNotAgendaGradeAtendimento() {
        return notAgendaGradeAtendimento;
    }

    public void setNotAgendaGradeAtendimento(AgendaGradeAtendimento notAgendaGradeAtendimento) {
        this.notAgendaGradeAtendimento = notAgendaGradeAtendimento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Empresa getEmpresaOrigem() {
        return empresaOrigem;
    }

    public void setEmpresaOrigem(Empresa empresaOrigem) {
        this.empresaOrigem = empresaOrigem;
    }

    public Empresa getLocalAgendamento() {
        return localAgendamento;
    }

    public void setLocalAgendamento(Empresa localAgendamento) {
        this.localAgendamento = localAgendamento;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public DatePeriod getDatePeriod() {
        return datePeriod;
    }

    public void setDatePeriod(DatePeriod datePeriod) {
        this.datePeriod = datePeriod;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }


    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public void setTipoProcedimento(TipoProcedimento tipoExame) {
        this.tipoProcedimento = tipoExame;
    }

    public Long getVagasNecessarias() {
        return Coalesce.asLong(vagasNecessarias, 1L);
    }

    public void setVagasNecessarias(Long vagasNecessarias) {
        this.vagasNecessarias = vagasNecessarias;
    }

    public boolean isAdjustRangeHour() {
        return adjustRangeHour;
    }

    public void setAdjustRangeHour(boolean adjustRangeHour) {
        this.adjustRangeHour = adjustRangeHour;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public boolean isValidaSemConfirmacaoPaciente() {
        return validaSemConfirmacaoPaciente;
    }

    public void setValidaSemConfirmacaoPaciente(boolean validaSemConfirmacaoPaciente) {
        this.validaSemConfirmacaoPaciente = validaSemConfirmacaoPaciente;
    }

    public boolean isSomenteRegistroSolicitacao() {
        return somenteRegistroSolicitacao;
    }

    public void setSomenteRegistroSolicitacao(boolean somenteRegistroSolicitacao) {
        this.somenteRegistroSolicitacao = somenteRegistroSolicitacao;
    }

    public boolean isSomenteRegistroSemAgendaGradeAtendimento() {
        return somenteRegistroSemAgendaGradeAtendimento;
    }

    public void setSomenteRegistroSemAgendaGradeAtendimento(boolean somenteRegistroSemAgendaGradeAtendimento) {
        this.somenteRegistroSemAgendaGradeAtendimento = somenteRegistroSemAgendaGradeAtendimento;
    }

    public TipoData getTipoData() {
        return tipoData;
    }

    public void setTipoData(TipoData tipoData) {
        this.tipoData = tipoData;
    }

    public enum TipoData {

        DATA_AGENDAMENTO, DATA_SOLICITACAO, DATA_CADASTRO
    }

    public boolean isValidaDataReabertura() {
        return validaDataReabertura;
    }

    public void setValidaDataReabertura(boolean validaDataReabertura) {
        this.validaDataReabertura = validaDataReabertura;
    }

    public SolicitacaoAgendamento getSolicitacaoAgendamento() {
        return solicitacaoAgendamento;
    }

    public void setSolicitacaoAgendamento(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    public boolean isPermission() {
        return permission;
    }

    public void setPermission(boolean permission) {
        this.permission = permission;
    }

    public List<Long> getTipoAgendamentoList() {
        return tipoAgendamentoList;
    }

    public void setTipoAgendamentoList(List<Long> tipoAgendamentoList) {
        this.tipoAgendamentoList = tipoAgendamentoList;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public OperadorValor<List<Empresa>> getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(OperadorValor<List<Empresa>> estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public Date getDataAgendasRemanejamento() {
        return dataAgendasRemanejamento;
    }

    public void setDataAgendasRemanejamento(Date dataAgendasRemanejamento) {
        this.dataAgendasRemanejamento = dataAgendasRemanejamento;
    }

    public AgendaGradeAtendimento getAgendaExameGradeAtendimento() {
        return agendaExameGradeAtendimento;
    }

    public void setAgendaExameGradeAtendimento(AgendaGradeAtendimento agendaExameGradeAtendimento) {
        this.agendaExameGradeAtendimento = agendaExameGradeAtendimento;
    }

    public List<Integer> getDiaDaSemanaList() {
        return diaDaSemanaList;
    }

    public void setDiaDaSemanaList(List<Integer> diaDaSemanaList) {
        this.diaDaSemanaList = diaDaSemanaList;
    }

    public AgendaCota getAgendaCota() {
        return agendaCota;
    }

    public void setAgendaCota(AgendaCota agendaCota) {
        this.agendaCota = agendaCota;
    }

    public Long getFiltroConfirmacaoPresenca() {
        return filtroConfirmacaoPresenca;
    }

    public void setFiltroConfirmacaoPresenca(Long filtroConfirmacaoPresenca) {
        this.filtroConfirmacaoPresenca = filtroConfirmacaoPresenca;
    }

    public Long getCodigoAgenda() {
        return codigoAgenda;
    }

    public void setCodigoAgenda(Long codigoAgenda) {
        this.codigoAgenda = codigoAgenda;
    }

    public Empresa getEmpresaAgenda() {
        return empresaAgenda;
    }

    public void setEmpresaAgenda(Empresa empresaAgenda) {
        this.empresaAgenda = empresaAgenda;
    }

    public boolean isFazerSelectNumeroProtocolo() {
        return fazerSelectNumeroProtocolo;
    }

    public void setFazerSelectNumeroProtocolo(boolean fazerSelectNumeroProtocolo) {
        this.fazerSelectNumeroProtocolo = fazerSelectNumeroProtocolo;
    }

    public List<Long> getLocalAgendamentoList() {
        return localAgendamentoList;
    }

    public void setLocalAgendamentoList(List<Long> localAgendamentoList) {
        this.localAgendamentoList = localAgendamentoList;
    }

    public boolean isValidaTipoAgenda() {
        return validaTipoAgenda;
    }

    public void setValidaTipoAgenda(boolean validaTipoAgenda) {
        this.validaTipoAgenda = validaTipoAgenda;
    }

    public List<Profissional> getProfissionalList() {
        return profissionalList;
    }

    public void setProfissionalList(List<Profissional> profissionalList) {
        this.profissionalList = profissionalList;
    }

    public boolean isAguardaProcedimento() {
        return aguardaProcedimento;
    }

    public void setAguardaProcedimento(boolean aguardaProcedimento) {
        this.aguardaProcedimento = aguardaProcedimento;
    }

    public boolean isVisualizaAtendimentoAtencaoBasica() {
        return visualizaAtendimentoAtencaoBasica;
    }

    public void setVisualizaAtendimentoAtencaoBasica(boolean visualizaAtendimentoAtencaoBasica) {
        this.visualizaAtendimentoAtencaoBasica = visualizaAtendimentoAtencaoBasica;
    }

    public boolean isApenasUmDiaAgenda() {
        return apenasUmDiaAgenda;
    }

    public void setApenasUmDiaAgenda(boolean apenasUmDiaAgenda) {
        this.apenasUmDiaAgenda = apenasUmDiaAgenda;
    }

    public List<TipoProcedimento.TipoAgenda> getTipoAgendaList() {
        return tipoAgendaList;
    }

    public void setTipoAgendaList(List<TipoProcedimento.TipoAgenda> tipoAgendaList) {
        this.tipoAgendaList = tipoAgendaList;
    }

    public Profissional getProfissionalVagaInterna() {
        return profissionalVagaInterna;
    }

    public void setProfissionalVagaInterna(Profissional profissionalVagaInterna) {
        this.profissionalVagaInterna = profissionalVagaInterna;
    }

    public List<ExameProcedimento> getExameProcedimentoList() {
        if(CollectionUtils.isAllEmpty(exameProcedimentoList)){
            exameProcedimentoList = new ArrayList<>();
        }
        return exameProcedimentoList != null ? exameProcedimentoList : new ArrayList<ExameProcedimento>();
    }

    public void setExameProcedimentoList(List<ExameProcedimento> exameProcedimentoList) {
        this.exameProcedimentoList = exameProcedimentoList;
    }

    public boolean isSomenteVagaInterna() {
        return somenteVagaInterna;
    }

    public void setSomenteVagaInterna(boolean somenteVagaInterna) {
        this.somenteVagaInterna = somenteVagaInterna;
    }

    public boolean isCustomProcess() {
        return customProcess;
    }

    public void setCustomProcess(boolean customProcess) {
        this.customProcess = customProcess;
    }

    public List<AgendaGradeAtendimentoDTO> getListaDiasDisponiveisAux() {
        return listaDiasDisponiveisAux;
    }

    public void setListaDiasDisponiveisAux(List<AgendaGradeAtendimentoDTO> listaDiasDisponiveisAux) {
        this.listaDiasDisponiveisAux = listaDiasDisponiveisAux;
    }

    public List<AgendaGradeExame> getAgendaExamesSolicitados() {
        return agendaExamesSolicitados;
    }

    public void setAgendaExamesSolicitados(List<AgendaGradeExame> agendaExamesSolicitados) {
        this.agendaExamesSolicitados = agendaExamesSolicitados;
    }

    public boolean solicitacaoPossuiExames() {
        return CollectionUtils.isNotNullEmpty(this.exameProcedimentoList);
    }

    public boolean possuiAgendasParaExamesSolicitados() {
        return CollectionUtils.isNotNullEmpty(this.agendaExamesSolicitados);
    }

    public boolean isValidarExamesDaSolicitacao() {
        return this.validarExamesDaSolicitacao;
    }

    public void setValidarExamesDaSolicitacao(boolean validarExamesDaSolicitacao) {
        this.validarExamesDaSolicitacao = validarExamesDaSolicitacao;
    }

    public DiasLimiteAgendamento getDiasLimiteAgendamentoListasEspera() {
        return diasLimiteAgendamentoListasEspera;
    }

    public void setDiasLimiteAgendamentoListasEspera(DiasLimiteAgendamento diasLimiteAgendamentoListasEspera) {
        this.diasLimiteAgendamentoListasEspera = diasLimiteAgendamentoListasEspera;
    }

    public int quantidadeDiasDisponiveisAux(){
        return this.listaDiasDisponiveisAux != null ? listaDiasDisponiveisAux.size() : 0;
    }

    public Long getNumeroSolicitacao() {
        return numeroSolicitacao;
    }

    public void setNumeroSolicitacao(Long numeroSolicitacao) {
        this.numeroSolicitacao = numeroSolicitacao;
    }

    public Empresa getUnidadeReferencia() {
        return unidadeReferencia;
    }

    public void setUnidadeReferencia(Empresa unidadeReferencia) {
        this.unidadeReferencia = unidadeReferencia;
    }

    public Long getCns() {
        return cns;
    }

    public void setCns(Long cns) {
        this.cns = cns;
    }

    public boolean isPermiteEncaixarPaciente() {
        return permiteEncaixarPaciente;
    }

    public void setPermiteEncaixarPaciente(boolean permiteEncaixarPaciente) {
        this.permiteEncaixarPaciente = permiteEncaixarPaciente;
    }
}
