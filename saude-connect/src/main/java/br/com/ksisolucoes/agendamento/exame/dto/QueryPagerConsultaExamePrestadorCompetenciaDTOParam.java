/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaExamePrestadorCompetenciaDTOParam implements Serializable {

    private Empresa unidade;
    private TipoExame tipoExame;
    private Date competencia;

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Date getCompetencia() {
        return competencia;
    }

    public void setCompetencia(Date competencia) {
        this.competencia = competencia;
    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

}
