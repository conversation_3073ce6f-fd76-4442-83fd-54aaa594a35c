/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.agendamento.cotas.dto;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DistribuirCotaUnidadeDTO implements Serializable {

    private List<Long> codigosAgendaGradeAtendimento;
    private Long codigoTipoProcedimento;
    private Long codigoUnidade;
    private Long numeroVagas;
    private Long numeroMaximoVagasDia;
    private String somaVagas;

    public List<Long> getCodigosAgendaGradeAtendimento() {
        return codigosAgendaGradeAtendimento;
    }

    public void setCodigosAgendaGradeAtendimento(List<Long> codigosAgendaGradeAtendimento) {
        this.codigosAgendaGradeAtendimento = codigosAgendaGradeAtendimento;
    }

    public Long getCodigoTipoProcedimento() {
        return codigoTipoProcedimento;
    }

    public void setCodigoTipoProcedimento(Long codigoTipoProcedimento) {
        this.codigoTipoProcedimento = codigoTipoProcedimento;
    }

    public Long getCodigoUnidade() {
        return codigoUnidade;
    }

    public void setCodigoUnidade(Long codigoUnidade) {
        this.codigoUnidade = codigoUnidade;
    }

    public Long getNumeroVagas() {
        return numeroVagas;
    }

    public void setNumeroVagas(Long numeroVagas) {
        this.numeroVagas = numeroVagas;
    }

    public Long getNumeroMaximoVagasDia() {
        return numeroMaximoVagasDia;
    }

    public void setNumeroMaximoVagasDia(Long numeroMaximoVagasDia) {
        this.numeroMaximoVagasDia = numeroMaximoVagasDia;
    }

    public String getSomaVagas() {
        return somaVagas;
    }

    public void setSomaVagas(String somaVagas) {
        this.somaVagas = somaVagas;
    }
    
}
