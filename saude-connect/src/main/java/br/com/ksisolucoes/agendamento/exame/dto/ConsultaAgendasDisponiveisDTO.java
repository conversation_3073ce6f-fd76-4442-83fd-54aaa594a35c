package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ConsultaAgendasDisponiveisDTO implements Serializable {

    public static final String PROP_AGENDA = "agenda";

    private Long codigo;
    private Empresa empresa;
    private Agenda agenda;
    private TipoProcedimento tipoProcedimento;
    private Profissional profissional;
    private Long totalVagas;
    private Long vagasAgendadas;
    private Long vagasDisponiveis;

    public Agenda getAgenda() {
        return agenda;
    }

    public void setAgenda(Agenda agenda) {
        this.agenda = agenda;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Long getTotalVagas() {
        return totalVagas;
    }

    public void setTotalVagas(Long totalVagas) {
        this.totalVagas = totalVagas;
    }

    public Long getVagasAgendadas() {
        return Coalesce.asLong(vagasAgendadas);
    }

    public void setVagasAgendadas(Long vagasAgendadas) {
        this.vagasAgendadas = vagasAgendadas;
    }

    public Long getVagasDisponiveis() {
        return Coalesce.asLong(vagasDisponiveis);
    }

    public void setVagasDisponiveis(Long vagasDisponiveis) {
        this.vagasDisponiveis = vagasDisponiveis;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Long getVagasTotalDisponivel(){
        return new Dinheiro(getVagasDisponiveis()).subtrair(getVagasAgendadas().doubleValue()).longValue();
    }
}
