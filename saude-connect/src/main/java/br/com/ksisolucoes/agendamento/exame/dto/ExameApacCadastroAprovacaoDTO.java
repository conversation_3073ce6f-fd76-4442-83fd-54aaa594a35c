/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameApac;
import br.com.ksisolucoes.vo.prontuario.basico.ExameApacInfosComplementaresMedicamentos;
import br.com.ksisolucoes.vo.prontuario.basico.ExameApacMedicamentos;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExameApacCadastroAprovacaoDTO implements Serializable {
    
    private ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO;
    private ExameApac exameApac;
    private List<ExameApacMedicamentos> exameApacMedicamentos = new ArrayList<>();
    private Long checkTodos;

    public List<ExameApacMedicamentos> getExameApacMedicamentos() {
        return exameApacMedicamentos;
    }

    public void setExameApacMedicamentos(List<ExameApacMedicamentos> exameApacMedicamentos) {
        this.exameApacMedicamentos = exameApacMedicamentos;
    }

    public ExameApac getExameApac() {
        return exameApac;
    }

    public void setExameApac(ExameApac exameApac) {
        this.exameApac = exameApac;
    }

    public ExameCadastroAprovacaoDTO getExameCadastroAprovacaoDTO() {
        return exameCadastroAprovacaoDTO;
    }

    public void setExameCadastroAprovacaoDTO(ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO) {
        this.exameCadastroAprovacaoDTO = exameCadastroAprovacaoDTO;
    }

    public Long getCheckTodos() {
        return checkTodos;
    }

    public void setCheckTodos(Long checkTodos) {
        this.checkTodos = checkTodos;
    }
}
