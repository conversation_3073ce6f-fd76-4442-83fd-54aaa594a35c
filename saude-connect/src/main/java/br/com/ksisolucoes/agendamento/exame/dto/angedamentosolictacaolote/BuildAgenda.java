package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import ch.lambdaj.group.Group;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.equalTo;

public class BuildAgenda {

    private final ParametrosBuildAgendaDTO parametrosBuildAgendaDTO;

    public BuildAgenda(ParametrosBuildAgendaDTO parametrosBuildAgendaDTO) {
        this.parametrosBuildAgendaDTO = parametrosBuildAgendaDTO;
    }

    public List<AgendaDTO> build() {
        List<AgendaDTO> agendas = new ArrayList<>();
        Group<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoAgrupado = group(parametrosBuildAgendaDTO.getAgendasGradeAtendimeto(), by(on(AgendaGradeAtendimentoDTO.class).getAgenda().getCodigo()));
        for (Group<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoSubgrupo : agendaGradeAtendimentoAgrupado.subgroups()) {
            List<AgendaGradeAtendimentoDTO> atendimentos = agendaGradeAtendimentoSubgrupo.findAll();
            AgendaDTO agenda = buildAgenda(atendimentos);
            agendas.add(agenda);
        }
        return agendas;
    }

    private AgendaDTO buildAgenda(List<AgendaGradeAtendimentoDTO> atendimentos) {
        AgendaGradeAtendimentoDTO atendimentoDTO = atendimentos.get(0);
        Agenda agenda = atendimentoDTO.getAgenda();
        Empresa empresaAgenda = agenda.getEmpresa();

        return new AgendaDTO().setCodigo(agenda.getCodigo())
                              .setCodigoUnidade(empresaAgenda.getCodigo())
                              .setDescricaoUnidade(empresaAgenda.getDescricao())
                              .setTipoAgenda(atendimentoDTO.getTipoAgendaEmpresa())
                              .setGrades(grades(atendimentos))
                              .setExamePrestadorCompetencia(competencia(empresaAgenda))
                              .setExamesPrestadorRealiza(examesPrestadorRealiza(empresaAgenda));
    }

    private List<GradeDTO> grades(List<AgendaGradeAtendimentoDTO> atendimentos) {
        return convert(atendimentos, new BuildGradeDTO(parametrosBuildAgendaDTO.getAgendasExames(), parametrosBuildAgendaDTO.getHorarios()));
    }

    private ExamePrestadorCompetencia competencia(Empresa empresa) {
        ExamePrestadorCompetencia competencia = on(ExamePrestadorCompetencia.class);
        return selectFirst(parametrosBuildAgendaDTO.getCompetencias(), having(competencia.isCompetenciaPrestadorAgenda(empresa, parametrosBuildAgendaDTO.getTipoExame()), equalTo(true)));
    }

    private List<ExameProcedimento> examesPrestadorRealiza(Empresa empresa) {
        List<ExamePrestadorProcedimento> examesPrestador = parametrosBuildAgendaDTO.getExamesPrestador();
        Empresa prestador = on(ExamePrestadorProcedimento.class).getExamePrestador().getPrestador();
        List<ExamePrestadorProcedimento> procedimentosPrestador = select(examesPrestador, having(prestador.getCodigo(), equalTo(empresa.getCodigo())));
        return extract(procedimentosPrestador, on(ExamePrestadorProcedimento.class).getExameProcedimento());
    }
}
