package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.on;

public class AgendaDTO implements Serializable {

    private Long codigo;

    private Long codigoUnidade;
    private String descricaoUnidade;
    private Long tipoAgenda;

    private ExamePrestadorCompetencia examePrestadorCompetencia;
    private List<GradeDTO> grades;
    private List<ExameProcedimento> examesPrestadorRealiza;


    public Long getCodigo() {
        return codigo;
    }

    public AgendaDTO setCodigo(Long codigo) {
        this.codigo = codigo;
        return this;
    }

    public Long getCodigoUnidade() {
        return codigoUnidade;
    }

    public AgendaDTO setCodigoUnidade(Long codigoUnidade) {
        this.codigoUnidade = codigoUnidade;
        return this;
    }

    public String getDescricaoUnidade() {
        return descricaoUnidade;
    }

    public AgendaDTO setDescricaoUnidade(String descricaoUnidade) {
        this.descricaoUnidade = descricaoUnidade;
        return this;
    }

    public Long getTipoAgenda() {
        return tipoAgenda;
    }

    public AgendaDTO setTipoAgenda(Long tipoAgenda) {
        this.tipoAgenda = tipoAgenda;
        return this;
    }

    public List<GradeDTO> getGrades() {
        return grades;
    }

    public AgendaDTO setGrades(List<GradeDTO> grades) {
        this.grades = grades;
        return this;
    }

    public ExamePrestadorCompetencia getExamePrestadorCompetencia() {
        return examePrestadorCompetencia;
    }

    public AgendaDTO setExamePrestadorCompetencia(ExamePrestadorCompetencia examePrestadorCompetencia) {
        this.examePrestadorCompetencia = examePrestadorCompetencia;
        return this;
    }

    public List<ExameProcedimento> getExamesPrestadorRealiza() {
        return examesPrestadorRealiza;
    }

    public AgendaDTO setExamesPrestadorRealiza(List<ExameProcedimento> examesPrestadorRealiza) {
        this.examesPrestadorRealiza = examesPrestadorRealiza;
        return this;
    }

    public List<GradeDTO> getGradesComVagasDisponiveis() {
        GradeDTO gradeDTO = on(GradeDTO.class);
        return Lambda.select(grades, Lambda.having(gradeDTO.temVagasDisponiveis(), Matchers.equalTo(true)));
    }

    public List<GradeDTO> getGradesComVagasDisponiveisParaProcedimentos(List<ExameProcedimento> procedimentos) {
        GradeDTO gradeDTO = on(GradeDTO.class);
        return Lambda.select(this.getGradesComVagasDisponiveis(), Lambda.having(gradeDTO.atendeAosProcedimentos(procedimentos), Matchers.equalTo(true)));
    }

    public List<ExameProcedimento> getExamesPrestadorRealiza(List<ExameProcedimento> procedimentos) {
        List<ExameProcedimento> procedimentosCompativeis = new ArrayList<>();
        for (ExameProcedimento procedimentoPrestador : this.examesPrestadorRealiza) {
            if (Lambda.exists(procedimentos, Lambda.having(on(ExameProcedimento.class).getCodigo(), Matchers.equalTo(procedimentoPrestador.getCodigo())))) {
                procedimentosCompativeis.add(procedimentoPrestador);
            }
        }
        return CollectionUtils.isNotNullEmpty(this.examesPrestadorRealiza) ? procedimentosCompativeis : procedimentos;
    }

    public boolean possuiVagaDisponivelEPrestadorRealizaAlgumExame(List<ExameProcedimento> procedimentos) {
        List<GradeDTO> gradesHorariosDisponiveis = this.getGradesComVagasDisponiveisParaProcedimentos(procedimentos);
        List<ExameProcedimento> examesPrestador = this.getExamesPrestadorRealiza(procedimentos);
        return CollectionUtils.isNotNullEmpty(examesPrestador) &&
                CollectionUtils.isNotNullEmpty(gradesHorariosDisponiveis);
    }
}
