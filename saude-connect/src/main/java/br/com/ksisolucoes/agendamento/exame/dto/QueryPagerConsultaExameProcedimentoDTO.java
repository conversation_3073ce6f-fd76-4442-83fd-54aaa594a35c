/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaExameProcedimentoDTO implements Serializable {

    public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";
    public static final String PROP_VALOR_TABELA = "valorTabela";
    
    private ExameProcedimento exameProcedimento;
    private Double valorTabela;

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Double getValorTabela() {
        return valorTabela;
    }

    public void setValorTabela(Double valorTabela) {
        this.valorTabela = valorTabela;
    }
}
