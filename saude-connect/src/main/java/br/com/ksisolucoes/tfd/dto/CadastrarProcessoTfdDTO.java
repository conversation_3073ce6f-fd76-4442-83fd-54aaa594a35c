/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.tfd.dto;

import br.com.ksisolucoes.bo.agendamento.tfd.pedidotfdagendamento.dto.DataAgendamentoRetornoTfdDTO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarProcessoTfdDTO implements Serializable {
    
    private RegistroTfdManualDTO registroTfdManualDTO;
    
    private Empresa unidade;
    private Profissional profissional;
    private String nomeProfissional;
    private Date dataAgendamento;
    private String responsavel;
    private List<DataAgendamentoRetornoTfdDTO> dtoDatasRetorno;

    public RegistroTfdManualDTO getRegistroTfdManualDTO() {
        return registroTfdManualDTO;
    }

    public void setRegistroTfdManualDTO(RegistroTfdManualDTO registroTfdManualDTO) {
        this.registroTfdManualDTO = registroTfdManualDTO;
    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public Date getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(Date dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public List<DataAgendamentoRetornoTfdDTO> getDtoDatasRetorno() {
        return dtoDatasRetorno;
    }

    public void setDtoDatasRetorno(List<DataAgendamentoRetornoTfdDTO> dtoDatasRetorno) {
        this.dtoDatasRetorno = dtoDatasRetorno;
    }
}
