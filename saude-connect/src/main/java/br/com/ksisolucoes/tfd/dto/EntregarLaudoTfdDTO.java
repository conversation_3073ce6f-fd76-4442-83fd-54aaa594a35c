package br.com.ksisolucoes.tfd.dto;

import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EntregarLaudoTfdDTO implements Serializable {

    private Long codigoPedidoTfd;
    private String responsavel;
    private Long statusTransporte;
    private List<SalvarPedidoTfdPassageiroDTO> passageiros;
    private TipoTransporteViagem tipoTransporteViagem;

    public Long getCodigoPedidoTfd() {
        return codigoPedidoTfd;
    }

    public void setCodigoPedidoTfd(Long codigoPedidoTfd) {
        this.codigoPedidoTfd = codigoPedidoTfd;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Long getStatusTransporte() {
        return statusTransporte;
    }

    public void setStatusTransporte(Long statusTransporte) {
        this.statusTransporte = statusTransporte;
    }

    public List<SalvarPedidoTfdPassageiroDTO> getPassageiros() {
        return passageiros;
    }

    public void setPassageiros(List<SalvarPedidoTfdPassageiroDTO> passageiros) {
        this.passageiros = passageiros;
    }

    public TipoTransporteViagem getTipoTransporteViagem() {
        return tipoTransporteViagem;
    }

    public void setTipoTransporteViagem(TipoTransporteViagem tipoTransporteViagem) {
        this.tipoTransporteViagem = tipoTransporteViagem;
    }

}
