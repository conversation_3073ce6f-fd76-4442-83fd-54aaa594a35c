package br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaLotesDisponiveisDevolucaoDTOParam implements Serializable{

    private UsuarioCadsus usuarioCadsus;
    private Produto produto;
    private String lote;
    private Long codigoDispencacaoItemLote;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public String getLote() {
        return lote;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public Long getCodigoDispencacaoItemLote() {
        return codigoDispencacaoItemLote;
    }

    public void setCodigoDispencacaoItemLote(Long codigoDispencacaoItemLote) {
        this.codigoDispencacaoItemLote = codigoDispencacaoItemLote;
    }
}
