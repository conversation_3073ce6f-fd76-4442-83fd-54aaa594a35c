package br.com.ksisolucoes.bo.prontuario.tuberculose.dto;

import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomaticoExames;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class TuberculoseResultadoSintomaticoDTO implements Serializable {

    private TuberculoseSintomatico tuberculoseSintomatico;
    private List<TuberculoseSintomaticoExames> tuberculoseSintomaticoExamesList = new ArrayList<>();

    public TuberculoseSintomatico getTuberculoseSintomatico() {
        return tuberculoseSintomatico;
    }

    public void setTuberculoseSintomatico(TuberculoseSintomatico tuberculoseSintomatico) {
        this.tuberculoseSintomatico = tuberculoseSintomatico;
    }

    public List<TuberculoseSintomaticoExames> getTuberculoseSintomaticoExamesList() {
        return tuberculoseSintomaticoExamesList;
    }

    public void setTuberculoseSintomaticoExamesList(List<TuberculoseSintomaticoExames> tuberculoseSintomaticoExamesList) {
        this.tuberculoseSintomaticoExamesList = tuberculoseSintomaticoExamesList;
    }
}
