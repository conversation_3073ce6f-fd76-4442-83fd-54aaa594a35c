/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.entradas.estoque.reserva.dto;

import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.vacina.ProdutoVacina;

import java.io.Serializable;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ReservaDTO implements Serializable{
    
    private java.lang.Long documento;
    private java.lang.Long item;
    private java.lang.String tipoReserva;

    private String grupo;
    private Long codigoDeposito;
    private Long codigoEmpresa;
    private String codigoProduto;
    private LocalizacaoEstrutura localizacaoEstrutura;
    private ProdutoVacina produtoVacina;
    private String observacao;

    private java.lang.Double quantidade;
    
    private boolean validarDisponivelNegativo = false;

    public boolean isValidarDisponivelNegativo() {
        return validarDisponivelNegativo;
    }

    public void setValidarDisponivelNegativo(boolean validarDisponivelNegativo) {
        this.validarDisponivelNegativo = validarDisponivelNegativo;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }

    public Long getDocumento() {
        return documento;
    }

    public void setDocumento(Long documento) {
        this.documento = documento;
    }

    public Long getItem() {
        return item;
    }

    public void setItem(Long item) {
        this.item = item;
    }

    public String getTipoReserva() {
        return tipoReserva;
    }

    public void setTipoReserva(String tipoReserva) {
        this.tipoReserva = tipoReserva;
    }

    public Long getCodigoDeposito() {
        return codigoDeposito;
    }

    public void setCodigoDeposito(Long codigoDeposito) {
        this.codigoDeposito = codigoDeposito;
    }

    public Long getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Long codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public LocalizacaoEstrutura getLocalizacaoEstrutura() {
        LocalizacaoEstrutura local = null;
        try {
            local = localizacaoEstrutura != null ? localizacaoEstrutura : EstoqueHelper.getLocalizacaoEstruturaPadrao();
        } catch (ValidacaoException ex) {
            Logger.getLogger(ReservaDTO.class.getName()).log(Level.SEVERE, null, ex);
            Loggable.log.error(ex);
        }
        return local;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura) {
        this.localizacaoEstrutura = localizacaoEstrutura;
    }

    public ProdutoVacina getProdutoVacina() {
        return produtoVacina;
    }

    public void setProdutoVacina(ProdutoVacina produtoVacina) {
        this.produtoVacina = produtoVacina;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
