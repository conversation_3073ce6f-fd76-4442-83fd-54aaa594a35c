package br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

import java.io.Serializable;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 */
public class ReceituarioItemDTO implements Serializable {

    private ReceituarioItem receituarioItem;
    private final UUID uuid;
    private Integer receita;
    private Frequencia frequencia;
    private String tipoFrequencia;
    private Atendimento atendimento;
    private String anotacao;
    private ReceituarioMedicamentoNaoPadronizadoDTO receituarioMedicamentoNaoPadronizadoDTO;
    private Integer diasValidadeProlongado;
    private UsuarioCadsusDocumento usuarioCadsusDocumento;
    private String tipoReceita;

    public ReceituarioItemDTO() {
        uuid = UUID.randomUUID();
    }

    public UsuarioCadsusDocumento getUsuarioCadsusDocumento() {
        return usuarioCadsusDocumento;
    }

    public void setUsuarioCadsusDocumento(UsuarioCadsusDocumento usuarioCadsusDocumento) {
        this.usuarioCadsusDocumento = usuarioCadsusDocumento;
    }
    private boolean adicionarPrescricoesSugeridas;

    public String getAnotacao() {
        return anotacao;
    }

    public void setAnotacao(String anotacao) {
        this.anotacao = anotacao;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public ReceituarioItemDTO(ReceituarioItem receituarioItem) {
        uuid = UUID.randomUUID();
        this.receituarioItem = receituarioItem;
    }

    public UUID getUuid() {
        return uuid;
    }

    public ReceituarioItem getReceituarioItem() {
        return receituarioItem;
    }

    public void setReceituarioItem(ReceituarioItem receituarioItem) {
        this.receituarioItem = receituarioItem;
    }

    public Integer getReceita() {
        return receita;
    }

    public void setReceita(Integer receita) {
        this.receita = receita;
    }

    public Frequencia getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Frequencia frequencia) {
        this.frequencia = frequencia;
    }

    public enum Frequencia {

        DIA(ReceituarioItem.FREQUENCIA_DIA),
        SEMANA(ReceituarioItem.FREQUENCIA_SEMANA),
        MES(ReceituarioItem.FREQUENCIA_MES),
        HORA_12(ReceituarioItem.FREQUENCIA_HORA, 12L),
        HORA_8(ReceituarioItem.FREQUENCIA_HORA, 8L),
        HORA_6(ReceituarioItem.FREQUENCIA_HORA, 6L),
        HORA_4(ReceituarioItem.FREQUENCIA_HORA, 4L),
        OUTRA(ReceituarioItem.FREQUENCIA_OUTRA),
        SN(ReceituarioItem.FREQUENCIA_SN, 0L),
        DOSE_UNICA(ReceituarioItem.FREQUENCIA_DOSE_UNICA),;

        private final Long frequencia;
        private Long intervalo;

        Frequencia(Long frequencia) {
            this.frequencia = frequencia;
        }

        Frequencia(Long frequencia, Long intervalo) {
            this.frequencia = frequencia;
            this.intervalo = intervalo;
        }

        public Long frequencia() {
            return frequencia;
        }

        public Long intervalo() {
            return intervalo;
        }

        public static Frequencia resolve(Long frequencia, Long intervalo) {
            if (DIA.frequencia().equals(frequencia)) {
                return DIA;
            } else if (SEMANA.frequencia().equals(frequencia)) {
                return SEMANA;
            } else if (MES.frequencia().equals(frequencia)) {
                return MES;
            } else if (ReceituarioItem.FREQUENCIA_HORA.equals(frequencia)) {
                if (HORA_12.intervalo().equals(intervalo)) {
                    return HORA_12;
                } else if (HORA_8.intervalo().equals(intervalo)) {
                    return HORA_8;
                } else if (HORA_6.intervalo().equals(intervalo)) {
                    return HORA_6;
                } else if (HORA_4.intervalo().equals(intervalo)) {
                    return HORA_4;
                }
            } else if (SN.frequencia().equals(frequencia)) {
                return SN;
            } else if (OUTRA.frequencia().equals(frequencia)) {
                return OUTRA;
            } else if (DOSE_UNICA.frequencia().equals(frequencia)) {
                return DOSE_UNICA;
            }
            return DIA;
        }
    }

    public String getTipoFrequencia() {
        return tipoFrequencia;
    }

    public void setTipoFrequencia(String tipoFrequencia) {
        this.tipoFrequencia = tipoFrequencia;
    }

    public String getDescricaoTipoReceita() {
        if(getReceituarioItem().getProduto() != null) {
            return getReceituarioItem().getProduto().getTipoReceita().getDescricao();
        } else if (getReceituarioItem().getTipoReceitaProdutoNaoCadastrado() != null) {
            return getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getDescricao();
        } else {
            return "";
        }
    }

    public ReceituarioMedicamentoNaoPadronizadoDTO getReceituarioMedicamentoNaoPadronizadoDTO() {
        return receituarioMedicamentoNaoPadronizadoDTO;
    }

    public void setReceituarioMedicamentoNaoPadronizadoDTO(ReceituarioMedicamentoNaoPadronizadoDTO receituarioMedicamentoNaoPadronizadoDTO) {
        this.receituarioMedicamentoNaoPadronizadoDTO = receituarioMedicamentoNaoPadronizadoDTO;
    }

    public Integer getDiasValidadeProlongado() {
        return diasValidadeProlongado;
    }

    public void setDiasValidadeProlongado(Integer diasValidadeProlongado) {
        this.diasValidadeProlongado = diasValidadeProlongado;
    }

    public enum FormaValidacaoReceita implements IEnum {

        SEM_VALIDACAO(0L, Bundle.getStringApplication("rotulo_sem_validacao")),
        VALIDADE_POR_RECEITA(1L, Bundle.getStringApplication("rotulo_validade_por_receita")),
        VALIDADE_POR_MEDICAMENTO(2L, Bundle.getStringApplication("rotulo_validade_por_medicamento"));

        private final Long value;
        private final String descricao;

        FormaValidacaoReceita(long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

        public static RepositoryComponentDefault.TipoImpressoraEtiqueta valueOf(Long value) {
            for (RepositoryComponentDefault.TipoImpressoraEtiqueta tie : RepositoryComponentDefault.TipoImpressoraEtiqueta.values()) {
                if (tie.value().equals(value)) {
                    return tie;
                }
            }
            return null;
        }
    }

    public boolean isAdicionarPrescricoesSugeridas() {
        return adicionarPrescricoesSugeridas;
    }

    public void setAdicionarPrescricoesSugeridas(boolean adicionarPrescricoesSugeridas) {
        this.adicionarPrescricoesSugeridas = adicionarPrescricoesSugeridas;
    }

    public String getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(String tipoReceita) {
        this.tipoReceita = tipoReceita;
    }
}
