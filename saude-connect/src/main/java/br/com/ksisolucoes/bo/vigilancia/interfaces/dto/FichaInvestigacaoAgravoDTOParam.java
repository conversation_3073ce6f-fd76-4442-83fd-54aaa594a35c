package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FichaInvestigacaoAgravoDTOParam implements Serializable {

    private Empresa unidade;
    private Empresa unidadeReferencia;
    private ClassificacaoCids classificacaoCids;
    private List<Cid> listCid;
    private UsuarioCadsus usuarioCadsus;
    private Long status;
    private DatePeriod periodo;
    private TipoRelatorio tipoArquivo;
    private Long todasNotificacoes;

    public enum Status implements IEnum {

        TODOS(99L, Bundle.getStringApplication("rotulo_todos")), 
        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        MONITORAMENTO(1L, Bundle.getStringApplication("rotulo_monitoramento")),
        CONCLUIDO(2L, Bundle.getStringApplication("rotulo_concluido")),
        CANCELADO(3L, Bundle.getStringApplication("rotulo_cancelado")),
        EM_INVESTIGACAO(4L, Bundle.getStringApplication("rotulo_em_investigacao")),
        INVESTIGACAO_CONCLUIDA(5L, Bundle.getStringApplication("rotulo_investigacao_concluida")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    public ClassificacaoCids getClassificacaoCids() {
        return classificacaoCids;
    }

    public void setClassificacaoCids(ClassificacaoCids classificacaoCids) {
        this.classificacaoCids = classificacaoCids;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Empresa getUnidadeReferencia() {
        return unidadeReferencia;
    }

    public void setUnidadeReferencia(Empresa unidadeReferencia) {
        this.unidadeReferencia = unidadeReferencia;
    }

    public List<Cid> getListCid() {
        return listCid;
    }

    public void setListCid(List<Cid> listCid) {
        this.listCid = listCid;
    }

    public TipoRelatorio getTipoArquivo() {
        return tipoArquivo;
    }

    public void setTipoArquivo(TipoRelatorio tipoArquivo) {
        this.tipoArquivo = tipoArquivo;
    }

    public Long getTodasNotificacoes() {
        return todasNotificacoes;
    }

    public void setTodasNotificacoes(Long todasNotificacoes) {
        this.todasNotificacoes = todasNotificacoes;
    }
}
