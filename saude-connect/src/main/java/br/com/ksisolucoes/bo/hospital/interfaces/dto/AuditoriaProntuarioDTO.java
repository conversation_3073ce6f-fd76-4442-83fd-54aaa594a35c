package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.Cuidados;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentosItem;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AuditoriaProntuarioDTO implements Serializable{
    
    private EvolucaoProntuario evolucaoProntuario;
    private List<AtendimentoItem> lstAtendimentoItem = new ArrayList<AtendimentoItem>();
    private Atendimento atendimento;
    private List<Cuidados> lstCuidados = new ArrayList<Cuidados>();
//    private List<SolicitacaoProcedimentos> lstSolicitacaoProcedimentos;
    private List<ExameRequisicao> lstExameRequisicao = new ArrayList<ExameRequisicao>();
    private List<SolicitacaoProcedimentosItem> lstSolicitacaoProcedimentosItem;

    public List<Cuidados> getLstCuidados() {
        return lstCuidados;
    }

    public void setLstCuidados(List<Cuidados> lstCuidados) {
        this.lstCuidados = lstCuidados;
    }

    public List<ExameRequisicao> getLstExameRequisicao() {
        return lstExameRequisicao;
    }

    public void setLstExameRequisicao(List<ExameRequisicao> lstExameRequisicao) {
        this.lstExameRequisicao = lstExameRequisicao;
    }
    
    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public EvolucaoProntuario getEvolucaoProntuario() {
        return evolucaoProntuario;
    }

    public void setEvolucaoProntuario(EvolucaoProntuario evolucaoProntuario) {
        this.evolucaoProntuario = evolucaoProntuario;
    }

    public List<AtendimentoItem> getLstAtendimentoItem() {
        return lstAtendimentoItem;
    }

    public void setLstAtendimentoItem(List<AtendimentoItem> lstAtendimentoItem) {
        this.lstAtendimentoItem = lstAtendimentoItem;
    }

    public List<SolicitacaoProcedimentosItem> getLstSolicitacaoProcedimentosItem() {
        return lstSolicitacaoProcedimentosItem;
    }

    public void setLstSolicitacaoProcedimentosItem(List<SolicitacaoProcedimentosItem> lstSolicitacaoProcedimentosItem) {
        this.lstSolicitacaoProcedimentosItem = lstSolicitacaoProcedimentosItem;
    }
}
