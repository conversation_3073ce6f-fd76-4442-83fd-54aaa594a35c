package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorEdital;
import br.com.ksisolucoes.vo.consorcio.ContratoEdital;

import java.io.Serializable;
import java.util.List;

public class ConsorcioPrestadorEditalDTO implements Serializable {

    private ConsorcioPrestadorEdital consorcioPrestadorEdital;
    private List<ContratoEdital> contratoEditalList;

    public ConsorcioPrestadorEditalDTO(ConsorcioPrestadorEdital consorcioPrestadorEdital) {
        this.consorcioPrestadorEdital = consorcioPrestadorEdital;
    }

    public ConsorcioPrestadorEdital getConsorcioPrestadorEdital() {
        return consorcioPrestadorEdital;
    }

    public void setConsorcioPrestadorEdital(ConsorcioPrestadorEdital consorcioPrestadorEdital) {
        this.consorcioPrestadorEdital = consorcioPrestadorEdital;
    }

    public List<ContratoEdital> getContratoEditalList() {
        return contratoEditalList;
    }

    public void setContratoEditalList(List<ContratoEdital> contratoEditalList) {
        this.contratoEditalList = contratoEditalList;
    }
}
