package br.com.ksisolucoes.bo.exame.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.io.Serializable;

/**
 * Created by ma<PERSON>cley on 25/05/18.
 */
public class SolicitacaoExameHistoricoDTOParam implements Serializable {
    private Atendimento atendimento;
    private DatePeriod datePeriod;

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public DatePeriod getPeriod() {
        return datePeriod;
    }

    public void setPeriod(DatePeriod datePeriod) {
        this.datePeriod = datePeriod;
    }
}
