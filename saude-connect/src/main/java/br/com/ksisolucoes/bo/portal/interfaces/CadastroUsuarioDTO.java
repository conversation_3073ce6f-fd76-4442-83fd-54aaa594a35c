package br.com.ksisolucoes.bo.portal.interfaces;

import br.com.ksisolucoes.vo.portal.UsuarioPortal;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class CadastroUsuarioDTO implements Serializable{
    private UsuarioPortal usuarioPortal;
    private String cpf;
    private Date dataNascimento;
    private String confirmacaoSenha;

    public UsuarioPortal getUsuarioPortal() {
        return usuarioPortal;
    }

    public void setUsuarioPortal(UsuarioPortal usuarioPortal) {
        this.usuarioPortal = usuarioPortal;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getConfirmacaoSenha() {
        return confirmacaoSenha;
    }

    public void setConfirmacaoSenha(String confirmacaoSenha) {
        this.confirmacaoSenha = confirmacaoSenha;
    }
}
