package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.FechamentoAnual;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.consorcio.TipoMovimentacao;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class MovimentacaoFinanceiraDTO implements Serializable{

    public static final String PROP_HISTORICO = "historico";
    public static final String PROP_TIPO_CONTA = "tipoConta";
    public static final String PROP_TIPO_MOVIMENTACAO = "tipoMovimentacao";
    public static final String PROP_VALOR = "valor";
    public static final String PROP_CONSORCIADO = "consorciado";
    public static final String PROP_FECHAMENTO_ANUAL = "fechamentoAnual";

    private String historico;
    private SubConta tipoConta;
    private TipoMovimentacao tipoMovimentacao;
    private Double valor;
    private Empresa consorciado;
    private FechamentoAnual fechamentoAnual;
    private Long ano;

    public String getHistorico() {
        return historico;
    }

    public void setHistorico(String historico) {
        this.historico = historico;
    }

    public SubConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(SubConta tipoConta) {
        this.tipoConta = tipoConta;
    }

    public TipoMovimentacao getTipoMovimentacao() {
        return tipoMovimentacao;
    }

    public void setTipoMovimentacao(TipoMovimentacao tipoMovimentacao) {
        this.tipoMovimentacao = tipoMovimentacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    public FechamentoAnual getFechamentoAnual() {
        return fechamentoAnual;
    }

    public void setFechamentoAnual(FechamentoAnual fechamentoAnual) {
        this.fechamentoAnual = fechamentoAnual;
    }

    public Long getAno() {
        return ano;
    }

    public void setAno(Long ano) {
        this.ano = ano;
    }
}
