package br.com.ksisolucoes.bo.controle.interfaces.dto;

import br.com.ksisolucoes.vo.controle.Usuario;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * <PERSON>riado em: Jul 12, 2013
 */
public class EmpresasUsuarioDTO implements Serializable{

    private Usuario usuario;
    private List<Long> tiposEstabelecimento;

    public EmpresasUsuarioDTO(Usuario usuario) {
        this.usuario = usuario;
    }

    public EmpresasUsuarioDTO(Usuario usuario, List<Long> tiposEstabelecimento){
        this.usuario = usuario;
        this.tiposEstabelecimento = tiposEstabelecimento;
    }

    public List<Long> getTiposEstabelecimento() {
        return tiposEstabelecimento;
    }

    public void setTiposEstabelecimento(List<Long> tiposEstabelecimento) {
        this.tiposEstabelecimento = tiposEstabelecimento;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
    
    
}
