package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioRelacaoFinanceiroResumidoDTO implements Serializable {

    private Long quantidade;
    private Long tipoDocumento;
    private Double valorTotal;
    private String descricaoTipoDocumento;
    private VigilanciaFinanceiro vigilanciaFinanceiro;


    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(Long tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getDescricaoTipoDocumento() {
        return descricaoTipoDocumento;
    }

    public void setDescricaoTipoDocumento(String descricaoTipoDocumento) {
        this.descricaoTipoDocumento = descricaoTipoDocumento;
    }

    public VigilanciaFinanceiro getVigilanciaFinanceiro() {
        return vigilanciaFinanceiro;
    }

    public void setVigilanciaFinanceiro(VigilanciaFinanceiro vigilanciaFinanceiro) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
    }
}
