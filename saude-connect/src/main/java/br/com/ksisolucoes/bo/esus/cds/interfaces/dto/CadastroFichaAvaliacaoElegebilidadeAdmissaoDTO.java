package br.com.ksisolucoes.bo.esus.cds.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAvaliacaoElegebilidadeAdmissao;

import java.io.Serializable;

/**
 * Created by sulivan on 17/08/17.
 */
public class CadastroFichaAvaliacaoElegebilidadeAdmissaoDTO implements Serializable {

    private EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao;

    public EsusFichaAvaliacaoElegebilidadeAdmissao getEsusFichaAvaliacaoElegebilidadeAdmissao() {
        return esusFichaAvaliacaoElegebilidadeAdmissao;
    }

    public void setEsusFichaAvaliacaoElegebilidadeAdmissao(EsusFichaAvaliacaoElegebilidadeAdmissao esusFichaAvaliacaoElegebilidadeAdmissao) {
        this.esusFichaAvaliacaoElegebilidadeAdmissao = esusFichaAvaliacaoElegebilidadeAdmissao;
    }
}
