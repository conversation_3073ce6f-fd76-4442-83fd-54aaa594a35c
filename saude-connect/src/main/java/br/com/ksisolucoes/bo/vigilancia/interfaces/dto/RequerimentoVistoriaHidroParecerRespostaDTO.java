package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioParecerResposta;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RequerimentoVistoriaHidroParecerRespostaDTO implements Serializable {

    private RequerimentoVistoriaHidrossanitarioParecer requerimentoVistoriaHidrossanitarioParecer;
    private RequerimentoVistoriaHidrossanitarioParecerResposta requerimentoVistoriaHidrossanitarioParecerResposta;

    public RequerimentoVistoriaHidrossanitarioParecer getRequerimentoVistoriaHidrossanitarioParecer() {
        return requerimentoVistoriaHidrossanitarioParecer;
    }

    public void setRequerimentoVistoriaHidrossanitarioParecer(RequerimentoVistoriaHidrossanitarioParecer requerimentoVistoriaHidrossanitarioParecer) {
        this.requerimentoVistoriaHidrossanitarioParecer = requerimentoVistoriaHidrossanitarioParecer;
    }

    public RequerimentoVistoriaHidrossanitarioParecerResposta getRequerimentoVistoriaHidrossanitarioParecerResposta() {
        return requerimentoVistoriaHidrossanitarioParecerResposta;
    }

    public void setRequerimentoVistoriaHidrossanitarioParecerResposta(RequerimentoVistoriaHidrossanitarioParecerResposta requerimentoVistoriaHidrossanitarioParecerResposta) {
        this.requerimentoVistoriaHidrossanitarioParecerResposta = requerimentoVistoriaHidrossanitarioParecerResposta;
    }
}
