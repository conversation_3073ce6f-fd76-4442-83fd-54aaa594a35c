/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.interfaces;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.EstabelecimentoCerestCnaeDTO;
import br.com.ksisolucoes.bo.basico.dto.ValidacaoEderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryGroup;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomGroup;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.painel.Painel;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.programasaude.Mamografia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import br.com.ksisolucoes.vo.vigilancia.TabelaCnae;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.hamcrest.Matchers;

import java.util.*;

import static br.com.celk.util.DataUtil.zerarHora;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoProcedimento.PROP_FLAG_AGENDAR_NO_ATENDIMENTO;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class AtendimentoHelper {

    private AtendimentoHelper() {
    }

    //    public static AtendimentoHelper getNewInstance(){
//        AtendimentoHelper atendimentoHelper = new AtendimentoHelper();
//        return atendimentoHelper;
//    }
//    }
    public static void validaGeracaoEncaminhamento(Conduta encaminhamento, Atendimento atendimento) throws DAOException, ValidacaoException {
        if (encaminhamento != null
                && RepositoryComponentDefault.SIM.equals(encaminhamento.getGeraEncaminhamento())) {
            Boolean ae = LoadManager.getInstance(Encaminhamento.class)
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(Encaminhamento.PROP_ATENDIMENTO), atendimento))
                    .exists();
            if (!ae) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_encaminhamento_exige_agenda_especializada"));
            }
        }
    }

    public static NaturezaProcuraTipoAtendimento validaNaturezaProcuraTipoAtendimento(Long codigoNaturezaProcura, Long codigoTipoAtendimento) throws DAOException, ValidacaoException {
        NaturezaProcuraTipoAtendimento npta = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, NaturezaProcura.PROP_CODIGO), codigoNaturezaProcura))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO), codigoTipoAtendimento))
                .addProperty(NaturezaProcuraTipoAtendimento.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TIPO_ATENDIMENTO))
                .addProperty(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                .start().getVO();
        if (npta == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_uma_relacao_definida_entre_tipo_atendimento_natureza_procura", codigoNaturezaProcura, codigoTipoAtendimento));
        }
        return npta;
    }

    public static ProcedimentoCompetencia validaProcedimentoCompetencia(Procedimento procedimento) throws DAOException, ValidacaoException {
        return validaProcedimentoCompetencia(procedimento, null);
    }

    public static ProcedimentoCompetencia validaProcedimentoCompetencia(Procedimento procedimento, UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
        ProcedimentoCompetencia procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addProperties(new HQLProperties(ProcedimentoCompetencia.class).getProperties())
                .addProperty(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO), procedimento.getCodigo()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO)))
                .start().getVO();
        if (procedimentoCompetencia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_invalido_competencia_atual",procedimento.getDescricaoFormatado()));
        }
        if (usuarioCadsus != null) {
            if (!procedimentoCompetencia.getTipoSexo().equals(ProcedimentoCompetencia.SEXO_INDIFERENTE)
                    && !procedimentoCompetencia.getTipoSexo().equals(ProcedimentoCompetencia.SEXO_NAO_APLICA)
                    && !procedimentoCompetencia.getTipoSexo().equals(usuarioCadsus.getSexo())) {

                throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_nao_aplicavel_pacientes_sexo_X", usuarioCadsus.getSexoFormatado()));
            }
        }
        return procedimentoCompetencia;
    }

    public static ProcedimentoCbo validaProcedimentoCboProfissionalAtendimento(Procedimento procedimento, Profissional profissional, Empresa empresa) throws DAOException, ValidacaoException {
        if (isProcedimentoContainsCBO(procedimento)) {
            Date dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);

            List<ProcedimentoCbo> procedimentoCboList = LoadManager.getInstance(ProcedimentoCbo.class)
                    .addInterceptor(new LoadInterceptorValidaProcedimentoCboProfissionalAtendimento(dataCompetencia, procedimento, profissional, empresa))
                    .start().getList();

            if (CollectionUtils.isEmpty(procedimentoCboList)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_nao_habilitado_exec_procedimento_selecionado"));
            }
            return procedimentoCboList.get(0);
        }

        return null;
    }

    public static Date competenciaAtendimento(ProcedimentoCompetencia procedimentoCompetencia, Profissional profissional, Long codigoEmpresa, TabelaCbo tabelaCbo) throws DAOException, ValidacaoException {
        return competenciaAtendimento(Data.getDataAtual(), procedimentoCompetencia, profissional, codigoEmpresa, tabelaCbo);
    }

    public static Date competenciaAtendimento(Date dataReferencia, ProcedimentoCompetencia procedimentoCompetencia, Profissional profissional, Long codigoEmpresa, TabelaCbo tabelaCbo) throws DAOException, ValidacaoException {
        Long dia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia();
        Date competenciaAtual = Data.competenciaData(dia.intValue(), dataReferencia);

        Date competenciaAtendimento = competenciaAtual;
        Long count = LoadManager.getInstance(ProcedimentoRegistro.class)
                .addGroup(new QueryCustomGroup(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_REGISTRO_CADASTRO, ProcedimentoRegistroCadastro.PROP_CODIGO), QueryGroup.COUNT))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_REGISTRO_CADASTRO, ProcedimentoRegistroCadastro.PROP_CODIGO), ProcedimentoRegistroCadastro.BPA_INDIVIDUAL))
                .start().getVO();
        if (count > 0) {
            List<ProfissionalCargaHoraria> pchList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_PROFISSIONAL), profissional))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_EMPRESA, Empresa.PROP_CODIGO), codigoEmpresa))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_TABELA_CBO), tabelaCbo))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(pchList)) {
                if (pchList.get(0).getCompetenciaInicio() != null && pchList.get(0).getCompetenciaInicio().after(competenciaAtual)) {
                    competenciaAtendimento = pchList.get(0).getCompetenciaInicio();
                }
            }
        }
        return competenciaAtendimento;
    }

    public static void validaCategoriaCid(Cid cid) throws ValidacaoException, DAOException {
        if (cid == null) {
            return;
        }

        List<Cid> cidList = LoadManager.getInstance(Cid.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Cid.PROP_CODIGO, QueryCustom.QueryCustomParameter.LIKE, cid.getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(cidList) && cidList.size() > 1) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_cid_X_especificado_categoria", cid.getDescricaoFormatado()));
        }
    }

    public static void validaProcedimentoCid(Cid cid, Procedimento procedimento) throws ValidacaoException, DAOException {
        Date dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
        validaProcedimentoCid(cid, new ProcedimentoCompetencia(new ProcedimentoCompetenciaPK(procedimento, dataCompetencia)));
    }

    public static void validaProcedimentoCid(Cid cid, ProcedimentoCompetencia procedimentoCompetencia) throws ValidacaoException, DAOException {
        if (cid == null || procedimentoCompetencia == null) {
            return;
        }

        ProcedimentoCid proxy = on(ProcedimentoCid.class);
        LoadManager load = LoadManager.getInstance(ProcedimentoCid.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProcedimentoCompetencia().getId()), procedimentoCompetencia))
                .setMaxResults(1);

        boolean existsProcedimento = load.exists();
        if (existsProcedimento) {
            load.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getCid()), cid));

            boolean existsProcedimentoCid = load.exists();
            if (!existsProcedimentoCid) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_X_nao_tem_relacao_com_cid_Y", procedimentoCompetencia.getId().getProcedimento().getDescricao(), cid.getDescricaoFormatado()));
            }
        }
    }

    public static void validaProcedimentoCategoriaCid(Cid cid, Procedimento procedimento) throws ValidacaoException, DAOException {
        Date dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
        validaProcedimentoCategoriaCid(cid, new ProcedimentoCompetencia(new ProcedimentoCompetenciaPK(procedimento, dataCompetencia)));
    }

    public static void validaProcedimentoCategoriaCid(Cid cid, ProcedimentoCompetencia procedimentoCompetencia) throws ValidacaoException, DAOException {
        validaCategoriaCid(cid);
        validaProcedimentoCid(cid, procedimentoCompetencia);
    }

    public static Map<String, String[]> montarPropriedadesAtendimentoPrimario(AtendimentoPrimario atendimentoPrimario) {
        Map<String, String[]> valores = new LinkedHashMap<String, String[]>();
        if (atendimentoPrimario != null) {

            if (atendimentoPrimario.getPeso() != null && atendimentoPrimario.getPeso() > 0D) {
                valores.put("@Peso", new String[]{"Peso (Kg)", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getPeso(), 3)});
            }
            if (atendimentoPrimario.getTemperatura() != null && atendimentoPrimario.getTemperatura() > 0D) {
                valores.put("@Temperatura", new String[]{"Temperatura", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getTemperatura())});
            }
            if (atendimentoPrimario.getAltura() != null && atendimentoPrimario.getAltura() > 0D) {
                valores.put("@Altura", new String[]{"Altura (cm)", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getAltura())});
            }
            if (atendimentoPrimario.getPressaoArterialSistolica() != null && atendimentoPrimario.getPressaoArterialSistolica() > 0D) {
                valores.put("@PA", new String[]{"PA", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getPressaoArterialSistolica()) + "x" + br.com.celk.util.Coalesce.asString(atendimentoPrimario.getPressaoArterialDiastolica())});
            }
            if (atendimentoPrimario.getGlicemia() != null && atendimentoPrimario.getGlicemia() > 0D) {
                valores.put("@Glicemia", new String[]{"Glicemia", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getGlicemia())});
            }
            if (atendimentoPrimario.getFrequenciaCardiaca() != null && atendimentoPrimario.getFrequenciaCardiaca() > 0D) {
                valores.put("@FrequenciaCardiaca", new String[]{"Frequ&#234;ncia Card&#237;aca", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getFrequenciaCardiaca())});
            }
            if (atendimentoPrimario.getCintura() != null && atendimentoPrimario.getCintura() > 0D) {
                valores.put("@Cintura", new String[]{"Cintura (cm)", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getCintura())});
            }
            if (atendimentoPrimario.getPerimetroCefalico() != null && atendimentoPrimario.getPerimetroCefalico() > 0D) {
                valores.put("@PerimetroCefalico", new String[]{"Per&#237;metro Cef&#225;lico (cm)", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getPerimetroCefalico())});
            }
            if (atendimentoPrimario.getTemperaturaRetal() != null && atendimentoPrimario.getTemperaturaRetal() > 0D) {
                valores.put("@TemperaturaRetal", new String[]{"Temperatura Retal", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getTemperaturaRetal())});
            }
            if (atendimentoPrimario.getFrequenciaRespiratoria() != null && atendimentoPrimario.getFrequenciaRespiratoria() > 0D) {
                valores.put("@FrequenciaRespiratoria", new String[]{"Frequ&#234;ncia Respirat&#243;ria", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getFrequenciaRespiratoria())});
            }
            if (atendimentoPrimario.getFrequenciaCardiacaFetal() != null && atendimentoPrimario.getFrequenciaCardiacaFetal() > 0D) {
                valores.put("@FrequenciaCardiacaFetal", new String[]{"Frequ&#234;ncia Card&#237;aca Fetal", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getFrequenciaCardiacaFetal())});
            }
            if (atendimentoPrimario.getDiurese() != null && atendimentoPrimario.getDiurese() > 0L) {
                valores.put("@Diurese", new String[]{"Diurese (" + atendimentoPrimario.getDescricaoTipoDiurese() + ")", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getDiurese())});
            }
            if (atendimentoPrimario.getDiurese() != null && atendimentoPrimario.getDiurese() > 0L) {
                valores.put("@Evacuacao", new String[]{"Evacuação", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getEvacuacao())});
            }
            if (atendimentoPrimario.getSaturacaoOxigenio() != null && atendimentoPrimario.getSaturacaoOxigenio() > 0L) {
                valores.put("@SaturacaoOxigenio", new String[]{"Satura&#231;&#227;o de Oxig&#234;nio (%)", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getSaturacaoOxigenio())});
            }
            if (atendimentoPrimario.getDescricaoAlergia() != null && !atendimentoPrimario.getDescricaoAlergia().isEmpty()) {
                valores.put("@Alergias", new String[]{"Alergias", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getDescricaoAlergia())});
            }
            if (atendimentoPrimario.getImc() != null && atendimentoPrimario.getImc() > 0D) {
                valores.put("@IMC", new String[]{"IMC", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getImc())});
            }
            if (atendimentoPrimario.getIndiceImc() != null && atendimentoPrimario.getIndiceImc().getSituacao() != null) {
                valores.put("@IndiceIMC", new String[]{"Índice IMC", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getIndiceImc().getSituacao())});
            }
            if (atendimentoPrimario.getTriglicerideos() != null && atendimentoPrimario.getTriglicerideos() > 0L) {
                valores.put("@Triglicerideos", new String[]{"Triglicerídeo", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getTriglicerideos())});
            }
            if (atendimentoPrimario.getColesterolTotal() != null && atendimentoPrimario.getColesterolTotal() > 0L) {
                valores.put("@ColesterolTotal", new String[]{"Colesterol Total", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getColesterolTotal())});
            }
            if (atendimentoPrimario.getLdl() != null && atendimentoPrimario.getLdl() > 0L) {
                valores.put("@Ldl", new String[]{"LDL", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getLdl())});
            }
            if (atendimentoPrimario.getHdl() != null && atendimentoPrimario.getHdl() > 0L) {
                valores.put("@Hdl", new String[]{"HDL", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getHdl())});
            }
            if (atendimentoPrimario.getCircunferenciaQuadril() != null && atendimentoPrimario.getCircunferenciaQuadril() > 0L) {
                valores.put("@CircunferenciaQuadril", new String[]{"Circunferência Quadril", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getCircunferenciaQuadril())});
            }
            if (atendimentoPrimario.getCircunferenciaAbdomen() != null && atendimentoPrimario.getCircunferenciaAbdomen() > 0L) {
                valores.put("@CircunferenciaAbdomen", new String[]{"Circunferência Abdomen", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getCircunferenciaAbdomen())});
            }
            if (atendimentoPrimario.getHemoglobinaGlicada() != null && atendimentoPrimario.getHemoglobinaGlicada() > 0.0D) {
                valores.put("@HemoglobinaGlicada", new String[]{"Hemoglobina Glicada", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getHemoglobinaGlicada())});
            }
            if (atendimentoPrimario.getCalculoRelacaoCinturaQuadril() != null && atendimentoPrimario.getCalculoRelacaoCinturaQuadril() > 0L) {
                valores.put("@CalculoRelacaoCinturaQuadril", new String[]{"Cálculo Relação Cintura Quadril", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getCalculoRelacaoCinturaQuadril())});
            }
            if (atendimentoPrimario.getMotivoConsulta() != null) {
                valores.put("@MotivoConsulta", new String[]{"Motivo Consulta", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getMotivoConsulta())});
            }
            if (atendimentoPrimario.getTemperaturaAxilar() != null && atendimentoPrimario.getTemperaturaAxilar() > 0D) {
                valores.put("@TemperaturaAxilar", new String[]{"Temperatura Axilar", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getTemperaturaAxilar())});
            }
            if (atendimentoPrimario.getTemperaturaIncubadora() != null && atendimentoPrimario.getTemperaturaIncubadora() > 0D) {
                valores.put("@TemperaturaIncubadora", new String[]{"Temperatura Incubadora", Valor.adicionarFormatacaoMonetaria(atendimentoPrimario.getTemperaturaIncubadora())});
            }
            if (atendimentoPrimario.getPressaoArterialMedia() != null && atendimentoPrimario.getPressaoArterialMedia() > 0L) {
                valores.put("@PAM", new String[]{"PA Média", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getPressaoArterialMedia())});
            }
            if (atendimentoPrimario.getNivelCo2() != null && atendimentoPrimario.getNivelCo2() > 0L) {
                valores.put("@NivelCO2", new String[]{"Nível CO2", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getNivelCo2())});
            }
            if (atendimentoPrimario.getPressaoVenosaCentral() != null && atendimentoPrimario.getPressaoVenosaCentral() > 0L) {
                valores.put("@PressaoVenosaCentral", new String[]{"Pressão Venosa Central", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getPressaoVenosaCentral())});
            }
            if (atendimentoPrimario.getPressaoIntraAbdominal() != null && atendimentoPrimario.getPressaoIntraAbdominal() > 0L) {
                valores.put("@PressaoIntraAbdominal", new String[]{"Pressão Intra Abdominal", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getPressaoIntraAbdominal())});
            }
            if (atendimentoPrimario.getDiureseDensidade() != null && atendimentoPrimario.getDiureseDensidade() > 0L) {
                valores.put("@DiureseDensidade", new String[]{"Diurese Densidade", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getDiureseDensidade())});
            }
            if (atendimentoPrimario.getDiureseVolume() != null && atendimentoPrimario.getDiureseVolume() > 0L) {
                valores.put("@DiureseVolume", new String[]{"Diurese Volume", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getDiureseVolume())});
            }
            if (atendimentoPrimario.getCiap() != null) {
                if (!atendimentoPrimario.getCiap().getTituloOriginal().equals(atendimentoPrimario.getCiap().getTituloLeigo())) {
                    valores.put("@Ciap", new String[]{"CIAP", atendimentoPrimario.getCiap().getTituloOriginal() + " / " + atendimentoPrimario.getCiap().getTituloLeigo()});
                }
                valores.put("@Ciap", new String[]{"CIAP", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getCiap().getTituloOriginal())});
            }
            if (atendimentoPrimario.getAtendimento() != null && atendimentoPrimario.getAtendimento().getClassificacaoRisco() != null && atendimentoPrimario.getAtendimento().getClassificacaoRisco().getCodigo() != null) {
                valores.put("@ClassificacaoRisco", new String[]{"Classificação de Risco", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getAtendimento().getClassificacaoRisco().getDescricao())});
            }
            if (atendimentoPrimario.getAtendimento() != null && atendimentoPrimario.getAtendimento().getValorSubClassificacaoRisco() != null) {
                valores.put("@Prioridade", new String[]{"Prioridade", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getAtendimento().getDescricaoSubClassificacaoRisco())});
            }

            if (Coalesce.asLong(atendimentoPrimario.getTotalGlasgow()) > 0L) {
                valores.put("@TotalGlasgow", new String[]{"Total Glasgow", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getTotalGlasgow())});
            }

            if (atendimentoPrimario.getEscalaDor() != null) {
                valores.put("@EscalaDor", new String[]{"Escala de Dor", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getEscalaDor())});
            }

            if (atendimentoPrimario.getHistorico() != null) {
                valores.put("@Historico", new String[]{"Historico", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getHistorico())});
            }

            if (atendimentoPrimario.getObservacao() != null) {
                valores.put("@Observacao", new String[]{"Observa&#231;&#227;o", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getObservacao())});
            }

            if (atendimentoPrimario.getFatorRisco() != null && atendimentoPrimario.getFatorRisco().getDescricao() != null) {
                valores.put("@FatorRisco", new String[]{"Fator Risco", br.com.celk.util.Coalesce.asString(atendimentoPrimario.getFatorRisco().getDescricao())});
            }
            if (atendimentoPrimario.getDum() != null) {
                valores.put("@DUM", new String[]{"DUM", DataUtil.getFormatarDiaMesAno(atendimentoPrimario.getDum())});
            }

            {//Ventilação Mecânica

            }
        }
        return valores;
    }

    public static String getDescricaoHtmlAtendimentoPrimarioMesmaLinha(AtendimentoPrimario atendimentoPrimario) {
        return getDescricaoHtmlAtendimentoPrimarioMesmaLinha(atendimentoPrimario, null, null, null);
    }

    public static String getDescricaoHtmlAtendimentoPrimarioMesmaLinha(AtendimentoPrimario atendimentoPrimario, Usuario usuario) {
        return getDescricaoHtmlAtendimentoPrimarioMesmaLinha(atendimentoPrimario, usuario, null, null);
    }

    public static String getDescricaoHtmlAtendimentoPrimarioMesmaLinha(AtendimentoPrimario atendimentoPrimario, Usuario usuario, List<AtendimentoOxigenoterapia> lstOxigenoterapia, List<SintomaFatorRisco> lstSintomaFatorRisco) {
        Map<String, String[]> valores = montarPropriedadesAtendimentoPrimario(atendimentoPrimario);
        StringBuilder descricaoValor = new StringBuilder();

        if (!valores.isEmpty()) {
            descricaoValor.append("<html>");
            descricaoValor.append("  <head>");
            descricaoValor.append("    ");
            descricaoValor.append("  </head>");
            descricaoValor.append("  <body>");
            /*"    <p style=\"margin-bottom: 0.0; margin-top: 0\">" +
             "      <b><u>Avalia&#231;&#227;o Prim&#225;ria</u>" +
             "</b> - "+Data.formatarDataHora(atendimentoPrimario.getDataAvaliacao()) +"</p>" +*/
            descricaoValor.append("    <p style=\"margin-bottom: 0.0; margin-top: 0\">");

            if (usuario != null && RepositoryComponentDefault.SIM_LONG.equals(usuario.getFlagUsuarioTemporario())) {
                descricaoValor.append("<b>Registrado por: </b>");
                descricaoValor.append(usuario.getNome());
                descricaoValor.append("\n<br/>");
            }

            for (Map.Entry<String, String[]> entry : valores.entrySet()) {
                String[] value = entry.getValue();

                descricaoValor.append("<b>");
                descricaoValor.append(value[0]);
                descricaoValor.append(": </b>");
                descricaoValor.append(value[1]);
                descricaoValor.append(", ");
            }

            appendOxigenoterapia(lstOxigenoterapia, descricaoValor);
            appendSintomasFatorRisco(lstSintomaFatorRisco, descricaoValor);
            descricaoValor.append("</p>");
            descricaoValor.append("</body></html>");
        }

        return descricaoValor.toString();
    }

    private static void appendOxigenoterapia(List<AtendimentoOxigenoterapia> lstOxigenoterapia, StringBuilder descricao) {
        if (CollectionUtils.isNotNullEmpty(lstOxigenoterapia)) {

            StringBuilder oxigenoterapia = new StringBuilder();
            for (AtendimentoOxigenoterapia item : lstOxigenoterapia) {
                oxigenoterapia.append("<br/>")
                        .append("<b>")
                        .append(item.getDescricaoTipoOxigenoterapia())
                        .append("</b>")
                        .append("<br/>");
                if (item.getFrequenciaOxigenio() != null && item.getFrequenciaOxigenio() > 0L) {
                    oxigenoterapia.append("FIO2: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getFrequenciaOxigenio()))
                            .append("<br/>");
                }
                if (item.getFrequenciaRespiratoria() != null && item.getFrequenciaRespiratoria() > 0L) {
                    oxigenoterapia.append("FR: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getFrequenciaRespiratoria()))
                            .append("<br/>");
                }
                if (item.getPressaoPositivaFinalExpiracao() != null && item.getPressaoPositivaFinalExpiracao() > 0L) {
                    oxigenoterapia.append("PEEP: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getPressaoPositivaFinalExpiracao()))
                            .append("<br/>");
                }
                if (item.getPressaoInspiratoria() != null && item.getPressaoInspiratoria() > 0L) {
                    oxigenoterapia.append("PIP: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getPressaoInspiratoria()))
                            .append("<br/>");
                }
                if (item.getOxigenio() != null && item.getOxigenio() > 0L) {
                    oxigenoterapia.append("O2: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getOxigenio()))
                            .append(" l/min")
                            .append("<br/>");
                }
                if (item.getArComprimido() != null && item.getArComprimido() > 0L) {
                    oxigenoterapia.append("Ar Comprimido: ")
                            .append(br.com.celk.util.Coalesce.asString(item.getArComprimido()))
                            .append(" l/min")
                            .append("<br/>");
                }
            }

            if (oxigenoterapia.length() > 0) {
                oxigenoterapia.insert(0, "<br /><br /><b><u>Oxigenoterapia: </u></b><br />");
                descricao.append(oxigenoterapia);
            }
        }
    }

    private static void appendSintomasFatorRisco(List<SintomaFatorRisco> lstSintomaFatorRisco, StringBuilder descricao) {
        if (CollectionUtils.isNotNullEmpty(lstSintomaFatorRisco)) {
            StringBuilder sintomas = new StringBuilder();

            for (SintomaFatorRisco item : lstSintomaFatorRisco) {
                sintomas.append(item.getDescricaoVO()).append("; ");
            }

            if (sintomas.length() > 0) {
                sintomas.setLength(sintomas.length() - 2);

                int lastCommaIndex = descricao.lastIndexOf(",");
                if (lastCommaIndex == descricao.length() - 1) {
                    descricao.setLength(descricao.length() - 1);
                }

                descricao.append(", <b>Sintomas:</b> ").append(sintomas);
            }
        }
    }


    public static String getDescricaoHtmlAtendimentoPrimario(AtendimentoPrimario atendimentoPrimario) {
        Map<String, String[]> valores = montarPropriedadesAtendimentoPrimario(atendimentoPrimario);
        String descricaoValor = "";
        if (!valores.isEmpty()) {
            descricaoValor += "<html>\n"
                    + "  <head>\n"
                    + "    \n"
                    + "  </head>\n"
                    + "  <body>\n"
                    + "    <p style=\"margin-bottom: 0.0; margin-top: 0\">\n"
                    + "      <b><u>Avalia&#231;&#227;o Prim&#225;ria</u>\n"
                    + "</b> - " + Data.formatarDataHora(atendimentoPrimario.getDataAvaliacao()) + "</p>\n"
                    + "    <p style=\"margin-bottom: 0.0; margin-top: 0\">\n"
                    + "      <b>\n"
                    + "</b>    </p>\n";
            for (Map.Entry<String, String[]> entry : valores.entrySet()) {
                String[] value = entry.getValue();

                descricaoValor += "<p style=\"margin-bottom: 0.0; margin-top: 0\">\n"
                        + "<b>" + value[0] + ": </b>" + value[1] + "<b>\n</b></p> <p style=\"margin-bottom: 0.0; margin-top: 0\">\n</p>";
            }
            descricaoValor += "</body>\n</html>\n";
        }
        return descricaoValor;
    }

    public static TabelaCbo validarCboProfissionalProcedimento(TabelaCbo cboInformado, Empresa empresa, Profissional profissional, Procedimento procedimento) throws ValidacaoException, DAOException {
        QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
        param.setEmpresa(empresa);
        param.setProfissional(profissional);
        param.setProcedimento(procedimento);
        List<TabelaCbo> cbos = BOFactory.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);
        if (cbos.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_cbo_profissional_incompativel_cbo_procedimento"));
        }
        if (cboInformado != null) {
            if (cbos.contains(cboInformado)) {
                return cboInformado;
            } else {
                throw new ValidacaoException(Bundle.getStringApplication("msg_cbo_profissional_incompativel_cbo_procedimento"));
            }
        }
        return cbos.get(0);
    }

    public static boolean isProcedimentoContainsCBO(Procedimento procedimento) throws ValidacaoException, DAOException {
        return LoadManager.getInstance(ProcedimentoCbo.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCbo.PROP_ID, ProcedimentoCboPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO), procedimento.getCodigo()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCbo.PROP_ID, ProcedimentoCboPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO)))
                .start()
                .exists();
    }

    public static boolean existePainelCadastrado(Atendimento atendimento) {
        Long count = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
                .addGroup(new QueryCustomGroup(EmpresaNaturezaProcuraTipoAtendimento.PROP_CODIGO, QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_PAINEL, Painel.PROP_CODIGO), QueryCustomParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_EMPRESA, Empresa.PROP_CODIGO), atendimento.getEmpresa().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_CODIGO), atendimento.getNaturezaProcuraTipoAtendimento().getCodigo()))
                .start().getVO();

        return count != 0L;
    }

    public static Atendimento getSituacaoAndProfissionalAtendimento(Atendimento atendimento) {
        Atendimento atendimentoBD = LoadManager.getInstance(Atendimento.class)
                .addProperty(Atendimento.PROP_CODIGO)
                .addProperty(Atendimento.PROP_STATUS)
                .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addParameter(new QueryCustomParameter(Atendimento.PROP_CODIGO, atendimento.getCodigo()))
                .start().getVO();
        return atendimentoBD;
    }

    public static List<EstabelecimentoCerestCnaeDTO> verificarVinculoEstabelecimentoCidCnae(Atendimento atendimento, Cid cid) throws ValidacaoException {
        List<EstabelecimentoCerestCnaeDTO> dtoList = new ArrayList<>();

        if (atendimento.getEstabelecimentoCerest() != null) {
            if (atendimento.getEstabelecimentoCerest() != null && cid != null) {
                List<EstabelecimentoCerestCnae> estabelecimentoCerestCnaeList = LoadManager.getInstance(EstabelecimentoCerestCnae.class)
                        .addProperties(new HQLProperties(EstabelecimentoCerestCnae.class).getProperties())
                        .addProperties(new HQLProperties(TabelaCnae.class, EstabelecimentoCerestCnae.PROP_TABELA_CNAE).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoCerestCnae.PROP_ESTABELECIMENTO_CEREST, atendimento.getEstabelecimentoCerest()))
                        .start().getList();

                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(estabelecimentoCerestCnaeList)) {
                    List<String> gruposList = Lambda.extract(estabelecimentoCerestCnaeList, Lambda.on(EstabelecimentoCerestCnae.class).getTabelaCnae().getGrupo());

                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(gruposList)) {
                        List<CidCerestCnae> cidCerestCnaeList = LoadManager.getInstance(CidCerestCnae.class)
                                .addProperties(new HQLProperties(CidCerestCnae.class).getProperties())
                                .addParameter(new QueryCustom.QueryCustomParameter(CidCerestCnae.PROP_GRUPO, BuilderQueryCustom.QueryParameter.IN, gruposList))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CidCerestCnae.PROP_CODIGO_CID_CEREST, CidCerest.PROP_CODIGO_CID, Cid.PROP_CODIGO), cid))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(cidCerestCnaeList)) {
                            List<String> grupoVinculadosList = Lambda.extract(cidCerestCnaeList, Lambda.on(CidCerestCnae.class).getGrupo());

                            estabelecimentoCerestCnaeList = Lambda.select(estabelecimentoCerestCnaeList, Lambda.having(on(EstabelecimentoCerestCnae.class).getTabelaCnae().getGrupo(), Matchers.isIn(grupoVinculadosList)));

                            EstabelecimentoCerestCnaeDTO dto;

                            for (EstabelecimentoCerestCnae ecc : estabelecimentoCerestCnaeList) {
                                dto = new EstabelecimentoCerestCnaeDTO();
                                dto.setEstabelecimentoCerestCnae(ecc);
                                dtoList.add(dto);
                            }
                        }

                    }
                }
            }
        }
        return dtoList;
    }

    public static ValidacaoEderecoDTO validarEndereco(UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
        if (RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsus.getFlagEstrangeiro()) || usuarioCadsus.getFlagEstrangeiro() == null) {

            ValidacaoEderecoDTO validacaoEderecoDTO = new ValidacaoEderecoDTO();
            UsuarioCadsus usuarioCadsusLoad = null;
            EnderecoUsuarioCadsus enderecoUsuarioCadsus = null;

            { // Validação se possui registro de Endereço no Sistema.
                // Procura Endereço do Domicilio primeiro, senão pega do cadastro
                UsuarioCadsus proxy = on(UsuarioCadsus.class);
                usuarioCadsusLoad = LoadManager.getInstance(UsuarioCadsus.class)
                        .addProperty(path(proxy.getEnderecoDomicilio().getCodigo()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCodigo()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCep()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getBairro()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getTipoLogradouro()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getLogradouro()))
                        .addProperty(path(proxy.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getNumeroLogradouro()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEnderecoDomicilio().getExcluido()), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                        .setId(usuarioCadsus.getCodigo())
                        .start().getVO();
                if (usuarioCadsusLoad != null && usuarioCadsusLoad.getEnderecoDomicilio() != null && usuarioCadsusLoad.getEnderecoDomicilio().getCodigo() != null) {
                    enderecoUsuarioCadsus = usuarioCadsusLoad.getEnderecoDomicilio().getEnderecoUsuarioCadsus();
                } else {
                    usuarioCadsusLoad = LoadManager.getInstance(UsuarioCadsus.class)
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getCodigo()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getCodigo()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getCep()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getCidade()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getBairro()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getTipoLogradouro()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getLogradouro()))
                            .addProperty(path(proxy.getEnderecoUsuarioCadsus().getNumeroLogradouro()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEnderecoUsuarioCadsus().getAtivo()), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.ATIVO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.ATIVO))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEnderecoUsuarioCadsus().getExcluido()), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                            .setId(usuarioCadsus.getCodigo())
                            .start().getVO();
                    if (usuarioCadsusLoad != null) {
                        enderecoUsuarioCadsus = usuarioCadsusLoad.getEnderecoUsuarioCadsus();
                    }
                }
                if (enderecoUsuarioCadsus == null || enderecoUsuarioCadsus.getCodigo() == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_usuario_sem_endereco"));
                    return validacaoEderecoDTO;
                }
            }

            { // Validação do CEP
                CepBrasil cepBrasil;
                if (StringUtils.trimToNull(enderecoUsuarioCadsus.getCep()) != null) {
                    cepBrasil = LoadManager.getInstance(CepBrasil.class)
                            .addProperties(new HQLProperties(CepBrasil.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(CepBrasil.PROP_CEP, new Long(enderecoUsuarioCadsus.getCep())))
                            .start().getVO();

                    if (cepBrasil == null) {
                        validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_CEP_invalido_ou_nao_cadastrado_deseja_continuar"));
                        validacaoEderecoDTO.setValidacaoCep(true);
                        return validacaoEderecoDTO;
                    }
                }
            }

            { // Validação da Cidade
                if (enderecoUsuarioCadsus.getCidade() == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_cidade_nao_cadastrada_favor_cadastrar_para_poder_prosseguir_com_atendimento"));
                    return validacaoEderecoDTO;
                }
            }

            { // Validação do Bairro
                if (StringUtils.trimToNull(enderecoUsuarioCadsus.getBairro()) == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_bairro_nao_cadastrado_favor_cadastrar_para_poder_prosseguir_com_atendimento"));
                    return validacaoEderecoDTO;
                }
            }

            { // Validação do Tipo de Logradouro
                if (enderecoUsuarioCadsus.getTipoLogradouro() == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_tipo_logradouro_nao_cadastrado_favor_cadastrar_para_poder_prosseguir_com_atendimento"));
                    return validacaoEderecoDTO;
                }
            }

            { // Validação do Logradouro
                if (StringUtils.trimToNull(enderecoUsuarioCadsus.getLogradouro()) == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_logradouro_nao_cadastrado_favor_cadastrar_para_poder_prosseguir_com_atendimento"));
                    return validacaoEderecoDTO;
                }
            }

            { // Validação do Numero Logradouro
                if (StringUtils.trimToNull(enderecoUsuarioCadsus.getNumeroLogradouro()) == null) {
                    validacaoEderecoDTO.setMsgValidacao(Bundle.getStringApplication("msg_paciente_numero_logradouro_nao_cadastrado_favor_cadastrar_para_poder_prosseguir_com_atendimento"));
                    return validacaoEderecoDTO;
                }
            }
        }
        return null;
    }

    public static ClassificacaoCids getClassificacaoCid(Cid cid) {
        if (cid == null || cid.getCodigo() == null) {
            return null;
        }

        ClassificacaoCids classificacaoCid =
                ((Cid) LoadManager.getInstance(Cid.class)
                        .addProperties(new HQLProperties(ClassificacaoCids.class, Cid.PROP_CID_CLASSIFICACAO).getProperties())
                        .setId(cid.getCodigo())
                        .start().getVO()
                ).getCidClassificacao();

        return classificacaoCid;
    }

    public static boolean obrigatorioAgendamento(Encaminhamento encaminhamento, TipoProcedimento tipoProcedimento) {
        return RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(encaminhamento.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG)) && isPermiteAgendarNoAtendimento(tipoProcedimento);
    }

    public static boolean obrigatorioAgendamentoMamografia(Mamografia mamografia) {
        if (mamografia != null && mamografia.getExameRequisicao() != null && mamografia.getExameRequisicao().getExame() != null
                && mamografia.getExameRequisicao().getExame().getTipoExame() != null && mamografia.getExameRequisicao().getExame().getTipoExame().getTipoProcedimento() != null
                && mamografia.getExameRequisicao().getExame().getTipoExame().getTipoProcedimento().getCodigo() != null
                && isPermiteAgendarNoAtendimento(mamografia.getExameRequisicao().getExame().getTipoExame().getTipoProcedimento())
                && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(mamografia.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG))) {
            return true;
        }
        return false;
    }

    public static boolean obrigatorioAgendamentoExamePadrao(Exame exame,TipoProcedimento tipoProcedimento) {
        return RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(exame.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG)) && isPermiteAgendarNoAtendimento(tipoProcedimento);
    }

    public static boolean existeSolicitacaoAgendamentoAgendamentoAtendimento(Long codigoTipoProcedimento, Atendimento atendimento, String flagRetorno){
        if(codigoTipoProcedimento == null){
            return false;
        }

        TipoProcedimento tipoProcedimento = LoadManager.getInstance(TipoProcedimento.class)
                .addProperty(TipoProcedimento.PROP_CODIGO)
                .addProperty(TipoProcedimento.PROP_REGULADO)
                .addProperty(TipoProcedimento.PROP_TIPO_AGENDAMENTO)
                .setId(codigoTipoProcedimento)
                .start().getVO();

        if (RepositoryComponentDefault.NAO.equals(tipoProcedimento.getRegulado()) && TipoProcedimento.TIPO_PROCEDIMENTO_DENTRO_DA_REDE.equals(tipoProcedimento.getTipoAgendamento())) {
            LoadManager loadManager = LoadManager.getInstance(SolicitacaoAgendamento.class)
                    .addProperty(SolicitacaoAgendamento.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_STATUS, SolicitacaoAgendamento.STATUS_FILA_ESPERA))
                    .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, tipoProcedimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoAgendamento.PROP_ATENDIMENTO_ORIGEM, Atendimento.PROP_CODIGO), QueryCustomParameter.DIFERENTE, atendimento.getCodigo(), HQLHelper.RESOLVE_CHAR_TYPE, 0L));
            if(flagRetorno != null){
                if (RepositoryComponentDefault.SIM.equals(flagRetorno)) {
                    loadManager.addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_CONSULTA, SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO));
                } else {
                    loadManager.addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_CONSULTA, SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL));
                }
            }

            return loadManager.exists();
        }
        return false;
    }

    public static boolean isPermiteAgendarNoAtendimento(TipoProcedimento tipoProcedimento) {
        tipoProcedimento = findTipoProcedimento(tipoProcedimento);
        return tipoProcedimento != null && RepositoryComponentDefault.SIM_LONG.equals(tipoProcedimento.getFlagAgendarNoAtendimento());
    }

    private static TipoProcedimento findTipoProcedimento(TipoProcedimento tipoProcedimento) {
        return tipoProcedimento != null ? LoadManager.getInstance(TipoProcedimento.class)
                                                     .addProperty(PROP_FLAG_AGENDAR_NO_ATENDIMENTO)
                                                     .setId(tipoProcedimento.getCodigo())
                                                     .start().getVO() : null;
    }

    public static List<TipoAtendimentoProcedimentoTabela> carregarTipoAtendimentoProcedimentoTabela(TipoAtendimento ta) {
        return LoadManager.getInstance(TipoAtendimentoProcedimentoTabela.class)
                .addProperties(new HQLProperties(TipoAtendimentoProcedimentoTabela.class).getProperties())
                .addProperties(new HQLProperties(Procedimento.class, TipoAtendimentoProcedimentoTabela.PROP_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimentoProcedimentoTabela.PROP_TIPO_ATENDIMENTO, ta))
                .start().getList();
    }

    public static List<TipoAtendimentoProcedimento> carregarProcedimentos(TipoAtendimento tipoAtendimento) {
        return LoadManager.getInstance(TipoAtendimentoProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimentoProcedimento.PROP_TIPO_ATENDIMENTO), tipoAtendimento))
                .start().getList();
    }

    public static boolean isValidarUsuarioTemporario(Long flagUsuarioTemporario, String flagIdentificavel) {
        try {
            String validarUsuarioTemporario = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("validarUsuarioTemporario");
            return RepositoryComponentDefault.SIM.equals(validarUsuarioTemporario) 
                    && flagIdentificavel.equals(RepositoryComponentDefault.NAO) 
                    && flagUsuarioTemporario.equals(RepositoryComponentDefault.SIM_LONG);
        } catch (DAOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static Date getUltimoAtendimentoGestante(Atendimento atendimento) {
        if (RepositoryComponentDefault.SIM_LONG.equals(atendimento.getFlagGestante())) {
            Atendimento ultimoAtendimento = LoadManager.getInstance(Atendimento.class)
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_DUM_GESTANTE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS), atendimento.getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_DATA_FECHAMENTO), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Atendimento.PROP_DATA_CADASTRO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();

            if (ultimoAtendimento != null && ultimoAtendimento.getDumGestante() != null)
                return zerarHora(ultimoAtendimento.getDumGestante());
        }
        return null;
    }
    public static Atendimento getUltimoAtendimentoGestanteInfo(Atendimento atendimento) {
        if (RepositoryComponentDefault.SIM_LONG.equals(atendimento.getFlagGestante())) {
            Atendimento ultimoAtendimento = LoadManager.getInstance(Atendimento.class)
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_DUM_GESTANTE))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_DPP_USG))
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_DATA_PRIMEIRA_USG))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS), atendimento.getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_DATA_FECHAMENTO), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Atendimento.PROP_DATA_CADASTRO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();

            if (ultimoAtendimento != null)
                return ultimoAtendimento;
        }
        return null;
    }
}