package br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.procedimento.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProcedimentoDTO implements Serializable {

    private Procedimento procedimento;
    private ProcedimentoCompetencia procedimentoCompetencia;
    private List<ProcedimentoCbo> procedimentoCbos = new ArrayList();
    private List<ProcedimentoTipoTabela> procedimentoTipoTabelas = new ArrayList();
    private List<ProcedimentoRegistroEmpresa> procedimentoRegistroEmpresas = new ArrayList();

    public ProcedimentoDTO() {
    }

    public ProcedimentoDTO(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public Procedimento getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public List<ProcedimentoCbo> getProcedimentoCbos() {
        return procedimentoCbos;
    }

    public void setProcedimentoCbos(List<ProcedimentoCbo> procedimentoCbos) {
        this.procedimentoCbos = procedimentoCbos;
    }

    public ProcedimentoCompetencia getProcedimentoCompetencia() {
        return procedimentoCompetencia;
    }

    public void setProcedimentoCompetencia(ProcedimentoCompetencia procedimentoCompetencia) {
        this.procedimentoCompetencia = procedimentoCompetencia;
    }

    public List<ProcedimentoTipoTabela> getProcedimentoTipoTabelas() {
        return procedimentoTipoTabelas;
    }

    public void setProcedimentoTipoTabelas(List<ProcedimentoTipoTabela> procedimentoTipoTabelas) {
        this.procedimentoTipoTabelas = procedimentoTipoTabelas;
    }

    public List<ProcedimentoRegistroEmpresa> getProcedimentoRegistroEmpresas() {
        return procedimentoRegistroEmpresas;
    }

    public void setProcedimentoRegistroEmpresas(List<ProcedimentoRegistroEmpresa> procedimentoRegistroEmpresas) {
        this.procedimentoRegistroEmpresas = procedimentoRegistroEmpresas;
    }
}
