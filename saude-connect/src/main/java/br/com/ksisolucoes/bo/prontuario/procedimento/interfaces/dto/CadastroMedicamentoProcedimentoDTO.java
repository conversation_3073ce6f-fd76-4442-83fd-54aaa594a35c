package br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.dispensacao.MedicamentoProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class CadastroMedicamentoProcedimentoDTO implements Serializable{
    
    private MedicamentoProcedimento medicamentoProcedimento;
    private ProcedimentoCompetencia procedimentoCompetencia;

    public MedicamentoProcedimento getMedicamentoProcedimento() {
        return medicamentoProcedimento;
    }

    public void setMedicamentoProcedimento(MedicamentoProcedimento medicamentoProcedimento) {
        this.medicamentoProcedimento = medicamentoProcedimento;
    }

    public ProcedimentoCompetencia getProcedimentoCompetencia() {
        return procedimentoCompetencia;
    }

    public void setProcedimentoCompetencia(ProcedimentoCompetencia procedimentoCompetencia) {
        this.procedimentoCompetencia = procedimentoCompetencia;
    }
    
}
