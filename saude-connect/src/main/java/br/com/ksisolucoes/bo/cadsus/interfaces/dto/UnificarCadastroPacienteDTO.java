package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class UnificarCadastroPacienteDTO implements Serializable {
    
    public static final String PROP_USUARIO_CADSUS_DESTINO = "usuarioCadsusDestino";
    
    private UsuarioCadsus usuarioCadsusDestino;
    private List<UsuarioCadsus> usuarioCadsusOrigemList;

    public UsuarioCadsus getUsuarioCadsusDestino() {
        return usuarioCadsusDestino;
    }

    public void setUsuarioCadsusDestino(UsuarioCadsus usuarioCadsusDestino) {
        this.usuarioCadsusDestino = usuarioCadsusDestino;
    }

    public List<UsuarioCadsus> getUsuarioCadsusOrigemList() {
        return usuarioCadsusOrigemList;
    }

    public void setUsuarioCadsusOrigemList(List<UsuarioCadsus> usuarioCadsusOrigemList) {
        this.usuarioCadsusOrigemList = usuarioCadsusOrigemList;
    }  
}