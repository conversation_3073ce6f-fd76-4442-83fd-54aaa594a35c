package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FormularioTriagemCovid19DTO implements Serializable {

    private Long codigo;
    private Date dataPrimeirosSintomas;
    private Long flagHistoricoViagemForaBrasil;
    private Long contatoPacienteSuspeitoCovid;
    private String localContatoSuspeito;
    private Long contatoPacienteConfirmado;
    private Long profissionalSaude;
    private String descricaoOcupacaoProfissionalSaude;
    private String localContatoConfirmado;
    private String outrosSintomas;
    private SituacaoSaudePaciente situacaoSaudePaciente;
    private List<Long> sintomas;
    private List<Long> sinaisClinicos;
    private List<Long> morbidades;
    private Atendimento atendimento;
    private Long resultadoPontuacao;
    private String localTrabalho;
    private String localViagem;
    private Long quantidadePessoasCasa;
    private Long quantidadePessoas14Dias;

    public Long getQuantidadePessoasCasa() {
        return quantidadePessoasCasa;
    }

    public void setQuantidadePessoasCasa(Long quantidadePessoasCasa) {
        this.quantidadePessoasCasa = quantidadePessoasCasa;
    }

    public Long getQuantidadePessoas14Dias() {
        return quantidadePessoas14Dias;
    }

    public void setQuantidadePessoas14Dias(Long quantidadePessoas14Dias) {
        this.quantidadePessoas14Dias = quantidadePessoas14Dias;
    }

    public String getLocalTrabalho() {
        return localTrabalho;
    }

    public void setLocalTrabalho(String localTrabalho) {
        this.localTrabalho = localTrabalho;
    }

    public String getLocalViagem() {
        return localViagem;
    }

    public void setLocalViagem(String localViagem) {
        this.localViagem = localViagem;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Date getDataPrimeirosSintomas() {
        return dataPrimeirosSintomas;
    }

    public void setDataPrimeirosSintomas(Date dataPrimeirosSintomas) {
        this.dataPrimeirosSintomas = dataPrimeirosSintomas;
    }

    public Long getFlagHistoricoViagemForaBrasil() {
        return flagHistoricoViagemForaBrasil;
    }

    public void setFlagHistoricoViagemForaBrasil(Long flagHistoricoViagemForaBrasil) {
        this.flagHistoricoViagemForaBrasil = flagHistoricoViagemForaBrasil;
    }

    public Long getContatoPacienteSuspeitoCovid() {
        return contatoPacienteSuspeitoCovid;
    }

    public void setContatoPacienteSuspeitoCovid(Long contatoPacienteSuspeitoCovid) {
        this.contatoPacienteSuspeitoCovid = contatoPacienteSuspeitoCovid;
    }

    public String getLocalContatoSuspeito() {
        return localContatoSuspeito;
    }

    public void setLocalContatoSuspeito(String localContatoSuspeito) {
        this.localContatoSuspeito = localContatoSuspeito;
    }

    public Long getContatoPacienteConfirmado() {
        return contatoPacienteConfirmado;
    }

    public void setContatoPacienteConfirmado(Long contatoPacienteConfirmado) {
        this.contatoPacienteConfirmado = contatoPacienteConfirmado;
    }

    public String getLocalContatoConfirmado() {
        return localContatoConfirmado;
    }

    public void setLocalContatoConfirmado(String localContatoConfirmado) {
        this.localContatoConfirmado = localContatoConfirmado;
    }

    public SituacaoSaudePaciente getSituacaoSaudePaciente() {
        return situacaoSaudePaciente;
    }

    public void setSituacaoSaudePaciente(SituacaoSaudePaciente situacaoSaudePaciente) {
        this.situacaoSaudePaciente = situacaoSaudePaciente;
    }

    public List<Long> getSintomas() {
        return sintomas = sintomas != null ? sintomas : new ArrayList<Long>();
    }

    public void setSintomas(List<Long> sintomas) {
        this.sintomas = sintomas;
    }

    public List<Long> getSinaisClinicos() {
        return sinaisClinicos = sinaisClinicos != null ? sinaisClinicos : new ArrayList<Long>();
    }

    public void setSinaisClinicos(List<Long> sinaisClinicos) {
        this.sinaisClinicos = sinaisClinicos;
    }

    public List<Long> getMorbidades() {
        return morbidades = morbidades != null ? morbidades : new ArrayList<Long>();
    }

    public void setMorbidades(List<Long> morbidades) {
        this.morbidades = morbidades;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Long getProfissionalSaude() {
        return profissionalSaude;
    }

    public void setProfissionalSaude(Long profissionalSaude) {
        this.profissionalSaude = profissionalSaude;
    }

    public String getDescricaoOcupacaoProfissionalSaude() {
        return descricaoOcupacaoProfissionalSaude;
    }

    public void setDescricaoOcupacaoProfissionalSaude(String descricaoOcupacaoProfissionalSaude) {
        this.descricaoOcupacaoProfissionalSaude = descricaoOcupacaoProfissionalSaude;
    }

    public String getOutrosSintomas() {
        return outrosSintomas;
    }

    public void setOutrosSintomas(String outrosSintomas) {
        this.outrosSintomas = outrosSintomas;
    }

    public boolean isAtributosNaoNulos() {
        for (Field field : getClass().getDeclaredFields()) {
            try {
                if (field.get(this) != null)
                    return true;
            } catch (IllegalAccessException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return false;
    }

    public Long getResultadoPontuacao() {
        return resultadoPontuacao;
    }

    public String getDescricaoResultadoPontuacao(Long valorMinimoSuspeitoCovid19) {
        if (valorMinimoSuspeitoCovid19 != null && resultadoPontuacao != null) {
            return resultadoPontuacao >= valorMinimoSuspeitoCovid19 ? "Caso suspeito de Covid-19" : "";
        }
        return "";
    }

    public void setResultadoPontuacao(Long resultadoPontuacao) {
        this.resultadoPontuacao = resultadoPontuacao;
    }

    public String getDataPrimeirosSintomasFormatada() {
        return DataUtil.getFormatarDiaMesAno(this.getDataPrimeirosSintomas());
    }

    public String hasSintomaAsString(Long sintoma) {
        return this.getSintomas().contains(sintoma) ? "Sim" : "Não";
    }

    public String hasMorbidadeAsString(Long sintoma) {
        return this.getMorbidades().contains(sintoma) ? "Sim" : "Não";
    }

    public String getProfissionalSaudeAsString() {
        return RepositoryComponentDefault.SIM_LONG.equals(this.getProfissionalSaude()) ? "Sim" : "Não";
    }

    public String getContatoPacienteSuspeitoCovidAsString() {
        if (RepositoryComponentDefault.SIM_LONG.equals(this.getContatoPacienteSuspeitoCovid())) {
            return "Sim";
        } else if (RepositoryComponentDefault.NAO_SABE_LONG.equals(this.getContatoPacienteSuspeitoCovid())) {
            return "Não Sabe";
        }
        return "Não";
    }

    public String getContatoPacienteConfirmadoAsString() {
        if (RepositoryComponentDefault.SIM_LONG.equals(this.getContatoPacienteConfirmado())) {
            return "Sim";
        } else if (RepositoryComponentDefault.NAO_SABE_LONG.equals(this.getContatoPacienteConfirmado())) {
            return "Não Sabe";
        }
        return "Não";
    }
}
