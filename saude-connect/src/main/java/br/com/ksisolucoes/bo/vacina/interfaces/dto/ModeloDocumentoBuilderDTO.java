package br.com.ksisolucoes.bo.vacina.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ModeloDocumentoBuilderDTO implements Serializable {
    
    private String descricaoContribuinte;
    private String enderecoContribuinte;
    private String numeroProcessoAdministrativo;
    private String numeroAuto;
    private String descricaoManual;
    private String textoDefesaRecursoExterno;

    public String getDescricaoContribuinte() {
        return descricaoContribuinte;
    }

    public void setDescricaoContribuinte(String descricaoContribuinte) {
        this.descricaoContribuinte = descricaoContribuinte;
    }

    public String getEnderecoContribuinte() {
        return enderecoContribuinte;
    }

    public void setEnderecoContribuinte(String enderecoContribuinte) {
        this.enderecoContribuinte = enderecoContribuinte;
    }

    public String getNumeroProcessoAdministrativo() {
        return numeroProcessoAdministrativo;
    }

    public void setNumeroProcessoAdministrativo(String numeroProcessoAdministrativo) {
        this.numeroProcessoAdministrativo = numeroProcessoAdministrativo;
    }

    public String getNumeroAuto() {
        return numeroAuto;
    }

    public void setNumeroAuto(String numeroAuto) {
        this.numeroAuto = numeroAuto;
    }

    public String getDescricaoManual() {
        return descricaoManual;
    }

    public void setDescricaoManual(String descricaoManual) {
        this.descricaoManual = descricaoManual;
    }

    public String getTextoDefesaRecursoExterno() {
        return textoDefesaRecursoExterno;
    }

    public void setTextoDefesaRecursoExterno(String textoDefesaRecursoExterno) {
        this.textoDefesaRecursoExterno = textoDefesaRecursoExterno;
    }
}
