package br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.procedimento.TipoTabelaProcedimento;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProcedimentoDTOParam implements Serializable {

    private Long codigo;
    private String referencia;
    private String descricao;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private boolean faturavel;
    private Convenio convenio;
    private List<TipoTabelaProcedimento> tipoTabelaProcedimentoList = new ArrayList<TipoTabelaProcedimento>();

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public boolean isFaturavel() {
        return faturavel;
    }

    public void setFaturavel(boolean faturavel) {
        this.faturavel = faturavel;
    }

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public List<TipoTabelaProcedimento> getTipoTabelaProcedimentoList() {
        return tipoTabelaProcedimentoList;
    }

    public void setTipoTabelaProcedimentoList(List<TipoTabelaProcedimento> tipoTabelaProcedimentoList) {
        this.tipoTabelaProcedimentoList = tipoTabelaProcedimentoList;
    }
}
