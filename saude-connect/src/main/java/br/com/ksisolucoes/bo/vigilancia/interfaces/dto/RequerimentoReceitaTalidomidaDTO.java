package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaTalidomida;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaTalidomida;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RequerimentoReceitaTalidomidaDTO implements Serializable {

    private RequerimentoReceitaTalidomida requerimentoReceitaTalidomida;
    private List<TalonarioReceitaTalidomida> receitaTalidomidaList = new ArrayList<TalonarioReceitaTalidomida>();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList = new ArrayList<>();
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList;
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir;

    public RequerimentoReceitaTalidomida getRequerimentoReceitaTalidomida() {
        return requerimentoReceitaTalidomida;
    }

    public void setRequerimentoReceitaTalidomida(RequerimentoReceitaTalidomida requerimentoReceitaTalidomida) {
        this.requerimentoReceitaTalidomida = requerimentoReceitaTalidomida;
    }

    public List<TalonarioReceitaTalidomida> getReceitaTalidomidaList() {
        return receitaTalidomidaList;
    }

    public void setReceitaTalidomidaList(List<TalonarioReceitaTalidomida> receitaTalidomidaList) {
        this.receitaTalidomidaList = receitaTalidomidaList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoDTOList() {
        if(CollectionUtils.isEmpty(requerimentoVigilanciaAnexoDTOList)){
            requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        }
        return requerimentoVigilanciaAnexoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList) {
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoExcluidoDTOList() {
        return requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoExcluidoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaList() {
        return eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaList = eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaExcluirList() {
        return eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaExcluirList = eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalListExcluir() {
        return requerimentoVigilanciaFiscalListExcluir;
    }

    public void setRequerimentoVigilanciaFiscalListExcluir(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir) {
        this.requerimentoVigilanciaFiscalListExcluir = requerimentoVigilanciaFiscalListExcluir;
    }
}
