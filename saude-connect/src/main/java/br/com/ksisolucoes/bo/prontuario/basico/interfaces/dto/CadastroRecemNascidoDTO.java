package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoRecemNascido;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroRecemNascidoDTO implements Serializable{
    
    private AtendimentoRecemNascido atendimentoRecemNascido;
    private UsuarioCadsus usuarioCadsus;
    private List<CadastroRecemNascidoItemDTO> cadastroRecemNascidoItemDTOdtos;

    public AtendimentoRecemNascido getAtendimentoRecemNascido() {
        return atendimentoRecemNascido;
    }

    public void setAtendimentoRecemNascido(AtendimentoRecemNascido atendimentoRecemNascido) {
        this.atendimentoRecemNascido = atendimentoRecemNascido;
    }

    public List<CadastroRecemNascidoItemDTO> getCadastroRecemNascidoItemDTOdtos() {
        return cadastroRecemNascidoItemDTOdtos;
    }

    public void setCadastroRecemNascidoItemDTOdtos(List<CadastroRecemNascidoItemDTO> cadastroRecemNascidoItemDTOdtos) {
        this.cadastroRecemNascidoItemDTOdtos = cadastroRecemNascidoItemDTOdtos;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
}
