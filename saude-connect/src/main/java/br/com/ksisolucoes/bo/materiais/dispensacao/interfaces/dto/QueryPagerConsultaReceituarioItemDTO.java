package br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaReceituarioItemDTO implements Serializable {

    public static final String PROP_RECEITUARIO_ITEM = "receituarioItem";

    private ReceituarioItem receituarioItem;
    private Long saldo;

    public ReceituarioItem getReceituarioItem() {
        return receituarioItem;
    }

    public void setReceituarioItem(ReceituarioItem receituarioItem) {
        this.receituarioItem = receituarioItem;
    }

    public Long getSaldo() {
        return saldo;
    }

    public void setSaldo(Long saldo) {
        this.saldo = saldo;
    }

}
