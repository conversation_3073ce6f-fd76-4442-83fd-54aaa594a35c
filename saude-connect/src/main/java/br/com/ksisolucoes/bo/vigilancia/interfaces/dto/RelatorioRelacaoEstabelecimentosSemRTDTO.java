package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoEstabelecimentosSemRTDTO implements Serializable {
    
    private Estabelecimento estabelecimento;
    private String descricaoAtividadePrincipal;

    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getDescricaoAtividadePrincipal() {
        return descricaoAtividadePrincipal;
    }

    public void setDescricaoAtividadePrincipal(String descricaoAtividadePrincipal) {
        this.descricaoAtividadePrincipal = descricaoAtividadePrincipal;
    }
    
}
