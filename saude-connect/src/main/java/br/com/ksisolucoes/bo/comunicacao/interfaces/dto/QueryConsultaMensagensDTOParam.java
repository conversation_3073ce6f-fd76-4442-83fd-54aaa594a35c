package br.com.ksisolucoes.bo.comunicacao.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class QueryConsultaMensagensDTOParam implements Serializable {

    private String de;
    private String para;
    private String propOrdenacao;
    private Usuario usuario;
    private Empresa unidade;
    private Mensagem.Tipo tipo;
    private Long codigoMensagem;
    private boolean excluida;
    private boolean asc;

    public Long getCodigoMensagem() {
        return codigoMensagem;
    }

    public void setCodigoMensagem(Long codigoMensagem) {
        this.codigoMensagem = codigoMensagem;
    }

    public Mensagem.Tipo getTipo() {
        return tipo;
    }

    public void setTipo(Mensagem.Tipo tipo) {
        this.tipo = tipo;
    }

    public String getPropOrdenacao() {
        return propOrdenacao;
    }

    public void setPropOrdenacao(String propOrdenacao) {
        this.propOrdenacao = propOrdenacao;
    }

    public boolean isExcluida() {
        return excluida;
    }

    public void setExcluida(boolean excluida) {
        this.excluida = excluida;
    }

    public boolean isAsc() {
        return asc;
    }

    public void setAsc(boolean asc) {
        this.asc = asc;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getDe() {
        return de;
    }

    public void setDe(String de) {
        this.de = de;
    }

    public String getPara() {
        return para;
    }

    public void setPara(String para) {
        this.para = para;
    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }
}