package br.com.ksisolucoes.bo.programasaude.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.bo.programasaude.interfaces.dto.GrupoProgramaSaudeDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.programasaude.Hiperdia;
import br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario.TipoProgramaSaude;
import java.util.List;

/**                                                            .
 * <AUTHOR>                                   .
 *                                                             . 
 */              
@EJBLocation("br.com.ksisolucoes.bo.programasaude.ProgramaSaudeBO")
public interface ProgramaSaudeFacade extends FacadeBO {

    public Hiperdia cadastrarHiperdia(UsuarioCadsus usuarioCadsus,Hiperdia hiperdia, List<HiperdiaMedicamento> medicamentos) throws DAOException, ValidacaoException;

    public void removerHiperdia(Hiperdia hiperdia) throws DAOException, ValidacaoException;

    public ProgramaSaudeUsuario cadastrarProgramaSaude(UsuarioCadsus usuarioCadsus, TipoProgramaSaude tipo) throws DAOException, ValidacaoException;

    public void salvarGrupoProgramaSaude(GrupoProgramaSaudeDTO dto) throws DAOException, ValidacaoException;

    public void excluirGrupoProgramaSaude(GrupoProgramaSaudeDTO dto) throws DAOException, ValidacaoException;

}
