package br.com.ksisolucoes.bo.materiais.recebimento.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class NotaFiscalOrdemCompraDTO implements Serializable{
    
    private RegistroItemNotaFiscal registroItemNotaFiscal;
    private List<OrdemCompraItem> ordemCompraItemList;

    public RegistroItemNotaFiscal getRegistroItemNotaFiscal() {
        return registroItemNotaFiscal;
    }

    public void setRegistroItemNotaFiscal(RegistroItemNotaFiscal registroItemNotaFiscal) {
        this.registroItemNotaFiscal = registroItemNotaFiscal;
    }

    public List<OrdemCompraItem> getOrdemCompraItemList() {
        return ordemCompraItemList;
    }

    public void setOrdemCompraItemList(List<OrdemCompraItem> ordemCompraItemList) {
        this.ordemCompraItemList = ordemCompraItemList;
    }
    
}
