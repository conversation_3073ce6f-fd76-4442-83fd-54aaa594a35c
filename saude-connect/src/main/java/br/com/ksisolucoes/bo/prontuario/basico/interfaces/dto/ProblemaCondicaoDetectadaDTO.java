package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.prontuario.basico.GrupoProblemasCondicoes;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by sulivan on 24/01/18.
 */
public class ProblemaCondicaoDetectadaDTO implements Serializable {

    private List<GrupoProblemasCondicoes> grupoProblemasCondicoesList;

    public List<GrupoProblemasCondicoes> getGrupoProblemasCondicoesList() {
        if(CollectionUtils.isEmpty(grupoProblemasCondicoesList)){
            grupoProblemasCondicoesList = new ArrayList<>();
        }
        return grupoProblemasCondicoesList;
    }

    public void setGrupoProblemasCondicoesList(List<GrupoProblemasCondicoes> grupoProblemasCondicoesList) {
        this.grupoProblemasCondicoesList = grupoProblemasCondicoesList;
    }
}
