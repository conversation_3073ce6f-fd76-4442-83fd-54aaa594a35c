package br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto;

import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DevolucaoMedicamentoDTO implements Serializable{
    
    private DevolucaoMedicamento devolucaoMedicamento;
    private List<DevolucaoMedicamentoItem2DTO> dtosItens;

    public DevolucaoMedicamento getDevolucaoMedicamento() {
        return devolucaoMedicamento;
    }

    public void setDevolucaoMedicamento(DevolucaoMedicamento devolucaoMedicamento) {
        this.devolucaoMedicamento = devolucaoMedicamento;
    }

    public List<DevolucaoMedicamentoItem2DTO> getDtosItens() {
        return dtosItens;
    }

    public void setDtosItens(List<DevolucaoMedicamentoItem2DTO> dtosItens) {
        this.dtosItens = dtosItens;
    }

}
