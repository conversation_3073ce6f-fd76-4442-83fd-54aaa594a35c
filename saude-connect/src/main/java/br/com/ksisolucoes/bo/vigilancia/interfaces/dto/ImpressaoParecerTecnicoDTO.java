package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjetoParecer;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ImpressaoParecerTecnicoDTO implements Serializable {

    private RequerimentoAnaliseProjetoParecer requerimentoAnaliseProjetoParecer;
    private RequerimentoAnaliseProjeto requerimentoAnaliseProjeto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoProjetoVigilancia tipoProjetoVigilancia;
    private String descricaoDimensionamentoAdequacao;
    private String descricaoFuncionalidadeEdificacao;
    private String descricaoInstalacoesOrdinarias;
    private String descricaoRecomendacoes;
    private String descricaoInscricoesImobiliarias;
    private String descricaoResponsaveisTecnicos;
    private String descricaoRodape;
    private String descricaoStatus;
    private Date dataRetorno;
    private String resposta;
    private String anexos;

    private List<Profissional> fiscais;
    private String urlQrCode;

    public RequerimentoAnaliseProjetoParecer getRequerimentoAnaliseProjetoParecer() {
        return requerimentoAnaliseProjetoParecer;
    }

    public void setRequerimentoAnaliseProjetoParecer(RequerimentoAnaliseProjetoParecer requerimentoAnaliseProjetoParecer) {
        this.requerimentoAnaliseProjetoParecer = requerimentoAnaliseProjetoParecer;
    }

    public RequerimentoAnaliseProjeto getRequerimentoAnaliseProjeto() {
        return requerimentoAnaliseProjeto;
    }

    public void setRequerimentoAnaliseProjeto(RequerimentoAnaliseProjeto requerimentoAnaliseProjeto) {
        this.requerimentoAnaliseProjeto = requerimentoAnaliseProjeto;
    }

    public String getDescricaoDimensionamentoAdequacao() {
        return descricaoDimensionamentoAdequacao;
    }

    public void setDescricaoDimensionamentoAdequacao(String descricaoDimensionamentoAdequacao) {
        this.descricaoDimensionamentoAdequacao = descricaoDimensionamentoAdequacao;
    }

    public String getDescricaoFuncionalidadeEdificacao() {
        return descricaoFuncionalidadeEdificacao;
    }

    public void setDescricaoFuncionalidadeEdificacao(String descricaoFuncionalidadeEdificacao) {
        this.descricaoFuncionalidadeEdificacao = descricaoFuncionalidadeEdificacao;
    }

    public String getDescricaoInstalacoesOrdinarias() {
        return descricaoInstalacoesOrdinarias;
    }

    public void setDescricaoInstalacoesOrdinarias(String descricaoInstalacoesOrdinarias) {
        this.descricaoInstalacoesOrdinarias = descricaoInstalacoesOrdinarias;
    }

    public String getDescricaoRecomendacoes() {
        return descricaoRecomendacoes;
    }

    public void setDescricaoRecomendacoes(String descricaoRecomendacoes) {
        this.descricaoRecomendacoes = descricaoRecomendacoes;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public String getDescricaoInscricoesImobiliarias() {
        return descricaoInscricoesImobiliarias;
    }

    public void setDescricaoInscricoesImobiliarias(String descricaoInscricoesImobiliarias) {
        this.descricaoInscricoesImobiliarias = descricaoInscricoesImobiliarias;
    }

    public String getDescricaoResponsaveisTecnicos() {
        return descricaoResponsaveisTecnicos;
    }

    public void setDescricaoResponsaveisTecnicos(String descricaoResponsaveisTecnicos) {
        this.descricaoResponsaveisTecnicos = descricaoResponsaveisTecnicos;
    }

    public TipoProjetoVigilancia getTipoProjetoVigilancia() {
        return tipoProjetoVigilancia;
    }

    public void setTipoProjetoVigilancia(TipoProjetoVigilancia tipoProjetoVigilancia) {
        this.tipoProjetoVigilancia = tipoProjetoVigilancia;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public String getDescricaoStatus() {
        if (getRequerimentoAnaliseProjetoParecer() != null) {
            Long status = getRequerimentoAnaliseProjetoParecer().getStatus();
            if (status != null) {
                RequerimentoVigilancia.Situacao situacao = RequerimentoVigilancia.Situacao.valeuOf(status);
                if (situacao != null) {
                    return situacao.descricao();
                }
            }
        }
        return "";
    }

    public void setDescricaoStatus(String descricaoStatus) {
        this.descricaoStatus = descricaoStatus;
    }

    public Date getDataRetorno() {
        return dataRetorno;
    }

    public void setDataRetorno(Date dataRetorno) {
        this.dataRetorno = dataRetorno;
    }

    public List<Profissional> getFiscais() {
        return fiscais;
    }

    public void setFiscais(List<Profissional> fiscais) {
        this.fiscais = fiscais;
    }

    public String getDescricaoRodape() {
        return descricaoRodape;
    }

    public void setDescricaoRodape(String descricaoRodape) {
        this.descricaoRodape = descricaoRodape;
    }

    public String getUrlQrCode() {
        return urlQrCode;
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }
}