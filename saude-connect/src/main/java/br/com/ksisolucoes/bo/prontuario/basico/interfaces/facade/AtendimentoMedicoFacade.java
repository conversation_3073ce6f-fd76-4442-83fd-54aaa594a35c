package br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedico;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento.TiposAtendimento;

/**                                                            .
 * <AUTHOR>                                   .
 * <AUTHOR>                      .
 *                                                             . 
 */                                                   
@EJBLocation("br.com.ksisolucoes.bo.prontuario.basico.AtendimentoMedicoBO")
public interface AtendimentoMedicoFacade extends FacadeBO {

    public AtendimentoMedico solucionarAtendimentoMedico(Long codigo) throws DAOException, ValidacaoException;

    public Long getMediaTempoEspera(TiposAtendimento tipoAtendimento) throws ValidacaoException, DAOException;

    public AtendimentoMedico iniciarAtendimentoMedico(Atendimento atendimento, Long codigoProfissional) throws DAOException, ValidacaoException;
}
