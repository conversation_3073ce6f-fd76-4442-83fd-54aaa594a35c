package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConvenioPacienteDTO implements Serializable{

    private Convenio convenio;
    private Convenio subConvenio;
    private ConvenioPaciente convenioPaciente;

    public ConvenioPacienteDTO(ConvenioPaciente convenioPaciente) {
        this.convenioPaciente = convenioPaciente;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Convenio getSubConvenio() {
        return subConvenio;
    }

    public void setSubConvenio(Convenio subConvenio) {
        this.subConvenio = subConvenio;
    }

//    public Convenio getConvenioExibicao() {
//        if (subConvenio != null) {
//            return subConvenio;
//        } else {
//            return convenio;
//        }
//    }

    public ConvenioPaciente getConvenioPaciente() {
        return convenioPaciente;
    }

    public void setConvenioPaciente(ConvenioPaciente convenioPaciente) {
        this.convenioPaciente = convenioPaciente;
    }
}
