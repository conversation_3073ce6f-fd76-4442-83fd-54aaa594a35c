package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaTalonarioDTOParam implements Serializable{

    private Long codigo;
    private Long numeracaoInicial;
    private String keyword;
    private String propSort;
    private Long Status;
    private boolean ascending;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Long getNumeracaoInicial() {
        return numeracaoInicial;
    }

    public void setNumeracaoInicial(Long numeracaoInicial) {
        this.numeracaoInicial = numeracaoInicial;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getStatus() {
        return Status;
    }

    public void setStatus(Long status) {
        Status = status;
    }
}
