package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import java.io.Serializable;
import java.util.List;

/**
 * Created by sulivan on 31/05/17.
 */
public class RelatorioListaInspecaoDTOParam implements Serializable {

    private List<Long> codigoRequerimentoList;

    public List<Long> getCodigoRequerimentoList() {
        return codigoRequerimentoList;
    }

    public void setCodigoRequerimentoList(List<Long> codigoRequerimentoList) {
        this.codigoRequerimentoList = codigoRequerimentoList;
    }
}
