/*
 * Created on 17/05/2004
 *
 * To change the template for this generated file go to
 * Window - Preferences - Java - Code Generation - Code and Comments
 */
package br.com.ksisolucoes.bo.controle.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.ProgramaPaginaPermissao;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 
 * To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Generation - Code and Comments
 */
@EJBLocation("br.com.ksisolucoes.bo.controle.ControlePermissaoGrupoBO")
public interface ControlePermissaoGrupoFacade extends FacadeBO {

    public Set<Long> getPermissoes(<PERSON> codigoUsuario, Long codigoPrograma) throws DAOException, ValidacaoException;
    
    boolean isAcessoPermitido(Long codigoUsuario, Long codigoPrograma) throws DAOException, ValidacaoException;

    public Map<ProgramaWeb, List<ProgramaPaginaPermissao>> getMapPaginaPermissoes() throws DAOException, ValidacaoException;
    
}
