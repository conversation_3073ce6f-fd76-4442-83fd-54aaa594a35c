package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ImpressaoParecerVistoriaHidrossanitariaDTOParam implements Serializable {

    private String urlQRcode;
    private String chaveQrcode;
    private RequerimentoVigilancia requerimentoVigilancia;

    public String getUrlQRcode() {
        return new StringBuilder().append(urlQRcode).append("?CHQRC=").append(getChaveQrcode()).toString();
    }

    public void setUrlQRcode(String urlQRcode) {
        this.urlQRcode = urlQRcode;
    }

    public String getChaveQrcode() {
        return chaveQrcode;
    }

    public void setChaveQrcode(String chaveQrcode) {
        this.chaveQrcode = chaveQrcode;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }
}