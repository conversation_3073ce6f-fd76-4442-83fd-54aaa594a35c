package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class VigilanciaEnderecoDTOParam implements Serializable{

    private String enderecoDenunciante;
    private String endereco;
    private Long codigo;
    private EquipeMicroArea equipeMicroArea;
    private EquipeArea area;
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public String getEnderecoDenunciante() {
        return enderecoDenunciante;
    }

    public void setEnderecoDenunciante(String enderecoDenunciante) {
        this.enderecoDenunciante = enderecoDenunciante;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public EquipeArea getArea() {
        return area;
    }

    public void setArea(EquipeArea area) {
        this.area = area;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }
}
