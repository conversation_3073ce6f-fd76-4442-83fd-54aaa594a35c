package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.FatorRisco;
import br.com.ksisolucoes.vo.prontuario.basico.FatorRiscoGrupo;

import java.io.Serializable;
import java.util.List;

public class CadastroFatorRiscoDTO implements Serializable {
    private FatorRisco fatorRisco;
    private List<FatorRiscoGrupo> fatorRiscoGrupoList;

    public FatorRisco getFatorRisco() {
        return fatorRisco;
    }

    public void setFatorRisco(FatorRisco fatorRisco) {
        this.fatorRisco = fatorRisco;
    }

    public List<FatorRiscoGrupo> getFatorRiscoGrupoList() {
        return fatorRiscoGrupoList;
    }

    public void setFatorRiscoGrupoList(List<FatorRiscoGrupo> fatorRiscoGrupoList) {
        this.fatorRiscoGrupoList = fatorRiscoGrupoList;
    }
}
