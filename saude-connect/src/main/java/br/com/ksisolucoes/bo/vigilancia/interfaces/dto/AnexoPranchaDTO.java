package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AnexoPranchaDTO implements Serializable {

    private File file;
    private GerenciadorArquivo.OrigemArquivo origemArquivo;
    private String nomeArquivoOriginal;
    private String descricaoAnexo;
    private Usuario usuarioCadastro;
    private Date dataCadastro;
    private Long situacao;
    private Usuario usuarioAlteracao;
    private Date dataAlteracao;

    private RequerimentoProjetoHidrossanitarioDeclaratorio hidrossanitarioDeclaratorio;
    private RequerimentoProjetoHidrossanitarioDeclaratorioAnexo hidrossanitarioDeclaratorioAnexo = new RequerimentoProjetoHidrossanitarioDeclaratorioAnexo();
    private RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario;
    private RequerimentoProjetoHidrossanitarioAnexo requerimentoProjetoHidrossanitarioAnexo = new RequerimentoProjetoHidrossanitarioAnexo();
    private RequerimentoProjetoArquitetonicoSanitario requerimentoProjetoArquitetonicoSanitario;
    private RequerimentoProjetoArquitetonicoSanitarioAnexo requerimentoProjetoArquitetonicoSanitarioAnexo = new RequerimentoProjetoArquitetonicoSanitarioAnexo();
    private GerenciadorArquivo gerenciadorArquivo;

    private List<AnexoPranchaDTO> listAnexosPrancha = new ArrayList<>();
    private List<AnexoPranchaDTO> listAnexosPranchaExcluidos = new ArrayList<>();
    private List<AnexoPranchaDTO> listAnexosMemorial = new ArrayList<>();
    private List<AnexoPranchaDTO> listAnexosMemorialExcluidos = new ArrayList<>();


    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public GerenciadorArquivo.OrigemArquivo getOrigemArquivo() {
        return origemArquivo;
    }

    public void setOrigemArquivo(GerenciadorArquivo.OrigemArquivo origemArquivo) {
        this.origemArquivo = origemArquivo;
    }

    public String getNomeArquivoOriginal() {
        return nomeArquivoOriginal;
    }

    public void setNomeArquivoOriginal(String nomeArquivoOriginal) {
        this.nomeArquivoOriginal = nomeArquivoOriginal;
    }

    public String getDescricaoAnexo() {
        return descricaoAnexo;
    }

    public void setDescricaoAnexo(String descricaoAnexo) {
        this.descricaoAnexo = descricaoAnexo;
    }

    public Usuario getUsuarioCadastro() {
        return usuarioCadastro;
    }

    public void setUsuarioCadastro(Usuario usuarioCadastro) {
        this.usuarioCadastro = usuarioCadastro;
    }

    public Usuario getUsuarioAlteracao() {
        return usuarioAlteracao;
    }

    public void setUsuarioAlteracao(Usuario usuarioAlteracao) {
        this.usuarioAlteracao = usuarioAlteracao;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }


    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public RequerimentoProjetoHidrossanitarioDeclaratorio getHidrossanitarioDeclaratorio() {
        return hidrossanitarioDeclaratorio;
    }

    public void setHidrossanitarioDeclaratorio(RequerimentoProjetoHidrossanitarioDeclaratorio hidrossanitarioDeclaratorio) {
        this.hidrossanitarioDeclaratorio = hidrossanitarioDeclaratorio;
    }

    public RequerimentoProjetoHidrossanitarioDeclaratorioAnexo getHidrossanitarioDeclaratorioAnexo() {
        return hidrossanitarioDeclaratorioAnexo;
    }

    public void setHidrossanitarioDeclaratorioAnexo(RequerimentoProjetoHidrossanitarioDeclaratorioAnexo hidrossanitarioDeclaratorioAnexo) {
        this.hidrossanitarioDeclaratorioAnexo = hidrossanitarioDeclaratorioAnexo;
    }

    public RequerimentoProjetoHidrossanitario getRequerimentoProjetoHidrossanitario() {
        return requerimentoProjetoHidrossanitario;
    }

    public void setRequerimentoProjetoHidrossanitario(RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario) {
        this.requerimentoProjetoHidrossanitario = requerimentoProjetoHidrossanitario;
    }

    public RequerimentoProjetoHidrossanitarioAnexo getRequerimentoProjetoHidrossanitarioAnexo() {
        return requerimentoProjetoHidrossanitarioAnexo;
    }

    public void setRequerimentoProjetoHidrossanitarioAnexo(RequerimentoProjetoHidrossanitarioAnexo requerimentoProjetoHidrossanitarioAnexo) {
        this.requerimentoProjetoHidrossanitarioAnexo = requerimentoProjetoHidrossanitarioAnexo;
    }

    public RequerimentoProjetoArquitetonicoSanitario getRequerimentoProjetoArquitetonicoSanitario() {
        return requerimentoProjetoArquitetonicoSanitario;
    }

    public void setRequerimentoProjetoArquitetonicoSanitario(RequerimentoProjetoArquitetonicoSanitario requerimentoProjetoArquitetonicoSanitario) {
        this.requerimentoProjetoArquitetonicoSanitario = requerimentoProjetoArquitetonicoSanitario;
    }

    public RequerimentoProjetoArquitetonicoSanitarioAnexo getRequerimentoProjetoArquitetonicoSanitarioAnexo() {
        return requerimentoProjetoArquitetonicoSanitarioAnexo;
    }

    public void setRequerimentoProjetoArquitetonicoSanitarioAnexo(RequerimentoProjetoArquitetonicoSanitarioAnexo requerimentoProjetoArquitetonicoSanitarioAnexo) {
        this.requerimentoProjetoArquitetonicoSanitarioAnexo = requerimentoProjetoArquitetonicoSanitarioAnexo;
    }

    public GerenciadorArquivo getGerenciadorArquivo() {
        return gerenciadorArquivo;
    }

    public void setGerenciadorArquivo(GerenciadorArquivo gerenciadorArquivo) {
        this.gerenciadorArquivo = gerenciadorArquivo;
    }

    public List<AnexoPranchaDTO> getListAnexosPrancha() {
        return listAnexosPrancha;
    }

    public void setListAnexosPrancha(List<AnexoPranchaDTO> listAnexosPrancha) {
        this.listAnexosPrancha = listAnexosPrancha;
    }

    public List<AnexoPranchaDTO> getListAnexosPranchaExcluidos() {
        return listAnexosPranchaExcluidos;
    }

    public void setListAnexosPranchaExcluidos(List<AnexoPranchaDTO> listAnexosPranchaExcluidos) {
        this.listAnexosPranchaExcluidos = listAnexosPranchaExcluidos;
    }

    public List<AnexoPranchaDTO> getListAnexosMemorial() {
        return listAnexosMemorial;
    }

    public void setListAnexosMemorial(List<AnexoPranchaDTO> listAnexosMemorial) {
        this.listAnexosMemorial = listAnexosMemorial;
    }

    public List<AnexoPranchaDTO> getListAnexosMemorialExcluidos() {
        return listAnexosMemorialExcluidos;
    }

    public void setListAnexosMemorialExcluidos(List<AnexoPranchaDTO> listAnexosMemorialExcluidos) {
        this.listAnexosMemorialExcluidos = listAnexosMemorialExcluidos;
    }
}
