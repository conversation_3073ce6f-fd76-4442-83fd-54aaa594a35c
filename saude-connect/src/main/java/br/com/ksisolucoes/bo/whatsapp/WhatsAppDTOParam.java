package br.com.ksisolucoes.bo.whatsapp;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;

import java.io.Serializable;

/**
 * <AUTHOR>
 * <PERSON>riado em: Nov 18, 2013
 */
public class WhatsAppDTOParam implements Serializable{

    private String phone;
    private String message;
    private UsuarioCadsus usuarioCadsus;
    private SmsMensagem.OrigemSms origem;
    private String nomePaciente;

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public SmsMensagem.OrigemSms getOrigem() {
        return origem;
    }

    public void setOrigem(SmsMensagem.OrigemSms origem) {
        this.origem = origem;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
}
