package br.com.ksisolucoes.bo.enderecocoordenadas;

import br.com.ksisolucoes.util.maps.DadosGoogleMapsHelper;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.coordenadas.EnderecoCoordenadas;
import org.hibernate.criterion.Restrictions;

public class EndereceUsuarioCadsusHandler extends EnderecoGenericHandler {

    public EndereceUsuarioCadsusHandler(LatitudeLongitudeEndereco latitudeLongitudeEndereco) {
        super(latitudeLongitudeEndereco);
    }

    public void handle(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        if (enderecoUsuarioCadsus != null && enderecoUsuarioCadsus.getCodigo() != null) {
            EnderecoCoordenadas enderecoCoordenadas = (EnderecoCoordenadas) getSession()
                    .createCriteria(EnderecoCoordenadas.class)
                    .add(Restrictions.eq(EnderecoCoordenadas.PROP_ENDERECO_USUARIO_CADSUS, enderecoUsuarioCadsus))
                    .setMaxResults(1)
                    .uniqueResult();
            if (enderecoCoordenadas == null) {
                String endereco = DadosGoogleMapsHelper.enderecoCompleto(enderecoUsuarioCadsus);
                if (endereco != null && !endereco.isEmpty()) {
                    enderecoCoordenadas = buscaEndereco(endereco);
                    enderecoCoordenadas.setEnderecoUsuarioCadsus(enderecoUsuarioCadsus);
                    if (!Double.valueOf(0.0).equals(enderecoCoordenadas.getLongitude())
                            && !Double.valueOf(0.0).equals(enderecoCoordenadas.getLatitude())) {
                        salvaEndereco(enderecoCoordenadas);
                    }
                }
            } else {
                setEnderecoFromCoordenadas(enderecoCoordenadas);
            }
        }
    }
}
