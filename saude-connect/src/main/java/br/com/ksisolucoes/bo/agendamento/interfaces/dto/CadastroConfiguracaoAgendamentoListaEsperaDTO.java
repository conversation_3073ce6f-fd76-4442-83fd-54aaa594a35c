package br.com.ksisolucoes.bo.agendamento.interfaces.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by mauricley on 04/05/18.
 */
public class CadastroConfiguracaoAgendamentoListaEsperaDTO implements Serializable {
    private List<ConfiguracaoConsultaAgendamentoListaEsperaDTO> configuracaoConsultaList = new ArrayList<>();
    private List<ConfiguracaoOrdenacaoAgendamentoListaEsperaDTO> ConfiguracaoOrdenacaoList = new ArrayList<>();

    public List<ConfiguracaoConsultaAgendamentoListaEsperaDTO> getConfiguracaoConsultaList() {
        return configuracaoConsultaList;
    }

    public void setConfiguracaoConsultaList(List<ConfiguracaoConsultaAgendamentoListaEsperaDTO> configuracaoConsultaList) {
        this.configuracaoConsultaList = configuracaoConsultaList;
    }

    public List<ConfiguracaoOrdenacaoAgendamentoListaEsperaDTO> getConfiguracaoOrdenacaoList() {
        return ConfiguracaoOrdenacaoList;
    }

    public void setConfiguracaoOrdenacaoList(List<ConfiguracaoOrdenacaoAgendamentoListaEsperaDTO> configuracaoOrdenacaoList) {
        this.ConfiguracaoOrdenacaoList = configuracaoOrdenacaoList;
    }
}
