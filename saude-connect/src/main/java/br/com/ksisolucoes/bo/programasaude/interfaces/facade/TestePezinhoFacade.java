package br.com.ksisolucoes.bo.programasaude.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.TestePezinho;

/**                                                            .
 * <AUTHOR>                                   .
 *                                                             . 
 */                          
@EJBLocation("br.com.ksisolucoes.bo.programasaude.TestePezinhoBO")
public interface TestePezinhoFacade extends FacadeBO {

    public void gerarNovaColeta(TestePezin<PERSON> testePezinho) throws DAOException, ValidacaoException;

    public void concluirTestePezin<PERSON>(TestePezin<PERSON> teste<PERSON>ez<PERSON>) throws DAOException, ValidacaoException;

    public void atenderTeste<PERSON>ezinho(TestePezinho testePezinho) throws DAOException, ValidacaoException;

}
