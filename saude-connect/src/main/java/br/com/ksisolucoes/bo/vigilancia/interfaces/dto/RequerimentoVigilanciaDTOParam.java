package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.TipoEnquadramentoProjeto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RequerimentoVigilanciaDTOParam implements Serializable {

    private Estabelecimento estabelecimento;
    private VigilanciaProfissional vigilanciaProfissional;
    private Long tipoDocumento;
    private String nome;
    private String logradouro;
    private String bairro;
    private String solicitante;
    private Long origem;
    private TipoSolicitacao tipoSolicitacao;
    private Long situacao;
    private Long situacaoOcorrencia;
    private Long situacaoAprovacao;
    private Long situacaoAnaliseProjetos;
    private List<UsuarioSetorVigilancia> lstUsuSetor;
    private List<Long> lstAt = new ArrayList<>();
    private Long numeroProtocolo;
    private Long numeroAutorizacao;
    private SetorVigilancia setorVigilancia;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private GrupoEstabelecimento grupoEstabelecimento;
    private String propSort;
    private String sortDataRequerimento;
    private boolean ascending;
    private boolean considerarAprovacaoNula;
    private String entregaDocumento;
    private Long visualizarTodosRequerimentos;
    private Long visualizarCancelados;
    private boolean paginaFiscal;
    private RequerimentoVigilancia.Turno turno;
    private Profissional profissional;
    private String cpf;
    private String numeroInscricaoImobiliaria;
    private String numeroProjetoAprovado;
    private Long classificacaoRisco;
    private Long todosEnquadramentos;
    private List<TipoEnquadramentoProjeto> tipoEnquadramentoProjetoList;
    private Long situacaoParecer;
    private Long tipoAnalise;

    public Long getTipoAnalise() {
        return tipoAnalise;
    }

    public void setTipoAnalise(Long tipoAnalise) {
        this.tipoAnalise = tipoAnalise;
    }

    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public Long getOrigem() {
        return origem;
    }

    public void setOrigem(Long origem) {
        this.origem = origem;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getSolicitante() {
        return solicitante;
    }

    public void setSolicitante(String solicitante) {
        this.solicitante = solicitante;
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public List<UsuarioSetorVigilancia> getLstUsuSetor() {
        return lstUsuSetor;
    }

    public void setLstUsuSetor(List<UsuarioSetorVigilancia> lstUsuSetor) {
        this.lstUsuSetor = lstUsuSetor;
    }

    public List<Long> getLstAt() {
        return lstAt;
    }

    public void setLstAt(List<Long> lstAt) {
        this.lstAt = lstAt;
    }

    public AtividadeEstabelecimento getAtividadeEstabelecimento() {
        return atividadeEstabelecimento;
    }

    public void setAtividadeEstabelecimento(AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = atividadeEstabelecimento;
    }

    public GrupoEstabelecimento getGrupoEstabelecimento() {
        return grupoEstabelecimento;
    }

    public void setGrupoEstabelecimento(GrupoEstabelecimento grupoEstabelecimento) {
        this.grupoEstabelecimento = grupoEstabelecimento;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(Long tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public VigilanciaProfissional getVigilanciaProfissional() {
        return vigilanciaProfissional;
    }

    public void setVigilanciaProfissional(VigilanciaProfissional vigilanciaProfissional) {
        this.vigilanciaProfissional = vigilanciaProfissional;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Long getNumeroProtocolo() {
        return numeroProtocolo;
    }

    public void setNumeroProtocolo(Long numeroProtocolo) {
        this.numeroProtocolo = numeroProtocolo;
    }

    public SetorVigilancia getSetorVigilancia() {
        return setorVigilancia;
    }

    public void setSetorVigilancia(SetorVigilancia setorVigilancia) {
        this.setorVigilancia = setorVigilancia;
    }

    public Long getNumeroAutorizacao() {
        return numeroAutorizacao;
    }

    public void setNumeroAutorizacao(Long numeroAutorizacao) {
        this.numeroAutorizacao = numeroAutorizacao;
    }

    public String getEntregaDocumento() {
        return entregaDocumento;
    }

    public void setEntregaDocumento(String entregaDocumento) {
        this.entregaDocumento = entregaDocumento;
    }

    public Long getVisualizarTodosRequerimentos() {
        return visualizarTodosRequerimentos;
    }

    public void setVisualizarTodosRequerimentos(Long visualizarTodosRequerimentos) {
        this.visualizarTodosRequerimentos = visualizarTodosRequerimentos;
    }

    public RequerimentoVigilancia.Turno getTurno() {
        return turno;
    }

    public void setTurno(RequerimentoVigilancia.Turno turno) {
        this.turno = turno;
    }

    public Long getVisualizarCancelados() {
        return visualizarCancelados;
    }

    public void setVisualizarCancelados(Long visualizarCancelados) {
        this.visualizarCancelados = visualizarCancelados;
    }

    public boolean isPaginaFiscal() {
        return paginaFiscal;
    }

    public void setPaginaFiscal(boolean paginaFiscal) {
        this.paginaFiscal = paginaFiscal;
    }

    public Long getSituacaoOcorrencia() {
        return situacaoOcorrencia;
    }

    public void setSituacaoOcorrencia(Long situacaoOcorrencia) {
        this.situacaoOcorrencia = situacaoOcorrencia;
    }

    public Long getSituacaoAprovacao() {
        return situacaoAprovacao;
    }

    public void setSituacaoAprovacao(Long situacaoAprovacao) {
        this.situacaoAprovacao = situacaoAprovacao;
    }

    public Long getSituacaoAnaliseProjetos() {
        return situacaoAnaliseProjetos;
    }

    public void setSituacaoAnaliseProjetos(Long situacaoAnaliseProjetos) {
        this.situacaoAnaliseProjetos = situacaoAnaliseProjetos;
    }

    public boolean isConsiderarAprovacaoNula() {
        return considerarAprovacaoNula;
    }

    public void setConsiderarAprovacaoNula(boolean considerarAprovacaoNula) {
        this.considerarAprovacaoNula = considerarAprovacaoNula;
    }

    public String getSortDataRequerimento() {
        return sortDataRequerimento;
    }

    public void setSortDataRequerimento(String sortDataRequerimento) {
        this.sortDataRequerimento = sortDataRequerimento;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNumeroInscricaoImobiliaria() {
        return numeroInscricaoImobiliaria;
    }

    public void setNumeroInscricaoImobiliaria(String numeroInscricaoImobiliaria) {
        this.numeroInscricaoImobiliaria = numeroInscricaoImobiliaria;
    }

    public String getNumeroProjetoAprovado() {
        return numeroProjetoAprovado;
    }

    public void setNumeroProjetoAprovado(String numeroProjetoAprovado) {
        this.numeroProjetoAprovado = numeroProjetoAprovado;
    }

    public Long getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public void setClassificacaoRisco(Long classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }

    public List<TipoEnquadramentoProjeto> getTipoEnquadramentoProjetoList() {
        if (tipoEnquadramentoProjetoList == null) {
            tipoEnquadramentoProjetoList = new ArrayList<>();
        }
        return tipoEnquadramentoProjetoList;
    }

    public void setTipoEnquadramentoProjetoList(List<TipoEnquadramentoProjeto> tipoEnquadramentoProjetoList) {
        this.tipoEnquadramentoProjetoList = tipoEnquadramentoProjetoList;
    }

    public Long getTodosEnquadramentos() {
        return todosEnquadramentos;
    }

    public void setTodosEnquadramentos(Long todosEnquadramentos) {
        this.todosEnquadramentos = todosEnquadramentos;
    }

    public Long getSituacaoParecer() {
        return situacaoParecer;
    }

    public void setSituacaoParecer(Long situacaoParecer) {
        this.situacaoParecer = situacaoParecer;
    }
}
