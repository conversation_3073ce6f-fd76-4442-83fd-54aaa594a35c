package br.com.ksisolucoes.bo.integracao.cnes.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by sulivan on 14/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoEmpresaDTO implements Serializable {

    public CnesProcessoEmpresaDTO() {
    }

    @XmlElement(name = "DADOS_GERAIS_ESTABELECIMENTOS")
    private List<CnesProcessoEmpresaDadosDTO> dadosEmpresasList;

    public List<CnesProcessoEmpresaDadosDTO> getDadosEmpresasList() {
        return dadosEmpresasList;
    }

    public void setDadosEmpresasList(List<CnesProcessoEmpresaDadosDTO> dadosEmpresasList) {
        this.dadosEmpresasList = dadosEmpresasList;
    }

    public void addDadosEmpresaList (CnesProcessoEmpresaDadosDTO dados){
        if(dadosEmpresasList == null){
            dadosEmpresasList = new ArrayList<>();
        }
        dadosEmpresasList.add(dados);
    }

}
