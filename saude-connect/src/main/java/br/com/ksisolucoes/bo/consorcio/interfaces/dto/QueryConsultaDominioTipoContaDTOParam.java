package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioTipoContaDTOParam implements Serializable{

    private Long codigo;
    private String keyword;
    private String descricao;
    private String propSort;
    private boolean ascending;
    private boolean validarVisivelConsorciado;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isValidarVisivelConsorciado() {
        return validarVisivelConsorciado;
    }

    public void setValidarVisivelConsorciado(boolean validarVisivelConsorciado) {
        this.validarVisivelConsorciado = validarVisivelConsorciado;
    }
    
}
