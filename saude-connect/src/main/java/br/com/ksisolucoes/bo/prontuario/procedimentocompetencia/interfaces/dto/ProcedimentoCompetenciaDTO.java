package br.com.ksisolucoes.bo.prontuario.procedimentocompetencia.interfaces.dto;

import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;

import java.io.Serializable;

/**
 * Created by mauricley on 22/03/18.
 */
public class ProcedimentoCompetenciaDTO implements Serializable {

    private ProcedimentoCompetencia procedimentoCompetencia;
    private ExameProcedimentoDTO exameProcedimentoDTO;
    private Empresa empresa;

    private AtendimentoExameItem atendimentoExameItem;

    public ProcedimentoCompetenciaDTO() {
    }

    public ProcedimentoCompetenciaDTO(ProcedimentoCompetencia procedimentoCompetencia) {
        this.procedimentoCompetencia = procedimentoCompetencia;
    }

    public ProcedimentoCompetenciaDTO(ProcedimentoCompetencia procedimentoCompetencia, Empresa empresa) {
        this.procedimentoCompetencia = procedimentoCompetencia;
        this.empresa = empresa;
    }

    public ProcedimentoCompetencia getProcedimentoCompetencia() {
        return procedimentoCompetencia;
    }

    public void setProcedimentoCompetencia(ProcedimentoCompetencia procedimentoCompetencia) {
        this.procedimentoCompetencia = procedimentoCompetencia;
    }

    public ExameProcedimentoDTO getExameProcedimentoDTO() {
        return exameProcedimentoDTO;
    }

    public void setExameProcedimentoDTO(ExameProcedimentoDTO exameProcedimentoDTO) {
        this.exameProcedimentoDTO = exameProcedimentoDTO;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public AtendimentoExameItem getAtendimentoExameItem() {
        return atendimentoExameItem;
    }

    public void setAtendimentoExameItem(AtendimentoExameItem atendimentoExameItem) {
        this.atendimentoExameItem = atendimentoExameItem;
    }
}
