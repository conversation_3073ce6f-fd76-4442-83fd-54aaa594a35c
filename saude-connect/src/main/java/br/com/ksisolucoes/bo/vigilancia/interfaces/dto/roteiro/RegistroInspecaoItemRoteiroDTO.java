package br.com.ksisolucoes.bo.vigilancia.interfaces.dto.roteiro;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecaoPergunta;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItem;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItemPerguntaResposta;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by laudecir on 02/06/17.
 */
public class RegistroInspecaoItemRoteiroDTO implements Serializable {

    private RegistroInspecaoRoteiroItem registroInspecaoRoteiroItem;
    private List<RegistroInspecaoRespostaPerguntaItemRoteiroInspecaoDTO> respostaPerguntaList = new ArrayList();

    public RegistroInspecaoItemRoteiroDTO(RegistroInspecaoRoteiroItem registroInspecaoRoteiroItem) {
        this.registroInspecaoRoteiroItem = registroInspecaoRoteiroItem;
    }

    public RegistroInspecaoRoteiroItem getRegistroInspecaoRoteiroItem() {
        return registroInspecaoRoteiroItem;
    }

    public void setRegistroInspecaoRoteiroItem(RegistroInspecaoRoteiroItem registroInspecaoRoteiroItem) {
        this.registroInspecaoRoteiroItem = registroInspecaoRoteiroItem;
    }

    public List<RegistroInspecaoRespostaPerguntaItemRoteiroInspecaoDTO> getRespostaPerguntaList() {
        return respostaPerguntaList;
    }

    public void setRespostaPerguntaList(List<RegistroInspecaoRespostaPerguntaItemRoteiroInspecaoDTO> respostaPerguntaList) {
        this.respostaPerguntaList = respostaPerguntaList;
    }

    public void add(RegistroInspecaoRespostaPerguntaItemRoteiroInspecaoDTO perguntaItemRoteiroDTO) {
        this.respostaPerguntaList.add(perguntaItemRoteiroDTO);
    }
}
