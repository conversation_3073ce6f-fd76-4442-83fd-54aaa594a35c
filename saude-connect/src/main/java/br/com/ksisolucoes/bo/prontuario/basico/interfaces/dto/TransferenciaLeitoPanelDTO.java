package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaLeito;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TransferenciaLeitoPanelDTO implements Serializable{
    
    public static final String PROP_ATENDIMENTO_TRANSFERENCIA_LEITO = "atendimentoTransferenciaLeito";
    
    private AtendimentoTransferenciaLeito atendimentoTransferenciaLeito;
    private List<LeitoQuarto> lstLeitoQuarto;    

    public AtendimentoTransferenciaLeito getAtendimentoTransferenciaLeito() {
        return atendimentoTransferenciaLeito;
    }

    public void setAtendimentoTransferenciaLeito(AtendimentoTransferenciaLeito atendimentoTransferenciaLeito) {
        this.atendimentoTransferenciaLeito = atendimentoTransferenciaLeito;
    }

    public List<LeitoQuarto> getLstLeitoQuarto() {
        return lstLeitoQuarto;
    }

    public void setLstLeitoQuarto(List<LeitoQuarto> lstLeitoQuarto) {
        this.lstLeitoQuarto = lstLeitoQuarto;
    }
}
