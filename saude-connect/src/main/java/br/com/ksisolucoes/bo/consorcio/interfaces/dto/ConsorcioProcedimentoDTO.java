package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConsorcioProcedimentoDTO implements Serializable{

    public static final String PROP_CONSORCIO_PROCEDIMENTO = "consorcioProcedimento";
    public static final String PROP_VALOR_REFERENCIA = "valorReferencia";
    
    private ConsorcioProcedimento consorcioProcedimento;
    private Double valorReferencia;

    public ConsorcioProcedimento getConsorcioProcedimento() {
        return consorcioProcedimento;
    }

    public void setConsorcioProcedimento(ConsorcioProcedimento consorcioProcedimento) {
        this.consorcioProcedimento = consorcioProcedimento;
    }

    public Double getValorReferencia() {
        return valorReferencia;
    }

    public void setValorReferencia(Double valorReferncia) {
        this.valorReferencia = valorReferncia;
    }
    
}
