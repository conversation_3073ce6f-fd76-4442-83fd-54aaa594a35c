package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19;

import br.com.ksisolucoes.enums.IEnum;

import java.io.Serializable;

public class SintomasCovid19DTO implements IEnum<SinaisClinicosCovid19DTO>, Serializable {

    private Long codigo;
    private String descricao;
    private Long pontuacao;

    public Long getPontuacao() {
        return pontuacao != null ? pontuacao : 0L;
    }

    public void setPontuacao(Long pontuacao) {
        this.pontuacao = pontuacao;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    @Override
    public Object value() {
        return this.codigo;
    }

    @Override
    public String descricao() {
        return this.descricao;
    }
}
