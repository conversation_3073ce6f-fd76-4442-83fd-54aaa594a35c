package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.List;

public class QueryPedidoTransferenciaForEmbarqueBean implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * Holds value of property empresasDestino.
     */
    private List<Empresa> empresasDestino;

    /**
     * Getter for property codigoEmpresa.
     * @return Value of property codigoEmpresa.
     */
    public List<Empresa> getEmpresasDestino() {
        return this.empresasDestino;
    }

    /**
     * Setter for property codigoEmpresa.
     * @param codigoEmpresa New value of property codigoEmpresa.
     */
    public void setEmpresasDestino(List<Empresa> empresasDestino) {
        this.empresasDestino = empresasDestino;
    }
    /**
     * Holds value of property status.
     */
    private List<Long> status;

    /**
     * Getter for property produtos.
     * @return Value of property produtos.
     */
    public List<Long> getStatus() {
        return this.status;
    }

    /**
     * Setter for property produtos.
     * @param produtos New value of property produtos.
     */
    public void setStatus(List<Long> status) {
        this.status = status;
    }
    /**
     * Holds value of property pedido.
     */
    private Long pedido;

    /**
     * Getter for property pedido.
     * @return Value of property pedido.
     */
    public Long getPedido() {
        return this.pedido;
    }

    /**
     * Setter for property pedido.
     * @param pedido New value of property pedido.
     */
    public void setPedido(Long pedido) {
        this.pedido = pedido;
    }
}
