package br.com.ksisolucoes.bo.prontuario.web.encaminhamento.interfaces;

import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoadInterceptorEloNaturezaTipoEncaminhamento implements LoadInterceptor{

    private List<Empresa> empresas;

    public LoadInterceptorEloNaturezaTipoEncaminhamento(List<Empresa> empresas) {
        this.empresas = empresas;
    }
    
    public LoadInterceptorEloNaturezaTipoEncaminhamento(Empresa empresa) {
        this.empresas = Arrays.asList(empresa);
    }
    
    @Override
    public void customHQL(HQLHelper hql, String alias) {
        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        
        hqlSub.addToSelect("1");
        hqlSub.addToFrom("EmpresaNaturezaProcuraTipoAtendimento empresaNaturezaProcuraTipoAtendimento");
        hqlSub.addToWhereWhithAnd("empresaNaturezaProcuraTipoAtendimento.empresa in", empresas);
        hqlSub.addToWhereWhithAnd("empresaNaturezaProcuraTipoAtendimento.naturezaProcuraTipoAtendimento.tipoAtendimento = "+alias+".naturezaProcuraTipoAtendimento.tipoAtendimento");
        hqlSub.addToWhereWhithAnd("empresaNaturezaProcuraTipoAtendimento.naturezaProcuraTipoAtendimento.naturezaProcura = "+alias+".naturezaProcuraTipoAtendimento.naturezaProcura");
        
        hql.addToWhereWhithAnd("exists( "+hqlSub.getQuery()+" )");
    }

}
