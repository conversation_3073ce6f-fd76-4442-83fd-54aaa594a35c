package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.builder;

import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.FormularioTriagemCovid19DTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.covid19.SituacaoSaudePaciente;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemMorbidadeCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemSinaisClinicosCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import ch.lambdaj.Lambda;

import java.util.Date;
import java.util.List;

public class FormularioTriagemCovid19DTOBuilder {

    private FormularioTriagemCovid19DTO formularioTriagemCovid19DTO;

    public FormularioTriagemCovid19DTOBuilder() {
        this.formularioTriagemCovid19DTO = new FormularioTriagemCovid19DTO();
    }

    public FormularioTriagemCovid19DTOBuilder setCodigo(Long codigo) {
        this.formularioTriagemCovid19DTO.setCodigo(codigo);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setDataPrimeirosSintomas(Date dataPrimeirosSintomas) {
        this.formularioTriagemCovid19DTO.setDataPrimeirosSintomas(dataPrimeirosSintomas);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setFlagHistoricoViagemForaBrasil(Long flagHistoricoViagemForaBrasil) {
        this.formularioTriagemCovid19DTO.setFlagHistoricoViagemForaBrasil(flagHistoricoViagemForaBrasil);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setContatoPacienteSuspeitoCovid(Long contatoPacienteSuspeitoCovid) {
        this.formularioTriagemCovid19DTO.setContatoPacienteSuspeitoCovid(contatoPacienteSuspeitoCovid);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setLocalCasoSuspeito(String localContatoSuspeito) {
        this.formularioTriagemCovid19DTO.setLocalContatoSuspeito(localContatoSuspeito);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setLocalCasoConfirmado(String localContatoConfirmado) {
        this.formularioTriagemCovid19DTO.setLocalContatoConfirmado(localContatoConfirmado);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setContatoPacienteConfirmado(Long contatoPacienteConfirmado) {
        this.formularioTriagemCovid19DTO.setContatoPacienteConfirmado(contatoPacienteConfirmado);
        return this;
    }


    public FormularioTriagemCovid19DTOBuilder setSituacaoSaudePaciente(Long situacaoSaudePaciente) {
        if (situacaoSaudePaciente != null) {
            this.formularioTriagemCovid19DTO.setSituacaoSaudePaciente(SituacaoSaudePaciente.valueOf(situacaoSaudePaciente));
        }
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setAtendimento(Atendimento atendimento) {
        this.formularioTriagemCovid19DTO.setAtendimento(atendimento);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setSintomas(List<FormularioTriagemSintomasCovid19> sintomas) {
        if (sintomas != null) {
            List<Long> codigosSintomas = Lambda.extract(sintomas, Lambda.on(FormularioTriagemSintomasCovid19.class).getSintomaCovid19().getCodigo());
            this.formularioTriagemCovid19DTO.setSintomas(codigosSintomas);
        }
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setSinaisClinicos(List<FormularioTriagemSinaisClinicosCovid19> sinais) {
        if (sinais != null) {
            List<Long> codigosSinais = Lambda.extract(sinais, Lambda.on(FormularioTriagemSinaisClinicosCovid19.class).getSinalClinicoCovid19().getCodigo());
            this.formularioTriagemCovid19DTO.setSinaisClinicos(codigosSinais);
        }
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setMorbidades(List<FormularioTriagemMorbidadeCovid19> morbidades) {
        if (morbidades != null) {
            List<Long> codigosMorbidade = Lambda.extract(morbidades, Lambda.on(FormularioTriagemMorbidadeCovid19.class).getMorbidadeCovid19().getCodigo());
            this.formularioTriagemCovid19DTO.setMorbidades(codigosMorbidade);
        }
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setIsProfissionalSaude(Long profissionalSaude) {
        this.formularioTriagemCovid19DTO.setProfissionalSaude(profissionalSaude);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setDescricaoOcupacaoProfissionalSaude(String descricaoOcupacaoProfissionalSaude) {
        this.formularioTriagemCovid19DTO.setDescricaoOcupacaoProfissionalSaude(descricaoOcupacaoProfissionalSaude);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setOutroSintoma(String outroSintoma) {
        this.formularioTriagemCovid19DTO.setOutrosSintomas(outroSintoma);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setResultadoPontuacao(Long resultadoPontuacao) {
        this.formularioTriagemCovid19DTO.setResultadoPontuacao(resultadoPontuacao);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setQuantidadePessoasCasa(Long quantidadePessoasCasa) {
        this.formularioTriagemCovid19DTO.setQuantidadePessoasCasa(quantidadePessoasCasa);
        return this;
    }

    public FormularioTriagemCovid19DTOBuilder setQuantidadePessoas14Dias(Long quantidadePessoas14Dias) {
        this.formularioTriagemCovid19DTO.setQuantidadePessoas14Dias(quantidadePessoas14Dias);
        return this;
    }

    public FormularioTriagemCovid19DTO build() {
        return this.formularioTriagemCovid19DTO;
    }


}
