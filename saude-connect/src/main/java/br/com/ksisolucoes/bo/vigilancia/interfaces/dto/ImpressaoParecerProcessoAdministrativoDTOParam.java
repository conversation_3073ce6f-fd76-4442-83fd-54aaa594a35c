package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoParecer;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoParecerProcessoAdministrativoDTOParam implements Serializable {

    private ProcessoAdministrativoParecer processoAdministrativoParecer;
    private QRCodeGenerateDTOParam QRCodeParam;
    private String numeroFormatado;

    public ProcessoAdministrativoParecer getProcessoAdministrativoParecer() {
        return processoAdministrativoParecer;
    }

    public void setProcessoAdministrativoParecer(ProcessoAdministrativoParecer processoAdministrativoParecer) {
        this.processoAdministrativoParecer = processoAdministrativoParecer;
    }

    public QRCodeGenerateDTOParam getQRCodeParam() {
        return QRCodeParam;
    }

    public void setQRCodeParam(QRCodeGenerateDTOParam QRCodeParam) {
        this.QRCodeParam = QRCodeParam;
    }

    public String getNumeroFormatado() {
        return numeroFormatado;
    }

    public void setNumeroFormatado(String numeroFormatado) {
        this.numeroFormatado = numeroFormatado;
    }
}
