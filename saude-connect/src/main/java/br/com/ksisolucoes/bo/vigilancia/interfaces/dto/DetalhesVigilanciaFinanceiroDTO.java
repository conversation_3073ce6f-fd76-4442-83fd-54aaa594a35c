package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.integracao.boleto.Boleto;
import br.com.ksisolucoes.vo.integracao.boleto.BoletoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.IntegracaoFinanceiroGoianiaOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.IntegracaoFinanceiroSmartOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DetalhesVigilanciaFinanceiroDTO implements Serializable {
    
    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private Boleto boleto;
    private List<BoletoOcorrencia> boletoOcorrenciaList;
    private List<IntegracaoFinanceiroSmartOcorrencia> listOcorrenciasSmart;
    private List<IntegracaoFinanceiroGoianiaOcorrencia> listOcorrenciasGoiania;

    public VigilanciaFinanceiro getVigilanciaFinanceiro() {
        return vigilanciaFinanceiro;
    }

    public void setVigilanciaFinanceiro(VigilanciaFinanceiro vigilanciaFinanceiro) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
    }

    public Boleto getBoleto() {
        return boleto;
    }

    public void setBoleto(Boleto boleto) {
        this.boleto = boleto;
    }

    public List<BoletoOcorrencia> getBoletoOcorrenciaList() {
        return boletoOcorrenciaList;
    }

    public void setBoletoOcorrenciaList(List<BoletoOcorrencia> boletoOcorrenciaList) {
        this.boletoOcorrenciaList = boletoOcorrenciaList;
    }

    public List<IntegracaoFinanceiroSmartOcorrencia> getListOcorrenciasSmart() {
        return listOcorrenciasSmart;
    }

    public void setListOcorrenciasSmart(List<IntegracaoFinanceiroSmartOcorrencia> listOcorrenciasSmart) {
        this.listOcorrenciasSmart = listOcorrenciasSmart;
    }

    public List<IntegracaoFinanceiroGoianiaOcorrencia> getListOcorrenciasGoiania() {
        return listOcorrenciasGoiania;
    }

    public void setListOcorrenciasGoiania(List<IntegracaoFinanceiroGoianiaOcorrencia> listOcorrenciasGoiania) {
        this.listOcorrenciasGoiania = listOcorrenciasGoiania;
    }
}
