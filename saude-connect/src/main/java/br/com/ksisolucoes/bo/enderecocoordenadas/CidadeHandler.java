package br.com.ksisolucoes.bo.enderecocoordenadas;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.coordenadas.EnderecoCoordenadas;
import org.hibernate.criterion.Restrictions;

public class CidadeHandler extends EnderecoGenericHandler {

    public CidadeHandler(LatitudeLongitudeEndereco latitudeLongitudeEndereco) {
        super(latitudeLongitudeEndereco);
    }

    public void handle(Cidade cidade) {
        if (cidade != null && cidade.getCodigo() != null) {
            EnderecoCoordenadas enderecoCoordenadas = (EnderecoCoordenadas) getSession()
                    .createCriteria(EnderecoCoordenadas.class)
                    .add(Restrictions.eq(EnderecoCoordenadas.PROP_CIDADE, cidade))
                    .setMaxResults(1)
                    .uniqueResult();
            if (enderecoCoordenadas == null && cidade.getDescricaoCidadeUf() != null) {
                enderecoCoordenadas = buscaEndereco(cidade.getDescricaoCidadeUf());
                enderecoCoordenadas.setCidade(cidade);
                if (!Double.valueOf(0.0).equals(enderecoCoordenadas.getLongitude())
                        && !Double.valueOf(0.0).equals(enderecoCoordenadas.getLatitude())) {
                    salvaEndereco(enderecoCoordenadas);
                }
            }
            if (enderecoCoordenadas != null) {
                setEnderecoFromCoordenadas(enderecoCoordenadas);
            }
        }
    }
}
