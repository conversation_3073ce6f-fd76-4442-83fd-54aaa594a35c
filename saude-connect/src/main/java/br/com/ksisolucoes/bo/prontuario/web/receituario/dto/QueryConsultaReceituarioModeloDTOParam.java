package br.com.ksisolucoes.bo.prontuario.web.receituario.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaReceituarioModeloDTOParam implements Serializable {
    
    private TipoReceita tipoReceita;
    private Profissional profissional;
    private Long compartilhado;
    private boolean compartilhadoUsuario = false;
    private String descricao;
    private String keyword;
    private String propSort;
    private boolean ascending;

    public TipoReceita getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    public Long getCompartilhado() {
        return compartilhado;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public void setCompartilhado(Long compartilhado) {
        this.compartilhado = compartilhado;
    }

    public boolean isCompartilhadoUsuario() {
        return compartilhadoUsuario;
    }

    public void setCompartilhadoUsuario(boolean compartilhadoUsuario) {
        this.compartilhadoUsuario = compartilhadoUsuario;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
