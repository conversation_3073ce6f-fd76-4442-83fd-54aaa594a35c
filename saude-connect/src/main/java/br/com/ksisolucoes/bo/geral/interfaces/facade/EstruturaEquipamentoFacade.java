package br.com.ksisolucoes.bo.geral.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.geral.interfaces.dto.EstruturaEquipamentoDTOParam;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamentoRevisao;
import java.util.List;

/**                                                            .
 * <AUTHOR>                                   .
 *                                                             . 
 */              
@EJBLocation("br.com.ksisolucoes.bo.geral.EstruturaEquipamentoBO")
public interface EstruturaEquipamentoFacade extends FacadeBO {

    public void apagarPrimeiroNivel( Produto produto ) throws DAOException, ValidacaoException ;

    public RetornoValidacao substituirComponente( Produto componenteOri, Produto componenteDes ) throws DAOException, ValidacaoException;

    public RetornoValidacao substituirComponente( Produto componenteOri, Produto componenteDes, List<Produto> produtoList , List<SubGrupo> subGrupoList ) throws DAOException, ValidacaoException;

    public List<EstruturaEquipamento> montarListEstruturaEquipamento(EstruturaEquipamentoDTOParam bean) throws DAOException, ValidacaoException;

    public List<EstruturaEquipamentoRevisao> montarListEstruturaEquipamentoRevisao(EstruturaEquipamentoDTOParam bean) throws DAOException, ValidacaoException;

    public void copiarEstrutura(Produto produtoOri, Produto produtoDes) throws DAOException, ValidacaoException;

    public void copiarEstrutura(Produto produtoOri, Produto produtoDes, boolean aprovar) throws DAOException, ValidacaoException ;

    public boolean containsEstruturaEquipamento(Produto produto) throws DAOException, ValidacaoException ;

    public List<Produto> processarEstruturaEquipamentoToProdutos(List<Produto> produtoList, int niveis, String procedencia, Double quantidadeNecessaria) throws DAOException, ValidacaoException;
}
