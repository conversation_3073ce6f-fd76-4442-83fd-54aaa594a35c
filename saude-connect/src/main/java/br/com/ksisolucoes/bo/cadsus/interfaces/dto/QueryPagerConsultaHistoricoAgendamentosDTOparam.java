package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaHistoricoAgendamentosDTOparam implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private String propSort;
    private boolean ascending;

    private DTOParamConfigureDefault configureParam;

    public DTOParamConfigureDefault getConfigureParam() {
        if (configureParam == null) {
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

}
