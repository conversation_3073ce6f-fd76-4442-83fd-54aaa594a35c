package br.com.ksisolucoes.bo.vacina.pni.interfaces.dto;

import br.com.ksisolucoes.vo.vacina.pni.PniConfiguracao;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class CadastroPniConfiguracaoDTO implements Serializable {
    
    private PniConfiguracao pniConfiguracao;

    public PniConfiguracao getPniConfiguracao() {
        return pniConfiguracao;
    }

    public void setPniConfiguracao(PniConfiguracao pniConfiguracao) {
        this.pniConfiguracao = pniConfiguracao;
    }
}
