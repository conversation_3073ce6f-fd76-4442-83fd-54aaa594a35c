package br.com.ksisolucoes.bo.vacina.interfaces.dto;

import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import br.com.ksisolucoes.vo.vacina.VacinaCalendarioAprazamento;
import br.com.ksisolucoes.vo.vacina.VacinaCalendarioEsconder;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroVacinaCalendarioDTO implements Serializable {
    
    private VacinaCalendario vacinaCalendario;
    private List<VacinaCalendarioAprazamento> vacinaCalendarioAprazamentoList = new ArrayList<VacinaCalendarioAprazamento>();
    private List<VacinaCalendarioEsconder> vacinaCalendarioEsconderList = new ArrayList<VacinaCalendarioEsconder>();

    public VacinaCalendario getVacinaCalendario() {
        return vacinaCalendario;
    }

    public void setVacinaCalendario(VacinaCalendario vacinaCalendario) {
        this.vacinaCalendario = vacinaCalendario;
    }

    public List<VacinaCalendarioAprazamento> getVacinaCalendarioAprazamentoList() {
        return vacinaCalendarioAprazamentoList;
    }

    public void setVacinaCalendarioAprazamentoList(List<VacinaCalendarioAprazamento> vacinaCalendarioAprazamentoList) {
        this.vacinaCalendarioAprazamentoList = vacinaCalendarioAprazamentoList;
    }

    public List<VacinaCalendarioEsconder> getVacinaCalendarioEsconderList() {
        return vacinaCalendarioEsconderList;
    }

    public void setVacinaCalendarioEsconderList(List<VacinaCalendarioEsconder> vacinaCalendarioEsconderList) {
        this.vacinaCalendarioEsconderList = vacinaCalendarioEsconderList;
    }
    
}
