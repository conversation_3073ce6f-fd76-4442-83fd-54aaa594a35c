package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class LoteAihDTOParam implements Serializable{

    private Long lote;
    private DatePeriod periodo;
    private Long situacao;
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public Long getLote() {
        return lote;
    }

    public void setLote(Long lote) {
        this.lote = lote;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }
    
}
