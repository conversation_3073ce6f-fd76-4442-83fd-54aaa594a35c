package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.dao.HQLHelper;

/**
 *
 * <AUTHOR>
 */
public class LoadInterceptorNotExistsAIHAtendimento implements LoadInterceptor {

    public LoadInterceptorNotExistsAIHAtendimento() {
    }

    @Override
    public void customHQL(HQLHelper hql, String alias) {
        hql.addToWhereWhithAnd("not exists(select 1 from Aih aih where aih.atendimento = " + alias + ")");
    }

}
