package br.com.ksisolucoes.bo.exame.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.io.Serializable;

/**
 * Created by sulivan on 21/02/19.
 */
public class ConsultaCotaExameProfissionalDTOParam implements Serializable {

    private Profissional profissional;
    private TipoExame tipoExame;
    private Empresa empresa;
    private String propSort;
    private boolean ascending;

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }
}
