package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporteVeiculo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AutoIntimacaoExigenciaDTO implements Serializable {

    private AutoIntimacaoExigencia autoIntimacaoExigencia;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList = new ArrayList<>();

    public AutoIntimacaoExigenciaDTO(AutoIntimacaoExigencia exigencia) {
        this.autoIntimacaoExigencia = exigencia;
    }

    public AutoIntimacaoExigenciaDTO(AutoIntimacaoExigencia autoIntimacaoExigencia, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.autoIntimacaoExigencia = autoIntimacaoExigencia;
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public AutoIntimacaoExigencia getAutoIntimacaoExigencia() {
        return autoIntimacaoExigencia;
    }

    public void setAutoIntimacaoExigencia(AutoIntimacaoExigencia autoIntimacaoExigencia) {
        this.autoIntimacaoExigencia = autoIntimacaoExigencia;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoDTOList() {
        return requerimentoVigilanciaAnexoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList) {
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoExcluidoDTOList() {
        return requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoExcluidoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }
}