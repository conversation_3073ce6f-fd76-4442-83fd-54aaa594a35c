package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatos;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal;

import java.io.Serializable;

public class RelatorioRelacaoPopulacaoCaesGatosDTO implements Serializable {

    private PopulacaoCaesGatos populacaoCaesGatos;
    private PopulacaoCaesGatosTipoAnimal populacaoCaesGatosTipoAnimal;
    private Long quantidade;


    public PopulacaoCaesGatos getPopulacaoCaesGatos() {
        return populacaoCaesGatos;
    }

    public void setPopulacaoCaesGatos(PopulacaoCaesGatos populacaoCaesGatos) {
        this.populacaoCaesGatos = populacaoCaesGatos;
    }

    public PopulacaoCaesGatosTipoAnimal getPopulacaoCaesGatosTipoAnimal() {
        return populacaoCaesGatosTipoAnimal;
    }

    public void setPopulacaoCaesGatosTipoAnimal(PopulacaoCaesGatosTipoAnimal populacaoCaesGatosTipoAnimal) {
        this.populacaoCaesGatosTipoAnimal = populacaoCaesGatosTipoAnimal;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }
}
