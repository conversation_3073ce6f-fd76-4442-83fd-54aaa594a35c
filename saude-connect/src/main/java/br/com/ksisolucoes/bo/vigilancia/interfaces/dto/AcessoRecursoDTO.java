package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AcessoRecursoDTO implements Serializable {

    private ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao;

    private List<AutoIntimacao> autoIntimacaoList = new ArrayList<>();
    private List<AutoInfracao> autoInfracaoList = new ArrayList<>();
    private List<AutoPenalidade> autoPenalidadeList = new ArrayList<>();
    private List<AutoMulta> autoMultaList = new ArrayList<>();

    public List<AutoIntimacao> getAutoIntimacaoList() {
        return autoIntimacaoList;
    }

    public void setAutoIntimacaoList(List<AutoIntimacao> autoIntimacaoList) {
        this.autoIntimacaoList = autoIntimacaoList;
    }

    public List<AutoInfracao> getAutoInfracaoList() {
        return autoInfracaoList;
    }

    public void setAutoInfracaoList(List<AutoInfracao> autoInfracaoList) {
        this.autoInfracaoList = autoInfracaoList;
    }

    public List<AutoPenalidade> getAutoPenalidadeList() {
        return autoPenalidadeList;
    }

    public void setAutoPenalidadeList(List<AutoPenalidade> autoPenalidadeList) {
        this.autoPenalidadeList = autoPenalidadeList;
    }

    public List<AutoMulta> getAutoMultaList() {
        return autoMultaList;
    }

    public void setAutoMultaList(List<AutoMulta> autoMultaList) {
        this.autoMultaList = autoMultaList;
    }

    public ProcessoAdministrativoAutenticacao getProcessoAdministrativoAutenticacao() {
        return processoAdministrativoAutenticacao;
    }

    public void setProcessoAdministrativoAutenticacao(ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao) {
        this.processoAdministrativoAutenticacao = processoAdministrativoAutenticacao;
    }
}