package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.bo.vigilancia.interfaces.enumeration.TipoImpressaoVigilancia;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoConsultaRequerimentoVigilanciaDTO implements Serializable {

    private TipoImpressaoVigilancia tipoImpressao;

    public ImpressaoConsultaRequerimentoVigilanciaDTO() {}

    public ImpressaoConsultaRequerimentoVigilanciaDTO(TipoImpressaoVigilancia tipoImpressao) {
        this.tipoImpressao = tipoImpressao;
    }

    public TipoImpressaoVigilancia getTipoImpressao() {
        return tipoImpressao;
    }

    public void setTipoImpressao(TipoImpressaoVigilancia tipoImpressao) {
        this.tipoImpressao = tipoImpressao;
    }

}
