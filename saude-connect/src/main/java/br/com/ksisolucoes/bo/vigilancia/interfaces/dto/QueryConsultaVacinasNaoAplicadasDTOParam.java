package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import java.io.Serializable;

public class QueryConsultaVacinasNaoAplicadasDTOParam implements Serializable {
    private Long codigo;
    private String descricao;
    private String keyword;
    private Long dose;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Long getDose() {
        return dose;
    }

    public void setDose(Long dose) {
        this.dose = dose;
    }
}
