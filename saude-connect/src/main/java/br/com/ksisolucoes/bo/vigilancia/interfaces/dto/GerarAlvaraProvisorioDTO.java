package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class GerarAlvaraProvisorioDTO implements Serializable {

    private RequerimentoVigilancia requerimentoVigilancia;
    private Date dataValidade;
    private String urlQRCode;

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public String getUrlQRCode() {
        return urlQRCode;
    }

    public void setUrlQRCode(String urlQRCode) {
        this.urlQRCode = urlQRCode;
    }
}
