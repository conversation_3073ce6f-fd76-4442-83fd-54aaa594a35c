package br.com.ksisolucoes.bo.siab.raas;

import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarRaasProcessoDTOParam implements Serializable{

    private Long mes;
    private Long ano;
    private Long codigoRaasProcesso;
    private Empresa empresa;
    private List<Empresa> empresaList;

    public Long getAno() {
        return ano;
    }

    public void setAno(Long ano) {
        this.ano = ano;
    }

    public Long getMes() {
        return mes;
    }

    public void setMes(Long mes) {
        this.mes = mes;
    }

    public Long getCodigoRaasProcesso() {
        return codigoRaasProcesso;
    }

    public void setCodigoRaasProcesso(Long codigoRaasProcesso) {
        this.codigoRaasProcesso = codigoRaasProcesso;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public List<Empresa> getEmpresaList() {
        return empresaList;
    }

    public void setEmpresaList(List<Empresa> empresaList) {
        this.empresaList = empresaList;
    }
}
