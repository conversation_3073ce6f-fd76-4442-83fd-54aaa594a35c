package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class AtendimentosExternosDTOParam implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private Integer meses;
    private String descricaoTipoAtendimento;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Integer getMeses() {
        return meses;
    }

    public void setMeses(Integer meses) {
        this.meses = meses;
    }

    public String getDescricaoTipoAtendimento() {
        return descricaoTipoAtendimento;
    }

    public void setDescricaoTipoAtendimento(String descricaoTipoAtendimento) {
        this.descricaoTipoAtendimento = descricaoTipoAtendimento;
    }

}
