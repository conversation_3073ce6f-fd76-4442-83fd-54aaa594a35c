package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sulivan on 10/11/17.
 */
public class ServicoCnsUsuarioCadsusDTOParam implements Serializable {

    private String cns;
    private String cpf;
    private String rg;
    private String nomePaciente;
    private String nomeMae;
    private Date dataNascimento;
    private String sexo;

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }
}
