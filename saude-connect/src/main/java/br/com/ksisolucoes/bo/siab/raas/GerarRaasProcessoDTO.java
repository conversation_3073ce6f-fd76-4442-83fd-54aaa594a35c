package br.com.ksisolucoes.bo.siab.raas;

import br.com.ksisolucoes.vo.atendimento.RaasProcesso;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class GerarRaasProcessoDTO implements Serializable {
    
    private RaasProcesso.Status status;
    private Long numeroTotalFolhas;

    public RaasProcesso.Status getStatus() {
        return status;
    }

    public void setStatus(RaasProcesso.Status status) {
        this.status = status;
    }

    public Long getNumeroTotalFolhas() {
        return numeroTotalFolhas;
    }

    public void setNumeroTotalFolhas(Long numeroTotalFolhas) {
        this.numeroTotalFolhas = numeroTotalFolhas;
    }    
}
