package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoConsultaAtendimento;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoConsultaAtendimentoDTO implements Serializable {
    
    private ConfiguracaoConsultaAtendimento configuracao;

    public ConfiguracaoConsultaAtendimento getConfiguracao() {
        return configuracao;
    }

    public void setConfiguracao(ConfiguracaoConsultaAtendimento configuracao) {
        this.configuracao = configuracao;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(configuracao.getEmpresa());
        hash = 97 * hash + Objects.hashCode(configuracao.getCodigoColuna());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ConfiguracaoConsultaAtendimentoDTO other = (ConfiguracaoConsultaAtendimentoDTO) obj;
        if (!Objects.equals(configuracao.getEmpresa(), other.getConfiguracao().getEmpresa())) {
            return false;
        }
        if (!Objects.equals(configuracao.getCodigoColuna(), other.getConfiguracao().getCodigoColuna())) {
            return false;
        }
        return true;
    }

    
}
