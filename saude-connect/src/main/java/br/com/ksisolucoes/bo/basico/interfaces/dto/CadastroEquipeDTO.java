package br.com.ksisolucoes.bo.basico.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroEquipeDTO implements Serializable {

    private Equipe equipe;
    private EquipeProfissional equipeProfissional;
    private List<EquipeProfissionalDTO> equipeProfissionalDTOList;

    public CadastroEquipeDTO() {
    }

    public Equipe getEquipe() {
        return equipe;
    }

    public void setEquipe(Equipe equipe) {
        this.equipe = equipe;
    }

    public EquipeProfissional getEquipeProfissional() {
        return equipeProfissional;
    }

    public void setEquipeProfissional(EquipeProfissional equipeProfissional) {
        this.equipeProfissional = equipeProfissional;
    }

    public List<EquipeProfissionalDTO> getEquipeProfissionalDTOList() {
        return equipeProfissionalDTOList;
    }

    public void setEquipeProfissionalDTOList(List<EquipeProfissionalDTO> equipeProfissionalDTOList) {
        this.equipeProfissionalDTOList = equipeProfissionalDTOList;
    }
}
