package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class LancamentosProntoAtendimentoDTO implements Serializable {

    private Atendimento atendimentoPrincipal;
    private AtendimentoInformacao atendimentoInformacao;
    private ItemContaPaciente itemContaPaciente;
    private Profissional profissional;
    private Empresa empresa;
    private Double valorMedicamentoMaterial;
    private UsuarioCadsus usuarioCadsus;
    private String codigoHonorario;
    private Long referencial;
    private ContaPaciente contaPaciente;
    private Convenio convenio;

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public ContaPaciente getContaPaciente() {
        return contaPaciente;
    }

    public void setContaPaciente(ContaPaciente contaPaciente) {
        this.contaPaciente = contaPaciente;
    }

    public String getCodigoHonorario() {
        return codigoHonorario;
    }

    public void setCodigoHonorario(String codigoHonorario) {
        this.codigoHonorario = codigoHonorario;
    }

    public Long getReferencial() {
        return referencial;
    }

    public void setReferencial(Long referencial) {
        this.referencial = referencial;
    }

    public Atendimento getAtendimentoPrincipal() {
        return atendimentoPrincipal;
    }

    public void setAtendimentoPrincipal(Atendimento atendimentoPrincipal) {
        this.atendimentoPrincipal = atendimentoPrincipal;
    }

    public AtendimentoInformacao getAtendimentoInformacao() {
        return atendimentoInformacao;
    }

    public void setAtendimentoInformacao(AtendimentoInformacao atendimentoInformacao) {
        this.atendimentoInformacao = atendimentoInformacao;
    }

    public ItemContaPaciente getItemContaPaciente() {
        return itemContaPaciente;
    }

    public void setItemContaPaciente(ItemContaPaciente itemContaPaciente) {
        this.itemContaPaciente = itemContaPaciente;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Double getValorMedicamentoMaterial() {
        return valorMedicamentoMaterial;
    }

    public void setValorMedicamentoMaterial(Double valorMedicamentoMaterial) {
        this.valorMedicamentoMaterial = valorMedicamentoMaterial;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
}
