package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.hospital.LoteAih;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class LoteAihDTO implements Serializable {
    
    public static final String PROP_LOTE_AIH = "loteAih";

    private LoteAih loteAih;
    private Long quantidadeAih;
    
    public LoteAihDTO(){
    }

    public Long getQuantidadeAih() {
        return quantidadeAih;
    }

    public void setQuantidadeAih(Long quantidadeAih) {
        this.quantidadeAih = quantidadeAih;
    }

    public LoteAih getLoteAih() {
        return loteAih;
    }

    public void setLoteAih(LoteAih loteAih) {
        this.loteAih = loteAih;
    }
}
