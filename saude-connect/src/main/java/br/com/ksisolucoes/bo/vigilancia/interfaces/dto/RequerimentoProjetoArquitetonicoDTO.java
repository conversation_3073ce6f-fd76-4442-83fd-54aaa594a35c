package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoSanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitario;

import java.util.List;

public class RequerimentoProjetoArquitetonicoDTO extends RequerimentoHidrossanitarioBaseDTO implements IRequerimentoHidrossanitarioDTO {

    private RequerimentoProjetoArquitetonicoSanitario requerimentoProjetoArquitetonicoSanitario;

    private List<AnexoPranchaDTO> listAnexosMemorial;
    private List<AnexoPranchaDTO> listAnexosMemorialExcluidos;


    @Override
    public RequerimentoVistoriaHidrossanitario getRequerimentoVistoriaHidrossanitario() {
        return null;
    }

    @Override
    public RequerimentoProjetoArquitetonicoSanitario getRequerimentoProjetoArquitetonicoSanitario() {
        return requerimentoProjetoArquitetonicoSanitario;
    }

    public void setRequerimentoProjetoArquitetonicoSanitario(RequerimentoProjetoArquitetonicoSanitario requerimentoProjetoArquitetonicoSanitario) {
        this.requerimentoProjetoArquitetonicoSanitario = requerimentoProjetoArquitetonicoSanitario;
    }

    public List<AnexoPranchaDTO> getListAnexosMemorial() {
        return listAnexosMemorial;
    }

    public void setListAnexosMemorial(List<AnexoPranchaDTO> listAnexosMemorial) {
        this.listAnexosMemorial = listAnexosMemorial;
    }

    public List<AnexoPranchaDTO> getListAnexosMemorialExcluidos() {
        return listAnexosMemorialExcluidos;
    }

    public void setListAnexosMemorialExcluidos(List<AnexoPranchaDTO> listAnexosMemorialExcluidos) {
        this.listAnexosMemorialExcluidos = listAnexosMemorialExcluidos;
    }
}
