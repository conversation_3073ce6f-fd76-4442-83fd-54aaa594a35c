package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaResponsavelTecnico;

import java.io.Serializable;
import java.util.List;

public class RequerimentoResponsavelTecnicoDTO implements Serializable {

    private List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnico;
    private List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnicoExcluidos;

    public List<EloRequerimentoVigilanciaResponsavelTecnico> getListResponsavelTecnico() {
        return listResponsavelTecnico;
    }

    public void setListResponsavelTecnico(List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnico) {
        this.listResponsavelTecnico = listResponsavelTecnico;
    }

    public List<EloRequerimentoVigilanciaResponsavelTecnico> getListResponsavelTecnicoExcluidos() {
        return listResponsavelTecnicoExcluidos;
    }

    public void setListResponsavelTecnicoExcluidos(List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnicoExcluidos) {
        this.listResponsavelTecnicoExcluidos = listResponsavelTecnicoExcluidos;
    }
}
