package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta;

import java.io.Serializable;

public class RequerimentoProjetoHidroDeclaratorioParecerRespostaDTO implements Serializable {

    private RequerimentoProjetoHidrossanitarioDeclaratorioParecer requerimentoProjetoHidrossanitarioDeclaratorioParecer;
    private RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta;

    public RequerimentoProjetoHidrossanitarioDeclaratorioParecer getRequerimentoProjetoHidrossanitarioDeclaratorioParecer() {
        return requerimentoProjetoHidrossanitarioDeclaratorioParecer;
    }

    public void setRequerimentoProjetoHidrossanitarioDeclaratorioParecer(RequerimentoProjetoHidrossanitarioDeclaratorioParecer requerimentoProjetoHidrossanitarioDeclaratorioParecer) {
        this.requerimentoProjetoHidrossanitarioDeclaratorioParecer = requerimentoProjetoHidrossanitarioDeclaratorioParecer;
    }

    public RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta getRequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta() {
        return requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta;
    }

    public void setRequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta(RequerimentoProjetoHidrossanitarioDeclaratorioParecerResposta requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta) {
        this.requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta = requerimentoProjetoHidrossanitarioDeclaratorioParecerResposta;
    }
}
