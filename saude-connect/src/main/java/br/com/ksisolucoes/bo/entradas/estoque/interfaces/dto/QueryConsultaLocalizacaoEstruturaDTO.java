package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;

/**
 * Created by su<PERSON><PERSON> on 16/10/17.
 */
public class QueryConsultaLocalizacaoEstruturaDTO implements Serializable, PesquisaObjectInterface {

    private LocalizacaoEstrutura localizacaoEstrutura;

    public LocalizacaoEstrutura getLocalizacaoEstrutura() {
        return localizacaoEstrutura;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura) {
        this.localizacaoEstrutura = localizacaoEstrutura;
    }

    @Override
    public String getDescricaoVO() {
        return getLocalizacaoEstrutura().getDescricaoEstrutura();
    }

    @Override
    public String getIdentificador() {
        return getLocalizacaoEstrutura().getCodigo().toString();
    }

}
