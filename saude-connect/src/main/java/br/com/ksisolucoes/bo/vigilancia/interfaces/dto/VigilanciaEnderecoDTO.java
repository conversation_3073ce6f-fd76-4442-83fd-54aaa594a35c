/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class VigilanciaEnderecoDTO implements Serializable {

    public static final String PROP_RUA ="rua";
    public static final String PROP_VIGILANCIA_ENDERECO ="vigilanciaEndereco";
    public static final String PROP_DESCRICAO_AREA ="descricaoArea";
    public static final String PROP_MICRO_AREA ="microArea";

    private VigilanciaEndereco vigilanciaEndereco;
    private String descricaoArea;
    private Long microArea;

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }

    public void setVigilanciaEndereco(VigilanciaEndereco vigilanciaEndereco) {
        this.vigilanciaEndereco = vigilanciaEndereco;
    }

    public String getRua(){
        return getVigilanciaEndereco().getLogradouro();
    }

    public String getDescricaoArea() {
        return descricaoArea;
    }

    public void setDescricaoArea(String descricaoArea) {
        this.descricaoArea = descricaoArea;
    }

    public Long getMicroArea() {
        return microArea;
    }

    public void setMicroArea(Long microArea) {
        this.microArea = microArea;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final VigilanciaEnderecoDTO other = (VigilanciaEnderecoDTO) obj;
        if (this.vigilanciaEndereco != other.vigilanciaEndereco && (this.vigilanciaEndereco == null || !this.vigilanciaEndereco.equals(other.vigilanciaEndereco))) {
            return false;
        }
        return true;
    }

}
