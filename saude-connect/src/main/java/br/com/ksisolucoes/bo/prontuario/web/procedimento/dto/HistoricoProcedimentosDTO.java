package br.com.ksisolucoes.bo.prontuario.web.procedimento.dto;

import br.com.ksisolucoes.util.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class HistoricoProcedimentosDTO implements Serializable {

    private Long codigoAtendimentoItem;
    private Date dataAtendimento;
    private String descricaoTipoAtendimento;
    private String descricaoProcedimento;
    private String descricaoUnidade;
    private String nomeProfissional;
    private String anotacao;
    private String justificativa;
    private String codigoCid;
    private String codigoCidItem;
    private String descricaoCidItem;

    public Long getCodigoAtendimentoItem() {
        return codigoAtendimentoItem;
    }

    public void setCodigoAtendimentoItem(Long codigoAtendimentoItem) {
        this.codigoAtendimentoItem = codigoAtendimentoItem;
    }

    public String getCodigoCid() {
        return codigoCid;
    }

    public void setCodigoCid(String codigoCid) {
        this.codigoCid = codigoCid;
    }

    public String getDataAtendimentoFormatado() {
        return Data.formatarDataHora(dataAtendimento);
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getDescricaoTipoAtendimento() {
        return descricaoTipoAtendimento;
    }

    public void setDescricaoTipoAtendimento(String descricaoTipoAtendimento) {
        this.descricaoTipoAtendimento = descricaoTipoAtendimento;
    }

    public String getDescricaoUnidade() {
        return descricaoUnidade;
    }

    public void setDescricaoUnidade(String descricaoUnidade) {
        this.descricaoUnidade = descricaoUnidade;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getAnotacao() {
        return anotacao;
    }

    public void setAnotacao(String anotacao) {
        this.anotacao = anotacao;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getDescricaoProcedimento() {
        return descricaoProcedimento;
    }

    public void setDescricaoProcedimento(String descricaoProcedimento) {
        this.descricaoProcedimento = descricaoProcedimento;
    }

    public String getDescricaoCidItem() {
        return descricaoCidItem;
    }

    public void setDescricaoCidItem(String descricaoCidItem) {
        this.descricaoCidItem = descricaoCidItem;
    }

    public String getDescricaoCidFormatado(){
        if(descricaoCidItem != null && codigoCidItem != null) {
            return "(" + codigoCidItem + ") " + descricaoCidItem;
        }else{
            return "";
        }
    }

    public String getCodigoCidItem() {
        return codigoCidItem;
    }

    public void setCodigoCidItem(String codigoCidItem) {
        this.codigoCidItem = codigoCidItem;
    }
}
