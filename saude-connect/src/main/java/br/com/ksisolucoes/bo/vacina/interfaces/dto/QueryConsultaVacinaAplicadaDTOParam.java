package br.com.ksisolucoes.bo.vacina.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaVacinaAplicadaDTOParam implements Serializable {
    
    private String descricao;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private UsuarioCadsus usuarioCadsus;
    private Long codigoInvestigacaoAgravo;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Long getCodigoInvestigacaoAgravo() {
        return codigoInvestigacaoAgravo;
    }

    public void setCodigoInvestigacaoAgravo(Long codigoInvestigacaoAgravo) {
        this.codigoInvestigacaoAgravo = codigoInvestigacaoAgravo;
    }
}
