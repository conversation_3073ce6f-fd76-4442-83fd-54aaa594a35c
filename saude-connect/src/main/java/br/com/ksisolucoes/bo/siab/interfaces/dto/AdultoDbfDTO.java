package br.com.ksisolucoes.bo.siab.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AdultoDbfDTO implements Serializable{

    public final static String PROP_CODIGO_SEGMENTO = "codigoSegmento";
    public final static String PROP_CODIGO_AREA = "codigoArea";
    public final static String PROP_CODIGO_MICRO_AREA = "codigoMicroArea";
    public final static String PROP_NUMERO_FAMILIA = "numeroFamilia";
    public final static String PROP_DATA_NASCIMENTO = "dataNascimento";
    public final static String PROP_IDADE = "idade";
    public final static String PROP_SEXO = "sexo";
    public final static String PROP_ALFABETIZADO = "alfabetizado";
    public final static String PROP_CODIGO_OCUPACAO = "codigoOcupacao";
    public final static String PROP_NOME_OCUPACAO = "nomeOcupacao";
    public final static String PROP_ALCOOLISMO = "alcoolismo";
    public final static String PROP_CHAGAS = "chagas";
    public final static String PROP_DEFICIENCIA_FISICA = "deficienciaFisica";
    public final static String PROP_DIABETES = "diabetes";
    public final static String PROP_DISTURBIO_MENTAL = "disturbioMental";
    public final static String PROP_EPILEPSIA = "epilepsia";
    public final static String PROP_GESTANTE = "gestante";
    public final static String PROP_HANSENIASE = "hanseniase";
    public final static String PROP_HIPERTENSAO_ARTERIAL = "hipertensaoArterial";
    public final static String PROP_MALARIA = "malaria";
    public final static String PROP_TUBERCULOSE = "tuberculose";
    
    private Long codigoSegmento;
    private Long codigoArea;
    private Long codigoMicroArea;
    private Long numeroFamilia;
    private Date dataNascimento;
    private Long idade;
    private String sexo;
    private String alfabetizado;
    private Long codigoOcupacao;
    private String nomeOcupacao;
    private String alcoolismo;
    private String chagas;
    private String deficienciaFisica;
    private String diabetes;
    private String disturbioMental;
    private String epilepsia;
    private String gestante;
    private String hanseniase;
    private String hipertensaoArterial;
    private String malaria;
    private String tuberculose;

    public Long getCodigoSegmento() {
        return codigoSegmento;
    }

    public void setCodigoSegmento(Long codigoSegmento) {
        this.codigoSegmento = codigoSegmento;
    }

    public Long getCodigoArea() {
        return codigoArea;
    }

    public void setCodigoArea(Long codigoArea) {
        this.codigoArea = codigoArea;
    }

    public Long getCodigoMicroArea() {
        return codigoMicroArea;
    }

    public void setCodigoMicroArea(Long codigoMicroArea) {
        this.codigoMicroArea = codigoMicroArea;
    }

    public Long getNumeroFamilia() {
        return numeroFamilia;
    }

    public void setNumeroFamilia(Long numeroFamilia) {
        this.numeroFamilia = numeroFamilia;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Long getIdade() {
        return idade;
    }

    public void setIdade(Long idade) {
        this.idade = idade;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getAlfabetizado() {
        return alfabetizado;
    }

    public void setAlfabetizado(String alfabetizado) {
        this.alfabetizado = alfabetizado;
    }

    public Long getCodigoOcupacao() {
        return codigoOcupacao;
    }

    public void setCodigoOcupacao(Long codigoOcupacao) {
        this.codigoOcupacao = codigoOcupacao;
    }

    public String getNomeOcupacao() {
        return nomeOcupacao;
    }

    public void setNomeOcupacao(String nomeOcupacao) {
        this.nomeOcupacao = nomeOcupacao;
    }

    public String getAlcoolismo() {
        return alcoolismo;
    }

    public void setAlcoolismo(String alcoolismo) {
        this.alcoolismo = alcoolismo;
    }

    public String getChagas() {
        return chagas;
    }

    public void setChagas(String chagas) {
        this.chagas = chagas;
    }

    public String getDeficienciaFisica() {
        return deficienciaFisica;
    }

    public void setDeficienciaFisica(String deficienciaFisica) {
        this.deficienciaFisica = deficienciaFisica;
    }

    public String getDiabetes() {
        return diabetes;
    }

    public void setDiabetes(String diabetes) {
        this.diabetes = diabetes;
    }

    public String getDisturbioMental() {
        return disturbioMental;
    }

    public void setDisturbioMental(String disturbioMental) {
        this.disturbioMental = disturbioMental;
    }

    public String getEpilepsia() {
        return epilepsia;
    }

    public void setEpilepsia(String epilepsia) {
        this.epilepsia = epilepsia;
    }

    public String getGestante() {
        return gestante;
    }

    public void setGestante(String gestante) {
        this.gestante = gestante;
    }

    public String getHanseniase() {
        return hanseniase;
    }

    public void setHanseniase(String hanseniase) {
        this.hanseniase = hanseniase;
    }

    public String getHipertensaoArterial() {
        return hipertensaoArterial;
    }

    public void setHipertensaoArterial(String hipertensaoArterial) {
        this.hipertensaoArterial = hipertensaoArterial;
    }

    public String getMalaria() {
        return malaria;
    }

    public void setMalaria(String malaria) {
        this.malaria = malaria;
    }

    public String getTuberculose() {
        return tuberculose;
    }

    public void setTuberculose(String tuberculose) {
        this.tuberculose = tuberculose;
    }
    
}
