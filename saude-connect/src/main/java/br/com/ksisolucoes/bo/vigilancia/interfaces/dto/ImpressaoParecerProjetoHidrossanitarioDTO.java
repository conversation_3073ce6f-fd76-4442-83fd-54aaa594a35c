package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ImpressaoParecerProjetoHidrossanitarioDTO extends RequerimentoProjetoHidrossanitarioParecer {

    private List<Profissional> fiscais;
    private String descricaoRodape;
    private String resposta;
    private String anexos;
    private RequerimentoVigilancia requerimentoVigilancia;
    private String urlQrCode;

    public List<Profissional> getFiscais() {
        return fiscais;
    }

    public void setFiscais(List<Profissional> fiscais) {
        this.fiscais = fiscais;
    }

    public String getDescricaoRodape() {
        return descricaoRodape;
    }

    public void setDescricaoRodape(String descricaoRodape) {
        this.descricaoRodape = descricaoRodape;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }
    public String getUrlQrCode() {
        return urlQrCode;
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }
}