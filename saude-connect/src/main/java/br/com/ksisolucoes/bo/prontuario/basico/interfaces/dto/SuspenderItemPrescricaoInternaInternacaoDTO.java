package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SuspenderItemPrescricaoInternaInternacaoDTO implements Serializable {
    
    private Receituario receituario;
    private List<ReceituarioItem> receituarioItemSuspenderList = new ArrayList<ReceituarioItem>();
    private String justificativa;
    
    public Receituario getReceituario() {
        return receituario;
    }

    public void setReceituario(Receituario receituario) {
        this.receituario = receituario;
    }

    public List<ReceituarioItem> getReceituarioItemSuspenderList() {
        return receituarioItemSuspenderList;
    }

    public void setReceituarioItemSuspenderList(List<ReceituarioItem> receituarioItemSuspenderList) {
        this.receituarioItemSuspenderList = receituarioItemSuspenderList;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }
    
}
