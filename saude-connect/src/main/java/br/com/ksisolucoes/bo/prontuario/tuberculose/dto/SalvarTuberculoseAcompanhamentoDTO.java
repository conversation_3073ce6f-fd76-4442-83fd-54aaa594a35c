package br.com.ksisolucoes.bo.prontuario.tuberculose.dto;

import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;

import java.io.Serializable;

public class SalvarTuberculoseAcompanhamentoDTO implements Serializable {

    private TuberculoseSintomatico tuberculoseSintomatico = new TuberculoseSintomatico();
    private TuberculoseAcompanhamento tuberculoseAcompanhamento = new TuberculoseAcompanhamento();

    public TuberculoseSintomatico getTuberculoseSintomatico() {
        return tuberculoseSintomatico;
    }

    public void setTuberculoseSintomatico(TuberculoseSintomatico tuberculoseSintomatico) {
        this.tuberculoseSintomatico = tuberculoseSintomatico;
    }

    public TuberculoseAcompanhamento getTuberculoseAcompanhamento() {
        return tuberculoseAcompanhamento;
    }

    public void setTuberculoseAcompanhamento(TuberculoseAcompanhamento tuberculoseAcompanhamento) {
        this.tuberculoseAcompanhamento = tuberculoseAcompanhamento;
    }
}
