package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServico;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServicoCota;
import br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital;
import br.com.ksisolucoes.vo.consorcio.TabelaPrecoEditalItem;
import br.com.ksisolucoes.vo.consorcio.util.ConsorcioHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConsorcioPrestadorServicoDTO implements Serializable{

    public static final String PROP_CONSORCIO_PRESTADOR_SERVICO = "consorcioPrestadorServico";
    public static final String PROP_VALOR = "valor";
    
    private ConsorcioPrestadorServico consorcioPrestadorServico;
    private List<ConsorcioPrestadorServicoCota> consorcioPrestadorServicoCotas = new ArrayList<>();
    private List<ConsorcioPrestadorServicoCota> consorcioPrestadorServicoCotasExcluir = new ArrayList<>();
    private Double valor;
    private Date dataCompetencia = CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento();

    public ConsorcioPrestadorServico getConsorcioPrestadorServico() {
        return consorcioPrestadorServico;
    }

    public void setConsorcioPrestadorServico(ConsorcioPrestadorServico consorcioPrestadorServico) {
        this.consorcioPrestadorServico = consorcioPrestadorServico;
    }

    public Double getValor() {
        boolean utilizaEdital = false;
        if (RepositoryComponentDefault.SIM.equals(getUtilizarEditalConsorcio())) {
            TabelaPrecoEdital tabelaPrecoEdital = ConsorcioHelper.carregarEditalValidoPrestadorProcedimento(consorcioPrestadorServico.getConsorcioPrestador(), Arrays.asList(consorcioPrestadorServico.getConsorcioProcedimento()));

            if(tabelaPrecoEdital != null){
                TabelaPrecoEditalItem proxyEdital = on(TabelaPrecoEditalItem.class);

                TabelaPrecoEditalItem tpei = LoadManager.getInstance(TabelaPrecoEditalItem.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxyEdital.getConsorcioProcedimento().getReferencia()), consorcioPrestadorServico.getConsorcioProcedimento().getReferencia()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxyEdital.getTabelaPrecoEdital().getCodigo()), tabelaPrecoEdital.getCodigo()))
                        .setMaxResults(1)
                        .start().getVO();

                if(tpei != null){
                    if(Coalesce.asDouble(tpei.getValorProcedimento()) > 0D){
                        valor = Coalesce.asDouble(tpei.getValorProcedimento());
                        utilizaEdital = true;
                    }
                }
            }
        }

        if (valor==null && !utilizaEdital) {
            valor = consorcioPrestadorServico.getConsorcioProcedimento().getValorProcedimento();

            if(valor == 0D || RepositoryComponentDefault.SIM_LONG.equals(consorcioPrestadorServico.getConsorcioPrestador().getFlagManterPrecoSus())){
                ProcedimentoCompetencia procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), consorcioPrestadorServico.getConsorcioProcedimento().getProcedimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), dataCompetencia))
                        .start().getVO();
                if (procedimentoCompetencia!=null) {
                    valor = procedimentoCompetencia.getValorServicoAmbulatorial();
                }
            }
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getUtilizarEditalConsorcio() {
        try{
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("utilizarEditalConsorcio");
        } catch (DAOException e) {
            Loggable.log.warn(Bundle.getStringApplication("msg_nao_foi_possivel_consultar_informacoes_sobre_parametro"));
        }
        return null;
    }

    public List<ConsorcioPrestadorServicoCota> getConsorcioPrestadorServicoCotas() {
        return consorcioPrestadorServicoCotas;
    }

    public void setConsorcioPrestadorServicoCotas(List<ConsorcioPrestadorServicoCota> consorcioPrestadorServicoCotas) {
        this.consorcioPrestadorServicoCotas = consorcioPrestadorServicoCotas;
    }

    public List<ConsorcioPrestadorServicoCota> getConsorcioPrestadorServicoCotasExcluir() {
        return consorcioPrestadorServicoCotasExcluir;
    }

    public void setConsorcioPrestadorServicoCotasExcluir(List<ConsorcioPrestadorServicoCota> consorcioPrestadorServicoCotasExcluir) {
        this.consorcioPrestadorServicoCotasExcluir = consorcioPrestadorServicoCotasExcluir;
    }
}
