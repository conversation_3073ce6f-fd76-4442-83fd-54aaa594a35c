package br.com.ksisolucoes.bo.integracao.cnes.dto;

import br.com.ksisolucoes.xml.util.DateAdapter;
import br.com.ksisolucoes.xml.util.LongAdapter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by sulivan on 16/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoHabilitacaoDadosDTO implements Serializable {

    @XmlAttribute(name = "COD_HABILITACAO")
    private String codigoHabilitacao;
    @XmlAttribute(name = "DS_HABILITACAO")
    private String descricaoHabilitacao;
    @XmlAttribute(name = "CMPT_I")
    private String competenciaInicial;
    @XmlAttribute(name = "CMPT_F")
    private String competenciaFinal;
    @XmlAttribute(name = "QT_LEITOS")
    @XmlJavaTypeAdapter(LongAdapter.class)
    private Long quantidadeLeitos;
    @XmlAttribute(name = "NU_PORTARIA")
    private String numeroPortaria;
    @XmlAttribute(name = "DT_LANCAMENTO")
    @XmlJavaTypeAdapter(DateAdapter.class)
    private Date dataLancamento;
    @XmlAttribute(name = "USUARIO")
    private String usuario;

    public String getCodigoHabilitacao() {
        return codigoHabilitacao;
    }

    public void setCodigoHabilitacao(String codigoHabilitacao) {
        this.codigoHabilitacao = codigoHabilitacao;
    }

    public String getDescricaoHabilitacao() {
        return descricaoHabilitacao;
    }

    public void setDescricaoHabilitacao(String descricaoHabilitacao) {
        this.descricaoHabilitacao = descricaoHabilitacao;
    }

    public String getCompetenciaInicial() {
        return competenciaInicial;
    }

    public void setCompetenciaInicial(String competenciaInicial) {
        this.competenciaInicial = competenciaInicial;
    }

    public String getCompetenciaFinal() {
        return competenciaFinal;
    }

    public void setCompetenciaFinal(String competenciaFinal) {
        this.competenciaFinal = competenciaFinal;
    }

    public Long getQuantidadeLeitos() {
        return quantidadeLeitos;
    }

    public void setQuantidadeLeitos(Long quantidadeLeitos) {
        this.quantidadeLeitos = quantidadeLeitos;
    }

    public String getNumeroPortaria() {
        return numeroPortaria;
    }

    public void setNumeroPortaria(String numeroPortaria) {
        this.numeroPortaria = numeroPortaria;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }
}
