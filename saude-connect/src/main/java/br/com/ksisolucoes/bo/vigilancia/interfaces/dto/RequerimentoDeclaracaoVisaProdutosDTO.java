package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDeclaracaoVisaProdutos;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RequerimentoDeclaracaoVisaProdutosDTO implements Serializable {

    private RequerimentoDeclaracaoVisaProdutos requerimentoDeclaracaoVisaProdutos;
    private TipoSolicitacao tipoSolicitacao;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList = new ArrayList<>();
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList;
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir;
    private Profissional profissionalImpressao;

    public RequerimentoDeclaracaoVisaProdutosDTO() {
    }

    public RequerimentoDeclaracaoVisaProdutosDTO(RequerimentoDeclaracaoVisaProdutos requerimentoDeclaracaoVisaProdutos, TipoSolicitacao tipoSolicitacao) {
        this.requerimentoDeclaracaoVisaProdutos = requerimentoDeclaracaoVisaProdutos;
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public RequerimentoDeclaracaoVisaProdutos getRequerimentoDeclaracaoVisaProdutos() {
        return requerimentoDeclaracaoVisaProdutos;
    }

    public void setRequerimentoDeclaracaoVisaProdutos(RequerimentoDeclaracaoVisaProdutos requerimentoDeclaracaoVisaProdutos) {
        this.requerimentoDeclaracaoVisaProdutos = requerimentoDeclaracaoVisaProdutos;
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoDTOList() {
        return requerimentoVigilanciaAnexoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList) {
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoExcluidoDTOList() {
        return requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoExcluidoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaList() {
        return eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaList = eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaExcluirList() {
        return eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaExcluirList = eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalListExcluir() {
        return requerimentoVigilanciaFiscalListExcluir;
    }

    public void setRequerimentoVigilanciaFiscalListExcluir(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir) {
        this.requerimentoVigilanciaFiscalListExcluir = requerimentoVigilanciaFiscalListExcluir;
    }

    public Profissional getProfissionalImpressao() {
        return profissionalImpressao;
    }

    public void setProfissionalImpressao(Profissional profissionalImpressao) {
        this.profissionalImpressao = profissionalImpressao;
    }
}
