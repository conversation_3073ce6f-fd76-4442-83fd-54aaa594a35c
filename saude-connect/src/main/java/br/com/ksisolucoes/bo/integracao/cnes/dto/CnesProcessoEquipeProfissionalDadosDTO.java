package br.com.ksisolucoes.bo.integracao.cnes.dto;

import br.com.ksisolucoes.xml.util.DateAdapter;
import br.com.ksisolucoes.xml.util.LongAdapter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by sulivan on 16/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoEquipeProfissionalDadosDTO implements Serializable {

    public CnesProcessoEquipeProfissionalDadosDTO() {
    }

    @XmlAttribute(name = "PROF_ID")
    private String profissionalId;
    @XmlAttribute(name = "COD_CBO")
    private String codigoCbo;
    @XmlAttribute(name = "IND_VINC")
    private String indicaVinculacao;
    @XmlAttribute(name = "TP_SUS_NAO_SUS")
    private String tipoSusNaoSus;
    @XmlAttribute(name = "CD_HORAAMB")
    @XmlJavaTypeAdapter(LongAdapter.class)
    private Long codigoHoraAmbulatorial;
    @XmlAttribute(name = "CG_HORAHOSP")
    @XmlJavaTypeAdapter(LongAdapter.class)
    private Long codigoHoraHospital;
    @XmlAttribute(name = "CGHORAOUTR")
    @XmlJavaTypeAdapter(LongAdapter.class)
    private Long codigoHoraOutro;
    @XmlAttribute(name = "FL_EQUIPEMINIMA")
    private String flagEquipeMinima;
    @XmlAttribute(name = "MICROAREA")
    private String microArea;
    @XmlAttribute(name = "CNES_OUTRAEQUIPE")
    private String cnesOutraEquipe;
    @XmlAttribute(name = "COD_MUN_OUTRAEQUIPE")
    private String codigoMunicipioOutraEquipe;
    @XmlAttribute(name = "DT_ENTRADA")
    @XmlJavaTypeAdapter(DateAdapter.class)
    private Date dataEntrada;
    @XmlAttribute(name = "DT_DESLIGAMENTO")
    @XmlJavaTypeAdapter(DateAdapter.class)
    private Date dataDesligamento;
    @XmlAttribute(name = "CNES_ATENDCOMP1")
    private String cnesAtendComplementar1;
    @XmlAttribute(name = "CNES_ATENDCOMP2")
    private String cnesAtendComplementar2;
    @XmlAttribute(name = "CNES_ATENDCOMP3")
    private String cnesAtendComplementar3;
    @XmlAttribute(name = "CNES1CHDIFER_SISTPENIT")
    private String cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
    @XmlAttribute(name = "CNES1CHDIFER_HPP")
    private String cnes1CargaHorariaDiferenciadaSistemaHpp;
    @XmlAttribute(name = "CHOUTROSCHDIFER_RESMED")
    private String cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
    @XmlAttribute(name = "USUARIO")
    private String usuario;

    public String getProfissionalId() {
        return profissionalId;
    }

    public void setProfissionalId(String profissionalId) {
        this.profissionalId = profissionalId;
    }

    public String getCodigoCbo() {
        return codigoCbo;
    }

    public void setCodigoCbo(String codigoCbo) {
        this.codigoCbo = codigoCbo;
    }

    public String getIndicaVinculacao() {
        return indicaVinculacao;
    }

    public void setIndicaVinculacao(String indicaVinculacao) {
        this.indicaVinculacao = indicaVinculacao;
    }

    public String getTipoSusNaoSus() {
        return tipoSusNaoSus;
    }

    public void setTipoSusNaoSus(String tipoSusNaoSus) {
        this.tipoSusNaoSus = tipoSusNaoSus;
    }

    public Long getCodigoHoraAmbulatorial() {
        return codigoHoraAmbulatorial;
    }

    public void setCodigoHoraAmbulatorial(Long codigoHoraAmbulatorial) {
        this.codigoHoraAmbulatorial = codigoHoraAmbulatorial;
    }

    public Long getCodigoHoraHospital() {
        return codigoHoraHospital;
    }

    public void setCodigoHoraHospital(Long codigoHoraHospital) {
        this.codigoHoraHospital = codigoHoraHospital;
    }

    public Long getCodigoHoraOutro() {
        return codigoHoraOutro;
    }

    public void setCodigoHoraOutro(Long codigoHoraOutro) {
        this.codigoHoraOutro = codigoHoraOutro;
    }

    public String getFlagEquipeMinima() {
        return flagEquipeMinima;
    }

    public void setFlagEquipeMinima(String flagEquipeMinima) {
        this.flagEquipeMinima = flagEquipeMinima;
    }

    public String getMicroArea() {
        return microArea;
    }

    public void setMicroArea(String microArea) {
        this.microArea = microArea;
    }

    public String getCnesOutraEquipe() {
        return cnesOutraEquipe;
    }

    public void setCnesOutraEquipe(String cnesOutraEquipe) {
        this.cnesOutraEquipe = cnesOutraEquipe;
    }

    public String getCodigoMunicipioOutraEquipe() {
        return codigoMunicipioOutraEquipe;
    }

    public void setCodigoMunicipioOutraEquipe(String codigoMunicipioOutraEquipe) {
        this.codigoMunicipioOutraEquipe = codigoMunicipioOutraEquipe;
    }

    public Date getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(Date dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public Date getDataDesligamento() {
        return dataDesligamento;
    }

    public void setDataDesligamento(Date dataDesligamento) {
        this.dataDesligamento = dataDesligamento;
    }

    public String getCnesAtendComplementar1() {
        return cnesAtendComplementar1;
    }

    public void setCnesAtendComplementar1(String cnesAtendComplementar1) {
        this.cnesAtendComplementar1 = cnesAtendComplementar1;
    }

    public String getCnesAtendComplementar2() {
        return cnesAtendComplementar2;
    }

    public void setCnesAtendComplementar2(String cnesAtendComplementar2) {
        this.cnesAtendComplementar2 = cnesAtendComplementar2;
    }

    public String getCnesAtendComplementar3() {
        return cnesAtendComplementar3;
    }

    public void setCnesAtendComplementar3(String cnesAtendComplementar3) {
        this.cnesAtendComplementar3 = cnesAtendComplementar3;
    }

    public String getCnes1CargaHorariaDiferenciadaSistemaPenitenciario() {
        return cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
    }

    public void setCnes1CargaHorariaDiferenciadaSistemaPenitenciario(String cnes1CargaHorariaDiferenciadaSistemaPenitenciario) {
        this.cnes1CargaHorariaDiferenciadaSistemaPenitenciario = cnes1CargaHorariaDiferenciadaSistemaPenitenciario;
    }

    public String getCnes1CargaHorariaDiferenciadaSistemaHpp() {
        return cnes1CargaHorariaDiferenciadaSistemaHpp;
    }

    public void setCnes1CargaHorariaDiferenciadaSistemaHpp(String cnes1CargaHorariaDiferenciadaSistemaHpp) {
        this.cnes1CargaHorariaDiferenciadaSistemaHpp = cnes1CargaHorariaDiferenciadaSistemaHpp;
    }

    public String getCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica() {
        return cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
    }

    public void setCargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica(String cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica) {
        this.cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica = cargaHorariaOutrosCargaHorariaDiferenciadaResidenciaMedica;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }
}
