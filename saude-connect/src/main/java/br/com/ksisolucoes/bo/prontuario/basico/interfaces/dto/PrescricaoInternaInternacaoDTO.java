package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Cuidados;
import br.com.ksisolucoes.vo.prontuario.basico.EloTipoDietaReceituario;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PrescricaoInternaInternacaoDTO implements Serializable {
    
    private Receituario receituario = new Receituario();
    private EloTipoDietaReceituario eloTipoDietaReceituario = new EloTipoDietaReceituario();
    private List<ReceituarioItemComponentesDTO> lstReceituarioItem = new ArrayList<ReceituarioItemComponentesDTO>();
    private List<Cuidados> lstCuidados = new ArrayList<Cuidados>();
    
    public Receituario getReceituario() {
        return receituario;
    }

    public void setReceituario(Receituario receituario) {
        this.receituario = receituario;
    }

    public List<ReceituarioItemComponentesDTO> getLstReceituarioItem() {
        return lstReceituarioItem;
    }

    public void setLstReceituarioItem(List<ReceituarioItemComponentesDTO> lstReceituarioItem) {
        this.lstReceituarioItem = lstReceituarioItem;
    }

    public List<Cuidados> getLstCuidados() {
        return lstCuidados;
    }

    public void setLstCuidados(List<Cuidados> lstCuidados) {
        this.lstCuidados = lstCuidados;
    }

    public EloTipoDietaReceituario getEloTipoDietaReceituario() {
        return eloTipoDietaReceituario;
    }

    public void setEloTipoDietaReceituario(EloTipoDietaReceituario eloTipoDietaReceituario) {
        this.eloTipoDietaReceituario = eloTipoDietaReceituario;
    }
}