package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.TabelaPrecoEdital;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaPrestadoresProcedimentosDTO implements Serializable {
    
    private ConsorcioPrestador consorcioPrestador;
    private Double valorTotalPrestador;
    private boolean menorValor = false;
    private TabelaPrecoEdital tabelaPrecoEdital;

    public ConsorcioPrestador getConsorcioPrestador() {
        return consorcioPrestador;
    }

    public void setConsorcioPrestador(ConsorcioPrestador consorcioPrestador) {
        this.consorcioPrestador = consorcioPrestador;
    }    

    public Double getValorTotalPrestador() {
        return Coalesce.asDouble(valorTotalPrestador);
    }

    public void setValorTotalPrestador(Double valorTotalPrestador) {
        this.valorTotalPrestador = valorTotalPrestador;
    }

    public boolean isMenorValor() {
        return menorValor;
    }

    public void setMenorValor(boolean menorValor) {
        this.menorValor = menorValor;
    }

    public TabelaPrecoEdital getTabelaPrecoEdital() {
        return tabelaPrecoEdital;
    }

    public void setTabelaPrecoEdital(TabelaPrecoEdital tabelaPrecoEdital) {
        this.tabelaPrecoEdital = tabelaPrecoEdital;
    }
}
