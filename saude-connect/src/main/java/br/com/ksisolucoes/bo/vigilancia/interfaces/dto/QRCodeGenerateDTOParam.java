package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QRCodeGenerateDTOParam implements Serializable {
    
    private String url;
    private String chave;

    public QRCodeGenerateDTOParam() {
    }

    public QRCodeGenerateDTOParam(String url, String chave) {
        this.url = url;
        this.chave = chave;
    }

    public String generateURL() {
        StringBuilder url = new StringBuilder();

        if (this.url != null) {
            url.append(this.url);

            if (this.chave != null) {
                url.append("?CHQRC=")
                        .append(this.chave);
            }
        }

        return url.toString();
    }

    public void setURL(String url) {
        this.url = url;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

}
