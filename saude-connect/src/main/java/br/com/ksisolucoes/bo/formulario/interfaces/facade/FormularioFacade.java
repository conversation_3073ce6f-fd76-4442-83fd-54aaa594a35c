/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.formulario.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.formulario.dto.ConsultaFormularioValorDTOParam;
import br.com.ksisolucoes.bo.formulario.dto.DTOFormularioEstrutura;
import br.com.ksisolucoes.bo.formulario.dto.DTOFormularioValor;
import br.com.ksisolucoes.bo.formulario.dto.QueryConsultaFormularioDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.formulario.Formulario;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.bo.formulario.FormularioBO")
public interface FormularioFacade extends Serializable{

    public DTOFormularioValor saveFormularioValores(DTOFormularioValor dTOFormularioValor) throws DAOException, ValidacaoException;
    
    public void deleteFormularioValores(DTOFormularioValor dTOFormularioValor) throws DAOException, ValidacaoException;

    public List<DTOFormularioValor> queryFormularioValor(ConsultaFormularioValorDTOParam param) throws DAOException, ValidacaoException;

    public DTOFormularioEstrutura getFormularioEstrutura(Long codigoFormulario) throws DAOException, ValidacaoException;

    public Collection<Class> getBeansWithPnlConsulta() throws DAOException, ValidacaoException;

    public DataPagingResult<DTOFormularioValor> queryPagerFormularioValor(DataPaging<ConsultaFormularioValorDTOParam> dataPaging) throws DAOException, ValidacaoException;

    public String getCorpoRelatorio(Long codigoFormulario, Long codigoFormularioValor) throws DAOException, ValidacaoException;
    
    public DataPagingResult<Formulario> consultarFormulario(DataPaging<QueryConsultaFormularioDTOParam> dataPaging) throws DAOException, ValidacaoException;
}
