package br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class SalvarReceituarioModeloDTO implements Serializable {
    
    private String descricao;
    private String anotacao;
    private Long compartilhado;
    private Long codigoReceituario;
    private TipoReceita tipoReceita;
    private Profissional profissional;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAnotacao() {
        return anotacao;
    }

    public void setAnotacao(String anotacao) {
        this.anotacao = anotacao;
    }

    public Long getCompartilhado() {
        return compartilhado;
    }

    public void setCompartilhado(Long compartilhado) {
        this.compartilhado = compartilhado;
    }

    public Long getCodigoReceituario() {
        return codigoReceituario;
    }

    public void setCodigoReceituario(Long codigoReceituario) {
        this.codigoReceituario = codigoReceituario;
    }

    public TipoReceita getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }    
}
