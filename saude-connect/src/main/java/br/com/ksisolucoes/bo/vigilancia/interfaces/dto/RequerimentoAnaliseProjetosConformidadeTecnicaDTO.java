package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoAnaliseProjetosConformidadeTecnicaDTO implements Serializable {
    
    private RequerimentoAnaliseProjeto requerimentoAnaliseProjeto;
    private Long estaConforme;

    public RequerimentoAnaliseProjeto getRequerimentoAnaliseProjeto() {
        return requerimentoAnaliseProjeto;
    }

    public void setRequerimentoAnaliseProjeto(RequerimentoAnaliseProjeto requerimentoAnaliseProjeto) {
        this.requerimentoAnaliseProjeto = requerimentoAnaliseProjeto;
    }

    public Long getEstaConforme() {
        return estaConforme;
    }

    public void setEstaConforme(Long estaConforme) {
        this.estaConforme = estaConforme;
    }
    
}