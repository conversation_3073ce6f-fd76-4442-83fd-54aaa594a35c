package br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Qware;

import java.io.Serializable;

public class AtualizarCadastrarQwareDTO implements Serializable {

    private Qware usuarioCadsusQware;
    private UsuarioCadsus usuarioCadsus;
    private String ano;
    private String semestre;

    public Qware getUsuarioCadsusQware() {
        return usuarioCadsusQware;
    }

    public void setUsuarioCadsusQware(Qware usuarioCadsusQware) {
        this.usuarioCadsusQware = usuarioCadsusQware;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getAno() {
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public String getSemestre() {
        return semestre;
    }

    public void setSemestre(String semestre) {
        this.semestre = semestre;
    }
}
