package br.com.ksisolucoes.bo.smsappservice.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsCadastro;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EnviarSmsCadastroDTO implements Serializable {

    private SmsCadastro smsCadastro;
    private SmsControleIntegracao smsControleIntegracao;
    private boolean reenvioSms;
    private List<UsuarioCadsus> lstUsuarioCadsus;

    public SmsControleIntegracao getSmsControleIntegracao() {
        return smsControleIntegracao;
    }

    public void setSmsControleIntegracao(SmsControleIntegracao smsControleIntegracao) {
        this.smsControleIntegracao = smsControleIntegracao;
    }

    public SmsCadastro getSmsCadastro() {
        return smsCadastro;
    }

    public void setSmsCadastro(SmsCadastro smsCadastro) {
        this.smsCadastro = smsCadastro;
    }

    public boolean isReenvioSms() {
        return reenvioSms;
    }

    public void setReenvioSms(boolean reenvioSms) {
        this.reenvioSms = reenvioSms;
    }

    public List<UsuarioCadsus> getLstUsuarioCadsus() {
        if(lstUsuarioCadsus == null){
            lstUsuarioCadsus = new ArrayList<UsuarioCadsus>();
        }
        return lstUsuarioCadsus;
    }

    public void setLstUsuarioCadsus(List<UsuarioCadsus> lstUsuarioCadsus) {
        this.lstUsuarioCadsus = lstUsuarioCadsus;
    }

}
