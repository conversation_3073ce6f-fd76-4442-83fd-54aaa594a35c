package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.io.Serializable;

/**
 * Created by sulivan on 16/10/17.
 */
public class QueryConsultaLocalizacaoEstruturaDTOParam implements Serializable {

    private String descricao;
    private String mascara;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private boolean exibirApenasVisivelSim;
    private boolean exibirApenasVisivelNao;
    private boolean exibirApenasVisivelInventario;
    private boolean exibirApenasPadrao;
    private LocalizacaoEstrutura localizacaoEstruturaBase;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getMascara() {
        return mascara;
    }

    public void setMascara(String mascara) {
        this.mascara = mascara;
    }

    public boolean isExibirApenasVisivelSim() {
        return exibirApenasVisivelSim;
    }

    public void setExibirApenasVisivelSim(boolean exibirApenasVisivelSim) {
        this.exibirApenasVisivelSim = exibirApenasVisivelSim;
    }

    public boolean isExibirApenasVisivelNao() {
        return exibirApenasVisivelNao;
    }

    public void setExibirApenasVisivelNao(boolean exibirApenasVisivelNao) {
        this.exibirApenasVisivelNao = exibirApenasVisivelNao;
    }

    public boolean isExibirApenasVisivelInventario() {
        return exibirApenasVisivelInventario;
    }

    public void setExibirApenasVisivelInventario(boolean exibirApenasVisivelInventario) {
        this.exibirApenasVisivelInventario = exibirApenasVisivelInventario;
    }

    public boolean isExibirApenasPadrao() {
        return exibirApenasPadrao;
    }

    public void setExibirApenasPadrao(boolean exibirApenasPadrao) {
        this.exibirApenasPadrao = exibirApenasPadrao;
    }

    public LocalizacaoEstrutura getLocalizacaoEstruturaBase() {
        return localizacaoEstruturaBase;
    }

    public void setLocalizacaoEstruturaBase(LocalizacaoEstrutura localizacaoEstruturaBase) {
        this.localizacaoEstruturaBase = localizacaoEstruturaBase;
    }
}
