/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.basico.interfaces.dto;

import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class EquipeProfissionalDTO implements Serializable, Comparable<EquipeProfissionalDTO>  {
    
    public static String PROP_EQUIPE_PROFISSIONAL = "equipeProfissional";
    public static String PROP_PROFISSIONAL_CARGA_HORARIA = "profissionalCargaHoraria";

    private EquipeProfissional equipeProfissional;
    private ProfissionalCargaHoraria profissionalCargaHoraria;

    public EquipeProfissional getEquipeProfissional() {
        return equipeProfissional;
    }

    public void setEquipeProfissional(EquipeProfissional equipeProfissional) {
        this.equipeProfissional = equipeProfissional;
    }

    public ProfissionalCargaHoraria getProfissionalCargaHoraria() {
        return profissionalCargaHoraria;
    }

    public void setProfissionalCargaHoraria(ProfissionalCargaHoraria profissionalCargaHoraria) {
        this.profissionalCargaHoraria = profissionalCargaHoraria;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final EquipeProfissionalDTO other = (EquipeProfissionalDTO) obj;
        if (this.profissionalCargaHoraria != other.profissionalCargaHoraria && (this.profissionalCargaHoraria == null || !this.profissionalCargaHoraria.equals(other.profissionalCargaHoraria))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + (this.profissionalCargaHoraria != null ? this.profissionalCargaHoraria.hashCode() : 0);
        return hash;
    }

    @Override
    public int compareTo(EquipeProfissionalDTO o2) {
        if(getProfissionalCargaHoraria() == null){
            return -1;
        }
                
        if(o2.getProfissionalCargaHoraria() == null){
            return 1;
        }
        
        if(getProfissionalCargaHoraria().getTabelaCbo() == null){
            return -1;
        }
        if(o2.getProfissionalCargaHoraria().getTabelaCbo() == null){
            return 1;
        }
        
        int retorno = getProfissionalCargaHoraria().getTabelaCbo().getDescricao().compareTo(o2.getProfissionalCargaHoraria().getTabelaCbo().getDescricao());
        if(retorno == 0){
            if(getEquipeProfissional() == null){
                return -1;
            }
            if(o2.getEquipeProfissional() == null){
                return 1;
            }
            
            if(getEquipeProfissional().getEquipeMicroArea() == null){
                return -1;
            }
            if(o2.getEquipeProfissional().getEquipeMicroArea() == null){
                return 1;
            }
            retorno = getEquipeProfissional().getEquipeMicroArea().getMicroArea().compareTo(o2.getEquipeProfissional().getEquipeMicroArea().getMicroArea());
        }
        return retorno;
    }

}
