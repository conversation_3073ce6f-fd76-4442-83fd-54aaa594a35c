/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDomicilio;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ComponenteFamiliaDTO implements Serializable {

    public final static String PROP_USUARIO_CADSUS_DOMICILIO= "usuarioCadsusDomicilio";
    public final static String PROP_DOENCAS_FORMATADO = "doencasFormatado";

    private UsuarioCadsusDomicilio usuarioCadsusDomicilio;
    private List<Doenca> doencas = new ArrayList<Doenca>();

    public List<Doenca> getDoencas() {
        return doencas;
    }

    public void setDoencas(List<Doenca> doencas) {
        this.doencas = doencas;
    }

    public UsuarioCadsusDomicilio getUsuarioCadsusDomicilio() {
        return usuarioCadsusDomicilio;
    }

    public void setUsuarioCadsusDomicilio(UsuarioCadsusDomicilio usuarioCadsusDomicilio) {
        this.usuarioCadsusDomicilio = usuarioCadsusDomicilio;
    }

//    public String getDoencasFormatado(){
//        String doencasFormatado = null;
//        if (CollectionUtils.isNotNullEmpty(doencas)) {
//            for (Doenca doenca : doencas) {
//                if (doencasFormatado == null) {
//                    doencasFormatado = doenca.getSiglaSiab();
//                } else {
//                    doencasFormatado += " / "+doenca.getSiglaSiab();
//                }
//            }
//        }
//        return doencasFormatado;
//    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ComponenteFamiliaDTO other = (ComponenteFamiliaDTO) obj;
        if (this.usuarioCadsusDomicilio != other.usuarioCadsusDomicilio && (this.usuarioCadsusDomicilio == null || !this.usuarioCadsusDomicilio.equals(other.usuarioCadsusDomicilio))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        return hash;
    }

}
