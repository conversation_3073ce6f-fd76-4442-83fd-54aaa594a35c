package br.com.ksisolucoes.bo.recepcao.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaLeito;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TransferenciaLeitoRecepcaoPanelDTO implements Serializable {

    private AtendimentoTransferenciaLeito atendimentoTransferenciaLeito;
    private Atendimento atendimento;
    private List<LeitoQuarto> lstLeitoQuarto;

    public AtendimentoTransferenciaLeito getAtendimentoTransferenciaLeito() {
        return atendimentoTransferenciaLeito;
    }

    public void setAtendimentoTransferenciaLeito(AtendimentoTransferenciaLeito atendimentoTransferenciaLeito) {
        this.atendimentoTransferenciaLeito = atendimentoTransferenciaLeito;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public List<LeitoQuarto> getLstLeitoQuarto() {
        return lstLeitoQuarto;
    }

    public void setLstLeitoQuarto(List<LeitoQuarto> lstLeitoQuarto) {
        this.lstLeitoQuarto = lstLeitoQuarto;
    }
}
