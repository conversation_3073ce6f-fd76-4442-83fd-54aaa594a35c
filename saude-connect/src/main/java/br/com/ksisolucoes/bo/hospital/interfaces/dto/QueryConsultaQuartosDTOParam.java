package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.EspecialidadeLeito;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaQuartosDTOParam implements Serializable{
    
    private String setor;
    private String quarto;
    private String nomePaciente;
    private Long situacao;
    private String campoOrdenacao;
    private String tipoOrdenacao;
    private List<Empresa> empresa;
    private EspecialidadeLeito especialidadeLeito;
    private String leitoQuartoDescricao;
    private Long tipoLeito;
    private Long sexo;
    private boolean ignorarAcentoPath;

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    public String getSetor() {
        return setor;
    }

    public void setSetor(String setor) {
        this.setor = setor;
    }

    public String getQuarto() {
        return quarto;
    }

    public void setQuarto(String quarto) {
        this.quarto = quarto;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public List<Empresa> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(List<Empresa> empresa) {
        this.empresa = empresa;
    }

    public EspecialidadeLeito getEspecialidadeLeito() {
        return especialidadeLeito;
    }

    public void setEspecialidadeLeito(EspecialidadeLeito especialidadeLeito) {
        this.especialidadeLeito = especialidadeLeito;
    }

    public String getLeitoQuartoDescricao() {
        return leitoQuartoDescricao;
    }

    public void setLeitoQuartoDescricao(String leitoQuartoDescricao) {
        this.leitoQuartoDescricao = leitoQuartoDescricao;
    }

    public Long getTipoLeito() {
        return tipoLeito;
    }

    public void setTipoLeito(Long tipoLeito) {
        this.tipoLeito = tipoLeito;
    }

    public Long getSexo() {
        return sexo;
    }

    public void setSexo(Long sexo) {
        this.sexo = sexo;
    }

    public boolean isIgnorarAcentoPath() {
        return ignorarAcentoPath;
    }

    public void setIgnorarAcentoPath(boolean ignorarAcentoPath) {
        this.ignorarAcentoPath = ignorarAcentoPath;
    }
}
