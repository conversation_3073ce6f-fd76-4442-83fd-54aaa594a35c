package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Antecedentes;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AntecedentesDTO implements Serializable {

    private Atendimento atendimento;

    private Antecedentes antecedentes;

    private boolean existePreNatalFinalizado = false;
    private boolean carregarDadosPreNatal = true;
    private boolean carregaDadosAntecedentes = true;
    private boolean carregaDadosAntecedentesPessoal = true;

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Antecedentes getAntecedentes() {
        return antecedentes;
    }

    public void setAntecedentes(Antecedentes antecedentes) {
        this.antecedentes = antecedentes;
    }

    public boolean isExistePreNatalFinalizado() {
        return existePreNatalFinalizado;
    }

    public void setExistePreNatalFinalizado(boolean existePreNatalFinalizado) {
        this.existePreNatalFinalizado = existePreNatalFinalizado;
    }

    public boolean isCarregarDadosPreNatal() {
        return carregarDadosPreNatal;
    }

    public void setCarregarDadosPreNatal(boolean carregarDadosPreNatal) {
        this.carregarDadosPreNatal = carregarDadosPreNatal;
    }

    public boolean isCarregaDadosAntecedentes() {
        return carregaDadosAntecedentes;
    }

    public void setCarregaDadosAntecedentes(boolean carregaDadosAntecedentes) {
        this.carregaDadosAntecedentes = carregaDadosAntecedentes;
    }

    public boolean isCarregaDadosAntecedentesPessoal() {
        return carregaDadosAntecedentesPessoal;
    }

    public void setCarregaDadosAntecedentesPessoal(boolean carregaDadosAntecedentesPessoal) {
        this.carregaDadosAntecedentesPessoal = carregaDadosAntecedentesPessoal;
    }
}