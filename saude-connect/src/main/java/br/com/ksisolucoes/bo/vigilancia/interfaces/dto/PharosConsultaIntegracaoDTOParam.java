package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;

import java.io.Serializable;

public class PharosConsultaIntegracaoDTOParam implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long tipoIntegracao;
    private Long situacao;
    private DatePeriod periodo;

    public Long getTipoIntegracao() {
        return tipoIntegracao;
    }

    public void setTipoIntegracao(Long tipoIntegracao) {
        this.tipoIntegracao = tipoIntegracao;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }
}
