package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoRequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioParecer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoVistoriaHidrossanitarioParecerTecnicoDTO implements Serializable {

    private List<AtividadesVigilancia> atividadesVigilanciaList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private RequerimentoVistoriaHidrossanitarioParecer requerimentoVistoriaHidrossanitarioParecer;
    private List<TipoProjetoRequerimentoVigilancia> tipoProjetoRequerimentoVigilanciaList;
    private RequerimentoVigilancia requerimentoVigilancia;
    private PnlRequerimentoVigilanciaAnexoDTO anexoDTO;

    public RequerimentoVistoriaHidrossanitarioParecer getRequerimentoVistoriaHidrossanitarioParecer() {
        return requerimentoVistoriaHidrossanitarioParecer;
    }

    public void setRequerimentoVistoriaHidrossanitarioParecer(RequerimentoVistoriaHidrossanitarioParecer requerimentoVistoriaHidrossanitarioParecer) {
        this.requerimentoVistoriaHidrossanitarioParecer = requerimentoVistoriaHidrossanitarioParecer;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        if (requerimentoVigilanciaFiscalList == null) {
            requerimentoVigilanciaFiscalList = new ArrayList<>();
        }
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<AtividadesVigilancia> getAtividadesVigilanciaList() {
        return atividadesVigilanciaList;
    }

    public void setAtividadesVigilanciaList(List<AtividadesVigilancia> atividadesVigilanciaList) {
        this.atividadesVigilanciaList = atividadesVigilanciaList;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public List<TipoProjetoRequerimentoVigilancia> getTipoProjetoRequerimentoVigilanciaList() {
        if(CollectionUtils.isEmpty(tipoProjetoRequerimentoVigilanciaList)){
            tipoProjetoRequerimentoVigilanciaList = new ArrayList<>();
        }
        return tipoProjetoRequerimentoVigilanciaList;
    }

    public void setTipoProjetoRequerimentoVigilanciaList(List<TipoProjetoRequerimentoVigilancia> tipoProjetoRequerimentoVigilanciaList) {
        this.tipoProjetoRequerimentoVigilanciaList = tipoProjetoRequerimentoVigilanciaList;
    }

    public PnlRequerimentoVigilanciaAnexoDTO getAnexoDTO() {
        return anexoDTO;
    }

    public void setAnexoDTO(PnlRequerimentoVigilanciaAnexoDTO anexoDTO) {
        this.anexoDTO = anexoDTO;
    }
}
