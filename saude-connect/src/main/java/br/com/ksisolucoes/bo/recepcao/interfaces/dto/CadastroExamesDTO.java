package br.com.ksisolucoes.bo.recepcao.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroExamesDTO implements Serializable {

    private AtendimentoExame atendimentoExame;
    private List<ExamesSolicitadosViewDTO> lstExamesSolicitadosViewDTO;
    private GerarAtendimentoDTO gerarAtendimentoDTO;

    public CadastroExamesDTO() {
    }

    public CadastroExamesDTO(AtendimentoExame atendimentoExame) {
        this.atendimentoExame = atendimentoExame;
    }

    public AtendimentoExame getAtendimentoExame() {
        if (atendimentoExame == null) {
            atendimentoExame = new AtendimentoExame();
        }
        return atendimentoExame;
    }

    public void setAtendimentoExame(AtendimentoExame atendimentoExame) {
        this.atendimentoExame = atendimentoExame;
    }

    public List<ExamesSolicitadosViewDTO> getLstExamesSolicitadosViewDTO() {
        if (lstExamesSolicitadosViewDTO == null) {
            lstExamesSolicitadosViewDTO = new ArrayList<ExamesSolicitadosViewDTO>();
        }
        return lstExamesSolicitadosViewDTO;
    }

    public void setLstExamesSolicitadosViewDTO(List<ExamesSolicitadosViewDTO> lstExamesSolicitadosViewDTO) {
        this.lstExamesSolicitadosViewDTO = lstExamesSolicitadosViewDTO;
    }

    public GerarAtendimentoDTO getGerarAtendimentoDTO() {
        return gerarAtendimentoDTO;
    }

    public void setGerarAtendimentoDTO(GerarAtendimentoDTO gerarAtendimentoDTO) {
        this.gerarAtendimentoDTO = gerarAtendimentoDTO;
    }

}
