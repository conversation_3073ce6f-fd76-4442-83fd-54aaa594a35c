package br.com.ksisolucoes.bo.basico.pesquisa.dto;

import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRespostaRoteiro;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroPerguntaRoteiroDTO implements Serializable {
    
    private PerguntaRoteiro perguntaRoteiro;
    private List<PerguntaRespostaRoteiro> respostas;

    public PerguntaRoteiro getPerguntaRoteiro() {
        return perguntaRoteiro;
    }

    public void setPerguntaRoteiro(PerguntaRoteiro perguntaRoteiro) {
        this.perguntaRoteiro = perguntaRoteiro;
    }

    public List<PerguntaRespostaRoteiro> getRespostas() {
        return respostas;
    }

    public void setRespostas(List<PerguntaRespostaRoteiro> respostas) {
        this.respostas = respostas;
    }
}
