package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class VigilanciaFinanceiroBoletoDTO implements Serializable {

    private RequerimentoVigilancia requerimentoVigilancia;
    private AutoMulta autoMulta;
    private AutoPenalidade autoPenalidade;
    private Double valorBoleto;
    private BigDecimal quantidadeTaxa;
    private BigDecimal quantidadeTaxaAlvara;
    private BigDecimal valorTaxa;
    private Date dataVencimento;
    private Taxa taxaVigente;
    private String informacoesPagador;
    private Long quantidadeParcelas;
    private Double valorParcela;
    private String observacaoBoleto;
    private List<EmissaoBoletoMultiploDTO> emissaoBoletoMultiploDTOList;
    private List<EstabelecimentoAtividadeSetorDTO> estabelecimentoAtividadeSetorDTOList;

    public Taxa getTaxaVigente() {
        return taxaVigente;
    }

    public void setTaxaVigente(Taxa taxaVigente) {
        this.taxaVigente = taxaVigente;
    }

    public BigDecimal getQuantidadeTaxa() {
        return quantidadeTaxa;
    }

    public void setQuantidadeTaxa(BigDecimal quantidadeTaxa) {
        this.quantidadeTaxa = quantidadeTaxa;
    }

    public BigDecimal getValorTaxa() {
        return valorTaxa;
    }

    public void setValorTaxa(BigDecimal valorTaxa) {
        this.valorTaxa = valorTaxa;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public Double getValorBoleto() {
        return valorBoleto;
    }

    public void setValorBoleto(Double valorBoleto) {
        this.valorBoleto = valorBoleto;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public BigDecimal getQuantidadeTaxaAlvara() {
        return quantidadeTaxaAlvara;
    }

    public void setQuantidadeTaxaAlvara(BigDecimal quantidadeTaxaAlvara) {
        this.quantidadeTaxaAlvara = quantidadeTaxaAlvara;
    }

    public List<EmissaoBoletoMultiploDTO> getEmissaoBoletoMultiploDTOList() {
        return emissaoBoletoMultiploDTOList;
    }

    public void setEmissaoBoletoMultiploDTOList(List<EmissaoBoletoMultiploDTO> emissaoBoletoMultiploDTOList) {
        this.emissaoBoletoMultiploDTOList = emissaoBoletoMultiploDTOList;
    }

    public String getInformacoesPagador() {
        return informacoesPagador;
    }

    public void setInformacoesPagador(String informacoesPagador) {
        this.informacoesPagador = informacoesPagador;
    }

    public List<EstabelecimentoAtividadeSetorDTO> getEstabelecimentoAtividadeSetorDTOList() {
        return estabelecimentoAtividadeSetorDTOList;
    }

    public void setEstabelecimentoAtividadeSetorDTOList(List<EstabelecimentoAtividadeSetorDTO> estabelecimentoAtividadeSetorDTOList) {
        this.estabelecimentoAtividadeSetorDTOList = estabelecimentoAtividadeSetorDTOList;
    }

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }

    public void setAutoMulta(AutoMulta autoMulta) {
        this.autoMulta = autoMulta;
    }

    public AutoPenalidade getAutoPenalidade() {
        return autoPenalidade;
    }

    public void setAutoPenalidade(AutoPenalidade autoPenalidade) {
        this.autoPenalidade = autoPenalidade;
    }

    public Long getQuantidadeParcelas() {
        return quantidadeParcelas;
    }

    public void setQuantidadeParcelas(Long quantidadeParcelas) {
        this.quantidadeParcelas = quantidadeParcelas;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getObservacaoBoleto() {
        return observacaoBoleto;
    }

    public void setObservacaoBoleto(String observacaoBoleto) {
        this.observacaoBoleto = observacaoBoleto;
    }
}
