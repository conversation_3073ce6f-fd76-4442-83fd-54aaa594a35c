package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoInspecoesRealizadasDTOParam implements Serializable {
    
    private Estabelecimento estabelecimento;
    private VigilanciaPessoa vigilanciaPessoa;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private SetorVigilancia setorVigilancia;
    private Profissional profissional;
    private DatePeriod periodo;
    private Long formaApresentacao;
    private Long tipoRelatorio;

    public static enum FormaApresentacao implements IEnum<RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao> {

        GERAL(0L, Bundle.getStringApplication("rotulo_data")),
        ATIVIDADE(1L, Bundle.getStringApplication("rotulo_atividade_cnae")),
        SETOR_RESPONSAVEL(2L, Bundle.getStringApplication("rotulo_setor_responsavel")),
        PROFISSIONAL(3L, Bundle.getStringApplication("rotulo_profissional")),
        ;
        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao valueOf(Long value) {
            for (RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao fa : RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao.values()) {
                if (fa.value().equals(value)) {
                    return fa;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum TipoRelatorio implements IEnum<RelatorioRelacaoInspecoesRealizadasDTOParam.TipoRelatorio> {

        DETALHADO(0L, Bundle.getStringApplication("rotulo_detalhado")),
        RESUMIDO(1L, Bundle.getStringApplication("rotulo_resumido"));

        private Long value;
        private String descricao;

        private TipoRelatorio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static RelatorioRelacaoInspecoesRealizadasDTOParam.TipoRelatorio valueOf(Long value) {
            for (RelatorioRelacaoInspecoesRealizadasDTOParam.TipoRelatorio tipoResumo : RelatorioRelacaoInspecoesRealizadasDTOParam.TipoRelatorio.values()) {
                if (tipoResumo.value().equals(value)) {
                    return tipoResumo;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getDescricaoFormaApresentacao() {
        return new EnumUtil().resolveDescricao(RelatorioRelacaoInspecoesRealizadasDTOParam.FormaApresentacao.values(), getFormaApresentacao());
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }
    @DescricaoParametro("rotulo_pessoa")
    public VigilanciaPessoa getVigilanciaPessoa() {
        return vigilanciaPessoa;
    }

    public void setVigilanciaPessoa(VigilanciaPessoa vigilanciaPessoa) {
        this.vigilanciaPessoa = vigilanciaPessoa;
    }

    @DescricaoParametro("rotulo_atividade_estabelecimento")
    public AtividadeEstabelecimento getAtividadeEstabelecimento() {
        return atividadeEstabelecimento;
    }

    public void setAtividadeEstabelecimento(AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = atividadeEstabelecimento;
    }

    @DescricaoParametro("rotulo_setor_responsavel")
    public SetorVigilancia getSetorVigilancia() {
        return setorVigilancia;
    }

    public void setSetorVigilancia(SetorVigilancia setorVigilancia) {
        this.setorVigilancia = setorVigilancia;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(Long formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public Long getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }
}
