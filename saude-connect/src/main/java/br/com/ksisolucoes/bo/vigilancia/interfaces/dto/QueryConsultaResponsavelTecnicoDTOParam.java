package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaResponsavelTecnicoDTOParam implements Serializable{

    private Long codigo;
    private String descricao;
    private String keyword;
    private String propSort;
    private boolean ascending;

    private boolean filtrarEstabelecimento;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public boolean isFiltrarEstabelecimento() {
        return filtrarEstabelecimento;
    }

    public void setFiltrarEstabelecimento(boolean filtrarEstabelecimento) {
        this.filtrarEstabelecimento = filtrarEstabelecimento;
    }
}
