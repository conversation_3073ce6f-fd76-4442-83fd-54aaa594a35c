package br.com.ksisolucoes.bo.enderecocoordenadas;

import br.com.ksisolucoes.util.maps.DadosGoogleMapsHelper;
import br.com.ksisolucoes.vo.coordenadas.EnderecoCoordenadas;
import br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico;
import org.hibernate.criterion.Restrictions;

public class DenguePontoEstrategicoHandler extends EnderecoGenericHandler {
    public DenguePontoEstrategicoHandler(LatitudeLongitudeEndereco latitudeLongitudeEndereco) {
        super(latitudeLongitudeEndereco);
    }

    public void handle(DenguePontoEstrategico denguePontoEstrategico) {
        if (denguePontoEstrategico != null && denguePontoEstrategico.getCodigo() != null) {
            EnderecoCoordenadas enderecoCoordenadas = (EnderecoCoordenadas) getSession()
                    .createCriteria(EnderecoCoordenadas.class)
                    .add(Restrictions.eq(EnderecoCoordenadas.PROP_DENGUE_PONTO_ESTRATEGICO, denguePontoEstrategico))
                    .setMaxResults(1)
                    .uniqueResult();
            if (enderecoCoordenadas == null) {
                String endereco = DadosGoogleMapsHelper.enderecoCompleto(denguePontoEstrategico);
                if (endereco != null && !endereco.isEmpty()) {
                    enderecoCoordenadas = buscaEndereco(endereco);
                    enderecoCoordenadas.setDenguePontoEstrategico(denguePontoEstrategico);
                    if (!Double.valueOf(0.0).equals(enderecoCoordenadas.getLongitude())
                            && !Double.valueOf(0.0).equals(enderecoCoordenadas.getLatitude())) {
                        salvaEndereco(enderecoCoordenadas);
                    }
                }
            } else {
                setEnderecoFromCoordenadas(enderecoCoordenadas);
            }
        }
    }
}
