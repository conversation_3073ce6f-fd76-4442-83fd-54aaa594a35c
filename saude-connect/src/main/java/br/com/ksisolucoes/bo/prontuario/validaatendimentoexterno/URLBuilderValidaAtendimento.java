package br.com.ksisolucoes.bo.prontuario.validaatendimentoexterno;

import br.com.ksisolucoes.util.EncryptorUtils;
import br.com.ksisolucoes.util.QrCodeUtils;

import javax.ws.rs.core.UriBuilder;

public class URLBuilderValidaAtendimento {

    private URLBuilderValidaAtendimento() {}

    public static String getURL(Long codigoAtendimento) {
        UriBuilder uriBuilder = UriBuilder.fromPath("http://" + QrCodeUtils.getHost() + "/validaQRCodeDocumentoAtendimento")
                .queryParam("idAtendimento", EncryptorUtils.encrypt(codigoAtendimento.toString()));
        return uriBuilder.build().toString();
    }

    public static String getURLPortal() {
        UriBuilder uriBuilder = UriBuilder.fromPath("https://receituario.celk.com.br");
        return uriBuilder.build().toString();
    }
}
