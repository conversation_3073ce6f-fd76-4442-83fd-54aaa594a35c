package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConsultaProfissionalVigilanciaDTOParam implements Serializable {
    
    private String nomeProfissional;
    private String nomeDiretor;
    private Long numeroRegistro;
    private VigilanciaEndereco vigilanciaEndereco;
    private String sortProp;
    private boolean ascending;

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public String getNomeDiretor() {
        return nomeDiretor;
    }

    public void setNomeDiretor(String nomeDiretor) {
        this.nomeDiretor = nomeDiretor;
    }

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }

    public void setVigilanciaEndereco(VigilanciaEndereco vigilanciaEndereco) {
        this.vigilanciaEndereco = vigilanciaEndereco;
    }

    public String getSortProp() {
        return sortProp;
    }

    public void setSortProp(String sortProp) {
        this.sortProp = sortProp;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Long getNumeroRegistro() {
        return numeroRegistro;
    }

    public void setNumeroRegistro(Long numeroRegistro) {
        this.numeroRegistro = numeroRegistro;
    }
    
}
