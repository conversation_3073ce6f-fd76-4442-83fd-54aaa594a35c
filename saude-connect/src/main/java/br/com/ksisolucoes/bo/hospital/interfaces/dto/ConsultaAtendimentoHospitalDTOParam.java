package br.com.ksisolucoes.bo.hospital.interfaces.dto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConsultaAtendimentoHospitalDTOParam implements Serializable {
    
    private String paciente;
    private String convenio;
    private TipoAtendimento tipoAtendimento;
    private Long atendimento;
    private String tipoPeriodo;
    private DatePeriod periodo;
    private Long situacao;
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Long getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Long atendimento) {
        this.atendimento = atendimento;
    }

    public String getTipoPeriodo() {
        return tipoPeriodo;
    }

    public void setTipoPeriodo(String tipoPeriodo) {
        this.tipoPeriodo = tipoPeriodo;
    }

    public DatePeriod getPeriodo() {
        if(periodo == null){
            return Data.adjustRangeHour(DataUtil.getDataAtual());
        }
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }    
    
}
