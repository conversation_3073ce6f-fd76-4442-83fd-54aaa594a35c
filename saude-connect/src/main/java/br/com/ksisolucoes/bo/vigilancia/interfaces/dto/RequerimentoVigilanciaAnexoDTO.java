package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo;

import java.io.File;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoVigilanciaAnexoDTO implements Serializable {
    
    private File file;
    private Long origem;
    private String nomeArquivoOriginal;
    private String descricaoAnexo;
    private Date dataPagamento;
    private TipoSolicitacaoAnexo tipoSolicitacaoAnexo;
    private RequerimentoVigilanciaAnexo requerimentoVigilanciaAnexo;
    private GerenciadorArquivo gerenciadorArquivo;

    public TipoSolicitacaoAnexo getTipoSolicitacaoAnexo() {
        return tipoSolicitacaoAnexo;
    }

    public void setTipoSolicitacaoAnexo(TipoSolicitacaoAnexo tipoSolicitacaoAnexo) {
        this.tipoSolicitacaoAnexo = tipoSolicitacaoAnexo;
    }

    public String getDescricaoAnexo() {
        return descricaoAnexo;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public Long getOrigem() {
        return origem;
    }

    public void setOrigem(Long origem) {
        this.origem = origem;
    }

    public String getNomeArquivoOriginal() {
        return nomeArquivoOriginal;
    }

    public void setNomeArquivoOriginal(String nomeArquivoOriginal) {
        this.nomeArquivoOriginal = nomeArquivoOriginal;
    }


    public String getDescricaoAnexoFormatado() {
        return Coalesce.asString(getDescricaoAnexo()).toUpperCase();
    }

    public void setDescricaoAnexo(String descricaoAnexo) {
        this.descricaoAnexo = descricaoAnexo;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public RequerimentoVigilanciaAnexo getRequerimentoVigilanciaAnexo() {
        return requerimentoVigilanciaAnexo;
    }

    public void setRequerimentoVigilanciaAnexo(RequerimentoVigilanciaAnexo requerimentoVigilanciaAnexo) {
        this.requerimentoVigilanciaAnexo = requerimentoVigilanciaAnexo;
    }

    public GerenciadorArquivo getGerenciadorArquivo() {
        return gerenciadorArquivo;
    }

    public void setGerenciadorArquivo(GerenciadorArquivo gerenciadorArquivo) {
        this.gerenciadorArquivo = gerenciadorArquivo;
    }
}
