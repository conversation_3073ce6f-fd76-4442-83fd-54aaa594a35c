package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConsultaEstabelecimentoDTOParam implements Serializable {
    
    private String razaoSocial;
    private String fantasia;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private GrupoEstabelecimento grupoEstabelecimento;
    private Long situacao = (Long) Estabelecimento.Situacao.ATIVO.value();
    private Long origemCadastro;
    private String sortProp;
    private boolean ascending;
    private Usuario usuarioResponsavel;
    private String cpfCnpj;
    private ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento;
    private VigilanciaEndereco vigilanciaEndereco;

    private Long mei;

    public Long getMei() {
        return mei;
    }

    public void setMei(Long mei) {
        this.mei = mei;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public Long getOrigemCadastro() {
        return origemCadastro;
    }

    public void setOrigemCadastro(Long origemCadastro) {
        this.origemCadastro = origemCadastro;
    }

    public GrupoEstabelecimento getGrupoEstabelecimento() {
        return grupoEstabelecimento;
    }

    public void setGrupoEstabelecimento(GrupoEstabelecimento grupoEstabelecimento) {
        this.grupoEstabelecimento = grupoEstabelecimento;
    }

    public String getSortProp() {
        return sortProp;
    }

    public void setSortProp(String sortProp) {
        this.sortProp = sortProp;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getFantasia() {
        return fantasia;
    }

    public void setFantasia(String fantasia) {
        this.fantasia = fantasia;
    }

    public AtividadeEstabelecimento getAtividadeEstabelecimento() {
        return atividadeEstabelecimento;
    }

    public void setAtividadeEstabelecimento(AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = atividadeEstabelecimento;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public Usuario getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Usuario usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public ClassificacaoGrupoEstabelecimento getClassificacaoGrupoEstabelecimento() {
        return classificacaoGrupoEstabelecimento;
    }

    public void setClassificacaoGrupoEstabelecimento(ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento) {
        this.classificacaoGrupoEstabelecimento = classificacaoGrupoEstabelecimento;
    }

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }

    public void setVigilanciaEndereco(VigilanciaEndereco vigilanciaEndereco) {
        this.vigilanciaEndereco = vigilanciaEndereco;
    }

    public static enum Situacao implements IEnum<Estabelecimento.Situacao> {

        ATIVO(0L, Bundle.getStringApplication("rotulo_ativo")),
        INATIVO(1L, Bundle.getStringApplication("rotulo_inativo")),
        PROVISORIO(2L, Bundle.getStringApplication("rotulo_provisorio")),
//        AUTORIZADO(3L, Bundle.getStringApplication("rotulo_autorizado")),
        NAO_AUTORIZADO(4L, Bundle.getStringApplication("rotulo_nao_autorizado"));

        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Estabelecimento.Situacao valeuOf(Long value) {
            for (Estabelecimento.Situacao situacao : Estabelecimento.Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

    }

    public static enum SituacaoAutorizacao implements IEnum<Estabelecimento.Situacao> {

        TODOS(null, Bundle.getStringApplication("rotulo_todos")),
        PROVISORIO(2L, Bundle.getStringApplication("rotulo_provisorio")),
        AUTORIZADO(3L, Bundle.getStringApplication("rotulo_autorizado")),
        NAO_AUTORIZADO(4L, Bundle.getStringApplication("rotulo_nao_autorizado"));

        private Long value;
        private String descricao;

        private SituacaoAutorizacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Estabelecimento.Situacao valeuOf(Long value) {
            for (Estabelecimento.Situacao situacao : Estabelecimento.Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

    }
}
