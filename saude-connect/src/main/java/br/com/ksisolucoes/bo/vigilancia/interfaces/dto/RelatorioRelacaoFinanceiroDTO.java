package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioRelacaoFinanceiroDTO implements Serializable {

    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private String tipoDocumento;
    private String protocolo;
    private Double valorTotal;

    public VigilanciaFinanceiro getVigilanciaFinanceiro() {
        return vigilanciaFinanceiro;
    }

    public void setVigilanciaFinanceiro(VigilanciaFinanceiro vigilanciaFinanceiro) {
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
    }

    public String getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public String getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(String protocolo) {
        this.protocolo = protocolo;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }
}
