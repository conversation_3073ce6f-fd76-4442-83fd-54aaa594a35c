/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.tfd.requisicaoenvioexame;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.agendamento.tfd.TipoRequisicaoExame;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaRequisicaoEnvioExameDTOParam implements Serializable {

    private List<Empresa> empresas;
    private List<UsuarioCadsus> usuarioCadsuses;
    private List<TipoRequisicaoExame> tipoRequisicaoExames;
    private List<Long> status;
    private List<Empresa> empresaDestinos;
    private Boolean dataRecebimentoPacienteNull;
    private DatePeriod datePeriodo;
    private DTOParamConfigureDefault configureParam;

    public DTOParamConfigureDefault getConfigureParam() {
        if(configureParam == null){
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public Boolean getDataRecebimentoPacienteNull() {
        return dataRecebimentoPacienteNull;
    }

    public void setDataRecebimentoPacienteNull(Boolean dataRecebimentoPacienteNull) {
        this.dataRecebimentoPacienteNull = dataRecebimentoPacienteNull;
    }

    public List<Long> getStatus() {
        return status;
    }

    public void setStatus(List<Long> status) {
        this.status = status;
    }

    public DatePeriod getDatePeriodo() {
        return datePeriodo;
    }

    public void setDatePeriodo(DatePeriod datePeriodo) {
        this.datePeriodo = datePeriodo;
    }

    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    public List<Empresa> getEmpresaDestinos() {
        return empresaDestinos;
    }

    public void setEmpresaDestinos(List<Empresa> empresaDestinos) {
        this.empresaDestinos = empresaDestinos;
    }

    public List<TipoRequisicaoExame> getTipoRequisicaoExames() {
        return tipoRequisicaoExames;
    }

    public void setTipoRequisicaoExames(List<TipoRequisicaoExame> tipoRequisicaoExames) {
        this.tipoRequisicaoExames = tipoRequisicaoExames;
    }

    public List<UsuarioCadsus> getUsuarioCadsuses() {
        return usuarioCadsuses;
    }

    public void setUsuarioCadsuses(List<UsuarioCadsus> usuarioCadsuses) {
        this.usuarioCadsuses = usuarioCadsuses;
    }


}
