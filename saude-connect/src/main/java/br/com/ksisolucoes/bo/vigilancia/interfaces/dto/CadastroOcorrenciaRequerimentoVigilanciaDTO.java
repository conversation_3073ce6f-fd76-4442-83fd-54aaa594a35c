package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroOcorrenciaRequerimentoVigilanciaDTO implements Serializable {
    
    private RequerimentoVigilancia requerimentoVigilancia;
    private Date data;
    private String descricao;
    private Long situacao;
    private List<Profissional> profissionalList;
    private String motivo;
    private Long publicoPrivado;

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public List<Profissional> getProfissionalList() {
        if(profissionalList == null){
            profissionalList = new ArrayList<>();
        }
        return profissionalList;
    }

    public void setProfissionalList(List<Profissional> profissionalList) {
        this.profissionalList = profissionalList;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public Long getPublicoPrivado() {
        return publicoPrivado;
    }

    public void setPublicoPrivado(Long publicoPrivado) {
        this.publicoPrivado = publicoPrivado;
    }
}
