package br.com.ksisolucoes.bo.frota.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaVeiculoDTOParam implements Serializable {

    private String descricao;
    private String placa;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private boolean flagValidaEstabelecimento;
    private boolean flagValidaVeiculoAtivo;

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public boolean isFlagValidaEstabelecimento() {
        return flagValidaEstabelecimento;
    }

    public void setFlagValidaEstabelecimento(boolean flagValidaEstabelecimento) {
        this.flagValidaEstabelecimento = flagValidaEstabelecimento;
    }

    public boolean isFlagValidaVeiculoAtivo() {
        return flagValidaVeiculoAtivo;
    }

    public void setFlagValidaVeiculoAtivo(boolean flagValidaVeiculoAtivo) {
        this.flagValidaVeiculoAtivo = flagValidaVeiculoAtivo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

}
