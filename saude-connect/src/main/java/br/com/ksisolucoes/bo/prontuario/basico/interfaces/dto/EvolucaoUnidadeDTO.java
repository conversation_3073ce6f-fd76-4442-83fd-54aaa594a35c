package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EvolucaoUnidadeDTO implements Serializable{
    private Date dataPrimeirosSintomasCID;
    private String descricaoEvolucao;
    private String descricaoAlergia;
    private String descricaoAnotacao;
    private Long diasRetorno;
    private Cid cid;
    private Cid cidSecundario;
    private Boolean registrarCidPatologiasPaciente;
    private ClassificacaoAtendimento classificacaoAtendimento;
    private Long acessoCompartilhado;
    private Conduta conduta;
    private List<CondutaAtendimento> condutaList;
    private Long diarreia;
    private AtendimentoMDDA atendimentoMDDA;
    private Ciap ciap;
    private Long racionalidadeSaude;
    private Long nasfs;
    private Long localAtendimento;
    private Profissional profissionalAuxiliar;
    private TabelaCbo cboProfissionalAuxiliar;
    private boolean carregarCondutaLocalAtendimento = true;
    private Long atendimentoCompartilhado;
    private Long tipoAtendimentoEsus;
    private Long atencaoDomiciliar;
    private Long vacinaEmDia;
    private Date dumGestante;
    private Long gravidezPlanejada;
    private Long idadeGestacional;
    private Long numeroGestasPrevias;
    private Long numeroPartos;
    private Boolean carregarDadosPreNatal;
    private List<UsuarioCadsusDoenca> condicoesSaudeList;
    private Long permiteOutraNotificacaoCID;
    private Long condutaCovid;
    private List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList;
    private HashSet<ClassificacaoAtendimento> classificacaoAtendimentoRemovidosList;
    private List<Cid> cidSecundarioList;


    public Long getCondutaCovid() {
        return condutaCovid;
    }

    public void setCondutaCovid(Long condutaCovid) {
        this.condutaCovid = condutaCovid;
    }

    public List<UsuarioCadsusDoenca> getCondicoesSaudeList() {
        return condicoesSaudeList;
    }

    public void setCondicoesSaudeList(List<UsuarioCadsusDoenca> condicoesSaudeList) {
        this.condicoesSaudeList = condicoesSaudeList;
    }
    private Long flagPreencheuNotificacao;

    public Long getAtendimentoCompartilhado() {
        return atendimentoCompartilhado;
    }

    public void setAtendimentoCompartilhado(Long atendimentoCompartilhado) {
        this.atendimentoCompartilhado = atendimentoCompartilhado;
    }

    public Profissional getProfissionalAuxiliar() {
        return profissionalAuxiliar;
    }

    public void setProfissionalAuxiliar(Profissional profissionalAuxiliar) {
        this.profissionalAuxiliar = profissionalAuxiliar;
    }

    public TabelaCbo getCboProfissionalAuxiliar() {
        return cboProfissionalAuxiliar;
    }

    public void setCboProfissionalAuxiliar(TabelaCbo cboProfissionalAuxiliar) {
        this.cboProfissionalAuxiliar = cboProfissionalAuxiliar;
    }

    public Cid getCidSecundario() {
        return cidSecundario;
    }

    public void setCidSecundario(Cid cidSecundario) {
        this.cidSecundario = cidSecundario;
    }

    public ClassificacaoAtendimento getClassificacaoAtendimento() {
        return classificacaoAtendimento;
    }

    public void setClassificacaoAtendimento(ClassificacaoAtendimento classificacaoAtendimento) {
        this.classificacaoAtendimento = classificacaoAtendimento;
    }

    public Boolean getRegistrarCidPatologiasPaciente() {
        return registrarCidPatologiasPaciente;
    }

    public void setRegistrarCidPatologiasPaciente(Boolean registrarCidPatologiasPaciente) {
        this.registrarCidPatologiasPaciente = registrarCidPatologiasPaciente;
    }

    public Long getDiasRetorno() {
        return diasRetorno;
    }

    public void setDiasRetorno(Long diasRetorno) {
        this.diasRetorno = diasRetorno;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public String getDescricaoEvolucao() {
        return descricaoEvolucao;
    }

    public void setDescricaoEvolucao(String descricaoEvolucao) {
        this.descricaoEvolucao = descricaoEvolucao;
    }

    public Long getAcessoCompartilhado() {
        return acessoCompartilhado;
    }

    public void setAcessoCompartilhado(Long acessoCompartilhado) {
        this.acessoCompartilhado = acessoCompartilhado;
    }

    public Conduta getConduta() {
        return conduta;
    }

    public void setConduta(Conduta conduta) {
        this.conduta = conduta;
    }

    public Long getDiarreia() {
        return diarreia;
    }

    public void setDiarreia(Long diarreia) {
        this.diarreia = diarreia;
    }

    public AtendimentoMDDA getAtendimentoMDDA() {
        return atendimentoMDDA;
    }

    public void setAtendimentoMDDA(AtendimentoMDDA atendimentoMDDA) {
        this.atendimentoMDDA = atendimentoMDDA;
    }

    public String getDescricaoAlergia() {
        return descricaoAlergia;
    }

    public void setDescricaoAlergia(String descricaoAlergia) {
        this.descricaoAlergia = descricaoAlergia;
    }

    public String getDescricaoAnotacao() {
        return descricaoAnotacao;
    }

    public void setDescricaoAnotacao(String descricaoAnotacao) {
        this.descricaoAnotacao = descricaoAnotacao;
    }

    public Ciap getCiap() {
        return ciap;
    }

    public void setCiap(Ciap ciap) {
        this.ciap = ciap;
    }

    public List<CondutaAtendimento> getCondutaList() {
        if(CollectionUtils.isEmpty(condutaList)){
            condutaList = new ArrayList<>();
        }
        return condutaList;
    }

    public void setCondutaList(List<CondutaAtendimento> condutaList) {
        this.condutaList = condutaList;
    }

    public Long getRacionalidadeSaude() {
        return racionalidadeSaude;
    }

    public void setRacionalidadeSaude(Long racionalidadeSaude) {
        this.racionalidadeSaude = racionalidadeSaude;
    }

    public Long getNasfs() {
        return nasfs;
    }

    public void setNasfs(Long nasfs) {
        this.nasfs = nasfs;
    }

    public Long getLocalAtendimento() {
        return localAtendimento;
    }

    public void setLocalAtendimento(Long localAtendimento) {
        this.localAtendimento = localAtendimento;
    }

    public boolean isCarregarCondutaLocalAtendimento() {
        return carregarCondutaLocalAtendimento;
    }

    public void setCarregarCondutaLocalAtendimento(boolean carregarCondutaLocalAtendimento) {
        this.carregarCondutaLocalAtendimento = carregarCondutaLocalAtendimento;
    }

    public Long getTipoAtendimentoEsus() {
        return tipoAtendimentoEsus;
    }

    public void setTipoAtendimentoEsus(Long tipoAtendimentoEsus) {
        this.tipoAtendimentoEsus = tipoAtendimentoEsus;
    }

    public Long getAtencaoDomiciliar() {
        return atencaoDomiciliar;
    }

    public void setAtencaoDomiciliar(Long atencaoDomiciliar) {
        this.atencaoDomiciliar = atencaoDomiciliar;
    }

    public Long getVacinaEmDia() {
        return vacinaEmDia;
    }

    public void setVacinaEmDia(Long vacinaEmDia) {
        this.vacinaEmDia = vacinaEmDia;
    }

    public Date getDumGestante() {
        return dumGestante;
    }

    public void setDumGestante(Date dumGestante) {
        this.dumGestante = dumGestante;
    }

    public Long getGravidezPlanejada() {
        return gravidezPlanejada;
    }

    public void setGravidezPlanejada(Long gravidezPlanejada) {
        this.gravidezPlanejada = gravidezPlanejada;
    }

    public Long getIdadeGestacional() {
        return idadeGestacional;
    }

    public void setIdadeGestacional(Long idadeGestacional) {
        this.idadeGestacional = idadeGestacional;
    }

    public Long getNumeroGestasPrevias() {
        return numeroGestasPrevias;
    }

    public void setNumeroGestasPrevias(Long numeroGestasPrevias) {
        this.numeroGestasPrevias = numeroGestasPrevias;
    }

    public Long getNumeroPartos() {
        return numeroPartos;
    }

    public void setNumeroPartos(Long numeroPartos) {
        this.numeroPartos = numeroPartos;
    }

    public boolean isCarregarDadosPreNatal() {
        setCarregarDadosPreNatal(carregarDadosPreNatal == null);
        return carregarDadosPreNatal;
    }

    public void setCarregarDadosPreNatal(boolean carregarDadosPreNatal) {
        this.carregarDadosPreNatal = carregarDadosPreNatal;
    }

    public Long getFlagPreencheuNotificacao() {
        return flagPreencheuNotificacao;
    }

    public void setFlagPreencheuNotificacao(Long flagPreencheuNotificacao) {
        this.flagPreencheuNotificacao = flagPreencheuNotificacao;
    }

    public Date getDataPrimeirosSintomasCID() {
        return dataPrimeirosSintomasCID;
    }

    public void setDataPrimeirosSintomasCID(Date dataPrimeirosSintomasCID) {
        this.dataPrimeirosSintomasCID = dataPrimeirosSintomasCID;
    }

    public Long getPermiteOutraNotificacaoCID() {
        return permiteOutraNotificacaoCID;
    }

    public void setPermiteOutraNotificacaoCID(Long permiteOutraNotificacaoCID) {
        this.permiteOutraNotificacaoCID = permiteOutraNotificacaoCID;
    }

    public List<AtendimentoClassificacaoAtendimento> getAtendimentoClassificacaoAtendimentoList() {
        if(CollectionUtils.isEmpty(atendimentoClassificacaoAtendimentoList)){
            atendimentoClassificacaoAtendimentoList = new ArrayList<>();
        }
        return atendimentoClassificacaoAtendimentoList;
    }

    public void setAtendimentoClassificacaoAtendimentoList(List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) {
        this.atendimentoClassificacaoAtendimentoList = atendimentoClassificacaoAtendimentoList;
    }

    public HashSet<ClassificacaoAtendimento> getClassificacaoAtendimentoRemovidosList() {
        if(CollectionUtils.isEmpty(classificacaoAtendimentoRemovidosList)){
            classificacaoAtendimentoRemovidosList = new HashSet<>();
        }
        return classificacaoAtendimentoRemovidosList;
    }

    public void setClassificacaoAtendimentoRemovidosList(HashSet<ClassificacaoAtendimento> classificacaoAtendimentoRemovidosList) {
        this.classificacaoAtendimentoRemovidosList = classificacaoAtendimentoRemovidosList;
    }

    public Boolean getCarregarDadosPreNatal() {
        return carregarDadosPreNatal;
    }

    public void setCarregarDadosPreNatal(Boolean carregarDadosPreNatal) {
        this.carregarDadosPreNatal = carregarDadosPreNatal;
    }

    public List<Cid> getCidSecundarioList() {
        return cidSecundarioList;
    }

    public void setCidSecundarioList(List<Cid> cidSecundarioList) {
        this.cidSecundarioList = cidSecundarioList;
    }
}
