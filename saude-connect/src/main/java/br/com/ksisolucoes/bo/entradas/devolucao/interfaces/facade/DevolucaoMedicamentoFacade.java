package br.com.ksisolucoes.bo.entradas.devolucao.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DevolucaoMedicamentoDTO;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DevolucaoMedicamentoItem2DTO;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DevolucaoMedicamentoItemDTO;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.QueryConsultaItensDisponiveisDevolucaoDTOParam;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.QueryConsultaLotesDisponiveisDevolucaoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoItemLote;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamento;
import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamentoItem;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */              
@EJBLocation("br.com.ksisolucoes.bo.entradas.devolucao.DevolucaoMedicamentoBO")
public interface DevolucaoMedicamentoFacade {

    public DevolucaoMedicamento cadastrarDevolucaoMedicamento(DevolucaoMedicamentoDTO dto) throws ValidacaoException, DAOException;
    
    public DevolucaoMedicamentoItem cadastrarDevolucaoMedicamentoItem(DevolucaoMedicamentoItemDTO dto) throws ValidacaoException, DAOException;
    
    public List<DevolucaoMedicamentoItem2DTO> consultarItensDisponiveisDevolucao(QueryConsultaItensDisponiveisDevolucaoDTOParam param) throws ValidacaoException, DAOException;
    
    public Double sumLotesDisponiveisDevolucao(QueryConsultaLotesDisponiveisDevolucaoDTOParam param) throws ValidacaoException, DAOException;
    
    public List<DispensacaoItemLote> consultarLotesDisponiveisDevolucao(QueryConsultaLotesDisponiveisDevolucaoDTOParam param) throws ValidacaoException, DAOException;

    public List<DispensacaoItemLote> consultarLotesDisponiveisDevolucaoAgrupados(QueryConsultaLotesDisponiveisDevolucaoDTOParam param) throws ValidacaoException, DAOException;
}
