/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.vos.interfaces;

import br.com.ksisolucoes.integracao.dao.DAOException;
import java.util.List;

/**
 * Define a inteface utilizada pela camada de persintência dos dados.
 * <AUTHOR>
 */
public interface Entidade {

    /**
     * Nome da entidade. Ex.: Pessoa
     * @return
     */
    String getNomeEntidade();
    
    Entidade newInstance();

    /**
     * Fornece os atributos da entidade
     * @return
     */
    List<? extends Atributo> getAtributos();

    /**
     * Define a interface dos Atributos da entidade. Esta interface e provida das informacoes necessarias para o mecanismo de persistencia
     */
    public static interface Atributo {

        String getNomeAtributo();
        

        ValorAtributo getValorAtributo();

        boolean isIdentificadorEntidade();

        boolean isInsert();
        
        boolean isUpdate();
        
        Atributo newInstance();

        /**
         * Interface responsavel em fornecer o valor do atributo, este valor pode assumir varias implementacoes: valores formatados, de uma outra fonte de dados, etc...
         */
        public static interface ValorAtributo {

            Object getValor() throws DAOException ;
            
            ValorAtributo newInstance();
        }
    }
}
