/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.interfaces;

import br.com.ksisolucoes.integracao.dao.interfaces.DAO;
import javax.ejb.Local;

/**
 * Interface para dimensionar um EJB para acesso Local. Esta interface estende DAO para possibilitar criar um DAO em um interface EJB para se beneficiar dos recursos de transacao.
 * <AUTHOR>
 */
@Local
public interface IntegracaoSaudeDataAcessBOLocal extends DAO {
}
