/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.integracao.dao.util.vo;

import br.com.ksisolucoes.integracao.vos.interfaces.Entidade.Atributo;

/**
 * Implementacao da interface Atributo para carga de uma entidade sincronizada com outra fonte de dados
 * <AUTHOR>
 */
public class AtributoSync implements Atributo, ModoConversao{

    private String nomeAtributoOrigem;

    private Atributo atributoDestino;

    private ModoConversao modoConversao;

    private ValorAtributo valorAtributo;

    public AtributoSync(Atributo atributoDestino) {
        this.atributoDestino = atributoDestino;
    }

    public AtributoSync(Atributo atributoDestino, ValorAtributo valorAtributo) {
        this.atributoDestino = atributoDestino;
        this.valorAtributo = valorAtributo;
    }

    public AtributoSync(String nomeAtributoOrigem, Atributo atributoDestino) {
        this.nomeAtributoOrigem = nomeAtributoOrigem;
        this.atributoDestino = atributoDestino;
    }

    public AtributoSync(String nomeAtributoOrigem, Atributo atributoDestino, ModoConversao modoConversao) {
        this.nomeAtributoOrigem = nomeAtributoOrigem;
        this.atributoDestino = atributoDestino;
        this.modoConversao = modoConversao;
    }

    public String getNomeAtributo() {
        return this.atributoDestino.getNomeAtributo();
    }

    public String getNomeAtributoOrigem() {
        return nomeAtributoOrigem;
    }

    public ValorAtributo getValorAtributo() {
        return valorAtributo;
    }

    public void setNomeAtributoOrigem(String nomeAtributoOrigem) {
        this.nomeAtributoOrigem = nomeAtributoOrigem;
    }

    public void setAtributoDestino(Atributo atributoDestino) {
        this.atributoDestino = atributoDestino;
    }

    public void setModoConversao(ModoConversao modoConversao) {
        this.modoConversao = modoConversao;
    }

    public void setValorAtributo(ValorAtributo valorAtributo) {
        this.valorAtributo = valorAtributo;
    }

    public boolean isIdentificadorEntidade() {
        return this.atributoDestino.isIdentificadorEntidade();
    }

    public boolean isInsert() {
        return this.atributoDestino.isInsert();
    }

    public boolean isUpdate() {
        return this.atributoDestino.isUpdate();
    }

    public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
        if (modoConversao != null) {
            return this.modoConversao.getValorConvertido(tipo, value);
        } else {
            return value;
        }

    }

    public Atributo newInstance(){
        AtributoSync atributoSync = new AtributoSync(this.atributoDestino);
        atributoSync.setModoConversao(this.modoConversao);
        atributoSync.setNomeAtributoOrigem(this.nomeAtributoOrigem);
        atributoSync.setValorAtributo(this.valorAtributo);
        
        return atributoSync;
    }

}
