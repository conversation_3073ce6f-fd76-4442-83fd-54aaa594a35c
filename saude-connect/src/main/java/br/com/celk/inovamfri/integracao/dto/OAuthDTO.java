package br.com.celk.inovamfri.integracao.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class OAuthDTO implements Serializable {
    
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("refresh_token")
    private String refreshToken;

    @JsonProperty("scope")
    private String scope;

    @JsonProperty("expires_in")
    private Long expires;

    @JsonIgnore
    private Date expiresDate;
    
    public OAuthDTO() {
    }

    public OAuthDTO(String accessToken, String refreshToken, String scope, Long expires, Date expiresDate) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.scope = scope;
        this.expires = expires;
        this.expiresDate = expiresDate;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Long getExpires() {
        return expires;
    }

    public void setExpires(Long expires) {
        this.expires = expires;
    }

    @JsonIgnore
    public Date getExpiresDate() {
        return expiresDate;
    }

    @JsonIgnore
    public void setExpiresDate(Date expiresDate) {
        this.expiresDate = expiresDate;
    }
}
