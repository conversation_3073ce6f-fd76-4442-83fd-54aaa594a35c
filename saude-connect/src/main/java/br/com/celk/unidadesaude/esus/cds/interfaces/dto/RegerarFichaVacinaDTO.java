package br.com.celk.unidadesaude.esus.cds.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.esus.ProcessoGeracaoFichaEsus;

import java.io.Serializable;

public class RegerarFichaVacinaDTO implements Serializable {

    private DatePeriod periodo;
    private Empresa empresa;
    private ProcessoGeracaoFichaEsus.TipoFicha tipoFicha;

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public ProcessoGeracaoFichaEsus.TipoFicha getTipoFicha() {
        return tipoFicha;
    }

    public void setTipoFicha(ProcessoGeracaoFichaEsus.TipoFicha tipoFicha) {
        this.tipoFicha = tipoFicha;
    }
}
