package br.com.celk.unidadesaude.exames.relacaoExame;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelacaoExameDTO implements Serializable {

    private TipoExame tipoExame;
    private Profissional profissionalExecutante;
    private Profissional profissionalResponsavel;
    private Long atendimento;
    private UsuarioCadsus paciente;
    private Date dataExame;
    private ExameProcedimento exameProcedimento;
    private Procedimento procedimento;
    private Convenio convenio;
    private Double precoUnitario;

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public Profissional getProfissionalExecutante() {
        return profissionalExecutante;
    }

    public void setProfissionalExecutante(Profissional profissionalExecutante) {
        this.profissionalExecutante = profissionalExecutante;
    }

    public Profissional getProfissionalResponsavel() {
        return profissionalResponsavel;
    }

    public void setProfissionalResponsavel(Profissional profissionalResponsavel) {
        this.profissionalResponsavel = profissionalResponsavel;
    }

    public Long getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Long atendimento) {
        this.atendimento = atendimento;
    }

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public Date getDataExame() {
        return dataExame;
    }

    public void setDataExame(Date dataExame) {
        this.dataExame = dataExame;
    }

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Double getPrecoUnitario() {
        return precoUnitario;
    }

    public void setPrecoUnitario(Double precoUnitario) {
        this.precoUnitario = precoUnitario;
    }

    public Procedimento getProcedimento() {
        return procedimento;
    }

    public void setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
    }

    public String getDataExameFormatado() {
        return Data.formatar(dataExame);
    }

    public String getNomeProfissionalExecutante() {
        if (getProfissionalExecutante() != null && getProfissionalExecutante().getNome() != null) {
            return getProfissionalExecutante().getNome();
        }
        return "";
    }

    public String getNomeProfissionalResponsavel() {
        if (getProfissionalResponsavel() != null && getProfissionalResponsavel().getNome() != null) {
            return getProfissionalResponsavel().getNome();
        }
        return "";
    }
}
