package br.com.celk.unidadesaude.esus.relatorios.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioSictecDTO implements Serializable {

    private String prontuario;
    private String cns;
    private String data;
    private String hora;
    private String numeroProfissional;
    private String especialidadeMedico;
    private Long conselhoClasse;
    private String prestadorAgendado;
    private String vlAmb;
    private String vlSa;
    private String codPaciente;
    private String nomePaciente;
    private String sexo;
    private String dataNascimento;
    private String maePaciente;
    private String pisPasep;
    private String ddd;
    private String telefone;
    private String municipioPaciente;
    private String uf;
    private String bairro;
    private String logradouro;
    private String cep;
    private String numeroLogradouro;
    private String municipioNascimento;
    private String ufMunicipioNascimento;
    private Long cns2;
    private String tipoAtendimento;
    private Long codigoSolicitacao;
    private String tipoProcedimento;
    private String line;
    private String tipoLogradouro;


    public String getProntuario() {
        return prontuario;
    }

    public void setProntuario(String prontuario) {
        this.prontuario = prontuario;
    }

    public String getCns() {
        return cns;
    }

    public void setCns(String cns) {
        this.cns = cns;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getNumeroProfissional() {
        return numeroProfissional;
    }

    public void setNumeroProfissional(String numeroProfissional) {
        this.numeroProfissional = numeroProfissional;
    }

    public String getEspecialidadeMedico() {
        return especialidadeMedico;
    }

    public void setEspecialidadeMedico(String especialidadeMedico) {
        this.especialidadeMedico = especialidadeMedico;
    }

    public Long getConselhoClasse() {
        return conselhoClasse;
    }

    public void setConselhoClasse(Long conselhoClasse) {
        this.conselhoClasse = conselhoClasse;
    }

    public String getPrestadorAgendado() {
        return prestadorAgendado;
    }

    public void setPrestadorAgendado(String prestadorAgendado) {
        this.prestadorAgendado = prestadorAgendado;
    }

    public String getVlAmb() {
        return vlAmb;
    }

    public void setVlAmb(String vlAmb) {
        this.vlAmb = vlAmb;
    }

    public String getVlSa() {
        return vlSa;
    }

    public void setVlSa(String vlSa) {
        this.vlSa = vlSa;
    }

    public String getCodPaciente() {
        return codPaciente;
    }

    public void setCodPaciente(String codPaciente) {
        this.codPaciente = codPaciente;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getMaePaciente() {
        return maePaciente;
    }

    public void setMaePaciente(String maePaciente) {
        this.maePaciente = maePaciente;
    }

    public String getPisPasep() {
        return pisPasep;
    }

    public void setPisPasep(String pisPasep) {
        this.pisPasep = pisPasep;
    }

    public String getDdd() {
        return ddd;
    }

    public void setDdd(String ddd) {
        this.ddd = ddd;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getMunicipioPaciente() {
        return municipioPaciente;
    }

    public void setMunicipioPaciente(String municipioPaciente) {
        this.municipioPaciente = municipioPaciente;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getLogradouro() {
        return tipoLogradouro + " " + logradouro + " " + numeroLogradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getNumeroLogradouro() {
        return numeroLogradouro;
    }

    public void setNumeroLogradouro(String numeroLogradouro) {
        this.numeroLogradouro = numeroLogradouro;
    }

    public String getMunicipioNascimento() {
        return municipioNascimento;
    }

    public void setMunicipioNascimento(String municipioNascimento) {
        this.municipioNascimento = municipioNascimento;
    }

    public String getUfMunicipioNascimento() {
        return ufMunicipioNascimento;
    }

    public void setUfMunicipioNascimento(String ufMunicipioNascimento) {
        this.ufMunicipioNascimento = ufMunicipioNascimento;
    }

    public Long getCns2() {
        return cns2;
    }

    public void setCns2(Long cns2) {
        this.cns2 = cns2;
    }

    public String getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(String tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Long getCodigoSolicitacao() {
        return codigoSolicitacao;
    }

    public void setCodigoSolicitacao(Long codigoSolicitacao) {
        this.codigoSolicitacao = codigoSolicitacao;
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getTipoLogradouro() {
        return tipoLogradouro;
    }

    public void setTipoLogradouro(String tipoLogradouro) {
        this.tipoLogradouro = tipoLogradouro;
    }
}