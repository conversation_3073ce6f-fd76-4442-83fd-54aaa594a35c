package br.com.celk.unidadesaude.esus.cds.interfaces.dto;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by sulivan on 21/07/17.
 */
public class ConsultaMedicamentoPublicoDTO implements Serializable {

    private String referencia;
    private String descricao;
    private String unidade;
    private Double saldoDisponivel;
    private String codigo;
    private String classificacao;
    private String flagControlado;
    private Long flagJudicial;
    private Long flagPortaria344;

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public Double getSaldoDisponivel() {
        return saldoDisponivel;
    }

    public void setSaldoDisponivel(Double saldoDisponivel) {
        this.saldoDisponivel = saldoDisponivel;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public Long getSaldoDisponivelFormatado() {
        return Math.round(this.getSaldoDisponivel());
    }

    public String getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }

    public String getFlagControlado() {
        return flagControlado;
    }

    public void setFlagControlado(String flagControlado) {
        this.flagControlado = flagControlado;
    }

    public Long getFlagJudicial() {
        return flagJudicial;
    }

    public void setFlagJudicial(Long flagJudicial) {
        this.flagJudicial = flagJudicial;
    }

    public Long getFlagPortaria344() {
        return flagPortaria344;
    }

    public void setFlagPortaria344(Long flagPortaria344) {
        this.flagPortaria344 = flagPortaria344;
    }
}
