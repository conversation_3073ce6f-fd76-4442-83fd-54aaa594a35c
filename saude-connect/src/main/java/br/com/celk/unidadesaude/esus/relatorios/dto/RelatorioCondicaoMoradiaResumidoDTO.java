package br.com.celk.unidadesaude.esus.relatorios.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RelatorioCondicaoMoradiaResumidoDTO implements Serializable {

    private EquipeMicroArea equipeMicroArea;
    private String tipo;
    private Long codigo;
    private String descricao;
    private Long total;

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public String getDescricaoArea() {
        StringBuilder descricao = new StringBuilder();
        descricao.append(Bundle.getStringApplication("rotulo_area"));
        descricao.append(": ");

        if (getEquipeMicroArea() != null && getEquipeMicroArea().getEquipeArea() != null && getEquipeMicroArea().getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeMicroArea().getEquipeArea().getDescricao());
        } else {
            descricao.append("Sem Área");
        }

        return descricao.toString();
    }

    public String getDescricaoMicroArea() {
        StringBuilder descricao = new StringBuilder();
        descricao.append(Bundle.getStringApplication("rotulo_micro_area"));
        descricao.append(": ");

        if (getEquipeMicroArea() != null && getEquipeMicroArea().getEquipeArea() != null && getEquipeMicroArea().getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeMicroArea().getEquipeArea().getDescricao());

            descricao.append(" / ");
            if (getEquipeMicroArea().getMicroArea() != null) {
                descricao.append(getEquipeMicroArea().getMicroArea());
            } else {
                descricao.append("Sem Microárea");
            }
        } else {
            descricao.append("Sem Área / Microárea");
        }

        return descricao.toString();
    }
}
