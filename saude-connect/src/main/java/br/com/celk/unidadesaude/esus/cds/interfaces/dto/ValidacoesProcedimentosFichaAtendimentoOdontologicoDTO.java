package br.com.celk.unidadesaude.esus.cds.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItemProcedimento;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by sulivan on 09/10/17.
 */
public class ValidacoesProcedimentosFichaAtendimentoOdontologicoDTO implements Serializable {

    private String inconsistencia;
    private List<EsusFichaOdontoItemProcedimento> lstProcedimentos = new ArrayList();

    public String getInconsistencia() {
        return inconsistencia;
    }

    public void setInconsistencia(String inconsistencia) {
        this.inconsistencia = inconsistencia;
    }

    public List<EsusFichaOdontoItemProcedimento> getLstProcedimentos() {
        return lstProcedimentos;
    }

    public void setLstProcedimentos(List<EsusFichaOdontoItemProcedimento> lstProcedimentos) {
        this.lstProcedimentos = lstProcedimentos;
    }
}
