package br.com.celk.unidadesaude.odontograma.dto;

import java.io.Serializable;

/**
 * Created by laudecir on 11/12/17.
 */
public class OdontogramaTratamentoJsonDTO implements Serializable {

    private String id;
    private SituacaoJsonDTO status;
    private boolean inativo;
    private SituacaoJsonDTO coroa;
    private SituacaoJsonDTO colo;
    private SituacaoJsonDTO raiz;
    private FacesOdontogramaJsonDTO faces;

    public OdontogramaTratamentoJsonDTO() {
    }

    public OdontogramaTratamentoJsonDTO(String id, SituacaoJsonDTO status, boolean inativo, SituacaoJsonDTO coroa, SituacaoJsonDTO colo, SituacaoJsonDTO raiz, FacesOdontogramaJsonDTO faces) {
        this.id = id;
        this.status = status;
        this.inativo = inativo;
        this.coroa = coroa;
        this.colo = colo;
        this.raiz = raiz;
        this.faces = faces;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public SituacaoJsonDTO getStatus() {
        return status;
    }

    public void setStatus(SituacaoJsonDTO status) {
        this.status = status;
    }

    public boolean isInativo() {
        return inativo;
    }

    public void setInativo(boolean inativo) {
        this.inativo = inativo;
    }

    public SituacaoJsonDTO getCoroa() {
        return coroa;
    }

    public void setCoroa(SituacaoJsonDTO coroa) {
        this.coroa = coroa;
    }

    public SituacaoJsonDTO getColo() {
        return colo;
    }

    public void setColo(SituacaoJsonDTO colo) {
        this.colo = colo;
    }

    public SituacaoJsonDTO getRaiz() {
        return raiz;
    }

    public void setRaiz(SituacaoJsonDTO raiz) {
        this.raiz = raiz;
    }

    public FacesOdontogramaJsonDTO getFaces() {
        return faces;
    }

    public void setFaces(FacesOdontogramaJsonDTO faces) {
        this.faces = faces;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OdontogramaTratamentoJsonDTO)) return false;

        OdontogramaTratamentoJsonDTO that = (OdontogramaTratamentoJsonDTO) o;

        return id.equals(that.id);

    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        return "OdontogramaJsonDTO{" +
                "id='" + id + '\'' +
                ", status=" + status +
                ", inativo=" + inativo +
                ", coroa=" + coroa +
                ", colo=" + colo +
                ", raiz=" + raiz +
                ", faces=" + faces +
                '}';
    }
}
