package br.com.celk.unidadesaude.esus.relatorios;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendDomiciliarItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RelatorioFichasAtendimentoDomiciliarDTO implements Serializable {

    private Long quantidade;
    private Long quantidadeDomicilio;
    private Profissional profissional;
    private Empresa empresa;
    private UsuarioCadsus usuarioCadsus;
    private EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem = new EsusFichaAtendDomiciliarItem();
    private List<RelatorioFichasAtendimentoDomiciliarCondicaoAvaliadaDTO> listCondicaoAvaliadas = new ArrayList<>();
    private Long totalCondicoesFA;
    private Long totalCondicoesGeral;

    public Long getTotalCondicoesGeral() {
        return totalCondicoesGeral;
    }

    public void setTotalCondicoesGeral(Long totalCondicoesGeral) {
        this.totalCondicoesGeral = totalCondicoesGeral;
    }

    public List<RelatorioFichasAtendimentoDomiciliarCondicaoAvaliadaDTO> getListCondicaoAvaliadas() {
        return listCondicaoAvaliadas;
    }

    public void setListCondicaoAvaliadas(List<RelatorioFichasAtendimentoDomiciliarCondicaoAvaliadaDTO> listCondicaoAvaliadas) {
        this.listCondicaoAvaliadas = listCondicaoAvaliadas;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public EsusFichaAtendDomiciliarItem getEsusFichaAtendDomiciliarItem() {
        return esusFichaAtendDomiciliarItem;
    }

    public void setEsusFichaAtendDomiciliarItem(EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem) {
        this.esusFichaAtendDomiciliarItem = esusFichaAtendDomiciliarItem;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Long getQuantidadeDomicilio() {
        return quantidadeDomicilio;
    }

    public void setQuantidadeDomicilio(Long quantidadeDomicilio) {
        this.quantidadeDomicilio = quantidadeDomicilio;
    }


    public Long getTotalCondicoesFA() {
        return totalCondicoesFA;
    }

    public void setTotalCondicoesFA(Long totalCondicoesFA) {
        this.totalCondicoesFA = totalCondicoesFA;
    }
}
