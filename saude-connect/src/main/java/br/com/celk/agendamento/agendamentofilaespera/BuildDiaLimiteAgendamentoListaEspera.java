package br.com.celk.agendamento.agendamentofilaespera;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.*;

public class BuildDiaLimiteAgendamentoListaEspera {

    private BuildDiaLimiteAgendamentoListaEspera() {

    }

    public static Date diasLimiteAgendamentoListaEspera(int quantidadeDiasLimite) {
        return quantidadeDiasLimite > 0 ? getDataLimiteAgendamento(quantidadeDiasLimite) : null;
    }

    private static Date getDataLimiteAgendamento(int quantidadeDiasLimite) {
        List<Date> datasIndisponiveis = new ArrayList<>();
        Date dataAtual = DataUtil.getDataAtual();
        Date dataIndisponivel = Data.adjustRangeHour(dataAtual).getDataInicial();
        datasIndisponiveis.add(dataIndisponivel);

        int diasUteisAdicionados = 0;
        while (diasUteisAdicionados < quantidadeDiasLimite) {
            Date data = Data.adjustRangeHour(Data.addDias(dataAtual, datasIndisponiveis.size())).getDataInicial();
            datasIndisponiveis.add(data);
            if (Data.isDiaUtil(data)) diasUteisAdicionados++;
        }

        return orderDescAndSelectFirst(datasIndisponiveis);
    }

    private static Date orderDescAndSelectFirst(List<Date> datasIndisponiveis) {
        Collections.sort(datasIndisponiveis, new Comparator<Date>() {
            @Override
            public int compare(Date data1, Date data2) {
                return data2.compareTo(data1);
            }
        });
        return Lambda.selectFirst(datasIndisponiveis, Matchers.notNullValue());
    }
}
