package br.com.celk.agendamento.ppi.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.PpiSituacao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;

import java.io.Serializable;
import java.util.*;

public class PpiSecretariaDTO extends BaseRootVO implements Serializable {

    private Long cdPpiSecretaria;
    private Long cdSecretaria;
    private Date dtCompetencia;
    private Double vlPpi;
    private Double vlPpiOriginal;
    private Double vlUsado;
    private Double vlGlobal;

    private Double vlGlobalOriginal;
    private Double vlAdicional;

    private Double vlAdicionalOriginal;
    private List<PpiTipoExameDTO> ppiTiposExameDTO = new ArrayList<>();
    private String contratosAdicionais;
    private Long stSituacao;
    private List<PpiContratoAdicionalDTO> contratos = new ArrayList<>();
    private String nomeSecretaria;
    private String cnesSecretaria;
    private Empresa secretaria;
    private Double vlAlocado;
    private Double vlAlocadoUsado;
    private Boolean stFoiResgatado;
    private Boolean permiteResgate;
    private Boolean permiteResgateOriginal;
    private String descricaoClassificacao;
    private Long cdClassificacao;
    private TipoProcedimentoClassificacao tipoProcedimentoClassificacao;
    private Integer agendaSemSaldo;
    private static final String VALUE_SEPARATOR = ",";
    private static final String OBJECT_SEPARATOR = ";";

    public PpiSecretariaDTO() {
        vlGlobal = 0D;
        vlPpi = 0D;
        vlPpiOriginal = 0D;
        vlUsado = 0D;
        vlAlocado = 0D;
        vlAlocadoUsado = 0D;
        vlAdicional = 0D;
        stFoiResgatado = false;
        permiteResgate = true;
        permiteResgateOriginal = true;
        stSituacao = PpiSituacao.ATIVO.value();
        agendaSemSaldo = 0;
    }

    public Long getCdPpiSecretaria() {
        return cdPpiSecretaria;
    }

    public void setCdPpiSecretaria(Long cdPpiSecretaria) {
        this.cdPpiSecretaria = cdPpiSecretaria;
    }

    public Long getCdSecretaria() {
        return cdSecretaria;
    }

    public void setCdSecretaria(Long cdSecretaria) {
        this.cdSecretaria = cdSecretaria;
    }

    public Date getDtCompetencia() {
        return dtCompetencia;
    }

    public void setDtCompetencia(Date dtCompetencia) {
        this.dtCompetencia = dtCompetencia;
    }

    public Double getVlPpi() {
        return vlPpi;
    }

    public void setVlPpi(Double vlPpi) {
        this.vlPpi = vlPpi;
    }

    public Double getVlPpiOriginal() {
        return vlPpiOriginal;
    }

    public void setVlPpiOriginal(Double vlPpiOriginal) {
        this.vlPpiOriginal = vlPpiOriginal;
    }

    public void setVlUsado(Double vlUsado) {
        this.vlUsado = vlUsado;
    }

    public Double getVlGlobal() {
        if (vlGlobalOriginal == null || vlGlobalOriginal.equals(0d)) {

            this.vlGlobal = vlPpi + vlAdicional;
            return this.vlGlobal;
        }

        this.vlGlobal = calcularValorGlobal();

        return this.vlGlobal;
    }

    private Double calcularValorGlobal() {
        Dinheiro dVlPpi = new Dinheiro(vlPpi);
        Dinheiro dVlPpiOri = new Dinheiro(vlPpiOriginal);
        dVlPpi = dVlPpi.subtrair(dVlPpiOri);
        Dinheiro dVlGlobal = new Dinheiro(vlGlobalOriginal);
        Dinheiro dVlAdicional = new Dinheiro(vlAdicional);
        Dinheiro dVlAdicionalOriginal = new Dinheiro(vlAdicionalOriginal);
        dVlGlobal = dVlGlobal.subtrair(dVlAdicionalOriginal);
        dVlGlobal = dVlGlobal.somar(dVlAdicional);

        return dVlGlobal.somar(dVlPpi).doubleValue();
    }

    public void setVlGlobal(Double vlGlobal) {
        this.vlGlobal = vlGlobal;
    }

    public Double getVlGlobalOriginal() {
        return vlGlobalOriginal;
    }

    public void setVlGlobalOriginal(Double vlGlobalOriginal) {
        this.vlGlobalOriginal = vlGlobalOriginal;
    }

    public Double getVlAdicional() {
        return vlAdicional;
    }

    public void setVlAdicional(Double vlAdicional) {
        this.vlAdicional = vlAdicional;
    }

    public Double getVlAdicionalOriginal() {
        return vlAdicionalOriginal;
    }

    public void setVlAdicionalOriginal(Double vlAdicionalOriginal) {
        this.vlAdicionalOriginal = vlAdicionalOriginal;
    }

    public List<PpiTipoExameDTO> getPpiTiposExameDTO() {
        return ppiTiposExameDTO;
    }

    public void setPpiTiposExameDTO(List<PpiTipoExameDTO> ppiTiposExameDTO) {
        this.ppiTiposExameDTO = ppiTiposExameDTO;
    }

    public String getContratosAdicionais() {
        return contratosAdicionais;
    }

    public void setContratosAdicionais(String contratosAdicionais) {
        this.contratosAdicionais = contratosAdicionais;
        contratos = stringToList(contratosAdicionais);
        vlAdicional = somarContratosAdicionais(contratos);
        vlGlobal = new Dinheiro(vlAdicional).somar(vlPpi).doubleValue();
    }

    public Long getStSituacao() {
        return stSituacao;
    }

    public void setStSituacao(Long stSituacao) {
        this.stSituacao = stSituacao;
    }

    public List<PpiContratoAdicionalDTO> getContratos() {
        return contratos;
    }

    public void setContratos(List<PpiContratoAdicionalDTO> contratos) {
        this.contratos = contratos;
        contratosAdicionais = listToString(contratos);
        vlAdicional = somarContratosAdicionais(contratos);
        vlGlobal = new Dinheiro(vlAdicional).somar(vlPpi).doubleValue();
    }

    public String getNomeSecretaria() {
        return nomeSecretaria;
    }

    public void setNomeSecretaria(String nomeSecretaria) {
        this.nomeSecretaria = nomeSecretaria;
    }

    public String getCnesSecretaria() {
        return cnesSecretaria;
    }

    public void setCnesSecretaria(String cnesSecretaria) {
        this.cnesSecretaria = cnesSecretaria;
    }

    public Double getVlGlobalUsado() {
        Dinheiro vlCompartilhadoUsado = new Dinheiro(getVlCompartilhadoUsado());
        Dinheiro vlAlocadoUsado = new Dinheiro(getVlAlocadoUsado());
        return vlCompartilhadoUsado.somar(vlAlocadoUsado).doubleValue();
    }

    public Double getSaldoGlobal() {
        Dinheiro saldoCompartilhado = new Dinheiro(getSaldoCompartilhado());
        Dinheiro saldoAlocado = new Dinheiro(getSaldoAlocado());
        return saldoCompartilhado.somar(saldoAlocado).doubleValue();
    }

    public Double getVlUsadoTotal() {
        Dinheiro vlAlocadoUsado = new Dinheiro(getVlAlocadoUsado());
        Dinheiro vlCompartilhadoUsado = new Dinheiro(getVlCompartilhadoUsado());
        return vlAlocadoUsado.somar(vlCompartilhadoUsado).doubleValue();
    }

    public Double getVlCompartilhado() {
        Dinheiro valorGlobal = new Dinheiro(vlGlobal);
        Dinheiro valorAlocado = new Dinheiro(getVlAlocado());
        return valorGlobal.subtrair(valorAlocado).doubleValue();
    }

    public Double getVlCompartilhadoUsado() {
        return vlUsado;
    }

    public Double getSaldoCompartilhado() {
        Dinheiro valorCompartilhado = new Dinheiro(getVlCompartilhado());
        Dinheiro valorUsado = new Dinheiro(vlUsado);
        return valorCompartilhado.subtrair(valorUsado).doubleValue();
    }

    /**
     * @return o valor alocado calculado na QueryPpiSecretariaPager ou o calculado a partir do PpiTipoExameDTO
     */
    public Double getVlAlocado() {
        if (ppiTiposExameDTO.isEmpty() && new Dinheiro(vlAlocado).compareTo(Dinheiro.ZERO) > 0) return vlAlocado;
        Dinheiro valor = Dinheiro.ZERO;
        for (PpiTipoExameDTO ppiTipoExameDTO : ppiTiposExameDTO) {
            valor = valor.somar(new Dinheiro(ppiTipoExameDTO.getVlPpi()));
        }
        return valor.doubleValue();
    }

    /**
     * @return o valor usado do alocado calculado na QueryPpiSecretariaPager ou o calculado a partir do PpiTipoExameDTO
     */
    public Double getVlAlocadoUsado() {
        if (ppiTiposExameDTO.isEmpty() && new Dinheiro(vlAlocadoUsado).compareTo(Dinheiro.ZERO) > 0)
            return vlAlocadoUsado;
        Dinheiro valor = Dinheiro.ZERO;
        for (PpiTipoExameDTO ppiTipoExameDTO : ppiTiposExameDTO) {
            valor = valor.somar(new Dinheiro(ppiTipoExameDTO.getVlUsadoTotal()));
        }
        return valor.doubleValue();
    }

    public Double getSaldoAlocado() {
        Dinheiro vlAlocado = new Dinheiro(getVlAlocado());
        Dinheiro vlAlocadoUsado = new Dinheiro(getVlAlocadoUsado());
        return vlAlocado.subtrair(vlAlocadoUsado).doubleValue();
    }

    public String getDtCompetenciaFormatada() {
        return Data.formatarMesAno(dtCompetencia);
    }

    private Double somarContratosAdicionais(List<PpiContratoAdicionalDTO> contratosAdicionais) {
        if (contratosAdicionais == null) return 0D;
        Dinheiro soma = Dinheiro.ZERO;
        for (PpiContratoAdicionalDTO contrato : contratosAdicionais) {
            soma = soma.somar(new Dinheiro(contrato.getValorAdicional()));
        }
        return soma.doubleValue();
    }

    private List<PpiContratoAdicionalDTO> stringToList(String contrato) {
        if (contrato != null && !contrato.isEmpty()) {
            List<PpiContratoAdicionalDTO> ppiContratoAdicionalDTOList = new ArrayList<>();
            Map<String, String> contratoMap = new HashMap<String, String>();
            String[] contratoValue = contrato.split(";");
            for (String value : contratoValue) {
                PpiContratoAdicionalDTO dto = new PpiContratoAdicionalDTO();
                String[] keyValue = value.split(",");
                contratoMap.put(keyValue[0], keyValue[1]);
                dto.setNumeroContrato(keyValue[0]);
                dto.setValorAdicional(Double.parseDouble(keyValue[1]));
                ppiContratoAdicionalDTOList.add(dto);
            }
            return ppiContratoAdicionalDTOList;
        }
        return null;
    }

    private String listToString(List<PpiContratoAdicionalDTO> contratosAdicionais) {
        if (contratosAdicionais == null) return null;
        StringBuilder contrato = new StringBuilder();
        for (PpiContratoAdicionalDTO contratoAdicional : contratosAdicionais) {
            contrato
                    .append(contratoAdicional.getNumeroContrato())
                    .append(VALUE_SEPARATOR)
                    .append(contratoAdicional.getValorAdicional().toString())
                    .append(OBJECT_SEPARATOR);
        }
        return contrato.toString();
    }

    public Empresa getSecretaria() {
        return secretaria;
    }

    public void setSecretaria(Empresa secretaria) {
        this.secretaria = secretaria;
    }

    public void setVlAlocado(Double vlAlocado) {
        this.vlAlocado = vlAlocado;
    }

    public void setVlAlocadoUsado(Double vlAlocadoUsado) {
        this.vlAlocadoUsado = vlAlocadoUsado;
    }

    public void setVlCompartilhadoUsado(Double vlCompartilhadoUsado) {
        this.vlUsado = vlCompartilhadoUsado;
    }

    public Boolean getStFoiResgatado() {
        return stFoiResgatado;
    }

    public void setStFoiResgatado(Boolean stFoiResgatado) {
        this.stFoiResgatado = stFoiResgatado;
    }

    public Boolean getPermiteResgate() {
        return permiteResgate;
    }

    public void setPermiteResgate(Boolean permiteResgate) {
        this.permiteResgate = permiteResgate;
    }

    public String getDescricaoClassificacao() {
        return descricaoClassificacao;
    }

    public void setDescricaoClassificacao(String descricaoClassificacao) {
        this.descricaoClassificacao = descricaoClassificacao;
    }

    public Long getCdClassificacao() {
        return cdClassificacao;
    }

    public void setCdClassificacao(Long cdClassificacao) {
        this.cdClassificacao = cdClassificacao;
    }

    public TipoProcedimentoClassificacao getTipoProcedimentoClassificacao() {
        return tipoProcedimentoClassificacao;
    }

    public void setTipoProcedimentoClassificacao(TipoProcedimentoClassificacao tipoProcedimentoClassificacao) {
        this.tipoProcedimentoClassificacao = tipoProcedimentoClassificacao;
    }

    public Boolean getPermiteResgateOriginal() {
        return permiteResgateOriginal;
    }

    public void setPermiteResgateOriginal(Boolean permiteResgateOriginal) {
        this.permiteResgateOriginal = permiteResgateOriginal;
    }

    /*public Integer getAgendaSemSaldo() {
        return agendaSemSaldo;
    }

    public void setAgendaSemSaldo(Integer agendaSemSaldo) {
        this.agendaSemSaldo = agendaSemSaldo;
    }*/
}
