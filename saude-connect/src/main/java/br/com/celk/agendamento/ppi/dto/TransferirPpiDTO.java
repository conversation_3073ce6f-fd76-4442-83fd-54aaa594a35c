package br.com.celk.agendamento.ppi.dto;

import java.io.Serializable;

public class TransferirPpiDTO implements Serializable {

    private PpiTipoExameDTO tipoExameOrigem;
    private PpiExameDTO exameOrigem;
    private PpiExameDTO exameDestino;
    private PpiTipoExameDTO tipoExameDestino;
    private PpiSecretariaDTO secretariaDestino;
    private Double valor;

    public TransferirPpiDTO(PpiExameDTO exameOrigem, PpiExameDTO exameDestino, Double valor) {
        this.exameOrigem = exameOrigem;
        this.exameDestino = exameDestino;
        this.valor = valor;
    }

    public TransferirPpiDTO(PpiExameDTO exameOrigem, PpiTipoExameDTO tipoExameDestino, Double valor) {
        this.exameOrigem = exameOrigem;
        this.tipoExameDestino = tipoExameDestino;
        this.valor = valor;
    }

    public TransferirPpiDTO(PpiTipoExameDTO origem, PpiTipoExameDTO tipoExameDestino, Double valor) {
        this.tipoExameOrigem = origem;
        this.tipoExameDestino = tipoExameDestino;
        this.valor = valor;
    }

    public TransferirPpiDTO(PpiTipoExameDTO origem, PpiSecretariaDTO secretariaDestino, Double valor) {
        this.tipoExameOrigem = origem;
        this.secretariaDestino = secretariaDestino;
        this.valor = valor;
    }

    public PpiTipoExameDTO getTipoExameOrigem() {
        return tipoExameOrigem;
    }

    public void setTipoExameOrigem(PpiTipoExameDTO origem) {
        this.tipoExameOrigem = origem;
    }

    public PpiTipoExameDTO getTipoExameDestino() {
        return tipoExameDestino;
    }

    public void setTipoExameDestino(PpiTipoExameDTO tipoExameDestino) {
        this.tipoExameDestino = tipoExameDestino;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public PpiSecretariaDTO getSecretariaDestino() {
        return secretariaDestino;
    }

    public void setSecretariaDestino(PpiSecretariaDTO secretariaDestino) {
        this.secretariaDestino = secretariaDestino;
    }

    public PpiExameDTO getExameDestino() {
        return exameDestino;
    }

    public void setExameDestino(PpiExameDTO exameDestino) {
        this.exameDestino = exameDestino;
    }

    public PpiExameDTO getExameOrigem() {
        return exameOrigem;
    }

    public void setExameOrigem(PpiExameDTO exameOrigem) {
        this.exameOrigem = exameOrigem;
    }
}
