package br.com.celk.agendamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.agendamento.dto.CopiaAgendaDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Feriado;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.hamcrest.Matchers;

import java.io.Serializable;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadastroAgendaBehavior implements Serializable {

    private final Agenda agenda;
    private boolean tipoAgendaDiario;
    private boolean tipoAgendaHorario;
    private boolean tipoAgendaPersonalizada;

    public CadastroAgendaBehavior(Agenda agenda) {
        this.agenda = agenda;
        verificarTipoAgendaDiario();
        verificarTipoAgendaHorario();
        verificarTipoAgendaPersonalizada();
    }

    // Validação de conflitos de horários já registrados no banco de dados
    public String validarConflitosAgendasSalvas(AgendaGradeAtendimento agendaGradeAtendimento) throws ValidacaoException, DAOException {
        return validarConflitosAgendasSalvas(agendaGradeAtendimento, null, false);
    }
    
    // Validação de conflitos de horários já registrados no banco de dados
    public String validarConflitosAgendasSalvas(AgendaGradeAtendimento agendaGradeAtendimento, Profissional novoProfissionalAgenda, boolean retornarCodigoAgendaConflito) throws ValidacaoException, DAOException {
        ValidaConflitoHorariosDTOParam validaConflitoHorariosDTOParam = new ValidaConflitoHorariosDTOParam(agenda, agendaGradeAtendimento);
        if(novoProfissionalAgenda != null){
            validaConflitoHorariosDTOParam.setNovoProfissionalAgenda(novoProfissionalAgenda);
        }
        if (agenda.getTipoProcedimento().getTipoProcedimentoClassificacao().pertenceClassificacaoExame()) {
            validaConflitoHorariosDTOParam.setAgendaSemProfissional(true);
        }

        List<AgendaGrade> agendasConflitantes = AgendamentoHelper.validarConflitosHorarios(validaConflitoHorariosDTOParam);
        AgendaGrade ag = agendaGradeAtendimento.getAgendaGrade();

        if (CollectionUtils.isNotNullEmpty(agendasConflitantes)) {
            for (AgendaGrade agendaGrade1 : agendasConflitantes) {
                if (ag == null || ag.getCodigo() == null || !ag.getCodigo().equals(agendaGrade1.getAgenda().getCodigo())) {

                    if (RepositoryComponentDefault.SIM_LONG.equals(agendaGrade1.getAgenda().getTipoProcedimento().getFlagValidaHorario())) {
                        ValidacaoException validacaoException = new ValidacaoException(Bundle.getStringApplication("msg_horario_conflitante", agendaGrade1.getAgenda().getCodigo(), agendaGrade1.getAgenda().getEmpresa().getDescricaoFormatado()));
                        validacaoException.addValue("agenda", agendaGrade1.getAgenda().getCodigo().toString());
                        validacaoException.addValue("unidade", agendaGrade1.getAgenda().getEmpresa().getDescricaoFormatado());
                        throw validacaoException;
                    } else if(retornarCodigoAgendaConflito) {
                        return agendaGrade1.getAgenda().getCodigo().toString();
                    } else {
                        return Bundle.getStringApplication("msg_horario_conflitante_adicionar_sim_nao", agendaGrade1.getAgenda().getCodigo(), agendaGrade1.getAgenda().getEmpresa().getDescricaoFormatado());
                    }
                }
            }
        }
        return null;
    }

    // Validação de conflitos de horários da lista da agenda atual na edição de horários (AGENDA GRADE HORÁRIO)
    public void validarConflitosAgendaGradeHorarioNaoSalvas(List<AgendaGradeAtendimentoHorariosDTO> list, List<AgendaGradeHorario> agendaGradeHorarioListAtualizado, AgendaGradeAtendimento agendaGradeAtendimento, AgendaGradeHorario agendaGradeHorario) throws ValidacaoException {
        List<AgendaGradeHorario> listaHorariosDia = new ArrayList<>();

        for (AgendaGradeAtendimentoHorariosDTO item : list) {
            if (CollectionUtils.isNotNullEmpty(item.getAgendaGradeHorarioList()) &&
                    item.getAgendaGradeAtendimento().getAgendaGrade().getData().compareTo(agendaGradeAtendimento.getAgendaGrade().getData()) == 0) {
                listaHorariosDia.addAll(item.getAgendaGradeHorarioList());
            }
        }

        // Horários que estão sendo adicionados
        Date horaInicioAdicionar = agendaGradeHorario.getHora();
        Date horaFinalAdicionar = Data.addMinutos(agendaGradeHorario.getHora(), agendaGradeAtendimento.getTempoMedio().intValue());

        // Horários que já estão adicionados na lista
        Date horaInicioList;
        Date horaFinalList;

        for (AgendaGradeHorario agh : listaHorariosDia) {
            horaInicioList = agh.getHora();
            horaFinalList = Data.addMinutos(agh.getHora(), agh.getAgendaGradeAtendimento().getTempoMedio().intValue());

            if (horaInicioList != null && horaFinalList != null && AgendamentoHelper.isAgendasConflitantes(horaInicioList, horaFinalList, horaInicioAdicionar, horaFinalAdicionar)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_agenda_conflito_verifique_horarios"));
            }
        }
    }
    
    public void validarConflitosAgendasNaoSalvas(List<AgendaGradeAtendimentoHorariosDTO> list, AgendaGradeAtendimento agendaGradeAtendimento) throws ValidacaoException, DAOException {
        validarConflitosAgendasNaoSalvas(list, agendaGradeAtendimento, false);
    }

    // Validação de conflitos de horários da lista da agenda atual
    public void validarConflitosAgendasNaoSalvas(List<AgendaGradeAtendimentoHorariosDTO> list, AgendaGradeAtendimento agendaGradeAtendimento, boolean ignorarProprioHorario) throws ValidacaoException, DAOException {
        // Horários que estão sendo adicionados
        Date horaInicioAdicionar = null;
        Date horaFinalAdicionar = null;

        // Horários que já estão adicionados na lista
        Date horaInicioList = null;
        Date horaFinalList = null;

        // AgendaGrade do item que será adicionado
        AgendaGrade ag = agendaGradeAtendimento.getAgendaGrade();

        boolean conflitoAgenda = false;

        // Percorre a lista dos itens já adicionados, verificando conflito de horários com o que está sendo adicionado
        for (AgendaGradeAtendimentoHorariosDTO item : list) {
            conflitoAgenda = false;
            
            if(ignorarProprioHorario && item.getAgendaGradeAtendimento().getCodigo().equals(agendaGradeAtendimento.getCodigo())){
                continue;
            }
            
            // AgendaGrade do item da lista
            AgendaGrade agItem = item.getAgendaGradeAtendimento().getAgendaGrade();

            // Tipo de Agenda Diário
            if (isTipoAgendaDiario()) {
                // Verifica se a data do item que está adicionado, é igual a data do item já adicionado na lista
                if (Data.formatar(agItem.getData()).equals(Data.formatar(ag.getData()))) {
                    horaInicioAdicionar = ag.getHoraInicial();
                    horaFinalAdicionar = ag.getHoraFinal();
                    horaInicioList = agItem.getHoraInicial();
                    horaFinalList = agItem.getHoraFinal();

                    if (AgendamentoHelper.isAgendasConflitantes(horaInicioList, horaFinalList, horaInicioAdicionar, horaFinalAdicionar)) {
                        conflitoAgenda = true;
                    }
                }
            } else if (agItem.getHoraInicial() != null) {
                horaInicioAdicionar = DataUtil.mergeDataHora(ag.getData(), ag.getHoraInicial());
                horaInicioList = DataUtil.mergeDataHora(agItem.getData(), agItem.getHoraInicial());

                // Tipo de Agenda Horário ou Personalizada
                if (isTipoAgendaPersonalizada()) {
                    horaFinalAdicionar = DataUtil.mergeDataHora(ag.getData(), ag.getDescricaoHoraFinal());
                    horaFinalList = DataUtil.mergeDataHora(agItem.getData(), agItem.getHoraFinal());

                    if (AgendamentoHelper.isAgendasConflitantes(horaInicioList, horaFinalList, horaInicioAdicionar, horaFinalAdicionar)) {
                        conflitoAgenda = true;
                    }
                } else {
                    if(CollectionUtils.isNotNullEmpty(item.getAgendaGradeHorarioList())) {
                        Long duracaoAgenda = new Dinheiro(agendaGradeAtendimento.getQuantidadeAtendimento()).multiplicar(agendaGradeAtendimento.getTempoMedio()).longValue();
                        horaFinalAdicionar = Data.addMinutos(horaInicioAdicionar, duracaoAgenda.intValue());

                        for (AgendaGradeHorario itemHorario : item.getAgendaGradeHorarioList()) {
                            horaInicioList = itemHorario.getHora();
                            horaFinalList = Data.addMinutos(horaInicioList, item.getAgendaGradeAtendimento().getTempoMedio().intValue());

                            if (AgendamentoHelper.isAgendasConflitantes(horaInicioList, horaFinalList, horaInicioAdicionar, horaFinalAdicionar)) {
                                conflitoAgenda = true;
                                break;
                            }
                        }
                    }
                }
            }

            if (conflitoAgenda) {
                if (isTipoAgendaDiario() || isTipoAgendaHorario()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_conflito_horarios_propria_agenda_verifique_horarios_horario_agenda_X_horario_conflitante_X", 
                            Data.formatarDataHora(DataUtil.mergeDataHora(item.getAgendaGradeAtendimento().getAgendaGrade().getData(), horaInicioList)) + " - " + Data.formatarHora(horaFinalList),
                            Data.formatarDataHora(DataUtil.mergeDataHora(item.getAgendaGradeAtendimento().getAgendaGrade().getData(), horaInicioAdicionar)) + " - " + Data.formatarHora(horaFinalAdicionar)));
                } else {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_agenda_conflito_verifique_horarios"));
                }
            }
        }
    }

    public Date getHoraFinal(List<AgendaGradeAtendimentoHorariosDTO> list) {
        Date horaInicial = list.get(0).getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial();

        Date horaFim = null;

        boolean isEmpty = true;
        for (AgendaGradeAtendimentoHorariosDTO dto : list) {
            if (CollectionUtils.isNotNullEmpty(dto.getAgendaGradeHorarioList())) {
                isEmpty = false;
                break;
            }
        }

        if (!isTipoAgendaHorario() || isEmpty) {
            for (AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimento_ : list) {
                Long quantAtendimentos = agendaGradeAtendimento_.getAgendaGradeAtendimento().getQuantidadeAtendimento();
                Long tempoMedio = agendaGradeAtendimento_.getAgendaGradeAtendimento().getTempoMedio();

                if (horaFim == null) {
                    horaFim = horaInicial;
                }

                Long tempoTotal = quantAtendimentos * tempoMedio;

                horaFim = Data.addMinutos(horaFim, tempoTotal.intValue());
            }
        } else {
            Date maiorHora = null;
            Long tempoMedio = 0L;
            for (AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimento_ : list) {
                maiorHora = null;
                tempoMedio = agendaGradeAtendimento_.getAgendaGradeAtendimento().getTempoMedio();
                for (AgendaGradeHorario item : agendaGradeAtendimento_.getAgendaGradeHorarioList()) {
                    Calendar c = new GregorianCalendar();
                    c.setTime(item.getHora());

                    if (maiorHora == null || c.getTime().after(maiorHora)) {
                        maiorHora = item.getHora();
                    }
                }
            }

            try {
                maiorHora = Data.getDateTime(horaInicial, maiorHora);
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
            horaFim = Data.addMinutos(maiorHora, tempoMedio.intValue());
        }

        return horaFim;
    }

    public Date getHoraFinal(AgendaGradeAtendimento agendaGradeAtendimento) {
        Date horaInicial = agendaGradeAtendimento.getAgendaGrade().getHoraInicial();
        Date horaFim = null;

        Long quantAtendimentos = agendaGradeAtendimento.getQuantidadeAtendimento();
        Long tempoMedio = agendaGradeAtendimento.getTempoMedio();

        if (horaFim == null) {
            horaFim = horaInicial;
        }

        Long tempoTotal = quantAtendimentos * tempoMedio;

        horaFim = Data.addMinutos(horaFim, tempoTotal.intValue());

        return horaFim;
    }
    
    public Date getHoraFinal(Date horaInicial, Long quantAtendimentos, Long tempoMedio) {
        Date horaFim = horaInicial;

        Long tempoTotal = new Dinheiro(quantAtendimentos).multiplicar(tempoMedio).longValue();

        horaFim = Data.addMinutos(horaFim, tempoTotal.intValue());

        return horaFim;
    }

    // Validação ao remover um horário se já existe agendamento registrado
    public void validaExcluir(AgendaGradeAtendimento aga) throws ValidacaoException {

        if (aga.getCodigo() != null) {
            List<AgendaGradeAtendimentoHorario> lista = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                    .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_CODIGO), aga.getCodigo()))
                    .start().getList();

            if (!lista.isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_pode_remover_agendamento_em_aberto"));
            }
        }
    }

    // Carregar lista de horários dos atendimentos agendados
    public List<AgendaGradeAtendimentoHorariosDTO> carregaListaAtendimentos(List<AgendaGradeAtendimentoHorariosDTO> list, Date data, boolean agendasAtuais) {
        LoadManager load = LoadManager.getInstance(AgendaGradeAtendimento.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimento.class).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_DATA))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_HORA_INICIAL))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_HORA_FINAL))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_VALIDA_HORARIO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA), agenda));
        if (agendasAtuais) {
            load.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_DATA), QueryCustom.QueryCustomParameter.MAIOR_IGUAL, data));
        } else {
            load.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_DATA), QueryCustom.QueryCustomParameter.MAIOR_IGUAL, data));
            load.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_DATA), QueryCustom.QueryCustomParameter.MENOR, DataUtil.getDataAtual()));
        }

        List<AgendaGradeAtendimento> listAgendaNew = load.start().getList();
        if (CollectionUtils.isNotNullEmpty(listAgendaNew)) {
            List<AgendaGradeHorario> listHorario = LoadManager.getInstance(AgendaGradeHorario.class)
                    .addProperties(new HQLProperties(AgendaGradeHorario.class).getProperties())
                    .addProperties(new HQLProperties(AgendaGradeAtendimento.class, AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO).getProperties())
                    .addProperties(new HQLProperties(AgendaGrade.class, VOUtils.montarPath(AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO, BuilderQueryCustom.QueryParameter.IN, listAgendaNew))
                    .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeHorario.PROP_HORA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            List<Long> codigoAgendaGradeList = Lambda.extract(listAgendaNew, Lambda.on(AgendaGradeAtendimento.class).getAgendaGrade().getCodigo());

            List<AgendaGradeExame> listExame = LoadManager.getInstance(AgendaGradeExame.class)
                    .addProperties(new HQLProperties(AgendaGradeExame.class).getProperties())
                    .addProperties(new HQLProperties(ExameProcedimento.class, AgendaGradeExame.PROP_EXAME_PROCEDIMENTO).getProperties())
                    .addProperties(new HQLProperties(AgendaGrade.class, AgendaGradeExame.PROP_AGENDA_GRADE).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeExame.PROP_AGENDA_GRADE, AgendaGrade.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IN, codigoAgendaGradeList))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AgendaGradeExame.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            for (AgendaGradeAtendimento item : listAgendaNew) {
                if (gradeAtendimentoJahEstahNaLista(item, list)) continue;
                AgendaGradeAtendimentoHorariosDTO dto = new AgendaGradeAtendimentoHorariosDTO();
                dto.setAgendaGradeAtendimento(item);
                if (CollectionUtils.isNotNullEmpty(listHorario)) {
                    List<AgendaGradeHorario> listHorarioAux = Lambda.select(listHorario, Lambda.having(on(AgendaGradeHorario.class).getAgendaGradeAtendimento().getCodigo(), Matchers.equalTo(item.getCodigo())));
                    dto.setAgendaGradeHorarioList(listHorarioAux);
                }
                if (CollectionUtils.isNotNullEmpty(listExame)) {
                    List<AgendaGradeExame> listExameAux = Lambda.select(listExame, Lambda.having(on(AgendaGradeExame.class).getAgendaGrade().getCodigo(), Matchers.equalTo(item.getAgendaGrade().getCodigo())));
                    dto.setAgendaGradeExameList(listExameAux);
                }
                dto.setTotalVagasAgendadas(carregarVagasAgendadas(item));
                list.add(dto);
            }
        }
        return list;
    }

    private Long carregarVagasAgendadas(AgendaGradeAtendimento agendaGradeAtendimento) {
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addGroup(new QueryCustom
                        .QueryCustomGroup(
                        AgendaGradeAtendimentoHorario.PROP_QUANTIDADE_VAGAS_OCUPADAS,
                        BuilderQueryCustom.QueryGroup.SUM
                ))
                .addParameter(new QueryCustom
                        .QueryCustomParameter(VOUtils
                        .montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO,
                                AgendaGradeAtendimento.PROP_CODIGO),
                        BuilderQueryCustom.QueryParameter.IGUAL, agendaGradeAtendimento.getCodigo()))
                .start().getVO();
    }

    private boolean gradeAtendimentoJahEstahNaLista(AgendaGradeAtendimento item, List<AgendaGradeAtendimentoHorariosDTO> list) {
        for (AgendaGradeAtendimentoHorariosDTO dto: list) {
            if (dto.getAgendaGradeAtendimento().getCodigo().equals(item.getCodigo())) return true;
        }
        return false;
    }

    // Validações do botão Adicionar
    public boolean validarAdicionar(AgendaGradeAtendimento agendaGradeAtendimento) throws ValidacaoException {
        ValidacaoProcesso validacao = new ValidacaoProcesso();

        if (agendaGradeAtendimento.getAgendaGrade() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_dados_para_adicionar"));
        } else if (agendaGradeAtendimento.getAgendaGrade().getHoraInicial() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_hora_de_inicio_valida"));
        } else {
            if (agendaGradeAtendimento.getAgendaGrade().getData() == null) {
                validacao.add(Bundle.getStringApplication("msg_campo_X_obrigatorio", Bundle.getStringApplication("data")));
            }
            if (agendaGradeAtendimento.getQuantidadeAtendimento() == null) {
                validacao.add(Bundle.getStringApplication("msg_campo_X_obrigatorio", Bundle.getStringApplication("vagas")));
            }
            if (agendaGradeAtendimento.getTempoMedio() == null) {
                validacao.add(Bundle.getStringApplication("msg_campo_X_obrigatorio", Bundle.getStringApplication("tempo_medio")));
            }
            if (agendaGradeAtendimento.getTipoAtendimentoAgenda() == null) {
                validacao.add(Bundle.getStringApplication("msg_campo_X_obrigatorio", Bundle.getStringApplication("tipoAtendimento")));
            }
            if (isTipoAgendaPersonalizada()) {
                if (agendaGradeAtendimento.getAgendaGrade().getHoraFinal() == null) {
                    validacao.add(Bundle.getStringApplication("msg_campo_X_obrigatorio", Bundle.getStringApplication("rotulo_hora_final")));
                } else if (DataUtil.compareHour(agendaGradeAtendimento.getAgendaGrade().getHoraInicial(), agendaGradeAtendimento.getAgendaGrade().getHoraFinal()) > 0) {
                    validacao.add(Bundle.getStringApplication("msg_hora_final_deve_ser_maior_que_hora_inicio"));
                }
            }
        }
        if (!validacao.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacao);
        }

        return true;
    }

    // Validar feriados
    public String validarDataFeriado(Date data, Empresa empresa) throws ValidacaoException {
        Empresa e = LoadManager.getInstance(Empresa.class)
                .addProperties(new HQLProperties(Cidade.class, Empresa.PROP_CIDADE).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, empresa.getCodigo()))
                .start().getVO();

        try {
            List<Feriado> feriadoList = BOFactory.getBO(BasicoFacade.class).consultarFeriados(data, e.getCidade());

            if (CollectionUtils.isNotNullEmpty(feriadoList)) {
                List<String> descricaoFeriadoList = Lambda.extract(feriadoList, on(Feriado.class).getDescricao());
                
                StringBuilder sb = new StringBuilder();
                for (String descricao : descricaoFeriadoList) {
                    sb.append(descricao);
                    sb.append(", ");
                }
                
                return Bundle.getStringApplication("msg_data_X_feriado_X_deseja_confirmar_cadastro_agenda", Data.formatar(data), sb.toString().substring(0, sb.toString().length() - 2));
            }
        } catch (DAOException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }

        return null;
    }

    // Gerar objeto AgendaGradeAtendimentoHorariosDTO
    public AgendaGradeAtendimentoHorariosDTO gerarAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) throws ValidacaoException {
        AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO = new AgendaGradeAtendimentoHorariosDTO();

        Date data = agendaGradeAtendimento.getAgendaGrade().getData();
        if (Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().after(data)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_adicionar_horario_data_inferior_data_atual"));
        }

        if (isTipoAgendaHorario()) {
            Date hora = agendaGradeAtendimento.getAgendaGrade().getHoraInicial();
            Long tempoMedio = agendaGradeAtendimento.getTempoMedio();
            List<AgendaGradeHorario> list = new ArrayList<AgendaGradeHorario>();

            try {
                Date dataFormatada = Data.getDateTime(data, hora);

                for (int i = 0; i < agendaGradeAtendimento.getQuantidadeAtendimento(); i++) {
                    AgendaGradeHorario agendaGradeHorario = new AgendaGradeHorario();

                    if (i != 0) {
                        dataFormatada = Data.addMinutos(dataFormatada, tempoMedio.intValue());
                    }
                    agendaGradeHorario.setHora(dataFormatada);
                    agendaGradeHorario.setStatus(AgendaGradeHorario.Status.PENDENTE.value());
                    agendaGradeHorario.setAgendaGradeAtendimento(agendaGradeAtendimento);

                    list.add(agendaGradeHorario);
                }
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
            agendaGradeAtendimentoHorariosDTO.setAgendaGradeHorarioList(list);
        }

        agendaGradeAtendimentoHorariosDTO.setAgendaGradeAtendimento(agendaGradeAtendimento);

        return agendaGradeAtendimentoHorariosDTO;
    }

    // Cópia da agenda diária e personalizada
    public List<AgendaGradeAtendimento> copiarAgendaDiariaPersonalizada(List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList, CopiaAgendaDTO dto) throws ValidacaoException, DAOException {
        Date dataBase = dto.getDataBase();
        List<AgendaGradeAtendimento> agendasParaCopiar = new ArrayList<AgendaGradeAtendimento>();
        List<AgendaGradeAtendimento> agendasCopiadas = new ArrayList<AgendaGradeAtendimento>();
        for (AgendaGradeAtendimentoHorariosDTO item : agendaGradeAtendimentoHorariosAtuaisDTOList) {
            Date dataHoraInicio = DataUtil.mergeDataHora(item.getAgendaGradeAtendimento().getAgendaGrade().getData(), item.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraInicial());
            if (dataHoraInicio.equals(dataBase)) {
                agendasParaCopiar.add(item.getAgendaGradeAtendimento());
            }
        }

        Date dataInicial = dto.getDataInicio();
        Date dataFinal = Data.adjustRangeHour(dto.getDataTermino()).getDataFinal();
        Date dataAux = dataInicial;
        Calendar calendar = GregorianCalendar.getInstance();
        Date horaInicioAdicionar = dto.getHoraInicial();
        Date horaFinalAdicionar = dto.getHoraFinal();

        do {
            calendar.setTime(dataAux);
            if (dto.getDiasSelecionadosList().contains(String.valueOf(calendar.get(Calendar.DAY_OF_WEEK)))) {
                for (AgendaGradeAtendimentoHorariosDTO item : agendaGradeAtendimentoHorariosAtuaisDTOList) {
                    AgendaGrade ag = item.getAgendaGradeAtendimento().getAgendaGrade();

                    if (Data.formatar(ag.getData()).equals(Data.formatar(dataAux))) {
                        Date horaInicioList = ag.getHoraInicial();
                        Date horaFinalList = ag.getHoraFinal();

                        if (AgendamentoHelper.isAgendasConflitantes(horaInicioList, horaFinalList, horaInicioAdicionar, horaFinalAdicionar)) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_copiar_agenda_dia_adicionado", dataAux));
                        }
                    }
                }

                for (AgendaGradeAtendimento agendaGradeAtendimento : agendasParaCopiar) {
                    AgendaGrade novaAgendaGrade = new DefinerPropertiesCloning().define(agendaGradeAtendimento.getAgendaGrade());
                    novaAgendaGrade.setData(dataAux);

                    AgendaGradeAtendimento novaAgenda = new DefinerPropertiesCloning().define(agendaGradeAtendimento);
                    novaAgenda.setAgendaGrade(novaAgendaGrade);

                    agendasCopiadas.add(novaAgenda);
                }
            }
            if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY && dto.getSemanasRepeticao() > 1) {
                int dias = (dto.getSemanasRepeticao() * 7) - 7 + 1;
                dataAux = Data.addDias(dataAux, dias);
            } else {
                dataAux = Data.addDias(dataAux, 1);
            }
        } while (dataAux.before(dataFinal));

        return agendasCopiadas;
    }

    // Cópia da agenda horário
    public List<AgendaGradeAtendimentoHorariosDTO> copiarAgendaHorario(List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList, CopiaAgendaDTO dto) throws ValidacaoException, DAOException {
        Date dataBase = dto.getDataBase();
        List<AgendaGradeAtendimentoHorariosDTO> agendasParaCopiar = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
        List<AgendaGradeAtendimentoHorariosDTO> agendasCopiadas = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
        for (AgendaGradeAtendimentoHorariosDTO item : agendaGradeAtendimentoHorariosAtuaisDTOList) {
            Date dataHoraInicio = DataUtil.mergeDataHora(item.getAgendaGradeAtendimento().getAgendaGrade().getData(), item.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraInicial());
            if (dataHoraInicio.equals(dataBase)) {
                AgendaGradeAtendimentoHorariosDTO itemDTO = (AgendaGradeAtendimentoHorariosDTO) SerializationUtils.clone(item);
                agendasParaCopiar.add(itemDTO);
            }
        }

        Date dataInicial = dto.getDataInicio();
        Date dataFinal = Data.adjustRangeHour(dto.getDataTermino()).getDataFinal();
        Date dataAux = dataInicial;
        Calendar calendar = GregorianCalendar.getInstance();

        do {
            calendar.setTime(dataAux);
            if (dto.getDiasSelecionadosList().contains(String.valueOf(calendar.get(Calendar.DAY_OF_WEEK)))) {
                for (AgendaGradeAtendimentoHorariosDTO agahDTO : agendasParaCopiar) {
                    AgendaGrade novaAgendaGrade = new DefinerPropertiesCloning().define(agahDTO.getAgendaGradeAtendimento().getAgendaGrade());
                    novaAgendaGrade.setData(dataAux);

                    AgendaGradeAtendimento novaAgendaGradeAtendimento = new DefinerPropertiesCloning().define(agahDTO.getAgendaGradeAtendimento());
                    novaAgendaGradeAtendimento.setAgendaGrade(novaAgendaGrade);
                    novaAgendaGradeAtendimento.setQuantidadeCotaUnidade(null);

                    validarConflitosAgendasNaoSalvas(agendaGradeAtendimentoHorariosAtuaisDTOList, novaAgendaGradeAtendimento);

                    if (agahDTO.getAgendaGradeHorarioList() != null) {
                        List<AgendaGradeHorario> aghList = new DefinerPropertiesCloning().define(agahDTO.getAgendaGradeHorarioList());
                        if (CollectionUtils.isNotNullEmpty(aghList)) {
                            for (AgendaGradeHorario agendaGrade : aghList) {
                                if (agendaGrade.getStatus().equals(AgendaGradeHorario.Status.AGENDADO.value())) {
                                    agendaGrade.setStatus(AgendaGradeHorario.Status.PENDENTE.value());
                                }
                            }
                        }

                        AgendaGradeAtendimentoHorariosDTO novaAgenda = new AgendaGradeAtendimentoHorariosDTO();
                        novaAgenda.setAgendaGradeAtendimento(novaAgendaGradeAtendimento);
                        for (AgendaGradeHorario agh : aghList) {
                            try {
                                agh.setHora(Data.getDateTime(dataAux, agh.getHora()));
                            } catch (ParseException ex) {
                                Loggable.log.error(ex.getMessage(), ex);
                            }
                        }
                        novaAgenda.setAgendaGradeHorarioList(aghList);

                        if (CollectionUtils.isNotNullEmpty(agahDTO.getAgendaGradeExameList())) {
                            List<AgendaGradeExame> ageList = new DefinerPropertiesCloning().define(agahDTO.getAgendaGradeExameList());
                            novaAgenda.setAgendaGradeExameList(ageList);
                        }

                        agendasCopiadas.add(novaAgenda);
                    }

//                    String mensagemHorarioConflitante = validarConflitosAgendasSalvas(novaAgendaGradeAtendimento);
//
//                    if (mensagemHorarioConflitante != null) {
//                        throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_copiar_agenda_dia_adicionado", dataAux));
//                    }
                }
            }
            if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY && dto.getSemanasRepeticao() > 1) {
                int dias = (dto.getSemanasRepeticao() * 7) - 7 + 1;
                dataAux = Data.addDias(dataAux, dias);
            } else {
                dataAux = Data.addDias(dataAux, 1);
            }
        } while (dataAux.before(dataFinal));

        return agendasCopiadas;
    }

    private boolean isTipoAgendaDiario() {
        return tipoAgendaDiario;
    }

    private void verificarTipoAgendaDiario() {
        try {
            tipoAgendaDiario = TipoProcedimento.TipoAgenda.DIARIO.value().equals(AgendamentoHelper.getTipoAgenda(agenda));
        } catch (ValidacaoException ex) {
            Logger.getLogger(CadastroAgendaBehavior.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private boolean isTipoAgendaHorario() {
        return tipoAgendaHorario;
    }

    private void verificarTipoAgendaHorario() {
        try {
            tipoAgendaHorario = TipoProcedimento.TipoAgenda.HORARIO.value().equals(AgendamentoHelper.getTipoAgenda(agenda));
        } catch (ValidacaoException ex) {
            Logger.getLogger(CadastroAgendaBehavior.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private boolean isTipoAgendaPersonalizada() {
        return tipoAgendaPersonalizada;
    }

    private void verificarTipoAgendaPersonalizada() {
        try {
            tipoAgendaPersonalizada = TipoProcedimento.TipoAgenda.PERSONALIZADA.value().equals(AgendamentoHelper.getTipoAgenda(agenda));
        } catch (ValidacaoException ex) {
            Logger.getLogger(CadastroAgendaBehavior.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}
