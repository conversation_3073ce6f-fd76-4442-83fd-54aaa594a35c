package br.com.celk.materiais.bnafar.dto;


import java.io.Serializable;
import java.util.List;


public class ItemDto implements Serializable {

    public ItemDto() {
    }

    public ItemDto(String codigoOrigem, String numero, String tipoProduto, String lote, String dataValidade,
                   String cnpjFabricante, String nomeFabricanteInternacional, Integer quantidade, String siglaProgramaSaude, List<IumDto> iums) {
        this.codigoOrigem = codigoOrigem;
        this.numero = numero;
        this.tipoProduto = tipoProduto;
        this.lote = lote;
        this.dataValidade = dataValidade;
        this.cnpjFabricante = cnpjFabricante;
        this.nomeFabricanteInternacional = nomeFabricanteInternacional;
        this.quantidade = quantidade;
        this.siglaProgramaSaude = siglaProgramaSaude;
        this.iums = iums;
    }

    protected String codigoOrigem;
    protected String numero;
    protected String tipoProduto;
    protected String lote;
    protected String dataValidade;
    protected String cnpjFabricante;
    protected String nomeFabricanteInternacional;
    protected Integer quantidade;
    protected String siglaProgramaSaude;
    protected List<IumDto> iums;

    public String getCodigoOrigem() {
        return codigoOrigem;
    }

    public void setCodigoOrigem(String codigoOrigem) {
        this.codigoOrigem = codigoOrigem;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public String getLote() {
        return lote;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public String getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(String dataValidade) {
        this.dataValidade = dataValidade;
    }

    public String getCnpjFabricante() {
        return cnpjFabricante;
    }

    public void setCnpjFabricante(String cnpjFabricante) {
        this.cnpjFabricante = cnpjFabricante;
    }

    public String getNomeFabricanteInternacional() {
        return nomeFabricanteInternacional;
    }

    public void setNomeFabricanteInternacional(String nomeFabricanteInternacional) {
        this.nomeFabricanteInternacional = nomeFabricanteInternacional;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getSiglaProgramaSaude() {
        return siglaProgramaSaude;
    }

    public void setSiglaProgramaSaude(String siglaProgramaSaude) {
        this.siglaProgramaSaude = siglaProgramaSaude;
    }

    public List<IumDto> getIums() {
        return iums;
    }

    public void setIums(List<IumDto> iums) {
        this.iums = iums;
    }
}
