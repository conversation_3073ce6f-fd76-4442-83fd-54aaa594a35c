package br.com.celk.materiais.bnafar.dto;

import java.io.Serializable;
public class PosologiaDto implements Serializable {

    public PosologiaDto() {
    }

    public PosologiaDto(Integer dose, String unidadeDose, Integer frequencia, String periodo) {
        this.dose = dose;
        this.unidadeDose = unidadeDose;
        this.frequencia = frequencia;
        this.periodo = periodo;
    }

    private Integer dose;
    private String unidadeDose;
    private Integer frequencia;
    private String periodo;

    public Integer getDose() {
        return dose;
    }

    public void setDose(Integer dose) {
        this.dose = dose;
    }

    public String getUnidadeDose() {
        return unidadeDose;
    }

    public void setUnidadeDose(String unidadeDose) {
        this.unidadeDose = unidadeDose;
    }

    public Integer getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public String toJson() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"dose\"").append(": \"").append(dose).append("\",");
        sb.append("\"unidadeDose\"").append(":\"").append(unidadeDose).append("\",");
        sb.append("\"frequencia\"").append(":\"").append(frequencia).append("\",");
        sb.append("\"periodo\"").append(":\"").append(periodo).append("\"");
        sb.append("}");
        return sb.toString();
    }
}
