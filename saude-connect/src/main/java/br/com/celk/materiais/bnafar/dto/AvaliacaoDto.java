package br.com.celk.materiais.bnafar.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

public class AvaliacaoDto implements Serializable {

    public AvaliacaoDto() {
    }

    public AvaliacaoDto(Integer codigo,
     EstabelecimentoDispensacaoDto estabelecimentoAvaliador,
     CaracterizacaoAvaliacaoDto caracterizacao,
     UsuarioSusDto usuarioSus, List<ProcedimentoDto> procedimentos,
     List<QuantidadeAvaliadaDto> quantidadesAvaliada) {
        this.codigo = codigo;
        this.estabelecimentoAvaliador = estabelecimentoAvaliador;
        this.caracterizacao = caracterizacao;
        this.usuarioSus = usuarioSus;
        this.procedimentos = procedimentos;
        this.quantidadesAvaliada = quantidadesAvaliada;
    }

    private Integer codigo;
    private EstabelecimentoDispensacaoDto estabelecimentoAvaliador;
    private CaracterizacaoAvaliacaoDto caracterizacao;
    private UsuarioSusDto usuarioSus;
    private List<ProcedimentoDto> procedimentos = new ArrayList<ProcedimentoDto>();
    private List<QuantidadeAvaliadaDto> quantidadesAvaliada = new ArrayList<QuantidadeAvaliadaDto>();

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EstabelecimentoDispensacaoDto getEstabelecimentoAvaliador() {
        return estabelecimentoAvaliador;
    }

    public void setEstabelecimentoAvaliador(EstabelecimentoDispensacaoDto estabelecimentoAvaliador) {
        this.estabelecimentoAvaliador = estabelecimentoAvaliador;
    }

    public CaracterizacaoAvaliacaoDto getCaracterizacao() {
        return caracterizacao;
    }

    public void setCaracterizacao(CaracterizacaoAvaliacaoDto caracterizacao) {
        this.caracterizacao = caracterizacao;
    }

    public UsuarioSusDto getUsuarioSus() {
        return usuarioSus;
    }

    public void setUsuarioSus(UsuarioSusDto usuarioSus) {
        this.usuarioSus = usuarioSus;
    }

    public List<ProcedimentoDto> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<ProcedimentoDto> procedimentos) {
        this.procedimentos = procedimentos;
    }

    public List<QuantidadeAvaliadaDto> getQuantidadesAvaliada() {
        return quantidadesAvaliada;
    }

    public void setQuantidadesAvaliada(List<QuantidadeAvaliadaDto> quantidadesAvaliada) {
        this.quantidadesAvaliada = quantidadesAvaliada;
    }

    public String toJson() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"codigo\"").append(": \"").append(codigo).append("\",");
        sb.append("\"estabelecimentoAvaliador\"").append(":").append(estabelecimentoAvaliador.toJson()).append(",");
        sb.append("\"caracterizacao\"").append(":").append(caracterizacao.toJson()).append(",");
        sb.append("\"usuarioSus\"").append(":").append(usuarioSus.toJson()).append(",");
        sb.append("\"procedimentos\"").append(":[");
        StringJoiner procedimentosJoiner = new StringJoiner(",");
        for (ProcedimentoDto procedimento : procedimentos) {
            procedimentosJoiner.add(procedimento.toJson());
        }
        sb.append(procedimentosJoiner);
        sb.append("]");
        sb.append(",");
        sb.append("\"quantidadesAvaliada\"").append(":[");
        StringJoiner quantidadesAvaliadaJoiner = new StringJoiner(",");
        for (QuantidadeAvaliadaDto quantidadeAvaliadaDto : quantidadesAvaliada) {
            quantidadesAvaliadaJoiner.add(quantidadeAvaliadaDto.toJson());
        }
        sb.append(quantidadesAvaliadaJoiner);
        sb.append("]");
        sb.append("}");
        return sb.toString();
    }


}
