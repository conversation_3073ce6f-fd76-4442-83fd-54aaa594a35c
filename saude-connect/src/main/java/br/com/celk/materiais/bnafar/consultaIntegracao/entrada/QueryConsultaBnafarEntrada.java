package br.com.celk.materiais.bnafar.consultaIntegracao.entrada;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;

import java.util.List;
import java.util.Map;

public class QueryConsultaBnafarEntrada extends CommandQueryPager<QueryConsultaBnafarEntrada> {


    private final ConsultaIntegracaoBnafarEntradaDTOParam param;

    public QueryConsultaBnafarEntrada(ConsultaIntegracaoBnafarEntradaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(ConsultaIntegracaoBnafarEntradaDTO.class.getName());

        //DataEntrada
        hql.addToSelect("be.dataEntrada", "dataEntrada");

        //Numero documento
        hql.addToSelect("be.numeroDocumento", "numeroDocumento");

        //Estabelecimento
        hql.addToSelect("em.codigo", "empresa.codigo");
        hql.addToSelect("em.descricao", "empresa.descricao");

        //Produto
        hql.addToSelect("pr.codigo", "produto.codigo");
        hql.addToSelect("pr.descricao", "produto.descricao");

        //Situação
        hql.addToSelect("be.statusRegistro", "situacao");

        //bnafar entrada
        hql.addToSelect("be.codigo", "bnafarEntrada.codigo");
        hql.addToSelect("be.statusRegistro", "bnafarEntrada.statusRegistro");
        hql.addToSelect("be.dataUltimoEnvio", "bnafarEntrada.dataUltimoEnvio");

        hql.addToSelect("bee.codigo", "bnafarEntradaElo.codigo");
        hql.addToSelect("bei.codigo", "bnafarEntradaElo.bnafarEntradaIntegracao.codigo");

        hql.addToFrom("BnafarEntrada be "
                + "left outer join be.empresa em "
                + "left outer join be.produtoOrigem pr "
                + "left outer join be.BnafarEntradaElo bee "
                + "left outer join bee.bnafarEntradaIntegracao bei "
        );

        if (param.getEmpresa() != null)
            hql.addToWhereWhithAnd("em.codigo = ", param.getEmpresa().getCodigo());

        if (param.getProduto() != null)
            hql.addToWhereWhithAnd("pr.codigo = ", param.getProduto().getCodigo());

        if (param.getDataEntrada() != null) {
            if (param.getDataEntrada().getDataInicial() != null)
                hql.addToWhereWhithAnd("be.dataEntrada >= ", param.getDataEntrada().getDataInicial());

            if (param.getDataEntrada().getDataFinal() != null)
                hql.addToWhereWhithAnd("be.dataEntrada <= ", param.getDataEntrada().getDataFinal());
        }


        if (param.getNumeroDocumento() != null)
            hql.addToWhereWhithAnd("be.numeroDocumento = ", param.getNumeroDocumento());

        if (param.getStatusRegistro() != null) {
            hql.addToWhereWhithAnd("be.statusRegistro = ", param.getStatusRegistro());
        } else {
            hql.addToWhereWhithAnd("be.statusRegistro <> ", BnafarHelper.StatusRegistro.GERADO.value());
        }
        hql.addToOrder("be.dataEntrada desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
