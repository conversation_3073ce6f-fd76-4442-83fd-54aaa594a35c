package br.com.celk.report.vigilancia.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDenunciasDTOParam implements Serializable {

    private SetorVigilancia setorVigilancia;
    private Profissional profissional;
    private TipoDenuncia tipoDenuncia;
    private String denunciante;
    private String denunciado;
    private String bairro;
    private Long situacao;
    private Long protocolo;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private Ordenacao ordenacao;

    public static enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
//        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
//        SETOR(Bundle.getStringApplication("rotulo_setor")),
        DATA_REGISTRO(Bundle.getStringApplication("rotulo_data_registro"));

        private final String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum Ordenacao {

        DATA_REGISTRO(Bundle.getStringApplication("rotulo_data_registro")),
        DENUNCIADO(Bundle.getStringApplication("rotulo_denunciado"));

        private final String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public Long getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(Long protocolo) {
        this.protocolo = protocolo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_denunciante")
    public String getDenunciante() {
        return denunciante;
    }

    public void setDenunciante(String denunciante) {
        this.denunciante = denunciante;
    }

    @DescricaoParametro("rotulo_denunciado")
    public String getDenunciado() {
        return denunciado;
    }

    public void setDenunciado(String denunciado) {
        this.denunciado = denunciado;
    }

    @DescricaoParametro("rotulo_bairro")
    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        Denuncia.Status status = Denuncia.Status.valeuOf(getSituacao());
        if (status != null) {
            return status.descricao();
        }
        return null;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_tipo_denuncia")
    public TipoDenuncia getTipoDenuncia() {
        return tipoDenuncia;
    }

    public void setTipoDenuncia(TipoDenuncia tipoDenuncia) {
        this.tipoDenuncia = tipoDenuncia;
    }

    @DescricaoParametro("rotulo_setor")
    public SetorVigilancia getSetorVigilancia() {
        return setorVigilancia;
    }

    public void setSetorVigilancia(SetorVigilancia setorVigilancia) {
        this.setorVigilancia = setorVigilancia;
    }
}
