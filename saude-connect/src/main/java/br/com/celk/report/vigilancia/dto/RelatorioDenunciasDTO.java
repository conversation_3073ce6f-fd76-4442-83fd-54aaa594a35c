package br.com.celk.report.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDenunciasDTO implements Serializable {

    private Date data;
    private Denuncia denuncia;
    private RequerimentoVigilancia requerimentoVigilancia;
    private String setores;
    private String profissionais;

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Denuncia getDenuncia() {
        return denuncia;
    }

    public void setDenuncia(Denuncia denuncia) {
        this.denuncia = denuncia;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public String getSetores() {
        return setores;
    }

    public void setSetores(String setores) {
        this.setores = setores;
    }

    public String getProfissionais() {
        return profissionais;
    }

    public void setProfissionais(String profissionais) {
        this.profissionais = profissionais;
    }
}
