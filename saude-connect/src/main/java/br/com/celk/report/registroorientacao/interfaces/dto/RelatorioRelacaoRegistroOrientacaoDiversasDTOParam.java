package br.com.celk.report.registroorientacao.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoRegistroOrientacaoDiversasDTOParam implements Serializable {
    
    public enum FormaApresentacao {
        DATA_ORIENTACAO,
        PROFISSIONAL;

        @Override
        public String toString() {
            if (this.equals(PROFISSIONAL)) {
                return Bundle.getStringApplication("rotulo_profissional_orientador");
            } else if (this.equals(DATA_ORIENTACAO)) {
                return Bundle.getStringApplication("rotulo_data_orientacao");
            }
            return "";
        }

    }
    
    public enum TipoRelatorio {
        RESUMIDO,
        DETALHADO;

        @Override
        public String toString() {
            if (this.equals(RESUMIDO)) {
                return Bundle.getStringApplication("rotulo_resumido");
            } else if (this.equals(DETALHADO)) {
                return Bundle.getStringApplication("rotulo_detalhado");
            }
            return "";
        }

    }
    
    private List<Profissional> profissional;
    private List<Empresa> estabelecimento;
    private List<TabelaCbo> tabelaCbo;
    private String orientado;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private TipoRelatorio tipoRelatorio;

    @DescricaoParametro("rotulo_periodo")
    public String getOrientado() {
        return orientado;
    }

    public void setOrientado(String orientado) {
        this.orientado = orientado;
    }
    
    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_profissional_orientador")
    public List<Profissional> getProfissional() {
        return profissional;
    }

    public void setProfissional(List<Profissional> profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_cbo")
    public List<TabelaCbo> getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(List<TabelaCbo> tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public TipoRelatorio getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(TipoRelatorio tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public List<Empresa> getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(List<Empresa> estabelecimento) {
        this.estabelecimento = estabelecimento;
    }
}
