package br.com.celk.report.vigilancia.registrodiarioantivetorial.dto;

import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAtividade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueCiclo;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProducaoServicoAntivetorialDTO implements Serializable {

    private List<Long> lstCodigo;
    private DengueCiclo ciclo;
    private DengueLocalidade localidade;
    private DengueAtividade atividade;

    public List<Long> getLstCodigo() {
        return lstCodigo;
    }

    public void setLstCodigo(List<Long> lstCodigo) {
        this.lstCodigo = lstCodigo;
    }

    public DengueCiclo getCiclo() {
        return ciclo;
    }

    public void setCiclo(DengueCiclo ciclo) {
        this.ciclo = ciclo;
    }

    public DengueLocalidade getLocalidade() {
        return localidade;
    }

    public void setLocalidade(DengueLocalidade localidade) {
        this.localidade = localidade;
    }

    public DengueAtividade getAtividade() {
        return atividade;
    }

    public void setAtividade(DengueAtividade atividade) {
        this.atividade = atividade;
    }

    // ************* Resumo do Trabalho de Campo *************** \\
    // Total de Quarteirões Concluídos
    private GrupoConsolidado rtcTotalQuartConcluidos;

    // Nº Imóveis Trabalhados por Tipo
    private List<GrupoConsolidado> rtcNroTipoImoveis;

    // Nº Imóveis
    private List<GrupoConsolidado> rtcNroImoveis;

    private GrupoConsolidado rtcAmostrasColetadas;

    // Pendência
    private List<GrupoConsolidado> rtcPendencias;

    // Nº Depósitos Inspecionados por Tipo
    private List<GrupoConsolidado> rtcNroTipoDepositosInspecionados;

    // Depósito Eliminado
    private GrupoConsolidado rtcDepositoEliminado;

    // Depósitos Inspecionados por Tipo Inseticida
    private List<GrupoConsolidado> rtcDepInspTipoInsetLarvicida1;
    private List<GrupoConsolidado> rtcDepInspTipoInsetLarvicida2;
    private List<GrupoConsolidado> rtcDepInspTipoInsetAdulticida;

    // Total Dias Trabalhados na Semana
    private GrupoConsolidado rtcTotalDiasTrabalhados;

    // ********************************************************* \\
    // **************** Resumo do Laboratório ****************** \\
    private List<GrupoConsolidado> rlNroDepositosTipoEspecimeAegypti;
    private List<GrupoConsolidado> rlNroDepositosTipoEspecimeAlbopictus;

    private List<TituloGrupo> rlTituloTipoImovel;
    private List<TituloGrupo> rlTituloExemplares;
    private List<GrupoConsolidadoEspecime> rlEspecimes;

    private List<NumeroSequencia> rlNroSeqQuarteiraoAegypti;
    private List<NumeroSequencia> rlNroSeqQuarteiraoAlbopictus;
    private List<NumeroSequencia> rlNroSeqQuarteiraoAegyptiAlbopictus;

    // ********************************************************* \\
    public GrupoConsolidado getRtcTotalQuartConcluidos() {
        return rtcTotalQuartConcluidos;
    }

    public void setRtcTotalQuartConcluidos(GrupoConsolidado rtcTotalQuartConcluidos) {
        this.rtcTotalQuartConcluidos = rtcTotalQuartConcluidos;
    }

    public List<GrupoConsolidado> getRtcNroTipoImoveis() {
        return rtcNroTipoImoveis;
    }

    public void setRtcNroTipoImoveis(List<GrupoConsolidado> rtcNroTipoImoveis) {
        this.rtcNroTipoImoveis = rtcNroTipoImoveis;
    }

    public List<GrupoConsolidado> getRtcNroImoveis() {
        return rtcNroImoveis;
    }

    public void setRtcNroImoveis(List<GrupoConsolidado> rtcNroImoveis) {
        this.rtcNroImoveis = rtcNroImoveis;
    }

    public GrupoConsolidado getRtcAmostrasColetadas() {
        return rtcAmostrasColetadas;
    }

    public void setRtcAmostrasColetadas(GrupoConsolidado rtcAmostrasColetadas) {
        this.rtcAmostrasColetadas = rtcAmostrasColetadas;
    }

    public List<GrupoConsolidado> getRtcPendencias() {
        return rtcPendencias;
    }

    public void setRtcPendencias(List<GrupoConsolidado> rtcPendencias) {
        this.rtcPendencias = rtcPendencias;
    }

    public List<GrupoConsolidado> getRtcNroTipoDepositosInspecionados() {
        return rtcNroTipoDepositosInspecionados;
    }

    public void setRtcNroTipoDepositosInspecionados(List<GrupoConsolidado> rtcNroTipoDepositosInspecionados) {
        this.rtcNroTipoDepositosInspecionados = rtcNroTipoDepositosInspecionados;
    }

    public GrupoConsolidado getRtcDepositoEliminado() {
        return rtcDepositoEliminado;
    }

    public void setRtcDepositoEliminado(GrupoConsolidado rtcDepositoEliminado) {
        this.rtcDepositoEliminado = rtcDepositoEliminado;
    }

    public List<GrupoConsolidado> getRtcDepInspTipoInsetLarvicida1() {
        return rtcDepInspTipoInsetLarvicida1;
    }

    public void setRtcDepInspTipoInsetLarvicida1(List<GrupoConsolidado> rtcDepInspTipoInsetLarvicida1) {
        this.rtcDepInspTipoInsetLarvicida1 = rtcDepInspTipoInsetLarvicida1;
    }

    public List<GrupoConsolidado> getRtcDepInspTipoInsetLarvicida2() {
        return rtcDepInspTipoInsetLarvicida2;
    }

    public void setRtcDepInspTipoInsetLarvicida2(List<GrupoConsolidado> rtcDepInspTipoInsetLarvicida2) {
        this.rtcDepInspTipoInsetLarvicida2 = rtcDepInspTipoInsetLarvicida2;
    }

    public List<GrupoConsolidado> getRtcDepInspTipoInsetAdulticida() {
        return rtcDepInspTipoInsetAdulticida;
    }

    public void setRtcDepInspTipoInsetAdulticida(List<GrupoConsolidado> rtcDepInspTipoInsetAdulticida) {
        this.rtcDepInspTipoInsetAdulticida = rtcDepInspTipoInsetAdulticida;
    }

    public GrupoConsolidado getRtcTotalDiasTrabalhados() {
        return rtcTotalDiasTrabalhados;
    }

    public void setRtcTotalDiasTrabalhados(GrupoConsolidado rtcTotalDiasTrabalhados) {
        this.rtcTotalDiasTrabalhados = rtcTotalDiasTrabalhados;
    }

    public List<GrupoConsolidado> getRlNroDepositosTipoEspecimeAegypti() {
        return rlNroDepositosTipoEspecimeAegypti;
    }

    public void setRlNroDepositosTipoEspecimeAegypti(List<GrupoConsolidado> rlNroDepositosTipoEspecimeAegypti) {
        this.rlNroDepositosTipoEspecimeAegypti = rlNroDepositosTipoEspecimeAegypti;
    }

    public List<GrupoConsolidado> getRlNroDepositosTipoEspecimeAlbopictus() {
        return rlNroDepositosTipoEspecimeAlbopictus;
    }

    public void setRlNroDepositosTipoEspecimeAlbopictus(List<GrupoConsolidado> rlNroDepositosTipoEspecimeAlbopictus) {
        this.rlNroDepositosTipoEspecimeAlbopictus = rlNroDepositosTipoEspecimeAlbopictus;
    }

    public List<TituloGrupo> getRlTituloTipoImovel() {
        return rlTituloTipoImovel;
    }

    public void setRlTituloTipoImovel(List<TituloGrupo> rlTituloTipoImovel) {
        this.rlTituloTipoImovel = rlTituloTipoImovel;
    }

    public List<TituloGrupo> getRlTituloExemplares() {
        return rlTituloExemplares;
    }

    public void setRlTituloExemplares(List<TituloGrupo> rlTituloExemplares) {
        this.rlTituloExemplares = rlTituloExemplares;
    }

    public List<GrupoConsolidadoEspecime> getRlEspecimes() {
        return rlEspecimes;
    }

    public void setRlEspecimes(List<GrupoConsolidadoEspecime> rlEspecimes) {
        this.rlEspecimes = rlEspecimes;
    }

    public List<NumeroSequencia> getRlNroSeqQuarteiraoAegypti() {
        return rlNroSeqQuarteiraoAegypti;
    }

    public void setRlNroSeqQuarteiraoAegypti(List<NumeroSequencia> rlNroSeqQuarteiraoAegypti) {
        this.rlNroSeqQuarteiraoAegypti = rlNroSeqQuarteiraoAegypti;
    }

    public List<NumeroSequencia> getRlNroSeqQuarteiraoAlbopictus() {
        return rlNroSeqQuarteiraoAlbopictus;
    }

    public void setRlNroSeqQuarteiraoAlbopictus(List<NumeroSequencia> rlNroSeqQuarteiraoAlbopictus) {
        this.rlNroSeqQuarteiraoAlbopictus = rlNroSeqQuarteiraoAlbopictus;
    }

    public List<NumeroSequencia> getRlNroSeqQuarteiraoAegyptiAlbopictus() {
        return rlNroSeqQuarteiraoAegyptiAlbopictus;
    }

    public void setRlNroSeqQuarteiraoAegyptiAlbopictus(List<NumeroSequencia> rlNroSeqQuarteiraoAegyptiAlbopictus) {
        this.rlNroSeqQuarteiraoAegyptiAlbopictus = rlNroSeqQuarteiraoAegyptiAlbopictus;
    }

    public static class NumeroSequencia implements Serializable {

        private Long numero;
        private Long sequencia;

        public Long getNumero() {
            return numero;
        }

        public void setNumero(Long numero) {
            this.numero = numero;
        }

        public Long getSequencia() {
            return sequencia;
        }

        public void setSequencia(Long sequencia) {
            this.sequencia = sequencia;
        }
    }

    public static class GrupoConsolidado implements Serializable {

        private String descricao;
        private Long sum;
        private Long sumAux;

        public GrupoConsolidado() {
        }

        public GrupoConsolidado(String descricao, Long sum) {
            this.descricao = descricao;
            this.sum = sum;
        }

        public GrupoConsolidado(String descricao, Long sum, Long sumAux) {
            this.descricao = descricao;
            this.sum = sum;
            this.sumAux = sumAux;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }

        public Long getSum() {
            return sum;
        }

        public void setSum(Long sum) {
            this.sum = sum;
        }

        public Long getSumAux() {
            return sumAux;
        }

        public void setSumAux(Long sumAux) {
            this.sumAux = sumAux;
        }

        @Override
        public String toString() {
            return getDescricao();
        }
    }

    public static class GrupoConsolidadoEspecime implements Serializable {

        private String especime;
        private List<GrupoConsolidado> lstNroTipoImovel;
        private List<GrupoConsolidado> lstNroExemplares;

        public GrupoConsolidadoEspecime(String especime, List<GrupoConsolidado> lstNroTipoImovel, List<GrupoConsolidado> lstNroExemplares) {
            this.especime = especime;
            this.lstNroTipoImovel = lstNroTipoImovel;
            this.lstNroExemplares = lstNroExemplares;
        }

        public String getEspecime() {
            return especime;
        }

        public void setEspecime(String especime) {
            this.especime = especime;
        }

        public List<GrupoConsolidado> getLstNroTipoImovel() {
            return lstNroTipoImovel;
        }

        public void setLstNroTipoImovel(List<GrupoConsolidado> lstNroTipoImovel) {
            this.lstNroTipoImovel = lstNroTipoImovel;
        }

        public List<GrupoConsolidado> getLstNroExemplares() {
            return lstNroExemplares;
        }

        public void setLstNroExemplares(List<GrupoConsolidado> lstNroExemplares) {
            this.lstNroExemplares = lstNroExemplares;
        }
    }

    public static class TituloGrupo implements Serializable {

        private String titulo;

        public TituloGrupo(String titulo) {
            this.titulo = titulo;
        }

        public String getTitulo() {
            return titulo;
        }

        public void setTitulo(String titulo) {
            this.titulo = titulo;
        }

    }
}
