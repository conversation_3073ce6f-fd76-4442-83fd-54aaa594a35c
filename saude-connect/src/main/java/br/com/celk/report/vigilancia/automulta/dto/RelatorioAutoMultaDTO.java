package br.com.celk.report.vigilancia.automulta.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioAutoMultaDTO implements Serializable {

    private AutoMulta autoMulta;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private GrupoEstabelecimento grupoEstabelecimento;
    private Profissional profissional;
    private Integer tipoPrazo;

    private Long numeroInfracao;
    private Long numeroIntimacao;

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }

    public void setAutoMulta(AutoMulta autoMulta) {
        this.autoMulta = autoMulta;
    }

    public AtividadeEstabelecimento getAtividadeEstabelecimento() {
        return atividadeEstabelecimento;
    }

    public void setAtividadeEstabelecimento(AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = atividadeEstabelecimento;
    }

    public GrupoEstabelecimento getGrupoEstabelecimento() {
        return grupoEstabelecimento;
    }

    public void setGrupoEstabelecimento(GrupoEstabelecimento grupoEstabelecimento) {
        this.grupoEstabelecimento = grupoEstabelecimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public String getSituacao() {
        return AutoMulta.Situacao.valueOf(getAutoMulta().getSituacao()).descricao();
    }

    public Integer getTipoPrazo() {
        return tipoPrazo;
    }

    public void setTipoPrazo(Integer tipoPrazo) {
        this.tipoPrazo = tipoPrazo;
    }

    public String getPrazo() {
        if (getTipoPrazo() != null) {
            if (getTipoPrazo().equals(0)) {
                return RelatorioAutoMultaDTOParam.PrazoDefesa.A_VENCER.toString();
            } else if (getTipoPrazo().equals(1)) {
                return RelatorioAutoMultaDTOParam.PrazoDefesa.VENCIDO.toString();
            }
        }
        return null;
    }

    public Long getNumeroInfracao() {
        return numeroInfracao;
    }

    public void setNumeroInfracao(Long numeroInfracao) {
        this.numeroInfracao = numeroInfracao;
    }

    public Long getNumeroIntimacao() {
        return numeroIntimacao;
    }

    public void setNumeroIntimacao(Long numeroIntimacao) {
        this.numeroIntimacao = numeroIntimacao;
    }
}
