package br.com.celk.report.unidadesaude.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoPacientesTratamentoCapsDTO implements Serializable {

    private Date dataAtendimento;
    private FichaAcolhimento fichaAcolhimento;
    private UsuarioCadsus usuarioCadsus;
    private MotivoDestinoSaida motivoDestinoSaida;
    private Empresa empresa;
    private Cid cid;
    private Profissional profissional;
    private Empresa unidadeOrigem;

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public FichaAcolhimento getFichaAcolhimento() {
        return fichaAcolhimento;
    }

    public void setFichaAcolhimento(FichaAcolhimento fichaAcolhimento) {
        this.fichaAcolhimento = fichaAcolhimento;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public MotivoDestinoSaida getMotivoDestinoSaida() {
        return motivoDestinoSaida;
    }

    public void setMotivoDestinoSaida(MotivoDestinoSaida motivoDestinoSaida) {
        this.motivoDestinoSaida = motivoDestinoSaida;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    public String getDescricaoUnidadeOrigem() {
        return getUnidadeOrigem() == null ? "" : getUnidadeOrigem().getDescricao();
    }
}
