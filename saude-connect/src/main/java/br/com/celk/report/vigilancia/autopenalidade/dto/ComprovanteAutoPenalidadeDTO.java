package br.com.celk.report.vigilancia.autopenalidade.dto;

import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeFiscal;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeItem;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.TipoPenalidade;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ComprovanteAutoPenalidadeDTO extends AutoPenalidadeFiscal implements Serializable {

    private List<TipoPenalidade> lstTipoPenalidades;
    private List<InfracaoDTO> lstInfracaoDTO;
    private List<AutoPenalidadeItem> lstAutoPenalidadeItem;
    private String descricaoAtividadePrincipal;
    private List<AutoPenalidadeFiscal> autoPenalidadeFiscalList;
    private String descricaoTaxa;

    public List<TipoPenalidade> getLstTipoPenalidades() {
        return lstTipoPenalidades;
    }

    public void setLstTipoPenalidades(List<TipoPenalidade> lstTipoPenalidades) {
        this.lstTipoPenalidades = lstTipoPenalidades;
    }

    public List<InfracaoDTO> getLstInfracaoDTO() {
        return lstInfracaoDTO;
    }

    public void setLstInfracaoDTO(List<InfracaoDTO> lstInfracaoDTO) {
        this.lstInfracaoDTO = lstInfracaoDTO;
    }

    public List<AutoPenalidadeItem> getLstAutoPenalidadeItem() {
        return lstAutoPenalidadeItem;
    }

    public void setLstAutoPenalidadeItem(List<AutoPenalidadeItem> lstAutoPenalidadeItem) {
        this.lstAutoPenalidadeItem = lstAutoPenalidadeItem;
    }

    public String getDescricaoAtividadePrincipal() {
        return descricaoAtividadePrincipal;
    }

    public void setDescricaoAtividadePrincipal(String descricaoAtividadePrincipal) {
        this.descricaoAtividadePrincipal = descricaoAtividadePrincipal;
    }

    public List<AutoPenalidadeFiscal> getAutoPenalidadeFiscalList() {
        return autoPenalidadeFiscalList;
    }

    public void setAutoPenalidadeFiscalList(List<AutoPenalidadeFiscal> autoPenalidadeFiscalList) {
        this.autoPenalidadeFiscalList = autoPenalidadeFiscalList;
    }

    public String getDescricaoTaxa() {
        return descricaoTaxa;
    }

    public void setDescricaoTaxa(String descricaoTaxa) {
        this.descricaoTaxa = descricaoTaxa;
    }
}

