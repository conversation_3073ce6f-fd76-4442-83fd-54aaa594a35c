package br.com.celk.report.unidadesaude.samu.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.samu.ClassificacaoEquipeSamu;
import br.com.ksisolucoes.vo.samu.EncaminhamentoSamu;
import br.com.ksisolucoes.vo.samu.MotivoOcorrenciaSamu;
import br.com.ksisolucoes.vo.samu.MotivoOcorrenciaTipoSamu;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ResumoAtendimentosSamuDTOParam implements Serializable {
    
    public enum FormaApresentacao {

        TIPO(Bundle.getStringApplication("rotulo_tipo")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        UNIDADE(Bundle.getStringApplication("rotulo_unidade")),
        CLASSIFICACAO_EQUIPE(Bundle.getStringApplication("rotulo_classificacao_equipe")),
        MOTIVO(Bundle.getStringApplication("rotulo_motivo")),
        FAIXA_ETARIA(Bundle.getStringApplication("rotulo_faixa_etaria")),
        ENCAMINHAMENTO(Bundle.getStringApplication("rotulo_encaminhamento"));
        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    public enum Ordenacao {

        TIPO_RESUMO(Bundle.getStringApplication("rotulo_tipo_resumo")),
        NUMERO_ATENDIMENTO(Bundle.getStringApplication("rotulo_nr_atendimento"));
        private String name;

        private Ordenacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
    
    public enum TipoOrdenacao {
        CRESCENTE(Bundle.getStringApplication("rotulo_crescente"),"asc"),
        DECRESCENTE(Bundle.getStringApplication("rotulo_decrescente"),"desc");

        private String name;
        private String command;

        private TipoOrdenacao(String name, String command) {
            this.name = name;
            this.command = command;
        }

        @Override
        public String toString() {
            return name;
        }

        public String getCommand() {
            return command;
        }

    }
    
    public static enum TipoResumo implements IEnum<TipoResumo> {

        TIPO(1L, Bundle.getStringApplication("rotulo_tipo")),
        PROFISSIONAL(2L, Bundle.getStringApplication("rotulo_profissional")),
        UNIDADE(4L, Bundle.getStringApplication("rotulo_unidade")),
        MOTIVO(8L, Bundle.getStringApplication("rotulo_motivo")),
        ENCAMINHAMENTO(16L, Bundle.getStringApplication("rotulo_encaminhamento")),
        CBO(32L, Bundle.getStringApplication("rotulo_cbo")),
        ;

        private Long value;
        private String descricao;

        private TipoResumo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        public static TipoResumo valueOf(Long value) {
            for (TipoResumo tipoResumo : TipoResumo.values()) {
                if (tipoResumo.value().equals(value)) {
                    return tipoResumo;
                }
            }
            return null;
        }
    }
    
    private FormaApresentacao formaApresentacao;
    private Ordenacao ordenacao;
    private Empresa empresa;
    private Profissional profissional;
    private ClassificacaoEquipeSamu classificacaoEquipeSamu;
    private TabelaCbo tabelaCbo;
    private MotivoOcorrenciaSamu motivoOcorrenciaSamu;
    private MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamu;
    private EncaminhamentoSamu encaminhamentoSamu;
    private Cidade cidade;
    private String bairro;
    private FaixaEtaria faixaEtaria;
    private FaixaEtariaItem faixaEtariaItem;
    private DatePeriod periodo;
    private TipoOrdenacao tipoOrdenacao;
    private List<Long> tipoResumo = new ArrayList<Long>();
    
    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_unidade")
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_classificacao_equipe")
    public String getDescricaoClassificacaoEquipeSamu() {
        return classificacaoEquipeSamu != null ? classificacaoEquipeSamu.getDescricao() : "";
    }

    public ClassificacaoEquipeSamu getClassificacaoEquipeSamu() {
        return classificacaoEquipeSamu;
    }

    public void setClassificacaoEquipeSamu(ClassificacaoEquipeSamu classificacaoEquipeSamu) {
        this.classificacaoEquipeSamu = classificacaoEquipeSamu;
    }

    @DescricaoParametro("rotulo_cbo")
    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    @DescricaoParametro("rotulo_motivo")
    public MotivoOcorrenciaSamu getMotivoOcorrenciaSamu() {
        return motivoOcorrenciaSamu;
    }

    public void setMotivoOcorrenciaSamu(MotivoOcorrenciaSamu motivoOcorrenciaSamu) {
        this.motivoOcorrenciaSamu = motivoOcorrenciaSamu;
    }

    @DescricaoParametro("rotulo_tipo")
    public MotivoOcorrenciaTipoSamu getMotivoOcorrenciaTipoSamu() {
        return motivoOcorrenciaTipoSamu;
    }

    public void setMotivoOcorrenciaTipoSamu(MotivoOcorrenciaTipoSamu motivoOcorrenciaTipoSamu) {
        this.motivoOcorrenciaTipoSamu = motivoOcorrenciaTipoSamu;
    }

    @DescricaoParametro("rotulo_encaminhamento")
    public EncaminhamentoSamu getEncaminhamentoSamu() {
        return encaminhamentoSamu;
    }

    public void setEncaminhamentoSamu(EncaminhamentoSamu encaminhamentoSamu) {
        this.encaminhamentoSamu = encaminhamentoSamu;
    }

    @DescricaoParametro("rotulo_cidade")
    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    @DescricaoParametro("rotulo_bairro")
    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }
    
    @DescricaoParametro("rotulo_faixa_etaria_padrao")
    public FaixaEtaria getFaixaEtaria() {
        return faixaEtaria;
    }

    public void setFaixaEtaria(FaixaEtaria faixaEtaria) {
        this.faixaEtaria = faixaEtaria;
    }

    @DescricaoParametro("rotulo_faixa_etaria")
    public String getDescricaoFaixaEtariaItem() {
        return faixaEtariaItem != null ? faixaEtariaItem.getDescricao() : "";
    }

    public FaixaEtariaItem getFaixaEtariaItem() {
        return faixaEtariaItem;
    }

    public void setFaixaEtariaItem(FaixaEtariaItem faixaEtariaItem) {
        this.faixaEtariaItem = faixaEtariaItem;
    }
    
    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }
    
    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }
    
    public List<Long> getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(List<Long> tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public String getDescricaoTipoResumo() {
        StringBuilder descricao = new StringBuilder();

        for (Long item : getTipoResumo()) {
            TipoResumo tr = TipoResumo.valueOf(item);
            if (tr != null) {
                descricao.append(tr.descricao());
                descricao.append(", ");
            }
        }

        // Remove a última vírgula
        descricao = descricao.deleteCharAt(descricao.length() - 2);

        return descricao.toString();
    }
    
    public boolean isShowFormaApresentacao() {
        // Se o tipo do resumo tem apenas um campo marcado e é o mesmo da FA, não deve sair a FA.
        return !(getTipoResumo().size() == 1
                && ((FormaApresentacao.TIPO.equals(getFormaApresentacao()) && getTipoResumo().contains(TipoResumo.TIPO.value()))
                || (FormaApresentacao.PROFISSIONAL.equals(getFormaApresentacao()) && getTipoResumo().contains(TipoResumo.PROFISSIONAL.value()))
                || (FormaApresentacao.UNIDADE.equals(getFormaApresentacao()) && getTipoResumo().contains(TipoResumo.UNIDADE.value()))
                || (FormaApresentacao.ENCAMINHAMENTO.equals(getFormaApresentacao()) && getTipoResumo().contains(TipoResumo.ENCAMINHAMENTO.value()))
                || (FormaApresentacao.MOTIVO.equals(getFormaApresentacao()) && getTipoResumo().contains(TipoResumo.MOTIVO.value()))));
    }
    
}