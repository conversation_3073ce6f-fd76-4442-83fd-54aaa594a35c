package br.com.celk.report.recepcao.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoEtiquetaExameDTO implements Serializable {
    
    private AtendimentoExame atendimentoExame;
    private UsuarioCadsus usuarioCadsus;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private String descricaoExame;

    public AtendimentoExame getAtendimentoExame() {
        return atendimentoExame;
    }

    public void setAtendimentoExame(AtendimentoExame atendimentoExame) {
        this.atendimentoExame = atendimentoExame;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public String getDescricaoExame() {
        return descricaoExame;
    }

    public void setDescricaoExame(String descricaoExame) {
        this.descricaoExame = descricaoExame;
    }
}
