package br.com.celk.report.materiais.brasindice.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelacaoProdutoBrasindiceDTO implements Serializable {
    
    private String codigoProduto;
    private String descricaoProduto;
    private Long codigoBrasindice;
    private String descricaoBrasindice;
    private String descricaoApresentacao;
    private String descricaoLaboratorio;
    private String codigoTiss;
    private String codigoTuss;
    private Double preco;
    private Long versaoBrasindice;
    private Date dataInicioVigencia;

    public String getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Long getCodigoBrasindice() {
        return codigoBrasindice;
    }

    public void setCodigoBrasindice(Long codigoBrasindice) {
        this.codigoBrasindice = codigoBrasindice;
    }

    public String getDescricaoBrasindice() {
        return descricaoBrasindice;
    }

    public void setDescricaoBrasindice(String descricaoBrasindice) {
        this.descricaoBrasindice = descricaoBrasindice;
    }

    public String getDescricaoApresentacao() {
        return descricaoApresentacao;
    }

    public void setDescricaoApresentacao(String descricaoApresentacao) {
        this.descricaoApresentacao = descricaoApresentacao;
    }

    public String getDescricaoLaboratorio() {
        return descricaoLaboratorio;
    }

    public void setDescricaoLaboratorio(String descricaoLaboratorio) {
        this.descricaoLaboratorio = descricaoLaboratorio;
    }

    public String getCodigoTiss() {
        return codigoTiss;
    }

    public void setCodigoTiss(String codigoTiss) {
        this.codigoTiss = codigoTiss;
    }

    public String getCodigoTuss() {
        return codigoTuss;
    }

    public void setCodigoTuss(String codigoTuss) {
        this.codigoTuss = codigoTuss;
    }

    public Double getPreco() {
        return preco;
    }

    public void setPreco(Double preco) {
        this.preco = preco;
    }

    public Long getVersaoBrasindice() {
        return versaoBrasindice;
    }

    public void setVersaoBrasindice(Long versaoBrasindice) {
        this.versaoBrasindice = versaoBrasindice;
    }

    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }
    
}
