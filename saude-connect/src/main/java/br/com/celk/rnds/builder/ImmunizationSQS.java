package br.com.celk.rnds.builder;

import java.io.Serializable;

public class ImmunizationSQS implements Serializable {
    private String idRegistro;
    private String uuidRnds;
    private Long tipoIntegracao;
    private String tenant;
    private String cluster;
    private String httpMethod;
    private String payload;

    protected ImmunizationSQS() {
    }

    public String getIdRegistro() {
        return idRegistro;
    }

    public void setIdRegistro(String idRegistro) {
        this.idRegistro = idRegistro;
    }

    public String getUuidRnds() {
        return uuidRnds;
    }

    public void setUuidRnds(String uuidRnds) {
        this.uuidRnds = uuidRnds;
    }

    public Long getTipoIntegracao() {
        return tipoIntegracao;
    }

    public void setTipoIntegracao(Long tipoIntegracao) {
        this.tipoIntegracao = tipoIntegracao;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }
}
