package br.com.celk.util;

import br.com.celk.bo.service.rest.fiscalnarua.VigilanciaEnderecoFiscalNaRuaRestDTO;
import br.com.celk.report.vigilancia.autointimacao.dto.AssinaturaDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracaoEmail;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoEmail;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.*;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class FiscalNaRuaUtil {

    public static boolean habilitaAplicativoFiscalNaRua() {
        try {
            String habilitarFru = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("habilitaAplicativoFiscalNaRua");
            return StringUtils.isNotEmpty(habilitarFru) && RepositoryComponentDefault.SIM.equals(habilitarFru);
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void enviarMsg(StringBuilder mensagem, Usuario usuarioExecucao, String assunto, String mensagemConclusao) throws ValidacaoException, DAOException {
        MensagemDTO mensagemDTO = new MensagemDTO();
        mensagemDTO.setAssunto(assunto);
        mensagemDTO.setUsuarios(Collections.singletonList(usuarioExecucao));
        mensagemDTO.setMensagem(msg(mensagem.toString(), mensagemConclusao));

        BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
    }

    private static String msg(String mensagem, String msgConclusao) {
        StringBuilder sb = new StringBuilder();
        sb.append(msgConclusao);
        sb.append(System.lineSeparator());
        sb.append(System.lineSeparator());
        if (!mensagem.isEmpty()) {
            sb.append(mensagem);
        }

        return sb.toString();
    }

    public static VigilanciaEndereco coalesceVigilanciaEndereco(RegistroInspecao ri, RequerimentoVigilancia rv, Estabelecimento est, VigilanciaPessoa vp) {
        VigilanciaEndereco ve = null;
        if (ri != null && ri.getVigilanciaEndereco() != null) {
            ve = ri.getVigilanciaEndereco();
        }
        if (rv != null && rv.getVigilanciaEndereco() != null) {
            ve = rv.getVigilanciaEndereco();
        }
        if (rv != null && rv.getEstabelecimento() != null && rv.getEstabelecimento().getVigilanciaEndereco() != null) {
            ve = rv.getEstabelecimento().getVigilanciaEndereco();
        }
        if (est != null && est.getVigilanciaEndereco() != null) {
            ve = est.getVigilanciaEndereco();
        }
        if (vp != null && vp.getVigilanciaEndereco() != null) {
            ve = vp.getVigilanciaEndereco();
        }
        return ve;
    }

    public static Long coalesceTipoDenunciado(RegistroInspecao ri, RequerimentoVigilancia rv, Estabelecimento est, VigilanciaPessoa vp) {
        if (isEstabelecimento(ri, rv)) {
            return RegistroInspecao.TipoInspecionado.ESTABELECIMENTO.value();
        } else if (isPessoa(ri, rv)){
            return RegistroInspecao.TipoInspecionado.PESSOA.value();
        } else if (est != null){
            return RegistroInspecao.TipoInspecionado.ESTABELECIMENTO.value();
        } else if (vp != null){
            return RegistroInspecao.TipoInspecionado.PESSOA.value();
        }
        return null;
    }

    public static Estabelecimento coalesceEstabelecimento(RegistroInspecao ri, RequerimentoVigilancia rv,Estabelecimento est) {
        if (ri != null && rv != null) {
            return Coalesce.asObj(ri.getEstabelecimento(), rv.getEstabelecimento());

        } else {
            if (ri != null && ri.getEstabelecimento() != null) {
                return ri.getEstabelecimento();

            } else if (rv != null && rv.getEstabelecimento() != null) {
                return rv.getEstabelecimento();
            } else if (est != null){
                return est;
            }
        }

        return null;
    }

    public static VigilanciaPessoa coalesceVigilanciaPessoa(RegistroInspecao ri, RequerimentoVigilancia rv,VigilanciaPessoa vp) {
        if (ri != null && rv != null) {
            return Coalesce.asObj(ri.getVigilanciaPessoa(), rv.getVigilanciaPessoa());

        } else {
            if (ri != null && ri.getVigilanciaPessoa() != null) {
                return ri.getVigilanciaPessoa();

            } else if (rv != null && rv.getVigilanciaPessoa() != null) {
                return rv.getVigilanciaPessoa();
            } else if (vp != null){
                return vp;
            }
        }

        return null;
    }

    public static Estabelecimento coalesceEstabelecimentoInspecao(RequerimentoVigilancia rv, Long idEstabelecimento, String estabelecimentoApp) {
        if (rv != null && rv.getEstabelecimento() != null) {
            return rv.getEstabelecimento();
        } else if (idEstabelecimento != null) {
            return LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, idEstabelecimento))
                    .start().getVO();
        } else if (StringUtils.isNotEmpty(estabelecimentoApp)) {
            return LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_UUID_APP_FRU, estabelecimentoApp))
                    .start().getVO();
        }
        return null;
    }

    public static VigilanciaPessoa coalesceVigilanciaPessoaInspecao(RequerimentoVigilancia rv, Long idVigilanciaPessoa, String vigilanciaPessoaApp) {
        if (rv != null && rv.getVigilanciaPessoa() != null) {
            return rv.getVigilanciaPessoa();
        } else if (idVigilanciaPessoa != null) {
            return LoadManager.getInstance(VigilanciaPessoa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaPessoa.PROP_CODIGO, idVigilanciaPessoa))
                    .start().getVO();
        } else if (StringUtils.isNotEmpty(vigilanciaPessoaApp)) {
            return LoadManager.getInstance(VigilanciaPessoa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaPessoa.PROP_UUID_APP_FRU, vigilanciaPessoaApp))
                    .start().getVO();
        }
        return null;
    }

    public static VigilanciaEndereco coalesceVigilanciaEnderecoInspecao(RequerimentoVigilancia rv,Estabelecimento estabelecimento, VigilanciaPessoa pessoa) {
        if (rv != null && rv.getVigilanciaEndereco() != null) {
            return rv.getVigilanciaEndereco();
        } else if (estabelecimento != null && estabelecimento.getVigilanciaEndereco() != null) {
            return estabelecimento.getVigilanciaEndereco();
        } else if (pessoa != null && pessoa.getVigilanciaEndereco() != null) {
            return pessoa.getVigilanciaEndereco();
        }
        return null;
    }

    public static String coalesceNomeResponsavel(String respDto, RegistroInspecao ri, RequerimentoVigilancia rv,Estabelecimento est, VigilanciaPessoa vp) {
        if (StringUtils.isNotEmpty(respDto)) {
            return respDto;
        } else {
            if (isEstabelecimento(ri, rv)) {
                if (ri.getEstabelecimento() != null && StringUtils.isNotEmpty(ri.getEstabelecimento().getRepresentanteNome())) {
                    return ri.getEstabelecimento().getRepresentanteNome();
                } else if (rv.getEstabelecimento() != null && StringUtils.isNotEmpty(rv.getEstabelecimento().getRepresentanteNome())) {
                    return rv.getEstabelecimento().getRepresentanteNome();
                }
            } else if (isPessoa(ri, rv)) {
                if (ri.getVigilanciaPessoa() != null) {
                    return Coalesce.asString(ri.getVigilanciaPessoa().getNome(), ri.getVigilanciaPessoa().getNomeFantasia());
                } else if (rv.getVigilanciaPessoa() != null) {
                    return Coalesce.asString(rv.getVigilanciaPessoa().getNome(), rv.getVigilanciaPessoa().getNomeFantasia());
                }
            } else if (est != null){
                return est.getRepresentanteNome();
            } else if (vp != null){
                return Coalesce.asString(vp.getNome(), vp.getNomeFantasia());
            }
        }
        return null;
    }

    public static boolean isEstabelecimento(RegistroInspecao ri, RequerimentoVigilancia rv) {
        return (
                ri != null &&
                        ((RegistroInspecao.TipoPessoa.JURIDICA.value().equals(ri.getTipoPessoa())) ||
                                (RegistroInspecao.TipoInspecionado.ESTABELECIMENTO.value().equals(ri.getTipoInspecionado())))

        ) || (
                rv != null &&
                        (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(rv.getTipoPessoa()) ||
                                RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(rv.getTipoRequerente()))
        );
    }

    public static boolean isPessoa(RegistroInspecao ri, RequerimentoVigilancia rv) {
        return (
                ri != null &&
                        ((RegistroInspecao.TipoPessoa.FISICA.value().equals(ri.getTipoPessoa())) ||
                                (RegistroInspecao.TipoInspecionado.PESSOA.value().equals(ri.getTipoInspecionado())))
        ) || (
                rv != null &&
                        (RequerimentoVigilancia.TipoPessoa.FISICA.value().equals(rv.getTipoPessoa()) ||
                                RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(rv.getTipoRequerente()))
        );
    }


    public static List<AssinaturaDTO> criarArquivoAssinaturasIntimacao(AutoIntimacao ai, RequerimentoVigilanciaAnexo.TipoAssinatura tipoAssinatura) throws IOException, ValidacaoException {
        List<RequerimentoVigilanciaAnexo> rvaList = carregarAssinaturasIntimacao(ai, tipoAssinatura);
        if (rvaList != null && !rvaList.isEmpty()) {
            List<AssinaturaDTO> fileList =  new ArrayList<>();
            for (RequerimentoVigilanciaAnexo rva : rvaList) {
                File newFile = File.createTempFile(tipoAssinatura.descricao() + ai.getCodigo(), ".jpg");
                FileUtils.buscarArquivoFtp(rva.getGerenciadorArquivo().getCaminho(), newFile.getAbsolutePath());

                AssinaturaDTO assinaturaDTO = new AssinaturaDTO();
                assinaturaDTO.setFile(newFile);

                fileList.add(assinaturaDTO);
            }
            return fileList;
        }
        return null;
    }

    public static List<AssinaturaDTO> criarArquivoAssinaturasInfracao(AutoInfracao ai, RequerimentoVigilanciaAnexo.TipoAssinatura tipoAssinatura) throws IOException, ValidacaoException {
        List<RequerimentoVigilanciaAnexo> rvaList = carregarAssinaturasInfracao(ai, tipoAssinatura);
        if (rvaList != null && !rvaList.isEmpty()) {
            List<AssinaturaDTO> fileList =  new ArrayList<>();
            for (RequerimentoVigilanciaAnexo rva : rvaList) {
                File newFile = File.createTempFile(tipoAssinatura.descricao() + ai.getCodigo(), ".jpg");
                FileUtils.buscarArquivoFtp(rva.getGerenciadorArquivo().getCaminho(), newFile.getAbsolutePath());

                AssinaturaDTO assinaturaDTO = new AssinaturaDTO();
                assinaturaDTO.setFile(newFile);

                fileList.add(assinaturaDTO);
            }
            return fileList;
        }
        return null;
    }



    // CONSULTAS

    public static List<RequerimentoVigilanciaAnexo> carregarAssinaturasIntimacao(AutoIntimacao ai, RequerimentoVigilanciaAnexo.TipoAssinatura tipoAssinatura) {
        return LoadManager.getInstance(RequerimentoVigilanciaAnexo.class)
                .addProperties(new HQLProperties(RequerimentoVigilanciaAnexo.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, RequerimentoVigilanciaAnexo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVigilanciaAnexo.PROP_AUTO_INTIMACAO, AutoIntimacao.PROP_CODIGO), ai.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_TIPO_ASSINATURA_ANEXO, tipoAssinatura.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_STATUS, RequerimentoVigilanciaAnexo.Status.CADASTRADO.value()))
                .start().getList();
    }

    public static List<RequerimentoVigilanciaAnexo> carregarAssinaturasInfracao(AutoInfracao ai, RequerimentoVigilanciaAnexo.TipoAssinatura tipoAssinatura) {
        return LoadManager.getInstance(RequerimentoVigilanciaAnexo.class)
                .addProperties(new HQLProperties(RequerimentoVigilanciaAnexo.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, RequerimentoVigilanciaAnexo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVigilanciaAnexo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_CODIGO), ai.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_TIPO_ASSINATURA_ANEXO, tipoAssinatura.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_STATUS, RequerimentoVigilanciaAnexo.Status.CADASTRADO.value()))
                .start().getList();
    }

    public static List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscal(Long idRequerimento) {
        return LoadManager.getInstance(RequerimentoVigilanciaFiscal.class)
                .addProperties(new HQLProperties(RequerimentoVigilanciaFiscal.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), idRequerimento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .startLeitura()
                .getList();
    }


    public static RequerimentoVigilancia loadRequerimentoVigilanciaById(Long id) {
        if (id != null) {
            return LoadManager.getInstance(RequerimentoVigilancia.class)
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                    .addProperties(new HQLProperties(TipoSolicitacao.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_TIPO_SOLICITACAO)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }

    public static EstabelecimentoAtividade getEstabelecimentoAtividadeByEstabelecimento(Estabelecimento estabelecimento) {
        return LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .start().getVO();
    }

    public static RequerimentoVigilancia loadRequerimentoVigilanciaFru(Long codigo) {
        RequerimentoVigilancia rv =
                LoadManager.getInstance(RequerimentoVigilancia.class)
                        .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                        .addProperties(new HQLProperties(TipoSolicitacao.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_TIPO_SOLICITACAO)).getProperties())
                        .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                        .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RAZAO_SOCIAL))
                        .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_FANTASIA))
                        .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CNPJ_CPF))
                        .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                        .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO_SETORES, EstabelecimentoSetores.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_CODIGO, codigo))
                        .setMaxResults(1)
                        .start().getVO();

        if (rv != null && rv.getEstabelecimento() != null) {
            EstabelecimentoAtividade estabelecimentoAtividade = getEstabelecimentoAtividadeByEstabelecimento(rv.getEstabelecimento());
            rv.getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
        }

        return rv;
    }

    public static List<RequerimentoVigilancia> listRequerimentoVigilancia(TipoSolicitacao tipoSolicitacao) {
        List<RequerimentoVigilancia> list = LoadManager.getInstance(RequerimentoVigilancia.class)
                .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO_SETORES, EstabelecimentoSetores.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_BAIRRO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CEP))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_NUMERO_LOGRADOURO))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_TIPO_SOLICITACAO, tipoSolicitacao))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_SITUACAO, RequerimentoVigilancia.Situacao.ANALISE.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_ESTABELECIMENTO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))//Deve enviar apenas requerimento com estabelecimento vinculado.
                .startLeitura().getList();

        for (RequerimentoVigilancia rv : list) {
            EstabelecimentoAtividade estabelecimentoAtividade = getEstabelecimentoAtividadeByEstabelecimento(rv.getEstabelecimento());
            rv.getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
            rv.setEnviadoAppFru(RepositoryComponentDefault.SIM_LONG);
        }

        return list;
    }

    public static List<RequerimentoVigilancia> listRequerimentoVigilanciaComFiscalBy(TipoSolicitacao tipoSolicitacao) {
        List<RequerimentoVigilancia> list = LoadManager.getInstance(RequerimentoVigilancia.class)
                .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO_SETORES, EstabelecimentoSetores.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_BAIRRO))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CEP))
                .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_NUMERO_LOGRADOURO))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_TIPO_SOLICITACAO, tipoSolicitacao))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_SITUACAO, RequerimentoVigilancia.Situacao.ANALISE.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_ESTABELECIMENTO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL)) //Deve enviar apenas requerimento com estabelecimento vinculado.
                .addInterceptor((LoadInterceptor) (hql, alias) -> {
                    HQLHelper exists = hql.getNewInstanceSubQuery();
                    exists.addToSelect("1");
                    exists.addToFrom("RequerimentoVigilanciaFiscal rvf");
                    exists.addToWhereWhithAnd("rvf.requerimentoVigilancia.codigo = " + alias + ".codigo");

                    hql.addToWhereWhithAnd("EXISTS(" + exists.getQuery() + ")");
                })
                .startLeitura()
                .getList();

        for (RequerimentoVigilancia rv : list) {
            EstabelecimentoAtividade estabelecimentoAtividade = getEstabelecimentoAtividadeByEstabelecimento(rv.getEstabelecimento());
            rv.getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
            rv.setEnviadoAppFru(RepositoryComponentDefault.SIM_LONG);
        }

        return list;
    }

    public static RegistroInspecaoRoteiroItemPerguntaResposta loadRegistroInspecaoRoteiroItemPerguntaResposta(Long id) {
        if (id != null) {
            return LoadManager.getInstance(RegistroInspecaoRoteiroItemPerguntaResposta.class)
                    .addProperties(new HQLProperties(RegistroInspecaoRoteiroItemPerguntaResposta.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoRoteiroItemPerguntaResposta.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }

    public static RegistroInspecaoRoteiro loadRegistroInspecaoRoteiro(RegistroInspecao registroInspecao, RoteiroInspecao roteiroInspecao) {
        if (registroInspecao != null && roteiroInspecao != null) {
            return LoadManager.getInstance(RegistroInspecaoRoteiro.class)
                    .addProperties(new HQLProperties(RegistroInspecaoRoteiro.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoRoteiro.PROP_REGISTRO_INSPECAO, registroInspecao))
                    .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoRoteiro.PROP_ROTEIRO_INSPECAO, roteiroInspecao))
                    .start().getVO();
        }
        return null;
    }

    public static RegistroInspecaoRoteiroItem loadRegistroInspecaoRoteiroItem(Long idItemInspecaoCopia, Long idInspecaoRoteiro) {
        if (idItemInspecaoCopia != null) {
            return LoadManager.getInstance(RegistroInspecaoRoteiroItem.class)
                    .addProperties(new HQLProperties(RegistroInspecaoRoteiroItem.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroInspecaoRoteiroItem.PROP_REGISTRO_INSPECAO_ROTEIRO, RegistroInspecaoRoteiro.PROP_CODIGO), idInspecaoRoteiro))
                    .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoRoteiroItem.PROP_ID_ITEM_INSPECAO_COPIA, idItemInspecaoCopia))
                    .start().getVO();
        }
        return null;
    }

    public static RegistroInspecaoRoteiroItemPerguntaResposta loadRegistroInspecaoRoteiroItemPerguntaResposta(Long idRegistroItem, Long idPerguntaOrigem) {
        return LoadManager.getInstance(RegistroInspecaoRoteiroItemPerguntaResposta.class)
                .addProperties(new HQLProperties(RegistroInspecaoRoteiroItemPerguntaResposta.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroInspecaoRoteiroItemPerguntaResposta.PROP_REGISTRO_INSPECAO_ROTEIRO_ITEM, RegistroInspecaoRoteiroItem.PROP_CODIGO), idRegistroItem))
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoRoteiroItemPerguntaResposta.PROP_ID_ITEM_INSPECAO_PERGUNTA_COPIA, idPerguntaOrigem))
                .start()
                .getVO();
    }

    public static RoteiroInspecao loadRoteiroInspecaoById(Long id) {
        if (id != null) {
            return LoadManager.getInstance(RoteiroInspecao.class)
                    .addProperties(new HQLProperties(RoteiroInspecao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RoteiroInspecao.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }

    public static RegistroInspecao loadRegistroInspecaoByRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            return LoadManager.getInstance(RegistroInspecao.class)
                    .addProperties(new HQLProperties(RegistroInspecao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .setMaxResults(1)
                    .start().getVO();
        }
        return null;
    }

    public static Usuario loadUsuario(Long id) {
        if (id != null) {
            return LoadManager.getInstance(Usuario.class)
                    .addProperties(new HQLProperties(Usuario.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }

    public static MotivoVisita loadMotivoInspecao(Long id) {
        if (id != null) {
            return LoadManager.getInstance(MotivoVisita.class)
                    .addProperties(new HQLProperties(MotivoVisita.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(MotivoVisita.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }


    public static Profissional loadProfissional(Long id) {
        if (id != null) {
            return LoadManager.getInstance(Profissional.class)
                    .addProperties(new HQLProperties(Profissional.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .start().getVO();
        }
        return null;
    }

    public static List<Profissional> listProfissionaisFRU() {
        return LoadManager.getInstance(Profissional.class)
                .addProperty(Profissional.PROP_CODIGO)
                .addProperty(Profissional.PROP_NOME)
                .addProperty(Profissional.PROP_REFERENCIA)
                .addProperty(Profissional.PROP_NUMERO_REGISTRO)
                .startLeitura().getList();
    }

    public static List<AtividadeEstabelecimento> listAtividadeEstabelecimentosFRU() {
        return LoadManager.getInstance(AtividadeEstabelecimento.class)
                .addProperty(AtividadeEstabelecimento.PROP_CODIGO)
                .addProperty(AtividadeEstabelecimento.PROP_DESCRICAO)
                .addProperty(AtividadeEstabelecimento.PROP_EXIGE_RESPONSAVEL_TECNICO)
                .startLeitura().getList();
    }

    public static List<MotivoVisita> listMotivoVisitaFRU() {
        return LoadManager.getInstance(MotivoVisita.class)
                .addProperty(MotivoVisita.PROP_CODIGO)
                .addProperty(MotivoVisita.PROP_DESCRICAO)
                .startLeitura().getList();
    }

    public static List<ItemInspecao> listItemInspecaoFRU() {
        return LoadManager.getInstance(ItemInspecao.class)
                .addProperty(ItemInspecao.PROP_CODIGO)
                .addProperty(ItemInspecao.PROP_SUBTITULO)
                .addProperty(ItemInspecao.PROP_ENQUADRAMENTO_LEGAL)
                .startLeitura().getList();
    }

    public static List<RoteiroInspecao> listRoteiroInspecaoFRU() {
        return LoadManager.getInstance(RoteiroInspecao.class)
                .addProperty(RoteiroInspecao.PROP_CODIGO)
                .addProperty(RoteiroInspecao.PROP_NOME_ROTEIRO)
                .addProperty(RoteiroInspecao.PROP_ENQUADRAMENTO_LEGAL)
                .addProperty(RoteiroInspecao.PROP_DATA_CADASTRO)
                .addProperty(RoteiroInspecao.PROP_USUARIO)
                .startLeitura().getList();
    }

    public static List<ItemInspecaoPergunta> listItemInspecaoPerguntaFRU() {
        return LoadManager.getInstance(ItemInspecaoPergunta.class)
                .addProperty(ItemInspecaoPergunta.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(ItemInspecaoPergunta.PROP_ITEM_INSPECAO, ItemInspecao.PROP_CODIGO))
                .addProperty(ItemInspecaoPergunta.PROP_PERGUNTA)
                .addProperty(ItemInspecaoPergunta.PROP_LEI_ARTIGO)
                .addProperty(ItemInspecaoPergunta.PROP_PROVIDENCIA)
                .addProperty(ItemInspecaoPergunta.PROP_CLASSIFICACAO)
                .startLeitura().getList();
    }

    public static List<Estabelecimento> listEstabelecimentoFRU(){
        return LoadManager.getInstance(Estabelecimento.class)
            .addProperty(Estabelecimento.PROP_CODIGO)
            .addProperty(Estabelecimento.PROP_RAZAO_SOCIAL)
            .addProperty(Estabelecimento.PROP_FANTASIA)
            .addProperty(Estabelecimento.PROP_EMAIL)
            .addProperty(Estabelecimento.PROP_CNPJ_CPF)
            .addProperty(Estabelecimento.PROP_TIPO_PESSOA)
            .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
            .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_CODIGO))
            .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_DESCRICAO))
            .addProperty(Estabelecimento.PROP_TIPO_PESSOA)
            .addProperty(VOUtils.montarPath(Estabelecimento.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_CODIGO))
            .addProperty(VOUtils.montarPath(Estabelecimento.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_DESCRICAO))
            .addProperty(Estabelecimento.PROP_COMPLEMENTO)
            .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_SITUACAO, Estabelecimento.Situacao.ATIVO.value()))
            .startLeitura().getList();
    }

    public static List<String> listCnaeEstabelecimentoFRU(Estabelecimento estabelecimento) {
        List<EstabelecimentoCnae> estabelecimentoCnaes = LoadManager.getInstance(EstabelecimentoCnae.class)
                .addProperty(VOUtils.montarPath(EstabelecimentoCnae.PROP_CNAE, TabelaCnae.PROP_CNAE))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoCnae.PROP_ESTABELECIMENTO, BuilderQueryCustom.QueryParameter.IN, estabelecimento))
                .startLeitura().getList();

        return estabelecimentoCnaes
                .stream()
                .map(estabelecimentoCnae -> estabelecimentoCnae.getCnae().getCnae())
                .collect(Collectors.toList());
    }

    public static List<VigilanciaPessoa> listVigilanciaPessoaFRU(){
        return LoadManager.getInstance(VigilanciaPessoa.class)
                .addProperty(VigilanciaPessoa.PROP_CODIGO)
                .addProperty(VigilanciaPessoa.PROP_NOME)
                .addProperty(VigilanciaPessoa.PROP_CPF)
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_LOGRADOURO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_NUMERO_LOGRADOURO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_BAIRRO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CEP))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_KEYWORD))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_DESCRICAO))
                .addProperty(VigilanciaPessoa.PROP_NUMERO_LOGRADOURO)
                .addProperty(VigilanciaPessoa.PROP_EMAIL)
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VigilanciaPessoa.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_DESCRICAO))
                .addProperty(VigilanciaPessoa.PROP_NOME_FANTASIA)
                .startLeitura().getList();
    }

    public static List<VigilanciaEndereco> listVigilanciaEnderecoFRU(){
        return LoadManager.getInstance(VigilanciaEndereco.class)
                .addProperty(VigilanciaEndereco.PROP_CODIGO)
                .addProperty(VigilanciaEndereco.PROP_LOGRADOURO)
                .addProperty(VigilanciaEndereco.PROP_BAIRRO)
                .addProperty(VigilanciaEndereco.PROP_CEP)
                .addProperty(VigilanciaEndereco.PROP_NUMERO_LOGRADOURO)
                .addProperty(VigilanciaEndereco.PROP_KEYWORD)
                .addProperty(VOUtils.montarPath(VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(VigilanciaEndereco.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_DESCRICAO))
                .startLeitura().getList();
    }

    public static List<EloRoteiroAtividadeEstabelecimento> listEloRoteiroTipoAtividade() {
        return LoadManager.getInstance(EloRoteiroAtividadeEstabelecimento.class)
                .addProperty(VOUtils.montarPath(EloRoteiroAtividadeEstabelecimento.PROP_ROTEIRO_INSPECAO, RoteiroInspecao.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EloRoteiroAtividadeEstabelecimento.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_CODIGO))
                .startLeitura().getList();
    }

    public static List<RoteiroItemInspecao> listEloRoteiroItemRoteiroFRU() {
        return LoadManager.getInstance(RoteiroItemInspecao.class)
                .addProperty(VOUtils.montarPath(RoteiroItemInspecao.PROP_ROTEIRO_INSPECAO, RoteiroInspecao.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(RoteiroItemInspecao.PROP_ITEM_INSPECAO, ItemInspecao.PROP_CODIGO))
                .addProperty(RoteiroItemInspecao.PROP_ORDEM)
                .startLeitura().getList();
    }

    public static List<OrgaoEmissor> listConselhoClasseProfissionalFRU() {
        return LoadManager.getInstance(OrgaoEmissor.class)
                .addProperty(OrgaoEmissor.PROP_CODIGO)
                .addProperty(OrgaoEmissor.PROP_DESCRICAO)
                .addProperty(OrgaoEmissor.PROP_SIGLA)
                .startLeitura().getList();
    }

    public static VigilanciaPessoa loadVigilanciaPessoaById(Long id){
        if(id != null){
            return LoadManager.getInstance(VigilanciaPessoa.class)
                    .addProperties(new HQLProperties(VigilanciaPessoa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaPessoa.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, id))
                    .setMaxResults(1)
                    .start().getVO();
        }
        return null;
    }

    public static Estabelecimento loadEstabelecimentoById(Long codigo) {
        if(codigo != null) {
            return LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, codigo))
                    .setMaxResults(1)
                    .start().getVO();
        }
        return null;
    }

    public  static  Estabelecimento loadEstabelecimentoByUUID(String uuid){
        if(uuid != null){
            return LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_UUID_APP_FRU, uuid))
                    .setMaxResults(1)
                    .start().getVO();
        }
        return null;
    }

    public  static  VigilanciaPessoa loadVigilanciaPessoaByUUID(String uuid){
        if(uuid != null){
            return LoadManager.getInstance(VigilanciaPessoa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaPessoa.PROP_UUID_APP_FRU, uuid))
                    .setMaxResults(1)
                    .start().getVO();
        }
        return null;
    }

    public static AutoIntimacao loadAutoIntimacaoById(Long codigo) {
        return LoadManager.getInstance(AutoIntimacao.class)
                .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, codigo))
                .setMaxResults(1)
                .start().getVO();
    }

    public static AutoInfracao loadAutoInfracaoById(Long codigo) {
        return LoadManager.getInstance(AutoInfracao.class)
                .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, codigo))
                .setMaxResults(1)
                .start().getVO();
    }

    public static AutoIntimacao loadAutoIntimacaoByRequerimentoVigilancia(Long idRequerimento) {
        return LoadManager.getInstance(AutoIntimacao.class)
                .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoIntimacao.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), idRequerimento))
                .setMaxResults(1)
                .start().getVO();
    }

    public static AutoInfracao loadAutoInfracaoByRequerimentoVigilancia(Long idRequerimento) {
        return LoadManager.getInstance(AutoInfracao.class)
                .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoInfracao.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), idRequerimento))
                .setMaxResults(1)
                .start().getVO();
    }

    public static ResponsavelTecnico loadResponsavelTecnico(String cpf) {
        return LoadManager.getInstance(ResponsavelTecnico.class)
                .addProperties(new HQLProperties(ResponsavelTecnico.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ResponsavelTecnico.PROP_CPF, cpf))
                .setMaxResults(1)
                .start().getVO();
    }

    public static AtividadeEstabelecimento loadAtividadeEstabelecimento(Long id) {
        return LoadManager.getInstance(AtividadeEstabelecimento.class)
                .addProperties(new HQLProperties(AtividadeEstabelecimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AtividadeEstabelecimento.PROP_CODIGO, id))
                .setMaxResults(1)
                .start().getVO();
    }

    public static OrgaoEmissor loadOrgaoEmissor(Long id) {
        return LoadManager.getInstance(OrgaoEmissor.class)
                .addProperties(new HQLProperties(OrgaoEmissor.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(OrgaoEmissor.PROP_CODIGO, id))
                .setMaxResults(1)
                .start().getVO();
    }

    public static TabelaCbo loadCbo(String id) {
        return LoadManager.getInstance(TabelaCbo.class)
                .addProperties(new HQLProperties(TabelaCbo.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TabelaCbo.PROP_CBO,id))
                .setMaxResults(1)
                .start().getVO();
    }

    public static List<AutoIntimacao> listAutoIntimacaoEnviarEmail() {
        return LoadManager.getInstance(AutoIntimacao.class)
                .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_FLAG_CRIADO_PELO_APP_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_FLAG_EMAIL_ENVIADO_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.NAO_LONG))
                .startLeitura().getList();
    }

    public static List<AutoInfracao> listAutoInfracaoEnviarEmail() {
        return LoadManager.getInstance(AutoInfracao.class)
                .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_FLAG_CRIADO_PELO_APP_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_FLAG_EMAIL_ENVIADO_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.NAO_LONG))
                .startLeitura().getList();
    }

    public static List<AutoIntimacao> listAutoIntimacaoSubEnviarEmail() {
        return LoadManager.getInstance(AutoIntimacao.class)
                .addProperties(new HQLProperties(AutoIntimacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_FLAG_CRIADO_PELO_APP_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_TIPO, AutoIntimacao.Tipo.SUBSISTENTE.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_FLAG_EMAIL_ENVIADO_SUB_FRU, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.NAO_LONG))
                .startLeitura().getList();
    }

    public static List<AutoIntimacaoEmail> listAutoIntimacaoEmail(Long codAutoIntimacao) {
        return LoadManager.getInstance(AutoIntimacaoEmail.class)
                .addProperties(new HQLProperties(AutoIntimacaoEmail.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoIntimacaoEmail.PROP_AUTO_INTIMACAO, AutoIntimacao.PROP_CODIGO), codAutoIntimacao))
                .startLeitura().getList();
    }

    public static List<AutoInfracaoEmail> listAutoInfracaoEmail(Long codAutoInfracao) {
        return LoadManager.getInstance(AutoInfracaoEmail.class)
                .addProperties(new HQLProperties(AutoInfracaoEmail.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoInfracaoEmail.PROP_AUTO_INFRACAO, AutoInfracao.PROP_CODIGO), codAutoInfracao))
                .startLeitura().getList();
    }

    public static List<ResponsavelTecnico> listResponsavelTecnicoFRU(){
        return LoadManager.getInstance(ResponsavelTecnico.class)
                .addProperty(ResponsavelTecnico.PROP_CODIGO)
                .addProperty(ResponsavelTecnico.PROP_NOME)
                .addProperty(ResponsavelTecnico.PROP_NUMERO_REGISTRO)
                .addProperty(ResponsavelTecnico.PROP_CPF)
                .startLeitura().getList();
    }

    public static List<TabelaCbo> listCboFRU(){
        return LoadManager.getInstance(TabelaCbo.class)
                .addProperty(TabelaCbo.PROP_CBO)
                .addProperty(TabelaCbo.PROP_DESCRICAO)
                .startLeitura().getList();
    }


    public static VigilanciaEndereco enderecoEstabelecimentoCreateOrUpdate(VigilanciaEnderecoFiscalNaRuaRestDTO dto) throws ValidacaoException, DAOException {
        VigilanciaEndereco endereco = null;
        if (StringUtils.isNotEmpty(dto.getCep())) {
            endereco =  VigilanciaHelper.carregarVigilanciaEnderecoByCEP(dto.getCep());
        }

        if (endereco == null) {
            endereco = new VigilanciaEndereco();
        } else {
            return endereco;
        }

        if (StringUtils.isNotEmpty(dto.getCep())) {
            CepWSDTO cepWSDTO = BOFactory.getBO(BasicoFacade.class).consultaCepWS(dto.getCep());

            if (cepWSDTO != null) {
                endereco.setBairro(Coalesce.asString(cepWSDTO.getBairro(), dto.getBairro()));
                endereco.setCep(Coalesce.asString(cepWSDTO.getCep(), dto.getCep()));
                endereco.setLogradouro(Coalesce.asString(cepWSDTO.getLogradouro(), dto.getLogradouro()) + dto.getComplementoEndereco());
                endereco.setNumeroLogradouro(dto.getNumeroLogradouro().toString());
                endereco.setCidade(cepWSDTO.getCidade());
                endereco.setTipoLogradouro(cepWSDTO.getTipoLogradouro());
                endereco.setDataCadastro(DataUtil.getDataAtual());
            } else {
                throw new ValidacaoException("CEP inválido");
            }

        } else {
            endereco.setBairro(dto.getBairro());
            endereco.setLogradouro(dto.getBairro()  + dto.getComplementoEndereco());
            endereco.setNumeroLogradouro(dto.getNumeroLogradouro().toString());
            endereco.setDataCadastro(DataUtil.getDataAtual());
        }
        return BOFactory.save(endereco);
    }

    public static  RegistroInspecao loadInspecaoByIdApp(String UUID){
        return LoadManager.getInstance(RegistroInspecao.class)
                .addProperties(new HQLProperties(RegistroInspecao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecao.PROP_UUID_APP_FRU, UUID))
                .setMaxResults(1)
                .start().getVO();
    }

    public static  AutoInfracao loadIAutoInfracaoByIdApp(String UUID){
        return LoadManager.getInstance(AutoInfracao.class)
                .addProperties(new HQLProperties(AutoInfracao.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(AutoInfracao.PROP_ESTABELECIMENTO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_UUID_APP_FRU, UUID))
                .setMaxResults(1)
                .start().getVO();
    }

    public static String convertUUIDToString(UUID uuid) {
        if (uuid == null) {
            return null;
        }
        return uuid.toString();
    }
}
