package br.com.celk.esus;

import br.com.ksisolucoes.util.DatePeriod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExportacaoEsusDTOParam implements Serializable {

    private Long codigoProcesso;
    private DatePeriod periodo;
    private Date dataLimite;
    private Long todos;
    private boolean individual;
    private boolean domiciliar;
    private boolean visita;
    private boolean atendimentos;
    private boolean atividades;
    private boolean atendimentosOdontologicos;
    private boolean atendimentosDomiciliar;
    private boolean procedimentos;
    private boolean marcadoresConsumoAlimentar;
    private boolean avaliacaoElegebilidadeAdmissao;
    private boolean utilizarIntegracaoDetalhada;
    private boolean sindromeNeurologicaZikaMicrocefalia;
    private boolean vacina;

    public enum TipoEsus {

        INDIVIDUAL,
        DOMICILIO,
        VISITA,
        ATENDIMENTOS,
        ATIVIDADES,
        ATENDIMENTOS_ODONTOLOGICOS,
        ATENDIMENTOS_DOMICILIAR,
        PROCEDIMENTOS,
        MARCADORES_CONSUMO_ALIMENTAR,
        AVALIACAO_ELEGEBILIDADE_ADMISSAO,
        SINDROME_NEUROLOGICA_ZIKA_MICROCEFALIA,
        VACINA
        ;
    }

    public List<TipoEsus> getTipoEsusList() {
        List<TipoEsus> l = new ArrayList();
        if (individual) {
            l.add(TipoEsus.INDIVIDUAL);
        }
        if (domiciliar) {
            l.add(TipoEsus.DOMICILIO);
        }
        if (visita) {
            l.add(TipoEsus.VISITA);
        }
        if (atendimentos) {
            l.add(TipoEsus.ATENDIMENTOS);
        }
        if (atividades) {
            l.add(TipoEsus.ATIVIDADES);
        }
        if (atendimentosOdontologicos) {
            l.add(TipoEsus.ATENDIMENTOS_ODONTOLOGICOS);
        }
        if (procedimentos) {
            l.add(TipoEsus.PROCEDIMENTOS);
        }
        if (atendimentosDomiciliar) {
            l.add(TipoEsus.ATENDIMENTOS_DOMICILIAR);
        }
        if (marcadoresConsumoAlimentar) {
            l.add(TipoEsus.MARCADORES_CONSUMO_ALIMENTAR);
        }
        if (avaliacaoElegebilidadeAdmissao) {
            l.add(TipoEsus.AVALIACAO_ELEGEBILIDADE_ADMISSAO);
        }
        if (sindromeNeurologicaZikaMicrocefalia) {
            l.add(TipoEsus.SINDROME_NEUROLOGICA_ZIKA_MICROCEFALIA);
        }
        if (vacina) {
            l.add(TipoEsus.VACINA);
        }
        return l;
    }

    public Long getCodigoProcesso() {
        return codigoProcesso;
    }

    public void setCodigoProcesso(Long codigoProcesso) {
        this.codigoProcesso = codigoProcesso;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public boolean isIndividual() {
        return individual;
    }

    public void setIndividual(boolean individual) {
        this.individual = individual;
    }

    public boolean isDomiciliar() {
        return domiciliar;
    }

    public void setDomiciliar(boolean domiciliar) {
        this.domiciliar = domiciliar;
    }

    public boolean isVisita() {
        return visita;
    }

    public void setVisita(boolean visita) {
        this.visita = visita;
    }

    public boolean isAtividades() {
        return atividades;
    }

    public void setAtividades(boolean atividades) {
        this.atividades = atividades;
    }

    public boolean isAtendimentosOdontologicos() {
        return atendimentosOdontologicos;
    }

    public void setAtendimentosOdontologicos(boolean atendimentosOdontologicos) {
        this.atendimentosOdontologicos = atendimentosOdontologicos;
    }

    public boolean isProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(boolean procedimentos) {
        this.procedimentos = procedimentos;
    }

    public boolean isAtendimentosDomiciliar() {
        return atendimentosDomiciliar;
    }

    public void setAtendimentosDomiciliar(boolean atendimentosDomiciliar) {
        this.atendimentosDomiciliar = atendimentosDomiciliar;
    }

    public boolean isMarcadoresConsumoAlimentar() {
        return marcadoresConsumoAlimentar;
    }

    public void setMarcadoresConsumoAlimentar(boolean marcadoresConsumoAlimentar) {
        this.marcadoresConsumoAlimentar = marcadoresConsumoAlimentar;
    }

    public boolean isAvaliacaoElegebilidadeAdmissao() {
        return avaliacaoElegebilidadeAdmissao;
    }

    public void setAvaliacaoElegebilidadeAdmissao(boolean avaliacaoElegebilidadeAdmissao) {
        this.avaliacaoElegebilidadeAdmissao = avaliacaoElegebilidadeAdmissao;
    }

    public boolean isSindromeNeurologicaZikaMicrocefalia() {
        return sindromeNeurologicaZikaMicrocefalia;
    }

    public void setSindromeNeurologicaZikaMicrocefalia(boolean sindromeNeurologicaZikaMicrocefalia) {
        this.sindromeNeurologicaZikaMicrocefalia = sindromeNeurologicaZikaMicrocefalia;
    }

    public boolean isVacina() {
        return vacina;
    }

    public void setVacina(boolean vacina) {
        this.vacina = vacina;
    }

    public Date getDataLimite() {
        return dataLimite;
    }

    public void setDataLimite(Date dataLimite) {
        this.dataLimite = dataLimite;
    }

    public boolean isUtilizarIntegracaoDetalhada() {
        return utilizarIntegracaoDetalhada;
    }

    public void setUtilizarIntegracaoDetalhada(boolean utilizarIntegracaoDetalhada) {
        this.utilizarIntegracaoDetalhada = utilizarIntegracaoDetalhada;
    }

    public Long getTodos() {
        return todos;
    }

    public void setTodos(Long todos) {
        this.todos = todos;
    }
}
