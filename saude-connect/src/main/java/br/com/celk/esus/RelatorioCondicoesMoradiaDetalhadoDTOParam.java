package br.com.celk.esus;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCondicoesMoradiaDetalhadoDTOParam implements Serializable {
    
    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        MICROAREA(Bundle.getStringApplication("rotulo_microarea"));

        private final String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

        public String descricao() {
            return name;
        }

    }
    
    public enum CondicaoAvaliada {

        ABASTECIMENTO_AGUA(Bundle.getStringApplication("rotulo_abastecimento_agua")),
        AGUA_CONSUMO_DOMICILIO(Bundle.getStringApplication("rotulo_agua_consumo_domicilio")),
        CONDICAO_POSSE_USO_TERRA(Bundle.getStringApplication("rotulo_condicao_posse_uso_terra")),
        DESTINO_LIXO(Bundle.getStringApplication("rotulo_destino_lixo")),
        DISPONIBILIDADE_ENERGIA_ELETRICA(Bundle.getStringApplication("rotulo_disponibilidade_energia_eletrica")),
        FORMA_ESCOAMENTO_BANHEIRO(Bundle.getStringApplication("rotulo_forma_escoamento_banheiro")),
        LOCALIZACAO(Bundle.getStringApplication("rotulo_localizacao")),
        MATERIAL_PREDOMINANTE_PAREDES_EXTERNAS(Bundle.getStringApplication("rotulo_material_predominante_paredes_externas")),
        NUMERO_COMODOS(Bundle.getStringApplication("rotulo_numero_comodos")),
        SITUACAO_MORADIA(Bundle.getStringApplication("rotulo_situacao_moradia")),
        TIPO_ACESSO_DOMICILIO(Bundle.getStringApplication("rotulo_tipo_acesso_domicilio")),
        TIPO_DOMICILIO(Bundle.getStringApplication("rotulo_tipo_domicilio")),
        LOCAL_PROLIFERACAO_MOSQUITO(Bundle.getStringApplication("rotulo_local_proliferacao_mosquito"))
        ;

        private final String name;

        private CondicaoAvaliada(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

        public String descricao() {
            return name;
        }

    }

    private List<Empresa> estabelecimento;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    private FormaApresentacao formaApresentacao;
    private CondicaoAvaliada condicaoAvaliada;
    private Long situacaoMoradia;
    private Long localizacao;
    private Long tipoDomicilio;
    private Long numeroComodos;
    private Long condicaoUsoTerra;
    private Long tipoAcessoDomicilio;
    private Long materialDominante;
    private Long possuiEnergiaEletrica;
    private Long abastecimentoAgua;
    private Long tratamentoAgua;
    private Long esgotamento;
    private Long destinoLixo;
    private Long opcao;
    private Long localProliferacaoMosquito;

    @DescricaoParametro("rotulo_estabelecimento")
    public List<Empresa> getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(List<Empresa> estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    @DescricaoParametro("rotulo_area")
    public EquipeArea getArea() {
        return equipeArea;
    }

    public void setArea(EquipeArea area) {
        this.equipeArea = area;
    }

    @DescricaoParametro("rotulo_microarea")
    public String getDescricaoEquipeMicroArea() {
        return equipeMicroArea != null ? (equipeMicroArea.getMicroArea().toString()) : "";
    }
    
    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }
    
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }
    
    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getDescricaoFormaApresentacao() {
        return formaApresentacao.descricao();
    }
    
    public CondicaoAvaliada getCondicaoAvaliada() {
        return condicaoAvaliada;
    }

    public void setCondicaoAvaliada(CondicaoAvaliada condicaoMoradia) {
        this.condicaoAvaliada = condicaoMoradia;
    }
    
    @DescricaoParametro("rotulo_condicao_avaliada")
    public String getDescricaoCondicaoAvaliada() {
        return condicaoAvaliada.descricao();
    }
    
    public Long getOpcao() {
        return opcao;
    }

    public void setOpcao(Long opcao) {
        this.opcao = opcao;
    }

    @DescricaoParametro("rotulo_opcao")
    public String getDescricaoOpcao() {
        String descricaoOpcao = null;
        if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.ABASTECIMENTO_AGUA.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.AbastecimentoAgua.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.AGUA_CONSUMO_DOMICILIO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.TratamentoAgua.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.CONDICAO_POSSE_USO_TERRA.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.CondicaoPosseTerra.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.DESTINO_LIXO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.DestinoLixo.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.DISPONIBILIDADE_ENERGIA_ELETRICA.equals(getCondicaoAvaliada())){
            if (RepositoryComponentDefault.SIM_LONG.equals(getOpcao())) {
                descricaoOpcao = "Sim";
            } else {
                descricaoOpcao = "Não";                
            }
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.FORMA_ESCOAMENTO_BANHEIRO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.EscoamentoSanitario.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.LOCALIZACAO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.Localizacao.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.MATERIAL_PREDOMINANTE_PAREDES_EXTERNAS.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.MaterialPredominante.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.SITUACAO_MORADIA.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.SituacaoMoradia.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.TIPO_ACESSO_DOMICILIO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.TipoAcessoDomicilio.valueOf(getOpcao()).descricao();
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.TIPO_DOMICILIO.equals(getCondicaoAvaliada())){
            descricaoOpcao = EnderecoDomicilioEsus.TipoDomicilio.valueOf(getOpcao()).descricao();
        } else if(CondicaoAvaliada.LOCAL_PROLIFERACAO_MOSQUITO.equals(getCondicaoAvaliada())){
            if (RepositoryComponentDefault.SIM_LONG.equals(getOpcao())) {
                descricaoOpcao = "Sim";
            } else {
                descricaoOpcao = "Não";
            }
        }
        return descricaoOpcao;
    }
    
    public Long getSituacaoMoradia() {
        return situacaoMoradia;
    }

    public void setSituacaoMoradia(Long situacaoMoradia) {
        this.situacaoMoradia = situacaoMoradia;
    }


    public Long getLocalizacao() {
        return localizacao;
    }

    public void setLocalizacao(Long localizacao) {
        this.localizacao = localizacao;
    }


    public Long getTipoDomicilio() {
        return tipoDomicilio;
    }

    public void setTipoDomicilio(Long tipoDomicilio) {
        this.tipoDomicilio = tipoDomicilio;
    }


    @DescricaoParametro("rotulo_numero_comodos_abv")
    public Long getNumeroComodos() {
        return numeroComodos;
    }

    public void setNumeroComodos(Long numeroComodos) {
        this.numeroComodos = numeroComodos;
    }

    public Long getCondicaoUsoTerra() {
        return condicaoUsoTerra;
    }

    public void setCondicaoUsoTerra(Long condicaoUsoTerra) {
        this.condicaoUsoTerra = condicaoUsoTerra;
    }


    public Long getTipoAcessoDomicilio() {
        return tipoAcessoDomicilio;
    }

    public void setTipoAcessoDomicilio(Long tipoAcessoDomicilio) {
        this.tipoAcessoDomicilio = tipoAcessoDomicilio;
    }


    public Long getMaterialDominante() {
        return materialDominante;
    }

    public void setMaterialDominante(Long materialDominante) {
        this.materialDominante = materialDominante;
    }


    public Long getPossuiEnergiaEletrica() {
        return possuiEnergiaEletrica;
    }

    public void setPossuiEnergiaEletrica(Long possuiEnergiaEletrica) {
        this.possuiEnergiaEletrica = possuiEnergiaEletrica;
    }

    public Long getLocalProliferacaoMosquito() {
        return localProliferacaoMosquito;
    }

    public void setLocalProliferacaoMosquito(Long localProliferacaoMosquito) {
        this.localProliferacaoMosquito = localProliferacaoMosquito;
    }



    public Long getAbastecimentoAgua() {
        return abastecimentoAgua;
    }

    public void setAbastecimentoAgua(Long abastecimentoAgua) {
        this.abastecimentoAgua = abastecimentoAgua;
    }


    public Long getTratamentoAgua() {
        return tratamentoAgua;
    }

    public void setTratamentoAgua(Long tratamentoAgua) {
        this.tratamentoAgua = tratamentoAgua;
    }


    public Long getEsgotamento() {
        return esgotamento;
    }

    public void setEsgotamento(Long esgotamento) {
        this.esgotamento = esgotamento;
    }


    public Long getDestinoLixo() {
        return destinoLixo;
    }

    public void setDestinoLixo(Long destinoLixo) {
        this.destinoLixo = destinoLixo;
    }

    @DescricaoParametro("rotulo_situacao_moradia_posse_terra")
    public String getDescricaoSituacaoMoradia() {
        if (getSituacaoMoradia() != null) {
            for (EnderecoDomicilioEsus.SituacaoMoradia moradia : EnderecoDomicilioEsus.SituacaoMoradia.values()) {
                if (moradia.value().equals(getSituacaoMoradia())) {
                    return moradia.descricao();
                }
            }
        }
        return "";
    }


    @DescricaoParametro("rotulo_localizacao")
    public String getDescricaoLocalizacao() {
        if (getLocalizacao() != null) {
            for (EnderecoDomicilioEsus.Localizacao l : EnderecoDomicilioEsus.Localizacao.values()) {
                if (l.value().equals(getLocalizacao())) {
                    return l.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_tipo_domicilio")
    public String getDescricaoTipoDomicilio() {
        if (getTipoDomicilio() != null) {
            for (EnderecoDomicilioEsus.TipoDomicilio td : EnderecoDomicilioEsus.TipoDomicilio.values()) {
                if (td.value().equals(getLocalizacao())) {
                    return td.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_condicao_posse_uso_terra")
    public String getDescricaocondicaoUsoTerra() {
        if (getCondicaoUsoTerra() != null) {
            for (EnderecoDomicilioEsus.CondicaoPosseTerra condicaoPosseTerra : EnderecoDomicilioEsus.CondicaoPosseTerra.values()) {
                if (condicaoPosseTerra.value().equals(getCondicaoUsoTerra())) {
                    return condicaoPosseTerra.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_tipo_acesso_domicilio")
    public String getDescricaoTipoAcessoDomicilio() {
        if (getCondicaoUsoTerra() != null) {
            for (EnderecoDomicilioEsus.TipoAcessoDomicilio tad : EnderecoDomicilioEsus.TipoAcessoDomicilio.values()) {
                if (tad.value().equals(getCondicaoUsoTerra())) {
                    return tad.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_material_predominante_paredes_externas")
    public String getDescricaoMaterialPredominante() {
        if (getMaterialDominante() != null) {
            for (EnderecoDomicilioEsus.MaterialPredominante materialPredominante : EnderecoDomicilioEsus.MaterialPredominante.values()) {
                if (materialPredominante.value().equals(getMaterialDominante())) {
                    return materialPredominante.descricao();
                }
            }
        }
        return "";
    }


    @DescricaoParametro("rotulo_disponibilidade_energia_eletrica")
    public String getDescricaoPossuiEnergiaEletrica() {
        if (getPossuiEnergiaEletrica() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getPossuiEnergiaEletrica())) {
                return "Sim";
            }
            return "Não";
        }
        return "";
    }

    @DescricaoParametro("rotulo_local_proliferacao_mosquito")
    public String getDescricaoLocalProliferacaoMosquito() {
        if (getLocalProliferacaoMosquito() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(getLocalProliferacaoMosquito())) {
                return "Sim";
            }
            return "Não";
        }
        return "";
    }

    @DescricaoParametro("rotulo_abastecimento_agua")
    public String getDescricaoAbastecimentoAgua() {
        if (getAbastecimentoAgua() != null) {
            for (EnderecoDomicilioEsus.AbastecimentoAgua aa : EnderecoDomicilioEsus.AbastecimentoAgua.values()) {
                if (aa.value().equals(getAbastecimentoAgua())) {
                    return aa.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_tratamento_agua_domicilio")
    public String getDescricaoTratamentoAgua() {
        if (getTratamentoAgua() != null) {
            for (EnderecoDomicilioEsus.TratamentoAgua ta : EnderecoDomicilioEsus.TratamentoAgua.values()) {
                if (ta.value().equals(getTratamentoAgua())) {
                    return ta.descricao();
                }
            }
        }
        return "";
    }

    @DescricaoParametro("rotulo_esgotamento_sanitario")
    public String getDescricaoEsgotamento() {
        if (getEsgotamento() != null) {
            for (EnderecoDomicilioEsus.EscoamentoSanitario escoamentoSanitario : EnderecoDomicilioEsus.EscoamentoSanitario.values()) {
                if (escoamentoSanitario.value().equals(getEsgotamento())) {
                    return escoamentoSanitario.descricao();
                }
            }
        }
        return "";
    }


    @DescricaoParametro("rotulo_destino_lixo")
    public String getDescricaoDestinoLixo() {
        if (getDestinoLixo() != null) {
            for (EnderecoDomicilioEsus.DestinoLixo dl : EnderecoDomicilioEsus.DestinoLixo.values()) {
                if (dl.value().equals(getDestinoLixo())) {
                    return dl.descricao();
                }
            }
        }
        return "";
    }
    
}
