package br.com.celk.esus.interfaces.dto;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPlanejamentoVisitaDTOParam implements Serializable {

    private Profissional profissional;
    private DatePeriod periodo;
    private String sortProp;
    private boolean ascending;
    private DTOParamConfigureDefault configureParam;
    private EquipeMicroArea equipeMicroArea;

    public DTOParamConfigureDefault getConfigureParam() {
        if (configureParam == null) {
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getSortProp() {
        return sortProp;
    }

    public void setSortProp(String sortProp) {
        this.sortProp = sortProp;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

}
