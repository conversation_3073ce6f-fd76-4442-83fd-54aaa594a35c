package br.com.celk.esus.domicilio.query;

import br.com.ksisolucoes.vo.basico.EquipeMicroArea;

import java.io.Serializable;

public class ConsultaEnderecoDomicilioCadastroFichaDTOParam implements Serializable {

    private Long tipoVisita;
    private EquipeMicroArea equipeMicroArea;
    private Long familia;
    private Long domicilio;
    private String endereco;
    private Long apenasEnderecosComFamilia;
    private Long apenasFamiliaNaoVisitada;
    private String paciente;
    private Long codigoPaciente;
    private Long referenciaPaciente;
    private String numeroCartao;


    public Long getTipoVisita() {
        return tipoVisita;
    }

    public void setTipoVisita(Long tipoVisita) {
        this.tipoVisita = tipoVisita;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public Long getFamilia() {
        return familia;
    }

    public void setFamilia(Long familia) {
        this.familia = familia;
    }

    public Long getDomicilio() {
        return domicilio;
    }

    public void setDomicilio(Long domicilio) {
        this.domicilio = domicilio;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public Long getApenasEnderecosComFamilia() {
        return apenasEnderecosComFamilia;
    }

    public void setApenasEnderecosComFamilia(Long apenasEnderecosComFamilia) {
        this.apenasEnderecosComFamilia = apenasEnderecosComFamilia;
    }

    public Long getApenasFamiliaNaoVisitada() {
        return apenasFamiliaNaoVisitada;
    }

    public void setApenasFamiliaNaoVisitada(Long apenasFamiliaNaoVisitada) {
        this.apenasFamiliaNaoVisitada = apenasFamiliaNaoVisitada;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public Long getReferenciaPaciente() {
        return referenciaPaciente;
    }

    public void setReferenciaPaciente(Long referenciaPaciente) {
        this.referenciaPaciente = referenciaPaciente;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }
}
