package br.com.celk.esus.interfaces.dto;

import br.com.ksisolucoes.vo.esus.EsusFicha;
import br.com.ksisolucoes.vo.esus.EsusTransporte;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by maicon on 03/10/16.
 */
public class EsusDetalheTransporteDTO implements Serializable {

    private EsusTransporte esusTransporte;
    private List<EsusFicha> esusFichaList = new ArrayList<EsusFicha>();;

    public EsusDetalheTransporteDTO(EsusTransporte esusTransporte) {
        this.esusTransporte = esusTransporte;
    }

    public EsusTransporte getEsusTransporte() {
        return esusTransporte;
    }

    public void setEsusTransporte(EsusTransporte esusTransporte) {
        this.esusTransporte = esusTransporte;
    }

    public List<EsusFicha> getEsusFichaList() {
        return esusFichaList;
    }

    public void setEsusFichaList(List<EsusFicha> esusFichaList) {
        this.esusFichaList = esusFichaList;
    }
}
