package br.com.celk.solicitacaoagendamento;

import br.com.celk.agendamento.ValidacoesAgendamentoBehavior;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.logging.Level;
import java.util.logging.Logger;

public class ValidacaoDadosPacienteImpl implements ValidacaoDadosPaciente {

    @Override
    public boolean isDadosPacienteObrigatorio(UsuarioCadsus usuarioCadsus) {
        if (this.getParamObrigatoriedadeInformacoesAdicionaisDoPacienteEstaHabilitado()) {
            return validarDadosObrigatoriosPaciente(usuarioCadsus);
        } else {
            return false;
        }
    }

    private boolean validarDadosObrigatoriosPaciente(UsuarioCadsus paciente) {
        boolean cpfVazio = paciente.getCpf() == null || "".equals(Coalesce.asString(paciente.getCpf()));
        boolean cnsVazio = paciente.getCns() == null || "".equals(Coalesce.asString(paciente.getCns()));
        boolean celularVazio = paciente.getCelular() == null || "".equals(Coalesce.asString(paciente.getCelular()));
        boolean cepVazio = (paciente.getEnderecoUsuarioCadsus() == null) || paciente.getEnderecoUsuarioCadsus().getCep() == null || "".equals(Coalesce.asString(paciente.getEnderecoUsuarioCadsus().getCep()));
        return cpfVazio && (cnsVazio || celularVazio || cepVazio);
    }

    private boolean getParamObrigatoriedadeInformacoesAdicionaisDoPacienteEstaHabilitado() {
        String obrigatorioInformacoesAdicionaisDoPaciente = null;
        try {
            obrigatorioInformacoesAdicionaisDoPaciente = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("obrigatorioInformacoesAdicionaisDoPaciente");
        } catch (DAOException ex) {
            Logger.getLogger(ValidacoesAgendamentoBehavior.class.getName()).log(Level.SEVERE, null, ex);
        }
        return RepositoryComponentDefault.SIM.equals(obrigatorioInformacoesAdicionaisDoPaciente);
    }

}
