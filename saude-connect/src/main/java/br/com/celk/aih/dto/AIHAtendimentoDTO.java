/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.aih.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class AIHAtendimentoDTO implements Serializable {

    private Atendimento atendimento;

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

}
