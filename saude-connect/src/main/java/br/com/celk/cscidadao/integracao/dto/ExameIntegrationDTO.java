package br.com.celk.cscidadao.integracao.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ExameIntegrationDTO extends DefaultDTO implements Serializable {

    private String nomePaciente;
    private String cnsPaciente;
    private String dataNascimentoPaciente;
    private String estabelecimento;
    private String idRegistroClientUnidade;
    private String descricaoExame;
    private String situacao;
    private String dataSituacao;
    @JsonIgnore
    private Long codigoUsuarioCadsus;

    public ExameIntegrationDTO() {
    }

    public ExameIntegrationDTO(String idRegistroClient, String nomePaciente, String cnsPaciente, String dataNascimentoPaciente, String estabelecimento, String descricaoExame, String situacao, String dataSituacao) {
        setIdRegistroClient(idRegistroClient);
        this.nomePaciente = nomePaciente;
        this.cnsPaciente = cnsPaciente;
        this.dataNascimentoPaciente = dataNascimentoPaciente;
        this.estabelecimento = estabelecimento;
        this.idRegistroClientUnidade = idRegistroClientUnidade;
        this.descricaoExame = descricaoExame;
        this.situacao = situacao;
        this.dataSituacao = dataSituacao;
    }

    public String getIdRegistroClientUnidade() {
        return idRegistroClientUnidade;
    }

    public void setIdRegistroClientUnidade(String idRegistroClientUnidade) {
        this.idRegistroClientUnidade = idRegistroClientUnidade;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(String dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getDescricaoExame() {
        return descricaoExame;
    }

    public void setDescricaoExame(String descricaoExame) {
        this.descricaoExame = descricaoExame;
    }

    public String getSituacao() {
        for (ExameRequisicao.Status status : ExameRequisicao.Status.values()) {
            if (status.value().toString().equals(this.situacao)) {
                return status.descricao();
            }
        }
        return "";
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDataSituacao() {
        return dataSituacao;
    }

    public void setDataSituacao(String dataSituacao) {
        this.dataSituacao = dataSituacao;
    }

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", exame: " + getDescricaoExame() + ", situação: " + getSituacao() + ", estabelecimento:" + getEstabelecimento();
    }

}
