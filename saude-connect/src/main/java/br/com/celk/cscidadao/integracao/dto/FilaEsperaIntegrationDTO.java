package br.com.celk.cscidadao.integracao.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class FilaEsperaIntegrationDTO extends DefaultDTO implements Serializable {

    private String idLote;
    private String nomePaciente;
    private String cnsPaciente;
    private String dataNascimentoPaciente;
    private String descricaoItemSolicitado;
    private String prioridade;
    private String situacao;
    private String estabelecimento;
    private String idRegistroClientUnidade;
    private String dataSolicitacao;
    private String idRegistroClientItemSolicitado;
    @JsonIgnore
    private Long codigoUsuarioCadsus;

    public FilaEsperaIntegrationDTO() {
    }

    public FilaEsperaIntegrationDTO(String idRegistroClient, String idLote, String nomePaciente, String cnsPaciente, String dataNascimentoPaciente, String descricaoItemSolicitado, String prioridade, String situacao, String estabelecimento, String dataSolicitacao) {
        setIdRegistroClient(idRegistroClient);
        this.idLote = idLote;
        this.nomePaciente = nomePaciente;
        this.cnsPaciente = cnsPaciente;
        this.dataNascimentoPaciente = dataNascimentoPaciente;
        this.descricaoItemSolicitado = descricaoItemSolicitado;
        this.prioridade = prioridade;
        this.situacao = situacao;
        this.estabelecimento = estabelecimento;
        this.idRegistroClientUnidade = idRegistroClientUnidade;
        this.dataSolicitacao = dataSolicitacao;
    }

    public String getIdRegistroClientItemSolicitado() {
        return idRegistroClientItemSolicitado;
    }

    public void setIdRegistroClientItemSolicitado(String idRegistroClientItemSolicitado) {
        this.idRegistroClientItemSolicitado = idRegistroClientItemSolicitado;
    }

    public String getIdRegistroClientUnidade() {
        return idRegistroClientUnidade;
    }

    public void setIdRegistroClientUnidade(String idRegistroClientUnidade) {
        this.idRegistroClientUnidade = idRegistroClientUnidade;
    }

    public String getIdLote() {
        return idLote;
    }

    public void setIdLote(String idLote) {
        this.idLote = idLote;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(String dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public String getDescricaoItemSolicitado() {
        return descricaoItemSolicitado;
    }

    public void setDescricaoItemSolicitado(String descricaoItemSolicitado) {
        this.descricaoItemSolicitado = descricaoItemSolicitado;
    }

    public String getPrioridade() {
        if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(this.prioridade)) {
            return Bundle.getStringApplication("rotulo_urgente");
        } else if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(this.prioridade)) {
            return Bundle.getStringApplication("rotulo_brevidade");
        } else {
            return Bundle.getStringApplication("rotulo_eletivo");
        }
    }

    public void setPrioridade(String prioridade) {
        this.prioridade = prioridade;
    }

    public String getSituacao() {
        if (SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_aguardando_analise");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_aguardando_agendamento");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_agendado");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_status_negado");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_agendado");
        } else if (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_devolvido");
        } else if (SolicitacaoAgendamento.STATUS_CANCELADO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_cancelado");
        } else if (SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_agendado");
        } else if (SolicitacaoAgendamento.STATUS_DEVOLVIDO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_devolvido");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_aguardando_analise");
        } else if (SolicitacaoAgendamento.STATUS_AGUARDANDO_AUTORIZACAO.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_aguardando_autorizacao");
        } else if (SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR.toString().equals(this.situacao)) {
            return Bundle.getStringApplication("rotulo_aguardando_agendamento");
        }
        return "";
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getDataSolicitacao() {
        return dataSolicitacao;
    }

    public void setDataSolicitacao(String dataSolicitacao) {
        this.dataSolicitacao = dataSolicitacao;
    }

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", data da solicitação: " + getDataSolicitacao()
                + ", item solicitado: " + getDescricaoItemSolicitado() + ", estabelecimento:" + getEstabelecimento();
    }
}
