package br.com.celk.cscidadao.integracao.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AtendimentoIntegrationDTO extends DefaultDTO implements Serializable {

    private String nomePaciente;
    private String cnsPaciente;
    private String dataNascimentoPaciente;
    private String sexoPaciente;
    private String dataAtendimento;
    private String horaAtendimento;
    private String estabelecimento;
    private String idRegistroClientUnidade;
    private String tipoAtendimento;
    private String profissional;

    private Long codigoUsuarioCadSus;

    @JsonIgnore
    public Long getCodigoUsuarioCadSus() {
        return codigoUsuarioCadSus;
    }

    @JsonIgnore
    public void setCodigoUsuarioCadSus(Long codigoUsuarioCadSus) {
        this.codigoUsuarioCadSus = codigoUsuarioCadSus;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getDataNascimentoPaciente() {
        return dataNascimentoPaciente;
    }

    public void setDataNascimentoPaciente(String dataNascimentoPaciente) {
        this.dataNascimentoPaciente = dataNascimentoPaciente;
    }

    public String getSexoPaciente() {
        return sexoPaciente;
    }

    public void setSexoPaciente(String sexoPaciente) {
        this.sexoPaciente = sexoPaciente;
    }

    public String getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(String dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getHoraAtendimento() {
        return horaAtendimento;
    }

    public void setHoraAtendimento(String horaAtendimento) {
        this.horaAtendimento = horaAtendimento;
    }

    public String getIdRegistroClientUnidade() {
        return idRegistroClientUnidade;
    }

    public void setIdRegistroClientUnidade(String idRegistroClientUnidade) {
        this.idRegistroClientUnidade = idRegistroClientUnidade;
    }

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public String getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(String tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public String getProfissional() {
        return profissional;
    }

    public void setProfissional(String profissional) {
        this.profissional = profissional;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", tipo de atendimento: " + getTipoAtendimento() + ", estabelecimento:" + getEstabelecimento();
    }
    
    
}
