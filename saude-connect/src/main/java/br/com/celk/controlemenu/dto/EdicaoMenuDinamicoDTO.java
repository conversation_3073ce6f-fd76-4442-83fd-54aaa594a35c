package br.com.celk.controlemenu.dto;

import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EdicaoMenuDinamicoDTO implements Serializable {
    
    private String descricao;
    private MenuWeb modulo;
    private MenuWeb menu;
    private MenuWeb submenu;
    private MenuWeb programa;
    private Long layoutMenu;
    
    private Long codigoPrograma;
    private Long tipoMenu;
    private List<MenuWeb> caminhosProgramasList;
    private List<MenuWeb> caminhosProgramasRemovidosList;

    public Long getCodigoPrograma() {
        return codigoPrograma;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public MenuWeb getModulo() {
        return modulo;
    }

    public void setModulo(MenuWeb modulo) {
        this.modulo = modulo;
    }

    public MenuWeb getMenu() {
        return menu;
    }

    public void setMenu(MenuWeb menu) {
        this.menu = menu;
    }

    public MenuWeb getSubmenu() {
        return submenu;
    }

    public void setSubmenu(MenuWeb submenu) {
        this.submenu = submenu;
    }

    public MenuWeb getPrograma() {
        return programa;
    }

    public void setPrograma(MenuWeb programa) {
        this.programa = programa;
    }

    public void setCodigoPrograma(Long codigoPrograma) {
        this.codigoPrograma = codigoPrograma;
    }
    
    public Long getTipoMenu() {
        return tipoMenu;
    }

    public void setTipoMenu(Long tipoMenu) {
        this.tipoMenu = tipoMenu;
    }

    public List<MenuWeb> getCaminhosProgramasList() {
        return caminhosProgramasList;
    }

    public void setCaminhosProgramasList(List<MenuWeb> caminhosProgramasList) {
        this.caminhosProgramasList = caminhosProgramasList;
    }

    public List<MenuWeb> getCaminhosProgramasRemovidosList() {
        return caminhosProgramasRemovidosList;
    }

    public void setCaminhosProgramasRemovidosList(List<MenuWeb> caminhosProgramasRemovidosList) {
        this.caminhosProgramasRemovidosList = caminhosProgramasRemovidosList;
    }

    public Long getLayoutMenu() {
        return layoutMenu;
    }

    public void setLayoutMenu(Long layoutMenu) {
        this.layoutMenu = layoutMenu;
    }
}
