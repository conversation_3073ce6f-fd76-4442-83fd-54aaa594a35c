package br.com.celk.recepcao.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.basico.PessoaContrato;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRecepcaoTipoProcedimentoConfirmacaoPresencaPanelDTO implements Serializable {

    private Long codigoEmpresa;
    private DatePeriod dataAgendamento;
    private boolean consultaFiltroConfirmacaoPresenca;
    private boolean exameFiltroConfirmacaoPresenca;

    public QueryRecepcaoTipoProcedimentoConfirmacaoPresencaPanelDTO(Long codigoEmpresa, DatePeriod dataAgendamento, boolean consultaFiltroConfirmacaoPresenca, boolean exameFiltroConfirmacaoPresenca) {
        this.codigoEmpresa = codigoEmpresa;
        this.dataAgendamento = dataAgendamento;
        this.consultaFiltroConfirmacaoPresenca = consultaFiltroConfirmacaoPresenca;
        this.exameFiltroConfirmacaoPresenca = exameFiltroConfirmacaoPresenca;
    }

    public QueryRecepcaoTipoProcedimentoConfirmacaoPresencaPanelDTO() {
        this.consultaFiltroConfirmacaoPresenca = false;
        this.exameFiltroConfirmacaoPresenca = false;
    }

    public Long getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Long codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public DatePeriod getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(DatePeriod dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public boolean isConsultaFiltroConfirmacaoPresenca() {
        return consultaFiltroConfirmacaoPresenca;
    }

    public void setConsultaFiltroConfirmacaoPresenca(boolean consultaFiltroConfirmacaoPresenca) {
        this.consultaFiltroConfirmacaoPresenca = consultaFiltroConfirmacaoPresenca;
    }

    public boolean isExameFiltroConfirmacaoPresenca() {
        return exameFiltroConfirmacaoPresenca;
    }

    public void setExameFiltroConfirmacaoPresenca(boolean exameFiltroConfirmacaoPresenca) {
        this.exameFiltroConfirmacaoPresenca = exameFiltroConfirmacaoPresenca;
    }
}
