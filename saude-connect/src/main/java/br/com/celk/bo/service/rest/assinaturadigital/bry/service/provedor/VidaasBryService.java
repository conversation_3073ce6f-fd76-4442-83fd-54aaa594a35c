package br.com.celk.bo.service.rest.assinaturadigital.bry.service.provedor;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

public class VidaasBryService extends PscService {

    private final String ENDPOINT = "/v0/oauth/authorize?";

    public VidaasBryService() {
        super();
    }

    public VidaasBryService(PscAuthorizeRequestParam dto) {
        super(dto);
    }

    @Override
    public String getAuthUrl() throws ValidacaoException {
        try {
            String url = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("urlVidaas");

            return url + ENDPOINT + dto.toQueryString();
        } catch (DAOException e) {
            throw new ValidacaoException(e);
        }
    }

    @Override
    public String generateToken() {
        return "";
    }
}
