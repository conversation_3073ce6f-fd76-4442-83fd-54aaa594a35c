package br.com.celk.bo.emprestimo.interfaces.dto;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem;
import br.com.ksisolucoes.vo.emprestimo.TipoEmprestimo;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ResumoEmprestimosDTOParam implements Serializable {

    public enum FormaApresentacao {

        TIPO_EMPRESTIMO(Bundle.getStringApplication("rotulo_tipo_emprestimo")),
        SIGNATARIO(Bundle.getStringApplication("rotulo_signatario")),
        ESTABELECIMENTO_EMPRESTIMO(Bundle.getStringApplication("rotulo_estabelecimento_emprestimo")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TipoResumo {

        TIPO_EMPRESTIMO(Bundle.getStringApplication("rotulo_tipo_emprestimo")),
        SIGNATARIO(Bundle.getStringApplication("rotulo_signatario")),
        ESTABELECIMENTO_EMPRESTIMO(Bundle.getStringApplication("rotulo_estabelecimento_emprestimo")),
        PACIENTE(Bundle.getStringApplication("rotulo_paciente"));

        private String name;

        private TipoResumo(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum Situacao implements IEnum {

        PENDENTE(LancamentoEmprestimoItem.Status.PENDENTE.value(), LancamentoEmprestimoItem.Status.PENDENTE.descricao()),
        DEVOLVIDO(LancamentoEmprestimoItem.Status.DEVOLVIDO.value(), LancamentoEmprestimoItem.Status.DEVOLVIDO.descricao()),
        CANCELADO(LancamentoEmprestimoItem.Status.CANCELADO.value(), LancamentoEmprestimoItem.Status.CANCELADO.descricao());

        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

        public static Situacao valueOf(Long value) {
            for (Situacao situacao : Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }
    }

    public enum Ordenacao {

        PRODUTO(Bundle.getStringApplication("rotulo_produto")),
        QUANTIDADE(Bundle.getStringApplication("rotulo_quantidade"));

        private String name;

        private Ordenacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TipoOrdenacao {

        CRESCENTE(Bundle.getStringApplication("rotulo_crescente"), QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST),
        DECRESCENTE(Bundle.getStringApplication("rotulo_decrescente"), QueryCustom.QueryCustomSorter.DECRESCENTE_NULLS_LAST);

        private String name;
        private String command;

        private TipoOrdenacao(String name, String command) {
            this.name = name;
            this.command = command;
        }

        @Override
        public String toString() {
            return name;
        }

        public String getCommand() {
            return command;
        }
    }

    public enum TipoOperacao implements IEnum {

        ENTRADA(TipoEmprestimo.FlagTipoEmprestimo.ENTRADA.value(), TipoEmprestimo.FlagTipoEmprestimo.ENTRADA.descricao()),
        SAIDA(TipoEmprestimo.FlagTipoEmprestimo.SAIDA.value(), TipoEmprestimo.FlagTipoEmprestimo.SAIDA.descricao());

        private Long value;
        private String descricao;

        private TipoOperacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

        public static TipoOperacao valueOf(Long value) {
            for (TipoOperacao tipoOperacao : TipoOperacao.values()) {
                if (tipoOperacao.value().equals(value)) {
                    return tipoOperacao;
                }
            }
            return null;
        }
    }

    private OperadorValor<List<Empresa>> estabelecimento;
    private TipoEmprestimo tipoEmprestimo;
    private Long tipoOperacao;
    private Long situacao;
    private UsuarioCadsus paciente;
    private Empresa estabelecimentoEmprestimo;
    private Produto produto;
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private Ordenacao ordenacao;
    private TipoOrdenacao tipoOrdenacao;
    private DatePeriod periodo;
    private String visualizarTotais;

    @DescricaoParametro("rotulo_tipo_emprestimo")
    public TipoEmprestimo getTipoEmprestimo() {
        return tipoEmprestimo;
    }

    public void setTipoEmprestimo(TipoEmprestimo tipoEmprestimo) {
        this.tipoEmprestimo = tipoEmprestimo;
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public OperadorValor<List<Empresa>> getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(OperadorValor<List<Empresa>> estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    @DescricaoParametro("rotulo_tipo_operacao")
    public String getDescricaoTipoOperacao() {
        TipoOperacao tipoOperacao = TipoOperacao.valueOf(getTipoOperacao());
        if (tipoOperacao != null) {
            return tipoOperacao.descricao();
        }
        return null;
    }

    public Long getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(Long tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    @DescricaoParametro("rotulo_estabelecimento_emprestimo")
    public Empresa getEstabelecimentoEmprestimo() {
        return estabelecimentoEmprestimo;
    }

    public void setEstabelecimentoEmprestimo(Empresa estabelecimentoEmprestimo) {
        this.estabelecimentoEmprestimo = estabelecimentoEmprestimo;
    }

    @DescricaoParametro("rotulo_produto")
    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        Situacao situacao = Situacao.valueOf(getSituacao());
        if (situacao != null) {
            return situacao.descricao();
        }
        return null;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_ordenacao")
    public TipoOrdenacao getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(TipoOrdenacao tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_visualizar_totais")
    public String getDescricaoVisualizaTotais() {
        if (RepositoryComponentDefault.SIM.equals(getVisualizarTotais())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public String getVisualizarTotais() {
        return visualizarTotais;
    }

    public void setVisualizarTotais(String visualizarTotais) {
        this.visualizarTotais = visualizarTotais;
    }

    private Boolean viewFormaApresentacao;

    public Boolean isViewFormaApresentacao() {
        if (viewFormaApresentacao == null) {
            viewFormaApresentacao = !((getFormaApresentacao().equals(FormaApresentacao.ESTABELECIMENTO_EMPRESTIMO) && getTipoResumo().equals(TipoResumo.ESTABELECIMENTO_EMPRESTIMO))
                    || (getFormaApresentacao().equals(FormaApresentacao.PACIENTE) && getTipoResumo().equals(TipoResumo.PACIENTE))
                    || (getFormaApresentacao().equals(FormaApresentacao.SIGNATARIO) && getTipoResumo().equals(TipoResumo.SIGNATARIO))
                    || (getFormaApresentacao().equals(FormaApresentacao.TIPO_EMPRESTIMO) && getTipoResumo().equals(TipoResumo.TIPO_EMPRESTIMO)));
        }
        return viewFormaApresentacao;
    }
}
