package br.com.celk.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoFiscais;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiro;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItem;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiroItemPerguntaResposta;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Deprecated
public class FinalizarRegistroRoteiroInspecaoDTO implements Serializable {

    private RegistroInspecao registroInspecao;
    private List<RegistroInspecaoFiscais> fiscais;
    private List<RegistroInspecaoRoteiro> roteiros;
    private List<RegistroInspecaoRoteiroItem> itensRoteiro;
    private List<RegistroInspecaoRoteiroItemPerguntaResposta> perguntasRespostasItem;

    public FinalizarRegistroRoteiroInspecaoDTO(RegistroInspecao registroInspecao, List<RegistroInspecaoFiscais> fiscais) {
        this.registroInspecao = registroInspecao;
        this.fiscais = fiscais;
    }

    public RegistroInspecao getRegistroInspecao() {
        return registroInspecao;
    }

    public void setRegistroInspecao(RegistroInspecao registroInspecao) {
        this.registroInspecao = registroInspecao;
    }

    public List<RegistroInspecaoFiscais> getFiscais() {
        return fiscais;
    }

    public void setFiscais(List<RegistroInspecaoFiscais> fiscais) {
        this.fiscais = fiscais;
    }

    public List<RegistroInspecaoRoteiro> getRoteiros() {
        return roteiros;
    }

    public void setRoteiros(List<RegistroInspecaoRoteiro> roteiros) {
        this.roteiros = roteiros;
    }

    public List<RegistroInspecaoRoteiroItem> getItensRoteiro() {
        return itensRoteiro;
    }

    public void setItensRoteiro(List<RegistroInspecaoRoteiroItem> itensRoteiro) {
        this.itensRoteiro = itensRoteiro;
    }

    public List<RegistroInspecaoRoteiroItemPerguntaResposta> getPerguntasRespostasItem() {
        return perguntasRespostasItem;
    }

    public void setPerguntasRespostasItem(List<RegistroInspecaoRoteiroItemPerguntaResposta> perguntasRespostasItem) {
        this.perguntasRespostasItem = perguntasRespostasItem;
    }

}
