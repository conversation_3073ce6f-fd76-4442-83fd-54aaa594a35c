package br.com.celk.bo.hospital.financeiro.interfaces.dto;

import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceira;
import br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class LiberacaoPacienteDTO implements Serializable {
    
    private ContaFinanceira contaFinanceira;
    private Double valorTotal;
    private Double valorPago;
    private Double saldoDevedor;
    private Double valor;
    private FormaPagamento formaPagamento;
    private Long desconto;

    public ContaFinanceira getContaFinanceira() {
        return contaFinanceira;
    }

    public void setContaFinanceira(ContaFinanceira contaFinanceira) {
        this.contaFinanceira = contaFinanceira;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Double getValorPago() {
        return valorPago;
    }

    public void setValorPago(Double valorPago) {
        this.valorPago = valorPago;
    }

    public Double getSaldoDevedor() {
        return saldoDevedor;
    }

    public void setSaldoDevedor(Double saldoDevedor) {
        this.saldoDevedor = saldoDevedor;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamento formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Long getDesconto() {
        return desconto;
    }

    public void setDesconto(Long desconto) {
        this.desconto = desconto;
    }
}
