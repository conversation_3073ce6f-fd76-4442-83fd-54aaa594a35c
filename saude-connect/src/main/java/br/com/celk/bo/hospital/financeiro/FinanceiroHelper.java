package br.com.celk.bo.hospital.financeiro;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceira;
import br.com.ksisolucoes.vo.hospital.financeiro.ContaFinanceiraItem;
import br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.hamcrest.Matchers;

/**
 *
 * <AUTHOR>
 */
public class FinanceiroHelper {
    
    public static Double getSomaValorCredito(ContaFinanceira contaFinanceira) throws DAOException, ValidacaoException{
        ContaFinanceiraItem proxy = on(ContaFinanceiraItem.class);        
        List<ContaFinanceiraItem> itemList = getContaFinanceiraItemAbertoList(contaFinanceira);
        
        return Coalesce.asDouble(Lambda.sum(
                Lambda.select(
                        itemList,
                        Lambda.having(
                                proxy.getTipoMovimentoContaFinanceira().getTipoMovimento(),
                                Matchers.equalTo(TipoMovimentoContaFinanceira.TipoMovimento.CREDITO.value())
                        )
                ),
                proxy.getValor()
        ));
    }
    
    public static Double getSomaValorDebito(ContaFinanceira contaFinanceira) throws DAOException, ValidacaoException{
        ContaFinanceiraItem proxy = on(ContaFinanceiraItem.class);        
        List<ContaFinanceiraItem> itemList = getContaFinanceiraItemAbertoList(contaFinanceira);
        
        return Coalesce.asDouble(Lambda.sum(
                Lambda.select(
                        itemList,
                        Lambda.having(
                                proxy.getTipoMovimentoContaFinanceira().getTipoMovimento(),
                                Matchers.equalTo(TipoMovimentoContaFinanceira.TipoMovimento.DEBITO.value())
                        )
                ),
                proxy.getValor()
        ));
    }
    
    public static List<ContaFinanceiraItem> getContaFinanceiraItemAbertoList(ContaFinanceira contaFinanceira){
        ContaFinanceiraItem proxy = on(ContaFinanceiraItem.class);
        
        return LoadManager.getInstance(ContaFinanceiraItem.class)
                    .addProperty(path(proxy.getValor()))
                    .addProperty(path(proxy.getTipoMovimentoContaFinanceira().getTipoMovimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getContaFinanceira()), contaFinanceira))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), ContaFinanceiraItem.Status.ABERTO.value()))
                    .start().getList();
    }
    
}
