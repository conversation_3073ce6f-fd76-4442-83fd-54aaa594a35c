package br.com.celk.bo.service.rest.assinaturadigital.bry.util;

import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoImagem;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoQrCode;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoTexto;
import br.com.celk.bo.service.rest.assinaturadigital.bry.enums.RotacaoTexto;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;

import java.util.Map;

public class ConfigUtil {

    public static ConfiguracaoImagem getDefaultImagemDocumentosConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("40");
        configuracaoImagem.setCoordenadaY("46");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemAtestadosConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("40");
        configuracaoImagem.setCoordenadaY("46");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemEncaminhamentoConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("40");
        configuracaoImagem.setCoordenadaY("14");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("PRIMEIRA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("50");
        configuracaoImagem.setLargura("50");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemProntuarioConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("40");
        configuracaoImagem.setCoordenadaY("14");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemExameConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setCoordenadaY("6");
        configuracaoImagem.setAltura("40");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoImagem getDefaultImagemReceituarioConfig() {
        ConfiguracaoImagem configuracaoImagem = new ConfiguracaoImagem();
        configuracaoImagem.setAltura("60");
        configuracaoImagem.setLargura("40");
        configuracaoImagem.setCoordenadaY("9");
        configuracaoImagem.setPosicao("INFERIOR_DIREITO");
        configuracaoImagem.setPagina("ULTIMA");
        return configuracaoImagem;
    }

    public static ConfiguracaoQrCode getDefaultQrCodeConfig() {
        ConfiguracaoQrCode configuracaoQrCode = new ConfiguracaoQrCode();
        configuracaoQrCode.setTexto("https://validar.iti.gov.br/");
        configuracaoQrCode.setDimensao(25);
        return configuracaoQrCode;
    }

    public static ConfiguracaoImagem getConfiguracaoByTipoDocumento(Long value) {
        if (DocumentoAssinado.OrigemTipoDocumento.DOCUMENTO.value().equals(value)) {
            return getDefaultImagemDocumentosConfig();
        } else if (DocumentoAssinado.OrigemTipoDocumento.ENCAMINHAMENTO_ESPECIALISTA.value().equals(value)) {
            return getDefaultImagemEncaminhamentoConfig();
        } else if (DocumentoAssinado.OrigemTipoDocumento.RECEITA.value().equals(value)) {
            return getDefaultImagemReceituarioConfig();
        } else if (DocumentoAssinado.OrigemTipoDocumento.PRONTUARIO.value().equals(value)) {
            return getDefaultImagemProntuarioConfig();
        } else if (DocumentoAssinado.OrigemTipoDocumento.EXAME.value().equals(value)) {
            return getDefaultImagemExameConfig();
        } else if (DocumentoAssinado.OrigemTipoDocumento.ATESTADO.value().equals(value)){
            return getDefaultImagemAtestadosConfig();
        } else {
            return getDefaultImagemConfig();
        }
    }

    public static Map<String, String> addMetadadosByDocumento(Map<String, String> metadados, Long tipoDocumento) {
        if (DocumentoAssinado.OrigemTipoDocumento.ENCAMINHAMENTO_ESPECIALISTA.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1", "Documentos Digitais da Saúde");
        } else if (DocumentoAssinado.OrigemTipoDocumento.DOCUMENTO.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1", "Documentos Digitais da Saúde");
        } else if (DocumentoAssinado.OrigemTipoDocumento.RECEITA.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1.1", "Prescrição de medicamento");
        } else if (DocumentoAssinado.OrigemTipoDocumento.EXAME.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1.3", "Solicitação de exame");
        } else if (DocumentoAssinado.OrigemTipoDocumento.ATESTADO.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1.2", "Atestado médico");
        } else if (DocumentoAssinado.OrigemTipoDocumento.PRONTUARIO.value().equals(tipoDocumento)) {
            metadados.put("*********.12.1.6", "Registro de atendimento clínico");
        }
        return metadados;
    }

    public static ConfiguracaoTexto getDefaultTextoConfig() {
        ConfiguracaoTexto configuracaoTexto = new ConfiguracaoTexto();
        configuracaoTexto.setIncluirCN(true);
        configuracaoTexto.setIncluirCPF(false);
        configuracaoTexto.setIncluirEmail(false);
        configuracaoTexto.setRotacaoTexto(RotacaoTexto.HORARIO_90_GRAUS);
        configuracaoTexto.setTexto("Assinado por:");
        return configuracaoTexto;
    }
}
