package br.com.celk.bo.service.rest.exame;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ConsultaExamesPendentesIntegracaoV12DTO implements Serializable{
    private ExameItemRestDTO item;

    private Long codigoExame;
    private Date dataCadastro;
    private Date dataAgendamento;
    
    private ConvenioRestDTO convenio;
    private PacienteRestV12DTO paciente;
    private EmpresaRestDTO unidade;
    private ProfissionalRestDTO profissional;

    public ProfissionalRestDTO getProfissional() {
        return profissional;
    }

    public void setProfissional(ProfissionalRestDTO profissional) {
        this.profissional = profissional;
    }

    public EmpresaRestDTO getUnidade() {
        return unidade;
    }

    public void setUnidade(EmpresaRestDTO unidade) {
        this.unidade = unidade;
    }

    public ExameItemRestDTO getItem() {
        return item;
    }

    public void setItem(ExameItemRestDTO item) {
        this.item = item;
    }
    
    public Long getCodigoExame() {
        return codigoExame;
    }

    public void setCodigoExame(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public ConvenioRestDTO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioRestDTO convenio) {
        this.convenio = convenio;
    }

    public PacienteRestV12DTO getPaciente() {
        return paciente;
    }

    public void setPaciente(PacienteRestV12DTO paciente) {
        this.paciente = paciente;
    }

    public Date getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(Date dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }
}
