package br.com.celk.bo.esus.interfaces.facade;

import br.com.celk.esus.RelatorioCondicoesMoradiaDetalhadoDTOParam;
import br.com.celk.provider.ejb.EJBLocation;
import br.com.celk.unidadesaude.esus.relatorios.*;
import br.com.celk.unidadesaude.esus.relatorios.dto.*;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoPacientesSemAderenciaDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRiscoCardiovascularDTOParam;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryPlanejamentoVisitasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.celk.bo.esus.EsusReportBO")
public interface EsusReportFacade {

    public DataReport relacaoPlanejamentoVisita(QueryPlanejamentoVisitasDTOParam param) throws ReportException;

    public DataReport relatorioVisitaDomiciliarACS(RelatorioVisitaDomiciliarAcsDTOParam param) throws ReportException;

    public DataReport relacaoGestantesSemAderenciaIndicadores(RelacaoGestantesSemAderenciaIndicadoresDTOParam param) throws ReportException;

    public DataReport relatorioProcedimentos(RelatorioProcedimentosDTOParam param) throws ReportException;

    public DataReport relatorioAtendimentos(RelatorioAtendimentosDTOParam param) throws ReportException;

    public DataReport relatorioFichasAtendimentoDomiciliar(RelatorioFichasAtendimentoDomiciliarDTOParam param) throws ReportException;

    public DataReport relatorioFichasAtendimentoDomiciliarDetalhado(RelatorioFichasAtendimentoDomiciliarDetalhadoDTOParam param) throws ReportException;
    
    public DataReport relatorioCondicoesMoradiaDetalhado(RelatorioCondicoesMoradiaDetalhadoDTOParam param) throws ReportException;

    public DataReport relatorioCondicaoMoradiaResumido(RelatorioCondicaoMoradiaResumidoDTOParam param) throws ReportException;

    public DataReport relatorioProgramaMaisMedicos(RelatorioProgramaMaisMedicosDTOParam param) throws ReportException;

    public DataReport relatorioAcompanhamento(RelatorioAcompanhamentoDTOParam param) throws ReportException;

    public DataReport relacaoHipertensosSemAderencia(RelacaoPacientesSemAderenciaDTOParam param) throws ReportException;

    public DataReport relacaoDiabeticosSemAderencia(RelacaoPacientesSemAderenciaDTOParam param) throws ReportException;

    public DataReport relacaoCriancasSemAderencia(RelacaoPacientesSemAderenciaDTOParam param) throws ReportException;

    public DataReport relacaoMulheresSemAderencia(RelacaoPacientesSemAderenciaDTOParam param) throws ReportException;

    public DataReport relatorioRiscoCardiovascular(RelatorioRiscoCardiovascularDTOParam param) throws ReportException;

    public DataReport relatorioMonitoramento(RelatorioMonitoramentoDTOParam param) throws ReportException;

    public DataReport relatorioCadastroIndividual(RelatorioCadastroIndividualDTOParam param) throws ReportException;

    public DataReport relacaoProducao(RelacaoProducaoDTOParam param) throws ReportException;

    public void enviarImpressaoPlanejamentoVisita(QueryPlanejamentoVisitasDTOParam param) throws DAOException, ValidacaoException;

    public DataReport relatorioEstratificacaoRiscoIndividual(RelatorioEstratificacaoRiscoIndividualDTOParam param) throws ReportException;

    public DataReport relatorioEstratificacaoRisco(RelatorioEstratificacaoRiscoFamiliarDTOParam param) throws ReportException;

}
