package br.com.celk.bo.emprestimo.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ConsultaDevolucaoEmprestimoDTOParam implements Serializable {
    
    private String pacienteEstabelecimento;
    private Produto produto;
    private DatePeriod periodo;
    private Long codigo;    
    private Long situacao;    
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public String getPacienteEstabelecimento() {
        return pacienteEstabelecimento;
    }

    public void setPacienteEstabelecimento(String pacienteEstabelecimento) {
        this.pacienteEstabelecimento = pacienteEstabelecimento;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }
}
