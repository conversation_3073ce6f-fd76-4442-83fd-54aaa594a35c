package br.com.celk.bo.service.rest.fiscalnarua;

import java.io.Serializable;
import java.util.Date;

public class RegistroInspecaoRoteiroItemPerguntaRespostaFruDTO implements Serializable {

    private Long idRoteiroPerguntaCopia;
    private String dsPergunta;
    private String dsLeiArtigo;
    private String dsProvidencia;
    private Long resposta;
    private Long classificacao;
    private String observacao;
    private Long idUsuario;
    private Date dtRegistro;


    public String getDsLeiArtigo() {
        return dsLeiArtigo;
    }

    public void setDsLeiArtigo(String dsLeiArtigo) {
        this.dsLeiArtigo = dsLeiArtigo;
    }

    public String getDsProvidencia() {
        return dsProvidencia;
    }

    public void setDsProvidencia(String dsProvidencia) {
        this.dsProvidencia = dsProvidencia;
    }

    public Long getIdRoteiroPerguntaCopia() {
        return idRoteiroPerguntaCopia;
    }







    public void setIdRoteiroPerguntaCopia(Long idRoteiroPerguntaCopia) {
        this.idRoteiroPerguntaCopia = idRoteiroPerguntaCopia;
    }

    public String getDsPergunta() {
        return dsPergunta;
    }

    public void setDsPergunta(String dsPergunta) {
        this.dsPergunta = dsPergunta;
    }

    public Long getResposta() {
        return resposta;
    }

    public void setResposta(Long resposta) {
        this.resposta = resposta;
    }

    public Long getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(Long classificacao) {
        this.classificacao = classificacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Long getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Long idUsuario) {
        this.idUsuario = idUsuario;
    }

    public Date getDtRegistro() {
        return dtRegistro;
    }

    public void setDtRegistro(Date dtRegistro) {
        this.dtRegistro = dtRegistro;
    }
}
