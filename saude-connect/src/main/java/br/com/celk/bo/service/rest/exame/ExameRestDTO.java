package br.com.celk.bo.service.rest.exame;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExameRestDTO implements Serializable{
    private Long codigoExame;
    private Date dataCadastro;
    private ConvenioRestDTO convenio;
    private PacienteRestDTO paciente;
    private EmpresaRestDTO unidade;
    private ProfissionalRestDTO profissional;
    private List<ExameItemRestDTO> itens;

    public ProfissionalRestDTO getProfissional() {
        return profissional;
    }

    public void setProfissional(ProfissionalRestDTO profissional) {
        this.profissional = profissional;
    }

    public EmpresaRestDTO getUnidade() {
        return unidade;
    }

    public void setUnidade(EmpresaRestDTO unidade) {
        this.unidade = unidade;
    }

    public ConvenioRestDTO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioRestDTO convenio) {
        this.convenio = convenio;
    }

    public PacienteRestDTO getPaciente() {
        return paciente;
    }

    public void setPaciente(PacienteRestDTO paciente) {
        this.paciente = paciente;
    }

    public List<ExameItemRestDTO> getItens() {
        return itens;
    }

    public void setItens(List<ExameItemRestDTO> itens) {
        this.itens = itens;
    }

    public Long getCodigoExame() {
        return codigoExame;
    }

    public void setCodigoExame(Long codigoExame) {
        this.codigoExame = codigoExame;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
    
}
