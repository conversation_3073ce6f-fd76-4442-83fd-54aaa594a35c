package br.com.celk.bo.service.rest.fiscalnarua;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;


public class AutoInfracaoFiscalNaRuaRestDTO implements Serializable {

    private UUID idApp;
    private Long idRequerimentoVigilancia;
    private Date dataInfracao;
    private String dsEnquadramentoLegal;
    private String nomeResponsavel;
    private Date dataCadastro;
    private List<AutoInfracaoProvidenciaFruDTO> listProvidencias;
    private List<String> emails;
    private List<Long> idFiscaisList;
    private List<AutoInfracaoEloMidiaFruDTO> listEloMidia;
    private UUID inspecaoApp;
    private Long idUsuarioCadastro;
    private Long IdEstabelecimento;
    private Long idVigilanciaPessoa;
    private UUID estabelecimentoApp;
    private UUID vigilanciaPessoaApp;
    private Long autuadoRecusouAuto;
    private Long status;

    public Long getIdRequerimentoVigilancia() {
        return idRequerimentoVigilancia;
    }

    public void setIdRequerimentoVigilancia(Long idRequerimentoVigilancia) {
        this.idRequerimentoVigilancia = idRequerimentoVigilancia;
    }

    public Date getDataInfracao() {
        return dataInfracao;
    }

    public void setDataInfracao(Date dataInfracao) {
        this.dataInfracao = dataInfracao;
    }

    public String getDsEnquadramentoLegal() {
        return dsEnquadramentoLegal;
    }

    public void setDsEnquadramentoLegal(String dsEnquadramentoLegal) {
        this.dsEnquadramentoLegal = dsEnquadramentoLegal;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public List<AutoInfracaoProvidenciaFruDTO> getListProvidencias() {
        return listProvidencias;
    }

    public void setListProvidencias(List<AutoInfracaoProvidenciaFruDTO> listProvidencias) {
        this.listProvidencias = listProvidencias;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<Long> getIdFiscaisList() {
        return idFiscaisList;
    }

    public void setIdFiscaisList(List<Long> idFiscaisList) {
        this.idFiscaisList = idFiscaisList;
    }

    public List<AutoInfracaoEloMidiaFruDTO> getListEloMidia() {
        return listEloMidia;
    }

    public void setListEloMidia(List<AutoInfracaoEloMidiaFruDTO> listEloMidia) {
        this.listEloMidia = listEloMidia;
    }

    public Long getIdUsuarioCadastro() {
        return idUsuarioCadastro;
    }

    public void setIdUsuarioCadastro(Long idUsuarioCadastro) {
        this.idUsuarioCadastro = idUsuarioCadastro;
    }

    public Long getIdEstabelecimento() {
        return IdEstabelecimento;
    }

    public void setIdEstabelecimento(Long idEstabelecimento) {
        IdEstabelecimento = idEstabelecimento;
    }

    public Long getIdVigilanciaPessoa() {
        return idVigilanciaPessoa;
    }

    public void setIdVigilanciaPessoa(Long idVigilanciaPessoa) {
        this.idVigilanciaPessoa = idVigilanciaPessoa;
    }

    public Long getAutuadoRecusouAuto() {
        return autuadoRecusouAuto;
    }

    public void setAutuadoRecusouAuto(Long autuadoRecusouAuto) {
        this.autuadoRecusouAuto = autuadoRecusouAuto;
    }

    public UUID getInspecaoApp() {
        return inspecaoApp;
    }

    public void setInspecaoApp(UUID inspecaoApp) {
        this.inspecaoApp = inspecaoApp;
    }

    public UUID getIdApp() {
        return idApp;
    }

    public void setIdApp(UUID idApp) {
        this.idApp = idApp;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public UUID getEstabelecimentoApp() {
        return estabelecimentoApp;
    }

    public void setEstabelecimentoApp(UUID estabelecimentoApp) {
        this.estabelecimentoApp = estabelecimentoApp;
    }

    public UUID getVigilanciaPessoaApp() {
        return vigilanciaPessoaApp;
    }

    public void setVigilanciaPessoaApp(UUID vigilanciaPessoaApp) {
        this.vigilanciaPessoaApp = vigilanciaPessoaApp;
    }
}