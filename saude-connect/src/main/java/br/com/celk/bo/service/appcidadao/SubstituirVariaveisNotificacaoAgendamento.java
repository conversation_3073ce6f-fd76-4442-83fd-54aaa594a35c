package br.com.celk.bo.service.appcidadao;

import br.com.celk.bo.service.agendamento.SubstituirVariaveisAgendamento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;

public class SubstituirVariaveisNotificacaoAgendamento extends SubstituirVariaveisAgendamento {

    public SubstituirVariaveisNotificacaoAgendamento() {
    }

    public SubstituirVariaveisNotificacaoAgendamento(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        super(agendaGradeAtendimentoHorario);
    }

    public String substituirVariaveis(String mensagem) {
        if (mensagem != null && mensagem.length() > 0) {
            if (getAgendaGradeAtendimentoHorario() != null) {
                super.substituirVariaveis();
            }
        }
        setMensagem(getSubstituirVariaveis().build().substituir(mensagem));
        return getMensagem();
    }
}
