package br.com.celk.bo.emprestimo.interfaces.facade;

import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoDevolucaoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.dto.RelacaoEmprestimoDTOParam;
import br.com.celk.bo.emprestimo.interfaces.dto.ResumoDevolucoesDTOParam;
import br.com.celk.bo.emprestimo.interfaces.dto.ResumoEmprestimosDTOParam;
import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.interfaces.FacadeBO;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.celk.report.emprestimo.EmprestimoReportBO")
public interface EmprestimoReportFacade extends FacadeBO {

    public DataReport relatorioRelacaoEmprestimo(RelacaoEmprestimoDTOParam param) throws ReportException;

    public DataReport relatorioRelacaoDevolucoes(RelacaoDevolucaoDTOParam param) throws ReportException;

    public DataReport comprovanteEmprestimo(LancamentoEmprestimo lancamentoEmprestimo) throws ReportException;

    public DataReport relatorioResumoEmprestimos(ResumoEmprestimosDTOParam param) throws ReportException;

    public DataReport relatorioResumoDevolucoes(ResumoDevolucoesDTOParam param) throws ReportException;

    public DataReport comprovanteDevolucao(DevolucaoEmprestimo devolucaoEmprestimo) throws ReportException;
}
