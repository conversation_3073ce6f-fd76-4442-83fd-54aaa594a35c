package br.com.celk.bo.service.rest.assinaturadigital;

import br.com.celk.report.HtmlFileUtil;
import br.com.celk.report.HtmlReport;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.util.Date;
import java.util.UUID;

public abstract class AbstractAssinaturaDigitalHelper implements Serializable {

    private static byte[] readFileToByteArray(File file) throws IOException {
        try (InputStream inputStream = new FileInputStream(file);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        }
    }

    public boolean isAssinaturaEnabled() {
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("habilitaAssinaturaDigital"));
        } catch (DAOException e) {
            return false;
        }
    }

    public Long carregarTempoExpiracao() {
        return new AbstractAssinaturaDigitalService().carregarTempoDuracaoLink();
    }

    public DocumentoAssinado assinarDocumento(DataReport dataReport, AssinaturaDigitalDTO assinaturaDigitalDTO) throws ValidacaoException, JRException, IOException, DAOException {
        File file = File.createTempFile(String.valueOf(UUID.randomUUID()), ".pdf");
        JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
        return enviarDocumentoParaAssinatura(file, assinaturaDigitalDTO);
    }

    public DocumentoAssinado assinarDocumento(HtmlReport dataReport, AssinaturaDigitalDTO assinaturaDigitalDTO) throws ValidacaoException, JRException, IOException, DAOException {
        File file = HtmlFileUtil.resolveHtmlReport(dataReport);
        return enviarDocumentoParaAssinatura(file, assinaturaDigitalDTO);
    }

    protected abstract DocumentoAssinado enviarDocumentoParaAssinatura(File file, AssinaturaDigitalDTO assinaturaDigitalDTO) throws ValidacaoException, DAOException;

    public DocumentoAssinado carregarDocumentoAssinado(Long codigo) {
        return LoadManager.getInstance(DocumentoAssinado.class)
                .addProperties(new HQLProperties(DocumentoAssinado.class).getProperties())
                .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(DocumentoAssinado.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL)).getProperties())
                .addProperties(new HQLProperties(Usuario.class, VOUtils.montarPath(DocumentoAssinado.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_USUARIO)).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(DocumentoAssinado.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, DocumentoAssinado.PROP_GERENCIADOR_ARQUIVO_ORIGINAL).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, DocumentoAssinado.PROP_GERENCIADOR_ARQUIVO_ASSINADO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(DocumentoAssinado.PROP_CODIGO, codigo))
                .start().getVO();
    }

    public DocumentoAssinado salvarDocumentoAssinado(File file, Atendimento atendimento, DocumentoAssinado documentoAssinado, DocumentoAssinado.OrigemTipoDocumento origemTipoDocumento, String tipoDocumento) throws DAOException, ValidacaoException {
        Date date = new Date();
        GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(file, GerenciadorArquivo.OrigemArquivo.DOCUMENTO_ASSINADO.value());

        if (documentoAssinado == null) {
            documentoAssinado = new DocumentoAssinado();
        }
        if (documentoAssinado.getAtendimento() == null) {
            documentoAssinado.setAtendimento(atendimento);
        } if (documentoAssinado.getDataCadastro() == null) {
            documentoAssinado.setDataCadastro(date);
        }
        documentoAssinado.setTipoDocumentoOrigem(origemTipoDocumento.value());
        documentoAssinado.setFlagAssinado(RepositoryComponentDefault.NAO_LONG);
        documentoAssinado.setGerenciadorArquivoOriginal(gerenciadorArquivo);
        documentoAssinado.setTipoArquivo(tipoDocumento);

        return documentoAssinado;
    }

    public File decodificarDocumento(String text) throws ValidacaoException {
        // Decodificar a string Base64
        byte[] pdfBytes = Base64.decodeBase64(text);

        // Criar um arquivo temporário
        File tempFile = null;
        try {
            tempFile = File.createTempFile("documento_" + UUID.randomUUID(), ".pdf");
            tempFile.deleteOnExit(); // O arquivo será deletado quando o programa terminar

            // Escrever os bytes no arquivo temporário
            try (OutputStream outputStream = new FileOutputStream(tempFile)) {
                outputStream.write(pdfBytes);
            } catch (IOException e) {
                throw new ValidacaoException("Não foi possível converter em arquivo", e);
            }
        } catch (IOException e) {
            throw new ValidacaoException("Não foi possível criar um arquivo temporário", e);
        }
        return tempFile;
    }

    //endregion

    public DocumentoAssinado carregarDocumento(String uuid) throws DAOException {
        return LoadManager.getInstance(DocumentoAssinado.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DocumentoAssinado.PROP_UUID, uuid))
                .start().getVO();
    }

    public DocumentoAssinado atualizarDocumentoAssinado(DocumentoAssinado documentoAssinado, File arquivo) throws DAOException, ValidacaoException, IOException {

        String caminhoArquivo = "documento_" + UUID.randomUUID() + ".pdf";
        GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(arquivo, GerenciadorArquivo.OrigemArquivo.DOCUMENTO_ASSINADO.value(), caminhoArquivo);

        documentoAssinado.setGerenciadorArquivoAssinado(gerenciadorArquivo);
        BOFactory.save(documentoAssinado);
        return documentoAssinado;
    }

    public boolean isDocumentoAssinado(DocumentoAssinado documentoAssinado) {
        if (documentoAssinado != null)
            return RepositoryComponentDefault.SIM_LONG.equals(documentoAssinado.getFlagAssinado());
        return false;
    }

    public boolean isLinkExpirado(DocumentoAssinado documentoAssinado) {
        Long linkRetornoExpiration = carregarTempoExpiracao();
        return linkRetornoExpiration < DataUtil.getMinutosDiferenca(documentoAssinado.getDataLinkRetorno(), new Date());
    }
}
