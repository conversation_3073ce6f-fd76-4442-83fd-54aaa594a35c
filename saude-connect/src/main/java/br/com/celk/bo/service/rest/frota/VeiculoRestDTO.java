/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.bo.service.rest.frota;

import com.google.common.base.Preconditions;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class VeiculoRestDTO implements Serializable{
    private Long codigo;
    private String descricao;
    private String placa;
    private String fabricante;
    private String km;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getFabricante() {
        return fabricante;
    }

    public void setFabricante(String fabricante) {
        this.fabricante = fabricante;
    }

    protected VeiculoRestDTO(){}
    
    private VeiculoRestDTO(Long codigo, String descricao, String placa, String fabricante, String km) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.placa = placa;
        this.fabricante = fabricante;
        this.km = km;
    }
    
    public static VeiculoRestDTO of(Long codigo){
        Preconditions.checkNotNull(codigo);
        
        VeiculoRestDTO instance = new VeiculoRestDTO();
        
        instance.setCodigo(codigo);
        
        return instance;
    }
    
    
    public static VeiculoRestDTO of(Long codigo, String descricao, String placa, String fabricante, String km){
        
        Preconditions.checkNotNull(codigo);
        Preconditions.checkNotNull(descricao);
        
        return new VeiculoRestDTO(codigo, descricao, placa, fabricante, km);
    }

    public String getKm() {
        return km;
    }

    public void setKm(String km) {
        this.km = km;
    }
}
