package br.com.celk.bo.emprestimo.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioComprovanteEmprestimoDTO implements Serializable {
    
    private LancamentoEmprestimoItem lancamentoEmprestimoItem;
    private UsuarioCadsus usuarioCadsus;
    private Empresa empresaEmprestimo;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;

    public LancamentoEmprestimoItem getLancamentoEmprestimoItem() {
        return lancamentoEmprestimoItem;
    }

    public void setLancamentoEmprestimoItem(LancamentoEmprestimoItem lancamentoEmprestimoItem) {
        this.lancamentoEmprestimoItem = lancamentoEmprestimoItem;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Empresa getEmpresaEmprestimo() {
        return empresaEmprestimo;
    }

    public void setEmpresaEmprestimo(Empresa empresaEmprestimo) {
        this.empresaEmprestimo = empresaEmprestimo;
    }
    
}
