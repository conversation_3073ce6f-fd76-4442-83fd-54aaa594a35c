package br.com.celk.bo.agenda.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ManutencaoAgendaDiarioDTOParam implements Serializable {
    
    private Long codigoAgenda;
    private Long domingo;
    private Long segundaFeira;
    private Long tercaFeira;
    private Long quartaFeira;
    private Long quintaFeira;
    private Long sextaFeira;
    private Long sabado;
    private DatePeriod periodo;
    private Date horario;
    private TipoAtendimentoAgenda TipoAtendimentoAgenda;
    private boolean count;

    public Long getCodigoAgenda() {
        return codigoAgenda;
    }

    public void setCodigoAgenda(Long codigoAgenda) {
        this.codigoAgenda = codigoAgenda;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Date getHorario() {
        return horario;
    }

    public void setHorario(Date horario) {
        this.horario = horario;
    }

    public TipoAtendimentoAgenda getTipoAtendimentoAgenda() {
        return TipoAtendimentoAgenda;
    }

    public void setTipoAtendimentoAgenda(TipoAtendimentoAgenda TipoAtendimentoAgenda) {
        this.TipoAtendimentoAgenda = TipoAtendimentoAgenda;
    }

    public Long getDomingo() {
        return domingo;
    }

    public void setDomingo(Long domingo) {
        this.domingo = domingo;
    }

    public Long getSegundaFeira() {
        return segundaFeira;
    }

    public void setSegundaFeira(Long segundaFeira) {
        this.segundaFeira = segundaFeira;
    }

    public Long getTercaFeira() {
        return tercaFeira;
    }

    public void setTercaFeira(Long tercaFeira) {
        this.tercaFeira = tercaFeira;
    }

    public Long getQuartaFeira() {
        return quartaFeira;
    }

    public void setQuartaFeira(Long quartaFeira) {
        this.quartaFeira = quartaFeira;
    }

    public Long getQuintaFeira() {
        return quintaFeira;
    }

    public void setQuintaFeira(Long quintaFeira) {
        this.quintaFeira = quintaFeira;
    }

    public Long getSextaFeira() {
        return sextaFeira;
    }

    public void setSextaFeira(Long sextaFeira) {
        this.sextaFeira = sextaFeira;
    }

    public Long getSabado() {
        return sabado;
    }

    public void setSabado(Long sabado) {
        this.sabado = sabado;
    }
    
    public List<Integer> getDiaSemanaSelecionadoList(){
        List<Integer> list = new ArrayList<>();
        Integer i = 0;
        if (RepositoryComponentDefault.SIM_LONG.equals(getDomingo())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 1;
        if (RepositoryComponentDefault.SIM_LONG.equals(getSegundaFeira())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 2;
        if (RepositoryComponentDefault.SIM_LONG.equals(getTercaFeira())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 3;
        if (RepositoryComponentDefault.SIM_LONG.equals(getQuartaFeira())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 4;
        if (RepositoryComponentDefault.SIM_LONG.equals(getQuintaFeira())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 5;
        if (RepositoryComponentDefault.SIM_LONG.equals(getSextaFeira())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        i = 6;
        if (RepositoryComponentDefault.SIM_LONG.equals(getSabado())) {
            list.add(i);
        } else {
            list.remove(i);
        }
        return list;
    }

    public boolean isCount() {
        return count;
    }

    public void setCount(boolean count) {
        this.count = count;
    }
    
}
