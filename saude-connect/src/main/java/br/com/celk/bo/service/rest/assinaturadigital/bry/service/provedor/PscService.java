package br.com.celk.bo.service.rest.assinaturadigital.bry.service.provedor;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

public abstract class PscService {
    protected PscAuthorizeRequestParam dto;

    public PscService() {
    }

    public PscService(PscAuthorizeRequestParam dto) {
        this.dto = dto;
    }

    public static PscService getInstance(Usuario usuario) throws ValidacaoException {
        if (Usuario.ProvedorCertificado.SYNGULAR.value().equals(usuario.getProvedorCertificado())) {
            return new SyngularBryService();
        }

        if (Usuario.ProvedorCertificado.VIDAAS.value().equals(usuario.getProvedorCertificado())) {
            return new VidaasBryService();
        }
        throw new ValidacaoException("Provedor de certificado não carregado ou configurado");
    }

    public abstract String getAuthUrl() throws ValidacaoException;

    public abstract String generateToken();

    public void setDto(PscAuthorizeRequestParam dto) {
        this.dto = dto;
    }
}
