package br.com.celk.bo.service.rest.assinaturadigital;

public enum Assinadores {
    GLOBALTECH("0", "Globaltech"),
    BRY("1", "BRY");

    private final String id;
    private final String descricao;

    Assinadores(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }
}
