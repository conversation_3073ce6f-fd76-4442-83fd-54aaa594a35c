package br.com.celk.bo.service.rest.assinaturadigital.bry.dto.autenticacao;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import com.fasterxml.jackson.annotation.JsonProperty;

public class TokenServiceBry {

    private final String ENDPOINT = "/token-service/jwt";

    @JsonProperty("grant_type")
    private String grantType;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("client_secret")
    private String clientSecret;

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getAuthEndpoint() throws ValidacaoException {
        try {
            String url = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("urlAutenticacaoAssinaturaDigital");

            return url + ENDPOINT;
        } catch (DAOException e) {
            throw new ValidacaoException(e);
        }
    }
}
