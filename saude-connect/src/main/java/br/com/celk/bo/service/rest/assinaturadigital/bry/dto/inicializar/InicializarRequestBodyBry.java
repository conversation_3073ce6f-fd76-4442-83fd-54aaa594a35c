package br.com.celk.bo.service.rest.assinaturadigital.bry.dto.inicializar;

import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoImagem;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoQrCode;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.config.ConfiguracaoTexto;
import br.com.celk.bo.service.rest.assinaturadigital.bry.util.ConfigUtil;
import br.com.celk.bo.service.rest.assinaturadigital.bry.util.ConfiguracaoTextoSerializer;
import br.com.celk.bo.service.rest.assinaturadigital.bry.util.DocumentoSerializer;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InicializarRequestBodyBry {

    @JsonSerialize(using = DocumentoSerializer.class)
    @JsonProperty("documento")
    private List<File> documentos;

    @JsonProperty("dados_inicializar")
    private DadosInicializarBry dadosInicializarBry;

    @JsonProperty("configuracao_imagem")
    private ConfiguracaoImagem configuracaoImagem;

    @JsonProperty("configuracao_qrcode")
    private ConfiguracaoQrCode configuracaoQrCode;

    @JsonProperty("configuracao_texto")
    @JsonSerialize(using = ConfiguracaoTextoSerializer.class)
    private ConfiguracaoTexto configuracaoTexto;

    @JsonProperty("metadados")
    private String metadados;

    public List<File> getDocumentos() {
        return documentos;
    }

    public void setDocumentos(List<File> documentos) {
        this.documentos = documentos;
    }

    public DadosInicializarBry getDadosInicializar() {
        return dadosInicializarBry;
    }

    public void setDadosInicializar(DadosInicializarBry dadosInicializarBry) {
        this.dadosInicializarBry = dadosInicializarBry;
    }

    public ConfiguracaoImagem getConfiguracaoImagem() {
        return configuracaoImagem;
    }

    public void setConfiguracaoImagem(ConfiguracaoImagem configuracaoImagem) {
        this.configuracaoImagem = configuracaoImagem;
    }

    public ConfiguracaoQrCode getConfiguracaoQrCode() {
        return configuracaoQrCode;
    }

    public void setConfiguracaoQrCode(ConfiguracaoQrCode configuracaoQrCode) {
        this.configuracaoQrCode = configuracaoQrCode;
    }

    public ConfiguracaoTexto getConfiguracaoTexto() {
        return configuracaoTexto;
    }

    public void setConfiguracaoTexto(ConfiguracaoTexto configuracaoTexto) {
        this.configuracaoTexto = configuracaoTexto;
    }

    public DadosInicializarBry getDadosInicializarBry() {
        return dadosInicializarBry;
    }

    public void setDadosInicializarBry(DadosInicializarBry dadosInicializarBry) {
        this.dadosInicializarBry = dadosInicializarBry;
    }

    public String getMetadados() {
        return metadados;
    }

    public void setMetadados(String metadados) {
        this.metadados = metadados;
    }

    public void setMetadados(Usuario usuario, Atendimento atendimento, Long tipoDocumento) throws JsonProcessingException {
        Profissional profissional = LoadManager.getInstance(Profissional.class)
                .addProperties(new HQLProperties(Profissional.class).getProperties())
                .addProperties(new HQLProperties(OrgaoEmissor.class, Profissional.PROP_CONSELHO_CLASSE).getProperties())
                .setId(usuario.getProfissional().getCodigo())
                .start().getVO();

        Map<String, String> map = ConfigUtil.addMetadadosByDocumento(new HashMap<>(), tipoDocumento);

        if (profissional != null && profissional.getConselhoClasse() != null) {
            Atendimento at = LoadManager.getInstance(Atendimento.class)
                    .addProperties(new HQLProperties(Atendimento.class).getProperties())
                    .addProperties(new HQLProperties(TabelaCbo.class, Atendimento.PROP_TABELA_CBO).getProperties())
                    .setId(atendimento.getCodigo())
                    .start().getVO();
            String metadadosPrefix = profissional.getConselhoClasse().getOid();

            map.put(metadadosPrefix + ".1", profissional.getNumeroRegistro());
            map.put(metadadosPrefix + ".2", profissional.getUnidadeFederacaoConselhoRegistro());
            map.put(metadadosPrefix + ".3", at.getTabelaCbo().getDescricao());

            ObjectMapper objectMapper = new ObjectMapper();
            this.metadados = objectMapper.writeValueAsString(map);
        }
    }
}
