package br.com.celk.bo.agenda.interfaces.dto;

import br.com.ksisolucoes.vo.agendamento.AgendaOcorrencia.TipoOcorrencia;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroManutencaoAgendaDiarioDTO implements Serializable {
    
    private List<ManutencaoAgendaDiarioDTO> horariosSelecionadosDTOList = new ArrayList<>();
    private Long vagas;
    private Long codigoAgenda;
    private Long tempoMedio;
    private String motivo;
    private TipoOcorrencia tipoOcorrencia;
    private TipoAtendimentoAgenda tipoAtendimentoAgenda;
    private Profissional profissionalAtual;
    private Profissional profissionalNovo;
    private Empresa empresaAgenda;
    private TipoProcedimento tipoProcedimentoAgenda;
    private Date horaInicial;

    public List<ManutencaoAgendaDiarioDTO> getHorariosSelecionadosDTOList() {
        return horariosSelecionadosDTOList;
    }

    public void setHorariosSelecionadosDTOList(List<ManutencaoAgendaDiarioDTO> horariosSelecionadosDTOList) {
        this.horariosSelecionadosDTOList = horariosSelecionadosDTOList;
    }

    public Long getVagas() {
        return vagas;
    }

    public void setVagas(Long vagas) {
        this.vagas = vagas;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public TipoOcorrencia getTipoOcorrencia() {
        return tipoOcorrencia;
    }

    public void setTipoOcorrencia(TipoOcorrencia tipoOcorrencia) {
        this.tipoOcorrencia = tipoOcorrencia;
    }

    public Long getCodigoAgenda() {
        return codigoAgenda;
    }

    public void setCodigoAgenda(Long codigoAgenda) {
        this.codigoAgenda = codigoAgenda;
    }

    public TipoAtendimentoAgenda getTipoAtendimentoAgenda() {
        return tipoAtendimentoAgenda;
    }

    public void setTipoAtendimentoAgenda(TipoAtendimentoAgenda tipoAtendimentoAgenda) {
        this.tipoAtendimentoAgenda = tipoAtendimentoAgenda;
    }

    public Long getTempoMedio() {
        return tempoMedio;
    }

    public void setTempoMedio(Long tempoMedio) {
        this.tempoMedio = tempoMedio;
    }

    public Profissional getProfissionalAtual() {
        return profissionalAtual;
    }

    public void setProfissionalAtual(Profissional profissionalAtual) {
        this.profissionalAtual = profissionalAtual;
    }

    public Profissional getProfissionalNovo() {
        return profissionalNovo;
    }

    public void setProfissionalNovo(Profissional profissionalNovo) {
        this.profissionalNovo = profissionalNovo;
    }

    public Empresa getEmpresaAgenda() {
        return empresaAgenda;
    }

    public void setEmpresaAgenda(Empresa empresaAgenda) {
        this.empresaAgenda = empresaAgenda;
    }
        
    public Date getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(Date horaInicial) {
        this.horaInicial = horaInicial;
    }

    public TipoProcedimento getTipoProcedimentoAgenda() {
        return tipoProcedimentoAgenda;
    }

    public void setTipoProcedimentoAgenda(TipoProcedimento tipoProcedimentoAgenda) {
        this.tipoProcedimentoAgenda = tipoProcedimentoAgenda;
    }
}