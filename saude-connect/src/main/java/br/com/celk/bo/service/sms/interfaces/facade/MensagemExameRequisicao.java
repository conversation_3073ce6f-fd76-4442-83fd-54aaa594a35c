package br.com.celk.bo.service.sms.interfaces.facade;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;

public class MensagemExameRequisicao implements ReplaceVariablesI {

    ExameRequisicao exameRequisicao;

    @Override
    public String mensagemTratada(String mensagem, Object obj) {
        exameRequisicao = (ExameRequisicao) obj;

        if (exameRequisicao != null) {
            mensagem = mensagem.replaceAll("\\$data\\$", Data.formatar(exameRequisicao.getExame().getDataSolicitacao()));
            mensagem = mensagem.replaceAll("\\$nome_completo_paciente\\$", exameRequisicao.getExame().getUsuarioCadsus().getNome());
            return mensagem;
        }
        return mensagem;
    }
}
