package br.com.celk.bo.geral;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Grupo;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class UsuarioDTOParam implements Serializable{

    private String nome;
    private String login;
    private Grupo grupo;
    private Empresa empresa;
    private String propSort;
    private String status;
    private boolean ascending;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public Grupo getGrupo() {
        return grupo;
    }

    public void setGrupo(Grupo grupo) {
        this.grupo = grupo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }
}
