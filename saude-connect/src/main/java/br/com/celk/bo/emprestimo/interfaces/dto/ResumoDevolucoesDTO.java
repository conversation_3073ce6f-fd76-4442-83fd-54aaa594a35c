package br.com.celk.bo.emprestimo.interfaces.dto;

import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ResumoDevolucoesDTO implements Serializable {

    private DevolucaoEmprestimoItem devolucaoEmprestimoItem;
    private String signatario;
    private Double quantidade;

    public DevolucaoEmprestimoItem getDevolucaoEmprestimoItem() {
        return devolucaoEmprestimoItem;
    }

    public void setDevolucaoEmprestimoItem(DevolucaoEmprestimoItem devolucaoEmprestimoItem) {
        this.devolucaoEmprestimoItem = devolucaoEmprestimoItem;
    }

    public String getSignatario() {
        return signatario;
    }

    public void setSignatario(String signatario) {
        this.signatario = signatario;
    }

    public Double getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
}
