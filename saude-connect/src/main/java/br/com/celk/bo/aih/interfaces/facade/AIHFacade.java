/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.bo.aih.interfaces.facade;

import br.com.celk.aih.dto.AIHConsultaImpressaoDTO;
import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.celk.bo.aih.AIHBO")
public interface AIHFacade {

    public List<Atendimento> consultarAtendimentos(AutorizacaoInternacaoHospitalarDTO dto) throws DAOException, ValidacaoException;

    public List<AIHConsultaImpressaoDTO> consultaSolicitacaoMudancaProcedimentoAIH(Long aihCodigo) throws DAOException, ValidacaoException;

    public List<AIHConsultaImpressaoDTO> consultaSolicitacaoProcedimentosAIH(Long aihCodigo) throws DAOException, ValidacaoException;
    
    public List<ContaPaciente> consultarContaPacienteCadastroAih(AutorizacaoInternacaoHospitalarDTO dto) throws DAOException, ValidacaoException;
    
    public Aih salvarAih(Aih aih, Atendimento atendimento) throws DAOException, ValidacaoException;

}
