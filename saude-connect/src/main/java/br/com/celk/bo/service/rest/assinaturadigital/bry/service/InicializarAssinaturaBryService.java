package br.com.celk.bo.service.rest.assinaturadigital.bry.service;

import br.com.celk.bo.service.rest.assinaturadigital.AssinaturaDigitalDTO;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.inicializar.DadosInicializarBry;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.inicializar.InicializarRequestBodyBry;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.inicializar.response.ResponseInicializarBry;
import br.com.celk.bo.service.rest.assinaturadigital.bry.enums.AlgoritmoHash;
import br.com.celk.bo.service.rest.assinaturadigital.bry.enums.TipoPerfil;
import br.com.celk.bo.service.rest.assinaturadigital.bry.enums.TipoRestricao;
import br.com.celk.bo.service.rest.assinaturadigital.bry.util.ConfigUtil;
import br.com.celk.util.service.rest.WebserviceResponse;
import br.com.celk.util.service.rest.WebserviceUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;

public class InicializarAssinaturaBryService {

    private static final String ENDPOINT = "/fw/v1/pdf/pkcs1/assinaturas/acoes/inicializar";

    private WebserviceResponse inicializarAssinaturaService(String baseUrl, Map<String, String> headers, File file, AssinaturaDigitalDTO dto) throws ValidacaoException, IOException, IllegalAccessException {
        DadosInicializarBry dadosInicializarBry = new DadosInicializarBry();
        dadosInicializarBry.setCertificado(dto.getUsuario().getPublicKeyCertificate());
        dadosInicializarBry.setAlgoritmoHash(AlgoritmoHash.SHA256);
        dadosInicializarBry.setFormatoDadosEntrada("Base64");
        dadosInicializarBry.setFormatoDadosSaida("Base64");
        dadosInicializarBry.setContato(dto.getAtendimento().getProfissional().getEmail());
        dadosInicializarBry.setLocal("");
        dadosInicializarBry.setNonces(Collections.singletonList("0"));
        dadosInicializarBry.setPerfil(TipoPerfil.TIMESTAMP);
        dadosInicializarBry.setRestructure(false);
        dadosInicializarBry.setRazao("");
        dadosInicializarBry.setTipoRestricao(TipoRestricao.NENHUMA_RESTRICAO);

        InicializarRequestBodyBry body = new InicializarRequestBodyBry();
        body.setDocumentos(Collections.singletonList(file));
        body.setMetadados(dto.getUsuario(), dto.getAtendimento(), dto.getOrigemTipoDocumento().value());
        body.setDadosInicializar(dadosInicializarBry);
        body.setConfiguracaoTexto(ConfigUtil.getDefaultTextoConfig());
        body.setConfiguracaoImagem(ConfigUtil.getConfiguracaoByTipoDocumento(dto.getOrigemTipoDocumento().value()));
        body.setConfiguracaoQrCode(ConfigUtil.getDefaultQrCodeConfig());

        return new WebserviceUtil().postFormDataRequest(baseUrl + ENDPOINT, body, headers);
    }

    public ResponseInicializarBry inicializarAssinatura(String baseUrl, Map<String, String> headers, File file, AssinaturaDigitalDTO dto) throws ValidacaoException, IOException, IllegalAccessException {
        ObjectMapper objectMapper = new ObjectMapper();
        WebserviceResponse response = inicializarAssinaturaService(baseUrl, headers, file, dto);
        return objectMapper.readValue(response.getResponseMessage(), ResponseInicializarBry.class);
    }
}
