package br.com.celk.fornecedor.dto;

import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.basico.PessoaContrato;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroFornecedorDTO implements Serializable {

    private Pessoa pessoa;
    private List<PessoaContrato> pessoaContratoList;

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public List<PessoaContrato> getPessoaContratoList() {
        return pessoaContratoList;
    }

    public void setPessoaContratoList(List<PessoaContrato> pessoaContratoList) {
        this.pessoaContratoList = pessoaContratoList;
    }
}
