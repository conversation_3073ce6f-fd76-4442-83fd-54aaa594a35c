package br.com.celk.vigilancia.fiscalnarua;

import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

public class CboDTO {

    private String id;
    private String descricao;
    private String tenant;

    public CboDTO(TabelaCbo tabelaCbo){
        this.id = tabelaCbo.getCbo();
        this.descricao = tabelaCbo.getDescricao();
        this.tenant = TenantContext.getContext();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
