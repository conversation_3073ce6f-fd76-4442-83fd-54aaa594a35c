package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;

import java.io.Serializable;
import java.util.List;

public class FichaInvestigacaoAgravoFebreTifoideDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoFebreTifoide investigacaoAgravoFebreTifoide;
	private List<InvestigacaoAgravoFebreTifoideDeslocamento> investigacaoAgravoFebreTifoideDeslocamentos;
    private List<InvestigacaoAgravoFebreTifoideAlimentos> investigacaoAgravoFebreTifoideAlimentos;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoFebreTifoide getInvestigacaoAgravoFebreTifoide() {
        return investigacaoAgravoFebreTifoide;
    }

    public void setInvestigacaoAgravoFebreTifoide(InvestigacaoAgravoFebreTifoide investigacaoAgravoFebreTifoide) {
        this.investigacaoAgravoFebreTifoide = investigacaoAgravoFebreTifoide;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }


    public List<InvestigacaoAgravoFebreTifoideDeslocamento> getInvestigacaoAgravoFebreTifoideDeslocamentos() {
        return investigacaoAgravoFebreTifoideDeslocamentos;
    }

    public void setInvestigacaoAgravoFebreTifoideDeslocamentos(List<InvestigacaoAgravoFebreTifoideDeslocamento> investigacaoAgravoFebreTifoideDeslocamentos) {
        this.investigacaoAgravoFebreTifoideDeslocamentos = investigacaoAgravoFebreTifoideDeslocamentos;
    }

    public List<InvestigacaoAgravoFebreTifoideAlimentos> getInvestigacaoAgravoFebreTifoideAlimentos() {
        return investigacaoAgravoFebreTifoideAlimentos;
    }

    public void setInvestigacaoAgravoFebreTifoideAlimentos(List<InvestigacaoAgravoFebreTifoideAlimentos> investigacaoAgravoFebreTifoideAlimentos) {
        this.investigacaoAgravoFebreTifoideAlimentos = investigacaoAgravoFebreTifoideAlimentos;
    }
}
