package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSifilisCongenita;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class FichaInvestigacaoAgravoSifilisCongenitaDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoSifilisCongenita investigacaoAgravo;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoSifilisCongenita getInvestigacaoAgravo() {
        return investigacaoAgravo;
    }

    public void setInvestigacaoAgravo(InvestigacaoAgravoSifilisCongenita investigacaoAgravo) {
        this.investigacaoAgravo = investigacaoAgravo;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }

}
