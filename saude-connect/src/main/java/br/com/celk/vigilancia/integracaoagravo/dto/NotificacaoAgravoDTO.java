package br.com.celk.vigilancia.integracaoagravo.dto;

public class NotificacaoAgravoDTO {
    private String codigoAtendimento;
    private String codigoCid;
    private String dataNotificacao;
    private String atendimentoCnes;
    private String atendimentoDataSintoma;
    private String nomePaciente;
    private String idade;
    private String tipoIdade;
    private String sexo;
    private String gestante;
    private String racaCor;
    private String numeroCartaoSus;
    private String nomeMae;


    public String getCodigoAtendimento() {
        return codigoAtendimento;
    }

    public void setCodigoAtendimento(String codigoAtendimento) {
        this.codigoAtendimento = codigoAtendimento;
    }

    public String getCodigoCid() {
        return codigoCid;
    }

    public void setCodigoCid(String codigoCid) {
        this.codigoCid = codigoCid;
    }

    public String getDataNotificacao() {
        return dataNotificacao;
    }

    public void setDataNotificacao(String dataNotificacao) {
        this.dataNotificacao = dataNotificacao;
    }

    public String getAtendimentoCnes() {
        return atendimentoCnes;
    }

    public void setAtendimentoCnes(String atendimentoCnes) {
        this.atendimentoCnes = atendimentoCnes;
    }

    public String getAtendimentoDataSintoma() {
        return atendimentoDataSintoma;
    }

    public void setAtendimentoDataSintoma(String atendimentoDataSintoma) {
        this.atendimentoDataSintoma = atendimentoDataSintoma;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getIdade() {
        return idade;
    }

    public void setIdade(String idade) {
        this.idade = idade;
    }

    public String getTipoIdade() {
        return tipoIdade;
    }

    public void setTipoIdade(String tipoIdade) {
        this.tipoIdade = tipoIdade;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getGestante() {
        return gestante;
    }

    public void setGestante(String gestante) {
        this.gestante = gestante;
    }

    public String getRacaCor() {
        return racaCor;
    }

    public void setRacaCor(String racaCor) {
        this.racaCor = racaCor;
    }

    public String getNumeroCartaoSus() {
        return numeroCartaoSus;
    }

    public void setNumeroCartaoSus(String numeroCartaoSus) {
        this.numeroCartaoSus = numeroCartaoSus;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }
}
