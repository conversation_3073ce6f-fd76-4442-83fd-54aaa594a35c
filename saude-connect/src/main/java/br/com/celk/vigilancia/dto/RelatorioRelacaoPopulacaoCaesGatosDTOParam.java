package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;

import java.io.Serializable;

public class RelatorioRelacaoPopulacaoCaesGatosDTOParam implements Serializable {

    private Profissional profissional;
    private String bairro;
    private Long tipoAnimal;
    private String sexo;
    private FormaApresentacao formaApresentacao;
    private DatePeriod periodo;
    private String descricaoSexo;
    private String descricaoTipoAnimal;


    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        BAIRRO(Bundle.getStringApplication("rotulo_bairro")),
        TIPO_ANIMAL(Bundle.getStringApplication("rotulo_tipo_animal")),
        SEXO(Bundle.getStringApplication("sexo"));

        private String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    @DescricaoParametro("rotulo_profissional")
    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("bairro")
    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public Long getTipoAnimal() {
        return tipoAnimal;
    }

    public void setTipoAnimal(Long tipoAnimal) {
        this.tipoAnimal = tipoAnimal;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("sexo")
    public String getDescricaoSexo() {

        if (this.getSexo() != null){
            return this.getSexo().equals("M") ? Bundle.getStringApplication("rotulo_macho") : Bundle.getStringApplication("rotulo_femea");
        }else {
            return Bundle.getStringApplication("rotulo_ambos");
        }
    }

    @DescricaoParametro("rotulo_tipo_animal")
    public String getDescricaoTipoAnimal() {
        if (this.getTipoAnimal() != null){
            return this.getTipoAnimal() == 0 ? Bundle.getStringApplication("rotulo_cao") : Bundle.getStringApplication("rotulo_gato");
        }else {
            return Bundle.getStringApplication("rotulo_ambos");
        }
    }
}
