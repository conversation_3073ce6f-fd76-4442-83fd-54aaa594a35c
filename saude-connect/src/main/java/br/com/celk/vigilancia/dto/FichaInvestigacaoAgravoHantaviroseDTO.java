package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoHantavirose;

import java.io.Serializable;

public class FichaInvestigacaoAgravoHantaviroseDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoHantavirose investigacaoAgravoHantavirose;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoHantavirose getInvestigacaoAgravoHantavirose() {
        return investigacaoAgravoHantavirose;
    }

    public void setInvestigacaoAgravoHantavirose(InvestigacaoAgravoHantavirose investigacaoAgravoHantavirose) {
        this.investigacaoAgravoHantavirose = investigacaoAgravoHantavirose;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }
}
