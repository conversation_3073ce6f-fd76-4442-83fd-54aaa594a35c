package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoIntoxicacaoExogena;

import java.io.Serializable;

public class FichaInvestigacaoAgravoIntoxicacaoExogenaDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoIntoxicacaoExogena investigacaoAgravoIntoxicacaoExogena;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoIntoxicacaoExogena getInvestigacaoAgravoIntoxicacaoExogena() {
        return investigacaoAgravoIntoxicacaoExogena;
    }

    public void setInvestigacaoAgravoIntoxicacaoExogena(InvestigacaoAgravoIntoxicacaoExogena investigacaoAgravoIntoxicacaoExogena) {
        this.investigacaoAgravoIntoxicacaoExogena = investigacaoAgravoIntoxicacaoExogena;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }
}
