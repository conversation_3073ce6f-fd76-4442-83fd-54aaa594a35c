package br.com.celk.vigilancia.integracaodocsprime.dto;

import br.com.ksisolucoes.vo.controle.Grupo;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ParticipantesIntegracaoDocsprimeDTO implements Serializable {
    
    private Usuario usuario;
    private Grupo grupo;
    private String responsavelAcao;

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public Grupo getGrupo() {
        return grupo;
    }

    public void setGrupo(Grupo grupo) {
        this.grupo = grupo;
    }

    public String getResponsavelAcao() {
        return responsavelAcao;
    }

    public void setResponsavelAcao(String responsavelAcao) {
        this.responsavelAcao = responsavelAcao;
    }
}
