package br.com.celk.vigilancia.fiscalnarua;

import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;

import java.io.Serializable;

public class TipoSolicitacaoDTO implements Serializable {
    private long id;
    private String descricao;
    private long tipoRequerimento;
    private String tenant;

    public TipoSolicitacaoDTO(TipoSolicitacao tipoSolicitacao) {
        this.id = tipoSolicitacao.getCodigo();
        this.descricao = tipoSolicitacao.getDescricao();
        this.tipoRequerimento = tipoSolicitacao.getTipoRequerimento();
        this.tenant = TenantContext.getContext();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public long getTipoRequerimento() {
        return tipoRequerimento;
    }

    public void setTipoRequerimento(long tipoRequerimento) {
        this.tipoRequerimento = tipoRequerimento;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}