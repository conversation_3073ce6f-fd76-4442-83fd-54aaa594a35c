package br.com.celk.vigilancia.fiscalnarua;

import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.EloRoteiroAtividadeEstabelecimento;

import java.io.Serializable;

public class RoteiroEloTipoAtividadeDTO implements Serializable {
    private Long idRoteiroInspecao;
    private Long idTipoAtividade;
    private String tenant;

    public RoteiroEloTipoAtividadeDTO(EloRoteiroAtividadeEstabelecimento eloRoteiroAtividadeEstabelecimento) {
        this.idRoteiroInspecao = eloRoteiroAtividadeEstabelecimento.getRoteiroInspecao().getCodigo();
        this.idTipoAtividade = eloRoteiroAtividadeEstabelecimento.getAtividadeEstabelecimento().getCodigo();
        this.tenant = TenantContext.getContext();
    }

    public Long getIdRoteiroInspecao() {
        return idRoteiroInspecao;
    }

    public void setIdRoteiroInspecao(Long idRoteiroInspecao) {
        this.idRoteiroInspecao = idRoteiroInspecao;
    }

    public Long getIdTipoAtividade() {
        return idTipoAtividade;
    }

    public void setIdTipoAtividade(Long idTipoAtividade) {
        this.idTipoAtividade = idTipoAtividade;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}
