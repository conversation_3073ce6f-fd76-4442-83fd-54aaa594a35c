package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMeningite;

import java.io.Serializable;

public class FichaInvestigacaoAgravoMeningiteDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoMeningite investigacaoAgravoMeningite;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoMeningite getInvestigacaoAgravoMeningite() {
        return investigacaoAgravoMeningite;
    }

    public void setInvestigacaoAgravoMeningite(InvestigacaoAgravoMeningite investigacaoAgravoMeningite) {
        this.investigacaoAgravoMeningite = investigacaoAgravoMeningite;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }
}
