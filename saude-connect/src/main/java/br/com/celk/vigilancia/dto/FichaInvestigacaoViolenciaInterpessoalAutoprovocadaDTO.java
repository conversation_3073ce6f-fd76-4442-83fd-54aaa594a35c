package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoViolenciaInterpessoalAutoprovocada;

import java.io.Serializable;
import java.util.List;

public class FichaInvestigacaoViolenciaInterpessoalAutoprovocadaDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoViolenciaInterpessoalAutoprovocada investigacaoAgravoViolenciaInterpessoalAutoprovocada;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoViolenciaInterpessoalAutoprovocada getInvestigacaoAgravoViolenciaInterpessoalAutoprovocada() {
        return investigacaoAgravoViolenciaInterpessoalAutoprovocada;
    }

    public void setInvestigacaoAgravoViolenciaInterpessoalAutoprovocada(InvestigacaoAgravoViolenciaInterpessoalAutoprovocada investigacaoAgravoViolenciaInterpessoalAutoprovocada) {
        this.investigacaoAgravoViolenciaInterpessoalAutoprovocada = investigacaoAgravoViolenciaInterpessoalAutoprovocada;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }

}
