package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaParecer;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoParecerRequerimentoDTO implements Serializable {

    private RequerimentoVigilanciaParecer requerimentoVigilanciaParecer;
    private RequerimentoVigilancia requerimentoVigilancia;
    private VigilanciaEndereco vigilanciaEndereco;
    private String descricaoAtividadePrincipal;
    private Profissional profissionalImpressao;
    private List<Profissional> fiscais;
    private String resposta;
    private String anexos;
    private String urlQrCode;

    public RequerimentoVigilanciaParecer getRequerimentoVigilanciaParecer() {
        return requerimentoVigilanciaParecer;
    }

    public void setRequerimentoVigilanciaParecer(RequerimentoVigilanciaParecer requerimentoVigilanciaParecer) {
        this.requerimentoVigilanciaParecer = requerimentoVigilanciaParecer;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public VigilanciaEndereco getVigilanciaEndereco() {
        return vigilanciaEndereco;
    }

    public void setVigilanciaEndereco(VigilanciaEndereco vigilanciaEndereco) {
        this.vigilanciaEndereco = vigilanciaEndereco;
    }

    public String getEnderecoFormatado() {
        StringBuilder builder = new StringBuilder();

        if (this.getVigilanciaEndereco().getRuaFormatada() != null) {
            builder.append(this.getVigilanciaEndereco().getRuaFormatada());
        }

        if (this.getRequerimentoVigilancia().getEstabelecimento() != null && this.getRequerimentoVigilancia().getEstabelecimento().getCodigo() != null) { //autuado é um Estabelecimento
            builder.append(", ");
            if (this.getRequerimentoVigilancia().getEstabelecimento().getNumeroLogradouro() != null) {
                builder.append(this.getRequerimentoVigilancia().getEstabelecimento().getNumeroLogradouro());
            } else {
                builder.append(" S/N");
            }
        }
        if (this.getVigilanciaEndereco().getBairro() != null) {
            builder.append(", ");
            builder.append(this.getVigilanciaEndereco().getBairro());
        }
        if (this.getVigilanciaEndereco().getCep() != null) {
            builder.append(", CEP - ");
            builder.append(this.getVigilanciaEndereco().getCepFormatado());
        }
        if (this.getVigilanciaEndereco().getCidade() != null && this.getVigilanciaEndereco().getCodigo() != null) {
            builder.append(", ");
            builder.append(this.getVigilanciaEndereco().getCidadeFormatado());
        }

        if(StringUtils.trimToNull(builder.toString()) == null) {
            return "Não Informado";
        }
        return builder.toString();
    }

    public String getDescricaoAtividadePrincipal() {
        return descricaoAtividadePrincipal;
    }

    public void setDescricaoAtividadePrincipal(String descricaoAtividadePrincipal) {
        this.descricaoAtividadePrincipal = descricaoAtividadePrincipal;
    }

    public Profissional getProfissionalImpressao() {
        return profissionalImpressao;
    }

    public void setProfissionalImpressao(Profissional profissionalImpressao) {
        this.profissionalImpressao = profissionalImpressao;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getAnexos() {
        return anexos;
    }

    public void setAnexos(String anexos) {
        this.anexos = anexos;
    }

    public List<Profissional> getFiscais() {
        return fiscais;
    }

    public void setFiscais(List<Profissional> fiscais) {
        this.fiscais = fiscais;
    }

    public String getUrlQrCode() {
        return urlQrCode;
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }
}
