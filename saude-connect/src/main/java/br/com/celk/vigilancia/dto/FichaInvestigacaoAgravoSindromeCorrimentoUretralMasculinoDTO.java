package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoSindromeCorrimentoUretralMasculino;

import java.io.Serializable;

public class FichaInvestigacaoAgravoSindromeCorrimentoUretralMasculinoDTO implements Serializable {

    private RegistroAgravo registroAgravo;
    private InvestigacaoAgravoSindromeCorrimentoUretralMasculino investigacaoAgravoSindromeCorrimentoUretralMasculino;
    private boolean encerrarFicha;

    public RegistroAgravo getRegistroAgravo() {
        return registroAgravo;
    }

    public void setRegistroAgravo(RegistroAgravo registroAgravo) {
        this.registroAgravo = registroAgravo;
    }

    public InvestigacaoAgravoSindromeCorrimentoUretralMasculino getInvestigacaoAgravoSindromeCorrimentoUretralMasculino() {
        return investigacaoAgravoSindromeCorrimentoUretralMasculino;
    }

    public void setInvestigacaoAgravoSindromeCorrimentoUretralMasculino(InvestigacaoAgravoSindromeCorrimentoUretralMasculino investigacaoAgravoSindromeCorrimentoUretralMasculino) {
        this.investigacaoAgravoSindromeCorrimentoUretralMasculino = investigacaoAgravoSindromeCorrimentoUretralMasculino;
    }

    public boolean isEncerrarFicha() {
        return encerrarFicha;
    }

    public void setEncerrarFicha(boolean encerrarFicha) {
        this.encerrarFicha = encerrarFicha;
    }

}
