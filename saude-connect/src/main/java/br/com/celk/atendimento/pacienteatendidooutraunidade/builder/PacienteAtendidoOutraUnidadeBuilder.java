package br.com.celk.atendimento.pacienteatendidooutraunidade.builder;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.io.Serializable;
import java.util.Date;

public class PacienteAtendidoOutraUnidadeBuilder implements Serializable {
    private PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade;

    public PacienteAtendidoOutraUnidadeBuilder builder() {
        this.pacienteAtendidoOutraUnidade = new PacienteAtendidoOutraUnidade();
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setCodigo(Long codigo) {
        this.pacienteAtendidoOutraUnidade.setCodigo(codigo);
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setDataCadastro(Date dataCadastro) {
        this.pacienteAtendidoOutraUnidade.setDataCadastro(dataCadastro);
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setAtendimento(Atendimento atendimento) {
        this.pacienteAtendidoOutraUnidade.setAtendimento(atendimento);
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setEmpresaPaciente(Empresa empresaPaciente) {
        this.pacienteAtendidoOutraUnidade.setEmpresaPaciente(empresaPaciente);
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setSituacao(Long situacao) {
        this.pacienteAtendidoOutraUnidade.setSituacao(situacao);
        return this;
    }

    public PacienteAtendidoOutraUnidadeBuilder setJustificativa(String justificativa) {
        this.pacienteAtendidoOutraUnidade.setJustificativa(justificativa);
        return this;
    }

    public PacienteAtendidoOutraUnidade build() {
        return this.pacienteAtendidoOutraUnidade;
    }
}
