package br.com.celk.atendimento.prontuario.reprocessaAtendimentosEsus;

import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.io.Serializable;

public class DTOTipoAtendimentoNaoProcessado implements Serializable {
    private String codigoTipoAtendimento;
    private String descricaoTipoAtendimento;
    private long total;

    public String getCodigoTipoAtendimento() {
        return codigoTipoAtendimento;
    }

    public void setCodigoTipoAtendimento(String codigoTipoAtendimento) {
        this.codigoTipoAtendimento = codigoTipoAtendimento;
    }

    public String getDescricaoTipoAtendimento() {
        return descricaoTipoAtendimento;
    }

    public void setDescricaoTipoAtendimento(String descricaoTipoAtendimento) {
        this.descricaoTipoAtendimento = descricaoTipoAtendimento;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
