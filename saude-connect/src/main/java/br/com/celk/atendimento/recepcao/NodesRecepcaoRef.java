package br.com.celk.atendimento.recepcao;

/**
 * <AUTHOR>
 */
public enum NodesRecepcaoRef {
    AGENDAMENTOS,
    AGEN<PERSON>MENTO_CIRURGICO,
    ATENDIMENTOS,
    ATENDIMENTOS_HOSPITAL,
    CONFIRMACAO_EXAMES,
    CO<PERSON><PERSON><PERSON>CA<PERSON>_INTERNACAO,
    CO<PERSON><PERSON>MACA<PERSON>_PRESENCA,
    CONFIRMACAO_PRESENCA_HOSPITAL,
    ENTRADA,
    EXAMES,
    EXAMES_AUTORIZACAO,
    MARCACAO,
    PRESTACAO_CONTAS,
    REG_LEITO_CONFIRMACAO_CHEGADA;
}
