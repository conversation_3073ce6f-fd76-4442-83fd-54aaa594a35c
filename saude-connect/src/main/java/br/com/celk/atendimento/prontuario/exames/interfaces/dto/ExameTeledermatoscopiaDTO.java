package br.com.celk.atendimento.prontuario.exames.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoTeledermatoscopia;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ExameTeledermatoscopiaDTO implements Serializable {

    private RequisicaoTeledermatoscopia requisicaoTeledermatoscopia;
    private ExameRequisicao exameRequisicao;
    private UsuarioCadsus usuarioCadsus;
    private Profissional profissional;

    public RequisicaoTeledermatoscopia getRequisicaoTeledermatoscopia() {
        return requisicaoTeledermatoscopia;
    }

    public void setRequisicaoTeledermatoscopia(RequisicaoTeledermatoscopia requisicaoTeledermatoscopia) {
        this.requisicaoTeledermatoscopia = requisicaoTeledermatoscopia;
    }

    public ExameRequisicao getExameRequisicao() {
        return exameRequisicao;
    }

    public void setExameRequisicao(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
    }
}