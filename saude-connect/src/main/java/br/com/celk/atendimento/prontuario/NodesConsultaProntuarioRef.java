package br.com.celk.atendimento.prontuario;

/**
 *
 * <AUTHOR>
 */
public enum NodesConsultaProntuarioRef {
    CONSULTA_CURVA_CRESCIMENTO,
    CONSULTA_DOCUMENTOS,
    CONS<PERSON><PERSON>A_<PERSON>NC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CEO,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_ENCAMINHAMENTO_ESPECIALISTA,
    CONSULTA_ESTRATIFICA_RISCO_PARANA,
    CONSULTA_FICHA_ACOLHIMENTO,
    CONSULTA_HISTORICO_CLINICO,
    CONSULTA_HISTORICO_EVOLUCAO,
    CONSULTA_HISTORICO_ODONTOGRAMA,
    CONSULTA_NOVO_HISTORICO_ODONTOGRAMA,
    CONSULTA_LAUDO_APAC,
    CONSULTA_LAUDO_BPAI,
    CONSULTA_LAUDO_TFD,
    CONSULTA_PRESCRICAO_INTERNA_INTERNACAO,
    CONSULTA_PROCEDIMENTOS,
    CONSULTA_RECEITUARIO,
    CONSULTA_RECEITUARIO_LIVRE,
    CONSULTA_REQUISICAO_EXAMES,
    CONSULTA_SAUDE_MULHER,
    CONSULTA_SOLICITACAO_EXAMES,
    CONSULTA_TESTE_RAPIDO,
    CONSULTA_PROTOCOLO_SEPSE
}
