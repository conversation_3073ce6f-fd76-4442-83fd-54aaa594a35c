package br.com.celk.cadsus.relatorio.pacientesconferencia;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacientesConferenciaDTO implements Serializable, Comparable<RelatorioPacientesConferenciaDTO>{

    private Empresa empresa;
    private UsuarioCadsus usuarioCadsus;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private UsuarioCadsusCns usuarioCadsusCns;
    private Usuario usuarioCadastro;
    private String rg;
    private String certidaoNascimento;
    private String certidaoCasamento;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }
    public Usuario getUsuarioCadastro() {
        return usuarioCadastro;
    }

    public void setUsuarioCadastro(Usuario usuarioCadastro) {
        this.usuarioCadastro = usuarioCadastro;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getCertidaoNascimento() {
        return certidaoNascimento;
    }

    public void setCertidaoNascimento(String certidaoNascimento) {
        this.certidaoNascimento = certidaoNascimento;
    }

    public String getCertidaoCasamento() {
        return certidaoCasamento;
    }

    public void setCertidaoCasamento(String certidaoCasamento) {
        this.certidaoCasamento = certidaoCasamento;
    }

    @Override
    public int compareTo(RelatorioPacientesConferenciaDTO o2) {
        if(getEmpresa() == null){
            return -1;
        }
        if(o2.getEmpresa() == null){
            return 1;
        }

        int retorno = getEmpresa().getDescricao().compareTo(o2.getEmpresa().getDescricao());
        if(retorno == 0){
            if(getUsuarioCadsus() == null){
                return -1;
            }
            if(o2.getUsuarioCadsus() == null){
                return 1;
            }
            retorno = getUsuarioCadsus().getNome().compareTo(o2.getUsuarioCadsus().getNome());
        }
        return retorno;
    }
}
