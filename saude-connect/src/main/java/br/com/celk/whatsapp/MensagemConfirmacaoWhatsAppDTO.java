package br.com.celk.whatsapp;

import br.com.ksisolucoes.system.sessao.TenantContext;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class MensagemConfirmacaoWhatsAppDTO implements Serializable {

    private String name;
    private String data;
    private String hora;
    private String tipoConsulta;
    private String tipo;
    private String codigo;
    private String telefone;
    private String link;
    private String local;
    private String phone;
    private String celkMessageId;
    private String tenant;

    public MensagemConfirmacaoWhatsAppDTO() {
        this.tipo = "confirmacao";
        this.tenant = TenantContext.getRealContext();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCelkMessageId() {
        return celkMessageId;
    }

    public void setCelkMessageId(String celkMessageId) {
        this.celkMessageId = celkMessageId;
    }

}
