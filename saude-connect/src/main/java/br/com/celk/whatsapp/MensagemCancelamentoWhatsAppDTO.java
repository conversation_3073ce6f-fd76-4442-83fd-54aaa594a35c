package br.com.celk.whatsapp;

import br.com.ksisolucoes.system.sessao.TenantContext;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class MensagemCancelamentoWhatsAppDTO implements Serializable {

    private String name;
    private String data;
    private String hora;
    private String tipoConsulta;
    private String tipo;
    private String local;
    private String phone;
    private String celkMessageId;
    private String tenant;

    public MensagemCancelamentoWhatsAppDTO() {
        this.tipo = "cancelamento";
        this.tenant = TenantContext.getRealContext();
    }

    public String getCelkMessageId() {
        return celkMessageId;
    }

    public void setCelkMessageId(String celkMessageId) {
        this.celkMessageId = celkMessageId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }


    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
