package br.com.ksisolucoes.bo.enderecocoordenadas;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.coordenadas.EnderecoCoordenadas;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Criterion;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CidadeHandlerTest {

    private EnderecoCoordenadas enderecoCoordenadas;
    private EnderecoCoordenadas enderecoCoordenadasResult;
    @Mock
    private Session mockSession;
    @Mock
    private Criteria mockCriteria;

    @Before
    public void setUp() throws Exception {
        Mockito.when(mockSession.createCriteria(Mockito.<Class>any())).thenReturn(mockCriteria);
        Mockito.when(mockCriteria.add(Mockito.<Criterion>any())).thenReturn(mockCriteria);
        Mockito.when(mockCriteria.setMaxResults(Mockito.anyInt())).thenReturn(mockCriteria);
    }

    @Test
    public void handleQuandoNaoEncontrarCoordenadasNoBancoDeveBuscarNoGoogle() {
        Mockito.when(mockCriteria.setMaxResults(1).uniqueResult()).thenReturn(null);
        enderecoCoordenadas = new EnderecoCoordenadas();
        enderecoCoordenadas.setLatitude(10d);
        enderecoCoordenadas.setLongitude(10d);
        enderecoCoordenadasResult = null;
        Cidade cidade = new Cidade();
        cidade.setCodigo(1l);
        cidade.setDescricao("Florianópolis");
        LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco();
        CidadeHandler endereceUsuarioCadsusHandler = new CidadeHandlerMock(latitudeLongitudeEndereco);
        endereceUsuarioCadsusHandler.handle(cidade);
        Assert.assertNotNull(enderecoCoordenadasResult);
        Assert.assertNotNull(enderecoCoordenadasResult.getCidade());
        Assert.assertNotNull(enderecoCoordenadasResult.getCidade().getCodigo());
    }

    @Test
    public void handleQuandoNaoEncontrarCoordenadasNoBancoEDescCidadeForNullNaoDeveBuscarNoGoogle() {
        Mockito.when(mockCriteria.setMaxResults(1).uniqueResult()).thenReturn(null);
        enderecoCoordenadas = new EnderecoCoordenadas();
        enderecoCoordenadasResult = null;
        Cidade cidade = new Cidade();
        cidade.setCodigo(1l);
        LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco();
        CidadeHandler endereceUsuarioCadsusHandler = new CidadeHandlerMock(latitudeLongitudeEndereco);
        endereceUsuarioCadsusHandler.handle(cidade);
        Assert.assertEquals(null, enderecoCoordenadasResult);
    }

    @Test
    public void handleQuandoEncontrarCoordenadasNoBancoNaoDeveBuscarNoGoogle() {
        enderecoCoordenadas = new EnderecoCoordenadas();
        enderecoCoordenadas.setLatitude(10d);
        enderecoCoordenadas.setLongitude(10d);
        Mockito.when(mockCriteria.setMaxResults(1).uniqueResult()).thenReturn(enderecoCoordenadas);
        enderecoCoordenadasResult = null;
        Cidade cidade = new Cidade();
        cidade.setCodigo(2l);
        LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco();
        CidadeHandler endereceUsuarioCadsusHandler = new CidadeHandlerMock(latitudeLongitudeEndereco);
        endereceUsuarioCadsusHandler.handle(cidade);
        Assert.assertEquals(10d, enderecoCoordenadas.getLatitude(), 0);
        Assert.assertEquals(10d, enderecoCoordenadas.getLongitude(), 0);
        Assert.assertEquals(null, enderecoCoordenadasResult);
    }

    private class CidadeHandlerMock extends CidadeHandler {

        public CidadeHandlerMock(LatitudeLongitudeEndereco latitudeLongitudeEndereco) {
            super(latitudeLongitudeEndereco);
        }

        @Override
        public Session getSession() {
            return mockSession;
        }

        @Override
        public EnderecoCoordenadas buscaEndereco(String endereco) {
            return enderecoCoordenadas;
        }

        @Override
        public void salvaEndereco(EnderecoCoordenadas enderecoCoordenadas) {
            enderecoCoordenadasResult = enderecoCoordenadas;
        }
    }
}