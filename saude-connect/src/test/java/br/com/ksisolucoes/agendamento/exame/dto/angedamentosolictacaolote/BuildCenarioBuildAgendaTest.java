package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BuildCenarioBuildAgendaTest {

    public static final Empresa empresa_1;
    public static final Empresa empresa_2;

    public static final Agenda agenda_1;
    public static final Agenda agenda_2;

    public static final AgendaGrade agenda_grade_1;
    public static final AgendaGrade agenda_grade_2;

    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_1;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_2;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_3;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_4;

    public static final Procedimento procedimento_1;
    public static final Procedimento procedimento_2;
    public static final Procedimento procedimento_3;
    public static final Procedimento procedimento_4;

    public static final TipoExame tipoExame;

    static {
        empresa_1 = buildEmpresa(1L, "Empresa 1");
        empresa_2 = buildEmpresa(2L, "Empresa 2");

        agenda_1 = buildAgenda(1L, empresa_1);
        agenda_2 = buildAgenda(2L, empresa_2);

        agenda_grade_1 = buildAgendaGrade(1L, agenda_1);
        agenda_grade_2 = buildAgendaGrade(2L, agenda_2);

        agenda_grade_atendimento_1 = buildAgendaGradeAtendimentoDto(1L, agenda_grade_1);
        agenda_grade_atendimento_2 = buildAgendaGradeAtendimentoDto(2L, agenda_grade_1);
        agenda_grade_atendimento_3 = buildAgendaGradeAtendimentoDto(3L, agenda_grade_2);
        agenda_grade_atendimento_4 = buildAgendaGradeAtendimentoDto(4L, agenda_grade_2);

        procedimento_1 = buildProcedimento(1L, "Procedimento 1");
        procedimento_2 = buildProcedimento(2L, "Procedimento 2");
        procedimento_3 = buildProcedimento(3L, "Procedimento 3");
        procedimento_4 = buildProcedimento(4L, "Procedimento 4");

        tipoExame = new TipoExame(1L);
    }

    public static ParametrosBuildAgendaDTO buildParametros() {
        return new ParametrosBuildAgendaDTO().setAgendasGradeAtendimeto(buildAtendimentos())
                                             .setAgendasExames(buildListAgendaGradeExame())
                                             .setHorarios(buildHorarios())
                                             .setExamesPrestador(buildListExamePrestadorProcedimento())
                                             .setCompetencias(buildListaExamePrestadorCompetencia())
                                             .setParam(new AgendaGradeAtendimentoDTOParam())
                                             .setTipoExame(tipoExame);
    }

    private static List<AgendaGradeAtendimentoDTO> buildAtendimentos() {
        List<AgendaGradeAtendimentoDTO> atendimentos = new ArrayList<>();
        atendimentos.add(agenda_grade_atendimento_1);
        atendimentos.add(agenda_grade_atendimento_2);
        atendimentos.add(agenda_grade_atendimento_3);
        atendimentos.add(agenda_grade_atendimento_4);
        return atendimentos;
    }

    private static AgendaGradeAtendimentoDTO buildAgendaGradeAtendimentoDto(Long codigo, AgendaGrade agendaGrade) {
        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = new AgendaGradeAtendimentoDTO();
        AgendaGradeAtendimento agendaGradeAtendimento = new AgendaGradeAtendimento();

        agendaGradeAtendimento.setCodigo(codigo);
        agendaGradeAtendimento.setAgendaGrade(agendaGrade);
        agendaGradeAtendimentoDTO.setAgendaGradeAtendimento(agendaGradeAtendimento);
        return agendaGradeAtendimentoDTO;
    }

    private static Empresa buildEmpresa(Long codigo, String descricao) {
        Empresa empresa = new Empresa();
        empresa.setCodigo(codigo);
        empresa.setDescricao(descricao);
        return empresa;
    }

    private static Agenda buildAgenda(Long codigo, Empresa empresa) {
        Agenda agenda = new Agenda();
        agenda.setCodigo(codigo);
        agenda.setEmpresa(empresa);
        return agenda;
    }

    private static AgendaGrade buildAgendaGrade(Long codigo, Agenda agenda) {
        AgendaGrade agendaGrade = new AgendaGrade();
        agendaGrade.setCodigo(codigo);
        agendaGrade.setAgenda(agenda);
        agendaGrade.setData(DataUtil.getDataAtual());
        return agendaGrade;
    }

    private static List<AgendaGradeAtendimentoPacienteDTO> buildHorarios() {
        List<AgendaGradeAtendimentoPacienteDTO> horarios = new ArrayList<>();
        horarios.add(buildHorario(AgendaGradeHorario.Status.PENDENTE, DataUtil.getDataAtual(), agenda_grade_atendimento_1));
        horarios.add(buildHorario(AgendaGradeHorario.Status.AGENDADO, DataUtil.getDataAtual(), agenda_grade_atendimento_2));
        horarios.add(buildHorario(AgendaGradeHorario.Status.RESERVADO, DataUtil.getDataAtual(), agenda_grade_atendimento_3));
        horarios.add(buildHorario(AgendaGradeHorario.Status.PENDENTE, DataUtil.getDataAtual(), agenda_grade_atendimento_4));
        return horarios;
    }

    private static AgendaGradeAtendimentoPacienteDTO buildHorario(AgendaGradeHorario.Status status, Date hora, AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO) {
        AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO = new AgendaGradeAtendimentoPacienteDTO();
        AgendaGradeHorario agendaGradeHorario = new AgendaGradeHorario();
        agendaGradeHorario.setStatus(status.value());
        agendaGradeHorario.setHora(hora);
        agendaGradeHorario.setAgendaGradeAtendimento(agendaGradeAtendimentoDTO.getAgendaGradeAtendimento());

        agendaGradeAtendimentoPacienteDTO.setAgendaGradeHorario(agendaGradeHorario);

        return agendaGradeAtendimentoPacienteDTO;
    }

    private static List<ExamePrestadorCompetencia> buildListaExamePrestadorCompetencia() {
        List<ExamePrestadorCompetencia> competencias = new ArrayList<>();
        competencias.add(buildExamePrestadorCompetencia(tipoExame, empresa_1));
        competencias.add(buildExamePrestadorCompetencia(null, empresa_1));
        return competencias;
    }

    private static ExamePrestadorCompetencia buildExamePrestadorCompetencia(TipoExame tipoExame, Empresa empresa) {
        ExamePrestadorCompetencia competencia = new ExamePrestadorCompetencia();
        competencia.setTipoExame(tipoExame);
        competencia.setTetoFinanceiro(20D);
        competencia.setTetoFinanceiroRealizado(10D);
        competencia.setEmpresa(empresa);
        return competencia;
    }

    public static List<AgendaGradeExame> buildListAgendaGradeExame() {
        List<AgendaGradeExame> agendasGrade = new ArrayList<>();
        agendasGrade.add(buildAgendaGradeExame(buildExameProcedimento(1L, procedimento_1), agenda_grade_1));
        agendasGrade.add(buildAgendaGradeExame(buildExameProcedimento(2L, procedimento_2), agenda_grade_1));
        return agendasGrade;
    }

    private static ExameProcedimento buildExameProcedimento(Long codigo, Procedimento procedimento) {
        ExameProcedimento exameProcedimento = new ExameProcedimento();
        exameProcedimento.setCodigo(codigo);
        exameProcedimento.setProcedimento(procedimento);
        return exameProcedimento;
    }

    private static Procedimento buildProcedimento(Long codigo, String descricao) {
        Procedimento procedimento = new Procedimento();
        procedimento.setCodigo(codigo);
        procedimento.setDescricao(descricao);
        return procedimento;
    }

    private static AgendaGradeExame buildAgendaGradeExame(ExameProcedimento exameProcedimento, AgendaGrade agendaGrade) {
        AgendaGradeExame agendaGradeExame = new AgendaGradeExame();
        agendaGradeExame.setExameProcedimento(exameProcedimento);
        agendaGradeExame.setAgendaGrade(agendaGrade);
        return agendaGradeExame;
    }

    private static List<ExamePrestadorProcedimento> buildListExamePrestadorProcedimento() {
        ExamePrestador examePrestador = new ExamePrestador();
        examePrestador.setPrestador(empresa_1);
        List<ExamePrestadorProcedimento> examesPrestador = new ArrayList<>();
        examesPrestador.add(buildExamePrestadorProcedimento(examePrestador, buildExameProcedimento(1L, procedimento_1)));
        examesPrestador.add(buildExamePrestadorProcedimento(examePrestador, buildExameProcedimento(2L, procedimento_2)));
        examesPrestador.add(buildExamePrestadorProcedimento(examePrestador, buildExameProcedimento(3L, procedimento_3)));
        return examesPrestador;
    }

    private static ExamePrestadorProcedimento buildExamePrestadorProcedimento(ExamePrestador examePrestador, ExameProcedimento exameProcedimento) {
        ExamePrestadorProcedimento examePrestadorProcedimento = new ExamePrestadorProcedimento();
        examePrestadorProcedimento.setExamePrestador(examePrestador);
        examePrestadorProcedimento.setExameProcedimento(exameProcedimento);
        return examePrestadorProcedimento;
    }
}
