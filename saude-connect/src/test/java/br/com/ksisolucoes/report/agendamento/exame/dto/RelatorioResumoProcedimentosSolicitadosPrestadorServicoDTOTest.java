package br.com.ksisolucoes.report.agendamento.exame.dto;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class RelatorioResumoProcedimentosSolicitadosPrestadorServicoDTOTest {

    private RelatorioResumoProcedimentosSolicitadosPrestadorServicoDTO dto;

    @Before
    public void setUp() throws Exception {
        dto = new RelatorioResumoProcedimentosSolicitadosPrestadorServicoDTO();
    }

    @Test
    public void valorSUSTest() {
        double valorSus = 126.14D;
        dto.setValorSUS(valorSus);
        dto.setValorRecursoProprio(0D);
        assertEquals(valorSus, dto.getValorSUS(), 0.0);

        dto.setValorRecursoProprio(111D);
        assertEquals(valorSus, dto.getValorSUS(), 0.0);
    }

    @Test
    public void valorComplementoTest() {
        double valorComplemento = 45D;
        dto.setValorComplemento(valorComplemento);
        dto.setValorRecursoProprio(0D);
        assertEquals(valorComplemento, dto.getValorComplemento(), 0.0);

        dto.setValorRecursoProprio(111D);
        assertEquals(valorComplemento, dto.getValorComplemento(), 0.0);
    }

    @Test
    public void procedimentoReferenciaFormatadoTest() {
        String referencia = "0405020015";
        dto.setProcedimentoReferenciaFormatado(referencia);
        String referenciaFormatado = dto.getProcedimentoReferenciaFormatado();
        assertEquals("04.05.02.001-5", referenciaFormatado);

        referencia = "0";
        dto.setProcedimentoReferenciaFormatado(referencia);
        referenciaFormatado = dto.getProcedimentoReferenciaFormatado();
        assertEquals("00.00.00.000-0", referenciaFormatado);

        referencia = "";
        dto.setProcedimentoReferenciaFormatado(referencia);
        referenciaFormatado = dto.getProcedimentoReferenciaFormatado();
        assertEquals("", referenciaFormatado);

        referencia = "1abcd";
        dto.setProcedimentoReferenciaFormatado(referencia);
        referenciaFormatado = dto.getProcedimentoReferenciaFormatado();
        assertEquals("1abcd", referenciaFormatado);
    }
}
