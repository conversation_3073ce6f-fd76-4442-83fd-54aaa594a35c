package br.com.celk.solicitacaoagendamento;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static br.com.celk.solicitacaoagendamento.ValidacaoDadosPacienteAtendimento.validaDadosPaciente;
import static br.com.ksisolucoes.util.Modulos.AGENDAMENTO;
import static br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.NAO;
import static br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.SIM;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.powermock.api.mockito.PowerMockito.when;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BOFactory.class, UsuarioCadsusHelper.class})
public class ValidacaoDadosPacienteAtendimentoTest {

    private static final String obrigatorioInformacoesAdicionaisDoPaciente = "obrigatorioInformacoesAdicionaisDoPaciente";

    private UsuarioCadsus paciente;

    @Mock
    private CommomFacade commomFacade;

    @Mock
    private IParameterModuleContainer iParameterModuleContainer;

    @Before
    public void setup() throws DAOException {
        PowerMockito.mockStatic(BOFactory.class);
        PowerMockito.mockStatic(UsuarioCadsusHelper.class);

        paciente = new UsuarioCadsus(1L);

        when(BOFactory.getBO(CommomFacade.class)).thenReturn(commomFacade);
        when(commomFacade.modulo(AGENDAMENTO)).thenReturn(iParameterModuleContainer);
        when(UsuarioCadsusHelper.carregarDocumentosObrigatorios(paciente.getCodigo())).thenReturn(paciente);
        when(UsuarioCadsusHelper.carregarNumeroCartaoFormatado(paciente)).thenReturn("");

    }

    @Test
    public void deveValidarDadosPaciente() {
        when(iParameterModuleContainer.getParametro(obrigatorioInformacoesAdicionaisDoPaciente)).thenReturn(SIM);
        try {
            validaDadosPaciente(paciente);
            fail("Deve retornar exceção");
        } catch (ValidacaoException exception) {
            assertEquals(Bundle.getStringApplication("msg_validacao_obrigatoriedade_informacoes_adicionais_paciente"), exception.getMessage());
        }
    }

    @Test()
    public void naoDeveValidarDadosPaciente() {
        when(iParameterModuleContainer.getParametro(obrigatorioInformacoesAdicionaisDoPaciente)).thenReturn(NAO);
        try {
            validaDadosPaciente(paciente);
        } catch (ValidacaoException exception) {
            fail("Não deve retornar exceção");
        }
    }
}