package br.com.celk.cenarios;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;

import java.util.Arrays;
import java.util.List;

public class BuildCenarioFiltrarAgendasParametroUnidadeOrigemESolicitanteTest {

    public static final Empresa empresa_1;
    public static final Empresa empresa_2;
    public static final Empresa empresa_3;

    public static final Agenda agenda_1;
    public static final Agenda agenda_2;

    public static final AgendaGrade agenda_grade_1;
    public static final AgendaGrade agenda_grade_2;

    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_1;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_2;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_3;
    public static final AgendaGradeAtendimentoDTO agenda_grade_atendimento_4;

    public static final Procedimento procedimento_1;
    public static final Procedimento procedimento_2;
    public static final Procedimento procedimento_3;
    public static final Procedimento procedimento_4;

    public static final ExameProcedimento exame_procedimento_1;
    public static final ExameProcedimento exame_procedimento_2;

    public static final ExamePrestador exame_prestador_1;
    public static final ExamePrestador exame_prestador_2;


    public static final SolicitacaoAgendamento solicitacao_agendamento;
    public static final SolicitacaoAgendamentoExame solicitacao_agendamento_exame_1;
    public static final SolicitacaoAgendamentoExame solicitacao_agendamento_exame_2;


    static {
        empresa_1 = buildEmpresa(1L, "Empresa 1");
        empresa_2 = buildEmpresa(2L, "Empresa 2");
        empresa_3 = buildEmpresa(3L, "Empresa 3");

        agenda_1 = buildAgenda(1L, empresa_1);
        agenda_2 = buildAgenda(2L, empresa_2);

        agenda_grade_1 = buildAgendaGrade(1L, agenda_1);
        agenda_grade_2 = buildAgendaGrade(2L, agenda_2);

        agenda_grade_atendimento_1 = buildAgendaGradeAtendimentoDto(1L, agenda_grade_1);
        agenda_grade_atendimento_2 = buildAgendaGradeAtendimentoDto(2L, agenda_grade_1);
        agenda_grade_atendimento_3 = buildAgendaGradeAtendimentoDto(3L, agenda_grade_2);
        agenda_grade_atendimento_4 = buildAgendaGradeAtendimentoDto(4L, agenda_grade_2);

        procedimento_1 = buildProcedimento(1L, "Procedimento 1");
        procedimento_2 = buildProcedimento(2L, "Procedimento 2");
        procedimento_3 = buildProcedimento(3L, "Procedimento 3");
        procedimento_4 = buildProcedimento(4L, "Procedimento 4");

        exame_procedimento_1 = buildExameProcedimento(1L);
        exame_procedimento_2 = buildExameProcedimento(2L);

        exame_prestador_1 = buildExamePrestador(empresa_1);
        exame_prestador_2 = buildExamePrestador(empresa_2);

        solicitacao_agendamento = buildSolicitacao(empresa_1, empresa_2);

        solicitacao_agendamento_exame_1 = buildSolicitacaoAgendamentoExame(exame_procedimento_1);
        solicitacao_agendamento_exame_2 = buildSolicitacaoAgendamentoExame(exame_procedimento_2);
    }

    public static List<AgendaGradeAtendimentoDTO> buildAgendas() {
        return Arrays.asList(agenda_grade_atendimento_1,
                             agenda_grade_atendimento_2,
                             agenda_grade_atendimento_3,
                             agenda_grade_atendimento_4);
    }

    public static List<SolicitacaoAgendamentoExame> buildSolicitacaoAgendamentosExames() {
        return Arrays.asList(solicitacao_agendamento_exame_1,
                             solicitacao_agendamento_exame_2);
    }

    public static List<ExamePrestadorProcedimento> buildExamePrestadorEmpresa1Procedimentos() {
        return Arrays.asList(buildExamePrestadorProcedimento(exame_procedimento_1, exame_prestador_1),
                             buildExamePrestadorProcedimento(exame_procedimento_2, exame_prestador_1));
    }

    public static List<ExamePrestadorProcedimento> buildExamePrestadorEmpresa2Procedimentos() {
        return Arrays.asList(buildExamePrestadorProcedimento(exame_procedimento_1, exame_prestador_2),
                             buildExamePrestadorProcedimento(exame_procedimento_2, exame_prestador_2));
    }

    private static Empresa buildEmpresa(Long codigo, String descricao) {
        Empresa empresa = new Empresa();
        empresa.setCodigo(codigo);
        empresa.setDescricao(descricao);
        return empresa;
    }

    private static Agenda buildAgenda(Long codigo, Empresa empresa) {
        Agenda agenda = new Agenda();
        agenda.setCodigo(codigo);
        agenda.setEmpresa(empresa);
        return agenda;
    }

    private static AgendaGrade buildAgendaGrade(Long codigo, Agenda agenda) {
        AgendaGrade agendaGrade = new AgendaGrade();
        agendaGrade.setCodigo(codigo);
        agendaGrade.setAgenda(agenda);
        agendaGrade.setData(DataUtil.getDataAtual());
        return agendaGrade;
    }

    private static AgendaGradeAtendimentoDTO buildAgendaGradeAtendimentoDto(Long codigo, AgendaGrade agendaGrade) {
        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = new AgendaGradeAtendimentoDTO();
        AgendaGradeAtendimento agendaGradeAtendimento = new AgendaGradeAtendimento();

        agendaGradeAtendimento.setCodigo(codigo);
        agendaGradeAtendimento.setAgendaGrade(agendaGrade);
        agendaGradeAtendimentoDTO.setAgendaGradeAtendimento(agendaGradeAtendimento);
        return agendaGradeAtendimentoDTO;
    }

    private static SolicitacaoAgendamento buildSolicitacao(Empresa unidadeOrigem, Empresa unidadeSolicitante) {
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setEmpresa(unidadeSolicitante);
        solicitacaoAgendamento.setUnidadeOrigem(unidadeOrigem);
        return solicitacaoAgendamento;
    }

    private static Procedimento buildProcedimento(Long codigo, String descricao) {
        Procedimento procedimento = new Procedimento();
        procedimento.setCodigo(codigo);
        procedimento.setDescricao(descricao);
        return procedimento;
    }

    private static ExameProcedimento buildExameProcedimento(Long codigo) {
        ExameProcedimento exameProcedimento = new ExameProcedimento();
        exameProcedimento.setCodigo(codigo);
        return exameProcedimento;
    }

    private static SolicitacaoAgendamentoExame buildSolicitacaoAgendamentoExame(ExameProcedimento exameProcedimento) {
        SolicitacaoAgendamentoExame solicitacaoAgendamentoExame = new SolicitacaoAgendamentoExame();
        solicitacaoAgendamentoExame.setExameProcedimento(exameProcedimento);
        return solicitacaoAgendamentoExame;
    }

    private static ExamePrestadorProcedimento buildExamePrestadorProcedimento(ExameProcedimento exameProcedimento, ExamePrestador examePrestador) {
        ExamePrestadorProcedimento examePrestadorProcedimento = new ExamePrestadorProcedimento();
        examePrestadorProcedimento.setExameProcedimento(exameProcedimento);
        examePrestadorProcedimento.setExamePrestador(examePrestador);
        return examePrestadorProcedimento;
    }

    private static ExamePrestador buildExamePrestador(Empresa empresa) {
        ExamePrestador examePrestador = new ExamePrestador();
        examePrestador.setPrestador(empresa);
        return examePrestador;
    }
}
