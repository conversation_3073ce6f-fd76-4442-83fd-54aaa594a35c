package br.com.celk.agendamento.agendamentofilaespera;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Calendar;
import java.util.Date;

import static java.util.Calendar.*;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BOFactory.class, BuildDiaLimiteAgendamentoListaEspera.class})
public class RegraDiasLimiteAgendamentoListaEsperaTest {

    @Mock
    private CommomFacade commomFacade;

    @Mock
    private IParameterModuleContainer paramContainer;

    private static final String  DIAS_LIMITE_AGENDAMENTO_LISTA_ESPERA = "diasLimiteAgendamentoListasEspera";
    private RegraDiasLimiteAgendamentoListaEspera regraDiasLimiteAgendamentoListaEspera;
    private Date dia14Setembro;

    @Before
    public void setup() throws DAOException {
        PowerMockito.mockStatic(BOFactory.class);
        PowerMockito.mockStatic(BuildDiaLimiteAgendamentoListaEspera.class);
        dia14Setembro = this.getData(14);

        when(BOFactory.getBO(CommomFacade.class)).thenReturn(commomFacade);
        when(commomFacade.modulo(Modulos.AGENDAMENTO)).thenReturn(paramContainer);
        regraDiasLimiteAgendamentoListaEspera = new RegraDiasLimiteAgendamentoListaEspera(new Empresa(1L));
    }

    @Test
    public void dataAgendaGradeDeveSerMenorDataLimite() {
        Date dia04Setembro = this.getData(4);

        when(paramContainer.getParametro(1L, null, DIAS_LIMITE_AGENDAMENTO_LISTA_ESPERA)).thenReturn(10L);
        when(BuildDiaLimiteAgendamentoListaEspera.diasLimiteAgendamentoListaEspera(10)).thenReturn(dia14Setembro);

        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = buildAgendaGrade(dia04Setembro);
        boolean dataAgendaMenorDataLimite = regraDiasLimiteAgendamentoListaEspera.isDataAgendaMenorDataLimite(agendaGradeAtendimentoDTO);

        assertTrue("data agenda grade deve ser menor que data limite", dataAgendaMenorDataLimite);
    }

    @Test
    public void dataAgendaGradeDeveSerIgualDataLimite() {
        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = buildAgendaGrade(dia14Setembro);

        when(paramContainer.getParametro(1L, null, DIAS_LIMITE_AGENDAMENTO_LISTA_ESPERA)).thenReturn(10L);
        when(BuildDiaLimiteAgendamentoListaEspera.diasLimiteAgendamentoListaEspera(10)).thenReturn(dia14Setembro);

        boolean dataAgendaMenorDataLimite = regraDiasLimiteAgendamentoListaEspera.isDataAgendaMenorDataLimite(agendaGradeAtendimentoDTO);
        assertTrue("data agenda grade deve ser igual a data limite", dataAgendaMenorDataLimite);
    }

    @Test
    public void dataAgendaGradeDeveSerMaiorDataLimiteCenario1() {
        Date dia15Setembro = this.getData(15);

        when(paramContainer.getParametro(1L, null, DIAS_LIMITE_AGENDAMENTO_LISTA_ESPERA)).thenReturn(10L);
        when(BuildDiaLimiteAgendamentoListaEspera.diasLimiteAgendamentoListaEspera(10)).thenReturn(dia14Setembro);

        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = buildAgendaGrade(dia15Setembro);
        boolean dataAgendaMenorDataLimite = regraDiasLimiteAgendamentoListaEspera.isDataAgendaMenorDataLimite(agendaGradeAtendimentoDTO);
        assertFalse("data agenda grade deve ser maior que data limite", dataAgendaMenorDataLimite);
    }

    @Test
    public void dataAgendaGradeDeveSerMaiorDataLimiteCenario2() {
        Date dia15Setembro = this.getData(15);

        when(paramContainer.getParametro(1L, null, DIAS_LIMITE_AGENDAMENTO_LISTA_ESPERA)).thenReturn(10L);
        when(BuildDiaLimiteAgendamentoListaEspera.diasLimiteAgendamentoListaEspera(10)).thenReturn(null);

        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = buildAgendaGrade(dia15Setembro);
        boolean dataAgendaMenorDataLimite = regraDiasLimiteAgendamentoListaEspera.isDataAgendaMenorDataLimite(agendaGradeAtendimentoDTO);
        assertFalse("data agenda grade deve ser maior que data limite", dataAgendaMenorDataLimite);
    }

    private AgendaGradeAtendimentoDTO buildAgendaGrade(Date data) {
        AgendaGrade agendaGrade = new AgendaGrade();
        agendaGrade.setData(data);

        AgendaGradeAtendimento agendaGradeAtendimento = new AgendaGradeAtendimento();
        agendaGradeAtendimento.setAgendaGrade(agendaGrade);

        AgendaGradeAtendimentoDTO agendaGradeAtendimentoDTO = new AgendaGradeAtendimentoDTO();
        agendaGradeAtendimentoDTO.setAgendaGradeAtendimento(agendaGradeAtendimento);

        return agendaGradeAtendimentoDTO;
    }

    private Date getData(int dia) {
        Calendar data = Calendar.getInstance();
        data.set(DAY_OF_MONTH, dia);
        data.set(MONTH, Calendar.SEPTEMBER);
        data.set(YEAR, 2020);
        return Data.adjustRangeHour(data.getTime()).getDataInicial();
    }
}