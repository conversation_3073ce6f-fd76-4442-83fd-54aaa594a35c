# syntax = docker/dockerfile:1.1.5-experimental
FROM ubuntu:18.04 AS builder

ENV DEBIAN_FRONTEND=noninteractive
ENV LANG="pt_BR.UTF-8"
RUN apt-get -qq update &&\
    apt-get install -qq -y awscli ruby-dev ruby maven ruby-compass unzip \
    compass-blueprint-plugin locales && \
    rm -rf /var/lib/apt/lists/* && \
    localedef -i pt_BR -c -f UTF-8 -A /usr/share/locale/locale.alias pt_BR.UTF-8

#TODO tirar esssas credenciais hardcoded
ENV AWS_ACCESS_KEY_ID=********************
ENV AWS_SECRET_ACCESS_KEY="NIZ85Mz0UB2BB0/uMjub0MgOzPSznIgUpuztXpuA"

RUN --mount=type=bind,target=/downloads/,source=/export/downloads/,rw,from=nexus.apoio.celk.info:5001/celksistemas/saude-server/build-cache:latest \
    aws s3 sync s3://net.celk.apoio.downloads/ /downloads/ --size-only --exclude="*" \
        --include="tzdata2019b-rearguard.tar.gz" \
        --include="tzupdater-2.2.0.jar" \
        --include="wildfly-9.0.2.Final.tar.gz" \
        --include="jdk-8u251-linux-x64.tar.gz" &&\
    mkdir jdk &&\
    tar -xzf /downloads/jdk-8u251-linux-x64.tar.gz --strip-components=1 -C ./jdk &&\
    # a pasta export contem arquivos que serão consumidos em outras layers
    mkdir -p /export/wildfly/ &&\
    mkdir -p /export/java8/ &&\
    cp downloads/tzdata2019b-rearguard.tar.gz /export/tzdata.tar.gz &&\
    cp downloads/tzupdater-2.2.0.jar /export/tzupdater.jar &&\
    tar xf /downloads/wildfly-9.0.2.Final.tar.gz --strip-components=1 -C /export/wildfly/ &&\
    tar xf /downloads/jdk-8u251-linux-x64.tar.gz --strip-components=1 -C /export/java8/
    

COPY deploy/docker/m2-settings.xml /root/.m2/settings.xml
COPY resources /saude/resources
COPY saude-app-cidadao-service /saude/saude-app-cidadao-service
COPY saude-bo-ejb /saude/saude-bo-ejb
COPY saude-command /saude/saude-command
COPY saude-connect /saude/saude-connect
COPY saude-consumer-ejb /saude/saude-consumer-ejb
COPY saude-core /saude/saude-core
COPY saude-ear /saude/saude-ear
COPY saude-ear-processo /saude/saude-ear-processo
COPY saude-ear-sistema /saude/saude-ear-sistema
COPY saude-ejb-utils /saude/saude-ejb-utils
COPY saude-indra-connect /saude/saude-indra-connect
COPY saude-integ-laboratorio /saude/saude-integ-laboratorio
COPY saude-integrating-modules /saude/saude-integrating-modules
COPY saude-processos-service /saude/saude-processos-service
COPY saude-provider /saude/saude-provider
COPY saude-sistema-healthcheck /saude/saude-sistema-healthcheck 
COPY saude-sms /saude/saude-sms
COPY saude-utils /saude/saude-utils
COPY saude-web-wicket /saude/saude-web-wicket
COPY saude-wicket /saude/saude-wicket
COPY versionamento /saude/versionamento
COPY pom.xml /saude/pom.xml
ENV JAVA_HOME=/jdk
WORKDIR /saude/


RUN --mount=type=bind,target=/root/.m2/repository,source=/export/.m2/repository,rw,from=nexus.apoio.celk.info:5001/celksistemas/saude-server/build-cache:latest \
    --mount=type=bind,target=/downloads/,source=/export/downloads/,rw,from=nexus.apoio.celk.info:5001/celksistemas/saude-server/build-cache:latest \
    ulimit -n 10000 &&\
    mvn clean install -T4 -Dmaven.test.skip=true &&\
    cp /saude/saude-ear-sistema/target/saude-ear.ear /export/sistema.ear &&\
    cp /saude/saude-ear-processo/target/saude-ear.ear /export/processo.ear &&\
    unzip /saude/saude-ear-sistema/target/saude-ear.ear "saude-core*.jar" -d jars &&\
    cp jars/saude-core.jar /export/saude-core.jar &&\
    mvn clean &&\
    mkdir -p /export/.m2 &&\
    rm -r /root/.m2/repository/br/com/celk &&\
    cp -r /root/.m2/repository /export/.m2/ &&\
    cp -r /downloads/ /export/


#######################################################################################
FROM ubuntu:20.04 as saude-runner-base

ENV DEBIAN_FRONTEND noninteractive
ENV LANG "pt_BR.UTF-8"

RUN --mount=type=cache,target=/var/cache/apt,sharing=private \
    echo "ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula select true" | debconf-set-selections &&\
    apt-get update -qq && \
    apt-get install -y curl unzip ssh locales fontconfig ttf-mscorefonts-installer && \
    apt-get install -qq -y --no-install-recommends libreoffice libreoffice-java-common && \
    apt-get remove -y --autoremove --purge openjdk* &&\
    rm -rf /var/lib/apt/lists/* && \
    localedef -i pt_BR -c -f UTF-8 -A /usr/share/locale/locale.alias pt_BR.UTF-8

ENV JBOSS_HOME=/opt/jboss
COPY --from=builder /export/wildfly ${JBOSS_HOME}
COPY --from=builder /export/java8 /usr/lib/jvm
RUN update-alternatives --install "/usr/bin/java" "java" "/usr/lib/jvm/jre/bin/java" 2 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/lib/jvm/bin/javac" 2
    
COPY deploy/docker/opt /opt

COPY --from=builder /export/tzupdater.jar ./
COPY --from=builder /export/tzdata.tar.gz ./
RUN ulimit -n 10000 &&\
    java -jar tzupdater.jar -f -l file://$PWD/tzdata.tar.gz &&\
    rm tzupdater.jar tzdata.tar.gz


ENV LOCAL_TIME_FILE=America/Sao_Paulo
USER root
CMD ["/opt/jboss/bin/rungem.sh"]
EXPOSE 8080

# # Remove OpenJD
# apt-get clean

#######################################################################################
FROM saude-runner-base as sistema
COPY --from=builder /export/sistema.ear /opt/jboss/standalone/deployments/saude-ear.ear


#######################################################################################
FROM saude-runner-base as processo
COPY --from=builder /export/processo.ear /opt/jboss/standalone/deployments/saude-ear.ear

#######################################################################################
FROM alpine:3.12.0 as init

ENV DEBIAN_FRONTEND=noninteractive
RUN apk add --no-cache postgresql-client aws-cli bash

COPY deploy/docker/config-init.sh /config-init.sh

CMD ["/config-init.sh"]

#######################################################################################
FROM ubuntu:20.04 as flyway

ENV DEBIAN_FRONTEND=noninteractive

COPY --from=builder /export/java8 /usr/lib/jvm
RUN update-alternatives --install "/usr/bin/java" "java" "/usr/lib/jvm/jre/bin/java" 2 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/lib/jvm/bin/javac" 2

WORKDIR /flyway
COPY --from=builder /export/saude-core.jar /flyway/jars/saude-core.jar
COPY deploy/docker/flyway /flyway/

CMD ["/flyway/flyway.sh"]
