#!/bin/bash

set -ex

requiredvars="DBURL DBPORT DBUSERNAME DBPASSWORD DBNAME"
for i in $requiredvars; do
    if [ -z "${!i}" ]; then
        echo "$i env var is not set!"
        exit 1
    fi
done

# java -classpath bin/flyway-commandline-2.0.2.jar:bin/flyway-core-2.0.2.jar \
#     com.googlecode.flyway.commandline.Main \
#     -url=****************************************?user=$DBUSERNAME\&password=$DBPASSWORD validate

java -classpath bin/flyway-commandline-2.0.2.jar:bin/flyway-core-2.0.2.jar \
    com.googlecode.flyway.commandline.Main \
    -url=**************************************** \
    -user=$DBUSERNAME \
    -password="$DBPASSWORD" \
    info

java -classpath bin/flyway-commandline-2.0.2.jar:bin/flyway-core-2.0.2.jar \
    com.googlecode.flyway.commandline.Main \
    -url=**************************************** \
    -user=$DBUSERNAME \
    -password="$DBPASSWORD" \
    migrate

