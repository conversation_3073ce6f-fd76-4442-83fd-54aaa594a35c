# Debug: Token reCAPTCHA v3 Vazio

## 🔍 Situação Atual

Pelos logs, vemos que:
- ✅ **Parâmetro encontrado**: `Found parameter: recaptcha_token_reCaptcha = ...`
- ❌ **Token vazio**: `Completed: false`

Isso indica que o token está sendo enviado, mas está vazio ou contém apenas espaços.

## 🔧 Debug Melhorado

Implementei logs mais detalhados que mostrarão:

### **Logs Esperados Agora:**
```
DEBUG ReCaptchaV3 - Found parameter: recaptcha_token_reCaptcha = ABC123... (ou EMPTY_OR_NULL)
DEBUG ReCaptchaV3 - Parameter length: 1234
DEBUG ReCaptchaV3 - Parameter trimmed length: 1234
DEBUG ReCaptchaV3 - Token for recaptcha_token_reCaptcha:
  Raw token: 'ABC123...'
  Token length: 1234
  Token trimmed: 'ABC123...'
  Token trimmed length: 1234
  Completed: true
```

### **Se Token Estiver Vazio:**
```
DEBUG ReCaptchaV3 - Found parameter: recaptcha_token_reCaptcha = EMPTY_OR_NULL
DEBUG ReCaptchaV3 - Parameter length: 0 (ou null)
DEBUG ReCaptchaV3 - Parameter trimmed length: 0 (ou null)
DEBUG ReCaptchaV3 - Token for recaptcha_token_reCaptcha:
  Raw token: '' (ou null)
  Token length: 0 (ou null)
  Token trimmed: ''
  Token trimmed length: 0
  Completed: false
```

## 🚨 Possíveis Causas do Token Vazio

### **1. JavaScript Não Executando**
**Sintomas:**
- Token sempre vazio
- Nenhum log no console do navegador

**Verificação:**
1. Abra DevTools (F12) → Console
2. Procure por logs do reCAPTCHA v3
3. Se não houver logs, o JavaScript não está executando

**Soluções:**
- Verificar se script do Google está carregando
- Verificar se chave do site está correta
- Verificar se domínio está autorizado

### **2. Campo Oculto Não Sendo Atualizado**
**Sintomas:**
- Logs mostram token recebido no JavaScript
- Mas campo oculto permanece vazio

**Verificação:**
```javascript
// No console do navegador
var field = document.getElementById('recaptcha_token_reCaptcha');
console.log('Field found:', field);
console.log('Field value:', field ? field.value : 'not found');
```

**Soluções:**
- Verificar se ID do campo está correto
- Verificar se campo está dentro do formulário
- Verificar se JavaScript está encontrando o elemento

### **3. Formulário Sendo Resetado**
**Sintomas:**
- Token definido corretamente no JavaScript
- Mas campo vazio no momento do submit

**Verificação:**
- Verificar se há código que limpa o formulário
- Verificar se há múltiplos submits
- Verificar se campo está sendo removido/recriado

### **4. Timing Issues**
**Sintomas:**
- Às vezes funciona, às vezes não
- Token vazio em submits rápidos

**Soluções:**
- Aumentar timeout do JavaScript
- Executar reCAPTCHA antes do submit
- Adicionar verificação de token antes do submit

## 🔧 Testes para Fazer

### **Teste 1: Verificar Console do Navegador**
1. Abra a página
2. Abra DevTools (F12) → Console
3. Procure por logs como:
   ```
   reCAPTCHA v3 token received: 03AGdBq26...
   Looking for hidden field with ID: recaptcha_token_reCaptcha
   Hidden field found: YES
   reCAPTCHA v3 token set successfully for action: search
   ```

### **Teste 2: Verificar Campo Oculto Manualmente**
```javascript
// Execute no console do navegador
var field = document.getElementById('recaptcha_token_reCaptcha');
console.log('Field:', field);
console.log('Value:', field ? field.value : 'not found');
console.log('Value length:', field && field.value ? field.value.length : 0);
```

### **Teste 3: Executar reCAPTCHA Manualmente**
```javascript
// Execute no console do navegador
if (typeof grecaptcha !== 'undefined') {
    grecaptcha.ready(function() {
        grecaptcha.execute('SUA_SITE_KEY', {action: 'test'}).then(function(token) {
            console.log('Manual token:', token);
            console.log('Token length:', token.length);
            
            var field = document.getElementById('recaptcha_token_reCaptcha');
            if (field) {
                field.value = token;
                console.log('Token set manually');
            }
        });
    });
}
```

### **Teste 4: Verificar Timing**
```javascript
// Execute no console antes de submeter
setTimeout(function() {
    var field = document.getElementById('recaptcha_token_reCaptcha');
    console.log('Field value after delay:', field ? field.value : 'not found');
}, 2000);
```

## 📋 Checklist de Verificação

### **Frontend**
- [ ] Script do Google carregando (`https://www.google.com/recaptcha/api.js?render=SITE_KEY`)
- [ ] Logs do reCAPTCHA aparecendo no console
- [ ] Token sendo recebido do Google
- [ ] Campo oculto sendo encontrado
- [ ] Token sendo definido no campo
- [ ] Campo mantendo valor até o submit

### **Backend**
- [ ] Parâmetro sendo encontrado no request
- [ ] Valor do parâmetro não sendo nulo
- [ ] Valor do parâmetro não sendo vazio
- [ ] Valor do parâmetro não sendo apenas espaços

### **Configuração**
- [ ] Chave do site correta
- [ ] Domínio autorizado no Google reCAPTCHA
- [ ] reCAPTCHA v3 configurado (não v2)
- [ ] Componente habilitado

## 🎯 Próximos Passos

1. **Execute a página** com o debug melhorado
2. **Colete os novos logs** detalhados
3. **Execute os testes** no console do navegador
4. **Identifique** qual das causas está acontecendo
5. **Aplique a solução** específica

## 📞 Informações Necessárias

Para ajudar a resolver, preciso dos seguintes logs:

### **Logs do Servidor:**
```
DEBUG ReCaptchaV3 - Found parameter: recaptcha_token_reCaptcha = ???
DEBUG ReCaptchaV3 - Parameter length: ???
DEBUG ReCaptchaV3 - Token for recaptcha_token_reCaptcha:
  Raw token: ???
  Token length: ???
  Completed: ???
```

### **Logs do Navegador:**
```
reCAPTCHA v3 token received: ???
Hidden field found: ???
reCAPTCHA v3 token set successfully: ???
```

### **Teste Manual:**
```javascript
// Resultado do teste manual no console
var field = document.getElementById('recaptcha_token_reCaptcha');
console.log('Field value:', field ? field.value : 'not found');
```

Com essas informações, posso identificar exatamente onde está o problema! 🔍
