<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT300000UV04.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT240003UV02.xsd"/>
   <xs:include schemaLocation="COCT_MT260003UV.xsd"/>
   <xs:complexType name="COCT_MT300000UV04.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="prescriberRole" type="COCT_MT300000UV04.PrescriberRole" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Destination">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="DST"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.HealthCareProvider">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="PROV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.InFulfillmentOf">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substanceAdministrationOrder"
                     type="COCT_MT300000UV04.SubstanceAdministrationOrder"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipFulfills" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Location">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.ManufacturedMaterialKind">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="formCode" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminerDetermined" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_PQ_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="manufacturedMaterialKind"
                     type="COCT_MT300000UV04.ManufacturedMaterialKind"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Origin">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="ORG"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Performer1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="healthCareProvider" type="COCT_MT300000UV04.HealthCareProvider"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationPhysicalPerformer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Performer2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substitutionRole" type="COCT_MT300000UV04.SubstitutionRole"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationPhysicalPerformer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.PertinentInformation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substitution" type="COCT_MT300000UV04.Substitution" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipPertains" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.PertinentInformation2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substitution" type="COCT_MT300000UV04.Substitution" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipPertains" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.PrescriberPerson">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="3"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.PrescriberRole">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="playingPrescriberPerson" type="COCT_MT300000UV04.PrescriberPerson"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Product">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT300000UV04.ManufacturedProduct"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Reason">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="supplyOrder" type="COCT_MT300000UV04.SupplyOrder" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Reason2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substanceAdministrationIntent"
                     type="COCT_MT300000UV04.SubstanceAdministrationIntent"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Subject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="detectedMedicationIssue" type="COCT_MT260003UV.DetectedMedicationIssue"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="SUBJ"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.SubstanceAdministrationIntent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="inFulfillmentOf" type="COCT_MT300000UV04.InFulfillmentOf" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="pertinentInformation" type="COCT_MT300000UV04.PertinentInformation2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="subjectOf" type="COCT_MT300000UV04.Subject" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SBADM"/>
      <xs:attribute name="moodCode" type="ActMoodIntent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.SubstanceAdministrationOrder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="author" type="COCT_MT300000UV04.Author" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="reason" type="COCT_MT300000UV04.Reason" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="pertinentInformation" type="COCT_MT300000UV04.PertinentInformation"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SBADM"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="RQO"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.Substitution">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="reasonCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="performer" type="COCT_MT300000UV04.Performer2" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SUBST"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.SubstitutionRole">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.SupplyEvent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="1" maxOccurs="1"/>
         <xs:element name="expectedUseTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="product" type="COCT_MT300000UV04.Product" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="performer" type="COCT_MT300000UV04.Performer1" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="origin" type="COCT_MT300000UV04.Origin" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="destination" type="COCT_MT300000UV04.Destination" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="location" type="COCT_MT300000UV04.Location" minOccurs="1" maxOccurs="1"/>
         <xs:element name="reasonOf" type="COCT_MT300000UV04.Reason2" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassSupply" use="required"/>
      <xs:attribute name="moodCode" type="x_ActMoodIntentEvent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT300000UV04.SupplyOrder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="repeatNumber" type="IVL_INT" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassSupply" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="RQO"/>
   </xs:complexType>
</xs:schema>