<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT260003UV.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:complexType name="COCT_MT260003UV.AssignedEntity">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassAssignedEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedEntity" type="COCT_MT260003UV.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Consumable">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT260003UV.ManufacturedProduct"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="CSM"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Definition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="detectedMedicationIssueDefinition"
                     type="COCT_MT260003UV.DetectedMedicationIssueDefinition"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="optional" default="INST"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.DetectedMedicationIssue">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="value" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="definition" type="COCT_MT260003UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT260003UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="mitigatedBy" type="COCT_MT260003UV.Mitigates" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT260003UV.Subject" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="ALRT"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.DetectedMedicationIssueDefinition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="ALRT"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Location">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="pharmacy" type="COCT_MT260003UV.Pharmacy" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Management">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT260003UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.ManufacturedMaterialKind">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminerDetermined" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedMaterialKind" type="COCT_MT260003UV.ManufacturedMaterialKind"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Mitigates">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="management" type="COCT_MT260003UV.Management" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipMitigates" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.OtherMedication">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="consumable" type="COCT_MT260003UV.Consumable" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SBADM"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.OtherSupply">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expectedUseTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="location" type="COCT_MT260003UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassSupply" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Pharmacy">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="telecom" type="TEL" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="DSDLOC"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.SeverityObservation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="value" type="CE" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Subject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="severityObservation" type="COCT_MT260003UV.SeverityObservation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="SUBJ"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT260003UV.Subject2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:element name="otherMedication" type="COCT_MT260003UV.OtherMedication" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="otherSupply" type="COCT_MT260003UV.OtherSupply" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="SUBJ"/>
   </xs:complexType>
</xs:schema>