<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type QUQI_MT000001UV01.
* Source information:
*     Rendered by: RoseTree 4.2.7
*     Rendered on: 
* HMD was rendered into XML using software provided to HL7 by Beeler Consulting LLC.
 HMD to MIF Transform: $Id: RoseTreeHmdToMIFStaticModel.xsl,v 1.15 2007/10/19 05:55:13 wbeeler Exp $
  Base transform: $Id: ConvertBase.xsl,v 1.5 2007/10/19 05:55:13 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
  HTML To MIF markup: $Id: HtmlToMIFMarkup.xsl,v 1.4 2007/03/20 02:48:49 wbeeler Exp $
 Flat to Serialization Transform: $Id: MIFStaticModelFlatToSerialization.xsl,v 1.5 2007/03/20 02:48:50 wbeeler Exp $
 Fix Names Transform: $Id: FixMifNames.xsl,v 1.8 2007/03/20 02:48:49 wbeeler Exp $
  Base transform: $Id: ConvertBase.xsl,v 1.5 2007/10/19 05:55:13 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:
HMD was rendered into XML using software provided to HL7 by Beeler Consulting LLC.
 HMD to MIF Transform: $Id: RoseTreeHmdToMIFStaticModel.xsl,v 1.15 2007/10/19 05:55:13 wbeeler Exp $
  Base transform: $Id: ConvertBase.xsl,v 1.5 2007/10/19 05:55:13 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
  HTML To MIF markup: $Id: HtmlToMIFMarkup.xsl,v 1.4 2007/03/20 02:48:49 wbeeler Exp $
 Flat to Serialization Transform: $Id: MIFStaticModelFlatToSerialization.xsl,v 1.5 2007/03/20 02:48:50 wbeeler Exp $
 Fix Names Transform: $Id: FixMifNames.xsl,v 1.8 2007/03/20 02:48:49 wbeeler Exp $
  Base transform: $Id: ConvertBase.xsl,v 1.5 2007/10/19 05:55:13 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT090300UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT090100UV01.xsd"/>
   <xs:include schemaLocation="MCAI_MT900001UV01.xsd"/>
   <xs:complexType name="QUQI_MT000001UV01.AuthorOrPerformer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="noteText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="assignedDevice" type="COCT_MT090300UV01.AssignedDevice" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="assignedPerson" type="COCT_MT090100UV01.AssignedPerson" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="x_ParticipationAuthorPerformer" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AP"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.ControlActProcess">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="reasonCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="overseer" type="QUQI_MT000001UV01.Overseer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="authorOrPerformer" type="QUQI_MT000001UV01.AuthorOrPerformer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="QUQI_MT000001UV01.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="informationRecipient" type="QUQI_MT000001UV01.InformationRecipient"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reasonOf" type="QUQI_MT000001UV01.Reason" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="queryContinuation" type="QUQI_MT000001UV01.QueryContinuation"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassControlAct" use="required"/>
      <xs:attribute name="moodCode" type="x_ActMoodIntentEvent" use="required"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.DataEnterer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedPerson" type="COCT_MT090100UV01.AssignedPerson" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="ENT"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AP"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.InformationRecipient">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedPerson" type="COCT_MT090100UV01.AssignedPerson" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationInformationRecipient" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AP"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.Overseer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="noteText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedPerson" type="COCT_MT090100UV01.AssignedPerson" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="x_ParticipationVrfRespSprfWit" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AP"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.QueryContinuation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="queryId" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="startResultNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="continuationQuantity" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="QUQI_MT000001UV01.Reason">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="detectedIssueEvent" type="MCAI_MT900001UV01.DetectedIssueEvent"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional"/>
   </xs:complexType>
</xs:schema>