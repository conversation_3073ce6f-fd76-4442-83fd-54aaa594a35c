package br.com.celk.template.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.annotation.template.Template;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.CustomColorTableRow;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.Column;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilancia;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.link.ExternalLink;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
@Template
public abstract class ConsultaVigilanciaPage<T extends Serializable, E> extends BasePageVigilancia {

    private boolean exibeTable2;
    private IPagerProvider pagerProvider;
    private IPagerProvider pagerProvider2;

    private PageableTable<T> pageableTable;
    private PageableTable<T> pageableTable2;

    private List<IColumn> columns;
    private ArrayList<IColumn> columns2;

    private Component linkNovo;
    private AbstractAjaxButton btnVoltar;
    private RepeatingView controls;
    private ProcurarButton<E> btnProcurar;
    private boolean procurarAoAbrir = true;
    private boolean exibeExpandir;
    private boolean exibeBtnVoltar;
    private WebMarkupContainer divExpandir;
    private WebMarkupContainer containerTable2;

    public ConsultaVigilanciaPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    public ConsultaVigilanciaPage(IModel<?> model) {
        super(model);
        init();
    }

    public ConsultaVigilanciaPage() {
        super();
        init();
    }

    public ConsultaVigilanciaPage(boolean exibeTable2) {
        super();
        this.exibeTable2 = exibeTable2;
        init();
    }

    private void init(){
        Form form = new Form("form");

        form.add(pageableTable = newPageableTable("table", getColumns(), getPagerProvider(), 10));

        form.add(containerTable2 = new WebMarkupContainer("containerTable2"));
        containerTable2.setOutputMarkupId(true);
        containerTable2.add(pageableTable2 = newPageableTable2("table2", getColumns2(), getPagerProvider2(), 10));
        containerTable2.setVisible(exibeTable2);
        if(exibeTable2){
            getPageableTable2().populate();
        }

        form.add(btnProcurar = new ProcurarButton<E>("btnProcurar", pageableTable) {

            @Override
            public E getParam() {
                return getParameters();
            }
        });

        form.add(controls = new RepeatingView("controls"));

        controls.add(linkNovo = criarLinkNovo(controls.newChildId()));

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new VigilanciaHomePage());
            }
        }.setDefaultFormProcessing(false));

        linkNovo.add(new AttributeModifier("class", "doc-new"));
        linkNovo.add(new AttributeModifier("value", BundleManager.getString("novo")));

        initForm(form);

        form.add(divExpandir = new WebMarkupContainer("divExpandir"));
        divExpandir.setOutputMarkupId(true);
        ExternalLink linkExpandir = new ExternalLink("linkExpandir",(String) null);
        divExpandir.add(linkExpandir);
        divExpandir.setVisible(exibeExpandir);

        add(form);

    }

    public PageableTable<T> newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage){
        return new PageableTable(tableId, columns, pagerProvider){
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new CustomColorTableRow(id, index, model) {
                    @Override
                    public TableColorEnum getColor() {
                        return getColorLine(getRowObject());
                    }
                };
            }
        };
    }

    public PageableTable<T> newPageableTable2(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage){
        return new PageableTable(tableId, columns, pagerProvider){
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new CustomColorTableRow(id, index, model) {
                    @Override
                    public TableColorEnum getColor() {
                        return getColorLine2(getRowObject());
                    }
                };
            }
        };
    }

    protected TableColorEnum getColorLine(Serializable rowObject) {
        return TableColorEnum.PADRAO;
    }

    protected TableColorEnum getColorLine2(Serializable rowObject) {
        return TableColorEnum.PADRAO;
    }

    protected Component criarLinkNovo(String id){
        if (getPageParameters().isEmpty()) {
            return new BookmarkablePageLink(id, getCadastroPage());
        } else {
            return new BookmarkablePageLink(id, getCadastroPage(), getPageParameters());
        }
    }

    @Override
    protected void onBeforeRender() {
        if (procurarAoAbrir) {
            btnProcurar.procurar();
        }
        super.onBeforeRender();
    }

    public RepeatingView getControls() {
        return controls;
    }

    public abstract void initForm(Form form);

    public List<IColumn> getColumns(){
        if (this.columns == null) {
            this.columns = new ArrayList<IColumn>();
        }

        return getColumns(columns);
    }

    public List<IColumn> getColumns2(){
        if (this.columns2 == null) {
            this.columns2 = new ArrayList<IColumn>();
        }
        List<IColumn> columns2 = getColumns2(this.columns2);
        if(columns2.size() > 0) {
            return columns2;
        } else {
            columns2.add(new Column("default", null));
            return columns2;
        }
    }

    public abstract List<IColumn> getColumns(List<IColumn> columns);

    public List<IColumn> getColumns2(List<IColumn> columns){
        return columns;
    };

    public IPagerProvider getPagerProvider(){
        if (this.pagerProvider == null) {
            this.pagerProvider = getPagerProviderInstance();
        }

        return this.pagerProvider;
    }

    public IPagerProvider getPagerProvider2(){
        if (this.pagerProvider2 == null) {
            this.pagerProvider2 = getPagerProviderInstance2();
        }

        return this.pagerProvider2;
    }

    public PageableTable<T> getPageableTable() {
        return pageableTable;
    }

    public PageableTable<T> getPageableTable2() {
        return pageableTable2;
    }

    public Component getLinkNovo() {
        return linkNovo;
    }

    public void setProcurarAoAbrir(boolean procurarAoAbrir) {
        this.procurarAoAbrir = procurarAoAbrir;
    }

    public AbstractAjaxButton getBtnVoltar() {
        return btnVoltar;
    }

    public abstract IPagerProvider getPagerProviderInstance();

    public abstract IPagerProvider getPagerProviderInstance2();

    public abstract E getParameters();

    public abstract Class getCadastroPage();

    public void setExibeExpandir(boolean exibeExpandir) {
        this.exibeExpandir = exibeExpandir;
    }

    public void setExibeBtnVoltar(boolean exibeBtnVoltar) {
        this.exibeBtnVoltar = exibeBtnVoltar;
    }
}