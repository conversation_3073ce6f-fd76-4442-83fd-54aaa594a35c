package br.com.celk.template.breadcrumb;

import br.com.celk.annotation.template.Template;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.notification.NotificationPanel;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.TemplatePage;
import br.com.celk.template.annotation.PainelControle;
import java.util.LinkedList;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.html.TransparentWebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.AbstractLink;
import org.apache.wicket.markup.html.link.ExternalLink;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Template
public abstract class BreadCrumbPage extends TemplatePage implements INotificationPanel{

    private NotificationPanel notificationPanel;
    private Label nomePrograma;
    
    private List<BreadCrumbPage> crumbs;
    
    public BreadCrumbPage() {
        init(null);
    }
    
    public BreadCrumbPage(BreadCrumbPage originCrumb) {
        init(originCrumb);
    }

    public BreadCrumbPage(IModel<?> model, BreadCrumbPage originCrumb) {
        super(model);
        init(originCrumb);
    }

    public BreadCrumbPage(PageParameters parameters, BreadCrumbPage originCrumb) {
        super(parameters);
        init(originCrumb);
    }
    
    private void init(BreadCrumbPage originCrumb){
        crumbs = new LinkedList<BreadCrumbPage>();
        if (originCrumb!=null) {
            crumbs.addAll(originCrumb.getCrumbs());
        }
        crumbs.add(this);
        
        ListView<BreadCrumbPage> listView =new ListView<BreadCrumbPage>("breadcrumb", crumbs) {

            @Override
            protected void populateItem(ListItem<BreadCrumbPage> item) {
                final BreadCrumbPage crumb = item.getModelObject();
                if(item.getIndex()>0){
                    item.add(new Label("arrow", ">"));
                } else {
                    item.add(new Label("arrow"));
                }
                AbstractLink link = null;
                if (crumb!=BreadCrumbPage.this) {
                    item.add(link = new Link("link") {

                        @Override
                        public void onClick() {
                            setResponsePage(crumb);
                        }
                    });
                } else {
                    item.add(link = new ExternalLink("link", "#"));
                }
                link.add(new Label("label", crumb.getTituloPrograma()));
            }

        };
        
        TransparentWebMarkupContainer section = new TransparentWebMarkupContainer("section");
        
        section.add(nomePrograma = new Label("nomePrograma", getTituloPrograma()));

        section.add(notificationPanel = new NotificationPanel("notificationPanel"));

        if (getClass().isAnnotationPresent(PainelControle.class)) {
            section.add(new AttributeModifier("class", "with-sidebar"));
        }
        
        section.add(listView);
        
        add(section);
        
    }
    
    public List<BreadCrumbPage> getCrumbs() {
        return crumbs;
    }
    
    @Override
    public void info(AjaxRequestTarget target, String message){
        message(target, message, FeedbackMessage.INFO);
    }
    
    @Override
    public void warn(AjaxRequestTarget target, String message){
        message(target, message, FeedbackMessage.WARNING);
    }

    @Override
    public void error(AjaxRequestTarget target, String message){
        message(target, message, FeedbackMessage.ERROR);
    }

    @Override
    public void clearNotifications(AjaxRequestTarget target) {
        getSession().getFeedbackMessages().clear();
        updateNotificationPanel(target);
    }

    @Override
    public void message(AjaxRequestTarget target, String message, int lvl){
        getSession().getFeedbackMessages().add(new FeedbackMessage(notificationPanel, message, lvl));
        updateNotificationPanel(target);
    }
    
    @Override
    public void updateNotificationPanel(AjaxRequestTarget target){
        updateNotificationPanel(target, true);
    }
    
    @Override
    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop){
        target.add(notificationPanel);
        if (scrollToTop) {
            target.appendJavaScript(JScript.scrollToTop());
        }
    }

}
