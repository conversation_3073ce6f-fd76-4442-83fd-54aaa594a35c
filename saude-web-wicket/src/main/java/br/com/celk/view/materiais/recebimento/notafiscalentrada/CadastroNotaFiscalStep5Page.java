package br.com.celk.view.materiais.recebimento.notafiscalentrada;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.DetalhesActionLink;
import br.com.celk.component.action.link.RemoverActionLink;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.lote.entrada.LotesEntradaChooser;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.dialog.DlgConfirmacaoValorNota;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.dialog.DlgDetalhesLote;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.bo.materiais.recebimento.interfaces.dto.NotaFiscalOrdemCompraDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalHelper;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;
import org.wicketstuff.annotation.mount.MountPath;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.text.DecimalFormat;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
@MountPath(value = "materiais/recebimento/notaFiscal/cadastro/step5")
public class CadastroNotaFiscalStep5Page extends BasePage {

    private CompoundPropertyModel<RegistroItemNotaFiscal> modelItem;
    private Table<RegistroItemNotaFiscal> tblItens;
    private MultiSelectionTableOld<OrdemCompraItem> tblOrdemCompra;
    private DoubleField txtValorTotalTabela;
    private DoubleField txtValorItem;
    private DoubleField txtPrecoUnitario;
    private DoubleField txtQuantidade;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private WebMarkupContainer containerItem;
    private DlgConfirmacaoValorNota dlgConfirmacaoValorNota;
    private LotesEntradaChooser lotesEntradaChooser;
    private Map<Object, List<OrdemCompraEloNota>> ordemCompraItemMap;
    private AjaxFormComponentUpdatingBehavior quantidadeBehavior = new AjaxFormComponentUpdatingBehavior("onblur") {

        @Override
        protected void onUpdate(AjaxRequestTarget target) {
            calcularTotal(target);
        }
    };

    private RegistroNotaFiscal registroNotaFiscal;
    private List<NotaFiscalOrdemCompraDTO> itens = new ArrayList<NotaFiscalOrdemCompraDTO>();
    private List<OrdemCompraItem> ordemCompraList = new ArrayList<OrdemCompraItem>();
    private List<MovimentoGrupoEstoqueItemDTO> lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private String bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos;
    private InputField txtUnidade;
    @Deprecated
    public CadastroNotaFiscalStep5Page() {
    }

    public CadastroNotaFiscalStep5Page(RegistroNotaFiscal registroNotaFiscal) throws DAOException {
        this.bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos");
        this.registroNotaFiscal = registroNotaFiscal;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(registroNotaFiscal));

        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_NUMERO_NOTA_FISCAL)));
        form.add(new DisabledInputField(VOUtils.montarPath(RegistroNotaFiscal.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO)));

        containerItem = new WebMarkupContainer("containerItem", modelItem = new CompoundPropertyModel(new RegistroItemNotaFiscal()));
        containerItem.setOutputMarkupId(true);

        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(RegistroItemNotaFiscal.PROP_PRODUTO) {

            @Override
            public String[] getPropertiesLoad() {
                return VOUtils.mergeProperties(new HQLProperties(Produto.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_CODIGO),
                            VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
                            VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO),
                            VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO),
                            VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_CONTROLA_GRUPO_ESTOQUE),});
            }

        });
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setEmpresas(Arrays.asList(br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa()));
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                carregarUnidade(target,object);
                consultarOrdemCompra(target, object);
            }
        });
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                txtUnidade.setComponentValue(null);
                target.add(txtUnidade);
                limparOrdemCompraList(target);
                txtPrecoUnitario.setEnabled(true);
                txtValorItem.setEnabled(true);
                txtValorItem.setComponentValue(0D);
                txtQuantidade.setComponentValue(0D);
                txtPrecoUnitario.setComponentValue(0D);
                target.add(txtValorItem);
                target.add(txtPrecoUnitario);
                target.add(txtQuantidade);
            }
        });
        containerItem.add(txtUnidade= new InputField(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO,Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        txtUnidade.setEnabled(false);
        containerItem.add(txtQuantidade = new DoubleField(RegistroItemNotaFiscal.PROP_QUANTIDADE).setMDec(0));
        containerItem.add(lotesEntradaChooser = new LotesEntradaChooser("lotes", new PropertyModel<List<MovimentoGrupoEstoqueItemDTO>>(this, "lotes"), true) {
            @Override
            public void confirmarAction(AjaxRequestTarget target) {
                calcularTotal(target);
            }
        });
        lotesEntradaChooser.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto)
                .setAutoCompleteConsultaProduto(autoCompleteConsultaProduto)
                .setTxtQuantidade(txtQuantidade)
                .registerEvents();

        containerItem.add(txtPrecoUnitario = new DoubleField(RegistroItemNotaFiscal.PROP_PRECO_UNITARIO).setMDec(4));
        containerItem.add(txtValorItem = new DoubleField(RegistroItemNotaFiscal.PROP_VALOR_ITEM));
        containerItem.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarItem(target);
            }

        });

        containerItem.add(tblOrdemCompra = new MultiSelectionTableOld("tblOrdemCompra", getColumnsOrdemCompra(), getCollectionProviderOrdemCompra()));
        tblOrdemCompra.setScrollY("90px");
        tblOrdemCompra.setScrollX("600px");
        tblOrdemCompra.setScrollCollapse(true);
        tblOrdemCompra.populate();

        containerItem.add(tblItens = new Table("tblItens", getColumnsItens(), getCollectionProviderItens()));
        tblItens.populate();

        containerItem.add(txtValorTotalTabela = new DisabledDoubleField("valorTotalItens", new LoadableDetachableModel<Double>() {

            @Override
            protected Double load() {
                return getValorTotalItens();
            }
        }));

        form.add(containerItem);

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarAction(target);
            }
        }));

        add(form);

        addModal(dlgConfirmacaoValorNota = new DlgConfirmacaoValorNota(newModalId()) {

            @Override
            public void onAjustarTotalNota(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                ajustarTotalNota(target);
            }

        });

        txtQuantidade.add(quantidadeBehavior);

        txtQuantidade.add(new AjaxFormComponentUpdatingBehavior("onblur") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularTotal(target);
            }
        });

        txtValorItem.add(new AjaxFormComponentUpdatingBehavior("onblur") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularTotal(target);
            }
        });

        txtPrecoUnitario.add(new AjaxFormComponentUpdatingBehavior("onblur") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularTotal(target);
            }
        });

        txtPrecoUnitario.setEnabled(registroNotaFiscal.getTipoDocumento().getTipoDigitacaoPrecoNFEntrada().equals(TipoDocumento.TIPO_DIGITACAO_PRECO_NF_PRECO_UNITARIO));
        txtValorItem.setEnabled(registroNotaFiscal.getTipoDocumento().getTipoDigitacaoPrecoNFEntrada().equals(TipoDocumento.TIPO_DIGITACAO_PRECO_NF_VALOR_ITEM));

        iniciarEdicao();
    }

    private List<IColumn> getColumnsOrdemCompra() {
        List<IColumn> columns = new ArrayList<IColumn>();

        OrdemCompraItem proxy = on(OrdemCompraItem.class);

        columns.add(createColumn(bundle("oc"), proxy.getOrdemCompra().getCodigo()));
        columns.add(createColumn(bundle("nrPregao"), proxy.getOrdemCompra().getNumeroPregao()));
        columns.add(createColumn(bundle("saldo"), proxy.getSaldo()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderOrdemCompra() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ordemCompraList;
            }
        };
    }

    private List<IColumn> getColumnsItens() {
        List<IColumn> columns = new ArrayList<IColumn>();

        NotaFiscalOrdemCompraDTO proxy = on(NotaFiscalOrdemCompraDTO.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("produto"), proxy.getRegistroItemNotaFiscal().getProduto().getDescricao()));
        columns.add(createColumn(bundle("un"), proxy.getRegistroItemNotaFiscal().getProduto().getUnidade().getUnidade()));
        columns.add(new DoubleColumn(BundleManager.getString("quantidade"), path(proxy.getRegistroItemNotaFiscal().getQuantidade())).setCasasDecimais(0));
        columns.add(createColumn(bundle("lotes"), proxy.getRegistroItemNotaFiscal().getDescricaoLotes()));
        columns.add(new DoubleColumn(BundleManager.getString("precoUn"), path(proxy.getRegistroItemNotaFiscal().getPrecoUnitario())).setCasasDecimais(4));
        columns.add(createColumn(bundle("valorTotal"), proxy.getRegistroItemNotaFiscal().getValorItem()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<NotaFiscalOrdemCompraDTO>() {
            @Override
            public void customizeColumn(final NotaFiscalOrdemCompraDTO rowObject) {
                addAction(RemoverActionLink.class, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItem(target, rowObject);
                    }
                }).setVisible(rowObject.getRegistroItemNotaFiscal() == null || rowObject.getRegistroItemNotaFiscal().getStatus() == null || RegistroItemNotaFiscal.STATUS_NORMAL.equals(rowObject.getRegistroItemNotaFiscal().getStatus()));
                DetalhesActionLink detalhesAction = addAction(DetalhesActionLink.class, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        exibirDetalhesLote(target, rowObject.getRegistroItemNotaFiscal());
                    }
                });
                if (!rowObject.getRegistroItemNotaFiscal().getProduto().getSubGrupo().isExigeGrupo()) {
                    detalhesAction.setVisible(false);
                }
            }
        };
    }

    private DlgDetalhesLote dlgDetalhesLote;

    private void exibirDetalhesLote(AjaxRequestTarget target, RegistroItemNotaFiscal rowObject) {
        if (dlgDetalhesLote == null) {
            dlgDetalhesLote = new DlgDetalhesLote(newModalId());
            addModal(target, dlgDetalhesLote);
        }
        dlgDetalhesLote.setRegistroItemNotaFiscal(rowObject);
        dlgDetalhesLote.show(target);
    }

    private ICollectionProvider getCollectionProviderItens() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroEntradaMateriaisMedicamentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProduto.getTxtDescricao().getTextField();
    }

    private Double getValorTotalItens() {
        Double valorTotalItens = 0D;

        for (NotaFiscalOrdemCompraDTO itensDTO : itens) {
            valorTotalItens = new Dinheiro(valorTotalItens).somar(itensDTO.getRegistroItemNotaFiscal().getValorItem()).round().doubleValue();
        }

        return valorTotalItens;
    }

    private void adicionarItem(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        final RegistroItemNotaFiscal registroItemNotaFiscal = modelItem.getObject();

        if (registroItemNotaFiscal.getProduto() != null
                && registroItemNotaFiscal.getProduto().getSubGrupo().isExigeGrupo()
                && !lotes.isEmpty()) {
            registroItemNotaFiscal.setQuantidade(lotesEntradaChooser.getQuantidadeTotal());
        }

        //Realiza validação padrão dos itens conforme o tipo do documento
        RegistroItemNotaFiscalHelper.validarAdicionarItemNota(registroNotaFiscal, registroItemNotaFiscal, null, lotes, ordemCompraList);

        if (CollectionUtils.isEmpty(tblOrdemCompra.getSelectedObjects())) {
            throw new ValidacaoException(BundleManager.getString("selecionePeloMenosUmaOrdemCompra"));
        } else if (CollectionUtils.isNotNullEmpty(tblOrdemCompra.getSelectedObjects())) {
            BigDecimal totalSaldo = new BigDecimal(BigInteger.ZERO);

            for (OrdemCompraItem ordemCompraItem : tblOrdemCompra.getSelectedObjects()) {
                totalSaldo = totalSaldo.add(ordemCompraItem.getSaldo());
            }

            if (registroItemNotaFiscal.getQuantidade().compareTo(totalSaldo.doubleValue()) > 0) {
                throw new ValidacaoException(BundleManager.getString("quantidadeInformadaDeveSerMenorIgualTotalSaldoOrdemCompraSelecionados"));
            }
        }

        for (NotaFiscalOrdemCompraDTO _registroItemNotaFiscal : itens) {
            if (_registroItemNotaFiscal.getRegistroItemNotaFiscal().getProduto().equals(registroItemNotaFiscal.getProduto())) {
                throw new ValidacaoException(BundleManager.getString("produtoJaAdicionado"));
            }
        }

        Empresa empresaLogada = SessaoAplicacaoImp.getInstance().getEmpresa();
        Double ultimoPreco = EstoqueEmpresaHelper.getUltimoPreco(empresaLogada, registroItemNotaFiscal.getProduto());
        DecimalFormat df = new DecimalFormat("#,###.00");

        if (RegistroItemNotaFiscalHelper.getDivergenciaPreco(registroNotaFiscal.getTipoDocumento(), registroItemNotaFiscal.getProduto(), registroItemNotaFiscal.getPrecoUnitario(), empresaLogada)) {
            addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(), Bundle.getStringApplication("msg_preco_ultrapassou_limite_divergencia_preco_compra_anterior_ultimo_preco_X_preco_atual_X_deseja_adicionar",
                    df.format(Coalesce.asDouble(ultimoPreco)), registroItemNotaFiscal.getPrecoUnitario())) {

                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            registroItemNotaFiscal.setPrecoDivergente(RepositoryComponentDefault.SIM_LONG);
                            adicionar(target, registroItemNotaFiscal);
                        }
                    });
            dlgConfirmacaoSimNao.show(target);
        } else {
            adicionar(target, registroItemNotaFiscal);
        }
    }

    private void adicionar(AjaxRequestTarget target, RegistroItemNotaFiscal registroItemNotaFiscal) throws DAOException, ValidacaoException {
        List<RecebimentoGrupoEstoque> recebimentos = new ArrayList<RecebimentoGrupoEstoque>();
        for (MovimentoGrupoEstoqueItemDTO lote : lotes) {
            RecebimentoGrupoEstoque rge = new RecebimentoGrupoEstoque();
            rge.setDataVencimento(lote.getDataValidade());
            rge.setQuantidadeLote(lote.getQuantidade());
            rge.setGrupoEstoque(lote.getGrupoEstoque());
            rge.setLocalizacaoEstrutura(lote.getLocalizacaoEstrutura());
            rge.setFabricante(lote.getFabricante());
            rge.setRegistroItemNotaFiscal(registroItemNotaFiscal);
            rge.setEmpresaSetor(lote.getEmpresa());
            recebimentos.add(rge);
        }

        registroItemNotaFiscal.setRecebimentoGruposEstoque(recebimentos);

        NotaFiscalOrdemCompraDTO dto = new NotaFiscalOrdemCompraDTO();
        dto.setRegistroItemNotaFiscal(registroItemNotaFiscal);
        List<OrdemCompraItem> list = new ArrayList<OrdemCompraItem>(tblOrdemCompra.getSelectedObjects());
        dto.setOrdemCompraItemList(list);

        itens.add(0, dto);
        tblItens.update(target);
        target.add(txtValorTotalTabela);
        limparItem(target);
        limparOrdemCompraList(target);
        txtPrecoUnitario.setEnabled(true);
        txtValorItem.setEnabled(true);
        txtPrecoUnitario.setComponentValue(0D);
        target.add(txtValorItem);
        target.add(txtPrecoUnitario);
        target.add(txtQuantidade);
    }

    private void removerItem(AjaxRequestTarget target, NotaFiscalOrdemCompraDTO dto) throws DAOException, ValidacaoException {
        for (int i = 0; i < itens.size(); i++) {
            if (itens.get(i) == dto) {
                itens.remove(i);
            }
        }
        tblItens.update(target);
        target.add(txtValorTotalTabela);

    }

    private void limparItem(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        modelItem.setObject(new RegistroItemNotaFiscal());
        autoCompleteConsultaProduto.limpar(target);
        txtQuantidade.limpar(target);
        txtValorItem.limpar(target);
        txtPrecoUnitario.limpar(target);
        autoCompleteConsultaProduto.focus(target);
        lotesEntradaChooser.limpar(target);
    }

    private void calcularTotal(AjaxRequestTarget target) {
        if(RepositoryComponentDefault.SIM.equals(bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos)) {
            modelItem.getObject().setValorItem(new Dinheiro(Coalesce.asDouble(modelItem.getObject().getPrecoUnitario()), MathContext.DECIMAL128).multiplicar(Coalesce.asDouble(modelItem.getObject().getQuantidade()), 2).doubleValue());
            target.add(txtValorItem);
        } else if (TipoDocumento.TIPO_DIGITACAO_PRECO_NF_VALOR_ITEM.equals(registroNotaFiscal.getTipoDocumento().getTipoDigitacaoPrecoNFEntrada())) {
            txtPrecoUnitario.limpar(target);
            if (modelItem.getObject().getProduto() != null && modelItem.getObject().getProduto().getSubGrupo().isExigeGrupo()) {
                if (lotesEntradaChooser.getQuantidadeTotal() == 0D) {
                    modelItem.getObject().setPrecoUnitario(0D);
                } else {
                    modelItem.getObject().setPrecoUnitario(new Dinheiro(Coalesce.asDouble(modelItem.getObject().getValorItem()), MathContext.DECIMAL128).dividir(lotesEntradaChooser.getQuantidadeTotal(), 4).doubleValue());
                }
            } else {
                if (modelItem.getObject().getQuantidade() == 0D) {
                    modelItem.getObject().setPrecoUnitario(0D);
                } else {
                    modelItem.getObject().setPrecoUnitario(new Dinheiro(Coalesce.asDouble(modelItem.getObject().getValorItem()), MathContext.DECIMAL128).dividir(modelItem.getObject().getQuantidade(), 4).doubleValue());
                }
            }
        } else {
            txtValorItem.limpar(target);
            if (modelItem.getObject().getProduto() != null && modelItem.getObject().getProduto().getSubGrupo().isExigeGrupo()) {
                modelItem.getObject().setValorItem(new Dinheiro(Coalesce.asDouble(modelItem.getObject().getPrecoUnitario()), MathContext.DECIMAL128).multiplicar(lotesEntradaChooser.getQuantidadeTotal(), 2).doubleValue());
            } else {
                modelItem.getObject().setValorItem(new Dinheiro(Coalesce.asDouble(modelItem.getObject().getPrecoUnitario()), MathContext.DECIMAL128).multiplicar(modelItem.getObject().getQuantidade(), 2).doubleValue());
            }
        }
    }

    private void salvarAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (!registroNotaFiscal.getValorTotal().equals(getValorTotalItens())) {
            dlgConfirmacaoValorNota.setValores(registroNotaFiscal.getValorTotal(), getValorTotalItens());
            dlgConfirmacaoValorNota.show(target);
        } else {
            salvar(target);
        }
    }

    private void ajustarTotalNota(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        registroNotaFiscal.setValorTotal(getValorTotalItens());
        salvar(target);
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        registroNotaFiscal.setTotalCalculado(getValorTotalItens());
        registroNotaFiscal = BOFactoryWicket.getBO(RegistroNotaFiscalFacade.class).cadastrarNotaFiscalOrdemCompra(registroNotaFiscal, itens, null);
        Page page = new CadastroNotaFiscalResumoPage(registroNotaFiscal);
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registroSalvoSucessoCodigoX", registroNotaFiscal.getCodigo()));
    }

    private void iniciarEdicao() {
        if (registroNotaFiscal.getCodigo() != null) {
            List<RegistroItemNotaFiscal> registroItemNotaFiscalList = LoadManager.getInstance(RegistroItemNotaFiscal.class)
                    .addProperties(new HQLProperties(RegistroItemNotaFiscal.class).getProperties())
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                    .addProperty(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_CONTROLA_GRUPO_ESTOQUE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), registroNotaFiscal))
                    .start().getList();

            List<OrdemCompraEloNota> eloItens = new ArrayList<OrdemCompraEloNota>();

            for (RegistroItemNotaFiscal item : registroItemNotaFiscalList) {
                NotaFiscalOrdemCompraDTO dto = new NotaFiscalOrdemCompraDTO();
                dto.setRegistroItemNotaFiscal(item);

                List<OrdemCompraEloNota> ordemCompraEloNota = LoadManager.getInstance(OrdemCompraEloNota.class)
                        .addProperties(new HQLProperties(OrdemCompraEloNota.class).getProperties())
                        .addProperties(new HQLProperties(OrdemCompraItem.class, OrdemCompraEloNota.PROP_ORDEM_COMPRA_ITEM).getProperties())
                        .addProperties(new HQLProperties(OrdemCompra.class, VOUtils.montarPath(OrdemCompraEloNota.PROP_ORDEM_COMPRA_ITEM, OrdemCompraItem.PROP_ORDEM_COMPRA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompraEloNota.PROP_REGISTRO_ITEM_NOTA_FISCAL), item))
                        .start().getList();

                List<OrdemCompraItem> ociList = new ArrayList<OrdemCompraItem>();
                for (OrdemCompraEloNota elo : ordemCompraEloNota) {
                    eloItens.add(elo);

                    OrdemCompraItem ordemCompraItem = LoadManager.getInstance(OrdemCompraItem.class)
                            .addProperties(new HQLProperties(OrdemCompraItem.class).getProperties())
                            .addProperties(new HQLProperties(OrdemCompra.class, OrdemCompraItem.PROP_ORDEM_COMPRA).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(OrdemCompraItem.PROP_CODIGO, elo.getOrdemCompraItem().getCodigo()))
                            .start().getVO();

                    if (ordemCompraItem != null) {
                        ociList.add(ordemCompraItem);
                    }
                }

                dto.setOrdemCompraItemList(ociList);

                itens.add(dto);
            }

            ordemCompraItemMap = br.com.ksisolucoes.util.CollectionUtils.groupMap(eloItens, VOUtils.montarPath(OrdemCompraEloNota.PROP_ORDEM_COMPRA_ITEM, OrdemCompraItem.PROP_PRODUTO));

            List<RecebimentoGrupoEstoque> _lotes = LoadManager.getInstance(RecebimentoGrupoEstoque.class)
                    .addProperties(new HQLProperties(RecebimentoGrupoEstoque.class).getProperties())
                    .addProperties(new HQLProperties(LocalizacaoEstrutura.class, RecebimentoGrupoEstoque.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_REGISTRO_ITEM_NOTA_FISCAL, RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), registroNotaFiscal))
                    .start().getList();

            List<List<RecebimentoGrupoEstoque>> groupList = br.com.ksisolucoes.util.CollectionUtils.groupList(_lotes, VOUtils.montarPath(RecebimentoGrupoEstoque.PROP_REGISTRO_ITEM_NOTA_FISCAL, RegistroItemNotaFiscal.PROP_CODIGO));

            for (List<RecebimentoGrupoEstoque> list : groupList) {
                RegistroItemNotaFiscal item = registroItemNotaFiscalList.get(registroItemNotaFiscalList.indexOf(list.get(0).getRegistroItemNotaFiscal()));
                item.setRecebimentoGruposEstoque(list);
            }
        }
        validaTrocaFornecedor();
    }

    private void consultarOrdemCompra(AjaxRequestTarget target, Produto produto) {
        ordemCompraList.clear();
        List<OrdemCompraEloNota> itensExistentes = ordemCompraItemMap != null ? ordemCompraItemMap.get(produto) : null;
        OrdemCompraItem proxy = on(OrdemCompraItem.class);

        Empresa empresaLogada = SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa();

        List<OrdemCompraItem> itensPendentes = LoadManager.getInstance(OrdemCompraItem.class)
                .addProperties(new HQLProperties(OrdemCompraItem.class).getProperties())
                .addProperties(new HQLProperties(OrdemCompra.class, path(proxy.getOrdemCompra())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getOrdemCompra().getEmpresa()), empresaLogada))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), OrdemCompraItem.Status.PENDENTE.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getProduto()), produto))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getOrdemCompra().getPessoa()), registroNotaFiscal.getFornecedor()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getOrdemCompra().getDataCadastro()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(itensExistentes)) {
            List<OrdemCompraEloNota> itensAdicionais = new ArrayList<OrdemCompraEloNota>(itensExistentes);
            for (OrdemCompraItem oci : itensPendentes) {
                for (OrdemCompraEloNota elo : itensExistentes) {
                    if (oci.equals(elo.getOrdemCompraItem())) {
                        oci.setQuantidadeRecebida(oci.getQuantidadeRecebida().subtract(elo.getQuantidade()));
                        itensAdicionais.remove(elo);
                    }
                }
                ordemCompraList.add(oci);
            }
            for (OrdemCompraEloNota ordemCompraEloNota : itensAdicionais) {
                if (registroNotaFiscal.getFornecedor().equals(ordemCompraEloNota.getOrdemCompraItem().getOrdemCompra().getPessoa())) {
                    ordemCompraList.add(ordemCompraEloNota.getOrdemCompraItem());
                }
            }
        } else {
            ordemCompraList = itensPendentes;
        }
        if (CollectionUtils.isNotNullEmpty(ordemCompraList)) {
            for (OrdemCompraItem ordemCompraItem : ordemCompraList) {
                AutorizacaoFornecimentoItem autorizacaoFornecimentoItem = LoadManager.getInstance(AutorizacaoFornecimentoItem.class)
                        .addProperties(new HQLProperties(AutorizacaoFornecimentoItem.class).getProperties())
                        .addProperties(new HQLProperties(AutorizacaoFornecimento.class, VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_AUTORIZACAO_FORNECIMENTO)).getProperties())
                        .addProperties(new HQLProperties(OrdemCompraItem.class, VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_ORDEM_COMPRA_ITEM)).getProperties())
                        .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_ORDEM_COMPRA_ITEM, OrdemCompraItem.PROP_PRODUTO)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_AUTORIZACAO_FORNECIMENTO, AutorizacaoFornecimento.PROP_ORDEM_COMPRA), ordemCompraItem.getOrdemCompra()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, AutorizacaoFornecimentoItem.Situacao.CANCELADA.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_ORDEM_COMPRA_ITEM, OrdemCompraItem.PROP_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, produto))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AutorizacaoFornecimentoItem.PROP_ORDEM_COMPRA_ITEM, OrdemCompraItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
                        .setMaxResults(1).start().getVO();
                if (autorizacaoFornecimentoItem != null && autorizacaoFornecimentoItem.getOrdemCompraItem().getProduto().equals(produto)) {
                    if (autorizacaoFornecimentoItem.getFabricante() != null) {
                        lotesEntradaChooser.setFabricante(autorizacaoFornecimentoItem.getFabricante().getDescricao());
                    }
                    txtPrecoUnitario.setComponentValue(new Dinheiro(autorizacaoFornecimentoItem.getPrecoUnitario()).doubleValue());
                    if (RepositoryComponentDefault.SIM.equals(bloquearPrecoUnitarioValorTotalEntradaMateriaisMedicamentos)) {
                        txtPrecoUnitario.setEnabled(false);
                        txtValorItem.setEnabled(false);
                        target.add(txtValorItem);
                        target.add(txtPrecoUnitario);
                    }
                    break;
                }
            }
        }
        tblOrdemCompra.update(target);
    }

    private void validaTrocaFornecedor() {
        if (itens != null && !itens.isEmpty()) {
            List<NotaFiscalOrdemCompraDTO> itensAux = new ArrayList<NotaFiscalOrdemCompraDTO>();
            itensAux.addAll(itens);
            for (NotaFiscalOrdemCompraDTO notaFiscalOrdemCompraDTO : itensAux) {
                List<OrdemCompraItem> ordemCompraItemList = notaFiscalOrdemCompraDTO.getOrdemCompraItemList();
                if (ordemCompraItemList != null && !ordemCompraItemList.isEmpty()) {
                    for (OrdemCompraItem ordemCompraItem : ordemCompraItemList) {
                        if (!registroNotaFiscal.getFornecedor().equals(ordemCompraItem.getOrdemCompra().getPessoa())) {
                            itens.remove(notaFiscalOrdemCompraDTO);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void limparOrdemCompraList(AjaxRequestTarget target) {
        ordemCompraList = new ArrayList<OrdemCompraItem>();
        tblOrdemCompra.clearSelection(target);
        tblOrdemCompra.update(target);
    }
    private void carregarUnidade(AjaxRequestTarget target,Produto object) {
        List<Produto> produtos = LoadManager.getInstance(Produto.class).addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO,object.getCodigo()))
                .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(Produto.PROP_UNIDADE)).getProperties()).start().getList();
        txtUnidade.setComponentValue(produtos.get(0).getUnidade().getDescricaoComSigla());
        target.add(txtUnidade);
    }
}
