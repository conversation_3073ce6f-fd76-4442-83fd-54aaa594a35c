package br.com.celk.view.materiais.emprestimo.lancamentos.registrodevolucaoemprestimo.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCancelarDevolucaoEmprestimo extends Panel{
    
    private Form form;
    private DevolucaoEmprestimo devolucaoEmprestimo;
    private String motivo;
    
    private InputArea txaMotivo;
    
    public PnlCancelarDevolucaoEmprestimo(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(txaMotivo = new InputArea("motivo", new PropertyModel<Long>(this, "motivo")));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    onConfirmar(target, devolucaoEmprestimo, motivo);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if(txaMotivo.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, DevolucaoEmprestimo devolucaoEmprestimo, String motivoCancelamento) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, DevolucaoEmprestimo devolucaoEmprestimo){
        this.devolucaoEmprestimo = devolucaoEmprestimo;
        txaMotivo.limpar(target);
        target.add(txaMotivo);
    }
}