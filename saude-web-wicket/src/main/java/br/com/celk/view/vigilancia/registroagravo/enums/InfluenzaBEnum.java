package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum InfluenzaBEnum implements IEnum {
    VICTORIA(1L, "Victoria"),
    YAMAGATHA(2L, "Yamagatha"),
    NAO_REALIZADA(3L, "Não Realizado"),
    INCONCLUSIVO(4L, "Inconclusivo"),
    OUTRO_ESPECIFIQUE(9L, "Outro, especifique");

    private Long value;
    private String descricao;

    InfluenzaBEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static InfluenzaBEnum valueOf(Long value) {
        for (InfluenzaBEnum v : InfluenzaBEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
