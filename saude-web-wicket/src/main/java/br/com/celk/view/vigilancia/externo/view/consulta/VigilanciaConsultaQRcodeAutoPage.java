package br.com.celk.view.vigilancia.externo.view.consulta;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilanciaExterno;
import br.com.celk.view.vigilancia.externo.view.components.feedback.FeedBackVigilancia;
import br.com.celk.view.vigilancia.externo.view.components.feedback.IFeedBackVigilancia;
import br.com.celk.view.vigilancia.externo.view.consulta.panel.FiscalPanel;
import br.com.celk.vigilancia.dto.TermoAjustamentoCondutaDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeBuilderAutos;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeBuilderAutos.Tipo;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeFiscal;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.FiscalAutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoFiscal;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMultaFiscal;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.FiscalTermoAjustamentoConduta;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.util.string.StringValue;
import org.jasypt.util.text.BasicTextEncryptor;
import org.wicketstuff.annotation.mount.MountPath;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@MountPath(VigilanciaHelper.URL_AUTO)
public class VigilanciaConsultaQRcodeAutoPage extends BasePageVigilanciaExterno implements IFeedBackVigilancia {

    private StatelessForm form;

    private Label lbTitulo;
    private Label lbSituacao;

    private String titulo;
    private String situacao;
    private String numero;
    private String tipo;
    private String autuado;
    private String dataAutuacao;
    private String prazo;

    private FeedBackVigilancia feedBackVigilancia;
    private WebMarkupContainer containerForm;
    private WebMarkupContainer containerPrazo;
    private WebMarkupContainer containerFiscais;

    private RepeatingView repeatingFiscais;

    @Override
    protected void onInitialize() {
        super.onInitialize();

        add(form = new StatelessForm("form"));
        form.setOutputMarkupId(true);
        form.setModel(new CompoundPropertyModel(this));

        form.add(feedBackVigilancia = new FeedBackVigilancia("feedBack"));
        feedBackVigilancia.setOutputMarkupId(true);

        form.add(lbTitulo = new Label("titulo"));
        lbTitulo.setDefaultModel(Model.of(BundleManager.getString("consultaAutuacao")));

        form.add(containerForm = new WebMarkupContainer("containerForm"));

        containerForm.add(lbSituacao = new Label("situacao"));
        containerForm.add(new Label("numero"));
        containerForm.add(new Label("tipo"));
        containerForm.add(new Label("autuado"));
        containerForm.add(new Label("fiscal"));
        containerForm.add(new Label("dataAutuacao"));

        containerForm.add(containerPrazo = new WebMarkupContainer("containerPrazo"));
        containerPrazo.setOutputMarkupPlaceholderTag(true);
        containerPrazo.add(new Label("prazo"));

        containerForm.add(containerFiscais = new WebMarkupContainer("containerFiscais"));
        containerFiscais.setOutputMarkupPlaceholderTag(true);
        containerFiscais.add(repeatingFiscais = new RepeatingView("repeatingFiscais"));
        containerFiscais.setVisible(false);

        buscarRecurso();
    }

    private void buscarRecurso() {
        StringValue chaveParam = getPageParameters().get(QRCodeBuilderAutos.PARAM_CHAVE);
        StringValue codigoParam = getPageParameters().get(QRCodeBuilderAutos.PARAM_CODIGO);
        StringValue tipoParam = getPageParameters().get(QRCodeBuilderAutos.PARAM_TIPO);

        try {
            if (chaveParam == null || chaveParam.isEmpty()
                    || codigoParam == null || codigoParam.isEmpty()
                    || tipoParam == null || tipoParam.isEmpty()) {
                throw new ValidacaoException("Está faltando atributos na requisição");
            }

            BasicTextEncryptor encryptor = new BasicTextEncryptor();
            encryptor.setPassword(QRCodeBuilderAutos.CRYPTO_PASS);

            String chave = encryptor.decrypt(chaveParam.toString());
            Long codigo = Long.parseLong(encryptor.decrypt(codigoParam.toString()));
            Tipo tipo = Tipo.valueOf(encryptor.decrypt(tipoParam.toString()));

            String style = " label-default";

            List<Profissional> fiscais = null;

            if (Tipo.INFRACAO.equals(tipo)) {
                AutoInfracao autoInfracao = LoadManager.getInstance(AutoInfracao.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoInfracao.PROP_CODIGO, codigo))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoInfracao.PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO, ProcessoAdministrativoAutenticacao.PROP_CHAVE), chave))
                        .start().getVO();

                if (autoInfracao == null) {
                    throw new ValidacaoException("Não foi possível encontrar o Auto de Infração para os parâmetros informados");
                }

                this.tipo = "Infração";
                this.numero = autoInfracao.getNumeroFormatado();
                this.autuado = autoInfracao.getDenunciado();
                this.situacao = autoInfracao.getStatusDescricao();
                this.dataAutuacao = Data.formatar(autoInfracao.getDataInfracao());

                if (autoInfracao.getPrazoDefesa() != null) {
                    this.prazo = Data.formatar(autoInfracao.getPrazoDefesa());
                } else {
                    containerPrazo.setVisible(false);
                }

                if (AutoInfracao.Status.CONCLUIDO.value().equals(autoInfracao.getSituacao())) {
                    containerPrazo.setVisible(false);
                    style = "label-success";
                } else {
                    style = "label-primary";
                }

                List<FiscalAutoInfracao> list = LoadManager.getInstance(FiscalAutoInfracao.class)
                        .addProperty(VOUtils.montarPath(FiscalAutoInfracao.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FiscalAutoInfracao.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(FiscalAutoInfracao.PROP_AUTO_INFRACAO, autoInfracao))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    fiscais = Lambda.extract(list, Lambda.on(FiscalAutoInfracao.class).getProfissional());
                }

            } else if (Tipo.INTIMACAO.equals(tipo)) {
                AutoIntimacao autoIntimacao = LoadManager.getInstance(AutoIntimacao.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacao.PROP_CODIGO, codigo))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoIntimacao.PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO, ProcessoAdministrativoAutenticacao.PROP_CHAVE), chave))
                        .start().getVO();

                if (autoIntimacao == null) {
                    throw new ValidacaoException("Não foi possível encontrar o Auto de Intimação para os parâmetros informados");
                }

                Date dataCumprimentoPrazo = LoadManager.getInstance(AutoIntimacaoExigencia.class)
                        .addGroup(new QueryCustom.QueryCustomGroup(AutoIntimacaoExigencia.PROP_DATA_CUMPRIMENTO_PRAZO, QueryCustom.QueryCustomGroup.MAX))
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacaoExigencia.PROP_AUTO_INTIMACAO, autoIntimacao))
                        .start().getVO();

                this.tipo = "Intimação";
                this.numero = autoIntimacao.getNumeroFormatado();
                this.autuado = autoIntimacao.getAutuado();
                this.situacao = autoIntimacao.getStatusDescricao();
                this.dataAutuacao = Data.formatar(autoIntimacao.getDataIntimacao());

                if (dataCumprimentoPrazo != null) {
                    this.prazo = Data.formatar(dataCumprimentoPrazo);
                } else if (AutoIntimacao.Status.AGUARDANDO_RECEBIMENTO.value().equals(autoIntimacao.getSituacao())) {
                    this.prazo = "Aguardando Cálculo";
                } else {
                    this.prazo = "Imediato";
                }

                if (AutoIntimacao.Status.CONCLUIDO.value().equals(autoIntimacao.getSituacao())) {
                    containerPrazo.setVisible(false);
                    style = "label-success";
                } else {
                    style = "label-primary";
                }

                List<AutoIntimacaoFiscal> list = LoadManager.getInstance(AutoIntimacaoFiscal.class)
                        .addProperty(VOUtils.montarPath(AutoIntimacaoFiscal.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(AutoIntimacaoFiscal.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoIntimacaoFiscal.PROP_AUTO_INTIMACAO, autoIntimacao))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    fiscais = Lambda.extract(list, Lambda.on(AutoIntimacaoFiscal.class).getProfissional());
                }

            } else if (Tipo.MULTA.equals(tipo)) {
                AutoMulta autoMulta = LoadManager.getInstance(AutoMulta.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoMulta.PROP_CODIGO, codigo))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoMulta.PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO, ProcessoAdministrativoAutenticacao.PROP_CHAVE), chave))
                        .start().getVO();

                if (autoMulta == null) {
                    throw new ValidacaoException("Não foi possível encontrar o Auto de Multa para os parâmetros informados");
                }

                this.tipo = "Multa";
                this.numero = autoMulta.getNumeroFormatado();
                this.autuado = autoMulta.getDescricaoAutuado();
                this.situacao = autoMulta.getSituacaoFormatado();
                this.dataAutuacao = Data.formatar(autoMulta.getDataMulta());

                if (autoMulta.getDataPrazoRecurso() != null) {
                    this.prazo = Data.formatar(autoMulta.getDataPrazoRecurso());
                } else {
                    containerPrazo.setVisible(false);
                }

                if (AutoMulta.Situacao.CONCLUIDO.value().equals(autoMulta.getSituacao())) {
                    containerPrazo.setVisible(false);
                    style = "label-success";
                } else {
                    style = "label-primary";
                }

                List<AutoIntimacaoFiscal> list = LoadManager.getInstance(AutoMultaFiscal.class)
                        .addProperty(VOUtils.montarPath(AutoMultaFiscal.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(AutoMultaFiscal.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoMultaFiscal.PROP_AUTO_MULTA, autoMulta))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    fiscais = Lambda.extract(list, Lambda.on(AutoMultaFiscal.class).getProfissional());
                }

            } else if (Tipo.PENALIDADE.equals(tipo)) {
                AutoPenalidade autoPenalidade = LoadManager.getInstance(AutoPenalidade.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoPenalidade.PROP_CODIGO, codigo))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AutoPenalidade.PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO, ProcessoAdministrativoAutenticacao.PROP_CHAVE), chave))
                        .start().getVO();

                if (autoPenalidade == null) {
                    throw new ValidacaoException("Não foi possível encontrar o Auto de Penalidade para os parâmetros informados");
                }

                this.tipo = "Penalidade";
                this.numero = autoPenalidade.getNumeroFormatado();
                this.autuado = autoPenalidade.getAutuado();
                this.situacao = autoPenalidade.getSituacaoFormatado();
                this.dataAutuacao = Data.formatar(autoPenalidade.getDataPenalidade());

                if (autoPenalidade.getPrazoRecurso() != null) {
                    this.prazo = Data.formatar(autoPenalidade.getPrazoRecurso());
                } else {
                    containerPrazo.setVisible(false);
                }

                if (AutoPenalidade.Situacao.CONCLUIDO.value().equals(autoPenalidade.getSituacao())) {
                    containerPrazo.setVisible(false);
                    style = "label-success";
                } else {
                    style = "label-primary";
                }

                List<FiscalAutoInfracao> list = LoadManager.getInstance(AutoPenalidadeFiscal.class)
                        .addProperty(VOUtils.montarPath(AutoPenalidadeFiscal.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(AutoPenalidadeFiscal.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(AutoPenalidadeFiscal.PROP_AUTO_PENALIDADE, autoPenalidade))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    fiscais = Lambda.extract(list, Lambda.on(AutoPenalidadeFiscal.class).getProfissional());
                }
            } else if (Tipo.TERMO_AJUSTAMENTO_CONDUTA.equals(tipo)) {
                TermoAjustamentoConduta termoAjustamentoConduta = LoadManager.getInstance(TermoAjustamentoConduta.class)
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_NUMERO))
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_SITUACAO))
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_DATA_EMISSAO))
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_ESTABELECIMENTO, Estabelecimento.PROP_RAZAO_SOCIAL))
                        .addProperty(VOUtils.montarPath(TermoAjustamentoConduta.PROP_VIGILANCIA_PESSOA, VigilanciaPessoa.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_CODIGO, codigo))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TermoAjustamentoConduta.PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO, ProcessoAdministrativoAutenticacao.PROP_CHAVE), chave))
                        .start().getVO();

                if (termoAjustamentoConduta == null) {
                    throw new ValidacaoException("Não foi possível encontrar o Auto de Termo de Consulta da Conduta para os parâmetros informados");
                }

                this.tipo = "Termo de Ajustamento da Conduta";
                this.numero = termoAjustamentoConduta.getNumeroFormatado();
                this.autuado = termoAjustamentoConduta.getEstabelecimentoPessoa();
                this.situacao = termoAjustamentoConduta.getStatusDescricao();
                this.dataAutuacao = Data.formatar(termoAjustamentoConduta.getDataEmissao());

                containerPrazo.setVisible(false);

                if (TermoAjustamentoConduta.Status.CONCLUIDO.value().equals(termoAjustamentoConduta.getSituacao())) {
                    containerPrazo.setVisible(false);
                    style = "label-success";
                } else {
                    style = "label-primary";
                }

                List<FiscalTermoAjustamentoConduta> list = LoadManager.getInstance(FiscalTermoAjustamentoConduta.class)
                        .addProperty(VOUtils.montarPath(FiscalTermoAjustamentoConduta.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FiscalTermoAjustamentoConduta.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(FiscalTermoAjustamentoConduta.PROP_TERMO_AJUSTAMENTO_CONDUTA, termoAjustamentoConduta))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    fiscais = Lambda.extract(list, Lambda.on(FiscalTermoAjustamentoConduta.class).getProfissional());
                }
            }

            lbSituacao.add(new AttributeModifier("class", "label " + style));

            if (CollectionUtils.isNotNullEmpty(fiscais)) {
                for (Profissional fiscal : fiscais) {
                    repeatingFiscais.add(new FiscalPanel(repeatingFiscais.newChildId(), fiscal));
                }

                containerFiscais.setVisible(true);
            }

        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
            containerForm.setVisible(false);
        }

        if (!containerForm.isVisible()) {
            error(BundleManager.getString("msgNaoFoiPossivelEncontrarAuto"));
            lbTitulo.setDefaultModel(Model.of(BundleManager.getString("ops")));
        }
    }

    @Override
    public String getTituloPrograma() {
        return "";
    }

    @Override
    public String getSufixoVigilancia() {
        ConfiguracaoVigilancia configuracaoVigilancia = null;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        if (configuracaoVigilancia != null) {
            Empresa empresa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, configuracaoVigilancia.getEmpresa().getCodigo()))
                    .setMaxResults(1).start().getVO();
            return empresa.getEnderecoCidadeBairroFormatado().toUpperCase();
        }
        return null;
    }

    @Override
    public FeedBackVigilancia getFeedBackVigilancia() {
        return feedBackVigilancia;
    }
}
