package br.com.celk.view.vacina.entradavacina;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.DetalhesActionColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vacina.ItemEntradaVacina;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import br.com.celk.view.vacina.itementradavacina.DlgDetalhesItemEntradaVacina;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.vacina.EntradaVacina;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class DetalhesEntradaVacinaPage extends BasePage {

    private List<ItemEntradaVacina> itens = new ArrayList<ItemEntradaVacina>();
    
    private Table<ItemEntradaVacina> table;
    private DlgDetalhesItemEntradaVacina dlgDetalhesItemEntradaVacina;
    
    private EntradaVacina entradaVacina;
    
    private RepeatingView controls;
    
    public DetalhesEntradaVacinaPage(EntradaVacina pedidoVacinaInsumo) {
        this.entradaVacina = pedidoVacinaInsumo;
        initItens();
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel<EntradaVacina>(entradaVacina));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DATA_CADASTRO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_NOTA)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DATA_EMISSAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_FORNECEDOR, Pessoa.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DATA_PORTARIA)));
        form.add(new DisabledDoubleField(VOUtils.montarPath(EntradaVacina.PROP_VALOR_TOTAL)));
        form.add(new DisabledInputArea(VOUtils.montarPath(EntradaVacina.PROP_OBSERVACAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DESCRICAO_SITUACAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DATA_USUARIO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_USUARIO,Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_USUARIO_CONFIRMACAO,Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(EntradaVacina.PROP_DATA_CONFIRMACAO)));

        form.add(table = new Table<ItemEntradaVacina>("tableItens", getColumns(), getCollectionProvider()));
    
        form.add(controls = new RepeatingView("controls"));
        
        controls.add(new BookmarkablePageLink(controls.newChildId(), ConsultaEntradaVacinaPage.class) {
            {
                add(new AttributeModifier("class", "arrow-left"));
                add(new AttributeModifier("value", BundleManager.getString("voltar")));
            }
        });
        
        table.populate();
        
        add(form);
        addModal(dlgDetalhesItemEntradaVacina = new DlgDetalhesItemEntradaVacina(newModalId()));
    }

    public RepeatingView getControls() {
        return controls;
    }

    public void setControls(RepeatingView controls) {
        this.controls = controls;
    }
    
    private List<ISortableColumn<ItemEntradaVacina>> getColumns(){
        List<ISortableColumn<ItemEntradaVacina>> columns = new ArrayList<ISortableColumn<ItemEntradaVacina>>();
        
        ColumnFactory columnFactory = new ColumnFactory(ItemEntradaVacina.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(ItemEntradaVacina.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(ItemEntradaVacina.PROP_QUANTIDADE_ENTRADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valorUnitario"), VOUtils.montarPath(ItemEntradaVacina.PROP_VALOR_UNITARIO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valorTotal"), VOUtils.montarPath(ItemEntradaVacina.PROP_VALOR_TOTAL)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<ItemEntradaVacina>() {

            @Override
            public Component getComponent(String componentId, final ItemEntradaVacina rowObject) {
                return new DetalhesActionColumnPanel(componentId) {

                    @Override
                    public void onDetalhar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dlgDetalhesItemEntradaVacina.setModelObject(rowObject);
                        dlgDetalhesItemEntradaVacina.show(target);
                    }

                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesEntradaVacina");
    }
    
    private void initItens() {
            itens = LoadManager.getInstance(ItemEntradaVacina.class)
                    .addProperties(new HQLProperties(ItemEntradaVacina.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemEntradaVacina.PROP_ENTRADA_VACINA), this.entradaVacina))
                    .addSorter(new QueryCustom.QueryCustomSorter(ItemEntradaVacina.PROP_CODIGO))
                    .start().getList();
    }

}
