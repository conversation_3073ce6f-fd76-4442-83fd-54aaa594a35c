package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum CriterioEncerramentoEnum implements IEnum {
    LAB(1L, "Laboratorial"),
    CLIN_EPIDEMIOLOGICO(2L, "Clínico-Epidemiológico"),
    CLINICO(3L, "Clínico"),
    CLINICO_IMAGEM(9L, "Clínico-imagem");

    private Long value;
    private String descricao;

    CriterioEncerramentoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static CriterioEncerramentoEnum valueOf(Long value) {
        for (CriterioEncerramentoEnum v : CriterioEncerramentoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
