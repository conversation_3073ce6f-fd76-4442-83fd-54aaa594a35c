package br.com.celk.view.siab.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.siab.SiabPma2c;
import br.com.ksisolucoes.vo.siab.SiabSsa2;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaSiabSsa2 extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return SiabSsa2.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(SiabSsa2.class).getProperties(),
                new String[]{
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME),
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_MICRO_AREA),
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_DESCRICAO),
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE, Cidade.PROP_DESCRICAO),
                    VOUtils.montarPath(SiabSsa2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO),
                });
    }

    
}
