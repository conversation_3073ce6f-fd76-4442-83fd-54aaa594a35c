package br.com.celk.view.vigilancia.tipoendereco;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConsultaTipoEnderecoVigilanciaPage extends ConsultaPage<TipoEnderecoVigilancia, List<BuilderQueryCustom.QueryParameter>> {
    
    private String descricao;

    public ConsultaTipoEnderecoVigilanciaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TipoEnderecoVigilancia proxy = on(TipoEnderecoVigilancia.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TipoEnderecoVigilancia>() {
            @Override
            public void customizeColumn(final TipoEnderecoVigilancia rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<TipoEnderecoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoEnderecoVigilancia modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoEnderecoVigilanciaPage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoEnderecoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoEnderecoVigilancia modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return TipoEnderecoVigilancia.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoEnderecoVigilancia.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoEnderecoVigilancia.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoEnderecoVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoEnderecoVigilancia");
    }
}
