package br.com.celk.view.consorcio.licitacao.columnpanel;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class PregaoLicitacaoItemColumnPanel extends Panel{

    private AjaxLink btnAdicionar;
    private AjaxLink btnRemover;
    
    public PregaoLicitacaoItemColumnPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        add(btnAdicionar = new AbstractAjaxLink("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onAdicionar(target);
            }

        });
        add(btnRemover = new AbstractAjaxLink("btnRemover") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onRemover(target);
            }
            
        });
    }
    
    public abstract void onAdicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
}
