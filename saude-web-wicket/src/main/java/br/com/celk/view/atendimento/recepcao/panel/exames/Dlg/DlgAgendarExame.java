package br.com.celk.view.atendimento.recepcao.panel.exames.Dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.prontuario.basico.ExameAutorizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
/**
 *
 * <AUTHOR>
 */
public abstract class DlgAgendarExame extends Window {

    public PnlAgendarExame pnlAgendarExame;

    public DlgAgendarExame(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("confirmarAgendaExame"));

        setInitialWidth(700);
        setInitialHeight(280);

        setResizable(true);

        setContent(pnlAgendarExame = new PnlAgendarExame(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) {
                DlgAgendarExame.this.onConfirmar(target);
            }
        });
    }

    public void show(AjaxRequestTarget target,ExameAutorizacao exameAutorizacao, boolean agendar) {
        super.show(target);
        pnlAgendarExame.setExameAutorizacao(target, exameAutorizacao);
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target);

}
