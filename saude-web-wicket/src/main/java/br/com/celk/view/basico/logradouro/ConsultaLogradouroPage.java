package br.com.celk.view.basico.logradouro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoLogradouro;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaLogradouroPage extends ConsultaPage<EnderecoEstruturadoLogradouro, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long codigo;

    public ConsultaLogradouroPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<Long>("codigo"));
        form.add(new UpperField("descricao"));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(EnderecoEstruturadoLogradouro.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(EnderecoEstruturadoLogradouro.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(EnderecoEstruturadoLogradouro.PROP_DESCRICAO)));

        return columns;
    }


    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EnderecoEstruturadoLogradouro>() {

            @Override
            public void customizeColumn(EnderecoEstruturadoLogradouro rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EnderecoEstruturadoLogradouro>() {
                    @Override
                    public void action(AjaxRequestTarget target, EnderecoEstruturadoLogradouro modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLogradouroPage(modelObject, false, true));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EnderecoEstruturadoLogradouro>() {
                    @Override
                    public void action(AjaxRequestTarget target, EnderecoEstruturadoLogradouro modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<EnderecoEstruturadoLogradouro>() {
                    @Override
                    public void action(AjaxRequestTarget target, EnderecoEstruturadoLogradouro modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLogradouroPage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return EnderecoEstruturadoLogradouro.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(EnderecoEstruturadoLogradouro.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(EnderecoEstruturadoLogradouro.PROP_CODIGO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoEstruturadoLogradouro.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoEstruturadoLogradouro.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLogradouroPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLogradouro");
    }
}
