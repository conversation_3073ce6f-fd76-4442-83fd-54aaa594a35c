package br.com.celk.view.hospital.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioRelacaoAtendimentosDTOParam;
import br.com.ksisolucoes.report.hospital.interfaces.facade.HospitalReportFacade;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioRelacaoAtendimentosPage extends RelatorioPage<RelatorioRelacaoAtendimentosDTOParam>{
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaConvenio autoCompleteConsultaConvenio;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownSituacao;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(new AutoCompleteConsultaTipoAtendimento("tipoAtendimentos").setIncluirInativos(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(autoCompleteConsultaConvenio = (AutoCompleteConsultaConvenio) new AutoCompleteConsultaConvenio("convenio")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("tipoData", RelatorioRelacaoAtendimentosDTOParam.TipoData.values()));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioRelacaoAtendimentosDTOParam.FormaApresentacao.values()));
        form.add(getDropDownSituacao());
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioRelacaoAtendimentosDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioRelacaoAtendimentosDTOParam.TipoOrdenacao.values()));

        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
    }
    
    public DropDown<String> getDropDownSituacao(){
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<String>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(AtendimentoInformacao.StatusAtendimento.ABERTO.descricao(), BundleManager.getString("aberto"));
            dropDownSituacao.addChoice(AtendimentoInformacao.StatusAtendimento.CONCLUIDO.descricao(), BundleManager.getString("concluido"));
        }
        
        return dropDownSituacao;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoAtendimentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoAtendimentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(HospitalReportFacade.class).relatorioRelacaoAtendimentosHpt(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioRelacaoAtendimentos");
    }
    
}
