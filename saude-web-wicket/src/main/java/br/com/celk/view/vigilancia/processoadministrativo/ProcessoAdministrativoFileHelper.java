package br.com.celk.view.vigilancia.processoadministrativo;

import br.com.celk.report.DocumentoVigilanciaHtmlReport;
import br.com.celk.report.HtmlFileUtil;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.export.PdfUtil;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.DocumentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import com.lowagie.text.DocumentException;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Leonardo
 */
public class ProcessoAdministrativoFileHelper {

    public static File getArquivoUnicoProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) {
        try {
            List<ArquivoProcessoDTO> documentosPdf = new ArrayList<>();

            List<InputStream> documentosAutosList = new ArrayList<>();

            ProcessoAdministrativoFileHelper.addAutosProcessoAdministrativo(processoAdministrativo, documentosAutosList);

            ProcessoAdministrativoFileHelper.addRelatoriosInspecaoProcessoAdministrativo(processoAdministrativo, documentosPdf);

            ProcessoAdministrativoFileHelper.addDocumentosOcorrenciasProcessoAdministrativo(processoAdministrativo, documentosPdf);

            List<InputStream> finalStreamList = new ArrayList<>();
            if (CollectionUtils.isNotNullEmpty(documentosAutosList)) {
                finalStreamList.addAll(documentosAutosList);
            }
            if (CollectionUtils.isNotNullEmpty(documentosPdf)) {
                List<ArquivoProcessoDTO> documentosByCronologia = Lambda.sort(documentosPdf, Lambda.on(ArquivoProcessoDTO.class), new ArgumentComparator(Lambda.on(ArquivoProcessoDTO.class).getDataCriacao()));
                finalStreamList.addAll(Lambda.extract(documentosByCronologia, Lambda.on(ArquivoProcessoDTO.class).getDocumento()));
            }
            if (CollectionUtils.isNotNullEmpty(finalStreamList)) {
                File processoAdministrativoPDF = PdfUtil.mergePDFFiles(finalStreamList, "processo_administrativo");
                return PdfUtil.numerarPaginasPdf(processoAdministrativoPDF);
            }
        } catch (Exception e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        return null;
    }

    public static File getPDFAutoInfracao(AutoInfracao autoInfracao) throws IOException, JRException, ReportException {
        DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoInfracao(autoInfracao.getCodigo(), autoInfracao.getNumeroFormatado(), autoInfracao.getSituacao());
        File file = File.createTempFile("auto_infracao_" + autoInfracao.getCodigo(), ".pdf");
        JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
        return file;
    }

    public static File getPDFAutoMulta(AutoMulta autoMulta) throws IOException, JRException, ReportException {
        DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoMulta(autoMulta.getCodigo(), autoMulta.getNumeroFormatado());
        File file = File.createTempFile("auto_multa_" + autoMulta.getCodigo(), ".pdf");
        JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
        return file;
    }

    public static File getPDFAutoIntimacao(AutoIntimacao autoIntimacao) throws IOException, JRException, ReportException {
        if (autoIntimacao != null) {
            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(autoIntimacao.getCodigo(), autoIntimacao.getNumeroFormatado());
            File file = File.createTempFile("auto_intimacao_" + autoIntimacao.getCodigo(), ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
            return file;
        } else {
            return null;
        }
    }

    public static ArquivoProcessoDTO getPDFRelatorioInspecaoAntInfracao(AutoInfracao autoInfracao) throws IOException, JRException, ReportException {
        if (autoInfracao.getRelatorioInspecao() != null) {
            RelatorioInspecao relatorioInspecao = AutosHelper.reloadRelatorioInspecao(autoInfracao.getRelatorioInspecao());
            if (relatorioInspecao != null) {
                RelatorioInspecaoSanitariaDTOParam dto = new RelatorioInspecaoSanitariaDTOParam();
                dto.setRelatorioInspecao(relatorioInspecao);

                DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(dto);
                File file = File.createTempFile("relatorio_inspecao_" + relatorioInspecao.getCodigo(), ".pdf");
                JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                return new ArquivoProcessoDTO(new FileInputStream(file), relatorioInspecao.getDataCadastro());
            }
        }
        return null;
    }

    public static ArquivoProcessoDTO getPDFRelatorioInspecaoAntIntimacao(AutoIntimacao autoIntimacao) throws IOException, JRException, ReportException {
        if (autoIntimacao != null && autoIntimacao.getRelatorioInspecao() != null) {
            RelatorioInspecao relatorioInspecao = AutosHelper.reloadRelatorioInspecao(autoIntimacao.getRelatorioInspecao());
            if (relatorioInspecao != null) {
                RelatorioInspecaoSanitariaDTOParam dto = new RelatorioInspecaoSanitariaDTOParam();
                dto.setRelatorioInspecao(relatorioInspecao);

                DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(dto);
                File file = File.createTempFile("relatorio_inspecao_" + relatorioInspecao.getCodigo(), ".pdf");
                JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                return new ArquivoProcessoDTO(new FileInputStream(file), relatorioInspecao.getDataCadastro());
            }
        }
        return null;
    }

    public static List<ArquivoProcessoDTO> getPDFRelatorioInspecaoPosInfracao(AutoInfracao autoInfracao) throws IOException, JRException, ReportException {
        List<RelatorioInspecao> relatorioInspecaoList = AutosHelper.getRelatoriosInspecaoFromInfracao(autoInfracao);
        List<ArquivoProcessoDTO> fileList = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(relatorioInspecaoList)) {
            for (RelatorioInspecao relatorioInspecao : relatorioInspecaoList) {
                if (relatorioInspecao != null) {
                    RelatorioInspecaoSanitariaDTOParam dto = new RelatorioInspecaoSanitariaDTOParam();
                    dto.setRelatorioInspecao(relatorioInspecao);
                    DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(dto);

                    File file = File.createTempFile("relatorio_inspecao_" + relatorioInspecao.getCodigo(), ".pdf");
                    JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

                    fileList.add(new ArquivoProcessoDTO(new FileInputStream(file), relatorioInspecao.getDataCadastro()));
                }
            }
        }
        return fileList;
    }

    public static List<ArquivoProcessoDTO> getPDFRelatorioInspecaoPosMulta(AutoMulta autoMulta) throws IOException, JRException, ReportException {
        List<RelatorioInspecao> relatorioInspecaoList = AutosHelper.getRelatoriosInspecaoFromMulta(autoMulta);
        List<ArquivoProcessoDTO> fileList = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(relatorioInspecaoList)) {
            for (RelatorioInspecao relatorioInspecao : relatorioInspecaoList) {
                if (relatorioInspecao != null) {
                    RelatorioInspecaoSanitariaDTOParam dto = new RelatorioInspecaoSanitariaDTOParam();
                    dto.setRelatorioInspecao(relatorioInspecao);
                    DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(dto);

                    File file = File.createTempFile("relatorio_inspecao_" + relatorioInspecao.getCodigo(), ".pdf");
                    JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

                    fileList.add(new ArquivoProcessoDTO(new FileInputStream(file), relatorioInspecao.getDataCadastro()));
                }
            }
        }
        return fileList;
    }

    public static ArquivoProcessoDTO getPDFRelatorioInspecaoPosIntimacao(AutoIntimacao autoIntimacao) throws IOException, JRException, ReportException {
        if (autoIntimacao != null) {
            RelatorioInspecao relatorioInspecaoFromIntimacao = AutosHelper.getRelatorioInspecaoFromIntimacao(autoIntimacao);
            if (relatorioInspecaoFromIntimacao != null) {
                RelatorioInspecaoSanitariaDTOParam dto = new RelatorioInspecaoSanitariaDTOParam();
                dto.setRelatorioInspecao(relatorioInspecaoFromIntimacao);

                DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(dto);
                File file = File.createTempFile("relatorio_inspecao_" + relatorioInspecaoFromIntimacao.getCodigo(), ".pdf");
                JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
                return new ArquivoProcessoDTO(new FileInputStream(file), relatorioInspecaoFromIntimacao.getDataCadastro());
            }
        }
        return null;
    }

    public static void addDocumentosOcorrenciasProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo, List<ArquivoProcessoDTO> documentosPdf) throws Exception {
        List<ProcessoAdministrativoOcorrencia> ocorrenciaList = LoadManager.getInstance(ProcessoAdministrativoOcorrencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, processoAdministrativo))
                .addSorter(new QueryCustom.QueryCustomSorter(ProcessoAdministrativoOcorrencia.PROP_DATA_OCORRENCIA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        for (ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia : ocorrenciaList) {
            File pdfDocumento = getPDFDocumentoOcorrencia(processoAdministrativoOcorrencia);
            if (pdfDocumento != null) {
                documentosPdf.add(new ArquivoProcessoDTO(new FileInputStream(pdfDocumento), processoAdministrativoOcorrencia.getDataOcorrencia()));
            }
        }
    }

    private static File getPDFDocumentoOcorrencia(ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia) throws Exception {
        Long tipo = processoAdministrativoOcorrencia.getTipo();

        File file = null;
        String fileName = null;
        DataReport dataReport = null;
        Long codigoDocumento = processoAdministrativoOcorrencia.getDocumento();

        if (ProcessoAdministrativoOcorrencia.Tipo.PARECER_DEFESA.value().equals(tipo)) {
            fileName = "parecer_defesa";
            ProcessoAdministrativoParecer parecer = LoadManager.getInstance(ProcessoAdministrativoParecer.class).setId(codigoDocumento).start().getVO();
            file = getMergedFileParecer(parecer);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.PARECER_RECURSO.value().equals(tipo)) {
            fileName = "parecer_recurso";
            ProcessoAdministrativoParecer parecer = LoadManager.getInstance(ProcessoAdministrativoParecer.class).setId(codigoDocumento).start().getVO();
            file = getMergedFileParecer(parecer);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DECISAO_DEFESA.value().equals(tipo)) {
            fileName = "decisao_defesa";
            ProcessoAdministrativoDecisao decisao = LoadManager.getInstance(ProcessoAdministrativoDecisao.class).setId(codigoDocumento).start().getVO();
            file = getMergedFileDecisao(decisao);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DECISAO_RECURSO.value().equals(tipo)) {
            fileName = "decisao_recurso";
            ProcessoAdministrativoDecisao decisao = LoadManager.getInstance(ProcessoAdministrativoDecisao.class).setId(codigoDocumento).start().getVO();
            file = getMergedFileDecisao(decisao);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.AUTO_PENALIDADE.value().equals(tipo)) {
            fileName = "auto_penalidade";
            AutoPenalidade autoPenalidade = LoadManager.getInstance(AutoPenalidade.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoPenalidade(codigoDocumento, autoPenalidade.getNumeroFormatado());
        } else if (ProcessoAdministrativoOcorrencia.Tipo.RECURSO_CADASTRADO.value().equals(tipo)) {
            fileName = "doc_recurso";
            ProcessoAdministrativoRecurso recurso = LoadManager.getInstance(ProcessoAdministrativoRecurso.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport = new DocumentoVigilanciaHtmlReport(recurso.getDocumentoVigilancia().getDescricao());
            file = getMergedFileRecurso(recurso, documentoVigilanciaHtmlReport);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DEFESA_PREVIA_CADASTRADA.value().equals(tipo)) {
            fileName = "doc_defesa";
            ProcessoAdministrativoRecurso recurso = LoadManager.getInstance(ProcessoAdministrativoRecurso.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport = new DocumentoVigilanciaHtmlReport(recurso.getDocumentoVigilancia().getDescricao());
            file = getMergedFileRecurso(recurso, documentoVigilanciaHtmlReport);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DOCUMENTO_EXTRA_DEFESA_CADASTRADO.value().equals(tipo)) {
            fileName = "doc_ex_defesa";
            ProcessoAdministrativoRecurso recurso = LoadManager.getInstance(ProcessoAdministrativoRecurso.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport = new DocumentoVigilanciaHtmlReport(recurso.getDocumentoVigilancia().getDescricao());
            file = getMergedFileRecurso(recurso, documentoVigilanciaHtmlReport);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DOCUMENTO_EXTRA_RECURSO_CADASTRADO.value().equals(tipo)) {
            fileName = "doc_ex_recurso";
            ProcessoAdministrativoRecurso recurso = LoadManager.getInstance(ProcessoAdministrativoRecurso.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport = new DocumentoVigilanciaHtmlReport(recurso.getDocumentoVigilancia().getDescricao());
            file = getMergedFileRecurso(recurso, documentoVigilanciaHtmlReport);
        } else if (ProcessoAdministrativoOcorrencia.Tipo.DESPACHO.value().equals(tipo)) {
            fileName = "certidao_proc";
            DocumentoVigilancia documentoVigilancia = LoadManager.getInstance(DocumentoVigilancia.class).setId(codigoDocumento).setMaxResults(1).start().getVO();
            DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport = new DocumentoVigilanciaHtmlReport(documentoVigilancia.getDescricao());
            file = resolveHtmlReport(documentoVigilanciaHtmlReport);
        }

        if (dataReport != null) {
            file = File.createTempFile(fileName + codigoDocumento, ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
        }

        return file;
    }

    private static File getMergedFileParecer(ProcessoAdministrativoParecer parecer) throws Exception {
        ImpressaoParecerProcessoAdministrativoDTOParam param = new ImpressaoParecerProcessoAdministrativoDTOParam();
        param.setProcessoAdministrativoParecer(parecer);
        QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageParecerProcessoAdministrativo(), parecer.getChaveQRcode());
        param.setQRCodeParam(qrCodeParam);

        DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoParecerProcessoAdministrativo(param);
        File fileParecer = null;
        if (dataReport != null) {
            fileParecer = File.createTempFile("parecer" + parecer.getCodigo(), ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), fileParecer.getAbsolutePath());
        }
        File anexos = getAnexosParecer(parecer);
        List<InputStream> inputStreams;
        if (anexos != null) {
            inputStreams = getInputStreams(fileParecer, anexos);
            return PdfUtil.mergePDFFiles(inputStreams, "anexos_parecer");
        } else {
            return fileParecer;
        }
    }

    private static File getMergedFileDecisao(ProcessoAdministrativoDecisao decisao) throws Exception {
        ImpressaoDecisaoProcessoAdministrativoDTOParam param = new ImpressaoDecisaoProcessoAdministrativoDTOParam();
        param.setProcessoAdministrativoDecisao(decisao);
        QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageDecisaoProcessoAdministrativo(), decisao.getChaveQRcode());
        param.setQRCodeParam(qrCodeParam);

        DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoDecisaoProcessoAdministrativo(param);
        File fileDecisao = null;
        if (dataReport != null) {
            fileDecisao = File.createTempFile("decisao" + decisao.getCodigo(), ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), fileDecisao.getAbsolutePath());
        }
        File anexos = getAnexosDecisao(decisao);
        List<InputStream> inputStreams;
        if (anexos != null) {
            inputStreams = getInputStreams(fileDecisao, anexos);
            return PdfUtil.mergePDFFiles(inputStreams, "anexos_decisao");
        } else {
            return fileDecisao;
        }
    }

    private static File getMergedFileRecurso(ProcessoAdministrativoRecurso recurso, DocumentoVigilanciaHtmlReport documentoVigilanciaHtmlReport) throws Exception {
        File certidao = resolveHtmlReport(documentoVigilanciaHtmlReport);
        File anexos = getAnexosRecurso(recurso);
        List<InputStream> inputStreams;
        if (anexos != null) {
            inputStreams = getInputStreams(certidao, anexos);
            return PdfUtil.mergePDFFiles(inputStreams, "anexos_recurso");
        } else {
            return certidao;
        }
    }

    private static List<InputStream> getInputStreams(File... fileList) throws Exception {
        List<InputStream> streams = new ArrayList<>();
        for (File file : fileList) {
            streams.add(new FileInputStream(file));
        }
        return streams;
    }

    private static File getAnexosParecer(ProcessoAdministrativoParecer processoAdministrativoParecer) throws ValidacaoException {
        List<RequerimentoVigilanciaAnexo> anexoList = VigilanciaHelper.carregarAnexosVigilancia(processoAdministrativoParecer);
        if (CollectionUtils.isNotNullEmpty(anexoList)) {
            try {
                List<InputStream> ios = new ArrayList<>();
                for (RequerimentoVigilanciaAnexo anexo : anexoList) {
                    File newFile = File.createTempFile("anexo", "vigilancia");
                    FileUtils.buscarArquivoFtp(anexo.getGerenciadorArquivo().getCaminho(), newFile.getAbsolutePath());
                    if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_PDF)) {
                        ios.add(new FileInputStream(newFile));
                    } else if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_JPG, FileUtils.MAGIC_NUMBER_PNG)) {
                        File filePdf = PdfUtil.convertImageToPdf(newFile, "image_anexo", anexo.getDescricao());
                        if (filePdf != null) {
                            ios.add(new FileInputStream(filePdf));
                        }
                    }
                }
                if (CollectionUtils.isNotNullEmpty(ios)) {
                    return PdfUtil.mergePDFFiles(ios, "anexos_proc_adm_visa_parecer");
                }
            } catch (IOException | DocumentException ex) {
                Loggable.log.error(ex);
            }
        }
        return null;
    }

    private static File getAnexosDecisao(ProcessoAdministrativoDecisao processoAdministrativoDecisao) throws ValidacaoException {
        List<RequerimentoVigilanciaAnexo> anexoList = VigilanciaHelper.carregarAnexosVigilancia(processoAdministrativoDecisao);
        if (CollectionUtils.isNotNullEmpty(anexoList)) {
            try {
                List<InputStream> ios = new ArrayList<>();
                for (RequerimentoVigilanciaAnexo anexo : anexoList) {
                    File newFile = File.createTempFile("anexo", "vigilancia");
                    FileUtils.buscarArquivoFtp(anexo.getGerenciadorArquivo().getCaminho(), newFile.getAbsolutePath());
                    if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_PDF)) {
                        ios.add(new FileInputStream(newFile));
                    } else if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_JPG, FileUtils.MAGIC_NUMBER_PNG)) {
                        File filePdf = PdfUtil.convertImageToPdf(newFile, "image_anexo", anexo.getDescricao());
                        ios.add(new FileInputStream(filePdf));
                    }
                }
                if (CollectionUtils.isNotNullEmpty(ios)) {
                    return PdfUtil.mergePDFFiles(ios, "anexos_proc_adm_visa_decisao");
                }
            } catch (IOException | DocumentException ex) {
                Loggable.log.error(ex);
            }
        }
        return null;
    }

    private static File getAnexosRecurso(ProcessoAdministrativoRecurso processoAdministrativoRecurso) throws ValidacaoException {
        List<RequerimentoVigilanciaAnexo> anexoList = VigilanciaHelper.carregarAnexosVigilancia(processoAdministrativoRecurso);
        if (CollectionUtils.isNotNullEmpty(anexoList)) {
            try {
                List<InputStream> ios = new ArrayList<>();
                for (RequerimentoVigilanciaAnexo anexo : anexoList) {
                    File newFile = File.createTempFile("anexo", "vigilancia");
                    FileUtils.buscarArquivoFtp(anexo.getGerenciadorArquivo().getCaminho(), newFile.getAbsolutePath());
                    if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_PDF)) {
                        ios.add(new FileInputStream(newFile));
                    } else if (newFile != null && FileUtils.isValidFile(newFile, FileUtils.MAGIC_NUMBER_JPG, FileUtils.MAGIC_NUMBER_PNG)) {
                        File filePdf = PdfUtil.convertImageToPdf(newFile, "image_anexo", anexo.getDescricao());
                        ios.add(new FileInputStream(filePdf));
                    }
                }
                if (CollectionUtils.isNotNullEmpty(ios)) {
                    return PdfUtil.mergePDFFiles(ios, "anexos_proc_adm_visa_recurso");
                }
            } catch (IOException | DocumentException ex) {
                Loggable.log.error(ex);
            }
        }
        return null;
    }

    public static void addAutosProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo, List<InputStream> documentosPdf) throws IOException, JRException, ReportException {
        if (processoAdministrativo != null && processoAdministrativo.getAutoInfracao() != null) {
            File pdfAutoInfracao = ProcessoAdministrativoFileHelper.getPDFAutoInfracao(processoAdministrativo.getAutoInfracao());
            if (pdfAutoInfracao != null) {
                documentosPdf.add(new FileInputStream(pdfAutoInfracao));
            }
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromInfracao(processoAdministrativo.getAutoInfracao());

            File pdfAutoIntimacao = ProcessoAdministrativoFileHelper.getPDFAutoIntimacao(autoIntimacao);
            if (pdfAutoIntimacao != null) {
                documentosPdf.add(new FileInputStream(pdfAutoIntimacao));
            }

            AutoIntimacao autoIntimacaoSubsistente = AutosHelper.getIntimacaoSubsistenteFromInfracao(processoAdministrativo.getAutoInfracao());
            File pdfIntimacaoSubsistente = ProcessoAdministrativoFileHelper.getPDFAutoIntimacaoSubsistente(autoIntimacaoSubsistente);
            if (pdfIntimacaoSubsistente != null) {
                documentosPdf.add(new FileInputStream(pdfIntimacaoSubsistente));
            }

        } else if (processoAdministrativo != null && processoAdministrativo.getAutoMulta() != null) {
            File pdfAutoMulta = ProcessoAdministrativoFileHelper.getPDFAutoMulta(processoAdministrativo.getAutoMulta());
            if (pdfAutoMulta != null) {
                documentosPdf.add(new FileInputStream(pdfAutoMulta));
            }
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromInfracao(processoAdministrativo.getAutoInfracao());

            File pdfAutoIntimacao = ProcessoAdministrativoFileHelper.getPDFAutoIntimacao(autoIntimacao);
            if (pdfAutoIntimacao != null) {
                documentosPdf.add(new FileInputStream(pdfAutoIntimacao));
            }

            AutoIntimacao autoIntimacaoSubsistente = AutosHelper.getIntimacaoSubsistenteFromMulta(processoAdministrativo.getAutoMulta());
            File pdfIntimacaoSubsistente = ProcessoAdministrativoFileHelper.getPDFAutoIntimacaoSubsistente(autoIntimacaoSubsistente);
            if (pdfIntimacaoSubsistente != null) {
                documentosPdf.add(new FileInputStream(pdfIntimacaoSubsistente));
            }
        } else if (processoAdministrativo != null && processoAdministrativo.getRequerimentoRestituicaoTaxa() != null) {

            RelatorioRequerimentoSolicitacaoJuridicaDTOParam param = new RelatorioRequerimentoSolicitacaoJuridicaDTOParam();
            param.setRequerimentoVigilancia(processoAdministrativo.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia());
            DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoSolicitacaoJuridica(param);
            File file = File.createTempFile("solicitacao_juridica_" + processoAdministrativo.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCodigo(), ".pdf");
            JasperExportManager.exportReportToPdfFile(comprovanteRequerimento.getJasperPrint(), file.getAbsolutePath());
            documentosPdf.add(new FileInputStream(file));
        }
    }

    public static void addRelatoriosInspecaoProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo, List<ArquivoProcessoDTO> documentosPdf) throws IOException, JRException, ReportException {
        if (processoAdministrativo != null && processoAdministrativo.getAutoInfracao() != null) {
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromInfracao(processoAdministrativo.getAutoInfracao());
            AutoIntimacao autoIntimacaoSubsistente = AutosHelper.getIntimacaoSubsistenteFromInfracao(processoAdministrativo.getAutoInfracao());

            ArquivoProcessoDTO pdfRelatorioInspecaoAntIntimacao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoAntIntimacao(autoIntimacao);
            if (pdfRelatorioInspecaoAntIntimacao != null) {
                documentosPdf.add(pdfRelatorioInspecaoAntIntimacao);
            }

            ArquivoProcessoDTO pdfRelatorioInspecaoAntInfracao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoAntInfracao(processoAdministrativo.getAutoInfracao());
            if (pdfRelatorioInspecaoAntInfracao != null) {
                documentosPdf.add(pdfRelatorioInspecaoAntInfracao);
            }

            ArquivoProcessoDTO pdfRelatorioInspecaoPosIntimacao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosIntimacao(autoIntimacao);
            if (pdfRelatorioInspecaoPosIntimacao != null) {
                documentosPdf.add(pdfRelatorioInspecaoPosIntimacao);
            }

            ArquivoProcessoDTO pdfRelatorioInspecaoPosIntimacaoSubsistente = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosIntimacao(autoIntimacaoSubsistente);
            if (pdfRelatorioInspecaoPosIntimacaoSubsistente != null) {
                documentosPdf.add(pdfRelatorioInspecaoPosIntimacaoSubsistente);
            }

            List<ArquivoProcessoDTO> pdfsRelatorioInspecaoPosInfracao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosInfracao(processoAdministrativo.getAutoInfracao());
            if (CollectionUtils.isNotNullEmpty(pdfsRelatorioInspecaoPosInfracao)) {
                documentosPdf.addAll(pdfsRelatorioInspecaoPosInfracao);
            }

        } else if (processoAdministrativo != null && processoAdministrativo.getAutoMulta() != null) {
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromInfracao(processoAdministrativo.getAutoInfracao());
            AutoIntimacao autoIntimacaoSubsistente = AutosHelper.getIntimacaoSubsistenteFromMulta(processoAdministrativo.getAutoMulta());

            ArquivoProcessoDTO pdfRelatorioInspecaoAntIntimacao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoAntIntimacao(autoIntimacao);
            if (pdfRelatorioInspecaoAntIntimacao != null) {
                documentosPdf.add(pdfRelatorioInspecaoAntIntimacao);
            }

            ArquivoProcessoDTO pdfRelatorioInspecaoPosIntimacao = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosIntimacao(autoIntimacao);
            if (pdfRelatorioInspecaoPosIntimacao != null) {
                documentosPdf.add(pdfRelatorioInspecaoPosIntimacao);
            }

            ArquivoProcessoDTO pdfRelatorioInspecaoPosIntimacaoSubsistente = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosIntimacao(autoIntimacaoSubsistente);
            if (pdfRelatorioInspecaoPosIntimacaoSubsistente != null) {
                documentosPdf.add(pdfRelatorioInspecaoPosIntimacaoSubsistente);
            }

            List<ArquivoProcessoDTO> pdfsRelatorioInspecaoPosMulta = ProcessoAdministrativoFileHelper.getPDFRelatorioInspecaoPosMulta(processoAdministrativo.getAutoMulta());
            if (CollectionUtils.isNotNullEmpty(pdfsRelatorioInspecaoPosMulta)) {
                documentosPdf.addAll(pdfsRelatorioInspecaoPosMulta);
            }
        }
    }

    public static File getPDFAutoIntimacaoSubsistente(AutoIntimacao autoIntimacao) throws IOException, JRException, ReportException {
        if (autoIntimacao != null) {
            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(autoIntimacao.getCodigo(), autoIntimacao.getNumeroFormatado());
            File file = File.createTempFile("auto_intimacao" + autoIntimacao.getCodigo(), ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());
            return file;
        } else {
            return null;
        }
    }

    public static File resolveHtmlReport(HtmlReport htmlReport) {
        return HtmlFileUtil.resolveHtmlReport(htmlReport);
    }
}
