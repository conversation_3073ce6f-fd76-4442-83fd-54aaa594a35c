package br.com.celk.view.agenda.solicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.DlgDetalhesAgendamento;
import br.com.celk.view.agenda.agendamento.dialog.DlgContatoAgendamentoListaEspera;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.recepcao.panel.agendamentos.AgendamentoTab;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class AcompanhamentoAgendamentoPage extends BasePage {

    private PnlDatePeriod pnlDatePeriod;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private DlgDetalhesAgendamento dialogDetalhesAgendamento;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private String nomePaciente;
    private boolean hasPermission;
    private Form<AgendaGradeAtendimentoDTOParam> form;
    private DlgImpressaoObject<RelatorioImprimirComprovanteAgendamentoDTOParam> dlgImpressao;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private List<AgendaGradeAtendimentoHorario> agendamentosList = new ArrayList<>();
    private DlgContatoAgendamentoListaEspera dlgContato;
    private AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
    private QueryPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam> pagerProvider;
    private PageParameters parameters;
    private boolean procurar;
    private WebMarkupContainer containerReferencia;
    private AgendaGradeAtendimentoDTOParam.TipoData tipoData;
    private PageableTable<AgendaGradeAtendimentoHorarioDTO> pageableTable;
    private Long cns;

    public AcompanhamentoAgendamentoPage(PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;
        init();
    }

    public AcompanhamentoAgendamentoPage(PageParameters parameters, boolean procurar) {
        super(parameters);
        this.parameters = parameters;
        this.procurar = procurar;
        init();
    }

    private void init() {
        hasPermission = isActionPermitted(Permissions.EMPRESA);

        getForm().add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true));
        autoCompleteConsultaTipoProcedimento.add(new ConsultaListener<TipoProcedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoProcedimento tipoProcedimento) {
                removeParameter();
                parameters.add("codigoTipoProcedimento", tipoProcedimento.getCodigo());
            }
        });
        autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                removeParameter();
            }
        });
        getForm().add(new AutoCompleteConsultaEmpresa("empresaOrigem").setValidaUsuarioEmpresa(!hasPermission));

        getForm().add(new AutoCompleteConsultaEmpresa("unidadeReferencia"));

        getForm().add(new InputField("nomeUsuarioCadsus"));

        getForm().add(new DateChooser("dataNascimento"));

        getForm().add(new InputField("cns"));

        getForm().add(containerReferencia = new WebMarkupContainer("containerReferencia"));
        containerReferencia.setOutputMarkupId(true);
        containerReferencia.add(new InputField("referencia"));

        containerReferencia.setVisible(visualizarReferencia());

        getForm().add(pnlDatePeriod = (PnlDatePeriod) new RequiredPnlDatePeriod("datePeriod").setLabel(new Model<>(bundle("periodo"))));
        getForm().add(populateDropDownSituacao(new DropDown<Long>("situacao")));
        getForm().add(DropDownUtil.getSimNaoDropDown("regulado", true, bundle("ambos")));
        getForm().add(populateDropDownTipoData(new DropDown<AgendaGradeAtendimentoDTOParam.TipoData>("tipoData")));

        ProcurarButton procurarButton;
        getForm().add(procurarButton = new ProcurarButton<AgendaGradeAtendimentoDTOParam>("btnProcurar", getPageableTable()) {
            @Override
            public AgendaGradeAtendimentoDTOParam getParam() {
                return AcompanhamentoAgendamentoPage.this.getParam();
            }
        });

        if (procurar) {
            procurarButton.procurar();
        }

        getForm().add(pageableTable);
        getForm().add(dialogDetalhesAgendamento = new DlgDetalhesAgendamento("dialogDetalhesAgendamento"));
        pnlDatePeriod.setModelObject(new DatePeriod(DataUtil.getDataAtual(), null));

        add(getForm());

    }

    private DropDown<AgendaGradeAtendimentoDTOParam.TipoData> populateDropDownTipoData(DropDown<AgendaGradeAtendimentoDTOParam.TipoData> dropDown) {

        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO, BundleManager.getString("agendamento"));
        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_CADASTRO, BundleManager.getString("cadastro"));

        return dropDown;
    }

    private void removeParameter() {
        if (!parameters.get("codigoTipoProcedimento").isEmpty()) {
            parameters.remove("codigoTipoProcedimento");
        }
    }

    private Form<AgendaGradeAtendimentoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<>(param));

            if (!parameters.get("codigoTipoProcedimento").isEmpty()) {
                Long codigoTipoProcedimento = parameters.get("codigoTipoProcedimento").toLongObject();
                TipoProcedimento tipoProcedimento = LoadManager.getInstance(TipoProcedimento.class).setId(codigoTipoProcedimento).start().getVO();
                param.setTipoProcedimento(tipoProcedimento);
            }
        }
        return this.form;
    }

    private DropDown<Long> populateDropDownSituacao(DropDown<Long> dropDown) {
        dropDown.addChoice(null, BundleManager.getString("todas"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, BundleManager.getString("agendado"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, BundleManager.getString("cancelado"));

        return dropDown;
    }

    public PageableTable getPageableTable() {
        if (this.pageableTable == null) {
            this.pageableTable = new PageableTable("table", getColumns(), getDataProvider());

            this.pageableTable.setScrollX("100%");
        }

        return this.pageableTable;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorarioDTO proxy = on(AgendaGradeAtendimentoHorarioDTO.class);

        columns.add(getCustomColumn());
        if (visualizarReferencia()) {
            columns.add(createSortableColumn(BundleManager.getString("referencia"), proxy.getAgendaGradeAtendimentoHorario().getUsuarioCadsus().getReferencia()));
        }
        columns.add(createSortableColumn(BundleManager.getString("paciente"), proxy.getNomePaciente()));
        columns.add(createSortableColumn(BundleManager.getString("tipo_procedimento"), proxy.getAgendaGradeAtendimentoHorario().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getAgendaGradeAtendimentoHorario().getSituacao()));
        columns.add(createSortableColumn(BundleManager.getString("dataAgendamento"), proxy.getDataAgendamentoFormatadoDataHora()));
        columns.add(createSortableColumn(BundleManager.getString("unidadeResponsavel"), proxy.getAgendaGradeAtendimentoHorario().getEmpresaOrigem().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("unidadeReferencia"), proxy.getUnidadeReferencia()));
        columns.add(createSortableColumn(BundleManager.getString("ultimoContato"), proxy.getDataUltimoContatoFormatadoDataHora()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorarioDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoHorarioDTO rowObject) {
                addAction(ActionType.CONTATO, rowObject,
                        new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new ContatoAgendamentoAcompanhamentoPage(modelObject, parameters));
                            }
                        }
                ).setTitleBundleKey("registrarContato");

                addAction(ActionType.CONSULTAR, rowObject,
                        new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new DetalhesSolicitacaoPage(modelObject.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getCodigo()));
                            }
                        }
                ).setTitleBundleKey("detalhes_solicitacao");
            }
        };
    }

    private QueryPagerProvider getDataProvider() {
        if (pagerProvider == null) {
            pagerProvider = new QueryPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam>() {
                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(" 1 nulls first, 2", true);
                }

                @Override
                public void customizeParam(AgendaGradeAtendimentoDTOParam param) {
                    SingleSortState<String> sortState = (SingleSortState) getDataProvider().getSortState();
                    if (sortState.getSort() != null) {
                        param.setCampoOrdenacao(sortState.getSort().getProperty());
                        param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");
                    }
                }

                @Override
                public DataPagingResult executeQueryPager(DataPaging<AgendaGradeAtendimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentosAcompanhamentoPager(dataPaging);
                }

            };
        }
        return pagerProvider;
    }

    private AgendaGradeAtendimentoDTOParam getParam() {
        param.setPermission(hasPermission);
        param.setEmpresas(null);
        if(this.cns != null) param.setCns(cns);

        Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();
        if (getForm().getModel().getObject().getEmpresaOrigem() == null && !usuario.isNivelAdminOrMaster() && !hasPermission) {
            try {
                usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                param.setEmpresas(usuario.getEmpresasUsuario());
            } catch (SGKException ex) {
                Logger.getLogger(AgendamentoTab.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_NOME_PACIENTE));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_DATA_CADASTRO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_STATUS));

        return param;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("acompanhamento_agendamento");
    }

    private boolean visualizarReferencia() {
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        return false;
    }

}
