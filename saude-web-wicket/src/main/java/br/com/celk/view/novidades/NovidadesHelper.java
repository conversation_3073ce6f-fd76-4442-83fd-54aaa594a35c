package br.com.celk.view.novidades;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.nota.ControleNota;
import br.com.ksisolucoes.vo.nota.Nota;
import br.com.ksisolucoes.vo.nota.Versao;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class NovidadesHelper {

    public static String carregarNotasVersao(Long codigoPrograma) {
        List<ControleNota> lstControleNota = new ArrayList();

        if (codigoPrograma != null) {
            lstControleNota = LoadManager.getInstance(ControleNota.class)
                    .addProperties(new HQLProperties(ControleNota.class).getProperties())
                    .addProperty(VOUtils.montarPath(ControleNota.PROP_NOTA, Nota.PROP_VERSAO, Versao.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ControleNota.PROP_USUARIO, SessaoAplicacaoImp.getInstance().getUsuario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleNota.PROP_NOTA, Nota.PROP_CODIGO_PROGRAMA), codigoPrograma))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleNota.PROP_NOTA, Nota.PROP_VERSAO, Versao.PROP_DESCRICAO), QueryCustom.QueryCustomParameter.MENOR_IGUAL, ApplicationSession.get().getVersao()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ControleNota.PROP_NOTA, Nota.PROP_VERSAO), "desc"))
                    .start().getList();
        }

        StringBuilder notasVersao = new StringBuilder();
        notasVersao.append("<div style=\"margin-left : 20px;height:558px;overflow:auto;\">");
        if (CollectionUtils.isNotNullEmpty(lstControleNota)) {
            List<ControleNota> notasNaoLidas = Lambda.select(lstControleNota, Lambda.having(Lambda.on(ControleNota.class).getLido(), Matchers.equalTo(RepositoryComponentDefault.NAO_LONG)));
            if (CollectionUtils.isNotNullEmpty(notasNaoLidas)) {
                for (ControleNota controleNota : notasNaoLidas) {
                    notasVersao.append("<strong style=\"font-size:32px;\">Versão: ")
                            .append(controleNota.getNota().getVersao().getDescricao())
                            .append("</strong>")
                            .append("<br></br>")
                            .append("<div style=\"margin-left : 20px;\">")
                            .append(controleNota.getNota().getDescricao())
                            .append("</div>")
                            .append("<br></br>");

                    try {
                        controleNota.setLido(RepositoryComponentDefault.SIM_LONG);
                        BOFactory.save(controleNota);
                    } catch (DAOException ex) {
                        Loggable.log.error(ex);
                    } catch (ValidacaoException ex) {
                        Loggable.log.error(ex);
                    }
                }
            } else {
                for (ControleNota controleNota : lstControleNota) {
                    notasVersao.append("<strong style=\"font-size:32px;\">Versão: ")
                            .append(controleNota.getNota().getVersao().getDescricao())
                            .append("</strong>")
                            .append("<br></br>")
                            .append("<div style=\"margin-left : 20px;\">")
                            .append(controleNota.getNota().getDescricao())
                            .append("</div>")
                            .append("<br></br>");
                }

            }
        } else {
            notasVersao.append("<strong style=\"font-size:32px;\">")
                    .append(BundleManager.getString("naoExisteAtualizacaoPrograma"))
                    .append("</strong>");
        }

        notasVersao.append("</div>");

        return notasVersao.toString();
    }

    public static String getScript(Long codigoPrograma) {
        String dados = carregarNotasVersao(codigoPrograma);
        dados = dados.replaceAll("'", "\"");
        dados = dados.replaceAll("\n", "");
        String script = "var win = window.open(\"\", \"\", \"width=800,height=600,left=\" + (document.documentElement.clientWidth - 800) / 2 + \",top=\" + (document.documentElement.clientHeight - 600) / 2, \"scroolbar=no\");  "
                + "$(win.document.head).html('<link rel=\"stylesheet\" type=\"text/css\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.3.5/css/bootstrap.min.css\"><title>CELK Saúde</title>');"
                + "$(win.document.body).html('" + dados + "');";
        return script;
    }

    public static boolean verificarNotasPrograma(Long codigoPrograma) {
        if (codigoPrograma != null) {
            Long count = LoadManager.getInstance(Nota.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(Nota.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Nota.PROP_CODIGO_PROGRAMA), codigoPrograma))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Nota.PROP_ATIVO), RepositoryComponentDefault.SIM_LONG))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Nota.PROP_VERSAO, Versao.PROP_DESCRICAO), QueryCustom.QueryCustomParameter.MENOR_IGUAL, ApplicationSession.get().getVersao()))
                    .start().getVO();
            if (count > 0L) {
                return true;
            }
        }
        return false;
    }
}
