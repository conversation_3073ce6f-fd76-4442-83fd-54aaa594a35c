package br.com.celk.view.publico.agenda.listaespera;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.publico.template.base.BasePagePublico;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.wicketstuff.annotation.mount.MountPath;

import java.io.File;

/**
 * <AUTHOR>
 */
@MountPath("lista-publica")
public class ListaPublicaHomePage extends BasePagePublico {

    public ListaPublicaHomePage() {
        super();
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();

        carregarBotaoBuscaFiltrada();

        add(new AbstractAjaxLink("btnConsultarFilaEsperaCompleta") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new ListaPublicaPage(true));
            }
        });
    }

    private void carregarBotaoBuscaFiltrada() {
        WebMarkupContainer contBuscaIndividual
                = criarBotaoBuscaFiltrada("BuscaIndividual");

        WebMarkupContainer contConsultarFilaEsperaPorCpfCns
                = criarBotaoBuscaFiltrada("ConsultarFilaEsperaPorCpfCns");

        boolean validaPermissaoHabilitaPesquisaCPFListaPublica
                = validaPermissaoHabilitaPesquisaCPFListaPublica();

        contConsultarFilaEsperaPorCpfCns.setVisible(validaPermissaoHabilitaPesquisaCPFListaPublica);
        contBuscaIndividual.setVisible(!validaPermissaoHabilitaPesquisaCPFListaPublica);
    }

    private WebMarkupContainer criarBotaoBuscaFiltrada(String id) {
        WebMarkupContainer contBtn = new WebMarkupContainer("cont" + id);
        contBtn.setOutputMarkupId(true);
        contBtn.add(new AbstractAjaxLink("btn" + id) {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new ListaPublicaPage(false));
            }
        });
        add(contBtn);
        return contBtn;
    }

    private boolean validaPermissaoHabilitaPesquisaCPFListaPublica() {
        try {
            String habilitaPesquisaCPFListaPublica = BOFactory.getBO(CommomFacade.class)
                    .modulo(Modulos.AGENDAMENTO)
                    .getParametro("HabilitaPesquisaCPFListaPublica");
            if (RepositoryComponentDefault.NAO.equals(habilitaPesquisaCPFListaPublica)) {
                return false;
            }
            return true;
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        return false;
    }

    @Override
    protected Image carregarLogo() {
        Image logoSistemaListaPublica = new Image("gemIco", Resources.Images.CELK_SAUDE_LIGHT.resourceReference());
        try {
            File logoSistemaTelaLoginFile = LogoHelper.getLogoListaPublica();
            if (logoSistemaTelaLoginFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaTelaLoginFile);
                logoSistemaListaPublica = new NonCachingImage("gemIco", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex) {
            logoSistemaListaPublica = new Image("gemIco", Resources.Images.CELK_SAUDE_LIGHT.resourceReference());
        }
        return logoSistemaListaPublica;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("listaPublica");
    }
}
