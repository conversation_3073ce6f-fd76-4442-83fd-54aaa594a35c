package br.com.celk.view.basico.estadocivil.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.cadsus.EstadoCivil;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaEstadoCivil extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(EstadoCivil.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(EstadoCivil.PROP_CODIGO));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), EstadoCivil.PROP_CODIGO);
        properties.put(BundleManager.getString("descricao"), EstadoCivil.PROP_DESCRICAO);
    }

    @Override
    public Class getClassConsulta() {
        return EstadoCivil.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(EstadoCivil.class).getProperties();
    }
    
}
