package br.com.celk.view.hospital.convenio.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.hospital.convenio.autocomplete.restricaocontainer.RestricaoContainerConvenio;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaConvenioDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaConvenio extends AutoCompleteConsulta<Convenio> { 

    private List<Long> codigoConvenioNotIn;
    
    public AutoCompleteConsultaConvenio(String id) {
        super(id);
    }

    public AutoCompleteConsultaConvenio(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaConvenio(String id, IModel<Convenio> model) {
        super(id, model);
    }

    public AutoCompleteConsultaConvenio(String id, IModel<Convenio> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(Convenio.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Convenio.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerConvenio(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Convenio, QueryConsultaConvenioDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaConvenioDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(HospitalFacade.class).consultarConvenio(dataPaging);
                    }

                    @Override
                    public QueryConsultaConvenioDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaConvenioDTOParam param = new QueryConsultaConvenioDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaConvenioDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setCodigoConvenioNotIn(codigoConvenioNotIn);
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Convenio.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return Convenio.class;
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("convenios");
    }

    public AutoCompleteConsultaConvenio setCodigoConvenioNotIn(List<Long> codigoConvenioNotIn) {
        this.codigoConvenioNotIn = codigoConvenioNotIn;
        return this;
    }
}
