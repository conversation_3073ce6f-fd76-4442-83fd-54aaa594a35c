<wicket:panel>
        <fieldset>
            <div class="field">
                <div class="span-horizontal">
                    <fieldset>
                        <h2><label><wicket:message key="dados"/></label></h2> 
                        <div class="field"><label><wicket:message key="descricao"/></label><input type="text" wicket:id="tipoAtendimento.descricao" maxlength="50" size="60" /></div>
                        <div class="field"><label><wicket:message key="setorAtendimento"/></label><div class="group" wicket:id="tipoAtendimento.empresa"/></div>
                        <div class="field"><label><wicket:message key="tipoAtendimentoPrincipal"/></label><div class="group" wicket:id="tipoAtendimento.tipoAtendimentoPrincipal"/></div>
                        <div class="field"><label><wicket:message key="tipoProcedimentoTransferencia"/></label><div class="group" wicket:id="tipoAtendimento.tipoProcedimentoTransferencia"/></div>
                        <div class="field"><label><wicket:message key="caraterAtendimento"/></label><select wicket:id="tipoAtendimento.caraterAtendimento"/></div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="exigeLeito"/></label><select wicket:id="tipoAtendimento.exigeLeito"/></div>
                            <div class="span-4 last"><label><wicket:message key="corListaAtendimento"/></label><div class="group" wicket:id="tipoAtendimento.corListaAtendimento"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="exigeProfissional"/></label><select wicket:id="tipoAtendimento.flagExigeProfissional"/></div>
                            <div class="span-4 last"><label><wicket:message key="transferivel"/></label><select wicket:id="tipoAtendimento.transferivel"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="idade"/></label><input type="text" maxlength="4" size="10" class="text-right number" wicket:id="tipoAtendimento.idadeInicial"/><input type="text" size="10" maxlength="4" class="text-right number"  wicket:id="tipoAtendimento.idadeFinal"/></div>
                            <div class="span-4 last"><label><wicket:message key="sexo"/></label><select wicket:id="tipoAtendimento.sexo"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="impressaoObstetrica"/></label><select wicket:id="tipoAtendimento.impressaoObstetrica"/></div>
                            <div class="span-4 last"><label><wicket:message key="cancelaPrimeiroAtendimento"/></label><select wicket:id="tipoAtendimento.flagCancelaPrimeiroAtendimento"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="imprimeProntuario"/></label><select wicket:id="tipoAtendimento.imprimeProntuario"/></div>
                            <div class="span-4 last"><label><wicket:message key="validaCns"/></label><select wicket:id="tipoAtendimento.flagValidaCns"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="imprimeNotaAlta" /></label><select wicket:id="tipoAtendimento.imprimeNotaAlta" /></div>
                            <div class="span-6 last"><label><wicket:message key="flagVisualizarProcedimentosFichaAtendimento" /></label><select wicket:id="tipoAtendimento.flagVisualizarProcedimentosFichaAtendimento" /></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="classificacaoAtendimento"/></label><select wicket:id="tipoAtendimento.classificacaoAtendimento"/></div>
                            <div class="span-4 last"><label><wicket:message key="transferenciaEstabelecimento"/></label><select wicket:id="tipoAtendimento.transferenciaEstabelecimento"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="validaDocumentosObrigatorios"/></label><select wicket:id="tipoAtendimento.validaDocumento"/></div>
                            <div class="span-4 last"><label><wicket:message key="orientacao"/></label><select wicket:id="tipoAtendimento.tipoOrientacao"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="dispensacaoPrescricaoPorTurno" /></label><select wicket:id="tipoAtendimento.dispensacaoPorTurno" /></div>
                            <div class="span-6 last"><label><wicket:message key="tituloImpressaoProntuario"/></label><input type="text" size="60" maxlength="60" wicket:id="tipoAtendimento.tituloProntuario"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="atendimentoGestante" /></label><select wicket:id="tipoAtendimento.flagAtendimentoGestante" /></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="atendimentoPeriodico" /></label><select wicket:id="tipoAtendimento.flagPeriodico" /></div>
                            <div class="span-6 last"><label><wicket:message key="numeroDias" /></label><input type="text" size="10" maxlength="3" wicket:id="tipoAtendimento.numeroDias" /></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="autorizaExames" /></label><select wicket:id="tipoAtendimento.flagAutorizaExame" /></div>
                            <div class="span-4 last"><label><wicket:message key="permiteReclassificacao"/></label><select wicket:id="tipoAtendimento.flagPermiteReclassificacao"/></div>
                        </div>
                        <div class="field">
                            <div class="span-4"><label><wicket:message key="exigeUnidadeOrige"/></label><select wicket:id="tipoAtendimento.flagExigeUnidadeOrigem"/></div>
                            <div class="span-4 last"><label><wicket:message key="notificacaoAvulsaNoAtendimento"/></label><select wicket:id="tipoAtendimento.flagNotificacaoAvulsaAtendimento"/></div>
                            <div class="span-4 last"><label><wicket:message key="validarFichaAcolhimentoAtendimento"/></label><select wicket:id="tipoAtendimento.flagValidaFichaAcolhimento"/></div>
                        </div>
                        <div class="field">
                            <label><wicket:message key="utilidade"/></label><textarea wicket:id="tipoAtendimento.utilidade" maxlength="1500" class="textarea-only no-resize" style="width: 360px;"/>
                        </div>
                    </fieldset>
                </div>
            </div>
        </fieldset>
</wicket:panel>