package br.com.celk.view.unidadesaude.esus.domicilio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.behavior.attribute.AttributeRemover;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.esus.domicilio.CadastroDomicilioDTO;
import br.com.celk.esus.domicilio.ComponenteDomicilioDTO;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.unidadesaude.esus.domicilio.tabbedpanel.CadastroDomicilioEsusTabbedPanel;
import br.com.celk.view.unidadesaude.esus.domicilio.tabbedpanel.ComponentesTab;
import br.com.celk.view.unidadesaude.esus.domicilio.tabbedpanel.DadosDomicilioTab;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.CidadeCep;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroDomicilioEsusPage extends BasePage {

    public static final int TAB_DADOS_DOMICILIO = 0;
    public static final int TAB_COMPONENTES = 1;
    private int selectedTab;
    private CadastroDomicilioDTO cadastroDomicilioDTO;

    public CadastroDomicilioEsusPage() {
        this(null, TAB_DADOS_DOMICILIO);
    }

    public CadastroDomicilioEsusPage(CadastroDomicilioDTO cadastroDomicilioDTO) {
        this(cadastroDomicilioDTO, TAB_DADOS_DOMICILIO);
    }

    public CadastroDomicilioEsusPage(CadastroDomicilioDTO cadastroDomicilioDTO, int selectedTab) {
        this.cadastroDomicilioDTO = cadastroDomicilioDTO;
        this.selectedTab = selectedTab;
        init();
    }

    private void init() {
        newInstance();
        if(cadastroDomicilioDTO.getEnderecoDomicilioEsus() != null && cadastroDomicilioDTO.getEnderecoDomicilioEsus().getNumeroCartaoResponsavelTecnico() != null){
            cadastroDomicilioDTO.setNumeroCartaoResponsavelTecnico(cadastroDomicilioDTO.getEnderecoDomicilioEsus().getNumeroCartaoResponsavelTecnico().toString());
        }

        final Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
        List<ITab> tabs = new ArrayList<>();

        tabs.add(new CadastroTab<CadastroDomicilioDTO>(cadastroDomicilioDTO) {
            @Override
            public ITabPanel<CadastroDomicilioDTO> newTabPanel(String panelId, CadastroDomicilioDTO cadastroDomicilioDTO) {
                return new DadosDomicilioTab(panelId, cadastroDomicilioDTO, isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL));
            }
        });
        tabs.add(new CadastroTab<CadastroDomicilioDTO>(cadastroDomicilioDTO) {
            @Override
            public ITabPanel<CadastroDomicilioDTO> newTabPanel(String panelId, CadastroDomicilioDTO cadastroDomicilioDTO) {
                return new ComponentesTab(panelId, cadastroDomicilioDTO);
            }
        });

        boolean permissaoEditar = isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL, ConsultaDomicilioEsusPage.class);
        cadastroDomicilioDTO.setPermissaoEditar(permissaoEditar);

        CadastroDomicilioEsusTabbedPanel cadastroTabbedPanel = new CadastroDomicilioEsusTabbedPanel("wizard", cadastroDomicilioDTO, tabs);
        cadastroTabbedPanel.setSelectedTab(selectedTab);
        cadastroTabbedPanel.getForm().add(new AttributeRemover("class"));
        add(cadastroTabbedPanel);
    }

    private void newInstance() {
        if (cadastroDomicilioDTO == null) {
            cadastroDomicilioDTO = new CadastroDomicilioDTO();

            Cidade cidade = ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCidade();
            CidadeCep cidadeCep = LoadManager.getInstance(CidadeCep.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(CidadeCep.PROP_CIDADE, cidade))
                    .start().getVO();

            EnderecoUsuarioCadsus enderecoUsuarioCadsus = new EnderecoUsuarioCadsus();
            enderecoUsuarioCadsus.setCidade(cidade);
            if (cidadeCep != null) {
                enderecoUsuarioCadsus.setCep(cidadeCep.getCep());
            }

            EnderecoDomicilio enderecoDomicilio = new EnderecoDomicilio();
            enderecoDomicilio.setEnderecoUsuarioCadsus(enderecoUsuarioCadsus);

            cadastroDomicilioDTO.setEnderecoDomicilio(enderecoDomicilio);
            cadastroDomicilioDTO.setEnderecoDomicilioEsus(new EnderecoDomicilioEsus());
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroDomiciliar");
    }
}
