package br.com.celk.view.vigilancia.registroagravo.panel;

import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.util.DataUtil;
import br.com.celk.view.cadsus.usuariocadsus.CadastroPacientePage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Rafael.Dantas on 28/05/2020.
 */
public class PnlDadosBasicosFichasInvestigacao extends Panel {

    private final PnlDadosBasicosFichasInvestigacaoDTO dto;
    private Form<RegistroAgravo> form;
    private DateChooser dchDataPrimeirosSintomas;

    public PnlDadosBasicosFichasInvestigacao(PnlDadosBasicosFichasInvestigacaoDTO dto) {
        super("dadosBasicosFichasInvestigacao");
        this.dto = dto;
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(dto.getAgravo()));
        RegistroAgravo proxy = on(RegistroAgravo.class);

        carregarDadosGerais(proxy);
        carregarNotificacaoIndividual(proxy);
        carregarDadosResidencia(proxy);
        carregarEdicaoPaciente();

        add(form);
    }

    private void carregarDadosGerais(RegistroAgravo proxy) {
        form.add(new DisabledInputField(path(proxy.getCodigoNotificacao())));
        form.add(new DisabledInputField(path(proxy.getCid().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getDataRegistro())));
        form.add(new DisabledInputField(path(proxy.getDataEncerramento())));
        form.add(new DisabledInputField(path(proxy.getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getDescricaoGestante())));
        form.add(dchDataPrimeirosSintomas = new DateChooser(path(proxy.getDataPrimeirosSintomas())));
        form.add(new DisabledInputField(path(proxy.getDescricaoStatus())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadastro().getNome())));
        form.add(new DisabledInputField(path(proxy.getProfissional().getNome())));
        dchDataPrimeirosSintomas.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchDataPrimeirosSintomas.setEnabled(!dto.isModoLeitura());
    }

    private void carregarNotificacaoIndividual(RegistroAgravo proxy) {
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNome())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getDataNascimento())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getSexoFormatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeMae())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefoneFormatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone2Formatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone3Formatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone4Formatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getCelularFormatado())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getEmail())));
    }

    private void carregarDadosResidencia(RegistroAgravo proxy) {
        form.add(new DisabledInputField(path(proxy.getEndereco().getCidade().getEstado().getSigla())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getCidade().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getBairro())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getLogradouro())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getNumeroLogradouro())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getComplementoLogradouro())));
        form.add(new DisabledInputField(path(proxy.getEndereco().getCepFormatado())));
    }

    private void carregarEdicaoPaciente() {
        AbstractAjaxLink btnEditarPaciente;
        form.add(btnEditarPaciente = new AbstractAjaxLink("btnEditarPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                setResponsePage(new CadastroPacientePage(dto.getAgravo().getUsuarioCadsus(), true) {
                    @Override
                    public void retornaPagina(AjaxRequestTarget target) {
                        recarregarPaciente();
                        setResponsePage(dto.getPaginaOrigem());
                    }
                });
            }
        });
        btnEditarPaciente.setVisible(verificaPermissaoEditarPaciente());
        btnEditarPaciente.setOutputMarkupPlaceholderTag(true);
        btnEditarPaciente.setEnabled(!dto.isModoLeitura());
    }

    private boolean verificaPermissaoEditarPaciente() {
        return new PermissoesWebUtil().isPagePermitted(SessaoAplicacaoImp.getInstance().getUsuario(),
                CadastroPacientePage.class.getName());
    }

    private void recarregarPaciente() {
        UsuarioCadsus pacienteAtualizado = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperties(new HQLProperties(UsuarioCadsus.class).getProperties())
                .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS).getProperties())
                .setId(dto.getAgravo().getUsuarioCadsus().getCodigo()).start().getVO();
        form.getModel().getObject().setUsuarioCadsus(pacienteAtualizado);
    }

    public DateChooser getDataPrimeirosSintomas(){
        return dchDataPrimeirosSintomas;
    }
}
