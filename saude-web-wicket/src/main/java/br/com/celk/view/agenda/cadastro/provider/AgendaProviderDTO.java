package br.com.celk.view.agenda.cadastro.provider;

import br.com.celk.bo.treetable.interfaces.dto.TreeTableDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AgendaProviderDTO extends TreeTableDTO implements Serializable {

    private AgendaGradeAtendimento agendaGradeAtendimento;
    private AgendaGradeHorario agendaGradeHorario;
    private Long vagasAgendadas;
    private Long vagasDisponiveis;

    private List<AgendaProviderDTO> filhos;
    private AgendaProviderDTO pai;

    public AgendaProviderDTO getPai() {
        return pai;
    }

    public void setPai(AgendaProviderDTO pai) {
        this.pai = pai;
    }

    public List<AgendaProviderDTO> getFilhos() {
        if (filhos == null) {
            filhos = new ArrayList<AgendaProviderDTO>();
        }
        return filhos;
    }

    public void addFilho(AgendaProviderDTO filho) {
        if (filhos == null) {
            filhos = new ArrayList<AgendaProviderDTO>();
        }
        filhos.add(filho);
    }

    public AgendaGradeAtendimento getAgendaGradeAtendimento() {
        return agendaGradeAtendimento;
    }

    public void setAgendaGradeAtendimento(AgendaGradeAtendimento agendaGradeAtendimento) {
        this.agendaGradeAtendimento = agendaGradeAtendimento;
    }

    public AgendaGradeHorario getAgendaGradeHorario() {
        return agendaGradeHorario;
    }

    public void setAgendaGradeHorario(AgendaGradeHorario agendaGradeHorario) {
        this.agendaGradeHorario = agendaGradeHorario;
    }

    @Override
    public Object getRoot() {
        return agendaGradeAtendimento;
    }

    @Override
    public Object getLeaf() {
        return agendaGradeHorario;
    }

    public Long getVagasAgendadas() {
        return vagasAgendadas;
    }

    public void setVagasAgendadas(Long vagasAgendadas) {
        this.vagasAgendadas = vagasAgendadas;
    }

    public Long getVagasDisponiveis() {
        return vagasDisponiveis;
    }

    public void setVagasDisponiveis(Long vagasDisponiveis) {
        this.vagasDisponiveis = vagasDisponiveis;
    }
}
