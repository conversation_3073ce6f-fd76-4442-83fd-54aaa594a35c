package br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoclinico;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoConsultaHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.web.historico.dto.HistoricoMedicamentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusPatologia;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DadosClinicoConsultaTab extends TabPanel<NoHistoricoClinicoDTO> {

    private IModel<NoConsultaHistoricoClinicoDTO> model;
    private UsuarioCadsus usuarioCadsus;
    private WebMarkupContainer containerOcorrencias;

    public DadosClinicoConsultaTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        this.usuarioCadsus = object.getUsuarioCadsus();
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();
        init();
    }

    public void init() {
        setDefaultModel(model = new CompoundPropertyModel<>(new LoadableDetachableModel<NoConsultaHistoricoClinicoDTO>() {
            @Override
            protected NoConsultaHistoricoClinicoDTO load() {
                try {
                    return BOFactoryWicket.getBO(AtendimentoFacade.class).carregarNodoConsultaHistoricoClinico(usuarioCadsus.getCodigo());
                } catch (DAOException | ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return new NoConsultaHistoricoClinicoDTO();
            }
        }));

        Table tblPrincipaisPatologias = new Table("tblPrincipaisPatologias", getColumnsPrincipaisPatologias(), getCollectionProviderPrincipaisPatologias());
        Table tblMedicamentos = new Table("tblMedicamentos", getColumnsMedicamentos(), getCollectionProviderMedicamentos());
        Table tblProgramasSaude = new Table("tblProgramasSaude", getColumnsProgramasSaude(), getCollectionProviderProgramasSaude());
        Table tblHistoricoAvaliacoes = new Table("tblHistoricoAvaliacoes", getColumnsHistoricoAvaliacoes(), getCollectionProviderHistoricoAvaliacoes());

        add(tblPrincipaisPatologias, tblMedicamentos, tblProgramasSaude, tblHistoricoAvaliacoes);

        tblPrincipaisPatologias.populate();
        tblMedicamentos.populate();
        tblProgramasSaude.populate();
        tblHistoricoAvaliacoes.populate();

        add(containerOcorrencias = new WebMarkupContainer("containerOcorrencias"));
        containerOcorrencias.setOutputMarkupId(true);
        Table tblOcorrencias = new Table("tblOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias());
        containerOcorrencias.add(tblOcorrencias);
        tblOcorrencias.populate();

    }

    private ICollectionProvider getCollectionProviderOcorrencias() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getOcorrenciasList();
            }
        };
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList<IColumn>();

        UsuarioCadsusOcorrencia proxy = on(UsuarioCadsusOcorrencia.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataOcorrencia())));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsPrincipaisPatologias() {
        List<IColumn> columns = new ArrayList<>();

        UsuarioCadsusPatologia proxy = on(UsuarioCadsusPatologia.class);


        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataAtendimento())));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getCodigo()));
        columns.add(createColumn(bundle("descricao"), proxy.getCid().getDescricao()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderPrincipaisPatologias() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getPatologiaList();
            }
        };
    }

    private List<IColumn> getColumnsMedicamentos() {
        List<IColumn> columns = new ArrayList<>();

        HistoricoMedicamentoDTO proxy = on(HistoricoMedicamentoDTO.class);

        columns.add(createColumn(bundle("medicamento", this), proxy.getNomeProduto()));
        columns.add(createColumn(bundle("posologia", this), proxy.getPosologia()));
        columns.add(createColumn(bundle("cid", this), proxy.getCodigoCid()));
        columns.add(new DateColumn(bundle("ultimaReceita", this), path(proxy.getDataUltimaReceita())));
        columns.add(createColumn(bundle("tipoReceita", this), proxy.getDescricaoTipoReceita()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderMedicamentos() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getHistoricoMedicamentoList();
            }
        };
    }

    private List<IColumn> getColumnsProgramasSaude() {
        List<IColumn> columns = new ArrayList<>();

        ProgramaSaudeUsuario proxy = on(ProgramaSaudeUsuario.class);

        columns.add(createColumn(bundle("programa"), proxy.getDescricaoProgramaSaude()));
        columns.add(new DateTimeColumn(bundle("dataCadastro"), path(proxy.getDataCadastro())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderProgramasSaude() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getProgramasSaudeList();
            }
        };
    }

    private List<IColumn> getColumnsHistoricoAvaliacoes() {
        List<IColumn> columns = new ArrayList<>();

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getAtendimento().getDataAtendimento())));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pas"), proxy.getPressaoArterialSistolica()));
        columns.add(createColumn(bundle("pad"), proxy.getPressaoArterialDiastolica()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("glicemia"), proxy.getGlicemia()));
        columns.add(createColumn(bundle("imc"), proxy.getImc()));
        columns.add(createColumn(bundle("pontosGlasgow"), proxy.getTotalGlasgow()));
        columns.add(createColumn(bundle("escalaDor"), proxy.getEscalaDor()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistoricoAvaliacoes() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getAtendimentoPrimarioList();
            }
        };
    }

    @Override
    public String getTitle() {
        return bundle("dadosClinicos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (CollectionUtils.isNotNullEmpty(model.getObject().getOcorrenciasList())) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.showFieldset(containerOcorrencias)));
        }
    }

}