package br.com.celk.view.hospital.faturamento.dialogs.ipe;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLancamentosConfirmadosProntoAtendimento extends Window {

    private PnlLancamentosConfirmadosProntoAtenidmento pnlLancamentosConfirmadosProntoAtenidmento;

    public DlgLancamentosConfirmadosProntoAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(400);
        setInitialWidth(1000);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("lancamentosConfirmados");
            }
        });

        setContent(pnlLancamentosConfirmadosProntoAtenidmento = new PnlLancamentosConfirmadosProntoAtenidmento(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgLancamentosConfirmadosProntoAtendimento.this.onFechar(target);
                close(target);
            }

            @Override
            public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                DlgLancamentosConfirmadosProntoAtendimento.this.onReverter(target, itemContaPaciente);
            }
        });
    }

    public List<ItemContaPaciente> getListaItens() {
        return pnlLancamentosConfirmadosProntoAtenidmento.getListaItens();
    }

    public void setItemLista(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        pnlLancamentosConfirmadosProntoAtenidmento.setItemContaPaciente(target, itemContaPaciente);
    }

    public void setListItem(AjaxRequestTarget target, List<ItemContaPaciente> lista) {
        pnlLancamentosConfirmadosProntoAtenidmento.setListItemContaPaciente(target, lista);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public abstract void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
}
