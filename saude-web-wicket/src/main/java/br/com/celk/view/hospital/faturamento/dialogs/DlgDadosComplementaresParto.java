package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDadosComplementaresParto extends Window {

    private PnlDadosComplementaresParto pnlDadosComplementaresParto;
    private ItemContaPaciente itemContaPaciente;

    public DlgDadosComplementaresParto(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("dadosComplementaresParto"));
        setInitialWidth(400);
        setInitialHeight(200);
        setResizable(false);

        setContent(pnlDadosComplementaresParto = new PnlDadosComplementaresParto(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                close(target);
                DlgDadosComplementaresParto.this.onFechar(target, itemContaPaciente);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
                DlgDadosComplementaresParto.this.onFechar(target, itemContaPaciente);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                close(target);
                onFechar(target, itemContaPaciente);
                return false;
            }
        });
    }

    public void show(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        super.show(target);
        this.itemContaPaciente = itemContaPaciente;
        pnlDadosComplementaresParto.limpar(target);
        pnlDadosComplementaresParto.setItemContaPaciente(target, itemContaPaciente);
    }

    public abstract void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente);
}
