package br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class DlgAnexosAutoIntimacaoExigencia<T extends Serializable> extends Window {

    private T object;
    private PnlAnexosAutoIntimacaoExigencia pnlAnexosAutoIntimacaoExigencia;
    private AutoIntimacao autoIntimacao;
    private boolean enabled;
    private TipoSolicitacao tipoSolicitacao;

    public DlgAnexosAutoIntimacaoExigencia(String id, AutoIntimacao autoIntimacao, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled) {
        super(id);
        init(autoIntimacao, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, false, false);
    }

    public DlgAnexosAutoIntimacaoExigencia(String id, AutoIntimacao autoIntimacao, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled, boolean exibirObservacao, boolean exibirTipoAnexoDescritivo) {
        super(id);
        init(autoIntimacao, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, exibirObservacao, exibirTipoAnexoDescritivo);
    }

    private void init(AutoIntimacao autoIntimacao, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled, boolean exibirObservacao, boolean exibirTipoAnexoDescritivo) {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("anexos");
            }
        });

        setInitialWidth(1024);
        setInitialHeight(450);
        setResizable(true);

        setContent(pnlAnexosAutoIntimacaoExigencia = new PnlAnexosAutoIntimacaoExigencia(getContentId(), autoIntimacao, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, exibirObservacao, exibirTipoAnexoDescritivo) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException {
                close(target);
                DlgAnexosAutoIntimacaoExigencia.this.onConfirmar(target, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList);
            }

            @Override
            public void onConfirmarReenviar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException {
                close(target);
                DlgAnexosAutoIntimacaoExigencia.this.onConfirmarReenviar(target, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public boolean isBtnConfirmarReenviarVisible() {
                return DlgAnexosAutoIntimacaoExigencia.this.isBtnConfirmarReenviarVisible();
            }
        });
    }

    public T getObject() {
        return object;
    }

    public void show(AjaxRequestTarget target, T object) {
        super.show(target);
        this.object = object;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException;

    public abstract void onConfirmarReenviar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException;

    public boolean isBtnConfirmarReenviarVisible() {
        return true;
    }

}