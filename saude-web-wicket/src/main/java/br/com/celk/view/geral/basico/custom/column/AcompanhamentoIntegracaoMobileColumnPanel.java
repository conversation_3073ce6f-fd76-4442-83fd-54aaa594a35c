package br.com.celk.view.geral.basico.custom.column;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.geral.basico.custom.column.dialog.acoeslote.DlgAcoesLote;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class AcompanhamentoIntegracaoMobileColumnPanel extends Panel {

    private AjaxLink btnAcoesLote;
    private IntegracaoMobile integracaoMobile;
    private DlgAcoesLote dlgAcoesLote;


    public AcompanhamentoIntegracaoMobileColumnPanel(String id, IntegracaoMobile integracaoMobile) {

        super(id);

        this.integracaoMobile = integracaoMobile;

        init();
    }

    private void init() {

        btnAcoesLote = new AbstractAjaxLink("btnAcoesLote") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                initDlgAcoesLote(target);

                dlgAcoesLote.show(target);
            }
        };

        btnAcoesLote.add(new AttributeModifier("title", BundleManager.getString("detalhesLote")));

        add(btnAcoesLote);
    }

    private void initDlgAcoesLote(AjaxRequestTarget target) {

        if (dlgAcoesLote == null) {

            dlgAcoesLote = new DlgAcoesLote(WindowUtil.newModalId(this), integracaoMobile) {

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                    refreshColumnPanel(target);
                }
            };

            WindowUtil.addModal(target, this, dlgAcoesLote);
        }
    }

    public void refreshColumnPanel(AjaxRequestTarget target) {

    }
}