package br.com.celk.view.consorcio.dirf;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConfiguracaoDirfDTO;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConfiguracaoDirf;
import br.com.ksisolucoes.vo.consorcio.GeracaoDirf;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> Schmoeller.
 */
@Private
public class ConfiguracaoDIRFPage extends BasePage {

    private InputField txtIdentificadorEstruturaLeiaute;
    private InputField txtCnpjDeclarante;
    private InputField txtNomeEmpresarial;
    private InputField txtCpfResponsavelCnpj;
    private InputField txtCpfResponsavelPreenchimento;
    private InputField txtNomeResponsavelPreenchimento;
    private InputField txtTelefoneResponsavelPreenchimento;
    private InputField txtRamalResponsavelPreenchimento;
    private InputField txtFaxResponsavelPreenchimento;
    private InputField txtEmailResponsavelPreenchimento;
    private Form<ConfiguracaoDirfDTO> form;
    private DropDown<Long> dropDownAno;


    public ConfiguracaoDIRFPage() {
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(new ConfiguracaoDirfDTO()));
        carregarConfiguracoes();
        ConfiguracaoDirfDTO proxy = on(ConfiguracaoDirfDTO.class);

        form.add(dropDownAno = DropDownUtil.getAnoDropDown(path(proxy.getConfiguracaoDirf().getAno()), false, false, getAnoInicial(), false));

        dropDownAno.removeChoice(Long.valueOf(DataUtil.getAnoAtual()));

        form.add(txtIdentificadorEstruturaLeiaute = new InputField<String>(path(proxy.getConfiguracaoDirf().getIdentificadorEstruturaLeiaute())));
        txtIdentificadorEstruturaLeiaute.setRequired(true);
        txtIdentificadorEstruturaLeiaute.addRequiredClass();
        txtIdentificadorEstruturaLeiaute.setLabel(new Model<String>(bundle("identificadorEstruturaLeiaute")));

        form.add(txtCnpjDeclarante = new InputField<String>(path(proxy.getConfiguracaoDirf().getCnpjDeclarante())));
        txtCnpjDeclarante.setRequired(true);
        txtCnpjDeclarante.addRequiredClass();
        txtCnpjDeclarante.setLabel(new Model<String>(bundle("cnpj")));

        form.add(txtNomeEmpresarial = new InputField<String>(path(proxy.getConfiguracaoDirf().getNomeEmpresarial())));
        txtNomeEmpresarial.setRequired(true);
        txtNomeEmpresarial.addRequiredClass();
        txtNomeEmpresarial.setLabel(new Model<String>(bundle("nomeEmpresarial")));

        form.add(txtCpfResponsavelCnpj = new InputField<String>(path(proxy.getConfiguracaoDirf().getCpfResponsavelCnpj())));
        txtCpfResponsavelCnpj.setRequired(true);
        txtCpfResponsavelCnpj.addRequiredClass();
        txtCpfResponsavelCnpj.setLabel(new Model<String>(bundle("cpf")));

        form.add(txtCpfResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getCpfResponsavelPreenchimento())));
        txtCpfResponsavelPreenchimento.setRequired(true);
        txtCpfResponsavelPreenchimento.addRequiredClass();
        txtCpfResponsavelPreenchimento.setLabel(new Model<String>(bundle("cpf")));

        form.add(txtNomeResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getNomeResponsavelPreenchimento())));
        txtNomeResponsavelPreenchimento.setRequired(true);
        txtNomeResponsavelPreenchimento.addRequiredClass();
        txtNomeResponsavelPreenchimento.setLabel(new Model<String>(bundle("nome")));

        form.add(txtTelefoneResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getTelefoneResponsavelPreenchimento())));
        txtTelefoneResponsavelPreenchimento.setRequired(true);
        txtTelefoneResponsavelPreenchimento.addRequiredClass();
        txtTelefoneResponsavelPreenchimento.setLabel(new Model<String>(bundle("telefone")));

        form.add(txtRamalResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getRamalResponsavelPreenchimento())));
        form.add(txtFaxResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getFaxResponsavelPreenchimento())));
        form.add(txtEmailResponsavelPreenchimento = new InputField<String>(path(proxy.getConfiguracaoDirf().getEmailResponsavelPreenchimento())));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(form);
    }

    private void carregarConfiguracoes() {
        ConfiguracaoDirf configuracaoDirf = LoadManager.getInstance(ConfiguracaoDirf.class)
                .addProperties(new HQLProperties(ConfiguracaoDirf.class).getProperties())
                .start().getVO();

        if(configuracaoDirf != null && configuracaoDirf.getCodigo() != null){
            form.getModel().getObject().setConfiguracaoDirf(configuracaoDirf);
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCnpjDeclarante;
    }

    private void salvar() throws ValidacaoException, DAOException {

        if (form.getModelObject().getConfiguracaoDirf().getTelefoneResponsavelPreenchimento() == null) {
            throw new ValidacaoException(bundle("campoTelefoneObrigatorio"));
        }

        if (form.getModelObject().getConfiguracaoDirf().getIdentificadorEstruturaLeiaute() == null) {
            throw new ValidacaoException(bundle("msgIdentificadorEstruturaLeiaute"));
        }

        if (form.getModelObject().getConfiguracaoDirf().getEmailResponsavelPreenchimento() != null) {
            if (!EmailValidator.validarEmail(form.getModelObject().getConfiguracaoDirf().getEmailResponsavelPreenchimento())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_email_invalido"));
            }
        }
        ConfiguracaoDirfDTO configuracaoDirf = form.getModelObject();

        BOFactoryWicket.getBO(AtendimentoGeralFacade.class).salvarConfiguracaoDirf(configuracaoDirf);
        Page page = new GeracaoDirfPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
    }

    public int getAnoInicial() {
        return DataUtil.getAnoAtual() - GeracaoDirf.LIMITE_ANOS_ANTERIORES_VALIDOS_DIRF;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("configuracaoDirf");
    }
}
