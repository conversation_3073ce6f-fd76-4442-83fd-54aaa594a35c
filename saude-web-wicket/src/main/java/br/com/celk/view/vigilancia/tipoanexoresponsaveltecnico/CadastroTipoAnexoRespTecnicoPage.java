package br.com.celk.view.vigilancia.tipoanexoresponsaveltecnico;


import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.vigilancia.tipoanexoresptecnico.TipoAnexosResponsavelTecnico;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastroTipoAnexoRespTecnicoPage extends CadastroPage<TipoAnexosResponsavelTecnico> {

    private Form<TipoAnexosResponsavelTecnico> form;
    private InputField txtDescricao;
    private DropDown dropDownObrigatorio;

    public CadastroTipoAnexoRespTecnicoPage(TipoAnexosResponsavelTecnico object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroTipoAnexoRespTecnicoPage(TipoAnexosResponsavelTecnico object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoAnexoRespTecnicoPage(TipoAnexosResponsavelTecnico object) {
        this(object, false);
    }

    public CadastroTipoAnexoRespTecnicoPage() {
        this(null);
    }


    @Override
    public void init(Form form) {
        TipoAnexosResponsavelTecnico proxy = on(TipoAnexosResponsavelTecnico.class);

        getForm().add(txtDescricao = (InputField) new RequiredInputField(path(proxy.getDescricao())));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getObrigatorio()), TipoAnexosResponsavelTecnico.ObrigatorioEnum.values(), true));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<TipoAnexosResponsavelTecnico> getReferenceClass() {
        return TipoAnexosResponsavelTecnico.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoAnexoRespTecnicoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoAnexoRespTecnico");
    }




}
