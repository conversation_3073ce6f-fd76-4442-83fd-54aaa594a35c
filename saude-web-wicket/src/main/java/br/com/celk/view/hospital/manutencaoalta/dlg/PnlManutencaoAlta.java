package br.com.celk.view.hospital.manutencaoalta.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlManutencaoAlta extends Panel {

    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private DateChooserAjax dchDataAlta;
    private DropDown dropDownMotivoAlta;
    private AtendimentoAlta atendimentoAltaOriginal;
    private CompoundPropertyModel<AtendimentoAlta> model;
    private InputArea txaJustificativa;
    private String justificativa;

    public PnlManutencaoAlta(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        Form form = new Form("form", model = new CompoundPropertyModel(new AtendimentoAlta()));

        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(AtendimentoAlta.PROP_CID));
        autoCompleteConsultaCid.add(new ConsultaListener<Cid>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cid cid) {
                enableConfirmar(target);
            }
        });

        autoCompleteConsultaCid.add(new RemoveListener<Cid>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid cid) {
                enableConfirmar(target);
            }
        });

        form.add(dchDataAlta = new DateChooserAjax(AtendimentoAlta.PROP_DATA_ALTA));
        dchDataAlta.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableConfirmar(target);
            }
        });

        form.add(dropDownMotivoAlta = DropDownUtil.getIEnumDropDown(AtendimentoAlta.PROP_MOTIVO_ALTA, AtendimentoAlta.MotivoAlta.values()));
        dropDownMotivoAlta.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableConfirmar(target);
            }
        });

        form.add(txaJustificativa = new InputArea("justificativa", new PropertyModel(this, "justificativa")));
        txaJustificativa.setOutputMarkupId(true);

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        add(form);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtendimentoAlta atendimentoAlta = model.getObject();

        if (atendimentoAlta.getDataAlta() != null && Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().compareTo(atendimentoAlta.getDataAlta()) < 0) {
            throw new ValidacaoException(BundleManager.getString("msgDataAltaMaiorDataAtual"));
        }

        BOFactoryWicket.getBO(HospitalFacade.class).manutencaoDadosAlta(atendimentoAlta, atendimentoAltaOriginal, justificativa);
    }

    public void carregarAtendimentoAlta(AjaxRequestTarget target, Long codigoAtendimentoAlta) {
        limpar(target);

        this.atendimentoAltaOriginal = LoadManager.getInstance(AtendimentoAlta.class)
                .addProperties(new HQLProperties(AtendimentoAlta.class).getProperties())
                .addProperty(VOUtils.montarPath(AtendimentoAlta.PROP_CID, Cid.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoAlta.PROP_CID, Cid.PROP_DESCRICAO))
                .setId(codigoAtendimentoAlta)
                .start().getVO();

        model.setObject((AtendimentoAlta) SerializationUtils.clone(atendimentoAltaOriginal));

        target.add(this);
    }

    private void enableConfirmar(AjaxRequestTarget target) {
        AtendimentoAlta atendimentoAlta = model.getObject();

        boolean enabled = !(atendimentoAltaOriginal.getCid().equals(atendimentoAlta.getCid())
                && atendimentoAlta.getDataAlta() != null
                && atendimentoAltaOriginal.getDataAlta().compareTo(atendimentoAlta.getDataAlta()) == 0
                && atendimentoAltaOriginal.getMotivoAlta().equals(atendimentoAlta.getMotivoAlta()));

        target.add(btnConfirmar.setEnabled(enabled));
    }

    private void limpar(AjaxRequestTarget target) {
        this.autoCompleteConsultaCid.limpar(target);
        this.dchDataAlta.limpar(target);
        this.dropDownMotivoAlta.limpar(target);
        txaJustificativa.limpar(target);
        target.add(btnConfirmar.setEnabled(false));
    }

    public InputField getTextFieldFocus() {
        return autoCompleteConsultaCid.getTxtDescricao().getTextField();
    }
}
