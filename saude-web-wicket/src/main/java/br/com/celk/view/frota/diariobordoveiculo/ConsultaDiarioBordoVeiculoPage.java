package br.com.celk.view.frota.diariobordoveiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete.AutoCompleteConsultaMotorista;
import br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete.AutoCompleteConsultaVeiculo;
import br.com.ksisolucoes.bo.frota.interfaces.dto.DiarioBordoVeiculoDTO;
import br.com.ksisolucoes.bo.frota.interfaces.dto.DiarioBordoVeiculoDTOParam;
import br.com.ksisolucoes.bo.frota.interfaces.facade.FrotaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.DiarioBordoVeiculo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaDiarioBordoVeiculoPage extends ConsultaPage<DiarioBordoVeiculoDTO, DiarioBordoVeiculoDTOParam> {

    private DiarioBordoVeiculoDTOParam param;
    private AutoCompleteConsultaVeiculo autoCompleteConsultaVeiculo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(param = new DiarioBordoVeiculoDTOParam()));

        DiarioBordoVeiculoDTOParam proxy = on(DiarioBordoVeiculoDTOParam.class);

        form.add(autoCompleteConsultaVeiculo = new AutoCompleteConsultaVeiculo(path(proxy.getVeiculo())));
        form.add(new AutoCompleteConsultaMotorista(path(proxy.getMotorista())));
        form.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DiarioBordoVeiculoDTO proxy = on(DiarioBordoVeiculoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("veiculo"), proxy.getDiarioBordoVeiculo().getVeiculo().getDescricao()));
        columns.add(new DateTimeColumn(bundle("dataSaida2"), path(proxy.getDiarioBordoVeiculo().getDataSaida()), path(proxy.getDiarioBordoVeiculo().getDataSaida())));
        columns.add(new DateTimeColumn(bundle("dataChegada2"), path(proxy.getDiarioBordoVeiculo().getDataChegada()), path(proxy.getDiarioBordoVeiculo().getDataChegada())));
        columns.add(createSortableColumn(bundle("motorista"), proxy.getDiarioBordoVeiculo().getMotorista().getNome()));
        columns.add(createSortableColumn(bundle("destino"), proxy.getDiarioBordoVeiculo().getDestino()));

        return columns;
    }

    private CustomColumn<DiarioBordoVeiculoDTO> getCustomColumn() {
        return new CustomColumn<DiarioBordoVeiculoDTO>() {
            @Override
            public Component getComponent(String componentId, final DiarioBordoVeiculoDTO rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setDataDTO(rowObject);
                        setResponsePage(new DiarioBordoVeiculoPage(rowObject, false, ConsultaDiarioBordoVeiculoPage.class));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject.getDiarioBordoVeiculo());
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setDataDTO(rowObject);
                        setResponsePage(new DiarioBordoVeiculoPage(rowObject, true, ConsultaDiarioBordoVeiculoPage.class));
                    }
                };
            }

            private void setDataDTO(DiarioBordoVeiculoDTO dto) {
                DiarioBordoVeiculo dbv = dto.getDiarioBordoVeiculo();

                dto.setDataSaida(dbv.getDataSaida());
                dto.setHoraSaida(dbv.getDataSaida());
                dto.setDataChegada(dbv.getDataChegada());
                dto.setHoraChegada(dbv.getDataChegada());
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<DiarioBordoVeiculoDTO, DiarioBordoVeiculoDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(FrotaFacade.class).consultarDiarioBordoVeiculos(dataPaging);
            }

            @Override
            public void customizeParam(DiarioBordoVeiculoDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                if (sortState.getSort() != null) {
                    param.setCampoOrdenacao(sortState.getSort().getProperty());
                    param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");
                }
            }
        };
    }

    @Override
    public DiarioBordoVeiculoDTOParam getParameters() {
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return RegistroDiarioBordoStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaDiarioBordo");
    }
}
