package br.com.celk.view.unidadesaude.atendimento.consulta.consultaatendimentos.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by sulivan on 09/06/17.
 */
public class DlgDetalhesAtendimentosCancelados extends Window {

    private PnlDetalhesAtendimentosCancelados pnlDetalhesAtendimentosCancelados;

    public DlgDetalhesAtendimentosCancelados(String id) {
        super(id);
        init();
    }

    private void init(){
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(290);

        setResizable(false);

        setTitle(BundleManager.getString("detalhesAtendimentoCancelado"));

        setContent(pnlDetalhesAtendimentosCancelados = new PnlDetalhesAtendimentosCancelados(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void setModelObject(AjaxRequestTarget target, Atendimento atendimento){
        show(target);
        pnlDetalhesAtendimentosCancelados.setModelObject(target, atendimento);
    }

}
