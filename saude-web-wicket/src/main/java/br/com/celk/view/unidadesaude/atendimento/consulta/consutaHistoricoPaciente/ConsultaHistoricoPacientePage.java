package br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaHistoricoPacientePage extends ConsultaPage<UsuarioCadsus, List<BuilderQueryCustom.QueryParameter>> {

    private Long codigo;
    private Date dataNascimento;
    private InputField txtPaciente;
    private String paciente;
    private String nomeMae;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtPaciente = new InputField<String>("paciente"));
        form.add(new InputField<Long>("codigo", new PropertyModel<Long>(this, "codigo")));
        form.add(new DateChooser("dataNascimento"));
        form.add(new InputField<String>("nomeMae"));

        getLinkNovo().setVisible(false);

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        UsuarioCadsus proxy = on(UsuarioCadsus.class);

        columns.add(getCustomActionColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo(), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("nome"), proxy.getNome(), proxy.getNomeSocial()));
        columns.add(createSortableColumn(bundle("telefone"), proxy.getCelularOuTelefonesFormatado(), proxy.getCelularOuTelefonesFormatado()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getSexo(), proxy.getSexoFormatado()));
        columns.add(new DateColumn(bundle("dataNascimento"), path(proxy.getDataNascimento()), path(proxy.getDataNascimento())));
        columns.add(createSortableColumn(bundle("nomeMae"), proxy.getNomeMae(), proxy.getNomeMae()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getDescricaoSituacao()));
        columns.add(createSortableColumn(bundle("motivoInatExcl"), proxy.getMotivoExclusao(), proxy.getDescricaoMotivoExclusao()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<UsuarioCadsus>() {
            @Override
            public void customizeColumn(final UsuarioCadsus rowObject) {
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhamentoHistoricoPaciente(rowObject));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return UsuarioCadsus.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(UsuarioCadsus.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(UsuarioCadsus.PROP_NOME), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        if (paciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_NOME, BuilderQueryCustom.QueryParameter.ILIKE, paciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, paciente))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_NOME_MAE, BuilderQueryCustom.QueryParameter.ILIKE, nomeMae));
        parameters.add(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_DATA_NASCIMENTO, dataNascimento));
        parameters.add(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_SITUACAO,BuilderQueryCustom.QueryParameter.IN,
                Arrays.asList(
                        UsuarioCadsus.SITUACAO_ATIVO,
                        UsuarioCadsus.SITUACAO_PROVISORIO,
                        UsuarioCadsus.SITUACAO_INATIVO)));

        parameters.add(new QueryCustom.QueryCustomParameter(
                new BuilderQueryCustom.QueryGroupAnd(
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_MOTIVO_EXCLUSAO, BuilderQueryCustom.QueryParameter.IN,
                                        Arrays.asList(
                                                UsuarioCadsus.MotivoExclusao.OBITO.value(),
                                                UsuarioCadsus.MotivoExclusao.MUDANCA_TERRITORIO.value(),
                                                UsuarioCadsus.MotivoExclusao.DUPLICADO.value())))),
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_MOTIVO_EXCLUSAO, BuilderQueryCustom.QueryParameter.IS_NULL))))));

        if (paciente == null || paciente.isEmpty()) {
        } else {
            // parameters.add(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_NOME, BuilderQueryCustom.QuerySorter.CRESCENTE, paciente));
        }

        return parameters;
    }


    @Override
    public Class getCadastroPage() {
        return ConsultaHistoricoPacientePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("historicoPaciente");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return this.txtPaciente;
    }
}
