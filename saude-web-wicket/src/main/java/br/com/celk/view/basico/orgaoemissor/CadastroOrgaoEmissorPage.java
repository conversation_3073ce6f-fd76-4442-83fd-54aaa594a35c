package br.com.celk.view.basico.orgaoemissor;

import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroOrgaoEmissorPage extends CadastroPage<OrgaoEmissor> {

    private RequiredUpperField txtDescricao;

    public CadastroOrgaoEmissorPage(OrgaoEmissor object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroOrgaoEmissorPage(OrgaoEmissor object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroOrgaoEmissorPage(OrgaoEmissor object) {
        this(object, false);
    }

    public CadastroOrgaoEmissorPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredUpperField(OrgaoEmissor.PROP_DESCRICAO));
        form.add(new UpperField(OrgaoEmissor.PROP_SIGLA));
        form.add(DropDownUtil.getNaoSimDropDown(OrgaoEmissor.PROP_FLAG_SAUDE));
        form.add(new InputField(OrgaoEmissor.PROP_CODIGO_CONSELHO_TISS));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<OrgaoEmissor> getReferenceClass() {
        return OrgaoEmissor.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaOrgaoEmissorPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroOrgaoEmissor");
    }

    @Override
    public Object salvar(OrgaoEmissor object) throws DAOException, ValidacaoException {
        return BOFactoryWicket.save(object);
    }

    @Override
    public String getMsgSalvo(Object returnObject) {
        return BundleManager.getString("registro_salvo_sucesso");
    }
}
