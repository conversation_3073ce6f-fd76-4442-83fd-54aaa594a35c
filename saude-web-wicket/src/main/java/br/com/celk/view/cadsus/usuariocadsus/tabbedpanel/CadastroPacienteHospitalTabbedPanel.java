package br.com.celk.view.cadsus.usuariocadsus.tabbedpanel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.CadastroUsuarioCadsusHospitalDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroPacienteHospitalTabbedPanel extends CadastroTabbedPanel<CadastroUsuarioCadsusHospitalDTO> {

    public CadastroPacienteHospitalTabbedPanel(String id, CadastroUsuarioCadsusHospitalDTO object, boolean viewOnly, List<ITab> tabs) {
        super(id, object, viewOnly, tabs, true);
    }

    public CadastroPacienteHospitalTabbedPanel(String id, CadastroUsuarioCadsusHospitalDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public CadastroPacienteHospitalTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }
    
    @Override
    public Class<CadastroUsuarioCadsusHospitalDTO> getReferenceClass() {
        return CadastroUsuarioCadsusHospitalDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return object.getClasseVoltar();
    }
    
    @Override
    public Object salvar(CadastroUsuarioCadsusHospitalDTO object) throws DAOException, ValidacaoException {
        UsuarioCadsusEnderecoDTO dto = new UsuarioCadsusEnderecoDTO();

        object.getEndereco().getId().getEndereco().setCep(StringUtilKsi.getDigits(object.getEndereco().getId().getEndereco().getCep()));
        object.getEndereco().getId().setUsuarioCadsus(object.getUsuarioCadsus());
        dto.setUsuarioCadsusEndereco(object.getEndereco());
        String cnsDigits = StringUtils.trimToNull(StringUtilKsi.getDigits(object.getNumeroCartao()));
        if (cnsDigits != null) {
            UsuarioCadsusCns usuarioCadsusCns = new UsuarioCadsusCns();
            usuarioCadsusCns.setNumeroCartao(new Long(cnsDigits));
            usuarioCadsusCns.setUsuarioCadsus(object.getUsuarioCadsus());
            dto.setCartoes(Arrays.asList(usuarioCadsusCns));
        }

        String cpfDigits = StringUtils.trimToNull(StringUtilKsi.getDigits(object.getUsuarioCadsus().getCpf()));
        object.getUsuarioCadsus().setCpf(cpfDigits);
        ArrayList<UsuarioCadsusDocumento> listaDocs = new ArrayList<UsuarioCadsusDocumento>();
        if (cpfDigits != null) {
            object.getDocumentoCpf().setUsuarioCadsus(object.getUsuarioCadsus());
            object.getDocumentoCpf().setNumeroDocumento(cpfDigits);
            object.getDocumentoCpf().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF));
            listaDocs.add(object.getDocumentoCpf());
        }

        object.getUsuarioCadsus().setRg(null);
        if ((StringUtils.trimToNull(object.getDocumentoIdentidade().getNumeroDocumento()) != null
                || StringUtils.trimToNull(object.getDocumentoIdentidade().getSiglaUf()) != null
                || object.getDocumentoIdentidade().getDataEmissao() != null
                || object.getDocumentoIdentidade().getOrgaoEmissor() != null)) {

            object.getDocumentoIdentidade().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG));
            object.getDocumentoIdentidade().setUsuarioCadsus(object.getUsuarioCadsus());
            object.getUsuarioCadsus().setRg(object.getDocumentoIdentidade().getNumeroDocumento());
            listaDocs.add(object.getDocumentoIdentidade());
        }

        if ((StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroFolha()) != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroLivro()) != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroCartorio()) != null
                || object.getDocumentoCertidao().getDataEmissao() != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroTermo()) != null)) {

            object.getDocumentoCertidao().setUsuarioCadsus(object.getUsuarioCadsus());
            listaDocs.add(object.getDocumentoCertidao());
        }

        dto.setDocumentos(listaDocs);

        List<Long> tipoDocumentosList = new ArrayList<Long>();
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO);

        dto.setTipoDocumentos(tipoDocumentosList);

        if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsus().getFlagNaoPossuiCns())) {
            validarCns(object.getUsuarioCadsus(), object.getNumeroCartao());
        }
        
        if(CollectionUtils.isNotNullEmpty(object.getAcompanhanteDTOList())){
            dto.setAcompanhanteUsuarioCadsusHospitalDTOList(object.getAcompanhanteDTOList());
        }
        if(CollectionUtils.isNotNullEmpty(object.getAcompanhanteRemovidosDTOList())){
            dto.setAcompanhanteUsuarioCadsusHospitalRemovidosDTOList(object.getAcompanhanteRemovidosDTOList());
        }

        return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).cadastrarUsuarioCadsusHospital(dto);
    }
    
    private void validarCns(UsuarioCadsus usuarioCadsus, String cns) throws ValidacaoException, DAOException {
        if (cns != null) {
            cns = StringUtilKsi.getDigits(cns);
            if (CnsValidator.validaCns(cns)) {
                UsuarioCadsusCns usuarioCadsusCnsValidacao = LoadManager.getInstance(UsuarioCadsusCns.class)
                        .addProperties(new HQLProperties(UsuarioCadsusCns.class).getProperties())
                        .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO))
                        .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                        .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                        .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, usuarioCadsus.getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), Coalesce.asLong(cns)))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                        .start().getVO();

                if (usuarioCadsusCnsValidacao != null) {
                    throw new ValidacaoException(BundleManager.getString("ja_existe_paciente_com_esse_cns") + ": " + usuarioCadsusCnsValidacao.getUsuarioCadsus().getDescricaoSocialFormatado());
                }
            } else {
                if (StringUtils.trimToNull(cns) != null) {
                    throw new ValidacaoException(BundleManager.getString("cns_invalido"));
                } else {
                    throw new ValidacaoException(BundleManager.getString("cns_obrigatorio"));
                }
            }
        }
    }
}
