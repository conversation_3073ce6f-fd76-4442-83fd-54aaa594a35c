package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.dialog.DlgCadastroResponsavelTecnico;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.externo.view.estabelecimento.CadastroEstabelecimentoExternoPage;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete.AutoCompleteConsultaTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class RequerimentoAnaliseProjetosExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoAnaliseProjetosDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoAnaliseProjeto requerimentoAnaliseProjeto;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaTipoProjetoVigilancia autoCompleteConsultaTipoProjetoVigilancia;
    private DisabledInputField<String> txtCnpjCpfFormatado;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtDescricaoEndereco;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;

    private boolean enabled;
    private Class classReturn;
    private WebMarkupContainer containerTipoProjeto;
    private Label lbValorTotal;
    private String valorTotal;
    private RequiredDoubleField txtArea;
    private ConfiguracaoVigilancia configuracaoVigilancia = null;

    private WebMarkupContainer containerDadosObra;
    private PnlVigilanciaEndereco pnlVigilanciaEndereco;

    private WebMarkupContainer containerDados;
    private WebMarkupContainer containerPessoa;
    private WebMarkupContainer containerEstabelecimento;
    private DropDown dropDownTipoPessoa;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private DisabledInputField<String> txtNomeFantasia;
    private DisabledInputField<String> txtCpfFormatado;
    private InputField<String> txtEmail;

    private RequiredInputField txtNumeroObra;

    private ResponsavelTecnico responsavelTecnico;
    private String numeroInscricaoImobiliaria;
    private InputField<String> txtInscricaoImobiliaria;

    private Table tblResponsavelTecnico;
    private Table tblInscricaoImobiliaria;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavelTecnico;
    private AbstractAjaxButton btnAdicionar;
    private AbstractAjaxButton btnAdicionarInscricao;
    private AbstractAjaxLink btnCadadastroResponsavel;
    private DlgCadastroResponsavelTecnico dlgCadastroResponsavelTecnico;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private Estabelecimento estabelecimento;
    private Label lbl;
    private String msg;

    public RequerimentoAnaliseProjetosExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoAnaliseProjetosExternoPage(TipoSolicitacao tipoSolicitacao, Estabelecimento estabelecimento, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.estabelecimento = estabelecimento;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoAnaliseProjetosExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoAnaliseProjetos(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }
        if (configuracaoVigilancia == null) {
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Loggable.vigilancia.error(e.getMessage(), e);
            }
        }

        RequerimentoAnaliseProjetosDTO proxy = on(RequerimentoAnaliseProjetosDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoAnaliseProjetosDTO()));
        if (requerimentoAnaliseProjeto != null) {
            form.getModel().getObject().setRequerimentoAnaliseProjeto(requerimentoAnaliseProjeto);
        } else {
            form.getModel().getObject().setRequerimentoAnaliseProjeto(new RequerimentoAnaliseProjeto());
            form.getModel().getObject().getRequerimentoAnaliseProjeto().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }

        lbl = new Label("msg", msg);
        lbl.setVisible(false);
        lbl.setOutputMarkupPlaceholderTag(true);
        form.add(lbl);

        containerDadosObra = new WebMarkupContainer("containerDadosObra");
        containerDadosObra.setOutputMarkupId(true);
        containerDadosObra.setEnabled(enabled);
        form.add(containerDadosObra);

        containerDadosObra.add(tblResponsavelTecnico = new Table("tblResponsavelTecnico", getColumns(), getCollectionProvider()));
        tblResponsavelTecnico.setScrollY("180px");
        tblResponsavelTecnico.populate();
        tblResponsavelTecnico.setOutputMarkupPlaceholderTag(true);
        tblResponsavelTecnico.setOutputMarkupId(true);
        tblResponsavelTecnico.getAjaxRegionMarkupId();

        containerDadosObra.add(tblInscricaoImobiliaria = new Table("tblInscricaoImobiliaria", getColumnsInsc(), getCollectionProviderInsc()));
        tblInscricaoImobiliaria.setScrollY("180px");
        tblInscricaoImobiliaria.populate();
        tblInscricaoImobiliaria.setOutputMarkupPlaceholderTag(true);
        tblInscricaoImobiliaria.setOutputMarkupId(true);
        tblInscricaoImobiliaria.getAjaxRegionMarkupId();

        containerDadosObra.add(btnAdicionar = new AbstractAjaxButton("btnAdicionarResponsavelTecnico") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        btnAdicionar.setDefaultFormProcessing(false);

        containerDadosObra.add(btnAdicionarInscricao = new AbstractAjaxButton("btnAdicionarInscricao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarInscricaoImob(target);
            }
        });

        btnAdicionarInscricao.setDefaultFormProcessing(false);

        containerDadosObra.add(autoCompleteConsultaResponsavelTecnico = (AutoCompleteConsultaResponsavelTecnico)
                new AutoCompleteConsultaResponsavelTecnico("responsavelTecnico", new PropertyModel(this, "responsavelTecnico")).setEnabled(enabled));
        autoCompleteConsultaResponsavelTecnico.setOutputMarkupId(true);
        autoCompleteConsultaResponsavelTecnico.setLabel(new Model(Bundle.getStringApplication("responsavelTecnico")));
        containerDadosObra.add(btnCadadastroResponsavel = new AbstractAjaxLink("btnCadadastroResponsavel") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarResponsavel(target);
            }
        });

        form.add(containerDados = new WebMarkupContainer("containerDados"));
        containerDados.setEnabled(form.getModel().getObject().getRequerimentoAnaliseProjeto().getCodigo() == null);
        containerDados.setOutputMarkupPlaceholderTag(true);
        containerDados.getAjaxRegionMarkupId();
        containerDados.add(getDropDownTipoPessoa(proxy));

        containerDados.add(getContainerPessoa(proxy));

        containerDados.add(getContainerEstabelecimento(proxy));

        containerDados.add(new DisabledInputField<String>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getProtocoloFormatado())));

        form.add(new DisabledInputField<String>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getProtocoloFormatado())));

        containerTipoProjeto = new WebMarkupContainer("containerTipoProjeto");
        containerTipoProjeto.setOutputMarkupId(true);
        containerTipoProjeto.setEnabled(enabled);
        containerTipoProjeto.add(autoCompleteConsultaTipoProjetoVigilancia = new AutoCompleteConsultaTipoProjetoVigilancia(path(proxy.getRequerimentoAnaliseProjeto().getTipoProjetoVigilancia()), true));
        autoCompleteConsultaTipoProjetoVigilancia.setLabel(Model.of(bundle("tipoProjeto")));
        autoCompleteConsultaTipoProjetoVigilancia.setTipo(TipoProjetoVigilancia.Tipo.PBA.value());
        autoCompleteConsultaTipoProjetoVigilancia.setEnabled(enabled);

        autoCompleteConsultaTipoProjetoVigilancia.add(new ConsultaListener<TipoProjetoVigilancia>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoProjetoVigilancia object) {
                habilitarArea(target, object);
            }
        });
        autoCompleteConsultaTipoProjetoVigilancia.add(new RemoveListener<TipoProjetoVigilancia>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProjetoVigilancia object) {
                habilitarArea(target, null);
            }
        });

        containerTipoProjeto.add(txtArea = new RequiredDoubleField(path(proxy.getRequerimentoAnaliseProjeto().getArea())));
        txtArea.setMDec(4).addAjaxUpdateValue();
        txtArea.setEnabled(false);
        txtArea.setLabel(Model.of(bundle("areaM2")));

        txtArea.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularValorTaxa(target);
            }
        });
        if (form.getModel().getObject().getRequerimentoAnaliseProjeto().getCodigo() != null) {
            calcularValorTaxa(null);
        }
        containerTipoProjeto.add(lbValorTotal = new Label("valorTotal", new PropertyModel<String>(this, "valorTotal")));
        lbValorTotal.setOutputMarkupId(true);

        form.add(containerTipoProjeto);
        if (form.getModel().getObject().getRequerimentoAnaliseProjeto().getCodigo() != null) {
            containerTipoProjeto.setEnabled(CollectionUtils.isEmpty(VigilanciaHelper.getVigilanciaFinanceiroList(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia())));
        }

        containerDadosObra.add(pnlVigilanciaEndereco = (PnlVigilanciaEndereco) new PnlVigilanciaEndereco(path(proxy.getRequerimentoAnaliseProjeto().getVigilanciaEndereco())).setLabel(Model.of(bundle("endereco"))));
        pnlVigilanciaEndereco.setEnabled(enabled);
        pnlVigilanciaEndereco.setLabel(Model.of(bundle("endereco")));
        pnlVigilanciaEndereco.addRequired();
        pnlVigilanciaEndereco.setRequired(true);

        containerDadosObra.add(txtNumeroObra = new RequiredInputField(path(proxy.getRequerimentoAnaliseProjeto().getNumeroObra())));
        txtNumeroObra.setLabel(Model.of(bundle("numeroAbv")));
        txtNumeroObra.setEnabled(enabled);
        containerDadosObra.add(new InputField(path(proxy.getRequerimentoAnaliseProjeto().getQuadraObra())).setEnabled(enabled));
        containerDadosObra.add(new InputField(path(proxy.getRequerimentoAnaliseProjeto().getNumeroObraAoLado())).setEnabled(enabled));
        containerDadosObra.add(new InputField(path(proxy.getRequerimentoAnaliseProjeto().getLoteObra())).setEnabled(enabled));
        containerDadosObra.add(new InputField(path(proxy.getRequerimentoAnaliseProjeto().getComplementoObra())).setEnabled(enabled));
        containerDadosObra.add(new InputField(path(proxy.getRequerimentoAnaliseProjeto().getNumeroLoteamentoObra())).setEnabled(enabled));
        containerDadosObra.add(txtInscricaoImobiliaria = new InputField("numeroInscricaoImobiliaria", new PropertyModel(this, "numeroInscricaoImobiliaria")));
        txtInscricaoImobiliaria.setLabel(Model.of(bundle("nInscricaoImobiliaria")));
        txtInscricaoImobiliaria.setEnabled(enabled);
        txtInscricaoImobiliaria.addAjaxUpdateValue();

        containerDadosObra.setVisible(RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagVisualizarDadosObra()));

        form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia(), enabled));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        form.add(new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                carregarCpfCnpjEstabelecimentoPrincipal(object);
                carregarEnderecoVigilancia(object);
                atualizarDadosEstabelecimento(target);
                verificarEstabelecimentoIsento(target, object);
            }
        });
        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosEstabelecimento(target);
                form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaEndereco(null);
                autoCompleteConsultaEstabelecimento.limpar(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
                target.add(autoCompleteConsultaTipoProjetoVigilancia);
                lbl.setVisible(false);
                target.add(lbl);
            }
        });

        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(form);
        habilitarArea(null, form.getModel().getObject().getRequerimentoAnaliseProjeto().getTipoProjetoVigilancia());
        if (estabelecimento != null && estabelecimento.getCodigo() != null) {
            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);

            carregarCpfCnpjEstabelecimentoPrincipal(estabelecimento);
            carregarEnderecoVigilancia(estabelecimento);
            atualizarDadosEstabelecimento(null);
        }
        enableCamposRequerente(null, false);

        if (requerimentoVigilancia != null) {
            carregarEloRequerimentoVigilanciaResponsavelTecnico(requerimentoVigilancia);
            carregarInscricoesImob(requerimentoVigilancia);
        }
    }

    private void cadastrarResponsavel(AjaxRequestTarget target) {
        if (dlgCadastroResponsavelTecnico == null) {
            addModal(target, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(newModalId()));
            dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, ResponsavelTecnico responsavelTecnico) throws ValidacaoException, DAOException {
                    autoCompleteConsultaResponsavelTecnico.limpar(target);
                    autoCompleteConsultaResponsavelTecnico.setComponentValue(responsavelTecnico);
                    target.add(autoCompleteConsultaResponsavelTecnico);
                }
            });
        }

        dlgCadastroResponsavelTecnico.limpar(target);
        dlgCadastroResponsavelTecnico.show(target);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (responsavelTecnico == null) {
            throw new ValidacaoException(BundleManager.getString("informeResponsavelTecnico"));
        }

        if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList())) {
            for (EloRequerimentoVigilanciaResponsavelTecnico elo : form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList()) {
                if (elo.getResponsavelTecnico().getCodigo().equals(responsavelTecnico.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("responsavelTecnicoJaAdicionado"));
                }
            }
        }

        EloRequerimentoVigilanciaResponsavelTecnico elo = new EloRequerimentoVigilanciaResponsavelTecnico();
        elo.setResponsavelTecnico(responsavelTecnico);
        form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().add(elo);

        tblResponsavelTecnico.update(target);
        tblResponsavelTecnico.populate();
        responsavelTecnico = null;
        autoCompleteConsultaResponsavelTecnico.limpar(target);
    }

    private void adicionarInscricaoImob(AjaxRequestTarget target) throws ValidacaoException {
        if (txtInscricaoImobiliaria.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("informeNumeroInscricaoImobiliaria"));
        }

        if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList())) {
            for (RequerimentoVigilanciaInscricaoImob insc : form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList()) {
                if (insc.getNumeroInscricaoImobiliaria().equals(txtInscricaoImobiliaria.getComponentValue())) {
                    throw new ValidacaoException(BundleManager.getString("inscricaoImobiliariaJaAdicionada"));
                }
            }
        }

        RequerimentoVigilanciaInscricaoImob inscricao = new RequerimentoVigilanciaInscricaoImob();
        inscricao.setNumeroInscricaoImobiliaria(txtInscricaoImobiliaria.getComponentValue());
        form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().add(inscricao);

        tblInscricaoImobiliaria.update(target);
        tblInscricaoImobiliaria.populate();
        txtInscricaoImobiliaria.limpar(target);
    }

    private List<IColumn> getColumns() {
        ColumnFactory columnFactory = new ColumnFactory(EloRequerimentoVigilanciaResponsavelTecnico.class);
        List<IColumn> columns = new ArrayList<>();
        EloRequerimentoVigilanciaResponsavelTecnico proxy = on(EloRequerimentoVigilanciaResponsavelTecnico.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("nome"), path(proxy.getResponsavelTecnico().getNome())));
        columns.add(columnFactory.createColumn(BundleManager.getString("cpf"), path(proxy.getResponsavelTecnico().getCpfFormatado())));
        columns.add(columnFactory.createColumn(BundleManager.getString("registro"), path(proxy.getResponsavelTecnico().getDescricaoRegistro())));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModelObject().getEloRequerimentoVigilanciaResponsavelTecnicoList();
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EloRequerimentoVigilanciaResponsavelTecnico>() {
            @Override
            public void customizeColumn(final EloRequerimentoVigilanciaResponsavelTecnico rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnsInsc() {
        ColumnFactory columnFactory = new ColumnFactory(RequerimentoVigilanciaInscricaoImob.class);
        List<IColumn> columns = new ArrayList<>();
        RequerimentoVigilanciaInscricaoImob proxy = on(RequerimentoVigilanciaInscricaoImob.class);

        columns.add(getCustomColumnInsc());
        columns.add(columnFactory.createColumn(BundleManager.getString("inscricaoImobiliaria"), path(proxy.getNumeroInscricaoImobiliaria())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderInsc() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModelObject().getRequerimentoVigilanciaInscricaoImobList();
            }
        };
    }

    private IColumn getCustomColumnInsc() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaInscricaoImob>() {
            @Override
            public void customizeColumn(final RequerimentoVigilanciaInscricaoImob rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerInscricaoImob(target, rowObject);
                    }
                });
            }
        };
    }

    private void remover(AjaxRequestTarget target, EloRequerimentoVigilanciaResponsavelTecnico rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().size(); i++) {
            EloRequerimentoVigilanciaResponsavelTecnico elo = form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().get(i);
            if (elo == rowObject) {
                if (elo.getCodigo() != null) {
                    form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoExcluirList().add(elo);
                }
                form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().remove(i);
            }
        }
        tblResponsavelTecnico.populate();
        tblResponsavelTecnico.update(target);

        CrudUtils.removerItem(target, tblResponsavelTecnico, form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList(), rowObject);
    }

    private void removerInscricaoImob(AjaxRequestTarget target, RequerimentoVigilanciaInscricaoImob rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().size(); i++) {
            RequerimentoVigilanciaInscricaoImob insc = form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().get(i);
            if (insc == rowObject) {
                if (insc.getCodigo() != null) {
                    form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobExcluirList().add(insc);
                }
                form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().remove(i);
            }
        }
        tblInscricaoImobiliaria.populate();
        tblInscricaoImobiliaria.update(target);

        CrudUtils.removerItem(target, tblInscricaoImobiliaria, form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList(), rowObject);
    }

    private WebMarkupContainer getContainerEstabelecimento(RequerimentoAnaliseProjetosDTO proxy) {
        containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
        containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.getAjaxRegionMarkupId();
        containerEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setLabel(Model.of(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        try {
            autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        autoCompleteConsultaEstabelecimento.setEnabled(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getCodigo() == null);
        autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.add(txtFantasia = new DisabledInputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        containerEstabelecimento.add(txtCnpjCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado())));
        containerEstabelecimento.add(txtDescricaoEndereco = new DisabledInputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaEndereco().getEnderecoFormatadoComCidade())));

        containerEstabelecimento.add(new AbstractAjaxLink("btnCadEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoAnaliseProjetosExternoPage.this.setResponsePage(new CadastroEstabelecimentoExternoPage(tipoSolicitacao, true));
            }
        });

        return containerEstabelecimento;
    }

    private WebMarkupContainer getContainerPessoa(RequerimentoAnaliseProjetosDTO proxy) {
        containerPessoa = new WebMarkupContainer("containerPessoa");
        containerPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa())));
        containerPessoa.setOutputMarkupPlaceholderTag(true);
        containerPessoa.getAjaxRegionMarkupId();
        containerPessoa.add(txtNomeFantasia = new DisabledInputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa().getNomeFantasia())));
        containerPessoa.add(txtCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa().getCpfFormatado())));
        containerPessoa.add(txtEmail = new InputField<>(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail())));
        txtEmail.setEnabled(false);
        txtEmail.setLabel(new Model(bundle("email")));

        autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        try {
            autoCompleteConsultaVigilanciaPessoa.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoPessoaRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarPessoa(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarPessoa(target, null);
            }
        });

        containerPessoa.add(new AbstractAjaxLink("btnCadPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId(), true) {
                    @Override
                    public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                        autoCompleteConsultaVigilanciaPessoa.limpar(target);
                        autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);

                        target.add(txtCpfFormatado);
                        target.add(txtNomeFantasia);
                        target.add(txtEmail);

                        target.focusComponent(autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().getTextField());
                    }
                });
                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        });

        return containerPessoa;
    }

    private void atualizarPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        if (vigilanciaPessoa != null) {
            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
            if (vigilanciaPessoa.getEmail() == null) {
                txtEmail.setEnabled(true);
                txtEmail.setRequired(true);
                txtEmail.addRequiredClass();
            }
        } else {
            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaPessoa(null);
            txtEmail.setRequired(false);
            txtEmail.setEnabled(false);
            txtEmail.removeRequiredClass();
        }
        target.add(txtCpfFormatado);
        target.add(txtNomeFantasia);
        target.add(txtEmail);
    }

    private DropDown getDropDownTipoPessoa(RequerimentoAnaliseProjetosDTO proxy) {
        dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente()), RequerimentoVigilancia.TipoRequerente.values(), false, true);
        dropDownTipoPessoa.addAjaxUpdateValue();
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCamposRequerente(target, true);
            }
        });
        dropDownTipoPessoa.setEnabled(enabled);
        return dropDownTipoPessoa;
    }

    private void enableCamposRequerente(AjaxRequestTarget target, boolean limparCampos) {
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente())) {
            form.getModelObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaPessoa(null);
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            containerEstabelecimento.setVisible(true);
            containerPessoa.setVisible(false);
        } else {
            form.getModelObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setEstabelecimento(null);
            form.getModelObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaEndereco(null);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(true);
            containerEstabelecimento.setVisible(false);
            containerPessoa.setVisible(true);
        }

        if (target != null) {
            if (limparCampos) {
                autoCompleteConsultaEstabelecimento.limpar(target);
                autoCompleteConsultaVigilanciaPessoa.limpar(target);
            }
            target.add(containerEstabelecimento);
            target.add(containerPessoa);
            target.add(containerDados);
            target.add(autoCompleteConsultaEstabelecimento);
            target.add(autoCompleteConsultaVigilanciaPessoa);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void calcularValorTaxa(AjaxRequestTarget target) {
        RequerimentoAnaliseProjetosDTO object = form.getModel().getObject();
        try {
            carregarConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
            }
            StringBuilder sb = new StringBuilder();
            BigDecimal totalTaxaUfm = new BigDecimal(0);
            BigDecimal totalTaxa = new BigDecimal(0);
            TipoProjetoVigilancia tipoProjetoVigilancia = object.getRequerimentoAnaliseProjeto().getTipoProjetoVigilancia();
            if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(object.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente())
                    && object.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento() != null
                    && VigilanciaHelper.isIsentoPorLei(object.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento())) {
                totalTaxa = new BigDecimal(0);
            } else {
                TaxaIndice taxaIndice = carregarTaxaVigente(tipoProjetoVigilancia.getTaxa());
                totalTaxaUfm = new BigDecimal(tipoProjetoVigilancia.getValor());

                Double area = object.getRequerimentoAnaliseProjeto().getArea();
                if (Coalesce.asDouble(area) > tipoProjetoVigilancia.getMetragemMaxima()) {
                    if (Coalesce.asDouble(configuracaoVigilancia.getValorExcedidoAnaliseProjeto()) == 0D) {
                        throw new ValidacaoException(BundleManager.getString("msgAreaInformadaExcedeMetragemMaximaTipoProjeto"));
                    } else {
                        BigDecimal areaExcedida = new Dinheiro(Coalesce.asDouble(area), 4)
                                .subtrair(tipoProjetoVigilancia.getMetragemMaxima(), 4).bigDecimalValue();
                        BigDecimal valorTotalExcedido = new Dinheiro(Coalesce.asBigDecimal(areaExcedida).doubleValue(), 4)
                                .multiplicar(configuracaoVigilancia.getValorExcedidoAnaliseProjeto(), 4).bigDecimalValue();

                        totalTaxaUfm = new Dinheiro(totalTaxaUfm).somar(valorTotalExcedido.doubleValue(), 4).bigDecimalValue();
                    }
                }

                if (TipoProjetoVigilancia.TipoCobranca.POR_M2.value().equals(tipoProjetoVigilancia.getTipoCobranca())) {
                    totalTaxaUfm = new Dinheiro(Coalesce.asBigDecimal(totalTaxaUfm).doubleValue(), 4)
                            .multiplicar(Coalesce.asDouble(area), 4)
                            .bigDecimalValue();
                }

                totalTaxa = new Dinheiro(Coalesce.asBigDecimal(totalTaxaUfm).doubleValue(), 4)
                        .multiplicar(taxaIndice == null ? 0 : taxaIndice.getValorIndice())
                        .bigDecimalValue();
            }
            NumberFormat nf = NumberFormat.getCurrencyInstance();
            sb.append(new Dinheiro(Coalesce.asBigDecimal(totalTaxaUfm), 4).toString());
            sb.append(" ").append(tipoProjetoVigilancia.getTaxa().getDescricao());
            sb.append(" - ");
            sb.append(nf.format(totalTaxa));
            valorTotal = sb.toString();
            if (target != null) {
                target.add(lbValorTotal);
            }
        } catch (DAOException | ValidacaoException ex) {
            if (target != null) {
                modalWarn(target, ex);
                limparCamposArea(target);
            }
        }
    }

    private void limparCamposArea(AjaxRequestTarget target) {
        if (target != null) {
            txtArea.limpar(target);
            NumberFormat nf = NumberFormat.getCurrencyInstance();
            valorTotal = nf.format(new BigDecimal(0));
            target.add(lbValorTotal);
        }
    }

    private TaxaIndice carregarTaxaVigente(Taxa taxa) {
        try {
            return FinanceiroVigilanciaHelper.getTaxaIndiceVigente(taxa);
        } catch (Exception e) {
            Loggable.log.info(e.getMessage());
        }
        return null;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        RequerimentoAnaliseProjetosDTO dto = form.getModel().getObject();
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente()) && dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabelecimento"));
        }

        if (RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente())) {
            if (dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa() == null) {
                throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarPessoa"));
            }
            if (txtEmail.isEnabled()) {
                if (!EmailValidator.validarEmail(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail())) {
                    throw new ValidacaoException(bundle("email_invalido"));
                }
                VigilanciaPessoa vigilanciaPessoa = BOFactoryWicket.save(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa());
                if (vigilanciaPessoa != null) {
                    form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
                }
            }
        }
        if (!EmailValidator.validarEmail(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEmailSolicitante())) {
            throw new ValidacaoException(bundle("email_invalido"));
        }

        if (RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente()) && dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaPessoa() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarPessoa"));
        }

        if (form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }

        if (configuracaoVigilancia != null && RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagVisualizarDadosObra())){
            if (CollectionUtils.isEmpty(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList())) {
                throw new ValidacaoException(bundle("msgInformeAoMenosUmResponsavelTecnico"));
            }

            if (CollectionUtils.isEmpty(form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList())) {
                throw new ValidacaoException(bundle("msgInformeAoMenosUmaInscricaoImobiliaria"));
            }
        }

        dto.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        dto.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoAnaliseProjetos(form.getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        gerarBoleto(target, rv, configuracaoVigilancia);

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);
                param.setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList<>();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void gerarBoleto(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia, ConfiguracaoVigilancia configuracaoVigilancia) throws ValidacaoException, DAOException {
        String boletoBase64RequerimentoExterno = FinanceiroVigilanciaHelper.getBoletoBase64RequerimentoExterno(requerimentoVigilancia, configuracaoVigilancia);

        if (boletoBase64RequerimentoExterno != null) {
            ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64RequerimentoExterno);
        }
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target) {
        if (target != null) {
            target.add(txtCnpjCpfFormatado);
            target.add(txtFantasia);
            target.add(txtDescricaoEndereco);
        }
    }

    private void carregarRequerimentoAnaliseProjetos(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoAnaliseProjeto = LoadManager.getInstance(RequerimentoAnaliseProjeto.class)
                    .addProperties(new HQLProperties(RequerimentoAnaliseProjeto.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(TipoProjetoVigilancia.class, RequerimentoAnaliseProjeto.PROP_TIPO_PROJETO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(Taxa.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_TIPO_PROJETO_VIGILANCIA, TipoProjetoVigilancia.PROP_TAXA)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoAnaliseProjeto.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoAnaliseProjeto.setRequerimentoVigilancia(requerimentoVigilancia);
            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void carregarEloRequerimentoVigilanciaResponsavelTecnico(RequerimentoVigilancia requerimentoVigilancia) {
        List<EloRequerimentoVigilanciaResponsavelTecnico> elos = LoadManager.getInstance(EloRequerimentoVigilanciaResponsavelTecnico.class)
                .addProperties(new HQLProperties(EloRequerimentoVigilanciaResponsavelTecnico.class).getProperties())
                .addProperty(VOUtils.montarPath(EloRequerimentoVigilanciaResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_CPF))
                .addParameter(new QueryCustom.QueryCustomParameter(EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(elos)) {
            form.getModelObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().addAll(elos);
        }
    }

    private void carregarInscricoesImob(RequerimentoVigilancia requerimentoVigilancia) {
        List<RequerimentoVigilanciaInscricaoImob> inscricaoImobList = LoadManager.getInstance(RequerimentoVigilanciaInscricaoImob.class)
                .addProperties(new HQLProperties(RequerimentoVigilanciaInscricaoImob.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(inscricaoImobList)) {
            form.getModelObject().getRequerimentoVigilanciaInscricaoImobList().addAll(inscricaoImobList);
        }
    }

    private void carregarCpfCnpjEstabelecimentoPrincipal(Estabelecimento estabelecimento) {
        if (form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null
                || form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty()) {
            Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                    .start().getVO();

            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getEstabelecimento().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
        }
    }

    private void carregarEnderecoVigilancia(Estabelecimento estabelecimento) {
        if (form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getVigilanciaEndereco() == null) {
            estabelecimento = LoadManager.getInstance(Estabelecimento.class)
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getCodigo()))
                    .start().getVO();

            form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().setVigilanciaEndereco(estabelecimento.getVigilanciaEndereco());
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("analiseProjetoBasicoArquitetura");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoAnaliseProjeto().getRequerimentoVigilancia().getTipoRequerente())) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
        } else {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaVigilanciaPessoa.getTxtDescricao().getTextField())));
        }
    }

    private void habilitarArea(AjaxRequestTarget target, TipoProjetoVigilancia tipoProjetoVigilancia) {
        if (enabled) {
            if (tipoProjetoVigilancia != null) {
                txtArea.setEnabled(true);
            } else {
                txtArea.setEnabled(false);
            }

            limparCamposArea(target);
        }
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    @Override
    public Permissions getAction() {
        return Permissions.PROJETO_BASICO_ARQUITETURA;
    }

    private void verificarEstabelecimentoIsento(AjaxRequestTarget target, Estabelecimento object) {
        if (Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getTipoEmpresa())) {
            if (VigilanciaHelper.isIsentoPorLei(object)) {
                lbl.setDefaultModel(Model.of("Esta empresa está configurada como “Isenta por lei” de taxas, portanto será necessário anexar o(s) documento(s) comprobatório(s) desta isenção."));
                lbl.setVisible(true);
                target.add(lbl);
            }
        }
    }
}
