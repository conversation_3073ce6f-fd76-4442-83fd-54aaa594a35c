package br.com.celk.view.hospital.faturamento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBox;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.component.window.WindowUtil;
import static br.com.celk.component.window.WindowUtil.addModal;
import br.com.celk.resources.Icon16;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.hospital.faturamento.dialogs.DlgLancamentosConfirmadosMateriaisMedicamentos;
import br.com.celk.view.hospital.faturamento.dialogs.DlgNovoLancamentoMaterialMedicamento;
import br.com.celk.view.hospital.faturamento.dialogs.DlgTransferirItemContaPaciente;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.TransferenciaItemContaPacienteDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 * <AUTHOR>
 */
public class MateriaisMedicamentosTab extends TabPanel<FechamentoContaDTO> {

    private MultiSelectionTable<ItemContaPaciente> tabela;
    private DlgNovoLancamentoMaterialMedicamento dlgNovoLancamento;
    private DlgLancamentosConfirmadosMateriaisMedicamentos dlgLancamentosConfirmados;
    private AbstractAjaxButton btnLancamentosConfirmados;
    private FechamentoContaDTO fechamentoContaDTO;
    private List<ItemContaPaciente> medicamentosList;
    private List<ItemContaPaciente> medicamentosConfirmadosList;
    private boolean btnConfirmadoOK;
    private DlgTransferirItemContaPaciente dlgTransferirItemContaPaciente;
    private DlgConfirmacao dlgConfirmacao;
    private DlgConfirmacao dlgConfirmacaoTodos;
    private int idxItem = -1;
    private CheckBox selectionAll;
    private Boolean selected;

    public MateriaisMedicamentosTab(String id, FechamentoContaDTO object) {
        super(id, object);
        this.fechamentoContaDTO = object;
        init();
    }

    private void init() {
        add(tabela = new MultiSelectionTable("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();
        add(selectionAll = new CheckBox("selectionAll", new PropertyModel(this, "selected")));

        add(new AbstractAjaxButton("btnRemoverSelecionado") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isAllEmpty(tabela.getSelectedObjects())) {
                    throw new ValidacaoException(bundle("msgMedicamentoObrigatorio"));
                } else {
                    if (dlgConfirmacao == null) {
                        addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), bundle("msgExcluirRegistrosSelecionados")) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                removerMedicamentos(target, tabela.getSelectedObjects());
                                tabela.setSelectedObject(new ArrayList<ItemContaPaciente>());
                            }

                            @Override
                            public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                                btnConfirmar.add(new AttributeModifier("value", bundle("sim")));
                                btnConfirmar.add(new AttributeModifier("class", "btn-red"));
                                btnFechar.add(new AttributeModifier("value", bundle("nao")));
                                btnFechar.add(new AttributeModifier("class", "btn-green"));

                            }

                        });
                    }
                    dlgConfirmacao.show(target);
                }
            }
        });

        add(new AbstractAjaxButton("btnNovoLancamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                idxItem = -1;
                ItemContaPaciente itemContaPaciente = new ItemContaPaciente();
                itemContaPaciente.setContaPaciente(fechamentoContaDTO.getContaPaciente());
                itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
                viewDlgNovoLancto(target, itemContaPaciente);
            }
        });

        add(new AbstractAjaxButton("btnLancamentosConfirmadosTodos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isNotNullEmpty(tabela.getSelectedObjects())) {
                    if (dlgConfirmacaoTodos == null) {
                        addModal(target, this, dlgConfirmacaoTodos = new DlgConfirmacao(WindowUtil.newModalId(this), bundle("msgTodosSelecionadosConfirmados")) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                changeStatusList(target, tabela.getSelectedObjects(), ItemContaPaciente.Status.CONFIRMADO.value());
                            }
                        });
                    }
                    dlgConfirmacaoTodos.show(target);
                } else {
                    throw new ValidacaoException(bundle("msgMedicamentoObrigatorio"));
                }
            }
        });

        add(getBtnLanctosConfirmados());
    }

    private void viewDlgNovoLancto(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgNovoLancamento == null) {
            WindowUtil.addModal(target, this, dlgNovoLancamento = new DlgNovoLancamentoMaterialMedicamento(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, final ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    if (itemContaPaciente.getPrecoTotal() == null || itemContaPaciente.getPrecoTotal().equals(0.0D)) {
                        MessageUtil.warn(target, dlgNovoLancamento, bundle("msgPrecoTotalZerado"));
                    }
                    adicionaItem(target, itemContaPaciente);
                }
            });
        }

        dlgNovoLancamento.show(target, itemContaPaciente);
    }

    private void adicionaItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (idxItem >= 0) {
            fechamentoContaDTO.getListaItensContaPaciente().remove(idxItem);
            fechamentoContaDTO.getListaItensContaPaciente().add(idxItem, itemContaPaciente);
        } else {
            fechamentoContaDTO.getListaItensContaPaciente().add(itemContaPaciente);
        }

        tabela.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

//        columns.add(getSelectionActionColumn());
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("produto"), proxy.getProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("unidade"), proxy.getUnidade().getUnidade()));
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(new DoubleColumn(bundle("preco"), VOUtils.montarPath(ItemContaPaciente.PROP_PRECO_UNITARIO)).setCasasDecimais(4));
        columns.add(createSortableColumn(bundle("total"), proxy.getPrecoTotal()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente icp) throws ValidacaoException, DAOException {
                        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
                            ItemContaPaciente item = fechamentoContaDTO.getListaItensContaPaciente().get(i);
                            if (item == icp) {
                                idxItem = i;
                                break;
                            }
                        }

                        viewDlgNovoLancto(target, (ItemContaPaciente) SerializationUtils.clone(icp));
                    }
                });

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CONFIRMADO.value());
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        removerMedicamento(target, modelObject);
                    }
                });

                addAction(ActionType.TRANSFERENCIA, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        transferirItem(target, modelObject);
                    }
                }).setIcon(Icon16.arrow_switch);
            }
        };
    }

    private void transferirItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws ValidacaoException, DAOException {
        if (dlgTransferirItemContaPaciente == null) {
            WindowUtil.addModal(target, this, dlgTransferirItemContaPaciente = new DlgTransferirItemContaPaciente(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, TransferenciaItemContaPacienteDTO transferenciaItemContaPacienteDTO) throws ValidacaoException, DAOException {
                    fechamentoContaDTO.getListaItensContaPacienteTransferidos().add(transferenciaItemContaPacienteDTO);
                    removerItem(target, transferenciaItemContaPacienteDTO.getItemContaPaciente());
                }
            });
        }

        dlgTransferirItemContaPaciente.show(target, itemContaPaciente);
    }

    private void removerItem(AjaxRequestTarget target, ItemContaPaciente modelObject) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == modelObject) {
                fechamentoContaDTO.getListaItensContaPaciente().remove(i);
                tabela.setSelected(target, false, modelObject, null);
                break;
            }
        }

        tabela.update(target);
    }

    private void changeStatus(AjaxRequestTarget target, ItemContaPaciente icp, Long status) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == icp) {
                tabela.setSelected(target, false, icp, null);
                fechamentoContaDTO.getListaItensContaPaciente().get(i).setStatus(status);
                break;
            }
        }

        tabela.update(target);
        target.add(getBtnLanctosConfirmados());
    }

    private void changeStatusList(AjaxRequestTarget target, List<ItemContaPaciente> listIcp, Long status) {
        for (int i = 0; i < listIcp.size(); i++) {
            ItemContaPaciente icp = listIcp.get(i);
            for (int j = 0; j < fechamentoContaDTO.getListaItensContaPaciente().size(); j++) {
                if (fechamentoContaDTO.getListaItensContaPaciente().get(j) == icp) {
                    fechamentoContaDTO.getListaItensContaPaciente().get(j).setStatus(status);
                    break;
                }
            }
        }
        tabela.update(target);
        tabela.clearSelection(target);
        target.add(btnLancamentosConfirmados);

    }

    private AbstractAjaxButton getBtnLanctosConfirmados() {
        if (btnLancamentosConfirmados == null) {
            btnLancamentosConfirmados = new AbstractAjaxButton("btnLancamentosConfirmados") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    enviaConfirmados(target);
                    dlgLancamentosConfirmados.show(target);
                }
            };
        }

        btnLancamentosConfirmados.setEnabled(dlgLancamentosConfirmados != null && !dlgLancamentosConfirmados.getListaItens().isEmpty() || btnConfirmadoOK);

        return btnLancamentosConfirmados;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                medicamentosList = new ArrayList();

                for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
                    if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value())) {
                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                            medicamentosList.add(itemContaPaciente);
                        }

                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                            btnConfirmadoOK = true;
                            getBtnLanctosConfirmados();
                        }
                    }
                }

                return medicamentosList;
            }
        };
    }

    private List<ItemContaPaciente> getListConfirmados() {
        medicamentosConfirmadosList = new ArrayList();

        for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value()) && itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                medicamentosConfirmadosList.add(itemContaPaciente);
            }
        }

        return medicamentosConfirmadosList;
    }

    private void enviaConfirmados(AjaxRequestTarget target) {
        if (dlgLancamentosConfirmados == null) {
            WindowUtil.addModal(target, this, dlgLancamentosConfirmados = new DlgLancamentosConfirmadosMateriaisMedicamentos(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    if (dlgLancamentosConfirmados.getListaItens().isEmpty()) {
                        btnConfirmadoOK = false;
                    }
                    getBtnLanctosConfirmados();
                    target.add(getBtnLanctosConfirmados());
                }

                @Override
                public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    changeStatus(target, itemContaPaciente, ItemContaPaciente.Status.ABERTO.value());
                }
            });
        }

        if (CollectionUtils.isNotNullEmpty(getListConfirmados())) {
            dlgLancamentosConfirmados.setListItem(target, getListConfirmados());
        }

        target.add(getBtnLanctosConfirmados());
    }

    private void removerMedicamento(AjaxRequestTarget target, ItemContaPaciente modelObject) {
        changeStatus(target, modelObject, ItemContaPaciente.Status.CANCELADO.value());
    }

    private void removerMedicamentos(AjaxRequestTarget target, List<ItemContaPaciente> listIcp) {
        changeStatusList(target, listIcp, ItemContaPaciente.Status.CANCELADO.value());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("materialMedicamentoAbv");
    }
}
