package br.com.celk.view.indicadores.paciente;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.breadcrumb.BreadCrumbPage;
import br.com.celk.view.indicadores.paciente.medicamento.MedicamentosPacientePage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;


/**
 *
 * <AUTHOR>
 */
@Private

public class IndicadoresPacientePage extends BreadCrumbPage{

    private UsuarioCadsus paciente;
    
    @Deprecated
    public IndicadoresPacientePage() {
    }
    
    public IndicadoresPacientePage(UsuarioCadsus paciente) {
        this.paciente = paciente;
        init();
    }

    public IndicadoresPacientePage(BreadCrumbPage originCrumb, UsuarioCadsus paciente) {
        super(originCrumb);
        this.paciente = paciente;
        init();
    }

    public IndicadoresPacientePage(IModel<?> model, BreadCrumbPage originCrumb, UsuarioCadsus paciente) {
        super(model, originCrumb);
        this.paciente = paciente;
        init();
    }

    public IndicadoresPacientePage(PageParameters parameters, BreadCrumbPage originCrumb, UsuarioCadsus paciente) {
        super(parameters, originCrumb);
        this.paciente = paciente;
        init();
    }
    
    private void init(){
        Form form = new Form("form");
        
        form.add(new AbstractAjaxButton("btnMedicamentos") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new MedicamentosPacientePage(IndicadoresPacientePage.this, paciente));
            }
        });
        
        WebMarkupContainer containerPaciente = new WebMarkupContainer("containerPaciente", new CompoundPropertyModel(paciente));
        
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_NOME_SOCIAL)));
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_NOME_MAE)));
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_DESCRICAO_IDADE)));
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_SEXO_FORMATADO)));
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_BAIRRO)));
        containerPaciente.add(new DisabledInputField(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        
        form.add(containerPaciente);
        
        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("paciente");
    }

}
