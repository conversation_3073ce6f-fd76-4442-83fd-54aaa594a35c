package br.com.celk.view.materiais.materiais.subgrupo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.materiais.subgrupo.dialog.DlgCopiarSubGrupoNaoControlaEstoque;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoNaoControlaEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import ch.lambdaj.function.compare.ArgumentComparator;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.apache.commons.collections.ComparatorUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaSubGrupoNaoControlaEstoquePage extends ConsultaPage<SubGrupoNaoControlaEstoque, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private GrupoProduto grupoProdutoSubGrupo;
    private SubGrupo subGrupo;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DlgCopiarSubGrupoNaoControlaEstoque dlgCopiarSubGrupoNaoControlaEstoque;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());

        AbstractAjaxButton btnCopiar;
        getControls().add(btnCopiar = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCopiar(target);
            }
        });

        btnCopiar.add(new AttributeModifier("class", "button print btn-light-blue"));
        btnCopiar.add(new AttributeModifier("value", bundle("copiar")));
        btnCopiar.add(new AttributeModifier("style", "margin-left: 5px;"));
    }

    private void onCopiar(AjaxRequestTarget target) {
        if (dlgCopiarSubGrupoNaoControlaEstoque == null) {
            addModal(target, dlgCopiarSubGrupoNaoControlaEstoque = new DlgCopiarSubGrupoNaoControlaEstoque(newModalId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, Empresa empresaOrigem, Empresa empresaDestino) throws ValidacaoException, DAOException {
                    confirmarCopiar(target, empresaOrigem, empresaDestino);
                }

            });
        }
        dlgCopiarSubGrupoNaoControlaEstoque.showDlg(target);
    }

    private void confirmarCopiar(AjaxRequestTarget target, Empresa empresaOrigem, Empresa empresaDestino) throws DAOException, ValidacaoException {
        SubGrupoNaoControlaEstoque proxy = on(SubGrupoNaoControlaEstoque.class);

        List<SubGrupoNaoControlaEstoque> listOrigem = LoadManager.getInstance(SubGrupoNaoControlaEstoque.class)
                .addProperties(new HQLProperties(SubGrupoNaoControlaEstoque.class).getProperties())
                .addProperties(new HQLProperties(SubGrupo.class, path(proxy.getSubGrupo())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEmpresa()), empresaOrigem))
                .start().getList();

        List<SubGrupoNaoControlaEstoque> listDestino = LoadManager.getInstance(SubGrupoNaoControlaEstoque.class)
                .addProperties(new HQLProperties(SubGrupoNaoControlaEstoque.class).getProperties())
                .addProperties(new HQLProperties(SubGrupo.class, path(proxy.getSubGrupo())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEmpresa()), empresaDestino))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(listOrigem)) {
            for (SubGrupoNaoControlaEstoque item : listOrigem) {
                item.setCodigo(null);
                item.setEmpresa(empresaDestino);
                item.setUsuario(null);
                item.setDataCadastro(null);
                item.setVersion(null);
            }

            listOrigem.addAll(listDestino);

            Comparator byGrupo = new ArgumentComparator(proxy.getSubGrupo().getId().getCodigoGrupoProduto());
            Comparator bySubGrupo = new ArgumentComparator(proxy.getSubGrupo().getId().getCodigo());

            listOrigem = new ArrayList(Lambda.selectDistinct(listOrigem, ComparatorUtils.chainedComparator(ComparatorUtils.nullLowComparator(byGrupo), 
                    ComparatorUtils.nullLowComparator(bySubGrupo))));

            BOFactoryWicket.getBO(MaterialBasicoFacade.class).salvarSubGrupoNaoControlaEstoque(listOrigem);

            getPageableTable().populate(target);
            info(target, bundle("registro_salvo_sucesso"));
        } else {
            warn(target, bundle("msgNenhumSubGrupoEncontradoParaCopiar"));
        }

        INotificationPanel findParent = findParent(INotificationPanel.class);
        if (findParent != null) {
            findParent.updateNotificationPanel(target, false);
        }
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo", new PropertyModel<SubGrupo>(this, "subGrupo"));
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo", new PropertyModel<GrupoProduto>(this, "grupoProdutoSubGrupo"));
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        SubGrupoNaoControlaEstoque proxy = on(SubGrupoNaoControlaEstoque.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("empresa"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("grupo"), proxy.getSubGrupo().getRoGrupoProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("subGrupo"), proxy.getSubGrupo().getDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<SubGrupoNaoControlaEstoque>() {
            @Override
            public void customizeColumn(SubGrupoNaoControlaEstoque rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<SubGrupoNaoControlaEstoque>() {
                    @Override
                    public void action(AjaxRequestTarget target, SubGrupoNaoControlaEstoque modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroSubGrupoNaoControlaEstoquePage(modelObject, true));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<SubGrupoNaoControlaEstoque>() {
                    @Override
                    public void action(AjaxRequestTarget target, SubGrupoNaoControlaEstoque modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SubGrupoNaoControlaEstoque.class;
            }

            @Override
            public String[] getProperties() {
                SubGrupoNaoControlaEstoque proxy = on(SubGrupoNaoControlaEstoque.class);

                return VOUtils.mergeProperties(new HQLProperties(SubGrupoNaoControlaEstoque.class).getProperties(),
                        new HQLProperties(Empresa.class, path(proxy.getEmpresa())).getProperties(),
                        new HQLProperties(SubGrupo.class, path(proxy.getSubGrupo())).getProperties(),
                        new String[]{
                            path(proxy.getSubGrupo().getRoGrupoProduto().getCodigo()),
                            path(proxy.getSubGrupo().getRoGrupoProduto().getDescricao()),
                            path(proxy.getSubGrupo().getFlagMedicamento())
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SubGrupoNaoControlaEstoque.PROP_EMPRESA, Empresa.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(SubGrupoNaoControlaEstoque.PROP_SUB_GRUPO, BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupoNaoControlaEstoque.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, grupoProdutoSubGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(SubGrupoNaoControlaEstoque.PROP_EMPRESA, empresa));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroSubGrupoNaoControlaEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("subGruposQueNaoControlamEstoque");
    }
}
