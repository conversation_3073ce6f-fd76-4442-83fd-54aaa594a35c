package br.com.celk.view.consorcio.pedidotransferencialicitacao.columnpanel;

import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgMotivoAreaMaxLength;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.DetalhesPedidoTransferenciaLicitacaoPage;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class AprovacaoFinanceiraPedidoTransferenciaColumnPanel extends Panel implements PermissionContainer{

    private AjaxLink btnConsultar;
//    @Permission(type= Permissions.CONFIRMAR, action = ActionsEnum.RENDER)
    private AjaxLink btnAprovarPedido;

//    @Permission(type= Permissions.CANCELAR, action = ActionsEnum.RENDER)
//    private AjaxLink btnReprovarPedido;

    private DlgMotivoAreaMaxLength dlgMotivoAreaMaxLength;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgMotivoObject dlgMotivoConfirmacao;

    private PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;

    public AprovacaoFinanceiraPedidoTransferenciaColumnPanel(String id, PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
        super(id);
        this.pedidoTransferenciaLicitacao = pedidoTransferenciaLicitacao;
        init();
    }

    private void init() {
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onConsultar(target);
            }

        });
        
        add(btnAprovarPedido = new AbstractAjaxLink("btnAprovarPedido") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if(BOFactoryWicket.getBO(ConsorcioFacade.class).existeSaldoDisponivelConsorciadoPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacao)){
                    initDlgConfirmacaoAprovar(target);
                } else {
                    initDlgMotivoConfirmacaoAprovar(target);
                }
            }
            @Override
            public boolean isVisible() {
                return PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro());
            }
        });

//        add(btnReprovarPedido = new AbstractAjaxLink("btnReprovarPedido") {
//            @Override
//            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                initDlgMotivoObjectReprovar(target);
//            }
//            @Override
//            public boolean isVisible() {
//                return PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro());
//            }
//        });
    }

    private void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        setResponsePage(new DetalhesPedidoTransferenciaLicitacaoPage(pedidoTransferenciaLicitacao));
    }

    private void initDlgConfirmacaoAprovar(AjaxRequestTarget target){
        if (dlgConfirmacaoSimNao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgConfirmarAprovacaoPedidoControleFinanceiro")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(ConsorcioFacade.class).aprovarPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacao, false);
                    updateTable(target);
                }
            });
        }
        dlgConfirmacaoSimNao.setObject(pedidoTransferenciaLicitacao);
        dlgConfirmacaoSimNao.show(target);
    }

    private void initDlgMotivoConfirmacaoAprovar(AjaxRequestTarget target){
        if (dlgMotivoConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgMotivoConfirmacao = new DlgMotivoObject<PedidoTransferenciaLicitacao>(WindowUtil.newModalId(this), bundle("msgConfirmarAprovacaoPedidoControleFinanceiro")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, PedidoTransferenciaLicitacao object) throws ValidacaoException, DAOException {
                    object.setDescricaoMotivoControleFinanceiro(motivo);
                    BOFactoryWicket.getBO(ConsorcioFacade.class).aprovarPedidoTransferenciaLicitacao(object, false);
                    updateTable(target);
                }
            });
        }
        dlgMotivoConfirmacao.setObject(pedidoTransferenciaLicitacao);
        dlgMotivoConfirmacao.show(target);
    }

//    private void initDlgMotivoObjectReprovar(AjaxRequestTarget target) {
//        if (dlgMotivoAreaMaxLength == null) {
//            WindowUtil.addModal(target, this, dlgMotivoAreaMaxLength = new DlgMotivoAreaMaxLength(WindowUtil.newModalId(this), bundle("reprovarPedidoControleFinanceiro"), 1024L) {
//                @Override
//                public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
//                    PedidoTransferenciaLicitacao ptl = (PedidoTransferenciaLicitacao) object;
//                    ptl.setDescricaoMotivoControleFinanceiro(motivo);
//                    BOFactoryWicket.getBO(ConsorcioFacade.class).reprovarPedidoTransferenciaLicitacao(ptl);
//                    updateTable(target);
//                }
//            });
//        }
//        dlgMotivoAreaMaxLength.setObject(pedidoTransferenciaLicitacao);
//        dlgMotivoAreaMaxLength.show(target);
//    }

    public abstract void updateTable(AjaxRequestTarget target);

    public AjaxLink getBtnAprovarPedido() {
        return btnAprovarPedido;
    }

//    public AjaxLink getBtnReprovarPedido() {
//        return btnReprovarPedido;
//    }

}
