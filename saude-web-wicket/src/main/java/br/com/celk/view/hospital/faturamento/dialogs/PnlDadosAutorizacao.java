package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.hospital.tiss.DadosAutorizacaoTiss;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDadosAutorizacao extends Panel {

    private CompoundPropertyModel<DadosAutorizacaoTiss> model;
    private InputField txtNumeroGuia;
    private InputField txtNumeroGuiaPrincipal;
    private DateChooser dchDataAutorizacao;
    private DateChooser dchDataSolicitacao;
    private InputField txtSenhaAutorizacao;
    private DateChooser dchValidadeSenha;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnCancelar;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissionalSolicitante;
    private DropDown dropDownCboSolicitante;

    public PnlDadosAutorizacao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new DadosAutorizacaoTiss()));

        DadosAutorizacaoTiss proxy = on(DadosAutorizacaoTiss.class);

        form.add(txtNumeroGuia = new InputField(path(proxy.getNumeroGuia())));
        form.add(dchDataSolicitacao = new DateChooser(path(proxy.getDataSolicitacao())));
        form.add(txtNumeroGuiaPrincipal = new InputField(path(proxy.getNumeroGuiaPrincipal())));
        form.add(dchDataAutorizacao = new DateChooser(path(proxy.getDataAutorizacao())));
        form.add(txtSenhaAutorizacao = new InputField(path(proxy.getSenhaAutorizacao())));
        form.add(dchValidadeSenha = new DateChooser(path(proxy.getValidadeSenha())));
        form.add(autoCompleteConsultaProfissionalSolicitante = new AutoCompleteConsultaProfissional(path(proxy.getProfissionalSolicitante())));
        autoCompleteConsultaProfissionalSolicitante.addPropertiesLoad(VOUtils.montarPath(Profissional.PROP_CONSELHO_CLASSE, OrgaoEmissor.PROP_CODIGO_CONSELHO_TISS));
        autoCompleteConsultaProfissionalSolicitante.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                updateDropDownCboSolicitante(target, true);
            }
        });
        autoCompleteConsultaProfissionalSolicitante.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional profissional) {
                updateDropDownCboSolicitante(target, true);
            }
        });

        form.add(dropDownCboSolicitante = new DropDown(path(proxy.getCboSolicitante())));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                DadosAutorizacaoTiss dadosAutorizacaoTiss = model.getObject();
                validacoes(dadosAutorizacaoTiss);
                confirmar(target, dadosAutorizacaoTiss);
            }
        });

        form.add(btnCancelar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnCancelar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        add(form);
        dropDownCboSolicitante.setEnabled(false);
    }

    private void validacoes(DadosAutorizacaoTiss dadosAutorizacaoTiss) throws ValidacaoException {
        ValidacaoProcesso validacoes = new ValidacaoProcesso();

        // Validações do Profissional
        Profissional profissionalSolicitante = dadosAutorizacaoTiss.getProfissionalSolicitante();
        if (profissionalSolicitante != null) {
            ValidacaoProcesso validacoesProfissional = new ValidacaoProcesso();

            // Deve possuir Conselho de Classe Configurado
            if (profissionalSolicitante.getConselhoClasse() == null || profissionalSolicitante.getConselhoClasse().getCodigoConselhoTiss() == null) {
                validacoesProfissional.add("* " + bundle("conselhoClasse"));
            }

            // Deve possuir UF do Conselho de Registro Configurado
            if (profissionalSolicitante.getUnidadeFederacaoConselhoRegistro() == null) {
                validacoesProfissional.add("* " + bundle("ufConselhoRegistro"));
            }

            // Deve possuir Número do Registro
            if (profissionalSolicitante.getNumeroRegistro() == null) {
                validacoesProfissional.add("* " + bundle("numeroRegistro"));
            }

            if (dadosAutorizacaoTiss.getCboSolicitante() == null) {
                Empresa empresaPrincipal = dadosAutorizacaoTiss.getContaPaciente().getAtendimentoInformacao().getEmpresa();
                if (dadosAutorizacaoTiss.getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal() != null) {
                    empresaPrincipal = dadosAutorizacaoTiss.getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal();
                }

                validacoesProfissional.add("* " + bundle("msgCBONaoEncontradoEstabelecimentoX", empresaPrincipal.getDescricao()));
            }

            if (!validacoesProfissional.getMensagemList().isEmpty()) {
                validacoes.add(bundle("msgProfissionalXNaoEstaCorretamenteConfigurado", profissionalSolicitante.getNome()));
                validacoes.getValidacaoProcessoList().add(validacoesProfissional);
            }
        }

        if (!validacoes.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacoes);
        }
    }

    public void updateDropDownCboSolicitante(AjaxRequestTarget target, boolean updateComponentValue) {
        Profissional profissional = (Profissional) autoCompleteConsultaProfissionalSolicitante.getComponentValue();

        dropDownCboSolicitante.removeAllChoices();
        if (profissional != null) {
            try {
                dropDownCboSolicitante.setEnabled(true);

                Empresa empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa();
                if (this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal() != null) {
                    empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal();
                }

                QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
                param.setEmpresa(empresaPrincipal);
                param.setProfissional(profissional);
                List<TabelaCbo> cbos = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);

                if (CollectionUtils.isNotNullEmpty(cbos)) {
                    for (TabelaCbo cbo : cbos) {
                        dropDownCboSolicitante.addChoice(cbo, cbo.getDescricaoFormatado());
                    }
                    if (updateComponentValue) {
                        dropDownCboSolicitante.setComponentValue(cbos.get(0));
                    }
                } else {
                    dropDownCboSolicitante.limpar(target);
                }
            } catch (SGKException ex) {
                Loggable.log.warn(ex.getMessage(), ex);
            }
        } else {
            dropDownCboSolicitante.setEnabled(false);
            dropDownCboSolicitante.limpar(target);
        }

        if (target != null) {
            target.add(dropDownCboSolicitante);
        }
    }

    private void confirmar(AjaxRequestTarget target, DadosAutorizacaoTiss dadosAutorizacaoTiss) throws ValidacaoException, DAOException {
        BOFactoryWicket.save(dadosAutorizacaoTiss);
        onFechar(target);
    }

    public void setContaPaciente(AjaxRequestTarget target, ContaPaciente contaPaciente) {
        DadosAutorizacaoTiss proxy = on(DadosAutorizacaoTiss.class);

        DadosAutorizacaoTiss dadosAutorizacaoTiss = LoadManager.getInstance(DadosAutorizacaoTiss.class)
                .addProperties(new HQLProperties(DadosAutorizacaoTiss.class).getProperties())
                .addProperties(new HQLProperties(ContaPaciente.class, path(proxy.getContaPaciente())).getProperties())
                .addProperties(new HQLProperties(TabelaCbo.class, path(proxy.getCboSolicitante())).getProperties())
                .addProperty(path(proxy.getProfissionalSolicitante().getNumeroRegistro()))
                .addProperty(path(proxy.getProfissionalSolicitante().getUnidadeFederacaoConselhoRegistro()))
                .addProperty(path(proxy.getProfissionalSolicitante().getConselhoClasse().getCodigo()))
                .addProperty(path(proxy.getProfissionalSolicitante().getConselhoClasse().getCodigoConselhoTiss()))
                .addProperty(path(proxy.getContaPaciente().getAtendimentoInformacao().getEmpresa().getCodigo()))
                .addProperty(path(proxy.getContaPaciente().getAtendimentoInformacao().getEmpresa().getDescricao()))
                .addProperty(path(proxy.getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal().getCodigo()))
                .addProperty(path(proxy.getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getContaPaciente()), contaPaciente))
                .start().getVO();

        if (dadosAutorizacaoTiss == null) {
            dadosAutorizacaoTiss = new DadosAutorizacaoTiss();
            dadosAutorizacaoTiss.setContaPaciente(contaPaciente);
        }

        limpar(target);
        model.setObject(dadosAutorizacaoTiss);
        updateDropDownCboSolicitante(target, false);
        dropDownCboSolicitante.setComponentValue(dadosAutorizacaoTiss.getCboSolicitante());
        target.add(dropDownCboSolicitante);
        update(target);
        target.focusComponent(txtNumeroGuia);
    }

    private void limpar(AjaxRequestTarget target) {
        txtNumeroGuia.limpar(target);
        txtNumeroGuiaPrincipal.limpar(target);
        dchDataAutorizacao.limpar(target);
        dchDataSolicitacao.limpar(target);
        txtSenhaAutorizacao.limpar(target);
        dchValidadeSenha.limpar(target);
        autoCompleteConsultaProfissionalSolicitante.limpar(target);
        dropDownCboSolicitante.removeAllChoices(target);
        dropDownCboSolicitante.limpar(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
