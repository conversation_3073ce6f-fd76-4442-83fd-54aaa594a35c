package br.com.celk.view.consorcio.modelodocumento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.consorcio.DocumentoConsorcio;

import java.text.SimpleDateFormat;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public class ModeloDocumentoConsorcioHelper {

    public static String builder(DocumentoConsorcio documentoConsorcio, String modelo) throws ValidacaoException, DAOException {
        String modeloCompilado = modelo;
        if (documentoConsorcio.getModeloDocumentoConsorcio() == null || modeloCompilado == null) {
            throw new ValidacaoException(bundle("msgModeloDocumentoNaoDisponivel"));
        }

        if (documentoConsorcio.getUsuarioCadsus() != null) {
            if (documentoConsorcio.getUsuarioCadsus().getEnderecoUsuarioCadsus() == null || documentoConsorcio.getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoComCepFormatado().isEmpty()) {
                documentoConsorcio.getUsuarioCadsus().setEnderecoUsuarioCadsus(null);
                documentoConsorcio.getUsuarioCadsus().setEnderecoUsuarioCadsus(UsuarioCadsusHelper.getEnderecoUsuarioCadsus(documentoConsorcio.getUsuarioCadsus(), false));
            }
            if (documentoConsorcio.getUsuarioCadsus().getReferencia() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPaciente\\|", documentoConsorcio.getUsuarioCadsus().getReferencia());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPaciente\\|", "referência desconhecida");
            }
            if (documentoConsorcio.getUsuarioCadsus().getNome() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Nome\\|", documentoConsorcio.getUsuarioCadsus().getNome());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Nome\\|", "nome não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getApelido() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@NomeSocial\\|", documentoConsorcio.getUsuarioCadsus().getApelido());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@NomeSocial\\|", "nome social não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getDataNascimento() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@DataNascimento\\|", DataUtil.getDataFormatadaMesString(documentoConsorcio.getUsuarioCadsus().getDataNascimento()));
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@DataNascimento\\|", "data nascimento não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getDescricaoIdadeAnoMesDiaExtenso() != null && !documentoConsorcio.getUsuarioCadsus().getDescricaoIdadeAnoMesDiaExtenso().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Idade\\|", documentoConsorcio.getUsuarioCadsus().getDescricaoIdadeAnoMesDiaExtenso());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Idade\\|", "idade não informada");
            }
            if (documentoConsorcio.getUsuarioCadsus().getSexoFormatado() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Sexo\\|", documentoConsorcio.getUsuarioCadsus().getSexoFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Sexo\\|", "sexo não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getNomeMae() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@NomeMae\\|", documentoConsorcio.getUsuarioCadsus().getNomeMae());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@NomeMae\\|", "nome da mãe não informado");
            }
            if (!documentoConsorcio.getUsuarioCadsus().getTelefonesFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePaciente\\|", documentoConsorcio.getUsuarioCadsus().getTelefonesFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePaciente\\|", "telefone não informado");
            }
            if (!documentoConsorcio.getUsuarioCadsus().getCelularFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPaciente\\|", documentoConsorcio.getUsuarioCadsus().getCelularFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPaciente\\|", "celular não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getCns() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CNS\\|", documentoConsorcio.getUsuarioCadsus().getCns());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CNS\\|", "CNS não informado");
            }
            if (documentoConsorcio.getUsuarioCadsus().getEnderecoUsuarioCadsus() != null && !documentoConsorcio.getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoComCepFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPaciente\\|", documentoConsorcio.getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoComCepFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPaciente\\|", "endereço não informado");
            }
        } else {
            modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPaciente\\|", "referência desconhecida");
            modeloCompilado = modeloCompilado.replaceAll("\\|@Nome\\|", "nome não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@NomeSocial\\|", "nome social não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@DataNascimento\\|", "data nascimento não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@Idade\\|", "idade não informada");
            modeloCompilado = modeloCompilado.replaceAll("\\|@Sexo\\|", "sexo não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@NomeMae\\|", "nome da mãe não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePaciente\\|", "telefone não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPaciente\\|", "celular não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@CNS\\|", "CNS não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPaciente\\|", "endereço não informado");
        }

        if (documentoConsorcio.getConsorcioPrestador() != null && documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador() != null) {

            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getReferencia() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPrestador\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getReferencia());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPrestador\\|", "referência desconhecida");
            }
            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getDescricao() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@DescricaoPrestador\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getDescricao());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@DescricaoPrestador\\|", "descrição não informada");
            }
            if (!documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCnpjFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CPFCNPJ\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCnpjFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CPFCNPJ\\|", "CPF ou CNPJ não informado");
            }
            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCnes() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CNES\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCnes());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CNES\\|", "CNES não informado");
            }
            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getContato() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Contato\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getContato());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Contato\\|", "contato não informado");
            }
            if (!documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getTelefoneFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePrestador\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getTelefoneFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePrestador\\|", "telefone não informado");
            }
            if (!documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCelularFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPrestador\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getCelularFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPrestador\\|", "celular não informado");
            }
            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getEmail() != null) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Email\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getEmail());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@Email\\|", "e-mail não informado");
            }
            if (documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador() != null && !documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getEnderecoCidadeBairroCepFormatado().isEmpty()) {
                modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPrestador\\|", documentoConsorcio.getConsorcioPrestador().getEmpresaPrestador().getEnderecoCidadeBairroCepFormatado());
            } else {
                modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPrestador\\|", "endereço não informado");
            }
        } else {
            modeloCompilado = modeloCompilado.replaceAll("\\|@ReferenciaPrestador\\|", "referência desconhecida");
            modeloCompilado = modeloCompilado.replaceAll("\\|@DescricaoPrestador\\|", "descrição não informada");
            modeloCompilado = modeloCompilado.replaceAll("\\|@CPFCNPJ\\|", "CPF ou CNPJ não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@CNES\\|", "CNES não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@Contato\\|", "contato não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@TelefonePrestador\\|", "telefone não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@CelularPrestador\\|", "celular não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@Email\\|", "e-mail não informado");
            modeloCompilado = modeloCompilado.replaceAll("\\|@EnderecoPrestador\\|", "endereço não informado");
        }

        SimpleDateFormat sdfData = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat sdfHora = new SimpleDateFormat("HH:mm:ss");
        modeloCompilado = modeloCompilado.replaceAll("\\|@Data\\|", sdfData.format(DataUtil.getDataAtual()));
        modeloCompilado = modeloCompilado.replaceAll("\\|@Hora\\|", sdfHora.format(DataUtil.getDataAtual()));

        return modeloCompilado;
    }

}
