package br.com.celk.view.consorcio.consorcioguiaprocedimento.columnpanel;

import br.com.celk.component.dialog.DlgImpressao;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.ReportLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.AuditoriaGuiaProcedimentosPage;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.DlgAuditoriaGuia;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.DlgVizualizaMotivoReprovacao;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioGuiaProcedimentoItemDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.SalvarGuiaProcedimentoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.*;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static br.com.celk.component.window.WindowUtil.newModalId;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.util.VOUtils.montarPath;

/**
 * <AUTHOR>
 */
public abstract class AuditoriaGuiaProcedimentoColumnPanel extends Panel implements PermissionContainer {

    @Permission(type = Permissions.IMPRIMIR, action = ActionsEnum.RENDER)
    private ReportLink btnImprimir;

    @Permission(type = Permissions.CANCELAR_AGENDAMENTO, action = ActionsEnum.RENDER)
    private AjaxLink btnCancelar;

    @Permission(type = Permissions.AUDITA_GUIA_PROCEDIMENTO, action = ActionsEnum.RENDER)
    private AjaxLink btnAuditar;

    private AjaxLink btnAgendar;

    private AjaxLink btnVizualizarMotCanc;

    private DlgMotivoObject<ConsorcioGuiaProcedimento> dlgMotivoCancelamento;
    private DlgVizualizaMotivoReprovacao dlgMotivoReprovacaoVizualiza;
    private DlgMotivoObject<ConsorcioGuiaProcedimento> dlgMotivoReprovacao;
    private DlgAuditoriaGuia dlgAuditoriaGuiaAuditoria;
    private DlgAuditoriaGuia dlgAuditoriaGuiaAgendamento;
    private DlgImpressaoObject<ConsorcioGuiaProcedimento> dlgConfirmacaoImpressao;

    private final ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
    private final Long statusGuia;
    private final Empresa empresaSessao;
    private final boolean isUsuarioAuditor;
    private final boolean isAgendamentoSemAuditoria;

    private Class clazz;

    public AuditoriaGuiaProcedimentoColumnPanel(String id, ConsorcioGuiaProcedimento consorcioGuiaProcedimento, boolean isUsuarioAuditor, boolean isAgendamentoSemAuditoria) {
        super(id);
        this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
        this.statusGuia = consorcioGuiaProcedimento.getStatus();
        this.empresaSessao = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        this.isUsuarioAuditor = isUsuarioAuditor;
        this.isAgendamentoSemAuditoria = isAgendamentoSemAuditoria;
        init();
    }

    private void init() {
        add(btnImprimir = new ReportLink("btnImprimir") {

            @Override
            public DataReport getDataReport() throws ReportException {
                return BOFactoryWicket.getBO(ConsorcioFacade.class).impressaoGuiaProcedimentos(consorcioGuiaProcedimento.getCodigo(), true);
            }

            @Override
            public boolean isVisible() {
                return Arrays.asList(
                        ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGENDADO.value(),
                        ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value(),
                        ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value()
                ).contains(statusGuia);
            }
        });
        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onCancelar(target);
            }

            @Override
            public boolean isVisible() {
                return ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGENDADO.value().equals(statusGuia);
            }

        });

        add(btnAuditar = new AbstractAjaxLink("btnAuditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onAuditar(target);
            }

            @Override
            public boolean isVisible() {
                return ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.PENDENTE.value()
                        .equals(statusGuia) && !isAgendamentoSemAuditoria;
            }

        });

        add(btnAgendar = new AbstractAjaxLink("btnAgendar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onAgendar(target);
            }

            @Override
            public boolean isVisible() {
                return ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGUARDANDO_AGENDAMENTO.value()
                        .equals(statusGuia) || (ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.PENDENTE.value()
                        .equals(statusGuia) && isAgendamentoSemAuditoria);
            }

        });

        add(btnVizualizarMotCanc = new AbstractAjaxLink("btnVizualizar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onVizualizar(target);
            }

            @Override
            public boolean isVisible() {
                return ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.REPROVADO.value().equals(statusGuia);
            }

        });

        btnImprimir.add(new AttributeModifier("title", BundleManager.getString("imprimir")));
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
        btnAuditar.add(new AttributeModifier("title", BundleManager.getString("auditar")));
        btnAgendar.add(new AttributeModifier("title", BundleManager.getString("agendar")));
        btnVizualizarMotCanc.add(new AttributeModifier("title", BundleManager.getString("vizualizarMotivoReprovacao")));

        add(AttributeModifier.replace("style", "width: 60px;"));
    }

    private void onVizualizar(AjaxRequestTarget target) {
        if (dlgMotivoReprovacaoVizualiza == null) {
            WindowUtil.addModal(target,this, dlgMotivoReprovacaoVizualiza = new DlgVizualizaMotivoReprovacao(newModalId(this)));
        }
        dlgMotivoReprovacaoVizualiza.show(target, consorcioGuiaProcedimento);
    }

    private void initDlgMotivoCancelamento(AjaxRequestTarget target) {
        if (dlgMotivoCancelamento == null) {
            WindowUtil.addModal(target, this, dlgMotivoCancelamento = new DlgMotivoObject<ConsorcioGuiaProcedimento>(newModalId(this), bundle("motivoCancelamentoGuia")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, ConsorcioGuiaProcedimento consorcioGuiaProcedimento) throws ValidacaoException, DAOException {
                    consorcioGuiaProcedimento.setMotivoCancelamento(motivo);
                    atualizaCotaProcedimentoPrestador(consorcioGuiaProcedimento);
                    BOFactoryWicket.getBO(ConsorcioFacade.class).cancelarGuiaProcedimento(consorcioGuiaProcedimento);
                    updateTable(target);
                }
            });
        }
    }

    private void atualizaCotaProcedimentoPrestador(ConsorcioGuiaProcedimento consorcioGuiaProcedimento) throws ValidacaoException, DAOException {
        List<ConsorcioGuiaProcedimentoItem> procedimentos = LoadManager.getInstance(ConsorcioGuiaProcedimentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioGuiaProcedimentoItem.PROP_CONSORCIO_GUIA_PROCEDIMENTO, consorcioGuiaProcedimento))
                .start().getList();

        for(ConsorcioGuiaProcedimentoItem procedimento: procedimentos) {
            ConsorcioPrestadorServicoCota cota = LoadManager.getInstance(ConsorcioPrestadorServicoCota.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(montarPath(ConsorcioPrestadorServicoCota.PROP_CONSORCIO_PRESTADOR_SERVICO, ConsorcioPrestadorServico.PROP_CONSORCIO_PRESTADOR), consorcioGuiaProcedimento.getConsorcioPrestador()))
                    .addParameter(new QueryCustom.QueryCustomParameter(montarPath(ConsorcioPrestadorServicoCota.PROP_CONSORCIO_PRESTADOR_SERVICO, ConsorcioPrestadorServico.PROP_CONSORCIO_PROCEDIMENTO), procedimento.getConsorcioProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ConsorcioPrestadorServicoCota.PROP_COMPETENCIA, DataUtil.adjustDateToCompetencia(consorcioGuiaProcedimento.getDataAgendamento())))
                    .start().getVO();

            if(Objects.nonNull(cota)) {
                cota.setContagemCotaConsorcio(Math.addExact(cota.getContagemCotaConsorcio(), procedimento.getQuantidade()));
                BOFactory.save(cota);
            }
        }
    }

    private void initDlgMotivoReprovacao(AjaxRequestTarget target) {
        if (dlgMotivoReprovacao == null) {
            WindowUtil.addModal(target, this, dlgMotivoReprovacao = new DlgMotivoObject<ConsorcioGuiaProcedimento>(newModalId(this), bundle("motivoReprovacaoGuia")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, ConsorcioGuiaProcedimento consorcioGuiaProcedimento) throws ValidacaoException, DAOException {
                    close(target);
                    consorcioGuiaProcedimento.setMotivoReprovacao(motivo);
                    BOFactoryWicket.getBO(ConsorcioFacade.class).reprovarGuiaProcedimento(consorcioGuiaProcedimento);
                    updateTable(target);
                }
            });
        }
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoImpressao = new DlgImpressaoObject<ConsorcioGuiaProcedimento>(newModalId(this), bundle("desejaImprimirGuiaProcedimento")) {
                @Override
                public DataReport getDataReport(ConsorcioGuiaProcedimento consorcioGuiaProcedimento) throws ReportException {
                    return BOFactoryWicket.getBO(ConsorcioFacade.class).impressaoGuiaProcedimentos(consorcioGuiaProcedimento.getCodigo(), true);
                }
            });
        }

        dlgConfirmacaoImpressao.show(target, consorcioGuiaProcedimento);
    }

    public ReportLink getBtnImprimir() {
        return btnImprimir;
    }

    public AjaxLink getBtnAuditar() {
        return btnAuditar;
    }

    public AjaxLink getBtnAgendar() {
        return btnAgendar;
    }

    public abstract void updateTable(AjaxRequestTarget target);


    private void onAuditar(AjaxRequestTarget target) throws ValidacaoException {
        if (!isUsuarioAuditor) {
            throw new ValidacaoException(BundleManager.getString("msgUsuarioSemFuncaoAuditor"));
        } else {
            if (dlgAuditoriaGuiaAuditoria == null) {
                WindowUtil.addModal(target, this, dlgAuditoriaGuiaAuditoria = new DlgAuditoriaGuia<ConsorcioGuiaProcedimento>(newModalId(this), DlgAuditoriaGuia.TipoDialog.AUDITORIA) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ConsorcioFacade.class).aprovarGuiaProcedimento(consorcioGuiaProcedimento);
                        updateTable(target);
                        success(BundleManager.getString("guiaAuditadaSucesso"));
                        updateNotificationPanel(target);
                    }

                    @Override
                    public void onReprovar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {
                        initDlgMotivoReprovacao(target);
                        if (dlgMotivoReprovacao != null) {
                            dlgMotivoReprovacao.setObject(consorcioGuiaProcedimento);
                            dlgMotivoReprovacao.show(target);
                        }
                    }

                    @Override
                    public void onSalvar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {
                    }
                });
            }
            dlgAuditoriaGuiaAuditoria.show(target, consorcioGuiaProcedimento);
        }
    }

    private void onAgendar(AjaxRequestTarget target) throws ValidacaoException {
        WindowUtil.addModal(target, this, dlgAuditoriaGuiaAgendamento = new DlgAuditoriaGuia<ConsorcioGuiaProcedimento>(newModalId(this), DlgAuditoriaGuia.TipoDialog.AGENDAMENTO) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {

            }

            @Override
            public void onReprovar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {

            }

            @Override
            public void onSalvar(AjaxRequestTarget target, ConsorcioGuiaProcedimento object) throws ValidacaoException, DAOException {
                this.salvar(target);
                updateTable(target);
                close(target);
                initDialogImpressao(target);
            }
        });
        dlgAuditoriaGuiaAgendamento.show(target, consorcioGuiaProcedimento);
    }

    private void onCancelar(AjaxRequestTarget target) throws ValidacaoException {
        if (!Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(empresaSessao.getTipoUnidade())) {
            if (!Arrays.asList(
                    ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.AGENDADO.value(),
                    ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value()
                ).contains(statusGuia)) {
                throw new ValidacaoException(BundleManager.getString("msgCancelamentoIndisponivelParaGuiasNaoAberta"));
            }
        }
        initDlgMotivoCancelamento(target);
        if (dlgMotivoCancelamento != null) {
            dlgMotivoCancelamento.setObject(consorcioGuiaProcedimento);
            dlgMotivoCancelamento.show(target);
        }
    }

}
