<wicket:extend>
    <div class="span-10 last">
        <form wicket:id="form" class="dirty">
            <fieldset>
                <h2><label><wicket:message key="dados"/></label></h2>
                <div class="field">
                    <div class="span-horizontal">
                        <fieldset>
                            <h2><label><wicket:message key="dadosEstabelecimento"/></label></h2>
                            <div class="field"><label><wicket:message key="estabelecimento" /></label><div class="group" wicket:id="requerimentoReceitaB.estabelecimento" /></div>
                            <div class="field"><label><wicket:message key="endereco"/></label><input type="text" size="120" wicket:id="enderecoEstabelecimento"/></div>
                            <div class="field"><label><wicket:message key="responsavelTecnico"/></label><select wicket:id="requerimentoReceitaB.responsavelTecnico" /></div>
                        </fieldset>
                    </div>
                </div>
                <div class="field">
                    <div class="span-horizontal">
                        <fieldset>
                            <h2><label><wicket:message key="dadosProfissional"/></label></h2>
                            <div class="field"><label><wicket:message key="profissional" /></label><div class="group" wicket:id="requerimentoReceitaB.vigilanciaProfissional" /></div>
                            <div class="field"><label><wicket:message key="endereco"/></label><input type="text" size="120" wicket:id="enderecoProfissional"/></div>
                            <div class="field"><label><wicket:message key="especialidade"/></label><input type="text" size="80" wicket:id="especialidade"/></div>
                            <div class="field"><label><wicket:message key="crmCrmvCro"/></label><input type="text" size="20" wicket:id="crm"/></div>
                        </fieldset>
                    </div>
                </div>
                <div class="field">
                    <div class="span-horizontal">
                        <fieldset>
                            <h2><label><wicket:message key="dadosReceita"/></label></h2>
                            <div class="field">
                                <div class="span-3"><label><wicket:message key="subtipo" /></label><select wicket:id="requerimentoReceitaB.subtipo" /></div>

                                <div class="span-7 last" wicket:id="containerFolha"><label><wicket:message key="nFolhas"/></label><input onkeyup='if (isNaN(this.value)) {this.value = ""}' type="text" size="6" maxlength="5" wicket:id="requerimentoReceitaB.numeroFolhas"/>
                                    <span wicket:id="valorTaxa" id="taxaFolha"></span>
                                </div>
                                <div class="span-7 last" wicket:id="containerTalao"><label><wicket:message key="nTaloes"/></label><input onkeyup='if (isNaN(this.value)) {this.value = ""}' type="text" size="6" maxlength="5"  wicket:id="taloes"/>
                                    <span wicket:id="valorTaxa" id="taxaTalao"></span>
                                </div>

                            </div>
                            <div class="field" wicket:id="containerNumeroAutorizacao"><label><wicket:message key="nAutorizacao"/></label><input type="text" size="32" wicket:id="requerimentoReceitaB.protocoloFormatado"/></div>
                            <div class="field" wicket:id="containerNumeracao">
                                <div class="span-4 last"><label><wicket:message key="numeracaoInicial"/></label><input type="text" size="32" wicket:id="requerimentoReceitaB.numeracaoInicial"/></div>
                                <div class="span-6 last"><label><wicket:message key="numeracaoFinal"/></label><input type="text" size="32" wicket:id="requerimentoReceitaB.numeracaoFinal"/></div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="field">
                    <div class="span-horizontal">
                        <fieldset wicket:id="dadosSolicitante">
                            <h2><label><wicket:message key="dadosSolicitante"/></label></h2>
                            <div class="field">
                                <span wicket:id="messageLabel" class="info-message-field">message</span>
                            </div>
                            <div class="field"><label><wicket:message key="nomeSolicitante"/></label><input type="text" size="65" maxlength="80" wicket:id="requerimentoReceitaB.requerimentoVigilancia.nomeSolicitante"/></div>
                            <div class="field">
                                <label><wicket:message key="cpf"/></label><input type="text" class="cpf" size="20" maxlength="14" wicket:id="requerimentoReceitaB.requerimentoVigilancia.cpfSolicitante"/>
                                <label><wicket:message key="rg"/></label><input type="text" size="20" maxlength="13" wicket:id="requerimentoReceitaB.requerimentoVigilancia.rgSolicitante"/>
                                <label><wicket:message key="dataEmissao"/></label><div class="group" wicket:id="requerimentoReceitaB.requerimentoVigilancia.dataEmissaoRgSolicitante"/>
                            </div>
                            <div class="field"><label><wicket:message key="endereco"/></label><input type="text" maxlength="80" wicket:id="requerimentoReceitaB.requerimentoVigilancia.enderecoSolicitante" size="120"/></div>
                            <div class="field"><label><wicket:message key="email"/></label><input type="text" size="65" maxlength="100" wicket:id="requerimentoReceitaB.requerimentoVigilancia.emailSolicitante"/></div>
                            <div class="field"><label><wicket:message key="ocupacao"/></label><input type="text" size="65" maxlength="200" wicket:id="requerimentoReceitaB.requerimentoVigilancia.cargoSolicitante"/></div>
                            <div class="field">
                                <div class="span-3"><label><wicket:message key="telefone" /></label><input wicket:id="requerimentoReceitaB.requerimentoVigilancia.telefoneSolicitante" type="text" maxlength="15" class="telefoneFixo" /></div>
                                <div class="span-7 last"><label><wicket:message key="celular" /></label><input wicket:id="requerimentoReceitaB.requerimentoVigilancia.celularSolicitante" type="text" maxlength="15" class="fone" /></div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div wicket:id="dadosComumRequerimentoVigilancia"/>    
                <div wicket:id="requerimentoVigilanciaAnexo"/>
            </fieldset>
            <div wicket:id="ocorrencias"></div>
            <div id="control-bottom">
                <input type="button" wicket:message="value:voltar" class="arrow-left" wicket:id="btnVoltar"/>
                <input type="submit" wicket:id="btnSalvar" class="save" wicket:message="value:salvar" />
            </div>
        </form>
    </div>
</wicket:extend>