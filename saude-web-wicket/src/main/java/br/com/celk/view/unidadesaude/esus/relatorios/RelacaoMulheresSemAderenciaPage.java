package br.com.celk.view.unidadesaude.esus.relatorios;

import br.com.celk.bo.esus.interfaces.facade.EsusReportFacade;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelacaoPacientesSemAderenciaDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */

public class RelacaoMulheresSemAderenciaPage extends RelatorioPage<RelacaoPacientesSemAderenciaDTOParam> {

    @Override
    public void init(Form<RelacaoPacientesSemAderenciaDTOParam> form) {
        RelacaoPacientesSemAderenciaDTOParam proxy = on(RelacaoPacientesSemAderenciaDTOParam.class);

        form.add(DropDownUtil.getAreaDropDown(path(proxy.getEquipeArea())));
        form.add(DropDownUtil.getSimNaoDropDown(path(proxy.getIndicatorFour()), true, "Todos"));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown(path(proxy.getTipoArquivo())));
    }

    @Override
    public Class<RelacaoPacientesSemAderenciaDTOParam> getDTOParamClass() {
        return RelacaoPacientesSemAderenciaDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoPacientesSemAderenciaDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EsusReportFacade.class).relacaoMulheresSemAderencia(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoMulheresSemAderencia");
    }
}
