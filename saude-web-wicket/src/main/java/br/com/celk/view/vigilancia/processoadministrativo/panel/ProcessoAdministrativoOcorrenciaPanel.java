package br.com.celk.view.vigilancia.processoadministrativo.panel;

import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;

/**
 * Created by le<PERSON><PERSON> on 21/05/18.
 */
public class ProcessoAdministrativoOcorrenciaPanel extends Panel {

    private static final String CSS_FILE = "ProcessoAdministrativoOcorrenciaPanel.css";

    private ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia;

    public ProcessoAdministrativoOcorrenciaPanel(String id, ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia) {
        super(id);
        this.processoAdministrativoOcorrencia = processoAdministrativoOcorrencia;
        init();
    }

    private void init() {
        add(new Label("tipoOcorrencia", new Model(processoAdministrativoOcorrencia.getDescricaoTipoFormatado())));
        add(new Label("dataOcorrencia", new Model(processoAdministrativoOcorrencia.getDataOcorrencia())));
        add(new Label("usuario", new Model(processoAdministrativoOcorrencia.getUsuario().getNome())));
        add(new Label("descricaoOcorrencia", new Model(processoAdministrativoOcorrencia.getDescricao())).setEscapeModelStrings(false));
        addPanel();
    }

    private void addPanel() {
        Panel panel = null;
        Long tipo = processoAdministrativoOcorrencia.getTipo();
        if (processoAdministrativoOcorrencia.getDocumento() != null) {
            if (ProcessoAdministrativoOcorrencia.Tipo.PARECER_DEFESA.value().equals(tipo)) {
                panel = new ParecerProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DECISAO_DEFESA.value().equals(tipo)) {
                panel = new DecisaoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.AUTO_PENALIDADE.value().equals(tipo)) {
                panel = new CadastroAutoPenalidadeViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.PARECER_RECURSO.value().equals(tipo)) {
                panel = new ParecerProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DECISAO_RECURSO.value().equals(tipo)) {
                panel = new DecisaoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DOCUMENTO_ANEXADO.value().equals(tipo)) {
                panel = new DocumentoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.CADASTRO_PROCESSO.value().equals(tipo)) {
                panel = new CadastroProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia);
            } else if (ProcessoAdministrativoOcorrencia.Tipo.RECURSO_CADASTRADO.value().equals(tipo)) {
                panel = new RecursoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DEFESA_PREVIA_CADASTRADA.value().equals(tipo)) {
                panel = new RecursoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DOCUMENTO_EXTRA_RECURSO_CADASTRADO.value().equals(tipo)) {
                panel = new RecursoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DOCUMENTO_EXTRA_DEFESA_CADASTRADO.value().equals(tipo)) {
                panel = new RecursoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.PAGAMENTO_FINANCEIRO.value().equals(tipo)) {
                panel = new PagamentoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.GERACAO_FINANCEIRO.value().equals(tipo)) {
                panel = new GeracaoBoletoProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia.getDocumento());
            } else if (ProcessoAdministrativoOcorrencia.Tipo.DESPACHO.value().equals(tipo)) {
                panel = new DescricaoOcorrenciaProcessoAdministrativoViewPanel("panel", processoAdministrativoOcorrencia);
            }
        }

        if (panel != null) {
            add(panel);
        } else {
            add(new EmptyPanel("panel").setVisible(false));
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(ProcessoAdministrativoOcorrenciaPanel.class, CSS_FILE)));
    }

}
