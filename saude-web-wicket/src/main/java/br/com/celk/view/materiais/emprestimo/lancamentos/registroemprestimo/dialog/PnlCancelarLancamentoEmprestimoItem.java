package br.com.celk.view.materiais.emprestimo.lancamentos.registroemprestimo.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCancelarLancamentoEmprestimoItem extends Panel{
    
    private Form form;
    private LancamentoEmprestimoItem lancamentoEmprestimoItem;
    private String motivo;
    
    private InputArea txaMotivo;
    
    public PnlCancelarLancamentoEmprestimoItem(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(txaMotivo = new InputArea("motivo", new PropertyModel<Long>(this, "motivo")));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    lancamentoEmprestimoItem.setMotivoCancelamento(motivo);
                    
                    if(Coalesce.asDouble(lancamentoEmprestimoItem.getQuantidadeDevolvida()) > 0D) {
                        lancamentoEmprestimoItem.setStatus(LancamentoEmprestimoItem.Status.DEVOLVIDO.value());
                        lancamentoEmprestimoItem.setQuantidadeCancelada(new Dinheiro(Coalesce.asDouble(lancamentoEmprestimoItem.getQuantidade()))
                                .subtrair(Coalesce.asDouble(lancamentoEmprestimoItem.getQuantidadeDevolvida())).doubleValue());
                        lancamentoEmprestimoItem.setQuantidade(Coalesce.asDouble(lancamentoEmprestimoItem.getQuantidadeDevolvida()));
                    } else {
                        lancamentoEmprestimoItem.setStatus(LancamentoEmprestimoItem.Status.CANCELADO.value());
                        lancamentoEmprestimoItem.setQuantidadeCancelada(Coalesce.asDouble(lancamentoEmprestimoItem.getQuantidade()));
                        lancamentoEmprestimoItem.setQuantidade(0D);
                    }
                    
                    lancamentoEmprestimoItem.setDataCancelamento(DataUtil.getDataAtual());
                    lancamentoEmprestimoItem.setUsuarioCancelamento(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario());
                    
                    onConfirmar(target, lancamentoEmprestimoItem);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if(txaMotivo.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, LancamentoEmprestimoItem lancamentoEmprestimoItem) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, LancamentoEmprestimoItem lancamentoEmprestimoItem){
        this.lancamentoEmprestimoItem = lancamentoEmprestimoItem;
        txaMotivo.limpar(target);
        target.add(txaMotivo);
    }
}