package br.com.celk.view.agenda.agendamento;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.agendamento.exame.dto.CadastroSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.SolicitacaoAgendamentoExameViewDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import ch.lambdaj.Lambda;

import java.util.List;


public class ValidaSolicitacaoAgendamentoPPIUtil {


    public static void validaSolicitacaoAgendamentoValidaPPICriaUpdate(SolicitacaoAgendamento solicitacaoAgendamento, Long codigoSolicitacao, TipoDeValidacao tipoDeValidacao, CadastroSolicitacaoAgendamentoDTOParam dtoParam, List<ExameRequisicao> exameRequisicao, List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames) throws DAOException, ValidacaoException {
        final boolean controlaCotaFinanceiraPPInocadastrotela611e346 = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("controlaCotaFinanceiraPPInocadastrotela611e346").equals(RepositoryComponentDefault.SIM);
        SolicitacaoAgendamento solAgendamento = solicitacaoAgendamento == null ? buscarSolicitacaoAgendamento(codigoSolicitacao): solicitacaoAgendamento;
        List<SolicitacaoAgendamentoExame> exames = null;

        switch (tipoDeValidacao) {
            case NOVO:
                if(controlaCotaFinanceiraPPInocadastrotela611e346) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).validaRegistraPPIAntesAgendamento(solAgendamento);
                }
                break;
            case VALIDA:
                if(controlaCotaFinanceiraPPInocadastrotela611e346) {
                    exames = Lambda.extract(dtoParam.getLstSolicitacaoAgendamentoExameViewDTO(), Lambda.on(SolicitacaoAgendamentoExameViewDTO.class).getSolicitacaoAgendamentoExame());
                    BOFactoryWicket.getBO(AgendamentoFacade.class).validaRegistraPPIAntesAgendamento(solAgendamento, exames);
                }
                break;
            case UPDATE:
                if(controlaCotaFinanceiraPPInocadastrotela611e346){
                        validaAlteracaoFormulario(dtoParam,solAgendamento,exameRequisicao,solicitacaoAgendamentoExames);
                }
                break;
            case DELETE:
                if(controlaCotaFinanceiraPPInocadastrotela611e346){
                        validaSeDevolvePPI(solAgendamento);
                }
                break;
        }
    }

    private static void validaAlteracaoFormulario(CadastroSolicitacaoAgendamentoDTOParam dtoParam, SolicitacaoAgendamento solicitacaoAgendamento, List<ExameRequisicao> exameRequisicao, List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames) throws DAOException, ValidacaoException {
        if((dtoParam.getUnidadeSolicitante().getCodigo() != solicitacaoAgendamento.getEmpresa().getCodigo()) ||
                (dtoParam.getTipoProcedimento() != solicitacaoAgendamento.getTipoProcedimento())){
            validaSeDevolvePPI(solicitacaoAgendamento);
        }else if(exameRequisicao.size() > 0 && solicitacaoAgendamentoExames.size() > 0){
            validaSeDevolvePPI(solicitacaoAgendamento);
        }
    }


    private static void validaSeDevolvePPI(SolicitacaoAgendamento solAgendamento) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(ExameFacade.class).estornarPpiSolicitacaoAgendamento(solAgendamento,solAgendamento.getDataCadastro());
    }

    private static SolicitacaoAgendamento buscarSolicitacaoAgendamento(Long codigoSolicitacaoAgendamento) {
        SolicitacaoAgendamento solicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_EMPRESA)).getProperties())
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TELEFONE))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CELULAR))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_EQUIPE, Equipe.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_EXAMES_SOLICITADOS_OBRIGATORIOS))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_REGULADO))
                .setId(codigoSolicitacaoAgendamento)
                .start().getVO();

        return solicitacaoAgendamento;
    }


     public enum TipoDeValidacao{
        VALIDA,
         NOVO,
         UPDATE,
         DELETE
     }
}
