package br.com.celk.view.materiais.estoque.inventario.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
public abstract class DlgCadastroInventarioBasico extends Window {

    private PnlCadastroInventarioBasico pnlCadastroInventarioBasico;

    public DlgCadastroInventarioBasico(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("cadastroInventario"));
        setInitialWidth(650);
        setInitialHeight(250);

        setContent(pnlCadastroInventarioBasico = new PnlCadastroInventarioBasico(getContentId()) {
            @Override
            public void onSalvar(AjaxRequestTarget target, Inventario inventario) throws DAOException, ValidacaoException {
                setInvetario(target, inventario);
                onFechar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    private void limparPanel(AjaxRequestTarget target) {
        pnlCadastroInventarioBasico.limpar(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgCadastroInventarioBasico.this.onFechar(target);
        limparPanel(target);
        close(target);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }

    public void onFechar(AjaxRequestTarget target) {}

    public abstract void setInvetario(AjaxRequestTarget target, Inventario inventario);
}
