package br.com.celk.view.materiais.dispensacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.ImpressaoEtiquetaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MedicamentoObmAmpp;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ImpressaoEtiquetaProdutoPage extends BasePage {

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Form form;
    private Produto produto;
    private String grupoEstoque;
    private Long quantidadeEtiquetas;
    private Double quantidadeProduto;
    private LongField txtQuantidadeEtiquetas;
    private DoubleField txtQuantidadeProduto;
    private AjaxReportLink btnImprimir;
    private AbstractAjaxButton btnImprimirZebra;
    private MedicamentoObmAmpp medicamentoObmAmpp;

    private Long tipoImpressora;

    private PnlSaidaLote pnlSaidaLote;

    public ImpressaoEtiquetaProdutoPage() throws DAOException {
        tipoImpressora = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tipoImpressoraEtiqueta");
        init();
    }

    private void init() {

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                pnlSaidaLote.limpar(target);
            }
        });
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(pnlSaidaLote = new PnlSaidaLote("grupoEstoque"));
        pnlSaidaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlSaidaLote.setValidarLotesVencidos(true);
        pnlSaidaLote.registerEvents();
        form.add(txtQuantidadeEtiquetas = new LongField("quantidadeEtiquetas"));
        txtQuantidadeEtiquetas.setVMax(999L);
        txtQuantidadeEtiquetas.addAjaxUpdateValue();

        form.add(txtQuantidadeProduto = new DoubleField("quantidadeProduto"));
        txtQuantidadeProduto.setVMax(99999D);
        txtQuantidadeProduto.addAjaxUpdateValue();
        txtQuantidadeProduto.add(new Tooltip().setText("quantidadeReferenteProdutoImpressaEtiqueta"));


        form.add(btnImprimir = new AjaxReportLink("btnImprimir") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                MovimentoGrupoEstoqueItemDTO dto;

                if (produto == null) {
                    throw new ValidacaoException(BundleManager.getString("informeProduto"));
                }

                if (RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                    if (pnlSaidaLote.getLoteSelecionado() != null) {
                        dto = pnlSaidaLote.getLoteSelecionado();
                        dto.setProduto(produto);
                    } else {
                        throw new ValidacaoException(BundleManager.getString("informeLote"));
                    }
                } else {
                    dto = new MovimentoGrupoEstoqueItemDTO();
                    dto.setProduto(produto);
                    dto.setGrupoEstoque("0");
                }

                if (quantidadeEtiquetas == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
                }

                if(RepositoryComponentDefault.TipoImpressoraEtiqueta.ZEBRA33x21.value().equals(tipoImpressora)){
                    if(quantidadeProduto == null){
                        throw new ValidacaoException(BundleManager.getString("informeQuantidadeProduto"));
                    }
                }

                List<Long> lstCodigoBarrasProduto = BOFactoryWicket.getBO(ProdutoFacade.class).gerarCodigoBarrasProduto(dto, quantidadeEtiquetas, quantidadeProduto);

                ImpressaoEtiquetaDTOParam param = new ImpressaoEtiquetaDTOParam();
                param.setLstCodigoBarrasProduto(lstCodigoBarrasProduto);
                param.setLote(dto.getGrupoEstoque());
                param.setValidade(dto.getDataValidade());
                param.setQuantidadeEtiquetas(quantidadeEtiquetas);
                param.setProduto(produto);
                param.setTipoImpressora(tipoImpressora);
                param.setQuantidadeProduto(quantidadeProduto);

                if (produto.getMedicamentoObmAmpp() != null) {
                    medicamentoObmAmpp = LoadManager.getInstance(MedicamentoObmAmpp.class)
                            .addProperties(new HQLProperties(MedicamentoObmAmpp.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(MedicamentoObmAmpp.PROP_CODIGO, produto.getMedicamentoObmAmpp().getCodigo()))
                            .start()
                            .getVO();

                    if (medicamentoObmAmpp != null && medicamentoObmAmpp.getRegistroSanitarioAmpp() != null) {
                        param.setNumeroRegistroSanitario(medicamentoObmAmpp.getRegistroSanitarioAmpp());
                    }
                }
                return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioImpressaoEtiquetaProduto(param);
            }
        });
        btnImprimir.setOutputMarkupPlaceholderTag(true);
        form.add(btnImprimirZebra = new AbstractAjaxButton("btnImprimirZebra") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {

                MovimentoGrupoEstoqueItemDTO dto;

                if (produto == null) {
                    throw new ValidacaoException(BundleManager.getString("informeProduto"));
                }

                if (RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                    if (pnlSaidaLote.getLoteSelecionado() != null) {
                        dto = pnlSaidaLote.getLoteSelecionado();
                        dto.setProduto(produto);
                    } else {
                        throw new ValidacaoException(BundleManager.getString("informeLote"));
                    }
                } else {
                    dto = new MovimentoGrupoEstoqueItemDTO();
                    dto.setProduto(produto);
                    dto.setGrupoEstoque("0");
                }

                if (quantidadeEtiquetas == null) {
                    throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
                }
            }
        });
        btnImprimirZebra.setOutputMarkupPlaceholderTag(true);


        btnImprimir.setVisible(true);
        btnImprimirZebra.setVisible(false);

        add(form);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("impressaoEtiquetaProdutos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProduto;
    }

}
