package br.com.celk.view.unidadesaude.esus.relatorios;

import br.com.celk.component.checkgroup.CheckBoxGroup;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.siab.relatorios.RelatorioAcompanhamentoCadastroFamiliasPage;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioCriancasDTOParam;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class RelatorioCriancasPage extends RelatorioPage<RelatorioCriancasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private CheckBoxGroup checkBoxGroup;
    //private List<Long> tiposUnidade = new ArrayList<>();

    private static final String CSS_FILE = "RelatorioCriancasPage.css";
    private CssResourceReference cssResourceReference;


    @Override
    public void init(Form form) {
        this.cssResourceReference = new CssResourceReference(RelatorioCriancasPage.class, CSS_FILE);
        RelatorioCriancasDTOParam proxy = on(RelatorioCriancasDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        form.add(checkBoxGroup = new CheckBoxGroup("tiposUnidade", TipoEstabelecimento.values(), !SessaoAplicacaoImp.getInstance().<Usuario>getUsuario().isNivelAdminOrMaster()) {
            @Override
            public CssResourceReference getCssResourceReference() {
                return RelatorioCriancasPage.this.cssResourceReference;
            }
        });

        try {
            form.add(dropDownArea = createDropDownArea());
            form.add(dropDownMicroArea = new DropDown(path(proxy.getEquipeMicroArea())));
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        if (checkBoxGroup.isSelectionEmpty())
            throw new ValidacaoException(bundle("msgInformeTipoUnidade"));

        List empresas = (List) autoCompleteConsultaEmpresa.getComponentValue();
        if ((CollectionUtils.isEmpty(empresas) || empresas.size() > 1) && dropDownArea.getComponentValue() == null) {
            throw new ValidacaoException(bundle("msgInformeEstabelecimentoOuArea"));
        }
    }

    @Override
    public Class<RelatorioCriancasDTOParam> getDTOParamClass() {
        return RelatorioCriancasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioCriancasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioCriancas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioCriancas");
    }

    private DropDown<EquipeArea> createDropDownArea() throws DAOException, ValidacaoException {
        DropDown<EquipeArea> dropDown = new DropDown("equipeArea");

        LoadManager loadManager = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO));

        final List<Long> empresas = new ArrayList();
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            empresas.add(((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getCodigo());
        } else {
            if (!isActionPermitted(Permissions.EMPRESA)) {
                try {
                    empresas.addAll(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario()));
                } catch (SGKException ex) {
                    Logger.getLogger(RelatorioAcompanhamentoCadastroFamiliasPage.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }

        if (!empresas.isEmpty()) {
            loadManager.addInterceptor(new LoadInterceptor() {
                @Override
                public void customHQL(HQLHelper hql, String alias) {
                    HQLHelper exists = hql.getNewInstanceSubQuery();
                    exists.addToSelect("1");
                    exists.addToFrom("EquipeMicroArea ema JOIN ema.equipeProfissional ep JOIN ep.equipe e JOIN e.empresa emp");
                    exists.addToWhereWhithAnd("ema.equipeArea.codigo = " + alias + ".codigo");
                    exists.addToWhereWhithAnd("emp.codigo in ", empresas);
                    hql.addToWhereWhithAnd("exists(" + exists.getQuery() + ")");
                }
            });
        }

        List<EquipeArea> list = loadManager.start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }
                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }
}
