package br.com.celk.view.materiais.tipoviamedicamento.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaTipoViaMedicamento extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoViaMedicamento.PROP_DESCRICAO), QueryParameter.CONSULTA_LIKED));
        filterProperties.put(BundleManager.getString("referencia"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoViaMedicamento.PROP_REFERENCIA), QueryParameter.IGUAL));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("referencia"), VOUtils.montarPath(TipoViaMedicamento.PROP_REFERENCIA));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(TipoViaMedicamento.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return TipoViaMedicamento.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(TipoViaMedicamento.class).getProperties());
    }

}
