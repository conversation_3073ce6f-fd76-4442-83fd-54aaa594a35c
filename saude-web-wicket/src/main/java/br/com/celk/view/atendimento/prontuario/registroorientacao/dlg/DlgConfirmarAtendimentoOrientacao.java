package br.com.celk.view.atendimento.prontuario.registroorientacao.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarAtendimentoOrientacao extends Window {

    private PnlConfirmarAtendimentoOrientacao pnlConfirmarAtendimentoOrientacao;

    public DlgConfirmarAtendimentoOrientacao(String id) throws ValidacaoException {
        super(id);
        init();
    }

    private void init() throws ValidacaoException {
        setTitle(BundleManager.getString("confirmarAtendimento"));

        setInitialWidth(800);
        setInitialHeight(120);

        setResizable(false);

        setContent(pnlConfirmarAtendimentoOrientacao = new PnlConfirmarAtendimentoOrientacao(getContentId()) {
            @Override
            public void onOk(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                try {
                    validarTipoAtendimento(pnlConfirmarAtendimentoOrientacao.getTipoAtendimento());
                    close(target);
                    DlgConfirmarAtendimentoOrientacao.this.onOk(target, pnlConfirmarAtendimentoOrientacao.getTipoAtendimento(),
                            pnlConfirmarAtendimentoOrientacao.getProfissional(),
                            pnlConfirmarAtendimentoOrientacao.getDto(), pnlConfirmarAtendimentoOrientacao.getTipoProcedimentoAtendimento(), 
                            pnlConfirmarAtendimentoOrientacao.getCondicaoPaciente(),
                            pnlConfirmarAtendimentoOrientacao.getMotivoConsulta());
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, this, ex);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onOk(AjaxRequestTarget target, TipoAtendimento tipoAtendimento, Profissional profissional, ConsultaUsuarioCadsusDTO dto,
                              TipoProcedimentoAtendimento tipoProcedimentoAtendimento, String condicaoPaciente, String motivoConsulta) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) {
    }

    public void validarTipoAtendimento(TipoAtendimento ta) throws ValidacaoException {
        if (ta == null) {
            throw new ValidacaoException(bundle("magValidarTipoAtendimento"));
        }
    }

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        pnlConfirmarAtendimentoOrientacao.limpar(target);
        pnlConfirmarAtendimentoOrientacao.setPaciente(dto.getUsuarioCadsus().getNomeSocial());
        pnlConfirmarAtendimentoOrientacao.setDto(dto);
        pnlConfirmarAtendimentoOrientacao.setResourceImage(target, ImagemAvatarHelper.carregarAvatarResource(dto.getUsuarioCadsus()));
        
        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgConfirmarAtendimentoOrientacao.this.onFechar(target);
        close(target);
    }
}
