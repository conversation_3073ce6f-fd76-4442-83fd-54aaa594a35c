package br.com.celk.view.atendimento.buscaativacaps;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.buscaativacaps.dialog.DlgCadastroOcorrenciaBuscaAtivaCaps;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.cva.animal.dialog.DlgCadastroAnimal;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.BuscaAtivaCaps;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.OcorrenciaBuscaAtivaCaps;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

public class ConsultaBuscaAtivaCapsPage extends ConsultaPage<BuscaAtivaCaps, List<BuilderQueryCustom.QueryParameter>> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DropDown<Long> dropDownSituacao;
    private DateChooser dateChooserDataInicial;
    private DateChooser dateChooserDataFinal;
    private Empresa empresa;
    private Profissional profissional;
    private Long situacao;
    private Date dataInicial;
    private Date dataFinal;
    private DlgConfirmacaoOk dlgBuscaAtivaCancelada;
    private DlgConfirmacaoOk dlgBuscaAtivaConcluida;
    private DlgCadastroOcorrenciaBuscaAtivaCaps dlgCadastroOcorrenciaBuscaAtivaCaps;

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaBuscaAtivaCaps");
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));

        form.add(getDropDownSituacao());

        form.add(dateChooserDataInicial = new DateChooser("dataInicial"));
        form.add(dateChooserDataFinal = new DateChooser("dataFinal"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        BuscaAtivaCaps proxy = on(BuscaAtivaCaps.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("diasEspera"), proxy.getDiasEspera()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome()));
        columns.add(createSortableColumn(bundle("ultimaOcorrencia"), proxy.getDataOcorrencia()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<BuscaAtivaCaps>() {
            @Override
            public void customizeColumn(BuscaAtivaCaps rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<BuscaAtivaCaps>() {
                    @Override
                    public void action(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroBuscaAtivaCapsPage(buscaAtivaCaps));
                    }
                }).setEnabled(BuscaAtivaCaps.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<BuscaAtivaCaps>() {
                    @Override
                    public void action(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) throws ValidacaoException, DAOException {
                        buscaAtivaCaps.setStatus(BuscaAtivaCaps.Status.CANCELADO.value());
                        buscaAtivaCaps.setDataCancelamento(DataUtil.getDataAtual());
                        buscaAtivaCaps.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                        BOFactoryWicket.save(buscaAtivaCaps);
                        viewDlgBuscaAtivaCancelada(target, buscaAtivaCaps);
                    }
                }).setEnabled(BuscaAtivaCaps.Status.PENDENTE.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<BuscaAtivaCaps>() {
                    @Override
                    public void action(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesBuscaAtivaCapsPage(buscaAtivaCaps));
                    }
                }).setTitleBundleKey("detalhes");

                addAction(ActionType.OCORRENCIA, rowObject, new IModelAction<BuscaAtivaCaps>() {
                    @Override
                    public void action(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) throws ValidacaoException, DAOException {
                        viewDlgRegistroOcorrencia(target, buscaAtivaCaps);
                    }
                }).setEnabled(BuscaAtivaCaps.Status.PENDENTE.value().equals(rowObject.getStatus()) ||
                        BuscaAtivaCaps.Status.ANDAMENTO.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONCLUIR, rowObject, new IModelAction<BuscaAtivaCaps>() {
                    @Override
                    public void action(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) throws ValidacaoException, DAOException {
                        buscaAtivaCaps.setStatus(BuscaAtivaCaps.Status.CONCLUIDO.value());
                        buscaAtivaCaps.setDataConclusao(DataUtil.getDataAtual());
                        buscaAtivaCaps.setUsuarioConclusao(SessaoAplicacaoImp.getInstance().getUsuario());
                        BOFactoryWicket.save(buscaAtivaCaps);
                        viewDlgBuscaAtivaConfirmada(target, buscaAtivaCaps);
                    }
                }).setEnabled(BuscaAtivaCaps.Status.PENDENTE.value().equals(rowObject.getStatus()) ||
                        BuscaAtivaCaps.Status.ANDAMENTO.value().equals(rowObject.getStatus()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return BuscaAtivaCaps.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(BuscaAtivaCaps.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(BuscaAtivaCaps.PROP_DATA_CADASTRO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_EMPRESA, BuilderQueryCustom.QueryParameter.IGUAL, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_PROFISSIONAL, BuilderQueryCustom.QueryParameter.IGUAL, profissional));

        if (BuscaAtivaCaps.Status.PENDENTE.value().equals(situacao)) {
            List<Long> statusList = new ArrayList<>();
            statusList.add(BuscaAtivaCaps.Status.PENDENTE.value());
            statusList.add(BuscaAtivaCaps.Status.ANDAMENTO.value());

            parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, statusList));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_STATUS, BuilderQueryCustom.QueryParameter.IGUAL, situacao));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_DATA_CADASTRO, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, Data.adjustRangeHour(dataInicial).getDataInicial()));
        parameters.add(new QueryCustom.QueryCustomParameter(BuscaAtivaCaps.PROP_DATA_CADASTRO, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, Data.adjustRangeHour(dataFinal).getDataFinal()));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroBuscaAtivaCapsPage.class;
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(BuscaAtivaCaps.Status.PENDENTE.value(), BundleManager.getString("pendente"));
            dropDownSituacao.addChoice(BuscaAtivaCaps.Status.CANCELADO.value(), BundleManager.getString("cancelado"));
            dropDownSituacao.addChoice(BuscaAtivaCaps.Status.CONCLUIDO.value(), BundleManager.getString("concluido"));
        }
        return dropDownSituacao;
    }

    private void viewDlgBuscaAtivaCancelada(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) {
        if(dlgBuscaAtivaCancelada == null) {
            addModal(target, dlgBuscaAtivaCancelada = new DlgConfirmacaoOk(newModalId(), bundle("buscaAtivaCapsCancelada")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    getPageableTable().update(target);
                    close(target);
                }
            });
        }

        dlgBuscaAtivaCancelada.show(target);
    }

    private void viewDlgBuscaAtivaConfirmada(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) {
        if(dlgBuscaAtivaConcluida == null) {
            addModal(target, dlgBuscaAtivaConcluida = new DlgConfirmacaoOk(newModalId(), bundle("buscaAtivaConcluida")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    getPageableTable().update(target);
                    close(target);
                }
            });
        }

        dlgBuscaAtivaConcluida.show(target);
    }

    private void viewDlgRegistroOcorrencia(AjaxRequestTarget target, BuscaAtivaCaps buscaAtivaCaps) {
        if (dlgCadastroOcorrenciaBuscaAtivaCaps == null) {
            addModal(target, dlgCadastroOcorrenciaBuscaAtivaCaps = new DlgCadastroOcorrenciaBuscaAtivaCaps(newModalId(), buscaAtivaCaps) {});
            dlgCadastroOcorrenciaBuscaAtivaCaps.add(new ICadastroListener<OcorrenciaBuscaAtivaCaps>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, OcorrenciaBuscaAtivaCaps ocorrenciaBuscaAtivaCaps) throws ValidacaoException, DAOException {
                    BuscaAtivaCaps buscaAtivaCapsAux = LoadManager.getInstance(BuscaAtivaCaps.class).setId(buscaAtivaCaps.getCodigo()).start().getVO();

                    if (BuscaAtivaCaps.Status.PENDENTE.value().equals(buscaAtivaCapsAux.getStatus())) {
                        buscaAtivaCapsAux.setDataAndamento(DataUtil.getDataAtual());
                    }

                    buscaAtivaCapsAux.setStatus(BuscaAtivaCaps.Status.ANDAMENTO.value());
                    buscaAtivaCapsAux.setDataOcorrencia(DataUtil.getDataAtual());
                    BOFactoryWicket.save(buscaAtivaCapsAux);

                    getPageableTable().update(target);
                }
            });

        }

        dlgCadastroOcorrenciaBuscaAtivaCaps.setBuscaAtivaCaps(buscaAtivaCaps);
        dlgCadastroOcorrenciaBuscaAtivaCaps.show(target);
    }
}
