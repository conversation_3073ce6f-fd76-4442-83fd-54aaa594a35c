package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.RadioGroup;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RequerimentoBaixaResponsabilidadeTecnicaExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoBaixaResponsabilidadeDTO> form;
    private RequerimentoBaixaResponsabilidade requerimentoBaixaResponsabilidade;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoSolicitacao tipoSolicitacao;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavelTecnico;
    private DisabledInputField txtRazaoSocial;
    private DisabledInputField<String> txtEnderecoEstabelecimento;
    private DisabledInputField<String> txtCnpjCpfFormatado;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtProtocolo;
    private DisabledInputField<String> txtDescricaoAtividadeEstabelecimento;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgImpressaoObjectMulti;

    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private boolean enabled;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;

    private Class classReturn;
    private SimpleSelectionTable<EstabelecimentoResponsavelTecnicoTableDTO> tblEstabelecimentos;
    private List<EstabelecimentoResponsavelTecnicoTableDTO> estabelecimentoResponsavelTecnicoDTOList;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public RequerimentoBaixaResponsabilidadeTecnicaExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoBaixaResponsabilidadeTecnicaExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoBaixaResponsabilidade(requerimentoVigilancia);
        if(RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())){
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoBaixaResponsabilidadeDTO proxy = on(RequerimentoBaixaResponsabilidadeDTO.class);

        getForm().add(autoCompleteConsultaResponsavelTecnico = (AutoCompleteConsultaResponsavelTecnico) new AutoCompleteConsultaResponsavelTecnico(path(proxy.getRequerimentoBaixaResponsabilidade().getResponsavelTecnico()), true).setLabel(Model.of(bundle("responsavelTecnico"))));
        autoCompleteConsultaResponsavelTecnico.setEnabled(enabled && getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getCodigo() == null);
        try {
            autoCompleteConsultaResponsavelTecnico.setFiltrarEstabelecimento(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        getForm().add(tblEstabelecimentos = new SimpleSelectionTable("tblEstabelecimentos", getColumns(), getCollectionProvider()));
        tblEstabelecimentos.addSelectionAction(new ISelectionAction<EstabelecimentoResponsavelTecnicoTableDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, EstabelecimentoResponsavelTecnicoTableDTO estabelecimentoResponsavelTecnicoTableDTO) {
                atualizarEstabelecimento(target, estabelecimentoResponsavelTecnicoTableDTO);
            }
        });
        tblEstabelecimentos.populate();
        tblEstabelecimentos.setEnabled(enabled && getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getCodigo() == null);
        if (CollectionUtils.isNotNullEmpty(estabelecimentoResponsavelTecnicoDTOList)) {
            tblEstabelecimentos.setSelectedObject(estabelecimentoResponsavelTecnicoDTOList.get(0));
        }

        {// Estabelecimento
            getForm().add(txtEnderecoEstabelecimento = new DisabledInputField<>(path(proxy.getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().getEnderecoFormatado())));
            getForm().add(txtRazaoSocial = new DisabledInputField(path(proxy.getRequerimentoBaixaResponsabilidade().getEstabelecimento().getRazaoSocial())));
            getForm().add(txtCnpjCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoBaixaResponsabilidade().getEstabelecimento().getCnpjCpfFormatado())));
            getForm().add(txtFantasia = new DisabledInputField<>(path(proxy.getRequerimentoBaixaResponsabilidade().getEstabelecimento().getFantasia())));
            getForm().add(txtProtocolo = new DisabledInputField<>(path(proxy.getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().getProtocoloFormatado())));
            getForm().add(txtDescricaoAtividadeEstabelecimento = new DisabledInputField<>(path(proxy.getRequerimentoBaixaResponsabilidade().getEstabelecimento().getAtividadeEstabelecimento().getDescricao())));

        }

        getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia(), enabled));

        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));
        pnlDadosComumRequerimentoVigilancia.getContainerSetorVigilancia().setVisible(false);

        {
            //Anexo
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
            //Fim Anexo
        }

        getForm().add(new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(getForm());
        autoCompleteConsultaResponsavelTecnico.add(new ConsultaListener<ResponsavelTecnico>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ResponsavelTecnico object) {
                updateNotification(target);
                getSession().getFeedbackMessages().clear();
                updateNotificationPanel(target);

                List<EstabelecimentoResponsavelTecnico> estabelecimentoResponsavelTecnicoList = LoadManager.getInstance(EstabelecimentoResponsavelTecnico.class)
                        .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, object))
                        .start().getList();

                estabelecimentoResponsavelTecnicoDTOList = new ArrayList<EstabelecimentoResponsavelTecnicoTableDTO>();

                for (EstabelecimentoResponsavelTecnico estabelecimentoResponsavelTecnico : estabelecimentoResponsavelTecnicoList) {
                    EstabelecimentoResponsavelTecnicoTableDTO dto = new EstabelecimentoResponsavelTecnicoTableDTO();
                    dto.setEstabelecimento(estabelecimentoResponsavelTecnico.getEstabelecimento());
                    dto.setResponsavelTecnico(estabelecimentoResponsavelTecnico.getResponsavelTecnico());
                    estabelecimentoResponsavelTecnicoDTOList.add(dto);
                }


                for (EstabelecimentoSetores estabelecimentoSetores : getSetoresList(object)) {
                    EstabelecimentoResponsavelTecnicoTableDTO dto = new EstabelecimentoResponsavelTecnicoTableDTO();
                    dto.setEstabelecimentoSetores(estabelecimentoSetores);
                    estabelecimentoResponsavelTecnicoDTOList.add(dto);
                }

                if (CollectionUtils.isEmpty(estabelecimentoResponsavelTecnicoDTOList)) {
                    warn(target, bundle("msgEstabelecimentoNaoEncontradoResponsavelTecnico"));
                    updateNotificationPanel(target);
                    updateNotification(target);
                    getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setVigilanciaEndereco(null);
                    target.add(txtEnderecoEstabelecimento);
                    target.add(txtCnpjCpfFormatado);
                    target.add(txtFantasia);
                    target.add(txtProtocolo);
                    target.add(txtDescricaoAtividadeEstabelecimento);
                    autoCompleteConsultaResponsavelTecnico.limpar(target);
                    target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaResponsavelTecnico.getTxtDescricao().getTextField()));
                } else {
                    tblEstabelecimentos.update(target);
                    if(estabelecimentoResponsavelTecnicoDTOList.size() == 1){
                        tblEstabelecimentos.setSelectedObject(estabelecimentoResponsavelTecnicoDTOList.get(0));
                        atualizarEstabelecimento(target, estabelecimentoResponsavelTecnicoDTOList.get(0));
                    }
                }

                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaResponsavelTecnico.add(new RemoveListener<ResponsavelTecnico>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ResponsavelTecnico object) {
                autoCompleteConsultaResponsavelTecnico.limpar(target);
                getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().setEstabelecimento(null);
                getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setVigilanciaEndereco(null);
                target.add(txtEnderecoEstabelecimento);
                target.add(txtRazaoSocial);
                target.add(txtCnpjCpfFormatado);
                target.add(txtFantasia);
                target.add(txtProtocolo);
                target.add(txtDescricaoAtividadeEstabelecimento);
                getSession().getFeedbackMessages().clear();
                updateNotification(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                tblEstabelecimentos.clearSelection(target);
                ((RadioGroup) tblEstabelecimentos.getBody()).setModelObject(null);
                tblEstabelecimentos.update(target);
                tblEstabelecimentos.populate(target);
                estabelecimentoResponsavelTecnicoDTOList.clear();
            }
        });
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();

        EstabelecimentoResponsavelTecnicoTableDTO proxy = on(EstabelecimentoResponsavelTecnicoTableDTO.class);

        columns.add(createColumn(bundle("estabelecimento"), proxy.getRazaoSocialEstabelecimento()));
        columns.add(createColumn(bundle("setor"), proxy.getEstabelecimentoSetores().getDescricaoSetor()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return estabelecimentoResponsavelTecnicoDTOList;
            }
        };
    }

    private Form<RequerimentoBaixaResponsabilidadeDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoBaixaResponsabilidadeDTO()));
            if (requerimentoBaixaResponsabilidade != null) {
                form.getModel().getObject().setRequerimentoBaixaResponsabilidade(requerimentoBaixaResponsabilidade);
            } else {
                form.getModel().getObject().setRequerimentoBaixaResponsabilidade(new RequerimentoBaixaResponsabilidade());
                form.getModel().getObject().getRequerimentoBaixaResponsabilidade().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if(getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getCodigo() == null && tblEstabelecimentos.getSelectedObject() == null){
            throw new ValidacaoException(bundle("msgSelecioneEstabelecimento"));
        }
        if(getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().getCpfSolicitante() == null
                && getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().getRgSolicitante() == null){
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }

        getForm().getModel().getObject().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        atualizarDadosComuns();
        getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        final RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoBaixaResponsabilidade(getForm().getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");

        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        gerarBoleto(target, rv, configuracaoVigilancia);

        addModal(target, dlgImpressaoObjectMulti = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);
                param.setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport dataReportLandscape = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                RelatorioAutorizacaoBaixaResponsabilidadeDTOParam paramBaixa = new RelatorioAutorizacaoBaixaResponsabilidadeDTOParam();
                paramBaixa.setCodigoRequerimentoVigilancia(object.getCodigo());
                paramBaixa.setDescricaoSetor(object.getSetorFormatado());

                List<IReport> lstDataReport = new ArrayList<>();
                if (dataReportLandscape != null && dataReportLandscape.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReportLandscape);
                }

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgImpressaoObjectMulti.show(target, rv);
    }

    private void gerarBoleto(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia, ConfiguracaoVigilancia configuracaoVigilancia) throws ValidacaoException, DAOException {
        String boletoBase64RequerimentoExterno = FinanceiroVigilanciaHelper.getBoletoBase64RequerimentoExterno(requerimentoVigilancia, configuracaoVigilancia);

        if (boletoBase64RequerimentoExterno != null) {
            ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64RequerimentoExterno);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("baixaResponsabilidadeTecnica");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaResponsavelTecnico.getTxtDescricao().getTextField())));
    }

    private void carregarRequerimentoBaixaResponsabilidade(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoBaixaResponsabilidade = LoadManager.getInstance(RequerimentoBaixaResponsabilidade.class)
                    .addProperties(new HQLProperties(RequerimentoBaixaResponsabilidade.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoBaixaResponsabilidade.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(EstabelecimentoSetores.class, RequerimentoBaixaResponsabilidade.PROP_ESTABELECIMENTO_SETORES).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoBaixaResponsabilidade.PROP_ESTABELECIMENTO_SETORES, EstabelecimentoSetores.PROP_ESTABELECIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoBaixaResponsabilidade.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoBaixaResponsabilidade.setRequerimentoVigilancia(requerimentoVigilancia);

            if(requerimentoBaixaResponsabilidade != null){
                EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, requerimentoBaixaResponsabilidade.getEstabelecimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                        .start().getVO();

                if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                    getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
                }
            }
            carregarAnexos(requerimentoVigilancia);

            estabelecimentoResponsavelTecnicoDTOList = new ArrayList();
            EstabelecimentoResponsavelTecnicoTableDTO dto = new EstabelecimentoResponsavelTecnicoTableDTO();
            if (requerimentoBaixaResponsabilidade.getEstabelecimentoSetores() == null) {
                dto.setEstabelecimento(requerimentoBaixaResponsabilidade.getEstabelecimento());
                dto.setResponsavelTecnico(requerimentoBaixaResponsabilidade.getResponsavelTecnico());
                estabelecimentoResponsavelTecnicoDTOList.add(dto);
            } else {
                dto.setEstabelecimentoSetores(requerimentoBaixaResponsabilidade.getEstabelecimentoSetores());
                estabelecimentoResponsavelTecnicoDTOList.add(dto);
            }

        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv){
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for(RequerimentoVigilanciaAnexo rva : list){
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void updateNotification(AjaxRequestTarget target) {
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(RequerimentoBaixaResponsabilidadeTecnicaExternoPage.this);
        if (findNotificationPanel != null) {
            getSession().getFeedbackMessages().clear();
            if (target != null) {
                findNotificationPanel.updateNotificationPanel(target);
            }
        }
    }

    private void atualizarEstabelecimento(AjaxRequestTarget target, EstabelecimentoResponsavelTecnicoTableDTO estabelecimentoResponsavelTecnicoTableDTO){
        if (CollectionUtils.isNotNullEmpty(estabelecimentoResponsavelTecnicoDTOList)) {
            Estabelecimento estabelecimento = null;
            if(estabelecimentoResponsavelTecnicoTableDTO.getEstabelecimento() != null){
                estabelecimento = estabelecimentoResponsavelTecnicoTableDTO.getEstabelecimento();
            } else {
                estabelecimento = estabelecimentoResponsavelTecnicoTableDTO.getEstabelecimentoSetores().getEstabelecimento();
            }
            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();

            if (estabelecimento != null) {
                updateNotification(target);
                getSession().getFeedbackMessages().clear();
                updateNotificationPanel(target);
                getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().setEstabelecimento(estabelecimento);
                getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().setEstabelecimentoSetores(estabelecimentoResponsavelTecnicoTableDTO.getEstabelecimentoSetores());
                if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                    getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
                }

                if (estabelecimento.getVigilanciaEndereco() != null && estabelecimento.getVigilanciaEndereco().getCodigo() != null) {
                    VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(estabelecimento.getVigilanciaEndereco().getCodigo()).start().getVO();
                    form.getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
                }

                target.add(txtEnderecoEstabelecimento);
                target.add(txtRazaoSocial);
                target.add(txtCnpjCpfFormatado);
                target.add(txtFantasia);
                target.add(txtProtocolo);
                target.add(txtDescricaoAtividadeEstabelecimento);
            }
        }
    }

    private List<EstabelecimentoSetores> getSetoresList(ResponsavelTecnico responsavelTecnico) {
        return LoadManager.getInstance(EstabelecimentoSetores.class)
                .addProperties(new HQLProperties(EstabelecimentoSetores.class).getProperties())
                .addProperties(new HQLProperties(ResponsavelTecnico.class, EstabelecimentoSetores.PROP_RESPONSAVEL_TECNICO).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoSetores.PROP_ESTABELECIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoSetores.PROP_STATUS, RepositoryComponentDefault.ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoSetores.PROP_RESPONSAVEL_TECNICO, responsavelTecnico))
                .start().getList();
    }

    @Override
    public Permissions getAction() {
        return Permissions.BAIXA_RESPONSABILIDADE_TECNICA;
    }
    
    private void atualizarDadosComuns(){
        getForm().getModel().getObject().getRequerimentoBaixaResponsabilidade().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}