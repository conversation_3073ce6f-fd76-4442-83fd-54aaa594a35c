package br.com.celk.view.basico.unidade;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaAtendimentoPrestado;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaEsferaAdministrativa;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaNivelHierarquia;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaTipoConvenio;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaTurnoAtendimento;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EmpresaCaracterizacaoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaAtendimentoPrestado;
import br.com.ksisolucoes.vo.basico.EmpresaAtendimentoPrestadoPK;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrestado;
import br.com.ksisolucoes.vo.prontuario.basico.TipoConvenio;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroCaracterizacaoUnidadePage extends BasePage {

    private Form<Empresa> form;
    private Form containerAtendimentoPrestado;

    private final Empresa rowObject;
    private List<EmpresaAtendimentoPrestado> lstAtendimentoPrestado;

    private AtendimentoPrestado atendimentoPrestado;
    private TipoConvenio tipoConvenio;

    private AutoCompleteConsultaAtendimentoPrestado autoCompleteConsultaAtendimentoPrestado;
    private AutoCompleteConsultaTipoConvenio autoCompleteConsultaTipoConvenio;

    private Table<EmpresaAtendimentoPrestado> tblAtendimentoPrestado;

    public CadastroCaracterizacaoUnidadePage(Empresa rowObject) {
        this.rowObject = rowObject;
    }

    @Override
    protected void postConstruct() {
        form = new Form<Empresa>("form", new CompoundPropertyModel(rowObject));

        lstAtendimentoPrestado = LoadManager.getInstance(EmpresaAtendimentoPrestado.class)
                .addProperties(new HQLProperties(EmpresaAtendimentoPrestado.class).getProperties())
                .addProperty(VOUtils.montarPath(EmpresaAtendimentoPrestado.PROP_ID, EmpresaAtendimentoPrestadoPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(EmpresaAtendimentoPrestado.PROP_ID, EmpresaAtendimentoPrestadoPK.PROP_ATENDIMENTO_PRESTADO, AtendimentoPrestado.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(EmpresaAtendimentoPrestado.PROP_ID, EmpresaAtendimentoPrestadoPK.PROP_TIPO_CONVENIO, TipoConvenio.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaAtendimentoPrestado.PROP_ID, EmpresaAtendimentoPrestadoPK.PROP_EMPRESA),
                                rowObject.getCodigo()))
                .start().getList();

        form.add(new InputField("descricao")
                .setEnabled(false));
        form.add(new AutoCompleteConsultaEsferaAdministrativa("esferaAdministrativa"));
        form.add(new AutoCompleteConsultaNivelHierarquia("nivelHierarquia"));
        form.add(getDropDownFluxoClientela("codigoFluxoClientela"));
        form.add(new AutoCompleteConsultaTurnoAtendimento("turnoAtendimento"));

        containerAtendimentoPrestado = new Form("containerAtendimentoPrestado");
        containerAtendimentoPrestado.add(autoCompleteConsultaAtendimentoPrestado = new AutoCompleteConsultaAtendimentoPrestado("atendimentoPrestado", new PropertyModel<AtendimentoPrestado>(this, "atendimentoPrestado")));
        containerAtendimentoPrestado.add(autoCompleteConsultaTipoConvenio = new AutoCompleteConsultaTipoConvenio("tipoConvenio", new PropertyModel<TipoConvenio>(this, "tipoConvenio")));
        containerAtendimentoPrestado.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        form.add(containerAtendimentoPrestado);

        form.add(tblAtendimentoPrestado = new Table("tblAtendimentoPrestado", getColumns(), getCollectionProvider()));
        tblAtendimentoPrestado.populate();

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }

        }));

        add(form);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        EmpresaCaracterizacaoDTO dto = new EmpresaCaracterizacaoDTO();

        dto.setCodigoEmpresa(form.getModel().getObject().getCodigo());
        dto.setEsferaAdministrativa(form.getModel().getObject().getEsferaAdministrativa());
        dto.setNivelHierarquia(form.getModel().getObject().getNivelHierarquia());
        dto.setFluxoClientela(form.getModel().getObject().getCodigoFluxoClientela());
        dto.setTurnoAtendimento(form.getModel().getObject().getTurnoAtendimento());
        dto.setEmpresaAtendimentosPrestados(lstAtendimentoPrestado);

        BOFactoryWicket.getBO(AtendimentoGeralFacade.class).salvarEmpresaCaracterizacao(dto);
        
        Page page = new ConsultaEmpresaPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (atendimentoPrestado == null) {
            throw new ValidacaoException(BundleManager.getString("informeAtendimento"));
        }
        if (tipoConvenio == null) {
            throw new ValidacaoException(BundleManager.getString("favorInformeConvenio"));
        }

        if (!CollectionUtils.isNotNullEmpty(lstAtendimentoPrestado)) {
            lstAtendimentoPrestado = new ArrayList<EmpresaAtendimentoPrestado>();
        }

        for (EmpresaAtendimentoPrestado item : lstAtendimentoPrestado) {
            if (item.getId().getTipoConvenio().equals(tipoConvenio) && item.getId().getAtendimentoPrestado().equals(atendimentoPrestado)) {
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        EmpresaAtendimentoPrestado empresaAtendimentoPrestado = new EmpresaAtendimentoPrestado();
        empresaAtendimentoPrestado.setId(new EmpresaAtendimentoPrestadoPK());
        empresaAtendimentoPrestado.getId().setTipoConvenio(tipoConvenio);
        empresaAtendimentoPrestado.getId().setAtendimentoPrestado(atendimentoPrestado);
        empresaAtendimentoPrestado.getId().setEmpresa(rowObject);
        empresaAtendimentoPrestado.setDataAtualizacao(DataUtil.getDataAtual());

        lstAtendimentoPrestado.add(empresaAtendimentoPrestado);
        autoCompleteConsultaAtendimentoPrestado.limpar(target);
        autoCompleteConsultaTipoConvenio.limpar(target);
        tblAtendimentoPrestado.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EmpresaAtendimentoPrestado proxy = on(EmpresaAtendimentoPrestado.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("atendimento"), proxy.getId().getAtendimentoPrestado().getDescricao()));
        columns.add(createColumn(bundle("convenio"), proxy.getId().getTipoConvenio().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstAtendimentoPrestado;
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EmpresaAtendimentoPrestado>() {

            @Override
            public void customizeColumn(EmpresaAtendimentoPrestado rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EmpresaAtendimentoPrestado>() {
                    @Override
                    public void action(AjaxRequestTarget target, EmpresaAtendimentoPrestado modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private void remover(AjaxRequestTarget target, EmpresaAtendimentoPrestado modelObject) {
        lstAtendimentoPrestado.remove(modelObject);
        tblAtendimentoPrestado.update(target);
    }

    private DropDown getDropDownFluxoClientela(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");
        dropDown.addChoice(Empresa.DEMANDA_ESPONTANEA, Bundle.getStringApplication("rotulo_demanda_espontanea"));
        dropDown.addChoice(Empresa.DEMANDA_REFERENCIADA, Bundle.getStringApplication("rotulo_demanda_referenciada"));
        dropDown.addChoice(Empresa.DEMANDA_ESPONTANEA_E_REFERENCIADA, Bundle.getStringApplication("rotulo_demanda_espontanea_referenciada"));

        return dropDown;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroCaracterizacaoUnidade");
    }

}
