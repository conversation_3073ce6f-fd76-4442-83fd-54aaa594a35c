package br.com.celk.view.agenda.agendamento.manutencaoagendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;

import static br.com.celk.agendamento.FiltrarAgendasParametroUnidadeOrigemESolicitante.filtrarAgendasByUnidade;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.FiltrarAgendaGradePorPrioridade;
import br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog.DlgCadastroNovaAgenda;
import br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog.DlgRemanejamentoAgendamento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.CadastroNovaAgendaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.RemanejamentoAgendamentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoAgendasCanceladasRemanejadasDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.equalTo;

import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;

import java.util.*;

import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class RemanejamentoAgendamentoPage extends BasePage{
    
    private Form<AgendaGradeAtendimentoDTOParam> form;
    private List<AgendaGradeAtendimentoHorario> lstAgendaGradeAtendimentoHorario;
    private List<AgendaGradeAtendimentoDTO> lstAgendaGradeAtendimentoDTO;
    
    private WebMarkupContainer containerDados;
    private String descricaoTipoProcedimento;
    private Long vagasNecessarias = 0L;
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private MultiSelectionTable<AgendaGradeAtendimentoDTO> tblAgendamentoSolicitacao;
    private AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
    private DlgRemanejamentoAgendamento dlgRemanejamentoAgendamento;
    private DlgCadastroNovaAgenda dlgCadastroNovaAgenda;
    private DlgImpressaoObject<List<AgendaGradeAtendimentoHorario>> dlgImpressaoRemanejamento;
    private boolean isPermissaoEmpresa;
    private boolean filtrosObrigatorios;
    private Long tipoAgendaRecepcao;

    private ExamePrestadorContrato contrato;


    public RemanejamentoAgendamentoPage(List<AgendaGradeAtendimentoHorario> lstAgendaGradeAtendimentoHorario, boolean isPermissaoEmpresa, Empresa empresa) {
        super();
        this.lstAgendaGradeAtendimentoHorario = lstAgendaGradeAtendimentoHorario;
        this.isPermissaoEmpresa = isPermissaoEmpresa;
        param.setEstabelecimento(new OperadorValor<List<Empresa>>());
        param.getEstabelecimento().setValue(new ArrayList<Empresa>());
        param.getEstabelecimento().getValue().add(empresa);
        configurarContainerDados();
        init();
    }

    public RemanejamentoAgendamentoPage(List<AgendaGradeAtendimentoHorario> lstAgendaGradeAtendimentoHorario, boolean isPermissaoEmpresa, Empresa empresa, boolean filtrosObrigatorios) {
        super();
        this.lstAgendaGradeAtendimentoHorario = lstAgendaGradeAtendimentoHorario;
        this.isPermissaoEmpresa = isPermissaoEmpresa;
        this.filtrosObrigatorios = filtrosObrigatorios;
        param.setEstabelecimento(new OperadorValor<List<Empresa>>());
        param.getEstabelecimento().setValue(new ArrayList<Empresa>());
        param.getEstabelecimento().getValue().add(empresa);
        configurarContainerDados();
        init();
    }

    private void init(){
        containerDados.add(new DisabledInputField("descricaoTipoProcedimento", new PropertyModel<String>(this, "descricaoTipoProcedimento")));
        containerDados.add(new DisabledInputField("vagasNecessarias", new PropertyModel<String>(this, "vagasNecessarias")));
        getForm().add(containerDados);
        
        AgendaGradeAtendimentoDTOParam proxy = on(AgendaGradeAtendimentoDTOParam.class);

        if (filtrosObrigatorios){
            getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento()), true));
            getForm().add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true));
        }else {
            getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
            getForm().add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        }
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        

        
        getForm().add(tblAgendamentoSolicitacao = new MultiSelectionTable("tblAgendamentoSolicitacao", getColumns(), getCollectionProvider()));
        
        getForm().add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (filtrosObrigatorios){
                  procurarAguardaProcedimentoSim(target);
                }else {
                    procurar(target);
                }
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ManutencaoAgendamentoPage());
            }
        }.setDefaultFormProcessing(false));
        
        getForm().add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarConfirmar();
                remanejamentoAgendamento(target);
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnNovaAgenda") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarNovaAgenda();
                cadastroNovaAgenda(target);
            }
        });
        
        add(getForm());
        procurar(null);
    }
    
    private void initDialogImpressaoRemanejamento(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agahList){
        if (dlgImpressaoRemanejamento == null) {
            addModal(target, dlgImpressaoRemanejamento = new DlgImpressaoObject<List<AgendaGradeAtendimentoHorario>>(newModalId(), bundle("msgRemanejamentoAgendamentoConcluidoSucesso")) {

                @Override
                public DataReport getDataReport(List<AgendaGradeAtendimentoHorario> agahList) throws ReportException {
                    RelacaoAgendasCanceladasRemanejadasDTOParam param = new RelacaoAgendasCanceladasRemanejadasDTOParam();
                    param.setAgendaGradeAtendimentoHorarioList(agahList);
                    param.setAgendasRemanejadas(true);
                    
                    return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relacaoAgendasCanceladasRemanejadas(param);
                }
                
                @Override
                public void onFechar(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agahList) throws ValidacaoException, DAOException {
                    setResponsePage(new ManutencaoAgendamentoPage());
                }
            
            });
            dlgImpressaoRemanejamento.setLabelImprimir(target, BundleManager.getString("imprimirListaPaciente"));
        }
        dlgImpressaoRemanejamento.show(target, agahList);
    }
    
    private List<AgendaGradeAtendimentoDTO> getAgendasSelecionadas(){
        return tblAgendamentoSolicitacao.getSelectedObjects();
    }
    
    private void validarConfirmar() throws ValidacaoException{
        if(CollectionUtils.isEmpty(getAgendasSelecionadas())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_uma_agenda"));
        }

        Long vagasDisponiveis = 0L;
        for(AgendaGradeAtendimentoDTO itensDTO : getAgendasSelecionadas()){
            vagasDisponiveis = vagasDisponiveis + itensDTO.getVagasDisponiveis();
        }

        if(vagasDisponiveis < vagasNecessarias){
            throw new ValidacaoException(Bundle.getStringApplication("msg_numero_vagas_agendas_selecionadas_superior_numero_vagas_necessarias"));            
        }

        for(AgendaGradeAtendimentoHorario agah : lstAgendaGradeAtendimentoHorario){
            if(agah.getQuantidadeVagasOcupadas() > 1L){
                for(AgendaGradeAtendimentoDTO itensDTO : getAgendasSelecionadas()){
                    if(itensDTO.getVagasDisponiveis() < agah.getQuantidadeVagasOcupadas()){
                        throw new ValidacaoException(Bundle.getStringApplication("msg_numero_vagas_disponiveis_deve_ser_maior_igual_numero_vagas_solicitadas"));            
                    }
                }
            }
        }
    }
    
    private void validarNovaAgenda() throws ValidacaoException{
        Long codigoProfissional = null;
        Long codigoEstabelecimento = null;
        
        for(AgendaGradeAtendimentoHorario agah : lstAgendaGradeAtendimentoHorario){
            if(codigoProfissional == null || codigoEstabelecimento == null){
                if (agah.getProfissional() != null) {
                    codigoProfissional = agah.getProfissional().getCodigo();
                }
                codigoEstabelecimento = agah.getLocalAgendamento().getCodigo();
            } else {
                if(!codigoEstabelecimento.equals(agah.getLocalAgendamento().getCodigo())){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_estabelecimentos_agendamentos_selecionados_devem_ser_mesmos"));
                } else if(codigoProfissional != null && !codigoProfissional.equals(agah.getProfissional().getCodigo())){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_profissionais_agendamentos_selecionados_devem_ser_mesmos"));
                }
            }
        }
    }
    
    private void remanejamentoAgendamento(AjaxRequestTarget target) {
        if (dlgRemanejamentoAgendamento == null) {
            addModal(target, dlgRemanejamentoAgendamento = new DlgRemanejamentoAgendamento(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, RemanejamentoAgendamentoDTO dto) throws ValidacaoException, DAOException {
                    List<AgendaGradeAtendimentoHorario> agahList = BOFactoryWicket.getBO(AgendamentoFacade.class).processarRemanejamentoAgendamento(dto);
                    initDialogImpressaoRemanejamento(target, agahList);
                }
            });
        }
        RemanejamentoAgendamentoDTO remanejamentoAgendamentoDTO = new RemanejamentoAgendamentoDTO();
        remanejamentoAgendamentoDTO.setLstAgendaGradeAtendimentoHorario(lstAgendaGradeAtendimentoHorario);
        remanejamentoAgendamentoDTO.setLstAgendaGradeAtendimentoDTO(getAgendasSelecionadas());        
        
        dlgRemanejamentoAgendamento.show(target, remanejamentoAgendamentoDTO);
    }
    
    private void cadastroNovaAgenda(AjaxRequestTarget target) {
        if (dlgCadastroNovaAgenda == null) {
            addModal(target, dlgCadastroNovaAgenda = new DlgCadastroNovaAgenda(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, CadastroNovaAgendaDTO dto) throws ValidacaoException, DAOException {
                    Date dataValidadeContrato = getContratoPrestador(dto.getAgendaGradeAtendimentoHorarioList().get(0)) != null ? getContratoPrestador(dto.getAgendaGradeAtendimentoHorarioList().get(0)).getDataValidade() : null;

                    if (dataValidadeContrato != null && dto.getDataAgenda().after(dataValidadeContrato)){
                        throw new ValidacaoException(new ValidacaoProcesso(BundleManager.getString("dataMaiorDataValidade", Data.formatar(dataValidadeContrato))));
                    }

                    dto.setQuantidadeAtendimento(vagasNecessarias);
                    List<AgendaGradeAtendimentoHorario> agahList = BOFactoryWicket.getBO(AgendamentoFacade.class).cadastrarNovaAgendaRemanejamento(dto);
                    dlgCadastroNovaAgenda.close(target);
                    initDialogImpressaoRemanejamento(target, agahList);
                }
            });
        }    
        
        dlgCadastroNovaAgenda.show(target, lstAgendaGradeAtendimentoHorario);
    }

    private ExamePrestadorContrato getContratoPrestador(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        if (contrato == null) {
            contrato = LoadManager.getInstance(ExamePrestadorContrato.class)
                    .addProperty(VOUtils.montarPath(ExamePrestadorContrato.PROP_DATA_VALIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, agendaGradeAtendimentoHorario.getLocalAgendamento().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, agendaGradeAtendimentoHorario.getTipoProcedimento().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestadorContrato.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.IGUAL, ExamePrestadorContrato.Situacao.ATIVO.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(ExamePrestadorContrato.PROP_DATA_VALIDADE, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1)
                    .start().getVO();
        }

        return contrato;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
    
    private Form<AgendaGradeAtendimentoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<AgendaGradeAtendimentoDTOParam>(param));
        }
        return this.form;
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);

        columns.add(createColumn(bundle("data"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getData()));
        columns.add(createColumn(bundle("dia"), proxy.getDiaSemanaAbv()));
        columns.add(createColumn(bundle("inicio"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraInicial()));
        columns.add(createColumn(bundle("vagasDisponiveis"), proxy.getVagasDisponiveis()));
        columns.add(createColumn(bundle("tipo"), proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));
        columns.add(createColumn(bundle("unidade"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstAgendaGradeAtendimentoDTO;
            }
        };
    }
    
    private void procurar(AjaxRequestTarget target){
        try {
            List<AgendaGradeAtendimentoDTO> list = BOFactoryWicket.getBO(AgendamentoFacade.class).verificarExisteVagasDisponiveisAgendaExameList(lstAgendaGradeAtendimentoHorario.get(0).getSolicitacaoAgendamento(), true);

            lstAgendaGradeAtendimentoDTO = new FiltrarAgendaGradePorPrioridade().filtrar(list, lstAgendaGradeAtendimentoHorario.get(0).getSolicitacaoAgendamento());

            if(param.getEstabelecimento().getValue().size() == 1){
                lstAgendaGradeAtendimentoDTO = filtrarAgendasByUnidade(lstAgendaGradeAtendimentoDTO, param.getEstabelecimento().getValue().get(0));
            }

            if(param.getProfissional() != null){
                AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);
                lstAgendaGradeAtendimentoDTO = filter(having(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getCodigo(), equalTo(param.getProfissional().getCodigo())), lstAgendaGradeAtendimentoDTO);
            }

            {
                AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);
                lstAgendaGradeAtendimentoDTO = filter(having(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento(),
                        equalTo(lstAgendaGradeAtendimentoHorario.get(0).getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento())), lstAgendaGradeAtendimentoDTO);
            }

        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        tblAgendamentoSolicitacao.populate();
        if(target != null){
            tblAgendamentoSolicitacao.clearSelection(target);
            tblAgendamentoSolicitacao.update(target);
        }
    }

    private void procurarAguardaProcedimentoSim(AjaxRequestTarget target){
        param.setValidarUnidadeInformatizada(false);
        if (TipoAtendimentoAgenda.TIPO_CONSULTA.equals(Arrays.asList(lstAgendaGradeAtendimentoHorario.get(0).getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()))) {
            param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_RETORNO));
        } else {
            param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_REGULACAO));
        }
        param.setApenasAgendasComVagas(true);
        param.setValidarEmpresaOrigem(false);
        param.setTipoProcedimento(null);
        param.setDataAgendasRemanejamento(DataUtil.getDataAtual());
        param.setAdjustRangeHour(false);
        param.setAguardaProcedimento(true);

        if (RepositoryComponentDefault.TIPO_AGENDA_LOCAL_RECPCAO.equals(getParametroTipoAgendaRecepcao())) {
            param.setTipoAgenda(Agenda.TIPO_AGENDA_LOCAL);
        }
        if (RepositoryComponentDefault.TIPO_AGENDA_COMPARTILHADA_RECPCAO.equals(getParametroTipoAgendaRecepcao())) {
            param.setTipoAgenda(Agenda.TIPO_AGENDA_COMPARTILHADA);
        }

        try {
            lstAgendaGradeAtendimentoDTO = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        tblAgendamentoSolicitacao.populate();
        if(target != null){
            tblAgendamentoSolicitacao.clearSelection(target);
            tblAgendamentoSolicitacao.update(target);
        }
    }
    
    private void configurarContainerDados(){
        getForm().add(containerDados = new WebMarkupContainer("containerDados", new CompoundPropertyModel(this)));
        
        descricaoTipoProcedimento = lstAgendaGradeAtendimentoHorario.get(0).getTipoProcedimento().getDescricao();
        for(AgendaGradeAtendimentoHorario agah : lstAgendaGradeAtendimentoHorario){
            vagasNecessarias = vagasNecessarias + agah.getQuantidadeVagasOcupadas();
        }
    }

    private Long getParametroTipoAgendaRecepcao() {
        if (tipoAgendaRecepcao == null) {
            try {
                tipoAgendaRecepcao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("tipoAgendaRecepcao");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return tipoAgendaRecepcao;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("remanejamentoAgendamentos");
    }
}