package br.com.celk.view.agenda.agendamento.leito;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.MultiFileUploadField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public abstract class PnlReenviarAih extends Panel {

    private CompoundPropertyModel<Aih> model;
    private InputArea<String> txaMotivo;
    private String motivo;
    private MultiFileUploadField mfuAnexo;
    private final Collection<FileUpload> uploads = new ArrayList();

    public PnlReenviarAih(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new Aih()));

        Aih proxy = on(Aih.class);
        form.add(new DisabledInputField(path(proxy.getCodigo())));
        form.add(new DisabledInputField(path(proxy.getUsuarioCadSus().getNomeSocial())));

        form.add(txaMotivo = new InputArea("motivo", new PropertyModel(this, "motivo")));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarConfirmacaoReenvio(target)) {
                    PnlReenviarAih.this.onConfirmar(target, model.getObject(), motivo, uploads);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        Form formAnexo = new Form("formAnexo");

        formAnexo.setMultiPart(true);
        formAnexo.add(mfuAnexo = new MultiFileUploadField("uploads", new PropertyModel(this, "uploads")));
        mfuAnexo.setOutputMarkupId(true);

        form.add(formAnexo);

        add(form);
    }

    private boolean validarConfirmacaoReenvio(AjaxRequestTarget target) {
        try {
            if ("".equals(Coalesce.asString(motivo))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih, String motivoReenvio, final Collection<FileUpload> uploads) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setAih(AjaxRequestTarget target, Aih aih) {
        limpar(target);
        this.model.setObject(loadAih(aih));
        target.add(this);
    }

    private Aih loadAih(Aih aih) {
        return LoadManager.getInstance(Aih.class)
                .setId(aih.getCodigo())
                .start().getVO();
    }

    public void limpar(AjaxRequestTarget target) {
        txaMotivo.limpar(target);
        motivo = null;
    }

    public InputArea getTxaMotivo() {
        return txaMotivo;
    }
}
