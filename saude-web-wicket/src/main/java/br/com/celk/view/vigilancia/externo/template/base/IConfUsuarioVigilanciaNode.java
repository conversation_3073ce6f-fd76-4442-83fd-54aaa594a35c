package br.com.celk.view.vigilancia.externo.template.base;

import br.com.celk.resources.Icon32;
import br.com.celk.view.vigilancia.externo.template.UsuarioExternoDefaultCadastroPanel;
import br.com.celk.vigilancia.nodes.NodesConfUsuariosVigilanciaRef;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface IConfUsuarioVigilanciaNode extends Serializable {

    public UsuarioExternoDefaultCadastroPanel getPanel(String id);

    public String getTitulo();

    public Icon32 getIcone();

    public NodesConfUsuariosVigilanciaRef getIdentificador();

    public Boolean visible(Boolean isAdmin);

}