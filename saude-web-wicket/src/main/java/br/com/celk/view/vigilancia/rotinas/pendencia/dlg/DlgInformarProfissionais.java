package br.com.celk.view.vigilancia.rotinas.pendencia.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarProfissionais extends Window {

    private PnlInformarProfissionais pnlInformarProfissionais;

    public DlgInformarProfissionais(String id) {
        super(id);
        init();
    }

    private void init() {

        setInitialWidth(700);
        setInitialHeight(200);

        setTitle(BundleManager.getString("profissionais"));

        setContent(pnlInformarProfissionais = new PnlInformarProfissionais(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) {
              DlgInformarProfissionais.this.onConfirmar(target);
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, PendenciaDia pd) {
        super.show(target);
        pnlInformarProfissionais.setDTO(target, pd);
    }
    
     public abstract void onConfirmar(AjaxRequestTarget target);

}
