package br.com.celk.view.controle.parametros.nodes.base;

import br.com.celk.component.window.Window;
import br.com.celk.view.controle.parametros.panel.template.DefaultParametrosPanel;
import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface IParametrosController extends Serializable{

    public void changeNode(AjaxRequestTarget target, IParametrosNode node);
    
    public void changePanel(AjaxRequestTarget target, DefaultParametrosPanel panel);
    
    public String newWindowId();
    
    public void addWindow(AjaxRequestTarget target, Window window);
    
    public String panelId();

}
