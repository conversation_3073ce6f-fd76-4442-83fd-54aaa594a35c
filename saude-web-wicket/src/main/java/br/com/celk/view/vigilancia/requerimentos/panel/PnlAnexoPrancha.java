package br.com.celk.view.vigilancia.requerimentos.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoExumacaoPage;
import br.com.celk.view.vigilancia.requerimentos.columns.DownloadAnexoPranchaVigilanciaColumnPanel;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.VigilanciaAnexosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AnexoPranchaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.joda.time.DateTime;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class PnlAnexoPrancha extends Panel {

    private Form<AnexoPranchaDTO> form;
    private AnexoPranchaDTO pranchaDTO;
    private CompoundPropertyModel<AnexoPranchaDTO> modelAnexoPrancha;
    private Table tblAnexoPrancha;
    private FileUpload fileUploadPrancha;
    private FileUploadField fileUploadFieldPranchas;
    private List<FileUpload> lstUploadPranchas;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private boolean modoEdicao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoSolicitacao tipoSolicitacao;
    private GerenciadorArquivo.OrigemArquivo origemArquivo;
    private String label = "Anexar Pranchas";


    public PnlAnexoPrancha(
            String id,
            AnexoPranchaDTO pranchaDTO,
            boolean modoEdicao,
            RequerimentoVigilancia requerimentoVigilancia,
            TipoSolicitacao tipoSolicitacao,
            GerenciadorArquivo.OrigemArquivo origemArquivo,
            String label
    ) {
        super(id);
        this.pranchaDTO = pranchaDTO;
        this.modoEdicao = modoEdicao;
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.tipoSolicitacao = tipoSolicitacao;
        this.origemArquivo = origemArquivo;
        this.label = label;
        init();
    }

    private void init() {
        modelAnexoPrancha = new CompoundPropertyModel<>(new AnexoPranchaDTO());
        form = new Form("form", modelAnexoPrancha);
        form.add(new Label("labelImprimir", new CompoundPropertyModel(label)));

        fileUploadFieldPranchas = new FileUploadField("fileUploadPrancha", new PropertyModel<>(this, "lstUploadPranchas"));
        fileUploadFieldPranchas.setEnabled(isModoEdicao());

        AbstractAjaxButton btnAdicionarPrancha = new AbstractAjaxButton("btnAdicionarPrancha") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarPrancha(target);
            }
        };
        btnAdicionarPrancha.setDefaultFormProcessing(false);
        btnAdicionarPrancha.setEnabled(isModoEdicao());
        tblAnexoPrancha = new Table("tblAnexoPrancha", getColumnsAnexoPrancha(), getCollectionProviderAnexoPrancha());
        tblAnexoPrancha.setScrollXInner("930px");
        tblAnexoPrancha.populate();

        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        form.add(fileUploadFieldPranchas, btnAdicionarPrancha, tblAnexoPrancha);

        add(form);
    }

    private void adicionarPrancha(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AnexoPranchaDTO anexo = (AnexoPranchaDTO) SerializationUtils.clone(modelAnexoPrancha.getObject());

        fileUploadPrancha = fileUploadFieldPranchas.getFileUpload();
        validarUploadAnexos(fileUploadPrancha);

        try {
            File file = File.createTempFile("anexo", fileUploadPrancha.getClientFileName());

            fileUploadPrancha.writeTo(file);
            anexo.setFile(file);
            anexo.setDescricaoAnexo(fileUploadPrancha.getClientFileName());
            anexo.setNomeArquivoOriginal(fileUploadPrancha.getClientFileName());
            anexo.setUsuarioCadastro(ApplicationSession.get().getSessaoAplicacao().getUsuario());
            anexo.setDataCadastro(DateTime.now().toDate());
            anexo.setOrigemArquivo(origemArquivo);
            anexo.setSituacao(RequerimentosProjetosEnums.Status.PENDENTE.value());

            if (isModoEdicao()) {
                if (isProjetoArquitetonico()) {
                    anexo.getRequerimentoProjetoArquitetonicoSanitarioAnexo().setDataAlteracao(DateTime.now().toDate());
                    anexo.getRequerimentoProjetoArquitetonicoSanitarioAnexo().setUsuarioAlteracao(ApplicationSession.get().getSessaoAplicacao().getUsuario());
                } else if (isHidro()) {
                    anexo.getRequerimentoProjetoHidrossanitarioAnexo().setDataAlteracao(DateTime.now().toDate());
                    anexo.getRequerimentoProjetoHidrossanitarioAnexo().setUsuarioAlteracao(ApplicationSession.get().getSessaoAplicacao().getUsuario());
                } else if (isHidroDeclaratorio()) {
                    anexo.getHidrossanitarioDeclaratorioAnexo().setDataAlteracao(DateTime.now().toDate());
                    anexo.getHidrossanitarioDeclaratorioAnexo().setUsuarioAlteracao(ApplicationSession.get().getSessaoAplicacao().getUsuario());
                }
            }

            pranchaDTO.getListAnexosPrancha().add(anexo);

            tblAnexoPrancha.populate();
            tblAnexoPrancha.update(target);

            modelAnexoPrancha.setObject(new AnexoPranchaDTO());
            fileUploadPrancha = null;
            target.add(form);

        } catch (IOException ex) {
            Logger.getLogger(RequerimentoExumacaoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void validarUploadAnexos(FileUpload fileUpload) throws ValidacaoException, DAOException {
        if (fileUpload == null) {
            throw new ValidacaoException(bundle("selecioneAnexo"));
        }
        if (!fileUpload.getClientFileName().endsWith(".pdf")) {
            throw new ValidacaoException(bundle("somentePossivelAnexarPdf"));
        }

        Long tamanhoAnexo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("tamanhoAnexoPranchas");
        if (tamanhoAnexo != null) {
            if ((fileUpload.getSize() / 1024) > tamanhoAnexo) {
                throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexoXMB", tamanhoAnexo));
            }
        } else if (fileUpload.getSize() > 1048576L) {
            throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexo1MB"));
        }
    }

    private List<IColumn> getColumnsAnexoPrancha() {
        List<IColumn> columns = new ArrayList<>();
        AnexoPranchaDTO proxy = on(AnexoPranchaDTO.class);

        columns.add(getRemoverColumnAnexoPrancha());
        columns.add(getColumnStatus(proxy));
        columns.add(getDownloadAnexoActionColumn());
        columns.add(new DateColumn(bundle("data"), path(proxy.getDataCadastro())).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(getExibirConsultarAnexosPrancha());

        return columns;
    }

    private ICollectionProvider getCollectionProviderAnexoPrancha() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return pranchaDTO.getListAnexosPrancha();
            }
        };
    }

    private IColumn getColumnStatus(AnexoPranchaDTO proxy) {
        if (isProjetoArquitetonico()) {
            return createColumn(bundle("status"), proxy.getRequerimentoProjetoArquitetonicoSanitarioAnexo().getDescricaoStatus());
        } else if (isHidroDeclaratorio()) {
            return createColumn(bundle("status"), proxy.getHidrossanitarioDeclaratorioAnexo().getDescricaoStatus());
        }
        return createColumn(bundle("status"), proxy.getRequerimentoProjetoHidrossanitarioAnexo().getDescricaoStatus());
    }

    private IColumn getDownloadAnexoActionColumn() {
        return new CustomColumn<AnexoPranchaDTO>(bundle("anexo")) {
            @Override
            public Component getComponent(String componentId, final AnexoPranchaDTO rowObject) {
                return new DownloadAnexoPranchaVigilanciaColumnPanel(componentId, rowObject) {
                };
            }
        };
    }

    private IColumn getRemoverColumnAnexoPrancha() {
        return new MultipleActionCustomColumn<AnexoPranchaDTO>() {
            @Override
            public void customizeColumn(final AnexoPranchaDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AnexoPranchaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < pranchaDTO.getListAnexosPrancha().size(); i++) {
                            AnexoPranchaDTO item = pranchaDTO.getListAnexosPrancha().get(i);
                            if (item == rowObject) {
                                pranchaDTO.getListAnexosPranchaExcluidos().add(item);
                                pranchaDTO.getListAnexosPrancha().remove(i);
                            }
                        }
                        tblAnexoPrancha.populate();
                        tblAnexoPrancha.update(target);
                    }
                }).setEnabled(isModoEdicao());
            }
        };
    }

    private IColumn getExibirConsultarAnexosPrancha() {
        return new MultipleActionCustomColumn<AnexoPranchaDTO>() {
            @Override
            public void customizeColumn(AnexoPranchaDTO rowObject) {
                addAction(ActionType.EXIBIR, rowObject, new IModelAction<AnexoPranchaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException {

                        viewFilePrancha(target, rowObject);
                    }
                }).setEnabled(isExibirPrancha(rowObject));
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AnexoPranchaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException {
                        preVisualizarAprovacao(target, rowObject);
                    }
                }).setIcon(Icon.DOC_ARROW_RIGHT)
                        .setTitleBundleKey("preVizualizarCarimboAprovacao")
                        .setVisible(requerimentoVigilancia == null ||
                                !RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(requerimentoVigilancia.getSituacao())
                        );
            }
        };
    }

    private void viewFilePrancha(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException {
        ajaxPreviewBlank.initiatePdfBase64(
                target,
                VigilanciaAnexosHelper.getFileBase64PranchaAprovada(
                        requerimentoVigilancia,
                        TipoSolicitacao.TipoDocumento.valueOf(tipoSolicitacao.getTipoDocumento()),
                        modelObject.getRequerimentoProjetoHidrossanitarioAnexo(),
                        modelObject.getHidrossanitarioDeclaratorioAnexo(),
                        modelObject.getRequerimentoProjetoArquitetonicoSanitarioAnexo()
                )
        );
    }

    private void preVisualizarAprovacao(AjaxRequestTarget target, AnexoPranchaDTO modelObject) throws ValidacaoException {
        ajaxPreviewBlank.initiatePdfBase64(
                target,
                VigilanciaAnexosHelper.getPreVisualizacaoCarimboPrancha(
                        modelObject,
                        requerimentoVigilancia,
                        TipoSolicitacao.TipoDocumento.valueOf(tipoSolicitacao.getTipoDocumento()))
        );
    }

    private boolean isExibirPrancha(AnexoPranchaDTO dto) {
        return dto.getRequerimentoProjetoHidrossanitarioAnexo().getCodigo() != null
                || dto.getHidrossanitarioDeclaratorioAnexo().getCodigo() != null
                || dto.getRequerimentoProjetoArquitetonicoSanitarioAnexo().getCodigo() != null
                ;
    }

    private boolean isHidro() {
        return GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJ_HIDROSSANITARIO.value().equals(origemArquivo);
    }

    private boolean isHidroDeclaratorio() {
        return GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJ_HIDRO_DECLARATORIO.value().equals(origemArquivo);
    }

    private boolean isProjetoArquitetonico() {
        return GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJETO_ARQUITETONICO.value().equals(origemArquivo);
    }

    public boolean isModoEdicao() {
        return modoEdicao;
    }

    public void setModoEdicao(boolean modoEdicao) {
        this.modoEdicao = modoEdicao;
    }

}
