package br.com.celk.view.unidadesaude.sae.utils.autocompleteresultadoesperado;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.sae.dto.QueryConsultaResultadoEsperadoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerResultadoEsperado extends Panel implements IRestricaoContainer<QueryConsultaResultadoEsperadoDTOParam> {

    private InputField<String> txtDescricao;

    private QueryConsultaResultadoEsperadoDTOParam param = new QueryConsultaResultadoEsperadoDTOParam();

    public RestricaoContainerResultadoEsperado(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaResultadoEsperadoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
