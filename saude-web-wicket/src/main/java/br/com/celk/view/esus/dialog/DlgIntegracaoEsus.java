package br.com.celk.view.esus.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.esus.ExportacaoEsusDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by sulivan on 21/07/17.
 */
public abstract class DlgIntegracaoEsus extends Window {

    private PnlIntegracaoEsus pnlIntegracaoEsus;

    public DlgIntegracaoEsus(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("integracaoEsus"));

        setInitialWidth(800);
        setInitialHeight(250);
        setResizable(true);

        setContent(pnlIntegracaoEsus = new PnlIntegracaoEsus(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ExportacaoEsusDTOParam dto) throws ValidacaoException, DAOException {
                close(target);
                DlgIntegracaoEsus.this.onConfirmar(target, dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ExportacaoEsusDTOParam dto) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
        pnlIntegracaoEsus.focus(target);
    }

    public void onFechar(AjaxRequestTarget target) {
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
        DlgIntegracaoEsus.this.onFechar(target);
    }
}
