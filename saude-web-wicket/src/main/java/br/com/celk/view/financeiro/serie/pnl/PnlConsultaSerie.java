package br.com.celk.view.financeiro.serie.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.financeiro.serie.customize.CustomizeConsultaSerie;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.financeiro.Serie;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaSerie extends PnlConsulta<Serie> {

    public PnlConsultaSerie(String id, IModel<Serie> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaSerie(String id, IModel<Serie> model) {
        super(id, model);
    }

    public PnlConsultaSerie(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaSerie(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaSerie();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("serie");
    }

}
