package br.com.celk.view.materiais.relatorios.judicial;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import static br.com.celk.system.methods.WicketMethods.bundle;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoMedicamentoJudicialDTOParam;
import static br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoMedicamentoJudicialDTOParam.FormaApresentacao;
import static br.com.ksisolucoes.report.materiais.judicial.interfaces.dto.RelatorioRelacaoMedicamentoJudicialDTOParam.TipoRelatorio;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.TipoSolicitacaoProduto;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoMedicamentoJudicialPage extends RelatorioPage<RelatorioRelacaoMedicamentoJudicialDTOParam> {

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private DropDown dropDownFormaApresentacao;
    private DropDown dropDownTipoSolicitacaoProduto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private CheckBoxLongValue checkBoxExibeEndPaciente;
    private CheckBoxLongValue checkBoxExibeTelefonePaciente;

    private Long exibeEndPaciente;


    @Override
    public void init(Form form) {
        RelatorioRelacaoMedicamentoJudicialDTOParam proxy = on(RelatorioRelacaoMedicamentoJudicialDTOParam.class);

        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(getDropDownTipoRelatorio(path(proxy.getTipoRelatorio())));
        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), FormaApresentacao.values()));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getListarVencidos())));

        form.add(checkBoxExibeEndPaciente = new CheckBoxLongValue("exibeEndPaciente"));
        form.add(checkBoxExibeTelefonePaciente = new CheckBoxLongValue("exibeTelefonePaciente"));


        form.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getVisualizarFA())));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getApenasSemEstoque())));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getListarCancelados())));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getVisualizarEntregas())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(getDropDownTipoPeriado(path(proxy.getTipoPeriado())));
        form.add(getDropDownTipoSolicitacaoProduto(path(proxy.getTipoSolicitacaoProduto())));
    }
    
    private DropDown getDropDownTipoSolicitacaoProduto(String id) {
        if(dropDownTipoSolicitacaoProduto == null){
            dropDownTipoSolicitacaoProduto = (DropDown) new DropDown(id).setLabel(new Model<String>(bundle("tipoSolicitacao")));

            List<TipoSolicitacaoProduto> list = LoadManager.getInstance(TipoSolicitacaoProduto.class)
                    .addProperties(new HQLProperties(TipoSolicitacaoProduto.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoSolicitacaoProduto.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            dropDownTipoSolicitacaoProduto.addChoice(null, "");
            if(CollectionUtils.isNotNullEmpty(list)){
                for (TipoSolicitacaoProduto tsp : list) {
                    dropDownTipoSolicitacaoProduto.addChoice(tsp, tsp.getDescricao());
                }
            }
        }

        return dropDownTipoSolicitacaoProduto;
    }

    private DropDown getDropDownTipoRelatorio(String id) {
        DropDown dropDown = DropDownUtil.getEnumDropDown(id, TipoRelatorio.values());
        dropDown.addAjaxUpdateValue();

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (TipoRelatorio.RESUMIDO.equals((Enum) this.getFormComponent().getModel().getObject())) {
                    checkBoxExibeTelefonePaciente.limpar(target);
                    checkBoxExibeEndPaciente.limpar(target);
                    checkBoxExibeEndPaciente.setEnabled(false);
                    checkBoxExibeTelefonePaciente.setEnabled(false);
                    dropDownFormaApresentacao.removeChoice(FormaApresentacao.PRODUTO);
                } else {
                    checkBoxExibeTelefonePaciente.limpar(target);
                    checkBoxExibeEndPaciente.limpar(target);
                    checkBoxExibeEndPaciente.setEnabled(true);
                    checkBoxExibeTelefonePaciente.setEnabled(true);

                    dropDownFormaApresentacao.addChoice(FormaApresentacao.PRODUTO, FormaApresentacao.PRODUTO.toString());
                }

                target.add(checkBoxExibeEndPaciente);
                target.add(checkBoxExibeTelefonePaciente);
                target.add(dropDownFormaApresentacao);
            }
        });

        return dropDown;
    }

    public DropDown getDropDownTipoPeriado(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(0L, BundleManager.getString("dataCadastro"));
        dropDown.addChoice(1L, BundleManager.getString("dataValidade"));

        return dropDown;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoMedicamentoJudicialDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoMedicamentoJudicialDTOParam param) throws ReportException {
        return BOFactory.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRelacaoMedicamentoJudicial(param);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relacaoProdutosSolicitados");
    }
}
