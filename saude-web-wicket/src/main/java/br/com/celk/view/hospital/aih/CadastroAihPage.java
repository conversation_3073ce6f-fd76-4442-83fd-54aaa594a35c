package br.com.celk.view.hospital.aih;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.markup.html.form.Form;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;
import net.java.dev.messagebundle.log.interfaces.Loggable;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
@Private
@Deprecated
public class CadastroAihPage extends BasePage {

    private Form form;
    private CompoundPropertyModel<AutorizacaoInternacaoHospitalarDTO> model;
    private DropDown<Long> dropDownClinica;
    private DropDown<Long> dropDownCaraterInternacao;
    private InputField<String> txtCnpjSeguradora;
    private InputField<String> txtCnpjEmpresa;
    private InputField<String> txtSerie;
    private InputField<String> txtNumBilhete;
    private InputField<String> txtCnaeEmpresa;
    private InputField<String> txtCbor;
    private RadioButtonGroup radioGroupAciente;
    private RadioButtonGroup radioGroupVinculoPrevidencia;
    private AjaxRadio radioEmpregado;
    private AjaxRadio radioEmpregador;
    private AjaxRadio radioAutonomo;
    private AjaxRadio radioDesempregado;
    private AjaxRadio radioAposentado;
    private AjaxRadio radioNaoSegurado;
    private AutoCompleteConsultaCid autoCompleteConsultaCidPrincipal;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetenciaSolicitado;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetenciaRealizado;
    private InputField inputFieldProfSolicitante;
    private RadioButtonGroup radioGroupTipoDocumentoSol;
    private InputField<String> nroDocProfSol;
    private AbstractAjaxLink btnDataSolicitacao;
    private DateChooser dataSolicitacao;
    private AttributeModifier attributeModifierCns = new AttributeModifier("class", "cns");

    @Deprecated
    public CadastroAihPage() {
        this(null);
    }

    @Deprecated
    public CadastroAihPage(AutorizacaoInternacaoHospitalarDTO object) {
        if (object == null) {
            object = new AutorizacaoInternacaoHospitalarDTO();
        }

        model = new CompoundPropertyModel(object);
        init();
    }

    public void init() {
        form = new Form("form", model);
        AutorizacaoInternacaoHospitalarDTO proxy = on(AutorizacaoInternacaoHospitalarDTO.class);

        form.add(new RequiredInputArea(path(proxy.getAutorizacaoInternacaoHospitalar().getPrincipaisSinaisSintomasClinicos())));
        form.add(new RequiredInputArea(path(proxy.getAutorizacaoInternacaoHospitalar().getCondicoesJustificamInternacao())));
        form.add(new RequiredInputArea(path(proxy.getAutorizacaoInternacaoHospitalar().getPrincipaisResultadosProvasDiagnosticas())));
        form.add(autoCompleteConsultaCidPrincipal = new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidPrincipal()), true));
        autoCompleteConsultaCidPrincipal.addAjaxUpdateValue();
        form.add(new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidSecundario())));
        form.add(new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidCausaAssociada())));
        form.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getNumeroAutorizacaoAihAnterior())));
        form.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getNumeroAutorizacaoAihPosterior())));
        form.add(autoCompleteConsultaProcedimentoCompetenciaSolicitado = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetenciaSolicitado()), true));
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setLabel(Model.of(bundle("procedimentoSolicitado")));
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.addAjaxUpdateValue();
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setValidarNaoFaturaveis(true);
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.AIH_PRINCIPAL));
        WebMarkupContainer containerProcedimentoRealizado = new WebMarkupContainer("containerProcedimentoRealizado");
        containerProcedimentoRealizado.add(autoCompleteConsultaProcedimentoCompetenciaRealizado = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetenciaRealizado()), true));
        autoCompleteConsultaProcedimentoCompetenciaRealizado.setLabel(Model.of(bundle("procedimentoRealizado")));
        autoCompleteConsultaProcedimentoCompetenciaRealizado.addAjaxUpdateValue();
        autoCompleteConsultaProcedimentoCompetenciaRealizado.setValidarNaoFaturaveis(true);
        autoCompleteConsultaProcedimentoCompetenciaRealizado.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.AIH_PRINCIPAL));
        form.add(containerProcedimentoRealizado);
        if (isActionPermitted(Permissions.ALTERAR_PROC_REALIZADO, ConsultaAihPage.class)) {
            containerProcedimentoRealizado.setEnabled(true);
            containerProcedimentoRealizado.setVisible(true);
        } else {
            containerProcedimentoRealizado.setEnabled(false);
            containerProcedimentoRealizado.setVisible(false);
        }
        form.add(dropDownClinica = getDropDownClinica(path(proxy.getAutorizacaoInternacaoHospitalar().getClinica())));
        form.add(dropDownCaraterInternacao = getDropDownCaraterInternacao(path(proxy.getAutorizacaoInternacaoHospitalar().getCaraterInternacao())));

        autoCompleteConsultaCidPrincipal.add(new ConsultaListener<Cid>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cid cid) {
                try {
                    autoCompleteConsultaProcedimentoCompetenciaSolicitado.setCid(cid);
                    autoCompleteConsultaProcedimentoCompetenciaRealizado.setCid(cid);
                    target.add(autoCompleteConsultaProcedimentoCompetenciaSolicitado);
                    target.add(autoCompleteConsultaProcedimentoCompetenciaRealizado);

                    AtendimentoHelper.validaProcedimentoCid(cid, (ProcedimentoCompetencia) autoCompleteConsultaProcedimentoCompetenciaRealizado.getComponentValue());
                } catch (ValidacaoException ex) {
                    warn(target, ex.getMessage());
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });

        autoCompleteConsultaCidPrincipal.add(new RemoveListener<Cid>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid object) {
                autoCompleteConsultaCidPrincipal.limpar(target);
                autoCompleteConsultaProcedimentoCompetenciaSolicitado.setCid(null);
                autoCompleteConsultaProcedimentoCompetenciaRealizado.setCid(null);
                target.add(autoCompleteConsultaProcedimentoCompetenciaSolicitado);
                target.add(autoCompleteConsultaProcedimentoCompetenciaRealizado);
            }
        });

        form.add(radioGroupVinculoPrevidencia = (RadioButtonGroup) new RadioButtonGroup(path(proxy.getAutorizacaoInternacaoHospitalar().getVinculoPrevidencia())));
        radioGroupVinculoPrevidencia.add(radioEmpregado = new AjaxRadio("empregado", new Model<Long>(Aih.VinculoPrevidencia.EMPREGADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupVinculoPrevidencia.add(radioEmpregador = new AjaxRadio("empregador", new Model<Long>(Aih.VinculoPrevidencia.EMPREGADOR.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupVinculoPrevidencia.add(radioAutonomo = new AjaxRadio("autonomo", new Model<Long>(Aih.VinculoPrevidencia.AUTONOMO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupVinculoPrevidencia.add(radioDesempregado = new AjaxRadio("desempregado", new Model<Long>(Aih.VinculoPrevidencia.DESEMPREGADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupVinculoPrevidencia.add(radioAposentado = new AjaxRadio("aposentado", new Model<Long>(Aih.VinculoPrevidencia.APOSENTADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });
        radioGroupVinculoPrevidencia.add(radioNaoSegurado = new AjaxRadio("naoSegurado", new Model<Long>(Aih.VinculoPrevidencia.NAO_SEGURADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
            }
        });

        form.add(radioGroupAciente = (RadioButtonGroup) new RadioButtonGroup(path(proxy.getAutorizacaoInternacaoHospitalar().getAcidente())));
        radioGroupAciente.add(new AjaxRadio("nenhum", new Model(null)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                disabledForm(target);
            }
        });
        radioGroupAciente.add(new AjaxRadio("acidenteTransito", new Model<Long>(Aih.Acidente.TRANSITO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                disabledForm(target);
            }
        });
        radioGroupAciente.add(new AjaxRadio("acidenteTrabalhoTipico", new Model<Long>(Aih.Acidente.TRABALHO_TIPICO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                disabledForm(target);
            }
        });
        radioGroupAciente.add(new AjaxRadio("acidenteTrabalhoTrajeto", new Model<Long>(Aih.Acidente.TRABALHO_TRAJETO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                disabledForm(target);
            }
        });

        form.add(txtCnpjSeguradora = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getCnpjSeguradora())));
        form.add(txtCnpjEmpresa = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getCnpjEmpresa())));
        form.add(txtSerie = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getSerie())));
        form.add(txtNumBilhete = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getNumeroBilhete())));
        form.add(txtCnaeEmpresa = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getCnaeEmpresa())));
        form.add(txtCbor = new InputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getCbor())));

        form.add(inputFieldProfSolicitante = new RequiredInputField(path(proxy.getAutorizacaoInternacaoHospitalar().getNomeProfissionalSolicitante())));

        form.add(nroDocProfSol = new RequiredInputField(path(proxy.getAutorizacaoInternacaoHospitalar().getNroDocProfSol())));
        nroDocProfSol.setLabel(new Model(BundleManager.getString("cns")));

        form.add(dataSolicitacao = new DateChooser(path(proxy.getAutorizacaoInternacaoHospitalar().getDataSolicitacao())));
        dataSolicitacao.setRequired(true);
        dataSolicitacao.setLabel(new Model(BundleManager.getString("dataSolicitacao")));
        dataSolicitacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        form.add(new VoltarButton("btnVoltar"));

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(form);
        disabledForm(null);
        validacoesInit();
    }

    private void validacoesInit() {
        AutorizacaoInternacaoHospitalarDTO dto = model.getObject();

        if (dto != null) {
            autoCompleteConsultaProcedimentoCompetenciaSolicitado.setCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal());
            autoCompleteConsultaProcedimentoCompetenciaRealizado.setCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal());

            try {
                AtendimentoHelper.validaProcedimentoCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal(), dto.getProcedimentoCompetenciaRealizado());
            } catch (ValidacaoException ex) {
                warn(ex.getMessage());
            } catch (DAOException ex) {
                Logger.getLogger(CadastroAihPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private DropDown<Long> getDropDownClinica(String id) {
        if (dropDownClinica == null) {
            dropDownClinica = new DropDown<Long>(id);

            dropDownClinica.addChoice(null, "");
            for (Aih.Clinica list : Aih.Clinica.values()) {
                dropDownClinica.addChoice(list.value(), list.descricao());
            }

        }
        return dropDownClinica;
    }

    private DropDown<Long> getDropDownCaraterInternacao(String id) {
        if (dropDownCaraterInternacao == null) {
            dropDownCaraterInternacao = new DropDown<Long>(id);

            dropDownCaraterInternacao.addChoice(null, "");
            for (Aih.CaraterInternacao list : Aih.CaraterInternacao.values()) {
                dropDownCaraterInternacao.addChoice(list.value(), list.descricao());
            }

        }
        return dropDownCaraterInternacao;
    }

    private void disabledForm(AjaxRequestTarget target) {
        if (radioGroupAciente.getModelObject() == null) {
            txtCnpjSeguradora.setEnabled(false);
            txtCnpjEmpresa.setEnabled(false);
            txtSerie.setEnabled(false);
            txtNumBilhete.setEnabled(false);
            txtCnaeEmpresa.setEnabled(false);
            txtCbor.setEnabled(false);

            radioGroupVinculoPrevidencia.setEnabled(false);
            radioGroupVinculoPrevidencia.setModelObject(null);

            txtCnpjSeguradora.setComponentValue(null);
            txtCnpjEmpresa.setComponentValue(null);
            txtSerie.setComponentValue(null);
            txtNumBilhete.setComponentValue(null);
            txtCnaeEmpresa.setComponentValue(null);
            txtCbor.setComponentValue(null);
        } else {
            txtCnpjSeguradora.setEnabled(true);
            txtCnpjEmpresa.setEnabled(true);
            txtSerie.setEnabled(true);
            txtNumBilhete.setEnabled(true);
            txtCnaeEmpresa.setEnabled(true);
            txtCbor.setEnabled(true);
            radioGroupVinculoPrevidencia.setEnabled(true);
        }

        if (target != null) {
            target.add(txtCnpjSeguradora);
            target.add(txtCnpjEmpresa);
            target.add(txtSerie);
            target.add(txtNumBilhete);
            target.add(txtCnaeEmpresa);
            target.add(txtCbor);
            target.add(radioEmpregado);
            target.add(radioEmpregador);
            target.add(radioAutonomo);
            target.add(radioDesempregado);
            target.add(radioAposentado);
            target.add(radioNaoSegurado);
        }
    }

    public void salvar() throws DAOException, ValidacaoException {
        AutorizacaoInternacaoHospitalarDTO dto = model.getObject();
        String numeroCartao = StringUtil.getDigits(dto.getAutorizacaoInternacaoHospitalar().getNroDocProfSol());
        if (!CnsValidator.validaCns(numeroCartao)) {
            throw new ValidacaoException(BundleManager.getString("cns_invalido"));
        }

        if (dropDownClinica.getModelObject() == null) {
            throw new ValidacaoException(BundleManager.getString("campoClinicaObrigatorio"));
        }
        if (dropDownCaraterInternacao.getModelObject() == null) {
            throw new ValidacaoException(BundleManager.getString("campoCaraterInternacaoObrigatorio"));
        }

        dto.getAutorizacaoInternacaoHospitalar().setDataCadastro(DataUtil.getDataAtual());
        dto.getAutorizacaoInternacaoHospitalar().setDataAlteracao(DataUtil.getDataAtual());
        dto.getAutorizacaoInternacaoHospitalar().setUsuarioAlteracao(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario());
        dto.getAutorizacaoInternacaoHospitalar().setProcedimentoSolicitado(dto.getProcedimentoCompetenciaSolicitado().getId().getProcedimento());
        dto.getAutorizacaoInternacaoHospitalar().setProcedimentoRealizado(dto.getProcedimentoCompetenciaRealizado().getId().getProcedimento());
        dto.getAutorizacaoInternacaoHospitalar().setDiagnosticoInicial(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal().getDescricao());

        Aih aih = BOFactoryWicket.save(dto.getAutorizacaoInternacaoHospitalar());

        Page page = new ConsultaAihPage(aih.getCodigo());
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(AutorizacaoInternacaoHospitalarDTO.class, aih));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("laudoAih");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
//        if (model.getObject().getAutorizacaoInternacaoHospitalar().getTpDocProfSol() != null) {
//
//            if (model.getObject().getAutorizacaoInternacaoHospitalar().getTpDocProfSol()
//                    .equals(Aih.TipoDocumento.CNS.getValue())) {
//                response.render(JavaScriptHeaderItem.forScript("$('#" + nroDocProfSol.getMarkupId() + "').mask('999.9999.9999.9999');", ""));
//
//                response.render(JavaScriptHeaderItem.forScript("$(document).ready(function(){\n"
//                        + "$('#" + nroDocProfSol.getMarkupId() + "').mask('999.9999.9999.9999');"
//                        + "});", ""));
//            }
//
//            if (model.getObject().getAutorizacaoInternacaoHospitalar().getTpDocProfSol()
//                    .equals(Aih.TipoDocumento.CPF.getValue())) {
//                response.render(JavaScriptHeaderItem.forScript("$('#" + nroDocProfSol.getMarkupId() + "').mask('999.999.999-99');", ""));
//
//                response.render(JavaScriptHeaderItem.forScript("$(document).ready(function(){\n"
//                        + "$('#" + nroDocProfSol.getMarkupId() + "').mask('999.999.999-99');"
//                        + "});", ""));
//            }
//        }
    }
}
