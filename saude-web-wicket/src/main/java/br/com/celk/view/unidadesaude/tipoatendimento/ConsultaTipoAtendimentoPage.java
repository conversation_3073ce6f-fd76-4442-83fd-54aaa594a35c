package br.com.celk.view.unidadesaude.tipoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.atendimento.tipoatendimento.customize.CustomizeConsultaTipoAtendimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroTipoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoProcedimentoTabela;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoImpressaoProntuario;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaTipoAtendimentoPage extends ConsultaPage<TipoAtendimento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long tipoAtendimento;
    private GrupoAtendimentoCbo grupoAtendimentoCbo;
    private Long situacao;

    public ConsultaTipoAtendimentoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSituacao());
        situacao = TipoAtendimento.Situacao.ATIVO.value();
    }

    private DropDown getDropDownSituacao() {
        DropDown dropDown = new DropDown("situacao");

        dropDown.addChoice(null, BundleManager.getString("ambos"));
        for (TipoAtendimento.Situacao situacao : TipoAtendimento.Situacao.values()) {
            dropDown.addChoice(situacao.value(), situacao.descricao());
        }
        return dropDown;
    }

    private DropDown getDropDownGrupo() {
        DropDown<GrupoAtendimentoCbo> dropDown = new DropDown<>("grupoAtendimentoCbo");
        dropDown.addAjaxUpdateValue();

        List<GrupoAtendimentoCbo> grupoAtendimentoCbos = LoadManager.getInstance(GrupoAtendimentoCbo.class)
                .addSorter(new QueryCustom.QueryCustomSorter(GrupoAtendimentoCbo.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
        dropDown.addChoice(null, "");
        for (GrupoAtendimentoCbo grupoAtendCbo : grupoAtendimentoCbos) {
            dropDown.addChoice(grupoAtendCbo, grupoAtendCbo.getDescricao());
        }

        return dropDown;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TipoAtendimento proxy = on(TipoAtendimento.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("eSusClassificacao"), proxy.getDescricaoTipoClassificacaoEsus()));
        columns.add(createColumn(bundle("eSusTipoAtendimento"), proxy.getDescricaoTipoAtendimentoEsus()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<TipoAtendimento>() {
            @Override
            public void customizeColumn(final TipoAtendimento rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TipoAtendimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento atendimento) throws ValidacaoException, DAOException {
                        CadastroTipoAtendimentoDTO dto = new CadastroTipoAtendimentoDTO();
                        dto.setTipoAtendimento(rowObject);
                        setResponsePage(new CadastroTipoAtendimentoPage(dto, false));
                    }
                }).setEnabled(TipoAtendimento.Situacao.ATIVO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CLONAR, rowObject, new IModelAction<TipoAtendimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento tipoAtendimento) throws ValidacaoException, DAOException {
                        clonar(tipoAtendimento);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoAtendimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento atendimento) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AtendimentoFacade.class).removerTipoAtendimento(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setEnabled(TipoAtendimento.Situacao.ATIVO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TipoAtendimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento atendimento) throws ValidacaoException, DAOException {
                        CadastroTipoAtendimentoDTO dto = new CadastroTipoAtendimentoDTO();
                        dto.setTipoAtendimento(rowObject);
                        setResponsePage(new CadastroTipoAtendimentoPage(dto, true));
                    }
                });
                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<TipoAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento modelObject) throws ValidacaoException, DAOException {
                        rowObject.setSituacao(TipoAtendimento.Situacao.INATIVO.value());
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(TipoAtendimento.Situacao.ATIVO.value().equals(rowObject.getSituacao()));
                addAction(ActionType.REATIVAR, rowObject, new IModelAction<TipoAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento modelObject) throws ValidacaoException, DAOException {
                        rowObject.setSituacao(TipoAtendimento.Situacao.ATIVO.value());
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(TipoAtendimento.Situacao.INATIVO.value().equals(rowObject.getSituacao()));
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTipoAtendimento()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoAtendimento.PROP_DESCRICAO, true);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (grupoAtendimentoCbo != null) {
                            StringBuilder where = new StringBuilder();
                            where.append("EXISTS(");
                            where.append("SELECT 1 ")
                                    .append("FROM EloTipoAtendimentoGrupoAtendimentoCbo elo ")
                                    .append("JOIN elo.grupoAtendimentoCbo grupo ")
                                    .append("JOIN elo.tipoAtendimento tipoAtendimento ")
                                    .append("WHERE grupo.codigo = ").append(grupoAtendimentoCbo.getCodigo())
                                    .append(" AND tipoAtendimento.codigo = ").append(alias).append(".codigo");
                            where.append(")");
                            hql.addToWhereWhithAnd(where.toString());
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimento.PROP_SITUACAO), situacao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoAtendimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoAtendimento");
    }

    private void clonar(TipoAtendimento tipoAtendimento) {

        TipoAtendimento newAtendimento = VOUtils.cloneObject(tipoAtendimento);
        newAtendimento.setDescricao(null);

        List<EloTipoAtendimentoGrupoAtendimentoCbo> lista = LoadManager.getInstance(EloTipoAtendimentoGrupoAtendimentoCbo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EloTipoAtendimentoGrupoAtendimentoCbo.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EloTipoAtendimentoGrupoAtendimentoCbo.PROP_GRUPO_ATENDIMENTO_CBO, GrupoAtendimentoCbo.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        List<EloTipoAtendimentoGrupoAtendimentoCbo> gruposNovos = new ArrayList();

        for (EloTipoAtendimentoGrupoAtendimentoCbo grupoAntigo : lista) {
            EloTipoAtendimentoGrupoAtendimentoCbo novoGrupo = VOUtils.cloneObject(grupoAntigo);
            novoGrupo.setTipoAtendimento(newAtendimento);

            gruposNovos.add(novoGrupo);
        }

        List<TipoAtendimentoProcedimento> listaProcedimentos = LoadManager.getInstance(TipoAtendimentoProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimentoProcedimento.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                .start().getList();

        List<TipoAtendimentoProcedimento> procedimentosNovos = new ArrayList();

        for (TipoAtendimentoProcedimento old_ : listaProcedimentos) {
            TipoAtendimentoProcedimento new_ = VOUtils.cloneObject(old_);
            new_.setTipoAtendimento(newAtendimento);

            procedimentosNovos.add(new_);
        }

        List<TipoAtendimentoProcedimentoTabela> tipoAtendimentoProcedimentoTabelaList = LoadManager.getInstance(TipoAtendimentoProcedimentoTabela.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimentoProcedimentoTabela.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                .start().getList();

        List<TipoAtendimentoProcedimentoTabela> tipoAtendimentoProcedimentoTabelaNovos = new ArrayList();

        for (TipoAtendimentoProcedimentoTabela old_ : tipoAtendimentoProcedimentoTabelaList) {
            TipoAtendimentoProcedimentoTabela new_ = VOUtils.cloneObject(old_);
            new_.setTipoAtendimento(newAtendimento);

            tipoAtendimentoProcedimentoTabelaNovos.add(new_);
        }

        List<TipoAtendimentoImpressaoProntuario> tipoAtendimentoImpressaoProntuarioList = LoadManager.getInstance(TipoAtendimentoImpressaoProntuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimentoImpressaoProntuario.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                .start().getList();

        List<TipoAtendimentoImpressaoProntuario> tipoAtendimentoImpressaoProntuarioNovos = new ArrayList();

        for (TipoAtendimentoImpressaoProntuario old_ : tipoAtendimentoImpressaoProntuarioList) {
            TipoAtendimentoImpressaoProntuario new_ = VOUtils.cloneObject(old_);
            new_.setTipoAtendimento(newAtendimento);

            tipoAtendimentoImpressaoProntuarioNovos.add(new_);
        }

        CadastroTipoAtendimentoDTO dto = new CadastroTipoAtendimentoDTO();
        dto.setTipoAtendimento(newAtendimento);
        dto.setGrupos(gruposNovos);
        dto.setProcedimentos(procedimentosNovos);
        dto.setTipoAtendimentoProcedimentoTabelaList(tipoAtendimentoProcedimentoTabelaNovos);
        dto.setTipoAtendimentoImpressaoProntuarioList(tipoAtendimentoImpressaoProntuarioNovos);
        setResponsePage(new CadastroTipoAtendimentoPage(dto));
    }
}
