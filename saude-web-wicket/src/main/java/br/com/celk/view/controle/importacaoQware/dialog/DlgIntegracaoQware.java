package br.com.celk.view.controle.importacaoQware.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by sulivan on 14/06/17.
 */
public abstract class DlgIntegracaoQware extends Window {

    private PnlIntegracaoQware pnlIntegracaoQWare;

    public DlgIntegracaoQware(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("dados"));

        setInitialWidth(500);
        setInitialHeight(200);
        setResizable(true);

        setContent(pnlIntegracaoQWare = new PnlIntegracaoQware(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgIntegracaoQware.this.onConfirmar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }

    public void onFechar(AjaxRequestTarget target) {
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
        DlgIntegracaoQware.this.onFechar(target);
    }
}

