package br.com.celk.view.unidadesaude.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.doenca.interfaces.dto.QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam;
import br.com.celk.view.basico.doenca.autocomplete.AutoCompleteConsultaDoenca;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.siab.relatorios.RelatorioAcompanhamentoCadastroFamiliasPage;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.esus.TipoAtividadeTema;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioPacienteCondicaoSituacaoSaudePage extends RelatorioPage<QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private DropDown dropDownTipoAtendimento;
    private DropDown dropDowntemaAtividadeGrupo;
    private CheckBoxLongValue checkBoxVisualizarUltimoAtendimento;
    private CheckBoxLongValue checkBoxParticipaAtividadeGrupo;
    private RequiredPnlChoicePeriod dataPeriodo;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaDoenca("doenca"));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA));
        form.add(checkBoxVisualizarUltimoAtendimento = new CheckBoxLongValue("imprimirDataultimoAtendimento"));
        checkBoxVisualizarUltimoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                dropDownTipoAtendimento.limpar(art);
                if (RepositoryComponentDefault.SIM_LONG.equals(checkBoxVisualizarUltimoAtendimento.getComponentValue())) {
                    dropDownTipoAtendimento.setEnabled(true);
                } else {
                    dropDownTipoAtendimento.setEnabled(false);
                }
                art.add(dropDownTipoAtendimento);
            }
        });

        form.add(checkBoxParticipaAtividadeGrupo = new CheckBoxLongValue("participaAtividadeGrupoTema"));
        checkBoxParticipaAtividadeGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                dropDowntemaAtividadeGrupo.limpar(art);
                if (RepositoryComponentDefault.SIM_LONG.equals(checkBoxParticipaAtividadeGrupo.getComponentValue())) {
                    dropDowntemaAtividadeGrupo.setEnabled(true);
                    dataPeriodo.setEnabled(true);
                } else {
                    dropDowntemaAtividadeGrupo.setEnabled(false);
                    dataPeriodo.setEnabled(false);
                }
                art.add(dropDowntemaAtividadeGrupo);
                art.add(dataPeriodo);
            }
        });


        form.add(getTemaAtividadeGrupo());
        form.add(getDropDownTipoAtendimento());

        form.add(dataPeriodo = new RequiredPnlChoicePeriod("periodo"));
        dataPeriodo.setEnabled(false);

        try {
            form.add(dropDownArea = createDropDownArea());
            form.add(dropDownMicroArea = new DropDown<EquipeMicroArea>("equipeMicroArea"));
            dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        form.add(DropDownUtil.getIEnumDropDown("formaApresentacao", QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam.FormaApresentacao.values()));
    }
    
    public DropDown getDropDownTipoAtendimento() {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown("tipoAtendimento");
            dropDownTipoAtendimento.addChoice(null, bundle("todos"));

            List<TipoAtendimento> listTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoAtendimento.PROP_DESCRICAO))
                    .start().getList();

            for (TipoAtendimento tipoAtendimento : listTipoAtendimento) {
                dropDownTipoAtendimento.addChoice(tipoAtendimento, tipoAtendimento.getDescricao());
            }
        }
        dropDownTipoAtendimento.setEnabled(false);

        return dropDownTipoAtendimento;
    }

    public DropDown getTemaAtividadeGrupo(){
        if (dropDowntemaAtividadeGrupo == null) {
            dropDowntemaAtividadeGrupo = new DropDown("tipoAtividadeTema");
            dropDowntemaAtividadeGrupo.addChoice(null, bundle("todos"));

            List<TipoAtividadeTema> listTipoAtividadeTema = LoadManager.getInstance(TipoAtividadeTema.class)
                    .addProperty(VOUtils.montarPath(TipoAtividadeTema.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(TipoAtividadeTema.PROP_DESCRICAO_TEMA_ATIVIDADE))
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoAtividadeTema.PROP_DESCRICAO_TEMA_ATIVIDADE))
                    .start().getList();

            for (TipoAtividadeTema tipoAtividadeGrupo : listTipoAtividadeTema) {
                dropDowntemaAtividadeGrupo.addChoice(tipoAtividadeGrupo, tipoAtividadeGrupo.getDescricaoTemaAtividade());
            }
        }
        dropDowntemaAtividadeGrupo.setEnabled(false);

        return dropDowntemaAtividadeGrupo;
    }



    @Override
    public Class<QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam> getDTOParamClass() {
        return QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioPacienteCondicaoSituacaoSaudeDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(BasicoReportFacade.class).relacaoPacientesComorbidade(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioPacienteCondicaoSituacaoSaude");
    }

    private DropDown<EquipeArea> createDropDownArea() throws DAOException, ValidacaoException {
        DropDown<EquipeArea> dropDown = new DropDown("area");

        LoadManager loadManager = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO));

        final List<Long> empresas = new ArrayList();
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            empresas.add(((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getCodigo());
        } else {
            if (!isActionPermitted(Permissions.EMPRESA)) {
                try {
                    empresas.addAll(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario()));
                } catch (SGKException ex) {
                    Logger.getLogger(RelatorioAcompanhamentoCadastroFamiliasPage.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }

        if (!empresas.isEmpty()) {
            loadManager.addInterceptor(new LoadInterceptor() {
                @Override
                public void customHQL(HQLHelper hql, String alias) {
                    HQLHelper exists = hql.getNewInstanceSubQuery();
                    exists.addToSelect("1");
                    exists.addToFrom("EquipeMicroArea ema JOIN ema.equipeProfissional ep JOIN ep.equipe e JOIN e.empresa emp");
                    exists.addToWhereWhithAnd("ema.equipeArea.codigo = " + alias + ".codigo");
                    exists.addToWhereWhithAnd("emp.codigo in ", empresas);
                    hql.addToWhereWhithAnd("exists(" + exists.getQuery() + ")");
                }
            });
        }

        List<EquipeArea> list = loadManager.start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }

                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }
}
