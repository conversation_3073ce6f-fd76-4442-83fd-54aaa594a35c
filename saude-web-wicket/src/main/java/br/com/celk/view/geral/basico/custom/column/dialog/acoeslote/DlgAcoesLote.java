package br.com.celk.view.geral.basico.custom.column.dialog.acoeslote;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public abstract class DlgAcoesLote extends Window {

     private PnlAcoesLote pnlAcoesLote;
     private IntegracaoMobile item;

     protected DlgAcoesLote(String id, IntegracaoMobile item) {
          super(id);
          this.item = item;
          init();
     }

     private void init() {

          setInitialHeight(375);
          setInitialWidth(900);
          setResizable(false);
          setTitle(BundleManager.getString("detalhesLote"));

          setContent(pnlAcoesLote = new PnlAcoesLote(getContentId(), item) {

               @Override
               public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    close(target);
                    DlgAcoesLote.this.onFechar(target);

               }
          });

          setCloseButtonCallback(new CloseButtonCallback() {

               @Override
               public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                    try {
                         DlgAcoesLote.this.onFechar(target);
                    } catch (ValidacaoException | DAOException ex) {
                         Loggable.log.error(ex.getMessage(), ex);
                    }
                    return true;
               }
          });

          formModals.setMultiPart(true);

     }

     public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

     public void show(AjaxRequestTarget target) {
          super.show(target);
     }
}
