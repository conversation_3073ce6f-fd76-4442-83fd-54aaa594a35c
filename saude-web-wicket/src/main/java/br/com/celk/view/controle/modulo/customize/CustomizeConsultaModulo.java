package br.com.celk.view.controle.modulo.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.Modulo;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaModulo extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("nome"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Modulo.PROP_NOME), QueryParameter.LIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(Modulo.PROP_CODIGO));
        properties.put(BundleManager.getString("nome"), VOUtils.montarPath(Modulo.PROP_NOME));
    }

    @Override
    public Class getClassConsulta() {
        return Modulo.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(Modulo.class).getProperties();
    }

}
