package br.com.celk.view.vigilancia.configuracao;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.DiaMesField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.ComponentWicketUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroConfiguracaoVigilanciaDTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.OnChangeAjaxBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by maicon on 20/05/16.
 */
public class ConfiguracaoVigilanciaLicencaTransporteTab extends TabPanel<CadastroConfiguracaoVigilanciaDTO> {

    private String proximoProtocolo;
    private LongField iniciarProtocoloLicencaTransporte;
    private Label lbProximoProtocolo;
    private CadastroConfiguracaoVigilanciaDTO configuracaoVigilanciaDTO;

    private DropDown<Long> ddDataFixaLicenca;

    private WebMarkupContainer containerLicencaTransporteAnosValidade;
    private DropDown licencaTransporteAnosValidade;

    private WebMarkupContainer containerDataBase;
    private DropDown<Long> ddDataBase;

    private WebMarkupContainer containerPeriodoValidadeLicenca;
    private DropDown<Long> ddValidadeLicencaPeriodo;

    private WebMarkupContainer containerDataVencimento;
    private DiaMesField dataVencimentoLicenca;

    private WebMarkupContainer containerPeriodoSolicitacaoLicenca;
    private DiaMesField dataInicialLicTransp;
    private DiaMesField dataFinalLicTransp;

    private WebMarkupContainer containerDataVencimentoAtividadeEstabelecimento;
    private DropDown<Long> ddDataVencimentoAtividadeEstabelecimento;


    public ConfiguracaoVigilanciaLicencaTransporteTab(String id, CadastroConfiguracaoVigilanciaDTO object) {
        super(id, object);
        this.configuracaoVigilanciaDTO = object;
        init();
    }

    private void init() {
        CadastroConfiguracaoVigilanciaDTO proxy = on(CadastroConfiguracaoVigilanciaDTO.class);

        criarDataFixa(proxy);
        criarDataBase(proxy);
        criarPeriodoValidadeLicenca(proxy);
        criarDataVencimentoAtividadeEstabelecimento(proxy);

        add(new LongField(path(proxy.getConfiguracaoVigilancia().getIniciarProtocoloLicencaTransporteAnoAnterior())));

        add(iniciarProtocoloLicencaTransporte = new LongField(path(proxy.getConfiguracaoVigilancia().getIniciarProtocoloLicencaTransporte())));
        iniciarProtocoloLicencaTransporte.addAjaxUpdateValue();
        iniciarProtocoloLicencaTransporte.setInitialValue(0L);
        iniciarProtocoloLicencaTransporte.setConvertZeroToNull(false);

        add(lbProximoProtocolo = new Label("proximoProtocolo", new PropertyModel<String>(this, "proximoProtocolo")));
        lbProximoProtocolo.setOutputMarkupId(true);
        
        add(new InputField(path(proxy.getConfiguracaoVigilancia().getAnoBaseLicencaTransporte())));
        add(new InputField(path(proxy.getConfiguracaoVigilancia().getConcedido())).add(new Tooltip().setText("msgConcedidoToolTip")));
        add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getConfiguracaoVigilancia().getObrigatoridadeRefrigerado()), true));
        add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getConfiguracaoVigilancia().getFlagObrigaDadosVeiculo())).add(new Tooltip().setText("msgObrigatorioInformarVeiculoCadastroEstabelecimento")));

        updateContainersDataFixa(null);
    }

    private void criarDataFixa(CadastroConfiguracaoVigilanciaDTO proxy) {
        ddDataFixaLicenca = DropDownUtil.getNaoSimLongDropDown(path(proxy.getConfiguracaoVigilancia().getValidadeLicencaDataFixa()));
        ddDataFixaLicenca.add(new OnChangeAjaxBehavior() {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateContainersDataFixa(target);
            }
        });

        containerDataVencimento = new WebMarkupContainer("containerDataVencimento");
        containerDataVencimento.setOutputMarkupPlaceholderTag(true);

        dataVencimentoLicenca = new DiaMesField(path(proxy.getConfiguracaoVigilancia().getValidadeLicencaDataVencimento()));
        dataVencimentoLicenca.setLabel(Model.of(BundleManager.getString("dataVencimentoValidadeLicenca")));
        containerDataVencimento.add(dataVencimentoLicenca);

        containerLicencaTransporteAnosValidade = new WebMarkupContainer("containerLicencaTransporteAnosValidade");
        containerLicencaTransporteAnosValidade.setOutputMarkupPlaceholderTag(true);
        licencaTransporteAnosValidade = DropDownUtil.getIEnumDropDown(path(proxy.getConfiguracaoVigilancia().getLicencaTransporteAnosValidade()), ConfiguracaoVigilanciaEnum.QuantidadeAnosSomarValidadeAlvaraLicenca.values(), true, true);
        licencaTransporteAnosValidade.add(new Tooltip().setText("infoPeriodoSomarValidade"));
        containerLicencaTransporteAnosValidade.add(licencaTransporteAnosValidade);

        containerPeriodoSolicitacaoLicenca = new WebMarkupContainer("containerPeriodoSolicitacaoLicenca");
        containerPeriodoSolicitacaoLicenca.setOutputMarkupPlaceholderTag(true);

        dataInicialLicTransp = new DiaMesField(path(proxy.getConfiguracaoVigilancia().getDataInicialLicencaTransporte()));
        dataInicialLicTransp.setLabel(Model.of(BundleManager.getString("dataInicial")));
        dataFinalLicTransp = new DiaMesField(path(proxy.getConfiguracaoVigilancia().getDataFinalLicencaTransporte()));
        dataFinalLicTransp.setLabel(Model.of(BundleManager.getString("dataFinal")));
        dataFinalLicTransp.add(new Tooltip().setText("msgPeriodoSolicitacaoRequerimentoLicencaSanitaria"));

        containerPeriodoSolicitacaoLicenca.add(dataInicialLicTransp, dataFinalLicTransp);
        add(ddDataFixaLicenca, containerDataVencimento, containerLicencaTransporteAnosValidade, containerPeriodoSolicitacaoLicenca);
    }

    private void criarDataBase(CadastroConfiguracaoVigilanciaDTO proxy) {
        containerDataBase = new WebMarkupContainer("containerDataBase");
        containerDataBase.setVisible(RepositoryComponentDefault.NAO_LONG.equals(configuracaoVigilanciaDTO.getConfiguracaoVigilancia().getValidadeLicencaDataFixa()));
        containerDataBase.setOutputMarkupId(true);
        containerDataBase.setOutputMarkupPlaceholderTag(true);

        ddDataBase = DropDownUtil.getIEnumDropDown(path(proxy.getConfiguracaoVigilancia().getTipoDataCalcLicencaTransporte()), ConfiguracaoVigilanciaEnum.TipoDataBaseLicencaTransporte.values(), true, !isDataFixa());
        ddDataBase.setLabel(Model.of(BundleManager.getString("dataBase")));
        ddDataBase.add(new Tooltip().setText("msgDataValidadeLicencaDeAcordoCom1aSolicitacaoOucomCadRequerimento"));
        ddDataBase.add(new OnChangeAjaxBehavior() {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateContainersDataBase(target);
            }
        });

        containerDataBase.add(ddDataBase);
        add(containerDataBase);
    }

    private void criarPeriodoValidadeLicenca(CadastroConfiguracaoVigilanciaDTO proxy) {
        containerPeriodoValidadeLicenca = new WebMarkupContainer("containerPeriodoValidadeLicenca");
        containerPeriodoValidadeLicenca.setOutputMarkupPlaceholderTag(true);

        ddValidadeLicencaPeriodo = DropDownUtil.getIEnumDropDown(path(proxy.getConfiguracaoVigilancia().getValidadeLicencaPeriodo()), ConfiguracaoVigilanciaEnum.PeriodoValidadeAlvarasLicencas.values(), true, !isDataFixa());
        ddValidadeLicencaPeriodo.setLabel(Model.of(BundleManager.getString("dataVencimentoValidadeLicenca")));
        containerPeriodoValidadeLicenca.add(ddValidadeLicencaPeriodo);

        add(containerPeriodoValidadeLicenca);
    }

    private void criarDataVencimentoAtividadeEstabelecimento(CadastroConfiguracaoVigilanciaDTO proxy) {
        containerDataVencimentoAtividadeEstabelecimento = new WebMarkupContainer("containerDataVencimentoAtividadeEstabelecimento");
        containerDataVencimentoAtividadeEstabelecimento.setOutputMarkupPlaceholderTag(true);

        ddDataVencimentoAtividadeEstabelecimento = DropDownUtil.getNaoSimLongDropDown(path(proxy.getConfiguracaoVigilancia().getLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento()));
        ddDataVencimentoAtividadeEstabelecimento.add(new Tooltip().setText("msgDataVencimentoLicencaTransporteAtividadeEstabelecimento"));

        ddDataVencimentoAtividadeEstabelecimento.add(new OnChangeAjaxBehavior() {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateContainersAtividadeEstabelecimento(target);
            }
        });

        containerDataVencimentoAtividadeEstabelecimento.add(ddDataVencimentoAtividadeEstabelecimento);
        add(containerDataVencimentoAtividadeEstabelecimento);
    }

    private void updateContainersDataFixa(AjaxRequestTarget target) {
        containerDataVencimentoAtividadeEstabelecimento.setVisible(isDataFixa());
        containerDataBase.setVisible(!isDataFixa());
        containerPeriodoValidadeLicenca.setVisible(!isDataFixa());

        containerPeriodoSolicitacaoLicenca.setVisible(isDataFixa());
        containerPeriodoSolicitacaoLicenca.setEnabled(isDataFixa());

        licencaTransporteAnosValidade.add(new Tooltip().setText("infoPeriodoSomarValidade"));
        if (isDataFixa()) {
            ddDataBase.add(new Tooltip().setText("msgDataValidadeLicencaDeAcordoCom1aSolicitacaoOucomCadRequerimento"));
            ddDataVencimentoAtividadeEstabelecimento.add(new Tooltip().setText("msgDataVencimentoLicencaTransporteAtividadeEstabelecimento"));
            dataFinalLicTransp.add(new Tooltip().setText("msgPeriodoSolicitacaoRequerimentoLicencaSanitaria"));
        }

        updateContainersAtividadeEstabelecimento(target);

        ComponentWicketUtil.addOrRemoveRequired(isDataFixa(), dataInicialLicTransp);
        ComponentWicketUtil.addOrRemoveRequired(isDataFixa(), dataFinalLicTransp);
        ComponentWicketUtil.addOrRemoveRequired(!isDataFixa(), ddDataBase);
        ComponentWicketUtil.addOrRemoveRequired(!isDataFixa(), ddValidadeLicencaPeriodo);

        if (target != null) {
            ddDataVencimentoAtividadeEstabelecimento.limpar(target);
            licencaTransporteAnosValidade.limpar(target);
            dataInicialLicTransp.limpar(target);
            dataFinalLicTransp.limpar(target);
            ddDataBase.limpar(target);
            ddValidadeLicencaPeriodo.limpar(target);

            target.add(ddDataVencimentoAtividadeEstabelecimento, dataInicialLicTransp, dataFinalLicTransp, ddDataBase, ddValidadeLicencaPeriodo, licencaTransporteAnosValidade);
            target.add(containerPeriodoValidadeLicenca, containerLicencaTransporteAnosValidade, containerPeriodoSolicitacaoLicenca, containerDataBase, containerDataVencimentoAtividadeEstabelecimento);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void updateContainersAtividadeEstabelecimento(AjaxRequestTarget target) {
        containerDataVencimento.setVisible(isDataFixa());
        containerDataVencimento.setEnabled(isDataFixa() && !isUsingDataAtividadeEstabelecimento());

        ComponentWicketUtil.addOrRemoveRequired(isDataFixa() && !isUsingDataAtividadeEstabelecimento(), dataVencimentoLicenca);
        if (target != null) {
            dataVencimentoLicenca.limpar(target);
            licencaTransporteAnosValidade.limpar(target);
            target.add(dataVencimentoLicenca, licencaTransporteAnosValidade, containerDataVencimento);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void updateContainersDataBase(AjaxRequestTarget target) {
        if (target != null) {
            licencaTransporteAnosValidade.limpar(target);
            ddValidadeLicencaPeriodo.limpar(target);
            target.add(licencaTransporteAnosValidade, ddValidadeLicencaPeriodo);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private boolean isUsingDataAtividadeEstabelecimento() {
        return (ddDataVencimentoAtividadeEstabelecimento != null && ddDataVencimentoAtividadeEstabelecimento.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(ddDataVencimentoAtividadeEstabelecimento)) ||
                (object != null && object.getConfiguracaoVigilancia() != null && RepositoryComponentDefault.SIM_LONG.equals(object.getConfiguracaoVigilancia().getLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento())) ||
                (configuracaoVigilanciaDTO != null && configuracaoVigilanciaDTO.getConfiguracaoVigilancia() != null && RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilanciaDTO.getConfiguracaoVigilancia().getLicencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento()));
    }

    public boolean isDataFixa() {
        return (ddDataFixaLicenca != null && ddDataFixaLicenca.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(ddDataFixaLicenca.getComponentValue())) ||
                (object != null && object.getConfiguracaoVigilancia() != null && RepositoryComponentDefault.SIM_LONG.equals(object.getConfiguracaoVigilancia().getValidadeLicencaDataFixa())) ||
                (configuracaoVigilanciaDTO != null && configuracaoVigilanciaDTO.getConfiguracaoVigilancia() != null && RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilanciaDTO.getConfiguracaoVigilancia().getValidadeLicencaDataFixa()))
                ;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("licTransporte");
    }
}
