package br.com.celk.view.frota.veiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.*;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.frota.Veiculo;
import br.com.ksisolucoes.vo.frota.VeiculoEmpresa;
import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroVeiculoEstabelecimentoPage extends BasePage {

    private Form form;
    private final Veiculo veiculo;

    private Table<VeiculoEmpresa> tblEmpresas;
    private List<VeiculoEmpresa> lstVeiculoEmpresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa empresa;

    public CadastroVeiculoEstabelecimentoPage(Veiculo veiculo) {
        this.veiculo = veiculo;
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField("placa", new PropertyModel(veiculo, "placa")).setEnabled(false));
        form.add(new InputField("descricao", new PropertyModel(veiculo, "descricao")).setEnabled(false));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        lstVeiculoEmpresa = LoadManager.getInstance(VeiculoEmpresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VeiculoEmpresa.PROP_VEICULO, Veiculo.PROP_CODIGO), veiculo.getCodigo()))
                .start().getList();
        if (lstVeiculoEmpresa == null) {
            lstVeiculoEmpresa = new ArrayList<VeiculoEmpresa>();
        }

        form.add(tblEmpresas = new Table("tblEmpresas", getColumns(), getCollectionProvider()));
        tblEmpresas.populate();

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(form);
    }

    private void salvar() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(VeiculoEmpresa.class, lstVeiculoEmpresa, new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VeiculoEmpresa.PROP_VEICULO, Veiculo.PROP_CODIGO), veiculo.getCodigo()));

        Page page = new ConsultaVeiculoPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
    }

    private void remover(AjaxRequestTarget target, VeiculoEmpresa object) {
        lstVeiculoEmpresa.remove(object);
        tblEmpresas.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        VeiculoEmpresa proxy = on(VeiculoEmpresa.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getEmpresa().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstVeiculoEmpresa;
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<VeiculoEmpresa>() {

            @Override
            public void customizeColumn(VeiculoEmpresa rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<VeiculoEmpresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, VeiculoEmpresa modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }

                });
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if(empresa == null){
                throw new ValidacaoException(bundle("informeEstabelecimento"));
        }
        for (VeiculoEmpresa ve : lstVeiculoEmpresa) {
            if (ve.getEmpresa().equals(empresa)) {
                autoCompleteConsultaEmpresa.limpar(target);
                throw new ValidacaoException(bundle("itemJaAdicionado"));
            }
        }
        VeiculoEmpresa ve = new VeiculoEmpresa();
        ve.setEmpresa(empresa);
        ve.setVeiculo(veiculo);
        lstVeiculoEmpresa.add(ve);
        tblEmpresas.update(target);
        autoCompleteConsultaEmpresa.limpar(target);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("veiculoXestabelecimento");
    }

}
