package br.com.celk.view.controle.grupo.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.Grupo;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AutoCompleteConsultaGrupo extends AutoCompleteConsulta<Grupo> {

    public AutoCompleteConsultaGrupo(String id) {
        super(id);
    }

    public AutoCompleteConsultaGrupo(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaGrupo(String id, IModel<Grupo> model) {
        super(id, model);
    }

    public AutoCompleteConsultaGrupo(String id, IModel<Grupo> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), Grupo.PROP_CODIGO);
                        properties.put(BundleManager.getString("nome"), Grupo.PROP_NOME);
                        properties.put(BundleManager.getString("utilidade"), Grupo.PROP_UTILIDADE);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("nome"), new QueryCustom.QueryCustomParameter(Grupo.PROP_NOME, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(Grupo.PROP_CODIGO));
                        filterProperties.put(BundleManager.getString("utilidade"), new QueryCustom.QueryCustomParameter(Grupo.PROP_UTILIDADE));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return Grupo.class;
                    }
                };
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(Grupo.PROP_NOME), true);
            }

            @Override
            public List getSearchParam(String searchCriteria) {
                List<BuilderQueryCustom.QueryParameter> list = new ArrayList<BuilderQueryCustom.QueryParameter>();
                list.add(new QueryCustom.QueryCustomParameter(Grupo.PROP_NOME, BuilderQueryCustom.QueryParameter.ILIKE, (searchCriteria != null ? searchCriteria.trim() : null)));
                return list;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("grupo");
    }
}