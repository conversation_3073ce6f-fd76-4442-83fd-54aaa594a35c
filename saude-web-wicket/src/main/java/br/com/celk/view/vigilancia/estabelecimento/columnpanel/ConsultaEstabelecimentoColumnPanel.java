package br.com.celk.view.vigilancia.estabelecimento.columnpanel;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaEstabelecimentoColumnPanel extends Panel implements PermissionContainer{

    private AjaxLink btnEditar;
    
    private AjaxLink btnExcluir;
    
    private AjaxLink btnConsultar;
    
//    private AjaxLink btnRequerimento;
    
    private DlgConfirmacao dlgConfirmacao;
    
    private Long situacao;
    public ConsultaEstabelecimentoColumnPanel(String id, Long situacao) {
        super(id);
        this.situacao = situacao;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onEditar(target);
            }
        });
        add(btnExcluir = new AbstractAjaxLink("btnExcluir") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao!=null) {
                    dlgConfirmacao.show(target);
                }
            }
        });
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onConsultar(target);
            }
        });
//        add(btnRequerimento = new AbstractAjaxLink("btnRequerimento") {
//
//            @Override
//            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                onRequerimento(target);
//            }
//
//            @Override
//            public boolean isEnabled() {
//                return situacao.equals((Long) Estabelecimento.Situacao.ATIVO.value());
//            }
//        });
        
        btnEditar.add(new AttributeModifier("title", BundleManager.getString("alterar")));
        btnExcluir.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
//        btnRequerimento.add(new AttributeModifier("title", BundleManager.getString("cadastroRequerimento")));
    }
    
    private void initDlgConfirmacao(AjaxRequestTarget target){
        if (dlgConfirmacao==null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteExcluir")+"?") {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    onExcluir(target);
                }
            });
        }
    }
    
    public abstract void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
//    public abstract void onRequerimento(AjaxRequestTarget target) throws ValidacaoException, DAOException;

}
