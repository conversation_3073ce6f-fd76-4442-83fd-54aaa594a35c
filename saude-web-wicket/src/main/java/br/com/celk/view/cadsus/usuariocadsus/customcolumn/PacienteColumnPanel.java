package br.com.celk.view.cadsus.usuariocadsus.customcolumn;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.cadsus.usuariocadsus.CadastroPacientePage;
import br.com.ksisolucoes.bo.appcidadao.interfaces.facade.AppCidadaoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 * <AUTHOR> Santos
 */
public abstract class PacienteColumnPanel extends Panel implements PermissionContainer {

    private AjaxLink btnEditar;
    private AjaxLink btnConsultar;
    private AjaxLink btnAtivarAppCidadao;
    private AjaxLink btnLimparSenhaAppCidadao;

    private DlgConfirmacao dlgConfirmacao;

    private UsuarioCadsus usuarioCadsus;
    private Usuario usuarioLogado;
    private Boolean permissaoEditar;
    private Boolean permissaoConsultar;
    private Boolean permissaoAppCidadao;

    public PacienteColumnPanel(String id, UsuarioCadsus usuarioCadsus, Boolean permissaoConsultar, Boolean permissaoEditar, Boolean permissaoAppCidadao) {
        super(id);
        this.usuarioCadsus = usuarioCadsus;
        this.permissaoConsultar = permissaoConsultar;
        this.permissaoEditar = permissaoEditar;
        this.permissaoAppCidadao = permissaoAppCidadao;
        usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                editar(target);
            }

        });
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                consultar(target);
            }

        });
        add(btnAtivarAppCidadao = new AbstractAjaxLink("btnAtivarAppCidadao") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                initDlgConfirmacao(target, "desejaRealmenteAtivarApp");
            }

        });
        add(btnLimparSenhaAppCidadao = new AbstractAjaxLink("btnLimparSenhaAppCidadao") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                initDlgConfirmacao(target, "desejaRealmentelimparSenhaApp");
            }

        });

        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnEditar.setEnabled(this.permissaoEditar);
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
        btnConsultar.setEnabled(this.permissaoConsultar);
        btnAtivarAppCidadao.add(new AttributeModifier("title", BundleManager.getString("ativar_app_cidadao")));
        if(this.usuarioCadsus != null && this.usuarioCadsus.isAppCidadaoAtivo() != null) {
            btnAtivarAppCidadao.setVisible(!this.usuarioCadsus.isAppCidadaoAtivo() && this.permissaoAppCidadao);
            btnLimparSenhaAppCidadao.setVisible(this.usuarioCadsus.isAppCidadaoAtivo() && this.permissaoAppCidadao);
        }
        btnLimparSenhaAppCidadao.add(new AttributeModifier("title", BundleManager.getString("apagar_senha")));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target, final String mensagemConfirmacao) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString(mensagemConfirmacao)) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    if (mensagemConfirmacao.equals("desejaRealmenteAtivarApp")) {
                        ativarAppCidadao(target);
                    } else {
                        limparSenhaAppCidadao(target);
                    }
                }

                @Override
                public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                    btnConfirmar.add(new AttributeModifier("value", BundleManager.getString("sim")));
                    btnConfirmar.add(new AttributeModifier("class", "btn-green"));
                    btnFechar.add(new AttributeModifier("value", BundleManager.getString("nao")));
                    btnFechar.add(new AttributeModifier("class", "btn-red"));
                }
            });
        }
        dlgConfirmacao.show(target);
    }

    private void ativarAppCidadao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        carregarUsuarioCadsus();

        BOFactory.getBO(AppCidadaoFacade.class).ativarAppCidadao(this.usuarioCadsus);
        updateTable(target);
    }

    private void carregarUsuarioCadsus() {
        usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperties(new HQLProperties(UsuarioCadsus.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, usuarioCadsus.getCodigo()))
                .start().getVO();

    }

    private void limparSenhaAppCidadao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        carregarUsuarioCadsus();
        BOFactory.getBO(AppCidadaoFacade.class).limparSenhaAppCidadao(this.usuarioCadsus);
    }

    private void consultar(AjaxRequestTarget target) {
        setResponsePage(new CadastroPacientePage(this.usuarioCadsus, false));
    }

    public abstract void updateTable(AjaxRequestTarget target);

    private void editar(AjaxRequestTarget target) {
        setResponsePage(new CadastroPacientePage(this.usuarioCadsus));
    }
}
