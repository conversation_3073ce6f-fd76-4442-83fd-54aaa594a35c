package br.com.celk.view.unidadesaude.atendimento.consulta.configuracaoprocessocid;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ProcessoCidNotificavelDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ProcessoCidNotificavel;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConfiguracaoProcessoCidNotificavel extends BasePage {

    private Table table;
    private ProcurarButton btnProcurar;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    private Empresa empresa;

    private DlgDataInicioProcessoCid dlgDataInicioProcessoCid;

    private List<ProcessoCidNotificavelDTO> result;

    public ConfiguracaoProcessoCidNotificavel() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));

        carregar();
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));

        form.add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }

            @Override
            public void antesProcurar(AjaxRequestTarget target) {
                carregar();
            }
        });

        form.add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(CadastroConfiguracaoProcessoCidNotificavel.class);
            }
        });

        form.add(new AbstractAjaxButton("btnProcessar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (dlgDataInicioProcessoCid == null) {
                    addModal(target, dlgDataInicioProcessoCid = new DlgDataInicioProcessoCid(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Date dataInicio) throws ValidacaoException, DAOException {
                            if (dataInicio.after(DataUtil.getDataAtual())) {
                                throw new ValidacaoException(bundle("msgDataInicioNaoPodeMaiorDataAtual"));
                            }
                            BOFactoryWicket.getBO(BasicoFacade.class).processarConfiguracoesCidNotificavel(dataInicio);
                        }
                    });
                }
                dlgDataInicioProcessoCid.show(target);
            }
        });

        add(form);
        btnProcurar.procurar();
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(ProcessoCidNotificavel.PROP_EMPRESA, empresa));

        return parameters;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ProcessoCidNotificavelDTO on = on(ProcessoCidNotificavelDTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("estabelecimento"), on.getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("usuario"), on.getNomeUsuario()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProcessoCidNotificavelDTO>() {
            @Override
            public void customizeColumn(ProcessoCidNotificavelDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ProcessoCidNotificavelDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoCidNotificavelDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConfiguracaoProcessoCidNotificavel(modelObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProcessoCidNotificavelDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoCidNotificavelDTO modelObject) throws ValidacaoException, DAOException {
                        for (Usuario u : modelObject.getUsuarios()) {
                            LoadManager load = LoadManager.getInstance(ProcessoCidNotificavel.class)
                                    .addProperties(new HQLProperties(ProcessoCidNotificavel.class).getProperties())
                                    .addParameter(new QueryCustom.QueryCustomParameter(ProcessoCidNotificavel.PROP_EMPRESA, modelObject.getEmpresa()))
                                    .addParameter(new QueryCustom.QueryCustomParameter(ProcessoCidNotificavel.PROP_USUARIO, u));

                            ProcessoCidNotificavel exists = load.start().getVO();

                            if (exists != null) {
                                BOFactoryWicket.delete(exists);
                            }
                        }
                        carregar();
                        table.populate();
                        table.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return result;
            }
        };
    }

    public void carregar() {
        LoadManager load = LoadManager.getInstance(ProcessoCidNotificavel.class)
                .addProperties(VOUtils.montarPath(ProcessoCidNotificavel.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ProcessoCidNotificavel.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ProcessoCidNotificavel.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addProperties(VOUtils.montarPath(ProcessoCidNotificavel.PROP_USUARIO, Usuario.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ProcessoCidNotificavel.PROP_USUARIO, Usuario.PROP_NOME))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ProcessoCidNotificavel.PROP_EMPRESA)))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcessoCidNotificavel.PROP_EMPRESA), empresa));

        List<ProcessoCidNotificavel> lista = load.start().getList();
        result = new ArrayList();
        for (ProcessoCidNotificavel p : lista) {
            if (CollectionUtils.isAllEmpty(result)) {
                ProcessoCidNotificavelDTO dto = new ProcessoCidNotificavelDTO();
                dto.setCodigo(p.getCodigo());
                dto.setEmpresa(p.getEmpresa());
                dto.setNomeUsuario(p.getUsuario().getNome());
                dto.setUsuarios(new ArrayList<Usuario>());
                dto.getUsuarios().add(p.getUsuario());
                result.add(dto);
            } else {
                boolean controle = true;
                for (ProcessoCidNotificavelDTO dto : result) {
                    if (dto.getEmpresa().equals(p.getEmpresa())) {
                        dto.setNomeUsuario(dto.getNomeUsuario() + ", " + p.getUsuario().getNome());
                        dto.getUsuarios().add(p.getUsuario());
                        controle = false;
                    }
                }
                if (controle) {
                    ProcessoCidNotificavelDTO dto = new ProcessoCidNotificavelDTO();
                    dto.setCodigo(p.getCodigo());
                    dto.setEmpresa(p.getEmpresa());
                    dto.setNomeUsuario(p.getUsuario().getNome());
                    dto.setUsuarios(new ArrayList<Usuario>());
                    dto.getUsuarios().add(p.getUsuario());
                    result.add(dto);
                }
            }
        }

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaConfiguracaoProcessoCid");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
