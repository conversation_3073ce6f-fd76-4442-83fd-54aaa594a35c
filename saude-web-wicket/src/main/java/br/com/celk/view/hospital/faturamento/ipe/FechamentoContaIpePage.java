package br.com.celk.view.hospital.faturamento.ipe;

import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.hospital.faturamento.ipe.tabbedpanel.*;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class FechamentoContaIpePage extends BasePage {

    private CompoundPropertyModel modelForm;
    private ContaPaciente contaPaciente;
    private Long tipoConta;
    private InputField txtCompetencia;

    public FechamentoContaIpePage(ContaPaciente contaPaciente) {
        this.contaPaciente = contaPaciente;
        this.tipoConta = contaPaciente.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoContaIpe();
        init();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("fechamentoContaPacienteIpe");
    }

    private void init() {
        Form form = new Form("form", modelForm = new CompoundPropertyModel(contaPaciente));

        ContaPaciente proxy = on(ContaPaciente.class);

        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getIdade())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNumeroRegistroConvenio())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCpfFormatado())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getDataChegada())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getLeitoQuarto().getQuartoInternacao().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getLeitoQuarto().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getConvenio().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getProfissionalAlta().getNome())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getCid().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getDataAlta())));
        form.add(new DisabledInputField(path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getMotivoFormatado())));
        form.add(txtCompetencia = new MesAnoField(path(proxy.getCompetenciaAtendimento())));
        txtCompetencia.addAjaxUpdateValue();

        adicionaTabs(form);

        add(form);
    }

    private void adicionaTabs(Form form) {
        FechamentoContaDTO fechamentoContaDTO = new FechamentoContaDTO();

        fechamentoContaDTO.setListaItensContaPaciente(carregarItensContaPaciente());
        fechamentoContaDTO.setContaPaciente(contaPaciente);

        List<ITab> tabs = new ArrayList<ITab>();

        if (!TipoAtendimento.TipoContaIpe.ATENDIMENTO_COMPLEMENTAR.value().equals(tipoConta) && !TipoAtendimento.TipoContaIpe.PRONTO_ATENDIMENTO.value().equals(tipoConta)) {

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new MedicamentosIpeTab(panelId, dto);
                }
            });

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new ServicosIpeTab(panelId, dto);
                }
            });

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new ProcedimentosIpeTab(panelId, dto);
                }
            });

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new ExamesIpeTab(panelId, dto);
                }
            });

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new HonorariosIpeTab(panelId, dto);
                }
            });
        } else if (TipoAtendimento.TipoContaIpe.ATENDIMENTO_COMPLEMENTAR.value().equals(tipoConta)) {

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new AtendimentoComplementarTab(panelId, dto);
                }
            });

        } else if (TipoAtendimento.TipoContaIpe.PRONTO_ATENDIMENTO.value().equals(tipoConta)) {

            tabs.add(new CadastroTab<FechamentoContaDTO>(fechamentoContaDTO) {
                @Override
                public ITabPanel newTabPanel(String panelId, FechamentoContaDTO dto) {
                    return new ProntoAtendimentoTab(panelId, dto);
                }
            });
        }

        form.add(new FechamentoContaIpeTabbedPanel("abas", fechamentoContaDTO, false, tabs));
    }

    private List<ItemContaPaciente> carregarItensContaPaciente() {
        ItemContaPaciente proxy = on(ItemContaPaciente.class);
        List<ItemContaPaciente> lista = LoadManager.getInstance(ItemContaPaciente.class)
                .addProperties(new HQLProperties(ItemContaPaciente.class).getProperties())
                .addProperties(new HQLProperties(ContaPaciente.class, path(proxy.getContaPaciente())).getProperties())
                .addProperty(path(proxy.getExameProcedimento().getCodigo()))
                .addProperty(path(proxy.getExameProcedimento().getDescricaoProcedimento()))
                .addProperty(path(proxy.getExameProcedimento().getProcedimento().getCodigo()))
                .addProperty(path(proxy.getProcedimento().getCodigo()))
                .addProperty(path(proxy.getProcedimento().getReferencia()))
                .addProperty(path(proxy.getProcedimento().getDescricao()))
                .addProperty(path(proxy.getProfissional().getCodigo()))
                .addProperty(path(proxy.getProfissional().getNome()))
                .addProperty(path(proxy.getProfissional().getNumeroRegistro()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getContaPaciente().getContaPacientePrincipal()), contaPaciente))
                .start().getList();

        return lista;
    }
}
