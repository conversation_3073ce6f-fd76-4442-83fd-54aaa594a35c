package br.com.celk.view.materiais.estoque.configuracaoestoqueminimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.ProcessoAvaliacaoEstoque;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConfiguracaoProcessoAvaliacaoEstoquePage extends BasePage {

    private PageableTable table;
    private ProcurarButton btnProcurar;
    private Empresa empresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Long tipoProcesso;
    private DropDown<Long> dropDownTipoProcesso;

    public ConfiguracaoProcessoAvaliacaoEstoquePage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(dropDownTipoProcesso = DropDownUtil.getIEnumDropDown("tipoProcesso", ProcessoAvaliacaoEstoque.TipoProcesso.values(), true, bundle("todos")));

        form.add(table = new PageableTable("table", getColumns(), getPagerProviderInstance()));

        form.add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(CadastroConfiguracaoPage.class);
            }
        });

        form.add(new AbstractAjaxButton("btnProcessar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(null);
            }
        });

        add(form);
        btnProcurar.procurar();
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_EMPRESA, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_TIPO_PROCESSO, tipoProcesso));

        return parameters;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ProcessoAvaliacaoEstoque on = on(ProcessoAvaliacaoEstoque.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("estabelecimento"), on.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("usuarioResponsavel"), on.getUsuario().getNome()));
        columns.add(createSortableColumn(bundle("tipoProcesso"), on.getTipoProcesso(), on.getDescricaoTipoProcesso()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProcessoAvaliacaoEstoque>() {
            @Override
            public void customizeColumn(ProcessoAvaliacaoEstoque rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ProcessoAvaliacaoEstoque>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAvaliacaoEstoque modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConfiguracaoPage(modelObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProcessoAvaliacaoEstoque>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcessoAvaliacaoEstoque modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        table.populate(target);
                    }
                });
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ProcessoAvaliacaoEstoque.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ProcessoAvaliacaoEstoque.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(ProcessoAvaliacaoEstoque.PROP_EMPRESA, Empresa.PROP_CODIGO),
                    VOUtils.montarPath(ProcessoAvaliacaoEstoque.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                    VOUtils.montarPath(ProcessoAvaliacaoEstoque.PROP_USUARIO, Usuario.PROP_CODIGO),
                    VOUtils.montarPath(ProcessoAvaliacaoEstoque.PROP_USUARIO, Usuario.PROP_NOME),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ProcessoAvaliacaoEstoque.PROP_EMPRESA, Empresa.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaConfiguracaoProcesso");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
