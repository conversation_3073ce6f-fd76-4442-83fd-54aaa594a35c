package br.com.celk.view.vigilancia.requerimentos.panel;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoInscricaoImobiliariaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaInscricaoImob;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class PnlInscricaoImobiliaria extends Panel {

    private Form form;
    private InputField<String> txtInscricaoImobiliaria;
    private Table tblInscricaoImobiliaria;
    private String numeroInscricaoImobiliaria;
    private AbstractAjaxButton btnAdicionarInscricaoImobiliaria;
    private boolean modoEdicao;
    private RequerimentoInscricaoImobiliariaDTO dto;


    public PnlInscricaoImobiliaria(String id, boolean modoEdicao, RequerimentoInscricaoImobiliariaDTO dto) {
        super(id);
        this.modoEdicao = modoEdicao;
        this.dto = dto;
        init();
    }

    protected void init() {
        form = new Form("form", new CompoundPropertyModel<>(new RequerimentoInscricaoImobiliariaDTO()));
        form.setOutputMarkupId(true);

        txtInscricaoImobiliaria = new InputField("numeroInscricaoImobiliaria", new PropertyModel(this, "numeroInscricaoImobiliaria"));
        txtInscricaoImobiliaria.setLabel(Model.of(bundle("nInscricaoImobiliaria")));
        txtInscricaoImobiliaria.setEnabled(isModoEdicao());
        txtInscricaoImobiliaria.addAjaxUpdateValue();

        tblInscricaoImobiliaria = new Table("tblInscricaoImobiliaria", getColumnsInscricaoImobiliaria(), getCollectionProviderInscricaoImobiliaria());
        tblInscricaoImobiliaria.setScrollY("180px");
        tblInscricaoImobiliaria.populate();
        tblInscricaoImobiliaria.setOutputMarkupPlaceholderTag(true);
        tblInscricaoImobiliaria.setOutputMarkupId(true);
        tblInscricaoImobiliaria.getAjaxRegionMarkupId();
        tblInscricaoImobiliaria.setEnabled(isModoEdicao());

        btnAdicionarInscricaoImobiliaria = new AbstractAjaxButton("btnAdicionarInscricao") {
            @Override
            public void onAction(
                    AjaxRequestTarget target,
                    Form form
            ) throws ValidacaoException, DAOException {
                adicionarInscricaoImobiliaria(target);
            }
        };
        btnAdicionarInscricaoImobiliaria.setDefaultFormProcessing(false);
        btnAdicionarInscricaoImobiliaria.setEnabled(isModoEdicao());

        form.add(
                tblInscricaoImobiliaria, btnAdicionarInscricaoImobiliaria, txtInscricaoImobiliaria
        );

        add(form);
    }

    private void adicionarInscricaoImobiliaria(AjaxRequestTarget target) throws ValidacaoException {
        if (txtInscricaoImobiliaria.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("informeNumeroInscricaoImobiliaria"));
        }

        if (CollectionUtils.isNotNullEmpty(dto.getListInscricaoImobiliaria())) {
            for (RequerimentoVigilanciaInscricaoImob insc : dto.getListInscricaoImobiliaria()) {
                if (insc.getNumeroInscricaoImobiliaria().equals(txtInscricaoImobiliaria.getComponentValue())) {
                    throw new ValidacaoException(BundleManager.getString("inscricaoImobiliariaJaAdicionada"));
                }
            }
        }

        RequerimentoVigilanciaInscricaoImob inscricao = new RequerimentoVigilanciaInscricaoImob();
        inscricao.setNumeroInscricaoImobiliaria(txtInscricaoImobiliaria.getComponentValue());
        dto.getListInscricaoImobiliaria().add(inscricao);

        if (CollectionUtils.isNotNullEmpty(dto.getListInscricaoImobiliaria())) {
            txtInscricaoImobiliaria.setRequired(false);
            txtInscricaoImobiliaria.removeRequiredClass();
            target.add(txtInscricaoImobiliaria);
        }

        tblInscricaoImobiliaria.populate(target);
        txtInscricaoImobiliaria.limpar(target);
    }

    private void removerInscricaoImobiliaria(
            AjaxRequestTarget target,
            RequerimentoVigilanciaInscricaoImob rowObject
    ) throws ValidacaoException, DAOException {

        for (int i = 0; i < dto.getListInscricaoImobiliaria().size(); i++) {
            RequerimentoVigilanciaInscricaoImob insc = dto.getListInscricaoImobiliaria().get(i);
            if (insc == rowObject) {
                if (insc.getCodigo() != null) {
                    dto.getListInscricaoImobiliariaExcluidos().add(insc);
                }
                dto.getListInscricaoImobiliaria().remove(i);
            }
        }

        if (CollectionUtils.isEmpty(dto.getListInscricaoImobiliaria())) {
            txtInscricaoImobiliaria.setRequired(true);
            txtInscricaoImobiliaria.addRequiredClass();
            target.add(txtInscricaoImobiliaria);
        }

        tblInscricaoImobiliaria.populate(target);

        CrudUtils.removerItem(target, tblInscricaoImobiliaria, dto.getListInscricaoImobiliariaExcluidos(), rowObject);
    }

    private List<IColumn> getColumnsInscricaoImobiliaria() {
        ColumnFactory columnFactory = new ColumnFactory(RequerimentoVigilanciaInscricaoImob.class);
        List<IColumn> columns = new ArrayList<>();
        RequerimentoVigilanciaInscricaoImob proxy = on(RequerimentoVigilanciaInscricaoImob.class);

        columns.add(getCustomColumnInscricaoImobiliaria());
        columns.add(columnFactory.createColumn(BundleManager.getString("inscricaoImobiliaria"),
                path(proxy.getNumeroInscricaoImobiliaria())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderInscricaoImobiliaria() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dto.getListInscricaoImobiliaria();
            }
        };
    }

    private IColumn getCustomColumnInscricaoImobiliaria() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaInscricaoImob>() {
            @Override
            public void customizeColumn(final RequerimentoVigilanciaInscricaoImob rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerInscricaoImobiliaria(target, rowObject);
                    }
                });
            }
        };
    }

    public void inscricaoImobiliariaRequired(AjaxRequestTarget target) {
        if (CollectionUtils.isEmpty(dto.getListInscricaoImobiliaria())) {
            txtInscricaoImobiliaria.setRequired(true);
            txtInscricaoImobiliaria.addRequiredClass();
        } else {
            txtInscricaoImobiliaria.setRequired(false);
            txtInscricaoImobiliaria.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtInscricaoImobiliaria);
        }
    }

    public void limparInscricaoImobiliaria(AjaxRequestTarget target) {
        txtInscricaoImobiliaria.limpar(target);
        tblInscricaoImobiliaria.limpar(target);
        tblInscricaoImobiliaria.update(target);
        dto.getListInscricaoImobiliaria().clear();
        inscricaoImobiliariaRequired(target);
    }

    public Form getForm() {
        return form;
    }

    public void setForm(Form form) {
        this.form = form;
    }

    public InputField<String> getTxtInscricaoImobiliaria() {
        return txtInscricaoImobiliaria;
    }

    public void setTxtInscricaoImobiliaria(InputField<String> txtInscricaoImobiliaria) {
        this.txtInscricaoImobiliaria = txtInscricaoImobiliaria;
    }

    public Table getTblInscricaoImobiliaria() {
        return tblInscricaoImobiliaria;
    }

    public void setTblInscricaoImobiliaria(Table tblInscricaoImobiliaria) {
        this.tblInscricaoImobiliaria = tblInscricaoImobiliaria;
    }

    public String getNumeroInscricaoImobiliaria() {
        return numeroInscricaoImobiliaria;
    }

    public void setNumeroInscricaoImobiliaria(String numeroInscricaoImobiliaria) {
        this.numeroInscricaoImobiliaria = numeroInscricaoImobiliaria;
    }

    public AbstractAjaxButton getBtnAdicionarInscricaoImobiliaria() {
        return btnAdicionarInscricaoImobiliaria;
    }

    public void setBtnAdicionarInscricaoImobiliaria(AbstractAjaxButton btnAdicionarInscricaoImobiliaria) {
        this.btnAdicionarInscricaoImobiliaria = btnAdicionarInscricaoImobiliaria;
    }

    public boolean isModoEdicao() {
        return modoEdicao;
    }

    public void setModoEdicao(boolean modoEdicao) {
        this.modoEdicao = modoEdicao;
    }

    public RequerimentoInscricaoImobiliariaDTO getDto() {
        return dto;
    }

    public void setDto(RequerimentoInscricaoImobiliariaDTO dto) {
        this.dto = dto;
    }
}
