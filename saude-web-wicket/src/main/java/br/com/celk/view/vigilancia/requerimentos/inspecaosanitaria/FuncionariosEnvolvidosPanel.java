package br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.FuncionarioEnvolvidoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaFuncionarioEnvolvido;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

public class FuncionariosEnvolvidosPanel extends Panel {

    private FuncionarioEnvolvidoDTO dto;
    private Form form;
    private WebMarkupContainer containerFuncionarioEnvolvido;
    private CompoundPropertyModel<FuncionarioEnvolvidoDTO> modelFuncionarioEnvolvidoDTO;
    private Table tblFuncionariosEnvolvidos;
    private List<FuncionarioEnvolvidoDTO> funcionarioEnvolvidoDTOList;
    private List<FuncionarioEnvolvidoDTO> funcionarioEnvolvidoDTOListExcluir = new ArrayList<>();
    private FuncionarioEnvolvidoDTO funcionarioEnvolvidoDTOEdicao;
    private InputField numeroTurnosHorariosIF;
    private InputField numeroFuncionariosIF;
    private DropDown areaDD;

    public FuncionariosEnvolvidosPanel(String id, RequerimentoVigilancia requerimentoVigilancia, boolean enabled) {
        super(id);
        this.dto = new FuncionarioEnvolvidoDTO();

        init(enabled);
    }

    private void init(boolean enabled){
        FuncionarioEnvolvidoDTO proxy = on(FuncionarioEnvolvidoDTO.class);
        form = new Form("form", modelFuncionarioEnvolvidoDTO = new CompoundPropertyModel(dto));

        containerFuncionarioEnvolvido = new WebMarkupContainer("containerFuncionarioEnvolvido");
        containerFuncionarioEnvolvido.setOutputMarkupId(true);

        containerFuncionarioEnvolvido.add(numeroTurnosHorariosIF = new InputField("numeroTurnosHorarios"));
        containerFuncionarioEnvolvido.add(areaDD = getDropDownArea("area"));
        containerFuncionarioEnvolvido.add(numeroFuncionariosIF = new InputField("numeroFuncionarios"));

        containerFuncionarioEnvolvido.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        containerFuncionarioEnvolvido.add(tblFuncionariosEnvolvidos = new Table("tblFuncionariosEnvolvidos", getColumns(), getCollectionProvider()));
        tblFuncionariosEnvolvidos.populate();

        form.add(containerFuncionarioEnvolvido);
        form.add(new AjaxPreviewBlank());

        form.setEnabled(enabled);
        add(form);

    }

    private DropDown getDropDownArea(String id){
        return DropDownUtil.getIEnumDropDown(id, RequerimentoInspecaoSanitariaFuncionarioEnvolvido.Area.values(), true);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        FuncionarioEnvolvidoDTO funcionarioEnvolvidoDTO = modelFuncionarioEnvolvidoDTO.getObject();
        funcionarioEnvolvidoDTOList.add(funcionarioEnvolvidoDTO);
        target.add(form);
        modelFuncionarioEnvolvidoDTO.setObject(new FuncionarioEnvolvidoDTO());

    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        FuncionarioEnvolvidoDTO proxy = on(FuncionarioEnvolvidoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("numeroTurnosHorarios"), proxy.getNumeroTurnosHorarios()));
        columns.add(createColumn(bundle("area"), proxy.getAreaFormatada()));
        columns.add(createColumn(bundle("numeroFuncionarios"), proxy.getNumeroFuncionarios()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<FuncionarioEnvolvidoDTO>() {
            @Override
            public void customizeColumn(final FuncionarioEnvolvidoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<FuncionarioEnvolvidoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, FuncionarioEnvolvidoDTO modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblFuncionariosEnvolvidos, funcionarioEnvolvidoDTOList, modelObject);
                        if(modelObject.getRequerimentoInspecaoSanitariaFuncionarioEnvolvido() != null){
                            funcionarioEnvolvidoDTOListExcluir.add(modelObject);
                        }
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<FuncionarioEnvolvidoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, FuncionarioEnvolvidoDTO modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });
            }
        };
    }

    private void editar(AjaxRequestTarget target, FuncionarioEnvolvidoDTO modelObject) {
        limparForm(target);
        funcionarioEnvolvidoDTOEdicao = (FuncionarioEnvolvidoDTO) SerializationUtils.clone(modelObject);
        modelFuncionarioEnvolvidoDTO.setObject(funcionarioEnvolvidoDTOEdicao);
        funcionarioEnvolvidoDTOList.remove(modelObject);
        target.add(numeroTurnosHorariosIF);
        target.add(numeroFuncionariosIF);
        target.add(areaDD);
    }

    private void limparForm(AjaxRequestTarget target){
        numeroTurnosHorariosIF.limpar(target);
        numeroFuncionariosIF.limpar(target);
        areaDD.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (funcionarioEnvolvidoDTOList == null) {
                    funcionarioEnvolvidoDTOList = new ArrayList<>();
                }
                return funcionarioEnvolvidoDTOList;
            }
        };
    }

    public List<FuncionarioEnvolvidoDTO> getFuncionarioEnvolvidoDTOList() {
        return funcionarioEnvolvidoDTOList;
    }

    public void setFuncionarioEnvolvidoDTOList(List<FuncionarioEnvolvidoDTO> funcionarioEnvolvidoDTOList) {
        this.funcionarioEnvolvidoDTOList = funcionarioEnvolvidoDTOList;
    }

    public List<FuncionarioEnvolvidoDTO> getFuncionarioEnvolvidoDTOListExcluir() {
        return funcionarioEnvolvidoDTOListExcluir;
    }
}

