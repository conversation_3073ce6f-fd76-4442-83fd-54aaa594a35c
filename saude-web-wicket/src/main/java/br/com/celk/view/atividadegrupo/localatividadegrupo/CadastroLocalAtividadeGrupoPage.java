package br.com.celk.view.atividadegrupo.localatividadegrupo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroLocalAtividadeGrupoPage extends CadastroPage<LocalAtividadeGrupo> {

    private InputField local;
    private List<Empresa> unidade;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<Long> dropDownTipoLocal;
    private LongField numeroInep;

    public CadastroLocalAtividadeGrupoPage(LocalAtividadeGrupo object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroLocalAtividadeGrupoPage(LocalAtividadeGrupo object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroLocalAtividadeGrupoPage(LocalAtividadeGrupo object) {
        this(object, false);
    }

    public CadastroLocalAtividadeGrupoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        boolean permissaoEmpresa = isActionPermitted(Permissions.EMPRESA);

        local = new RequiredInputField<String>(LocalAtividadeGrupo.PROP_DESCRICAO);
        local.setLabel(new Model<String>(bundle("local")));

        numeroInep = new LongField(LocalAtividadeGrupo.PROP_NUMERO_INEP);
        numeroInep.setVMax(99999999L);
        numeroInep.setVMin(0L);
        numeroInep.setLabel(new Model<String>(bundle("numeroInep"))).setEnabled(false);
        numeroInep.setEnabled(true);
        form.add(numeroInep);
        form.add(local);

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", true);
        form.add(autoCompleteConsultaEmpresa);

        dropDownTipoLocal = DropDownUtil.getIEnumDropDown(LocalAtividadeGrupo.PROP_TIPO_LOCAL, LocalAtividadeGrupo.TipoLocal.values(), true, true);
        form.add(dropDownTipoLocal);

        dropDownTipoLocal.setRequired(false);
        dropDownTipoLocal.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                numeroInep.limpar(target);
                if (dropDownTipoLocal.getComponentValue() != null){
                    if (dropDownTipoLocal.getComponentValue().equals(LocalAtividadeGrupo.TipoLocal.ESCOLA.value())){
                        numeroInep.setEnabled(true);
                    } else {
                        numeroInep.setEnabled(false);
                    }
                } else {
                    numeroInep.setEnabled(false);
                }
                target.add(numeroInep);
            }
        });


        if (!permissaoEmpresa) {
            try {
                carregarUnidades();
            } catch (SGKException ex) {
                error(ex.getMessage());
            }
        }
    }

    private void carregarUnidades() throws DAOException, ValidacaoException {
        List<Long> empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresas))
                    .start().getList();
            unidade = empresaList;
        }
    }

    @Override
    public Object salvar(LocalAtividadeGrupo object) throws DAOException, ValidacaoException {
        if (dropDownTipoLocal.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("msg_selecionar_tipo_local"));
        }
        if (dropDownTipoLocal.getComponentValue().equals(LocalAtividadeGrupo.TipoLocal.ESCOLA.value())) {
            if (numeroInep.getComponentValue() != null){
                if (RepositoryComponentDefault.NUMERO_LIMITE_INEP > numeroInep.getComponentValue().toString().length()) {
                    throw new ValidacaoException(BundleManager.getString("msg_numero_inep_menor_oito"));
                }
            }
        }
        return super.salvar(object);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaLocalAtividadeGrupoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLocalAtividadeGrupo");
    }

    @Override
    public Class<LocalAtividadeGrupo> getReferenceClass() {
        return LocalAtividadeGrupo.class;
    }
}
