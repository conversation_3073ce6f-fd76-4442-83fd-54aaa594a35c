package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgCadastroAcompanhanteHospital;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.tablerow.LeitoTableRow;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.hospital.financeiro.autocomplete.AutoCompleteConsultaFormaPagamento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.AcompanhanteUsuarioCadsusHospitalDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.LoadInterceptorEmpresaNaturezaTipo;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.EloTipoUnidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EloUsuarioCadsusAcompanhante;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusAcompanhante;
import br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.IResource;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.util.VOUtils.montarPath;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmarInternacao extends Panel {

    private String paciente;
    private TipoAtendimento tipoAtendimento;
    private Convenio convenio;
    private Convenio subconvenio;
    private UsuarioCadsusAcompanhante acompanhante;
    private Empresa empresa;
    private String numeroConvenio;
    private InputField txtNumeroConvenio;
    private NaturezaProcura naturezaProcura;
    private SimpleSelectionTable<LeitoQuarto> tableLeito;
    private DropDown cbxEmpresa;
    private LeitoQuarto leitoQuarto;
    private DropDown<UsuarioCadsusAcompanhante> cbxAcompanhante;
    private DropDown cbxConvenio;
    private DropDown cbxSubConvenio;
    private DropDown cbxTipoAtendimento;
    private Profissional profissional;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private UsuarioCadsus usuarioCadsus;
    private String observacaoMarcacao;
    private boolean somenteInternacao;
    private AbstractAjaxLink btnCadadastroAcompanhante;
    private DlgCadastroAcompanhanteHospital dlgCadastroAcompanhanteHospital;
    private Double valorAdiantamento;
    private FormaPagamento formaPagamento;
    private InputField txtValorAdiantamento;
    private AutoCompleteConsultaFormaPagamento autoCompleteConsultaFormaPagamento;
    private Convenio convenioParticular;
    private DateChooser dchDataValidadeConvenio;
    private Date dataValidadeConvenio;
    private WebMarkupContainer containerFinanceiro;

    private Image imagem;

    public PnlConfirmarInternacao(String id, boolean somenteInternacao) {
        super(id);
        this.somenteInternacao = somenteInternacao;
        init();
    }

    private void init() {
        try {
            Form form = new Form("form");
            naturezaProcura = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("NatProcuraInternacao");
            convenioParticular = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioParticular");

            form.add(new InputField("paciente", new PropertyModel(this, "paciente"))
                    .setEnabled(false));
            form.add(txtNumeroConvenio = new InputField("numeroConvenio", new PropertyModel(this, "numeroConvenio")));

            form.add(dchDataValidadeConvenio = (DateChooser) new DateChooser("dataValidadeConvenio", new PropertyModel(this, "dataValidadeConvenio")));
            dchDataValidadeConvenio.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));

            cbxTipoAtendimento = getDropDownTipoAtendimento(naturezaProcura);
            form.add(cbxTipoAtendimento);

            cbxConvenio = getDropDownConvenio();
            form.add(cbxConvenio);

            cbxSubConvenio = getDropDownSubConvenio();
            form.add(cbxSubConvenio);
            cbxSubConvenio.setEnabled(false);

            cbxEmpresa = getDropDownEmpresa();
            form.add(cbxEmpresa);

            form.add(new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));

            cbxAcompanhante = new DropDown("acompanhante", new PropertyModel(this, "acompanhante"));

            form.add(cbxAcompanhante);

            form.add(btnCadadastroAcompanhante = new AbstractAjaxLink("btnCadadastroAcompanhante") {
                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    dlgCadastrarNovoAcompanhante(target);
                }
            });

//            form.add(new AutoCompleteConsultaUsuarioCadsus("responsavel", new PropertyModel(this, "responsavel")) {
//                @Override
//                public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
//                    return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
//                }
//            });
//            cbxProfissional = getDropDownProfissional();
//            form.add(cbxProfissional);
//            
//            if(tipoAtendimento == null){
//                cbxProfissional.setEnabled(false);
//            }
            form.add(new InputArea("observacaoMarcacao", new PropertyModel(this, "observacaoMarcacao")));

            form.add(tableLeito = new SimpleSelectionTable("tableLeito", getColumns(), getCollectionProvider()) {
                @Override
                protected Item newRowItem(String id, int index, IModel model) {
                    return new LeitoTableRow(id, index, model, this);
                }
            });

            tableLeito.populate();

            form.add(containerFinanceiro = new WebMarkupContainer("containerFinanceiro"));
            containerFinanceiro.setOutputMarkupId(true);

            containerFinanceiro.add(txtValorAdiantamento = (InputField) new DoubleField("valorAdiantamento", new PropertyModel(this, "valorAdiantamento")).setLabel(new Model<String>(bundle("valor"))));
            containerFinanceiro.add(autoCompleteConsultaFormaPagamento = (AutoCompleteConsultaFormaPagamento) new AutoCompleteConsultaFormaPagamento("formaPagamento", new PropertyModel(this, "formaPagamento"))
                    .setLabel(new Model<String>(bundle("formaPagamento"))));
            autoCompleteConsultaFormaPagamento.setExibirFormaPagamentoAdiantamento(false);
            autoCompleteConsultaFormaPagamento.setExibirFormaPagamentoDesconto(false);

            form.add(new AbstractAjaxButton("btnOk") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
//                    carregarProfissional();
                    //onOk(target);

                    if (tableLeito.getSelectedObject() != null) {
                        if (LeitoQuarto.Situacao.AGUARDANDO_LIMPEZA.value().equals(tableLeito.getSelectedObject().getSituacao())) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_leito_nao_pode_ser_utilizado_situacao_limpeza"));
                        } else if (LeitoQuarto.Situacao.ISOLADO.value().equals(tableLeito.getSelectedObject().getSituacao())) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_leito_nao_pode_ser_utilizado_situacao_isolado"));
                        }
                    }

                    if (convenio != null) {
                        if (RepositoryComponentDefault.SIM_LONG.equals(convenio.getFlagNumeroConvenioObrigatorio())
                                || Convenio.ValidacaoNumeroConvenio.OBRIGATORIO.value().equals(convenio.getValidacaoNumeroConvenio())) {
                            if (numeroConvenio == null) {
                                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numero_convenio"));
                            }
                            if (dataValidadeConvenio == null) {
                                throw new ValidacaoException(bundle("informeDataValidade"));
                            }
                        } else if (Convenio.ValidacaoNumeroConvenio.MATRICULA_IP.value().equals(convenio.getValidacaoNumeroConvenio())) {
                            ConvenioHelper.validaNumeroConvenioIpe(numeroConvenio);
                        } else if (Convenio.ValidacaoNumeroConvenio.MATRICULA_UNIMED.value().equals(convenio.getValidacaoNumeroConvenio())) {
                            //validar numero convenio da unimed...
                            if (numeroConvenio == null) {
                                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numero_convenio"));
                            }
                        }
                    }

                    if (valorAdiantamento != null && formaPagamento == null) {
                        throw new ValidacaoException(bundle("msgInformeFormaPagamento"));
                    } else if (valorAdiantamento == null && formaPagamento != null) {
                        throw new ValidacaoException(bundle("msgInformeValor"));
                    }

                    if (dataValidadeConvenio != null && Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().after(Data.adjustRangeHour(dataValidadeConvenio).getDataInicial())) {
                        throw new ValidacaoException(bundle("msgDataValidadeNaoPodeSerMenorDataAtual"));
                    }

                    onOk(target);
                }
            });

            form.add(new AbstractAjaxButton("btnFechar") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    onFechar(target);
                }
            }.setDefaultFormProcessing(false));

            cbxConvenio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    Convenio convenioSelecionado = (Convenio) cbxConvenio.getComponentValue();

                    if (convenioSelecionado != null) {
                        List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
                                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, convenioSelecionado))
                                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                                .start().getList();
                        if (convList.isEmpty()) {
                            cbxSubConvenio.setEnabled(false);
                            cbxSubConvenio.removeAllChoices();
                            enableAdiantamento(target, convenioSelecionado);
                        } else {
                            cbxSubConvenio.setEnabled(true);
                            cbxSubConvenio.removeAllChoices();
                            for (Convenio convenio : convList) {
                                cbxSubConvenio.addChoice(convenio, convenio.getDescricao());
                            }
                            enableAdiantamento(target, convList.get(0));
                        }
                    } else {
                        cbxSubConvenio.setEnabled(false);
                        cbxSubConvenio.removeAllChoices();
                        cbxSubConvenio.limpar(target);
                        enableAdiantamento(target, convenioSelecionado);
                    }
                    target.add(cbxSubConvenio);
                }
            });

            cbxSubConvenio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    Convenio convenioSelecionado = (Convenio) cbxSubConvenio.getComponentValue();
                    enableAdiantamento(target, convenioSelecionado);
                }
            });

            cbxEmpresa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    tableLeito.limpar(target);
                    tableLeito.populate();
                }
            });

            cbxTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    TipoAtendimento tipoAtendimento = (TipoAtendimento) cbxTipoAtendimento.getComponentValue();
                    if (tipoAtendimento != null && RepositoryComponentDefault.SIM_LONG.equals(tipoAtendimento.getExigeLeito())) {
                        tableLeito.limpar(target);
                        tableLeito.setEnabled(true);
                        tableLeito.populate();
                        cbxEmpresa.setEnabled(true);
                    } else {
                        tableLeito.limpar(target);
                        tableLeito.setEnabled(false);
                        cbxEmpresa.setEnabled(false);
                    }
                    target.add(tableLeito);
                    target.add(cbxEmpresa);
                }
            });

            tableLeito.addSelectionAction(new ISelectionAction<LeitoQuarto>() {
                @Override
                public void onSelection(AjaxRequestTarget target, LeitoQuarto object) {
                    leitoQuarto = object;
                }
            });

            tableLeito.setEnabled(false);
            cbxEmpresa.setEnabled(false);

            form.add(imagem = new Image("imgAvatar", ""));
            imagem.setOutputMarkupId(true);

            add(form);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void dlgCadastrarNovoAcompanhante(AjaxRequestTarget target) {
        if (dlgCadastroAcompanhanteHospital == null) {
            WindowUtil.addModal(target, this, dlgCadastroAcompanhanteHospital = new DlgCadastroAcompanhanteHospital(WindowUtil.newModalId(this)) {

                @Override
                public void onSalvar(AjaxRequestTarget target, AcompanhanteUsuarioCadsusHospitalDTO dto) throws ValidacaoException, DAOException {
                    salvarAcompanhante(target, dto);
                }
            });
        }
        dlgCadastroAcompanhanteHospital.showDlg(target);
    }

    private void salvarAcompanhante(AjaxRequestTarget target, AcompanhanteUsuarioCadsusHospitalDTO dto) throws DAOException, ValidacaoException {
        List<AcompanhanteUsuarioCadsusHospitalDTO> acompanhantesList = new ArrayList<AcompanhanteUsuarioCadsusHospitalDTO>();
        acompanhantesList.add(dto);

        BOFactory.getBO(UsuarioCadsusFacade.class).salvarUsuarioCadsusAcompanhante(acompanhantesList, null, getUsuarioCadsus());
        carregarAcompanhantes(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        LeitoQuarto proxy = on(LeitoQuarto.class);

        columns.add(createColumn(bundle("setor"), proxy.getQuartoInternacao().getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("quarto"), proxy.getQuartoInternacao().getDescricao()));
        columns.add(createColumn(bundle("leito"), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (cbxTipoAtendimento.getComponentValue() == null) {
                    return Collections.emptyList();
                }

                List<LeitoQuarto> leitos = LoadManager.getInstance(LeitoQuarto.class)
                        .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                        .addProperties(new HQLProperties(QuartoInternacao.class, LeitoQuarto.PROP_QUARTO_INTERNACAO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_SITUACAO), QueryCustom.QueryCustomParameter.IN, Arrays.asList(LeitoQuarto.Situacao.LIBERADO.value(), LeitoQuarto.Situacao.ISOLADO.value())))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_EMPRESA), cbxEmpresa.getComponentValue()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_TIPO_QUARTO), QuartoInternacao.TipoQuarto.INTERNACAO.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO)))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_DESCRICAO)))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(LeitoQuarto.PROP_DESCRICAO)))
                        .start().getList();

                return leitos;
            }
        };
    }

//    private DropDown getDropDownProfissional(){
//        DropDown dropDown = new DropDown("profissional", new PropertyModel(this, "dominioProfissional"));
//        return dropDown;
//    }
    private DropDown getDropDownTipoAtendimento(NaturezaProcura naturezaProcura) {
        DropDown dropDown = new DropDown("tipoAtendimento", new PropertyModel(this, "tipoAtendimento"));

        dropDown.addChoice(null, "");

        LoadManager load = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class);
        load.addProperties(new HQLProperties(TipoAtendimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, naturezaProcura))
                .addInterceptor(new LoadInterceptorEmpresaNaturezaTipo((Empresa) SessaoAplicacaoImp.getInstance().getEmpresa()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO)));
        if (somenteInternacao) {
            load.addParameter(new QueryCustom.QueryCustomParameter(montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_FLAG_INTERNACAO), RepositoryComponentDefault.SIM_LONG));
        }

        List<NaturezaProcuraTipoAtendimento> nptaList = load.start().getList();
        for (NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento : nptaList) {
            dropDown.addChoice(naturezaProcuraTipoAtendimento.getTipoAtendimento(), naturezaProcuraTipoAtendimento.getTipoAtendimento().getDescricao());
        }
        return dropDown;
    }

    private DropDown getDropDownConvenio() {
        DropDown dropDown = new DropDown("convenio", new PropertyModel(this, "convenio"));

        dropDown.addChoice(null, "");

        List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.NAO_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                .start().getList();
        for (Convenio convenio : convList) {
            dropDown.addChoice(convenio, convenio.getDescricao());
        }
        return dropDown;
    }

    private DropDown getDropDownAcompanhante() {
        cbxAcompanhante.addChoice(null, "");

        EloUsuarioCadsusAcompanhante proxy = on(EloUsuarioCadsusAcompanhante.class);

        List<EloUsuarioCadsusAcompanhante> eloList = LoadManager.getInstance(EloUsuarioCadsusAcompanhante.class)
                .addProperty(path(proxy.getUsuarioCadsusAcompanhante().getCodigo()))
                .addProperty(path(proxy.getUsuarioCadsusAcompanhante().getNome()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus()), getUsuarioCadsus()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getUsuarioCadsusAcompanhante().getNome()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        for (EloUsuarioCadsusAcompanhante elo : eloList) {
            cbxAcompanhante.addChoice(elo.getUsuarioCadsusAcompanhante(), elo.getUsuarioCadsusAcompanhante().getNome());
        }
        return cbxAcompanhante;
    }

    private DropDown getDropDownSubConvenio() {
        DropDown dropDown = new DropDown("subconvenio", new PropertyModel(this, "subconvenio"));

        dropDown.addChoice(null, "");

//        if (cbxConvenio.getComponentValue() != null) {
//            List<Convenio> convList = LoadManager.getInstance(Convenio.class)
//                    .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
//                    .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, cbxConvenio.getComponentValue()))
//                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
//                    .start().getList();
//            for (Convenio convenio : convList) {
//                dropDown.addChoice(convenio, convenio.getDescricao());
//            }
//        }
        return dropDown;
    }

    private DropDown getDropDownEmpresa() {
        DropDown dropDownEmpresa = new DropDown("empresa", new PropertyModel(this, "empresa"));
        List<Empresa> empresas = new ArrayList<Empresa>();

        List<EloTipoUnidade> elos = LoadManager.getInstance(EloTipoUnidade.class)
                .addProperties(new HQLProperties(EloTipoUnidade.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, EloTipoUnidade.PROP_EMPRESA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EloTipoUnidade.PROP_TIPO_UNIDADE, EloTipoUnidade.TipoUnidade.ATENDIMENTO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EloTipoUnidade.PROP_EMPRESA, Empresa.PROP_DESCRICAO)))
                .start().getList();
        for (EloTipoUnidade eloTipoUnidade : elos) {
            empresas.add(eloTipoUnidade.getEmpresa());
        }

        dropDownEmpresa.addChoice(null, "");
        for (Empresa empresa : empresas) {
            dropDownEmpresa.addChoice(empresa, empresa.getDescricao());
        }
        return dropDownEmpresa;
    }

//    private void controleTipoAtendimento(TipoAtendimento ta){
//        try {
//            TipoAtendimento tipoAtendimentoConsulta = ta.getTipoAtendimentoProximo();
//            if(tipoAtendimentoConsulta == null){
//                tipoAtendimentoConsulta = ta;
//            }
//            
//            QueryConsultaDominioProfissionalDTOParam param = new QueryConsultaDominioProfissionalDTOParam();
//            param.setPeriodoUnidade(true);
//            param.setNaturezaProcura(naturezaProcura);
//            param.setTipoAtendimento(tipoAtendimentoConsulta);
//            if(tipoAtendimentoConsulta.getEmpresa() !=  null){
//                param.setCodigoEmpresa(tipoAtendimentoConsulta.getEmpresa().getCodigo());
//            }else{
//                param.setCodigoEmpresa(null);
//            }
//            
//            DataPaging<QueryConsultaDominioProfissionalDTOParam> dataPaging = new DataPagingImpl<QueryConsultaDominioProfissionalDTOParam>(DataPaging.Type.ALVO_LIST);
//            dataPaging.setFirstResult(0);
//            dataPaging.setMaxResult(999);
//            dataPaging.setParam(param);
//            
//            DataPagingResult<DominioProfissional> result = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaDominioProfissional(dataPaging);
//            cbxProfissional.addChoice(null, "");
//            for (DominioProfissional dominioProfissional : result.getList()) {
//                cbxProfissional.addChoice(dominioProfissional, dominioProfissional.getNome());
//            }
//        } catch (SGKException ex) {
//            Loggable.log.error(ex.getMessage(), ex);
//        }
//    }
//    private void carregarProfissional(){
//        profissional = null;
//        if(dominioProfissional != null){
//            profissional = LoadManager.getInstance(Profissional.class)
//                    .setId(dominioProfissional.getProfissional().getCodigo())
//                    .start().getVO();
//        }
//    }
    public abstract void onOk(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        paciente = null;
        usuarioCadsus = null;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

//    public Profissional getProfissional() {
//        return profissional;
//    }
//
//    public void setProfissional(Profissional profissional) {
//        this.profissional = profissional;
//    }
    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getNumeroConvenio() {
        return numeroConvenio;
    }

    public void setNumeroConvenio(String numeroConvenio) {
        this.numeroConvenio = numeroConvenio;
    }

    public LeitoQuarto getLeitoQuarto() {
        return leitoQuarto;
    }

    public void setLeitoQuarto(LeitoQuarto leitoQuarto) {
        this.leitoQuarto = leitoQuarto;
    }

    public Convenio getSubconvenio() {
        return subconvenio;
    }

    public void setSubconvenio(Convenio subconvenio) {
        this.subconvenio = subconvenio;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getObservacaoMarcacao() {
        return observacaoMarcacao;
    }

    public void setObservacaoMarcacao(String observacaoMarcacao) {
        this.observacaoMarcacao = observacaoMarcacao;
    }

    public UsuarioCadsusAcompanhante getAcompanhante() {
        return acompanhante;
    }

    public void setAcompanhante(UsuarioCadsusAcompanhante acompanhante) {
        this.acompanhante = acompanhante;
    }

    public Double getValorAdiantamento() {
        return valorAdiantamento;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public Date getDataValidadeConvenio() {
        return dataValidadeConvenio;
    }

    public void setDataValidadeConvenio(Date dataValidadeConvenio) {
        this.dataValidadeConvenio = dataValidadeConvenio;
    }

    public void carregarAcompanhantes(AjaxRequestTarget target) {
        cbxAcompanhante.removeAllChoices();

        getDropDownAcompanhante();
        target.add(cbxAcompanhante);
    }

    private void enableAdiantamento(AjaxRequestTarget target, Convenio convenio) {
        boolean enable = false;
        if (convenioParticular != null && convenio != null && convenio.getCodigo().equals(convenioParticular.getCodigo())) {
            enable = true;
        }

        if (enable) {
            try {
                BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("adiantamento");
            } catch (DAOException ex) {
                MessageUtil.modalWarn(target, this, ex);
                return;
            } catch (ValidacaoRuntimeException ex) {
                MessageUtil.modalWarn(target, this, ex);
                return;
            }
        }

        txtValorAdiantamento.limpar(target);
        autoCompleteConsultaFormaPagamento.limpar(target);
        if (enable) {
            target.appendJavaScript(JScript.showFieldset(containerFinanceiro));
        } else {
            target.appendJavaScript(JScript.hideFieldset(containerFinanceiro));
        }
        target.add(containerFinanceiro);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(OnDomReadyHeaderItem.forScript(JScript.hideFieldset(containerFinanceiro)));
    }

    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }
}
