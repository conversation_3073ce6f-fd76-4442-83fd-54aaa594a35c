package br.com.celk.view.vacina.entradavacina.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.vacina.EntradaVacina;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaEntradaVacina extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return EntradaVacina.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(EntradaVacina.class).getProperties();
    }

}
