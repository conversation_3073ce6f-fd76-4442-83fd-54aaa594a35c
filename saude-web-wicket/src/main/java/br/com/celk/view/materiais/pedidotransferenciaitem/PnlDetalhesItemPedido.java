package br.com.celk.view.materiais.pedidotransferenciaitem;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.utils.parametros.ParametrosMateriaisUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDetalhesItemPedido extends Panel{

    private WebMarkupContainer dialogRoot;
    private WebMarkupContainer containerVacina;
    private CompoundPropertyModel<PedidoTransferenciaItem> dialogModel;
    private CollectionProvider<PedidoTransferenciaItemLote, PedidoTransferenciaItem> collectionProvider;
    private Table table;
    private AbstractAjaxButton btnFechar;
    private DisabledInputArea txtDescricaoJustificativa;

    private CompoundPropertyModel<PedidoTransferenciaItem> modelItem;
    private WebMarkupContainer containerItem;
    private boolean possuiDescricaoSeparacao;
    private boolean isVacina;
    private String utilizaLocalizacaoEstoque;

    public PnlDetalhesItemPedido(String id) {
        super(id);
        init();
    }

    public PnlDetalhesItemPedido(String id, boolean isVacina) {
        super(id);
        this.isVacina = isVacina;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        
        dialogModel = new CompoundPropertyModel(new PedidoTransferenciaItem());
        dialogRoot = new WebMarkupContainer("root", dialogModel);
        containerVacina = new WebMarkupContainer("vacinaMarkup", new CompoundPropertyModel(dialogModel));
        PedidoTransferenciaItem proxy = on(PedidoTransferenciaItem.class);

        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_CODIGO)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_ITEM)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_DESCRICAO_STATUS)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_DATA_CADASTRO)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_SOLICITADA)));
        dialogRoot.add(new DisabledInputField(path(proxy.getQuantidadeEnviada())));
        dialogRoot.add(new DisabledInputField(path(proxy.getQuantidadeNaoAprovada())));
        dialogRoot.add(new DisabledInputField(path(proxy.getQuantidadeSemEstoque())));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_RECEBIDA)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_DESCRICAO_FORMATADO)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_DATA_CANCELAMENTO)));
        dialogRoot.add(new DisabledInputArea(VOUtils.montarPath(PedidoTransferenciaItem.PROP_DESCRICAO_CANCELAMENTO)));
        dialogRoot.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_OBSERVACAO)));

        containerVacina.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_VACINA, TipoVacina.PROP_DESCRICAO)));
        containerVacina.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_FABRICANTE, FabricanteMedicamento.PROP_DESCRICAO)));
        dialogRoot.add(containerVacina);
        containerVacina.setVisible(isVacina);

        containerItem = new WebMarkupContainer("containerItem");
        containerItem.add(txtDescricaoJustificativa = new DisabledInputArea(VOUtils.montarPath(PedidoTransferenciaItem.PROP_DESCRICAO_JUSTIFICATIVA_SEPARACAO)));
        containerItem.setOutputMarkupId(true);
        dialogRoot.add(containerItem);
        dialogRoot.add(table = new Table("tableLotes", getColumns(), getCollectionProvider()));
        dialogRoot.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        btnFechar.setDefaultFormProcessing(false);
        add(dialogRoot);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setModelObject(PedidoTransferenciaItem pedidoTransferenciaItem){
        possuiDescricaoSeparacao = pedidoTransferenciaItem != null && pedidoTransferenciaItem.getDescricaoJustificativaSeparacao() != null;
        dialogModel.setObject(pedidoTransferenciaItem);
        getCollectionProvider().setParameters(pedidoTransferenciaItem);
    }

    public void update(AjaxRequestTarget target){
        target.add(this);
        table.populate(target);
        containerItem.setVisible(possuiDescricaoSeparacao);
        target.add(containerItem);
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaItemLote.class);
        
        columns.add(columnFactory.createColumn(BundleManager.getString("lote"), VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_LOTE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("validade"), VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_DATA_VALIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_QUANTIDADE)));
        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            columns.add(columnFactory.createColumn(BundleManager.getString("localizacaoEstrutura"), VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_MASCARA)));
        }
        
        return columns;
    }
    
    private ICollectionProvider<PedidoTransferenciaItemLote, PedidoTransferenciaItem> getCollectionProvider(){
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider<PedidoTransferenciaItemLote, PedidoTransferenciaItem>() {

            @Override
            public Collection getCollection(PedidoTransferenciaItem pedidoTransferenciaItem) throws DAOException, ValidacaoException {
                return LoadManager.getInstance(PedidoTransferenciaItemLote.class)
                        .addProperty(VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_LOTE))
                        .addProperty(VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_DATA_VALIDADE))
                        .addProperty(VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_QUANTIDADE))
                        .addProperty(VOUtils.montarPath(PedidoTransferenciaItemLote.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_MASCARA))
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaItemLote.PROP_PEDIDO_TRANSFERENCIA_ITEM, pedidoTransferenciaItem))
                        .start().getList();
            }
        };
        }
        
        return this.collectionProvider;
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(PnlDetalhesItemPedido.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }
}
