package br.com.celk.view.consorcio.pedidotransferencialicitacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEntrega;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgEncaminharPedido extends Window{

    private PnlEncaminharPedido pnlEncaminharPedido;
    
    public DlgEncaminharPedido(String id) {
        super(id);
        
        setInitialHeight(500);
        setInitialWidth(800);
        
        setResizable(false);
        
        setTitle(BundleManager.getString("confirmarEntregaPedido"));
        
        setContent(pnlEncaminharPedido = new PnlEncaminharPedido(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega) throws DAOException, ValidacaoException {
                close(target);
                DlgEncaminharPedido.this.onConfirmar(target, pedidoTransferenciaLicitacaoEntrega);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
        pnlEncaminharPedido.setPedidoTransferenciaLicitacao(target, pedidoTransferenciaLicitacao);
        show(target);
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, PedidoTransferenciaLicitacaoEntrega pedidoTransferenciaLicitacaoEntrega) throws DAOException, ValidacaoException;
    
}
