package br.com.celk.view.materiais.estoque.relatorio.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.IOException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoResumoSaldo extends Panel {

    private WebMarkupContainer image;
    private MultiLineLabel label;
    private AbstractAjaxButton btnImprimir;
    private AbstractAjaxButton btnReprocessar;
    private AbstractAjaxButton btnFechar;

    private final String IMG = "img-warn";

    private String message;

    public PnlConfirmacaoResumoSaldo(String id, String message) {
        super(id);
        this.message = message;
        init();
    }

    private void init() {

        add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        add(label = new MultiLineLabel("message", message));

        label.setOutputMarkupId(true);

        add(btnImprimir = new AbstractAjaxButton("btnImprimir") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException{
                onImprimir(target);
            }

        });

        add(btnReprocessar = new AbstractAjaxButton("btnReprocessar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onReprocessar(target);
            }

        });

        add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        //configurarButtons(btnConfirmar, btnFechar, btnReprocessar);
        //btnConfirmar.setDefaultFormProcessing(false);
        btnFechar.setDefaultFormProcessing(false);
    }

    public abstract void onImprimir(AjaxRequestTarget target);

    public abstract void onReprocessar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("confirmar");
    }

    public String getFecharLabel() {
        return BundleManager.getString("fechar");
    }

    public String getReprocessarLabel() {
        return BundleManager.getString("reprocessar");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

    public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar, AbstractAjaxButton btnReprocessar) {
        btnConfirmar.add(new AttributeModifier("value", getConfirmarLabel()));
        btnFechar.add(new AttributeModifier("value", getFecharLabel()));
        btnReprocessar.add(new AttributeModifier("value", getReprocessarLabel()));
    }
}
