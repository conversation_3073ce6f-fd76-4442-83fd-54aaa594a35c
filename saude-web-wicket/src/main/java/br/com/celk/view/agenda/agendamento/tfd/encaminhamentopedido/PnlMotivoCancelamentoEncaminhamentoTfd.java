package br.com.celk.view.agenda.agendamento.tfd.encaminhamentopedido;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoCancelamentoEncaminhamentoTfd extends Panel {

    private String motivoCancelamento;
    private RequiredInputField txtMotivoCancelamento;

    public PnlMotivoCancelamentoEncaminhamentoTfd(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");

        form.add(txtMotivoCancelamento = new RequiredInputField("motivoCancelamento", new PropertyModel(this, "motivoCancelamento")));
        
        form.add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlMotivoCancelamentoEncaminhamentoTfd.this.onCancelar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    PnlMotivoCancelamentoEncaminhamentoTfd.this.onConfirmar(target, motivoCancelamento);
            }
        });

        add(form);
    }

    public void limpar(AjaxRequestTarget target) {
        txtMotivoCancelamento.limpar(target);
        motivoCancelamento = null;
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    public abstract void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException;
}
