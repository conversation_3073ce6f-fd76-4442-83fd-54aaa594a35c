package br.com.celk.view.esus.relatorio.condicoesmoradia.detalhado;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusReportFacade;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.esus.RelatorioCondicoesMoradiaDetalhadoDTOParam;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.siab.relatorios.RelatorioAcompanhamentoCadastroFamiliasPage;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioCondicoesMoradiaDetalhadoPage extends RelatorioPage<RelatorioCondicoesMoradiaDetalhadoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private DropDown dropDownCondicaoAvaliada;
    private DropDown dropDownOpcao;
    private DropDown dropDownSituacaoMoradia;
    private DropDown dropDownLocalizacao;
    private DropDown dropDownTipoDomicilio;
    private DropDown dropDownCondicaoUsoTerra;
    private DropDown dropDownTipoAcessoDomicilio;
    private DropDown dropDownAbastecimentoAgua;
    private DropDown dropDownEsgotamento;
    private DropDown dropDownTratamentoAgua;
    private DropDown dropDownDestinoLixo;
    private DropDown dropDownPossuiEnergiaEletrica;
    private DropDown dropDownMaterialDominante;
    private InputField txtNumeroComodos;
    private WebMarkupContainer containerCondicaoAvaliada;
    private DropDown dropDownLocalProliferacaoMosquito;

    @Override
    public void init(Form form) {
        RelatorioCondicoesMoradiaDetalhadoDTOParam proxy = on(RelatorioCondicoesMoradiaDetalhadoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        form.add(dropDownArea = getDropDownArea(path(proxy.getArea())));
        form.add(dropDownMicroArea = getDropDownMicroArea(path(proxy.getEquipeMicroArea())));
        form.add(dropDownCondicaoAvaliada = DropDownUtil.getEnumDropDown(path(proxy.getCondicaoAvaliada()), RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.values(), ""));
        dropDownCondicaoAvaliada.addAjaxUpdateValue();
        dropDownCondicaoAvaliada.addRequiredClass();
        dropDownCondicaoAvaliada.setRequired(true);
        form.add(getDropDownOpcao(path(proxy.getOpcao())));
        
        dropDownCondicaoAvaliada.add(new AjaxEventBehavior("onchange") {
            @Override
            protected void onEvent(AjaxRequestTarget target) {
                dropDownOpcao.setEnabled(false);
                dropDownOpcao.setRequired(false);
                dropDownOpcao.removeRequiredClass();
                dropDownOpcao.removeAllChoices();
                dropDownOpcao.addChoice(null, BundleManager.getString("selecioneOpcao"));
                
                target.add(dropDownOpcao);
            }
        });
        
        dropDownCondicaoAvaliada.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarDropDownOpcao(target);
            }
        });
        
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioCondicoesMoradiaDetalhadoDTOParam.FormaApresentacao.values()));
        
        /* [BEGIN] CONDIÇÃO DE MORADIA */
        containerCondicaoAvaliada = new WebMarkupContainer("containerCondicaoAvaliada");
        containerCondicaoAvaliada.setOutputMarkupId(true);
        containerCondicaoAvaliada.add(dropDownSituacaoMoradia = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoMoradia()), EnderecoDomicilioEsus.SituacaoMoradia.values(), true));
        containerCondicaoAvaliada.add(dropDownLocalizacao = DropDownUtil.getIEnumDropDown(path(proxy.getLocalizacao()), EnderecoDomicilioEsus.Localizacao.values(), true));
        containerCondicaoAvaliada.add(dropDownTipoDomicilio = DropDownUtil.getIEnumDropDown(path(proxy.getTipoDomicilio()), EnderecoDomicilioEsus.TipoDomicilio.values(), true));
        containerCondicaoAvaliada.add(dropDownCondicaoUsoTerra = DropDownUtil.getIEnumDropDown(path(proxy.getCondicaoUsoTerra()), EnderecoDomicilioEsus.CondicaoPosseTerra.values(), true));
        containerCondicaoAvaliada.add(dropDownTipoAcessoDomicilio = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAcessoDomicilio()), EnderecoDomicilioEsus.TipoAcessoDomicilio.values(), true));
        containerCondicaoAvaliada.add(dropDownAbastecimentoAgua = DropDownUtil.getIEnumDropDown(path(proxy.getAbastecimentoAgua()), EnderecoDomicilioEsus.AbastecimentoAgua.values(), true));
        containerCondicaoAvaliada.add(dropDownEsgotamento = DropDownUtil.getIEnumDropDown(path(proxy.getEsgotamento()), EnderecoDomicilioEsus.EscoamentoSanitario.values(), true));
        containerCondicaoAvaliada.add(dropDownTratamentoAgua = DropDownUtil.getIEnumDropDown(path(proxy.getTratamentoAgua()), EnderecoDomicilioEsus.TratamentoAgua.values(), true));
        containerCondicaoAvaliada.add(dropDownDestinoLixo = DropDownUtil.getIEnumDropDown(path(proxy.getDestinoLixo()), EnderecoDomicilioEsus.DestinoLixo.values(), true));
        containerCondicaoAvaliada.add(dropDownPossuiEnergiaEletrica = DropDownUtil.getSimNaoLongDropDown(path(proxy.getPossuiEnergiaEletrica()), true, false));
        containerCondicaoAvaliada.add(txtNumeroComodos = new InputField(path(proxy.getNumeroComodos())));
        containerCondicaoAvaliada.add(dropDownMaterialDominante = DropDownUtil.getIEnumDropDown(path(proxy.getMaterialDominante()), EnderecoDomicilioEsus.MaterialPredominante.values(), true));
        containerCondicaoAvaliada.add(dropDownLocalProliferacaoMosquito = DropDownUtil.getSimNaoLongDropDown(path(proxy.getLocalProliferacaoMosquito()), true, false));
        
        form.add(containerCondicaoAvaliada);
        /*   [END] CONDIÇÃO DE MORADIA */
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        List empresas = (List) autoCompleteConsultaEmpresa.getComponentValue();
        if ((CollectionUtils.isEmpty(empresas) || empresas.size() > 1)  && dropDownArea.getComponentValue() == null) {
            throw new ValidacaoException(bundle("msgInformeEstabelecimentoOuArea"));
        }
    }

    private DropDown getDropDownArea(String id) {
        if(dropDownArea == null){
            dropDownArea = new DropDown(id);

            LoadManager loadManager = LoadManager.getInstance(EquipeArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO));

            final List<Long> empresas = new ArrayList();
            if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
                empresas.add(((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getCodigo());
            } else {
                if (!isActionPermitted(Permissions.EMPRESA)) {
                    try {
                        empresas.addAll(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario()));
                    } catch (SGKException ex) {
                        Logger.getLogger(RelatorioAcompanhamentoCadastroFamiliasPage.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }

            if (!empresas.isEmpty()) {
                loadManager.addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        HQLHelper exists = hql.getNewInstanceSubQuery();
                        exists.addToSelect("1");
                        exists.addToFrom("EquipeMicroArea ema JOIN ema.equipeProfissional ep JOIN ep.equipe e JOIN e.empresa emp");
                        exists.addToWhereWhithAnd("ema.equipeArea.codigo = " + alias + ".codigo");
                        exists.addToWhereWhithAnd("emp.codigo in ", empresas);
                        hql.addToWhereWhithAnd("exists(" + exists.getQuery() + ")");
                    }
                });
            }

            List<EquipeArea> areas = loadManager.start().getList();

            dropDownArea.addChoice(null, BundleManager.getString("todas"));
            for (EquipeArea equipeArea : areas) {
                dropDownArea.addChoice(equipeArea, equipeArea.getDescricao());
            }

            dropDownArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownMicroArea.removeAllChoices();
                    dropDownMicroArea.addChoice(null, BundleManager.getString("selecioneArea"));
                    populateCbxMicroArea();
                    target.add(dropDownMicroArea);
                }
            });
        }

        return dropDownArea;
    }
    
    private void populateCbxMicroArea() {
        EquipeArea equipeArea = dropDownArea.getComponentValue();
        if (equipeArea != null) {
            List<EquipeMicroArea> microAreas = LoadManager.getInstance(EquipeMicroArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                    .start().getList();
            for (EquipeMicroArea equipeMicroArea : microAreas) {
                dropDownMicroArea.addChoice(equipeMicroArea, Coalesce.asString(equipeMicroArea.getMicroArea()));
            }
        }
    }
    
    private DropDown getDropDownMicroArea(String id) {
        if(dropDownMicroArea == null){
            dropDownMicroArea = new DropDown(id);
            dropDownMicroArea.addChoice(null, BundleManager.getString("selecioneArea"));
        }
        
        return dropDownMicroArea;
    }
    
    private DropDown getDropDownOpcao(String id) {
        if(dropDownOpcao == null){
            dropDownOpcao = new DropDown(id);
            dropDownOpcao.addChoice(null, BundleManager.getString("selecioneOpcao"));
            dropDownOpcao.setEnabled(false);
        }
        
        return dropDownOpcao;
    }
    
    private void carregarDropDownOpcao(AjaxRequestTarget target){
        dropDownOpcao.setEnabled(false);
        dropDownOpcao.setRequired(false);
        dropDownOpcao.removeRequiredClass();
        dropDownOpcao.removeAllChoices();
        dropDownOpcao.addChoice(null, BundleManager.getString("selecioneOpcao"));
        
        dropDownAbastecimentoAgua.setEnabled(true);
        dropDownCondicaoUsoTerra.setEnabled(true);
        dropDownDestinoLixo.setEnabled(true);
        dropDownEsgotamento.setEnabled(true);
        dropDownLocalizacao.setEnabled(true);
        dropDownMaterialDominante.setEnabled(true);
        dropDownPossuiEnergiaEletrica.setEnabled(true);
        dropDownSituacaoMoradia.setEnabled(true);
        dropDownTipoDomicilio.setEnabled(true);
        dropDownTipoAcessoDomicilio.setEnabled(true);
        dropDownTratamentoAgua.setEnabled(true);
        txtNumeroComodos.setEnabled(true);
        dropDownLocalProliferacaoMosquito.setEnabled(true);

        IEnum[] enums = null;
        
        if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.ABASTECIMENTO_AGUA.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.AbastecimentoAgua.values();
            dropDownAbastecimentoAgua.setEnabled(false);
            dropDownAbastecimentoAgua.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.AGUA_CONSUMO_DOMICILIO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.TratamentoAgua.values();
            dropDownTratamentoAgua.setEnabled(false);
            dropDownTratamentoAgua.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.CONDICAO_POSSE_USO_TERRA.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.CondicaoPosseTerra.values();
            dropDownCondicaoUsoTerra.setEnabled(false);
            dropDownCondicaoUsoTerra.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.DESTINO_LIXO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.DestinoLixo.values();
            dropDownDestinoLixo.setEnabled(false);
            dropDownDestinoLixo.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.DISPONIBILIDADE_ENERGIA_ELETRICA.equals(dropDownCondicaoAvaliada.getComponentValue())){
            dropDownOpcao.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownOpcao.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));
            dropDownOpcao.setEnabled(true);
            
            dropDownPossuiEnergiaEletrica.setEnabled(false);
            dropDownPossuiEnergiaEletrica.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.FORMA_ESCOAMENTO_BANHEIRO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.EscoamentoSanitario.values();
            dropDownEsgotamento.setEnabled(false);
            dropDownEsgotamento.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.LOCALIZACAO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.Localizacao.values();
            dropDownLocalizacao.setEnabled(false);
            dropDownLocalizacao.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.MATERIAL_PREDOMINANTE_PAREDES_EXTERNAS.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.MaterialPredominante.values();
            dropDownMaterialDominante.setEnabled(false);
            dropDownMaterialDominante.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.NUMERO_COMODOS.equals(dropDownCondicaoAvaliada.getComponentValue())){
            txtNumeroComodos.setEnabled(false);
            txtNumeroComodos.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.SITUACAO_MORADIA.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.SituacaoMoradia.values();
            dropDownSituacaoMoradia.setEnabled(false);
            dropDownSituacaoMoradia.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.TIPO_ACESSO_DOMICILIO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.TipoAcessoDomicilio.values();
            dropDownTipoAcessoDomicilio.setEnabled(false);
            dropDownTipoAcessoDomicilio.limpar(target);
        } else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.TIPO_DOMICILIO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            enums = EnderecoDomicilioEsus.TipoDomicilio.values();
            dropDownTipoDomicilio.setEnabled(false);
            dropDownTipoDomicilio.limpar(target);
        }else if(RelatorioCondicoesMoradiaDetalhadoDTOParam.CondicaoAvaliada.LOCAL_PROLIFERACAO_MOSQUITO.equals(dropDownCondicaoAvaliada.getComponentValue())){
            dropDownOpcao.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownOpcao.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));
            dropDownOpcao.setEnabled(true);

            dropDownLocalProliferacaoMosquito.setEnabled(false);
            dropDownLocalProliferacaoMosquito.limpar(target);
        }

        if(enums != null){
            for (IEnum value : enums) {
                dropDownOpcao.addChoice(value.value(), value.descricao());
            }
            dropDownOpcao.setEnabled(true);
            dropDownOpcao.setRequired(true);
            dropDownOpcao.addRequiredClass();
        }
        
        target.add(dropDownOpcao);
        target.add(dropDownAbastecimentoAgua);
        target.add(dropDownCondicaoUsoTerra);
        target.add(dropDownDestinoLixo);
        target.add(dropDownEsgotamento);
        target.add(dropDownLocalizacao);
        target.add(dropDownMaterialDominante);
        target.add(dropDownPossuiEnergiaEletrica);
        target.add(dropDownSituacaoMoradia);
        target.add(dropDownTipoDomicilio);
        target.add(dropDownTipoAcessoDomicilio);
        target.add(dropDownTratamentoAgua);
        target.add(txtNumeroComodos);
        target.add(dropDownLocalProliferacaoMosquito);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioCondicoesMoradiaDetalhadoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioCondicoesMoradiaDetalhadoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EsusReportFacade.class).relatorioCondicoesMoradiaDetalhado(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("condicoesMoradiaDetalhado");
    }
}