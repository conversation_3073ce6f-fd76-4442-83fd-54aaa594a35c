package br.com.celk.view.unidadesaude.atividadegrupo.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.tipoatividadegrupo.autocomplete.AutoCompleteConsultaTipoAtividadeGrupo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoAtividadeProfissionalDTOParam;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioRelacaoAtividadeProfissional extends RelatorioPage<RelatorioRelacaoAtividadeProfissionalDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private String competencia;
    private DropDown<String> dropDownFormaApresentacao;
    private InputField txtCompetencia;
    private DropDown<String> dropDownProfissionaisSemAtividade;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setValidarTipoEstabelecimento(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTipoAtividadeGrupo("tipoAtividadeGrupo")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setMultiplaSelecao(true));
        form.add(getDropDownFormaApresentacao());
        form.add(dropDownProfissionaisSemAtividade = DropDownUtil.getNaoSimDropDown("profissionaisSemAtividade"));
        form.add(txtCompetencia = new InputField("competencia", new PropertyModel(this, "competencia")));
        txtCompetencia.setEnabled(false);
        
        this.dropDownProfissionaisSemAtividade.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownProfissionaisSemAtividade.getComponentValue() != null) {
                    if (dropDownProfissionaisSemAtividade.getComponentValue().equals("S")) {
                        dropDownFormaApresentacao.setEnabled(false);
                        dropDownFormaApresentacao.setComponentValue(BundleManager.getString("geral"));
                        txtCompetencia.setEnabled(true);
                    } else {
                        txtCompetencia.setEnabled(false);
                        txtCompetencia.limpar(target);
                        dropDownFormaApresentacao.setEnabled(true);
                    }
                    target.appendJavaScript("$('#" + txtCompetencia.getMarkupId() + "').mask('99/9999');");
                    target.add(dropDownFormaApresentacao);
                    target.add(txtCompetencia);
                }
            }
        });
    }
    
    public DropDown getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            dropDownFormaApresentacao.addChoice(BundleManager.getString("geral"), BundleManager.getString("geral"));
            dropDownFormaApresentacao.addChoice(Empresa.REF, BundleManager.getString("empresa"));
            dropDownFormaApresentacao.addChoice(TipoAtividadeGrupo.REF, BundleManager.getString("tipoAtividade"));
            dropDownFormaApresentacao.addChoice(Profissional.REF, BundleManager.getString("profissional"));
        }
        return dropDownFormaApresentacao;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoAtividadeProfissionalDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoAtividadeProfissionalDTOParam param) throws ReportException {
        if (competencia != null && !competencia.trim().equals("")) {
            Date data = Data.parserMounthYear(competencia);
            param.setCompetencia(Data.adjustRangeDay(data).getDataInicial());
        }
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioRelacaoAtividadeProfissional(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoAtividadeProfissional");
    }
}
