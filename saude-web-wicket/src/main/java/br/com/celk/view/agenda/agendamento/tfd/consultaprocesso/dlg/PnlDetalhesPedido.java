package br.com.celk.view.agenda.agendamento.tfd.consultaprocesso.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdAgendamento;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class PnlDetalhesPedido extends Panel {

    private AbstractAjaxButton btnConfirmar;
    private PedidoTfd pedidoTfdSelecionado;
    private Table<PedidoTfdAgendamento> table;
    private Form<PedidoTfd> form;
    private List<PedidoTfdAgendamento> lista = new ArrayList<>();

    public PnlDetalhesPedido(String id) {
        super(id);
        if (pedidoTfdSelecionado == null) {
            pedidoTfdSelecionado = new PedidoTfd();
        }
        init();
    }

    protected void init() {
        form = new Form("form", new CompoundPropertyModel(pedidoTfdSelecionado));
        PedidoTfd proxy = on(PedidoTfd.class);
        form.add(new InputField(path(proxy.getNumeroPedido())).setEnabled(false));
        form.add(new InputField(path(proxy.getDescricaoTipoTfd())).setEnabled(false));
        form.add(new InputField(path(proxy.getDataEncaminhamentoFormatado())).setEnabled(false));
        form.add(new InputField(path(proxy.getDataRetornoFormatado())).setEnabled(false));
        form.add(new InputField(path(proxy.getDataParecerFormatado())).setEnabled(false));
        form.add(new InputField(path(proxy.getDescricaoParecer())).setEnabled(false));
        form.add(new InputField(path(proxy.getJustificativaParecer())).setEnabled(false));

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        
//        form.add(new InputField(path(proxy.getSolicitacaoAgendamento().getDataAgendamento())).setEnabled(false));
//        form.add(new InputField(path(proxy.getSolicitacaoAgendamento().getUnidadeExecutante())).setEnabled(false));
        
        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        PedidoTfdAgendamento proxy = on(PedidoTfdAgendamento.class);

        columns.add(createColumn(bundle("dataAgendamento"), proxy.getDataAgendamento()));
        columns.add(createColumn(bundle("local"), proxy.getLocalAgendamento().getDescricao()));
        columns.add(createColumn(bundle("tipoConsulta"), proxy.getSolicitacaoAgendamento().getDescricaoTipoConsulta()));

        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lista;
            }
        };
    }

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("ok");
    }

    public AbstractAjaxButton getBtnConfirmar() {
        return btnConfirmar;
    }

    public void setPedido(AjaxRequestTarget target, PedidoTfd pedidoTfd) {
        this.pedidoTfdSelecionado = pedidoTfd;
        
        lista = LoadManager.getInstance(PedidoTfdAgendamento.class)
                .addProperty(PedidoTfdAgendamento.PROP_DATA_AGENDAMENTO)
                .addProperty(VOUtils.montarPath(PedidoTfdAgendamento.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(PedidoTfdAgendamento.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_CONSULTA))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTfdAgendamento.PROP_PEDIDO_TFD, PedidoTfd.PROP_CODIGO), pedidoTfd.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(PedidoTfdAgendamento.PROP_DATA_AGENDAMENTO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
        
        table.update(target);;

        form.setModelObject(pedidoTfdSelecionado);

        target.add(form);
    }

}
