package br.com.celk.view.vigilancia.processoadministrativo.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.link.AjaxMultiReportLink;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroProcessoAdministrativoViewPanelDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioInspecaoSanitariaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by leonardo on 24/05/18.
 */
public class CadastroProcessoAdministrativoViewPanel extends Panel {

    private ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia;
    private AbstractAjaxButton btnVisualizarInfracao;
    private CadastroProcessoAdministrativoViewPanelDTO dto;
    private Component btnRelatorioPosteriorInfracao;
    private Component btnRelatorioPosteriorMulta;


    public CadastroProcessoAdministrativoViewPanel(String id, ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia) {
        super(id);
        this.processoAdministrativoOcorrencia = processoAdministrativoOcorrencia;
        init();
    }

    private void init() {
        carregarDados();

        add((new AjaxReportLink("btnVisualizarInfracao") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoInfracao(dto.getAutoInfracao().getCodigo(), dto.getAutoInfracao().getNumeroFormatado(), dto.getAutoInfracao().getSituacao());
            }
        }).setOutputMarkupId(true).setVisible(dto.getAutoInfracao() != null));

        add((new AjaxReportLink("btnVisualizarMulta") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoMulta(dto.getAutoMulta().getCodigo(), dto.getAutoMulta().getNumeroFormatado());
            }
        }).setOutputMarkupId(true).setVisible(dto.getAutoMulta() != null));

        add((new AjaxReportLink("btnVisualizarIntimacao") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(dto.getAutoIntimacao().getCodigo(), dto.getAutoIntimacao().getNumeroFormatado());
            }
        }).setOutputMarkupId(true).setVisible(dto.getAutoIntimacao() != null));

        add((new AjaxReportLink("btnVisualizarRelatorioInspecaoAnteriorIntimacao") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                param.setRelatorioInspecao(dto.getRelatorioInspecaoAnteriorIntimacao());
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param) ;
            }
        }).setOutputMarkupId(true).setVisible(dto.getRelatorioInspecaoAnteriorIntimacao() != null));

        add((new AjaxReportLink("btnVisualizarRelatorioInspecaoAnteriorInfracao") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                param.setRelatorioInspecao(dto.getRelatorioInspecaoAnteriorInfracao());
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param) ;
            }
        }).setOutputMarkupId(true).setVisible(dto.getRelatorioInspecaoAnteriorInfracao() != null));

        add((new AjaxReportLink("btnVisualizarRelatorioInspecaoPosteriorIntimacao") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                param.setRelatorioInspecao(dto.getRelatorioInspecaoPosteriorIntimacao());
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param) ;
            }
        }).setOutputMarkupId(true).setVisible(dto.getRelatorioInspecaoPosteriorIntimacao() != null));

        if(CollectionUtils.isNotNullEmpty(dto.getRelatoriosInspecaoPosteriorInfracao()) && dto.getRelatoriosInspecaoPosteriorInfracao().size() == 1) {
            add(btnRelatorioPosteriorInfracao = new AjaxReportLink("btnVisualizarRelatorioInspecaoPosteriorInfracao") {
                @Override
                public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                    RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                    param.setRelatorioInspecao(dto.getRelatoriosInspecaoPosteriorInfracao().get(0));
                    return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param);
                }
            });
            btnRelatorioPosteriorInfracao.setOutputMarkupId(true);
        } else {
            if(CollectionUtils.isNotNullEmpty(dto.getRelatoriosInspecaoPosteriorInfracao())) {
                add(btnRelatorioPosteriorInfracao = new AjaxMultiReportLink("btnVisualizarRelatorioInspecaoPosteriorInfracao") {
                    @Override
                    public List<IReport> getDataReports(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                        List<IReport> reportList = new ArrayList<>();
                        for (RelatorioInspecao relatorioInspecao : dto.getRelatoriosInspecaoPosteriorInfracao()) {
                            RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                            param.setRelatorioInspecao(relatorioInspecao);
                            reportList.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param));
                        }
                        return reportList;
                    }
                });
                btnRelatorioPosteriorInfracao.setOutputMarkupId(true);
            } else {
                add(btnRelatorioPosteriorInfracao = new AjaxReportLink("btnVisualizarRelatorioInspecaoPosteriorInfracao") {
                    @Override
                    public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                        return null;
                    }
                });
                btnRelatorioPosteriorInfracao.setOutputMarkupId(true);
                btnRelatorioPosteriorInfracao.setVisible(false);
            }
        }


        if(CollectionUtils.isNotNullEmpty(dto.getRelatoriosInspecaoPosteriorMulta()) && dto.getRelatoriosInspecaoPosteriorMulta().size() == 1) {
            add(btnRelatorioPosteriorMulta = new AjaxReportLink("btnVisualizarRelatorioInspecaoPosteriorMulta") {
                @Override
                public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                    RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                    param.setRelatorioInspecao(dto.getRelatoriosInspecaoPosteriorMulta().get(0));
                    return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param);
                }
            });
            btnRelatorioPosteriorMulta.setOutputMarkupId(true);
        } else {
            if(CollectionUtils.isNotNullEmpty(dto.getRelatoriosInspecaoPosteriorMulta())) {
                add(btnRelatorioPosteriorMulta = new AjaxMultiReportLink("btnVisualizarRelatorioInspecaoPosteriorMulta") {
                    @Override
                    public List<IReport> getDataReports(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                        List<IReport> reportList = new ArrayList<>();
                        for (RelatorioInspecao relatorioInspecao : dto.getRelatoriosInspecaoPosteriorMulta()) {
                            RelatorioInspecaoSanitariaDTOParam param = new RelatorioInspecaoSanitariaDTOParam();
                            param.setRelatorioInspecao(relatorioInspecao);
                            reportList.add(BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioInspecaoSanitaria(param));
                        }
                        return reportList;
                    }
                });
                btnRelatorioPosteriorMulta.setOutputMarkupId(true);
            } else {
                add(btnRelatorioPosteriorMulta = new AjaxReportLink("btnVisualizarRelatorioInspecaoPosteriorMulta") {
                    @Override
                    public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                        return null;
                    }
                });
                btnRelatorioPosteriorMulta.setOutputMarkupId(true);
                btnRelatorioPosteriorMulta.setVisible(false);
            }
        }

        add((new AjaxReportLink("btnVisualizarIntimacaoSubsistente") {
            @Override
            public DataReport getDataRepoReport(AjaxRequestTarget target) throws ReportException {
                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteAutoIntimacao(dto.getAutoIntimacaoSubsistente().getCodigo(), dto.getAutoIntimacaoSubsistente().getNumeroFormatado()) ;
            }
        }).setOutputMarkupId(true).setVisible(dto.getAutoIntimacaoSubsistente() != null));

    }

    private void carregarDados() {
        if (ProcessoAdministrativo.Tipo.AUTO_INFRACAO.value().equals(processoAdministrativoOcorrencia.getProcessoAdministrativo().getTipo())) {

            processoAdministrativoOcorrencia = LoadManager.getInstance(ProcessoAdministrativoOcorrencia.class)
                    .addProperties(new HQLProperties(ProcessoAdministrativoOcorrencia.class).getProperties())
                    .addProperties(new HQLProperties(ProcessoAdministrativo.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO)).getProperties())
                    .addProperties(new HQLProperties(AutoInfracao.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_INFRACAO)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_VIGILANCIA_PESSOA)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_INFRACAO, AutoInfracao.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .setId(this.processoAdministrativoOcorrencia.getCodigo())
                    .start().getVO();

            dto = new CadastroProcessoAdministrativoViewPanelDTO();
            AutoInfracao autoInfracao = processoAdministrativoOcorrencia.getProcessoAdministrativo().getAutoInfracao();
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromInfracao(autoInfracao);
            dto.setAutoInfracao(autoInfracao);
            dto.setAutoIntimacao(autoIntimacao);
            dto.setRelatorioInspecaoAnteriorInfracao(autoInfracao.getRelatorioInspecao());
            if (autoIntimacao != null) {
                dto.setRelatorioInspecaoAnteriorIntimacao(autoIntimacao.getRelatorioInspecao());
            }
            dto.setRelatoriosInspecaoPosteriorInfracao(AutosHelper.getRelatoriosInspecaoFromInfracao(autoInfracao));
            dto.setRelatorioInspecaoPosteriorIntimacao(AutosHelper.getRelatorioInspecaoFromIntimacao(dto.getAutoIntimacao()));
            dto.setAutoIntimacaoSubsistente(AutosHelper.getIntimacaoSubsistenteFromInfracao(autoInfracao));
        } else if (ProcessoAdministrativo.Tipo.AUTO_MULTA.value().equals(processoAdministrativoOcorrencia.getProcessoAdministrativo().getTipo())) {
            processoAdministrativoOcorrencia = LoadManager.getInstance(ProcessoAdministrativoOcorrencia.class)
                    .addProperties(new HQLProperties(ProcessoAdministrativoOcorrencia.class).getProperties())
                    .addProperties(new HQLProperties(ProcessoAdministrativo.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO)).getProperties())
                    .addProperties(new HQLProperties(AutoMulta.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_MULTA)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_MULTA, AutoMulta.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_MULTA, AutoMulta.PROP_VIGILANCIA_PESSOA)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(ProcessoAdministrativoOcorrencia.PROP_PROCESSO_ADMINISTRATIVO, ProcessoAdministrativo.PROP_AUTO_MULTA, AutoMulta.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .setId(this.processoAdministrativoOcorrencia.getCodigo())
                    .start().getVO();
            dto = new CadastroProcessoAdministrativoViewPanelDTO();
            AutoMulta autoMulta = processoAdministrativoOcorrencia.getProcessoAdministrativo().getAutoMulta();
            AutoIntimacao autoIntimacao = AutosHelper.getAutoIntimacaoFromMulta(autoMulta);
            dto.setAutoMulta(autoMulta);
            dto.setAutoIntimacao(autoIntimacao);
            if(autoIntimacao != null) {
                dto.setRelatorioInspecaoAnteriorIntimacao(autoIntimacao.getRelatorioInspecao());
            }
            dto.setRelatoriosInspecaoPosteriorMulta(AutosHelper.getRelatoriosInspecaoFromMulta(autoMulta));
            dto.setRelatorioInspecaoPosteriorIntimacao(AutosHelper.getRelatorioInspecaoFromIntimacao(dto.getAutoIntimacao()));
            dto.setAutoIntimacaoSubsistente(AutosHelper.getIntimacaoSubsistenteFromMulta(autoMulta));
        } else if(ProcessoAdministrativo.Tipo.SOLICITACAO_JURIDICA.value().equals(processoAdministrativoOcorrencia.getProcessoAdministrativo().getTipo())) {
            dto = new CadastroProcessoAdministrativoViewPanelDTO();
        }
    }
}
