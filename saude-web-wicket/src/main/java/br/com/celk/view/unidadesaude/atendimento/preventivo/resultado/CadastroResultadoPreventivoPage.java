package br.com.celk.view.unidadesaude.atendimento.preventivo.resultado;

import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;

/**
 *
 * <AUTHOR>
 */
public class CadastroResultadoPreventivoPage extends BasePage {
    
    private ExameRequisicao exameRequisicao;

    public CadastroResultadoPreventivoPage(ExameRequisicao exameRequisicao) {
        this.exameRequisicao = exameRequisicao;
        init();
    }
    
    private void init(){
        
    }

    @Override
    public String getTituloPrograma() {
        return bundle("");
    }

}
