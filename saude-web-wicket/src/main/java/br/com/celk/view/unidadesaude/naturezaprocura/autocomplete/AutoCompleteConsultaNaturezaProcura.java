package br.com.celk.view.unidadesaude.naturezaprocura.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.unidadesaude.naturezaprocura.autocomplete.restricaocontainer.RestricaoContainerNaturezaProcura;
import br.com.ksisolucoes.bo.basico.dto.QueryConsultaNaturezaProcuraDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaNaturezaProcura extends AutoCompleteConsulta<NaturezaProcura> { 

    public AutoCompleteConsultaNaturezaProcura(String id) {
        super(id);
    }

    public AutoCompleteConsultaNaturezaProcura(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaNaturezaProcura(String id, IModel<NaturezaProcura> model) {
        super(id, model);
    }

    public AutoCompleteConsultaNaturezaProcura(String id, IModel<NaturezaProcura> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(NaturezaProcura.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(NaturezaProcura.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(NaturezaProcura.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerNaturezaProcura(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<NaturezaProcura, QueryConsultaNaturezaProcuraDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaNaturezaProcuraDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarNaturezaProcura(dataPaging);
                    }

                    @Override
                    public QueryConsultaNaturezaProcuraDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaNaturezaProcuraDTOParam param = new QueryConsultaNaturezaProcuraDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaNaturezaProcuraDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(NaturezaProcura.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return NaturezaProcura.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("naturezaProcura");
    }
}
