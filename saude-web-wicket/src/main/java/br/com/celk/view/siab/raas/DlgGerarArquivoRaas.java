package br.com.celk.view.siab.raas;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgGerarArquivoRaas extends Window {

    public DlgGerarArquivoRaas(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(new PnlGerarArquivoRaas(getContentId()) {
            @Override
            public void fechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void atualizarTabela(AjaxRequestTarget target) {
                DlgGerarArquivoRaas.this.atualizarTabela(target);
            }
        });

        setInitialHeight(105);
        setInitialWidth(785);

        setTitle(BundleManager.getString("periodo"));
    }

    public abstract void atualizarTabela(AjaxRequestTarget target);

}
