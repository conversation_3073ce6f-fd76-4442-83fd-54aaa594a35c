package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoDermatoseOcupacionalDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoDermatoseOcupacionalPage extends FichaInvestigacaoAgravoBasePage {
    private final String CSSFILE = "FichaInvestigacaoAgravoDermatoseOcupacionalPage.css";

    private InvestigacaoAgravoDermatoseOcupacional investigacaoAgravoDermatoseOcupacional;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DropDown ddSituacaoMercadoTrabalho;
    private InputField txtSituacaoMercadoTrabalhoOutros;
    private InputField txtTempoTrabalho;
    private DropDown ddTipoTempoTrabalho;

    private DropDown ddHipertensaoArterial;
    private DropDown ddDiabetesMillitus;
    private DropDown ddHanseniase;
    private DropDown ddTranstornoMental;
    private DropDown ddTuberculose;
    private DropDown ddAsma;
    private DropDown ddOutro;
    private InputField txtOutrosAgravosAssociados;

    private InputField txtTempoExposicao;
    private DropDown ddTipoTempoExposicao;
    private DropDown ddRegimeTratamento;

    private WebMarkupContainer containerDadosEmpresaContratante;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DisabledInputField txtCnpjCpf;
    private DisabledInputField txtCnae;
    private DisabledInputField txtEndereco;
    private DisabledInputField txtMunicipio;
    private DisabledInputField txtUF;
    private InputField txtDistrito;
    private DisabledInputField txtBairro;
    private DisabledInputField txtNumero;
    private InputField txtPontoReferencia;
    private InputField txtTelefone;
    private DropDown ddEmpresaTerceirizada;

    private WebMarkupContainer containerDermatosesOcupacionais;
    private DropDown ddPrincipalAgenteCausador;
    private InputField txtOutrosPrincipalAgenteCausador;
    private DropDown ddLocalizacaoLesao;
    private InputField txtOutrosLocalizacaoLesao;
    private DropDown ddTesteEpicutaneoPositivo;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;

    private WebMarkupContainer containerConclusao;
    private DropDown ddAfastamentoTrabalho;
    private InputField txtTempoAfastamentoTrabalho;
    private DropDown ddTipoTempoAfastamentoTrabalho;
    private DropDown ddComAfastamentoTrabalho;
    private DropDown ddTrabalhoresComDoenca;

    private DropDown ddAfastamentoPostoTrabalho;
    private DropDown ddAdocaoMudancaTrabalho;
    private DropDown ddAdocaoProtecaoColetiva;
    private DropDown ddAfastamentoLocalTrabalho;
    private DropDown ddAdocaoProtecaoIndividual;
    private DropDown ddNenhum;
    private InputField txtOutrosCondutaGeral;

    private DropDown ddEvolucaoCaso;
    private DateChooser dataObito;
    private DropDown ddCAT;

    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public FichaInvestigacaoAgravoDermatoseOcupacionalPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoDermatoseOcupacional = InvestigacaoAgravoDermatoseOcupacional.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoDermatoseOcupacional == null) {
            investigacaoAgravoDermatoseOcupacional = new InvestigacaoAgravoDermatoseOcupacional();
            investigacaoAgravoDermatoseOcupacional.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoDermatoseOcupacional.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoDermatoseOcupacional.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoDermatoseOcupacional.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoDermatoseOcupacional.setOcupacaoCbo(tabelaCbo);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoDermatoseOcupacional)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoDermatoseOcupacionalDTO fichaDTO = new FichaInvestigacaoAgravoDermatoseOcupacionalDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoDermatoseOcupacional(investigacaoAgravoDermatoseOcupacional);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoDermatoseOcupacional.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoDermatoseOcupacional.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoDermatoseOcupacional proxy = on(InvestigacaoAgravoDermatoseOcupacional.class);

        criarInvestigacao(proxy);
        criarAntecedentesEpidemiologicos(proxy);
        criarDermatosesOcupacionais(proxy);
        criarConclusao(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);

    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {

        carregarInvestigacao();
        carregarAntecedentesEpidemiologicos();
        carregarDadosEmpresaContratante();
        carregarDermatosesOcupacionais();
        carregarConclusao();
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        InvestigacaoAgravoDermatoseOcupacional proxy = on(InvestigacaoAgravoDermatoseOcupacional.class);

    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoDermatoseOcupacionalDTO dto = (FichaInvestigacaoAgravoDermatoseOcupacionalDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoDermatoseOcupacional(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoDermatoseOcupacionalDTO fichaDTO = (FichaInvestigacaoAgravoDermatoseOcupacionalDTO) getFichaDTO();

        if (investigacaoAgravoDermatoseOcupacional.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoDermatoseOcupacional.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoDermatoseOcupacional.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoDermatoseOcupacionalPage.class, CSSFILE))
        );
    }

    private void criarInvestigacao(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void carregarInvestigacao() {
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataInvestigacao, !isModoLeitura(), true, null);
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
    }

    private void criarAntecedentesEpidemiologicos(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        ddSituacaoMercadoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoMercadoTrabalho()), InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.values(), true);
        txtSituacaoMercadoTrabalhoOutros = new InputField(path(proxy.getSituacaoMercadoTrabalhoOutros()));
        txtTempoTrabalho = new InputField(path(proxy.getTempoTrabalhoOcupacao()));
        ddTipoTempoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getTempoTrabalhoOcupacaoUnidadeMedida()), InvestigacaoAgravoDermatoseOcupacionalEnum.TempoTrabalhoEnum.values(), true);

        //container dentro do outro

        ddHipertensaoArterial = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoHipertensaoArterial()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDiabetesMillitus = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoDiabetesMellitus()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddHanseniase = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoHanseniase()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddTranstornoMental = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoTranstornoMental()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddTuberculose = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoTuberculose()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddAsma = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAsma()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddOutro = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoOutro()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        txtOutrosAgravosAssociados = new InputField(path(proxy.getAgravoOutros()));

        txtTempoExposicao = new InputField(path(proxy.getTempoExposicaoAgenteRisco()));
        ddTipoTempoExposicao = DropDownUtil.getIEnumDropDown(path(proxy.getTempoExposicaoAgenteRiscoUnidadeMedida()), InvestigacaoAgravoDermatoseOcupacionalEnum.TempoTrabalhoEnum.values(), true);
        ddRegimeTratamento = DropDownUtil.getIEnumDropDown(path(proxy.getRegimeTratamento()), InvestigacaoAgravoDermatoseOcupacionalEnum.RegimeTratamentoEnum.values(), true);

        containerAntecedentesEpidemiologicos.add(ddSituacaoMercadoTrabalho, txtSituacaoMercadoTrabalhoOutros, txtTempoTrabalho, ddTipoTempoTrabalho,
                ddHipertensaoArterial, ddDiabetesMillitus, ddHanseniase, ddTranstornoMental, ddTranstornoMental, ddTuberculose, ddAsma, ddOutro,
                txtOutrosAgravosAssociados, txtTempoExposicao, ddTipoTempoExposicao, ddRegimeTratamento
        );

        criarDadosEmpresaContratante();
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void carregarAntecedentesEpidemiologicos() {

        FichaInvestigacaoAgravoHelper.enableDisableInput(txtSituacaoMercadoTrabalhoOutros, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoTrabalho, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoTempoTrabalho, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddHipertensaoArterial, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddDiabetesMillitus, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddHanseniase, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTranstornoMental, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTuberculose, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAsma, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddOutro, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutrosAgravosAssociados, false, false, null);

        containerDadosEmpresaContratante.setEnabled(false);

        ddSituacaoMercadoTrabalho.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtTempoTrabalho.setEnabled(!isModoLeitura() && !(InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.DESEMPREGADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())
                        || InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.IGNORADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())));

                ddTipoTempoTrabalho.setEnabled(!isModoLeitura() && !(InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.DESEMPREGADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())
                        || InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.IGNORADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())));

                containerDadosEmpresaContratante.setEnabled(!isModoLeitura() && !(InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.DESEMPREGADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())
                        || InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.IGNORADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())));

                txtSituacaoMercadoTrabalhoOutros.setEnabled(!isModoLeitura() && !(InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.DESEMPREGADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())
                        || InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.IGNORADO.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())));

                txtSituacaoMercadoTrabalhoOutros.setEnabled(!isModoLeitura() && (InvestigacaoAgravoDermatoseOcupacionalEnum.SituacaoMercadoEnum.OUTROS.value().equals(ddSituacaoMercadoTrabalho.getComponentValue())));

                target.add(txtTempoTrabalho, ddTipoTempoTrabalho, containerDadosEmpresaContratante, txtSituacaoMercadoTrabalhoOutros);
            }
        });

        ddOutro.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                txtOutrosAgravosAssociados.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddOutro, InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.SIM.value()));
                target.add(txtOutrosAgravosAssociados);
                txtOutrosAgravosAssociados.limpar(target);
            }
        });
    }

    private void criarDadosEmpresaContratante() {
        InvestigacaoAgravoDermatoseOcupacional proxy = on(InvestigacaoAgravoDermatoseOcupacional.class);
        containerDadosEmpresaContratante = new WebMarkupContainer("containerDadosEmpresaContratante");
        containerDadosEmpresaContratante.setOutputMarkupId(true);

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaContratante()));
        txtCnpjCpf = new DisabledInputField(path(proxy.getEmpresaContratante().getCnpjFormatado()));
        txtCnae = new DisabledInputField(path(proxy.getEmpresaContratante().getAtividade().getDescricao()));
        txtEndereco = new DisabledInputField(path(proxy.getEmpresaContratante().getRua()));
        txtMunicipio = new DisabledInputField(path(proxy.getEmpresaContratante().getCidade().getDescricao()));
        txtUF = new DisabledInputField(path(proxy.getEmpresaContratante().getCidade().getEstado().getSigla()));
        txtDistrito = new InputField(path(proxy.getEmpresaDistrito()));
        txtBairro = new DisabledInputField(path(proxy.getEmpresaContratante().getBairro()));
        txtNumero = new DisabledInputField(path(proxy.getEmpresaContratante().getNumero()));
        txtPontoReferencia = new InputField(path(proxy.getEmpresaPontoReferencia()));
        txtTelefone = new InputField(path(proxy.getEmpresaTelefone()));
        ddEmpresaTerceirizada = DropDownUtil.getIEnumDropDown(path(proxy.getEmpregadorEmpresaTerceirizada()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.values(), true);

        containerDadosEmpresaContratante.add(autoCompleteConsultaEmpresa, txtCnpjCpf, txtCnae, txtEndereco, txtMunicipio,
                txtUF, txtDistrito, txtBairro, txtNumero, txtPontoReferencia, txtTelefone, ddEmpresaTerceirizada
        );

        containerAntecedentesEpidemiologicos.add(containerDadosEmpresaContratante);
    }

    private void carregarDadosEmpresaContratante() {
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                if (empresa.getCnpjFormatado() != null) {
                    txtCnpjCpf.setComponentValue(empresa.getCnpjFormatado());
                }
                if (empresa.getAtividade() != null && empresa.getAtividade().getDescricao() != null) {
                    txtCnae.setComponentValue(empresa.getAtividade().getDescricao());
                }
                if (empresa.getRua() != null) {
                    txtEndereco.setComponentValue(empresa.getRua());
                }
                if (empresa.getCidade() != null && empresa.getCidade().getDescricao() != null) {
                    txtMunicipio.setComponentValue(empresa.getCidade().getDescricao());
                }
                Cidade cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(investigacaoAgravoDermatoseOcupacional.getEmpresaContratante().getCidade().getCodigo());
                if (cidadeTemp != null && cidadeTemp.getEstado() != null && cidadeTemp.getEstado().getSigla() != null) {
                    txtUF.setComponentValue(cidadeTemp.getEstado().getSigla());
                }
                if (empresa.getEnderecoEstruturadoDistrito() != null && empresa.getEnderecoEstruturadoDistrito().getDescricao() != null) {
                    txtDistrito.setComponentValue(empresa.getEnderecoEstruturadoDistrito().getDescricao());
                }
                if (empresa.getBairro() != null) {
                    txtBairro.setComponentValue(empresa.getBairro());
                }
                if (empresa.getNumero() != null) {
                    txtNumero.setComponentValue(empresa.getNumero());
                }
                if (empresa.getTelefoneFormatado() != null) {
                    txtTelefone.setComponentValue(empresa.getTelefoneFormatado());
                }
                target.add(txtCnpjCpf, txtCnae, txtEndereco, txtMunicipio, txtUF, txtDistrito, txtBairro, txtNumero, txtTelefone);
            }
        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                txtCnpjCpf.limpar(target);
                txtCnae.limpar(target);
                txtEndereco.limpar(target);
                txtMunicipio.limpar(target);
                txtUF.limpar(target);
                txtDistrito.limpar(target);
                txtBairro.limpar(target);
                txtNumero.limpar(target);
                txtTelefone.limpar(target);
                txtPontoReferencia.limpar(target);
            }
        });
    }

    private void criarDermatosesOcupacionais(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerDermatosesOcupacionais = new WebMarkupContainer("containerDermatosesOcupacionais");
        containerDermatosesOcupacionais.setOutputMarkupId(true);

        ddPrincipalAgenteCausador = DropDownUtil.getIEnumDropDown(path(proxy.getPrincipalAgenteDermatose()), InvestigacaoAgravoDermatoseOcupacionalEnum.AgenteCausadorEnum.values(), true);
        txtOutrosPrincipalAgenteCausador = new InputField(path(proxy.getPrincipalAgenteDermatoseOutros()));
        ddLocalizacaoLesao = DropDownUtil.getIEnumDropDown(path(proxy.getLocalizacaoLesao()), InvestigacaoAgravoDermatoseOcupacionalEnum.LocalizacaoLesaoEnum.values(), true);
        txtOutrosLocalizacaoLesao = new InputField(path(proxy.getLocalizacaoLesaoOutros()));
        ddTesteEpicutaneoPositivo = DropDownUtil.getIEnumDropDown(path(proxy.getTesteEpicutaneoPositivo()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getDiagnosticoEspecifico()));

        containerDermatosesOcupacionais.add(ddPrincipalAgenteCausador, txtOutrosPrincipalAgenteCausador, ddLocalizacaoLesao,
                txtOutrosLocalizacaoLesao, ddTesteEpicutaneoPositivo, autoCompleteConsultaCid);
        getContainerInformacoesComplementares().add(containerDermatosesOcupacionais);
    }

    private void carregarDermatosesOcupacionais() {
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutrosPrincipalAgenteCausador, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutrosLocalizacaoLesao, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddPrincipalAgenteCausador, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalizacaoLesao, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTesteEpicutaneoPositivo, true, true, null);
        autoCompleteConsultaCid.setRequired(true);

        ddPrincipalAgenteCausador.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtOutrosPrincipalAgenteCausador.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddPrincipalAgenteCausador, InvestigacaoAgravoDermatoseOcupacionalEnum.AgenteCausadorEnum.OUTROS.value()));
                target.add(txtOutrosPrincipalAgenteCausador);
            }
        });

        ddLocalizacaoLesao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtOutrosLocalizacaoLesao.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddLocalizacaoLesao, InvestigacaoAgravoDermatoseOcupacionalEnum.LocalizacaoLesaoEnum.OUTRO.value()));
                target.add(txtOutrosLocalizacaoLesao);
            }
        });
    }

    private void criarConclusao(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddAfastamentoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getAfastamentoTrabalhoTratamento()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        txtTempoAfastamentoTrabalho = new InputField(path(proxy.getTempoAfastamentoTrabalho()));
        ddTipoTempoAfastamentoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getTempoAfastamentoTrabalhoUnidadeMedida()), InvestigacaoAgravoDermatoseOcupacionalEnum.TempoTrabalhoEnum.values(), true);
        ddComAfastamentoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getAfastamentoTrabalhoMelhoraPiora()), InvestigacaoAgravoDermatoseOcupacionalEnum.ComAfastamentoTrabalhoEnum.values(), true);
        ddTrabalhoresComDoenca = DropDownUtil.getIEnumDropDown(path(proxy.getOutrosTrabalhadoresMesmaDoenca()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);

        ddAfastamentoPostoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralAfastamentoAgenteRisco()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        ddAdocaoMudancaTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralMudancaOrganizacao()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        ddAdocaoProtecaoColetiva = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralProtecaoColetiva()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        ddAfastamentoLocalTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralAfastamentoLocal()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        ddAdocaoProtecaoIndividual = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralProtecaoIndividual()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        ddNenhum = DropDownUtil.getIEnumDropDown(path(proxy.getCondutaGeralNenhum()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.getSimNao(), true);
        txtOutrosCondutaGeral = new InputField(path(proxy.getCondutaGeralOutros()));

        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.values(), true);
        dataObito = new DateChooser(path(proxy.getDataObito()));
        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddCAT = DropDownUtil.getIEnumDropDown(path(proxy.getEmitidaCAT()), InvestigacaoAgravoDermatoseOcupacionalEnum.SimNaoIgnoradoEnum.values(), true);

        containerConclusao.add(ddAfastamentoTrabalho, txtTempoAfastamentoTrabalho, ddTipoTempoAfastamentoTrabalho,
                ddComAfastamentoTrabalho, ddTrabalhoresComDoenca, ddAfastamentoPostoTrabalho, ddAdocaoMudancaTrabalho,
                ddAdocaoProtecaoColetiva, ddAfastamentoLocalTrabalho, ddAdocaoProtecaoIndividual, ddNenhum,
                txtOutrosCondutaGeral, ddEvolucaoCaso, dataObito, ddCAT
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAfastamentoTrabalho, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoAfastamentoTrabalho, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCAT, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCAT, true, true, null);

        ddAfastamentoTrabalho.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAfastamentoTrabalho, InvestigacaoAgravoDermatoseOcupacionalEnum.ComAfastamentoTrabalhoEnum.MELHORA.value())){
                    txtTempoAfastamentoTrabalho.setEnabled(true);
                    ddTipoTempoAfastamentoTrabalho.setEnabled(true);
                    ddComAfastamentoTrabalho.setEnabled(true);

                }
                else{
                    txtTempoAfastamentoTrabalho.setEnabled(false);
                    ddTipoTempoAfastamentoTrabalho.setEnabled(false);
                    ddComAfastamentoTrabalho.setEnabled(false);
                }

                target.add(txtTempoAfastamentoTrabalho, ddTipoTempoAfastamentoTrabalho, ddComAfastamentoTrabalho);
            }
        });
        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(!isModoLeitura() && ((FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.OBITO_DOENCA.value()))
                || (FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoDermatoseOcupacionalEnum.EvolucaoCasoEnum.OBITO_OUTRA_CAUSA.value())))){
                    dataObito.setEnabled(true);
                }
                else{
                    dataObito.setEnabled(false);
                }

                target.add(dataObito);
            }
        });
    }

    private void criarObservacoes(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoDermatoseOcupacional proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }

}
