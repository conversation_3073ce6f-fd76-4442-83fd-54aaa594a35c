package br.com.celk.view.controle.reprocessaAtendimentosEsus;

import br.com.celk.atendimento.prontuario.reprocessaAtendimentosEsus.DTOErroReprocessaAtendimentos;
import br.com.celk.atendimento.prontuario.reprocessaAtendimentosEsus.DTOTipoAtendimentoNaoProcessado;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.template.annotation.PainelControle;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@PainelControle
public class ReprocessaAtendimentosEsusPage extends BasePage {

    private Table tblTipoAtendimento;
    private List<DTOTipoAtendimentoNaoProcessado> tipoAtendimentoList;
    private List<DTOErroReprocessaAtendimentos> errosList;
    private HashMap<String, Integer> erros;
    private Table tblErros;

    public ReprocessaAtendimentosEsusPage() {
        init();
    }

    private void init() {
        errosList = new ArrayList<>();
        try {
            tipoAtendimentoList = BOFactory.getBO(AtendimentoGeralFacade.class).buscaTiposAtendimentoNaoForamEsus();
        } catch (DAOException | ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        Form form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel(this));
            form.add(new AbstractAjaxButton("btnProcurar") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) {
                    try {
                        erros = BOFactory.getBO(AtendimentoGeralFacade.class).reprocessaAtendimentosNaoForamEsus();
                    } catch (DAOException | ValidacaoException e) {
                         br.com.ksisolucoes.util.log.Loggable.log.error(e);
                    }
                    errosList = new ArrayList<>();
                    for (String erro : erros.keySet()) {
                        errosList.add(new DTOErroReprocessaAtendimentos(erro.replaceAll("\\<[^>]*>", ""), erros.get(erro)));
                    }
                    tblErros.populate();
                    target.add(tblErros);
                }
            });
        form.add(tblErros = new Table("tblErros", getColumnsErros(), getErrosCollectionProvider()));
        form.add(tblTipoAtendimento = new Table("tblAtendimentos", getColumns(), getCollectionProvider()));
        tblTipoAtendimento.populate();
        add(form);
    }


    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        DTOTipoAtendimentoNaoProcessado atendimentoMockup = on(DTOTipoAtendimentoNaoProcessado.class);
        columns.add(createColumn(bundle("descricao"), atendimentoMockup.getDescricaoTipoAtendimento()));
        columns.add(createColumn(bundle("total"), atendimentoMockup.getTotal()));
        return columns;
    }

    private List<IColumn> getColumnsErros() {
        List<IColumn> columns = new ArrayList<IColumn>();
        DTOErroReprocessaAtendimentos atendimentoMockup = on(DTOErroReprocessaAtendimentos.class);
        columns.add(createColumn(bundle("erro"), atendimentoMockup.getErro()));
        columns.add(createColumn(bundle("total"), atendimentoMockup.getTotal()));
        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return tipoAtendimentoList;
            }
        };
    }

    private ICollectionProvider getErrosCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return errosList;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("reprocessaAtendimentosEsus");
    }

}
