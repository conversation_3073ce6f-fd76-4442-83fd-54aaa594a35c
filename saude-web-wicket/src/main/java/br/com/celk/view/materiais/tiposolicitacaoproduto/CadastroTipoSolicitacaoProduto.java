package br.com.celk.view.materiais.tiposolicitacaoproduto;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TipoSolicitacaoProduto;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoSolicitacaoProduto extends CadastroPage<TipoSolicitacaoProduto>{
    
    private InputField txtDescricao;
    
    public CadastroTipoSolicitacaoProduto(TipoSolicitacaoProduto object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoSolicitacaoProduto(TipoSolicitacaoProduto object) {
        this(object, false);
    }

    public CadastroTipoSolicitacaoProduto() {
        this(null); 
    }

    @Override
    public void init(Form form) {
        TipoSolicitacaoProduto proxy = on(TipoSolicitacaoProduto.class);
        
        form.add(txtDescricao = new RequiredInputField<String>(path(proxy.getDescricao())));
    }
    
    @Override
    public Class<TipoSolicitacaoProduto> getReferenceClass() {
        return TipoSolicitacaoProduto.class;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
    
    @Override
    public Class getResponsePage() {
        return ConsultaTipoSolicitacaoProduto.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoSolicitacaoProdutos");
    }  

    @Override
    public Object salvar(TipoSolicitacaoProduto object) throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(TipoSolicitacaoProduto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoSolicitacaoProduto.PROP_DESCRICAO, object.getDescricao()));
        
        if(object.getCodigo() != null){
            load.addParameter(new QueryCustom.QueryCustomParameter(TipoSolicitacaoProduto.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }
        
        TipoSolicitacaoProduto tsp = load.start().getVO();
        
        if(tsp != null){
            throw new ValidacaoException(bundle("msgJaExisteTipoSolicitacaoProdutoComEssaDescricao"));
        }
        
        return super.salvar(object);
    }
}