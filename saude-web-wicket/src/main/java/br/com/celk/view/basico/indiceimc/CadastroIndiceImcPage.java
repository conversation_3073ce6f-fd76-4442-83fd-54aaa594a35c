package br.com.celk.view.basico.indiceimc;

import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.longfield.RequiredLongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.basico.IndiceImc;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;

/**
 *
 * <AUTHOR>
 */
public class CadastroIndiceImcPage extends CadastroPage<IndiceImc> {

    private InputField txtSituacao;
    private DropDown<String> dropDownSexo;
    private DropDown<Integer> dropDownisGestante;
    private DropDown<Integer> dropDownIdadeGestacional;
    private DropDown<String> dropDownSexo2;
    private RequiredLongField idadeInicial;
    private LongField idadeFinal;

    public CadastroIndiceImcPage(IndiceImc object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroIndiceImcPage(IndiceImc object) {
        this(object, false);
    }

    public CadastroIndiceImcPage() {
        this(null);
    }

    @Override
    public void init(Form<IndiceImc> form) {
        IndiceImc indiceImc = form.getModel().getObject();
        String siglaSexo = indiceImc.getSexo();
        
        IndiceImc proxy = on(IndiceImc.class);
        form.add(txtSituacao = new RequiredInputField(path(proxy.getSituacao())));
        form.add(dropDownisGestante = DropDownUtil.getNaoSimIntegerDropDown(path(proxy.getGestante())));
        form.add(dropDownIdadeGestacional = populaDropIdadeGestacional(proxy));
        form.add(dropDownSexo2 = getDropDownSexo(proxy));
        form.add(idadeInicial = (RequiredLongField) new RequiredLongField(path(proxy.getIdadeInicial())).setVMax(1799L).setVMin(0L));
        form.add(idadeFinal = new LongField(path(proxy.getIdadeFinal())).setVMax(1800L).setVMin(0L));
        form.add(new RequiredDoubleField(path(proxy.getFaixaInicial())));
        form.add(new RequiredDoubleField(path(proxy.getFaixaFinal())));

        habilitarCampos(dropDownisGestante.getComponentValue(), null);
        
        dropDownSexo2.setComponentValue(siglaSexo);
        
        dropDownisGestante.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                habilitarCampos(dropDownisGestante.getComponentValue(), art);
                art.add(dropDownIdadeGestacional, idadeInicial, idadeFinal, dropDownSexo2);

            }
        });
    }

    private void habilitarCampos(Integer gestante, AjaxRequestTarget art) {
        if (RepositoryComponentDefault.SIM_INTEGER.equals(gestante)) {
            dropDownIdadeGestacional.setEnabled(true);
            idadeInicial.setEnabled(false);
            idadeFinal.setEnabled(false);
            dropDownSexo2.setEnabled(false);
            dropDownSexo2.setComponentValue(RepositoryComponentDefault.SIGLA_FEMININO);
            if (art != null) {
                idadeFinal.limpar(art);
                idadeInicial.limpar(art);
            }
        } else {
            dropDownIdadeGestacional.setComponentValue(0);
            dropDownIdadeGestacional.setEnabled(false);
            idadeInicial.setEnabled(true);
            idadeFinal.setEnabled(true);
            dropDownSexo2.setEnabled(true);
            dropDownSexo2.setComponentValue(RepositoryComponentDefault.AMBOS);
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtSituacao;
    }

    @Override
    public Class<IndiceImc> getReferenceClass() {
        return IndiceImc.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaIndiceImcPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroIndiceImc");
    }

    private DropDown getDropDownSexo(IndiceImc proxy) {

        if (dropDownSexo == null) {
            dropDownSexo = new DropDown<String>(path(proxy.getSexo()));
            dropDownSexo.addChoice(null, BundleManager.getString("ambos"));
            dropDownSexo.addChoice(RepositoryComponentDefault.SIGLA_FEMININO, BundleManager.getString("feminino"));
            dropDownSexo.addChoice(RepositoryComponentDefault.SIGLA_MASCULINO, BundleManager.getString("masculino"));
        }

        return dropDownSexo;
    }

    @Override
    public Object salvar(IndiceImc object) throws DAOException, ValidacaoException {
        if (object.getIdadeFinal() == null || object.getIdadeFinal() == 0L) {
            object.setIdadeFinal(1800L);
        }
        return super.salvar(object); //To change body of generated methods, choose Tools | Templates.
    }

    private DropDown populaDropIdadeGestacional(IndiceImc proxy) {
        if (dropDownIdadeGestacional == null) {
            dropDownIdadeGestacional = new DropDown<Integer>(path(proxy.getIdadeGestacional()));
            dropDownIdadeGestacional.addChoice(null, "");
            for (int i = 1; i <= 42; i++) {
                dropDownIdadeGestacional.addChoice(i, String.valueOf(i));
            }

        }
        return dropDownIdadeGestacional;
    }
}
