package br.com.celk.view.agenda.agendamento.tfd.processo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.agendamento.tfd.consultaprocesso.dlg.DlgOcorrenciaLaudo;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.QueryPagerConsultaLaudoDTO;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.QueryPagerConsultaProcessoTfdDTOparam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaRetornoTfdPage extends BasePage {

    private Form<QueryPagerConsultaProcessoTfdDTOparam> form;

    private PnlChoicePeriod pnlChoicePeriod;
    private PageableTable pageableTable;
    private DlgOcorrenciaLaudo dlgOcorrenciaLaudo;

    public ConsultaRetornoTfdPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(new QueryPagerConsultaProcessoTfdDTOparam()));

        form.add(new InputField<String>("nomePaciente"));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true).setTfd(true));
        form.add(new InputField("numeroPedido"));
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo"));
        pnlChoicePeriod.setDefaultOutro();
//        form.add(getDropDownSituacao("situacao"));
        form.add(pageableTable = new PageableTable("table", getColumns(), getDataProvider()));
        form.add(new ProcurarButton<QueryPagerConsultaProcessoTfdDTOparam>("btnProcurar", pageableTable) {
            @Override
            public QueryPagerConsultaProcessoTfdDTOparam getParam() {
                return ConsultaRetornoTfdPage.this.getParam();
            }
        });

        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(QueryPagerConsultaLaudoDTO.class);
        QueryPagerConsultaLaudoDTO proxy = on(QueryPagerConsultaLaudoDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("pedido"), VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoProcedimento"), VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataCadastro"), VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_DATA_CADASTRO)));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<QueryPagerConsultaLaudoDTO>() {
            @Override
            public void customizeColumn(QueryPagerConsultaLaudoDTO rowObject) {
                addAction(ActionType.OCORRENCIA, rowObject, new IModelAction<QueryPagerConsultaLaudoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaLaudoDTO modelObject) throws ValidacaoException, DAOException {
                        onOcorrencia(target, modelObject.getLaudoTfd());
                    }
                });
                addAction(ActionType.RETORNO, rowObject, new IModelAction<QueryPagerConsultaLaudoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaLaudoDTO modelObject) throws ValidacaoException, DAOException {
                        onRetorno(target, modelObject.getLaudoTfd());
                    }
                });
            }
        };
    }

    private void onOcorrencia(AjaxRequestTarget target, LaudoTfd modelObject) {
        if (dlgOcorrenciaLaudo == null) {
            addModal(target, dlgOcorrenciaLaudo = new DlgOcorrenciaLaudo(newModalId()));
        }
        dlgOcorrenciaLaudo.show(target, modelObject.getCodigo());
    }

    private void onRetorno(AjaxRequestTarget target, LaudoTfd modelObject) {
        setResponsePage(new CadastroRetornoStep1Page(modelObject));
    }

    private IPagerProvider<QueryPagerConsultaLaudoDTO, QueryPagerConsultaProcessoTfdDTOparam> getDataProvider() {
        return new QueryPagerProvider<QueryPagerConsultaLaudoDTO, QueryPagerConsultaProcessoTfdDTOparam>() {

            @Override
            public DataPagingResult<QueryPagerConsultaLaudoDTO> executeQueryPager(DataPaging<QueryPagerConsultaProcessoTfdDTOparam> dataPaging) throws DAOException, ValidacaoException {
                DataPagingResult<QueryPagerConsultaLaudoDTO> result = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarProcessoTfdPager(dataPaging);
                return result;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(QueryPagerConsultaLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_DATA_CADASTRO), false);
            }

            @Override
            public void customizeParam(QueryPagerConsultaProcessoTfdDTOparam param) {
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
            }
        };
    }

    private QueryPagerConsultaProcessoTfdDTOparam getParam() {
        QueryPagerConsultaProcessoTfdDTOparam param = form.getModel().getObject();

        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_DATA_CADASTRO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_DATA_ENTREGA_PACIENTE));
        param.setSituacao(LaudoTfd.StatusLaudoTfd.AUTORIZADO.value());

        return param;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("retornoTfd");
    }
}
