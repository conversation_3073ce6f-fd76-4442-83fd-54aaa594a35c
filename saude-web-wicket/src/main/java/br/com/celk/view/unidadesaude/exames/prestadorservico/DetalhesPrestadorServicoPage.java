package br.com.celk.view.unidadesaude.exames.prestadorservico;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.agendamento.exame.dto.QueryPagerConsultaExamePrestadorProcedimentoDTOParam;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private

public class DetalhesPrestadorServicoPage extends BasePage {

    private PageableTable<ExamePrestadorProcedimento> table;
    private ExamePrestador examePrestador;
    private String tipoTeto;

    public DetalhesPrestadorServicoPage(Long codigoExamePrestador) {
        try {
            init(codigoExamePrestador);
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void init(Long codigoExamePrestador) throws DAOException, ValidacaoException {
        examePrestador = LoadManager.getInstance(ExamePrestador.class)
                .setId(codigoExamePrestador)
                .start().getVO();

        final Form form = new Form("form", new CompoundPropertyModel<ExamePrestador>(examePrestador));
        form.add(new DisabledInputField("tipoExame.descricao"));
        form.add(new DisabledInputField("prestador.descricao"));
        form.add(new DisabledInputField("tipoTeto", new PropertyModel(this, "tipoTeto")));
        form.add(new DisabledDoubleField("tetoFinanceiro"));

        ExameCotaPpi ecp = null;
        if (examePrestador.getTipoExame() != null && examePrestador.getTipoExameSecundario() != null) {
            ecp = LoadManager.getInstance(ExameCotaPpi.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), examePrestador.getTipoExame().getCodigo()))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), examePrestador.getTipoExameSecundario() != null ? examePrestador.getTipoExameSecundario().getCodigo() : null))))))
                    .start().getVO();
        } else if (examePrestador.getTipoExame() != null) {
            ecp = LoadManager.getInstance(ExameCotaPpi.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), examePrestador.getTipoExame().getCodigo()))
                    .start().getVO();
        }

        if (ecp != null) {
            tipoTeto = ecp.getDescricaoTipoTeto();
        } else {
            tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
        }

        form.add(table = new PageableTable<ExamePrestadorProcedimento>("table", getColumns(), getPagerProviderEndereco()));
        table.populate();

        form.add(new VoltarButton("btnVoltar"));
        add(form);

    }

    private List<ISortableColumn<ExamePrestadorProcedimento>> getColumns() {
        List<ISortableColumn<ExamePrestadorProcedimento>> columns = new ArrayList<ISortableColumn<ExamePrestadorProcedimento>>();

        ColumnFactory columnFactory = new ColumnFactory(ExamePrestadorProcedimento.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_REFERENCIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("procedimento"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("exame"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO)));

        return columns;
    }

    public IPagerProvider getPagerProviderEndereco() {
        return new QueryPagerProvider() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                QueryPagerConsultaExamePrestadorProcedimentoDTOParam param = new QueryPagerConsultaExamePrestadorProcedimentoDTOParam();
                param.setExamePrestador(examePrestador);
                dataPaging.setParam(param);
                return BOFactoryWicket.getBO(ExameFacade.class).queryPagerConsultaExamePrestadorProcedimento(dataPaging);
            }
        };
    }

    public Class getResponsePage() {
        return ConsultaPrestadorServicoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesPrestadorServico");
    }
}
