package br.com.celk.view.unidadesaude.esus.domicilio.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.RowNumberColumn;
import br.com.celk.esus.domicilio.CadastroDomicilioDTO;
import br.com.celk.esus.domicilio.ComponenteDomicilioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlComponentesDomicilio extends Panel {

    private Table tblComponentes;
    private CadastroDomicilioDTO cadastroDomicilioDTO;

    public PnlComponentesDomicilio(String id) {
        super(id);
        init();
    }

    private void init() {
        AbstractAjaxButton btnFechar;
        add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        btnFechar.setDefaultFormProcessing(false);

        add(tblComponentes = new Table("tblComponentes", getColumns(), getCollectionProvider()));
        tblComponentes.setScrollX("1500px");
        tblComponentes.setScrollY("170px");
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ComponenteDomicilioDTO proxy = on(ComponenteDomicilioDTO.class);

        columns.add(new RowNumberColumn<ComponenteDomicilioDTO>());
        columns.add(createColumn(bundle("nome"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getDescricaoSocialFormatado()));
        columns.add(createColumn(bundle("dataNascimento"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getDataNascimento()));
        columns.add(createColumn(bundle("idade"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getIdade()));
        columns.add(createColumn(bundle("sexo"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getSexoFormatado()));
        columns.add(createColumn(bundle("escolaridade"), proxy.getUsuarioCadsusEsus().getDescricaoNivelEscolaridade()));
        columns.add(createColumn(bundle("ocupacao"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getTabelaCbo().getDescricaoFormatado()));
        columns.add(createColumn(bundle("responsavel"), proxy.getUsuarioCadsusDomicilio().getResponsavelFormatado()));
        columns.add(createColumn(bundle("situacao"), proxy.getUsuarioCadsusDomicilio().getUsuarioCadsus().getDescricaoSituacao()));
        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return cadastroDomicilioDTO.getComponentes();
            }
        };
    }

    public void setModelObject(CadastroDomicilioDTO cadastroDomicilioDTO) {
        this.cadastroDomicilioDTO = cadastroDomicilioDTO;
        tblComponentes.populate();
    }

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
