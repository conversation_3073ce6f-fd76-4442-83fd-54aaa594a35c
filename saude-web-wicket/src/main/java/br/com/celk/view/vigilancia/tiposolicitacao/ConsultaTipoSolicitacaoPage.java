package br.com.celk.view.vigilancia.tiposolicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.FiscalNaRuaUtil;
import br.com.celk.view.vigilancia.tiposolicitacao.customize.CustomizeConsultaTipoSolicitacao;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaTipoSolicitacaoPage extends ConsultaPage<TipoSolicitacao, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long ativo = RepositoryComponentDefault.SIM_LONG;

    public ConsultaTipoSolicitacaoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(DropDownUtil.getSimNaoLongDropDown("ativo", true, false));

        setExibeExpandir(true);

        getPageableTable().getDataProvider().setParameters(getParameters());
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(TipoSolicitacao.class);
        TipoSolicitacao proxy = on(TipoSolicitacao.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(bundle("descricao"), path(proxy.getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("ativo"),path(proxy.getAtivo()), path(proxy.getAtivoFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("inspeciona"),path(proxy.getFlagInspeciona()), path(proxy.getInspecionaFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("geraIntimacao"),path(proxy.getFlagGeraIntimacao()), path(proxy.getGeraIntimacaoFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("geraInfracao"),path(proxy.getFlagGeraInfracao()), path(proxy.getGeraInfracaoFormatado())));
        columns.add(columnFactory.createSortableColumn(bundle("geraMulta"),path(proxy.getFlagGeraMulta()), path(proxy.getGeraMultaFormatado())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TipoSolicitacao>() {
            @Override
            public void customizeColumn(final TipoSolicitacao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TipoSolicitacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoSolicitacao modelObject) throws ValidacaoException, DAOException {
                            setResponsePage(new CadastroTipoSolicitacaoPage(modelObject));
                    }
                });

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<TipoSolicitacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoSolicitacao modelObject) throws ValidacaoException, DAOException {
                        modelObject.setAtivo(RepositoryComponentDefault.SIM_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getAtivo().equals(RepositoryComponentDefault.NAO_LONG));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<TipoSolicitacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoSolicitacao modelObject) throws ValidacaoException, DAOException {
                        modelObject.setAtivo(RepositoryComponentDefault.NAO_LONG);
                        BOFactoryWicket.save(modelObject);
                        getPageableTable().populate(target);
                    }
                }).setVisible(rowObject.getAtivo().equals(RepositoryComponentDefault.SIM_LONG));


                ModelActionLinkPanel malp = addAction(ActionType.REENVIAR, rowObject, new IModelAction<TipoSolicitacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoSolicitacao modelObject) throws ValidacaoException, DAOException {
                        enviarTipoSolicitacaoFru(modelObject, target);
                    }
                });

                malp.setTitleBundleKey("reenviarAppFru");
                malp.setVisible(enviarTipoSolicitacaoAppFru(rowObject));
            }


//            @Override
//            public void onDesativa(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                rowObject.setFlagAtivo(RepositoryComponentDefault.NAO);
//                BOFactoryWicket.save(rowObject);
//                getPageableTable().populate(target);
//            }
//
//            @Override
//            public void onAtiva(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                rowObject.setFlagAtivo(RepositoryComponentDefault.SIM);
//                BOFactoryWicket.save(rowObject);
//                getPageableTable().populate(target);
//            }
        };
    }

    private void enviarTipoSolicitacaoFru(TipoSolicitacao modelObject, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Usuario usuarioExecucao = ApplicationSession.get().getSessaoAplicacao().getUsuario();

        getSession().getFeedbackMessages().info(this, BundleManager.getString("msg_processo_requerimento_vigilancia"));
        target.add(super.getNotificationPanel());

        BOFactoryWicket.getBO(VigilanciaFacade.class).reenviarRequerimentosTipoSolicitacaoFiscalRua(modelObject, usuarioExecucao);
        getPageableTable().populate(target);
    }

    public boolean enviarTipoSolicitacaoAppFru(TipoSolicitacao tipoSolicitacao) {
        boolean appHablitado = FiscalNaRuaUtil.habilitaAplicativoFiscalNaRua();
        boolean isAtivo = RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getAtivo());
        boolean enviarApp = RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getFlagEnviarAppFiscalRua());

        return (isAtivo && appHablitado && enviarApp);
    }


    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaTipoSolicitacao()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoSolicitacao.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoSolicitacao.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoSolicitacao.PROP_ATIVO), ativo));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoSolicitacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoSolicitacao");
    }
}
