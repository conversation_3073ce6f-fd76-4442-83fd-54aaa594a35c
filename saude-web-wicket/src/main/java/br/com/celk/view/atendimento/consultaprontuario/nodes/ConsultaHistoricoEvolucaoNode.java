package br.com.celk.view.atendimento.consultaprontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesConsultaProntuarioRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.consultaprontuario.nodes.annotations.ConsultaProntuarioNode;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.ConsultaProntuarioNodeImp;
import br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoevolucao.ConsultaHistoricoEvolucaoPanel;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ConsultaProntuarioNode(NodesConsultaProntuarioRef.CONSULTA_HISTORICO_EVOLUCAO)
public class ConsultaHistoricoEvolucaoNode extends ConsultaProntuarioNodeImp{

    @Override
    public ConsultaProntuarioCadastroPanel getPanel(String id) {
        return new ConsultaHistoricoEvolucaoPanel(id, getTitulo());
    }
    
    @Override
    public Icon32 getIcone() {
        return Icon32.REPORT;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("evolucao");
    }
}
