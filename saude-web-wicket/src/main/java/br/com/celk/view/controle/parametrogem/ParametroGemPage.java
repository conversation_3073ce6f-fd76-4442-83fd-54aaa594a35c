package br.com.celk.view.controle.parametrogem;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.utils.parametros.ParametrosMateriaisUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.controle.parametrogem.dlg.DlgTabbedNiveis;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.ParametroGemDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.geral.interfaces.dto.NiveisParametroGemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.parametrogem.NivelParametroGem;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.basico.ParametroGemPK;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.tree.DefaultTableTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.TableTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.TreeColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.request.resource.ResourceReference;

/**
 *
 * <AUTHOR>
 */
public class ParametroGemPage extends BasePage {

    private static final int MINUTES_TO_WAIT = 1;
    private TableTree tableTree;
    private static final ResourceReference CSS = new CssResourceReference(ParametroGemPage.class, "ParametroGemPage.css");
    private DlgTabbedNiveis dlgTabbedNiveis;
    private HashMap<ParametroGem, NiveisParametroGemDTO> mapParametros;
    private final ParametroTreeProvider provider = new ParametroTreeProvider();
    
    private String nomeParametro;
    
    public ParametroGemPage(PageParameters pageParameters) {
        super(pageParameters);
        init();
    }

    private void init() {
        Form form = new Form("form");

        form.add(new InputField("nomeParametro", new PropertyModel(this, "nomeParametro")));
        
        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target);
            }
        });

        mapParametros = new HashMap<ParametroGem, NiveisParametroGemDTO>();

        tableTree = new DefaultTableTree("treeTable", getColumns(), provider, 999999);
        form.add(tableTree);
        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        warn(BundleManager.getString("warnMessageTempoAtualizacaoParametroGem", MINUTES_TO_WAIT));
        add(form);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<ParametroGem> parametros = new ArrayList<ParametroGem>();
        for (ParametroDTO parametroDTO : provider.getParametros()) {
            parametros.add(parametroDTO.getParametro());
        }

        ParametrosMateriaisUtil.limpaParametroUtilizaLocalizacaoEstoque();

        BOFactoryWicket.getBO(BasicoFacade.class).salvarParametroGem(parametros, mapParametros);

        Page page = new ParametroGemPage(getPageParameters());
        getSession().getFeedbackMessages().info(page, BundleManager.getString("parametroSalvoSucesso"));
        setResponsePage(page);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(CSS));
    }
    
    private void procurar(AjaxRequestTarget target) {
        ParametroGemDTOParam param = new ParametroGemDTOParam();
        param.setNomeParametro(nomeParametro);
        
        provider.recarregarProvider(param);

        target.add(tableTree);
    }

    private List getColumns() {
        List<IColumn<ParametroDTO, String>> result = new LinkedList();

        result.add(new TreeColumn<ParametroDTO, String>(Model.of("Parametro")) {
            @Override
            public void populateItem(Item<ICellPopulator<ParametroDTO>> cellItem, String componentId, final IModel<ParametroDTO> rowModel) {
                super.populateItem(cellItem, componentId, rowModel);
                if (rowModel.getObject().getParametro() != null) {
                    cellItem.get(componentId).add(new AttributeModifier("title", rowModel.getObject().getParametro().getObservacao()));
                    cellItem.get(componentId).add(new AjaxEventBehavior("onclick") {

                        @Override
                        protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                            super.updateAjaxAttributes(attributes);
                            attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
                        }

                        @Override
                        protected void onEvent(AjaxRequestTarget art) {
                            NiveisParametroGemDTO dto;
                            ParametroGem parametroGem = rowModel.getObject().getParametro();
                            List<ParametroGem> lstEmpresas;
                            List<ParametroGem> lstUsuarios;
                            List<ParametroGem> lstCbos;
                            List<ParametroGem> lstTipoProcedimentos = new ArrayList<>();

                            Boolean isParametroDiasMaximoCancelamentoAgendamento = parametroGem.getId().getParametro().equals("diasMaximoCancelamentoAgendamento");

                            if (mapParametros.containsKey(parametroGem)) {
                                lstEmpresas = mapParametros.get(parametroGem).getLstParametroEmpresas();
                                lstUsuarios = mapParametros.get(parametroGem).getLstParametroUsuario();
                                lstCbos = mapParametros.get(parametroGem).getLstParametroCbo();

                                if (isParametroDiasMaximoCancelamentoAgendamento) {
                                    lstTipoProcedimentos = mapParametros.get(parametroGem).getLstParametroTipoProcedimento();
                                }
                            } else {
                                lstEmpresas = LoadManager.getInstance(ParametroGem.class)
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_PARAMETRO), parametroGem.getId().getParametro()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_NIVEL), NivelParametroGem.EMPRESA.getNivel()))
                                        .start().getList();
                                lstUsuarios = LoadManager.getInstance(ParametroGem.class)
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_PARAMETRO), parametroGem.getId().getParametro()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_NIVEL), NivelParametroGem.USUARIO.getNivel()))
                                        .start().getList();
                                lstCbos = LoadManager.getInstance(ParametroGem.class)
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_PARAMETRO), parametroGem.getId().getParametro()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_NIVEL), NivelParametroGem.CBO.getNivel()))
                                        .start().getList();

                                if (isParametroDiasMaximoCancelamentoAgendamento) {
                                    lstTipoProcedimentos = LoadManager.getInstance(ParametroGem.class)
                                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_PARAMETRO), parametroGem.getId().getParametro()))
                                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ParametroGem.PROP_ID, ParametroGemPK.PROP_NIVEL), NivelParametroGem.TIPO_PROCEDIMENTO.getNivel()))
                                            .start().getList();
                                }
                            }
                            if (CollectionUtils.isNotNullEmpty(lstEmpresas)) {
                                setParamInfo(parametroGem, lstEmpresas);
                            }
                            if (CollectionUtils.isNotNullEmpty(lstUsuarios)) {
                                setParamInfo(parametroGem, lstUsuarios);
                            }
                            if (CollectionUtils.isNotNullEmpty(lstCbos)) {
                                setParamInfo(parametroGem, lstCbos);
                            }
                            if (isParametroDiasMaximoCancelamentoAgendamento) {
                                if (CollectionUtils.isNotNullEmpty(lstTipoProcedimentos)) {
                                    setParamInfo(parametroGem, lstTipoProcedimentos);
                                }
                            }

                            dto = new NiveisParametroGemDTO(parametroGem, lstEmpresas, lstUsuarios, lstCbos, lstTipoProcedimentos);

                            addModal(art, dlgTabbedNiveis = new DlgTabbedNiveis(newModalId(), dto) {
                                @Override
                                public void ok(AjaxRequestTarget target, NiveisParametroGemDTO object) throws DAOException, ValidacaoException {
                                    mapParametros.put(object.getParametroGem(), object);
                                }
                            });
                            dlgTabbedNiveis.show(art);
                        }
                    });
                }
            }
        });
        result.add(new ValorColumn(Model.of("Valor")));

        return result;
    }

    private void setParamInfo(ParametroGem parametroGem, List<ParametroGem> parametroGems) {
        for (ParametroGem parametro : parametroGems) {
            parametro.setType(parametroGem.getType());
            parametro.setCheckValues(parametroGem.getCheckValues());
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("parametroGem");
    }
}
