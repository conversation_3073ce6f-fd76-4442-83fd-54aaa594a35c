package br.com.celk.view.vigilancia.atividadeestabelecimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.atividadeestabelecimento.customize.CustomizeConsultaAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AtividadesEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaAtividadeEstabelecimentoPage extends ConsultaPage<AtividadeEstabelecimento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private DropDown<GrupoEstabelecimento> dropDownGrupo;
    private GrupoEstabelecimento grupoEstabelecimento;
    private SetorVigilancia setor;

    public ConsultaAtividadeEstabelecimentoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(getDropDownGrupo());
        form.add(new AutoCompleteConsultaSetorVigilancia("setor"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(AtividadeEstabelecimento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(AtividadeEstabelecimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupoEstabelecimento"), VOUtils.montarPath(AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO, GrupoEstabelecimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("setor"), VOUtils.montarPath(AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA, SetorVigilancia.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn<AtividadeEstabelecimento> getCustomColumn() {
        return new CustomColumn<AtividadeEstabelecimento>() {

            @Override
            public Component getComponent(String componentId, AtividadeEstabelecimento rowObject) {
                return new CrudActionsColumnPanel<AtividadeEstabelecimento>(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        AtividadesEstabelecimentoDTO atividadesEstabelecimentoDTO = new AtividadesEstabelecimentoDTO();
                        atividadesEstabelecimentoDTO.setAtividadeEstabelecimento(getObject());
                        setResponsePage(new CadastroAtividadeEstabelecimentoPage(atividadesEstabelecimentoDTO, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).deletarAtividadeEstabelecimento(getObject());
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        AtividadesEstabelecimentoDTO atividadesEstabelecimentoDTO = new AtividadesEstabelecimentoDTO();
                        atividadesEstabelecimentoDTO.setAtividadeEstabelecimento(getObject());
                        setResponsePage(new CadastroAtividadeEstabelecimentoPage(atividadesEstabelecimentoDTO, true));
                    }
                };
            }
        };
    }

    private DropDown<GrupoEstabelecimento> getDropDownGrupo() {
        if (this.dropDownGrupo == null) {
            this.dropDownGrupo = new DropDown<GrupoEstabelecimento>("grupoEstabelecimento");

            List<GrupoEstabelecimento> grupos = LoadManager.getInstance(GrupoEstabelecimento.class)
                    .addProperty(VOUtils.montarPath(GrupoEstabelecimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoEstabelecimento.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoEstabelecimento.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupo.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoEstabelecimento grupoEstabelecimento : grupos) {
                    dropDownGrupo.addChoice(grupoEstabelecimento, grupoEstabelecimento.getDescricao());
                }
            }
        }
        return this.dropDownGrupo;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAtividadeEstabelecimento()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtividadeEstabelecimento.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeEstabelecimento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        if (grupoEstabelecimento != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO, GrupoEstabelecimento.PROP_DESCRICAO), grupoEstabelecimento.getDescricao()));
        }
        if (setor != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA, SetorVigilancia.PROP_DESCRICAO), setor.getDescricao()));
        }
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAtividadeEstabelecimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAtividadeEstabelecimento");
    }
}