package br.com.celk.view.vigilancia.eventos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEventosVigilanciaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EventosVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaEventosVigilanciaPage extends ConsultaPage<EventosVigilancia, ConsultaEventosVigilanciaDTOParam> {

    private CompoundPropertyModel<ConsultaEventosVigilanciaDTOParam> model;
    private PnlDatePeriod datePeriod;

    public ConsultaEventosVigilanciaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(model = new CompoundPropertyModel(new ConsultaEventosVigilanciaDTOParam()));
        ConsultaEventosVigilanciaDTOParam proxy = on(ConsultaEventosVigilanciaDTOParam.class);

        form.add(new InputField<String>(path(proxy.getNome())));
        form.add(new InputField<String>(path(proxy.getTipo())));
        form.add(new InputField<String>(path(proxy.getLocal())));
        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(datePeriod = new PnlDatePeriod(path(proxy.getPeriodo())));
//        datePeriod.getModel().setObject(new DatePeriod(DataUtil.getDataAtual(), null));

        setExibeExpandir(true);

        // Permissão invertida para esconder o botão
        if(isActionPermitted(Permissions.CADASTRAR, true)) {
            getLinkNovo().setVisible(false);
        } else {
            getLinkNovo().setVisible(true);
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(EventosVigilancia.class);
        EventosVigilancia proxy = on(EventosVigilancia.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(bundle("nomeEvento"), path(proxy.getNome())));
        columns.add(columnFactory.createSortableColumn(bundle("tipoEvento"), path(proxy.getDescricaoTipo())));
        columns.add(columnFactory.createSortableColumn(bundle("local"), path(proxy.getLocal())));
        columns.add(columnFactory.createSortableColumn(bundle("estabelecimento"), path(proxy.getEstabelecimento().getRazaoSocial())));
        columns.add(columnFactory.createColumn(bundle("periodo"), path(proxy.getDescricaoPeriodo())));
        columns.add(columnFactory.createColumn(bundle("situacao"), path(proxy.getSituacaoFormatada())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EventosVigilancia>() {
            @Override
            public void customizeColumn(final EventosVigilancia rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EventosVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, EventosVigilancia modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEventosVigilanciaPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EventosVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, EventosVigilancia modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<EventosVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, EventosVigilancia modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEventosVigilanciaPage(rowObject, true));
                    }
                });
            }
        };
    }
    
    @Override
    public IPagerProvider<EventosVigilancia, ConsultaEventosVigilanciaDTOParam> getPagerProviderInstance() {
        return new QueryPagerProvider<EventosVigilancia, ConsultaEventosVigilanciaDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaEventosVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarEventosVigilancia(dataPaging);
            }
            
            @Override
            public void customizeParam(ConsultaEventosVigilanciaDTOParam param) {
                ConsultaEventosVigilanciaPage.this.model.getObject().setSortProp(getSort().getProperty());
                ConsultaEventosVigilanciaPage.this.model.getObject().setAscending(getSort().isAscending());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("eventosVigilancia.nome", true);
            }
        };
    }
    
    @Override
    public ConsultaEventosVigilanciaDTOParam getParameters() {
        return model.getObject();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEventosVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEventos");
    }
}
