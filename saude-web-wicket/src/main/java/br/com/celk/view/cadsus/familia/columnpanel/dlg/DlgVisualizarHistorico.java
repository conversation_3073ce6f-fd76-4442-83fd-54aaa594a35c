package br.com.celk.view.cadsus.familia.columnpanel.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgVisualizarHistorico extends Window {

    private PnlVisualizarHistorico pnlVisualizarHistorico;

    public DlgVisualizarHistorico(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(getPnlComponentesFamilia());

        setTitle(BundleManager.getString("historicoFamilia"));

        setInitialWidth(950);
        setInitialHeight(350);
        setResizable(false);
    }

    private Component getPnlComponentesFamilia() {
        if (this.pnlVisualizarHistorico == null) {
            this.pnlVisualizarHistorico = new PnlVisualizarHistorico(getContentId()) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    close(target);
                }
            };
        }
        
        return this.pnlVisualizarHistorico;
    }

    public void show(AjaxRequestTarget target, EnderecoDomicilio enderecoDomicilio) {
        pnlVisualizarHistorico.setEnderecoDomicilio(target, enderecoDomicilio);
        show(target);
    }

}
