package br.com.celk.view.vigilancia.taxas;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 */
@Private
public class CadastroTaxasPage extends BasePage {

    private Form<Taxa> form;
    private InputField txtDescricao;
    private WebMarkupContainer containerIndices;
    private CompoundPropertyModel<TaxaIndice> modelIndices;
    private Table tblIndices;
    private TaxaIndice indiceEdicao;
    private List<TaxaIndice> taxaIndiceList;
    private Taxa taxa;
    private boolean editarDescricao;
    private boolean editarIndices;
    private DateChooser dataInicio;
    private DoubleField valorIndice;

    public CadastroTaxasPage(Taxa object, boolean editarDescricao, boolean editarIndices) {
        this.taxa = object;
        this.editarIndices = editarIndices;
        this.editarDescricao = editarDescricao;
        init();
    }

    public CadastroTaxasPage() {
        this.editarDescricao = true;
        init();
    }

    private void init(){
        carregarIndices();
        getForm().add(txtDescricao = new RequiredInputField<String>(Taxa.PROP_DESCRICAO));
        txtDescricao.setEnabled(editarDescricao);

        getForm().add(containerIndices = new WebMarkupContainer("containerIndices", modelIndices = new CompoundPropertyModel(new TaxaIndice())));
        containerIndices.setVisible(editarIndices);
        containerIndices.setOutputMarkupId(true);

        TaxaIndice proxyIndices = on(TaxaIndice.class);
        containerIndices.add(dataInicio = new DateChooser(path(proxyIndices.getDataInicioVigencia())));
        containerIndices.add(valorIndice = new DoubleField(path(proxyIndices.getValorIndice())));
        dataInicio.addAjaxUpdateValue();
        dataInicio.addRequiredClass();
        valorIndice.setLabel(Model.of(bundle("valor")));
        valorIndice.setMDec(4).addAjaxUpdateValue();
        valorIndice.addRequiredClass();
        containerIndices.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        containerIndices.add(tblIndices = new Table("tblIndices", getColumns(), getCollectionProvider()));
        tblIndices.populate();

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(getForm());
    }

    private Form<Taxa> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(taxa == null ? (taxa = new Taxa()) : taxa));
        }
        return this.form;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        TaxaIndice proxyIndices = on(TaxaIndice.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("dataInicioVigencia"), proxyIndices.getDataInicioVigencia()));
        columns.add(new DoubleColumn(bundle("valor"), path(proxyIndices.getValorIndice())).setCasasDecimais(4));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TaxaIndice>() {

            @Override
            public void customizeColumn(final TaxaIndice rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TaxaIndice>() {
                    @Override
                    public void action(AjaxRequestTarget target, TaxaIndice modelObject) throws ValidacaoException, DAOException {
                        editarIndice(target, modelObject);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TaxaIndice>() {
                    @Override
                    public void action(AjaxRequestTarget target, TaxaIndice modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target,tblIndices,taxaIndiceList,modelObject);
                    }
                });
            }
        };
    }

    private void editarIndice(AjaxRequestTarget target, TaxaIndice modelObject) {
        limpar(target);
        indiceEdicao = modelObject;
        modelIndices.setObject((TaxaIndice) SerializationUtils.clone(modelObject));
        target.add(containerIndices);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        TaxaIndice indice = modelIndices.getObject();

        validarAdd(indice);

        Integer idx = null;
        for (int i = 0; i < taxaIndiceList.size(); i++) {
            TaxaIndice item = taxaIndiceList.get(i);
            if (indiceEdicao != null && indiceEdicao == item) {
                idx = i;
            } else if (indice.getDataInicioVigencia().compareTo(item.getDataInicioVigencia()) == 0) {
                throw new ValidacaoException(BundleManager.getString("jaExisteIndiceMesmaDataInicio"));
            }
        }

        if (indiceEdicao != null && idx != null) {
            taxaIndiceList.remove(idx.intValue());
            taxaIndiceList.add(idx, indice);
        } else {
            taxaIndiceList.add(0, indice);
        }

        tblIndices.populate();
        tblIndices.update(target);

        modelIndices.setObject(new TaxaIndice());
        limpar(target);
        target.add(containerIndices);
        target.focusComponent(dataInicio);
        indiceEdicao = null;
    }

    private void validarAdd(TaxaIndice indice) throws ValidacaoException {
        if (indice.getDataInicioVigencia() == null) {
            throw new ValidacaoException(bundle("msgInformeDataInicioVigencia"));
        }
        if (indice.getValorIndice() == null) {
            throw new ValidacaoException(bundle("msgInformeValorIndice"));
        }
    }

    private void limpar(AjaxRequestTarget target) {
        modelIndices.setObject(new TaxaIndice());
        target.add(containerIndices);
        indiceEdicao = null;
        dataInicio.limpar(target);
        valorIndice.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return taxaIndiceList;
            }
        };
    }

    private void carregarIndices() {
        taxaIndiceList = new ArrayList();
        if (editarIndices && taxa != null) {
            taxaIndiceList = LoadManager.getInstance(TaxaIndice.class)
                    .addProperties(new HQLProperties(TaxaIndice.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(TaxaIndice.PROP_TAXA, taxa))
                    .start().getList();
        }
    }

    private void salvar() throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarCadastroTaxa(getForm().getModel().getObject(), taxaIndiceList, editarIndices);

        ConsultaTaxaPage page = new ConsultaTaxaPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTaxa");
    }

}