package br.com.celk.view.hospital.aih;

import br.com.celk.bo.aih.interfaces.facade.AIHFacade;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroAihAutorizadaPageStep1 extends BasePage {

    private CompoundPropertyModel<AutorizacaoInternacaoHospitalarDTO> model;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;

    public CadastroAihAutorizadaPageStep1() {
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new AutorizacaoInternacaoHospitalarDTO()));

        AutorizacaoInternacaoHospitalarDTO proxy = on(AutorizacaoInternacaoHospitalarDTO.class);

        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus())));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                verificaAtendimentoSus((AutorizacaoInternacaoHospitalarDTO) form.getModel().getObject());
            }
        }));
        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroAihAutorizada");
    }

    private void verificaAtendimentoSus(AutorizacaoInternacaoHospitalarDTO dto) throws DAOException, ValidacaoException {

        if (dto.getAutorizacaoInternacaoHospitalar() == null || dto.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus() == null) {
            throw new ValidacaoException(BundleManager.getString("informeUmPaciente"));
        }

        List<ContaPaciente> contaPacienteList = (List<ContaPaciente>) BOFactory.getBO(AIHFacade.class).consultarContaPacienteCadastroAih(dto);

        if (contaPacienteList.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("pacienteNaoPossuiContaSusAberto"));
        }

        if (contaPacienteList.size() > 1) {
            dto.setContaPacienteList(contaPacienteList);
            setResponsePage(new SelecionaAtendimentoAih(dto));
        } else {
            model.getObject().getAutorizacaoInternacaoHospitalar().setContaPaciente(contaPacienteList.get(0));
            model.getObject().getAutorizacaoInternacaoHospitalar().setAtendimento(contaPacienteList.get(0).getAtendimentoInformacao().getAtendimentoPrincipal());
            if (model.getObject().getAutorizacaoInternacaoHospitalar().getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getProfissionalResponsavel() != null) {
                if (model.getObject().getAutorizacaoInternacaoHospitalar().getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getProfissionalResponsavel().getCodigoCns() == null) {
                    throw new ValidacaoException(bundle("msgProfissionalResponsavelSemCns", model.getObject().getAutorizacaoInternacaoHospitalar().getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getProfissionalResponsavel().getNome()));
                }
            }
            setResponsePage(new ManutencaoAihAutorizadaPage(dto, false));
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField();
    }
}
