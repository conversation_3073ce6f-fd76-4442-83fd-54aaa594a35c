package br.com.celk.view.vigilancia.externo.view.estabelecimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dialog.DlgInformacoesArea;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaVigilanciaPage;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.sort.ISortState;
import org.apache.wicket.extensions.markup.html.repeater.data.sort.SortOrder;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaEstabelecimentoExternoPage extends ConsultaVigilanciaPage<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> {

    private CompoundPropertyModel<ConsultaEstabelecimentoDTOParam> model;
    private InputField<String> txtRazaoSocial;
    private DlgInformacoesArea dlgInformacoesArea;
    private AbstractAjaxButton btnNovo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(model = new CompoundPropertyModel(new ConsultaEstabelecimentoDTOParam()));
        ConsultaEstabelecimentoDTOParam proxy = on(ConsultaEstabelecimentoDTOParam.class);

        form.add(txtRazaoSocial = new InputField<>(path(proxy.getRazaoSocial())));
        form.add(new InputField(path(proxy.getFantasia())));
        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividadeEstabelecimento())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), ConsultaEstabelecimentoDTOParam.Situacao.values(), true, "Todos"));
        setExibeExpandir(true);
        setExibeBtnVoltar(true);

        // Permissão invertida para esconder o botão
        if(isActionPermitted(Permissions.CADASTRAR)) {
            getLinkNovo().setVisible(false);
        } else {
            getLinkNovo().setVisible(true);
        }

        model.getObject().setSituacao(null);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ConsultaEstabelecimentoDTO proxy = on(ConsultaEstabelecimentoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("alvara"), proxy.getEstabelecimento().getAlvara(), proxy.getEstabelecimento().getAlvaraFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("razaoSocial"), proxy.getEstabelecimento().getRazaoSocial()));
        columns.add(createSortableColumn(BundleManager.getString("fantasia"), proxy.getEstabelecimento().getFantasia()));
        columns.add(createColumn(BundleManager.getString("cpfCnpj"), proxy.getCnpjCpf()));
        columns.add(createColumn(VigilanciaHelper.isGestaoAtividadeCnae() ? BundleManager.getString("cnae") : BundleManager.getString("atividade"), proxy.getDescricaoAtividadePrincipal()));
        columns.add(createSortableColumn(BundleManager.getString("endereco"), proxy.getEstabelecimento().getVigilanciaEndereco().getLogradouro(), proxy.getEstabelecimento().getVigilanciaEndereco().getEnderecoFormatadoComCidade()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getEstabelecimento().getSituacao(), proxy.getEstabelecimento().getDescricaoSituacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ConsultaEstabelecimentoDTO>() {
            @Override
            public void customizeColumn(final ConsultaEstabelecimentoDTO rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEstabelecimentoExternoPage(rowObject.getEstabelecimento(), null, false, null));
                    }
                }).setEnabled(Estabelecimento.Situacao.NAO_AUTORIZADO.value().equals(rowObject.getEstabelecimento().getSituacao()));
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject.getEstabelecimento());
                        getPageableTable().populate(target);
                    }
                }).setEnabled(false);

                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEstabelecimentoExternoPage(rowObject.getEstabelecimento(), true, null, false, null));
                    }
                });
                addAction(ActionType.WARN, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if (dlgInformacoesArea == null) {
                            dlgInformacoesArea = new DlgInformacoesArea(newModalId(), BundleManager.getString("motivoNaoAutorizado")) {
                            };
                        }
                        addModal(target, dlgInformacoesArea);
                        dlgInformacoesArea.setConteudo(rowObject.getEstabelecimento().getMotivoNaoAutorizado());
                        dlgInformacoesArea.show(target);
                    }
                }).setVisible(Estabelecimento.Situacao.NAO_AUTORIZADO.value().equals(rowObject.getEstabelecimento().getSituacao()));

                addAction(ActionType.ENVIAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.getEstabelecimento().setSituacao(Estabelecimento.Situacao.PROVISORIO.value());
                        BOFactoryWicket.getBO(CadastroFacade.class).save(rowObject.getEstabelecimento());
                        getPageableTable().populate(target);
                    }
                }).setQuestionDialogBundleKey("desejaEnviarNovamenteParaAnalise")
                        .setVisible(Estabelecimento.Situacao.NAO_AUTORIZADO.value().equals(rowObject.getEstabelecimento().getSituacao()));


            }
        };
    }

    @Override
    public IPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam> getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaEstabelecimentoDTO, ConsultaEstabelecimentoDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<ConsultaEstabelecimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarEstabelecimentoVigilanciaExterno(dataPaging);
            }

            @Override
            public void customizeParam(ConsultaEstabelecimentoDTOParam param) {
                ConsultaEstabelecimentoExternoPage.this.model.getObject().setSortProp(getSort().getProperty());
                ConsultaEstabelecimentoExternoPage.this.model.getObject().setAscending(getSort().isAscending());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("estabelecimento.razaoSocial", true);
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance2() {
        return new IPagerProvider() {
            @Override
            public Object getCustomizeVO(Serializable object) {
                return null;
            }

            @Override
            public Object loadVO(String idReferenceProperty, Serializable id) throws DAOException, ValidacaoException {
                return null;
            }

            @Override
            public List search(String searchCriteria, int maxResults) throws DAOException, ValidacaoException {
                return null;
            }

            @Override
            public List getList(int first, int count) throws DAOException, ValidacaoException {
                return null;
            }

            @Override
            public void setParameters(Object param) {

            }

            @Override
            public ISortState getSortState() {
                return null;
            }

            @Override
            public Iterator iterator(long l, long l1) {
                return null;
            }

            @Override
            public long size() {
                return 0;
            }

            @Override
            public IModel model(Object o) {
                return null;
            }

            @Override
            public void detach() {

            }
        };
    }

    @Override
    public ConsultaEstabelecimentoDTOParam getParameters() {
        return model.getObject();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEstabelecimentoExternoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEstabelecimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtRazaoSocial;
    }
}
