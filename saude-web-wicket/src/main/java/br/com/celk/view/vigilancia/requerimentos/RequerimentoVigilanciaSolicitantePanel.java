package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class RequerimentoVigilanciaSolicitantePanel extends Panel {

    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean camposObrigatorios;

    public RequerimentoVigilanciaSolicitantePanel(String id, RequerimentoVigilancia requerimentoVigilancia, boolean enable) {
        super(id);
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.camposObrigatorios = true;
        init(enable);
    }

    public RequerimentoVigilanciaSolicitantePanel(String id, RequerimentoVigilancia requerimentoVigilancia, boolean enable, boolean camposObrigatorios) {
        super(id);
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.camposObrigatorios = camposObrigatorios;
        init(enable);
    }

    private void init(boolean enable) {

        RequerimentoVigilancia proxy = on(RequerimentoVigilancia.class);
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(requerimentoVigilancia));

        MultiLineLabel messageLabel = new MultiLineLabel("messageLabel", new PropertyModel(this, "messageLabel"));
        messageLabel.setDefaultModel(new Model<>(bundle("msgInformeCpfEOuRg")));
        messageLabel.setVisible(enable);
        root.add(messageLabel);

        InputField<String> inputNomeSolicitante = new InputField<>(path(proxy.getNomeSolicitante()));
        inputNomeSolicitante.setLabel((new Model(bundle("nomeSolicitante"))));

        UpperField inputCpfSolicitante = new UpperField(path(proxy.getCpfSolicitante()));



        UpperField inputRgSolicitante = new UpperField(path(proxy.getRgSolicitante()));

        InputField inputEmailSolicitante = new InputField(path(proxy.getEmailSolicitante()));
        inputEmailSolicitante.setLabel((new Model(bundle("email"))));


        inputNomeSolicitante.setRequired(camposObrigatorios);
        inputCpfSolicitante.setRequired(camposObrigatorios);
        inputEmailSolicitante.setRequired(camposObrigatorios);

        if(camposObrigatorios){
            inputNomeSolicitante.addRequiredClass();
            inputCpfSolicitante.addRequiredClass();
            inputEmailSolicitante.addRequiredClass();
        }

        root.add(inputNomeSolicitante);
        root.add(inputCpfSolicitante);
        root.add(inputRgSolicitante);
        root.add(inputEmailSolicitante);
        root.add(new InputField(path(proxy.getCargoSolicitante())));
        root.add(new InputField(path(proxy.getTelefoneSolicitante())));
        root.add(new InputField(path(proxy.getCelularSolicitante())));

        root.setEnabled(enable);
        add(root);

    }

}
