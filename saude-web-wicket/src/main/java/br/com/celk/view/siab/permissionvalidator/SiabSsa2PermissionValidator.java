package br.com.celk.view.siab.permissionvalidator;

import br.com.celk.system.authorization.interfaces.IPermissionValidator;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.siab.columnpanel.SiabSsa2ColumnPanel;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.Component;
import org.apache.wicket.authorization.Action;

/**
 *
 * <AUTHOR>
 */
public class SiabSsa2PermissionValidator implements IPermissionValidator{

    @Override
    public boolean validarSemPermissao(Component component, Action action) {
        Profissional profissional = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getProfissional();
        if (profissional==null) {
            return false;
        }
        SiabSsa2ColumnPanel findParent = component.findParent(SiabSsa2ColumnPanel.class);
        if (findParent!=null) {
            if (findParent.getSiabSsa2().getEquipeProfissional().getProfissional().equals(profissional)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean validarComPermissao(Component component, Action action) {
        return true;
    }

}
