package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioDispensacaoRelacaoFaltasDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDispensacaoRelacaoFaltasPage extends RelatorioPage<RelatorioDispensacaoRelacaoFaltasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        final RelatorioDispensacaoRelacaoFaltasDTOParam proxy = on(RelatorioDispensacaoRelacaoFaltasDTOParam.class);
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa())));
        autoCompleteConsultaEmpresa.setOperadorValor(true).setMultiplaSelecao(true);
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente())));
        form.add(new RequiredPnlChoicePeriod((path(proxy.getPeriodo()))));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoRelatorio());
        form.add(getDropOrdenacao());
        form.add(getDropTipoOrdenacao());
    }

    @Override
    public Class<RelatorioDispensacaoRelacaoFaltasDTOParam> getDTOParamClass() {
        return RelatorioDispensacaoRelacaoFaltasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDispensacaoRelacaoFaltasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioRelacaoFaltasDispensacao(param);
    }

    @Override
    public String getTituloPrograma() {

        return BundleManager.getString("relatorioRelacaoFaltas");
    }

    private DropDown<String> getDropDownFormaApresentacao() {

        DropDown<String> dropDownFormaApresentacao = DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioDispensacaoRelacaoFaltasDTOParam.FormaApresentacao.values());
        return dropDownFormaApresentacao;
    }

    private DropDown<String> getDropDownTipoRelatorio() {

        DropDown<String> dropDownFormaApresentacao = DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioDispensacaoRelacaoFaltasDTOParam.TipoRelatorio.values());
        return dropDownFormaApresentacao;
    }

    private DropDown<String> getDropOrdenacao() {

        DropDown<String> dropDownFormaApresentacao = DropDownUtil.getEnumDropDown("ordenacao", RelatorioDispensacaoRelacaoFaltasDTOParam.Ordenacao.values());
        return dropDownFormaApresentacao;
    }

    private DropDown<String> getDropTipoOrdenacao() {

        DropDown<String> dropDownFormaApresentacao = DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioDispensacaoRelacaoFaltasDTOParam.TipoOrdenacao.values());
        return dropDownFormaApresentacao;
    }

}
