package br.com.celk.view.consorcio.consorcioprocedimento.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.consorcio.consorcioprocedimento.customize.CustomizeConsultaConsorcioProcedimento;
import br.com.celk.view.consorcio.consorcioprocedimento.pnl.restricaocontainer.RestricaoContainerConsorcioProcedimento;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaConsorcioProcedimento extends PnlConsulta<ConsorcioProcedimento> {

    public PnlConsultaConsorcioProcedimento(String id) {
        super(id);
    }

    public PnlConsultaConsorcioProcedimento(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaConsorcioProcedimento(String id, IModel<ConsorcioProcedimento> model) {
        super(id, model);
    }

    public PnlConsultaConsorcioProcedimento(String id, IModel<ConsorcioProcedimento> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public int getMinDialogHeight() {
        return 600;
    }

    @Override
    public int getMinDialogWidth() {
        return 900;
    }
    
    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {
            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(ConsorcioProcedimento.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(ConsorcioProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(ConsorcioProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerConsorcioProcedimento(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new CustomizeConsultaPagerProvider(new CustomizeConsultaConsorcioProcedimento());
            }

            @Override
            public Class getReferenceClass() {
                return ConsorcioProcedimento.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("procedimentos");
    }

}
