package br.com.celk.view.unidadesaude.bpa.processo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoDetalhe;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoDetalheCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoDetalhePK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroPK;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroLancamentoBpaConsolidadoPage extends CadastroPage<LancamentoBpaConsolidado> {

    private Form<LancamentoBpaConsolidadoItem> formItem;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    private InputField txtCompetencia;
    private InputField txtQuantidade;
    private InputField txtIdade;
    private Table<LancamentoBpaConsolidadoItem> table;
    private List<LancamentoBpaConsolidadoItem> lancamentoBpaConsolidadoItemList = new ArrayList<LancamentoBpaConsolidadoItem>();
    private Procedimento procedimentoEdicao;
    private WebMarkupContainer containerIdade;

    public CadastroLancamentoBpaConsolidadoPage() {
    }

    public CadastroLancamentoBpaConsolidadoPage(LancamentoBpaConsolidado object) {
        this(object, false);
    }

    public CadastroLancamentoBpaConsolidadoPage(LancamentoBpaConsolidado object, boolean viewOnly) {
        super(object, viewOnly);
        carregarItens();
    }

    @Override
    public void init(Form form) {
        LancamentoBpaConsolidado proxy = on(LancamentoBpaConsolidado.class);

        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true)
                .setLabel(new Model<String>(bundle("estabelecimento"))));
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA, ConsultaLancamentoBpaConsolidadoPage.class);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProfissional.limpar(target);
                autoCompleteConsultaProfissional.setCodigoEmpresa(empresa.getCodigo());
                autoCompleteConsultaProfissional.setEnabled(true);
                target.add(autoCompleteConsultaProfissional);
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProfissional.setEnabled(false);
                autoCompleteConsultaProfissional.limpar(target);
                autoCompleteConsultaTabelaCbo.setEnabled(false);
                autoCompleteConsultaTabelaCbo.limpar(target);
            }
        });

        form.add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true)
                .setLabel(new Model<String>(bundle("profissional"))));
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                autoCompleteConsultaTabelaCbo.limpar(target);
                autoCompleteConsultaTabelaCbo.setProfissionalUnidade(profissional, (Empresa) autoCompleteConsultaEmpresa.getComponentValue());
                autoCompleteConsultaTabelaCbo.setEnabled(true);
                target.add(autoCompleteConsultaTabelaCbo);
            }
        });
        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional profissional) {
                autoCompleteConsultaTabelaCbo.setEnabled(false);
                autoCompleteConsultaTabelaCbo.limpar(target);
            }
        });
        form.add(autoCompleteConsultaTabelaCbo = (AutoCompleteConsultaTabelaCbo) new AutoCompleteConsultaTabelaCbo(path(proxy.getTabelaCbo()), true)
                .setLabel(new Model<String>(bundle("cbo"))));
        autoCompleteConsultaTabelaCbo.setFiltrarAtivos(true);
        form.add(txtCompetencia = new MesAnoField(path(proxy.getCompetencia())));
        txtCompetencia.addRequiredClass();
        txtCompetencia.setRequired(true);

        formItem = new Form("formItem", new CompoundPropertyModel<LancamentoBpaConsolidadoItem>(new LancamentoBpaConsolidadoItem()));
        formItem.add(autoCompleteConsultaProcedimento = (AutoCompleteConsultaProcedimento) new AutoCompleteConsultaProcedimento(LancamentoBpaConsolidadoItem.PROP_PROCEDIMENTO)
                .setLabel(new Model<String>(bundle("procedimento"))));
        formItem.add(txtQuantidade = new InputField(LancamentoBpaConsolidadoItem.PROP_QUANTIDADE));

        formItem.add(table = new Table("table", getColumns(), getCollectionProvider()));
        formItem.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                LancamentoBpaConsolidadoItem item = formItem.getModelObject();
                adicionar(target, item);
            }
        });
        table.populate();

        containerIdade = new WebMarkupContainer("containerIdade");
        txtIdade = new InputField(LancamentoBpaConsolidadoItem.PROP_IDADE);
        containerIdade.add(txtIdade);
        containerIdade.setVisible(false);
        containerIdade.setOutputMarkupId(true);
        containerIdade.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaProcedimento.add(new ConsultaListener<Procedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Procedimento object) {
                if (exigeIdade(object)) {
                    containerIdade.setVisible(true);
                    txtIdade.setRequired(true);
                    txtIdade.addRequiredClass();
                } else {
                    containerIdade.setVisible(false);
                    txtIdade.setRequired(false);
                    txtIdade.removeRequiredClass();
                }
                target.add(containerIdade);
            }
        });
        autoCompleteConsultaProcedimento.add(new RemoveListener<Procedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Procedimento object) {
                containerIdade.setVisible(false);
                txtIdade.setRequired(false);
                txtIdade.removeRequiredClass();
                txtIdade.limpar(target);
                txtQuantidade.limpar(target);
                target.add(containerIdade);
            }
        });
        formItem.add(containerIdade);

        form.add(formItem);
        enableComponentes(getForm().getModelObject());
    }

    private void adicionar(AjaxRequestTarget target, LancamentoBpaConsolidadoItem lancamentoBpaConsolidadoItem) throws DAOException, ValidacaoException {
        validacoesAdicionar(lancamentoBpaConsolidadoItem);

        int idx = 0;
        for (int i = 0; i < lancamentoBpaConsolidadoItemList.size(); i++) {
            LancamentoBpaConsolidadoItem item = lancamentoBpaConsolidadoItemList.get(i);
            if (procedimentoEdicao != null && procedimentoEdicao == item.getProcedimento()) {
                idx = i;
            } else if (lancamentoBpaConsolidadoItem.getProcedimento().getCodigo().equals(item.getProcedimento().getCodigo())) {
                if (lancamentoBpaConsolidadoItem.getIdade() == null) {
                    throw new ValidacaoException(bundle("oProcedimentoJaEstaAdicionado"));
                }else if(lancamentoBpaConsolidadoItem.getIdade().equals(item.getIdade())){
                    throw new ValidacaoException(bundle("oProcedimentoJaEstaAdicionado"));
                }
            }
        }

        if (procedimentoEdicao != null && CollectionUtils.isNotNullEmpty(lancamentoBpaConsolidadoItemList)) {
            lancamentoBpaConsolidadoItemList.remove(idx);
            lancamentoBpaConsolidadoItemList.add(idx, (LancamentoBpaConsolidadoItem) SerializationUtils.clone(lancamentoBpaConsolidadoItem));
        } else {
            lancamentoBpaConsolidadoItemList.add(0, (LancamentoBpaConsolidadoItem) SerializationUtils.clone(lancamentoBpaConsolidadoItem));
        }

        procedimentoEdicao = null;
        table.update(target);
        autoCompleteConsultaProcedimento.limpar(target);
        txtQuantidade.limpar(target);
        txtIdade.limpar(target);
        autoCompleteConsultaProcedimento.focus(target);
        containerIdade.setVisible(false);
        target.add(containerIdade);
    }

    private boolean exigeIdade(Procedimento procedimento) {
        ProcedimentoCompetencia procedimentoCompetencia = getProcedimentoCompetencia(procedimento);
        return LoadManager.getInstance(ProcedimentoDetalhe.class)
                .addProperty(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoDetalhe.PROP_ID, ProcedimentoDetalhePK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoDetalhe.PROP_ID, ProcedimentoDetalhePK.PROP_DETALHE, ProcedimentoDetalheCadastro.PROP_CODIGO),
                                ProcedimentoDetalheCadastro.EXIGE_IDADE))
                .exists();
    }

    private void validacoesAdicionar(LancamentoBpaConsolidadoItem item) throws ValidacaoException, DAOException {
        LancamentoBpaConsolidado lancamento = getForm().getModelObject();

        if (lancamento.getEmpresa() == null) {
            throw new ValidacaoException(bundle("informeEstabelecimento"));
        } else if (lancamento.getProfissional() == null) {
            throw new ValidacaoException(bundle("informeProfissional"));
        } else if (lancamento.getTabelaCbo() == null) {
            throw new ValidacaoException(bundle("informeCbo"));
        } else if (item.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        } else if (Coalesce.asLong(item.getQuantidade()) == 0L) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        }

        if(AtendimentoHelper.isProcedimentoContainsCBO(item.getProcedimento())){
            AtendimentoHelper.validarCboProfissionalProcedimento(lancamento.getTabelaCbo(), lancamento.getEmpresa(),
                    lancamento.getProfissional(), item.getProcedimento());
        }

        ProcedimentoCompetencia procedimentoCompetencia = getProcedimentoCompetencia(item.getProcedimento());
        if (procedimentoCompetencia == null) {
            throw new ValidacaoException(bundle("msgProcedimentoInvalidoCompetenciaAtual"));
        }

        boolean procedimentoConsolidado = LoadManager.getInstance(ProcedimentoRegistro.class)
                .addProperty(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_REGISTRO_CADASTRO, ProcedimentoRegistroCadastro.PROP_CODIGO),
                                ProcedimentoRegistroCadastro.BPA_CONSOLIDADO))
                .exists();

        if (!procedimentoConsolidado) {
            throw new ValidacaoException(bundle("msgProcedimentoInformadoDeveSerConsolidado"));
        }
    }

    private ProcedimentoCompetencia getProcedimentoCompetencia(Procedimento procedimento) {
        return LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addProperty(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), procedimento))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento()))
                .start().getVO();
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        LancamentoBpaConsolidadoItem proxy = on(LancamentoBpaConsolidadoItem.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimento().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(createColumn(bundle("idade"), proxy.getIdade()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LancamentoBpaConsolidadoItem>() {
            @Override
            public void customizeColumn(LancamentoBpaConsolidadoItem rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LancamentoBpaConsolidadoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, LancamentoBpaConsolidadoItem modelObject) throws ValidacaoException, DAOException {
                        editarItem(target, modelObject);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<LancamentoBpaConsolidadoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, LancamentoBpaConsolidadoItem modelObject) throws ValidacaoException, DAOException {
                        removerItem(target, modelObject);
                    }
                });
            }
        };
    }

    private void editarItem(AjaxRequestTarget target, LancamentoBpaConsolidadoItem modelObject) {
        autoCompleteConsultaProcedimento.limpar(target);
        txtQuantidade.limpar(target);
        txtIdade.limpar(target);
        Procedimento procedimento = (Procedimento) SerializationUtils.clone(modelObject.getProcedimento());
        procedimentoEdicao = modelObject.getProcedimento();

        this.formItem.getModelObject().setProcedimento(procedimento);
        this.formItem.getModelObject().setQuantidade(modelObject.getQuantidade());
        this.formItem.getModelObject().setIdade(modelObject.getIdade());
        if (exigeIdade(modelObject.getProcedimento())) {
            containerIdade.setVisible(true);
            txtIdade.setRequired(true);
            txtIdade.addRequiredClass();
        } else {
            containerIdade.setVisible(false);
            txtIdade.setRequired(false);
            txtIdade.removeRequiredClass();
        }
        target.add(containerIdade);

        target.add(autoCompleteConsultaProcedimento);
        target.add(txtQuantidade);
        target.add(txtIdade);
    }

    private void removerItem(AjaxRequestTarget target, LancamentoBpaConsolidadoItem lancamentoBpaConsolidadoItem) {
        procedimentoEdicao = null;
        for (int i = 0; i < lancamentoBpaConsolidadoItemList.size(); i++) {
            if (lancamentoBpaConsolidadoItemList.get(i).getProcedimento() == lancamentoBpaConsolidadoItem.getProcedimento()) {
                lancamentoBpaConsolidadoItemList.remove(i);
                break;
            }
        }
        table.update(target);
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lancamentoBpaConsolidadoItemList;
            }
        };
    }

    @Override
    public Class<LancamentoBpaConsolidado> getReferenceClass() {
        return LancamentoBpaConsolidado.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public Class getResponsePage() {
        return ConsultaLancamentoBpaConsolidadoPage.class;
    }

    @Override
    public Object salvar(LancamentoBpaConsolidado object) throws DAOException, ValidacaoException {
        validarRegistroJaExiste(object);
        return BOFactoryWicket.getBO(AtendimentoGeralFacade.class).salvarLancamentoBpaConsolidado(object, lancamentoBpaConsolidadoItemList);
    }

    private void validarRegistroJaExiste(LancamentoBpaConsolidado object) throws ValidacaoException {
        LoadManager load = LoadManager.getInstance(LancamentoBpaConsolidado.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidado.PROP_EMPRESA, object.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidado.PROP_PROFISSIONAL, object.getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidado.PROP_TABELA_CBO, object.getTabelaCbo()))
                .addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidado.PROP_COMPETENCIA, object.getCompetencia()));

        if (object.getCodigo() != null) {
            load.addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidado.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }

        LancamentoBpaConsolidado lancamentoBpaConsolidado = load.start().getVO();

        if (lancamentoBpaConsolidado != null) {
            throw new ValidacaoException(bundle("msgJaExisteCadastroLancamentoBpaConsolidadoEssasInformacoes"));
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLancamentoBpaConsolidado");
    }

    private void carregarItens() {
        List<LancamentoBpaConsolidadoItem> list = LoadManager.getInstance(LancamentoBpaConsolidadoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LancamentoBpaConsolidadoItem.PROP_LANCAMENTO_BPA_CONSOLIDADO, getForm().getModelObject()))
                .start().getList();
        this.lancamentoBpaConsolidadoItemList = new ArrayList<LancamentoBpaConsolidadoItem>(list);
    }

    private void enableComponentes(LancamentoBpaConsolidado object) {
        if (object != null && object.getCodigo() != null) {
            autoCompleteConsultaProfissional.setEnabled(true);
            autoCompleteConsultaProfissional.setCodigoEmpresa(object.getEmpresa().getCodigo());
            autoCompleteConsultaTabelaCbo.setEnabled(true);
            autoCompleteConsultaTabelaCbo.setProfissionalUnidade(object.getProfissional(), object.getEmpresa());
        } else {
            autoCompleteConsultaProfissional.setEnabled(false);
            autoCompleteConsultaTabelaCbo.setEnabled(false);
        }
    }
}
