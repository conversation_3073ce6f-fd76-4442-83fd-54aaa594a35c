package br.com.celk.view.siab.microarea;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.siab.equipearea.CadastroEquipeAreaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaMicroAreaPage extends ConsultaPage<EquipeArea, List<BuilderQueryCustom.QueryParameter>> {

    private DropDown<EquipeArea> dropDownArea;
    private Long microArea;
    private EquipeArea area;
    private DlgNovoMicroArea dlgNovoMicroArea;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(dropDownArea = createDropDownArea());
        form.add(new InputField<Long>("microArea"));
        setExibeExpandir(true);
    }
    
    private void salvar(EquipeMicroArea equipeMicroArea, AjaxRequestTarget target) throws DAOException, ValidacaoException{
        BOFactoryWicket.getBO(AtendimentoGeralFacade.class).cadastrarEquipeMicroArea(equipeMicroArea);
        info(target, BundleManager.getString("registro_salvo_sucesso"));
        getPageableTable().update(target);
    }

    private DropDown<EquipeArea> createDropDownArea() {
        
        DropDown<EquipeArea> dropDown = new DropDown<EquipeArea>("area");
        List<EquipeArea> list = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        return dropDown;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        EquipeMicroArea on = on(EquipeMicroArea.class);

        columns.add(getActionColumn());
        
        columns.add(createSortableColumn(bundle("area"), on.getEquipeArea().getDescricao()));
        columns.add(createSortableColumn(bundle("microArea"), on.getMicroArea()));
        columns.add(createSortableColumn(bundle("unidade"), on.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), on.getEquipeProfissional().getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("status"), on.getDescricaoSituacao()));

        return columns;
    }

    private void initDlgMicroArea(AjaxRequestTarget target) {
        dlgNovoMicroArea = new DlgNovoMicroArea(newModalId()){
            @Override
            public void onConfirmar(AjaxRequestTarget target, EquipeMicroArea equipeMicroArea) throws DAOException, ValidacaoException {
                salvar(equipeMicroArea, target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {

            }
        };
        addModal(target, dlgNovoMicroArea);
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EquipeMicroArea>() {

            @Override
            public void customizeColumn(EquipeMicroArea rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EquipeMicroArea>() {

                    @Override
                    public void action(AjaxRequestTarget target, EquipeMicroArea modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().update(target);
                    }
                });

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<EquipeMicroArea>() {

                    @Override
                    public void action(AjaxRequestTarget target, EquipeMicroArea modelObject) throws ValidacaoException, DAOException {
                        modelObject.setStatus(RepositoryComponentDefault.ATIVO);
                        BOFactory.save(modelObject);
                        getPageableTable().update(target);
                    }
                }).setVisible(RepositoryComponentDefault.INATIVO.equals(rowObject.getStatus()));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<EquipeMicroArea>() {

                    @Override
                    public void action(AjaxRequestTarget target, EquipeMicroArea modelObject) throws ValidacaoException, DAOException {
                        if (modelObject.getEquipeProfissional() != null && modelObject.getEquipeProfissional().getProfissional() != null && modelObject.getEquipeProfissional().getProfissional().getCodigo() != null) {
                            throw new ValidacaoException("Não é possível desativar uma microárea com profissional informado.");
                        }
                        modelObject.setStatus(RepositoryComponentDefault.INATIVO);
                        BOFactory.save(modelObject);
                        getPageableTable().update(target);
                    }
                }).setVisible(RepositoryComponentDefault.ATIVO.equals(rowObject.getStatus()));
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EquipeMicroArea>() {

                    @Override
                    public void action(AjaxRequestTarget target, EquipeMicroArea modelObject) throws ValidacaoException, DAOException {
                        initDlgMicroArea(target);
                        dlgNovoMicroArea.show(target, modelObject);
                    }
                });
            }
        };
    }

     @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return EquipeMicroArea.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(EquipeMicroArea.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EMPRESA, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EMPRESA, Empresa.PROP_REFERENCIA),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                            VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME),
                            });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_MICRO_AREA, microArea));
        
        if (dropDownArea.getComponentValue() != null) {
            EquipeArea ea = dropDownArea.getComponentValue();
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, ea.getCodigo()));
        }

        return parameters;
    }

    @Override
    protected Component criarLinkNovo(String id) {
        return new AbstractAjaxLink(id) {
            
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgMicroArea(target);
                dlgNovoMicroArea.show(target, null);
            }
        };
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEquipeAreaPage.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("consultaMicroArea");
    }

}