package br.com.celk.view.basico.unidade.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.basico.dto.CadastroEmpresaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 10/11/17.
 */
public class EmpresaSetorTab extends TabPanel<CadastroEmpresaDTO> {

    private Table<EmpresaSetor> tblSetores;
    private Empresa setor;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    public EmpresaSetorTab(String id, CadastroEmpresaDTO object) {
        super(id, object);
        init();
    }

    private void init() {
        add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("setor", new PropertyModel(this, "setor")));
        add(new AbstractAjaxButton("btnAdicionarSetor") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        add(tblSetores = new Table("tblSetores", getColumns(), getCollectionProvider()));
        tblSetores.populate();
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EmpresaSetor proxy = on(EmpresaSetor.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("setor"), proxy.getSetor().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EmpresaSetor>() {

            @Override
            public void customizeColumn(EmpresaSetor rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EmpresaSetor>() {

                    @Override
                    public void action(AjaxRequestTarget target, EmpresaSetor modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return Lambda.sort(object.getEmpresaSetorList(), on(EmpresaSetor.class).getSetor().getDescricao());
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if(setor == null){
            throw new ValidacaoException(BundleManager.getString("informeSetor"));
        } else if(object.getEmpresa().getCodigo() != null && object.getEmpresa().getCodigo().equals(setor.getCodigo())){
            throw new ValidacaoException(BundleManager.getString("msgSetorNaoPodeSerPropriaUnidade"));
        }

        for (EmpresaSetor empresaSetor : object.getEmpresaSetorList()) {
            if (setor.getCodigo().equals(empresaSetor.getSetor().getCodigo())) {
                throw new ValidacaoException(BundleManager.getString("setorJaAdicionado"));
            }
        }

        EmpresaSetor empresaSetor = new EmpresaSetor();

        empresaSetor.setEmpresa(object.getEmpresa());
        empresaSetor.setSetor(setor);

        object.getEmpresaSetorList().add(empresaSetor);

        autoCompleteConsultaEmpresa.limpar(target);
        target.focusComponent(autoCompleteConsultaEmpresa.getTxtDescricao().getTextField());
        tblSetores.update(target);
    }

    private void remover(AjaxRequestTarget target, EmpresaSetor empresaSetor) {
        for (int i = 0; i < object.getEmpresaSetorList().size(); i++) {
            if (object.getEmpresaSetorList().get(i) == empresaSetor) {
                object.getEmpresaSetorList().remove(i);
                break;
            }
        }
        tblSetores.update(target);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("setores");
    }
}
