package br.com.celk.view.vigilancia.requerimentovigilancia;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLancarOcorrencia extends Window{

    private PnlLancarOcorrencia pnlLancarOcorrencia;
    
    public DlgLancarOcorrencia(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        setInitialWidth(600);
        setInitialHeight(250);
        
        setResizable(false);
        
        setTitle(BundleManager.getString("lancarOcorrencia"));
        
        setContent(pnlLancarOcorrencia = new PnlLancarOcorrencia(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                limpar(target);
                close(target);
                onClose(target);
            }
        });
    }
    
    public void setModelObject(RequerimentoVigilancia requerimentoVigilancia) {
        pnlLancarOcorrencia.setRequerimentoVigilancia(requerimentoVigilancia);
    }

    public void update(AjaxRequestTarget target){
        pnlLancarOcorrencia.update(target);
    }
    
    public abstract void onClose(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}
