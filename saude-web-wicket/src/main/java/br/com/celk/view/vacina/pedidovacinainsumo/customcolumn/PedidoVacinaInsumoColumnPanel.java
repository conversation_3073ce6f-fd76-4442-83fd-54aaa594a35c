package br.com.celk.view.vacina.pedidovacinainsumo.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.ReportLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vacina.pedidovacinainsumo.CadastroPedidoVacinaPage;
import br.com.celk.view.vacina.pedidovacinainsumo.DetalhesPedidoVacinaPage;
import br.com.celk.view.vacina.pedidovacinainsumo.DlgEncaminharPedidoVacinaInsumo;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vacina.interfaces.facade.VacinaReportFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.UsuarioCadsusDTOParam;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo;
import java.util.Arrays;
import java.util.Date;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class PedidoVacinaInsumoColumnPanel extends Panel implements PermissionContainer{

    private AjaxLink btnEditar;
    
    private AjaxLink btnCancelar;
    
    private AjaxLink btnConsultar;
    
    private ReportLink btnImprimir;

    @Permission(type=Permissions.ENCAMINHAR, action=ActionsEnum.RENDER)
    private AjaxLink btnEncaminhar;
    
    private DlgConfirmacao dlgConfirmacaoCancelar;
    
    private DlgEncaminharPedidoVacinaInsumo dlgEncaminharPedidoVacina;
    
    private PedidoVacinaInsumo pedidoVacinaInsumo;
    
    public PedidoVacinaInsumoColumnPanel(String id, PedidoVacinaInsumo pedidoVacinaInsumo) {
        super(id);
        this.pedidoVacinaInsumo = pedidoVacinaInsumo;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroPedidoVacinaPage(pedidoVacinaInsumo));
            }

            @Override
            public boolean isEnabled() {
                return PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ABERTO.value().equals(pedidoVacinaInsumo.getStatus());
            }

        });
        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacaoCancelar!=null) {
                    dlgConfirmacaoCancelar.show(target);
                }
                
            }
            
            @Override
            public boolean isEnabled() {
                return PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ABERTO.value().equals(pedidoVacinaInsumo.getStatus()) || PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ENCAMINHADO.value().equals(pedidoVacinaInsumo.getStatus());
            }

        });
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new DetalhesPedidoVacinaPage(pedidoVacinaInsumo));
            }

        });
        add(btnImprimir = new ReportLink("btnImprimir") {

            @Override
            public DataReport getDataReport() throws ReportException {
                return BOFactoryWicket.getBO(VacinaReportFacade.class).relatorioImpressaoPedidoVacina(pedidoVacinaInsumo.getCodigo());
            }

            @Override
            public boolean isEnabled() {
                return PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ABERTO.value().equals(pedidoVacinaInsumo.getStatus());
            }
            
        });
        
        add(btnEncaminhar = new AbstractAjaxLink("btnEncaminhar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgEncaminhar(target);
                if (dlgEncaminharPedidoVacina!=null) {
                    dlgEncaminharPedidoVacina.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return pedidoVacinaInsumo.getStatus().equals(PedidoVacinaInsumo.StatusPedidoVacinaInsumo.ABERTO.value());
            }
            
        });
        
        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
        btnImprimir.add(new AttributeModifier("title", BundleManager.getString("imprimir")));
        btnEncaminhar.add(new AttributeModifier("title", BundleManager.getString("encaminhar")));
    }
    
    private void initDlgConfirmacao(AjaxRequestTarget target){
        if (dlgConfirmacaoCancelar==null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoCancelar = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteCancelar")+"?") {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VacinaFacade.class).cancelarPedidoVacina(pedidoVacinaInsumo.getCodigo());
                    updateTable(target);
                }
            });
        }
    }
    
    private void initDlgEncaminhar(AjaxRequestTarget target){
        if (dlgEncaminharPedidoVacina==null) {
            WindowUtil.addModal(target, this, dlgEncaminharPedidoVacina = new DlgEncaminharPedidoVacinaInsumo(WindowUtil.newModalId(this), pedidoVacinaInsumo) {

                @Override
                public void onEncaminhar(AjaxRequestTarget target, Date dataEncaminhamento) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(VacinaFacade.class).encaminharPedidoVacina(pedidoVacinaInsumo.getCodigo(),dataEncaminhamento);
                    updateTable(target);
                }
            });
        }
    }
    
    private DataReport imprimirPedido(AjaxRequestTarget target) throws ReportException {
        MessageUtil.growlInfo(target, this, BundleManager.getString("relatorio_enviado_processamento"));
        return BOFactoryWicket.getBO(VacinaReportFacade.class).relatorioImpressaoPedidoVacina(pedidoVacinaInsumo.getCodigo());
    }

    public abstract void updateTable(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public AjaxLink getBtnEncaminhar() {
        return btnEncaminhar;
    }
    
}
