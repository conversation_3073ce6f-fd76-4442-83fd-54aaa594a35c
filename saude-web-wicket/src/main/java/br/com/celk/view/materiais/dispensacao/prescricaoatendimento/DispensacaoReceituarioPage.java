package br.com.celk.view.materiais.dispensacao.prescricaoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoObject;
import br.com.celk.component.dialog.DlgDisabledArea;
import br.com.celk.component.dialog.DlgImpressao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.dialog.DlgDetalhesSuspencaoPrescricaoInternaInternacao;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog.*;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.tablerow.DispensacaoTableRow;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoItemDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto.ReceituarioStatusDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.dto.QueryMovimentoGrupoEstoqueItemDTOParam;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.QueryImpressaoDispensacaoPrescricaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class DispensacaoReceituarioPage extends BasePage {

    private Form<DispensacaoMedicamento> form;
    private Table<DispensacaoMedicamentoItemDTO> tblItensReceituario;
    private Table<DispensacaoMedicamentoItemDTO> tblItensDispensar;
    private DlgImpressao dlgImpressao;
    private Receituario receituario;
    private List<DispensacaoMedicamentoItemDTO> ItensReceituario = new ArrayList<DispensacaoMedicamentoItemDTO>();
    private List<DispensacaoMedicamentoItemDTO> itensDispensar = new ArrayList<DispensacaoMedicamentoItemDTO>();
    private boolean validaPrecoUnitario = false;
    private LongField txtCodigoBarras;
    private Long codigoBarrasProduto;

    private List<MovimentoGrupoEstoqueItemDTO> lotesCarregados;
    private ReceituarioStatusDTO receituarioStatusDTO = new ReceituarioStatusDTO();

    private DlgDispensarSimples dlgDispensarSimplesAdicionar;
    private DlgDispensarSimples dlgDispensarSimplesAdicionarSemLote;
    private DlgDispensarControlaLote dlgDispensarControlaLoteAdicionar;
    private DlgDispensarSimples dlgDispensarSimplesEditar;
    private DlgDispensarSimples dlgDispensarSimplesEditarLote;
    private DlgDispensarControlaLote dlgDispensarControlaLoteEditar;
    private DlgEditaCodigoBarras dlgEditaCodigoBarras;
    private DlgEditaCodigoBarrasLote dlgEditaCodigoBarrasLote;
    private DlgSubstituicaoProdutoKit dlgSubstituicaoProdutoKit;
    private DlgConfirmacaoObject<DispensacaoMedicamentoItemDTO> dlgConfirmacaoObjectNaoUtilizar;
    private DlgConfirmacaoObject<DispensacaoMedicamentoItemDTO> dlgConfirmacaoObjectReutilizar;
    private DlgVincularProduto dlgVincularProduto;
    private Page pageVoltar;

    private DlgDisabledArea dlgJustificativaAdep;
    private DlgDisabledArea dlgJustificativa;
    private DlgDetalhesSuspencaoPrescricaoInternaInternacao dlgDetalhesSuspencaoPrescricaoInternaInternacao;

    private Empresa empresaBaixa;

    private InputField txtNumeroReceita;
    private Long numeroReceita;
    private String utilizarLeitoraCodigoBarrasProduto;
    private String validarSituacaoCodigoBarrasProduto;


    public DispensacaoReceituarioPage(Receituario receituario, Empresa empresaBaixa) {
        carregaEmpresaBaixa(empresaBaixa);
        carregaReceituario(receituario);
        init(iniciarDispensacao(receituario));
    }

    public DispensacaoReceituarioPage(Receituario receituario, Page pageVoltar, Empresa empresaBaixa) {
        this.pageVoltar = pageVoltar;
        carregaEmpresaBaixa(empresaBaixa);
        carregaReceituario(receituario);
        init(iniciarDispensacao(receituario));
    }

    private void init(DispensacaoMedicamento dispensacaoMedicamento) {
        form = new Form<DispensacaoMedicamento>("form", new CompoundPropertyModel(dispensacaoMedicamento));

        try {
            this.utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            this.validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME_SOCIAL)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_ATENDIMENTO, Atendimento.PROP_CONVENIO, Convenio.PROP_DESCRICAO)));
        WebMarkupContainer containerReceita = new WebMarkupContainer("containerReceita");
        containerReceita.add(txtNumeroReceita = new InputField("numeroReceita", new PropertyModel(this, "numeroReceita")));
        
        form.add(containerReceita);

        if (TipoReceita.RECEITA_AMARELA.equals(receituario.getTipoReceita().getTipoReceita())
                || TipoReceita.RECEITA_AZUL.equals(receituario.getTipoReceita().getTipoReceita())) {
            containerReceita.setVisible(true);
        } else {
            containerReceita.setVisible(false);
        }

        WebMarkupContainer containerItensReceituario = new WebMarkupContainer("containerItensReceituario", new CompoundPropertyModel(new DispensacaoMedicamentoItem()));
        containerItensReceituario.setOutputMarkupId(true);

        containerItensReceituario.add(txtCodigoBarras = new LongField("codigoBarrasProduto", new PropertyModel(this, "codigoBarrasProduto")));
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (codigoBarrasProduto != null) {
                    try {
                        adicionarViaCodigoBarras(art);
                    } catch (ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                        DispensacaoReceituarioPage.this.modalWarn(art, ex);
                    } catch (DAOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= 0";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtCodigoBarras.getMarkupId(), condition));
            }
        });

        containerItensReceituario.add(tblItensReceituario = new Table("tblItensReceituario", getColumnsItensReceituario(), getCollectionProviderItensReceituario()) {
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new DispensacaoTableRow(id, index, model);
            }
        });
        tblItensReceituario.populate();
        form.add(containerItensReceituario);

        WebMarkupContainer containerItensDispensar = new WebMarkupContainer("containerItensDispensar", new CompoundPropertyModel(new DispensacaoMedicamentoItem()));
        containerItensDispensar.setOutputMarkupId(true);
        containerItensDispensar.add(tblItensDispensar = new Table("tblItensDispensar", getColumnsItensDispensar(), getCollectionProviderItensDispensar()));
        tblItensDispensar.populate();
        form.add(containerItensDispensar);

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        add(form);

        addModal(dlgImpressao = new DlgImpressao(newModalId(), bundle("dispensacaoSalvaSucesso")) {
            @Override
            public DataReport onImprimir() throws ReportException {
                return imprimir();
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                confirmar(target);
            }
        });
    }

    private List<IColumn> getColumnsItensReceituario() {
        List<IColumn> columns = new ArrayList<IColumn>();

        DispensacaoMedicamentoItemDTO proxy = on(DispensacaoMedicamentoItemDTO.class);

        columns.add(getCustomColumnItensReceituario());
        columns.add(createColumn(bundle("produto"), proxy.getDescricaoProduto()));
        columns.add(createColumn(bundle("un"), proxy.getDispensacaoMedicamentoItem().getProduto().getUnidade().getUnidade()));
        columns.add(createColumn(bundle("via"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getTipoViaMedicamento().getDescricao()));
        columns.add(createColumn(bundle("posologia"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getPosologia()));
        columns.add(createColumn(bundle("saldo"), proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensar()));

        return columns;
    }

    private IColumn getCustomColumnItensReceituario() {
        return new MultipleActionCustomColumn<DispensacaoMedicamentoItemDTO>() {
            @Override
            public void customizeColumn(DispensacaoMedicamentoItemDTO rowObject) {
                Long statusReceituarioItem = rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getStatus();

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoConfirmar(target, dmi);
                    }
                }).setTitleBundleKey("dispensar")
                        .setVisible(verificaStatusNormal(rowObject) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem))
                        .setEnabled(rowObject.getDispensacaoMedicamentoItem().getProduto() != null);

                addAction(ActionType.VINCULAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoVincular(target, dmi);
                    }
                }).setVisible(RepositoryComponentDefault.NAO_LONG.equals(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getFlagPadronizado())
                        && Coalesce.asLong(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getQuantidadeDispensada()).equals(0L)
                        && Coalesce.asDouble(rowObject.getDispensacaoMedicamentoItem().getQuantidadeDispensada()).equals(0D) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        mostrarDlgJustificativaAdep(target, dmi);
                    }

                }).setTitleBundleKey("justificativaAdep")
                        .setVisible(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem() != null
                                && rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativaAdep() != null
                                && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        mostrarDlgJustificativa(target, dmi);
                    }

                }).setTitleBundleKey("justificativaNaoPadronizado")
                        .setVisible(verificaMedicamentoNaoPadronizado(rowObject.getDispensacaoMedicamentoItem()) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.SUBSTITUIR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoSubstituir(target, dmi);
                    }
                }).setVisible(RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSubstituivel()) && verificaStatusNormal(rowObject) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.NAO_UTILIZAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoNaoUtilizar(target, dmi);
                    }
                }).setVisible(verificaStatusNormal(rowObject) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.REUTILIZAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoReutilizar(target, dmi);
                    }
                }).setVisible(!verificaStatusNormal(rowObject) && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.LAUDAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        mostrarDlgJustificativaSuspencao(target, dmi);
                    }
                }).setTitleBundleKey("justificativa").setVisible(ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));
            }
        };
    }

    private void mostrarDlgJustificativaSuspencao(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgDetalhesSuspencaoPrescricaoInternaInternacao == null) {
            addModal(target, dlgDetalhesSuspencaoPrescricaoInternaInternacao = new DlgDetalhesSuspencaoPrescricaoInternaInternacao(newModalId()) {
            });
        }
        dlgDetalhesSuspencaoPrescricaoInternaInternacao.showDialog(target, dmi.getDispensacaoMedicamentoItem().getReceituarioItem());
    }

    private boolean verificaMedicamentoNaoPadronizado(DispensacaoMedicamentoItem dmi) {
        if (RepositoryComponentDefault.NAO_LONG.equals(dmi.getReceituarioItem().getFlagPadronizado())) {
            return true;
        } else {
            if (dmi.getProduto() != null) {
                return RepositoryComponentDefault.NAO.equals(dmi.getProduto().getFlagPadronizado());
            }
        }
        return false;
    }

    private void mostrarDlgJustificativa(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgJustificativa == null) {
            addModal(target, dlgJustificativa = new DlgDisabledArea(newModalId(), bundle("justificativaNaoPadronizado")));
        }
        dlgJustificativa.show(target, dmi.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativa());
    }

    private void mostrarDlgJustificativaAdep(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgJustificativaAdep == null) {
            addModal(target, dlgJustificativaAdep = new DlgDisabledArea(newModalId(), bundle("justificativaAdep")));
        }
        dlgJustificativaAdep.show(target, dmi.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativaAdep());
    }

    private boolean verificaStatusNormal(DispensacaoMedicamentoItemDTO rowObject) {
        if (DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(rowObject.getDispensacaoMedicamentoItem().getTipo())) {
            return ReceituarioItem.Status.NORMAL.value().equals(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getStatus());
        }
        if (DispensacaoMedicamentoItem.Tipo.COMPONENTE.value().equals(rowObject.getDispensacaoMedicamentoItem().getTipo())) {
            return ReceituarioItemComponente.Status.NORMAL.value().equals(rowObject.getDispensacaoMedicamentoItem().getReceituarioItemComponente().getStatus());
        }
        if (DispensacaoMedicamentoItem.Tipo.KIT.value().equals(rowObject.getDispensacaoMedicamentoItem().getTipo())) {
            return ReceituarioItemKit.Status.NORMAL.value().equals(rowObject.getDispensacaoMedicamentoItem().getHistoricoKit().getStatus());
        }
        return false;
    }

    private void acaoNaoUtilizar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgConfirmacaoObjectNaoUtilizar == null) {
            addModal(target, dlgConfirmacaoObjectNaoUtilizar = new DlgConfirmacaoObject<DispensacaoMedicamentoItemDTO>(newModalId(), bundle("desejaRealmenteNaoUtilizar")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                    if (DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItem ri = dmi.getDispensacaoMedicamentoItem().getReceituarioItem();
                        ri.setStatus(ReceituarioItem.Status.NAO_UTILIZA.value());
                        ri.setDataCancelamento(DataUtil.getDataAtual());
                        ri.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                        receituarioStatusDTO.getLstReceituarioItem().add(ri);
                    }
                    if (DispensacaoMedicamentoItem.Tipo.COMPONENTE.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItemComponente ric = dmi.getDispensacaoMedicamentoItem().getReceituarioItemComponente();
                        ric.setStatus(ReceituarioItemComponente.Status.NAO_UTILIZA.value());
                        ric.setDataCancelamento(DataUtil.getDataAtual());
                        ric.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                        receituarioStatusDTO.getLstReceituarioItemComponente().add(ric);
                    }
                    if (DispensacaoMedicamentoItem.Tipo.KIT.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItemKit rik = getItemHistorico(receituarioStatusDTO.getLstHistoricoKit(), dmi.getDispensacaoMedicamentoItem().getHistoricoKit());
                        if (rik == null) {
                            rik = dmi.getDispensacaoMedicamentoItem().getHistoricoKit();
                            receituarioStatusDTO.getLstHistoricoKit().add(rik);
                        }
                        rik.setStatus(ReceituarioItemKit.Status.NAO_UTILIZA.value());
                        rik.setDataCancelamento(DataUtil.getDataAtual());
                        rik.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                    }
                    tblItensReceituario.update(target);
                }
            });
        }
        dlgConfirmacaoObjectNaoUtilizar.show(target, dmi);
    }

    private ReceituarioItemKit getItemHistorico(List<ReceituarioItemKit> lstReceituarioItemKit, ReceituarioItemKit historico) {
        for (ReceituarioItemKit rik : lstReceituarioItemKit) {
            if (rik.getProdutoKit().getCodigo().equals(historico.getProdutoKit().getCodigo()) && rik.getReceituarioItem().getCodigo().equals(historico.getReceituarioItem().getCodigo())) {
                return rik;
            }
        }
        return null;
    }

    private void acaoReutilizar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgConfirmacaoObjectReutilizar == null) {
            addModal(target, dlgConfirmacaoObjectReutilizar = new DlgConfirmacaoObject<DispensacaoMedicamentoItemDTO>(newModalId(), bundle("desejaRealmenteReutilizar")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                    if (DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItem ri = dmi.getDispensacaoMedicamentoItem().getReceituarioItem();
                        ri.setStatus(ReceituarioItem.Status.NORMAL.value());
                        ri.setDataCancelamento(DataUtil.getDataAtual());
                        ri.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                        receituarioStatusDTO.getLstReceituarioItem().add(ri);
                    }
                    if (DispensacaoMedicamentoItem.Tipo.COMPONENTE.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItemComponente ric = dmi.getDispensacaoMedicamentoItem().getReceituarioItemComponente();
                        ric.setStatus(ReceituarioItemComponente.Status.NORMAL.value());
                        ric.setDataCancelamento(DataUtil.getDataAtual());
                        ric.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                        receituarioStatusDTO.getLstReceituarioItemComponente().add(ric);
                    }
                    if (DispensacaoMedicamentoItem.Tipo.KIT.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        ReceituarioItemKit rik = getItemHistorico(receituarioStatusDTO.getLstHistoricoKit(), dmi.getDispensacaoMedicamentoItem().getHistoricoKit());
                        if (rik == null) {
                            rik = dmi.getDispensacaoMedicamentoItem().getHistoricoKit();
                            receituarioStatusDTO.getLstHistoricoKit().add(rik);
                        }
                        rik.setStatus(ReceituarioItemKit.Status.NORMAL.value());
                        rik.setDataCancelamento(DataUtil.getDataAtual());
                        rik.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
                    }
                    tblItensReceituario.update(target);
                }
            });
        }
        dlgConfirmacaoObjectReutilizar.show(target, dmi);
    }

    private void acaoSubstituir(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        DispensacaoMedicamentoItemDTO itemDispensar = getItem(dispensacaoMedicamentoItem, itensDispensar);
        if (itemDispensar != null) {
            throw new ValidacaoException(bundle("itemJaAdicionadoRemovaParaSubstituir"));
        }
        if (dlgSubstituicaoProdutoKit == null) {
            addModal(target, dlgSubstituicaoProdutoKit = new DlgSubstituicaoProdutoKit(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Produto produto, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                    object.getDispensacaoMedicamentoItem().setProduto(produto);
                    object.getDispensacaoMedicamentoItem().setLstCodigoBarrasProduto(new ArrayList<CodigoBarrasProduto>());
                    object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    tblItensReceituario.update(target);
                }
            });
        }
        dlgSubstituicaoProdutoKit.setObject(dispensacaoMedicamentoItem);
        dlgSubstituicaoProdutoKit.show(target);
    }

    private void acaoVincular(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        if (dlgVincularProduto == null) {
            addModal(target, dlgVincularProduto = new DlgVincularProduto(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Produto produto, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                    if (produto == null) {
                        throw new ValidacaoException(bundle("informeProduto"));
                    }
                    object.getDispensacaoMedicamentoItem().setProduto(produto);
                    object.getDispensacaoMedicamentoItem().getReceituarioItem().setProduto(produto);
                    Long quantidadePrescrita = ReceituarioHelper.calcularQuantidadePrescrita(object.getDispensacaoMedicamentoItem().getReceituarioItem());
                    object.getDispensacaoMedicamentoItem().getReceituarioItem().setQuantidadePrescrita(quantidadePrescrita);
                    Double saldo = Coalesce.asLong(object.getDispensacaoMedicamentoItem().getReceituarioItem().getQuantidadePrescrita()).doubleValue()
                            - Coalesce.asLong(object.getDispensacaoMedicamentoItem().getReceituarioItem().getQuantidadeDispensada()).doubleValue();
                    object.getDispensacaoMedicamentoItem().setQuantidadeDispensar(saldo >= 0D ? saldo : 0D);
                    object.getDispensacaoMedicamentoItem().setLstCodigoBarrasProduto(new ArrayList<CodigoBarrasProduto>());
                    object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    tblItensReceituario.update(target);
                }
            });
        }
        dlgVincularProduto.setObject(dispensacaoMedicamentoItem);
        dlgVincularProduto.show(target);
    }

    private void acaoConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        //carrega o subgrupo
        SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                .start().getVO();

        //verifica se controla lote
        if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
            lotesCarregados = new ArrayList();

            Produto produto = dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto();
            Empresa empresa = empresaBaixa;
            Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

            if (produto != null && deposito != null) {
                QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                param.setDataValidade(DataUtil.getDataAtual());
                param.setCodigoEmpresa(empresa.getCodigo());
                param.setCodigoDeposito(deposito.getCodigo());
                param.setCodigoProduto(produto.getCodigo());
                param.setApenasComDisponivel(true);

                lotesCarregados = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
            }
            //verifica se tem mais de um lote disponivel
            if (lotesCarregados.size() == 1) {
                if (dlgDispensarSimplesAdicionar == null) {
                    addModal(target, dlgDispensarSimplesAdicionar = new DlgDispensarSimples(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                            if (object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null) {
                                object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                            }
                            Produto produto = object.getDispensacaoMedicamentoItem().getProduto();
                            Empresa empresa = empresaBaixa;
                            Deposito deposito = empresa.getEmpresaMaterial().getDeposito();
                            if (produto != null && deposito != null) {

                                QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                                param.setDataValidade(DataUtil.getDataAtual());
                                param.setCodigoEmpresa(empresa.getCodigo());
                                param.setCodigoDeposito(deposito.getCodigo());
                                param.setCodigoProduto(produto.getCodigo());
                                param.setApenasComDisponivel(true);
                                lotesCarregados = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
                            }

                            if (lotesCarregados == null || lotesCarregados.isEmpty()) {
                                throw new ValidacaoException(bundle("naoExisteEstoqueCadastradoParaEsteProduto"));
                            }

                            MovimentoGrupoEstoqueItemDTO loteBanco = lotesCarregados.get(0);
                            MovimentoGrupoEstoqueItemDTO loteItem = getLote(object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList(), loteBanco);
                            if (loteItem != null) {
                                loteItem.setQuantidade(object.getDispensacaoMedicamentoItem().getQuantidadeDispensada() + quantidade.doubleValue());
                            } else {
                                loteBanco.setQuantidade(object.getDispensacaoMedicamentoItem().getQuantidadeDispensada() + quantidade.doubleValue());
                                object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().add(loteBanco);
                            }
                            adicionar(target, quantidade, object);
                        }
                    });
                }
                dlgDispensarSimplesAdicionar.setObject(dispensacaoMedicamentoItem, dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getQuantidadeDispensar().longValue());
                dlgDispensarSimplesAdicionar.show(target);
            } else {
                if (dlgDispensarControlaLoteAdicionar == null) {
                    addModal(target, dlgDispensarControlaLoteAdicionar = new DlgDispensarControlaLote(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object, Long quantidade, List<MovimentoGrupoEstoqueItemDTO> lotes) throws ValidacaoException, DAOException {
                            if (quantidade.doubleValue() > object.getDispensacaoMedicamentoItem().getQuantidadeDispensar()
                                    && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
                                throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
                            }
                            if (object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null) {
                                object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                            }
                            if (object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
                                object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().addAll(lotes);
                            } else {
                                for (MovimentoGrupoEstoqueItemDTO lote : lotes) {
                                    MovimentoGrupoEstoqueItemDTO dto = getLote(object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList(), lote);
                                    if (dto != null) {
                                        dto.setQuantidade(Coalesce.asDouble(dto.getQuantidade()) + Coalesce.asDouble(lote.getQuantidade()));
                                        if (lote.getLstCodigoBarrasProduto() != null && !lote.getLstCodigoBarrasProduto().isEmpty()) {
                                            dto.getLstCodigoBarrasProduto().addAll(lote.getLstCodigoBarrasProduto());
                                        }
                                    } else {
                                        object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().add(lote);
                                    }
                                }
                            }

                            adicionar(target, quantidade, object);
                        }
                    });
                }
                dlgDispensarControlaLoteAdicionar.setObject(target, empresaBaixa, dispensacaoMedicamentoItem, false);
                dlgDispensarControlaLoteAdicionar.show(target);
            }
        } else {
            if (dlgDispensarSimplesAdicionarSemLote == null) {
                addModal(target, dlgDispensarSimplesAdicionarSemLote = new DlgDispensarSimples(newModalId()) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                        adicionar(target, quantidade, object);
                    }
                });
            }
            dlgDispensarSimplesAdicionarSemLote.setObject(dispensacaoMedicamentoItem, dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getQuantidadeDispensar().longValue());
            dlgDispensarSimplesAdicionarSemLote.show(target);
        }
    }

    private void adicionar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException {
        //verifica se possui saldo no receituario
        if (quantidade > object.getDispensacaoMedicamentoItem().getQuantidadeDispensar()
                && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
            throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
        }
        //verifica se vai dispensar total ou parcialmente
        if (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar().equals(quantidade.doubleValue())) {
            //remove do receituario, pois foi totalmente dispensado
            ItensReceituario.remove(getIndexOfItem(object, ItensReceituario));
            //verifica se ja foi dispensado parcialmente
            DispensacaoMedicamentoItemDTO itemDispensar = getItem(object, itensDispensar);
            object.getDispensacaoMedicamentoItem().setQuantidadeDispensar(0D);
            if (itemDispensar == null) {
                object.getDispensacaoMedicamentoItem().setQuantidadeDispensada(quantidade.doubleValue());
                itensDispensar.add(object);
            } else {
                itemDispensar.getDispensacaoMedicamentoItem().setQuantidadeDispensada(itemDispensar.getDispensacaoMedicamentoItem().getQuantidadeDispensada() + quantidade.doubleValue());
            }
        } else {
            //remove a quantidade a ser dispensada do receituario
            DispensacaoMedicamentoItemDTO itemReceituario = getItem(object, ItensReceituario);
            itemReceituario.getDispensacaoMedicamentoItem().setQuantidadeDispensar(itemReceituario.getDispensacaoMedicamentoItem().getQuantidadeDispensar() - quantidade.doubleValue());
            //verifica se ja foi dispensado parcialmente
            DispensacaoMedicamentoItemDTO itemDispensar = getItem(object, itensDispensar);
            if (itemDispensar == null) {
                object.getDispensacaoMedicamentoItem().setQuantidadeDispensada(quantidade.doubleValue());
                itensDispensar.add(object);
            } else {
                itemDispensar.getDispensacaoMedicamentoItem().setQuantidadeDispensada(itemDispensar.getDispensacaoMedicamentoItem().getQuantidadeDispensada() + quantidade.doubleValue());
            }
        }
        tblItensReceituario.update(target);
        tblItensDispensar.update(target);
    }

    private DispensacaoMedicamentoItemDTO getItem(DispensacaoMedicamentoItemDTO dmi, List<DispensacaoMedicamentoItemDTO> list) {
        for (DispensacaoMedicamentoItemDTO item : list) {
            if (dmi.getDispensacaoMedicamentoItem().getProduto().equals(item.getDispensacaoMedicamentoItem().getProduto())) {
                if (item.getDispensacaoMedicamentoItem().getTipo().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                    if (DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDispensacaoMedicamentoItem().getReceituarioItem().equals(item.getDispensacaoMedicamentoItem().getReceituarioItem())) {
                            return item;
                        }
                    }
                    if (DispensacaoMedicamentoItem.Tipo.COMPONENTE.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDispensacaoMedicamentoItem().getReceituarioItemComponente().equals(item.getDispensacaoMedicamentoItem().getReceituarioItemComponente())) {
                            return item;
                        }
                    }
                    if (DispensacaoMedicamentoItem.Tipo.KIT.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDmiPrincipalKit().getProduto().getCodigo().equals(item.getDmiPrincipalKit().getProduto().getCodigo())) {
                            return item;
                        }
                    }
                }
            }
        }

        return null;
    }

    private int getIndexOfItem(DispensacaoMedicamentoItemDTO dmi, List<DispensacaoMedicamentoItemDTO> list) throws ValidacaoException {
        for (int i = 0; i < list.size(); i++) {
            if (dmi.getDispensacaoMedicamentoItem().getProduto().equals(list.get(i).getDispensacaoMedicamentoItem().getProduto())) {
                if (list.get(i).getDispensacaoMedicamentoItem().getTipo().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                    if (DispensacaoMedicamentoItem.Tipo.ITEM.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDispensacaoMedicamentoItem().getReceituarioItem().equals(list.get(i).getDispensacaoMedicamentoItem().getReceituarioItem())) {
                            return i;
                        }
                    }
                    if (DispensacaoMedicamentoItem.Tipo.COMPONENTE.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDispensacaoMedicamentoItem().getReceituarioItemComponente().equals(list.get(i).getDispensacaoMedicamentoItem().getReceituarioItemComponente())) {
                            return i;
                        }
                    }
                    if (DispensacaoMedicamentoItem.Tipo.KIT.value().equals(dmi.getDispensacaoMedicamentoItem().getTipo())) {
                        if (dmi.getDmiPrincipalKit().getProduto().getCodigo().equals(list.get(i).getDmiPrincipalKit().getProduto().getCodigo())) {
                            return i;
                        }
                    }
                }
            }
        }
        throw new ValidacaoException(bundle("indexNaoEncontrado"));
    }

    private ICollectionProvider getCollectionProviderItensReceituario() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ItensReceituario;
            }
        };
    }

    private List<IColumn> getColumnsItensDispensar() {
        List<IColumn> columns = new ArrayList<IColumn>();

        DispensacaoMedicamentoItemDTO proxy = on(DispensacaoMedicamentoItemDTO.class
        );

        columns.add(getCustomColumnItensDispensar());
        columns.add(createColumn(bundle("produto"), proxy.getDispensacaoMedicamentoItem().getProduto().getDescricao()));
        columns.add(createColumn(bundle("un"), proxy.getDispensacaoMedicamentoItem().getProduto().getUnidade().getUnidade()));
        columns.add(createColumn(bundle("lotes"), proxy.getDispensacaoMedicamentoItem().getDescricaoLote()));
        columns.add(createColumn(bundle("via"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getTipoViaMedicamento().getDescricao()));
        columns.add(createColumn(bundle("posologia"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getPosologia()));
        columns.add(createColumn(bundle("dispensado"), proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada()));

        return columns;
    }

    private IColumn getCustomColumnItensDispensar() {
        return new MultipleActionCustomColumn<DispensacaoMedicamentoItemDTO>() {
            @Override
            public void customizeColumn(DispensacaoMedicamentoItemDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoEditar(target, dmi);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        acaoRemover(target, dmi);
                    }
                });
            }
        };
    }

    private void acaoRemover(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException {
        dmi.getDispensacaoMedicamentoItem().setQuantidadeDispensar(dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada());
        dmi.getDispensacaoMedicamentoItem().setQuantidadeDispensada(0D);
        dmi.getDispensacaoMedicamentoItem().setLstCodigoBarrasProduto(new ArrayList<CodigoBarrasProduto>());
        DispensacaoMedicamentoItemDTO itemReceituario = getItem(dmi, ItensReceituario);
        if (itemReceituario == null) {
            ItensReceituario.add(dmi);
        }
        dmi.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
        itensDispensar.remove(getIndexOfItem(dmi, itensDispensar));
        tblItensDispensar.update(target);
        tblItensReceituario.update(target);

    }

    private void acaoEditar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        //carrega o subgrupo
        SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class
        )
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                .start().getVO();

        //verifica se controla lote
        if (subGrupo.getFlagControlaGrupoEstoque()
                .equals(RepositoryComponentDefault.SIM)) {
            List<MovimentoGrupoEstoqueItemDTO> itens = new ArrayList();

            Produto produto = dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto();
            Empresa empresa = empresaBaixa;
            Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

            if (produto != null && deposito != null) {
                QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                param.setDataValidade(DataUtil.getDataAtual());
                param.setCodigoEmpresa(empresa.getCodigo());
                param.setCodigoDeposito(deposito.getCodigo());
                param.setCodigoProduto(produto.getCodigo());
                param.setApenasComDisponivel(true);

                itens = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
            }
            //verifica se tem mais de um lote disponivel
            if (itens.size() == 1) {
                if (!existeCodigoBarrasLote(dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem())) {
                    addDlgSimplesLote(target);
                    dlgDispensarSimplesEditarLote.setObject(dispensacaoMedicamentoItem, dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getQuantidadeDispensada().longValue());
                    dlgDispensarSimplesEditarLote.show(target);
                } else {
                    addDlgEditaCodigoBarrasLote(target);
                    dlgEditaCodigoBarrasLote.setObject(target, dispensacaoMedicamentoItem);
                    dlgEditaCodigoBarrasLote.show(target);
                }
            } else {
                if (!existeCodigoBarrasLote(dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem())) {
                    if (dlgDispensarControlaLoteEditar == null) {
                        addModal(target, dlgDispensarControlaLoteEditar = new DlgDispensarControlaLote(newModalId()) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object, Long quantidade, List<MovimentoGrupoEstoqueItemDTO> lotes) throws ValidacaoException, DAOException {
                                if (quantidade > (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + object.getDispensacaoMedicamentoItem().getQuantidadeDispensada())
                                        && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
                                    throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
                                }
                                object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                                object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().addAll(lotes);
                                editar(target, quantidade, object);
                            }
                        });
                    }
                    dlgDispensarControlaLoteEditar.setObject(target, empresaBaixa, dispensacaoMedicamentoItem, true);
                    dlgDispensarControlaLoteEditar.show(target);
                } else {
                    addDlgEditaCodigoBarrasLote(target);
                    dlgEditaCodigoBarrasLote.setObject(target, dispensacaoMedicamentoItem);
                    dlgEditaCodigoBarrasLote.show(target);
                }
            }
        } else {
            if (dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().isEmpty()) {
                addDlgSimples(target);
                dlgDispensarSimplesEditar.setObject(dispensacaoMedicamentoItem, dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getQuantidadeDispensada().longValue());
                dlgDispensarSimplesEditar.show(target);
            } else {
                addDlgEditaCodigoBarras(target);
                dlgEditaCodigoBarras.setObject(target, dispensacaoMedicamentoItem);
                dlgEditaCodigoBarras.show(target);
            }
        }
    }

    private boolean existeCodigoBarrasLote(DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        for (MovimentoGrupoEstoqueItemDTO m : dispensacaoMedicamentoItem.getMovimentoGrupoEstoqueItemDTOList()) {
            if (!m.getLstCodigoBarrasProduto().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    private void editar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException {
        if (quantidade > (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + object.getDispensacaoMedicamentoItem().getQuantidadeDispensada())
                && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
            throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
        }
        if (!object.getDispensacaoMedicamentoItem().getQuantidadeDispensada().equals(quantidade.doubleValue())) {
            object.getDispensacaoMedicamentoItem().setQuantidadeDispensar((object.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + object.getDispensacaoMedicamentoItem().getQuantidadeDispensada()) - quantidade.doubleValue());
            object.getDispensacaoMedicamentoItem().setQuantidadeDispensada(quantidade.doubleValue());
            DispensacaoMedicamentoItemDTO itemReceituario = getItem(object, ItensReceituario);
            if (itemReceituario == null) {
                ItensReceituario.add(object);
            }
            if (object.getDispensacaoMedicamentoItem().getQuantidadeDispensada().equals(0D)) {
                itensDispensar.remove(getIndexOfItem(object, itensDispensar));
            }
            if (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar().equals(0D)) {
                ItensReceituario.remove(getIndexOfItem(object, ItensReceituario));
            }
        }
        tblItensReceituario.update(target);
        tblItensDispensar.update(target);
    }

    private ICollectionProvider getCollectionProviderItensDispensar() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itensDispensar;
            }
        };
    }

    private void addDlgSimples(AjaxRequestTarget target) {
        if (dlgDispensarSimplesEditar == null) {
            addModal(target, dlgDispensarSimplesEditar = new DlgDispensarSimples(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                    editar(target, quantidade, object);
                }
            });
        }
    }

    private void addDlgSimplesLote(AjaxRequestTarget target) {
        if (dlgDispensarSimplesEditarLote == null) {
            addModal(target, dlgDispensarSimplesEditarLote = new DlgDispensarSimples(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException {
                    if (quantidade > (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + object.getDispensacaoMedicamentoItem().getQuantidadeDispensada())
                            && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
                        throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
                    }
                    if (!object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
                        object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().get(0).setQuantidade(quantidade.doubleValue());
                    }
                    editar(target, quantidade, object);
                }
            });
        }
    }

    private void addDlgEditaCodigoBarras(AjaxRequestTarget target) {
        if (dlgEditaCodigoBarras == null) {
            addModal(target, dlgEditaCodigoBarras = new DlgEditaCodigoBarras(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object, Long quantidade, List<CodigoBarrasProduto> lstCodigoBarras) throws ValidacaoException, DAOException {
                    editar(target, quantidade, object);
                    object.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().clear();
                    if (!lstCodigoBarras.isEmpty()) {
                        object.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().addAll(lstCodigoBarras);
                    }
                }
            });
        }
    }

    private void addDlgEditaCodigoBarrasLote(AjaxRequestTarget target) {
        if (dlgEditaCodigoBarrasLote == null) {
            addModal(target, dlgEditaCodigoBarrasLote = new DlgEditaCodigoBarrasLote(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object, Long quantidade, List<MovimentoGrupoEstoqueItemDTO> lotes) throws ValidacaoException, DAOException {
                    if (quantidade > (object.getDispensacaoMedicamentoItem().getQuantidadeDispensar() + object.getDispensacaoMedicamentoItem().getQuantidadeDispensada())
                            && RepositoryComponentDefault.NAO.equals(object.getDispensacaoMedicamentoItem().getProduto().getFlagPermiteDispensarMais())) {
                        throw new ValidacaoException(bundle("quantidadeSuperiorSaldoDisponivel"));
                    }
                    object.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    object.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().addAll(lotes);
                    editar(target, quantidade, object);
                }
            });
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("dispensacaoPrescricao");
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (CollectionUtils.isEmpty(itensDispensar)
                && CollectionUtils.isEmpty(receituarioStatusDTO.getLstReceituarioItem())
                && CollectionUtils.isEmpty(receituarioStatusDTO.getLstReceituarioItemComponente())
                && CollectionUtils.isEmpty(receituarioStatusDTO.getLstHistoricoKit())) {
            throw new ValidacaoException(bundle("msgNaoEncontradoAlteracoesSalvarDispensacao"));
        }

        for (DispensacaoMedicamentoItemDTO _dispensacaoMedicamentoItem : itensDispensar) {
            if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getCoalesceQuantidadeDispensada() == 0D) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_dispensacao_quantidade_maior_zero"));
            }

            if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto() != null && _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getCodigo() != null) {
                SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class
                )
                        .setId(_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId())
                        .start().getVO();

                if (subGrupo.getFlagControlaGrupoEstoque()
                        .equals(RepositoryComponentDefault.SIM)) {
                    if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null || _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_com_controle_de_lote_tem_que_ser_definido"));
                    }
                }
            } else {
                throw new ValidacaoException(bundle("msgMedicamentoXNaoCadastradoParaContinuarEditeVinculeMedicamentoCadastrado", _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getReceituarioItem().getNomeProduto()));
            }
        }

        DispensacaoMedicamento dispensacaoMedicamento = form.getModelObject();

        if (TipoReceita.RECEITA_AMARELA.equals(receituario.getTipoReceita().getTipoReceita())
                || TipoReceita.RECEITA_AZUL.equals(receituario.getTipoReceita().getTipoReceita())) {
            if (numeroReceita == null) {
                throw new ValidacaoException(bundle("msgNumeroReceitaObrigatorio"));
            }
            receituario.setNumeroReceita(numeroReceita);
        }

        List<DispensacaoMedicamentoItem> lstDmiTemp = new ArrayList();
        for (DispensacaoMedicamentoItemDTO dto : itensDispensar) {
            lstDmiTemp.add(dto.getDispensacaoMedicamentoItem());
        }

        dispensacaoMedicamento.setItensDispensacaoMedicamentoSet(new HashSet(lstDmiTemp));

        if (dispensacaoMedicamento.getAtendimento() == null) {
            dispensacaoMedicamento.setAtendimento(dispensacaoMedicamento.getReceituario().getAtendimento());

        }

        dispensacaoMedicamento = BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).dispensarPrescricao(dispensacaoMedicamento, receituario, receituarioStatusDTO);
        form.setModelObject(dispensacaoMedicamento);

        if (existsItemPermissaoComprovante(dispensacaoMedicamento)) {
            dlgImpressao.show(target);
        } else {
            confirmar(target);
        }
    }

    private boolean existsItemPermissaoComprovante(DispensacaoMedicamento dispensacaoMedicamento) {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, dispensacaoMedicamento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_COMPROVANTE), RepositoryComponentDefault.SIM_LONG))
                .exists();
    }

    public String getMsgSalvo(DispensacaoMedicamento returnObject) {
        String msg = bundle("registro_salvo_sucesso");

        if (returnObject.getCodigo() != null) {
            String identificador = returnObject.getCodigo().toString();
            msg += " " + bundle("codigo") + ": " + identificador;
        }
        return msg;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCodigoBarras;

    }

    private void adicionarViaCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (codigoBarrasProduto != null) {
            Double quantidadeProduto  = 1D;
            CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class)
                    .addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, codigoBarrasProduto.toString()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value()))
                    .setMaxResults(1).start().getVO();

            if (cbp == null) {
                resetarFocoCodigoBarras(target);
                throw new ValidacaoException(bundle("codigoBarrasInvalido"));
            } else {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (cbp.isEtiquetaForaEstoque()) {
                        resetarFocoCodigoBarras(target);
                        throw new ValidacaoException(bundle("codigoBarrasJaDispensado"));
                    }
                }

                quantidadeProduto = Coalesce.asDouble(cbp.getQuantidadeProduto(), 1D);

                DispensacaoMedicamentoItemDTO itemReceituario = null;
                for (DispensacaoMedicamentoItemDTO item : ItensReceituario) {
                    if (cbp.getProduto().equals(item.getDispensacaoMedicamentoItem().getProduto())) {
                        itemReceituario = item;
                    }
                }
                if (itemReceituario == null) {
                    resetarFocoCodigoBarras(target);
                    throw new ValidacaoException(bundle("produtoNaoConstaReceituarioOuExcedeuLimitePrescrito"));
                }

                DispensacaoMedicamentoItemDTO itemDispensar = getItem(itemReceituario, itensDispensar);
                //carrega o subgrupo
                SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), itemReceituario.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), itemReceituario.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                        .start().getVO();

                //verifica se controla lote
                if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
                    if (itemDispensar != null) {
                        for (MovimentoGrupoEstoqueItemDTO dto : itemDispensar.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList()) {
                            if (dto.getLstCodigoBarrasProduto().contains(cbp)) {
                                resetarFocoCodigoBarras(target);
                                throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                            }
                        }
                    }

                    if (itemReceituario.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null) {
                        itemReceituario.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    }

                    MovimentoGrupoEstoqueItemDTO dto = getLote(itemReceituario.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList(), cbp);
                    cbp.setDispensacaoMedicamentoItem(itemReceituario.getDispensacaoMedicamentoItem());

                    Double quantidadeProdutoAux = quantidadeProduto;
                    if (dto == null) {
                        EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
                        estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                        estoqueEmpresaPK.setProduto(cbp.getProduto());

                        EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, ApplicationSession.get().getSession().getEmpresa()))
                                .start().getVO();

                        if (empresaMaterial == null) {
                            throw new ValidacaoException("Não foi encontrado um depósito padrão definido para a empresa.");
                        }

                        GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
                        grupoEstoquePK.setGrupo(cbp.getGrupo());
                        grupoEstoquePK.setCodigoDeposito(empresaMaterial.getDeposito().getCodigo());
                        grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
                        grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

                        GrupoEstoque grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                                .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                                .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                                .setId(grupoEstoquePK)
                                .start().getVO();

                        if (grupoEstoque == null) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
                        }

                        dto = new MovimentoGrupoEstoqueItemDTO();
                        dto.setDataValidade(grupoEstoque.getDataValidade());
                        dto.setDeposito(grupoEstoque.getRoDeposito());
                        dto.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                        dto.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
                        dto.setEstoqueEncomendado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueEncomendado());
                        dto.setEstoqueFisico(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueFisico());
                        dto.setEstoqueReservado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueReservado());
                        dto.setGrupoEstoque(grupoEstoque.getId().getGrupo());
                        dto.setProduto(cbp.getProduto());
                        itemReceituario.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().add(dto);
                    } else {
                        quantidadeProdutoAux = new Dinheiro(dto.getQuantidade()).somar(quantidadeProduto).doubleValue();
                    }

                    adicionar(target, quantidadeProduto.longValue(), itemReceituario);

                    dto.setQuantidade(quantidadeProdutoAux);
                    dto.getLstCodigoBarrasProduto().add(cbp);
                } else {
                    if (itemDispensar != null) {
                        if (itemDispensar.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().contains(cbp)) {
                            resetarFocoCodigoBarras(target);
                            throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                        }
                    }

                    adicionar(target, quantidadeProduto.longValue(), itemReceituario);
                    itemDispensar = getItem(itemReceituario, itensDispensar);
                    if (itemDispensar != null) {
                        cbp.setDispensacaoMedicamentoItem(itemDispensar.getDispensacaoMedicamentoItem());
                        itemDispensar.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().add(cbp);
                    } else {
                        throw new ValidacaoException(bundle("erroVincularCodigoBarras"));
                    }
                }

                tblItensReceituario.update(target);
                txtCodigoBarras.limpar(target);
                target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));

                updateNotificationPanel(target);
            }
        }
    }

    private MovimentoGrupoEstoqueItemDTO getLote(List<MovimentoGrupoEstoqueItemDTO> lstLote, CodigoBarrasProduto cbp) {
        if (!lstLote.isEmpty()) {
            for (MovimentoGrupoEstoqueItemDTO dto : lstLote) {
                if (dto.getGrupoEstoque().equals(cbp.getGrupo())) {
                    return dto;
                }
            }
        }
        return null;
    }

    private MovimentoGrupoEstoqueItemDTO getLote(List<MovimentoGrupoEstoqueItemDTO> lstLote, MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO) {
        if (!lstLote.isEmpty()) {
            for (MovimentoGrupoEstoqueItemDTO dto : lstLote) {
                if (dto.getGrupoEstoque().equals(movimentoGrupoEstoqueItemDTO.getGrupoEstoque())) {
                    return dto;
                }
            }
        }
        return null;
    }

    private void resetarFocoCodigoBarras(AjaxRequestTarget target) {
        txtCodigoBarras.limpar(target);
        target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private DispensacaoMedicamento iniciarDispensacao(Receituario receituario) {
        DispensacaoMedicamento dispensacaoMedicamento = new DispensacaoMedicamento();
        if (receituario != null) {
            dispensacaoMedicamento.setDataReceita(receituario.getDataCadastro());
            dispensacaoMedicamento.setEmpresa(empresaBaixa);
            dispensacaoMedicamento.setEmpresaOrigem(receituario.getEmpresa());
            dispensacaoMedicamento.setProfissional(receituario.getProfissional());
            dispensacaoMedicamento.setReceituario(receituario);
            dispensacaoMedicamento.setAtendimento(receituario.getAtendimento());
            dispensacaoMedicamento.setReceita(Coalesce.asString(receituario.getNumeroReceita()));
            dispensacaoMedicamento.setReceitaContinua(receituario.getReceitaContinua());
            dispensacaoMedicamento.setTipoReceita(receituario.getTipoReceita());
            dispensacaoMedicamento.setUsuarioCadsusDestino(receituario.getUsuarioCadsus());

            List<ReceituarioItem> receituarioItems = LoadManager.getInstance(ReceituarioItem.class)
                    .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                    .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_TIPO_RECEITA)).getProperties())
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), receituario))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_STATUS),BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                    .start().getList();

            Long i = 0L;
            for (ReceituarioItem receituarioItem : receituarioItems) {

                if (ReceituarioItem.TipoItem.SOLUCOES.value().equals(receituarioItem.getTipoItem()) || ReceituarioItem.TipoItem.KIT.value().equals(receituarioItem.getTipoItem())) {
                    List<ReceituarioItemComponente> lstComponentes = LoadManager.getInstance(ReceituarioItemComponente.class)
                            .addProperties(new HQLProperties(ReceituarioItemComponente.class).getProperties())
                            .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO)).getProperties())
                            .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                            .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                            .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), receituarioItem.getCodigo()))
                            .start().getList();
                    if (lstComponentes != null && !lstComponentes.isEmpty()) {
                        for (ReceituarioItemComponente componente : lstComponentes) {
                            Produto produto = componente.getProduto();
                            Atendimento atendimento = receituario.getAtendimento();

                            if (produto != null) {
                                boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                                        .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                                        .exists();

                                if (!exists) {
                                    continue;
                                }
                            }
                            DispensacaoMedicamentoItem dmi = new DispensacaoMedicamentoItem();

                            dmi.setDispensacaoMedicamento(dispensacaoMedicamento);
                            dmi.setItem(i);
                            dmi.setReceituarioItem(receituarioItem);
                            dmi.setReceituarioItemComponente(componente);
                            dmi.setProduto(produto);
                            Double saldo = Coalesce.asLong(componente.getQuantidadePrescrita()).doubleValue() - Coalesce.asLong(componente.getQuantidadeDispensada()).doubleValue();
                            dmi.setQuantidadeDispensar(saldo >= 0D ? saldo : 0D);
                            dmi.setQuantidadeDispensada(0D);
                            dmi.setQuantidadePrescrita(componente.getQuantidadePrescrita().doubleValue());
                            dmi.setPosologia(Coalesce.asDouble(receituarioItem.getQuantidadePosologia()));
                            dmi.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());

                            if (produto != null) {
                                Date dataPrescricao = DataUtil.getDataAtual();
                                if (receituarioItem.getReceituario() != null) {
                                    dataPrescricao = receituario.getDataCadastro();
                                }

                                Convenio convenio = null;
                                Double precoProduto = null;
                                try {
                                    precoProduto = BOFactoryWicket.getBO(HospitalFacade.class).getPrecoProduto(produto, atendimento.getConvenio(), dataPrescricao);
                                    convenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
                                } catch (DAOException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                } catch (ValidacaoException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                }

                                dmi.setPrecoUnitario(precoProduto);

                                if (convenio != null && convenio.equals(atendimento.getConvenio())) {
                                    validaPrecoUnitario = false;
                                } else {
                                    validaPrecoUnitario = true;
                                }

                                if (produto.getSubGrupo() != null && RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                                    List<MovimentoGrupoEstoqueItemDTO> itens = new ArrayList();

                                    Empresa empresa = empresaBaixa;
                                    Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

                                    if (deposito != null) {
                                        try {
                                            QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                                            param.setDataValidade(DataUtil.getDataAtual());
                                            param.setCodigoEmpresa(empresa.getCodigo());
                                            param.setCodigoDeposito(deposito.getCodigo());
                                            param.setCodigoProduto(produto.getCodigo());
                                            param.setApenasComDisponivel(true);

                                            itens = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
                                        } catch (DAOException ex) {
                                            Loggable.log.error(ex.getMessage(), ex);
                                        } catch (ValidacaoException ex) {
                                            Loggable.log.error(ex.getMessage(), ex);
                                        }
                                    }

                                    if (itens.size() == 1) {
                                        dmi.setMovimentoGrupoEstoqueItemDTOList(Arrays.asList(itens.get(0)));
                                    }
                                }
                            }
                            if ((dmi.getQuantidadeDispensar() - dmi.getQuantidadeDispensada()) > 0D) {
                                DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                                dto.setDispensacaoMedicamentoItem(dmi);
                                dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.COMPONENTE.value());
                                if (ReceituarioItem.TipoItem.KIT.value().equals(receituarioItem.getTipoItem())) {
                                    ItemKitProduto itemKitProduto = LoadManager.getInstance(ItemKitProduto.class)
                                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_CODIGO), receituarioItem.getProduto().getCodigo()))
                                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO, Produto.PROP_CODIGO), dmi.getProduto().getCodigo()))
                                            .start().getVO();
                                    if (itemKitProduto != null) {
                                        dto.setSubstituivel(itemKitProduto.getFlagSubstituivel());
                                        UsuarioCadsus paciente = receituario.getUsuarioCadsus();
                                        if (itemKitProduto.getIdadeMinima() != null) {
                                            if (paciente.getIdadeEmMeses() < itemKitProduto.getIdadeMinima()) {
                                                continue;
                                            }
                                        }
                                        if (itemKitProduto.getIdadeMaxima() != null) {
                                            if (paciente.getIdadeEmMeses() > itemKitProduto.getIdadeMaxima()) {
                                                continue;
                                            }
                                        }
                                    }
                                }
                                ItensReceituario.add(dto);
                                i++;
                            }
                        }
                    }
                } else {
                    Produto produto = receituarioItem.getProduto();
                    Atendimento atendimento = receituario.getAtendimento();

                    if (produto != null) {
                        boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                                .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                                .exists();

                        if (!exists) {
                            continue;
                        }
                    }
//                    else {
//                        EloMedicamentoNaoPadronizadoProduto elo = LoadManager.getInstance(EloMedicamentoNaoPadronizadoProduto.class)
//                                .addProperties(new HQLProperties(Produto.class, EloMedicamentoNaoPadronizadoProduto.PROP_PRODUTO).getProperties())
//                                .addParameter(
//                                        new QueryCustom.QueryCustomParameter(
//                                                VOUtils.montarPath(EloMedicamentoNaoPadronizadoProduto.PROP_MEDICAMENTO_NAO_PADRONIZADO, MedicamentoNaoPadronizado.PROP_DESCRICAO),
//                                                receituarioItem.getNomeProduto())
//                                )
//                                .start().getVO();
//                        if (elo != null) {
//                            produto = elo.getProduto();
//                            receituarioItem.setProduto(produto);
//                        }
//                    }

                    DispensacaoMedicamentoItem dmi = new DispensacaoMedicamentoItem();

                    dmi.setDispensacaoMedicamento(dispensacaoMedicamento);
                    dmi.setItem(i);
                    dmi.setReceituarioItem(receituarioItem);
                    dmi.setProduto(produto);
                    Double saldo = Coalesce.asLong(receituarioItem.getQuantidadePrescrita()).doubleValue() - Coalesce.asLong(receituarioItem.getQuantidadeDispensada()).doubleValue();
                    dmi.setQuantidadeDispensar(saldo >= 0D ? saldo : 0D);
                    dmi.setQuantidadeDispensada(0D);
                    dmi.setPosologia(Coalesce.asDouble(receituarioItem.getQuantidadePosologia()));
                    dmi.setQuantidadePrescrita(Coalesce.asLong(receituarioItem.getQuantidadePrescrita()).doubleValue());
                    dmi.setPosologia(Coalesce.asDouble(receituarioItem.getQuantidadePosologia()));
                    dmi.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());

                    if (produto != null) {
                        Date dataPrescricao = DataUtil.getDataAtual();
                        if (receituarioItem.getReceituario() != null) {
                            dataPrescricao = receituario.getDataCadastro();
                        }

                        Convenio convenio = null;
                        Double precoProduto = null;
                        try {
                            precoProduto = BOFactoryWicket.getBO(HospitalFacade.class).getPrecoProduto(produto, atendimento.getConvenio(), dataPrescricao);
                            convenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
                        } catch (DAOException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        } catch (ValidacaoException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        }

                        dmi.setPrecoUnitario(precoProduto);

                        if (convenio != null && convenio.equals(atendimento.getConvenio())) {
                            validaPrecoUnitario = false;
                        } else {
                            validaPrecoUnitario = true;
                        }

                        if (produto.getSubGrupo() != null && RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                            List<MovimentoGrupoEstoqueItemDTO> itens = new ArrayList();

                            Empresa empresa = empresaBaixa;
                            Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

                            if (deposito != null) {
                                try {
                                    QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                                    param.setDataValidade(DataUtil.getDataAtual());
                                    param.setCodigoEmpresa(empresa.getCodigo());
                                    param.setCodigoDeposito(deposito.getCodigo());
                                    param.setCodigoProduto(produto.getCodigo());
                                    param.setApenasComDisponivel(true);

                                    itens = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
                                } catch (DAOException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                } catch (ValidacaoException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                }
                            }

                            if (itens.size() == 1) {
                                dmi.setMovimentoGrupoEstoqueItemDTOList(Arrays.asList(itens.get(0)));
                            }
                        }

                        if ((dmi.getQuantidadeDispensar() - dmi.getQuantidadeDispensada()) > 0D) {
                            DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                            dto.setDispensacaoMedicamentoItem(dmi);
                            dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.ITEM.value());
                            if (TipoReceita.RECEITA_SOLICITACAO_MATERIAIS.equals(dmi.getReceituarioItem().getReceituario().getTipoReceita().getTipoReceita())) {
                                dto.setSubstituivel(RepositoryComponentDefault.SIM_LONG);
                            }
                            ItensReceituario.add(dto);
                            i = insereKit(dto, i);
                            i++;
                        }
                    } else {
                        if ((dmi.getQuantidadeDispensar() - dmi.getQuantidadeDispensada()) > 0D) {
                            DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                            dto.setDispensacaoMedicamentoItem(dmi);
                            dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.ITEM.value());
                            dto.setDescricaoProduto(receituarioItem.getNomeProduto());
                            ItensReceituario.add(dto);
                            i++;
                        }
                    }
                }
            }
        }

        return dispensacaoMedicamento;
    }

    private Long insereKit(DispensacaoMedicamentoItemDTO dmiPai, Long i) {
        List<ItemKitProduto> lstItemKitProduto = LoadManager.getInstance(ItemKitProduto.class)
                .addProperties(new HQLProperties(ItemKitProduto.class).getProperties())
                .addProperties(new HQLProperties(Produto.class, ItemKitProduto.PROP_PRODUTO).getProperties())
                .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_CODIGO), dmiPai.getDispensacaoMedicamentoItem().getProduto().getCodigo()))
                .start().getList();

        UsuarioCadsus paciente = receituario.getUsuarioCadsus();
        TipoViaMedicamento via = dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem().getTipoViaMedicamento();

        if (lstItemKitProduto != null && !lstItemKitProduto.isEmpty()) {
            for (ItemKitProduto itemKit : lstItemKitProduto) {
                if (itemKit.getIdadeMinima() != null) {
                    if (paciente.getIdadeEmMeses() < itemKit.getIdadeMinima()) {
                        continue;
                    }
                }
                if (itemKit.getIdadeMaxima() != null) {
                    if (paciente.getIdadeEmMeses() > itemKit.getIdadeMaxima()) {
                        continue;
                    }
                }
                if (itemKit.getTipoViaMedicamento() != null) {
                    if (via != null) {
                        if (!via.getCodigo().equals(itemKit.getTipoViaMedicamento().getCodigo())) {
                            continue;
                        }
                    }
                }
                ReceituarioItemKit historico = LoadManager.getInstance(ReceituarioItemKit.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemKit.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemKit.PROP_PRODUTO_KIT, Produto.PROP_CODIGO), itemKit.getProduto().getCodigo()))
                        .start().getVO();
                Double saldo;
                if (historico != null) {
                    saldo = Coalesce.asDouble(historico.getQuantidade()) - Coalesce.asDouble(historico.getQuantidadeDispensada());
                } else {
                    saldo = itemKit.getQuantidade() * dmiPai.getDispensacaoMedicamentoItem().getQuantidadePrescrita();
                }
                if (saldo > 0D) {
                    DispensacaoMedicamentoItem dmiKit = new DispensacaoMedicamentoItem();
                    dmiKit.setDispensacaoMedicamento(dmiPai.getDispensacaoMedicamentoItem().getDispensacaoMedicamento());
                    dmiKit.setItem(i);
                    dmiKit.setReceituarioItem(dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem());
                    dmiKit.setProduto(itemKit.getProduto());
                    dmiKit.setQuantidadeDispensar(saldo);
                    dmiKit.setQuantidadeDispensada(0D);
                    dmiKit.setQuantidadePrescrita(itemKit.getQuantidade() * dmiPai.getDispensacaoMedicamentoItem().getQuantidadePrescrita());
                    dmiKit.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());
                    DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                    dto.setDispensacaoMedicamentoItem(dmiKit);
                    dto.setDmiPrincipalKit(dmiPai.getDispensacaoMedicamentoItem());
                    dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.KIT.value());
                    ItensReceituario.add(dto);
                    i++;
                    if (historico == null) {
                        historico = new ReceituarioItemKit();
                        historico.setProdutoKit(itemKit.getProduto());
                        historico.setReceituarioItem(dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem());
                        historico.setUnidade(itemKit.getUnidade());
                        historico.setQuantidade(dmiKit.getQuantidadePrescrita());
                        historico.setStatus(ReceituarioItemKit.Status.NORMAL.value());
                        receituarioStatusDTO.getLstHistoricoKit().add(historico);
                    }
                    dto.setSubstituivel(itemKit.getFlagSubstituivel());
                    dto.getDispensacaoMedicamentoItem().setHistoricoKit(historico);
                }
            }
        }
        return i;
    }

    private DataReport imprimir() throws ReportException {
        QueryImpressaoDispensacaoPrescricaoDTOParam param = new QueryImpressaoDispensacaoPrescricaoDTOParam();
        param.setCodigoDispensacao(form.getModelObject().getCodigo());
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).impressaoDispensacaoPrescricao(param);
    }

    private void confirmar(AjaxRequestTarget target) throws DAOException {
        Page page = new ConsultaPrescricaoAtendimentoPage();

        if (pageVoltar != null) {
            page = pageVoltar;
        }
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, getMsgSalvo(form.getModelObject()));
    }

    private void carregaReceituario(Receituario receituario) {
        this.receituario = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, Receituario.PROP_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL)).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, Receituario.PROP_USUARIO_CADSUS).getProperties())
                .addProperties(new HQLProperties(TipoReceita.class, Receituario.PROP_TIPO_RECEITA).getProperties())
                .addProperty(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DISPENSACAO_POR_TURNO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_CODIGO), receituario.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                .start().getVO();
        numeroReceita = this.receituario.getNumeroReceita();
    }

    private void carregaEmpresaBaixa(Empresa empresaBaixa) {
        if (empresaBaixa == null) {
            this.empresaBaixa = SessaoAplicacaoImp.getInstance().getEmpresa();
        } else {
            this.empresaBaixa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, empresaBaixa.getCodigo()))
                    .start().getVO();
        }
    }
}
