package br.com.celk.view.agenda.cadastro;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadastroAgendaPageStep1 extends BasePage {

    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DropDown dropDowntipoAgenda;
    private DropDown dropDownSexo;
    private InputField visibilidade;
    private InputArea<String> txaObservacao;
    private InputArea<String> txaRecomendacao;
    private Agenda agenda;
    private boolean edicao;
    private DropDown dropDownVisualizaRecomendacoes;
    private FileUpload upload;
    private FileUploadField fileUploadField;
    private Label lblAnexo;
    private final MensagemAnexoDTO mensagemAnexoDTO;
    private final List<FileUpload> lstUpload;
    private boolean possuiAnexo;

    private boolean tipoAgendaHorario;
    private boolean tipoAgendaPersonalizada;

    private Long modeloAgendamentoRecepcao;
    private LongField idadeInicio;
    private LongField idadeFim;

    public CadastroAgendaPageStep1() {
        this.possuiAnexo = false;
        this.mensagemAnexoDTO = new MensagemAnexoDTO();
        this.lstUpload = new ArrayList<FileUpload>();
    }

    public CadastroAgendaPageStep1(PageParameters parameters) {
        super(parameters);
        this.possuiAnexo = false;
        this.mensagemAnexoDTO = new MensagemAnexoDTO();
        this.lstUpload = new ArrayList<FileUpload>();
    }

    public CadastroAgendaPageStep1(Agenda agenda) {
        this.possuiAnexo = false;
        this.mensagemAnexoDTO = new MensagemAnexoDTO();
        if (agenda.getGerenciadorArquivo() != null) {
            this.mensagemAnexoDTO.setNomeArquivoOriginal(agenda.getGerenciadorArquivo().getNomeArquivo());
            this.mensagemAnexoDTO.setNomeArquivoUpload(agenda.getGerenciadorArquivo().getCaminho());
            this.mensagemAnexoDTO.setOrigem(agenda.getGerenciadorArquivo().getOrigemArquivo());
        }
        this.lstUpload = new ArrayList<FileUpload>();
        this.agenda = agenda;
        edicao = true;
    }

    @Override
    protected void postConstruct() {
        Form<Agenda> form = new Form("form", new CompoundPropertyModel(agenda == null ? (agenda = new Agenda()) : agenda));

        Agenda proxy = on(Agenda.class);

        form.add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento(path(proxy.getTipoProcedimento())));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa())));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        autoCompleteConsultaProfissional.setEnabled(false);
        form.add(dropDowntipoAgenda = getDropDownTipoAgenda(path(proxy.getTipoAgenda())));
        form.add(visibilidade = new InputField(path(proxy.getVisibilidadeAgenda())));
        dropDownSexo = DropDownUtil.getSexoDropDown(path(proxy.getSexo()));
        dropDownSexo.setEnabled(false);
        form.add(dropDownSexo);
        idadeInicio = new LongField(path(proxy.getIdadeInicio())).setInitialValue(0L);
        idadeInicio.setEnabled(false);
        form.add(idadeInicio);
        idadeFim = new LongField(path(proxy.getIdadeFim())).setInitialValue(0L);
        idadeFim.setEnabled(false);
        form.add(idadeFim);
        form.add(txaObservacao = new InputArea<String>(path(proxy.getObservacao())));
        form.add(dropDownVisualizaRecomendacoes = DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagVisualizaAgendar())));
        form.add(txaRecomendacao = new InputArea<String>(path(proxy.getRecomendacoes())));

        form.add(fileUploadField = new FileUploadField("upload", new PropertyModel<List<FileUpload>>(this, "lstUpload")));
        form.add(lblAnexo = new Label("mensagemAnexoDTO.nomeArquivoOriginal", new PropertyModel<String>(this, "mensagemAnexoDTO.nomeArquivoOriginal")));
        lblAnexo.setOutputMarkupId(true);
        form.add(new SubmitButton("btnAnexar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                upload = fileUploadField.getFileUpload();
                if (upload != null) {
                    target.add(lblAnexo);

                    try {
                        if (upload.getClientFileName().endsWith(".pdf")) {
                            File newFile = File.createTempFile("anexo", upload.getClientFileName());
                            upload.writeTo(newFile);
                            CadastroAgendaPageStep1.this.mensagemAnexoDTO.setNomeArquivoOriginal(upload.getClientFileName());
                            CadastroAgendaPageStep1.this.mensagemAnexoDTO.setNomeArquivoUpload(newFile.getAbsolutePath());
                            CadastroAgendaPageStep1.this.mensagemAnexoDTO.setOrigem(GerenciadorArquivo.OrigemArquivo.ANEXO_RECOMENDACAO_AGENDA.value());
                            CadastroAgendaPageStep1.this.possuiAnexo = true;
                        } else {
                            throw new ValidacaoException(bundle("somentePossivelAnexarPdf"));
                        }
                    } catch (IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            }
        }).setDefaultFormProcessing(false));
        form.add(new AbstractAjaxLink("btnRemoverAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                upload = null;
                CadastroAgendaPageStep1.this.mensagemAnexoDTO.setNomeArquivoOriginal(bundle("nenhumAnexoAdicionado"));
                CadastroAgendaPageStep1.this.mensagemAnexoDTO.setNomeArquivoUpload(null);
                CadastroAgendaPageStep1.this.mensagemAnexoDTO.setOrigem(null);
                target.add(lblAnexo);
            }
        });

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        });

        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar();
            }
        });

        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(
                TipoEstabelecimento.UNIDADE.value(),
                TipoEstabelecimento.SECRETARIA_SAUDE.value(),
                TipoEstabelecimento.EXTERNO.value(),
                TipoEstabelecimento.PRESTADOR_SERVICO.value(),
                TipoEstabelecimento.UNIDADE_FILANTROPICA.value())
        );
        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                avaliarConsultaProfissional(target);
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                avaliarConsultaProfissional(target);
            }
        });
        autoCompleteConsultaTipoProcedimento.add(new ConsultaListener<TipoProcedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                avaliarConsultaProfissional(target);
            }
        });
        autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                avaliarConsultaProfissional(target);
            }
        });

        if  (RepositoryComponentDefault.MODELO_AGENDAMENTO_RECEPCAO_CBO.equals(getParametroModeloAgendamento())){
            getSession().getFeedbackMessages().info(null, BundleManager.getString("msg_agendas_cadastradas"));
        }

        add(form);
        form.getModel().setObject(agenda);
        if (edicao) {
            verificaEdicao();
        }
    }

    private void voltar() {
        setResponsePage(ConsultaAgendaPage.class);
    }

    private void avancar() throws ValidacaoException {
        if (validarAvancar()) {
            Empresa empresa = (Empresa) autoCompleteConsultaEmpresa.getComponentValue();
            TipoProcedimento tipoProcedimento = (TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue();
            CadastroAgendaPageStep2 step2 = new CadastroAgendaPageStep2(agenda, mensagemAnexoDTO, possuiAnexo, tipoAgendaHorario, tipoAgendaPersonalizada, edicao, empresa.getCodigo(), tipoProcedimento.getCodigo());
            setResponsePage(step2);
        }
    }

    private DropDown getDropDownTipoAgenda(String id) {
        if (dropDowntipoAgenda == null) {
            dropDowntipoAgenda = new DropDown(id);

            dropDowntipoAgenda.addChoice(Agenda.TIPO_AGENDA_LOCAL, BundleManager.getString("local"));
            dropDowntipoAgenda.addChoice(Agenda.TIPO_AGENDA_COMPARTILHADA, BundleManager.getString("compartilhada"));
        }
        return dropDowntipoAgenda;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroAgenda");
    }

    private void avaliarConsultaProfissional(AjaxRequestTarget target) {
        TipoProcedimento tipoProcedimento = (TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue();
        Empresa empresa = (Empresa) autoCompleteConsultaEmpresa.getComponentValue();

//        if (tipoProcedimento != null && !TipoProcedimentoClassificacao.EXAMES.equals(tipoProcedimento.getTipoProcedimentoClassificacao().getCodigo()) && empresa != null) {
        if (tipoProcedimento != null && empresa != null) {
            autoCompleteConsultaProfissional.setCodigoEmpresa(empresa.getCodigo());
            autoCompleteConsultaProfissional.setEnabled(true);
            autoCompleteConsultaProfissional.focus(target);
            validaSeApresentaRestricao(tipoProcedimento, empresa, target);
        } else {
            autoCompleteConsultaProfissional.limpar(target);
            autoCompleteConsultaProfissional.setEnabled(false);
            target.add(dropDownSexo.setEnabled(false));
            target.add(idadeInicio.setEnabled(false));
            target.add(idadeFim.setEnabled(false));
        }


        target.add(autoCompleteConsultaProfissional);
    }

    private void validaSeApresentaRestricao(TipoProcedimento tipoProcedimento,Empresa empresa, AjaxRequestTarget target) {

        List<TipoProcedimentoAgenda> lstTipoProcedimentoAgenda =  LoadManager.getInstance(TipoProcedimentoAgenda.class)
                .addProperties(new HQLProperties(TipoProcedimentoAgenda.class).getProperties())
                .addProperty(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_EMPRESA, Empresa.PROP_CODIGO), empresa.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_TIPO_PROCEDIMENTO,TipoProcedimento.PROP_CODIGO), tipoProcedimento.getCodigo()))
                .start().getList();

        if (!lstTipoProcedimentoAgenda.isEmpty() && lstTipoProcedimentoAgenda.get(0).getRestricaoSexoIdade() == TipoProcedimentoAgenda.RestricaoSexoIdade.SIM.value()) {
            target.add(dropDownSexo.setEnabled(true));
            target.add(idadeInicio.setEnabled(true));
            target.add(idadeFim.setEnabled(true));
        }
       else {
            target.add(dropDownSexo.setEnabled(false));
            target.add(idadeInicio.setEnabled(false));
            target.add(idadeFim.setEnabled(false));
        }
    }

    private boolean validarAvancar() throws ValidacaoException {
        ValidacaoProcesso validacao = new ValidacaoProcesso();
        if (autoCompleteConsultaTipoProcedimento.getComponentValue() == null) {
            validacao.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("tipo_procedimento")));
        }
        if (autoCompleteConsultaEmpresa.getComponentValue() == null) {
            validacao.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("unidade")));
        }

        if (autoCompleteConsultaTipoProcedimento.getComponentValue() != null) {
            if (!((TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue()).getTipoProcedimentoClassificacao().pertenceClassificacaoExame()) {
                if (autoCompleteConsultaProfissional.getComponentValue() == null) {
                    validacao.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("profissional")));
                }
            }
        }
        if (visibilidade.getComponentValue() == null) {
            validacao.add(BundleManager.getString("campoXObrigatorio", BundleManager.getString("visibilidadeEmSemanas")));
        }

        if (existeAgendaProcedUniProf()) {
            String procedimento = null;
            String empresa = null;
            if (autoCompleteConsultaProfissional != null && autoCompleteConsultaTipoProcedimento.getComponentValue() != null) {
                procedimento = ((TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue()).getDescricao();
            }
            if (autoCompleteConsultaEmpresa != null && autoCompleteConsultaEmpresa.getComponentValue() != null) {
                empresa = ((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getDescricao();
            }
            if (autoCompleteConsultaProfissional.getComponentValue() == null) {
                validacao.add(BundleManager.getString("jaExisteAgendaProcedimentoUnidade", procedimento, empresa));
            } else {
                String profissa = ((Profissional) autoCompleteConsultaProfissional.getComponentValue()).getNome();
                validacao.add(BundleManager.getString("jaExisteAgendaProcedimentoUnidadeProfissional", procedimento, empresa, profissa));
            }
        }

        if(getIdade(idadeFim.getModelObject()) < getIdade(idadeInicio.getModelObject())){
            validacao.add(BundleManager.getString("idadeInvalida"));
        }

        if(existeFpoVencida()) {
            validacao.add(BundleManager.getString("fpoUnidadeVencidaParaTipoProcedimento"));
        }

        if (!validacao.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacao);
        }

        verificarTipoAgendaHorario();
        verificarTipoAgendaPersonalizada();

        return true;
    }

    private boolean existeFpoVencida() {
        Empresa empresa = (Empresa) autoCompleteConsultaEmpresa.getComponentValue();
        TipoProcedimento tipoProcedimento = (TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue();

        if (empresa != null && tipoProcedimento != null) {
            ExamePrestadorContrato contrato = LoadManager.getInstance(ExamePrestadorContrato.class)
                    .addProperty(VOUtils.montarPath(ExamePrestadorContrato.PROP_DATA_VALIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, empresa.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, tipoProcedimento.getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(ExamePrestadorContrato.PROP_DATA_VALIDADE, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1)
                    .start().getVO();

            if (contrato == null || contrato.getDataValidade().after(DataUtil.getDataAtualSemHora())) {
                return false;
            }

            return true;
        }

        return false;
    }

    private boolean existeAgendaProcedUniProf() {
        LoadManager lm = LoadManager.getInstance(Agenda.class)
                .addProperties(new HQLProperties(Agenda.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Agenda.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, agenda.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Agenda.PROP_TIPO_PROCEDIMENTO), agenda.getTipoProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Agenda.PROP_EMPRESA), agenda.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Agenda.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Agenda.STATUS_CONFIRMADO, Agenda.STATUS_PENDENTE)));

        if (agenda.getProfissional() != null) {
            lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Agenda.PROP_PROFISSIONAL), agenda.getProfissional()));
        } else {
            lm.addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_PROFISSIONAL, BuilderQueryCustom.QueryParameter.IS_NULL));
        }
        return CollectionUtils.isNotNullEmpty(lm.start().getList());
    }

    private void verificarTipoAgendaHorario() throws ValidacaoException {
        tipoAgendaHorario = TipoProcedimento.TipoAgenda.HORARIO.value().equals(AgendamentoHelper.getTipoAgenda(agenda.getTipoProcedimento(), agenda.getEmpresa()));
    }

    private void verificarTipoAgendaPersonalizada() throws ValidacaoException {
        tipoAgendaPersonalizada = TipoProcedimento.TipoAgenda.PERSONALIZADA.value().equals(AgendamentoHelper.getTipoAgenda(agenda.getTipoProcedimento(), agenda.getEmpresa()));
    }

    private void verificaEdicao() {
        Boolean exists = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(AgendaGradeAtendimentoHorario.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO), this.agenda.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, AgendaGradeAtendimentoHorario.STATUS_CANCELADO))
                .exists();


        if (exists) {
            autoCompleteConsultaTipoProcedimento.setEnabled(false);
            autoCompleteConsultaEmpresa.setEnabled(false);
            autoCompleteConsultaProfissional.setEnabled(false);
            //dropDowntipoAgenda.setEnabled(false);
        }

        List<TipoProcedimentoAgenda> lstTipoProcedimentoAgenda =  LoadManager.getInstance(TipoProcedimentoAgenda.class)
                .addProperties(new HQLProperties(TipoProcedimentoAgenda.class).getProperties())
                .addProperty(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_EMPRESA, Empresa.PROP_CODIGO), agenda.getEmpresa().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoProcedimentoAgenda.PROP_TIPO_PROCEDIMENTO,TipoProcedimento.PROP_CODIGO), agenda.getTipoProcedimento().getCodigo()))
                .start().getList();


        if (lstTipoProcedimentoAgenda.get(0).getRestricaoSexoIdade() == TipoProcedimentoAgenda.RestricaoSexoIdade.SIM.value()) {
            dropDownSexo.setEnabled(true);
            idadeInicio.setEnabled(true);
            idadeFim.setEnabled(true);
        }
        else {
            dropDownSexo.setEnabled(false);
            idadeInicio.setEnabled(false);
            idadeFim.setEnabled(false);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaTipoProcedimento.getTxtDescricao().getTextField())));
    }

    private Long getParametroModeloAgendamento() {
        if (modeloAgendamentoRecepcao == null) {
            try {
                modeloAgendamentoRecepcao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("modeloAgendamentoRecepcao");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return modeloAgendamentoRecepcao;
    }

    private Long getIdade(Long idade){
        return idade != null ? idade : 0L;
    }
}
