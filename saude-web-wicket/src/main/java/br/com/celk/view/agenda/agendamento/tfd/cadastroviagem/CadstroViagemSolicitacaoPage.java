package br.com.celk.view.agenda.agendamento.tfd.cadastroviagem;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.component.treetable.pageable.PageableTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ICustomColumnTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ITreeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.solicitacaoviagem.SolicitacaoViagemDTO;
import br.com.celk.view.agenda.solicitacaoviagem.SolicitacaoViagemDTOParam;
import br.com.celk.view.agenda.solicitacaoviagem.SolicitacaoViagemTreeProvider;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.frota.viagem.tipotransporteviagem.autocomplete.AutoCompleteConsultaTipoTransporteViagem;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadstroViagemSolicitacaoPage extends BasePage {

    private Cidade destino;
    private TipoTransporteViagem tipoTransporteViagem;
    private Date dataViagem = DataUtil.getDataAtual();
    private PageableTreeTable tblViagens;
    private final SolicitacaoViagemTreeProvider provider = new SolicitacaoViagemTreeProvider();

    private List<SolicitacaoViagem> lstSolicitacaoViagems;

    public CadstroViagemSolicitacaoPage() {
        init();
    }

    @Override
    protected void postConstruct() {
        super.postConstruct();
        carregarViagens(null);
    }

    public void init() {
        Form form = new Form("form");

        form.add(new AutoCompleteConsultaCidade("destino", new PropertyModel(this, "destino")));
        form.add(new AutoCompleteConsultaTipoTransporteViagem("tipoTransporteViagem", new PropertyModel(this, "tipoTransporteViagem")));
        form.add(new DateChooser("dataViagem", new PropertyModel(this, "dataViagem")));
        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarProcurar();
                carregarViagens(target);
            }
        });

        form.add(tblViagens = new PageableTreeTable("tblViagens", getColumns(), provider, 10));
        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isAllEmpty(lstSolicitacaoViagems)) {
                    throw new ValidacaoException(bundle("msgUmPacienteObrigatorio"));
                }
                validarListaSelecionada();
                setResponsePage(new CadastroViagemTfdPage(lstSolicitacaoViagems));
            }
        });

        form.add(new VoltarButton("btnVoltar"));
        add(form);
    }

    private void validarProcurar() throws ValidacaoException {
        if (destino == null && dataViagem == null && tipoTransporteViagem == null) {
            throw new ValidacaoException(BundleManager.getString("informeAlgumFiltro"));
        }
    }

    private void carregarViagens(AjaxRequestTarget target) {
        SolicitacaoViagemDTOParam param = new SolicitacaoViagemDTOParam();
        param.setDestino(destino);
        param.setTipoTransporteViagem(tipoTransporteViagem);
        param.setDataViagem(dataViagem);
        param.setSituacao(SolicitacaoViagem.Status.PENDENTE.value());
        provider.recarregar(param);
        if (target != null) {
            target.add(tblViagens);
        }
    }

    public List getColumns() {
        List<IColumn<SolicitacaoViagemDTO, String>> result = new LinkedList();
        int headerIndex = 0;

        SolicitacaoViagemDTO proxy = on(SolicitacaoViagemDTO.class);

        result.add(new ITreeColumn(Model.of("")));
        result.add(getSelectionActionColumn());
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getUsuarioCadsus().getNomeSocial()), path(proxy.getSolicitacaoViagemLeaf().getAcompanhante().getNomeSocial()), headerIndex++, Model.of(BundleManager.getString("paciente")), true));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getUsuarioCadsus().getIdade()), null, headerIndex++, Model.of(BundleManager.getString("idade"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getDescricaoPcdSimNao()), null, headerIndex++, Model.of(BundleManager.getString("pcd"))));
        result.add(new DateColumn(bundle("dataViagem"), path(proxy.getSolicitacaoViagem().getDataViagem())));
        result.add(new TimeColumn(bundle("horario"), path(proxy.getSolicitacaoViagem().getHorario())));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getTipoTransporteViagem().getDescricao()), null, headerIndex++, Model.of(BundleManager.getString("tipoTransporte"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getLocal()), null, headerIndex++, Model.of(BundleManager.getString("local"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getCidade().getDescricao()), null, headerIndex++, Model.of(BundleManager.getString("destino"))));
        return result;
    }

    private void validarListaSelecionada() throws ValidacaoException {
        Group<SolicitacaoViagem> groupData = Lambda.group(lstSolicitacaoViagems, by(on(SolicitacaoViagem.class).getDataViagem()));
        if (CollectionUtils.isNotNullEmpty(groupData.findAll()) && groupData.subgroups().size() > 1) {
            throw new ValidacaoException(bundle("msgSelecioneSolicitacoesMesmaDataViagem"));
        }
    }

    private IColumn getSelectionActionColumn() {
        return new CustomColumn<SolicitacaoViagemDTO>() {

            @Override
            public Component getComponent(String componentId, SolicitacaoViagemDTO rowObject) {
                boolean selected = false;
                if (rowObject.getSolicitacaoViagem() != null) {
                    if (lstSolicitacaoViagems == null) {
                        lstSolicitacaoViagems = new ArrayList<SolicitacaoViagem>();
                    }
                    selected = lstSolicitacaoViagems.contains(rowObject.getSolicitacaoViagem());
                }
                return new CadastroSolicitacaoViagemColumnPanel(componentId, rowObject, selected) {
                    @Override
                    public void onSelectionAction(AjaxRequestTarget target, boolean selectionAction, SolicitacaoViagemDTO dto) {
                        if (lstSolicitacaoViagems == null) {
                            lstSolicitacaoViagems = new ArrayList<SolicitacaoViagem>();
                        }
                        if (selectionAction) {
                            SolicitacaoViagem solicitacaoViagemCompleta = consultaSolicitacaoViagemCompleta(dto);
                            lstSolicitacaoViagems.add(solicitacaoViagemCompleta);
                            lstSolicitacaoViagems.addAll(converterListaDTO(dto.getFilhos()));
                        } else {
                            removerSolicitacoes(dto.getSolicitacaoViagem().getSolicitacaoViagemPrincipal().getCodigo());
                            lstSolicitacaoViagems.removeAll(converterListaDTO(dto.getFilhos()));
                        }
                    }
                };
            }
        };
    }

    private void removerSolicitacoes(Long codigo) {
        if (lstSolicitacaoViagems == null || codigo == null) return;

        List<SolicitacaoViagem> novaLista = new ArrayList<SolicitacaoViagem>();

        for (SolicitacaoViagem sv : lstSolicitacaoViagems) {
            if (sv == null || sv.getSolicitacaoViagemPrincipal() == null ||
                    !codigo.equals(sv.getSolicitacaoViagemPrincipal().getCodigo())) {
                novaLista.add(sv);
            }
        }

        lstSolicitacaoViagems.clear();
        lstSolicitacaoViagems.addAll(novaLista);
    }

    private SolicitacaoViagem consultaSolicitacaoViagemCompleta(SolicitacaoViagemDTO dto) {
        SolicitacaoViagem solicitacaoViagemProxy = on(SolicitacaoViagem.class);

        return LoadManager.getInstance(SolicitacaoViagem.class)
                .addProperties(new HQLProperties(SolicitacaoViagem.class).getProperties())
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getNome()))
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getApelido()))
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getUtilizaNomeSocial()))
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getDataNascimento()))
                .addProperty(path(solicitacaoViagemProxy.getUsuarioCadsus().getEnderecoUsuarioCadsus().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getNome()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getUtilizaNomeSocial()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getApelido()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getDataNascimento()))
                .addProperty(path(solicitacaoViagemProxy.getAcompanhante().getEnderecoUsuarioCadsus().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getCidade().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getCidade().getDescricao()))
                .addProperty(path(solicitacaoViagemProxy.getTipoTransporteViagem().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getTipoTransporteViagem().getDescricao()))
                .addProperty(path(solicitacaoViagemProxy.getSolicitacaoViagemPrincipal().getCodigo()))
                .addProperty(path(solicitacaoViagemProxy.getSolicitacaoViagemPrincipal().getStatus()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(solicitacaoViagemProxy.getCodigo()), dto.getSolicitacaoViagem().getSolicitacaoViagemPrincipal().getCodigo()))
                .start().getVO();
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroViagemSolicitacao");
    }

    public List<SolicitacaoViagem> converterListaDTO(List<SolicitacaoViagemDTO> listaNormal) {
        List<SolicitacaoViagem> lstConvertida = new ArrayList();
        for (SolicitacaoViagemDTO dto : listaNormal) {
            if (!dto.isHeader()) {
                lstConvertida.add(dto.getSolicitacaoViagemLeaf());
            }
        }
        return lstConvertida;
    }
}
