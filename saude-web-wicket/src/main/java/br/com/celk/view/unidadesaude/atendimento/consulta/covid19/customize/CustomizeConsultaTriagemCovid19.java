package br.com.celk.view.unidadesaude.atendimento.consulta.covid19.customize;

import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CustomizeConsultaTriagemCovid19 extends CustomizeConsultaAdapter {

    @Override
    public Class getClassConsulta() {
        return FormularioTriagemCovid19.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(
                new String[]{
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_CODIGO),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                        VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO, Atendimento.PROP_CONDUTA_COVID)
                });
    }
}
