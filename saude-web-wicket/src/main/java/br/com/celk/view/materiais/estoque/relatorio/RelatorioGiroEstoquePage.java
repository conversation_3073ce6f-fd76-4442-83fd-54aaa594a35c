package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBox;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioGiroEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioGiroEstoquePage extends RelatorioPage<RelatorioGiroEstoqueDTOParam> {
        
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaEmpresa autoCompleConsultaEmpresa;
    
    
    @Override
    public void init(Form form) {
        Empresa empresa = br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa();
        form.add(autoCompleConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        autoCompleConsultaEmpresa.setComponentValue(empresa);
        form.add(new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioGiroEstoqueDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioGiroEstoqueDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioGiroEstoqueDTOParam.TipoOrdenacao.values()));
        form.add(new CheckBox("estoqueAtualAbaixoEstoqueMinimo"));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioGiroEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioGiroEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioGiroEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioGiroEstoque");
    }
    
    public DropDown getDropDownFormaApresentacao(){
        DropDown dropDown = new DropDown("formaApresentacao");
        dropDown.addChoice(new Long(ReportProperties.GERAL), BundleManager.getString("geral"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_GRUPO) , BundleManager.getString("grupo"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_EMPRESA) , BundleManager.getString("unidadeDestinoOrigem"));
        dropDown.addChoice(new Long(ReportProperties.AGRUPAR_CENTRO_CUSTO), BundleManager.getString("centroCusto"));
        
        return dropDown;
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }
        
        return this.dropDownSubGrupo;
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                dropDownSubGrupo.removeAllChoices();
                                dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                            param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();

                dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
                
                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        return this.dropDownGrupoProduto;
    }
}
