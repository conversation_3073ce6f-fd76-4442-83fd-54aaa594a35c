package br.com.celk.view.agenda.agendamento.manutencaoagendamento;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog.DlgCancelamentoAgendamento;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.exame.dto.CancelamentoAgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.agendamento.manutencaoagendamento.dto.ManutencaoAgendamentoDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoAgendasCanceladasRemanejadasDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ManutencaoAgendamentoPage extends BasePage {

    private Form<ManutencaoAgendamentoDTOParam> form;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private MultiSelectionTable<AgendaGradeAtendimentoHorario> tblAgendamentos;
    private ManutencaoAgendamentoDTOParam param = new ManutencaoAgendamentoDTOParam();
    private List<AgendaGradeAtendimentoHorario> lstAgendaGradeAtendimentoHorario = new ArrayList<AgendaGradeAtendimentoHorario>();
    private DlgCancelamentoAgendamento dlgCancelamentoAgendamento;
    private DlgImpressaoObject<List<AgendaGradeAtendimentoHorario>> dlgImpressao;
    private InputField inputCodigoPaciente;
    private AutoCompleteConsultaProfissional autoCompleteProfissional;
    private Long diasRetroativos;
    private DropDown<Long> tipoAtendimentoDropDown;

    private SolicitacaoAgendamento solicitacaoAgendamento;

    private InputField nrSolicitacao;

    public ManutencaoAgendamentoPage() {
        super();
        init();
    }

    private void init() {
        ManutencaoAgendamentoDTOParam proxy = on(ManutencaoAgendamentoDTOParam.class);
        try {
            diasRetroativos = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("diasRemanejarAgendamentoRetroativo");
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa());
        autoCompleteConsultaEmpresa.setLabel(new Model(bundle("estabelecimento")));

        getForm().add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento(path(proxy.getTipoProcedimento()), true));
        autoCompleteConsultaTipoProcedimento.setIncluirInativos(true);
        autoCompleteConsultaTipoProcedimento.setTfd(false);
        autoCompleteConsultaTipoProcedimento.add(new ConsultaListener<TipoProcedimento>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, ((TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue()).getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_FLAG_AGUARDA_PROCEDIMENTO, RepositoryComponentDefault.SIM_LONG))
                        .start().getVO();

                if (tp != null) {
                    autoCompleteProfissional.setRequired(target, true);
                    target.add(autoCompleteProfissional);
                }
            }
        });
        autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                autoCompleteProfissional.setRequired(target, false);
                target.add(autoCompleteConsultaTipoProcedimento);
            }
        });

        getForm().add(tipoAtendimentoDropDown = DropDownUtil.getTipoAtendimentoDropDown("tipoAtendimento"));
        tipoAtendimentoDropDown.setRequired(true);

        getForm().add(autoCompleteProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));

        getForm().add(new InputField(path(proxy.getPaciente())));
        getForm().add(new PnlDatePeriod(path(proxy.getPeriodo())));
        getForm().add(inputCodigoPaciente = new InputField("codigoPaciente"));

        getForm().add(tblAgendamentos = new MultiSelectionTable("tblAgendamentos", getColumns(), getCollectionProvider()));

        nrSolicitacao = new InputField(path(proxy.getNumeroSolicitacao()));

        nrSolicitacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaEmpresa.setRequired(target, nrSolicitacao.getComponentValue() == null);
                autoCompleteConsultaTipoProcedimento.setRequired(target, nrSolicitacao.getComponentValue() == null);
                target.add(autoCompleteConsultaEmpresa, autoCompleteConsultaTipoProcedimento);
            }
        });

        getForm().add(nrSolicitacao);

        autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                autoCompleteProfissional.setRequired(target, false);
                target.add(autoCompleteConsultaTipoProcedimento);
            }
        });
        getForm().add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarFiltro()) {
                    procurar(target);
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnRemanejar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarDadosDaSolicitacao();
                Long tipoAgenda = AgendamentoHelper.getTipoAgenda(param.getTipoProcedimento(), param.getEmpresa());
                if (TipoProcedimento.TipoAgenda.PERSONALIZADA.value().equals(tipoAgenda)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_fazer_remanejamento_para_este_tipo_procedimento"));
                }
                validarPacientesSelecionados();
                if (getAguardaProcedimento(param.getTipoProcedimento())) {
                    setResponsePage(new RemanejamentoAgendamentoPage(getPacientesSelecionados(), isPermissaoEmpresa(), param.getEmpresa(), true));
                } else {
                    setResponsePage(new RemanejamentoAgendamentoPage(getPacientesSelecionados(), isPermissaoEmpresa(), param.getEmpresa()));
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarPacientesSelecionados();
                cancelamentoAgendamento(target);
            }
        });

        add(getForm());
    }

    private boolean isPermissaoEmpresa() {
        return isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgImpressao == null) {
            addModal(target, dlgImpressao = new DlgImpressaoObject<List<AgendaGradeAtendimentoHorario>>(newModalId(), bundle("msgCancelamentoAgendamentoConcluidoSucesso")) {

                @Override
                public DataReport getDataReport(List<AgendaGradeAtendimentoHorario> agahList) throws ReportException {
                    RelacaoAgendasCanceladasRemanejadasDTOParam param = new RelacaoAgendasCanceladasRemanejadasDTOParam();
                    param.setAgendaGradeAtendimentoHorarioList(agahList);
                    param.setAgendasCanceladas(true);
                    return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relacaoAgendasCanceladasRemanejadas(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agahList) throws ValidacaoException {
                    if (validarFiltro()) {
                        procurar(target);
                    }
                }

            });
            dlgImpressao.setLabelImprimir(target, BundleManager.getString("imprimirListaPaciente"));
        }
        dlgImpressao.show(target, getPacientesSelecionados());
    }

    private List<AgendaGradeAtendimentoHorario> getPacientesSelecionados() {
        return tblAgendamentos.getSelectedObjects();
    }

    private void validarPacientesSelecionados() throws ValidacaoException {
        if (CollectionUtils.isEmpty(getPacientesSelecionados())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_paciente"));
        }
    }

    private void cancelamentoAgendamento(AjaxRequestTarget target) {
        if (dlgCancelamentoAgendamento == null) {
            addModal(target, dlgCancelamentoAgendamento = new DlgCancelamentoAgendamento(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, CancelamentoAgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarAgendamentos(dto);
                    initDialogImpressao(target);
                }
            });
        }
        dlgCancelamentoAgendamento.show(target, getPacientesSelecionados());
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    private Form<ManutencaoAgendamentoDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<ManutencaoAgendamentoDTOParam>(param));
        }
        return this.form;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorario proxy = on(AgendaGradeAtendimentoHorario.class);

        columns.add(new DateColumn(bundle("data"), path(proxy.getDataAgendamento())).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(createColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("estabelecimento"), proxy.getLocalAgendamento().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createColumn(bundle("cpf"), proxy.getUsuarioCadsus().getCpfFormatado()));
        columns.add(createColumn(bundle("tipoProcedimento"), proxy.getTipoProcedimento().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstAgendaGradeAtendimentoHorario;
            }
        };
    }

    private boolean validarFiltro() throws ValidacaoException {
        if (nrSolicitacao.getComponentValue() == null) {
            if (getAguardaProcedimento((TipoProcedimento) autoCompleteConsultaTipoProcedimento.getComponentValue())
                    && autoCompleteProfissional.getComponentValue() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_obrigatorio"));
            }
        } else {
            carregarSolicitacaoAgendamento();
            carregarDadosDaSolicitacao();
            return solicitacaoAgendamento != null;
        }
        return true;
    }

    private void procurar(AjaxRequestTarget target) {

        AgendaGradeAtendimentoHorario proxy = on(AgendaGradeAtendimentoHorario.class);

        if (getAguardaProcedimento(param.getTipoProcedimento())) {
            LoadManager lm = getLoadManager(proxy, true);
            List tipoAgendaList;
            if (TipoAtendimentoAgenda.TIPO_CONSULTA.equals(param.getTipoAtendimento())) {
                tipoAgendaList = Arrays.asList(TipoAtendimentoAgenda.TIPO_RETORNO, param.getTipoAtendimento());
            } else {
                tipoAgendaList = Arrays.asList(param.getTipoAtendimento());
            }

            lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()),
                    QueryCustom.QueryCustomParameter.IN, tipoAgendaList));

            if (diasRetroativos > 0) {
                lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, Data.adjustRangeHour(Data.removeDias(DataUtil.getDataAtual(), diasRetroativos.intValue())).getDataInicial()));
            } else {
                lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, DataUtil.getDataAtual()));
            }

            if (param.getPaciente() != null) {
                lm.addParameter(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getNomePaciente()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()), RepositoryComponentDefault.SIM_LONG))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getApelido()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))))));

            }

            lstAgendaGradeAtendimentoHorario = lm.start().getList();
        } else {
            LoadManager lm = getLoadManager(proxy, false);
            List tipoAgendaList;
            if (TipoAtendimentoAgenda.TIPO_CONSULTA.equals(param.getTipoAtendimento())) {
                tipoAgendaList = Arrays.asList(TipoAtendimentoAgenda.TIPO_RETORNO, param.getTipoAtendimento());
            } else {
                tipoAgendaList = Arrays.asList(param.getTipoAtendimento());
            }

            lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()),
                    QueryCustom.QueryCustomParameter.IN, tipoAgendaList));

            if (diasRetroativos > 0) {
                lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, Data.adjustRangeHour(Data.removeDias(DataUtil.getDataAtual(), diasRetroativos.intValue())).getDataInicial()));
            } else {
                lm.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, DataUtil.getDataAtual()));
            }
            if (param.getPaciente() != null) {
                lm.addParameter(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getNomePaciente()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()), RepositoryComponentDefault.SIM_LONG))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getApelido()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))))));

            }
            lstAgendaGradeAtendimentoHorario = lm.start().getList();
        }

        if (target != null) {
            tblAgendamentos.clearSelection(target);
            tblAgendamentos.update(target);
            tblAgendamentos.populate();
        }
    }

    private LoadManager getLoadManager(AgendaGradeAtendimentoHorario proxy, boolean aguardaProcedimento) {
        if (aguardaProcedimento) {
            return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                    .addProperty(path(proxy.getCodigo()))
                    .addProperty(path(proxy.getVersion()))
                    .addProperty(path(proxy.getDataAgendamento()))
                    .addProperty(path(proxy.getQuantidadeVagasOcupadas()))
                    .addProperty(path(proxy.getUsuarioCadsus().getNome()))
                    .addProperty(path(proxy.getUsuarioCadsus().getCodigo()))
                    .addProperty(path(proxy.getUsuarioCadsus().getApelido()))
                    .addProperty(path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()))
                    .addProperty(path(proxy.getUsuarioCadsus().getCpf()))
                    .addProperty(path(proxy.getNomePaciente()))
                    .addProperty(path(proxy.getProfissional().getCodigo()))
                    .addProperty(path(proxy.getProfissional().getNome()))
                    .addProperty(path(proxy.getLocalAgendamento().getCodigo()))
                    .addProperty(path(proxy.getLocalAgendamento().getDescricao()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getVersion()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getQuantidadeAtendimento()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getVersion()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getVersion()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getTipoProcedimento().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getTipoProcedimento().getVersion()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()))
                    .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getVersion()))
                    .addProperty(path(proxy.getTipoProcedimento().getCodigo()))
                    .addProperty(path(proxy.getTipoProcedimento().getVersion()))
                    .addProperty(path(proxy.getTipoProcedimento().getDescricao()))
                    .addProperty(path(proxy.getAgendaGradeHorario().getCodigo()))
                    .addProperty(path(proxy.getAgendaGradeHorario().getVersion()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getCodigo()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getVersion()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getCodigo()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexo()))
                    .addProperty(path(proxy.getSolicitacaoAgendamento().getTipoFila()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getNumeroSolicitacao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getCodigoPaciente()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getTipoProcedimento()), param.getTipoProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional()), param.getProfissional()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLocalAgendamento()), QueryCustom.QueryCustomParameter.IN, param.getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getCodigoPaciente()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), param.getPeriodo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getCodigo()), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), AgendaGradeAtendimentoHorario.STATUS_AGENDADO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimentoPrincipal()), BuilderQueryCustom.QueryParameter.IS_NULL))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QuerySorter.CRESCENTE));
        }
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getVersion()))
                .addProperty(path(proxy.getDataAgendamento()))
                .addProperty(path(proxy.getQuantidadeVagasOcupadas()))
                .addProperty(path(proxy.getUsuarioCadsus().getNome()))
                .addProperty(path(proxy.getUsuarioCadsus().getCodigo()))
                .addProperty(path(proxy.getUsuarioCadsus().getApelido()))
                .addProperty(path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()))
                .addProperty(path(proxy.getUsuarioCadsus().getCpf()))
                .addProperty(path(proxy.getNomePaciente()))
                .addProperty(path(proxy.getProfissional().getCodigo()))
                .addProperty(path(proxy.getProfissional().getNome()))
                .addProperty(path(proxy.getLocalAgendamento().getCodigo()))
                .addProperty(path(proxy.getLocalAgendamento().getDescricao()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getVersion()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getQuantidadeAtendimento()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getVersion()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getVersion()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getTipoProcedimento().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getTipoProcedimento().getVersion()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getVersion()))
                .addProperty(path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getTipoAtendimento()))
                .addProperty(path(proxy.getTipoProcedimento().getCodigo()))
                .addProperty(path(proxy.getTipoProcedimento().getVersion()))
                .addProperty(path(proxy.getTipoProcedimento().getDescricao()))
                .addProperty(path(proxy.getAgendaGradeHorario().getCodigo()))
                .addProperty(path(proxy.getAgendaGradeHorario().getVersion()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getCodigo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getVersion()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getCodigo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getTipoProcedimento().getCodigo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getTipoProcedimento().getTipoProcedimentoClassificacao().getCodigo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getTipoFila()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getNumeroSolicitacao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getCodigoPaciente()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoProcedimento()), param.getTipoProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getTipoProcedimento().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getTipoProcedimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getProfissional()), param.getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLocalAgendamento()), QueryCustom.QueryCustomParameter.IN, param.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getCodigo()), BuilderQueryCustom.QueryParameter.IGUAL, param.getCodigoPaciente()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataAgendamento()), param.getPeriodo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimento().getCodigo()), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), AgendaGradeAtendimentoHorario.STATUS_AGENDADO))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAgendaGradeAtendimentoPrincipal()), BuilderQueryCustom.QueryParameter.IS_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataAgendamento()), BuilderQueryCustom.QuerySorter.CRESCENTE));

    }


    private SolicitacaoAgendamento carregarSolicitacaoAgendamento() {

        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);

        solicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), param.getNumeroSolicitacao()))
                .start()
                .getVO();

        return solicitacaoAgendamento;
    }

    private void carregarDadosDaSolicitacao() {
        if (solicitacaoAgendamento != null) {
            param.setTipoProcedimento(solicitacaoAgendamento.getTipoProcedimento());
            param.setEmpresa(solicitacaoAgendamento.getUnidadeExecutante());
        }
    }

    private boolean getAguardaProcedimento(TipoProcedimento tipoProcedimento) {
        TipoProcedimento tipoProcedimentoProxy = on(TipoProcedimento.class);

        return LoadManager.getInstance(TipoProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(tipoProcedimentoProxy.getCodigo()), tipoProcedimento.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(tipoProcedimentoProxy.getFlagAguardaProcedimento()), RepositoryComponentDefault.SIM_LONG))
                .start()
                .exists();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("manutencaoAgendamentos");
    }
}
