package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum LocalProvavelInfeccaoEnum implements IEnum<SimNaoEnum> {
    UNIDADE_HEMOTERAPIA(1L, "Unidade de Hemoterapia"),
    DOMICILIO(2L, "Domicílio"),
    LABORATORIO(3L, "Laboratório"),
    OUTRO(4L, "Outro"),
    IGNORADO(9L, "Ignorado"),
    ;

    private Long value;
    private String descricao;

    LocalProvavelInfeccaoEnum(long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }


    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static LocalProvavelInfeccaoEnum valueOf(Long value) {
        for (LocalProvavelInfeccaoEnum v : LocalProvavelInfeccaoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
