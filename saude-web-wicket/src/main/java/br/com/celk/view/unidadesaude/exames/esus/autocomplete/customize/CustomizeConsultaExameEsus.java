package br.com.celk.view.unidadesaude.exames.esus.autocomplete.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.esus.ExameEsus;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaExameEsus extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("codigoEsus"), new QueryCustom.QueryCustomParameter(ExameEsus.PROP_CODIGO_ESUS, QueryParameter.IGUAL));
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(ExameEsus.PROP_DESCRICAO_EXAME_ESUS, QueryParameter.CONSULTA_LIKED));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), ExameEsus.PROP_CODIGO);
        properties.put(BundleManager.getString("codigoEsus"), ExameEsus.PROP_CODIGO_ESUS);
        properties.put(BundleManager.getString("descricao"), ExameEsus.PROP_DESCRICAO_EXAME_ESUS);
    }

    @Override
    public Class getClassConsulta() {
        return ExameEsus.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(ExameEsus.class).getProperties();
    }
}
