package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * <AUTHOR>
 */
public abstract class DlgLancamentosConfirmadosExames extends Window {

    private PnlLancamentosConfirmadosExames pnlLancamentosConfirmadosExames;

    public DlgLancamentosConfirmadosExames(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(400);
        setInitialWidth(1000);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("lancamentosConfirmados");
            }
        });

        setContent(pnlLancamentosConfirmadosExames = new PnlLancamentosConfirmadosExames(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                DlgLancamentosConfirmadosExames.this.onFechar(target);
            }

            @Override
            public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                DlgLancamentosConfirmadosExames.this.onReverter(target, itemContaPaciente);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                onFechar(target);
                return true;
            }
        });
    }

    public List<ItemContaPaciente> getListaItens() {
        return pnlLancamentosConfirmadosExames.getListaItens();
    }

    public void setItemLista(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        pnlLancamentosConfirmadosExames.setItemContaPaciente(target, itemContaPaciente);
    }

    public void setListItem(AjaxRequestTarget target, List<ItemContaPaciente> lista) {
        pnlLancamentosConfirmadosExames.setListItemContaPaciente(target, lista);
    }

    public void onFechar(AjaxRequestTarget target) {
        close(target);
    }

    public abstract void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
}
