package br.com.celk.view.atividadegrupo.localatividadegrupo;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.unidadesaude.atividadegrupo.dialog.DlgImpressaoBoletimAtividadeGrupo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.Empresa;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaLocalAtividadeGrupoPage extends ConsultaPage<AtividadeGrupo, List<BuilderQueryCustom.QueryParameter>> {

    private List<Empresa> unidade;
    private String descricao;
    private Long codigo;

    private DlgImpressaoBoletimAtividadeGrupo dlgImpressaoBoletimLocalAtividadeGrupo;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    public ConsultaLocalAtividadeGrupoPage() {
    }

    @Override
    public void initForm(Form form) {
        boolean permissaoEmpresa = isActionPermitted(Permissions.EMPRESA);
        form.setDefaultModel(new CompoundPropertyModel(this));

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("unidade");
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!permissaoEmpresa)
                .setMultiplaSelecao(true);
        form.add(autoCompleteConsultaEmpresa);
        form.add(new InputField("descricao"));
        
        if (!permissaoEmpresa) {
            try {
                carregarUnidades();
            } catch (SGKException ex) {
                error(ex.getMessage());
            }
        }

        setExibeExpandir(true);
    }

    private void carregarUnidades() throws DAOException, ValidacaoException {
        List<Long> empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresas))
                    .start().getList();
            unidade = empresaList;
        }
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        LocalAtividadeGrupo proxy = on(LocalAtividadeGrupo.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("empresa"), proxy.getEmpresa().getDescricao(), proxy.getEmpresa().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("local"), proxy.getDescricao()));

        return columns;
    }

    private CustomColumn<LocalAtividadeGrupo> getCustomColumn() {
        return new CustomColumn<LocalAtividadeGrupo>() {

            @Override
            public Component getComponent(String componentId, final LocalAtividadeGrupo rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalAtividadeGrupoPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalAtividadeGrupoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LocalAtividadeGrupo.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LocalAtividadeGrupo.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LocalAtividadeGrupo.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LocalAtividadeGrupo.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(LocalAtividadeGrupo.PROP_EMPRESA, BuilderQueryCustom.QueryParameter.IN, unidade));
        parameters.add(new QueryCustom.QueryCustomParameter(LocalAtividadeGrupo.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroLocalAtividadeGrupoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLocalAtividadeGrupo");
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LocalAtividadeGrupo>() {

            @Override
            public void customizeColumn(final LocalAtividadeGrupo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LocalAtividadeGrupo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LocalAtividadeGrupo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalAtividadeGrupoPage(rowObject));
                    }
                }).setEnabled(true);

                addAction(ActionType.REMOVER, rowObject, new IModelAction<LocalAtividadeGrupo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LocalAtividadeGrupo modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().update(target);
                    }
                }).setEnabled(true);

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LocalAtividadeGrupo>() {

                    @Override
                    public void action(AjaxRequestTarget target, LocalAtividadeGrupo modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLocalAtividadeGrupoPage(rowObject, true));
                    }
                }).setEnabled(true);
            }
        };
    }
}
