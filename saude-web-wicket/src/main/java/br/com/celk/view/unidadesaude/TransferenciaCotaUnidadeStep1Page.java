package br.com.celk.view.unidadesaude;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class TransferenciaCotaUnidadeStep1Page extends BasePage{

    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;
    private TipoExame tipoExame;
    private Empresa estabelecimento;
    private ExameUnidadeCompetencia exameUnidadeCompetenciaOrigem;
    private ArrayList<ExameUnidadeCompetencia> lstExameUnidadeCompetencias;
    private Date competenciaAtual;

    public TransferenciaCotaUnidadeStep1Page() {
        try {
            init();
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    public void init() throws DAOException, ValidacaoException{
        Form form = new Form("form",new CompoundPropertyModel(this));
        
        form.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame("tipoExame",true));
        form.add(new AutoCompleteConsultaEmpresa("estabelecimento",true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_SECRETARIA_SAUDE)));
        
        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarExameUnidadeCompetencias(tipoExame, estabelecimento);
                avancar(target);
            }
        });
        
        add(form);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("transferenciaCotaEstabelecimento");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoExame.getTxtDescricao().getTextField();
    }
    
    private void avancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Page page = new TransferenciaCotaUnidadeStep2Page(tipoExame, estabelecimento,lstExameUnidadeCompetencias,exameUnidadeCompetenciaOrigem,competenciaAtual);
        setResponsePage(page);
    }

    
    private void carregarExameUnidadeCompetencias(TipoExame _tipoExame, Empresa _unidade) throws DAOException, ValidacaoException {
        int diaInicioCompetencia = 0;
        try {
            diaInicioCompetencia = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage());
        }
        this.competenciaAtual = Data.competenciaData(diaInicioCompetencia, Data.getDataAtual());
        List<ExameUnidadeCompetencia> list = LoadManager.getInstance(ExameUnidadeCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_TIPO_EXAME, _tipoExame))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_DATA_COMPETENCIA, this.competenciaAtual))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameUnidadeCompetencia.PROP_EMPRESA,Empresa.PROP_REFERENCIA)))
                .start().getList();

        lstExameUnidadeCompetencias = new ArrayList<ExameUnidadeCompetencia>();
        for (ExameUnidadeCompetencia exameUnidadeCompetencia : list) {
            if (exameUnidadeCompetencia.getEmpresa().equals(_unidade)) {
                exameUnidadeCompetenciaOrigem = exameUnidadeCompetencia;
            } else {
                lstExameUnidadeCompetencias.add(exameUnidadeCompetencia);
            }
        }
        if (exameUnidadeCompetenciaOrigem == null) {
            throw new ValidacaoException(BundleManager.getString("msgUnidadeXSemCotaCadastradaParaCopetenciaAtual", _unidade.getDescricaoFormatado()));        
        }
    }
}
