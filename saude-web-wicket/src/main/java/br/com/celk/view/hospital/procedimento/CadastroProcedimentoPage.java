package br.com.celk.view.hospital.procedimento;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.prontuario.tipotabelaprocedimento.autocomplete.AutoCompleteConsultaTipoTabelaProcedimento;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class CadastroProcedimentoPage extends CadastroPage<Procedimento> {

    public CadastroProcedimentoPage() {
    }

    public CadastroProcedimentoPage(Procedimento object) {
        this(object, false);
    }

    public CadastroProcedimentoPage(Procedimento object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form form) {
        form.add(new InputField(Procedimento.PROP_REFERENCIA).setRequired(false));
        form.add(new RequiredUpperField(Procedimento.PROP_DESCRICAO).setRequired(true));
        form.add(new AutoCompleteConsultaTipoTabelaProcedimento(Procedimento.PROP_TIPO_TABELA_PROCEDIMENTO, true));
    }

    @Override
    public Class<Procedimento> getReferenceClass() {
        return Procedimento.class;
    }

    @Override
    public Class getResponsePage() {
        return br.com.celk.view.hospital.procedimento.ConsultaProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProcedimentos");
    }

    @Override
    public Object salvar(Procedimento object) throws DAOException, ValidacaoException {
        object.setFlagFaturavel(RepositoryComponentDefault.NAO);
        return BOFactoryWicket.getBO(HospitalFacade.class).gerarProcedimentoCompetencia(object);
    }

}
