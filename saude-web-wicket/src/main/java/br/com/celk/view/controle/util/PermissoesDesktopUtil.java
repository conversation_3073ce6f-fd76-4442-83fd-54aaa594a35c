package br.com.celk.view.controle.util;

import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.controle.Programa;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PermissoesDesktopUtil {

    /**
     * Retorna as Permissões Desktop do Grupo
     * 
     * @param codigoGrupo
     * @return Lista de programas
     */
    public static List<Programa> getGroupPermissions(Long codigoGrupo){
        try {
            return BOFactoryWicket.getBO(UsuarioFacade.class).getPermissoesGrupoDesktop(codigoGrupo);
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(),ex);
        }
        return null;
    }
}
