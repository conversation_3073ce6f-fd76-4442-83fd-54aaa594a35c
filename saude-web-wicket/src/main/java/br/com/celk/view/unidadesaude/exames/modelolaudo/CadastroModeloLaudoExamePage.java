package br.com.celk.view.unidadesaude.exames.modelolaudo;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.ModeloLaudoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroModeloLaudoExamePage extends CadastroPage<ModeloLaudoExame> {

    private InputField<String> txtReferencia;
    private AutoCompleteConsultaExameProcedimento autoCompleteConsultaExameProcedimento;
    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;

    public CadastroModeloLaudoExamePage(PageParameters parameters) {
        super(parameters);
    }

    public CadastroModeloLaudoExamePage(ModeloLaudoExame object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroModeloLaudoExamePage(ModeloLaudoExame object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroModeloLaudoExamePage(ModeloLaudoExame object) {
        this(object, false);
    }

    @Override
    public void init(Form form) {
        ModeloLaudoExame proxy = on(ModeloLaudoExame.class);

        form.add(this.txtReferencia = new InputField(path(proxy.getReferencia())));
        form.add(new RequiredInputArea(path(proxy.getDescricao())));
        form.add(autoCompleteConsultaExameProcedimento = new AutoCompleteConsultaExameProcedimento(path(proxy.getExameProcedimento())));
        autoCompleteConsultaExameProcedimento.add(new ConsultaListener<ExameProcedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ExameProcedimento object) {
                if(object != null){
                    if(object.getTipoExame() != null){
                        autoCompleteConsultaTipoExame.limpar(target);
                        autoCompleteConsultaTipoExame.setComponentValue(object.getTipoExame());
                        autoCompleteConsultaTipoExame.setEnabled(false);
                    }
                }
            }
        });
        autoCompleteConsultaExameProcedimento.add(new RemoveListener<ExameProcedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ExameProcedimento object) {
                autoCompleteConsultaTipoExame.limpar(target);
                autoCompleteConsultaTipoExame.setEnabled(true);
            }
        });
        form.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame(path(proxy.getTipoExame())));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<ModeloLaudoExame> getReferenceClass() {
        return ModeloLaudoExame.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaModeloLaudoExamePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroModeloLaudoExame");
    }
}
