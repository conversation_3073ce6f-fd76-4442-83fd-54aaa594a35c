package br.com.celk.view.hospital.aih.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.hospital.aih.ConsultaAltaTransferenciaPacientePage;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;
/**
 * <AUTHOR>
 */
public abstract class DlgConfirmarAlta extends Window {
    private PnlConfirmarAlta pnlConfirmarAlta;

    public DlgConfirmarAlta(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("altaAihPaciente");
            }
        });

        setInitialWidth(750);
        setInitialHeight(355);

        setResizable(false);

        pnlConfirmarAlta = new PnlConfirmarAlta(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Aih aih) throws ValidacaoException, DAOException {
                geraNumeracaoAih(target, aih);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        };
        setContent(pnlConfirmarAlta);

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    private void geraNumeracaoAih(AjaxRequestTarget target, Aih aih) throws DAOException, ValidacaoException {
        DlgConfirmacaoAltaAih dlgMsgConfirmarAlta = new DlgConfirmacaoAltaAih(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) {
                Page page = new ConsultaAltaTransferenciaPacientePage();
                setResponsePage(page);
                getSession().getFeedbackMessages().info(page, BundleManager.getString("msgAltaAIHRealizadaSucesso"));
            }
        };
        addModal(target, dlgMsgConfirmarAlta);

        Long numeracaoAih = BOFactory.getBO(AgendamentoFacade.class).confirmarAltaTransferenciaAih(aih);

        dlgMsgConfirmarAlta.show(target, numeracaoAih);
    }
    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, Aih aih) {
        show(target);
        pnlConfirmarAlta.setAih(target, aih);
    }

    private void fechar(AjaxRequestTarget target) {
        pnlConfirmarAlta.limpar(target);
        close(target);
    }
}
