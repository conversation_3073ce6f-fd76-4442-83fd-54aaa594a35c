package br.com.celk.view.agenda.encaminhamento.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.encaminhamento.autocomplete.AutoCompleteConsultaTipoEncaminhamento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioGraficoEncaminhamentoSituacaoDTOParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioGraficoEncaminhamentoSituacaoPage extends RelatorioPage<RelatorioGraficoEncaminhamentoSituacaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTipoEncaminhamento("tipoEncaminhamento")
                .setIncluirInativos(true)
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioGraficoEncaminhamentoSituacaoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoDado", RelatorioGraficoEncaminhamentoSituacaoDTOParam.TipoDado.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioGraficoEncaminhamentoSituacaoDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioGraficoEncaminhamentoSituacaoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioGraficoEncaminhamentoSituacao(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("encaminhamentosSituacao");
    }
}
