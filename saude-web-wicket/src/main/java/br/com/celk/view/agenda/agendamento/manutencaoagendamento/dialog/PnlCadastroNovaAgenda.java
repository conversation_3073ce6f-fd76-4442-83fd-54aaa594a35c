package br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.tipoatendimentoagenda.autocomplete.AutoCompleteConsultaTipoAtendimentoAgenda;
import br.com.ksisolucoes.agendamento.exame.dto.CadastroNovaAgendaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.Date;
import java.util.List;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCadastroNovaAgenda extends Panel{
    
    private LoadableObjectModel<CadastroNovaAgendaDTO> model;
    private List<AgendaGradeAtendimentoHorario> agahList;
    
    private DateChooser dchDataAgenda;
    private InputField txtHoraInicio;
    private InputField txtTempoMedio;
    private AutoCompleteConsultaTipoAtendimentoAgenda autoCompleteConsultaTipoAtendimentoAgenda;
    private DropDown dropDownReabrirAgenda;
    private InputArea txaMotivo;
    
    public PnlCadastroNovaAgenda(String id){
        super(id);
        init();
    }

    private void init() {
        Form form = new Form<CadastroNovaAgendaDTO>("form", new CompoundPropertyModel(model = new LoadableObjectModel<CadastroNovaAgendaDTO>(CadastroNovaAgendaDTO.class, true)));
        
        CadastroNovaAgendaDTO proxy = on(CadastroNovaAgendaDTO.class);
                
        form.add(dchDataAgenda = new DateChooser(path(proxy.getDataAgenda())));
        form.add(txtHoraInicio = new HoraMinutoField(path(proxy.getHoraInicio())));
        form.add(autoCompleteConsultaTipoAtendimentoAgenda = new AutoCompleteConsultaTipoAtendimentoAgenda(path(proxy.getTipoAtendimentoAgenda())));
        form.add(txtTempoMedio = new InputField<Long>(path(proxy.getTempoMedio())));
        
        form.add(dropDownReabrirAgenda = DropDownUtil.getSimNaoLongDropDown(path(proxy.getReabrirAgenda()), true, false));
        form.add(txaMotivo = new InputArea(path(proxy.getMotivo())));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    model.getObject().setAgendaGradeAtendimentoHorarioList(agahList);
                    
                    onConfirmar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(dchDataAgenda.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_agenda"));
            }
            if (Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().after(dchDataAgenda.getComponentValue())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_data_inferior_data_atual"));
            }
            if(txtHoraInicio.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_hora_inicio"));
            }
            if (Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().equals(dchDataAgenda.getComponentValue()) 
                    && DataUtil.compareHour((Date) txtHoraInicio.getComponentValue(), DataUtil.getDataAtual()) < 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_hora_inferior_hora_atual"));
            }
            if(autoCompleteConsultaTipoAtendimentoAgenda.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_tipo_atendimento"));
            }
            if(txtTempoMedio.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_o_tempo_medio"));
            }
            if(dropDownReabrirAgenda.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_campo_reabrir_agenda"));
            }
            if(txaMotivo.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, CadastroNovaAgendaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setAgendaGradeAtendimentoHorarioList(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agahList){
        this.agahList = agahList;
        
        dchDataAgenda.limpar(target);
        txtHoraInicio.limpar(target);
        txtTempoMedio.limpar(target);
        autoCompleteConsultaTipoAtendimentoAgenda.limpar(target);
        dropDownReabrirAgenda.limpar(target);
        txaMotivo.limpar(target);
    }
}