package br.com.celk.view.consorcio.pagamentoguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.component.table.column.panel.DetalhesActionColumnPanel;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.DlgConsultarGuia;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.PnlVizualizaSaldoConsorciado;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioGuiaProcedimentoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaGuiasDTOParam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import br.com.ksisolucoes.vo.consorcio.util.PrestadorUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.*;


/**
 *
 * <AUTHOR>
 */
@Private

public class PagamentoGuiaProcedimentoStep2Page extends BasePage{

    private static final String INTEGRACAO_AMBULATORIAL_CONSORCIO = "Integraçao Consórcio-Ambulatorial";
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private MultiSelectionTableOld<ConsorcioGuiaProcedimentoDTO> tblGuiasUtilizadas;
    private Table tblGuiasSelecionadas;
    private InputField txtTotal;
    private InputField txtNumeroGuias;
    private DlgConsultarGuia dlgConsultarGuia;

    private Empresa empresaConsorcio;
    private Empresa empresaPrestador;
    private TipoConta tipoConta;
    private Empresa consorciado;
    private DatePeriod periodo;
    private QueryConsultaGuiasDTOParam.TipoData tipoData;
    private Long nGuia;
    private String nReferencia;
    private List<ConsorcioGuiaProcedimentoDTO> guiasUtilizadas = new ArrayList<ConsorcioGuiaProcedimentoDTO>();
    private List<ConsorcioGuiaProcedimentoDTO> guiasSelecionadas = new ArrayList<ConsorcioGuiaProcedimentoDTO>();
    private DisabledDoubleField txtTotalImposto;
    private PnlVizualizaSaldoConsorciado pnlVizualizaSaldoConsorciado;
    private boolean efetuarBaixaSaldoNoPagamentoDaGuia = false;
    private boolean integracaoAmbulatorialConsorcio = false;

    private int quantidadeMaximaProcessamento = 1500;

    @Deprecated
    public PagamentoGuiaProcedimentoStep2Page() {}
    
    public PagamentoGuiaProcedimentoStep2Page(Empresa empresaConsorcio, Empresa empresaPrestador) {
        this.empresaConsorcio = empresaConsorcio;
        this.empresaPrestador = empresaPrestador;
        efetuarBaixaSaldoNoPagamentoDaGuia = isEfetuarBaixaSaldoNoPagamentoDaGuia();
        integracaoAmbulatorialConsorcio = isIntegraAmbulatorialConsorcio();
        init();
    }

    public PagamentoGuiaProcedimentoStep2Page(Empresa empresaConsorcio, Empresa empresaPrestador, TipoConta tipoConta) {
        this.empresaConsorcio = empresaConsorcio;
        this.empresaPrestador = empresaPrestador;
        this.tipoConta = tipoConta;
        efetuarBaixaSaldoNoPagamentoDaGuia = isEfetuarBaixaSaldoNoPagamentoDaGuia();
        integracaoAmbulatorialConsorcio = isIntegraAmbulatorialConsorcio();
        init();
    }
    
    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));

        List<Long> tiposEmpresa = new ArrayList<>();
        tiposEmpresa.add(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO);
        if(integracaoAmbulatorialConsorcio) {
            tiposEmpresa.add(Empresa.TIPO_ESTABELECIMENTO_UNIDADE);
        }
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(tiposEmpresa));
        autoCompleteConsultaEmpresa.setRequired(efetuarBaixaSaldoNoPagamentoDaGuia);
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                if (efetuarBaixaSaldoNoPagamentoDaGuia) {
                    pnlVizualizaSaldoConsorciado.carregarDados(empresa);
                    target.add(pnlVizualizaSaldoConsorciado);
                }
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                if (efetuarBaixaSaldoNoPagamentoDaGuia) {
                    pnlVizualizaSaldoConsorciado.limparDados();
                    target.add(pnlVizualizaSaldoConsorciado);
                }
            }
        });
        form.add(new PnlDatePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("tipoData", QueryConsultaGuiasDTOParam.TipoData.values()));
        form.add(new InputField("nGuia"));
        form.add(new InputField("nReferencia"));
        form.add(new AbstractAjaxButton("btnPesquisar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                pesquisar(target);
            }
        });
        form.add(tblGuiasUtilizadas = new MultiSelectionTableOld("tblGuiasUtilizadas", getColumnsGuiasUtilizadas(), getCollectionProviderGuiasUtilizadas()));
        tblGuiasUtilizadas.populate();
        tblGuiasUtilizadas.setScrollY("300px");
        tblGuiasUtilizadas.setScrollCollapse(false);
        
        form.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        form.add(tblGuiasSelecionadas = new Table("tblGuiasSelecionadas", getColumnsGuiasSelecionadas(), getCollectionProviderGuiasSelecionadas()));
        tblGuiasSelecionadas.populate();
        tblGuiasSelecionadas.setScrollY("300px");
        tblGuiasSelecionadas.setScrollCollapse(false);
        
        form.add(txtTotal = new DisabledDoubleField("valorTotal", new LoadableDetachableModel<Double>() {

            @Override
            protected Double load() {
                return calcularTotal();
            }
        }));

        form.add(txtTotalImposto = new DisabledDoubleField("totalImpostos", new LoadableDetachableModel<Double>() {
            @Override
            protected Double load() {
                return calcularTotalImposto();
            }
        }));
        form.add(txtNumeroGuias = new DisabledInputField("nGuias", new LoadableDetachableModel<Object>() {

            @Override
            protected Object load() {
                return guiasSelecionadas.size();
            }
        }));

        form.add(pnlVizualizaSaldoConsorciado = new PnlVizualizaSaldoConsorciado("saldoConsorciado"));
        pnlVizualizaSaldoConsorciado.setOutputMarkupId(true);
        pnlVizualizaSaldoConsorciado.setOutputMarkupPlaceholderTag(true);
        pnlVizualizaSaldoConsorciado.setVisible(efetuarBaixaSaldoNoPagamentoDaGuia);

        form.add(new VoltarButton("btnVoltar"));
        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar(target);
            }

        });

        add(form);
        
        addModal(dlgConsultarGuia = new DlgConsultarGuia(newModalId()));
    }

    private boolean isEfetuarBaixaSaldoNoPagamentoDaGuia() {
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO)
                    .getParametro("efetuarBaixaDoSaldoNoPagametoDaGuia"));
        } catch (DAOException e) {
            return false;
        }
    }

    private boolean isIntegraAmbulatorialConsorcio() {
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO)
                    .getParametro(INTEGRACAO_AMBULATORIAL_CONSORCIO));
        } catch (DAOException e) {
            return false;
        }
    }
    
    private List<IColumn> getColumnsGuiasUtilizadas(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimentoDTO.class);
        
        columns.add(getCustomColumnGuiasUtilizadas());
        columns.add(columnFactory.createColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valor"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_VALOR_TOTAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cadastro"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("agendamento"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("utilizacao"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_APLICACAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_REFERENCIA)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumnGuiasUtilizadas(){
        return new CustomColumn<ConsorcioGuiaProcedimentoDTO>() {

            @Override
            public Component getComponent(String componentId, final ConsorcioGuiaProcedimentoDTO rowObject) {
                return new DetalhesActionColumnPanel(componentId) {

                    @Override
                    public void onDetalhar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        consultarGuia(target, rowObject);
                    }

                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProviderGuiasUtilizadas(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return guiasUtilizadas;
            }
        };
    }
    
    private List<IColumn> getColumnsGuiasSelecionadas(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimentoDTO.class);
        
        columns.add(getCustomColumnGuiasSelecionadas());
        columns.add(columnFactory.createColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("valor"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_VALOR_TOTAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cadastro"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("agendamento"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_AGENDAMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("utilizacao"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_DATA_APLICACAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(ConsorcioGuiaProcedimentoDTO.PROP_CONSORCIO_GUIA_PROCEDIMENTO, ConsorcioGuiaProcedimento.PROP_REFERENCIA)));

        return columns;
    }
    
    private CustomColumn getCustomColumnGuiasSelecionadas(){
        return new CustomColumn<ConsorcioGuiaProcedimentoDTO>() {

            @Override
            public Component getComponent(String componentId, final ConsorcioGuiaProcedimentoDTO rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerGuiaSelecionada(target, rowObject);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        consultarGuia(target, rowObject);
                    }

                    @Override
                    public boolean isEditarVisible() {
                        return false;
                    }
                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProviderGuiasSelecionadas(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return guiasSelecionadas;
            }
        };
    }
    
    private void pesquisar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        QueryConsultaGuiasDTOParam param = new QueryConsultaGuiasDTOParam();
        param.setConsorcio(empresaConsorcio);
        param.setPrestador(empresaPrestador);
        param.setConsorciado(consorciado);
        param.setNumeroGuia(nGuia);
        param.setPeriodo(periodo);
        param.setTipoData(tipoData);
        param.setTipoConta(tipoConta);
        if(Objects.nonNull(nReferencia)) {
            param.setNumeroReferencia(nReferencia);
        }
        guiasUtilizadas = BOFactoryWicket.getBO(ConsorcioFacade.class).consultarGuiasUtilizadas(param);
        validarQuantidadeGuiasLimite();
        guiasUtilizadas = (List<ConsorcioGuiaProcedimentoDTO>) CollectionUtils.subtract(guiasUtilizadas, guiasSelecionadas);
        tblGuiasUtilizadas.update(target);
    }

    private void validarQuantidadeGuiasLimite() throws ValidacaoException {
        if (guiasUtilizadas.size() > quantidadeMaximaProcessamento){
            guiasUtilizadas = new ArrayList<>();
            throw new ValidacaoException(BundleManager.getString("msgQuantidadeMaximaGuiasExcedida", quantidadeMaximaProcessamento));
        }
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        List<ConsorcioGuiaProcedimentoDTO> selectedObjects = tblGuiasUtilizadas.getSelectedObjects();
        guiasSelecionadas.addAll(selectedObjects);
        guiasUtilizadas = (List<ConsorcioGuiaProcedimentoDTO>) CollectionUtils.subtract(guiasUtilizadas, guiasSelecionadas);
        tblGuiasUtilizadas.clearSelection(target);
        tblGuiasSelecionadas.update(target);
        recalcularTotais(target);
    }
    
    private Double calcularTotal(){
        Dinheiro total = Dinheiro.ZERO;
        for (ConsorcioGuiaProcedimentoDTO consorcioGuiaProcedimentoDTO : guiasSelecionadas) {
            total = total.somar(consorcioGuiaProcedimentoDTO.getValorTotal());
        }
        return total.doubleValue();
    }

    private Double calcularTotalImposto(){
        Dinheiro totalImposto = Dinheiro.ZERO;
        PrestadorUtil prestadorUtil = new PrestadorUtil(empresaPrestador);
        Double valorTotalBruto = calcularTotal();

        Double valorImpostoRenda = 0D;
        Double valorInss = 0D;
        Double valorIss = 0D;
        try {
            valorInss = prestadorUtil.calcularValorINSS(valorTotalBruto);
            valorIss = prestadorUtil.calcularValorISS(valorTotalBruto);

            Double valorTotalLiquido = new Dinheiro(valorTotalBruto).subtrair(valorInss).subtrair(valorIss).round().doubleValue();
            valorImpostoRenda = prestadorUtil.calcularValorIR(valorTotalLiquido);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        totalImposto = new Dinheiro(valorImpostoRenda).somar(valorInss).somar(valorIss).round();
        return totalImposto.doubleValue();
    }

    private void removerGuiaSelecionada(AjaxRequestTarget target, ConsorcioGuiaProcedimentoDTO dto){
        guiasSelecionadas.remove(dto);
        tblGuiasSelecionadas.update(target);
        recalcularTotais(target);
    }
    
    private void consultarGuia(AjaxRequestTarget target, ConsorcioGuiaProcedimentoDTO dto){
        dlgConsultarGuia.setModelObject(target, dto);
        dlgConsultarGuia.show(target);
    }
    
    private void recalcularTotais(AjaxRequestTarget target){
        target.add(txtTotal);
        target.add(txtNumeroGuias);
        target.add(txtTotalImposto);
    }
    
    private void avancar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        if (guiasSelecionadas.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("selecionePeloMenosUmaGuia"));
        }
        setResponsePage(new PagamentoGuiaProcedimentoConfirmacaoPage(empresaPrestador, guiasSelecionadas));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("pagamentoGuiasUtilizadas");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
