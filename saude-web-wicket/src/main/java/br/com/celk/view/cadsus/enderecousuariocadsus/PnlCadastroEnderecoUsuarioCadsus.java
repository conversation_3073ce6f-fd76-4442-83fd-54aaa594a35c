package br.com.celk.view.cadsus.enderecousuariocadsus;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.autocomplete.AutoCompleteBairro;
import br.com.celk.component.autocomplete.RequiredAutoCompleteBairro;
import br.com.celk.component.cepField.CepWsField;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.panel.PnlCadastro;
import br.com.celk.util.Util;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.tipologradourocadsus.autocomplete.AutoCompleteConsultaTipoLogradouroCadsus;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.CidadeCep;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public class PnlCadastroEnderecoUsuarioCadsus extends PnlCadastro<EnderecoUsuarioCadsus> {

    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private AutoCompleteBairro autoCompleteBairro;
    private RequiredInputField txtLogradouro;
    private RequiredInputField txtNumero;
    private CheckBoxLongValue cbxFlagEnderecoSN;
    private Long enderecoSN;
    private InputField txtComplemento;
    private InputField txtQuadra;
    private InputField txtLote;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaTipoLogradouroCadsus autoCompleteConsultaTipoLogradouroCadsus;

    private CepWsField cepWsField;

    public PnlCadastroEnderecoUsuarioCadsus(String id) {
        super(id);
    }

    @Override
    public void init(Form<EnderecoUsuarioCadsus> form) {
        form.add(cepWsField = new CepWsField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_CEP)) {
            @Override
            public void load(AjaxRequestTarget target, CepWSDTO cepWSDTO) {
                autoCompleteConsultaTipoLogradouroCadsus.setComponentValue(cepWSDTO.getTipoLogradouro());
                autoCompleteConsultaCidade.setComponentValue(cepWSDTO.getCidade());
                txtLogradouro.setComponentValue(cepWSDTO.getLogradouro());
                autoCompleteBairro.setComponentValue(cepWSDTO.getBairro());
                autoCompleteBairro.setCidade(cepWSDTO.getCidade());
                txtComplemento.setComponentValue(cepWSDTO.getComplemento());

                target.add(autoCompleteConsultaTipoLogradouroCadsus);
                target.add(autoCompleteConsultaCidade);
                target.add(txtLogradouro);
                target.add(autoCompleteBairro);
                target.add(txtComplemento);
            }

            @Override
            public void unload(AjaxRequestTarget target) {
                autoCompleteConsultaTipoLogradouroCadsus.limpar(target);
                autoCompleteConsultaCidade.limpar(target);
                txtLogradouro.limpar(target);
                autoCompleteBairro.limpar(target);
                txtComplemento.limpar(target);
                txtQuadra.limpar(target);
                txtLote.limpar(target);
            }
        });

        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_CIDADE), true));
        form.add(autoCompleteBairro = new RequiredAutoCompleteBairro(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_BAIRRO)));
        form.add(autoCompleteConsultaTipoLogradouroCadsus = new AutoCompleteConsultaTipoLogradouroCadsus(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_TIPO_LOGRADOURO), true));
        form.add(txtLogradouro = new RequiredInputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_LOGRADOURO)));
        form.add(txtNumero = (RequiredInputField) new RequiredInputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_NUMERO_LOGRADOURO)).setLabel(new Model<String>(bundle("numero"))));
        form.add(txtComplemento = new InputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_COMPLEMENTO_LOGRADOURO)));
        form.add(txtQuadra = new InputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_QUADRA)));
        form.add(txtLote = new InputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_LOTE)));
        form.add(new InputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_TELEFONE)));
        form.add(new InputField(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_PONTO_REFERENCIA)));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(VOUtils.montarPath(EnderecoUsuarioCadsus.PROP_EMPRESA)));

        txtNumero.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONKEYUP) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                String novaString = Util.mantemSomenteNumeros(txtNumero.getValue());
                if (RepositoryComponentDefault.NAO_LONG.equals(enderecoSN) && !txtNumero.getValue().equals(novaString)){
                    txtNumero.setComponentValue(novaString);
                    target.add(txtNumero);
                }
            }
        });
        cbxFlagEnderecoSN = new CheckBoxLongValue("enderecoSN", RepositoryComponentDefault.SIM_LONG, new PropertyModel(this, "enderecoSN"));
        form.add(cbxFlagEnderecoSN);
        cbxFlagEnderecoSN.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (RepositoryComponentDefault.SIM_LONG.equals(enderecoSN)) {
                    txtNumero.setEnabled(false);
                    txtNumero.limpar(art);
                    txtNumero.setComponentValue("SN");

                } else {
                    txtNumero.limpar(art);
                    txtNumero.setEnabled(true);
                    txtNumero.setComponentValue("");
                }
                art.add(txtNumero);
            }
        });
        enderecoSN = RepositoryComponentDefault.NAO_LONG;

        autoCompleteConsultaCidade.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                autoCompleteBairro.limpar(target);
                autoCompleteBairro.setCidade(cidade);
            }
        });

        autoCompleteBairro.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                String bairro = (String) autoCompleteBairro.getComponentValue();
                Cidade cidade = autoCompleteBairro.getCidade();
                if (cidade != null && bairro != null && !bairro.isEmpty()) {
                    setEmpresa(bairro, cidade, target);
                }
            }
        });

        autoCompleteConsultaEmpresa.addAjaxUpdateValue();
    }

    private void setEmpresa(String bairroDescricao, Cidade cidade, AjaxRequestTarget target) {
        Bairro bairro = LoadManager.getInstance(Bairro.class)
                .addProperty(VOUtils.montarPath(Bairro.PROP_DESCRICAO))
                .addProperties(new HQLProperties(Empresa.class, Bairro.PROP_EMPRESA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_DESCRICAO), bairroDescricao))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_CIDADE), cidade))
                .start().getVO();

        if (bairro != null && bairro.getEmpresa() != null) {
            autoCompleteConsultaEmpresa.setComponentValue(target, bairro.getEmpresa());
            target.add(autoCompleteConsultaEmpresa);
        }
    }

    @Override
    public void initInstance(EnderecoUsuarioCadsus object) {
        object.setCidade(ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCidade());
        if (object.getCidade() != null) {
            autoCompleteBairro.setCidade(object.getCidade());
            CidadeCep cidadeCep = LoadManager.getInstance(CidadeCep.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(CidadeCep.PROP_CIDADE, object.getCidade()))
                    .start().getVO();
            if (cidadeCep != null) {
                object.setCep(cidadeCep.getCep());
            }
        }

        if( (Util.isNotNull(object)) && ("0".equals(object.getNumeroLogradouro()) || "SN".equals(object.getNumeroLogradouro()))){
            enderecoSN = RepositoryComponentDefault.SIM_LONG;
            txtNumero.setComponentValue("SN");
            txtNumero.setEnabled(false);
        }
    }

    @Override
    public Class<EnderecoUsuarioCadsus> getReferenceClass() {
        return EnderecoUsuarioCadsus.class;
    }

    public void limparPainel(AjaxRequestTarget target) {
        autoCompleteConsultaCidade.setEnabled(true);
        autoCompleteBairro.setEnabled(true);
        autoCompleteConsultaTipoLogradouroCadsus.setEnabled(true);
        txtLogradouro.setEnabled(true);
        target.add(autoCompleteConsultaCidade);
        target.add(autoCompleteBairro);
        target.add(autoCompleteConsultaTipoLogradouroCadsus);
        target.add(txtLogradouro);
    }

    public FormComponent getFocusComponent() {
        return cepWsField;
    }

}
