package br.com.celk.view.siab.producaoequipe;

import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.longfield.RequiredLongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.basico.ProducaoCadastroEquipe;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.on;
import java.util.Collections;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class CadastroProducaoEquipePage extends CadastroPage<ProducaoCadastroEquipe> {

    private LongField txtTotalFamilia;
    private LongField txtMetaCadastroSemanal;
    private RequiredInputField txtResponsavelTreinamento;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private RequiredDateChooser dataTreinamento;

    public CadastroProducaoEquipePage() {
    }

    public CadastroProducaoEquipePage(ProducaoCadastroEquipe object) {
        super(object);
    }

    public CadastroProducaoEquipePage(ProducaoCadastroEquipe object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<ProducaoCadastroEquipe> form) {
        ProducaoCadastroEquipe proxy = on(ProducaoCadastroEquipe.class);

        try {
            form.add(dropDownArea = createDropDownArea(path(proxy.getEquipeMicroArea().getEquipeArea())));
            dropDownArea.setRequired(true);
            dropDownArea.setLabel(new Model(BundleManager.getString("area")));
            form.add(dropDownMicroArea = new DropDown<EquipeMicroArea>(path(proxy.getEquipeMicroArea())));
            dropDownMicroArea.setRequired(true);
            dropDownMicroArea.setLabel(new Model(BundleManager.getString("microarea")));
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true));

        txtTotalFamilia = new RequiredLongField(path(proxy.getTotalFamilia()));
        form.add(txtTotalFamilia);
        txtMetaCadastroSemanal = new RequiredLongField(path(proxy.getMetaSemanal()));
        form.add(txtMetaCadastroSemanal);
        form.add(dataTreinamento = new RequiredDateChooser(path(proxy.getDataTreinamento())));
        dataTreinamento.setRequired(true);
        txtResponsavelTreinamento = new RequiredInputField(path(proxy.getResponsavelTreinamento()));
        form.add(txtResponsavelTreinamento);
        sugerirProfissional();

        add(form);

        if (form.getModel().getObject() != null) {
            carregarMicroAreas(form.getModel().getObject().getEquipeMicroArea());
        }
    }

    private void carregarMicroAreas(EquipeMicroArea microArea) {
        if (microArea != null) {
            dropDownMicroArea.addChoice(null, "");
            for (EquipeMicroArea equipeMicroArea : getListMicroAreas(microArea.getEquipeArea())) {
                String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                dropDownMicroArea.addChoice(equipeMicroArea, descricao);
            }
        }
    }

    private List<EquipeMicroArea> getListMicroAreas(EquipeArea equipeArea) {

        List<EquipeMicroArea> equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                .addProperty(EquipeMicroArea.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                .start().getList();
        return equipeMicroAreas;
    }

    private DropDown<EquipeArea> createDropDownArea(String proxy) throws DAOException, ValidacaoException {
        DropDown<EquipeArea> dropDown = new DropDown<EquipeArea>(proxy);
        List<EquipeArea> list = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDown.addChoice(null, "");
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = getListMicroAreas(equipeArea);
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, "");
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }

                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }

    @Override
    public Object salvar(ProducaoCadastroEquipe object) throws DAOException, ValidacaoException {
        if (object.getDataCadastro() == null) {
            object.setDataCadastro(DataUtil.getDataAtual());
        }

        object.setUsuario(ApplicationSession.get().getSession().<Usuario>getUsuario());
        object.setDataUsuario(DataUtil.getDataAtual());

        return super.salvar(object);
    }

    @Override
    public Class<ProducaoCadastroEquipe> getReferenceClass() {
        return ProducaoCadastroEquipe.class;
    }

    @Override
    public String getMsgSalvo(Object returnObject) {
        return BundleManager.getString("registro_salvo_sucesso");
    }

    @Override
    public Class getResponsePage() {
        return ConsultaProducaoEquipePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroProducaoEquipe");
    }

    private void sugerirProfissional() {
        dropDownMicroArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownMicroArea.getComponentValue() != null) {
                    autoCompleteConsultaProfissional.limpar(target);
                    EquipeMicroArea equipeMicroArea = (EquipeMicroArea) dropDownMicroArea.getComponentValue();
                    if (equipeMicroArea.getEquipeProfissional() != null) {
                        autoCompleteConsultaProfissional.setComponentValue(target, equipeMicroArea.getEquipeProfissional().getProfissional());
                        target.appendJavaScript(JScript.removeAutoCompleteSelection(autoCompleteConsultaProfissional));
                    }
                }
            }
        });
    }
}
