package br.com.celk.view.agenda.agendamento;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlSelecionarTipoAgendamento extends Panel {

    private AbstractAjaxButton btnDentroRede;
    private AbstractAjaxButton btnForaRede;
    private AbstractAjaxButton btnCancelar;

    public PnlSelecionarTipoAgendamento(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(btnDentroRede = new AbstractAjaxButton("btnDentroRede") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onDentroRede(target);
            }

        });

        form.add(btnForaRede = new AbstractAjaxButton("btnForaRede") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onForaRede(target);
            }
        });

        form.add(btnCancelar = new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
            }
        });

        add(form);
    }

    public abstract void onDentroRede(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onForaRede(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

}
