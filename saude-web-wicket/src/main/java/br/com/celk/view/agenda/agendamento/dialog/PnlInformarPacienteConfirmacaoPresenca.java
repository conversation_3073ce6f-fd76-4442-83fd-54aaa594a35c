package br.com.celk.view.agenda.agendamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlInformarPacienteConfirmacaoPresenca extends Panel{
    
    private UsuarioCadsus usuarioCadsus;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private AgendaGradeAtendimentoHorarioDTO dto;
    
    public PnlInformarPacienteConfirmacaoPresenca(String id){
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", new PropertyModel<UsuarioCadsus>(this, "usuarioCadsus")) {

            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }

        });
        
        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarSalvar(target)){
                    dto.setUsuarioCadsus(usuarioCadsus);
                    onConfirmar(target, dto);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    private boolean validarSalvar(AjaxRequestTarget target) {
        try {
            if (autoCompleteConsultaUsuarioCadsus.getComponentValue() == null) {
                throw new ValidacaoException(bundle("msgInformePaciente"));
            }
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto){
        this.dto = dto;
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        
        target.focusComponent(autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField());
    }
}