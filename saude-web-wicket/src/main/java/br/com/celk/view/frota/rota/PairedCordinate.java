package br.com.celk.view.frota.rota;

import br.com.ksisolucoes.vo.service.maps.Coordenadas;

/**
 * <AUTHOR>
 */
public class PairedCordinate {

    private Coordenadas coordenadaInicial;
    private Coordenadas coordenadasFinal;

    public Coordenadas getCoordenadaInicial() {
        return coordenadaInicial;
    }

    public void setCoordenadaInicial(Coordenadas coordenadaInicial) {
        this.coordenadaInicial = coordenadaInicial;
    }

    public Coordenadas getCoordenadasFinal() {
        return coordenadasFinal;
    }

    public void setCoordenadasFinal(Coordenadas coordenadasFinal) {
        this.coordenadasFinal = coordenadasFinal;
    }
}
