package br.com.celk.view.controle.importacaocnes;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.integracao.bos.interfaces.IntegracaoSaudeFacade;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import java.rmi.RemoteException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroImportacaoCnesPage extends BasePage {

    private Form<ConfiguracaoCnesDTO> form;

    private Parametro parametro;
    private DlgConfirmacao dlgConfirmacao;

    public CadastroImportacaoCnesPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(new SubmitButton("btnConfiguracoes", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                setResponsePage(new ConfiguracaoImportacaoCnesPage());
            }
        }));
        form.add(new SubmitButton("btnImportarCnes", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                importar(target);
            }
        }));

        add(form);
    }

    private void importar(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            addModal(target, dlgConfirmacao = new DlgConfirmacao(newModalId(), bundle("msgDesejaRealizarImportacaoCnes")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    try {
                        carregar();
                        BOFactoryWicket.getBO(IntegracaoSaudeFacade.class).integrarInformacoesCnes(parametro.getDriverBaseCnes(), parametro.getUrlBaseCnes(), parametro.getUsuarioBaseCnes(), parametro.getSenhaBaseCnes());
                    } catch (RemoteException ex) {
                        throw new DAOException(ex.getCause());
                    }
                }
            });
        }
        dlgConfirmacao.show(target);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("importacaoCnes");
    }

    private void carregar() {
        parametro = LoadManager.getInstance(Parametro.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Parametro.PROP_CODIGO, CargaBasicoPadrao.CODIGO_PARAMETROS_PADRAO))
                .start().getVO();
    }

}
