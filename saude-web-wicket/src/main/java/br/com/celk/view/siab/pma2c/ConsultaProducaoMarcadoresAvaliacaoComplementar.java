package br.com.celk.view.siab.pma2c;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.siab.pma2c.customcolumn.SiabPma2cColumnPanel;
import br.com.celk.view.siab.pma2c.customize.CustomizeConsultaSiabPma2c;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.siab.SiabPma2c;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaProducaoMarcadoresAvaliacaoComplementar extends ConsultaPage<SiabPma2c, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private Profissional profissional;
    private Cidade cidade;
    private EquipeArea equipeArea;
    private Long mes;
    private Long ano;
    
    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private DropDown cbxEquipeArea;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(DropDownUtil.getMesesDropDown("mes", false));
        form.add(DropDownUtil.getAnoDropDown("ano", false));
        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade("cidade"));
        form.add(cbxEquipeArea = new DropDown("equipeArea"));

        autoCompleteConsultaCidade.add(new ConsultaListener<Cidade>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade object) {
                cbxEquipeArea.setEnabled(object!=null);
                cbxEquipeArea.limpar(target);
                cbxEquipeArea.removeAllChoices(target);
                    if (object!=null) {
                        List<EquipeArea> areas = LoadManager.getInstance(EquipeArea.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), object))
                                .start().getList();

                        cbxEquipeArea.addChoice(null, BundleManager.getString("todas"));
                        for (EquipeArea equipeArea : areas) {
                            cbxEquipeArea.addChoice(equipeArea, equipeArea.getDescricao());
                        }
                    }
            }
        });
        
        cbxEquipeArea.setEnabled(false);
        
        profissional = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getProfissional();
        
        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(SiabPma2c.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("mes"), VOUtils.montarPath(SiabPma2c.PROP_MES), VOUtils.montarPath(SiabPma2c.PROP_DESCRICAO_MES)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("ano"), VOUtils.montarPath(SiabPma2c.PROP_ANO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("profissional"), VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cidade"), VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE, Cidade.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("area"), VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<SiabPma2c>() {

            @Override
            public Component getComponent(String componentId, SiabPma2c rowObject) {
                return new SiabPma2cColumnPanel(componentId, rowObject) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        getPageableTable().update(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaSiabPma2c()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SiabPma2c.PROP_MES), false);
            }
            
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL), profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE), cidade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA), equipeArea));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_MES), mes));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2c.PROP_ANO), ano));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroProducaoMarcadoresAvaliacaoComplementar.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProducaoMarcadoresAvaliacaoComplementar");
    }

}
