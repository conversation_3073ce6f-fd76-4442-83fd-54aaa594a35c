package br.com.celk.view.consorcio.consorcioprestador;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioPrestadorServicoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorServicoCota;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCotaProcedimento<T extends Serializable> extends Window {

    private PnlCotaProcedimento pnlCotaProcedimento;
    private T object;

    public DlgCotaProcedimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(getDialogTitle());

        setInitialHeight(550);
        setInitialWidth(1300);

        setContent(pnlCotaProcedimento = new PnlCotaProcedimento(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgCotaProcedimento.this.onConfirmar(target, pnlCotaProcedimento.getListaCotas(), pnlCotaProcedimento.getListaCotasExcluir());
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public void show(AjaxRequestTarget target, ConsorcioPrestadorServicoDTO dto) {
        pnlCotaProcedimento.setObject(target, dto);
        show(target);
    }

    public void onFechar(AjaxRequestTarget target) {
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<ConsorcioPrestadorServicoCota> listaCotas, List<ConsorcioPrestadorServicoCota> listaCotasExcluir) throws ValidacaoException, DAOException;

    public void setObject(T object) {
        this.object = object;
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("confirmar");
    }

    public String getDialogTitle() {
        return BundleManager.getString("cotasProcedimento");
    }

    private void fechar(AjaxRequestTarget target) {
        DlgCotaProcedimento.this.onFechar(target);
        close(target);
    }
}
