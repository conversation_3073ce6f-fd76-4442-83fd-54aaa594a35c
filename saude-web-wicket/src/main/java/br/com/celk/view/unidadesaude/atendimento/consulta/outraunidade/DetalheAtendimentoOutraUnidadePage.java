package br.com.celk.view.unidadesaude.atendimento.consulta.outraunidade;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.atendimento.consultaprontuario.ConsultaProntuarioPage;
import br.com.celk.view.atendimento.consultaprontuario.ProntuarioPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidadeEncaminhamento;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.EmailTextField;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class DetalheAtendimentoOutraUnidadePage extends CadastroPage<PacienteAtendidoOutraUnidade> {

    private static final long serialVersionUID = -7946968027589155063L;
    private PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade;

    private Table tblEncaminhamentos;
    private List<PacienteAtendidoOutraUnidadeEncaminhamento> encaminhamentos;

    public DetalheAtendimentoOutraUnidadePage(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        super(pacienteAtendidoOutraUnidade, true);
        this.pacienteAtendidoOutraUnidade = pacienteAtendidoOutraUnidade;
        init(pacienteAtendidoOutraUnidade);
    }

    @Override
    public void init(Form<PacienteAtendidoOutraUnidade> form) {
        hideDefaultButtons();
    }

    public void init(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        addCampos(pacienteAtendidoOutraUnidade);
        addTabelaEncaminhamentos(pacienteAtendidoOutraUnidade);
        addBotoes(pacienteAtendidoOutraUnidade);
        addModal();
    }

    private void addModal() {
        getForm().add(new DlgLancarEncaminhamento("modalLancarEncaminhamento") {
            @Override
            public void onCloseDlg(AjaxRequestTarget target, PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
                closeDlg(target, pacienteAtendidoOutraUnidade);
            }
        });
    }

    private void addCampos(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        getForm().add(new TextField<>("paciente", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getNomePaciente())).setEnabled(false));
        getForm().add(new TextField<>("dtAtendimento", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getDataHoraAtendimento())).setEnabled(false));
        getForm().add(new TextField<>("tipoAtendimento", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao())).setEnabled(false));
        getForm().add(new TextField<>("unidade", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getEmpresa().getDescricao())).setEnabled(false));

        getForm().add(new TelefoneField("telefone1", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefoneFormatado())).setEnabled(false));
        getForm().add(new TelefoneField("telefone2", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone2Formatado())).setEnabled(false));
        getForm().add(new TelefoneField("telefone3", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone3Formatado())).setEnabled(false));
        getForm().add(new TelefoneField("telefone4", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone4Formatado())).setEnabled(false));
        getForm().add(new TelefoneField("celular", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getCelularFormatado())).setEnabled(false));
        getForm().add(new EmailTextField("email", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getEmail())).setEnabled(false));
    }

    private void addBotoes(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        getForm().add(getBotaoVoltar());
        getForm().add(getBtnLancarEncaminhamento(pacienteAtendidoOutraUnidade));
        getForm().add(getBtnProntuario());
    }

    private AjaxButton getBotaoVoltar() {
        return new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setResponsePage(new ConsultaAtendimentoOutraUnidadePage());
            }
        };
    }

    private AjaxButton getBtnProntuario() {
        AbstractAjaxButton button = new AbstractAjaxButton("btnProntuario") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
                setResponsePage(new ProntuarioPage(usuarioLogado, (PacienteAtendidoOutraUnidade) form.getModel().getObject()));
            }
        };

        button.setEnabled(new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), ConsultaProntuarioPage.class.getName()));

        return button;
    }

    private AjaxButton getBtnLancarEncaminhamento(final PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        return new AbstractAjaxButton("btnLancarEncaminhamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                DlgLancarEncaminhamento dlgLancarEncaminhamento = new DlgLancarEncaminhamento("modalLancarEncaminhamento", pacienteAtendidoOutraUnidade, true) {
                    @Override
                    public void onCloseDlg(AjaxRequestTarget target, PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
                        closeDlg(target, pacienteAtendidoOutraUnidade);
                    }
                };
                dlgLancarEncaminhamento.init();
                getForm().addOrReplace(dlgLancarEncaminhamento);
                dlgLancarEncaminhamento.show(target);
            }
        };
    }

    private void closeDlg(AjaxRequestTarget target, PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        if (PacienteAtendidoOutraUnidade.SituacaoPacienteAtendidoOutraUnidade.ENCERRADO.value().equals(pacienteAtendidoOutraUnidade.getSituacao())) {
            setResponsePage(new ConsultaAtendimentoOutraUnidadePage());
        } else {
            setResponsePage(new DetalheAtendimentoOutraUnidadePage(pacienteAtendidoOutraUnidade));
        }
    }

    private void addTabelaEncaminhamentos(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        encaminhamentos = getEncaminhamentos(pacienteAtendidoOutraUnidade);

        tblEncaminhamentos = new Table("tblEncaminhamentos", getColumnsLotes(), getCollectionProvider());
        getForm().add(tblEncaminhamentos);
        tblEncaminhamentos.populate();
    }

    private List<PacienteAtendidoOutraUnidadeEncaminhamento> getEncaminhamentos(PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento = on(PacienteAtendidoOutraUnidadeEncaminhamento.class);
        return LoadManager.getInstance(PacienteAtendidoOutraUnidadeEncaminhamento.class)
                .addProperty(path(encaminhamento.getCodigo()))
                .addProperty(path(encaminhamento.getVersion()))
                .addProperty(path(encaminhamento.getDescricao()))
                .addProperty(path(encaminhamento.getDataCadastro()))
                .addProperty(path(encaminhamento.getUsuarioCadastro().getCodigo()))
                .addProperty(path(encaminhamento.getUsuarioCadastro().getNome()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(encaminhamento.getPacienteAtendidoOutraUnidade().getCodigo()), pacienteAtendidoOutraUnidade.getCodigo()))
                .start().getList();
    }

    private List<IColumn> getColumnsLotes() {
        List<IColumn> columns = new ArrayList<>();
        PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento = on(PacienteAtendidoOutraUnidadeEncaminhamento.class);

        ColumnFactory columnFactory = new ColumnFactory(PacienteAtendidoOutraUnidadeEncaminhamento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), path(encaminhamento.getDescricao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataCadastro"), path(encaminhamento.getDataCadastroFormatada())));
        columns.add(columnFactory.createColumn(BundleManager.getString("usuarioCadastro"), path(encaminhamento.getUsuarioCadastro().getNome())));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<PacienteAtendidoOutraUnidadeEncaminhamento>() {
            @Override
            public Component getComponent(String componentId, final PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerEncaminhamento(target, encaminhamento);
                    }
                };
            }
        };
    }

    private void removerEncaminhamento(AjaxRequestTarget target, PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(CadastroFacade.class).delete(encaminhamento);
        setResponsePage(new DetalheAtendimentoOutraUnidadePage(pacienteAtendidoOutraUnidade));
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return encaminhamentos;
            }
        };
    }

    @Override
    public Class<PacienteAtendidoOutraUnidade> getReferenceClass() {
        return PacienteAtendidoOutraUnidade.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaAtendimentoOutraUnidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesEncaminhamento");
    }

    private void hideDefaultButtons() {
        getBtnVoltar().setVisible(false);
        getBtnSalvar().setVisible(false);
    }
}
