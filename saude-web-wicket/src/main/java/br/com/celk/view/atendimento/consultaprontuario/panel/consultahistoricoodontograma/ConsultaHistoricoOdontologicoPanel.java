package br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoodontograma;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Resources;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConsultaHistoricoOdontologicoPanel extends ConsultaProntuarioCadastroPanel{

    private List<Dente> denteList = new ArrayList<Dente>();
    private List<AtendimentoOdontoPlano> odontoList;
    private Map<Dente, List<AtendimentoOdontoPlano>> map = new LinkedHashMap<Dente, List<AtendimentoOdontoPlano>>();
    private SelectionTable<Dente> tblDentes;
    private Table tblHistorico;
    
    public ConsultaHistoricoOdontologicoPanel(String id) {
        super(id, bundle("historicoOdontograma"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");
        
        form.add(new Image("imagemOdontograma", Resources.IMG_ODONTOGRAMA));
        form.add(tblDentes = new SelectionTable("tblDentes", getDenteColumns(), getDenteCollectionProvider()));
        form.add(tblHistorico = new Table("tblHistorico", getHistoricoColumns(), getHistoricoCollectionProvider()));
        
        tblDentes.addSelectionAction(new ISelectionAction<Dente>() {
            @Override
            public void onSelection(AjaxRequestTarget target, Dente dente) {
                odontoList = map.get(dente);
                target.add(tblHistorico);
            }
        });
        tblDentes.populate();
        tblHistorico.populate();
        
        add(form);
        carregarItens();
    }
    
    private List<IColumn> getDenteColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        Dente dente = on(Dente.class);
        
        columns.add(createColumn(bundle("dente", this), dente.getNome()));
        return columns;
    }

    private ICollectionProvider getDenteCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return denteList;
            }
        };
    }

    private List<IColumn> getHistoricoColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        AtendimentoOdontoPlano plano = on(AtendimentoOdontoPlano.class);
        
        columns.add(getCustomActionColumn());
        columns.add(new DateTimeColumn(bundle("data"), path(plano.getDataCadastro())));
        columns.add(createColumn(bundle("face", this), plano.getDescricaoFaceFormatado()));
        columns.add(createColumn(bundle("situacao"), plano.getSituacaoDente().getDescricaoFormatado()));
        columns.add(createColumn(bundle("status"), plano.getDescricaoStatus()));
        return columns;
    }

    private ICollectionProvider getHistoricoCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return odontoList;
            }
        };
    }

    private void carregarItens() {
        AtendimentoOdontoPlano proxy = on(AtendimentoOdontoPlano.class);
        List<AtendimentoOdontoPlano> registros = LoadManager.getInstance(AtendimentoOdontoPlano.class)
            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getUsuarioCadsus().getCodigo()), getUsuarioCadsus().getCodigo()))
            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDente()), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDente().getNome())))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataCadastro()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo())))
            .start().getList();
        if(CollectionUtils.isNotNullEmpty(registros)){
            List<List<AtendimentoOdontoPlano>> agrupamento = CollectionUtils.groupList(registros, path(proxy.getDente().getCodigo()));
            for (List<AtendimentoOdontoPlano> list : agrupamento) {
                map.put(list.get(0).getDente(), list);
                denteList.add(list.get(0).getDente());
            }
            tblDentes.setSelectedObject(agrupamento.get(0).get(0).getDente());
            odontoList = agrupamento.get(0);
        }
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<AtendimentoOdontoPlano>() {
            @Override
            public void customizeColumn(AtendimentoOdontoPlano rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AtendimentoOdontoPlano>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoOdontoPlano modelObject) throws ValidacaoException, DAOException {
                        getConsultaProntuarioController().changePanel(target, new DetalhesHistoricoOdontogramaConsultaPanel(getConsultaProntuarioController().panelId(), modelObject));
                    }

                }).setTitleBundleKey("detalhesTratamento");
            }
        };
    }
}
