package br.com.celk.view.unidadesaude.comorbidades.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.basico.Comorbidade;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaComorbidade extends AutoCompleteConsulta<Comorbidade> {

    public AutoCompleteConsultaComorbidade(String id) {
        super(id);
    }

    public AutoCompleteConsultaComorbidade(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaComorbidade(String id, IModel<Comorbidade> model) {
        super(id, model);
    }

    public AutoCompleteConsultaComorbidade(String id, IModel<Comorbidade> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return Comorbidade.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Comorbidade.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {
                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), Comorbidade.PROP_CODIGO);
                        properties.put(BundleManager.getString("sigla"), Comorbidade.PROP_SIGLA);
                        properties.put(BundleManager.getString("descricao"), Comorbidade.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(Comorbidade.PROP_DESCRICAO, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(Comorbidade.PROP_CODIGO));
                        filterProperties.put(BundleManager.getString("sigla"), new QueryCustom.QueryCustomParameter(Comorbidade.PROP_SIGLA, (String) BuilderQueryCustom.QueryParameter.ILIKE));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return Comorbidade.class;
                    }
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(Comorbidade.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("comorbidade");
    }
}
