package br.com.celk.view.agenda.agendamento.tfd.processo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.frota.viagem.tipotransporteviagem.autocomplete.AutoCompleteConsultaTipoTransporteViagem;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.tfd.dto.ProcessoLaudoTfdDTO;
import br.com.ksisolucoes.tfd.dto.SalvarPedidoTfdPassageiroDTO;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfdPassageiro;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroProcessoViagemLaudoTfdPage extends BasePage {

    private Form<ProcessoLaudoTfdDTO> form;
    private Form containerPassageiros;
    private final ProcessoLaudoTfdDTO processoLaudoTfdDTO;

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaPassageiro;
    private InputField txtObservacao;
    private DropDown dropDownTipoViagem;
    private Table<SalvarPedidoTfdPassageiroDTO> tblPassageiros;

    private UsuarioCadsus passageiro;
    private String observacao;
    private Long tipoViagem;

    public CadastroProcessoViagemLaudoTfdPage(ProcessoLaudoTfdDTO processoLaudoTfdDTO) {
        this.processoLaudoTfdDTO = processoLaudoTfdDTO;
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(processoLaudoTfdDTO));
        ProcessoLaudoTfdDTO proxy = on(ProcessoLaudoTfdDTO.class);

        form.add(new InputField(path(proxy.getLaudoTfd().getUsuarioCadsus().getNomeSocial())).setEnabled(false));
        form.add(new InputField(path(proxy.getLaudoTfd().getPedidoTfd().getNumeroPedido())).setEnabled(false));
        form.add(new InputField(path(proxy.getLaudoTfd().getTipoProcedimento().getDescricao())).setEnabled(false));

        form.getModel().getObject().setStatusTransporte(PedidoTfd.StatusTransporte.UTILIZA);
        form.add(new AutoCompleteConsultaTipoTransporteViagem(path(proxy.getTipoTransporteViagem()), true)
                .setLabel(new Model(bundle("tipoTransporte2"))));

        containerPassageiros = new Form("containerPassageiros");
        containerPassageiros.add(autoCompleteConsultaPassageiro = new AutoCompleteConsultaUsuarioCadsus("passageiro", new PropertyModel<UsuarioCadsus>(this, "passageiro")) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        containerPassageiros.add(dropDownTipoViagem = DropDownUtil.getIEnumDropDown("tipoViagem", new PropertyModel(this, "tipoViagem"), SolicitacaoViagem.TipoViagem.values()));
        containerPassageiros.add(txtObservacao = new InputField("observacao", new PropertyModel(this, "observacao")));

        containerPassageiros.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        form.add(containerPassageiros);

        if (processoLaudoTfdDTO.getLaudoTfd().getPedidoTfd().getTipoTfd().equals(PedidoTfd.TipoTfd.PRIMEIRO_ATENDIMENTO.value())) {
            SalvarPedidoTfdPassageiroDTO dto = new SalvarPedidoTfdPassageiroDTO();
            dto.setUsuarioCadsus(processoLaudoTfdDTO.getLaudoTfd().getUsuarioCadsus());
            dto.setTipoPassageiro(PedidoTfdPassageiro.TIPO_PASSAGEIRO_PACIENTE);
            dto.setTipoViagem(SolicitacaoViagem.TipoViagem.IDA_VOLTA.value());
            processoLaudoTfdDTO.setSalvarPedidoTfdPassageiroDTOs(new ArrayList<SalvarPedidoTfdPassageiroDTO>());
            processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs().add(dto);
        }
        form.add(tblPassageiros = new Table("tblPassageiros", getColumns(), getCollectionProvider()));
        tblPassageiros.populate();

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(form);

        form.getModel().setObject(processoLaudoTfdDTO);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        SalvarPedidoTfdPassageiroDTO proxy = on(SalvarPedidoTfdPassageiroDTO.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("passageiro"), proxy.getUsuarioCadsus().getDescricaoSocialFormatado()));
        columns.add(createColumn(bundle("tipoPassageiro"), proxy.getDescricaoTipoPassageiro()));
        columns.add(createColumn(bundle("tipoViagem"), proxy.getDescricaoTipoViagem()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs();
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<SalvarPedidoTfdPassageiroDTO>() {

            @Override
            public void customizeColumn(SalvarPedidoTfdPassageiroDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<SalvarPedidoTfdPassageiroDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, SalvarPedidoTfdPassageiroDTO modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private void remover(AjaxRequestTarget target, SalvarPedidoTfdPassageiroDTO modelObject) {
        processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs().remove(modelObject);
        tblPassageiros.update(target);
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (passageiro == null) {
            throw new ValidacaoException(BundleManager.getString("informePassageiro"));
        }

        if (CollectionUtils.isEmpty(processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs())) {
            processoLaudoTfdDTO.setSalvarPedidoTfdPassageiroDTOs(new ArrayList<SalvarPedidoTfdPassageiroDTO>());
        }

        for (SalvarPedidoTfdPassageiroDTO item : processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs()) {
            if (item.getUsuarioCadsus().getCodigo().equals(passageiro.getCodigo())) {
                throw new ValidacaoException(BundleManager.getString("passageiroJaAdicionado"));
            }
        }

        SalvarPedidoTfdPassageiroDTO dto = new SalvarPedidoTfdPassageiroDTO();
        dto.setUsuarioCadsus(passageiro);
        dto.setTipoViagem(tipoViagem);
        dto.setObservacao(observacao);

        if (passageiro.getCodigo().equals(processoLaudoTfdDTO.getLaudoTfd().getUsuarioCadsus().getCodigo())) {
            dto.setTipoPassageiro(PedidoTfdPassageiro.TIPO_PASSAGEIRO_PACIENTE);
        } else {
            dto.setTipoPassageiro(PedidoTfdPassageiro.TIPO_PASSAGEIRO_ACOMPANHANTE);
        }

        processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs().add(dto);
        autoCompleteConsultaPassageiro.limpar(target);
        dropDownTipoViagem.limpar(target);
        txtObservacao.limpar(target);
        tblPassageiros.update(target);
    }

    private void salvar() throws ValidacaoException, DAOException {
        if (processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs() == null || processoLaudoTfdDTO.getSalvarPedidoTfdPassageiroDTOs().isEmpty()) {
            throw new ValidacaoException(bundle("msgInformePeloMenosUmPassageiro"));
        }
        BOFactoryWicket.getBO(TfdFacade.class).cadastrarProcessoTfdWeb(processoLaudoTfdDTO);
        setResponsePage(new ConsultaProcessoLaudoTfdPage());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("agendamentoViagem");
    }

}
