package br.com.celk.view.unidadesaude.cadastro.caps.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaMotivoDestinoSaida extends AutoCompleteConsulta<MotivoDestinoSaida> {

    public AutoCompleteConsultaMotivoDestinoSaida(String id) {
        super(id);
    }

    public AutoCompleteConsultaMotivoDestinoSaida(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaMotivoDestinoSaida(String id, IModel<MotivoDestinoSaida> model) {
        super(id, model);
    }

    public AutoCompleteConsultaMotivoDestinoSaida(String id, IModel<MotivoDestinoSaida> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return MotivoDestinoSaida.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(MotivoDestinoSaida.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), MotivoDestinoSaida.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), MotivoDestinoSaida.PROP_DESCRICAO);
                        properties.put(BundleManager.getString("codigoRaas"), MotivoDestinoSaida.PROP_CODIGO_RAAS);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(MotivoDestinoSaida.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return MotivoDestinoSaida.class;
                    }

                };
            }

            @Override
            public List<QueryCustom.QueryCustomParameter> getSearchParam(String searchCriteria) {
                return Arrays.asList(new QueryCustom.QueryCustomParameter(MotivoDestinoSaida.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("motivoDestinoSaida");
    }

}
