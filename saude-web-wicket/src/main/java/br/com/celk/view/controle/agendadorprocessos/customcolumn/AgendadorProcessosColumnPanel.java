package br.com.celk.view.controle.agendadorprocessos.customcolumn;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.EstabelecimentoSIMUtil;
import br.com.celk.view.unidadesaude.esus.domicilio.dialog.DlgUsuarios;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.*;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.bo.prontuario.emergencia.interfaces.facade.ProntuarioEventosFacade;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AgendadorProcesso;
import br.com.ksisolucoes.vo.basico.AgendadorProcessoUsuarios;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.basico.base.BaseAgendadorProcessoUsuarios;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.ProcessoAvaliacaoEstoque;
import br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoNotificacao;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AgendadorProcessosColumnPanel extends Panel implements PermissionContainer {

    private DlgConfirmacao dlgConfirmacao;
    private DlgConfirmacao dlgAvisoProcessoAgendadoSomentePeloTimer;
    private AgendadorProcesso agendadorProcesso;
    private DlgUsuarios dlgUsuarios;
    private List<Usuario> usuariosList;

    private static final Long GOVE_DIGITAL = 4L;

    public AgendadorProcessosColumnPanel(String id, AgendadorProcesso agendadorProcesso) {
        super(id);
        this.agendadorProcesso = agendadorProcesso;
        init();
        carregarAgendadorProcessoUsuarios();
    }

    private void init() {
        add(new AbstractAjaxLink("btnHabilitar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target, agendadorProcesso);
                if (dlgConfirmacao != null) {
                    dlgConfirmacao.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return agendadorProcesso.getStatus().equals(AgendadorProcesso.Status.DESABILITADO.value());
            }
        });
        add(new AbstractAjaxLink("btnDesabilitar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target, agendadorProcesso);
                if (dlgConfirmacao != null) {
                    dlgConfirmacao.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return agendadorProcesso.getStatus().equals(AgendadorProcesso.Status.HABILITADO.value());
            }
        });
        add(new AbstractAjaxLink("btnExecutar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (AgendadorProcesso.Processo.ENVIO_SMS.getValue().equals(agendadorProcesso.getCodigo())) {
                    if ((BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("plataformaEnvioMensagens").equals(GOVE_DIGITAL)) &&
                            (BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("emailGoveDigital") == null ||
                             BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("senhaGoveDigital") == null)) {
                        error("Parâmetros GEM emailGoveDigital ou senhaGoveDigital não preenchidos. Por favor, verifique e tente novamente!");
                    }else{
                        BOFactoryWicket.getBO(AgendamentoFacade.class).processoEnvioSMS();
                    }
                } else if (AgendadorProcesso.Processo.CONFIRMACAO_PRESENCA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(SmsFacade.class).enviarSmsConfirmacaoPresencaAgenda();
                } else if (AgendadorProcesso.Processo.REENVIO_AVISO.getValue().equals(agendadorProcesso.getCodigo())) {
                    if ((BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("plataformaEnvioMensagens").equals(GOVE_DIGITAL)) &&
                            (BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("emailGoveDigital") == null ||
                             BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("senhaGoveDigital") == null)) {
                        error("Parâmetros GEM emailGoveDigital ou senhaGoveDigital não preenchidos. Por favor, verifique e tente novamente!");
                    }else{
                        BOFactoryWicket.getBO(SmsFacade.class).enviarSmsReenvioAvisoAgendamento();
                    }
                } else if (AgendadorProcesso.Processo.RESPOSTA_SMS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoRespostaSMS();
                } else if (AgendadorProcesso.Processo.RETORNO_SMS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoRetornoSMS();
                } else if (AgendadorProcesso.Processo.AVISO_AGENDAMENTOS_LOCAIS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoAvisoAgendamentosLocaisSMS();
                } else if (AgendadorProcesso.Processo.AVALIACAO_ESTOQUE_MINIMO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.ESTOQUE_MINIMO);
                } else if (AgendadorProcesso.Processo.VALIDADE_PRODUTO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.VALIDADE_PRODUTO);
                } else if (AgendadorProcesso.Processo.METAS_CADASTRO_FAMILIA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(BasicoFacade.class).processarMetasCadastroFamilia();
                } else if (AgendadorProcesso.Processo.AVISO_AGENDAMENTOS_CANCELADOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoAvisoAgendamentosCanceladosSMS();
                } else if (AgendadorProcesso.Processo.ATUALIZACAO_SMS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(SmsFacade.class).atualizarMensagensSms();
                    BOFactoryWicket.getBO(SmsFacade.class).consultarRespostasSms();
                } else if (AgendadorProcesso.Processo.AVISO_AGENDAMENTOS_REMANEJADOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoAvisoAgendamentosRemanejadosSMS();
                } else if (AgendadorProcesso.Processo.REENVIO_MENSAGENS_SMS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(SmsFacade.class).processarReenvioMensagensSms();
                } else if (AgendadorProcesso.Processo.NOTIFICAO_INCLUSAO_FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(SmsFacade.class).enviarSMSWhatsappAvisoInclusaoFilaEspera();
                } else if (AgendadorProcesso.Processo.CID_NOTIFICAVEL.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(BasicoFacade.class).processarConfiguracoesCidNotificavel(getDataInicio());
                } else if (AgendadorProcesso.Processo.GERA_AGENDAMENTO_ENCAMINHAMENTO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processarAgendamentoEncaminhamentosLote();
                } else if (AgendadorProcesso.Processo.PACIENTES_DUPLICADOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(BasicoFacade.class).processarPacienteDuplicado();
                } else if (AgendadorProcesso.Processo.CONSULTAR_VALIDACAO_XML_HORUS.getValue().equals(agendadorProcesso.getCodigo())) {
//                    BOFactoryWicket.getBO(MaterialBasicoFacade.class).consultarValidacaoXmlHorus();
                } else if (AgendadorProcesso.Processo.CANCELAMENTO_DAS_GUIAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(ConsorcioFacade.class).cancelarGuiaProcedimento(true);
                } else if (AgendadorProcesso.Processo.NOTIFICACOES_PACIENTE.getValue().equals(agendadorProcesso.getCodigo())) {
                    if (BOFactory.getBO(CommomFacade.class).modulo(Modulos.VACINAS).getParametro("DiasParaNotificacaoVacinasJaAplicadas") == null
                            || BOFactory.getBO(CommomFacade.class).modulo(Modulos.VACINAS).getParametro("DiasParaNotificacaoVacinasJaAplicadas").equals(0)) {
                        error(BundleManager.getString("msgDiasParaNotificacaoVacinasJaAplicadasDeveSerConfigurado"));
                    }else{
                        BOFactory.getBO(UsuarioCadsusFacade.class).atualizarNotificacoesUsuarioCadsus();
                    }
                } else if (AgendadorProcesso.Processo.VACINAS_APLICADAS_ACS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VacinaFacade.class).notificarVacinasAplicadasAgentesComunitarias();
                } else if (AgendadorProcesso.Processo.IMPORTACAO_SIGTAP.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(ProntuarioEventosFacade.class).importarRegistrosSigtapAgendadorProcesso();
                } else if (AgendadorProcesso.Processo.INATIVAR_PROFISSIONAIS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(ProfissionalFacade.class).inativarProfissionais();
                } else if (AgendadorProcesso.Processo.RELACAO_GESTANTES.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.RELACAO_GESTANTES);
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_AGENDADAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(UsuarioFacade.class).notificarSolicitacoesAgendadas(usuariosList, true);
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_DEVOLVIDAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(UsuarioFacade.class).notificarSolicitacoesDevolvidas(usuariosList, true);
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_NEGADAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(UsuarioFacade.class).notificarSolicitacoesNegada(usuariosList, true);
                } else if (AgendadorProcesso.Processo.VACINA_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarVacina(usuariosList);
                } else if (AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarAtendimento(usuariosList);
                } else if (AgendadorProcesso.Processo.AGENDAMENTO_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarAgendamento(usuariosList);
                } else if (AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarEstoque(usuariosList);
                } else if (AgendadorProcesso.Processo.TFD_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarTfd(usuariosList);
                } else if (AgendadorProcesso.Processo.EXAME_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarExame(usuariosList);
                } else if (AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarDispensacaoMedicamentos(usuariosList);
                } else if (AgendadorProcesso.Processo.FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarFilaEspera(usuariosList);
                } else if (AgendadorProcesso.Processo.VIGILANCIA_INTEGRACAO_DOCSPRIME.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).processoVigilanciaIntegracaoDocsprime(usuariosList, true);
                } else if (AgendadorProcesso.Processo.RELACAO_ESTABELECIMENTOS_SEM_RT.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).processoRelacaoEstabelecimentosSemRT(usuariosList, true);
                } else if (AgendadorProcesso.Processo.GERAIS_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoInovamfriFacade.class).gerarUnidades(usuariosList);
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_ALVARAS_PROVISORIOS_VENCIDOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).processoNotificacaoAlvarasProvisoriosVencidos(usuariosList, true);
                } else if (AgendadorProcesso.Processo.CONTROLE_TEMPERATURA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VacinaFacade.class).gerarRegistrosControleTemperatura();
                } else if (AgendadorProcesso.Processo.ORDENAR_POSICAO_FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).calcularPosicoesFilaEspera();
                } else if (AgendadorProcesso.Processo.ENVIO_MENSAGEM_LAUDO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).enviaMensagemLaudoDisponivel();
                } else if (AgendadorProcesso.Processo.DEVOLVE_AIH_AGUARDANDO_ANALISE.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).devolverAih();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_DEVOLUCAO_AIH_ESTABELECIMENTO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(UsuarioFacade.class).notificarAihsDevolvidas(usuariosList);
                }
//CS-Cidadao
                else if (AgendadorProcesso.Processo.VACINA_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarVacina(usuariosList);
                } else if (AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarAtendimento(usuariosList);
                } else if (AgendadorProcesso.Processo.AGENDAMENTO_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarAgendamento(usuariosList);
                } else if (AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarEstoque(usuariosList);
                } else if (AgendadorProcesso.Processo.TFD_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarTfd(usuariosList);
                } else if (AgendadorProcesso.Processo.EXAME_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarExame(usuariosList);
                } else if (AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarDispensacaoMedicamentos(usuariosList);
                } else if (AgendadorProcesso.Processo.FILA_ESPERA_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarFilaEspera(usuariosList);
                } else if (AgendadorProcesso.Processo.GERAIS_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoCsCidadaoFacade.class).gerarUnidades(usuariosList);
                } else if (AgendadorProcesso.Processo.EXAME_VENCIMENTO_VALIDADE_AUTORIZACAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(ExameFacade.class).processarExamesComValidadeAutorizacaoVencida();
                } else if (AgendadorProcesso.Processo.DESBLOQUEAR_CONSORCIADOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(ConsorcioFacade.class).desbloquearConsorciados();
                } else if (AgendadorProcesso.Processo.RELATORIO_DENUNCIAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).enviarRelatorioDenuncias();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_VENCIMENTO_CONTRATOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(ConsorcioFacade.class).enviarRelatorioRelacaoPrestadores();
                } else if (AgendadorProcesso.Processo.LOCALIZACAO_REGISTRO_AGRAVO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarLocalizacaoRegistroAgravo();
                } else if (AgendadorProcesso.Processo.CONSULTAR_PRODUTOS_BRANET.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoBranetFacade.class).enviarRelatorioSincronizacaoProdutos();
                } else if (AgendadorProcesso.Processo.CONSULTAR_EMPRESAS_BRANET.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoBranetFacade.class).enviarRelatorioSincronizacaoUnidades();
                } else if (AgendadorProcesso.Processo.SINCRONIZAR_ITENS_INTEGRADOS_BRANET.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(IntegracaoBranetFacade.class).sincronizarItensIntegrados(null);
                } else if (AgendadorProcesso.Processo.FECHAMENTO_CANCELAMENTO_ATENDIMENTO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AtendimentoFacade.class).fecharCancelarAtendimentoAutomatico();
                } else if (AgendadorProcesso.Processo.ENVIO_AGRAVO_DATA_LIMITE.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).enviarRelatorioRegistroAgravo();
                } else if (AgendadorProcesso.Processo.ALERTAR_DOSES_ATRASADAS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VacinaFacade.class).alertarDosesAtrasadas();
                } else if (AgendadorProcesso.Processo.ATUALIZAR_SITUACAO_BOLETOS_WS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).atualizarSituacaoBoletos();
                } else if (AgendadorProcesso.Processo.ATUALIZAR_SITUACAO_BOLETOS_REMESSA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRetornoRemessaBoletos();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_REQUERIMENTO_PARADO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(VigilanciaFacade.class).notificarRequerimentosParados();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_VCTO_VALIDADE_ALVARA_LICENCA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).notificarVctoValidadeAlvaraLicenca();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_VCTO_PRAZO_DEFESA_RECURSO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).notificarVctoPrazoDefesaRecurso();
                } else if (AgendadorProcesso.Processo.INTEGRACAO_CNES.getValue().equals(agendadorProcesso.getCodigo())) {
                    if (RepositoryComponentDefault.AMBIENTE_PRODUCAO.equals(System.getProperty("ambiente"))) {
                        throw new ValidacaoException(BundleManager.getString("msgProcessoAgendadoSomentePeloTimer", agendadorProcesso.getNomeServico()));
                    } else {
                        BOFactory.getBO(BasicoFacade.class).processarIntegracaoCnesAsync();
                        throw new ValidacaoException(BundleManager.getString("msgProcessoAgendadoEmExecucao", agendadorProcesso.getNomeServico()));
                    }
                } else if (AgendadorProcesso.Processo.CANCELAR_AGENDAMENTOS_VENCIDOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).cancelarAgendamentosVencidos(SessaoAplicacaoImp.getInstance().getUsuario());
                } else if (AgendadorProcesso.Processo.ATUALIZAR_RESUMO_MOVIMENTACAO_ESTOQUE.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(EstoqueEmpresaFacade.class).atualizarResumoMovimentacaoEstoque();
                } else if (AgendadorProcesso.Processo.ATUALIZAR_CONFIG_ANO_BASE_VIRADA_ANO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).atualizarConfiguracoesViradaAno();
                } else if (AgendadorProcesso.Processo.ENVIA_NOTIFICACAO_2A_DOSE_VACINA_APP_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VacinaFacade.class).processarEnvioMensagemNotificacaoVacinaAppCidadao();
                } else if (AgendadorProcesso.Processo.DEVOLVER_SALDO_NAO_UTILIZADO_COTA_ANTERIOR.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).resgatarSaldoPpi();
                } else if (AgendadorProcesso.Processo.ATUALIZAR_SITUACAO_LEITO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).atualizarSituacaoLeito();
                } else if (AgendadorProcesso.Processo.CANCELAR_AGENDAMENTOS_DEVOLVIDOS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).cancelarAgendamentosDevolvidos();
                } else if (AgendadorProcesso.Processo.INATIVAR_AGENDA_CONTRATO_VENCIDO.getValue().equals(agendadorProcesso.getCodigo())){
                    BOFactory.getBO(AgendamentoFacade.class).inativarAgendaContratoVencido();
                } else if (AgendadorProcesso.Processo.NOTIFICACAO_VENCIMENTO_FPO.getValue().equals(agendadorProcesso.getCodigo())){
                    BOFactory.getBO(AgendamentoFacade.class).notificacaoVencimentoFPO(usuariosList, true);
                }else if (AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_ENTRADA.getValue().equals(agendadorProcesso.getCodigo())){
                    throw new ValidacaoException(BundleManager.getString("msgProcessoDesabilitado"));
                    //Desabilitado pois Não envia mmais bnafar, posteriormente pode ser habilitado para envio ao RNDS
                    //BOFactory.getBO(MaterialBasicoFacade.class).enviarBnafarEntrada();
                }else if (AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_SAIDA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(MaterialBasicoFacade.class).enviarBnafarSaida();
                }else if (AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_DISPENSACAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("msgProcessoDesabilitado"));
                    //Desabilitado pois Não envia mmais bnafar, posteriormente pode ser habilitado para envio ao RNDS
                    //BOFactory.getBO(MaterialBasicoFacade.class).atualizarBnafarDispensacao();
                }else if (AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_POS_ESTOQUE.getValue().equals(agendadorProcesso.getCodigo())) {
                    if (existeUsuariosAgendadorDeProcesso(AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_POS_ESTOQUE.getValue())){
                        BOFactory.getBO(MaterialBasicoFacade.class).gerarEnviarBnafarPosEstoque();
                    }else{
                        throw new ValidacaoException(BundleManager.getString("msgProcessoAgendadoSemUsuario", agendadorProcesso.getNomeServico()));
                    }
                } else if (AgendadorProcesso.Processo.DEVOLVE_SOLICITACOES_FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(AgendamentoFacade.class).devolverSolicitacoesFilaEspera();

                } else if (AgendadorProcesso.Processo.ENVIAR_EMAIL_AUTOS_APP_FRU.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).enviarEmailAutosAppFru();
                } else if (AgendadorProcesso.Processo.INTEGRACAO_ESTABELECIMENTO_SIM.getValue().equals(agendadorProcesso.getCodigo())) {
                    if (!EstabelecimentoSIMUtil.isIntegracaoEstabelecimentoSIMHabilitado()) {
                        throw new ValidacaoException("Integração não está habilitada, verifique o parâmetro GEM (habilitaIntegracaoEstabelecimentoSIM), com a Diretoria de Vigilância Sanitária!");
                    }
                    BOFactory.getBO(VigilanciaFacade.class).createUpdateEstabelecimentosSIM();
                } else if (AgendadorProcesso.Processo.ENVIAR_DADOS_FASTMEDIC.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(IntegracaoFastMedicFacade.class).enviarDadosFastmedic();
                } else if (AgendadorProcesso.Processo.ENVIAR_DADOS_AGRAVO_NOYIFICACAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.POST.value());
                } else if (AgendadorProcesso.Processo.ATUALIZAR_DADOS_AGRAVO_NOTIFICACAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.PUT.value());
                } else if (AgendadorProcesso.Processo.DELETAR_DADOS_AGRAVO_NOTIFICACAO.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.DELETE.value());
                } else if (AgendadorProcesso.Processo.ENVIAR_INTEGRACAO_SMAR.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactory.getBO(EstoqueEmpresaFacade.class).enviarAtualizacaoEstoqueSmar();
                } else if (AgendadorProcesso.Processo.ENVIO_MENSAGEM_AGEN_VIAGEM.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processaMensagensAgendaViagem();
                } else if (AgendadorProcesso.Processo.ENVIO_MENSAGEM_AGEN_VIAGEM_CANCEL.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processaMensagensAgendaViagemCancelamento();
                } else if (AgendadorProcesso.Processo.REENVIO_AVISO_AGENDAMENTOS_LOCAIS.getValue().equals(agendadorProcesso.getCodigo())) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).processoReenvioAvisoAgendamentosLocaisSMS();
                }
            }

            @Override
            public boolean isEnabled() {
                return true;
            }

        });

        add(new AbstractAjaxLink("btnComponentes") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                carregarAgendadorProcessoUsuarios();
                onVisualizarUsuarios(target);
            }

            @Override
            public boolean isEnabled() {
                return agendadorProcesso.getStatus().equals(AgendadorProcesso.Status.DESABILITADO.value()) && (AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_DEVOLVIDAS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_AGENDADAS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_NEGADAS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.AGENDAMENTO_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.VACINA_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.GERAIS_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.TFD_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.EXAME_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_INOVAMFRI.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.VIGILANCIA_INTEGRACAO_DOCSPRIME.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.RELACAO_ESTABELECIMENTOS_SEM_RT.getValue().equals(agendadorProcesso.getCodigo()))
                        || AgendadorProcesso.Processo.NOTIFICACAO_ALVARAS_PROVISORIOS_VENCIDOS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.NOTIFICACAO_VENCIMENTO_FPO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIO_MENSAGEM_LAUDO.getValue().equals(agendadorProcesso.getCodigo())
//CS-Cidadao
                        || AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.AGENDAMENTO_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.VACINA_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.GERAIS_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.TFD_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.EXAME_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.FILA_ESPERA_CS_CIDADAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.RELATORIO_DENUNCIAS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.NOTIFICACAO_VENCIMENTO_CONTRATOS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.CONSULTAR_EMPRESAS_BRANET.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.CONSULTAR_PRODUTOS_BRANET.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.SINCRONIZAR_ITENS_INTEGRADOS_BRANET.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIO_AGRAVO_DATA_LIMITE.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ALERTAR_DOSES_ATRASADAS.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_POS_ESTOQUE.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIAR_EMAIL_AUTOS_APP_FRU.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.DEVOLVE_SOLICITACOES_FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.INTEGRACAO_ESTABELECIMENTO_SIM.getValue().equals(agendadorProcesso.getCodigo())
                        ||AgendadorProcesso.Processo.DEVOLVE_SOLICITACOES_FILA_ESPERA.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIAR_DADOS_FASTMEDIC.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIAR_DADOS_AGRAVO_NOYIFICACAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ATUALIZAR_DADOS_AGRAVO_NOTIFICACAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.DELETAR_DADOS_AGRAVO_NOTIFICACAO.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIAR_INTEGRACAO_SMAR.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.ENVIO_MENSAGEM_AGEN_VIAGEM_CANCEL.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.DEVOLVE_AIH_AGUARDANDO_ANALISE.getValue().equals(agendadorProcesso.getCodigo())
                        || AgendadorProcesso.Processo.NOTIFICACAO_DEVOLUCAO_AIH_ESTABELECIMENTO.getValue().equals(agendadorProcesso.getCodigo())
                        ;
            }
        });
    }

    private Date getDataInicio() {
        ParametroAtendimento parametroAtendimento = CargaBasicoPadrao.getInstance().getParametroAtendimento();
        if (parametroAtendimento.getDataProcessoCidNotificavel() == null) {
            return (new GregorianCalendar(1900, 1, 1)).getTime();
        }
        return parametroAtendimento.getDataProcessoCidNotificavel();
    }

    private void initDlgConfirmacao(AjaxRequestTarget target, final AgendadorProcesso agendadorProcesso) throws ValidacaoException {
        if (dlgConfirmacao == null) {
            if (AgendadorProcesso.Status.DESABILITADO.value().equals(agendadorProcesso.getStatus())) {
                if (AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_POS_ESTOQUE.getValue().equals(agendadorProcesso.getCodigo())) {
                    if (!existeUsuariosAgendadorDeProcesso(AgendadorProcesso.Processo.ATUALIZAR_BNAFAR_POS_ESTOQUE.getValue())) {
                        throw new ValidacaoException(BundleManager.getString("msgProcessoAgendadoSemUsuario", agendadorProcesso.getNomeServico()));
                    }
                }
                WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("msgDesejaRealmenteHabilitarProcessoX", agendadorProcesso.getNomeServico())) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CommomFacade.class).habilitarProcesso(agendadorProcesso);
                        AgendadorProcessosColumnPanel.this.updateTable(target);
                    }
                });
            } else {
                WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("msgDesejaRealmenteDesabilitarProcessoX", agendadorProcesso.getNomeServico())) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CommomFacade.class).desabilitarProcesso(agendadorProcesso);
                        AgendadorProcessosColumnPanel.this.updateTable(target);
                    }
                });
            }
        }
    }

    private void onVisualizarUsuarios(AjaxRequestTarget target) {
        initDlgComponentes(target);
        if (dlgUsuarios != null) {
            dlgUsuarios.show(target);
        }
    }

    private void initDlgComponentes(AjaxRequestTarget target) {
        if (dlgUsuarios == null) {
            WindowUtil.addModal(target, this, dlgUsuarios = new DlgUsuarios(WindowUtil.newModalId(this), usuariosList, agendadorProcesso) {
                @Override
                public void concluir(AjaxRequestTarget target, List<Usuario> usuarioList, List<Usuario> usuariosRemovidosList) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(UsuarioFacade.class).atualizarAgendadorProcessoUsuarios(agendadorProcesso, usuarioList, usuariosRemovidosList);
                    AgendadorProcessosColumnPanel.this.usuariosList = usuarioList;

                }
            });
        }
    }

    private void carregarAgendadorProcessoUsuarios() {
        List<Usuario> usuariosExtractList = Lambda.extract(getAgendadorProcessoUsuarios(), Lambda.on(AgendadorProcessoUsuarios.class).getUsuario());
        usuariosList = usuariosExtractList;
    }

    private List<AgendadorProcessoUsuarios> getAgendadorProcessoUsuarios() {
        return LoadManager.getInstance(AgendadorProcessoUsuarios.class)
                .addParameter(new QueryCustom.QueryCustomParameter(BaseAgendadorProcessoUsuarios.PROP_AGENDADOR_PROCESSO, agendadorProcesso.getCodigo()))
                .start().getList();

    }

    private boolean existeUsuariosAgendadorDeProcesso(Long codigoProcesso) {
        List<AgendadorProcessoUsuarios>  usuarios = LoadManager.getInstance(AgendadorProcessoUsuarios.class)
                .addParameter(new QueryCustom.QueryCustomParameter(BaseAgendadorProcessoUsuarios.PROP_AGENDADOR_PROCESSO, codigoProcesso))
                .start().getList();
        return !usuarios.isEmpty();


    }

    public abstract void updateTable(AjaxRequestTarget target);
}
