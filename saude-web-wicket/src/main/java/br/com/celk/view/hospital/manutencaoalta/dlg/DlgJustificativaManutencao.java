package br.com.celk.view.hospital.manutencaoalta.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgJustificativaManutencao extends Window {

    private PnlJustificativaManutencao pnlJustificativaManutencao;

    public DlgJustificativaManutencao(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(500);
        setInitialHeight(92);

        setResizable(false);

        setTitle(BundleManager.getString("justificativa"));

        setContent(pnlJustificativaManutencao = new PnlJustificativaManutencao(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, String justificativa) {
        pnlJustificativaManutencao.limpar(target);
        pnlJustificativaManutencao.setJustificativa(target, justificativa);
        super.show(target);
    }
}
