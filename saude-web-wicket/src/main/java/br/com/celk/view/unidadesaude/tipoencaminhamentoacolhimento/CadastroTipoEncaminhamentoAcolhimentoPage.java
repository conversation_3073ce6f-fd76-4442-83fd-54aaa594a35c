package br.com.celk.view.unidadesaude.tipoencaminhamentoacolhimento;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.prontuario.TipoEncaminhamentoAcolhimento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroTipoEncaminhamentoAcolhimentoPage extends CadastroPage<TipoEncaminhamentoAcolhimento> {
    
    private InputField txtDescricao;

    public CadastroTipoEncaminhamentoAcolhimentoPage(TipoEncaminhamentoAcolhimento object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }
    
    public CadastroTipoEncaminhamentoAcolhimentoPage(TipoEncaminhamentoAcolhimento object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoEncaminhamentoAcolhimentoPage(TipoEncaminhamentoAcolhimento object) {
        this(object, false);
    }

    public CadastroTipoEncaminhamentoAcolhimentoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredInputField<String>(TipoEncaminhamentoAcolhimento.PROP_DESCRICAO));
        form.add(DropDownUtil.getIEnumDropDown(TipoEncaminhamentoAcolhimento.PROP_ORIGEM_PACIENTE, TipoEncaminhamentoAcolhimento.OrigemPaciente.values()));
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<TipoEncaminhamentoAcolhimento> getReferenceClass() {
        return TipoEncaminhamentoAcolhimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoEncaminhamentoAcolhimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoEncaminhamento");
    }
}