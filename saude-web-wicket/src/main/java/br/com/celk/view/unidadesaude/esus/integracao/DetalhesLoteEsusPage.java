package br.com.celk.view.unidadesaude.esus.integracao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.esus.interfaces.dto.EsusDetalheDTO;
import br.com.celk.esus.interfaces.dto.EsusDetalheTransporteDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.unidadesaude.esus.integracao.dialog.DlgDetalhesEsusFicha;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.esus.Esus;
import br.com.ksisolucoes.vo.esus.EsusTransporte;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class DetalhesLoteEsusPage extends BasePage {

    private Form<EsusDetalheDTO> form;
    private Esus esus;
    private DlgDetalhesEsusFicha dlgDetalhesEsusFicha;
    private Table tableTransporte;
    private Empresa empresa;
    private Profissional profissional;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Long tipoFicha;
    private DropDown ddTipoFicha;

    public DetalhesLoteEsusPage(Esus esus) {
        this.esus = esus;
        init();
    }

    private void init() {
        EsusDetalheDTO proxy = on(EsusDetalheDTO.class);

        getForm().add(new DisabledInputField(path(proxy.getEsus().getCodigo())));
        getForm().add(new DisabledInputField(path(proxy.getEsus().getDescricaoStatus())));
        getForm().add(new DisabledInputField(path(proxy.getEsus().getDataGeracao())));
        getForm().add(new DisabledInputField(path(proxy.getEsus().getUsuario().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getEsus().getEmpresa().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getEsus().getDescricaoPeriodo())));

        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", new PropertyModel<Empresa>(this, "empresa")));
        getForm().add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel<Profissional>(this, "profissional")));
        getForm().add(ddTipoFicha = DropDownUtil.getIEnumDropDown("tipoFicha", new PropertyModel<Long>(this, "tipoFicha"), EsusTransporte.TipoFicha.values(), true));

        getForm().add(tableTransporte = new Table("tableTransporte", getColumns(), getCollectionProvider()));
        tableTransporte.populate();

        getForm().add(new VoltarButton("btnVoltar").setEnabled(true));


        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                carregarEsusTransporte();
                tableTransporte.populate();
                tableTransporte.update(target);
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                carregarEsusTransporte();
                tableTransporte.populate();
                tableTransporte.update(target);
            }
        });

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                carregarEsusTransporte();
                tableTransporte.populate();
                tableTransporte.update(target);
            }
        });
        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                carregarEsusTransporte();
                tableTransporte.populate();
                tableTransporte.update(target);
            }
        });
        ddTipoFicha.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarEsusTransporte();
                tableTransporte.populate();
                tableTransporte.update(target);
            }
        });
        add(getForm());
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        EsusDetalheTransporteDTO proxy = on(EsusDetalheTransporteDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("tipoFicha"), proxy.getEsusTransporte().getDescricaoTipoFicha()));
        columns.add(createColumn(bundle("estabelecimento"), proxy.getEsusTransporte().getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getEsusTransporte().getProfissional().getNome()));
        columns.add(createColumn(bundle("quantidade"), proxy.getEsusTransporte().getQuantidadeFicha()));
        columns.add(createColumn(bundle("origem"), proxy.getEsusTransporte().getDescricaoOrigem()));

        return columns;
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) {
                return getForm().getModel().getObject().getEsusTransporteList();
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EsusDetalheTransporteDTO>() {
            @Override
            public void customizeColumn(EsusDetalheTransporteDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<EsusDetalheTransporteDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EsusDetalheTransporteDTO modelObject) throws ValidacaoException, DAOException {
                        consultarDetalhesEsusFicha(target, modelObject);
                    }
                }).setTitleBundleKey("detalhes");
            }
        };
    }

    private void consultarDetalhesEsusFicha(AjaxRequestTarget target, EsusDetalheTransporteDTO dto) throws DAOException, ValidacaoException {
        if (dlgDetalhesEsusFicha == null) {
            addModal(target, dlgDetalhesEsusFicha = new DlgDetalhesEsusFicha(newModalId()) {
            });
        }
        dlgDetalhesEsusFicha.show(target, dto);
    }

    private Form<EsusDetalheDTO> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<EsusDetalheDTO>(new EsusDetalheDTO()));
            this.form.getModel().getObject().setEsus(esus);
            this.form.getModel().getObject().setEsusTransporteList(carregarEsusTransporte());
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesLoteEsus");
    }

    private List<EsusDetalheTransporteDTO> carregarEsusTransporte() {
        EsusTransporte proxy = on(EsusTransporte.class);
        ArrayList<EsusDetalheTransporteDTO> listEsusDetalheTransporteDTO = new ArrayList<>();

        List<EsusTransporte> list = LoadManager.getInstance(EsusTransporte.class)
                .addProperties(new HQLProperties(EsusTransporte.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, path(proxy.getEmpresa())).getProperties())
                .addProperties(new HQLProperties(Profissional.class, path(proxy.getProfissional())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEsus()), esus))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEmpresa()), empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getProfissional()), profissional))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoFichaEsus()), tipoFicha))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getTipoFichaEsus())))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getEmpresa().getDescricao())))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getProfissional().getNome())))
                .start().getList();

        for (EsusTransporte esusTransporte : list) {
            listEsusDetalheTransporteDTO.add(new EsusDetalheTransporteDTO(esusTransporte));
        }
        getForm().getModel().getObject().setEsusTransporteList(listEsusDetalheTransporteDTO);
        return listEsusDetalheTransporteDTO;
    }
}
