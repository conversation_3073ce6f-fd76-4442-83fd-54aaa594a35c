package br.com.celk.view.frota.veiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.programasaude.autocomplete.AutoCompleteConsultaProgramaSaude;
import br.com.celk.view.frota.veiculo.autocomplete.AutoCompleteConsultaTipoVeiculo;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.Veiculo;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroVeiculoPage extends BasePage {

    private Form<VeiculoDTO> form;
    private Veiculo veiculo;

    private InputField txtReferencia;
    private SubmitButton btnSalvar;
    private SubmitButton btnAvancar;

    public CadastroVeiculoPage(Veiculo object) {
        this.veiculo = object;
    }

    public CadastroVeiculoPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(new VeiculoDTO()));
        VeiculoDTO proxy = on(VeiculoDTO.class);
        if (veiculo != null) {
            form.getModel().getObject().setVeiculo(veiculo);
        }

        form.add(txtReferencia = new UpperField(path(proxy.getVeiculo().getReferencia())));
        form.add(new RequiredInputField<String>(path(proxy.getVeiculo().getDescricao()))
                .setLabel(new Model<String>(bundle("descricao"))));
        form.add(new RequiredInputField<String>(path(proxy.getVeiculo().getQuantidadeLugares()))
                .setLabel(new Model<String>(bundle("nrOcupantes"))));
        form.add(new AutoCompleteConsultaTipoVeiculo(path(proxy.getVeiculo().getTipoVeiculo()), true)
                .setLabel(new Model(bundle("tipoVeiculo"))));
        form.add(new AutoCompleteConsultaProgramaSaude(path(proxy.getVeiculo().getProgramaSaude()), true)
                .setLabel(new Model(bundle("programaSaude"))));
        form.add(getDropDownTerceirizado(path(proxy.getVeiculo().getFlagTerceiro())));

        form.add(new VoltarButton("btnVoltar"));
        form.add(btnSalvar = (SubmitButton) new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }).setEnabled(false));

        form.add(btnAvancar = (SubmitButton) new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                setResponsePage(new CadastroVeiculoStep2Page(CadastroVeiculoPage.this.form.getModel().getObject()));
            }
        }).setEnabled(false));
        if (form.getModel().getObject().getVeiculo() != null) {
            if (form.getModel().getObject().getVeiculo().getFlagTerceiro().equals(RepositoryComponentDefault.SIM_LONG)) {
                btnSalvar.setEnabled(true);
                btnAvancar.setEnabled(false);
            } else {
                btnSalvar.setEnabled(false);
                btnAvancar.setEnabled(true);
            }
        }
        add(form);
    }

    private void salvar() throws DAOException, ValidacaoException {
        BOFactoryWicket.save(form.getModel().getObject().getVeiculo());
        Page page = new ConsultaVeiculoPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroVeiculo");
    }

    private RequiredDropDown getDropDownTerceirizado(String id) {
        RequiredDropDown dropDown = new RequiredDropDown(id);

        dropDown.addChoice(null, "");
        dropDown.addChoice(RepositoryComponentDefault.NAO_LONG, Bundle.getStringApplication("rotulo_nao"));
        dropDown.addChoice(RepositoryComponentDefault.SIM_LONG, Bundle.getStringApplication("rotulo_sim"));
        dropDown.setLabel(new Model(bundle("terceirizado")));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (form.getModel().getObject().getVeiculo().getFlagTerceiro().equals(RepositoryComponentDefault.SIM_LONG)) {
                    btnSalvar.setEnabled(true);
                    btnAvancar.setEnabled(false);
                } else {
                    btnSalvar.setEnabled(false);
                    btnAvancar.setEnabled(true);
                }
                art.add(btnSalvar);
                art.add(btnAvancar);
            }
        });

        return dropDown;
    }

}
