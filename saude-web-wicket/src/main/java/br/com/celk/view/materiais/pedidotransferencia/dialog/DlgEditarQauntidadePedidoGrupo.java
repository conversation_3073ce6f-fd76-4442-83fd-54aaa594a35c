package br.com.celk.view.materiais.pedidotransferencia.dialog;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferenciaItem;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * Created by sulivan on 06/11/17.
 */
public abstract class DlgEditarQauntidadePedidoGrupo extends Window {

    private DTOPedidoTransferenciaItem object;
    private PnlEditarQuantidadePedidoGrupo pnlEditarQuantidadePedidoGrupo;

    public DlgEditarQauntidadePedidoGrupo(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(330);
        setInitialHeight(80);

        setResizable(false);

        setTitle(bundle("editarLocalizacaoEstrutura"));

        setContent(pnlEditarQuantidadePedidoGrupo = new PnlEditarQuantidadePedidoGrupo(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, DTOPedidoTransferenciaItem dto) throws ValidacaoException, DAOException {
                close(target);
                DlgEditarQauntidadePedidoGrupo.this.onConfirmar(target, dto);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, DTOPedidoTransferenciaItem dto) throws ValidacaoException, DAOException;

    public void setObject(AjaxRequestTarget target, DTOPedidoTransferenciaItem object) {
        this.object = object;
        pnlEditarQuantidadePedidoGrupo.setObject(target, object);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }

}