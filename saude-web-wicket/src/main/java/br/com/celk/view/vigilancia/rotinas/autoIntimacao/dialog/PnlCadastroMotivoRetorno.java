package br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

public abstract class PnlCadastroMotivoRetorno extends Panel {

    private Form form;
    private String descricao;
    private InputField txtDescricao;

    private List<MotivoRetorno> lstMotivoRetornos;
    private Table table;

    public PnlCadastroMotivoRetorno(String id) {
        super(id);
        init();
    }

    private void init() {
        add(form = new Form("form", new CompoundPropertyModel(this)));
        form.setOutputMarkupId(true);

        form.add(txtDescricao = new InputField<String>("descricao"));
        txtDescricao.addRequiredClass();

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                adicionar(target);
            }
        });

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.setScrollY("108px");
        table.setScrollXInner("665px");
        table.populate();

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        MotivoRetorno proxy = on(MotivoRetorno.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("motivoRetorno"), proxy.getDescricao()));
        return columns;
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (descricao == null || descricao.trim().length() == 0) {
            throw new ValidacaoException(bundle("msgInformeMotivo"));
        }

        MotivoRetorno motivoRetorno = new MotivoRetorno();
        motivoRetorno.setDescricao(descricao);
        BOFactoryWicket.save(motivoRetorno);
        lstMotivoRetornos.add(0, motivoRetorno);
        table.update(target);
        txtDescricao.limpar(target);
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<MotivoRetorno>() {
            @Override
            public void customizeColumn(MotivoRetorno rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<MotivoRetorno>() {
                    @Override
                    public void action(AjaxRequestTarget target, MotivoRetorno motivoRetorno) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(motivoRetorno);
                        CrudUtils.removerItem(target, table, lstMotivoRetornos, motivoRetorno);
                        onExcluir(target, motivoRetorno);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstMotivoRetornos;
            }
        };
    }

    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
        lstMotivoRetornos = LoadManager.getInstance(MotivoRetorno.class)
                .addProperties(new HQLProperties(MotivoRetorno.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(MotivoRetorno.PROP_DESCRICAO))
                .start().getList();
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    public abstract void onExcluir(AjaxRequestTarget target, MotivoRetorno motivoRetorno) throws ValidacaoException, DAOException;
}
