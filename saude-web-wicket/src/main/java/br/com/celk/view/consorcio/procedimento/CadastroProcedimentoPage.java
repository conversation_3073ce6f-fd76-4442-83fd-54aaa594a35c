package br.com.celk.view.consorcio.procedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.atendimento.procedimentogrupo.pnl.PnlConsultaProcedimentoGrupo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroProcedimentoPage extends CadastroPage<Procedimento> {

    private PnlConsultaProcedimentoGrupo pnlConsultaProcedimentoGrupo;
    private DropDown<ProcedimentoSubGrupo> cbxProcedimentoSubGrupo;
    private DropDown<ProcedimentoFormaOrganizacao> cbxProcedimentoFormaOrganizacao;

    private InputField txtReferencia;
    private boolean registroNovo;
    
    public CadastroProcedimentoPage() {
        this.registroNovo = true;
        extra();
    }
    
    public CadastroProcedimentoPage(Procedimento object) {
        this(object, false, true);
        extra();
    }

    public CadastroProcedimentoPage(Procedimento object, boolean viewOnly, boolean registroNovo) {
        super(object, viewOnly);
        this.registroNovo = registroNovo;
        extra();
    }

    private void extra() {
        ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao = getForm().getModelObject().getProcedimentoFormaOrganizacao();
        if(! registroNovo){
            txtReferencia.setEnabled(false);

            if(procedimentoFormaOrganizacao != null){
                if(isViewOnly()){
                    ProcedimentoSubGrupo procedimentoSubGrupo = procedimentoFormaOrganizacao.getRoProcedimentoSubGrupo();

                    cbxProcedimentoSubGrupo.addChoice(procedimentoSubGrupo, procedimentoSubGrupo.getDescricaoFormatado());
                    cbxProcedimentoFormaOrganizacao.addChoice(procedimentoFormaOrganizacao, procedimentoFormaOrganizacao.getDescricaoFormatado());
                }else{
                    carregarCbxProcedimentoSubGrupo(procedimentoFormaOrganizacao.getId().getCodigoProcedimentoGrupo());
                    carregarCbxFormaOrganizacao(procedimentoFormaOrganizacao.getId().getCodigoProcedimentoSubGrupo(), procedimentoFormaOrganizacao.getId().getCodigoProcedimentoGrupo());
                }
            }
        }
        
        pnlConsultaProcedimentoGrupo.add(new ConsultaListener<ProcedimentoGrupo>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoGrupo object) {
                eventoProcedimentoGrupo(target, object);
            }
        });
        cbxProcedimentoSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                eventoProcedimentoSubGrupo(target, cbxProcedimentoSubGrupo.getModelObject());
            }
        });
        
    }
    
    @Override
    public void init(Form form) {
        form.add(txtReferencia = new InputField<Long>(Procedimento.PROP_REFERENCIA));
        form.add(new RequiredUpperField(Procedimento.PROP_DESCRICAO));
        form.add(pnlConsultaProcedimentoGrupo = new PnlConsultaProcedimentoGrupo("procedimentoFormaOrganizacao.roProcedimentoSubGrupo.roGrupo",true));
        form.add(cbxProcedimentoSubGrupo = new RequiredDropDown("procedimentoFormaOrganizacao.roProcedimentoSubGrupo"));
        form.add(cbxProcedimentoFormaOrganizacao = new RequiredDropDown("procedimentoFormaOrganizacao"));
        
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<Procedimento> getReferenceClass() {
        return Procedimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProcedimentosAdicionais");
    }

    @Override
    public Object salvar(Procedimento object) throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).cadastrarProcedimentosAdicionais(object, registroNovo);
    }

    private void eventoProcedimentoGrupo(AjaxRequestTarget target, ProcedimentoGrupo object){
        cbxProcedimentoSubGrupo.limpar(target);
        cbxProcedimentoSubGrupo.removeAllChoices();
        
        cbxProcedimentoSubGrupo.addChoice(null, "");
        
        if (object!=null) {
            carregarCbxProcedimentoSubGrupo(object.getCodigo());
        }
    }
    
    private void carregarCbxProcedimentoSubGrupo(Long codigoGrupo){
            List<ProcedimentoSubGrupo> subGrupos = LoadManager.getInstance(ProcedimentoSubGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoSubGrupo.PROP_ID, ProcedimentoSubGrupoPK.PROP_CODIGO_GRUPO), codigoGrupo))
                    .start().getList();
            for (ProcedimentoSubGrupo procedimentoSubGrupo1 : subGrupos) {
                cbxProcedimentoSubGrupo.addChoice(procedimentoSubGrupo1, procedimentoSubGrupo1.getDescricaoFormatado());
            }
    }

    private void eventoProcedimentoSubGrupo(AjaxRequestTarget target, ProcedimentoSubGrupo object){
        cbxProcedimentoFormaOrganizacao.limpar(target);
        cbxProcedimentoFormaOrganizacao.removeAllChoices();
        
        cbxProcedimentoFormaOrganizacao.addChoice(null, "");
        if (object!=null) {
            carregarCbxFormaOrganizacao(object.getId().getCodigo(), object.getId().getCodigoGrupo());
        }
    }
    
    private void carregarCbxFormaOrganizacao(Long codigoSubGrupo, Long codigoGrupo){
            List<ProcedimentoFormaOrganizacao> formaOrganizacoes = LoadManager.getInstance(ProcedimentoFormaOrganizacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_GRUPO), codigoGrupo))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_SUB_GRUPO), codigoSubGrupo))
                    .start().getList();
            for (ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao1 : formaOrganizacoes) {
                cbxProcedimentoFormaOrganizacao.addChoice(procedimentoFormaOrganizacao1, procedimentoFormaOrganizacao1.getDescricaoFormatado());
            }
    }
}
