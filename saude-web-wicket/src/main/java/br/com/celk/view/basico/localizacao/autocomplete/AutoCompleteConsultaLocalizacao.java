package br.com.celk.view.basico.localizacao.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaLocalizacao extends AutoCompleteConsulta<Localizacao> {

    public AutoCompleteConsultaLocalizacao(String id) {
        super(id);
    }

    public AutoCompleteConsultaLocalizacao(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaLocalizacao(String id, IModel<Localizacao> model) {
        super(id, model);
    }

    public AutoCompleteConsultaLocalizacao(String id, IModel<Localizacao> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return Localizacao.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Localizacao.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), Localizacao.PROP_CODIGO);
                        properties.put(BundleManager.getString("localizacao"), Localizacao.PROP_DESCRICAO);
                        properties.put(BundleManager.getString("sigla"), Localizacao.PROP_SIGLA);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(Localizacao.PROP_DESCRICAO, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("sigla"), new QueryCustom.QueryCustomParameter(Localizacao.PROP_SIGLA, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return Localizacao.class;
                    }

                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(Localizacao.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("localizacao");
    }

}
