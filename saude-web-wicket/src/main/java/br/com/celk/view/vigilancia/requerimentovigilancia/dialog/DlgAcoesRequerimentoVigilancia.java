package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAcoesRequerimentoVigilancia extends Window {

    private PnlAcoesRequerimentoVigilancia pnlAcoesRequerimentoVigilancia;

    public DlgAcoesRequerimentoVigilancia(String id, boolean telaFiscal) {
        super(id);
        init(telaFiscal);
    }

    private void init(boolean telaFiscal) {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("maisAcoes");
            }
        });

        setInitialWidth(1024);
        setInitialHeight(360);
        setResizable(true);

        setContent(pnlAcoesRequerimentoVigilancia = new PnlAcoesRequerimentoVigilancia(getContentId(), telaFiscal) {

            @Override
            public void onEditar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onEditar(target, dto);
            }

            @Override
            public void onAprovar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onAprovar(target, dto);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onCancelar(target, dto);
            }

            @Override
            public void onEmAnalise(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String justificativa) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onEmAnalise(target, dto, justificativa);
            }

            @Override
            public void onReverterSituacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onReverterSituacao(target, dto);
            }

            @Override
            public void onReverterFinalizacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String motivo) throws ValidacaoException, DAOException {
                close(target);
                dto.getRequerimentoVigilancia().setMotivoReversaoFinalizacao(motivo);
                DlgAcoesRequerimentoVigilancia.this.onReverterFinalizacao(target, dto);
            }

            @Override
            public void onLancarOcorrencia(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onLancarOcorrencia(target, dto);
            }

            @Override
            public void onFinalizar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onFinalizar(target, dto);
            }

            @Override
            public void onEntregaDocumento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onEntregaDocumento(target, dto);
            }

            @Override
            public void onConformidadeTecnica(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onConformidadeTecnica(target, dto);
            }

            @Override
            public void onEmitirBoleto(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onEmitirBoleto(target, dto);
            }

            @Override
            public void onComprovantePagamento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onComprovantePagamento(target, dto);
            }

            @Override
            public void onInformarFiscais(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onInformarFiscais(target, dto);
            }

            @Override
            public void onRoteiroInspecao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onRoteiroInspecao(target, dto);
            }

            @Override
            public void onRegistroVisita(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onRegistroVisitas(target, dto);
            }

            //            @Override
//            public void onDevolver(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
//                close(target);
//                DlgAcoesRequerimentoVigilancia.this.onDevolver(target, dto);
//            }

            @Override
            public void onRelatorioInspecoes(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                DlgAcoesRequerimentoVigilancia.this.onRelatorioInspecoes(target, dto);
            }

            @Override
            public void onAutoInfracao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                DlgAcoesRequerimentoVigilancia.this.onAutoInfracao(target, dto);
            }

            @Override
            public void onAutoIntimacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                DlgAcoesRequerimentoVigilancia.this.onAutoIntimacao(target, dto);
            }

            @Override
            public void onAutoMulta(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                DlgAcoesRequerimentoVigilancia.this.onAutoMulta(target, dto);
            }

            @Override
            public void onAutoPenalidade(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                DlgAcoesRequerimentoVigilancia.this.onAutoPenalidade(target, dto);
            }

            @Override
            public void onParecer(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onParecer(target, dto);
            }

            @Override
            public void onDocumentosRequerimento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onDocumentosRequerimento(target, dto);
            }

            @Override
            public void onHistoricoContribuinte(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onHistoricoContribuinte(target, dto);
            }

            @Override
            public void onBoletoComplementar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesRequerimentoVigilancia.this.onBoletoComplementar(target, dto);
            }

            @Override
            public void onVoltar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                voltar(target);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                voltar(target);
                return true;
            }
        });
    }

    private void voltar(AjaxRequestTarget target) {
        close(target);
        DlgAcoesRequerimentoVigilancia.this.onVoltar(target);
    }

    public abstract void onEditar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAprovar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onCancelar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onEmAnalise(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto, String justificativa) throws ValidacaoException, DAOException;

    public abstract void onReverterSituacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onReverterFinalizacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onLancarOcorrencia(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFinalizar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onEntregaDocumento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onConformidadeTecnica(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onEmitirBoleto(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onComprovantePagamento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onInformarFiscais(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public void onRoteiroInspecao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException {};

//    public abstract void onDevolver(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onVoltar(AjaxRequestTarget target);

    public abstract void onRelatorioInspecoes(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onRegistroVisitas(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAutoIntimacao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAutoInfracao(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAutoMulta(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onAutoPenalidade(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onParecer(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onDocumentosRequerimento(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onHistoricoContribuinte(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public abstract void onBoletoComplementar(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, RequerimentoVigilanciaDTO dto) {
        show(target);
        pnlAcoesRequerimentoVigilancia.setObject(target, dto);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.initMasks()));
        response.render(OnLoadHeaderItem.forScript(JScript.initTextAreaLimit()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeAutoCompleteDrop()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeEnterSubmitFromForm()));
    }

}