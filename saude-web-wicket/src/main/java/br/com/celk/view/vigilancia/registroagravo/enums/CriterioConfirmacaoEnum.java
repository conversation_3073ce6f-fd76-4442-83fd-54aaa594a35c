package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

import java.util.ArrayList;
import java.util.List;

public enum CriterioConfirmacaoEnum implements IEnum {

    LABORATORIAL(0L, Bundle.getStringApplication("laboratorial")),
    CLINICO_EPIDEMIOLOGICO(1L, Bundle.getStringApplication("clinicoEpidemiologico"));

    private Long value;
    private String descricao;

    CriterioConfirmacaoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static CriterioConfirmacaoEnum valueOf(Long value) {
        for (CriterioConfirmacaoEnum v : CriterioConfirmacaoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
