<wicket:extend>
    <form wicket:id="form" class="dirty">
        <div class="span-10 last">
            <fieldset>
                <h2><label><wicket:message key="dadosPaciente"/></label></h2>
                <div class="field">
                    <div class="span-5"><label>
                        <wicket:message key="paciente"/>
                    </label><input size="60" type="text"
                                   wicket:id="vacinaAplicacao.usuarioCadsus.descricaoSocialFormatado"/>
                        <a class="icon pencil" href="#" wicket:id="btnEditarPaciente"
                           wicket:message="title:editarPaciente">
                            <wicket:message key="editarPaciente"/>
                        </a>
                    </div>
                    <div class="span-2 last"><label><wicket:message key="cns"/></label><input size="15" type="text" wicket:id="vacinaAplicacao.usuarioCadsus.cns"/></div>
                    <div class="span-3 last"><label><wicket:message key="idade"/></label><input size="15" type="text" wicket:id="vacinaAplicacao.usuarioCadsus.descricaoIdadeAnoMesDia"/></div>
                </div>
                <div class="field"><label><wicket:message key="grupoDeAtendimento"/></label><select wicket:id="vacinaAplicacao.grupoAtendimento"/></div>
                <div class="field">
                    <div class="span-2"><label><wicket:message key="comunicanteHanseniase"/></label><select wicket:id="vacinaAplicacao.comunicanteHanseniase"/></div>
                    <div class="span-4"><label><wicket:message key="gestante"/></label><select wicket:id="vacinaAplicacao.flagGestante"/></div>
                    <div class="span-4 last"><label><wicket:message key="puerpera"/></label><select wicket:id="vacinaAplicacao.flagPuerpera"/></div>
                </div>
            </fieldset>
            <fieldset>
                <h2><label><wicket:message key="dadosVacina"/></label></h2>
                <div class="field"><label><wicket:message key="vacina"/></label><input type="text" size="60" wicket:id="vacinaAplicacao.tipoVacina.descricao"/></div>
                <div class="field"><label><wicket:message key="laboratorio"/></label><div class="group" wicket:id="vacinaAplicacao.produtoVacina"/></div>
                <div class="field"><label><wicket:message key="localAtendimento"/></label><select wicket:id="vacinaAplicacao.localAtendimento"/></div>
                <div class="field"><label><wicket:message key="turno"/></label><select wicket:id="vacinaAplicacao.turno"/></div>
                <div class="field"><label><wicket:message key="viajante"/></label><select wicket:id="vacinaAplicacao.viajante"/></div>
                <div class="field"><div wicket:id="vacinaAplicacao.lote"/></div>
                <div class="field"><label><wicket:message key="profissionalAplicacao"/></label><div class="group" wicket:id="vacinaAplicacao.profissionalAplicacao"></div></div>
                <div class="field">
                    <label><wicket:message key="dataAplicacao"/></label><div class="group" wicket:id="vacinaAplicacao.dataAplicacao"/>
                    <label>
                        <wicket:message key="horaInicio"/>
                    </label><input class="hora" size="6" type="text" wicket:id="horaInicio"/>
                    <label>
                        <wicket:message key="horaTermino"/>
                    </label><input class="hora" size="6" type="text" wicket:id="horaFim"/>
                    <!--<label><wicket:message key="frascoNovo"/></label><select wicket:id="vacinaAplicacao.novoFrasco"/>-->
                </div>
                <div class="field"><label><wicket:message key="viaAdministracao"/></label><select wicket:id="vacinaAplicacao.viaAdministracao"/></div>
                <div class="field"><label><wicket:message key="localAplicacao"/></label><select wicket:id="vacinaAplicacao.localAplicacao"/></div>
                <div class="field"><label><wicket:message key="motivo"/></label><div class="group" wicket:id="vacinaAplicacao.motivoVacinaEspecial"/></div>
                <div class="field"><label><wicket:message key="profissionalIndicador"/></label><div class="group" wicket:id="vacinaAplicacao.profissionalIndicador"></div></div>
                <div class="field"><label><wicket:message key="observacao"/></label><textarea class="limit" data-limit="1024" wicket:id="vacinaAplicacao.observacao"/></div>
            </fieldset>
                <fieldset wicket:id="containerVacinaAplicacaoInsumo">
            <form wicket:id="formInsumos">
                    <h2>
                        <a class="expand" wicket:message="data-expandir:+,data-recolher:-"/> 
                        <label><wicket:message key="itensUtilizadosAplicacao"/></label>
                    </h2>
                    <div class="field expand"><label><wicket:message key="insumo"/></label><div class="group" wicket:id="produto"></div></div>
                    <div class="field expand">
                        <div class="span-2"><label><wicket:message key="quantidade"/></label><input type="text" size="7" maxlength="3" class="number" wicket:id="quantidade"/></div>
                        <div class="span-8 last"><div wicket:id="lote"/></div>
                    </div>
                    <div class="field expand">
                        <div class="span-horizontal">
                            <input type="button" class="arrow-bottom" wicket:message="value:adicionar" wicket:id="btnAdicionarVacinaAplicacaoInsumo" />
                        </div>
                    </div>
                    <div class="field expand">
                        <div class="span-horizontal">
                            <div wicket:id="tblVacinaAplicacaoInsumo"/>
                        </div>
                    </div>
            </form>
                </fieldset>
        </div>
        <div id="control-bottom">
            <input type="button" wicket:message="value:voltar" class="arrow-left" wicket:id="btnVoltar"/>
            <input type="button" wicket:message="value:salvar" class="checkmark" wicket:id="btnSalvar"/>
        </div>
    </form>
</wicket:extend>