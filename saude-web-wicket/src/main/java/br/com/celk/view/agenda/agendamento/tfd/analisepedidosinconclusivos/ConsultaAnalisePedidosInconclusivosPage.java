package br.com.celk.view.agenda.agendamento.tfd.analisepedidosinconclusivos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.agenda.agendamento.tfd.analisepedidosinconclusivos.dlg.AnalisePedidoInconclusivoViewDTO;
import br.com.celk.view.agenda.agendamento.tfd.analisepedidosinconclusivos.dlg.DlgAnalisePedidoInconclusivo;
import br.com.celk.view.agenda.agendamento.tfd.processo.AlteracaoLaudoTfdPage;
import br.com.celk.view.agenda.agendamento.tfd.processo.customize.CustomizeConsultaLaudoTfd;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.unidadesaude.exames.manutencaocotacbo.ConsultaCotaCboPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.tfd.pedidotfdagendamento.dto.PedidoTfdDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.exame.ExameCboCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaAnalisePedidosInconclusivosPage extends BasePage {

    private Form form;
    private String nomePaciente;
    private Empresa empresa;
    private TipoProcedimento tipoProcedimento;
    private String numeroPedido;
    private DatePeriod periodo;
    private PnlChoicePeriod pnlChoicePeriod;

    private PageableTable pageableTable;
    private DlgAnalisePedidoInconclusivo dlgAnalisePedidoInconclusivo;

    public ConsultaAnalisePedidosInconclusivosPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField<String>("nomePaciente"));
        form.add(new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true).setTfd(true));
        form.add(new InputField("numeroPedido"));
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo"));
        pnlChoicePeriod.setDefaultOutro();

        form.add(pageableTable = new PageableTable("table", getColumns(), getPagerProvider()));
        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                return ConsultaAnalisePedidosInconclusivosPage.this.getParam();
            }
        });
        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(LaudoTfd.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("numeroPedido"), VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(LaudoTfd.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoProcedimento"), VOUtils.montarPath(LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataCadastro"), VOUtils.montarPath(LaudoTfd.PROP_DATA_CADASTRO)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<LaudoTfd>() {
            @Override
            public void customizeColumn(final LaudoTfd rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LaudoTfd>() {
                    @Override
                    public void action(AjaxRequestTarget target, LaudoTfd modelObject) throws ValidacaoException, DAOException {
                        onReceber(target, modelObject);
                    }
                }).setTitleBundleKey("avaliar")
                        .setIcon(Icon.ROUND_CHECKMARK);

                addAction(ActionType.MANUTENCAO, rowObject, new IModelAction<LaudoTfd>() {
                    @Override
                    public void action(AjaxRequestTarget target, LaudoTfd modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new AlteracaoLaudoTfdPage(rowObject));
                    }
                }).setTitleBundleKey("editarLaudoTFD")
                        .setIcon(Icon.DOC_EDIT).setVisible(getUtilizaRegulacaoTFD());

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<LaudoTfd>() {
                    @Override
                    public void action(AjaxRequestTarget target, LaudoTfd modelObject) throws ValidacaoException, DAOException {
                        PedidoTfdDTO pedidoTfdDTO = new PedidoTfdDTO();
                        pedidoTfdDTO.setLaudoTfd(modelObject);
                        BOFactoryWicket.getBO(TfdFacade.class).confirmarEnvioTfdConclusivoRegulacao(pedidoTfdDTO);
                        pageableTable.update(target);
                    }
                }).setTitleBundleKey("enviarParaRegulação").setQuestionDialogBundleKey("msgConfirmaEnvioPedidoParaAnaliseRegulacao")
                        .setVisible(getUtilizaRegulacaoTFD());
            }
        };
    }

    private boolean getUtilizaRegulacaoTFD(){
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaRegulacaoTFD"));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return false;
    }

    private void onReceber(AjaxRequestTarget target, LaudoTfd modelObject) {
        if (dlgAnalisePedidoInconclusivo == null) {
            addModal(target, dlgAnalisePedidoInconclusivo = new DlgAnalisePedidoInconclusivo(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, AnalisePedidoInconclusivoViewDTO dto, LaudoTfd laudoTfd) throws ValidacaoException, DAOException {
                    PedidoTfdDTO pedidoTfdDTO = new PedidoTfdDTO();

                    pedidoTfdDTO.setLaudoTfd(laudoTfd);
                    String numeroCartao = StringUtils.trimToNull(Coalesce.asString(dto.getNumeroCartao()).replaceAll("[^0-9]", ""));
                    if (numeroCartao != null) {
                        pedidoTfdDTO.setNumeroCartao(Coalesce.asLong(numeroCartao));
                    }

                    BOFactoryWicket.getBO(TfdFacade.class).confirmarAvaliacaoPedidosTfdConclusivos(pedidoTfdDTO);
                    pageableTable.update(target);
                }
            });
        }
        dlgAnalisePedidoInconclusivo.show(target, modelObject);
    }

    public IPagerProvider getPagerProvider() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaLaudoTfd()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(LaudoTfd.PROP_DATA_CADASTRO, false);
            }
        };
    }

    public List<BuilderQueryCustom.QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));

        }
        parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_TIPO_PROCEDIMENTO, tipoProcedimento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO), numeroPedido));
        parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_DATA_CADASTRO, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_STATUS, LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value()));

        Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();

        if (!isActionPermitted(usuario, Permissions.EMPRESA)) {
            try {
                usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCboCompetencia.PROP_EMPRESA, Empresa.PROP_CODIGO),
                        QueryCustom.QueryCustomParameter.IN, usuario.getEmpresasUsuario()));
            } catch (SGKException ex) {
                Logger.getLogger(ConsultaCotaCboPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_EMPRESA, empresa));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAnalisePedidosInconclusivos");
    }
}
