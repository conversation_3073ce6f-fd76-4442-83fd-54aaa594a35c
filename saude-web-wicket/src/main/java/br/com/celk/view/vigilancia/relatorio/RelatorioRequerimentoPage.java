package br.com.celk.view.vigilancia.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.bairro.autocomplete.AutoCompleteConsultaBairroMulti;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.profissional.autocomplete.AutoCompleteConsultaProfissionalVigilancia;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilanciaMulti;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioRequerimentoPage extends RelatorioPage<RelatorioRequerimentoDTOParam> {

    private DropDown<TipoSolicitacao> dropDownTipoSolicitacao;
    private AutoCompleteConsultaSetorVigilanciaMulti autoCompleteConsultaSetorVigilanciaMulti;
    private AutoCompleteConsultaBairroMulti autoCompleteConsultaBairroMulti;
    @Override
    public void init(Form<RelatorioRequerimentoDTOParam> form) {
        RelatorioRequerimentoDTOParam proxy = on(RelatorioRequerimentoDTOParam.class);

        form.add(new PnlAtividadeEstabelecimento(path(proxy.getAtividade())));
        form.add(new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento())));
        form.add(new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getVigilanciaPessoa())));
        form.add(new AutoCompleteConsultaProfissionalVigilancia(path(proxy.getVigilanciaProfissional())));
        autoCompleteConsultaSetorVigilanciaMulti = new AutoCompleteConsultaSetorVigilanciaMulti(path(proxy.getSetorVigilanciaList()));
        autoCompleteConsultaSetorVigilanciaMulti.setEnabled(false);
        autoCompleteConsultaSetorVigilanciaMulti.setLabel(Model.of(bundle("setorVigilancia")));
        CheckBoxLongValue cbTodosSetores = new CheckBoxLongValue(path(proxy.getTodosSetoresVigilancia()));
        autoCompleteConsultaBairroMulti = new AutoCompleteConsultaBairroMulti(path(proxy.getBairroList()));
        autoCompleteConsultaBairroMulti.setEnabled(false);
        autoCompleteConsultaBairroMulti.setLabel(Model.of(bundle("bairro")));
        CheckBoxLongValue cbTodosBairros = new CheckBoxLongValue(path(proxy.getTodosBairros()));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDocumento()), TipoSolicitacao.TipoDocumento.values(), true, false, true));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRequerimentoDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), RelatorioRequerimentoDTOParam.Situacao.values(), true));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getOrigem()), RequerimentoVigilancia.Origem.values(), true));
        form.add(new CheckBoxSimNao(path(proxy.getEntregaDocumento())));

        form.add(autoCompleteConsultaSetorVigilanciaMulti);
        form.add(cbTodosSetores);
        form.add(autoCompleteConsultaBairroMulti);
        form.add(cbTodosBairros);

        cbTodosSetores.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configurarTodosSetores(target, form, autoCompleteConsultaSetorVigilanciaMulti);
            }
        });

        cbTodosBairros.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configurarTodosBairros(target, form, autoCompleteConsultaBairroMulti);
            }
        });
    }

    private void configurarTodosSetores(AjaxRequestTarget target, Form form, AutoCompleteConsultaSetorVigilanciaMulti autoCompleteConsultaSetorVigilanciaMulti) {
        RelatorioRequerimentoDTOParam object = (RelatorioRequerimentoDTOParam) form.getModel().getObject();
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getTodosSetoresVigilancia())) {
            autoCompleteConsultaSetorVigilanciaMulti.setEnabled(false);
            autoCompleteConsultaSetorVigilanciaMulti.setRequired(false);
        } else {
            autoCompleteConsultaSetorVigilanciaMulti.setEnabled(true);
            autoCompleteConsultaSetorVigilanciaMulti.setRequired(true);
        }
        autoCompleteConsultaSetorVigilanciaMulti.limpar(target);
        target.add(autoCompleteConsultaSetorVigilanciaMulti);
    }

    private void configurarTodosBairros(AjaxRequestTarget target, Form form, AutoCompleteConsultaBairroMulti autoCompleteConsultaBairroMulti){
        RelatorioRequerimentoDTOParam object = (RelatorioRequerimentoDTOParam) form.getModel().getObject();
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getTodosBairros())) {
            autoCompleteConsultaBairroMulti.setEnabled(false);
            autoCompleteConsultaBairroMulti.setRequired(false);
        } else {
            autoCompleteConsultaBairroMulti.setEnabled(true);
            autoCompleteConsultaBairroMulti.setRequired(true);
        }
        autoCompleteConsultaBairroMulti.limpar(target);
        target.add(autoCompleteConsultaBairroMulti);
    }

    private DropDown<TipoSolicitacao> getDropDownTipoSolicitacao(String id) {
        if (dropDownTipoSolicitacao == null) {
            dropDownTipoSolicitacao = new DropDown(id);
            dropDownTipoSolicitacao.addChoice(null, "");

            List<TipoSolicitacao> list = LoadManager.getInstance(TipoSolicitacao.class)
                    .addProperty(TipoSolicitacao.PROP_CODIGO)
                    .addProperty(TipoSolicitacao.PROP_DESCRICAO)
                    .start().getList();

            for (TipoSolicitacao tipoSolicitacao : list) {
                dropDownTipoSolicitacao.addChoice(tipoSolicitacao, tipoSolicitacao.getDescricao());
            }
        }

        return dropDownTipoSolicitacao;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("relatorioRequerimentos");
    }

    @Override
    public Class<RelatorioRequerimentoDTOParam> getDTOParamClass() {
        return RelatorioRequerimentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRequerimentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimento(param);
    }

}
