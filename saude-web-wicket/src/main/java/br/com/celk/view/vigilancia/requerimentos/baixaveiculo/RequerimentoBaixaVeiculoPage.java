package br.com.celk.view.vigilancia.requerimentos.baixaveiculo;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.CadastroEstabelecimentoPage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.externo.view.consulta.dlg.DlgAnexosVeiculosObservacao;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoRevalidacaoAlvaraPage;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.veiculo.dialog.DlgEdicaoVeiculoEstabelecimento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaVeiculo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaVeiculoItens;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.RequerimentoLicencaTransporteHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Roger on 02/06/16.
 */
public class RequerimentoBaixaVeiculoPage extends BasePage {


    private Form<RequerimentoBaixaVeiculoDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoBaixaVeiculo requerimentoBaixaVeiculo;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private DisabledInputField<String> txtEnderecoEstabelecimento;
    private DisabledInputField<String> txtCnpjCpfFormatado;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtProtocolo;
    private List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList = new ArrayList<VeiculoEstabelecimentoDTO>();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private MultiSelectionTable<VeiculoEstabelecimentoDTO> tblVeiculos;
    private DlgEdicaoVeiculoEstabelecimento dlgEdicaoVeiculoEstabelecimento;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private boolean viewOnly;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;
    private Class classReturn;
    private DropDown dropDownAnoBase;
    private AbstractAjaxLink btnCadastrarEstabelecimento;
    private Estabelecimento estabelecimento;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private DlgAnexosVeiculosObservacao dlgAnexosVeiculosObservacao;

    public RequerimentoBaixaVeiculoPage(TipoSolicitacao tipoSolicitacao, boolean viewOnly, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.classReturn = clazz;
        this.viewOnly = viewOnly;
        init();
    }

    public RequerimentoBaixaVeiculoPage(TipoSolicitacao tipoSolicitacao, Estabelecimento estabelecimento, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.estabelecimento = estabelecimento;
        this.classReturn = clazz;
        this.viewOnly = true;
        init();
    }

    public RequerimentoBaixaVeiculoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.classReturn = clazz;
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.viewOnly = isPendenteOrAnalise() ? !viewOnly : false;
        carregarRequerimentoBaixaVeiculo(requerimentoVigilancia);
        init();
    }

    private boolean isPendenteOrAnalise() {
        return (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(this.requerimentoVigilancia.getSituacao()) ||
                RequerimentoVigilancia.Situacao.ANALISE.value().equals(this.requerimentoVigilancia.getSituacao()));
    }

    private void carregarConfiguracaoVigilancia() {
        if (configuracaoVigilancia == null) {
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(),e);
            }
        }
    }
    
    private void init() {
        carregarConfiguracaoVigilancia();

        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoBaixaVeiculoDTO proxy = on(RequerimentoBaixaVeiculoDTO.class);

        getForm().add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setEnabled(isCadastrar());
        getForm().add(btnCadastrarEstabelecimento = new AbstractAjaxLink("btnCadastrarEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoBaixaVeiculoPage.this.setResponsePage(new CadastroEstabelecimentoPage(tipoSolicitacao, null));
            }
        });
        btnCadastrarEstabelecimento.setVisible(isNew() && hasPermission());

        getForm().add(txtEnderecoEstabelecimento = new DisabledInputField<>(path(proxy.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEnderecoFormatado())));
        getForm().add(txtCnpjCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado())));
        getForm().add(txtFantasia = new DisabledInputField<>(path(proxy.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        getForm().add(txtProtocolo = new DisabledInputField<>(path(proxy.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getProtocoloFormatado())));

        {        //VeiculoEstabelecimento
            getForm().add(tblVeiculos = new MultiSelectionTable("tblVeiculos", getColumns(), getCollectionProvider()));
            tblVeiculos.populate();
            tblVeiculos.setEnabled(isCadastrar());

            getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia(), isCadastrar()));

            DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                    .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia());
            getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, isCadastrar()));

            {//Inicio Anexos
                PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
                dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
                dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
                getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, isCadastrar()));
                pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
            }

            getForm().add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

            if (estabelecimento != null) {
                getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
                carregarEstabelecimento(null, estabelecimento);
            }

            getForm().add(new AbstractAjaxButton("btnVoltar") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    try {
                        setResponsePage((Page) classReturn.newInstance());
                    } catch (InstantiationException | IllegalAccessException ex) {
                        Logger.getLogger(RequerimentoRevalidacaoAlvaraPage.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }.setDefaultFormProcessing(false));
            getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
                @Override
                public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                    salvar(target);
                }
            }).setEnabled(isCadastrar()));

            autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                    carregarEstabelecimento(target, object);
                }
            });
            autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                    veiculoEstabelecimentoDTOList.clear();
                    getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setVigilanciaEndereco(null);
                    pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
                    pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
                    atualizarDadosEstabelecimento(target, object);
                    tblVeiculos.setSelectAll(false);
                    tblVeiculos.updateAndClearSelection(target);
                }
            });

            getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getCodigo(), false).setVisible(!isCadastrar()));

            add(getForm());
            carregarVeiculosSelecionados();
        }
    }

    private Form<RequerimentoBaixaVeiculoDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoBaixaVeiculoDTO()));
            if (requerimentoBaixaVeiculo != null) {
                form.getModel().getObject().setRequerimentoBaixaVeiculo(requerimentoBaixaVeiculo);
            } else {
                form.getModel().getObject().setRequerimentoBaixaVeiculo(new RequerimentoBaixaVeiculo());
                form.getModel().getObject().getRequerimentoBaixaVeiculo().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                 if (veiculoEstabelecimentoDTOList == null) {
                    veiculoEstabelecimentoDTOList = new ArrayList<>();
                }
                return veiculoEstabelecimentoDTOList;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();

        VeiculoEstabelecimentoDTO proxy = on(VeiculoEstabelecimentoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("placa"), proxy.getVeiculoEstabelecimento().getPlacaFormatada()));
        columns.add(createColumn(bundle("tipoVeiculo"), proxy.getVeiculoEstabelecimento().getTipoVeiculo()));
        columns.add(createColumn(bundle("renavam"), proxy.getVeiculoEstabelecimento().getRenavam()));
        columns.add(createColumn(bundle("especificacao"), proxy.getVeiculoEstabelecimento().getEspecificacao()));
        columns.add(createColumn(bundle("observacao"), proxy.getVeiculoEstabelecimento().getRestricoes()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<VeiculoEstabelecimentoDTO>() {
            @Override
            public void customizeColumn(final VeiculoEstabelecimentoDTO rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<VeiculoEstabelecimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, VeiculoEstabelecimentoDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgEdicaoVeiculoEstabelecimento(target, modelObject.getVeiculoEstabelecimento());
                    }
                });
            }
        };
    }

    private void initDlgEdicaoVeiculoEstabelecimento(AjaxRequestTarget target, VeiculoEstabelecimento ve){
        if (dlgEdicaoVeiculoEstabelecimento == null) {
            addModal(target, dlgEdicaoVeiculoEstabelecimento = new DlgEdicaoVeiculoEstabelecimento(newModalId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, VeiculoEstabelecimento ve, List<VeiculoEstabelecimento> veiculoEstabelecimentoSelecionadoList) throws ValidacaoException, DAOException {
                    veiculoEstabelecimentoDTOList.clear();
                    tblVeiculos.setSelectAll(false);
                    tblVeiculos.updateAndClearSelection(target);

                    List<VeiculoEstabelecimento> veiculoEstabelecimentos = buscarVeiculosEstabelecimento(ve.getEstabelecimento());
                    ArrayList<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList = new ArrayList<>();
                    for (VeiculoEstabelecimento veiculoEstabelecimento : veiculoEstabelecimentos) {
                        veiculoEstabelecimentoDTOList.add(new VeiculoEstabelecimentoDTO(veiculoEstabelecimento));
                    }

                    veiculoEstabelecimentoDTOList.addAll(veiculoEstabelecimentoDTOList);
                    tblVeiculos.setSelectedObject(veiculoEstabelecimentoDTOList);
                    target.add(tblVeiculos);
                }
            });
        }
        List<VeiculoEstabelecimento> veiculoEstabelecimentoSelecionadoList = new ArrayList<>();
        for (VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO : tblVeiculos.getSelectedObjects()) {
            veiculoEstabelecimentoSelecionadoList.add(veiculoEstabelecimentoDTO.getVeiculoEstabelecimento());
        }

        dlgEdicaoVeiculoEstabelecimento.show(target, ve, veiculoEstabelecimentoSelecionadoList);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if(getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getCpfSolicitante() == null
                && getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getRgSolicitante() == null){
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }

        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        getForm().getModel().getObject().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);

        getForm().getModel().getObject().setLstVeiculoEstabelecimentoDTO(tblVeiculos.getSelectedObjects());
        atualizarDadosComuns();
        RequerimentoVigilancia requerimentoVigilancia = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoBaixaVeiculo(getForm().getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                if(VigilanciaHelper.abrirTelaFinanceiro(object)){
                    RequerimentoVigilanciaDTO dto = new RequerimentoVigilanciaDTO();
                    dto.setRequerimentoVigilancia(object);
                    setResponsePage(new BoletoVigilanciaPage(dto, classReturn));
                } else {
                    try {
                        Page pageReturn = (Page) classReturn.newInstance();
                        getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                        setResponsePage(pageReturn);
                    } catch (InstantiationException | IllegalAccessException e) {
                        Loggable.log.error(e.getMessage(), e);
                    }
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, requerimentoVigilancia);
    }


    private void carregarEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null || getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty()) {
            Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                    .start().getVO();

            getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
        }

        List<VeiculoEstabelecimento> veiculoEstabelecimentos = buscarVeiculosEstabelecimento(estabelecimento);
        ArrayList<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList = new ArrayList<>();
        for (VeiculoEstabelecimento veiculoEstabelecimento : veiculoEstabelecimentos) {
            VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO = new VeiculoEstabelecimentoDTO(veiculoEstabelecimento);
            veiculoEstabelecimentoDTOList.add(veiculoEstabelecimentoDTO);
        }

        this.veiculoEstabelecimentoDTOList.addAll(veiculoEstabelecimentoDTOList);

        if (target != null) {
            atualizarDadosEstabelecimento(target, estabelecimento);
        }

        EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                .addProperties(new HQLProperties(SetorVigilancia.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .start().getVO();

        pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();

        if(estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null
                && estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia() != null){
            EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
            elo.setSetorVigilancia(estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia());
            pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
            if (target != null) {
                pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
            }
        }

        if (estabelecimento.getVigilanciaEndereco() != null && estabelecimento.getVigilanciaEndereco().getCodigo() != null) {
            VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(estabelecimento.getVigilanciaEndereco().getCodigo()).start().getVO();
            form.getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
        }
    }

    private List<VeiculoEstabelecimento> buscarVeiculosEstabelecimento(Estabelecimento estabelecimento){
        return LoadManager.getInstance(VeiculoEstabelecimento.class)
                .addProperties(new HQLProperties(VeiculoEstabelecimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VeiculoEstabelecimento.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(VeiculoEstabelecimento.PROP_SITUACAO, VeiculoEstabelecimento.Situacao.ATIVO.value()))
                .start().getList();
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        target.add(txtEnderecoEstabelecimento);
        target.add(txtCnpjCpfFormatado);
        target.add(txtFantasia);
        target.add(txtProtocolo);
        target.add(tblVeiculos);
    }

    private void carregarRequerimentoBaixaVeiculo(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoBaixaVeiculo = LoadManager.getInstance(RequerimentoBaixaVeiculo.class)
                    .addProperties(new HQLProperties(RequerimentoBaixaVeiculo.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoBaixaVeiculo.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoBaixaVeiculo.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoBaixaVeiculo.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoBaixaVeiculo.setRequerimentoVigilancia(requerimentoVigilancia);

            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv){
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for(RequerimentoVigilanciaAnexo rva : list){
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void carregarVeiculosSelecionados(){
        if(getForm().getModel().getObject().getRequerimentoBaixaVeiculo() != null && getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getCodigo() != null){
            List<RequerimentoBaixaVeiculoItens> requerimentoBaixaVeiculoItensList = RequerimentoLicencaTransporteHelper.carregarRequerimentoBaixaVeiculoItens(requerimentoVigilancia);
            ArrayList<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList = new ArrayList();
            for (RequerimentoBaixaVeiculoItens requerimentoBaixaVeiculoItens : requerimentoBaixaVeiculoItensList) {
                VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO = new VeiculoEstabelecimentoDTO(requerimentoBaixaVeiculoItens.getVeiculoEstabelecimento());
                veiculoEstabelecimentoDTOList.add(veiculoEstabelecimentoDTO);
            }
            if(CollectionUtils.isNotNullEmpty(veiculoEstabelecimentoDTOList)){
                this.veiculoEstabelecimentoDTOList.addAll(veiculoEstabelecimentoDTOList);
                tblVeiculos.setSelectedObject(this.veiculoEstabelecimentoDTOList);
            }
            getForm().getModel().getObject().setRequerimentoBaixaVeiculoItensList(requerimentoBaixaVeiculoItensList);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("baixaVeiculos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    private void atualizarDadosComuns(){
        getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        getForm().getModel().getObject().setRequerimentoVigilanciaFiscalList(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalList());
        getForm().getModel().getObject().setRequerimentoVigilanciaFiscalListExcluir(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalListExcluir());
    }

    private boolean isCadastrar() {
        return !viewOnly && (isNew() || isEditar());
    }

    private boolean isNew() {
        return getForm().getModel().getObject().getRequerimentoBaixaVeiculo() == null || getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getCodigo() == null;
    }

    private boolean isEditar() {
        return getForm().getModel().getObject().getRequerimentoBaixaVeiculo() != null && getForm().getModel().getObject().getRequerimentoBaixaVeiculo().getCodigo() != null;
    }

    private boolean hasPermission() {
        return (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), CadastroEstabelecimentoPage.class.getName()));
    }
}
