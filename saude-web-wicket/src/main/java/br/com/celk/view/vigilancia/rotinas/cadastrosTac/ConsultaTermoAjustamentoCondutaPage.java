package br.com.celk.view.vigilancia.rotinas.cadastrosTac;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.rotinas.autoinfracao.dlg.DlgConcluirTermoAjustamentoConduta;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.EspecificacaoAtoFatoConstitutivo;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.FiscalTermoAjustamentoConduta;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.string.StringValue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConsultaTermoAjustamentoCondutaPage extends ConsultaPage<TermoAjustamentoConduta, List<BuilderQueryCustom.QueryParameter>> {

    private PageParameters parameters;

    private AbstractAjaxButton btnNovo;

    private Long numeroTermoAjustamentoConduta;
    private Profissional profissionalTac;
    private Estabelecimento estabelecimentoTac;
    private VigilanciaPessoa vigilanciaPessoaTac;
    private DatePeriod periodoEmissao;
    private DropDown cbxSituacao;
    private Long situacao;
    private DlgConcluirTermoAjustamentoConduta dlgConcluirTermoAjustamentoConduta;

    public ConsultaTermoAjustamentoCondutaPage() {
        super();
    }

    public ConsultaTermoAjustamentoCondutaPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("numeroTermoAjustamentoConduta"));
        form.add(new AutoCompleteConsultaProfissional("profissionalTac"));
        form.add(new AutoCompleteConsultaEstabelecimento("estabelecimentoTac"));
        form.add(new AutoCompleteConsultaVigilanciaPessoa("vigilanciaPessoaTac"));
        form.add(new PnlDatePeriod("periodoEmissao"));
        form.add(cbxSituacao = DropDownUtil.getIEnumDropDown("situacao", TermoAjustamentoConduta.Status.values(), true));
        cbxSituacao.addAjaxUpdateValue();
        cbxSituacao.setOutputMarkupId(true);

        setExibeExpandir(true);

        getLinkNovo().setVisible(false);

        getControls().add(btnNovo = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                CadastroTermoAjustamentoCondutaPage cadastroTermoAjustamentoCondutaPage = new CadastroTermoAjustamentoCondutaPage(getPageParameters());
                setResponsePage(cadastroTermoAjustamentoCondutaPage);
            }
        });

        btnNovo.add(new AttributeModifier("class", "doc-new"));
        btnNovo.add(new AttributeModifier("value", bundle("novo")));
        btnNovo.add(new AttributeModifier("style", "margin-left: 5px;"));

        setParameters(this.getPageParameters());

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TermoAjustamentoConduta proxy = on(TermoAjustamentoConduta.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("numeracao"), proxy.getNumeroFormatado()));
        columns.add(createColumn(bundle("estabelecimentoPessoa"), proxy.getEstabelecimentoPessoa()));
        columns.add(createSortableColumn(bundle("dataEmissao"), proxy.getDataEmissao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getStatusDescricao()));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TermoAjustamentoConduta>() {

            @Override
            public void customizeColumn(final TermoAjustamentoConduta rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TermoAjustamentoConduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAjustamentoConduta modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoAjustamentoCondutaPage(modelObject, getPageParameters()));
                    }
                }).setEnabled(TermoAjustamentoConduta.Status.EM_ANDAMENTO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TermoAjustamentoConduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAjustamentoConduta modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VigilanciaFacade.class).removerTermoAjustamentoConduta(rowObject);
                        getPageableTable().populate();
                        getPageableTable().update(target);
                    }
                }).setEnabled(TermoAjustamentoConduta.Status.EM_ANDAMENTO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TermoAjustamentoConduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAjustamentoConduta modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTermoAjustamentoCondutaPage(modelObject, true, getPageParameters()));
                    }
                });

                ModelActionLinkPanel acaoConcluir = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<TermoAjustamentoConduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAjustamentoConduta modelObject) throws ValidacaoException, DAOException {
                    if (dlgConcluirTermoAjustamentoConduta == null) {
                        addModal(target, dlgConcluirTermoAjustamentoConduta = new DlgConcluirTermoAjustamentoConduta(newModalId()) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target, TermoAjustamentoConduta termoAjustamentoConduta) throws ValidacaoException, DAOException {
                                BOFactoryWicket.getBO(VigilanciaFacade.class).finalizarTermoAjustamentoConduta(termoAjustamentoConduta);
                                getPageableTable().populate();
                                getPageableTable().update(target);
                            }
                        });
                    }
                        dlgConcluirTermoAjustamentoConduta.show(target, modelObject);
                    }
                });
                acaoConcluir.setEnabled(TermoAjustamentoConduta.Status.EM_ANDAMENTO.value().equals(rowObject.getSituacao()));
                acaoConcluir.setTitleBundleKey("finalizarArquivar");

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<TermoAjustamentoConduta>() {
                    @Override
                    public DataReport action(TermoAjustamentoConduta modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoComprovanteTermoAjustamentoConduta(modelObject.getCodigo(), modelObject.getNumeroFormatado(), modelObject.getSituacao(), modelObject.getFlagFinalizado());
                    }
                });
            }
        };
    }

    private void setParameters(PageParameters pageParameters) {
        this.parameters = pageParameters;

        StringValue situacaoParam = parameters.get("situacao");
        if (!situacaoParam.isEmpty()) {
            situacao = situacaoParam.toLong();
        }
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TermoAjustamentoConduta.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(TermoAjustamentoConduta.class).getProperties(),
                        new HQLProperties(Estabelecimento.class, TermoAjustamentoConduta.PROP_ESTABELECIMENTO).getProperties(),
                        new HQLProperties(VigilanciaPessoa.class, TermoAjustamentoConduta.PROP_VIGILANCIA_PESSOA).getProperties(),
                        new HQLProperties(VigilanciaEndereco.class, TermoAjustamentoConduta.PROP_VIGILANCIA_ENDERECO).getProperties(),
                        new HQLProperties(AutoInfracao.class, TermoAjustamentoConduta.PROP_AUTO_INFRACAO).getProperties(),
                        new HQLProperties(Usuario.class, TermoAjustamentoConduta.PROP_USUARIO).getProperties()
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(TermoAjustamentoConduta.PROP_NUMERO), false);
            }

            @Override
            public List getInterceptors() {

                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (profissionalTac != null) {
                            StringBuilder where = new StringBuilder();
                            where.append("select 1 ");
                            where.append("from FiscalTermoAjustamentoConduta fta ");
                            where.append("where fta.termoAjustamentoConduta.codigo = ").append(alias).append(".codigo");
                            where.append(" and fta.profissional.codigo = ").append(profissionalTac.getCodigo());
                            where.insert(0, "exists(").append(")");
                            hql.addToWhereWhithAnd(where.toString());
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_NUMERO, this.numeroTermoAjustamentoConduta));
        parameters.add(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_ESTABELECIMENTO, estabelecimentoTac));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TermoAjustamentoConduta.PROP_USUARIO, Usuario.PROP_PROFISSIONAL), this.profissionalTac));
        parameters.add(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_VIGILANCIA_PESSOA, vigilanciaPessoaTac));
        parameters.add(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_SITUACAO, this.situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(TermoAjustamentoConduta.PROP_DATA_EMISSAO, this.periodoEmissao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTermoAjustamentoCondutaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consulta_termo_ajustamento_conduta");
    }
}
