package br.com.celk.view.materiais.medicamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.medicamento.tabbedpanel.*;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMedicamentoDTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.EloProdutoBrasindice;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class CadastroMedicamentoPage extends BasePage {

    public CadastroMedicamentoPage() {
        this(null);
    }

    public CadastroMedicamentoPage(CadastroMedicamentoDTO cadastroMedicamentosDTO) {
        this(cadastroMedicamentosDTO, false);
    }

    public CadastroMedicamentoPage(CadastroMedicamentoDTO cadastroMedicamentosDTO, boolean viewOnly) {
        init(cadastroMedicamentosDTO, viewOnly);
    }

    private void init(CadastroMedicamentoDTO cadastroMedicamentosDTO, boolean viewOnly) {
        if (cadastroMedicamentosDTO == null) {
            cadastroMedicamentosDTO = getNewInstance();
        }

        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new DadosMedicamentoTab(panelId, cadastroMedicamentosDTO);
            }
        });
        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new ProgramaSaudeMedicamentoTab(panelId, cadastroMedicamentosDTO);
            }
        });

        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new TipoViaMedicamentoTab(panelId, cadastroMedicamentosDTO);
            }
        });

        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new ConvenioTab(panelId, cadastroMedicamentosDTO);
            }
        });

        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new BnafarTab(panelId, cadastroMedicamentosDTO);
            }
        });

        tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

            @Override
            public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                return new InteracaoMedicamentosaTab(panelId, cadastroMedicamentosDTO);
            }
        });
        if (cadastroMedicamentosDTO.getProduto() != null
                && cadastroMedicamentosDTO.getProduto().getFlagEmiteLme() != null
                && RepositoryComponentDefault.SIM_LONG.equals(cadastroMedicamentosDTO.getProduto().getFlagEmiteLme())) {
            tabs.add(new CadastroTab<CadastroMedicamentoDTO>(cadastroMedicamentosDTO) {

                @Override
                public ITabPanel<CadastroMedicamentoDTO> newTabPanel(String panelId, CadastroMedicamentoDTO cadastroMedicamentosDTO) {
                    return new CidDocumentosTab(panelId, cadastroMedicamentosDTO);
                }
            });
        }
        add(new CadastroMedicamentoTabbedPanel("wizard", cadastroMedicamentosDTO, viewOnly, tabs));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroMedicamento");
    }

    private CadastroMedicamentoDTO getNewInstance() {
        CadastroMedicamentoDTO object = new CadastroMedicamentoDTO();
        object.setProduto(new Produto());
        object.setProdutoProgramaSaudes(new ArrayList());
        object.setTipoViaMedicamentos(new ArrayList());
        object.setRiscoProdutoList(new ArrayList());
        object.setEloProdutoBrasindice(new EloProdutoBrasindice());

        return object;
    }
}
