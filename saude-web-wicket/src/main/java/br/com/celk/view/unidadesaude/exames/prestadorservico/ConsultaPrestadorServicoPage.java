package br.com.celk.view.unidadesaude.exames.prestadorservico;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.celk.view.unidadesaude.exames.prestadorservico.cotaestabelecimento.CadastroCotaEstabelecimentosPage;
import br.com.ksisolucoes.bo.prestadorservico.interfaces.dto.QueryPagerConsultaPrestadorServicoDTO;
import br.com.ksisolucoes.bo.prestadorservico.interfaces.dto.QueryPagerConsultaPrestadorServicoDTOparam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaPrestadorServicoPage extends ConsultaPage<ExamePrestador, QueryPagerConsultaPrestadorServicoDTOparam> {

    private TipoExame tipoExame;
    private TipoExame tipoExameSecundario;
    private String prestadorServico;
    private Date dataContrato;
    private String numeroContrato;
    private QueryPagerProvider<QueryPagerConsultaPrestadorServicoDTO, QueryPagerConsultaPrestadorServicoDTOparam> dataProvider;

    public ConsultaPrestadorServicoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("prestadorServico"));
        form.add(new AutoCompleteConsultaTipoExame("tipoExame"));
        form.add(new AutoCompleteConsultaTipoExame("tipoExameSecundario"));
        form.add(new InputField<String>("numeroContrato"));
        form.add(new DateChooser("dataContrato"));


        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(QueryPagerConsultaPrestadorServicoDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prestadorServico"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupoProcedimento"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TIPO_EXAME, TipoExame.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupoProcedimentoSecundario"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TIPO_EXAME_SECUNDARIO, TipoExame.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoTeto"), QueryPagerConsultaPrestadorServicoDTO.PROP_TIPO_TETO, QueryPagerConsultaPrestadorServicoDTO.DESCRICAO_TIPO_TETO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("teto"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TETO_FINANCEIRO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("numeroContrato"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_ULTIMO_CONTRATO, ExamePrestadorContrato.PROP_NUMERO_CONTRATO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("validadeContrato"), VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_ULTIMO_CONTRATO, ExamePrestadorContrato.PROP_DATA_VALIDADE)));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<QueryPagerConsultaPrestadorServicoDTO>() {
            @Override
            public void customizeColumn(QueryPagerConsultaPrestadorServicoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<QueryPagerConsultaPrestadorServicoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaPrestadorServicoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPrestadorServicoStep1Page(modelObject.getExamePrestador().getCodigo()));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<QueryPagerConsultaPrestadorServicoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaPrestadorServicoDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject.getExamePrestador());
                        getPageableTable().update(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<QueryPagerConsultaPrestadorServicoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaPrestadorServicoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesPrestadorServicoPage(modelObject.getExamePrestador().getCodigo()));
                    }
                });
                addAction(ActionType.ESTABELECIMENTOS, rowObject, new IModelAction<QueryPagerConsultaPrestadorServicoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaPrestadorServicoDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCotaEstabelecimentosPage(modelObject.getExamePrestador().getCodigo()));
                    }
                }).setTitleBundleKey("cotaEstabelecimento")
                        .setIcon(Icon.DB);
            }
        };
    }


    @Override
    public IPagerProvider<QueryPagerConsultaPrestadorServicoDTO, QueryPagerConsultaPrestadorServicoDTOparam> getPagerProviderInstance() {
        if (this.dataProvider == null) {
            this.dataProvider = new QueryPagerProvider<QueryPagerConsultaPrestadorServicoDTO, QueryPagerConsultaPrestadorServicoDTOparam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<QueryPagerConsultaPrestadorServicoDTOparam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(ExameFacade.class).consultarPrestadorServicoPager(dataPaging);
                }

                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(VOUtils.montarPath(QueryPagerConsultaPrestadorServicoDTO.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_CODIGO), true);
                }

                @Override
                public void customizeParam(QueryPagerConsultaPrestadorServicoDTOparam param) {
                    param.setPropSort(getSort().getProperty());
                    param.setAscending(getSort().isAscending());
                }
            };
        }

        return this.dataProvider;
    }


    @Override
    public QueryPagerConsultaPrestadorServicoDTOparam getParameters() {
        QueryPagerConsultaPrestadorServicoDTOparam param = new QueryPagerConsultaPrestadorServicoDTOparam();
        param.setPrestadorServico(prestadorServico);
        param.setTipoExame(tipoExame);
        param.setTipoExameSec(tipoExameSecundario);
        param.setDataContrato(dataContrato);
        param.setNumeroContrato(numeroContrato);

        param.getConfigureParam().addProperty(VOUtils.montarPath(ExamePrestador.PROP_PRESTADOR, Empresa.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(ExamePrestador.PROP_TIPO_EXAME));
        param.getConfigureParam().addProperty(VOUtils.montarPath(ExamePrestador.PROP_TIPO_EXAME_SECUNDARIO));

        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPrestadorServicoStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPrestadorServico");
    }

}
