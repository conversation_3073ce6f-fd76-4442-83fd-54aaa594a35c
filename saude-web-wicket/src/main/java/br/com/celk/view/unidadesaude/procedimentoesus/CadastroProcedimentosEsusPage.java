package br.com.celk.view.unidadesaude.procedimentoesus;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.ProcedimentoEloEsus;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus.FichaIntegracao;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
public class CadastroProcedimentosEsusPage extends CadastroPage<ProcedimentoEsus> {

    private WebMarkupContainer containerElos;
    private Table<ProcedimentoEloEsus> tblElos;
    private List<ProcedimentoEloEsus> elosList = new ArrayList();
    private CompoundPropertyModel<ProcedimentoEloEsus> modelElos;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;

    private DropDown dropDownFichaInscricao;
    private DropDown dropDownProcedimentoConsolidado;
    private DropDown dropDownCodigoClassificacao;
    private InputField txtCodigoEsus;

    public CadastroProcedimentosEsusPage(ProcedimentoEsus object, boolean viewOnly) {
        super(object, viewOnly);
        carregarElos(object);
    }

    public CadastroProcedimentosEsusPage(PageParameters parameters) {
        super(parameters);
    }

    public CadastroProcedimentosEsusPage(ProcedimentoEsus object) {
        super(object);
    }

    @Override
    public void init(Form<ProcedimentoEsus> form) {
        form.add(new RequiredInputField(ProcedimentoEsus.PROP_DESCRICAO_PROCEDIMENTO));
        form.add(dropDownFichaInscricao = DropDownUtil.getIEnumDropDown(ProcedimentoEsus.PROP_FICHA_INTEGRACAO, ProcedimentoEsus.FichaIntegracao.values(), true, true));
        form.add(dropDownProcedimentoConsolidado = DropDownUtil.getIEnumDropDown(ProcedimentoEsus.PROP_PROCEDIMENTO_CONSOLIDADO, ProcedimentoEsus.ProcedimentoConsolidado.values(), true));
        form.add(dropDownCodigoClassificacao = DropDownUtil.getIEnumDropDown(ProcedimentoEsus.PROP_CODIGO_CLASSIFICACAO, ProcedimentoEsus.Classificacao.values(), true));
        form.add(txtCodigoEsus = new InputField(ProcedimentoEsus.PROP_CODIGO_ESUS));

        if (ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value().equals(form.getModelObject().getFichaIntegracao())) {
            dropDownCodigoClassificacao.setEnabled(false);
            txtCodigoEsus.setEnabled(false);
            dropDownCodigoClassificacao.setRequired(false);
            txtCodigoEsus.setRequired(false);
        } else {
            dropDownProcedimentoConsolidado.setEnabled(false);
            dropDownProcedimentoConsolidado.setRequired(false);
        }

        dropDownFichaInscricao.add(new AjaxFormComponentUpdatingBehavior("onChange") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value().equals(dropDownFichaInscricao.getComponentValue())) {
                    dropDownCodigoClassificacao.setEnabled(false);
                    txtCodigoEsus.setEnabled(false);
                    dropDownCodigoClassificacao.setRequired(false);
                    txtCodigoEsus.setRequired(false);
                    txtCodigoEsus.limpar(art);
                    dropDownCodigoClassificacao.limpar(art);
                    dropDownProcedimentoConsolidado.setEnabled(true);

                } else {
                    dropDownCodigoClassificacao.setEnabled(true);
                    dropDownProcedimentoConsolidado.setRequired(false);
                    txtCodigoEsus.setEnabled(true);
                    dropDownProcedimentoConsolidado.setEnabled(false);
                    dropDownProcedimentoConsolidado.limpar(art);
                }
                art.add(dropDownCodigoClassificacao);
                art.add(txtCodigoEsus);
                art.add(dropDownProcedimentoConsolidado);
            }
        });

        form.add(containerElos = new WebMarkupContainer("containerElos", modelElos = new CompoundPropertyModel(new ProcedimentoEloEsus())));
        containerElos.setOutputMarkupId(true);
        containerElos.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProcedimento(target);
            }
        }.setDefaultFormProcessing(false));
        containerElos.add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(ProcedimentoEloEsus.PROP_PROCEDIMENTO));
        containerElos.add(tblElos = new Table("tblElos", getColumns(), getCollectionProvider()));
        tblElos.populate();
    }

    private void adicionarProcedimento(AjaxRequestTarget target) throws ValidacaoException {
        ProcedimentoEloEsus elo = (ProcedimentoEloEsus) SerializationUtils.clone(modelElos.getObject());
        validarAdicionar(elo);
        elosList.add(elo);
        tblElos.update(target);
        limparContainer(target);
    }

    private void limparContainer(AjaxRequestTarget target) {
        modelElos.setObject(new ProcedimentoEloEsus());
        autoCompleteConsultaProcedimento.limpar(target);
        target.add(containerElos);
    }

    private void validarAdicionar(ProcedimentoEloEsus elo) throws ValidacaoException {
        if (elo != null && elo.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimentos"));
        }

        for (ProcedimentoEloEsus item : elosList) {
            if (item.getProcedimento().equals(elo.getProcedimento())) {
                throw new ValidacaoException(bundle("msgProcedimentoXJaAdicionado", elo.getProcedimento().getDescricao()));
            }
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        ProcedimentoEloEsus proxy = on(ProcedimentoEloEsus.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimento().getDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ProcedimentoEloEsus>() {
            @Override
            public void customizeColumn(ProcedimentoEloEsus rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProcedimentoEloEsus>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProcedimentoEloEsus modelObject) throws ValidacaoException, DAOException {
                        removerProcedimento(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerProcedimento(AjaxRequestTarget target, ProcedimentoEloEsus elo) {
        for (int i = 0; i < elosList.size(); i++) {
            ProcedimentoEloEsus procedimentoEloEsus = elosList.get(i);
            if (procedimentoEloEsus.getProcedimento().equals(elo.getProcedimento())) {
                elosList.remove(i);
                break;
            }
        }

        tblElos.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return elosList;
            }
        };
    }

    @Override
    public Object salvar(ProcedimentoEsus object) throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(EsusFacade.class).salvarProcedimentosEsus(object, elosList);
    }

    @Override
    public Class<ProcedimentoEsus> getReferenceClass() {
        return ProcedimentoEsus.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaProcedimentosEsusPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroProcedimentoEsus");
    }

    private void carregarElos(ProcedimentoEsus procedimentoEsus) {
        elosList = LoadManager.getInstance(ProcedimentoEloEsus.class)
                .addProperties(new HQLProperties(ProcedimentoEloEsus.class).getProperties())
                .addProperties(new HQLProperties(Procedimento.class, ProcedimentoEloEsus.PROP_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ProcedimentoEloEsus.PROP_PROCEDIMENTO_ESUS, procedimentoEsus))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ProcedimentoEloEsus.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();
    }
}
