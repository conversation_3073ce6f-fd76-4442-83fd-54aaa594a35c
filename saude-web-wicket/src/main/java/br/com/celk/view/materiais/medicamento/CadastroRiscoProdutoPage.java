package br.com.celk.view.materiais.medicamento;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TipoInfusao;
import br.com.ksisolucoes.vo.esus.ExameEsus;
import br.com.ksisolucoes.vo.materiais.RiscoProduto;
import org.apache.wicket.markup.html.form.Form;

public class CadastroRiscoProdutoPage  extends CadastroPage<RiscoProduto> {

    private InputField txtDescricao;
    private DropDown<Long> dropDownStatus;

    public CadastroRiscoProdutoPage() {

    }

    public CadastroRiscoProdutoPage(RiscoProduto object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<RiscoProduto> form) {
        form.add(txtDescricao = new RequiredInputField<String>(RiscoProduto.PROP_DESCRICAO));
        form.add(getDropDownStatus());
    }

    @Override
    public Object salvar(RiscoProduto object) throws DAOException, ValidacaoException {
        return super.salvar(object);
    }

    @Override
    public Class<RiscoProduto> getReferenceClass() {
        return RiscoProduto.class;
    }

    private DropDown getDropDownStatus() {
        DropDown dropDown = new DropDown(RiscoProduto.PROP_STATUS);
        for (RiscoProduto.Status status : RiscoProduto.Status.values()) {
            dropDown.addChoice(status.value(), status.descricao());
        }
        return dropDown;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaRiscoProdutoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastro_risco_produto");
    }
}
