package br.com.celk.view.cadsus.usuariocadsus.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.AcompanhanteUsuarioCadsusHospitalDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConsultaUsuarioCadsusAcompanhante extends Window{
    
    private PnlConsultaUsuarioCadsusAcompanhante pnlConsultaUsuarioCadsusAcompanhante;
    
    public DlgConsultaUsuarioCadsusAcompanhante(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("dadosAcompanhante");
            }
        });
                
        setInitialWidth(900);
        setInitialHeight(470);
        setResizable(true);
        
        setContent(pnlConsultaUsuarioCadsusAcompanhante = new PnlConsultaUsuarioCadsusAcompanhante(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public void show(AjaxRequestTarget target, AcompanhanteUsuarioCadsusHospitalDTO dto){
        show(target);
        pnlConsultaUsuarioCadsusAcompanhante.setDTO(target, dto);
    }    
}