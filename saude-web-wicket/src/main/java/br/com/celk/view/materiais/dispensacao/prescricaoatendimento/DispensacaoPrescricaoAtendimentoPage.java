package br.com.celk.view.materiais.dispensacao.prescricaoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoObject;
import br.com.celk.component.dialog.DlgDisabledArea;
import br.com.celk.component.dialog.DlgImpressao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.dialog.DlgDetalhesSuspencaoPrescricaoInternaInternacao;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog.DlgAdicionarDispensacaoPrescricaoItem;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog.DlgRelacionarMedicamentoNaoCadastradoComProduto;
import br.com.celk.view.materiais.dispensacao.prescricaoatendimento.tablerow.DispensacaoTableRow;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoItemDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.dto.QueryMovimentoGrupoEstoqueItemDTOParam;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.QueryImpressaoDispensacaoPrescricaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class DispensacaoPrescricaoAtendimentoPage extends BasePage {

    private CompoundPropertyModel<DispensacaoMedicamentoItem> modelDispensacaoItem;
    private Form<DispensacaoMedicamento> form;
    private DlgAdicionarDispensacaoPrescricaoItem dlgAdicionarItem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Table<DispensacaoMedicamentoItemDTO> tblItems;
    private DlgImpressao dlgImpressao;
    private DlgConfirmacaoObject<DispensacaoMedicamentoItem> dlgConfirmacao;
    private CheckBoxLongValue checkBoxPrimeiroTurno;
    private CheckBoxLongValue checkBoxSegundoTurno;
    private CheckBoxLongValue checkBoxTerceiroTurno;
    private WebMarkupContainer containerTurno;
    private Receituario receituario;
    private List<DispensacaoMedicamentoItemDTO> itens = new ArrayList<DispensacaoMedicamentoItemDTO>();
    private Long dispensacaoPorTurno;
    private DlgDisabledArea dlgJustificativa;
    private DlgRelacionarMedicamentoNaoCadastradoComProduto dlgRelacionarMedicamento;
    private boolean validaPrecoUnitario = false;
    private LongField txtCodigoBarras;
    private Long codigoBarrasProduto;

    private WebMarkupContainer containerRemoveCodBarras;
    private LongField txtCodigoBarrasRemover;
    private Long codigoBarrasRemover;
    private Page pageVoltar;

    private Empresa empresaBaixa;

    private DlgDisabledArea dlgJustificativaAdep;
    private DlgDetalhesSuspencaoPrescricaoInternaInternacao dlgDetalhesSuspencaoPrescricaoInternaInternacao;
    private String utilizarLeitoraCodigoBarrasProduto;
    private String validarSituacaoCodigoBarrasProduto;

    public DispensacaoPrescricaoAtendimentoPage(DispensacaoMedicamento dispensacaoMedicamento, Empresa empresaBaixa) {
        carregaEmpresaBaixa(empresaBaixa);
        dispensacaoPorTurno = dispensacaoMedicamento.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDispensacaoPorTurno();
        init(dispensacaoMedicamento);
    }

    public DispensacaoPrescricaoAtendimentoPage(Receituario receituario, Empresa empresaBaixa) {
        carregaEmpresaBaixa(empresaBaixa);
        carregaReceituario(receituario);
        init(iniciarDispensacao(receituario));
    }

    public DispensacaoPrescricaoAtendimentoPage(Receituario receituario, Page pageVoltar, Empresa empresaBaixa) {
        carregaEmpresaBaixa(empresaBaixa);
        this.pageVoltar = pageVoltar;
        carregaReceituario(receituario);
        init(iniciarDispensacao(receituario));
    }

    private void init(DispensacaoMedicamento dispensacaoMedicamento) {
        form = new Form<DispensacaoMedicamento>("form", new CompoundPropertyModel(dispensacaoMedicamento));

        try {
            this.utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            this.validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_NOME_SOCIAL)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamento.PROP_ATENDIMENTO, Atendimento.PROP_CONVENIO, Convenio.PROP_DESCRICAO)));

        containerTurno = new WebMarkupContainer("containerTurno");
        containerTurno.setOutputMarkupId(true);
        containerTurno.add(checkBoxPrimeiroTurno = (CheckBoxLongValue) new CheckBoxLongValue("primeiroTurno", Receituario.PRIMEIRO_TURNO, new Model<Long>()));
        containerTurno.add(checkBoxSegundoTurno = (CheckBoxLongValue) new CheckBoxLongValue("segundoTurno", Receituario.SEGUNDO_TURNO, new Model<Long>()));
        containerTurno.add(checkBoxTerceiroTurno = (CheckBoxLongValue) new CheckBoxLongValue("terceiroTurno", Receituario.TERCEIRO_TURNO, new Model<Long>()));
        form.add(containerTurno);
        if (receituario != null) {
            if (receituario.getSomatorioTurno() != null) {
                CheckBoxUtil.selecionarSomatorio(Arrays.asList(checkBoxPrimeiroTurno, checkBoxSegundoTurno, checkBoxTerceiroTurno), receituario.getSomatorioTurno());
                if (checkBoxPrimeiroTurno.getModelObject() != null) {
                    checkBoxPrimeiroTurno.setEnabled(false);
                }
                if (checkBoxSegundoTurno.getModelObject() != null) {
                    checkBoxSegundoTurno.setEnabled(false);
                }
                if (checkBoxTerceiroTurno.getModelObject() != null) {
                    checkBoxTerceiroTurno.setEnabled(false);
                }
            }
        }
        containerTurno.setVisible(RepositoryComponentDefault.SIM_LONG.equals(dispensacaoPorTurno) && receituario != null);

        WebMarkupContainer containerItem = new WebMarkupContainer("containerItem", modelDispensacaoItem = new CompoundPropertyModel(new DispensacaoMedicamentoItem()));
        containerItem.setOutputMarkupId(true);

        containerItem.add(txtCodigoBarras = new LongField("codigoBarrasProduto", new PropertyModel(this, "codigoBarrasProduto")));
        txtCodigoBarras.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                try {
                    adicionarViaCodigoBarras(art);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                    warn(art, ex.getMessage());
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= 0";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtCodigoBarras.getMarkupId(), condition));
            }
        });

        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO)) {
            @Override
            public String[] getPropertiesLoad() {
                String[] propertiesDefault = super.getPropertiesLoad();
                return VOUtils.mergeProperties(propertiesDefault, new HQLProperties(Unidade.class, Produto.PROP_UNIDADE).getProperties());
            }
        }.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO)
                .setEmpresas(Arrays.asList(ApplicationSession.get().getSession().<Empresa>getEmpresa())));

        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                try {
                    if (validarAcaoBtnAdicionarItem(target, modelDispensacaoItem.getObject())) {
                        abriDialogAdicionar(target, modelDispensacaoItem.getObject());
                        autoCompleteConsultaProduto.limpar(target);
                    }
                } catch (ValidacaoException ex) {
                    info(target, ex.getMessage());
                } catch (DAOException ex) {
                    Logger.getLogger(DispensacaoPrescricaoAtendimentoPage.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        });

        containerRemoveCodBarras = new WebMarkupContainer("containerRemoveCodBarras");
        containerRemoveCodBarras.setVisible(false); // TODO: Será revisto futuramente...
        containerRemoveCodBarras.setOutputMarkupId(true);
        containerRemoveCodBarras.add(txtCodigoBarrasRemover = new LongField("codigoBarrasRemover", new PropertyModel<Long>(this, "codigoBarrasRemover")));
        txtCodigoBarrasRemover.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                removerCodigoBarras(target, codigoBarrasRemover);
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
        });
        containerRemoveCodBarras.add(new AbstractAjaxButton("btnRemover") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                target.add(txtCodigoBarrasRemover);
                target.appendJavaScript(JScript.showFieldset(containerRemoveCodBarras));
                target.appendJavaScript(JScript.focusComponent(txtCodigoBarrasRemover));
            }
        });
        containerItem.add(containerRemoveCodBarras);

        containerItem.add(tblItems = new Table("tableItems", getColumnsItems(), getCollectionProviderItems()) {
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new DispensacaoTableRow(id, index, model);
            }
        });
        tblItems.populate();

        form.add(containerItem);

        addModal(dlgAdicionarItem = new DlgAdicionarDispensacaoPrescricaoItem(newModalId()) {
            @Override
            public void adicionar(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException {
                adicionarItem(target, itemOrigem, itemDestino);
            }
        });

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        add(form);

        addModal(dlgImpressao = new DlgImpressao(newModalId(), bundle("dispensacaoSalvaSucesso")) {
            @Override
            public DataReport onImprimir() throws ReportException {
                return imprimir();
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                confirmar(target);
            }
        });

        addModal(dlgConfirmacao = new DlgConfirmacaoObject<DispensacaoMedicamentoItem>(newModalId(), bundle("produtoJaAdicionadoAdicionarMesmoAssim")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItem modelObject) throws ValidacaoException, DAOException {
                abriDialogAdicionar(target, modelObject);
            }
        });

        addModal(dlgJustificativa = new DlgDisabledArea(newModalId(), bundle("justificativaNaoPadronizado")));

        addModal(dlgRelacionarMedicamento = new DlgRelacionarMedicamentoNaoCadastradoComProduto(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItem dmi) throws ValidacaoException, DAOException {
                dlgAdicionarItem.setObject(target, dmi, empresaBaixa);
                dlgAdicionarItem.show(target);
            }
        });
    }

    private void removerCodigoBarras(AjaxRequestTarget target, Long codigoBarrasRemover) {
        if (buscaERemove(codigoBarrasRemover)) {
//            txtCodigoBarrasRemover.limpar(target);
            target.appendJavaScript(JScript.hideFieldset(containerRemoveCodBarras));
            tblItems.update(target);
        } else {
//            txtCodigoBarrasRemover.limpar(target);
            target.appendJavaScript(JScript.hideFieldset(containerRemoveCodBarras));
            warn(target, bundle("codigoBarrasNaoEncontrado"));
        }
    }

    private boolean buscaERemove(Long codigoBarras) {
        for (DispensacaoMedicamentoItemDTO item : itens) {
            DispensacaoMedicamentoItemDTO dmi = buscaLote(item, codigoBarras);
            if (dmi != null) {
                dmi.getDispensacaoMedicamentoItem().setQuantidadeDispensada(dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada() - 1D);
                if (dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada() < 1D) {
                    itens.remove(dmi);
                }
                return true;
            }

            dmi = buscaSemLote(item, codigoBarras);
            if (dmi != null) {
                dmi.getDispensacaoMedicamentoItem().setQuantidadeDispensada(dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada() - 1D);
                if (dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada() < 1D) {
                    itens.remove(dmi);
                }
                return true;
            }
        }
        return false;
    }

    private DispensacaoMedicamentoItemDTO buscaSemLote(DispensacaoMedicamentoItemDTO dmi, Long codigoBarras) {
        List<CodigoBarrasProduto> lstCodigoBarrasProduto = dmi.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto();
        if (!lstCodigoBarrasProduto.isEmpty()) {
            for (CodigoBarrasProduto cbp : lstCodigoBarrasProduto) {
                if (cbp.getCodigo().equals(codigoBarras)) {
                    dmi.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().remove(cbp);
                    return dmi;
                }
            }
        }
        return null;
    }

    private DispensacaoMedicamentoItemDTO buscaLote(DispensacaoMedicamentoItemDTO dmi, Long codigoBarras) {
        List<MovimentoGrupoEstoqueItemDTO> lstMovimentoGrupoEstoqueItemDTO = dmi.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList();
        if (lstMovimentoGrupoEstoqueItemDTO != null) {
            for (MovimentoGrupoEstoqueItemDTO dto : lstMovimentoGrupoEstoqueItemDTO) {
                List<CodigoBarrasProduto> lstCodigoBarrasProduto = dto.getLstCodigoBarrasProduto();
                if (!lstCodigoBarrasProduto.isEmpty()) {
                    for (CodigoBarrasProduto cbp : lstCodigoBarrasProduto) {
                        if (cbp.getCodigo().equals(codigoBarras)) {
                            dto.getLstCodigoBarrasProduto().remove(cbp);
                            dto.setQuantidade(dto.getQuantidade() - 1D);
                            if (dto.getQuantidade() < 1D && dmi.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().size() > 1) {
                                dmi.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().remove(dto);
                            }
                            return dmi;
                        }
                    }
                }
            }
        }
        return null;
    }

    private List<IColumn> getColumnsItems() {
        List<IColumn> columns = new ArrayList<IColumn>();

        DispensacaoMedicamentoItemDTO proxy = on(DispensacaoMedicamentoItemDTO.class);

        columns.add(getCustomColumnItems());
        columns.add(createColumn(bundle("produto"), proxy.getDescricaoProduto()));
        columns.add(createColumn(bundle("un"), proxy.getDispensacaoMedicamentoItem().getProduto().getUnidade().getUnidade()));
        columns.add(createColumn(bundle("lotes"), proxy.getDispensacaoMedicamentoItem().getDescricaoLote()));
        columns.add(createColumn(bundle("via"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getTipoViaMedicamento().getDescricao()));
        columns.add(createColumn(bundle("posologia"), proxy.getDispensacaoMedicamentoItem().getReceituarioItem().getPosologia()));
        columns.add(createColumn(bundle("dispensado"), proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada()));

        return columns;
    }

    private IColumn getCustomColumnItems() {
        return new MultipleActionCustomColumn<DispensacaoMedicamentoItemDTO>() {
            @Override
            public void customizeColumn(DispensacaoMedicamentoItemDTO rowObject) {
                Long statusReceituarioItem = ReceituarioItem.Status.NORMAL.value();
                if (rowObject.getDispensacaoMedicamentoItem().getReceituarioItem() != null) {
                    statusReceituarioItem = rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getStatus();
                }

                addAction(ActionType.EDITAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        if (dmi.getDispensacaoMedicamentoItem().getProduto() != null) {
                            dlgAdicionarItem.setObject(target, dmi.getDispensacaoMedicamentoItem(), empresaBaixa);
                            dlgAdicionarItem.show(target);
                        } else {
                            dlgRelacionarMedicamento.show(target, dmi.getDispensacaoMedicamentoItem());
                        }
                    }
                }).setVisible(!ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO modelObject) throws ValidacaoException, DAOException {
                        removerItem(modelObject);
                        tblItems.update(target);
                    }
                }).setVisible(!ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        mostrarDlgJustificativaAdep(target, dmi);
                    }

                }).setTitleBundleKey("justificativaAdep")
                        .setVisible(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem() != null && rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativaAdep() != null
                                && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO modelObject) throws ValidacaoException, DAOException {
                        dlgJustificativa.show(target, modelObject.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativa());
                    }
                }).setTitleBundleKey("justificativaNaoPadronizado")
                        .setVisible(rowObject.getDispensacaoMedicamentoItem().getReceituarioItem() != null && rowObject.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativa() != null
                                && !ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

                addAction(ActionType.LAUDAR, rowObject, new IModelAction<DispensacaoMedicamentoItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) throws ValidacaoException, DAOException {
                        mostrarDlgJustificativaSuspencao(target, dmi);
                    }
                }).setTitleBundleKey("justificativa").setVisible(ReceituarioItem.Status.SUSPENSO.value().equals(statusReceituarioItem));

            }
        };
    }

    private void mostrarDlgJustificativaSuspencao(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgDetalhesSuspencaoPrescricaoInternaInternacao == null) {
            addModal(target, dlgDetalhesSuspencaoPrescricaoInternaInternacao = new DlgDetalhesSuspencaoPrescricaoInternaInternacao(newModalId()) {
            });
        }
        dlgDetalhesSuspencaoPrescricaoInternaInternacao.showDialog(target, dmi.getDispensacaoMedicamentoItem().getReceituarioItem());
    }

    private void mostrarDlgJustificativaAdep(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO dmi) {
        if (dlgJustificativaAdep == null) {
            addModal(target, dlgJustificativaAdep = new DlgDisabledArea(newModalId(), bundle("justificativaAdep")));
        }
        dlgJustificativaAdep.show(target, dmi.getDispensacaoMedicamentoItem().getReceituarioItem().getJustificativaAdep());
    }

    private void removerItem(DispensacaoMedicamentoItemDTO rowObject) {
        for (int i = 0; i < itens.size(); i++) {
            if (itens.get(i).getDispensacaoMedicamentoItem() == rowObject.getDispensacaoMedicamentoItem()) {
                itens.remove(i);
            }
        }
    }

    private ICollectionProvider getCollectionProviderItems() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("dispensacaoPrescricao");
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (itens.isEmpty()) {
            throw new ValidacaoException(bundle("informePeloMenosUmItem"));
        }

        for (DispensacaoMedicamentoItemDTO _dispensacaoMedicamentoItem : itens) {
            if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getCoalesceQuantidadeDispensada() == 0D) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_dispensacao_quantidade_maior_zero"));
            }

            if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto() != null && _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getCodigo() != null) {
                SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                        .setId(_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto().getSubGrupo().getId())
                        .start().getVO();

                if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
                    if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null || _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().isEmpty()) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_todos_itens_com_controle_de_lote_tem_que_ser_definido"));
                    }
                }
            } else {
                throw new ValidacaoException(bundle("msgMedicamentoXNaoCadastradoParaContinuarEditeVinculeMedicamentoCadastrado", _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getReceituarioItem().getNomeProduto()));
            }

//            if (validaPrecoUnitario) {
//                if (Coalesce.asDouble(_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getPrecoUnitario()) == 0D) {
//                    throw new ValidacaoException(bundle("informePrecoUnitarioProdutoX", _dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getDescricaoProduto()));
//                }
//            }
        }

        DispensacaoMedicamento dispensacaoMedicamento = form.getModelObject();

        List<DispensacaoMedicamentoItem> lstDmiTemp = new ArrayList<DispensacaoMedicamentoItem>();
        for (DispensacaoMedicamentoItemDTO dto : itens) {
            lstDmiTemp.add(dto.getDispensacaoMedicamentoItem());
        }

        dispensacaoMedicamento.setItensDispensacaoMedicamentoSet(new HashSet<DispensacaoMedicamentoItem>(lstDmiTemp));

        if (dispensacaoMedicamento.getAtendimento() == null) {
            dispensacaoMedicamento.setAtendimento(dispensacaoMedicamento.getReceituario().getAtendimento());
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(dispensacaoPorTurno) && receituario != null) {
            receituario.setSomatorioTurno(validaSomatorioTurno());
        }
        dispensacaoMedicamento = BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).dispensarPrescricao(dispensacaoMedicamento, receituario);
        form.setModelObject(dispensacaoMedicamento);

        if (existsItemPermissaoComprovante(dispensacaoMedicamento)) {
            dlgImpressao.show(target);
        } else {
            confirmar(target);
        }
    }

    private boolean existsItemPermissaoComprovante(DispensacaoMedicamento dispensacaoMedicamento) {
        return LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, dispensacaoMedicamento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_COMPROVANTE), RepositoryComponentDefault.SIM_LONG))
                .exists();
    }

    private Long validaSomatorioTurno() throws DAOException, ValidacaoException {
        Collection<CheckBoxLongValue> listaCheck = Arrays.asList(checkBoxPrimeiroTurno, checkBoxSegundoTurno, checkBoxTerceiroTurno);
        Long somatorio = CheckBoxUtil.getSomatorio(listaCheck);
        Collection<CheckBoxLongValue> listaAux = new ArrayList<CheckBoxLongValue>();

        for (CheckBoxLongValue cb : listaCheck) {
            if (cb.isEnabled()) {
                listaAux.add(cb);
            }
        }
        Long somatorioNovos = CheckBoxUtil.getSomatorio(listaAux);

        if (somatorioNovos == null || somatorioNovos.equals(0L)) {
            throw new ValidacaoException(bundle("informeUmDosTurnos"));
        }

        return somatorio;
    }

    public String getMsgSalvo(DispensacaoMedicamento returnObject) {
        String msg = bundle("registro_salvo_sucesso");

        if (returnObject.getCodigo() != null) {
            String identificador = returnObject.getCodigo().toString();
            msg += " " + bundle("codigo") + ": " + identificador;
        }
        return msg;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCodigoBarras;
    }

    private boolean validarAcaoBtnAdicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem _dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
        if (_dispensacaoMedicamentoItem.getProduto() == null) {
            throw new ValidacaoException(bundle("informeProduto"));
        }

        for (DispensacaoMedicamentoItemDTO __dispensacaoMedicamentoItem : itens) {
            if (_dispensacaoMedicamentoItem.getProduto().equals(__dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem().getProduto())) {
//                dlgConfirmacao.show(target, _dispensacaoMedicamentoItem);
//                return false;
                throw new ValidacaoException(bundle("produtoJaAdicionado"));
            }
        }

        return true;
    }

    private void abriDialogAdicionar(AjaxRequestTarget target, DispensacaoMedicamentoItem _dispensacaoMedicamentoItem) throws DAOException, ValidacaoException {
        modelDispensacaoItem.getObject().setDispensacaoMedicamento(((Form<DispensacaoMedicamento>) form).getModelObject());
        dlgAdicionarItem.setObject(target, _dispensacaoMedicamentoItem, empresaBaixa);
        dlgAdicionarItem.show(target);
    }

    private void validarAdicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
        if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada() == 0L) {
            throw new ValidacaoException(bundle("quantidadeDispensarMaiorZero"));
        }

        SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                .addProperty(VOUtils.montarPath(SubGrupo.PROP_FLAG_ATUALIZA_TABELA_PRECO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), dispensacaoMedicamentoItem.getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                .start().getVO();
        if (RepositoryComponentDefault.SIM_LONG.equals(subGrupo.getFlagControlaEstoque())) {
            EstoqueEmpresa estoqueEmpresa = LoadManager.getInstance(EstoqueEmpresa.class)
                    .setId(new EstoqueEmpresaPK(dispensacaoMedicamentoItem.getProduto(), new Empresa(br.com.celk.system.session.ApplicationSession.get().getSession().getCodigoEmpresa())))
                    .start().getVO();
            if (dispensacaoMedicamentoItem.getCoalesceQuantidadeDispensada()
                    > Math.abs(estoqueEmpresa.getEstoqueDisponivel())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_maior_disponivel"));
            }
        }
    }

    //itemOrigem é o retorno do Dlg com o lote, quantidade e preços, itemDestino é só o produto
    private void adicionarItem(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException {
        boolean add = true;
        validarAdicionarItem(target, itemOrigem);
        try {
            BeanUtils.copyProperties(itemDestino, itemOrigem);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (InvocationTargetException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        itemDestino.setDataProximaDispensacao(DataUtil.getDataAtual());

        if (Coalesce.asDouble(itemDestino.getQuantidadePrescrita()) == 0D) {
            itemDestino.setQuantidadePrescrita(itemDestino.getQuantidadeDispensada());
        }

        for (DispensacaoMedicamentoItemDTO _dispensacaoMedicamentoItem : itens) {
            if (_dispensacaoMedicamentoItem.getDispensacaoMedicamentoItem() == itemDestino) {
                add = false;
            }
        }
        if (add) {
            adicionar(target, itemDestino.getQuantidadeDispensada().longValue(), itemDestino);
        }
        modelDispensacaoItem.setObject(new DispensacaoMedicamentoItem());
        tblItems.update(target);
        autoCompleteConsultaProduto.limpar(target);
        target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void adicionar(AjaxRequestTarget target, Long quantidade, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        DispensacaoMedicamentoItemDTO dmi = getItem(dispensacaoMedicamentoItem.getProduto(), itens);
        if (dmi != null) {
            dmi.getDispensacaoMedicamentoItem().setQuantidadeDispensada(dmi.getDispensacaoMedicamentoItem().getQuantidadeDispensada() + quantidade.doubleValue());
        } else {
            DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
            dto.setDispensacaoMedicamentoItem(dispensacaoMedicamentoItem);
            itens.add(dto);
        }
    }

    private DispensacaoMedicamentoItemDTO getItem(Produto produto, List<DispensacaoMedicamentoItemDTO> list) {
        for (DispensacaoMedicamentoItemDTO item : list) {
            if (produto.equals(item.getDispensacaoMedicamentoItem().getProduto())) {
                return item;
            }
        }
        return null;
    }

    private MovimentoGrupoEstoqueItemDTO getLote(List<MovimentoGrupoEstoqueItemDTO> lstLote, CodigoBarrasProduto cbp) {
        if (!lstLote.isEmpty()) {
            for (MovimentoGrupoEstoqueItemDTO dto : lstLote) {
                if (dto.getGrupoEstoque().equals(cbp.getGrupo())) {
                    return dto;
                }
            }
        }
        return null;
    }

    private void adicionarViaCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (codigoBarrasProduto != null) {
            Double quantidadeProduto  = 1D;
            CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class)
                    .addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, codigoBarrasProduto.toString()))
                    .addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value()))
                    .setMaxResults(1).start().getVO();

            if (cbp == null) {
                resetarFocoCodigoBarras(target);
                throw new ValidacaoException(bundle("codigoBarrasInvalido"));
            } else {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (cbp.isEtiquetaForaEstoque()) {
                        resetarFocoCodigoBarras(target);
                        throw new ValidacaoException(bundle("codigoBarrasJaDispensado"));
                    }
                }

                DispensacaoMedicamentoItemDTO item = getItem(cbp.getProduto(), itens);

                if(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto)){
                    quantidadeProduto = cbp.getQuantidadeProduto();
                }

                EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
                estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                estoqueEmpresaPK.setProduto(cbp.getProduto());

                EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, ApplicationSession.get().getSession().getEmpresa()))
                        .start().getVO();

                if (empresaMaterial == null) {
                    throw new ValidacaoException("Não foi encontrado um depósito padrão definido para a empresa.");
                }

                GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
                grupoEstoquePK.setGrupo(cbp.getGrupo());
                grupoEstoquePK.setCodigoDeposito(empresaMaterial.getDeposito().getCodigo());
                grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
                grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

                GrupoEstoque grupoEstoque = LoadManager.getInstance(GrupoEstoque.class)
                        .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                        .addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties())
                        .setId(grupoEstoquePK)
                        .start().getVO();

                if (grupoEstoque == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
                }

                if (item == null) {
                    item = new DispensacaoMedicamentoItemDTO();
                    item.setDispensacaoMedicamentoItem(new DispensacaoMedicamentoItem());
                    item.getDispensacaoMedicamentoItem().setProduto(cbp.getProduto());
                    item.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    item.getDispensacaoMedicamentoItem().setDataProximaDispensacao(DataUtil.getDataAtual());
                    item.getDispensacaoMedicamentoItem().setPrecoCusto(grupoEstoque.getId().getEstoqueEmpresa().getPrecoCusto());
                    item.getDispensacaoMedicamentoItem().setPrecoMedio(grupoEstoque.getId().getEstoqueEmpresa().getPrecoMedio());

                    Double precoUnitario = BOFactory.getBO(HospitalFacade.class).getPrecoProduto(cbp.getProduto(), form.getModelObject().getAtendimento().getConvenio(), DataUtil.getDataAtual());
                    item.getDispensacaoMedicamentoItem().setPrecoUnitario(precoUnitario);

                    item.getDispensacaoMedicamentoItem().setQuantidadePrescrita(quantidadeProduto);
                    item.getDispensacaoMedicamentoItem().setQuantidadeDispensar(quantidadeProduto);
                    item.getDispensacaoMedicamentoItem().setQuantidadeDispensada(quantidadeProduto);

                    item.getDispensacaoMedicamentoItem().setDispensacaoMedicamento(form.getModelObject());
                }

                //carrega o subgrupo
                SubGrupo subGrupo = LoadManager.getInstance(SubGrupo.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO), cbp.getProduto().getSubGrupo().getId().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), cbp.getProduto().getSubGrupo().getId().getCodigoGrupoProduto()))
                        .start().getVO();

                //verifica se controla lote
                if (subGrupo.getFlagControlaGrupoEstoque().equals(RepositoryComponentDefault.SIM)) {
                    if (item.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList() == null) {
                        item.getDispensacaoMedicamentoItem().setMovimentoGrupoEstoqueItemDTOList(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
                    }
                    for (MovimentoGrupoEstoqueItemDTO dto : item.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList()) {
                        if (dto.getLstCodigoBarrasProduto().contains(cbp)) {
                            resetarFocoCodigoBarras(target);
                            throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                        }
                    }

                    MovimentoGrupoEstoqueItemDTO dto = getLote(item.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList(), cbp);

                    Double quantidadeProdutoAux = quantidadeProduto;
                    if (dto == null) {
                        dto = new MovimentoGrupoEstoqueItemDTO();
                        dto.setDataValidade(grupoEstoque.getDataValidade());
                        dto.setDeposito(grupoEstoque.getRoDeposito());
                        dto.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
                        dto.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
                        dto.setEstoqueFisico(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueFisico());
                        dto.setEstoqueReservado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueReservado());
                        dto.setEstoqueEncomendado(grupoEstoque.getId().getEstoqueEmpresa().getEstoqueEncomendado());
                        dto.setGrupoEstoque(grupoEstoque.getId().getGrupo());
                        dto.setProduto(cbp.getProduto());
                        item.getDispensacaoMedicamentoItem().getMovimentoGrupoEstoqueItemDTOList().add(dto);
                    } else {
                        quantidadeProdutoAux = new Dinheiro(dto.getQuantidade()).somar(quantidadeProduto).doubleValue();
                    }

                    adicionar(target, quantidadeProduto.longValue(), item.getDispensacaoMedicamentoItem());

                    dto.setQuantidade(quantidadeProdutoAux);
                    dto.getLstCodigoBarrasProduto().add(cbp);
                } else {
                    if (item.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().contains(cbp)) {
                        resetarFocoCodigoBarras(target);
                        throw new ValidacaoException(bundle("codigoBarrasJaInseridoNestaDispensacao"));
                    }

                    adicionar(target, quantidadeProduto.longValue(), item.getDispensacaoMedicamentoItem());
                    item = getItem(item.getDispensacaoMedicamentoItem().getProduto(), itens);
                    if (item != null) {
                        item.getDispensacaoMedicamentoItem().getLstCodigoBarrasProduto().add(cbp);
                    } else {
                        throw new ValidacaoException(bundle("erroVincularCodigoBarras"));
                    }
                }

                tblItems.update(target);
                txtCodigoBarras.limpar(target);
                target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));

                updateNotificationPanel(target);
            }
        }
    }

    private void resetarFocoCodigoBarras(AjaxRequestTarget target) {
        txtCodigoBarras.limpar(target);
        target.appendJavaScript(JScript.focusComponent(txtCodigoBarras));
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private DispensacaoMedicamento iniciarDispensacao(Receituario receituario) {
        DispensacaoMedicamento dispensacaoMedicamento = new DispensacaoMedicamento();
        if (receituario != null) {
            dispensacaoMedicamento.setDataReceita(receituario.getDataCadastro());
            dispensacaoMedicamento.setEmpresa(empresaBaixa);
            dispensacaoMedicamento.setEmpresaOrigem(receituario.getEmpresa());
            dispensacaoMedicamento.setProfissional(receituario.getProfissional());
            dispensacaoMedicamento.setReceituario(receituario);
            dispensacaoMedicamento.setAtendimento(receituario.getAtendimento());
            dispensacaoMedicamento.setReceita(Coalesce.asString(receituario.getNumeroReceita()));
            dispensacaoMedicamento.setReceitaContinua(receituario.getReceitaContinua());
            dispensacaoMedicamento.setTipoReceita(receituario.getTipoReceita());
            dispensacaoMedicamento.setUsuarioCadsusDestino(receituario.getUsuarioCadsus());

            List<ReceituarioItem> receituarioItems = LoadManager.getInstance(ReceituarioItem.class)
                    .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), receituario))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_STATUS),BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                    .start().getList();

            Long i = 0L;
            for (ReceituarioItem receituarioItem : receituarioItems) {

                if (receituarioItem.getQuantidade() == null) {
                    List<ReceituarioItemComponente> lstComponentes = LoadManager.getInstance(ReceituarioItemComponente.class)
                            .addProperties(new HQLProperties(ReceituarioItemComponente.class).getProperties())
                            .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO)).getProperties())
                            .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                            .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                            .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItemComponente.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemComponente.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), receituarioItem.getCodigo()))
                            .start().getList();
                    if (lstComponentes != null && !lstComponentes.isEmpty()) {
                        for (ReceituarioItemComponente componente : lstComponentes) {
                            Produto produto = componente.getProduto();
                            Atendimento atendimento = receituario.getAtendimento();

                            if (produto != null) {
                                boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                                        .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()))
                                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                                        .exists();

                                if (!exists) {
                                    continue;
                                }
                            }

                            DispensacaoMedicamentoItem dmi = new DispensacaoMedicamentoItem();

                            dmi.setDispensacaoMedicamento(dispensacaoMedicamento);
                            dmi.setItem(i);
                            dmi.setReceituarioItem(receituarioItem);
                            dmi.setReceituarioItemComponente(componente);
                            dmi.setProduto(produto);
                            Double saldo = componente.getQuantidade() - Coalesce.asLong(componente.getQuantidadeDispensada()).doubleValue();
                            dmi.setQuantidadeDispensar(saldo >= 0D ? saldo : 0D);
                            dmi.setQuantidadeDispensada(0D);
                            dmi.setQuantidadePrescrita(componente.getQuantidade());
                            dmi.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());

                            if (produto != null) {
                                Date dataPrescricao = DataUtil.getDataAtual();
                                if (receituarioItem.getReceituario() != null) {
                                    dataPrescricao = receituario.getDataCadastro();
                                }

                                Convenio convenio = null;
                                Double precoProduto = null;
                                try {
                                    precoProduto = BOFactoryWicket.getBO(HospitalFacade.class).getPrecoProduto(produto, atendimento.getConvenio(), dataPrescricao);
                                    convenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
                                } catch (DAOException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                } catch (ValidacaoException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                }

                                dmi.setPrecoUnitario(precoProduto);

                                if (convenio != null && convenio.equals(atendimento.getConvenio())) {
                                    validaPrecoUnitario = false;
                                } else {
                                    validaPrecoUnitario = true;
                                }

                                if (produto.getSubGrupo() != null && RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                                    List<MovimentoGrupoEstoqueItemDTO> itens = new ArrayList();

                                    Empresa empresa = empresaBaixa;
                                    Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

                                    if (deposito != null) {
                                        try {
                                            QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                                            param.setDataValidade(DataUtil.getDataAtual());
                                            param.setCodigoEmpresa(empresa.getCodigo());
                                            param.setCodigoDeposito(deposito.getCodigo());
                                            param.setCodigoProduto(produto.getCodigo());
                                            param.setApenasComDisponivel(true);

                                            itens = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
                                        } catch (DAOException ex) {
                                            Loggable.log.error(ex.getMessage(), ex);
                                        } catch (ValidacaoException ex) {
                                            Loggable.log.error(ex.getMessage(), ex);
                                        }
                                    }

                                    if (itens.size() == 1) {
                                        dmi.setMovimentoGrupoEstoqueItemDTOList(Arrays.asList(itens.get(0)));
                                    }
                                }
                            }

                            if ((dmi.getQuantidadeDispensar() - dmi.getQuantidadeDispensada()) > 0D) {
                                DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                                dto.setDispensacaoMedicamentoItem(dmi);
                                dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.COMPONENTE.value());
                                itens.add(dto);
                                i++;
                            }
                        }
                    }
                } else {
                    Produto produto = receituarioItem.getProduto();
                    Atendimento atendimento = receituario.getAtendimento();

                    if (produto != null) {
                        boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                                .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                                .exists();

                        if (!exists) {
                            continue;
                        }
                    }

                    DispensacaoMedicamentoItem dmi = new DispensacaoMedicamentoItem();

                    dmi.setDispensacaoMedicamento(dispensacaoMedicamento);
                    dmi.setItem(i);
                    dmi.setReceituarioItem(receituarioItem);
                    dmi.setProduto(produto);
                    Double saldo = Coalesce.asDouble(receituarioItem.getQuantidade()) - Coalesce.asLong(receituarioItem.getQuantidadeDispensada()).doubleValue();
                    dmi.setQuantidadeDispensar(saldo >= 0D ? saldo : 0D);
                    dmi.setQuantidadeDispensada(0D);
                    dmi.setPosologia(Coalesce.asDouble(receituarioItem.getQuantidadePosologia()));
                    dmi.setQuantidadePrescrita(receituarioItem.getQuantidade());
                    dmi.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());

                    if (produto != null) {
                        Date dataPrescricao = DataUtil.getDataAtual();
                        if (receituarioItem.getReceituario() != null) {
                            dataPrescricao = receituario.getDataCadastro();
                        }

                        Convenio convenio = null;
                        Double precoProduto = null;
                        try {
                            precoProduto = BOFactoryWicket.getBO(HospitalFacade.class).getPrecoProduto(produto, atendimento.getConvenio(), dataPrescricao);
                            convenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
                        } catch (DAOException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        } catch (ValidacaoException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        }

                        dmi.setPrecoUnitario(precoProduto);

                        if (convenio != null && convenio.equals(atendimento.getConvenio())) {
                            validaPrecoUnitario = false;
                        } else {
                            validaPrecoUnitario = true;
                        }

                        if (produto.getSubGrupo() != null && RepositoryComponentDefault.SIM.equals(produto.getSubGrupo().getFlagControlaGrupoEstoque())) {
                            List<MovimentoGrupoEstoqueItemDTO> itens = new ArrayList();

                            Empresa empresa = empresaBaixa;
                            Deposito deposito = empresa.getEmpresaMaterial().getDeposito();

                            if (deposito != null) {
                                try {
                                    QueryMovimentoGrupoEstoqueItemDTOParam param = new QueryMovimentoGrupoEstoqueItemDTOParam();

                                    param.setDataValidade(DataUtil.getDataAtual());
                                    param.setCodigoEmpresa(empresa.getCodigo());
                                    param.setCodigoDeposito(deposito.getCodigo());
                                    param.setCodigoProduto(produto.getCodigo());
                                    param.setApenasComDisponivel(true);

                                    itens = BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).getMovimentoGrupoEstoqueItens(param);
                                } catch (DAOException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                } catch (ValidacaoException ex) {
                                    Loggable.log.error(ex.getMessage(), ex);
                                }
                            }

                            if (itens.size() == 1) {
                                dmi.setMovimentoGrupoEstoqueItemDTOList(Arrays.asList(itens.get(0)));
                            }
                        }
                    }

                    if ((dmi.getQuantidadeDispensar() - dmi.getQuantidadeDispensada()) > 0D) {
                        DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                        dto.setDispensacaoMedicamentoItem(dmi);
                        dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.ITEM.value());
                        dto.setDescricaoProduto(receituarioItem.getNomeProduto());
                        itens.add(dto);
                        if (produto != null) {
                            i = insereKit(dto, i);
                        }
                        i++;
                    }
                }
            }
        }

        return dispensacaoMedicamento;
    }

    private Long insereKit(DispensacaoMedicamentoItemDTO dmiPai, Long i) {
        List<ItemKitProduto> lstItemKitProduto = LoadManager.getInstance(ItemKitProduto.class)
                .addProperties(new HQLProperties(ItemKitProduto.class).getProperties())
                .addProperties(new HQLProperties(Produto.class, ItemKitProduto.PROP_PRODUTO).getProperties())
                .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ItemKitProduto.PROP_PRODUTO_PRINCIPAL, Produto.PROP_CODIGO), dmiPai.getDispensacaoMedicamentoItem().getProduto().getCodigo()))
                .start().getList();

        UsuarioCadsus paciente = receituario.getUsuarioCadsus();
        TipoViaMedicamento via = dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem().getTipoViaMedicamento();

        if (lstItemKitProduto != null && !lstItemKitProduto.isEmpty()) {
            for (ItemKitProduto itemKit : lstItemKitProduto) {
                if (itemKit.getIdadeMinima() != null) {
                    if (paciente.getIdade() < itemKit.getIdadeMinima()) {
                        continue;
                    }
                }
                if (itemKit.getIdadeMaxima() != null) {
                    if (paciente.getIdade() > itemKit.getIdadeMaxima()) {
                        continue;
                    }
                }
                if (itemKit.getTipoViaMedicamento() != null) {
                    if (via != null) {
                        if (!via.getCodigo().equals(itemKit.getTipoViaMedicamento().getCodigo())) {
                            continue;
                        }
                    }
                }
                ReceituarioItemKit historico = LoadManager.getInstance(ReceituarioItemKit.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemKit.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO), dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItemKit.PROP_PRODUTO_KIT, Produto.PROP_CODIGO), itemKit.getProduto().getCodigo()))
                        .start().getVO();
                Double saldo;
                if (historico != null) {
                    saldo = Coalesce.asDouble(historico.getQuantidade()) - Coalesce.asDouble(historico.getQuantidadeDispensada());
                } else {
                    saldo = itemKit.getQuantidade() * dmiPai.getDispensacaoMedicamentoItem().getQuantidadePrescrita();
                }
                if (saldo > 0D) {
                    DispensacaoMedicamentoItem dmiKit = new DispensacaoMedicamentoItem();
                    dmiKit.setDispensacaoMedicamento(dmiPai.getDispensacaoMedicamentoItem().getDispensacaoMedicamento());
                    dmiKit.setItem(i);
                    dmiKit.setReceituarioItem(null);
                    dmiKit.setProduto(itemKit.getProduto());
                    dmiKit.setQuantidadeDispensar(saldo);
                    dmiKit.setQuantidadeDispensada(0D);
                    dmiKit.setQuantidadePrescrita(itemKit.getQuantidade() * dmiPai.getDispensacaoMedicamentoItem().getQuantidadePrescrita());
                    dmiKit.setOrigem(DispensacaoMedicamentoItem.Origem.PRESCRICAO.getValue());
                    DispensacaoMedicamentoItemDTO dto = new DispensacaoMedicamentoItemDTO();
                    dto.setDispensacaoMedicamentoItem(dmiKit);
                    dto.setDmiPrincipalKit(dmiPai.getDispensacaoMedicamentoItem());
                    dto.getDispensacaoMedicamentoItem().setTipo(DispensacaoMedicamentoItem.Tipo.KIT.value());
                    itens.add(dto);
                    i++;
                    if (historico == null) {
                        historico = new ReceituarioItemKit();
                        historico.setProdutoKit(itemKit.getProduto());
                        historico.setReceituarioItem(dmiPai.getDispensacaoMedicamentoItem().getReceituarioItem());
                        historico.setUnidade(itemKit.getUnidade());
                        historico.setQuantidade(dmiKit.getQuantidadePrescrita());
                    }
                    dto.getDispensacaoMedicamentoItem().setHistoricoKit(historico);
                }
            }
        }
        return i;
    }

    private DataReport imprimir() throws ReportException {
        QueryImpressaoDispensacaoPrescricaoDTOParam param = new QueryImpressaoDispensacaoPrescricaoDTOParam();
        param.setCodigoDispensacao(form.getModelObject().getCodigo());
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).impressaoDispensacaoPrescricao(param);
    }

    private void confirmar(AjaxRequestTarget target) throws DAOException {
        Page page = new ConsultaPrescricaoAtendimentoPage();

        if (pageVoltar != null) {
            page = pageVoltar;
        }

        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, getMsgSalvo(form.getModelObject()));
    }

    private void carregaReceituario(Receituario receituario) {
        this.receituario = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, Receituario.PROP_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL)).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, Receituario.PROP_USUARIO_CADSUS).getProperties())
                .addProperty(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO, Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DISPENSACAO_POR_TURNO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_CODIGO), receituario.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                .start().getVO();

        dispensacaoPorTurno = this.receituario.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDispensacaoPorTurno();
    }

    private void carregaEmpresaBaixa(Empresa empresaBaixa) {
        if (empresaBaixa == null) {
            this.empresaBaixa = SessaoAplicacaoImp.getInstance().getEmpresa();
        } else {
            this.empresaBaixa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addProperties(new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, empresaBaixa.getCodigo()))
                    .start().getVO();
        }
    }
}
