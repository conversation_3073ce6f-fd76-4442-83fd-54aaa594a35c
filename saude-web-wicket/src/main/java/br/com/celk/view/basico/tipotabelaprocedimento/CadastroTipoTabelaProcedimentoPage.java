package br.com.celk.view.basico.tipotabelaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.prontuario.procedimento.TipoTabelaProcedimento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoTabelaProcedimentoPage extends CadastroPage<TipoTabelaProcedimento> {
    
    private InputField txtDescricao;

    public CadastroTipoTabelaProcedimentoPage(TipoTabelaProcedimento object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        
    }
    
    public CadastroTipoTabelaProcedimentoPage(TipoTabelaProcedimento object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoTabelaProcedimentoPage(TipoTabelaProcedimento object) {
        this(object, false);
    }

    public CadastroTipoTabelaProcedimentoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredUpperField(TipoTabelaProcedimento.PROP_DESCRICAO));
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<TipoTabelaProcedimento> getReferenceClass() {
        return TipoTabelaProcedimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoTabelaProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoTabelaProcedimento");
    }

}
