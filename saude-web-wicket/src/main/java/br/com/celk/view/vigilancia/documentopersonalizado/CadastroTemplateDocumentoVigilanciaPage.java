package br.com.celk.view.vigilancia.documentopersonalizado;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgImpressaoHtml;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.SimpleEditorSettings;
import br.com.celk.report.DocumentoVigilanciaHtmlReport;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TemplateDocumentoVigilancia;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import wicket.contrib.tinymce.ajax.TinyMceAjaxSubmitModifier;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroTemplateDocumentoVigilanciaPage extends CadastroPage<TemplateDocumentoVigilancia> {
    
    private RequiredInputField<String> txtNome;
    private InputArea txaTexto;
    private AbstractAjaxButton btnPreview;
    private DlgImpressaoHtml<String> dlgPreview;

    public CadastroTemplateDocumentoVigilanciaPage(TemplateDocumentoVigilancia object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }
    
    public CadastroTemplateDocumentoVigilanciaPage(TemplateDocumentoVigilancia object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTemplateDocumentoVigilanciaPage(TemplateDocumentoVigilancia object) {
        this(object, false);
    }

    public CadastroTemplateDocumentoVigilanciaPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtNome = new RequiredInputField<String>(TemplateDocumentoVigilancia.PROP_NOME));
        form.add(DropDownUtil.getIEnumDropDown(TemplateDocumentoVigilancia.PROP_TIPO_DOCUMENTO, TemplateDocumentoVigilancia.TipoDocumento.values(), false));
        form.add(txaTexto = new RequiredInputArea<String>(TemplateDocumentoVigilancia.PROP_TEXTO));
        txaTexto.add(new EditorBehavior(new SimpleEditorSettings(700, 350)));

        getBtnSalvar().add(new TinyMceAjaxSubmitModifier());
        btnPreview.add(new TinyMceAjaxSubmitModifier());
    }

    @Override
    protected Component[] antesSalvarButton() {
        btnPreview = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                String modelo = CadastroTemplateDocumentoVigilanciaPage.this.getForm().getModel().getObject().getTexto();
                viewDlgPreviewDocumento(target, modelo);
            }
        };

        btnPreview.add(new AttributeModifier("class", "print"));
        btnPreview.add(new AttributeModifier("style", "margin-right: 5px;"));
        btnPreview.add(new AttributeModifier("value", BundleManager.getString("preview")));

        return new Component[]{btnPreview};
    }

    private void viewDlgPreviewDocumento(AjaxRequestTarget target, String modelo) {
        if (dlgPreview == null) {
            addModal(target, dlgPreview = new DlgImpressaoHtml<String>(newModalId(), bundle("desejaVisualizarPreview")) {
                @Override
                public HtmlReport getHtmlReport(AjaxRequestTarget target, String modelo) throws ValidacaoException, DAOException {
                    return new DocumentoVigilanciaHtmlReport(modelo);
                }
            });

            dlgPreview.setTitle(bundle("visualizarPreview"));
            dlgPreview.setLabelImprimir(target, bundle("visualizar"));
        }
        dlgPreview.show(target, modelo);
    }

    @Override
    public void antesSalvar(AjaxRequestTarget target, TemplateDocumentoVigilancia object) throws DAOException, ValidacaoException {
        if(getForm().getModel().getObject().getTexto() == null) {
            throw new ValidacaoException(bundle("informeDescricao"));
        }
        LoadManager loadManager = LoadManager.getInstance(TemplateDocumentoVigilancia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TemplateDocumentoVigilancia.PROP_TIPO_DOCUMENTO, object.getTipoDocumento()));
        if(object.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(TemplateDocumentoVigilancia.PROP_CODIGO, QueryCustom.QueryCustomParameter.DIFERENTE, object.getCodigo()));
        }
        if(loadManager.exists()) {
            throw new ValidacaoException(bundle("msgJaExisteModeloDocumentoParaTipoDocumentoSelecionado"));
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNome;
    }

    @Override
    public Class<TemplateDocumentoVigilancia> getReferenceClass() {
        return TemplateDocumentoVigilancia.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTemplateDocumentoVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroModeloDocumentoVigilancia");
    }

}
