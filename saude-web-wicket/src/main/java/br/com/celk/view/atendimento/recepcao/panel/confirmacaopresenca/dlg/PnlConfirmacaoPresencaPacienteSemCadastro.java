package br.com.celk.view.atendimento.recepcao.panel.confirmacaopresenca.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.IResource;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoPresencaPacienteSemCadastro extends Panel{
    
    private Form form;
    private AgendaGradeAtendimentoHorarioDTO model;
    private MultiLineLabel label;
    private final String message;
    private WebMarkupContainer image;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private UsuarioCadsus usuarioCadsus;
    private Image imagem;
    private final String IMG = "img-warn";
    
    public PnlConfirmacaoPresencaPacienteSemCadastro(String id){
        super(id);
        message = bundle("msgParaConfirmarAtendimentoPacienteNecessarioInserirPacienteCampoAbaixoRealizarCadastroSeMesmoNaoPossuir");
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));
        form.add(label = new MultiLineLabel("message", message));
        
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", new PropertyModel<UsuarioCadsus>(this, "usuarioCadsus")));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    onConfirmar(target, model, usuarioCadsus);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        form.add(new AbstractAjaxButton("btnNovoCadastro") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onNovoCadastro(target, model);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(autoCompleteConsultaUsuarioCadsus.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_paciente"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto, UsuarioCadsus usuarioCadsus) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onNovoCadastro(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;
    
    public void setDTO(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto){
        this.model = dto;
        
        this.usuarioCadsus = null;
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        
        target.focusComponent(autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField());
    }
    
    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }
}