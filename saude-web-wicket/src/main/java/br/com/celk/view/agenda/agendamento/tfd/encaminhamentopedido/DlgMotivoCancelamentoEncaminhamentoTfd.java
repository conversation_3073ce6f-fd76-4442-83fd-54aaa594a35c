/*
 * Copyright 2014 pietro.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.view.agenda.agendamento.tfd.encaminhamentopedido;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoCancelamentoEncaminhamentoTfd extends Window {

    private PnlMotivoCancelamentoEncaminhamentoTfd pnlMotivoCancelamentoEncaminhamentoTfd;
    private EncaminhamentoPedidoDTO modelObject;

    public DlgMotivoCancelamentoEncaminhamentoTfd(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(60);

        setResizable(false);

        setTitle(BundleManager.getString("motivoCancelamento"));

        setContent(pnlMotivoCancelamentoEncaminhamentoTfd = new PnlMotivoCancelamentoEncaminhamentoTfd(getContentId()) {
            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoCancelamentoEncaminhamentoTfd.this.onConfirmar(target, motivo, modelObject);
            }
        });
    }

    public void show(AjaxRequestTarget target, EncaminhamentoPedidoDTO modelObject) {
        pnlMotivoCancelamentoEncaminhamentoTfd.limpar(target);
        this.modelObject = modelObject;
        super.show(target);
    }
    

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, EncaminhamentoPedidoDTO modelObject) throws ValidacaoException, DAOException;

}
