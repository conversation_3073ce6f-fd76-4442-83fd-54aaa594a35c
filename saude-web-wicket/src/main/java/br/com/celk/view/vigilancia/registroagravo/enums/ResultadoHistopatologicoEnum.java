package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum ResultadoHistopatologicoEnum implements IEnum {
    ABERTO(1L, "Aberto"),
    CONCLUIDO(2L, "Concluído"),
    NAO_REALIZADO(3L, "Não Realizado"),
    CANCELADO(4L, "Cancelado");

    private Long value;
    private String descricao;

    ResultadoHistopatologicoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static ResultadoHistopatologicoEnum valueOf(Long value) {
        for (ResultadoHistopatologicoEnum v : ResultadoHistopatologicoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
