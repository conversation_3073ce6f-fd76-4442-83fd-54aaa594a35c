package br.com.celk.view.atendimento.consultaprontuario.panel.consultalaudobpai;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.examebpai.dto.ExameBpaiDTOParam;
import br.com.ksisolucoes.agendamento.examebpai.dto.ExameRequisicaoBpaiDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConsultaLaudoBpaIPanel extends ConsultaProntuarioCadastroPanel{

    private Table tblPendentes;
    private Table tblHistorico;
    private Integer periodo;
    
    public ConsultaLaudoBpaIPanel(String id) {
        super(id, bundle("laudoBpaI"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");

        form.add(tblPendentes = new Table("tblPendentes", getColumnsPendentes(), getCollectionProviderPendentes()));
        tblPendentes.populate();

        form.add(getDropDownFiltroHistorico());
        form.add(tblHistorico = new Table("tblHistorico", getColumnsHistorico(), getCollectionProviderHistorico()));
        tblHistorico.populate();
        periodo = 999;
        add(form);
    }

    private List<IColumn> getColumnsPendentes() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ExameRequisicaoBpaiDTO proxy = on(ExameRequisicaoBpaiDTO.class);

        columns.add(getActionColumnPendentes());
        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getExameBpai().getExame().getDataCadastro())));
        columns.add(createColumn(bundle("exame"), proxy.getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getExameBpai().getExame().getTipoExame().getDescricao()));
        columns.add(new DateTimeColumn(bundle("dataAgendamento"), path(proxy.getSolicitacaoAgendamento().getDataAgendamento())));
        columns.add(createColumn(bundle("situacao"), proxy.getSolicitacaoAgendamento().getDescricaoSituacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderPendentes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                ExameBpaiDTOParam dtoParam = new ExameBpaiDTOParam();

                DatePeriod datePeriod;
                if (periodo != 999) {
                    Date dataAtual = DataUtil.getDataAtual();
                    datePeriod = new DatePeriod(Data.removeMeses(dataAtual, periodo), dataAtual);
                } else {
                    datePeriod = null;
                }
//                dtoParam.setAtendimento(getAtendimento());
                dtoParam.setContemAtendimento(false);
                dtoParam.setPeriodo(datePeriod);
                dtoParam.setContemImTable(true);
                dtoParam.setPendente(true);
                dtoParam.setUsuarioCadsus(getUsuarioCadsus());
                return BOFactoryWicket.getBO(ExameFacade.class).consultaExameRequisicaoBpai(dtoParam);
            }
        };
    }
    
    private DropDown getDropDownFiltroHistorico(){
        DropDown dropDown = new DropDown("filtroHistorico", new PropertyModel(this, "periodo"));
        dropDown.addChoice(999, BundleManager.getString("todos", this));
        dropDown.addChoice(3, BundleManager.getString("ultimos3meses", this));
        dropDown.addChoice(6, BundleManager.getString("ultimos6meses", this));
        dropDown.addChoice(12, BundleManager.getString("ultimoAno", this));
        
        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tblHistorico.update(target);
            }
        });
        return dropDown;
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ExameRequisicaoBpaiDTO proxy = on(ExameRequisicaoBpaiDTO.class);

        columns.add(getActionColumnHistorico());
        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getExameBpai().getExame().getDataCadastro())));
        columns.add(createColumn(bundle("exame"), proxy.getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("diagnostico"), proxy.getExameBpai().getDescricaoDiagnostico()));

        return columns;
    }
    
    private ICollectionProvider getCollectionProviderHistorico(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                ExameBpaiDTOParam dtoParam = new ExameBpaiDTOParam();
                
                DatePeriod datePeriod;
                if (periodo != 999) {
                    Date dataAtual = DataUtil.getDataAtual();
                    datePeriod = new DatePeriod(Data.removeMeses(dataAtual, periodo), dataAtual);
                }else{
                    datePeriod = null;
                }
                dtoParam.setUsuarioCadsus(getUsuarioCadsus());
                dtoParam.setContemAtendimento(false);
                dtoParam.setPeriodo(datePeriod);                
                return BOFactoryWicket.getBO(ExameFacade.class).consultaExameRequisicaoBpai(dtoParam);
            }
        };
    }
    
    private IColumn getActionColumnHistorico(){
        return new MultipleActionCustomColumn<ExameRequisicaoBpaiDTO>() {

            @Override
            public void customizeColumn(ExameRequisicaoBpaiDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ExameRequisicaoBpaiDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, ExameRequisicaoBpaiDTO modelObject) throws ValidacaoException, DAOException {
                        DetalhesBpaIConsultaPanel detalhesBpaIConsultaPanel = new DetalhesBpaIConsultaPanel(getConsultaProntuarioController().panelId(),modelObject.getExameBpai().getCodigo(),modelObject.getExameRequisicao().getCodigo());
                        getConsultaProntuarioController().changePanel(target, detalhesBpaIConsultaPanel);
                    }
                });
                
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ExameRequisicaoBpaiDTO>() {

                    @Override
                    public DataReport action(ExameRequisicaoBpaiDTO modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoExameBpai(modelObject.getExameBpai().getExame().getCodigo());
                    }
                });
            }
        };
    }

    private IColumn getActionColumnPendentes(){
        return new MultipleActionCustomColumn<ExameRequisicaoBpaiDTO>() {

            @Override
            public void customizeColumn(ExameRequisicaoBpaiDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ExameRequisicaoBpaiDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, ExameRequisicaoBpaiDTO modelObject) throws ValidacaoException, DAOException {
                        DetalhesBpaIConsultaPanel detalhesBpaIConsultaPanel = new DetalhesBpaIConsultaPanel(getConsultaProntuarioController().panelId(),modelObject.getExameBpai().getCodigo(),modelObject.getExameRequisicao().getCodigo());
                        getConsultaProntuarioController().changePanel(target, detalhesBpaIConsultaPanel);
                    }
                });

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ExameRequisicaoBpaiDTO>() {

                    @Override
                    public DataReport action(ExameRequisicaoBpaiDTO modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoExameBpai(modelObject.getExameBpai().getExame().getCodigo());
                    }
                });
            }
        };
    }
}