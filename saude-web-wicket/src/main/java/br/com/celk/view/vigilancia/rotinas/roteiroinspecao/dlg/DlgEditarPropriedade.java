package br.com.celk.view.vigilancia.rotinas.roteiroinspecao.dlg;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public abstract class DlgEditarPropriedade extends Window {

    private String propriedade;
    private String descricaoItem;
    private int maxLength;
    private boolean enabled;
    private PnlEditarPropriedade panel;

    public DlgEditarPropriedade(String id, String descricaoItem, String propriedade, int maxLength, boolean enabled) {
        super(id);
        this.descricaoItem = descricaoItem;
        this.propriedade = propriedade;
        this.maxLength = maxLength;
        this.enabled = enabled;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(768);
        setInitialHeight(300);

        setResizable(true);

        setContent(panel = new PnlEditarPropriedade(getContentId(), descricaoItem, propriedade,  maxLength, enabled) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String descricao) throws ValidacaoException, DAOException {
                close(target);
                DlgEditarPropriedade.this.onConfirmar(target, descricao);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String descricao) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, String descricao) {
        super.show(target);
        panel.setDescricao(target, descricao);
    }
}
