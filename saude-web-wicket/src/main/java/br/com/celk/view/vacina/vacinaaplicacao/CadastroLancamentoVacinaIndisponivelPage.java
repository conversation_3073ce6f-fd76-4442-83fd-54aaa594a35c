package br.com.celk.view.vacina.vacinaaplicacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vacina.produtovacina.autocomplete.AutoCompleteConsultaProdutoVacina;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ReservaFacade;
import br.com.ksisolucoes.bo.entradas.estoque.reserva.dto.ReservaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Reserva;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroLancamentoVacinaIndisponivelPage extends BasePage {

    private Form<Reserva> form;
    private Reserva reserva;
    private Empresa empresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProdutoVacina autoCompleteConsultaProdutoVacina;
    private PnlSaidaLote pnlSaidaLote;
    private InputField txtQuantidade;
    private TextArea txtObservacao;
    private String grupoEstoque;
    private WebMarkupContainer containerMotivoCancelamento;

    public CadastroLancamentoVacinaIndisponivelPage() {
        if (empresa == null) {
            empresa = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        }
        init(true);
    }

    public CadastroLancamentoVacinaIndisponivelPage(Reserva reserva) {
        this.reserva = reserva;
        init(true);
    }

    public CadastroLancamentoVacinaIndisponivelPage(Reserva reserva, boolean viewOnly) {
        this.reserva = reserva;
        if (reserva.getGrupoEstoque() != null && reserva.getGrupoEstoque().getId().getEstoqueEmpresa() != null) {
            empresa = reserva.getGrupoEstoque().getId().getEstoqueEmpresa().getId().getEmpresa();
            grupoEstoque = reserva.getGrupoEstoque().getId().getGrupo();
        }
        init(viewOnly);
    }

    private void init(boolean enabled) {
        Reserva proxy = on(Reserva.class);

        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", new PropertyModel<Empresa>(this, "empresa")));
        autoCompleteConsultaEmpresa.setEnabled(false);

        getForm().add(autoCompleteConsultaProdutoVacina = new AutoCompleteConsultaProdutoVacina(path(proxy.getProdutoVacina()), true));
        autoCompleteConsultaProdutoVacina.setEnabled(enabled);
        autoCompleteConsultaProdutoVacina.setEmpresa(empresa);

        getForm().add(txtObservacao = new TextArea(path(proxy.getObservacao())));
        txtObservacao.setOutputMarkupId(true);
        txtObservacao.setEnabled(enabled);

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }).setEnabled(enabled));

        form.add(txtQuantidade = new InputField(path(proxy.getQuantidade())));
        txtQuantidade.setEnabled(enabled);

        getForm().add(pnlSaidaLote = new PnlSaidaLote("grupoEstoque", new PropertyModel<Empresa>(this, "grupoEstoque")));
        pnlSaidaLote.setAutoCompleteConsultaProdutoVacina(autoCompleteConsultaProdutoVacina);
        pnlSaidaLote.setLabel(new Model<>(bundle("lote")));
        pnlSaidaLote.setTxtQuantidade(txtQuantidade);
        pnlSaidaLote.setValidarLotesVencidos(false);
        pnlSaidaLote.setEmpresa(empresa);
        pnlSaidaLote.registerEvents();

        containerMotivoCancelamento = new WebMarkupContainer("containerMotivoCancelamento");
        containerMotivoCancelamento.setOutputMarkupId(true);
        containerMotivoCancelamento.setVisible(!enabled);

        containerMotivoCancelamento.add(new DisabledInputField(path(proxy.getDescricaoSituacaoVacinasIndisponiveis())));
        containerMotivoCancelamento.add(new DisabledInputArea(path(proxy.getMotivoCancelamento())));

        getForm().add(containerMotivoCancelamento);

        add(getForm());
    }


    private Form<Reserva> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new Reserva()));
            if (reserva != null) {
                form.getModel().setObject(reserva);
            }
        }
        return form;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProdutoVacina.getTxtDescricao().getTextField();
    }

    private void salvar() throws DAOException, ValidacaoException {
        Reserva reserva = getForm().getModel().getObject();
        ReservaDTO reservaDTO = new ReservaDTO();

        reservaDTO.setCodigoProduto(reserva.getProdutoVacina().getProduto().getCodigo());
        reservaDTO.setCodigoEmpresa(empresa.getCodigo());
        reservaDTO.setCodigoDeposito(ApplicationSession.get().getSessaoAplicacao().getEmpresa().getEmpresaMaterial().getDeposito().getCodigo());
        reservaDTO.setQuantidade(reserva.getQuantidade());
        reservaDTO.setItem(1L);
        reservaDTO.setDocumento(ApplicationSession.get().getSessaoAplicacao().getUsuario().getCodigo());
        reservaDTO.setGrupo(grupoEstoque);
        reservaDTO.setTipoReserva(Reserva.TIPO_RESERVA_LANCAMENTO_VACINA_INDISPONIVEL);
        reservaDTO.setValidarDisponivelNegativo(true);
        reservaDTO.setProdutoVacina(reserva.getProdutoVacina());
        reservaDTO.setObservacao(reserva.getObservacao());

        BOFactory.getBO(ReservaFacade.class).gerarReserva(reservaDTO);

        ConsultaLancamentoVacinaIndisponivelPage page = new ConsultaLancamentoVacinaIndisponivelPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLancamentoVacinasIndisponiveis");
    }

}
