package br.com.celk.view.vigilancia.requerimentovigilancia.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.VeiculoEstabelecimento;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaRequerimentoVigilancia extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return RequerimentoVigilancia.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties(), 
               VOUtils.mergeProperties(new HQLProperties(VeiculoEstabelecimento.class, RequerimentoVigilancia.PROP_VEICULO).getProperties()),
                VOUtils.mergeProperties(new HQLProperties(TipoSolicitacao.class, RequerimentoVigilancia.PROP_TIPO_SOLICITACAO).getProperties()),
               VOUtils.mergeProperties(new HQLProperties(Estabelecimento.class, RequerimentoVigilancia.PROP_ESTABELECIMENTO).getProperties()));
    }
}
