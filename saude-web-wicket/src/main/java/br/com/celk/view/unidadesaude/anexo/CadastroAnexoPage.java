package br.com.celk.view.unidadesaude.anexo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.unidadesaude.tipoanexo.autocomplete.AutoCompleteConsultaTipoAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.AnexosPacienteDTO;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.comunicacao.base.AnexoDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo;
import br.com.ksisolucoes.vo.prontuario.hospital.AihAnexo;
import br.com.ksisolucoes.vo.prontuario.hospital.SolicitacaoAgendamentoAnexo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.MultiFileUploadField;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroAnexoPage extends BasePage {

    private CompoundPropertyModel<AnexosPacienteDTO> modelAnexo;
    private AutoCompleteConsultaTipoAnexo autoCompleteConsultaTipoAnexo;
    private InputField txtKeyword;
    private DateChooser txtDataDocumento;
    private InputArea txaDescricao;
    private MultiFileUploadField mfuAnexo;
    private final Collection<FileUpload> uploads = new ArrayList();
    private WebMarkupContainer containerAnexos;
    private RepeatingView repeaterAnexo;

    public CadastroAnexoPage() {
        init(null, false);
    }

    public CadastroAnexoPage(Long codigo, boolean viewOnly) {
        init(codigo, viewOnly);
    }

    private void init(Long codigo, final boolean viewOnly) {
        AnexosPacienteDTO proxy = on(AnexosPacienteDTO.class);

        Form form = new Form("form");

        WebMarkupContainer container = new WebMarkupContainer("container", modelAnexo = new CompoundPropertyModel(load(codigo)));
        container.setOutputMarkupId(true);
        container.setEnabled(!viewOnly);

        container.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getAnexoPaciente().getUsuarioCadsus()), true).setLabel(new Model(bundle("paciente"))));
        container.add(autoCompleteConsultaTipoAnexo = new AutoCompleteConsultaTipoAnexo(path(proxy.getAnexoPaciente().getTipoAnexo()), true));
        autoCompleteConsultaTipoAnexo.setLabel(new Model(bundle("tipoAnexo")));
        container.add(txtKeyword = new InputField(path(proxy.getAnexoPaciente().getKeyword())));
        container.add(txtDataDocumento = new DateChooser(path(proxy.getAnexoPaciente().getDataDocumento())));

        container.add(txaDescricao = new InputArea(path(proxy.getAnexoPaciente().getDescricao())));

        Form formAnexo = new Form("formAnexo");
        formAnexo.setVisible(!viewOnly);
        formAnexo.setMultiPart(true);
        formAnexo.add(mfuAnexo = new MultiFileUploadField("uploads", new PropertyModel(this, "uploads")));
        mfuAnexo.setOutputMarkupId(true);
        container.add(formAnexo);

        form.add(container);

        form.add(containerAnexos = new WebMarkupContainer("containerAnexos"));
        containerAnexos.setOutputMarkupId(true);
        containerAnexos.add(repeaterAnexo = new RepeatingView("repeaterAnexo"));

        populateArquivosAnexados(viewOnly);

        form.add(new VoltarButton("btnVoltar"));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, false);
            }
        }.setVisible(!viewOnly));

        form.add(new AbstractAjaxButton("btnSalvarContinuar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, true);
            }
        }.setVisible(!viewOnly));

        add(form);
    }

    private void salvar(AjaxRequestTarget target, boolean continuar) throws DAOException, ValidacaoException {
        AnexosPacienteDTO dto = modelAnexo.getObject();
        Empresa empresaLogado = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (dto.getAnexoPaciente().getDataDocumento() != null && dto.getAnexoPaciente().getDataDocumento().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(bundle("msgDataDocumentoNaoPodeSerMaiorDataAtual"));
        }

        if (uploads.isEmpty() && dto.getElos().isEmpty()) {
            throw new ValidacaoException(bundle("msgNecessarioAnexarPeloMenosArquivoPoderSalvar"));
        }

        for (FileUpload upload : uploads) {
            String clientFileName = upload.getClientFileName();
            if (clientFileName.toLowerCase().endsWith(".pdf")
                    || clientFileName.toLowerCase().endsWith(".png")
                    || clientFileName.toLowerCase().endsWith(".jpeg")
                    || clientFileName.toLowerCase().endsWith(".jpe")
                    || clientFileName.toLowerCase().endsWith(".jpg")) {
                try {
                    MensagemAnexoDTO anexo = new MensagemAnexoDTO();

                    File newFile = File.createTempFile("anexo", clientFileName);
                    upload.writeTo(newFile);

                    anexo.setNomeArquivoOriginal(upload.getClientFileName());
                    anexo.setNomeArquivoUpload(newFile.getAbsolutePath());
                    dto.addAnexo(anexo);

                } catch (IOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            } else {
                throw new ValidacaoException(bundle("msgExistemAnexosTiposNaoPermitidosPacientes"));
            }
        }

        dto.getAnexoPaciente().setEmpresa(empresaLogado);
        AnexoPaciente anexoPaciente = BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAnexosPaciente(dto);

        if (continuar) {
            limpar(target);
        } else {
            ConsultaAnexoPage page = new ConsultaAnexoPage();
            getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(AnexoPaciente.class, anexoPaciente));
            setResponsePage(page);
        }
    }

    private void limpar(AjaxRequestTarget target) {
        UsuarioCadsus usuarioCadsus = modelAnexo.getObject().getAnexoPaciente().getUsuarioCadsus();

        modelAnexo.setObject(new AnexosPacienteDTO());
        modelAnexo.getObject().setAnexoPaciente(new AnexoPaciente());
        modelAnexo.getObject().getAnexoPaciente().setUsuarioCadsus(usuarioCadsus);

        autoCompleteConsultaTipoAnexo.limpar(target);
        txtKeyword.limpar(target);
        txtDataDocumento.limpar(target);
        txaDescricao.limpar(target);
        uploads.clear();
        repeaterAnexo.removeAll();
        target.add(containerAnexos);
        target.add(mfuAnexo);
    }

    private AnexosPacienteDTO load(Long codigo) {
        AnexosPacienteDTO dto = new AnexosPacienteDTO();
        Empresa empresaLogado = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (codigo != null) {
            AnexoPaciente anexoPaciente = LoadManager.getInstance(AnexoPaciente.class)
                    .addProperties(new HQLProperties(AnexoPaciente.class).getProperties())
                    .addProperty(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                    .addProperty(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                    .addProperty(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                    .addProperty(VOUtils.montarPath(AnexoPaciente.PROP_TIPO_ANEXO, TipoAnexo.PROP_DESCRICAO))
                    .setId(codigo)
                    .start().getVO();

            List<AnexoPacienteElo> elos = LoadManager.getInstance(AnexoPacienteElo.class)
                    .addProperties(new HQLProperties(AnexoPacienteElo.class).getProperties())
                    .addProperties(new HQLProperties(GerenciadorArquivo.class, AnexoPacienteElo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(AnexoPacienteElo.PROP_ANEXO_PACIENTE, anexoPaciente))
                    .start().getList();

            dto.setAnexoPaciente(anexoPaciente);
            dto.setElos(elos);
        }

        return dto;
    }

    private void populateArquivosAnexados(final boolean viewOnly) {
        for (AnexoPacienteElo elo : modelAnexo.getObject().getElos()) {
            repeaterAnexo.add(new PanelArquivosAnexados(repeaterAnexo.newChildId(), elo) {
                @Override
                public void removerAnexo(AjaxRequestTarget target, AnexoPacienteElo elo) throws ValidacaoException, DAOException {
                    modelAnexo.getObject().getElos().remove(elo);
                    repeaterAnexo.remove(this);
                    target.add(containerAnexos);
                }

                @Override
                public void removerAnexo(AjaxRequestTarget target, SolicitacaoAgendamentoAnexo anexo) throws ValidacaoException, DAOException {
                }

                public void removerAnexo(AjaxRequestTarget target, AihAnexo anexo) throws ValidacaoException, DAOException {
                }

                @Override
                public void removerAnexoDocumento(AjaxRequestTarget target, AnexoDocumento anexoDocumento) throws ValidacaoException, DAOException {

                }

                @Override
                public boolean isViewOnly() {
                    return viewOnly;
                }
            });
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroAnexos");
    }
}
