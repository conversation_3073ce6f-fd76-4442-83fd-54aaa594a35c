package br.com.celk.view.consorcio.movimentacaofinanceira.relatorio;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioExtratoContasDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoMovimentacoesDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;

import br.com.ksisolucoes.vo.consorcio.SubContaAno;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioResumoMovimentacoesPage extends RelatorioPage<RelatorioResumoMovimentacoesDTOParam> {
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaConsorciado;
    private Boolean controlaSaldoPorAno;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaConsorciado = new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));        
        form.add(new AutoCompleteConsultaTipoConta("tipoConta").setValidarVisivelConsorciado(true));
        form.add(getContainerAno());
        form.add(new RequiredInputField("periodoInicial"));
        form.add(new RequiredInputField("periodoFinal"));
        form.add(DropDownUtil.getIEnumDropDown("frequencia", RelatorioResumoMovimentacoesDTOParam.Frequencia.values()));
    }

    private WebMarkupContainer getContainerAno() {
        WebMarkupContainer containerAno = new WebMarkupContainer("containerAno");
        containerAno.add(DropDownUtil.getAnoDropDown("ano", true, false, SubContaAno.ANO_INICIAL, false));
        containerAno.setVisible(controlaSaldoPorAno());
        return containerAno;
    }

    private boolean controlaSaldoPorAno() {
        if (controlaSaldoPorAno == null) {
            try {
                controlaSaldoPorAno =  RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno"));
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage());
            }
        }

        return controlaSaldoPorAno;
    }

    @Override
    public void customDTOParam(RelatorioResumoMovimentacoesDTOParam param) {
        if (controlaSaldoPorAno()) {
            param.setAno((long) DataUtil.getAno());
        }
        param.setControlaSaldoPorAno(controlaSaldoPorAno());
    }
    
    @Override
    public Class<RelatorioResumoMovimentacoesDTOParam> getDTOParamClass() {
        return RelatorioResumoMovimentacoesDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoMovimentacoesDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioResumoMovimentacoes(param);
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException {
        param.setControlaSaldoPorAno(controlaSaldoPorAno());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoMovimentacoes");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaConsorciado;
    }

}
