package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoExumacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class RequerimentoExumacaoPage extends BasePage {
    
    private Form<RequerimentoExumacaoDTO> form;
    private RequerimentoExumacao requerimentoExumacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoSolicitacao tipoSolicitacao;
    private InputField txtNomeMorto;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private CheckBoxLongValue flagExigeFiscal;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj required");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf required");
    private boolean enabled;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private Class classReturn;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;

    public RequerimentoExumacaoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoExumacaoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoExumacao(requerimentoVigilancia);
        if(RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())){
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }
        
        RequerimentoExumacaoDTO proxy = on(RequerimentoExumacaoDTO.class);

        getForm().add(txtNomeMorto = (InputField<String>) new RequiredInputField<String>(path(proxy.getRequerimentoExumacao().getNomeMorto())).setLabel(new Model<>(bundle("nome"))).setEnabled(enabled));
        getForm().add(new DisabledInputField<String>(path(proxy.getRequerimentoExumacao().getRequerimentoVigilancia().getProtocoloFormatado())));
        getForm().add((DropDown) DropDownUtil.getSexoDropDown(path(proxy.getRequerimentoExumacao().getSexo())).setLabel(new Model(bundle("sexo"))).setEnabled(enabled));
        getForm().add((DateChooser) new DateChooser(path(proxy.getRequerimentoExumacao().getDataNascimento())).setLabel(new Model(bundle("dataNascimento"))).setEnabled(enabled));
        getForm().add((DateChooser) new DateChooser(path(proxy.getRequerimentoExumacao().getDataFalecimento())).setLabel(new Model(bundle("dataFalecimento"))).setEnabled(enabled));
        getForm().add((DateChooser) new DateChooser(path(proxy.getRequerimentoExumacao().getDataSepultamento())).setLabel(new Model(bundle("dataSepultamento"))).setEnabled(enabled));
        getForm().add((InputField<String>) new InputField<String>(path(proxy.getRequerimentoExumacao().getDescricaoCemiterioSepultado())).setLabel(new Model<>(bundle("cemiterioSepultado"))).setEnabled(enabled));
        getForm().add((InputField<String>) new InputField<String>(path(proxy.getRequerimentoExumacao().getDescricaoCemiterioTransporte())).setLabel(new Model<>(bundle("cemiterioTransporte"))).setEnabled(enabled));

        getForm().add(flagExigeFiscal = new CheckBoxLongValue(path(proxy.getRequerimentoExumacao().getFlagExigeFiscal())){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().limpar(target);
                if (flagExigeFiscal.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(flagExigeFiscal.getComponentValue())) {
                    pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().getTxtDescricao().addRequiredClass();
                    pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(true);
                } else {
                    pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().getTxtDescricao().removeRequiredClass();
                    pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(false);
                }
                target.add(pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia());
            }
        });

        getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia(), enabled));
        
        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));
        
        //Inicio Anexos
        //Anexo
        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
        getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
        pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        //Fim Anexos

        getForm().add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));
        
        getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia().getCodigo(), false).setVisible(!enabled));
        
        add(getForm());
    }
    
    private Form<RequerimentoExumacaoDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoExumacaoDTO()));
            if(requerimentoExumacao != null){
                form.getModel().getObject().setRequerimentoExumacao(requerimentoExumacao);                
            } else {
                form.getModel().getObject().setRequerimentoExumacao(new RequerimentoExumacao());                
                form.getModel().getObject().getRequerimentoExumacao().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }
    
    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if(getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia().getCpfSolicitante() == null
                && getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia().getRgSolicitante() == null){
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }


        getForm().getModel().getObject().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        atualizarDadosComuns();
        if(flagExigeFiscal.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(flagExigeFiscal.getComponentValue()) 
                && CollectionUtils.isEmpty(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList())){
            throw new ValidacaoException(bundle("msgPreenchaSetorResponsavel"));
        }
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoExumacao(getForm().getModel().getObject());

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), bundle("desejaImprimirAutorizacaoExumacaoTransporteRestosMortais")) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioAutorizacaoExumacaoDTOParam param = new RelatorioAutorizacaoExumacaoDTOParam();
                param.setCodigoRequerimentoVigilancia(object.getCodigo());
                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(param);

                List<IReport> lstDataReport = new ArrayList<>();
                lstDataReport.add(comprovanteRequerimento);

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroAutorizacaoExumacaoTransporteRestosMortais");
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtNomeMorto)));
    }

    private void carregarRequerimentoExumacao(RequerimentoVigilancia requerimentoVigilancia){
        if(requerimentoVigilancia != null){
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();
            
            requerimentoExumacao = LoadManager.getInstance(RequerimentoExumacao.class)
                    .addProperties(new HQLProperties(RequerimentoExumacao.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoExumacao.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoExumacao.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            
            carregarAnexos(requerimentoVigilancia);
        }
    }
    
    private void carregarAnexos(RequerimentoVigilancia rv){
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);
        
        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for(RequerimentoVigilanciaAnexo rva : list){
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }
    
    private void atualizarDadosComuns(){
        getForm().getModel().getObject().getRequerimentoExumacao().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}