package br.com.celk.view.vigilancia.dengue.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.vigilancia.dengue.DenguePontoEstrategico;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

public class AutoCompleteConsultaDenguePontoEstrategico extends AutoCompleteConsulta<DenguePontoEstrategico> {

    public AutoCompleteConsultaDenguePontoEstrategico(String id) {
        super(id);
    }

    public AutoCompleteConsultaDenguePontoEstrategico(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaDenguePontoEstrategico(String id, IModel<DenguePontoEstrategico> model) {
        super(id, model);
    }

    public AutoCompleteConsultaDenguePontoEstrategico(String id, IModel<DenguePontoEstrategico> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("pontoEstrategico");
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public Class getReferenceClass() {
                return DenguePontoEstrategico.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(DenguePontoEstrategico.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), DenguePontoEstrategico.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), DenguePontoEstrategico.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_CODIGO));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return DenguePontoEstrategico.class;
                    }

                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(DenguePontoEstrategico.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }

        };
    }
}
