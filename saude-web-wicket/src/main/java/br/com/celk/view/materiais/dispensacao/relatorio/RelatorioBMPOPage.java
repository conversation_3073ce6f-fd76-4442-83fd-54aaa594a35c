package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioBMPOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Grupo;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.text.ParseException;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioBMPOPage extends RelatorioPage<RelatorioBMPOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<Long> dropDownTipoPeriodo;
    private DropDown<Long> dropDownPeriodicidade;
    private DisabledInputField<String> txtCrf;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DropDown<String> dropDownTipoReceita;
    private DropDown<String> dropDownTipoBalanco;
    private LongField txtAnoExercicio;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo>  dropDownSubGrupo;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresaList", true)
                .setLabel(new Model(WicketMethods.bundle("empresa"))));
        form.add(txtAnoExercicio = new LongField("anoExercicio"));
        form.add(getDropDownTipoPeriodo());
        form.add(getDropDownPeriodicidade(null));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("responsavel", true));
        autoCompleteConsultaProfissional.setEnabled(false);
        form.add(txtCrf = new DisabledInputField<String>("crf"));
        form.add(getDropDownTipoReceita());
        form.add(getDropDownTipoBalanco());
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());

        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                carregarEmpresaMaterial(empresa);
                autoCompleteConsultaProfissional.setCodigoEmpresa(empresa.getCodigo());
                autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
                autoCompleteConsultaProfissional.setEnabled(true);
                target.add(autoCompleteConsultaProfissional);
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa empresa) {
                autoCompleteConsultaProfissional.limpar(target);
                txtCrf.limpar(target);
                autoCompleteConsultaProfissional.setEnabled(false);
                target.add(autoCompleteConsultaProfissional);
            }
        });

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                if (object != null) {
                    Profissional pro = LoadManager.getInstance(Profissional.class)
                            .addProperties(new HQLProperties(Profissional.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, object.getCodigo()))
                            .start().getVO();
                    txtCrf.setComponentValue(pro.getNumeroRegistro());
                    target.add(txtCrf);
                } else {
                    txtCrf.limpar(target);
                }
            }
        });
        try {
            this.txtAnoExercicio.setComponentValue(new Long(Data.getAno(Data.getDataAtual())));
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        dropDownPeriodicidade.setEnabled(false);
    }

    public DropDown<String> getDropDownTipoBalanco() {
        if (dropDownTipoBalanco == null) {
            dropDownTipoBalanco = new DropDown<String>("tipoBalanco");
            dropDownTipoBalanco.addChoice(BundleManager.getString("balancoCompleto"), BundleManager.getString("balancoCompleto"));
            dropDownTipoBalanco.addChoice(BundleManager.getString("balancoAquisicao"), BundleManager.getString("balancoAquisicao"));
        }
        return dropDownTipoBalanco;
    }

    public DropDown<String> getDropDownTipoReceita() {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<String>("tipoReceita");
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_CONTROLADAS, BundleManager.getString("controladas"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_AMARELA, BundleManager.getString("amarela"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_AZUL, BundleManager.getString("azul"));
            dropDownTipoReceita.addChoice(TipoReceita.RECEITA_BRANCA, BundleManager.getString("branca"));
        }
        return dropDownTipoReceita;
    }

    public DropDown<Long> getDropDownTipoPeriodo() {
        if (dropDownTipoPeriodo == null) {
            dropDownTipoPeriodo = new DropDown<Long>("tipoPeriodo");
            dropDownTipoPeriodo.addChoice(null, BundleManager.getString("anual"));
            dropDownTipoPeriodo.addChoice(1L, BundleManager.getString("trimestral"));
            dropDownTipoPeriodo.addChoice(2L, BundleManager.getString("mensal"));
            dropDownTipoPeriodo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget art) {
                    getDropDownPeriodicidade(dropDownTipoPeriodo.getComponentValue());
                    dropDownPeriodicidade.setEnabled(dropDownTipoPeriodo.getComponentValue() != null);
                    art.add(dropDownPeriodicidade);
                }
            });
        }
        return dropDownTipoPeriodo;
    }

    public DropDown<Long> getDropDownPeriodicidade(Long tipoPeriodo) {
        if (dropDownPeriodicidade == null)
            dropDownPeriodicidade = new DropDown<>("periodicidade");

        dropDownPeriodicidade.removeAllChoices();

        if (tipoPeriodo == null)
            dropDownPeriodicidade.addChoice(1L, "1");
        else if (tipoPeriodo == 1)
            for(long choice = 1; choice <= 4; choice++) {
                dropDownPeriodicidade.addChoice(choice, Long.toString(choice));
            }
        else if (tipoPeriodo == 2)
            for(long choice = 1; choice <= 12; choice++) {
                dropDownPeriodicidade.addChoice(choice, DataUtil.getDescricaoMes(choice));
            }
        return dropDownPeriodicidade;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioBMPOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioBMPOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioBMPO(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("balancoMedicamentosControlados");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    private void carregarEmpresaMaterial(Empresa empresa) {
        List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                .addProperties(new HQLProperties(Empresa.class).getProperties())
                .addProperty(VOUtils.montarPath(Empresa.PROP_EMPRESA_MATERIAL, EmpresaMaterial.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_EMPRESA_MATERIAL, EmpresaMaterial.PROP_NUM_LICENCA_FUNCIONAMENTO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_EMPRESA_MATERIAL, EmpresaMaterial.PROP_NUMERO_AUTORIZACAO_FUNCIONAMENTO))
                .setId(empresa).start().getList();
        autoCompleteConsultaEmpresa.getModel().setObject(empresaList);
    }
}
