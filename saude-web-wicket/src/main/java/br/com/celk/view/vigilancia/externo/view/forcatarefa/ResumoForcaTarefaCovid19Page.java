
package br.com.celk.view.vigilancia.externo.view.forcatarefa;

import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilanciaExternoCovid19;
import br.com.celk.view.vigilancia.externo.view.components.feedback.FeedBackVigilancia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaForcaTarefaCovid19DTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.forcatarefa.FichaForcaTarefaCovid19;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.string.StringValue;
import org.wicketstuff.annotation.mount.MountPath;

import java.util.List;

import static br.com.celk.view.vigilancia.externo.view.forcatarefa.ForcaTarefaCovid19Helper.listItem;

/**
 * <AUTHOR>
 */
@MountPath(ForcaTarefaCovid19Helper.URL_RESUMO_COVID19)
public class ResumoForcaTarefaCovid19Page extends BasePageVigilanciaExternoCovid19 {

    private Form<FichaForcaTarefaCovid19DTO> form;
    private FichaForcaTarefaCovid19DTO model;

    private FeedBackVigilancia feedBackVigilancia;
    private WebMarkupContainer containerAssintomatico;
    private WebMarkupContainer containerSintomatico;
    private WebMarkupContainer containerSintomas;
    private Label labSintomas;
    private Label labDataInicioSintomas;
    private Label labNome;
    private Label labDataPreenchimento;


    public ResumoForcaTarefaCovid19Page() {
        this.model = new FichaForcaTarefaCovid19DTO();
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();

        StringValue uuid = getPageParameters().get("uuid");
        this.model.setFichaForcaTarefaCovid19(obterFichaForcaTarefaCovid19(uuid.toString()));

        boolean isAssintomatico = RepositoryComponentDefault.SIM_INTEGER.equals(this.model.getFichaForcaTarefaCovid19().getAceiteTermo());

        this.form = new StatelessForm("form", new CompoundPropertyModel(this));
        this.form.add(criarFeedbackVigilancia());
        this.form.add(criarContainerAssintomatico(isAssintomatico));
        this.form.add(criarContainerSintomatico(isAssintomatico));
        this.form.add(criarContainerSintomas(isAssintomatico));
        this.form.add(criarLabelsGenericos());
        this.form.setOutputMarkupId(true);

        add(this.form);
    }

    private FeedBackVigilancia criarFeedbackVigilancia() {
        feedBackVigilancia = new FeedBackVigilancia("feedBack");
        feedBackVigilancia.setOutputMarkupId(true);
        return feedBackVigilancia;
    }

    private WebMarkupContainer criarContainerAssintomatico(boolean isAssintomatico) {
        containerAssintomatico = new WebMarkupContainer("containerAssintomatico");
        containerAssintomatico.setOutputMarkupId(true);
        containerAssintomatico.setVisible(isAssintomatico);
        return containerAssintomatico;
    }

    private WebMarkupContainer criarContainerSintomatico(boolean isAssintomatico) {
        containerSintomatico = new WebMarkupContainer("containerSintomatico");
        containerSintomatico.setOutputMarkupId(true);
        containerSintomatico.setVisible(!isAssintomatico);
        return containerSintomatico;
    }

    private WebMarkupContainer criarContainerSintomas(boolean isAssintomatico) {
        FichaForcaTarefaCovid19 ficha = this.model.getFichaForcaTarefaCovid19();

        containerSintomas = new WebMarkupContainer("containerSintomas");
        containerSintomas.add(this.criarLabSintomas(isAssintomatico, ficha));
        containerSintomas.add(this.criarLabDataInicioSintomas(isAssintomatico, ficha));
        containerSintomas.setOutputMarkupId(true);
        containerSintomas.setVisible(!isAssintomatico);

        return containerSintomas;
    }

    private Label criarLabSintomas(boolean isAssintomatico, FichaForcaTarefaCovid19 ficha) {
        labSintomas = new Label("labSintomas", new PropertyModel<String>(this, "labSintomas"));
        if (!isAssintomatico) {
            labSintomas.setDefaultModel(Model.of(criarHtmlSintomas(ficha)));
            labSintomas.setEscapeModelStrings(false);
        }
        return labSintomas;
    }

    private Label criarLabDataInicioSintomas(boolean isAssintomatico, FichaForcaTarefaCovid19 ficha) {
        labDataInicioSintomas = new Label("labDataInicioSintomas", new PropertyModel<String>(this, "labDataInicioSintomas"));
        if (!isAssintomatico && ficha.getDataInicioSintomas() != null) {
            String dataInicioSintomas = ForcaTarefaCovid19Helper.sdf.format(ficha.getDataInicioSintomas());
            labDataInicioSintomas.setDefaultModel(Model.of(dataInicioSintomas));
        }
        return labDataInicioSintomas;
    }

    private String criarHtmlSintomas(FichaForcaTarefaCovid19 ficha) {
        StringBuilder sb = new StringBuilder();
        sb.append("<ul>");
        if (ficha.getFebre() > 0) sb.append(listItem("Febre"));
        if (ficha.getTosse() > 0) sb.append(listItem("Tosse"));
        if (ficha.getFaltaDeAr() > 0) sb.append(listItem("Falta de Ar"));
        if (ficha.getCongestaoNasal() > 0) sb.append(listItem("Congestão Nasal"));
        if (ficha.getNarizEscorrendo() > 0) sb.append(listItem("Nariz Escorrendo"));
        if (ficha.getDorDeGarganta() > 0) sb.append(listItem("Dor de Garganta/Dificuldade de Engolir"));
        sb.append("</ul>");
        return sb.toString();
    }

    private Label[] criarLabelsGenericos() {
        FichaForcaTarefaCovid19 ficha = this.model.getFichaForcaTarefaCovid19();

        labNome = new Label("labNome", new PropertyModel<String>(this, "labNome"));
        labNome.setDefaultModel(Model.of(ficha.getNome()));

        labDataPreenchimento = new Label("labDataPreenchimento", new PropertyModel<String>(this, "labDataPreenchimento"));
        if (ficha.getDataCadastro() != null) {
            String dataCadastro = ForcaTarefaCovid19Helper.sdf.format(ficha.getDataCadastro());
            labDataPreenchimento.setDefaultModel(Model.of(dataCadastro));
        }
        return new Label[] { labNome, labDataPreenchimento };
    }

    private FichaForcaTarefaCovid19 obterFichaForcaTarefaCovid19(String uuid) {
        List<FichaForcaTarefaCovid19> listaFichaForcaTarefaCovid19 = LoadManager.getInstance(FichaForcaTarefaCovid19.class)
            .addProperties(new HQLProperties(FichaForcaTarefaCovid19.class).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(FichaForcaTarefaCovid19.PROP_UUID_QR_CODE, uuid))
            .addSorter(new QueryCustom.QueryCustomSorter(FichaForcaTarefaCovid19.PROP_DATA_CADASTRO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
            .start().getList();
        if (listaFichaForcaTarefaCovid19 != null
        && !listaFichaForcaTarefaCovid19.isEmpty()) {
            return listaFichaForcaTarefaCovid19.get(0);
        }
        //TODO Disparar erro
        return null;
    }

    @Override
    public String getTituloPrograma() {
        return "FORÇA TAREFA COVID-19";
    }

    @Override
    public String getSufixoVigilancia() {
        return "";
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(Resources.CSS_VIGILANCIA_EXTERNO));
        response.render(CssHeaderItem.forReference(Resources.CSS_BOOTSTRAP));
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_BOOTSTRAP));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_VALIDATOR));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_VALIDATOR_MIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_NOVO_CADASTRO_VIGILANCIA));
    }

    @Override
    public FeedBackVigilancia getFeedBackVigilancia() {
        return feedBackVigilancia;
    }

    private boolean validar(Form form) {
        System.err.println("NYI");
        return true;
    }

    private void salvar() throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaForcaTarefaCovid19(this.model);
    }

}
