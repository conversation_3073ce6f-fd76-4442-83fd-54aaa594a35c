package br.com.celk.view.agenda.agendamento;

import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.AgendamentoListaEsperaPageParameters;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.BuildAgendamentoListaEsperoDtoParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import org.apache.wicket.request.mapper.parameter.PageParameters;

public class AdicionarFiltrosAgendamentoListaEspera {

    private final PageParameters pageParameters;
    private boolean deveManterFiltro;

    public AdicionarFiltrosAgendamentoListaEspera(PageParameters pageParameters) {
        this.pageParameters = pageParameters;
    }

    public AdicionarFiltrosAgendamentoListaEspera deveManterFiltro(boolean deveManterFiltro) {
        this.deveManterFiltro = deveManterFiltro;
        return this;
    }

    public PageParameters addFiltersPageParameters(AgendamentoListaEsperaDTOParam listaEsperaDTOParam) {
        return new AgendamentoListaEsperaPageParameters(pageParameters, deveManterFiltro).limparFiltro()
                                                                                         .addCodigoTipoProcedimento(listaEsperaDTOParam.getTipoProcedimento())
                                                                                         .addCodigoExameProcedimento(listaEsperaDTOParam.getExameProcedimento())
                                                                                         .addDataNascimento(listaEsperaDTOParam.getDataNascimento())
                                                                                         .addApenasReagendamento(listaEsperaDTOParam.getApenasReagendamento())
                                                                                         .addEmpresas(listaEsperaDTOParam.getEmpresas())
                                                                                         .addCodigoOrigemSolicitacao(listaEsperaDTOParam.getOrigemSolicitacao())
                                                                                         .addCodigoUnidadeResponsavel(listaEsperaDTOParam.getUnidadeResponsavel())
                                                                                         .addNomePaciente(listaEsperaDTOParam.getPaciente())
                                                                                         .addCodigoSolicitacao(listaEsperaDTOParam.getCodigoSolicitacao())
                                                                                         .addCodigoPaciente(listaEsperaDTOParam.getCodigoPaciente())
                                                                                         .addCns(listaEsperaDTOParam.getCns())
                                                                                         .addPrioridade(listaEsperaDTOParam.getPrioridade())
                                                                                         .addCodigoTipoConsulta(listaEsperaDTOParam.getTipoConsulta())
                                                                                         .addNumeroAuxiliar(listaEsperaDTOParam.getNumeracaoAuxiliar())
                                                                                         .addApenasBloqueio(listaEsperaDTOParam.getApenasBloqueada())
                                                                                         .build();
    }

    public AgendamentoListaEsperaDTOParam addFiltersAgendamentoListaEsperaDTOParam(AgendamentoListaEsperaDTOParam param) {
        return new BuildAgendamentoListaEsperoDtoParam(pageParameters, param).addCodigoTipoProcedimento()
                                                                             .addCodigoExameProcedimento()
                                                                             .addDataNascimento()
                                                                             .addApenasReagendamento()
                                                                             .addCodigoEmpresa()
                                                                             .addCodigoOrigemSolicitacao()
                                                                             .addCodigoUnidadeResponsavel()
                                                                             .addNomePaciente()
                                                                             .addCodigoSolicitacao()
                                                                             .addCodigoPaciente()
                                                                             .addCns()
                                                                             .addPrioridade()
                                                                             .addTipoConsulta()
                                                                             .addNumeracaoAuxiliar()
                                                                             .addApenasBloqueada()
                                                                             .build();
    }
}
