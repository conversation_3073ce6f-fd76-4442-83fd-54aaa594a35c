package br.com.celk.view.unidadesaude.exames.prestadorservico;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.panel.EditDeleteActionsColumnPanel;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.basico.PessoaContrato;
import br.com.ksisolucoes.vo.prontuario.basico.ExameCotaPpi;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorUnidade;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.text.ParseException;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroPrestadorServicoStep1Page extends BasePage {

    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;
    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExameSecundario;
    private boolean consulta;
    private ExamePrestadorCadastroDTO examePrestadorCadastroDTO;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private List<ExamePrestadorUnidade> listEmpresas = new ArrayList<ExamePrestadorUnidade>();
    private ExamePrestadorUnidade examePrestadorUnidade;
    private Table table;
    private AutoCompleteConsultaEmpresa autoCompleteConsulaEmpresa;
    private String tipoTeto;
    private Form containerFisico;
    private Form containerFinanceiro;
    private LongField txtTetoFisico;
    private DoubleField txtTetoFinanceiro;
    private DoubleField txtRecursoProprioFinanceiro;
    private InputField txtTipoTeto;
    private DropDown dropDownAgendaEspecialista;
    private InputField txtNumeroPacientesAtendidos;
    private HoraMinutoField txtHoraInicioAtendimento;

    private Form<ExamePrestadorCadastroDTO> form;
    private ExamePrestadorContrato examePrestadorContratoObject;
    private Form<ExamePrestadorContrato> formExamePrestadorContrato;
    private Table<ExamePrestadorContrato> tblExamePrestadorContrato;
    private InputField<Long> txtNumeroContrato;
    private DateChooser dcDataContrato;
    private DateChooser dcValidadeContrato;
    private DropDown<String> dropDownSituacao;
    private CompoundPropertyModel<ExamePrestadorContrato> modelExamePrestadorContrato;
    private List<ExamePrestadorContrato> examePrestadorContratoList = new ArrayList<ExamePrestadorContrato>();
    private boolean editar;

    public CadastroPrestadorServicoStep1Page(Long codigo) {
        this.consulta = true;
        init(codigo);
        carregaContratos();
    }

    public CadastroPrestadorServicoStep1Page() {
        init(null);
        carregaContratos();
    }

    public void init(Long codigoExamePrestador) {
        form = new Form("form", new CompoundPropertyModel(examePrestadorCadastroDTO = new ExamePrestadorCadastroDTO()));
        ExamePrestadorCadastroDTO proxy = Lambda.on(ExamePrestadorCadastroDTO.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(CoreMethods.path(proxy.getExamePrestador().getPrestador()), true));
        form.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame(CoreMethods.path(proxy.getExamePrestador().getTipoExame())));
        form.add(autoCompleteConsultaTipoExameSecundario = new AutoCompleteConsultaTipoExame(CoreMethods.path(proxy.getExamePrestador().getTipoExameSecundario())));
        autoCompleteConsultaTipoExame.add(new ConsultaListener<TipoExame>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoExame object) {
                ExameCotaPpi ecp = LoadManager.getInstance(ExameCotaPpi.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), object.getCodigo()))
                        .start().getVO();
                if (ecp != null) {
                    if (ExameCotaPpi.TipoTeto.FINANCEIRO.value().equals(ecp.getTipoTeto())) {
                        JScript.showFieldset(target, containerFinanceiro);
                        JScript.hideFieldset(target, containerFisico);
                        txtTetoFisico.setEnabled(false);
                        txtTetoFinanceiro.setEnabled(true);
                        txtRecursoProprioFinanceiro.setEnabled(true);
                        tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
                    } else if (ExameCotaPpi.TipoTeto.FISICO.value().equals(ecp.getTipoTeto())) {
                        JScript.hideFieldset(target, containerFinanceiro);
                        JScript.showFieldset(target, containerFisico);
                        txtTetoFisico.setEnabled(true);
                        txtTetoFinanceiro.setEnabled(false);
                        txtRecursoProprioFinanceiro.setEnabled(false);
                        tipoTeto = ExameCotaPpi.TipoTeto.FISICO.descricao();
                    }
                } else {
                    JScript.showFieldset(target, containerFinanceiro);
                    JScript.hideFieldset(target, containerFisico);
                    txtTetoFisico.setEnabled(false);
                    txtTetoFinanceiro.setEnabled(true);
                    txtRecursoProprioFinanceiro.setEnabled(true);
                    tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
                }
                txtTetoFinanceiro.limpar(target);
                txtRecursoProprioFinanceiro.limpar(target);
                txtTetoFisico.limpar(target);
                target.add(txtTipoTeto);
            }
        });
        autoCompleteConsultaTipoExame.add(new RemoveListener<TipoExame>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoExame object) {
                JScript.showFieldset(target, containerFinanceiro);
                JScript.hideFieldset(target, containerFisico);
                txtTetoFisico.setEnabled(false);
                txtTetoFinanceiro.setEnabled(true);
                txtTetoFinanceiro.limpar(target);
                txtRecursoProprioFinanceiro.setEnabled(true);
                txtRecursoProprioFinanceiro.limpar(target);
                txtTetoFisico.limpar(target);
                tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
                target.add(txtTipoTeto);
            }
        });
        form.add(txtTipoTeto = (InputField) new InputField("tipoTeto", new PropertyModel(this, "tipoTeto")).setEnabled(false));
        containerFisico = new Form("containerFisico");
        containerFisico.setOutputMarkupId(true);
        containerFisico.add(txtTetoFisico = new LongField(CoreMethods.path(proxy.getExamePrestador().getTetoFinanceiro())));
        form.add(containerFisico);
        containerFinanceiro = new Form("containerFinanceiro");
        containerFinanceiro.setOutputMarkupId(true);
        containerFinanceiro.add(txtTetoFinanceiro = new DoubleField(CoreMethods.path(proxy.getExamePrestador().getTetoFinanceiro())));
        containerFinanceiro.add(txtRecursoProprioFinanceiro = new DoubleField(CoreMethods.path(proxy.getExamePrestador().getRecursoProprio())));
        txtRecursoProprioFinanceiro.add(new Tooltip().setText("msgDefinicaoCampoRecursoProprio"));
        form.add(containerFinanceiro);
        form.add(DropDownUtil.getIEnumDropDown(CoreMethods.path(proxy.getExamePrestador().getTipoCota()), ExamePrestador.TipoCota.values()));
        form.add(txtNumeroPacientesAtendidos = new InputField(CoreMethods.path(proxy.getExamePrestador().getNumeroPacienteAtendidos())));
        form.add(txtHoraInicioAtendimento = new HoraMinutoField(path(proxy.getExamePrestador().getHoraInicial())));

        form.add(dropDownAgendaEspecialista = DropDownUtil.getNaoSimIntegerDropDown(CoreMethods.path(proxy.getExamePrestador().getAgendaEspecialista())));
        dropDownAgendaEspecialista.add(new Tooltip().setText("examePrestadorPossuiAgendaEspecialista"));
        dropDownAgendaEspecialista.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarAgendamento(target);
            }
        });

        if (consulta) {
            autoCompleteConsultaTipoExame.setEnabled(false);
            autoCompleteConsultaEmpresa.setEnabled(false);
            if (form.getModel().getObject().getExamePrestador() != null && form.getModel().getObject().getExamePrestador().getTipoExameSecundario() != null) {
                autoCompleteConsultaTipoExameSecundario.setEnabled(false);
            }
        }

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar(target);
            }
        }) {

            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                ExameCotaPpi ecp = null;
                if (examePrestadorCadastroDTO.getExamePrestador() != null) {
                    ecp = LoadManager.getInstance(ExameCotaPpi.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), examePrestadorCadastroDTO.getExamePrestador().getCodigo()))
                            .start().getVO();
                }
                if (ecp != null) {
                    if (ExameCotaPpi.TipoTeto.FINANCEIRO.value().equals(ecp.getTipoTeto())) {
                        JScript.showFieldset(containerFinanceiro);
                        JScript.hideFieldset(containerFisico);
                        txtTetoFisico.setEnabled(false);
                        txtTetoFinanceiro.setEnabled(true);
                        txtRecursoProprioFinanceiro.setEnabled(true);
                        tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
                    } else if (ExameCotaPpi.TipoTeto.FISICO.value().equals(ecp.getTipoTeto())) {
                        JScript.hideFieldset(containerFinanceiro);
                        JScript.showFieldset(containerFisico);
                        txtTetoFisico.setEnabled(true);
                        txtTetoFinanceiro.setEnabled(false);
                        txtRecursoProprioFinanceiro.setEnabled(false);
                        tipoTeto = ExameCotaPpi.TipoTeto.FISICO.descricao();
                    }
                } else {
                    JScript.showFieldset(containerFinanceiro);
                    JScript.hideFieldset(containerFisico);
                    txtTetoFisico.setEnabled(false);
                    txtTetoFinanceiro.setEnabled(true);
                    txtRecursoProprioFinanceiro.setEnabled(true);
                    tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
                }
            }
        });

        WebMarkupContainer containerUnidades = new WebMarkupContainer("containerUnidades", new CompoundPropertyModel(this));
        containerUnidades.add(autoCompleteConsulaEmpresa = new AutoCompleteConsultaEmpresa("examePrestadorUnidade.empresa"));
        containerUnidades.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarEmpresa(target);
            }
        });
        containerUnidades.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();

        formExamePrestadorContrato = new Form("formExamePrestadorContrato", modelExamePrestadorContrato = new CompoundPropertyModel(new ExamePrestadorContrato()));
        examePrestadorContratoObject = formExamePrestadorContrato.getModel().getObject();

        ExamePrestadorContrato proxyExamePrestadorContrato = on(ExamePrestadorContrato.class);
        formExamePrestadorContrato.add(txtNumeroContrato = new InputField<Long>(path(proxyExamePrestadorContrato.getNumeroContrato())));
        txtNumeroContrato.addRequiredClass();
        formExamePrestadorContrato.add(dcDataContrato = new DateChooser(path(proxyExamePrestadorContrato.getDataContrato())));
        dcDataContrato.addRequiredClass();
        formExamePrestadorContrato.add(dcValidadeContrato = new DateChooser(path(proxyExamePrestadorContrato.getDataValidade())));
        dcValidadeContrato.addRequiredClass();
        formExamePrestadorContrato.add(dropDownSituacao = DropDownUtil.getIEnumDropDown(path(proxyExamePrestadorContrato.getSituacao()), PessoaContrato.Situacao.values()));
        dropDownSituacao.addRequiredClass();

        formExamePrestadorContrato.add(new AbstractAjaxButton("btnAdicionarContrato") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                try {
                    adicionarContrato(target, form);
                } catch (ParseException e) {
                    Loggable.log.error(e);
                }
            }
        });

        formExamePrestadorContrato.add(tblExamePrestadorContrato = new Table("tblExamePrestadorContrato", getColumnsContrato(), getCollectionProviderContrato()));
        tblExamePrestadorContrato.populate();
        form.add(formExamePrestadorContrato);
        form.add(containerUnidades);

        add(form);

        carregarExamePrestador(codigoExamePrestador);
        habilitarAgendamento(null);
    }

    private void habilitarAgendamento(AjaxRequestTarget target) {
        boolean isAgendaEspecialista = RepositoryComponentDefault.SIM_INTEGER.equals(dropDownAgendaEspecialista.getComponentValue());
        txtHoraInicioAtendimento.setEnabled(!isAgendaEspecialista);
        txtNumeroPacientesAtendidos.setEnabled(!isAgendaEspecialista);

        if(target != null){
            txtNumeroPacientesAtendidos.limpar(target);
            txtHoraInicioAtendimento.limpar(target);
            target.add(txtNumeroPacientesAtendidos);
            target.add(txtHoraInicioAtendimento);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private List<IColumn> getColumnsContrato() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ExamePrestadorContrato proxy = on(ExamePrestadorContrato.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("numeroContratoAbrev"), proxy.getNumeroContrato()));
        columns.add(createColumn(bundle("dataContratoAbrev"), proxy.getDataContrato()));
        columns.add(createColumn(bundle("validadeContratoAbrev"), proxy.getDataValidade()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoFormatado()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderContrato() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return examePrestadorContratoList;
            }
        };
    }

    private Collection carregaContratos() {
        if (form.getModelObject().getExamePrestador() != null && form.getModelObject().getExamePrestador().getCodigo() != null){
            examePrestadorContratoList = LoadManager.getInstance(ExamePrestadorContrato.class)
                    .addProperties(new HQLProperties(ExamePrestadorContrato.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_CODIGO), form.getModelObject().getExamePrestador().getCodigo()))
                    .start().getList();
        }
        return examePrestadorContratoList;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<ExamePrestadorContrato>() {

            @Override
            public Component getComponent(String componentId, final ExamePrestadorContrato rowObject) {
                return new EditDeleteActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        editarRegistro(target, rowObject);
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblExamePrestadorContrato, examePrestadorContratoList, rowObject);
                        tblExamePrestadorContrato.update(target);
                    }
                };
            }
        };
    }

    private void editarRegistro(AjaxRequestTarget target, ExamePrestadorContrato examePrestadorContrato) {
        limparFormPessoaContrato(target);
        formExamePrestadorContrato.setModelObject(examePrestadorContrato);
        examePrestadorContratoObject = formExamePrestadorContrato.getModelObject();
        target.add(formExamePrestadorContrato);
        editar = true;
    }

    public DropDown getDropDownSituacao() {
        if (this.dropDownSituacao == null) {
            this.dropDownSituacao = new DropDown<String>(Pessoa.PROP_FLAG);
            this.dropDownSituacao.addChoice(Pessoa.ATIVA, BundleManager.getString("ativo"));
            this.dropDownSituacao.addChoice(Pessoa.INATIVA, BundleManager.getString("inativo"));
            this.dropDownSituacao.addChoice(Pessoa.EXCLUIDA, BundleManager.getString("excluido"));
        }
        return dropDownSituacao;
    }


    private void adicionarContrato(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ParseException {
        verificarCamposNulos();
        verificarData();
        if(!editar) {
            CrudUtils.adicionarItem(target, tblExamePrestadorContrato, examePrestadorContratoList, examePrestadorContratoObject);
            tblExamePrestadorContrato.update(target);
            limparFormPessoaContrato(target);
        }else {
            List<ExamePrestadorContrato> transfer = new ArrayList<>();
            for (ExamePrestadorContrato contrato : examePrestadorContratoList) {
                if (examePrestadorContratoObject == contrato || examePrestadorContratoObject.equals(contrato)) {
                    transfer.add(examePrestadorContratoObject);
                } else {
                    transfer.add(contrato);
                }
            }
            examePrestadorContratoList = transfer;
            target.add(tblExamePrestadorContrato);
            limparFormPessoaContrato(target);
        }
    }

    private void limparFormPessoaContrato(AjaxRequestTarget target) {
        formExamePrestadorContrato.setModelObject(new ExamePrestadorContrato());
        examePrestadorContratoObject = formExamePrestadorContrato.getModel().getObject();
        txtNumeroContrato.limpar(target);
        dcDataContrato.limpar(target);
        dcValidadeContrato.limpar(target);
        dropDownSituacao.limpar(target);
        editar=false;
    }

    private void verificarCamposNulos() throws ValidacaoException {
        if (txtNumeroContrato.getComponentValue()==null||dcDataContrato.getComponentValue()==null
                ||dcValidadeContrato.getComponentValue()==null){
            throw  new ValidacaoException("É necessário preencher todos os campos para adicionar um contrato!");
        }
    }

    private void verificarData() throws ValidacaoException, ParseException {
        Date dataValidade = examePrestadorContratoObject.getDataValidade();
        Date dataContrato = examePrestadorContratoObject.getDataContrato();
        String[] dValidade= dataValidade.toLocaleString().split(" ");
        dValidade= dValidade[0].split("/");
        String[] dContrato= dataContrato.toLocaleString().split(" ");
        dContrato = dContrato[0].split("/");
        int[] ano= {Integer.parseInt(dValidade[2]),Integer.parseInt(dContrato[2])};
        if(!(ano[1]>1850/*Data de contrato*/)){
            throw  new ValidacaoException("O ano do contrato têm que ser superior a 1850!");
        }
        if (Data.getDataAtual().compareTo(dataContrato)<0){
            throw  new ValidacaoException("A data de contrato não pode ser maior que a data atual");
        }
        if(!(dataValidade.compareTo(dataContrato)>0)){
            throw  new ValidacaoException("A data de validade têm que ser maior que a data do contrato");
        }

    }

    private void adicionarEmpresa(AjaxRequestTarget target) throws ValidacaoException {
        if (examePrestadorUnidade == null || examePrestadorUnidade.getEmpresa() == null) {
            throw new ValidacaoException(BundleManager.getString("informeUnidade"));
        }
        for (ExamePrestadorUnidade empresa1 : listEmpresas) {
            if (examePrestadorUnidade.getEmpresa().equals(empresa1.getEmpresa())) {
                throw new ValidacaoException(BundleManager.getString("unidadeJaAdicionada"));
            }
        }

        listEmpresas.add(examePrestadorUnidade);

        examePrestadorUnidade = null;
        autoCompleteConsulaEmpresa.limpar(target);
        autoCompleteConsulaEmpresa.focus(target);
        table.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return listEmpresas;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ExamePrestadorUnidade proxy = Lambda.on(ExamePrestadorUnidade.class);
        ColumnFactory columnFactory = new ColumnFactory(ExamePrestadorUnidade.class);

        columns.add(getCustomActionColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), CoreMethods.path(proxy.getEmpresa().getDescricao())));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<ExamePrestadorUnidade>() {
            @Override
            public void customizeColumn(ExamePrestadorUnidade rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ExamePrestadorUnidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExamePrestadorUnidade modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < listEmpresas.size(); i++) {
                            if (listEmpresas.get(i) == modelObject) {
                                listEmpresas.remove(i);
                                break;
                            }
                        }
                        table.update(target);
                    }
                });
            }
        };
    }

    private void carregarExamePrestador(Long codigoExamePrestador) {
        if (codigoExamePrestador != null) {
            ExamePrestador examePrestador = LoadManager.getInstance(ExamePrestador.class).setId(codigoExamePrestador).start().getVO();
            examePrestadorCadastroDTO.setExamePrestador(examePrestador);

            listEmpresas = LoadManager.getInstance(ExamePrestadorUnidade.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestadorUnidade.PROP_EXAME_PRESTADOR, examePrestador))
                    .start().getList();
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroFpoPrestadorServico");
    }

    private void avancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Empresa prestador = examePrestadorCadastroDTO.getExamePrestador().getPrestador();

        if (!consulta && examePrestadorCadastroDTO.getExamePrestador().getTipoExame() == null) {

            boolean existePrestadorSemTipoExame = LoadManager.getInstance(ExamePrestador.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_PRESTADOR, prestador))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_TIPO_EXAME, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .exists();
            if (existePrestadorSemTipoExame) {
                throw new ValidacaoException(bundle("msgExisteCadastroPrestadorXSemTipoExame", prestador.getDescricao()));
            }
        } else if (!consulta && examePrestadorCadastroDTO.getExamePrestador().getTipoExame() != null) {
            boolean existePrestadorSemTipoExame = LoadManager.getInstance(ExamePrestador.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_PRESTADOR, prestador))
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_TIPO_EXAME, examePrestadorCadastroDTO.getExamePrestador().getTipoExame()))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_TIPO_EXAME_SECUNDARIO, examePrestadorCadastroDTO.getExamePrestador().getTipoExame()))))))
                    .exists();
            if (existePrestadorSemTipoExame) {
                throw new ValidacaoException(bundle("msgExisteCadastroPrestadorXComTipoExameXInformado", prestador.getDescricao(),
                        examePrestadorCadastroDTO.getExamePrestador().getTipoExame().getDescricao()));
            }
        }
        if (examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario() != null) {
            boolean existePrestadorSemTipoExame = LoadManager.getInstance(ExamePrestador.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_PRESTADOR, prestador))
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_TIPO_EXAME, examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario()))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(ExamePrestador.PROP_TIPO_EXAME_SECUNDARIO, examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario()))))))
                    .exists();
            if (existePrestadorSemTipoExame) {
                throw new ValidacaoException(bundle("msgExisteCadastroPrestadorXComTipoExameXInformado", prestador.getDescricao(),
                        examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario().getDescricao()));
            }
            if (examePrestadorCadastroDTO.getExamePrestador().getTipoExame().getCodigo().equals(examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario().getCodigo())) {
                throw new ValidacaoException(bundle("msgTipoExameNaoPodeSerIgualTipoExameSecundario"));
            }
        }
        examePrestadorCadastroDTO.setUnidades(listEmpresas);
        examePrestadorCadastroDTO.setContratoList(examePrestadorContratoList);
        examePrestadorCadastroDTO.setControleFinanceiro(tipoTeto.equals(ExameCotaPpi.TipoTeto.FINANCEIRO.descricao()) && Coalesce.asDouble(form.getModelObject().getExamePrestador().getRecursoProprio()) > 0D);

        Page page = new CadastroPrestadorServicoStep2Page(examePrestadorCadastroDTO, consulta);
        setResponsePage(page);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        ExameCotaPpi ecp = null;
        if (examePrestadorCadastroDTO.getExamePrestador() != null && examePrestadorCadastroDTO.getExamePrestador().getTipoExame() != null) {
            ecp = LoadManager.getInstance(ExameCotaPpi.class)
                .addParameter(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), examePrestadorCadastroDTO.getExamePrestador().getTipoExame().getCodigo()))))))
                .start().getVO();
        }
        if (ecp != null) {
            if (ExameCotaPpi.TipoTeto.FINANCEIRO.value().equals(ecp.getTipoTeto())) {
                response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerFinanceiro)));
                response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerFisico)));
                txtTetoFisico.setEnabled(false);
                txtTetoFinanceiro.setEnabled(true);
                txtRecursoProprioFinanceiro.setEnabled(true);
                tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
            } else if (ExameCotaPpi.TipoTeto.FISICO.value().equals(ecp.getTipoTeto())) {
                response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerFinanceiro)));
                response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerFisico)));
                txtTetoFisico.setEnabled(true);
                txtTetoFinanceiro.setEnabled(false);
                txtRecursoProprioFinanceiro.setEnabled(false);
                tipoTeto = ExameCotaPpi.TipoTeto.FISICO.descricao();
            }
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerFinanceiro)));
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerFisico)));
            txtTetoFisico.setEnabled(false);
            txtTetoFinanceiro.setEnabled(true);
            txtRecursoProprioFinanceiro.setEnabled(true);
            tipoTeto = ExameCotaPpi.TipoTeto.FINANCEIRO.descricao();
        }
    }
}
