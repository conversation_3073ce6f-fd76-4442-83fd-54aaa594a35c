package br.com.celk.view.vigilancia.externo.view.consulta.dlg;

import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class DlgAnexosVeiculosObservacao extends Window {

    private PnlAnexosVeiculosObservacao pnlAnexosObservacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean enabled;
    private TipoSolicitacao tipoSolicitacao;

    public DlgAnexosVeiculosObservacao(String id, RequerimentoVigilancia requerimentoVigilancia, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled) {
        super(id);
        init(requerimentoVigilancia, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, false);
    }

    public DlgAnexosVeiculosObservacao(String id, RequerimentoVigilancia requerimentoVigilancia, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled, boolean exibirObservacao) {
        super(id);
        init(requerimentoVigilancia, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, exibirObservacao);
    }

    private void init(RequerimentoVigilancia requerimentoVigilancia, TipoSolicitacao tipoSolicitacao, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, boolean enabled, boolean exibirObservacao) {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("anexos");
            }
        });

        setInitialWidth(1024);
        setInitialHeight(450);
        setResizable(true);

        setContent(pnlAnexosObservacao = new PnlAnexosVeiculosObservacao(getContentId(), requerimentoVigilancia, tipoSolicitacao, requerimentoVigilanciaAnexoDTOList, enabled, exibirObservacao) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException {
                close(target);
                DlgAnexosVeiculosObservacao.this.onConfirmar(target, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList);
            }

            @Override
            public void onConfirmarReenviar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException {
                close(target);
                DlgAnexosVeiculosObservacao.this.onConfirmarReenviar(target, requerimentoVigilanciaAnexoDTOList, requerimentoVigilanciaAnexoDTOExcluidoList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public boolean isBtnConfirmarReenviarVisible() {
                return DlgAnexosVeiculosObservacao.this.isBtnConfirmarReenviarVisible();
            }
        });
    }

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, ActionType actionType) {
        show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException;

    public abstract void onConfirmarReenviar(AjaxRequestTarget target, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList, List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOExcluidoList) throws ValidacaoException, DAOException;

    public boolean isBtnConfirmarReenviarVisible() {
        return true;
    }

}