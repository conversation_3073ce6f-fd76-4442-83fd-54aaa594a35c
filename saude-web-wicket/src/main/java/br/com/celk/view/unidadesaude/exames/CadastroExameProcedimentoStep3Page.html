<wicket:extend>
    <div class="span-10 last">
        <form wicket:id="form">
            <fieldset>
                <h2><label><wicket:message key="dados"/></label></h2>
                <div class="field"><label><wicket:message key="grupoProcedimento"/></label><input type="text" size="60" wicket:id="tipoExame.descricao"/></div>
                <div wicket:id="container">
                    <div class="field"><label><wicket:message key="ppiGrupo"/></label><div class="group" wicket:id="ppiGrupo"/></div>
                    <div class="field"><label><wicket:message key="exameEsus"/></label><div class="group" wicket:id="exameEsus"/></div>
                    <div class="field"><label><wicket:message key="exibirNoApac"/></label><select wicket:id="flagExibirNoApac"/></div>
                    <div class="field">
                        <div class="span-horizontal">
                            <fieldset>
                                <h2><label><wicket:message key="estrutura"/></label></h2>
                                <div class="field"><label><wicket:message key="grupo"/></label><div class="group" wicket:id="procedimentoGrupo"/></div>
                                <div class="field"><label><wicket:message key="subGrupo"/></label><select wicket:id="procedimentoSubGrupo"/></div>
                                <div class="field"><label><wicket:message key="formaOrganizacao"/></label><select wicket:id="procedimentoFormaOrganizacao"/></div>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </fieldset>
            <div id="control-bottom">
                <input type="button" wicket:id="btnVoltar" wicket:message="value:voltar" class="arrow-left"/>
                <input type="button" wicket:id="btnSalvar" wicket:message="value:salvar" class="save"/>
            </div>
        </form>
    </div>
</wicket:extend>