package br.com.celk.view.unidadesaude.processos.solicitacaoprioridade;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoPrioridade;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.extract;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaSolicitacaoPrioridadePage extends ConsultaPage<SolicitacaoPrioridade, List<BuilderQueryCustom.QueryParameter>> {

    private Long nrSolicitacao;
    private String nomePaciente;
    private DatePeriod periodo;
    private TipoProcedimento tipoProcedimento;
    private Long codigoPaciente;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new LongField("nrSolicitacao"));
        form.add(new InputField("nomePaciente"));
        form.add(new InputField("codigoPaciente"));
        form.add(getDropDownTipoProcedimento("tipoProcedimento"));
        form.add(new PnlDatePeriod("periodo"));

        getLinkNovo().setVisible(false);
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        SolicitacaoPrioridade proxy = on(SolicitacaoPrioridade.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("tipoProcedimento"), proxy.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("paciente"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNome(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(BundleManager.getString("dataNascimento"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento()));
        columns.add(createSortableColumn(BundleManager.getString("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(BundleManager.getString("profissionalSolicitante"), proxy.getSolicitacaoAgendamento().getNomeProfissionalOrigem()));
        columns.add(createSortableColumn(BundleManager.getString("usuario"), proxy.getSolicitacaoAgendamento().getUsuarioUrgente().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<SolicitacaoPrioridade>() {
            @Override
            public void customizeColumn(SolicitacaoPrioridade rowObject) {
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<SolicitacaoPrioridade>() {
                    @Override
                    public void action(AjaxRequestTarget target, SolicitacaoPrioridade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new OrdenacaoPrioridadePage(modelObject));
                    }
                }).setTitleBundleKey("analisar");
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SolicitacaoPrioridade.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(SolicitacaoPrioridade.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_URGENTE, Usuario.PROP_NOME),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_NOME_PROFISSIONAL_ORIGEM),
                                VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO),
                        });
            }

            @Override
            public void consultaCustomizeSorters(List<BuilderQueryCustom.QuerySorter> sorters) {
                sorters.add(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_CODIGO), QueryCustom.QueryCustomSorter.CRESCENTE));
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SolicitacaoPrioridade.PROP_DATA_CADASTRO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (Coalesce.asLong(this.nrSolicitacao) > 0L) {
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_CODIGO, this.nrSolicitacao));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO), this.tipoProcedimento));
        if (this.nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), this.codigoPaciente));

        if (this.periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_DATA_CADASTRO, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, this.periodo.getDataInicial()));
            parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_DATA_CADASTRO, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, this.periodo.getDataFinal()));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_TFD), RepositoryComponentDefault.NAO));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR)));
        parameters.add(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_STATUS, SolicitacaoPrioridade.Status.PENDENTE.value()));

        return parameters;
    }

    private DropDown getDropDownTipoProcedimento(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");
        for (TipoProcedimento tipoProcedimento : getListaTipoProcedimentos()) {
            dropDown.addChoice(tipoProcedimento, tipoProcedimento.getDescricao());
        }

        return dropDown;
    }

    private List<TipoProcedimento> getListaTipoProcedimentos() {
        List<SolicitacaoPrioridade> lstSP = LoadManager.getInstance(SolicitacaoPrioridade.class)
                .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_TFD), RepositoryComponentDefault.NAO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR)))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_STATUS, SolicitacaoPrioridade.Status.PENDENTE.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO)))
                .start().getList();

        List<TipoProcedimento> extract = extract(lstSP, on(SolicitacaoPrioridade.class).getSolicitacaoAgendamento().getTipoProcedimento());

        return extract;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("priorizacaoSolicitacoes");
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaSolicitacaoPrioridadePage.class;
    }

}
