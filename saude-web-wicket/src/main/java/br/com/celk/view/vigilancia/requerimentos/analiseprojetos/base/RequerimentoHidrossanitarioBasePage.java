package br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.dialog.DlgCadastroResponsavelTecnico;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.*;
import br.com.celk.view.vigilancia.requerimentovigilancia.autocomplete.AutoCompleteConsultaRequerimentoProjetoHidrossanitario;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.autocomplete.AutoCompleteConsultaTipoEnquadramentoProjeto;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.dialog.DlgCadastroTipoEnquadramentoProjeto;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete.AutoCompleteConsultaTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.TipoEnquadramentoProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.record.formula.functions.T;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base.RequerimentosHelper.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class RequerimentoHidrossanitarioBasePage extends BasePage implements RequerimentoHidrossanitarioBaseInterface {

    private Form<T> form;
    private Class pageReturn;
    private TipoSolicitacao tipoSolicitacao;
    private Estabelecimento estabelecimento;
    protected RequerimentoHidrossanitarioBaseDTO baseDTO;

    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgImpressao;

    private boolean modoEdicao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private SubmitButton btnVoltar;
    private SubmitButton btnSalvar;
    private WebMarkupContainer containerOutros;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    private WebMarkupContainer containerNumeroProtocolo;
    private DisabledInputField numeroProtocolo;

    private WebMarkupContainer containerDadosGerais;
    private WebMarkupContainer containerEstabelecimento;
    private WebMarkupContainer containerPessoa;
    private AbstractAjaxLink btnCadastrarPessoa;
    private WebMarkupContainer containerEndereco;
    private DropDown ddTipoPessoa;
    private AutoCompleteConsultaEstabelecimento autoCompleteEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteVigilanciaPessoa;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteEnderecoProprietario;
    private InputField txtEmailPessoa;
    private InputField txtEmailEstabelecimento;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;

    private WebMarkupContainer containerCAEProprietario;
    private InputField txtCadastroAtividadeEconomicaProprietario;

    private WebMarkupContainer containerTipoProjeto;
    private AutoCompleteConsultaTipoProjetoVigilancia autoCompleteTipoProjetoVigilancia;
    private DoubleField txtArea;
    private Button btnAdicionarTipoProjeto;
    private Table tblTipoProjeto;
    private CompoundPropertyModel<TipoProjetoRequerimentoVigilancia> modelTipoProjeto;

    private WebMarkupContainer containerProjetoHidrossanitarioAprovado;
    private AutoCompleteConsultaRequerimentoProjetoHidrossanitario autoCompleteProjetoHidrossanitario;
    private InputField txtNumeroProjetoHidrossanitario;

    private WebMarkupContainer containerDadosProjeto;
    private AutoCompleteConsultaVigilanciaEndereco autocompleteEnderecoObra;
    private RequiredInputField obraNumeroEndereco;
    private InputField obraQuadra;
    private InputField obraNumeroLado;
    private InputField obraLote;
    private InputField obraComplemento;
    private InputField obraNumeroLoteamento;


    private WebMarkupContainer containerAutorProjeto;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteAutorProjeto;
    private AbstractAjaxLink btnCadadastroAutorProjeto;
    private InputField<String> numeroCadastroAtividadeEconomicaAutor;
    private InputField<String> conselhoClasse;
    private InputField<String> numeroInscricaoRegistro;

    private DlgCadastroResponsavelTecnico dlgCadastroResponsavelTecnico;

    private WebMarkupContainer containerUsoEdificacao;
    private DropDown ddUsoEdificacao;
    private InputField txtObservacaoUsoEdificacao;

    private WebMarkupContainer containerInformacoes;
    private HiddenField classificacaoRisco;
    private String txtClassificacaoRisco;
    private DisabledInputField descricaoClassificacaoRisco;
    private Label lblnumeroProcessoProjetoArquitetonico;
    private InputField txtNumeroProcessoProjeto;
    private DropDown ddRegiaoCobertaRedeEsgoto;
    private DropDown ddRegiaoAbastecidaAgua;
    private DropDown ddUnifamiliar;
    private DropDown ddLincenciavelAmbiental;
    private DropDown ddSistemaAprovAguasPluviais;
    private WebMarkupContainer containerReclassificado;
    private DropDown ddReclassificadoBaixoRisco;

    private WebMarkupContainer containerTipoEnquadramentoProjeto;
    private AutoCompleteConsultaTipoEnquadramentoProjeto autoCompleteTipoEnquadramentoProjeto;
    private WebMarkupContainer containerNumPHSAprovado;
    private InputField txtNumeroPHSAprovado;
    private DlgCadastroTipoEnquadramentoProjeto dlgCadastroTipoEnquadramentoProjeto;

    private WebMarkupContainer containerParcelamentoSolo;
    private LongField txtNumeroLotesParcelamento;
    private InputField txtNumeroProjetoUrbanistico;
    private InputField txtNumeroLicitacaoAmbiental;
    private InputField txtNumeroProjetoEsgoto;
    private InputField txtNumeroProjetoAgua;

    private WebMarkupContainer containerMetragem;
    private DoubleField txtAreaComercial;
    private DoubleField txtAreaResidencial;
    private DoubleField txtAreaTotal;

    private PnlResponsavelTecnico pnlResponsavelTecnico;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComum;
    private RequerimentoVigilanciaSolicitantePanel pnlSolicitante;
    private PnlRequerimentoVigilanciaAnexo pnlAnexos;
    private RequerimentoVigilanciaFiscaisPanel pnlFiscais;
    private RequerimentoVigilanciaOcorrenciaPanel pnlOcorrencias;
    private PnlAnexoPrancha pnlAnexosPranchas;
    private PnlAnexoMemorial pnlAnexoMemorial;
    private PnlInscricaoImobiliaria pnlInscricaoImobiliaria;


    private WebMarkupContainer containerOrientacaoTecnica;
    private CheckBoxLongValue cbxTermoAceite;

    public RequerimentoHidrossanitarioBasePage(
            boolean modoEdicao,
            TipoSolicitacao tipoSolicitacao,
            Class pageReturn
    ) {
        this.modoEdicao = modoEdicao;
        this.tipoSolicitacao = tipoSolicitacao;
        this.pageReturn = pageReturn;
        this.requerimentoVigilancia = new RequerimentoVigilancia();
        this.requerimentoVigilancia.setTipoSolicitacao(tipoSolicitacao);
        this.baseDTO = new RequerimentoHidrossanitarioBaseDTO();
        this.baseDTO.setRequerimentoVigilancia(requerimentoVigilancia);
        this.baseDTO.setTipoSolicitacao(tipoSolicitacao);
        init();
    }

    public RequerimentoHidrossanitarioBasePage(
            RequerimentoVigilancia requerimentoVigilancia,
            boolean modoEdicao,
            Class pageReturn
    ) {
        this.modoEdicao = modoEdicao;
        this.pageReturn = pageReturn;
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();
        this.baseDTO = new RequerimentoHidrossanitarioBaseDTO();
        this.baseDTO.setRequerimentoVigilancia(requerimentoVigilancia);
        init();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    @Override
    public String getTituloPrograma() {
        return null;
    }

    @Override
    public void criarForm() {
    }

    @Override
    public void criarEntidadeRequerimento() {
    }

    @Override
    public void carregarLists() {
    }

    @Override
    public void criarComponentesRequerimento() {
    }

    @Override
    public void carregarRegrasComponentes() {
    }

    private void carregarRegrasComponentesBase() {
        btnSalvar.setEnabled(isOrientacaoTecnica() && isModoEdicao());
        if (TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value().equals(getTipoSolicitacao().getTipoDocumento())) {
            btnSalvar.setEnabled(isModoEdicao());
        }
    }

    @Override
    public void validarRequerimento(Object dto, AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    @Override
    public RequerimentoVigilancia salvarRequerimento(Object dto, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        return null;
    }

    @Override
    public Object getRequerimentoDTO() {
        return null;
    }


    private void init() {
        beforeInit();
        criarComponentesRequerimento();
        afterInit();
        getSession().getFeedbackMessages().clear();
    }

    private void beforeInit() {
        carregarConfiguracao();
        criarEntidadeRequerimento();
        criarForm();
        criarContainerOutros();
        criarBtnVoltarSalvar();
    }

    private void afterInit() {
        carregarLists();
        carregarRegrasComponentes();
        carregarRegrasComponentesBase();
        add(form);
    }

    /**
     * MÉTODOS PARA CRIAÇÃO DOS CONTAINERS
     */

    private void criarContainerOutros() {
        containerOutros = new WebMarkupContainer("containerOutros");
        containerOutros.setOutputMarkupId(true);
        containerOutros.setEnabled(isModoEdicao());

        form.add(containerOutros);
    }

    private void criarBtnVoltarSalvar() {
        btnVoltar = new SubmitButton("btnVoltar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                setResponsePage(getPageVoltar());
            }
        });
        btnVoltar.setDefaultFormProcessing(false);

        btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });
        btnSalvar.setEnabled(false);

        form.add(btnVoltar, btnSalvar);
    }

    protected void criarNumeroProtocolo(boolean hasEntity) {
        containerNumeroProtocolo = new WebMarkupContainer("containerNumeroProtocolo");
        containerNumeroProtocolo.setEnabled(false);
        containerNumeroProtocolo.setOutputMarkupId(true);
        containerNumeroProtocolo.setVisible(hasEntity);

        numeroProtocolo =
                new DisabledInputField<String>(
                        path(getProxy().getRequerimentoVigilancia().getProtocoloFormatado())
                );

        containerNumeroProtocolo.add(numeroProtocolo);

        getContainerOutros().add(containerNumeroProtocolo);
    }

    protected void criarDadosProprietario(boolean hasEntity) {
        this.criarDadosProprietario(hasEntity, false);
    }

    protected void criarDadosProprietario(boolean hasEntity, boolean criarCAE) {
        containerDadosGerais = new WebMarkupContainer("containerDadosGerais");
        containerDadosGerais.setEnabled(!hasEntity);
        containerDadosGerais.setOutputMarkupId(true);

        containerDadosGerais.add(
                criarDropDownTipoPessoa(),
                criarContainerPessoa(),
                criarContainerEstabelecimento(),
                criarDadosEndereco()
        );

        if (criarCAE) {
            containerDadosGerais.add(criarNumeroCadastroAtividadeEconomicaProprietario());
        }

        getContainerOutros().add(containerDadosGerais);
    }

    private DropDown criarDropDownTipoPessoa() {
        ddTipoPessoa = DropDownUtil.getIEnumDropDown(
                path(getProxy().getRequerimentoVigilancia().getTipoRequerente()),
                RequerimentoVigilancia.TipoRequerente.values(), true, true);
        ddTipoPessoa.setLabel(new Model(bundle("tipoPessoa")));
        ddTipoPessoa.addAjaxUpdateValue();
        ddTipoPessoa.setEnabled(isModoEdicao());
        ddTipoPessoa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCamposDadosProprietario(target, true);
                limparCamposHidroManual(target, true);
                updateNotificationPanel(target);
            }
        });

        return ddTipoPessoa;
    }

    private WebMarkupContainer criarNumeroCadastroAtividadeEconomicaProprietario() {
        containerCAEProprietario = new WebMarkupContainer("containerCAEProprietario");
        containerCAEProprietario.setOutputMarkupPlaceholderTag(true);

        txtCadastroAtividadeEconomicaProprietario = new InputField(path(getProxy().getNumeroCadastroAtividadeEconomicaProprietario()));
        txtCadastroAtividadeEconomicaProprietario.setEnabled(true);
        txtCadastroAtividadeEconomicaProprietario.setLabel(new Model(bundle("numeroCadastroAtividadeEconomica")));

        containerCAEProprietario.add(txtCadastroAtividadeEconomicaProprietario);

        return containerCAEProprietario;
    }

    private WebMarkupContainer criarContainerPessoa() {
        containerPessoa = new WebMarkupContainer("containerPessoa");
        containerPessoa.setOutputMarkupPlaceholderTag(true);
        containerPessoa.setVisible(false);

        autoCompleteVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(getProxy().getRequerimentoVigilancia().getVigilanciaPessoa()), true);
        autoCompleteVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        autoCompleteVigilanciaPessoa.setLabel(new Model(bundle("pessoa")));
        autoCompleteVigilanciaPessoa.setEnabled(isModoEdicao());
        autoCompleteVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(
                    AjaxRequestTarget target,
                    VigilanciaPessoa object
            ) {
                atualizarEnderecoPessoa(target, object);
                habilitarCampoProjetoHidroVigPessoa(target, object);
                limparCamposHidroManual(target, true);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(
                    AjaxRequestTarget target,
                    VigilanciaPessoa object
            ) {
                atualizarEnderecoPessoa(target, null);
                habilitarCampoProjetoHidroVigPessoa(target, object);
                limparCamposHidroManual(target, true);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        txtEmailPessoa = new InputField(path(getProxy().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail()));
        txtEmailPessoa.setEnabled(false);
        txtEmailPessoa.setLabel(new Model(bundle("email")));


        dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId()) {
            @Override
            public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                autoCompleteVigilanciaPessoa.limpar(target);
                autoCompleteVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);
                autoCompleteEnderecoProprietario.limpar(target);
                txtEmailPessoa.limpar(target);

                VigilanciaPessoa vp = VigilanciaHelper.carregarEmailPessoaById(vigilanciaPessoa.getCodigo());
                txtEmailPessoa.setComponentValue(vp.getEmail());

                target.add(txtEmailPessoa, autoCompleteEnderecoProprietario);
            }
        };

        btnCadastrarPessoa = new AbstractAjaxLink("btnCadastrarPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroVigilanciaPessoa);
                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        };

        containerPessoa.add(autoCompleteVigilanciaPessoa, txtEmailPessoa, btnCadastrarPessoa);

        return containerPessoa;
    }

    private void atualizarEnderecoPessoa(
            AjaxRequestTarget target,
            VigilanciaPessoa vigilanciaPessoa
    ) {
        autoCompleteEnderecoProprietario.limpar(target);
        txtEmailPessoa.limpar(target);

        if (vigilanciaPessoa != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);
            autoCompleteEnderecoProprietario.setComponentValue(target, ve);

            VigilanciaPessoa vp = VigilanciaHelper.carregarEmailPessoaById(vigilanciaPessoa.getCodigo());

            if (vp.getEmail() == null) {
                txtEmailPessoa.setEnabled(true);
                txtEmailPessoa.setRequired(true);
                txtEmailPessoa.addRequiredClass();
            } else {
                txtEmailPessoa.setRequired(false);
                txtEmailPessoa.setEnabled(false);
                txtEmailPessoa.removeRequiredClass();
            }
            txtEmailPessoa.setComponentValue(vp.getEmail());
            target.add(txtEmailPessoa);
        }
    }

    private WebMarkupContainer criarContainerEstabelecimento() {
        containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
        containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.setVisible(false);

        autoCompleteEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(getProxy().getRequerimentoVigilancia().getEstabelecimento()), true);
        autoCompleteEstabelecimento.setOutputMarkupPlaceholderTag(true);
        autoCompleteEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteEstabelecimento.setEnabled(isModoEdicao());
        autoCompleteEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(
                    AjaxRequestTarget target,
                    Estabelecimento object
            ) {
                atualizarEnderecoEstabelecimento(target, object);
                verificarEstabelecimentoIsento(target, object);
                habilitarCampoProjetoHidroEstabelecimento(target, object);
                limparCamposHidroManual(target, true);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(
                    AjaxRequestTarget target,
                    Estabelecimento object
            ) {
                atualizarEnderecoEstabelecimento(target, null);
                habilitarCampoProjetoHidroEstabelecimento(target, object);
                limparCamposHidroManual(target, true);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        txtEmailEstabelecimento = new InputField(path(getProxy().getRequerimentoVigilancia().getEstabelecimento().getEmail()));
        txtEmailEstabelecimento.setEnabled(false);
        txtEmailEstabelecimento.setLabel(new Model(bundle("email")));

        containerEstabelecimento.add(autoCompleteEstabelecimento, txtEmailEstabelecimento);

        return containerEstabelecimento;
    }

    private WebMarkupContainer criarDadosEndereco() {
        containerEndereco = new WebMarkupContainer("containerEndereco");
        containerEndereco.setOutputMarkupId(true);

        autoCompleteEnderecoProprietario = new AutoCompleteConsultaVigilanciaEndereco(path(getProxy().getRequerimentoVigilancia().getVigilanciaEndereco()), true);
        autoCompleteEnderecoProprietario.setLabel(new Model(bundle("endereco")));
        autoCompleteEnderecoProprietario.setEnabled(false);

        containerEndereco.add(autoCompleteEnderecoProprietario);

        return containerEndereco;
    }

    private void verificarEstabelecimentoIsento(
            AjaxRequestTarget target,
            Estabelecimento object
    ) {
        if (Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getTipoEmpresa()) && VigilanciaHelper.isIsentoPorLei(object)) {
            modalWarn(target, new ValidacaoException(bundle("msgEmpresaIsentaTaxasPorLei")));
        }
    }

    private void atualizarEnderecoEstabelecimento(
            AjaxRequestTarget target,
            Estabelecimento estabelecimento
    ) {
        autoCompleteEnderecoProprietario.limpar(target);

        txtEmailEstabelecimento.limpar(target);
        txtEmailEstabelecimento.setRequired(false);
        txtEmailEstabelecimento.setEnabled(false);
        txtEmailEstabelecimento.removeRequiredClass();

        if (estabelecimento != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
            autoCompleteEnderecoProprietario.setComponentValue(target, ve);

            Estabelecimento e = VigilanciaHelper.carregarEmailEstabelecimentoById(estabelecimento.getCodigo());
            if (e.getEmail() == null) {
                txtEmailEstabelecimento.setEnabled(true);
                txtEmailEstabelecimento.setRequired(true);
                txtEmailEstabelecimento.addRequiredClass();
            }
            txtEmailEstabelecimento.setComponentValue(e.getEmail());
            target.add(txtEmailEstabelecimento);
        }
    }

    protected void enableCamposDadosProprietario(
            AjaxRequestTarget target,
            boolean limparCampos
    ) {

        if (RequerimentoVigilancia.TipoRequerente.isEstabelecimento((Long) ddTipoPessoa.getComponentValue())) {
            containerEstabelecimento.setVisible(true);
            containerPessoa.setVisible(false);
        } else {
            containerEstabelecimento.setVisible(false);
            containerPessoa.setVisible(true);
        }

        if (target != null) {
            if (limparCampos) {
                autoCompleteEstabelecimento.limpar(target);
                autoCompleteVigilanciaPessoa.limpar(target);
                autoCompleteEnderecoProprietario.limpar(target);
            }
            target.add(containerDadosGerais);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    protected void criarTipoProjeto(boolean hasEntity, TipoProjetoVigilancia.Tipo tipo) {
        TipoProjetoRequerimentoVigilancia proxyTipoProjeto = on(TipoProjetoRequerimentoVigilancia.class);
        modelTipoProjeto = new CompoundPropertyModel<>(new TipoProjetoRequerimentoVigilancia());

        containerTipoProjeto = new WebMarkupContainer("containerTipoProjeto", modelTipoProjeto);
        containerTipoProjeto.setOutputMarkupId(true);
        containerTipoProjeto.setEnabled(isModoEdicao());

        autoCompleteTipoProjetoVigilancia = new AutoCompleteConsultaTipoProjetoVigilancia(path(proxyTipoProjeto.getTipoProjetoVigilancia()));
        autoCompleteTipoProjetoVigilancia.setOutputMarkupPlaceholderTag(true);
        autoCompleteTipoProjetoVigilancia.addAjaxUpdateValue();
        autoCompleteTipoProjetoVigilancia.setLabel(Model.of(bundle("tipoProjeto")));
        autoCompleteTipoProjetoVigilancia.setTipo(tipo.value());
        autoCompleteTipoProjetoVigilancia.setEnabled(isModoEdicao());

        txtArea = new DoubleField(path(proxyTipoProjeto.getArea()));
        txtArea.setMDec(4).addAjaxUpdateValue();
        txtArea.setVMax(999999.9999);
        txtArea.setEnabled(isModoEdicao());
        txtArea.setLabel(Model.of(bundle("areaM2")));

        btnAdicionarTipoProjeto = new AbstractAjaxButton("btnAdicionarTipoProjeto") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarTipoProjeto(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        };
        btnAdicionarTipoProjeto.setDefaultFormProcessing(false);
        btnAdicionarTipoProjeto.setEnabled(isModoEdicao());

        tblTipoProjeto = new Table("tblTipoProjeto", getColumnsTipoProjeto(), getCollectionProviderTipoProjeto());
        tblTipoProjeto.populate();
        tblTipoProjeto.setScrollY("1800");
        tblTipoProjeto.setEnabled(isModoEdicao());

        containerTipoProjeto.add(autoCompleteTipoProjetoVigilancia, txtArea, btnAdicionarTipoProjeto, tblTipoProjeto);

        if (hasEntity && isModoEdicao()) {
            containerTipoProjeto.setEnabled(
                    hasEntity && isModoEdicao() ||
                            CollectionUtils.isEmpty(VigilanciaHelper.getVigilanciaFinanceiroList(getRequerimentoVigilancia()))
            );
        }

        getContainerOutros().add(containerTipoProjeto);
    }

    private void adicionarTipoProjeto(AjaxRequestTarget target) throws ValidacaoException {
        TipoProjetoRequerimentoVigilancia tprv = modelTipoProjeto.getObject();

        if (tprv.getTipoProjetoVigilancia() == null) {
            throw new ValidacaoException(bundle("informeTipoProjeto"));
        }

        if (tprv.getArea() == null) {
            throw new ValidacaoException(bundle("informeArea"));
        }

        if (Coalesce.asDouble(tprv.getArea()) > tprv.getTipoProjetoVigilancia().getMetragemMaxima()) {
            if (Coalesce.asDouble(configuracaoVigilancia.getValorExcedidoAnaliseProjeto()) == 0D) {
                throw new ValidacaoException(bundle("msgAreaInformadaExcedeMetragemMaximaTipoProjetoX", tprv.getTipoProjetoVigilancia().getMetragemMaxima()));
            }
        }

        getBaseDTO().getListTipoProjetos().add(tprv);

        limparCamposTipoProjeto(target);
        tipoProjetoRequired(target);
        calcularAreaTotalConstrucao(target);
        tblTipoProjeto.populate(target);
    }

    private List<IColumn> getColumnsTipoProjeto() {
        List<IColumn> columns = new ArrayList<IColumn>();
        TipoProjetoRequerimentoVigilancia proxy = on(TipoProjetoRequerimentoVigilancia.class);

        columns.add(getActionColumnTipoProjeto());
        columns.add(createColumn(bundle("tipo"), proxy.getTipoProjetoVigilancia().getDescricao()));
        columns.add(createColumn(bundle("area"), proxy.getArea()));

        return columns;
    }

    private IColumn getActionColumnTipoProjeto() {
        return new MultipleActionCustomColumn<TipoProjetoRequerimentoVigilancia>() {
            @Override
            public void customizeColumn(TipoProjetoRequerimentoVigilancia rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoProjetoRequerimentoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target,TipoProjetoRequerimentoVigilancia modelObject) throws ValidacaoException, DAOException {
                        removerTipoProjeto(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerTipoProjeto(AjaxRequestTarget target, TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia) {
        for (int i = 0; i < getBaseDTO().getListTipoProjetos().size(); i++) {
            TipoProjetoRequerimentoVigilancia busca = getBaseDTO().getListTipoProjetos().get(i);
            if (busca == tipoProjetoRequerimentoVigilancia) {
                if (busca.getCodigo() != null) {
                    getBaseDTO().getListTiposProjetosExcluidos().add(busca);
                }
                getBaseDTO().getListTipoProjetos().remove(i);
            }
        }

        limparCamposTipoProjeto(target);
        tipoProjetoRequired(target);
        calcularAreaTotalConstrucao(target);
        tblTipoProjeto.populate(target);
    }

    private void calcularAreaTotalConstrucao(AjaxRequestTarget target) {
        if (txtAreaTotal != null) {
            Double totalArea = 0D;

            if (CollectionUtils.isNotNullEmpty(getBaseDTO().getListTipoProjetos())) {
                for (TipoProjetoRequerimentoVigilancia tprv : getBaseDTO().getListTipoProjetos()) {
                    totalArea = new Dinheiro(totalArea).somar(new Dinheiro(tprv.getArea())).doubleValue();
                }
            }
            getBaseDTO().setAreaTotalConstrucao(totalArea);
            txtAreaTotal.setComponentValue(totalArea);
            target.add(txtAreaTotal);
        }
    }

    private void limparCamposTipoProjeto(AjaxRequestTarget target) {
        modelTipoProjeto.setObject(new TipoProjetoRequerimentoVigilancia());
        autoCompleteTipoProjetoVigilancia.limpar(target);
        txtArea.limpar(target);
    }

    private void limparListaTipoProjeto(AjaxRequestTarget target) {
        getBaseDTO().getListTipoProjetos().clear();
        tblTipoProjeto.limpar(target);
        tblTipoProjeto.populate(target);
    }

    private ICollectionProvider getCollectionProviderTipoProjeto() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getBaseDTO().getListTipoProjetos();
            }
        };
    }

    protected void criarDadosProjeto() {
        containerDadosProjeto = new WebMarkupContainer("containerDadosProjeto");
        containerDadosProjeto.setOutputMarkupId(true);

        autocompleteEnderecoObra = new AutoCompleteConsultaVigilanciaEndereco(path(getProxy().getVigilanciaEndereco()));
        autocompleteEnderecoObra.setEnabled(isModoEdicao());
        autocompleteEnderecoObra.getTxtDescricao().addRequiredClass();
        autocompleteEnderecoObra.setRequired(true);
        autocompleteEnderecoObra.setLabel(new Model(bundle("endereco")));

        obraNumeroEndereco = new RequiredInputField(path(getProxy().getObraNumeroEndereco()));
        obraNumeroEndereco.setLabel(new Model(bundle("numeroAbv")));
        obraNumeroEndereco.setEnabled(isModoEdicao());

        obraQuadra = new InputField(path(getProxy().getObraQuadra()));
        obraQuadra.setLabel(new Model<>(bundle("quadra")));
        obraQuadra.setEnabled(isModoEdicao());

        obraNumeroLado = new InputField(path(getProxy().getObraNumeroLado()));
        obraNumeroLado.setEnabled(isModoEdicao());

        obraLote = new InputField(path(getProxy().getObraLote()));
        obraLote.setLabel(new Model<>(bundle("lote")));
        obraLote.setEnabled(isModoEdicao());

        obraComplemento = new InputField(path(getProxy().getObraComplemento()));
        obraComplemento.setEnabled(isModoEdicao());

        obraNumeroLoteamento = new InputField(path(getProxy().getObraNumeroLoteamento()));
        obraNumeroLoteamento.setEnabled(isModoEdicao());

        containerDadosProjeto.add(
                autocompleteEnderecoObra,
                obraNumeroEndereco,
                obraQuadra,
                obraNumeroLado,
                obraLote,
                obraComplemento,
                obraNumeroLoteamento
        );

        getContainerOutros().add(containerDadosProjeto);
    }

    protected void criarAutorProjeto() {
        containerAutorProjeto = new WebMarkupContainer("containerAutorProjeto");
        containerAutorProjeto.setOutputMarkupPlaceholderTag(true);

        autoCompleteAutorProjeto = new AutoCompleteConsultaResponsavelTecnico(path(getProxy().getAutorProjeto()), true);
        autoCompleteAutorProjeto.setEnabled(isModoEdicao());
        autoCompleteAutorProjeto.setOutputMarkupId(true);
        autoCompleteAutorProjeto.setLabel(new Model<>(Bundle.getStringApplication("autorProjeto")));

        btnCadadastroAutorProjeto = new AbstractAjaxLink("btnCadadastroAutorProjeto") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarAutorProjeto(target);
            }
        };
        btnCadadastroAutorProjeto.setEnabled(isModoEdicao());

        numeroCadastroAtividadeEconomicaAutor = new InputField(path(getProxy().getNumeroCadastroAtividadeEconomicaAutor()));
        numeroCadastroAtividadeEconomicaAutor.setLabel(new Model<>(Bundle.getStringApplication("numeroCadastroAtividadeEconomica")));
        numeroCadastroAtividadeEconomicaAutor.setEnabled(isModoEdicao());
        numeroCadastroAtividadeEconomicaAutor.setRequired(true);
        numeroCadastroAtividadeEconomicaAutor.addRequiredClass();

        conselhoClasse = new DisabledInputField<>(path(getProxy().getAutorProjeto().getOrgaoEmissor().getDescricao()));
        numeroInscricaoRegistro = new DisabledInputField<>(path(getProxy().getAutorProjeto().getNumeroRegistro()));


        containerAutorProjeto.add(
                autoCompleteAutorProjeto, btnCadadastroAutorProjeto, numeroCadastroAtividadeEconomicaAutor,
                conselhoClasse, numeroInscricaoRegistro
        );

        autoCompleteAutorProjeto.add(new ConsultaListener<ResponsavelTecnico>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ResponsavelTecnico object) {
                if (object.getOrgaoEmissor() != null && !StringUtils.isEmpty(object.getOrgaoEmissor().getDescricao())) {
                    conselhoClasse.setComponentValue(object.getOrgaoEmissor().getDescricao());
                }
                numeroInscricaoRegistro.setComponentValue(object.getNumeroRegistro());

                target.add(conselhoClasse, numeroInscricaoRegistro);
            }
        });

        autoCompleteAutorProjeto.add(new RemoveListener<ResponsavelTecnico>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ResponsavelTecnico object) {
                conselhoClasse.limpar(target);
                numeroInscricaoRegistro.limpar(target);
            }
        });

        getContainerOutros().add(containerAutorProjeto);

    }

    private void cadastrarAutorProjeto(AjaxRequestTarget target) {
        if (dlgCadastroResponsavelTecnico == null) {
            addModal(target, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(newModalId()));
            dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
                @Override
                public void onSalvar(
                        AjaxRequestTarget target,
                        ResponsavelTecnico responsavelTecnico
                ) throws ValidacaoException, DAOException {
                    autoCompleteAutorProjeto.limpar(target);
                    autoCompleteAutorProjeto.setComponentValue(responsavelTecnico);
                    target.add(autoCompleteAutorProjeto);
                }
            });
        }

        dlgCadastroResponsavelTecnico.limpar(target);
        dlgCadastroResponsavelTecnico.show(target);
    }

    protected void criarInscricaoImobiliaria() {
        RequerimentoInscricaoImobiliariaDTO dto = new RequerimentoInscricaoImobiliariaDTO();
        dto.setListInscricaoImobiliaria(getBaseDTO().getListInscricaoImobiliaria());
        dto.setListInscricaoImobiliariaExcluidos(getBaseDTO().getListInscricaoImobiliariaExcluidos());

        pnlInscricaoImobiliaria = new PnlInscricaoImobiliaria("pnlInscricaoImobiliaria", isModoEdicao(), dto);
        pnlInscricaoImobiliaria.setOutputMarkupPlaceholderTag(true);

        getContainerOutros().add(pnlInscricaoImobiliaria);
    }

    protected void criarResponsavelTecnico() {
        RequerimentoResponsavelTecnicoDTO dto = new RequerimentoResponsavelTecnicoDTO();
        dto.setListResponsavelTecnico(getBaseDTO().getListResponsavelTecnico());
        dto.setListResponsavelTecnicoExcluidos(getBaseDTO().getListResponsavelTecnicoExcluidos());

        pnlResponsavelTecnico = new PnlResponsavelTecnico("pnlResponsavelTecnico", isModoEdicao(), dto);
        pnlResponsavelTecnico.setOutputMarkupPlaceholderTag(true);

        getContainerOutros().add(pnlResponsavelTecnico);
    }

    protected void criarUsoEdificacao() {
        containerUsoEdificacao = new WebMarkupContainer("containerUsoEdificacao");
        containerUsoEdificacao.setOutputMarkupId(true);

        ddUsoEdificacao = DropDownUtil.getIEnumDropDown(
                path(getProxy().getUsoEdificacao()),
                RequerimentosProjetosEnums.UsoEdificacao.values(),
                true,
                true,
                false,
                true);
        ddUsoEdificacao.setEnabled(isModoEdicao());
        ddUsoEdificacao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarUsoEdificacao(target);
                habilitarParcelamentoSolo(target);
                habilitarMetragem(target);
                vigilanciaAltoBaixoRisco(target);
                target.appendJavaScript(JScript.initMasks());
            }
        });

        txtObservacaoUsoEdificacao = new InputField(path(getProxy().getObservacaoUsoEdificacao()));
        txtObservacaoUsoEdificacao.setLabel(Model.of(bundle("outros")));
        txtObservacaoUsoEdificacao.setEnabled(isModoEdicao());

        containerUsoEdificacao.add(ddUsoEdificacao, txtObservacaoUsoEdificacao);

        getContainerOutros().add(containerUsoEdificacao);
    }

    protected void habilitarMetragem(AjaxRequestTarget target) {
        if (target != null) {
            txtAreaComercial.limpar(target);
            txtAreaResidencial.limpar(target);
        }

        if (RequerimentosProjetosEnums.UsoEdificacao.MISTA.value().equals((Long) ddUsoEdificacao.getComponentValue())) {
            txtAreaComercial.setEnabled(true);
            txtAreaComercial.setRequired(true);
            txtAreaComercial.addRequiredClass();

            txtAreaResidencial.setEnabled(true);
            txtAreaResidencial.setRequired(true);
            txtAreaResidencial.addRequiredClass();
        } else {
            txtAreaComercial.setEnabled(false);
            txtAreaComercial.setRequired(false);
            txtAreaComercial.removeRequiredClass();

            txtAreaResidencial.setEnabled(false);
            txtAreaResidencial.setRequired(false);
            txtAreaResidencial.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtAreaComercial, txtAreaResidencial);
        }
    }

    protected void habilitarUsoEdificacao(AjaxRequestTarget target) {
        if (target != null) {
            txtObservacaoUsoEdificacao.limpar(target);
        }

        if (RequerimentosProjetosEnums.UsoEdificacao.OUTROS.value().equals((Long) ddUsoEdificacao.getComponentValue())) {
            txtObservacaoUsoEdificacao.setEnabled(true);
            txtObservacaoUsoEdificacao.setRequired(true);
            txtObservacaoUsoEdificacao.addRequiredClass();
        } else {
            txtObservacaoUsoEdificacao.setEnabled(false);
            txtObservacaoUsoEdificacao.setRequired(false);
            txtObservacaoUsoEdificacao.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtObservacaoUsoEdificacao);
        }
    }

    protected void criarInformacoes(boolean hasEntity) {
        containerInformacoes = new WebMarkupContainer("containerInformacoes");
        containerInformacoes.setOutputMarkupId(true);

        classificacaoRisco = new HiddenField(path(getProxy().getClassificacaoRisco()));
        descricaoClassificacaoRisco = new DisabledInputField("descricaoClassificacaoRisco", new PropertyModel<String>(this, "txtClassificacaoRisco"));

        lblnumeroProcessoProjetoArquitetonico = new Label("lblnumeroProcessoProjetoArquitetonico");
        lblnumeroProcessoProjetoArquitetonico.setDefaultModel(Model.of(bundle("numeroProcessoProjetoArquitetonicoJunto", configuracaoVigilancia.getSiglaOrgaoMunicipalProjetoArquitetonico())));

        txtNumeroProcessoProjeto = new RequiredInputField(path(getProxy().getNumeroProcessoProjeto()));
        txtNumeroProcessoProjeto.setEnabled(isModoEdicao());
        txtNumeroProcessoProjeto.addAjaxUpdateValue();
        txtNumeroProcessoProjeto.setLabel(Model.of(bundle("numeroProcessoProjetoArquitetonicoJunto", configuracaoVigilancia.getSiglaOrgaoMunicipalProjetoArquitetonico())));
        txtNumeroProcessoProjeto.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarParcelamentoSolo(target);
            }
        });

        ddRegiaoCobertaRedeEsgoto = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getRegiaoCobertaRedeEsgoto()), true, true);
        ddRegiaoCobertaRedeEsgoto.setLabel(new Model(bundle("regiaoCobertaPorRedeEsgoto")));
        ddRegiaoCobertaRedeEsgoto.setEnabled(isModoEdicao());
        ddRegiaoCobertaRedeEsgoto.setOutputMarkupPlaceholderTag(true);
        ddRegiaoCobertaRedeEsgoto.addAjaxUpdateValue();
        ddRegiaoCobertaRedeEsgoto.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarParcelamentoSolo(target);
                vigilanciaAltoBaixoRisco(target);
            }
        });


        ddRegiaoAbastecidaAgua = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getRegiaoAbastecidaAgua()), true, true);
        ddRegiaoAbastecidaAgua.setLabel(new Model(bundle("regiaoAbastecidaPorAguaPotavel")));
        ddRegiaoAbastecidaAgua.setEnabled(isModoEdicao());
        ddRegiaoAbastecidaAgua.setOutputMarkupPlaceholderTag(true);
        ddRegiaoAbastecidaAgua.addAjaxUpdateValue();
        ddRegiaoAbastecidaAgua.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                vigilanciaAltoBaixoRisco(target);
            }
        });


        ddUnifamiliar = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getEdificacaoExclusivamenteUnifamiliar()), true, true);
        ddUnifamiliar.setLabel(new Model(bundle("edificacaoExclusivamenteUnifamiliar")));
        ddUnifamiliar.setEnabled(isModoEdicao());
        ddUnifamiliar.setOutputMarkupPlaceholderTag(true);
        ddUnifamiliar.addAjaxUpdateValue();
        ddUnifamiliar.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                vigilanciaAltoBaixoRisco(target);
            }
        });

        ddSistemaAprovAguasPluviais = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getSistemaAguaPluvial()), true, false);
        ddSistemaAprovAguasPluviais.setEnabled(isModoEdicao());
        ddSistemaAprovAguasPluviais.setLabel(new Model(bundle("sistemaAproveitamentoAguasPluviais")));
        ddSistemaAprovAguasPluviais.setOutputMarkupPlaceholderTag(true);
        ddSistemaAprovAguasPluviais.addAjaxUpdateValue();


        ddLincenciavelAmbiental = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getProjetoLicenciavelOrgaoAmbiental()), true, true);
        ddLincenciavelAmbiental.setEnabled(isModoEdicao());
        ddLincenciavelAmbiental.setLabel(new Model(bundle("projetoLicenciavelOrgaoAmbiental")));
        ddLincenciavelAmbiental.setOutputMarkupPlaceholderTag(true);
        ddLincenciavelAmbiental.addAjaxUpdateValue();
        ddLincenciavelAmbiental.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                vigilanciaAltoBaixoRisco(target);
            }
        });

        containerReclassificado = new WebMarkupContainer("containerReclassificado");
        ddReclassificadoBaixoRisco = DropDownUtil.getNaoSimLongDropDown(path(getProxy().getProcessoReclassificadoBaixoRiscoDecreto()), true, hasEntity);
        ddReclassificadoBaixoRisco.setLabel(new Model(bundle("processoReclassificadoBaixoRiscoDecreto")));
        ddReclassificadoBaixoRisco.setOutputMarkupPlaceholderTag(true);
        ddReclassificadoBaixoRisco.addAjaxUpdateValue();
        ddReclassificadoBaixoRisco.setEnabled(isModoEdicao());
        ddReclassificadoBaixoRisco.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                vigilanciaAltoBaixoRisco(target);
            }
        });
        containerReclassificado.add(ddReclassificadoBaixoRisco);
        containerReclassificado.setEnabled(isModoEdicao());
        containerReclassificado.setVisible(hasEntity);

        containerInformacoes.add(
                classificacaoRisco, descricaoClassificacaoRisco, lblnumeroProcessoProjetoArquitetonico,
                txtNumeroProcessoProjeto, ddRegiaoCobertaRedeEsgoto, ddUnifamiliar,
                ddLincenciavelAmbiental, containerReclassificado, ddRegiaoAbastecidaAgua,
                ddSistemaAprovAguasPluviais
        );

        getContainerOutros().add(containerInformacoes);
    }

    protected void vigilanciaAltoBaixoRisco(AjaxRequestTarget target) {

        String styleSemRisco = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #F2F2F2";
        String styleRiscoBaixo = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #2AABD2";
        String styleRiscoAlto = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #E17373";


        if (isLongSim(ddReclassificadoBaixoRisco)) {
            descricaoClassificacaoRisco.add(new AttributeModifier("style", styleRiscoBaixo));
            getBaseDTO().setClassificacaoRisco(RequerimentosProjetosEnums.ClassificacaoRisco.BAIXO_RISCO.value());
            txtClassificacaoRisco = RequerimentosProjetosEnums.ClassificacaoRisco.getValue(getBaseDTO().getClassificacaoRisco()).descricao();

        } else {
            List<DropDown> dds = Arrays.asList(ddUsoEdificacao, ddRegiaoCobertaRedeEsgoto, ddRegiaoAbastecidaAgua, ddUnifamiliar, ddLincenciavelAmbiental);

            if (allInfoDropsContainValues(dds)) {
                if (isAltoRisco()) {
                    descricaoClassificacaoRisco.add(new AttributeModifier("style", styleRiscoAlto));
                    getBaseDTO().setClassificacaoRisco(RequerimentosProjetosEnums.ClassificacaoRisco.ALTO_RISCO.value());
                    txtClassificacaoRisco = RequerimentosProjetosEnums.ClassificacaoRisco.getValue(getBaseDTO().getClassificacaoRisco()).descricao();

                } else if (isBaixoRisco()) {
                    descricaoClassificacaoRisco.add(new AttributeModifier("style", styleRiscoBaixo));
                    getBaseDTO().setClassificacaoRisco(RequerimentosProjetosEnums.ClassificacaoRisco.BAIXO_RISCO.value());
                    txtClassificacaoRisco = RequerimentosProjetosEnums.ClassificacaoRisco.getValue(getBaseDTO().getClassificacaoRisco()).descricao();

                    if (target != null) {
                        MessageUtil.modalWarn(target, descricaoClassificacaoRisco, new ValidacaoException(bundle("msgClassificacaoProjetoBaixoRisco")));
                    }

                } else {
                    descricaoClassificacaoRisco.add(new AttributeModifier("style", styleSemRisco));
                    txtClassificacaoRisco = null;
                }

            } else {
                descricaoClassificacaoRisco.add(new AttributeModifier("style", styleSemRisco));
                txtClassificacaoRisco = null;
            }
        }

        if (target != null) {
            target.add(descricaoClassificacaoRisco);
        }
    }


    protected void habilitarParcelamentoSolo(AjaxRequestTarget target) {
        if (target != null) {
            txtNumeroLotesParcelamento.limpar(target);
            txtNumeroProjetoUrbanistico.limpar(target);
            txtNumeroLicitacaoAmbiental.limpar(target);
            txtNumeroProjetoEsgoto.limpar(target);
            txtNumeroProjetoAgua.limpar(target);
        }

        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio((Long) ddUsoEdificacao.getComponentValue())) {
            containerParcelamentoSolo.setVisible(true);
            txtNumeroLotesParcelamento.setRequired(true);
            txtNumeroLotesParcelamento.addRequiredClass();

            txtNumeroProjetoUrbanistico.setRequired(true);
            txtNumeroProjetoUrbanistico.addRequiredClass();

            if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamento((Long) ddUsoEdificacao.getComponentValue()) && isLongSim(ddRegiaoCobertaRedeEsgoto)) {
                txtNumeroProjetoEsgoto.setRequired(true);
                txtNumeroProjetoEsgoto.addRequiredClass();
            } else {
                txtNumeroProjetoEsgoto.setRequired(false);
                txtNumeroProjetoEsgoto.removeRequiredClass();
            }

            txtNumeroProjetoAgua.setRequired(true);
            txtNumeroProjetoAgua.addRequiredClass();

            if (txtNumeroProcessoProjeto.getComponentValue() != null && isLongNao(ddRegiaoCobertaRedeEsgoto)) {
                txtNumeroProjetoUrbanistico.setComponentValue(StringUtil.getDigits((String) txtNumeroProcessoProjeto.getComponentValue()));
            } else {
                txtNumeroProjetoUrbanistico.setComponentValue(null);
            }
        } else {
            containerParcelamentoSolo.setVisible(false);
            txtNumeroLotesParcelamento.setRequired(false);
            txtNumeroLotesParcelamento.removeRequiredClass();

            txtNumeroProjetoUrbanistico.setRequired(false);
            txtNumeroProjetoUrbanistico.removeRequiredClass();

            txtNumeroProjetoEsgoto.setRequired(false);
            txtNumeroProjetoEsgoto.removeRequiredClass();

            txtNumeroProjetoAgua.setRequired(false);
            txtNumeroProjetoAgua.removeRequiredClass();
        }

        if (target != null) {
            target.add(
                    txtNumeroLotesParcelamento, containerParcelamentoSolo, txtNumeroProjetoUrbanistico,
                    txtNumeroLicitacaoAmbiental, txtNumeroProjetoEsgoto, txtNumeroProjetoAgua
            );
        }
    }

    public boolean isAltoRisco() {

        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio((Long) ddUsoEdificacao.getComponentValue())) {
            return (
                    (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua))
            );
        } else {
            return (
                    (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental)) ||
                            (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
                            (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental))
            );
        }
    }

    public boolean isBaixoRisco() {

        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio((Long) ddUsoEdificacao.getComponentValue())) {
            return isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua);
        } else {
            return (
                    (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
                            (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
                            (isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar)) ||
                            (isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental))
            );
        }

    }

    protected void criarTipoEnquadramentoProjeto() {
        containerTipoEnquadramentoProjeto = new WebMarkupContainer("containerTipoEnquadramentoProjeto");
        containerTipoEnquadramentoProjeto.setOutputMarkupId(true);
        containerTipoEnquadramentoProjeto.setEnabled(isModoEdicao());

        autoCompleteTipoEnquadramentoProjeto = new AutoCompleteConsultaTipoEnquadramentoProjeto(path(getProxy().getTipoEnquadramentoProjeto()), true);
        autoCompleteTipoEnquadramentoProjeto.setLabel(Model.of(bundle("tipoEnquadramentoProjeto")));
        autoCompleteTipoEnquadramentoProjeto.add(new ConsultaListener<TipoEnquadramentoProjeto>() {
            @Override
            public void valueObjectLoaded(
                    AjaxRequestTarget target,
                    TipoEnquadramentoProjeto tipoEnquadramentoProjeto
            ) {
                if (tipoEnquadramentoProjeto.getDescricao().equalsIgnoreCase("Substituição de Projeto")) {
                    containerNumPHSAprovado.setVisible(true);
                    txtNumeroPHSAprovado.limpar(target);
                    target.add(containerNumPHSAprovado, txtNumeroPHSAprovado);
                }
            }
        });

        autoCompleteTipoEnquadramentoProjeto.add(new RemoveListener<TipoEnquadramentoProjeto>() {
            @Override
            public void valueObjectUnLoaded(
                    AjaxRequestTarget target,
                    TipoEnquadramentoProjeto tipoEnquadramentoProjeto
            ) {
                containerNumPHSAprovado.setVisible(false);
                txtNumeroPHSAprovado.limpar(target);
                target.add(containerNumPHSAprovado, txtNumeroPHSAprovado);
            }
        });

        containerNumPHSAprovado = new WebMarkupContainer("containerNumPHSAprovado");
        containerNumPHSAprovado.setOutputMarkupId(true);
        containerNumPHSAprovado.setOutputMarkupPlaceholderTag(true);
        containerNumPHSAprovado.getAjaxRegionMarkupId();

        txtNumeroPHSAprovado = new InputField(path(getProxy().getNumeroProjetoHSAprovado()));
        containerNumPHSAprovado.add(txtNumeroPHSAprovado);

        containerTipoEnquadramentoProjeto.add(autoCompleteTipoEnquadramentoProjeto, containerNumPHSAprovado);
        dlgCadastroTipoEnquadramentoProjeto = new DlgCadastroTipoEnquadramentoProjeto(newModalId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.add(autoCompleteTipoEnquadramentoProjeto);
                autoCompleteTipoEnquadramentoProjeto.focus(target);
            }

            @Override
            public void onExcluir(
                    AjaxRequestTarget target,
                    TipoEnquadramentoProjeto tipoEnquadramentoProjeto
            ) throws ValidacaoException, DAOException {
                if (getBaseDTO().getTipoEnquadramentoProjeto() != null && getBaseDTO().getTipoEnquadramentoProjeto().equals(tipoEnquadramentoProjeto)) {
                    autoCompleteTipoEnquadramentoProjeto.limpar(target);
                }
            }
        };
        containerTipoEnquadramentoProjeto.add(new AbstractAjaxLink("btnTipoEnquadramentoProjeto") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroTipoEnquadramentoProjeto);
                dlgCadastroTipoEnquadramentoProjeto.show(target);
            }
        });

        getContainerOutros().add(containerTipoEnquadramentoProjeto);
    }

    protected void habilitarNumeroPHSAprovado() {
        if (getBaseDTO() == null ||
                Coalesce.asString(getBaseDTO().getNumeroProcessoProjeto()).isEmpty()) {
            containerNumPHSAprovado.setVisible(false);
        } else {
            containerNumPHSAprovado.setVisible(true);
        }
    }

    protected void criarParcelamentoDoSolo() {
        containerParcelamentoSolo = new WebMarkupContainer("containerParcelamentoSolo");
        containerParcelamentoSolo.setOutputMarkupId(true);
        containerParcelamentoSolo.setOutputMarkupPlaceholderTag(true);
        containerParcelamentoSolo.getAjaxRegionMarkupId();
        containerParcelamentoSolo.setEnabled(isModoEdicao());

        txtNumeroLotesParcelamento = new LongField(path(getProxy().getParcelamentoSoloNumeroLotes()));
        txtNumeroLotesParcelamento.setEnabled(isModoEdicao());
        txtNumeroLotesParcelamento.setVMax(99999L);
        txtNumeroLotesParcelamento.setLabel(Model.of(bundle("nrLotes")));

        txtNumeroProjetoUrbanistico = new InputField(path(getProxy().getNumeroProjetoUrbanistico()));
        txtNumeroProjetoUrbanistico.setEnabled(isModoEdicao());
        txtNumeroProjetoUrbanistico.setLabel(new Model(bundle("numeroProjetoUrbanismo")));

        txtNumeroLicitacaoAmbiental = new InputField(path(getProxy().getNumeroLicitacaoAmbiental()));
        txtNumeroLicitacaoAmbiental.setEnabled(isModoEdicao());
        txtNumeroLicitacaoAmbiental.setLabel(new Model(bundle("numeroLicencaAmbiental")));

        txtNumeroProjetoEsgoto = new InputField(path(getProxy().getNumeroProjetoEsgoto()));
        txtNumeroProjetoEsgoto.setEnabled(isModoEdicao());
        txtNumeroProjetoEsgoto.setLabel(new Model(bundle("numeroProjetoEsgoto")));

        txtNumeroProjetoAgua = new InputField(path(getProxy().getNumeroProjetoAgua()));
        txtNumeroProjetoAgua.setEnabled(isModoEdicao());
        txtNumeroProjetoAgua.setLabel(new Model(bundle("numeroProjetoAgua")));

        containerParcelamentoSolo.add(
                txtNumeroLotesParcelamento, txtNumeroProjetoUrbanistico, txtNumeroLicitacaoAmbiental,
                txtNumeroProjetoEsgoto, txtNumeroProjetoAgua
        );

        getContainerOutros().add(containerParcelamentoSolo);
    }

    protected void criarMetragem() {

        containerMetragem = new WebMarkupContainer("containerMetragem");
        containerMetragem.setOutputMarkupId(true);

        txtAreaComercial = new RequiredDoubleField(path(getProxy().getAreaComercial()));
        txtAreaComercial.setMDec(4).addAjaxUpdateValue();
        txtAreaComercial.setVMax(999999.9999);
        txtAreaComercial.setEnabled(isModoEdicao());
        txtAreaComercial.setLabel(Model.of(bundle("areaComercial")));

        txtAreaComercial.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarAreaTotalConstrucao(target);
            }
        });

        txtAreaResidencial = new RequiredDoubleField(path(getProxy().getAreaResidencial()));
        txtAreaResidencial.setMDec(4).addAjaxUpdateValue();
        txtAreaResidencial.setVMax(999999.9999);
        txtAreaResidencial.setEnabled(isModoEdicao());
        txtAreaResidencial.setLabel(Model.of(bundle("areaResidencial")));

        txtAreaResidencial.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarAreaTotalConstrucao(target);
            }
        });

        txtAreaTotal = new DoubleField(path(getProxy().getAreaTotalConstrucao()));
        txtAreaTotal.setMDec(4).addAjaxUpdateValue();
        txtAreaTotal.setVMax(999999.9999);
        txtAreaTotal.setEnabled(false);
        txtAreaTotal.setLabel(Model.of(bundle("areaTotalConstrucao")));

        containerMetragem.add(txtAreaComercial, txtAreaResidencial, txtAreaTotal);

        getContainerOutros().add(containerMetragem);
    }

    protected void validarAreaTotalConstrucao(AjaxRequestTarget target) {
        double areaSomada;

        if (RequerimentosProjetosEnums.UsoEdificacao.isMista((Long) ddUsoEdificacao.getComponentValue()) &&
                getBaseDTO().getAreaComercial() != null &&
                getBaseDTO().getAreaResidencial() != null) {

            areaSomada =
                    new Dinheiro(
                            Coalesce.asDouble(getBaseDTO().getAreaComercial()))
                            .somar(Coalesce.asDouble(getBaseDTO().getAreaResidencial()))
                            .doubleValue();

            if (getBaseDTO().getAreaTotalConstrucao().compareTo(areaSomada) != 0) {
                modalWarn(target, new ValidacaoException(bundle("msgValorSomadoAreaComercialAreaResidencialDiferenteDoTotalCalculado")));
            }
        }
    }

    protected void criarPanelDadosComuns(String lblFiscais) {
        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam =
                VigilanciaPageHelper.createDadosComumRequerimentoVigilanciaDTOParam(getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(!isEdicao());

        pnlDadosComum = new PnlDadosComumRequerimentoVigilancia("pnlDadosComum", dadosComumParam, isModoEdicao(), lblFiscais);
        getContainerOutros().add(pnlDadosComum);
    }

    protected void adicionarSetorAnaliseProjetos() {
        try {
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(bundle("msg_nao_existe_configuracao"));
            } else {
                if (configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos() != null) {
                    if (CollectionUtils.isEmpty(pnlDadosComum.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList())) {
                        EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                        elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos());
                        pnlDadosComum.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
                    }
                }
            }
        } catch (ValidacaoException ex) {
            warn(ex);
        }
    }

    protected void criarPanelDadosSolicitante() {
        pnlSolicitante = new RequerimentoVigilanciaSolicitantePanel("pnlSolicitante", getRequerimentoVigilancia(), isModoEdicao());
        getContainerOutros().add(pnlSolicitante);
    }

    protected void criarPanelAnexos() {
        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(getBaseDTO().getListAnexos());
        dtoPnlAnexo.setTipoSolicitacao(getTipoSolicitacao());
        pnlAnexos =
                new PnlRequerimentoVigilanciaAnexo(
                        "pnlAnexos",
                        dtoPnlAnexo,
                        isModoEdicao(),
                        false,
                        false,
                        false,
                        bundle("anexosInserirDocumentosProcesso")
                );
        pnlAnexos.setOutputMarkupId(true);

        getContainerOutros().add(pnlAnexos);
    }

    protected void criarPanelAnexoPranchas(GerenciadorArquivo.OrigemArquivo origemArquivo, String labelTitulo) {
        AnexoPranchaDTO dto = new AnexoPranchaDTO();
        dto.setOrigemArquivo(origemArquivo);
        dto.setListAnexosPrancha(getBaseDTO().getListAnexosPrancha());
        dto.setListAnexosPranchaExcluidos(getBaseDTO().getListAnexosPranchaExcluidos());

        pnlAnexosPranchas =
                new PnlAnexoPrancha(
                        "pnlAnexoPrancha",
                        dto,
                        isModoEdicao(),
                        requerimentoVigilancia,
                        getTipoSolicitacao(),
                        origemArquivo,
                        labelTitulo
                );
        pnlAnexosPranchas.setOutputMarkupId(true);

        getContainerOutros().add(pnlAnexosPranchas);
    }

    protected void criarPanelAnexoMemorial(GerenciadorArquivo.OrigemArquivo origemArquivo) {
        AnexoPranchaDTO dto = new AnexoPranchaDTO();
        dto.setOrigemArquivo(origemArquivo);
        dto.setListAnexosMemorial(getBaseDTO().getListAnexosMemorial());
        dto.setListAnexosMemorialExcluidos(getBaseDTO().getListAnexosMemorialExcluidos());

        pnlAnexoMemorial =
                new PnlAnexoMemorial(
                        "pnlAnexoMemorial",
                        dto,
                        isModoEdicao(),
                        requerimentoVigilancia,
                        getTipoSolicitacao(),
                        origemArquivo
                );
        pnlAnexoMemorial.setOutputMarkupId(true);

        getContainerOutros().add(pnlAnexoMemorial);
    }

    protected void criarPanelFiscais() {
        pnlFiscais = new RequerimentoVigilanciaFiscaisPanel("pnlFiscais", getRequerimentoVigilancia());
        pnlFiscais.setVisible(isEdicao());
        getContainerOutros().add(pnlFiscais);
    }
    protected void criarPanelOcorrencias() {
        pnlOcorrencias = new RequerimentoVigilanciaOcorrenciaPanel("pnlOcorrencias", getRequerimentoVigilancia().getCodigo(), false);
        pnlOcorrencias.setVisible(isEdicao());

        getContainerOutros().add(pnlOcorrencias);
    }

    protected void criarCheckboxOrientacaoTecnica(String mensagem) {
        containerOrientacaoTecnica = new WebMarkupContainer("containerOrientacaoTecnica");
        containerOrientacaoTecnica.setOutputMarkupId(true);
        containerOrientacaoTecnica.setEnabled(isModoEdicao());

        cbxTermoAceite = new CheckBoxLongValue(
                path(getProxy().getTermoAceite()),
                RepositoryComponentDefault.SIM_LONG
        );
        cbxTermoAceite.setEnabled(isModoEdicao());

        cbxTermoAceite.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableDisableBtnSalvar(target);
            }
        });

        containerOrientacaoTecnica.add(
                cbxTermoAceite,
                new Label("mensagem", new CompoundPropertyModel(mensagem))
        );

        getContainerOutros().add(containerOrientacaoTecnica);
    }

    private void enableDisableBtnSalvar(AjaxRequestTarget target) {
        if (target != null) {
            btnSalvar.setEnabled(isOrientacaoTecnica() && isModoEdicao());
            target.add(btnSalvar);
        }
    }

    private boolean isOrientacaoTecnica() {
        return cbxTermoAceite != null &&
                cbxTermoAceite.getComponentValue() != null &&
                RepositoryComponentDefault.SIM_LONG.equals(cbxTermoAceite.getComponentValue());
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validarRequerimento(getRequerimentoDTO(), target);
        RequerimentoVigilancia rv = salvarRequerimento(getRequerimentoDTO(), target);
        setRequerimentoVigilancia(rv);
        criarDialogImpressao(target, rv);
    }

    protected void redirectAfterSave(String protocolo) {
        Page page = getPageVoltar();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso_protocolo_x", protocolo));
        setResponsePage(page);
    }

    protected void criarNumeroProjetoAprovado() {
        containerProjetoHidrossanitarioAprovado = new WebMarkupContainer("containerProjetoHidrossanitarioAprovado");
        containerProjetoHidrossanitarioAprovado.setOutputMarkupId(true);

        autoCompleteProjetoHidrossanitario = new AutoCompleteConsultaRequerimentoProjetoHidrossanitario(path(getProxy().getRequerimentoProjetoHidrossanitario()));
        autoCompleteProjetoHidrossanitario.addAjaxUpdateValue();
        autoCompleteProjetoHidrossanitario.setSituacao(RequerimentoVigilancia.Situacao.DEFERIDO.value());
        autoCompleteProjetoHidrossanitario.setEnabled(isModoEdicao());
        autoCompleteProjetoHidrossanitario.setLabel(Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));

        autoCompleteProjetoHidrossanitario.add(
                new ConsultaListener<RequerimentoProjetoHidrossanitario>() {
                    @Override
                    public void valueObjectLoaded(
                            AjaxRequestTarget target,
                            RequerimentoProjetoHidrossanitario object
                    ) {
                        habilitarCampoProjetoHidroManual(target, object);
                    }
                });
        autoCompleteProjetoHidrossanitario.add(
                new RemoveListener<RequerimentoProjetoHidrossanitario>() {
                    @Override
                    public void valueObjectUnLoaded(
                            AjaxRequestTarget target,
                            RequerimentoProjetoHidrossanitario object
                    ) {
                        limparCamposHidroManual(target, true);
                    }
                });

        txtNumeroProjetoHidrossanitario = new InputField(path(getProxy().getNumeroProjetoAprovado()));
        txtNumeroProjetoHidrossanitario.setEnabled(isModoEdicao());
        txtNumeroProjetoHidrossanitario.setLabel(Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));

        containerProjetoHidrossanitarioAprovado.add(autoCompleteProjetoHidrossanitario, txtNumeroProjetoHidrossanitario);

        getContainerOutros().add(containerProjetoHidrossanitarioAprovado);
    }

    private void limparCamposHidroManual(AjaxRequestTarget target, boolean isLimparCampoProjetoAprovado) {
        if (target != null) {

            if (isHabitese() && isLimparCampoProjetoAprovado) {
                autoCompleteProjetoHidrossanitario.limpar(target);
                txtNumeroProjetoHidrossanitario.limpar(target);
            }

            // Grupo Tipo do Projeto
            limparCamposTipoProjeto(target);
            limparListaTipoProjeto(target);
            tipoProjetoRequired(target);

            // Grupo Dados da Obra
            if (autocompleteEnderecoObra != null) {
                autocompleteEnderecoObra.limpar(target);
                obraNumeroEndereco.limpar(target);
                obraQuadra.limpar(target);
                obraNumeroLado.limpar(target);
                obraLote.limpar(target);
                obraComplemento.limpar(target);
                obraNumeroLoteamento.limpar(target);
            }

            //inscricao imobiliaria
            if (getPnlInscricaoImobiliaria() != null) {
                getPnlInscricaoImobiliaria().limparInscricaoImobiliaria(target);
                getBaseDTO().getListInscricaoImobiliaria().clear();
            }

            //responsavel tecnico
            if (getPnlResponsavelTecnico() != null) {
                getPnlResponsavelTecnico().limparResponsavelTecnico(target);
                getBaseDTO().getListResponsavelTecnico().clear();
            }

            // Grupo Uso da Edificação
            if (ddUsoEdificacao != null) {
                ddUsoEdificacao.limpar(target);
                txtObservacaoUsoEdificacao.limpar(target);
            }

            // Grupo Informações
            if (txtNumeroProcessoProjeto != null) {
                txtNumeroProcessoProjeto.limpar(target);
                ddRegiaoCobertaRedeEsgoto.limpar(target);
                ddRegiaoAbastecidaAgua.limpar(target);
                ddUnifamiliar.limpar(target);
                ddSistemaAprovAguasPluviais.limpar(target);
                ddLincenciavelAmbiental.limpar(target);
                ddReclassificadoBaixoRisco.limpar(target);
            }

            // Grupo Enquadramento
            if (autoCompleteTipoEnquadramentoProjeto != null) {
                autoCompleteTipoEnquadramentoProjeto.limpar(target);
            }

            // Grupo Parcelamento do Solo
            if (txtNumeroLotesParcelamento != null) {
                txtNumeroLotesParcelamento.limpar(target);
                txtNumeroProjetoUrbanistico.limpar(target);
                txtNumeroLicitacaoAmbiental.limpar(target);
                txtNumeroProjetoEsgoto.limpar(target);
                txtNumeroProjetoAgua.limpar(target);
            }

            // Grupo Metragem
            if (txtAreaComercial != null) {
                txtAreaComercial.limpar(target);
                txtAreaResidencial.limpar(target);
                txtAreaTotal.limpar(target);
            }
        }
    }

    private void carregarCamposComProjetoHidrossanitarioAprovado(RequerimentoProjetoHidrossanitario rph, AjaxRequestTarget target) {
        if (rph != null && isHabitese()) {
            carregarListTipoProjeto(rph.getRequerimentoVigilancia(), true);

            if (CollectionUtils.isNotNullEmpty(getBaseDTO().getListTipoProjetos())) {
                String mensagemHabitese = null;
                for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : getBaseDTO().getListTipoProjetos()) {
                    boolean contains = Valor.resolveSomatorio(tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getTipo()).contains(TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE.value());
                    if (!contains) {
                        mensagemHabitese = "O Tipo de Projeto '" + tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getDescricao() + "' do Projeto Hidrossanitário Aprovado não está habilitado para o Habite-se. Aréa: " + tipoProjetoRequerimentoVigilancia.getArea();
                        tipoProjetoRequerimentoVigilancia.setTipoProjetoVigilancia(null);
                    }
                }
                if (mensagemHabitese != null) {
                    info(target, mensagemHabitese);
                    getBaseDTO().getListTipoProjetos().clear();
                }
            }
            tipoProjetoRequired(target);
            tblTipoProjeto.populate(target);

            carregarListResponsavelTecnico(rph.getRequerimentoVigilancia(), true);
            getPnlResponsavelTecnico().responsavelTecnicoRequired(target);
            getPnlResponsavelTecnico().getTblResponsavelTecnico().populate(target);

            carregarListInscricoesImobiliarias(rph.getRequerimentoVigilancia(), true);
            getPnlInscricaoImobiliaria().inscricaoImobiliariaRequired(target);
            getPnlInscricaoImobiliaria().getTblInscricaoImobiliaria().populate(target);

            //Dados da Obra
            if (rph.getVigilanciaEndereco() != null) {
                VigilanciaEndereco endereco = VigilanciaHelper.carregarVigilanciaEnderecoDadosObra(rph.getVigilanciaEndereco().getCodigo());
                getBaseDTO().getRequerimentoVigilancia().setVigilanciaEndereco(endereco);
                autocompleteEnderecoObra.setComponentValue(target, endereco);
            }
            obraNumeroEndereco.setComponentValue(rph.getObraNumeroEndereco());
            obraQuadra.setComponentValue(rph.getObraQuadra());
            obraNumeroLado.setComponentValue(rph.getObraNumeroLado());
            obraLote.setComponentValue(rph.getObraLote());
            obraComplemento.setComponentValue(rph.getObraComplemento());
            obraNumeroLoteamento.setComponentValue(rph.getObraNumeroLoteamento());

            //Uso da Edificação
            ddUsoEdificacao.setComponentValue(rph.getUsoEdificacao());
            txtObservacaoUsoEdificacao.setComponentValue(rph.getObservacaoUsoEdificacao());
            habilitarUsoEdificacao(target);

            //Informações
            txtNumeroProcessoProjeto.setComponentValue(rph.getNumeroProcessoProjeto());
            ddRegiaoCobertaRedeEsgoto.setComponentValue(rph.getRegiaoCobertaRedeEsgoto());
            ddRegiaoAbastecidaAgua.setComponentValue(rph.getRegiaoAbastecidaAgua());
            ddSistemaAprovAguasPluviais.setComponentValue(rph.getSistemaAguaPluvial());
            ddLincenciavelAmbiental.setComponentValue(rph.getProjetoLicenciavelOrgaoAmbiental());
            ddReclassificadoBaixoRisco.setComponentValue(rph.getProcessoReclassificadoBaixoRiscoDecreto());
            ddUnifamiliar.setComponentValue(rph.getEdificacaoExclusivamenteUnifamiliar());

            //Enquadramento
            autoCompleteTipoEnquadramentoProjeto.setComponentValue(rph.getTipoEnquadramentoProjeto());

            //Parcelamento do Solo
            habilitarParcelamentoSolo(target);
            txtNumeroLotesParcelamento.setComponentValue(rph.getParcelamentoSoloNumeroLotes());
            txtNumeroProjetoUrbanistico.setComponentValue(rph.getNumeroProjetoUrbanistico());
            txtNumeroLicitacaoAmbiental.setComponentValue(rph.getNumeroLicitacaoAmbiental());
            txtNumeroProjetoEsgoto.setComponentValue(rph.getNumeroProjetoEsgoto());
            txtNumeroProjetoAgua.setComponentValue(rph.getNumeroProjetoAgua());

            //Metragem
            habilitarMetragem(target);
            txtAreaComercial.setComponentValue(rph.getAreaComercial());
            txtAreaResidencial.setComponentValue(rph.getAreaResidencial());
            txtAreaTotal.setComponentValue(rph.getAreaTotalConstrucao());

            target.add(
                    autoCompleteProjetoHidrossanitario,
                    tblTipoProjeto, autoCompleteTipoProjetoVigilancia, txtArea,
                    autocompleteEnderecoObra, obraNumeroEndereco, obraQuadra, obraNumeroLado, obraLote, obraComplemento, obraNumeroLoteamento,
                    ddUsoEdificacao, txtObservacaoUsoEdificacao,
                    txtNumeroProcessoProjeto, ddRegiaoCobertaRedeEsgoto, ddRegiaoAbastecidaAgua, ddReclassificadoBaixoRisco,
                    ddSistemaAprovAguasPluviais, ddLincenciavelAmbiental, ddUnifamiliar,
                    autoCompleteTipoEnquadramentoProjeto,
                    txtNumeroLotesParcelamento, txtNumeroProjetoUrbanistico, txtNumeroLicitacaoAmbiental, txtNumeroProjetoEsgoto, txtNumeroProjetoAgua,
                    txtAreaComercial, txtAreaResidencial, txtAreaTotal
            );
        }
    }

    private void habilitarCampoProjetoHidroManual(
            AjaxRequestTarget target,
            RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario
    ) {
        limparCamposHidroManual(target, false);
        carregarCamposComProjetoHidrossanitarioAprovado(requerimentoProjetoHidrossanitario, target);
        habilitarUsoEdificacao(target);
        habilitarParcelamentoSolo(target);
        habilitarMetragem(target);
    }

    protected void configurarTipoEstabelecimento() {
        if (getRequerimentoVigilancia().getCodigo() != null) {
            if (RequerimentoVigilancia.TipoRequerente.isPessoa(getRequerimentoVigilancia().getTipoRequerente())) {
                habilitarCampoProjetoHidroVigPessoa(null, getRequerimentoVigilancia().getVigilanciaPessoa());
            } else {
                habilitarCampoProjetoHidroEstabelecimento(null, getRequerimentoVigilancia().getEstabelecimento());
            }
        }
    }

    protected void configurarEstabelecimento() {
        if (estabelecimento != null && estabelecimento.getCodigo() != null) {
            getRequerimentoVigilancia().setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
            getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
            atualizarEnderecoEstabelecimento(null, estabelecimento);
            habilitarCampoProjetoHidroEstabelecimento(null, estabelecimento);
            habilitarCampoProjetoHidroManual(null, null);
        }
    }

    private void habilitarCampoProjetoHidroVigPessoa(
            AjaxRequestTarget target,
            VigilanciaPessoa vigilanciaPessoa
    ) {
        if (isHabitese()) {
            if (vigilanciaPessoa != null) {
                autoCompleteProjetoHidrossanitario.setEnabled(true);
                autoCompleteProjetoHidrossanitario.setVigilanciaPessoa(vigilanciaPessoa);
                autoCompleteProjetoHidrossanitario.setEstabelecimento(null);
            } else {
                autoCompleteProjetoHidrossanitario.setEnabled(false);
                autoCompleteProjetoHidrossanitario.setVigilanciaPessoa(null);
            }

            if (target != null) {
                target.add(autoCompleteProjetoHidrossanitario, txtNumeroProjetoHidrossanitario);
            }
        }
    }

    private void habilitarCampoProjetoHidroEstabelecimento(
            AjaxRequestTarget target,
            Estabelecimento estabelecimento
    ) {
        if (isHabitese()) {
            if (estabelecimento != null) {
                autoCompleteProjetoHidrossanitario.setEnabled(true);
                autoCompleteProjetoHidrossanitario.setEstabelecimento(estabelecimento);
                autoCompleteProjetoHidrossanitario.setVigilanciaPessoa(null);
            } else {
                autoCompleteProjetoHidrossanitario.setEnabled(false);
                autoCompleteProjetoHidrossanitario.setEstabelecimento(null);
            }

            if (target != null) {
                target.add(autoCompleteProjetoHidrossanitario, txtNumeroProjetoHidrossanitario);
            }
        }
    }


    /**
     * MÉTODOS PARA CARREGAR AS LISTS
     */

    protected void carregarConfiguracao() {
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }

        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }
    }

    protected void carregarListTipoProjeto(RequerimentoVigilancia requerimentoVigilancia, boolean isHidroManual) {
        List<TipoProjetoRequerimentoVigilancia> listTipoProjeto = VigilanciaHelper.carregarListTipoProjeto(requerimentoVigilancia);

        getBaseDTO().getListTipoProjetos().clear();
        if (isHabitese() && isHidroManual) {
            for (TipoProjetoRequerimentoVigilancia elo : listTipoProjeto) {
                TipoProjetoRequerimentoVigilancia eloClone = VOUtils.cloneObject(elo);
                eloClone.setRequerimentoVigilancia(null);
                getBaseDTO().getListTipoProjetos().add(eloClone);
            }
        } else {
            getBaseDTO().getListTipoProjetos().addAll(listTipoProjeto);
        }
    }

    protected void carregarListResponsavelTecnico(RequerimentoVigilancia rv, boolean isHidroManual) {
        List<EloRequerimentoVigilanciaResponsavelTecnico> listResponsavelTecnico = VigilanciaHelper.carregarListResponsavelTecnico(rv);

        getBaseDTO().getListResponsavelTecnico().clear();
        if (isHabitese() && isHidroManual) {
            for (EloRequerimentoVigilanciaResponsavelTecnico elo : listResponsavelTecnico) {
                EloRequerimentoVigilanciaResponsavelTecnico eloClone = VOUtils.cloneObject(elo);
                eloClone.setRequerimentoVigilancia(null);
                getBaseDTO().getListResponsavelTecnico().add(eloClone);
            }
        } else {
            getBaseDTO().getListResponsavelTecnico().addAll(listResponsavelTecnico);
        }
    }

    protected void carregarListInscricoesImobiliarias(RequerimentoVigilancia rv, boolean isHidroManual) {
        List<RequerimentoVigilanciaInscricaoImob> listInscricaoImobiliaria = VigilanciaHelper.carregarListInscricoesImobiliarias(rv);

        if (isHabitese() && isHidroManual) {
            getBaseDTO().getListInscricaoImobiliaria().clear();
            for (RequerimentoVigilanciaInscricaoImob elo : listInscricaoImobiliaria) {
                RequerimentoVigilanciaInscricaoImob eloClone = VOUtils.cloneObject(elo);
                eloClone.setRequerimentoVigilancia(null);
                getBaseDTO().getListInscricaoImobiliaria().add(eloClone);
            }
        } else {
            getBaseDTO().getListInscricaoImobiliaria().addAll(listInscricaoImobiliaria);
        }
    }

    protected void carregarAnexos() {
        List<RequerimentoVigilanciaAnexo> listAnexos = VigilanciaHelper.carregarAnexosVigilancia(getRequerimentoVigilancia());

        for (RequerimentoVigilanciaAnexo rva : listAnexos) {
            RequerimentoVigilanciaAnexoDTO anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            getBaseDTO().getListAnexos().add(anexoDTO);
        }
    }

    protected void carregarPranchasHidrossanitario() {
        List<AnexoPranchaDTO> listPranchas = VigilanciaPageHelper.carregarAnexosPranchasHidrossanitario(getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(listPranchas)) {
            getBaseDTO().getListAnexosPrancha().addAll(listPranchas);
        }
    }

    protected void carregarPranchasProjetoArquitetonico() {
        List<AnexoPranchaDTO> listPranchas = VigilanciaPageHelper.carregarPranchasProjetoArquitetonico(getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(listPranchas)) {
            getBaseDTO().getListAnexosPrancha().addAll(listPranchas);
        }
    }

    protected void carregarMemorial() {
        List<AnexoPranchaDTO> listMemorial = VigilanciaPageHelper.carregarMemorialProjetoArquitetonico(getRequerimentoVigilancia());

        if (CollectionUtils.isNotNullEmpty(listMemorial)) {
            getBaseDTO().getListAnexosMemorial().addAll(listMemorial);
        }
    }

    protected void tipoProjetoRequired(AjaxRequestTarget target) {
        if (CollectionUtils.isEmpty(getBaseDTO().getListTipoProjetos())) {
            autoCompleteTipoProjetoVigilancia.getTxtDescricao().addRequiredClass();
            txtArea.addRequiredClass();
        } else {
            autoCompleteTipoProjetoVigilancia.getTxtDescricao().removeRequiredClass();
            txtArea.removeRequiredClass();
        }

        if (target != null) {
            target.add(autoCompleteTipoProjetoVigilancia, txtArea);
        }
    }

    protected void dadosProjetoRequired(AjaxRequestTarget target) {
        if (isProjetoArquitetonicoSanitario()) {
            obraQuadra.setRequired(true);
            obraQuadra.addRequiredClass();
            obraLote.setRequired(true);
            obraLote.addRequiredClass();
        }
        if (target != null) {
            target.add(obraQuadra, obraLote);
        }
    }


    private boolean isHabitese() {
        return (TipoSolicitacao.TipoDocumento.isHabitese(getTipoSolicitacao().getEnumTipoDocumento()));
    }

    private boolean isProjetoArquitetonicoSanitario() {
        return TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value().equals(tipoSolicitacao.getTipoDocumento());
    }

    private Page getPageVoltar() {
        try {
            return (Page) this.pageReturn.newInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    protected Estabelecimento salvarEstabelecimento(Long tipoRequerente, Estabelecimento estabelecimento) throws DAOException, ValidacaoException {
        if (RequerimentoVigilancia.TipoRequerente.isEstabelecimento(tipoRequerente) &&
                estabelecimento != null &&
                estabelecimento.getCodigo() == null
        ) {
            Estabelecimento estabelecimento2 = BOFactoryWicket.save(estabelecimento);

            return estabelecimento2;
        }
        return null;
    }

    protected VigilanciaPessoa salvarPessoaVigilancia(Long tipoRequerente, VigilanciaPessoa vigilanciaPessoa) throws DAOException, ValidacaoException {
        if (RequerimentoVigilancia.TipoRequerente.isPessoa(tipoRequerente) &&
                vigilanciaPessoa != null &&
                vigilanciaPessoa.getCodigo() == null
        ) {
            VigilanciaPessoa vigilanciaPessoa2 = BOFactoryWicket.save(vigilanciaPessoa);
            return vigilanciaPessoa2;
        }
        return null;
    }

    private void criarDialogImpressao(AjaxRequestTarget target, RequerimentoVigilancia rv) throws ValidacaoException {
        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        dlgImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList<>();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(
                    AjaxRequestTarget target,
                    RequerimentoVigilancia object
            ) throws ValidacaoException, DAOException {
                redirectAfterSave(rv.getProtocoloFormatado());
            }
        };

        addModal(target, dlgImpressao);
        dlgImpressao.show(target, rv);
    }

    protected void validarRequerimentoVigilancia(RequerimentoVigilancia rv) throws ValidacaoException {
        if (RequerimentoVigilancia.TipoRequerente.isEstabelecimento(rv.getTipoRequerente())) {
            if (rv.getEstabelecimento() == null) {
                throw new ValidacaoException(bundle("msgObrigatorioInformarEstabelecimento"));
            }
            if (getTxtEmailEstabelecimento().isEnabled()) {
                if (!EmailValidator.validarEmail(rv.getEstabelecimento().getEmail())) {
                    throw new ValidacaoException(bundle("email_invalido"));
                }
            }
        }

        if (RequerimentoVigilancia.TipoRequerente.isPessoa(rv.getTipoRequerente())) {
            if (rv.getVigilanciaPessoa() == null) {
                throw new ValidacaoException(bundle("msgObrigatorioInformarPessoa"));
            }
            if (getTxtEmailPessoa().isEnabled()) {
                if (!EmailValidator.validarEmail(rv.getVigilanciaPessoa().getEmail())) {
                    throw new ValidacaoException(bundle("email_invalido"));
                }
            }
        }

        if (!EmailValidator.validarEmail(rv.getEmailSolicitante())) {
            throw new ValidacaoException(bundle("email_invalido"));
        }

        if (rv.getCpfSolicitante() == null && rv.getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
    }

    protected boolean isEdicao() {
        return isModoEdicao() && getRequerimentoVigilancia() != null && getRequerimentoVigilancia().getCodigo() != null;
    }

    protected boolean isConsulta() {
        return !isModoEdicao() && getRequerimentoVigilancia() != null && getRequerimentoVigilancia().getCodigo() != null;
    }

    /**
     * GETTERS AND SETTERS
     */

    public Form<T> getForm() {
        return form;
    }

    public void setForm(Form<T> form) {
        this.form = form;
    }

    public Class getPageReturn() {
        return pageReturn;
    }

    public void setPageReturn(Class pageReturn) {
        this.pageReturn = pageReturn;
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public WebMarkupContainer getContainerOutros() {
        return containerOutros;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    public InputField getTxtEmailPessoa() {
        return txtEmailPessoa;
    }

    public InputField getTxtEmailEstabelecimento() {
        return txtEmailEstabelecimento;
    }

    public RequerimentoHidrossanitarioBaseDTO getBaseDTO() {
        return baseDTO;
    }

    public boolean isModoEdicao() {
        return modoEdicao;
    }

    public DropDown getDdReclassificadoBaixoRisco() {
        return ddReclassificadoBaixoRisco;
    }

    public PnlDadosComumRequerimentoVigilancia getPnlDadosComum() {
        return pnlDadosComum;
    }

    public PnlResponsavelTecnico getPnlResponsavelTecnico() {
        return pnlResponsavelTecnico;
    }

    public PnlInscricaoImobiliaria getPnlInscricaoImobiliaria() {
        return pnlInscricaoImobiliaria;
    }

    public RequerimentoHidrossanitarioBaseDTO getProxy() {
        return on(RequerimentoHidrossanitarioBaseDTO.class);
    }

}
