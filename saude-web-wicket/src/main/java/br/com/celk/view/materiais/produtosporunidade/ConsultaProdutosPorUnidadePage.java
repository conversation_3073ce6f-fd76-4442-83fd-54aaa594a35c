package br.com.celk.view.materiais.produtosporunidade;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.materiais.estoqueempresa.customize.CustomizeConsultaEstoqueEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.produtosporunidade.customcolumn.ConsultaProdutosPorUnidadeColumnPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaProdutosPorUnidadePage extends ConsultaPage<EstoqueEmpresa, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private Produto produto;
    private Localizacao localizacao;
    private String flagAtivo;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private AbstractAjaxButton btnCopiarPorUnidade;
    private AbstractAjaxButton btnCopiarPorProduto;
    private AbstractAjaxButton btnGerarEstoqueEmpresaPorGrupoSubgrupo;
    private AbstractAjaxButton btnAtivarEstoqueEmpresaPorGrupoSubgrupo;
    private DlgCopiarEstoqueEmpresaPorUnidade dialogCopiarEstoqueEmpresaPorUnidade;
    private DlgCopiarEstoqueEmpresaPorProduto dialogCopiarEstoqueEmpresaPorProduto;
    private DlgGerarEstoqueEmpresaPorGrupoSubgrupo dlgGerarEstoqueEmpresaPorGrupoSubgrupo;
    private DlgAtivarEstoqueEmpresaPorGrupoSubgrupo dlgAtivarEstoqueEmpresaPorGrupoSubgrupo;
    private DropDown<String> dropDownFlagAtivo;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    public ConsultaProdutosPorUnidadePage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("empresa").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL, Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,Empresa.TIPO_ESTABELECIMENTO_FARMACIA)));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        form.add(new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(getDropDownFlagAtivo());
        form.add(dialogCopiarEstoqueEmpresaPorUnidade = new DlgCopiarEstoqueEmpresaPorUnidade("dialogCopiarEstoqueEmpresaPorUnidade"));
        form.add(dialogCopiarEstoqueEmpresaPorProduto = new DlgCopiarEstoqueEmpresaPorProduto("dialogCopiarEstoqueEmpresaPorProduto"));
        form.add(dlgGerarEstoqueEmpresaPorGrupoSubgrupo = new DlgGerarEstoqueEmpresaPorGrupoSubgrupo("dlgGerarEstoqueEmpresaPorGrupoSubgrupo"));
        form.add(dlgAtivarEstoqueEmpresaPorGrupoSubgrupo = new DlgAtivarEstoqueEmpresaPorGrupoSubgrupo("dlgAtivarEstoqueEmpresaPorGrupoSubgrupo"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());

        form.add(btnCopiarPorUnidade = new AbstractAjaxButton("btnCopiarPorUnidade") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dialogCopiarEstoqueEmpresaPorUnidade.show(target);
            }
            
        });
        autoCompleteConsultaProduto.setIncluirInativos(false);
        getControls().add(btnCopiarPorUnidade);
        
        btnCopiarPorUnidade.add(new AttributeModifier("class", "clipboard-copy"));
        btnCopiarPorUnidade.add(new AttributeModifier("value", BundleManager.getString("copiarPorUnidade")));
        
        form.add(btnCopiarPorProduto = new AbstractAjaxButton("btnCopiarPorProduto") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dialogCopiarEstoqueEmpresaPorProduto.show(target);
            }
            
        });
        getPageableTable().setScrollX("1300px");
        
        getControls().add(btnCopiarPorProduto);
        
        btnCopiarPorProduto.add(new AttributeModifier("class", "clipboard-copy"));
        btnCopiarPorProduto.add(new AttributeModifier("value", BundleManager.getString("copiarPorProduto")));

        form.add(btnGerarEstoqueEmpresaPorGrupoSubgrupo = new AbstractAjaxButton("btnGerarEstoqueEmpresaPorGrupoSubgrupo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgGerarEstoqueEmpresaPorGrupoSubgrupo.limpar(target);
                dlgGerarEstoqueEmpresaPorGrupoSubgrupo.show(target);
            }
        });
        getControls().add(btnGerarEstoqueEmpresaPorGrupoSubgrupo);

        btnGerarEstoqueEmpresaPorGrupoSubgrupo.add(new AttributeModifier("class", "clipboard-copy"));
        btnGerarEstoqueEmpresaPorGrupoSubgrupo.add(new AttributeModifier("value", BundleManager.getString("gerarGrupoSubgrupo")));

        form.add(btnAtivarEstoqueEmpresaPorGrupoSubgrupo = new AbstractAjaxButton("btnAtivarEstoqueEmpresaPorGrupoSubgrupo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgAtivarEstoqueEmpresaPorGrupoSubgrupo.limpar(target);
                dlgAtivarEstoqueEmpresaPorGrupoSubgrupo.show(target);
            }
        });
        getControls().add(btnAtivarEstoqueEmpresaPorGrupoSubgrupo);

        btnAtivarEstoqueEmpresaPorGrupoSubgrupo.add(new AttributeModifier("class", "clipboard-copy"));
        btnAtivarEstoqueEmpresaPorGrupoSubgrupo.add(new AttributeModifier("value", BundleManager.getString("ativarGrupoSubgrupo")));

        setExibeExpandir(true);
    }

    public DropDown<String> getDropDownFlagAtivo(){
        if (dropDownFlagAtivo == null) {
            dropDownFlagAtivo = new DropDown<String>(EstoqueEmpresa.PROP_FLAG_ATIVO);
            
            dropDownFlagAtivo.addChoice(null, BundleManager.getString("ambos"));
            dropDownFlagAtivo.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("ativo"));
            dropDownFlagAtivo.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("inativo"));
        }
        
       return dropDownFlagAtivo;
    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(EstoqueEmpresa.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO), VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO_FORMATADO2)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estoqueMinimoAbv"), VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_MINIMO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("quantidadePadraoAbv"), VOUtils.montarPath(EstoqueEmpresa.PROP_QUANTIDADE_PADRAO_DISPENSACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("localizacao"), VOUtils.montarPath(EstoqueEmpresa.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO)));
        
        return columns;
    }

    private CustomColumn<EstoqueEmpresa> getCustomColumn() {
        return new CustomColumn<EstoqueEmpresa>() {

            @Override
            public Component getComponent(String componentId, final EstoqueEmpresa rowObject) {
                return new ConsultaProdutosPorUnidadeColumnPanel(componentId,rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroProdutosPorUnidadePage(rowObject,false,true));
                    }

                    @Override
                    public void onDesativa(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setFlagAtivo(RepositoryComponentDefault.NAO);
                        BOFactoryWicket.save(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onAtiva(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setFlagAtivo(RepositoryComponentDefault.SIM);
                        BOFactoryWicket.save(rowObject);
                        getPageableTable().populate(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaEstoqueEmpresa()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IGUAL, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, produto));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_LOCALIZACAO), BuilderQueryCustom.QueryParameter.IGUAL, localizacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), BuilderQueryCustom.QueryParameter.IGUAL, flagAtivo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO), BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO), BuilderQueryCustom.QueryParameter.IGUAL, grupoProduto));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroProdutosPorUnidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProdutosPorUnidade");
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    dropDownSubGrupo.limpar(target);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
            for (GrupoProduto grupoProduto : grupos) {
                dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }
}
