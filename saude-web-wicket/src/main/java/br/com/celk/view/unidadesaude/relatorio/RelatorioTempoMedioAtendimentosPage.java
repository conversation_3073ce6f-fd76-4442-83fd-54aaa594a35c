package br.com.celk.view.unidadesaude.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioTempoMedioAtendimentosDTOParam;
import br.com.ksisolucoes.util.Data;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 * <AUTHOR>
 */
@Private
public class RelatorioTempoMedioAtendimentosPage extends RelatorioPage<RelatorioTempoMedioAtendimentosDTOParam> {

    private AutoCompleteConsultaProfissional autoCompleteProfissional;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {

        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        boolean isActionPermittedEmpresa = isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermittedEmpresa);

        form.add(autoCompleteProfissional = new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus"));
        form.add(new AutoCompleteConsultaTabelaCbo("tabelaCbo"));
        form.add(new AutoCompleteConsultaTipoAtendimento("tipoAtendimento"));
        form.add(new RequiredPnlDatePeriod("periodo"));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioTempoMedioAtendimentosDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioTempoMedioAtendimentosDTOParam param) throws ReportException {
        if (param.getPeriodo().getDataInicial() != null || param.getPeriodo().getDataFinal() != null) {
            if (param.getPeriodo().getDataInicial() == null && param.getPeriodo().getDataFinal() != null) {
                throw new ReportException(BundleManager.getString("msg_informar_data_inicial"));
            }
            if (param.getPeriodo().getDataInicial() != null && param.getPeriodo().getDataFinal() != null) {
                Long diasDiferenca = Data.getDiasDiferenca(param.getPeriodo().getDataInicial(), param.getPeriodo().getDataFinal());
                if (diasDiferenca > 30) {
                    throw new ReportException(BundleManager.getString("msg_nao_permitido_informar_periodo_maior_que_31_dias"));
                }
            }
        }
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioRelacaoTempoMedioAtendimentos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioTempoMedioAtendimentos");
    }
}
