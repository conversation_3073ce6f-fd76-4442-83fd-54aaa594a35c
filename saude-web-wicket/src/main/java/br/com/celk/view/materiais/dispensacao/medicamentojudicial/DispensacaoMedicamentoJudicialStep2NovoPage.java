package br.com.celk.view.materiais.dispensacao.medicamentojudicial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.processojudicial.ConsultaProdutoSolicitadoNovoPage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProdutoSolicitadoItem;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.dto.ProdutoSolicitadoDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.dto.ReciboProdutoSolicitadoDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.geral.interfaces.facade.GeralReportFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoItem;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matchers;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;

/**
 * <AUTHOR>
 */
@Private
public class DispensacaoMedicamentoJudicialStep2NovoPage extends BasePage {

    private final UsuarioCadsus usuarioCadsus;
    private Form<ProdutoSolicitadoDTO> form;

    private AutoCompleteConsultaProdutoSolicitadoItem autoCompleteConsultaProdutoSolicitado;

    private String lote;

    private Table<ProdutoSolicitadoDTO> tableProdutoSolicitado;
    private final List<ProdutoSolicitadoDTO> lstProdutoSolicitadoDTO;

    private DlgImpressaoObject dlgImpressaoObject;
    private DlgAdicionarDispensacaoMedicamentoJudicial dlgAdicionarItem;
    private DropDown dropDownTipoSolicitacaoProduto;
    private ProdutoSolicitado produtoSolicitado;
    private AutoCompleteConsultaEmpresa autoCompleteEmpresa;
    private DateChooser dataSolicitacaoItem;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private InputField txtRota;
    private String justificativa;

    public DispensacaoMedicamentoJudicialStep2NovoPage(UsuarioCadsus usuarioCadsus, ProdutoSolicitado produtoSolicitado, String justificativa) {
        this.lstProdutoSolicitadoDTO = new ArrayList<ProdutoSolicitadoDTO>();
        this.usuarioCadsus = usuarioCadsus;
        this.produtoSolicitado = produtoSolicitado;
        this.justificativa = justificativa;
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(new ProdutoSolicitadoDTO()));

        ProdutoSolicitadoDTO proxy = on(ProdutoSolicitadoDTO.class);

        form.add(autoCompleteEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getProdutoSolicitado().getEmpresa())).setLabel(new Model<String>(bundle("estabelecimentoSolicitante"))).setEnabled(false));
        form.add(dataSolicitacaoItem = (DateChooser) new DateChooser(path(proxy.getProdutoSolicitado().getDataReceita())).setLabel(new Model<String>(bundle("dataSolicitacao"))).setEnabled(false));
        form.add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getProdutoSolicitado().getProfissional())).setLabel(new Model<String>(bundle("profissionalSolicitante"))).setEnabled(false));
        form.add(getDropDownTipoSolicitacaoProduto(path(proxy.getProdutoSolicitado().getTipoSolicitacaoProduto())).setEnabled(false));
        form.add(txtRota = new DisabledInputField(path(proxy.getProdutoSolicitado().getDescricaoRota())));

        form.add(autoCompleteConsultaProdutoSolicitado = new AutoCompleteConsultaProdutoSolicitadoItem("produtoSolicitadoItem"));
        autoCompleteConsultaProdutoSolicitado.add(new ConsultaListener<ProdutoSolicitadoItem>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProdutoSolicitadoItem object) {
                verificaValidade(target, object);
            }


        });
        autoCompleteConsultaProdutoSolicitado.setStatus(ProdutoSolicitado.STATUS_ATIVO);
        autoCompleteConsultaProdutoSolicitado.setUsuarioCadsus(usuarioCadsus);
        autoCompleteConsultaProdutoSolicitado.setProdutoSolicitado(produtoSolicitado);

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form c) throws ValidacaoException, DAOException {
                ProdutoSolicitadoItem produtoSolicitadoItem = (ProdutoSolicitadoItem) autoCompleteConsultaProdutoSolicitado.getComponentValue();
                if (produtoSolicitadoItem != null) {
                    boolean produtoJaAdicionado = exists(lstProdutoSolicitadoDTO, having(on(ProdutoSolicitadoDTO.class).getProdutoSolicitadoItem().getProduto().getCodigo(), Matchers.equalTo(produtoSolicitadoItem.getProduto().getCodigo())));
                    if (produtoJaAdicionado) {
                        autoCompleteConsultaProdutoSolicitado.limpar(target);
                        throw new ValidacaoException(bundle("produtoJaAdicionado"));
                    } else {
                        addModal(target, dlgAdicionarItem = new DlgAdicionarDispensacaoMedicamentoJudicial(newModalId()) {

                            @Override
                            public void adicionar(AjaxRequestTarget target, ProdutoSolicitadoDTO itemOrigem, ProdutoSolicitadoDTO itemDestino) throws ValidacaoException, DAOException {
                                Double quantidadeTotal = 0D;
                                if (CollectionUtils.isNotNullEmpty(itemOrigem.getLotes())) {
                                    for (MovimentoGrupoEstoqueItemDTO lote : itemOrigem.getLotes()) {
                                        quantidadeTotal += lote.getQuantidade();

                                    }
                                }
                                if (quantidadeTotal > 0D) {
                                    itemOrigem.setQuantidade(quantidadeTotal);
                                }
                                DispensacaoMedicamentoJudicialStep2NovoPage.this.adicionarItem(target, itemOrigem, itemDestino);
                            }
                        });
                        dlgAdicionarItem.setObject(target, DispensacaoMedicamentoJudicialStep2NovoPage.this.form.getModel().getObject());
                        dlgAdicionarItem.show(target);
                    }
                }
            }
        });

        form.add(tableProdutoSolicitado = new Table("tableProdutoSolicitado", getColumns(), getCollectionProvider()));
        tableProdutoSolicitado.populate();

        form.add(new VoltarButton("btnVoltar"));

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        carregaSolicitacaoProduto();
        add(form);
    }

    //Verifica a data de validade informada no menu 520, se menor que atual, barra dispensaçao do mesmo e emite mensagem.
    private void verificaValidade(AjaxRequestTarget target, ProdutoSolicitadoItem object) {
        if (object.getDataValidade() != null) {
            if (object.getDataValidade().compareTo(DataUtil.getDataAtualSemHora()) < 0) {
                warn(target, bundle("oProdutoEstaComDataDeValidadeVencidaProdutoXDataValidadeX", object.getProduto().getDescricao(), DataUtil.getFormatarDiaMesAno(object.getDataValidade())));
                updateNotificationPanel(target);
                autoCompleteConsultaProdutoSolicitado.limpar(target);
                target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaProdutoSolicitado.getTxtDescricao().getTextField()));
            }
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ProdutoSolicitadoDTO proxy = on(ProdutoSolicitadoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("produto"), proxy.getProdutoSolicitadoItem().getProduto().getDescricao()));
        columns.add(createColumn(bundle("dataValidade"), proxy.getProdutoSolicitadoItem().getDataValidade()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(createColumn(bundle("observacao"), proxy.getProdutoSolicitadoItem().getObservacao()));

        return columns;
    }

    private DropDown getDropDownTipoSolicitacaoProduto(String id) {
        if (dropDownTipoSolicitacaoProduto == null) {
            dropDownTipoSolicitacaoProduto = (DropDown) new DropDown(id).setLabel(new Model<String>(bundle("tipoSolicitacao")));

            dropDownTipoSolicitacaoProduto.addChoice(produtoSolicitado.getTipoSolicitacaoProduto(), produtoSolicitado.getTipoSolicitacaoProduto().getDescricao());
        }
        return dropDownTipoSolicitacaoProduto;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstProdutoSolicitadoDTO;
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ProdutoSolicitadoDTO>() {

            @Override
            public void customizeColumn(ProdutoSolicitadoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProdutoSolicitadoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ProdutoSolicitadoDTO modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private void carregaSolicitacaoProduto() {
        if (produtoSolicitado != null) {
            autoCompleteEmpresa.setComponentValue(produtoSolicitado.getEmpresa());
            autoCompleteConsultaProfissional.setComponentValue(produtoSolicitado.getProfissional());
            dataSolicitacaoItem.setComponentValue(produtoSolicitado.getDataReceita());
            txtRota.setComponentValue(produtoSolicitado.getDescricaoRota());
        }


    }

    private void remover(AjaxRequestTarget target, ProdutoSolicitadoDTO modelObject) {
        for (int i = 0; i < lstProdutoSolicitadoDTO.size(); i++) {
            if (lstProdutoSolicitadoDTO.get(i) == modelObject) {
                lstProdutoSolicitadoDTO.remove(i);
                break;
            }
        }
        tableProdutoSolicitado.update(target);
    }

    //itemOrigem é o retorno do Dlg com o lote, quantidade e preços, itemDestino é só o produto
    private void adicionarItem(AjaxRequestTarget target, ProdutoSolicitadoDTO itemOrigem, ProdutoSolicitadoDTO itemDestino) throws ValidacaoException, DAOException {
        boolean add = true;
        try {
            BeanUtils.copyProperties(itemDestino, itemOrigem);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (InvocationTargetException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        for (ProdutoSolicitadoDTO produtoSolicitadoDTO : lstProdutoSolicitadoDTO) {
            if (produtoSolicitadoDTO == itemDestino) {
                add = false;
            }
        }
        if (add) {
            validarAdicionar(target, itemDestino);
            lstProdutoSolicitadoDTO.add(itemDestino);
        }
        form.setModelObject(new ProdutoSolicitadoDTO());
        limpar(target);
        target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaProdutoSolicitado.getTxtDescricao().getTextField()));
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void validarAdicionar(AjaxRequestTarget target, ProdutoSolicitadoDTO produtoSolicitadoDTO) throws DAOException, ValidacaoException {
        if (Coalesce.asDouble(produtoSolicitadoDTO.getQuantidade()).compareTo(0D) == 0) {
            throw new ValidacaoException(BundleManager.getString("adicionePeloMenosUmLote"));
        }
        Long dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados");
        if (!RepositoryComponentDefault.SEM_CONTROLE.equals(dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados)) {
            if (RepositoryComponentDefault.IGUAL_SOLICITADO.equals(dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados)
                    && !produtoSolicitadoDTO.getQuantidade().equals(form.getModelObject().getProdutoSolicitadoItem().getQuantidadeMensal())) {
                throw new ValidacaoException(BundleManager.getString("qtdTotalAdicionadaDeveSerIgualSolicitada"));
            }
            if (RepositoryComponentDefault.ABAIXO_SOLICITADO.equals(dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados)
                    && produtoSolicitadoDTO.getQuantidade() > (form.getModelObject().getProdutoSolicitadoItem().getQuantidadeMensal())) {
                throw new ValidacaoException(BundleManager.getString("qtdTotalAdicionadaDeveSerMenorOuIgualSolicitada"));
            }
            if (RepositoryComponentDefault.ACIMA_SOLICITADO.equals(dispensarQuantidadeIgualSolicitadaEmProdutosSolicitados)
                    && produtoSolicitadoDTO.getQuantidade() < (form.getModelObject().getProdutoSolicitadoItem().getQuantidadeMensal())) {
                throw new ValidacaoException(BundleManager.getString("qtdTotalAdicionadaDeveSerMaiorOuIgualSolicitada"));
            }
        }
        for (ProdutoSolicitadoDTO item : lstProdutoSolicitadoDTO) {
            if (item.getProdutoSolicitadoItem().getProduto().getCodigo().equals(form.getModelObject().getProdutoSolicitadoItem().getProduto().getCodigo()) && lstProdutoSolicitadoDTO.size() > 1) {
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaProdutoSolicitado.limpar(target);
        tableProdutoSolicitado.update(target);
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (CollectionUtils.isEmpty(lstProdutoSolicitadoDTO)) {
            throw new ValidacaoException(BundleManager.getString("informePeloMenosUmProduto"));
        }
        Long numeroBaixa = BOFactoryWicket.getBO(MaterialBasicoFacade.class).baixarProdutoSolicitado(lstProdutoSolicitadoDTO,justificativa);

        addModal(target, dlgImpressaoObject = new DlgImpressaoObject(newModalId(), bundle("dispensadoSucessoImprimir")) {

            @Override
            public DataReport getDataReport(Object object) throws ReportException {
                ReciboProdutoSolicitadoDTOParam param = new ReciboProdutoSolicitadoDTOParam();
                param.setCodigosMovimentos(Arrays.asList((Long) object));
                return BOFactory.getBO(GeralReportFacade.class).reciboEntregaMedicamentoSolicitado(param);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, Object object) throws ValidacaoException, DAOException {
                Page page = new ConsultaProdutoSolicitadoNovoPage();
                getSession().getFeedbackMessages().info(page, bundle("registroSalvoSucesso"));
                setResponsePage(page);
            }

        });
        dlgImpressaoObject.show(target, numeroBaixa);

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("dispensacaoMedicamentosJudicializados");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaProdutoSolicitado.getTxtDescricao().getTextField())));
    }

}
