package br.com.celk.view.vigilancia.rotinas.autoinfracao.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConcluirInfracao extends Window {

    private PnlConcluirInfracao pnlConcluirInfracao;

    public DlgConcluirInfracao(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("concluirInfracao"));
        setInitialWidth(400);
        setInitialHeight(130);
        setResizable(true);

        setContent(pnlConcluirInfracao = new PnlConcluirInfracao(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, AutoInfracao autoInfracao) throws ValidacaoException, DAOException {
                close(target);
                DlgConcluirInfracao.this.onConfirmar(target, autoInfracao);
            }
        });

    }

    public void show(AjaxRequestTarget target, AutoInfracao autoInfracao) {
        super.show(target);
        pnlConcluirInfracao.setAutoInfracao(target, autoInfracao);
    }

    public PnlConcluirInfracao getPnlConcluirInfracao() {
        return pnlConcluirInfracao;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AutoInfracao autoInfracao) throws ValidacaoException, DAOException;

}
