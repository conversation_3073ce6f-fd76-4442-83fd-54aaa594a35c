package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlSelecionaEmpresa extends Panel {

    private AbstractAjaxButton btnFechar;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa empresa;

    public PnlSelecionaEmpresa(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", true));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, empresa);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, Empresa empresa) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.autoCompleteConsultaEmpresa.limpar(target);
        this.autoCompleteConsultaEmpresa.setComponentValue(target, SessaoAplicacaoImp.getInstance().getEmpresa());
        target.appendJavaScript(JScript.removeAutoCompleteSelection(autoCompleteConsultaEmpresa));
    }

    public InputField getTextFieldFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
