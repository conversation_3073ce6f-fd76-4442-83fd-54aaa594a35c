package br.com.celk.view.materiais.devolucao.item.lote;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlLotesDevolucao extends Panel{

    private Table<MovimentoGrupoEstoqueItemDTO> tblLotes;
    private CompoundPropertyModel<MovimentoGrupoEstoqueItemDTO> modelLote;
    private InputField txtProduto;
    private LoteDevolucaoChooser txtLote;
    private DoubleField txtQuantidade;
    private DoubleField txtTotal;
    private DateChooser dchDataValidade;
    
    private List<MovimentoGrupoEstoqueItemDTO> lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private List<MovimentoGrupoEstoqueItemDTO> lotesTemp = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private Produto produto;
    private UsuarioCadsus usuarioCadsus;
    
    public PnlLotesDevolucao(String id) {
        super(id);
        init();
    }

    public PnlLotesDevolucao(String id, IModel<?> model) {
        super(id, model);
        init();
    }
    
    private void init(){
        Form form = new Form("form", modelLote = new CompoundPropertyModel(new MovimentoGrupoEstoqueItemDTO()));
        
        form.add(txtProduto = new DisabledInputField("produto", new PropertyModel(this, "produto.descricao")));
        form.add(txtLote = new LoteDevolucaoChooser(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_GRUPO_ESTOQUE)));
        form.add(txtQuantidade = new DoubleField(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_QUANTIDADE)).setMDec(0));
        form.add(dchDataValidade = new DateChooser(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_DATA_VALIDADE)));
        form.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarLote(target);
            }
        });
        
        form.add(tblLotes = new Table("tblLotes", getColumns(), getCollectionProvider()));
        tblLotes.populate();
        
        form.add(txtTotal = new DisabledDoubleField("total", new LoadableDetachableModel<Double>() {

            @Override
            protected Double load() {
                return getQuantidadeTotalInterno();
            }
        }));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                lotes = new ArrayList(lotesTemp);
                onConfirmar(target);
            }
        });
        
        form.add(new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
                cancelar(target);
            }
        });
        
        add(form);
        
        txtLote.add(new LoteDevolucaoChooser.LoteListener() {

            @Override
            public void onLoteChanged(AjaxRequestTarget target, String lote) {
                carregarLoteExistente(target, lote);
            }
        });
    }
    
    public void cancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        lotesTemp = new ArrayList(lotes);
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(MovimentoGrupoEstoqueItemDTO.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("lote"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_GRUPO_ESTOQUE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("validade"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_DATA_VALIDADE)));
        columns.add(new DoubleColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_QUANTIDADE)).setCasasDecimais(0));
        
        return columns;
    }
    
    private void carregarLoteExistente(AjaxRequestTarget target, String lote){
        try {
            if(lote != null){
                AbstractSessaoAplicacao sessaoAplicacao = br.com.celk.system.session.ApplicationSession.get().getSession();
                EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO,Deposito.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, sessaoAplicacao.getCodigoEmpresa()))
                        .start().getVO();
                if (empresaMaterial == null || empresaMaterial.getDeposito()== null || empresaMaterial.getDeposito().getCodigo() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO)));
                }

                GrupoEstoque loteExistente = LoadManager.getInstance(GrupoEstoque.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID,GrupoEstoquePK.PROP_CODIGO_DEPOSITO), empresaMaterial.getDeposito().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID,GrupoEstoquePK.PROP_LOCALIZACAO_ESTRUTURA), EstoqueHelper.getLocalizacaoEstruturaPadrao()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID,GrupoEstoquePK.PROP_GRUPO), lote))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID,GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CODIGO), getProduto().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID,GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_CODIGO), sessaoAplicacao.getCodigoEmpresa()))
                        .start().getVO();
                if(loteExistente != null && loteExistente.getDataValidade() != null){
                    modelLote.getObject().setDataValidade(loteExistente.getDataValidade());
                    dchDataValidade.setEnabled(false);
                }else{
                    modelLote.getObject().setDataValidade(null);
                    dchDataValidade.setEnabled(true);
                }
            }else{
                modelLote.getObject().setDataValidade(null);
                dchDataValidade.setEnabled(true);
            }
            target.add(dchDataValidade);
            target.focusComponent(txtQuantidade);
            target.appendJavaScript(JScript.initMasks());
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
            
    private CustomColumn getCustomColumn(){
        return new CustomColumn<MovimentoGrupoEstoqueItemDTO>() {

            @Override
            public Component getComponent(String componentId, final MovimentoGrupoEstoqueItemDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerLote(target, rowObject);
                    }
                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lotesTemp;
            }
        };
    }
    
    private void adicionarLote(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        MovimentoGrupoEstoqueItemDTO lote = modelLote.getObject();
        if (StringUtils.trimToNull(lote.getGrupoEstoque())==null) {
            throw new ValidacaoException(BundleManager.getString("informeLote"));
        }
        if (lote.getQuantidade()==0D) {
            throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
        }
        if (lote.getDataValidade()==null) {
            throw new ValidacaoException(BundleManager.getString("informeValidade"));
        }
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotesTemp) {
            if(movimentoGrupoEstoqueItemDTO.getGrupoEstoque().equals(lote.getGrupoEstoque())){
                throw new ValidacaoException(BundleManager.getString("loteJaAdicionado"));
            }
        }
        
        if(lote.getDataValidade().before(Data.adjustRangeHour(Data.getDataAtual()).getDataInicial())){
            exibirConfirmacaoValidade(target);
        }else{
            atualizarLoteAdicionado(target);
        }
    }
    
    private DlgConfirmacaoSimNao dlgConfirmacao;
    private void exibirConfirmacaoValidade(AjaxRequestTarget target){
        if (dlgConfirmacao==null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), BundleManager.getString("validadeInformadaVencida")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    atualizarLoteAdicionado(target);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.focusComponent(txtLote);
                }
            });
        }
        dlgConfirmacao.show(target);
    }
    
    private void atualizarLoteAdicionado(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        MovimentoGrupoEstoqueItemDTO lote = modelLote.getObject();
        
        AbstractSessaoAplicacao sessaoAplicacao = br.com.celk.system.session.ApplicationSession.get().getSession();
        EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO,Deposito.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, sessaoAplicacao.getCodigoEmpresa()))
                .start().getVO();
        
        lote.setEmpresa(sessaoAplicacao.<Empresa>getEmpresa());
        lote.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
        lote.setDeposito(empresaMaterial.getDeposito());
        
        lote.setProduto(produto);
        lotesTemp.add(lote);
        modelLote.setObject(new MovimentoGrupoEstoqueItemDTO());
        tblLotes.update(target);
        limparLote(target);
        target.add(txtTotal);
        target.focusComponent(txtLote);
    }
    
    private void removerLote(AjaxRequestTarget target, MovimentoGrupoEstoqueItemDTO dto){
        lotesTemp.remove(dto);
        tblLotes.update(target);
        target.add(txtTotal);
    }
    
    private void limparLote(AjaxRequestTarget target){
        txtLote.limparLote(target);
        txtQuantidade.limpar(target);
        dchDataValidade.limpar(target);
    }
    
    public void limpar(AjaxRequestTarget target){
        produto = null;
        txtProduto.limpar(target);
        txtLote.limpar(target);
        txtQuantidade.limpar(target);
        dchDataValidade.limpar(target);
        txtTotal.limpar(target);
        lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        lotesTemp = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        tblLotes.update(target);
    }
    
    private Double getQuantidadeTotalInterno(){
        Double total = 0D;
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotesTemp) {
            total = new Dinheiro(total).somar(movimentoGrupoEstoqueItemDTO.getQuantidade()).doubleValue();
        }
        return Coalesce.asDouble(total);
    }
    
    public Double getQuantidadeTotal(){
        Double total = 0D;
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotes) {
            total = new Dinheiro(total).somar(movimentoGrupoEstoqueItemDTO.getQuantidade()).doubleValue();
        }
        return Coalesce.asDouble(total);
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
        txtLote.setProduto(produto);
    }

    public Produto getProduto() {
        return produto;
    }

    public void setLotes(List<MovimentoGrupoEstoqueItemDTO> lotes) {
        this.lotesTemp = lotes;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        txtLote.setUsuarioCadsus(usuarioCadsus);
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
    
    public abstract void onCancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public InputField getTxtLote() {
        return txtLote.getTxtLote();
    }

    public List<MovimentoGrupoEstoqueItemDTO> getLotes() {
        return lotes;
    }

}
