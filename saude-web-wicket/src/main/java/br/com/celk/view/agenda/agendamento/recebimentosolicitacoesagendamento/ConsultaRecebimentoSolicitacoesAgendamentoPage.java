package br.com.celk.view.agenda.agendamento.recebimentosolicitacoesagendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.bo.UnidadeHelper;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaRecebimentoSolicitacoesAgendamentoPage extends ConsultaPage<LoteSolicitacaoAgendamento, List<BuilderQueryCustom.QueryParameter>> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoDestino;
    private Empresa estabelecimento;
    private List<Empresa> estabelecimentoDestino;
    private Long numeroLote;
    private Long situacao;
    private DropDown<Long> dropDownSituacao;

    private PageParameters parameters;

    public ConsultaRecebimentoSolicitacoesAgendamentoPage(PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;    
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEmpresa("estabelecimento", new PropertyModel(this, "estabelecimento")));
        form.add(autoCompleteConsultaEstabelecimentoDestino = new AutoCompleteConsultaEmpresa("estabelecimentoDestino", new PropertyModel(this, "estabelecimentoDestino")));
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEstabelecimentoDestino.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEstabelecimentoDestino.setMultiplaSelecao(true);
        form.add(new InputField<Long>("numeroLote", new PropertyModel(this, "numeroLote")));
        form.add(getDropDownSituacao());

        if(!isPermissaoEmpresa){
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            try {
                estabelecimentoDestino = BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(usuarioLogado));
            } catch (ValidacaoException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }

        getLinkNovo().setVisible(false);

        setExibeExpandir(true);
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField();
    }

    private DropDown getDropDownSituacao() {
        if (dropDownSituacao == null) { 
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("ambos"));

            for (LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento status : LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.values()) {
                if(status.value() != 0L){
                    dropDownSituacao.addChoice(status.value(), status.descricao());
                }
            }
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LoteSolicitacaoAgendamento proxy = on(LoteSolicitacaoAgendamento.class);
        
        columns.add(getActionColumn());
        columns.add(createSortableColumn(BundleManager.getString("nrLote"), proxy.getCodigo()));
        columns.add(createSortableColumn(BundleManager.getString("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(BundleManager.getString("dataEnvio"), proxy.getDataEnvio()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(BundleManager.getString("responsavel"), proxy.getResponsavel()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LoteSolicitacaoAgendamento>() {
            @Override
            public void customizeColumn(LoteSolicitacaoAgendamento rowObject) {
                addAction(ActionType.CURTIR, rowObject, new IModelAction<LoteSolicitacaoAgendamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LoteSolicitacaoAgendamento modelObject) throws ValidacaoException, DAOException {
                        if(UnidadeHelper.isCentralAgendamento()){
                            setResponsePage(new ConfirmacaoRecebimentoSolicitacoesPage(modelObject.getCodigo(), parameters));
                        } else{
                            setResponsePage(new ConfirmacaoRecebimentoSolicitacoesUnidadePage(modelObject.getCodigo(), parameters));
                        }
                    }
                }).setTitleBundleKey("confirmarRecebimento")
                        .setEnabled(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.ENVIADO.value().equals(rowObject.getStatus()));
                 
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<LoteSolicitacaoAgendamento>() {
                    @Override
                    public DataReport action(LoteSolicitacaoAgendamento modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoLoteSolicitacao(modelObject);
                    }
                }).setTitleBundleKey("relatorioEnvio");
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LoteSolicitacaoAgendamento.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LoteSolicitacaoAgendamento.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_CODIGO),
                    VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO),
                    VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_DATA_CADASTRO),
                    VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_STATUS),
                    VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_RESPONSAVEL),
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_CODIGO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA), this.estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA_DESTINO), BuilderQueryCustom.QueryParameter.IN, this.estabelecimentoDestino));

        if (this.numeroLote != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_CODIGO), numeroLote));
        }
        if (this.situacao != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_STATUS), situacao));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaRecebimentoSolicitacoesAgendamentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRecebimentoSolicitacoesAgendamento");
    }

}