package br.com.celk.view.unidadesaude.tipoatendimento.nodoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionRow;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.AnnotationUtils;
import br.com.celk.util.Coalesce;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NodoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaNodosAtendimentoDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.EloNodoTipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.NodoAtendimentoWeb;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.EloProcedimentoClassificacaoTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoClassificacao;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.sortable.SortableBehavior;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private

public class CadastroNodoAtendimentoPage extends CadastroPage<TipoAtendimento> {

    private DropDown<NodesAtendimentoRef> dropDownNodo;
    private DropDown<EloTipoAtendimentoGrupoAtendimentoCbo> dropDownGrupos;
    private DropDown<String> dropDownSexo;
    private DropDown<String> dropDownVisivelUsuariosTemporarios;
    private DropDown<Long> dropDownApenasMunicipio;
    private DropDown<Long> dropDownPermiteCorrecao;
    private DropDown<Long> dropDownExibirAtendimento;
    private DropDown<Long> dropDownPermiteHistorico;
    private DropDown<Long> dropDownPermiteAtendimentoParalelo;
    private DropDown<Convenio> dropDownConvenios;
    private DropDown<Long> dropDownInformarProfissional;
    private DropDown<Long> dropDownInformarEstabelecimentoOrigem;
    private DropDown<Long> dropDownInformarReclassificacao;
    private DropDown<GrupoAtendimentoCbo> dropDownGruposProfissional;
    private DropDown<ProcedimentoClassificacao> dropDownProcedimentoClassificacao;
    private List<NodoAtendimentoWeb> nodos = new ArrayList<NodoAtendimentoWeb>();
    private WebMarkupContainer containerNodos;
    private Map<String, NodesAtendimentoRef> mapNodos = new HashMap<String, NodesAtendimentoRef>();
    private SelectionTable<NodoAtendimentoDTO> tblNodos;
    private Table tblGrupos;
    private Table tblConvenios;
    private Table tblClassificacoes;

    private List<NodoAtendimentoDTO> dtoList = new ArrayList<NodoAtendimentoDTO>();
    private List<EloTipoAtendimentoGrupoAtendimentoCbo> grupoList = new ArrayList<EloTipoAtendimentoGrupoAtendimentoCbo>();
    private List<Convenio> convenioList = new ArrayList<Convenio>();
    private List<EloProcedimentoClassificacaoTipoAtendimento> classificacoes = new ArrayList<EloProcedimentoClassificacaoTipoAtendimento>();

    private NodesAtendimentoRef nodo;
    private EloTipoAtendimentoGrupoAtendimentoCbo grupo;
    private GrupoAtendimentoCbo grupoProfissional;
    private Convenio convenio;
    private ProcedimentoClassificacao procedimentoClassificacao;
    private Long apenasMunicipio;
    private Long flagPermiteReclassificacao;
    private String sexo;
    private String visivelUsuariosTemporarios;
    private Long permiteCorrecao;
    private Long permiteAtendimentoParalelo;
    private Long informarProfissional;
    private Long informarEstabelecimentoOrigem;
    private Long permissaoProfissional;
    private Long exibirAtendimento;
    private Long permiteHistorico;

    private Form<EloNodoTipoExame> containerAtendimentoExame;
    private DropDown dropDownTipoAcao;
    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;
    private AutoCompleteConsultaTipoAtendimento autoCompleteConsultaTipoAtendimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Table tblAtendimentoExame;
    private List<EloNodoTipoExame> lstEloNodoTipoExame = new ArrayList<EloNodoTipoExame>();

    public CadastroNodoAtendimentoPage(TipoAtendimento object) {
        super(object);
        relacionarNodosDisponiveis();
        carregarNodos();
    }

    public CadastroNodoAtendimentoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(new DisabledInputField<String>(TipoAtendimento.PROP_DESCRICAO));

        containerNodos = new WebMarkupContainer("containerNodos", new CompoundPropertyModel(this));
        containerNodos.add(getNodo());
        containerNodos.add(new AbstractAjaxButton("btnAdicionarNodo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarNodo(target);
            }
        });
        containerNodos.add(tblNodos = new SelectionTable("tblNodos", getColumnNodos(), getCollectionProviderNodos()));

        containerNodos.add(getDropDownApenasMunicipio("apenasMunicipio"));
        containerNodos.add(getDropDownSexo("sexo"));
        containerNodos.add(getDropDownPermiteHistorico("permiteHistorico"));
        containerNodos.add(getDropDownVisivelUsuariosTemporarios("visivelUsuariosTemporarios"));
        containerNodos.add(getDropDownExibirAtendimento("exibirAtendimento"));
        containerNodos.add(getDropDownPermiteCorrecao("permiteCorrecao"));

        containerNodos.add(getDropDownPermiteAtendimentoParalelo("permiteAtendimentoParalelo"));
        containerNodos.add(getDropDownInformarProfissional("informarProfissional"));
        containerNodos.add(getDropDownInformarEstabelecimentoOrigem("informarEstabelecimentoOrigem"));
        containerNodos.add(dropDownGruposProfissional = getDropDownGruposProfissional("grupoProfissional"));
        dropDownGruposProfissional.setEnabled(false);

        containerNodos.add(getDropDownGrupos("grupo"));
        containerNodos.add(new AbstractAjaxButton("btnAdicionarGrupo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarGrupo(target);
            }
        });
        containerNodos.add(tblGrupos = new Table("tblGrupos", getColumnGrupos(), getCollectionProviderGrupos()));
        containerNodos.add(dropDownInformarReclassificacao = getDropDownReclassificacao("flagPermiteReclassificacao"));

        tblNodos.populate();
        tblGrupos.populate();

        containerNodos.add(getDropDownConvenios("convenio"));
        containerNodos.add(new AbstractAjaxButton("btnAdicionarConvenio") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarConvenio(target);
            }
        });
        containerNodos.add(tblConvenios = new Table("tblConvenios", getColumnConvenios(), getCollectionProviderConvenios()));
        tblConvenios.populate();

        EloNodoTipoExame on = on(EloNodoTipoExame.class);
        containerNodos.add(containerAtendimentoExame = new Form("containerAtendimentoExame", new CompoundPropertyModel(new EloNodoTipoExame())));
        containerAtendimentoExame.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame(path(on.getTipoExame())));
        containerAtendimentoExame.add(dropDownTipoAcao = DropDownUtil.getIEnumDropDown(path(on.getTipoAcao()), EloNodoTipoExame.Acao.values()));
        dropDownTipoAcao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitaTipoAtendimento(target);
            }
        });
        containerAtendimentoExame.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(on.getEmpresa())));
        containerAtendimentoExame.add(autoCompleteConsultaTipoAtendimento = new AutoCompleteConsultaTipoAtendimento(path(on.getTipoAtendimento())));
        containerAtendimentoExame.add(new AbstractAjaxButton("btnAdicionarAtendimentoExame") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAtendimentoExame(target);
            }
        });
        containerAtendimentoExame.add(tblAtendimentoExame = new Table("tblAtendimentoExame", getColumnAtendimentoExame(), getCollectionProviderAtendimentoExame()));
        tblAtendimentoExame.populate();

        form.add(getDropDownProcedimentoClassificacao("procedimentoClassificacao"));
        form.add(new AbstractAjaxButton("btnAdicionarClassificacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarClassificacao(target);
            }
        });
        form.add(tblClassificacoes = new Table("tblClassificacoes", getColumnClassificacoes(), getCollectionProviderClassificacoes()));
        tblClassificacoes.populate();

        tblNodos.addSelectionAction(new ISelectionAction<NodoAtendimentoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, NodoAtendimentoDTO dtoSelecionado) {
                carregarDados(dtoSelecionado, target);
            }
        });

        SortableBehavior sortableBehavior = new SortableBehavior();
        sortableBehavior.setUpdateEvent(new SortableBehavior.AjaxUpdateCallback() {
            @Override
            public void update(AjaxRequestTarget art, Component cmpnt, int i, Component sortedComponent) {
                SelectionRow<NodoAtendimentoDTO> sr = (SelectionRow<NodoAtendimentoDTO>) sortedComponent;
                dtoList.remove(dtoList.indexOf(sr.getModelObject()));
                dtoList.add(i, sr.getModelObject());
            }
        });
        sortableBehavior.setConnectWith(".connectedSortable");
        sortableBehavior.setAxis(SortableBehavior.AxisEnum.Y);
        tblNodos.getBody().add(sortableBehavior);

        tblNodos.getBody().add(new AttributeModifier("class", "connectedSortable"));

        form.add(containerNodos);
        autoCompleteConsultaTipoAtendimento.setEnabled(false);
    }

    private void adicionarAtendimentoExame(AjaxRequestTarget target) throws ValidacaoException {
        EloNodoTipoExame elo = containerAtendimentoExame.getModelObject();
        if (elo.getTipoExame() == null) {
            throw new ValidacaoException(bundle("selecioneEstabelecimento"));
        }
        if (elo.getEmpresa() == null) {
            throw new ValidacaoException(bundle("selecioneEstabelecimento"));
        }
        if (EloNodoTipoExame.Acao.GERAR_ATENDIMENTO.value().equals(elo.getTipoAcao()) && elo.getTipoAtendimento() == null) {
            throw new ValidacaoException(bundle("selecioneTipoAtendimento"));
        }
        containerAtendimentoExame.setModelObject(new EloNodoTipoExame());
        autoCompleteConsultaEmpresa.limpar(target);
        autoCompleteConsultaTipoAtendimento.limpar(target);
        autoCompleteConsultaTipoAtendimento.setEnabled(false);
        autoCompleteConsultaTipoExame.limpar(target);
        target.add(containerAtendimentoExame);
        target.focusComponent(autoCompleteConsultaTipoExame.getTxtDescricao().getTextField());

        validaExisteAtendimentoExameAdicionado(target, elo);
        lstEloNodoTipoExame.add(elo);
    }

    private void validaExisteAtendimentoExameAdicionado(AjaxRequestTarget target, EloNodoTipoExame elo) throws ValidacaoException {
        for (EloNodoTipoExame item : lstEloNodoTipoExame) {
            if (item.getTipoExame().equals(elo.getTipoExame()) && item.getEmpresa().equals(elo.getEmpresa())) {
                throw new ValidacaoException(bundle("itemJaAdicionado"));
            }
        }

    }

    private void habilitaTipoAtendimento(AjaxRequestTarget target) {
        if (EloNodoTipoExame.Acao.GERAR_ATENDIMENTO.value().equals(dropDownTipoAcao.getComponentValue())) {
            autoCompleteConsultaTipoAtendimento.setEnabled(true);
        } else {
            if (target != null) {
                autoCompleteConsultaTipoAtendimento.limpar(target);
            }
            autoCompleteConsultaTipoAtendimento.setEnabled(false);
        }
        if (target != null) {
            target.add(autoCompleteConsultaTipoAtendimento);
        }
    }

    private DropDown<NodesAtendimentoRef> getNodo() {
        if (dropDownNodo == null) {
            dropDownNodo = new DropDown<NodesAtendimentoRef>("nodo");

            List<NodesAtendimentoRef> list = new ArrayList(Arrays.asList(NodesAtendimentoRef.values()));

            Collections.sort(list, new Comparator<NodesAtendimentoRef>() {
                @Override
                public int compare(NodesAtendimentoRef o1, NodesAtendimentoRef o2) {
                    return o1.toString().compareTo(o2.toString());
                }
            });

            for (NodesAtendimentoRef node : list) {
                dropDownNodo.addChoice(node, node.toString());
            }

        }
        return dropDownNodo;
    }

    private void adicionarGrupo(AjaxRequestTarget target) throws ValidacaoException {
        if (tblNodos.getSelectedObject() == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneNo"));
        }
        if (grupo == null) {
            throw new ValidacaoException(BundleManager.getString("informeGrupo"));
        }
        if (tblNodos.getSelectedObject().getGrupo().contains(grupo)) {
            throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
        }

        tblNodos.getSelectedObject().getGrupo().add(0, grupo);

        tblGrupos.update(target);
        dropDownGrupos.limpar(target);
    }

    private void adicionarConvenio(AjaxRequestTarget target) throws ValidacaoException {
        if (tblNodos.getSelectedObject() == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneNo"));
        }
        if (convenio == null) {
            throw new ValidacaoException(BundleManager.getString("informeConvenio"));
        }
        if (tblNodos.getSelectedObject().getConvenios().contains(convenio)) {
            throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
        }
        tblNodos.getSelectedObject().getConvenios().add(0, convenio);

        tblConvenios.update(target);
        dropDownConvenios.limpar(target);
    }

    private void adicionarClassificacao(AjaxRequestTarget target) throws ValidacaoException {
        if (procedimentoClassificacao == null) {
            throw new ValidacaoException(BundleManager.getString("informeClassificacao"));
        }

        for (EloProcedimentoClassificacaoTipoAtendimento eloProcedimentoClassificacaoTipoAtendimento : classificacoes) {
            if (eloProcedimentoClassificacaoTipoAtendimento.getProcedimentoClassificacao().equals(procedimentoClassificacao)) {
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        EloProcedimentoClassificacaoTipoAtendimento elo = new EloProcedimentoClassificacaoTipoAtendimento();

        elo.setTipoAtendimento(getForm().getModelObject());
        elo.setProcedimentoClassificacao(procedimentoClassificacao);

        classificacoes.add(0, elo);

        tblClassificacoes.update(target);
        dropDownProcedimentoClassificacao.limpar(target);
    }

    private void adicionarNodo(AjaxRequestTarget target) throws ValidacaoException {
        if (nodo == null) {
            throw new ValidacaoException(BundleManager.getString("informeNo"));
        }
        for (NodoAtendimentoDTO nodoAtendimentoDTO : dtoList) {
            if (nodoAtendimentoDTO.getNodo().equals(nodo)) {
                throw new ValidacaoException(BundleManager.getString("itemJaAdicionado"));
            }
        }

        String className = null;
        for (Map.Entry<String, NodesAtendimentoRef> entry : mapNodos.entrySet()) {
            if (entry.getValue().equals(nodo)) {
                className = entry.getKey();
                break;
            }
        }

        NodoAtendimentoDTO dto = new NodoAtendimentoDTO();
        dto.setApenasMunicipio(RepositoryComponentDefault.NAO_LONG);
        dto.setSexo(RepositoryComponentDefault.SEXO_INDIFERENTE);
        dto.setPermiteHistorico(RepositoryComponentDefault.SIM_LONG);
        dto.setVisivelUsuariosTemporarios(RepositoryComponentDefault.NAO);
        dto.setPermiteCorrecao(RepositoryComponentDefault.NAO_LONG);
        dto.setPermiteAtendimentoParalelo(RepositoryComponentDefault.NAO_LONG);
        dto.setFlagPermiteReclassificacao(RepositoryComponentDefault.NAO_LONG);
        dto.setClassName(className);
        dto.setGrupo(new ArrayList<EloTipoAtendimentoGrupoAtendimentoCbo>());
        dto.setConvenios(new ArrayList<Convenio>());
        dto.setNodo(nodo);
        dto.setInformarProfissional(RepositoryComponentDefault.NAO_LONG);
        dto.setInformarEstabelecimentoOrigem(RepositoryComponentDefault.NAO_LONG);

        dtoList.add(0, dto);

        dropDownNodo.limpar(target);
        target.add(tblNodos);
    }

    private void carregarDados(NodoAtendimentoDTO dtoSelecionado, AjaxRequestTarget target) {
        dtoSelecionado.setVisivelUsuariosTemporarios(Coalesce.asString(dtoSelecionado.getVisivelUsuariosTemporarios(), RepositoryComponentDefault.NAO));
        dtoSelecionado.setPermiteCorrecao(Coalesce.asLong(dtoSelecionado.getPermiteCorrecao(), RepositoryComponentDefault.NAO_LONG));
        dtoSelecionado.setPermiteHistorico(Coalesce.asLong(dtoSelecionado.getPermiteHistorico(), RepositoryComponentDefault.SIM_LONG));
        dtoSelecionado.setExibirAtendimento(Coalesce.asLong(dtoSelecionado.getExibirAtendimento(), RepositoryComponentDefault.NAO_LONG));
        dtoSelecionado.setPermiteAtendimentoParalelo(Coalesce.asLong(dtoSelecionado.getPermiteAtendimentoParalelo(), RepositoryComponentDefault.NAO_LONG));
        dtoSelecionado.setFlagPermiteReclassificacao(Coalesce.asLong(dtoSelecionado.getFlagPermiteReclassificacao(), RepositoryComponentDefault.NAO_LONG));

        sexo = dtoSelecionado.getSexo();
        permiteHistorico = dtoSelecionado.getPermiteHistorico();
        apenasMunicipio = dtoSelecionado.getApenasMunicipio();
        visivelUsuariosTemporarios = dtoSelecionado.getVisivelUsuariosTemporarios();
        permiteCorrecao = dtoSelecionado.getPermiteCorrecao();
        exibirAtendimento = dtoSelecionado.getExibirAtendimento();
        permiteAtendimentoParalelo = dtoSelecionado.getPermiteAtendimentoParalelo();
        informarProfissional = dtoSelecionado.getInformarProfissional();
        informarEstabelecimentoOrigem = dtoSelecionado.getInformarEstabelecimentoOrigem();
        flagPermiteReclassificacao = dtoSelecionado.getFlagPermiteReclassificacao();
        grupoProfissional = dtoSelecionado.getGrupoProfissional();
        grupoList = dtoSelecionado.getGrupo();
        convenioList = dtoSelecionado.getConvenios();
        lstEloNodoTipoExame = dtoSelecionado.getLstEloNodoTipoExame();
        tblGrupos.update(target);
        tblConvenios.update(target);
        tblAtendimentoExame.update(target);

        if (RepositoryComponentDefault.SIM_LONG.equals(informarProfissional)) {
            dropDownGruposProfissional.setEnabled(true);
        } else {
            dropDownGruposProfissional.setEnabled(false);
        }
        dropDownExibirAtendimento.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(permiteCorrecao));
        dropDownPermiteHistorico.setEnabled(NodesAtendimentoRef.EVOLUCAO_PROCEDIMENTO.equals(tblNodos.getSelectedObject().getNodo()) || NodesAtendimentoRef.EVOLUCAO_PROCEDIMENTO.equals(nodo));

        target.add(dropDownApenasMunicipio);
        target.add(dropDownSexo);
        target.add(dropDownPermiteHistorico);
        target.add(dropDownVisivelUsuariosTemporarios);
        target.add(dropDownPermiteCorrecao);
        target.add(dropDownExibirAtendimento);
        target.add(dropDownPermiteAtendimentoParalelo);
        target.add(dropDownInformarProfissional);
        target.add(dropDownInformarEstabelecimentoOrigem);
        target.add(dropDownInformarReclassificacao);
        target.add(dropDownGruposProfissional);
    }

    private DropDown getDropDownSexo(String id) {
        if (dropDownSexo == null) {
            dropDownSexo = new DropDown<String>(id);

            dropDownSexo.addChoice(RepositoryComponentDefault.SEXO_INDIFERENTE, bundle("ambos"));
            dropDownSexo.addChoice(RepositoryComponentDefault.SEXO_MASCULINO, bundle("masculino"));
            dropDownSexo.addChoice(RepositoryComponentDefault.SEXO_FEMININO, bundle("feminino"));

            dropDownSexo.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setSexo(dropDownSexo.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }
        return dropDownSexo;
    }

    private DropDown getDropDownPermiteAtendimentoParalelo(String id) {
        if (dropDownPermiteAtendimentoParalelo == null) {
            dropDownPermiteAtendimentoParalelo = DropDownUtil.getNaoSimLongDropDown(id);

            dropDownPermiteAtendimentoParalelo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setPermiteAtendimentoParalelo(dropDownPermiteAtendimentoParalelo.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }

        return dropDownPermiteAtendimentoParalelo;
    }

    private DropDown getDropDownPermiteCorrecao(String id) {
        if (dropDownPermiteCorrecao == null) {
            dropDownPermiteCorrecao = DropDownUtil.getNaoSimLongDropDown(id);

            dropDownPermiteCorrecao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {

                    if (dropDownPermiteCorrecao.getComponentValue() != null) {
                        dropDownExibirAtendimento.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(dropDownPermiteCorrecao.getComponentValue()));
                        target.add(dropDownExibirAtendimento);
                    }

                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setPermiteCorrecao(dropDownPermiteCorrecao.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }

        return dropDownPermiteCorrecao;
    }
    private DropDown getDropDownPermiteHistorico(String id) {
        if (dropDownPermiteHistorico == null) {
            dropDownPermiteHistorico = DropDownUtil.getNaoSimLongDropDown(id);

            dropDownPermiteHistorico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setPermiteHistorico(dropDownPermiteHistorico.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }
        return dropDownPermiteHistorico;
    }

    private DropDown getDropDownExibirAtendimento(String id) {
        if (dropDownExibirAtendimento == null) {
            dropDownExibirAtendimento = DropDownUtil.getNaoSimLongDropDown(id);
        }
        dropDownExibirAtendimento.add(new Tooltip().setText("defineSeNoSeraExibidoRealizacaoAtendimento"));

        dropDownExibirAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (tblNodos.getSelectedObject() != null) {
                    tblNodos.getSelectedObject().setExibirAtendimento(dropDownExibirAtendimento.getComponentValue());
                    target.add(tblNodos);
                }
            }

        });

        dropDownExibirAtendimento.setEnabled(false);

        return dropDownExibirAtendimento;
    }

    private DropDown getDropDownVisivelUsuariosTemporarios(String id) {
        if (dropDownVisivelUsuariosTemporarios == null) {
            dropDownVisivelUsuariosTemporarios = DropDownUtil.getNaoSimDropDown(id);

            dropDownVisivelUsuariosTemporarios.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setVisivelUsuariosTemporarios(dropDownVisivelUsuariosTemporarios.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }

        return dropDownVisivelUsuariosTemporarios;
    }

    private DropDown getDropDownApenasMunicipio(String id) {
        if (dropDownApenasMunicipio == null) {
            dropDownApenasMunicipio = new DropDown<Long>(id);

            dropDownApenasMunicipio.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownApenasMunicipio.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));

            dropDownApenasMunicipio.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setApenasMunicipio(dropDownApenasMunicipio.getComponentValue());
                        target.add(tblNodos);
                    }
                }

            });
        }
        return dropDownApenasMunicipio;
    }

    private DropDown getDropDownGrupos(String id) {
        if (dropDownGrupos == null) {
            dropDownGrupos = new DropDown<EloTipoAtendimentoGrupoAtendimentoCbo>(id);
            List<EloTipoAtendimentoGrupoAtendimentoCbo> eloGrupos = LoadManager.getInstance(EloTipoAtendimentoGrupoAtendimentoCbo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EloTipoAtendimentoGrupoAtendimentoCbo.PROP_TIPO_ATENDIMENTO, getForm().getModelObject()))
                    .start().getList();

            dropDownGrupos.addChoice(null, "");
            for (EloTipoAtendimentoGrupoAtendimentoCbo elo : eloGrupos) {
                dropDownGrupos.addChoice(elo, elo.getGrupoAtendimentoCbo().getDescricao());
            }
        }
        return dropDownGrupos;
    }

    private DropDown getDropDownGruposProfissional(String id) {
        if (dropDownGruposProfissional == null) {
            dropDownGruposProfissional = new DropDown<GrupoAtendimentoCbo>(id);
            List<GrupoAtendimentoCbo> grupos = LoadManager.getInstance(GrupoAtendimentoCbo.class)
                    .start().getList();

            dropDownGruposProfissional.addChoice(null, "");
            for (GrupoAtendimentoCbo grupo : grupos) {
                dropDownGruposProfissional.addChoice(grupo, grupo.getDescricao());
            }

            dropDownGruposProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    tblNodos.getSelectedObject().setGrupoProfissional(dropDownGruposProfissional.getComponentValue());
                    target.add(tblNodos);
                }
            });
        }
        return dropDownGruposProfissional;
    }

    private DropDown getDropDownConvenios(String id) {
        if (dropDownConvenios == null) {
            dropDownConvenios = new DropDown<Convenio>(id);
            List<Convenio> convenios = LoadManager.getInstance(Convenio.class)
                    .start().getList();

            dropDownConvenios.addChoice(null, "");
            for (Convenio _convenio : convenios) {
                dropDownConvenios.addChoice(_convenio, _convenio.getDescricao());
            }
        }
        return dropDownConvenios;
    }

    private DropDown getDropDownProcedimentoClassificacao(String id) {
        if (dropDownProcedimentoClassificacao == null) {
            dropDownProcedimentoClassificacao = new DropDown<ProcedimentoClassificacao>(id, new PropertyModel(this, "procedimentoClassificacao"));
            List<ProcedimentoClassificacao> _classificacoes = LoadManager.getInstance(ProcedimentoClassificacao.class)
                    .start().getList();

            dropDownProcedimentoClassificacao.addChoice(null, "");
            for (ProcedimentoClassificacao _classificacao : _classificacoes) {
                dropDownProcedimentoClassificacao.addChoice(_classificacao, _classificacao.getDescricao());
            }
        }
        return dropDownProcedimentoClassificacao;
    }

    private DropDown getDropDownInformarProfissional(String id) {
        if (dropDownInformarProfissional == null) {
            dropDownInformarProfissional = new DropDown<Long>(id);

            dropDownInformarProfissional.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownInformarProfissional.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));

            dropDownInformarProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setInformarProfissional(dropDownInformarProfissional.getComponentValue());

                        if (RepositoryComponentDefault.NAO_LONG.equals(dropDownInformarProfissional.getComponentValue())) {
                            tblNodos.getSelectedObject().setGrupoProfissional(null);
                            grupoProfissional = null;
                            dropDownGruposProfissional.setEnabled(false);
                        } else {
                            dropDownGruposProfissional.setEnabled(true);
                        }
                    } else {
                        grupoProfissional = null;
                        dropDownGruposProfissional.setEnabled(false);
                    }
                    target.add(tblNodos);
                    target.add(dropDownGruposProfissional);
                }
            });
        }
        return dropDownInformarProfissional;
    }

    private DropDown getDropDownInformarEstabelecimentoOrigem(String id) {
        if (dropDownInformarEstabelecimentoOrigem == null) {
            dropDownInformarEstabelecimentoOrigem = new DropDown<Long>(id);

            dropDownInformarEstabelecimentoOrigem.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownInformarEstabelecimentoOrigem.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));

            dropDownInformarEstabelecimentoOrigem.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setInformarEstabelecimentoOrigem(dropDownInformarEstabelecimentoOrigem.getComponentValue());
                    }
                    target.add(tblNodos);
                    target.add(dropDownInformarEstabelecimentoOrigem);
                }
            });
        }
        return dropDownInformarEstabelecimentoOrigem;
    }

    private DropDown getDropDownReclassificacao(String id) {
        if (dropDownInformarReclassificacao == null) {
            dropDownInformarReclassificacao = new DropDown<Long>(id);

            dropDownInformarReclassificacao.addChoice(RepositoryComponentDefault.NAO_LONG, bundle("nao"));
            dropDownInformarReclassificacao.addChoice(RepositoryComponentDefault.SIM_LONG, bundle("sim"));
            dropDownInformarReclassificacao.add(new Tooltip().setText("defineNoTipoAtendimentoReclassificacao"));
            dropDownInformarReclassificacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (tblNodos.getSelectedObject() != null) {
                        tblNodos.getSelectedObject().setFlagPermiteReclassificacao(dropDownInformarReclassificacao.getComponentValue());
                    }
                    target.add(tblNodos);
                    target.add(dropDownInformarReclassificacao);
                }
            });
        }
        return dropDownInformarReclassificacao;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return dropDownNodo;
    }

    @Override
    public Class<TipoAtendimento> getReferenceClass() {
        return TipoAtendimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaNodoAtendimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("configurarTipoAtendimento");
    }

    private void carregarNodos() {
        try {
            QueryConsultaNodosAtendimentoDTOParam param = new QueryConsultaNodosAtendimentoDTOParam();

            param.setTipoAtendimento(getForm().getModelObject());
            dtoList = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarNodosAtendimento(param);

            for (NodoAtendimentoDTO nodoAtendimentoDTO : dtoList) {
                nodoAtendimentoDTO.setNodo(mapNodos.get(nodoAtendimentoDTO.getClassName()));
            }

            classificacoes = LoadManager.getInstance(EloProcedimentoClassificacaoTipoAtendimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EloProcedimentoClassificacaoTipoAtendimento.PROP_TIPO_ATENDIMENTO, getForm().getModelObject()))
                    .start().getList();

        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public Object salvar(TipoAtendimento tipoAtendimento) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).configurarTipoAtendimento(tipoAtendimento, dtoList, classificacoes);
        return null;
    }

    private List<IColumn> getColumnGrupos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EloTipoAtendimentoGrupoAtendimentoCbo dto = on(EloTipoAtendimentoGrupoAtendimentoCbo.class);

        columns.add(getCustomColumnGrupos());
        columns.add(createColumn(bundle("descricao"), dto.getGrupoAtendimentoCbo().getDescricao()));
        return columns;
    }

    private IColumn getCustomColumnGrupos() {
        return new MultipleActionCustomColumn<EloTipoAtendimentoGrupoAtendimentoCbo>() {
            @Override
            public void customizeColumn(EloTipoAtendimentoGrupoAtendimentoCbo rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EloTipoAtendimentoGrupoAtendimentoCbo>() {

                    @Override
                    public void action(AjaxRequestTarget target, EloTipoAtendimentoGrupoAtendimentoCbo modelObject) throws ValidacaoException, DAOException {
                        grupoList.remove(modelObject);
                        tblNodos.getSelectedObject().setGrupo(grupoList);
                        tblGrupos.update(target);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnConvenios() {
        List<IColumn> columns = new ArrayList<IColumn>();
        Convenio on = on(Convenio.class);

        columns.add(getCustomColumnConvenios());
        columns.add(createColumn(bundle("descricao"), on.getDescricao()));
        return columns;
    }

    private IColumn getCustomColumnConvenios() {
        return new MultipleActionCustomColumn<Convenio>() {
            @Override
            public void customizeColumn(Convenio rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Convenio>() {

                    @Override
                    public void action(AjaxRequestTarget target, Convenio modelObject) throws ValidacaoException, DAOException {
                        convenioList.remove(modelObject);
                        tblNodos.getSelectedObject().setConvenios(convenioList);
                        tblConvenios.update(target);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnAtendimentoExame() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EloNodoTipoExame on = on(EloNodoTipoExame.class);

        columns.add(getCustomColumnAtendimentoExame());
        columns.add(createColumn(bundle("tipoExame"), on.getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("acao"), on.getDescricaoAcao()));
        columns.add(createColumn(bundle("estabelecimento"), on.getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("tipoAtendimento"), on.getTipoAtendimento().getDescricao()));
        return columns;
    }

    private IColumn getCustomColumnAtendimentoExame() {
        return new MultipleActionCustomColumn<EloNodoTipoExame>() {
            @Override
            public void customizeColumn(EloNodoTipoExame rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EloNodoTipoExame>() {

                    @Override
                    public void action(AjaxRequestTarget target, EloNodoTipoExame modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < lstEloNodoTipoExame.size(); i++) {
                            if (lstEloNodoTipoExame.get(i) == modelObject) {
                                lstEloNodoTipoExame.remove(i);
                            }
                        }
                        tblAtendimentoExame.update(target);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnClassificacoes() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EloProcedimentoClassificacaoTipoAtendimento on = on(EloProcedimentoClassificacaoTipoAtendimento.class);

        columns.add(getCustomColumnClassificacoes());
        columns.add(createColumn(bundle("descricao"), on.getProcedimentoClassificacao().getDescricao()));
        return columns;
    }

    private IColumn getCustomColumnClassificacoes() {
        return new MultipleActionCustomColumn<EloProcedimentoClassificacaoTipoAtendimento>() {
            @Override
            public void customizeColumn(EloProcedimentoClassificacaoTipoAtendimento rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EloProcedimentoClassificacaoTipoAtendimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, EloProcedimentoClassificacaoTipoAtendimento modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < classificacoes.size(); i++) {
                            if (classificacoes.get(i) == modelObject) {
                                classificacoes.remove(i);
                            }
                        }
                        tblClassificacoes.update(target);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnNodos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        NodoAtendimentoDTO dto = on(NodoAtendimentoDTO.class);

        columns.add(getCustomColumnNode());
        columns.add(createColumn(bundle("descricao"), dto.getNodo()));
        columns.add(createColumn(bundle("apenasPacienteMunicipio"), dto.getApenasMunicipioFormatado()));
        columns.add(createColumn(bundle("sexo"), dto.getSexoFormatado()));
        columns.add(createColumn(bundle("visivelUsuariosTemporarios"), dto.getVisivelUsuariosTemporariosFormatado()));
        columns.add(createColumn(bundle("permiteCorrecao"), dto.getPermiteCorrecaoFormatado()));
        columns.add(createColumn(bundle("exibirAtendimento"), dto.getExibirAtendimentoFormatado()));
        columns.add(createColumn(bundle("permiteAtendimentoParalelo"), dto.getPermiteAtendimentoParaleloFormatado()));
        columns.add(createColumn(bundle("permiteReclassificacao"), dto.getFlagPermiteReclassificacaoDescricao()));
        columns.add(createColumn(bundle("informarProfissional"), dto.getInformarProfissionalFormatado()));

        return columns;
    }

    private IColumn getCustomColumnNode() {
        return new MultipleActionCustomColumn<NodoAtendimentoDTO>() {
            @Override
            public void customizeColumn(final NodoAtendimentoDTO rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dtoList.remove(rowObject);
                        target.add(tblNodos);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderGrupos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return grupoList;
            }
        };
    }

    private ICollectionProvider getCollectionProviderConvenios() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return convenioList;
            }
        };
    }

    private ICollectionProvider getCollectionProviderAtendimentoExame() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstEloNodoTipoExame;
            }
        };
    }

    private ICollectionProvider getCollectionProviderClassificacoes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return classificacoes;
            }
        };
    }

    private ICollectionProvider getCollectionProviderNodos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dtoList;
            }
        };
    }

    private void relacionarNodosDisponiveis() {
        List<Class> classList = AnnotationUtils.findClasses(ProntuarioNode.class, "br/com/celk/view/atendimento/prontuario/nodes");
        for (Class clazz : classList) {
            ProntuarioNode pn = (ProntuarioNode) clazz.getAnnotation(ProntuarioNode.class);
            mapNodos.put(clazz.getName(), pn.value());
        }
    }

}
