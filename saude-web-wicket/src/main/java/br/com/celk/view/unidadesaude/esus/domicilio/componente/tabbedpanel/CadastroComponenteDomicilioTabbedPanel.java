package br.com.celk.view.unidadesaude.esus.domicilio.componente.tabbedpanel;

import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.esus.domicilio.CadastroDomicilioDTO;
import br.com.celk.esus.domicilio.ComponenteDomicilioDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.celk.view.cadsus.usuariocadsus.CadastroUsuarioCidadaoDTO;
import br.com.celk.view.unidadesaude.esus.domicilio.CadastroDomicilioEsusPage;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.*;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

/**
 *
 * <AUTHOR>
 */
public class CadastroComponenteDomicilioTabbedPanel extends CadastroTabbedPanel<CadastroUsuarioCidadaoDTO> implements IAppletAction {

    private CadastroDomicilioDTO cadastroDomicilioDTO;

    public CadastroComponenteDomicilioTabbedPanel(String id, CadastroUsuarioCidadaoDTO object, boolean viewOnly, List<ITab> tabs, CadastroDomicilioDTO cadastroDomicilioDTO) {
        super(id, object, viewOnly, tabs, true);
        this.cadastroDomicilioDTO = cadastroDomicilioDTO;
    }

    public CadastroComponenteDomicilioTabbedPanel(String id, CadastroUsuarioCidadaoDTO object, List<ITab> tabs, CadastroDomicilioDTO cadastroDomicilioDTO) {
        super(id, object, tabs);
        this.cadastroDomicilioDTO = cadastroDomicilioDTO;
    }

    public CadastroComponenteDomicilioTabbedPanel(String id, List<ITab> tabs, CadastroDomicilioDTO cadastroDomicilioDTO) {
        super(id, tabs);
        this.cadastroDomicilioDTO = cadastroDomicilioDTO;
    }

    @Override
    public Class<CadastroUsuarioCidadaoDTO> getReferenceClass() {
        return CadastroUsuarioCidadaoDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }

    @Override
    protected Component newButtonBar(String id) {
        return new CadastroComponenteDomicilioTabbedPanelBar(id, this);
    }

    @Override
    public Object salvar(CadastroUsuarioCidadaoDTO object) throws DAOException, ValidacaoException {
        UsuarioCadsusEnderecoDTO dto = new UsuarioCadsusEnderecoDTO();

        object.getUsuarioCadsus().setFlagDocumento(RepositoryComponentDefault.SIM);
        object.getUsuarioCadsusEsus().setUsuarioCadsus(object.getUsuarioCadsus());
        dto.setUsuarioCadsusEsus(object.getUsuarioCadsusEsus());
        dto.setCartoes(object.getCartoes());

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsus().getFlagResponsavelFamiliar())) {
            object.getUsuarioCadsus().setResponsavelFamiliar(object.getUsuarioCadsus());
        }

        //cpf
        ArrayList<UsuarioCadsusDocumento> listaDocs = new ArrayList<UsuarioCadsusDocumento>();
        if (object.getUsuarioCadsus().getCpf() != null) {
            String cpfDigits = StringUtils.trimToNull(StringUtil.getDigits(object.getUsuarioCadsus().getCpf()));
            if (cpfDigits != null) {
                object.setDocumentoCpf(new UsuarioCadsusDocumento());
                object.getDocumentoCpf().setNumeroDocumento(cpfDigits);
                object.getDocumentoCpf().setUsuarioCadsus(object.getUsuarioCadsus());
                object.getDocumentoCpf().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF));
                listaDocs.add(object.getDocumentoCpf());
            }
        }

        //rg
        if ((StringUtils.trimToNull(object.getDocumentoIdentidade().getNumeroDocumento()) != null
                || StringUtils.trimToNull(object.getDocumentoIdentidade().getSiglaUf()) != null
                || object.getDocumentoIdentidade().getDataEmissao() != null
                || object.getDocumentoIdentidade().getOrgaoEmissor() != null)) {

            object.getUsuarioCadsus().setRg(StringUtils.trimToNull(object.getDocumentoIdentidade().getNumeroDocumento()));
            object.getDocumentoIdentidade().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG));
            object.getDocumentoIdentidade().setUsuarioCadsus(object.getUsuarioCadsus());
            listaDocs.add(object.getDocumentoIdentidade());
        }

        //certidão
        if ((StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroFolha()) != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroLivro()) != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroCartorio()) != null
                || object.getDocumentoCertidao().getDataEmissao() != null
                || StringUtils.trimToNull(object.getDocumentoCertidao().getNumeroTermo()) != null)) {

            object.getDocumentoCertidao().setUsuarioCadsus(object.getUsuarioCadsus());
            listaDocs.add(object.getDocumentoCertidao());
        }

        //pis
        if (StringUtils.trimToNull(object.getDocumentoPis().getNumeroDocumento()) != null) {
            object.getDocumentoPis().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_PIS));
            object.getDocumentoPis().setUsuarioCadsus(object.getUsuarioCadsus());
            listaDocs.add(object.getDocumentoPis());
        }

        if (StringUtils.trimToNull(object.getDocumentoEleitor().getNumeroDocumento()) != null) {
            object.getDocumentoEleitor().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_TITULO_ELEITOR));
            object.getDocumentoEleitor().setUsuarioCadsus(object.getUsuarioCadsus());
            listaDocs.add(object.getDocumentoEleitor());
        }

        dto.setDocumentos(listaDocs);

        List<Long> tipoDocumentosList = new ArrayList<Long>();
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CPF);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO);
        tipoDocumentosList.add(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO);

        dto.setTipoDocumentos(tipoDocumentosList);

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsus().getFlagResponsavelFamiliar())) {
            if (CollectionUtils.isAllEmpty(object.getCartoes()) && StringUtils.isEmpty(object.getUsuarioCadsus().getCpf())) {
                throw new ValidacaoException(bundle("msgResponsavelSemCnsDefinido"));
            }
        }

        UsuarioCadsusCns usuarioCadsusCns = selectFirst(object.getCartoes(), having(on(UsuarioCadsusCns.class).getExcluido(), not(equalTo(RepositoryComponentDefault.EXCLUIDO))));

        if(Util.isNull(usuarioCadsusCns) && StringUtils.isEmpty(object.getUsuarioCadsus().getCpf())) {
            throw new ValidacaoException(bundle("msgComponenteSemCnsDefinido"));
        }
        UsuarioCadsusHelper.validatePhoneForBrazilianOrNaturalized(object.getUsuarioCadsus());

        UsuarioCadsus usuarioCadsus = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).cadastrarComponenteDomicilio(dto);

        boolean contains = false;
        for (ComponenteDomicilioDTO componenteDomicilioDTO : this.cadastroDomicilioDTO.getComponentes()) {
            if (componenteDomicilioDTO.getUsuarioCadsusDomicilio().getUsuarioCadsus().equals(usuarioCadsus)) {
                usuarioCadsus.setRendaFamiliar(componenteDomicilioDTO.getUsuarioCadsusDomicilio().getUsuarioCadsus().getRendaFamiliar());
                usuarioCadsus.setResideDesde(componenteDomicilioDTO.getUsuarioCadsusDomicilio().getUsuarioCadsus().getResideDesde());
                componenteDomicilioDTO.getUsuarioCadsusDomicilio().setUsuarioCadsus(usuarioCadsus);
                contains = true;
            }
        }

        if (!contains) {
            ComponenteDomicilioDTO componenteDomicilioDTO = new ComponenteDomicilioDTO();
            UsuarioCadsusDomicilio usuarioCadsusDomicilio = new UsuarioCadsusDomicilio();
            usuarioCadsusDomicilio.setUsuarioCadsus(usuarioCadsus);
            componenteDomicilioDTO.setUsuarioCadsusDomicilio(usuarioCadsusDomicilio);
            this.cadastroDomicilioDTO.getComponentes().add(componenteDomicilioDTO);
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getPossuiDeficiencia())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getDeficienciaAuditiva())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getDeficienciaIntelectual())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getDeficienciaVisual())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getDeficienciaFisica())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getDeficienciaOutra())) {
                throw new ValidacaoException(bundle("selecioneDeficiencia"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getUtilizaProtese())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getProteseAuditiva())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getProteseMembrosSuperiores())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getProteseMembrosInferiores())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getProteseCadeiraRodas())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getProteseOutros())) {
                throw new ValidacaoException(bundle("selecioneProtese"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getDoencaRespiratoria())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRespiratoriaAsma())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRespiratoriaEfisema())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRespiratoriaNaoSabe())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRespiratoriaOutros())) {
                throw new ValidacaoException(bundle("selecioneDoencaRespiratoria"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getDoencaCardiaca())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getCardiacaInsuficiencia())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getCardiacaNaoSabe())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getCardiacaOutros())) {
                throw new ValidacaoException(bundle("selecioneDoencaCardiaca"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getDoencaRins())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRinsInsuficiencia())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRinsNaoSabe())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getRinsOutros())) {
                throw new ValidacaoException(bundle("selecioneDoencaRins"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getUsaPlantasMedicinais())) {
            if (object.getUsuarioCadsusEsus().getQuaisPlantas() == null || object.getUsuarioCadsusEsus().getQuaisPlantas().isEmpty()) {
                throw new ValidacaoException(bundle("informeQuaisPlantas"));
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getSituacaoRua())
                && RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsusEsus().getAcessoHigienePessoal())) {
            if (RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getHigieneBanho())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getHigieneSanitario())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getHigieneBucal())
                    && RepositoryComponentDefault.NAO_LONG.equals(object.getUsuarioCadsusEsus().getHigieneOutros())) {
                throw new ValidacaoException(bundle("selecioneAcessoHigienePessoal"));
            }
        }

        return usuarioCadsus;
    }

    @Override
    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Object returnObject = salvar(object);

        Page page = new CadastroDomicilioEsusPage(cadastroDomicilioDTO, CadastroDomicilioEsusPage.TAB_COMPONENTES);
        setResponsePage(page);

        getSession().getFeedbackMessages().info(page, getMsgSalvo(returnObject));
    }

    @Override
    public void search(AjaxRequestTarget target, String key) {
    }

    @Override
    public void register(AjaxRequestTarget target, String key) {
        object.getUsuarioCadsus().setChaveBiometria(key);
    }

    public CadastroDomicilioDTO getCadastroDomicilioDTO() {
        return cadastroDomicilioDTO;
    }
}
