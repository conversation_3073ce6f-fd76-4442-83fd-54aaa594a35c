package br.com.celk.view.materiais.dispensacao.item;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItemHelper;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAdicionarItemControlaUnicoLote extends Panel implements IAdicionarItemPanel {

    private CompoundPropertyModel<DispensacaoMedicamentoItem> model;
    private InputField txtDuracaoDias;
    private DoubleField txtPosologia;
    private InputField<Double> txtQuantidadeDispensada;
    private InputField txtQuantidadePrescrita;
    private Form form;
    private AbstractAjaxButton btnAdicionar;
    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnCalcularQuantidade;
    private DlgCalculaQuantidadePrescrita dlgCalculaQuantidadePrescrita;
    private DropDown cbxTipoUso;
    private List<MovimentoGrupoEstoqueItemDTO> lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO;
    private DispensacaoMedicamentoItem _dispensacaoMedicamentoItem;


    public PnlAdicionarItemControlaUnicoLote(String id, DlgCalculaQuantidadePrescrita dlgCalculaQuantidadePrescrita, MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO) {
        super(id);
        this.dlgCalculaQuantidadePrescrita = dlgCalculaQuantidadePrescrita;
        this.movimentoGrupoEstoqueItemDTO = movimentoGrupoEstoqueItemDTO;
        init();
    }

    private void init() {
        form = new Form("form", model = new CompoundPropertyModel<DispensacaoMedicamentoItem>(new DispensacaoMedicamentoItem()));

        form.setOutputMarkupId(true);

        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));

        WebMarkupContainer containerLote = new WebMarkupContainer("containerLote", new CompoundPropertyModel<MovimentoGrupoEstoqueItemDTO>(movimentoGrupoEstoqueItemDTO));
        MovimentoGrupoEstoqueItemDTO proxy = Lambda.on(MovimentoGrupoEstoqueItemDTO.class);
        containerLote.add(new DisabledInputField(path(proxy.getGrupoEstoque())));
        containerLote.add(new DisabledInputField(path(proxy.getDataValidade())));
        containerLote.add(new DisabledInputField(path(proxy.getEstoqueDisponivel())));

        form.add(containerLote);

        form.add(txtQuantidadePrescrita = new InputField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_QUANTIDADE_PRESCRITA)));
        form.add(new Label("lblDescricaoUnidade", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                if (model.getObject().getReceituarioItem() != null && model.getObject().getReceituarioItem().getUnidade() != null) {
                    return model.getObject().getReceituarioItem().getUnidade().getDescricao();
                } else {
                    return "";
                }
            }
        }));
        form.add(new DisabledInputField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_QTDADE_MG_ML)));
        form.add(btnCalcularQuantidade = new AbstractAjaxButton("btnCalcularQuantidade") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgCalculaQuantidadePrescrita.show(target);
            }
        });
        form.add(txtPosologia = new DoubleField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_POSOLOGIA)));
        form.add(new Label("lblUnidade", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return DispensacaoMedicamentoItemHelper.getDescricaoUnidadePosologia(model.getObject().getProduto());
            }
        }));
        form.add(getCbxTipoUso());
        form.add(txtDuracaoDias = new DisabledInputField("duracaoDias", new LoadableDetachableModel() {
            @Override
            protected Object load() {
                Long qtdadeDias = 0L;
                if (model.getObject().getProduto() != null && Coalesce.asDouble(model.getObject().getCoalesceQuantidadeDispensada()) > 0 && Coalesce.asDouble(model.getObject().getPosologia()) > 0) {
                    qtdadeDias = DispensacaoMedicamentoItemHelper.getQuantidadeDias(model.getObject().getProduto(), model.getObject().getCoalesceQuantidadeDispensada(), model.getObject().getPosologia(), model.getObject().getProduto().getQtdadeMgMl(), model.getObject().getTipoUso());
                }
                return qtdadeDias;
            }
        }));
        form.add(txtQuantidadeDispensada = new InputField(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA)));

        form.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        btnFechar.setDefaultFormProcessing(false);

        add(form);

        txtPosologia.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.add(txtDuracaoDias);
            }
        });
        txtQuantidadeDispensada.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.add(txtDuracaoDias);
            }
        });
    }

    private DropDown getCbxTipoUso() {
        cbxTipoUso = DropDownUtil.getIEnumDropDown("tipoUso", DispensacaoMedicamentoItem.TipoUso.values());

        cbxTipoUso.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.add(txtDuracaoDias);
            }
        });

        return cbxTipoUso;
    }

    @Override
    public void update(AjaxRequestTarget target) {
        DispensacaoMedicamentoItem dmi = model.getObject();
        model.setObject(new DispensacaoMedicamentoItem());
        cbxTipoUso.limpar(target);
        model.setObject(dmi);
        target.add(form);
        target.focusComponent(txtQuantidadeDispensada);
    }

    @Override
    public void setObject(AjaxRequestTarget target, DispensacaoMedicamentoItem dispensacaoMedicamentoItem, boolean edicao) {
        lotes.clear();
        _dispensacaoMedicamentoItem = dispensacaoMedicamentoItem;
        DispensacaoMedicamentoItem dmi = (DispensacaoMedicamentoItem) SerializationUtils.clone(dispensacaoMedicamentoItem);
        model.setObject(dmi);
        dlgCalculaQuantidadePrescrita.setModelObject(dmi);
        lotes = dmi.getMovimentoGrupoEstoqueItemDTOList();
        boolean enableReceituario = !(_dispensacaoMedicamentoItem.getDispensacaoMedicamento().getReceituario() != null && edicao);
        txtQuantidadePrescrita.setEnabled(enableReceituario);
        if (DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(dmi.getProduto().getFlagDispensacaoEspecial())) {
            txtPosologia.setEnabled(false);
            cbxTipoUso.setEnabled(false);
            btnCalcularQuantidade.setEnabled(false);
        } else {
            txtPosologia.setEnabled(enableReceituario);
            cbxTipoUso.setEnabled(enableReceituario);
            btnCalcularQuantidade.setEnabled(enableReceituario);
        }
        target.add(txtDuracaoDias);
        target.add(form);
        if (lotes == null) {
            lotes = new ArrayList();
        }
    }

//    public void setMovimentoGrupoEstoqueItemDTO(MovimentoGrupoEstoqueItemDTO dto) {
//        this.movimentoGrupoEstoqueItemDTO = dto;
//    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        DispensacaoMedicamentoItem dmi = model.getObject();

        if (Coalesce.asDouble(dmi.getQuantidadePrescrita()) < 1D )  {
            throw new ValidacaoException(BundleManager.getString("msgInformeQuantidadePrescrita"));
        }

        if (Coalesce.asDouble(dmi.getQuantidadeDispensada()) < 1D )  {
            throw new ValidacaoException(BundleManager.getString("informeQuantidadeDispensar"));
        }

        movimentoGrupoEstoqueItemDTO.setQuantidade(dmi.getQuantidadeDispensada());

        if (dmi.getQuantidadeDispensada() > movimentoGrupoEstoqueItemDTO.getEstoqueDisponivel()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_maior_disponivel"));
        }

        if (CollectionUtils.isNotNullEmpty(lotes)) {
            lotes.set(0, movimentoGrupoEstoqueItemDTO);
        } else {
            lotes.add(movimentoGrupoEstoqueItemDTO);
        }

        dmi.setMovimentoGrupoEstoqueItemDTOList(lotes);
        adicionar(target, dmi, _dispensacaoMedicamentoItem);
    }

    public abstract void adicionar(AjaxRequestTarget target, DispensacaoMedicamentoItem itemOrigem, DispensacaoMedicamentoItem itemDestino) throws ValidacaoException, DAOException;

    public abstract void fechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtQuantidadePrescrita.isEnabled() ? txtQuantidadePrescrita : txtQuantidadeDispensada;
    }
}
