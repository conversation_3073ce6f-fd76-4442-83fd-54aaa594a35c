package br.com.celk.view.vigilancia.atividadeestabelecimento.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AtividadeEstabelecimentoAutoCompleteSettings extends AbstractAutoCompleteSettings {
    
    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<>();

        propertiesMap.put("id", o.getIdentificador());

        if (o instanceof AtividadeEstabelecimento) {
            AtividadeEstabelecimento bean = (AtividadeEstabelecimento) o;
            propertiesMap.put("name", bean.getDescricaoVO());
            propertiesMap.put("descricao", bean.getDescricao());
            propertiesMap.put("setor", (bean.getSetorVigilancia() != null ? Coalesce.asString(bean.getSetorVigilancia().getDescricao()).length() > 0 ? bean.getSetorVigilancia().getDescricao() : "" : ""));
            String stringMaxPrecision = StringUtil.getStringMaxPrecision(Coalesce.asString(bean.getDescricaoComplementar()), 120);
            if (StringUtils.trimToNull(stringMaxPrecision) != null) {
                propertiesMap.put("descricaoComplementar", "| " + stringMaxPrecision);
            } else {
                propertiesMap.put("descricaoComplementar", "");
            }
        }
        return propertiesMap;
    }

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();
        builder.append("<li>");
            builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
                builder.append("<div class=\"nivel-1\"> '+item.descricao+' </div>");
                builder.append("<div class=\"nivel-2\" > " + "Setor" + ": '+item.setor+' '+item.descricaoComplementar+' </div>");
            builder.append("</div>");
        builder.append("</li>");
        
        return builder.toString();
    }
}
