package br.com.celk.view.hospital.faturamento.tiss.dialogs.outrasDespesas;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.basico.unidade.autocomplete.AutoCompleteConsultaUnidade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPacienteTissDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.util.logging.Level;
import java.util.logging.Logger;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import static ch.lambdaj.Lambda.on;
import java.util.Arrays;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNovoLancamentoDespesaTiss extends Panel {

    private CompoundPropertyModel<ItemContaPacienteTissDTO> model;
    private Date dataCompetencia;
    private ProcedimentoCompetencia procedimentoCompetencia;
    private DateChooser dchDataLancamento;
    private DropDown dropDownTipoDespesa;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private AutoCompleteConsultaUnidade autoCompleteConsultaUnidade;
    private HoraMinutoField txtHoraInicial;
    private HoraMinutoField txtHoraFinal;
    private DoubleField txtQuantidade;
    private DoubleField txtPrecoUnitario;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;

    public PnlNovoLancamentoDespesaTiss(String id) {
        super(id);
        init();
    }

    private void init() {
        dataCompetencia = CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento();

        Form<ItemContaPacienteTissDTO> form = new Form("form", model = new CompoundPropertyModel(new ItemContaPacienteTissDTO()));
        ItemContaPacienteTissDTO proxy = on(ItemContaPacienteTissDTO.class);

        form.add(dchDataLancamento = new DateChooser(path(proxy.getDataLancamento())));
        dchDataLancamento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        form.add(dropDownTipoDespesa = DropDownUtil.getIEnumDropDown(path(proxy.getTipoDespesa()), ItemContaPaciente.TipoDespesa.values(), true));

        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia("procedimentoCompetencia", new PropertyModel<ProcedimentoCompetencia>(this, "procedimentoCompetencia")));
        autoCompleteConsultaProcedimentoCompetencia.setDataCompetencia(dataCompetencia);
        autoCompleteConsultaProcedimentoCompetencia.add(new ConsultaListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoCompetencia procedimentoCompetencia) {
                if (procedimentoCompetencia != null) {
                    setPrecoProcedimento(target, procedimentoCompetencia.getId().getProcedimento());
                }
            }
        });

        autoCompleteConsultaProcedimentoCompetencia.add(new RemoveListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ProcedimentoCompetencia procedimentoCompetencia) {
                txtPrecoUnitario.limpar(target);
            }
        });

        form.add(txtHoraInicial = new HoraMinutoField(path(proxy.getHoraInicial())));
        form.add(txtHoraFinal = new HoraMinutoField(path(proxy.getHoraFinal())));

        form.add(txtQuantidade = new DoubleField(path(proxy.getQuantidade())));
        txtQuantidade.setVMax(99999999D);

        form.add(txtPrecoUnitario = new DoubleField(path(proxy.getPrecoUnitario())));
        txtPrecoUnitario.setVMax(99999999D);

        form.add(autoCompleteConsultaUnidade = new AutoCompleteConsultaUnidade(path(proxy.getUnidade())));
        autoCompleteConsultaUnidade.addAjaxUpdateValue();
        autoCompleteConsultaUnidade.addPropertiesLoad(Unidade.PROP_CODIGO_UNIDADE_TISS);

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ItemContaPacienteTissDTO dto = model.getObject();
                dto.setTipo(ItemContaPaciente.Tipo.OUTRAS_DESPESAS.value());
                dto.setStatus(ItemContaPaciente.Status.ABERTO.value());
                validarAdicionar(dto);
                dto.setPrecoTotal(new Dinheiro(Coalesce.asDouble(dto.getPrecoUnitario())).multiplicar(Coalesce.asDouble(dto.getQuantidade())).doubleValue());
                onConfirmar(target, dto);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        add(form);

        setOutputMarkupId(true);
    }

    private void validarAdicionar(ItemContaPacienteTissDTO dto) throws ValidacaoException, DAOException {
        if (dto.getDataLancamento() == null) {
            throw new ValidacaoException(bundle("msgInformeDataLancamento"));
        }

        if (procedimentoCompetencia == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        } else {
            dto.setProcedimento(procedimentoCompetencia.getId().getProcedimento());
        }

        if (dto.getQuantidade() == null) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        }

        if (dto.getUnidade() == null) {
            throw new ValidacaoException(bundle("informeUnidade"));
        } else {
            if (dto.getUnidade().getCodigoUnidadeTiss() == null) {
                throw new ValidacaoException(bundle("msgCodigoUnidadeTissUnidadeXDeveSerDefinido", dto.getUnidade().getDescricao()));
            }
        }
    }

    public void limpar(AjaxRequestTarget target) {
        model.setObject(new ItemContaPacienteTissDTO());
        dchDataLancamento.limpar(target);
        dropDownTipoDespesa.limpar(target);
        autoCompleteConsultaProcedimentoCompetencia.limpar(target);
        txtHoraInicial.limpar(target);
        txtHoraFinal.limpar(target);
        txtQuantidade.limpar(target);
        txtPrecoUnitario.limpar(target);
        autoCompleteConsultaUnidade.limpar(target);
        update(target);
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) {
        limpar(target);
        this.model.setObject(dto);

        if (dto.getProcedimento() != null) {
            ProcedimentoCompetencia proxy = on(ProcedimentoCompetencia.class);

            procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                    .addProperties(new HQLProperties(ProcedimentoCompetencia.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, path(proxy.getId().getProcedimento())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProcedimento()), dto.getProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getDataCompetencia()), dataCompetencia))
                    .start().getVO();
        }

        if (dto.getDataLancamento() == null) {
            target.appendJavaScript(JScript.focusComponent(dchDataLancamento.getData()));
        }

        autoCompleteConsultaProcedimentoCompetencia.setTipoTabelaProcedimentoList(Arrays.asList(dto.getContaPaciente().getConvenio().getTipoTabelaProcedimento().getCodigo()));
        update(target);
    }

    private void setPrecoProcedimento(AjaxRequestTarget target, Procedimento procedimento) {
        ContaPaciente contaPaciente = model.getObject().getContaPaciente();

        Double preco = null;
        try {
            preco = BOFactory.getBO(HospitalFacade.class).getPrecoProcedimento(procedimento, contaPaciente.getConvenio(), contaPaciente.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getCodigo());
        } catch (DAOException ex) {
            Logger.getLogger(PnlNovoLancamentoDespesaTiss.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(PnlNovoLancamentoDespesaTiss.class.getName()).log(Level.SEVERE, null, ex);
        }

        if (preco != null) {
            txtPrecoUnitario.setComponentValue(preco);
            target.add(txtPrecoUnitario);
        } else {
            txtPrecoUnitario.limpar(target);
        }

        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void update(AjaxRequestTarget target) {
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        target.appendJavaScript(JScript.initMasks());
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
