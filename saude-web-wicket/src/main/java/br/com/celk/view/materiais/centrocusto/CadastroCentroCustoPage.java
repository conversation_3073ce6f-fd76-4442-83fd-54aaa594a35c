package br.com.celk.view.materiais.centrocusto;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.pnl.PnlConsultaEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroCentroCustoPage extends CadastroPage<CentroCusto> {
    
    private InputField txtDescricao;

    public CadastroCentroCustoPage(CentroCusto object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        
    }
    
    public CadastroCentroCustoPage(CentroCusto object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroCentroCustoPage(CentroCusto object) {
        this(object, false);
    }

    public CadastroCentroCustoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredInputField<String>(CentroCusto.PROP_DESCRICAO));
        form.add(new InputField<String>(CentroCusto.PROP_MASCARA));
        form.add(new PnlConsultaEmpresa(CentroCusto.PROP_EMPRESA));
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<CentroCusto> getReferenceClass() {
        return CentroCusto.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaCentroCustoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroCentroCusto");
    }

}
