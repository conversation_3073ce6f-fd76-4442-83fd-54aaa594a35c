package br.com.celk.view.hospital.aih;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAutorizarAih extends Window {

    private PnlAutorizarAih pnlAutorizarAih;

    public DlgAutorizarAih(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(200);
        setInitialWidth(700);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("autorizarAih");
            }
        });

        setContent(pnlAutorizarAih = new PnlAutorizarAih(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Aih aih) throws DAOException, ValidacaoException {
                DlgAutorizarAih.this.onConfirmar(target, aih);
                onFechar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih) throws DAOException, ValidacaoException;

    public void show(AjaxRequestTarget target, Aih Aih) {
        show(target);
        pnlAutorizarAih.setAih(target, Aih);
    }

    private void fechar(AjaxRequestTarget target) {
        pnlAutorizarAih.limpar(target);
        close(target);
    }
}
