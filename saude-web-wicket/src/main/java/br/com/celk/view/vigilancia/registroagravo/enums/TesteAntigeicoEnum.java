package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum TesteAntigeicoEnum implements IEnum {
    POSITIVO(1L, "Positivo"),
    NEGATIVO(2L, "Negativo"),
    INCONCLUSIVO(3L, "Inconclusivo"),
    NAO_REALIZADO(4L, "Não Realizado"),
    AGUARDANDO_RESULTADO(5L, "Aguardando resultado"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    TesteAntigeicoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static TesteAntigeicoEnum valueOf(Long value) {
        for (TesteAntigeicoEnum v : TesteAntigeicoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
