package br.com.celk.view.basico.bairro.autocomplete;


import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.messaging.autocomplete.MessagingAutoComplete;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.basico.Bairro;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class AutoCompleteConsultaBairroMulti extends MessagingAutoComplete<Bairro> {

    public AutoCompleteConsultaBairroMulti(String wicketId) {
        super(wicketId);
    }

    @Override
    public IConsultaConfigurator getConsultaConfiguratorInstance() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return Bairro.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Bairro.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {
                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), Bairro.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), Bairro.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(Bairro.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(Bairro.PROP_CODIGO));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return Bairro.class;
                    }
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(Bairro.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }
}
