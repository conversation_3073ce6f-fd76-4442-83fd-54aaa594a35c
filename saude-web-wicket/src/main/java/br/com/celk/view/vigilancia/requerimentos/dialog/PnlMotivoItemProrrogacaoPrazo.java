package br.com.celk.view.vigilancia.requerimentos.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoItemProrrogacaoPrazo extends Panel {

    private Form form;
    private AbstractAjaxButton btnFechar;
    private String item;
    private String motivo;
    private InputField txtItem;
    private InputArea txaMotivo;

    public PnlMotivoItemProrrogacaoPrazo(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        form = new Form("form");
        form.setOutputMarkupId(true);
        
        form.add(txtItem = new DisabledInputField("item", new PropertyModel<String>(this, "item")));
        form.add(txaMotivo = new DisabledInputArea("motivo", new PropertyModel<String>(this, "motivo")));

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setRequerimentoProrrogacaoPrazoItem(AjaxRequestTarget target, RequerimentoProrrogacaoPrazoItem requerimentoProrrogacaoPrazoItem) {
        txtItem.limpar(target);
        txaMotivo.limpar(target);
        
        item = requerimentoProrrogacaoPrazoItem.getDescricao();
        motivo = requerimentoProrrogacaoPrazoItem.getMotivo();

        target.add(txtItem);
        target.add(txaMotivo);
    }

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {
    }
}
