package br.com.celk.view.basico.empresa.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaDominioEstabelecimentoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerDominioEstabelecimento extends Panel implements IRestricaoContainer<QueryConsultaDominioEstabelecimentoDTOParam> {

    private InputField<String> txtNome;
    private InputField<String> txtCodigo;
    private InputField<String> txtCnes;
    private InputField<String> txtSigla;
    private InputField<String> txtCpfCnpj;
    
    private QueryConsultaDominioEstabelecimentoDTOParam param = new QueryConsultaDominioEstabelecimentoDTOParam();
    
    public RestricaoContainerDominioEstabelecimento(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtNome = new InputField<String>("nome"));
        root.add(txtCodigo = new InputField<String>("codigo"));
        root.add(txtCnes = new InputField<String>("cnes"));
        root.add(txtSigla = new UpperField("sigla"));
        root.add(txtCpfCnpj = new InputField<String>("cnpjCpf"));
        add(root);
    }

    @Override
    public QueryConsultaDominioEstabelecimentoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtNome.limpar(target);
        txtCodigo.limpar(target);
        txtCpfCnpj.limpar(target);
        txtCnes.limpar(target);
        txtSigla.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtNome;
    }

}
