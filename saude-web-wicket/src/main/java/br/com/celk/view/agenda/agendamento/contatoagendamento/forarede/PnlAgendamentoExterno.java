package br.com.celk.view.agenda.agendamento.contatoagendamento.forarede;


import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.template.Panel;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.AgendamentoListaEsperaResumoContatoPage;
import br.com.celk.view.agenda.agendamento.DlgSelecionarNotificacaoRegulacao;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.regionalsaude.autocomplete.AutoCompleteConsultaRegionalSaude;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.ConsultaRegulacaoSolicitacaoPage;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoExternoSolicitacaoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendarSolicitacaoForaRedeDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.*;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.ConsultaRegulacaoSolicitacaoDTOParam;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Leonardo.
 */
public class PnlAgendamentoExterno extends Panel {

    private AgendamentoExternoSolicitacaoDTOParam param;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private InputField txtNomeProfissional;
    private DlgImpressaoObject<AgendaGradeAtendimentoHorario> dlgImpressao;
    private FileUploadField fileUploadField;
    private Label lblAnexo;
    private MensagemAnexoDTO mensagemAnexoDTO;
    private List<FileUpload> lstUpload;
    private boolean possuiAnexo = false;
    private FileUpload upload;
    private DlgSelecionarNotificacaoRegulacao dlgNotificacao;

    private AgendamentoListaEsperaDTOParam paramLista;
    private Class classeVoltar;
    private ConsultaRegulacaoSolicitacaoDTOParam paramConsulta;

    public PnlAgendamentoExterno(String id, SolicitacaoAgendamento solicitacaoAgendamento,  AgendamentoListaEsperaDTOParam paramLista, Class classeVoltar) {
        super(id);
        inicializarParam(solicitacaoAgendamento);
        this.paramLista = paramLista;
        this.classeVoltar = classeVoltar;
        init();
    }

    public PnlAgendamentoExterno(String id, SolicitacaoAgendamento solicitacaoAgendamento,  AgendamentoListaEsperaDTOParam paramLista, Class classeVoltar, ConsultaRegulacaoSolicitacaoDTOParam paramConsulta) {
        super(id);
        inicializarParam(solicitacaoAgendamento);
        this.paramLista = paramLista;
        this.classeVoltar = classeVoltar;
        this.paramConsulta = paramConsulta;
        init();
    }

    private void init() {
        WebMarkupContainer container = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        container.setOutputMarkupId(true);

        AgendamentoExternoSolicitacaoDTOParam proxy = on(AgendamentoExternoSolicitacaoDTOParam.class);

        container.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getLocalAtendimento()), true));
        container.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                if (object.getNome() != null) {
                    txtNomeProfissional.setComponentValue(object.getNome());
                    txtNomeProfissional.setEnabled(false);
                    target.add(txtNomeProfissional);
                }
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                txtNomeProfissional.setComponentValue(null);
                txtNomeProfissional.setEnabled(true);
                target.add(txtNomeProfissional);
            }
        });
        container.add(txtNomeProfissional = new InputField(path(proxy.getNomeProfissional())));
        container.add(new RequiredDateChooser(path(proxy.getDataAtendimento())));
        container.add(new RequiredInputField(path(proxy.getHoraAtendimento())));
        container.add(new InputField(path(proxy.getContato())));
        container.add(new InputField(path(proxy.getCodigoExterno())).add(new Tooltip().setText("msgCentralMarcacaoConsultasExternasUtilizarCasoQueiraRegistrarCodigoExternoAgendamentoExemploCodigoSISREG")));
        container.add(new AutoCompleteConsultaRegionalSaude(path(proxy.getRegionalSaude())));

        Form formAnexo = new Form("formAnexo", new CompoundPropertyModel(this));
        formAnexo.setOutputMarkupId(true);
        container.add(formAnexo);

        formAnexo.add(fileUploadField = new FileUploadField("lstUpload"));

        formAnexo.add(lblAnexo = new Label("mensagemAnexoDTO.nomeArquivoOriginal"));
        lblAnexo.setOutputMarkupId(true);

        formAnexo.add(new SubmitButton("btnAnexar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                upload = fileUploadField.getFileUpload();
                if (upload != null) {
                    target.add(lblAnexo);

                    try {
                        if (upload.getClientFileName().endsWith(".pdf") || upload.getClientFileName().endsWith(".jpg")
                                || upload.getClientFileName().endsWith(".jpeg") || upload.getClientFileName().endsWith(".png")) {
                            File newFile = File.createTempFile("anexo", upload.getClientFileName());
                            upload.writeTo(newFile);
                            mensagemAnexoDTO = new MensagemAnexoDTO();
                            mensagemAnexoDTO.setNomeArquivoOriginal(upload.getClientFileName());
                            mensagemAnexoDTO.setNomeArquivoUpload(newFile.getAbsolutePath());
                            mensagemAnexoDTO.setOrigem(GerenciadorArquivo.OrigemArquivo.ANEXO_RECOMENDACAO_AGENDA.value());
                            possuiAnexo = true;
                        } else {
                            throw new ValidacaoException(bundle("somentePossivelAnexarImagensPdf"));
                        }
                    } catch (IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);

                    }
                }
            }
        }).setDefaultFormProcessing(false));

        formAnexo.add(new AbstractAjaxLink("btnRemoverAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                upload = null;
                mensagemAnexoDTO = new MensagemAnexoDTO();
                mensagemAnexoDTO.setNomeArquivoOriginal(bundle("nenhumAnexoAdicionado"));
                possuiAnexo = false;
                target.add(lblAnexo);
            }
        });

        container.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        verificarComparecimento();
        carregarOrientacaoDocumentoRetido();

        add(container);
    }

    private void verificarComparecimento() {
        List<AgendaGradeAtendimentoHorario> agahList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, param.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, param.getTipoProcedimento()))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(agahList)) {
            for (AgendaGradeAtendimentoHorario agah : agahList) {
                if (AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agah.getStatus())) {
                    warn(bundle("msgPacienteNaoCompareceuUltimoAgendamentoRealizadoParaEsteTipoDataAgendamentoXLocalX",
                            agah.getDataHoraAgendamentoFormatado(), agah.getLocalAgendamento().getDescricao()));
                }
                break;
            }
        }
    }

    private void inicializarParam(SolicitacaoAgendamento solicitacaoAgendamento) {
        param = new AgendamentoExternoSolicitacaoDTOParam();
        param.setSolicitacaoAgendamento(solicitacaoAgendamento);
        param.setTipoProcedimento(solicitacaoAgendamento.getTipoProcedimento());
        param.setEmpresaSolicitante(solicitacaoAgendamento.getEmpresa());
        param.setUsuarioCadsus(solicitacaoAgendamento.getUsuarioCadsus());
    }

    private AgendamentoExternoSolicitacaoDTOParam getParam() {
        return param;
    }

    private void carregarOrientacaoDocumentoRetido() {
        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                .addProperty(TipoProcedimento.PROP_CODIGO)
                .addProperty(TipoProcedimento.PROP_DESCRICAO)
                .addProperty(TipoProcedimento.PROP_FLAG_DOCUMENTO_RETIDO)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, getParam().getTipoProcedimento().getCodigo()))
                .start().getVO();
        if (RepositoryComponentDefault.SIM_LONG.equals(tp.getFlagDocumentoRetido())) {
            warn(bundle("msgDocumentoDeveFicarRetidoEstabelecimento"));
        }
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AgendamentoExternoSolicitacaoDTOParam dtoParam = getParam();

        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                .addProperties(new HQLProperties(TipoProcedimento.class).getProperties())
                .addProperties(new HQLProperties(TipoProcedimentoClassificacao.class, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, dtoParam.getTipoProcedimento().getCodigo()))
                .start().getVO();

        if (!tp.getTipoProcedimentoClassificacao().pertenceClassificacaoExame()
                && dtoParam.getProfissional() == null && (dtoParam.getNomeProfissional() == null || dtoParam.getNomeProfissional().trim().isEmpty())) {
            throw new ValidacaoException(BundleManager.getString("msg_informe_profissional_ou_nome_profissional"));
        }

        final AgendarSolicitacaoForaRedeDTO dto = new AgendarSolicitacaoForaRedeDTO();
        dto.setCodigoSolicitacao(dtoParam.getSolicitacaoAgendamento().getCodigo());
        dto.setUnidadeExecutante(dtoParam.getLocalAtendimento());

        upload = fileUploadField.getFileUpload();
        if (upload != null && !possuiAnexo) {
            throw new ValidacaoException(bundle("msgArquivoSelecionadoNaoAnexadoFavorClicarAnexar"));
        }
        dto.setAnexoDTO(mensagemAnexoDTO);
        if (dtoParam.getProfissional() != null) {
            dto.setProfissionalExecutante(dtoParam.getProfissional());
        }

        if (dtoParam.getNomeProfissional() != null && !dtoParam.getNomeProfissional().trim().isEmpty()) {
            dto.setNomeProfissionalExecutante(dtoParam.getNomeProfissional());
        }

        dto.setDataHora(DataUtil.getDataHora(dtoParam.getDataAtendimento(), dtoParam.getHoraAtendimento()));
        dto.setContato(dtoParam.getContato());
        dto.setCodigoExterno(dtoParam.getCodigoExterno());
        dto.setRegionalSaude(dtoParam.getRegionalSaude());

        showDlgNotificacaoRegulacao(target, dto);
    }

    private void gerarAgendamento(AjaxRequestTarget target, AgendarSolicitacaoForaRedeDTO dto, Long pacienteNotificado) throws DAOException, ValidacaoException {
        dto.setPacienteNotificado(pacienteNotificado);
        AgendaGradeAtendimentoHorario saveAgendaGradeAtendimentoHorario = BOFactoryWicket.getBO(AgendamentoFacade.class).agendarSolicitacaoForaRedeReturnAgah(dto);
        showDlgImpressaoComprovanteAgendamento(target, saveAgendaGradeAtendimentoHorario);
    }

    private void showDlgNotificacaoRegulacao(AjaxRequestTarget target, AgendarSolicitacaoForaRedeDTO agendarSolicitacaoForaRedeDTO) {
        if (dlgNotificacao == null) {
            WindowUtil.addModal(target, this, dlgNotificacao = new DlgSelecionarNotificacaoRegulacao<AgendarSolicitacaoForaRedeDTO>(WindowUtil.newModalId(this)) {
                @Override
                public void onNotificarRegulacao(AjaxRequestTarget target, AgendarSolicitacaoForaRedeDTO dto) throws ValidacaoException, DAOException {
                    gerarAgendamento(target, dto, RepositoryComponentDefault.SIM_LONG);
                }

                @Override
                public void onNotificarUnidade(AjaxRequestTarget target, AgendarSolicitacaoForaRedeDTO dto) throws ValidacaoException, DAOException {
                    gerarAgendamento(target, dto, RepositoryComponentDefault.NAO_LONG);
                }
            });
        }

        dlgNotificacao.show(target, agendarSolicitacaoForaRedeDTO);
    }

    private void showDlgImpressaoComprovanteAgendamento(AjaxRequestTarget target, AgendaGradeAtendimentoHorario agah) {
        if (dlgImpressao == null) {
            WindowUtil.addModal(target, this, dlgImpressao = new DlgImpressaoObject<AgendaGradeAtendimentoHorario>(WindowUtil.newModalId(this), bundle("agendamentoExternoRealizadoSucesso")) {
                @Override
                public DataReport getDataReport(AgendaGradeAtendimentoHorario object) throws ReportException {
                    RelatorioImprimirComprovanteAgendamentoDTOParam param = new RelatorioImprimirComprovanteAgendamentoDTOParam();
                    param.setAgendaGradeAtendimentoHorarioList(Collections.singletonList(object));
                    return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, AgendaGradeAtendimentoHorario object) throws ValidacaoException, DAOException {
                    if(ConsultaRegulacaoSolicitacaoPage.class == classeVoltar) {
                        setResponsePage(new ConsultaRegulacaoSolicitacaoPage(paramConsulta, true));
                    } else {
                        Page page;
                        page = new AgendamentoListaEsperaResumoContatoPage(paramLista, true);
                        setResponsePage(page);
                    }
                }
            });
        }
        dlgImpressao.show(target, agah);
    }

}