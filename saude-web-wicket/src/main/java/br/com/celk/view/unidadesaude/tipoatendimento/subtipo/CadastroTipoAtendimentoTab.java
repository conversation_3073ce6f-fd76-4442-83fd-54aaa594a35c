package br.com.celk.view.unidadesaude.tipoatendimento.subtipo;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.unidadesaude.tipoatendimento.dto.TipoAtendimentoDTO;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoAtendimentoTab extends TabPanel<TipoAtendimentoDTO> {

    private TipoAtendimento tipoAtendimento;
    private AutoCompleteConsultaTipoAtendimento autoCompleteConsultaTipoAtendimento;

    private InputField txtDescricao;
    private Table table;

    public CadastroTipoAtendimentoTab(String id, TipoAtendimentoDTO object) {
        super(id, object);
        init();
    }
    
    

    private void init() {
        TipoAtendimentoDTO proxy = on(TipoAtendimentoDTO.class);

        add(txtDescricao = new RequiredInputField("descricao", new PropertyModel(this, "object.descricao")));

        add(autoCompleteConsultaTipoAtendimento = new AutoCompleteConsultaTipoAtendimento("tipoAtendimento", new PropertyModel(this, "tipoAtendimento")));
        add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }.setDefaultFormProcessing(false));

        add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (tipoAtendimento == null) {
            throw new ValidacaoException(bundle("msgObrigatorioTipoAtendimento"));
        }

        for (TipoAtendimento tp : object.getLstTipoAtendimento()) {
            if (tp.equals(tipoAtendimento)) {
                throw new ValidacaoException(bundle("msgTipoAtendimentoJaAdicionado"));
            }
        }

        object.getLstTipoAtendimento().add(tipoAtendimento);
        table.populate();
        table.update(target);

        autoCompleteConsultaTipoAtendimento.limpar(target);
        target.add(autoCompleteConsultaTipoAtendimento);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getLstTipoAtendimento();
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        TipoAtendimento proxy = on(TipoAtendimento.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TipoAtendimento>() {
            @Override
            public void customizeColumn(final TipoAtendimento rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoAtendimento modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < object.getLstTipoAtendimento().size(); i++) {
                            TipoAtendimento item = object.getLstTipoAtendimento().get(i);
                            if (item == rowObject) {
                                if (item.getTipoAtendimentoAgrupador() != null && object.getTipoAtendimentoEdicao() != null) {
                                    if (item.getTipoAtendimentoAgrupador().equals(object.getTipoAtendimentoEdicao())) {
                                        object.getLstTipoAtendimentoExclusao().add(item);
                                    }
                                }
                                object.getLstTipoAtendimento().remove(i);
                            }
                        }
                        table.populate();
                        table.update(target);
                    }
                });
            }
        };
    }

    public InputField getTxtDescricao() {
        return txtDescricao;
    }

    @Override
    public String getTitle() {
        return bundle("atendimento");
    }

}
