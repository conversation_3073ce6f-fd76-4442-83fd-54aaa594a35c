package br.com.celk.view.unidadesaude.esus.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.biometria.Biometria;
import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.esus.domicilio.ComponenteDomicilioDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.cadsus.usuariocadsus.CadastroUsuarioCidadaoDTO;
import br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel.CadastroUsuarioCidadaoTabbedPanel;
import br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel.DadosUsuarioCidadaoTab;
import br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel.DocumentosUsuarioCidadaoTab;
import br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel.EnderecoUsuarioCidadaoTab;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.SegmentoTerritorial;
import br.com.ksisolucoes.vo.cadsus.*;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroUsuarioCidadaoPage extends BasePage {

    private Biometria appletBiometria;

    public CadastroUsuarioCidadaoPage() {
        this(null);
    }

    public CadastroUsuarioCidadaoPage(CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO) {
        this(cadastroUsuarioCidadaoDTO, false);
    }

    public CadastroUsuarioCidadaoPage(CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO, boolean viewOnly) {
            init(cadastroUsuarioCidadaoDTO, viewOnly);
    }

    private void init(CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO, boolean viewOnly) {
        if (cadastroUsuarioCidadaoDTO == null) {
            cadastroUsuarioCidadaoDTO = getNewInstance();
        }

        if (cadastroUsuarioCidadaoDTO.getUsuarioCadsusEsus() == null) {
            cadastroUsuarioCidadaoDTO.setUsuarioCadsusEsus(new UsuarioCadsusEsus());
        }

        if (cadastroUsuarioCidadaoDTO.getUsuarioCadsus() != null) {
            List<UsuarioCadsusEndereco> lista = carregarEnderecosPaciente(cadastroUsuarioCidadaoDTO.getUsuarioCadsus().getCodigo());
            if (CollectionUtils.isNotNullEmpty(lista)) {
                for (UsuarioCadsusEndereco usuarioCadsusEndereco : lista) {
                    try {
                        List<UsuarioCadsusDomicilio> moradores = getUsuariosEndereco(usuarioCadsusEndereco.getId().getEndereco());
                        if (CollectionUtils.isNotNullEmpty(moradores)) {
                            boolean responsavel = false;
                            for (UsuarioCadsusDomicilio usuarioCadsusDomicilio : moradores) {
                                if (usuarioCadsusDomicilio != null && usuarioCadsusDomicilio.getUsuarioCadsus() != null && RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusDomicilio.getUsuarioCadsus().getFlagResponsavelFamiliar())) {
                                    ComponenteDomicilioDTO dto = new ComponenteDomicilioDTO();
                                    dto.setUsuarioCadsusDomicilio(usuarioCadsusDomicilio);
                                    cadastroUsuarioCidadaoDTO.setResponsavel(dto);
                                    responsavel = true;
                                    break;
                                }
                            }
                            
                            if(responsavel){
                                break;
                            }

                        }
                    } catch (DAOException | ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            }
        }

        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<CadastroUsuarioCidadaoDTO>(cadastroUsuarioCidadaoDTO) {

            @Override
            public ITabPanel<CadastroUsuarioCidadaoDTO> newTabPanel(String panelId, CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO) {
                return new DadosUsuarioCidadaoTab(panelId, cadastroUsuarioCidadaoDTO);
            }
        });
        tabs.add(new CadastroTab<CadastroUsuarioCidadaoDTO>(cadastroUsuarioCidadaoDTO) {

            @Override
            public ITabPanel<CadastroUsuarioCidadaoDTO> newTabPanel(String panelId, CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO) {
                return new DocumentosUsuarioCidadaoTab(panelId, cadastroUsuarioCidadaoDTO);
            }
        });
        tabs.add(new CadastroTab<CadastroUsuarioCidadaoDTO>(cadastroUsuarioCidadaoDTO) {

            @Override
            public ITabPanel<CadastroUsuarioCidadaoDTO> newTabPanel(String panelId, CadastroUsuarioCidadaoDTO cadastroUsuarioCidadaoDTO) {
                return new EnderecoUsuarioCidadaoTab(panelId, cadastroUsuarioCidadaoDTO);
            }
        });
        CadastroTabbedPanel cadastroPacienteTabbedPanel;
        add(cadastroPacienteTabbedPanel = new CadastroUsuarioCidadaoTabbedPanel("wizard", cadastroUsuarioCidadaoDTO, viewOnly, tabs));

        appletBiometria = new Biometria("appletBiometria");
        add(appletBiometria);

        appletBiometria.setListener((IAppletAction) cadastroPacienteTabbedPanel);
    }

    public List<UsuarioCadsusDomicilio> getUsuariosEndereco(EnderecoUsuarioCadsus enderecoUsuarioCadsus) throws DAOException, ValidacaoException {
        List<UsuarioCadsusDomicilio> moradores = new ArrayList<UsuarioCadsusDomicilio>();

        List<EnderecoDomicilio> domiciliosList = LoadManager.getInstance(EnderecoDomicilio.class)
                .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                .addProperties(new HQLProperties(EquipeMicroArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA)).getProperties())
                .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA)).getProperties())
                .addProperties(new HQLProperties(SegmentoTerritorial.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO), enderecoUsuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(EnderecoDomicilio.PROP_EXCLUIDO, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(domiciliosList)) {

            List<UsuarioCadsusDomicilio> lista = LoadManager.getInstance(UsuarioCadsusDomicilio.class)
                    .addProperties(new HQLProperties(UsuarioCadsusDomicilio.class).getProperties())
                    .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_USUARIO_CADSUS)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_ENDERECO_DOMICILIO), domiciliosList.get(0)))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDomicilio.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(lista)) {
                for (UsuarioCadsusDomicilio _usuarioCadsusDomicilio : lista) {
                    moradores.add(_usuarioCadsusDomicilio);
                }
            }

        }
        return moradores;
    }

    private List<UsuarioCadsusEndereco> carregarEnderecosPaciente(Long id) {
        UsuarioCadsusEndereco proxy = on(UsuarioCadsusEndereco.class);
        List<UsuarioCadsusEndereco> enderecoList = new ArrayList<UsuarioCadsusEndereco>();
        if (id != null) {
            enderecoList = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                    .addProperties(new HQLProperties(UsuarioCadsusEndereco.class).getProperties())
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, path(proxy.getId().getEndereco())).getProperties())
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getCodigo()))
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getDescricao()))
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getSigla()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getUsuarioCadsus().getCodigo()), id))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataUsuario()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();
        }
        return enderecoList;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroUsuarioCidadao");
    }

    private CadastroUsuarioCidadaoDTO getNewInstance() {
        CadastroUsuarioCidadaoDTO object = new CadastroUsuarioCidadaoDTO();

        object.setUsuarioCadsus(new UsuarioCadsus());
        object.setUsuarioCadsusEsus(new UsuarioCadsusEsus());
        object.setDocumentoCertidao(new UsuarioCadsusDocumento());
        object.getDocumentoCertidao().setTipoDocumento(new TipoDocumentoUsuario());
        object.setDocumentoIdentidade(new UsuarioCadsusDocumento());
        object.getDocumentoIdentidade().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG));
        object.setDocumentoPis(new UsuarioCadsusDocumento());
        object.getDocumentoPis().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_PIS));
        object.setDocumentoEleitor(new UsuarioCadsusDocumento());
        object.getDocumentoEleitor().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_TITULO_ELEITOR));

        return object;
    }

}
