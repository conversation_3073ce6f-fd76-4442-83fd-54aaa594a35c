package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoTuberculoseDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTuberculose;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTuberculoseEnum;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoTuberculosePage extends FichaInvestigacaoAgravoBasePage {

    private InvestigacaoAgravoTuberculose investigacaoAgravoTuberculose;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DateChooser dataInicioTratamentoAtual;
    private InputField totalContatosIdentificados;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerDadosGerais;
    private DropDown ddTipoDeEntrada;
    private DropDown ddBeneficiarioGoverno;

    private WebMarkupContainer containerPopulacaoEspecial;
    private DropDown populacaoEspecialPrivadaLiberdade;
    private DropDown populacaoEspecialSituacaoDeRua;
    private DropDown populacaoEspecialProfissionalSaude;
    private DropDown populacaoEspecialImigrante;

    private DropDown ddForma;
    private DropDown ddExtrapulmonar;
    private InputField txtExtrapulmonar;

    private WebMarkupContainer containerDoencasEAgravos;
    private DropDown ddAids;
    private DropDown ddAlcoolismo;
    private DropDown ddDiabetes;
    private DropDown ddDoencaMental;
    private DropDown ddDrogasIlicitas;
    private DropDown ddTabagismo;
    private DropDown ddDoencaAgravosOutros;
    private InputField txtDoencaAgravosOutros;

    private WebMarkupContainer containerExamesTestes;
    private DropDown ddBaciloscopiaDeEscarro;
    private DropDown ddRadiografiaTorax;
    private DropDown ddHIV;
    private DropDown ddTerapiaAntirretroviral;
    private DropDown ddHistopatologia;
    private DropDown ddExameDeCultura;
    private DropDown ddTesteMolecularRapidoTB;
    private DropDown ddTesteSensibilidade;

    private WebMarkupContainer containerObservacao;
    private InputArea txtObservacao;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public FichaInvestigacaoAgravoTuberculosePage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoTuberculose = InvestigacaoAgravoTuberculoseEnum.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoTuberculose == null) {
            investigacaoAgravoTuberculose = new InvestigacaoAgravoTuberculose();
            investigacaoAgravoTuberculose.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoTuberculose.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoTuberculose.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoTuberculose.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoTuberculose.setOcupacaoCbo(tabelaCbo);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoTuberculose)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoTuberculoseDTO fichaDTO = new FichaInvestigacaoAgravoTuberculoseDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoTuberculose(investigacaoAgravoTuberculose);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoTuberculose.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoTuberculose.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoTuberculose proxy = on(InvestigacaoAgravoTuberculose.class);

        criarInvestigacao(proxy);
        criarDadosGerais(proxy);
        criarPopulacaoEspecial(proxy);
        criarDoencasEAgravos(proxy);
        criarExamesTestes(proxy);
        criarObservacao(proxy);
        criarEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataInicioTratamentoAtual.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));


        ddForma.addAjaxUpdateValue();
        ddExtrapulmonar.setEnabled(InvestigacaoAgravoTuberculoseEnum.FormaTuberculoseEnum.isExtrapulmonar((Long) ddForma.getComponentValue()));
        ddForma.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ddExtrapulmonar.setEnabled(InvestigacaoAgravoTuberculoseEnum.FormaTuberculoseEnum.isExtrapulmonar((Long) ddForma.getComponentValue()));
                ddExtrapulmonar.limpar(target);
                txtExtrapulmonar.limpar(target);
                target.add(ddExtrapulmonar, txtExtrapulmonar);
            }
        });

        ddExtrapulmonar.addAjaxUpdateValue();
        txtExtrapulmonar.setEnabled(InvestigacaoAgravoTuberculoseEnum.ExtrapulmonarTuberculoseEnum.OUTRA.value().equals(ddExtrapulmonar.getComponentValue()));
        ddExtrapulmonar.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtExtrapulmonar.setEnabled(InvestigacaoAgravoTuberculoseEnum.ExtrapulmonarTuberculoseEnum.OUTRA.value().equals(ddExtrapulmonar.getComponentValue()));
                txtExtrapulmonar.limpar(target);
                target.add(txtExtrapulmonar);
            }
        });

        ddDoencaAgravosOutros.addAjaxUpdateValue();
        txtDoencaAgravosOutros.setEnabled(InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.SIM.value().equals(ddDoencaAgravosOutros.getComponentValue()));
        ddDoencaAgravosOutros.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtDoencaAgravosOutros.setEnabled(InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.SIM.value().equals(ddDoencaAgravosOutros.getComponentValue()));
                txtDoencaAgravosOutros.limpar(target);
                target.add(txtDoencaAgravosOutros);
            }
        });
        txtDoencaAgravosOutros.addAjaxUpdateValue();
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        Date dataInvestigacao = investigacaoAgravoTuberculose.getDataInvestigacao();
        Date dataRegistro = investigacaoAgravoTuberculose.getRegistroAgravo().getDataRegistro();
        Date dataInicioTratamento = investigacaoAgravoTuberculose.getDataInicioTratamento();
        Date dataEncerramento = investigacaoAgravoTuberculose.getDataEncerramento();

        if (dataInvestigacao != null && DataUtil.dataMaiorQueAtual(dataInvestigacao)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_X_futuro", "Data de Investigação"));
        }

        if (dataRegistro != null && DataUtil.dataMaiorQueAtual(dataRegistro)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_X_futuro", "Data de Registro"));
        }

        if (dataInicioTratamento != null && DataUtil.dataMaiorQueAtual(dataInicioTratamento)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_X_futuro", "Data de Início do Tratamento"));
        }

        if (dataEncerramento != null && DataUtil.dataMaiorQueAtual(dataEncerramento)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_X_futuro", "Data de Encerramento"));
        }

        if (dataInvestigacao != null && dataEncerramento != null && dataInvestigacao.after(dataEncerramento)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_X_maior_Y", "Data de Investigação", "Data de Encerramento"));
        }
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoTuberculoseDTO dto = (FichaInvestigacaoAgravoTuberculoseDTO) fichaDTO;
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoTuberculose(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoTuberculoseDTO fichaDTO = (FichaInvestigacaoAgravoTuberculoseDTO) getFichaDTO();

        if (investigacaoAgravoTuberculose.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoTuberculose.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoTuberculose.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    private void criarInvestigacao(InvestigacaoAgravoTuberculose proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupPlaceholderTag(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true);
        dataInvestigacao.addRequiredClass();

        dataInicioTratamentoAtual = new DateChooser(path(proxy.getDataInicioTratamento()));

        totalContatosIdentificados = new InputField(path(proxy.getTotalContatosIdentificados()));
        totalContatosIdentificados.setRequired(true);
        totalContatosIdentificados.addRequiredClass();

        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, dataInicioTratamentoAtual, ocupacaoCbo, totalContatosIdentificados);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarDadosGerais(InvestigacaoAgravoTuberculose proxy) {
        containerDadosGerais = new WebMarkupContainer("containerDadosGerais");
        containerDadosGerais.setOutputMarkupPlaceholderTag(true);

        ddTipoDeEntrada = DropDownUtil.getIEnumDropDown(path(proxy.getTipoEntrada()), InvestigacaoAgravoTuberculoseEnum.TipoEntradaTuberculoseEnum.values(), true, true);
        ddBeneficiarioGoverno = DropDownUtil.getIEnumDropDown(path(proxy.getBenficiarioProgramaTransferenciaRenda()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);

        ddForma = DropDownUtil.getIEnumDropDown(path(proxy.getFormaTuberculose()), InvestigacaoAgravoTuberculoseEnum.FormaTuberculoseEnum.values(), true, true);
        ddForma.setLabel(new Model(bundle("formaTuberculose")));

        ddExtrapulmonar = DropDownUtil.getIEnumDropDown(path(proxy.getFormaExtrapulmonar()), InvestigacaoAgravoTuberculoseEnum.ExtrapulmonarTuberculoseEnum.values(), true, true);
        ddExtrapulmonar.setEnabled(false);

        txtExtrapulmonar = new InputField(path(proxy.getFormaExtrapulmonarOutros()));
        txtExtrapulmonar.setEnabled(false);

        containerDadosGerais.add(ddTipoDeEntrada, ddBeneficiarioGoverno, ddForma, ddExtrapulmonar, txtExtrapulmonar);
        getContainerInformacoesComplementares().add(containerDadosGerais);

    }

    private void criarPopulacaoEspecial(InvestigacaoAgravoTuberculose proxy) {
        containerPopulacaoEspecial = new WebMarkupContainer("containerPopulacaoEspecial");
        containerPopulacaoEspecial.setOutputMarkupPlaceholderTag(true);

        populacaoEspecialPrivadaLiberdade = DropDownUtil.getIEnumDropDown(path(proxy.getPopulacaoPrivadaLiberdade()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        populacaoEspecialSituacaoDeRua = DropDownUtil.getIEnumDropDown(path(proxy.getPopulacaoSituacaoRua()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        populacaoEspecialProfissionalSaude = DropDownUtil.getIEnumDropDown(path(proxy.getPopulacaoProfissionalSaude()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        populacaoEspecialImigrante = DropDownUtil.getIEnumDropDown(path(proxy.getPopulacaoImigrante()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);

        containerPopulacaoEspecial.add(populacaoEspecialPrivadaLiberdade, populacaoEspecialSituacaoDeRua, populacaoEspecialProfissionalSaude, populacaoEspecialImigrante);
        containerDadosGerais.add(containerPopulacaoEspecial);
    }

    private void criarDoencasEAgravos(InvestigacaoAgravoTuberculose proxy) {
        containerDoencasEAgravos = new WebMarkupContainer("containerDoencasEAgravos");
        containerDoencasEAgravos.setOutputMarkupPlaceholderTag(true);

        ddAids = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoAids()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddAlcoolismo = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoAlcoolismo()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddDiabetes = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoDiabetes()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddDoencaMental = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoDoencaMental()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddDrogasIlicitas = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoDrogas()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddTabagismo = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoTabagismo()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddDoencaAgravosOutros = DropDownUtil.getIEnumDropDown(path(proxy.getAgravoAssociadoOutros()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        txtDoencaAgravosOutros = new InputField(path(proxy.getAgravoAssociadoOutrosStr()));
        txtDoencaAgravosOutros.setEnabled(false);

        containerDoencasEAgravos.add(ddAids, ddAlcoolismo, ddDiabetes, ddDoencaMental, ddDrogasIlicitas, ddTabagismo, ddDoencaAgravosOutros, txtDoencaAgravosOutros);
        getContainerInformacoesComplementares().add(containerDoencasEAgravos);
    }

    private void criarExamesTestes(InvestigacaoAgravoTuberculose proxy) {
        containerExamesTestes = new WebMarkupContainer("containerExamesTestes");
        containerExamesTestes.setOutputMarkupPlaceholderTag(true);

        ddBaciloscopiaDeEscarro = DropDownUtil.getIEnumDropDown(path(proxy.getBaciloscopiaEscarro()), InvestigacaoAgravoTuberculoseEnum.PositivoNegativoNaoRelizadoNaoSeAplicaEnum.values(), true, true);
        ddBaciloscopiaDeEscarro.setLabel(new Model(bundle("baciloscopiaDeEscarro")));
        ddRadiografiaTorax = DropDownUtil.getIEnumDropDown(path(proxy.getRadiografiaTorax()), InvestigacaoAgravoTuberculoseEnum.RadiografiaToraxEnum.values(), true, false);
        ddHIV = DropDownUtil.getIEnumDropDown(path(proxy.getHiv()), InvestigacaoAgravoTuberculoseEnum.PositivoNegativoAndamentoNaoRealizadoEnum.values(), true, true);
        ddTerapiaAntirretroviral = DropDownUtil.getIEnumDropDown(path(proxy.getTerapiaAntirretroviralDuranteTratamento()), InvestigacaoAgravoTuberculoseEnum.SimNaoIgnoradoEnum.values(), true, false);
        ddHistopatologia = DropDownUtil.getIEnumDropDown(path(proxy.getExameHistopatologia()), InvestigacaoAgravoTuberculoseEnum.HistopatologiaEnum.values(), true, false);
        ddExameDeCultura = DropDownUtil.getIEnumDropDown(path(proxy.getExameCultura()), InvestigacaoAgravoTuberculoseEnum.PositivoNegativoNaoRelizadoNaoSeAplicaEnum.values(), true, true);
        ddExameDeCultura.setLabel(new Model(bundle("cultura")));
        ddTesteMolecularRapidoTB = DropDownUtil.getIEnumDropDown(path(proxy.getTesteMolecularRapido()), InvestigacaoAgravoTuberculoseEnum.TesteMolecularRapidoEnum.values(), true, false);
        ddTesteSensibilidade = DropDownUtil.getIEnumDropDown(path(proxy.getTesteSensibilidade()), InvestigacaoAgravoTuberculoseEnum.TesteSensibilidadeEnum.values(), true, true);

        containerExamesTestes.add(
                ddBaciloscopiaDeEscarro, ddRadiografiaTorax, ddHIV,
                ddTerapiaAntirretroviral, ddHistopatologia, ddExameDeCultura,
                ddTesteMolecularRapidoTB, ddTesteSensibilidade
        );

        getContainerInformacoesComplementares().add(containerExamesTestes);
    }

    private void criarObservacao(InvestigacaoAgravoTuberculose proxy) {
        containerObservacao = new WebMarkupContainer("containerObservacao");
        containerObservacao.setOutputMarkupPlaceholderTag(true);

        txtObservacao = new InputArea(path(proxy.getObservacao()));

        containerObservacao.add(txtObservacao);
        getContainerInformacoesComplementares().add(containerObservacao);
    }

    private void criarEncerramento(InvestigacaoAgravoTuberculose proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupPlaceholderTag(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }

}