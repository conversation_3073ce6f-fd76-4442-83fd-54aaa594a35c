package br.com.celk.view.basico.profissional.tabbedpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.ProfissionalHistorico;
import br.com.ksisolucoes.vo.cadsus.Vinculacao;
import br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipo;
import br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipoPK;
import br.com.ksisolucoes.vo.cadsus.VinculacaoTipo;
import br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlHistoricoVinculos extends Panel {

    private CompoundPropertyModel<List<ProfissionalHistorico>> dialogModel;
    private WebMarkupContainer dialogRoot;
    private AbstractAjaxButton btnFechar;
    private Table<ProfissionalHistorico> table;

    public PnlHistoricoVinculos(String id) {
        super(id);
        createDialogDetalhes();
    }

    private void createDialogDetalhes() {
        
        setOutputMarkupId(true);
        
        dialogModel = new CompoundPropertyModel(new ArrayList());

        dialogRoot = new WebMarkupContainer("dialogRoot", dialogModel);
        
        dialogRoot.setOutputMarkupId(true);
        
        dialogRoot.add(table = new Table("tableVinculos", getColumns(), getCollectionProvider()));
        dialogRoot.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        
        table.setScrollX("1500px");
        table.setScrollY("300px");
        table.populate();

        add(dialogRoot);
        
        
        btnFechar.setDefaultFormProcessing(false);
        
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setModelObject(List<ProfissionalHistorico> lst){
        dialogModel.setObject(lst);
    }
    
    public void update(AjaxRequestTarget target){
        target.add(dialogRoot);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(ProfissionalHistorico.class);

//        columns.add(getCustomColumn());
//        columns.add(new RowNumberColumn<ProfissionalHistorico>());
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade"), VOUtils.montarPath(ProfissionalHistorico.PROP_EMPRESA, Empresa.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cbo"), VOUtils.montarPath(ProfissionalHistorico.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO_FORMATADO)));
//        columns.add(columnFactory.createColumn(BundleManager.getString("vinculacao"), VOUtils.montarPath(ProfissionalHistorico.PROP_VINCULACAO_SUB_TIPO, VinculacaoSubTipo.PROP_ID, VinculacaoSubTipoPK.PROP_VINCULACAO_TIPO, VinculacaoTipo.PROP_ID, VinculacaoTipoPK.PROP_VINCULACAO, Vinculacao.PROP_DESCRICAO)));
//        columns.add(columnFactory.createColumn(BundleManager.getString("tipo"), VOUtils.montarPath(ProfissionalHistorico.PROP_VINCULACAO_SUB_TIPO, VinculacaoSubTipo.PROP_ID, VinculacaoSubTipoPK.PROP_VINCULACAO_TIPO, VinculacaoTipo.PROP_DESCRICAO)));
//        columns.add(columnFactory.createColumn(BundleManager.getString("subTipo"), VOUtils.montarPath(ProfissionalHistorico.PROP_VINCULACAO_SUB_TIPO, VinculacaoSubTipo.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataEntrada"), VOUtils.montarPath(ProfissionalHistorico.PROP_DATA_ENTRADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("competenciaInicial"), VOUtils.montarPath(ProfissionalHistorico.PROP_DESCRICAO_COMPETENCIA_INICIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataDesligamento"), VOUtils.montarPath(ProfissionalHistorico.PROP_DATA_DESLIGAMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("competenciaFinal"), VOUtils.montarPath(ProfissionalHistorico.PROP_DESCRICAO_COMPETENCIA_FINAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("usuario"), VOUtils.montarPath(ProfissionalHistorico.PROP_USUARIO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataAlteracao"), VOUtils.montarPath(ProfissionalHistorico.PROP_DATA_ATUALIZACAO)));

        return columns;
    }
    
    private CustomColumn getCustomColumn() {
        return new CustomColumn<ProfissionalHistorico>() {
            @Override
            public Component getComponent(String componentId, final ProfissionalHistorico rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return false;
                    }

                    @Override
                    public boolean isEditarVisible() {
                        return false;
                    }
                };
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dialogModel.getObject();
            }
        };
    }
}
