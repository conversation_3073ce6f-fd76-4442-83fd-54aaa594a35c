package br.com.celk.view.unidadesaude.odonto;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.methods.CoreMethods;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import ch.lambdaj.Lambda;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaSituacaoDentePage extends ConsultaPage<SituacaoDente, List<BuilderQueryCustom.QueryParameter>> {

    private String situacao;
    private String referencia;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("situacao"));
        form.add(new InputField("referencia"));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(SituacaoDente.class);
        SituacaoDente proxy = Lambda.on(SituacaoDente.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(bundle("referencia"), CoreMethods.path(proxy.getReferencia())));
        columns.add(columnFactory.createSortableColumn(bundle("situacao"), CoreMethods.path(proxy.getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("tipo"), path(proxy.getTipoSituacao()), CoreMethods.path(proxy.getDescricaoTipoSituacao())));
        columns.add(columnFactory.createSortableColumn(bundle("ordem"), CoreMethods.path(proxy.getOrdem())));
        return columns;
    }

    private CustomColumn<SituacaoDente> getCustomColumn() {
        return new CustomColumn<SituacaoDente>() {

            @Override
            public Component getComponent(String componentId, final SituacaoDente rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroSituacaoDentePage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AtendimentoFacade.class).excluirSituacaoDente(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroSituacaoDentePage(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return SituacaoDente.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(SituacaoDente.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(SituacaoDente.PROP_ORDEM), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SituacaoDente.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SituacaoDente.PROP_REFERENCIA), BuilderQueryCustom.QueryParameter.ILIKE, referencia));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroSituacaoDentePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaSituacaoDente");
    }

}
