package br.com.celk.view.vigilancia.cva.proprietarioresponsavel.dialog;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.cpffield.CpfField;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ComponentWicketUtil;
import br.com.celk.template.cadastro.panel.PnlCadastro;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class PnlCadastroProprietarioResponsavel extends PnlCadastro<CvaProprietarioResponsavel> {

    private InputField txtDescricao;
    private InputField txtNumero;
    private DateChooser dataNascimento;
    private InputField txtRg;
    private CpfField txtCnpjCpf;
    private InputField txtProfissao;
    private InputField txtComplemento;
    private DropDown dropDownSexo;
    private DropDown ddTipoPessoa;
    private AttributeModifier attributeModifierCnpj;
    private AttributeModifier attributeModifierCpf;
    private TelefoneField txtTelefone1;
    private TelefoneField txtTelefone2;
    private TelefoneField txtTelefone3;
    private InputField txtEmail;
    private InputArea txaObservacao;
    private CheckBoxLongValue cbxFlagEstrangeiro;
    private AutoCompleteConsultaPais autoCompleteConsultaPais;
    private PnlVigilanciaEndereco pnlVigilanciaEndereco;

    public PnlCadastroProprietarioResponsavel(String id) {
        super(id);
    }

    @Override
    public void init(Form form) {
        CvaProprietarioResponsavel proxy = on(CvaProprietarioResponsavel.class);

        form.add(txtDescricao = new RequiredInputField(path(proxy.getProprietarioResponsavel())));
        form.add(dropDownSexo = DropDownUtil.getSexoDropDown(path(proxy.getSexo()), true));
        form.add(dataNascimento = new DateChooser(path(proxy.getDataNascimento())));
        form.add(txtRg = new InputField(path(proxy.getRg())));
        form.add(ddTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getTipoPessoa()), CvaProprietarioResponsavel.TipoPessoa.values(), false, true));
        form.add(txtCnpjCpf = new CpfField(path(proxy.getCpf())));
        form.add(txtProfissao = new InputField(path(proxy.getProfissao())));
        form.add(txtComplemento = new InputField(path(proxy.getComplementoLogradouro())));
        form.add(txtNumero = new InputField(path(proxy.getNumeroLogradouro())));

        ddTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                changeDropdownTipoPessoa(target);
            }
        });

        attributeModifierCnpj = new AttributeModifier("class", "cnpj");
        attributeModifierCpf = new AttributeModifier("class", "cpf");

        form.add(autoCompleteConsultaPais = new AutoCompleteConsultaPais(path(proxy.getPaisNascimento())));
        autoCompleteConsultaPais.setEnabled(false);
        autoCompleteConsultaPais.setLabel(new Model(BundleManager.getString("paisOrigem")));

        form.add(cbxFlagEstrangeiro = new CheckBoxLongValue(path(proxy.getFlagEstrangeiro()), RepositoryComponentDefault.SIM_LONG));

        form.add(pnlVigilanciaEndereco = new PnlVigilanciaEndereco(path(proxy.getVigilanciaEndereco())));
        pnlVigilanciaEndereco.addRequired();

        cbxFlagEstrangeiro.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configNacionalidade(target);
            }
        });

        form.add(txtTelefone1 = new TelefoneField(path(proxy.getTelefone1())));
        form.add(txtTelefone2 = new TelefoneField(path(proxy.getTelefone2())));
        form.add(txtTelefone3 = new TelefoneField(path(proxy.getTelefone3())));
        form.add(txtEmail = new InputField(path(proxy.getEmail())));

        form.add(txaObservacao = new InputArea(path(proxy.getObservacao())));

        changeDropdownTipoPessoa(null);
    }

    private void changeDropdownTipoPessoa(AjaxRequestTarget target) {
        txtCnpjCpf.setRequired(true);
        txtCnpjCpf.addRequiredClass();

        if (ddTipoPessoa != null && ddTipoPessoa.getComponentValue() == null) {
            ddTipoPessoa.setComponentValue(CvaProprietarioResponsavel.TipoPessoa.FISICA.value());
        }

        boolean isPessoaJuridica = CvaProprietarioResponsavel.TipoPessoa.JURIDICA.value().equals(ddTipoPessoa.getModelObject());
        ComponentWicketUtil.onEventTipoPessoa(isPessoaJuridica, txtCnpjCpf, target);

        if (isPessoaJuridica) {
            dataNascimento.setRequired(false);
            dataNascimento.setEnabled(false);
            dataNascimento.removeRequiredClass();

            dropDownSexo.setRequired(false);
            dropDownSexo.setEnabled(false);
            dropDownSexo.removeRequiredClass();

            txtProfissao.setEnabled(false);
            txtRg.setEnabled(false);
        } else {
            dataNascimento.setRequired(true);
            dataNascimento.setEnabled(true);
            dataNascimento.addRequiredClass();

            dropDownSexo.setRequired(true);
            dropDownSexo.setEnabled(true);
            dropDownSexo.addRequiredClass();

            txtProfissao.setEnabled(true);
            txtRg.setEnabled(true);
        }

        if (target != null) {
            txtProfissao.limpar(target);
            txtCnpjCpf.limpar(target);
            dataNascimento.limpar(target);
            dropDownSexo.limpar(target);
            txtRg.limpar(target);
            target.add(txtCnpjCpf);
        }
    }

    private void configNacionalidade(AjaxRequestTarget target) {
        if (RepositoryComponentDefault.SIM_LONG.equals(getObject().getFlagEstrangeiro())) {
            autoCompleteConsultaPais.setEnabled(true);
            autoCompleteConsultaPais.setRequired(true);
            autoCompleteConsultaPais.getTxtDescricao().addRequiredClass();
            getObject().setVigilanciaEndereco(null);
            pnlVigilanciaEndereco.limpar(target);
            pnlVigilanciaEndereco.removeRequired();
            pnlVigilanciaEndereco.setEnabled(false);
            txtNumero.setEnabled(false);
        } else {
            autoCompleteConsultaPais.setRequired(false);
            autoCompleteConsultaPais.getTxtDescricao().removeRequiredClass();
            autoCompleteConsultaPais.setEnabled(false);
            autoCompleteConsultaPais.limpar(target);
            pnlVigilanciaEndereco.limpar(target);
            pnlVigilanciaEndereco.setEnabled(true);
            pnlVigilanciaEndereco.addRequired();
            txtNumero.setEnabled(true);
        }

        target.add(autoCompleteConsultaPais);
        target.add(pnlVigilanciaEndereco);
        target.add(pnlVigilanciaEndereco);
        target.add(txtNumero);
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        super.limpar(target);
        ddTipoPessoa.limpar(target);
        txtDescricao.limpar(target);
        dataNascimento.limpar(target);
        dropDownSexo.limpar(target);
        txtCnpjCpf.limpar(target);
        txtRg.limpar(target);
        txtProfissao.limpar(target);
        autoCompleteConsultaPais.limpar(target);
        cbxFlagEstrangeiro.limpar(target);
        pnlVigilanciaEndereco.limpar(target);
        txtNumero.limpar(target);
        txtComplemento.limpar(target);
        txtTelefone1.limpar(target);
        txtTelefone2.limpar(target);
        txtTelefone3.limpar(target);
        txtEmail.limpar(target);
        txaObservacao.limpar(target);
        configNacionalidade(target);
        changeDropdownTipoPessoa(target);
    }

    @Override
    public Class<CvaProprietarioResponsavel> getReferenceClass() {
        return CvaProprietarioResponsavel.class;
    }

    public FormComponent getFocusComponent() {
        return txtDescricao;
    }

}
