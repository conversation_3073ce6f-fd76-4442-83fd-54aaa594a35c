package br.com.celk.view.consorcio.talonarioTalidomina.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaTalonarioDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerTalonario extends Panel implements IRestricaoContainer<QueryConsultaTalonarioDTOParam> {

    private InputField<String> txtNumeracaoInicial;
    
    private QueryConsultaTalonarioDTOParam param = new QueryConsultaTalonarioDTOParam();
    
    public RestricaoContainerTalonario(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtNumeracaoInicial = new InputField<String>("numeracaoInicial"));
        
        add(root);
    }

    @Override
    public QueryConsultaTalonarioDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtNumeracaoInicial.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtNumeracaoInicial;
    }

}
