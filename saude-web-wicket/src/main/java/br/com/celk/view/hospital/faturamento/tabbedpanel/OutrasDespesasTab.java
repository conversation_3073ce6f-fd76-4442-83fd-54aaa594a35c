package br.com.celk.view.hospital.faturamento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.view.hospital.faturamento.dialogs.DlgLancamentosConfirmadosDespesas;
import br.com.celk.view.hospital.faturamento.dialogs.DlgNovoLancamentoDespesa;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class OutrasDespesasTab extends TabPanel<FechamentoContaDTO> {

    private Table<ItemContaPaciente> tabela;
    private DlgNovoLancamentoDespesa dlgNovoLancamento;
    private DlgLancamentosConfirmadosDespesas dlgLancamentosConfirmados;
    private AbstractAjaxButton btnLancamentosConfirmados;
    private FechamentoContaDTO fechamentoContaDTO;
    private List<ItemContaPaciente> procedimentosList;
    private List<ItemContaPaciente> procedimentosConfirmadosList;
    private boolean btnConfirmadoOK;
    private int idxItem = -1;

    public OutrasDespesasTab(String id, FechamentoContaDTO object) {
        super(id, object);
        this.fechamentoContaDTO = object;
        init();
    }

    private void init() {
        add(tabela = new Table("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();

        add(new AbstractAjaxButton("btnNovoLancamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                idxItem = -1;
                ItemContaPaciente itemContaPaciente = new ItemContaPaciente();
                itemContaPaciente.setContaPaciente(fechamentoContaDTO.getContaPaciente());
                itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
                viewDlgNovoLancamento(target, itemContaPaciente);
            }
        });

        add(getBtnLanctosConfirmados());
    }

    private void viewDlgNovoLancamento(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgNovoLancamento == null) {
            WindowUtil.addModal(target, this, dlgNovoLancamento = new DlgNovoLancamentoDespesa(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    adicionaItem(target, itemContaPaciente);
                }
            });
        }

        dlgNovoLancamento.show(target, itemContaPaciente);
    }

    private void adicionaItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (idxItem >= 0) {
            fechamentoContaDTO.getListaItensContaPaciente().remove(idxItem);
            fechamentoContaDTO.getListaItensContaPaciente().add(idxItem, itemContaPaciente);
        } else {
            fechamentoContaDTO.getListaItensContaPaciente().add(itemContaPaciente);
        }

        tabela.update(target);

    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("procedimento"), proxy.getProcedimento().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(createSortableColumn(bundle("preco"), proxy.getPrecoUnitario()));
        columns.add(createSortableColumn(bundle("total"), proxy.getValorTotal()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente icp) throws ValidacaoException, DAOException {
                        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
                            ItemContaPaciente item = fechamentoContaDTO.getListaItensContaPaciente().get(i);
                            if (item == icp) {
                                idxItem = i;
                                break;
                            }
                        }

                        viewDlgNovoLancamento(target, (ItemContaPaciente) SerializationUtils.clone(icp));
                    }
                });

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CONFIRMADO.value());
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CANCELADO.value());
                    }
                });
            }
        };
    }

    private void changeStatus(AjaxRequestTarget target, ItemContaPaciente icp, Long status) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == icp) {
                fechamentoContaDTO.getListaItensContaPaciente().get(i).setStatus(status);
                break;
            }
        }

        tabela.update(target);
        target.add(getBtnLanctosConfirmados());
    }

    private AbstractAjaxButton getBtnLanctosConfirmados() {
        if (btnLancamentosConfirmados == null) {
            btnLancamentosConfirmados = new AbstractAjaxButton("btnLancamentosConfirmados") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    viewDlgLanctosConfirmados(target);
                }
            };
        }

        btnLancamentosConfirmados.setEnabled(dlgLancamentosConfirmados != null && !dlgLancamentosConfirmados.getListaItens().isEmpty() || btnConfirmadoOK);

        return btnLancamentosConfirmados;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                procedimentosList = new ArrayList();

                for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
                    if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.OUTRAS_DESPESAS.value())) {
                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                            procedimentosList.add(itemContaPaciente);
                        }

                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                            btnConfirmadoOK = true;
                            getBtnLanctosConfirmados();
                        }
                    }
                }

                return procedimentosList;
            }
        };
    }

    private List<ItemContaPaciente> getListConfirmados() {
        procedimentosConfirmadosList = new ArrayList();

        for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.OUTRAS_DESPESAS.value()) && itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                procedimentosConfirmadosList.add(itemContaPaciente);
            }
        }

        return procedimentosConfirmadosList;
    }

    private void viewDlgLanctosConfirmados(AjaxRequestTarget target) {
        if (dlgLancamentosConfirmados == null) {
            WindowUtil.addModal(target, this, dlgLancamentosConfirmados = new DlgLancamentosConfirmadosDespesas(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    if (dlgLancamentosConfirmados.getListaItens().isEmpty()) {
                        btnConfirmadoOK = false;
                    }

                    target.add(getBtnLanctosConfirmados());
                }

                @Override
                public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    changeStatus(target, itemContaPaciente, ItemContaPaciente.Status.ABERTO.value());
                }
            });
        }

        if (CollectionUtils.isNotNullEmpty(getListConfirmados())) {
            dlgLancamentosConfirmados.setListItem(target, getListConfirmados());
        }

        target.add(getBtnLanctosConfirmados());
        dlgLancamentosConfirmados.show(target);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("outrasDespesas");
    }
}
