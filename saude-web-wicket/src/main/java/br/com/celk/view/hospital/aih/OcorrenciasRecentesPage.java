package br.com.celk.view.hospital.aih;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.atendimento.prontuario.tablecolumn.ClassificacaoRiscoColumnT;
import br.com.ksisolucoes.agendamento.regulacaoleito.OcorrenciasRecentesAihDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoLeito;
import org.apache.commons.collections.CollectionUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Clayton Andrade
 */
@Private
public class OcorrenciasRecentesPage extends BasePage {

    private static final String CSS_FILE = "OcorrenciasRecentesPage.css";

    private List<OcorrenciasRecentesAihDTO> ocorrenciasRecentesAihList = new ArrayList();
    private Table<OcorrenciasRecentesAihDTO> tblOcorrencias;

    public OcorrenciasRecentesPage() {
        init();
    }

    public OcorrenciasRecentesPage(List<OcorrenciasRecentesAihDTO> list) {
        this.ocorrenciasRecentesAihList = list;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(tblOcorrencias = new Table("tblOcorrencias", getColumns(), getCollectionProvider()));
        tblOcorrencias.populate();

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaRegulacaoAihPage());
            }
        });

        carregarOcorrencias(null);

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        OcorrenciasRecentesAihDTO proxy = on(OcorrenciasRecentesAihDTO.class);

        columns.add(getActionColumn());
        columns.add(new ClassificacaoRiscoColumnT<OcorrenciasRecentesAihDTO>("CR", path(proxy.getAih().getClassificacaoRisco().getNivelGravidade())) {
            @Override
            public ClassificacaoRisco getClassificacaoRiscoFromObject(OcorrenciasRecentesAihDTO object) {
                return object.getAih().getClassificacaoRisco();
            }
        });
        columns.add(createColumn(bundle("aih"), proxy.getAih().getCodigo()));
        columns.add(createColumn(bundle("dataOcorrencia"), proxy.getOcorrenciaAih().getDataCadastro()));
        columns.add(createColumn(bundle("paciente"), proxy.getAih().getUsuarioCadSus().getNomeSocial()));
        columns.add(createColumn(bundle("sexo"), proxy.getAih().getUsuarioCadSus().getSexoFormatado()));
        columns.add(createColumn(bundle("caraterInternacao"), proxy.getAih().getDescricaoCaraterInternacao()));
        columns.add(createColumn(bundle("situacao"), proxy.getAih().getDescricaoStatus()));
        columns.add(createColumn(bundle("estabelecimento"), proxy.getAih().getEmpresa().getDescricao()));
        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return carregarOcorrencias(null);
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("ocorrenciasRecentes");
    }

    private List<OcorrenciasRecentesAihDTO> carregarOcorrencias(AjaxRequestTarget target) {
        try {
            ocorrenciasRecentesAihList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarOcorrenciasRecentes();
            if (target != null) {
                tblOcorrencias.update(target);
            }

        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        } finally {
            if (CollectionUtils.isEmpty(ocorrenciasRecentesAihList)) {
                ocorrenciasRecentesAihList = new ArrayList<>();
            }
        }

        return ocorrenciasRecentesAihList;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<OcorrenciasRecentesAihDTO>() {
            @Override
            public void customizeColumn(OcorrenciasRecentesAihDTO rowObject) {

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<OcorrenciasRecentesAihDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, OcorrenciasRecentesAihDTO modelObject) throws ValidacaoException, DAOException {
                        AutorizacaoInternacaoHospitalarDTO dto = new AutorizacaoInternacaoHospitalarDTO();
                        dto.setAutorizacaoInternacaoHospitalar(modelObject.getAih());
                        dto.setProcedimentoCompetenciaSolicitado(carregarProcedimentoSolicitado(dto).getProcedimentoCompetenciaSolicitado());
                        dto.setProcedimentoCompetenciaRealizado(carregarProcedimentoRealizado(dto).getProcedimentoCompetenciaRealizado());

                        setResponsePage(new ManutencaoAihAutorizadaPage(true, dto, true));
                    }
                });
            }
        };
    }

    private AutorizacaoInternacaoHospitalarDTO carregarProcedimentoSolicitado(AutorizacaoInternacaoHospitalarDTO dto) {
        ProcedimentoCompetencia proxy = on(ProcedimentoCompetencia.class);
        ProcedimentoCompetencia pc = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addProperties(new HQLProperties(ProcedimentoCompetencia.class).getProperties())
                .addProperty(path(proxy.getId().getProcedimento().getReferencia()))
                .addProperty(path(proxy.getId().getProcedimento().getCodigo()))
                .addProperty(path(proxy.getId().getProcedimento().getDescricao()))
                .addProperty(path(proxy.getId().getDataCompetencia()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProcedimento()), dto.getAutorizacaoInternacaoHospitalar().getProcedimentoSolicitado()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getDataCompetencia()), CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento()))
                .start().getVO();

        if (pc != null) {
            dto.setProcedimentoCompetenciaSolicitado(pc);

            ProcedimentoLeito proxyPL = on(ProcedimentoLeito.class);
            ProcedimentoLeito pl = LoadManager.getInstance(ProcedimentoLeito.class)
                    .addProperty(path(proxyPL.getId().getProcedimentoTipoLeito().getCodigo()))
                    .addProperty(path(proxyPL.getId().getProcedimentoTipoLeito().getDescricao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyPL.getId().getProcedimentoCompetencia().getId().getProcedimento()), pc.getId().getProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyPL.getId().getProcedimentoCompetencia().getId().getDataCompetencia()), pc.getId().getDataCompetencia()))
                    .setMaxResults(1)
                    .start().getVO();

            if (pl != null) {
                dto.setProcedimentoLeito(pl);
            }

        }

        return dto;
    }
    private AutorizacaoInternacaoHospitalarDTO carregarProcedimentoRealizado(AutorizacaoInternacaoHospitalarDTO dto) {
        ProcedimentoCompetencia proxy = on(ProcedimentoCompetencia.class);
        ProcedimentoCompetencia pc = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addProperties(new HQLProperties(ProcedimentoCompetencia.class).getProperties())
                .addProperty(path(proxy.getId().getProcedimento().getReferencia()))
                .addProperty(path(proxy.getId().getProcedimento().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProcedimento()), dto.getAutorizacaoInternacaoHospitalar().getProcedimentoRealizado()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getDataCompetencia()), CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento()))
                .start().getVO();

        if (pc != null) {
            dto.setProcedimentoCompetenciaRealizado(pc);
        }

        return dto;
    }
}