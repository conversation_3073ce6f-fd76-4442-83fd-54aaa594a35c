package br.com.celk.view.frota.tipoveiculo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.frota.TipoVeiculo;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoVeiculoPage extends CadastroPage<TipoVeiculo> {

    private RequiredInputField txtDescricao;
    private InputField txtReferencia;
    private DropDown<Long> dropDownTransportePaciente;

    public CadastroTipoVeiculoPage(TipoVeiculo object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoVeiculoPage(TipoVeiculo object) {
        super(object);
    }

    public CadastroTipoVeiculoPage() {
    }

    @Override
    public void init(Form form) {
        form.add(txtReferencia = new UpperField("referencia"));
        form.add(txtDescricao = new RequiredInputField<String>("descricao"));
        form.add(dropDownTransportePaciente = DropDownUtil.getNaoSimLongDropDown("flagTransportePaciente"));

    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<TipoVeiculo> getReferenceClass() {
        return TipoVeiculo.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoVeiculoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoVeiculo");
    }

}
