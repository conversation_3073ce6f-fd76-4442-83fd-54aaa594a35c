package br.com.celk.view.unidadesaude.tabelacbogrupoatendimento;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboGrupoAtendimento;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroCboGrupoAtendimentoPage extends CadastroPage<TabelaCboGrupoAtendimento> {
    private InputField txtDescricao;
    private String descricao;
    private TabelaCbo tabelaCbo;
    private Table<TabelaCbo> table;
    private List<TabelaCbo> lstCboGrupoAtendimento = new ArrayList<TabelaCbo>();
    
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;

    public CadastroCboGrupoAtendimentoPage() {
    }

    public CadastroCboGrupoAtendimentoPage(TabelaCboGrupoAtendimento object) {
        this(object, false);
    }

    public CadastroCboGrupoAtendimentoPage(TabelaCboGrupoAtendimento object, boolean viewOnly) {
        super(object, viewOnly);
        
        carregarCbos();
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new InputField("descricao"));
        form.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbo",new PropertyModel(this, "tabelaCbo")));
        autoCompleteConsultaTabelaCbo.setFiltrarAtivos(true);
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        table.populate();        
    }
    
    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(TabelaCbo.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("cbo"), VOUtils.montarPath(TabelaCbo.PROP_DESCRICAO_FORMATADO)));
        
        return columns;
    }
    
    private CustomColumn<TabelaCbo> getCustomColumn() {
        return new CustomColumn<TabelaCbo>() {
            @Override
            public Component getComponent(String componentId, final TabelaCbo rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        lstCboGrupoAtendimento.remove(rowObject);
                        table.update(target);
                    }
                };
            }
        };
    }
    
    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstCboGrupoAtendimento;
            }
        };
    }

    private void carregarCbos() {
            List<TabelaCbo> list = LoadManager.getInstance(TabelaCbo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TabelaCbo.PROP_TABELA_CBO_GRUPO_ATENDIMENTO), getForm().getModelObject()))
                .start().getList();
                this.lstCboGrupoAtendimento = new ArrayList<TabelaCbo>(list);
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tabelaCbo == null) {
            throw new ValidacaoException(BundleManager.getString("informeCbo"));
        }
        boolean adicionar = true;
        for (TabelaCbo _cbo : lstCboGrupoAtendimento) {
            if (_cbo.equals(tabelaCbo)) {
                adicionar = false;
            }
        }
        if (adicionar) {
            lstCboGrupoAtendimento.add(tabelaCbo);
            table.update(target);
        }
        limpar(target);
        autoCompleteConsultaTabelaCbo.focus(target);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroGrupoCboAtendimento");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Object salvar(TabelaCboGrupoAtendimento object) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarTabelaCboGrupoAtendimento(object, lstCboGrupoAtendimento);
        return null;
    }

    public void limpar(AjaxRequestTarget target) {
        this.autoCompleteConsultaTabelaCbo.limpar(target);
    }

    @Override
    public Class getReferenceClass() {
        return TabelaCboGrupoAtendimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaCboGrupoAtendimentoPage.class;
    }
}
