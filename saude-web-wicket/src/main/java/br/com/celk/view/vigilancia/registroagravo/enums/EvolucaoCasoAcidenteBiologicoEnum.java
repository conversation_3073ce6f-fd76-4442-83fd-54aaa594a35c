package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

/**
 * <AUTHOR>
 */

public enum EvolucaoCasoAcidenteBiologicoEnum implements IEnum {
    ALTA_COM_CONVERSAO_SOROLOGICA(1L, Bundle.getStringApplication("altaConversaoSorologica")),
    ALTA_SEM_CONVERSAO_SOROLOGICA(2L, Bundle.getStringApplication("altaSemConversaoSorologica")),
    ALTA_PACIENTE_FONTE_NEGATIVA(3L, Bundle.getStringApplication("altaFonteNegativa")),
    ABANDONO(4L, Bundle.getStringApplication("abandono")),
    OBITO_POR_ACIDENTE(5L, Bundle.getStringApplication("obitoAcidenteBiologico")),
    OBITO_POR_OUTRA_CAUSA(6L, Bundle.getStringApplication("obitoOutraCausaSingular")),
    IGNORADO(9L, Bundle.getStringApplication("ignorado"));

    private Long value;
    private String descricao;

    EvolucaoCasoAcidenteBiologicoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static EvolucaoCasoAcidenteBiologicoEnum valueOf(Long value) {
        for (EvolucaoCasoAcidenteBiologicoEnum v : EvolucaoCasoAcidenteBiologicoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
