package br.com.celk.view.vigilancia.dengue.microarea;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ConsultaMicroAreaVigilanciaPage extends ConsultaPage<DengueMicroAreaVigilancia, List<BuilderQueryCustom.QueryParameter>> {

    private String area;
    private String microArea;
    private Long situacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<String>("area"));
        form.add(new InputField<String>("microArea"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", DengueMicroAreaVigilancia.Situacao.values()));

        if (situacao == null) {
            situacao = DengueMicroAreaVigilancia.Situacao.ATIVO.value();
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        DengueMicroAreaVigilancia proxy = on(DengueMicroAreaVigilancia.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("dataCadastro"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("microArea"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("area"), proxy.getDengueAreaVigilancia().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getDescricaoSituacao()));
        return columns;
    }

    private CustomColumn<DengueMicroAreaVigilancia> getCustomColumn() {
        return new CustomColumn<DengueMicroAreaVigilancia>() {

            @Override
            public Component getComponent(String componentId, final DengueMicroAreaVigilancia rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMicroAreaVigilanciaPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        rowObject.setSituacao(DengueMicroAreaVigilancia.Situacao.INATIVO.value());
                        BOFactoryWicket.save(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public boolean isExcluirVisible() {
                        return false;
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMicroAreaVigilanciaPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return DengueMicroAreaVigilancia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(DengueMicroAreaVigilancia.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, DengueAreaVigilancia.PROP_CODIGO),
                                VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, DengueAreaVigilancia.PROP_DESCRICAO),
                                VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, DengueAreaVigilancia.PROP_SITUACAO),
                                VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, DengueAreaVigilancia.PROP_DATA_CADASTRO),});

            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DATA_CADASTRO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(DengueMicroAreaVigilancia.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, microArea));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueMicroAreaVigilancia.PROP_DENGUE_AREA_VIGILANCIA, DengueAreaVigilancia.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, area));
        parameters.add(new QueryCustom.QueryCustomParameter(DengueMicroAreaVigilancia.PROP_SITUACAO, situacao));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroMicroAreaVigilanciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaMicroAreaVigilancia");
    }

}
