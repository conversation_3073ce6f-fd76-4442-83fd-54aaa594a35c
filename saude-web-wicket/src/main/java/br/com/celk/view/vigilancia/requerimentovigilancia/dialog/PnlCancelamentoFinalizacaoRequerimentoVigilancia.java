package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.service.web.vigilancia.InspecaoEstabelecimentoService;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaCancelamentoFinalizacaoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia.Situacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlvara;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAutorizacaoSanitaria;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoEvento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlCancelamentoFinalizacaoRequerimentoVigilancia extends Panel {
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private Form form;
    private RequerimentoVigilancia rv;
    private String motivo;
    private String observacaoDestaqueAlvara;
    private String observacaoDestaqueCredenciamento;
    private InputArea txaMotivo;
    private Situacao situacao;
    private DropDown dropDrownSituacao;
    private WebMarkupContainer containerSituacao;
    private WebMarkupContainer containerDataFinalizacao;
    private WebMarkupContainer containerObservacaoAlvara;
    private WebMarkupContainer containerObservacaoCredenciamento;
    private WebMarkupContainer containerDesobrigacaoAlvara;
    private boolean situacaoObrigatorio;
    private boolean acaoFinalizar;
    private DateChooser dcDataFinalizacao;
    private Date dataFinalizacao;
    private TextArea txtObservacao;
    private TextArea txtObservacaoCredenciamento;
    private WebMarkupContainer containerDeferido;
    private SimpleSelectionTable<EstabelecimentoAtividade> tblAtividades;
    private List<EstabelecimentoAtividade> atividades;

    private Long flagDesobrigacaoAlvara;
    private DropDown dropDownDesobrigacaoAlvara;

    public PnlCancelamentoFinalizacaoRequerimentoVigilancia(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        form.setMultiPart(true);
        containerSituacao = new WebMarkupContainer("containerSituacao");
        containerSituacao.setOutputMarkupId(true);
        containerDataFinalizacao = new WebMarkupContainer("containerDataFinalizacao");
        containerDataFinalizacao.setOutputMarkupId(true);
        containerObservacaoAlvara = new WebMarkupContainer("containerObservacaoAlvara");
        containerObservacaoAlvara.setOutputMarkupId(true);
        containerObservacaoCredenciamento = new WebMarkupContainer("containerObservacaoCredenciamento");
        containerObservacaoCredenciamento.setOutputMarkupId(true);
        containerDesobrigacaoAlvara = new WebMarkupContainer("containerDesobrigacaoAlvara");
        containerDesobrigacaoAlvara.setOutputMarkupId(true);
        containerDesobrigacaoAlvara.setOutputMarkupPlaceholderTag(true);

        containerSituacao.add(dropDrownSituacao = new DropDown("situacao", new PropertyModel<Situacao>(this, "situacao")));
        dropDrownSituacao.addAjaxUpdateValue();
        dropDrownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                mudarVisibiliadeAnexo(ajaxRequestTarget);
                mudarVisibilidadeDesobrigacao(ajaxRequestTarget);
            }
        });

        dropDownDesobrigacaoAlvara = DropDownUtil.getNaoSimLongDropDown("flagDesobrigacaoAlvara", new PropertyModel<Long>(this, "flagDesobrigacaoAlvara"));
        containerDesobrigacaoAlvara.add(dropDownDesobrigacaoAlvara);
        containerDesobrigacaoAlvara.setVisible(false);

        containerDataFinalizacao.add(dcDataFinalizacao = new DateChooser("dataFinalizacao", new PropertyModel<Date>(this, "dataFinalizacao")));
        dcDataFinalizacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dcDataFinalizacao.setComponentValue(DataUtil.getDataAtual());

        form.add(txaMotivo = new InputArea("motivo", new PropertyModel<String>(this, "motivo")));
        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCadastro(target)) {
                    RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto = new RequerimentoVigilanciaCancelamentoFinalizacaoDTO();
                    dto.setRequerimentoVigilancia(rv);
                    dto.setDataFinalizacao(dataFinalizacao);
                    dto.setMotivo(motivo);
                    dto.setObservacaoAlvara(observacaoDestaqueAlvara);
                    dto.setObservacaoCredenciamento(observacaoDestaqueCredenciamento);
                    dto.setFlagDesobrigacaoAlvara(flagDesobrigacaoAlvara);
                    if (situacao != null) {
                        dto.setSituacao(situacao);
                    }
                    if(pnlRequerimentoVigilanciaAnexoDTO != null) {
                        dto.setPnlRequerimentoVigilanciaAnexoDTO(pnlRequerimentoVigilanciaAnexoDTO);
                    }

                    if (atividades != null && !atividades.isEmpty()) {
                        dto.setAtividades(atividades);
                    }

                    onConfirmar(target, dto);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        containerObservacaoAlvara.add(txtObservacao = new TextArea("observacaoDestaqueAlvara", new PropertyModel<String>(this, "observacaoDestaqueAlvara")));
        txtObservacao.setOutputMarkupId(true);
        containerObservacaoAlvara.setVisible(false);

        containerObservacaoCredenciamento.add(txtObservacaoCredenciamento = new TextArea("observacaoDestaqueCredenciamento", new PropertyModel<String>(this, "observacaoDestaqueCredenciamento")));
        txtObservacaoCredenciamento.setOutputMarkupId(true);
        containerObservacaoCredenciamento.setVisible(false);

        form.add(containerSituacao);
        form.add(containerDataFinalizacao);
        form.add(containerObservacaoAlvara);
        form.add(containerObservacaoCredenciamento);
        form.add(containerDesobrigacaoAlvara);

        containerDeferido = new WebMarkupContainer("containerDeferido");
        pnlRequerimentoVigilanciaAnexoDTO = new PnlRequerimentoVigilanciaAnexoDTO();
        containerDeferido.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(pnlRequerimentoVigilanciaAnexoDTO, true, false, true, false));
        pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);

        tblAtividades = new SimpleSelectionTable("tblAtividades", getColumnsItensAtvd(), getCollectionProviderItensAtvd());
        tblAtividades.addSelectionAction(new ISelectionAction<EstabelecimentoAtividade>() {
            @Override
            public void onSelection(AjaxRequestTarget target, EstabelecimentoAtividade estabelecimentoAtividade) {
                for (EstabelecimentoAtividade ea : atividades) {
                    if (ea == estabelecimentoAtividade) {
                        ea.setAtividadeLicenciavelPrincipal(RepositoryComponentDefault.SIM_LONG);
                        ea.setAtividadeLicenciavel(RepositoryComponentDefault.NAO_LONG);
                    } else {
                        ea.setAtividadeLicenciavelPrincipal(RepositoryComponentDefault.NAO_LONG);
                        ea.setAtividadeLicenciavel(RepositoryComponentDefault.SIM_LONG);
                    }
                }
            }
        });
        tblAtividades.populate();

        containerDeferido.add(tblAtividades);

        containerDeferido.setOutputMarkupPlaceholderTag(true);
        containerDeferido.setVisible(false);
        form.add(containerDeferido);
        add(form);
    }

    private void selecionaAtividadePrincipal() {
        for (EstabelecimentoAtividade ea : atividades) {
            if (RepositoryComponentDefault.SIM_LONG.equals(ea.getFlagPrincipal())) {
                ea.setAtividadeLicenciavelPrincipal(RepositoryComponentDefault.SIM_LONG);
                ea.setAtividadeLicenciavel(RepositoryComponentDefault.NAO_LONG);
                tblAtividades.setSelectedObject(ea);
            }else{
                ea.setAtividadeLicenciavelPrincipal(RepositoryComponentDefault.NAO_LONG);
                ea.setAtividadeLicenciavel(RepositoryComponentDefault.SIM_LONG);
            }
        }
    }

    private List<IColumn> getColumnsItensAtvd() {
        List<IColumn> columns = new ArrayList<IColumn>();

        EstabelecimentoAtividade proxy = on(EstabelecimentoAtividade.class);

        columns.add(getActionColumnAtvd());
        columns.add(createColumn(VigilanciaHelper.isGestaoAtividadeCnae() ? bundle("cnae") : bundle("atividade"), proxy.getAtividadeEstabelecimento().getDescricao()));
        return columns;
    }

    private ICollectionProvider getCollectionProviderItensAtvd() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return atividades;
            }
        };
    }

    private IColumn getActionColumnAtvd() {
        return new MultipleActionCustomColumn<EstabelecimentoAtividade>() {
            @Override
            public void customizeColumn(EstabelecimentoAtividade rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoAtividade>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoAtividade eAtividade) throws ValidacaoException, DAOException {
                        if (!TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(rv.getTipoDocumento()) && atividades.size() == 1) {
                           throw new ValidacaoException(Bundle.getStringApplication("msg_deve_existir_pelo_menos_uma_atividade"));
                        }
                        CrudUtils.removerItem(target, tblAtividades, atividades, eAtividade);
                        EstabelecimentoAtividade objSelecionado = tblAtividades.getSelectedObject();
                        tblAtividades.update(target);
                        tblAtividades.setSelectedObject(objSelecionado);
                        target.add(containerDeferido);
                    }
                });
            }
        };
    }

    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if (acaoFinalizar && dataFinalizacao == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_finalizacao"));
            } else if (dataFinalizacao != null && Data.adjustRangeHour(dataFinalizacao).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_finalizacao_deve_ser_menor_igual_data_atual"));
            }
            if (situacaoObrigatorio && situacao == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_situacao"));
            } else if ((!acaoFinalizar || situacaoObrigatorio && Situacao.INDEFERIDO.value().equals(situacao.value())) && "".equals(Coalesce.asString(motivo))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
            if (dataFinalizacao != null && dataFinalizacao.before(DataUtil.getDataHora(rv.getDataRequerimento(), "00:00"))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_finalizacao_inferior_data_requerimento"));
            }
            if (!atividades.isEmpty() && !VigilanciaHelper.hasAtividadeLicenciavelPrincipal(atividades)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_deve_existir_pelo_menos_uma_atividade"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    private void mudarVisibiliadeAnexo(AjaxRequestTarget target) {

        if (situacao != null && Situacao.DEFERIDO.value().equals(situacao.value())) {
            containerDeferido.setVisible(true);
            if (target != null) {
                target.appendJavaScript("document.getElementsByClassName('w_content_container')[0].style='overflow-y: scroll; overflow-x:hidden; height:550px;'");
                target.appendJavaScript("document.getElementsByClassName('wicket-modal')[0].style.width='1020px';");
            }
        } else {
            containerDeferido.setVisible(false);
            pnlRequerimentoVigilanciaAnexo.limpar(target);
            if (target != null) {
                target.appendJavaScript("document.getElementsByClassName('w_content_container')[0].style='height:220px;'");
                target.appendJavaScript("document.getElementsByClassName('wicket-modal')[0].style.width='550px';");
            }
        }
        if (target != null) {
            target.add(containerDeferido);
        }
    }

    private void mudarVisibilidadeDesobrigacao(AjaxRequestTarget ajaxRequestTarget) {
        if(situacao != null
                && Situacao.DEFERIDO.value().equals(situacao.value())
                && VigilanciaHelper.isDesobrigacaoAtiva()
                && estabelecimentoBaixoRIsco(rv)
                && TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(rv.getTipoDocumento()))
        {
           containerDesobrigacaoAlvara.setVisible(true);
        }else {
            containerDesobrigacaoAlvara.setVisible(false);
        }
        ajaxRequestTarget.add(containerDesobrigacaoAlvara);
    }

    private boolean estabelecimentoBaixoRIsco(RequerimentoVigilancia rv) {
        if (rv != null && rv.getEstabelecimento() != null) {
            List<EstabelecimentoAtividade> estabelecimentoAtividades = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(GrupoEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, rv.getEstabelecimento()))
                    .start().getList();
            ClassificacaoGrupoEstabelecimento classificacao = InspecaoEstabelecimentoService.getInstance().getClassificacaoRiscoAtividadesEstabelecimento(estabelecimentoAtividades);
            if (classificacao != null) {
                if (classificacao.getCodigo().equals(ClassificacaoGrupoEstabelecimento.RiscoClassificacaoGrupo.BAIXO_RISCO.value())) {
                    return true;
                }
            }
        }
        return false;
    }

    public void setObject(AjaxRequestTarget target, RequerimentoVigilancia rv, boolean acaoFinalizar, boolean situacaoObrigatorio) {
        txtObservacao.setModelObject(null);
        this.rv = rv;
        this.situacaoObrigatorio = situacaoObrigatorio;
        this.acaoFinalizar = acaoFinalizar;
        this.motivo = null;
        this.situacao = null;
        this.dataFinalizacao = null;
        this.observacaoDestaqueAlvara = null;
        txaMotivo.limpar(target);
        dropDrownSituacao.limpar(target);
        dcDataFinalizacao.limpar(target);
        pnlRequerimentoVigilanciaAnexo.limpar(target);
        target.focusComponent(txaMotivo);

        if (situacaoObrigatorio) {
            containerSituacao.setVisible(true);
            target.focusComponent(dropDrownSituacao);
        } else {
            containerSituacao.setVisible(false);
        }
        if (acaoFinalizar) {
            containerDataFinalizacao.setVisible(true);
            target.focusComponent(dcDataFinalizacao);
        } else {
            containerDataFinalizacao.setVisible(false);
        }
        if (acaoFinalizar && TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value().equals(rv.getTipoDocumento())) {
            containerObservacaoCredenciamento.setVisible(true);
        } else if (acaoFinalizar && (TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value().equals(rv.getTipoDocumento())
                || TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(rv.getTipoDocumento()))) {
            containerObservacaoAlvara.setVisible(true);
            if (TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value().equals(rv.getTipoDocumento())) {
                RequerimentoEvento requerimentoEvento = LoadManager.getInstance(RequerimentoEvento.class)
                        .addProperty(RequerimentoEvento.PROP_OBSERVACAO_DESTAQUE_ALVARA)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoEvento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), rv.getCodigo()))
                        .setMaxResults(1).start().getVO();
                observacaoDestaqueAlvara = requerimentoEvento.getObservacaoDestaqueAlvara();
            } else if (TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value().equals(rv.getTipoDocumento())) {
                RequerimentoAutorizacaoSanitaria requerimentoAutorizacaoSanitaria = LoadManager.getInstance(RequerimentoAutorizacaoSanitaria.class)
                        .addProperty(RequerimentoAutorizacaoSanitaria.PROP_DESCRICAO_OBSERVACAO_DESTAQUE)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoAutorizacaoSanitaria.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), rv.getCodigo()))
                        .setMaxResults(1).start().getVO();
                observacaoDestaqueAlvara = requerimentoAutorizacaoSanitaria.getDescricaoObservacaoDestaque();
            } else {
                RequerimentoAlvara requerimentoAlvara = LoadManager.getInstance(RequerimentoAlvara.class)
                        .addProperty(RequerimentoAlvara.PROP_OBSERVACAO_DESTAQUE_ALVARA)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_CODIGO), rv.getCodigo()))
                        .setMaxResults(1).start().getVO();
                observacaoDestaqueAlvara = requerimentoAlvara.getObservacaoDestaqueAlvara();
            }
        } else {
            containerObservacaoAlvara.setVisible(false);
            containerObservacaoCredenciamento.setVisible(false);
        }

        dropDrownSituacao.removeAllChoices();
        dropDrownSituacao.addChoice(null, "");
        dropDrownSituacao.addChoice(Situacao.DEFERIDO, rv != null && TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(rv.getTipoDocumento()) ? bundle("concluido") : bundle("deferido"));
        dropDrownSituacao.addChoice(Situacao.INDEFERIDO, rv != null && TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(rv.getTipoDocumento()) ? bundle("improcedente") : bundle("indeferido"));

        atividades =  VigilanciaHelper.geEstabelecimentoAtividadeReq(rv);
        tblAtividades.setVisible(VigilanciaHelper.usaAtividadeslicenciaveisaoDeferir() && VigilanciaHelper.isAlvaras(rv.getTipoDocumento()));
        selecionaAtividadePrincipal();

        containerDeferido.setVisible(false);

        target.add(containerDeferido);
        target.add(containerDataFinalizacao);
        target.add(containerSituacao);
        target.add(txtObservacao);
        target.add(tblAtividades);
        target.add(containerObservacaoAlvara);
        target.add(containerObservacaoCredenciamento);
    }
}