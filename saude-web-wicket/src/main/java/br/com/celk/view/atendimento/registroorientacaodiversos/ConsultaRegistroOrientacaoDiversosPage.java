package br.com.celk.view.atendimento.registroorientacaodiversos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.RegistroOrientacao;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaRegistroOrientacaoDiversosPage extends ConsultaPage<RegistroOrientacao, List<BuilderQueryCustom.QueryParameter>>{

    private Profissional profissionalOrientador;
    private String orientado;
    private DatePeriod periodo;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaProfissional("profissionalOrientador"));
        form.add(new InputField<String>("orientado"));
        form.add(new PnlDatePeriod("periodo"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        RegistroOrientacao proxy = on(RegistroOrientacao.class);
        
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("dataOrientacao"), proxy.getDataOrientacao()));
        columns.add(createSortableColumn(bundle("orientado"), proxy.getDescricaoOrientado()));
        columns.add(createSortableColumn(bundle("profissionalOrientador"), proxy.getProfissional().getNome()));
        return columns;
    }
    
    private CustomColumn<RegistroOrientacao> getCustomColumn() {
        return new CustomColumn<RegistroOrientacao>() {

            @Override
            public Component getComponent(String componentId, final RegistroOrientacao rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroOrientacaoDiversosPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroOrientacaoDiversosPage(rowObject, true));
                    }
                };
            }
        };
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return RegistroOrientacao.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(RegistroOrientacao.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(RegistroOrientacao.PROP_DATA_ORIENTACAO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroOrientacao.PROP_DESCRICAO_ORIENTADO, BuilderQueryCustom.QueryParameter.ILIKE, orientado));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroOrientacao.PROP_PROFISSIONAL, profissionalOrientador));
        parameters.add(new QueryCustom.QueryCustomParameter(RegistroOrientacao.PROP_DATA_ORIENTACAO, periodo));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroOrientacaoDiversosPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRegistroOrientacaoDiversas");
    }
}