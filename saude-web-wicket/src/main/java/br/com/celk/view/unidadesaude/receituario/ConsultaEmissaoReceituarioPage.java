package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.unidadesaude.receituario.customcolumn.EmissaoReceituarioColumnPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaEmissaoReceituarioPage extends BasePage {

    private QueryPagerProvider<AtendimentoDTO, AtendimentoDTOParam> dataProvider;
    private AtendimentoDTOParam param = new AtendimentoDTOParam();
    private PageableTable<AtendimentoDTO> pageableTable;
    private DlgCancelarEmissaoReceituario dlgCancelar;

    public ConsultaEmissaoReceituarioPage() {
        init();
    }

    public void init() {
        
        Form form = new Form("form", new CompoundPropertyModel(param));
        form.add(new InputField("nomePaciente"));
        pageableTable = new PageableTable<AtendimentoDTO>("table", getColumns(), getDataProvider());
        
        form.add(new ProcurarButton<AtendimentoDTOParam>("btnProcurar", pageableTable) {

            @Override
            public AtendimentoDTOParam getParam() {
                return customizeParam();
            }
        });
        form.add(pageableTable);
        form.add(dlgCancelar = new DlgCancelarEmissaoReceituario("dlgCancelar") {

            @Override
            public void onClose(AjaxRequestTarget target) {
                pageableTable.update(target);
            }
        });
        add(form);
    }
    
    private AtendimentoDTOParam customizeParam(){
        param.setSituacoes(Arrays.asList(Atendimento.STATUS_AGUARDANDO, Atendimento.STATUS_ATENDENDO, Atendimento.STATUS_OBSERVACAO));
        param.setTiposAtendimento(new OperadorValor<List<Long>>(Arrays.asList(TipoAtendimento.TiposAtendimento.RECEITUARIO.value()), OperadorValor.Operadores.IN));
        param.getConfigureParam().addSorter(VOUtils.montarPath(Atendimento.PROP_DATA_CHEGADA));
        return param;
    }

    public List<ISortableColumn<AtendimentoDTO>> getColumns() {
        List<ISortableColumn<AtendimentoDTO>> columns = new ArrayList<ISortableColumn<AtendimentoDTO>>();
        ColumnFactory columnFactory = new ColumnFactory(AtendimentoDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prontuario"), VOUtils.montarPath(AtendimentoDTO.PROP_NUMERO_PRONTUARIO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("idade"), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_IDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sexo"), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO),VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataSolicitacaoAbv"), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_DATA_CHEGADA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("usuarioAtendimento"), VOUtils.montarPath(AtendimentoDTO.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO, Usuario.PROP_NOME)));

        return columns;
    }

    private CustomColumn<AtendimentoDTO> getCustomColumn() {
        return new CustomColumn<AtendimentoDTO>() {

            @Override
            public Component getComponent(String componentId, final AtendimentoDTO rowObject) {
                return new EmissaoReceituarioColumnPanel(componentId, rowObject) {
                    
                    @Override
                    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                        if (validaAtendimentoPossuiReceituario(rowObject.getAtendimento())) {
//                            throw new ValidacaoException(BundleManager.getString("cancelamentoNaoPermetidoAtendimentoJaPossuiReceituario"));
//                        }
                        dlgCancelar.setModelObject(rowObject.getAtendimento());
                        dlgCancelar.update(target);
                        dlgCancelar.show(target);
                    }

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        confirmar(target,rowObject);
                    }
                };
            }
        };
    }

    private QueryPagerProvider<AtendimentoDTO, AtendimentoDTOParam> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new QueryPagerProvider<AtendimentoDTO, AtendimentoDTOParam>() {
                @Override
                public DataPagingResult executeQueryPager(DataPaging<AtendimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(AtendimentoFacade.class).pagerAtendimentos(dataPaging);
                }
            };
        }

        return this.dataProvider;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("emissaoReceituarioPaciente");
    }
    
    private void confirmar(AjaxRequestTarget target, AtendimentoDTO atendimentoDTO) throws ValidacaoException, DAOException {
        setResponsePage(new CadastroEmissaoReceituarioPage(atendimentoDTO.getAtendimento().getUsuarioCadsus()));
    }
    
//    private boolean validaAtendimentoPossuiReceituario(Atendimento _atendimento) throws DAOException, ValidacaoException{
//        List<Receituario> receituarios = LoadManager.getInstance(Receituario.class)
//                                         .addProperties(new HQLProperties(Receituario.class).getProperties())
//                                         .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_ATENDIMENTO, _atendimento))
//                                         .start().getList();
//        if (CollectionUtils.isNotNullEmpty(receituarios)) {
//            return true;
//        }
//        return false;
//    }
}
