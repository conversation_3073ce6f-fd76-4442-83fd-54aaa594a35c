package br.com.celk.view.agenda.agendamento.recebimentosolicitacoesagendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.agendamento.recebimentosolicitacoes.dto.ConfirmacaoEnvioSolicitacoesDTO;
import br.com.ksisolucoes.agendamento.recebimentosolicitacoes.dto.ConfirmacaoRecebimentoSolicitacoesDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 */
@Private
public class ConfirmacaoRecebimentoSolicitacoesUnidadePage extends BasePage{

    private Form<ConfirmacaoEnvioSolicitacoesDTO> form;
    private ConfirmacaoEnvioSolicitacoesDTO confirmacaoEnvioSolicitacoesDTO;
    private Table<LoteSolicitacaoAgendamentoItem> tblItens;

    private PageParameters parameters;
    
    public ConfirmacaoRecebimentoSolicitacoesUnidadePage(Long codigoSolicitacao, PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;
        carregarLoteSolicitacaoAgendamento(codigoSolicitacao);
        init();
    }
    
    private void init(){    
        ConfirmacaoRecebimentoSolicitacoesDTO proxy = on(ConfirmacaoRecebimentoSolicitacoesDTO.class);
        
        getForm().add(new DisabledInputField(path(proxy.getLoteSolicitacaoAgendamento().getEmpresa().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getLoteSolicitacaoAgendamento().getCodigo())));
        getForm().add(new DisabledInputField(path(proxy.getLoteSolicitacaoAgendamento().getDataCadastro())));
        getForm().add(new DisabledInputField(path(proxy.getLoteSolicitacaoAgendamento().getDataEnvio())));
        
        getForm().add(tblItens = new Table("tblItens", getColumns(), getCollectionProvider()));
        tblItens.populate();
        
        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmar();
            }
        });
        
        add(getForm());
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        LoteSolicitacaoAgendamentoItem on = on(LoteSolicitacaoAgendamentoItem.class);
    
        columns.add(createColumn(bundle("nrSolicitacao"), on.getSolicitacaoAgendamento().getCodigo()));
        columns.add(createColumn(bundle("tipoProcedimento"), on.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(createColumn(bundle("paciente"), on.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("dataSolicitacao"), on.getSolicitacaoAgendamento().getDataSolicitacao()));
        columns.add(createColumn(bundle("prioridade"), on.getSolicitacaoAgendamento().getDescricaoPrioridade()));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {
            
            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>(VOUtils.montarPath(LoteSolicitacaoAgendamentoItem.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO), true);
            }

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return consultarLista(getSort());
            }
        };
    }
    
    private Form<ConfirmacaoEnvioSolicitacoesDTO> getForm(){
        if(this.form == null){
            this.form = new Form<ConfirmacaoEnvioSolicitacoesDTO>("form", new CompoundPropertyModel<ConfirmacaoEnvioSolicitacoesDTO>(confirmacaoEnvioSolicitacoesDTO));
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("confirmacaoRecebimentoSolicitacoes");
    }
    
    private List<LoteSolicitacaoAgendamentoItem> consultarLista(SortParam sort){
        ConfirmacaoEnvioSolicitacoesDTO dto = getForm().getModel().getObject();
        LoteSolicitacaoAgendamentoItem proxy = on(LoteSolicitacaoAgendamentoItem.class);
        
        List<LoteSolicitacaoAgendamentoItem> loteSolicitacaoAgendamentoItemList = LoadManager.getInstance(LoteSolicitacaoAgendamentoItem.class)
                .addProperties(new HQLProperties(LoteSolicitacaoAgendamentoItem.class).getProperties())
                .addProperties(new HQLProperties(LoteSolicitacaoAgendamento.class, path(proxy.getLoteSolicitacaoAgendamento())).getProperties())
                .addProperties(new HQLProperties(Empresa.class, path(proxy.getLoteSolicitacaoAgendamento().getEmpresa())).getProperties())
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class, path(proxy.getSolicitacaoAgendamento())).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, path(proxy.getSolicitacaoAgendamento().getTipoProcedimento())).getProperties())
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getCodigo()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNome()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getApelido()))
                .addProperty(path(proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getUtilizaNomeSocial()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLoteSolicitacaoAgendamento()), dto.getLoteSolicitacaoAgendamento()))
                .addSorter(new QueryCustom.QueryCustomSorter((String) sort.getProperty(), sort.isAscending()?BuilderQueryCustom.QuerySorter.CRESCENTE:BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getSolicitacaoAgendamento().getDataSolicitacao()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
        
        getForm().getModel().getObject().setLoteSolicitacaoAgendamentoItemList(loteSolicitacaoAgendamentoItemList);
        
        return loteSolicitacaoAgendamentoItemList;
    }
    
    private void confirmar() throws DAOException, ValidacaoException{
        ConfirmacaoEnvioSolicitacoesDTO dto = getForm().getModel().getObject();
        if(CollectionUtils.isEmpty(dto.getLoteSolicitacaoAgendamentoItemList())){
            throw new ValidacaoException(BundleManager.getString("nenhumaSolicitacaoEncontrada"));
        }
        
        BOFactoryWicket.getBO(AgendamentoFacade.class).confirmarEnvioSolicitacoesUnidade(dto);
        ConsultaRecebimentoSolicitacoesAgendamentoPage page = new ConsultaRecebimentoSolicitacoesAgendamentoPage(parameters);
        page.info(bundle("recebimentoSolicitacaoConfirmado"));
        setResponsePage(page);
    }

    private void carregarLoteSolicitacaoAgendamento(Long codigoSolicitacao) {
        confirmacaoEnvioSolicitacoesDTO = new ConfirmacaoEnvioSolicitacoesDTO();
        
        LoteSolicitacaoAgendamento loteSolicitacaoAgendamento = LoadManager.getInstance(LoteSolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(LoteSolicitacaoAgendamento.class).getProperties())
                .setId(codigoSolicitacao).start().getVO();

        confirmacaoEnvioSolicitacoesDTO.setLoteSolicitacaoAgendamento(loteSolicitacaoAgendamento);
    }
    
}
