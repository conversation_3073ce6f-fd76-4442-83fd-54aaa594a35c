package br.com.celk.view.vigilancia.externo.view.servicos.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoBaixaResponsabilidadeTecnicaExternoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoInclusaoResponsabilidadeExternoPage;
import br.com.celk.view.vigilancia.requerimentos.RequerimentosPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.baixa.RequerimentoBaixaResponsabilidadeTecnicaPage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.inclusao.RequerimentoInclusaoResponsabilidadePage;
import br.com.celk.view.vigilancia.responsabilidadetecnica.nadaconsta.RequerimentoCertidaoNadaConstaPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlEscolherTipoResponsabilidadeTecnicaExterno extends Panel {

    private Form form;
    private AbstractAjaxButton btnFechar;
    private TipoSolicitacao tipoSolicitacaoSelecionado;

    public PnlEscolherTipoResponsabilidadeTecnicaExterno(String id, boolean possuiPermissaoBaixaResponsabilidade, boolean possuiPermissaoInclusaoResponsabilidade) {
        super(id);
        init(possuiPermissaoBaixaResponsabilidade, possuiPermissaoInclusaoResponsabilidade);
    }

    private void init(boolean possuiPermissaoBaixaResponsabilidade, boolean possuiPermissaoInclusaoResponsabilidade) {
        setOutputMarkupId(true);

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnBaixa") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value());
                setResponsePage(new RequerimentoBaixaResponsabilidadeTecnicaExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
            }
        }.setVisible(possuiPermissaoBaixaResponsabilidade));
        form.add(new AbstractAjaxButton("btnMudancaInclusao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value());
                setResponsePage(new RequerimentoInclusaoResponsabilidadeExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoInclusaoResponsabilidade));

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacaoSelecionado = tipoSolicitacao;
    }

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {
    }
}
