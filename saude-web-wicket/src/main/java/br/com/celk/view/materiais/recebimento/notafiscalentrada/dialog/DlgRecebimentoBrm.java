package br.com.celk.view.materiais.recebimento.notafiscalentrada.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.financeiro.Serie;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.Date;

public abstract class DlgRecebimentoBrm extends Window {

    private PnlRecebimentoBrm pnlRecebimentoBrm;

    public DlgRecebimentoBrm(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("recebimento_brm"));
        setOutputMarkupId(true);

        setInitialWidth(800);
        setInitialHeight(400);
        setResizable(false);

        setContent(pnlRecebimentoBrm = new PnlRecebimentoBrm(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long idBrm, Serie serie, TipoDocumento tipoDocumento, Date dataEntrada) throws ValidacaoException, DAOException {
                close(target);
                DlgRecebimentoBrm.this.onConfirmar(target, idBrm, serie, tipoDocumento, dataEntrada);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long idBrm, Serie serie, TipoDocumento tipoDocumento, Date dataEntrada) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        pnlRecebimentoBrm.limpar(target);
        super.show(target);
    }
}
