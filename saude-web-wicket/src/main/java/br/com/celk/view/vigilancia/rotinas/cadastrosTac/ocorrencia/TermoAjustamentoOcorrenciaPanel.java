package br.com.celk.view.vigilancia.rotinas.cadastrosTac.ocorrencia;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.TextColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.view.agenda.agendamento.dialog.DlgOcorrenciaTermoAjustamentoConduta;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoOcorrencia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class TermoAjustamentoOcorrenciaPanel extends Panel {

    private WebMarkupContainer containerTableOcorrencias;
    private List<TermoAjustamentoOcorrencia> ocorrenciaTermoAjustamentoCondutaList;
    private Table tableOcorrencias;
    private DlgOcorrenciaTermoAjustamentoConduta dlgOcorrenciaTermoAjustamentoConduta;

    public TermoAjustamentoOcorrenciaPanel(String id, Long codigoTermoAjustamento) {
        super(id);
        carregarOcorrenciaTermoAjustamentoConduta(codigoTermoAjustamento);
        init();
    }

    private void init() {
        containerTableOcorrencias = new WebMarkupContainer("containerTableOcorrencias");
        containerTableOcorrencias.setOutputMarkupId(true);

        containerTableOcorrencias.add(tableOcorrencias = new Table("tableOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias()));
        tableOcorrencias.populate();
        tableOcorrencias.setScrollY("135");
        tableOcorrencias.setScrollXInner("100%");

        add(containerTableOcorrencias);
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList<>();
        TermoAjustamentoOcorrencia proxy = on(TermoAjustamentoOcorrencia.class);

        columns.add(getActionColumn());
        columns.add(new DateTimeColumn(bundle("dataHora"), path(proxy.getDataOcorrencia())));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(new TextColumn(bundle("descricao"),  path(proxy.getDescricao())).setMaxPrecision(500));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<TermoAjustamentoOcorrencia>() {
            @Override
            public void customizeColumn(final TermoAjustamentoOcorrencia rowObject) {

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TermoAjustamentoOcorrencia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TermoAjustamentoOcorrencia modelObject) throws ValidacaoException, DAOException {
                        initDlgOcorrenciaConsultaRequerimentoVigilancia(target,modelObject.getDescricao());
                    }
                }).setEnabled(true);
            }
        };
    }

    private void initDlgOcorrenciaConsultaRequerimentoVigilancia(AjaxRequestTarget target, String descricao) {
        if (dlgOcorrenciaTermoAjustamentoConduta == null) {
            WindowUtil.addModal(target, this, dlgOcorrenciaTermoAjustamentoConduta = new DlgOcorrenciaTermoAjustamentoConduta(WindowUtil.newModalId(this)) {
            });
        }
        dlgOcorrenciaTermoAjustamentoConduta.show(target, descricao);
    }

    private ICollectionProvider getCollectionProviderOcorrencias() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return ocorrenciaTermoAjustamentoCondutaList;
            }
        };
    }

    private void carregarOcorrenciaTermoAjustamentoConduta(Long codigoTermoAjustamento){
        if (codigoTermoAjustamento == null) {
            return;
        }

        TermoAjustamentoOcorrencia proxy = on(TermoAjustamentoOcorrencia.class);
        ocorrenciaTermoAjustamentoCondutaList = LoadManager.getInstance(TermoAjustamentoOcorrencia.class)
                .addProperty(path(proxy.getDataOcorrencia()))
                .addProperty(path(proxy.getUsuario().getNome()))
                .addProperty(path(proxy.getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTermoAjustamentoConduta().getCodigo()), codigoTermoAjustamento))
                .start().getList();
    }

}
