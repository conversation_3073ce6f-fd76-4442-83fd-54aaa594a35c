package br.com.celk.view.atendimento.recepcao.panel.agendamentocirurgico;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.RequiredHoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.longfield.RequiredLongField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.recepcao.panel.template.DefaultRecepcaoPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamentocirurgico.dto.CadastroAgendamentoCirurgicoDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioAgendamentosCirurgicosDTOParam;
import br.com.ksisolucoes.report.hospital.interfaces.facade.HospitalReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaCirurgia;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.List;

import static br.com.celk.component.window.WindowUtil.addModal;
import static br.com.celk.component.window.WindowUtil.newModalId;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class MarcacaoAgendamentoCirurgicoPanel extends RecepcaoCadastroPanel {

    private Form<CadastroAgendamentoCirurgicoDTO> form;
    private final DefaultRecepcaoPanel defaultRecepcaoPanel;
    private final UsuarioCadsus paciente;

    private WebMarkupContainer containerUsuarioCadsus;
    private DropDown dropDownConvenio;
    private DropDown dropDownSubConvenio;
    private LongField txtDuracao;
    private DateChooser dchData;
    private DlgConfirmacaoOk dlgConfirmacao;
    private DlgImpressaoObject<Long> dlgImpressao;

    public MarcacaoAgendamentoCirurgicoPanel(String id, DefaultRecepcaoPanel defaultRecepcaoPanel, UsuarioCadsus paciente) {
        super(id, bundle("marcacaoCirurgias"));
        this.paciente = paciente;
        this.defaultRecepcaoPanel = defaultRecepcaoPanel;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        CadastroAgendamentoCirurgicoDTO proxy = on(CadastroAgendamentoCirurgicoDTO.class);

        getForm().add(containerUsuarioCadsus = new WebMarkupContainer("containerUsuarioCadsus"));
        containerUsuarioCadsus.setOutputMarkupId(true);

        containerUsuarioCadsus.add(new DisabledInputField(path(proxy.getPaciente().getNomeSocial())));
        containerUsuarioCadsus.add(new DisabledInputField(path(proxy.getPaciente().getIdade())));

        getForm().add(new AutoCompleteConsultaTipoProcedimento(path(proxy.getTipoProcedimento()), true));
        getForm().add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true));
        getForm().add(dropDownConvenio = getDropDownConvenio(path(proxy.getConvenio())));

        getForm().add(dropDownSubConvenio = getDropDownSubConvenio(path(proxy.getSubconvenio())));
        dropDownSubConvenio.setEnabled(false);

        getForm().add(dchData = new RequiredDateChooser(path(proxy.getData())));
        dchData.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));

        getForm().add(new RequiredHoraMinutoField(path(proxy.getHoraInicial())));
        getForm().add(txtDuracao = new RequiredLongField(path(proxy.getDuracao())));
        txtDuracao.setVMax(99999L);

        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getAnestesista())));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getCirculante())));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getLado()), AgendaCirurgia.Lado.values(), true, false));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getRaioX())));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getOpm())));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getBancoSangue())));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getServicosEspeciais())));

        getForm().add(getDropDownSalaUnidade(path(proxy.getSala())));

        getForm().add(new InputArea(path(proxy.getObservacaoCirurgia())));

        AjaxButton btnVoltar;
        getForm().add(btnVoltar = getBtnVoltar());
        btnVoltar.setDefaultFormProcessing(false);
        getForm().add(new AbstractAjaxButton("btnAgendar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                cadastrarAgendamentoCirurgico(target);
            }
        });

        add(getForm());

        dropDownConvenio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownConvenio.getComponentValue() != null) {
                    List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
                            .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, dropDownConvenio.getComponentValue()))
                            .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                            .start().getList();
                    if (convList.isEmpty()) {
                        dropDownSubConvenio.setEnabled(false);
                        dropDownSubConvenio.removeAllChoices();
                    } else {
                        dropDownSubConvenio.setEnabled(true);
                        dropDownSubConvenio.removeAllChoices();
                        for (Convenio convenio : convList) {
                            dropDownSubConvenio.addChoice(convenio, convenio.getDescricao());
                        }
                    }
                } else {
                    dropDownSubConvenio.setEnabled(false);
                }
                target.add(dropDownSubConvenio);
            }
        });
    }

    private DropDown getDropDownSalaUnidade(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");

        List<SalaUnidade> salaList = LoadManager.getInstance(SalaUnidade.class)
                .addProperties(new HQLProperties(SalaUnidade.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, SalaUnidade.PROP_EMPRESA).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(SalaUnidade.PROP_DESCRICAO))
                .start().getList();

        for (SalaUnidade sala : salaList) {
            dropDown.addChoice(sala, sala.getEmpresa().getDescricao() + " - " + sala.getDescricao());
        }

        return dropDown;
    }

    private DropDown getDropDownConvenio(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");

        List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                .addProperties(new HQLProperties(Convenio.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.NAO_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                .start().getList();
        for (Convenio c : convList) {
            dropDown.addChoice(c, c.getDescricao());
        }

        return dropDown;
    }

    private DropDown getDropDownSubConvenio(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null, "");

        return dropDown;
    }

    private AjaxButton getBtnVoltar() {
        return new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getRecepcaoController().changePanel(target, defaultRecepcaoPanel);
            }
        };
    }

    private Form getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel<CadastroAgendamentoCirurgicoDTO>(new CadastroAgendamentoCirurgicoDTO()));
            form.getModel().getObject().setPaciente(paciente);
        }
        return form;
    }

    private void cadastrarAgendamentoCirurgico(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        CadastroAgendamentoCirurgicoDTO dto = (CadastroAgendamentoCirurgicoDTO) getForm().getModel().getObject();
        if (dto.getConvenio() == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneConvenio"));
        }
        if (dto.getSala() == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneSala"));
        }

        AgendaGradeAtendimentoHorario agah = BOFactoryWicket.getBO(AgendamentoFacade.class).cadastrarAgendamentoCirurgico(dto);

//        viewDlgConfirmacao(target);
        viewDlgImpressao(target, agah.getCodigo());
    }

    private void viewDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            addModal(target, this, dlgConfirmacao = new DlgConfirmacaoOk(newModalId(this), bundle("msgCirurgiaAgendadaComSucesso")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    getRecepcaoController().changePanel(target, defaultRecepcaoPanel);
                }
            });
        }

        dlgConfirmacao.show(target);
    }

    private void viewDlgImpressao(AjaxRequestTarget target, Long codigoAgendaGradeAtendimentoHorario) {
        if (dlgImpressao == null) {
            addModal(target, this, dlgImpressao = new DlgImpressaoObject<Long>(newModalId(this), bundle("desejaImprimirAgendamentoCirurgico")) {
                @Override
                public DataReport getDataReport(Long codigoAgah) throws ReportException {
                    RelatorioAgendamentosCirurgicosDTOParam param = new RelatorioAgendamentosCirurgicosDTOParam();
                    param.setCodigoAgendaGradeAtendimentoHorario(codigoAgah);
                    return BOFactoryWicket.getBO(HospitalReportFacade.class).comprovanteAgendamentosCirurgicos(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long object) throws ValidacaoException, DAOException {
                    getRecepcaoController().changePanel(target, defaultRecepcaoPanel);
                }

            });
        }

        dlgImpressao.show(target, codigoAgendaGradeAtendimentoHorario);
    }
}
