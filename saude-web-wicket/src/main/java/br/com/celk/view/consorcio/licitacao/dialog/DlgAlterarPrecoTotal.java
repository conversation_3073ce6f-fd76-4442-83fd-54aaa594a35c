package br.com.celk.view.consorcio.licitacao.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
public abstract class DlgAlterarPrecoTotal extends Window{

    private PnlAlterarPrecoTotal pnlAlterarValorLicitado;
    
    public DlgAlterarPrecoTotal(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("alterarValorTotalLicitado"));
        
        setInitialHeight(230);
        setInitialWidth(500);
        setResizable(false);

        setContent(pnlAlterarValorLicitado = new PnlAlterarPrecoTotal(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, LicitacaoItem licitacaoItem) throws ValidacaoException, DAOException {
                DlgAlterarPrecoTotal.this.onConfirmar(target, licitacaoItem);
                close(target);
            }

        });
    }
    
    public void show(AjaxRequestTarget target, LicitacaoItem licitacaoItem){
        pnlAlterarValorLicitado.limpar(target);
        pnlAlterarValorLicitado.setLicitacaoItem(licitacaoItem);
        show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, LicitacaoItem licitacaoItem) throws ValidacaoException, DAOException;

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlAlterarValorLicitado.getTxtValorTotal();
    }
    
}
