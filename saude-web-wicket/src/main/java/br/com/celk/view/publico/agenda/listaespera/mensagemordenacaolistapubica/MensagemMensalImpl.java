package br.com.celk.view.publico.agenda.listaespera.mensagemordenacaolistapubica;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.basico.AgendadorProcesso;

public class MensagemMensalImpl implements MensagemOrdenacaoListaEspera {

    @Override
    public String build(AgendadorProcesso agendadorProcesso) {

        return BundleManager.getString("msgIntevervaloAtualizacaoListaEsperaMensal", agendadorProcesso.getDiaMes(),
                                                                                           agendadorProcesso.getHoraMes(),
                                                                                           agendadorProcesso.getMinutoMes());
    }
}
