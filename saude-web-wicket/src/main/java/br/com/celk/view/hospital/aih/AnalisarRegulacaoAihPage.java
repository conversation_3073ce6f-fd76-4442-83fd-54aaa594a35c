package br.com.celk.view.hospital.aih;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.leito.DlgMudancaProcedimentoAih;
import br.com.celk.view.agenda.agendamento.leito.DlgRegulacaoAih;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.solicitacaomudancaprocedimentoDTO.SolicitacaoMudancaProcedimentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaAih;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoLeito;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoMudancaProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.ExternalLink;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class AnalisarRegulacaoAihPage extends BasePage {
    private CompoundPropertyModel<SolicitacaoMudancaProcedimentoDTO> model;
    private AutorizacaoInternacaoHospitalarDTO dtoAih;
    private SolicitacaoMudancaProcedimentoDTO dto;
    private Form<SolicitacaoMudancaProcedimentoDTO> form;
    private WebMarkupContainer containerRegulacaoAih;
    private InputField procedimentoAnteriorFormatado;
    private InputField novoProcedimento;
    private InputField diagnosticoInicial;
    private InputField cidPrincipal;
    private InputField cidSecundario;
    private InputField cidCausasAssociadas;
    private InputField profissionalSolicitante;
    private DateChooser dataSolicitacao;
    private InputField estabelecimentoSolicitante;
    private InputArea justificativa;

    private ExternalLink voltar;
    private DlgMudancaProcedimentoAih dlgNegar;
    private DlgMudancaProcedimentoAih dlgAutorizar;
    private DlgLancarOcorrenciaAih dlgLancarOcorrenciaAih;


    public AnalisarRegulacaoAihPage(AutorizacaoInternacaoHospitalarDTO dtoAih) {
        this.dto = new SolicitacaoMudancaProcedimentoDTO();
        this.dtoAih = dtoAih;
        init();
    }

    public void init() {
        form = new Form<SolicitacaoMudancaProcedimentoDTO>("form", model = new CompoundPropertyModel<SolicitacaoMudancaProcedimentoDTO>(new SolicitacaoMudancaProcedimentoDTO()));

        SolicitacaoMudancaProcedimentoDTO proxy = on(SolicitacaoMudancaProcedimentoDTO.class);

        form.add(containerRegulacaoAih = new WebMarkupContainer("containerRegulacaoAih"));
        containerRegulacaoAih.setEnabled(false);

        containerRegulacaoAih.add(procedimentoAnteriorFormatado = new DisabledInputField(path(proxy.getProcedimentoAnteriorFormatado())));
        containerRegulacaoAih.add(novoProcedimento = new DisabledInputField(path(proxy.getNovoProcedimento())));
        containerRegulacaoAih.add(diagnosticoInicial = new DisabledInputField(path(proxy.getDiagnosticoInicial())));
        containerRegulacaoAih.add(cidPrincipal = new DisabledInputField(path(proxy.getCidPrincipal())));
        containerRegulacaoAih.add(cidSecundario = new DisabledInputField(path(proxy.getCidSecundario())));
        containerRegulacaoAih.add(cidCausasAssociadas = new DisabledInputField(path(proxy.getCidCausasAssociadas())));
        containerRegulacaoAih.add(profissionalSolicitante = new DisabledInputField(path(proxy.getProfissionalSolicitante())));
        containerRegulacaoAih.add(dataSolicitacao = new DateChooser(path(proxy.getDataSolicitacao())));
        containerRegulacaoAih.add(estabelecimentoSolicitante = new DisabledInputField(path(proxy.getSolicitacaoMudancaProcedimento().getEstabelecimentoSolicitante())));
        dataSolicitacao.setEnabled(false);

        containerRegulacaoAih.add(justificativa = new DisabledInputArea(path(proxy.getJustificativa())));

        form.add(voltar = new VoltarButton("btnVoltar"));
        form.add(new AbstractAjaxButton("btnAutorizar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                if (dlgAutorizar == null) {
                    addModal(target, dlgAutorizar = new DlgMudancaProcedimentoAih(newModalId(), BundleManager.getString("confirmacaoAutorizarMudancaProcedimento"), false) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Aih aih, String motivo) throws ValidacaoException, DAOException {
                            atualizaSolicitacaoMudancaProcedimentoAihGeraOcorrencia(aih, SolicitacaoMudancaProcedimento.Status.AUTORIZADA, motivo);
                            setResponsePage(new ConsultaRegulacaoAihPage());
                        }
                    });
                }
                dlgAutorizar.show(target, dto);
            }
        });
        form.add(new AbstractAjaxButton("btnNegar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                if (dlgNegar == null) {
                    addModal(target, dlgNegar = new DlgMudancaProcedimentoAih(newModalId(), BundleManager.getString("negarAutorizarMudancaProcedimento"), true) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Aih aih, String motivo) throws ValidacaoException, DAOException {
                            atualizaSolicitacaoMudancaProcedimentoAihGeraOcorrencia(aih, SolicitacaoMudancaProcedimento.Status.NEGADA, motivo);
                            setResponsePage(new ConsultaRegulacaoAihPage());
                        }
                    });
                }
                dlgNegar.show(target, dto);
            }
        });
        form.add(new AbstractAjaxButton("btnVisualizarAih") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                setResponsePage(new ManutencaoAihAutorizadaPage(dtoAih, true));
            }
        });
        form.add(new AbstractAjaxButton("btnLancarOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                viewDialogLancarOcorrencia(target);
            }
        });

        carregarSolicitacaoMudancaProcedimento();

        add(form);

    }

    public void viewDialogLancarOcorrencia(AjaxRequestTarget target) {
        if (dlgLancarOcorrenciaAih == null) {
            addModal(target, dlgLancarOcorrenciaAih = new DlgLancarOcorrenciaAih(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String ocorrencia) throws ValidacaoException, DAOException {
                    if (ocorrencia == null || ocorrencia.isEmpty()) {
                        throw new ValidacaoException(BundleManager.getString("informeDescricaoOcorrencia"));
                    }
                    lancarOcorrencia(ocorrencia);
                }
            });
        }
        dlgLancarOcorrenciaAih.show(target);
    }

    private void lancarOcorrencia(String ocorrencia) throws DAOException, ValidacaoException {
        Aih aih = (Aih) HibernateSessionFactory.getSession().get(Aih.class, dtoAih.getAutorizacaoInternacaoHospitalar().getCodigo());
        OcorrenciaAih ocorrenciaAih = new OcorrenciaAih();
        ocorrenciaAih.setDataCadastro(DataUtil.getDataAtual());
        ocorrenciaAih.setUsuarioCadastro(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario());
        ocorrenciaAih.setDescricao(ocorrencia);
        ocorrenciaAih.setAih(aih);
        BOFactoryWicket.save(ocorrenciaAih);
    }

    private void atualizaSolicitacaoMudancaProcedimentoAihGeraOcorrencia(Aih aih, SolicitacaoMudancaProcedimento.Status status, String motivo) throws DAOException, ValidacaoException {
        aih.setStatus(Aih.Status.CONFIRMADA.value());
        if (status.equals(SolicitacaoMudancaProcedimento.Status.AUTORIZADA)) {
            aih.setProcedimentoSolicitado(dto.getSolicitacaoMudancaProcedimento().getNovoProcedimento());
            aih.setCidPrincipal(dto.getSolicitacaoMudancaProcedimento().getCidPrincipal());
            aih.setCidSecundario(dto.getSolicitacaoMudancaProcedimento().getCidSecundario());
            aih.setCidCausaAssociada(dto.getSolicitacaoMudancaProcedimento().getCidCausasAssociadas());
        }

        BOFactoryWicket.save(aih);

        dto.getSolicitacaoMudancaProcedimento().setStatus(status.value());
        dto.getSolicitacaoMudancaProcedimento().setDataAnalise(DataUtil.getDataAtual());

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarSolicitacaoMudancaProcedimento(dto, true);

        String descricaoOcorrencia = SolicitacaoMudancaProcedimento.Status.valueOf(status.value())
                + " a mudança de procedimento "
                + dto.getProcedimentoAnteriorFormatado()
                + " para "
                + dto.getNovoProcedimento()
                +" - Observação: "
                + motivo;
        salvarOcorrencia(descricaoOcorrencia, aih);
    }

    private OcorrenciaAih salvarOcorrencia(String descricao, Aih aih) throws DAOException, ValidacaoException {
        OcorrenciaAih ocorrencia = new OcorrenciaAih();
        ocorrencia.setDataCadastro(DataUtil.getDataAtual());
        ocorrencia.setUsuarioCadastro(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario());
        ocorrencia.setDescricao(descricao);
        ocorrencia.setAih(aih);
        BOFactoryWicket.save(ocorrencia);

        return ocorrencia;
    }

    private SolicitacaoMudancaProcedimento carregarSolicitacaoMudancaProcedimento() {
        SolicitacaoMudancaProcedimento solicitacaoMudancaProcedimento = LoadManager.getInstance(SolicitacaoMudancaProcedimento.class)
                .addProperties(new HQLProperties(SolicitacaoMudancaProcedimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(
                        SolicitacaoMudancaProcedimento.PROP_AUTORIZACAO_INTERNACAO_HOSPITALAR, dtoAih.getAutorizacaoInternacaoHospitalar()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(SolicitacaoMudancaProcedimento.PROP_CODIGO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1).start().getVO();

        dto.setProcedimentoAnteriorFormatado(solicitacaoMudancaProcedimento.getProcedimentoAnterior());
        dto.setNovoProcedimento(solicitacaoMudancaProcedimento.getNovoProcedimento());
        dto.setDiagnosticoInicial(solicitacaoMudancaProcedimento.getDiagnosticoInicial());
        dto.setCidPrincipal(solicitacaoMudancaProcedimento.getCidPrincipal());
        dto.setCidSecundario(solicitacaoMudancaProcedimento.getCidSecundario());
        dto.setCidCausasAssociadas(solicitacaoMudancaProcedimento.getCidCausasAssociadas());
        dto.setProfissionalSolicitante(solicitacaoMudancaProcedimento.getProfissionalSolicitante());
        dto.setDataSolicitacao(solicitacaoMudancaProcedimento.getDataSolicitacao());
        dto.setJustificativa(solicitacaoMudancaProcedimento.getJustificativa());
        dto.setSolicitacaoMudancaProcedimento(solicitacaoMudancaProcedimento);
        dto.getSolicitacaoMudancaProcedimento().setAutorizacaoInternacaoHospitalar(dtoAih.getAutorizacaoInternacaoHospitalar());

        model.setObject(dto);
        return solicitacaoMudancaProcedimento;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("regulacaoAih");
    }
}
