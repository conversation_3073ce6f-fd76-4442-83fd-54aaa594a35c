package br.com.celk.view.agenda.indisponibilidadeprofissionalunidade;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.css.TextAlign;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.StringColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.IndisponibilidadeProfissionalUnidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> S. Schmoeller
 */
public class ConsultaIndisponibilidadeProfissionalUnidadePage extends ConsultaPage<IndisponibilidadeProfissionalUnidade, List<BuilderQueryCustom.QueryParameter>> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private RequiredDateChooser dataInicialIndisponibilidade;
    private RequiredDateChooser dataFinalIndisponibilidade;
    private Empresa empresa;
    private Profissional profissionalSolicitante;
    private Date dataInicial;
    private Date dataFinal;

    public ConsultaIndisponibilidadeProfissionalUnidadePage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        IndisponibilidadeProfissionalUnidade proxy = on(IndisponibilidadeProfissionalUnidade.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissionalSolicitante())));
        form.add(new DateChooser(path(proxy.getDataInicial())));
        form.add(new DateChooser(path(proxy.getDataFinal())));

        add(form);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        IndisponibilidadeProfissionalUnidade proxy = on(IndisponibilidadeProfissionalUnidade.class);

        columns.add(getActionColumn());

        columns.add(new StringColumn(BundleManager.getString("dataIndisponibilidade"), path(proxy.getDataIndisponibilidadeFormatada())).setTextAlign(TextAlign.CENTER));
        columns.add(createSortableColumn(BundleManager.getString("unidade_solicitante"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("profissional"), proxy.getProfissionalSolicitante().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<IndisponibilidadeProfissionalUnidade>() {
            @Override
            public void customizeColumn(final IndisponibilidadeProfissionalUnidade rowObject) {

                addAction(ActionType.EDITAR, rowObject, new IModelAction<IndisponibilidadeProfissionalUnidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, IndisponibilidadeProfissionalUnidade modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroIndisponibilidadeProfissionalUnidadePage(rowObject, false, true));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<IndisponibilidadeProfissionalUnidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, IndisponibilidadeProfissionalUnidade modelObject) throws ValidacaoException, DAOException {

                        //TODO IndisponibilidadeProfissionalUnidade
                        // Solicitar o Motivo do Cancelamento.

                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);

                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<IndisponibilidadeProfissionalUnidade>() {
                    @Override
                    public void action(AjaxRequestTarget target, IndisponibilidadeProfissionalUnidade modelObject) {
                        setResponsePage(new CadastroIndisponibilidadeProfissionalUnidadePage(rowObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return IndisponibilidadeProfissionalUnidade.class;
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(IndisponibilidadeProfissionalUnidade.PROP_DATA_INICIAL, true);
            }
        };




    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IndisponibilidadeProfissionalUnidade.PROP_EMPRESA), empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IndisponibilidadeProfissionalUnidade.PROP_PROFISSIONAL_SOLICITANTE), profissionalSolicitante));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IndisponibilidadeProfissionalUnidade.PROP_DATA_INICIAL), dataInicial));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IndisponibilidadeProfissionalUnidade.PROP_DATA_FINAL), dataFinal));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroIndisponibilidadeProfissionalUnidadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaIndisponibilidadeProfissionalUnidade");
    }
}
