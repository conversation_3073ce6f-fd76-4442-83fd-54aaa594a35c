package br.com.celk.view.hospital.quartosleitos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.hospital.quartosleitos.autocomplete.AutoCompleteConsultaEspecialidadeLeito;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaQuartosDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaQuartosDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.EspecialidadeLeito;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaQuartos extends ConsultaPage<QueryConsultaQuartosDTO, QueryConsultaQuartosDTOParam> {

    private Empresa estabelecimento;
    private EspecialidadeLeito especialidadeLeito;
    private String tipoLeitoQuarto;
    private String paciente;
    private Long status;
    private Long tipoLeito;
    private Long sexo;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaEspecialidadeLeito autoCompleteConsultaEspecialidadeLeito;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("estabelecimento"));
        if (!isActionPermitted(Permissions.EMPRESA)) {
            autoCompleteConsultaEmpresa.setEnabled(isActionPermitted(Permissions.EMPRESA));
            estabelecimento = SessaoAplicacaoImp.getInstance().getEmpresa();
        }
        form.add(new InputField("tipoLeitoQuarto", new PropertyModel(this, "tipoLeitoQuarto")));
        form.add(new InputField("paciente", new PropertyModel(this, "paciente")));
        form.add(autoCompleteConsultaEspecialidadeLeito = new AutoCompleteConsultaEspecialidadeLeito("especialidadeLeito"));
        autoCompleteConsultaEspecialidadeLeito.setLabel(new Model<String>(bundle("especialidadeLeito")));
        form.add(getDropDownStatus());
        form.add(getDropDownTipoLeito());
        form.add(getDropDownSexo());
        getLinkNovo().setVisible(false);
    }
    
    public DropDown getDropDownStatus(){
        DropDown  dropDownStatus = new DropDown("status", new PropertyModel(this, "status"));
        
        dropDownStatus.addChoice(null , BundleManager.getString("todos"));
        dropDownStatus.addChoice(LeitoQuarto.Situacao.LIBERADO.value() , BundleManager.getString("liberado"));
        dropDownStatus.addChoice(LeitoQuarto.Situacao.AGUARDANDO_LIMPEZA.value() , BundleManager.getString("limpeza"));
        dropDownStatus.addChoice(LeitoQuarto.Situacao.OCUPADO.value() , BundleManager.getString("ocupado"));
        
        return dropDownStatus;
    }

    public DropDown getDropDownTipoLeito() {
        DropDown dropDownTipoLeito = new DropDown("tipoLeito", new PropertyModel(this, "tipoLeito"));

        dropDownTipoLeito.addChoice(null , BundleManager.getString("todos"));
        dropDownTipoLeito.addChoice(LeitoQuarto.TipoLeito.SUS.value() , BundleManager.getString("sus"));
        dropDownTipoLeito.addChoice(LeitoQuarto.TipoLeito.CONVENIO.value() , BundleManager.getString("convenio"));
        dropDownTipoLeito.addChoice(LeitoQuarto.TipoLeito.PARTICULAR.value() , BundleManager.getString("particular"));

        return dropDownTipoLeito;
    }

    public DropDown getDropDownSexo() {
        DropDown dropDownSexo = new DropDown("sexo", new PropertyModel(this, "sexo"));

        dropDownSexo.addChoice(LeitoQuarto.Sexo.AMBOS.value() , BundleManager.getString("rotulo_ambos"));
        dropDownSexo.addChoice(LeitoQuarto.Sexo.MASCULINO.value() , BundleManager.getString("m"));
        dropDownSexo.addChoice(LeitoQuarto.Sexo.FEMININO.value() , BundleManager.getString("f"));

        return dropDownSexo;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(QueryConsultaQuartosDTO.class);
        
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estabelecimento"), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_SETOR)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoLeitoQuarto"), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_QUARTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("leito"), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_LEITO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoLeito"), LeitoQuarto.PROP_TIPO_LEITO, QueryConsultaQuartosDTO.PROP_DESCRICAO_TIPO_LEITO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sexo"), LeitoQuarto.PROP_SEXO, QueryConsultaQuartosDTO.PROP_DESCRICAO_SEXO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), LeitoQuarto.PROP_SITUACAO, QueryConsultaQuartosDTO.PROP_DESCRICAO_SITUACAO));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("especialidadeLeito"), VOUtils.montarPath(QueryConsultaQuartosDTO.PROP_ESPECIALIDADE_LEITO)));
        
        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<QueryConsultaQuartosDTO, QueryConsultaQuartosDTOParam>() {
            
            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryConsultaQuartosDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setCampoOrdenacao((String)((SingleSortState)getPagerProvider().getSortState()).getSort().getProperty());
                dataPaging.getParam().setTipoOrdenacao(((SingleSortState)getPagerProvider().getSortState()).getSort().isAscending()?"asc":"desc");
                return BOFactoryWicket.getBO(HospitalFacade.class).consultarQuartos(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(QueryConsultaQuartosDTO.PROP_QUARTO, false);
            }
        };
    }
    
    @Override
    public QueryConsultaQuartosDTOParam getParameters() {
        QueryConsultaQuartosDTOParam param = new QueryConsultaQuartosDTOParam();
        param.setNomePaciente(this.paciente);
        param.setQuarto(this.tipoLeitoQuarto);
        param.setSituacao(this.status);
        if (this.estabelecimento != null) {
            param.setEmpresa(Arrays.asList(this.estabelecimento));
        } else {
            param.setEmpresa(null);
        }
        param.setEspecialidadeLeito(this.especialidadeLeito);
        param.setTipoLeito(this.tipoLeito);
        param.setSexo(this.sexo);

        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaQuartos.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaQuartos");
    }
        
}
