package br.com.celk.view.vigilancia.faturamento.lancamentoAtividades;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.autocomplete.AutoCompleteLancamentoAtividadeVigilanciaTipoAtividade;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.faturamento.autocomplete.AutoCompleteConsultaAtividadesVigilancia;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.javascript.JScript.initMasks;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroLancamentoAtividadesVigilanciaPage extends BasePage {
    private String detalhesRequerimentoVigilancia;

    private Form<LancamentoAtividadesVigilanciaDTO> form;
    private CompoundPropertyModel<LancamentoAtividadesVigilanciaItem> modelItem;
    private WebMarkupContainer containerItem;
    private AbstractAjaxButton btnAdicionarItem;
    private Table tblLancamentosItem;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DateChooser dataAtividadeChooser;
    private AutoCompleteConsultaAtividadesVigilancia autoCompleteConsultaAtividadesVigilancia;

    private LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia;
    private WebMarkupContainer containerMaisProfissionais;
    private Table tblFiscais;
    private List<Profissional> fiscaisList = new ArrayList<>();

    private String cpf;
    private String cnpj;
    private Long tipoPessoa;
    private DropDown dropDownTipoPessoa;
    private InputField txtCpf;
    private InputField txtCnpj;
    private InputField txtDescricaoOutros;
    private DropDown<Long> dropDownFisicaJuridica;
    private WebMarkupContainer containerDadosEstabelecimentoPessoa;
    private WebMarkupContainer containerPessoa;
    private WebMarkupContainer containerEstabelecimento;
    private WebMarkupContainer containerAtividadeEstabelecimento;
    private WebMarkupContainer containerOutros;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private PnlAtividadeEstabelecimento pnlAtividadeEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private WebMarkupContainer containerCnpj;
    private WebMarkupContainer containerCpf;
    private DoubleField txtPontuacao;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public CadastroLancamentoAtividadesVigilanciaPage() {
        this(null);
    }

    public CadastroLancamentoAtividadesVigilanciaPage(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia) {
        this(lancamentoAtividadesVigilancia, false);
    }

    public CadastroLancamentoAtividadesVigilanciaPage(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, boolean viewOnly) {
        this.lancamentoAtividadesVigilancia = lancamentoAtividadesVigilancia;
        if (this.lancamentoAtividadesVigilancia == null) {
            this.lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        }
        init(!viewOnly);
    }


    private void init(boolean enabled) {
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        LancamentoAtividadesVigilanciaDTO proxy = on(LancamentoAtividadesVigilanciaDTO.class);

        getForm().add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getLancamentoAtividadesVigilancia().getProfissional())).setEnabled(lancamentoAtividadesVigilancia.getCodigo() == null));

        getForm().add(containerMaisProfissionais = new WebMarkupContainer("containerMaisProfissionais"));
        containerMaisProfissionais.add(new AbstractAjaxButton("btnAdicionarFiscal") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarFiscais(target, autoCompleteConsultaProfissional.getModel().getObject());
            }
        }.setDefaultFormProcessing(false).add(new AttributeModifier("title", bundle("msgAdicionarMaisFiscaisParaClonarLancamentoAtividade"))));

        containerMaisProfissionais.add(tblFiscais = new Table("tblFiscais", getColumnsFiscais(), getCollectionProviderFiscais()));
        tblFiscais.populate();
        containerMaisProfissionais.setVisible(lancamentoAtividadesVigilancia.getCodigo() == null);

        getForm().add(dataAtividadeChooser = (DateChooser) new DateChooser(path(proxy.getLancamentoAtividadesVigilancia().getDataAtividade())).setEnabled(enabled));
        dataAtividadeChooser.addRequiredClass();
        dataAtividadeChooser.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        getForm().add(new AutoCompleteLancamentoAtividadeVigilanciaTipoAtividade(path(proxy.getLancamentoAtividadesVigilancia().getTipoAtividade())).setEnabled(enabled));

        // Dados Estabelecimento/Pessoa
        getForm().add(containerDadosEstabelecimentoPessoa = (WebMarkupContainer) new WebMarkupContainer("containerDadosEstabelecimentoPessoa").setEnabled(enabled));
        containerDadosEstabelecimentoPessoa.setOutputMarkupPlaceholderTag(true);

        containerDadosEstabelecimentoPessoa.add(getDropDownTipoEstabelecimento(path(proxy.getLancamentoAtividadesVigilancia().getFlagTipo())));
        dropDownTipoPessoa.setOutputMarkupPlaceholderTag(true);
        dropDownTipoPessoa.addAjaxUpdateValue();
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCampos(target);
            }
        });

        containerDadosEstabelecimentoPessoa.add(containerPessoa = new WebMarkupContainer("containerPessoa"));
        containerPessoa.setOutputMarkupPlaceholderTag(true);
        containerPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getLancamentoAtividadesVigilancia().getVigilanciaPessoa()))).setVisible(enabled);
        autoCompleteConsultaVigilanciaPessoa.addAjaxUpdateValue();

        containerPessoa.add(getDropDownFisicaJuridica());

        containerDadosEstabelecimentoPessoa.add(containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento"));
        containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getLancamentoAtividadesVigilancia().getEstabelecimento()), false));
        autoCompleteConsultaEstabelecimento.addAjaxUpdateValue();

        containerDadosEstabelecimentoPessoa.add(containerCpf = new WebMarkupContainer("containerCpf"));
        containerCpf.add(txtCpf = new InputField("cpf", new PropertyModel<String>(this, "cpf")));
        containerCpf.setOutputMarkupId(true);
        txtCpf.setOutputMarkupPlaceholderTag(true);
        txtCpf.setEnabled(false);

        containerDadosEstabelecimentoPessoa.add(containerCnpj = new WebMarkupContainer("containerCnpj"));
        containerCnpj.add(txtCnpj = new InputField("cnpj", new PropertyModel<String>(this, "cnpj")));
        containerCnpj.setOutputMarkupId(true);
        txtCnpj.setOutputMarkupPlaceholderTag(true);
        txtCnpj.setEnabled(false);

        containerDadosEstabelecimentoPessoa.add(containerAtividadeEstabelecimento = new WebMarkupContainer("containerAtividadeEstabelecimento"));
        containerAtividadeEstabelecimento.add(pnlAtividadeEstabelecimento = new PnlAtividadeEstabelecimento(path(proxy.getLancamentoAtividadesVigilancia().getAtividadeEstabelecimento())));
        pnlAtividadeEstabelecimento.setOutputMarkupPlaceholderTag(true);

        containerDadosEstabelecimentoPessoa.add(containerOutros = new WebMarkupContainer("containerOutros"));
        containerOutros.setOutputMarkupId(true);
        containerOutros.add(txtDescricaoOutros = new InputField(path(proxy.getLancamentoAtividadesVigilancia().getDescricaoOutros())));
        txtDescricaoOutros.setOutputMarkupPlaceholderTag(true);
        txtDescricaoOutros.setEnabled(false);

        configurarAutoCompleteEstabelecimento();
        configurarAutoCompleteVigilanciaPessoa();
        getForm().add(containerDadosEstabelecimentoPessoa);

        getForm().add(new InputArea(path(proxy.getLancamentoAtividadesVigilancia().getObservacao())).setEnabled(enabled));
        WebMarkupContainer containerDetalhesRequerimento;
        getForm().add(containerDetalhesRequerimento = new WebMarkupContainer("containerDetalhesRequerimento"));
        containerDetalhesRequerimento.add(new InputArea("detalhesRequerimentoVigilancia", new PropertyModel<String>(this, "detalhesRequerimentoVigilancia")));
        containerDetalhesRequerimento.setVisible(lancamentoAtividadesVigilancia.getCodigo() != null && lancamentoAtividadesVigilancia.getRequerimentoVigilancia() != null);
        containerDetalhesRequerimento.setEnabled(false);
        containerDetalhesRequerimento.setOutputMarkupId(true);

//      Inicio item
        containerItem = new WebMarkupContainer("containerItem") {
            @Override
            protected void onConfigure() {
                this.setEnabled(lancamentoAtividadesVigilancia.getCodigo() == null);
            }
        };

        containerItem.setOutputMarkupId(true);
        containerItem.setDefaultModel(modelItem = new CompoundPropertyModel<>(new LancamentoAtividadesVigilanciaItem()));

        LancamentoAtividadesVigilanciaItem proxyItem = on(LancamentoAtividadesVigilanciaItem.class);
        containerItem.add(autoCompleteConsultaAtividadesVigilancia = new AutoCompleteConsultaAtividadesVigilancia(path(proxyItem.getAtividadesVigilancia())));
        autoCompleteConsultaAtividadesVigilancia.add(new ConsultaListener<AtividadesVigilancia>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, AtividadesVigilancia object) {
                modelItem.getObject().setPontuacao(object.getPontuacao());
                target.add(txtPontuacao);
            }
        });
        autoCompleteConsultaAtividadesVigilancia.add(new RemoveListener<AtividadesVigilancia>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, AtividadesVigilancia object) {
                modelItem.getObject().setPontuacao(null);
                target.add(txtPontuacao);
            }
        });

        containerItem.add(new LongField(path(proxyItem.getQuantidade())));
        containerItem.add(txtPontuacao = new DisabledDoubleField(path(proxyItem.getPontuacao())));

        containerItem.add(btnAdicionarItem = new AbstractAjaxButton("btnAdicionarItem") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (modelItem.getObject().getAtividadesVigilancia() == null) {
                    throw new ValidacaoException("Por favor, informe a atividade.");
                }
                if (modelItem.getObject().getQuantidade() == null) {
                    throw new ValidacaoException("Por favor, informe a quantidade.");
                }
                CrudUtils.adicionarItem(target, tblLancamentosItem, CadastroLancamentoAtividadesVigilanciaPage.this.getForm().getModel().getObject().getLancamentoAtividadesVigilanciaItemList(), modelItem.getObject());
                limparItem(target);
            }
        }).setEnabled(enabled);

        containerItem.add(tblLancamentosItem = new Table("tblLancamentosItem", getColumns(), getCollectionProvider()));
        tblLancamentosItem.populate();
        tblLancamentosItem.setScrollY("1800");
        tblLancamentosItem.setEnabled(lancamentoAtividadesVigilancia.getCodigo() == null);

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }).setVisible(enabled));


        getForm().add(containerItem);

        add(getForm());
        carregarDados();
        enableCampos(null);
    }

    private DropDown getDropDownTipoEstabelecimento(String id) {
        if (dropDownTipoPessoa == null) {

            dropDownTipoPessoa = new RequiredDropDown(id);
            dropDownTipoPessoa.addChoice(LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value(), BundleManager.getString("estabelecimento"));
            dropDownTipoPessoa.addChoice(LancamentoAtividadesVigilancia.TipoPessoa.PESSOA.value(), BundleManager.getString("pessoa"));
            if (RepositoryComponentDefault.NAO_LONG.equals(configuracaoVigilancia.getFlagObrigaDadosEstabelecimento())) {
                dropDownTipoPessoa.addChoice(LancamentoAtividadesVigilancia.TipoPessoa.OUTROS.value(), BundleManager.getString("outros"));
            }
        }
        return dropDownTipoPessoa;
    }

    private DropDown getDropDownFisicaJuridica() {
        if (dropDownFisicaJuridica == null) {
            dropDownFisicaJuridica = new DropDown<Long>("tipoPessoa", new PropertyModel<Long>(this, "tipoPessoa"));
            dropDownFisicaJuridica.setOutputMarkupPlaceholderTag(true);
            dropDownFisicaJuridica.addAjaxUpdateValue();

            dropDownFisicaJuridica.addChoice(LancamentoAtividadesVigilanciaDTO.TipoPessoa.FISICA.value(), BundleManager.getString("fisica"));
            dropDownFisicaJuridica.addChoice(LancamentoAtividadesVigilanciaDTO.TipoPessoa.JURIDICA.value(), BundleManager.getString("juridica"));

            dropDownFisicaJuridica.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    onChangeTipoPessoa(target);
                }
            });
        }
        return dropDownFisicaJuridica;
    }

    private void onChangeTipoPessoa(AjaxRequestTarget target) {
        if (LancamentoAtividadesVigilanciaDTO.TipoPessoa.FISICA.value().equals(tipoPessoa)) {
            containerCpf.setVisible(true);
            containerCnpj.setVisible(false);
            txtCpf.setEnabled(true);
            txtCpf.addRequiredClass();
            if (target != null) {
                txtCpf.limpar(target);
            }
        } else {
            containerCpf.setVisible(false);
            containerCnpj.setVisible(true);
            txtCnpj.setEnabled(true);
            txtCnpj.addRequiredClass();
            if (target != null) {
                txtCnpj.limpar(target);
            }
        }
        if (target != null) {
            target.add(containerDadosEstabelecimentoPessoa);
            target.appendJavaScript(initMasks());
            target.appendJavaScript(JScript.removeAutoCompleteDrop());
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        LancamentoAtividadesVigilanciaItem proxy = on(LancamentoAtividadesVigilanciaItem.class);

        columns.add(getActionColumnItem());
        columns.add(createColumn(bundle("atividade"), proxy.getAtividadesVigilancia().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(((DoubleColumn) createColumn(bundle("pontuacao"), proxy.getPontuacao())));

        return columns;
    }

    private IColumn getActionColumnItem() {
        return new MultipleActionCustomColumn<LancamentoAtividadesVigilanciaItem>() {
            @Override
            public void customizeColumn(LancamentoAtividadesVigilanciaItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<LancamentoAtividadesVigilanciaItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, LancamentoAtividadesVigilanciaItem modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblLancamentosItem, getForm().getModel().getObject().getLancamentoAtividadesVigilanciaItemList(), modelObject);
                        if (modelObject.getCodigo() != null) {
                            getForm().getModel().getObject().getItensRemovidos().add(modelObject);
                        }
                    }
                });
            }
        };
    }

    private void limparItem(AjaxRequestTarget target) {
        modelItem.setObject(new LancamentoAtividadesVigilanciaItem());
        autoCompleteConsultaAtividadesVigilancia.limpar(target);
        target.add(containerItem);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (getForm().getModel().getObject().getLancamentoAtividadesVigilanciaItemList() == null) {
                    getForm().getModel().getObject().setLancamentoAtividadesVigilanciaItemList(new ArrayList<LancamentoAtividadesVigilanciaItem>());
                }
                return getForm().getModel().getObject().getLancamentoAtividadesVigilanciaItemList();
            }
        };
    }

    private Form<LancamentoAtividadesVigilanciaDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new LancamentoAtividadesVigilanciaDTO()));
            form.getModel().getObject().setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        }
        return form;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProfissional.getTxtDescricao().getTextField();
    }

    private void salvar() throws DAOException, ValidacaoException {
        LancamentoAtividadesVigilanciaDTO dto = getForm().getModel().getObject();
        dto.setFaturavel(true);
        dto.setFiscaisList(this.fiscaisList);

        if (LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value().equals(dto.getLancamentoAtividadesVigilancia().getFlagTipo()) && dto.getLancamentoAtividadesVigilancia().getEstabelecimento() == null) {
            throw new ValidacaoException(bundle("informeEstabelecimento"));
        } else if (LancamentoAtividadesVigilancia.TipoPessoa.PESSOA.value().equals(dto.getLancamentoAtividadesVigilancia().getFlagTipo()) && dto.getLancamentoAtividadesVigilancia().getVigilanciaPessoa() == null) {
            throw new ValidacaoException(bundle("informePessoa"));
        }

        if (cpf != null && cnpj != null) {
            throw new ValidacaoException(BundleManager.getString("mensagem_informe_somente_cpf_cnpj"));
        } else if (cpf != null && cnpj == null) {
            dto.getLancamentoAtividadesVigilancia().setCnpjCpfPessoa(StringUtil.getDigits(cpf));
        } else if (cpf == null && cnpj != null) {
            dto.getLancamentoAtividadesVigilancia().setCnpjCpfPessoa(StringUtil.getDigits(cnpj));
        } else if (cpf == null && cnpj == null && !LancamentoAtividadesVigilancia.TipoPessoa.OUTROS.value().equals(dto.getLancamentoAtividadesVigilancia().getFlagTipo())) {
            throw new ValidacaoException(BundleManager.getString("mensagem_informe_cpf_cnpj"));
        }

        if (dto.getLancamentoAtividadesVigilancia() != null && dto.getLancamentoAtividadesVigilancia().getCodigo() != null) { //Edição
            BOFactoryWicket.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(dto);
        } else {
            BOFactoryWicket.getBO(VigilanciaFacade.class).gerarCadastroLancamentoAtividadesVigilancia(dto);
        }

        ConsultaLancamentoAtividadesVigilanciaPage page = new ConsultaLancamentoAtividadesVigilanciaPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    private void carregarDados() {
        if (lancamentoAtividadesVigilancia != null && lancamentoAtividadesVigilancia.getCodigo() != null) {
            lancamentoAtividadesVigilancia = LoadManager.getInstance(LancamentoAtividadesVigilancia.class)
                    .addProperties(new HQLProperties(LancamentoAtividadesVigilancia.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, LancamentoAtividadesVigilancia.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, LancamentoAtividadesVigilancia.PROP_VIGILANCIA_PESSOA).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, LancamentoAtividadesVigilancia.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, LancamentoAtividadesVigilancia.PROP_ATIVIDADE_ESTABELECIMENTO).getProperties())
                    .setId(lancamentoAtividadesVigilancia.getCodigo())
                    .start().getVO();

            if (lancamentoAtividadesVigilancia.getCnpjCpfPessoa() != null && lancamentoAtividadesVigilancia.getCnpjCpfPessoa().length() == 14) {
                cnpj = lancamentoAtividadesVigilancia.getCnpjCpfPessoa();
                tipoPessoa = LancamentoAtividadesVigilanciaDTO.TipoPessoa.JURIDICA.value();
            } else {
                cpf = lancamentoAtividadesVigilancia.getCnpjCpfPessoa();
                tipoPessoa = LancamentoAtividadesVigilanciaDTO.TipoPessoa.FISICA.value();
            }
            atualizarDadosFromEstabelecimento(null, lancamentoAtividadesVigilancia.getEstabelecimento());
            atualizarDadosFromVigilanciaPessoa(null, lancamentoAtividadesVigilancia.getVigilanciaPessoa());
            containerDadosEstabelecimentoPessoa.setEnabled(false);

            LancamentoAtividadesVigilanciaItem proxy = on(LancamentoAtividadesVigilanciaItem.class);
            List<LancamentoAtividadesVigilanciaItem> list = LoadManager.getInstance(LancamentoAtividadesVigilanciaItem.class)
                    .addProperties(new HQLProperties(LancamentoAtividadesVigilanciaItem.class).getProperties())
                    .addParameter(new QueryCustomParameter(path(proxy.getLancamentoAtividadesVigilancia().getCodigo()), lancamentoAtividadesVigilancia.getCodigo()))
                    .addSorter(new QueryCustomSorter(path(proxy.getDataCadastro()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                getForm().getModel().getObject().setLancamentoAtividadesVigilanciaItemList(list);
            }

            if (lancamentoAtividadesVigilancia.getRequerimentoVigilancia() != null) {
                // CARREGAR TEXTO DETALHES REQUERIMENTO VIGILANCIA
                RequerimentoVigilancia requerimento = (RequerimentoVigilancia) LoadManager.getInstance(RequerimentoVigilancia.class)
                        .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                        .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                        .addProperties(new HQLProperties(TipoSolicitacao.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_TIPO_SOLICITACAO)).getProperties())
                        .addParameter(new QueryCustomParameter(RequerimentoVigilancia.PROP_CODIGO, lancamentoAtividadesVigilancia.getRequerimentoVigilancia().getCodigo()))
                        .start().getVO();
                if (requerimento != null) {
                    StringBuilder builder = new StringBuilder("Protocolo Nº: ");
                    builder.append(requerimento.getProtocoloFormatado());
                    builder.append(" referente a solicitação de: ");
                    builder.append(requerimento.getDescricaoTipoDocumentoComPlaca());
                    builder.append(" para ");
                    builder.append(requerimento.getEstabelecimentoPessoaProfissionalFormatado());
                    builder.append(". Data do requerimento: ");
                    builder.append(requerimento.getDataRequerimentoFormatado());
                    if (requerimento.getDataFinalizacao() != null) {
                        builder.append(". Data de Finalização: ");
                        builder.append(requerimento.getDataFinalizacaoFormatado());
                        builder.append(".");
                    }
                    detalhesRequerimentoVigilancia = builder.toString();
                }
            }
        } else {
            lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        }
    }

    private List<IColumn> getColumnsFiscais() {
        List<IColumn> columns = new ArrayList();

        Profissional proxy = on(Profissional.class);
        columns.add(getActionColumnFiscais());
        columns.add(createColumn(bundle("fiscal"), proxy.getNome()));

        return columns;
    }

    private IColumn getActionColumnFiscais() {
        return new MultipleActionCustomColumn<Profissional>() {

            @Override
            public void customizeColumn(final Profissional rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Profissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, Profissional modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < fiscaisList.size(); i++) {
                            Profissional item = fiscaisList.get(i);
                            if (item == rowObject) {
                                fiscaisList.remove(i);
                            }
                        }
                        tblFiscais.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderFiscais() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return fiscaisList;
            }
        };
    }

    private void adicionarFiscais(AjaxRequestTarget target, Object object) throws ValidacaoException {
        Profissional fiscal = (Profissional) object;
        validarAddFiscal(fiscal);
        fiscaisList.add(fiscal);
        tblFiscais.update(target);
        autoCompleteConsultaProfissional.limpar(target);
    }

    private void validarAddFiscal(Profissional fiscal) throws ValidacaoException {
        if (fiscal == null) {
            throw new ValidacaoException(bundle("msgInformeFiscal"));
        }

        Profissional unique = Lambda.selectUnique(fiscaisList, Lambda.having(on(Profissional.class).getCodigo(), Matchers.equalTo(fiscal.getCodigo())));
        if (unique != null) {
            throw new ValidacaoException(bundle("msgFiscalAdicionado"));
        }
    }

    private void enableCampos(AjaxRequestTarget target) {
        if (getForm().getModel().getObject().getLancamentoAtividadesVigilancia().getFlagTipo() == null) {
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setFlagTipo(LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value());
        }

        if (getForm().getModel().getObject().getLancamentoAtividadesVigilancia().getRequerimentoVigilancia() != null) {
            containerDadosEstabelecimentoPessoa.setEnabled(false);
        }

        if (LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value().equals(getForm().getModelObject().getLancamentoAtividadesVigilancia().getFlagTipo())) {
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setVigilanciaPessoa(null);
            containerEstabelecimento.setVisible(true);
            containerPessoa.setVisible(false);
            containerOutros.setVisible(false);
            pnlAtividadeEstabelecimento.setEnabled(false);
            containerAtividadeEstabelecimento.setVisible(true);
            onChangeTipoPessoa(target);
        } else if (LancamentoAtividadesVigilancia.TipoPessoa.PESSOA.value().equals(getForm().getModelObject().getLancamentoAtividadesVigilancia().getFlagTipo())) {
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setEstabelecimento(null);
            containerEstabelecimento.setVisible(false);
            containerPessoa.setVisible(true);
            containerOutros.setVisible(false);
            pnlAtividadeEstabelecimento.setEnabled(true);
            containerAtividadeEstabelecimento.setVisible(true);
            onChangeTipoPessoa(target);
        } else {
            containerPessoa.setVisible(false);
            containerEstabelecimento.setVisible(false);
            containerCnpj.setVisible(false);
            containerCpf.setVisible(false);
            containerAtividadeEstabelecimento.setVisible(false);
            txtDescricaoOutros.setEnabled(true);
            containerOutros.setVisible(true);
        }


        if (target != null) {
            autoCompleteConsultaEstabelecimento.limpar(target);
            autoCompleteConsultaVigilanciaPessoa.limpar(target);
            txtCpf.limpar(target);
            txtCnpj.limpar(target);
            pnlAtividadeEstabelecimento.limpar(target);
            target.add(containerDadosEstabelecimentoPessoa);
            target.appendJavaScript(initMasks());
            target.appendJavaScript(JScript.removeAutoCompleteDrop());
        }
    }

    private void configurarAutoCompleteEstabelecimento() {
        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosFromEstabelecimento(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosFromEstabelecimento(target, null);
            }
        });

    }

    private void configurarAutoCompleteVigilanciaPessoa() {
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarDadosFromVigilanciaPessoa(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarDadosFromVigilanciaPessoa(target, null);
            }
        });

    }

    private void atualizarDadosFromEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (target != null) {
            pnlAtividadeEstabelecimento.limpar(target);
            autoCompleteConsultaEstabelecimento.limpar(target);
            txtCpf.limpar(target);
            txtCnpj.limpar(target);
        }

        if (estabelecimento != null) {
            if (estabelecimento.getCnpjCpf() != null && estabelecimento.getCnpjCpf().length() == 14) {
                cnpj = estabelecimento.getCnpjCpfFormatado();
                cpf = null;
                containerCnpj.setVisible(true);
                containerCpf.setVisible(false);
                tipoPessoa = LancamentoAtividadesVigilanciaDTO.TipoPessoa.JURIDICA.value();
            } else {
                cpf = estabelecimento.getCnpjCpfFormatado();
                cnpj = null;
                containerCpf.setVisible(true);
                containerCnpj.setVisible(false);
                tipoPessoa = LancamentoAtividadesVigilanciaDTO.TipoPessoa.FISICA.value();
            }
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setNomePessoa(estabelecimento.getRazaoSocial());
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setAtividadeEstabelecimento(carregarEstabelecimentoAtividade(estabelecimento));
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setCnpjCpfPessoa(estabelecimento.getCnpjCpf());
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setEstabelecimento(estabelecimento);
        } else {
            if (target != null) {
                ComponentUtils.limparContainer(containerEstabelecimento, target);
            }
        }
        if (target != null) {
            target.add(pnlAtividadeEstabelecimento);
            target.add(containerDadosEstabelecimentoPessoa);
            target.add(dropDownFisicaJuridica);
            target.appendJavaScript(JScript.removeAutoCompleteDrop());
        }
    }

    private void atualizarDadosFromVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        if (target != null) {
            pnlAtividadeEstabelecimento.limpar(target);
            autoCompleteConsultaVigilanciaPessoa.limpar(target);
            txtCpf.limpar(target);
            txtCnpj.limpar(target);
        }

        if (vigilanciaPessoa != null) {
            if (vigilanciaPessoa.getCpf() != null) {
                cpf = vigilanciaPessoa.getCpfFormatado();
                cnpj = null;
                containerCnpj.setVisible(false);
                containerCpf.setVisible(true);
                tipoPessoa = LancamentoAtividadesVigilanciaDTO.TipoPessoa.FISICA.value();
            }
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setAtividadeEstabelecimento(vigilanciaPessoa.getAtividadeEstabelecimento());
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setNomePessoa(vigilanciaPessoa.getNome());
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setCnpjCpfPessoa(vigilanciaPessoa.getCpf());
            getForm().getModel().getObject().getLancamentoAtividadesVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
        } else {
            if (target != null) {
                ComponentUtils.limparContainer(containerPessoa, target);
            }
        }
        if (target != null) {
            target.add(pnlAtividadeEstabelecimento);
            target.add(containerDadosEstabelecimentoPessoa);
            target.add(dropDownFisicaJuridica);
            target.appendJavaScript(JScript.removeAutoCompleteDrop());
        }
    }

    private AtividadeEstabelecimento carregarEstabelecimentoAtividade(Estabelecimento estabelecimento) {
        EstabelecimentoAtividade vo = LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                .addProperties(new HQLProperties(AtividadeEstabelecimento.class, EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, RepositoryComponentDefault.SIM_LONG))
                .start().getVO();
        if (vo != null) {
            return vo.getAtividadeEstabelecimento();
        }
        return null;
    }


    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLancamentoAtividadesVigilancia");
    }

}
