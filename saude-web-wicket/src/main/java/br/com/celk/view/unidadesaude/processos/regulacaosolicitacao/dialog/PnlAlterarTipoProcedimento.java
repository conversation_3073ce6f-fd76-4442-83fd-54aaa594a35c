package br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAlterarTipoProcedimento extends Panel {

    private CompoundPropertyModel<SolicitacaoAgendamento> model;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private TipoProcedimento tipoProcedimento;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;

    public PnlAlterarTipoProcedimento(String id) {
        super(id);
        init();
    }

    private void init() {
        Form<SolicitacaoAgendamento> form = new Form<SolicitacaoAgendamento>("form", model = new CompoundPropertyModel(new SolicitacaoAgendamento()));
        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);

        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getDataSolicitacao())));
        form.add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento(path(proxy.getTipoProcedimento()), new PropertyModel(this, "tipoProcedimento")));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarConfirmacaoEnvio();
                onConfirmar(target, tipoProcedimento);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        add(form);
    }

    private void validarConfirmacaoEnvio() throws ValidacaoException {
        if (tipoProcedimento == null) {
            throw new ValidacaoException(BundleManager.getString("campoXObrigatorio", BundleManager.getString("tipoProcedimento")));
        } else if (tipoProcedimento.equals(model.getObject().getTipoProcedimento())) {
            throw new ValidacaoException(BundleManager.getString("msgPorFavorInformeTipoProcedimentoDiferenteAtual"));
        } else if (RepositoryComponentDefault.NAO.equals(tipoProcedimento.getRegulado())) {
            throw new ValidacaoException(BundleManager.getString("msgNaoPossivelAlterarSolicitacaoTipoProcedimentoSelecionadoTipoNaoRegulado"));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaTipoProcedimento.limpar(target);
    }

    public void setSolicitacaoAgendamento(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        this.model.setObject(solicitacaoAgendamento);
        autoCompleteConsultaTipoProcedimento.setTipoProcedimentoPrincipal(solicitacaoAgendamento.getTipoProcedimento());
        autoCompleteConsultaTipoProcedimento.setClassificacao(TipoProcedimentoClassificacao.Classificacao.CONSULTAS.value());
        autoCompleteConsultaTipoProcedimento.setRegulado(true);
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, TipoProcedimento tipoProcedimento) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
