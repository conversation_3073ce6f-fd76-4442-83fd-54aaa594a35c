package br.com.celk.view.vigilancia.externo.template.base;

import br.com.celk.component.link.AbstractAjaxLink;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;

/**
 * <AUTHOR>
 */
public abstract class NodeButtonConsultaConfUsuarioVigilancia extends AbstractAjaxLink {

    private IConfUsuarioVigilanciaNode consultaConfUsuarioVigilanciaNode;
    private boolean active;

    public NodeButtonConsultaConfUsuarioVigilancia(String id, IConfUsuarioVigilanciaNode recepcaoNode) {
        super(id);
        this.consultaConfUsuarioVigilanciaNode = recepcaoNode;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(new WebMarkupContainer("icon").add(new AttributeModifier("class", "icon32 " + consultaConfUsuarioVigilanciaNode.getIcone().clazz())));
        add(new Label("labelNode", consultaConfUsuarioVigilanciaNode.getTitulo()));
    }

    public IConfUsuarioVigilanciaNode getConsultaConfUsuarioVigilanciaNode() {
        return consultaConfUsuarioVigilanciaNode;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (active) {
            response.render(OnDomReadyHeaderItem.forScript("$('#" + getMarkupId() + "').addClass('active')"));
        }
    }

    public NodeButtonConfUsuarioVigilanciaEventListener createListener() {
        return new NodeButtonConfUsuarioVigilanciaEventListener() {
            @Override
            public void ativar(AjaxRequestTarget target, IConfUsuarioVigilanciaNode iConfUsuarioVigilanciaNode) {
                if (iConfUsuarioVigilanciaNode.getIdentificador().equals(getConsultaConfUsuarioVigilanciaNode().getIdentificador())) {
                    if (!active) {
                        active = true;
                        if (target != null) {
                            target.appendJavaScript("$('#" + getMarkupId() + "').addClass('active')");
                        }
                    }
                } else {
                    if (active) {
                        active = false;
                        if (target != null) {
                            target.appendJavaScript("$('#" + getMarkupId() + "').removeClass('active')");
                        }
                    }
                }
            }

        };
    }
}
