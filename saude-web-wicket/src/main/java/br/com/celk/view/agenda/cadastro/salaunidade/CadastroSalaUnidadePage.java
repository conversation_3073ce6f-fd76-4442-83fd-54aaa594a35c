package br.com.celk.view.agenda.cadastro.salaunidade;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroSalaUnidadePage extends CadastroPage<SalaUnidade>{
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private boolean permissionEmpresa;
    
    public CadastroSalaUnidadePage(SalaUnidade object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroSalaUnidadePage(SalaUnidade object) {
        this(object, false);
    }

    public CadastroSalaUnidadePage() {
        this(null); 
    }

    @Override
    public void init(Form form) {
        SalaUnidade proxy = on(SalaUnidade.class);
        
        permissionEmpresa = true;
        
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true).setValidaUsuarioEmpresa(!permissionEmpresa));
        form.add(new RequiredInputField<String>(path(proxy.getDescricao())));
        
        permissionEmpresa();
    }
    
    private void permissionEmpresa() {
        Usuario usuarioLogado = getUsuarioLogado();
        if (!isActionPermitted(usuarioLogado, Permissions.EMPRESA, ConsultaSalaUnidadePage.class)) {
            permissionEmpresa = false;
            autoCompleteConsultaEmpresa.setComponentValue(ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa());
            autoCompleteConsultaEmpresa.setEnabled(false);
        }
    }
    
    public Usuario getUsuarioLogado() {
        return ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
    }
    
    
    
    @Override
    public Class<SalaUnidade> getReferenceClass() {
        return SalaUnidade.class;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
    
    @Override
    public Class getResponsePage() {
        return ConsultaSalaUnidadePage.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroSalaXUnidade");
    }  

    @Override
    public Object salvar(SalaUnidade object) throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(SalaUnidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_DESCRICAO, object.getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_EMPRESA, object.getEmpresa()));
        
        if(object.getCodigo() != null){
            load.addParameter(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }
        
        SalaUnidade salaUnidade = load.start().getVO();
        
        if(salaUnidade != null){
            throw new ValidacaoException(bundle("msgJaExisteUmaSalaComEssaDescricaoParaEssaUnidade"));
        }
        
        return super.salvar(object);
    }
}