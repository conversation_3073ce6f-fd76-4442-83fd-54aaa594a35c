package br.com.celk.view.materiais.dispensacao.impressaoprescricao.dlg;

import br.com.celk.component.window.Window;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConsultarPrescricao extends Window {

    private PnlConsultarPrescricao pnlConsultarPrescricao;

    public DlgConsultarPrescricao(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(1000);
        setInitialHeight(280);

        setResizable(true);

        setTitle(bundle("detalhes"));

        setContent(pnlConsultarPrescricao = new PnlConsultarPrescricao(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, Long codigo) {
        show(target);
        pnlConsultarPrescricao.setReceituario(codigo);
    }

}
