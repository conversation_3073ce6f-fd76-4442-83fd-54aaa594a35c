package br.com.celk.view.agenda.agendamento.manutencaoagendamento.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.exame.dto.CancelamentoAgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCancelamentoAgendamento extends Window{
    
    private PnlCancelamentoAgendamento pnlCancelamentoAgendamento;
    
    public DlgCancelamentoAgendamento(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("cancelamentoAgendamento");
            }
        });
                
        setInitialWidth(500);
        setInitialHeight(210);
        setResizable(true);
        
        setContent(pnlCancelamentoAgendamento = new PnlCancelamentoAgendamento(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, CancelamentoAgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgCancelamentoAgendamento.this.onConfirmar(target, dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, CancelamentoAgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarioList){
        show(target);
        pnlCancelamentoAgendamento.setObject(target, agendaGradeAtendimentoHorarioList);
    }    
}