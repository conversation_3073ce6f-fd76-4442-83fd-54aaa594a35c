package br.com.celk.view.cadsus.confirmacaoexclusaopaciente;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class DetalhesExclusaoPacientePage extends BasePage {

    private Form<UsuarioCadsusExclusao> form;
    private final UsuarioCadsusExclusao usuarioCadsusExclusao;
    
    public DetalhesExclusaoPacientePage(UsuarioCadsusExclusao usuarioCadsusExclusao) {
        this.usuarioCadsusExclusao = usuarioCadsusExclusao;
        init();
    }
    
    private void init() {
        UsuarioCadsusExclusao proxy = on(UsuarioCadsusExclusao.class);
        
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeMae())));
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getDataNascimento())));
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCadsus().getSexoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getDescricaoSituacaoPaciente())));
        getForm().add(new DisabledInputField(path(proxy.getDescricaoMotivoExclusaoPaciente())));
        
        getForm().add(new DisabledInputField(path(proxy.getUsuario().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getDataCadastro())));
        
        getForm().add(new DisabledInputField(path(proxy.getUsuarioCancelamento().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getDataCancelamento())));
        
        getForm().add(new DisabledInputField(path(proxy.getUsuarioConfirmacao().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getDataConfirmacao())));
        
        getForm().add(new DisabledInputField(path(proxy.getUsuarioReversao().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getDataReversao())));
        getForm().add(new DisabledInputArea(path(proxy.getMotivoReversao())));
        
        getForm().add(new DisabledInputField(path(proxy.getUsuarioUnificacao().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getDataUnificacao())));
        
        getForm().add(new VoltarButton("btnVoltar"));
        
        add(getForm());
    }
    
    private Form<UsuarioCadsusExclusao> getForm(){
        if(form == null){
            form = new Form("form", new CompoundPropertyModel(usuarioCadsusExclusao));
        }
        return form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesConfirmacaoExclusaoPaciente");
    }
    
}
