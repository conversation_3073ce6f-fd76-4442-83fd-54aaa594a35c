package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.TipoOcorrenciaAgravo;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoOcorrenciaAgravoPage extends CadastroPage<TipoOcorrenciaAgravo>{
    
    private InputField txtDescricao;
    
    public CadastroTipoOcorrenciaAgravoPage(TipoOcorrenciaAgravo object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoOcorrenciaAgravoPage(TipoOcorrenciaAgravo object) {
        this(object, false);
    }

    public CadastroTipoOcorrenciaAgravoPage() {
        this(null); 
    }

    @Override
    public void init(Form form) {
        TipoOcorrenciaAgravo proxy = on(TipoOcorrenciaAgravo.class);
        
        form.add(txtDescricao = new RequiredInputField<String>(path(proxy.getDescricao())));
    }
    
    @Override
    public Class<TipoOcorrenciaAgravo> getReferenceClass() {
        return TipoOcorrenciaAgravo.class;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
    
    @Override
    public Class getResponsePage() {
        return ConsultaTipoOcorrenciaAgravoPage.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoOcorrenciaAgravo");
    }  

    @Override
    public Object salvar(TipoOcorrenciaAgravo object) throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(TipoOcorrenciaAgravo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoOcorrenciaAgravo.PROP_DESCRICAO, object.getDescricao()));
        
        if(object.getCodigo() != null){
            load.addParameter(new QueryCustom.QueryCustomParameter(TipoOcorrenciaAgravo.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }
        
        TipoOcorrenciaAgravo tipoOcorrenciaAgravo = load.start().getVO();
        
        if(tipoOcorrenciaAgravo != null){
            throw new ValidacaoException(bundle("msgJaExisteUmTipoOcorrenciaComEssaDescricao"));
        }
        
        return super.salvar(object);
    }
}