package br.com.celk.view.vigilancia.externo.view.servicos.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoAtividadeEconomicaExternoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoRepresentanteLegalExternoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoEnderecoExternoPage;
import br.com.celk.view.vigilancia.requerimentos.contratosocial.RequerimentoAlteracaoRazaoSocialExternoPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlEscolherTipoContratoSocialExterno extends Panel {

    private Form form;
    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnRepresentanteLegal;
    private AbstractAjaxButton btnAtividadeEconomica;
    private TipoSolicitacao tipoSolicitacaoSelecionado;

    public PnlEscolherTipoContratoSocialExterno(String id, boolean possuiPermissaoRepresentanteLegal, boolean possuiPermissaoAtividadeEconomica, boolean possuiPermissaoAlteracaoEndereco, boolean possuiPermissaoAlteracaoRazaoSocial) {
        super(id);
        init(possuiPermissaoRepresentanteLegal, possuiPermissaoAtividadeEconomica, possuiPermissaoAlteracaoEndereco, possuiPermissaoAlteracaoRazaoSocial);
    }

    private void init(boolean possuiPermissaoRepresentanteLegal, boolean possuiPermissaoAtividadeEconomica, boolean possuiPermissaoAlteracaoEndereco, boolean possuiPermissaoAlteracaoRazaoSocial) {
        setOutputMarkupId(true);

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(btnRepresentanteLegal = new AbstractAjaxButton("btnRepresentanteLegal") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value());
                setResponsePage(new RequerimentoRepresentanteLegalExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
            }
        });
        btnRepresentanteLegal.setVisible(possuiPermissaoRepresentanteLegal);

        form.add(btnAtividadeEconomica = new AbstractAjaxButton("btnAtividadeEconomica") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value());
                setResponsePage(new RequerimentoAtividadeEconomicaExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        });
        btnAtividadeEconomica.setVisible(possuiPermissaoAtividadeEconomica);

        form.add(new AbstractAjaxButton("btnAlterarEndereco") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value());
                setResponsePage(new RequerimentoAlteracaoEnderecoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAlteracaoEndereco));

        form.add(new AbstractAjaxButton("btnAlterarRazaoSocial") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_RAZAO_SOCIAL.value());
                setResponsePage(new RequerimentoAlteracaoRazaoSocialExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAlteracaoRazaoSocial));

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacaoSelecionado = tipoSolicitacao;
    }

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {
    }

}
