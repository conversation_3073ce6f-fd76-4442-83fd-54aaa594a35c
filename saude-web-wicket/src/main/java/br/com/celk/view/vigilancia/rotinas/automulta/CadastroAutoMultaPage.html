<wicket:extend>
    <div class="span-10 last">
        <fieldset wicket:id="containerDadosEdicao">
            <h2><label><wicket:message key="dadosAlteracao"/></label></h2>
            <div class="field">
                <div class="span-6"><label><wicket:message key="usuarioCriacao"/></label><input type="text" size="60" wicket:id="usuario.nome"/></div>
                <div class="span-3 left last"><label><wicket:message key="dataCadastro"/></label><div class="group" wicket:id="dataCadastro"/></div>
            </div>
            <div class="field">
                <div class="span-6"><label><wicket:message key="usuarioUltimaEdicao"/></label><input type="text" size="60" wicket:id="usuarioEdicao.nome"/></div>
                <div class="span-3 left last"><label><wicket:message key="dataUltimaEdicao"/></label><div class="group" wicket:id="dataUsuario"/></div>
            </div>
        </fieldset>
        <fieldset>
            <div wicket:id="containerDadosGeral">
                <h2><label><wicket:message key="dados"/></label></h2>
                <div class="field">
                    <div class="span-3">
                        <label><wicket:message key="codigoDenuncia"/></label><input type="text" size="20" maxlength="8" wicket:id="denuncia.codigo" class="number"/>
                    </div>
                    <div class="span-3">
                        <label><wicket:message key="numeroAutoIntimacaoAbv"/></label><input type="text" size="20" maxlength="8" wicket:id="autoIntimacao.numeroFormatado" class="number"/>
                    </div>
                    <div class="span-3 last">
                        <label><wicket:message key="numeroAutoInfracaoAbv"/></label><input type="text" size="20" maxlength="8" wicket:id="autoInfracao.numeroFormatado" class="number"/>
                    </div>
                </div>
                <div class="field">
                    <div class="span-5"><label><wicket:message key="numeroAutoMultaAbv"/></label><input type="text" size="20" maxlength="8" wicket:id="numeroFormatado" class="number"/></div>
                    <div class="span-5 last"><label><wicket:message key="dataMulta"/></label><div class="group" wicket:id="dataMulta"/></div>
                </div>
                <div class="field">
                    <div class="span-5"><label><wicket:message key="numeroFormulario"/></label><input type="text" size="20" maxlength="8" wicket:id="numeroFormulario" class="number"/></div>
                    <div class="span-5 last"><label><wicket:message key="protocolo"/></label><input type="text" size="20" maxlength="8" wicket:id="requerimentoVigilancia.protocoloFormatado" class="number"/></div>
                </div>
            </div>
            <div class="field">
                <div class="span-horizontal">
                    <fieldset wicket:id="containerAutuado">
                        <h2><label><wicket:message key="autuado"/></label></h2>
                        <div class="field no-line"><label><wicket:message key="tipo"/></label><select wicket:id="tipoAutuado"/></div>
                        <div wicket:id="containerAutuadoPessoa">
                            <div class="field no-line">
                                <label><wicket:message key="pessoa"/></label><div class="group" wicket:id="vigilanciaPessoa"/>
                            </div>
                        </div>
                        <div wicket:id="containerAutuadoEstabelecimento">
                            <div class="field no-line">
                                <label><wicket:message key="estabelecimento"/></label><div class="group" wicket:id="estabelecimento"/>
                            </div>
                        </div>
                        <div class="field no-line">
                            <div class="span-horizontal">
                                <fieldset>
                                    <h2><label><wicket:message key="dadosEndereco"/></label></h2>
                                    <div class="field no-line">
                                        <label><wicket:message key="endereco"/></label><div class="group" wicket:id="vigilanciaEndereco"/>
                                        <a href="#" wicket:id="btnCadastrarVigilanciaEndereco" class="icon round-plus" tabindex="-1" wicket:message="title:cadastroEndereco" />
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset wicket:id="containerEnquadramento">
                        <h2><label><wicket:message key="enquadramentoLegal"/></label></h2>
                        <div class="field no-line">
                            <div class="span-horizontal">
                                <textarea maxlength="1000" wicket:id="enquadramentoLegal" style="width: 98%; margin-left: 10px;" />
                            </div>
                        </div>
                    </fieldset>
                    <fieldset wicket:id="containerEspecificacaoDetalhada">
                        <h2><label><wicket:message key="especificacaoDetalhadaAtoFatoInfracao"/></label></h2>
                        <div class="field no-line">
                            <div class="span-horizontal">
                                <textarea maxlength="5000" wicket:id="descricaoMulta" style="width: 98%; margin-left: 10px;" />
                            </div>
                        </div>
                        <div class="field ">
                            <div class="span-4"><label><wicket:message key="tipoTaxa"/></label><div class="group" wicket:id="taxa"/></div>
                            <div class="span-3"><label><wicket:message key="valorMulta"/></label><input type="text" size="15" maxlength="12" wicket:id="valorMulta" /></div>
                            <div class="span-3 last"><label><wicket:message key="total"/></label><input type="text" size="15" maxlength="12" wicket:id="valorTotalMulta" /></div>

                        </div>

                    </fieldset>
                    <fieldset wicket:id="containerFiscal">
                        <h2><label><wicket:message key="autoridadesSaude"/></label></h2>
                        <div class="field no-line"><label><wicket:message key="nome"/></label><div class="group" wicket:id="profissional"/></div>
                        <div class="field no-line">
                            <div class="span-horizontal">
                                <input type="button" class="arrow-bottom" wicket:id="btnAdicionarFiscal" wicket:message="value:adicionar"/>
                                <div wicket:id="tblFiscal"/>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset wicket:id="containerEnvio">
                        <h2><label><wicket:message key="dadosEnvio"/></label></h2>
                        <div class="field no-line">
                            <div class="span-3"><label><wicket:message key="enviado" /></label><select wicket:id="enviado" /></div>
                            <div class="span-3"><label><wicket:message key="dataRecebimento"/></label><div class="group" wicket:id="dataRecebimento"/></div>
                            <div class="span-3 last"><label><wicket:message key="hora"/></label><input type="text" wicket:id="hora" size="6"/></div>
                        </div>
                        <div class="field no-line">
                            <label><wicket:message key="motivoRetorno" /></label><div class="group" wicket:id="motivoRetorno" />
                            <a href="#" wicket:id="btnCadastrarMotivo" class="icon round-plus" tabindex="-1" wicket:message="title:cadastrarMotivoRetorno" />
                        </div>
                    </fieldset>
                </div>
                <div wicket:id="requerimentoVigilanciaAnexo"/>
            </div>
        </fieldset>
    </div>
</wicket:extend>