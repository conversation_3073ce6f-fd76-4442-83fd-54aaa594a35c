package br.com.celk.view.atendimento.recepcao.panel.examesautorizacao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCid;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCidServicoClassificacao;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarServicoClassificacao;
import br.com.celk.view.atendimento.recepcao.RecepcaoPage;
import br.com.celk.view.atendimento.recepcao.panel.confirmacaopresenca.ConfirmacaoPresencaoPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParamFilter;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoExamesDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimentocompetencia.interfaces.dto.ProcedimentoCompetenciaDTO;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCid;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCidPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * <AUTHOR>
 */
public class ConfirmacaoExamesPanel extends RecepcaoCadastroPanel {

    private Form<ConfirmacaoExamesDTO> form;

    private InputField txtNumeroProtocolo;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private ArrayList<ExameProcedimentoDTO> lstExameProcedimentoDto;
    private DateChooser dchDataSolicitacao;
    private MultiSelectionTable<ExameProcedimentoDTO> tableExame;
    private DropDown<Date> dropDownDataCompetencia;
    private Double saldo;
    private Long quantidadeAdd;
    private DisabledDoubleField txtSaldo;
    private Long codigoExameAutorizacao;
    private boolean enableField;
    private AbstractAjaxButton btnGerarConfirmacao;
    private AbstractAjaxLink btnConfirmar;
    private AbstractAjaxLink btnCancelar;
    private DlgConfirmacao dlgConfirmacao;
    private DropDown<Profissional> dropDownProfissional;
    private DlgInformarCid dlgInformarCid;
    private List<ExameProcedimentoDTO> exameProcedimentoDTOsConfirmados = new ArrayList<>();
    private List<ExameProcedimentoDTO> exameProcedimentoConfirmadoAux = new ArrayList<>();
    private AgendaGradeAtendimentoHorarioDTO agendaGradeAtendimentoHorarioDTO;
    private AgendaGradeAtendimentoDTOParamFilter dtoParamFilter;
    private String codAutorizacaoApac = "";
    private FileUpload uploads;
    private FileUploadField fileUploadField;
    private SubmitButton btnAnexar;

    private List<FileUpload> lstUpload;
    private Label lblAnexo;

    private DateChooser dchDataConfirmacao;
    private final Date dataAtual = DataUtil.getDataAtual();

    private WebMarkupContainer divDataConfirmacao;

    boolean habilitarDataDaConfirmacao;

    private ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO;
    private DlgInformarServicoClassificacao dlgInformarServicoClassificacao;
    private DlgInformarCidServicoClassificacao dlgInformarCidServicoClassificacao;


    public ConfirmacaoExamesPanel(String id) {
        super(id, bundle("confirmacaoExames"));
        lstExameProcedimentoDto = new ArrayList<>();
        this.enableField = true;
    }

    public ConfirmacaoExamesPanel(String id, AgendaGradeAtendimentoHorarioDTO agendaGradeAtendimentoHorarioDTO) throws ValidacaoException, DAOException {
        super(id, bundle("confirmacaoExames"));
        lstExameProcedimentoDto = new ArrayList<>();
        this.enableField = true;
        this.agendaGradeAtendimentoHorarioDTO = agendaGradeAtendimentoHorarioDTO;
        carregarDados();
    }

    public ConfirmacaoExamesPanel(String id, AgendaGradeAtendimentoHorarioDTO agendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParamFilter dtoParamFilter) throws ValidacaoException, DAOException {
        super(id, bundle("confirmacaoExames"));
        lstExameProcedimentoDto = new ArrayList<>();
        this.enableField = true;
        this.agendaGradeAtendimentoHorarioDTO = agendaGradeAtendimentoHorarioDTO;
        this.dtoParamFilter = dtoParamFilter;
        carregarDados();
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        ConfirmacaoExamesDTO proxy = on(ConfirmacaoExamesDTO.class);
        checarHabilitarDataConfirmacao();

        getForm().add(txtNumeroProtocolo = (InputField) new InputField(path(proxy.getNumeroProtocolo())).setEnabled(enableField));
        this.txtNumeroProtocolo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    carregarDados(target);
                } catch (ValidacaoException ex) {
                    Logger.getLogger(ConfirmacaoExamesPanel.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        });

        getForm().add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        autoCompleteConsultaUsuarioCadsus.setLabel(Model.of(BundleManager.getString("paciente"))).setEnabled(false);
        getForm().add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getProfissionalSolicitante())).setEnabled(false));
        autoCompleteConsultaProfissional.setLabel(Model.of(bundle("profissionalExecutante")));
        getForm().add(dchDataSolicitacao = (DateChooser) new RequiredDateChooser(path(proxy.getDataAutorizacao())).setLabel(new Model(bundle("dataSolicitacao"))).setEnabled(false));

        dchDataConfirmacao = (DateChooser) new RequiredDateChooser(path(proxy.getDataConfirmacao()))
                .setLabel(new Model(bundle("dataConfirmacao")));

        divDataConfirmacao = new WebMarkupContainer("divDataConfirmacao", new CompoundPropertyModel(form.getModel().getObject()));

        getForm().add(divDataConfirmacao);

        divDataConfirmacao.setOutputMarkupId(true);
        divDataConfirmacao.setVisible(habilitarDataDaConfirmacao);
        divDataConfirmacao.add(dchDataConfirmacao);

        getForm().add(getDropDownProfissional(path(proxy.getProfissionalExecutante())).setEnabled(false));

        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getUnidadeAutorizada())));
        autoCompleteConsultaEmpresa.setEnabled(false);

        getForm().add(tableExame = new ConfirmacaoExamesItemTableColor("tableExame", getColumns(), getCollectionProvider()));
        tableExame.populate();

        getForm().add(btnGerarConfirmacao = new AbstractAjaxButton("btnGerarConfirmacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                for (ExameProcedimentoDTO exameProcedimentoDTO : lstExameProcedimentoDto) {
                    if (!Lambda.exists(exameProcedimentoDTOsConfirmados, having(on(ExameProcedimentoDTO.class).getExameProcedimento().getCodigo(), equalTo(exameProcedimentoDTO.getExameProcedimento().getCodigo())))
                            && exameProcedimentoDTO.getStatus().equals(ExameProcedimentoDTO.Status.CONFIRMADO.value())) {
                        exameProcedimentoDTOsConfirmados.add(exameProcedimentoDTO);
                        exameProcedimentoConfirmadoAux.add(exameProcedimentoDTO);
                    }
                }

                try {
                    validarLaudoAnexado();
                    validarCidProcedimentosConfirmados(target);
                }catch(ValidacaoException ex){
                    throw ex;
                }

            }
        });
        btnGerarConfirmacao.setEnabled(false);

        getForm().add(btnConfirmar = new AbstractAjaxLink("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                trocarStatusItens(target, ExameProcedimentoDTO.Status.CONFIRMADO.value());
            }
        });
        getForm().add(btnCancelar = new AbstractAjaxLink("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                trocarStatusItens(target, ExameProcedimentoDTO.Status.CANCELADO.value());
            }
        });

        getForm().add(fileUploadField = new FileUploadField("uploads", new PropertyModel<List<FileUpload>>(this, "lstUpload")));
        form.add(lblAnexo = new Label("arquivoAnexado.name"));
        lblAnexo.setOutputMarkupId(true);

        form.add(btnAnexar = new SubmitButton("btnAnexar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                uploads = fileUploadField.getFileUpload();
                if (uploads != null) {
                    target.add(lblAnexo);

                    try {
                        if (uploads.getClientFileName().toLowerCase().endsWith(".pdf") || uploads.getClientFileName().toLowerCase().endsWith(".jpg")
                                || uploads.getClientFileName().toLowerCase().endsWith(".jpeg") || uploads.getClientFileName().toLowerCase().endsWith(".png")) {
                            File newFile = File.createTempFile("anexo", uploads.getClientFileName());
                            uploads.writeTo(newFile);
                            getForm().getModel().getObject().setArquivoAnexado(newFile);
                        } else {
                            throw new ValidacaoException(bundle("somentePossivelAnexarPdfJpgJpegPng"));
                        }
                    } catch (IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }

                }

                new TempHelperV2().save(getForm());
            }
        }));

        form.add(new AbstractAjaxLink("btnRemoverAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                uploads = null;
                getForm().getModel().getObject().setArquivoAnexado(null);
                getForm().getModel().getObject().setPossuiAnexo(false);
                target.add(lblAnexo);
                new TempHelperV2().save(getForm());
            }
        });

        if (agendaGradeAtendimentoHorarioDTO != null) {
            txtNumeroProtocolo.setEnabled(false);
            dropDownProfissional.setEnabled(true);
            populaDropDownProfissional(null);
            btnGerarConfirmacao.setEnabled(true);
            RecepcaoPage recepcaoPage = (RecepcaoPage) getPage();
            if (recepcaoPage != null && !recepcaoPage.isActionPermitted(Permissions.NAO_VALIDAR_UNIDADE_CONFIRMACAO_EXAMES)) {
                autoCompleteConsultaEmpresa.setEnabled(true);
            }
        }
        add(getForm());
    }

    private void checarHabilitarDataConfirmacao() {
        String parametroGem;
        try {
            parametroGem = BOFactory
                    .getBO(CommomFacade.class)
                    .modulo(Modulos.AGENDAMENTO)
                    .getParametro("permiteConfirmacaoPosterior");
        } catch (DAOException e) {
            Loggable.log.error("Não foi possível carregar parâmetro gem");
            parametroGem = "N";
        }

        habilitarDataDaConfirmacao = RepositoryComponentDefault.SIM.equals(parametroGem);
    }


    private void validaCargaHoraria() throws ValidacaoException, DAOException {
        boolean isValidaCargaHorariaAmbulatorial = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("validaCargaHorariaAmbulatorial"));

        if (isValidaCargaHorariaAmbulatorial) {
            Empresa unidadeAutorizada = getForm().getModelObject().getUnidadeAutorizada();
            Profissional profissionalExecutante = getForm().getModelObject().getProfissionalExecutante();

            if (profissionalExecutante != null && unidadeAutorizada != null) {
                ProfissionalCargaHoraria profissionalCargaHoraria = CiapHelper.getProfissionalCargaHoraria(profissionalExecutante, unidadeAutorizada);

                if (profissionalCargaHoraria == null || Coalesce.asLong(profissionalCargaHoraria.getCargaHorariaAmbulatorial()) == 0L) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_carga_horaria_nao_definida_profissional_executante"));
                }
            }
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ExameProcedimentoDTO on = on(ExameProcedimentoDTO.class);
        columns.add(createColumn(bundle("procedimento"), on.getExameProcedimento().getDescricaoFormatado()));
        columns.add(createColumn(bundle("tipoExame"), on.getExameProcedimento().getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), on.getQuantidade()));
        columns.add(createColumn(bundle("situacao"), on.getDescricaoSituacao()));
        columns.add(createColumn(bundle("valor"), on.getValor()));
        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstExameProcedimentoDto;
            }
        };
    }


    private Form<ConfirmacaoExamesDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new ConfirmacaoExamesDTO()));
        }
        return this.form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtNumeroProtocolo)));
        response.render(OnLoadHeaderItem.forScript(JScript.initExpandLinks()));
    }

    private void carregarDados() throws DAOException, ValidacaoException {
        lstExameProcedimentoDto.clear();

        getForm().getModel().getObject().setExame(carregarExameConfirmacao());
        if (getForm().getModel().getObject().getExame() != null) {
            if (getForm().getModel().getObject().getExame().getDataConfirmacao() != null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_exame_ja_confirmado_pelo_prestador"));
            }
            if (Exame.STATUS_CANCELADO.equals(getForm().getModel().getObject().getExame().getStatus())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_exame_cancelado"));
            }
            ExameAutorizacao exameAutorizacao = carregarExameAutorizacaoConfirmacao(getForm().getModel().getObject().getExame());
            populaDTO(getForm().getModel().getObject().getExame(), exameAutorizacao);

            List<ExameRequisicao> exameRequisicaoList = carregarExameRequisicao(getForm().getModel().getObject().getExame());

            if (CollectionUtils.isNotNullEmpty(exameRequisicaoList)) {
                populaListaExames(exameRequisicaoList);
            }
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existem_exames_a_serem_confirmados"));
        }
    }

    private Exame carregarExameConfirmacao() {
        LoadManager lm = LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addProperties(new HQLProperties(Exame.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME)).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS)).getProperties())
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_STATUS, QueryCustomParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameRequisicao.PROP_CODIGO), BuilderQueryCustom.QuerySorter.DECRESCENTE));

        if (agendaGradeAtendimentoHorarioDTO.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento() != null
                && agendaGradeAtendimentoHorarioDTO.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getCodigo() != null) {
            lm.addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO), agendaGradeAtendimentoHorarioDTO.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getCodigo()));
        } else {
            lm.addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_CODIGO), agendaGradeAtendimentoHorarioDTO.getAgendaGradeAtendimentoHorario().getCodigo()));
        }

        ExameRequisicao exameRequisicao = lm.setMaxResults(1).start().getVO();
        if (exameRequisicao != null) {
            return exameRequisicao.getExame();
        }
        return null;
    }

    private ExameAutorizacao carregarExameAutorizacaoConfirmacao(Exame exame) {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = loadAgendaGradeAtendimentoHorario(exame);
        if (agendaGradeAtendimentoHorario.getTipoProcedimento().getControleCota().equals("N")){
            return null;
        } else {
            return LoadManager.getInstance(ExameAutorizacao.class)
                    .addProperties(new HQLProperties(ExameAutorizacao.class).getProperties())
                    .addProperties(new HQLProperties(ExamePrestadorCompetencia.class, ExameAutorizacao.PROP_EXAME_PRESTADOR_COMPETENCIA).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(ExameAutorizacao.PROP_EXAME_PRESTADOR_COMPETENCIA, ExamePrestadorCompetencia.PROP_EMPRESA)).getProperties())
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameAutorizacao.PROP_EXAME, Exame.PROP_CODIGO), exame.getCodigo()))
                    .start().getVO();
        }
    }

    private AgendaGradeAtendimentoHorario loadAgendaGradeAtendimentoHorario(Exame exame) {
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS), exame.getUsuarioCadsus()))
                .setMaxResults(1).start().getVO();
    }

    private void populaDTO(Exame exame, ExameAutorizacao exameAutorizacao) throws ValidacaoException {
        getForm().getModel().getObject().setUsuarioCadsus(exame.getUsuarioCadsus());
        getForm().getModel().getObject().setProfissionalSolicitante(exame.getProfissional());
        getForm().getModel().getObject().setDataConfirmacao(dataAtual);
        if (exameAutorizacao != null) {
            getForm().getModel().getObject().setDataAutorizacao(exameAutorizacao.getDataCadastro());
            if (exameAutorizacao.getExamePrestadorCompetencia() != null) {
                getForm().getModel().getObject().setUnidadeAutorizada(exameAutorizacao.getExamePrestadorCompetencia().getEmpresa());
            } else {
                throw  new ValidacaoException(bundle("msg_exame_prestador_competencia"));
            }
        } else {
            if (exame.getDataAutorizacao() == null) {
                getForm().getModel().getObject().setDataAutorizacao(dataAtual);
            } else {
                getForm().getModel().getObject().setDataAutorizacao(exame.getDataAutorizacao());
            }
            if (exame.getLocalExame() == null) {
                getForm().getModel().getObject().setUnidadeAutorizada(SessaoAplicacaoImp.getInstance().getEmpresa());
            } else {
                getForm().getModel().getObject().setUnidadeAutorizada(exame.getLocalExame());
            }
        }
    }


    private void carregarDados(AjaxRequestTarget target) throws ValidacaoException {
        if (target != null) {
            cleanFields(target);
        }

        if (txtNumeroProtocolo == null || txtNumeroProtocolo.getComponentValue() == null || getForm().getModel().getObject().getNumeroProtocolo() == null) {
            btnGerarConfirmacao.setEnabled(false);
            return;
        }
        lstExameProcedimentoDto.clear();

        getForm().getModel().getObject().setExame(carregarExame());
        if (getForm().getModel().getObject().getExame() != null) {
            if (validaStatusExame(target)) {
                return;
            }
            ExameAutorizacao exameAutorizacao = carregarExameAutorizacao(getForm().getModel().getObject().getExame());
            populaDTO(getForm().getModel().getObject().getExame(), exameAutorizacao, target);
            dropDownProfissional.setEnabled(true);
            target.add(dropDownProfissional);

            List<ExameRequisicao> exameRequisicaoList = carregarExameRequisicao(getForm().getModel().getObject().getExame());

            if (CollectionUtils.isNotNullEmpty(exameRequisicaoList)) {
                populaListaExames(exameRequisicaoList);
                btnGerarConfirmacao.setEnabled(true);
            } else {
                btnGerarConfirmacao.setEnabled(false);
            }
        } else {
            MessageUtil.warn(target, this, "Não existem exames a serem confirmados para este protocolo.");
            txtNumeroProtocolo.limpar(target);
        }
        if (!isAtendimento()){
            btnAnexar.setEnabled(false);
            target.add(btnAnexar);
        }
        updateFields(target);
    }

    private boolean isAtendimento() {
        try {
            return getForm().getModel().getObject().getExame().getAtendimento() != null;
        }catch (NullPointerException e) {
            return false;
        }
    }

    private boolean validaStatusExame(AjaxRequestTarget target) {
        if (getForm().getModel().getObject().getExame().getDataConfirmacao() != null) {
            MessageUtil.warn(target, this, "O exame já foi confirmado pelo prestador.");
            txtNumeroProtocolo.limpar(target);
            return true;
        }
        if (Exame.STATUS_CANCELADO.equals(getForm().getModel().getObject().getExame().getStatus())) {
            MessageUtil.warn(target, this, "Este exame foi cancelado.");
            txtNumeroProtocolo.limpar(target);
            return true;
        }
        return false;
    }

    private void cleanFields(AjaxRequestTarget target) {
        updateNotification(target);
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        autoCompleteConsultaProfissional.limpar(target);
        autoCompleteConsultaEmpresa.limpar(target);
        dchDataSolicitacao.limpar(target);
        lstExameProcedimentoDto = new ArrayList<ExameProcedimentoDTO>();
        target.add(tableExame);
    }

    private void populaListaExames(List<ExameRequisicao> exameRequisicaoList) {
        lstExameProcedimentoDto.clear();

        for (ExameRequisicao exameRequisicao : exameRequisicaoList) {
            ExameProcedimentoDTO procedimentoDTO = new ExameProcedimentoDTO();
            procedimentoDTO.setExameRequisicao(exameRequisicao);
            procedimentoDTO.setExameProcedimento(exameRequisicao.getExameProcedimento());
            procedimentoDTO.setQuantidade(exameRequisicao.getQuantidade());
            procedimentoDTO.setValor(new ExtraiValorExame().getValor(exameRequisicao, getForm().getModel().getObject().getUnidadeAutorizada()));
            procedimentoDTO.setStatus(ExameProcedimentoDTO.Status.PENDENTE.value());

            lstExameProcedimentoDto.add(procedimentoDTO);
        }
    }

    private void updateFields(AjaxRequestTarget target) {
        if (target != null) {
            target.add(txtNumeroProtocolo);
            target.add(autoCompleteConsultaUsuarioCadsus);
            target.add(autoCompleteConsultaProfissional);
            target.add(autoCompleteConsultaEmpresa);
            target.add(dchDataSolicitacao);
            target.add(tableExame);
            target.add(btnGerarConfirmacao);
        }
    }

    private List<ExameRequisicao> carregarExameRequisicao(Exame exame) {
        return LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addProperties(new HQLProperties(ExameProcedimento.class, ExameRequisicao.PROP_EXAME_PROCEDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoExame.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME)).getProperties())
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_EXAME, exame))
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_STATUS, QueryCustomParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()))
                .start().getList();
    }

    private void populaDTO(Exame exame, ExameAutorizacao exameAutorizacao, AjaxRequestTarget target) throws ValidacaoException {
        getForm().getModel().getObject().setUsuarioCadsus(exame.getUsuarioCadsus());
        getForm().getModel().getObject().setProfissionalSolicitante(exame.getProfissional());
        if (exameAutorizacao != null) {
            getForm().getModel().getObject().setDataAutorizacao(exameAutorizacao.getDataCadastro());
            getForm().getModel().getObject().setUnidadeAutorizada(exameAutorizacao.getExamePrestadorCompetencia().getEmpresa());
        } else {
            getForm().getModel().getObject().setDataAutorizacao(exame.getDataAutorizacao());
            getForm().getModel().getObject().setUnidadeAutorizada(exame.getLocalExame());
        }
        populaDropDownProfissional(target);
    }

    private ExameAutorizacao carregarExameAutorizacao(Exame exame) {
        return LoadManager.getInstance(ExameAutorizacao.class)
                .addProperties(new HQLProperties(ExameAutorizacao.class).getProperties())
                .addProperties(new HQLProperties(ExamePrestadorCompetencia.class, ExameAutorizacao.PROP_EXAME_PRESTADOR_COMPETENCIA).getProperties())
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(ExameAutorizacao.PROP_EXAME_PRESTADOR_COMPETENCIA, ExamePrestadorCompetencia.PROP_EMPRESA)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameAutorizacao.PROP_EXAME, Exame.PROP_NUMERO_PROTOCOLO_AUTORIZACAO), getForm().getModel().getObject().getNumeroProtocolo()))
                .start().getVO();
    }

    private Exame carregarExame() {
        LoadManager loadManager = LoadManager.getInstance(Exame.class)
                .addProperties(new HQLProperties(Exame.class).getProperties())
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NACIONALIDADE))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_FLAG_ESTRANGEIRO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO))
                .addParameter(new QueryCustomParameter(Exame.PROP_NUMERO_PROTOCOLO_AUTORIZACAO, getForm().getModel().getObject().getNumeroProtocolo()));
        RecepcaoPage recepcaoPage = (RecepcaoPage) getPage();
        if (recepcaoPage != null && !recepcaoPage.isActionPermitted(Permissions.NAO_VALIDAR_UNIDADE_CONFIRMACAO_EXAMES)) {
            loadManager.addParameter(new QueryCustomParameter(Exame.PROP_LOCAL_EXAME, SessaoAplicacaoImp.getInstance().getEmpresa()));
        }
        return loadManager.start().getVO();
    }

    private void confirmar(AjaxRequestTarget target) {
        WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), getMessage()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if(habilitarDataDaConfirmacao) {
                    validarDataConfirmacao();
                }
                validarLaudoAnexado(uploads);
                salvar(target);
                if (!"".equals(codAutorizacaoApac)) {
                    MessageUtil.warn(target, this, BundleManager.getString("codigoAutorizacaoApac") + " " + codAutorizacaoApac);
                }
                tableExame.populate(target);
                target.add(btnGerarConfirmacao);
            }
        });
        dlgConfirmacao.show(target);
    }

    private void validarDataConfirmacao() throws ValidacaoException {
        Date dataConfirmacao = getForm().getModel().getObject().getDataConfirmacao();
        Date competenciaMenorTresMeses = Data.getDataParaPrimeiroDiaMes(Data.removeMeses(dataAtual, 4));

        if(dataConfirmacao.after(dataAtual)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_menor_igual_atual"));
        }

        if(DataUtil.zerarHora(competenciaMenorTresMeses).after(DataUtil.zerarHora(dataConfirmacao))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_confirmacao_deve_estar_entre_tres_meses",
                    DataUtil.getFormatarDiaMesAno(competenciaMenorTresMeses), DataUtil.getFormatarDiaMesAno(dataAtual)));
        }
    }

    private void initDlgInformarClassificacaoServico(AjaxRequestTarget target) {
        if (dlgInformarServicoClassificacao == null) {
            WindowUtil.addModal(target, this, dlgInformarServicoClassificacao = new DlgInformarServicoClassificacao(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, ProcedimentoServicoClassificacao procedimentoServicoClassificacao, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException {
                    procedimentoCompetenciaDTO.getExameProcedimentoDTO().setProcedimentoServicoClassificacao(procedimentoServicoClassificacao);
                    exameProcedimentoDTOsConfirmados.remove(procedimentoCompetenciaDTO.getExameProcedimentoDTO());
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    validarCidProcedimentosConfirmados(target);
                }
            });
        }
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(lstExameProcedimentoDto)) {

            getForm().getModel().getObject().setExameProcedimentoDTOList(lstExameProcedimentoDto);
            codAutorizacaoApac = BOFactoryWicket.getBO(AtendimentoFacade.class).confirmarExamePrestador(getForm().getModel().getObject(), agendaGradeAtendimentoHorarioDTO);

            if (agendaGradeAtendimentoHorarioDTO != null) {
                getRecepcaoController().changePanel(target, new ConfirmacaoPresencaoPanel(getRecepcaoController().panelId(), dtoParamFilter));
            } else {
                ConfirmacaoExamesPanel.this.setResponsePage(new RecepcaoPage());
            }
        }
    }

    private ProcedimentoCompetenciaDTO createProcedimentoCompetenciaDTO(ExameProcedimentoDTO exameProcedimentoDTO) throws DAOException, ValidacaoException {
        ProcedimentoCompetenciaDTO dto = new ProcedimentoCompetenciaDTO();
        dto.setExameProcedimentoDTO(exameProcedimentoDTO);
        dto.setProcedimentoCompetencia(AtendimentoHelper.validaProcedimentoCompetencia(exameProcedimentoDTO.getExameProcedimento().getProcedimento()));
        return dto;
    }

    private void validarCidProcedimentosConfirmados(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validaCargaHoraria();

        if (CollectionUtils.isNotNullEmpty(exameProcedimentoDTOsConfirmados)) {
            removeCancelados(exameProcedimentoDTOsConfirmados);
            viewDlgInformarCidServicoClassificacao(target, exameProcedimentoDTOsConfirmados.get(0));
        } else {
            for (ExameProcedimentoDTO exameProcedimentoDTO : lstExameProcedimentoDto) {
                for (ExameProcedimentoDTO exameProcedimentoDTOConfirmado : exameProcedimentoConfirmadoAux) {
                    if (exameProcedimentoDTO.getExameProcedimento().getCodigo().equals(exameProcedimentoDTOConfirmado.getExameProcedimento().getCodigo())) {
                        exameProcedimentoDTO.setCid(exameProcedimentoDTOConfirmado.getCid() != null ? exameProcedimentoDTOConfirmado.getCid() : null);
                        exameProcedimentoDTO.setProcedimentoServicoClassificacao(exameProcedimentoDTOConfirmado.getProcedimentoServicoClassificacao() != null ? exameProcedimentoDTOConfirmado.getProcedimentoServicoClassificacao() : null);
                    }
                }
            }

            confirmar(target);
        }
    }

    private void removeCancelados(List<ExameProcedimentoDTO> exameProcedimentoDTOsConfirmados) {
        Iterator<ExameProcedimentoDTO> iterator = exameProcedimentoDTOsConfirmados.iterator();
        while (iterator.hasNext()) {
            ExameProcedimentoDTO exame = iterator.next();
            if (exame.getStatus() == ExameProcedimentoDTO.Status.CANCELADO.value()) {
                iterator.remove();
            }
        }
    }

    private void validarLaudoAnexado(FileUpload upload) throws  ValidacaoException{
        boolean exijeLaudo = false;
        try {
            exijeLaudo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ExigeAnexarLaudo").equals(RepositoryComponentDefault.SIM);
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        if (!exijeLaudo) return;

        if (upload == null && isAtendimento()){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nescessario_anexar_laudo"));
        }

    }

    private List<EmpresaServicoClassificacao> getServicoClassificacaoDaEmpresa(ProcedimentoCompetencia procedimentoCompetencia) {
        EmpresaServicoClassificacao proxy = on(EmpresaServicoClassificacao.class);
        Empresa empresa = getForm().getModelObject().getUnidadeAutorizada();
        return LoadManager.getInstance(EmpresaServicoClassificacao.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getProcedimentoServicoClassificacao().getId().getCodigo()))
                .addProperty(path(proxy.getProcedimentoServicoClassificacao().getDescricao()))
                .addProperty(path(proxy.getProcedimentoServicoClassificacao().getId().getProcedimentoServicoCadastro().getCodigo()))
                .addProperty(path(proxy.getProcedimentoServicoClassificacao().getId().getProcedimentoServicoCadastro().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEmpresa()), empresa))
                .addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        HQLHelper exists = hql.getNewInstanceSubQuery();
                        exists.addToSelect("1");
                        exists.addToFrom("ProcedimentoServico ps");
                        exists.addToWhereWhithAnd("ps.id.procedimentoCompetencia = ", procedimentoCompetencia);
                        exists.addToWhereWhithAnd("ps.id.procedimentoServicoClassificacao = " + alias + ".id.procedimentoServicoClassificacao");
                        hql.addToWhereWhithAnd("EXISTS(" + exists.getQuery() + ")");
                    }
                }).start().getList();
    }

    private void validarLaudoAnexado() throws ValidacaoException{

        if (fileUploadField.getFileUpload() != null && uploads == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nescessario_anexar_laudo"));
        }
    }

    private void viewDlgInformarCidServicoClassificacao(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO) throws ValidacaoException, DAOException {
        initDlgInformarCid(target);
        initDlgInformarClassificacaoServico(target);
        initDlgInformarCidClassificacaoServico(target);

        procedimentoCompetenciaDTO = createProcedimentoCompetenciaDTO(exameProcedimentoDTO);

        boolean existeProcedimentoCid = LoadManager.getInstance(ProcedimentoCid.class)
                .addParameter(new QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetenciaDTO.getProcedimentoCompetencia())))
                .exists();

        List<EmpresaServicoClassificacao> list = getServicoClassificacaoDaEmpresa(procedimentoCompetenciaDTO.getProcedimentoCompetencia());

        if (existeProcedimentoCid && (!CollectionUtils.isNotNullEmpty(list))) {
            dlgInformarCid.show(target, procedimentoCompetenciaDTO);
            return;
        } else if (!existeProcedimentoCid && CollectionUtils.isNotNullEmpty(list) && list.size() > 1) {
            dlgInformarServicoClassificacao.show(target, procedimentoCompetenciaDTO);
            return;
        } else if (!existeProcedimentoCid && CollectionUtils.isNotNullEmpty(list)  && list.size() == 1) {
            procedimentoCompetenciaDTO.getExameProcedimentoDTO().setProcedimentoServicoClassificacao(list.get(0).getProcedimentoServicoClassificacao());
        } else if (existeProcedimentoCid && CollectionUtils.isNotNullEmpty(list)) {
            dlgInformarCidServicoClassificacao.show(target, procedimentoCompetenciaDTO);
            return;
        }

        exameProcedimentoDTOsConfirmados.remove(exameProcedimentoDTO);
        validarCidProcedimentosConfirmados(target);
    }

    private void initDlgInformarCidClassificacaoServico(AjaxRequestTarget target) {
        if (dlgInformarCidServicoClassificacao == null) {
            WindowUtil.addModal(target, this, dlgInformarCidServicoClassificacao = new DlgInformarCidServicoClassificacao(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Cid cid, ProcedimentoServicoClassificacao procedimentoServicoClassificacao, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException {
                    procedimentoCompetenciaDTO.getExameProcedimentoDTO().setCid(cid);
                    procedimentoCompetenciaDTO.getExameProcedimentoDTO().setProcedimentoServicoClassificacao(procedimentoServicoClassificacao);
                    exameProcedimentoDTOsConfirmados.remove(procedimentoCompetenciaDTO.getExameProcedimentoDTO());
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    validarCidProcedimentosConfirmados(target);
                }
            });
        }
    }

    private void initDlgInformarCid(AjaxRequestTarget target) {
        if (dlgInformarCid == null) {
            WindowUtil.addModal(target, this, dlgInformarCid = new DlgInformarCid(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Cid cid, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException {
                    procedimentoCompetenciaDTO.getExameProcedimentoDTO().setCid(cid);
                    exameProcedimentoDTOsConfirmados.remove(procedimentoCompetenciaDTO.getExameProcedimentoDTO());
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    validarCidProcedimentosConfirmados(target);
                }
            });
        }
    }

    private String getMessage() {
        String message;
        if (Lambda.exists(lstExameProcedimentoDto, Lambda.having(on(ExameProcedimentoDTO.class).getStatus(), Matchers.not(ExameProcedimentoDTO.Status.CANCELADO.value())))) {
            message = "Deseja mesmo confirmar os exames?";
        } else {
            message = "Deseja mesmo cancelar os exames?";
        }
        return message;
    }

    private void trocarStatusItens(AjaxRequestTarget target, Long status) {
        List<ExameProcedimentoDTO> selectedObjects = tableExame.getSelectedObjects();
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(selectedObjects)) {
            Lambda.forEach(selectedObjects).setStatus(status);
        }
        tableExame.populate(target);
        target.add(btnGerarConfirmacao);
    }

    private void updateNotification(AjaxRequestTarget target) {
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(ConfirmacaoExamesPanel.this);
        if (findNotificationPanel != null) {
            getSession().getFeedbackMessages().clear();
            if (target != null) {
                findNotificationPanel.updateNotificationPanel(target);
            }
        }
    }

    private DropDown getDropDownProfissional(String id) {
        if (dropDownProfissional == null) {
            dropDownProfissional = new DropDown(id);
            dropDownProfissional.addAjaxUpdateValue();
            dropDownProfissional.setOutputMarkupId(true);
            dropDownProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                }
            });
        }
        return dropDownProfissional;
    }

    public void populaDropDownProfissional(AjaxRequestTarget target) {
        List<ProfissionalCargaHoraria> profissionalCargaHorariaList = LoadManager.getInstance(ProfissionalCargaHoraria.class)
                .addProperties(new HQLProperties(ProfissionalCargaHoraria.class).getProperties())
                .addParameter(new QueryCustomParameter(ProfissionalCargaHoraria.PROP_EMPRESA, SessaoAplicacaoImp.getInstance().getEmpresa()))
                .addProperties(new HQLProperties(Profissional.class, ProfissionalCargaHoraria.PROP_PROFISSIONAL).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ProfissionalCargaHoraria.PROP_PROFISSIONAL, Profissional.PROP_NOME), QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(profissionalCargaHorariaList)) {
            if (profissionalCargaHorariaList.size() == 1) {
                Profissional profissional = profissionalCargaHorariaList.get(0).getProfissional();
                dropDownProfissional.setComponentValue(profissional);
                getForm().getModel().getObject().setProfissionalExecutante(profissional);
                dropDownProfissional.addChoice(profissional, profissional.getNome());
            } else {
                dropDownProfissional.addChoice(null, "");
                List<Profissional> profissionalList = Lambda.extract(profissionalCargaHorariaList, Lambda.on(ProfissionalCargaHoraria.class).getProfissional());
                for (Profissional profissional : profissionalList) {
                    dropDownProfissional.addChoice(profissional, profissional.getNome());
                }
            }
        } else {
            if (target != null) {
                MessageUtil.error(target, this, "Não existem profissionais com carga horária definidas para esta unidade.");
            }
        }
    }
}
