package br.com.celk.view.basico.microrregiao.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.microrregiao.autocomplete.restricaocontainer.RestricaoContainerMicrorregiao;
import br.com.ksisolucoes.bo.basico.dto.QueryConsultaMicrorregiaoDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Microrregiao;
import br.com.ksisolucoes.vo.basico.RegionalSaude;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaMicrorregiao extends AutoCompleteConsulta<Microrregiao> {

    private Long cdRegionalSaude;

    public AutoCompleteConsultaMicrorregiao(String id) {
        super(id);
    }

    public AutoCompleteConsultaMicrorregiao(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaMicrorregiao(String id, IModel<Microrregiao> model) {
        super(id, model);
    }

    public AutoCompleteConsultaMicrorregiao(String id, IModel<Microrregiao> model, boolean required, Long cdRegionalSaude) {
        super(id, model, required);
        this.cdRegionalSaude = cdRegionalSaude;
    }

    public AutoCompleteConsultaMicrorregiao(String id, IModel<Microrregiao> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(Microrregiao.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(Microrregiao.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("regionalSaude"), VOUtils.montarPath(Microrregiao.PROP_REGIONAL_SAUDE, RegionalSaude.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Microrregiao.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerMicrorregiao(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Microrregiao, QueryConsultaMicrorregiaoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaMicrorregiaoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarMicrorregiao(dataPaging);
                    }

                    @Override
                    public QueryConsultaMicrorregiaoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaMicrorregiaoDTOParam param = new QueryConsultaMicrorregiaoDTOParam();
                        param.setKeyword(searchCriteria);

                        if (getCdRegionalSaude() != null) {
                            param.setCdRegionalSaude(getCdRegionalSaude());
                        }

                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaMicrorregiaoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Microrregiao.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return Microrregiao.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("microRegiao");
    }

    public Long getCdRegionalSaude() {
        return cdRegionalSaude;
    }

    public void setCdRegionalSaude(Long cdRegionalSaude) {
        this.cdRegionalSaude = cdRegionalSaude;
    }
}
