package br.com.celk.view.comunicacao.grupomensagem;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagem;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import static ch.lambdaj.Lambda.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaGrupoMensagemPage extends ConsultaPage<GrupoMensagem, List<BuilderQueryCustom.QueryParameter>>{

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.add(new InputField("descricao", new PropertyModel(this, "descricao")));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        GrupoMensagem on = on(GrupoMensagem.class);
        
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("codigo"), on.getCodigo()));
        columns.add(createColumn(bundle("descricao"), on.getDescricao()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<GrupoMensagem>() {

            @Override
            public void customizeColumn(GrupoMensagem rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<GrupoMensagem>() {

                    @Override
                    public void action(AjaxRequestTarget target, GrupoMensagem modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroGrupoMensagemPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<GrupoMensagem>() {

                    @Override
                    public void action(AjaxRequestTarget target, GrupoMensagem modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ComunicacaoFacade.class).removerGrupoMensagem(modelObject);
                        getPageableTable().update(target);
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){

            @Override
            public Class getClassConsulta() {
                return GrupoMensagem.class;
            }
        });
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(GrupoMensagem.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroGrupoMensagemPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaGruposMensagem");
    }

}
