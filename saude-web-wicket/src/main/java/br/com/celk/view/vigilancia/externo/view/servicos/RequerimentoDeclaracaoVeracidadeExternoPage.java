package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.vigilanciaendereco.RequiredPnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDeclaracaoVeracidade;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by maicon on 31/05/16.
 */
public class RequerimentoDeclaracaoVeracidadeExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoDeclaracaoVeracidadeDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoDeclaracaoVeracidade requerimentoDeclaracaoVeracidade;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private InputField<String> txtCnpjCpf;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtDescricaoAtividadeEstabelecimento;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf");
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;

    private WebMarkupContainer containerPJ;
    private WebMarkupContainer containerPF;
    private InputField txtNome;
    private InputField txtTelefone;
    private RequiredPnlVigilanciaEndereco pnlVigilanciaEndereco;
    private boolean enabled;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private DropDown<Long> dropDownTipoPessoa;
    private Class classReturn;

    public RequerimentoDeclaracaoVeracidadeExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoDeclaracaoVeracidadeExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoDeclaracaoVeracidade(requerimentoVigilancia);
        if(RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())){
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoDeclaracaoVeracidadeDTO proxy = on(RequerimentoDeclaracaoVeracidadeDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoDeclaracaoVeracidadeDTO()));
        if (requerimentoDeclaracaoVeracidade != null) {
            form.getModel().getObject().setRequerimentoDeclaracaoVeracidade(requerimentoDeclaracaoVeracidade);
        } else {
            form.getModel().getObject().setRequerimentoDeclaracaoVeracidade(new RequerimentoDeclaracaoVeracidade());
            form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }
        Long codigoRequerimentoDeclaracaoVeracidade = form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getCodigo();

        form.add(autoCompleteConsultaEstabelecimento = (AutoCompleteConsultaEstabelecimento) new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoDeclaracaoVeracidade().getEstabelecimento())).setEnabled(enabled));
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setOutputMarkupId(true);
        autoCompleteConsultaEstabelecimento.setEnabled(enabled && codigoRequerimentoDeclaracaoVeracidade == null);
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        try {
            autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        form.add(new DisabledInputField<String>(path(proxy.getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getProtocoloFormatado())));
        form.add(txtNome = (InputField) new InputField(path(proxy.getRequerimentoDeclaracaoVeracidade().getNome())).setLabel(new Model(bundle("nome"))).setEnabled(enabled && codigoRequerimentoDeclaracaoVeracidade == null));

        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        form.add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));
        pnlDadosComumRequerimentoVigilancia.getContainerSetorVigilancia().setVisible(false);

        {
            containerPJ = new WebMarkupContainer("containerPJ");
            containerPJ.setVisible(codigoRequerimentoDeclaracaoVeracidade != null && form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento() != null);
            containerPJ.setOutputMarkupId(true);
            containerPJ.setOutputMarkupPlaceholderTag(true);
            containerPJ.add(txtFantasia = (DisabledInputField<String>) new DisabledInputField<String>(path(proxy.getRequerimentoDeclaracaoVeracidade().getEstabelecimento().getFantasia())));
            containerPJ.add(txtDescricaoAtividadeEstabelecimento = new DisabledInputField<String>(path(proxy.getRequerimentoDeclaracaoVeracidade().getEstabelecimento().getAtividadeEstabelecimento().getDescricao())));
            containerPJ.setEnabled(enabled && codigoRequerimentoDeclaracaoVeracidade == null);
            form.add(containerPJ);
        }
        {
            containerPF = new WebMarkupContainer("containerPF");
            containerPF.setOutputMarkupId(true);
            containerPF.setOutputMarkupPlaceholderTag(true);
            containerPF.setEnabled(enabled && codigoRequerimentoDeclaracaoVeracidade == null);

            containerPF.add(pnlVigilanciaEndereco = (RequiredPnlVigilanciaEndereco) new RequiredPnlVigilanciaEndereco(path(proxy.getRequerimentoDeclaracaoVeracidade().getVigilanciaEndereco())).setLabel(new Model(bundle("endereco"))));
            containerPF.add(dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getTipoPessoa()), RequerimentoVigilancia.TipoPessoa.values()));
            dropDownTipoPessoa.setEnabled(codigoRequerimentoDeclaracaoVeracidade != null && form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento() == null);
            dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (getFormComponent().getModelObject() != null) {
                        if (Estabelecimento.TipoPessoa.FISICA.value().equals(getFormComponent().getModelObject())) {
                            target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                            if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                                txtCnpjCpf.remove(attributeModifierCnpj);
                            }
                            if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                                txtCnpjCpf.limpar(target);
                                txtCnpjCpf.add(attributeModifierCpf);
                            }
                        } else {
                            target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                            if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                                txtCnpjCpf.remove(attributeModifierCpf);
                            }
                            if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                                txtCnpjCpf.limpar(target);
                                txtCnpjCpf.add(attributeModifierCnpj);
                            }
                        }
                        target.add(txtCnpjCpf);
                    }
                    ;
                }
            });
            containerPF.add(txtTelefone = (InputField) new TelefoneField(path(proxy.getRequerimentoDeclaracaoVeracidade().getTelefone())).setEnabled(enabled));
            containerPF.add(txtCnpjCpf = new InputField<>(path(proxy.getRequerimentoDeclaracaoVeracidade().getCnpjCpf())));

            txtCnpjCpf.setEnabled(codigoRequerimentoDeclaracaoVeracidade != null && form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento() == null);
            form.add(containerPF);
        }

        form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia(), enabled));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        form.add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosEstabelecimento(target, object);
            }
        });
        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosEstabelecimento(target, null);
            }
        });

        txtNome.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (txtNome.getComponentValue() != null) {
                    autoCompleteConsultaEstabelecimento.setEnabled(false);
                    initMaskCpf(target);
                    txtCnpjCpf.setEnabled(true);
                    dropDownTipoPessoa.setEnabled(true);
                } else {
                    autoCompleteConsultaEstabelecimento.setEnabled(true);
                }
                target.add(txtCnpjCpf);
                target.add(dropDownTipoPessoa);
                target.add(autoCompleteConsultaEstabelecimento);
            }
        });


        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        add(form);

        if(codigoRequerimentoDeclaracaoVeracidade != null){
            this.txtCnpjCpf.add(this.attributeModifierCpf);
            if (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getTipoPessoa())) {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.remove(attributeModifierCpf);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.add(attributeModifierCnpj);
                }
            } else {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.remove(attributeModifierCnpj);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.add(attributeModifierCpf);
                }
            }
            if(form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getEstabelecimento() != null){
                atualizarDadosEstabelecimento(null, form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getEstabelecimento());
            }
        }
    }

    private void initMaskCpf(AjaxRequestTarget target) {
        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
            txtCnpjCpf.remove(attributeModifierCnpj);
        }
        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
            txtCnpjCpf.limpar(target);
            txtCnpjCpf.add(attributeModifierCpf);
        }
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        RequerimentoDeclaracaoVeracidadeDTO dto = form.getModel().getObject();
        if (dto.getRequerimentoDeclaracaoVeracidade().getEstabelecimento() == null && dto.getRequerimentoDeclaracaoVeracidade().getNome() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabOuNome"));
        }
        if(form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().getRgSolicitante() == null){
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
        dto.getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        dto.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        dto.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
//        dto.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        atualizarDadosComuns();
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoDeclaracaoVeracidade(form.getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);
                param.setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if(estabelecimento != null){
            if (form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento().getCnpjCpf() == null || form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento().getCnpjCpf().isEmpty()) {
                Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                        .start().getVO();

                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setTipoPessoa(estabPrincipal.getTipoPessoa());
            } else {
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setCnpjCpf(estabelecimento.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setTipoPessoa(estabelecimento.getTipoPessoa());
            }
            if(target != null){
                pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().limpar(target);
            }
            if(estabelecimento.getVigilanciaEndereco() != null && estabelecimento.getVigilanciaEndereco().getCodigo() != null){
                VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getVigilanciaEndereco().getCodigo()))
                        .start().getVO();

                pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().setComponentValue(ve);
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setTelefone(estabelecimento.getTelefone());
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setVigilanciaEndereco(ve);
            }

            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();
            if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
            }
            txtNome.setEnabled(false);
            containerPF.setEnabled(false);
            containerPJ.setVisible(true);
//            form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setSetorVigilancia(null);
            if(target != null){
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().limpar(target);
            }
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setModelObject(null);
        } else {
            containerPF.setEnabled(true);
            containerPJ.setVisible(false);
            txtNome.setEnabled(true);
            if(target != null){
                ComponentUtils.limparContainer(containerPF, target);
            }
            form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().setVigilanciaEndereco(null);
            pnlVigilanciaEndereco.setEnabled(true);
            if(target != null){
                pnlVigilanciaEndereco.limpar(target);
            }
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(true);
            if(target != null){
                initMaskCpf(target);
            }
        }

        if(target != null){
            target.add(pnlVigilanciaEndereco.getBtnCadastrarEndereco());
            target.add(containerPJ);
            target.add(containerPF);
            target.add(txtCnpjCpf);
            target.add(txtFantasia);
            target.add(txtDescricaoAtividadeEstabelecimento);
            target.add(txtNome);
            target.add(txtTelefone);
            target.add(pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia());
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void carregarRequerimentoDeclaracaoVeracidade(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoDeclaracaoVeracidade = LoadManager.getInstance(RequerimentoDeclaracaoVeracidade.class)
                    .addProperties(new HQLProperties(RequerimentoDeclaracaoVeracidade.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoDeclaracaoVeracidade.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoDeclaracaoVeracidade.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoDeclaracaoVeracidade.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoDeclaracaoVeracidade.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoDeclaracaoVeracidade.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoDeclaracaoVeracidade.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoDeclaracaoVeracidade.setRequerimentoVigilancia(requerimentoVigilancia);
            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv){
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for(RequerimentoVigilanciaAnexo rva : list){
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("declaracaoCartorio");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    @Override
    public Permissions getAction() {
        return Permissions.DECLARACAO_CARTORIO;
    }
    
    private void atualizarDadosComuns(){
        form.getModel().getObject().getRequerimentoDeclaracaoVeracidade().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        form.getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        form.getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}
