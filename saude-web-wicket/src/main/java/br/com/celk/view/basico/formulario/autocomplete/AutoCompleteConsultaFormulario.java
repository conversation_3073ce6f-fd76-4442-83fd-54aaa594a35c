package br.com.celk.view.basico.formulario.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.formulario.autocomplete.restricaocontainer.RestricaoContainerFormulario;
import br.com.ksisolucoes.bo.formulario.dto.QueryConsultaFormularioDTOParam;
import br.com.ksisolucoes.bo.formulario.interfaces.facade.FormularioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.formulario.Formulario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaFormulario extends AutoCompleteConsulta<Formulario> { 

    public AutoCompleteConsultaFormulario(String id) {
        super(id);
    }

    public AutoCompleteConsultaFormulario(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaFormulario(String id, IModel<Formulario> model) {
        super(id, model);
    }

    public AutoCompleteConsultaFormulario(String id, IModel<Formulario> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(TipoExame.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(Formulario.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Formulario.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerFormulario(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Formulario, QueryConsultaFormularioDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaFormularioDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(FormularioFacade.class).consultarFormulario(dataPaging);
                    }

                    @Override
                    public QueryConsultaFormularioDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaFormularioDTOParam param = new QueryConsultaFormularioDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaFormularioDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Formulario.PROP_DESCRICAO), true);
                    }
                };
            }
            
            @Override
            public Class getReferenceClass() {
                return Formulario.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("formularios");
    }
}
