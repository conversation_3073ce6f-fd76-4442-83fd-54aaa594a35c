package br.com.celk.view.basico.unidade;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.customize.CustomizeConsultaEmpresa;
import br.com.celk.view.basico.microrregiao.autocomplete.AutoCompleteConsultaMicrorregiao;
import br.com.celk.view.basico.regionalsaude.autocomplete.AutoCompleteConsultaRegionalSaude;
import br.com.celk.view.basico.unidade.dropDownTipoEstabelecimento.DropDownTipoEstabelecimentoFiltroConsulta;
import br.com.celk.view.consorcio.empresaterceiroresponsavel.CadastroEmpresaTerceiroResponsavelPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Microrregiao;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.basico.RegionalSaude;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaEmpresaPage extends ConsultaPage<Empresa, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private String referencia;
    private String cnes;
    private Long tipoEstabelecimento;
    private RegionalSaude regionalSaude;
    private Microrregiao microrregiao;
    private DropDown<Long> dropDownTipoEstabelicimento;
    private DropDown<String> dropDownTipoPessoa;
    private InputField txtCnpjCpf;
    private InputField<String> txtDescricao;
    private AutoCompleteConsultaRegionalSaude autoCompleteConsultaRegionalSaude;
    private AutoCompleteConsultaMicrorregiao autoCompleteConsultaMicrorregiao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(txtDescricao = new InputField<String>("descricao"));
        form.add(new UpperField("referencia"));
        form.add(new InputField("cnes"));
        form.add(getDropDownTipoEstabelicimento());
        form.add(autoCompleteConsultaRegionalSaude = new AutoCompleteConsultaRegionalSaude("regionalSaude"));
        form.add(autoCompleteConsultaMicrorregiao = new AutoCompleteConsultaMicrorregiao("microrregiao"));

        setExibeExpandir(true);
    }

    public DropDown getDropDownTipoPessoa() {
        if (this.dropDownTipoPessoa == null) {
            this.dropDownTipoPessoa = new DropDown<String>("tipo");
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_JURIDICA, BundleManager.getString("juridica"));
            this.dropDownTipoPessoa.addChoice(Pessoa.PESSOA_FISICA, BundleManager.getString("fisica"));

            this.dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    txtCnpjCpf.limpar(target);
                    if (dropDownTipoPessoa.getModelObject().equals(Pessoa.PESSOA_FISICA)) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                    } else {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                    }
                }
            });
        }
        return dropDownTipoPessoa;
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Empresa.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Empresa.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cnes"), VOUtils.montarPath(Empresa.PROP_CNES)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("telefone"), VOUtils.montarPath(Empresa.PROP_TELEFONE), VOUtils.montarPath(Empresa.PROP_TELEFONE_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoEstabelecimento"), VOUtils.montarPath(Empresa.PROP_TIPO_UNIDADE), VOUtils.montarPath(Empresa.PROP_TIPO_UNIDADE_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("registroSaudeAbv"), VOUtils.montarPath(Empresa.PROP_MICRORREGIAO, Microrregiao.PROP_REGIONAL_SAUDE, RegionalSaude.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("microRegiao"), VOUtils.montarPath(Empresa.PROP_MICRORREGIAO, Microrregiao.PROP_DESCRICAO)));

        return columns;
    }

    private IColumn getCustomColumn() {

        return new MultipleActionCustomColumn<Empresa>() {
            @Override
            public void customizeColumn(Empresa rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEmpresaPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEmpresaPage(modelObject, true));
                    }
                });
                addAction(ActionType.CARACTERIZACAO, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCaracterizacaoUnidadePage(modelObject));
                    }
                });
                addAction(ActionType.CONJUNTO, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConjuntoPage(modelObject));
                    }
                });
                addAction(ActionType.CADASTRAR_EDITAR_RECEITUARIO, rowObject, new IModelAction<Empresa>() {
                    @Override
                    public void action(AjaxRequestTarget target, Empresa modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEmpresaTerceiroResponsavelPage(modelObject, getPageClass()));
                    }
                }).setIcon(Icon.MEMBERS).setTitleBundleKey("terceiro");

            }
        };

    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaEmpresa()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Empresa.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, this.descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_REFERENCIA), BuilderQueryCustom.QueryParameter.IGUAL, this.referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_CNES), BuilderQueryCustom.QueryParameter.ILIKE, this.cnes));
        if (tipoEstabelecimento != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_TIPO_UNIDADE), BuilderQueryCustom.QueryParameter.IGUAL, this.tipoEstabelecimento));
        }

        if (microrregiao != null && regionalSaude != null) {
            parameters.add(
                new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupOr(
                        new QueryCustom.QueryCustomParameter(
                                Empresa.PROP_MICRORREGIAO, this.microrregiao
                        )
                    )
                )
            );

            parameters.add(
                new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupOr(
                        new QueryCustom.QueryCustomParameter(
                                VOUtils.montarPath(Empresa.PROP_MICRORREGIAO, Microrregiao.PROP_REGIONAL_SAUDE), this.regionalSaude
                        )
                    )
                )
            );
        } else if (microrregiao != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(Empresa.PROP_MICRORREGIAO, this.microrregiao));
        } else if (regionalSaude != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_MICRORREGIAO, Microrregiao.PROP_REGIONAL_SAUDE), this.regionalSaude));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEmpresaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaUnidades");
    }

    public DropDown<Long> getDropDownTipoEstabelicimento() {
        this.dropDownTipoEstabelicimento = this.dropDownTipoEstabelicimento != null ? this.dropDownTipoEstabelicimento
                                                                                    : new DropDownTipoEstabelecimentoFiltroConsulta("tipoEstabelecimento").build();
        return this.dropDownTipoEstabelicimento;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
}
