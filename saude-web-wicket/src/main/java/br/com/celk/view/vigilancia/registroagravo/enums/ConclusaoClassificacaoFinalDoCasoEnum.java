package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum ConclusaoClassificacaoFinalDoCasoEnum implements IEnum {
    INFLUENZA(1L, "SRAG por influenza"),
    OUTRO_RESPIRATORIO(2L, "SRAG por outro vírus respiratório"),
    ESPECIFICADO(3L, "SRAG não especificado"),
    COVID_19(4L, "SRAG por covid-19"),
    OUTRO_AGENTE(5L, "SRAG por outro Agente Etiológico, qual? ");

    private Long value;
    private String descricao;

    ConclusaoClassificacaoFinalDoCasoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static ConclusaoClassificacaoFinalDoCasoEnum valueOf(Long value) {
        for (ConclusaoClassificacaoFinalDoCasoEnum v : ConclusaoClassificacaoFinalDoCasoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
