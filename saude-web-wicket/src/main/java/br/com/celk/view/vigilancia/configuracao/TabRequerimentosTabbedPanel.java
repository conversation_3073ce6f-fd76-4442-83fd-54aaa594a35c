package br.com.celk.view.vigilancia.configuracao;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroConfiguracaoVigilanciaDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TabRequerimentosTabbedPanel extends CadastroTabbedPanel<CadastroConfiguracaoVigilanciaDTO> {

    public TabRequerimentosTabbedPanel(String id, CadastroConfiguracaoVigilanciaDTO object, boolean viewOnly, List<ITab> tabs, boolean buttonBackVisible) {
        super(id, object, viewOnly, tabs, buttonBackVisible);
    }

    public TabRequerimentosTabbedPanel(String id, CadastroConfiguracaoVigilanciaDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public TabRequerimentosTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<CadastroConfiguracaoVigilanciaDTO> getReferenceClass() {
        return CadastroConfiguracaoVigilanciaDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }
}
