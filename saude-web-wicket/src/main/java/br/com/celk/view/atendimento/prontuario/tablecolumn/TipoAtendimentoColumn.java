/*
 * Copyright 2013 claudio.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package br.com.celk.view.atendimento.prontuario.tablecolumn;

import br.com.celk.component.css.TextAlign;
import br.com.celk.component.table.column.Column;
import br.com.celk.resources.atendimento.TipoAtendimentoCssGenerator;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTO;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

/**
 * <AUTHOR>
 * Criado em: Sep 20, 2013
 */
public class TipoAtendimentoColumn extends Column<AtendimentoWebDTO>{

    public TipoAtendimentoColumn(String displayModel, String sortProperty, String propertyExpression) {
        super(displayModel, sortProperty, propertyExpression);
    }

    public TipoAtendimentoColumn(String displayModel, String propertyExpression) {
        super(displayModel, propertyExpression);
    }

    @Override
    public void populateItem(Item<ICellPopulator<AtendimentoWebDTO>> item, String componentId, IModel<AtendimentoWebDTO> rowModel) {
        Label label;
        item.add(label = new Label(componentId, getDataModel(rowModel)));
        label.add(new AttributeModifier("class", TextAlign.CENTER.value()+" "+new TipoAtendimentoCssGenerator().resolveClassName(rowModel.getObject().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento())));
    }

}
