package br.com.celk.view.frota.viagem.tipotransporteviagem.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTipoTransporteViagem extends AutoCompleteConsulta<TipoTransporteViagem> {

    public AutoCompleteConsultaTipoTransporteViagem(String id) {
        super(id);
    }

    public AutoCompleteConsultaTipoTransporteViagem(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaTipoTransporteViagem(String id, IModel<TipoTransporteViagem> model) {
        super(id, model);
    }

    public AutoCompleteConsultaTipoTransporteViagem(String id, IModel<TipoTransporteViagem> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return TipoTransporteViagem.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoTransporteViagem.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {
                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), TipoTransporteViagem.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), TipoTransporteViagem.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(TipoTransporteViagem.PROP_CODIGO));
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(TipoTransporteViagem.PROP_DESCRICAO, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return TipoTransporteViagem.class;
                    }
                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(TipoTransporteViagem.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tipoTransporteViagem");
    }
}
