package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaOcupacaoDiariaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaOcupacaoDiariaSetorDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaOcupacaoDiariaPorSetorPage extends BasePage {
    
    private QueryConsultaOcupacaoDiariaDTO consultaOcupacaoDiariadto;
    private List<QueryConsultaOcupacaoDiariaSetorDTO> lstQuartos = new ArrayList<QueryConsultaOcupacaoDiariaSetorDTO>();    
    private Table table;
    
    public ConsultaOcupacaoDiariaPorSetorPage(QueryConsultaOcupacaoDiariaDTO consultaOcupacaoDiariadto){
        this.consultaOcupacaoDiariadto = consultaOcupacaoDiariadto;
        try {
            carregarItens(consultaOcupacaoDiariadto.getEmpresa());
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        init();
    }
    
    private void init(){
        Form form = new Form("form", new CompoundPropertyModel<QueryConsultaOcupacaoDiariaDTO>(consultaOcupacaoDiariadto == null ? consultaOcupacaoDiariadto = new QueryConsultaOcupacaoDiariaDTO() : consultaOcupacaoDiariadto));
        
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        form.add(new Label("setor", consultaOcupacaoDiariadto.getEmpresa().getDescricao()));
        
        table.setScrollY("500px");
        table.populate();
        
        form.add(new VoltarButton("btnVoltar"));
        
        add(form);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns;
        columns = new ArrayList<IColumn>();

        QueryConsultaOcupacaoDiariaSetorDTO proxy = on(QueryConsultaOcupacaoDiariaSetorDTO.class);

        columns.add(createColumn(bundle("quarto"), proxy.getQuartoInternacao().getDescricao()));
        columns.add(createColumn(bundle("leito"), proxy.getLeitoQuarto().getDescricao()));
        columns.add(createColumn(bundle("paciente"), proxy.getAtendimento().getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("idade"), proxy.getAtendimento().getUsuarioCadsus().getDescricaoIdadeSimples()));
        columns.add(createColumn(bundle("dataChegada"), proxy.getAtendimento().getDataChegada()));
        columns.add(createColumn(bundle("tempo"), proxy.getTempoAtendimento()));
        columns.add(createColumn(bundle("convenio"), proxy.getAtendimento().getConvenio().getDescricao()));
        columns.add(createColumn(bundle("tipoAtendimento"), proxy.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstQuartos;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("ocupacaoAtual");
    }

    private void carregarItens(Empresa dto) throws DAOException, ValidacaoException {
        if (dto.getCodigo() != null) {
            lstQuartos = BOFactoryWicket.getBO(HospitalFacade.class).consultaOcupacaoDiariaSetor(dto);
        }
    }
    
}
