package br.com.celk.view.vacina.vacinaaplicacao;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.lote.saida.PnlSaidaLote;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.helper.vacinas.AplicarVacinaHelper;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaMotivoVacina;
import br.com.celk.view.vacina.produtovacina.autocomplete.AutoCompleteConsultaProdutoVacinaFabricante;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaProfissionalIndicador;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.AplicacaoVacinaDTO;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.vacina.*;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.javascript.JScript.toggleFieldset;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class AplicarVacinaForaEsquemaVacinalPage extends BasePage {

    private Form<AplicacaoVacinaDTO> form;
    private Form<VacinaAplicacaoInsumo> formInsumos;
    private AutoCompleteConsultaProdutoVacinaFabricante autoCompleteConsultaProdutoVacinaFabricante;
    private AutoCompleteConsultaTipoVacina autoCompleteConsultaTipoVacina;
    private final UsuarioCadsus usuarioCadsus;
    private final RegistroAgravo registroAgravo;
    private PnlSaidaLote pnlSaidaLote;
    private PnlSaidaLote pnlSaidaLoteInsumo;
    private WebMarkupContainer containerVacinaAplicacaoInsumo;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Table tblVacinaAplicacaoInsumo;
    private InputField txtQuantidade;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissionalAplicacao;
    private AutoCompleteConsultaProfissionalIndicador autoCompleteConsultaProfissionalIndicador;
    private AutoCompleteConsultaMotivoVacina autoCompleteConsultaMotivoVacina;
    private DropDown dropDownGrupoAtendimento;
    private DropDown dropDownLocalAtendimento;
    private DropDown dropDownComunicanteHanseniase;
    private DropDown dropDownGestante;
    private DropDown dropDownPuerpera;
    private DropDown dropDownEstrategia;
    private DropDown dropDownDoses;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private DateChooser dchDataAplicacao;
    private VacinaAplicacaoInsumo vacinaAplicacaoInsumoEdicao;
    private final Long tipoLancamento = VacinaAplicacao.TipoLancamento.REGISTRO.value();
    private UpperField txtLote;
    private WebMarkupContainer containerHistoricoLote;
    private WebMarkupContainer containerLote;
    private DropDown dropDownLocalAplicacao;
    private DropDown<ViaAdministracao> dropDownViaAdministracao;

    private RadioButtonGroup radioGroupTipoLancamento;

    public AplicarVacinaForaEsquemaVacinalPage(UsuarioCadsus usuarioCadsus, RegistroAgravo registroAgravo) {
        this.usuarioCadsus = usuarioCadsus;
        this.registroAgravo = registroAgravo;
        init();
        initAplicacao();
        carregarUsuarioCadsusDado();
        carregarUltimaAplicacao();
    }

    private void init() {
        AplicacaoVacinaDTO proxy = on(AplicacaoVacinaDTO.class);

        getForm().add(radioGroupTipoLancamento = new RadioButtonGroup("tipoLancamento", new PropertyModel<Long>(this, "tipoLancamento")));
        radioGroupTipoLancamento.add(new AjaxRadio("registro", new Model(VacinaAplicacao.TipoLancamento.REGISTRO.value())));
        radioGroupTipoLancamento.add(new AjaxRadio("historico", new Model(VacinaAplicacao.TipoLancamento.HISTORICO.value())));

        radioGroupTipoLancamento.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                controlDropDownDoses(target);
            }
        });

        getForm().add(new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getDescricaoSocialFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getDescricaoIdadeAnoMesDia())));
        getForm().add(dropDownGrupoAtendimento = (DropDown) DropDownUtil.getDropDownGrupoAtendimentoVacinacao(path(proxy.getVacinaAplicacao().getGrupoAtendimento()),false).setLabel(new Model(bundle("grupoDeAtendimento"))));
        getForm().add(dropDownComunicanteHanseniase = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVacinaAplicacao().getComunicanteHanseniase())));
        getForm().add(dropDownGestante = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVacinaAplicacao().getFlagGestante())));
        getForm().add(dropDownPuerpera = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVacinaAplicacao().getFlagPuerpera())));

        getForm().add(dropDownEstrategia = getDropDownEstrategia(path(proxy.getVacinaAplicacao().getEstrategia())));
        getForm().add(dropDownLocalAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaAplicacao().getLocalAtendimento()), VacinaAplicacao.LocalAtendimento.values(), false, false, true));
        dropDownLocalAtendimento.setComponentValue(VacinaAplicacao.LocalAtendimento.UBS.value());
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getVacinaAplicacao().getTurno()), VacinaAplicacao.Turno.values(), false, false, true));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getVacinaAplicacao().getViajante())));
        getForm().add(new DisabledInputField(path(proxy.getVacinaAplicacao().getTipoVacina().getDescricao())));
        getForm().add(autoCompleteConsultaTipoVacina = (AutoCompleteConsultaTipoVacina) new AutoCompleteConsultaTipoVacina(path(proxy.getVacinaAplicacao().getTipoVacina()), true).setLabel(new Model<>(bundle("vacina"))));
        autoCompleteConsultaTipoVacina.add(new ConsultaListener<TipoVacina>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoVacina tipoVacina) {
                autoCompleteConsultaProdutoVacinaFabricante.limpar(target);
                autoCompleteConsultaProdutoVacinaFabricante.setEnabled(true);
                autoCompleteConsultaProdutoVacinaFabricante.setTipoVacina(tipoVacina);
                target.add(autoCompleteConsultaProdutoVacinaFabricante);
                controlDropDownDoses(target);
            }
        });
        autoCompleteConsultaTipoVacina.add(new RemoveListener<TipoVacina>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoVacina tipoVacina) {
                autoCompleteConsultaProdutoVacinaFabricante.limpar(target);
                autoCompleteConsultaProdutoVacinaFabricante.setEnabled(false);
                autoCompleteConsultaProdutoVacinaFabricante.setTipoVacina(null);
                target.add(autoCompleteConsultaProdutoVacinaFabricante);
                dropDownDoses.limpar(target);
                pnlSaidaLote.limparLote(target);
                controlDropDownDoses(target);
            }
        });
        getForm().add(getDropDownDoses(path(proxy.getVacinaAplicacao().getDose())));
        getForm().add(autoCompleteConsultaProdutoVacinaFabricante = (AutoCompleteConsultaProdutoVacinaFabricante) new AutoCompleteConsultaProdutoVacinaFabricante(path(proxy.getVacinaAplicacao().getProdutoVacina()), true)
                .setLabel(new Model<>(bundle("laboratorio"))));
        autoCompleteConsultaProdutoVacinaFabricante.add(new RemoveListener<ProdutoVacina>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ProdutoVacina object) {
                pnlSaidaLote.limparLote(target);
            }
        });
        autoCompleteConsultaProdutoVacinaFabricante.setIncluirInativo(false);


        containerHistoricoLote = new WebMarkupContainer("containerHistoricoLote");
        containerHistoricoLote.setOutputMarkupId(true);
        containerHistoricoLote.setVisible(false);
        containerHistoricoLote.setOutputMarkupPlaceholderTag(true);
        containerHistoricoLote.add(txtLote = new UpperField(path(proxy.getVacinaAplicacao().getLote())));

        getForm().add(containerHistoricoLote);

        containerLote = new WebMarkupContainer("containerLote");
        containerLote.setOutputMarkupId(true);
        containerLote.setOutputMarkupPlaceholderTag(true);
        containerLote.setVisible(true);
        containerLote.add(pnlSaidaLote = new PnlSaidaLote(path(proxy.getVacinaAplicacao().getLote())));
        pnlSaidaLote.setLabel(new Model<>(bundle("lote")));
        pnlSaidaLote.setAutoCompleteConsultaProdutoVacinaFabricante(autoCompleteConsultaProdutoVacinaFabricante);
        pnlSaidaLote.setValidarLotesVencidos(true);
        pnlSaidaLote.registerEvents();

        boolean isRotina = !AplicarVacinaHelper.getInstance().isCalendarioExportaEsus(getForm().getModel().getObject().getVacinaAplicacao().getEstrategia());

        dropDownViaAdministracao = (DropDown) DropDownUtil.getDropDownViaAdministracao(path(proxy.getVacinaAplicacao().getViaAdministracao()),isRotina).setLabel(new Model(bundle("viaAdministracao")));
        dropDownViaAdministracao.setOutputMarkupPlaceholderTag(true);
        dropDownViaAdministracao.setEnabled(isRotina);
        getForm().add(dropDownViaAdministracao);

        dropDownLocalAplicacao = (DropDown) DropDownUtil.getDropDownLocalAplicacaoVia(path(proxy.getVacinaAplicacao().getLocalAplicacao()),isRotina,dropDownViaAdministracao.getChoices().get(0)).setLabel(new Model(bundle("localAplicacao")));
        dropDownLocalAplicacao.setOutputMarkupPlaceholderTag(true);
        dropDownGrupoAtendimento.setEnabled(isRotina);
        getForm().add(dropDownLocalAplicacao);

        dropDownViaAdministracao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownLocalAplicacao.removeAllChoices();
                List<LocalAplicacao> list = AplicarVacinaHelper.getInstance().getLocalAplicacaoList(dropDownViaAdministracao.getComponentValue());
                for (LocalAplicacao local : list) {
                    dropDownLocalAplicacao.addChoice(local, local.getDescricao());
                }
                dropDownLocalAplicacao.setChoices(list);
                target.add(dropDownLocalAplicacao);
            }
        });

        pnlSaidaLote.addLoteListener(new PnlSaidaLote.iLoteListener() {
            @Override
            public void listener(AjaxRequestTarget target, MovimentoGrupoEstoqueItemDTO loteSelecionado) {
                if (loteSelecionado != null) {
                    validarPrazoValidade(target, loteSelecionado.getGrupoEstoque());
                } else {
                    validarPrazoValidade(target);
                }
            }
        });
        getForm().add(containerLote);
        getForm().add(autoCompleteConsultaProfissionalAplicacao = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getVacinaAplicacao().getProfissionalAplicacao()), true)
                .setLabel(new Model<>(bundle("profissionalAplicacao"))));
        autoCompleteConsultaProfissionalAplicacao.setComponentValue(ApplicationSession.get().getSessaoAplicacao().getUsuario().getProfissional());
        getForm().add(dchDataAplicacao = new RequiredDateChooser(path(proxy.getVacinaAplicacao().getDataAplicacao())));
        dchDataAplicacao.addAjaxUpdateValue();
        dchDataAplicacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarPrazoValidade(target);
            }
        });
        getForm().add(autoCompleteConsultaMotivoVacina = new AutoCompleteConsultaMotivoVacina(path(proxy.getVacinaAplicacao().getMotivoVacinaEspecial()), false));
        getForm().add(autoCompleteConsultaProfissionalIndicador = new AutoCompleteConsultaProfissionalIndicador(path(proxy.getVacinaAplicacao().getProfissionalIndicador()), false));

        // INÍCIO - Insumos
        VacinaAplicacaoInsumo proxyInsumo = on(VacinaAplicacaoInsumo.class);

        containerVacinaAplicacaoInsumo = new WebMarkupContainer("containerVacinaAplicacaoInsumo");
        containerVacinaAplicacaoInsumo.setOutputMarkupId(true);
        containerVacinaAplicacaoInsumo.setOutputMarkupPlaceholderTag(true);

        getFormInsumos().add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxyInsumo.getProduto())));
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setMedicamento(SubGrupo.MEDICAMENTO_NAO);
        autoCompleteConsultaProduto.setIncluirInativos(false);
        getFormInsumos().add(txtQuantidade = new InputField(path(proxyInsumo.getQuantidade())));
        getFormInsumos().add(pnlSaidaLoteInsumo = new PnlSaidaLote(path(proxyInsumo.getLote())));
        pnlSaidaLoteInsumo.setLabel(new Model<>(bundle("lote")));
        pnlSaidaLoteInsumo.setValidateRequired(false);
        pnlSaidaLoteInsumo.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlSaidaLoteInsumo.setValidarLotesVencidos(true);
        pnlSaidaLoteInsumo.registerEvents();

        getFormInsumos().add(new AbstractAjaxButton("btnAdicionarVacinaAplicacaoInsumo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarInsumo(target);
            }
        });

        getFormInsumos().add(tblVacinaAplicacaoInsumo = new Table("tblVacinaAplicacaoInsumo", getColumnsInsumo(), getCollectionProviderInsumo()));
        tblVacinaAplicacaoInsumo.populate();
        tblVacinaAplicacaoInsumo.setScrollY("1800");
        // FIM - Insumos

        AbstractAjaxButton btnVoltar;
        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar();
            }
        });
        btnVoltar.setDefaultFormProcessing(false);

        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        containerVacinaAplicacaoInsumo.add(getFormInsumos());
        getForm().add(containerVacinaAplicacaoInsumo);
        add(getForm());

        autoCompleteConsultaProdutoVacinaFabricante.setEnabled(false);
        Profissional profissionalLogado = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissionalLogado != null) {
            getForm().getModel().getObject().getVacinaAplicacao().setProfissionalAplicacao(profissionalLogado);
            autoCompleteConsultaProfissionalAplicacao.setComponentValue(profissionalLogado);
        }
        containerVacinaAplicacaoInsumo.setVisible(false);
        dropDownEstrategia.setRequired(false);
        dropDownEstrategia.removeRequiredClass();
        autoCompleteConsultaProdutoVacinaFabricante.setRequired(false);
        autoCompleteConsultaProdutoVacinaFabricante.getTxtDescricao().removeRequiredClass();
        autoCompleteConsultaProfissionalAplicacao.setRequired(false);
        autoCompleteConsultaProfissionalAplicacao.getTxtDescricao().removeRequiredClass();
        containerLote.setVisible(false);
        containerHistoricoLote.setVisible(true);
    }

    private Form<AplicacaoVacinaDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new AplicacaoVacinaDTO()));
            form.getModel().getObject().setVacinaAplicacao(new VacinaAplicacao());
        }
        return form;
    }

    private Form<VacinaAplicacaoInsumo> getFormInsumos() {
        if (formInsumos == null) {
            formInsumos = new Form("formInsumos", new CompoundPropertyModel(new VacinaAplicacaoInsumo()));
        }
        return formInsumos;
    }

    private DropDown getDropDownEstrategia(String id) {
        if (dropDownEstrategia == null) {
            dropDownEstrategia = new DropDown(id);
            dropDownEstrategia.setRequired(true);
            dropDownEstrategia.addRequiredClass();
            dropDownEstrategia.setLabel(new Model<>(bundle("estrategia")));
            List<Calendario> calendariosList = LoadManager.getInstance(Calendario.class)
                    .addProperties(new HQLProperties(Calendario.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(Calendario.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            dropDownEstrategia.addChoice(null, "");
            if (CollectionUtils.isNotNullEmpty(calendariosList)) {
                for (Calendario calendario : calendariosList) {
                    dropDownEstrategia.addChoice(calendario, calendario.getDescricao());
                }
            }
        }

        return dropDownEstrategia;
    }

    private DropDown getDropDownDoses(String id) {
        dropDownDoses = new DropDown(id);
        dropDownDoses.setEnabled(false);
        return dropDownDoses;
    }

    private void controlDropDownDoses(AjaxRequestTarget target) {

        for (VacinaCalendario.Doses doses : VacinaCalendario.Doses.values()) {
            dropDownDoses.addChoice(doses.value(), doses.descricao());
        }
        dropDownDoses.setEnabled(true);
        target.add(dropDownDoses);
    }

    private void initAplicacao() {
        getForm().getModel().getObject().getVacinaAplicacao().setUsuarioCadsus(usuarioCadsus);
        getForm().getModel().getObject().getVacinaAplicacao().setDataAplicacao(DataUtil.getDataAtual());
    }

    private void salvar() throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(VacinaFacade.class).registrarHistoricoVacinacao(getForm().getModel().getObject().getVacinaAplicacao());
        voltar();
    }

    private boolean validarAplicacao(String loteSelecionado) throws ValidacaoException {
        TipoVacina tipoVacina = getForm().getModel().getObject().getVacinaAplicacao().getTipoVacina();
        if (tipoVacina != null && tipoVacina.getValidadeAberta() != null && (pnlSaidaLote.getLoteSelecionado() != null || loteSelecionado != null)) {
            VacinaAplicacao proxy = on(VacinaAplicacao.class);

            List<VacinaAplicacao> list = LoadManager.getInstance(VacinaAplicacao.class)
                    .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoVacina()), tipoVacina))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLote()), (getForm().getModel().getObject().getVacinaAplicacao().getLote() != null ? getForm().getModel().getObject().getVacinaAplicacao().getLote() : loteSelecionado)))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IN,
                            Arrays.asList(VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value(), VacinaAplicacao.StatusVacinaAplicacao.REAPLICADA.value())))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1)
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                VacinaAplicacao ultimaAplicacao = list.get(0);
                Long diferenca = 0L;
                Long validadeAberta = tipoVacina.getValidadeAberta();

                if (TipoVacina.TempoAposAberta.HORAS.value().equals(tipoVacina.getTempoAberta())) {
                    diferenca = DataUtil.getMinutosDiferenca(ultimaAplicacao.getDataAplicacao(), getForm().getModel().getObject().getVacinaAplicacao().getDataAplicacao());

                    validadeAberta = new Dinheiro(validadeAberta).multiplicar(60D).longValue();

                    return diferenca > validadeAberta && diferenca <= 1440;
                } else {
                    diferenca = Long.valueOf(DataUtil.getDiasDiferenca(Data.adjustRangeHour(ultimaAplicacao.getDataAplicacao()).getDataInicial(),
                            Data.adjustRangeHour(getForm().getModel().getObject().getVacinaAplicacao().getDataAplicacao()).getDataInicial()));

                    if (TipoVacina.TempoAposAberta.ANOS.value().equals(tipoVacina.getTempoAberta())) {
                        validadeAberta = new Dinheiro(validadeAberta).multiplicar(365D).longValue();
                    } else if (TipoVacina.TempoAposAberta.MESES.value().equals(tipoVacina.getTempoAberta())) {
                        validadeAberta = new Dinheiro(validadeAberta).multiplicar(30D).longValue();
                    } else if (TipoVacina.TempoAposAberta.SEMANAS.value().equals(tipoVacina.getTempoAberta())) {
                        validadeAberta = new Dinheiro(validadeAberta).multiplicar(7D).longValue();
                    }

                    return diferenca > validadeAberta && new Dinheiro(diferenca).subtrair(validadeAberta.doubleValue()).intValue() <= 1;
                }
            }
        }
        return false;
    }

    private void initDlgConfirmacaoOk(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            addModal(target, dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId(), Bundle.getStringApplication("msg_venceu_prazo_vacina_apos_aberto")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgConfirmacaoOk.show(target);
    }

    private void voltar() {
        if (registroAgravo != null) {
            setResponsePage(new FichaInvestigacaoAgravoPage(registroAgravo));
        } else {
            setResponsePage(new ControleVacinacaoPage(usuarioCadsus));
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("aplicacaoVacina");
    }

    private void carregarUsuarioCadsusDado() {
        if (usuarioCadsus==null) return;
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperty(UsuarioCadsusDado.PROP_COMUNICANTE_HANSENIASE)
                .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                .setId(usuarioCadsus.getCodigo())
                .start().getVO();

        if (usuarioCadsusDado != null) {
            dropDownComunicanteHanseniase.setComponentValue(usuarioCadsusDado.getComunicanteHanseniase());
            dropDownGestante.setComponentValue(usuarioCadsusDado.getGestante());
        }
        if (UsuarioCadsus.SEXO_MASCULINO.equals(usuarioCadsus.getSexo())) {
            dropDownGestante.setEnabled(false);
            dropDownPuerpera.setEnabled(false);
        }
    }

    public void carregarUltimaAplicacao() {
        if (usuarioCadsus != null) {
            VacinaAplicacao vac = LoadManager.getInstance(VacinaAplicacao.class)
                    .addProperty(VacinaAplicacao.PROP_GRUPO_ATENDIMENTO)
                    .addProperty(VacinaAplicacao.PROP_FLAG_GESTANTE)
                    .addProperty(VacinaAplicacao.PROP_FLAG_PUERPERA)
                    .addProperty(VacinaAplicacao.PROP_COMUNICANTE_HANSENIASE)
                    .addParameter(new QueryCustom.QueryCustomParameter(VacinaAplicacao.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO, "desc"))
                    .setMaxResults(1)
                    .start().getVO();
            if (vac != null) {
                dropDownGrupoAtendimento.setComponentValue(vac.getGrupoAtendimento());
                dropDownGestante.setComponentValue(vac.getFlagGestante());
                dropDownPuerpera.setComponentValue(vac.getFlagPuerpera());
                dropDownComunicanteHanseniase.setComponentValue(vac.getComunicanteHanseniase());
            }
        }
    }

    private void validarPrazoValidade(AjaxRequestTarget target) {
        validarPrazoValidade(target, null);
    }

    private void validarPrazoValidade(AjaxRequestTarget target, String loteSelecionado) {
        try {
            boolean venceuPrazoVacina = validarAplicacao(loteSelecionado);

            if (venceuPrazoVacina) {
                initDlgConfirmacaoOk(target);
            }
        } catch (ValidacaoException ex) {
            Logger.getLogger(AplicarVacinaPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private List<IColumn> getColumnsInsumo() {
        List<IColumn> columns = new ArrayList<>();
        VacinaAplicacaoInsumo proxy = on(VacinaAplicacaoInsumo.class);

        columns.add(getActionColumnInsumo());
        columns.add(createColumn(bundle("insumo"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(bundle("lote"), proxy.getLote()));
        columns.add(createColumn(bundle("unidade"), proxy.getProduto().getUnidade().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private IColumn getActionColumnInsumo() {
        return new MultipleActionCustomColumn<VacinaAplicacaoInsumo>() {
            @Override
            public void customizeColumn(VacinaAplicacaoInsumo rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<VacinaAplicacaoInsumo>() {
                    @Override
                    public void action(AjaxRequestTarget target, VacinaAplicacaoInsumo modelObject) throws ValidacaoException, DAOException {
                        editarInsumo(target, modelObject);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<VacinaAplicacaoInsumo>() {
                    @Override
                    public void action(AjaxRequestTarget target, VacinaAplicacaoInsumo modelObject) throws ValidacaoException, DAOException {
                        removerInsumo(target, modelObject);
                    }
                });
            }
        };
    }

    private void editarInsumo(AjaxRequestTarget target, VacinaAplicacaoInsumo vacinaAplicacaoInsumo) throws DAOException, ValidacaoException {
        limparInsumo(target);
        VacinaAplicacaoInsumo insumo = (VacinaAplicacaoInsumo) SerializationUtils.clone(vacinaAplicacaoInsumo);
        vacinaAplicacaoInsumoEdicao = vacinaAplicacaoInsumo;

        getFormInsumos().getModel().setObject(insumo);

        target.add(autoCompleteConsultaProduto);
        target.add(txtQuantidade);
        pnlSaidaLoteInsumo.atualizarLotes(target);
        target.add(pnlSaidaLoteInsumo);
    }

    private void removerInsumo(AjaxRequestTarget target, VacinaAplicacaoInsumo vacinaAplicacaoInsumo) {
        limparInsumo(target);
        for (int i = 0; i < getForm().getModel().getObject().getInsumoAplicacaoList().size(); i++) {
            if (getForm().getModel().getObject().getInsumoAplicacaoList().get(i) == vacinaAplicacaoInsumo) {
                getForm().getModel().getObject().getInsumoAplicacaoList().remove(i);
                break;
            }
        }
        tblVacinaAplicacaoInsumo.update(target);
    }

    private void limparInsumo(AjaxRequestTarget target) {
        getFormInsumos().getModel().setObject(new VacinaAplicacaoInsumo());
        autoCompleteConsultaProduto.limpar(target);
        txtQuantidade.limpar(target);
        pnlSaidaLoteInsumo.limpar(target);
        vacinaAplicacaoInsumoEdicao = null;
    }

    private ICollectionProvider getCollectionProviderInsumo() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getInsumoAplicacaoList();
            }
        };
    }

    private void adicionarInsumo(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        VacinaAplicacaoInsumo vai = getFormInsumos().getModel().getObject();

        if (vai.getProduto() == null) {
            throw new ValidacaoException(bundle("informeInsumo"));
        }
        if (Coalesce.asLong(vai.getQuantidade()) == 0L) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        }

        SubGrupo subGrupo = vai.getProduto().getSubGrupo();
        if (subGrupo != null && RepositoryComponentDefault.SIM.equals(subGrupo.getFlagControlaGrupoEstoque())) {
            if (Coalesce.asString(vai.getLote()).trim().isEmpty()) {
                throw new ValidacaoException(BundleManager.getString("informeLote"));
            }
        }

        int idx = 0;
        for (int i = 0; i < getForm().getModel().getObject().getInsumoAplicacaoList().size(); i++) {
            VacinaAplicacaoInsumo vacinaAplicacaoInsumo = getForm().getModel().getObject().getInsumoAplicacaoList().get(i);
            if (vacinaAplicacaoInsumoEdicao != null && vacinaAplicacaoInsumoEdicao == vacinaAplicacaoInsumo) {
                idx = i;
            } else if (vacinaAplicacaoInsumo.getProduto().getCodigo().equals(vai.getProduto().getCodigo())
                    && vacinaAplicacaoInsumo.getLote().equals(vai.getLote())) {
                throw new ValidacaoException(bundle("insumoJaAdicionado"));
            }
        }

        if (vacinaAplicacaoInsumoEdicao != null && CollectionUtils.isNotNullEmpty(getForm().getModel().getObject().getInsumoAplicacaoList())) {
            getForm().getModel().getObject().getInsumoAplicacaoList().remove(idx);
            getForm().getModel().getObject().getInsumoAplicacaoList().add(idx, vai);
        } else {
            getForm().getModel().getObject().getInsumoAplicacaoList().add(0, vai);
        }

        limparInsumo(target);
        tblVacinaAplicacaoInsumo.update(target);
        target.focusComponent(autoCompleteConsultaProduto.getTxtDescricao().getTextField());
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (CollectionUtils.isNotNullEmpty(getForm().getModelObject().getInsumoAplicacaoList())) {
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(containerVacinaAplicacaoInsumo)));
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(getFormInsumos())));
        }
    }
}