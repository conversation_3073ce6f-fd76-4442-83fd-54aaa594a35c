package br.com.celk.view.vigilancia.financeiro.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;

/**
 * Created by leonardo.
 */
public abstract class EscolherVigilanciaFinanceiroViewPanel extends Panel {

    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private AbstractAjaxButton btnEscolherVigilanciaFinanceiro;
    private Class clazz;

    public EscolherVigilanciaFinanceiroViewPanel(String id, VigilanciaFinanceiro vigilanciaFinanceiro, Class clazz) {
        super(id);
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
        this.clazz = clazz;
        init();
    }

    private void init() {
        btnEscolherVigilanciaFinanceiro = new AbstractAjaxButton("btnEscolherVigilanciaFinanceiro") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                onEscolherVigilanciaFinanceiro(target, vigilanciaFinanceiro);
            }
        };
        String descricaoBotao;
        // todo 23797: definir como será identificado o boleto
        descricaoBotao = "BOLETO Nº "+ vigilanciaFinanceiro.getCodigo();
        if(StringUtils.trimToNull(vigilanciaFinanceiro.getInstrucoesPagador()) != null) {
            descricaoBotao = descricaoBotao.concat(" (" + vigilanciaFinanceiro.getInstrucoesPagador() + ")");
        }
        if(VigilanciaFinanceiro.Status.PAGO.value().equals(vigilanciaFinanceiro.getStatus()) || vigilanciaFinanceiro.getAnexoComprovantePagamento() != null) {
            btnEscolherVigilanciaFinanceiro.add(new AttributeModifier("style", "background-color: #008000;"));
        } else {
            btnEscolherVigilanciaFinanceiro.add(new AttributeModifier("style", "background-color: #F5F5F5;"));
        }
        Label label = (Label) new Label("labelButton", descricaoBotao).setOutputMarkupId(true);
        btnEscolherVigilanciaFinanceiro.add(label);
        add(btnEscolherVigilanciaFinanceiro);
    }

    public abstract void onEscolherVigilanciaFinanceiro(AjaxRequestTarget target, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException;
}
