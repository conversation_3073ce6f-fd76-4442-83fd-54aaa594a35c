package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoHantaviroseDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnorado;
import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.isLongSim;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.project;

public class FichaInvestigacaoAgravoHantavirosePage extends FichaInvestigacaoAgravoBasePage {

    private final String CSSFILE = "FichaInvestigacaoAgravoHantavirosePage.css";

    private InvestigacaoAgravoHantavirose investigacaoAgravoHantavirose;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DropDown ddTreinamentoMilitar;
    private DropDown ddDesmatamento;
    private DropDown ddExposicao;
    private DropDown ddMoagem;
    private DropDown ddDormiu;
    private DropDown ddTransporte;
    private DropDown ddPescou;
    private DropDown ddTeveContato;
    private InputField txtOutrasAtividades;

    private WebMarkupContainer containerDadosClinicos;
    private DateChooser dataPrimeiroAtendimento;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DisabledInputField txtCnes;
    private DisabledInputField txtMunicipio;
    private DisabledInputField txtUf;

    private DropDown ddFebre;
    private DropDown ddTosseSeca;
    private DropDown ddDispneia;
    private DropDown ddInsuficienciaResp;
    private DropDown ddCefaleia;
    private DropDown ddMialgia;
    private DropDown ddDorLombar;
    private DropDown ddDorAbdominal;
    private DropDown ddHipotensao;
    private DropDown ddChoque;
    private DropDown ddNauseas;
    private DropDown ddDiarreia;

    private DropDown ddDorToracica;
    private DropDown ddTontura;
    private DropDown ddInsuficienciaCardiaca;
    private DropDown ddInsuficienciaRenal;
    private DropDown ddSintomasNeuro;
    private DropDown ddAstenia;
    private DropDown ddPetequias;
    private InputField txtOutrasManifestacoes;
    private InputField txtOutros;

    private WebMarkupContainer containerDadosLaboratorio;
    private DropDown ddColheuAmostra;

    private DropDown ddHematocrito;
    private DropDown ddTrombocitopenia;
    private DropDown ddLinfocitos;
    private DropDown ddUreia;
    private InputField txtTgo;
    private InputField txtTgp;

    private DropDown ddResultadoB;
    private DropDown ddRadiografiaTorax;

    private DropDown ddPulmonarDifuso;
    private DropDown ddPulmonarLocalizado;
    private DropDown ddDerramePleural;

    private DateChooser dataColetaIgm;
    private DropDown ddResultadoIgm;
    private DropDown ddResultadoImunohistoquimica;
    private DateChooser dataRtPcr;
    private DropDown ddResultadoRtPcr;

    private RadioButtonGroup radioGroupHospitalizacao;
    private WebMarkupContainer containerHospitalizacao;
    private DateChooser dataInternacao;
    private DisabledInputField estadoHospitalizacao;
    private DisabledInputField municipioHospitalizacao;
    private DisabledInputField codMunicipioHospital;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeHospitalizacao;
    private DisabledInputField unidadeHospitalizacaoCnes;
    private DisabledInputField telefoneHospital;

    private DropDown ddRespiradorMecanico;
    private DropDown ddMedicamentoAntiviral;
    private DropDown ddCorticoide;
    private DropDown ddCpap;
    private DropDown ddDrogas;
    private DropDown ddAntibioticos;
    private InputField txtOutrosTerapeutico;

    private RadioButtonGroup radioGroupCasoAutoctone;
    private WebMarkupContainer containerLocalInfeccao;
    private AutoCompleteConsultaCidade autoCompleteCidadeLocalInfeccao;
    private DisabledInputField estadoLocalInfeccao;
    private AutoCompleteConsultaPais autoCompletePaisLocalInfeccao;
    private DisabledInputField codMunicipioLocalInfeccao;
    private InputField distritoLocalInfeccao;
    private InputField bairroLocalInfeccao;

    private DropDown ddZonaInfeccao;
    private DropDown ddTipoAmbiente;
    private InputField txtOutrosTipoAmbiente;
    private InputField txtLocalizacaoLpi;
    private DropDown ddLocalizacaoLpi;

    private WebMarkupContainer containerConclusao;
    private DropDown ddClassificacaoFinal;
    private DropDown ddFormaClinica;
    private DropDown ddCriterioDiagnostico;
    private DropDown ddEvolucaoCaso;
    private DateChooser dataObitoAlta;
    private DropDown ddAutopsia;
    private DropDown ddDoencaTrabalho;

    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public FichaInvestigacaoAgravoHantavirosePage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);

    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoHantavirose = InvestigacaoAgravoHantavirose.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoHantavirose == null) {
            investigacaoAgravoHantavirose = new InvestigacaoAgravoHantavirose();
            investigacaoAgravoHantavirose.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoHantavirose.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoHantavirose.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoHantavirose.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoHantavirose.setOcupacaoCbo(tabelaCbo);
        }

        if (investigacaoAgravoHantavirose.getCidadeLocalInfeccao() != null) {
            Cidade cidadeExposicao = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(investigacaoAgravoHantavirose.getCidadeLocalInfeccao().getCodigo());
            investigacaoAgravoHantavirose.setCidadeLocalInfeccao(cidadeExposicao);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoHantavirose)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoHantaviroseDTO fichaDTO = new FichaInvestigacaoAgravoHantaviroseDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoHantavirose(investigacaoAgravoHantavirose);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoHantavirose.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoHantavirose.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoHantavirose proxy = on(InvestigacaoAgravoHantavirose.class);

        criarInvestigacao(proxy);
        criarAntecedentesEpidemiologicos(proxy);
        criarDadosClinicos(proxy);
        criarDadosLaboratorio(proxy);
        criarHospitalizacao(proxy);
        criarLocalInfeccao(proxy);
        criarConclusao(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarDadosClinicos();
        carregarDadosLaboratorio();
        carregarHospitalizacao();
        carregarLocalInfeccao();
        carregarConclusao();
    }

    @Override
    public void validarFicha() throws ValidacaoException {

    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoHantaviroseDTO dto = (FichaInvestigacaoAgravoHantaviroseDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoHantavirose(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoHantaviroseDTO fichaDTO = (FichaInvestigacaoAgravoHantaviroseDTO) getFichaDTO();

        if (investigacaoAgravoHantavirose.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoHantavirose.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoHantavirose.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoHantavirosePage.class, CSSFILE))
        );
    }

    private void criarInvestigacao(InvestigacaoAgravoHantavirose proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.addRequiredClass();
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarAntecedentesEpidemiologicos(InvestigacaoAgravoHantavirose proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        ddTreinamentoMilitar = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeTreinamentoMilitar()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDesmatamento = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeDesmatamento()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddExposicao = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeLimpeza()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddMoagem = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeMoagem()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDormiu = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeDormiu()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddTransporte = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeTransporte()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddPescou = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadePescaCaca()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddTeveContato = DropDownUtil.getIEnumDropDown(path(proxy.getAtividadeContatoRato()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        txtOutrasAtividades = new InputField(path(proxy.getAtividadeOutras()));

        containerAntecedentesEpidemiologicos.add(ddTreinamentoMilitar, ddDesmatamento, ddExposicao, ddMoagem,
                ddDormiu, ddTransporte, ddPescou, ddTeveContato, txtOutrasAtividades
        );
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void criarDadosClinicos(InvestigacaoAgravoHantavirose proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        dataPrimeiroAtendimento = new DateChooser(path(proxy.getDataPrimeiroAtendimento()));
        dataPrimeiroAtendimento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataPrimeiroAtendimento.getData().setMinDate(new DateOption(getAgravo().getDataPrimeirosSintomas()));

        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getLocalPrimeiroAtendimento()));
        txtCnes = new DisabledInputField(path(proxy.getLocalPrimeiroAtendimento().getCnes()));
        txtMunicipio = new DisabledInputField(path(proxy.getLocalPrimeiroAtendimento().getCidade().getDescricao()));
        txtUf = new DisabledInputField(path(proxy.getLocalPrimeiroAtendimento().getCidade().getEstado().getSigla()));

        ddFebre = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaFebre()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true, true);
        ddTosseSeca = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaTosseSeca()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddDispneia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaDispneia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddInsuficienciaResp = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaInsuficienciaRespiratoria()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddCefaleia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaCefaleia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddMialgia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaMialgiaGeneralizada()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddDorLombar = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaDorLombar()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddDorAbdominal = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaDorAbdominal()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddHipotensao = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaHipotensao()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddChoque = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaChoque()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddNauseas = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaNauseasVomito()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddDiarreia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaDiarreia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddDorToracica = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaDorToracica()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddTontura = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaTontura()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddInsuficienciaCardiaca = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaInsuficienciaCardiaca()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddInsuficienciaRenal = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaInsuficienciaRenal()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddSintomasNeuro = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaNeurologico()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddAstenia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaAstenia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        ddPetequias = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaPetequias()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true,true);
        txtOutrasManifestacoes = new InputField(path(proxy.getSinalSintomaOutrosHemorragico()));
        txtOutros = new InputField(path(proxy.getSinalSintomaOutos()));

        containerDadosClinicos.add(dataPrimeiroAtendimento, autoCompleteConsultaEmpresa, txtCnes, txtMunicipio, txtUf,
                ddFebre, ddTosseSeca, ddDispneia, ddInsuficienciaResp, ddCefaleia, ddMialgia, ddDorLombar, ddDorAbdominal,
                ddHipotensao, ddChoque, ddNauseas, ddDiarreia, ddDorToracica, ddTontura, ddInsuficienciaCardiaca,
                ddInsuficienciaRenal, ddSintomasNeuro, ddAstenia, ddPetequias, txtOutrasManifestacoes, txtOutros
        );
        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void carregarDadosClinicos() {
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                target.add(txtCnes);
                target.add(txtMunicipio);
                target.add(txtUf);

            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                target.add(txtCnes);
                target.add(txtMunicipio);
                target.add(txtUf);
            }
        });
    }

    private void criarDadosLaboratorio(InvestigacaoAgravoHantavirose proxy) {
        containerDadosLaboratorio = new WebMarkupContainer("containerDadosLaboratorio");
        containerDadosLaboratorio.setOutputMarkupId(true);

        ddColheuAmostra = DropDownUtil.getIEnumDropDown(path(proxy.getColheuAmostraSangue()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);

        ddHematocrito = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoAHematocrito()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.values(), true);
        ddTrombocitopenia = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoATrombocitopenia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.values(), true);
        ddLinfocitos = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoALinfocitoAtipico()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.values(), true);
        ddUreia = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoAUreiaCreatina()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.values(), true);
        txtTgo = new InputField(path(proxy.getResultadoATGO()));
        txtTgp = new InputField(path(proxy.getResultadoATGP()));

        ddResultadoB = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoBLeucocito()), InvestigacaoAgravoHantaviroseEnum.ResultadoBEnum.values(), true);
        ddRadiografiaTorax = DropDownUtil.getIEnumDropDown(path(proxy.getRadiografiaTorax()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);

        ddPulmonarDifuso = DropDownUtil.getIEnumDropDown(path(proxy.getAlteracoesToraxInfiltradoDifuso()), InvestigacaoAgravoHantaviroseEnum.ResultadoBEnum.values(), true);
        ddPulmonarLocalizado = DropDownUtil.getIEnumDropDown(path(proxy.getAlteracoesToraxInfiltradoLocalizado()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDerramePleural = DropDownUtil.getIEnumDropDown(path(proxy.getAlteracoesToraxDerramePleural()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);

        dataColetaIgm = new DateChooser(path(proxy.getExameSorologicoDataColeta()));
        dataColetaIgm.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataColetaIgm.getData().setMinDate(new DateOption(getAgravo().getDataPrimeirosSintomas()));
        ddResultadoIgm = DropDownUtil.getIEnumDropDown(path(proxy.getExameSorologicoResultado()), InvestigacaoAgravoHantaviroseEnum.ResultadoReagenteEnum.values(), true);

        ddResultadoImunohistoquimica = DropDownUtil.getIEnumDropDown(path(proxy.getImunohistoquimica()), InvestigacaoAgravoHantaviroseEnum.ResultadoPositivoEnum.values(), true);

        dataRtPcr = new DateChooser(path(proxy.getRtPcrDataColeta()));
        dataRtPcr.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataRtPcr.getData().setMinDate(new DateOption(getAgravo().getDataPrimeirosSintomas()));
        ddResultadoRtPcr = DropDownUtil.getIEnumDropDown(path(proxy.getRtPcrResultado()), InvestigacaoAgravoHantaviroseEnum.ResultadoPositivoEnum.values(), true);

        containerDadosLaboratorio.add(ddColheuAmostra, ddHematocrito, ddTrombocitopenia, ddLinfocitos, ddUreia, txtTgo, txtTgp,
                ddResultadoB, ddRadiografiaTorax, ddPulmonarDifuso, ddPulmonarLocalizado, ddDerramePleural, dataColetaIgm,
                ddResultadoIgm, ddResultadoImunohistoquimica, dataRtPcr, ddResultadoRtPcr
        );
        getContainerInformacoesComplementares().add(containerDadosLaboratorio);
    }

    private void carregarDadosLaboratorio() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddHematocrito, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTrombocitopenia, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLinfocitos, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddUreia, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtTgo, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtTgp, false, false, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddResultadoB, false, false, null);

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddPulmonarDifuso, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddPulmonarLocalizado, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddDerramePleural, false, false, null);

        ddColheuAmostra.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddColheuAmostra, InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.SIM.value()))) {
                    ddHematocrito.setEnabled(true);
                    ddTrombocitopenia.setEnabled(true);
                    ddLinfocitos.setEnabled(true);
                    ddUreia.setEnabled(true);
                    txtTgo.setEnabled(true);
                    txtTgp.setEnabled(true);
                    ddResultadoB.setEnabled(true);
                } else {
                    ddHematocrito.setEnabled(false);
                    ddTrombocitopenia.setEnabled(false);
                    ddLinfocitos.setEnabled(false);
                    ddUreia.setEnabled(false);
                    txtTgo.setEnabled(false);
                    txtTgp.setEnabled(false);
                    ddResultadoB.setEnabled(false);
                }

                target.add(ddHematocrito, ddTrombocitopenia, ddLinfocitos, ddUreia, txtTgo, txtTgp, ddResultadoB);
            }
        });

        ddRadiografiaTorax.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddRadiografiaTorax, InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.SIM.value()))) {
                    ddPulmonarDifuso.setEnabled(true);
                    ddPulmonarLocalizado.setEnabled(true);
                    ddDerramePleural.setEnabled(true);
                } else {
                    ddPulmonarDifuso.setEnabled(false);
                    ddPulmonarLocalizado.setEnabled(false);
                    ddDerramePleural.setEnabled(false);
                }

                target.add(ddPulmonarDifuso, ddPulmonarLocalizado, ddDerramePleural);
            }
        });
    }

    private void criarHospitalizacao(InvestigacaoAgravoHantavirose proxy) {
        containerHospitalizacao = new WebMarkupContainer("containerHospitalizacao");
        containerHospitalizacao.setOutputMarkupId(true);
        containerHospitalizacao.setEnabled(false);

        radioGroupHospitalizacao = new RadioButtonGroup(path(proxy.getHospitalizacao()));
        radioGroupHospitalizacao.setRequired(true);
        createRadioSimNaoIgnorado(radioGroupHospitalizacao, containerHospitalizacao);

        //Unidade Hospital
        dataInternacao = new DateChooser(path(proxy.getDataInternacao()));
        autoCompleteUnidadeHospitalizacao = new AutoCompleteConsultaEmpresa(path(proxy.getHospital()));
        unidadeHospitalizacaoCnes = new DisabledInputField(path(proxy.getHospital().getCnes()));
        telefoneHospital = new DisabledInputField(path(proxy.getHospital().getTelefoneFormatado()));
        municipioHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getDescricao()));
        codMunicipioHospital = new DisabledInputField(path(proxy.getHospital().getCidade().getCodigo()));
        estadoHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getEstado().getSigla()));

        ddRespiradorMecanico = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoRespiradorMecanico()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddMedicamentoAntiviral = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoMedicamentoAntiviral()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddCorticoide = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoCorticoide()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddCpap = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoCpapBipap()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDrogas = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoDrogasVasoativas()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddAntibioticos = DropDownUtil.getIEnumDropDown(path(proxy.getSuporteTerapeuticoAntibiotico()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        txtOutrosTerapeutico = new InputField(path(proxy.getSuporteTerapeuticoOutros()));

        containerHospitalizacao.add(
                dataInternacao,
                autoCompleteUnidadeHospitalizacao, unidadeHospitalizacaoCnes, telefoneHospital,
                municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao,
                ddRespiradorMecanico, ddMedicamentoAntiviral, ddCorticoide, ddCpap,
                ddDrogas, ddAntibioticos, txtOutrosTerapeutico);

        getContainerInformacoesComplementares().add(radioGroupHospitalizacao, containerHospitalizacao);
    }

    private void carregarHospitalizacao() {
        containerHospitalizacao.setEnabled(isLongSim(investigacaoAgravoHantavirose.getHospitalizacao()));

        dataInternacao.getData().setMinDate(new DateOption(investigacaoAgravoHantavirose.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataInternacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        autoCompleteUnidadeHospitalizacao.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                unidadeHospitalizacaoCnes.setComponentValue(empresa.getCnes());
                telefoneHospital.setComponentValue(empresa.getTelefoneFormatado());
                municipioHospitalizacao.setComponentValue(empresa.getCidade().getDescricao());
                codMunicipioHospital.setComponentValue(empresa.getCidade().getCodigo().toString());

                Cidade cidadeTemp = empresa.getCidade();

                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = LoadManager.getInstance(Cidade.class)
                            .addProperty(VOUtils.montarPath(Cidade.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_DESCRICAO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .setId(empresa.getCidade().getCodigo())
                            .start().getVO();
                }

                estadoHospitalizacao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(unidadeHospitalizacaoCnes, telefoneHospital, municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao);
            }
        });
        autoCompleteUnidadeHospitalizacao.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                unidadeHospitalizacaoCnes.limpar(target);
                telefoneHospital.limpar(target);
                municipioHospitalizacao.limpar(target);
                codMunicipioHospital.limpar(target);
                estadoHospitalizacao.limpar(target);
            }
        });
    }

    private void criarLocalInfeccao(InvestigacaoAgravoHantavirose proxy) {
        containerLocalInfeccao = new WebMarkupContainer("containerLocalInfeccao");
        containerLocalInfeccao.setOutputMarkupId(true);

        radioGroupCasoAutoctone = new RadioButtonGroup(path(proxy.getCasoAutoctone()));
        radioGroupCasoAutoctone.setRequired(true);
        FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnoradoIndeterminado(radioGroupCasoAutoctone, containerLocalInfeccao, false, true, true, false, false, false);

        autoCompleteCidadeLocalInfeccao = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalInfeccao()));
        codMunicipioLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getCodigo()));
        estadoLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getEstado().getSigla()));

        autoCompletePaisLocalInfeccao = new AutoCompleteConsultaPais(path(proxy.getPaisLocalInfeccao()));
        distritoLocalInfeccao = new InputField(path(proxy.getDistritoLocalInfeccao()));
        bairroLocalInfeccao = new InputField(path(proxy.getBairroLocalInfeccao()));

        ddZonaInfeccao = DropDownUtil.getIEnumDropDown(path(proxy.getLocalInfeccaoCaracteristicaZona()), InvestigacaoAgravoHantaviroseEnum.ZonaEnum.values(), true);
        ddTipoAmbiente = DropDownUtil.getIEnumDropDown(path(proxy.getLocalInfeccaoTipoAmbiente()), InvestigacaoAgravoHantaviroseEnum.TipoAmbienteEnum.values(), true);
        txtOutrosTipoAmbiente = new InputField(path(proxy.getLocalInfeccaoTipoAmbienteOutro()));
        txtLocalizacaoLpi = new InputField(path(proxy.getLocalInfeccaoLocalizacaoLpiKm()));
        ddLocalizacaoLpi = DropDownUtil.getIEnumDropDown(path(proxy.getLocalInfeccaoLocalizacaoLpiDirecao()), InvestigacaoAgravoHantaviroseEnum.LocalizacaoEnum.values(), true);

        containerLocalInfeccao.add(
                radioGroupCasoAutoctone,
                autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao,
                ddZonaInfeccao, ddTipoAmbiente, txtOutrosTipoAmbiente, txtLocalizacaoLpi,
                ddLocalizacaoLpi
        );

        getContainerInformacoesComplementares().add(radioGroupCasoAutoctone, containerLocalInfeccao);
    }

    private void carregarLocalInfeccao() {
        boolean notAutoctone = investigacaoAgravoHantavirose.getCasoAutoctone() != null && investigacaoAgravoHantavirose.getCasoAutoctone() == 2L;
        containerLocalInfeccao.setEnabled(notAutoctone);

        FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutrosTipoAmbiente, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalizacaoLpi, true, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalizacaoLpi, true, false, null);

        ddTipoAmbiente.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoAmbiente, InvestigacaoAgravoHantaviroseEnum.TipoAmbienteEnum.OUTRO.value()))) {
                    txtOutrosTipoAmbiente.setEnabled(true);
                }
                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoAmbiente, InvestigacaoAgravoHantaviroseEnum.TipoAmbienteEnum.IGNORADO.value()))) {
                    txtOutrosTipoAmbiente.setEnabled(false);
                    txtOutrosTipoAmbiente.limpar(target);
                    txtLocalizacaoLpi.setEnabled(false);
                    ddLocalizacaoLpi.setEnabled(false);
                }
                if ((!isModoLeitura()) && (!FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoAmbiente, InvestigacaoAgravoHantaviroseEnum.TipoAmbienteEnum.IGNORADO.value()))
                    && (!FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoAmbiente, InvestigacaoAgravoHantaviroseEnum.TipoAmbienteEnum.OUTRO.value()))) {
                    txtLocalizacaoLpi.setEnabled(true);
                    ddLocalizacaoLpi.setEnabled(true);
                    txtOutrosTipoAmbiente.setEnabled(false);
                    txtOutrosTipoAmbiente.limpar(target);
                }

                target.add(txtOutrosTipoAmbiente, txtLocalizacaoLpi, ddLocalizacaoLpi);
            }
        });

        autoCompletePaisLocalInfeccao.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoLocalInfeccao.limpar(target);
                    autoCompleteCidadeLocalInfeccao.limpar(target);
                    codMunicipioLocalInfeccao.limpar(target);
                }
            }
        });
        autoCompletePaisLocalInfeccao.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteCidadeLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioLocalInfeccao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoLocalInfeccao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioLocalInfeccao, estadoLocalInfeccao);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);
            }
        });
    }

    private void criarConclusao(InvestigacaoAgravoHantavirose proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddClassificacaoFinal = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoFinal()), InvestigacaoAgravoHantaviroseEnum.ClassificacaoFinalEnum.values(), true);
        ddFormaClinica = DropDownUtil.getIEnumDropDown(path(proxy.getFormaClinica()), InvestigacaoAgravoHantaviroseEnum.FormaClinicaEnum.values(), true);
        ddCriterioDiagnostico = DropDownUtil.getIEnumDropDown(path(proxy.getCriterioDiagnostico()), InvestigacaoAgravoHantaviroseEnum.CriterioDiagnosticoEnum.values(), true);
        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoHantaviroseEnum.EvolucaoCasoEnum.values(), true);

        dataObitoAlta = new DateChooser(path(proxy.getDataObitoAlta()));

        ddAutopsia = DropDownUtil.getIEnumDropDown(path(proxy.getObitoAutopsia()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);
        ddDoencaTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaRelacionadaTrabalho()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.getSimNaoIgonorado(), true);

        containerConclusao.add(ddClassificacaoFinal, ddFormaClinica, ddCriterioDiagnostico, ddEvolucaoCaso,
                dataObitoAlta, ddAutopsia, ddDoencaTrabalho
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddClassificacaoFinal, true, true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddFormaClinica, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCriterioDiagnostico, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddEvolucaoCaso, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataObitoAlta, false, false, null);

        ddClassificacaoFinal.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddClassificacaoFinal, InvestigacaoAgravoHantaviroseEnum.ClassificacaoFinalEnum.DESCARTADO.value()))) {
                    ddFormaClinica.setEnabled(false);
                    ddCriterioDiagnostico.setEnabled(false);
                    txtLocalizacaoLpi.setEnabled(false);
                    ddLocalizacaoLpi.setEnabled(false);
                    ddEvolucaoCaso.setEnabled(false);
                    dataObitoAlta.setEnabled(false);
                    ddAutopsia.setEnabled(false);
                    ddDoencaTrabalho.setEnabled(false);
                } else {
                    ddFormaClinica.setEnabled(true);
                    ddCriterioDiagnostico.setEnabled(true);
                    txtLocalizacaoLpi.setEnabled(true);
                    ddLocalizacaoLpi.setEnabled(true);
                    ddEvolucaoCaso.setEnabled(true);
                    dataObitoAlta.setEnabled(true);
                    ddAutopsia.setEnabled(true);
                    ddDoencaTrabalho.setEnabled(true);
                }
                target.add(ddFormaClinica, ddCriterioDiagnostico, txtLocalizacaoLpi, ddLocalizacaoLpi, ddEvolucaoCaso,
                        dataObitoAlta, ddAutopsia, ddDoencaTrabalho);
            }
        });

        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && ((FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoHantaviroseEnum.EvolucaoCasoEnum.OBITO_HANTAVIROSE.value()))
                        || (FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoHantaviroseEnum.EvolucaoCasoEnum.OBITO_OUTRA_CAUSA.value())))) {
                    dataObitoAlta.setEnabled(true);
                    ddAutopsia.setEnabled(true);
                    dataObitoAlta.setRequired(true);
                } else {
                    dataObitoAlta.setEnabled(false);
                    ddAutopsia.setEnabled(false);
                    dataObitoAlta.setRequired(false);
                }
                target.add(dataObitoAlta, ddAutopsia);
            }
        });


    }

    private void criarObservacoes(InvestigacaoAgravoHantavirose proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoHantavirose proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }


}
