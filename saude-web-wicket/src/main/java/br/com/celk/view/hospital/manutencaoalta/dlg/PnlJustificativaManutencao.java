package br.com.celk.view.hospital.manutencaoalta.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlJustificativaManutencao extends Panel {

    private AbstractAjaxButton btnFechar;
    private DisabledInputArea<String> txaJustificativa;
    private String justificativa;

    public PnlJustificativaManutencao(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        Form form = new Form("form");

        form.add(txaJustificativa = new DisabledInputArea<String>("justificativa", new PropertyModel(this, "justificativa")));

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        add(form);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txaJustificativa.limpar(target);
    }

    public void setJustificativa(AjaxRequestTarget target, String justificativa) {
        this.justificativa = justificativa;
        target.add(txaJustificativa);
    }
}
