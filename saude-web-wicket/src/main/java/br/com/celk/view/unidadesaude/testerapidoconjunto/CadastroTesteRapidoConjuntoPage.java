package br.com.celk.view.unidadesaude.testerapidoconjunto;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoConjunto;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastroTesteRapidoConjuntoPage extends CadastroPage<TesteRapidoConjunto> {

    private String nomeConjunto;
    private String fabricante;
    private String metodo;
    private Long status;
    private DropDown dropDownTipoExame;

    public CadastroTesteRapidoConjuntoPage(TesteRapidoConjunto object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTesteRapidoConjuntoPage(TesteRapidoConjunto object) {
        super(object);
    }

    public CadastroTesteRapidoConjuntoPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void init(Form<TesteRapidoConjunto> form) {
        TesteRapidoConjunto proxy = on(TesteRapidoConjunto.class);

        form.add(getDropDownTipoExame(proxy).setRequired(true));
        form.add(new InputField("nomeConjunto").setRequired(true));
        form.add(new InputField("fabricante").setRequired(true));
        form.add(new InputField("metodo").setRequired(true));
        form.add(getDropDownSituacao().setRequired(true));

    }

    private DropDown getDropDownSituacao() {
        DropDown dropDownSituacao = new DropDown("status");
        dropDownSituacao.setLabel(Model.of(bundle("situacao")));

        dropDownSituacao.addChoice(null, "");
        dropDownSituacao.addChoice(TesteRapidoConjunto.STATUS_ATIVO, bundle("ativo"));
        dropDownSituacao.addChoice(TesteRapidoConjunto.STATUS_INATIVO, bundle("inativo"));

        return dropDownSituacao;
    }

    private DropDown getDropDownTipoExame(TesteRapidoConjunto proxy) {
        if (dropDownTipoExame == null) {
            dropDownTipoExame = new DropDown(path(proxy.getTesteRapidoTipo()));

            dropDownTipoExame.addChoice(null, bundle("selecione"));
            List<TipoTesteRapido> lst = LoadManager.getInstance(TipoTesteRapido.class)
                    .addProperties(new HQLProperties(TipoTesteRapido.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, TipoTesteRapido.PROP_PROCEDIMENTO).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoTesteRapido.PROP_CODIGO))
                    .start().getList();

            for (TipoTesteRapido ttr : lst) {
                dropDownTipoExame.addChoice(ttr, ttr.getDescricao());
            }
        }

        return dropDownTipoExame;
    }

    @Override
    public Class<TesteRapidoConjunto> getReferenceClass() {
        return TesteRapidoConjunto.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTesteRapidoConjuntoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTesteRapidoConjunto");
    }

}
