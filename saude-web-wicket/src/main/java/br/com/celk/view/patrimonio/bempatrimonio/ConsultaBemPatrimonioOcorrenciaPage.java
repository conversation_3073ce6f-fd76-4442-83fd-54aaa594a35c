package br.com.celk.view.patrimonio.bempatrimonio;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.longfield.DisabledLongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.ISortableColumn;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import br.com.ksisolucoes.vo.patrimonio.BemPatrimonioOcorrencia;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class ConsultaBemPatrimonioOcorrenciaPage extends BasePage {

    private final BemPatrimonio bemPatrimonio;
    private Table<BemPatrimonioOcorrencia> tblOcorrencias;

    public ConsultaBemPatrimonioOcorrenciaPage(BemPatrimonio object) {
        super();
        this.bemPatrimonio = object;
    }

    @Override
    protected void postConstruct() {
        Form form = new Form("form", new CompoundPropertyModel(bemPatrimonio));

        form.add(new DisabledLongField("referencia"));
        form.add(new DisabledInputField("descricaoStatus"));
        form.add(new DisabledInputField("descricao"));
        form.add(new VoltarButton("btnVoltar"));

        form.add(tblOcorrencias = new Table<BemPatrimonioOcorrencia>("tblOcorrencias", getColumns(), getCollectionProvider()));
        tblOcorrencias.populate();

        add(form);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return carregarTabela(getSort());
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>(BemPatrimonioOcorrencia.PROP_DATA_OCORRENCIA, false);
            }
        };
    }

    private Collection carregarTabela(SortParam sortParam) {
        LoadManager load = LoadManager.getInstance(BemPatrimonioOcorrencia.class);
        load.addParameter(new QueryCustom.QueryCustomParameter(BemPatrimonioOcorrencia.PROP_BEM_PATRIMONIO, bemPatrimonio));

        String prop = (String) sortParam.getProperty();
        String order = sortParam.isAscending() ? BuilderQueryCustom.QuerySorter.CRESCENTE : BuilderQueryCustom.QuerySorter.DECRESCENTE;
        load.addSorter(new QueryCustom.QueryCustomSorter(prop, order));

        return load.start().getList();
    }

    public List<ISortableColumn<BemPatrimonioOcorrencia>> getColumns() {
        List<ISortableColumn<BemPatrimonioOcorrencia>> columns = new ArrayList<ISortableColumn<BemPatrimonioOcorrencia>>();
        BemPatrimonioOcorrencia proxy = on(BemPatrimonioOcorrencia.class);

        columns.add(new DateTimeColumn<BemPatrimonioOcorrencia>(bundle("data"), path(proxy.getDataOcorrencia())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createSortableColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        return columns;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaBemPatrimonioOcorrencia");
    }
}
