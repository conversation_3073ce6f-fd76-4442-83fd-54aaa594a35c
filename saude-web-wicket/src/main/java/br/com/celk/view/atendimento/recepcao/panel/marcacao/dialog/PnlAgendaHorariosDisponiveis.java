package br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.HorariosDisponiveisDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.ArrayList;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.Collection;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import java.util.Date;
import java.util.List;
import static ch.lambdaj.Lambda.on;
import java.text.ParseException;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlAgendaHorariosDisponiveis extends Panel{
    
    private Form<AgendaGradeAtendimentoDTO> form;
    private AgendaGradeAtendimentoDTO dto;
    private Date data;
    private String profissional;
    private Date horaInicio;
    private Date horaFinal;
    private Long tempoConsulta;
    private Long horarioFechado;
    private String observacao;
    
    private InputField txtHoraInicio;
    private InputField txtTempoConsulta;
    private InputField txtObservacao;
    private DropDown dropDownHorarioFechado;
    private AjaxButton btnAgendarContinuar;

    private Table<HorariosDisponiveisDTO> tblHorariosDisponiveis;
    private List<HorariosDisponiveisDTO> horariosDisponiveisList;
    
    public PnlAgendaHorariosDisponiveis(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form<AgendaGradeAtendimentoDTO>("form", new CompoundPropertyModel(this));
        setOutputMarkupId(true);
        
        form.add(new DisabledInputField("data"));
        form.add(new DisabledInputField("profissional"));
        form.add(txtHoraInicio = (InputField) new HoraMinutoField("horaInicio").setEnabled(false));
        form.add(txtTempoConsulta = new InputField<Long>("tempoConsulta", new PropertyModel<Long>(this, "tempoConsulta")));
        form.add(txtObservacao = new InputField("observacao", new PropertyModel<Date>(this, "observacao")));
        form.add(dropDownHorarioFechado = DropDownUtil.getSimNaoLongDropDown("horarioFechado", new PropertyModel<Long>(this, "horarioFechado"), true, false));
        
        tblHorariosDisponiveis = new Table("tblHorariosDisponiveis", getColumns(), getCollectionProvider());
        tblHorariosDisponiveis.setScrollY("180px");
        form.add(tblHorariosDisponiveis);
        tblHorariosDisponiveis.populate();
        
        form.add(new AbstractAjaxButton("btnAgendar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarRegrasNegocio(target)){
                    onConfirmar(target, dto);
                }
            }
        });
        
        form.add(btnAgendarContinuar = new AbstractAjaxButton("btnAgendarContinuar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarRegrasNegocio(target)){
                    onContinuar(target, dto);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        HorariosDisponiveisDTO proxy = on(HorariosDisponiveisDTO.class);
        
        columns.add(getCustomActionColumn());
        columns.add(new TimeColumn<Date>(bundle("hrInicio"), path(proxy.getHoraInicial())).setPattern("HH:mm"));
        columns.add(new TimeColumn<Date>(bundle("hrFim"), path(proxy.getHoraFinal())).setPattern("HH:mm"));

        return columns;
    }
    
    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<HorariosDisponiveisDTO>() {
            @Override
            public void customizeColumn(final HorariosDisponiveisDTO rowObject) {
                ActionLinkPanel alp = addAction(ActionType.CONFIRMAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        horaInicio = rowObject.getHoraInicial();
                        target.add(txtHoraInicio);
                    }
                });
                alp.setTitleBundleKey("selecionar");
            }
        };
    }
    
    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return horariosDisponiveisList;
            }
        };
    }
    
    public boolean validarRegrasNegocio(AjaxRequestTarget target) throws DAOException {
        if(validarCadastro(target)){
            try {
                Date dataFormatada = Data.addMinutos(Data.getDateTime(data, horaInicio), tempoConsulta.intValue());

                if(DataUtil.compareHour(dataFormatada, horaFinal) > 0){
                    throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_soma_horario_tempo_consulta_nao_podem_ultrapassar_hora_final_definada_agenda"));
                }
                dto.setHoraFinalAtendimento(dataFormatada);
                dto.setHoraInicioAgendaPersonalizada(DataUtil.mergeDataHora(data, horaInicio));

                List<AgendaGradeAtendimentoHorario> conflitoHorarios = BOFactoryWicket.getBO(AgendamentoFacade.class)
                        .consultarConflitoHorariosAgendaPersonalizada(dto.getAgendaGradeAtendimento(), horaInicio, dto.getHoraFinalAtendimento());

                if(CollectionUtils.isNotNullEmpty(conflitoHorarios)){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_horario_conflita_horario_agendado_favor_verificar_tempo_consulta"));
                }

            } catch (ValidacaoException e) {
                MessageUtil.modalWarn(target, this, e);
                return false;
            } catch (ParseException ex) {
                MessageUtil.modalWarn(target, this, ex);
                return false;
            }

            dto.setTempoConsulta(tempoConsulta);
            dto.setHorarioFechado(horarioFechado);
            dto.setObservacaoAtendimento(observacao);
        } else {
            return false;            
        }
        
        return true;
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) throws DAOException {
        try {
            if(txtHoraInicio.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_horario"));
            }
            if(txtTempoConsulta.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_informe_tempo_consulta"));
            }
            if(tempoConsulta % dto.getAgendaGradeAtendimento().getTempoMedio() != 0L){  
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_valor_tempo_consulta_deve_ser_multiplo_valor_tempo_medio_agenda_tempo_medio_x",
                        dto.getAgendaGradeAtendimento().getTempoMedio()));
            }  
            if(dropDownHorarioFechado.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_selecione_campo_horario_fechado"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onContinuar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target) {
        this.dto = null;
        this.data = null;
        this.profissional = null;
        this.horaInicio = null;
        this.tempoConsulta = null;
        this.observacao = null;
        this.horarioFechado = null;
        txtTempoConsulta.limpar(target);
        txtObservacao.limpar(target);
        dropDownHorarioFechado.limpar(target);
        horariosDisponiveisList = new ArrayList<HorariosDisponiveisDTO>();
        
        target.add(form);
    }
    
    public void setDTO(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException{
        this.dto = dto;
        this.data = dto.getAgendaGradeAtendimento().getAgendaGrade().getData();
        if(dto.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional() != null){
            this.profissional = dto.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome();            
        }
        this.horaInicio = dto.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial();
        this.tempoConsulta = dto.getAgendaGradeAtendimento().getTempoMedio();

        target.add(txtTempoConsulta);
        carregarAgendamentos();
        btnAgendarContinuar.setVisible(dto.getTipoProcedimento().habilitaAgendamentoGrupo());
    }
    
    private void carregarAgendamentos() throws ValidacaoException, DAOException{        
        Date dataFormatada = null;

        try {
            dataFormatada = Data.getDateTime(this.data, this.horaInicio);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        do {
            HorariosDisponiveisDTO horariosDisponiveisDTO = new HorariosDisponiveisDTO();
            horariosDisponiveisDTO.setHoraInicial(dataFormatada);
            
            dataFormatada = Data.addMinutos(dataFormatada, this.tempoConsulta.intValue());

            if(DataUtil.compareHour(dataFormatada, dto.getAgendaGradeAtendimento().getAgendaGrade().getHoraFinal()) > 0){
                dataFormatada = DataUtil.mergeDataHora(this.data, dto.getAgendaGradeAtendimento().getAgendaGrade().getHoraFinal());
            }

            horariosDisponiveisDTO.setHoraFinal(dataFormatada);
            
            List<AgendaGradeAtendimentoHorario> conflitoHorarios = BOFactoryWicket.getBO(AgendamentoFacade.class)
                            .consultarConflitoHorariosAgendaPersonalizada(dto.getAgendaGradeAtendimento(), horariosDisponiveisDTO.getHoraInicial(), horariosDisponiveisDTO.getHoraFinal());

            if(CollectionUtils.isEmpty(conflitoHorarios)){
                if(CollectionUtils.isEmpty(horariosDisponiveisList)){
                    this.horaInicio = horariosDisponiveisDTO.getHoraInicial();
                }
                horariosDisponiveisList.add(horariosDisponiveisDTO);
                horaFinal = horariosDisponiveisDTO.getHoraFinal();
            }
        } while(DataUtil.compareHour(dataFormatada, dto.getAgendaGradeAtendimento().getAgendaGrade().getHoraFinal()) < 0);
        
        if(horaFinal == null){
            horaInicio = null;
        }
    }
}