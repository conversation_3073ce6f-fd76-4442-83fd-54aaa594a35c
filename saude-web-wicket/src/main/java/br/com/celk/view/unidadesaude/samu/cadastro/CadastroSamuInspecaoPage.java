package br.com.celk.view.unidadesaude.samu.cadastro;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.duracaofield.RequiredHoraMinutoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.treetable.pageable.PageableTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ICustomColumnTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ITreeColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.unidadesaude.samu.cadastro.dlg.DlgInspecaoDiaria;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.samu.SamuChecklist;
import br.com.ksisolucoes.vo.samu.SamuInspecao;
import br.com.ksisolucoes.vo.samu.SamuInspecaoItem;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroSamuInspecaoPage extends BasePage {

    private PageableTreeTable tblItens;
    private final SamuInspecaoTreeProvider provider = new SamuInspecaoTreeProvider(null);

    private SamuInspecao samuInspecao;
    private Profissional profissional;
    private SamuChecklist samuChecklist;
    private Date dataChecagem;
    private Date hora;

    private DlgInspecaoDiaria dlgInspecaoDiaria;

    DropDown dropDownCheckList;

    private boolean viewOnly;
    private AbstractAjaxButton btnVoltar;

    private Date dataAlteracao;
    private Date horaAlteracao;
    private String usuario;

    private WebMarkupContainer containerTabela;
    private boolean controle;

    public CadastroSamuInspecaoPage() {
        init();
    }

    public CadastroSamuInspecaoPage(SamuInspecao samuInspecao) {
        this.samuInspecao = samuInspecao;
        init();
    }

    public CadastroSamuInspecaoPage(SamuInspecao samuInspecao, boolean viewOnly) {
        this.samuInspecao = samuInspecao;
        this.viewOnly = viewOnly;
        init();
    }

    private void init() {
        carregarDados();
        Form form = new Form("form");

        AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));
        autoCompleteConsultaProfissional.setEnabled(samuInspecao == null);
        if (profissional == null) {
            profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        }

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                profissional = object;
                carregarCheckList();
                dropDownCheckList.setEnabled(true);
                target.add(dropDownCheckList);
                target.add(containerTabela);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                carregarCheckList();
                dropDownCheckList.setEnabled(false);
                dropDownCheckList.removeAllChoices(target);
                target.add(containerTabela);
                controle = false;
            }
        });

        form.add(getDropDownCheckList("samuChecklist").setEnabled(samuInspecao == null));
        dropDownCheckList.setEnabled(false);
        form.add(new RequiredDateChooser("dataChecagem", new PropertyModel(this, "dataChecagem")).setLabel(new Model<String>(WicketMethods.bundle("data"))));
        form.add(new RequiredHoraMinutoField("hora", new PropertyModel(this, "hora")));

        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar();
            }
        });

        form.add(btnVoltar = new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(ConsultaSamuInspecaoPage.class);
            }
        });
        btnVoltar.setDefaultFormProcessing(false);

        containerTabela = new WebMarkupContainer("containerTabela");
        containerTabela.add(tblItens = new PageableTreeTable("tblItens", getColumns(), provider, 10));
        tblItens.setOutputMarkupId(true);
        containerTabela.setOutputMarkupId(true);
        form.add(containerTabela);

        WebMarkupContainer containerHistorico = new WebMarkupContainer("containerHistorico");
        containerHistorico.setOutputMarkupId(true);
        containerHistorico.add(new InputField("usuario", new PropertyModel(this, "usuario")));
        containerHistorico.add(new DateChooser("dataAlteracao", new PropertyModel(this, "dataAlteracao")));
        containerHistorico.add(new HoraMinutoField("horaAlteracao", new PropertyModel(this, "horaAlteracao")));
        containerHistorico.setVisible(viewOnly);
        form.add(containerHistorico);
        if (viewOnly) {
            usuario = provider.getListSalvar().get(0).getUsuario().getNome();
            dataAlteracao = provider.getListSalvar().get(0).getDataUsuario();
            horaAlteracao = provider.getListSalvar().get(0).getDataUsuario();
        }

        add(form);
        enableFields(form);
        btnVoltar.setEnabled(true);
    }

    public DropDown getDropDownCheckList(String id) {
        dropDownCheckList = new DropDown(id, new PropertyModel(this, id));

        carregarCheckList();

        dropDownCheckList.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                SamuInspecaoDTOParam param = new SamuInspecaoDTOParam();
                if (samuChecklist != null) {
                    if (samuInspecao != null && samuInspecao.getCodigo() != null) {
                        param.setEdicao(true);
                        param.setSamuInspecao(samuInspecao);
                        samuChecklist = samuInspecao.getSamuChecklist();
                    }
                    param.setSamuChecklist(samuChecklist);
                    provider.recarregar(param);
                } else {
                    provider.recarregar(null);
                }
                target.add(containerTabela);
            }
        });
        return dropDownCheckList;
    }

    public void carregarCheckList() {
        if (profissional != null) {
            List<TabelaCbo> lstTabelaCbo = null;
            QueryConsultaProfissionalCargaHorariaDTOParam dtoParam = new QueryConsultaProfissionalCargaHorariaDTOParam();
            dtoParam.setProfissional(profissional);
            dtoParam.setEmpresa(SessaoAplicacaoImp.getInstance().getEmpresa());
            try {
                lstTabelaCbo = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaCbosProfissional(dtoParam);
            } catch (DAOException ex) {
                Logger.getLogger(CadastroSamuInspecaoPage.class.getName()).log(Level.SEVERE, null, ex);
            } catch (ValidacaoException ex) {
                Logger.getLogger(CadastroSamuInspecaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }

            List<SamuChecklist> lstCheck = new ArrayList();
            if (CollectionUtils.isNotNullEmpty(lstTabelaCbo)) {
                lstCheck = LoadManager.getInstance(SamuChecklist.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(SamuChecklist.PROP_TABELA_CBO, QueryCustom.QueryCustomParameter.IN, lstTabelaCbo))
                        .start().getList();
            }

            for (SamuChecklist check : lstCheck) {
                dropDownCheckList.addChoice(check, check.getDescricao());
            }
            if (!lstCheck.isEmpty()) {
                samuChecklist = lstCheck.get(0);
                dropDownCheckList.setComponentValue(lstCheck.get(0));
            }
            if (!lstCheck.isEmpty()) {
                SamuInspecaoDTOParam param = new SamuInspecaoDTOParam();
                if (samuInspecao != null && samuInspecao.getCodigo() != null) {
                    param.setEdicao(true);
                    param.setSamuInspecao(samuInspecao);
                    samuChecklist = samuInspecao.getSamuChecklist();
                }
                param.setSamuChecklist(samuChecklist);
                provider.recarregar(param);
            } else {
                provider.recarregar(null);
            }
        } else {
            provider.recarregar(null);
        }
    }

    public void salvar() throws DAOException, ValidacaoException {
        if (samuInspecao == null) {
            samuInspecao = new SamuInspecao();
            samuInspecao.setDataCadastro(DataUtil.getDataAtual());
            samuInspecao.setEmpresa(SessaoAplicacaoImp.getInstance().getEmpresa());
        }
        samuInspecao.setDataChecagem(DataUtil.mergeDataHora(dataChecagem, hora));
        samuInspecao.setSamuChecklist(samuChecklist);
        samuInspecao.setProfissional(profissional);

        if (samuInspecao.getDataChecagem().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(WicketMethods.bundle("dataChecagemMenorDataAtual"));
        }

        if (CollectionUtils.isAllEmpty(provider.getListSalvar())) {
            throw new ValidacaoException(WicketMethods.bundle("profissionalSamuCheckListNaoTemPermissao"));
        }

        SamuInspecao salvoSamuInspecao = BOFactoryWicket.getBO(BasicoFacade.class).salvarSamuInspecao(samuInspecao, provider.getListSalvar());
        Page page = new ConsultaSamuInspecaoPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, WicketMethods.bundle("registro_salvo_sucesso_codigo_X", salvoSamuInspecao.getCodigo()));

    }

    @Override
    public String getTituloPrograma() {
        return WicketMethods.bundle("cadastroInspecaoDiaria");
    }

    public List getColumns() {
        List<IColumn<SamuInspecaoTreeDTO, String>> result = new LinkedList();
        int headerIndex = 0;

        SamuInspecaoTreeDTO proxy = on(SamuInspecaoTreeDTO.class);

        result.add(new ITreeColumn(Model.of("")));
        result.add(getCustomActionColumn());
        result.add(new ICustomColumnTreeTable(path(proxy.getSamuInspecaoItem().getDescricao()), path(proxy.getSamuInspecaoItemLeaf().getDescricao()), headerIndex++, Model.of(""), true));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getSamuInspecaoItemLeaf().getUnidade().getDescricao()), headerIndex++, Model.of("")));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getSamuInspecaoItemLeaf().getQuantidade()), headerIndex++, Model.of("")));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getSamuInspecaoItemLeaf().getQuantidadeChecada()), headerIndex++, Model.of("")));
        result.add(new ICustomColumnTreeTable(null, path(proxy.getSamuInspecaoItemLeaf().getObservacao()), headerIndex++, Model.of("")));
        return result;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<SamuInspecaoTreeDTO>() {
            @Override
            public void customizeColumn(final SamuInspecaoTreeDTO rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        if (dlgInspecaoDiaria == null) {
                            addModal(target, dlgInspecaoDiaria = new DlgInspecaoDiaria(newModalId()) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target, SamuInspecaoItem item, Long quantidade, String observacao) {
                                    item.setQuantidadeChecada(quantidade);
                                    item.setObservacao(observacao);

                                    target.add(tblItens);
                                }
                            });
                        }
                        dlgInspecaoDiaria.show(target, rowObject.getSamuInspecaoItemLeaf());
                    }
                }).setVisible(rowObject.getLeaf() != null).setEnabled(!viewOnly);
            }
        };
    }

    public void carregarDados() {
        if (samuInspecao != null) {
            profissional = samuInspecao.getProfissional();
            samuChecklist = samuInspecao.getSamuChecklist();
            dataChecagem = samuInspecao.getDataChecagem();
            hora = samuInspecao.getDataChecagem();
        }
    }

    private void enableFields(MarkupContainer parent) {
        if (viewOnly) {
            Iterator<Component> iterator = parent.iterator();
            while (iterator.hasNext()) {
                Component next = iterator.next();
                next.setEnabled(!viewOnly);
            }
            containerTabela.setEnabled(true);
            tblItens.setEnabled(true);
        }
    }
}
