package br.com.celk.view.agenda.agendamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class PnlContatoAgendamentoListaEspera extends Panel {

    private CompoundPropertyModel<SolicitacaoAgendamento> model;
    private WebMarkupContainer container;
    private SelectionTable table;
    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> collectionProvider;
    private DlgLancarOcorrenciaContatoListaEspera dlgLancarOcorrencia;

    public PnlContatoAgendamentoListaEspera(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        model = new CompoundPropertyModel(new SolicitacaoAgendamento());

        container = new WebMarkupContainer("container", model);

        container.setOutputMarkupId(true);

        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);

        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getDescricaoSocialFormatado())));
        container.add(new DisabledInputField(path(proxy.getDataSolicitacao())));
        container.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricaoFormatado())));
        container.add(new DisabledInputField(path(proxy.getEmpresa().getDescricaoFormatado())));
        container.add(new DisabledInputField(path(proxy.getNomeProfissionalOrigem())));

        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefoneFormatado())));
        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone2Formatado())));
        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone3())));
        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getTelefone4())));
        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getCelular())));
        container.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getEmail())));

        container.add(table = new SelectionTable("tableOcorrencias", getColumns(), getCollectionProvider()));

        container.add(new AbstractAjaxButton("btnConfirmarContato") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmarContato(target);
            }
        });

        container.add(new AbstractAjaxButton("btnLancarOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                viewDlgLancarOcorrencia(target);
            }
        });

        add(container);
    }

    public void setModelObject(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        model.setObject(solicitacaoAgendamento);
        getCollectionProvider().setParameters(solicitacaoAgendamento);
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(container);
        table.populate(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        SolicitacaoAgendamentoOcorrencia proxy = on(SolicitacaoAgendamentoOcorrencia.class);
        columns.add(createColumn(bundle("data_hora"), proxy.getDataHora()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoTipoOcorrencia()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                    SolicitacaoAgendamentoOcorrencia proxy = on(SolicitacaoAgendamentoOcorrencia.class);
                    return LoadManager.getInstance(SolicitacaoAgendamentoOcorrencia.class)
                            .addProperty(path(proxy.getDataOcorrencia()))
                            .addProperty(path(proxy.getUsuario().getNome()))
                            .addProperty(path(proxy.getDescricao()))
                            .addProperty(path(proxy.getTipoOcorrencia()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento()), solicitacaoAgendamento))
                            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataOcorrencia()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .start().getList();
                }
            };
        }

        return this.collectionProvider;
    }

    private void viewDlgLancarOcorrencia(AjaxRequestTarget target) {
        if (dlgLancarOcorrencia == null) {
            WindowUtil.addModal(target, this, dlgLancarOcorrencia = new DlgLancarOcorrenciaContatoListaEspera(WindowUtil.newModalId(this)) {
                @Override
                public void onSalvar(AjaxRequestTarget target, String ocorrencia, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                    salvarOcorrencia(target, ocorrencia);
                }
            });
        }

        dlgLancarOcorrencia.show(target);
    }

    private void salvarOcorrencia(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AgendamentoFacade.class)
                .gerarOcorrenciaSolicitacaoAgendamentoContato(
                        SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO,
                        Bundle.getStringApplication("rotulo_contato_X", ocorrencia),
                        model.getObject()
                );
        depoisSalvarOcorrencia(target);
    }

    private void confirmarContato(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(AgendamentoFacade.class).confirmarContatoSolicitacaoAgendamento(model.getObject().getCodigo(),  " Usuário: "+ SessaoAplicacaoImp.getInstance().getUsuario().getNome()+ " Data: "+ Data.formatarDataHora(DataUtil.getDataAtual()));
        depoisConfirmarContato(target);
    }

    public void depoisConfirmarContato(AjaxRequestTarget target) {
    }

    public void depoisSalvarOcorrencia(AjaxRequestTarget target) {
    }
}
