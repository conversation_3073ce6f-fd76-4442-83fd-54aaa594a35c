package br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.factory.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.agenda.agendamento.tfd.consultaprocesso.DetalhesProcessoTfdPage;
import br.com.celk.view.agenda.agendamento.tfd.consultaprocesso.dlg.DlgOcorrenciaLaudo;
import br.com.celk.view.agenda.solicitacao.DetalhesSolicitacaoPage;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoExamesPacienteDTO;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoExamesPacienteDTOParam;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.tablerow.HistoricoPacienteAgendamentosRow;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class DetalhamentoHistoricoPaciente extends BasePage {

    private UsuarioCadsus usuarioCadsus;
    private WebMarkupContainer containerAtendimento;
    private WebMarkupContainer containerExames;
    private WebMarkupContainer containerMedicamento;
    private WebMarkupContainer containerDoenca;
    private WebMarkupContainer containerAgendamento;
    private WebMarkupContainer containerTFD;
    private WebMarkupContainer containerMedicamentosRecebidos;
    private WebMarkupContainer containerViagens;
    private WebMarkupContainer containerAtividadeGrupoPaciente;
    private WebMarkupContainer containerEncaminhamentoEspecialista;
    private WebMarkupContainer containerLaudoApac;
    private WebMarkupContainer containerExameBpai;
    private WebMarkupContainer containerSolicitacaoAgendamento;
    private WebMarkupContainer containerVacinaAplicada;

    private List<HistoricoPacienteDTO> atendimentoList;
    private Table<Atendimento> tblAtendimentos;
    private List<HistoricoExamesPacienteDTO> examesList;
    private Table<ExameRequisicao> tblExames;
    private List<HistoricoPacienteDTO> medicamentoList;
    private Table<MedicamentoPaciente> tblMedicamentos;
    private List<HistoricoPacienteDTO> doencaList;
    private Table<UsuarioCadsusDoenca> tblDoencas;
    private List<HistoricoPacienteDTO> agendamentoList;
    private Table<AgendaGradeAtendimentoHorario> tblAgendamentos;
    private List<HistoricoPacienteDTO> TFDList;
    private Table<LaudoTfd> tblTFD;
    private List<HistoricoPacienteDTO> medicamentosRecebidosList;
    private Table<DispensacaoMedicamentoItem> tblMedicamentosRecebidos;
    private List<HistoricoPacienteDTO> viagensList;
    private Table<DispensacaoMedicamentoItem> tblViagens;
    private Form<HistoricoPacienteDTO> form;
    private List<HistoricoPacienteDTO> atividadeGrupoPacienteList;
    private Table<AtividadeGrupoPaciente> tblAtividadeGrupoPaciente;
    private List<HistoricoPacienteDTO> encaminhamentoEspecialistaList;
    private Table<EncaminhamentoConsulta> tblEncaminhamentoEspecialista;
    private Table<ExameApac> tblLaudoApac;
    private List<HistoricoPacienteDTO> laudoApacList;
    private Table<ExameBpai> tblExameBpai;
    private List<HistoricoPacienteDTO> exameBpaiList;
    private Table<SolicitacaoAgendamento> tblSolicitacaoAgendamento;
    private List<HistoricoPacienteDTO> solicitacaoAgendamentoList;
    private Table<VacinaAplicacao> tblVacinaAplicada;
    private List<HistoricoPacienteDTO> vacinaAplicadaList;

    public DetalhamentoHistoricoPaciente(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        init();
    }

    public void init() {
        form = new Form<HistoricoPacienteDTO>("form", new CompoundPropertyModel(new HistoricoPacienteDTO()));
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        carregarUsuarioCadsus();

        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getNomeSocial())).setEnabled(false));
        form.add(ImagemAvatarHelper.carregarAvatar(usuarioCadsus));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getIdade())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getSexoFormatado())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getNomeMae())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoDomicilio().getEquipeMicroArea().getEquipeArea().getDescricao())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoDomicilio().getEquipeMicroArea().getMicroArea())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoDomicilio().getNumeroFamilia())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoDomicilio().getEquipeMicroArea().getEquipeProfissional().getProfissional().getNome())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoDomicilio().getEquipeMicroArea().getEquipeProfissional().getEquipe().getEmpresa().getDescricao())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getDescricaoSituacao())).setEnabled(false));
        form.add(new InputField<String>(path(proxy.getUsuarioCadsus().getDescricaoMotivoExclusao())).setEnabled(false));

        containerAtividadeGrupoPaciente = new WebMarkupContainer("containerAtividadeGrupoPaciente");
        containerAtividadeGrupoPaciente.add(tblAtividadeGrupoPaciente = new Table("tblAtividadeGrupoPaciente", getColumnsAtividadeGrupoPaciente(), getCollectionProviderAtividadeGrupoPaciente()));
        tblAtividadeGrupoPaciente.populate();
        tblAtividadeGrupoPaciente.setScrollY("150px");

        containerAtendimento = new WebMarkupContainer("containerAtendimento");
        containerAtendimento.add(tblAtendimentos = new Table("tblAtendimentos", getColumnsAtendimento(), getCollectionProviderAtendimento()));
        tblAtendimentos.populate();
        tblAtendimentos.setScrollY("150px");
        containerAtendimento.add(
                DropDownUtil.getIEnumDropDown(path(proxy.getPagina()), HistoricoPacienteDTO.Paginas.values(), false)
                        .add(new AjaxFormComponentUpdatingBehavior("onchange") {
                            @Override
                            protected void onUpdate(AjaxRequestTarget art) {
                                getCollectionProviderAtendimento();
                                tblAtendimentos.update(art);
                            }
                        })
        );

        containerEncaminhamentoEspecialista = new WebMarkupContainer("containerEncaminhamentoEspecialista");
        containerEncaminhamentoEspecialista.add(tblEncaminhamentoEspecialista = new Table("tblEncaminhamentoEspecialista", getColumnsEncaminhamentoEspecialista(), getCollectionProviderEncaminhamentoEspecialista()));
        tblEncaminhamentoEspecialista.populate();
        tblEncaminhamentoEspecialista.setScrollY("150px");

        containerExames = new WebMarkupContainer("containerExames");
        containerExames.add(tblExames = new Table("tblExames", getColumnsExames(), getCollectionProviderExame()));
        tblExames.populate();
        tblExames.setScrollY("150px");

        containerMedicamentosRecebidos = new WebMarkupContainer("containerMedicamentosRecebidos");
        containerMedicamentosRecebidos.add(tblMedicamentosRecebidos = new Table("tblMedicamentosRecebidos", getColumnsMedicamentosRecebidos(), getCollectionProviderMedicamentosRecebidos()));
        tblMedicamentosRecebidos.populate();
        tblMedicamentosRecebidos.setScrollY("150px");

        containerMedicamento = new WebMarkupContainer("containerMedicamento");
        containerMedicamento.add(tblMedicamentos = new Table("tblMedicamentos", getColumnsMedicamentos(), getCollectionProviderMedicamentos()));
        tblMedicamentos.populate();
        tblMedicamentos.setScrollY("150px");

        containerDoenca = new WebMarkupContainer("containerDoenca");
        containerDoenca.add(tblDoencas = new Table("tblDoencas", getColumnsDoenca(), getCollectionProviderDoenca()));
        tblDoencas.populate();
        tblDoencas.setScrollY("150px");

        containerAgendamento = new WebMarkupContainer("containerAgendamento");
        containerAgendamento.add(tblAgendamentos = new Table("tblAgendamentos", getColumnsAgendamento(), getCollectionProviderAgendamento()) {
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new HistoricoPacienteAgendamentosRow(id, index, model);
            }
        });
        tblAgendamentos.populate();
        tblAgendamentos.setScrollY("150px");
        containerAgendamento.add(DropDownUtil.getIEnumDropDown(path(proxy.getStatusAgendamento()), HistoricoPacienteDTO.StatusAgendamento.values(), true, bundle("todos"), false)
                .add(new AjaxFormComponentUpdatingBehavior("onchange") {

                    @Override
                    protected void onUpdate(AjaxRequestTarget art) {
                        getCollectionProviderAgendamento();
                        tblAgendamentos.update(art);
                    }
                })
        );

        containerAgendamento.add(new DisabledInputField(path(proxy.getPercentualAbsenteismoFormatado())));

        containerLaudoApac = new WebMarkupContainer("containerLaudoApac");
        containerLaudoApac.add(tblLaudoApac = new Table("tblLaudoApac", getColumnsLaudoApac(), getCollectionProviderLaudoApac()));
        tblLaudoApac.populate();
        tblLaudoApac.setScrollY("150px");

        containerExameBpai = new WebMarkupContainer("containerExameBpai");
        containerExameBpai.add(tblExameBpai = new Table("tblExameBpai", getColumnsExameBpai(), getCollectionProviderExameBpai()));
        tblExameBpai.populate();
        tblExameBpai.setScrollY("150px");

        containerTFD = new WebMarkupContainer("containerTFD");
        containerTFD.add(tblTFD = new Table("tblTFD", getColumnsTFD(), getCollectionProviderTFD()));
        tblTFD.populate();
        tblTFD.setScrollY("150px");
        containerTFD.add(DropDownUtil.getIEnumDropDown(path(proxy.getTfdOP()), HistoricoPacienteDTO.TipoTFD.values(), false)
                .add(new AjaxFormComponentUpdatingBehavior("onchange") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget art) {
                        getCollectionProviderTFD();
                        tblTFD.update(art);
                    }
                })
        );

        containerSolicitacaoAgendamento = new WebMarkupContainer("containerSolicitacaoAgendamento");
        containerSolicitacaoAgendamento.add(tblSolicitacaoAgendamento = new Table("tblSolicitacaoAgendamento", getColumnsSolicitacaoAgendamento(), getCollectionProviderSolicitacaoAgendamento()));
        tblSolicitacaoAgendamento.populate();
        tblSolicitacaoAgendamento.setScrollY("150px");

        containerVacinaAplicada = new WebMarkupContainer("containerVacinaAplicada");
        containerVacinaAplicada.add(tblVacinaAplicada = new Table("tblVacinaAplicada", getColumnsVacinaAplicada(), getCollectionProviderVacinaAplicada()));
        tblVacinaAplicada.populate();
        tblVacinaAplicada.setScrollY("150px");

        containerViagens = new WebMarkupContainer("containerViagens");
        containerViagens.add(tblViagens = new Table("tblViagens", getColumnsViagens(), getCollectionProviderViagens()));
        tblViagens.populate();
        tblViagens.setScrollY("150px");

        form.add(new VoltarButton("btnVoltar"));
        form.add(containerAtividadeGrupoPaciente);
        form.add(containerAtendimento);
        form.add(containerEncaminhamentoEspecialista);
        form.add(containerExames);
        form.add(containerAgendamento);
        form.add(containerLaudoApac);
        form.add(containerExameBpai);
        form.add(containerDoenca);
        form.add(containerMedicamento);
        form.add(containerTFD);
        form.add(containerSolicitacaoAgendamento);
        form.add(containerMedicamentosRecebidos);
        form.add(containerVacinaAplicada);
        form.add(containerViagens);
        add(form);
    }

    private void carregarUsuarioCadsus() {
        UsuarioCadsus uc = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperties(new HQLProperties(UsuarioCadsus.class).getProperties())
                .addProperties(new HQLProperties(EnderecoDomicilio.class, UsuarioCadsus.PROP_ENDERECO_DOMICILIO).getProperties())
                .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS).getProperties())
                .addProperties(new HQLProperties(EquipeMicroArea.class, VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA)).getProperties())
                .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA)).getProperties())
                .addProperties(new HQLProperties(EquipeProfissional.class, VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL)).getProperties())
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, usuarioCadsus.getCodigo()))
                .start().getVO();
        form.getModel().getObject().setUsuarioCadsus(uc);
    }

    private List<IColumn> getColumnsAtividadeGrupoPaciente() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createColumn(bundle("data"), proxy.getAtividadeGrupoPaciente().getAtividadeGrupo().getDataInicio()));
        columns.add(createColumn(bundle("tipoAtividade"), proxy.getAtividadeGrupoPaciente().getAtividadeGrupo().getTipoAtividadeGrupo().getDescricaoVO()));
        columns.add(createColumn(bundle("local"), proxy.getAtividadeGrupoPaciente().getAtividadeGrupo().getLocalAtividadeGrupo().getDescricaoVO()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderAtividadeGrupoPaciente() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {

                List<AtividadeGrupoPaciente> listaAtividadeGrupoPaciente = LoadManager.getInstance(AtividadeGrupoPaciente.class)
                        .addProperty(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_DATA_INICIO))
                        .addProperty(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_LOCAL_ATIVIDADE_GRUPO, LocalAtividadeGrupo.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_TIPO_ATIVIDADE_GRUPO, TipoAtividadeGrupo.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(AtividadeGrupoPaciente.PROP_USUARIO_CADSUS, usuarioCadsus))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtividadeGrupoPaciente.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_DATA_CADASTRO), "desc"))
                        .start().getList();

                atividadeGrupoPacienteList = new ArrayList<>();
                for (AtividadeGrupoPaciente atividadeGrupoPaciente : listaAtividadeGrupoPaciente) {
                    HistoricoPacienteDTO dto = new HistoricoPacienteDTO();
                    dto.setAtividadeGrupoPaciente(atividadeGrupoPaciente);
                    atividadeGrupoPacienteList.add(dto);
                }
                return atividadeGrupoPacienteList;
            }
        };
    }

    private List<IColumn> getColumnsAtendimento() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getAtendimento().getDataAtendimento()), path(proxy.getAtendimento().getDataAtendimento())));
        columns.add(createSortableColumn(bundle("unidade"), proxy.getAtendimento().getEmpresa().getDescricao(), proxy.getAtendimento().getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getAtendimento().getProfissional().getNome(), proxy.getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoTipoAtendimento()));
        columns.add(createColumn(bundle("tempo_espera"), proxy.getTempoAtendimento()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderAtendimento() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                param.setUsuarioCadsus(usuarioCadsus);
                param.setPagina(form.getModel().getObject().getPagina());
                atendimentoList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryConsultaAtendimentosPaciente(param);
                return atendimentoList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("atendimento.dataAtendimento", false);
            }
        };
    }

    private List<IColumn> getColumnsEncaminhamentoEspecialista() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);
        ColumnFactory columnFactory = new ColumnFactory(HistoricoPacienteDTO.class);



        columns.add(createColumn(bundle("data"), proxy.getEncaminhamentoConsulta().getEncaminhamento().getDataCadastro()));
        columns.add(createColumn(bundle("encaminhamento"), proxy.getEncaminhamentoConsulta().getEncaminhamento().getTipoEncaminhamento().getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getEncaminhamentoConsulta().getEncaminhamento().getTipoEncaminhamento().getSituacaoFormatado()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderEncaminhamentoEspecialista() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {

                List<EncaminhamentoConsulta> listaEncaminhamentoEspecialista = LoadManager.getInstance(EncaminhamentoConsulta.class)
                        .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_DATA_CADASTRO))
                        .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_SITUACAO))
                        .addProperty(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_USUARIO_CADSUS), usuarioCadsus))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_FLAG_ODONTOLOGICO), RepositoryComponentDefault.NAO_LONG))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EncaminhamentoConsulta.PROP_ENCAMINHAMENTO, Encaminhamento.PROP_DATA_CADASTRO), "desc"))
                        .start().getList();

                encaminhamentoEspecialistaList = new ArrayList<>();
                for (EncaminhamentoConsulta encaminhamentoConsulta : listaEncaminhamentoEspecialista) {
                    HistoricoPacienteDTO dto = new HistoricoPacienteDTO();
                    dto.setEncaminhamentoConsulta(encaminhamentoConsulta);
                    encaminhamentoEspecialistaList.add(dto);
                }
                return encaminhamentoEspecialistaList;
            }
        };
    }

    private List<IColumn> getColumnsExames() {
        List<IColumn> columns = new ArrayList();
        HistoricoExamesPacienteDTO proxy = on(HistoricoExamesPacienteDTO.class);

        columns.add(new DateColumn(bundle("data"), path(proxy.getData())));
        columns.add(new DateColumn(bundle("autorizacao"), path(proxy.getDataAutorizacao())));
        columns.add(createColumn(bundle("solicitante"), proxy.getSolicitante()));
        columns.add(createColumn(bundle("tipo"), proxy.getTipoExame()));
        columns.add(createColumn(bundle("exame"), proxy.getExame()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacao()));
        return columns;
    }

    private ICollectionProvider getCollectionProviderExame() {
        return new CollectionProvider<HistoricoExamesPacienteDTO, HistoricoExamesPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoExamesPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoExamesPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                examesList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).consultarExamesPaciente(param);
                return examesList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("atendimento.dataAtendimento", false);
            }
        };
    }

    private List<IColumn> getColumnsMedicamentosRecebidos() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createSortableColumn(bundle("medicamento"), proxy.getDispensacaoMedicamentoItem().getProduto().getDescricao(), proxy.getDispensacaoMedicamentoItem().getProduto().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("un"), proxy.getDispensacaoMedicamentoItem().getProduto().getUnidade().getUnidade()));
        columns.add(new DoubleColumn(bundle("quantidadeTotalAbv"), path(proxy.getQuantidadeTotalDispensada())).setCasasDecimais(0));
        columns.add(new DoubleColumn(bundle("quantidadeUltimaDispensacaoAbv"), path(proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada())).setCasasDecimais(0));
        columns.add(new DateTimeColumn(bundle("ultimaDispencacao"), path(proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getDataUltimaDispensacao())));
        columns.add(createSortableColumn(bundle("localDispensacao"), proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getEmpresa().getDescricao()));
        columns.add(new DateColumn(bundle("dataProximaDispensacao"), path(proxy.getDispensacaoMedicamentoItem().getDataProximaDispensacaoCalculada())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderMedicamentosRecebidos() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                param.setUsuarioCadsus(usuarioCadsus);
                param.setPagina(form.getModel().getObject().getPagina());
                medicamentosRecebidosList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryConsultaHistoricoMedicamentosRecebidos(param);
                return medicamentosRecebidosList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("dispensacaoMedicamentoItem.dataProximaDispensacao", false);
            }
        };
    }

    private List<IColumn> getColumnsVacinaAplicada() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createSortableColumn(bundle("dataAplicacao"), proxy.getVacinaAplicacao().getDataAplicacao()));
        columns.add(createColumn(bundle("vacinaAplicada"), proxy.getVacinaAplicacao().getDescricaoVacina()));
        columns.add(createColumn(bundle("dose"), proxy.getVacinaAplicacao().getDose()));
        columns.add(createColumn(bundle("estrategia"), proxy.getVacinaAplicacao().getEstrategia().getDescricaoVO()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderVacinaAplicada() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {

                List<VacinaAplicacao> listaVacinaAplicacao = LoadManager.getInstance(VacinaAplicacao.class)
                        .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_DOSE))
                        .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_DATA_APLICACAO))
                        .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_ESTRATEGIA, Calendario.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_TIPO_VACINA, TipoVacina.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_DESCRICAO_VACINA))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS), usuarioCadsus))
                        .addParameter(new QueryCustom.QueryCustomParameter(VacinaAplicacao.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE , VacinaAplicacao.StatusVacinaAplicacao.CANCELADA.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(VacinaAplicacao.PROP_DATA_APLICACAO), "desc"))
                        .start().getList();

                vacinaAplicadaList  = new ArrayList<>();
                for (VacinaAplicacao vacinaAplicacao : listaVacinaAplicacao) {
                    HistoricoPacienteDTO dto = new HistoricoPacienteDTO();
                    dto.setVacinaAplicacao(vacinaAplicacao);
                    vacinaAplicadaList.add(dto);
                }
                return vacinaAplicadaList;
            }
        };
    }

    private List<IColumn> getColumnsViagens() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(new DateColumn(bundle("dataSaida"), path(proxy.getRoteiroViagemPassageiro().getRoteiro().getDataSaida())));
        columns.add(createSortableColumn(bundle("destino"), proxy.getRoteiroViagemPassageiro().getDestino()));
        columns.add(createSortableColumn(bundle("motorista"), proxy.getRoteiroViagemPassageiro().getRoteiro().getMotorista().getNome()));
        columns.add(createSortableColumn(bundle("veiculo"), proxy.getRoteiroViagemPassageiro().getRoteiro().getVeiculo().getDescricao()));
        columns.add(createColumn(bundle("tipoProcedimento"), proxy.getDescricaoTipoProcedimentoViagem()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderViagens() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                param.setUsuarioCadsus(usuarioCadsus);
                param.setPagina(form.getModel().getObject().getPagina());
                viagensList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryConsultaHistoricoViagens(param);
                return viagensList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("roteiroViagemPassageiro.roteiro.dataSaida", false);
            }
        };
    }

    private List<IColumn> getColumnsMedicamentos() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createColumn(bundle("medicamento"), proxy.getMedicamentoPaciente().getNomeProduto()));
        columns.add(createColumn(bundle("posologia"), proxy.getMedicamentoPaciente().getPosologia()));
        columns.add(createColumn(bundle("tipoReceita"), proxy.getMedicamentoPaciente().getTipoReceita().getDescricao()));
        columns.add(new DateColumn(bundle("ultimaReceita"), path(proxy.getUltimaReceita()), path(proxy.getUltimaReceita())));
        columns.add(new DateColumn(bundle("ultimaDispencacao"), path(proxy.getUltimaDispencacao()), path(proxy.getUltimaDispencacao())));
        columns.add(createColumn(bundle("localDispencacao"), proxy.getLocalDispencacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderMedicamentos() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                medicamentoList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryConsultaHistoricoMedicamentosPaciente(param);
                return medicamentoList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("medicamentoPaciente.nomeProduto", true);
            }

        };
    }

    private List<IColumn> getColumnsDoenca() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createSortableColumn(bundle("descricao"), proxy.getDoenca(), proxy.getDoenca()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderDoenca() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                doencaList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryDoencaHistorico(param);
                return doencaList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("doenca.descricao", true);
            }

        };
    }

    private List<IColumn> getColumnsAgendamento() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(getCustomActionColumnAgendamento());
        columns.add(new DateTimeColumn(bundle("dataAgendamento"), path(proxy.getAgendaGradeAtendimentoHorario().getDataAgendamento())));
        columns.add(createSortableColumn(bundle("tipoProcedimento"), proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getTipoProcedimento().getDescricao(), proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(new DateColumn(bundle("dataSolicitacao"), path(proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao()), path(proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao())));
        columns.add(createSortableColumn(bundle("estabelecimentoAgendado"), proxy.getAgendaGradeAtendimentoHorario().getLocalAgendamento().getDescricao()));
        columns.add(createSortableColumn(bundle("prioridade"), proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getPrioridade(), proxy.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDescricaoPrioridade()));
        columns.add(createColumn(bundle("situacao"), proxy.getAgendaGradeAtendimentoHorario().getSituacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderAgendamento() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                param.setStatusAgendamento(form.getModel().getObject().getStatusAgendamento());
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                agendamentoList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryAgendamentoHistorico(param);
                calcularPercentualAbsenteismo();
                return agendamentoList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("agendaGradeAtendimentoHorario.solicitacaoAgendamento.status", false);
            }

        };
    }

    private List<IColumn> getColumnsLaudoApac() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createColumn(bundle("data"), proxy.getExameApac().getExame().getDataCadastro()));
        columns.add(createColumn(bundle("exame"), proxy.getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getExameApac().getExame().getTipoExame().getDescricao()));

        columns.add(createColumn(bundle("situacao"), proxy.getExameRequisicao().getExame().getDescricaoStatus()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderLaudoApac() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {

                laudoApacList = new ArrayList<>();
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                laudoApacList = BOFactoryWicket.getBO(ExameFacade.class).consultaLaudoApac(param);
                return laudoApacList;
            }
        };
    }

    private List<IColumn> getColumnsExameBpai() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createColumn(bundle("data"), proxy.getExameBpai().getExame().getDataCadastro()));
        columns.add(createColumn(bundle("exame"), proxy.getExameRequisicao().getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getExameBpai().getExame().getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getExameBpai().getDescricaoStatus()));
        return columns;
    }

    private ICollectionProvider getCollectionProviderExameBpai() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {

                exameBpaiList = new ArrayList<>();
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                exameBpaiList = BOFactoryWicket.getBO(ExameFacade.class).consultaLaudoBpai(param);
                return exameBpaiList;
            }
        };
    }

    private IColumn getCustomActionColumnAgendamento() {
        return new MultipleActionCustomColumn<HistoricoPacienteDTO>() {
            @Override
            public void customizeColumn(final HistoricoPacienteDTO rowObject) {
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesSolicitacaoPage(rowObject.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getCodigo()));
                    }
                }).setTitleBundleKey(bundle("consultaSolicitacao")).setEnabled(rowObject.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getCodigo() != null);
            }
        };
    }

    private List<IColumn> getColumnsTFD() {
        List<IColumn> columns = new ArrayList();

        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);
        columns.add(getCustomActionColumnTFD());
        columns.add(createSortableColumn(bundle("numeroPedido"), proxy.getLaudoTfd().getPedidoTfd().getNumeroPedido()));
        columns.add(new DateColumn(bundle("dataLaudo"), path(proxy.getLaudoTfd().getDataLaudo()), path(proxy.getLaudoTfd().getDataLaudo())));
        columns.add(createSortableColumn(bundle("tipoProcedimento"), proxy.getLaudoTfd().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getLaudoTfd().getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getLaudoTfd().getStatus(), proxy.getLaudoTfd().getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("parecer"), proxy.getLaudoTfd().getPedidoTfd().getParecer(), proxy.getLaudoTfd().getPedidoTfd().getDescricaoParecer()));
        return columns;
    }

    private ICollectionProvider getCollectionProviderTFD() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {
                param = new HistoricoPacienteDTOParam();
                param.setUsuarioCadsus(usuarioCadsus);
                param.setTfdOP(form.getModel().getObject().getTfdOP());
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
                TFDList = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).queryConsultaHistoricoTFD(param);
                return TFDList;
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>("laudoTfd.status", true);
            }

        };
    }

    private IColumn getCustomActionColumnTFD() {
        return new MultipleActionCustomColumn<HistoricoPacienteDTO>() {
            @Override
            public void customizeColumn(final HistoricoPacienteDTO rowObject) {
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesProcessoTfdPage(rowObject.getLaudoTfd().getCodigo()));
                    }
                });
                addAction(ActionType.OCORRENCIA, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        viewDlgOcorrenciaLaudo(target, rowObject);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnsSolicitacaoAgendamento() {
        List<IColumn> columns = new ArrayList();
        HistoricoPacienteDTO proxy = on(HistoricoPacienteDTO.class);

        columns.add(createSortableColumn(bundle("data"), proxy.getSolicitacaoAgendamento().getDataSolicitacao()));
        columns.add(createColumn(bundle("tipoProcedimento"), proxy.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(createColumn(bundle("prioridade"), proxy.getSolicitacaoAgendamento().getDescricaoPrioridade()));
        columns.add(createColumn(bundle("situacao"), proxy.getSolicitacaoAgendamento().getDescricaoSituacao()));


        return columns;
    }

    private ICollectionProvider getCollectionProviderSolicitacaoAgendamento() {
        return new CollectionProvider<HistoricoPacienteDTO, HistoricoPacienteDTOParam>() {
            @Override
            public Collection getCollection(HistoricoPacienteDTOParam param) throws DAOException, ValidacaoException {


                List<SolicitacaoAgendamento> listaSolicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_STATUS))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_PRIORIDADE))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_DATA_CADASTRO))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_DATA_SOLICITACAO))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS), usuarioCadsus))
                        .addSorter(new QueryCustom.QueryCustomSorter(SolicitacaoAgendamento.PROP_DATA_CADASTRO, "desc"))
                        .start().getList();


                solicitacaoAgendamentoList = new ArrayList<>();
                for (SolicitacaoAgendamento solicitacaoAgendamento : listaSolicitacaoAgendamento) {
                    HistoricoPacienteDTO dto = new HistoricoPacienteDTO();
                    dto.setSolicitacaoAgendamento(solicitacaoAgendamento);
                    solicitacaoAgendamentoList.add(dto);
                }
                return solicitacaoAgendamentoList;
            }
        };
    }

    private DlgOcorrenciaLaudo dlgOcorrenciaLaudo;

    private void viewDlgOcorrenciaLaudo(AjaxRequestTarget target, HistoricoPacienteDTO dto) {
        if (dlgOcorrenciaLaudo == null) {
            addModal(target, dlgOcorrenciaLaudo = new DlgOcorrenciaLaudo(newModalId()));
        }
        dlgOcorrenciaLaudo.show(target, dto.getLaudoTfd().getCodigo());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("historicoPaciente");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (CollectionUtils.isNotNullEmpty(atividadeGrupoPacienteList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerAtividadeGrupoPaciente)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(agendamentoList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerAgendamento)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(laudoApacList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerLaudoApac)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(exameBpaiList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExameBpai)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(atendimentoList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerAtendimento)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(doencaList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerDoenca)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(medicamentoList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerMedicamento)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(TFDList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerTFD)));
        }
        if (CollectionUtils.isNotNullEmpty(solicitacaoAgendamentoList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerSolicitacaoAgendamento)));
        }
        if (CollectionUtils.isNotNullEmpty(medicamentosRecebidosList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerMedicamentosRecebidos)));
        }
        if (CollectionUtils.isNotNullEmpty(vacinaAplicadaList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerVacinaAplicada)));
        }
        if (CollectionUtils.isNotNullEmpty(viagensList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerViagens)));
        }
        if (CollectionUtils.isNotNullEmpty(examesList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExames)));
        } else {
        }
        if (CollectionUtils.isNotNullEmpty(encaminhamentoEspecialistaList)) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerEncaminhamentoEspecialista)));
        } else {
        }
    }

    private void calcularPercentualAbsenteismo() {
        if (CollectionUtils.isNotNullEmpty(agendamentoList)) {
            int naoCompareceu = 0;
            for (HistoricoPacienteDTO agendamento : agendamentoList) {
                if (AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agendamento.getAgendaGradeAtendimentoHorario().getStatus())) {
                    naoCompareceu++;
                }
            }

            if (naoCompareceu > 0) {
                Long multiplicao = new Dinheiro(100L).multiplicar(Coalesce.asInteger(naoCompareceu)).longValue();
                Double percentualAbsenteismo = new Dinheiro(Coalesce.asLong(multiplicao)).dividir(Coalesce.asInteger(agendamentoList.size())).doubleValue();

                form.getModel().getObject().setPercentualAbsenteismo(percentualAbsenteismo);
            }
        }
    }
}
