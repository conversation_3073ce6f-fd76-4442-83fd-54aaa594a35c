package br.com.celk.view.hospital.convenio;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.prontuario.tipotabelaprocedimento.autocomplete.AutoCompleteConsultaTipoTabelaProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import java.io.File;
import java.io.IOException;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroConvenioStep1 extends BasePage {

    private Convenio convenio;
    private CompoundPropertyModel<Convenio> model;
    private RequiredInputField txtDescricao;
    private DropDown<Long> dropDownSubConvenio;

    private FileUpload upload;
    private FileUploadField fileUploadField;
    private Label lblAnexo;
    private List<FileUpload> lstUpload;
    private MensagemAnexoDTO logo;
    private String rotulo;

    public CadastroConvenioStep1(Convenio convenio) {
        this.convenio = convenio;
        init();
    }

    public CadastroConvenioStep1() {
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel<Convenio>(new LoadableObjectModel<Convenio>(Convenio.class)));
        this.model.setObject(convenio == null ? convenio = new Convenio() : convenio);

        form.add(txtDescricao = new RequiredInputField<String>(Convenio.PROP_DESCRICAO));
        form.add(getDropDownSubConvenio());
        form.add(DropDownUtil.getIEnumDropDown(Convenio.PROP_VALIDACAO_NUMERO_CONVENIO, Convenio.ValidacaoNumeroConvenio.values()));
        form.add(DropDownUtil.getSimNaoLongDropDown(Convenio.PROP_FLAG_NUMERO_CONVENIO_OBRIGATORIO));
        form.add(new AutoCompleteConsultaTipoTabelaProcedimento(Convenio.PROP_TIPO_TABELA_PROCEDIMENTO, true));
        form.add(new InputField(Convenio.PROP_REGISTRO_ANS_DESTINO));
        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar();
            }
        }));
        initLogo(form);

        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroConvenio");
    }

    private DropDown<Long> getDropDownSubConvenio() {
        if (dropDownSubConvenio == null) {
            dropDownSubConvenio = new RequiredDropDown<Long>(Convenio.PROP_SUBCONVENIO);
            dropDownSubConvenio.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
            dropDownSubConvenio.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));
        }
        return dropDownSubConvenio;
    }

    private void avancar() throws ValidacaoException, DAOException {
        List<Convenio> _descricaoConvenio = LoadManager.getInstance(Convenio.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Convenio.PROP_DESCRICAO), this.txtDescricao.getValue()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Convenio.PROP_SUBCONVENIO), this.model.getObject().getSubconvenio()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Convenio.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, this.model.getObject().getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(_descricaoConvenio)) {
            throw new ValidacaoException(BundleManager.getString("jaExisteDescricaoConvenio", this.txtDescricao.getValue()));
        }

        if (this.model.getObject().getSubconvenio().equals(RepositoryComponentDefault.SIM_LONG)) {
            setResponsePage(new CadastroConvenioStep2(this.model.getObject(), logo));
        } else {
            setResponsePage(new CadastroConvenioStep3(this.model.getObject(), logo));
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    public void initLogo(Form form) {
        carregarImagemConvenio();
        form.add(fileUploadField = new FileUploadField("upload", new PropertyModel<List<FileUpload>>(this, "lstUpload")));
        form.add(lblAnexo = new Label("rotulo", new PropertyModel(this, "rotulo")));
        lblAnexo.setOutputMarkupId(true);
        form.add(new SubmitButton("btnAnexar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                upload = fileUploadField.getFileUpload();
                if (upload != null) {
                    try {
                        if (upload.getSize() < 200000L) {
                            if (upload.getClientFileName().endsWith(".jpg") || upload.getClientFileName().endsWith(".png")) {
                                rotulo = upload.getClientFileName();
                                target.add(lblAnexo);
                                File newFile = File.createTempFile("anexo", upload.getClientFileName());
                                upload.writeTo(newFile);
                                if (logo == null) {
                                    logo = new MensagemAnexoDTO();
                                }
                                logo.setNomeArquivoOriginal(upload.getClientFileName());
                                logo.setNomeArquivoUpload(newFile.getAbsolutePath());
                                logo.setOrigem(GerenciadorArquivo.OrigemArquivo.CADASTRO_CONVENIO.value());
                                logo.setPossuiAnexo(true);
                            } else {
                                throw new ValidacaoException(bundle("somentePossivelAnexarImagem"));
                            }
                        } else {
                            throw new ValidacaoException(bundle("somentePossivelAnexar200"));
                        }
                    } catch (IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            }
        }
        ).setDefaultFormProcessing(
                false));
        form.add(
                new AbstractAjaxLink("btnRemoverAnexo") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        upload = null;
                        if (logo == null) {
                            logo = new MensagemAnexoDTO();
                        }
                        logo.setNomeArquivoOriginal(bundle("nenhumAnexoAdicionado"));
                        rotulo = logo.getNomeArquivoOriginal();
                        logo.setNomeArquivoUpload(null);
                        logo.setOrigem(null);
                        target.add(lblAnexo);
                    }
                }
        );

    }

    public void carregarImagemConvenio() {
        GerenciadorArquivo ga = null;
        if (convenio.getLogo() != null) {
            ga = LoadManager.getInstance(GerenciadorArquivo.class)
                    .addProperties(new HQLProperties(GerenciadorArquivo.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(GerenciadorArquivo.PROP_CODIGO, convenio.getLogo().getCodigo()))
                    .start().getVO();
        }
        if (ga != null) {
            if (logo == null) {
                logo = new MensagemAnexoDTO();
            }
            logo.setNomeArquivoOriginal(ga.getNomeArquivo());
            logo.setNomeArquivoUpload(ga.getCaminho());
            logo.setOrigem(ga.getOrigemArquivo());
            rotulo = ga.getNomeArquivo();
        }
    }
}
