package br.com.celk.view.atendimento.recepcao.dialog;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoChegadaAihDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaQuartosDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaQuartosDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingImpl;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.maps.DadosGoogleMapsHelper;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.IResource;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

public abstract class PnlConfirmacaoChegadaAihNovoLeito extends Panel {

    private Form form;
    private ConfirmacaoChegadaAihDTO dto;
    private Image imagem;
    private InputArea txaOcorrencia;

    private AbstractAjaxButton btnConfirmar;

    public PnlConfirmacaoChegadaAihNovoLeito(String id,ConfirmacaoChegadaAihDTO dto) throws DAOException, ValidacaoException {
        super(id);
        this.dto =  dto;
        init();
    }

    private void init() throws DAOException, ValidacaoException {
        form = new Form("form", new CompoundPropertyModel(new ConfirmacaoChegadaAihDTO()));
        setOutputMarkupId(true);

        form.add(imagem = new Image("imgAvatar", ""));
        imagem.setOutputMarkupId(true);

        ConfirmacaoChegadaAihDTO proxy = Lambda.on(ConfirmacaoChegadaAihDTO.class);



        DataPaging<QueryConsultaQuartosDTOParam> dataPaging =new DataPagingImpl<>(DataPaging.Type.ALVO_LIST);
        QueryConsultaQuartosDTOParam dtoQuartos = new QueryConsultaQuartosDTOParam();
        dtoQuartos.setSituacao(0l);
        dtoQuartos.setEmpresa(Arrays.asList(ApplicationSession.get().getSessaoAplicacao().getEmpresa()));
        dtoQuartos.setEspecialidadeLeito(dto.getLeitoQuarto().getEspecialidadeLeito());
        dataPaging.setParam(dtoQuartos);

        List<QueryConsultaQuartosDTO> listaLivre  = BOFactoryWicket.getBO(HospitalFacade.class).consultarQuartos(dataPaging).getList();


        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getQuartoInternacao().getDescricao())));
        form.add(getDropDownLeitoQuarto("leitoQuarto",listaLivre));
        form.add(txaOcorrencia = new InputArea<String>(path(proxy.getDescricaoOcorrencia())));

        btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, dto);
            }
        };
        form.add(btnConfirmar);

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public DropDown getDropDownLeitoQuarto(String id,  List<QueryConsultaQuartosDTO> listaLivre) {
        DropDown leitoQuarto = new DropDown(id);
        if(listaLivre.size()>0){
            for(QueryConsultaQuartosDTO quartoLivre : listaLivre){
                leitoQuarto.addChoice(quartoLivre.getLeitoQuarto(), quartoLivre.getLeito());
            }
        }else{
            leitoQuarto.addChoice(0, Bundle.getStringApplication("naoLocalizadoLeitoLivre"));
        }

        leitoQuarto.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                 target.add(leitoQuarto);
            }
        });
        return leitoQuarto;
    }



    public abstract void onConfirmar(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setDTO(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws DAOException {
        this.dto = dto;
        form.setModelObject(new CompoundPropertyModel(this.dto));
    }

    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }

}
