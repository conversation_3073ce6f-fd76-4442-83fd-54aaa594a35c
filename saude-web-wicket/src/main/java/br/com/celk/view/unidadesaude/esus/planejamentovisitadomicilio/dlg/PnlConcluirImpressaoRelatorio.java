package br.com.celk.view.unidadesaude.esus.planejamentovisitadomicilio.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryPlanejamentoVisitasDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlConcluirImpressaoRelatorio extends Panel {

    private AbstractAjaxButton btnFechar;

    private Long imprimirEndereco;
    private Long imprimirDoenca;
    private Long ordenarRua;

    private CheckBoxLongValue chkImpressaoEndereco;
    private CheckBoxLongValue chkImpressaoDoenca;
    private CheckBoxLongValue chkOrdenacaoRua;

    public PnlConcluirImpressaoRelatorio(String id) {
        super(id);
        init();
    }

    private void init() {

        Form form = new Form("form", new CompoundPropertyModel(new QueryPlanejamentoVisitasDTOParam()));

        form.add(chkImpressaoEndereco = new CheckBoxLongValue("imprimirEndereco"));
        form.add(chkImpressaoDoenca = new CheckBoxLongValue("imprimirDoenca"));
        form.add(chkOrdenacaoRua = new CheckBoxLongValue("ordenarRua"));

        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                QueryPlanejamentoVisitasDTOParam param = (QueryPlanejamentoVisitasDTOParam) form.getModel().getObject();
                onConfirmar(target, param);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, QueryPlanejamentoVisitasDTOParam param) throws ValidacaoException, DAOException;

}
