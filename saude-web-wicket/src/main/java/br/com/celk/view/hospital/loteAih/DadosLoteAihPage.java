package br.com.celk.view.hospital.loteAih;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.hospital.loteAih.customcolumn.ConsultaAihsColumnPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.LoteAihDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioImpressaoLaudoSolicitacaoDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LoteAih;
import org.apache.wicket.Component;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 */
@Private

public class DadosLoteAihPage extends BasePage {

    private LoteAihDTO loteAihDTO;
    private Table table;
    private List<Aih> lstAihs = new ArrayList<Aih>();
        
    public DadosLoteAihPage(LoteAihDTO loteAihDTO){
        this.loteAihDTO = loteAihDTO;
        carregaAihs(loteAihDTO.getLoteAih());
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel<LoteAihDTO>(loteAihDTO == null ? loteAihDTO = new LoteAihDTO() : loteAihDTO));
        
        form.add(new DisabledInputField("loteAih.codigo"));
        form.add(new DisabledInputField("loteAih.dataCadastro"));
        form.add(new DisabledInputField("quantidadeAih"));
        form.add(new DisabledInputField("loteAih.descricaoStatus"));
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        
        form.add(new VoltarButton("btnVoltar"));
        
        add(form);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns;
        columns = new ArrayList<IColumn>();

        Aih proxy = on(Aih.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadSus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("dataSolicitacao"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("diagnosticoInicial"), proxy.getDiagnosticoInicial()));
        columns.add(createSortableColumn(bundle("cidPrincipal"), proxy.getCidPrincipal().getDescricao()));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstAihs;
            }
        };
    }
    
    private CustomColumn<Aih> getCustomColumn() {
        return new CustomColumn<Aih>() {
            @Override
            public Component getComponent(String componentId, final Aih rowObject) {
                return new ConsultaAihsColumnPanel(componentId, rowObject) {
                    
                    @Override
                    public DataReport onImprimir() throws ReportException {
                        return imprimir(rowObject);
                    }
                };
            }
        };
    }
    
    private DataReport imprimir(Aih aih) throws ReportException {
        final RelatorioImpressaoLaudoSolicitacaoDTOParam param = new RelatorioImpressaoLaudoSolicitacaoDTOParam();
        param.setCodigoAutorizacao(aih.getCodigo());
        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoLaudoSolicitacao(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("dadosLoteAih");
    }

    private void carregaAihs(LoteAih loteAih) {
        if (loteAih.getCodigo() != null) {
            lstAihs = LoadManager.getInstance(Aih.class)
                    .addProperties(new HQLProperties(Aih.class).getProperties())
                    .addProperty(VOUtils.montarPath(Aih.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Aih.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_NOME))
                    .addProperty(VOUtils.montarPath(Aih.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_APELIDO))
                    .addProperty(VOUtils.montarPath(Aih.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Aih.PROP_LOTE_AIH), loteAih))
                    .start().getList();
        }
    }
    
}
