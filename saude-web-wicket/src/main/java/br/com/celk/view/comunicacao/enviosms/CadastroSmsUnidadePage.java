package br.com.celk.view.comunicacao.enviosms;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsCadastro;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroSmsUnidadePage extends BasePage {
    
    private Form<SmsCadastro> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private SmsCadastro smsCadastro;
    
    public CadastroSmsUnidadePage(){
        init();
    }
    
    public CadastroSmsUnidadePage(SmsCadastro smsCadastro){
        this.smsCadastro = smsCadastro;
        init();
    }
    
    private void init() {
        form = new Form("form", new CompoundPropertyModel(smsCadastro != null ? smsCadastro : new SmsCadastro()));
        
        SmsCadastro proxy = on(SmsCadastro.class);
        
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true));
        form.add(new RequiredInputArea<String>(path(proxy.getMensagem())));
        
        form.add(new VoltarButton("btnVoltar"));
        
        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(false);
            }
        });
        
        add(form);
    }
    
    private void salvar(boolean enviarSms) throws DAOException, ValidacaoException{        
        smsCadastro = form.getModel().getObject();
        smsCadastro.setTipoDestino(SmsCadastro.TipoDestino.UNIDADE.value());
        smsCadastro.setDestino(BundleManager.getString("pacientesUnidade") + ": " + smsCadastro.getEmpresa().getDescricao());
        
        Long count = LoadManager.getInstance(UsuarioCadsus.class)
                .addGroup(new QueryCustom.QueryCustomGroup(UsuarioCadsus.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_SITUACAO, UsuarioCadsus.SITUACAO_ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CELULAR, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_EMPRESA), smsCadastro.getEmpresa()))
                .start().getVO();
        
        smsCadastro.setTotalSms(Coalesce.asLong(count));

        BOFactoryWicket.getBO(SmsFacade.class).salvarEnviarSmsCadastro(smsCadastro, null, enviarSms);
        
        Page page = new ConsultaEnvioSmsPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroSmsUnidade");
    }   
    
}
