package br.com.celk.view.materiais.estoque.calculoestoque;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.CalculoEstoqueDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

public class GerarCalculoEstoquePage extends CadastroPage<CalculoEstoqueDTO> {

    private final String HABILITA_CALCULO_ESTOQUE = "HabilitaCalculoEstoque";

    private WebMarkupContainer containerGrupo;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private CheckBoxLongValue cbxFlagPossuiEstoqueZerado ;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<String> ddCurva;
    private DropDown<String> ddCriticidade;
    private DropDown<Integer> ddPeriodo;
    private DropDown dropDownTipoProduto;
    private AbstractAjaxButton btnSalvar;
    private RequiredInputField txtEstoqueMaximo;
    private RequiredInputField txtTempoReposicao;

    public GerarCalculoEstoquePage(CalculoEstoqueDTO object) {
        super(object);
    }

    public GerarCalculoEstoquePage() {

    }

    @Override
    public void init(Form<CalculoEstoqueDTO> form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(true));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL, Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,Empresa.TIPO_ESTABELECIMENTO_FARMACIA));
        autoCompleteConsultaEmpresa.setComponentValue(ApplicationSession.get().getSession().getEmpresa());

        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setEnabled(false);
        autoCompleteConsultaProduto.setIncluirInativos(false);

        form.add(containerGrupo = new WebMarkupContainer("containerGrupo"));
        containerGrupo.add(dropDownSubGrupo = DropDownUtil.getDropDownSubGrupo(dropDownSubGrupo));
        containerGrupo.add(dropDownGrupoProduto = DropDownUtil.getDropDownGrupo(dropDownGrupoProduto, dropDownSubGrupo, autoCompleteConsultaProduto));
        dropDownSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProduto.clearInput();
                autoCompleteConsultaProduto.setSubGrupoFixo(null);
                SubGrupo subGrupo = dropDownSubGrupo.getComponentValue();
                autoCompleteConsultaProduto.setSubGrupoFixo(subGrupo);
                autoCompleteConsultaProduto.setEnabled(true);
            }
        });

        form.add(ddCurva = DropDownUtil.getCurvaProdutoDropDown("curva"));
        form.add(ddCriticidade = DropDownUtil.getCriticidadeProdutoDropDown("criticidade"));
        form.add(ddPeriodo = getPeriodoDropDown());
        form.add(txtEstoqueMaximo = new RequiredInputField("estoqueMaximo"));
        form.add(txtTempoReposicao = new RequiredInputField("tempoReposicao"));

        if(this.isEdicao()) {
            CalculoEstoqueDTO dto = getForm().getModelObject();
            Produto produto = getProduto(dto.getProduto().getReferencia());
            autoCompleteConsultaProduto.setEnabled(true);
            containerGrupo.setVisible(false);
            ddCurva.setComponentValue(produto.getCurva());
            ddCriticidade.setComponentValue(produto.getCriticidade());

            List<EstoqueEmpresa> eeList = getEstoqueEmpresa(dto);
            if(!eeList.isEmpty()) {
                EstoqueEmpresa estoqueEmpresa = eeList.get(0);
                txtEstoqueMaximo.setComponentValue(estoqueEmpresa.getEstoqueMaximo());
                txtTempoReposicao.setComponentValue(estoqueEmpresa.getTempoReposicao());
                if(Objects.nonNull(estoqueEmpresa.getPeriodoConsumoMedio())) {
                    ddPeriodo.setComponentValue(estoqueEmpresa.getPeriodoConsumoMedio().intValue());
                }
            }
            getForm().add(ddCurva, ddCriticidade, ddPeriodo, txtEstoqueMaximo, txtTempoReposicao);
        }

        btnSalvar = getBtnSalvar();
        btnSalvar.add(new AttributeModifier("value", BundleManager.getString("gerar")));
        btnSalvar.add(new AttributeModifier("class", "novo"));
        getControls().add(btnSalvar);

        ddPeriodo.add(new AttributeModifier("title", BundleManager.getString("descricaoPeriodoConsumoMedio")));
    }

    @Override
    public Class<CalculoEstoqueDTO> getReferenceClass() {
        return CalculoEstoqueDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return CalculoEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("gerarCalculoEstoque");
    }

    @Override
    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        gerar(target);
        Page page = new CalculoEstoquePage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, getMsgSalvo(null));
    }

    @Override
    public WebMarkupContainer createBtnVoltar(String id) {
        return new AbstractAjaxButton(id){

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(getResponsePage());
            }
        }.setDefaultFormProcessing(false);
    }

    private void gerar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        CalculoEstoqueDTO dto = getForm().getModelObject();
        List<EstoqueEmpresa> estoqueEmpresaList = getEstoqueEmpresa(dto);
        if(!estoqueEmpresaList.isEmpty()) {
            for(EstoqueEmpresa estoqueEmpresa: estoqueEmpresaList) {
                Produto produto = getProduto(estoqueEmpresa.getRoProduto().getCodigo());
                produto.setCriticidade(dto.getCriticidade());

                DateTime dtFinal = new DateTime(Calendar.getInstance().getTime());
                int dias = Days.daysBetween(getDataInicial(dto.getPeriodo()), dtFinal).getDays();
                DatePeriod datePeriod = new DatePeriod(getDataInicial(dto.getPeriodo()).toDate(), dtFinal.toDate());
                Double consumoProduto = Coalesce.asDouble(EstoqueEmpresaHelper.getConsumoProduto(dto.getEmpresa(), produto, datePeriod), 0D);
                estoqueEmpresa.setConsumoMedio(Coalesce.asDouble((consumoProduto / dias), 0D));

                if(RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro(HABILITA_CALCULO_ESTOQUE))) {
                    produto.setCurva(dto.getCurva());
                    estoqueEmpresa.setEstoqueMinimo(Coalesce.asDouble((estoqueEmpresa.getConsumoMedio() * dto.getTempoReposicao()), 0D));
                }

                BOFactoryWicket.save(produto);

                estoqueEmpresa.setEstoqueMaximo(dto.getEstoqueMaximo().doubleValue());
                estoqueEmpresa.setTempoReposicao(dto.getTempoReposicao());
                estoqueEmpresa.setPeriodoConsumoMedio(dto.getPeriodo().longValue());
                estoqueEmpresa.setRoProduto(produto);
                BOFactory.save(estoqueEmpresa);
            }
        }
    }

    private DateTime getDataInicial(int periodo) {
        //Período de 6 meses para cálculo do consumo médido
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -periodo);
        return new DateTime(cal.getTime());
    }

    private Produto getProduto(String ref) {
        Produto produto = LoadManager.getInstance(Produto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_REFERENCIA), ref))
                .start().getVO();
        return produto;
    }

    private List<EstoqueEmpresa> getEstoqueEmpresa(CalculoEstoqueDTO dto) {
        LoadManager loadManager = LoadManager.getInstance(EstoqueEmpresa.class);
        if(Objects.nonNull(dto.getEmpresa())) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), dto.getEmpresa()));
        }
        if(Objects.nonNull(dto.getSubGrupo())) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO), dto.getSubGrupo()));
        }
        if(Objects.nonNull(dto.getGrupoProduto())) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), dto.getGrupoProduto().getCodigo()));
        }
        if(Objects.nonNull(dto.getProduto())) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), dto.getProduto()));
        }

        return loadManager.start().getList();
    }

    private DropDown getPeriodoDropDown(){
        DropDown dropDown = new DropDown("periodo");
        dropDown.addChoice(3, BundleManager.getString("3mes"));
        dropDown.addChoice(6, BundleManager.getString("6mes"));
        dropDown.addChoice(12, BundleManager.getString("1ano"));
        return dropDown;
    }
}
