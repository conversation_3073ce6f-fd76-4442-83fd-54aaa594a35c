package br.com.celk.view.materiais.compras.dialog;

import br.com.celk.component.bigdecimalfield.DisabledBigDecimalField;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.materiais.compras.interfaces.dto.OrdemCompraItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.FundoConsorcio;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConsultarDetalhesOrdemCompraItem extends Panel{

    private LoadableObjectModel<OrdemCompraItemDTO> model;
    private WebMarkupContainer containerUtilizaPregao;
    private WebMarkupContainer containerFundo;
    private WebMarkupContainer containerConsorcio;
    private FundoConsorcio fundoConsorcio;
    private boolean existeFundoConsorcio;
    private String exibeFiltroPedidoConsorcio;


    public PnlConsultarDetalhesOrdemCompraItem(String id){
        super(id);
        init();
    }

    public PnlConsultarDetalhesOrdemCompraItem(String id, FundoConsorcio fundoConsorcio) {
        super(id);
        this.fundoConsorcio = fundoConsorcio;
        existeFundoConsorcio = fundoConsorcio != null;
        init();
    }

    private void init() {
        Form form = new Form<OrdemCompraItem>("form", new CompoundPropertyModel(model = new LoadableObjectModel<OrdemCompraItemDTO>(OrdemCompraItemDTO.class, false)));
        OrdemCompraItemDTO proxy = on(OrdemCompraItemDTO.class);

        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getProduto().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getDescricaoStatus())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getProduto().getUnidade().getUnidade())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getNumeroItemPregao())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getDataUsuario())));
        form.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getQuantidadeCompra())));
        form.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getQuantidadeRecebida())));
        form.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getPrecoUnitario())).setMDec(4));
        form.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getTotalItemComprados())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getUsuario().getNome())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getUsuarioCancelamento().getNome())));
        form.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getQuantidadeCancelada())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getDataCancelamento())));
        form.add(new DisabledInputField(path(proxy.getOrdemCompraItem().getModelo())));

        form.add(containerConsorcio = new WebMarkupContainer("containerConsorcio"));
        containerConsorcio.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getQuantidadeCompraOriginal())));

        form.add(containerUtilizaPregao = new WebMarkupContainer("containerUtilizaPregao"));
        containerUtilizaPregao.add(new DisabledBigDecimalField(path(proxy.getOrdemCompraItem().getSaldoPregao())));

        String utilizaPregao = null;
        try {
            utilizaPregao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaAutorizacaoFornecimentoDescritivo");
            exibeFiltroPedidoConsorcio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("visualizarFiltroPedidoConsorcio");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        if (RepositoryComponentDefault.SIM.equals(utilizaPregao)){
            containerUtilizaPregao.setVisible(true);
        } else{
            containerUtilizaPregao.setVisible(false);
        }

        if (!RepositoryComponentDefault.SIM.equals(exibeFiltroPedidoConsorcio)) {
            containerConsorcio.setVisible(false);
        }

        form.add(containerFundo = new WebMarkupContainer("containerFundo"));
        form.add(new DisabledInputField(proxy.getFundoConsorcio().getDescricao()).setVisible(existeFundoConsorcio));
        containerFundo.setVisible(existeFundoConsorcio);
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){}

    public void setOrdemCompraItem(OrdemCompraItem ordemCompraItem, FundoConsorcio fundoConsorcio) throws DAOException {
        OrdemCompraItemDTO ordemCompraItemDTO = new OrdemCompraItemDTO();
        ordemCompraItemDTO.setFundoConsorcio(fundoConsorcio);
        ordemCompraItemDTO.setOrdemCompraItem(ordemCompraItem);
        model.setObject(ordemCompraItemDTO);
    }
}
