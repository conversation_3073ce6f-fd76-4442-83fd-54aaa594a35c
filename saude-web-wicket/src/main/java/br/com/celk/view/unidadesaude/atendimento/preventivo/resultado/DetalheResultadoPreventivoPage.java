package br.com.celk.view.unidadesaude.atendimento.preventivo.resultado;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.unidadesaude.atendimento.preventivo.resultado.tabs.DadosGeraisResultadoPreventivoTab;
import br.com.celk.view.unidadesaude.atendimento.preventivo.resultado.tabs.PreventivoTab;
import br.com.celk.view.unidadesaude.atendimento.preventivo.resultado.tabs.ResultadoPreventivoTab;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import java.util.ArrayList;
import java.util.List;

import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
public class DetalheResultadoPreventivoPage extends BasePage {

    private Preventivo preventivo;
    private ExameRequisicao exameRequisicao;

    public DetalheResultadoPreventivoPage(Preventivo preventivo, ExameRequisicao exameRequisicao) {
        this.preventivo = preventivo;
        this.exameRequisicao = exameRequisicao;
        init();
    }

    private void init() {
        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<Preventivo>(preventivo) {
            @Override
            public ITabPanel<Preventivo> newTabPanel(String panelId, Preventivo object) {


                return new DadosGeraisResultadoPreventivoTab(panelId, object);
            }
        });
        
        tabs.add(new CadastroTab<ExameRequisicao>(exameRequisicao) {
            @Override
            public ITabPanel<ExameRequisicao> newTabPanel(String panelId, ExameRequisicao object) {

                /*
                    preventivo.getExameRequisicao()
                */

                return new PreventivoTab(panelId, object);
            }
        });
        
         tabs.add(new CadastroTab<ExameRequisicao>(exameRequisicao) {
            @Override
            public ITabPanel<ExameRequisicao> newTabPanel(String panelId, ExameRequisicao object) {
                /*
                    preventivo.getExameRequisicao()
                */
                return new ResultadoPreventivoTab(panelId, object);
            }
        });

        add(new DetalheResultadoPreventivoTabbedPanel("wizard", preventivo, tabs));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("detalheResultadoPreventivo");
    }
}
