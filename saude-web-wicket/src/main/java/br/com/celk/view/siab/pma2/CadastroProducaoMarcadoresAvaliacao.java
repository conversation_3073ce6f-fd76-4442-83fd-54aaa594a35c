package br.com.celk.view.siab.pma2;

import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.siab.SiabPma2;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroProducaoMarcadoresAvaliacao extends CadastroPage<SiabPma2> {

    private InputField txtMesAno;
    
    private String mesAno;
    
    public CadastroProducaoMarcadoresAvaliacao(SiabPma2 object, boolean viewOnly) {
        super(object, viewOnly);
        initEdicao();
    }

    public CadastroProducaoMarcadoresAvaliacao(SiabPma2 object) {
        super(object);
        initEdicao();
    }

    public CadastroProducaoMarcadoresAvaliacao() {
    }
    
    @Override
    public void init(Form form) {
        form.add(new DisabledInputField(VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        form.add(new DisabledInputField(VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO)));
        form.add(txtMesAno = new RequiredInputField("mesAno", new PropertyModel(this, "mesAno")));
        
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES_FORA_AREA_ABRANGENCIA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES_MENOR1_ANO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES1_A4_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES5_A9_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES10_A14_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES15_A19_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES20_A39_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES40_A49_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES50_A59_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_RESIDENTES60_ANOS_OU_MAIS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_PUERICULTURA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_PRE_NATAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_PREVENCAO_CANCER_CERVICO_UTERINO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_DST_AIDS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_DIABETES)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_HIPERTENSAO_ARTERIAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_HANSENIASE)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_TIPO_ATENDIMENTO_TUBERCULOSE)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_SOLICITACAO_EXAMES_PATOLOGIA_CLINICA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_SOLICITACAO_EXAMES_RADIODIAGNOSTICO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_SOLICITACAO_EXAMES_CITOPATOLOGICO_CERVICO_VAGINAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_SOLICITACAO_EXAMES_ULTRASSONOGRAFIA_OBSTETRICA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_SOLICITACAO_EXAMES_OUTROS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_ENCAMINHAMENTOS_ATENDIMENTO_ESPECIALIZADO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_ENCAMINHAMENTOS_INTERNACAO_HOSPITALAR)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_ENCAMINHAMENTOS_URGENCIA_EMERGENCIA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_INTERNACAO_DOMICILIAR)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_ESPECIFICO_AT)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_VISITA_INSPECAO_SANITARIA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_INDIVIDUAL_ENFERMEIRO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_INDIVIDUAL_OUTRO_PROFISSIONAL_NIVEL_SUPERIOR)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_CURATIVOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_INALACOES)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_INJECOES)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_RETIRADA_PONTOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_TERAPIA_REIDRATACAO_ORAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_SUTURA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_GRUPO_EDUCACAO_SAUDE)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_COLETIVO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_PROCEDIMENTO_REUNIAO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_VALVULOPATIAS_REUMATICAS_PESSOAS5_A14_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_ACIDENTE_VASCULAR_CEREBRAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_INFARTO_AGUDO_MIOCARDIO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_DHEG_FORMA_GRAVE)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_DOENCA_HEMOLITICA_PERINATAL)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_FRATURA_COLO_FEMUR_PESSOAS_MAIS50_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_MENINGITE_TUBERCULOSA_PESSOAS_MENORES5_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_HANSENIASE)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_CITOLOGIA_ONCOTICA)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_MARCADOR_PNEUMONIA_PESSOAS_MENORES5_ANOS)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_VISITAS_DOMICILIARES_MEDICO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_VISITAS_DOMICILIARES_ENFERMEIRO)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_VISITAS_DOMICILIARES_OUTROS_PROFISSIONAIS_NIVEL_SUPERIOR)));
        form.add(new InputField(VOUtils.montarPath(SiabPma2.PROP_VISITAS_DOMICILIARES_PROFISSIONAIS_NIVEL_MEDIO)));
        
        txtMesAno.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try{
                    Date parserMounthYear = Data.parserMounthYear(mesAno);
                    
                    if (parserMounthYear!=null) {
                        SiabPma2 siabPma2c = LoadManager.getInstance(SiabPma2.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL), getForm().getModelObject().getEquipeProfissional()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2.PROP_MES), Data.getMes(parserMounthYear)))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2.PROP_ANO), Data.getAno(parserMounthYear)))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SiabPma2.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, getForm().getModelObject().getCodigo()))
                        .start().getVO();
                        if (siabPma2c!=null) {
                            txtMesAno.limpar(target);
                            modalWarn(target, new ValidacaoException(BundleManager.getString("registroJaCadastradoParaMesInformado")));
                        }
                    }
                } catch (ParseException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });
    }

    @Override
    public Class<SiabPma2> getReferenceClass() {
        return SiabPma2.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaProducaoMarcadoresAvaliacao.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProducaoMarcadoresAvaliacao");
    }

    @Override
    public Object salvar(SiabPma2 object) throws DAOException, ValidacaoException {
        try {
            Date parserMounthYear = Data.parserMounthYear(mesAno);
            if (parserMounthYear.after(Data.adjustRangeDay(Data.getDataAtual()).getDataInicial())) {
                throw new ValidacaoException(BundleManager.getString("mesAnoNaoPodeSerSuperiorAoMesAnoAtual"));
            }
            object.setMes(new Integer(Data.getMes(parserMounthYear)).longValue());
            object.setAno(new Integer(Data.getAno(parserMounthYear)).longValue());
            object.setIdModelo("1");
            object.setStatus(SiabPma2.StatusSiabPma2.CADASTRADO.value());
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return super.salvar(object);
    }

    @Override
    public void customNewInstance(SiabPma2 object) {
        Profissional profissional = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getProfissional();
        if (profissional==null) {
            ConsultaProducaoMarcadoresAvaliacao page = new ConsultaProducaoMarcadoresAvaliacao();
            getSession().getFeedbackMessages().warn(page, BundleManager.getString("usuarioDevePossuirProfissionalRelacionado"));
            setResponsePage(page);
        } else {
                List<EquipeProfissional> equipeProfissionals = LoadManager.getInstance(EquipeProfissional.class)
                        .addProperties(new HQLProperties(EquipeProfissional.class).getProperties())
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_MICRO_AREA))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL), profissional))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_MICRO_AREA), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_DATA_DESLIGAMENTO), BuilderQueryCustom.QueryParameter.IS_NULL))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                        .start().getList();

                if (equipeProfissionals.isEmpty()) {
                    ConsultaProducaoMarcadoresAvaliacao page = new ConsultaProducaoMarcadoresAvaliacao();
                    getSession().getFeedbackMessages().warn(page, BundleManager.getString("profissionalUsuarioNaoPossuiRegistroValidoEmUmaEquipe"));
                    setResponsePage(page);
                } else if (equipeProfissionals.size()>1) {
                    ConsultaProducaoMarcadoresAvaliacao page = new ConsultaProducaoMarcadoresAvaliacao();
                    getSession().getFeedbackMessages().warn(page, BundleManager.getString("profissionalUsuarioRelacionadoMaisDeUmaEquipe"));
                    setResponsePage(page);
                } else {
                    object.setEquipeProfissional(equipeProfissionals.get(0));
                }         
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtMesAno;
    }
    
    private void initEdicao(){
        Calendar c = GregorianCalendar.getInstance();
        c.set(Calendar.MONTH, getForm().getModelObject().getMes().intValue()-1);
        c.set(Calendar.YEAR, getForm().getModelObject().getAno().intValue());
        
        mesAno = Data.formatarMesAno(c.getTime());
    }

}
