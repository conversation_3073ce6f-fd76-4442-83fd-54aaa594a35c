package br.com.celk.view.atendimento.consultaprontuario.panel.consultalaudotfd;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTO;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class DetalhesTfdConsultaPanel extends ConsultaProntuarioCadastroPanel {
    
    private LaudoTfdDTO dto;
    
    private DropDown<Long> dropDownUrgente;
    private DropDown<Long> dropDownCaraterAtendimento;
    private DropDown<Long> dropDownTipoTransporte;
    private WebMarkupContainer containerUrgente;
    private WebMarkupContainer containerExameFisico;
    private WebMarkupContainer containerDiagnosticoProvavel;
    private WebMarkupContainer containerExamesRealizados;
    private WebMarkupContainer containerTratamentosRealizados;
    private WebMarkupContainer containerProcedimentoTratamentoSolicitado;
    private WebMarkupContainer containerJustificativaTfd;
    private WebMarkupContainer containerJustificativaAcompanhante;
    private WebMarkupContainer containerTransporteRecomendavel;    

    public DetalhesTfdConsultaPanel(String id, Long codigoLaudo) {
        super(id, bundle("laudoTfd"));
        carregarLaudo(codigoLaudo);
    }

    private void carregarLaudo(Long codigo) {
        LaudoTfd proxy = on(LaudoTfd.class);

        LaudoTfd laudoTfd = LoadManager.getInstance(LaudoTfd.class)
                .addProperties(new HQLProperties(LaudoTfd.class).getProperties())
                .addProperty(path(proxy.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()))
                .addProperty(path(proxy.getPedidoTfd().getNumeroPedido()))
                .addProperty(path(proxy.getPedidoTfd().getSolicitacaoAgendamento().getDataAgendamento()))
                .addProperty(path(proxy.getPedidoTfd().getSolicitacaoAgendamento().getUnidadeExecutante().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_CODIGO, codigo)).start().getVO();
        dto = new LaudoTfdDTO();
        dto.setLaudoTfd(laudoTfd);
        dto.setProcedimentoSolicitadoTfdList(carregarProcedimentoSolicitadoTfd());
    }

    private List<ProcedimentoSolicitadoTfd> carregarProcedimentoSolicitadoTfd() {
        List<ProcedimentoSolicitadoTfd> lstProcedimentoSolicitadoTfd = LoadManager.getInstance(ProcedimentoSolicitadoTfd.class)
                .addProperties(new HQLProperties(ProcedimentoSolicitadoTfd.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ProcedimentoSolicitadoTfd.PROP_LAUDO_TFD, dto.getLaudoTfd())).start().getList();
        
        return lstProcedimentoSolicitadoTfd;
        
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        LaudoTfdDTO proxy = on(LaudoTfdDTO.class);
        Form form = new Form("form", new CompoundPropertyModel(dto));
        
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getDataCadastro())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getProfissional().getNome())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getTipoProcedimento().getDescricao())));
        form.add(dropDownCaraterAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getLaudoTfd().getCaraterAtendimento()), LaudoTfd.CaraterAtendimento.values(), false, false));
        dropDownCaraterAtendimento.setEnabled(false);
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getCid().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getDataLaudo())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getDescricaoStatus())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getPedidoTfd().getNumeroPedido())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getPedidoTfd().getSolicitacaoAgendamento().getDataAgendamento())));
        form.add(new DisabledInputField(path(proxy.getLaudoTfd().getPedidoTfd().getSolicitacaoAgendamento().getUnidadeExecutante().getDescricao())));
        
        form.add(new DisabledInputArea(path(proxy.getLaudoTfd().getHistoricoDoenca())));
        
        form.add(containerUrgente = new WebMarkupContainer("containerUrgente"));
        containerUrgente.setOutputMarkupId(true);
        containerUrgente.add(getDropDownUrgente(path(proxy.getLaudoTfd().getFlagUrgente())));
        containerUrgente.add(new DisabledInputArea(path(proxy.getLaudoTfd().getObservacaoUrgente())));
        dropDownUrgente.setEnabled(false);

        form.add(containerExameFisico = new WebMarkupContainer("containerExameFisico"));
        containerExameFisico.setOutputMarkupId(true);
        containerExameFisico.add(new DisabledInputArea(path(proxy.getLaudoTfd().getExameFisico())));

        form.add(containerDiagnosticoProvavel = new WebMarkupContainer("containerDiagnosticoProvavel"));
        containerDiagnosticoProvavel.setOutputMarkupId(true);
        containerDiagnosticoProvavel.add(new DisabledInputArea(path(proxy.getLaudoTfd().getDiagnosticoProvavel())));

        form.add(containerExamesRealizados = new WebMarkupContainer("containerExamesRealizados"));
        containerExamesRealizados.setOutputMarkupId(true);
        containerExamesRealizados.add(new DisabledInputArea(path(proxy.getLaudoTfd().getExameComplementarRealizado())));

        form.add(containerTratamentosRealizados = new WebMarkupContainer("containerTratamentosRealizados"));
        containerTratamentosRealizados.setOutputMarkupId(true);
        containerTratamentosRealizados.add(new DisabledInputArea(path(proxy.getLaudoTfd().getTratamentoRealizado())));

        form.add(containerProcedimentoTratamentoSolicitado = new WebMarkupContainer("containerProcedimentoTratamentoSolicitado"));
        containerProcedimentoTratamentoSolicitado.setOutputMarkupId(true);
        containerProcedimentoTratamentoSolicitado.add(new Table("tblProcedimentos", getColumns(), getCollectionProvider()).populate());

        form.add(containerJustificativaTfd = new WebMarkupContainer("containerJustificativaTfd"));
        containerJustificativaTfd.setOutputMarkupId(true);
        containerJustificativaTfd.add(new DisabledInputArea(path(proxy.getLaudoTfd().getJustificativaTfd())));

        form.add(containerJustificativaAcompanhante = new WebMarkupContainer("containerJustificativaAcompanhante"));
        containerJustificativaAcompanhante.setOutputMarkupId(true);
        containerJustificativaAcompanhante.add(new DisabledInputArea(path(proxy.getLaudoTfd().getJustificativaAcompanhante())));

        form.add(containerTransporteRecomendavel = new WebMarkupContainer("containerTransporteRecomendavel"));
        containerTransporteRecomendavel.setOutputMarkupId(true);
        containerTransporteRecomendavel.add(dropDownTipoTransporte = DropDownUtil.getIEnumDropDown(path(proxy.getLaudoTfd().getTransporteRecomendavel()), LaudoTfd.TransporteRecomendavel.values(), false, false));
        dropDownTipoTransporte.setEnabled(false);
        
        containerTransporteRecomendavel.add(new DisabledInputArea(path(proxy.getLaudoTfd().getJustificativaTransporte())));

        form.add(new SubmitButton("btnVoltar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                ConsultaLaudoTfdPanel consultaLaudoTfdPanel = new ConsultaLaudoTfdPanel(getConsultaProntuarioController().panelId(), bundle("laudoTfd"));
                getConsultaProntuarioController().changePanel(target, consultaLaudoTfdPanel);
            }
        }));
        
        add(form);
    }
    
    private DropDown getDropDownUrgente(String id) {
        dropDownUrgente = new DropDown(id);
        
        dropDownUrgente.addChoice(Encaminhamento.NAO, bundle("nao"));
        dropDownUrgente.addChoice(Encaminhamento.SIM, bundle("sim"));
        
        return dropDownUrgente;
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        ProcedimentoSolicitadoTfd proxy = on(ProcedimentoSolicitadoTfd.class);
        
        columns.add(createColumn(bundle("procedimento", this), proxy.getProcedimento().getDescricao()));
        columns.add(createColumn(bundle("observacao", this), proxy.getObservacao()));
        
        return columns;
    }

    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dto.getProcedimentoSolicitadoTfdList();
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); 
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (StringUtils.trimToNull(dto.getLaudoTfd().getObservacaoUrgente()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerUrgente)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getExameFisico()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExameFisico)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getDiagnosticoProvavel()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerDiagnosticoProvavel)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getExameComplementarRealizado()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExamesRealizados)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getTratamentoRealizado()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerTratamentosRealizados)));
        }

        if (dto.getProcedimentoSolicitadoTfdList() != null
                && !dto.getProcedimentoSolicitadoTfdList().isEmpty()) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerProcedimentoTratamentoSolicitado)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getJustificativaTfd()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerJustificativaTfd)));
        }

        if (StringUtils.trimToNull(dto.getLaudoTfd().getJustificativaAcompanhante()) != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerJustificativaAcompanhante)));
        }

        if (dto.getLaudoTfd().getJustificativaTransporte() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerTransporteRecomendavel)));
        }
    }

}
