package br.com.celk.view.materiais.estoque.configuracaoestoqueminimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.popover.Popover;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.ProcessoAvaliacaoEstoque;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroConfiguracaoPage extends CadastroPage<ProcessoAvaliacaoEstoque> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<Long> dropDownTipoProcesso;
    private InputField<Long> txtDiasValidade;
    private InputField<Long> txtMesesDum;
    private WebMarkupContainer containerDiasValidade;
    private WebMarkupContainer containerMesesDum;

    private Popover popover;

    public CadastroConfiguracaoPage(ProcessoAvaliacaoEstoque object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroConfiguracaoPage(ProcessoAvaliacaoEstoque object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroConfiguracaoPage(ProcessoAvaliacaoEstoque object) {
        this(object, false);
    }

    public CadastroConfiguracaoPage() {
        this(null);
    }

    @Override
    public void init(Form<ProcessoAvaliacaoEstoque> form) {
        ProcessoAvaliacaoEstoque proxy = on(ProcessoAvaliacaoEstoque.class);

        containerDiasValidade = new WebMarkupContainer("containerDiasValidade");
        containerDiasValidade.setOutputMarkupId(true);
        containerDiasValidade.setOutputMarkupPlaceholderTag(true);
        containerMesesDum = new WebMarkupContainer("containerMesesDum");
        containerMesesDum.setOutputMarkupId(true);
        containerMesesDum.setOutputMarkupPlaceholderTag(true);
//        containerDiasValidade.setVisible(false);

        form.add(dropDownTipoProcesso = DropDownUtil.getIEnumDropDown(path(proxy.getTipoProcesso()), ProcessoAvaliacaoEstoque.TipoProcesso.values(), true, true));
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()), true)
                .setLabel(new Model<String>(BundleManager.getString("estabelecimento"))));
        form.add(new AutoCompleteConsultaUsuario(path(proxy.getUsuario()), true));

        txtDiasValidade = new InputField(path(proxy.getDiasValidade()));
        txtMesesDum = new InputField(path(proxy.getMesesDumPrenatal()));
        popover = new Popover().setText("tempoGetacaoMeses");
        popover.setContent("msgMesesDumPrenatal");
        popover.setTrigger(Popover.Trigger.FOCUS);
        txtMesesDum.add(popover);

        containerDiasValidade.add(txtDiasValidade);
        containerMesesDum.add(txtMesesDum);

        dropDownTipoProcesso.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownTipoProcesso.getComponentValue() != null) {
                    if (ProcessoAvaliacaoEstoque.TipoProcesso.VALIDADE_PRODUTO.value().equals(dropDownTipoProcesso.getComponentValue())) {
                        containerDiasValidade.setVisible(true);
                    } else {
                        containerDiasValidade.setVisible(false);
                    }

                    if (ProcessoAvaliacaoEstoque.TipoProcesso.RELACAO_GESTANTES.value().equals(dropDownTipoProcesso.getComponentValue())) {
                        containerMesesDum.setVisible(true);
                    } else {
                        containerMesesDum.setVisible(false);
                    }
                }
                txtDiasValidade.limpar(target);
                txtMesesDum.limpar(target);
                target.add(containerDiasValidade);
                target.add(containerMesesDum);
            }
        });

        if (form.getModel().getObject() != null) {
            if (ProcessoAvaliacaoEstoque.TipoProcesso.VALIDADE_PRODUTO.value().equals(form.getModel().getObject().getTipoProcesso())) {
                containerDiasValidade.setVisible(true);
            } else {
                containerDiasValidade.setVisible(false);
            }
            if (ProcessoAvaliacaoEstoque.TipoProcesso.RELACAO_GESTANTES.value().equals(form.getModel().getObject().getTipoProcesso())) {
                containerMesesDum.setVisible(true);
            } else {
                containerMesesDum.setVisible(false);
            }
        }

        form.add(containerMesesDum);
        form.add(containerDiasValidade);

    }

    @Override
    public Object salvar(ProcessoAvaliacaoEstoque object) throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(ProcessoAvaliacaoEstoque.class)
                .addProperties(new HQLProperties(ProcessoAvaliacaoEstoque.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_TIPO_PROCESSO, object.getTipoProcesso()))
                .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_EMPRESA, object.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_USUARIO, object.getUsuario()));

        if (object.getCodigo() != null) {
            load.addParameter(new QueryCustom.QueryCustomParameter(ProcessoAvaliacaoEstoque.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }

        boolean exists = load.exists();

        if (exists) {
            throw new ValidacaoException(BundleManager.getString("msgJaExisteEstabelecimentoVinculadoUsuario"));
        }

        return BOFactoryWicket.save(object);
    }

    @Override
    public Class<ProcessoAvaliacaoEstoque> getReferenceClass() {
        return ProcessoAvaliacaoEstoque.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public Class getResponsePage() {
        return ConfiguracaoProcessoAvaliacaoEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroConfiguracao");
    }
}
