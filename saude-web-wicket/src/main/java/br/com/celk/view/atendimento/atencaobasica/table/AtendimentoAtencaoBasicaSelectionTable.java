package br.com.celk.view.atendimento.atencaobasica.table;

import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

import java.util.Arrays;
import java.util.List;

/**
 * Created by sulivan on 18/01/19.
 */
public abstract class AtendimentoAtencaoBasicaSelectionTable extends SelectionTable<AtendimentoWebDTO> {

    private String destacarPacientesPorIdade;
    private boolean pageable;
    private Long exibirAtendimentosAntigos;
    private Long pagina;

    public AtendimentoAtencaoBasicaSelectionTable(String id, List<ISortableColumn<AtendimentoWebDTO>> iSortableColumns, ICollectionProvider collectionProvider, String destacarPacientesPorIdade, Long exibirAtendimentosAntigos) {
        super(id, iSortableColumns, collectionProvider, Arrays.asList(Long.MAX_VALUE));
        init(destacarPacientesPorIdade, exibirAtendimentosAntigos);
    }

    private void init(String destacarPacientesPorIdade, Long exibirAtendimentosAntigos){
        this.destacarPacientesPorIdade = destacarPacientesPorIdade;
        this.exibirAtendimentosAntigos = exibirAtendimentosAntigos;
        this.addSelectionAction(new ISelectionAction<AtendimentoWebDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AtendimentoWebDTO object) {
                AtendimentoAtencaoBasicaSelectionTable.this.onSelection(target, object);
            }
        });

    }

    @Override
    protected Item<AtendimentoWebDTO> newRowItem(String id, int index, IModel<AtendimentoWebDTO> model) {
        return new AtendimentoAtencaoBasicaTableRow(id, index, model, AtendimentoAtencaoBasicaSelectionTable.this, destacarPacientesPorIdade);
    }

    public void populate(AjaxRequestTarget target, Long exibirAtendimentosAntigos, Long pagina) {
        populate();
        update(target, exibirAtendimentosAntigos, pagina);
    }

    public void update(AjaxRequestTarget target, Long exibirAtendimentosAntigos, Long pagina) {
        this.exibirAtendimentosAntigos = exibirAtendimentosAntigos;
        setPagina(pagina);
        super.update(target);

    }

    public Long getPagina() {
        return pagina;
    }

    public AtendimentoAtencaoBasicaSelectionTable setPagina(Long pagina) {
        this.pagina = pagina;
        return this;
    }

    public abstract void onSelection(AjaxRequestTarget target, AtendimentoWebDTO object);
}