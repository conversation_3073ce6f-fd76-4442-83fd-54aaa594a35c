package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.vigilancia.registroagravo.enums.SimNaoEnum;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaPage extends FichaInvestigacaoAgravoBasePage {
    private final String CSSFILE = "FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaPage.css";

    private InvestigacaoAgravoLeishmanioseTegumentarAmericana investigacaoAgravoLeishmanioseTegumentarAmericana;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerDadosClinicos;
    private DropDown ddPresencaLesaoCutanea;
    private DropDown ddPresencaLesaoMucosa;
    private DropDown ddCicatrizerCutaneas;
    private DropDown ddCoInfeccaoHiv;

    private WebMarkupContainer containerDadosLaboratoriais;
    private DropDown ddParasitologicoDireto;
    private DropDown ddIrm;
    private DropDown ddHistopatologia;

    private WebMarkupContainer containerClassificacaoCaso;
    private DropDown ddTipoEntrada;
    private DropDown ddFormaClinica;

    private WebMarkupContainer containerTratamento;
    private DateChooser dataInicioTratamento;
    private DropDown ddDrogaInicialAdministrada;
    private InputField peso;
    private DropDown ddDosePrescrita;
    private InputField numAmpolas;
    private DropDown ddOutraDroga;

    private RadioButtonGroup radioGroupCasoAutoctone;
    private WebMarkupContainer containerLocalInfeccao;
    private AutoCompleteConsultaCidade autoCompleteCidadeLocalInfeccao;
    private DisabledInputField estadoLocalInfeccao;
    private AutoCompleteConsultaPais autoCompletePaisLocalInfeccao;
    private DisabledInputField codMunicipioLocalInfeccao;
    private InputField distritoLocalInfeccao;
    private InputField bairroLocalInfeccao;

    private WebMarkupContainer containerConclusao;
    private DropDown ddCriterioConfirmacao;
    private DropDown ddClassificacaoEpidemiologica;
    private DropDown ddDoencaRelacionadaTrabalho;
    private DropDown ddEvolucaoCaso;
    private DateChooser dataObito;

    private WebMarkupContainer containerDeslocamento;
    private CompoundPropertyModel<InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento> modelDeslocamento;
    private Table tblDeslocamento;
    private List<InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento> deslocamentoList;
    private DateChooser dataDeslocamento;
    private AutoCompleteConsultaCidade autoCompleteCidadeDeslocamento;
    private DisabledInputField estadoLocalDeslocamento;
    private AutoCompleteConsultaPais autoCompletePaisDeslocamento;

    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoLeishmanioseTegumentarAmericana = InvestigacaoAgravoLeishmanioseTegumentarAmericana.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoLeishmanioseTegumentarAmericana == null) {
            investigacaoAgravoLeishmanioseTegumentarAmericana = new InvestigacaoAgravoLeishmanioseTegumentarAmericana();
            investigacaoAgravoLeishmanioseTegumentarAmericana.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoLeishmanioseTegumentarAmericana.setRegistroAgravo(getAgravo());
        }

        deslocamentoList = investigacaoAgravoLeishmanioseTegumentarAmericana.getLocaisDeslocamentoByIdInvestigacaoLeishmaniose();

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoLeishmanioseTegumentarAmericana.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoLeishmanioseTegumentarAmericana.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoLeishmanioseTegumentarAmericana.setOcupacaoCbo(tabelaCbo);
        }

        if (investigacaoAgravoLeishmanioseTegumentarAmericana.getCidadeLocalInfeccao() != null) {
            Cidade cidadeExposicao = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(investigacaoAgravoLeishmanioseTegumentarAmericana.getCidadeLocalInfeccao().getCodigo());
            investigacaoAgravoLeishmanioseTegumentarAmericana.setCidadeLocalInfeccao(cidadeExposicao);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoLeishmanioseTegumentarAmericana)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO fichaDTO = new FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoLeishmanioseTegumentarAmericana(investigacaoAgravoLeishmanioseTegumentarAmericana);
        fichaDTO.setInvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamentoList(deslocamentoList);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoLeishmanioseTegumentarAmericana.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoLeishmanioseTegumentarAmericana.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy = on(InvestigacaoAgravoLeishmanioseTegumentarAmericana.class);
        criarInvestigacao(proxy);
        criarDadosClinicos(proxy);
        criarDadosLaboratoriais(proxy);
        criarClassificacaoCaso(proxy);
        criarTratamento(proxy);
        criarLocalInfeccao(proxy);
        criarConclusao(proxy);
        criarDeslocamento();
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarInvestigacao();
        carregarDadosClinicos();
        carregarDadosLaboratoriais();
        carregarClassificacaoCaso();
        carregarTratamento();
        carregarLocalInfeccao();
        carregarConclusao();
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy = on(InvestigacaoAgravoLeishmanioseTegumentarAmericana.class);

        validarRegra(
                isBefore(dataInvestigacao.getData().getConvertedInput(), investigacaoAgravoLeishmanioseTegumentarAmericana.getRegistroAgravo().getDataRegistro()),
                dataInvestigacao,
                "msgDataInvestigacaoMaiorIgualDataAgravo");
    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public boolean isBefore(Date data1, Date data2) {
        return (data1.compareTo(data2) < 0);
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO dto = (FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoLeishmanioseTegumentarAmericana(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO fichaDTO = (FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaDTO) getFichaDTO();

        if (investigacaoAgravoLeishmanioseTegumentarAmericana.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoLeishmanioseTegumentarAmericana.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoLeishmanioseTegumentarAmericana.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoLeishmanioseTegumentarAmericanaPage.class, CSSFILE))
        );
    }

    private void criarInvestigacao(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.addRequiredClass();
        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void carregarInvestigacao() {
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataInvestigacao, !isModoLeitura(), true, null);
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
    }

    private void criarDadosClinicos(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        ddPresencaLesaoCutanea = DropDownUtil.getIEnumDropDown(path(proxy.getPresencaLesaoCutanea()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.getSimNao(), true);
        ddPresencaLesaoMucosa = DropDownUtil.getIEnumDropDown(path(proxy.getPresencaLesaoMucosa()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.getSimNao(), true);
        ddCicatrizerCutaneas = DropDownUtil.getIEnumDropDown(path(proxy.getPresencaLesaoMucosaCicatriz()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.getSimNao(), true);
        ddCoInfeccaoHiv = DropDownUtil.getIEnumDropDown(path(proxy.getCoInfeccaoHIV()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.getSimNaoIgnorado(), true);

        containerDadosClinicos.add(ddPresencaLesaoCutanea, ddPresencaLesaoMucosa,
                ddCicatrizerCutaneas, ddCoInfeccaoHiv);
        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void carregarDadosClinicos() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddPresencaLesaoCutanea, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddPresencaLesaoMucosa, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCicatrizerCutaneas, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCoInfeccaoHiv, !isModoLeitura(), true, null);

        ddPresencaLesaoMucosa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value().equals(ddPresencaLesaoMucosa.getComponentValue()))
                        && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value().equals(ddPresencaLesaoCutanea.getComponentValue()))) {
                    ddFormaClinica.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.MUCOSA.value());
                    ddCicatrizerCutaneas.setEnabled(false);
                }
                else if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value().equals(ddPresencaLesaoMucosa.getComponentValue()))
                        && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value().equals(ddPresencaLesaoCutanea.getComponentValue()))) {
                    ddFormaClinica.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.CUTANEA.value());
                    ddCicatrizerCutaneas.setEnabled(true);
                }
                else {
                    ddFormaClinica.limpar(target);
                }
                target.add(ddFormaClinica, ddCicatrizerCutaneas);
            }
        });

        ddPresencaLesaoCutanea.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value().equals(ddPresencaLesaoMucosa.getComponentValue()))
                        && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value().equals(ddPresencaLesaoCutanea.getComponentValue()))) {
                    ddFormaClinica.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.MUCOSA.value());
                    ddCicatrizerCutaneas.setEnabled(false);
                }
                else if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value().equals(ddPresencaLesaoMucosa.getComponentValue()))
                        && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value().equals(ddPresencaLesaoCutanea.getComponentValue()))) {
                    ddFormaClinica.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.CUTANEA.value());
                    ddCicatrizerCutaneas.setEnabled(true);
                }
                else {
                    ddFormaClinica.limpar(target);
                }
                target.add(ddFormaClinica, ddCicatrizerCutaneas);
            }
        });
    }

    private void criarDadosLaboratoriais(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerDadosLaboratoriais = new WebMarkupContainer("containerDadosLaboratoriais");
        containerDadosLaboratoriais.setOutputMarkupId(true);

        ddParasitologicoDireto = DropDownUtil.getIEnumDropDown(path(proxy.getParasitologicoDireto()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);
        ddIrm = DropDownUtil.getIEnumDropDown(path(proxy.getIrm()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);
        ddHistopatologia = DropDownUtil.getIEnumDropDown(path(proxy.getHistopatologia()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.HistopatologiaEnum.values(), true);

        containerDadosLaboratoriais.add(ddParasitologicoDireto, ddIrm, ddHistopatologia);
        getContainerInformacoesComplementares().add(containerDadosLaboratoriais);
    }

    private void carregarDadosLaboratoriais() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddParasitologicoDireto, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddIrm, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddHistopatologia, !isModoLeitura(), true, null);

        ddParasitologicoDireto.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.POSITIVO.value().equals(ddParasitologicoDireto.getComponentValue()))) {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.LABORATORIAL.value());
                    ddCriterioConfirmacao.setEnabled(false);
                } else {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.CLINICO_EPIDEMIOLOGICO.value());
                    ddCriterioConfirmacao.setEnabled(true);
                }
                target.add(ddCriterioConfirmacao);
            }
        });

        ddIrm.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.POSITIVO.value().equals(ddIrm.getComponentValue()))) {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.LABORATORIAL.value());
                    ddCriterioConfirmacao.setEnabled(false);
                } else {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.CLINICO_EPIDEMIOLOGICO.value());
                    ddCriterioConfirmacao.setEnabled(true);
                }
                target.add(ddCriterioConfirmacao);
            }
        });

        ddHistopatologia.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && ((InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.HistopatologiaEnum.ENCONTRO_PARASITA.value().equals(ddHistopatologia.getComponentValue()))
                        || (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.HistopatologiaEnum.COMPATIVEL.value().equals(ddHistopatologia.getComponentValue())))) {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.LABORATORIAL.value());
                } else {
                    ddCriterioConfirmacao.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.CLINICO_EPIDEMIOLOGICO.value());
                    ddCriterioConfirmacao.setEnabled(true);
                }
                target.add(ddCriterioConfirmacao);
            }
        });
    }

    private void criarClassificacaoCaso(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerClassificacaoCaso = new WebMarkupContainer("containerClassificacaoCaso");
        containerClassificacaoCaso.setOutputMarkupId(true);

        ddTipoEntrada = DropDownUtil.getIEnumDropDown(path(proxy.getTipoEntrada()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.TipoEntradaEnum.values(), true);
        ddFormaClinica = DropDownUtil.getIEnumDropDown(path(proxy.getFormaClinica()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.values(), true);

        containerClassificacaoCaso.add(ddTipoEntrada, ddFormaClinica);
        getContainerInformacoesComplementares().add(containerClassificacaoCaso);
    }

    private void carregarClassificacaoCaso() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoEntrada, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddFormaClinica, !isModoLeitura(), true, null);

        ddFormaClinica.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.CUTANEA.value().equals(ddFormaClinica.getComponentValue()))) {
                    ddPresencaLesaoCutanea.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value());
                    ddPresencaLesaoMucosa.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value());
                    ddCicatrizerCutaneas.setEnabled(true);
                }
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.MUCOSA.value().equals(ddFormaClinica.getComponentValue()))) {
                    ddPresencaLesaoCutanea.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.NAO.value());
                    ddPresencaLesaoMucosa.setComponentValue(InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.SIM.value());
                    ddCicatrizerCutaneas.setEnabled(false);
                }
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.FormaClinicaEnum.IGNORADO.value().equals(ddFormaClinica.getComponentValue()))) {
                    ddPresencaLesaoCutanea.limpar(target);
                    ddPresencaLesaoMucosa.limpar(target);
                    ddCicatrizerCutaneas.limpar(target);
                }
                target.add(ddPresencaLesaoCutanea, ddPresencaLesaoMucosa, ddCicatrizerCutaneas);
            }
        });
    }

    private void criarTratamento(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerTratamento = new WebMarkupContainer("containerTratamento");
        containerTratamento.setOutputMarkupId(true);

        dataInicioTratamento = new DateChooser(path(proxy.getDataInicioTratamento()));
        ddDrogaInicialAdministrada = DropDownUtil.getIEnumDropDown(path(proxy.getDrogaInicialAdministrada()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.DrogaInicialAdministradaEnum.values(), true);
        peso = new InputField(path(proxy.getPeso()));
        ddDosePrescrita = DropDownUtil.getIEnumDropDown(path(proxy.getDosePrescrita()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.DosePrescritaEnum.values(), true);
        numAmpolas = new InputField(path(proxy.getNumeroAmpolasPrescritas()));
        ddOutraDroga = DropDownUtil.getIEnumDropDown(path(proxy.getOutraDrogaUtilizada()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.DrogaUtilizadaEnum.values(), true);

        containerTratamento.add(dataInicioTratamento, ddDrogaInicialAdministrada, peso,
                ddDosePrescrita, numAmpolas, ddOutraDroga);
        getContainerInformacoesComplementares().add(containerTratamento);
    }

    private void carregarTratamento() {
        FichaInvestigacaoAgravoHelper.enableDisableInput(peso, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddDosePrescrita, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableInput(numAmpolas, false, false, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddOutraDroga, false, false, null);

        ddDrogaInicialAdministrada.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.DrogaInicialAdministradaEnum.ANTIMONIAL_PENTAVALENTE.value().equals(ddDrogaInicialAdministrada.getComponentValue()))) {
                    peso.setEnabled(true);
                    ddClassificacaoEpidemiologica.setEnabled(true);
                    ddDosePrescrita.setEnabled(true);
                    numAmpolas.setEnabled(true);
                    ddOutraDroga.setEnabled(true);
                } else {
                    peso.setEnabled(false);
                    ddClassificacaoEpidemiologica.setEnabled(false);
                    ddDosePrescrita.setEnabled(false);
                    numAmpolas.setEnabled(false);
                    ddOutraDroga.setEnabled(false);
                }

                target.add(peso, ddClassificacaoEpidemiologica, ddDosePrescrita,
                        numAmpolas, ddOutraDroga);
            }
        });
    }

    private void criarLocalInfeccao(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerLocalInfeccao = new WebMarkupContainer("containerLocalInfeccao");
        containerLocalInfeccao.setOutputMarkupId(true);

        radioGroupCasoAutoctone = new RadioButtonGroup(path(proxy.getCasoAutoctone()));
        radioGroupCasoAutoctone.setRequired(true);
        createRadioSimNaoIgnoradoIndeterminado(radioGroupCasoAutoctone, containerLocalInfeccao, true, false, false, false);

        autoCompleteCidadeLocalInfeccao = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalInfeccao()));
        codMunicipioLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getCodigo()));
        estadoLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getEstado().getSigla()));

        autoCompletePaisLocalInfeccao = new AutoCompleteConsultaPais(path(proxy.getPaisLocalInfeccao()));
        distritoLocalInfeccao = new InputField(path(proxy.getDistritoLocalInfeccao()));
        bairroLocalInfeccao = new InputField(path(proxy.getBairroLocalInfeccao()));

        containerLocalInfeccao.add(
                radioGroupCasoAutoctone,
                autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao
        );

        getContainerInformacoesComplementares().add(radioGroupCasoAutoctone, containerLocalInfeccao);
    }

    private void carregarLocalInfeccao() {

        if (investigacaoAgravoLeishmanioseTegumentarAmericana.getCidadeLocalInfeccao() == null){
            autoCompleteCidadeLocalInfeccao.setEnabled(false);
            codMunicipioLocalInfeccao.setEnabled(false);
            estadoLocalInfeccao.setEnabled(false);
        }
        if (investigacaoAgravoLeishmanioseTegumentarAmericana.getPaisLocalInfeccao() == null){
            autoCompletePaisLocalInfeccao.setEnabled(false);
        }
        distritoLocalInfeccao.setEnabled(true);
        bairroLocalInfeccao.setEnabled(true);

        autoCompletePaisLocalInfeccao.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoLocalInfeccao.limpar(target);
                    autoCompleteCidadeLocalInfeccao.limpar(target);
                    codMunicipioLocalInfeccao.limpar(target);
                }
            }
        });
        autoCompletePaisLocalInfeccao.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteCidadeLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioLocalInfeccao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoLocalInfeccao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioLocalInfeccao, estadoLocalInfeccao);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);
            }
        });
    }

    private void criarConclusao(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddCriterioConfirmacao = DropDownUtil.getIEnumDropDown(path(proxy.getCriterioConfirmacao()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.CriterioConfirmacaoEnum.values(), true);
        ddClassificacaoEpidemiologica = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoEpidemiologica()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.ClassificacaoEpidemiologicaEnum.values(), true);
        ddDoencaRelacionadaTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaRelacionadaTrabalho()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.SimNaoEnum.getSimNaoIgnorado(), true);
        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.EvolucaoCasoEnum.values(), true);
        dataObito = new DateChooser(path(proxy.getDataObito()));

        containerConclusao.add(ddCriterioConfirmacao, ddClassificacaoEpidemiologica, ddDoencaRelacionadaTrabalho,
                ddEvolucaoCaso, dataObito
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddCriterioConfirmacao, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddClassificacaoEpidemiologica, !isModoLeitura(), true, null);
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, false, false, null);

        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dataObito.setEnabled(!isModoLeitura() &&
                        ((InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.EvolucaoCasoEnum.OBITO_POR_LTA.value().equals(ddEvolucaoCaso.getComponentValue()))
                                || (InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.EvolucaoCasoEnum.OBITO_POR_OUTRAS_CAUSAS.value().equals(ddEvolucaoCaso.getComponentValue()))));

                target.add(dataObito);
            }
        });
    }

    private void criarDeslocamento() {

        InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento proxy = on(InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento.class);
        modelDeslocamento = new CompoundPropertyModel(new InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento());
        containerDeslocamento = new WebMarkupContainer("containerDeslocamento", modelDeslocamento);
        containerDeslocamento.setOutputMarkupId(true);

        dataDeslocamento = new DateChooser(path(proxy.getDataDeslocamento()));
        dataDeslocamento.addAjaxUpdateValue();
        autoCompleteCidadeDeslocamento = new AutoCompleteConsultaCidade(path(proxy.getCidadeDeslocamento()));
        estadoLocalDeslocamento = new DisabledInputField(path(proxy.getCidadeDeslocamento().getEstado().getSigla()));
        estadoLocalDeslocamento.addAjaxUpdateValue();
        autoCompleteCidadeDeslocamento.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                target.add(estadoLocalDeslocamento);
            }
        });
        autoCompletePaisDeslocamento = new AutoCompleteConsultaPais(path(proxy.getPaisDeslocamento()));

        AbstractAjaxButton btnAdicionarDeslocamento = new AbstractAjaxButton("btnAdicionarDeslocamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                btnAdicionarDeslocamento(target);
            }
        };
        btnAdicionarDeslocamento.setDefaultFormProcessing(false);
        tblDeslocamento = new Table("tblDeslocamento", getColumns(), getCollectionProvider());
        tblDeslocamento.populate();


        containerDeslocamento.add(btnAdicionarDeslocamento, tblDeslocamento, dataDeslocamento, autoCompleteCidadeDeslocamento,
                estadoLocalDeslocamento, autoCompletePaisDeslocamento);
        getContainerInformacoesComplementares().add(containerDeslocamento);
    }

    private void btnAdicionarDeslocamento(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento deslocamento = modelDeslocamento.getObject();

        if (deslocamento.getDataDeslocamento() == null) {
            throw new ValidacaoException(bundle("msgInformeData"));
        }
        if (deslocamento.getPaisDeslocamento() == null) {
            throw new ValidacaoException(bundle("msgInformePais"));
        }
        if (deslocamento.getCidadeDeslocamento() == null) {
            throw new ValidacaoException(bundle("informeCidade"));
        }

        deslocamentoList.add(deslocamento);
        tblDeslocamento.update(target);
        tblDeslocamento.populate();

        modelDeslocamento.setObject(new InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento());
        ComponentUtils.limparContainer(containerDeslocamento, target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento proxy = on(InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("data"), proxy.getDataDeslocamento()));
        columns.add(createColumn(bundle("municipio"), proxy.getCidadeDeslocamento().getDescricao()));
        columns.add(createColumn(bundle("uf"), proxy.getCidadeDeslocamento().getEstado().getSigla()));
        columns.add(createColumn(bundle("pais"), proxy.getPaisDeslocamento().getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento>() {
            @Override
            public void customizeColumn(InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, InvestigacaoAgravoLeishmanioseTegumentarAmericanaDeslocamento modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblDeslocamento, deslocamentoList, modelObject);
                        tblDeslocamento.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (deslocamentoList == null) {
                    deslocamentoList = new ArrayList<>();
                }
                return deslocamentoList;
            }
        };
    }

    private void criarObservacoes(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoLeishmanioseTegumentarAmericana proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }


    private void createRadioSimNaoIgnoradoIndeterminado(
            RadioButtonGroup radioGroup, WebMarkupContainer containerTarget,
            boolean simHabilita, boolean naoHabilita, boolean indeterminado, boolean ignorado
    ) {
        radioGroup.add(new AjaxRadio("sim", new Model((InvestigacaoAgravoDengueZikaChikungunya.SimNaoEnum.SIM.getValue()))) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {

                autoCompleteCidadeLocalInfeccao.setEnabled(naoHabilita);
                codMunicipioLocalInfeccao.setEnabled(naoHabilita);
                estadoLocalInfeccao.setEnabled(naoHabilita);
                autoCompletePaisLocalInfeccao.setEnabled(naoHabilita);
                distritoLocalInfeccao.setEnabled(simHabilita);
                bairroLocalInfeccao.setEnabled(simHabilita);

                autoCompleteCidadeLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);

                target.add(autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                        autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao);
            }
        });

        radioGroup.add(new AjaxRadio("nao", new Model((InvestigacaoAgravoDengueZikaChikungunya.SimNaoEnum.NAO.getValue()))) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                autoCompleteCidadeLocalInfeccao.setEnabled(simHabilita);
                codMunicipioLocalInfeccao.setEnabled(simHabilita);
                estadoLocalInfeccao.setEnabled(simHabilita);
                autoCompletePaisLocalInfeccao.setEnabled(simHabilita);
                distritoLocalInfeccao.setEnabled(simHabilita);
                bairroLocalInfeccao.setEnabled(simHabilita);

                autoCompleteCidadeLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);

                target.add(autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                        autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao);
            }
        });

        radioGroup.add(new AjaxRadio("indeterminado", new Model((InvestigacaoAgravoDengueZikaChikungunya.SimNaoEnum.INDETERMINADO.getValue()))) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                containerTarget.setEnabled(indeterminado);
                target.add(containerTarget);
            }
        });

    }


}
