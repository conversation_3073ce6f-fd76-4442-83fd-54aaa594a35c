package br.com.celk.view.unidadesaude.classificacaocids;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.classificacaocids.customize.CustomizeConsultaClassificacaoCids;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import ch.lambdaj.Lambda;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaClassificacaoCids extends ConsultaPage<ClassificacaoCids, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("descricao"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(ClassificacaoCids.class);
        ClassificacaoCids proxy = Lambda.on(ClassificacaoCids.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(bundle("descricao"), CoreMethods.path(proxy.getDescricao())));
        return columns;
    }
    
     private CustomColumn<ClassificacaoCids> getCustomColumn(){
        return new CustomColumn<ClassificacaoCids>() {

            @Override
            public Component getComponent(String componentId, final ClassificacaoCids rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                     @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroClassificacaoCids(rowObject,false,true));
                    }

                   @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroClassificacaoCids(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
         return new CustomizeConsultaPagerProvider(new CustomizeConsultaClassificacaoCids()) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ClassificacaoCids.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
         List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoCids.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroClassificacaoCids.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaClassificacaoCids");
    }
    
}
