package br.com.celk.view.consorcio.tipomovimentacao.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.consorcio.tipomovimentacao.customize.CustomizeConsultaTipoMovimentacao;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.consorcio.TipoMovimentacao;
import br.com.ksisolucoes.vo.consorcio.TipoMovimentacao.TipoMovimento;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaTipoMovimentacao extends PnlConsulta<TipoMovimentacao> {

    private TipoMovimentacao.TipoMovimento tipoMovimento;
    
    public PnlConsultaTipoMovimentacao(String id) {
        super(id);
    }

    public PnlConsultaTipoMovimentacao(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaTipoMovimentacao(String id, IModel<TipoMovimentacao> model) {
        super(id, model);
    }

    public PnlConsultaTipoMovimentacao(String id, IModel<TipoMovimentacao> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaTipoMovimentacao();
            }

            @Override
            public void customQuery(ICustomizeConsultaQuery customizeConsultaQuery) {
                ((CustomizeConsultaTipoMovimentacao)customizeConsultaQuery).setTipoMovimento(tipoMovimento);
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tiposMovimentacao");
    }

    public PnlConsultaTipoMovimentacao setTipoMovimento(TipoMovimento tipoMovimento) {
        this.tipoMovimento = tipoMovimento;
        return this;
    }

}
