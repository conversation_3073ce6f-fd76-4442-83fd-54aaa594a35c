package br.com.celk.view.materiais.permissaogrupoestoque;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.controle.grupo.autocomplete.AutoCompleteConsultaGrupo;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoqueItem;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroPermissaoGrupoEstoquePage extends CadastroPage<PermissaoGrupoEstoque> {

    private WebMarkupContainer containerGrupo;
    private WebMarkupContainer containerSubGrupo;
    private WebMarkupContainer containerProduto;

    private DropDown<GrupoProduto> dropDownGrupoProdutoOnly;
    private GrupoProduto grupoProdutoOnly;

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private GrupoProduto grupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private SubGrupo subGrupo;

    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Produto produto;

    private List<PermissaoGrupoEstoqueItem> lstPermissaoGrupoEstoqueItem;
    private Table tblPermissao;

    public CadastroPermissaoGrupoEstoquePage() {
    }

    public CadastroPermissaoGrupoEstoquePage(PermissaoGrupoEstoque object) {
        super(object);
    }

    public CadastroPermissaoGrupoEstoquePage(PermissaoGrupoEstoque object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<PermissaoGrupoEstoque> form) {

        form.add(new AutoCompleteConsultaGrupo("funcao", true).setEnabled(false));
        form.add(DropDownUtil.getIEnumDropDown("tipoPermissao", PermissaoGrupoEstoque.TipoPermissao.values(), true, true).setEnabled(false));

        form.add(containerGrupo = new WebMarkupContainer("containerGrupo"));
        containerGrupo.add(getDropDownGrupoProdutoOnly("grupoProdutoOnly"));
        containerGrupo.setOutputMarkupId(true);

        form.add(containerSubGrupo = new WebMarkupContainer("containerSubGrupo"));
        containerSubGrupo.add(getDropDownGrupo("grupoProduto"));
        containerSubGrupo.add(getDropDownSubGrupo("subGrupo"));
        containerSubGrupo.setOutputMarkupId(true);

        form.add(containerProduto = new WebMarkupContainer("containerProduto"));
        containerProduto.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto", new PropertyModel(this, "produto")));
        containerProduto.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(tblPermissao = new Table("tblPermissao", getColumns(), getCollectionProvider()));
        tblPermissao.populate();

        carregarItens();
        enableContainers();
    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        validarAdicionar();
        PermissaoGrupoEstoqueItem item = new PermissaoGrupoEstoqueItem();
        item.setFuncao(getForm().getModelObject().getFuncao());
        item.setGrupoProduto(grupoProdutoOnly);
        item.setProduto(produto);
        item.setSubGrupo(subGrupo);
        item.setTipoPermissao(getForm().getModelObject().getTipoPermissao());

        lstPermissaoGrupoEstoqueItem.add(item);

        dropDownGrupoProduto.limpar(target);
        dropDownGrupoProdutoOnly.limpar(target);
        dropDownSubGrupo.limpar(target);
        autoCompleteConsultaProduto.limpar(target);

        tblPermissao.populate();
        tblPermissao.update(target);

        target.add(containerGrupo);
        target.add(containerProduto);
        target.add(containerSubGrupo);
    }

    public void validarAdicionar() throws ValidacaoException {
        if (PermissaoGrupoEstoque.TipoPermissao.GRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            if (grupoProdutoOnly == null) {
                throw new ValidacaoException(bundle("campoGrupoObrigatorio"));
            }
            for (PermissaoGrupoEstoqueItem item : lstPermissaoGrupoEstoqueItem) {
                if (grupoProdutoOnly.equals(item.getGrupoProduto())) {
                    throw new ValidacaoException(bundle("msgGrupoProdutoAdicionado"));
                }
            }
        } else if (PermissaoGrupoEstoque.TipoPermissao.SUBGRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            if (grupoProduto == null) {
                throw new ValidacaoException(bundle("campoGrupoObrigatorio"));
            }
            if (subGrupo == null) {
                throw new ValidacaoException(bundle("campoSubGrupoObrigatorio"));
            }
            for (PermissaoGrupoEstoqueItem item : lstPermissaoGrupoEstoqueItem) {
                if (subGrupo.equals(item.getSubGrupo())) {
                    throw new ValidacaoException(bundle("msgSubGrupoAdicionado"));
                }
            }
        } else if (PermissaoGrupoEstoque.TipoPermissao.PRODUTO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            if (produto == null) {
                throw new ValidacaoException(bundle("campoProdutoObrigatorio"));
            }
            for (PermissaoGrupoEstoqueItem item : lstPermissaoGrupoEstoqueItem) {
                if (produto.equals(item.getProduto())) {
                    throw new ValidacaoException(bundle("msgProdutoAdicionado"));
                }
            }
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (lstPermissaoGrupoEstoqueItem == null) {
                    lstPermissaoGrupoEstoqueItem = new ArrayList<PermissaoGrupoEstoqueItem>();
                }
                return lstPermissaoGrupoEstoqueItem;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PermissaoGrupoEstoqueItem proxy = on(PermissaoGrupoEstoqueItem.class);

        columns.add(getActionColumn());
        if (PermissaoGrupoEstoque.TipoPermissao.GRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            columns.add(createColumn(bundle("grupo"), proxy.getGrupoProduto().getDescricao()));
        } else if (PermissaoGrupoEstoque.TipoPermissao.SUBGRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            columns.add(createColumn(bundle("grupo"), proxy.getSubGrupo().getRoGrupoProduto().getDescricao()));
            columns.add(createColumn(bundle("sub_grupo"), proxy.getSubGrupo().getDescricao()));
        } else if (PermissaoGrupoEstoque.TipoPermissao.PRODUTO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            columns.add(createColumn(bundle("produto"), proxy.getProduto()));
        }

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PermissaoGrupoEstoqueItem>() {
            @Override
            public void customizeColumn(final PermissaoGrupoEstoqueItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<PermissaoGrupoEstoqueItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, PermissaoGrupoEstoqueItem modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < lstPermissaoGrupoEstoqueItem.size(); i++) {
                            PermissaoGrupoEstoqueItem item = lstPermissaoGrupoEstoqueItem.get(i);
                            if (item == rowObject) {
                                lstPermissaoGrupoEstoqueItem.remove(i);
                            }
                        }
                        tblPermissao.populate();
                        tblPermissao.update(target);
                    }
                });
            }
        };
    }

    @Override
    public Class<PermissaoGrupoEstoque> getReferenceClass() {
        return PermissaoGrupoEstoque.class;
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id, new PropertyModel(this, id));

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();

                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, "");
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, "");

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProduto;
    }

    private DropDown<GrupoProduto> getDropDownGrupoProdutoOnly(String id) {
        if (this.dropDownGrupoProdutoOnly == null) {
            this.dropDownGrupoProdutoOnly = new DropDown<GrupoProduto>(id, new PropertyModel(this, id));

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProdutoOnly.addChoice(null, "");

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProdutoOnly.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }

        return this.dropDownGrupoProdutoOnly;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo(String id) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id, new PropertyModel(this, id));

            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    public void carregarItens() {
        if (getForm().getModelObject().getCodigo() != null) {
            lstPermissaoGrupoEstoqueItem = LoadManager.getInstance(PermissaoGrupoEstoqueItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(PermissaoGrupoEstoqueItem.PROP_PERMISSAO_GRUPO_ESTOQUE, getForm().getModelObject()))
                    .addProperties(new HQLProperties(PermissaoGrupoEstoqueItem.class).getProperties())
                    .addProperties(new HQLProperties(GrupoProduto.class, PermissaoGrupoEstoqueItem.PROP_GRUPO_PRODUTO).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, PermissaoGrupoEstoqueItem.PROP_SUB_GRUPO).getProperties())
                    .addProperties(new HQLProperties(Produto.class, PermissaoGrupoEstoqueItem.PROP_PRODUTO).getProperties())
                    .start().getList();
        }
    }

    public void enableContainers() {
        if (PermissaoGrupoEstoque.TipoPermissao.GRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            containerGrupo.setVisible(true);
            containerSubGrupo.setVisible(false);
            containerProduto.setVisible(false);
        } else if (PermissaoGrupoEstoque.TipoPermissao.SUBGRUPO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            containerGrupo.setVisible(false);
            containerSubGrupo.setVisible(true);
            containerProduto.setVisible(false);
        } else if (PermissaoGrupoEstoque.TipoPermissao.PRODUTO.value().equals(getForm().getModelObject().getTipoPermissao())) {
            containerGrupo.setVisible(false);
            containerSubGrupo.setVisible(false);
            containerProduto.setVisible(true);
        }
    }

    @Override
    public Object salvar(PermissaoGrupoEstoque object) throws DAOException, ValidacaoException {
        if (CollectionUtils.isAllEmpty(lstPermissaoGrupoEstoqueItem)) {
            throw new ValidacaoException(bundle("msgObrigatorioUmItem"));
        }
        getForm().setModelObject(BOFactoryWicket.getBO(MaterialBasicoFacade.class).SalvarPermissaoGrupoEstoque(object, lstPermissaoGrupoEstoqueItem));
        return getForm().getModelObject();
    }

    @Override
    public Class getResponsePage() {
        if (getForm().getModelObject().getCodigo() != null) {
            return ConsultaPermissaoGrupoEstoquePage.class;
        } else {
            return CadastroPermissaoGrupoEstoquePage.class;
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroPermissaoMovimentacao");
    }
}
