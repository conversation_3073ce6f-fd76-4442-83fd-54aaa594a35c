package br.com.celk.view.unidadesaude.esus.cds.atividadecoletiva;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaAtividadeColetivaPage extends ConsultaPage<AtividadeGrupoProfissional, List<BuilderQueryCustom.QueryParameter>> {

    private List<Empresa> unidade;
    private DatePeriod periodo;
    private Profissional profissional;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    @Override
    public void initForm(Form form) {
        boolean permissaoEmpresa = isActionPermitted(Permissions.EMPRESA);

        form.setDefaultModel(new CompoundPropertyModel(this));

        AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("unidade");
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!permissaoEmpresa)
                .setMultiplaSelecao(true);
        form.add(autoCompleteConsultaEmpresa);
        form.add(new PnlDatePeriod("periodo"));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));
        if (!isActionPermitted(Permissions.PROFISSIONAL)) {
            autoCompleteConsultaProfissional.setModelObject(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());
            autoCompleteConsultaProfissional.setEnabled(false);
        }
        if (!permissaoEmpresa) {
            try {
                carregarUnidades();
            } catch (SGKException ex) {
                error(ex.getMessage());
            }
        }
    }

    private void carregarUnidades() throws DAOException, ValidacaoException {
        List<Long> empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresas))
                    .start().getList();
            unidade = empresaList;
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AtividadeGrupoProfissional proxy = on(AtividadeGrupoProfissional.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("empresa"), proxy.getAtividadeGrupo().getEmpresa().getDescricao(), proxy.getAtividadeGrupo().getEmpresa().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("local"), proxy.getAtividadeGrupo().getLocalAtividadeGrupo().getDescricao()));
        columns.add(createSortableColumn(bundle("tipoAtividade"), proxy.getAtividadeGrupo().getTipoAtividadeGrupo().getDescricao()));
        columns.add(createSortableColumn(bundle("data"), proxy.getAtividadeGrupo().getDataInicio()));
        columns.add(createColumn(bundle("profissionalResponsavel"), proxy.getProfissional().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AtividadeGrupoProfissional>() {

            @Override
            public void customizeColumn(AtividadeGrupoProfissional rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AtividadeGrupoProfissional>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtividadeGrupoProfissional modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAtividadeColetivaPage(modelObject.getAtividadeGrupo()));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtividadeGrupoProfissional>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtividadeGrupoProfissional modelObject) throws ValidacaoException, DAOException {
                        modelObject.getAtividadeGrupo().setSituacao(AtividadeGrupo.SITUACAO_CANCELADA);
                        modelObject.getAtividadeGrupo().setDataCancelamento(DataUtil.getDataAtual());
                        BOFactory.getBO(CadastroFacade.class).save(modelObject.getAtividadeGrupo());
                        getPageableTable().update(target);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AtividadeGrupoProfissional>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtividadeGrupoProfissional modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAtividadeColetivaPage(modelObject.getAtividadeGrupo(), false));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return AtividadeGrupoProfissional.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(AtividadeGrupoProfissional.class).getProperties(),
                        new HQLProperties(AtividadeGrupo.class, AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO).getProperties(),
                        new HQLProperties(Profissional.class, AtividadeGrupoProfissional.PROP_PROFISSIONAL).getProperties(),
                        new String[]{
                            VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_EMPRESA, Empresa.PROP_CNES),
                            VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_EMPRESA, Empresa.PROP_CNPJ),
                            VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_LOCAL_ATIVIDADE_GRUPO, LocalAtividadeGrupo.PROP_TIPO_LOCAL),
                            VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_TIPO_ATIVIDADE_GRUPO, TipoAtividadeGrupo.PROP_FLAG_REUNIAO)
                        }
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_DATA_CADASTRO), false);
            }

        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_DATA_CADASTRO), periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_ORIGEM), AtividadeGrupo.ORIGEM_FICHA_ATIVIDADE_COLETIVA));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IN, unidade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_ATIVIDADE_GRUPO, AtividadeGrupo.PROP_SITUACAO), AtividadeGrupo.SITUACAO_CONCLUIDA));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_FLAG_RESPONSAVEL), RepositoryComponentDefault.SIM_LONG));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtividadeGrupoProfissional.PROP_PROFISSIONAL), profissional));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAtividadeColetivaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAtividadeGrupo");
    }

}
