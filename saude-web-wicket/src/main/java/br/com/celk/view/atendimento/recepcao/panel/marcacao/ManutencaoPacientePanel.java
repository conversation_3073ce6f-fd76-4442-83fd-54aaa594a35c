package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.DefaultRecepcaoPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.cadsus.usuariocadsus.CadastroPacienteDTO;
import br.com.celk.view.cadsus.usuariocadsus.customcolumn.CarregarDTOCadastroPaciente;
import br.com.celk.view.cadsus.usuariocadsus.tabbedpanel.CadastroPacienteTabbedPanel;
import br.com.celk.view.cadsus.usuariocadsus.tabbedpanel.DadosPacienteTab;
import br.com.celk.view.cadsus.usuariocadsus.tabbedpanel.DocumentosTab;
import br.com.celk.view.cadsus.usuariocadsus.tabbedpanel.EnderecoTab;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Deprecated
public abstract class ManutencaoPacientePanel extends RecepcaoCadastroPanel implements IAppletAction{

    private final Long codigoPaciente;
    private final DefaultRecepcaoPanel marcacaoPanel;
    private CadastroPacienteTabbedPanel cadastroPacienteTabbedPanel;
    private DadosPacienteTab dadosPacienteTab;
    
    public ManutencaoPacientePanel(String id, DefaultRecepcaoPanel marcacaoPanel, Long codigoPaciente) {
        super(id, bundle("cadastroPaciente"));
        this.codigoPaciente = codigoPaciente;
        this.marcacaoPanel = marcacaoPanel;
    }
    
    
    @Override
    public void postConstruct() {
        try {
            super.postConstruct();
            CadastroPacienteDTO cadastroPacienteDTO = carregarPaciente();
            if(cadastroPacienteDTO == null){
                cadastroPacienteDTO = getNewInstance();
            }
            
            List<ITab> tabs = new ArrayList<ITab>();
            tabs.add(new CadastroTab<CadastroPacienteDTO>(cadastroPacienteDTO) {
                
                @Override
                public ITabPanel<CadastroPacienteDTO> newTabPanel(String panelId,CadastroPacienteDTO cadastroPacienteDTO) {
                    return dadosPacienteTab = new DadosPacienteTab(panelId, cadastroPacienteDTO);
                }
            });
            tabs.add(new CadastroTab<CadastroPacienteDTO>(cadastroPacienteDTO) {
                
                @Override
                public ITabPanel<CadastroPacienteDTO> newTabPanel(String panelId,CadastroPacienteDTO cadastroPacienteDTO) {
                    return new DocumentosTab(panelId, cadastroPacienteDTO);
                }
            });
            tabs.add(new CadastroTab<CadastroPacienteDTO>(cadastroPacienteDTO) {
                
                @Override
                public ITabPanel<CadastroPacienteDTO> newTabPanel(String panelId,CadastroPacienteDTO cadastroPacienteDTO) {
                    return new EnderecoTab(panelId, cadastroPacienteDTO);
                }
            });
            add(cadastroPacienteTabbedPanel = new CadastroPacienteTabbedPanel("wizard", cadastroPacienteDTO, false, tabs){
                @Override
                protected Component newButtonBar(String id) {
                    return new ManutencaoCadastroButtonBar(id, getRecepcaoController(), marcacaoPanel) {

                        @Override
                        public void salvarAction(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                            UsuarioCadsus usuarioCadsus = (UsuarioCadsus) salvar(object);
                            getRecepcaoController().changePanel(target, getResponsePanel(getRecepcaoController().panelId(), usuarioCadsus.getCodigo()));
                        }
                    };
                }
            });
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    public abstract RecepcaoCadastroPanel getResponsePanel(String panelId, Long codigoPaciente);
    
    private CadastroPacienteDTO getNewInstance() {
        CadastroPacienteDTO object = new CadastroPacienteDTO();
        
        object.setUsuarioCadsus(new UsuarioCadsus());
        object.setDocumentoEstrangeiro(new UsuarioCadsusDocumento());
        if (object.getDocumentoEstrangeiro()==null) {
            object.setDocumentoEstrangeiro(new UsuarioCadsusDocumento());
        }
        object.setDocumentoCertidao(new UsuarioCadsusDocumento());
        object.getDocumentoCertidao().setTipoDocumento(new TipoDocumentoUsuario());
        object.setDocumentoIdentidade(new UsuarioCadsusDocumento());
        object.getDocumentoIdentidade().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG));
        object.setDocumentoTituloEleitor(new UsuarioCadsusDocumento());
        object.getDocumentoTituloEleitor().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_TITULO_ELEITOR));
        object.setDocumentoCtps(new UsuarioCadsusDocumento());
        object.getDocumentoCtps().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CTPS));
        object.setDocumentoPis(new UsuarioCadsusDocumento());
        object.getDocumentoPis().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_PIS));
        object.setDocumentoEstrangeiro(new UsuarioCadsusDocumento());
        
        return object;
    }

    private CadastroPacienteDTO carregarPaciente() throws DAOException, ValidacaoException {
        CadastroPacienteDTO dto = new CadastroPacienteDTO();
        
        UsuarioCadsus usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
            .setId(codigoPaciente)
            .start().getVO();
        
        dto.setUsuarioCadsus(usuarioCadsus);
        
        return CarregarDTOCadastroPaciente.carregarDTO(dto);
    }

    @Override
    public void search(AjaxRequestTarget target, String key) {
        cadastroPacienteTabbedPanel.search(target, key);
    }

    @Override
    public void register(AjaxRequestTarget target, String key) {
        cadastroPacienteTabbedPanel.register(target, key);
        dadosPacienteTab.getBiometriaActionPanel().setEnabled(true);
        target.add(dadosPacienteTab.getBiometriaActionPanel());
    }
}
