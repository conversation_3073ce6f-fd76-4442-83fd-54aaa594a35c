package br.com.celk.view.agenda.agendamento;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;

/**
 * <AUTHOR>
 */
public abstract class DlgSelecionarNotificacaoRegulacao<T> extends Window {

    private T object;
    private PnlSelecionarNotificacaoRegulacao pnlSelecionarNotificacaoRegulacao;

    public DlgSelecionarNotificacaoRegulacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("formaNotificacao"));

        setInitialHeight(75);
        setInitialWidth(320);
        setResizable(false);

        setContent(pnlSelecionarNotificacaoRegulacao = new PnlSelecionarNotificacaoRegulacao(getContentId()) {
            @Override
            public void onNotificarRegulacao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgSelecionarNotificacaoRegulacao.this.onNotificarRegulacao(target, object);
            }

            @Override
            public void onNotificarUnidade(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgSelecionarNotificacaoRegulacao.this.onNotificarUnidade(target, object);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget art) {
                return false;
            }
        });
    }

    public abstract void onNotificarRegulacao(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException;

    public abstract void onNotificarUnidade(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, T object) {
        super.show(target);
        this.object = object;
    }
}
