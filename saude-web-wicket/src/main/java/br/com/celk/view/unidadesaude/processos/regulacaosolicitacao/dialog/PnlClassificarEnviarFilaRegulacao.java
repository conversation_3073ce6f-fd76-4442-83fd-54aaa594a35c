package br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.msdropdown.MsDropDown;
import br.com.celk.component.msdropdown.MsItem;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.unidadesaude.justificativapriorizacao.autocomplete.AutoCompleteConsultaJustificativaPriorizacao;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.JustificativaPriorizacao;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SubClassificacaoFilaEspera;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.SharedResourceReference;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlClassificarEnviarFilaRegulacao extends Panel {

    private String observacaoAutorizador;
    private ClassificacaoRisco classificacaoRisco;
    private SubClassificacaoFilaEspera subClassificacaoFilaEspera;
    private JustificativaPriorizacao justificativaPriorizacao;
    private CompoundPropertyModel<SolicitacaoAgendamento> model;
    private MsDropDown<ClassificacaoRisco> cbxClassificaoRisco;
    private AbstractAjaxButton btnConfirmar;
    private DropDown<SubClassificacaoFilaEspera> dropDownSubFilaEspera;

    public PnlClassificarEnviarFilaRegulacao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form<SolicitacaoAgendamento> form = new Form<>("form", model = new CompoundPropertyModel(new SolicitacaoAgendamento()));
        SolicitacaoAgendamento proxy = on(SolicitacaoAgendamento.class);

        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getDataSolicitacao())));
        form.add(getDropDownSubRisco().add(new TempBehaviorV2()));
        form.add(getCbxClassificacaoRisco(proxy.getClassificacaoRisco()));
        form.add(new AutoCompleteConsultaJustificativaPriorizacao(path(proxy.getJustificativaPriorizacao()), new PropertyModel(this, "justificativaPriorizacao")));

        form.add(new InputArea(path(proxy.getObservacaoAutorizador()), new PropertyModel(this, "observacaoAutorizador")));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarConfirmacaoEnvio();
                onConfirmar(target, model.getObject().getCodigo(), classificacaoRisco, observacaoAutorizador, justificativaPriorizacao, subClassificacaoFilaEspera);
            }
        });

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmarAgendar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarConfirmacaoEnvio();
                onConfirmarAgendar(target, model.getObject().getCodigo(), classificacaoRisco, observacaoAutorizador, subClassificacaoFilaEspera);
            }
        });

        AbstractAjaxButton btnFechar;
        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        add(form);
    }

    private DropDown<SubClassificacaoFilaEspera> getDropDownSubRisco(){
        dropDownSubFilaEspera = new DropDown<SubClassificacaoFilaEspera>("subClassificacaoFilaEspera", new PropertyModel(this, "subClassificacaoFilaEspera"));
        dropDownSubFilaEspera.addChoice(null,"");
        return dropDownSubFilaEspera;
    }

    private MsDropDown<ClassificacaoRisco> getCbxClassificacaoRisco(ClassificacaoRisco classificacaoRisco) {
        if (cbxClassificaoRisco == null) {
            String id = path(classificacaoRisco);
            cbxClassificaoRisco = new MsDropDown<ClassificacaoRisco>(id, new PropertyModel(this, "classificacaoRisco"));
            List<ClassificacaoRisco> lstClassificacao = LoadManager.getInstance(ClassificacaoRisco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoRisco.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, BuilderQueryCustom.QueryParameter.DIFERENTE, ClassificacaoRisco.NivelGravidade.LARANJA.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();
            for (ClassificacaoRisco classificacao : lstClassificacao) {
                cbxClassificaoRisco.addChoice(new MsItem<>(classificacao, classificacao.getDescricao(), urlFor(new SharedResourceReference(Resources.class, classificacao.getCaminhoImagem()), null).toString()));
            }

            cbxClassificaoRisco.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubFilaEspera.removeAllChoices();
                    if  (target != null) {
                        carregaDropDown(target, null);
                    }
                }
            });
        }
        return cbxClassificaoRisco;
    }

    private void carregaDropDown(AjaxRequestTarget target, Long codigoSubClassificacao) {
        SubClassificacaoFilaEspera subClassificacaoAtual = null;
        dropDownSubFilaEspera.removeAllChoices(target);
        dropDownSubFilaEspera.limpar(target);
        if (classificacaoRisco != null && !ClassificacaoRisco.NivelGravidade.CINZA.value().equals(classificacaoRisco.getNivelGravidade())){
            List<SubClassificacaoFilaEspera> subClassificacaoFilaEsperaList = LoadManager.getInstance(SubClassificacaoFilaEspera.class)
                    .addProperties(new HQLProperties(SubClassificacaoFilaEspera.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(SubClassificacaoFilaEspera.PROP_CLASSIFICACAO_RISCO, cbxClassificaoRisco.getComponentValue()))
                    .addSorter(new QueryCustom.QueryCustomSorter(SubClassificacaoFilaEspera.PROP_VALOR, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(subClassificacaoFilaEsperaList)) {
                dropDownSubFilaEspera.addChoice(null,"");
                for (SubClassificacaoFilaEspera subClassificacaoFilaEspera : subClassificacaoFilaEsperaList) {
                    dropDownSubFilaEspera.addChoice(subClassificacaoFilaEspera, subClassificacaoFilaEspera.getDescricao());
                    if (codigoSubClassificacao != null && subClassificacaoFilaEspera.getValor().equals(codigoSubClassificacao)) {
                        subClassificacaoAtual = subClassificacaoFilaEspera;
                    }
                }
            }
        }
        dropDownSubFilaEspera.setModelObject(subClassificacaoAtual);
        target.add(dropDownSubFilaEspera);
    }

    private void validarConfirmacaoEnvio() throws ValidacaoException {
        if (classificacaoRisco == null || ClassificacaoRisco.NivelGravidade.CINZA.value().equals(classificacaoRisco.getNivelGravidade())) {
            throw new ValidacaoException(BundleManager.getString("msgNecessarioSelecionarClassificacao"));
        }
        if (observacaoAutorizador == null) {
            throw new ValidacaoException(BundleManager.getString("campoXObrigatorio", BundleManager.getString("motivo")));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        cbxClassificaoRisco.limpar(target);
        observacaoAutorizador = null;
    }

    public void setSolicitacaoAgendamento(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento, boolean exibirBtnSalvarAgendar) {
        this.model.setObject(solicitacaoAgendamento);
        cbxClassificaoRisco.setModelObject(solicitacaoAgendamento.getClassificacaoRisco());
        carregaDropDown(target, solicitacaoAgendamento.getValorSubclassificacaoFilaEspera());
        btnConfirmar.setVisible(exibirBtnSalvarAgendar);
        target.add(btnConfirmar);
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException;
    public abstract void onConfirmarAgendar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException;
    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
