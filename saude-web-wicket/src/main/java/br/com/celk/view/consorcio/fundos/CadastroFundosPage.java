package br.com.celk.view.consorcio.fundos;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.consorcio.FundoConsorcio;
import org.apache.wicket.markup.html.form.Form;

public class CadastroFundosPage extends CadastroPage<FundoConsorcio> {
    private boolean enabled = true;
    private InputField txtDescricao;
    private DropDown dropDownStatus;

    public CadastroFundosPage() {
        super();
    }

    public CadastroFundosPage(FundoConsorcio object) {
        super(object);
    }

    public CadastroFundosPage(FundoConsorcio object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new InputField("descricao"));
        txtDescricao.setRequired(true);
        txtDescricao.addRequiredClass();
        form.add(dropDownStatus = getDropDownStatus("situacao"));
        dropDownStatus.setEnabled(!isViewOnly());
        txtDescricao.setEnabled(!isViewOnly());
    }

    @Override
    public Class getReferenceClass() {
        return FundoConsorcio.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaFundosPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroFundoMunicipal");
    }

    private DropDown<Long> getDropDownStatus(String id) {
        if (this.dropDownStatus == null) {
            this.dropDownStatus = new DropDown<>(id);
            dropDownStatus.addChoice(FundoConsorcio.Situacao.ATIVO.value(), BundleManager.getString("ativo"));
            dropDownStatus.addChoice(FundoConsorcio.Situacao.INATIVO.value(), BundleManager.getString("inativo"));
        }

        return this.dropDownStatus;
    }
}
