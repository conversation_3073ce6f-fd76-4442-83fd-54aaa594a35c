package br.com.celk.view.materiais.medicamento.tabbedpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.RowNumberColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMedicamentoDTO;
import br.com.celk.view.materiais.tipoviamedicamento.pnl.PnlConsultaTipoViaMedicamento;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class TipoViaMedicamentoTab extends TabPanel<CadastroMedicamentoDTO> {

    private List<TipoViaMedicamento> lstTipoVia = new ArrayList<TipoViaMedicamento>();
    private TipoViaMedicamento tipoViaMedicamento;
    
    private PnlConsultaTipoViaMedicamento pnlConsultaTipoViaMedicamento;
    private Table<TipoViaMedicamento> table;

    public TipoViaMedicamentoTab(String id, CadastroMedicamentoDTO cadastroMedicamentoDTO) {
        super(id, cadastroMedicamentoDTO);
        init();
    }

    public void init() {
        add(pnlConsultaTipoViaMedicamento = new PnlConsultaTipoViaMedicamento("tipoViaMedicamento", new PropertyModel<TipoViaMedicamento>(this, "tipoViaMedicamento")));
        add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        
        add(table = new Table<TipoViaMedicamento>("table", getColumns(), getCollectionProvider()));
        

        lstTipoVia = object.getTipoViaMedicamentos();
        table.populate();
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        boolean adicionar = true;
        for (TipoViaMedicamento _tipoViaMedicamento : lstTipoVia) {
            if (_tipoViaMedicamento.getCodigo().equals(tipoViaMedicamento.getCodigo())) {
                adicionar = false;
                throw new ValidacaoException(BundleManager.getString("viaJaAdicionada"));
            }
        }
        if (adicionar) {
            lstTipoVia.add(tipoViaMedicamento);
        }
        pnlConsultaTipoViaMedicamento.limpar(target);
        table.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getTipoViaMedicamentos();
            }
        };
    }

    private List<ISortableColumn<TipoViaMedicamento>> getColumns() {
        List<ISortableColumn<TipoViaMedicamento>> columns = new ArrayList<ISortableColumn<TipoViaMedicamento>>();

        ColumnFactory columnFactory = new ColumnFactory(TipoViaMedicamento.class);

        columns.add(getCustomColumn());
        columns.add(new RowNumberColumn<TipoViaMedicamento>());
        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(TipoViaMedicamento.PROP_REFERENCIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoViaMedicamento.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<TipoViaMedicamento>() {
            @Override
            public Component getComponent(String componentId, final TipoViaMedicamento rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerTipoViaMedicamento(target, rowObject);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }
                    
                    @Override
                    public boolean isEditarVisible() {
                        return false;
                    }
                };
            }
        };
    }

    private void removerTipoViaMedicamento(AjaxRequestTarget target, TipoViaMedicamento _tipoViaMedicamento) {
        for (int i = 0; i < lstTipoVia.size(); i++) {
            if (lstTipoVia.get(i) == _tipoViaMedicamento) {
                lstTipoVia.remove(i);
            }
        }
        table.update(target);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("via");
    }
}
