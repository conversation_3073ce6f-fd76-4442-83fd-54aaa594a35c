package br.com.celk.view.unidadesaude.tipoanexo;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.RequiredTextField;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoAnexoPage extends CadastroPage<TipoAnexo> {

    public CadastroTipoAnexoPage(TipoAnexo object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoAnexoPage(TipoAnexo object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroTipoAnexoPage(PageParameters parameters) {
        super(parameters);
    }

    public CadastroTipoAnexoPage(TipoAnexo object) {
        super(object);
    }

    public CadastroTipoAnexoPage() {
        super();
    }

    @Override
    public void init(Form form) {
        TipoAnexo proxy = on(TipoAnexo.class);
        form.add(new RequiredTextField(path(proxy.getDescricao())));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagImpressaoProntuario())));
    }

    @Override
    public Class<TipoAnexo> getReferenceClass() {
        return TipoAnexo.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoAnexoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroTipoAnexo");
    }

}
