package br.com.celk.view.atendimento.recepcao.panel.confirmacaopresenca;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeQueryPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.report.recepcao.interfaces.dto.RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam;
import br.com.celk.report.recepcao.interfaces.facade.RecepcaoReportFacade;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.ConfirmarPresencaPage;
import br.com.celk.view.agenda.agendamento.dialog.DlgInformarPacienteConfirmacaoPresenca;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.recepcao.panel.confirmacaopresenca.dlg.DlgConfirmacaoPresencaComFoto;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoPresencaHospitalDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ConfirmacaoPresencaHospitalPanel extends RecepcaoCadastroPanel {

    private IPagerProvider iPagerProvider;
    private Form form;
    private PageableTable pageableTable;
    private DlgImpressaoObject dlgImpressaoFAA;
    private DlgConvenioHospital dlgConvenioHospital;
    private DlgInformarPacienteConfirmacaoPresenca dlgInformarPacienteConfirmacaoPresenca;

    private DlgConfirmacaoPresencaComFoto dlgConfirmacaoPresencaComFoto;

    public ConfirmacaoPresencaHospitalPanel(String id) {
        super(id, bundle("confirmacaoPresenca"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel(new AgendaGradeAtendimentoDTOParam()));

        form.add(new InputField("nomeUsuarioCadsus"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento"));
        form.add(getCbxSituacao());

        pageableTable = new PageableTable<AgendaGradeAtendimentoHorarioDTO>("table", getColumns(), getPagerProvider()) {
            @Override
            protected Item<AgendaGradeAtendimentoHorarioDTO> newRowItem(String id, int index, IModel<AgendaGradeAtendimentoHorarioDTO> model) {
                return new ConfirmacaoPresencaTableRow(id, index, model);
            }
        };
        form.add(pageableTable);
        pageableTable.getPagingBar().getCbxItemsPerPage().addChoice(Integer.MAX_VALUE, bundle("todos"));
        pageableTable.getPagingBar().getCbxItemsPerPage().setComponentValue(Integer.MAX_VALUE);

        ProcurarButton btnProcurar = new ProcurarButton<AgendaGradeAtendimentoDTOParam>("btnProcurar", pageableTable) {
            @Override
            public AgendaGradeAtendimentoDTOParam getParam() {
                return ConfirmacaoPresencaHospitalPanel.this.getParam();
            }
        };

        form.add(btnProcurar);
        add(form);
    }

    private AgendaGradeAtendimentoDTOParam getParam() {
        AgendaGradeAtendimentoDTOParam param = (AgendaGradeAtendimentoDTOParam) form.getModel().getObject();
        try {
            Date dataInicial = DataUtil.getDataAtual();
            Integer diasLimiteConfirmacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("diasLimiteConfirmacao");
            dataInicial = Data.removeDias(dataInicial, diasLimiteConfirmacao);

            param.setDatePeriod(Data.adjustRangeHour(dataInicial, DataUtil.getDataAtual()));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        param.setExibirCancelados(false);
        AgendaGradeAtendimentoHorarioDTO proxy = on(AgendaGradeAtendimentoHorarioDTO.class);
        param.getConfigureParam().addSorter(path(proxy.getAgendaGradeAtendimentoHorario().getNomePaciente()));
        return param;
    }

    public IPagerProvider<ICustomizeConsultaQuery, AgendaGradeAtendimentoDTOParam> getPagerProvider() {
        if (iPagerProvider == null) {
            iPagerProvider = new CustomizeQueryPagerProvider() {
                @Override
                public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentosPager(dataPaging);
                }

                @Override
                public SortParam getDefaultSort() {
                    AgendaGradeAtendimentoHorario proxy = on(AgendaGradeAtendimentoHorario.class);
                    return new SortParam(path(proxy.getStatus()), true);
                }

                @Override
                public ICustomizeConsultaQuery getCustomizeConsultaQuery() {
                    return new CustomizeConsultaAdapter();
                }
            };
        }
        return iPagerProvider;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorarioDTO>() {
            @Override
            public void customizeColumn(final AgendaGradeAtendimentoHorarioDTO rowObject) {
                String paciente = rowObject.getNomePaciente();
                if (rowObject.getUsuarioCadsus() != null && paciente != null) {
                    paciente = rowObject.getUsuarioCadsus().getNomeSocial();
                }
                ModelActionLinkPanel aConfirmar = addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                        if (dlgConfirmacaoPresencaComFoto == null) {
                            WindowUtil.addModal(target, ConfirmacaoPresencaHospitalPanel.this, dlgConfirmacaoPresencaComFoto = new DlgConfirmacaoPresencaComFoto(WindowUtil.newModalId(ConfirmacaoPresencaHospitalPanel.this)) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                                    abreDialogConvenio(target, modelObject);
                                }
                            });
                        }
                        String paciente = modelObject.getNomePaciente();
                        if (modelObject.getUsuarioCadsus() != null && modelObject.getUsuarioCadsus().getNomeSocial() != null) {
                            paciente = modelObject.getUsuarioCadsus().getNomeSocial();
                        }
                        dlgConfirmacaoPresencaComFoto.showDlg(target, bundle("confirmarAtendimentoPaciente", paciente.toUpperCase()), modelObject);
                    }
                });
                aConfirmar.setVisible(AgendaGradeAtendimentoHorario.STATUS_AGENDADO.equals(rowObject.getAgendaGradeAtendimentoHorario().getStatus()));

                ActionLinkPanel aCancelar = addAction(ActionType.CANCELAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AtendimentoFacade.class).naoCompareceuAtendimento(rowObject);
                        pageableTable.populate(target);
                    }
                });
                aCancelar.setQuestionDialogBundleKey("registrarNaoComparecimentoPaciente", paciente.toUpperCase());
                aCancelar.setTitleBundleKey("nao_compareceu");
                aCancelar.setVisible(AgendaGradeAtendimentoHorario.STATUS_AGENDADO.equals(rowObject.getAgendaGradeAtendimentoHorario().getStatus()));

                ActionLinkPanel aReverter = addAction(ActionType.REVERTER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AtendimentoFacade.class).reverterSituacaoPresencaAtendimento(rowObject);
                        pageableTable.populate(target);
                    }
                });
                aReverter.setQuestionDialogBundleKey("reverterSituacaoPaciente", paciente.toUpperCase());
                aReverter.setTitleBundleKey("reverter");
                aReverter.setVisible(AgendaGradeAtendimentoHorario.STATUS_CONCLUIDO.equals(rowObject.getAgendaGradeAtendimentoHorario().getStatus())
                        || AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(rowObject.getAgendaGradeAtendimentoHorario().getStatus()));
            }
        };
    }

    private void abreDialogConvenio(AjaxRequestTarget target, final AgendaGradeAtendimentoHorarioDTO agah) throws DAOException {

        Convenio convenioParticular = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioParticular");

        boolean validarAdiantamento = false;
        if (convenioParticular != null) {
            if (agah.getAgendaGradeAtendimentoHorario().getSubconvenio() != null
                    && convenioParticular.getCodigo().equals(agah.getAgendaGradeAtendimentoHorario().getSubconvenio().getCodigo())) {
                validarAdiantamento = true;
            } else if (agah.getAgendaGradeAtendimentoHorario().getConvenio() != null
                    && convenioParticular.getCodigo().equals(agah.getAgendaGradeAtendimentoHorario().getConvenio().getCodigo())) {
                validarAdiantamento = true;
            }
        }

        if (validarAdiantamento) {
            try {
                BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("adiantamento");
            } catch (DAOException ex) {
                Logger.getLogger(ConfirmarPresencaPage.class.getName()).log(Level.SEVERE, null, ex);
                return;
            }
        }

        getRecepcaoController().addWindow(target, dlgConvenioHospital = new DlgConvenioHospital(getRecepcaoController().newWindowId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ConfirmacaoPresencaHospitalDTO confirmacaoPresencaHospitalDTO) throws ValidacaoException, DAOException {
                if (agah.getAgendaGradeAtendimentoHorario().getUsuarioCadsus() == null) {
                    agah.getAgendaGradeAtendimentoHorario().setConvenio(confirmacaoPresencaHospitalDTO.getConvenio());
                    agah.getAgendaGradeAtendimentoHorario().setSubconvenio(confirmacaoPresencaHospitalDTO.getSubConvenio());
                    dlgInformarPacienteConfirmacaoPresenca(target, agah);
                } else {
                    agah.setAdiantamento(confirmacaoPresencaHospitalDTO.getAdiantamento());
                    agah.setValorAdiantamento(confirmacaoPresencaHospitalDTO.getValorAdiantamento());
                    agah.setFormaPagamento(confirmacaoPresencaHospitalDTO.getFormaPagamento());
                    confirmar(target, agah, confirmacaoPresencaHospitalDTO);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                dlgConvenioHospital.close(target);
            }
        });
        dlgConvenioHospital.showDialog(target, agah);
    }

    private void dlgInformarPacienteConfirmacaoPresenca(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO agendaGradeAtendimentoHorarioDTO) {
        if (dlgInformarPacienteConfirmacaoPresenca == null) {
            getRecepcaoController().addWindow(target, dlgInformarPacienteConfirmacaoPresenca = new DlgInformarPacienteConfirmacaoPresenca(getRecepcaoController().newWindowId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                    ConfirmacaoPresencaHospitalDTO confirmacaoPresencaHospitalDTO = new ConfirmacaoPresencaHospitalDTO();
                    confirmacaoPresencaHospitalDTO.setConvenio(dto.getAgendaGradeAtendimentoHorario().getConvenio());
                    confirmacaoPresencaHospitalDTO.setSubConvenio(dto.getAgendaGradeAtendimentoHorario().getSubconvenio());

                    confirmar(target, dto, confirmacaoPresencaHospitalDTO);
                }
            });
        }
        dlgInformarPacienteConfirmacaoPresenca.showDlg(target, agendaGradeAtendimentoHorarioDTO);
    }

    private void confirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO agah, ConfirmacaoPresencaHospitalDTO confirmacaoPresencaHospitalDTO) throws DAOException, ValidacaoException {
        Atendimento atendimento = BOFactoryWicket.getBO(AtendimentoFacade.class).confirmarPresencaAtendimentoHospital(agah, confirmacaoPresencaHospitalDTO);
        pageableTable.populate(target);

        if (atendimento != null) {
            impressaoFichaAtendimentoAmbulatorial(target, atendimento);
        }
    }

    private List getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorarioDTO proxy = on(AgendaGradeAtendimentoHorarioDTO.class);
        AgendaGradeAtendimentoHorario proxyQuery = on(AgendaGradeAtendimentoHorario.class);

        columns.add(getCustomActionColumn());
        columns.add(new DateColumn<AgendaGradeAtendimentoHorarioDTO>(bundle("data_atendimento"), path(proxyQuery.getDataAgendamento()), path(proxy.getAgendaGradeAtendimentoHorario().getDataAgendamento()))
                .setPattern("dd/MM/yyyy HH:mm"));
        columns.add(createSortableColumn(bundle("paciente"), proxyQuery.getUsuarioCadsus().getNome(), proxy.getAgendaGradeAtendimentoHorario().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("idade"), proxyQuery.getUsuarioCadsus().getDataNascimento(), proxy.getAgendaGradeAtendimentoHorario().getUsuarioCadsus().getDescricaoIdadeSimples()));
        columns.add(createSortableColumn(bundle("situacao"), proxyQuery.getStatus(), proxy.getAgendaGradeAtendimentoHorario().getSituacao()));
        columns.add(createSortableColumn(bundle("tipo"), proxyQuery.getTipoProcedimento().getDescricao(), proxy.getAgendaGradeAtendimentoHorario().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxyQuery.getNomeProfissional(), proxy.getAgendaGradeAtendimentoHorario().getNomeProfissional()));
        return columns;
    }

    private DropDown getCbxSituacao() {
        DropDown<Long> cbxSituacao = new DropDown<Long>("status");
        cbxSituacao.addChoice(null, BundleManager.getString("todas"));
        cbxSituacao.addChoice(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, BundleManager.getString("agendado"));
        cbxSituacao.addChoice(AgendaGradeAtendimentoHorario.STATUS_CONCLUIDO, BundleManager.getString("confirmado"));
        cbxSituacao.addChoice(AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU, BundleManager.getString("nao_compareceu"));
        return cbxSituacao;
    }

    private void impressaoFichaAtendimentoAmbulatorial(AjaxRequestTarget target, Atendimento atendimento) {
        EmpresaNaturezaProcuraTipoAtendimento empresaNaturezaProcuraTipoAtendimento = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, atendimento.getNaturezaProcuraTipoAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaNaturezaProcuraTipoAtendimento.PROP_EMPRESA, atendimento.getEmpresa()))
                .setMaxResults(1).start().getVO();

        if (empresaNaturezaProcuraTipoAtendimento != null && EmpresaNaturezaProcuraTipoAtendimento.CONTROLE_ATENDIMENTO_NAO.equals(empresaNaturezaProcuraTipoAtendimento.getControleAtendimento())) {
            if (RepositoryComponentDefault.SIM_LONG.equals(empresaNaturezaProcuraTipoAtendimento.getImprimeFaa())) {
                viewDlgImpressaoFAA(target, atendimento);
            }
        }
    }

    private void viewDlgImpressaoFAA(AjaxRequestTarget target, Atendimento atendimento) {
        if (dlgImpressaoFAA == null) {
            getRecepcaoController().addWindow(target, dlgImpressaoFAA = new DlgImpressaoObject<Atendimento>(getRecepcaoController().newWindowId(), bundle("atendimentoGeradoComSucesso")) {
                @Override
                public DataReport getDataReport(Atendimento atendimento) throws ReportException {
                    RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam param = new RelatorioImpressaoFichaAtendimentoAmbulatorialDTOParam();
                    param.setAtendimento(atendimento);

                    return BOFactoryWicket.getBO(RecepcaoReportFacade.class).relatorioFichaAtendimentoAmbulatorial(param);
                }
            });
            dlgImpressaoFAA.setLabelImprimir(target, bundle("imprimirFaa"));
        }

        dlgImpressaoFAA.show(target, atendimento);
    }
}
