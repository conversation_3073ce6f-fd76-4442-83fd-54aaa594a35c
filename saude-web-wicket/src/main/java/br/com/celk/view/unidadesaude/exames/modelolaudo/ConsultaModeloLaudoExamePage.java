package br.com.celk.view.unidadesaude.exames.modelolaudo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.exame.ModeloLaudoExame;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaModeloLaudoExamePage extends ConsultaPage<ModeloLaudoExame, List<BuilderQueryCustom.QueryParameter>> {

    private TipoExame tipoExame;
    private String descricao;
    private ExameProcedimento exameProcedimento;
    private String referencia;

    public ConsultaModeloLaudoExamePage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        
        form.add(new AutoCompleteConsultaTipoExame("tipoExame"));
        form.add(new InputField<String>("descricao"));
        form.add(new AutoCompleteConsultaExameProcedimento("exameProcedimento").setIncluirInativos(true));
        form.add(new InputField<String>("referencia"));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {

        ColumnFactory columnFactory = new ColumnFactory(ModeloLaudoExame.class);
        ModeloLaudoExame proxy = Lambda.on(ModeloLaudoExame.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(bundle("referencia"), CoreMethods.path(proxy.getReferencia())));
        columns.add(columnFactory.createSortableColumn(bundle("descricao"), CoreMethods.path(proxy.getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("tipoExame"), CoreMethods.path(proxy.getTipoExame().getDescricao())));
        columns.add(columnFactory.createSortableColumn(bundle("exame"), CoreMethods.path(proxy.getExameProcedimento().getDescricaoProcedimento())));
        
        return columns;
    }
    
    private CustomColumn<ModeloLaudoExame> getCustomColumn(){
        return new CustomColumn<ModeloLaudoExame>() {

            @Override
            public Component getComponent(String componentId, final ModeloLaudoExame rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                     @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroModeloLaudoExamePage(rowObject,false,true));
                    }

                   @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroModeloLaudoExamePage(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ModeloLaudoExame.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ModeloLaudoExame.class).getProperties(),
                        new HQLProperties(ExameProcedimento.class, ModeloLaudoExame.PROP_EXAME_PROCEDIMENTO).getProperties(),
                        new HQLProperties(TipoExame.class, ModeloLaudoExame.PROP_TIPO_EXAME).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ModeloLaudoExame.PROP_DESCRICAO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ModeloLaudoExame.PROP_REFERENCIA), QueryCustom.QueryCustomParameter.ILIKE, referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ModeloLaudoExame.PROP_DESCRICAO), QueryCustom.QueryCustomParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ModeloLaudoExame.PROP_EXAME_PROCEDIMENTO), exameProcedimento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ModeloLaudoExame.PROP_TIPO_EXAME), tipoExame));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroModeloLaudoExamePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaModeloLaudoExame");
    }
}
