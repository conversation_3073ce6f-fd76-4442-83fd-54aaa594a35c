package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ImpressaoTermoAutorizacaoTratamentoMedicoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.FichaPacienteDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.system.factory.exception.FactoryException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;

/**
 * <AUTHOR>
 */
public class DlgImpressaoMarcacaoHospital extends Window {

    private final Atendimento atendimento;

    public DlgImpressaoMarcacaoHospital(String id, Atendimento atendimento) {
        super(id);
        init();
        this.atendimento = atendimento;
    }

    private void init() {
        setTitle(BundleManager.getString("imprimirDocumentos"));

        setInitialWidth(280);
        setInitialHeight(50);

        setResizable(false);

        setContent(new PnlImpressaoMarcacaoHospital(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public DataReport getReportFicha() throws ReportException, FactoryException {
                FichaPacienteDTOParam param = new FichaPacienteDTOParam();
                param.setCodigoUsuarioCadsus(atendimento.getUsuarioCadsus().getCodigo());
                param.setCodigoAtendimento(atendimento.getAtendimentoPrincipal().getCodigo());
                return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioFichaPaciente(param);
            }

            @Override
            public DataReport getReportTermo() throws ReportException, FactoryException {
                ImpressaoTermoAutorizacaoTratamentoMedicoDTOParam param = new ImpressaoTermoAutorizacaoTratamentoMedicoDTOParam();
                param.setNomePaciente(atendimento.getNomePaciente());
                param.setCodigoPaciente(atendimento.getUsuarioCadsus().getCodigo());
                param.setResponsavel(atendimento.getResponsavel());
                return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoTermoAutorizacaoTratamentoMedico(param);
            }

        });
        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget art) {
                acaoLimpar(art);
                return true;
            }
        });
    }

    public void acaoLimpar(AjaxRequestTarget target) {
    }

    ;

    @Override
    public void close(AjaxRequestTarget target) {
        acaoLimpar(target);

        super.close(target); //To change body of generated methods, choose Tools | Templates.
    }

}
