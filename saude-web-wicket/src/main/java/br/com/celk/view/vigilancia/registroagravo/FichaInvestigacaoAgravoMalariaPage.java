package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.cepField.CepWsField;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.vigilancia.registroagravo.enums.*;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoMalariaDTO;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteAnimalPeconhento;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMalaria;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoMalariaPage extends FichaInvestigacaoAgravoBasePage {

    private InvestigacaoAgravoMalaria investigacaoAgravoMalaria;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DropDown principalAtividade;
    private DropDown tipoLamina;
    private DropDown sintomas;

    private WebMarkupContainer containerDadosExame;
    private DateChooser dataExame;
    private DropDown resultadoExame;
    private InputField<String> parasitaMM3;
    private DropDown parasitemiaEmCruzes;

    private WebMarkupContainer containerTratamento;
    private DropDown esquemaTratamentoUtilizado;
    private InputField<String> outrosJustificativa;
    private DateChooser inicioTratamento;

    private WebMarkupContainer containerCasoAutoctone;
    private DropDown ddCasoAutoctone;
    private AutoCompleteConsultaCidade autoCompleteCidadeLocalInfeccao;
    private DisabledInputField estadoLocalInfeccao;
    private AutoCompleteConsultaPais autoCompletePaisLocalInfeccao;
    private DisabledInputField codMunicipioLocalInfeccao;
    private InputField distritoLocalInfeccao;
    private InputField bairroLocalInfeccao;
    private InputField txtLogradouro;
    private InputField txtCorigoRua;
    private InputField txtNumero;
    private InputField txtComplemento;
    private InputField txtGeoCampo1;
    private InputField txtGeoCampo2;
    private InputField txtPontoDeReferencia;
    private CepWsField cepWsField;

    private InputField txtTelefone;
    private DropDown ddZona;
    private InputField<String> localProvavelInfeccao;

    private WebMarkupContainer containerConclusao;
    private DropDown classificacaoFinal;

    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;

    public FichaInvestigacaoAgravoMalariaPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoMalaria = InvestigacaoAgravoMalaria.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoMalaria == null) {
            investigacaoAgravoMalaria = new InvestigacaoAgravoMalaria();
            investigacaoAgravoMalaria.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoMalaria.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoMalaria.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoMalaria.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoMalaria.setOcupacaoCbo(tabelaCbo);
        }

        if (investigacaoAgravoMalaria.getCidadeLocalInfeccao() != null) {
            Cidade cidadeInfeccao = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(investigacaoAgravoMalaria.getCidadeLocalInfeccao().getCodigo());
            investigacaoAgravoMalaria.setCidadeLocalInfeccao(cidadeInfeccao);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoMalaria)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoMalariaDTO fichaDTO = new FichaInvestigacaoAgravoMalariaDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoMalaria(investigacaoAgravoMalaria);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoMalaria.getFlagInformacoesComplementares() == null ? "S" : investigacaoAgravoMalaria.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoMalaria proxy = on(InvestigacaoAgravoMalaria.class);

        criarInvestigacao(proxy);
        criarDadosClinicosEpidemiologicos(proxy);
        criarDadosExame(proxy);
        criarTratamento(proxy);
        criarLocalInfeccao(proxy);
        criarConclusao(proxy);
        criarUsuarioDataEncerramento(proxy);
        criarObservacao(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarTratamento();
        carregarLocalInfeccao();
        camposObrigatoriosVisualizacao();
    }


    private void criarInvestigacao(InvestigacaoAgravoMalaria proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));

        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarDadosClinicosEpidemiologicos(InvestigacaoAgravoMalaria proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        principalAtividade = DropDownUtil.getIEnumDropDown(path(proxy.getPrincipalAtividadeUltimosDias()), PrincipaisAtividadesEnum.values());
        tipoLamina = DropDownUtil.getIEnumDropDown(path(proxy.getTipoLamina()), TipoLaminaEnum.values());
        sintomas = DropDownUtil.getIEnumDropDown(path(proxy.getSintomas()), SintomasEnum.values());

        containerAntecedentesEpidemiologicos.add(principalAtividade, tipoLamina, sintomas);
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void criarDadosExame(InvestigacaoAgravoMalaria proxy) {
        containerDadosExame = new WebMarkupContainer("containerDadosExame");
        containerDadosExame.setOutputMarkupId(true);

        dataExame = new DateChooser(path(proxy.getDataExame()));

        resultadoExame = DropDownUtil.getIEnumDropDown(path(proxy.getExameResultado()), ResultadoExameMalariaEnum.values());
        parasitaMM3 = new InputField<String>(path(proxy.getExameParasitosMm3()));
        parasitemiaEmCruzes = DropDownUtil.getIEnumDropDown(path(proxy.getExameParasitemiaCruzes()), ParasitemiaEmCruzesEnum.values());

        containerDadosExame.add(dataExame, resultadoExame, parasitaMM3, parasitemiaEmCruzes);
        getContainerInformacoesComplementares().add(containerDadosExame);
    }

    private void criarTratamento(InvestigacaoAgravoMalaria proxy) {
        containerTratamento = new WebMarkupContainer("containerTratamento");
        containerTratamento.setOutputMarkupId(true);

        esquemaTratamentoUtilizado = DropDownUtil.getIEnumDropDown(path(proxy.getTratamentoEsquema()), EsquemaTratamentoUtilizadoEnum.values());
        inicioTratamento = new DateChooser(path(proxy.getTratamentoEsquemaDataInicio()));

        outrosJustificativa = new InputField<String>(path(proxy.getTratamentoEsquemaOutro()));
        outrosJustificativa.setEnabled(false);

        containerTratamento.add(esquemaTratamentoUtilizado, inicioTratamento, outrosJustificativa);
        getContainerInformacoesComplementares().add(containerTratamento);
    }

    private void carregarTratamento() {
        FichaInvestigacaoAgravoHelper.enableDisableDropDown(esquemaTratamentoUtilizado, !isModoLeitura(), true, null);

        esquemaTratamentoUtilizado.addAjaxUpdateValue();
        esquemaTratamentoUtilizado.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                outrosJustificativa.setEnabled(EsquemaTratamentoUtilizadoEnum.OUTRO_ESQUEMA.value().equals(esquemaTratamentoUtilizado.getComponentValue()));

                target.add(outrosJustificativa);
            }
        });

        FichaInvestigacaoAgravoHelper.enableDisableDropDown(esquemaTratamentoUtilizado, !isModoLeitura(), true, null);
        outrosJustificativa.setEnabled(EsquemaTratamentoUtilizadoEnum.OUTRO_ESQUEMA.value().equals(esquemaTratamentoUtilizado.getComponentValue()));
    }

    private void criarConclusao(InvestigacaoAgravoMalaria proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        classificacaoFinal = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoFinal()), ClassificacaoFinalEnum.values());

        containerConclusao.add(classificacaoFinal);

        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void criarObservacao(InvestigacaoAgravoMalaria proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacao");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarLocalInfeccao(InvestigacaoAgravoMalaria proxy) {
        containerCasoAutoctone = new WebMarkupContainer("containerCasoAutoctone");
        containerCasoAutoctone.setOutputMarkupId(true);

        ddCasoAutoctone = FichaInvestigacaoAgravoHelper.createDropDownCasoAutoctone(path(proxy.getCasoAutoctone()), containerCasoAutoctone, true);

        cepWsField = new CepWsField(path(proxy.getCep())) {
            @Override
            public void unload(AjaxRequestTarget target) {
                limparCamposAutoctone(target);
            }

            @Override
            public void load(AjaxRequestTarget target, CepWSDTO cepWSDTO) {
                preencherCamposAutoctone(cepWSDTO, target);
            }
        };

        autoCompleteCidadeLocalInfeccao = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalInfeccao()));
        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                limparCamposAutoctone(target);
            }
        });

        codMunicipioLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getCodigo()));
        estadoLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getEstado().getSigla()));

        distritoLocalInfeccao = new InputField(path(proxy.getDistritoLocalInfeccao()));
        bairroLocalInfeccao = new InputField(path(proxy.getBairroLocalInfeccao()));
        localProvavelInfeccao = new InputField<>(path(proxy.getLocalicadeProvavelInfeccao()));

        txtLogradouro = new InputField(path(proxy.getLogradouro()));
        txtCorigoRua = new InputField(path(proxy.getCodigoRua()));
        txtNumero = new InputField(path(proxy.getNumero()));
        txtComplemento = new InputField(path(proxy.getComplemento()));
        txtGeoCampo1 = new InputField(path(proxy.getGeoCampo1()));
        txtGeoCampo2 = new InputField(path(proxy.getGeoCampo2()));
        txtPontoDeReferencia = new InputField(path(proxy.getPontoReferencia()));
        txtTelefone = new InputField(path(proxy.getTelefone()));
        ddZona = DropDownUtil.getIEnumDropDown(path(proxy.getZona()), InvestigacaoAgravoAcidenteAnimalPeconhento.ZonaOcorrencia.values(), true);

        autoCompletePaisLocalInfeccao = new AutoCompleteConsultaPais(path(proxy.getPaisLocalInfeccao()));

        containerCasoAutoctone.add(
                ddCasoAutoctone, autoCompleteCidadeLocalInfeccao,
                codMunicipioLocalInfeccao, estadoLocalInfeccao, autoCompletePaisLocalInfeccao,
                distritoLocalInfeccao, bairroLocalInfeccao, localProvavelInfeccao,
                txtGeoCampo1, txtGeoCampo2, txtPontoDeReferencia,
                cepWsField, txtTelefone, ddZona,
                txtCorigoRua, txtLogradouro, txtNumero,
                txtComplemento
        );

        getContainerInformacoesComplementares().add(ddCasoAutoctone, containerCasoAutoctone);
    }

    private void limparCamposAutoctone(AjaxRequestTarget target) {
        autoCompleteCidadeLocalInfeccao.limpar(target);
        codMunicipioLocalInfeccao.limpar(target);
        estadoLocalInfeccao.limpar(target);
        autoCompletePaisLocalInfeccao.limpar(target);
        txtLogradouro.limpar(target);
        txtCorigoRua.limpar(target);
        txtNumero.limpar(target);
        txtComplemento.limpar(target);
        distritoLocalInfeccao.limpar(target);
        bairroLocalInfeccao.limpar(target);
        localProvavelInfeccao.limpar(target);
        txtGeoCampo1.limpar(target);
        txtGeoCampo2.limpar(target);
        txtPontoDeReferencia.limpar(target);
        ddZona.limpar(target);
        txtTelefone.limpar(target);

        target.appendJavaScript(JScript.initMasks());
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void preencherCamposAutoctone(CepWSDTO dto, AjaxRequestTarget target) {
        autoCompleteCidadeLocalInfeccao.setComponentValue(target, dto.getCidade());
        codMunicipioLocalInfeccao.setComponentValue(dto.getIbge());
        estadoLocalInfeccao.setComponentValue(dto.getUf());
        txtLogradouro.setComponentValue(dto.getLogradouroFormatado());
        txtComplemento.setComponentValue(dto.getComplemento());
        bairroLocalInfeccao.setComponentValue(dto.getBairro());
        localProvavelInfeccao.setComponentValue(dto.getLocalidade());

        target.add(
                autoCompleteCidadeLocalInfeccao,
                codMunicipioLocalInfeccao, estadoLocalInfeccao, txtLogradouro,
                txtComplemento, bairroLocalInfeccao, localProvavelInfeccao
        );
        target.appendJavaScript(JScript.initMasks());
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void carregarLocalInfeccao() {
        boolean notAutoctone = investigacaoAgravoMalaria.getCasoAutoctone() != null && investigacaoAgravoMalaria.getCasoAutoctone() == 2L;

        containerCasoAutoctone.setEnabled(notAutoctone);

        autoCompletePaisLocalInfeccao.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoLocalInfeccao.limpar(target);
                    autoCompleteCidadeLocalInfeccao.limpar(target);
                    codMunicipioLocalInfeccao.limpar(target);
                }
            }
        });
        autoCompletePaisLocalInfeccao.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteCidadeLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioLocalInfeccao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoLocalInfeccao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioLocalInfeccao, estadoLocalInfeccao);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);
            }
        });
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoMalaria proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }

    @Override
    public void validarFicha() throws ValidacaoException {

        Date dataRegistro = investigacaoAgravoMalaria.getRegistroAgravo().getDataRegistro();
        Date dataInvestigacao = investigacaoAgravoMalaria.getDataInvestigacao();
        Date dataExame = investigacaoAgravoMalaria.getDataExame();
        Date dataEncerramento = investigacaoAgravoMalaria.getDataEncerramento();
        Date dataInicioTratamento = investigacaoAgravoMalaria.getTratamentoEsquemaDataInicio();

        if (dataInvestigacao != null) {
            if (DataUtil.dataMaiorQueAtual(dataInvestigacao)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_investigacao_futuro"));
            }
        }

        if (dataExame != null) {
            if (DataUtil.dataMaiorQueAtual(dataExame)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_exame_futuro"));
            }
        }

        if (dataInicioTratamento != null) {
            if (DataUtil.dataMaiorQueAtual(dataInicioTratamento)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_inicio_tratamento_futuro"));
            }
        }

        if (dataInvestigacao != null && dataRegistro != null) {
            if (dataInvestigacao.before(dataRegistro)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_investigacao_data_registro"));
            }

        }

        if (dataExame != null && dataInvestigacao != null) {
            if (dataExame.before(dataInvestigacao)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_exame_data_investigacao"));
            }
        }

        if (dataEncerramento != null && dataInvestigacao != null) {
            if (dataEncerramento.before(dataInvestigacao)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_encerramento_data_investigacao"));
            }
        }

        camposObrigatorios();
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoMalariaDTO dto = (FichaInvestigacaoAgravoMalariaDTO) fichaDTO;
        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoMalaria(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoMalariaDTO fichaDTO = (FichaInvestigacaoAgravoMalariaDTO) getFichaDTO();

        if (investigacaoAgravoMalaria.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoMalaria.setUsuarioEncerramento(usuarioLogado);

            investigacaoAgravoMalaria.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    private void camposObrigatoriosVisualizacao() {
        if (investigacaoAgravoMalaria.getDataEncerramento() != null) {
            dataInvestigacao.setRequired(true);
            dataInvestigacao.setRequiredField(true);
            dataExame.setRequired(true);
            dataExame.setRequiredField(true);
            principalAtividade.setRequired(true);
            tipoLamina.setRequired(true);
            sintomas.setRequired(true);
            resultadoExame.setRequired(true);
            parasitemiaEmCruzes.setRequired(true);
            esquemaTratamentoUtilizado.setRequired(true);
            inicioTratamento.setRequired(true);
            inicioTratamento.setRequiredField(true);
            classificacaoFinal.setRequired(true);
            ddCasoAutoctone.setRequired(true);
        }
    }

    private void camposObrigatorios() throws ValidacaoException {
        if (investigacaoAgravoMalaria.getDataEncerramento() != null) {

            if (dataInvestigacao == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (principalAtividade == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (tipoLamina == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (sintomas == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (resultadoExame == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (parasitemiaEmCruzes == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (esquemaTratamentoUtilizado == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (inicioTratamento == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }

            if (classificacaoFinal == null) {
                throw new ValidacaoException("Verifique o campo Data de investigação");
            }
        }
    }
}
