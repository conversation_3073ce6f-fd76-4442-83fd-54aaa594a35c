package br.com.celk.view.vigilancia.dengue.armadilha.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilhaVisita;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

public abstract class PnlDengueResultadoColetaArmadilha extends Panel {

    private CompoundPropertyModel<DengueArmadilhaVisita> model;
    private RequiredDateChooser dataVisita;
    private InputField ovos;
    private InputField larva;
    private InputField aedAegypti;
    private InputField albopictus;
    private InputField outros;

    public PnlDengueResultadoColetaArmadilha(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        Form form = new Form("form", model = new CompoundPropertyModel(new DengueArmadilhaVisita()));

        DengueArmadilhaVisita proxy = on(DengueArmadilhaVisita.class);

        form.add(new DateChooser(path(proxy.getDataVisita())).setEnabled(false));
        form.add(new DisabledInputField(path(proxy.getProfissional().getNome())));
        form.add(new DisabledInputField(path(proxy.getDengueOcorrenciaDesfecho().getDescricao())));
        form.add(new DateChooser(path(proxy.getDataColeta())).setEnabled(false));
        form.add(new DisabledInputField(path(proxy.getNumero())));
        form.add(new DisabledInputField(path(proxy.getNumeroChave())));

        form.add(dataVisita = new RequiredDateChooser(path(proxy.getDataResultado())));
        form.add(ovos = new InputField(path(proxy.getNumeroOvos())));
        form.add(larva = new InputField(path(proxy.getNumeroLarvas())));
        form.add(aedAegypti = new InputField(path(proxy.getNumeroAedAegypti())));
        form.add(albopictus = new InputField(path(proxy.getNumeroAlbopictus())));
        form.add(outros = new InputField(path(proxy.getNumeroOutros())));

        form.add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlDengueResultadoColetaArmadilha.this.onCancelar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarConfirmacaoCancelamento(target)) {
                    PnlDengueResultadoColetaArmadilha.this.onConfirmar(target, model.getObject());
                }
            }
        });

        add(form);
    }

    private boolean validarConfirmacaoCancelamento(AjaxRequestTarget target) {
        try {
            if (DataUtil.getDataAtual().before(dataVisita.getComponentValue())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_resultado_nao_pode_maior_atual"));
            }
        } catch (ValidacaoException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }

        return true;
    }

    public void setResultadoColetaArmadilha(AjaxRequestTarget target, DengueArmadilhaVisita armadilhaVisita) {
        limpar(target);
        this.model.setObject(armadilhaVisita);
        update(target);
    }

    public void limpar(AjaxRequestTarget target) {
        model.setObject(new DengueArmadilhaVisita());
        ovos.limpar(target);
        larva.limpar(target);
        aedAegypti.limpar(target);
        albopictus.limpar(target);
        outros.limpar(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, DengueArmadilhaVisita armadilhaVisita) throws ValidacaoException, DAOException;
}
