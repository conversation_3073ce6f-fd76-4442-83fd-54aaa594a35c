package br.com.celk.view.basico.empresa.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.basico.DominioEstabelecimento;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class EmpresaAutoCompleteSettings extends AbstractAutoCompleteSettings {

    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<String, String>();
        
        propertiesMap.put("id", o.getIdentificador());
        propertiesMap.put("name", o.getDescricaoVO());
        
        if (o instanceof DominioEstabelecimento) {
            propertiesMap.put("fantasia", Coalesce.asString(StringUtils.trimToNull(((DominioEstabelecimento)o).getEmpresa().getFantasia()), BundleManager.getString("naoInformado")));
            propertiesMap.put("cnes", Coalesce.asString(StringUtils.trimToNull(((DominioEstabelecimento)o).getCnes()), BundleManager.getString("naoInformado")));
            propertiesMap.put("telefone", Coalesce.asString(StringUtils.trimToNull(((DominioEstabelecimento)o).getEmpresa().getTelefone()), BundleManager.getString("naoInformado")));
        }
        
        return propertiesMap;
    }

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();
        builder.append("<li>");
            builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
                builder.append("<div class=\"nivel-1\"> '+item.name+' </div>");
                builder.append("<div class=\"nivel-2\" > "+BundleManager.getString("fantasia") +": '+item.fantasia+' </div>");
                builder.append("<div class=\"nivel-2\" > "+BundleManager.getString("cnes") +": '+item.cnes+' | "+BundleManager.getString("telefone") +": '+item.telefone+' </div>");
            builder.append("</div>");
        builder.append("</li>");
        
        return builder.toString();
    }
    
}
