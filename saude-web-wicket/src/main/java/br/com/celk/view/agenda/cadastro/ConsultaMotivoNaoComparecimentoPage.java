package br.com.celk.view.agenda.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.MotivoNaoComparecimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaMotivoNaoComparecimentoPage extends ConsultaPage<MotivoNaoComparecimento, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.add(new InputField<String>("descricao", new PropertyModel<String>(this, "descricao")));
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        MotivoNaoComparecimento proxy = on(MotivoNaoComparecimento.class);

        columns.add(getCustomColumn());
        columns.add(WicketMethods.createSortableColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private CustomColumn<MotivoNaoComparecimento> getCustomColumn() {
        return new CustomColumn<MotivoNaoComparecimento>() {

            @Override
            public Component getComponent(String componentId, final MotivoNaoComparecimento rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMotivoNaoComparecimentoPage(rowObject));
                    }

                    @Override
                    public boolean isEditarEnabled() {
                        return !RepositoryComponentDefault.SIM_INTEGER.equals(rowObject.getOutros());
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public boolean isExcluirEnabled() {
                        return !RepositoryComponentDefault.SIM_INTEGER.equals(rowObject.getOutros());
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMotivoNaoComparecimentoPage(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(MotivoNaoComparecimento.class).getProperties());
            }

            @Override
            public Class getClassConsulta() {
                return MotivoNaoComparecimento.class;
            }

        });
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(MotivoNaoComparecimento.PROP_DESCRICAO, QueryCustom.QueryCustomParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroMotivoNaoComparecimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaMotivoNaoComparecimento");
    }
}
