package br.com.celk.view.unidadesaude.esus.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.cadsus.usuariocadsus.customcolumn.UsuarioCidadaoColumnPanel;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaUsuarioCidadaoPage extends ConsultaPage<ConsultaUsuarioCadsusDTO, QueryConsultaUsuarioCadsusDTOParam> {

    private QueryConsultaUsuarioCadsusDTOParam param;
    private Long situacao;
    private Boolean permissaoEditar;
    boolean parametroReferencia;

    @Override
    public void initForm(Form form) {

        form.setDefaultModel(new CompoundPropertyModel<QueryConsultaUsuarioCadsusDTOParam>(param = new QueryConsultaUsuarioCadsusDTOParam()));

        form.add(new InputField("nome"));
        form.add(new InputField("nomeMae"));
        form.add(new InputField("numeroCartao"));
        form.add(new InputField("rg"));
        form.add(new InputField("cpf"));
        form.add(new DateChooser("dataNascimento"));
        form.add(new InputField("codigo"));
        form.add(getDropDownSituacao());

        getPageableTable().setScrollX("1200px");

        getLinkNovo().setVisible(false);

        final Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
        AbstractAjaxButton btnNovo;
        getControls().add(btnNovo = new AbstractAjaxButton(getControls().newChildId()) {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (!isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL)) {
                    if (validarNovoCadastro()) {
                        setResponsePage(new CadastroUsuarioCidadaoPage());
                    } else {
                        throw new ValidacaoException(bundle("msgNaoFoiEncontradoAreaMicroAreaProfissional"));
                    }
                } else {
                    setResponsePage(new CadastroUsuarioCidadaoPage());
                }
            }
        });

        btnNovo.add(new AttributeModifier("class", "doc-new"));
        btnNovo.add(new AttributeModifier("value", bundle("novo")));

        permissaoEditar = true;
        if (!isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL)) {
            if (usuarioLogado.getProfissional() == null) {
                warn(bundle("msgNaoFoiEncontradoProfissionalUsuario"));
            }
            permissaoEditar = false;
        }

        if (!isActionPermitted(usuarioLogado, Permissions.CADASTRAR)) {
            btnNovo.setEnabled(false);
        }
        setExibeExpandir(false);
    }

    private boolean validarNovoCadastro() {
        Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();

        Usuario usuario = LoadManager.getInstance(Usuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_CODIGO, usuarioLogado.getCodigo()))
                .start().getVO();

        List<EquipeMicroArea> equipeMicroArea = LoadManager.getInstance(EquipeMicroArea.class)
                .addProperties(new HQLProperties(EquipeMicroArea.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL), usuario.getProfissional()))
                .start().getList();

        return CollectionUtils.isNotNullEmpty(equipeMicroArea);
    }

    private DropDown getDropDownSituacao() {
        DropDown dropDown = new DropDown("situacao", new PropertyModel(this, "situacao"));

        dropDown.addChoice(null, "");
        dropDown.addChoice(UsuarioCadsus.SITUACAO_ATIVO, BundleManager.getString("ativo"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_PROVISORIO, BundleManager.getString("provisorio"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_INATIVO, BundleManager.getString("inativo"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_EXCLUIDO, BundleManager.getString("excluido"));

        return dropDown;
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(ConsultaUsuarioCadsusDTO.class);
        try {
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        columns.add(getCustomColumn());
        if (parametroReferencia) {
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), UsuarioCadsus.PROP_REFERENCIA, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_REFERENCIA)));
        } else {
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), UsuarioCadsus.PROP_CODIGO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)));
        }
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), UsuarioCadsus.PROP_NOME, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nascimento"), UsuarioCadsus.PROP_DATA_NASCIMENTO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("rg"), UsuarioCadsus.PROP_RG, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_RG)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("mae"), UsuarioCadsus.PROP_NOME_MAE, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_MAE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), UsuarioCadsus.PROP_SITUACAO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_SITUACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unificado"), UsuarioCadsus.PROP_FLAG_UNIFICADO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_FLAG_UNIFICADO)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<ConsultaUsuarioCadsusDTO>() {

            @Override
            public Component getComponent(String componentId, ConsultaUsuarioCadsusDTO rowObject) {
                return new UsuarioCidadaoColumnPanel(componentId, rowObject, permissaoEditar) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        getPageableTable().update(target);
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<ConsultaUsuarioCadsusDTO, QueryConsultaUsuarioCadsusDTOParam>() {

            @Override
            public DataPagingResult<ConsultaUsuarioCadsusDTO> executeQueryPager(DataPaging<QueryConsultaUsuarioCadsusDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).getDominioUsuarioCadsusQueryPager(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(UsuarioCadsus.PROP_NOME, true);
            }

            @Override
            public void customizeParam(QueryConsultaUsuarioCadsusDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                param.setCampoOrdenacao(sortState.getSort().getProperty());
                param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");
            }
        };
    }

    @Override
    public QueryConsultaUsuarioCadsusDTOParam getParameters() {

        if (param.getCpf() != null) {
            String cpf = param.getCpf().replace(".", "").replace("-", "").replace("_", "");
            if (!cpf.trim().equals("")) {
                this.param.setCpf(cpf);
            }
        }

        if (param.getNumeroCartao() != null) {
            String numeroCartao = param.getNumeroCartao().replace(".", "").replace("-", "").replace("_", "");
            if (!numeroCartao.trim().equals("")) {
                this.param.setNumeroCartao(numeroCartao);
            }
        }

        param.setSituacao(null);

        if (situacao != null) {
            param.setSituacao(Arrays.asList(situacao));
        }

        param.setExibirExcluidos(true);
        param.setExibirInativos(true);
        param.setSomenteTabelaBase(true);
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaUsuarioCidadaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaUsuarioCidadao");
    }

}
