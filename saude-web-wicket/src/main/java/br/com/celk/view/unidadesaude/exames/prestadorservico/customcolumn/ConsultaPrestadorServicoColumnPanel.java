package br.com.celk.view.unidadesaude.exames.prestadorservico.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.unidadesaude.exames.prestadorservico.CadastroPrestadorServicoStep1Page;
import br.com.celk.view.unidadesaude.exames.prestadorservico.DetalhesPrestadorServicoPage;
import br.com.celk.view.unidadesaude.exames.prestadorservico.cotaestabelecimento.CadastroCotaEstabelecimentosPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaPrestadorServicoColumnPanel extends Panel {

    public AbstractAjaxLink btnEditar;
    public AbstractAjaxLink btnRemover;
    public AbstractAjaxLink btnConsultar;
    public AbstractAjaxLink btnCotaEstabelecimento;
    private DlgConfirmacao dlgConfirmacao;
    private ExamePrestador examePrestador;

    public ConsultaPrestadorServicoColumnPanel(String id, ExamePrestador examePrestador) {
        super(id);
        this.examePrestador = examePrestador;
        init();
    }

    private void init() {
        add(btnRemover = new AbstractAjaxLink("btnRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
            }
        });

        add(btnEditar = new AbstractAjaxLink("btnEditar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroPrestadorServicoStep1Page(examePrestador.getCodigo()));
            }
        });

        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new DetalhesPrestadorServicoPage(examePrestador.getCodigo()));
            }
        });

        add(btnCotaEstabelecimento = new AbstractAjaxLink("btnCotaEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroCotaEstabelecimentosPage(examePrestador.getCodigo()));
            }
        });

        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnRemover.add(new AttributeModifier("title", BundleManager.getString("excluir")));
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
        btnCotaEstabelecimento.add(new AttributeModifier("title", BundleManager.getString("cotaEstabelecimento")));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteExcluir") + "?") {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    removerExamePrestador(target);
                }
            });
        }
        dlgConfirmacao.show(target);
    }

    private void removerExamePrestador(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.delete(examePrestador);
        updateTable(target);
    }

    public abstract void updateTable(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}
