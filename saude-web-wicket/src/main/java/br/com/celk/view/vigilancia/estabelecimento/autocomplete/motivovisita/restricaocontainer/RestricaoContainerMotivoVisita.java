package br.com.celk.view.vigilancia.estabelecimento.autocomplete.motivovisita.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaMotivoVisitaDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerMotivoVisita extends Panel implements IRestricaoContainer<QueryConsultaMotivoVisitaDTOParam> {

    private InputField<String> txtDescricao;

    private QueryConsultaMotivoVisitaDTOParam param = new QueryConsultaMotivoVisitaDTOParam();

    public RestricaoContainerMotivoVisita(String id) {
        super(id);

        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));

        root.add(txtDescricao = new InputField<String>("descricao"));

        add(root);
    }

    @Override
    public QueryConsultaMotivoVisitaDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
