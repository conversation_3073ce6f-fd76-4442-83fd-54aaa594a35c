package br.com.celk.view.atendimento.consultaprontuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.atendimento.prontuario.NodesConsultaProntuarioRef;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitLink;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.favoritos.FavoritosCache;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.notification.NotificationPanel;
import br.com.celk.component.window.Window;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.TemplatePage;
import br.com.celk.view.agenda.agendamento.AnaliseResumoRegulacaoSolicitacaoPage;
import br.com.celk.view.atendimento.atencaobasica.ConsultaAtendimentoAtencaoBasicaPage;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.IConsultaProntuarioController;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.IConsultaProntuarioNode;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.NodeButtonConsultaProntuario;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.NodeButtonConsultaProntuarioEventListener;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.DefaultConsultaProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.DefaultConsultaAtendimentoPage;
import br.com.celk.view.hospital.aih.RegulacaoAihPage;
import br.com.celk.view.materiais.dispensacao.DispensacaoMedicamentoPage;
import br.com.celk.view.unidadesaude.atendimento.consulta.outraunidade.DetalheAtendimentoOutraUnidadePage;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.AnaliseRegulacaoSolicitacaoPage;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.AnaliseRegulacaoSolicitacaoTfdPage;
import br.com.celk.view.unidadesaude.processos.regulacoesdevolvidas.RegulacoesDevolvidasDTO;
import br.com.celk.view.unidadesaude.processos.regulacoesdevolvidas.RegulacoesDevolvidasPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ActionAtendimentoWebDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.ConsultaRegulacaoSolicitacaoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.LogMessageBuilder;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NodoConsultaProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import ch.lambdaj.group.Group;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.TransparentWebMarkupContainer;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static ch.lambdaj.collection.LambdaCollections.with;

/**
 * <AUTHOR>
 */
@Private
public class ProntuarioPage extends TemplatePage implements INotificationPanel {

    private static final String PANEL_ID = "nodePanel";

    private Usuario usuarioLogado;
    private UsuarioCadsus usuarioCadsus;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade;

    private NotificationPanel notificationPanel;
    private Form form;

    private WebMarkupContainer panelContainer;
    private IConsultaProntuarioNode activeNode;
    private List<NodesConsultaProntuarioRef> nodeList = new ArrayList<NodesConsultaProntuarioRef>();
    private List<NodeButtonConsultaProntuarioEventListener> nodeButtonListeners = new ArrayList<NodeButtonConsultaProntuarioEventListener>();
    private IConsultaProntuarioController prontuarioController;
    private DlgImpressaoObject<Atendimento> dlgConfirmacaoImpressao;
    private AbstractAjaxLink linkBookmark;
    private AttributeModifier bookmarked = new AttributeModifier("class", "icon16 star-fav");
    private AttributeModifier notBookmarked = new AttributeModifier("class", "icon star-fav-empty");

    private EventoSistema eventoSistema;
    private Class clazz;
    private ConsultaRegulacaoSolicitacaoDTOParam param;
    private String codigoBarras;
    private ActionAtendimentoWebDTO actionAtendimentoWebDTO;
    private AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO;

    public ProntuarioPage(ActionAtendimentoWebDTO dto, Usuario usuarioLogado) {
        this.usuarioCadsus = dto.getAtendimentoWebDTO().getAtendimento().getUsuarioCadsus();
        this.usuarioLogado = usuarioLogado;
        this.actionAtendimentoWebDTO = dto;
        iniciarConsultaProntuario();
    }
    public ProntuarioPage(ConsultaRegulacaoSolicitacaoDTOParam param, Usuario usuarioLogado, SolicitacaoAgendamento solicitacaoAgendamento) {
        this(usuarioLogado, solicitacaoAgendamento.getUsuarioCadsus());
        this.solicitacaoAgendamento = solicitacaoAgendamento;
        this.param = param;
    }

    public ProntuarioPage(Usuario usuarioLogado, SolicitacaoAgendamento solicitacaoAgendamento, Class clazz) {
        this(usuarioLogado, solicitacaoAgendamento.getUsuarioCadsus());
        this.solicitacaoAgendamento = solicitacaoAgendamento;
        this.clazz = clazz;
    }

    public ProntuarioPage(Usuario usuarioLogado, PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
        this.usuarioLogado = usuarioLogado;
        this.usuarioCadsus = pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus();
        this.pacienteAtendidoOutraUnidade = pacienteAtendidoOutraUnidade;
        iniciarConsultaProntuario();
    }

    public ProntuarioPage(Usuario usuarioLogado, UsuarioCadsus usuarioCadsus, String codigoBarras, Class clazz) {
        this.clazz = clazz;
        this.codigoBarras = codigoBarras;
        this.usuarioLogado = usuarioLogado;
        this.usuarioCadsus = usuarioCadsus;
        iniciarConsultaProntuario();
    }

    public ProntuarioPage(Usuario usuarioLogado, UsuarioCadsus usuarioCadsus) {
        this.usuarioLogado = usuarioLogado;
        this.usuarioCadsus = usuarioCadsus;
        iniciarConsultaProntuario();
    }

    public ProntuarioPage(Usuario usuarioLogado, UsuarioCadsus usuarioCadsus, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO) {
        this.usuarioLogado = usuarioLogado;
        this.usuarioCadsus = usuarioCadsus;
        this.autorizacaoInternacaoHospitalarDTO = autorizacaoInternacaoHospitalarDTO;
        iniciarConsultaProntuario();
    }

    private void iniciarConsultaProntuario() {
        this.eventoSistema = criarEventoSistema(usuarioCadsus, null, null);
        TransparentWebMarkupContainer section = new TransparentWebMarkupContainer("section");

        add(new Label("nomePrograma", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return getTituloPrograma();
            }
        }));

        add(new Label("nomePaciente", usuarioCadsus.getNomeSocial() + " | " + usuarioCadsus.getDescricaoIdadeSimples() + " | DN: " + usuarioCadsus.getDataNascimentoFormatado(true) + " | " + UsuarioCadsusHelper.carregarUsuarioCadsusDoenca(usuarioCadsus) ));

        StringBuilder sb = new StringBuilder();
        sb.append(bundle("situacao")).append(": ").append(usuarioCadsus.getDescricaoSituacao());

        if (UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {
            if (usuarioCadsus.getMotivoExclusao() != null && !"".equals(usuarioCadsus.getDescricaoMotivoExclusao())) {
                sb.append(" | ").append(bundle("motivoExclusao")).append(": ").append(usuarioCadsus.getDescricaoMotivoExclusao());
            }

            UsuarioCadsusEsus usuarioCadsusEsus = LoadManager.getInstance(UsuarioCadsusEsus.class)
                    .addProperty(UsuarioCadsusEsus.PROP_DATA_OBITO)
                    .addParameter(new QueryCustomParameter(UsuarioCadsusEsus.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .start().getVO();

            if (usuarioCadsusEsus != null && usuarioCadsusEsus.getDataObito() != null) {
                sb.append(" | ").append(bundle("dataObito")).append(": ").append(Data.formatar(usuarioCadsusEsus.getDataObito()));
            }
        }

        add(new Label("dadosSituacaoPaciente", sb.toString()));

        ListView<IConsultaProntuarioNode> listView = getListView();
        add(listView);

        add(notificationPanel = new NotificationPanel("notificationPanel"));

        add(panelContainer = new WebMarkupContainer("panelContainer"));
        panelContainer.setOutputMarkupId(true);

        form = new Form("form");

        form.add(new SubmitLink("btnVoltar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                voltarAction(target);
            }
        }));

        section.add(form);

        add(section);

        add(linkBookmark = new AbstractAjaxLink("btnAdicionarFavorito") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                ProgramaFavorito adicionarFavorito = BOFactoryWicket.getBO(UsuarioFacade.class).adicionarFavorito(getCodigoPrograma());
                addFavorito(target, adicionarFavorito);
                resolveBookmarkLinkClass(target);
            }

            @Override
            public boolean isVisible() {
                return getCodigoPrograma() != null;
            }
        });
        resolveBookmarkLinkClass();

        if (CollectionUtils.isNotNullEmpty(listView.getModelObject())) {
            changeNode(listView.getModelObject().get(0));
        } else {
            panelContainer.add(new Fragment(getConsultaProntuarioController().panelId(), "nodeNaoConfigurado-fragment", panelContainer));
        }
    }

    private void voltarAction(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (pacienteAtendidoOutraUnidade != null) {
            setResponsePage(new DetalheAtendimentoOutraUnidadePage(pacienteAtendidoOutraUnidade));
        } else if (this.clazz != null) {
            try {
                Page page = (Page) clazz.newInstance();
                if (page instanceof RegulacoesDevolvidasPage) {
                    setResponsePage(new RegulacoesDevolvidasPage(new RegulacoesDevolvidasDTO(solicitacaoAgendamento)));
                } else if (page instanceof DispensacaoMedicamentoPage) {
                    setResponsePage(new DispensacaoMedicamentoPage(codigoBarras,target));
                }
            } catch (InstantiationException e) {
                Loggable.log.error(e.getMessage());
            } catch (IllegalAccessException e) {
                Loggable.log.error(e.getMessage());
            }
        } else if (solicitacaoAgendamento != null) {
            if (RepositoryComponentDefault.SIM.equals(solicitacaoAgendamento.getTipoProcedimento().getFlagTfd())) {
                setResponsePage(new AnaliseRegulacaoSolicitacaoTfdPage(param, solicitacaoAgendamento));
            } else if (param.isTelaResumoRegulacao()) {
                setResponsePage(new AnaliseResumoRegulacaoSolicitacaoPage(param, solicitacaoAgendamento));
            } else {
                setResponsePage(new AnaliseRegulacaoSolicitacaoPage(param, solicitacaoAgendamento));
            }
        } else if (actionAtendimentoWebDTO != null){
            if (actionAtendimentoWebDTO.isTelaAtendimento()) {
                setResponsePage(new DefaultConsultaAtendimentoPage());
            } else if (actionAtendimentoWebDTO.isTelaAtendimentoAtencaoBasica()) {
                setResponsePage(new ConsultaAtendimentoAtencaoBasicaPage());
            }
        } else if (autorizacaoInternacaoHospitalarDTO != null){
            setResponsePage(new RegulacaoAihPage(autorizacaoInternacaoHospitalarDTO, true));
        } else {
            setResponsePage(ConsultaProntuarioPage.class);
        }
    }

    public void resolveBookmarkLinkClass(AjaxRequestTarget target) {
        resolveBookmarkLinkClass();
        target.add(linkBookmark);
    }

    private void resolveBookmarkLinkClass() {
        if (FavoritosCache.get().getFavoritos().contains(getProgramaFavorito())) {
            if (linkBookmark.getBehaviors().contains(notBookmarked)) {
                linkBookmark.remove(notBookmarked);
            }
            if (!linkBookmark.getBehaviors().contains(bookmarked)) {
                linkBookmark.add(bookmarked);
            }
        } else {
            if (linkBookmark.getBehaviors().contains(bookmarked)) {
                linkBookmark.remove(bookmarked);
            }
            if (!linkBookmark.getBehaviors().contains(notBookmarked)) {
                linkBookmark.add(notBookmarked);
            }
        }
    }

    public ProgramaFavorito getProgramaFavorito() {
        if (getCodigoPrograma() != null) {
            return LoadManager.getInstance(ProgramaFavorito.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProgramaFavorito.PROP_USUARIO, Usuario.PROP_CODIGO), br.com.celk.system.session.ApplicationSession.get().getSession().getCodigoUsuario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProgramaFavorito.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_CODIGO), getCodigoPrograma()))
                    .start().getVO();
        }
        return null;
    }

    private ListView getListView() {
        LoadableDetachableModel model = new LoadableDetachableModel<List<? extends IConsultaProntuarioNode>>() {
            @Override
            protected List<? extends IConsultaProntuarioNode> load() {
                return getNodes();
            }
        };

        ListView listView = new ListView<IConsultaProntuarioNode>("nodes", model) {
            @Override
            protected void populateItem(ListItem<IConsultaProntuarioNode> item) {
                NodeButtonConsultaProntuario nodeButton;
                item.add(nodeButton = new NodeButtonConsultaProntuario("btnNode", item.getModelObject()) {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        criarEventoSistema(usuarioCadsus, eventoSistema, getConsultaProntuarioNode().getIdentificador() + " - (" + getConsultaProntuarioNode().getTitulo() + ")");
                        getConsultaProntuarioController().changeNode(target, getConsultaProntuarioNode());
                    }
                });
                NodeButtonConsultaProntuarioEventListener createListener = nodeButton.createListener();
                nodeButtonListeners.add(createListener);
                createListener.ativar(null, activeNode);
            }
        };
        return listView;
    }

    private void callNodeButtonsAtivar(AjaxRequestTarget target) {
        for (NodeButtonConsultaProntuarioEventListener nodeButtonValidacaoListener : nodeButtonListeners) {
            nodeButtonValidacaoListener.ativar(target, activeNode);
        }
    }

    protected List<IConsultaProntuarioNode> getNodes() {
        try {
            nodeList.clear();

            List<GrupoAtendimentoCbo> grupoAtendimentoCboList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarGrupoAtendimentoCbo(usuarioLogado.getProfissional());
            List<IConsultaProntuarioNode> nodes = new ArrayList<IConsultaProntuarioNode>();

            if (CollectionUtils.isNotNullEmpty(grupoAtendimentoCboList)) {

                if (grupoAtendimentoCboList.size() > 1000) {
                    LogMessageBuilder.create()
                            .usuario(usuarioLogado.getLogin())
                            .classe(ProntuarioPage.class)
                            .mensagem("getNodes()")
                            .logInfo();
                }

                List<NodoConsultaProntuario> nodos;

                if (isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL)) {
                    nodos = LoadManager.getInstance(NodoConsultaProntuario.class)
                            .addSorter(new QueryCustom.QueryCustomSorter(path(on(NodoConsultaProntuario.class).getOrdem())))
                            .start().getList();
                } else {
                    nodos = LoadManager.getInstance(NodoConsultaProntuario.class)
                            .addParameter(new QueryCustomParameter(path(on(NodoConsultaProntuario.class).getGrupoAtendimentoCbo()), BuilderQueryCustom.QueryParameter.IN, grupoAtendimentoCboList))
                            .addSorter(new QueryCustom.QueryCustomSorter(path(on(NodoConsultaProntuario.class).getOrdem())))
                            .start().getList();
                }

                NodoConsultaProntuario proxy = on(NodoConsultaProntuario.class);

                Group<NodoConsultaProntuario> group = group(with(nodos).distinct(proxy.getCodigo()),
                        by(proxy.getClasseNodo()));

                NodoConsultaProntuario pathClass;

                for (Group<NodoConsultaProntuario> sub : group.subgroups()) {
                    pathClass = sub.first();

                    try {
                        IConsultaProntuarioNode nodo = (IConsultaProntuarioNode) Class.forName(pathClass.getClasseNodo()).newInstance();
                        nodeList.add(nodo.getIdentificador());
                        nodes.add(nodo);
                    } catch (ClassNotFoundException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }

                }

            }
            return nodes;
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Logger.getLogger(ProntuarioPage.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(ProntuarioPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        return new ArrayList<IConsultaProntuarioNode>();
    }

    private void changeNode(IConsultaProntuarioNode node) {
        activeNode = node;
        DefaultConsultaProntuarioPanel newInstance = node.getPanel(getConsultaProntuarioController().panelId());
        changePanel(null, newInstance);
    }

    private void changePanel(AjaxRequestTarget target, DefaultConsultaProntuarioPanel panel) {
        Component activePanel = panelContainer.get(getConsultaProntuarioController().panelId());
        if (activePanel != null) {
            activePanel.replaceWith(panel);
        } else {
            panelContainer.add(panel);
        }
        panel.setConsultaProntuarioController(getConsultaProntuarioController());
        panel.setIdentificador(activeNode.getIdentificador());
        if (target != null) {
            panel.changePanelAction(target);
        }
    }

    public IConsultaProntuarioController getConsultaProntuarioController() {
        if (this.prontuarioController == null) {
            this.prontuarioController = new IConsultaProntuarioController() {

                @Override
                public UsuarioCadsus getUsuarioCadsus() {
                    return ProntuarioPage.this.usuarioCadsus;
                }

                @Override
                public Usuario getUsuario() {
                    return ProntuarioPage.this.usuarioLogado;
                }

                @Override
                public void changeNode(AjaxRequestTarget target, IConsultaProntuarioNode node) {
                    ProntuarioPage.this.changeNode(node);
                    ProntuarioPage.this.callNodeButtonsAtivar(target);
                    target.add(panelContainer);
                }

                @Override
                public void changePanel(AjaxRequestTarget target, DefaultConsultaProntuarioPanel panel) {
                    ProntuarioPage.this.changePanel(target, panel);
                    target.add(panelContainer);
                    target.appendJavaScript(JScript.scrollToTop());
                }

                @Override
                public String newWindowId() {
                    return ProntuarioPage.this.newModalId();
                }

                @Override
                public void addWindow(AjaxRequestTarget target, Window window) {
                    ProntuarioPage.this.addModal(target, window);
                }

                @Override
                public String panelId() {
                    return PANEL_ID;
                }

            };
        }

        return this.prontuarioController;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProntuario");
    }

    @Override
    public void info(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.INFO);
    }

    @Override
    public void warn(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.WARNING);
    }

    @Override
    public void error(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.ERROR);
    }

    @Override
    public void clearNotifications(AjaxRequestTarget target) {
        getSession().getFeedbackMessages().clear();
        updateNotificationPanel(target);
    }

    @Override
    public void message(AjaxRequestTarget target, String message, int lvl) {
        getSession().getFeedbackMessages().add(new FeedbackMessage(notificationPanel, message, lvl));
        updateNotificationPanel(target);
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target) {
        updateNotificationPanel(target, true);
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop) {
        target.add(notificationPanel);
        if (scrollToTop) {
            target.appendJavaScript(JScript.scrollToTop());
        }
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    public EventoSistema criarEventoSistema(UsuarioCadsus usuarioCadsus, EventoSistema eventoSistemaPai, String nodeName) {
        EventoSistemaDTO eventoSistemaDTO = new EventoSistemaDTO();
        if (eventoSistemaPai != null) {
            eventoSistemaDTO.setCodigoEventoSistemaPai(eventoSistemaPai.getCodigo());
            eventoSistemaDTO.setDescricao(BundleManager.getString("msgAcessoProntuarioComNode_x", usuarioCadsus.getCodigo(),
                    nodeName));
        } else {
            eventoSistemaDTO.setDescricao(BundleManager.getString("msgAcessoProntuario_x", usuarioCadsus.getReferencia()));
        }
        eventoSistemaDTO.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
        eventoSistemaDTO.setFonteEvento(BundleManager.getString("consultaProntuario"));
        eventoSistemaDTO.setKeyword(BundleManager.getString("prontuario").toUpperCase());
        eventoSistemaDTO.setTipoEvento(EventoSistema.TipoEvento.PROGRAMA.value());
        eventoSistemaDTO.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.PRONTUARIO.value());
        if (usuarioCadsus != null) {
            eventoSistemaDTO.setUsuarioCadsus(usuarioCadsus);
        }
        EventoSistema eventoSalvo = null;
        try {
            eventoSalvo = BOFactory.getBO(CommomFacade.class).gerarEventoSistema(eventoSistemaDTO);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage());
        }
        return eventoSalvo;
    }
}
