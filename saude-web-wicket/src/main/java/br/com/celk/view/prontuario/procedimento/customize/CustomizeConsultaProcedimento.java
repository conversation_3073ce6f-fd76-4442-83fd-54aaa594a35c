package br.com.celk.view.prontuario.procedimento.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaProcedimento extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Procedimento.PROP_DESCRICAO), QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Procedimento.PROP_CODIGO)));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(Procedimento.PROP_CODIGO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(Procedimento.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return Procedimento.class;
    }

}
