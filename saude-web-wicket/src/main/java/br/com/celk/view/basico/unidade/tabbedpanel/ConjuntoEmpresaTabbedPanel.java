package br.com.celk.view.basico.unidade.tabbedpanel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.unidade.ConsultaEmpresaPage;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EmpresaConjuntoDTO;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
public class ConjuntoEmpresaTabbedPanel extends CadastroTabbedPanel<EmpresaConjuntoDTO> {

    public ConjuntoEmpresaTabbedPanel(String id, EmpresaConjuntoDTO empresa, boolean viewOnly, List<ITab> tabs) {
        super(id, empresa, viewOnly, tabs, true);
    }

    public ConjuntoEmpresaTabbedPanel(String id, EmpresaConjuntoDTO empresa, List<ITab> tabs) {
        super(id, empresa, tabs);
    }

    public ConjuntoEmpresaTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<EmpresaConjuntoDTO> getReferenceClass() {
        return EmpresaConjuntoDTO.class;
    }

    @Override
    public Object salvar(EmpresaConjuntoDTO object) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoGeralFacade.class).salvarEmpresaConjunto(object);
        return null;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaEmpresaPage.class;
    }
}
