<wicket:extend>
    <fieldset>
        <div class="field"><label><wicket:message key="naturezaProcura"/></label><div class="group" wicket:id="naturezaProcura"/></div>
        <div class="field"><label><wicket:message key="tipoAtendimento"/></label><div class="group" wicket:id="tipoAtendimento"/></div>
        <div class="field"><label><wicket:message key="imprimeTermoAutorizacao"/></label><select wicket:id="imprimeTermoAutorizacao"/></div>
        <div class="field"><label><wicket:message key="imprimeFichaCadastral"/></label><select wicket:id="imprimeFichaPaciente"/></div>
        <div class="field"><label><wicket:message key="tipoProcedimento"/></label><div class="group" wicket:id="tipoProcedimento"/></div>
        <div class="field"><label><wicket:message key="tipoExame"/></label><div class="group" wicket:id="tipoExame"/></div>
        <div class="field">
            <div class="span-horizontal">
                <fieldset wicket:id="containerEmpresaNaturezaTipo">
                    <h2><label><wicket:message key="unidades"/></label></h2>
                    <div class="field"><label><wicket:message key="unidade"/></label><div class="group" wicket:id="empresaNaturezaProcuraTipoAtendimento.empresa"/></div>
                    <div class="field">
                        <div class="span-2"><label><wicket:message key="controleAtendimentoAbv"/></label><select wicket:id="empresaNaturezaProcuraTipoAtendimento.controleAtendimento"/></div>
                        <div class="span-3"><label><wicket:message key="numeroVagasAbv"/></label><input type="text" maxlength="10" size="10" wicket:id="empresaNaturezaProcuraTipoAtendimento.numeroVagaDia"/></div>
                        <div class="span-5 last"><label><wicket:message key="imprimeFaa" /></label><select wicket:id="empresaNaturezaProcuraTipoAtendimento.imprimeFaa" /></div>
                    </div>
                    <div class="field">
                        <div class="span-2"><label><wicket:message key="atendimentoVisivel"/></label><select wicket:id="empresaNaturezaProcuraTipoAtendimento.visivel"/></div>
                        <div class="span-3 last"><label><wicket:message key="registrarFaltaProntuario" /></label><select wicket:id="empresaNaturezaProcuraTipoAtendimento.faltaProntuario" /></div>
                    </div>
                    <div class="field"><label><wicket:message key="painel"/></label><div class="group" wicket:id="empresaNaturezaProcuraTipoAtendimento.painel"/></div>
                    <div class="field"><label><wicket:message key="farmaciaDispensacao"/></label><div class="group" wicket:id="empresaNaturezaProcuraTipoAtendimento.empresaDispensacao"/></div>
                    <div class="field">
                        <div class="span-horizontal">
                            <input type="button" class="arrow-bottom" wicket:message="value:adicionar" wicket:id="btnAdicionarEmpresa"/>
                        </div>
                    </div>
                    <div class="field">
                        <div class="span-horizontal">
                            <div wicket:id="tblEmpresaNaturezaTipo"/>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="field">
            <div class="span-horizontal">
                <fieldset wicket:id="containerEloNaturezaTipoEncaminhamento">
                    <h2><label><wicket:message key="encaminhamentos"/></label></h2>
                    <div class="field"><label><wicket:message key="tipoAtendimento"/></label><div class="group" wicket:id="tipoAtendimento"/></div>
                    <div class="field"><label><wicket:message key="encaminhamento"/></label><select wicket:id="encaminhamentoTipo"/></div>
                    <div class="field">
                        <div class="span-horizontal">
                            <input type="button" class="arrow-bottom" wicket:message="value:adicionar" wicket:id="btnAdicionarEloNaturezaTipoEncaminhamento"/>
                        </div>
                    </div>
                    <div class="field">
                        <div class="span-horizontal">
                            <div wicket:id="tblEloNaturezaTipoEncaminhamento"/>
                            <p><wicket:message key="paraDefinirEncaminhamentoPadraoSelecione"/></p>
                            <input type="button" class="doc-empty" wicket:message="value:limparSelecao" wicket:id="btnLimparSelecao"/>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </fieldset>
</wicket:extend>
