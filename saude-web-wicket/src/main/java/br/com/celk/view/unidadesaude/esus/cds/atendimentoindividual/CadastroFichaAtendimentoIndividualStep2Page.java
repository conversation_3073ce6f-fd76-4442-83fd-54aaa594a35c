package br.com.celk.view.unidadesaude.esus.cds.atendimentoindividual;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.unidadesaude.exames.esus.autocomplete.AutoCompleteConsultaExameEsus;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaAtendimentoIndividualDTO;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaAtendimentoIndividualItemDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividual;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento;
import br.com.ksisolucoes.vo.esus.ExameEsus;
import br.com.ksisolucoes.vo.prontuario.basico.Ciap;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroFichaAtendimentoIndividualStep2Page extends BasePage {

    private Form<EsusFichaAtendIndividualItem> form;
    private final CadastroFichaAtendimentoIndividualDTO fichaAtendimentoIndividualDTO;
    private final CadastroFichaAtendimentoIndividualItemDTO itemFichaAtendimentoIndividualDTO;
    private InputField txtNumeroCartao;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private DateChooser dcDataNascimento;
    private DropDown<String> dropDownSexo;
    private DropDown dropDownGravidezPlanejada;
    private String cns;
    private DoubleField txtPeso;
    private DoubleField txtPerimetroCefalico;
    private DoubleField txtAltura;
    private LongField txtIdadeGestacional;
    private LongField txtNumeroGestasPrevias;
    private LongField txtNumeroPartos;
    private String sexo;
    private Table tblProcedimentos;
    private WebMarkupContainer containerProcedimentos;
    private CompoundPropertyModel<EsusFichaAtendIndividualItemProcedimento> modelProcedimentos;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private AutoCompleteConsultaExameEsus autoCompleteConsultaExameEsus;
    private DateChooser dchDumGestante;
    private Ciap ciap;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaCid autoCompleteConsultaCid2;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap2;

    private final List<CheckBoxLongValue> lstCheckBoxProblemaCondicaoAvaliada = new ArrayList<CheckBoxLongValue>();

    private final List<CheckBoxLongValue> lstCheckBoxNasf = new ArrayList<CheckBoxLongValue>();
    private final List<CheckBoxLongValue> lstCheckBoxConduta = new ArrayList<CheckBoxLongValue>();

    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaAsma;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDengue;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDesnutricao;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDiabetes;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDpoc;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDst;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaHanseniase;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaHipertensaoArterial;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaObesidade;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaPreNatal;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaPuericultura;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaPuerperio42Dias;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaCancerMama;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaCancerColoUtero;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaRiscoCardiovascular;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaReabilitacao;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaSaudeMental;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaSaudeSexualReprodutiva;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaTabagismo;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaTuberculose;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaUsuarioAlcool;
    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaUsuarioOutrasDrogas;

    private CheckBoxLongValue checkBoxProblemaCondicaoAvaliadaDoCiap;

    private CheckBoxLongValue checkBoxNasfAvaliacaoDiagnostico;
    private CheckBoxLongValue checkBoxNasfProcedimentosClinicosTerapeuticos;
    private CheckBoxLongValue checkBoxNasfPrescricaoTerapeutica;

    private CheckBoxLongValue checkBoxCondutaRetornoConsultaAgendada;
    private CheckBoxLongValue checkBoxCondutaRetornoCuidadoContinuadoProgramado;
    private CheckBoxLongValue checkBoxCondutaAgendamentoGrupos;
    private CheckBoxLongValue checkBoxCondutaAgendamentoEmulti;
    private CheckBoxLongValue checkBoxCondutaAltaEpisodio;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoInternoDia;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoServicoEspecializado;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoCaps;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoInternacaoHospitalar;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoUrgencia;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoServicoAtencaoDomiciliar;
    private CheckBoxLongValue checkBoxCondutaEncaminhamentoIntersetorial;
    private final boolean enable;

    private final String CSS_FILE = "CadastroFichaAtendimentoIndividualStep2Page.css";

    public CadastroFichaAtendimentoIndividualStep2Page(CadastroFichaAtendimentoIndividualDTO cadastroFichaAtendimentoIndividualDTO) {
        this.fichaAtendimentoIndividualDTO = cadastroFichaAtendimentoIndividualDTO;
        this.itemFichaAtendimentoIndividualDTO = new CadastroFichaAtendimentoIndividualItemDTO();
        this.enable = true;
    }

    public CadastroFichaAtendimentoIndividualStep2Page(CadastroFichaAtendimentoIndividualDTO cadastroFichaAtendimentoIndividualDTO, CadastroFichaAtendimentoIndividualItemDTO itemFichaAtendimentoIndividualDTO) {
        this.fichaAtendimentoIndividualDTO = cadastroFichaAtendimentoIndividualDTO;
        this.itemFichaAtendimentoIndividualDTO = itemFichaAtendimentoIndividualDTO;
        this.enable = true;
        if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getNumeroCartao() != null) {
            this.cns = StringUtils.leftPad(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getNumeroCartao().toString(), 15, '0');
        }
        if (EsusFichaAtendIndividualItem.SEXO_MASCULINO.equals(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getSexo())) {
            sexo = UsuarioCadsus.SEXO_MASCULINO;
        } else {
            sexo = UsuarioCadsus.SEXO_FEMININO;
        }
    }

    public CadastroFichaAtendimentoIndividualStep2Page(CadastroFichaAtendimentoIndividualDTO cadastroFichaAtendimentoIndividualDTO, CadastroFichaAtendimentoIndividualItemDTO itemFichaAtendimentoIndividualDTO, boolean enable) {
        this.fichaAtendimentoIndividualDTO = cadastroFichaAtendimentoIndividualDTO;
        this.itemFichaAtendimentoIndividualDTO = itemFichaAtendimentoIndividualDTO;
        this.enable = enable;
        if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getNumeroCartao() != null) {
            this.cns = StringUtils.leftPad(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getNumeroCartao().toString(), 15, '0');
        }
        if (EsusFichaAtendIndividualItem.SEXO_MASCULINO.equals(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getSexo())) {
            sexo = UsuarioCadsus.SEXO_MASCULINO;
        } else {
            sexo = UsuarioCadsus.SEXO_FEMININO;
        }
    }

    @Override
    protected void postConstruct() {

        EsusFichaAtendIndividualItem proxy = on(EsusFichaAtendIndividualItem.class);

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTurno()), EsusFichaAtendIndividualItem.Turno.values(), true, false, false, true).setLabel(new Model(bundle("turno"))).setEnabled(enable));
        getForm().add(new InputField(path(proxy.getNumeroProntuario())).setEnabled(enable));
        getForm().add(txtNumeroCartao = (InputField) new InputField("cns", new PropertyModel(this, "cns")).setEnabled(enable));
        txtNumeroCartao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dcDataNascimento.setEnabled(false);
                dcDataNascimento.setRequiredField(false);
                dropDownSexo.setEnabled(false);
                if (cns != null && CnsValidator.validaCns(cns = StringUtil.getDigits(cns))) {
                    List<UsuarioCadsusCns> lstUsuarioCadsusCnsValidacao = LoadManager.getInstance(UsuarioCadsusCns.class)
                            .addProperties(new HQLProperties(UsuarioCadsusCns.class).getProperties())
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                            .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), Coalesce.asLong(cns)))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                            .start().getList();
                    if (CollectionUtils.isNotNullEmpty(lstUsuarioCadsusCnsValidacao)) {
                        autoCompleteConsultaUsuarioCadsus.limpar(target);
                        carregarDadosPaciente(target, lstUsuarioCadsusCnsValidacao.get(0).getUsuarioCadsus());
                    } else {
                        limparCampos(target);
                    }
                } else {
                    if (cns != null) {
                        getSession().getFeedbackMessages().warn(CadastroFichaAtendimentoIndividualStep2Page.this, Bundle.getStringApplication("msg_cns_invalido"));
                    }
                    limparCampos(target);
                }

                enableGestante(target);
                CadastroFichaAtendimentoIndividualStep2Page.this.updateNotificationPanel(target);
                updateCiap(target);
                updateCid(target);
            }
        });

        getForm().add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        autoCompleteConsultaUsuarioCadsus.add(autoCompletePacienteConsultaListner());
        autoCompleteConsultaUsuarioCadsus.add(autoCompletePacienteRemoveListner());


        getForm().add(dcDataNascimento = new RequiredDateChooser(path(proxy.getDataNascimento())));
        dcDataNascimento.getData().setMaxDate(new DateOption(fichaAtendimentoIndividualDTO.getEsusFichaAtendIndividual().getDataAtendimento()))
                .setLabel(new Model<>(bundle("dataNascimento")))
                .setEnabled(enable);

        getForm().add(dropDownSexo = (DropDown) DropDownUtil.getSexoDropDown("sexo", new PropertyModel(this, "sexo"), true).setEnabled(enable));
        dropDownSexo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableGestante(target);
                updateCiap(target);
                updateCid(target);
            }
        });

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getLocalAtendimento()), EsusFichaAtendIndividualItem.LocalAtendimento.values(), true, true, false, true).setLabel(new Model(bundle("localAtendimento"))).setEnabled(enable));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoAtendimento()), EsusFichaAtendIndividualItem.TipoAtendimento.values(), true, true, false, true).setLabel(new Model(bundle("tipoAtendimento"))).setEnabled(enable));
        getForm().add(txtPerimetroCefalico = new DoubleField(path(proxy.getPerimetroCefalico())));
        txtPerimetroCefalico.setConvertZeroToNull(true).setVMax(200D).setMDec(2);
        getForm().add(txtPeso = new DoubleField(path(proxy.getPeso())));
        txtPerimetroCefalico.setEnabled(enable);
        txtPeso.setEnabled(enable);
        txtPeso.setConvertZeroToNull(true).setVMax(500D).setMDec(3);

        getForm().add(txtAltura = new DoubleField(path(proxy.getAltura())));
        txtAltura.setEnabled(enable);
        txtAltura.setConvertZeroToNull(true).setVMax(250D).setMDec(1);

        getForm().add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getVacinaEmDia()), true, false).setEnabled(enable));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getAleitamentoMaterno()), EsusFichaAtendIndividualItem.AleitamentoMaterno.values(), true, false, true).setLabel(new Model(bundle("aleitamentoMaterno"))).setEnabled(enable));
        getForm().add(dchDumGestante = (DateChooser) new DateChooser(path(proxy.getDumGestante())).setEnabled(enable));
        dchDumGestante.addAjaxUpdateValue();

        getForm().add(dropDownGravidezPlanejada = (DropDown) DropDownUtil.getSimNaoLongDropDown(path(proxy.getGravidezPlanejada()), true, false).setEnabled(enable));
        getForm().add(txtIdadeGestacional = new LongField(path(proxy.getIdadeGestacional())));
        txtIdadeGestacional.setEnabled(false);
        txtIdadeGestacional.setConvertZeroToNull(true).setVMin(1L).setVMax(42L);

        getForm().add(txtNumeroGestasPrevias = new LongField(path(proxy.getNumeroGestasPrevias())));
        txtNumeroGestasPrevias.setEnabled(enable);
        txtNumeroGestasPrevias.setVMax(99L);
        getForm().add(txtNumeroPartos = new LongField(path(proxy.getNumeroPartos())));
        txtNumeroPartos.setEnabled(enable);
        txtNumeroPartos.setVMax(99L);

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getAtencaoDomiciliar()), EsusFichaAtendIndividualItem.ModalidadeAD.values(), true, false, true).setLabel(new Model(bundle("modalidadeAd"))).setEnabled(enable));

        addCheckBoxProblemaCondicaoAvaliada();

//        getForm().add(new InputField(path(proxy.getOutroCiap1())).setEnabled(enable));
//        getForm().add(new InputField(path(proxy.getOutroCiap2())).setEnabled(enable));

        if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem() != null && itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getCiap() != null) {
            String classificacaoCiap = getCiapInvalido(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getCiap().getReferencia());

            if (!classificacaoCiap.isEmpty()) {
                Long sumValue = EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.valeuOf(classificacaoCiap).sum();
                getCheckBoxProblemaCondicaoAvaliada(sumValue);
            }

            if (!classificacaoCiap.isEmpty()) {
                itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().setCiap(null);
                getForm().add(addConsultaCiap(path(proxy.getCiap())));
            } else {
                getForm().add(addConsultaCiap(path(proxy.getCiap())));
            }
        } else {
            getForm().add(addConsultaCiap(path(proxy.getCiap())));
        }

        getForm().add(addConsultaCiap2(path(proxy.getCiap2())));
//        getForm().add(new AutoCompleteConsultaCiap(path(proxy.getCiap2())).setEnabled(enable));
        autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid()));
        autoCompleteConsultaCid.ocultarCidsInativos();
        autoCompleteConsultaCid.setEnabled(enable);
        autoCompleteConsultaCid.addAjaxUpdateValue();
        autoCompleteConsultaCid.setOutputMarkupId(true);
        autoCompleteConsultaCid.add(new Tooltip().setText("msgInfCidSexo"));
        getForm().add(autoCompleteConsultaCid);

        autoCompleteConsultaCid2 = new AutoCompleteConsultaCid(path(proxy.getCid2()));
        autoCompleteConsultaCid2.ocultarCidsInativos();
        autoCompleteConsultaCid2.setEnabled(enable);
        autoCompleteConsultaCid2.addAjaxUpdateValue();
        autoCompleteConsultaCid2.setOutputMarkupId(true);
        autoCompleteConsultaCid2.add(new Tooltip().setText("msgInfCidSexo"));
        getForm().add(autoCompleteConsultaCid2);


        // Procedimentos
        EsusFichaAtendIndividualItemProcedimento proxyProcedimento = on(EsusFichaAtendIndividualItemProcedimento.class);

        containerProcedimentos = new WebMarkupContainer("containerProcedimentos", modelProcedimentos = new CompoundPropertyModel<>(new EsusFichaAtendIndividualItemProcedimento()));
        containerProcedimentos.setOutputMarkupId(true);
        containerProcedimentos.setEnabled(enable);

        containerProcedimentos.add(autoCompleteConsultaExameEsus = new AutoCompleteConsultaExameEsus(path(proxyProcedimento.getExameEsus())));
        autoCompleteConsultaExameEsus.add(new ConsultaListener<ExameEsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ExameEsus exameEsus) {
                autoCompleteConsultaProcedimento.limpar(target);
                modelProcedimentos.getObject().setProcedimento(null);
                autoCompleteConsultaProcedimento.setEnabled(false);
                target.add(autoCompleteConsultaProcedimento);
            }
        });

        autoCompleteConsultaExameEsus.add(new RemoveListener<ExameEsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ExameEsus exameEsus) {
                enableProcedimento(target);
            }
        });
        containerProcedimentos.add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(path(proxyProcedimento.getProcedimento())));
        autoCompleteConsultaProcedimento.setFaturavel(true);
        autoCompleteConsultaProcedimento.add(new ConsultaListener<Procedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Procedimento procedimento) {
                autoCompleteConsultaExameEsus.limpar(target);
                modelProcedimentos.getObject().setExameEsus(null);
                autoCompleteConsultaExameEsus.setEnabled(false);
                target.add(autoCompleteConsultaExameEsus);
            }
        });

        autoCompleteConsultaProcedimento.add(new RemoveListener<Procedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Procedimento procedimento) {
                enableProcedimentoEsus(target);
            }
        });

        containerProcedimentos.add(DropDownUtil.getIEnumDropDown(path(proxyProcedimento.getSolicitadoAvaliado()), EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.values(), true, false, true).setLabel(new Model(bundle("solicitadoAvaliado"))));

        containerProcedimentos.add(new AbstractAjaxButton("btnAdicionarItem") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarItem(target);
            }
        });

        containerProcedimentos.add(tblProcedimentos = new Table("tblProcedimentos", getColumnsProcedimentos(), getCollectionProviderProcedimentos()));
        tblProcedimentos.populate();
        tblProcedimentos.setScrollY("1800");

        getForm().add(containerProcedimentos);

        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getRacionalidadeSaude()), EsusFichaAtendIndividualItem.RacionalidadeSaude.values(), true, false, true).setLabel(new Model(bundle("praticasIntegrativasComplementares"))).setEnabled(enable));
        getForm().add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getFicouEmObservacao()), true, false).setEnabled(enable));

        addCheckBoxNasf();

        addCheckBoxConduta();

        getForm().add(new VoltarButton("btnVoltar"));
        SubmitButton btnSalvarContinuar;
        getForm().add(btnSalvarContinuar = new SubmitButton("btnSalvarContinuar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(getForm());
        btnSalvarContinuar.setVisible(enable);

        EsusFichaAtendIndividualItem item = itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem();
        if (item != null && item.getCodigo() != null) {
            carregarCheckbox(item);
            enableGestante(null);

            if (CnsValidator.validaCns(cns, false)) {
                boolean isCNSPacienteCadastrado = LoadManager.getInstance(UsuarioCadsusCns.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), Coalesce.asLong(cns)))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                        .exists();
                if (isCNSPacienteCadastrado) {
                    dcDataNascimento.setEnabled(false);
                    dropDownSexo.setEnabled(false);
                }
            }
        }
        updateCiap(null);
        updateCid(null);
    }

    private ConsultaListener<UsuarioCadsus> autoCompletePacienteConsultaListner() {
        return new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                carregarDadosPaciente(target, usuarioCadsus);

                enableGestante(target);
                CadastroFichaAtendimentoIndividualStep2Page.this.updateNotificationPanel(target);
                updateCiap(target);
                updateCid(target);
            }
        };
    }

    private RemoveListener<UsuarioCadsus> autoCompletePacienteRemoveListner() {
        return new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                clearNotifications(target);
                limparCampos(target);

                enableGestante(target);
                CadastroFichaAtendimentoIndividualStep2Page.this.updateNotificationPanel(target);
                updateCiap(target);
                updateCid(target);
            }
        };
    }

    private void carregarDadosPaciente(AjaxRequestTarget target, UsuarioCadsus paciente) {

        getForm().getModel().getObject().setUsuarioCadsus(paciente);
        target.appendJavaScript(JScript.initMasks());
        target.add(autoCompleteConsultaUsuarioCadsus);

        txtNumeroCartao.limpar(target);
        txtNumeroCartao.setComponentValue(paciente.getCns());
        target.add(txtNumeroCartao);

        dcDataNascimento.limpar(target);
        dcDataNascimento.setComponentValue(paciente.getDataNascimento());
        target.add(dcDataNascimento);

        dropDownSexo.limpar(target);
        dropDownSexo.setComponentValue(paciente.getSexo());
        dropDownSexo.setEnabled(false);
        target.add(dropDownSexo);

        getSession().getFeedbackMessages().clear();
    }

    private void limparCampos(AjaxRequestTarget target) {
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        getForm().getModel().getObject().setUsuarioCadsus(null);
        autoCompleteConsultaUsuarioCadsus.limpar(target);
        dcDataNascimento.setEnabled(true);
        dcDataNascimento.setRequiredField(true);
        dcDataNascimento.limpar(target);
        dropDownSexo.setEnabled(true);
        dropDownSexo.setRequired(true);
        dropDownSexo.limpar(target);
        txtNumeroCartao.setEnabled(true);
        txtNumeroCartao.setRequired(true);
        txtNumeroCartao.limpar(target);
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void getCheckBoxProblemaCondicaoAvaliada(Long sumValue) {
        if (sumValue != null) {
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.ASMA.sum())) {
                checkBoxProblemaCondicaoAvaliadaAsma.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DENGUE.sum())) {
                checkBoxProblemaCondicaoAvaliadaDengue.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DESNUTRICAO.sum())) {
                checkBoxProblemaCondicaoAvaliadaDesnutricao.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DIABETES.sum())) {
                checkBoxProblemaCondicaoAvaliadaDiabetes.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DST.sum())) {
                checkBoxProblemaCondicaoAvaliadaDst.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.HANSENIASE.sum())) {
                checkBoxProblemaCondicaoAvaliadaHanseniase.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.HIPERTENSAO_ARTERIAL.sum())) {
                checkBoxProblemaCondicaoAvaliadaHipertensaoArterial.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.OBESIDADE.sum())) {
                checkBoxProblemaCondicaoAvaliadaObesidade.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PRE_NATAL.sum())) {
                checkBoxProblemaCondicaoAvaliadaPreNatal.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PUERICULTURA.sum())) {
                checkBoxProblemaCondicaoAvaliadaPuericultura.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PUERPERIO_42_DIAS.sum())) {
                checkBoxProblemaCondicaoAvaliadaPuerperio42Dias.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PUERPERIO_42_DIAS.sum())) {
                checkBoxProblemaCondicaoAvaliadaCancerMama.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.CANCER_COLO_UTERO.sum())) {
                checkBoxProblemaCondicaoAvaliadaCancerColoUtero.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.RISCO_CARDIOVASCULAR.sum())) {
                checkBoxProblemaCondicaoAvaliadaRiscoCardiovascular.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.REABILITACAO.sum())) {
                checkBoxProblemaCondicaoAvaliadaReabilitacao.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.SAUDE_MENTAL.sum())) {
                checkBoxProblemaCondicaoAvaliadaSaudeMental.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.SAUDE_SEXUAL_REPRODUTIVA.sum())) {
                checkBoxProblemaCondicaoAvaliadaSaudeSexualReprodutiva.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.TABAGISMO.sum())) {
                checkBoxProblemaCondicaoAvaliadaTabagismo.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.TUBERCULOSE.sum())) {
                checkBoxProblemaCondicaoAvaliadaTuberculose.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.USUARIO_ALCOOL.sum())) {
                checkBoxProblemaCondicaoAvaliadaUsuarioAlcool.setComponentValue(sumValue);
            }
            if (sumValue.equals(EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.USUARIO_OUTRAS_DROGAS.sum())) {
                checkBoxProblemaCondicaoAvaliadaUsuarioOutrasDrogas.setComponentValue(sumValue);
            }
        }
    }

    private String getCiapInvalido(String ciap) {
        if (ciap == null) {
            return "";
        }

        HashMap<String, String> ocorrencias = new HashMap<>();

        ocorrencias.put("R96", "ABP009"); //ASMA
        ocorrencias.put("A77", "ABP019"); //DENGUE
        ocorrencias.put("T91", "ABP008"); //DESNUTRICAO
        ocorrencias.put("T90", "ABP006"); //DIABETES
        ocorrencias.put("R95", "ABP010"); //DPOC
        ocorrencias.put("A78", "ABP018"); //HANSENIASE
        ocorrencias.put("K86", "ABP005"); //HIPERTENSAO_ARTERIAL
        ocorrencias.put("T82", "ABP007"); //OBESIDADE
        ocorrencias.put("W78", "ABP001"); //PRE_NATAL
        ocorrencias.put("A98", "ABP004"); //PUERICULTURA
        ocorrencias.put("W18", "ABP002"); //PUERPERIO_42_DIAS
        ocorrencias.put("K22", "ABP024"); //RISCO_CARDIOVASCULAR
        ocorrencias.put("P17", "ABP011"); //TABAGISMO
        ocorrencias.put("A70", "ABP017"); //TUBERCULOSE
        ocorrencias.put("P16", "ABP012"); //USUARIO_ALCOOL
        ocorrencias.put("P19", "ABP013"); //USUARIO_OUTRAS_DROGAS

        for (Map.Entry<String, String> i : ocorrencias.entrySet()) {
            if (i.getKey().equals(ciap)) {
                return ocorrencias.get(ciap);
            }
        }

        return "";
    }

    private Component addConsultaCiap(String path) {
        autoCompleteConsultaCiap = (AutoCompleteConsultaCiap) new AutoCompleteConsultaCiap(path).setEnabled(enable);
        autoCompleteConsultaCiap.setFiltrarCiapsAB();
        autoCompleteConsultaCiap.add(new AjaxEventBehavior("onchange") {
            @Override
            protected void onEvent(AjaxRequestTarget target) {
                Ciap ciap = (Ciap) autoCompleteConsultaCiap.getModelObject();
                validarCiapSexoPaciente(target, ciap);
            }
        });

        return autoCompleteConsultaCiap;
    }

    private Component addConsultaCiap2(String path) {
        autoCompleteConsultaCiap2 = (AutoCompleteConsultaCiap) new AutoCompleteConsultaCiap(path).setEnabled(enable);
        autoCompleteConsultaCiap2.setFiltrarCiapsAB();
        autoCompleteConsultaCiap2.add(new AjaxEventBehavior("onchange") {
            @Override
            protected void onEvent(AjaxRequestTarget target) {
                Ciap ciap = (Ciap) autoCompleteConsultaCiap2.getModelObject();
                validarCiapSexoPaciente(target, ciap);
            }
        });

        return autoCompleteConsultaCiap2;
    }

    private void updateCid(AjaxRequestTarget target) {
        autoCompleteConsultaCid.setSexo(sexo);
        autoCompleteConsultaCid2.setSexo(sexo);
        if (target != null) {
            autoCompleteConsultaCid.limpar(target);
            autoCompleteConsultaCid2.limpar(target);
            target.add(autoCompleteConsultaCid);
            target.add(autoCompleteConsultaCid2);
        }
    }

    private void updateCiap(AjaxRequestTarget target) {
        boolean isSexoFeminino = UsuarioCadsus.SEXO_FEMININO.equals(sexo);
        checkBoxProblemaCondicaoAvaliadaPreNatal.setEnabled(isSexoFeminino);
        checkBoxProblemaCondicaoAvaliadaPuerperio42Dias.setEnabled(isSexoFeminino);
        checkBoxProblemaCondicaoAvaliadaCancerColoUtero.setEnabled(isSexoFeminino);
        if (target != null) {
            if (!isSexoFeminino) {
                checkBoxProblemaCondicaoAvaliadaPreNatal.limpar(target);
                checkBoxProblemaCondicaoAvaliadaPuerperio42Dias.limpar(target);
                checkBoxProblemaCondicaoAvaliadaCancerColoUtero.limpar(target);
            }
            target.add(checkBoxProblemaCondicaoAvaliadaPreNatal);
            target.add(checkBoxProblemaCondicaoAvaliadaPuerperio42Dias);
            target.add(checkBoxProblemaCondicaoAvaliadaCancerColoUtero);
        }
    }

    private void enableGestante(AjaxRequestTarget target) {
        if (target != null) {
            dchDumGestante.limpar(target);
            dropDownGravidezPlanejada.limpar(target);
            txtIdadeGestacional.limpar(target);
            txtNumeroGestasPrevias.limpar(target);
            txtNumeroPartos.limpar(target);
        }

        if (enable) {
            if (UsuarioCadsus.SEXO_MASCULINO.equals(sexo)) {
                dchDumGestante.setEnabled(false);
                dropDownGravidezPlanejada.setEnabled(false);
                txtNumeroGestasPrevias.setEnabled(false);
                txtNumeroPartos.setEnabled(false);
            } else {
                PreNatal preNatal = UsuarioCadsusHelper.getPreNatal(getForm().getModel().getObject().getUsuarioCadsus());
                if (preNatal != null) {
                    dchDumGestante.setComponentValue(preNatal.getDataUltimaMenstruacao());
                    dchDumGestante.setEnabled(false);

                    dropDownGravidezPlanejada.setComponentValue(preNatal.getGravidezPlanejada());
                    txtNumeroGestasPrevias.setComponentValue(preNatal.getGestacoes());
                    txtNumeroPartos.setComponentValue(preNatal.getPartos());

                    if (target != null) {
                        target.add(dchDumGestante);
                        target.add(dropDownGravidezPlanejada);
                        target.add(txtNumeroGestasPrevias);
                        target.add(txtNumeroPartos);
                    }
                } else {
                    dchDumGestante.setEnabled(true);
                    dropDownGravidezPlanejada.setEnabled(true);
                    txtNumeroGestasPrevias.setEnabled(true);
                    txtNumeroPartos.setEnabled(true);
                }
                dropDownGravidezPlanejada.setEnabled(true);
                txtNumeroGestasPrevias.setEnabled(true);
                txtNumeroPartos.setEnabled(true);
            }
        }

        if (target != null) {
            target.add(dchDumGestante);
            target.add(dropDownGravidezPlanejada);
            target.add(txtNumeroGestasPrevias);
            target.add(txtNumeroPartos);
        }
    }

    private List<IColumn> getColumnsProcedimentos() {
        List<IColumn> columns = new ArrayList<>();
        EsusFichaAtendIndividualItemProcedimento proxy = on(EsusFichaAtendIndividualItemProcedimento.class);

        columns.add(getActionColumnProcedimentos());
        columns.add(createColumn(bundle("exameEsus"), proxy.getExameEsus().getDescricaoExameEsus()));
        columns.add(createColumn(bundle("outroSia"), proxy.getProcedimento().getDescricaoFormatado()));
        columns.add(createColumn(bundle("solicitadoAvaliado"), proxy.getAvaliadoSolicitadoFormatado()));

        return columns;
    }

    private IColumn getActionColumnProcedimentos() {
        return new MultipleActionCustomColumn<EsusFichaAtendIndividualItemProcedimento>() {
            @Override
            public void customizeColumn(EsusFichaAtendIndividualItemProcedimento rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EsusFichaAtendIndividualItemProcedimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, EsusFichaAtendIndividualItemProcedimento modelObject) throws ValidacaoException, DAOException {
                        removerProcedimentos(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerProcedimentos(AjaxRequestTarget target, EsusFichaAtendIndividualItemProcedimento procedimento) {
        limpar(target);
        for (int i = 0; i < itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList().size(); i++) {
            if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList().get(i) == procedimento) {
                if (procedimento.getCodigo() != null) {
                    fichaAtendimentoIndividualDTO.getProcedimentoDeletarList().add(procedimento);
                }
                itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList().remove(i);
                break;
            }
        }
        tblProcedimentos.update(target);
    }

    private ICollectionProvider getCollectionProviderProcedimentos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList();
            }
        };
    }

    private Form<EsusFichaAtendIndividualItem> getForm() {
        if (form == null) {
            if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem() != null && itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem().getCodigo() != null) {
                form = new Form("form", new CompoundPropertyModel(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem()));
            } else {
                form = new Form("form", new CompoundPropertyModel(new EsusFichaAtendIndividualItem()));
            }
        }
        return form;
    }

    private void addCheckBoxProblemaCondicaoAvaliada() {
        getForm().add(checkBoxProblemaCondicaoAvaliadaAsma = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaAsma", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.ASMA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaDengue = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaDengue", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DENGUE.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaDesnutricao = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaDesnutricao", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DESNUTRICAO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaDiabetes = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaDiabetes", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DIABETES.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaDpoc = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaDpoc", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DPOC.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaDst = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaDst", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.DST.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaHanseniase = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaHanseniase", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.HANSENIASE.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaHipertensaoArterial = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaHipertensaoArterial", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.HIPERTENSAO_ARTERIAL.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaObesidade = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaObesidade", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.OBESIDADE.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaPreNatal = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaPreNatal", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PRE_NATAL.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaPuericultura = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaPuericultura", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PUERICULTURA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaPuerperio42Dias = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaPuerperio42Dias", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.PUERPERIO_42_DIAS.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaCancerMama = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaCancerMama", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.CANCER_MAMA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaCancerColoUtero = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaCancerColoUtero", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.CANCER_COLO_UTERO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaRiscoCardiovascular = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaRiscoCardiovascular", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.RISCO_CARDIOVASCULAR.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaReabilitacao = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaReabilitacao", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.REABILITACAO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaSaudeMental = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaSaudeMental", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.SAUDE_MENTAL.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaSaudeSexualReprodutiva = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaSaudeSexualReprodutiva", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.SAUDE_SEXUAL_REPRODUTIVA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaTabagismo = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaTabagismo", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.TABAGISMO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaTuberculose = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaTuberculose", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.TUBERCULOSE.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaUsuarioAlcool = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaUsuarioAlcool", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.USUARIO_ALCOOL.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxProblemaCondicaoAvaliadaUsuarioOutrasDrogas = (CheckBoxLongValue) new CheckBoxLongValue("problemaCondicaoAvaliadaUsuarioOutrasDrogas", EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.USUARIO_OUTRAS_DROGAS.sum(), new Model<Long>()).setEnabled(enable));

        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaAsma);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaDengue);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaDesnutricao);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaDiabetes);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaDpoc);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaDst);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaHanseniase);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaHipertensaoArterial);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaObesidade);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaPreNatal);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaPuericultura);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaPuerperio42Dias);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaCancerMama);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaCancerColoUtero);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaRiscoCardiovascular);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaReabilitacao);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaSaudeMental);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaSaudeSexualReprodutiva);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaTabagismo);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaTuberculose);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaUsuarioAlcool);
        lstCheckBoxProblemaCondicaoAvaliada.add(checkBoxProblemaCondicaoAvaliadaUsuarioOutrasDrogas);
    }

    private void addCheckBoxNasf() {

        getForm().add(checkBoxNasfAvaliacaoDiagnostico = (CheckBoxLongValue) new CheckBoxLongValue("nasfAvaliacaoDiagnostico", EsusFichaAtendIndividualItem.Nasf.AVALIACAO_DIAGNOSTICO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxNasfProcedimentosClinicosTerapeuticos = (CheckBoxLongValue) new CheckBoxLongValue("nasfProcedimentosClinicosTerapeuticos", EsusFichaAtendIndividualItem.Nasf.PROCEDIMENTOS_CLINICOS_TERAPEUTICOS.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxNasfPrescricaoTerapeutica = (CheckBoxLongValue) new CheckBoxLongValue("nasfPrescricaoTerapeutica", EsusFichaAtendIndividualItem.Nasf.PRESCRICAO_TERAPEUTICA.sum(), new Model<Long>()).setEnabled(enable));

        lstCheckBoxNasf.add(checkBoxNasfAvaliacaoDiagnostico);
        lstCheckBoxNasf.add(checkBoxNasfProcedimentosClinicosTerapeuticos);
        lstCheckBoxNasf.add(checkBoxNasfPrescricaoTerapeutica);
    }

    private void addCheckBoxConduta() {

        getForm().add(checkBoxCondutaRetornoConsultaAgendada = (CheckBoxLongValue) new CheckBoxLongValue("condutaRetornoConsultaAgendada", EsusFichaAtendIndividualItem.CondutaEncaminhamento.RETORNO_CONSULTA_AGENDADA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaRetornoCuidadoContinuadoProgramado = (CheckBoxLongValue) new CheckBoxLongValue("condutaRetornoCuidadoContinuadoProgramado", EsusFichaAtendIndividualItem.CondutaEncaminhamento.RETORNO_CUIDADO_CONTINUADO_PROGRAMADO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaAgendamentoGrupos = (CheckBoxLongValue) new CheckBoxLongValue("condutaAgendamentoGrupos", EsusFichaAtendIndividualItem.CondutaEncaminhamento.AGEND_GRUPOS.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaAgendamentoEmulti = (CheckBoxLongValue) new CheckBoxLongValue("condutaAgendamentoEmulti", EsusFichaAtendIndividualItem.CondutaEncaminhamento.AGEND_EMULTI.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaAltaEpisodio = (CheckBoxLongValue) new CheckBoxLongValue("condutaAltaEpisodio", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ALTA_EPISODIO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoInternoDia = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoInternoDia", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_INTERNO_DIA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoServicoEspecializado = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoServicoEspecializado", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_SERVICO_ESPECIALIZADO.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoCaps = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoCaps", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_CAPS.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoInternacaoHospitalar = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoInternacaoHospitalar", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_INTERNACAO_HOSPITALAR.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoUrgencia = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoUrgencia", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_URGENCIA.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoServicoAtencaoDomiciliar = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoServicoAtencaoDomiciliar", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_SERVICO_ATENCAO_DOMICILIAR.sum(), new Model<Long>()).setEnabled(enable));
        getForm().add(checkBoxCondutaEncaminhamentoIntersetorial = (CheckBoxLongValue) new CheckBoxLongValue("condutaEncaminhamentoIntersetorial", EsusFichaAtendIndividualItem.CondutaEncaminhamento.ENC_INTERSETORIAL.sum(), new Model<Long>()).setEnabled(enable));

        lstCheckBoxConduta.add(checkBoxCondutaRetornoConsultaAgendada);
        lstCheckBoxConduta.add(checkBoxCondutaRetornoCuidadoContinuadoProgramado);
        lstCheckBoxConduta.add(checkBoxCondutaAgendamentoGrupos);
        lstCheckBoxConduta.add(checkBoxCondutaAgendamentoEmulti);
        lstCheckBoxConduta.add(checkBoxCondutaAltaEpisodio);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoInternoDia);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoServicoEspecializado);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoCaps);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoInternacaoHospitalar);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoUrgencia);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoServicoAtencaoDomiciliar);
        lstCheckBoxConduta.add(checkBoxCondutaEncaminhamentoIntersetorial);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroFichaAtendimentoIndividual");
    }

    private void salvar() throws ValidacaoException, DAOException {
        EsusFichaAtendIndividualItem item = getForm().getModel().getObject();
        if (cns != null) {
            if (!CnsValidator.validaCns(StringUtil.getDigits(cns))) {
                throw new ValidacaoException(bundle("cnsInvalido"));
            }
            item.setNumeroCartao(Long.valueOf(StringUtil.getDigits(cns)));
        } else {
            item.setNumeroCartao(null);
        }
        item.setProblemaCondicaoAvaliada(CheckBoxUtil.getSomatorio(lstCheckBoxProblemaCondicaoAvaliada));
        item.setNasfs(CheckBoxUtil.getSomatorio(lstCheckBoxNasf));
        item.setCondutas(CheckBoxUtil.getSomatorio(lstCheckBoxConduta));
        if (UsuarioCadsus.SEXO_MASCULINO.equals(sexo)) {
            item.setSexo(EsusFichaAtendIndividualItem.SEXO_MASCULINO);
        } else {
            item.setSexo(EsusFichaAtendIndividualItem.SEXO_FEMININO);
        }

        if (item.getCodigo() == null) {
            itemFichaAtendimentoIndividualDTO.setEsusFichaAtendIndividualItem(item);
            fichaAtendimentoIndividualDTO.getEsusFichaAtendimentoIndividualItemList().add(itemFichaAtendimentoIndividualDTO);
        } else {
            for (CadastroFichaAtendimentoIndividualItemDTO itemDTO : fichaAtendimentoIndividualDTO.getEsusFichaAtendimentoIndividualItemList()) {
                if (itemDTO.getEsusFichaAtendIndividualItem().getCodigo().equals(item.getCodigo())) {
                    itemDTO.setEsusFichaAtendIndividualItem(item);
                    itemDTO.setEsusFichaAtendIndividualItemProcedimentoList(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList());
                    break;
                }
            }
        }

        validarCadastro(item);

        fichaAtendimentoIndividualDTO.setIdItemASerModificado(item.getCodigo());
        EsusFichaAtendIndividual efai = BOFactoryWicket.getBO(EsusFacade.class).salvarFichaAtendIndividual(fichaAtendimentoIndividualDTO);
        setResponsePage(new CadastroFichaAtendimentoIndividualStep1Page(efai));

    }

    private void validarCadastro(EsusFichaAtendIndividualItem item) throws ValidacaoException {
        Date dataAtendimento = Data.adjustRangeHour(fichaAtendimentoIndividualDTO.getEsusFichaAtendIndividual().getDataAtendimento()).getDataInicial();
        Date dataNascimento = Data.adjustRangeHour(item.getDataNascimento()).getDataInicial();
        if (dataNascimento.after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(BundleManager.getString("msgDataNascimentoMaiorAtual"));
        }

        if (dataNascimento.after(dataAtendimento)) {
            throw new ValidacaoException(bundle("msgDataNascimentoNaoPodeSerMaiorQueDataAtendimentoX", Data.formatar(dataAtendimento)));
        }

        if (dataNascimento.before(Data.removeAnos(dataAtendimento, 130))) {
            throw new ValidacaoException(bundle("msgIdadeSuperior130Anos"));
        }

        if (item.getDumGestante() != null) {
            if (Data.adjustRangeHour(item.getDumGestante()).getDataInicial().after(dataAtendimento)) {
                throw new ValidacaoException(bundle("msgDataUltimaMenstruacaoDumNaoPodeSerMaiorQueDataAtendimentoX", Data.formatar(dataAtendimento)));
            } else if (Data.adjustRangeHour(item.getDumGestante()).getDataInicial().before(dataNascimento)) {
                throw new ValidacaoException(bundle("msgDataUltimaMenstruacaoDumNaoPodeSerMenorQueDataNascimentoX", Data.formatar(dataNascimento)));
            }
        }

//        if (Coalesce.asLong(item.getProblemaCondicaoAvaliada()) == 0L && item.getOutroCiap1() == null && item.getOutroCiap2() == null && item.getCid() == null) {
//            throw new ValidacaoException(bundle("msgSelecionePeloMenosUmItemOuInformeUmCampoGrupoProblemaCondicaoAvaliada"));
//        }

        if (item.getPeso() != null && Coalesce.asDouble(item.getPeso()) < 0.5d) {
            throw new ValidacaoException(bundle("msgPesoInformadoDeveSerMaiorIgualX", "0,5"));
        }

        if (item.getAltura() != null && Coalesce.asDouble(item.getAltura()) < 20d) {
            throw new ValidacaoException(bundle("msgAlturaInformadaDeveSerMaiorIgualX", "20"));
        }

        if (Coalesce.asLong(item.getCondutas()) == 0L) {
            throw new ValidacaoException(bundle("msgSelecionePeloMenosUmItemConduta"));
        }
    }

    private void enableProcedimentoEsus(AjaxRequestTarget target) {
        autoCompleteConsultaExameEsus.limpar(target);
        modelProcedimentos.getObject().setExameEsus(null);
        autoCompleteConsultaExameEsus.setEnabled(true);
        target.add(autoCompleteConsultaExameEsus);
    }

    private void enableProcedimento(AjaxRequestTarget target) {
        autoCompleteConsultaProcedimento.limpar(target);
        modelProcedimentos.getObject().setProcedimento(null);
        autoCompleteConsultaProcedimento.setEnabled(true);
        target.add(autoCompleteConsultaProcedimento);
    }

    private void adicionarItem(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        EsusFichaAtendIndividualItemProcedimento itemProcedimento = modelProcedimentos.getObject();
        if (itemProcedimento.getProcedimento() == null && itemProcedimento.getExameEsus() == null) {
            throw new ValidacaoException(bundle("msgInformeExameOuProcedimento"));
        }

        if (itemProcedimento.getSolicitadoAvaliado() == null) {
            throw new ValidacaoException(bundle("msgInformeSolicitadoAvaliado"));
        }
        if (itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList().size() >= 100) {
            throw new ValidacaoException(bundle("msgLimiteExameOuProcedimentoAtingido"));
        }

//        if (Coalesce.asLong(itemProcedimento.getQuantidade()) == 0L) {
//            throw new ValidacaoException(bundle("informeQuantidade"));
//        }

        for (EsusFichaAtendIndividualItemProcedimento item : itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList()) {
            if ((item.getProcedimento() != null && itemProcedimento.getProcedimento() != null && item.getProcedimento().getCodigo().equals(itemProcedimento.getProcedimento().getCodigo()))
                    || (item.getExameEsus() != null && itemProcedimento.getExameEsus() != null && item.getExameEsus().getCodigo().equals(itemProcedimento.getExameEsus().getCodigo()))) {
                throw new ValidacaoException(bundle("procedimentoJaAdicionado"));
            }
        }

        itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList().add(itemProcedimento);

        limpar(target);
        tblProcedimentos.update(target);
        target.focusComponent(autoCompleteConsultaExameEsus.getTxtDescricao().getTextField());
    }

    private void limpar(AjaxRequestTarget target) {
        modelProcedimentos.setObject(new EsusFichaAtendIndividualItemProcedimento());
        enableProcedimentoEsus(target);
        enableProcedimento(target);
//        txtQuantidade.limpar(target);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
        if (itemFichaAtendimentoIndividualDTO != null && CollectionUtils.isNotNullEmpty(itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItemProcedimentoList())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerProcedimentos)));
        }
    }

    private void carregarCheckbox(EsusFichaAtendIndividualItem item) {
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxProblemaCondicaoAvaliada, item.getProblemaCondicaoAvaliada());
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxNasf, item.getNasfs());
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxConduta, item.getCondutas());
    }

    public void validarCiapSexoPaciente(AjaxRequestTarget target, Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, itemFichaAtendimentoIndividualDTO.getEsusFichaAtendIndividualItem());
        if (ciapInvalido) {
            String msg = Bundle.getStringApplication("ciap_nao_valido_sexo_paciente");
            try {
                throw new ValidacaoException(msg);
            } catch (ValidacaoException e) {
                modalWarn(target, e);
            }
        }
    }

}