package br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog;

import br.com.celk.component.window.Window;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgVincularProduto extends Window {

    private DispensacaoMedicamentoItemDTO object;
    private PnlVincularProduto pnlVincularProduto;

    public DlgVincularProduto(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(460);
        setInitialHeight(80);

        setResizable(false);

        setTitle(bundle("vincularProduto"));

        setContent(pnlVincularProduto = new PnlVincularProduto(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Produto produto) throws ValidacaoException, DAOException {
                DlgVincularProduto.this.onConfirmar(target, produto, DlgVincularProduto.this.object);
                close(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Produto produto, DispensacaoMedicamentoItemDTO object) throws ValidacaoException, DAOException;

    public void setObject(DispensacaoMedicamentoItemDTO object) {
        this.object = object;
        pnlVincularProduto.setObject(object);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlVincularProduto.getFocusComonent();
    }
}
