package br.com.celk.view.consorcio.pagamentoguiaprocedimento.columnpanel;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.consorcio.pagamentoguiaprocedimento.BaixaImpostoRendaPage;
import br.com.celk.view.consorcio.pagamentoguiaprocedimento.DetalhesPagamentoGuiaProcedimentoPage;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PagamentoGuiaProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class PagamentoColumnPanel extends Panel implements PermissionContainer{

    @Permission(type= Permissions.ESTORNAR, action= ActionsEnum.RENDER)
    private AbstractAjaxLink btnEstornarPagamento;
    
    private AbstractAjaxLink btnConfirmarPagamentoImposto;
    private AbstractAjaxLink btnDetalhes;
    private DlgConfirmacao dlgConfirmacao;
    
    private PagamentoGuiaProcedimento pagamentoGuiaProcedimento;
    
    public PagamentoColumnPanel(String id, PagamentoGuiaProcedimento pagamentoGuiaProcedimento) {
        super(id);
        this.pagamentoGuiaProcedimento = pagamentoGuiaProcedimento;
        init();
    }

    private void init() {
        add(btnEstornarPagamento = new AbstractAjaxLink("btnEstornarPagamento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao!=null) {
                    dlgConfirmacao.show(target);
                }
            }
            @Override
            public boolean isVisible() {
                return PagamentoGuiaProcedimento.StatusPagamentoGuiaProcedimento.CONFIRMADO.value().equals(pagamentoGuiaProcedimento.getStatus());
            }
            
        });
        add(btnConfirmarPagamentoImposto = new AbstractAjaxLink("btnConfirmarPagamentoImposto") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new BaixaImpostoRendaPage(pagamentoGuiaProcedimento));
            }

            @Override
            public boolean isVisible() {
                return PagamentoGuiaProcedimento.StatusPagamentoGuiaProcedimento.CONFIRMADO.value().equals(pagamentoGuiaProcedimento.getStatus()) && PagamentoGuiaProcedimento.SituacaoImpostoPagamentoGuiaProcedimento.IMPOSTO_A_PAGAR.value().equals(pagamentoGuiaProcedimento.getSituacaoPagamento());
            }
            
        });
        add(btnDetalhes = new AbstractAjaxLink("btnDetalhes") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new DetalhesPagamentoGuiaProcedimentoPage(pagamentoGuiaProcedimento));
            }

        });
    }

    private void initDlgConfirmacao(AjaxRequestTarget target){
        if (dlgConfirmacao==null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteEstornarPagamento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(ConsorcioFacade.class).estornarPagamento(pagamentoGuiaProcedimento);
                    MessageUtil.info(target, this, BundleManager.getString("pagamentoXestornadoSucesso", pagamentoGuiaProcedimento.getChave()));
                    updateTable(target);
                }
            });
        }
    }

    public abstract void updateTable(AjaxRequestTarget target);
    
    public AbstractAjaxLink getBtnEstornarPagamento() {
        return btnEstornarPagamento;
    }
}
