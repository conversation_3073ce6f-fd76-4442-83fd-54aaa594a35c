package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.foto.FotoComponente;
import br.com.celk.view.hospital.financeiro.autocomplete.AutoCompleteConsultaFormaPagamento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaDominioProfissionalDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.LoadInterceptorEmpresaNaturezaTipo;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.recepcao.interfaces.dto.ConfirmacaoAtendimentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingImpl;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.DominioProfissional;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.hospital.financeiro.FormaPagamento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.component.window.WindowUtil.addModal;
import static br.com.celk.component.window.WindowUtil.newModalId;
import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public abstract class PnlConfirmarAtendimento extends Panel {

    private String paciente;
    private ConsultaUsuarioCadsusDTO dto;
    private Empresa unidadeOrigem;
    private TipoAtendimento tipoAtendimento;
    private TipoAtendimento subTipoAtendimento;
    private TipoProcedimentoAtendimento tipoProcedimentoAtendimento;
    private Profissional profissional;
    private DominioProfissional dominioProfissional;
    private DropDown cbxProfissional;
    private NaturezaProcura naturezaProcura;
    private WebMarkupContainer containerConvenio;
    private DropDown dropDownConvenio;
    private DropDown dropDownSubConvenio;
    private Double valorAdiantamento;
    private FormaPagamento formaPagamento;
    private InputField txtValorAdiantamento;
    private AutoCompleteConsultaFormaPagamento autoCompleteConsultaFormaPagamento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Convenio convenioSUS;
    private Convenio convenioParticular;
    private Convenio convenio;
    private Convenio subconvenio;
    private String selecionarConvenio;
    private Empresa estabelecimentoExecutante;
    private DropDown dropDownTipoAtendimento;
    private DropDown dropDownSubTipoAtendimento;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEstabelecimentoExecutante;
    private DropDown dropDownPrioridade;

    private Image imagem;
    private Long prioridade;
    private String condicaoPaciente;
    private String motivoConsulta;

    private WebMarkupContainer messageContainer;
    private MultiLineLabel messageLabel;

    private WebMarkupContainer containerFinanceiro;
    private FotoComponente fotoComponente;

    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;

    public PnlConfirmarAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        try {
            Form form = new Form("form");
            naturezaProcura = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("NatProcuraMarcacaoUnidade");
            convenioParticular = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioParticular");
            convenioSUS = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
            selecionarConvenio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("SelecionarConvenio");

            form.add(messageContainer = new WebMarkupContainer("messageContainer"));
            messageContainer.add(messageLabel = new MultiLineLabel("messageLabel"));
            messageContainer.setOutputMarkupId(true);

            form.add(new InputField("paciente", new PropertyModel(this, "paciente"))
                    .setEnabled(false));

            form.add(autoCompleteConsultaEstabelecimentoExecutante = new AutoCompleteConsultaEmpresa("estabelecimentoExecutante", new PropertyModel(this, "estabelecimentoExecutante")));
            autoCompleteConsultaEstabelecimentoExecutante.setValidaUsuarioEmpresa(true);

            autoCompleteConsultaEstabelecimentoExecutante.add(new ConsultaListener<Empresa>() {
                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                    carregarDropDownTipoAtendimento(target, object);
                }
            });

            autoCompleteConsultaEstabelecimentoExecutante.add(new RemoveListener<Empresa>() {

                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                    dropDownTipoAtendimento.removeAllChoices(target);
                    dropDownTipoAtendimento.limpar(target);
                    dropDownTipoAtendimento.setEnabled(false);
                    target.add(dropDownTipoAtendimento);
                    cbxProfissional.removeAllChoices(target);

                    dropDownSubTipoAtendimento.removeAllChoices(target);
                    dropDownSubTipoAtendimento.limpar(target);
                    dropDownSubTipoAtendimento.setEnabled(false);
                    target.add(dropDownSubTipoAtendimento);
                }
            });

            form.add(containerConvenio = new WebMarkupContainer("containerConvenio"));
            containerConvenio.setOutputMarkupId(true);
            dropDownConvenio = getDropDownConvenio();
            containerConvenio.add(dropDownConvenio);

            dropDownSubConvenio = getDropDownSubConvenio();
            dropDownSubConvenio.setEnabled(false);
            containerConvenio.add(dropDownSubConvenio);

            DropDown cbxTipoAtendimento = getDropDownTipoAtendimento();
            form.add(cbxTipoAtendimento);

            DropDown cbxSubTipoAtendimento = getDropDownSubTipoAtendimento();
            form.add(cbxSubTipoAtendimento);

            form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("unidadeOrigem", new PropertyModel(this, "unidadeOrigem")));

            form.add(getDropDownPrioridade());
            form.add(new InputField("condicaoPaciente", new PropertyModel(this, "condicaoPaciente")));
            form.add(new InputField("motivoConsulta", new PropertyModel(this, "motivoConsulta")));

            cbxProfissional = getDropDownProfissional();
            form.add(cbxProfissional);

            if (subTipoAtendimento == null) {
                cbxProfissional.setEnabled(false);
            }

            DropDown cbxTipoProcedimentoAtendimento = getDropDownTipoProcedimentoAtendimento();
            form.add(cbxTipoProcedimentoAtendimento);

            form.add(containerFinanceiro = new WebMarkupContainer("containerFinanceiro"));
            containerFinanceiro.setOutputMarkupId(true);

            containerFinanceiro.add(txtValorAdiantamento = (InputField) new DoubleField("valorAdiantamento", new PropertyModel(this, "valorAdiantamento")).setLabel(new Model<String>(bundle("valor"))));
            containerFinanceiro.add(autoCompleteConsultaFormaPagamento = (AutoCompleteConsultaFormaPagamento) new AutoCompleteConsultaFormaPagamento("formaPagamento", new PropertyModel(this, "formaPagamento"))
                    .setLabel(new Model<String>(bundle("formaPagamento"))));
            autoCompleteConsultaFormaPagamento.setExibirFormaPagamentoAdiantamento(false);
            autoCompleteConsultaFormaPagamento.setExibirFormaPagamentoDesconto(false);

            form.add(new AbstractAjaxButton("btnOk") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    if (validarSalvar(target)) {
                        carregarProfissional();
                        ConfirmacaoAtendimentoDTO dto = new ConfirmacaoAtendimentoDTO();
                        if (subTipoAtendimento != null) {
                            dto.setTipoAtendimento(subTipoAtendimento);
                        } else {
                            dto.setTipoAtendimento(tipoAtendimento);
                        }
                        dto.setProfissional(getProfissional());
                        dto.setConsultaUsuarioCadsusDTO(getDto());
                        dto.setTipoProcedimentoAtendimento(getTipoProcedimentoAtendimento());
                        if (RepositoryComponentDefault.SIM.equals(selecionarConvenio)) {
                            dto.setConvenio(convenio);
                            dto.setSubConvenio(subconvenio);
                        } else {
                            dto.setConvenio(convenioSUS);
                            if (convenioSUS.getSubconvenio() != null) {
                                dto.setSubConvenio(getSubConvenio());
                            }
                        }
                        dto.setSenha(getDto().getSenha());
                        dto.setTipoSenha(getDto().getTipoSenha());
                        dto.setValorAdiantamento(getValorAdiantamento());
                        dto.setFormaPagamento(getFormaPagamento());
                        dto.setPrioridade(getPrioridade());
                        dto.setCondicaoPaciente(getCondicaoPaciente());
                        dto.setMotivoConsulta(getMotivoConsulta());
                        dto.setEstabelecimentoExecutante(getEstabelecimentoExecutante());
                        dto.setUnidadeOrigem(unidadeOrigem);
                        onOk(target, dto);

                        dropDownPrioridade.limpar(target);
                        target.add(dropDownPrioridade);
                    }
                }
            });

            form.add(new AbstractAjaxButton("btnFechar") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    dropDownPrioridade.limpar(target);
                    target.add(dropDownPrioridade);
                    onFechar(target);
                }
            }.setDefaultFormProcessing(false));

            cbxSubTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    cbxProfissional.removeAllChoices();
                    dominioProfissional = null;
                    profissional = null;
                    if (subTipoAtendimento == null) {
                        cbxProfissional.setEnabled(false);
                    } else {
                        cbxProfissional.setEnabled(true);
                        controleTipoAtendimento(subTipoAtendimento);
                    }
                    target.add(cbxProfissional);
                }
            });

            form.add(fotoComponente = new FotoComponente());
            add(form);
        } catch (ValidacaoRuntimeException | DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private Convenio getSubConvenio() {
        return LoadManager.getInstance(Convenio.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, convenioSUS))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                .setMaxResults(1).start().getVO();
    }

    private boolean validarSalvar(AjaxRequestTarget target) {
        try {
            if (tipoAtendimento == null) {
                throw new ValidacaoException(bundle("magValidarTipoAtendimento"));
            }

            NaturezaProcuraTipoAtendimento npta = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                    .addProperties(new HQLProperties(TipoProcedimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO).getProperties())
                    .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TIPO_PROCEDIMENTO_TRANSFERENCIA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, tipoAtendimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, naturezaProcura))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TIPO_ATENDIMENTO_AGRUPADOR), QueryCustom.QueryCustomParameter.IS_NULL))
                    .start().getVO();
            boolean empresaOrigemObrigatoria = npta != null && npta.getTipoProcedimento() != null && RepositoryComponentDefault.SIM_LONG.equals(npta.getTipoProcedimento().getFlagSolicitarRetornoAutomatico());
            if (autoCompleteConsultaEstabelecimentoExecutante.getComponentValue() == null) {
                throw new ValidacaoException(bundle("informeEstabelecimentoExecutante"));
            } else if (valorAdiantamento != null && formaPagamento == null) {
                throw new ValidacaoException(bundle("msgInformeFormaPagamento"));
            } else if (valorAdiantamento == null && formaPagamento != null) {
                throw new ValidacaoException(bundle("msgInformeValor"));
            } else if (subTipoAtendimento == null && tipoAtendimento == null) {
                throw new ValidacaoException(bundle("magValidarTipoAtendimento"));
            } else if (empresaOrigemObrigatoria && unidadeOrigem == null) {
                throw new ValidacaoException(bundle("msgInformeUnidadeOrigemPaciente"));
            }

            if (RepositoryComponentDefault.SIM.equals(selecionarConvenio) && convenio == null) {
                throw new ValidacaoException(bundle("selecioneConvenio"));
            }

            TipoAtendimento tipoAtendimentoConsulta = BOFactory.getBO(AtendimentoFacade.class).consultarTipoAtendimentoEncaminhamentoPadrao(naturezaProcura.getCodigo(), tipoAtendimento.getCodigo());
            if (tipoAtendimentoConsulta == null) {
                tipoAtendimentoConsulta = LoadManager.getInstance(TipoAtendimento.class)
                        .addProperties(new HQLProperties(TipoAtendimento.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimento.PROP_CODIGO, tipoAtendimento.getCodigo()))
                        .start().getVO();
            }

            if (tipoAtendimentoConsulta != null && RepositoryComponentDefault.SIM.equals(tipoAtendimentoConsulta.getFlagExigeProfissional())
                    && (dominioProfissional == null || dominioProfissional.getProfissional() == null)) {
                throw new ValidacaoException(Bundle.getStringApplication("tipo_atendimento_exige_profissional"));
            }

            Atendimento agendamento = atendimentoEmAberto();
            if (agendamento != null) {
                viewDlgConfirmacao(target, agendamento);
                return false;
            }


        } catch (ValidacaoException | DAOException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }
        return true;
    }

    private void viewDlgConfirmacao(AjaxRequestTarget target, Atendimento atendimento) {
        if (dlgConfirmacaoSimNao == null) {
            addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                }

                @Override
                public String getDialogTitle() {
                    return bundle("aviso");
                }

                @Override
                public String getFecharLabel() {
                    return bundle("fechar");
                }

                @Override
                public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                    btnConfirmar.setVisible(false);
                }

            });
        }
        dlgConfirmacaoSimNao.setMessage(target, bundle("msg_existe_atendimento_em_aberto_para_este_paciente_tipo_atendimento_data_entrada", atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao(), atendimento.getDataHoraChegada()));
        dlgConfirmacaoSimNao.show(target);
    }


    private Atendimento atendimentoEmAberto() throws DAOException {

        Object validarAtendimento = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ValidarAtendimentoAbertoMarcacao");
        if (validarAtendimento.equals(RepositoryComponentDefault.SIM)) {

            Atendimento atendimentoAberto = LoadManager.getInstance(Atendimento.class)
                    .addProperty(Atendimento.PROP_DATA_CHEGADA)
                    .addProperty(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_USUARIO_CADSUS, dto.getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Atendimento.PROP_STATUS, BuilderQueryCustom.QueryParameter.MENOR, Atendimento.STATUS_FINALIZADO))
                    .setMaxResults(1)
                    .start().getVO();

            if (atendimentoAberto != null) {
                return atendimentoAberto;
            }
            return null;
        }
        return null;
    }

    private DropDown getDropDownProfissional() {
        DropDown dropDown = new DropDown("profissional", new PropertyModel(this, "dominioProfissional"));
        return dropDown;
    }

    private DropDown getDropDownTipoAtendimento() {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown("tipoAtendimento", new PropertyModel(this, "tipoAtendimento"));
        }
        return dropDownTipoAtendimento;
    }

    private DropDown getDropDownSubTipoAtendimento() {
        if (dropDownSubTipoAtendimento == null) {
            dropDownSubTipoAtendimento = new DropDown("subTipoAtendimento", new PropertyModel(this, "subTipoAtendimento"));
        }
        return dropDownSubTipoAtendimento;
    }

    private void carregarDropDownTipoAtendimento(AjaxRequestTarget target, Empresa empresa) {
        if (target != null) {
            dropDownTipoAtendimento.removeAllChoices(target);
            dropDownTipoAtendimento.limpar(target);
            cbxProfissional.removeAllChoices(target);
        } else {
            dropDownTipoAtendimento.removeAllChoices();
        }
        dropDownTipoAtendimento.setEnabled(true);

        dropDownTipoAtendimento.addChoice(null, "");

        List<NaturezaProcuraTipoAtendimento> nptaList = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addProperties(new HQLProperties(TipoAtendimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TIPO_PROCEDIMENTO_TRANSFERENCIA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, naturezaProcura))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TIPO_ATENDIMENTO_AGRUPADOR), QueryCustom.QueryCustomParameter.IS_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_SITUACAO), TipoAtendimento.Situacao.ATIVO.value()))
                .addInterceptor(new LoadInterceptorEmpresaNaturezaTipo(empresa))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO)))
                .start().getList();
        for (NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento : nptaList) {
            dropDownTipoAtendimento.addChoice(naturezaProcuraTipoAtendimento.getTipoAtendimento(), naturezaProcuraTipoAtendimento.getTipoAtendimento().getDescricao());
        }

        if (target != null) {
            target.add(dropDownTipoAtendimento);
        }

        dropDownTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dropDownSubTipoAtendimento.removeAllChoices(target);
                dropDownSubTipoAtendimento.limpar(target);
                dropDownSubTipoAtendimento.setEnabled(true);
                dropDownSubTipoAtendimento.addChoice(null, "");

                if (tipoAtendimento != null) {
                    List<TipoAtendimento> lstTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimento.PROP_TIPO_ATENDIMENTO_AGRUPADOR, tipoAtendimento))
                            .addSorter(new QueryCustom.QueryCustomSorter(TipoAtendimento.PROP_DESCRICAO))
                            .start().getList();

                    for (TipoAtendimento tp : lstTipoAtendimento) {
                        dropDownSubTipoAtendimento.addChoice(tp, tp.getDescricao());
                    }

                    target.add(dropDownSubTipoAtendimento);
                } else {
                    dropDownSubTipoAtendimento.removeAllChoices(target);
                    dropDownSubTipoAtendimento.limpar(target);
                    dropDownSubTipoAtendimento.setEnabled(false);
                }

                cbxProfissional.removeAllChoices();
                dominioProfissional = null;
                profissional = null;
                if (tipoAtendimento != null) {
                    if (tipoAtendimento.getTipoAtendimentoAgrupador() != null) {
                        cbxProfissional.setEnabled(false);
                    } else {
                        cbxProfissional.setEnabled(true);
                        controleTipoAtendimento(tipoAtendimento);
                    }
                    if (RepositoryComponentDefault.SIM_LONG.equals(tipoAtendimento.getFlagExigeUnidadeOrigem())) {
                        autoCompleteConsultaEmpresa.setRequired(true);
                        autoCompleteConsultaEmpresa.getTxtDescricao().addRequiredClass();
                    } else {
                        autoCompleteConsultaEmpresa.setRequired(false);
                        autoCompleteConsultaEmpresa.getTxtDescricao().removeRequiredClass();
                    }
                    target.add(cbxProfissional);
                    target.add(autoCompleteConsultaEmpresa);
                } else {
                    cbxProfissional.setEnabled(false);
                    target.add(cbxProfissional);
                }
            }
        });
    }

    private void carregarConvenio() {
        dropDownConvenio.addChoice(null, "");

        List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                .addProperties(new HQLProperties(Convenio.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.NAO_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                .start().getList();
        for (Convenio c : convList) {
            dropDownConvenio.addChoice(c, c.getDescricao());
            if (convList.size() == 1) {
                convenio = c;
            }
        }
    }

    private void carregarSubConvenio(AjaxRequestTarget target) {
        if (dropDownConvenio.getComponentValue() != null) {
            this.subconvenio = null;

            List<Convenio> convList = LoadManager.getInstance(Convenio.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_SUBCONVENIO, RepositoryComponentDefault.SIM_LONG))
                    .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CONVENIO_PAI, dropDownConvenio.getComponentValue()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Convenio.PROP_DESCRICAO)))
                    .start().getList();
            if (convList.isEmpty()) {
                dropDownSubConvenio.setEnabled(false);
                dropDownSubConvenio.removeAllChoices();
            } else {
                dropDownSubConvenio.setEnabled(true);
                dropDownSubConvenio.removeAllChoices();
                for (Convenio item : convList) {
                    dropDownSubConvenio.addChoice(item, item.getDescricao());
                    if (convList.size() == 1) {
                        subconvenio = item;
                    }
                }
            }
        } else {
            dropDownSubConvenio.setEnabled(false);
        }
        target.add(dropDownSubConvenio);
    }

    private DropDown getDropDownConvenio() {
        this.convenio = null;

        DropDown dropDown = new DropDown("convenio", new PropertyModel(this, "convenio"));
        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarSubConvenio(target);
            }
        });

        return dropDown;
    }

    private DropDown getDropDownSubConvenio() {
        DropDown dropDown = new DropDown("subconvenio", new PropertyModel(this, "subconvenio"));
        this.subconvenio = null;

        dropDown.addChoice(null, "");

        return dropDown;
    }

    private DropDown getDropDownTipoProcedimentoAtendimento() {
        DropDown dropDown = new DropDown("tipoProcedimentoAtendimento", new PropertyModel(this, "tipoProcedimentoAtendimento"));

        dropDown.addChoice(null, "");

        List<TipoProcedimentoAtendimento> tipoProcedimentoAtendimentoList = LoadManager.getInstance(TipoProcedimentoAtendimento.class)
                .addProperties(new HQLProperties(TipoProcedimentoAtendimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimentoAtendimento.PROP_FLAG_RESERVA, RepositoryComponentDefault.NAO_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimentoAtendimento.PROP_FLAG_EXIBIR_PAGINA, BuilderQueryCustom.QueryParameter.IN, Arrays.asList(TipoProcedimentoAtendimento.ExibirPagina.AMBOS.value(), TipoProcedimentoAtendimento.ExibirPagina.RECEPCAO.value())))
                .addSorter(new QueryCustom.QueryCustomSorter(TipoProcedimentoAtendimento.PROP_DESCRICAO))
                .start().getList();
        for (TipoProcedimentoAtendimento item : tipoProcedimentoAtendimentoList) {
            dropDown.addChoice(item, item.getDescricao());
        }
        return dropDown;
    }

    private DropDown getDropDownPrioridade() {
        dropDownPrioridade = new DropDown("prioridade", new PropertyModel(this, "prioridade"));

        dropDownPrioridade.addChoice(Atendimento.PRIORIDADE_NORMAL, bundle("normal"));
        dropDownPrioridade.addChoice(Atendimento.PRIORIDADE_URGENTE, bundle("urgente"));

        return dropDownPrioridade;
    }

    private void controleTipoAtendimento(TipoAtendimento ta) {
        try {
            TipoAtendimento tipoAtendimentoConsulta = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarTipoAtendimentoEncaminhamentoPadrao(naturezaProcura.getCodigo(), ta.getCodigo());
            if (tipoAtendimentoConsulta == null) {
                tipoAtendimentoConsulta = ta;
            }

            QueryConsultaDominioProfissionalDTOParam param = new QueryConsultaDominioProfissionalDTOParam();
            param.setPeriodoUnidade(true);
            param.setTipoAtendimento(tipoAtendimentoConsulta);
            if (tipoAtendimentoConsulta.getEmpresa() != null) {
                param.setCodigoEmpresa(tipoAtendimentoConsulta.getEmpresa().getCodigo());
            } else if (estabelecimentoExecutante != null) {
                param.setCodigoEmpresa(estabelecimentoExecutante.getCodigo());
            } else {
                param.setCodigoEmpresa(null);
            }

            DataPaging<QueryConsultaDominioProfissionalDTOParam> dataPaging = new DataPagingImpl<QueryConsultaDominioProfissionalDTOParam>(DataPaging.Type.ALVO_LIST);
            dataPaging.setFirstResult(0);
            dataPaging.setMaxResult(999);
            dataPaging.setParam(param);

            DataPagingResult<DominioProfissional> result = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaDominioProfissional(dataPaging);
            cbxProfissional.addChoice(null, "");
            for (DominioProfissional dominioProfissional : result.getList()) {
                cbxProfissional.addChoice(dominioProfissional, dominioProfissional.getNome());
            }
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void carregarProfissional() {
        profissional = null;
        if (dominioProfissional != null) {
            profissional = LoadManager.getInstance(Profissional.class)
                    .setId(dominioProfissional.getProfissional().getCodigo())
                    .start().getVO();
        }
    }

    public abstract void onOk(AjaxRequestTarget target, ConfirmacaoAtendimentoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        paciente = null;
        dto = null;
        setTipoAtendimento(null);
        setEstabelecimentoExecutante(null);
        setTipoProcedimentoAtendimento(null);
        autoCompleteConsultaEmpresa.limpar(target);
        motivoConsulta = null;
    }

    public String getPaciente() {
        return paciente;
    }

    public ConsultaUsuarioCadsusDTO getDto() {
        return dto;
    }

    public void setDto(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        this.dto = dto;
        if (estabelecimentoExecutante == null) {
            estabelecimentoExecutante = ApplicationSession.get().getSession().getEmpresa();
        }
        if (autoCompleteConsultaEstabelecimentoExecutante.getComponentValue() != null) {
            carregarDropDownTipoAtendimento(target, (Empresa) autoCompleteConsultaEstabelecimentoExecutante.getComponentValue());
        } else {
            dropDownTipoAtendimento.setEnabled(false);
            target.add(dropDownTipoAtendimento);
        }

        if (RepositoryComponentDefault.SIM.equals(selecionarConvenio)) {
            dropDownConvenio.limpar(target);
            carregarConvenio();
            if (convenio != null) {
                dropDownConvenio.setEnabled(false);
                target.add(dropDownConvenio);
                carregarSubConvenio(target);

                if (subconvenio != null) {
                    dropDownSubConvenio.setComponentValue(subconvenio);
                    dropDownSubConvenio.setEnabled(false);
                }
            } else {
                dropDownConvenio.setEnabled(true);
                dropDownConvenio.limpar(target);
                dropDownSubConvenio.setEnabled(false);
                dropDownSubConvenio.removeAllChoices(target);
                dropDownSubConvenio.limpar(target);
            }
        } else {
            containerConvenio.setVisible(false);
        }

        carregaUnidadeOrigem();
        messageContainer.setVisible(false);
        validarUnidadeOrigemPaciente(target);
        target.focusComponent(autoCompleteConsultaEstabelecimentoExecutante.getTxtDescricao().getTextField());
        fotoComponente.setUsuarioCadsus(dto.getUsuarioCadsus());
    }

    private void validarUnidadeOrigemPaciente(AjaxRequestTarget target) {
        if (this.unidadeOrigem != null && this.estabelecimentoExecutante != null) {
            if (!this.estabelecimentoExecutante.equals(this.unidadeOrigem)) {
                messageLabel.setDefaultModel(new Model(bundle("msgPacienteInformadoPertenceAreaX", this.unidadeOrigem.getDescricao())));
                messageContainer.setVisible(true);
            }
        }
        target.add(messageContainer);
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoProcedimentoAtendimento getTipoProcedimentoAtendimento() {
        return tipoProcedimentoAtendimento;
    }

    public void setTipoProcedimentoAtendimento(TipoProcedimentoAtendimento tipoProcedimentoAtendimento) {
        this.tipoProcedimentoAtendimento = tipoProcedimentoAtendimento;
    }

    public Double getValorAdiantamento() {
        return valorAdiantamento;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public Long getPrioridade() {
        return prioridade;
    }

    public void setPrioridade(Long prioridade) {
        this.prioridade = prioridade;
    }

    public String getCondicaoPaciente() {
        return condicaoPaciente;
    }

    public void setCondicaoPaciente(String condicaoPaciente) {
        this.condicaoPaciente = condicaoPaciente;
    }

    public String getMotivoConsulta() {
        return motivoConsulta;
    }

    public void setMotivoConsulta(String motivoConsulta) {
        this.motivoConsulta = motivoConsulta;
    }

    public Empresa getEstabelecimentoExecutante() {
        return estabelecimentoExecutante;
    }

    public void setEstabelecimentoExecutante(Empresa estabelecimentoExecutante) {
        this.estabelecimentoExecutante = estabelecimentoExecutante;
    }

    public Empresa getUnidadeOrigem() {
        return unidadeOrigem;
    }

    public void setUnidadeOrigem(Empresa unidadeOrigem) {
        this.unidadeOrigem = unidadeOrigem;
    }

    private void carregaUnidadeOrigem() {
        final UsuarioCadsus usuarioCadsus = this.dto.getUsuarioCadsus();

        EnderecoDomicilio enderecoDomicilio = usuarioCadsus.getEnderecoDomicilio();
        if (enderecoDomicilio != null) {
            enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                    .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                    .setId(enderecoDomicilio.getCodigo()).start().getVO();

            if (enderecoDomicilio.getEquipeMicroArea() != null) {
                EquipeProfissional equipeProfissional = LoadManager.getInstance(EquipeProfissional.class)
                        .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE_MICRO_AREA), enderecoDomicilio.getEquipeMicroArea()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE), enderecoDomicilio.getCidade()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                        .setMaxResults(1).start().getVO();

                if (equipeProfissional != null) {
                    this.unidadeOrigem = equipeProfissional.getEquipe().getEmpresa();
                }
            }
        }

        if (this.unidadeOrigem == null) {
            EnderecoUsuarioCadsus enderecoUsuarioCadsus = usuarioCadsus.getEnderecoUsuarioCadsus();
            if (enderecoUsuarioCadsus != null) {
                enderecoUsuarioCadsus = LoadManager.getInstance(EnderecoUsuarioCadsus.class)
                        .addProperties(new HQLProperties(Empresa.class, EnderecoUsuarioCadsus.PROP_EMPRESA).getProperties())
                        .setId(enderecoUsuarioCadsus.getCodigo()).start().getVO();
                this.unidadeOrigem = enderecoUsuarioCadsus.getEmpresa();
            }
        }
    }

    private void enableAdiantamento(AjaxRequestTarget target, Convenio convenio) {
        boolean enable = false;
        if (convenioParticular != null && convenio != null && convenio.getCodigo().equals(convenioParticular.getCodigo())) {
            enable = true;
        }

        if (enable) {
            try {
                BOFactory.getBO(CommomFacade.class
                ).modulo(Modulos.GERAL).getParametro("adiantamento");
            } catch (DAOException | ValidacaoRuntimeException ex) {
                MessageUtil.modalWarn(target, this, ex);
                return;
            }
        }
        txtValorAdiantamento.limpar(target);
        autoCompleteConsultaFormaPagamento.limpar(target);
        if (enable) {
            target.appendJavaScript(JScript.showFieldset(containerFinanceiro));
        } else {
            target.appendJavaScript(JScript.hideFieldset(containerFinanceiro));
        }
        target.add(containerFinanceiro);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(OnDomReadyHeaderItem.forScript(JScript.hideFieldset(containerFinanceiro)));
    }

    public void setResourceImage(AjaxRequestTarget target, String... param) {
        fotoComponente.setImageResource(target, param);
    }
}
