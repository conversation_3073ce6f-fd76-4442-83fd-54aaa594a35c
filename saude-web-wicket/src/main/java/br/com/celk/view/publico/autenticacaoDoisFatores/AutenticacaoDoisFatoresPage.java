package br.com.celk.view.publico.autenticacaoDoisFatores;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.notification.NotificationPanel;
import br.com.celk.system.Application;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.LoginHelper;
import br.com.celk.util.DataUtil;
import br.com.celk.view.bemvindo.BemVindoPage;
import br.com.celk.view.controle.usuario.EdicaoCpfTelefone;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.home.HomePage;
import br.com.celk.view.login.*;
import br.com.celk.view.publico.template.base.BasePagePublico;
import br.com.celk.view.senhaexpirada.SenhaExpiradaPage;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.smsappservice.dto.SmsDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SessaoWeb;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.ksisolucoes.vo.service.sms.CodigoAutenticacaoDoisFatores;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import net.sf.jasperreports.engine.JRException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import javax.resource.ResourceException;
import java.io.IOException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.sql.Timestamp;
import java.util.*;
import java.util.jar.Manifest;

public class AutenticacaoDoisFatoresPage extends BasePagePublico {

    private Usuario usuario;
    private Form form;
    private AutenticacaoDoisFatoresDTO autenticacaoDoisFatoresDTO;
    private String codigo;
    private Label labelMsgWhatsappEnviado;
    private NotificationPanel notificationPanel;
    private List<Empresa> empresas = new ArrayList<Empresa>();
    private Empresa empresa;
    private LoginCommons loginCommons;
    private InputField inputPrimeiroNumero;
    private InputField inputSegundoNumero;
    private InputField inputTerceiroNumero;
    private InputField inputQuartoNumero;
    private InputField inputQuintoNumero;
    private InputField inputSextoNumero;
    private String area;
    private String finalTelefone;
    private String primeiroCaractere;
    private String caractereConta;
    private String msgWhatsappReenviado;

    public AutenticacaoDoisFatoresPage() {
    }

    public AutenticacaoDoisFatoresPage(Usuario usuario, LoginCommons loginCommons) throws DAOException, ValidacaoException {
        this.loginCommons = loginCommons;
        this.usuario = usuario;
        enviarCodigoAutenticacao(usuario);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();

        form = new Form("form");

        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        add(notificationPanel = new NotificationPanel("notificationPanel"));

        area = "";
        finalTelefone = "";
        if (usuario.getTelefone() != null && !usuario.getTelefone().isEmpty()) {
            area = usuario.getTelefone().substring(1, 3);
            finalTelefone = usuario.getTelefone().substring(11, 15);
        }
        primeiroCaractere = "";
        caractereConta = "";
        if (usuario.getEmail() != null && !usuario.getEmail().isEmpty()) {
            primeiroCaractere = usuario.getEmail().substring(0, 1);

            int posicaoArroba = usuario.getEmail().indexOf('@');
            if (posicaoArroba != -1 && posicaoArroba + 2 < usuario.getEmail().length()) {
                caractereConta = usuario.getEmail().substring(posicaoArroba + 1, posicaoArroba + 3);
            }
        }
        String nome = usuario.getNome();
        String[] splitNome = nome.split(" ");

        if (usuario.getEmail() != null && !usuario.getEmail().isEmpty() && usuario.getTelefone() != null && !usuario.getTelefone().isEmpty()) {
            form.add(labelMsgWhatsappEnviado = new Label("msgWhatsappEnviado", BundleManager.getString("msgSmsEmailEnviado", splitNome[0], area, finalTelefone, primeiroCaractere, caractereConta)));
            labelMsgWhatsappEnviado.setOutputMarkupId(true);
            msgWhatsappReenviado = BundleManager.getString("msgSmsEmailReenviado", splitNome[0], area, finalTelefone, primeiroCaractere, caractereConta);
        } else if (usuario.getEmail() != null && !usuario.getEmail().isEmpty()) {
            form.add(labelMsgWhatsappEnviado = new Label("msgWhatsappEnviado", BundleManager.getString("msgEmailEnviado", splitNome[0], primeiroCaractere, caractereConta)));
            labelMsgWhatsappEnviado.setOutputMarkupId(true);
            msgWhatsappReenviado = BundleManager.getString("msgEmailReenviado", splitNome[0], primeiroCaractere, caractereConta);
        } else {
            form.add(labelMsgWhatsappEnviado = new Label("msgWhatsappEnviado", BundleManager.getString("msgSmsEnviado", splitNome[0], area, finalTelefone)));
            labelMsgWhatsappEnviado.setOutputMarkupId(true);
            msgWhatsappReenviado = BundleManager.getString("msgSmsReenviado", splitNome[0], area, finalTelefone);
        }

        form.add(inputPrimeiroNumero = new InputField("autenticacaoDoisFatoresDTO.primeiroNumero"));
        form.add(inputSegundoNumero = new InputField("autenticacaoDoisFatoresDTO.segundoNumero"));
        form.add(inputTerceiroNumero = new InputField("autenticacaoDoisFatoresDTO.terceiroNumero"));
        form.add(inputQuartoNumero = new InputField("autenticacaoDoisFatoresDTO.quartoNumero"));
        form.add(inputQuintoNumero = new InputField("autenticacaoDoisFatoresDTO.quintoNumero"));
        form.add(inputSextoNumero = new InputField("autenticacaoDoisFatoresDTO.sextoNumero"));

        inputPrimeiroNumero.addAjaxUpdateValue();
        inputPrimeiroNumero.add(new AjaxFormComponentUpdatingBehavior("keyup") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (inputPrimeiroNumero.getValue() != null && !inputPrimeiroNumero.getValue().isEmpty()) {
                    ajaxRequestTarget.appendJavaScript(JScript.focusComponent(inputSegundoNumero));
                    ajaxRequestTarget.add(inputSegundoNumero);
                }
            }
        });

        inputSegundoNumero.addAjaxUpdateValue();
        inputSegundoNumero.add(new AjaxFormComponentUpdatingBehavior("keyup") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (inputSegundoNumero.getValue() != null && !inputSegundoNumero.getValue().isEmpty()) {
                    ajaxRequestTarget.appendJavaScript(JScript.focusComponent(inputTerceiroNumero));
                    ajaxRequestTarget.add(inputTerceiroNumero);
                }
            }
        });

        inputTerceiroNumero.addAjaxUpdateValue();
        inputTerceiroNumero.add(new AjaxFormComponentUpdatingBehavior("keyup") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (inputTerceiroNumero.getValue() != null && !inputTerceiroNumero.getValue().isEmpty()) {
                    ajaxRequestTarget.appendJavaScript(JScript.focusComponent(inputQuartoNumero));
                    ajaxRequestTarget.add(inputQuartoNumero);
                }
            }
        });

        inputQuartoNumero.addAjaxUpdateValue();
        inputQuartoNumero.add(new AjaxFormComponentUpdatingBehavior("keyup") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (inputQuartoNumero.getValue() != null && !inputQuartoNumero.getValue().isEmpty()) {
                    ajaxRequestTarget.appendJavaScript(JScript.focusComponent(inputQuintoNumero));
                    ajaxRequestTarget.add(inputQuintoNumero);
                }
            }
        });

        inputQuintoNumero.addAjaxUpdateValue();
        inputQuintoNumero.add(new AjaxFormComponentUpdatingBehavior("keyup") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (inputQuintoNumero.getValue() != null && !inputQuintoNumero.getValue().isEmpty()) {
                    ajaxRequestTarget.appendJavaScript(JScript.focusComponent(inputSextoNumero));
                    ajaxRequestTarget.add(inputSextoNumero);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException, JRException, IOException {
                PageParameters pp = new PageParameters();
                setResponsePage(new LoginPage(pp));
            }
        });

        form.add(new AbstractAjaxButton("btnReenviarCodigo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                try {
                    labelMsgWhatsappEnviado.setDefaultModelObject(msgWhatsappReenviado);
                    target.add(labelMsgWhatsappEnviado);
                    enviarCodigoAutenticacao(usuario);
                    limpar(target);
                } catch (Throwable ex) {
                    Loggable.log.warn("Envio de Código: " + ex.getMessage());
                }
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                try {
                    validarCodigo();
                } catch (DAOException | ResourceException e) {
                    throw new RuntimeException(e);
                }
                target.add(labelMsgWhatsappEnviado);
            }
        });

        add(form);

    }

    private void validarCodigo() throws ValidacaoException, DAOException, ResourceException {
        String codigoDigitado;

        if (autenticacaoDoisFatoresDTO != null) {
            codigoDigitado = safeConcat(autenticacaoDoisFatoresDTO.getPrimeiroNumero())
                    .concat(safeConcat(autenticacaoDoisFatoresDTO.getSegundoNumero()))
                    .concat(safeConcat(autenticacaoDoisFatoresDTO.getTerceiroNumero())
                    .concat(safeConcat(autenticacaoDoisFatoresDTO.getQuartoNumero()))
                    .concat(safeConcat(autenticacaoDoisFatoresDTO.getQuintoNumero()))
                    .concat(safeConcat(autenticacaoDoisFatoresDTO.getSextoNumero())));
        } else {
            codigoDigitado = "";
        }

        CodigoAutenticacaoDoisFatores codigoAutenticacaoDoisFatores = buscarCodigoValidacaoUsuario();

        Date dataAtual = new Date();

        if (isWithin15Minutes(codigoAutenticacaoDoisFatores.getDataGeracao(), dataAtual) && codigoDigitado.equals(codigo)) {
            efetivarLogin(usuario);
        } else if (!codigoDigitado.equals(codigo)) {
            labelMsgWhatsappEnviado.setDefaultModelObject(BundleManager.getString("codigoInvalido"));
        } else {
            labelMsgWhatsappEnviado.setDefaultModelObject(BundleManager.getString("tempoExcedido"));
        }

    }

    private String safeConcat(String numero) {
        return numero != null ? numero : "";
    }

    private void enviaSms() throws ValidacaoException, DAOException {
        String telefoneLimpo = limpaFormatacaoTelefone(usuario.getTelefone());

        String mensagem = BundleManager.getString("msgCodigoSms", codigo);
        SmsDTOParam param = new SmsDTOParam();
        param.setMessage(mensagem);
        param.setPhone(telefoneLimpo);
        param.setOrigem(SmsMensagem.OrigemSms.PROCESSO_DESCONHECIDO);
        param.setNomePaciente(usuario.getNome());

        BOFactory.getBO(SmsFacade.class).enviarSms(param);
    }

    private void enviaEmail() throws ValidacaoException, DAOException {
        try{
            Email.create()
                    .assunto("Codigo de Autenticação Celk Sistemas")
                    .para(usuario.getEmail())
                    .mensagem("Código: " + codigo)
                    .send();
        }catch(Throwable ex){
            Loggable.log.warn("Email: "+ usuario.getEmail() + " - " + ex.getMessage());
            throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
        }
    }

    private String gerarCodigo() {
        Random random = new Random();
        int number = random.nextInt(1000000);
        return String.format("%06d", number);
    }

    private void salvarCodigoAutenticacao(String codigo) throws DAOException, ValidacaoException {
        CodigoAutenticacaoDoisFatores codigoAutenticacaoDoisFatores = new CodigoAutenticacaoDoisFatores();
        codigoAutenticacaoDoisFatores.setCodigoValidacao(codigo);
        codigoAutenticacaoDoisFatores.setUsuario(usuario);
        codigoAutenticacaoDoisFatores.setDataGeracao(new Timestamp(System.currentTimeMillis()));

        BOFactory.save(codigoAutenticacaoDoisFatores);
    }

    private CodigoAutenticacaoDoisFatores buscarCodigoValidacaoUsuario() {
        List<CodigoAutenticacaoDoisFatores> codigoAutenticacaoDoisFatores = Collections.emptyList();

        codigoAutenticacaoDoisFatores = LoadManager.getInstance(CodigoAutenticacaoDoisFatores.class)
                .addProperties(new HQLProperties(CodigoAutenticacaoDoisFatores.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(CodigoAutenticacaoDoisFatores.PROP_USUARIO, usuario.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(CodigoAutenticacaoDoisFatores.PROP_DATA_GERACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getList();

        this.codigo = codigoAutenticacaoDoisFatores.get(0).getCodigoValidacao().toString();

        return !codigoAutenticacaoDoisFatores.isEmpty() ? codigoAutenticacaoDoisFatores.get(0) : null;

    }

    private static boolean isWithin15Minutes(Date date1, Date date2) {
        long difEmMillis = Math.abs(date2.getTime() - date1.getTime());
        long difEmMinutos = difEmMillis / (1000 * 60);
        return difEmMinutos < 15;
    }

    public boolean verificaCelular(String numero) {
        return numero != null && numero.matches("\\(\\d{2}\\)\\s9\\d{4}-\\d{4}");
    }

    public void enviarCodigoAutenticacao(Usuario usuario) throws DAOException, ValidacaoException {
        codigo = gerarCodigo();

        if (usuario.getTelefone() != null && verificaCelular(usuario.getTelefone())) {
            enviaSms();
        }
        if (usuario.getEmail() != null) {
            enviaEmail();
        }

        salvarCodigoAutenticacao(codigo);
    }

    private String limpaFormatacaoTelefone(String telefoneFormatado) {
        String telefone = telefoneFormatado.replaceAll("\\D", "");
        if (telefone.length() >= 12) {
            return telefone.substring(2);
        }
        return telefone;
    }

    public void limpar(AjaxRequestTarget target) {
        inputPrimeiroNumero.limpar(target);
        inputSegundoNumero.limpar(target);
        inputTerceiroNumero.limpar(target);
        inputQuartoNumero.limpar(target);
        inputQuintoNumero.limpar(target);
        inputSextoNumero.limpar(target);
    }

    @Override
    public void message(AjaxRequestTarget target, String message, int lvl) {
        getSession().getFeedbackMessages().add(new FeedbackMessage(notificationPanel, message, lvl));
        updateNotificationPanel(target);
    }

    @Override
    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop) {
        target.add(notificationPanel);
        if (scrollToTop) {
            target.appendJavaScript(JScript.scrollToTop());
        }
    }

    @Override
    public String getTituloPrograma() {
        return "";
    }

    private void efetivarLogin(Usuario usuario) throws ValidacaoException, DAOException, ResourceException {
        this.usuario = usuario;
        List<Long> tipoEstabelecimentoList = TipoEstabelecimento.codigos();
        tipoEstabelecimentoList.remove(TipoEstabelecimento.VIGILANCIA_EXTERNO.value()); //não aparecer no login estabelecimentos da vigilância.

        empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(usuario, tipoEstabelecimentoList));

        Long obrigarEnderecoMac = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ObrigarEnderecoMac");
        if (RepositoryComponentDefault.SIM_LONG.equals(obrigarEnderecoMac)) {
            String mac = null;
            try {
                mac = WicketMethods.getMacAddress();
            } catch (SocketException | UnknownHostException e) {
                Loggable.log.error(e);
            }
            boolean macLiberado = BOFactoryWicket.getBO(BasicoFacade.class).consultaAcessoMac(mac);
            if (!macLiberado) {
                throw new ValidacaoException(BundleManager.getString("macXNaoLiberadoParaAcessoAoSistema", mac));
            }
        }

        if (getSession().isTemporary()) {
            getSession().bind();
        }
        String tenant = TenantContext.getContext();
        long timeSession = 0;
        if (tenant != null) {
            Date data = DataUtil.getDataAtual();
            timeSession = data.getTime();
            SessaoWeb sessaoWeb = new SessaoWeb();
            sessaoWeb.setDataEntrada(data);
            sessaoWeb.setIdSessao(ApplicationSession.get().getId() + timeSession);
            sessaoWeb.setHost(WicketMethods.getIpClient());
            sessaoWeb.setUsuario(usuario);
            try {
                BOFactoryWicket.save(sessaoWeb);
            } catch (SGKException ex) {
                Loggable.log.error(BundleManager.getString("naoFoiPossivelRegistrarSessao"), ex);
            }
        }
        ApplicationSession.get().setTenant(TenantContext.getRealContext());
        ApplicationSession.get().setTimeSession(timeSession);

        try {
            Manifest manifest = new Manifest(Application.get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
            String version = manifest.getMainAttributes().getValue("Gem-Version");
            ApplicationSession.get().setVersao(version);
        } catch (IOException e) {
            Loggable.log.error(e);
        }

        termosAceite(usuario);
    }

    private void termosAceite(Usuario usuario) throws ValidacaoException, DAOException {
        String mesagemAvisoLogin = loginCommons.getGeral().getParametro("MensagemAvisoLogin");

        if (mesagemAvisoLogin != null && !mesagemAvisoLogin.isEmpty()) {
            redirectAvisoLogin(usuario);
        } else {
            LoginHelper.configurarAceiteTermosDeUso(usuario);

            if (RepositoryComponentDefault.NAO_LONG.equals(usuario.getAceiteTermoUso())) {
                redirectAceiteTermoUso(usuario);
            } else {
                String validaTelefoneCpf = loginCommons.getGeral().getParametro("validaCpfTelefoneUsuario");
                if ((usuario.getTelefone() == null || usuario.getCpf() == null) && RepositoryComponentDefault.SIM.equals(validaTelefoneCpf)) {
                    redirectEdicaoTelefone(usuario);
                } else {
                    redirectLoginUnidade(usuario);
                }
            }
        }
    }

    private void redirectLoginUnidade(Usuario usuario) throws ValidacaoException, DAOException {
        if (apenasUmaEmpresaUsuario()) {
            getPrimeiraEmpresa();
            acessarSistema();
        } else {
            setResponsePage(new LoginUnidadePage(usuario, empresas));
        }
    }

    private void redirectEdicaoTelefone(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new EdicaoCpfTelefone(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new EdicaoCpfTelefone(usuario, empresas));
        }
    }

    private void redirectAceiteTermoUso(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new TermoUsoPage(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new TermoUsoPage(usuario, empresas));
        }
    }

    private void redirectAvisoLogin(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new LoginAvisoPage(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new LoginAvisoPage(usuario, empresas));
        }
    }

    private boolean apenasUmaEmpresaUsuario() {
        return empresas.size() == 1;
    }

    private Empresa getPrimeiraEmpresa() {
        empresa = empresas.get(0);
        return empresa;
    }

    private void acessarSistema() throws ValidacaoException, DAOException {
        if (empresa == null) {
            throw new ValidacaoException(BundleManager.getString("informe_unidade"));
        }
        usuario = LoginHelper.iniciarSessao(usuario, empresa);
        if (usuario.getDataRegistro() == null) {
            setResponsePage(BemVindoPage.class);
        } else if (usuario.getDiasExpirarSenha() != null
                && Data.adjustRangeHour(Data.addDias(usuario.getDataRegistro(), usuario.getDiasExpirarSenha().intValue())).getDataInicial().before(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
            setResponsePage(SenhaExpiradaPage.class);
        } else if (usuario.getProgramaWeb() != null) {
            getSession().setAttribute("mobilePaginaUnica", false);
            Class classe = buscarPrograma(usuario.getProgramaWeb());
            boolean pagePermitted = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), classe.getName());
            if (pagePermitted) {
                setResponsePage(classe);
            } else {
                throw new ValidacaoException(BundleManager.getString("msgUsuarioSemPermissao"));
            }
        } else {
            setResponsePage(HomePage.class);
        }

    }

    private Class buscarPrograma(ProgramaWeb programaWeb) {
        Class classe = null;
        programaWeb = LoadManager.getInstance(ProgramaWeb.class)
                .setId(programaWeb.getCodigo())
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .start().getVO();
        try {
            String caminhoPagina = programaWeb.getProgramaPaginaPrincipal().getCaminhoPagina();
            if (caminhoPagina != null) {
                classe = Class.forName(caminhoPagina);
            }
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return classe;
    }

}
