package br.com.celk.view.consorcio.pedidolicitacao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.EloLicitacaoPedido;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.ManutencaoPedidoLicitacaoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.math.MathContext;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;


/**
 *
 * <AUTHOR>
 */
public abstract class PnlAlterarValorTotalLicitado extends Panel{

    private CompoundPropertyModel<ManutencaoPedidoLicitacaoItem> model;
    private InputField txtProduto;
    private InputField txtValorTotalAnterior;
    private DoubleField txtSaldoLicitacao;
    private DoubleField txtValorTotal;
    private InputArea txtJustificativa;
    
    private PedidoLicitacaoItem pedidoLicitacaoItem;
    
    public PnlAlterarValorTotalLicitado(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel<ManutencaoPedidoLicitacaoItem>(new ManutencaoPedidoLicitacaoItem()));
        
        form.add(txtProduto = new DisabledInputField(VOUtils.montarPath(ManutencaoPedidoLicitacaoItem.PROP_PEDIDO_LICITACAO_ITEM, PedidoLicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        form.add(txtValorTotalAnterior = new DisabledDoubleField(VOUtils.montarPath(ManutencaoPedidoLicitacaoItem.PROP_VALOR_TOTAL_LICITADO_ANTERIOR)));
        form.add(txtSaldoLicitacao = new DisabledDoubleField("saldoLicitacao", new LoadableDetachableModel<Double>() {

            @Override
            protected Double load() {
                return getSaldoLicitacao();
            }
        }));
        form.add(txtValorTotal = new DoubleField(VOUtils.montarPath(ManutencaoPedidoLicitacaoItem.PROP_VALOR_TOTAL_LICITADO)));
        form.add(txtJustificativa = new InputArea(VOUtils.montarPath(ManutencaoPedidoLicitacaoItem.PROP_JUSTIFICATIVA)));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ManutencaoPedidoLicitacaoItem manutencaoPedidoLicitacaoItem = model.getObject();
                if (manutencaoPedidoLicitacaoItem.getValorTotalLicitado()==null) {
                    throw new ValidacaoException(BundleManager.getString("informeNovoValorTotal"));
                }
                if (manutencaoPedidoLicitacaoItem.getJustificativa()==null) {
                    throw new ValidacaoException(BundleManager.getString("informeJustificativa"));
                }
                pedidoLicitacaoItem = BOFactoryWicket.getBO(ConsorcioFacade.class).alterarValorTotalLicitadoPedidoLicitacaoItem(manutencaoPedidoLicitacaoItem);
                onConfirmar(target, pedidoLicitacaoItem);
            }

        });
        
        form.add(new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }

        }.setDefaultFormProcessing(false));
        
        add(form);
    }

    public void setPedidoLicitacaoItem(PedidoLicitacaoItem pedidoLicitacaoItem) {
        this.pedidoLicitacaoItem = pedidoLicitacaoItem;
        model.getObject().setPedidoLicitacaoItem(pedidoLicitacaoItem);
        model.getObject().setValorTotalLicitadoAnterior(pedidoLicitacaoItem.getValorTotalLicitado());
    }
    
    public void limpar(AjaxRequestTarget target){
        model.setObject(new ManutencaoPedidoLicitacaoItem());
        txtProduto.limpar(target);
        txtValorTotalAnterior.limpar(target);
        txtValorTotal.limpar(target);
        txtJustificativa.limpar(target);
    }
    
    private Double getSaldoLicitacao(){
            EloLicitacaoPedido elo = LoadManager.getInstance(EloLicitacaoPedido.class)
                    .addProperties(new HQLProperties(LicitacaoItem.class, EloLicitacaoPedido.PROP_LICITACAO_ITEM).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloLicitacaoPedido.PROP_PEDIDO_LICITACAO_ITEM), this.pedidoLicitacaoItem))
                    .start().getVO();
            
            LicitacaoItem licitacaoItem = elo.getLicitacaoItem();
            
            List<EloLicitacaoPedido> elos = LoadManager.getInstance(EloLicitacaoPedido.class)
                    .addProperties(new HQLProperties(PedidoLicitacaoItem.class, EloLicitacaoPedido.PROP_PEDIDO_LICITACAO_ITEM).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloLicitacaoPedido.PROP_LICITACAO_ITEM), licitacaoItem))
                    .start().getList();
            
            Double somaValorTotalLicitado = 0D;
            
            for (EloLicitacaoPedido eloLicitacaoPedido : elos) {
                somaValorTotalLicitado = new Dinheiro(somaValorTotalLicitado, MathContext.DECIMAL128).somar(Coalesce.asDouble(eloLicitacaoPedido.getPedidoLicitacaoItem().getValorTotalLicitado())).doubleValue();
            }
            
            return new Dinheiro(Coalesce.asDouble(licitacaoItem.getPrecoTotal()), MathContext.DECIMAL128).subtrair(somaValorTotalLicitado).round().doubleValue();
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, PedidoLicitacaoItem licitacaoItem) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public DoubleField getTxtValorTotal() {
        return txtValorTotal;
    }

}
