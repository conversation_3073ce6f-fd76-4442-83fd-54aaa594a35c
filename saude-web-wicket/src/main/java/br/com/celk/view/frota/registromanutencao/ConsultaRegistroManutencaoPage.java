package br.com.celk.view.frota.registromanutencao;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.frota.registromanutencao.autocomplete.AutoCompleteConsultaModeloDocumento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.frota.ModeloDocumento;
import br.com.ksisolucoes.vo.frota.RegistroManutencao;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaRegistroManutencaoPage extends ConsultaPage<RegistroManutencao, List<BuilderQueryCustom.QueryParameter>> {

    private String documento;
    private Pessoa fornecedor;
    private ModeloDocumento modeloDocumento;
    private String tipoData;
    private DatePeriod periodo;
    private UpperField txtDocumento;

    public ConsultaRegistroManutencaoPage() {
    }
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(txtDocumento = new UpperField("documento"));
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(new AutoCompleteConsultaModeloDocumento("modeloDocumento"));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(getDropDownTipoData());

        setExibeExpandir(true);
    }

    private DropDown getDropDownTipoData(){
        DropDown dropDown = new DropDown("tipoData");
        
        dropDown.addChoice(RegistroManutencao.PROP_DATA_MANUTENCAO , BundleManager.getString("dataManutencao"));
        dropDown.addChoice(RegistroManutencao.PROP_DATA_CADASTRO , BundleManager.getString("dataCadastro"));
        
        return dropDown;
    }
    
    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(RegistroManutencao.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("fornecedor"), VOUtils.montarPath(RegistroManutencao.PROP_FORNECEDOR,Pessoa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("documento"), VOUtils.montarPath(RegistroManutencao.PROP_DOCUMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("manutencao"), VOUtils.montarPath(RegistroManutencao.PROP_DATA_MANUTENCAO)));
        columns.add(new DoubleColumn(BundleManager.getString("valorTotalItem"), VOUtils.montarPath(RegistroManutencao.PROP_VALOR_TOTAL), VOUtils.montarPath(RegistroManutencao.PROP_VALOR_TOTAL)).setCasasDecimais(4));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("modeloDocumento"), VOUtils.montarPath(RegistroManutencao.PROP_MODELO_DOCUMENTO,ModeloDocumento.PROP_DESCRICAO)));
        
        return columns;
    }
    
    private CustomColumn<RegistroManutencao> getCustomColumn(){
        return new CustomColumn<RegistroManutencao>() {

            @Override
            public Component getComponent(String componentId, final RegistroManutencao rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroRegistroManutencaoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesRegistroManutencaoPage(rowObject));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaRegistroManutencao()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RegistroManutencao.PROP_DATA_CADASTRO, false);
            }
            
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroManutencao.PROP_DOCUMENTO), BuilderQueryCustom.QueryParameter.ILIKE, documento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroManutencao.PROP_EMPRESA), SessaoAplicacaoImp.getInstance().getEmpresa()));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroManutencao.PROP_FORNECEDOR), fornecedor));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroManutencao.PROP_MODELO_DOCUMENTO), modeloDocumento));
        if (periodo!=null) {
            parameters.add(new QueryCustom.QueryCustomParameter(tipoData, Data.adjustRangeHour(periodo)));
        }
        
        return parameters;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDocumento;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroRegistroManutencaoPage.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRegistroManutencao");
    }
    
}
