package br.com.celk.view.materiais.devolucao.item.lote;

import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoItemLote;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgLoteDevolucao extends Window{

    private PnlLoteDevolucao panelSaidaLote;
    
    public DlgLoteDevolucao(String id) {
        super(id);
        init();
    }

    private void init() {
        setMinimalWidth(700);
        setMinimalHeight(400);
        
        setContent(panelSaidaLote = new PnlLoteDevolucao(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
        
        setTitle(BundleManager.getString("lotes"));
    }
    
    public void setList(List<DispensacaoItemLote> itens){
        panelSaidaLote.setList(itens);
    }
    
    public void update(AjaxRequestTarget target){
        panelSaidaLote.update(target);
    }
    
    public void add(ISelectionAction<DispensacaoItemLote> selectionAction){
        panelSaidaLote.add(selectionAction);
    }
    
    public void limparSelecionado(){
        panelSaidaLote.limparSelecionado();
    }

}
