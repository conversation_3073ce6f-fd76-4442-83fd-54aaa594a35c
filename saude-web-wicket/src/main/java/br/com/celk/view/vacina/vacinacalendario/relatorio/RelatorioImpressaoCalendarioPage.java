package br.com.celk.view.vacina.vacinacalendario.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vacina.dto.RelatorioImpressaoCalendarioVacinacaoDTOParam;
import br.com.ksisolucoes.report.vacina.interfaces.facade.VacinaReportFacade;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioImpressaoCalendarioPage extends RelatorioPage<RelatorioImpressaoCalendarioVacinacaoDTOParam> {

    private Long idadeInicial;
    private Long idadeFinal;
    private Long tipoIdadeInicial;
    private Long tipoIdadeFinal;

    @Override
    public void init(Form form) {
        form.add(new InputField("idadeInicial", new PropertyModel(this, "idadeInicial")));
        form.add(DropDownUtil.getIEnumDropDown("cbxTipoIdadeInicial", new PropertyModel(this, "tipoIdadeInicial"), VacinaCalendario.TipoIdadeVacina.values()));
        form.add(new InputField("idadeFinal", new PropertyModel(this, "idadeFinal")));
        form.add(DropDownUtil.getIEnumDropDown("cbxTipoIdadeFinal", new PropertyModel(this, "tipoIdadeFinal"), VacinaCalendario.TipoIdadeVacina.values()));
        form.add(new AutoCompleteConsultaTipoVacina("tipoVacina"));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
        form.add(getDropDownOpcional());
    }

    private DropDown getDropDownOpcional() {
        DropDown dropDown = new DropDown("opcional");

        dropDown.addChoice(null, BundleManager.getString("ambos"));
        dropDown.addChoice(VacinaCalendario.VacinaOpcional.NAO.value(), VacinaCalendario.VacinaOpcional.NAO.descricao());
        dropDown.addChoice(VacinaCalendario.VacinaOpcional.SIM.value(), VacinaCalendario.VacinaOpcional.SIM.descricao());

        return dropDown;
    }

    @Override
    public Class<RelatorioImpressaoCalendarioVacinacaoDTOParam> getDTOParamClass() {
        return RelatorioImpressaoCalendarioVacinacaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioImpressaoCalendarioVacinacaoDTOParam param) throws ReportException {
        param.setIdadeInicial(resolveIdade(idadeInicial, tipoIdadeInicial));
        param.setIdadeFinal(resolveIdade(idadeFinal, tipoIdadeFinal));

        return BOFactoryWicket.getBO(VacinaReportFacade.class).relatorioImpressaoCalendarioVacinacao(param);
    }

    private Long resolveIdade(Long idade, Long tipoIdade) {
        if (tipoIdade.equals(VacinaCalendario.TipoIdadeVacina.ANOS.value())) {
            return idade * 12;
        } else {
            return idade;
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioCalendarioCavinacao");
    }

}
