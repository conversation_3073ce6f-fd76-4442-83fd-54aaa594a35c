package br.com.celk.view.agenda.manutencaoagenda.panel;

import br.com.celk.component.inputfield.DisabledInputField;
import br.com.ksisolucoes.bo.command.LoadManager;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class ManutencaoAgendaDadosAgendaPanel extends Panel {
    
    private WebMarkupContainer containerDadosAgenda;
    private Agenda agenda;
    
    public ManutencaoAgendaDadosAgendaPanel(String id, Long codigoAgenda) {
        super(id);
        carregarAgenda(codigoAgenda);
        init();
    }
    
    private void init() {
        containerDadosAgenda = new WebMarkupContainer("containerDadosAgenda", new CompoundPropertyModel(getAgenda()));
        containerDadosAgenda.setOutputMarkupId(true);

        Agenda proxyAgenda = on(Agenda.class);

        containerDadosAgenda.add(new DisabledInputField(path(proxyAgenda.getEmpresa().getDescricao())));
        containerDadosAgenda.add(new DisabledInputField(path(proxyAgenda.getTipoProcedimento().getDescricaoFormatado())));        
        containerDadosAgenda.add(new DisabledInputField(path(proxyAgenda.getProfissional().getNome())));        
        
        add(containerDadosAgenda);
    }
    
    private Agenda getAgenda() {
        return agenda;
    }
    
    private void carregarAgenda(Long codigoAgenda){
        Agenda proxy = on(Agenda.class);
        
        agenda = LoadManager.getInstance(Agenda.class)
                .addProperty(path(proxy.getTipoProcedimento().getCodigo()))
                .addProperty(path(proxy.getTipoProcedimento().getDescricao()))
                .addProperty(path(proxy.getTipoProcedimento().getTipoProcedimentoClassificacao().getCodigo()))
                .addProperty(path(proxy.getEmpresa().getDescricao()))
                .addProperty(path(proxy.getEmpresa().getCodigo()))
                .addProperty(path(proxy.getProfissional().getNome()))
                .setId(codigoAgenda)
                .start().getVO();
    }
    
}