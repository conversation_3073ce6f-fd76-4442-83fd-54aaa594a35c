package br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete.restricaocontainer.RestricaoContainerVeiculo;
import br.com.ksisolucoes.bo.frota.interfaces.dto.QueryConsultaVeiculoDTOParam;
import br.com.ksisolucoes.bo.frota.interfaces.facade.FrotaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.Veiculo;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaVeiculo extends AutoCompleteConsulta<Veiculo> {

    private boolean flagValidaEstabelecimento;
    private boolean flagValidaVeiculoAtivo;

    public void setFlagValidaEstabelecimento(boolean flagValidaEstabelecimento) {
        this.flagValidaEstabelecimento = flagValidaEstabelecimento;
    }

    public void setFlagValidaVeiculoAtivo(boolean flagValidaVeiculoAtivo) {
        this.flagValidaVeiculoAtivo = flagValidaVeiculoAtivo;
    }

    public AutoCompleteConsultaVeiculo(String id) {
        super(id);
    }

    public AutoCompleteConsultaVeiculo(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaVeiculo(String id, IModel<Veiculo> model) {
        super(id, model);
    }

    public AutoCompleteConsultaVeiculo(String id, IModel<Veiculo> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(Veiculo.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Veiculo.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("placa"), VOUtils.montarPath(Veiculo.PROP_PLACA)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerVeiculo(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Veiculo, QueryConsultaVeiculoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaVeiculoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(FrotaFacade.class).consultarVeiculo(dataPaging);
                    }

                    @Override
                    public QueryConsultaVeiculoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaVeiculoDTOParam param = new QueryConsultaVeiculoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaVeiculoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setFlagValidaEstabelecimento(flagValidaEstabelecimento);
                        param.setFlagValidaVeiculoAtivo(flagValidaVeiculoAtivo);
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Veiculo.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return Veiculo.class;
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("veiculo");
    }

}
