package br.com.celk.view.vacina.produtovacina.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.grupoproduto.autocomplete.AutoCompleteConsultaGrupoProduto;
import br.com.celk.view.vacina.fabricantemedicamento.autocomplete.AutoCompleteConsultaFabricanteMedicamento;
import br.com.celk.view.vacina.grupovacinacaoesus.autocomplete.AutoCompleteConsultaTipoVacinaMulti;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vacina.dto.RelatorioEstoqueVacinasDTOParam;
import br.com.ksisolucoes.report.vacina.interfaces.facade.VacinaReportFacade;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoLeptospiroseEnum;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioEstoqueVacinasPage extends RelatorioPage<RelatorioEstoqueVacinasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;
    private AutoCompleteConsultaGrupoProduto autoCompleteConsultaGrupoProduto;
    private AutoCompleteConsultaTipoVacinaMulti autoCompleteConsultaTipoVacina;
    private AutoCompleteConsultaFabricanteMedicamento autoCompleteConsultaFabricanteMedicamento;

    private DropDown estoqueFisico;
    private DropDown formaApresentacao;
    private DropDown exibirLotes;
    private CheckBoxLongValue checkApenasVacinasIndisponiveis;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito("deposito"));
        form.add(autoCompleteConsultaGrupoProduto = new AutoCompleteConsultaGrupoProduto("grupoProduto"));
        form.add(autoCompleteConsultaFabricanteMedicamento = new AutoCompleteConsultaFabricanteMedicamento("fabricanteMedicamento"));
        form.add(autoCompleteConsultaTipoVacina = new AutoCompleteConsultaTipoVacinaMulti("tipoVacina"));
        autoCompleteConsultaTipoVacina.setRequired(true);
        autoCompleteConsultaTipoVacina.getTextField().addRequiredClass();
        form.add(checkApenasVacinasIndisponiveis = new CheckBoxLongValue("apenasIndisponiveis"));
        checkApenasVacinasIndisponiveis.setOutputMarkupId(true);

        form.add(estoqueFisico = new DropDown("estoqueFisico"));
        form.add(formaApresentacao = new DropDown("formaApresentacao"));
        form.add(exibirLotes = DropDownUtil.getNaoSimDropDown("exibirLotes"));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));

        boolean isActionPermittedEmpresa = isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermittedEmpresa);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);

        autoCompleteConsultaDeposito.setMultiplaSelecao(true);
        autoCompleteConsultaDeposito.setOperadorValor(true);

        autoCompleteConsultaGrupoProduto.setMultiplaSelecao(true);
        autoCompleteConsultaGrupoProduto.setOperadorValor(true);

        autoCompleteConsultaFabricanteMedicamento.setMultiplaSelecao(true);
        autoCompleteConsultaFabricanteMedicamento.setOperadorValor(true);

        DropDownUtil.setEnumChoices(estoqueFisico, RelatorioEstoqueVacinasDTOParam.EstoqueFisico.values());
        DropDownUtil.setEnumChoices(formaApresentacao, RelatorioEstoqueVacinasDTOParam.FormaApresentacao.values());

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(
                    AjaxRequestTarget target,
                    Empresa empresa
            ) {
                autoCompleteConsultaTipoVacina.setRequired(false);
                autoCompleteConsultaTipoVacina.getTextField().removeRequiredClass();

                target.add(autoCompleteConsultaTipoVacina);
            }
        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(
                    AjaxRequestTarget target,
                    Empresa empresa
            ) {
                autoCompleteConsultaTipoVacina.setRequired(true);
                autoCompleteConsultaTipoVacina.getTextField().addRequiredClass();

                target.add(autoCompleteConsultaTipoVacina);
            }
        });
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioEstoqueVacinasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioEstoqueVacinasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VacinaReportFacade.class).relatorioEstoqueVacinas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioEstoqueVacinas");
    }

}
