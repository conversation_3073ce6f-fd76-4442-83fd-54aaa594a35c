package br.com.celk.view.agenda.agendamento.tfd.processo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.StringUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper.carregarNumeroCartao;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroLaudoTfdStep1Page extends BasePage {
 
    private LaudoTfdDTO laudoTfdDTO;

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private InputField txtNumeroCartao;
    private TelefoneField fieldTelefone;
    private TelefoneField fieldTelefone2;
    private InputField txtTelefone3;
    private InputField txtTelefone4;
    private TelefoneField fieldCelular;
    private InputField txtEmail;
    private String numeroCartao;
    private DropDown<Long> dropDownTipo;

    public CadastroLaudoTfdStep1Page() {
        laudoTfdDTO = new LaudoTfdDTO();
    }

    @Override
    protected void postConstruct() {
        final Form<LaudoTfdDTO> form = new Form("form", new CompoundPropertyModel(laudoTfdDTO));
        LaudoTfdDTO proxy = on(LaudoTfdDTO.class);

        form.add(dropDownTipo = populateDropDownTipo(new DropDown<Long>(path(proxy.getLaudoTfd().getFlagTipo()))));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getLaudoTfd().getUsuarioCadsus()), true) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO;
            }
        });
        autoCompleteConsultaUsuarioCadsus.setLabel(new Model(BundleManager.getString("paciente")));
        form.add(txtNumeroCartao = new InputField("numeroCartao", new PropertyModel(this, "numeroCartao")));


        form.add(fieldTelefone = new TelefoneField(path(proxy.getLaudoTfd().getUsuarioCadsus().getTelefone())));
        form.add(fieldTelefone2 = new TelefoneField(path(proxy.getLaudoTfd().getUsuarioCadsus().getTelefone2())));
        form.add(txtTelefone3 = new InputField(path(proxy.getLaudoTfd().getUsuarioCadsus().getTelefone3())));
        form.add(txtTelefone4 = new InputField(path(proxy.getLaudoTfd().getUsuarioCadsus().getTelefone4())));
        form.add(fieldCelular = new TelefoneField(path(proxy.getLaudoTfd().getUsuarioCadsus().getCelular())));
        form.add(txtEmail = new InputField(path(proxy.getLaudoTfd().getUsuarioCadsus().getEmail())));

        enableContatos(false);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getLaudoTfd().getEmpresa()), true)
                .setLabel(new Model(BundleManager.getString("unidade"))));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getLaudoTfd().getProfissional()))
                .setLabel(new Model(BundleManager.getString("profissional"))));
        form.add(new InputField<String>(path(proxy.getLaudoTfd().getNomeProfissional())));

        form.add(new InputField(path(proxy.getLaudoTfd().getPedidoTfd().getNumeroPedido())));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar();
            }
        }));

        add(form);

        form.getModel().setObject(laudoTfdDTO);

        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                enableContatos(true);
                UsuarioCadsus paciente = (UsuarioCadsus) autoCompleteConsultaUsuarioCadsus.getComponentValue();
                try {
                    txtNumeroCartao.setComponentValue(carregarNumeroCartao(paciente));
                } catch (DAOException ex) {
                    Logger.getLogger(CadastroLaudoTfdStep1Page.class.getName()).log(Level.SEVERE, null, ex);
                } catch (ValidacaoException ex) {
                    Logger.getLogger(CadastroLaudoTfdStep1Page.class.getName()).log(Level.SEVERE, null, ex);
                }
                target.add(txtNumeroCartao);
                target.add(fieldTelefone);
                target.add(fieldTelefone2);
                target.add(txtTelefone3);
                target.add(txtTelefone4);
                target.add(fieldCelular);
                target.add(txtEmail);
                target.appendJavaScript(JScript.initMasks());
            }
        });
        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                txtNumeroCartao.limpar(target);
                fieldTelefone.limpar(target);
                fieldTelefone2.limpar(target);
                txtTelefone3.limpar(target);
                txtTelefone4.limpar(target);
                fieldCelular.limpar(target);
                txtEmail.limpar(target);

                enableContatos(false);

                target.add(txtNumeroCartao);
                target.add(fieldTelefone);
                target.add(fieldTelefone2);
                target.add(txtTelefone3);
                target.add(txtTelefone4);
                target.add(fieldCelular);
                target.add(txtEmail);
                target.appendJavaScript(JScript.initMasks());
            }
        });
    }

    private DropDown<Long> populateDropDownTipo(DropDown<Long> dropDown) {
        dropDown.addChoice(LaudoTfd.Tipo.INTRAESTADUAL.value(), BundleManager.getString("dentroDoEstado"));
        dropDown.addChoice(LaudoTfd.Tipo.INTERESTADUAL.value(), BundleManager.getString("foraDoEstado"));

        return dropDown;
    }


    private void avancar() throws ValidacaoException, DAOException {
        if (laudoTfdDTO.getLaudoTfd().getProfissional() == null && laudoTfdDTO.getLaudoTfd().getNomeProfissional() == null) {
            throw new ValidacaoException("Por favor, preencha o campo profissional ou nome do profissional.");
        }
        if (numeroCartao != null) {
            laudoTfdDTO.getLaudoTfd().setNumeroCartao(new Long(StringUtil.getDigits(numeroCartao)));
            if (!CnsValidator.validaCns(laudoTfdDTO.getLaudoTfd().getNumeroCartao().toString())) {
                throw new ValidacaoException(BundleManager.getString("cns_invalido"));
            }
            UsuarioCadsusCns cnsIgual = LoadManager.getInstance(UsuarioCadsusCns.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, laudoTfdDTO.getLaudoTfd().getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_NUMERO_CARTAO, laudoTfdDTO.getLaudoTfd().getNumeroCartao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                    .start().getVO();
            if (cnsIgual != null) {
                throw new ValidacaoException(BundleManager.getString("cnsJaInformadoParaOPacienteX")
                        + cnsIgual.getUsuarioCadsus().getNome());
            }
        } else {
            throw new ValidacaoException(BundleManager.getString("informeCns"));
        }
        if (laudoTfdDTO.getLaudoTfd().getPedidoTfd() != null && laudoTfdDTO.getLaudoTfd().getPedidoTfd().getNumeroPedido() != null) {
            List<PedidoTfd> lstPedidoTfd = LoadManager.getInstance(PedidoTfd.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTfd.PROP_NUMERO_PEDIDO, laudoTfdDTO.getLaudoTfd().getPedidoTfd().getNumeroPedido()))
                    .start().getList();
            if (lstPedidoTfd != null && lstPedidoTfd.size() > 0) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_numero_pedido_informado_ja_cadastrado_so_e_permitido_cadastro_de_novos_pedidos"));
            }
        }

        if (dropDownTipo.getComponentValue().equals(LaudoTfd.Tipo.INTRAESTADUAL.value())) {
            setResponsePage(new CadastroLaudoTfdIntraestadualStep2Page(laudoTfdDTO));
        } else if (dropDownTipo.getComponentValue().equals(LaudoTfd.Tipo.INTERESTADUAL.value())) {
            setResponsePage(new CadastroLaudoTfdInterestadualStep2Page(laudoTfdDTO));
        }

    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLaudoTfd");
    }

    private void enableContatos(boolean b) {
        fieldTelefone.setEnabled(b);
        fieldTelefone2.setEnabled(b);
        txtTelefone3.setEnabled(b);
        txtTelefone4.setEnabled(b);
        fieldCelular.setEnabled(b);
        txtEmail.setEnabled(b);
    }

}
