package br.com.celk.view.atendimento.consultaprontuario.panel.consultafichaacolhimento;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.action.link.ReportActionLinkPanel;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgDetalhesFichaAcolhimento;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgMotivoDestinoSaida;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

public class ConsultaFichaAcolhimentoPanel extends ConsultaProntuarioCadastroPanel {

    private Table tblPendentes;
    private Table tblHistorico;

    private DlgMotivoDestinoSaida dlgMotivoDestinoSaida;
    private DlgDetalhesFichaAcolhimento dlgDetalhesFichaAcolhimento;

    public ConsultaFichaAcolhimentoPanel(String id) {
        super(id, BundleManager.getString("fichaAcolhimento"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");

        form.add(tblPendentes = new Table("tblPendentes", getColumnsPendentes(), getCollectionProviderPendentes()));
        tblPendentes.populate();

        form.add(tblHistorico = new Table("tblHistorico", getColumnsHistorico(), getCollectionProviderHistorico()));
        tblHistorico.populate();

        add(form);
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();

        FichaAcolhimento proxy = on(FichaAcolhimento.class);

        columns.add(getActionColumnHistorico());
        columns.add(createColumn(bundle("dataAdmissao"), proxy.getDataAdmissao()));
        columns.add(createColumn(bundle("dataConclusao"), proxy.getDataConclusao()));
        columns.add(createColumn(bundle("tipoEncaminhamento"), proxy.getTipoEncaminhamento().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getCodigo()));
        columns.add(createColumn(bundle("motivo/saida"), proxy.getMotivoDestinoSaida().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return LoadManager.getInstance(FichaAcolhimento.class)
                        .addProperties(new HQLProperties(FichaAcolhimento.class).getProperties())
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getUsuarioCadsus()))
                        .addParameter(new QueryCustom.QueryCustomParameter(FichaAcolhimento.PROP_STATUS, FichaAcolhimento.Status.FINALIZADO.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(FichaAcolhimento.PROP_DATA_CONCLUSAO), "desc"))
                        .start().getList();
            }
        };
    }

    private List<IColumn> getColumnsPendentes() {
        List<IColumn> columns = new ArrayList<IColumn>();
        FichaAcolhimento proxy = on(FichaAcolhimento.class);

        columns.add(getActionColumnPendentes());
        columns.add(createColumn(bundle("dataAdmissao"), proxy.getDataAdmissao()));
        columns.add(createColumn(bundle("ultimoAtendimento"), proxy.getUltimoAtendimento().getDataAtendimento()));
        columns.add(createColumn(bundle("tipoEncaminhamento"), proxy.getTipoEncaminhamento().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getCodigo()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderPendentes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getFichaAcolhimento();
            }
        };
    }

    private List<FichaAcolhimento> getFichaAcolhimento() {

        return LoadManager.getInstance(FichaAcolhimento.class)
                .addProperties(new HQLProperties(FichaAcolhimento.class).getProperties())
                .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ULTIMO_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
                .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(FichaAcolhimento.PROP_STATUS, FichaAcolhimento.Status.PENDENTE.value()))
                .start().getList();
    }

    private IColumn getActionColumnPendentes() {
        return new MultipleActionCustomColumn<FichaAcolhimento>() {

            @Override
            public void customizeColumn(final FichaAcolhimento rowObject) {

                ReportActionLinkPanel acaoImprimir = addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<FichaAcolhimento>() {

                    @Override
                    public DataReport action(FichaAcolhimento modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoFichaAcolhimento(modelObject);
                    }
                });

            }
        };
    }

    private IColumn getActionColumnHistorico() {
        return new MultipleActionCustomColumn<FichaAcolhimento>() {

            @Override
            public void customizeColumn(FichaAcolhimento rowObject) {

                ReportActionLinkPanel acaoImprimir = addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<FichaAcolhimento>() {
                    @Override
                    public DataReport action(FichaAcolhimento modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoFichaAcolhimento(modelObject);
                    }
                });

                ModelActionLinkPanel acaoDetalhes = addAction(ActionType.CONSULTAR, rowObject, new IModelAction<FichaAcolhimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, FichaAcolhimento modelObject) throws ValidacaoException, DAOException {
                        viewDlgDetalhesFichaAcolhimento(target, modelObject);
                    }
                });
                acaoDetalhes.setTitleBundleKey("detalhes");
            }
        };
    }

    public void updateTable(AjaxRequestTarget target) {
        tblHistorico.populate(target);
        tblHistorico.update(target);
        tblPendentes.populate(target);
        tblPendentes.update(target);
    }

    public void viewDlgDetalhesFichaAcolhimento(AjaxRequestTarget target, FichaAcolhimento ficha) {
        if (dlgDetalhesFichaAcolhimento == null) {
            WindowUtil.addModal(target, ConsultaFichaAcolhimentoPanel.this, dlgDetalhesFichaAcolhimento = new DlgDetalhesFichaAcolhimento(WindowUtil.newModalId(ConsultaFichaAcolhimentoPanel.this)));
        }
        dlgDetalhesFichaAcolhimento.show(target, ficha);
    }

}
