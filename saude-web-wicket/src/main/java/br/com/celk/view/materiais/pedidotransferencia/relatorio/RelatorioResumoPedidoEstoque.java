package br.com.celk.view.materiais.pedidotransferencia.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioResumoPedidoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioResumoPedidoEstoque extends RelatorioPage<QueryRelatorioResumoPedidoEstoqueDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresaOrigem = new AutoCompleteConsultaEmpresa("empresas"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito("depositos"));
        form.add(getDropDownProdutosSemEstoque());
        form.add(getDropDownFormaApresentacao());

        autoCompleteConsultaEmpresaOrigem.setMultiplaSelecao(true);
        autoCompleteConsultaDeposito.setMultiplaSelecao(true);

    }

    @Override
    public Class getDTOParamClass() {
        return QueryRelatorioResumoPedidoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(QueryRelatorioResumoPedidoEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioResumoPedidoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoPedidoXEstoque");
    }

    public DropDown getDropDownProdutosSemEstoque() {
        DropDown dropDown = new DropDown("produtoSemEstoque");
        dropDown.addChoice(RepositoryComponentDefault.AMBOS, BundleManager.getString("ambos"));
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));

        return dropDown;
    }

    public DropDown getDropDownFormaApresentacao() {
        DropDown dropDown = new DropDown("formApresentacao");
        DropDownUtil.setEnumChoices(dropDown, QueryRelatorioResumoPedidoEstoqueDTOParam.FormApresentacao.values());

        return dropDown;
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_RO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
