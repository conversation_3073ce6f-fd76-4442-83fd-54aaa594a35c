package br.com.celk.view.cadsus.usuariocadsus.autocomplete;

import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.restricaocontainer.RestricaoContainerDominioUsuarioCadsus;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.settings.UsuarioCadsusAutoCompleteSettings;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.dominio.interfaces.dto.QueryConsultaDominioUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.DominioUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConfiguratorDominioUsuarioCadsus extends ConsultaConfigurator {

    @Override
    public void getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(DominioUsuarioCadsus.class);

        boolean parametroReferencia = false;
        try {
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage());
        }
        if(parametroReferencia){
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), UsuarioCadsus.PROP_REFERENCIA, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_REFERENCIA)));
        }else{
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), UsuarioCadsus.PROP_CODIGO, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)));
        }
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), UsuarioCadsus.PROP_NOME, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataNascimento"),UsuarioCadsus.PROP_DATA_NASCIMENTO, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("idade"), UsuarioCadsus.PROP_DESCRICAO_IDADE, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_IDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("sexo"), UsuarioCadsus.PROP_SEXO_FORMATADO, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome_mae"), UsuarioCadsus.PROP_NOME_MAE, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_MAE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), UsuarioCadsus.PROP_SITUACAO, VOUtils.montarPath(DominioUsuarioCadsus.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_SITUACAO)));
    }

    @Override
    public IRestricaoContainer getRestricaoContainerInstance(String id) {
        return new RestricaoContainerDominioUsuarioCadsus(id);
    }

    @Override
    public IPagerProvider getDataProviderInstance() {
        return new QueryPagerProvider<UsuarioCadsus, QueryConsultaDominioUsuarioCadsusDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryConsultaDominioUsuarioCadsusDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).consultaDominioUsuarioCadsus(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(UsuarioCadsus.PROP_NOME), true);
            }

            @Override
            public void customizeParam(QueryConsultaDominioUsuarioCadsusDTOParam param) {
                param.setCampoOrdenacao(getSort().getProperty());
                param.setTipoOrdenacao(getSort().isAscending() ? "asc" : "desc");
            }

            @Override
            public QueryConsultaDominioUsuarioCadsusDTOParam getLoadByIdParam(Serializable objectIdReference) {
                return null;
            }

            @Override
            public QueryConsultaDominioUsuarioCadsusDTOParam getSearchParam(String searchCriteria) {
                QueryConsultaDominioUsuarioCadsusDTOParam param = new QueryConsultaDominioUsuarioCadsusDTOParam();
                param.setKeyword(searchCriteria);
                return param;
            }
        };
    }

    @Override
    public Class getReferenceClass() {
        return UsuarioCadsus.class;
    }

    @Override
    public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
        return new UsuarioCadsusAutoCompleteSettings();
    }
}
