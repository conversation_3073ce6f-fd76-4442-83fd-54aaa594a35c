package br.com.celk.view.unidadesaude.tabelaprecoprocedimento;

import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.view.hospital.convenio.autocomplete.AutoCompleteConsultaConvenio;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaPrecoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR> laudecir
 */
@Private

public class CadastroTabelaPrecoProcedimentoStep1 extends BasePage {

    private CompoundPropertyModel<TabelaPrecoProcedimento> model;
    private TabelaPrecoProcedimento tabelaPrecoProcedimento;

    public CadastroTabelaPrecoProcedimentoStep1(TabelaPrecoProcedimento tabelaPrecoProcedimento) {
        this.tabelaPrecoProcedimento = tabelaPrecoProcedimento;
        init();
    }

    public CadastroTabelaPrecoProcedimentoStep1() {
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel<TabelaPrecoProcedimento>(new LoadableObjectModel<TabelaPrecoProcedimento>(TabelaPrecoProcedimento.class)));
        this.model.setObject(tabelaPrecoProcedimento == null ? tabelaPrecoProcedimento = new TabelaPrecoProcedimento() : tabelaPrecoProcedimento);

        TabelaPrecoProcedimento on = on(TabelaPrecoProcedimento.class);
        form.add(new AutoCompleteConsultaConvenio(path(on.getConvenio()), true));
        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar();
            }
        }));

        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroTabelaPrecoProcedimento");
    }

    private void avancar() {
        setResponsePage(new CadastroTabelaPrecoProcedimentoStep2(this.model.getObject()));
    }
}
