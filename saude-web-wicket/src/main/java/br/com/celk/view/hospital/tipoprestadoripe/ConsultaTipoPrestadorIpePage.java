package br.com.celk.view.hospital.tipoprestadoripe;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaTipoPrestadorIpePage extends ConsultaPage<TipoPrestadorIpe, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    public ConsultaTipoPrestadorIpePage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TipoPrestadorIpe proxy = on(TipoPrestadorIpe.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("codigoTipo"), proxy.getTipo()));
        columns.add(createSortableColumn(BundleManager.getString("descricao"), proxy.getDescricaoTipo()));
        columns.add(createSortableColumn(BundleManager.getString("tipoDocumento"), proxy.getTipoDocumento(), proxy.getTipoDocumentoDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<TipoPrestadorIpe>() {
            @Override
            public void customizeColumn(TipoPrestadorIpe rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TipoPrestadorIpe>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoPrestadorIpe modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoPrestadorIpePage(modelObject));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoPrestadorIpe>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoPrestadorIpe modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(modelObject);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TipoPrestadorIpe>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoPrestadorIpe modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoPrestadorIpePage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TipoPrestadorIpe.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(TipoPrestadorIpe.class).getProperties());
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(TipoPrestadorIpe.PROP_DESCRICAO_TIPO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(TipoPrestadorIpe.PROP_DESCRICAO_TIPO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoPrestadorIpePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoPrestadorIpe");
    }
}
