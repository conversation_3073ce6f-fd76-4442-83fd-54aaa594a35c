package br.com.celk.view.hospital.faturamento.dialogs.ipe;

import br.com.celk.component.ajaxcalllistener.ConditionListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.hospital.ipe.autocomplete.AutoCompleteConsultaHonorariosIpe;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import static ch.lambdaj.Lambda.on;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNovoLancamentoHonorariosIpe extends Panel {

    private CompoundPropertyModel<ItemContaPaciente> model;
    private AbstractAjaxButton btnFechar;
    private Form<ItemContaPaciente> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaHonorariosIpe autoCompleteConsultaHonorariosIpe;
    private HonorariosIpe honorarioIpe;
    private DateChooser dataChooser;
    private InputField txtQuantidadeDias;
    private InputField txtQuantidadePorDia;
    private DoubleField txtValor;
    private AtendimentoInformacao atendimentoInformacao;

    public PnlNovoLancamentoHonorariosIpe(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form<ItemContaPaciente>("form", model = new CompoundPropertyModel(new ItemContaPaciente()));
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        form.add(autoCompleteConsultaHonorariosIpe = new AutoCompleteConsultaHonorariosIpe("honorarioIpe", new PropertyModel(this, "honorarioIpe")));

        form.add(txtQuantidadeDias = new InputField(path(proxy.getQuantidadeDias())));
        txtQuantidadeDias.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularValor(target, honorarioIpe);
            }
            
            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= ''";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtQuantidadeDias.getMarkupId(), condition));
            }
        });
        
        form.add(txtQuantidadePorDia = new InputField(path(proxy.getQuantidadePorDia())));
        txtQuantidadePorDia.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularValor(target, honorarioIpe);
            }
            
            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                String condition = "!= ''";
                attributes.getAjaxCallListeners().add(new ConditionListenerLoading(txtQuantidadePorDia.getMarkupId(), condition));
            }
        });
        
        form.add(txtValor = new DoubleField(path(proxy.getPrecoUnitario())).setVMax(99999999D));
        form.add(dataChooser = new DateChooser(path(proxy.getDataLancamento())));
        dataChooser.setRequired(true);
        dataChooser.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        form.add(DropDownUtil.getIEnumDropDown(ItemContaPaciente.PROP_CARATER, ItemContaPaciente.Carater.values()));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaPrestador())));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO));
        autoCompleteConsultaEmpresa.setValidarTipoEstabelecimento(true);
        form.add(DropDownUtil.getIEnumDropDown(ItemContaPaciente.PROP_VIAS_ACESSO_IPE, ItemContaPaciente.ViasDeAcesso.values()));
        form.add(DropDownUtil.getIEnumDropDown(ItemContaPaciente.PROP_INDICADOR_AUXILIAR_IPE, ItemContaPaciente.IndicadorAuxiliarIpe.values()));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validaLancamento();
                model.getObject().setProcedimento(honorarioIpe.getProcedimento());
                
                onConfirmar(target, model.getObject());
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);
        
        autoCompleteConsultaHonorariosIpe.add(new ConsultaListener<HonorariosIpe>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, HonorariosIpe honorariosIpe) {
                calcularValor(target, honorariosIpe);
            }
        });

        autoCompleteConsultaHonorariosIpe.add(new RemoveListener<HonorariosIpe>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, HonorariosIpe honorariosIpe) {
                calcularValor(target, null);  
            }
        });

        setOutputMarkupId(true);

        add(form);
    }

    private void calcularValor(AjaxRequestTarget target, HonorariosIpe honorariosIpe){
        txtValor.limpar(target);
        Double calculoValor = null;
        
        if(honorariosIpe != null){
            calculoValor = Coalesce.asDouble(honorariosIpe.getValor());
        }
        
        if(calculoValor != null){
            Long quantidadeDias = model.getObject().getQuantidadeDias();
            
            if(Coalesce.asLong(quantidadeDias) > 0L){
                calculoValor = new Dinheiro(quantidadeDias).multiplicar(calculoValor).doubleValue();
            }
            
            Long quantidadePorDia = model.getObject().getQuantidadePorDia();
            
            if(Coalesce.asLong(quantidadePorDia) > 0L){
                calculoValor = new Dinheiro(quantidadePorDia).multiplicar(calculoValor).doubleValue();
            }
        }
        
        txtValor.setComponentValue(Coalesce.asDouble(calculoValor));;
        target.add(txtValor);
    }

    private void validaLancamento() throws ValidacaoException, DAOException {

        if (honorarioIpe == null) {
            throw new ValidacaoException(BundleManager.getString("informeHonorario"));
        }
        
        if (model.getObject().getQuantidadeDias() == null || Coalesce.asLong(model.getObject().getQuantidadeDias()) < 0L) {
            throw new ValidacaoException(BundleManager.getString("msgInformeQuantidadeDias"));
        }
        if (model.getObject().getQuantidadePorDia() == null || Coalesce.asLong(model.getObject().getQuantidadePorDia()) < 0L) {
            throw new ValidacaoException(BundleManager.getString("msgInformeQuantidadeOcorrenciasPorDia"));
        }
        if(Data.adjustRangeHour(model.getObject().getDataLancamento()).getDataInicial().before(Data.adjustRangeHour(atendimentoInformacao.getDataChegada()).getDataInicial())){
            throw new ValidacaoException(BundleManager.getString("competenciaNaoPodeSerMenorDataChegadaAtendimentoDataChegadaX", 
                    Data.formatar(atendimentoInformacao.getDataChegada())));
        }
        if (DataUtil.getDataAtual().before(model.getObject().getDataLancamento())) {
            throw new ValidacaoException(BundleManager.getString("dataLancamentoNaoMaiorDataAtual"));
        }
        if(Coalesce.asDouble(this.model.getObject().getPrecoUnitario()) > 99999999.9999D){
            throw new ValidacaoException(BundleManager.getString("msgValorUltrapassouLimiteFavorVerificar"));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        form.getModel().setObject(new ItemContaPaciente());
        target.add(form);
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, AtendimentoInformacao atendimentoInformacao) {
        this.model.setObject(itemContaPaciente);
        this.atendimentoInformacao = atendimentoInformacao;
        if (itemContaPaciente != null && itemContaPaciente.getProcedimento() != null) {
            List<HonorariosIpe> hiList = LoadManager.getInstance(HonorariosIpe.class)
                    .addProperties(new HQLProperties(HonorariosIpe.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, HonorariosIpe.PROP_PROCEDIMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(HonorariosIpe.PROP_PROCEDIMENTO, itemContaPaciente.getProcedimento()))
                    .start().getList();
            
            if(CollectionUtils.isNotNullEmpty(hiList)){
                honorarioIpe = hiList.get(0);
            }
        }
        update(target);
        target.focusComponent(autoCompleteConsultaHonorariosIpe.getTxtDescricao().getTextField());
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
