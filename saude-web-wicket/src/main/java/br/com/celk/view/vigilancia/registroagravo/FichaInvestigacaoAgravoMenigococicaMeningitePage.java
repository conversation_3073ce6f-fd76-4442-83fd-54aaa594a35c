package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoMenigococicaMeningiteDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMenigococicaMeningiteEnum;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoMenigococicaMeningites;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.getCidadeByCodigo;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoMenigococicaMeningitePage extends FichaInvestigacaoAgravoBasePage {

    private InvestigacaoAgravoMenigococicaMeningites investigacaoAgravo;

    private WebMarkupContainer containerDadosGerais;
    private DateChooser dataNotificacao;
    private DisabledInputField ufMunicipioNotificacao;
    private AutoCompleteConsultaCidade autoCompleteMunicipioNotificacao;
    private DisabledInputField ibgeMunicipioNotificacao;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeNotificadora;
    private DisabledInputField txtCodigoUnidadeNotificadora;
    private DateChooser dataPrimeiroSintomas;


    private WebMarkupContainer containerNotificacaoIndividual;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private DateChooser dataNascimento;
    private DisabledInputField txtIdade;
    private String tipoIdade;
    private DropDown ddSexo;
    private DropDown ddGestante;
    private DropDown ddRacaCor;
    private DropDown ddEscolaridade;
    private DisabledInputField txtNumCartaoSus;
    private DisabledInputField txtNomeMae;


    private WebMarkupContainer containerDadosResidencia;
    private DisabledInputField txtUf;
    private AutoCompleteConsultaCidade autoCompleteMunicipioResidencia;
    private DisabledInputField txtCodigoResidencia;
    private InputField txtDistrito;
    private InputField txtBairro;
    private InputField txtLogradouro;
    private InputField txtCodigoLogradouro;
    private InputField txtNumero;
    private InputField txtComplemento;
    private InputField txtGeoCampo1;
    private InputField txtGeoCampo2;
    private InputField txtPontoReferencia;
    private InputField txtCep;
    private InputField txtTelefone;
    private DropDown ddZona;
    private AutoCompleteConsultaPais autoCompletePais;


    private WebMarkupContainer containerDadosComplementaresDoCaso;
    private DateChooser dataInvestigacao;
    private AutoCompleteConsultaTabelaCbo autoCompleteTabelaCbo;
    private DropDown snVacinaPolissacaridicaAc;
    private InputField nrVacinaPolissacaridicaAc;
    private DateChooser dtVacinaPolissacaridicaAc;
    private DropDown snVacinaPolissacaridicaBc;
    private InputField nrVacinaPolissacaridicaBc;
    private DateChooser dtVacinaPolissacaridicaBc;
    private DropDown snVacinaConjugadaMeningo;
    private InputField nrVacinaConjugadaMeningo;
    private DateChooser dtVacinaConjugadaMeningo;
    private DropDown snVacinaBcg;
    private InputField nrVacinaBcg;
    private DateChooser dtVacinaBcg;
    private DropDown snVacinaTriplice;
    private InputField nrVacinaTriplice;
    private DateChooser dtVacinaTriplice;
    private DropDown snVacinaHemofilo;
    private InputField nrVacinaHemofilo;
    private DateChooser dtVacinaHemofilo;
    private DropDown snVacinaPneumococo;
    private InputField nrVacinaPneumococo;
    private DateChooser dtVacinaPneumococo;
    private DropDown snVacinaOutra;
    private InputField nmVacinaOutra;
    private InputField nrVacinaOutra;
    private DateChooser dtVacinaOutra;
    private DropDown snDoencaPreAidsHiv;
    private DropDown snDoencaPreOutraImuno;
    private DropDown snDoencaPreIra;
    private DropDown snDoencaPreTuberculose;
    private DropDown snDoencaPreTraumatismo;
    private DropDown snDoencaPreInfeccaoHospital;
    private InputField doencaPreOutro;
    private DropDown casoSuspeito;
    private InputField nmContato;
    private InputField telefoneContato;
    private InputField enderecoContato;
    private DropDown snCasoSecundario;


    private WebMarkupContainer containerDadosClinicos;
    private DropDown snSinalSintomaCafaleia;
    private DropDown snSinalSintomaVomitos;
    private DropDown snSinalSintomaRegidezNuca;
    private DropDown snSinalSintomaAbaulamentoFontanela;
    private DropDown snSinalSintomaPetequias;
    private DropDown snSinalSintomaFebre;
    private DropDown snSinalSintomaConvulsoes;
    private DropDown snSinalSintomaKernigBrudzinski;
    private DropDown snSinalSintomaComa;
    private InputField sinalSintomaOutro;


    private WebMarkupContainer containerAtendimento;
    private DropDown snHospitalizacao;
    private DateChooser dtInternacao;
    private InputField txtUfHospital;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeHospital;
    private DisabledInputField municipioHospital;
    private DisabledInputField ibgeHospital;
    private DisabledInputField codigoHospital;

    private WebMarkupContainer containerDadosDoLaboratorio;
    private DropDown snPuncaoLombar;
    private DateChooser dtPuncaoLombar;
    private DropDown aspectoLiquor;
    private InputField culturaLiquor;
    private InputField culturaLesaoPetequial;
    private InputField culturaSangueSoro;
    private InputField culturaEscarro;
    private InputField cieLiquor;
    private InputField cieSangueSoro;
    private InputField pcrLiquor;
    private InputField pcrLesaoPetequial;
    private InputField pcrSangueSoro;
    private InputField pcrEscarro;
    private InputField bacterioscopiaLiquor;
    private InputField bacterioscopiaLesaoPetequial;
    private InputField bacterioscopiaSoro;
    private InputField bacterioscopiaEscarro;
    private InputField aglutinacaoLatexLiquor;
    private InputField aglutinacaoLatexSangueSoro;
    private InputField  isolamentoViralLiquor;
    private InputField  isolamentoViralFezes;


    private WebMarkupContainer containerClassificacaoDoCasoEtiologia;
    private DropDown classificacaoCaso;
    private DropDown especificacaoCaso;
    private InputField especificacaoCasoMeningiteOutraBacteria;
    private InputField especificacaoCasoMeningiteAsseptica;
    private InputField especificacaoCasoMeningiteOutraEtiologia;
    private DropDown criterioConfirmacao;
    private InputField especificacaoSorogrupo;

    private WebMarkupContainer containerMedidasDeControle;
    private InputField nrComunicantes;
    private DropDown snQuimioprofilaxiaComunicantes;
    private DateChooser dtQuimioprofilaxiaComunicantes;
    private DropDown snDoencaRelacionadaTrabalho;

    private WebMarkupContainer containerConclusao;
    private DropDown evolucaoCaso;
    private DateChooser dtEvolucaoCaso;
    private DateChooser dtEncerramento;


    private WebMarkupContainer containerInformacoesComplementares;
    private InputField quimiocitologicoHemacias;
    private InputField quimiocitologicoLeucocitos;
    private InputField quimiocitologicoMonocitos;
    private InputField quimiocitologicoNeutrofilos;
    private InputField quimiocitologicoEosinofilos;
    private InputField quimiocitologicoLinfocitos;
    private InputField quimiocitologicoGlicose;
    private InputField quimiocitologicoProteinas;
    private InputField quimiocitologicoCloreto;
    private InputArea observacao;

    private WebMarkupContainer containerNotificador;
    private InputField txtCnesUnidadeSaudeNotificador;
    private InputField txtUnidadeSaudeNotificador;
    private InputField txtNomeNotificador;
    private InputField txtFuncaoNotificador;


    public FichaInvestigacaoAgravoMenigococicaMeningitePage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravo = InvestigacaoAgravoMenigococicaMeningites.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravo == null) {
            investigacaoAgravo = new InvestigacaoAgravoMenigococicaMeningites();
            investigacaoAgravo.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravo.setRegistroAgravo(getAgravo());
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravo)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoMenigococicaMeningiteDTO fichaDTO = new FichaInvestigacaoAgravoMenigococicaMeningiteDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoMenigococicaMeningites(investigacaoAgravo);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravo.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravo.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoMenigococicaMeningites proxy = on(InvestigacaoAgravoMenigococicaMeningites.class);

        getContainerRadioInformacoesComplementares().setVisible(false);
        getPnlDadosBasicosFichasInvestigacao().setVisible(false);

        criarDadosGerais(proxy);
        criarNotificacaoIndividual(proxy);
        criarDadosResidencia(proxy);
        criarDadosComplementaresDoCaso(proxy);
        criarDadosClinicos(proxy);
        criarAtendimento(proxy);
        criarDadosDoLaboratorio(proxy);
        criarClassificacaoDoCasoEtiologia(proxy);
        criarMedidasDeControle(proxy);
        criarConclusao(proxy);
        criarInformacoesComplementares(proxy);
        criarNotificador(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarDadosGerais();
        carregarNotificacaoIndividual();
        carregarDadosResidencia();
        carregarDadosComplementaresDoCaso();
        carregarAtendimento();
        carregarClassificacaoDoCasoEtiologia();
        carregarMedidasDeControle();
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        Date dataRegistro = investigacaoAgravo.getRegistroAgravo().getDataRegistro();
        Date dataInvestigacao = investigacaoAgravo.getDtInvestigacao();
        Date dataEncerramento = investigacaoAgravo.getDtEncerramento();

        if (dataInvestigacao != null && DataUtil.dataMaiorQueAtual(dataInvestigacao)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_investigacao_futuro"));
        }

        if (dataInvestigacao != null && dataRegistro != null && dataInvestigacao.before(dataRegistro)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_investigacao_data_registro"));

        }

        if (dataEncerramento != null && dataInvestigacao != null && dataEncerramento.before(dataInvestigacao)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_data_encerramento_data_investigacao"));
        }
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoMenigococicaMeningiteDTO dto = (FichaInvestigacaoAgravoMenigococicaMeningiteDTO) fichaDTO;
        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoMenigococicaMeningite(dto);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoMenigococicaMeningiteDTO fichaDTO = (FichaInvestigacaoAgravoMenigococicaMeningiteDTO) getFichaDTO();

        if (investigacaoAgravo.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravo.setUsuarioEncerramento(usuarioLogado);
        }
        investigacaoAgravo.setDtEncerramento(fichaDTO.getInvestigacaoAgravoMenigococicaMeningites().getDtEncerramento());

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    private void criarDadosGerais(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerDadosGerais = new WebMarkupContainer("containerDadosGerais");
        containerDadosGerais.setOutputMarkupPlaceholderTag(true);

        dataNotificacao = new DateChooser(path(proxy.getDtNotificacao()));
        dataNotificacao.setLabel(new Model<>(bundle("dataNotificacao")));
        dataNotificacao.setRequired(true).setEnabled(!isModoLeitura());
        dataNotificacao.addRequiredClass();
        dataNotificacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        autoCompleteMunicipioNotificacao = new AutoCompleteConsultaCidade(path(proxy.getMunicipioNotificacao()));
        autoCompleteMunicipioNotificacao.setOutputMarkupId(true);

        ufMunicipioNotificacao = new DisabledInputField(path(proxy.getMunicipioNotificacao().getEstado().getSigla()));
        ibgeMunicipioNotificacao = new DisabledInputField(path(proxy.getMunicipioNotificacao().getCodigo()));

        autoCompleteUnidadeNotificadora = new AutoCompleteConsultaEmpresa(path(proxy.getUnidadeNotificadora()));
        autoCompleteUnidadeNotificadora.setOutputMarkupId(true);

        txtCodigoUnidadeNotificadora = new DisabledInputField(path(proxy.getUnidadeNotificadora().getCnes()));

        dataPrimeiroSintomas = new DateChooser(path(proxy.getDtPrimeiraSintomas()));
        dataPrimeiroSintomas.setLabel(new Model<>(bundle("dataPrimeiroSintomas")));
        dataPrimeiroSintomas.setRequired(true).setEnabled(!isModoLeitura());
        dataPrimeiroSintomas.addRequiredClass();
        dataPrimeiroSintomas.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerDadosGerais.add(
                dataNotificacao, autoCompleteMunicipioNotificacao, ufMunicipioNotificacao, ibgeMunicipioNotificacao,
                autoCompleteUnidadeNotificadora, txtCodigoUnidadeNotificadora, dataPrimeiroSintomas
        );

        getContainerInformacoesComplementares().add(containerDadosGerais);
    }

    private void carregarDadosGerais() {
        if (investigacaoAgravo.getMunicipioNotificacao() == null) {
            autoCompleteMunicipioNotificacao.setComponentValue(ApplicationSession.get().getSessaoAplicacao().getEmpresa().getCidade());
            ibgeMunicipioNotificacao.setComponentValue(ApplicationSession.get().getSessaoAplicacao().getEmpresa().getCidade().getCodigo().toString());
            ufMunicipioNotificacao.setComponentValue(ApplicationSession.get().getSessaoAplicacao().getEmpresa().getCidade().getEstado().getSigla());
        }

        if (investigacaoAgravo.getUnidadeNotificadora() == null) {
            autoCompleteUnidadeNotificadora.setComponentValue(investigacaoAgravo.getRegistroAgravo().getEmpresa());
            txtCodigoUnidadeNotificadora.setComponentValue(investigacaoAgravo.getRegistroAgravo().getEmpresa().getCodigo().toString());
        }

        autoCompleteMunicipioNotificacao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {

                ibgeMunicipioNotificacao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = getCidadeByCodigo(cidade.getCodigo());
                }

                ufMunicipioNotificacao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(ibgeMunicipioNotificacao, ufMunicipioNotificacao);
            }
        });
        autoCompleteMunicipioNotificacao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                autoCompleteMunicipioNotificacao.limpar(target);
                ibgeMunicipioNotificacao.limpar(target);
                ufMunicipioNotificacao.limpar(target);
            }
        });

        autoCompleteUnidadeNotificadora.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                txtCodigoUnidadeNotificadora.setComponentValue(empresa.getCnes());
                target.add(txtCodigoUnidadeNotificadora);
            }
        });
        autoCompleteUnidadeNotificadora.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteUnidadeNotificadora.limpar(target);
                txtCodigoUnidadeNotificadora.limpar(target);
            }
        });

    }

    private void criarNotificacaoIndividual(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerNotificacaoIndividual = new WebMarkupContainer("containerNotificacaoIndividual");
        containerNotificacaoIndividual.setOutputMarkupId(true);

        autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getPaciente()));
        autoCompleteConsultaUsuarioCadsus.setOutputMarkupId(true);
        autoCompleteConsultaUsuarioCadsus.addAjaxUpdateValue();

        dataNascimento = new DateChooser(path(proxy.getPaciente().getDataNascimento()));
        dataNascimento.setEnabled(false);

        txtIdade = new DisabledInputField(path(proxy.getPaciente().getDescricaoIdadeSimples()));
        txtIdade.setEnabled(false);

        ddSexo = DropDownUtil.getSexoDropDown(path(proxy.getSexo()));
        ddGestante = DropDownUtil.getIEnumDropDown(path(proxy.getGestante()), InvestigacaoAgravoMenigococicaMeningiteEnum.GestanteEnum.values(), true);
        ddRacaCor = DropDownUtil.getIEnumDropDown(path(proxy.getRacaCor()), InvestigacaoAgravoMenigococicaMeningiteEnum.RacaEnum.values(), true,true);
        ddEscolaridade = DropDownUtil.getIEnumDropDown(path(proxy.getEscolaridade()), InvestigacaoAgravoMenigococicaMeningiteEnum.EscolaridadeEnum.values(), true);
        txtNumCartaoSus = new DisabledInputField(path(proxy.getPaciente().getCns()));
        txtNomeMae = new DisabledInputField(path(proxy.getPaciente().getNomeMae()));

        containerNotificacaoIndividual.add(autoCompleteConsultaUsuarioCadsus, dataNascimento, txtIdade,
                ddSexo, ddGestante, ddRacaCor, ddEscolaridade, txtNumCartaoSus, txtNomeMae);

        getContainerInformacoesComplementares().add(containerNotificacaoIndividual);
    }

    private void carregarNotificacaoIndividual() {
        dataNotificacao.setComponentValue(investigacaoAgravo.getRegistroAgravo().getDataRegistro());
        dataPrimeiroSintomas.setComponentValue(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas());

        if (investigacaoAgravo.getPaciente() == null) {
            autoCompleteConsultaUsuarioCadsus.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus());
            ddRacaCor.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getRaca().getCodigo());
            ddSexo.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getSexo());
            ddEscolaridade.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getEscolaridade());
        }

        try {
            tipoIdade = Data.getDescricaoTipoIdade(Coalesce.asDate(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getDataNascimento(),DataUtil.getDataAtual()), DataUtil.getDataAtual());
            if (tipoIdade == "Anos" || tipoIdade == "Ano")
                investigacaoAgravo.setIdadeTipo(InvestigacaoAgravoMenigococicaMeningiteEnum.TempoIdadeEnum.ANO.value());
            if (tipoIdade == "Dias" || tipoIdade == "Dia")
                investigacaoAgravo.setIdadeTipo(InvestigacaoAgravoMenigococicaMeningiteEnum.TempoIdadeEnum.DIA.value());
            if (tipoIdade == "Meses" || tipoIdade == "Mês")
                investigacaoAgravo.setIdadeTipo(InvestigacaoAgravoMenigococicaMeningiteEnum.TempoIdadeEnum.MES.value());
            else investigacaoAgravo.setIdadeTipo(InvestigacaoAgravoMenigococicaMeningiteEnum.TempoIdadeEnum.HORA.value());

        } catch (java.text.ParseException e) {
            throw new RuntimeException(e);
        }

        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                dataNascimento.setComponentValue(usuarioCadsus.getDataNascimento());
                txtIdade.setComponentValue(usuarioCadsus.getDescricaoIdadeSimples());
                ddSexo.setComponentValue(usuarioCadsus.getSexo());
                ddRacaCor.setComponentValue(usuarioCadsus.getRaca().getCodigo());
                ddEscolaridade.setComponentValue(usuarioCadsus.getNivelEscolaridade());
                txtNumCartaoSus.setComponentValue(usuarioCadsus.getCns());
                txtNomeMae.setComponentValue(usuarioCadsus.getNomeMae());

                target.add(dataNascimento, txtIdade, ddSexo, ddRacaCor, ddEscolaridade, txtNumCartaoSus, txtNomeMae, autoCompleteTabelaCbo);
            }
        });
        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                dataNascimento.limpar(target);
                txtIdade.limpar(target);
                ddSexo.limpar(target);
                ddRacaCor.limpar(target);
                ddEscolaridade.limpar(target);
                txtNumCartaoSus.limpar(target);
                txtNomeMae.limpar(target);
                autoCompleteTabelaCbo.limpar(target);
            }
        });
    }

    private void criarDadosResidencia(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerDadosResidencia = new WebMarkupContainer("containerDadosResidencia");
        containerDadosResidencia.setOutputMarkupId(true);

        txtUf = new DisabledInputField(path(proxy.getMunicipioResidencia().getEstado().getSigla()));
        autoCompleteMunicipioResidencia = new AutoCompleteConsultaCidade(path(proxy.getMunicipioResidencia()), true);
        autoCompleteMunicipioResidencia.setOutputMarkupId(true);
        txtCodigoResidencia = new DisabledInputField(path(proxy.getMunicipioResidencia().getCodigo()));
        txtDistrito = new InputField(path(proxy.getDistrito()));
        txtBairro = new InputField(path(proxy.getBairro()));
        txtLogradouro = new InputField(path(proxy.getLogradouro()));
        txtCodigoLogradouro = new InputField(path(proxy.getCodigoLogradouro()));
        txtNumero = new InputField(path(proxy.getNumero()));
        txtComplemento = new InputField(path(proxy.getComplemento()));
        txtGeoCampo1 = new InputField(path(proxy.getGeoCampo1()));
        txtGeoCampo2 = new InputField(path(proxy.getGeoCampo2()));
        txtPontoReferencia = new InputField(path(proxy.getPontoReferencia()));
        txtCep = new InputField(path(proxy.getCep()));
        txtTelefone = new InputField(path(proxy.getTelefone()));
        ddZona = DropDownUtil.getIEnumDropDown(path(proxy.getZona()), InvestigacaoAgravoMenigococicaMeningiteEnum.ZonaEnum.values(), true);
        autoCompletePais = new AutoCompleteConsultaPais(path(proxy.getPais()), false);
        autoCompletePais.setOutputMarkupId(true);

        containerDadosResidencia.add(txtUf, autoCompleteMunicipioResidencia, txtCodigoResidencia, txtDistrito, txtBairro, txtLogradouro,
                txtCodigoLogradouro, txtNumero, txtComplemento, txtGeoCampo1, txtGeoCampo2, txtPontoReferencia,
                txtCep, txtTelefone, ddZona, autoCompletePais
        );

        getContainerInformacoesComplementares().add(containerDadosResidencia);
    }

    private void carregarDadosResidencia() {
        if (investigacaoAgravo.getMunicipioResidencia() == null) {
            autoCompleteMunicipioResidencia.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getMunicipioResidencia());
            if (investigacaoAgravo != null &&
                    investigacaoAgravo.getRegistroAgravo() != null &&
                    investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus() != null &&
                    investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getMunicipioResidencia() != null &&
                    investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getMunicipioResidencia().getEstado() != null &&
                    investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getEnderecoUsuarioCadsus() != null) {

                txtUf.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getMunicipioResidencia().getEstado().getSigla());
                txtLogradouro.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getEnderecoUsuarioCadsus().getLogradouro());
                txtNumero.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getEnderecoUsuarioCadsus().getNumeroLogradouro());
                txtCep.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getEnderecoUsuarioCadsus().getCepFormatado());
                txtTelefone.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getTelefone());
            }
            autoCompletePais.setComponentValue(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getPaisNascimento());
        }
        autoCompletePais.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    txtUf.limpar(target);
                    autoCompleteMunicipioResidencia.limpar(target);
                    txtCep.limpar(target);
                }
            }
        });
        autoCompletePais.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteMunicipioResidencia.limpar(target);
                txtUf.limpar(target);
                txtCep.limpar(target);
            }
        });

        autoCompleteMunicipioResidencia.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                txtCodigoResidencia.setComponentValue(cidade.getCodigo().toString());
                txtUf.setComponentValue(cidade.getEstado().getSigla());
                target.add(txtCodigoResidencia, txtUf);
            }
        });
        autoCompleteMunicipioResidencia.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                txtCodigoResidencia.limpar(target);
                txtUf.limpar(target);
            }
        });
    }

    private void criarDadosComplementaresDoCaso(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerDadosComplementaresDoCaso  = new WebMarkupContainer("containerDadosComplementaresDoCaso");
        containerDadosComplementaresDoCaso.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDtInvestigacao()));
        dataInvestigacao.setLabel(new Model<>(bundle("dataInvestigacao")));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.addRequiredClass();
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        autoCompleteTabelaCbo = new AutoCompleteConsultaTabelaCbo(path(proxy.getOcupacaoCbo()));
        autoCompleteTabelaCbo.setOutputMarkupId(true);

        snVacinaPolissacaridicaAc = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaPolissacaridicaAc()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaPolissacaridicaAc = new InputField(path(proxy.getVacinaPolissacaridicaAcNumero()));
        dtVacinaPolissacaridicaAc = new DateChooser(path(proxy.getVacinaPolissacaridicaAcData()));

        snVacinaPolissacaridicaBc = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaPolissacaridicaBc()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaPolissacaridicaBc = new InputField(path(proxy.getVacinaPolissacaridicaBcNumero()));
        dtVacinaPolissacaridicaBc = new DateChooser(path(proxy.getVacinaPolissacaridicaBcData()));

        snVacinaConjugadaMeningo = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaConjugadaMeningo()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaConjugadaMeningo = new InputField(path(proxy.getVacinaConjugadaMeningoNumero()));
        dtVacinaConjugadaMeningo = new DateChooser(path(proxy.getVacinaConjugadaMeningoData()));

        snVacinaBcg = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaBcg()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaBcg = new InputField(path(proxy.getVacinaBcgNumero()));
        dtVacinaBcg = new DateChooser(path(proxy.getVacinaBcgData()));

        snVacinaTriplice = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaTriplice()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaTriplice = new InputField(path(proxy.getVacinaTripliceNumero()));
        dtVacinaTriplice = new DateChooser(path(proxy.getVacinaTripliceData()));

        snVacinaHemofilo = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaHemofilo()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaHemofilo = new InputField(path(proxy.getVacinaHemofiloNumero()));
        dtVacinaHemofilo = new DateChooser(path(proxy.getVacinaHemofiloData()));

        snVacinaPneumococo = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaPneumococo()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nrVacinaPneumococo = new InputField(path(proxy.getVacinaPneumococoNumero()));
        dtVacinaPneumococo = new DateChooser(path(proxy.getVacinaPneumococoData()));

        snVacinaOutra = DropDownUtil.getIEnumDropDown(path(proxy.getVacinaOutra()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        nmVacinaOutra = new InputField(path(proxy.getVacinaOutraNome()));
        nrVacinaOutra = new InputField(path(proxy.getVacinaOutraNumero()));
        dtVacinaOutra = new DateChooser(path(proxy.getVacinaOutraData()));

        snDoencaPreAidsHiv = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreAidsHiv()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snDoencaPreOutraImuno = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreOutrasImuno()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snDoencaPreIra = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreIra()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snDoencaPreTuberculose = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreTubercolose()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snDoencaPreTraumatismo = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreTraumatismo()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snDoencaPreInfeccaoHospital = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaPreInfeccaoHospital()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        doencaPreOutro = new InputField(path(proxy.getDoencaPreOutro()));

        casoSuspeito = DropDownUtil.getIEnumDropDown(path(proxy.getContatoCasoSuspeito()), InvestigacaoAgravoMenigococicaMeningiteEnum.ContatoSuspeitoConfirmadoEnum.values(), true);
        nmContato = new InputField(path(proxy.getContatoNome()));
        telefoneContato = new InputField(path(proxy.getContatoTelefone()));
        enderecoContato = new InputField(path(proxy.getContatoEndereco()));
        snCasoSecundario = DropDownUtil.getIEnumDropDown(path(proxy.getCasoSecundario()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);

        containerDadosComplementaresDoCaso.add(dataInvestigacao, autoCompleteTabelaCbo, snVacinaPolissacaridicaAc, nrVacinaPolissacaridicaAc, dtVacinaPolissacaridicaAc,
                snVacinaPolissacaridicaBc, nrVacinaPolissacaridicaBc, dtVacinaPolissacaridicaBc, snVacinaConjugadaMeningo, nrVacinaConjugadaMeningo, dtVacinaConjugadaMeningo,
                snVacinaBcg, nrVacinaBcg, dtVacinaBcg, snVacinaTriplice, nrVacinaTriplice, dtVacinaTriplice, snVacinaHemofilo, nrVacinaHemofilo, dtVacinaHemofilo,
                snVacinaPneumococo, nrVacinaPneumococo, dtVacinaPneumococo, snVacinaOutra, nmVacinaOutra, nrVacinaOutra, dtVacinaOutra, snDoencaPreAidsHiv,
                snDoencaPreOutraImuno, snDoencaPreIra, snDoencaPreTuberculose, snDoencaPreTraumatismo, snDoencaPreInfeccaoHospital, doencaPreOutro, casoSuspeito,
                nmContato, telefoneContato, enderecoContato, snCasoSecundario);

        getContainerInformacoesComplementares().add(containerDadosComplementaresDoCaso);

    }

    private void carregarDadosComplementaresDoCaso() {
        if (investigacaoAgravo.getOcupacaoCbo() != null) {
            autoCompleteTabelaCbo.setComponentValue(investigacaoAgravo.getOcupacaoCbo());
        }

        snVacinaPolissacaridicaAc.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaPolissacaridicaAc, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaPolissacaridicaAc.setEnabled(true);
                    dtVacinaPolissacaridicaAc.setEnabled(true);
                } else {
                    nrVacinaPolissacaridicaAc.setEnabled(false);
                    dtVacinaPolissacaridicaAc.setEnabled(false);
                }
                target.add(nrVacinaPolissacaridicaAc, dtVacinaPolissacaridicaAc);
            }
        });

        snVacinaPolissacaridicaBc.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaPolissacaridicaBc, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaPolissacaridicaBc.setEnabled(true);
                    dtVacinaPolissacaridicaBc.setEnabled(true);
                } else {
                    nrVacinaPolissacaridicaBc.setEnabled(false);
                    dtVacinaPolissacaridicaBc.setEnabled(false);
                }
                target.add(nrVacinaPolissacaridicaBc, dtVacinaPolissacaridicaBc);
            }
        });

        snVacinaConjugadaMeningo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaConjugadaMeningo, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaConjugadaMeningo.setEnabled(true);
                    dtVacinaConjugadaMeningo.setEnabled(true);
                } else {
                    nrVacinaConjugadaMeningo.setEnabled(false);
                    dtVacinaConjugadaMeningo.setEnabled(false);
                }
                target.add(nrVacinaConjugadaMeningo, dtVacinaConjugadaMeningo);
            }
        });

        snVacinaBcg.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaBcg, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaBcg.setEnabled(true);
                    dtVacinaBcg.setEnabled(true);
                } else {
                    nrVacinaBcg.setEnabled(false);
                    dtVacinaBcg.setEnabled(false);
                }
                target.add(nrVacinaBcg, dtVacinaBcg);
            }
        });

        snVacinaTriplice.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaTriplice, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaTriplice.setEnabled(true);
                    dtVacinaTriplice.setEnabled(true);
                } else {
                    nrVacinaTriplice.setEnabled(false);
                    dtVacinaTriplice.setEnabled(false);
                }
                target.add(nrVacinaTriplice, dtVacinaTriplice);
            }
        });

        snVacinaHemofilo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaHemofilo, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaHemofilo.setEnabled(true);
                    dtVacinaHemofilo.setEnabled(true);
                } else {
                    nrVacinaHemofilo.setEnabled(false);
                    dtVacinaHemofilo.setEnabled(false);
                }
                target.add(nrVacinaHemofilo, dtVacinaHemofilo);
            }
        });

        snVacinaPneumococo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaPneumococo, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nrVacinaPneumococo.setEnabled(true);
                    dtVacinaPneumococo.setEnabled(true);
                } else {
                    nrVacinaPneumococo.setEnabled(false);
                    dtVacinaPneumococo.setEnabled(false);
                }
                target.add(nrVacinaPneumococo, dtVacinaPneumococo);
            }
        });

        snVacinaOutra.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snVacinaOutra, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    nmVacinaOutra.setEnabled(true);
                    nrVacinaOutra.setEnabled(true);
                    dtVacinaOutra.setEnabled(true);
                } else {
                    nmVacinaOutra.setEnabled(false);
                    nrVacinaOutra.setEnabled(false);
                    dtVacinaOutra.setEnabled(false);
                }
                target.add(nmVacinaOutra, nrVacinaOutra, dtVacinaOutra);
            }
        });
    }

    private void criarDadosClinicos(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        snSinalSintomaCafaleia = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaCefaleia()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaVomitos = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaVomitos()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaRegidezNuca = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaRigidezNuca()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaAbaulamentoFontanela  = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaAbaulamentoFontanela()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaPetequias = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaPetequias()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaFebre = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaFebre()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaConvulsoes = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaConvulsoes()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaKernigBrudzinski = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaKernigBrudzinski()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        snSinalSintomaComa = DropDownUtil.getIEnumDropDown(path(proxy.getSinalSintomaComa()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        sinalSintomaOutro = new InputField(path(proxy.getSinalSintomaOutro()));

        containerDadosClinicos.add(snSinalSintomaCafaleia, snSinalSintomaVomitos, snSinalSintomaRegidezNuca, snSinalSintomaAbaulamentoFontanela, snSinalSintomaPetequias,
                snSinalSintomaFebre, snSinalSintomaConvulsoes, snSinalSintomaKernigBrudzinski, snSinalSintomaComa, sinalSintomaOutro);

        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void criarAtendimento(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerAtendimento = new WebMarkupContainer("containerAtendimento");
        containerAtendimento.setOutputMarkupId(true);

        snHospitalizacao = DropDownUtil.getIEnumDropDown(path(proxy.getHospitalizacao()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        dtInternacao = new DateChooser(path(proxy.getDtInternacao()));
        autoCompleteUnidadeHospital = new AutoCompleteConsultaEmpresa(path(proxy.getHospital()), false);
        autoCompleteUnidadeHospital.setOutputMarkupId(true);
        txtUfHospital = new InputField(path(proxy.getHospital().getCidade().getEstado().getSigla()));

        municipioHospital = new DisabledInputField(path(proxy.getHospital().getCidade().getDescricao()));
        ibgeHospital = new DisabledInputField((path(proxy.getHospital().getCidade().getCodigo())));
        codigoHospital = new DisabledInputField(path(proxy.getHospital().getCnes()));

        containerAtendimento.add(snHospitalizacao, dtInternacao, autoCompleteUnidadeHospital, txtUfHospital
                , municipioHospital, ibgeHospital, codigoHospital);

        getContainerInformacoesComplementares().add(containerAtendimento);
    }

    private void carregarAtendimento() {
        autoCompleteUnidadeHospital.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                empresa = VigilanciaHelper.loadCidadeEstadoHospital(empresa);

                txtUfHospital.setComponentValue(empresa.getCidade().getEstado().getSigla());
                municipioHospital.setComponentValue(empresa.getCidade().getDescricao());
                ibgeHospital.setComponentValue(empresa.getCidade().getCodigo());
                codigoHospital.setComponentValue(empresa.getCnes());
                target.add(txtUfHospital, municipioHospital, ibgeHospital, codigoHospital);
            }
        });


        autoCompleteUnidadeHospital.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                txtUfHospital.limpar(target);
                municipioHospital.limpar(target);
                ibgeHospital.limpar(target);
                codigoHospital.limpar(target);
            }
        });
    }

    private void criarDadosDoLaboratorio(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerDadosDoLaboratorio = new WebMarkupContainer("containerDadosDoLaboratorio");
        containerDadosDoLaboratorio.setOutputMarkupId(true);

        snPuncaoLombar = DropDownUtil.getIEnumDropDown(path(proxy.getPuncaoLombar()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        dtPuncaoLombar = new DateChooser(path(proxy.getPuncaoLombarData()));

        aspectoLiquor = DropDownUtil.getIEnumDropDown(path(proxy.getAspectoLiquor()), InvestigacaoAgravoMenigococicaMeningiteEnum.AspectoLiquorEnum.values(), true);

        culturaLiquor = new InputField(path(proxy.getCulturaLiquor()));
        culturaLesaoPetequial = new InputField(path(proxy.getCulturaLesaoPetequial()));
        culturaSangueSoro = new InputField(path(proxy.getCulturaSangueSoro()));
        culturaEscarro = new InputField(path(proxy.getCulturaEscarro()));
        cieLiquor = new InputField(path(proxy.getCieLiquor()));
        cieSangueSoro = new InputField(path(proxy.getCieSangueSoro()));
        pcrLiquor = new InputField(path(proxy.getPcrLiquor()));
        pcrLesaoPetequial = new InputField(path(proxy.getPcrLesaoPetequial()));
        pcrSangueSoro = new InputField(path(proxy.getPcrSangueSoro()));
        pcrEscarro = new InputField(path(proxy.getPcrEscarro()));
        bacterioscopiaLiquor = new InputField(path(proxy.getBacterioscopiaLiquor()));
        bacterioscopiaLesaoPetequial = new InputField(path(proxy.getBacterioscopiaLesaoPetequial()));
        bacterioscopiaSoro = new InputField(path(proxy.getBacterioscopiaSangueSoro()));
        bacterioscopiaEscarro  = new InputField(path(proxy.getBacterioscopiaEscarro()));
        aglutinacaoLatexLiquor = new InputField(path(proxy.getAglutinacaoLatexLiquor()));
        aglutinacaoLatexSangueSoro = new InputField(path(proxy.getAglutinacaoLatexSangueSoro()));
        isolamentoViralLiquor = new InputField(path(proxy.getIsolamentoViralLiquor()));
        isolamentoViralFezes = new InputField(path(proxy.getIsolamentoViralFezes()));

        containerDadosDoLaboratorio.add(snPuncaoLombar, dtPuncaoLombar, aspectoLiquor, culturaLiquor, culturaLesaoPetequial, culturaSangueSoro,
                culturaEscarro, cieLiquor, cieSangueSoro, pcrLiquor, pcrLesaoPetequial, pcrSangueSoro, pcrEscarro, bacterioscopiaLiquor,
                bacterioscopiaLesaoPetequial, bacterioscopiaSoro, bacterioscopiaEscarro, aglutinacaoLatexLiquor, aglutinacaoLatexSangueSoro, isolamentoViralLiquor, isolamentoViralFezes);

        getContainerInformacoesComplementares().add(containerDadosDoLaboratorio);

    }

    private void criarClassificacaoDoCasoEtiologia(InvestigacaoAgravoMenigococicaMeningites proxy){
        containerClassificacaoDoCasoEtiologia = new WebMarkupContainer("containerClassificacaoDoCasoEtiologia");
        containerClassificacaoDoCasoEtiologia.setOutputMarkupId(true);

        classificacaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoCaso()), InvestigacaoAgravoMenigococicaMeningiteEnum.ClassificacaoCasoEnum.values(), true);

        especificacaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEspecificacaoCaso()), InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.values(), true);
        especificacaoCasoMeningiteOutraBacteria = new InputField(path(proxy.getEspecificacaoCasoMeningiteOutraBacteria()));
        especificacaoCasoMeningiteAsseptica = new InputField(path(proxy.getEspecificacaoCasoMeningiteAsseptica()));
        especificacaoCasoMeningiteOutraEtiologia = new InputField(path(proxy.getEspecificacaoCasoMeningiteOutraEtiologia()));

        criterioConfirmacao = DropDownUtil.getIEnumDropDown(path(proxy.getCriterioConfirmacao()), InvestigacaoAgravoMenigococicaMeningiteEnum.CriterioConfirmacaoEnum.values(), true);
        especificacaoSorogrupo = new InputField(path(proxy.getEspecificacaoSorogrupo()));

        containerClassificacaoDoCasoEtiologia.add(classificacaoCaso, especificacaoCaso, especificacaoCasoMeningiteOutraBacteria, especificacaoCasoMeningiteAsseptica,
                especificacaoCasoMeningiteOutraEtiologia, criterioConfirmacao, especificacaoSorogrupo);

        getContainerInformacoesComplementares().add(containerClassificacaoDoCasoEtiologia);

    }

    private void carregarClassificacaoDoCasoEtiologia(){

        getForm().getModel().getObject();
        especificacaoCaso.setEnabled(!isModoLeitura() && InvestigacaoAgravoMenigococicaMeningiteEnum.ClassificacaoCasoEnum.CONFIRMADO.value().equals(investigacaoAgravo.getClassificacaoCaso()));
        especificacaoCasoMeningiteOutraBacteria.setEnabled(!isModoLeitura() && InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_OUTRA_BACTERIA.value().equals(investigacaoAgravo.getEspecificacaoCaso()));
        especificacaoCasoMeningiteAsseptica.setEnabled(!isModoLeitura() && InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_ASSEPTICA.value().equals(investigacaoAgravo.getEspecificacaoCaso()));
        especificacaoCasoMeningiteOutraEtiologia.setEnabled(!isModoLeitura() && InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_OUTRA_ETIOLOGIA.value().equals(investigacaoAgravo.getEspecificacaoCaso()));

        classificacaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                especificacaoCaso.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(classificacaoCaso, InvestigacaoAgravoMenigococicaMeningiteEnum.ClassificacaoCasoEnum.CONFIRMADO.value()));
                target.add(especificacaoCaso);
            }
        });

        especificacaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                especificacaoCasoMeningiteOutraBacteria.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(especificacaoCaso, InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_OUTRA_BACTERIA.value()));
                especificacaoCasoMeningiteAsseptica.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(especificacaoCaso, InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_ASSEPTICA.value()));
                especificacaoCasoMeningiteOutraEtiologia.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(especificacaoCaso, InvestigacaoAgravoMenigococicaMeningiteEnum.EspecificacaoCasoEnum.MENINGITE_OUTRA_ETIOLOGIA.value()));
                target.add(especificacaoCasoMeningiteOutraBacteria, especificacaoCasoMeningiteAsseptica, especificacaoCasoMeningiteOutraEtiologia);
            }
        });
    }


    private void criarMedidasDeControle(InvestigacaoAgravoMenigococicaMeningites proxy){
        containerMedidasDeControle = new WebMarkupContainer("containerMedidasDeControle");
        containerMedidasDeControle.setOutputMarkupId(true);

        nrComunicantes = new InputField(path(proxy.getNumeroComunicantes()));
        snQuimioprofilaxiaComunicantes = DropDownUtil.getIEnumDropDown(path(proxy.getQuimioprofilaxiaComunicantes()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);
        dtQuimioprofilaxiaComunicantes = new DateChooser(path(proxy.getQuimioprofilaxiaComunicantesData()));
        snDoencaRelacionadaTrabalho  = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaRelacionadaTrabalho()), InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.values(), true);

        containerMedidasDeControle.add(nrComunicantes, snQuimioprofilaxiaComunicantes, dtQuimioprofilaxiaComunicantes, snDoencaRelacionadaTrabalho);

        getContainerInformacoesComplementares().add(containerMedidasDeControle);

    }

    private void carregarMedidasDeControle(){
        snQuimioprofilaxiaComunicantes.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE){
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(snQuimioprofilaxiaComunicantes, InvestigacaoAgravoMenigococicaMeningiteEnum.SimNaoEnum.SIM.value())){
                    dtQuimioprofilaxiaComunicantes.setEnabled(true);
                } else {
                    dtQuimioprofilaxiaComunicantes.setEnabled(false);
                }
                target.add(dtQuimioprofilaxiaComunicantes);
            }
        });
    }

    private void criarConclusao(InvestigacaoAgravoMenigococicaMeningites proxy){
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        evolucaoCaso  = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoMenigococicaMeningiteEnum.EvolucaoCasoEnum.values(), true);
        dtEvolucaoCaso = new DateChooser(path(proxy.getDtEvolucao()));
        dtEncerramento = new DateChooser(path(proxy.getDtEncerramento()));

        containerConclusao.add(evolucaoCaso, dtEvolucaoCaso, dtEncerramento);

        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void criarInformacoesComplementares(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerInformacoesComplementares = new WebMarkupContainer("containerInformacoesComplementares");
        containerInformacoesComplementares.setOutputMarkupId(true);

        quimiocitologicoHemacias = new InputField(path(proxy.getQuimiocitologicoHemacias()));
        quimiocitologicoLeucocitos = new InputField(path(proxy.getQuimiocitologicoLeucocitos()));
        quimiocitologicoMonocitos = new InputField(path(proxy.getQuimiocitologicoMonocitos()));
        quimiocitologicoNeutrofilos = new InputField(path(proxy.getQuimiocitologicoNeutrofilos()));
        quimiocitologicoEosinofilos = new InputField(path(proxy.getQuimiocitologicoEosinofilos()));
        quimiocitologicoLinfocitos = new InputField(path(proxy.getQuimiocitologicoLinfocitos()));
        quimiocitologicoGlicose = new InputField(path(proxy.getQuimiocitologicoGlicose()));
        quimiocitologicoProteinas = new InputField(path(proxy.getQuimiocitologicoProteinas()));
        quimiocitologicoCloreto = new InputField(path(proxy.getQuimiocitologicoCloreto()));

        observacao = new InputArea(path(proxy.getObservacao()));


        containerInformacoesComplementares.add(quimiocitologicoHemacias, quimiocitologicoLeucocitos, quimiocitologicoMonocitos, quimiocitologicoNeutrofilos,
                quimiocitologicoEosinofilos, quimiocitologicoLinfocitos, quimiocitologicoGlicose, quimiocitologicoProteinas, quimiocitologicoCloreto, observacao);

        getContainerInformacoesComplementares().add(containerInformacoesComplementares);
    }

    private void criarNotificador(InvestigacaoAgravoMenigococicaMeningites proxy) {
        containerNotificador = new WebMarkupContainer("containerNotificador");
        containerNotificador.setOutputMarkupId(true);

        txtUnidadeSaudeNotificador = new InputField(path(proxy.getUnidadeSaudeNotificador()));
        txtCnesUnidadeSaudeNotificador = new InputField(path(proxy.getCnesUnidadeSaudeNotificador()));

        txtNomeNotificador = new InputField(path(proxy.getNomeNotificador()));
        txtFuncaoNotificador = new InputField(path(proxy.getFuncaoNotificador()));

        containerNotificador.add(txtUnidadeSaudeNotificador, txtCnesUnidadeSaudeNotificador, txtNomeNotificador, txtFuncaoNotificador);

        getContainerInformacoesComplementares().add(containerNotificador);
    }

}
