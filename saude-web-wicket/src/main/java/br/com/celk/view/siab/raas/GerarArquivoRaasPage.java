package br.com.celk.view.siab.raas;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxDownload;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.siab.raas.customcolumn.StatusRaasProcessoColumnPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.MensagemValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.RaasProcesso;
import br.com.ksisolucoes.vo.atendimento.raas.RaasConfiguracao;
import br.com.ksisolucoes.vo.atendimento.raas.RaasUnidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class GerarArquivoRaasPage extends BasePage implements IAsyncProcessNotification {

    private Date competencia;
    private List<Long> situacao;
    private Empresa empresa;
    private DlgGerarArquivoRaas dlgGerarArquivoRaas;
    private AjaxDownload ajaxDownload;
    private PageableTable table;
    private ProcurarButton btnProcurar;
    private MesAnoField txtCompetencia;
    private DropDown dropDownSituacao;
    private RaasConfiguracao raasConfiguracao;
    private String extensao = "txt";

    public GerarArquivoRaasPage() {
        super();
        init();
        getRaasConfiguracao();
    }

    private void init() {
        Form form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtCompetencia = new MesAnoField("competencia"));
        form.add(getDropDownSituacao("situacao"));
        form.add(getDropDownRaasUnidade("empresa"));

        form.add(table = new PageableTable("table", getColumns(), getPagerProviderInstance()));
        table.populate();

        form.add(btnProcurar = new ProcurarButton("btnProcurar", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(new AbstractAjaxButton("btnGerarArquivo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgGerarArquivoRaas.show(target);
            }
        });

        form.add(new ProcurarButton("btnAtualizarRegistros", table) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        });

        form.add(ajaxDownload = new AjaxDownload());

        form.add(new AbstractAjaxButton("btnConfiguracoes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConfiguracaoRAASPage());
            }
        });

        add(form);

        addModal(dlgGerarArquivoRaas = new DlgGerarArquivoRaas(newModalId()) {
            @Override
            public void atualizarTabela(AjaxRequestTarget target) {
                table.update(target);
            }
        });

        btnProcurar.procurar();
    }

    private DropDown getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown(id);
            dropDownSituacao.addChoice(
                    Arrays.asList(
                            RaasProcesso.Status.STATUS_ARQUIVO_GERADO.value(),
                            RaasProcesso.Status.STATUS_ERRO.value(),
                            RaasProcesso.Status.STATUS_GERANDO_ARQUIVO.value(),
                            RaasProcesso.Status.STATUS_GERANDO_RAAS.value(),
                            RaasProcesso.Status.STATUS_RAAS_GERADO.value(),
                            RaasProcesso.Status.STATUS_SEM_REGISTROS.value()
                    ), bundle("todos"));
            dropDownSituacao.addChoice(Arrays.asList(RaasProcesso.Status.STATUS_ARQUIVO_GERADO.value()), bundle("arquivoGerado"));
            dropDownSituacao.addChoice(Arrays.asList(RaasProcesso.Status.STATUS_CANCELADO.value()), bundle("cancelado"));
        }

        return dropDownSituacao;
    }

    private DropDown getDropDownRaasUnidade(String id) {
        DropDown dropDown = new DropDown<Empresa>(id);
        List<RaasUnidade> raasUnidadeList = LoadManager.getInstance(RaasUnidade.class)
                .addProperties(new HQLProperties(RaasUnidade.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, RaasUnidade.PROP_EMPRESA).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(RaasUnidade.PROP_EMPRESA, Empresa.PROP_DESCRICAO)))
                .start().getList();

        dropDown.addChoice(null, BundleManager.getString("todas"));
        for (RaasUnidade raasUnidade : raasUnidadeList) {
            dropDown.addChoice(raasUnidade.getEmpresa(), raasUnidade.getEmpresa().getDescricao());
        }

        return dropDown;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        RaasProcesso proxy = on(RaasProcesso.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("mes"), proxy.getDescricaoMes()));
        columns.add(createColumn(bundle("ano"), proxy.getAno()));
        columns.add(createColumn(bundle("dataGeracao"), proxy.getDataGeracao()));
        columns.add(createColumn(bundle("empresa"), proxy.getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("folhas"), proxy.getTotalFolha()));
        columns.add(getCustomColumnStatus());

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<RaasProcesso>() {
            @Override
            public void customizeColumn(RaasProcesso rowObject) {

                addAction(ActionType.BAIXA, rowObject, new IModelAction<RaasProcesso>() {
                    @Override
                    public void action(AjaxRequestTarget target, RaasProcesso raasProcesso) throws ValidacaoException, DAOException {
                        downloadArquivo(target, raasProcesso);
                    }
                }).setTitleBundleKey("fazerDownloadArquivo")
                        .setEnabled(RaasProcesso.Status.STATUS_ARQUIVO_GERADO.value().equals(rowObject.getStatus()));

                addAction(ActionType.CONSULTAR, rowObject, new IReportAction<RaasProcesso>() {
                    @Override
                    public DataReport action(RaasProcesso raasProcesso) throws ReportException {
                        MensagemValidacao mv = new MensagemValidacao(newModalId());
                        mv.setMensagem(raasProcesso.getMensagemErro());
                        return BOFactoryWicket.getBO(BasicoReportFacade.class).relatorioValidacao(bundle("geracaoRaas"), Arrays.asList(mv));
                    }
                }).setTitleBundleKey("visualizarMensagemErro")
                        .setEnabled(RaasProcesso.Status.STATUS_ERRO.value().equals(rowObject.getStatus()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<RaasProcesso>() {
                    @Override
                    public void action(AjaxRequestTarget target, RaasProcesso raasProcesso) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AtendimentoGeralFacade.class).cancelarRaasProcesso(raasProcesso);
                        table.update(target);
                    }
                }).setQuestionDialogBundleKey("cancelar_producao_competencia")
                        .setTitleBundleKey("excluirArquivo")
                        .setEnabled(RaasProcesso.Status.STATUS_ARQUIVO_GERADO.value().equals(rowObject.getStatus())
                                || RaasProcesso.Status.STATUS_ERRO.value().equals(rowObject.getStatus())
                                || RaasProcesso.Status.STATUS_SEM_REGISTROS.value().equals(rowObject.getStatus())
                                || RaasProcesso.Status.STATUS_RAAS_GERADO.value().equals(rowObject.getStatus()));
            }
        };
    }

    private void verificaMsgErro(Long codigoRaasProcess) throws ValidacaoException {
        RaasProcesso proxy = on(RaasProcesso.class);

        RaasProcesso raasProcesso = (RaasProcesso) LoadManager.getInstance(RaasProcesso.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getMensagemErro()))
                .setId(codigoRaasProcess)
                .start().getVO();

        if (raasProcesso.getMensagemErro() != null) {
            throw new ValidacaoException(raasProcesso.getMensagemErro());
        }
    }

    private void downloadArquivo(AjaxRequestTarget target, RaasProcesso raasProcesso) throws DAOException, ValidacaoException {
        RaasProcesso rp = BOFactoryWicket.getBO(AtendimentoGeralFacade.class).downloadGeracaoRaas(raasProcesso);
        carregarArquivo(target, rp);
    }

    private void carregarArquivo(AjaxRequestTarget target, RaasProcesso rp) throws ValidacaoException, DAOException {
        try {
            File f = File.createTempFile(getNomeArquivo(rp), "." + extensao);
            FileUtils.buscarArquivoFtp(rp.getPath(), f.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
            ajaxDownload.initiate(target, getNomeArquivo(rp) + "." + extensao, resourceStream);
        } catch (IOException ex) {
            throw new DAOException(ex.getMessage());
        }
    }

    private void getRaasConfiguracao() {
        raasConfiguracao = LoadManager.getInstance(RaasConfiguracao.class)
                .addProperty(RaasConfiguracao.PROP_CNPJ_EMPRESA)
                .start().getVO();
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<RaasProcesso>() {
            @Override
            public Component getComponent(String componentId, RaasProcesso rowObject) {
                return new StatusRaasProcessoColumnPanel(componentId, rowObject);
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return RaasProcesso.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(RaasProcesso.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(RaasProcesso.PROP_ASYNC_PROCESS, AsyncProcess.PROP_CODIGO),
                                VOUtils.montarPath(RaasProcesso.PROP_ASYNC_PROCESS, AsyncProcess.PROP_STATUS)
                        }
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(RaasProcesso.PROP_DATA_GERACAO, false);
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (competencia != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(competencia);

            Long mes = (long) c.get(Calendar.MONTH) + 1;
            Long ano = (long) c.get(Calendar.YEAR);

            parameters.add(new QueryCustom.QueryCustomParameter(RaasProcesso.PROP_MES, mes));
            parameters.add(new QueryCustom.QueryCustomParameter(RaasProcesso.PROP_ANO, ano));
        }

        if (situacao == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(RaasProcesso.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, RaasProcesso.Status.STATUS_CANCELADO.value()));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(RaasProcesso.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, situacao));
        }
        if(empresa != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(RaasProcesso.PROP_EMPRESA, empresa));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("gerarArquivoRaas");
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        dropDownSituacao.limpar(target);
        txtCompetencia.limpar(target);

        btnProcurar.procurar();
        table.populate();
        table.update(target);
    }

    private String getNomeArquivo(RaasProcesso raasProcesso) {
        String prefix = "AA";
        String cnpjEmpresa = raasConfiguracao.getCnpjEmpresa().replaceAll("[./-]", "");
        String mes = Data.getDescricaoMesAbv(Long.valueOf(raasProcesso.getMes()).intValue() - 1).toUpperCase();

        return new StringBuilder(prefix).append(cnpjEmpresa).append(".").append(mes).toString();
    }
}
