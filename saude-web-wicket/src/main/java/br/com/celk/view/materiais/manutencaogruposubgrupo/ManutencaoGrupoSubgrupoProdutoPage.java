package br.com.celk.view.materiais.manutencaogruposubgrupo;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.manutencaogruposubgrupo.dialog.DlgAlterarGrupoSubGrupo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ManutencaoGrupoSubgrupoProdutoPage extends BasePage {

    private String descricao;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private String referencia;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private PageableTable<Produto> table;

    private DlgAlterarGrupoSubGrupo dlgAlterarGrupoSubGrupo;

    public ManutencaoGrupoSubgrupoProdutoPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new UpperField("referencia"));
        form.add(new InputField("descricao"));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());

        form.add(table = new PageableTable("table", getColumns(), getPagerProvider()));

        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", table) {
            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                return ManutencaoGrupoSubgrupoProdutoPage.this.getParameters();
            }
        });

        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        Produto proxy = on(Produto.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("referencia"), proxy.getReferencia()));
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createSortableColumn(bundle("grupo"), proxy.getSubGrupo().getRoGrupoProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("subGrupo"), proxy.getSubGrupo().getDescricao()));
        columns.add(createSortableColumn(bundle("unidade"), proxy.getUnidade().getDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<Produto>() {
            @Override
            public void customizeColumn(Produto produto) {
                addAction(ActionType.CONSULTAR, produto, new IModelAction<Produto>() {
                    @Override
                    public void action(AjaxRequestTarget target, Produto produto) throws ValidacaoException, DAOException {
                        viewDlgAlterarGrupoProduto(target, produto);
                    }
                }).setTitleBundleKey("alterarGrupoSubgrupo").setIcon(Icon.DOC_EDIT);
            }
        };
    }

    private void viewDlgAlterarGrupoProduto(AjaxRequestTarget target, Produto produto) {
        if (dlgAlterarGrupoSubGrupo == null) {
            addModal(target, dlgAlterarGrupoSubGrupo = new DlgAlterarGrupoSubGrupo(newModalId()) {
                @Override
                public void updateTable(AjaxRequestTarget target) {
                    table.update(target);
                }
            });
        }

        dlgAlterarGrupoSubGrupo.show(target, produto);
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");

            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }

                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    public IPagerProvider getPagerProvider() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return Produto.class;
            }

            @Override
            public String[] getProperties() {
                Produto proxy = on(Produto.class);

                return VOUtils.mergeProperties(new HQLProperties(Produto.class).getProperties(),
                        new String[]{
                            path(proxy.getSubGrupo().getId().getCodigo()),
                            path(proxy.getSubGrupo().getId().getCodigoGrupoProduto()),
                            path(proxy.getSubGrupo().getRoGrupoProduto().getCodigo()),
                            path(proxy.getSubGrupo().getRoGrupoProduto().getDescricao())
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Produto.PROP_DESCRICAO, true);
            }
        };
    }

    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        Produto proxy = on(Produto.class);

        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getReferencia()), BuilderQueryCustom.QueryParameter.ILIKE, referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getDescricao()), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getSubGrupo()), BuilderQueryCustom.QueryParameter.IGUAL, subGrupo));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getSubGrupo().getRoGrupoProduto()), BuilderQueryCustom.QueryParameter.IGUAL, grupoProduto));

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("manutencaoGrupoSubgrupoProduto");
    }
}
