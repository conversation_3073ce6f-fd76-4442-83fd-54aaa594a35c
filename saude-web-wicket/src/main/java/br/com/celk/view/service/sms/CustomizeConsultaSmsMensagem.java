package br.com.celk.view.service.sms;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;

/**
 * <AUTHOR>
 * Criado em: Nov 20, 2013
 */
public class CustomizeConsultaSmsMensagem extends CustomizeConsultaAdapter {

    @Override
    public Class getClassConsulta() {
        return SmsMensagem.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(SmsMensagem.class).getProperties(),
                new HQLProperties(UsuarioCadsus.class, SmsMensagem.PROP_USUARIO_CADSUS).getProperties());
    }
    
}
