package br.com.celk.view.vigilancia.externo.view.servicos.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.celk.view.vigilancia.externo.view.servicos.*;
import br.com.celk.view.vigilancia.externo.view.servicos.declaratorio.RequerimentoHabiteseDeclaratorioExternoPage;
import br.com.celk.view.vigilancia.externo.view.servicos.declaratorio.RequerimentoHidrossanitarioDeclaratorioExternoPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlEscolherTipoProjetoExterno extends Panel {

    private Form form;
    private AbstractAjaxButton btnFechar;
    private TipoSolicitacao tipoSolicitacaoSelecionado;

    public PnlEscolherTipoProjetoExterno(String id, boolean possuiPermissaoPBA, boolean possuiPermissaoVLTPBA, boolean possuiPermissaoAPH, boolean possuiPermissaoVHS, boolean possuiPermissaoAPHD, boolean possuiPermissaoHD, boolean possuiPermissaoPAS) {
        super(id);
        init(possuiPermissaoPBA, possuiPermissaoVLTPBA, possuiPermissaoAPH, possuiPermissaoVHS, possuiPermissaoAPHD, possuiPermissaoHD, possuiPermissaoPAS);
    }

    private void init(
            boolean possuiPermissaoPBA,
            boolean possuiPermissaoVLTPBA,
            boolean possuiPermissaoAPH,
            boolean possuiPermissaoVHS,
            boolean possuiPermissaoAPHD,
            boolean possuiPermissaoHD,
            boolean possuiPermissaoPAS
    ) {

        setOutputMarkupId(true);

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnPBA") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value());
                setResponsePage(new RequerimentoAnaliseProjetosExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoPBA));

        form.add(new AbstractAjaxButton("btnVLTPBA") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value());
                setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoVLTPBA));



        form.add(new AbstractAjaxButton("btnAPHD") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value());
                setResponsePage(new RequerimentoHidrossanitarioDeclaratorioExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAPHD));

        form.add(new AbstractAjaxButton("btnHD") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value());
                setResponsePage(new RequerimentoHabiteseDeclaratorioExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoHD));

        form.add(new AbstractAjaxButton("btnVHS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value());
                setResponsePage(new RequerimentoHabiteseExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoVHS));

        form.add(new AbstractAjaxButton("btnAPH") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value());
                setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAPH));

        form.add(new AbstractAjaxButton("btnPAS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value());
                setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioExternoPage(tipoSolicitacaoSelecionado, VigilanciaHomePage.class));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoPAS));


        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacaoSelecionado = tipoSolicitacao;
    }

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {
    }
}
