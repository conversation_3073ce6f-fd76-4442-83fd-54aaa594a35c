package br.com.celk.view.atendimento.recepcao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.ksisolucoes.agendamento.exame.dto.ConfirmacaoChegadaAihDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.IResource;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoChegadaAih extends Panel {

    private Form form;
    private ConfirmacaoChegadaAihDTO dto;
    private Image imagem;
    private InputArea txaOcorrencia;

    private AbstractAjaxButton btnConfirmar;

    public PnlConfirmacaoChegadaAih(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(new ConfirmacaoChegadaAihDTO()));
        setOutputMarkupId(true);

        form.add(imagem = new Image("imgAvatar", ""));
        imagem.setOutputMarkupId(true);

        ConfirmacaoChegadaAihDTO proxy = Lambda.on(ConfirmacaoChegadaAihDTO.class);

        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(new DisabledInputField(path(proxy.getQuartoInternacao().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getLeitoQuarto().getDescricao())));
        form.add(txaOcorrencia = new InputArea<String>(path(proxy.getDescricaoOcorrencia())));

        btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, dto);
            }
        };
        form.add(btnConfirmar);

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }



    public abstract void onConfirmar(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setDTO(AjaxRequestTarget target, ConfirmacaoChegadaAihDTO dto) throws DAOException {
        this.dto = dto;
        form.setModelObject(new CompoundPropertyModel(this.dto));
    }

    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }

}