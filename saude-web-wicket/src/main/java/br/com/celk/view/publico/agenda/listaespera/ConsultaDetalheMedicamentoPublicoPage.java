package br.com.celk.view.publico.agenda.listaespera;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaDetalheMedicamentoPublicoDTO;
import br.com.celk.view.publico.template.base.BasePagePublico;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ConsultaDetalheMedicamentoPublicoPage extends BasePagePublico {

    private Form<String> form;
    private PageableTable tblConsultaMedicamento;
    private QueryPagerProvider<ConsultaDetalheMedicamentoPublicoDTO, String> dataProvider;
    private String codigoDoProduto;
    private String descricaoMedicamento;
    private Label lblDescricaoMedicamento;

    private WebMarkupContainer notificationMessage;

    public ConsultaDetalheMedicamentoPublicoPage(String codigoDoProduto, String descricaoMedicamento) {
        this.codigoDoProduto = codigoDoProduto;
        this.descricaoMedicamento = descricaoMedicamento;
        init();
    }

    protected void init() {

        add(notificationMessage = new WebMarkupContainer("notificationMessage"));
        notificationMessage.setOutputMarkupId(true);

        getForm().add(lblDescricaoMedicamento = new Label("descricaoMedicamento", Bundle.getStringApplication("descricaoMedicamento", codigoDoProduto, descricaoMedicamento)));
        tblConsultaMedicamento = new PageableTable("tableDetalhesMedicamento", getColumns(), getDataProvider());
        tblConsultaMedicamento.populate();
        form.add(tblConsultaMedicamento);
        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaMedicamentoPublicoPage());
            }
        }.setDefaultFormProcessing(false));
        add(form);
    }

    private Form<String> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(codigoDoProduto));
        }
        return this.form;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ConsultaDetalheMedicamentoPublicoDTO proxy = on(ConsultaDetalheMedicamentoPublicoDTO.class);

        columns.add(createColumn(bundle("saldoDisponivel"), proxy.getSaldoDisponivelFormatado()));
        columns.add(createColumn(bundle("estabelecimento"), proxy.getDescricao()));
        columns.add(createColumn(bundle("telefone"), proxy.getTelefone()));
        columns.add(createColumn(bundle("endereco"), proxy.getRua()));
        columns.add(createColumn(bundle("numero"), proxy.getNumero()));
        columns.add(createColumn(bundle("complemento"), proxy.getComplemento()));
        columns.add(createColumn(bundle("bairro"), proxy.getBairro()));

        return columns;
    }

    private QueryPagerProvider getDataProvider() {
        if (dataProvider == null) {
            dataProvider = new QueryPagerProvider<ConsultaDetalheMedicamentoPublicoDTO, String>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<String> dataPaging) throws DAOException, ValidacaoException {
                    dataPaging.setParam(codigoDoProduto);
                    return BOFactoryWicket.getBO(EsusFacade.class).consultarDetalhesListaPublicaMedicamento(dataPaging);
                }
            };
        }
        return dataProvider;
    }

    @Override
    protected Image carregarLogo() {
        Image logoSistemaListaPublica = null;
        try {
            File logoSistemaTelaLoginFile = LogoHelper.getLogoListaPublica();
            if (logoSistemaTelaLoginFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaTelaLoginFile);
                logoSistemaListaPublica = new NonCachingImage("gemIco", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } finally {
            if (logoSistemaListaPublica == null) {
                logoSistemaListaPublica = new Image("gemIco", Resources.Images.CELK_SAUDE_LIGHT.resourceReference());
            }
        }
        return logoSistemaListaPublica;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaMedicamentos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.removeByClass("close")));
    }
}
