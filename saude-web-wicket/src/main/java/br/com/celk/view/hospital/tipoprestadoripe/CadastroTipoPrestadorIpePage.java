package br.com.celk.view.hospital.tipoprestadoripe;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoPrestadorIpePage extends CadastroPage<TipoPrestadorIpe> {

    private RadioButtonGroup radioGroupTipoDocumento;
    
    private CheckBoxLongValue checkConsultaMedica;
    private CheckBoxLongValue checkAtenidmentoComplementar;
    private CheckBoxLongValue checkProntoAtenidmento;
    private CheckBoxLongValue checkContaHospitalar;
    private CheckBoxLongValue checkContaAmbulatorial;
    private RequiredInputField tipo;
    private RequiredInputField descricaoTipo;

    public CadastroTipoPrestadorIpePage(TipoPrestadorIpe object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoPrestadorIpePage(TipoPrestadorIpe object) {
        this(object, false);
    }

    public CadastroTipoPrestadorIpePage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(tipo = new RequiredInputField<String>(TipoPrestadorIpe.PROP_TIPO));
        tipo.setLabel(new Model(BundleManager.getString("codigoTipo")));
        form.add(descricaoTipo = new RequiredInputField<String>(TipoPrestadorIpe.PROP_DESCRICAO_TIPO));
        descricaoTipo.setLabel(new Model(BundleManager.getString("descricao")));

        form.add(radioGroupTipoDocumento = new RadioButtonGroup(TipoPrestadorIpe.PROP_TIPO_DOCUMENTO));
        radioGroupTipoDocumento.setRequired(true);
        radioGroupTipoDocumento.setLabel(new Model(BundleManager.getString("tipoDocumento")));

        radioGroupTipoDocumento.add(new AjaxRadio("cpf", new Model(TipoPrestadorIpe.TipoDocumento.CPF.getValue())));
        radioGroupTipoDocumento.add(new AjaxRadio("cnpj", new Model(TipoPrestadorIpe.TipoDocumento.CNPJ.getValue())));
        
        form.add(checkConsultaMedica = new CheckBoxLongValue(TipoPrestadorIpe.PROP_TIPO_ATIVIDADE_CONSULTA_MEDICA, RepositoryComponentDefault.SIM_LONG));
        form.add(checkAtenidmentoComplementar = new CheckBoxLongValue(TipoPrestadorIpe.PROP_TIPO_ATIVIDADE_ATENDIMENTO_COMPLEMENTAR, RepositoryComponentDefault.SIM_LONG));
        form.add(checkProntoAtenidmento = new CheckBoxLongValue(TipoPrestadorIpe.PROP_TIPO_ATIVIDADE_PRONTO_ATENDIMENTO, RepositoryComponentDefault.SIM_LONG));
        form.add(checkContaHospitalar = new CheckBoxLongValue(TipoPrestadorIpe.PROP_TIPO_ATIVIDADE_CONTA_HOSPITALAR, RepositoryComponentDefault.SIM_LONG));
        form.add(checkContaAmbulatorial = new CheckBoxLongValue(TipoPrestadorIpe.PROP_TIPO_ATIVIDADE_CONTA_AMBULATORIAL, RepositoryComponentDefault.SIM_LONG));

    }

    @Override
    public Class<TipoPrestadorIpe> getReferenceClass() {
        return TipoPrestadorIpe.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoPrestadorIpePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoPrestadorIpe");
    }
}
