package br.com.celk.view.hospital.loteAih;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.LoteAihDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.LoteAihDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LoteAih;
import br.com.ksisolucoes.vo.prontuario.hospital.LoteAih.Status;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaLoteAihPage extends ConsultaPage<LoteAihDTO, LoteAihDTOParam>{

    private Long lote;
    private DatePeriod periodo;
    private Long situacao;
    private DropDown<Long> dropDownSituacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        
        form.add(new InputField<Long>("lote", new PropertyModel(this, "lote")));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(getDropDownSituacao());
        
        getLinkNovo().setVisible(false);
        
        AbstractAjaxButton btnNovoLote;
        getControls().add(btnNovoLote = new AbstractAjaxButton(getControls().newChildId()) {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new GerarLoteAihPage());
            }
        });
        
        btnNovoLote.add(new AttributeModifier("class", "doc-new"));
        btnNovoLote.add(new AttributeModifier("value", bundle("novoLote")));

        setExibeExpandir(true);
    }
    
    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LoteAihDTO proxy = on(LoteAihDTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numLote"), proxy.getLoteAih().getCodigo()));
        columns.add(createSortableColumn(bundle("dataDaGeracao"), proxy.getLoteAih().getDataCadastro()));
        columns.add(createSortableColumn(bundle("quantidadeAih"), proxy.getQuantidadeAih()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getLoteAih().getStatus(), proxy.getLoteAih().getDescricaoStatus()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<LoteAihDTO>() {

            @Override
            public void customizeColumn(LoteAihDTO rowObject) {                
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<LoteAihDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, LoteAihDTO modelObject) throws ValidacaoException, DAOException {
                        cancelarLote(target, modelObject);
                    }
                }).setQuestionDialogBundleKey("desejaCancelarLoteAih")
                        .setActionsEnum(ActionsEnum.RENDER)
                        .setEnabled(!LoteAih.Status.CANCELADO.value().equals(rowObject.getLoteAih().getStatus()));
                
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LoteAihDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, LoteAihDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DadosLoteAihPage(modelObject));
                    }
                });
            }
        };
    }
    
    private void cancelarLote(AjaxRequestTarget target, LoteAihDTO loteAih) throws DAOException, ValidacaoException {
        List<Aih> _aih = LoadManager.getInstance(Aih.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Aih.PROP_LOTE_AIH), loteAih.getLoteAih()))
                .start().getList();
        
        BOFactoryWicket.getBO(HospitalFacade.class).cancelarLoteAih(loteAih, _aih);
        
        getPageableTable().update(target);
    }
    
    private DropDown<Long> getDropDownSituacao(){
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");

            dropDownSituacao.addChoice(null, bundle("todas"));
            for (Status list : Status.values()) {
                dropDownSituacao.addChoice(list.value(), list.descricao());
            }
            
        }
        return dropDownSituacao;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<LoteAihDTO, LoteAihDTOParam>(){

            @Override
            public DataPagingResult executeQueryPager(DataPaging<LoteAihDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(HospitalFacade.class).consultaLoteAih(dataPaging);
            }
            
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LoteAihDTO.PROP_LOTE_AIH, LoteAih.PROP_DATA_CADASTRO), true);
            }
            
            @Override
            public void customizeParam(LoteAihDTOParam param) {
                SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();
        
                param.setCampoOrdenacao(sortState.getSort().getProperty());
                param.setTipoOrdenacao(sortState.getSort().isAscending()?"asc":"desc");
            }

        };
    }
    
    @Override
    public LoteAihDTOParam getParameters() {
        LoteAihDTOParam param = new LoteAihDTOParam();
        param.setLote(lote);
        param.setPeriodo(periodo);
        param.setSituacao(situacao);
        
        return param;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaLoteAihPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLoteAih");
    }
    
}
