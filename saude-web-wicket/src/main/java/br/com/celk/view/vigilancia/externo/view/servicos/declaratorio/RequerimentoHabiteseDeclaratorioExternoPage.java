package br.com.celk.view.vigilancia.externo.view.servicos.declaratorio;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.CustomColorTableRow;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.dialog.DlgCadastroResponsavelTecnico;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.externo.view.estabelecimento.CadastroEstabelecimentoExternoPage;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaExternoPage;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.requerimentovigilancia.autocomplete.AutoCompleteConsultaRequerimentoProjetoHidrossanitario;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.autocomplete.AutoCompleteConsultaTipoEnquadramentoProjeto;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete.AutoCompleteConsultaTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 03/01/19.
 */
public class RequerimentoHabiteseDeclaratorioExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoVistoriaHidrossanitarioDeclaratorioDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoVistoriaHidrossanitarioDeclaratorio requerimentoVistoriaHidrossanitarioDeclaratorio;
    private AutoCompleteConsultaTipoProjetoVigilancia autoCompleteConsultaTipoProjetoVigilancia;
    private AutoCompleteConsultaTipoEnquadramentoProjeto autoCompleteConsultaTipoEnquadramentoProjeto;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private AutoCompleteConsultaRequerimentoProjetoHidrossanitario autoCompleteConsultaRequerimentoProjetoHidrossanitario;

    private DropDown dropDownTipoPessoa;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private WebMarkupContainer containerDadosGerais;
    private WebMarkupContainer containerEstabelecimento;
    private WebMarkupContainer containerPessoa;
    private WebMarkupContainer containerTipoProjeto;
    private DropDown dropDownUsoEdificacao;
    private InputField txtObservacaoUsoEdificacao;
    private LongField txtNumeroLotesParcelamento;
    private DoubleField txtArea;
    private DoubleField txtAreaComercial;
    private DoubleField txtAreaResidencial;
    private DoubleField txtAreaTotal;
    private InputField txtEmailPessoa;
    private InputField txtEmailEstabelecimento;
    private InputField txtNumeroProjetoHidrossanitario;
    private InputField txtObraNumeroEndereco;
    private InputField txtObraQuadra;
    private InputField txtObraNumeroLado;
    private InputField txtObraLote;
    private InputField txtObraComplemento;
    private InputField txtObraNumeroLoteamento;
    //    private DropDown dropDownRegiaoCobertaRedeEsgoto;
    private DropDown dropDownRegiaoAbastecidaAgua;
    private DropDown dropDownSistemaAguaPluvial;

    private boolean enabled;
    private Class classReturn;
    private WebMarkupContainer containerTipoEnquadramentoProjeto;
    private WebMarkupContainer containerDadosObra;
    private WebMarkupContainer panelMsgEditarRT;
    private WebMarkupContainer containerInformacoes;
    private ConfiguracaoVigilancia configuracaoVigilancia = null;

    private Table tblTipoProjeto;
    private CompoundPropertyModel<TipoProjetoRequerimentoVigilancia> modelTipoProjetoRequerimentoVigilancia;

    private PnlVigilanciaEndereco pnlVigilanciaEndereco;

    private ResponsavelTecnico responsavelTecnico;
    private InputField<String> txtInscricaoImobiliaria;

    private Table tblResponsavelTecnico;
    private Table tblInscricaoImobiliaria;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavelTecnico;
    private AbstractAjaxButton btnAdicionar;
    private AbstractAjaxButton btnAdicionarInscricao;
    private AbstractAjaxLink btnCadadastroResponsavel;
    private DlgCadastroResponsavelTecnico dlgCadastroResponsavelTecnico;

    private String numeroInscricaoImobiliaria;
    //    private InputField txtNumeroProcessoProjeto;
    private WebMarkupContainer containerNumPHSAprovado;
    private InputField txtNumeroPHSAprovado;
    private InputField txtNumeroProjetoUrbanistico;
    private InputField txtNumeroLicitacaoAmbiental;
    private InputField txtNumeroProjetoEsgoto;
    private InputField txtNumeroProjetoAgua;
    private WebMarkupContainer containerParcelamentoSolo;
    private AbstractAjaxButton btnAdicionarTipoProjeto;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private InputField txtDescricaoClassificaoRisco;
    private Estabelecimento estabelecimento;
    private Label lbl;
    private String msg;
    private InputField txtLicencaAmbientalLai;
    private SubmitButton btnSalvar;

    public RequerimentoHabiteseDeclaratorioExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoHabiteseDeclaratorioExternoPage(TipoSolicitacao tipoSolicitacao, Estabelecimento estabelecimento, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.estabelecimento = estabelecimento;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoHabiteseDeclaratorioExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoVistoriaHidrossanitarioDeclaratorio(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }

        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoVistoriaHidrossanitarioDeclaratorioDTO proxy = on(RequerimentoVistoriaHidrossanitarioDeclaratorioDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoVistoriaHidrossanitarioDeclaratorioDTO()));
        if (requerimentoVistoriaHidrossanitarioDeclaratorio != null) {
            form.getModel().getObject().setRequerimentoVistoriaHidrossanitarioDeclaratorio(requerimentoVistoriaHidrossanitarioDeclaratorio);
            carregarTipoProjetoRequerimentoVigilanciaList(requerimentoVigilancia);
        } else {
            form.getModel().getObject().setRequerimentoVistoriaHidrossanitarioDeclaratorio(new RequerimentoVistoriaHidrossanitarioDeclaratorio());
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }

        lbl = new Label("msg", msg);
        lbl.setVisible(false);
        lbl.setOutputMarkupPlaceholderTag(true);
        form.add(lbl);

        form.add(containerDadosGerais = new WebMarkupContainer("containerDadosGerais"));
        containerDadosGerais.setEnabled(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo() == null);
        containerDadosGerais.setOutputMarkupId(true);

        containerDadosGerais.add(getDropDownTipoPessoa(proxy));

        containerDadosGerais.add(getContainerPessoa(proxy));

        containerDadosGerais.add(getContainerEstabelecimento(proxy));

        addCamposEndereco(proxy);

        form.add(new DisabledInputField<String>(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getProtocoloFormatado())));

        // INÍCIO - Tipo Projeto
        TipoProjetoRequerimentoVigilancia proxyTipoProjeto = on(TipoProjetoRequerimentoVigilancia.class);
        containerTipoProjeto = new WebMarkupContainer("containerTipoProjeto", modelTipoProjetoRequerimentoVigilancia = new CompoundPropertyModel<>(new TipoProjetoRequerimentoVigilancia()));
        containerTipoProjeto.setOutputMarkupId(true);
        containerTipoProjeto.setEnabled(enabled);

        containerTipoProjeto.add(autoCompleteConsultaTipoProjetoVigilancia = new AutoCompleteConsultaTipoProjetoVigilancia(path(proxyTipoProjeto.getTipoProjetoVigilancia())));
        autoCompleteConsultaTipoProjetoVigilancia.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaTipoProjetoVigilancia.addAjaxUpdateValue();
        autoCompleteConsultaTipoProjetoVigilancia.setLabel(Model.of(bundle("tipoProjeto")));
        autoCompleteConsultaTipoProjetoVigilancia.setTipo(TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE_DECLARATORIO.value());
        autoCompleteConsultaTipoProjetoVigilancia.setEnabled(enabled);

        containerTipoProjeto.add(txtArea = new DoubleField(path(proxyTipoProjeto.getArea())));
        txtArea.setMDec(4).addAjaxUpdateValue();
        txtArea.setVMax(999999.9999);
        txtArea.setEnabled(enabled);
        txtArea.setLabel(Model.of(bundle("areaM2")));

        containerTipoProjeto.add(btnAdicionarTipoProjeto = new AbstractAjaxButton("btnAdicionarTipoProjeto") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarTipoProjeto(target);
            }
        });
        btnAdicionarTipoProjeto.setDefaultFormProcessing(false).setEnabled(enabled);
        btnAdicionarTipoProjeto.setOutputMarkupPlaceholderTag(true);
        btnAdicionarTipoProjeto.getAjaxRegionMarkupId();

        containerTipoProjeto.add(tblTipoProjeto = new Table("tblTipoProjeto", getColumnsTipoProjetoRequerimentoVigilancia(), getCollectionProviderTipoProjetoRequerimentoVigilancia()));
        tblTipoProjeto.populate();
        tblTipoProjeto.setScrollY("1800");
        tblTipoProjeto.setEnabled(enabled);

        form.add(containerTipoProjeto);

        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo() != null) {
            containerTipoProjeto.setEnabled(CollectionUtils.isEmpty(VigilanciaHelper.getVigilanciaFinanceiroList(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia())));
        }
        // FIM - Tipo Projeto

        form.add(autoCompleteConsultaRequerimentoProjetoHidrossanitario = new AutoCompleteConsultaRequerimentoProjetoHidrossanitario(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoProjetoHidrossanitarioAprovado())));
        autoCompleteConsultaRequerimentoProjetoHidrossanitario.addAjaxUpdateValue();
        autoCompleteConsultaRequerimentoProjetoHidrossanitario.setSituacao(RequerimentoVigilancia.Situacao.DEFERIDO.value());
        autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo() == null);
        autoCompleteConsultaRequerimentoProjetoHidrossanitario.setLabel(Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));

        autoCompleteConsultaRequerimentoProjetoHidrossanitario.add(new ConsultaListener<RequerimentoProjetoHidrossanitario>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, RequerimentoProjetoHidrossanitario object) {
                habilitarCampoProjetoHidroManual(target, object, false);
            }
        });
        autoCompleteConsultaRequerimentoProjetoHidrossanitario.add(new RemoveListener<RequerimentoProjetoHidrossanitario>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, RequerimentoProjetoHidrossanitario object) {
                habilitarCampoProjetoHidroManual(target, null, false);
            }
        });

        form.add(txtNumeroProjetoHidrossanitario = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoAprovado())));
        txtNumeroProjetoHidrossanitario.setEnabled(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo() == null);
        txtNumeroProjetoHidrossanitario.setLabel(Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));

        containerDadosObra = new WebMarkupContainer("containerDadosObra");
        containerDadosObra.setOutputMarkupId(true);
        form.add(containerDadosObra);

        containerDadosObra.add(pnlVigilanciaEndereco = (PnlVigilanciaEndereco) new PnlVigilanciaEndereco(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getVigilanciaEndereco())));
        pnlVigilanciaEndereco.setEnabled(enabled);
        pnlVigilanciaEndereco.addRequired();
        pnlVigilanciaEndereco.setRequired(true);
        pnlVigilanciaEndereco.setLabel(new Model(bundle("endereco")));
        pnlVigilanciaEndereco.addAjaxUpdateValue();
        pnlVigilanciaEndereco.setOutputMarkupPlaceholderTag(true);

        panelMsgEditarRT = new WebMarkupContainer("panelMsg");
        Label lblMsg = new Label("msg", "Os registros em amarelo devem ser editados e preenchido os campos obrigatórios.");
        panelMsgEditarRT.setOutputMarkupPlaceholderTag(true);
        panelMsgEditarRT.add(lblMsg);
        panelMsgEditarRT.setVisible(false);
        containerDadosObra.add(panelMsgEditarRT);

        containerDadosObra.add(tblResponsavelTecnico = new Table("tblResponsavelTecnico", getColumnsRT(), getCollectionProvider()) {
            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new CustomColorTableRow(id, index, model) {
                    @Override
                    public TableColorEnum getColor() {
                        EloRequerimentoVigilanciaResponsavelTecnico rowObject = (EloRequerimentoVigilanciaResponsavelTecnico) getRowObject();
                        if (isCadastroInvalidoRT(rowObject.getResponsavelTecnico())) {
                            return TableColorEnum.AMARELA;
                        }
                        return TableColorEnum.PADRAO;
                    }
                };
            }
        });
        tblResponsavelTecnico.setScrollY("180px");
        tblResponsavelTecnico.populate();
        tblResponsavelTecnico.setOutputMarkupPlaceholderTag(true);
        tblResponsavelTecnico.setOutputMarkupId(true);
        tblResponsavelTecnico.getAjaxRegionMarkupId();

        containerDadosObra.add(tblInscricaoImobiliaria = new Table("tblInscricaoImobiliaria", getColumnsInsc(), getCollectionProviderInsc()));
        tblInscricaoImobiliaria.setScrollY("180px");
        tblInscricaoImobiliaria.populate();
        tblInscricaoImobiliaria.setOutputMarkupPlaceholderTag(true);
        tblInscricaoImobiliaria.setOutputMarkupId(true);
        tblInscricaoImobiliaria.getAjaxRegionMarkupId();

        containerDadosObra.add(btnAdicionar = new AbstractAjaxButton("btnAdicionarResponsavelTecnico") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarRT(target, responsavelTecnico);
            }
        });
        containerDadosObra.getAjaxRegionMarkupId();
        containerDadosObra.setOutputMarkupPlaceholderTag(true);

        btnAdicionar.setEnabled(enabled);
        btnAdicionar.getAjaxRegionMarkupId();
        btnAdicionar.setOutputMarkupPlaceholderTag(true);

        btnAdicionar.setDefaultFormProcessing(false);
        tblResponsavelTecnico.setEnabled(enabled);
        tblInscricaoImobiliaria.setEnabled(enabled);

        containerDadosObra.add(btnAdicionarInscricao = new AbstractAjaxButton("btnAdicionarInscricao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarInscricaoImob(target);
            }
        });
        btnAdicionarInscricao.setEnabled(enabled);
        btnAdicionarInscricao.setDefaultFormProcessing(false);

        containerDadosObra.add(autoCompleteConsultaResponsavelTecnico = (AutoCompleteConsultaResponsavelTecnico)
                new AutoCompleteConsultaResponsavelTecnico("responsavelTecnico", new PropertyModel(this, "responsavelTecnico")).setEnabled(enabled));
        autoCompleteConsultaResponsavelTecnico.setOutputMarkupId(true);
        autoCompleteConsultaResponsavelTecnico.setLabel(new Model(Bundle.getStringApplication("setorResponsavel")));
        containerDadosObra.add(btnCadadastroResponsavel = new AbstractAjaxLink("btnCadadastroResponsavel") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarResponsavel(target);
            }
        });
        btnCadadastroResponsavel.setEnabled(enabled);
        btnCadadastroResponsavel.getAjaxRegionMarkupId();
        btnCadadastroResponsavel.setOutputMarkupPlaceholderTag(true);

        if (requerimentoVistoriaHidrossanitarioDeclaratorio != null && requerimentoVistoriaHidrossanitarioDeclaratorio.getVigilanciaEndereco() != null) {
            pnlVigilanciaEndereco.setModelObject(requerimentoVistoriaHidrossanitarioDeclaratorio.getVigilanciaEndereco());
        }

        containerDadosObra.add(txtObraNumeroEndereco = (InputField) new RequiredInputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraNumeroEndereco())).setLabel(new Model(bundle("numeroAbv"))).setEnabled(enabled));
        containerDadosObra.add(txtObraQuadra = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraQuadra())).setEnabled(enabled));
        containerDadosObra.add(txtObraNumeroLado = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraNumeroLado())).setEnabled(enabled));
        containerDadosObra.add(txtObraLote = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraLote())).setEnabled(enabled));
        containerDadosObra.add(txtObraComplemento = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraComplemento())).setEnabled(enabled));
        containerDadosObra.add(txtObraNumeroLoteamento = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObraNumeroLoteamento())).setEnabled(enabled));

        containerDadosObra.add(txtInscricaoImobiliaria = new InputField("numeroInscricaoImobiliaria", new PropertyModel(this, "numeroInscricaoImobiliaria")));
        txtInscricaoImobiliaria.setLabel(Model.of(bundle("nInscricaoImobiliaria")));
        txtInscricaoImobiliaria.setEnabled(enabled);
        txtInscricaoImobiliaria.addAjaxUpdateValue();

        containerInformacoes = new WebMarkupContainer("containerInformacoes");
        containerInformacoes.setOutputMarkupId(true);
        containerDadosObra.add(containerInformacoes);

        containerInformacoes.add(dropDownRegiaoAbastecidaAgua = (DropDown) DropDownUtil.getNaoSimLongDropDown(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRegiaoAbastecidaAgua()), true, true).setLabel(new Model(bundle("regiaoAbastecidaPorAguaPotavel"))).setEnabled(enabled));
        dropDownRegiaoAbastecidaAgua.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                vigilanciaAltoBaixoRisco(target);
            }
        });
        dropDownRegiaoAbastecidaAgua.setOutputMarkupPlaceholderTag(true);
        dropDownRegiaoAbastecidaAgua.addAjaxUpdateValue();

        containerInformacoes.add(getRadioGroupNaturezaEdificacao(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNaturezaEdificacao())));
        containerInformacoes.add(txtLicencaAmbientalLai = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNaturezaEdificacaoLai())));
        txtLicencaAmbientalLai.setEnabled(enabled && RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.QUALQUER_NATUREZA_LICENCIADA.value().equals(form.getModelObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNaturezaEdificacao()));
        txtLicencaAmbientalLai.setLabel(Model.of(bundle("numeroLicencaAmbiental")));

        containerInformacoes.add(dropDownSistemaAguaPluvial = (DropDown) DropDownUtil.getNaoSimLongDropDown(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getSistemaAguaPluvial()), true, true).setLabel(new Model(bundle("sistemaAproveitamentoAguasPluviais"))).setEnabled(enabled));
        containerInformacoes.add(txtDescricaoClassificaoRisco = new DisabledInputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getVigilanciaAltoBaixoRisco().getDescricaoClassificacaoRisco())));
        txtDescricaoClassificaoRisco.setOutputMarkupPlaceholderTag(true);
        txtDescricaoClassificaoRisco.setOutputMarkupId(true);

        containerTipoEnquadramentoProjeto = new WebMarkupContainer("containerTipoEnquadramentoProjeto");
        containerTipoEnquadramentoProjeto.setOutputMarkupId(true);
        containerDadosObra.add(containerTipoEnquadramentoProjeto);

        containerTipoEnquadramentoProjeto.add(autoCompleteConsultaTipoEnquadramentoProjeto = new AutoCompleteConsultaTipoEnquadramentoProjeto(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getTipoEnquadramentoProjeto()), true)).setEnabled(enabled);
        autoCompleteConsultaTipoEnquadramentoProjeto.setLabel(Model.of(bundle("tipoEnquadramentoProjeto")));

        autoCompleteConsultaTipoEnquadramentoProjeto.add(new ConsultaListener<TipoEnquadramentoProjeto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoEnquadramentoProjeto tipoEnquadramentoProjeto) {
                if (tipoEnquadramentoProjeto.getDescricao().equalsIgnoreCase("Substituição de Projeto")) {
                    containerNumPHSAprovado.setVisible(true);
                    txtNumeroPHSAprovado.limpar(target);
                    target.add(containerNumPHSAprovado);
                    target.add(txtNumeroPHSAprovado);
                }
            }
        });

        autoCompleteConsultaTipoEnquadramentoProjeto.add(new RemoveListener<TipoEnquadramentoProjeto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoEnquadramentoProjeto tipoEnquadramentoProjeto) {
                containerNumPHSAprovado.setVisible(false);
                txtNumeroPHSAprovado.limpar(target);
                target.add(containerNumPHSAprovado);
                target.add(txtNumeroPHSAprovado);
            }
        });

        containerNumPHSAprovado = new WebMarkupContainer("containerNumPHSAprovado");
        containerNumPHSAprovado.setOutputMarkupId(true);
        containerNumPHSAprovado.setOutputMarkupPlaceholderTag(true);
        containerNumPHSAprovado.getAjaxRegionMarkupId();
        containerTipoEnquadramentoProjeto.add(containerNumPHSAprovado);
        containerNumPHSAprovado.add(txtNumeroPHSAprovado = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoHSAprovado())));

        containerDadosObra.add(dropDownUsoEdificacao = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao()), RequerimentoVistoriaHidrossanitarioDeclaratorio.UsoEdificacao.values(), true, "", false, false, true).setEnabled(enabled));
        containerDadosObra.add(txtObservacaoUsoEdificacao = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getObservacaoUsoEdificacao())).setEnabled(enabled));
        txtObservacaoUsoEdificacao.setLabel(Model.of(bundle("outros")));

        dropDownUsoEdificacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarUsoEdificacao(target);
                habilitarParcelamentoSolo(target);
                habilitarMetragem(target);
                target.appendJavaScript(JScript.initMasks());
            }
        });

        containerParcelamentoSolo = new WebMarkupContainer("containerParcelamentoSolo");
        containerParcelamentoSolo.setOutputMarkupId(true);
        containerParcelamentoSolo.setOutputMarkupPlaceholderTag(true);
        containerParcelamentoSolo.getAjaxRegionMarkupId();

        containerParcelamentoSolo.add(txtNumeroLotesParcelamento = (LongField) new LongField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getParcelamentoSoloNumeroLotes())).setEnabled(enabled));
        txtNumeroLotesParcelamento.setVMax(99999L);
        txtNumeroLotesParcelamento.setLabel(Model.of(bundle("nrLotes")));

        containerParcelamentoSolo.add(txtNumeroProjetoUrbanistico = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoUrbanistico())).setEnabled(enabled));
        containerParcelamentoSolo.add(txtNumeroLicitacaoAmbiental = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroLicencaAmbiental())).setEnabled(enabled));
        containerParcelamentoSolo.add(txtNumeroProjetoEsgoto = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoEsgoto())).setEnabled(enabled));
        containerParcelamentoSolo.add(txtNumeroProjetoAgua = (InputField) new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoAgua())).setEnabled(enabled));

        txtNumeroProjetoUrbanistico.setLabel(new Model(bundle("numeroProjetoUrbanismo")));
        txtNumeroLicitacaoAmbiental.setLabel(new Model(bundle("numeroLicencaAmbientalLAO")));
        txtNumeroProjetoEsgoto.setLabel(new Model(bundle("numeroProjetoEsgoto")));
        txtNumeroProjetoAgua.setLabel(new Model(bundle("numeroProjetoAgua")));

        containerDadosObra.add(containerParcelamentoSolo);

        containerDadosObra.add(txtAreaComercial = new RequiredDoubleField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaComercial())));
        txtAreaComercial.setMDec(4).addAjaxUpdateValue();
        txtAreaComercial.setVMax(99999.9999);
        txtAreaComercial.setEnabled(enabled);
        txtAreaComercial.setLabel(Model.of(bundle("areaComercial")));

        txtAreaComercial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarAreaTotalConstrucao(target);
            }
        });

        containerDadosObra.add(txtAreaResidencial = new RequiredDoubleField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaResidencial())));
        txtAreaResidencial.setMDec(4).addAjaxUpdateValue();
        txtAreaResidencial.setVMax(99999.9999);
        txtAreaResidencial.setEnabled(enabled);
        txtAreaResidencial.setLabel(Model.of(bundle("areaResidencial")));

        txtAreaResidencial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarAreaTotalConstrucao(target);
            }
        });

        containerDadosObra.add(txtAreaTotal = new DoubleField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaTotalConstrucao())));
        txtAreaTotal.setMDec(4).addAjaxUpdateValue();
        txtAreaTotal.setVMax(999999.9999);
        txtAreaTotal.setEnabled(false);
        txtAreaTotal.setLabel(Model.of(bundle("areaTotalConstrucao")));

        form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia(), enabled));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled, false));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        form.add(new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));


        form.add(new VoltarButton("btnVoltar"));
        btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        });
        btnSalvar.setEnabled(enabled && RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getTermoAceite()));
        form.add(btnSalvar);

        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());

        add(form);

        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo() == null) {
            try {
                if (configuracaoVigilancia == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
                }
            } catch (ValidacaoException ex) {
                warn(ex);
            }
        }

        if (requerimentoVigilancia != null) {
            carregarEloRequerimentoVigilanciaResponsavelTecnico(requerimentoVigilancia);
            carregarInscricoesImob(requerimentoVigilancia);
        }

        if (estabelecimento != null && estabelecimento.getCodigo() != null) {
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);

            atualizarEnderecoEstabelecimento(null, estabelecimento);
            habilitarCampoProjetoHidroEstabelecimento(null, estabelecimento);
            habilitarCampoProjetoHidroManual(null, null, true);
        }
        enableCamposDadosGeral(null, false);
        habilitarUsoEdificacao(null);
        habilitarParcelamentoSolo(null);
        habilitarMetragem(null);

        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getCodigo() != null) {
            if (RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente())) {
                habilitarCampoProjetoHidroVigPessoa(null, form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa());
            } else {
                habilitarCampoProjetoHidroEstabelecimento(null, form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento());
            }
        }

        addCheckTermoAceite(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getTermoAceite()));

        if (!enabled) {
            dropDownTipoPessoa.setEnabled(enabled);
            autoCompleteConsultaEstabelecimento.setEnabled(enabled);
            autoCompleteConsultaVigilanciaPessoa.setEnabled(enabled);
            txtObservacaoUsoEdificacao.setEnabled(enabled);
            containerParcelamentoSolo.setEnabled(enabled);
            txtAreaComercial.setEnabled(enabled);
            txtAreaResidencial.setEnabled(enabled);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(enabled);
            txtNumeroProjetoHidrossanitario.setEnabled(enabled);
        }
        habilitarNumeroPHSAprovado();
        habilitarParcelamentoSolo(null);
        vigilanciaAltoBaixoRisco(null);
    }

    private void addCheckTermoAceite(String id) {
        CheckBoxLongValue cbxTermoAceite = new CheckBoxLongValue(id, RepositoryComponentDefault.SIM_LONG);
        cbxTermoAceite.setEnabled(enabled);

        cbxTermoAceite.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                btnSalvar.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(cbxTermoAceite.getComponentValue()));
                art.add(btnSalvar);
            }
        });
        form.add(cbxTermoAceite);
    }

    private RadioButtonGroup getRadioGroupNaturezaEdificacao(String id) {
        RadioButtonGroup radioButtonGroup = new RadioButtonGroup(id);

        radioButtonGroup.add(new AjaxRadio("unifamiliarComRedeEsgoto", new Model(RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.UNIFAMILIAR_COM_REDE_ESGOTO.value())).setEnabled(enabled));
        radioButtonGroup.add(new AjaxRadio("unifamiliarSemRedeEsgoto", new Model(RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.UNIFAMILIAR_SEM_REDE_ESGOTO.value())).setEnabled(enabled));
        radioButtonGroup.add(new AjaxRadio("qualquerNaturezaComRedeEsgoto", new Model(RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.QUALQUER_NATUREZA_COM_REDE_ESGOTO.value())).setEnabled(enabled));
        radioButtonGroup.add(new AjaxRadio("qualquerNaturezaLicenciada", new Model(RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.QUALQUER_NATUREZA_LICENCIADA.value())).setEnabled(enabled));
        radioButtonGroup.add(new AjaxRadio("loteamentoCondominioComRedeEsgoto", new Model(RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.LOTEAMENTO_CONDOMINIO_COM_REDE_ESGOTO.value())).setEnabled(enabled));

        radioButtonGroup.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                txtLicencaAmbientalLai.setEnabled(false);
                txtLicencaAmbientalLai.setRequired(false);
                txtLicencaAmbientalLai.removeRequiredClass();
                if (RequerimentoVistoriaHidrossanitarioDeclaratorio.NaturezaEdificacao.QUALQUER_NATUREZA_LICENCIADA.value().equals(form.getModelObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNaturezaEdificacao())) {
                    txtLicencaAmbientalLai.setEnabled(true);
                    txtLicencaAmbientalLai.setRequired(true);
                    txtLicencaAmbientalLai.addRequiredClass();
                    txtLicencaAmbientalLai.setRequired(true);
                }
                target.add(txtLicencaAmbientalLai);
            }
        });
        radioButtonGroup.setEnabled(enabled);
        radioButtonGroup.setRequired(true);
        radioButtonGroup.setLabel(new Model(bundle("naturezaEdificacao")));
        return radioButtonGroup;
    }

    private void vigilanciaAltoBaixoRisco(AjaxRequestTarget target) {
        if (target == null) {
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().setVigilanciaAltoBaixoRisco(null);
        }
        txtDescricaoClassificaoRisco.add(new AttributeModifier("style", "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #F2F2F2"));

        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRegiaoAbastecidaAgua() != null) {
            VigilanciaAltoBaixoRisco vabr = LoadManager.getInstance(VigilanciaAltoBaixoRisco.class)
                    .addProperty(VigilanciaAltoBaixoRisco.PROP_CODIGO)
                    .addProperty(VigilanciaAltoBaixoRisco.PROP_CLASSIFICACAO_RISCO)
//                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaAltoBaixoRisco.PROP_REGIAO_COBERTA_REDE_ESGOTO, form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRegiaoCobertaRedeEsgoto()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaAltoBaixoRisco.PROP_REGIAO_ABASTECIDA_AGUA, form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRegiaoAbastecidaAgua()))
                    .setMaxResults(1)
                    .start().getVO();

            if (vabr != null && vabr.getCodigo() != null) {
                form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().setVigilanciaAltoBaixoRisco(vabr);

                if (VigilanciaAltoBaixoRisco.ClassificacaoRisco.ALTO_RISCO.value().equals(vabr.getClassificacaoRisco())) {
                    txtDescricaoClassificaoRisco.add(new AttributeModifier("style", "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #E17373"));
                } else if (VigilanciaAltoBaixoRisco.ClassificacaoRisco.BAIXO_RISCO.value().equals(vabr.getClassificacaoRisco())) {
                    txtDescricaoClassificaoRisco.add(new AttributeModifier("style", "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #2AABD2"));
                }
            }
        }
        if (target != null) {
            target.add(txtDescricaoClassificaoRisco);
        }
    }

    private void habilitarNumeroPHSAprovado() {
        if (requerimentoVistoriaHidrossanitarioDeclaratorio == null) {
            containerNumPHSAprovado.setVisible(false);
        } else {
            containerNumPHSAprovado.setVisible(true);
        }
    }

    private void cadastrarResponsavel(AjaxRequestTarget target) {
        if (dlgCadastroResponsavelTecnico == null) {
            addModal(target, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(newModalId()));
            dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, ResponsavelTecnico responsavelTecnico) throws ValidacaoException, DAOException {
                    autoCompleteConsultaResponsavelTecnico.limpar(target);
                    autoCompleteConsultaResponsavelTecnico.setComponentValue(responsavelTecnico);
                    target.add(autoCompleteConsultaResponsavelTecnico);
                }
            });
        }

        dlgCadastroResponsavelTecnico.limpar(target);
        dlgCadastroResponsavelTecnico.show(target);
    }

    private void editarRT(AjaxRequestTarget target, ResponsavelTecnico responsavelTecnico) {
        addModal(target, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(newModalId(), responsavelTecnico));
        dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
            @Override
            public void onSalvar(AjaxRequestTarget target, ResponsavelTecnico responsavelTecnico) throws ValidacaoException, DAOException {
                if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList())) {
                    EloRequerimentoVigilanciaResponsavelTecnico rtRemover = Lambda.selectUnique(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList(),
                            Lambda.having(on(EloRequerimentoVigilanciaResponsavelTecnico.class).getResponsavelTecnico().getCodigo(), Matchers.equalTo(responsavelTecnico.getCodigo())));
                    removerRT(target, rtRemover);
                }
                adicionarRT(target, responsavelTecnico);
            }
        });
        dlgCadastroResponsavelTecnico.setCamposObrigatorios(target);
        dlgCadastroResponsavelTecnico.show(target);
    }

    private boolean isCadastroInvalidoRT(ResponsavelTecnico responsavelTecnico) {
        return responsavelTecnico.getNome() == null
                || responsavelTecnico.getCpf() == null
                || responsavelTecnico.getCbo() == null
                || responsavelTecnico.getNumeroRegistro() == null
                || responsavelTecnico.getOrgaoEmissor() == null
                || responsavelTecnico.getVigilanciaEndereco() == null
                || responsavelTecnico.getNumero() == null
                || (responsavelTecnico.getTelefone() == null && responsavelTecnico.getCelular() == null)
                || responsavelTecnico.getEmail() == null;
    }

    private void adicionarRT(AjaxRequestTarget target, ResponsavelTecnico rt) throws ValidacaoException {
        if (rt == null) {
            throw new ValidacaoException(BundleManager.getString("informeResponsavelTecnico"));
        }

        if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList())) {
            for (EloRequerimentoVigilanciaResponsavelTecnico elo : form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList()) {
                if (elo.getResponsavelTecnico().getCodigo().equals(rt.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("responsavelTecnicoJaAdicionado"));
                }
            }
        }

        EloRequerimentoVigilanciaResponsavelTecnico elo = new EloRequerimentoVigilanciaResponsavelTecnico();
        elo.setResponsavelTecnico(rt);
        form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().add(elo);

        panelMsgEditarRT.setVisible(validarRegistrosInvalidosRT());
        target.add(panelMsgEditarRT);

        tblResponsavelTecnico.update(target);
        tblResponsavelTecnico.populate();
        responsavelTecnico = null;
        autoCompleteConsultaResponsavelTecnico.limpar(target);
    }

    private boolean validarRegistrosInvalidosRT() {
        boolean existeInvalido = false;
        for (EloRequerimentoVigilanciaResponsavelTecnico eloRt : form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList()) {
            if (isCadastroInvalidoRT(eloRt.getResponsavelTecnico())) {
                existeInvalido = true;
            }
        }
        return existeInvalido;
    }

    private void adicionarInscricaoImob(AjaxRequestTarget target) throws ValidacaoException {
        if (txtInscricaoImobiliaria.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("informeNumeroInscricaoImobiliaria"));
        }

        if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList())) {
            for (RequerimentoVigilanciaInscricaoImob insc : form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList()) {
                if (insc.getNumeroInscricaoImobiliaria().equals(txtInscricaoImobiliaria.getComponentValue())) {
                    throw new ValidacaoException(BundleManager.getString("inscricaoImobiliariaJaAdicionada"));
                }
            }
        }

        RequerimentoVigilanciaInscricaoImob inscricao = new RequerimentoVigilanciaInscricaoImob();
        inscricao.setNumeroInscricaoImobiliaria(txtInscricaoImobiliaria.getComponentValue());
        form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().add(inscricao);

        tblInscricaoImobiliaria.update(target);
        tblInscricaoImobiliaria.populate();
        txtInscricaoImobiliaria.limpar(target);
    }

    private List<IColumn> getColumnsRT() {
        ColumnFactory columnFactory = new ColumnFactory(EloRequerimentoVigilanciaResponsavelTecnico.class);
        List<IColumn> columns = new ArrayList<>();
        EloRequerimentoVigilanciaResponsavelTecnico proxy = on(EloRequerimentoVigilanciaResponsavelTecnico.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("nome"), path(proxy.getResponsavelTecnico().getNome())));
        columns.add(columnFactory.createColumn(BundleManager.getString("cpf"), path(proxy.getResponsavelTecnico().getCpfFormatado())));
        columns.add(columnFactory.createColumn(BundleManager.getString("registro"), path(proxy.getResponsavelTecnico().getDescricaoRegistro())));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModelObject().getEloRequerimentoVigilanciaResponsavelTecnicoList();
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EloRequerimentoVigilanciaResponsavelTecnico>() {
            @Override
            public void customizeColumn(EloRequerimentoVigilanciaResponsavelTecnico rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        editarRT(target, rowObject.getResponsavelTecnico());
                    }
                });
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerRT(target, rowObject);
                    }
                });
            }
        };
    }

    private List<IColumn> getColumnsInsc() {
        ColumnFactory columnFactory = new ColumnFactory(RequerimentoVigilanciaInscricaoImob.class);
        List<IColumn> columns = new ArrayList<>();
        RequerimentoVigilanciaInscricaoImob proxy = on(RequerimentoVigilanciaInscricaoImob.class);

        columns.add(getCustomColumnInsc());
        columns.add(columnFactory.createColumn(BundleManager.getString("inscricaoImobiliaria"), path(proxy.getNumeroInscricaoImobiliaria())));

        return columns;
    }

    private ICollectionProvider getCollectionProviderInsc() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModelObject().getRequerimentoVigilanciaInscricaoImobList();
            }
        };
    }

    private IColumn getCustomColumnInsc() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaInscricaoImob>() {
            @Override
            public void customizeColumn(final RequerimentoVigilanciaInscricaoImob rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerInscricaoImob(target, rowObject);
                    }
                });
            }
        };
    }

    private void removerRT(AjaxRequestTarget target, EloRequerimentoVigilanciaResponsavelTecnico rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().size(); i++) {
            EloRequerimentoVigilanciaResponsavelTecnico elo = form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().get(i);
            if (elo == rowObject) {
                if (elo.getCodigo() != null) {
                    form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoExcluirList().add(elo);
                }
                form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().remove(i);
            }
        }
        tblResponsavelTecnico.populate();
        tblResponsavelTecnico.update(target);

        CrudUtils.removerItem(target, tblResponsavelTecnico, form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList(), rowObject);
        validarRegistrosInvalidosRT();
        panelMsgEditarRT.setVisible(validarRegistrosInvalidosRT());
        target.add(panelMsgEditarRT);
    }

    private void removerInscricaoImob(AjaxRequestTarget target, RequerimentoVigilanciaInscricaoImob rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().size(); i++) {
            RequerimentoVigilanciaInscricaoImob insc = form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().get(i);
            if (insc == rowObject) {
                if (insc.getCodigo() != null) {
                    form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobExcluirList().add(insc);
                }
                form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().remove(i);
            }
        }
        tblInscricaoImobiliaria.populate();
        tblInscricaoImobiliaria.update(target);

        CrudUtils.removerItem(target, tblInscricaoImobiliaria, form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList(), rowObject);
    }

    private void adicionarTipoProjeto(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        TipoProjetoRequerimentoVigilancia tprv = modelTipoProjetoRequerimentoVigilancia.getObject();

        if (tprv.getTipoProjetoVigilancia() == null) {
            throw new ValidacaoException(bundle("informeTipoProjeto"));
        }

        if (tprv.getArea() == null) {
            throw new ValidacaoException(bundle("informeArea"));
        }

        for (TipoProjetoRequerimentoVigilancia item : form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList()) {
            if (item.getTipoProjetoVigilancia().getCodigo().equals(tprv.getTipoProjetoVigilancia().getCodigo())) {
                throw new ValidacaoException(BundleManager.getString("tipoProjetoJaAdicionado"));
            }
        }

        if (Coalesce.asDouble(tprv.getArea()) > tprv.getTipoProjetoVigilancia().getMetragemMaxima()) {
            if (Coalesce.asDouble(configuracaoVigilancia.getValorExcedidoAnaliseProjeto()) == 0D) {
                throw new ValidacaoException(BundleManager.getString("msgAreaInformadaExcedeMetragemMaximaTipoProjetoX", tprv.getTipoProjetoVigilancia().getMetragemMaxima()));
            }
        }

        form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().add(tprv);

        calcularAreaTotalConstrucao(target);

        limparTipoProjeto(target);
        tblTipoProjeto.update(target);
        target.focusComponent(autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().getTextField());
    }

    private List<IColumn> getColumnsTipoProjetoRequerimentoVigilancia() {
        List<IColumn> columns = new ArrayList<IColumn>();
        TipoProjetoRequerimentoVigilancia proxy = on(TipoProjetoRequerimentoVigilancia.class);

        columns.add(getActionColumnTipoProjetoRequerimentoVigilancia());
        columns.add(createColumn(bundle("tipo"), proxy.getTipoProjetoVigilancia().getDescricao()));
        columns.add(createColumn(bundle("area"), proxy.getArea()));

        return columns;
    }

    private IColumn getActionColumnTipoProjetoRequerimentoVigilancia() {
        return new MultipleActionCustomColumn<TipoProjetoRequerimentoVigilancia>() {
            @Override
            public void customizeColumn(TipoProjetoRequerimentoVigilancia rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoProjetoRequerimentoVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, TipoProjetoRequerimentoVigilancia modelObject) throws ValidacaoException, DAOException {
                        removerTipoProjetoRequerimentoVigilancia(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerTipoProjetoRequerimentoVigilancia(AjaxRequestTarget target, TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia) {
        limparTipoProjeto(target);
        for (int i = 0; i < form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().size(); i++) {
            if (form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().get(i) == tipoProjetoRequerimentoVigilancia) {
                if (form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().get(i).getCodigo() != null) {
                    form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaExcluirList().add(form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().get(i));
                }
                form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().remove(i);
                break;
            }
        }
        calcularAreaTotalConstrucao(target);
        tblTipoProjeto.update(target);
    }

    private void limparTipoProjeto(AjaxRequestTarget target) {
        modelTipoProjetoRequerimentoVigilancia.setObject(new TipoProjetoRequerimentoVigilancia());
        autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
        txtArea.limpar(target);
    }

    private ICollectionProvider getCollectionProviderTipoProjetoRequerimentoVigilancia() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList();
            }
        };
    }

    private void habilitarCampoProjetoHidroManual(AjaxRequestTarget target, RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario, boolean limparCampoProjetoHidrossanitario) {
        if (target != null) {
            if (limparCampoProjetoHidrossanitario) {
                autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
            }

            // Grupo Tipo do Projeto
            autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
            txtArea.limpar(target);

            // Grupo Dados da Obra
            txtObraNumeroEndereco.limpar(target);
            txtObraQuadra.limpar(target);
            txtObraNumeroLado.limpar(target);
            txtObraLote.limpar(target);
            txtObraComplemento.limpar(target);
            txtObraNumeroLoteamento.limpar(target);

            // Grupo Informações
//            txtNumeroProcessoProjeto.limpar(target);
//            dropDownRegiaoCobertaRedeEsgoto.limpar(target);
            dropDownRegiaoAbastecidaAgua.limpar(target);
            dropDownSistemaAguaPluvial.limpar(target);

            // Grupo Enquadramento
            autoCompleteConsultaTipoEnquadramentoProjeto.limpar(target);

            // Grupo Uso da Edificação
            dropDownUsoEdificacao.limpar(target);
            txtObservacaoUsoEdificacao.limpar(target);

            // Grupo Parcelamento do Solo
            txtNumeroLotesParcelamento.limpar(target);

            // Grupo Metragem
            txtAreaComercial.limpar(target);
            txtAreaResidencial.limpar(target);
            txtAreaTotal.limpar(target);

            pnlVigilanciaEndereco.limpar(target);
        }

        // Grupo Fiscais
        //pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalList().clear();

        if (requerimentoProjetoHidrossanitario != null) {
            // Grupo Tipo do Projeto
//            autoCompleteConsultaTipoProjetoVigilancia.setComponentValue(requerimentoProjetoHidrossanitario.getTipoProjetoVigilancia());
//            txtArea.setComponentValue(requerimentoProjetoHidrossanitario.getArea());
            carregarTipoProjetoRequerimentoVigilanciaList(requerimentoProjetoHidrossanitario.getRequerimentoVigilancia());
            tblTipoProjeto.populate();
            //txtNumeroPHSAprovado.setRequired(true);

            if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList())) {
                boolean contains;
                StringBuilder builder = new StringBuilder();
                for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList()) {
                    contains = Valor.resolveSomatorio(tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getTipo()).contains(TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE.value());
                    if (!contains) {
                        builder.append("O Tipo de Projeto '" + tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getDescricao() + "' do Projeto Hidrossanitário Aprovado não está habilitado para o Habite-se. Aréa: " + tipoProjetoRequerimentoVigilancia.getArea());
                        tipoProjetoRequerimentoVigilancia.setTipoProjetoVigilancia(null);

//                        try {
//                            throw new ValidacaoException("O Tipo de Projeto '" + tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getDescricao() + "' do Projeto Hidrossanitário Aprovado não está habilitado para o Habite-se");
//                        } catch (ValidacaoException e) {
//                            if (target != null) {
//                                modalWarn(target, e);
//                                autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
//                                form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().clear();
//                                tblTipoProjeto.update(target);
//                            }
//                            return;
//                        }
                    }
                }
                if (StringUtils.trimToNull(builder.toString()) != null) {
                    if (target != null) {
                        info(target, builder.toString());
                    }
                    form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().clear();
                    if (target != null) {
                        tblTipoProjeto.populate(target);
                    }
                }
            }

            if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList())) {
                List<TipoProjetoRequerimentoVigilancia> list = (List<TipoProjetoRequerimentoVigilancia>) SerializationUtils.clone((Serializable) form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList());
                form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().clear();
                for (TipoProjetoRequerimentoVigilancia elo : list) {
                    TipoProjetoRequerimentoVigilancia eloClone = VOUtils.cloneObject(elo);
                    eloClone.setRequerimentoVigilancia(null);
                    form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().add(eloClone);
                }
            }

            if (target != null) {
                target.add(tblTipoProjeto);
                target.add(autoCompleteConsultaTipoProjetoVigilancia);
                target.add(txtArea);
            }

            // Grupo Dados da Obra
            if (requerimentoProjetoHidrossanitario.getVigilanciaEndereco() != null) {
                VigilanciaEndereco endereco = LoadManager.getInstance(VigilanciaEndereco.class)
                        .addProperties(new HQLProperties(VigilanciaEndereco.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CODIGO, requerimentoProjetoHidrossanitario.getVigilanciaEndereco().getCodigo()))
                        .start().getVO();
                pnlVigilanciaEndereco.setModelObject(endereco);
                form.getModelObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().setVigilanciaEndereco(endereco);
            }

            txtObraNumeroEndereco.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroEndereco());
            txtObraQuadra.setComponentValue(requerimentoProjetoHidrossanitario.getObraQuadra());
            txtObraNumeroLado.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroLado());
            txtObraLote.setComponentValue(requerimentoProjetoHidrossanitario.getObraLote());
            txtObraComplemento.setComponentValue(requerimentoProjetoHidrossanitario.getObraComplemento());
            txtObraNumeroLoteamento.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroLoteamento());

            if (target != null) {
                target.add(pnlVigilanciaEndereco);
                target.add(txtObraNumeroEndereco);
                target.add(txtObraQuadra);
                target.add(txtObraNumeroLado);
                target.add(txtObraLote);
                target.add(txtObraComplemento);
                target.add(txtObraNumeroLoteamento);

                // Grupo Informações
//                txtNumeroProcessoProjeto.limpar(target);
//                dropDownRegiaoCobertaRedeEsgoto.limpar(target);
                dropDownRegiaoAbastecidaAgua.limpar(target);
                dropDownSistemaAguaPluvial.limpar(target);
            }

//            txtNumeroProcessoProjeto.setComponentValue(requerimentoProjetoHidrossanitario.getNumeroProcessoProjeto());
//            dropDownRegiaoCobertaRedeEsgoto.setComponentValue(requerimentoProjetoHidrossanitario.getRegiaoCobertaRedeEsgoto());
            dropDownRegiaoAbastecidaAgua.setComponentValue(requerimentoProjetoHidrossanitario.getRegiaoAbastecidaAgua());
            dropDownSistemaAguaPluvial.setComponentValue(requerimentoProjetoHidrossanitario.getSistemaAguaPluvial());

            if (target != null) {
//                target.add(txtNumeroProcessoProjeto);
//                target.add(dropDownRegiaoCobertaRedeEsgoto);
                target.add(dropDownRegiaoAbastecidaAgua);
                target.add(dropDownSistemaAguaPluvial);
            }

            // Grupo Enquadramento
            autoCompleteConsultaTipoEnquadramentoProjeto.setComponentValue(requerimentoProjetoHidrossanitario.getTipoEnquadramentoProjeto());
            if (target != null) {
                target.add(autoCompleteConsultaTipoEnquadramentoProjeto);
            }

            // Grupo Uso da Edificação
            dropDownUsoEdificacao.setComponentValue(requerimentoProjetoHidrossanitario.getUsoEdificacao());
            if (target != null) {
                target.add(dropDownUsoEdificacao);
            }
            habilitarUsoEdificacao(target);
            txtObservacaoUsoEdificacao.setComponentValue(requerimentoProjetoHidrossanitario.getObservacaoUsoEdificacao());
            if (target != null) {
                target.add(txtObservacaoUsoEdificacao);
            }

            // Grupo Parcelamento do Solo
            habilitarParcelamentoSolo(target);
            txtNumeroLotesParcelamento.setComponentValue(requerimentoProjetoHidrossanitario.getParcelamentoSoloNumeroLotes());
            if (target != null) {
                target.add(txtNumeroLotesParcelamento);
            }

            // Grupo Metragem
            habilitarMetragem(target);
            txtAreaComercial.setComponentValue(requerimentoProjetoHidrossanitario.getAreaComercial());
            txtAreaResidencial.setComponentValue(requerimentoProjetoHidrossanitario.getAreaResidencial());
            txtAreaTotal.setComponentValue(requerimentoProjetoHidrossanitario.getAreaTotalConstrucao());

            List<EloRequerimentoVigilanciaResponsavelTecnico> responsavelTecnicoList = LoadManager.getInstance(EloRequerimentoVigilanciaResponsavelTecnico.class)
                    .addProperties(new HQLProperties(EloRequerimentoVigilanciaResponsavelTecnico.class).getProperties())
                    .addProperties(new HQLProperties(ResponsavelTecnico.class, EloRequerimentoVigilanciaResponsavelTecnico.PROP_RESPONSAVEL_TECNICO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA, requerimentoProjetoHidrossanitario.getRequerimentoVigilancia()))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(responsavelTecnicoList)) {
                for (EloRequerimentoVigilanciaResponsavelTecnico elo : responsavelTecnicoList) {
                    EloRequerimentoVigilanciaResponsavelTecnico eloClone = VOUtils.cloneObject(elo);
                    eloClone.setRequerimentoVigilancia(null);
                    form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().add(eloClone);
                }
            }


            List<RequerimentoVigilanciaInscricaoImob> inscricaoList = LoadManager.getInstance(RequerimentoVigilanciaInscricaoImob.class)
                    .addProperties(new HQLProperties(RequerimentoVigilanciaInscricaoImob.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA, requerimentoProjetoHidrossanitario.getRequerimentoVigilancia()))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(inscricaoList)) {
                for (RequerimentoVigilanciaInscricaoImob elo : inscricaoList) {
                    RequerimentoVigilanciaInscricaoImob eloClone = VOUtils.cloneObject(elo);
                    eloClone.setRequerimentoVigilancia(null);
                    form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList().add(eloClone);
                }
            }

            if (target != null) {
                target.add(tblResponsavelTecnico);
                target.add(tblInscricaoImobiliaria);
            }
        } else {
            form.getModelObject().setEloRequerimentoVigilanciaResponsavelTecnicoList(new ArrayList<EloRequerimentoVigilanciaResponsavelTecnico>());
            form.getModelObject().setRequerimentoVigilanciaInscricaoImobList(new ArrayList<RequerimentoVigilanciaInscricaoImob>());
            form.getModelObject().setTipoProjetoRequerimentoVigilanciaList(new ArrayList<TipoProjetoRequerimentoVigilancia>());
            if (target != null) {
                tblInscricaoImobiliaria.update(target);
                tblResponsavelTecnico.update(target);
                tblTipoProjeto.update(target);
            }
            habilitarUsoEdificacao(target);
            habilitarParcelamentoSolo(target);
            habilitarMetragem(target);
        }
        //pnlDadosComumRequerimentoVigilancia.getTblFiscais().update(target);
    }

    private void habilitarUsoEdificacao(AjaxRequestTarget target) {
        if (target != null) {
            txtObservacaoUsoEdificacao.limpar(target);
        }

        if (RequerimentoVistoriaHidrossanitarioDeclaratorio.UsoEdificacao.OUTROS.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao())) {
            txtObservacaoUsoEdificacao.setEnabled(true);
            txtObservacaoUsoEdificacao.setRequired(true);
            txtObservacaoUsoEdificacao.addRequiredClass();
        } else {
            txtObservacaoUsoEdificacao.setEnabled(false);
            txtObservacaoUsoEdificacao.setRequired(false);
            txtObservacaoUsoEdificacao.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtObservacaoUsoEdificacao);
        }
    }

    private void habilitarParcelamentoSolo(AjaxRequestTarget target) {
        if (target != null) {
            txtNumeroLotesParcelamento.limpar(target);
            txtNumeroProjetoUrbanistico.limpar(target);
            txtNumeroLicitacaoAmbiental.limpar(target);
            txtNumeroProjetoEsgoto.limpar(target);
            txtNumeroProjetoAgua.limpar(target);
        }

        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao() != null
                && (RequerimentoVistoriaHidrossanitarioDeclaratorio.UsoEdificacao.LOTEAMENTO.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao())
                || RequerimentoVistoriaHidrossanitarioDeclaratorio.UsoEdificacao.CONDOMINIO.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao()))) {
            containerParcelamentoSolo.setVisible(true);
            txtNumeroLotesParcelamento.setRequired(true);
            txtNumeroLotesParcelamento.addRequiredClass();

            txtNumeroProjetoUrbanistico.setRequired(true);
            txtNumeroProjetoUrbanistico.addRequiredClass();

            if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamento(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao())) {
                txtNumeroProjetoEsgoto.setRequired(true);
                txtNumeroProjetoEsgoto.addRequiredClass();
            } else {
                txtNumeroProjetoEsgoto.setRequired(false);
                txtNumeroProjetoEsgoto.removeRequiredClass();
            }

            txtNumeroProjetoAgua.setRequired(true);
            txtNumeroProjetoAgua.addRequiredClass();

//            if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProcessoProjeto() != null) {
//                txtNumeroProjetoUrbanistico.setComponentValue(StringUtil.getDigits(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProcessoProjeto()));
//            }
        } else {
            containerParcelamentoSolo.setVisible(false);
            txtNumeroLotesParcelamento.setRequired(false);
            txtNumeroLotesParcelamento.removeRequiredClass();

            txtNumeroProjetoUrbanistico.setRequired(false);
            txtNumeroProjetoUrbanistico.removeRequiredClass();

            txtNumeroProjetoEsgoto.setRequired(false);
            txtNumeroProjetoEsgoto.removeRequiredClass();

            txtNumeroProjetoAgua.setRequired(false);
            txtNumeroProjetoAgua.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtNumeroLotesParcelamento);
            target.add(containerParcelamentoSolo);
            target.add(txtNumeroProjetoUrbanistico);
            target.add(txtNumeroLicitacaoAmbiental);
            target.add(txtNumeroProjetoEsgoto);
            target.add(txtNumeroProjetoAgua);
        }
    }

    private void habilitarMetragem(AjaxRequestTarget target) {
        if (target != null) {
            txtAreaComercial.limpar(target);
            txtAreaResidencial.limpar(target);
        }

        if (RequerimentoVistoriaHidrossanitarioDeclaratorio.UsoEdificacao.MISTA.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao())) {
            txtAreaComercial.setEnabled(true);
            txtAreaComercial.setRequired(true);
            txtAreaComercial.addRequiredClass();

            txtAreaResidencial.setEnabled(true);
            txtAreaResidencial.setRequired(true);
            txtAreaResidencial.addRequiredClass();
        } else {
            txtAreaComercial.setEnabled(false);
            txtAreaComercial.setRequired(false);
            txtAreaComercial.removeRequiredClass();

            txtAreaResidencial.setEnabled(false);
            txtAreaResidencial.setRequired(false);
            txtAreaResidencial.removeRequiredClass();
        }

        if (target != null) {
            target.add(txtAreaComercial);
            target.add(txtAreaResidencial);
        }
    }

    private void calcularAreaTotalConstrucao(AjaxRequestTarget target) {
        Double totalArea = 0D;
        if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList())) {
            for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList()) {
                totalArea = new Dinheiro(totalArea).somar(new Dinheiro(tipoProjetoRequerimentoVigilancia.getArea())).doubleValue();
            }
        }
        txtAreaTotal.setComponentValue(totalArea);
        target.add(txtAreaTotal);
    }


    private void validarAreaTotalConstrucao(AjaxRequestTarget target) {
        double areaSomada;
        if (RequerimentosProjetosEnums.UsoEdificacao.MISTA.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getUsoEdificacao())
                && form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaComercial() != null
                && form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaResidencial() != null) {
            areaSomada = new Dinheiro(Coalesce.asDouble(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaComercial()))
                    .somar(Coalesce.asDouble(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaResidencial())).doubleValue();
            if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getAreaTotalConstrucao().compareTo(areaSomada) != 0) {
                modalWarn(target, new ValidacaoException(bundle("msgValorSomadoAreaComercialAreaResidencialDiferenteDoTotalCalculado")));
            }
        }
    }

    private DropDown getDropDownTipoPessoa(RequerimentoVistoriaHidrossanitarioDeclaratorioDTO proxy) {
        dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente()), RequerimentoVigilancia.TipoRequerente.values(), false, true);
        dropDownTipoPessoa.addAjaxUpdateValue();
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCamposDadosGeral(target, true);
                habilitarCampoProjetoHidroManual(target, null, true);
            }
        });
        return dropDownTipoPessoa;
    }

    private WebMarkupContainer getContainerPessoa(RequerimentoVistoriaHidrossanitarioDeclaratorioDTO proxy) {
        containerPessoa = new WebMarkupContainer("containerPessoa");
        containerPessoa.setOutputMarkupPlaceholderTag(true);

        containerPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa()), true));
        autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        try {
            autoCompleteConsultaVigilanciaPessoa.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoPessoaRequerimentoExterno")));
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        autoCompleteConsultaVigilanciaPessoa.setLabel(new Model(bundle("pessoa")));
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, object);
                habilitarCampoProjetoHidroVigPessoa(target, object);
                habilitarCampoProjetoHidroManual(target, null, true);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, null);
                habilitarCampoProjetoHidroVigPessoa(target, null);
                habilitarCampoProjetoHidroManual(target, null, true);
            }
        });

        containerPessoa.add(txtEmailPessoa = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail())));
        txtEmailPessoa.setEnabled(false);
        txtEmailPessoa.setLabel(new Model(bundle("email")));

        containerPessoa.add(new AbstractAjaxLink("btnCadPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId(), true) {
                    @Override
                    public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                        autoCompleteConsultaVigilanciaPessoa.limpar(target);
                        autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);
                        autoCompleteConsultaVigilanciaEndereco.limpar(target);
                        VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(vigilanciaPessoa.getVigilanciaEndereco().getCodigo()).start().getVO();
                        form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
                        target.add(autoCompleteConsultaVigilanciaEndereco);

                        txtEmailPessoa.limpar(target);
                        VigilanciaPessoa vp = LoadManager.getInstance(VigilanciaPessoa.class)
                                .addProperty(VigilanciaPessoa.PROP_EMAIL)
                                .setId(vigilanciaPessoa.getCodigo())
                                .start().getVO();
                        txtEmailPessoa.setComponentValue(vp.getEmail());
                        target.add(txtEmailPessoa);
                        autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
                        target.focusComponent(autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().getTextField());
                    }
                });
                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        });
        return containerPessoa;
    }

    private void habilitarCampoProjetoHidroVigPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        if (target != null) {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
            txtNumeroProjetoHidrossanitario.limpar(target);
        }

        if (vigilanciaPessoa != null) {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(true);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(vigilanciaPessoa);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(null);
        } else {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(false);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(null);
        }

        if (target != null) {
            target.add(autoCompleteConsultaRequerimentoProjetoHidrossanitario);
            target.add(txtNumeroProjetoHidrossanitario);
        }
    }

    private void atualizarEnderecoPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        autoCompleteConsultaVigilanciaEndereco.limpar(target);
        txtEmailPessoa.limpar(target);

        if (vigilanciaPessoa != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);

            VigilanciaPessoa vp = LoadManager.getInstance(VigilanciaPessoa.class)
                    .addProperty(VigilanciaPessoa.PROP_EMAIL)
                    .setId(vigilanciaPessoa.getCodigo())
                    .start().getVO();

            if (vp.getEmail() == null) {
                txtEmailPessoa.setEnabled(true);
                txtEmailPessoa.setRequired(true);
                txtEmailPessoa.addRequiredClass();
            } else {
                txtEmailPessoa.setRequired(false);
                txtEmailPessoa.setEnabled(false);
                txtEmailPessoa.removeRequiredClass();
            }
            txtEmailPessoa.setComponentValue(vp.getEmail());
            target.add(txtEmailPessoa);
        }
    }

    private WebMarkupContainer getContainerEstabelecimento(RequerimentoVistoriaHidrossanitarioDeclaratorioDTO proxy) {
        containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
        containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        try {
            autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, object);
                habilitarCampoProjetoHidroEstabelecimento(target, object);
                habilitarCampoProjetoHidroManual(target, null, true);
                verificarEstabelecimentoIsento(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, null);
                habilitarCampoProjetoHidroEstabelecimento(target, object);
                habilitarCampoProjetoHidroManual(target, null, true);
                autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
                target.add(autoCompleteConsultaTipoProjetoVigilancia);
                lbl.setVisible(false);
                target.add(lbl);
            }
        });

        containerEstabelecimento.add(txtEmailEstabelecimento = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento().getEmail())));
        txtEmailEstabelecimento.setEnabled(false);
        txtEmailEstabelecimento.setLabel(new Model(bundle("email")));

        containerEstabelecimento.add(new AbstractAjaxLink("btnCadEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoHabiteseDeclaratorioExternoPage.this.setResponsePage(new CadastroEstabelecimentoExternoPage(tipoSolicitacao, true));
            }
        });

        return containerEstabelecimento;
    }

    private void habilitarCampoProjetoHidroEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (target != null) {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
            txtNumeroProjetoHidrossanitario.limpar(target);
        }

        if (estabelecimento != null) {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(true);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(estabelecimento);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(null);
        } else {
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(false);
            autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(null);
        }

        if (target != null) {
            target.add(autoCompleteConsultaRequerimentoProjetoHidrossanitario);
            target.add(txtNumeroProjetoHidrossanitario);
        }
    }

    private void atualizarEnderecoEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (target != null) {
            autoCompleteConsultaVigilanciaEndereco.limpar(target);
            txtEmailEstabelecimento.limpar(target);
        }

        if (estabelecimento != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            if (target != null) {
                autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
            }

            Estabelecimento e = LoadManager.getInstance(Estabelecimento.class)
                    .addProperty(Estabelecimento.PROP_EMAIL)
                    .setId(estabelecimento.getCodigo())
                    .start().getVO();
            if (e.getEmail() == null) {
                txtEmailEstabelecimento.setEnabled(true);
                txtEmailEstabelecimento.setRequired(true);
                txtEmailEstabelecimento.addRequiredClass();
            } else {
                txtEmailEstabelecimento.setRequired(false);
                txtEmailEstabelecimento.setEnabled(false);
                txtEmailEstabelecimento.removeRequiredClass();
            }
            txtEmailEstabelecimento.setComponentValue(e.getEmail());
            if (target != null) {
                target.add(txtEmailEstabelecimento);
            }
        }
    }

    private void addCamposEndereco(RequerimentoVistoriaHidrossanitarioDeclaratorioDTO proxy) {
        containerDadosGerais.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaEndereco()), true));
        autoCompleteConsultaVigilanciaEndereco.setLabel(new Model(bundle("endereco")));
        autoCompleteConsultaVigilanciaEndereco.setEnabled(false);
    }

    private void enableCamposDadosGeral(AjaxRequestTarget target, boolean limparCampos) {
        containerPessoa.setEnabled(enabled);
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente())) {
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setVigilanciaPessoa(null);
            containerEstabelecimento.setVisible(true);
            containerPessoa.setVisible(false);
        } else {
            form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setEstabelecimento(null);
            containerEstabelecimento.setVisible(false);
            containerPessoa.setVisible(true);
        }

        if (target != null) {
            if (limparCampos) {
                autoCompleteConsultaEstabelecimento.limpar(target);
                autoCompleteConsultaVigilanciaPessoa.limpar(target);
                autoCompleteConsultaVigilanciaEndereco.limpar(target);
            }
            target.add(containerDadosGerais);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        RequerimentoVistoriaHidrossanitarioDeclaratorioDTO dto = form.getModel().getObject();

        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente())) {
            if (dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento() == null) {
                throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabelecimento"));
            }
            if (txtEmailEstabelecimento.isEnabled()) {
                if (!EmailValidator.validarEmail(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento().getEmail())) {
                    throw new ValidacaoException(bundle("email_invalido"));
                }
                Estabelecimento estabelecimentoSave = BOFactoryWicket.save(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEstabelecimento());
                if (estabelecimentoSave != null) {
                    form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setEstabelecimento(estabelecimentoSave);
                }
            }
        }
        if (RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente())) {
            if (dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa() == null) {
                throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarPessoa"));
            }
            if (txtEmailPessoa.isEnabled()) {
                if (!EmailValidator.validarEmail(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail())) {
                    throw new ValidacaoException(bundle("email_invalido"));
                }
                VigilanciaPessoa vigilanciaPessoa = BOFactoryWicket.save(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getVigilanciaPessoa());
                if (vigilanciaPessoa != null) {
                    form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
                }
            }
        }
        if (!EmailValidator.validarEmail(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getEmailSolicitante())) {
            throw new ValidacaoException(bundle("email_invalido"));
        }
        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoProjetoHidrossanitarioAprovado() == null
                && form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getNumeroProjetoAprovado() == null) {
            throw new ValidacaoException(bundle("msgInformeCampoNumeroProjetoHidrossanitarioAprovado"));
        }
        if (form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }

        if (CollectionUtils.isEmpty(form.getModel().getObject().getEloRequerimentoVigilanciaResponsavelTecnicoList())) {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmResponsavelTecnico"));
        }

        if (CollectionUtils.isEmpty(form.getModel().getObject().getRequerimentoVigilanciaInscricaoImobList())) {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmaInscricaoImobiliaria"));
        }
        if (validarRegistrosInvalidosRT()) {
            throw new ValidacaoException(bundle("msgCadastrosTDInvalido"));
        }

        if (CollectionUtils.isEmpty(form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList())) {
            try {
                throw new ValidacaoException(bundle("msgInformeAoMenosUmTipoProjeto"));
            } catch (ValidacaoException e) {
                modalWarn(target, e);
            }
            return;
        }

        dto.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        dto.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        dto.getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVistoriaHidrossanitarioDeclaratorio(form.getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        gerarBoleto(target, rv, configuracaoVigilancia);

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList<>();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                if (VigilanciaHelper.abrirTelaFinanceiro(object)) {
                    RequerimentoVigilanciaDTO dto = new RequerimentoVigilanciaDTO();
                    dto.setRequerimentoVigilancia(object);
                    setResponsePage(new BoletoVigilanciaExternoPage(dto, classReturn));
                } else {
                    try {
                        Page pageReturn = (Page) classReturn.newInstance();
                        getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                        setResponsePage(pageReturn);
                    } catch (InstantiationException | IllegalAccessException e) {
                        Loggable.log.error(e.getMessage(), e);
                    }
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void gerarBoleto(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia, ConfiguracaoVigilancia configuracaoVigilancia) throws ValidacaoException, DAOException {
        String boletoBase64RequerimentoExterno = FinanceiroVigilanciaHelper.getBoletoBase64RequerimentoExterno(requerimentoVigilancia, configuracaoVigilancia);

        if (boletoBase64RequerimentoExterno != null) {
            ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64RequerimentoExterno);
        }
    }

    private void carregarRequerimentoVistoriaHidrossanitarioDeclaratorio(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoVistoriaHidrossanitarioDeclaratorio = LoadManager.getInstance(RequerimentoVistoriaHidrossanitarioDeclaratorio.class)
                    .addProperties(new HQLProperties(RequerimentoVistoriaHidrossanitarioDeclaratorio.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoProjetoHidrossanitario.class, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_APROVADO).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO_APROVADO, RequerimentoProjetoHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_PESSOA)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(TipoEnquadramentoProjeto.class, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_TIPO_ENQUADRAMENTO_PROJETO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaAltoBaixoRisco.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_VIGILANCIA_ALTO_BAIXO_RISCO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoVistoriaHidrossanitarioDeclaratorio.setRequerimentoVigilancia(requerimentoVigilancia);
            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarTipoProjetoRequerimentoVigilanciaList(RequerimentoVigilancia requerimentoVigilancia) {
        List<TipoProjetoRequerimentoVigilancia> list = LoadManager.getInstance(TipoProjetoRequerimentoVigilancia.class)
                .addProperties(new HQLProperties(TipoProjetoRequerimentoVigilancia.class).getProperties())
                .addProperties(new HQLProperties(TipoProjetoVigilancia.class, TipoProjetoRequerimentoVigilancia.PROP_TIPO_PROJETO_VIGILANCIA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProjetoRequerimentoVigilancia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(list)) {
            form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().addAll(list);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void carregarEloRequerimentoVigilanciaResponsavelTecnico(RequerimentoVigilancia requerimentoVigilancia) {
        List<EloRequerimentoVigilanciaResponsavelTecnico> elos = LoadManager.getInstance(EloRequerimentoVigilanciaResponsavelTecnico.class)
                .addProperties(new HQLProperties(EloRequerimentoVigilanciaResponsavelTecnico.class).getProperties())
                .addProperty(VOUtils.montarPath(EloRequerimentoVigilanciaResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_CPF))
                .addParameter(new QueryCustom.QueryCustomParameter(EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(elos)) {
            form.getModelObject().getEloRequerimentoVigilanciaResponsavelTecnicoList().addAll(elos);
        }
    }

    private void carregarInscricoesImob(RequerimentoVigilancia requerimentoVigilancia) {
        List<RequerimentoVigilanciaInscricaoImob> inscricaoImobList = LoadManager.getInstance(RequerimentoVigilanciaInscricaoImob.class)
                .addProperties(new HQLProperties(RequerimentoVigilanciaInscricaoImob.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(inscricaoImobList)) {
            form.getModelObject().getRequerimentoVigilanciaInscricaoImobList().addAll(inscricaoImobList);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("habiteseDeclaratorio");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia().getTipoRequerente())) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
        } else {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaVigilanciaPessoa.getTxtDescricao().getTextField())));
        }
    }

    @Override
    public Permissions getAction() {
        return Permissions.HABITE_SE_DECLARATORIO;
    }

    private void verificarEstabelecimentoIsento(AjaxRequestTarget target, Estabelecimento object) {
        if (Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getTipoEmpresa())) {
            if (VigilanciaHelper.isIsentoPorLei(object)) {
                lbl.setDefaultModel(Model.of("Esta empresa está configurada como “Isenta por lei” de taxas, portanto será necessário anexar o(s) documento(s) comprobatório(s) desta isenção."));
                lbl.setVisible(true);
                target.add(lbl);
            }
        }
    }
}
