package br.com.celk.view.unidadesaude.atividadegrupo.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.tipoatividadegrupo.autocomplete.AutoCompleteConsultaTipoAtividadeGrupo;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.atividadegrupo.interfaces.dto.RelatorioRelacaoProcedimentoAtividadeDTOParam;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoProcedimentoAtividadePage extends RelatorioPage<RelatorioRelacaoProcedimentoAtividadeDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown quantidadePor;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaTipoAtividadeGrupo("tipoAtividadeGrupo")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTabelaCbo("tabelaCbo")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProcedimento("procedimento")
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioRelacaoProcedimentoAtividadeDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoRelatorio", RelatorioRelacaoProcedimentoAtividadeDTOParam.TipoRelatorio.values()));
        form.add(quantidadePor = DropDownUtil.getEnumDropDown("quantidadePor", RelatorioRelacaoProcedimentoAtividadeDTOParam.QuantidadePor.values()));
        
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoProcedimentoAtividadeDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoProcedimentoAtividadeDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioRelacaoProcedimentoAtividade(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoProcedimentosAtividades");
    }
}