package br.com.celk.view.vigilancia.requerimentovigilancia.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.requerimentovigilancia.restricaocontainer.RestricaoContainerRequerimentoProjetoHidrossanitario;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.model.IModel;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 03/01/19.
 */
public class AutoCompleteConsultaRequerimentoProjetoHidrossanitario extends AutoCompleteConsulta<RequerimentoProjetoHidrossanitario> {

    private Long situacao;
    private Estabelecimento estabelecimento;
    private VigilanciaPessoa vigilanciaPessoa;

    public AutoCompleteConsultaRequerimentoProjetoHidrossanitario(String id) {
        super(id);
    }

    public AutoCompleteConsultaRequerimentoProjetoHidrossanitario(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaRequerimentoProjetoHidrossanitario(String id, IModel<RequerimentoProjetoHidrossanitario> model) {
        super(id, model);
    }

    public AutoCompleteConsultaRequerimentoProjetoHidrossanitario(String id, IModel<RequerimentoProjetoHidrossanitario> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(RequerimentoProjetoHidrossanitario.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("protocolo"), VOUtils.montarPath(RequerimentoProjetoHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_PROTOCOLO_FORMATADO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataRequerimento"), VOUtils.montarPath(RequerimentoProjetoHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_DATA_REQUERIMENTO_FORMATADO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerRequerimentoProjetoHidrossanitario(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<RequerimentoProjetoHidrossanitario, QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarRequerimentoProjetoHidrossanitario(dataPaging);
                    }

                    @Override
                    public QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam param = new QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaRequerimentoProjetoHidrossanitarioDTOParam param) {
                        param.setSituacao(situacao);
                        param.setEstabelecimento(estabelecimento);
                        param.setVigilanciaPessoa(vigilanciaPessoa);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return RequerimentoProjetoHidrossanitario.class;
            }

        };
    }

    @Override
    public String[] getPropertiesLoad() {
        RequerimentoProjetoHidrossanitario proxy = on(RequerimentoProjetoHidrossanitario.class);

        return VOUtils.mergeProperties(new HQLProperties(RequerimentoProjetoHidrossanitario.class).getProperties(),
                new HQLProperties(RequerimentoVigilancia.class, path(proxy.getRequerimentoVigilancia())).getProperties());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("requerimentos");
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public void setVigilanciaPessoa(VigilanciaPessoa vigilanciaPessoa) {
        this.vigilanciaPessoa = vigilanciaPessoa;
    }
}