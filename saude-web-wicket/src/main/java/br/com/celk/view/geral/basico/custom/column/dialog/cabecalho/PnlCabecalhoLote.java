package br.com.celk.view.geral.basico.custom.column.dialog.cabecalho;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.integracao.mobile.IntegracaoMobile;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

import java.text.SimpleDateFormat;


/**
 * <AUTHOR>
 */
public class PnlCabecalhoLote extends Panel {

    private IntegracaoMobile integracaoMobile;
    private Label lblIdLote;
    private String textoLote;

    public PnlCabecalhoLote(final String id) {
        super(id);
        init();
    }

    public PnlCabecalhoLote(final String id, IntegracaoMobile integracaoMobile) {
        super(id);
        this.integracaoMobile = integracaoMobile;
        init();
    }

    public PnlCabecalhoLote(final String id, String textoLote) {
        super(id);
        this.textoLote = textoLote;
        init();
    }


    private void init() {
        initLabel();

        add(lblIdLote = new Label("lblIdLote", new PropertyModel<String>(this, "textoLote")));

        lblIdLote.setOutputMarkupId(true);
        setOutputMarkupId(true);

    }

    private void initLabel(){
        if(integracaoMobile != null){
            textoLote = "Lote: "
                    + Valor.adicionarFormatacaoMonetaria(integracaoMobile.getCodigo(), 0) +
                    " | Profissional: "
                    + integracaoMobile.getProfissional().getDescricaoFormatado() +
                    " | Tipo da Integração: "
                    + integracaoMobile.getDescricaoTipoIntegracao() +
                    " | Data da Integração: "
                    + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(integracaoMobile.getDataIntegracao());
        }else if(StringUtils.isEmpty(textoLote)){
            textoLote = "Sem Dados";
        }
    }

    public void setIntegracaoMobile(AjaxRequestTarget target, IntegracaoMobile integracaoMobileItem) {
        this.integracaoMobile = integracaoMobileItem;
        initLabel();
        target.add(lblIdLote);
    }

    public void setTextoLote(AjaxRequestTarget target, String textoLote) {
        this.textoLote = textoLote;
        initLabel();
        target.add(lblIdLote);
    }
}
