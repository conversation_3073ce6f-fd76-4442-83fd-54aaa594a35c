package br.com.celk.view.basico.tipoexameimportacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExameImportacao;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroTipoExameImportacaoPage extends CadastroPage<TipoExameImportacao> {

    private InputField<String> txtDescricao;

    public CadastroTipoExameImportacaoPage(PageParameters parameters) {
        super(parameters);
    }

    public CadastroTipoExameImportacaoPage(TipoExameImportacao object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoExameImportacaoPage(TipoExameImportacao object) {
        this(object, false);
    }

    @Override
    public void init(Form form) {
        form.add(this.txtDescricao = new RequiredInputField<>(TipoExameImportacao.PROP_DESCRICAO));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<TipoExameImportacao> getReferenceClass() {
        return TipoExameImportacao.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoExameImportacaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoExameImportacao");
    }
}
