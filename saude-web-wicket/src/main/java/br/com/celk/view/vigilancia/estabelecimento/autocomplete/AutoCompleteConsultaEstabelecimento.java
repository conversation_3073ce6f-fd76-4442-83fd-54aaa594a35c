package br.com.celk.view.vigilancia.estabelecimento.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.restricaocontainer.RestricaoContainerEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.settings.EstabelecimentoAutoCompleteSettings;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaEstabelecimentoDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 * The type Auto complete consulta estabelecimento.
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaEstabelecimento extends AutoCompleteConsulta<Estabelecimento> {

    private boolean filtrarUsuarioLogado;
    private boolean exibirProvisorios; //Protocolos internos ou externos exibe provisórios, porém no SaveRequerimentoVigilancia trava-se o fluxo.
    private boolean exibirNaoAutorizados;
    private boolean vigilancia;

    public AutoCompleteConsultaEstabelecimento(String id) {
        super(id);
        onListeners();
    }

    public AutoCompleteConsultaEstabelecimento(String id, boolean required) {
        super(id, required);
        onListeners();
    }

    public AutoCompleteConsultaEstabelecimento(String id, IModel<Estabelecimento> model) {
        super(id, model);
        onListeners();
    }

    public AutoCompleteConsultaEstabelecimento(String id, IModel<Estabelecimento> model, boolean required) {
        super(id, model, required);
        onListeners();
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(Estabelecimento.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("razaoSocial"), VOUtils.montarPath(Estabelecimento.PROP_RAZAO_SOCIAL)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("fantasia"), VOUtils.montarPath(Estabelecimento.PROP_FANTASIA)));
                columns.add(columnFactory.createColumn(BundleManager.getString("cnpjCpf"), VOUtils.montarPath(Estabelecimento.PROP_CNPJ_CPF_FORMATADO_POR_ESTABELECIMENTO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerEstabelecimento(id);
            }
            
            @Override
            public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
                return new EstabelecimentoAutoCompleteSettings();
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Estabelecimento, QueryConsultaEstabelecimentoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaEstabelecimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarEstabelecimento(dataPaging);
                    }

                    @Override
                    public QueryConsultaEstabelecimentoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaEstabelecimentoDTOParam param = new QueryConsultaEstabelecimentoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaEstabelecimentoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setFiltrarUsuarioLogado(isFiltrarUsuarioLogado());
                        param.setExibirProvisorios(isExibirProvisorios());
                        param.setExibirNaoAutorizados(isExibirNaoAutorizados());
                        param.setSomenteVigilancia(isExibirSomenteVigilancia());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Estabelecimento.PROP_RAZAO_SOCIAL), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return Estabelecimento.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("estabelecimento");
    }
    
    @Override
    public String[] getPropertiesLoad() {
        return VOUtils.mergeProperties(new HQLProperties(Estabelecimento.class).getProperties(),
                new HQLProperties(VigilanciaEndereco.class, Estabelecimento.PROP_VIGILANCIA_ENDERECO).getProperties(),
                new HQLProperties(Estabelecimento.class, Estabelecimento.PROP_ESTABELECIMENTO_PRINCIPAL).getProperties(),
                new HQLProperties(Cidade.class, VOUtils.montarPath(Estabelecimento.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties(),
                new HQLProperties(Estado.class, VOUtils.montarPath(Estabelecimento.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties());
    }

    private void onListeners() {
        add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                if (!Estabelecimento.Situacao.ATIVO.value().equals(object.getSituacao())) {
                    setTitleTip(BundleManager.getString("estabelecimentoX", object.getDescricaoSituacao()));
                }
            }
        });

        add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                removeTitleTip();
            }
        });
    }

    public boolean isFiltrarUsuarioLogado() {
        return filtrarUsuarioLogado;
    }

    public void setFiltrarUsuarioLogado(boolean filtrarUsuarioLogado) {
        this.filtrarUsuarioLogado = filtrarUsuarioLogado;
    }

    public boolean isExibirProvisorios() {
        return exibirProvisorios;
    }
    /**
     * Sets exibir provisorios.
     *
     * @param exibirProvisorios default value *true
     */
    public void setExibirProvisorios(boolean exibirProvisorios) {
        this.exibirProvisorios = exibirProvisorios;
    }

    public boolean isExibirNaoAutorizados() {
        return exibirNaoAutorizados;
    }

    public void setExibirNaoAutorizados(boolean exibirNaoAutorizados) {
        this.exibirNaoAutorizados = exibirNaoAutorizados;
    }

    public boolean isExibirSomenteVigilancia() {
        return vigilancia;
    }

    public void setExibirSomenteVigilancia(boolean exibirSomenteVigilancia) {
        this.vigilancia = vigilancia;
    }
}
