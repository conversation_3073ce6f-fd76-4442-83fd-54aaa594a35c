package br.com.celk.view.atividadegrupo.copiarAtividadeGrupo;

import br.com.celk.atividadegrupo.dto.CopiaAtividadeGrupoDTO;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBox;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.core.javascript.helper.DateHelper;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlCopiarAtividadeGrupo extends Panel {

    private Form<CopiaAtividadeGrupoDTO> form;
    private AtividadeGrupo atividadeGrupo;
    private DropDown dropDownSemanasRepeticao;
    private InputField inputDataBase;
    private DateChooserAjax dateChooserAjaxInicio;
    private DateChooserAjax dateChooserAjaxTermino;
    private CheckBox chkSabado;
    private CheckBox chkDomingo;
    private CheckBox chkSegunda;
    private CheckBox chkTerca;
    private CheckBox chkQuarta;
    private CheckBox chkQuinta;
    private CheckBox chkSexta;

    public PnlCopiarAtividadeGrupo(String id) {
        super(id);
        init();
    }

    private void init() {
        try {

            form = new Form<CopiaAtividadeGrupoDTO>("form", new CompoundPropertyModel<CopiaAtividadeGrupoDTO>(new CopiaAtividadeGrupoDTO()));
            CopiaAtividadeGrupoDTO proxy = on(CopiaAtividadeGrupoDTO.class);

            form.add(inputDataBase = new InputField(path(proxy.getDescricaoDataBase())));
            inputDataBase.setEnabled(false);
            form.add(chkSabado = new CheckBox(path(proxy.isSabado())));
            form.add(chkDomingo = new CheckBox(path(proxy.isDomingo())));
            form.add(chkSegunda = new CheckBox(path(proxy.isSegundaFeira())));
            form.add(chkTerca = new CheckBox(path(proxy.isTercaFeira())));
            form.add(chkQuarta = new CheckBox(path(proxy.isQuartaFeira())));
            form.add(chkQuinta = new CheckBox(path(proxy.isQuintaFeira())));
            form.add(chkSexta = new CheckBox(path(proxy.isSextaFeira())));
            dropDownSemanasRepeticao = getDropDownSemanasRepeticao(proxy);
            form.add(dropDownSemanasRepeticao);
            form.add(dateChooserAjaxInicio = new DateChooserAjax(path(proxy.getDataInicio())));
            form.add(dateChooserAjaxTermino = new DateChooserAjax(path(proxy.getDataTermino())));

            dateChooserAjaxInicio.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (dateChooserAjaxInicio.getData().getModelObject() != null) {
                        dateChooserAjaxTermino.getData().setMinDate(new DateOption(dateChooserAjaxInicio.getData().getModelObject()));
                        target.appendJavaScript("$( '#" + dateChooserAjaxTermino.getData().getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dateChooserAjaxInicio.getData().getModelObject()) + " );");
                    } else {
                        dateChooserAjaxTermino.getData().setMinDate(null);
                    }
                    target.add(dateChooserAjaxTermino);
                    target.appendJavaScript(JScript.initMasks());
                }
            });

            dateChooserAjaxTermino.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (dateChooserAjaxTermino.getData().getModelObject() != null) {
                        dateChooserAjaxInicio.getData().setMaxDate(new DateOption(dateChooserAjaxTermino.getData().getModelObject()));
                        target.appendJavaScript("$( '#" + dateChooserAjaxInicio.getData().getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(dateChooserAjaxTermino.getData().getModelObject()) + " );");
                    } else {
                        dateChooserAjaxInicio.getData().setMaxDate(null);
                    }
                    target.add(dateChooserAjaxInicio);
                    target.appendJavaScript(JScript.initMasks());
                }
            });

            form.add(new AbstractAjaxButton("btnOk") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    if (validarCadastro(target)) {
                        onOk(target, atividadeGrupo);
                    }
                }
            });

            form.add(new AbstractAjaxButton("btnFechar") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    onFechar(target);
                }
            }.setDefaultFormProcessing(false));

            add(form);
        } catch (ValidacaoRuntimeException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if (getCopiaAtividadeGrupoDTO().getNenhumDia()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_um_dia_semana"));
            }
            if (getCopiaAtividadeGrupoDTO().getDataInicio() == null || getCopiaAtividadeGrupoDTO().getDataTermino() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_inicio_fim"));
            }
            if (Data.adjustRangeHour(getCopiaAtividadeGrupoDTO().getDataInicio()).getDataInicial().before(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_dt_inicial_apartir_dt_atual"));
            }
            if (getCopiaAtividadeGrupoDTO().getDataTermino().before(getCopiaAtividadeGrupoDTO().getDataInicio())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_dt_termino_menor_dt_inicio"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    private DropDown getDropDownSemanasRepeticao(CopiaAtividadeGrupoDTO proxy) {
        DropDown dropDown = new DropDown(path(proxy.getSemanasRepeticao()));
        dropDown.addChoice(1, "1");
        dropDown.addChoice(2, "2");
        dropDown.addChoice(3, "3");
        dropDown.addChoice(4, "4");
        return dropDown;
    }

    public abstract void onOk(AjaxRequestTarget target, AtividadeGrupo atividadeGrupo) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        form.getModel().setObject(new CopiaAtividadeGrupoDTO());

        dateChooserAjaxInicio.limpar(target);
        dateChooserAjaxInicio.getData().setMaxDate(null);
        target.add(dateChooserAjaxInicio);
        dateChooserAjaxTermino.limpar(target);
        dateChooserAjaxTermino.getData().setMinDate(null);
        target.add(dateChooserAjaxTermino);
        chkSabado.limpar(target);
        chkDomingo.limpar(target);
        chkSegunda.limpar(target);
        chkTerca.limpar(target);
        chkQuarta.limpar(target);
        chkQuinta.limpar(target);
        chkSexta.limpar(target);
        dropDownSemanasRepeticao.limpar(target);

    }

    public void setAtividadeGrupo(AtividadeGrupo atividadeGrupo) {
        this.atividadeGrupo = atividadeGrupo;
    }

    public void setDataBase(Date dataBase, Date horaInicio, Date horaFim) {
        Date data = DataUtil.mergeDataHora(dataBase, horaInicio);
        getCopiaAtividadeGrupoDTO().setDataBase(data);
        getCopiaAtividadeGrupoDTO().setDataInicio(Data.addDias(dataBase, 1));
        getCopiaAtividadeGrupoDTO().setHoraInicial(horaInicio);
        getCopiaAtividadeGrupoDTO().setHoraFinal(horaFim);
        dateChooserAjaxTermino.getData().setMinDate(new DateOption(Data.addDias(dataBase, 1)));
    }

    public CopiaAtividadeGrupoDTO getCopiaAtividadeGrupoDTO() {
        return form.getModel().getObject();
    }

}
