package br.com.celk.view.vigilancia.dengue.registrodiarioantivetorial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import org.apache.wicket.markup.html.form.Form;
import br.com.celk.vigilancia.vigilanciaambiental.dto.RegistroDiarioAntivetorialDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoInseticida;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorial;
import br.com.ksisolucoes.vo.vigilancia.dengue.RegistroDiarioAntivetorialPesquisa;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class DetalhesRegistroDiarioAntivetorialPage extends BasePage {

    private RegistroDiarioAntivetorialDTO dto;
    private boolean consulta;
    private Table<RegistroDiarioAntivetorialPesquisa> table;
    private List<RegistroDiarioAntivetorialPesquisa> lst;

    public DetalhesRegistroDiarioAntivetorialPage(RegistroDiarioAntivetorial registroDiarioAntivetorial, boolean consulta) {
        this.dto = new RegistroDiarioAntivetorialDTO(registroDiarioAntivetorial);
        this.consulta = consulta;
        init();
    }

    private void init() {
        Form<RegistroDiarioAntivetorial> form = new Form("form", new CompoundPropertyModel(dto));
        RegistroDiarioAntivetorialDTO proxy = on(RegistroDiarioAntivetorialDTO.class);

        form.add(new DisabledInputField(path(proxy.getRegistro().getDataCadastro())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getUsuario().getNome())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getMotivoCancelamento())));

        form.add(new DisabledInputField(path(proxy.getRegistro().getLocalidade().getLocalidade())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getAtividade().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getPontoEstrategico().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getDescricaoImovelInspecionado())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getProfissional().getNome())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getCiclo().getDescricaoPeriodoCiclo())));
        form.add(new DisabledInputField(path(proxy.getRegistro().getDataAtividade())));

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        loadList();
        table.populate();

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaRegistroDiarioAntivetorialPage());
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        RegistroDiarioAntivetorialPesquisa proxy = on(RegistroDiarioAntivetorialPesquisa.class);

        columns.add(getActionsColumn());
        columns.add(createColumn(bundle("logradouro"), proxy.getEndereco().getEnderecoFormatado()));
        columns.add(createColumn(bundle("numeroAbv"), proxy.getNumeroLogradouro()));
        columns.add(createColumn(bundle("sequencia"), proxy.getSequenciaLogradouro()));
        columns.add(createColumn(bundle("numeroQuarteirao"), proxy.getNumeroQuarteirao()));
        columns.add(createColumn(bundle("sequencia"), proxy.getSequenciaQuarteirao()));
        columns.add(createColumn(bundle("lado"), proxy.getLadoQuarteirao()));
        columns.add(createColumn(bundle("tipoImovel"), proxy.getTipoImovel().getDescricao()));
        columns.add(createColumn(bundle("pendencia"), proxy.getDescricaoPendencia()));

        return columns;
    }

    private IColumn getActionsColumn() {
        return new MultipleActionCustomColumn<RegistroDiarioAntivetorialPesquisa>() {
            @Override
            public void customizeColumn(RegistroDiarioAntivetorialPesquisa rowObject) {
                if (consulta) {
                    addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RegistroDiarioAntivetorialPesquisa>() {
                        @Override
                        public void action(AjaxRequestTarget target, RegistroDiarioAntivetorialPesquisa item) throws ValidacaoException, DAOException {
                            setResponsePage(new DetalhesRegistroDiarioAntivetorialPesquisaPage(item));
                        }
                    });
                } else {
                    addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<RegistroDiarioAntivetorialPesquisa>() {
                        @Override
                        public void action(AjaxRequestTarget target, RegistroDiarioAntivetorialPesquisa item) throws ValidacaoException, DAOException {
                            setResponsePage(new ResultadoPesquisaRegistroDiarioAntivetorialPage(item, false));
                        }
                    }).setIcon(Icon.LABORATORY)
                            .setTitleBundleKey("resultadoLaboratorio")
                            .setEnabled(RepositoryComponentDefault.SIM_LONG.equals(rowObject.getColetaAmostra()));
                }
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lst;
            }
        };
    }

    private void loadList() {
        RegistroDiarioAntivetorialPesquisa proxy = on(RegistroDiarioAntivetorialPesquisa.class);
        lst = LoadManager.getInstance(RegistroDiarioAntivetorialPesquisa.class)
                .addProperties(new HQLProperties(RegistroDiarioAntivetorialPesquisa.class).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, path(proxy.getEndereco())).getProperties())
                .addProperties(new HQLProperties(DengueTipoImovel.class, path(proxy.getTipoImovel())).getProperties())
                .addProperties(new HQLProperties(DengueTipoInseticida.class, path(proxy.getTipoInseticidaAmostra())).getProperties())
                .addProperties(new HQLProperties(DengueTipoInseticida.class, path(proxy.getTipoInseticidaLarvicida())).getProperties())
                .addProperties(new HQLProperties(DengueTipoInseticida.class, path(proxy.getTipoInseticidaAdulticida())).getProperties())
                .addProperties(new HQLProperties(RegistroDiarioAntivetorial.class, path(proxy.getRegistroDiarioAntivetorial())).getProperties())
                .addProperty(path(proxy.getRegistroDiarioAntivetorial().getLocalidade().getLocalidade()))
                .addProperty(path(proxy.getRegistroDiarioAntivetorial().getCiclo().getDataSemanaInicial()))
                .addProperty(path(proxy.getRegistroDiarioAntivetorial().getCiclo().getDataSemanaFinal()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroDiarioAntivetorial()), dto.getRegistro()))
                .start().getList();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesRegistroDiarioAntivetorial");
    }
}
