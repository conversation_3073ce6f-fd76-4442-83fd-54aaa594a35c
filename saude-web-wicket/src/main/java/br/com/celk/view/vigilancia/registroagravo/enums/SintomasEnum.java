package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

public enum SintomasEnum implements IEnum {
    COM_SINTOMAS(1L, Bundle.getStringApplication("com_sintomas")),
    SEM_SINTOMAS(2L, Bundle.getStringApplication("sem_sintomas"));

    private Long value;
    private String descricao;

    SintomasEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static SintomasEnum valueOf(Long value) {
        for (SintomasEnum v : SintomasEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
