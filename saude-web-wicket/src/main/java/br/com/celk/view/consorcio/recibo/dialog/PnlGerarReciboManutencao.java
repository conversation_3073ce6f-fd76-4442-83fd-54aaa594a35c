package br.com.celk.view.consorcio.recibo.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.consorcio.recibo.ConsultaReciboPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ReciboConsorcio;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Americo
 */
public abstract class PnlGerarReciboManutencao extends Panel {

    private Form form;
    private MultiSelectionTable table;
    private DateChooser dchDataRecibo;
    private InputField<String> txtDescricao;

    private String descricaoPadraoRecibo;
    private String descricao;
    private Date dataRecibo;
    private Usuario usuarioRecibo;
    private AutoCompleteConsultaUsuario autoCompleteUsuario;

    public PnlGerarReciboManutencao(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(dchDataRecibo = new DateChooser("dataRecibo"));
        dchDataRecibo.addRequiredClass();
        dchDataRecibo.setLabel(new Model<String>(WicketMethods.bundle("dataRecibo")));

        form.add(autoCompleteUsuario = new AutoCompleteConsultaUsuario("usuarioRecibo", true));
        autoCompleteUsuario.setLabel(new Model<String>(WicketMethods.bundle("usuarioRecibo")));

        form.add(txtDescricao = new InputField<String>(ReciboConsorcio.PROP_DESCRICAO));
        txtDescricao.setLabel(new Model<String>(WicketMethods.bundle("descricao")));
        txtDescricao.addRequiredClass();
        form.add(table = new MultiSelectionTable("table", getColumns(), getCollectionProvider()));
        table.setScrollXInner("100%");
        table.setScrollCollapse(true);
        table.populate();

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarCampos();
                salvar();
                onFechar(target);
                limpar(target);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);

        try {
            descricaoPadraoRecibo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("descricaoPadraoRecibo");
            if (descricaoPadraoRecibo != null) {
                String descricaoPadraoReciboFormatado = descricaoPadraoRecibo.replaceAll("\\|@MM\\|", String.valueOf(DataUtil.getDescricaoMes(DataUtil.getDataAtual())))
                        .replaceAll("\\|@AAAA\\|", String.valueOf(DataUtil.getAno(DataUtil.getDataAtual())));

                txtDescricao.setComponentValue(descricaoPadraoReciboFormatado);
            }
        } catch (DAOException e) {
            Loggable.log.trace(e);
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getEmpresaReciboList();
            }
        };
    }

    private void salvar() throws ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(table.getSelectedObjects())) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            List<Empresa> empresaList = table.getSelectedObjects();

            for (Empresa empresa : empresaList) {
                ReciboConsorcio reciboConsorcio = new ReciboConsorcio();
                reciboConsorcio.setEmpresa(empresa);
                reciboConsorcio.setValor(empresa.getValorManutencao());
                reciboConsorcio.setDataCadastro(DataUtil.getDataAtual());
                reciboConsorcio.setDataRecibo(dchDataRecibo.getData().getConvertedInput());
                reciboConsorcio.setDescricao(txtDescricao.getComponentValue());
                reciboConsorcio.setUsuario(usuarioLogado);
                reciboConsorcio.setUsuarioRecibo((Usuario) autoCompleteUsuario.getComponentValue());
                try {
                    BOFactoryWicket.save(reciboConsorcio);
                } catch (DAOException e) {
                    Loggable.log.error(e.getMessage(), e);
                } catch (ValidacaoException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        }else {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmConsorciado"));
        }

        Page page = null;
        try {
            page = ConsultaReciboPage.class.newInstance();
        } catch (InstantiationException e) {
            Loggable.log.error(e.getMessage(), e);
        } catch (IllegalAccessException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        getSession().getFeedbackMessages().info(page, BundleManager.getString("msgReciboSalvoComSucesso"));
        setResponsePage(page);
    }

    private Collection getEmpresaReciboList() {
        List<Empresa> empresaReciboList = LoadManager.getInstance(Empresa.class)
                .addProperties(VOUtils.montarPath(Empresa.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(Empresa.PROP_DESCRICAO))
                .addProperties(VOUtils.montarPath(Empresa.PROP_VALOR_MANUTENCAO))
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_VALOR_MANUTENCAO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .start().getList();
        return empresaReciboList;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        Empresa proxy = on(Empresa.class);

        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private void validarCampos() throws ValidacaoException {
        if (dchDataRecibo.getData().getConvertedInput() == null){
            throw new ValidacaoException(bundle("msgInformeData"));
        }
        if (txtDescricao.getComponentValue() == null){
            throw new ValidacaoException(bundle("msgInformeDescricao"));
        }
        if (table.getSelectedObjects() == null) {

        }
    }

    private void limpar(AjaxRequestTarget target){
        table.clearSelection(target);
        txtDescricao.limpar(target);
        dchDataRecibo.limpar(target);
    }

    protected abstract void onFechar(AjaxRequestTarget target);

}
