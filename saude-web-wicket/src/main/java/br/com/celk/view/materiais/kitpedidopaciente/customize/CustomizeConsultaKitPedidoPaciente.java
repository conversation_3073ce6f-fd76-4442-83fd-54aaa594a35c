package br.com.celk.view.materiais.kitpedidopaciente.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaKitPedidoPaciente extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(KitPedidoPaciente.PROP_DESCRICAO), QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(KitPedidoPaciente.PROP_CODIGO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(KitPedidoPaciente.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return KitPedidoPaciente.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(KitPedidoPaciente.class).getProperties());
    }

}
