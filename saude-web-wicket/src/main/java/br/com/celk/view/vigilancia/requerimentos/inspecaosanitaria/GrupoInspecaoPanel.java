package br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.RequerimentoNovaInspecaoSanitariaDTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;

import static ch.lambdaj.Lambda.on;

public class GrupoInspecaoPanel extends Panel {

    private RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria;
    private boolean camposObrigatorios;
    private RequerimentoNovaInspecaoSanitariaDTO dto;
    private DateChooser dataUltimaInspecao;
    private DateChooser dataInspecaoDC;
    private CheckBoxLongValue cbPrimeiraInspecao;

    public GrupoInspecaoPanel(String id, RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria, boolean enable) {
        super(id);
        this.requerimentoInspecaoSanitaria = requerimentoInspecaoSanitaria;
        this.camposObrigatorios = true;
        this.dto = new RequerimentoNovaInspecaoSanitariaDTO();

        init(enable);
    }

    private void init(boolean enable) {
        RequerimentoNovaInspecaoSanitariaDTO proxy = on(RequerimentoNovaInspecaoSanitariaDTO.class);

        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(dto));

        carregarUltimaInspecao(requerimentoInspecaoSanitaria.getEstabelecimento());
        carregarDataInspecao();

        root.add(dataUltimaInspecao);
        root.add(dataInspecaoDC);
        root.add(cbPrimeiraInspecao);

        root.setEnabled(enable);
        add(root);
    }

    public DateChooser getDataUltimaInspecao() {
        return dataUltimaInspecao;
    }

    public CheckBoxLongValue getCbPrimeiraInspecao() {
        return cbPrimeiraInspecao;
    }

    private void carregarUltimaInspecao(Estabelecimento estabelecimento){
        dataUltimaInspecao = new DateChooser("dataUltimaInspecao", new PropertyModel<Date>(dto, "dataUltimaInspecao"));
        dataUltimaInspecao.setEnabled(false);
        cbPrimeiraInspecao = new CheckBoxLongValue("isPrimeiraInspecao", RepositoryComponentDefault.SIM_LONG, new PropertyModel<Long>(dto, "isPrimeiraInspecao"));
        cbPrimeiraInspecao.setEnabled(false);

        if (estabelecimento != null){
            RegistroInspecao registroInspecao = VigilanciaHelper.carregarUltimaInspecao(estabelecimento);
            if (registroInspecao != null){
                dataUltimaInspecao.setComponentValue(registroInspecao.getDataInspecao());
                cbPrimeiraInspecao.setComponentValue(RepositoryComponentDefault.NAO_LONG);
            }else{
                cbPrimeiraInspecao.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                dataUltimaInspecao.setComponentValue(null);
            }
        }
    }

    private void carregarDataInspecao(){
        dataInspecaoDC = new DateChooser("dataInspecao", new PropertyModel<Date>(dto, "dataInspecao"));
        dataInspecaoDC.setRequiredField(camposObrigatorios);
        dataInspecaoDC.setComponentValue(requerimentoInspecaoSanitaria.getDataInspecao());
    }

    public Date getDataInspecao(){
        return dataInspecaoDC.getComponentValue();
    }
}
