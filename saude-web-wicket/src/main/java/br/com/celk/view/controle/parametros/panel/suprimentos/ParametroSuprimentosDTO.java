package br.com.celk.view.controle.parametros.panel.suprimentos;

import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ParametroSuprimentosDTO implements Serializable {

    private Double intervaloQtdePadrao;
    private TipoDocumento tipoDocumento;
    private TipoDocumento InventarioEntrada;
    private TipoDocumento inventarioSaida;
    private TipoDocumento docDispensacao;
    private TipoDocumento docPedidoTransfEntrada;
    private TipoDocumento docPedidoTransfSaida;
    private TipoDocumento tipoDocOPEntrada;
    private TipoDocumento tipoDocOPSaida;
    private TipoDocumento tipoDocNFEntrada;
    private TipoDocumento tipoDocNFSaida;
    private TipoDocumento tipoDocExtPedido;
    private TipoDocumento tipoDocEntradaJudicial;
    private TipoDocumento tipoDocSaidaJudicial;
    private TipoDocumento tipoDocEntradaEstadual;
    private TipoDocumento tipoDocSaidaEstadual;
    private CentroCusto centroCustoSaidaSolicitada;

    public Double getIntervaloQtdePadrao() {
        return intervaloQtdePadrao;
    }

    public void setIntervaloQtdePadrao(Double intervaloQtdePadrao) {
        this.intervaloQtdePadrao = intervaloQtdePadrao;
    }

    public TipoDocumento getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(TipoDocumento tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public TipoDocumento getInventarioEntrada() {
        return InventarioEntrada;
    }

    public void setInventarioEntrada(TipoDocumento InventarioEntrada) {
        this.InventarioEntrada = InventarioEntrada;
    }

    public TipoDocumento getInventarioSaida() {
        return inventarioSaida;
    }

    public void setInventarioSaida(TipoDocumento inventarioSaida) {
        this.inventarioSaida = inventarioSaida;
    }

    public TipoDocumento getDocDispensacao() {
        return docDispensacao;
    }

    public void setDocDispensacao(TipoDocumento docDispensacao) {
        this.docDispensacao = docDispensacao;
    }

    public TipoDocumento getDocPedidoTransfEntrada() {
        return docPedidoTransfEntrada;
    }

    public void setDocPedidoTransfEntrada(TipoDocumento docPedidoTransfEntrada) {
        this.docPedidoTransfEntrada = docPedidoTransfEntrada;
    }

    public TipoDocumento getDocPedidoTransfSaida() {
        return docPedidoTransfSaida;
    }

    public void setDocPedidoTransfSaida(TipoDocumento docPedidoTransfSaida) {
        this.docPedidoTransfSaida = docPedidoTransfSaida;
    }

    public TipoDocumento getTipoDocOPEntrada() {
        return tipoDocOPEntrada;
    }

    public void setTipoDocOPEntrada(TipoDocumento tipoDocOPEntrada) {
        this.tipoDocOPEntrada = tipoDocOPEntrada;
    }

    public TipoDocumento getTipoDocOPSaida() {
        return tipoDocOPSaida;
    }

    public void setTipoDocOPSaida(TipoDocumento tipoDocOPSaida) {
        this.tipoDocOPSaida = tipoDocOPSaida;
    }

    public TipoDocumento getTipoDocNFEntrada() {
        return tipoDocNFEntrada;
    }

    public void setTipoDocNFEntrada(TipoDocumento tipoDocNFEntrada) {
        this.tipoDocNFEntrada = tipoDocNFEntrada;
    }

    public TipoDocumento getTipoDocNFSaida() {
        return tipoDocNFSaida;
    }

    public void setTipoDocNFSaida(TipoDocumento tipoDocNFSaida) {
        this.tipoDocNFSaida = tipoDocNFSaida;
    }

    public TipoDocumento getTipoDocExtPedido() {
        return tipoDocExtPedido;
    }

    public void setTipoDocExtPedido(TipoDocumento tipoDocExtPedido) {
        this.tipoDocExtPedido = tipoDocExtPedido;
    }

    public TipoDocumento getTipoDocEntradaJudicial() {
        return tipoDocEntradaJudicial;
    }

    public void setTipoDocEntradaJudicial(TipoDocumento tipoDocEntradaJudicial) {
        this.tipoDocEntradaJudicial = tipoDocEntradaJudicial;
    }

    public TipoDocumento getTipoDocSaidaJudicial() {
        return tipoDocSaidaJudicial;
    }

    public void setTipoDocSaidaJudicial(TipoDocumento tipoDocSaidaJudicial) {
        this.tipoDocSaidaJudicial = tipoDocSaidaJudicial;
    }

    public TipoDocumento getTipoDocEntradaEstadual() {
        return tipoDocEntradaEstadual;
    }

    public void setTipoDocEntradaEstadual(TipoDocumento tipoDocEntradaEstadual) {
        this.tipoDocEntradaEstadual = tipoDocEntradaEstadual;
    }

    public TipoDocumento getTipoDocSaidaEstadual() {
        return tipoDocSaidaEstadual;
    }

    public void setTipoDocSaidaEstadual(TipoDocumento tipoDocSaidaEstadual) {
        this.tipoDocSaidaEstadual = tipoDocSaidaEstadual;
    }

    public CentroCusto getCentroCustoSaidaSolicitada() {
        return centroCustoSaidaSolicitada;
    }

    public void setCentroCustoSaidaSolicitada(CentroCusto centroCustoSaidaSolicitada) {
        this.centroCustoSaidaSolicitada = centroCustoSaidaSolicitada;
    }

}
