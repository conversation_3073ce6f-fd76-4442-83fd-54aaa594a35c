package br.com.celk.view.basico.tipoexame.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.tipoexame.autocomplete.restricaocontainer.RestricaoContainerTipoExame;
import br.com.ksisolucoes.agendamento.dto.QueryConsultaTipoExameDTOParam;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

public class AutoCompleteConsultaTipoExamePpi extends AutoCompleteConsulta<TipoExame> {

    private Long cdClassificacao;

    public AutoCompleteConsultaTipoExamePpi(String id, IModel<TipoExame> model, Long cdClassificacao) {
        super(id, model);
        this.cdClassificacao = cdClassificacao;
    }

    public AutoCompleteConsultaTipoExamePpi(String id, IModel<TipoExame> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(TipoExame.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(TipoExame.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoExame.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerTipoExame(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<TipoExame, QueryConsultaTipoExameDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaTipoExameDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarTipoExame(dataPaging);
                    }

                    @Override
                    public QueryConsultaTipoExameDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaTipoExameDTOParam param = new QueryConsultaTipoExameDTOParam();
                        param.setKeyword(searchCriteria);
                        param.setCdClassificacao(cdClassificacao);
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaTipoExameDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(TipoExame.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return TipoExame.class;
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tiposExame");
    }

    public Long getCdClassificacao() {
        return cdClassificacao;
    }

    public void setCdClassificacao(Long cdClassificacao) {
        this.cdClassificacao = cdClassificacao;
    }
}
