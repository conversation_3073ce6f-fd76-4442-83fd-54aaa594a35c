package br.com.celk.view.hospital.procedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.consorcio.procedimento.CustomizeConsultaProcedimento;
import br.com.celk.view.prontuario.tipotabelaprocedimento.autocomplete.AutoCompleteConsultaTipoTabelaProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaProcedimentoPage extends ConsultaPage<Procedimento, List<BuilderQueryCustom.QueryParameter>> {

    private String referencia;
    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("referencia", new PropertyModel(this, "referencia")));
        form.add(new UpperField("descricao"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Procedimento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Procedimento.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Procedimento.PROP_DESCRICAO)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<Procedimento>() {

            @Override
            public Component getComponent(String componentId, final Procedimento rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new br.com.celk.view.hospital.procedimento.CadastroProcedimentoPage(rowObject, false));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ConsorcioFacade.class).removerProcedimentoAdicional(rowObject);
                        getPageableTable().update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new br.com.celk.view.hospital.procedimento.CadastroProcedimentoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaProcedimento()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Procedimento.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Procedimento.PROP_REFERENCIA), referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Procedimento.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Procedimento.PROP_FLAG_FATURAVEL), RepositoryComponentDefault.NAO));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return br.com.celk.view.hospital.procedimento.CadastroProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaProcedimentos");
    }
}
