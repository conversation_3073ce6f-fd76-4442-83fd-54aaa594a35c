package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.EditarActionColumnPanel;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaReceitaContinuaPacienteDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaReceitaContinuaPacientePage extends BasePage {

    private PageableTable<ReceituarioItem> pageableTable;
    private QueryConsultaReceitaContinuaPacienteDTOParam param = new QueryConsultaReceitaContinuaPacienteDTOParam();

    public ConsultaReceitaContinuaPacientePage() {
        init();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaReceitaContinuaPaciente");
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(param));
        this.add(form);

        form.add(new InputField("nomePaciente"));
        form.add(new InputField("nomeProfissional"));
        form.add(new AutoCompleteConsultaEmpresa("unidade"));
        form.add(new InputField("nomeProduto"));

        pageableTable = new PageableTable<ReceituarioItem>("table", getColumns(), getDataProvider());

        form.add(new ProcurarButton("btnProcurar", pageableTable) {
            @Override
            public Object getParam() {
                return param;
            }
        });

        form.add(pageableTable);
        form.add(new AbstractAjaxButton("btnNovaReceita") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroReceitaContinuaPacientePage());
            }
        });

    }

    public List<ISortableColumn<ReceituarioItem>> getColumns() {
        List<ISortableColumn<ReceituarioItem>> columns = new ArrayList<ISortableColumn<ReceituarioItem>>();
        ColumnFactory columnFactory = new ColumnFactory(ReceituarioItem.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("profissional"), VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("medicamento"), VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidadeAbv"), VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("quantidadePrescritaAbv"), VOUtils.montarPath(ReceituarioItem.PROP_QUANTIDADE_PRESCRITA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataPrescricao"), VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO)));

        return columns;
    }

    private CustomColumn<ReceituarioItem> getCustomColumn() {
        return new CustomColumn<ReceituarioItem>() {
            @Override
            public Component getComponent(String componentId, final ReceituarioItem rowObject) {
                return new EditarActionColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        editar(rowObject, target);
                    }

                };
            }
        };
    }

    private QueryPagerProvider<ReceituarioItem, QueryConsultaReceitaContinuaPacienteDTOParam> getDataProvider() {
        return new QueryPagerProvider<ReceituarioItem, QueryConsultaReceitaContinuaPacienteDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryConsultaReceitaContinuaPacienteDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(ReceituarioFacade.class).queryConsultaReceitaContinuaPaciente(dataPaging);
            }
        };
    }
    
    private void editar(ReceituarioItem rowObject, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (rowObject.getReceituario().getNumeroReceita() != null) {
            List<DispensacaoMedicamento> dispensacaoMedicamentos = LoadManager.getInstance(DispensacaoMedicamento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(DispensacaoMedicamento.PROP_RECEITA, rowObject.getReceituario().getNumeroReceita().toString()))
                    .addProperty(DispensacaoMedicamento.PROP_CODIGO)
                    .start().getList();
            if (!dispensacaoMedicamentos.isEmpty()) {
                throw new ValidacaoException(BundleManager.getString("msgAlteracaoNaoPermitidaJaExisteDispensacaoParaReceita"));
            }
        }
        
        List<ReceituarioItem> receituarioItems = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Receituario.class, ReceituarioItem.PROP_RECEITUARIO).getProperties())
                .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperties(new HQLProperties(Produto.class, ReceituarioItem.PROP_PRODUTO).getProperties())
                .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), rowObject.getReceituario()))
                .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                .start().getList();
        Receituario receituario = receituarioItems.get(0).getReceituario();
        receituario.setReceituarioItemList(receituarioItems);
        
        setResponsePage(new CadastroReceitaContinuaPacientePage(receituario));
    }
    
}
