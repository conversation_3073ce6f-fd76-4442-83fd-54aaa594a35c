package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 * Created by Leonardo
 */
public abstract class DlgAdicionarFiscaisRequerimento extends Window {

    private PnlAdicionarFiscaisRequerimento pnlAdicionarFiscaisRequerimento;
    private boolean telaFiscais;

    public DlgAdicionarFiscaisRequerimento(String id, boolean telaFiscais){
        super(id);
        this.telaFiscais = telaFiscais;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){

            @Override
            protected String load(){
                return BundleManager.getString("informarFiscais");
            }
        });

        setInitialWidth(630);
        setInitialHeight(450);
        setResizable(true);

        setContent(pnlAdicionarFiscaisRequerimento = new PnlAdicionarFiscaisRequerimento(getContentId(), telaFiscais) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilancia rv, List<RequerimentoVigilanciaFiscal> fiscalList, List<RequerimentoVigilanciaFiscal> fiscalExcluirList) throws ValidacaoException, DAOException {
                close(target);
                DlgAdicionarFiscaisRequerimento.this.onConfirmar(target,  rv, fiscalList, fiscalExcluirList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, RequerimentoVigilancia rv, List<RequerimentoVigilanciaFiscal> fiscalList, List<RequerimentoVigilanciaFiscal> fiscalExcluirList) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia){
        show(target);
        pnlAdicionarFiscaisRequerimento.setObject(target, requerimentoVigilancia);
    }
}