package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoBotulismoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnorado;
import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.isLongSim;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoBotulismoPage extends FichaInvestigacaoAgravoBasePage {

    private final String CSSFILE = "FichaInvestigacaoAgravoBotulismoPage.css";

    private InvestigacaoAgravoBotulismo investigacaoAgravoBotulismo;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DateChooser dataPrimeiroAtendimento;
    private InputField txtNumAtendimentoSuspeicaoClinica;
    private DateChooser dataSuspeicaoClinica;

    private RadioButtonGroup radioGroupHospitalizacao;
    private WebMarkupContainer containerHospitalizacao;
    private DateChooser dataInternacao;
    private DateChooser dataAlta;
    private DisabledInputField estadoHospitalizacao;
    private DisabledInputField municipioHospitalizacao;
    private DisabledInputField codMunicipioHospital;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeHospitalizacao;
    private DisabledInputField unidadeHospitalizacaoCnes;

    private WebMarkupContainer containerDadosClinicos;
    private DropDown ddSinaisSintomasFebre;
    private DropDown ddSinaisSintomasNausea;
    private DropDown ddSinaisSintomasVomito;
    private DropDown ddSinaisSintomasDiarreia;
    private DropDown ddSinaisSintomasConstipacao;
    private DropDown ddSinaisSintomasCefaleia;
    private DropDown ddSinaisSintomasTontura;
    private DropDown ddSinaisSintomasVisaoTurva;
    private DropDown ddSinaisSintomasDiplopia;
    private DropDown ddSinaisSintomasDisartria;
    private DropDown ddSinaisSintomasDisfonia;
    private DropDown ddSinaisSintomasDisfagia;
    private DropDown ddSinaisSintomasBocaSeca;
    private DropDown ddSinaisSintomasFerimento;
    private DropDown ddSinaisSintomasFacidezPescoco;
    private DropDown ddSinaisSintomasDispneia;
    private DropDown ddSinaisSintomasInsuficienciaRespiratoria;
    private DropDown ddSinaisSintomasInsuficienciaCardiaca;
    private DropDown ddSinaisSintomasComa;
    private DropDown ddSinaisSintomasPasrestesia;
    private InputField txtSinaisSintomasPasrestesiaDescricao;
    private InputField txtSinaisSintomasOutros;

    private DropDown ddExameNeurologicoPtosePalpebral;
    private DropDown ddExameNeurologicoOftalmoparesia;
    private DropDown ddExameNeurologicoMidriase;
    private DropDown ddExameNeurologicoParalisiaFacial;
    private DropDown ddExameNeurologicoComprometimentoMusculaturaBulbar;
    private DropDown ddExameNeurologicoFraquezaMembrosSuperiores;
    private DropDown ddExameNeurologicoFraquezaMembrosInferiores;
    private DropDown ddExameNeurologicoFraquezaDescendente;
    private DropDown ddExameNeurologicoFraquezaSimetrica;
    private DropDown ddExameNeurologicoFraquezaAlteracoesSensibilidade;

    private DropDown ddReflexosNeurologicos;

    private WebMarkupContainer containerFonteTransmissao;
    private DropDown ddSuspeitaTransmissaoAlimentar;
    private InputField txtAlimentoSuspeito;

    private DropDown ddAlimentoSuspeitoIndustrial;
    private DropDown ddAlimentoSuspeitoCaseira;
    private InputField txtAlimentoSuspeitoIndustrialMarca;
    private DateChooser dataAlimentoSuspeitoIndustrialDataValidade;
    private InputField txtAlimentoSuspeitoIndustrialLote;

    private DropDown ddExposicaoAlimento;
    private InputField txtTempoIngestaoSintomasUnica;
    private InputField txtTempoPrimeiraIngestaoSintomasMultipla;
    private InputField txtTempoUltimaIngestaoSintomasMultipla;

    private DropDown ddLocalIngestaoDomicilio;
    private DropDown ddLocalIngestaoCrecheEscola;
    private DropDown ddLocalIngestaoTrabalho;
    private DropDown ddLocalIngestaoRestaurante;
    private DropDown ddLocalIngestaoFesta;
    private InputField txtLocalIngestaoOutro;

    private DisabledInputField cidadeIngestaoAlimentoUF;
    private AutoCompleteConsultaCidade autoCompleteCidadeIngestaoAlimento;
    private DisabledInputField cidadeIngestaoAlimentoIbge;
    private InputField txtNumPessoasConsumiramAlimento;

    private WebMarkupContainer containerTratamento;
    private DropDown ddTratamentoAssistenciaVentilatoria;
    private DropDown ddTratamentoAssistenciaSoroAntibotulinico;
    private DropDown ddTratamentoAssistenciaAntibioticoterapia;
    private InputField txtTratamentoAssistenciaOutro;

    private DateChooser dataAdministracao;
    private DropDown ddSoroAntibotulinico;

    private WebMarkupContainer containerDadosLaboratorio;
    private DropDown ddToxinaBotulinicaSoro;
    private DateChooser dataColetaToxinaBotulinicaSoro;
    private DropDown ddToxinaBotulinicaSoroResultado;
    private DropDown ddToxinaBotulinicaSoroTipo;

    private DropDown ddToxinaBotulinicaFezes;
    private DateChooser dataColetaToxinaBotulinicaFezes;
    private DropDown ddToxinaBotulinicaFezesResultado;
    private DropDown ddToxinaBotulinicaFezesTipo;

    private InputField txtToxinaBotulinicaAlimento1;
    private DropDown ddToxinaBotulinicaAlimento1;
    private DateChooser dataColetaToxinaBotulinicaAlimento1;
    private DropDown ddToxinaBotulinicaAlimento1Resultado;
    private DropDown ddToxinaBotulinicaAlimento1Tipo;

    private InputField txtToxinaBotulinicaAlimento2;
    private DropDown ddToxinaBotulinicaAlimento2;
    private DateChooser dataColetaToxinaBotulinicaAlimento2;
    private DropDown ddToxinaBotulinicaAlimento2Resultado;
    private DropDown ddToxinaBotulinicaAlimento2Tipo;

    private InputField txtToxinaBotulinicaOutro;
    private DropDown ddToxinaBotulinicaOutros;
    private DateChooser dataColetaToxinaBotulinicaOutros;
    private DropDown ddToxinaBotulinicaOutrosResultado;
    private DropDown ddToxinaBotulinicaOutrosTipo;

    private DropDown ddExamesComplementaresLiquor;
    private DateChooser dataColetaExamesComplementaresLiquor;
    private InputField txtExamesComplementaresLiquorNumCelular;
    private InputField txtExamesComplementaresLiquorProteinas;

    private DropDown ddEletroneuromiografia;
    private DateChooser dataRealizacaoEletroneuromiografia;
    private DropDown ddNeuroconducaoSensitiva;
    private DropDown ddNeuroconducaoMotora;
    private DropDown ddEstimulacaoRepetitiva;

    private WebMarkupContainer containerConclusao;
    private DropDown ddClassificacaoFinal;
    private InputField txtClassificacaoFinalEspecificar;
    private DropDown ddCriterioConfirmacaoDescarte;
    private DropDown ddFormaBotulismo;

    private DropDown ddToxinaBotuliticaClinica;
    private DropDown ddToxinaBotuliticaBromatologica;

    private DropDown ddTipoToxinaIsoladaClinica;
    private DropDown ddTipoToxinaBotuliticaBromatologica;

    private InputField txtCausaAlimento;
    private DropDown ddDoencaRelacionadaTrabalho;
    private DropDown ddEvolucaoCaso;
    private DateChooser dataObito;

    private WebMarkupContainer containerObservacoes;
    private InputArea observacao;

    private WebMarkupContainer containerAlimentos;
    private CompoundPropertyModel<InvestigacaoAgravoBotulismoAlimentos> modelAlimentos;
    private Table tblAlimentos;
    private List<InvestigacaoAgravoBotulismoAlimentos> alimentosList;
    private InputField txtTipoAlimento;
    private InputField txtLocalConsumo;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;

    public FichaInvestigacaoAgravoBotulismoPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoBotulismo = InvestigacaoAgravoBotulismo.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoBotulismo == null) {
            investigacaoAgravoBotulismo = new InvestigacaoAgravoBotulismo();
            investigacaoAgravoBotulismo.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoBotulismo.setRegistroAgravo(getAgravo());
        }

        alimentosList = investigacaoAgravoBotulismo.getBotulismoAlimentosByIdInvestigacaoBotulismo();

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravoBotulismo.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            investigacaoAgravoBotulismo.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravoBotulismo.setOcupacaoCbo(tabelaCbo);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoBotulismo)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoBotulismoDTO fichaDTO = new FichaInvestigacaoAgravoBotulismoDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoBotulismo(investigacaoAgravoBotulismo);
        fichaDTO.setInvestigacaoAgravoBotulismoAlimentosList(alimentosList);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoBotulismo.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoBotulismo.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoBotulismo proxy = on(InvestigacaoAgravoBotulismo.class);

        criarInvestigacao(proxy);
        criarAntecedentesEpidemiologicos(proxy);
        criarHospitalizacao(proxy);
        criarDadosClinicos(proxy);
        criarFonteTransmissao(proxy);
        criarTratamento(proxy);
        criarDadosLaboratorio(proxy);
        criarConclusao(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarHospitalizacao();
        carregarDadosClinicos();
        carregarTransmissao();
        carregarTratamento();
        carregarDadosLaboratorio();
        carregarConclusao();
    }

    @Override
    public void limparCamposInformacoesComplementares(AjaxRequestTarget target) {
        limparInvestigacao(target);
        limparAntecedentesEpidemiologicos(target);
        limparHospitalizacao(target);
        limparDadosClinicos(target);
        limparFonteTransmissao(target);
        limparTratamento(target);
        limparDadosLaboratorio(target);
        limparConclusao(target);
        limparObservacoes(target);
    }
    private void limparInvestigacao(AjaxRequestTarget target) {
        dataInvestigacao.limpar(target);
        ocupacaoCbo.limpar(target);
    }
    private void limparAntecedentesEpidemiologicos(AjaxRequestTarget target) {
        dataPrimeiroAtendimento.limpar(target);
        txtNumAtendimentoSuspeicaoClinica.limpar(target);
        dataSuspeicaoClinica.limpar(target);
    }
    private void limparHospitalizacao(AjaxRequestTarget target) {
        dataInternacao.limpar(target);
        dataAlta.limpar(target);
        autoCompleteUnidadeHospitalizacao.limpar(target);
        unidadeHospitalizacaoCnes.limpar(target);
        municipioHospitalizacao.limpar(target);
        codMunicipioHospital.limpar(target);
        estadoHospitalizacao.limpar(target);
    }
    private void limparDadosClinicos(AjaxRequestTarget target) {
        ddSinaisSintomasFebre.limpar(target);
        ddSinaisSintomasNausea.limpar(target);
        ddSinaisSintomasVomito.limpar(target);
        ddSinaisSintomasDiarreia.limpar(target);
        ddSinaisSintomasConstipacao.limpar(target);
        ddSinaisSintomasCefaleia.limpar(target);
        ddSinaisSintomasTontura.limpar(target);
        ddSinaisSintomasVisaoTurva.limpar(target);
        ddSinaisSintomasDiplopia.limpar(target);
        ddSinaisSintomasDisartria.limpar(target);
        ddSinaisSintomasDisfonia.limpar(target);
        ddSinaisSintomasDisfagia.limpar(target);
        ddSinaisSintomasBocaSeca.limpar(target);
        ddSinaisSintomasFerimento.limpar(target);
        ddSinaisSintomasFacidezPescoco.limpar(target);
        ddSinaisSintomasInsuficienciaRespiratoria.limpar(target);
        ddSinaisSintomasDispneia.limpar(target);
        ddSinaisSintomasInsuficienciaCardiaca.limpar(target);
        ddSinaisSintomasComa.limpar(target);
        ddSinaisSintomasPasrestesia.limpar(target);
        txtSinaisSintomasPasrestesiaDescricao.limpar(target);
        txtSinaisSintomasOutros.limpar(target);

        ddExameNeurologicoPtosePalpebral.limpar(target);
        ddExameNeurologicoOftalmoparesia.limpar(target);
        ddExameNeurologicoMidriase.limpar(target);
        ddExameNeurologicoParalisiaFacial.limpar(target);
        ddExameNeurologicoComprometimentoMusculaturaBulbar.limpar(target);
        ddExameNeurologicoFraquezaMembrosSuperiores.limpar(target);
        ddExameNeurologicoFraquezaMembrosInferiores.limpar(target);
        ddExameNeurologicoFraquezaDescendente.limpar(target);
        ddExameNeurologicoFraquezaSimetrica.limpar(target);
        ddExameNeurologicoFraquezaAlteracoesSensibilidade.limpar(target);

        ddReflexosNeurologicos.limpar(target);
    }
    private void limparFonteTransmissao(AjaxRequestTarget target) {
        ddSuspeitaTransmissaoAlimentar.limpar(target);
        txtAlimentoSuspeito.limpar(target);
        ddAlimentoSuspeitoIndustrial.limpar(target);
        ddAlimentoSuspeitoCaseira.limpar(target);
        txtAlimentoSuspeitoIndustrialMarca.limpar(target);
        dataAlimentoSuspeitoIndustrialDataValidade.limpar(target);
        txtAlimentoSuspeitoIndustrialLote.limpar(target);
        ddExposicaoAlimento.limpar(target);
        txtTempoIngestaoSintomasUnica.limpar(target);
        txtTempoPrimeiraIngestaoSintomasMultipla.limpar(target);
        txtTempoUltimaIngestaoSintomasMultipla.limpar(target);
        ddLocalIngestaoDomicilio.limpar(target);
        ddLocalIngestaoCrecheEscola.limpar(target);
        ddLocalIngestaoTrabalho.limpar(target);
        ddLocalIngestaoRestaurante .limpar(target);
        ddLocalIngestaoFesta.limpar(target);
        txtLocalIngestaoOutro.limpar(target);
        cidadeIngestaoAlimentoUF.limpar(target);
        autoCompleteCidadeIngestaoAlimento.limpar(target);
        cidadeIngestaoAlimentoIbge.limpar(target);
        txtNumPessoasConsumiramAlimento.limpar(target);
    }
    private void limparTratamento(AjaxRequestTarget target) {
        ddTratamentoAssistenciaVentilatoria.limpar(target);
        ddTratamentoAssistenciaSoroAntibotulinico.limpar(target);
        ddTratamentoAssistenciaAntibioticoterapia.limpar(target);
        txtTratamentoAssistenciaOutro.limpar(target);
        dataAdministracao.limpar(target);
        dataAdministracao.limpar(target);
        ddSoroAntibotulinico.limpar(target);
    }
    private void limparDadosLaboratorio(AjaxRequestTarget target) {
        ddToxinaBotulinicaSoro.limpar(target);
        dataColetaToxinaBotulinicaSoro.limpar(target);
        dataColetaToxinaBotulinicaSoro.limpar(target);
        ddToxinaBotulinicaSoroResultado.limpar(target);
        ddToxinaBotulinicaSoroTipo.limpar(target);
        ddToxinaBotulinicaFezes.limpar(target);
        dataColetaToxinaBotulinicaFezes.limpar(target);
        ddToxinaBotulinicaFezesResultado.limpar(target);
        ddToxinaBotulinicaFezesTipo.limpar(target);
        txtToxinaBotulinicaAlimento1.limpar(target);
        ddToxinaBotulinicaAlimento1.limpar(target);
        dataColetaToxinaBotulinicaAlimento1.limpar(target);
        ddToxinaBotulinicaAlimento1Resultado.limpar(target);
        ddToxinaBotulinicaAlimento1Tipo.limpar(target);
        txtToxinaBotulinicaAlimento2.limpar(target);
        ddToxinaBotulinicaAlimento2.limpar(target);
        dataColetaToxinaBotulinicaAlimento2 .limpar(target);
        ddToxinaBotulinicaAlimento2Resultado.limpar(target);
        ddToxinaBotulinicaAlimento2Tipo.limpar(target);
        txtToxinaBotulinicaOutro.limpar(target);
        ddToxinaBotulinicaOutros.limpar(target);
        dataColetaToxinaBotulinicaOutros .limpar(target);
        ddToxinaBotulinicaOutrosResultado.limpar(target);
        ddToxinaBotulinicaOutrosTipo.limpar(target);
        ddExamesComplementaresLiquor.limpar(target);
        dataColetaExamesComplementaresLiquor.limpar(target);
        txtExamesComplementaresLiquorNumCelular.limpar(target);
        txtExamesComplementaresLiquorProteinas.limpar(target);
        ddEletroneuromiografia.limpar(target);
        dataRealizacaoEletroneuromiografia.limpar(target);
        ddNeuroconducaoSensitiva .limpar(target);
        ddNeuroconducaoMotora.limpar(target);
        ddEstimulacaoRepetitiva.limpar(target);
    }
    private void limparConclusao(AjaxRequestTarget target) {
        ddClassificacaoFinal.limpar(target);
        txtClassificacaoFinalEspecificar.limpar(target);
        ddCriterioConfirmacaoDescarte.limpar(target);
        ddFormaBotulismo.limpar(target);
        ddToxinaBotuliticaClinica.limpar(target);
        ddToxinaBotuliticaBromatologica.limpar(target);
        ddTipoToxinaIsoladaClinica.limpar(target);
        ddTipoToxinaBotuliticaBromatologica.limpar(target);
        txtCausaAlimento.limpar(target);
        ddDoencaRelacionadaTrabalho.limpar(target);
        ddEvolucaoCaso.limpar(target);
        dataObito.limpar(target);
    }
    private void limparObservacoes(AjaxRequestTarget target) {
        observacao.limpar(target);
        txtTipoAlimento.limpar(target);
        txtLocalConsumo.limpar(target);
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoBotulismoDTO dto = (FichaInvestigacaoAgravoBotulismoDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoBotulismo(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        InvestigacaoAgravoBotulismo proxy = on(InvestigacaoAgravoBotulismo.class);

        validarDadosHospitalizacao(proxy);
        validarTransmissao(proxy);
        validarDadosLaboratorio(proxy);
    }

    public void validarDadosHospitalizacao(InvestigacaoAgravoBotulismo proxy) throws ValidacaoException {
        if (containerHospitalizacao.isEnabled()) {
            validarDatas(
                    "Verifique o campo Data de Internação!",
                    (DateChooser) containerHospitalizacao.get(path(proxy.getDataInternacao()))
            );

            if (investigacaoAgravoBotulismo.getHospital() == null) {
                setFocusComponentAfterException(containerHospitalizacao.get(path(proxy.getHospital())));
                throw new ValidacaoException("Verifique o campo Hospital da Internação");
            }
            validarRegra(
                    isBefore(dataInternacao.getData().getConvertedInput(), investigacaoAgravoBotulismo.getRegistroAgravo().getDataPrimeirosSintomas()),
                    dataInternacao,
                    "msgDataInternacaoMaiorIgualDataPrimeirosSintomas");

            validarRegra(
                    isBefore(dataAlta.getData().getConvertedInput(), investigacaoAgravoBotulismo.getDataInternacao()),
                    dataAlta,
                    "msgDataAtendimentoMaiorIgualDataInternacao");
        }
    }

    public void validarTransmissao(InvestigacaoAgravoBotulismo proxy) throws ValidacaoException {
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrial() != null && investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrial().equals(InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value()) &&
             (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialMarca() == null &&
             investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialDataValidade() == null &&
             investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialLote() == null)) {
            setFocusComponentAfterException(containerFonteTransmissao.get(path(proxy.getAlimentoSuspeitoIndustrialMarca())));
            throw new ValidacaoException("Adicione pelo menos um dos campos de Alimento de produção Industrial/Comercial");
        }

        List<DropDown> dds = Arrays.asList(ddLocalIngestaoDomicilio, ddLocalIngestaoCrecheEscola, ddLocalIngestaoTrabalho, ddLocalIngestaoRestaurante, ddLocalIngestaoFesta);

        if (allInfoDropsContainValues(dds) && txtLocalIngestaoOutro.isEnabled() &&
                txtLocalIngestaoOutro.getComponentValue() == null) {
            setFocusComponentAfterException(containerFonteTransmissao.get(path(proxy.getLocalIngestaoOutro())));
            throw new ValidacaoException("Selecione pelo menos um local de ingestão ou especifique em outros");
        }

    }

    public void validarDadosLaboratorio(InvestigacaoAgravoBotulismo proxy) throws ValidacaoException {
        if (dataPrimeiroAtendimento.getComponentValue() != null && dataRealizacaoEletroneuromiografia.getComponentValue() != null) {
            validarRegra(
                    isBefore(dataRealizacaoEletroneuromiografia.getData().getConvertedInput(), dataPrimeiroAtendimento.getData().getConvertedInput()),
                    dataRealizacaoEletroneuromiografia,
                    "msgDataRealizacaoMaiorIgualDataAtendimento");
            setFocusComponentAfterException(containerDadosLaboratorio.get(path(proxy.getDataRealizacaoEletroneuromiografia())));
        }
    }

    public static boolean allInfoDropsContainValues(List<DropDown> dds) {
        for (DropDown dd : dds) {
            if (isSim(dd)) {
                return false;
            }
        }
        return true;
    }

    public static boolean isSim(DropDown dd) {
        return (dd != null && dd.getComponentValue() == InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.SimNaoEnum.SIM.value());
    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public boolean isBefore(Date data1, Date data2) {
        return (data1.compareTo(data2) < 0);
    }

    private void validarDatas(String msg, DateChooser... a) throws ValidacaoException {
        List<DateChooser> datas = Arrays.asList(a);

        int qtdNull = 0;
        int qtdNotNull = 0;

        for (DateChooser d : datas) {
            if (d.getData() == null || (d.getData().getConvertedInput() == null)) {
                qtdNull++;
            } else {
                qtdNotNull++;
            }
        }

        if (qtdNull > 0 && qtdNotNull == 0) {
            setFocusComponentAfterException(datas.get(0));
            throw new ValidacaoException(msg);
        }
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoBotulismoDTO fichaDTO = (FichaInvestigacaoAgravoBotulismoDTO) getFichaDTO();

        if (investigacaoAgravoBotulismo.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoBotulismo.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoBotulismo.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoBotulismoPage.class, CSSFILE))
        );
    }

    private void criarInvestigacao(InvestigacaoAgravoBotulismo proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.addRequiredClass();
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarAntecedentesEpidemiologicos(InvestigacaoAgravoBotulismo proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        dataPrimeiroAtendimento = new DateChooser(path(proxy.getDataPrimeiroAtendimento()));
        dataPrimeiroAtendimento.getData().setMinDate(new DateOption(proxy.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataPrimeiroAtendimento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        txtNumAtendimentoSuspeicaoClinica = new InputField(path(proxy.getNumAtendimentoSuspeicaoClinica()));
        dataSuspeicaoClinica = new DateChooser(path(proxy.getDataSuspeicaoClinica()));
        dataSuspeicaoClinica.getData().setMinDate(new DateOption(proxy.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataSuspeicaoClinica.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerAntecedentesEpidemiologicos.add(dataPrimeiroAtendimento, txtNumAtendimentoSuspeicaoClinica, dataSuspeicaoClinica);
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void criarHospitalizacao(InvestigacaoAgravoBotulismo proxy) {
        containerHospitalizacao = new WebMarkupContainer("containerHospitalizacao");
        containerHospitalizacao.setOutputMarkupId(true);
        containerHospitalizacao.setEnabled(false);

        radioGroupHospitalizacao = new RadioButtonGroup(path(proxy.getHospitalizacao()));
        radioGroupHospitalizacao.setRequired(true);
        createRadioSimNaoIgnorado(radioGroupHospitalizacao, containerHospitalizacao);

        //Unidade Hospital
        dataInternacao = new DateChooser(path(proxy.getDataInternacao()));
        dataAlta = new DateChooser(path(proxy.getDataAlta()));
        autoCompleteUnidadeHospitalizacao = new AutoCompleteConsultaEmpresa(path(proxy.getHospital()));
        unidadeHospitalizacaoCnes = new DisabledInputField(path(proxy.getHospital().getCnes()));
        municipioHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getDescricao()));
        codMunicipioHospital = new DisabledInputField(path(proxy.getHospital().getCidade().getCodigo()));
        estadoHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getEstado().getSigla()));

        containerHospitalizacao.add(
                dataInternacao, dataAlta, autoCompleteUnidadeHospitalizacao, unidadeHospitalizacaoCnes,
                municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao);

        getContainerInformacoesComplementares().add(radioGroupHospitalizacao, containerHospitalizacao);
    }

    private void carregarHospitalizacao() {
        containerHospitalizacao.setEnabled(isLongSim(investigacaoAgravoBotulismo.getHospitalizacao()));

        dataInternacao.getData().setMinDate(new DateOption(investigacaoAgravoBotulismo.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataInternacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataAlta.getData().setMinDate(new DateOption(investigacaoAgravoBotulismo.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataAlta.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        autoCompleteUnidadeHospitalizacao.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                unidadeHospitalizacaoCnes.setComponentValue(empresa.getCnes());
                municipioHospitalizacao.setComponentValue(empresa.getCidade().getDescricao());
                codMunicipioHospital.setComponentValue(empresa.getCidade().getCodigo().toString());

                Cidade cidadeTemp = empresa.getCidade();

                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = LoadManager.getInstance(Cidade.class)
                            .addProperty(VOUtils.montarPath(Cidade.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_DESCRICAO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .setId(empresa.getCidade().getCodigo())
                            .start().getVO();
                }

                estadoHospitalizacao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(unidadeHospitalizacaoCnes, municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao);
            }
        });
        autoCompleteUnidadeHospitalizacao.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                unidadeHospitalizacaoCnes.limpar(target);
                municipioHospitalizacao.limpar(target);
                codMunicipioHospital.limpar(target);
                estadoHospitalizacao.limpar(target);
            }
        });
    }

    private void criarDadosClinicos(InvestigacaoAgravoBotulismo proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        ddSinaisSintomasFebre = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasFebre()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasNausea = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasNausea()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasVomito = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasVomito()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasDiarreia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDiarreia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasConstipacao = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasConstipacao()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasCefaleia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasCefaleia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasTontura = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasTontura()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasVisaoTurva = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasVisaoTurva()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasDiplopia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDiplopia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasDisartria = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDisartria()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasDisfonia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDisfonia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasDisfagia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDisfagia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasBocaSeca = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasBocaSeca()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasFerimento = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasFerimento()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasFacidezPescoco = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasFacidezPescoco()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasInsuficienciaRespiratoria = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasInsuficienciaRespiratoria()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasDispneia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDispneia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasInsuficienciaCardiaca = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasInsuficienciaCardiaca()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasComa = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasComa()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddSinaisSintomasPasrestesia = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasPasrestesia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        txtSinaisSintomasPasrestesiaDescricao = new InputField(path(proxy.getSinaisSintomasPasrestesiaDescricao()));
        txtSinaisSintomasOutros = new InputField(path(proxy.getSinaisSintomasOutros()));

        ddExameNeurologicoPtosePalpebral = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoPtosePalpebral()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddExameNeurologicoOftalmoparesia = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoOftalmoparesia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddExameNeurologicoMidriase = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoMidriase()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddExameNeurologicoParalisiaFacial = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoParalisiaFacial()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddExameNeurologicoComprometimentoMusculaturaBulbar = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoComprometimentoMusculaturaBulbar()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddExameNeurologicoFraquezaMembrosSuperiores = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoFraquezaMembrosSuperiores()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddExameNeurologicoFraquezaMembrosInferiores = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoFraquezaMembrosInferiores()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddExameNeurologicoFraquezaDescendente = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoFraquezaDescendente()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddExameNeurologicoFraquezaSimetrica = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoFraquezaSimetrica()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddExameNeurologicoFraquezaAlteracoesSensibilidade = DropDownUtil.getIEnumDropDown(path(proxy.getExameNeurologicoFraquezaAlteracoesSensibilidade()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);

        ddReflexosNeurologicos = DropDownUtil.getIEnumDropDown(path(proxy.getReflexosNeurologicos()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);

        containerDadosClinicos.add(ddSinaisSintomasFebre, ddSinaisSintomasNausea, ddSinaisSintomasVomito, ddSinaisSintomasDiarreia, ddSinaisSintomasConstipacao, ddSinaisSintomasCefaleia,
                ddSinaisSintomasTontura, ddSinaisSintomasVisaoTurva, ddSinaisSintomasDiplopia, ddSinaisSintomasDisartria, ddSinaisSintomasDisfonia, ddSinaisSintomasDisfagia, ddSinaisSintomasBocaSeca,
                ddSinaisSintomasFerimento, ddSinaisSintomasFacidezPescoco, ddSinaisSintomasInsuficienciaRespiratoria, ddSinaisSintomasDispneia, ddSinaisSintomasInsuficienciaCardiaca, ddSinaisSintomasComa,
                ddSinaisSintomasPasrestesia, txtSinaisSintomasPasrestesiaDescricao, txtSinaisSintomasOutros, ddExameNeurologicoPtosePalpebral, ddExameNeurologicoOftalmoparesia, ddExameNeurologicoMidriase,
                ddExameNeurologicoParalisiaFacial, ddExameNeurologicoComprometimentoMusculaturaBulbar, ddExameNeurologicoFraquezaMembrosSuperiores, ddExameNeurologicoFraquezaMembrosInferiores,
                ddExameNeurologicoFraquezaDescendente, ddExameNeurologicoFraquezaSimetrica, ddExameNeurologicoFraquezaAlteracoesSensibilidade, ddReflexosNeurologicos
        );
        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void carregarDadosClinicos() {
        if (investigacaoAgravoBotulismo.getSinaisSintomasPasrestesiaDescricao() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtSinaisSintomasPasrestesiaDescricao, false, false, null);
        }

        ddSinaisSintomasPasrestesia.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSinaisSintomasPasrestesia, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtSinaisSintomasPasrestesiaDescricao, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtSinaisSintomasPasrestesiaDescricao, false, false, target);
                }
                target.add(txtSinaisSintomasPasrestesiaDescricao);
            }
        });
    }

    private void criarFonteTransmissao(InvestigacaoAgravoBotulismo proxy) {
        containerFonteTransmissao = new WebMarkupContainer("containerFonteTransmissao");
        containerFonteTransmissao.setOutputMarkupId(true);

        ddSuspeitaTransmissaoAlimentar = DropDownUtil.getIEnumDropDown(path(proxy.getSuspeitaTransmissaoAlimentar()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        txtAlimentoSuspeito = new InputField(path(proxy.getSuspeitaTransmissaoAlimentarAlimento()));

        ddAlimentoSuspeitoIndustrial = DropDownUtil.getIEnumDropDown(path(proxy.getAlimentoSuspeitoIndustrial()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddAlimentoSuspeitoCaseira = DropDownUtil.getIEnumDropDown(path(proxy.getAlimentoSuspeitoCaseira()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        txtAlimentoSuspeitoIndustrialMarca = new InputField(path(proxy.getAlimentoSuspeitoIndustrialMarca()));
        dataAlimentoSuspeitoIndustrialDataValidade = new DateChooser(path(proxy.getAlimentoSuspeitoIndustrialDataValidade()));
        txtAlimentoSuspeitoIndustrialLote = new InputField(path(proxy.getAlimentoSuspeitoIndustrialLote()));

        ddExposicaoAlimento = DropDownUtil.getIEnumDropDown(path(proxy.getExposicaoAlimento()), InvestigacaoAgravoBotulismoEnum.ExposicaoAlimentoEnum.values(), true);
        txtTempoIngestaoSintomasUnica = new InputField(path(proxy.getTempoIngestaoSintomasUnica()));
        txtTempoPrimeiraIngestaoSintomasMultipla = new InputField(path(proxy.getTempoPrimeiraIngestaoSintomasMultipla()));
        txtTempoUltimaIngestaoSintomasMultipla = new InputField(path(proxy.getTempoUltimaIngestaoSintomasMultipla()));

        ddLocalIngestaoDomicilio = DropDownUtil.getIEnumDropDown(path(proxy.getLocalIngestaoDomicilio()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddLocalIngestaoCrecheEscola = DropDownUtil.getIEnumDropDown(path(proxy.getLocalIngestaoCrecheEscola()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddLocalIngestaoTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getLocalIngestaoTrabalho()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddLocalIngestaoRestaurante = DropDownUtil.getIEnumDropDown(path(proxy.getLocalIngestaoRestaurante()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddLocalIngestaoFesta = DropDownUtil.getIEnumDropDown(path(proxy.getLocalIngestaoFesta()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        txtLocalIngestaoOutro = new InputField(path(proxy.getLocalIngestaoOutro()));

        cidadeIngestaoAlimentoUF = new DisabledInputField(path(proxy.getCidadeIngestaoAlimento().getEstado().getSigla()));
        autoCompleteCidadeIngestaoAlimento = new AutoCompleteConsultaCidade(path(proxy.getCidadeIngestaoAlimento()));
        cidadeIngestaoAlimentoIbge = new DisabledInputField(path(proxy.getCidadeIngestaoAlimento().getCodigo()));
        txtNumPessoasConsumiramAlimento = new InputField(path(proxy.getNumPessoasConsumiramAlimento()));

        containerFonteTransmissao.add(ddSuspeitaTransmissaoAlimentar, txtAlimentoSuspeito, ddAlimentoSuspeitoIndustrial, ddAlimentoSuspeitoCaseira, txtAlimentoSuspeitoIndustrialMarca,
                dataAlimentoSuspeitoIndustrialDataValidade, txtAlimentoSuspeitoIndustrialLote, ddExposicaoAlimento, txtTempoIngestaoSintomasUnica, txtTempoPrimeiraIngestaoSintomasMultipla,
                txtTempoUltimaIngestaoSintomasMultipla, ddLocalIngestaoDomicilio, ddLocalIngestaoCrecheEscola, ddLocalIngestaoTrabalho, ddLocalIngestaoRestaurante, ddLocalIngestaoFesta,
                txtLocalIngestaoOutro, cidadeIngestaoAlimentoUF, autoCompleteCidadeIngestaoAlimento, cidadeIngestaoAlimentoIbge, txtNumPessoasConsumiramAlimento
        );
        getContainerInformacoesComplementares().add(containerFonteTransmissao);
    }

    private void carregarTransmissao() {
        if (investigacaoAgravoBotulismo.getSuspeitaTransmissaoAlimentarAlimento() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeito, false, false, null);
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialMarca() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, false, false, null);
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialDataValidade() == null)
            dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(false);
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrialLote() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, false, false, null);
        if (investigacaoAgravoBotulismo.getExposicaoAlimento() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddExposicaoAlimento, false, false, null);
        if (investigacaoAgravoBotulismo.getTempoIngestaoSintomasUnica() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoIngestaoSintomasUnica, false, false, null);
        if (investigacaoAgravoBotulismo.getTempoPrimeiraIngestaoSintomasMultipla() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoPrimeiraIngestaoSintomasMultipla, false, false, null);
        if (investigacaoAgravoBotulismo.getTempoUltimaIngestaoSintomasMultipla() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoUltimaIngestaoSintomasMultipla, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoDomicilio() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoDomicilio, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoCrecheEscola() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoCrecheEscola, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoTrabalho() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoTrabalho, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoRestaurante() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoRestaurante, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoFesta() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoFesta, false, false, null);
        if (investigacaoAgravoBotulismo.getLocalIngestaoOutro() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalIngestaoOutro, false, false, null);
        if (investigacaoAgravoBotulismo.getCidadeIngestaoAlimento() == null)
            autoCompleteCidadeIngestaoAlimento.setEnabled(false);
        if (investigacaoAgravoBotulismo.getNumPessoasConsumiramAlimento() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtNumPessoasConsumiramAlimento, false, false, null);
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoIndustrial() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoIndustrial, false, false, null);
        if (investigacaoAgravoBotulismo.getAlimentoSuspeitoCaseira() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoCaseira, false, false, null);
        if (investigacaoAgravoBotulismo.getSuspeitaTransmissaoAlimentar() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddSuspeitaTransmissaoAlimentar, false, false, null);
        ddSuspeitaTransmissaoAlimentar.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSuspeitaTransmissaoAlimentar, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeito, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoIndustrial, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoCaseira, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, true, false, null);
                    dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(true);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddExposicaoAlimento, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoIngestaoSintomasUnica, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoPrimeiraIngestaoSintomasMultipla, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoUltimaIngestaoSintomasMultipla, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoDomicilio, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoCrecheEscola, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoTrabalho, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoRestaurante, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoFesta, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalIngestaoOutro, true, false, null);
                    autoCompleteCidadeIngestaoAlimento.setEnabled(true);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtNumPessoasConsumiramAlimento, true, false, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeito, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoIndustrial, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoCaseira, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, false, false, target);
                    dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(false);
                    dataAlimentoSuspeitoIndustrialDataValidade.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddExposicaoAlimento, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoIngestaoSintomasUnica, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoPrimeiraIngestaoSintomasMultipla, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoUltimaIngestaoSintomasMultipla, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoDomicilio, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoCrecheEscola, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoTrabalho, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoRestaurante, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddLocalIngestaoFesta, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalIngestaoOutro, false, false, target);
                    autoCompleteCidadeIngestaoAlimento.setEnabled(false);
                    autoCompleteCidadeIngestaoAlimento.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtNumPessoasConsumiramAlimento, false, false, target);
                }
                target.add(txtAlimentoSuspeito, ddAlimentoSuspeitoIndustrial, ddAlimentoSuspeitoCaseira, txtAlimentoSuspeitoIndustrialMarca,
                        dataAlimentoSuspeitoIndustrialDataValidade, txtAlimentoSuspeitoIndustrialLote, ddExposicaoAlimento, txtTempoIngestaoSintomasUnica,
                        txtTempoPrimeiraIngestaoSintomasMultipla, txtTempoUltimaIngestaoSintomasMultipla, ddLocalIngestaoDomicilio, ddLocalIngestaoCrecheEscola,
                        ddLocalIngestaoTrabalho, ddLocalIngestaoRestaurante, ddLocalIngestaoFesta, txtLocalIngestaoOutro, autoCompleteCidadeIngestaoAlimento,
                        txtNumPessoasConsumiramAlimento);
            }
        });

        ddAlimentoSuspeitoIndustrial.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAlimentoSuspeitoIndustrial, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoCaseira, true, false, target);
                    ddAlimentoSuspeitoCaseira.setComponentValue(InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.NAO.value());
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, true, false, null);
                    dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(true);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, true, false, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, false, false, target);
                    dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(false);
                    dataAlimentoSuspeitoIndustrialDataValidade.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, false, false, target);
                }
                target.add(ddAlimentoSuspeitoCaseira, txtAlimentoSuspeitoIndustrialMarca, dataAlimentoSuspeitoIndustrialDataValidade, txtAlimentoSuspeitoIndustrialLote);
            }
        });

        ddAlimentoSuspeitoCaseira.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAlimentoSuspeitoCaseira, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddAlimentoSuspeitoIndustrial, true, false, target);
                    ddAlimentoSuspeitoIndustrial.setComponentValue(InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.NAO.value());
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialMarca, false, false, target);
                    dataAlimentoSuspeitoIndustrialDataValidade.setEnabled(false);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtAlimentoSuspeitoIndustrialLote, false, false, target);
                }
                target.add(ddAlimentoSuspeitoIndustrial, txtAlimentoSuspeitoIndustrialMarca, dataAlimentoSuspeitoIndustrialDataValidade, txtAlimentoSuspeitoIndustrialLote);
            }
        });

        ddExposicaoAlimento.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddExposicaoAlimento, InvestigacaoAgravoBotulismoEnum.ExposicaoAlimentoEnum.UNICA.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoIngestaoSintomasUnica, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoPrimeiraIngestaoSintomasMultipla, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoUltimaIngestaoSintomasMultipla, false, false, target);
                }
                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddExposicaoAlimento, InvestigacaoAgravoBotulismoEnum.ExposicaoAlimentoEnum.MULTIPLA.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoIngestaoSintomasUnica, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoPrimeiraIngestaoSintomasMultipla, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtTempoUltimaIngestaoSintomasMultipla, true, false, null);
                }
                target.add(txtTempoIngestaoSintomasUnica, txtTempoPrimeiraIngestaoSintomasMultipla, txtTempoUltimaIngestaoSintomasMultipla);
            }
        });

        autoCompleteCidadeIngestaoAlimento.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                cidadeIngestaoAlimentoIbge.setComponentValue(cidade.getCodigo().toString());
                cidadeIngestaoAlimentoUF.setComponentValue(cidade.getEstado().getSigla());

                target.add(cidadeIngestaoAlimentoIbge, cidadeIngestaoAlimentoUF);
            }
        });
        autoCompleteCidadeIngestaoAlimento.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                cidadeIngestaoAlimentoIbge.limpar(target);
                cidadeIngestaoAlimentoUF.limpar(target);
            }
        });
    }

    private void criarTratamento(InvestigacaoAgravoBotulismo proxy) {
        containerTratamento = new WebMarkupContainer("containerTratamento");
        containerTratamento.setOutputMarkupId(true);

        ddTratamentoAssistenciaVentilatoria = DropDownUtil.getIEnumDropDown(path(proxy.getTratamentoAssistenciaVentilatoria()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddTratamentoAssistenciaSoroAntibotulinico = DropDownUtil.getIEnumDropDown(path(proxy.getTratamentoAssistenciaSoroAntibotulinico()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddTratamentoAssistenciaAntibioticoterapia = DropDownUtil.getIEnumDropDown(path(proxy.getTratamentoAssistenciaAntibioticoterapia()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        txtTratamentoAssistenciaOutro = new InputField(path(proxy.getTratamentoAssistenciaOutro()));

        dataAdministracao = new DateChooser(path(proxy.getDataAdministracao()));
        dataAdministracao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddSoroAntibotulinico = DropDownUtil.getIEnumDropDown(path(proxy.getSoroAntibotulinico()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);

        containerTratamento.add(ddTratamentoAssistenciaVentilatoria, ddTratamentoAssistenciaSoroAntibotulinico, ddTratamentoAssistenciaAntibioticoterapia, txtTratamentoAssistenciaOutro,
                dataAdministracao, ddSoroAntibotulinico
        );
        getContainerInformacoesComplementares().add(containerTratamento);
    }

    private void carregarTratamento() {
        if (investigacaoAgravoBotulismo.getDataAdministracao() == null) dataAdministracao.setEnabled(false);
        if (investigacaoAgravoBotulismo.getSoroAntibotulinico() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddSoroAntibotulinico, false, false, null);

        ddTratamentoAssistenciaSoroAntibotulinico.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddTratamentoAssistenciaSoroAntibotulinico, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    dataAdministracao.setEnabled(true);
                    dataAdministracao.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddSoroAntibotulinico, true, true, null);
                } else {
                    dataAdministracao.setEnabled(false);
                    dataAdministracao.setRequired(false);
                    dataAdministracao.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddSoroAntibotulinico, false, false, target);
                }
                target.add(dataAdministracao, ddSoroAntibotulinico);
            }
        });

    }

    private void criarDadosLaboratorio(InvestigacaoAgravoBotulismo proxy) {
        containerDadosLaboratorio = new WebMarkupContainer("containerDadosLaboratorio");
        containerDadosLaboratorio.setOutputMarkupId(true);

        ddToxinaBotulinicaSoro = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaSoro()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        dataColetaToxinaBotulinicaSoro = new DateChooser(path(proxy.getDataColetaToxinaBotulinicaSoro()));
        dataColetaToxinaBotulinicaSoro.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddToxinaBotulinicaSoroResultado = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaSoroResultado()), InvestigacaoAgravoBotulismoEnum.ResultadoEnum.values(), true);
        ddToxinaBotulinicaSoroTipo = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaSoroTipo()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        ddToxinaBotulinicaFezes = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaFezes()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        dataColetaToxinaBotulinicaFezes = new DateChooser(path(proxy.getDataColetaToxinaBotulinicaFezes()));
        dataColetaToxinaBotulinicaFezes.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddToxinaBotulinicaFezesResultado = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaFezesResultado()), InvestigacaoAgravoBotulismoEnum.ResultadoEnum.values(), true);
        ddToxinaBotulinicaFezesTipo = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaFezesTipo()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        txtToxinaBotulinicaAlimento1 = new InputField(path(proxy.getToxinaBotulinicaAlimento1Nome()));
        ddToxinaBotulinicaAlimento1 = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento1()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        dataColetaToxinaBotulinicaAlimento1 = new DateChooser(path(proxy.getDataColetaToxinaBotulinicaAlimento1()));
        dataColetaToxinaBotulinicaAlimento1.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddToxinaBotulinicaAlimento1Resultado = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento1Resultado()), InvestigacaoAgravoBotulismoEnum.ResultadoEnum.values(), true);
        ddToxinaBotulinicaAlimento1Tipo = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento1Tipo()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        txtToxinaBotulinicaAlimento2 = new InputField(path(proxy.getToxinaBotulinicaAlimento2Nome()));
        ddToxinaBotulinicaAlimento2 = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento2()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        dataColetaToxinaBotulinicaAlimento2 = new DateChooser(path(proxy.getDataColetaToxinaBotulinicaAlimento2()));
        dataColetaToxinaBotulinicaAlimento2.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddToxinaBotulinicaAlimento2Resultado = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento2Resultado()), InvestigacaoAgravoBotulismoEnum.ResultadoEnum.values(), true);
        ddToxinaBotulinicaAlimento2Tipo = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaAlimento2Tipo()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        txtToxinaBotulinicaOutro = new InputField(path(proxy.getToxinaBotulinicaOutrosNome()));
        ddToxinaBotulinicaOutros = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaOutros()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true, true);
        dataColetaToxinaBotulinicaOutros = new DateChooser(path(proxy.getDataColetaToxinaBotulinicaOutros()));
        dataColetaToxinaBotulinicaOutros.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        ddToxinaBotulinicaOutrosResultado = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaOutrosResultado()), InvestigacaoAgravoBotulismoEnum.ResultadoEnum.values(), true);
        ddToxinaBotulinicaOutrosTipo = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotulinicaOutrosTipo()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        ddExamesComplementaresLiquor = DropDownUtil.getIEnumDropDown(path(proxy.getExamesComplementaresLiquor()), InvestigacaoAgravoBotulismoEnum.RealizadoEnum.values(), true, true);
        dataColetaExamesComplementaresLiquor = new DateChooser(path(proxy.getDataColetaExamesComplementaresLiquor()));
        dataColetaExamesComplementaresLiquor.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        txtExamesComplementaresLiquorNumCelular = new InputField(path(proxy.getExamesComplementaresLiquorNumCelular()));
        txtExamesComplementaresLiquorProteinas = new InputField(path(proxy.getExamesComplementaresLiquorProteinas()));

        ddEletroneuromiografia = DropDownUtil.getIEnumDropDown(path(proxy.getEletroneuromiografia()), InvestigacaoAgravoBotulismoEnum.RealizadoEnum.values(), true);
        dataRealizacaoEletroneuromiografia = new DateChooser(path(proxy.getDataRealizacaoEletroneuromiografia()));
        dataRealizacaoEletroneuromiografia.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataRealizacaoEletroneuromiografia.getData().setMinDate(new DateOption(proxy.getRegistroAgravo().getDataPrimeirosSintomas()));
        ddNeuroconducaoSensitiva = DropDownUtil.getIEnumDropDown(path(proxy.getNeuroconducaoSensitiva()), InvestigacaoAgravoBotulismoEnum.NeuroconducaoEnum.values(), true);
        ddNeuroconducaoMotora = DropDownUtil.getIEnumDropDown(path(proxy.getNeuroconducaoMotora()), InvestigacaoAgravoBotulismoEnum.NeuroconducaoEnum.values(), true);
        ddEstimulacaoRepetitiva = DropDownUtil.getIEnumDropDown(path(proxy.getEstimulacaoRepetitiva()), InvestigacaoAgravoBotulismoEnum.EstimulacaoEnum.values(), true);

        containerDadosLaboratorio.add(ddToxinaBotulinicaSoro, dataColetaToxinaBotulinicaSoro, ddToxinaBotulinicaSoroResultado, ddToxinaBotulinicaSoroTipo, ddToxinaBotulinicaFezes,
                dataColetaToxinaBotulinicaFezes, ddToxinaBotulinicaFezesResultado, ddToxinaBotulinicaFezesTipo, txtToxinaBotulinicaAlimento1, ddToxinaBotulinicaAlimento1, dataColetaToxinaBotulinicaAlimento1,
                ddToxinaBotulinicaAlimento1Resultado, ddToxinaBotulinicaAlimento1Tipo, txtToxinaBotulinicaAlimento2, ddToxinaBotulinicaAlimento2, dataColetaToxinaBotulinicaAlimento2, ddToxinaBotulinicaAlimento2Resultado,
                ddToxinaBotulinicaAlimento2Tipo, txtToxinaBotulinicaOutro, ddToxinaBotulinicaOutros, dataColetaToxinaBotulinicaOutros, ddToxinaBotulinicaOutrosResultado, ddToxinaBotulinicaOutrosTipo,
                ddExamesComplementaresLiquor, dataColetaExamesComplementaresLiquor, txtExamesComplementaresLiquorNumCelular, txtExamesComplementaresLiquorProteinas, ddEletroneuromiografia,
                dataRealizacaoEletroneuromiografia, ddNeuroconducaoSensitiva, ddNeuroconducaoMotora, ddEstimulacaoRepetitiva
        );
        getContainerInformacoesComplementares().add(containerDadosLaboratorio);
    }

    private void carregarDadosLaboratorio() {
        if (investigacaoAgravoBotulismo.getDataColetaToxinaBotulinicaSoro() == null)
            dataColetaToxinaBotulinicaSoro.setEnabled(false);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaSoroResultado() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroResultado, false, false, null);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaSoroTipo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroTipo, false, false, null);

        if (investigacaoAgravoBotulismo.getDataColetaToxinaBotulinicaFezes() == null)
            dataColetaToxinaBotulinicaFezes.setEnabled(false);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaFezesResultado() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesResultado, false, false, null);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaFezesTipo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesTipo, false, false, null);

        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento1Nome() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento1, false, false, null);
        if (investigacaoAgravoBotulismo.getDataColetaToxinaBotulinicaAlimento1() == null)
            dataColetaToxinaBotulinicaAlimento1.setEnabled(false);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento1Resultado() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Resultado, false, false, null);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento1Tipo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Tipo, false, false, null);

        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento2Nome() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento2, false, false, null);
        if (investigacaoAgravoBotulismo.getDataColetaToxinaBotulinicaAlimento2() == null)
            dataColetaToxinaBotulinicaAlimento2.setEnabled(false);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento2Resultado() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Resultado, false, false, null);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaAlimento2Tipo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Tipo, false, false, null);

        if (investigacaoAgravoBotulismo.getToxinaBotulinicaOutrosNome() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaOutro, false, false, null);
        if (investigacaoAgravoBotulismo.getDataColetaToxinaBotulinicaOutros() == null)
            dataColetaToxinaBotulinicaOutros.setEnabled(false);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaOutrosResultado() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosResultado, false, false, null);
        if (investigacaoAgravoBotulismo.getToxinaBotulinicaOutrosTipo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosTipo, false, false, null);

        if (investigacaoAgravoBotulismo.getDataColetaExamesComplementaresLiquor() == null)
            dataColetaExamesComplementaresLiquor.setEnabled(false);
        if (investigacaoAgravoBotulismo.getExamesComplementaresLiquorNumCelular() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorNumCelular, false, false, null);
        if (investigacaoAgravoBotulismo.getExamesComplementaresLiquorProteinas() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorProteinas, false, false, null);

        if (investigacaoAgravoBotulismo.getDataRealizacaoEletroneuromiografia() == null)
            dataRealizacaoEletroneuromiografia.setEnabled(false);
        if (investigacaoAgravoBotulismo.getNeuroconducaoSensitiva() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoSensitiva, false, false, null);
        if (investigacaoAgravoBotulismo.getNeuroconducaoMotora() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoMotora, false, false, null);
        if (investigacaoAgravoBotulismo.getEstimulacaoRepetitiva() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddEstimulacaoRepetitiva, false, false, null);

        ddToxinaBotulinicaSoro.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotulinicaSoro, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    dataColetaToxinaBotulinicaSoro.setEnabled(true);
                    dataColetaToxinaBotulinicaSoro.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroResultado, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroTipo, true, true, null);
                } else {
                    dataColetaToxinaBotulinicaSoro.setEnabled(false);
                    dataColetaToxinaBotulinicaSoro.setRequired(false);
                    dataColetaToxinaBotulinicaSoro.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroResultado, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaSoroTipo, false, false, target);
                }
                target.add(dataColetaToxinaBotulinicaSoro, ddToxinaBotulinicaSoroResultado, ddToxinaBotulinicaSoroTipo);
            }
        });

        ddToxinaBotulinicaFezes.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotulinicaFezes, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    dataColetaToxinaBotulinicaFezes.setEnabled(true);
                    dataColetaToxinaBotulinicaFezes.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesResultado, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesTipo, true, true, null);
                } else {
                    dataColetaToxinaBotulinicaFezes.setEnabled(false);
                    dataColetaToxinaBotulinicaFezes.setRequired(false);
                    dataColetaToxinaBotulinicaFezes.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesResultado, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaFezesTipo, false, false, target);
                }
                target.add(dataColetaToxinaBotulinicaFezes, ddToxinaBotulinicaFezesResultado, ddToxinaBotulinicaFezesTipo);
            }
        });

        ddToxinaBotulinicaAlimento1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotulinicaAlimento1, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento1, true, true, null);
                    dataColetaToxinaBotulinicaAlimento1.setEnabled(true);
                    dataColetaToxinaBotulinicaAlimento1.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Resultado, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Tipo, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento1, false, false, target);
                    dataColetaToxinaBotulinicaAlimento1.setEnabled(false);
                    dataColetaToxinaBotulinicaAlimento1.setRequired(false);
                    dataColetaToxinaBotulinicaAlimento1.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Resultado, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento1Tipo, false, false, target);
                }
                target.add(txtToxinaBotulinicaAlimento1, dataColetaToxinaBotulinicaAlimento1, ddToxinaBotulinicaAlimento1Resultado, ddToxinaBotulinicaAlimento1Tipo);
            }
        });

        ddToxinaBotulinicaAlimento2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotulinicaAlimento2, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento2, true, true, null);
                    dataColetaToxinaBotulinicaAlimento2.setEnabled(true);
                    dataColetaToxinaBotulinicaAlimento2.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Resultado, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Tipo, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaAlimento2, false, false, target);
                    dataColetaToxinaBotulinicaAlimento2.setEnabled(false);
                    dataColetaToxinaBotulinicaAlimento2.setRequired(false);
                    dataColetaToxinaBotulinicaAlimento2.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Resultado, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaAlimento2Tipo, false, false, target);
                }
                target.add(txtToxinaBotulinicaAlimento2, dataColetaToxinaBotulinicaAlimento2, ddToxinaBotulinicaAlimento2Resultado, ddToxinaBotulinicaAlimento2Tipo);
            }
        });

        ddToxinaBotulinicaOutros.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotulinicaOutros, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaOutro, true, true, null);
                    dataColetaToxinaBotulinicaOutros.setEnabled(true);
                    dataColetaToxinaBotulinicaOutros.setRequired(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosResultado, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosTipo, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtToxinaBotulinicaOutro, false, false, target);
                    dataColetaToxinaBotulinicaOutros.setEnabled(false);
                    dataColetaToxinaBotulinicaOutros.setRequired(false);
                    dataColetaToxinaBotulinicaOutros.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosResultado, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddToxinaBotulinicaOutrosTipo, false, false, target);
                }
                target.add(txtToxinaBotulinicaOutro, dataColetaToxinaBotulinicaOutros, ddToxinaBotulinicaOutrosResultado, ddToxinaBotulinicaOutrosTipo);
            }
        });

        ddExamesComplementaresLiquor.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddExamesComplementaresLiquor, InvestigacaoAgravoBotulismoEnum.RealizadoEnum.REALIZADO.value())) {
                    dataColetaExamesComplementaresLiquor.setEnabled(true);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorNumCelular, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorProteinas, true, false, null);
                } else {
                    dataColetaExamesComplementaresLiquor.setEnabled(false);
                    dataColetaExamesComplementaresLiquor.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorNumCelular, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtExamesComplementaresLiquorProteinas, false, false, target);
                }
                target.add(dataColetaExamesComplementaresLiquor, txtExamesComplementaresLiquorNumCelular, txtExamesComplementaresLiquorProteinas);
            }
        });

        ddEletroneuromiografia.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddEletroneuromiografia, InvestigacaoAgravoBotulismoEnum.RealizadoEnum.REALIZADO.value())) {
                    dataRealizacaoEletroneuromiografia.setEnabled(true);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoSensitiva, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoMotora, true, false, null);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddEstimulacaoRepetitiva, true, false, null);
                } else {
                    dataRealizacaoEletroneuromiografia.setEnabled(false);
                    dataRealizacaoEletroneuromiografia.limpar(target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoSensitiva, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddNeuroconducaoMotora, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddEstimulacaoRepetitiva, false, false, target);
                }
                target.add(dataRealizacaoEletroneuromiografia, ddNeuroconducaoSensitiva, ddNeuroconducaoMotora, ddEstimulacaoRepetitiva);
            }
        });
    }

    private void criarConclusao(InvestigacaoAgravoBotulismo proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddClassificacaoFinal = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaofinal()), InvestigacaoAgravoBotulismoEnum.ClassificacaoFinalEnum.values(), true, true);
        txtClassificacaoFinalEspecificar = new InputField(path(proxy.getClassificacaoFinalEspecificar()));
        ddCriterioConfirmacaoDescarte = DropDownUtil.getIEnumDropDown(path(proxy.getCriterioConfirmacaoDescarte()), InvestigacaoAgravoBotulismoEnum.CriterioConfirmacaoDescarteEnum.values(), true);
        ddFormaBotulismo = DropDownUtil.getIEnumDropDown(path(proxy.getFormaBotulismo()), InvestigacaoAgravoBotulismoEnum.FormaBotulismoEnum.values(), true);

        ddToxinaBotuliticaClinica = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotuliticaClinica()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);
        ddToxinaBotuliticaBromatologica = DropDownUtil.getIEnumDropDown(path(proxy.getToxinaBotuliticaBromatologica()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);

        ddTipoToxinaIsoladaClinica = DropDownUtil.getIEnumDropDown(path(proxy.getTipoToxinaIsoladaClinica()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);
        ddTipoToxinaBotuliticaBromatologica = DropDownUtil.getIEnumDropDown(path(proxy.getTipoToxinaBotuliticaBromatologica()), InvestigacaoAgravoBotulismoEnum.TipoToxinaEnum.values(), true);

        txtCausaAlimento = new InputField(path(proxy.getCausaAlimento()));
        ddDoencaRelacionadaTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getDoencaRelacionadaTrabalho()), InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.values(), true);

        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), InvestigacaoAgravoBotulismoEnum.EvolucaoCasoEnum.values(), true);
        dataObito = new DateChooser(path(proxy.getDataObito()));
        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerConclusao.add(ddClassificacaoFinal, txtClassificacaoFinalEspecificar, ddCriterioConfirmacaoDescarte, ddFormaBotulismo, ddToxinaBotuliticaClinica, ddToxinaBotuliticaBromatologica,
                ddTipoToxinaIsoladaClinica, ddTipoToxinaBotuliticaBromatologica, txtCausaAlimento, ddDoencaRelacionadaTrabalho, ddEvolucaoCaso, dataObito
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao() {
        if (investigacaoAgravoBotulismo.getFormaBotulismo() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddFormaBotulismo, false, false, null);
        if (investigacaoAgravoBotulismo.getClassificacaoFinalEspecificar() == null)
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtClassificacaoFinalEspecificar, false, false, null);

        if (investigacaoAgravoBotulismo.getTipoToxinaIsoladaClinica() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaIsoladaClinica, false, false, null);
        if (investigacaoAgravoBotulismo.getTipoToxinaBotuliticaBromatologica() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaBotuliticaBromatologica, false, false, null);

        if (investigacaoAgravoBotulismo.getDataObito() == null)
            FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, false, false, null);

        ddClassificacaoFinal.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddClassificacaoFinal, InvestigacaoAgravoBotulismoEnum.ClassificacaoFinalEnum.CONFIRMADO.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddFormaBotulismo, true, true, null);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtClassificacaoFinalEspecificar, false, false, target);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddFormaBotulismo, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtClassificacaoFinalEspecificar, true, false, null);
                }
                target.add(ddFormaBotulismo, txtClassificacaoFinalEspecificar);
            }
        });

        ddToxinaBotuliticaClinica.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotuliticaClinica, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaIsoladaClinica, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaIsoladaClinica, false, false, target);
                }
                target.add(ddTipoToxinaIsoladaClinica);
            }
        });

        ddToxinaBotuliticaBromatologica.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddToxinaBotuliticaBromatologica, InvestigacaoAgravoBotulismoEnum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaBotuliticaBromatologica, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddTipoToxinaBotuliticaBromatologica, false, false, target);
                }
                target.add(ddTipoToxinaBotuliticaBromatologica);
            }
        });

        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if (!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoBotulismoEnum.EvolucaoCasoEnum.OBITO_BOTULISMO.value())
                || FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, InvestigacaoAgravoBotulismoEnum.EvolucaoCasoEnum.OBITO_OUTRAS_CAUSAS.value()))) {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, true, true, null);
                } else {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, false, false, target);
                }
                target.add(dataObito);
            }
        });
    }


    private void criarObservacoes(InvestigacaoAgravoBotulismo proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);

        observacao = new InputArea(path(proxy.getObservacao()));
        containerObservacoes.add(observacao);

        criarAlimentos();
        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarAlimentos() {
        InvestigacaoAgravoBotulismoAlimentos proxy = on(InvestigacaoAgravoBotulismoAlimentos.class);
        containerAlimentos = new WebMarkupContainer("containerAlimentos", modelAlimentos = new CompoundPropertyModel(new InvestigacaoAgravoBotulismoAlimentos()));

        containerAlimentos.setOutputMarkupId(true);

        txtTipoAlimento = new InputField(path(proxy.getTipoAlimento()));
        txtTipoAlimento.addAjaxUpdateValue();
        txtLocalConsumo = new InputField(path(proxy.getLocalConsumo()));
        txtLocalConsumo.addAjaxUpdateValue();

        AbstractAjaxButton btnAdicionarAlimentos;

        containerAlimentos.add(btnAdicionarAlimentos = new AbstractAjaxButton("btnAdicionarAlimentos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                adicionarAlimentos(target);
            }
        });

        btnAdicionarAlimentos.setDefaultFormProcessing(false);

        containerAlimentos.add(tblAlimentos = new Table("tblAlimentos", getColumnsAlimentos(), getCollectionProviderAlimentos()));
        tblAlimentos.populate();

        containerAlimentos.add(txtTipoAlimento, txtLocalConsumo);
        containerObservacoes.add(containerAlimentos);
    }

    private void adicionarAlimentos(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoBotulismoAlimentos alimentos = modelAlimentos.getObject();

        if (txtTipoAlimento.getComponentValue() == null) {
            throw new ValidacaoException(bundle("informeTipoAlimento"));
        }
        if (txtLocalConsumo.getComponentValue() == null) {
            throw new ValidacaoException(bundle("informeLocalConsumo"));
        }

        alimentosList.add(alimentos);
        tblAlimentos.update(target);
        tblAlimentos.populate();

        modelAlimentos.setObject(new InvestigacaoAgravoBotulismoAlimentos());
        ComponentUtils.limparContainer(containerAlimentos, target);
    }

    private List<IColumn> getColumnsAlimentos() {
        List<IColumn> columns = new ArrayList<>();
        InvestigacaoAgravoBotulismoAlimentos proxy = on(InvestigacaoAgravoBotulismoAlimentos.class);
        columns.add(getActionColumnAlimentos());
        columns.add(createColumn(bundle("tipoAlimento"), proxy.getTipoAlimento()));
        columns.add(createColumn(bundle("localConsumo"), proxy.getLocalConsumo()));

        return columns;
    }

    private IColumn getActionColumnAlimentos() {
        return new MultipleActionCustomColumn<InvestigacaoAgravoBotulismoAlimentos>() {
            @Override
            public void customizeColumn(InvestigacaoAgravoBotulismoAlimentos rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<InvestigacaoAgravoBotulismoAlimentos>() {
                    @Override
                    public void action(AjaxRequestTarget target, InvestigacaoAgravoBotulismoAlimentos modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblAlimentos, alimentosList, modelObject);
                        tblAlimentos.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderAlimentos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (alimentosList == null) {
                    alimentosList = new ArrayList<>();
                }
                return alimentosList;
            }
        };
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoBotulismo proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }


}
