package br.com.celk.view.materiais.dispensacao.prescricaoatendimento.dialog;

import br.com.celk.component.window.Window;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgEditaCodigoBarrasLote extends Window {

    private PnlEditaCodigoBarrasLote pnlEditaCodigoBarrasLote;

    public DlgEditaCodigoBarrasLote(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(800);
        setInitialHeight(400);

        setResizable(false);

        setTitle(bundle("dispensar"));

        setContent(pnlEditaCodigoBarrasLote = new PnlEditaCodigoBarrasLote(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgEditaCodigoBarrasLote.this.onConfirmar(target, pnlEditaCodigoBarrasLote.getObject(), pnlEditaCodigoBarrasLote.getQuantidade(), pnlEditaCodigoBarrasLote.getLotes());
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object, Long quantidade, List<MovimentoGrupoEstoqueItemDTO> lotes) throws ValidacaoException, DAOException;

    public void setObject(AjaxRequestTarget target, DispensacaoMedicamentoItemDTO object) {
        pnlEditaCodigoBarrasLote.setObject(target, object);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
    }
}
