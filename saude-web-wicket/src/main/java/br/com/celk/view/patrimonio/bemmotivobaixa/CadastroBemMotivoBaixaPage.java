package br.com.celk.view.patrimonio.bemmotivobaixa;

import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.template.cadastro.CadastroPage;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.vo.patrimonio.BemMotivoBaixa;

/**
 *
 * <AUTHOR>
 */
public class CadastroBemMotivoBaixaPage extends CadastroPage<BemMotivoBaixa>{

    public CadastroBemMotivoBaixaPage() {
    }

    public CadastroBemMotivoBaixaPage(BemMotivoBaixa object) {
        super(object);
    }

    @Override
    public void init(Form form) {
        BemMotivoBaixa proxy = on(BemMotivoBaixa.class);
        
        form.add(new RequiredInputField(path(proxy.getDescricao())));
    }
    
   

    @Override
    public Class<BemMotivoBaixa> getReferenceClass() {
        return BemMotivoBaixa.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaBemMotivoBaixaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroBemMotivoBaixa");
    }

}
