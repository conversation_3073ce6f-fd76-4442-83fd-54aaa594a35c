package br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.Util;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.orgaoemissor.pnl.PnlConsultaOrgaoEmissor;
import br.com.celk.view.cadsus.usuariocadsus.CadastroUsuarioCidadaoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ServicoCnsUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ServicoCnsUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusMotivoCPF;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matchers;

import java.util.List;
import java.util.Objects;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DocumentosUsuarioCidadaoTab extends TabPanel<CadastroUsuarioCidadaoDTO> {

    private WebMarkupContainer containerCertidao;
    private UpperField txtMatricula;
    private UpperField txtCpf;
    private UpperField txtNumeroRg;
    private PnlConsultaOrgaoEmissor pnlorgaoemissor;
    private WebMarkupContainer containerCadastroPaciente;
    private WebMarkupContainer containerFlagCpf;
    private CheckBoxLongValue cbxFlagNaoPossuiCpf;
    private String validarCpfCadastro;
    private WebMarkupContainer containerCpfMotivo;
    private WebMarkupContainer containerEleitor;
    private DropDown<UsuarioCadsusMotivoCPF> dropDownMotivoCpf;
    private String categorizacaoMotivoCpf;
    private AbstractAjaxButton btnConsultaCpfWS;
    private Boolean utilizarServicoWS;


    public DocumentosUsuarioCidadaoTab(String id, CadastroUsuarioCidadaoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        CadastroUsuarioCidadaoDTO proxy = on(CadastroUsuarioCidadaoDTO.class);

        atualizarParametrosGem();

        add(txtCpf = new UpperField(path(proxy.getUsuarioCadsus().getCpf())));
        txtCpf.setLabel(new Model(bundle("cpf")));
        add(txtNumeroRg = new UpperField(path(proxy.getDocumentoIdentidade().getNumeroDocumento())));
        add(new UpperField(path(proxy.getDocumentoIdentidade().getNumeroDocumentoComplementar())));
        add(new UpperField(path(proxy.getDocumentoIdentidade().getSiglaUf())));
        add(new DateChooser(path(proxy.getDocumentoIdentidade().getDataEmissao())));
        add(pnlorgaoemissor = new PnlConsultaOrgaoEmissor(path(proxy.getDocumentoIdentidade().getOrgaoEmissor())));

        containerCertidao = new WebMarkupContainer("containerCertidao");
        containerCertidao.setOutputMarkupId(true);

        add(containerCertidao);

        containerCertidao.add(txtMatricula = new UpperField(path(proxy.getDocumentoCertidao().getNumeroMatricula())));
        containerCertidao.add(getDropDownTipoDocumento(path(proxy.getDocumentoCertidao().getTipoDocumento().getCodigo())));
        containerCertidao.add(new UpperField(path(proxy.getDocumentoCertidao().getNumeroFolha())));
        containerCertidao.add(new UpperField(path(proxy.getDocumentoCertidao().getNumeroTermo())));
        containerCertidao.add(new UpperField(path(proxy.getDocumentoCertidao().getNumeroLivro())));
        containerCertidao.add(new DateChooser(path(proxy.getDocumentoCertidao().getDataEmissao())));
        containerCertidao.add(new UpperField(path(proxy.getDocumentoCertidao().getNumeroCartorio())));
        txtMatricula.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                eventoNumeroMatricula(target);
            }
        });

        containerEleitor = containerEleitor(proxy);
        add(containerEleitor);

        add(new UpperField(path(proxy.getDocumentoPis().getNumeroDocumento())));
        add(new UpperField(path(proxy.getUsuarioCadsus().getNis())));

        if (this.object.getDocumentoCertidao() == null) {
            this.object.setDocumentoCertidao(new UsuarioCadsusDocumento());
            this.object.getDocumentoCertidao().setTipoDocumento(new TipoDocumentoUsuario());
        }
        if (this.object.getDocumentoIdentidade() == null) {
            this.object.setDocumentoIdentidade(new UsuarioCadsusDocumento());
            this.object.getDocumentoIdentidade().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_RG));
        }
        if (this.object.getDocumentoPis() == null) {
            this.object.setDocumentoPis(new UsuarioCadsusDocumento());
            this.object.getDocumentoPis().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_PIS));
        }
        if (this.object.getDocumentoEleitor() == null) {
            this.object.setDocumentoEleitor(new UsuarioCadsusDocumento());
            this.object.getDocumentoEleitor().setTipoDocumento(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_TITULO_ELEITOR));
        }

        containerFlagCpf = new WebMarkupContainer("containerFlagCpf");
        add(containerFlagCpf);
        containerFlagCpf.setOutputMarkupId(true);
        cbxFlagNaoPossuiCpf = new CheckBoxLongValue(path(proxy.getUsuarioCadsus().getFlagNaoPossuiCpf()), RepositoryComponentDefault.SIM_LONG);
        containerFlagCpf.add(cbxFlagNaoPossuiCpf);

        if (RepositoryComponentDefault.SIM.equals(validarCpfCadastro)) {
            object.getUsuarioCadsus().setFlagNaoPossuiCpf(RepositoryComponentDefault.NAO_LONG);
            containerFlagCpf.setEnabled(false);
            containerFlagCpf.setVisible(false);
            categorizacaoMotivoCpf = RepositoryComponentDefault.NAO;
        }

        txtCpf.setRequired(true);
        txtCpf.addRequiredClass();
        containerMotivoCpf();
        cbxFlagNaoPossuiCpf.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                txtCpf.setEnabled(false);
                txtCpf.setRequired(false);
                txtCpf.removeRequiredClass();
                if (!RepositoryComponentDefault.SIM_LONG.equals(cbxFlagNaoPossuiCpf.getComponentValue())) {
                    txtCpf.setEnabled(true);
                    txtCpf.addRequiredClass();
                    txtCpf.setRequired(true);
                    dropDownMotivoCpf.setRequired(false);
                    dropDownMotivoCpf.removeRequiredClass();
                    dropDownMotivoCpf.setEnabled(false);
                }else{
                    if (RepositoryComponentDefault.SIM.equals(categorizacaoMotivoCpf)) {
                        dropDownMotivoCpf.setEnabled(true);
                        dropDownMotivoCpf.addRequiredClass();
                        dropDownMotivoCpf.setRequired(true);
                    }
                }
                txtCpf.limpar(art);
                dropDownMotivoCpf.limpar(art);
                art.add(dropDownMotivoCpf);
                art.add(txtCpf);
                art.appendJavaScript(JScript.initMasks());
            }
        });

        habilitarCampoCpf(object.getUsuarioCadsus().getFlagNaoPossuiCpf());

        add(btnConsultaCpfWS = new AbstractAjaxButton("btnConsultaCpfWS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                consultaCpfporCns(target);
            }

            @Override
            public boolean isVisible() {
                return Util.isNotNull(object.getUsuarioCadsus().getCodigo()) && isUtilizarServicoWS() ;
            }
        });
        btnConsultaCpfWS.setDefaultFormProcessing(false);
    }

    private void consultaCpfporCns(AjaxRequestTarget target) throws ValidacaoException {
        if (Util.isNotNull( object.getUsuarioCadsus().getCns()) && RepositoryComponentDefault.NAO_LONG.equals(cbxFlagNaoPossuiCpf.getComponentValue())){
        try {
                ServicoCnsUsuarioCadsusDTOParam param = new ServicoCnsUsuarioCadsusDTOParam();
                param.setCns(object.getUsuarioCadsus().getCns());
                List<ServicoCnsUsuarioCadsusDTO> usuariosCadsus = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).usuariosCadsusWS(param);
                if (CollectionUtils.isNotNullEmpty(usuariosCadsus)) {
                    ServicoCnsUsuarioCadsusDTO servicoCnsUsuarioCadsusDTO = Lambda.selectFirst(usuariosCadsus, Lambda.having(on(ServicoCnsUsuarioCadsusDTO.class).getDataNascimento(), Matchers.equalTo(object.getUsuarioCadsus().getDataNascimento())));
                    if (servicoCnsUsuarioCadsusDTO != null) {
                        txtCpf.limpar(target);
                        txtCpf.setComponentValue(servicoCnsUsuarioCadsusDTO.getUsuarioCadsusWSDTO().getCpf());
                        target.add(txtCpf);
                    }
                }
            } catch (DAOException e) {
                throw new ValidacaoException(e.getMessage());
            }
        }
    }

    private Boolean isUtilizarServicoWS() {
        if (utilizarServicoWS == null) {
            try {
                String utilizaServicoIntegracao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("UtilizaServicoIntegracaoCadsusPixPDQ");
                utilizarServicoWS = RepositoryComponentDefault.SIM.equals(utilizaServicoIntegracao);
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage(), e);
            }
        }
        return utilizarServicoWS;
    }

    private void containerMotivoCpf() {

        containerCpfMotivo = new WebMarkupContainer("containerCpfMotivo");
        add(containerCpfMotivo);
        containerCpfMotivo.setOutputMarkupId(true);
        dropDownMotivoCpf = new DropDown<>("usuarioCadsus.usuarioCadsusMotivoCPF");
        dropDownMotivoCpf.setLabel(new Model(bundle("categorizacaoMotivoCpf")));
        containerCpfMotivo.add(dropDownMotivoCpf);

        List<UsuarioCadsusMotivoCPF> motivos = LoadManager.getInstance(UsuarioCadsusMotivoCPF.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusMotivoCPF.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                .addSorter(new QueryCustom.QueryCustomSorter(UsuarioCadsusMotivoCPF.PROP_CODIGO))
                .start().getList();
        dropDownMotivoCpf.addChoice(null, "");
        for (UsuarioCadsusMotivoCPF motivo : motivos) {
            dropDownMotivoCpf.addChoice(motivo, motivo.getDescricao());
        }


        if (RepositoryComponentDefault.SIM.equals(categorizacaoMotivoCpf) && RepositoryComponentDefault.NAO.equals(validarCpfCadastro)) {
            containerCpfMotivo.setVisible(true);
            if  (RepositoryComponentDefault.SIM_LONG.equals(object.getUsuarioCadsus().getFlagNaoPossuiCpf())) {
                dropDownMotivoCpf.setEnabled(true);
                dropDownMotivoCpf.addRequiredClass();
                dropDownMotivoCpf.setRequired(true);
            }else {
                dropDownMotivoCpf.setRequired(false);
                dropDownMotivoCpf.removeRequiredClass();
                dropDownMotivoCpf.setEnabled(false);
            }

        }else{
            containerCpfMotivo.setVisible(false);
        }
    }

    private DropDown<Long> getDropDownTipoDocumento(String id) {
        DropDown cbxTipoCertidao = new DropDown<Long>(id);

        cbxTipoCertidao.addChoice(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO, BundleManager.getString("nascimento"));
        cbxTipoCertidao.addChoice(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO, BundleManager.getString("casamento"));

        return cbxTipoCertidao;
    }

    private void atualizarParametrosGem() {
        try {
            validarCpfCadastro = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarCpfCadastro");
            categorizacaoMotivoCpf = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("exigeCategorizacaoCPF");
        } catch (ValidacaoRuntimeException | DAOException ex) {
            DocumentosUsuarioCidadaoTab.this.warn(ex.getMessage());
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void habilitarCampoCpf(Long flagNaoPossuiCpf) {
        if (Util.isNotNull(flagNaoPossuiCpf) && RepositoryComponentDefault.SIM_LONG.equals(flagNaoPossuiCpf)) {
            txtCpf.setEnabled(false);
        }
    }

    private WebMarkupContainer containerEleitor(CadastroUsuarioCidadaoDTO proxy) {
        WebMarkupContainer containerEleitor = new WebMarkupContainer("containerEleitor");

        containerEleitor.add(new UpperField(path(proxy.getDocumentoEleitor().getNumeroDocumento())));
        containerEleitor.add(new InputField<Long>(path(proxy.getDocumentoEleitor().getNumeroZonaEleitoral())));
        containerEleitor.add(new InputField<Long>(path(proxy.getDocumentoEleitor().getNumeroSecaoEleitoral())));
        containerEleitor.add(new UpperField(path(proxy.getDocumentoEleitor().getSiglaUf())));
        containerEleitor.add(new AutoCompleteConsultaCidade(path(proxy.getDocumentoEleitor().getCidadeDocumento())));
        containerEleitor.add(new DateChooser(path(proxy.getDocumentoEleitor().getDataEmissao())));
        return containerEleitor;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("documentos");
    }

    private void eventoNumeroMatricula(AjaxRequestTarget target) {
        String numeroMatricula = object.getDocumentoCertidao().getNumeroMatricula();
        if (numeroMatricula != null) {

            numeroMatricula = StringUtilKsi.getDigits(numeroMatricula);
            String tipoCertidao = numeroMatricula.substring(14, 15);
            if (tipoCertidao.equals("1")) {
                object.getDocumentoCertidao().getTipoDocumento().setCodigo(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO);
            } else if (tipoCertidao.equals("2")) {
                object.getDocumentoCertidao().getTipoDocumento().setCodigo(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO);
            }
            String folha = numeroMatricula.substring(20, 23);
            object.getDocumentoCertidao().setNumeroFolha(folha);
            String termo = numeroMatricula.substring(23, 30);
            object.getDocumentoCertidao().setNumeroTermo(termo);
            String livro = numeroMatricula.substring(15, 20);
            object.getDocumentoCertidao().setNumeroLivro(livro);
            String cartorio = numeroMatricula.substring(0, 6);
            object.getDocumentoCertidao().setNumeroCartorio(cartorio);
        }
        target.add(containerCertidao);
        target.appendJavaScript(JScript.initMasks());
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return this.txtCpf;
    }

    @Override
    public void onAjaxUpdate(AjaxRequestTarget target) {
        super.onAjaxUpdate(target);
        if (Objects.isNull(object.getUsuarioCadsus().getCodigo())) {
            pnlorgaoemissor.setVO(target, (OrgaoEmissor) pnlorgaoemissor.getModelObject());
            target.add(pnlorgaoemissor);
        }
    }
}