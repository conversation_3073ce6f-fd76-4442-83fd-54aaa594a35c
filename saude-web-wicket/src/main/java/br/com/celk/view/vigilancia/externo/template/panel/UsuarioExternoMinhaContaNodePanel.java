package br.com.celk.view.vigilancia.externo.template.panel;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.component.validator.CpfFieldValidator;
import br.com.celk.component.validator.PasswordValidator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.vigilancia.externo.template.UsuarioExternoDefaultCadastroPanel;
import br.com.celk.view.vigilancia.externo.template.configuracoes.ConfiguracaoUsuarioExternoPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.EmailTextField;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class UsuarioExternoMinhaContaNodePanel extends UsuarioExternoDefaultCadastroPanel {

    private Form<Usuario> form;

    private String senha;
    private String novaSenha;
    private String confirmacaoNovaSenha;
    private String identificador;

    public UsuarioExternoMinhaContaNodePanel(String id) {
        super(id, bundle("dados_usuario"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Usuario proxy = on(Usuario.class);
        form = new Form<Usuario>("form", new CompoundPropertyModel(new Usuario()));
        carregarUsuario();

        form.add(new DisabledInputField(path(proxy.getLogin())));
        form.add(new DisabledInputField(path(proxy.getNome())));
        form.add(new PasswordField("senha", new PropertyModel<String>(this, "senha")));
        form.add(new PasswordField("novaSenha", new PropertyModel<String>(this, "novaSenha")).add(new PasswordValidator()));
        form.add(new PasswordField("confirmacaoNovaSenha", new PropertyModel<String>(this, "confirmacaoNovaSenha")));
        form.add(new EmailTextField(path(proxy.getEmail())));
        form.add(new DisabledInputField(path(proxy.getCpf())).add(CpfFieldValidator.getInstance()));
        form.add(new InputField(path(proxy.getTelefone())));

        form.add(getContainerTermoAceite());


        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));

        add(form);
        add(form);
    }

    public WebMarkupContainer getContainerTermoAceite() {
        WebMarkupContainer containerTermosAceite = new WebMarkupContainer("containerTermosAceite");
        containerTermosAceite.setOutputMarkupId(true);

        final String urlTermosAceite = "https://celk-public-files.s3-sa-east-1.amazonaws.com/politica-privacidade/politica-privacidade_latest.pdf";

        containerTermosAceite.add(new AbstractAjaxLink("btnTermosAceite") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                target.appendJavaScript("setTimeout(\"window.open('" + urlTermosAceite + "','_blank')\", 100);");
            }
        });

        return containerTermosAceite;
    }

    private Usuario carregarUsuario() {
        Usuario usuario = LoadManager.getInstance(Usuario.class)
                .addProperties(new HQLProperties(Usuario.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_CODIGO), ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getCodigo()))
                .start().getVO();
        form.setModelObject(usuario);

        return usuario;
    }

    public Usuario salvar() throws DAOException, ValidacaoException {
        Usuario usuario = form.getModel().getObject();
        if (senha == null) {
            throw new ValidacaoException(BundleManager.getString("informe_senha"));
        }
        if (!ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getSenha().equals(Util.criptografarSenha(senha))) {
            throw new ValidacaoException(BundleManager.getString("senha_incorreta"));
        }

        if (novaSenha != null
                && !novaSenha.equals(confirmacaoNovaSenha)) {
            throw new ValidacaoException(BundleManager.getString("confirmacao_senha_diferente_nova_senha"));
        }

        if (novaSenha != null) {
            this.form.getModelObject().setSenha(Util.criptografarSenha(novaSenha));
        }
        if (identificador != null) {
            this.form.getModelObject().setIdentificacao(Util.criptografarSenha(this.form.getModelObject().getCodigo() + identificador));
        }
        if (form.getModelObject().getEmail() != null) {
            if (!EmailValidator.validarEmail(form.getModelObject().getEmail())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_email_invalido"));
            }
        }

        if (usuario.getCpf() != null) {
            String cpfLocal = form.getModelObject().getCpf();
            cpfLocal = cpfLocal.replaceAll("\\.", "");
            cpfLocal = cpfLocal.replaceAll("-", "");
            form.getModelObject().setCpf(cpfLocal);
            if (!CpfCnpJValidator.CPFIsValid(form.getModelObject().getCpf())) {
                throw new ValidacaoException(BundleManager.getString("cpfInvalido"));
            }

            Usuario usuarioBase = LoadManager.getInstance(Usuario.class)
                    .addProperties(new HQLProperties(Usuario.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_CPF), form.getModelObject().getCpf()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Usuario.PROP_CODIGO), QueryCustom.QueryCustomParameter.DIFERENTE, form.getModelObject().getCodigo()))
                    .setMaxResults(1).start().getVO();

            if (usuarioBase != null) {
                throw new ValidacaoException(BundleManager.getString("msgCpfCadastrado", usuarioBase.getNome()));
            }
        }
        Usuario usrSave = BOFactoryWicket.getBO(CadastroFacade.class).save(this.form.getModelObject());

        Page page = new ConfiguracaoUsuarioExternoPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, getMsgSalvo(usrSave));
        return usrSave;
    }

    public String getMsgSalvo(Object returnObject) {
        return WicketMethods.getMessageResgistroSalvo(getReferenceClass(), returnObject);
    }

    public Class getResponsePage() {
        return ConfiguracaoUsuarioExternoPage.class;
    }

    public Class<Usuario> getReferenceClass() {
        return Usuario.class;
    }

    public Usuario getFormObject() {
        return form.getModel().getObject();
    }

}
