package br.com.celk.view.agenda.agendamento.regulacao;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.ValidacoesAgendamentoBehavior;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IHtmlReportAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.report.HtmlReport;
import br.com.celk.resources.Icon;
import br.com.celk.resources.Resources;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.agenda.agendamento.*;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.AgruparAgendaPorEmpresa;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.DefinirTelaAgendamentoListaEspera;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.FiltrarAgendaGradePorPrioridade;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.ParamsRegraTelaSolicitacaoAgendamento;
import br.com.celk.view.agenda.agendamento.dialog.DlgContatoAgendamentoListaEspera;
import br.com.celk.view.agenda.agendamento.dialog.DlgSelecionarEmpresaAgenda;
import br.com.celk.view.agenda.agendamento.validacao.ValidacaoAgendamentoListaEspera;
import br.com.celk.view.agenda.solicitacao.DetalhesSolicitacaoPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.prontuario.tablecolumn.ClassificacaoRiscoColumnT;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog.DlgClassificarEnviarFilaRegulacao;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog.DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.agendamento.exame.dto.*;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirEncaminhamentoConsultaDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.JustificativaPriorizacaoUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.JustificativaPriorizacao;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.LiberacaoAgendamento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.group.Group;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;
import org.hamcrest.Matchers;

import java.io.File;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;

/**
 * <AUTHOR>
 */
@Private
public class AgendamentoListaEsperaRegulacaoPage extends BasePage {

    private Form<AgendamentoListaEsperaDTOParam> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private DropDown<Long> dropDownTipoConsulta;
    private DropDown<Long> dropDownTipoEstabelecimento;
    private PageableTable<AgendamentoListaEsperaDTO> tblAgendamentoListaEspera;
    private AgendamentoListaEsperaDTOParam param = new AgendamentoListaEsperaDTOParam();
    private DlgMotivoCancelamentoSolicitacaoAgendamento dlgMotivoCancelamentoSolicitacaoAgendamento;
    private DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento dlgDevolucao;
    private DlgSelecionarEmpresaAgenda dlgSelecionarEmpresaAgenda;
    private String tipoControleRegulacao;

    private IPagerProvider pagerProvider;
    private final PageParameters parameters;
    private boolean procurar;

    private Boolean isPermissaoVisualizarTipoProcAgend;
    private Boolean isPermissaoTipoProcedimento;
    private Boolean isPermissaoEmpresa;
    private boolean permissaoSolicitarUrgencia;

    private DlgClassificarEnviarFilaRegulacao dlgClassificarEnviarFila;
    private DlgSelecionarTipoAgendamento dlgSelecionarTipoAgendamento;

    private WebMarkupContainer containerVisualizacaoAtendimentos;
    private List<PacientesPorClassificacaoRiscoDTO> pacientesPorClassificacaoRiscoDTOList;
    private DisabledInputField txtNumeroPacientes;
    private String numeroPacientes;
    private DisabledInputField txtNumeroRed;
    private String numeroRed;
    private DisabledInputField txtNumeroYellow;
    private String numeroYellow;
    private DisabledInputField txtNumeroGreen;
    private String numeroGreen;
    private DisabledInputField txtNumeroBlue;
    private String numeroBlue;
    private String exibirPainelClassificacaoRiscoConsultaAtendimentos;
    private DlgContatoAgendamentoListaEspera dlgContato;
    private DropDown dropDownClassificacaoRisco;
    private DropDown dropDownSubClassificacaoRisco;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public AgendamentoListaEsperaRegulacaoPage(PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;
        init();
    }

    public AgendamentoListaEsperaRegulacaoPage(PageParameters parameters, boolean procurar) {
        super(parameters);
        this.parameters = parameters;
        this.procurar = procurar;
        init();
    }

    public AgendamentoListaEsperaRegulacaoPage(AgendamentoListaEsperaDTOParam param, PageParameters parameters) {
        super();
        this.param = param;
        this.parameters = parameters;
        this.procurar = true;
        init();
        try {
            atualizarTotalizadores(null);
        } catch (ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    private void init() {
        AgendamentoListaEsperaDTOParam proxy = on(AgendamentoListaEsperaDTOParam.class);

        permissaoSolicitarUrgencia = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.SOLICITAR_URGENCIA_BREVIDADE);
        getForm().add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true));
        autoCompleteConsultaTipoProcedimento.setTfd(false);
        {
            autoCompleteConsultaTipoProcedimento.add(new ConsultaListener<TipoProcedimento>() {
                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, TipoProcedimento tipoProcedimento) {
                    removeParameter();
                    parameters.add("codigoTipoProcedimento", tipoProcedimento.getCodigo());
                }
            });
            autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {
                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                    removeParameter();
                }
            });
        }
        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        getForm().add(new AutoCompleteConsultaExameProcedimento("exameProcedimento"));

        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas"));

        isPermissaoVisualizarTipoProcAgend = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.VISUALIZAR_TIPOS_PROCEDIMENTO_AGENDAMENTO);
        if (!isPermissaoVisualizarTipoProcAgend) {
            isPermissaoTipoProcedimento = true;
            isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        } else {
            isPermissaoTipoProcedimento = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.HABILITACAO_TIPO_PROCEDIMENTO);
            if (!isPermissaoTipoProcedimento) {
                isPermissaoEmpresa = true;
            } else {
                isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
            }
        }
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(false);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

        getForm().add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissionalDesejado"));

        param.setPermissaoTipoProcedimento(isPermissaoTipoProcedimento);
        param.setPermissaoVisualizarTipoProcAgend(isPermissaoVisualizarTipoProcAgend);
        try {
            param.setLstEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(SessaoAplicacaoImp.getInstance().getUsuario())));
        } catch (ValidacaoException | DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        getForm().add(new AutoCompleteConsultaEmpresa("origemSolicitacao"));
        getForm().add(new InputField("cns"));
        getForm().add(new InputField("paciente"));
        getForm().add(new InputField("codigoPaciente"));
        getForm().add(new InputField("codigoSolicitacao"));
        getForm().add(getDropDownClassificacaoRisco("classificacaoRisco"));
        getForm().add(getDropDownSubClassificacaoRisco("subClassificacao").add(new TempBehaviorV2()));
        getForm().add(new InputField("numeracaoAuxiliar"));
        getForm().add(new DateChooser("dataNascimento"));
        getForm().add(getDropDownTipoConsulta("tipoConsulta"));
        getForm().add(getDropDownTipoEstabelecimento("tipoEstabelecimento"));
        getForm().add(new PnlDatePeriod(path(proxy.getPeriodo())));

        getForm().add(tblAgendamentoListaEspera = new PageableTable("tblAgendamentoListaEspera", getColumns(), getPagerProvider()));

        ProcurarButton procurarButton;
        getForm().add(procurarButton = new ProcurarButton<AgendamentoListaEsperaDTOParam>("btnProcurar", tblAgendamentoListaEspera) {
            @Override
            public AgendamentoListaEsperaDTOParam getParam() {
                param.setSituacaoList(Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA));
                param.setTipoFila(SolicitacaoAgendamento.TIPO_FILA_REGULACAO);
                param.setTipoFilaRegulacao(true);
                param.setTipoEstabelecimento(dropDownTipoEstabelecimento.getComponentValue());
                Usuario usuarioLogado = ApplicationSession.get().getSession().getUsuario();
                param.setUsuarioLogado(usuarioLogado);
                return param;
            }

            @Override
            public void depoisProcurar(AjaxRequestTarget target) throws ValidacaoException {
                atualizarTotalizadores(target);
                super.depoisProcurar(target);
            }
        });

        if (procurar) {
            procurarButton.procurar();
        }

        Image imageRed = new Image("imgRed", Resources.Images.BALL_RED16.resourceReference());
        Image imageYellow = new Image("imgYellow", Resources.Images.BALL_YELLOW16.resourceReference());
        Image imageGreen = new Image("imgGreen", Resources.Images.BALL_GREEN16.resourceReference());
        Image imageBlue = new Image("imgBlue", Resources.Images.BALL_BLUE16.resourceReference());

        containerVisualizacaoAtendimentos = new WebMarkupContainer("containerVisualizacaoAtendimentos");
        containerVisualizacaoAtendimentos.setOutputMarkupPlaceholderTag(true);


        containerVisualizacaoAtendimentos.add(imageRed);
        containerVisualizacaoAtendimentos.add(txtNumeroRed = new DisabledInputField("numeroRed", new PropertyModel(this, "numeroRed")));
        containerVisualizacaoAtendimentos.add(imageYellow);
        containerVisualizacaoAtendimentos.add(txtNumeroYellow = new DisabledInputField("numeroYellow", new PropertyModel(this, "numeroYellow")));
        containerVisualizacaoAtendimentos.add(imageGreen);
        containerVisualizacaoAtendimentos.add(txtNumeroGreen = new DisabledInputField("numeroGreen", new PropertyModel(this, "numeroGreen")));
        containerVisualizacaoAtendimentos.add(imageBlue);
        containerVisualizacaoAtendimentos.add(txtNumeroBlue = new DisabledInputField("numeroBlue", new PropertyModel(this, "numeroBlue")));

        form.add(containerVisualizacaoAtendimentos);

        try {
            exibirPainelClassificacaoRiscoConsultaAtendimentos = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibirPainelClassificacaoRiscoConsultaAtendimentos");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        containerVisualizacaoAtendimentos.setVisible(RepositoryComponentDefault.SIM.equals(exibirPainelClassificacaoRiscoConsultaAtendimentos));

        add(getForm());

        addModal(dlgMotivoCancelamentoSolicitacaoAgendamento = new DlgMotivoCancelamentoSolicitacaoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(solicitacaoAgendamento.getCodigo(), motivo, true);
                tblAgendamentoListaEspera.update(target);
            }
        });

        addModal(dlgDevolucao = new DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento(newModalId(), BundleManager.getString("confirmacaoDevolucao")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, String observacaoAutorizador) throws DAOException, ValidacaoException {
                BOFactoryWicket.getBO(AgendamentoFacade.class).devolverSolicitacaoAgendamento(codigoSolicitacao, observacaoAutorizador, null);
                tblAgendamentoListaEspera.update(target);
            }
        });
    }

    private void atualizarTotalizadores(AjaxRequestTarget target) throws ValidacaoException {
        if (RepositoryComponentDefault.SIM.equals(exibirPainelClassificacaoRiscoConsultaAtendimentos)) {
            pacientesPorClassificacaoRiscoDTOList = new ArrayList<PacientesPorClassificacaoRiscoDTO>();
            try {
                pacientesPorClassificacaoRiscoDTOList.addAll(BOFactoryWicket.getBO(AgendamentoFacade.class).consultarPacientesPorClassificacaoRisco(param));
            } catch (DAOException e) {
                pacientesPorClassificacaoRiscoDTOList = null;
                return;
            }
            if (target != null) {
//                txtNumeroPacientes.limpar(target);
                txtNumeroRed.limpar(target);
                txtNumeroYellow.limpar(target);
                txtNumeroGreen.limpar(target);
                txtNumeroBlue.limpar(target);
            }
//            numeroPacientes = Coalesce.asInteger(pacientesPorClassificacaoRiscoDTOList.size()).toString();
            List<PacientesPorClassificacaoRiscoDTO> selectRed = select(pacientesPorClassificacaoRiscoDTOList, having(on(PacientesPorClassificacaoRiscoDTO.class).getClassificacaoRisco().getNivelGravidade(), Matchers.equalTo(ClassificacaoRisco.NivelGravidade.VERMELHO.value())));
            if(selectRed != null && !selectRed.isEmpty()){
                numeroRed = (selectRed.get(0).getPacientes()).toString();
            } else {
                numeroRed = "0";
            }
            List<PacientesPorClassificacaoRiscoDTO> selectYellow = select(pacientesPorClassificacaoRiscoDTOList, having(on(PacientesPorClassificacaoRiscoDTO.class).getClassificacaoRisco().getNivelGravidade(), Matchers.equalTo(ClassificacaoRisco.NivelGravidade.AMARELO.value())));
            if(selectYellow != null && !selectYellow.isEmpty()) {
                numeroYellow = (selectYellow.get(0).getPacientes()).toString();
            } else {
                numeroYellow = "0";
            }
            List<PacientesPorClassificacaoRiscoDTO> selectGreen = select(pacientesPorClassificacaoRiscoDTOList, having(on(PacientesPorClassificacaoRiscoDTO.class).getClassificacaoRisco().getNivelGravidade(), Matchers.equalTo(ClassificacaoRisco.NivelGravidade.VERDE.value())));
            if(selectGreen != null && !selectGreen.isEmpty()) {
                numeroGreen = (selectGreen.get(0).getPacientes()).toString();
            } else {
                numeroGreen = "0";
            }
            List<PacientesPorClassificacaoRiscoDTO> selectBlue = select(pacientesPorClassificacaoRiscoDTOList, having(on(PacientesPorClassificacaoRiscoDTO.class).getClassificacaoRisco().getNivelGravidade(), Matchers.equalTo(ClassificacaoRisco.NivelGravidade.AZUL.value())));
            if(selectBlue != null && !selectBlue.isEmpty()){
                numeroBlue = (selectBlue.get(0).getPacientes()).toString();
            } else {
                numeroBlue = "0";
            }

        }
    }

    private void removeParameter() {
        if (!parameters.get("codigoTipoProcedimento").isEmpty()) {
            parameters.remove("codigoTipoProcedimento");
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoProcedimento.getTxtDescricao().getTextField();
    }

    private Form<AgendamentoListaEsperaDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<AgendamentoListaEsperaDTOParam>(param));

            /*
             * [BEGIN] #7577 - Alterar para quando voltar para a página da Consulta, depois de concluir a ação Agendamento da Solicitação,
             *                 o campo Tipo do Procedimento deve permanecer com o mesmo valor de quando foi solicitado a ação.
             */
            if (!parameters.get("codigoTipoProcedimento").isEmpty()) {
                Long codigoTipoProcedimento = parameters.get("codigoTipoProcedimento").toLongObject();
                TipoProcedimento tipoProcedimento = LoadManager.getInstance(TipoProcedimento.class).setId(codigoTipoProcedimento).start().getVO();
                param.setTipoProcedimento(tipoProcedimento);
            }
            // [END]
        }
        return this.form;
    }

    private DropDown getDropDownClassificacaoRisco(String id) {
        if (dropDownClassificacaoRisco == null) {
            dropDownClassificacaoRisco = new DropDown(id);
            dropDownClassificacaoRisco.addChoice(null, bundle("todos"));

            List<ClassificacaoRisco> classificacaoRiscoList = LoadManager.getInstance(ClassificacaoRisco.class)
                    .addProperties(new HQLProperties(ClassificacaoRisco.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoRisco.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, BuilderQueryCustom.QueryParameter.DIFERENTE, ClassificacaoRisco.NivelGravidade.LARANJA.value()))
                    .start().getList();

            if(CollectionUtils.isNotNullEmpty(classificacaoRiscoList)) {
                for(ClassificacaoRisco classificacaoRisco : classificacaoRiscoList) {
                    dropDownClassificacaoRisco.addChoice(classificacaoRisco, classificacaoRisco.getDescricao());
                }
            }

            dropDownClassificacaoRisco.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubClassificacaoRisco.removeAllChoices();
                    if(target != null) {
                        carregaDropDownSubClassificacao(target, null);
                    }
                }
            });
        }
        return dropDownClassificacaoRisco;
    }

    private DropDown getDropDownSubClassificacaoRisco(String id) {
        if (dropDownSubClassificacaoRisco == null) {
            dropDownSubClassificacaoRisco = new DropDown(id);
            dropDownSubClassificacaoRisco.addChoice(null, bundle("todos"));
        }
        return dropDownSubClassificacaoRisco;
    }

    private void carregaDropDownSubClassificacao(AjaxRequestTarget target, Long codigoSubClassificacao) {
        SubClassificacaoFilaEspera subClassificacaoAtual = null;
        dropDownSubClassificacaoRisco.removeAllChoices(target);
        dropDownSubClassificacaoRisco.limpar(target);
        dropDownSubClassificacaoRisco.addChoice(null,bundle("todos"));

        if (dropDownClassificacaoRisco.getComponentValue() != null) {
            List<SubClassificacaoFilaEspera> subClassificacaoFilaEsperaList = LoadManager.getInstance(SubClassificacaoFilaEspera.class)
                    .addProperties(new HQLProperties(SubClassificacaoFilaEspera.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(SubClassificacaoFilaEspera.PROP_CLASSIFICACAO_RISCO, dropDownClassificacaoRisco.getComponentValue()))
                    .addSorter(new QueryCustom.QueryCustomSorter(SubClassificacaoFilaEspera.PROP_VALOR, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(subClassificacaoFilaEsperaList)) {
                for (SubClassificacaoFilaEspera subClassificacaoFilaEspera : subClassificacaoFilaEsperaList) {
                    dropDownSubClassificacaoRisco.addChoice(subClassificacaoFilaEspera, subClassificacaoFilaEspera.getDescricao());
                    if (codigoSubClassificacao != null && subClassificacaoFilaEspera.getValor().equals(codigoSubClassificacao)) {
                        subClassificacaoAtual = subClassificacaoFilaEspera;
                    }
                }
            }
        }

        dropDownSubClassificacaoRisco.setModelObject(subClassificacaoAtual);
        target.add(dropDownSubClassificacaoRisco);
    }

    private DropDown getDropDownTipoConsulta(String id) {
        if (dropDownTipoConsulta == null) {
            dropDownTipoConsulta = new DropDown<Long>(id);

            dropDownTipoConsulta.addChoice(null, bundle("ambos"));
            dropDownTipoConsulta.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL, bundle("primeiraVezNumerico"));
            dropDownTipoConsulta.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO, bundle("retorno"));
        }

        return dropDownTipoConsulta;
    }

    private DropDown getDropDownTipoEstabelecimento(String id) {
        if(dropDownTipoEstabelecimento == null) {
            dropDownTipoEstabelecimento = new DropDown<>(id);
            dropDownTipoEstabelecimento.addChoice(null, bundle("todos"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.UNIDADE.value(), bundle("unidade"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.PRESTADOR_SERVICO.value(), bundle("prestadorServico"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.SECRETARIA_SAUDE.value(), bundle("secretariaSaude"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.CONSORCIO.value(), bundle("consorcio"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.CONSORCIADO.value(), bundle("consorciado"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.UNIDADE_FILANTROPICA.value(), bundle("unidadeFilantropica"));
        }

        return dropDownTipoEstabelecimento;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendamentoListaEsperaDTO proxy = on(AgendamentoListaEsperaDTO.class);

        columns.add(getActionColumn());
        columns.add(new ClassificacaoRiscoColumnT<AgendamentoListaEsperaDTO>("CR", path(proxy.getSolicitacaoAgendamento().getClassificacaoRisco().getNivelGravidade())) {
            @Override
            public ClassificacaoRisco getClassificacaoRiscoFromObject(AgendamentoListaEsperaDTO object) {
                return object.getSolicitacaoAgendamento().getClassificacaoRisco();
            }
        });
        columns.add(createSortableColumn(bundle("subclassificacao"), proxy.getSolicitacaoAgendamento().getDescricaoSubclassificacaoFilaEspera()));
        columns.add(createSortableColumn(bundle("cns"), proxy.getCns()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNome(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexo(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexoFormatado()));
        columns.add(createSortableColumn(bundle("idade"),
                proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento(),
                    proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDescricaoIdadeSimplesAbvComDias()));
        columns.add(createSortableColumn(bundle("unidadeResponsavel"), proxy.getSolicitacaoAgendamento().getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("exames"), proxy.getDescricaoExameProcedimento()));
        columns.add(createSortableColumn(bundle("tipoProcedimento"), proxy.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("dtSolicitacao"), proxy.getSolicitacaoAgendamento().getDataSolicitacao()));
        columns.add(createSortableColumn(bundle("dtDesejada"), proxy.getSolicitacaoAgendamento().getDataDesejada()));
        columns.add(createSortableColumn(bundle("numeracaoAuxiliar"), proxy.getSolicitacaoAgendamento().getNumeracaoAuxiliar()));
        columns.add(createSortableColumn(bundle("tipoConsulta"), proxy.getSolicitacaoAgendamento().getTipoConsulta(), proxy.getSolicitacaoAgendamento().getDescricaoTipoConsulta()));

        return columns;
    }

    private void agendarDentroRede(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> horariosDisponiveis, AgendamentoListaEsperaDTO agendamentoListaEsperaDto) throws ValidacaoException, DAOException {
        SolicitacaoAgendamento solicitacaoAgendamento = agendamentoListaEsperaDto.getSolicitacaoAgendamento();
        horariosDisponiveis = new FiltrarAgendaGradePorPrioridade().filtrar(horariosDisponiveis, solicitacaoAgendamento);

        horariosDisponiveis = BOFactoryWicket.getBO(AgendamentoFacade.class).validarAgendasProximidadeSolicitanteExecutante(agendamentoListaEsperaDto.getSolicitacaoAgendamento().getEmpresa(), horariosDisponiveis);

        ValidacaoAgendamentoListaEspera.validar(horariosDisponiveis, agendamentoListaEsperaDto);
        Group<AgendaGradeAtendimentoDTO> agendasAgrupadasPorEmpresa = AgruparAgendaPorEmpresa.agrupar(horariosDisponiveis);

        if (agendasAgrupadasPorEmpresa.subgroups().size() > 1) {
            viewDlgSelecionarEmpresaAgenda(target, agendamentoListaEsperaDto, horariosDisponiveis);
        } else {
            Empresa unidadeAgenda = agendasAgrupadasPorEmpresa.subgroups().get(0).findAll().get(0).getEmpresaAgenda();
            Page solicitacaoAgendamentoPage = this.getPageAgendamentoListaEpera(solicitacaoAgendamento, unidadeAgenda, horariosDisponiveis);
            setResponsePage(solicitacaoAgendamentoPage);
        }
    }

    private void showDlgSelecionarTipoAgendamento(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> list, AgendamentoListaEsperaDTO modelObject) {
        if (dlgSelecionarTipoAgendamento == null) {
            addModal(target, dlgSelecionarTipoAgendamento = new DlgSelecionarTipoAgendamento(newModalId()) {
                @Override
                public void onDentroRede(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> list, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                    agendarDentroRede(target, list, modelObject);
                }

                @Override
                public void onForaRede(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                    setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), AgendamentoListaEsperaRegulacaoPage.class, parameters) {
                        @Override
                        public void voltar() {
                            setResponsePage(new AgendamentoListaEsperaRegulacaoPage(param, parameters));
                        }
                    });
                }
            });
        }
        dlgSelecionarTipoAgendamento.show(target, list, modelObject);
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendamentoListaEsperaDTO>() {
            @Override
            public void customizeColumn(AgendamentoListaEsperaDTO rowObject) {
                addAction(ActionType.AGENDAR, rowObject, new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                ValidacoesAgendamentoDTO validacoesAgendamentoDTO = new ValidacoesAgendamentoDTO();
                                validacoesAgendamentoDTO.setTipoProcedimento(modelObject.getSolicitacaoAgendamento().getTipoProcedimento());
                                validacoesAgendamentoDTO.setUsuarioCadsus(modelObject.getSolicitacaoAgendamento().getUsuarioCadsus());
                                validacoesAgendamentoDTO.setCidadeEmpresaLogada(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());

                                UsuarioCadsusHelper.validarTituloOuCompactuacao(rowObject.getSolicitacaoAgendamento().getUsuarioCadsus(), rowObject.getSolicitacaoAgendamento().getEmpresa(), rowObject.getSolicitacaoAgendamento().getTipoProcedimento());

                                ValidacoesAgendamentoBehavior behavior = new ValidacoesAgendamentoBehavior(validacoesAgendamentoDTO);
                                String msg = behavior.validacoes();

                                if (msg != null && !"".equals(msg)) {
                                    LiberacaoAgendamento la = behavior.getLiberacaoAgendamento();

                                    if (la == null) {
                                        throw new ValidacaoException(msg);
                                    }
                                }

                                if (TipoProcedimento.TIPO_PROCEDIMENTO_FORA_DA_REDE.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
                                    setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), AgendamentoListaEsperaRegulacaoPage.class, parameters) {
                                        @Override
                                        public void voltar() {
                                            setResponsePage(new AgendamentoListaEsperaRegulacaoPage(param, parameters));
                                        }
                                    });
                                } else {
                                    List<AgendaGradeAtendimentoDTO> list = BOFactoryWicket.getBO(AgendamentoFacade.class).verificarExisteVagasDisponiveisAgendaExameList(modelObject.getSolicitacaoAgendamento(), true);
                                    if (CollectionUtils.isNotNullEmpty(list)) {
                                        if (TipoProcedimento.TIPO_PROCEDIMENTO_SELECIONAR_AO_AGENDAR.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
                                            showDlgSelecionarTipoAgendamento(target, list, modelObject);
                                        } else {
                                            agendarDentroRede(target, list, modelObject);
                                        }
                                    } else {
                                        if (TipoProcedimento.TIPO_PROCEDIMENTO_DENTRO_DA_REDE.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())){
                                            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existem_vagas_disponiveis_dentro_da_rede"));
                                        }
                                        setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), AgendamentoListaEsperaRegulacaoPage.class, parameters) {
                                            @Override
                                            public void voltar() {
                                                setResponsePage(new AgendamentoListaEsperaRegulacaoPage(param, parameters));
                                            }
                                        });
                                    }
                                }
                            }
                        }
                ).setTitleBundleKey(
                        "agendamentoSolicitacao").setIcon(Icon.CALENDAR)
                        .setEnabled(SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                                || SolicitacaoAgendamento.STATUS_AGENDADO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                                || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(rowObject.getSolicitacaoAgendamento().getStatus()));

                addAction(ActionType.CONTATO, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                viewDlgContato(target, modelObject.getSolicitacaoAgendamento());
                            }
                        }
                ).setTitleBundleKey("registrarContato")
                        .setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao()) ||
                        RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()));

                addAction(ActionType.EDITAR, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new CadastroSolicitacaoAgendamentoPanel(modelObject.getSolicitacaoAgendamento().getCodigo(), true, false, parameters, AgendamentoListaEsperaRegulacaoPage.class, permissaoSolicitarUrgencia, true));
                            }
                        }
                ).setEnabled((SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        || (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(rowObject.getSolicitacaoAgendamento().getPrioridade())
                        || (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        && RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagDevolvido())
                        && rowObject.getSolicitacaoAgendamento().getDataAnaliseAutorizador() != null)
                        && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getSolicitarPrioridade())))
                        && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado()) && !existeAgendamentoAberto(rowObject));

                addAction(ActionType.CANCELAR, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                dlgMotivoCancelamentoSolicitacaoAgendamento.show(target, modelObject.getSolicitacaoAgendamento());
                            }
                        }
                ).setQuestionDialogBundleKey(
                        null).setEnabled(SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus()));

                addAction(ActionType.CONSULTAR, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                setResponsePage(new DetalhesSolicitacaoPage(modelObject.getSolicitacaoAgendamento().getCodigo()));
                            }
                        }
                );
                addAction(ActionType.ALTERAR_CLASSIFICACAO, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                showDlgClassificarEnviarFilaRegulacao(target, modelObject.getSolicitacaoAgendamento());
                            }
                        }
                );
                addAction(ActionType.RETORNO, rowObject,
                        new IModelAction<AgendamentoListaEsperaDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                                dlgDevolucao.show(target, modelObject.getSolicitacaoAgendamento());
                            }
                        }
                ).setTitleBundleKey("devolverSolicitacao").setIcon(Icon.RELOAD)
                        .setVisible(isActionPermitted(Permissions.RETORNO));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AgendamentoListaEsperaDTO>() {
                    @Override
                    public DataReport action(AgendamentoListaEsperaDTO modelObject) throws ReportException {
                        Encaminhamento encaminhamento = buscarEncaminhamento(rowObject);
                        if (encaminhamento != null) {
                            return gerarRelatorioEncaminhamento(encaminhamento);
                        }

                        carregarExames(rowObject);
                        if (rowObject.getExameApac() != null) {
                            return gerarRelatorioApac(modelObject);
                        }
                        if (rowObject.getExameBpai() != null) {
                            return gerarRelatorioBpai(modelObject);
                        }

                        return null;
                    }
                }).setTitleBundleKey("imprimirRequisicao").setEnabled(rowObject.getSolicitacaoAgendamento() != null && rowObject.getSolicitacaoAgendamento().getAtendimentoOrigem() != null);

            }
        };
    }

    private void carregarExames(AgendamentoListaEsperaDTO rowObject) {
        ExameRequisicao exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(ExameRequisicao.PROP_CODIGO)
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_CONVENIO_REALIZADO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_CLASSIFICACAO))
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, rowObject.getSolicitacaoAgendamento()))
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()))
                .setMaxResults(1)
                .start()
                .getVO();

        if (exameRequisicao == null) return;
        rowObject.setExameRequisicao(exameRequisicao);

        ExameApac exameApac = LoadManager.getInstance(ExameApac.class)
                .addProperties(ExameApac.PROP_CODIGO)
                .addParameter(new QueryCustomParameter(ExameApac.PROP_EXAME, exameRequisicao.getExame()))
                .setMaxResults(1)
                .start()
                .getVO();
        if (exameApac != null) {
            rowObject.setExameApac(exameApac);
            return;
        }

        ExameBpai exameBpai = LoadManager.getInstance(ExameBpai.class)
                .addProperties(ExameBpai.PROP_CODIGO)
                .addParameter(new QueryCustomParameter(ExameBpai.PROP_EXAME, exameRequisicao.getExame()))
                .setMaxResults(1)
                .start()
                .getVO();
        rowObject.setExameBpai(exameBpai);
    }

    private Encaminhamento buscarEncaminhamento(AgendamentoListaEsperaDTO rowObject) {
        return LoadManager.getInstance(Encaminhamento.class)
                .addProperties(Encaminhamento.PROP_CODIGO)
                .addParameter(new QueryCustomParameter(Encaminhamento.PROP_ATENDIMENTO, rowObject.getSolicitacaoAgendamento().getAtendimentoOrigem()))
                .start()
                .getVO();
    }

    private DataReport gerarRelatorioApac(AgendamentoListaEsperaDTO modelObject) throws ReportException {
        Long codigo = modelObject.getExameRequisicao().getExame().getCodigo();
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class)
                .relatorioImpressaoExameApac(Collections.singletonList(codigo));
    }

    private DataReport gerarRelatorioBpai(AgendamentoListaEsperaDTO modelObject) throws ReportException {
        Long codigo = modelObject.getExameRequisicao().getExame().getCodigo();
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class)
                .relatorioImpressaoExameBpai(codigo);
    }

    private DataReport gerarRelatorioEncaminhamento(Encaminhamento encaminhamento) throws ReportException {
        RelatorioImprimirEncaminhamentoConsultaDTOParam param = new RelatorioImprimirEncaminhamentoConsultaDTOParam();
        param.setCodigoEncaminhamentos(Collections.singletonList(encaminhamento.getCodigo()));
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class)
                .relatorioImprimirEncaminhamentoConsulta(param);
    }

    private void showDlgClassificarEnviarFilaRegulacao(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgClassificarEnviarFila == null) {
            addModal(target, dlgClassificarEnviarFila = new DlgClassificarEnviarFilaRegulacao(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, JustificativaPriorizacao justificativaPriorizacao, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException {
                    JustificativaPriorizacaoUtils.validaCampoJustificativa(justificativaPriorizacao);
                    BOFactoryWicket.getBO(AgendamentoFacade.class).classificarEnviarFilaRegulacaoSolicitacaoAgendamento(codigoSolicitacao, classificacaoRisco, observacaoAutorizador, justificativaPriorizacao, subClassificacaoFilaEspera, true);
                    tblAgendamentoListaEspera.update(target);
                }

                @Override
                public void onConfirmarAgendar(AjaxRequestTarget target, Long codigoSolicitacao, ClassificacaoRisco classificacaoRisco, String observacaoAutorizador, SubClassificacaoFilaEspera subClassificacaoFilaEspera) throws DAOException, ValidacaoException {

                }
            });
        }
        dlgClassificarEnviarFila.show(target, solicitacaoAgendamento, false);
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private void viewDlgContato(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgContato == null) {
            addModal(target, dlgContato = new DlgContatoAgendamentoListaEspera(newModalId()) {
                @Override
                public void depoisConfirmarContato(AjaxRequestTarget target) {
                    tblAgendamentoListaEspera.update(target);
                }

                @Override
                public void depoisSalvarOcorrencia(AjaxRequestTarget target) {
                    tblAgendamentoListaEspera.update(target);
                }
            });
        }
        dlgContato.show(target, solicitacaoAgendamento);
    }

    private boolean existeAgendamentoAberto(AgendamentoListaEsperaDTO objeto) {
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addParameter(new QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN,
                        Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO)))
                .addParameter(new QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, objeto.getSolicitacaoAgendamento()))
                .exists();
    }

    private void viewDlgSelecionarEmpresaAgenda(AjaxRequestTarget target, AgendamentoListaEsperaDTO dto, List<AgendaGradeAtendimentoDTO> horarios) {
        if (dlgSelecionarEmpresaAgenda == null) {
            addModal(target, dlgSelecionarEmpresaAgenda = new DlgSelecionarEmpresaAgenda(newModalId(), true) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException, DAOException {
                    setResponsePage(getPageAgendamentoListaEpera(solicitacaoAgendamento, unidadeAgenda, horarios));
                }
            });
        }

        dlgSelecionarEmpresaAgenda.show(target, dto.getSolicitacaoAgendamento(), horarios);
    }

    public IPagerProvider getPagerProvider() {
        if (this.pagerProvider == null) {
            this.pagerProvider = new QueryPagerProvider<AgendamentoListaEsperaDTO, AgendamentoListaEsperaDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentoListaEspera(dataPaging);
                }

                @Override
                public void customizeParam(AgendamentoListaEsperaDTOParam param) {
                    SingleSortState<String> sortState = (SingleSortState) getPagerProvider().getSortState();

                    if (sortState.getSort() != null) {
                        param.setCampoOrdenacao(sortState.getSort().getProperty());
                        param.setTipoOrdenacao(sortState.getSort().isAscending() ? "asc" : "desc");
                    }
                }
            };
        }

        return this.pagerProvider;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("agendamentoListaEsperaRegulacao");
    }

    private List<AgendaGradeAtendimentoDTO> filtraPorUnidade(List<AgendaGradeAtendimentoDTO> horariosDisponiveis, Empresa unidade) {
        if (unidade == null) {
            return horariosDisponiveis;
        }
        List<AgendaGradeAtendimentoDTO> horariosFiltrados = new ArrayList<>();
        for (AgendaGradeAtendimentoDTO horario: horariosDisponiveis) {
            Empresa unidadeHorario = horario.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa();
            if (unidade.getCodigo().equals(unidadeHorario.getCodigo())) {
                horariosFiltrados.add(horario);
            }
        }
        return horariosFiltrados;
    }

    private Page getPageAgendamentoListaEpera(SolicitacaoAgendamento solicitacaoAgendamento, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException {
        Long tipoAgenda = AgendamentoHelper.getTipoAgenda(solicitacaoAgendamento.getTipoProcedimento(), unidadeAgenda);
        Page paginaRetorno = new AgendamentoListaEsperaRegulacaoPage(param, parameters);
        List<AgendaGradeAtendimentoDTO> horariosFiltrados = filtraPorUnidade(horarios, unidadeAgenda);
        ParamsRegraTelaSolicitacaoAgendamento parametros = new ParamsRegraTelaSolicitacaoAgendamento()
                .setAgendamentoListaEsperaDTOParam(param)
                .setHorarios(horariosFiltrados)
                .setSolicitacaoAgendamento(solicitacaoAgendamento)
                .setUnidadeSaude(unidadeAgenda)
                .setPageParameters(parameters)
                .setTipoAgenda(tipoAgenda)
                .setPaginaRetorno(paginaRetorno);

        // todo: melhoria de desempenho apenas na classe AgendamentoSolicitacaoHorarioPanel
        Page page = new DefinirTelaAgendamentoListaEspera().regulada(parametros);
        if (page instanceof AgendamentoSolicitacaoHorarioPanel) {
            AgendamentoSolicitacaoHorarioPanel panel = (AgendamentoSolicitacaoHorarioPanel)page;
            panel.setHorarios(horariosFiltrados);
        } else if (page instanceof AgendamentoSolicitacaoPanel) {
            AgendamentoSolicitacaoPanel panel = (AgendamentoSolicitacaoPanel)page;
            panel.setHorarios(horariosFiltrados);
        }

        return page;
    }
}
