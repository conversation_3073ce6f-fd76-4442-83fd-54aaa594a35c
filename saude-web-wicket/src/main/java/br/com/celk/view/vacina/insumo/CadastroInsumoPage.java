package br.com.celk.view.vacina.insumo;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.view.basico.unidade.pnl.PnlConsultaUnidade;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMaterialDTO;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroInsumoPage extends CadastroPage<Produto> {

    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private InputField txtDescricao;
    
    public CadastroInsumoPage(Produto object, boolean viewOnly) {
        super(object, viewOnly);
        initiateSubGrupoEditView();
    }

    public CadastroInsumoPage(Produto object) {
        super(object);
        initiateSubGrupoEditView();
    }

    public CadastroInsumoPage() {
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredInputField(Produto.PROP_DESCRICAO));
        form.add(new PnlConsultaUnidade(Produto.PROP_UNIDADE, true));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(DropDownUtil.getSimNaoDropDown(Produto.PROP_FLAG_CONTROLE_MINIMO));
        form.add(DropDownUtil.getCurvaProdutoDropDown(Produto.PROP_CURVA));
        form.add(DropDownUtil.getNaoSimDropDown(Produto.PROP_FLAG_BAIXA_ESTOQUE_PROCESSO_ENFERMAGEM));
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new RequiredDropDown<SubGrupo>("subGrupo");
            
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
            
        }
        
        return this.dropDownSubGrupo;
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new RequiredDropDown<GrupoProduto>(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO));
            
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                dropDownSubGrupo.removeAllChoices();
                                dropDownSubGrupo.addChoice(null, "");
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                            getForm().getModelObject().setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();
                
                dropDownGrupoProduto.addChoice(null, "");

                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        
        return this.dropDownGrupoProduto;
    }
    
    private void initiateSubGrupoEditView(){
        dropDownSubGrupo.removeAllChoices();
        dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));

        GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

        if (grupoProduto!=null) {
                List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                        .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                        .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                    for (SubGrupo subGrupo : subGrupos) {
                        dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                    }
                }
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<Produto> getReferenceClass() {
        return Produto.class;
    }

    @Override
    public Object salvar(Produto object) throws DAOException, ValidacaoException {
        CadastroMaterialDTO dto = new CadastroMaterialDTO();
        dto.setProduto(object);

        return BOFactoryWicket.getBO(ProdutoFacade.class).saveMateriais(dto);
    }

    @Override
    public Class getResponsePage() {
        return ConsultaInsumoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroInsumo");
    }

}
