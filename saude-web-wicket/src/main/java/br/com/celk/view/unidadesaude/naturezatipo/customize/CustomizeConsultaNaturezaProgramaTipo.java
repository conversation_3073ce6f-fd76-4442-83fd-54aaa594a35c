package br.com.celk.view.unidadesaude.naturezatipo.customize;

import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaNaturezaProgramaTipo extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return NaturezaProcuraTipoAtendimento.class;
    }
}
