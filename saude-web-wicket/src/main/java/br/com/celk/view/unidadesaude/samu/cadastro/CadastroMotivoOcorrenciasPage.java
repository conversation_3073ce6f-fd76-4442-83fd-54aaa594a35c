package br.com.celk.view.unidadesaude.samu.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.unidadesaude.samu.cadastro.autocomplete.AutoCompleteConsultaMotivoOcorrenciaTipoSamu;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.samu.MotivoOcorrenciaSamu;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroMotivoOcorrenciasPage extends CadastroPage<MotivoOcorrenciaSamu>{
    
    private InputField txtDescricao;
    
    public CadastroMotivoOcorrenciasPage(MotivoOcorrenciaSamu object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroMotivoOcorrenciasPage(MotivoOcorrenciaSamu object) {
        this(object, false);
    }

    public CadastroMotivoOcorrenciasPage() {
        this(null); 
    }

    @Override
    public void init(Form form) {
        MotivoOcorrenciaSamu proxy = on(MotivoOcorrenciaSamu.class);
        
        form.add(txtDescricao = (InputField) new RequiredInputField<String>(path(proxy.getDescricaoMotivo()))
                .setLabel(new Model<String>(bundle("descricao"))));
        
        form.add(new AutoCompleteConsultaMotivoOcorrenciaTipoSamu(path(proxy.getMotivoOcorrenciaTipoSamu()), true)
                .setLabel(new Model<String>(bundle("tipoOcorrencia"))));
    }
    
    @Override
    public Class<MotivoOcorrenciaSamu> getReferenceClass() {
        return MotivoOcorrenciaSamu.class;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }
    
    @Override
    public Class getResponsePage() {
        return ConsultaMotivoOcorrenciasPage.class;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroMotivoOcorrencia");
    }  
}