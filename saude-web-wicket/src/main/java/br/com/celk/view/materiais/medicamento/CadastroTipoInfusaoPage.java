package br.com.celk.view.materiais.medicamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TipoInfusao;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 * <AUTHOR>
 */
@Private
public class CadastroTipoInfusaoPage extends CadastroPage<TipoInfusao> {
    private String descricao;

    public CadastroTipoInfusaoPage(){

    }

    public CadastroTipoInfusaoPage(TipoInfusao object) {
        super(object);
    }
    public CadastroTipoInfusaoPage(TipoInfusao object, boolean viewOnly){
        super(object, viewOnly);
    }
    public CadastroTipoInfusaoPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    public Object salvar(TipoInfusao object) throws DAOException, ValidacaoException {
        return super.salvar(object);
    }

    @Override
    public void init(Form<TipoInfusao> form) {
        form.add(new RequiredInputField("descricao"));
    }

    @Override
    public Class<TipoInfusao> getReferenceClass() {
        return TipoInfusao.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoInfusaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastro_tipo_infusao");
    }


}
