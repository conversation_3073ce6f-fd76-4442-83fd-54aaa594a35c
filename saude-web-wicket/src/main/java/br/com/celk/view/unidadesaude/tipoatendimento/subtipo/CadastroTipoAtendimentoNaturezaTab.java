package br.com.celk.view.unidadesaude.tipoatendimento.subtipo;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Icon;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.unidadesaude.tipoatendimento.dto.NaturezaProcuraTipoAtendimentoDTO;
import br.com.celk.unidadesaude.tipoatendimento.dto.TipoAtendimentoDTO;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.unidadesaude.naturezaprocura.autocomplete.AutoCompleteConsultaNaturezaProcura;
import br.com.celk.view.unidadesaude.tipoatendimento.subtipo.dlg.DlgInformarUnidadesNaturezaTipo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoAtendimentoNaturezaTab extends TabPanel<TipoAtendimentoDTO> {

    private NaturezaProcura naturezaProcura;
//    private TipoProcedimento tipoProcedimento;

    private AutoCompleteConsultaNaturezaProcura autoCompleteConsultaNaturezaProcura;
//    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;

    private Table table;

    private DlgInformarUnidadesNaturezaTipo dlgInformarUnidadesNaturezaTipo;

    public CadastroTipoAtendimentoNaturezaTab(String id, TipoAtendimentoDTO object) {
        super(id, object);
        init();
    }

    private void init() {

        add(autoCompleteConsultaNaturezaProcura = new AutoCompleteConsultaNaturezaProcura("naturezaProcura", new PropertyModel(this, "naturezaProcura")));
//        add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento", new PropertyModel(this, "tipoProcedimento")));

        add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
    }

    public void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (naturezaProcura == null) {
            throw new ValidacaoException(bundle("msgObrigatorioNaturezaProcura"));
        }
//        if (tipoProcedimento == null) {
//            throw new ValidacaoException(bundle("msgObrigatorioTipoProcediemento"));
//        }

        for (NaturezaProcuraTipoAtendimentoDTO dto : object.getLstNaturezaProcuraTipoAtendimentoDTO()) {
            if (dto.getNaturezaProcura().equals(naturezaProcura)) {
                throw new ValidacaoException(bundle("msgNaturezaProcuraRepetido"));
            }
        }

        NaturezaProcuraTipoAtendimentoDTO item = new NaturezaProcuraTipoAtendimentoDTO();
        item.setNaturezaProcura(naturezaProcura);
//        item.setTipoProcedimento(tipoProcedimento);
        item.setNovo(true);

        object.getLstNaturezaProcuraTipoAtendimentoDTO().add(item);

        table.populate();
        table.update(target);

        autoCompleteConsultaNaturezaProcura.limpar(target);
//        autoCompleteConsultaTipoProcedimento.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getLstNaturezaProcuraTipoAtendimentoDTO();
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        NaturezaProcuraTipoAtendimentoDTO proxy = on(NaturezaProcuraTipoAtendimentoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("naturezaProcura"), proxy.getNaturezaProcura().getDescricao()));
//        columns.add(createColumn(bundle("tipoProcedimento"), proxy.getTipoProcedimento().getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<NaturezaProcuraTipoAtendimentoDTO>() {
            @Override
            public void customizeColumn(final NaturezaProcuraTipoAtendimentoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<NaturezaProcuraTipoAtendimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, NaturezaProcuraTipoAtendimentoDTO modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < object.getLstNaturezaProcuraTipoAtendimentoDTO().size(); i++) {
                            NaturezaProcuraTipoAtendimentoDTO item = object.getLstNaturezaProcuraTipoAtendimentoDTO().get(i);
                            if (item == rowObject) {
//                                Long count = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
//                                        .addGroup(new QueryCustom.QueryCustomGroup(NaturezaProcuraTipoAtendimento.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
//                                        .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, object.getTipoAtendimentoEdicao()))
//                                        .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, item.getNaturezaProcura()))
//                                        .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO, item.getTipoProcedimento()))
//                                        .start().getVO();
//                                if (count > 0) {
//                                }
                                object.getLstNaturezaProcuraTipoAtendimentoExclusaoDTO().add(item);
                                object.getLstNaturezaProcuraTipoAtendimentoDTO().remove(i);
                            }
                        }
                        table.populate();
                        table.update(target);
                    }
                });
                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<NaturezaProcuraTipoAtendimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, NaturezaProcuraTipoAtendimentoDTO modelObject) throws ValidacaoException, DAOException {
                        if (dlgInformarUnidadesNaturezaTipo == null) {
                            WindowUtil.addModal(target, CadastroTipoAtendimentoNaturezaTab.this, dlgInformarUnidadesNaturezaTipo = new DlgInformarUnidadesNaturezaTipo(WindowUtil.newModalId(CadastroTipoAtendimentoNaturezaTab.this)) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target, NaturezaProcuraTipoAtendimentoDTO dto) {

                                }
                            });
                        }
                        dlgInformarUnidadesNaturezaTipo.show(target, rowObject);
                    }

                }).setIcon(Icon.DOC_PLUS);
            }
        };
    }

    @Override
    public String getTitle() {
        return bundle("natureza_procura");
    }

}
