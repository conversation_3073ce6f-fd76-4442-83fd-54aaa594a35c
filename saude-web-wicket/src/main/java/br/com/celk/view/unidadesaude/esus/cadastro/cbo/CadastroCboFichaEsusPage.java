package br.com.celk.view.unidadesaude.esus.cadastro.cbo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.celk.view.vigilancia.registroagravo.enums.SimNaoEnum;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 */
@Private
public class CadastroCboFichaEsusPage extends CadastroPage<CboFichaEsus> {

    private Long ficha;
    private DropDown<Long> ddTipoFicha;
    private TabelaCbo tabelaCbo;
    private Table<CboFichaEsusItem> table;
    private List<CboFichaEsusItem> lstCboGrupoAtendimento = new ArrayList<CboFichaEsusItem>();
    private CheckBoxLongValue cbxFlagInformarCid;
    private Long flagInformarCid;

    private WebMarkupContainer containerFlagInformarCid;

    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;

    public CadastroCboFichaEsusPage() {
    }

    public CadastroCboFichaEsusPage(CboFichaEsus object, boolean viewOnly) {
        super(object, viewOnly);
        if (object != null) {
            ficha = object.getFicha();
        }
        carregarCbos(object);
    }

    @Override
    public void init(Form form) {

        form.add(ddTipoFicha = DropDownUtil.getIEnumDropDown("ficha", new PropertyModel<Long>(this, "ficha"), CboFichaEsus.TipoFicha.values(), true));

        form.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbo", new PropertyModel(this, "tabelaCbo")));
        autoCompleteConsultaTabelaCbo.setFiltrarAtivos(true);

        containerFlagInformarCid = new WebMarkupContainer("containerFlagInformarCid");
        containerFlagInformarCid.add(cbxFlagInformarCid = new CheckBoxLongValue("flagInformarCid", RepositoryComponentDefault.SIM_LONG, new PropertyModel(this, "flagInformarCid")));

        form.add(containerFlagInformarCid);

        if (!CboFichaEsus.TipoFicha.FICHA_ATENDIMENTO_INDIVIDUAL.value().equals(getForm().getModel().getObject().getFicha()))
            containerFlagInformarCid.setVisible(false);

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        table.populate();

        ddTipoFicha.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (existeCboFichaEsusFicha(ddTipoFicha.getComponentValue())) {
                    warn(target, bundle("msgExisteListaCBOTipoFicha", CboFichaEsus.TipoFicha.valueOf(ddTipoFicha.getComponentValue()).descricao()));
                    ddTipoFicha.limpar(target);
                }
            }
        });
    }


    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        CboFichaEsusItem proxy = on(CboFichaEsusItem.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("cbo"), proxy.getTabelaCbo().getDescricaoFormatado()));

        if (CboFichaEsus.TipoFicha.FICHA_ATENDIMENTO_INDIVIDUAL.value().equals(getForm().getModel().getObject().getFicha()))
        columns.add(createColumn(bundle("flagInformarCid"), proxy.getFlagInformarCidFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<CboFichaEsusItem>() {
            @Override
            public void customizeColumn(CboFichaEsusItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<CboFichaEsusItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, CboFichaEsusItem modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, table, lstCboGrupoAtendimento, modelObject);
                        if (CollectionUtils.isEmpty(lstCboGrupoAtendimento)) {
                            ddTipoFicha.setEnabled(true);
                            target.add(ddTipoFicha);
                        }
                    }
                }).setTitleBundleKey("detalhes");
            }
        };
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstCboGrupoAtendimento;
            }
        };
    }

    private void carregarCbos(CboFichaEsus object) {
        List<CboFichaEsusItem> list = LoadManager.getInstance(CboFichaEsusItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CboFichaEsusItem.PROP_CBO_FICHA_ESUS), object))
                .start().getList();
        this.lstCboGrupoAtendimento = new ArrayList<CboFichaEsusItem>(list);
        if (CollectionUtils.isNotNullEmpty(lstCboGrupoAtendimento)) {
            ddTipoFicha.setEnabled(false);
        }
    }

    private boolean existeCboFichaEsusFicha(Long ficha) {
        CboFichaEsus cboFichaEsus = LoadManager.getInstance(CboFichaEsus.class)
                .addProperty(CboFichaEsus.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(CboFichaEsus.PROP_FICHA, ficha))
                .start().getVO();
        return cboFichaEsus != null;
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tabelaCbo == null) {
            throw new ValidacaoException(BundleManager.getString("informeCbo"));
        }
        List<TabelaCbo> tabelaCbos = Lambda.extract(lstCboGrupoAtendimento, Lambda.on(CboFichaEsusItem.class).getTabelaCbo());

        if (CollectionUtils.isNotNullEmpty(tabelaCbos) && tabelaCbos.contains(tabelaCbo)) {
            throw new ValidacaoException(BundleManager.getString("cboJaInformado"));
        }

        CboFichaEsusItem cboFichaEsusItem = new CboFichaEsusItem();
        cboFichaEsusItem.setCboFichaEsus(getForm().getModelObject());
        cboFichaEsusItem.setTabelaCbo(tabelaCbo);
        cboFichaEsusItem.setFlagInformarCid(flagInformarCid);

        lstCboGrupoAtendimento.add(cboFichaEsusItem);
        table.update(target);
        if (CollectionUtils.isNotNullEmpty(lstCboGrupoAtendimento)) {
            ddTipoFicha.setEnabled(false);
            target.add(ddTipoFicha);
        }
        limpar(target);
        autoCompleteConsultaTabelaCbo.focus(target);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroCbosFichasEsus");
    }

    @Override
    public Object salvar(CboFichaEsus object) throws DAOException, ValidacaoException {
        object.setFicha(ficha);
        BOFactoryWicket.getBO(EsusFacade.class).salvarSalvarTabelaCboGrupoAtendimento(object, lstCboGrupoAtendimento);
        return null;
    }

    public void limpar(AjaxRequestTarget target) {
        this.autoCompleteConsultaTabelaCbo.limpar(target);
    }

    @Override
    public Class getReferenceClass() {
        return CboFichaEsus.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaCboFichaEsusPage.class;
    }

    public Long getFlagInformarCid() {
        return flagInformarCid;
    }

    public void setFlagInformarCid(Long flagInformarCid) {
        this.flagInformarCid = flagInformarCid;
    }
}
