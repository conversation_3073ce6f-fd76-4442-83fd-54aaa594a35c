package br.com.celk.view.unidadesaude.tipoatendimento;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroTipoAtendimentoDTO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Conduta;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by maicon on 09/05/16.
 */
public class TipoAtendimentoEsusTab extends TabPanel<CadastroTipoAtendimentoDTO> {

    private DropDown cbxTipoClassificacao;
    private DropDown<Conduta> cbxConduta;
    private DropDown dropDownConsulta;

    public TipoAtendimentoEsusTab(String id, CadastroTipoAtendimentoDTO object) {
        super(id, object);
        init();
    }

    private void init() {
        CadastroTipoAtendimentoDTO proxy = on(CadastroTipoAtendimentoDTO.class);

        add(cbxTipoClassificacao = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAtendimento().getTipoClassificacao()), TipoAtendimento.TipoClassificacao.values(), true));
        cbxTipoClassificacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateCbxConduta(target);
            }
        });

        add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoAtendimento().getTipoAtendimentoEsus()), TipoAtendimento.TipoAtendimentoEsus.values(), true));

        DropDown dropDownLocalAtendimento;
        add(dropDownLocalAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAtendimento().getLocalAtendimento()), Empresa.LocalAtendimento.values(), false, true, false, true));
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.UNIDADE_PRONTO_ATENDIMENTO.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.CACON_UNACON.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_URGENCIA_EMERGENCIA.value());
        dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_DEMAIS_SETORES.value());

        add(cbxConduta = getCbxConduta(path(proxy.getTipoAtendimento().getConduta())));

        add(dropDownConsulta = DropDownUtil.getSimNaoLongDropDown(path(proxy.getTipoAtendimento().getFlagConsulta()), false, false));
        dropDownConsulta.add(new Tooltip().setText("msgToolTipoConsultaTipoAtendimento"));
    }

    private DropDown getCbxConduta(String id) {
        if (cbxConduta == null) {
            cbxConduta = new DropDown(id);
            updateCbxConduta(null);
        }
        return cbxConduta;
    }

    private void updateCbxConduta(AjaxRequestTarget target) {
        cbxConduta.removeAllChoices();
        if (target != null) {
            cbxConduta.limpar(target);
        }

        Long tipoClassificacao = object.getTipoAtendimento().getTipoClassificacao();

        LoadManager load = LoadManager.getInstance(Conduta.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Conduta.PROP_DESCRICAO));

        if (TipoAtendimento.TipoClassificacao.ENFERMAGEM_MEDICA.value().equals(tipoClassificacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(Conduta.PROP_TIPO_CONDUTA, Conduta.TipoConduta.CONSULTA_MEDICA.value()));
        } else if (TipoAtendimento.TipoClassificacao.ODONTOLOGICA.value().equals(tipoClassificacao)) {
            load.addParameter(new QueryCustom.QueryCustomParameter(Conduta.PROP_TIPO_CONDUTA, Conduta.TipoConduta.CONSULTA_ODONTOLOGICA.value()));
        }

        List<Conduta> lstConduta = load.start().getList();
        cbxConduta.addChoice(null, "");

        for (Conduta item : lstConduta) {
            cbxConduta.addChoice(item, item.getDescricao());
        }

        if (target != null) {
            target.add(cbxConduta);
        }
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("esus");
    }

}
