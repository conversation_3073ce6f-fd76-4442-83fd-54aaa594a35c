package br.com.celk.view.unidadesaude.balancohidrico.tipoganhoperda;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroTipoGanhoPerdaPage extends CadastroPage<TipoGanhoPerda> {

    public CadastroTipoGanhoPerdaPage() {
    }

    public CadastroTipoGanhoPerdaPage(TipoGanhoPerda object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<TipoGanhoPerda> form) {
        TipoGanhoPerda proxy = on(TipoGanhoPerda.class);

        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipo()), TipoGanhoPerda.Tipo.values()));
        form.add(new RequiredInputField(path(proxy.getDescricao())));
    }

    @Override
    public Class<TipoGanhoPerda> getReferenceClass() {
        return TipoGanhoPerda.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoGanhoPerdaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroTipoGanhoPerda");
    }
}
