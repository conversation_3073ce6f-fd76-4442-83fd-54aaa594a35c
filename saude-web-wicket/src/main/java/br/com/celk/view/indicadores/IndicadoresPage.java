package br.com.celk.view.indicadores;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.breadcrumb.BreadCrumbPage;
import br.com.celk.view.indicadores.paciente.IndicadoresPacientePage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;


/**
 *
 * <AUTHOR>
 */
@Private

public class IndicadoresPage extends BreadCrumbPage{

    private DlgInformarPaciente dlgInformarPaciente;
    
    public IndicadoresPage() {
        init();
    }

    public IndicadoresPage(BreadCrumbPage originCrumb) {
        super(originCrumb);
        init();
    }

    public IndicadoresPage(IModel<?> model, BreadCrumbPage originCrumb) {
        super(model, originCrumb);
        init();
    }

    public IndicadoresPage(PageParameters parameters, BreadCrumbPage originCrumb) {
        super(parameters, originCrumb);
        init();
    }
    
    private void init(){
        Form form = new Form("form");
        form.add(new AbstractAjaxButton("btnPaciente") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgInformarPaciente.show(target);
            }
        });
        add(form);
        addModal(dlgInformarPaciente = new DlgInformarPaciente(newModalId()) {

            @Override
            public void onOk(AjaxRequestTarget target, UsuarioCadsus paciente) throws ValidacaoException, DAOException {
                setResponsePage(new IndicadoresPacientePage(IndicadoresPage.this, paciente));
            }
        });
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("indicadores");
    }

}
