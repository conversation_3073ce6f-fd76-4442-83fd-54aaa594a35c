package br.com.celk.view.cadsus.usuariocadsus.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public class DlgCadastroPaciente extends Window {

    private PnlCadastroPaciente pnlCadastroPaciente;
    private boolean exibeBotaoFechar;
    private Long codigoUsuarioCadsus;
    private final Atendimento atendimento;
    private Boolean gerarNovoAgravo;

    private boolean exibeSomenteDadosAdicionais;

    public DlgCadastroPaciente(String id) {
        this(id, false, null);
    }

    public DlgCadastroPaciente(String id, boolean exibeBotaoFechar, Long codigoUsuarioCadsus) {
        this(id, exibeBotaoFechar, codigoUsuarioCadsus, null, null);
    }

    public DlgCadastroPaciente(String id, boolean exibeBotaoFechar, Long codigoUsuarioCadsus, Atendimento atendimento, Boolean gerarNovoAgravo) {
        super(id);

        this.exibeBotaoFechar = exibeBotaoFechar;
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
        this.atendimento = atendimento;
        this.gerarNovoAgravo = gerarNovoAgravo;

        init();
    }

    private void init() {
        try {
            exibeSomenteDadosAdicionais = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("DadosAdicionais_CID"));
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        setTitle(BundleManager.getString("cadastroPaciente"));
        setInitialWidth(900);
        setInitialHeight(exibeSomenteDadosAdicionais ? 175 : 495);

        setContent(pnlCadastroPaciente = new PnlCadastroPaciente(getContentId(), exibeBotaoFechar, codigoUsuarioCadsus, atendimento, gerarNovoAgravo) {
            @Override
            public void onSalvar(AjaxRequestTarget target, final UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
                onFechar(target);
                DlgCadastroPaciente.this.setUsuarioCadsus(target, usuarioCadsus);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    private void limparPanel(AjaxRequestTarget target) {
        pnlCadastroPaciente.limpar(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgCadastroPaciente.this.onFechar(target);

    }

    @Override
    public void show(AjaxRequestTarget target) {
        super.show(target);
        pnlCadastroPaciente.onFocus(target);
    }

    public void show(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        super.show(target);
        pnlCadastroPaciente.setUsuarioCadsus(target, usuarioCadsus);
        pnlCadastroPaciente.onFocus(target);
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setUsuarioCadsus(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
    }

    public void onFechar(AjaxRequestTarget target) {
    }
}
