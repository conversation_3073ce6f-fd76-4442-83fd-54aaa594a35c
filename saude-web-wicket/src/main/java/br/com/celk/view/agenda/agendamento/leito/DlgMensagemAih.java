package br.com.celk.view.agenda.agendamento.leito;

import br.com.celk.component.window.Window;
import br.com.celk.view.comunicacao.mensagem.IMensagemController;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.dto.AihMensagemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.ArrayList;
import java.util.List;

public abstract class DlgMensagemAih extends Window {

    private PnlMensagemAih pnlMensagemAih;
    private MensagemDTO mensagemDTO;
    private Boolean view;

    public DlgMensagemAih(String id) {
        super(id);
        view = false;
        init(view);
    }

    public DlgMensagemAih(String id, Boolean view, AihMensagemDTO object) {
        super(id);
        gerarMensagemDTO(object);
        this.view = view;
        init(view);
    }

    private void init(Boolean view) {
        setTitle(Bundle.getStringApplication("rotulo_mensagem"));

        setInitialWidth(650);
        setInitialHeight(350);

        setResizable(false);

        setContent(pnlMensagemAih = new PnlMensagemAih(getContentId(), view, mensagemDTO) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, MensagemDTO mensagemDTO, Aih aih) throws ValidacaoException, DAOException {
                onFechar(target);
                DlgMensagemAih.this.onConfirmar(target, aih, mensagemDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });


        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    private void gerarMensagemDTO(AihMensagemDTO object) {
        mensagemDTO = new MensagemDTO();
        mensagemDTO.setMensagem(object.getMensagemOrigem().getMensagem());
        mensagemDTO.setAssunto(object.getMensagemOrigem().getAssunto());

        List<Usuario> usuarios = new ArrayList<>();

        for (Mensagem mensagem : object.getMensagens()) {
            usuarios.add(mensagem.getUsuario());
        }

        mensagemDTO.setUsuarios(usuarios);
        mensagemDTO.setMensagemOrigem(object.getMensagemOrigem());
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih, MensagemDTO mensagemDTO) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, Aih autorizacaoInternacaoHospitalar) {
        if (!view) {
            pnlMensagemAih.setAih(target, autorizacaoInternacaoHospitalar);
        }

        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
    }
}
