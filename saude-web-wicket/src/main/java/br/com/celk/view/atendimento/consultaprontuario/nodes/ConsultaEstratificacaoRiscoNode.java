package br.com.celk.view.atendimento.consultaprontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesConsultaProntuarioRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.consultaprontuario.nodes.annotations.ConsultaProntuarioNode;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.ConsultaProntuarioNodeImp;
import br.com.celk.view.atendimento.consultaprontuario.panel.consultaestratificacaorisco.ConsultaEstratificacaoRiscoPanel;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;

/**
 * <AUTHOR>
 */
@ConsultaProntuarioNode(NodesConsultaProntuarioRef.CONSULTA_ESTRATIFICA_RISCO_PARANA)
public class ConsultaEstratificacaoRiscoNode extends ConsultaProntuarioNodeImp {

    @Override
    public ConsultaProntuarioCadastroPanel getPanel(String id) {
        return new ConsultaEstratificacaoRiscoPanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.ROTEIRO;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("estratificacaoRisco");
    }
}