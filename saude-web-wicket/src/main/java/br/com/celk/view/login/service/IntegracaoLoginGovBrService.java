package br.com.celk.view.login.service;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.jose4j.json.internal.json_simple.JSONArray;
import org.jose4j.json.internal.json_simple.JSONObject;
import org.jose4j.json.internal.json_simple.parser.JSONParser;
import org.jose4j.jwk.PublicJsonWebKey;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;

import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

public class IntegracaoLoginGovBrService implements Serializable {

    private static final long serialVersionUID = 1L;

    private static String URL_PROVIDER;
    private static String ID_CLIENT;
    private static String SECRET;
    private static String REDIRECT_URI;
    private static final String CODE_CHALLENGE_METHOD = "S256";
    public static String CODE_CHALLENGE;
    public static String STATE;
    public static String ID_TOKEN;
    private static final String SCOPES = "openid+email+profile+govbr_empresa+govbr_confiabilidades";
    public static String CODE_VERIFIER;
    private static final String CODE_VERIFIER_CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
    private static final int CODE_VERIFIER_LENGTH = 64;
    public static Boolean CARREGAR_LOG_GOV_BR;

    public IntegracaoLoginGovBrService() throws ValidacaoException, DAOException {
        this.CARREGAR_LOG_GOV_BR = carregarLoginGovBr();
        if(CARREGAR_LOG_GOV_BR){
            this.URL_PROVIDER = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("urlIntegracaoGovBr");
            this.ID_CLIENT = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("idUsuarioGovBr");
            this.SECRET = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("secretGovBr");
            this.REDIRECT_URI = "https://"+ TenantContext.getRealContext();
            this.CODE_VERIFIER = generateCodeVerifier();
            this.CODE_CHALLENGE = generateCodeChallenge(this.CODE_VERIFIER);
            this.CARREGAR_LOG_GOV_BR = carregarLoginGovBr();
            validarParametro(URL_PROVIDER, "urlIntegracaoGovBr");
            validarParametro(ID_CLIENT, "idUsuarioGovBr");
            validarParametro(SECRET, "secretGovBr");
            validarParametro(REDIRECT_URI, "redirectUri");
        }
    }

    public String execute(String code, String challenge, String codeVerifier) throws Exception {
        String tokens = extractToken(code, challenge,codeVerifier);

        JSONParser parser = new JSONParser();
        JSONObject tokensJson = (JSONObject) parser.parse(tokens);

        String accessToken = (String) tokensJson.get("access_token");
        ID_TOKEN = (String) tokensJson.get("id_token");

        JwtClaims idTokenJwtClaims;
        try {
            idTokenJwtClaims = processToClaims(ID_TOKEN);
            return idTokenJwtClaims.getSubject();
        } catch (Exception e) {
            System.out.println("Access Token inválido!");
            throw new Exception("Erro ao processar token: " + e.getMessage(), e);
        }
    }

    private void validarParametro(String valor, String nomeParametro) throws ValidacaoException {
        if (valor == null || valor.trim().isEmpty()) {
            throw new ValidacaoException("O parâmetro '" + nomeParametro + "' está nulo ou vazio. Procure o Suporte.");
        }
    }

    public String retornaUrlAutorize() throws UnsupportedEncodingException {
        this.STATE = UUID.randomUUID().toString();
        return URL_PROVIDER + "/authorize?response_type=code"
                + "&client_id=" + ID_CLIENT
                + "&scope=" + SCOPES
                + "&redirect_uri=" + URLEncoder.encode(REDIRECT_URI, "UTF-8")
                + "&nonce=" + STATE
                + "&state=" + STATE
                + "&code_challenge=" + CODE_CHALLENGE
                + "&code_challenge_method="+CODE_CHALLENGE_METHOD;
    }

    private String extractToken(String code, String challenge,String codeVerifier) throws Exception {
        String redirectURIEncodedURL = URLEncoder.encode(this.REDIRECT_URI, StandardCharsets.UTF_8.toString());

        // Montar os parâmetros no corpo da requisição
        String postData = "grant_type=authorization_code" +
                "&code=" + code +
                "&redirect_uri=" + redirectURIEncodedURL +
                "&code_verifier=" + codeVerifier;

        URL url = new URL(URL_PROVIDER + "/token");
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();

        // Configurações da conexão
        conn.setRequestMethod("POST");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setRequestProperty("Accept", "application/json");
        conn.setRequestProperty("Authorization", String.format("Basic %s",
                Base64.getEncoder().encodeToString(String.format("%s:%s", this.ID_CLIENT, this.SECRET).getBytes(StandardCharsets.UTF_8))));

        // Habilitar envio de corpo na requisição POST
        conn.setDoOutput(true);
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = postData.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        int responseCode = conn.getResponseCode();
        InputStream is = responseCode >= 400 ? conn.getErrorStream() : conn.getInputStream();

        if (is == null) {
            throw new Exception("Nenhum conteúdo retornado pela API. Código: " + responseCode);
        }

        try (BufferedReader br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }

            if (responseCode >= 400) {
                throw new Exception("Erro na requisição: " + responseCode + " - " + response.toString());
            }

            System.out.println("Resposta da API (" + responseCode + "): " + response.toString());
            return response.toString();
        }
    }

    public void logoutGovBr(AjaxRequestTarget target) throws Exception {
        if(CARREGAR_LOG_GOV_BR){
            String redirectURIEncodedURL = URLEncoder.encode(this.REDIRECT_URI, StandardCharsets.UTF_8.toString());
            String redirectUrl = URL_PROVIDER + "/logout"
                    + "&post_logout_redirect_uri=" + redirectURIEncodedURL;
            target.appendJavaScript("console.log('Redirecionando...'); window.location.href='" + redirectUrl + "';");

        }

    }

    public static String generateCodeChallenge(String codeVerifier) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(codeVerifier.getBytes(StandardCharsets.US_ASCII));
            return Base64.getUrlEncoder()
                    .withoutPadding()
                    .encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 Algorithm not found", e);
        }
    }

    public static String generateCodeVerifier() {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder codeVerifier = new StringBuilder(CODE_VERIFIER_LENGTH);
        for (int i = 0; i < CODE_VERIFIER_LENGTH; i++) {
            int index = secureRandom.nextInt(CODE_VERIFIER_CHARACTERS.length());
            codeVerifier.append(CODE_VERIFIER_CHARACTERS.charAt(index));
        }
        return codeVerifier.toString();
    }

    private static JwtClaims processToClaims(String token) throws Exception {
        URL url = new URL(URL_PROVIDER + "/jwk");
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Accept", "application/json");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);

        if (conn.getResponseCode() != 200) {
            throw new RuntimeException("Falhou : HTTP error code : " + conn.getResponseCode());
        }

        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder jwk = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                jwk.append(line);
            }

            JSONParser parser = new JSONParser();
            JSONObject tokensJson = (JSONObject) parser.parse(jwk.toString());

            JSONArray keys = (JSONArray) tokensJson.get("keys");
            JSONObject keyJSONObject = (JSONObject) keys.get(0);
            String key = keyJSONObject.toJSONString();

            PublicJsonWebKey pjwk = PublicJsonWebKey.Factory.newPublicJwk(key);

            JwtConsumer jwtConsumer = new JwtConsumerBuilder()
                    .setRequireExpirationTime()
                    .setExpectedAudience(ID_CLIENT)
                    .setMaxFutureValidityInMinutes(60)
                    .setAllowedClockSkewInSeconds(30)
                    .setRequireSubject()
                    .setExpectedIssuer(URL_PROVIDER + "/")
                    .setVerificationKey(pjwk.getPublicKey())
                    .build();

            return jwtConsumer.processToClaims(token);
        }
    }

    public boolean carregarLoginGovBr() {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.INTEGRACAO).getParametro("ativarIntegracaoGovBr").equals("S");
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
    }
}