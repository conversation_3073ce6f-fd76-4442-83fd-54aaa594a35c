package br.com.celk.view.controle.funcaousuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.controle.grupo.customize.CustomizeConsultaGrupo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Grupo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaFuncaoUsuarioPage extends ConsultaPage<Grupo, List<QueryParameter>> {

    public ConsultaFuncaoUsuarioPage() {
        super();
    }

    public ConsultaFuncaoUsuarioPage(PageParameters parameters) {
        super(parameters);
    }

    private InputField txtDescricao;

    @Override
    public void initForm(Form form) {
        form.add(txtDescricao = new InputField("txtDescricao", new Model()));
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Grupo.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("codigo"), VOUtils.montarPath(Grupo.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("nome"), VOUtils.montarPath(Grupo.PROP_NOME)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<Grupo>() {
            @Override
            public Component getComponent(String componentId, Grupo rowObject) {
                return new CrudActionsColumnPanel<Grupo>(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroFuncaoUsuarioPage(getObject()));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(getObject());
                        getPageableTable().update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroFuncaoUsuarioPage(getObject(), true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaGrupo());
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Grupo.PROP_NOME), QueryParameter.ILIKE, txtDescricao.getComponentValue()));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroFuncaoUsuarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_funcoes");
    }

}
