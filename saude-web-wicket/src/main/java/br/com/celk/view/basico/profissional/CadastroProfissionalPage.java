package br.com.celk.view.basico.profissional;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.biometria.Biometria;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.profissional.tabbedpanel.CadastroProfissionalTabbedPanel;
import br.com.celk.view.basico.profissional.tabbedpanel.DadosProfissionalTab;
import br.com.celk.view.basico.profissional.tabbedpanel.TissProfissionalConvenioTab;
import br.com.celk.view.basico.profissional.tabbedpanel.VinculosProfissionalTab;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.ProfissionalHistorico;
import br.com.ksisolucoes.vo.hospital.tiss.EloTissProfissionalConvenio;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroProfissionalPage extends BasePage {
    
    private Biometria appletBiometria;

    public CadastroProfissionalPage() {
        this(null);
    }
    
    public CadastroProfissionalPage(CadastroProfissionalDTO cadastroProfissionalDTO) {
        this(cadastroProfissionalDTO, false);
    }

    public CadastroProfissionalPage(CadastroProfissionalDTO cadastroProfissionalDTO, boolean viewOnly) {
        init(cadastroProfissionalDTO, viewOnly);
    }

    private void init(CadastroProfissionalDTO cadastroProfissionalDTO, boolean viewOnly){
        if(cadastroProfissionalDTO == null){
            cadastroProfissionalDTO = getNewInstance();
        }
        
        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<CadastroProfissionalDTO>(cadastroProfissionalDTO) {

            @Override
            public ITabPanel<CadastroProfissionalDTO> newTabPanel(String panelId,CadastroProfissionalDTO cadastroProfissionalDTO) {
                return new DadosProfissionalTab(panelId, cadastroProfissionalDTO);
            }
        });
        tabs.add(new CadastroTab<CadastroProfissionalDTO>(cadastroProfissionalDTO) {

            @Override
            public ITabPanel<CadastroProfissionalDTO> newTabPanel(String panelId,CadastroProfissionalDTO cadastroProfissionalDTO) {
                return new VinculosProfissionalTab(panelId, cadastroProfissionalDTO);
            }
        });
        tabs.add(new CadastroTab<CadastroProfissionalDTO>(cadastroProfissionalDTO) {

            @Override
            public ITabPanel<CadastroProfissionalDTO> newTabPanel(String panelId, CadastroProfissionalDTO cadastroProfissionalDTO) {
                return new TissProfissionalConvenioTab(panelId, cadastroProfissionalDTO);
            }
        });
        
        CadastroTabbedPanel cadastroProfissionalTabbedPanel;
        add(cadastroProfissionalTabbedPanel = new CadastroProfissionalTabbedPanel("wizard", cadastroProfissionalDTO, viewOnly, tabs));
        
        appletBiometria = new Biometria("appletBiometria");
        add(appletBiometria);
        
        appletBiometria.setListener((IAppletAction) cadastroProfissionalTabbedPanel);
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProfissional");
    }

    private CadastroProfissionalDTO getNewInstance() {
        CadastroProfissionalDTO object= new CadastroProfissionalDTO();
        object.setProfissional(new Profissional());
        object.setVinculos(new ArrayList<ProfissionalCargaHoraria>());
        object.setHistoricoVinculos(new ArrayList<ProfissionalHistorico>());
        object.setElosTiss(new ArrayList<EloTissProfissionalConvenio>());

        return object;
    }
}
