package br.com.celk.view.vigilancia.dengue.localidade;

import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
public class CadastroDengueLocalidadePage extends CadastroPage<DengueLocalidade> {

    private InputField txtDescricao;
    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private WebMarkupContainer containerInativacao;
    private DropDown dropDownSituacao;
    private DateChooser dataInativacao;
    private RequiredDateChooser dataRegistro;
    private InputField motivoInativacao;
    private WebMarkupContainer containerDadosEdicao;

    public CadastroDengueLocalidadePage(DengueLocalidade object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroDengueLocalidadePage(DengueLocalidade object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroDengueLocalidadePage(DengueLocalidade object) {
        this(object, false);
    }

    public CadastroDengueLocalidadePage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        addContainerDadosEdicao(form);
        form.add(new InputField(DengueLocalidade.PROP_CODIGO).setEnabled(false));
        form.add(txtDescricao = new RequiredInputField(DengueLocalidade.PROP_LOCALIDADE));
        form.add(dataRegistro = new RequiredDateChooser(VOUtils.montarPath(DengueLocalidade.PROP_DATA_REGISTRO)));
        dataRegistro.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade(DengueLocalidade.PROP_CIDADE, true));
        form.add(new RequiredInputField<String>(DengueLocalidade.PROP_CATEGORIA));
        form.add(DropDownUtil.getIEnumDropDown(DengueLocalidade.PROP_ZONA, DengueLocalidade.Zona.values(), true, true));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown(DengueLocalidade.PROP_SITUACAO, DengueLocalidade.Situacao.values()));
        dropDownSituacao.addAjaxUpdateValue();

        containerInativacao = new WebMarkupContainer("containerInativacao");
        containerInativacao.setVisible(false);
        containerInativacao.setOutputMarkupId(true);
        containerInativacao.setOutputMarkupPlaceholderTag(true);
        containerInativacao.add(dataInativacao = new DateChooser(VOUtils.montarPath(DengueLocalidade.PROP_DATA_INATIVACAO)));
        dataInativacao.setOutputMarkupId(true);
        containerInativacao.add(motivoInativacao = new InputField(DengueLocalidade.PROP_MOTIVO_INATIVACAO));
        form.add(containerInativacao);

        if (dropDownSituacao.getComponentValue() == null) {
            dropDownSituacao.setComponentValue(DengueLocalidade.Situacao.ATIVO.value());
        } else if (DengueLocalidade.Situacao.INATIVO.value().equals(dropDownSituacao.getComponentValue())) {
            containerInativacao.setVisible(true);
            dataInativacao.setRequired(true);
            dataInativacao.addRequiredClass();
        }

        dropDownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (DengueLocalidade.Situacao.INATIVO.value().equals(dropDownSituacao.getComponentValue())) {
                    containerInativacao.setVisible(true);
                    dataInativacao.setRequired(true);
                    dataInativacao.addRequiredClass();
                } else {
                    containerInativacao.setVisible(false);
                    dataInativacao.removeRequiredClass();
                    dataInativacao.setRequired(true);
                }
                target.add(containerInativacao);
            }
        });
        if (getForm().getModelObject().getDataRegistro() == null) {
            getForm().getModelObject().setDataRegistro(DataUtil.getDataAtual());
        }
    }

    private void addContainerDadosEdicao(Form form) {
        form.add(containerDadosEdicao = new WebMarkupContainer("containerDadosEdicao"));
        containerDadosEdicao.add(new DisabledInputField(VOUtils.montarPath(DengueLocalidade.PROP_USUARIO_CADASTRO, Usuario.PROP_NOME)));
        containerDadosEdicao.add(new DateChooser(DengueLocalidade.PROP_DATA_CADASTRO).setEnabled(false));

        if (isViewOnly()) {
            containerDadosEdicao.setVisible(true);
        } else {
            containerDadosEdicao.setVisible(false);
        }
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<DengueLocalidade> getReferenceClass() {
        return DengueLocalidade.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaDengueLocalidadePage.class;
    }
    
    @Override
    public Page getResponsePageInstance(Object returnObject) throws InstantiationException, IllegalAccessException {
        return new ConsultaDengueLocalidadePage((DengueLocalidade) (returnObject));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroDengueLocalidade");
    }

    @Override
    public Object salvar(DengueLocalidade object) throws DAOException, ValidacaoException {
        if (object.getDataRegistro().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(bundle("msgDataRegistroNaoPodeSerMaiorDataAtual"));
        }
        return super.salvar(object);
    }


}
