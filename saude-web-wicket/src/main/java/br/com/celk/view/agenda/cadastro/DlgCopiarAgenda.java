package br.com.celk.view.agenda.cadastro;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.agendamento.dto.CopiaAgendaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCopiarAgenda extends Window {

    private PnlCopiarAgenda pnlCopiarAgenda;

    public DlgCopiarAgenda(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("copiarAgenda"));

        setInitialWidth(700);
        setInitialHeight(180);

        setResizable(false);

        setContent(pnlCopiarAgenda = new PnlCopiarAgenda(getContentId()) {
            @Override
            public void onOk(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO) throws ValidacaoException, DAOException {
                try {
                    close(target);
                    DlgCopiarAgenda.this.onOk(target, pnlCopiarAgenda.getCopiaAgendaDTO(), agendaGradeAtendimentoHorariosDTO);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, this, ex);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onOk(AjaxRequestTarget target, CopiaAgendaDTO dto, AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) {
    }

    public void show(AjaxRequestTarget target, AgendaGrade agendaGrade, AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO) {
        pnlCopiarAgenda.limpar(target);
        pnlCopiarAgenda.setDataBase(agendaGrade.getData(), agendaGrade.getHoraInicial(), agendaGrade.getHoraFinal(), agendaGradeAtendimentoHorariosDTO);
                
        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgCopiarAgenda.this.onFechar(target);
        close(target);
    }
}
