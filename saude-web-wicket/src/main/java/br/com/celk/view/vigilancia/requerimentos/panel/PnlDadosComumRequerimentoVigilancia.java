package br.com.celk.view.vigilancia.requerimentos.panel;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.externo.view.home.VigilanciaHomePage;
import br.com.celk.view.vigilancia.requerimentos.RequerimentosPage;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.DadosComumRequerimentoVigilanciaDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class PnlDadosComumRequerimentoVigilancia extends Panel {

    private WebMarkupContainer containerOcorrenciaRequerimento;
    private WebMarkupContainer containerSetorVigilancia;
    private WebMarkupContainer containerFiscais;
    boolean enabled;
    private DadosComumRequerimentoVigilanciaDTOParam param;
    private AutoCompleteConsultaSetorVigilancia autoCompleteConsultaSetorVigilancia;
    private Table tblSetorResponsavel;
    private Table tblFiscais;
//    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList = new ArrayList<>();
    private SetorVigilancia setorVigilancia;
    private Profissional fiscal;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AbstractAjaxButton btnAdicionarFiscal;
    private AbstractAjaxButton btnAdicionar;
    private boolean habilitaFiscal;
    private boolean naoPermissaoCadastroCompleto;
    private boolean naoPermissaoCadastroCompletoExterno;
    private InputArea observacaoRequerimento;
    private String lblFiscais = "Fiscais";

    public PnlDadosComumRequerimentoVigilancia(String id, DadosComumRequerimentoVigilanciaDTOParam param, boolean enabled) {
        super(id);
        this.param = param;
        this.enabled = enabled;
        init();
    }

    public PnlDadosComumRequerimentoVigilancia(String id, DadosComumRequerimentoVigilanciaDTOParam param, boolean enabled, String lblFiscais) {
        super(id);
        this.param = param;
        this.enabled = enabled;
        this.lblFiscais = lblFiscais;
        init();
    }

    private void init() {
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));

        criarOcorrenciaRequerimento(root);
        criarSetorVigilancia(root);
        criarFiscais(root);

        add(root);
    }

    private void criarOcorrenciaRequerimento(WebMarkupContainer root) {
        DadosComumRequerimentoVigilanciaDTOParam proxy = on(DadosComumRequerimentoVigilanciaDTOParam.class);

        containerOcorrenciaRequerimento = new WebMarkupContainer("containerOcorrenciaRequerimento");
        containerOcorrenciaRequerimento.setVisible(isVisibleContainerOcorrencia());
        containerOcorrenciaRequerimento.setOutputMarkupId(true);

        observacaoRequerimento = new InputArea(path(proxy.getObservacaoRequerimento()));
        observacaoRequerimento.setEnabled(enabled);

        containerOcorrenciaRequerimento.add(observacaoRequerimento);
        root.add(containerOcorrenciaRequerimento);
    }

    private void criarSetorVigilancia(WebMarkupContainer root) {
        naoPermissaoCadastroCompleto = new PermissoesWebUtil().isActionPermitted(Permissions.NAO_VISUALIZAR_CADASTRO_COMPLETO, RequerimentosPage.class, true);
        naoPermissaoCadastroCompletoExterno = new PermissoesWebUtil().isActionPermitted(Permissions.NAO_VISUALIZAR_CADASTRO_COMPLETO, VigilanciaHomePage.class, true);

        containerSetorVigilancia = new WebMarkupContainer("containerSetorVigilancia");
        containerSetorVigilancia.setVisible(isVisibleContainerSetor());
        containerSetorVigilancia.setOutputMarkupId(true);
        containerSetorVigilancia.setEnabled(enabled);

        autoCompleteConsultaSetorVigilancia = new AutoCompleteConsultaSetorVigilancia("setorVigilancia", new PropertyModel(this, "setorVigilancia"));
        autoCompleteConsultaSetorVigilancia.setEnabled(enabled);
        autoCompleteConsultaSetorVigilancia.setOutputMarkupId(true);
        autoCompleteConsultaSetorVigilancia.setLabel(new Model(Bundle.getStringApplication("setorResponsavel")));

        btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        };
        btnAdicionar.setDefaultFormProcessing(false);

        tblSetorResponsavel = new Table("tblSetorResponsavel", getColumns(), getCollectionProvider());
        tblSetorResponsavel.setScrollY("180px");
        tblSetorResponsavel.populate();

        containerSetorVigilancia.add(autoCompleteConsultaSetorVigilancia, btnAdicionar, tblSetorResponsavel);
        root.add(containerSetorVigilancia);

        if (naoPermissaoCadastroCompleto && naoPermissaoCadastroCompletoExterno) {
            containerSetorVigilancia.setVisible(false);
        } else {
            containerSetorVigilancia.setVisible(true);
        }
    }

    private boolean hasPermissionEditarFiscal() {
        return new PermissoesWebUtil().isActionPermitted(Permissions.EDITAR_FISCAIS, RequerimentosPage.class);
    }

    private void criarFiscais(WebMarkupContainer root) {
        containerFiscais = new WebMarkupContainer("containerFiscais");
        containerFiscais.setVisible(isVisibleContainerFiscal());
        containerFiscais.setOutputMarkupId(true);
        containerFiscais.setEnabled(enabled && hasPermissionEditarFiscal());

        autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("fiscal", new PropertyModel(this, "fiscal"));
        autoCompleteConsultaProfissional.setOutputMarkupId(true);
        autoCompleteConsultaProfissional.setLabel(new Model(Bundle.getStringApplication("fiscal")));

        btnAdicionarFiscal = new AbstractAjaxButton("btnAdicionarFiscal") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarFiscal(target);
            }
        };
        btnAdicionarFiscal.setDefaultFormProcessing(false);

        tblFiscais = new Table("tblFiscais", getColumnsFiscais(), getCollectionProviderFiscais());
        tblFiscais.setScrollY("180px");
        tblFiscais.populate();

        containerFiscais.add(new Label("lblFiscais", new CompoundPropertyModel(lblFiscais)));
        containerFiscais.add(autoCompleteConsultaProfissional, btnAdicionarFiscal, tblFiscais);

        root.add(containerFiscais);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException{
        if (setorVigilancia == null) {
            throw new ValidacaoException(BundleManager.getString("informeSetorResponsavel"));
        }

        if(CollectionUtils.isNotNullEmpty(getParam().getEloRequerimentoVigilanciaSetorVigilanciaList())){
            for (EloRequerimentoVigilanciaSetorVigilancia elo : getParam().getEloRequerimentoVigilanciaSetorVigilanciaList()) {
                if (elo.getSetorVigilancia().getCodigo().equals(setorVigilancia.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("setorJaAdicionado"));
                }
            }
        }

        EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
        elo.setSetorVigilancia(setorVigilancia);
        getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);

        tblSetorResponsavel.update(target);
        setorVigilancia = null;
        autoCompleteConsultaSetorVigilancia.limpar(target);
    }

    private void adicionarFiscal(AjaxRequestTarget target) throws ValidacaoException{
        if (fiscal == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformeFiscal"));
        }

        if(CollectionUtils.isNotNullEmpty(getParam().getRequerimentoVigilanciaFiscalList())){
            for (RequerimentoVigilanciaFiscal fiscalAux : getParam().getRequerimentoVigilanciaFiscalList()) {
                if (fiscalAux.getProfissional().getCodigo().equals(fiscal.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("fiscalJaAdicionado"));
                }
            }
        }

        RequerimentoVigilanciaFiscal requerimentoVigilanciaFiscal = new RequerimentoVigilanciaFiscal();
        requerimentoVigilanciaFiscal.setProfissional(fiscal);
        getParam().getRequerimentoVigilanciaFiscalList().add(requerimentoVigilanciaFiscal);

        tblFiscais.update(target);
        fiscal = null;
        autoCompleteConsultaProfissional.limpar(target);
    }
    
    private List<IColumn> getColumns() {
        ColumnFactory columnFactory = new ColumnFactory(EloRequerimentoVigilanciaSetorVigilancia.class);
        List<IColumn> columns = new ArrayList<>();
        EloRequerimentoVigilanciaSetorVigilancia proxy = on(EloRequerimentoVigilanciaSetorVigilancia.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("setor"), path(proxy.getSetorVigilancia().getDescricao())));

        return columns;
    }

    private List<IColumn> getColumnsFiscais() {
        ColumnFactory columnFactory = new ColumnFactory(RequerimentoVigilanciaFiscal.class);
        List<IColumn> columns = new ArrayList<>();
        RequerimentoVigilanciaFiscal proxy = on(RequerimentoVigilanciaFiscal.class);

        columns.add(getCustomColumnFiscais());
        columns.add(columnFactory.createColumn(BundleManager.getString("fiscal"), path(proxy.getProfissional().getDescricaoFormatado())));

        return columns;
    }
    
    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EloRequerimentoVigilanciaSetorVigilancia>() {
            @Override
            public void customizeColumn(final EloRequerimentoVigilanciaSetorVigilancia rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject);
                    }
                });
            }
        };
    }

    private IColumn getCustomColumnFiscais() {
        return new MultipleActionCustomColumn<RequerimentoVigilanciaFiscal>() {
            @Override
            public void customizeColumn(final RequerimentoVigilanciaFiscal rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerFiscal(target, rowObject);
                    }
                }).setVisible(new PermissoesWebUtil().isActionPermitted(Permissions.REMOVER_FISCAIS, RequerimentosPage.class)
                        || rowObject.getCodigo() == null);
            }
        };
    }

    private void removerFiscal(AjaxRequestTarget target, RequerimentoVigilanciaFiscal rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < getParam().getRequerimentoVigilanciaFiscalList().size(); i++) {
            RequerimentoVigilanciaFiscal fiscal = getParam().getRequerimentoVigilanciaFiscalList().get(i);
            if (fiscal == rowObject) {
                if (fiscal.getCodigo() != null) {
                    getParam().getRequerimentoVigilanciaFiscalListExcluir().add(fiscal);
                }
                getParam().getRequerimentoVigilanciaFiscalList().remove(i);
            }
        }
        tblFiscais.populate();
        tblFiscais.update(target);

        CrudUtils.removerItem(target, tblFiscais, getParam().getRequerimentoVigilanciaFiscalList(), rowObject);
    }
    
    private void remover(AjaxRequestTarget target, EloRequerimentoVigilanciaSetorVigilancia rowObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().size(); i++) {
            EloRequerimentoVigilanciaSetorVigilancia elo = getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().get(i);
            if (elo == rowObject) {
                if (elo.getCodigo() != null) {
                    getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList().add(elo);
                }
                getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().remove(i);
            }
        }
        tblSetorResponsavel.populate();
        tblSetorResponsavel.update(target);
        
        CrudUtils.removerItem(target, tblSetorResponsavel, getParam().getEloRequerimentoVigilanciaSetorVigilanciaList(), rowObject);
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getParam().getEloRequerimentoVigilanciaSetorVigilanciaList();
            }
        };
    }

    private ICollectionProvider getCollectionProviderFiscais() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getParam().getRequerimentoVigilanciaFiscalList();
            }
        };
    }
    
    public DadosComumRequerimentoVigilanciaDTOParam getParam() {
        return param;
    }

    public Table getTblSetorResponsavel() {
        return tblSetorResponsavel;
    }

    public Table getTblFiscais() {
        return tblFiscais;
    }

    public AutoCompleteConsultaSetorVigilancia getAutoCompleteConsultaSetorVigilancia() {
        return autoCompleteConsultaSetorVigilancia;
    }

    public void setAutoCompleteConsultaSetorVigilancia(AutoCompleteConsultaSetorVigilancia autoCompleteConsultaSetorVigilancia) {
        this.autoCompleteConsultaSetorVigilancia = autoCompleteConsultaSetorVigilancia;
    }

    public WebMarkupContainer getContainerSetorVigilancia() {
        return containerSetorVigilancia;
    }

    public void limpar(AjaxRequestTarget target) {
        if (CollectionUtils.isNotNullEmpty(getParam().getEloRequerimentoVigilanciaSetorVigilanciaList())) {
            getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
            tblSetorResponsavel.update(target);
        }
    }

    public boolean isVisibleContainerFiscal(){
        return !getParam().isDesabilitaFiscais();
    }

    public boolean isVisibleContainerSetor(){
        return true;
    }

    private boolean isVisibleContainerOcorrencia() {
        return true;
    }

    public InputArea getObservacaoRequerimento() {
        return observacaoRequerimento;
    }

    public void setObservacaoRequerimento(InputArea observacaoRequerimento) {
        this.observacaoRequerimento = observacaoRequerimento;
    }
}