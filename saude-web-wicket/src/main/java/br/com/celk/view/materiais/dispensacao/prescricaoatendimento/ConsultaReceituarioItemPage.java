package br.com.celk.view.materiais.dispensacao.prescricaoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.unidadesaude.receituario.DlgSelecionaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade.DispensacaoMedicamentoFacade;
import br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto.QueryPagerConsultaReceituarioItemDTO;
import br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto.QueryPagerConsultaReceituarioItemDTOparam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaReceituarioItemPage extends BasePage {

    private PageableTable<ReceituarioItem> tblItens;
    private final String validaSaldo;
    private Form<QueryPagerConsultaReceituarioItemDTOparam> form;

    private QueryPagerProvider<QueryPagerConsultaReceituarioItemDTO, QueryPagerConsultaReceituarioItemDTOparam> dataProvider;

    private DlgSelecionaEmpresa dlgSelecionaEmpresa;
    private Empresa empresaDispensacao;
    private boolean permissaoVisualizarApenasPrescricaoEstabelecimento;

    public ConsultaReceituarioItemPage(Empresa empresaDispensacao, boolean permissaoVisualizarApenasPrescricaoEstabelecimento) throws DAOException {
        this.empresaDispensacao = empresaDispensacao;
        this.permissaoVisualizarApenasPrescricaoEstabelecimento = permissaoVisualizarApenasPrescricaoEstabelecimento;
        validaSaldo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidaSaldoDispensacaoPrescricao");
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(new QueryPagerConsultaReceituarioItemDTOparam()));

        form.add(new InputField("numeroAtendimento"));
        form.add(new InputField("nomePaciente"));
        form.add(new AutoCompleteConsultaProduto("produto"));
        form.add(new InputField("quarto"));

        form.add(tblItens = new PageableTable("table", getColumns(), getDataProvider()));
        tblItens.getDataProvider().setParameters(getParam());
        tblItens.populate();
        form.add(new ProcurarButton("btnProcurar", tblItens) {
            @Override
            public QueryPagerConsultaReceituarioItemDTOparam getParam() {
                return ConsultaReceituarioItemPage.this.getParam();
            }
        });

        form.add(new VoltarButton("btnVoltar"));

        add(form);
    }

    public List<IColumn> getColumns() {

        List<IColumn> columns = new ArrayList<IColumn>();

        QueryPagerConsultaReceituarioItemDTO proxy = on(QueryPagerConsultaReceituarioItemDTO.class);

        Receituario proxyReceituario = on(Receituario.class);
        ReceituarioItem proxyReceituarioItem = on(ReceituarioItem.class);
        LeitoQuarto proxyLeitoQuarto = on(LeitoQuarto.class);
        Produto proxyProduto = on(Produto.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("atendimento"), proxyReceituario.getAtendimento().getCodigo(), proxy.getReceituarioItem().getReceituario().getAtendimento().getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"), proxyReceituario.getUsuarioCadsus().getNome(), proxy.getReceituarioItem().getReceituario().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("dataPrescricao"), proxyReceituarioItem.getReceituario().getDataReceituario(), proxy.getReceituarioItem().getReceituario().getDataReceituario()));
        columns.add(createSortableColumn(bundle("farmaciaDispensacao"), proxyReceituario.getEmpresaDispensacao().getCodigo(), proxy.getReceituarioItem().getReceituario().getEmpresaDispensacao().getDescricao()));
        columns.add(createSortableColumn(bundle("setor"), proxyReceituario.getEmpresa().getCodigo(), proxy.getReceituarioItem().getReceituario().getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("leito"), proxyLeitoQuarto.getQuartoInternacao().getReferencia(), proxy.getReceituarioItem().getReceituario().getAtendimento().getLeitoQuarto().getDescricaoQuarto()));
        columns.add(createSortableColumn(bundle("profissional"), proxyReceituario.getProfissional().getNome(), proxy.getReceituarioItem().getReceituario().getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("produto"), proxyReceituarioItem.getProduto().getDescricao(), proxy.getReceituarioItem().getProduto().getDescricao()));
        columns.add(createSortableColumn(bundle("un"), proxyProduto.getUnidade().getUnidade(), proxy.getReceituarioItem().getProduto().getUnidade().getUnidade()));
        columns.add(createSortableColumn(bundle("saldo"), proxy.getSaldo()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<QueryPagerConsultaReceituarioItemDTO>() {
            @Override
            public void customizeColumn(QueryPagerConsultaReceituarioItemDTO rowObject) {
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<QueryPagerConsultaReceituarioItemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, QueryPagerConsultaReceituarioItemDTO modelObject) throws ValidacaoException, DAOException {
                        if (dlgSelecionaEmpresa == null) {
                            addModal(target, dlgSelecionaEmpresa = new DlgSelecionaEmpresa(newModalId(), bundle("selecioneFarmaciaSaida")) {

                                @Override
                                public void onConfirmar(AjaxRequestTarget target, Receituario receituario, Empresa empresa) throws ValidacaoException, DAOException {
                                    if (RepositoryComponentDefault.SIM.equals(validaSaldo)) {
                                        setResponsePage(new DispensacaoReceituarioPage(receituario, empresa));
                                    } else {
                                        setResponsePage(new DispensacaoPrescricaoAtendimentoPage(receituario, empresa));
                                    }
                                }
                            });
                        }
                        dlgSelecionaEmpresa.show(target, modelObject.getReceituarioItem().getReceituario());
                    }
                }).setTitleBundleKey("carregarReceituario")
                        .setIcon(Icon.ROUND_CHECKMARK);
            }
        };
    }

    private IPagerProvider<QueryPagerConsultaReceituarioItemDTO, QueryPagerConsultaReceituarioItemDTOparam> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new QueryPagerProvider<QueryPagerConsultaReceituarioItemDTO, QueryPagerConsultaReceituarioItemDTOparam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<QueryPagerConsultaReceituarioItemDTOparam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(DispensacaoMedicamentoFacade.class).consultarReceituarioItemPager(dataPaging);
                }

                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_RECEITUARIO), false);
                }

                @Override
                public void customizeParam(QueryPagerConsultaReceituarioItemDTOparam param) {
                    param.setPropSort(getSort().getProperty());
                    param.setAscending(getSort().isAscending());
                }
            };
        }

        return this.dataProvider;
    }

    private QueryPagerConsultaReceituarioItemDTOparam getParam() {
        QueryPagerConsultaReceituarioItemDTOparam param = form.getModel().getObject();

        param.setEmpresaDispensacao(empresaDispensacao);
        param.setPermissaoVisualizarApenasPrescricaoEstabelecimento(permissaoVisualizarApenasPrescricaoEstabelecimento);
        param.getConfigureParam().addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        param.getConfigureParam().addProperty(VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO));

        return param;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("dispensacaoPrescricao");
    }
}
