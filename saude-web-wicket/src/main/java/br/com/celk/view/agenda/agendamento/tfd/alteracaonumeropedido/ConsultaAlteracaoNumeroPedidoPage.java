package br.com.celk.view.agenda.agendamento.tfd.alteracaonumeropedido;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaAlteracaoNumeroPedidoPage extends BasePage {

    private Form form;

    private String numeroPedido;
    private String nomePaciente;
    private TipoProcedimento tipoProcedimento;
    private DatePeriod periodo;
    private PnlChoicePeriod pnlChoicePeriod;

    private PageableTable pageableTable;

    public ConsultaAlteracaoNumeroPedidoPage() {
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField<String>("numeroPedido"));
        form.add(new InputField<String>("nomePaciente"));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true).setTfd(true));
        form.add(pnlChoicePeriod = new PnlChoicePeriod("periodo"));
        pnlChoicePeriod.setDefaultOutro();

        form.add(pageableTable = new PageableTable("table", getColumns(), getPagerProvider()));
        
        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                return ConsultaAlteracaoNumeroPedidoPage.this.getParam();
            }
        });

        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(LaudoTfd.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("numeroPedido"), VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoProcedimento"), VOUtils.montarPath(LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataCadastro"), VOUtils.montarPath(LaudoTfd.PROP_DATA_CADASTRO)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<LaudoTfd>() {
            @Override
            public void customizeColumn(final LaudoTfd rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LaudoTfd>() {
                    @Override
                    public void action(AjaxRequestTarget target, LaudoTfd modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroAlteracaoNumeroPedidoPage(modelObject));
                    }
                }).setTitleBundleKey("alterarPedido");
            }
        };
    }

    public IPagerProvider getPagerProvider() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LaudoTfd.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LaudoTfd.class).getProperties(),
                        new HQLProperties(PedidoTfd.class, LaudoTfd.PROP_PEDIDO_TFD).getProperties(),
                        new String[]{
                        VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
                        VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                        VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                        VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                });
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(LaudoTfd.PROP_DATA_CADASTRO, false);
            }
        };
    }

    public List<BuilderQueryCustom.QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO), numeroPedido));
        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, this.nomePaciente))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_TIPO_PROCEDIMENTO, tipoProcedimento));
        parameters.add(new QueryCustom.QueryCustomParameter(LaudoTfd.PROP_DATA_CADASTRO, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO), BuilderQueryCustom.QueryParameter.IS_NOT_NULL));

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaAlteracaoNumeroPedido");
    }
}
