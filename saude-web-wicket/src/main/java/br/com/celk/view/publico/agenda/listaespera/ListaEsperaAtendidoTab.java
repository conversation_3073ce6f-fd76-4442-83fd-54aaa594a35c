package br.com.celk.view.publico.agenda.listaespera;

import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.publico.agenda.listaespera.customcolumn.PacienteColumnPanel;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ListaEsperaAtendidoTab extends TabPanel<AgendamentoListaEsperaDTOParam> {

    private Form<AgendamentoListaEsperaDTOParam> form;
    private DropDown<TipoProcedimento> dropDownTipoProcedimento;

    private Table<AgendamentoListaEsperaDTO> tblAgendamentoListaEspera;
    private List<AgendamentoListaEsperaDTO> lstAgendamentoListaEsperaDTO;
    private Label lblDataHoraConsulta;
    private String dataHora;

    private DateChooser dataInicialChooser;
    private DateChooser dataFinalChooser;

    public ListaEsperaAtendidoTab(String id, AgendamentoListaEsperaDTOParam param) {
        super(id, param);
        init();
    }

    private void init() {
        AgendamentoListaEsperaDTOParam proxy = on(AgendamentoListaEsperaDTOParam.class);

        getForm().add(dataInicialChooser = new DateChooser(path(proxy.getDataInicial())));
        getForm().add(dataFinalChooser = new DateChooser(path(proxy.getDataFinal())));

        getForm().add(dropDownTipoProcedimento = getDropDownTipoProcedimento(path(proxy.getTipoProcedimento())));

        getForm().add(tblAgendamentoListaEspera = new Table("tblAgendamentoListaEspera", getColumns(), getCollectionProvider()));
        dataHora = "";
        getForm().add(lblDataHoraConsulta = new Label("dataHora", new PropertyModel(this, "dataHora")));
        lblDataHoraConsulta.setOutputMarkupId(true);

        getForm().add(new ProcurarButton<AgendamentoListaEsperaDTOParam>("btnProcurar", tblAgendamentoListaEspera) {
            @Override
            public AgendamentoListaEsperaDTOParam getParam() {
                AgendamentoListaEsperaDTOParam param = ListaEsperaAtendidoTab.this.object;
                param.setOrderBy(AgendamentoListaEsperaDTOParam.OrderBy.PRIORIDADE);
                return param;
            }

            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                atualizarLbl(target);
            }
        });

        add(getForm());

    }

    private Form<AgendamentoListaEsperaDTOParam> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(this.object));
        }
        return this.form;
    }

    private DropDown getDropDownTipoProcedimento(String id) {
        if (dropDownTipoProcedimento == null) {
            dropDownTipoProcedimento = new DropDown(id);
            dropDownTipoProcedimento.addAjaxUpdateValue();
            try {
                List<TipoProcedimento> lstTipoProcedimentoListaEsperaPublica = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarTipoProcedimentoListaEsperaPublica();
                if (CollectionUtils.isNotNullEmpty(lstTipoProcedimentoListaEsperaPublica)) {
                    for (TipoProcedimento tipoProcedimento : lstTipoProcedimentoListaEsperaPublica) {
                        dropDownTipoProcedimento.addChoice(tipoProcedimento, tipoProcedimento.getDescricao());
                    }

                    if (CollectionUtils.isNotNullEmpty(lstTipoProcedimentoListaEsperaPublica)) {
                        TipoProcedimento tipoProcedimento = lstTipoProcedimentoListaEsperaPublica.get(0);
                        this.getForm().getModelObject().setTipoProcedimento(tipoProcedimento);
                    }
                }
            } catch (DAOException | ValidacaoException ex) {
                Loggable.log.error(ex.getMessage(), ex);
                error(ex.getMessage());
            }
        }
        return dropDownTipoProcedimento;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        AgendamentoListaEsperaDTO proxy = on(AgendamentoListaEsperaDTO.class);

        columns.add(getPacienteActionColumn());
        columns.add(createColumn(bundle("cns"), proxy.getCnsFormatadoPrefixoOculto()));
        columns.add(createColumn(bundle("dtNascimento"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento()));
        columns.add(new DateColumn(bundle("dtConfirmacao"), path(proxy.getAgendaGradeAtendimentoHorario().getDataConfirmacao())));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoAtendidoFormatado()));

        return columns;
    }

    private IColumn getPacienteActionColumn() {
        return new CustomColumn<AgendamentoListaEsperaDTO>(bundle("paciente")) {
            @Override
            public Component getComponent(String componentId, AgendamentoListaEsperaDTO rowObject) {
                return new PacienteColumnPanel(componentId, rowObject);
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                carregar();
                return lstAgendamentoListaEsperaDTO;
            }
        };
    }

    private void carregar() throws DAOException, ValidacaoException {
        if (this.object.getTipoProcedimento() == null && (this.object.getDataInicial() == null || this.object.getDataFinal() == null)) {
            warn(bundle("filtrosObrigatorios"));
            return;
        }

        Long totalRegistros = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentoListaEsperaAtendidoCount(this.object);

        if (totalRegistros > 500) {
            warn("Sua pesquisa retornou mais de 500 registros, por favor, selecione um intervalo de tempo menor.");
            return;
        }

        lstAgendamentoListaEsperaDTO = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentoListaEsperaAtendido(this.object);
    }

    private void atualizarLbl(AjaxRequestTarget target) {
        dataHora = bundle("msgPesquisaListaEsperaPublica", new SimpleDateFormat("dd/MM/yyyy").format(DataUtil.getDataAtual()), new SimpleDateFormat("HH:mm").format(DataUtil.getDataAtual()));
        target.add(lblDataHoraConsulta);
    }

    public String getTitle() {
        return BundleManager.getString("listaAtendidos");
    }

}
