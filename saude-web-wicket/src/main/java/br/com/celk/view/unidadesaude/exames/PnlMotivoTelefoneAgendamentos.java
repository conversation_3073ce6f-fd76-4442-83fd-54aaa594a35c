package br.com.celk.view.unidadesaude.exames;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.telefonefield.DisabledTelefoneField;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoTelefoneAgendamentos extends Panel {

    private AbstractAjaxButton btnFechar;
    private InputField<String> txtMotivo;
    private DisabledInputField txtNome;
    private Label lblCelulares;

    private String motivo;
    private String nomeUsuarioCadsus;
    private String celular;
    private String telefone;
    private String telefone2;
    private String telefone3;
    private String telefone4;
    private TelefoneField txtTelefone;
    private TelefoneField txtTelefone2;
    private InputField txtTelefone3;
    private InputField txtTelefone4;
    private TelefoneField txtCelular;


    public PnlMotivoTelefoneAgendamentos(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtNome = new DisabledInputField("nomeUsuarioCadsus"));
        form.add(txtTelefone = new DisabledTelefoneField("telefone"));
        form.add(txtTelefone2 = new DisabledTelefoneField("telefone2"));
        form.add(txtTelefone3 = new DisabledInputField("telefone3"));
        form.add(txtTelefone4 = new DisabledInputField("telefone4"));
        form.add(txtCelular = new DisabledTelefoneField("celular"));
//        form.add(lblCelulares = new Label("lblContatos"));
//        lblCelulares.setOutputMarkupPlaceholderTag(true);

        form.add(txtMotivo = new RequiredInputField<String>("motivo"));


        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, motivo);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException;

    public void setDto(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO dto) {
        limpar(target);
        if(dto.getAgendaGradeAtendimentoHorario() != null && dto.getAgendaGradeAtendimentoHorario().getUsuarioCadsus() != null){
            UsuarioCadsus vo = (UsuarioCadsus) LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_NOME)
                    .addProperty(UsuarioCadsus.PROP_APELIDO)
                    .addProperty(UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL)
                    .addProperty(UsuarioCadsus.PROP_CELULAR)
                    .addProperty(UsuarioCadsus.PROP_TELEFONE)
                    .addProperty(UsuarioCadsus.PROP_TELEFONE2)
                    .addProperty(UsuarioCadsus.PROP_TELEFONE3)
                    .addProperty(UsuarioCadsus.PROP_TELEFONE4)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, dto.getAgendaGradeAtendimentoHorario().getUsuarioCadsus().getCodigo()))
                    .start().getVO();

            nomeUsuarioCadsus = vo.getNomeSocial();

            celular = vo.getCelularFormatado();
            telefone = vo.getTelefoneFormatado();
            telefone2 = vo.getTelefone2Formatado();
            telefone3 = vo.getTelefone3Formatado();
            telefone4 = vo.getTelefone4Formatado();

//            celularesUsuarioCadsus = BundleManager.getString("telefonesContato") + ": "+ vo.getTelefonesCelularFormatado();

//            lblCelulares.setDefaultModel(new Model<Serializable>(vo.getTelefonesCelularFormatado()));

            target.add(txtNome);
//            target.add(lblCelulares);
            target.add(txtMotivo);
            target.add(txtTelefone);
            target.add(txtTelefone2);
            target.add(txtTelefone3);
            target.add(txtTelefone4);
            target.add(txtCelular);

        }
    }

    public void limpar(AjaxRequestTarget target) {
        this.txtMotivo.limpar(target);
//        this.lblCelulares.setDefaultModel(new Model<String>());
        this.txtNome.limpar(target);
    }
    
}
