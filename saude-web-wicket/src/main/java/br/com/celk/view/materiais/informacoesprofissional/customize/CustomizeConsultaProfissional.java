package br.com.celk.view.materiais.informacoesprofissional.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaProfissional extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("referencia"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_REFERENCIA), QueryParameter.IGUAL));
        filterProperties.put(BundleManager.getString("nome"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_NOME), QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("numeroRegistro"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_NUMERO_REGISTRO), QueryParameter.IGUAL));
        filterProperties.put(BundleManager.getString("codigoCns"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Profissional.PROP_CODIGO_CNS), QueryParameter.IGUAL));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("nome"), VOUtils.montarPath(Profissional.PROP_NOME));
        properties.put(BundleManager.getString("referencia"), VOUtils.montarPath(Profissional.PROP_REFERENCIA));
        properties.put(BundleManager.getString("numeroRegistro"), VOUtils.montarPath(Profissional.PROP_NUMERO_REGISTRO));
        properties.put(BundleManager.getString("codigoCns"),VOUtils.montarPath(Profissional.PROP_CODIGO_CNS));

    }

    @Override
    public Class getClassConsulta() {
        return Profissional.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(Profissional.class).getProperties());
    }

}
