package br.com.celk.view.agenda.agendamento;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.ValidacoesAgendamentoBehavior;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IHtmlReportAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgObservacao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.report.HtmlReport;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.AgruparAgendaPorEmpresa;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.DefinirTelaAgendamentoListaEspera;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.FiltrarAgendaGradePorPrioridade;
import br.com.celk.view.agenda.agendamento.agendamentoListaEspera.ParamsRegraTelaSolicitacaoAgendamento;
import br.com.celk.view.agenda.agendamento.dialog.*;
import br.com.celk.view.agenda.agendamento.validacao.ValidacaoAgendamentoListaEspera;
import br.com.celk.view.agenda.solicitacao.DetalhesSolicitacaoPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.prontuario.tablecolumn.ClassificacaoRiscoColumnT;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.celk.view.unidadesaude.processos.regulacaosolicitacao.dialog.DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.*;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.PacienteSemCadastroDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.*;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.programasaude.interfaces.RelatorioCadastroMamografiaDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.programasaude.Mamografia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import ch.lambdaj.group.Group;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;
import org.hamcrest.Matchers;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.vo.programasaude.base.BaseMamografia.PROP_ATENDIMENTO;
import static ch.lambdaj.Lambda.*;

/**
 * <AUTHOR>
 */
@Private
public class AgendamentoListaEsperaPage extends BasePage {

    private Form<AgendamentoListaEsperaDTOParam> form;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private AutoCompleteConsultaExameProcedimento autoCompleteConsultaExameProcedimento;
    private DropDown<Long> dropDownTipoConsulta;
    private DropDown<Long> dropDownPrioridade;
    private DropDown<Long> dropDownTipoEstabelecimento;
    private PageableTable<AgendamentoListaEsperaDTO> tblAgendamentoListaEspera;
    private AgendamentoListaEsperaDTOParam param = new AgendamentoListaEsperaDTOParam();
    private DlgMotivoCancelamentoSolicitacaoAgendamento dlgMotivoCancelamentoSolicitacaoAgendamento;
    private DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento dlgDevolucao;
    private DlgLancarOcorrenciaContatoListaEspera dlgOcorrencia;
    private DlgSelecionarEmpresaAgenda dlgSelecionarEmpresaAgenda;
    private DlgContatoAgendamentoListaEspera dlgContato;

    private InputField inputCodigoPaciente;
    private InputField inputNumeracaoAuxiliar;
    private InputField inputCodigoSolicitacao;

    private IPagerProvider pagerProvider;
    private PageParameters pageParameters;
    private final boolean procurar;

    private Boolean isPermissaoVisualizarTipoProcAgend;
    private Boolean isPermissaoTipoProcedimento;
    private Boolean isPermissaoEmpresa;
    private boolean permissaoSolicitarUrgencia;

    private DlgSelecionarTipoAgendamento dlgSelecionarTipoAgendamento;
    private DlgObservacao dlgObservacao;
    private String parametro;
    private DlgConfirmacaoSimNao<SolicitacaoAgendamento> dlgDesbloquear;
    private DlgMotivoBloqueioFilaEspera dlgMotivoBloqueio;
    private String tipoControleRegulacao;
    private WebMarkupContainer containerSolicitarPrioridade;
    private List<ConfiguracaoOrdenacaoAgendamentoListaEspera> configuracaoOrdenacao;
    private Boolean exibeObservacao;
    private DlgConfirmacaoSimNao<AgendamentoListaEsperaDTO> dlgQuestionaPacienteUnidadeDiferente;

    private WebMarkupContainer containerVisualizacaoAtendimentos;
    private String exibirPainelClassificacaoRiscoConsultaAtendimentos;
    private List<SolicitacaoAgendamentoExame> exames;
    private AjaxPreviewBlank ajaxPreviewBlank;

    private ExameProcedimento exameProcedimentoBusca;

    private DlgSelecionarEmpresaAgendaRecepcao dlgSelecionarEmpresaAgendaRecepcao;

    public AgendamentoListaEsperaPage() {
        this(new PageParameters(), false);
    }

    public AgendamentoListaEsperaPage(PageParameters pageParameters) {
        this(pageParameters, true);
    }

    public AgendamentoListaEsperaPage(PageParameters pageParameters, boolean procurar)  {
        super(pageParameters);
        this.pageParameters = pageParameters;
        this.procurar = procurar;
        init();
    }

    public AgendamentoListaEsperaPage(AgendamentoListaEsperaDTOParam param, PageParameters pageParameters) {
        super();
        this.param = param;
        this.pageParameters = pageParameters;
        this.procurar = true;
        init();
    }

    private void init() {
        AgendamentoListaEsperaDTOParam proxy = on(AgendamentoListaEsperaDTOParam.class);
        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        permissaoSolicitarUrgencia = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.SOLICITAR_URGENCIA_BREVIDADE);
        getForm().add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true));
        autoCompleteConsultaTipoProcedimento.setTfd(false);
        getForm().add(new AutoCompleteConsultaExameProcedimento("exameProcedimento"));
        isPermissaoVisualizarTipoProcAgend = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.VISUALIZAR_TIPOS_PROCEDIMENTO_AGENDAMENTO);
        if (!isPermissaoVisualizarTipoProcAgend) {
            isPermissaoTipoProcedimento = true;
            isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        } else {
            isPermissaoTipoProcedimento = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.HABILITACAO_TIPO_PROCEDIMENTO);
            if (!isPermissaoTipoProcedimento) {
                isPermissaoEmpresa = true;
            } else {
                isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
            }
        }
        getForm().add(new AutoCompleteConsultaProfissional("profissionalDesejado"));
        param.setPermissaoTipoProcedimento(isPermissaoTipoProcedimento);
        param.setPermissaoVisualizarTipoProcAgend(isPermissaoVisualizarTipoProcAgend);
        try {
            param.setLstEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(SessaoAplicacaoImp.getInstance().getUsuario())));
        } catch (ValidacaoException | DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        getForm().add(new AutoCompleteConsultaEmpresa("origemSolicitacao"));
        getForm().add(new AutoCompleteConsultaEmpresa("unidadeResponsavel"));
        getForm().add(new InputField("cns"));
        getForm().add(new InputField("paciente"));
        getForm().add(inputCodigoSolicitacao = new InputField("codigoSolicitacao"));
        inputCodigoSolicitacao.addAjaxUpdateValue();
        inputCodigoSolicitacao.setOutputMarkupId(true);
        getForm().add(inputCodigoPaciente = new InputField("codigoPaciente"));
        inputCodigoPaciente.addAjaxUpdateValue();
        inputCodigoPaciente.setOutputMarkupPlaceholderTag(true);
        getForm().add(inputNumeracaoAuxiliar = new InputField("numeracaoAuxiliar"));
        inputNumeracaoAuxiliar.addAjaxUpdateValue();
        inputNumeracaoAuxiliar.setOutputMarkupId(true);
        getForm().add(new DateChooser("dataNascimento"));
        getForm().add(getDropDownTipoConsulta("tipoConsulta"));
        getForm().add(getDropDownPrioridade("prioridade"));
        getForm().add(DropDownUtil.getNaoSimLongDropDown("apenasReagendamento"));
        getForm().add(getDropDownTipoEstabelecimento("tipoEstabelecimento"));
        getForm().add(new PnlDatePeriod(path(proxy.getPeriodo())));

        containerSolicitarPrioridade = new WebMarkupContainer("containerSolicitarPrioridade");
        containerSolicitarPrioridade.setOutputMarkupId(true);
        containerSolicitarPrioridade.setOutputMarkupPlaceholderTag(true);
        containerSolicitarPrioridade.add(DropDownUtil.getNaoSimLongDropDown("apenasBloqueada"));
        containerSolicitarPrioridade.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()));

        getForm().add(containerSolicitarPrioridade);
        getForm().add(tblAgendamentoListaEspera = new PageableTable("tblAgendamentoListaEspera", getColumns(), getPagerProvider()));

        ProcurarButton<AgendamentoListaEsperaDTOParam> procurarButton;
        getForm().add(procurarButton = new ProcurarButton<AgendamentoListaEsperaDTOParam>("btnProcurar", tblAgendamentoListaEspera) {
            @Override
            public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException {
                executarAntesProcurar();
            }

            @Override
            public AgendamentoListaEsperaDTOParam getParam() {
                if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao())) {
                    param.setSituacaoList(Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE, SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO, SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO));
                } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao())) {
                    param.setSituacaoList(Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA));
                } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
                    param.setSituacaoList(Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA));
                }
                List<ConfiguracaoOrdenacaoAgendamentoListaEspera> configuracoes = getConfiguracaoOrdenacao();
                if (CollectionUtils.isNotNullEmpty(configuracoes)) {
                    param.setConfigureParam(new DTOParamConfigureDefault());
                    for (ConfiguracaoOrdenacaoAgendamentoListaEspera configuracao : configuracoes) {
                        if (ConfiguracaoOrdenacaoAgendamentoListaEspera.CodigoColuna.OBSERVACAO.value().equals(configuracao.getCodigoColuna()) && !exibeObservacao())
                            continue;
                        else if (ConfiguracaoOrdenacaoAgendamentoListaEspera.CodigoColuna.IDADE.value().equals(configuracao.getCodigoColuna())) {
                            param.getConfigureParam().addSorter(configuracao.getPathSorterCodigoColuna(),
                                ConfiguracaoOrdenacaoAgendamentoListaEspera.Tipo.CRESCENTE.value().equals(configuracao.getTipo())
                                    ? ConfiguracaoOrdenacaoAgendamentoListaEspera.Tipo.DECRESCENTE.getSorter()
                                    : ConfiguracaoOrdenacaoAgendamentoListaEspera.Tipo.CRESCENTE.getSorter());
                        } else {
                            param.getConfigureParam().addSorter(configuracao.getPathSorterCodigoColuna(), configuracao.getTipoOrdenacao());
                        }
                    }
                }
                param.setTipoEstabelecimento(dropDownTipoEstabelecimento.getComponentValue());

                if (param.getExameProcedimento() != null && param.getTipoProcedimento() == null) {
                    ExameProcedimento ep = LoadManager.getInstance(ExameProcedimento.class)
                            .addProperty(ExameProcedimento.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(ExameProcedimento.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(ExameProcedimento.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(ExameProcedimento.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO))
                            .addParameter(new QueryCustom.QueryCustomParameter(ExameProcedimento.PROP_CODIGO, param.getExameProcedimento().getCodigo()))
                            .start().getVO();
                    if (ep != null) {
                        param.setTipoProcedimento(ep.getTipoExame().getTipoProcedimento());
                    }
                }
                Usuario usuarioLogado = ApplicationSession.get().getSession().getUsuario();
                param.setUsuarioLogado(usuarioLogado);
                addFiltersParameters(false);
                return param;
            }

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ((SingleSortState) pagerProvider.getSortState()).setSort(null);
                super.onAction(target, form);
            }
        });

        if (procurar && this.pageParameters.getAllNamed().size() > 0 &&!this.pageParameters.getAllNamed().get(0).getKey().equals("cdPrg")) {
            procurarButton.procurar();
        }
        containerVisualizacaoAtendimentos = new WebMarkupContainer("containerVisualizacaoAtendimentos");
        containerVisualizacaoAtendimentos.setOutputMarkupPlaceholderTag(true);
        form.add(containerVisualizacaoAtendimentos);
        try {
            exibirPainelClassificacaoRiscoConsultaAtendimentos = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibirPainelClassificacaoRiscoConsultaAtendimentos");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        containerVisualizacaoAtendimentos.setVisible(RepositoryComponentDefault.SIM.equals(exibirPainelClassificacaoRiscoConsultaAtendimentos));

        getForm().add(new AbstractAjaxButton("btnNovaSolicitacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroSolicitacaoAgendamentoPanel(pageParameters, true, AgendamentoListaEsperaPage.class, permissaoSolicitarUrgencia));
            }
        });

        form.add(new AbstractAjaxButton("btnConfiguracoes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                setResponsePage(new ConfiguracaoAgendamentoListaEsperaPage());
            }
        }.setVisible(isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.CONFIGURAR, AgendamentoListaEsperaPage.class)));

        add(getForm());

        addModal(dlgMotivoCancelamentoSolicitacaoAgendamento = new DlgMotivoCancelamentoSolicitacaoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(solicitacaoAgendamento.getCodigo(), motivo, true);
                ValidaSolicitacaoAgendamentoPPIUtil.validaSolicitacaoAgendamentoValidaPPICriaUpdate(solicitacaoAgendamento,solicitacaoAgendamento.getCodigo(),ValidaSolicitacaoAgendamentoPPIUtil.TipoDeValidacao.DELETE,null,null,null);
                tblAgendamentoListaEspera.update(target);
            }
        });

        addModal(dlgQuestionaPacienteUnidadeDiferente = new DlgConfirmacaoSimNao<AgendamentoListaEsperaDTO>(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                AgendamentoListaEsperaDTO agendaGradeAtendimentoDTO = getObject();
                validaAgendamentoExternoSolicitacaoPanel(target, agendaGradeAtendimentoDTO);
            }
        });

        addModal(dlgDevolucao = new DlgConfirmarDevolucaoRegulacaoSolicitacaoAgendamento(newModalId(), BundleManager.getString("confirmacaoDevolucao")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Long codigoSolicitacao, String observacaoAutorizador) throws DAOException, ValidacaoException {
                BOFactoryWicket.getBO(AgendamentoFacade.class).devolverSolicitacaoAgendamento(codigoSolicitacao, observacaoAutorizador, null);
                tblAgendamentoListaEspera.update(target);
            }
        });
    }

    private void executarAntesProcurar() throws ValidacaoException {
        if(
            (param.getTipoProcedimento() == null) &&
                (Coalesce.asString(param.getPaciente()).isEmpty()) &&
                (param.getDataNascimento() == null) &&
                (param.getCodigoPaciente() == null) &&
                (param.getExameProcedimento() == null) &&
                (param.getCodigoSolicitacao() == null) &&
                (param.getOrigemSolicitacao() == null) &&
                (param.getCns() == null) &&
                (param.getEmpresas() == null || param.getEmpresas().size() > 1)
        ){
            throw new ValidacaoException(bundle("filtrosObrigatoriosTela346"));
        }
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoProcedimento.getTxtDescricao().getTextField();
    }

    private Form<AgendamentoListaEsperaDTOParam> getForm() {
        if (this.form == null) {
            setFiltersParameters();
            this.form = new Form("form", new CompoundPropertyModel(param));
        }
        return this.form;
    }

    private void setFiltersParameters() {
        param = new AdicionarFiltrosAgendamentoListaEspera(this.getPageParameters()).addFiltersAgendamentoListaEsperaDTOParam(param);
    }

    private void addFiltersParameters(boolean deveManterFiltro) {
        this.pageParameters = new AdicionarFiltrosAgendamentoListaEspera(this.getPageParameters()).deveManterFiltro(deveManterFiltro)
            .addFiltersPageParameters(param);
    }

    private DropDown getDropDownPrioridade(String id) {
        if (dropDownPrioridade == null) {
            dropDownPrioridade = new DropDown<>(id);
            String tipoControleRegulacao = "";
            Long habilitaClassificacaoRiscoEncaminhamentoEspecialista = 0L;
            String habilitaClassificacaoRiscoExame = "N";
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
                habilitaClassificacaoRiscoEncaminhamentoEspecialista = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("habilitaClassificaçãoRiscoEncaminhamentoEspecialista");
                habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }

            if (RepositoryComponentDefault.SIM_LONG.equals(habilitaClassificacaoRiscoEncaminhamentoEspecialista) ||
                    RepositoryComponentDefault.SIM.equals(habilitaClassificacaoRiscoExame)) {
                dropDownPrioridade.addChoice(null, bundle("todas"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_URGENTE, BundleManager.getString("rotulo_emergencia"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_BREVIDADE, BundleManager.getString("rotulo_muito_urgente"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_ELETIVO, BundleManager.getString("rotulo_urgente"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_POUCO_URGENTE, BundleManager.getString("pouco_urgente"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_NAO_URGENTE, BundleManager.getString("rotulo_nao_urgente"));
                dropDownPrioridade.addChoice(SolicitacaoAgendamento.SEM_PRIORIDADE, BundleManager.getString("rotulo_nao_classificado"));
            } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(tipoControleRegulacao)
                || RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(tipoControleRegulacao)) {
                    dropDownPrioridade.addChoice(null, bundle("todas"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_BREVIDADE, bundle("brevidade"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_ELETIVO, bundle("eletivo"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_URGENTE, bundle("urgente"));
            } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(tipoControleRegulacao)) {
                    dropDownPrioridade.addChoice(null, bundle("todas"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.PRIORIDADE_BREVIDADE, bundle("atendida"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.STATUS_BLOQUEADO, bundle("bloqueada"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.STATUS_DEVOLVIDO, bundle("devolvida"));
                    dropDownPrioridade.addChoice(SolicitacaoAgendamento.STATUS_AGENDADO, bundle("solicitada"));
            } else {
                dropDownPrioridade.addChoice(null, bundle("todas"));
            }
        }
        return dropDownPrioridade;
    }

    private DropDown getDropDownTipoConsulta(String id) {
        if (dropDownTipoConsulta == null) {
            dropDownTipoConsulta = new DropDown<Long>(id);
            dropDownTipoConsulta.addChoice(null, bundle("ambos"));
            dropDownTipoConsulta.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL, bundle("primeiraVezNumerico"));
            dropDownTipoConsulta.addChoice(SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO, bundle("retorno"));
        }
        return dropDownTipoConsulta;
    }

    private DropDown getDropDownTipoEstabelecimento(String id) {
        if(dropDownTipoEstabelecimento == null) {
            dropDownTipoEstabelecimento = new DropDown<>(id);
            dropDownTipoEstabelecimento.addChoice(null, bundle("todos"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.UNIDADE.value(), bundle("unidade"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.PRESTADOR_SERVICO.value(), bundle("prestadorServico"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.SECRETARIA_SAUDE.value(), bundle("secretariaSaude"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.CONSORCIO.value(), bundle("consorcio"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.CONSORCIADO.value(), bundle("consorciado"));
            dropDownTipoEstabelecimento.addChoice(TipoEstabelecimento.UNIDADE_FILANTROPICA.value(), bundle("unidadeFilantropica"));
        }

        return dropDownTipoEstabelecimento;
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendamentoListaEsperaDTO proxy = on(AgendamentoListaEsperaDTO.class);
        columns.add(getActionColumn());
        List<ConfiguracaoConsultaAgendamentoListaEspera> configuracoes = consultarConfiguracoes();
        if (CollectionUtils.isNotNullEmpty(configuracoes)) {
            ColumnFactory columnFactory = new ColumnFactory(AgendamentoListaEsperaDTO.class);
            for (ConfiguracaoConsultaAgendamentoListaEspera configuracao : configuracoes) {

                if (ConfiguracaoConsultaAgendamentoListaEspera.CodigoColuna.OBSERVACAO.value().equals(configuracao.getCodigoColuna())) {
                    if (exibeObservacao()) {
                        columns.add(getActionColumnObservacao());
                    }
                } else if (ConfiguracaoConsultaAgendamentoListaEspera.CodigoColuna.CLASSIFICACAO_RISCO.value().equals(configuracao.getCodigoColuna())) {
                    columns.add(new ClassificacaoRiscoColumnT<AgendamentoListaEsperaDTO>("CR", path(proxy.getSolicitacaoAgendamento().getClassificacaoRisco().getNivelGravidade())) {
                        @Override
                        public ClassificacaoRisco getClassificacaoRiscoFromObject(AgendamentoListaEsperaDTO object) {
                            return object.getSolicitacaoAgendamento().getClassificacaoRisco();
                        }
                    });
                } else {
                    if (configuracao.getPathSorterCodigoColuna() == null || configuracao.getPathSorterCodigoColuna().isEmpty() ) {
                        columns.add(columnFactory.createColumn(bundle(configuracao.getBundleCodigoColuna()), configuracao.getPathCodigoColuna()));
                    } else {
                        columns.add(columnFactory.createSortableColumn(bundle(configuracao.getBundleCodigoColuna()), configuracao.getPathSorterCodigoColuna(), configuracao.getPathCodigoColuna()));
                    }
                }
            }
        } else {
            addColunasDefault(columns, proxy);
        }
        return columns;
    }

    private void addColunasDefault(List<IColumn> columns, AgendamentoListaEsperaDTO proxy) {
        columns.add(new ClassificacaoRiscoColumnT<AgendamentoListaEsperaDTO>("CR", path(proxy.getSolicitacaoAgendamento().getClassificacaoRisco().getNivelGravidade())) {
            @Override
            public ClassificacaoRisco getClassificacaoRiscoFromObject(AgendamentoListaEsperaDTO object) {
                return object.getSolicitacaoAgendamento().getClassificacaoRisco();
            }
        });
        columns.add(createSortableColumn(bundle("posicao"), proxy.getSolicitacaoAgendamento().getSolicitacaoAgendamentoPosicaoFila().getPosicaoFilaEspera()));
        columns.add(createColumn(bundle("cns"), proxy.getCns()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNome(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("sexo"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexo(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getSexoFormatado()));
        columns.add(createSortableColumn(bundle("idade"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getDataNascimento(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getIdade()));
        columns.add(createSortableColumn(bundle("unidadeResponsavel"), proxy.getSolicitacaoAgendamento().getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("tipoProcedimento"), proxy.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("prioridade"), proxy.getSolicitacaoAgendamento().getPrioridade(), proxy.getSolicitarPrioridadeFormatadoData()));
        columns.add(createSortableColumn(bundle("dtSolicitacao"), proxy.getSolicitacaoAgendamento().getDataSolicitacao()));
        columns.add(createSortableColumn(bundle("dtDesejada"), proxy.getSolicitacaoAgendamento().getDataDesejada()));
        columns.add(createSortableColumn(bundle("dtUltimoContato"), proxy.getSolicitacaoAgendamento().getDataUltimoContato()));
        columns.add(createSortableColumn(bundle("numeracaoAuxiliar"), proxy.getSolicitacaoAgendamento().getNumeracaoAuxiliar()));
        columns.add(createSortableColumn(bundle("tipoConsulta"), proxy.getSolicitacaoAgendamento().getTipoConsulta(), proxy.getSolicitacaoAgendamento().getDescricaoTipoConsulta()));
        if (exibeObservacao()) {
            columns.add(getActionColumnObservacao());
        }
    }

    private Boolean exibeObservacao() {
        if (exibeObservacao == null) {
            exibeObservacao = false;
            try {
                exibeObservacao = RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("exibeObservacaoSolicitacaoAgendamento"));
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }
        }
        return exibeObservacao;
    }

    private List<ConfiguracaoConsultaAgendamentoListaEspera> consultarConfiguracoes() {
        List<ConfiguracaoConsultaAgendamentoListaEspera> configuracoes = LoadManager.getInstance(ConfiguracaoConsultaAgendamentoListaEspera.class)
            .addSorter(new QueryCustom.QueryCustomSorter(ConfiguracaoConsultaAgendamentoListaEspera.PROP_ORDEM)).start().getList();
        if (CollectionUtils.isNotNullEmpty(configuracoes)) {
            List<ConfiguracaoConsultaAgendamentoListaEspera> configuracoesDaUnidade = select(configuracoes, having(on(ConfiguracaoConsultaAgendamentoListaEspera.class).getEmpresa(), Matchers.equalTo(SessaoAplicacaoImp.getInstance().getEmpresa())));
            if (CollectionUtils.isNotNullEmpty(configuracoesDaUnidade)) {
                configuracoes = configuracoesDaUnidade;
            } else {
                configuracoes = select(configuracoes, having(on(ConfiguracaoConsultaAgendamentoListaEspera.class).getEmpresa(), Matchers.nullValue()));
            }
        }
        return configuracoes;
    }

    private void agendarDentroRede(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> agendasGradesAtendimentos, AgendamentoListaEsperaDTO agendamentoListaEsperaDto) throws ValidacaoException, DAOException {
        SolicitacaoAgendamento solicitacaoAgendamento = agendamentoListaEsperaDto.getSolicitacaoAgendamento();
        exames = AgendamentoHelper.getListaSolicitacaoAgendamentoExame(solicitacaoAgendamento);
        agendasGradesAtendimentos = new FiltrarAgendaGradePorPrioridade().filtrar(agendasGradesAtendimentos, solicitacaoAgendamento, exames);
        agendasGradesAtendimentos = BOFactoryWicket.getBO(AgendamentoFacade.class).validarAgendasProximidadeSolicitanteExecutante(agendamentoListaEsperaDto.getSolicitacaoAgendamento().getEmpresa(), agendasGradesAtendimentos);
        ValidacaoAgendamentoListaEspera.validar(agendasGradesAtendimentos, agendamentoListaEsperaDto);
        Group<AgendaGradeAtendimentoDTO> agendasAgrupadasPorEmpresa = AgruparAgendaPorEmpresa.agrupar(agendasGradesAtendimentos);
        if (agendasAgrupadasPorEmpresa.subgroups().size() > 1) {
            abrirModalSelecionarUnidade(target, agendamentoListaEsperaDto, agendasGradesAtendimentos);
        } else {
            Empresa unidadeAgenda = agendasAgrupadasPorEmpresa.subgroups().get(0).findAll().get(0).getEmpresaAgenda();
            Page solicitacaoAgendamentoPage = this.getPageAgendamentoListaEpera(solicitacaoAgendamento, unidadeAgenda, agendasGradesAtendimentos);
            setResponsePage(solicitacaoAgendamentoPage);
        }
    }

    private void showDlgSelecionarTipoAgendamento(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> list, AgendamentoListaEsperaDTO modelObject) {
        if (dlgSelecionarTipoAgendamento == null) {
            addModal(target, dlgSelecionarTipoAgendamento = new DlgSelecionarTipoAgendamento(newModalId()) {
                @Override
                public void onDentroRede(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> list, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                    agendarDentroRede(target, list, modelObject);
                }

                @Override
                public void onForaRede(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                    setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), pageParameters){
                        @Override
                        public void voltar() {
                            setResponsePage(new AgendamentoListaEsperaPage(param, pageParameters));
                        }
                    });
                }
            });
        }
        dlgSelecionarTipoAgendamento.show(target, list, modelObject);
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendamentoListaEsperaDTO>() {
            @Override
            public void customizeColumn(AgendamentoListaEsperaDTO rowObject) {
                addAction(ActionType.AGENDAR, rowObject, new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, final AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            validarAgendamento(modelObject);

                            if (AgendamentoHelper.isCadastrarNaRecepcao(modelObject.getSolicitacaoAgendamento().getTipoProcedimento())) {
                                processoRecepcao(target, modelObject.getSolicitacaoAgendamento());
                            } else {
                                boolean verificaUnidadeDiferentePaciente = RepositoryComponentDefault.SIM_LONG.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("questionarSolicitacaoAgendamentoUnidadeDiferentePaciente"));
                                if (verificaUnidadeDiferentePaciente) {
                                    Empresa unidadeOrigemPaciente = UsuarioCadsusHelper.carregarUnidadeOrigem(modelObject.getSolicitacaoAgendamento().getUsuarioCadsus());
                                    if (unidadeOrigemPaciente != null && unidadeOrigemPaciente.getCodigo() != null) {
                                        if (RepositoryComponentDefault.SIM_LONG.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("questionarSolicitacaoAgendamentoUnidadeDiferentePaciente"))
                                                && (SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO.equals(modelObject.getSolicitacaoAgendamento().getTipoConsulta()))
                                                && (!SessaoAplicacaoImp.getInstance().getEmpresa().getCodigo().equals(unidadeOrigemPaciente.getCodigo()))) {
                                            dlgQuestionaPacienteUnidadeDiferente.setObject(modelObject);
                                            dlgQuestionaPacienteUnidadeDiferente.setMessage(target, bundle("msg_paciente_informado_pertence_outra_unidade", unidadeOrigemPaciente.getDescricao()));
                                            dlgQuestionaPacienteUnidadeDiferente.show(target);
                                        } else {
                                            validaAgendamentoExternoSolicitacaoPanel(target, modelObject);
                                        }
                                    } else {
                                        validaAgendamentoExternoSolicitacaoPanel(target, modelObject);
                                    }
                                } else {
                                    validaAgendamentoExternoSolicitacaoPanel(target, modelObject);
                                }
                            }

                        }
                    }
                ).setTitleBundleKey(
                    "agendamentoSolicitacao").setIcon(Icon.CALENDAR)
                    .setEnabled((SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        || SolicitacaoAgendamento.STATUS_AGENDADO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        || SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(rowObject.getSolicitacaoAgendamento().getStatus()))
                        // && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getSolicitarPrioridade())
                        && isActionPermitted(Permissions.ATENDER)
                        && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado())).setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()) || RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao()) || (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()) && isActionPermitted(Permissions.ATENDER)));

                addAction(ActionType.EDITAR, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(true);
                            setResponsePage(new CadastroSolicitacaoAgendamentoPanel(modelObject.getSolicitacaoAgendamento().getCodigo(), true, true, pageParameters, AgendamentoListaEsperaPage.class, permissaoSolicitarUrgencia, false));
                        }
                    }
                ).setEnabled((SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                    || (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(rowObject.getSolicitacaoAgendamento().getPrioridade())
                    || (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                    && RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagDevolvido())
                    && rowObject.getSolicitacaoAgendamento().getDataAnaliseAutorizador() != null)
                    && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getSolicitarPrioridade())))
                    && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado()) && !existeAgendamentoAberto(rowObject));

                addAction(ActionType.CANCELAR, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            dlgMotivoCancelamentoSolicitacaoAgendamento.show(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setQuestionDialogBundleKey(
                    null).setEnabled((SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                    || (SolicitacaoAgendamento.PRIORIDADE_ELETIVO.equals(rowObject.getSolicitacaoAgendamento().getPrioridade())
                    || (SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                    && RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagDevolvido())
                    && rowObject.getSolicitacaoAgendamento().getDataAnaliseAutorizador() != null)
                    && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getSolicitarPrioridade())))
                    && RepositoryComponentDefault.NAO_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado()));

                addAction(ActionType.CONSULTAR, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(true);
                            setResponsePage(new DetalhesSolicitacaoPage(modelObject.getSolicitacaoAgendamento().getCodigo()));
                        }
                    }
                );

                addAction(ActionType.CONTATO, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            viewDlgContato(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setTitleBundleKey(
                    "registrarContato").setEnabled(SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus()))
                    .setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(getTipoControleRegulacao()) ||
                            RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()));

                addAction(ActionType.OCORRENCIA, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            viewDlgOcorrencia(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setIcon(Icon.NOTEPAD).setTitleBundleKey(
                    "registrarOcorrencia").setEnabled(SolicitacaoAgendamento.STATUS_FILA_ESPERA.equals(rowObject.getSolicitacaoAgendamento().getStatus()) || SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO.equals(rowObject.getSolicitacaoAgendamento().getStatus()) || SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO.equals(rowObject.getSolicitacaoAgendamento().getStatus()))
                    .setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()));

                addAction(ActionType.SUBSTITUIR, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            setResponsePage(new CadastroRemanejamentoSolicitacaoAgendamentoPage(modelObject.getSolicitacaoAgendamento().getCodigo(), pageParameters));
                        }
                    }
                ).setTitleBundleKey(
                    "alterarTipoProcedimento").setIcon(Icon.ARROW_SWITCH)
                    .setEnabled(SolicitacaoAgendamento.STATUS_AGENDADO.equals(rowObject.getSolicitacaoAgendamento().getStatus())
                        || SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE.equals(rowObject.getSolicitacaoAgendamento().getStatus()));

                addAction(ActionType.REVERTER, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            viewDlgDesbloquear(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setTitleBundleKey("desbloquear").setIcon(Icon.ICON_UNBLOCKED).setEnabled(RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado()))
                    .setVisible(/*RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao()) && */isActionPermitted(Permissions.ESTORNAR));

                addAction(ActionType.RETORNO, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            viewDlgMotivoBloqueio(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setTitleBundleKey("motivoBloqueio").setIcon(Icon.DOC_LINES).setEnabled(RepositoryComponentDefault.SIM_LONG.equals(rowObject.getSolicitacaoAgendamento().getFlagBloqueado()));
                addAction(ActionType.RETORNO, rowObject,
                    new IModelAction<AgendamentoListaEsperaDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                            addFiltersParameters(false);
                            dlgDevolucao.show(target, modelObject.getSolicitacaoAgendamento());
                        }
                    }
                ).setTitleBundleKey("devolverSolicitacao").setIcon(Icon.RELOAD)
                    .setVisible(isActionPermitted(Permissions.RETORNO));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AgendamentoListaEsperaDTO>() {
                    @Override
                    public DataReport action(AgendamentoListaEsperaDTO modelObject) throws ReportException {
                        modelObject.setExameRequisicao(buscarExameRequisicao(modelObject));
                        return getDataReport(modelObject.getExameRequisicao());
                    }
                }).setTitleBundleKey("imprimirRequisicao").setEnabled(rowObject.getSolicitacaoAgendamento() != null && rowObject.getSolicitacaoAgendamento().getAtendimentoOrigem() != null);
            }
        };
    }

    private ExameRequisicao buscarExameRequisicao(AgendamentoListaEsperaDTO rowObject) {
        return LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(ExameRequisicao.PROP_CODIGO)
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_CONVENIO_REALIZADO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_CLASSIFICACAO))
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, rowObject.getSolicitacaoAgendamento()))
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()))
                .setMaxResults(1)
                .start()
                .getVO();
    }

    @Nullable
    private DataReport getDataReport(ExameRequisicao modelObject) throws ReportException {
        if (modelObject == null) return null;

        if(TipoExame.Classificacao.EXAMES_PADRAO.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            return getRelatorioImpressaoExame(modelObject);
        } else if (TipoExame.Classificacao.ELETROCARDIOGRAMA.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameEletrocardiogramaDTOParam param = new ImpressaoExameEletrocardiogramaDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameEletrocardiograma(param);
        } else if (TipoExame.Classificacao.HEPATITE.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameHepatiteDTOParam param = new ImpressaoExameHepatiteDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameHepatite(param);
        } else if (TipoExame.Classificacao.TELEDERMATOSCOPIA.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameEletrocardiogramaDTOParam param = new ImpressaoExameEletrocardiogramaDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).impressaoExameTeledermatoscopia(param);
        } else if (TipoExame.Classificacao.BPA_I.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            if (existeExameBpai(modelObject.getExame().getCodigo())) {
                return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoExameBpai(modelObject.getExame().getCodigo());
            } else {
                return getRelatorioImpressaoExame(modelObject);
            }

        } else if (TipoExame.Classificacao.IMUNOLOGIA.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameImunologiaDTOParam param = new ImpressaoExameImunologiaDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameImunologia(param);
        } else if (TipoExame.Classificacao.ANTI_HCV.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameAntiHcvDTOParam param = new ImpressaoExameAntiHcvDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameAntiHcv(param);
        } else if (TipoExame.Classificacao.CONTAGEM_LINFOCITOS.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoRequisicaoContagemLinfocitosDTOParam param = new ImpressaoRequisicaoContagemLinfocitosDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoRequisicaoContagemLinfocitos(param);
        } else if (TipoExame.Classificacao.DETECCAO_DNA_PRO_VIRAL_HIV_1.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoExameDeteccaoDNAProViralHIV1DTOParam param = new ImpressaoExameDeteccaoDNAProViralHIV1DTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameDeteccaoDNAProViralHIV1(param);
        }  else if (TipoExame.Classificacao.CARGA_VIRAL_HIV.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            ImpressaoRequisicaoCargaViralHIVDTOParam param = new ImpressaoRequisicaoCargaViralHIVDTOParam();
            param.setCodigoExame(modelObject.getExame().getCodigo());
            param.setAtendimento(modelObject.getExame().getAtendimento());

            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoRequisicaoCargaViralHIV(param);
        }  else if (TipoExame.Classificacao.MAMOGRAFIA.value().equals(modelObject.getExame().getTipoExame().getClassificacao())) {
            if(existeRequisicaoMamografia(modelObject.getExame().getAtendimento())) {
                RelatorioCadastroMamografiaDTOParam param = new RelatorioCadastroMamografiaDTOParam();
                param.setCodigoExame(modelObject.getExame().getCodigo());
                param.setAtendimento(modelObject.getExame().getAtendimento());
                return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioCadastroMamografia(param, false);
            }

            return getRelatorioImpressaoExame(modelObject);
        }

        return null;
    }

    private boolean existeRequisicaoMamografia(Atendimento atendimento) {
        Mamografia proxy = on(Mamografia.class);
        return LoadManager.getInstance(Mamografia.class)
                .addProperty(path(proxy.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_ATENDIMENTO), atendimento))
                .start().exists();
    }

    @Nullable
    private DataReport getRelatorioImpressaoExame(ExameRequisicao modelObject) throws ReportException {
        ImpressaoExameDTOParam param = new ImpressaoExameDTOParam();
        param.setCodigoExame(modelObject.getExame().getCodigo());
        param.setTipoConvenio(modelObject.getExame().getTipoConvenioRealizado());

        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExame(param);
    }

    private boolean existeExameBpai(Long codigoExame) {
        ExameBpai exameBpai = LoadManager.getInstance(ExameBpai.class)
                .addProperties(new HQLProperties(ExameBpai.class).getProperties())
                .addProperties(new HQLProperties(Exame.class, ExameBpai.PROP_EXAME).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameBpai.PROP_EXAME, Exame.PROP_CODIGO), codigoExame))
                .start().getVO();
        return exameBpai != null;
    }

    private void validarAgendamento(AgendamentoListaEsperaDTO agendamentoListaEsperaDTO) throws ValidacaoException {
        ValidacoesAgendamentoDTO validacoesAgendamentoDTO = new ValidacoesAgendamentoDTO();
        validacoesAgendamentoDTO.setTipoProcedimento(agendamentoListaEsperaDTO.getSolicitacaoAgendamento().getTipoProcedimento());
        validacoesAgendamentoDTO.setUsuarioCadsus(agendamentoListaEsperaDTO.getSolicitacaoAgendamento().getUsuarioCadsus());
        validacoesAgendamentoDTO.setCidadeEmpresaLogada(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());
        ValidacoesAgendamentoBehavior validacoesAgendamentoBehavior = new ValidacoesAgendamentoBehavior(validacoesAgendamentoDTO);
        String msg = validacoesAgendamentoBehavior.validacoes();
        if (msg != null && !"".equals(msg) && !validacoesAgendamentoBehavior.isAgendamentoLiberado()) {
            throw new ValidacaoException(msg);
        }
    }

    private void validaAgendamentoExternoSolicitacaoPanel(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
        if (TipoProcedimento.TIPO_PROCEDIMENTO_FORA_DA_REDE.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
            setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), pageParameters){
                @Override
                public void voltar() {
                    setResponsePage(new AgendamentoListaEsperaPage(param, pageParameters));
                }
            });
        } else {
            List<AgendaGradeAtendimentoDTO> list = BOFactoryWicket.getBO(AgendamentoFacade.class).verificarExisteVagasDisponiveisAgendaExameList(modelObject.getSolicitacaoAgendamento(), true);
            if (CollectionUtils.isNotNullEmpty(list)) {
                if (TipoProcedimento.TIPO_PROCEDIMENTO_SELECIONAR_AO_AGENDAR.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
                    showDlgSelecionarTipoAgendamento(target, list, modelObject);
                } else {
                    agendarDentroRede(target, list, modelObject);
                }
            } else if (TipoProcedimento.TIPO_PROCEDIMENTO_DENTRO_DA_REDE.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getTipoAgendamento())) {
                AgendaCota agendaCota = AgendamentoHelper.consultarAgendaCota(modelObject.getSolicitacaoAgendamento().getTipoProcedimento(), modelObject.getSolicitacaoAgendamento().getEmpresa());
                if (agendaCota != null && RepositoryComponentDefault.SIM.equals(modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getControleCota())) {
                    throw new ValidacaoException(BundleManager.getString("msgEstabelecimentoSemCotaProcedimento"));
                }
                boolean existeAgenda = LoadManager.getInstance(Agenda.class)
                    .addParameter(new QueryCustomParameter(Agenda.PROP_STATUS, Agenda.STATUS_CONFIRMADO))
                    .addParameter(new QueryCustomParameter(Agenda.PROP_TIPO_PROCEDIMENTO, modelObject.getSolicitacaoAgendamento().getTipoProcedimento()))
                    .setMaxResults(1).exists();
                if (existeAgenda) {
                    throw new ValidacaoException(bundle("msgNaoExistemVagasDisponiveisAgenda"));
                }
                throw new ValidacaoException(BundleManager.getString("naoExisteAgendaParaTipoProcedimento", modelObject.getSolicitacaoAgendamento().getTipoProcedimento().getDescricao()));
            } else {
                setResponsePage(new AgendamentoExternoSolicitacaoPanel(modelObject.getSolicitacaoAgendamento(), pageParameters) {
                    @Override
                    public void voltar() {
                        setResponsePage(new AgendamentoListaEsperaPage(param, pageParameters));
                    }
                });
            }
        }
    }

    private void viewDlgContato(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgContato == null) {
            addModal(target, dlgContato = new DlgContatoAgendamentoListaEspera(newModalId()) {
                @Override
                public void depoisConfirmarContato(AjaxRequestTarget target) {
                    tblAgendamentoListaEspera.update(target);
                }

                @Override
                public void depoisSalvarOcorrencia(AjaxRequestTarget target) {
                    tblAgendamentoListaEspera.update(target);
                }
            });
        }

        dlgContato.show(target, solicitacaoAgendamento);
    }

    private boolean existeAgendamentoAberto(AgendamentoListaEsperaDTO objeto) {
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
            .addParameter(new QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN,
                    Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_AGENDADO)))
            .addParameter(new QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, objeto.getSolicitacaoAgendamento()))
            .setMaxResults(1).exists();
    }

    private void abrirModalSelecionarUnidade(AjaxRequestTarget target, AgendamentoListaEsperaDTO dto, List<AgendaGradeAtendimentoDTO> horarios) {
        if (dlgSelecionarEmpresaAgenda == null) {
            addModal(target, dlgSelecionarEmpresaAgenda = new DlgSelecionarEmpresaAgenda(newModalId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException {
                    Page solicitacaoAgendamentoPage = getPageAgendamentoListaEpera(solicitacaoAgendamento, unidadeAgenda, horarios);
                    setResponsePage(solicitacaoAgendamentoPage);
                }
            });
        }
        dlgSelecionarEmpresaAgenda.show(target, dto.getSolicitacaoAgendamento(), horarios);
    }

    private List<SolicitacaoAgendamentoExame> getExames() {
        return this.exames;
    }

    private void viewDlgOcorrencia(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgOcorrencia == null) {
            addModal(target, dlgOcorrencia = new DlgLancarOcorrenciaContatoListaEspera(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, String ocorrencia, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
                    salvarOcorrencia(target, ocorrencia, solicitacaoAgendamento);
                }
            });
        }
        dlgOcorrencia.show(target, solicitacaoAgendamento);
    }

    private void viewDlgMotivoBloqueio(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgMotivoBloqueio == null) {
            addModal(target, dlgMotivoBloqueio = new DlgMotivoBloqueioFilaEspera(newModalId()));
        }
        dlgMotivoBloqueio.show(target, solicitacaoAgendamento);
    }

    private void salvarOcorrencia(AjaxRequestTarget target, String ocorrencia, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AgendamentoFacade.class)
            .gerarOcorrenciaSolicitacaoAgendamento(
                SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO,
                Bundle.getStringApplication("rotulo_ocorrencia_X", ocorrencia),
                solicitacaoAgendamento
            );
        info(target, Bundle.getStringApplication("rotulo_ocorrencia_gerada"));
    }

    private void viewDlgDesbloquear(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) {
        if (dlgDesbloquear == null) {
            addModal(target, dlgDesbloquear = new DlgConfirmacaoSimNao<SolicitacaoAgendamento>(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    desbloquearPaciente(target, getObject());
                }
            });
        }
        dlgDesbloquear.setObject(solicitacaoAgendamento);
        dlgDesbloquear.show(target);
        dlgDesbloquear.setMessage(target, bundle("rotuloDesejaDesbloquear"));
    }

    private void desbloquearPaciente(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
        solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
        solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
        solicitacaoAgendamento.setFlagBloqueado(RepositoryComponentDefault.NAO_LONG);
        BOFactoryWicket.save(solicitacaoAgendamento);
        info(target, Bundle.getStringApplication("rotulo_desbloqueado_sucesso"));
        setResponsePage(AgendamentoListaEsperaPage.class);
    }

    public IPagerProvider getPagerProvider() {
        if (this.pagerProvider == null) {
            this.pagerProvider = new QueryPagerProvider<AgendamentoListaEsperaDTO, AgendamentoListaEsperaDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                    executarAntesProcurar();
                    return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentoListaEspera(dataPaging);
                }

                @Override
                public void customizeParam(AgendamentoListaEsperaDTOParam param) {
                    param.setPermissaoEmpresa(isPermissaoEmpresa);
                    if (getSort() != null) {
                        DTOParamConfigureDefault configureParam = new DTOParamConfigureDefault();
                        configureParam.addSorter(getSort().getProperty(), getSort().isAscending() ? "asc" : "desc");
                        if (param.getConfigureParam().getSorter() != null) {
                            param.getConfigureParam().getSorter().remove(getSort().getProperty());
                            configureParam.getSorter().putAll(param.getConfigureParam().getSorter());
                        }
                        param.setConfigureParam(configureParam);
                    }
                }
            };
        }

        return this.pagerProvider;
    }

    private List<ConfiguracaoOrdenacaoAgendamentoListaEspera> getConfiguracaoOrdenacao() {
        if (configuracaoOrdenacao == null) {
            List<ConfiguracaoOrdenacaoAgendamentoListaEspera> configuracoes = LoadManager.getInstance(ConfiguracaoOrdenacaoAgendamentoListaEspera.class)
                .addSorter(new QueryCustom.QueryCustomSorter(ConfiguracaoOrdenacaoAgendamentoListaEspera.PROP_ORDEM)).start().getList();
            if (CollectionUtils.isNotNullEmpty(configuracoes)) {
                List<ConfiguracaoOrdenacaoAgendamentoListaEspera> configuracoesDaUnidade = select(configuracoes, having(on(ConfiguracaoOrdenacaoAgendamentoListaEspera.class).getEmpresa(), Matchers.equalTo(SessaoAplicacaoImp.getInstance().getEmpresa())));
                if (CollectionUtils.isNotNullEmpty(configuracoesDaUnidade)) {
                    configuracaoOrdenacao = configuracoesDaUnidade;
                } else {
                    configuracaoOrdenacao = select(configuracoes, having(on(ConfiguracaoOrdenacaoAgendamentoListaEspera.class).getEmpresa(), Matchers.nullValue()));
                }
            }
        }
        return configuracaoOrdenacao;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("agendamentoListaEspera");
    }

    private IColumn getActionColumnObservacao() {
        return new MultipleActionCustomColumn<AgendamentoListaEsperaDTO>("Observação") {
            @Override
            public void customizeColumn(AgendamentoListaEsperaDTO rowObject) {
                addAction(ActionType.EVOLUIR, rowObject, new IModelAction<AgendamentoListaEsperaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendamentoListaEsperaDTO modelObject) throws ValidacaoException, DAOException {
                        addModal(target, dlgObservacao = new DlgObservacao(newModalId(), bundle("observacao"), modelObject.getSolicitacaoAgendamento().getObservacao(), false, false) {

                            @Override
                            public void onConfirmar(AjaxRequestTarget target, String observacao) {}

                            @Override
                            public Long getMaxLengthObservacao() {
                                return 500L;
                            }
                        });
                        dlgObservacao.show(target);
                    }
                }).setTitleBundleKey("observacao").setEnabled(rowObject.getSolicitacaoAgendamento().getObservacao() != null);
            }
        };
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private List<AgendaGradeAtendimentoDTO> filtraPorUnidade(List<AgendaGradeAtendimentoDTO> horariosDisponiveis, Empresa unidade) {
        if (unidade == null) {
            return horariosDisponiveis;
        }
        List<AgendaGradeAtendimentoDTO> horariosFiltrados = new ArrayList<>();
        for (AgendaGradeAtendimentoDTO horario: horariosDisponiveis) {
            Empresa unidadeHorario = horario.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa();
            if (unidade.getCodigo().equals(unidadeHorario.getCodigo())) {
                horariosFiltrados.add(horario);
            }
        }
        return horariosFiltrados;
    }

    private Page getPageAgendamentoListaEpera(SolicitacaoAgendamento solicitacaoAgendamento, Empresa unidadeAgenda, List<AgendaGradeAtendimentoDTO> horarios) throws ValidacaoException {
        Long tipoAgenda = AgendamentoHelper.getTipoAgenda(solicitacaoAgendamento.getTipoProcedimento(), unidadeAgenda);
        List<AgendaGradeAtendimentoDTO> horariosFiltrados = filtraPorUnidade(horarios, unidadeAgenda);
        ParamsRegraTelaSolicitacaoAgendamento parametros = new ParamsRegraTelaSolicitacaoAgendamento()
            .setAgendamentoListaEsperaDTOParam(param)
            .setHorarios(horariosFiltrados)
            .setSolicitacaoAgendamento(solicitacaoAgendamento)
            .setUnidadeSaude(unidadeAgenda)
            .setPageParameters(pageParameters)
            .setTipoAgenda(tipoAgenda)
            .setPaginaRetorno(this);

        // todo: melhoria de desempenho apenas na classe AgendamentoSolicitacaoHorarioPanel
        Page page = new DefinirTelaAgendamentoListaEspera().cronologica(parametros);
        if (page instanceof AgendamentoSolicitacaoHorarioPanel) {
            AgendamentoSolicitacaoHorarioPanel panel = (AgendamentoSolicitacaoHorarioPanel)page;
            panel.setHorarios(horariosFiltrados);
            panel.setExames(exames);
        } else if (page instanceof AgendamentoSolicitacaoPanel) {
            AgendamentoSolicitacaoPanel panel = (AgendamentoSolicitacaoPanel)page;
            panel.setHorarios(horariosFiltrados);
            panel.setExames(exames);
        }

        return page;
    }

    private void initDlgSelecionarEmpresaAgenda(SolicitacaoAgendamento solicitacaoAgendamento, AjaxRequestTarget target, List<AgendaGradeAtendimentoGroupDTO> agendaGradeAtendimentoDTOList) {
        addModal(target, dlgSelecionarEmpresaAgendaRecepcao = new DlgSelecionarEmpresaAgendaRecepcao(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Empresa unidadeAgenda, AgendaGradeAtendimentoHorario agah, RemanejarVagaAgendaDTO remanejarVagaAgendaDTO, PacienteSemCadastroDTO pacienteSemCadastroDTO, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO, TipoProcedimento tp) throws ValidacaoException, DAOException {
                setResponsePage(verificaPaginaAgendamento(solicitacaoAgendamento, unidadeAgenda));
            }
        });
        dlgSelecionarEmpresaAgendaRecepcao.show(target, solicitacaoAgendamento.getUsuarioCadsus().getNomeSocial(), agendaGradeAtendimentoDTOList, solicitacaoAgendamento);
    }

    private void processoRecepcao(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
        param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_RETORNO));
        param.setTipoProcedimento(solicitacaoAgendamento.getTipoProcedimento());

        if (solicitacaoAgendamento.getUnidadeOrigem() != null) {
            param.setEmpresa(solicitacaoAgendamento.getUnidadeOrigem());
        } else {
            param.setEmpresa(solicitacaoAgendamento.getEmpresa());
        }

        param.setUsuarioCadsus(solicitacaoAgendamento.getUsuarioCadsus());

        //Avalia se possui agenda local para empresa logada/executante
        param.setTipoAgenda(Agenda.TIPO_AGENDA_LOCAL);
        List<AgendaGradeAtendimentoGroupDTO> agendaGradeAtendimentoDTOList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExameGroupBy(param, AgendaGradeAtendimentoDTOParam.GroupBy.EMPRESA);

        //Avalia se possui agenda Compartilhada para o tipo do atendimento e adiciona na lista.
        //A SELECAO DA EMPRESA DA AGENDA APENAS PARA AGENDAS DO TIPO COMPARTILHADA.
        param.setTipoAgenda(Agenda.TIPO_AGENDA_COMPARTILHADA);
        param.setEmpresa(null);
        agendaGradeAtendimentoDTOList.addAll(
                BOFactoryWicket.getBO(AgendamentoFacade.class)
                        .consultarVagasDisponiveisAgendaExameGroupBy(param, AgendaGradeAtendimentoDTOParam.GroupBy.EMPRESA)
        );

        if (!CollectionUtils.isNotNullEmpty(agendaGradeAtendimentoDTOList)) {
            throw new ValidacaoException(bundle("msgNaoExistemVagasDisponiveisAgenda"));
        } else if (agendaGradeAtendimentoDTOList.size() > 1) {
            initDlgSelecionarEmpresaAgenda(solicitacaoAgendamento, target, agendaGradeAtendimentoDTOList);
        } else {
            Empresa unidadeAgenda = agendaGradeAtendimentoDTOList.get(0).getEmpresa();
            setResponsePage(verificaPaginaAgendamento(solicitacaoAgendamento, unidadeAgenda));
        }
    }

    private Page verificaPaginaAgendamento(SolicitacaoAgendamento sa, Empresa unidadeAgenda) throws ValidacaoException {
        Long tipoAgenda = AgendamentoHelper.getTipoAgenda(sa.getTipoProcedimento(), unidadeAgenda);
        if (TipoProcedimento.TipoAgenda.DIARIO.value().equals(tipoAgenda)) {
            return new AgendamentoSolicitacaoPanel(sa, this.getPageClass(), getPageParameters(), unidadeAgenda);
        } else if (TipoProcedimento.TipoAgenda.HORARIO.value().equals(tipoAgenda)) {
            return new AgendamentoSolicitacaoHorarioPanel(sa, this.getPageClass(), getPageParameters(), unidadeAgenda);
        } else if (TipoProcedimento.TipoAgenda.PERSONALIZADA.value().equals(tipoAgenda)) {
            return new AgendamentoSolicitacaoPersonalizadaPanel(sa, this.getPageClass(), getPageParameters(), sa.getProcedimento(), sa.getTipoProcedimento(), unidadeAgenda);
        }

        Page page = null;
        try {
            Constructor constructor = this.getPageClass().getConstructor(PageParameters.class);
            page = (Page) constructor.newInstance(getPageParameters());
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException |
                 InstantiationException e) {
            Loggable.log.error(e);
        }

        return page;
    }

}
