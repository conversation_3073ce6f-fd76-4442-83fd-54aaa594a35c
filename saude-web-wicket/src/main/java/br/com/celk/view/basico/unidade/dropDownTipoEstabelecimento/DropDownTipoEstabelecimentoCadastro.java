package br.com.celk.view.basico.unidade.dropDownTipoEstabelecimento;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.bundle.BundleManager;

public class DropDownTipoEstabelecimentoCadastro extends AddChoiceDropDownTipoEstabelecimento {

    public DropDownTipoEstabelecimentoCadastro(String id) {
        super(id);
    }

    @Override
    public DropDown<Long> build() {
        this.dropDownTipoEstabelicimento.addChoice(null, BundleManager.getString("vazio"));
        this.addChoices();
        return dropDownTipoEstabelicimento;
    }
}
