package br.com.celk.view.unidadesaude.atendimento.consulta.consultaatendimentos.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Date;

/**
 * Created by sulivan on 09/06/17.
 */
public abstract class PnlDetalhesAtendimentosCancelados extends Panel {

    private WebMarkupContainer container;
    private CompoundPropertyModel model;
    private Long numeroAtendimento;
    private String usuarioCancelamento;
    private Date dataCancelamento;
    private String motivoCancelamento;
    private InputField txtNumeroAtendimento;
    private InputField txtUsuarioCancelamento;
    private InputField txtDataCancelamento;
    private InputArea txaMotivoCancelamento;

    public PnlDetalhesAtendimentosCancelados(String id) {
        super(id);
        init();
    }

    private void init() {
        model = new CompoundPropertyModel(this);

        container = new WebMarkupContainer("container", model);

        container.add(txtNumeroAtendimento = new DisabledInputField("numeroAtendimento"));
        container.add(txtUsuarioCancelamento = new DisabledInputField("usuarioCancelamento"));
        container.add(txtDataCancelamento = new DisabledInputField("dataCancelamento"));
        container.add(txaMotivoCancelamento = new DisabledInputArea("motivoCancelamento"));

        add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        add(container);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setModelObject(AjaxRequestTarget target, Atendimento atendimento) {
        txtNumeroAtendimento.limpar(target);
        txtUsuarioCancelamento.limpar(target);
        txtDataCancelamento.limpar(target);
        txaMotivoCancelamento.limpar(target);

        numeroAtendimento = atendimento.getCodigo();
        usuarioCancelamento = atendimento.getUsuarioCancelamento() != null ? atendimento.getUsuarioCancelamento().getNome() : "";
        dataCancelamento = atendimento.getDataCancelamento();
        motivoCancelamento = atendimento.getObservacaoCancelamento();

        target.add(txtNumeroAtendimento);
        target.add(txtUsuarioCancelamento);
        target.add(txtDataCancelamento);
        target.add(txaMotivoCancelamento);
    }

    public void update(AjaxRequestTarget target) {
        target.add(this);
    }
}
