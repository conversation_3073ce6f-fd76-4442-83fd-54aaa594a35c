package br.com.celk.view.basico.microrregiao.pnl;

import br.com.celk.component.interfaces.IComponent;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.view.basico.microrregiao.autocomplete.AutoCompleteConsultaMicrorregiao;
import br.com.celk.view.basico.microrregiao.dlg.DlgCadastroMicrorregiao;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Microrregiao;
import br.com.ksisolucoes.vo.basico.RegionalSaude;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public class PnlMicrorregiao extends FormComponentPanel<Microrregiao> implements IComponent<Microrregiao> {

    private DlgCadastroMicrorregiao dlgCadastroMicrorregiao;
    private AutoCompleteConsultaMicrorregiao autoCompleteConsultaMicrorregiao;
    private AbstractAjaxLink btnCadastrarMicrorregiao;
    private RegionalSaude regionalSaude;

    public PnlMicrorregiao(String id) {
        this(id, false, null);
    }

    public PnlMicrorregiao(String id, RegionalSaude regionalSaude) {
        this(id, false, regionalSaude);
    }

    public PnlMicrorregiao(String id, boolean required, RegionalSaude regionalSaude) {
        super(id);
        this.regionalSaude = regionalSaude;
        init(required);
    }

    public PnlMicrorregiao(String id, IModel model) {
        super(id, model);
        init(false);
    }

    private void init(boolean required) {
        setOutputMarkupId(true);

        IModel<Microrregiao> model = getModel();
        if (model == null) {
            model = new Model<Microrregiao>();
        }

        Long cdRegionalSaude = getRegionalSaude() != null && getRegionalSaude().getCodigo() != null ? getRegionalSaude().getCodigo() : null;

        add(autoCompleteConsultaMicrorregiao = new AutoCompleteConsultaMicrorregiao("autocomplete", model, required, cdRegionalSaude));
        autoCompleteConsultaMicrorregiao.setLabel(new Model(bundle("microRegiao")));

        add(btnCadastrarMicrorregiao = new AbstractAjaxLink("btnCadastrarMicrorregiao") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarMicrorregiao(target);
            }
        });
    }

    private void cadastrarMicrorregiao(AjaxRequestTarget target) {
        if (dlgCadastroMicrorregiao == null) {
            Component component = this;
            WindowUtil.addModal(target, getForm(), dlgCadastroMicrorregiao = new DlgCadastroMicrorregiao(WindowUtil.newModalId(component)));

            dlgCadastroMicrorregiao.add(new ICadastroListener<Microrregiao>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, Microrregiao object) throws DAOException, ValidacaoException {
                    setComponentValue(object);
                    autoCompleteConsultaMicrorregiao.setComponentValue(target, object);
                    target.add(autoCompleteConsultaMicrorregiao);
                    target.add(component);
                }
            });
        }

        dlgCadastroMicrorregiao.limpar(target);

        if (getRegionalSaude() != null) {
            dlgCadastroMicrorregiao.show(target, getRegionalSaude());
        } else {
            dlgCadastroMicrorregiao.show(target);
        }
    }

    @Override
    protected void convertInput() {
        setConvertedInput((Microrregiao) autoCompleteConsultaMicrorregiao.getComponentValue());
    }

    @Override
    protected void onBeforeRender() {
        Object modelObject = getModelObject();

        if (modelObject == null) {
            modelObject = getConvertedInput();
        }

        autoCompleteConsultaMicrorregiao.setModelObject(modelObject);

        super.onBeforeRender();
    }

    @Override
    public Microrregiao getComponentValue() {
        return getModelObject();
    }

    @Override
    public void setComponentValue(Microrregiao value) {
        setModelObject(value);
    }

    @Override
    public void addAjaxUpdateValue() {
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        setComponentValue(null);
        clearInput();
        autoCompleteConsultaMicrorregiao.limpar(target);

        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    public void addRequired(){
        autoCompleteConsultaMicrorregiao.setRequired(true);
        autoCompleteConsultaMicrorregiao.getTxtDescricao().addRequiredClass();
    }
    
    public void removeRequired(){
        autoCompleteConsultaMicrorregiao.setRequired(false);
        autoCompleteConsultaMicrorregiao.getTxtDescricao().removeRequiredClass();
    }

    public AutoCompleteConsultaMicrorregiao getAutoCompleteConsultaMicrorregiao() {
        return autoCompleteConsultaMicrorregiao;
    }

    public RegionalSaude getRegionalSaude() {
        return regionalSaude;
    }

    public void setRegionalSaude(RegionalSaude regionalSaude) {
        this.regionalSaude = regionalSaude;
    }

    public AbstractAjaxLink getBtnCadastrarMicrorregiao() {
        return btnCadastrarMicrorregiao;
    }

    public void setBtnCadastrarMicrorregiao(AbstractAjaxLink btnCadastrarMicrorregiao) {
        this.btnCadastrarMicrorregiao = btnCadastrarMicrorregiao;
    }
}
