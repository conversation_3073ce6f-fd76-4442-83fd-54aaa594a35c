package br.com.celk.view.unidadesaude.relatorio;

import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioResumoAtendimentoCidDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoAtendimentoCidsPage extends RelatorioPage<RelatorioResumoAtendimentoCidDTOParam> {

    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap;
    private DropDown<String> dropDownCidCiap;
    private DropDown<String> dropDownTipoCid;
    private DropDown<String> dropDownCidNotificavel;

    @Override
    public void init(Form form) {
        RelatorioResumoAtendimentoCidDTOParam proxy = on(RelatorioResumoAtendimentoCidDTOParam.class);

        form.add(new AutoCompleteConsultaEmpresa(path(proxy.getUnidade())));
        form.add(new AutoCompleteConsultaTipoAtendimento(path(proxy.getTipoAtendimento())).setIncluirInativos(true));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(dropDownCidCiap = DropDownUtil.getEnumDropDown(path(proxy.getTipoConsultaCidCiap()), RelatorioResumoAtendimentoCidDTOParam.CidCiap.values()));

        dropDownCidCiap.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validaCamposCidCiap(target);
            }
        });

        form.add(dropDownCidCiap);
        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));
        form.add(autoCompleteConsultaCiap = new AutoCompleteConsultaCiap(path(proxy.getCiap())));
        autoCompleteConsultaCiap.setEnabled(false);

        try {
            form.add(dropDownArea = createDropDownArea());
            form.add(dropDownMicroArea = new DropDown<EquipeMicroArea>("equipeMicroArea"));
        } catch (DAOException ex) {
            Logger.getLogger(RelatorioResumoAtendimentoCidsPage.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(RelatorioResumoAtendimentoCidsPage.class.getName()).log(Level.SEVERE, null, ex);
        }

        form.add(dropDownTipoCid = DropDownUtil.getEnumDropDown(path(proxy.getTipoCid()), RelatorioResumoAtendimentoCidDTOParam.TipoCid.values()));
        form.add(dropDownCidNotificavel = DropDownUtil.getEnumDropDown(path(proxy.getCidNotificavel()), RelatorioResumoAtendimentoCidDTOParam.CidNotificavel.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioResumoAtendimentoCidDTOParam.FormaApresentacao.values()));

        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));

        form.add(DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelatorioResumoAtendimentoCidDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoOrdenacao()), RelatorioResumoAtendimentoCidDTOParam.TipoOrdenacao.values()));

        DropDown dropDown = new DropDown("tipoArquivo");
        dropDown.addChoice(TipoRelatorio.PDF, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));
        form.add(dropDown);
    }

    private void validaCamposCidCiap(AjaxRequestTarget target) {
        if (dropDownCidCiap.getValue().equals(RelatorioResumoAtendimentoCidDTOParam.CidCiap.CIAP.name())){
            // bloquear e limpar campos CID
            autoCompleteConsultaCid.limpar(target);
            autoCompleteConsultaCid.setEnabled(false);
            dropDownCidNotificavel.setEnabled(false);
            dropDownTipoCid.setEnabled(false);
            dropDownTipoCid.limpar(target);
            dropDownCidNotificavel.limpar(target);
            target.add(autoCompleteConsultaCid);
            target.add(dropDownTipoCid);
            target.add(dropDownCidNotificavel);

            // desbloquear campo CIAP
            autoCompleteConsultaCiap.setEnabled(true);
            target.add(autoCompleteConsultaCiap);

        } else {
            // bloquear e limpar campos CIAP
            autoCompleteConsultaCiap.limpar(target);
            autoCompleteConsultaCiap.setEnabled(false);
            target.add(autoCompleteConsultaCiap);

            // desbloquear campos CID
            autoCompleteConsultaCid.setEnabled(true);
            dropDownTipoCid.setEnabled(true);
            dropDownCidNotificavel.setEnabled(true);
            target.add(dropDownTipoCid);
            target.add(dropDownCidNotificavel);
            target.add(autoCompleteConsultaCid);
        }
    }

    private DropDown<EquipeArea> createDropDownArea() throws DAOException, ValidacaoException {
        DropDown<EquipeArea> dropDown = new DropDown<EquipeArea>("equipeArea");
        List<EquipeArea> list = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }
//                    dropDownMicroArea.setEnabled(!dropDownArea.getChoices().isEmpty());

                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }

    @Override
    public Class<RelatorioResumoAtendimentoCidDTOParam> getDTOParamClass() {
        return RelatorioResumoAtendimentoCidDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoAtendimentoCidDTOParam param) throws ReportException, ValidacaoException {
            return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioResumoAtendimentoCid(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoPorCidOuCiap");
    }

}
