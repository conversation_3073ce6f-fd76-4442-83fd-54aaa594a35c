package br.com.celk.view.hospital.aih;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.css.TextAlign;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgAutorizacaoDoLeito;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.StringColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.agenda.agendamento.leito.DlgClassificarAih;
import br.com.celk.view.agenda.agendamento.leito.DlgLancarSistemaTerceiro;
import br.com.celk.view.agenda.agendamento.leito.DlgMensagemAih;
import br.com.celk.view.agenda.agendamento.leito.DlgRegulacaoAih;
import br.com.celk.view.atendimento.consultaprontuario.ProntuarioPage;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.unidadesaude.anexo.PanelArquivosAnexados;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.dto.AihAnexosDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ReservaLeito;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagem;
import br.com.ksisolucoes.vo.comunicacao.base.AnexoDocumento;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.hospital.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import net.sf.jasperreports.engine.JRException;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.markup.html.basic.Label;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class RegulacaoAihPage extends BasePage {

    private Table<OcorrenciaAih> table;
    private List<OcorrenciaAih> ocorrenciaAihList = new ArrayList<OcorrenciaAih>();
    private final CompoundPropertyModel<AutorizacaoInternacaoHospitalarDTO> model;
    private InputField inputProcedimentoTipoLeito;
    private InputField inputProcedimentoTipoLeitoCustom;
    private Label lblProcedimentoTipoLeito;
    private Label lblProcedimentoTipoLeitoCustom;
    private DropDown<Long> dropDownCaraterInternacao;
    private InputField<String> txtAihCodigo;
    private AutoCompleteConsultaCid autoCompleteConsultaCidPrincipal;
    private AutoCompleteConsultaCid autoCompleteConsultaCidSecundario;
    private AutoCompleteConsultaCid autoCompleteConsultaCidCausa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetenciaSolicitado;
    private DateChooser dataSolicitacao;
    private InputField inputFieldProfSolicitante;
    private DropDown<TipoProcedimento> dropDownTipoProcedimento;
    private TextArea<String> sinaisClinicos;
    private TextArea<String> justificaInternacao;
    private TextArea<String> provaDiagnostica;
    private AbstractAjaxButton voltar;
    private WebMarkupContainer containerTable;
    private WebMarkupContainer containerRegulacaoAih;
    private DlgRegulacaoAih dlgDevolver;
    private DlgClassificarAih dlgClassificarAih;
    private DlgRegulacaoAih dlgNegar;
    private AihAnexosDTO dtoAih;
    private WebMarkupContainer containerAnexos;
    private RepeatingView repeaterAnexo;
    private List<AihAnexo> aihAnexosRemovidos = new ArrayList();
    private boolean criacaoAih;
    private DlgMensagemAih dlgMensagemAih;
    private DlgLancarOcorrenciaAih dlgLancarOcorrenciaAih;
    private AbstractAjaxButton btnAutorizar;
    private AbstractAjaxButton btnDevolver;
    private AbstractAjaxButton btnNegar;
    private AbstractAjaxButton btnVisualizarAih;
    private AbstractAjaxButton btnMensagem;
    private AbstractAjaxButton btnLancarOcorrencia;
    private AbstractAjaxButton btnClassificar;
    private AbstractAjaxButton btnProntuario;
    private AbstractAjaxButton btnEditar;
    private AbstractAjaxButton btnLancarSistemaTerceiro;
    private DlgAutorizacaoDoLeito<AutorizacaoInternacaoHospitalarDTO> dlgConfAutorizacaoLeito;
    private DlgLancarSistemaTerceiro dlgLancarSistemaTerceiro;

    public RegulacaoAihPage(AutorizacaoInternacaoHospitalarDTO object, boolean view) {
        if (object == null) {
            object = new AutorizacaoInternacaoHospitalarDTO();
        }

        model = new CompoundPropertyModel(object);
        init(view);
    }

    public void init(boolean view) {
        Form form = new Form("form", model);

        form.add(containerRegulacaoAih = new WebMarkupContainer("containerRegulacaoAih"));
        containerRegulacaoAih.setEnabled(false);

        AutorizacaoInternacaoHospitalarDTO proxy = on(AutorizacaoInternacaoHospitalarDTO.class);
        containerRegulacaoAih.add(txtAihCodigo = new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getCodigo())));
        containerRegulacaoAih.add(dataSolicitacao = new DateChooser(path(proxy.getAutorizacaoInternacaoHospitalar().getDataSolicitacao())));
        dataSolicitacao.setRequired(true);
        dataSolicitacao.setLabel(new Model(BundleManager.getString("dataSolicitacao")));

        containerRegulacaoAih.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus())));
        autoCompleteConsultaUsuarioCadsus.addAjaxUpdateValue();

        containerRegulacaoAih.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getDataNascimento())));
        containerRegulacaoAih.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getSexoFormatado())));
        containerRegulacaoAih.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getDescricaoIdade())));

        containerRegulacaoAih.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getAutorizacaoInternacaoHospitalar().getEmpresa())));

        containerRegulacaoAih.add(inputFieldProfSolicitante = new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getNomeProfissionalSolicitante())));
        inputFieldProfSolicitante.setLabel(new Model(BundleManager.getString("nomeProfissionalSolicitante")));
        containerRegulacaoAih.add(new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getDataCadastro())));


        containerRegulacaoAih.add(sinaisClinicos = new TextArea<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getPrincipaisSinaisSintomasClinicos())));
        containerRegulacaoAih.add(justificaInternacao = new TextArea<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getCondicoesJustificamInternacao())));
        containerRegulacaoAih.add(provaDiagnostica = new TextArea<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getPrincipaisResultadosProvasDiagnosticas())));

        containerRegulacaoAih.add(autoCompleteConsultaCidPrincipal = new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidPrincipal()), true));
        autoCompleteConsultaCidPrincipal.addAjaxUpdateValue();
        containerRegulacaoAih.add(autoCompleteConsultaCidSecundario = new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidSecundario())));
        containerRegulacaoAih.add(autoCompleteConsultaCidCausa = new AutoCompleteConsultaCid(path(proxy.getAutorizacaoInternacaoHospitalar().getCidCausaAssociada())));

        containerRegulacaoAih.add(autoCompleteConsultaProcedimentoCompetenciaSolicitado = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetenciaSolicitado()), true));
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setLabel(Model.of(bundle("procedimentoSolicitado")));
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setValidarNaoFaturaveis(true);
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.AIH_PRINCIPAL));
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.addAjaxUpdateValue();
        containerRegulacaoAih.add(dropDownTipoProcedimento = getDropDownTipoProcedimento(path(proxy.getAutorizacaoInternacaoHospitalar().getTipoProcedimento())));

        boolean utilizaTabelaCustomizadaParaInformacaoDaClinica = false;

        try {
            utilizaTabelaCustomizadaParaInformacaoDaClinica = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaTabelaCustomizadaParaInformacaoDaClinica").equals(RepositoryComponentDefault.SIM);
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        containerRegulacaoAih.add(lblProcedimentoTipoLeito = new Label("clinicaProcedimentoTipoLeito", bundle("clinica")));
        containerRegulacaoAih.add(inputProcedimentoTipoLeito = new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getProcedimentoTipoLeito().getDescricao())));
        containerRegulacaoAih.add(lblProcedimentoTipoLeitoCustom = new Label("clinicaProcedimentoTipoLeitoCustom", bundle("clinica")));
        containerRegulacaoAih.add(inputProcedimentoTipoLeitoCustom = new InputField(path(proxy.getAutorizacaoInternacaoHospitalar().getProcedimentoTipoLeitoCustom().getDescricaoTipoLeitoCustom())));
        containerRegulacaoAih.add(dropDownCaraterInternacao = getDropDownCaraterInternacao(path(proxy.getAutorizacaoInternacaoHospitalar().getCaraterInternacao())));

        lblProcedimentoTipoLeito.setVisible(!utilizaTabelaCustomizadaParaInformacaoDaClinica);
        inputProcedimentoTipoLeito.setVisible(!utilizaTabelaCustomizadaParaInformacaoDaClinica);
        lblProcedimentoTipoLeitoCustom.setVisible(utilizaTabelaCustomizadaParaInformacaoDaClinica);
        inputProcedimentoTipoLeitoCustom.setVisible(utilizaTabelaCustomizadaParaInformacaoDaClinica);

        if (view) {
            containerRegulacaoAih.add(containerTable = new WebMarkupContainer("tableLeito"));
            containerTable.add(table = new Table("table", getColumns(), getCollectionProvider()));
            table.populate();
            containerTable.setVisible(true);
            txtAihCodigo.setEnabled(false);
        } else {
            containerRegulacaoAih.add(containerTable = new WebMarkupContainer("tableLeito"));
            containerTable.add(table = new Table("table", getColumns(), getCollectionProvider()));
            containerTable.setVisible(false);
        }

        form.add(btnClassificar = new AbstractAjaxButton("btnClassificar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                if (dlgClassificarAih == null) {
                    addModal(target, dlgClassificarAih = new DlgClassificarAih(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws ValidacaoException, DAOException {
                            atualizaAihGeraOcorrenciaClassificacao(autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar(), classificacaoRisco, motivo);
                            setResponsePage(new ConsultaRegulacaoAihPage());
                        }

                        @Override
                        public void onConfirmarAutorizar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws DAOException, ValidacaoException {
                            atualizaAihGeraOcorrenciaClassificacao(autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar(), classificacaoRisco, motivo);
                            setResponsePage(new AutorizacaoDoLeitoPage(model.getObject()));
                        }

                    });
                }

                dlgClassificarAih.show(target, model.getObject());
            }
        });

        form.add(voltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                setResponsePage(new ConsultaRegulacaoAihPage());
            }
        });
        form.add(btnAutorizar = new AbstractAjaxButton("btnAutorizar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarAnexos();
                setResponsePage(new AutorizacaoDoLeitoPage(model.getObject()));
            }
        });

        form.add(btnDevolver = new AbstractAjaxButton("btnDevolver") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                if (dlgDevolver == null) {
                    addModal(target, dlgDevolver = new DlgRegulacaoAih(newModalId(), BundleManager.getString("confirmacaoDevolucao")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Aih aih, String motivo) throws ValidacaoException, DAOException {
                            salvarAnexos();
                            atualizaAihGeraOcorrencia(aih, Aih.Status.DEVOLVIDA, motivo);
                            setResponsePage(new ConsultaRegulacaoAihPage());
                        }
                    });
                }
                dlgDevolver.show(target, model.getObject().getAutorizacaoInternacaoHospitalar());
            }
        });
        form.add(btnNegar = new AbstractAjaxButton("btnNegar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                if (dlgNegar == null) {
                    addModal(target, dlgNegar = new DlgRegulacaoAih(newModalId(), BundleManager.getString("confirmacaoNegar")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Aih aih, String motivo) throws ValidacaoException, DAOException {
                            salvarAnexos();
                            atualizaAihGeraOcorrencia(aih, Aih.Status.NEGADA, motivo);
                            setResponsePage(new ConsultaRegulacaoAihPage());
                        }
                    });
                }
                dlgNegar.show(target, model.getObject().getAutorizacaoInternacaoHospitalar());
            }
        });
        form.add(btnVisualizarAih = new AbstractAjaxButton("btnVisualizarAih") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                AutorizacaoInternacaoHospitalarDTO dto = new AutorizacaoInternacaoHospitalarDTO();
                dto.setAutorizacaoInternacaoHospitalar(model.getObject().getAutorizacaoInternacaoHospitalar());
                dto.setProcedimentoCompetenciaSolicitado(model.getObject().getProcedimentoCompetenciaSolicitado());
                dto.setProcedimentoCompetenciaRealizado(model.getObject().getProcedimentoCompetenciaRealizado());

                setResponsePage(new ManutencaoAihAutorizadaPage(dto, true));
            }
        });

        form.add(btnProntuario = new AbstractAjaxButton("btnProntuario") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
                if (!usuarioLogado.isNivelAdminOrMaster() && usuarioLogado.getProfissional() == null) {
                    warn(bundle("msgDeveSerConfiguradoProfissionalParaUsuario"));
                } else {
                    setResponsePage(new ProntuarioPage(usuarioLogado, model.getObject().getAutorizacaoInternacaoHospitalar().getUsuarioCadSus(), model.getObject()));
                }
            }
        });

        form.add(btnMensagem = new AbstractAjaxButton("btnMensagem") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                if (dlgMensagemAih == null) {
                    addModal(target, dlgMensagemAih = new DlgMensagemAih(newModalId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, Aih aih, MensagemDTO mensagemDTO) throws ValidacaoException, DAOException {
                            geraOcorrenciaMensagem(aih, mensagemDTO);
                            MessageUtil.info(target, this, "Mensagem enviada com sucesso!");
                            updateNotificationPanel(target);
                        }
                    });
                }
                dlgMensagemAih.show(target, model.getObject().getAutorizacaoInternacaoHospitalar());
            }
        });

        form.add(btnLancarOcorrencia = new AbstractAjaxButton("btnLancarOcorrencia") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                viewDialogLancarOcorrencia(target);
            }
        });

        form.add(btnEditar = new AbstractAjaxButton("btnEditar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                viewDialogEditarReservaLeito(target);
            }
        });

        form.add(btnLancarSistemaTerceiro = new AbstractAjaxButton("btnLancarSistemaTerceiro") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException, JRException, IOException {
                viewDialogLancarSistemaTerceiro(target);
            }
        });

        btnAutorizar.setEnabled(isActionPermitted(Permissions.AGENDAR) &&
                !model.getObject().getAutorizacaoInternacaoHospitalar().getStatus().equals(Aih.Status.AGENDADA.value()));
        btnDevolver.setEnabled(isActionPermitted(Permissions.DEVOLVER));
        btnNegar.setEnabled(isActionPermitted(Permissions.NEGAR));
        btnVisualizarAih.setEnabled(isActionPermitted(Permissions.VISUALIZAR));
        btnMensagem.setEnabled(isActionPermitted(Permissions.MENSAGEM));
        btnLancarOcorrencia.setEnabled(isActionPermitted(Permissions.OCORRENCIA));
        btnProntuario.setEnabled(isActionPermitted(Permissions.PRONTUARIO));
        btnClassificar.setEnabled(isActionPermitted(Permissions.CLASSIFICAR_RISCO));
        btnEditar.setEnabled(isActionPermitted(Permissions.AGENDAR) &&
                model.getObject().getAutorizacaoInternacaoHospitalar().getStatus().equals(Aih.Status.AGENDADA.value()));
        btnLancarSistemaTerceiro.setEnabled(validaPermissaoHabilitaSinalizacaoAIHsistemaTerceiro());

        dtoAih = load();

        form.add(containerAnexos = new WebMarkupContainer("containerAnexos"));
        containerAnexos.setOutputMarkupId(true);
        containerAnexos.add(repeaterAnexo = new RepeatingView("repeaterAnexo"));

        populateArquivosAnexados(true);

        add(form);
        if (view) {
            modeViewOnly();
        }
    }

    public void viewDialogLancarSistemaTerceiro(AjaxRequestTarget target) {
        if (dlgLancarSistemaTerceiro == null) {
            addModal(target, dlgLancarSistemaTerceiro = new DlgLancarSistemaTerceiro(newModalId(), model.getObject().getAutorizacaoInternacaoHospitalar(), false){
                @Override
                public void onConfirmar(AjaxRequestTarget target, Aih aih, String sistemaTerceiro, String numeroProtocolo, String observacao) throws ValidacaoException, DAOException {
                    if ("-1".equals(sistemaTerceiro)) {
                        throw new ValidacaoException(BundleManager.getString("msg_informe_sistema_terceiro"));
                    }
                    atualizaAihAnexaSistemaTerceiro(aih, sistemaTerceiro, numeroProtocolo, observacao);
                }
            });
        }
        dlgLancarSistemaTerceiro.show(target, model.getObject().getAutorizacaoInternacaoHospitalar(), false);
    }

    public void viewDialogLancarOcorrencia(AjaxRequestTarget target) {
        if (dlgLancarOcorrenciaAih == null) {
            addModal(target, dlgLancarOcorrenciaAih = new DlgLancarOcorrenciaAih(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String ocorrencia) throws ValidacaoException, DAOException {
                    if (ocorrencia == null || ocorrencia.isEmpty()) {
                        throw new ValidacaoException(BundleManager.getString("informeDescricaoOcorrencia"));
                    }
                    lancarOcorrencia(ocorrencia);
                }
            });
        }
        dlgLancarOcorrenciaAih.show(target);
    }

    public void viewDialogEditarReservaLeito(AjaxRequestTarget target) {
        if (dlgConfAutorizacaoLeito == null) {
            model.getObject().setBtnExcluir(true);
            addModal(target, dlgConfAutorizacaoLeito = new DlgAutorizacaoDoLeito(newModalId(), BundleManager.getString("editarReservaLeito"), model.getObject()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException {
                    dlgConfAutorizacaoLeito.showEditar(target, motivo, object);
                }

                @Override
                public void onExcluir(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException {
                    setResponsePage(new ConsultaRegulacaoAihPage());
                }

                @Override
                public void onEditar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException {
                    setResponsePage(new ConsultaRegulacaoAihPage());
                }
            });
        }
        carregarReservaLeito(model.getObject());
        dlgConfAutorizacaoLeito.showEditar(target, model.getObject().getMotivo(), model.getObject());
    }

    private AutorizacaoInternacaoHospitalarDTO carregarReservaLeito(AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO) {
        ReservaLeito reservaLeito = LoadManager.getInstance(ReservaLeito.class)
                .addProperties(new HQLProperties(ReservaLeito.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ReservaLeito.PROP_AIH, autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar().getCodigo()))
                .start().getVO();

        LeitoQuarto leitoQuarto = LoadManager.getInstance(LeitoQuarto.class)
                .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_CODIGO, autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().getCodigo()))
                .start().getVO();

        if (reservaLeito != null) {
            autorizacaoInternacaoHospitalarDTO.setReservaLeito(reservaLeito);
            autorizacaoInternacaoHospitalarDTO.setDataInicial(reservaLeito.getDataInicio());
            autorizacaoInternacaoHospitalarDTO.setHoraInicial(reservaLeito.getDataInicio());
            autorizacaoInternacaoHospitalarDTO.setDataPrevisaoAlta(reservaLeito.getDataAlta());
            autorizacaoInternacaoHospitalarDTO.setHoraAlta(reservaLeito.getDataAlta());
        }

        if (leitoQuarto != null) {
            autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().setSexo(leitoQuarto.getSexo());
            autorizacaoInternacaoHospitalarDTO.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().setTipoLeito(leitoQuarto.getTipoLeito());
        }


        return autorizacaoInternacaoHospitalarDTO;
    }

    private void lancarOcorrencia(String ocorrencia) throws DAOException, ValidacaoException {
        Aih aih = (Aih) HibernateSessionFactory.getSession().get(Aih.class, dtoAih.getAih().getCodigo());
        OcorrenciaAih ocorrenciaAih = new OcorrenciaAih();
        ocorrenciaAih.setDataCadastro(DataUtil.getDataAtual());
        ocorrenciaAih.setUsuarioCadastro(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario());
        ocorrenciaAih.setEmpresa(br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa());
        ocorrenciaAih.setDescricao(ocorrencia);
        ocorrenciaAih.setAih(aih);
        BOFactoryWicket.save(ocorrenciaAih);
    }

    private AihAnexosDTO load() {
        AihAnexosDTO dto = new AihAnexosDTO();

        Aih aih = model.getObject().getAutorizacaoInternacaoHospitalar();
        List<AihAnexo> aihAnexos = LoadManager.getInstance(AihAnexo.class)
                .addProperties(new HQLProperties(AihAnexo.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AihAnexo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AihAnexo.PROP_AIH, aih))
                .start().getList();

        dto.setAih(aih);
        dto.setAnexosAih(aihAnexos);

        return dto;
    }

    private void populateArquivosAnexados(final boolean viewOnly) {
        for (AihAnexo anexo : dtoAih.getAnexosAih()) {
            repeaterAnexo.add(new PanelArquivosAnexados(repeaterAnexo.newChildId(), anexo) {
                @Override
                public void removerAnexo(AjaxRequestTarget target, AihAnexo anexo) throws ValidacaoException, DAOException {
                    dtoAih.getAnexosAih().remove(anexo);
                    aihAnexosRemovidos.add(anexo);
                    repeaterAnexo.remove(this);
                    target.add(containerAnexos);
                }

                @Override
                public void removerAnexo(AjaxRequestTarget target, AnexoPacienteElo elo) throws ValidacaoException, DAOException {

                }

                @Override
                public void removerAnexo(AjaxRequestTarget target, SolicitacaoAgendamentoAnexo anexo) throws ValidacaoException, DAOException {

                }

                @Override
                public void removerAnexoDocumento(AjaxRequestTarget target, AnexoDocumento anexoDocumento) throws ValidacaoException, DAOException {

                }

                @Override
                public boolean isViewOnly() {
                    return viewOnly;
                }
            });
        }
    }

    private void atualizaAihAnexaSistemaTerceiro(Aih aih, String sistemaTerceiro, String numeroProtocolo, String observacao) throws DAOException, ValidacaoException {

        if(numeroProtocolo != null) {
            aih.setStatus(Aih.Status.PENDENTE_EM_SISTEMA_TERCEIRO.value());
        } else {
            aih.setStatus(Aih.Status.AGUARDANDO_CADASTRO_SISTEMA_TERCEIRO.value());
        }

        HibernateSessionFactory.getSession().flush();
        HibernateSessionFactory.getSession().clear();

        aih.setNumeroProtocolo(numeroProtocolo);
        aih.setObservacaoProtocolo(observacao);
        aih.setSistemaTerceiro(Long.valueOf(sistemaTerceiro));

        BOFactoryWicket.save(aih);

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("AIH enviada para cadastro em sistema terceiro: ")
                .append(Aih.SistemaTerceiro.valeuOf(aih.getSistemaTerceiro()))
                .append(" - ")
                .append(Aih.Status.valeuOf(aih.getStatus()).descricao())
                .append(", nº protocolo: ")
                .append(aih.getNumeroProtocolo() != null ? aih.getNumeroProtocolo() : "");

        String descricaoOcorrencia = stringBuilder.toString();

        salvarOcorrencia(descricaoOcorrencia, aih);
    }

    private void atualizaAihGeraOcorrencia(Aih aih, Aih.Status status, String motivo) throws DAOException, ValidacaoException {
        aih.setStatus(status.value());
        if (Aih.Status.NEGADA.value().equals(status.value())) {
            aih.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
            aih.setDataCancelamento(DataUtil.getDataAtual());
        }
        BOFactoryWicket.save(aih);

        String descricaoOcorrencia = "AIH "
                + Aih.Status.valeuOf(aih.getStatus())
                + "; Motivo: "
                + motivo;
        salvarOcorrencia(descricaoOcorrencia, aih);
    }

    private void atualizaAihGeraOcorrenciaClassificacao(Aih aih, ClassificacaoRisco classificacaoRisco, String motivo) throws DAOException, ValidacaoException {
        aih.setClassificacaoRisco(classificacaoRisco);
        BOFactoryWicket.save(aih);

        StringBuilder stringBuilder = new StringBuilder();
        String descricaoOcorrencia = stringBuilder.append("Classificação: ")
                .append(classificacaoRisco.getDescricao())
                .append("; Motivo: ")
                .append(motivo != null ? motivo : "")
                .append(".")
                .toString();

        salvarOcorrencia(descricaoOcorrencia, aih);
    }

    private void geraOcorrenciaMensagem(Aih aih, MensagemDTO mensagemDTO) throws DAOException, ValidacaoException {
        String usuarios = "";
        String grupos = "";

        for (Usuario usuario : mensagemDTO.getUsuarios()) {
            usuarios += usuario.getNome() + ", ";
        }

        for (GrupoMensagem grupo : mensagemDTO.getGrupos()) {
            grupos += grupo.getDescricao() + ", ";
        }

        String mensagem = mensagemDTO.getMensagem().length() < 20 ? mensagemDTO.getMensagem() : mensagemDTO.getMensagem().substring(0, 20) + "...";

        String descricaoOcorrencia = "Envio de mensagem para " + usuarios + "; Grupo(s) " + grupos + "; Mensagem: " + mensagem;

        salvarOcorrencia(descricaoOcorrencia, aih);
    }

    private void modeViewOnly() {
        txtAihCodigo.setEnabled(false);
        dataSolicitacao.setEnabled(false);
        autoCompleteConsultaUsuarioCadsus.setEnabled(false);
        dataSolicitacao.setEnabled(false);
        sinaisClinicos.setEnabled(false);
        justificaInternacao.setEnabled(false);
        provaDiagnostica.setEnabled(false);
        autoCompleteConsultaCidPrincipal.setEnabled(false);
        autoCompleteConsultaCidSecundario.setEnabled(false);
        autoCompleteConsultaCidCausa.setEnabled(false);
        autoCompleteConsultaProcedimentoCompetenciaSolicitado.setEnabled(false);
        dropDownTipoProcedimento.setEnabled(false);
        inputProcedimentoTipoLeito.setEnabled(false);
        inputProcedimentoTipoLeitoCustom.setEnabled(false);
        dropDownCaraterInternacao.setEnabled(false);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ocorrenciaAihList;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(OcorrenciaAih.class);
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("data"), VOUtils.montarPath(OcorrenciaAih.PROP_DATA_HORA)));
        columns.add(new StringColumn(BundleManager.getString("usuario"), VOUtils.montarPath(OcorrenciaAih.PROP_USUARIO_CADASTRO)).setTextAlign(TextAlign.LEFT));
        columns.add(new StringColumn(BundleManager.getString("descricao"), VOUtils.montarPath(OcorrenciaAih.PROP_DESCRICAO)).setTextAlign(TextAlign.LEFT));
        return columns;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    private void validacoesInit() {
        AutorizacaoInternacaoHospitalarDTO dto = model.getObject();

        if (dto != null) {
            autoCompleteConsultaProcedimentoCompetenciaSolicitado.setCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal());
            //autoCompleteConsultaProcedimentoCompetenciaRealizado.setCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal());

            try {
                AtendimentoHelper.validaProcedimentoCid(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal(), dto.getProcedimentoCompetenciaRealizado());
            } catch (ValidacaoException ex) {
                warn(ex.getMessage());
            } catch (DAOException ex) {
                Logger.getLogger(RegulacaoAihPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private DropDown<Long> getDropDownCaraterInternacao(String id) {
        if (dropDownCaraterInternacao == null) {
            dropDownCaraterInternacao = new DropDown<Long>(id);

            dropDownCaraterInternacao.addChoice(null, "");
            for (Aih.CaraterInternacao list : Aih.CaraterInternacao.values()) {
                dropDownCaraterInternacao.addChoice(list.value(), list.descricao());
            }

        }
        return dropDownCaraterInternacao;
    }


    private DropDown<TipoProcedimento> getDropDownTipoProcedimento(String id) {
        DropDown<TipoProcedimento> dropDown = null;
        dropDown = new DropDown<TipoProcedimento>(id);
        dropDown.addAjaxUpdateValue();
        dropDown.setOutputMarkupPlaceholderTag(true);
        return dropDown;
    }

    private OcorrenciaAih salvarOcorrencia(String descricao, Aih aih) throws DAOException, ValidacaoException {
        OcorrenciaAih ocorrencia = new OcorrenciaAih();
        ocorrencia.setDataCadastro(DataUtil.getDataAtual());
        ocorrencia.setUsuarioCadastro(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario());
        ocorrencia.setEmpresa(br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa());
        ocorrencia.setDescricao(descricao);
        ocorrencia.setAih(aih);
        BOFactoryWicket.save(ocorrencia);

        return ocorrencia;
    }

    private void salvarAnexos() throws DAOException, ValidacaoException {
        for (AihAnexo aihAnexo : aihAnexosRemovidos) {
            salvarOcorrencia("Exclusão do anexo da AIH.", aihAnexo.getAih());
        }

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAnexosAih(dtoAih);
    }

    public void salvar() throws DAOException, ValidacaoException {
        AutorizacaoInternacaoHospitalarDTO dto = model.getObject();

        String numeroCartao = StringUtil.getDigits(dto.getAutorizacaoInternacaoHospitalar().getNroDocProfSol());
        if (!CnsValidator.validaCns(numeroCartao)) {
            throw new ValidacaoException(BundleManager.getString("cns_invalido"));
        }

        dto.getAutorizacaoInternacaoHospitalar().setProcedimentoSolicitado(dto.getProcedimentoCompetenciaSolicitado().getId().getProcedimento());
        if (dto.getProcedimentoCompetenciaRealizado() != null) {
            dto.getAutorizacaoInternacaoHospitalar().setProcedimentoRealizado(dto.getProcedimentoCompetenciaRealizado().getId().getProcedimento());
        }
        dto.getAutorizacaoInternacaoHospitalar().setStatus(Aih.Status.AGUARDANDO_ANALISE.value());
        if (inputProcedimentoTipoLeito.getModelObject() == null && inputProcedimentoTipoLeitoCustom.getModelObject() == null) {
            throw new ValidacaoException(BundleManager.getString("campoClinicaObrigatorio"));
        }
        if (dropDownCaraterInternacao.getModelObject() == null) {
            throw new ValidacaoException(BundleManager.getString("campoCaraterInternacaoObrigatorio"));
        }

        if (dto.getAutorizacaoInternacaoHospitalar().getDataSolicitacao().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(BundleManager.getString("dataSolicitacaoMaiorDataAtua"));
        }

        dto.getAutorizacaoInternacaoHospitalar().setDataAlteracao(DataUtil.getDataAtual());
        dto.getAutorizacaoInternacaoHospitalar().setUsuarioAlteracao(br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario());

        if (dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal().getDescricao() != null) {
            dto.getAutorizacaoInternacaoHospitalar().setDiagnosticoInicial(dto.getAutorizacaoInternacaoHospitalar().getCidPrincipal().getDescricao());
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_cid_primario"));
        }

        Aih aih = BOFactoryWicket.save(dto.getAutorizacaoInternacaoHospitalar());

        salvarOcorrencia(Bundle.getStringApplication("realizadoCadastroAIH"), aih);

        for (AihAnexo aihAnexo : aihAnexosRemovidos) {
            salvarOcorrencia("Exclusão do anexo da AIH.", aihAnexo.getAih());
        }

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAnexosAih(dtoAih);

        Page page = new ConsultaAihPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(AutorizacaoInternacaoHospitalarDTO.class, aih));
    }

    private boolean validaPermissaoHabilitaSinalizacaoAIHsistemaTerceiro() {
        try {
            String habilitaPesquisaCPFListaPublica = BOFactory.getBO(CommomFacade.class)
                    .modulo(Modulos.AGENDAMENTO)
                    .getParametro("HabilitaSinalizacaoAIHsistemaTerceiro");
            if (RepositoryComponentDefault.NAO.equals(habilitaPesquisaCPFListaPublica)) {
                return false;
            }
            return true;
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        return false;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("regulacaoAih");
    }
}
