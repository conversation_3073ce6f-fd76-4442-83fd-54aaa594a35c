package br.com.celk.view.frota.modelodocumento;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.vo.frota.ModeloDocumento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroModeloDocumentoPage extends CadastroPage<ModeloDocumento> {

    private InputField txtReferencia;
    
    public CadastroModeloDocumentoPage(ModeloDocumento object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroModeloDocumentoPage(ModeloDocumento object) {
        this(object, false);
    }

    public CadastroModeloDocumentoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtReferencia = new UpperField("referencia"));
        form.add(new RequiredUpperField("descricao"));
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<ModeloDocumento> getReferenceClass() {
        return ModeloDocumento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaModeloDocumentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroModeloDocumento");
    }

}
