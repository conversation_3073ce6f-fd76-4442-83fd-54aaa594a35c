package br.com.celk.view.vacina.confirmacaocarteiravacinacao;

import org.apache.commons.lang3.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class PanelCarteiraVacinacao extends Panel {


    public PanelCarteiraVacinacao(String id, String caminhoImg) {
        super(id);
        init(caminhoImg);
    }

    private void init(String base64Img) {
        if (!StringUtils.isEmpty(base64Img)) {
            WebMarkupContainer containerImg = new WebMarkupContainer("container-img");
            containerImg.setOutputMarkupPlaceholderTag(true);
            String img = "data:image/jpeg;base64," + base64Img;
            Image imagem = new NonCachingImage("img", "");
            imagem.add(new AttributeModifier("src", img));

            containerImg.add(imagem);
            add(containerImg);
        }
    }
}