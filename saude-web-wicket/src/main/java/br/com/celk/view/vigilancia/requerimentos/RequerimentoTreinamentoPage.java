package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.Column;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.component.vigilanciaendereco.RequiredPnlVigilanciaEndereco;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.CadastroEstabelecimentoPage;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoTreinamento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoTreinamentoMinistrantes;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by Maicon on 17/12/18.
 */
public class RequerimentoTreinamentoPage extends BasePage {

    private Form<RequerimentoTreinamentoDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoTreinamento requerimentoTreinamento;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private InputField<String> txtCnpjCpf;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtDescricaoAtividadeEstabelecimento;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private AbstractAjaxLink btnCadastrarEstabelecimento;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf");
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;
    private AbstractAjaxButton btnAdicionarMinistrante;

    private WebMarkupContainer containerPJ;
    private WebMarkupContainer containerPF;
    private InputField txtTelefone;
    private RequiredPnlVigilanciaEndereco pnlVigilanciaEndereco;
    private boolean enabled;
    private InputField txtNomeMinistrante;
    private UpperField txtCpfMinistrante;
    private InputField txtFormacaoMinistrante;
    private InputField txtMaterialDidaticoOutros;

    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private DropDown<Long> dropDownTipoPessoa;
    private Class classReturn;

    private WebMarkupContainer containerMinistrantes;
    private Table tblMinistrantes;
    private List<RequerimentoTreinamentoMinistrantes> requerimentoTreinamentoMinistrantesList;
    private CompoundPropertyModel<RequerimentoTreinamentoMinistrantes> modelMinistrantes;
    private Estabelecimento estabelecimento;

    private CheckBoxLongValue checkBoxMaterialDidaticoCartilha;
    private CheckBoxLongValue checkBoxMaterialDidaticoApostila;
    private CheckBoxLongValue checkBoxMaterialDidaticoOutros;
    private List<CheckBoxLongValue> lstCheckBoxMaterialDidatico = new ArrayList<CheckBoxLongValue>();
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private String orientacoes;

    public RequerimentoTreinamentoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        super();
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoTreinamentoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        super();
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimento(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    public RequerimentoTreinamentoPage(TipoSolicitacao tipoSolicitacao, Estabelecimento estabelecimento, Class<RequerimentosPage> clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        carregarRequerimento(requerimentoVigilancia);
        this.estabelecimento = estabelecimento;
        init(true);
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
        }
        if (configuracaoVigilancia != null) {
            orientacoes = configuracaoVigilancia.getOrientacaoTreinamento();
        }
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoTreinamentoDTO proxy = on(RequerimentoTreinamentoDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoTreinamentoDTO()));
        if (requerimentoTreinamento != null) {
            form.getModel().getObject().setRequerimentoTreinamento(requerimentoTreinamento);
        } else {
            form.getModel().getObject().setRequerimentoTreinamento(new RequerimentoTreinamento());
            form.getModel().getObject().getRequerimentoTreinamento().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }
        Long codigoRequerimento = form.getModel().getObject().getRequerimentoTreinamento().getCodigo();


        autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento()));
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setOutputMarkupId(true);
        autoCompleteConsultaEstabelecimento.setEnabled(enabled && codigoRequerimento == null);
        form.add(autoCompleteConsultaEstabelecimento);

        form.add(btnCadastrarEstabelecimento = new AbstractAjaxLink("btnCadastrarEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoTreinamentoPage.this.setResponsePage(new CadastroEstabelecimentoPage(tipoSolicitacao, null));
            }
        });

        btnCadastrarEstabelecimento.setVisible(false);
        if (new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), CadastroEstabelecimentoPage.class.getName())) {
            btnCadastrarEstabelecimento.setVisible(form.getModel().getObject().getRequerimentoTreinamento().getCodigo() == null);
        }

        form.add(new DisabledInputField<String>(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getProtocoloFormatado())));

        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia());
        form.add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));

        {
            containerPJ = new WebMarkupContainer("containerPJ");
            containerPJ.setVisible(codigoRequerimento != null && form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() != null);
            containerPJ.setOutputMarkupId(true);
            containerPJ.setOutputMarkupPlaceholderTag(true);
            containerPJ.add(txtFantasia = (DisabledInputField<String>) new DisabledInputField<String>(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
            containerPJ.add(txtDescricaoAtividadeEstabelecimento = new DisabledInputField<String>(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento().getAtividadeEstabelecimento().getDescricao())));
            containerPJ.setEnabled(enabled && codigoRequerimento == null);
            form.add(containerPJ);
        }
        {
            containerPF = new WebMarkupContainer("containerPF");
            containerPF.setOutputMarkupId(true);
            containerPF.setOutputMarkupPlaceholderTag(true);
            containerPF.setEnabled(enabled && codigoRequerimento == null);

            containerPF.add(pnlVigilanciaEndereco = (RequiredPnlVigilanciaEndereco) new RequiredPnlVigilanciaEndereco(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getVigilanciaEndereco())).setLabel(new Model(bundle("endereco"))));
            containerPF.add(dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getTipoPessoa()), RequerimentoVigilancia.TipoPessoa.values()));
            dropDownTipoPessoa.setEnabled(codigoRequerimento != null && form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() == null);
            dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    txtCnpjCpf.limpar(target);
                    initMaskCpfCnpj(target);
                }
            });
            containerPF.add(txtTelefone = (InputField) new InputField(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getTelefone())).setEnabled(enabled));
            containerPF.add(txtCnpjCpf = new InputField<>(path(proxy.getRequerimentoTreinamento().getRequerimentoVigilancia().getCnpjCpf())));
            txtCnpjCpf.addAjaxUpdateValue();

            txtCnpjCpf.setEnabled(codigoRequerimento != null && form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() == null);
            form.add(containerPF);
        }

        form.add(new PnlVigilanciaEndereco(path(proxy.getRequerimentoTreinamento().getVigilanciaEndereco())).setLabel(new Model(bundle("endereco"))).setEnabled(enabled));
        form.add(new HoraMinutoField(path(proxy.getRequerimentoTreinamento().getHoraInicioPrimeiroTurno())));
        form.add(new HoraMinutoField(path(proxy.getRequerimentoTreinamento().getHoraFimPrimeiroTurno())));
        form.add(new HoraMinutoField(path(proxy.getRequerimentoTreinamento().getHoraInicioSegundoTurno())));
        form.add(new HoraMinutoField(path(proxy.getRequerimentoTreinamento().getHoraFimSegundoTurno())));

        form.add(checkBoxMaterialDidaticoCartilha = new CheckBoxLongValue("materialCartilha", RequerimentoTreinamento.MaterialDidatico.CARTILHA.sum(), new Model<Long>()));
        form.add(checkBoxMaterialDidaticoApostila = new CheckBoxLongValue("materialApostila", RequerimentoTreinamento.MaterialDidatico.APOSTILA.sum(), new Model<Long>()));
        form.add(checkBoxMaterialDidaticoOutros = new CheckBoxLongValue("materialOutros", RequerimentoTreinamento.MaterialDidatico.OUTROS.sum(), new Model<Long>()));
        lstCheckBoxMaterialDidatico.add(checkBoxMaterialDidaticoCartilha);
        lstCheckBoxMaterialDidatico.add(checkBoxMaterialDidaticoApostila);
        lstCheckBoxMaterialDidatico.add(checkBoxMaterialDidaticoOutros);
        form.add(txtMaterialDidaticoOutros = new InputField(path(proxy.getRequerimentoTreinamento().getMaterialDidaticoOutros())));
        txtMaterialDidaticoOutros.setEnabled(false);

        RequerimentoTreinamentoMinistrantes proxyMinistrantes = on(RequerimentoTreinamentoMinistrantes.class);
        containerMinistrantes = new WebMarkupContainer("containerMinistrantes", modelMinistrantes = new CompoundPropertyModel(new RequerimentoTreinamentoMinistrantes()));
        containerMinistrantes.add(txtNomeMinistrante = (InputField) new InputField(path(proxyMinistrantes.getNome())).setEnabled(enabled));
        containerMinistrantes.add(txtCpfMinistrante = new UpperField(path(proxyMinistrantes.getCpf())));
        containerMinistrantes.add(txtFormacaoMinistrante = (InputField) new InputField(path(proxyMinistrantes.getFormacao())).setEnabled(enabled));
        txtNomeMinistrante.addAjaxUpdateValue();
        txtCpfMinistrante.addAjaxUpdateValue();
        txtFormacaoMinistrante.addAjaxUpdateValue();
        containerMinistrantes.add(btnAdicionarMinistrante = new AbstractAjaxButton("btnAdicionarMinistrante") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarMinistrantes(target);
            }
        });
        btnAdicionarMinistrante.setEnabled(enabled);
        btnAdicionarMinistrante.setDefaultFormProcessing(false);
        containerMinistrantes.add(tblMinistrantes = new Table("tblMinistrantes", getColumnsMinistrantes(), getCollectionProviderMinistrantes()));
        tblMinistrantes.populate();
        tblMinistrantes.setEnabled(enabled);
        containerMinistrantes.setOutputMarkupId(true);
        form.add(containerMinistrantes);

        form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia(), enabled));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        form.add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        form.add(new InputArea("orientacoes", new PropertyModel(this, "orientacoes")).setEnabled(false));

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        if (estabelecimento != null) {
            form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
            atualizarDadosEstabelecimento(null, estabelecimento);
        }

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosEstabelecimento(target, object);
            }
        });
        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarDadosEstabelecimento(target, null);
            }
        });


        checkBoxMaterialDidaticoOutros.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (checkBoxMaterialDidaticoOutros.getComponentValue() != null && checkBoxMaterialDidaticoOutros.getComponentValue() == 4L) {
                    txtMaterialDidaticoOutros.setEnabled(true);
                } else {
                    txtMaterialDidaticoOutros.setEnabled(false);
                }
                txtMaterialDidaticoOutros.limpar(target);
            }
        });


        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getCodigo(), false).setVisible(!enabled));

        if (estabelecimento != null) {
            form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
        }

        if (form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() != null) {
            atualizarDadosEstabelecimento(null, form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento());
        }
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxMaterialDidatico, Coalesce.asLong(form.getModel().getObject().getRequerimentoTreinamento().getMaterialDidatico()));
        if (checkBoxMaterialDidaticoOutros.getComponentValue() != null && checkBoxMaterialDidaticoOutros.getComponentValue() == 4L) {
            txtMaterialDidaticoOutros.setEnabled(true);
        } else {
            txtMaterialDidaticoOutros.setEnabled(false);
        }
        add(form);
    }

    private void initMaskCpfCnpj(AjaxRequestTarget target) {
        if (form.getModel().getObject() != null) {
            if (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getTipoPessoa())) {
                if (target != null) {
                    target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                }
                if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    txtCnpjCpf.remove(attributeModifierCpf);
                }
                if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    txtCnpjCpf.add(attributeModifierCnpj);
                }
            } else {
                if (target != null) {
                    target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                }
                if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    txtCnpjCpf.remove(attributeModifierCnpj);
                }
                if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    txtCnpjCpf.add(attributeModifierCpf);
                }
            }
        }

        if (target != null) {
            target.add(txtCnpjCpf);
        }
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        RequerimentoTreinamentoDTO dto = form.getModel().getObject();

        if (dto.getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() == null) {
            throw new ValidacaoException("Informe o Estabelecimento");
        }

        if (dto.getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento() == null && dto.getRequerimentoTreinamento().getRequerimentoVigilancia().getNome() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabOuNome"));
        }
        if (form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
        dto.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        dto.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
//        dto.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoTreinamento().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        atualizarDadosComuns();
        dto.setRequerimentoTreinamentoMinistrantesList(requerimentoTreinamentoMinistrantesList);
        dto.getRequerimentoTreinamento().setMaterialDidatico(CheckBoxUtil.getSomatorio(lstCheckBoxMaterialDidatico));
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoTreinamento(dto);

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                if (VigilanciaHelper.abrirTelaFinanceiro(object)) {
                    RequerimentoVigilanciaDTO dto = new RequerimentoVigilanciaDTO();
                    dto.setRequerimentoVigilancia(object);
                    setResponsePage(new BoletoVigilanciaPage(dto, classReturn));
                } else {
                    try {
                        Page pageReturn = (Page) classReturn.newInstance();
                        getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                        setResponsePage(pageReturn);
                    } catch (InstantiationException | IllegalAccessException e) {
                        Loggable.log.error(e.getMessage(), e);
                    }
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (estabelecimento != null) {
            if (form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null || form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty()) {
                Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                        .start().getVO();

                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setTipoPessoa(estabPrincipal.getTipoPessoa());
            } else {
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setCnpjCpf(estabelecimento.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setTipoPessoa(estabelecimento.getTipoPessoa());
            }
            if (target != null) {
                pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().limpar(target);
            }
            if (estabelecimento.getVigilanciaEndereco() != null && estabelecimento.getVigilanciaEndereco().getCodigo() != null) {
                VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getVigilanciaEndereco().getCodigo()))
                        .start().getVO();

                pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().setComponentValue(ve);
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setTelefone(estabelecimento.getTelefone());
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            }

            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(SetorVigilancia.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();
            if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
            }

            if (CollectionUtils.isEmpty(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList()) && estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null
                    && estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia());
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
                if (target != null) {
                    pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
                }
            }

//            form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setSetorVigilancia(null);
            if (target != null) {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().limpar(target);
            }
            initMaskCpfCnpj(target);
            containerPF.setEnabled(false);
            if (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().getTipoPessoa())) {
                containerPJ.setVisible(true);
            } else {
                containerPJ.setVisible(false);
            }
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setModelObject(null);
        } else {
            form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setVigilanciaEndereco(null);
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(true);
            pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
            if (target != null) {
                pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
            }
            txtCnpjCpf.setEnabled(false);
            dropDownTipoPessoa.setEnabled(false);
            pnlVigilanciaEndereco.setEnabled(true);
            containerPF.setEnabled(true);
            containerPJ.setVisible(false);

            initMaskCpfCnpj(target);
            if (target != null) {
                pnlVigilanciaEndereco.limpar(target);
                target.add(dropDownTipoPessoa);
                ComponentUtils.limparContainer(containerPF, target);
            }
        }

        if (target != null) {
            target.add(pnlVigilanciaEndereco.getBtnCadastrarEndereco());
            target.add(containerPJ);
            target.add(containerPF);
            target.add(txtFantasia);
            target.add(txtDescricaoAtividadeEstabelecimento);
            target.add(txtTelefone);
            target.add(pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia());
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void carregarRequerimento(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoTreinamento = LoadManager.getInstance(RequerimentoTreinamento.class)
                    .addProperties(new HQLProperties(RequerimentoTreinamento.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(ResponsavelTecnico.class, RequerimentoTreinamento.PROP_RESPONSAVEL_TECNICO).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoTreinamento.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoTreinamento.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            requerimentoTreinamento.setRequerimentoVigilancia(requerimentoVigilancia);
            carregarAnexos(requerimentoVigilancia);

            if (requerimentoTreinamento != null && requerimentoTreinamento.getCodigo() != null) {
                carregarRequerimentoTreinamentoMinistrantes();
            }
        }
    }

    private void carregarRequerimentoTreinamentoMinistrantes() {
        requerimentoTreinamentoMinistrantesList = LoadManager.getInstance(RequerimentoTreinamentoMinistrantes.class)
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoTreinamentoMinistrantes.PROP_REQUERIMENTO_TREINAMENTO, requerimentoTreinamento))
                .start().getList();
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("requerimentoCredenciamentoTreinamento");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    private void atualizarDadosComuns() {
        form.getModel().getObject().getRequerimentoTreinamento().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        form.getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        form.getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        form.getModel().getObject().setRequerimentoVigilanciaFiscalList(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalList());
        form.getModel().getObject().setRequerimentoVigilanciaFiscalListExcluir(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalListExcluir());
    }

    private void adicionarMinistrantes(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        RequerimentoTreinamentoMinistrantes treinamentoMinistrantes = (RequerimentoTreinamentoMinistrantes) SerializationUtils.clone(modelMinistrantes.getObject());

        if (treinamentoMinistrantes.getNome() == null) {
            throw new ValidacaoException("Por favor, informe nome do Ministrante.");
        }

        CrudUtils.adicionarItem(target, tblMinistrantes, requerimentoTreinamentoMinistrantesList, treinamentoMinistrantes);

        modelMinistrantes.setObject(new RequerimentoTreinamentoMinistrantes());
        txtNomeMinistrante.limpar(target);
        txtCpfMinistrante.limpar(target);
        txtFormacaoMinistrante.limpar(target);
        target.focusComponent(txtNomeMinistrante);
        target.add(txtNomeMinistrante);
        target.add(containerMinistrantes);
    }

    private List<IColumn> getColumnsMinistrantes() {
        List<IColumn> columns = new ArrayList<>();
        RequerimentoTreinamentoMinistrantes proxy = on(RequerimentoTreinamentoMinistrantes.class);

        columns.add(getActionColumnMinistrantes());
        columns.add(new Column(bundle("nome"), path(proxy.getNome())));
        columns.add(new Column(bundle("cpf"), path(proxy.getCpf())));
        columns.add(new Column(bundle("formacao"), path(proxy.getFormacao())));
        return columns;
    }

    private IColumn getActionColumnMinistrantes() {
        return new MultipleActionCustomColumn<RequerimentoTreinamentoMinistrantes>() {
            @Override
            public void customizeColumn(RequerimentoTreinamentoMinistrantes rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<RequerimentoTreinamentoMinistrantes>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoTreinamentoMinistrantes modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblMinistrantes, requerimentoTreinamentoMinistrantesList, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderMinistrantes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (requerimentoTreinamentoMinistrantesList == null) {
                    requerimentoTreinamentoMinistrantesList = new ArrayList<>();
                }
                return requerimentoTreinamentoMinistrantesList;
            }
        };
    }
}
