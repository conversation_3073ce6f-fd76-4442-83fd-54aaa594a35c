package br.com.celk.view.atendimento.prontuario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroConfiguracaoConsultaAtendimentoDTO;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConfiguracaoConsultaAtendimentoPage extends BasePage {
    
    public ConfiguracaoConsultaAtendimentoPage() {
        init();
    }
    
    private void init(){        
        CadastroConfiguracaoConsultaAtendimentoDTO dto = new CadastroConfiguracaoConsultaAtendimentoDTO();
        
        List<ITab> tabs = new ArrayList<>();
        tabs.add(new CadastroTab<CadastroConfiguracaoConsultaAtendimentoDTO>(dto) {

            @Override
            public ITabPanel<CadastroConfiguracaoConsultaAtendimentoDTO> newTabPanel(String panelId, CadastroConfiguracaoConsultaAtendimentoDTO dto) {
                return new ConfiguracaoConsultaAtendimentoTab(panelId, dto);
            }
        });
        tabs.add(new CadastroTab<CadastroConfiguracaoConsultaAtendimentoDTO>(dto) {

            @Override
            public ITabPanel<CadastroConfiguracaoConsultaAtendimentoDTO> newTabPanel(String panelId, CadastroConfiguracaoConsultaAtendimentoDTO dto) {
                return new ConfiguracaoOrdenacaoConsultaAtendimentoTab(panelId, dto);
            }
        });
        
        add(new ConfiguracaoConsultaAtendimentoTabbedPanel("wizard", dto, false, tabs));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("configuracaoConsultaAtendimentos");
    }
}