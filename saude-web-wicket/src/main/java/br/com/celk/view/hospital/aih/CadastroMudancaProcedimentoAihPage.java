package br.com.celk.view.hospital.aih;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.solicitacaomudancaprocedimentoDTO.SolicitacaoMudancaProcedimentoDTO;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.Arrays;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroMudancaProcedimentoAihPage extends BasePage {

    private Aih aih;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private String procedimentoAnterior;
    private InputArea inputAreaJustificativa;
    private AutoCompleteConsultaCid autoCompleteConsultaCidPrincipal;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DateChooserAjax dataSolicitacao;
    private Form<SolicitacaoMudancaProcedimentoDTO> form;
    private AutoCompleteConsultaCid autoCompleteConsultaCidSec;
    private AutoCompleteConsultaCid autoCompleteConsultaCidCausasAssociadas;

    public CadastroMudancaProcedimentoAihPage(Aih aih) {
        this.aih = carregarDadosExtras(aih);
        init();
        getProcedimentoAnterior();
    }

    public void init() {
        this.form = new Form("form", new CompoundPropertyModel<SolicitacaoMudancaProcedimentoDTO>(new SolicitacaoMudancaProcedimentoDTO()));

        SolicitacaoMudancaProcedimentoDTO proxy = on(SolicitacaoMudancaProcedimentoDTO.class);

        form.add(new DisabledInputField<String>(path(proxy.getProcedimentoAnteriorFormatado())));

        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetencia())));
        autoCompleteConsultaProcedimentoCompetencia.setLabel(new Model(BundleManager.getString("novoProcedimento")));
        autoCompleteConsultaProcedimentoCompetencia.setRequired(true);
        autoCompleteConsultaProcedimentoCompetencia.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.AIH_PRINCIPAL));
//        autoCompleteConsultaProcedimentoCompetencia.setProfissional(aih.getAtendimento().getProfissional());
        autoCompleteConsultaProcedimentoCompetencia.setUsuarioCadsus(aih.getContaPaciente() != null ? aih.getContaPaciente().getUsuarioCadsus() : null);
        autoCompleteConsultaProcedimentoCompetencia.setUnidadeAtendimento(aih.getContaPaciente() != null && aih.getContaPaciente().getAtendimentoInformacao() != null ? aih.getContaPaciente().getAtendimentoInformacao().getEmpresa() : null);
        autoCompleteConsultaProcedimentoCompetencia.add(new ConsultaListener<ProcedimentoCompetencia>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoCompetencia object) {
                if (object != null) {
                    boolean existsProcedCid = LoadManager.getInstance(ProcedimentoCid.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), object)))
                            .exists();

                    if (existsProcedCid) {
                        autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(object);
                        autoCompleteConsultaCidSec.setProcedimentoCompetencia(object);
                        autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(object);
                    } else {
                        autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(null);
                        autoCompleteConsultaCidSec.setProcedimentoCompetencia(null);
                        autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(null);
                    }
                } else {
                    autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(null);
                    autoCompleteConsultaCidSec.setProcedimentoCompetencia(null);
                    autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(null);
                }
            }
        });
        form.add(new InputField(path(proxy.getSolicitacaoMudancaProcedimento().getDiagnosticoInicial())));
        form.add(autoCompleteConsultaCidPrincipal = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidPrincipal())));
        autoCompleteConsultaCidPrincipal.setLabel(new Model(BundleManager.getString("cidPrincipal")));
        autoCompleteConsultaCidPrincipal.setRequired(true);
        autoCompleteConsultaCidPrincipal.setSituacaoPrincipal(RepositoryComponentDefault.SIM);
        form.add(this.autoCompleteConsultaCidSec = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidSecundario())));
        autoCompleteConsultaCidSec.setSituacaoPrincipal(RepositoryComponentDefault.NAO);
        form.add(autoCompleteConsultaCidCausasAssociadas = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidCausasAssociadas())));
        form.add(inputAreaJustificativa = new InputArea(path(proxy.getSolicitacaoMudancaProcedimento().getJustificativa())));
        inputAreaJustificativa.setLabel(new Model(BundleManager.getString("justificativaSolicitacao")));
        inputAreaJustificativa.setRequired(true);

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getSolicitacaoMudancaProcedimento().getProfissionalSolicitante())));
        autoCompleteConsultaProfissional.setRequired(true);
        autoCompleteConsultaProfissional.setLabel(new Model(BundleManager.getString("profissional_solicitante")));

        form.add(dataSolicitacao = new DateChooserAjax(path(proxy.getSolicitacaoMudancaProcedimento().getDataSolicitacao())));
        dataSolicitacao.setRequired(true);
        dataSolicitacao.setLabel(new Model(BundleManager.getString("dataSolicitacao")));

        form.add(new VoltarButton("btnVoltar"));

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(form, target);
            }
        }));

        add(form);
    }

    private Procedimento getProcedimentoAnterior() {
        if (aih != null) {
            form.getModelObject().setProcedimentoAnteriorFormatado(aih.getProcedimentoSolicitado());
            return aih.getProcedimentoSolicitado();
        }
        return null;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("mudancaProcedimento");
    }

    public void salvar(Form form, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        SolicitacaoMudancaProcedimentoDTO object = (SolicitacaoMudancaProcedimentoDTO) form.getModel().getObject();

        if (object.getSolicitacaoMudancaProcedimento().getDataSolicitacao().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(BundleManager.getString("dataSolicitacaoDeveSerMenorIgualAtual"));
        }

        object.getSolicitacaoMudancaProcedimento().setNovoProcedimento(object.getProcedimentoCompetencia().getId().getProcedimento());
        object.getSolicitacaoMudancaProcedimento().setAutorizacaoInternacaoHospitalar(aih);
        object.getSolicitacaoMudancaProcedimento().setProcedimentoAnterior(getProcedimentoAnterior());
        object.getSolicitacaoMudancaProcedimento().setStatus(SolicitacaoMudancaProcedimento.Status.AGUARDANDO_ANALISE.value());

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarSolicitacaoMudancaProcedimento(object, false);

        setResponsePage(new ConsultaAihPage());
    }

    private Aih carregarDadosExtras(Aih aih) {
        return LoadManager.getInstance(Aih.class)
                .addProperties(new HQLProperties(Aih.class).getProperties())
                .addProperty(VOUtils.montarPath(Aih.PROP_CONTA_PACIENTE, ContaPaciente.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Aih.PROP_CONTA_PACIENTE, ContaPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Aih.PROP_CONTA_PACIENTE, ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, AtendimentoInformacao.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Aih.PROP_CONTA_PACIENTE, ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, AtendimentoInformacao.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Aih.PROP_CONTA_PACIENTE, ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, AtendimentoInformacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_CODIGO, aih.getCodigo()))
                .start().getVO();
    }
}