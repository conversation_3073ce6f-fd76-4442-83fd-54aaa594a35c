package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.telefonefield.RequiredTelefoneField;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDefesaPrevia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Deprecated
@Private
public class RequerimentoDefesaPreviaExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoDefesaPreviaDTO> form;
    private RequerimentoDefesaPrevia requerimentoDefesaPrevia;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoSolicitacao tipoSolicitacao;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private InputField txtNome;
    private RequiredTelefoneField txtTelefone;
    private InputField txtNumeroAutoInfracao;
    private InputField txtSerie;
    private InputArea txtAreaRazoes;
    private InputField txtDescricao;
    private InputArea txaMotivo;
    private PnlVigilanciaEndereco pnlVigilanciaEndereco;
    private DateChooser dchDatado;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private RequiredInputField<String> txtCnpjCpf;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj required");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf required");
    private boolean enabled;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private Class classReturn;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;
    private DropDown<Long> dropDownTipoPessoa;

    public RequerimentoDefesaPreviaExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoDefesaPreviaExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoDefesaPrevia(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao()) || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }
        Long codigoRequerimentoDefesaPrevia = getForm().getModel().getObject().getRequerimentoDefesaPrevia().getCodigo();

        RequerimentoDefesaPreviaDTO proxy = on(RequerimentoDefesaPreviaDTO.class);

        // Dados Estabelecimento ou pessoa fisica
        getForm().add(autoCompleteConsultaEstabelecimento = (AutoCompleteConsultaEstabelecimento) new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoDefesaPrevia().getEstabelecimento())).setLabel(Model.of(bundle("estabelecimento"))).setEnabled(enabled));
        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                updateFields(target);
            }
        });
        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                updateFields(target);
            }
        });
        autoCompleteConsultaEstabelecimento.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        getForm().add(txtNome = (InputField) new InputField(path(proxy.getRequerimentoDefesaPrevia().getNome())).setLabel(Model.of(bundle("nome")))
                .add(new AjaxFormComponentUpdatingBehavior("onblur") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        updateFields(target);
                        target.focusComponent(pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().getTxtDescricao().getTextField());
                    }
                }));
        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));
        pnlDadosComumRequerimentoVigilancia.getContainerSetorVigilancia().setVisible(false);

        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getCodigo() == null) {
            ConfiguracaoVigilancia configuracaoVigilancia = null;
            try {
                configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            } catch (ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }

            if (configuracaoVigilancia != null && configuracaoVigilancia.getSetorVigilanciaDefesaPrevia() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaDefesaPrevia());
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
            }
        }
        txtNome.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        getForm().add(new DisabledInputField<String>(path(proxy.getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getProtocoloFormatado())));
        getForm().add(pnlVigilanciaEndereco = (PnlVigilanciaEndereco) new PnlVigilanciaEndereco(path(proxy.getRequerimentoDefesaPrevia().getVigilanciaEndereco())).setLabel(Model.of(bundle("endereco"))));
        pnlVigilanciaEndereco.addRequired();
        pnlVigilanciaEndereco.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        getForm().add(txtTelefone = (RequiredTelefoneField) new RequiredTelefoneField(path(proxy.getRequerimentoDefesaPrevia().getTelefone())).setLabel(Model.of(bundle("telefone"))));
        txtTelefone.add(new AttributeModifier("size", "18"));
        txtTelefone.addRequiredClass();
        txtTelefone.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        getForm().add(dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getTipoPessoa()), RequerimentoVigilancia.TipoPessoa.values()));
        dropDownTipoPessoa.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getFormComponent().getModelObject() != null) {
                    if (Estabelecimento.TipoPessoa.FISICA.value().equals(getFormComponent().getModelObject())) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.remove(attributeModifierCnpj);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCpf);
                        }
                    } else {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.remove(attributeModifierCpf);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCnpj);
                        }
                    }
                    target.add(txtCnpjCpf);
                }
                ;
            }
        });
        getForm().add(txtCnpjCpf = (RequiredInputField<String>) new RequiredInputField<String>(path(proxy.getRequerimentoDefesaPrevia().getCnpjCpf())).setLabel(Model.of(bundle("cnpjCpf"))));
        txtCnpjCpf.addRequiredClass();
        txtCnpjCpf.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        if (RequerimentoVigilancia.Origem.INTERNO.value().equals(getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getOrigem())) {
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setRequired(true);
        }

        getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia(), enabled));

        getForm().add(txtNumeroAutoInfracao = (InputField) new RequiredInputField(path(proxy.getRequerimentoDefesaPrevia().getNumeroAutoInfracao())).setLabel((new Model(bundle("nAutoInfracao")))));
        txtNumeroAutoInfracao.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        getForm().add(dchDatado = (DateChooser) new RequiredDateChooser(path(proxy.getRequerimentoDefesaPrevia().getDataAuto())).setLabel(new Model(bundle("dataDoAuto"))));
        dchDatado.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);
        getForm().add(txtAreaRazoes = (InputArea) new InputArea(path(proxy.getRequerimentoDefesaPrevia().getRazoes())).setLabel((new Model(bundle("razoes")))));
        txtAreaRazoes.setEnabled(enabled && codigoRequerimentoDefesaPrevia == null);


        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }
        //Fim Anexos

        getForm().add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        add(getForm());

        if (codigoRequerimentoDefesaPrevia == null) {
            enableEstabelecimentoNome(null);
            enableEnderecoTelefoneCnpjCpf(null);
        } else {
            this.txtCnpjCpf.add(this.attributeModifierCpf);
            if (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getTipoPessoa())) {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.remove(attributeModifierCpf);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.add(attributeModifierCnpj);
                }
            } else {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.remove(attributeModifierCnpj);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.add(attributeModifierCpf);
                }
            }
        }
    }

    private void updateFields(AjaxRequestTarget target) {
        enableEstabelecimentoNome(target);
        enableEnderecoTelefoneCnpjCpf(target);
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        validaCampos();
        getForm().getModel().getObject().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        atualizarDadosComuns();
        getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

        requerimentoVigilancia = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoDefesaPrevia(getForm().getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);
                param.setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, requerimentoVigilancia);
    }

    private void validaCampos() throws ValidacaoException {
        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getEstabelecimento() == null &&
                Coalesce.asString(getForm().getModel().getObject().getRequerimentoDefesaPrevia().getNome()).trim().length() <= 0) {
            throw new ValidacaoException(bundle("msgInformeEstabelecimentoOuNome"));
        }
        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getNome() != null) {
            if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getVigilanciaEndereco() == null) {
                throw new ValidacaoException(bundle("msgInformeEndereco"));
            } else if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getTelefone() == null) {
                throw new ValidacaoException(bundle("msgInformeTelefone"));
            } else if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getCnpjCpf() == null) {
                throw new ValidacaoException(bundle("msgInformeCnpjCpf"));
            }
        }
        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getCpfSolicitante() == null
                && getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getDataAuto().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(bundle("msgCampoDataAutoMaiorDataAtual"));
        }
        if (Coalesce.asString(getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRazoes()).length() <= 0 && CollectionUtils.isEmpty(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList())) {
            throw new ValidacaoException(bundle("msgInformeCampoRazoesOuAdicioneAnexo"));
        }
    }

    private Form<RequerimentoDefesaPreviaDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoDefesaPreviaDTO()));
            if (requerimentoDefesaPrevia != null) {
                form.getModel().getObject().setRequerimentoDefesaPrevia(requerimentoDefesaPrevia);
            } else {
                form.getModel().getObject().setRequerimentoDefesaPrevia(new RequerimentoDefesaPrevia());
                form.getModel().getObject().getRequerimentoDefesaPrevia().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("tituloDefesaPrevia");
    }

    private void carregarRequerimentoDefesaPrevia(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoDefesaPrevia = LoadManager.getInstance(RequerimentoDefesaPrevia.class)
                    .addProperties(new HQLProperties(RequerimentoDefesaPrevia.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoDefesaPrevia.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoDefesaPrevia.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoDefesaPrevia.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, RequerimentoDefesaPrevia.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoDefesaPrevia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();

            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void enableEstabelecimentoNome(AjaxRequestTarget target) {
        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getNome() != null) {
            if (target != null) {
                autoCompleteConsultaEstabelecimento.limpar(target);
            }
            txtNome.setEnabled(true);
            autoCompleteConsultaEstabelecimento.setEnabled(false);
        } else if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getEstabelecimento() != null) {
            if (target != null) {
                txtNome.limpar(target);
            }
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            txtNome.setEnabled(false);
        } else {
            if (target != null) {
                autoCompleteConsultaEstabelecimento.limpar(target);
                txtNome.limpar(target);
            }
            autoCompleteConsultaEstabelecimento.setEnabled(true);
            txtNome.setEnabled(true);
        }

        if (target != null) {
            target.add(autoCompleteConsultaEstabelecimento);
            target.add(txtNome);
        }
    }

    private void enableEnderecoTelefoneCnpjCpf(AjaxRequestTarget target) {
        if (target != null) {
            pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().limpar(target);
            txtTelefone.limpar(target);
            txtCnpjCpf.limpar(target);
            dropDownTipoPessoa.limpar(target);
        }

        if (getForm().getModel().getObject().getRequerimentoDefesaPrevia().getNome() != null) {
            pnlVigilanciaEndereco.setEnabled(true);
            txtTelefone.setEnabled(true);
            txtCnpjCpf.setEnabled(true);
            dropDownTipoPessoa.setEnabled(true);
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(true);

            if (target != null) {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setRequired(target, true);
                target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
            } else {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setRequired(true);
            }
            if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                txtCnpjCpf.remove(attributeModifierCnpj);
            }
            if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                if (target != null) {
                    txtCnpjCpf.limpar(target);
                }
                txtCnpjCpf.add(attributeModifierCpf);
            }
        } else {
            pnlVigilanciaEndereco.setEnabled(false);
            txtTelefone.setEnabled(false);
            txtCnpjCpf.setEnabled(false);
            dropDownTipoPessoa.setEnabled(false);
            pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setEnabled(true);
            if (target != null) {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setRequired(target, false);
            } else {
                pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia().setRequired(false);
            }

            Estabelecimento e = getForm().getModel().getObject().getRequerimentoDefesaPrevia().getEstabelecimento();
            if (e != null) {
                if (Estabelecimento.TipoPessoa.FISICA.value().equals(e.getTipoPessoa())) {
                    if (target != null) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                    }
                    if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                        txtCnpjCpf.remove(attributeModifierCnpj);
                    }
                    if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                        if (target != null) {
                            txtCnpjCpf.limpar(target);
                        }
                        txtCnpjCpf.add(attributeModifierCpf);
                    }
                } else {
                    if (target != null) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                    }
                    if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                        txtCnpjCpf.remove(attributeModifierCpf);
                    }
                    if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                        if (target != null) {
                            txtCnpjCpf.limpar(target);
                        }
                        txtCnpjCpf.add(attributeModifierCnpj);
                    }
                }

                if (e.getVigilanciaEndereco() != null) {
                    VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaEndereco.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, e.getVigilanciaEndereco().getCodigo()))
                            .start().getVO();

                    getForm().getModel().getObject().getRequerimentoDefesaPrevia().setVigilanciaEndereco(ve);
                }
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().setTelefone(e.getTelefone());
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().setCnpjCpf(e.getCnpjCpf());
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().setTipoPessoa(e.getTipoPessoa());

                ConfiguracaoVigilancia configuracaoVigilancia = null;
                try {
                    configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(),ex);
                }
                if (configuracaoVigilancia != null && configuracaoVigilancia.getSetorVigilanciaDefesaPrevia() == null) {
                    EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                            .addProperties(new HQLProperties(SetorVigilancia.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, RepositoryComponentDefault.SIM_LONG))
                            .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, e))
                            .setMaxResults(1).start().getVO();

                    if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null
                            && estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia() != null) {
                        EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                        elo.setSetorVigilancia(estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia());
                        pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
                    }
                }
            } else {
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().setVigilanciaEndereco(null);
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().setTelefone(null);
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().setCnpjCpf(null);
                getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().setTipoPessoa((Long) RequerimentoVigilancia.TipoPessoa.FISICA.value());
            }
        }

        if (target != null) {
            target.appendJavaScript(JScript.initMasks());
            target.add(pnlVigilanciaEndereco);
            target.add(txtTelefone);
            target.add(dropDownTipoPessoa);
            target.add(txtCnpjCpf);
            target.add(pnlDadosComumRequerimentoVigilancia.getAutoCompleteConsultaSetorVigilancia());
        }
        txtCnpjCpf.addRequiredClass();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    @Override
    public Permissions getAction() {
        return Permissions.DEFESA_PREVIA;
    }

    private void atualizarDadosComuns() {
        getForm().getModel().getObject().getRequerimentoDefesaPrevia().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}