package br.com.celk.view.hospital.quartosleitos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.hospital.quartosleitos.autocomplete.AutoCompleteConsultaEspecialidadeLeito;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.EspecialidadeLeito;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.equalTo;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroQuartosLeitosStep2 extends BasePage {

    private Form form;
    private QuartoInternacao quartoInternacao;
    private WebMarkupContainer containerLeitos;
    private CompoundPropertyModel<LeitoQuarto> modelLeito;
    private InputField txtLeito;
    private AutoCompleteConsultaEspecialidadeLeito autoCompleteConsultaEspecialidadeLeito;
    private LeitoQuarto itemEdicao;
    private Table<LeitoQuarto> table;
    private List<LeitoQuarto> lstLeitoQuarto = new ArrayList();
    private DlgMotivoObject<LeitoQuarto> dlgMotivo;

    public CadastroQuartosLeitosStep2(QuartoInternacao quartoInternacao) {
        this.quartoInternacao = quartoInternacao;
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(quartoInternacao));
        form.add(new DisabledInputField(QuartoInternacao.PROP_DESCRICAO));
        form.add(new DisabledInputField(VOUtils.montarPath(QuartoInternacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));

        LeitoQuarto proxyContainer = on(LeitoQuarto.class);

        form.add(containerLeitos = new WebMarkupContainer("containerLeitos", modelLeito = new CompoundPropertyModel(new LeitoQuarto())));
        containerLeitos.setOutputMarkupId(true);
        containerLeitos.add(txtLeito = new InputField(path(proxyContainer.getDescricao())));
        containerLeitos.add(DropDownUtil.getIEnumDropDown(path(proxyContainer.getTipoLeito()), LeitoQuarto.TipoLeito.values()));
        containerLeitos.add(DropDownUtil.getIEnumDropDown(path(proxyContainer.getSexo()), LeitoQuarto.Sexo.values()));
        containerLeitos.add(new InputField(path(proxyContainer.getNumeroLeitoAih())));
        containerLeitos.add(autoCompleteConsultaEspecialidadeLeito = new AutoCompleteConsultaEspecialidadeLeito(path(proxyContainer.getEspecialidadeLeito())));
        containerLeitos.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAction(target);
            }
        });
        containerLeitos.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparAction(target);
            }
        });

        containerLeitos.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarAction(target);
            }
        }));

        add(form);
        carregaLeitos();
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroLeitoQuarto");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtLeito;
    }

    private void carregaLeitos() {
        if (quartoInternacao.getCodigo() != null) {
            LeitoQuarto proxy = on(LeitoQuarto.class);
            lstLeitoQuarto = LoadManager.getInstance(LeitoQuarto.class)
                    .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                    .addProperties(new HQLProperties(EspecialidadeLeito.class, path(proxy.getEspecialidadeLeito())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSituacao()), BuilderQueryCustom.QueryParameter.DIFERENTE, LeitoQuarto.Situacao.EXCLUIDO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getQuartoInternacao()), quartoInternacao))
                    .start().getList();
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstLeitoQuarto;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        LeitoQuarto proxy = on(LeitoQuarto.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("leito"), proxy.getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoDescricao()));
        columns.add(createColumn(bundle("numeroLeitoAih"), proxy.getNumeroLeitoAih()));
        columns.add(createColumn(bundle("especialidade"), proxy.getEspecialidadeLeito().getDescricao()));
        columns.add(createColumn(bundle("tipoLeito"), proxy.getDescricaoTipoLeito()));
        columns.add(createColumn(bundle("sexo"), proxy.getDescricaoSexo()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<LeitoQuarto>() {
            @Override
            public void customizeColumn(LeitoQuarto rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LeitoQuarto>() {
                    @Override
                    public void action(AjaxRequestTarget target, LeitoQuarto modelObject) throws ValidacaoException, DAOException {
                        editarAction(target, modelObject);
                    }
                }).setEnabled(!LeitoQuarto.Situacao.EXCLUIDO.value().equals(rowObject.getSituacao()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<LeitoQuarto>() {
                    @Override
                    public void action(AjaxRequestTarget target, LeitoQuarto modelObject) throws ValidacaoException, DAOException {
                        removerAction(target, modelObject);
                    }
                }).setEnabled(LeitoQuarto.Situacao.LIBERADO.value().equals(Coalesce.asLong(rowObject.getSituacao(), LeitoQuarto.Situacao.LIBERADO.value())));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<LeitoQuarto>() {
                    @Override
                    public void action(AjaxRequestTarget target, LeitoQuarto modelObject) throws ValidacaoException, DAOException {
                        viewDlgMotivo(target, modelObject);
                    }
                }).setQuestionDialogBundleKey(null)
                        .setTitleBundleKey("desativarLeitoTemporariamente")
                        .setVisible(LeitoQuarto.Situacao.LIBERADO.value().equals(rowObject.getSituacao()) && rowObject.getCodigo() != null);

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<LeitoQuarto>() {
                    @Override
                    public void action(AjaxRequestTarget target, LeitoQuarto modelObject) throws ValidacaoException, DAOException {
                        reativarAction(target, modelObject);
                    }
                }).setQuestionDialogBundleKey("desejaRealmenteReativarEsteLeito")
                        .setTitleBundleKey("reativarLeito")
                        .setVisible(LeitoQuarto.Situacao.DESATIVADO.value().equals(rowObject.getSituacao()));
            }
        };
    }

    private void editarAction(AjaxRequestTarget target, LeitoQuarto leitoQuarto) {
        itemEdicao = leitoQuarto;
        limpar(target);
        setModelLeito(target, (LeitoQuarto) SerializationUtils.clone(leitoQuarto));
    }

    private void removerAction(AjaxRequestTarget target, LeitoQuarto leitoQuarto) {
        if (leitoQuarto.getCodigo() == null) {
            for (int i = 0; i < lstLeitoQuarto.size(); i++) {
                if (lstLeitoQuarto.get(i) == leitoQuarto) {
                    lstLeitoQuarto.remove(i);
                }
            }
        } else {
            leitoQuarto.setSituacao(LeitoQuarto.Situacao.EXCLUIDO.value());
        }

        table.update(target);
    }

    private void reativarAction(AjaxRequestTarget target, LeitoQuarto leitoQuarto) throws ValidacaoException {
        if (!QuartoInternacao.Situacao.ATIVO.value().equals(quartoInternacao.getSituacao())) {
            throw new ValidacaoException(bundle("leitoNaoPodeSerReativadoQuartoSituacao", quartoInternacao.getSituacaoDescricao()));
        }

        leitoQuarto.setSituacao(LeitoQuarto.Situacao.LIBERADO.value());
        table.update(target);
        JScript.setDirtyForm(target, form);
    }

    private void viewDlgMotivo(AjaxRequestTarget target, LeitoQuarto leitoQuarto) {
        if (dlgMotivo == null) {
            addModal(target, dlgMotivo = new DlgMotivoObject<LeitoQuarto>(newModalId(), bundle("desativarQuartoTemporariamente")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, LeitoQuarto leitoQuarto) throws ValidacaoException, DAOException {
                    leitoQuarto.setSituacao(LeitoQuarto.Situacao.DESATIVADO.value());
                    leitoQuarto.setMotivo(motivo);
                    leitoQuarto.setDataDesativacao(DataUtil.getDataAtual());

                    table.update(target);
                    JScript.setDirtyForm(target, form);
                }
            });
        }

        dlgMotivo.setObject(leitoQuarto);
        dlgMotivo.show(target);
    }

    private void limparAction(AjaxRequestTarget target) {
        setModelLeito(target, new LeitoQuarto());
        limpar(target);
    }

    private void adicionarAction(AjaxRequestTarget target) throws ValidacaoException {
        LeitoQuarto leitoQuarto = modelLeito.getObject();
        validarAdicionar(target, leitoQuarto);

        if (QuartoInternacao.Situacao.DESATIVADO.value().equals(quartoInternacao.getSituacao())) {
            leitoQuarto.setSituacao(LeitoQuarto.Situacao.DESATIVADO.value());
        } else if (QuartoInternacao.Situacao.ISOLADO.value().equals(quartoInternacao.getSituacao())) {
            leitoQuarto.setSituacao(LeitoQuarto.Situacao.ISOLADO.value());
        } else {
            leitoQuarto.setSituacao(LeitoQuarto.Situacao.LIBERADO.value());
        }

        lstLeitoQuarto.add(leitoQuarto);
        limparAction(target);
        table.update(target);
        target.focusComponent(txtLeito);
    }

    private void validarAdicionar(AjaxRequestTarget target, LeitoQuarto leitoQuarto) throws ValidacaoException {
        if ("".equals(Coalesce.asString(leitoQuarto.getDescricao()))) {
            target.focusComponent(txtLeito);
            throw new ValidacaoException(bundle("informeDescricaoLeito"));
        }

        boolean existsLeitoList;
        if (itemEdicao != null) {
            for (int i = 0; i < lstLeitoQuarto.size(); i++) {
                LeitoQuarto item = lstLeitoQuarto.get(i);

                if (item == itemEdicao) {
                    List<LeitoQuarto> listAux = new ArrayList();
                    listAux.addAll(lstLeitoQuarto);
                    listAux.remove(i);

                    existsLeitoList = exists(listAux, having(on(LeitoQuarto.class).getDescricao(), equalTo(leitoQuarto.getDescricao())));

                    if (existsLeitoList) {
                        throw new ValidacaoException(bundle("leitoJaInformado"));
                    }

                    itemEdicao = null;
                    lstLeitoQuarto.remove(i);
                    break;
                }
            }
        } else {
            existsLeitoList = exists(lstLeitoQuarto, having(on(LeitoQuarto.class).getDescricao(), equalTo(leitoQuarto.getDescricao())));
            if (existsLeitoList) {
                throw new ValidacaoException(bundle("leitoJaInformado"));
            }
        }
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaEspecialidadeLeito.limpar(target);
    }

    private void setModelLeito(AjaxRequestTarget target, LeitoQuarto leitoQuarto) {
        modelLeito.setObject(leitoQuarto);
        target.add(containerLeitos);
    }

    private void salvarAction(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(HospitalFacade.class).salvarQuartosLeitos(lstLeitoQuarto, quartoInternacao);
        Page page = new ConsultaQuartosLeitos();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
    }

}
