package br.com.celk.view.materiais.dispensacao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlRegistrarAtendimento extends Panel {

    private CompoundPropertyModel model;
    private String evolucao;
    InputArea multiLineText;

    public PnlRegistrarAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(this));

        
        form.add(multiLineText = new InputArea("evolucao"));
        multiLineText.setOutputMarkupId(true);
        multiLineText.setEscapeModelStrings(false);

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onSalvar(target, evolucao);
            }
        });

        form.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onLimpar(target);
            }
        });

        add(form);
    }

    public void limpar(AjaxRequestTarget target) {
        evolucao = null;
        target.add(multiLineText);
    }

    public abstract void onSalvar(AjaxRequestTarget target, String evolucao) throws DAOException, ValidacaoException;
    public abstract void onLimpar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

}
