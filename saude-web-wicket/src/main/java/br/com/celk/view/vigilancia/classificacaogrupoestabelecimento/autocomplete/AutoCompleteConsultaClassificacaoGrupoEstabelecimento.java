package br.com.celk.view.vigilancia.classificacaogrupoestabelecimento.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.classificacaogrupoestabelecimento.autocomplete.restricaocontainer.RestricaoContainerClassificacaoGrupoEstabelecimento;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaClassificacaoGrupoEstabelecimento extends AutoCompleteConsulta<ClassificacaoGrupoEstabelecimento> { 

    public AutoCompleteConsultaClassificacaoGrupoEstabelecimento(String id) {
        super(id);
    }

    public AutoCompleteConsultaClassificacaoGrupoEstabelecimento(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaClassificacaoGrupoEstabelecimento(String id, IModel<ClassificacaoGrupoEstabelecimento> model) {
        super(id, model);
    }

    public AutoCompleteConsultaClassificacaoGrupoEstabelecimento(String id, IModel<ClassificacaoGrupoEstabelecimento> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(ClassificacaoGrupoEstabelecimento.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(ClassificacaoGrupoEstabelecimento.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerClassificacaoGrupoEstabelecimento(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<ClassificacaoGrupoEstabelecimento, QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarClassificacaoGrupoEstabelecimento(dataPaging);
                    }

                    @Override
                    public QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam param = new QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaClassificacaoGrupoEstabelecimentoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(ClassificacaoGrupoEstabelecimento.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return ClassificacaoGrupoEstabelecimento.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("classificacaoGrupoEstabelecimento");
    }

}
