package br.com.celk.view.hospital.manutencaoalta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.hospital.manutencaoalta.dlg.DlgManutencaoAlta;
import br.com.celk.view.hospital.manutencaoalta.dlg.DlgOcorrenciasAlta;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AltaOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ManutencaoAltaPage extends BasePage {

    private String paciente;
    private TipoAtendimento tipoAtendimento;
    private Long atendimento;
    private DatePeriod periodo;
    private Long situacao;
    private String tipoPeriodo;
    private DropDown dropDownTipoAtendimento;
    private DropDown<Long> dropDownSituacao;
    private DropDown<String> dropDownTipoPeriodo;
    private PageableTable<AtendimentoInformacao> pageableTable;
    private DlgManutencaoAlta dlgManutencaoAlta;
    private DlgOcorrenciasAlta dlgOcorrenciasAlta;

    public ManutencaoAltaPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(new InputField("paciente"));
        form.add(getDropDownTipoAtendimento());
        form.add(new InputField("atendimento"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(getDropDownTipoPeriodo());
        form.add(getDropDownSituacao());

        form.add(pageableTable = new PageableTable("table", getColumns(), getPagerProvider()));
        pageableTable.setScrollX("1880px");
        pageableTable.setScrollCollapse(false);

        form.add(new ProcurarButton<List<BuilderQueryCustom.QueryParameter>>("btnProcurar", pageableTable) {
            @Override
            public List<BuilderQueryCustom.QueryParameter> getParam() {
                return ManutencaoAltaPage.this.getParam();
            }
        });

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        AtendimentoInformacao proxy = on(AtendimentoInformacao.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("atendimento"), proxy.getAtendimentoPrincipal().getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getNomePaciente()));
        columns.add(createSortableColumn(bundle("idade"), proxy.getUsuarioCadsus().getDataNascimento(), proxy.getDescricaoIdade()));
        columns.add(createSortableColumn(bundle("tipoAtendimento"), proxy.getTipoAtendimentoFaturamento().getDescricao()));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataChegada"), path(proxy.getDataChegada()), path(proxy.getDataChegada())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataSaida"), path(proxy.getDataSaida()), path(proxy.getDataSaida())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createSortableColumn(bundle("dataDeAlta"), proxy.getAtendimentoAlta().getDataAlta()));
        columns.add(createSortableColumn(bundle("motivoAlta"), proxy.getAtendimentoAlta().getMotivoAlta(), proxy.getAtendimentoAlta().getMotivoFormatado()));
        columns.add(createSortableColumn(bundle("cid"), proxy.getAtendimentoAlta().getCid().getDescricao(), proxy.getAtendimentoAlta().getCid().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("profissionalAlta"), proxy.getProfissionalAlta().getNome()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AtendimentoInformacao>() {
            @Override
            public void customizeColumn(AtendimentoInformacao atendimentoInformacao) {
                addAction(ActionType.MANUTENCAO, atendimentoInformacao, new IModelAction<AtendimentoInformacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoInformacao modelObject) throws ValidacaoException, DAOException {
                        viewDlgManutencaoAlta(target, modelObject);
                    }
                }).setTitleBundleKey("manutencaoAlta");

                boolean existsOcorrencia = LoadManager.getInstance(AltaOcorrencia.class)
                        .addProperty(AltaOcorrencia.PROP_CODIGO)
                        .addParameter(new QueryCustom.QueryCustomParameter(AltaOcorrencia.PROP_ATENDIMENTO_ALTA, atendimentoInformacao.getAtendimentoAlta()))
                        .exists();

                addAction(ActionType.CONSULTAR, atendimentoInformacao, new IModelAction<AtendimentoInformacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoInformacao modelObject) throws ValidacaoException, DAOException {
                        viewDlgOcorrenciasAlta(target, modelObject);
                    }
                }).setTitleBundleKey("ocorrenciasAlta")
                        .setIcon(Icon.NOTEPAD)
                        .setEnabled(existsOcorrencia);
            }
        };
    }

    public IPagerProvider getPagerProvider() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return AtendimentoInformacao.class;
            }

            @Override
            public String[] getProperties() {
                AtendimentoInformacao proxy = on(AtendimentoInformacao.class);

                return VOUtils.mergeProperties(new HQLProperties(AtendimentoInformacao.class).getProperties(),
                        new String[]{
                            path(proxy.getTipoAtendimentoFaturamento().getDescricao()),
                            path(proxy.getAtendimentoAlta().getDataAlta()),
                            path(proxy.getAtendimentoAlta().getMotivoAlta()),
                            path(proxy.getAtendimentoAlta().getCid().getCodigo()),
                            path(proxy.getAtendimentoAlta().getCid().getDescricao()),
                            path(proxy.getProfissionalAlta().getNome()),
                            path(proxy.getUsuarioCadsus().getDataNascimento())
                        });
            }

        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtendimentoInformacao.PROP_NOME_PACIENTE, true);
            }
        };
    }

    public List<BuilderQueryCustom.QueryParameter> getParam() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        AtendimentoInformacao proxy = on(AtendimentoInformacao.class);

        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoAlta().getCodigo()), QueryCustom.QueryCustomParameter.IS_NOT_NULL));

        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getNomePaciente()), BuilderQueryCustom.QueryParameter.ILIKE, paciente));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getTipoAtendimentoFaturamento()), tipoAtendimento));
        parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoPrincipal().getCodigo()), atendimento));
        parameters.add(new QueryCustom.QueryCustomParameter(tipoPeriodo, periodo));

        if (situacao != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getStatusAtendimento()), situacao));
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getStatusAtendimento()), QueryCustom.QueryCustomParameter.IN, Arrays.asList(AtendimentoInformacao.StatusAtendimento.ABERTO.value(), AtendimentoInformacao.StatusAtendimento.CONCLUIDO.value())));
        }

        return parameters;
    }

    private void viewDlgManutencaoAlta(AjaxRequestTarget target, AtendimentoInformacao atendimentoInformacao) {
        if (dlgManutencaoAlta == null) {
            addModal(target, dlgManutencaoAlta = new DlgManutencaoAlta(newModalId()) {
                @Override
                public void updateTable(AjaxRequestTarget target) {
                    pageableTable.update(target);
                }
            });
        }
        dlgManutencaoAlta.show(target, atendimentoInformacao.getAtendimentoAlta().getCodigo());
    }

    private void viewDlgOcorrenciasAlta(AjaxRequestTarget target, AtendimentoInformacao atendimentoInformacao) {
        if (dlgOcorrenciasAlta == null) {
            addModal(target, dlgOcorrenciasAlta = new DlgOcorrenciasAlta(newModalId()));
        }
        dlgOcorrenciasAlta.show(target, atendimentoInformacao.getAtendimentoAlta().getCodigo());
    }

    private DropDown getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("ambas"));
            dropDownSituacao.addChoice(AtendimentoInformacao.StatusAtendimento.ABERTO.value(), AtendimentoInformacao.StatusAtendimento.ABERTO.descricao());
            dropDownSituacao.addChoice(AtendimentoInformacao.StatusAtendimento.CONCLUIDO.value(), AtendimentoInformacao.StatusAtendimento.CONCLUIDO.descricao());
        }
        return dropDownSituacao;
    }

    private DropDown getDropDownTipoPeriodo() {
        if (dropDownTipoPeriodo == null) {
            dropDownTipoPeriodo = new DropDown<String>("tipoPeriodo");
            dropDownTipoPeriodo.addChoice(AtendimentoInformacao.PROP_DATA_CHEGADA, BundleManager.getString("dataChegada"));
            dropDownTipoPeriodo.addChoice(VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_ALTA, AtendimentoAlta.PROP_DATA_ALTA), BundleManager.getString("dataDeAlta"));
            dropDownTipoPeriodo.addChoice(AtendimentoInformacao.PROP_DATA_SAIDA, BundleManager.getString("dataSaida"));
        }
        return dropDownTipoPeriodo;
    }

    public DropDown getDropDownTipoAtendimento() {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown("tipoAtendimento");
            dropDownTipoAtendimento.addChoice(null, bundle("todos"));

            List<TipoAtendimento> listTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoAtendimento.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();

            for (TipoAtendimento tipoAtendimento : listTipoAtendimento) {
                dropDownTipoAtendimento.addChoice(tipoAtendimento, tipoAtendimento.getDescricao());
            }
        }

        return dropDownTipoAtendimento;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("manutencaoDadosAlta");
    }
}
