package br.com.celk.view.basico.unidade.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.basico.unidade.customize.CustomizeConsultaUnidade;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaUnidade extends PnlConsulta<Unidade> {

    public PnlConsultaUnidade(String id, IModel<Unidade> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaUnidade(String id, IModel<Unidade> model) {
        super(id, model);
    }

    public PnlConsultaUnidade(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaUnidade(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaUnidade();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("unidades");
    }

}
