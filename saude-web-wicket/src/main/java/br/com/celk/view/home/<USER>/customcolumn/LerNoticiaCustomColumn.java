package br.com.celk.view.home.noticia.customcolumn;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.home.noticia.NoticiaPage;
import br.com.celk.view.home.noticia.TodasNoticiasPage;
import br.com.ksisolucoes.vo.comunicacao.Noticia;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class LerNoticiaCustomColumn extends Panel{

    private Link linkLerNoticia;
    
    private Noticia noticia;
    
    public LerNoticiaCustomColumn(String id, Noticia noticia) {
        super(id);
        this.noticia = noticia;
        init();
    }
    
    private void init(){
        add(linkLerNoticia = new Link("linkLerNoticia") {

            @Override
            public void onClick() {
                setResponsePage(new NoticiaPage(noticia) {

                    @Override
                    public Class getVoltarClass() {
                        return TodasNoticiasPage.class;
                    }
                });
            }

        });
        
        linkLerNoticia.add(new AttributeModifier("title", BundleManager.getString("lerNoticia")));
    }

}
