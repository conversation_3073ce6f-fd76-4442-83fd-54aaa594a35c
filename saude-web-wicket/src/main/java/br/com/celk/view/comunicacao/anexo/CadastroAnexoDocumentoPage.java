package br.com.celk.view.comunicacao.anexo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.unidadesaude.anexo.PanelArquivosAnexados;
import br.com.celk.view.unidadesaude.tipoanexo.autocomplete.AutoCompleteConsultaTipoAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.AnexosDocumentoDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.comunicacao.base.AnexoDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.hospital.SolicitacaoAgendamentoAnexo;
import br.com.ksisolucoes.vo.prontuario.hospital.AihAnexo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.MultiFileUploadField;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroAnexoDocumentoPage extends BasePage {

    private CompoundPropertyModel<AnexosDocumentoDTO> modelAnexo;
    private AutoCompleteConsultaTipoAnexo autoCompleteConsultaTipoAnexo;
    private InputField txtKeyword;
    private DateChooser txtDataDocumento;
    private InputArea txaDescricao;
    private MultiFileUploadField uploadField;
    private final Collection<FileUpload> uploads = new ArrayList();
    private WebMarkupContainer containerAnexos;
    private RepeatingView repeaterAnexo;

    public CadastroAnexoDocumentoPage() {
        init(null, false);
    }

    public CadastroAnexoDocumentoPage(Long codigo, boolean viewOnly) {
        init(codigo, viewOnly);
    }

    private void init(Long codigo, final boolean viewOnly) {
        AnexosDocumentoDTO proxy = on(AnexosDocumentoDTO.class);

        Form form = new Form("form");

        WebMarkupContainer container = new WebMarkupContainer("container", modelAnexo = new CompoundPropertyModel(load(codigo)));
        container.setOutputMarkupId(true);
        container.setEnabled(!viewOnly);


        container.add(txaDescricao = new InputArea(path(proxy.getAnexoDocumento().getDescricao())));

        Form formAnexo = new Form("formAnexo");
        formAnexo.setVisible(!viewOnly);
        formAnexo.setMultiPart(true);
        formAnexo.add(uploadField = new MultiFileUploadField("uploads", new PropertyModel(this, "uploads"), 1));
        uploadField.setOutputMarkupId(true);
        container.add(formAnexo);

        form.add(container);

        form.add(containerAnexos = new WebMarkupContainer("containerAnexos"));
        containerAnexos.setOutputMarkupId(true);
        containerAnexos.add(repeaterAnexo = new RepeatingView("repeaterAnexo"));

        populateArquivosAnexados(viewOnly);

        form.add(new VoltarButton("btnVoltar"));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        }.setVisible(!viewOnly));

        add(form);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (uploads.isEmpty() && (modelAnexo.getObject() == null || modelAnexo.getObject().getAnexoDocumento() == null || modelAnexo.getObject().getAnexoDocumento().getGerenciadorArquivo() == null)) {
            throw new ValidacaoException(bundle("msgNecessarioAnexarPeloMenosArquivoPoderSalvar"));
        }

        if (modelAnexo.getObject() == null || modelAnexo.getObject().getAnexoDocumento() == null || modelAnexo.getObject().getAnexoDocumento().getDescricao() == null) {
            throw new ValidacaoException(bundle("msgNecessarioAnexarDescricaoPoderSalvar"));
        }

        if(uploads.isEmpty()) {
            AnexosDocumentoDTO dto = modelAnexo.getObject();
            AnexoDocumento anexoDocumento = BOFactoryWicket.getBO(ComunicacaoFacade.class).salvarAnexosDocumento(dto);

            ConsultaAnexoDocumentoPage page = new ConsultaAnexoDocumentoPage();
            getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(AnexoDocumento.class, anexoDocumento));
            setResponsePage(page);
        }
        for (FileUpload upload : uploads) {
            if (upload == null) {
                throw new ValidacaoException(bundle("msgNecessarioAnexarPeloMenosArquivoPoderSalvar"));
            }

            if (modelAnexo.getObject() != null && modelAnexo.getObject().getAnexoDocumento() != null && modelAnexo.getObject().getAnexoDocumento().getGerenciadorArquivo() != null &&
                    !upload.getClientFileName().toLowerCase().equals(modelAnexo.getObject().getAnexoDocumento().getGerenciadorArquivo().getNomeArquivo().toLowerCase())) {
                throw new ValidacaoException(bundle("msgPermitidoSomente1ArquivoPorCadastro"));
            }

            if ((upload.getSize() / 1024) > 20000) {
                throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexoXMB", 20));
            }

            AnexosDocumentoDTO dto = modelAnexo.getObject();

            String clientFileName = upload.getClientFileName();
            if (clientFileName.toLowerCase().endsWith(".pdf") || clientFileName.toLowerCase().endsWith(".png") || clientFileName.toLowerCase().endsWith(".jpg")) {
                try {
                    File newFile = File.createTempFile("anexo", clientFileName);
                    upload.writeTo(newFile);

                    MensagemAnexoDTO anexo = new MensagemAnexoDTO();
                    anexo.setNomeArquivoOriginal(upload.getClientFileName());
                    anexo.setNomeArquivoUpload(newFile.getAbsolutePath());
                    dto.setAnexo(anexo);
                } catch (IOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            } else {
                throw new ValidacaoException(bundle("msgExistemAnexosTiposNaoPermitidos"));
            }

            AnexoDocumento anexoDocumento = BOFactoryWicket.getBO(ComunicacaoFacade.class).salvarAnexosDocumento(dto);

            ConsultaAnexoDocumentoPage page = new ConsultaAnexoDocumentoPage();
            getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(AnexoDocumento.class, anexoDocumento));
            setResponsePage(page);
        }
    }

    private void limpar(AjaxRequestTarget target) {

        modelAnexo.setObject(new AnexosDocumentoDTO());
        modelAnexo.getObject().setAnexoDocumento(new AnexoDocumento());

        txaDescricao.limpar(target);
        uploads.clear();
        repeaterAnexo.removeAll();
        target.add(containerAnexos);
    }

    private AnexosDocumentoDTO load(Long codigo) {
        AnexosDocumentoDTO dto = new AnexosDocumentoDTO();

        if (codigo != null) {
            AnexoDocumento anexoDocumento = LoadManager.getInstance(AnexoDocumento.class)
                    .addProperties(new HQLProperties(AnexoDocumento.class).getProperties())
                    .addProperties(new HQLProperties(GerenciadorArquivo.class, AnexoDocumento.PROP_GERENCIADOR_ARQUIVO).getProperties())
                    .setId(codigo)
                    .start().getVO();
            dto.setAnexoDocumento(anexoDocumento);
        }

        return dto;
    }

    private void populateArquivosAnexados(final boolean viewOnly) {
        if (modelAnexo.getObject() != null && modelAnexo.getObject().getAnexoDocumento() != null) {
            repeaterAnexo.add(new PanelArquivosAnexados(repeaterAnexo.newChildId(), modelAnexo.getObject().getAnexoDocumento(), true) {
                @Override
                public void removerAnexo(AjaxRequestTarget target, AnexoPacienteElo elo) {

                }

                @Override
                public void removerAnexo(AjaxRequestTarget target, SolicitacaoAgendamentoAnexo anexo) throws ValidacaoException, DAOException {
                }
                public void removerAnexo(AjaxRequestTarget target, AihAnexo anexo) throws ValidacaoException, DAOException {
                }

                @Override
                public void removerAnexoDocumento(AjaxRequestTarget target, AnexoDocumento anexoDocumento) {
                    modelAnexo.getObject().getAnexoDocumento().setGerenciadorArquivo(null);
                    repeaterAnexo.remove(this);
                    target.add(containerAnexos);
                }

                @Override
                public boolean isViewOnly() {
                    return viewOnly;
                }
            });
        }
    }


    @Override
    public String getTituloPrograma() {
        return bundle("cadastroAnexos");
    }
}
