package br.com.celk.view.basico.enderecoestruturado;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Cidade;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 * <AUTHOR>
 */
public abstract class PnlGerarEderecoEstruturado extends Panel {

    private final String IMG_WARN = "img-question";
    private MultiLineLabel messageLabel;
    private WebMarkupContainer image;

    public PnlGerarEderecoEstruturado(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(image = new WebMarkupContainer("img"));
        form.add(messageLabel = new MultiLineLabel("messageLabel"));
        image.add(new AttributeModifier("class", IMG_WARN));
        messageLabel.setDefaultModel(new Model<String>(buildMsgDlg()));

        form.add(new AbstractAjaxButton("btnGerar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validar();
                gerarEnderecoEstruturado(target);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlGerarEderecoEstruturado.this.onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    private void validar() throws ValidacaoException {
        boolean utilizaEnderecoEstruturado = false;
        Cidade cidadeEnderecoEstruturado = null;
        try {
            utilizaEnderecoEstruturado = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("utilizaEnderecoEstruturado"));
            cidadeEnderecoEstruturado = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("MunicipioComEnderecoEstruturado");
        } catch (ValidacaoRuntimeException | DAOException e) {
            Loggable.log.error(e.getMessage(), e);
            warn(e.getMessage());
        }
        if (!utilizaEnderecoEstruturado) {
            throw new ValidacaoException("Verifique o parâmetro GEM 'utilizaEnderecoEstruturado'.");
        }
        if (cidadeEnderecoEstruturado == null) {
            throw new ValidacaoException("Verifique o parâmetro GEM 'MunicipioComEnderecoEstruturado'.");
        }
    }

    private void gerarEnderecoEstruturado(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactory.getBO(UsuarioCadsusFacade.class).processarCriacaoEnderecoEstruturado(ApplicationSession.get().getSessaoAplicacao().getUsuario());
        PnlGerarEderecoEstruturado.this.onConfirmar(target);
        PnlGerarEderecoEstruturado.this.onFechar(target);
    }

    private String buildMsgDlg() {
        StringBuilder sb = new StringBuilder();
        sb.append("Atenção! Esse processo é irreversível.").append("\n\t");
        sb.append("\n");
        sb.append("Certifique-se de que os parâmetros 'utilizaEnderecoEstruturado' e 'MunicipioComEnderecoEstruturado' estejam corretamente preenchidos.").append("\n\t");
        sb.append("\n");
        sb.append("Deseja prosseguir com o processo?");
        return sb.toString();
    }

    public abstract void onFechar(AjaxRequestTarget target);

    public abstract void onConfirmar(AjaxRequestTarget target);
}
