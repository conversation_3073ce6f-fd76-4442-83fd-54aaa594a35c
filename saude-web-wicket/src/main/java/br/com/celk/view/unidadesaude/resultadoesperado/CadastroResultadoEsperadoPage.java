package br.com.celk.view.unidadesaude.resultadoesperado;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ResultadoEsperado;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.RequiredTextField;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastroResultadoEsperadoPage extends CadastroPage<ResultadoEsperado> {

    private ResultadoEsperado resultadoEsperado;

    public CadastroResultadoEsperadoPage(ResultadoEsperado resultadoEsperado, boolean viewOnly) {
        super(resultadoEsperado, viewOnly);
        this.resultadoEsperado = resultadoEsperado;
    }

    public CadastroResultadoEsperadoPage(ResultadoEsperado resultadoEsperado) {
        super(resultadoEsperado);
        this.resultadoEsperado = resultadoEsperado;
    }

    public CadastroResultadoEsperadoPage() {
    }

    @Override
    public void init(Form<ResultadoEsperado> form) {
        ResultadoEsperado proxy = on(ResultadoEsperado.class);

        form.add(new RequiredTextField(path(proxy.getDescricao())));
        form.add(new RequiredTextField(path(proxy.getReferencia())));
        form.add(new RequiredTextField(path(proxy.getEixoFoco())));
        form.add(new RequiredTextField(path(proxy.getEixoAcao())));
        form.add(new RequiredTextField(path(proxy.getEixoCliente())));
        form.add(new RequiredTextField(path(proxy.getEixoMeios())));
        form.add(new RequiredTextField(path(proxy.getEixoTempo())));
        form.add(new RequiredTextField(path(proxy.getEixoLocalizacao())));
    }

    @Override
    public Object salvar(ResultadoEsperado resultadoEsperado) throws DAOException, ValidacaoException {
        ValidacaoResultadoEsperado.validarReferenciaDuplicada(resultadoEsperado);
        if (!isEdicao()) {
            resultadoEsperado.setDataCadastro(DataUtil.getDataAtual());
            resultadoEsperado.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());
            resultadoEsperado.setStatus(ResultadoEsperado.Status.ATIVO.value());
        }
        return super.salvar(resultadoEsperado);
    }

    @Override
    public Class<ResultadoEsperado> getReferenceClass() {
        return ResultadoEsperado.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaResultadoEsperadoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroResultadoEsperado");
    }
}
