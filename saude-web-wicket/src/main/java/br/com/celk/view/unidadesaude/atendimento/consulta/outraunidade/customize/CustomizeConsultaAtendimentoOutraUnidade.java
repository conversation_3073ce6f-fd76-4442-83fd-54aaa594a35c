package br.com.celk.view.unidadesaude.atendimento.consulta.outraunidade.customize;

import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CustomizeConsultaAtendimentoOutraUnidade extends CustomizeConsultaAdapter {

    @Override
    public Class getClassConsulta() {
        return PacienteAtendidoOutraUnidade.class;
    }

    @Override
    public String[] getProperties() {
        PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade = on(PacienteAtendidoOutraUnidade.class);

        return VOUtils.mergeProperties(
                new String[]{
                        path(pacienteAtendidoOutraUnidade.getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getVersion()),
                        path(pacienteAtendidoOutraUnidade.getDataCadastro()),
                        path(pacienteAtendidoOutraUnidade.getSituacao()),
                        path(pacienteAtendidoOutraUnidade.getEmpresaPaciente().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getEmpresaPaciente().getDescricao()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getNomePaciente()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getDataAtendimento()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getDataAtendimento()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getProfissional().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getProfissional().getNome()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getEmpresa().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getEmpresa().getDescricao()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getNaturezaProcuraTipoAtendimento().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getTipoAtendimentoEsus()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getCodigo()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone2()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone3()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getTelefone4()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getCelular()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getEmail()),
                        path(pacienteAtendidoOutraUnidade.getAtendimento().getUsuarioCadsus().getNome()),
                });
    }
}
