package br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.restricaocontainer.RestricaoContainerTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaTipoProjetoVigilanciaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTipoProjetoVigilancia extends AutoCompleteConsulta<TipoProjetoVigilancia> {

    private Long tipo;

    public AutoCompleteConsultaTipoProjetoVigilancia(String id) {
        super(id);
    }

    public AutoCompleteConsultaTipoProjetoVigilancia(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaTipoProjetoVigilancia(String id, IModel<TipoProjetoVigilancia> model) {
        super(id, model);
    }

    public AutoCompleteConsultaTipoProjetoVigilancia(String id, IModel<TipoProjetoVigilancia> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(TipoProjetoVigilancia.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), TipoProjetoVigilancia.PROP_DESCRICAO));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerTipoProjetoVigilancia(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<TipoProjetoVigilancia, QueryConsultaTipoProjetoVigilanciaDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaTipoProjetoVigilanciaDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(VigilanciaFacade.class).consultarTipoProjetoVigilancia(dataPaging);
                    }

                    @Override
                    public QueryConsultaTipoProjetoVigilanciaDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaTipoProjetoVigilanciaDTOParam param = new QueryConsultaTipoProjetoVigilanciaDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(TipoProjetoVigilancia.PROP_DESCRICAO, true);
                    }

                    @Override
                    public void customizeParam(QueryConsultaTipoProjetoVigilanciaDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setTipo(tipo);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return TipoProjetoVigilancia.class;
            }

        };
    }

    @Override
    public String[] getPropertiesLoad() {
        TipoProjetoVigilancia proxy = on(TipoProjetoVigilancia.class);

        return VOUtils.mergeProperties(new HQLProperties(TipoProjetoVigilancia.class).getProperties(),
                new HQLProperties(Taxa.class, path(proxy.getTaxa())).getProperties());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tipoProjeto");
    }

    public AutoCompleteConsultaTipoProjetoVigilancia setTipo(Long tipo) {
        this.tipo = tipo;
        return this;
    }
}