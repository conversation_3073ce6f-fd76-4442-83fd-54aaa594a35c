package br.com.celk.view.materiais.inventarioindividual;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.lote.entrada.PnlEntradaLote;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.*;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.component.utils.parametros.ParametrosMateriaisUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.fabricante.autocomplete.AutoCompleteConsultaFabricante;
import br.com.celk.view.basico.fabricante.dialog.DlgCadastroFabricante;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.celk.view.materiais.estoque.deposito.autocomplete.AutoCompleteConsultaDeposito;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.materiais.recebimento.notafiscalentrada.CadastroImportacaoXmlNotaFiscalPageStep2;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam.Exibir;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class LancamentoInventarioMovimentoEstoquePage extends CadastroPage<LancamentoMovimentoEstoque> implements PermissionContainer {

    private Deposito deposito;
    private Produto produto;
    private String numeroDocumento;
    private Double quantidade;
    private String grupoEstoque;
    private Double precoMedio;
    private Double precoTotal;
    private String observacao;
    private Empresa empresa;
    private Fabricante fabricante;
    private List<MovimentoEstoque> movimentoEstoques = new ArrayList<MovimentoEstoque>();
    private MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
    private AutoCompleteConsultaDeposito autoCompleteConsultaDeposito;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private InputField<String> txtNumeroDocumento;
    private DoubleField txtQuantidade;
    private PnlEntradaLote pnlEntradaLote;
    private InputField<Double> txtPrecoMedio;
    private InputField<Double> txtPrecoTotal;
    private InputField<String> txtObservacao;
    private AbstractAjaxButton btnLimpar;
    private Table<MovimentoEstoque> table;
    private DlgConfirmacao dlgConfirmacaoAdicionar;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstrutura;
    private String utilizaLocalizacaoEstoque;
    private LocalizacaoEstrutura localizacaoEstrutura;
    private WebMarkupContainer containerLocalizacao;
    private AutoCompleteConsultaFabricante autoCompleteConsultaFabricante;
    private WebMarkupContainer containerFabricante;
    private DlgCadastroFabricante dlgCadastroFabricante;
    private AbstractAjaxLink btnCadFabricante;

    @Override
    public void init(Form form) {
        empresa = br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa();

        form.add(autoCompleteConsultaDeposito = new AutoCompleteConsultaDeposito("deposito", new PropertyModel<Deposito>(this, "deposito")));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto", new PropertyModel<Produto>(this, "produto")).setEmpresas(Arrays.asList(empresa)).setExibir(Exibir.SOMENTE_ATIVO));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                txtPrecoMedio.setEnabled(true);
                target.add(txtPrecoMedio);
            }
        });

        form.add(txtQuantidade = new DoubleField("quantidade", new PropertyModel<Double>(this, "quantidade")));
        form.add(txtNumeroDocumento = new InputField<String>("numeroDocumento", new PropertyModel(this, "numeroDocumento")));

        form.add(pnlEntradaLote = new PnlEntradaLote("grupoEstoque", new PropertyModel<String>(this, "grupoEstoque")));
        pnlEntradaLote.getBtnLotes().setVisible(true);
        pnlEntradaLote.setTxtQuantidade(txtQuantidade);
        pnlEntradaLote.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto);
        pnlEntradaLote.registerEvents();

        containerFabricante = new WebMarkupContainer("containerFabricante");
        containerFabricante.setOutputMarkupId(true);
        form.add(containerFabricante);

        containerFabricante.add(autoCompleteConsultaFabricante = new AutoCompleteConsultaFabricante("fabricante", new PropertyModel(this, "fabricante")));
        autoCompleteConsultaFabricante.setEnabled(false);

        containerFabricante.add(btnCadFabricante = new AbstractAjaxLink("btnCadFabricante") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgCadastroFabricante(target);
            }
        });
        btnCadFabricante.setEnabled(false);

        pnlEntradaLote.setAutoCompleteConsultaFabricante(autoCompleteConsultaFabricante);
        pnlEntradaLote.setBtnCadFabricante(btnCadFabricante);

        containerLocalizacao = new WebMarkupContainer("containerLocalizacao");

        containerLocalizacao.setOutputMarkupId(true);
        form.add(containerLocalizacao);

        containerLocalizacao.add(autoCompleteConsultaLocalizacaoEstrutura = new AutoCompleteConsultaLocalizacaoEstrutura("localizacaoEstrutura", new PropertyModel<LocalizacaoEstrutura>(this, "localizacaoEstrutura"), true));
        autoCompleteConsultaLocalizacaoEstrutura.setExibirApenasVisivelSim(true);

        form.add(txtPrecoMedio = new DoubleField("precoMedio", new PropertyModel<Double>(this, "precoMedio")).setMDec(4));
        form.add(txtPrecoTotal = new DoubleField("precoTotal", new PropertyModel<Double>(this, "precoTotal")));
        form.add(txtObservacao = new InputField<String>("observacao", new PropertyModel(this, "observacao")));
        form.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setMovimentoEstoque();
                adicionarMovimentacao(target);
            }
        });
        form.add(btnLimpar = new AbstractAjaxButton("btnLimpar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
            }
        });
        form.add(table = new Table<MovimentoEstoque>("table", getColumns(), getCollectionProvider()));
        table.populate();
        addModal(dlgConfirmacaoAdicionar = new DlgConfirmacao(newModalId(), BundleManager.getString("movimentacaoJaAdicionadoAtualizarDados")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                adicionarMovimentacao(target, existeRegistroTabela());
            }
        });
        txtPrecoTotal.setEnabled(false);

        txtQuantidade.add(new AjaxFormComponentUpdatingBehavior("onblur") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                txtPrecoTotal.setComponentValue(Coalesce.asDouble(txtQuantidade.getComponentValue()) * Coalesce.asDouble(txtPrecoMedio.getComponentValue()));
                art.add(txtPrecoTotal);
            }
        });

        txtPrecoMedio.add(new AjaxFormComponentUpdatingBehavior("onblur") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                txtPrecoTotal.setComponentValue(Coalesce.asDouble(txtQuantidade.getComponentValue()) * Coalesce.asDouble(txtPrecoMedio.getComponentValue()));
                art.add(txtPrecoTotal);
            }
        });

        btnLimpar.setDefaultFormProcessing(false);
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                eventoConsultaProduto(target, object);
            }
        });

        this.autoCompleteConsultaDeposito.setComponentValue(br.com.celk.system.session.ApplicationSession.get().getSession().<br.com.ksisolucoes.vo.basico.Empresa>getEmpresa().getEmpresaMaterial().getDeposito());

        form.add(pnlEntradaLote.registerEvents());
        this.getBtnVoltar().setVisible(false);
        getBtnSalvar().setDefaultFormProcessing(false);

        if(RepositoryComponentDefault.NAO.equals(getUtilizaLocalizacaoEstoque())){
            containerLocalizacao.setVisible(false);
        }
    }

    private void initDlgCadastroFabricante(AjaxRequestTarget target){
        if(dlgCadastroFabricante == null){
            addModal(target, dlgCadastroFabricante = new DlgCadastroFabricante(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, Fabricante fabricante) throws ValidacaoException, DAOException {
                    autoCompleteConsultaFabricante.limpar(target);
                    autoCompleteConsultaFabricante.setComponentValue(fabricante);
                    target.add(autoCompleteConsultaFabricante);
                }
            });
        }
        dlgCadastroFabricante.showDlg(target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProduto.getTxtDescricao().getTextField();
    }

    @Override
    public Class<LancamentoMovimentoEstoque> getReferenceClass() {
        return LancamentoMovimentoEstoque.class;
    }

    @Override
    public Class getResponsePage() {
        return LancamentoInventarioMovimentoEstoquePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("lancamentoInventarioIndividual");
    }

    @Override
    public Object salvar(LancamentoMovimentoEstoque object) throws DAOException, ValidacaoException {
        Set<MovimentoEstoque> movimentos = new HashSet<MovimentoEstoque>();

        if (movimentoEstoques.size() <= 0) {
            throw new ValidacaoException(BundleManager.getString("informePeloMenosUmMovimentoEstoque"));
        }

        for (MovimentoEstoque movimentoEstoque1 : movimentoEstoques) {
            movimentos.add(movimentoEstoque1);
        }
        object.setMovimentoEstoqueSet(movimentos);

        return BOFactoryWicket.getBO(MovimentoEstoqueFacade.class).saveLancamentoMovimentoEstoque(object);
    }

    public void eventoConsultaProduto(AjaxRequestTarget target, Produto _produto) {
        if (_produto != null) {
            Double preco = EstoqueEmpresaHelper.getPrecoMedio(empresa, produto);
            Double estoqueFisico = EstoqueEmpresaHelper.getEstoqueFisico(empresa, produto);
            if (br.com.celk.util.Coalesce.asDouble(preco) != 0D || br.com.celk.util.Coalesce.asDouble(estoqueFisico) != 0D) {
                txtPrecoMedio.setEnabled(false);
                txtPrecoMedio.setComponentValue(preco);
            } else {
                txtPrecoMedio.setEnabled(true);
                for (MovimentoEstoque me : movimentoEstoques) {
                    if (me.getProduto().getCodigo().equals(produto.getCodigo())) {
                        txtPrecoMedio.limpar(target);
                        txtPrecoMedio.setComponentValue(me.getPrecoMedio());
                        break;
                    }
                }
            }
            if (txtQuantidade.getComponentValue() != null) {
                txtPrecoTotal.setComponentValue(txtQuantidade.getComponentValue() * txtPrecoMedio.getComponentValue());
                target.add(txtPrecoTotal);
            }
            target.add(txtPrecoMedio);
        }
    }

    private void limpar(AjaxRequestTarget target) {
        this.autoCompleteConsultaDeposito.limpar(target);
        this.autoCompleteConsultaProduto.limpar(target);
        this.txtNumeroDocumento.limpar(target);
        this.txtQuantidade.limpar(target);
        this.pnlEntradaLote.limpar(target);
        this.txtPrecoMedio.limpar(target);
        this.txtPrecoTotal.limpar(target);
        this.autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        this.txtObservacao.limpar(target);
        pnlEntradaLote.setEnableField(true);
        autoCompleteConsultaProduto.setEnabled(true);
        autoCompleteConsultaDeposito.setEnabled(true);
        this.movimentoEstoque = new MovimentoEstoque();
        this.autoCompleteConsultaDeposito.setComponentValue(br.com.celk.system.session.ApplicationSession.get().getSession().<br.com.ksisolucoes.vo.basico.Empresa>getEmpresa().getEmpresaMaterial().getDeposito());
        autoCompleteConsultaFabricante.setEnabled(false);
        btnCadFabricante.setEnabled(false);
        autoCompleteConsultaFabricante.limpar(target);
        target.add(autoCompleteConsultaFabricante);
        target.add(btnCadFabricante);
        target.add(this.pnlEntradaLote);
        target.add(this.autoCompleteConsultaProduto);
        target.add(this.autoCompleteConsultaDeposito);
        target.focusComponent(this.getComponentRequestFocus());
    }

    private List<ISortableColumn<MovimentoEstoque>> getColumns() {
        List<ISortableColumn<MovimentoEstoque>> columns = new ArrayList<ISortableColumn<MovimentoEstoque>>();

        ColumnFactory columnFactory = new ColumnFactory(MovimentoEstoque.class);

        columns.add(getCustomColumn());
        columns.add(new RowNumberColumn<MovimentoEstoque>());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(MovimentoEstoque.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("grupoEstoque"), VOUtils.montarPath(MovimentoEstoque.PROP_GRUPO_ESTOQUE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(MovimentoEstoque.PROP_QUANTIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("numeroDocumento"), VOUtils.montarPath(MovimentoEstoque.PROP_NUMERO_DOCUMENTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("observacao"), VOUtils.montarPath(MovimentoEstoque.PROP_OBSERVACAO)));
        columns.add(new DoubleColumn(bundle("precoMedio"), VOUtils.montarPath(MovimentoEstoque.PROP_PRECO_MEDIO)).setCasasDecimais(4));

        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            columns.add(columnFactory.createColumn(BundleManager.getString("localizacaoEstrutura"), VOUtils.montarPath(MovimentoEstoque.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_MASCARA)));
        }

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<MovimentoEstoque>() {

            @Override
            public Component getComponent(String componentId, final MovimentoEstoque rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        atualizaFormMovimentacao(rowObject, target);
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerMovimentacao(target, rowObject);
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }
                };
            }
        };
    }

    private void removerMovimentacao(AjaxRequestTarget target, MovimentoEstoque _movimentoEstoque) {
        for (int i = 0; i < movimentoEstoques.size(); i++) {
            if (movimentoEstoques.get(i) == _movimentoEstoque) {
                movimentoEstoques.remove(i);
            }
        }
        table.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return movimentoEstoques;
            }
        };
    }

    private void adicionarMovimentacao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validaMovimentacao();
        MovimentoEstoque _movimentoEstoque = existeRegistroTabela();
        if (_movimentoEstoque == null) {
            adicionarMovimentacao(target, _movimentoEstoque);
        } else {
            dlgConfirmacaoAdicionar.show(target);
        }
    }

    private MovimentoEstoque existeRegistroTabela() {
        for (MovimentoEstoque _movimentacao : movimentoEstoques) {
            if (_movimentacao == movimentoEstoque) {
                return _movimentacao;
            }
            if (_movimentacao.getGrupoEstoque() != null
                    && movimentoEstoque.getGrupoEstoque() != null) {
                if (_movimentacao.getProduto().equals(movimentoEstoque.getProduto())
                        && _movimentacao.getGrupoEstoque().trim().equals(movimentoEstoque.getGrupoEstoque().trim())
                        && _movimentacao.getDeposito().equals(movimentoEstoque.getDeposito())
                        && _movimentacao.getLocalizacaoEstrutura().getCodigo().equals(movimentoEstoque.getLocalizacaoEstrutura().getCodigo())) {
                    return _movimentacao;
                }
            } else {
                if (_movimentacao.getProduto().equals(movimentoEstoque.getProduto())
                        && _movimentacao.getDeposito().equals(movimentoEstoque.getDeposito())) {
                    return _movimentacao;
                }
            }
        }
        return null;
    }

    private void validaMovimentacao() throws ValidacaoException {
        if (Data.adjustRangeHour(movimentoEstoque.getDataPortaria()).getDataInicial().after(Data.adjustRangeHour(Data.getDataAtual()).getDataInicial())) {
            throw new ValidacaoException(BundleManager.getString("msgDataInventarioNaoPodeSerPosteriorDataAtual"));
        }

        if (movimentoEstoque.getProduto() == null) {
            throw new ValidacaoException(BundleManager.getString("informeProduto"));
        }

        if (movimentoEstoque.getDeposito() == null) {
            throw new ValidacaoException(BundleManager.getString("informeDeposito"));
        }

        if (TipoDocumento.IS_EXIGE_OBSERVACAO.equals(movimentoEstoque.getTipoDocumento().getFlagExigeObservacao())) {
            if (movimentoEstoque.getObservacao() == null) {
                throw new ValidacaoException(BundleManager.getString("observacaoObrigatoria"));
            }
        }

        if (autoCompleteConsultaFabricante.isEnabled() && movimentoEstoque.getFabricante() == null) {
            throw new ValidacaoException(BundleManager.getString("informeFabricante"));
        }

        if (movimentoEstoque.getTipoDocumento() == null) {
            throw new ValidacaoException(BundleManager.getString("problemaCarregarParametroTipoDocumentoTransferenciaEntrada"));
        }

        if (RepositoryComponentDefault.SIM.equals(movimentoEstoque.getProduto().getSubGrupo().getFlagControlaGrupoEstoque())) {
            if (movimentoEstoque.getDataValidadeGrupoEstoque() == null) {
                throw new ValidacaoException(BundleManager.getString("informeDataValidade"));
            }
        }

    }

    private void adicionarMovimentacao(AjaxRequestTarget target, MovimentoEstoque _movimentacaoEstoque) throws ValidacaoException {

        if (_movimentacaoEstoque == null) {
            movimentoEstoques.add(movimentoEstoque);
        } else {
            MovimentoEstoquePK id = _movimentacaoEstoque.getId();
            try {
                BeanUtils.copyProperties(_movimentacaoEstoque, movimentoEstoque);
            } catch (IllegalAccessException ex) {
                throw new ValidacaoException(ex);
            } catch (InvocationTargetException ex) {
                throw new ValidacaoException(ex);
            }
            _movimentacaoEstoque.setId(id);
        }
        for (MovimentoEstoque me : movimentoEstoques) {
            if (me.getProduto().getCodigo().equals(produto.getCodigo())) {
                me.setPrecoMedio(txtPrecoMedio.getComponentValue());
            }
        }
        limpar(target);
        table.update(target);
    }

    private void setMovimentoEstoque() throws DAOException, ValidacaoException {
        Long itemNumeroDocumento = 1L;
        movimentoEstoque.setProduto(produto);
        movimentoEstoque.setQuantidade(quantidade);
        TipoDocumento tipoDocumentoEntrada = CargaBasicoPadrao.getInstance().getParametroPadrao().getTipoDocumentoEntrada();
        movimentoEstoque.setDataPortaria(DataUtil.getDataAtual());
        movimentoEstoque.setId(new MovimentoEstoquePK(empresa, null));
        movimentoEstoque.setTipoDocumento(tipoDocumentoEntrada);
        movimentoEstoque.setNumeroDocumento(numeroDocumento);
        movimentoEstoque.setItemDocumento(itemNumeroDocumento);
        movimentoEstoque.setObservacao(observacao);
        movimentoEstoque.setDeposito(deposito);
        movimentoEstoque.setPrecoMedio(precoMedio);
        movimentoEstoque.setDataValidadeGrupoEstoque(pnlEntradaLote.getDataValidade());
        movimentoEstoque.setGrupoEstoque((String) pnlEntradaLote.getComponentValue());
        movimentoEstoque.setLocalizacaoEstrutura(localizacaoEstrutura != null ? localizacaoEstrutura : EstoqueHelper.getLocalizacaoEstruturaPadrao());
        movimentoEstoque.setFabricante(fabricante);
    }

    private void atualizaFormMovimentacao(MovimentoEstoque _movimentacao, AjaxRequestTarget target) {
        autoCompleteConsultaDeposito.setComponentValue(_movimentacao.getDeposito());
        autoCompleteConsultaProduto.setComponentValue(_movimentacao.getProduto());
        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())) {
            autoCompleteConsultaLocalizacaoEstrutura.setComponentValue(_movimentacao.getLaboratorioFabricanteGrupoEstoque());
        }
        autoCompleteConsultaFabricante.limpar(target);
        autoCompleteConsultaFabricante.setComponentValue(_movimentacao.getFabricante());
        txtNumeroDocumento.setComponentValue(_movimentacao.getNumeroDocumento());
        pnlEntradaLote.setComponentValue(_movimentacao.getGrupoEstoque());
        pnlEntradaLote.setDataValidade(_movimentacao.getDataValidadeGrupoEstoque());
        txtQuantidade.setComponentValue(_movimentacao.getQuantidade());
        txtPrecoMedio.setComponentValue(_movimentacao.getPrecoMedio());
        txtPrecoTotal.setComponentValue(txtQuantidade.getComponentValue() * txtPrecoMedio.getComponentValue());
        txtObservacao.setComponentValue(_movimentacao.getObservacao());
        pnlEntradaLote.setEnableField(false);
        autoCompleteConsultaProduto.setEnabled(false);
        autoCompleteConsultaDeposito.setEnabled(false);
        target.add(autoCompleteConsultaDeposito);
        target.add(autoCompleteConsultaProduto);
        target.add(autoCompleteConsultaLocalizacaoEstrutura);
        target.add(autoCompleteConsultaFabricante);
        target.add(txtNumeroDocumento);
        target.add(pnlEntradaLote);
        target.add(txtQuantidade);
        target.add(txtPrecoMedio);
        target.add(txtPrecoTotal);
        target.add(txtObservacao);
    }

    private void habilitarLocalizacaoEstrutura(AjaxRequestTarget target){
        if(target != null){
            autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        }

        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(true);
        } else {
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
        }

        if(target != null){
            target.add(autoCompleteConsultaLocalizacaoEstrutura);
        }
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(LancamentoInventarioMovimentoEstoquePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }

}
