package br.com.celk.view.basico.atividade.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaAtividadeDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerAtividade extends Panel implements IRestricaoContainer<QueryConsultaAtividadeDTOParam> {

    private InputField<String> txtDescricao;
    
    private QueryConsultaAtividadeDTOParam param = new QueryConsultaAtividadeDTOParam();
    
    public RestricaoContainerAtividade(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaAtividadeDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
