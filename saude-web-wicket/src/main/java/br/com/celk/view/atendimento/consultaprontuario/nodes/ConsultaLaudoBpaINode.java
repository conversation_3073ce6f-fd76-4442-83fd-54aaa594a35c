package br.com.celk.view.atendimento.consultaprontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesConsultaProntuarioRef;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.consultaprontuario.nodes.annotations.ConsultaProntuarioNode;
import br.com.celk.view.atendimento.consultaprontuario.nodes.base.ConsultaProntuarioNodeImp;
import br.com.celk.view.atendimento.consultaprontuario.panel.consultalaudobpai.ConsultaLaudoBpaIPanel;
import br.com.celk.view.atendimento.consultaprontuario.panel.template.ConsultaProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ConsultaProntuarioNode(NodesConsultaProntuarioRef.CONSULTA_LAUDO_BPAI)
public class ConsultaLaudoBpaINode extends ConsultaProntuarioNodeImp{

    @Override
    public ConsultaProntuarioCadastroPanel getPanel(String id) {
        return new ConsultaLaudoBpaIPanel(id);
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("laudoBpaI");
    }
}