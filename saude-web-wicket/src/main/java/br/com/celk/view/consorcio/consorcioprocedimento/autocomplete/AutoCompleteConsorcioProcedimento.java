package br.com.celk.view.consorcio.consorcioprocedimento.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.restricaocontainer.RestricaoContainerDominioConsorcioProcedimento;
import br.com.celk.view.consorcio.consorcioprocedimento.autocomplete.settings.ConsorcioProcedimentoAutoCompleteSettings;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaDominioConsorcioProcedimentoDTOParam;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import br.com.ksisolucoes.vo.consorcio.DominioConsorcioProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsorcioProcedimento extends AutoCompleteConsulta<ConsorcioProcedimento> {

    private boolean apenasProcedimentosVinculadosPrestador = false;
    private ConsorcioPrestador consorcioPrestador;

    public AutoCompleteConsorcioProcedimento(String id) {
        super(id);
    }

    public AutoCompleteConsorcioProcedimento(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsorcioProcedimento(String id, IModel<ConsorcioProcedimento> model) {
        super(id, model);
    }

    public AutoCompleteConsorcioProcedimento(String id, IModel<ConsorcioProcedimento> model, boolean required) {
        super(id, model, required);
    }
    
    @Override
    public int getMinDialogHeight() {
        return 600;
    }

    @Override
    public int getMinDialogWidth() {
        return 900;
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public Class<ConsorcioProcedimento> getReferenceClass() {
                return ConsorcioProcedimento.class;
            }

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(DominioConsorcioProcedimento.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(DominioConsorcioProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(DominioConsorcioProcedimento.PROP_REFERENCIA)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(DominioConsorcioProcedimento.PROP_DESCRICAO_PROCEDIMENTO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerDominioConsorcioProcedimento(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<ConsorcioProcedimento, QueryConsultaDominioConsorcioProcedimentoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaDominioConsorcioProcedimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(ConsorcioFacade.class).consultarDominioConsorcioProcedimento(dataPaging);
                    }

                    @Override
                    public QueryConsultaDominioConsorcioProcedimentoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaDominioConsorcioProcedimentoDTOParam param = new QueryConsultaDominioConsorcioProcedimentoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(DominioConsorcioProcedimento.PROP_DESCRICAO_PROCEDIMENTO, true);
                    }

                    @Override
                    public void customizeParam(QueryConsultaDominioConsorcioProcedimentoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setApenasProcedimentosVinculadosPrestador(apenasProcedimentosVinculadosPrestador);
                        param.setConsorcioPrestador(consorcioPrestador);
                    }
                };
            }

            @Override
            public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
                return new ConsorcioProcedimentoAutoCompleteSettings();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("procedimentos");
    }

    public void setApenasProcedimentosVinculadosPrestador(boolean apenasProcedimentosVinculadosPrestador) {
        this.apenasProcedimentosVinculadosPrestador = apenasProcedimentosVinculadosPrestador;
    }

    public void setConsorcioPrestador(ConsorcioPrestador consorcioPrestador) {
        this.consorcioPrestador = consorcioPrestador;
    }
}
