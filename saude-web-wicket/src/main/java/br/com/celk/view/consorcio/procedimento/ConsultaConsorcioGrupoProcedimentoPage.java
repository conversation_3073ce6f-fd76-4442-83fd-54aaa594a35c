package br.com.celk.view.consorcio.procedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.atendimento.procedimentogrupo.pnl.PnlConsultaProcedimentoGrupo;
import br.com.celk.view.consorcio.consorcioprocedimento.pnl.PnlConsultaConsorcioProcedimento;
import br.com.celk.view.consorcio.procedimento.customize.CustomizeConsultaConsorcioGrupo;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo;
import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaConsorcioGrupoProcedimentoPage extends ConsultaPage<ConsorcioGrupo, List<BuilderQueryCustom.QueryParameter>> {

    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private DropDown<ProcedimentoSubGrupo> cbxProcedimentoSubGrupo;
    private DropDown<ProcedimentoFormaOrganizacao> cbxProcedimentoFormaOrganizacao;
    private PnlConsultaProcedimentoGrupo pnlConsultaProcedimentoGrupo;
    private String descricao;
    private ConsorcioProcedimento consorcioProcedimento;
    private InputField txtDescricao;
    private ProcedimentoGrupo procedimentoGrupo;
    private ProcedimentoSubGrupo procedimentoSubGrupo;
    private ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(txtDescricao = new InputField("descricao"));
        form.add(cbxProcedimentoSubGrupo = new DropDown("procedimentoSubGrupo"));
        form.add(cbxProcedimentoFormaOrganizacao = new DropDown("procedimentoFormaOrganizacao"));
        form.add(new PnlConsultaConsorcioProcedimento("consorcioProcedimento"));
        form.add(pnlConsultaProcedimentoGrupo = new PnlConsultaProcedimentoGrupo("procedimentoGrupo"));

        cbxProcedimentoSubGrupo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                eventoProcedimentoSubGrupo(target, cbxProcedimentoSubGrupo.getModelObject());
            }
        });

        pnlConsultaProcedimentoGrupo.add(new ConsultaListener<ProcedimentoGrupo>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoGrupo object) {
                eventoProcedimentoGrupo(target, object);
            }
        });

        setExibeExpandir(true);
    }
    
    @Override
    public List<IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGrupo.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(ConsorcioGrupo.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupo"), VOUtils.montarPath(ConsorcioGrupo.PROP_GRUPO, ProcedimentoGrupo.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("subGrupo"), VOUtils.montarPath(ConsorcioGrupo.PROP_SUB_GRUPO, ProcedimentoSubGrupo.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("formaOrganizacao"), VOUtils.montarPath(ConsorcioGrupo.PROP_PROCEDIMENTO_FORMA_ORGANIZACAO, ProcedimentoFormaOrganizacao.PROP_DESCRICAO)));

        return columns;
    }

    private void eventoProcedimentoSubGrupo(AjaxRequestTarget target, ProcedimentoSubGrupo object) {
        cbxProcedimentoFormaOrganizacao.limpar(target);
        cbxProcedimentoFormaOrganizacao.removeAllChoices();

        if (object != null) {
            cbxProcedimentoFormaOrganizacao.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoFormaOrganizacao> formaOrganizacoes = LoadManager.getInstance(ProcedimentoFormaOrganizacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_GRUPO), object.getId().getCodigoGrupo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoFormaOrganizacao.PROP_ID, ProcedimentoFormaOrganizacaoPK.PROP_CODIGO_PROCEDIMENTO_SUB_GRUPO), object.getId().getCodigo()))
                    .start().getList();
            for (ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao1 : formaOrganizacoes) {
                cbxProcedimentoFormaOrganizacao.addChoice(procedimentoFormaOrganizacao1, procedimentoFormaOrganizacao1.getDescricaoFormatado());
            }
        }
    }

    private void eventoProcedimentoGrupo(AjaxRequestTarget target, ProcedimentoGrupo object) {
        cbxProcedimentoSubGrupo.limpar(target);
        cbxProcedimentoSubGrupo.removeAllChoices();

        if (object != null) {
            cbxProcedimentoSubGrupo.addChoice(null, BundleManager.getString("todos"));
            List<ProcedimentoSubGrupo> subGrupos = LoadManager.getInstance(ProcedimentoSubGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoSubGrupo.PROP_ID, ProcedimentoSubGrupoPK.PROP_CODIGO_GRUPO), object.getCodigo()))
                    .start().getList();
            for (ProcedimentoSubGrupo procedimentoSubGrupo1 : subGrupos) {
                cbxProcedimentoSubGrupo.addChoice(procedimentoSubGrupo1, procedimentoSubGrupo1.getDescricaoFormatado());
            }
        }
    }
    
    private CustomColumn<ConsorcioGrupo> getCustomColumn(){
        return new CustomColumn<ConsorcioGrupo>() {

            @Override
            public Component getComponent(String componentId, final ConsorcioGrupo rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroConsorcioGrupoProcedimentoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ConsorcioFacade.class).removerConsorcioGrupo(rowObject);
                        getPageableTable().update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }
                    
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaConsorcioGrupo()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ConsorcioGrupo.PROP_CODIGO, false);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (consorcioProcedimento != null) {
                            HQLHelper subquery = hql.getNewInstanceSubQuery();
                            subquery.addToSelect("1");
                            subquery.addToFrom("ConsorcioGrupoProcedimento cgp");
                            subquery.addToWhereWhithAnd("cgp.consorcioProcedimento.codigo = ", consorcioProcedimento.getCodigo());
                            subquery.addToWhereWhithAnd("cgp.consorcioGrupo.codigo = " + alias + ".codigo");
                            hql.addToWhereWhithAnd("EXISTS(" + subquery.getQuery() + ")");
                        }
                    }
                });
            }

        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> param = new ArrayList<BuilderQueryCustom.QueryParameter>();

        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGrupo.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, descricao));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGrupo.PROP_GRUPO), procedimentoGrupo));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGrupo.PROP_SUB_GRUPO), procedimentoSubGrupo));
        param.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGrupo.PROP_PROCEDIMENTO_FORMA_ORGANIZACAO), procedimentoFormaOrganizacao));

        return param;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroConsorcioGrupoProcedimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaGruposPorProcedimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

}
