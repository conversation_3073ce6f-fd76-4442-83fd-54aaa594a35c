package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgNovoLancamentoDespesa extends Window {

    private PnlNovoLancamentoDespesa pnlNovoLancamento;

    public DlgNovoLancamentoDespesa(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(300);
        setInitialWidth(600);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("novoLancamento");
            }
        });

        setContent(pnlNovoLancamento = new PnlNovoLancamentoDespesa(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                DlgNovoLancamentoDespesa.this.onConfirmar(target, itemContaPaciente);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                pnlNovoLancamento.limpar(target);
                close(target);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                pnlNovoLancamento.limpar(target);
                return true;
            }
        });
    }

    public void show(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        show(target);
        pnlNovoLancamento.setItemContaPaciente(target, itemContaPaciente);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;
}
