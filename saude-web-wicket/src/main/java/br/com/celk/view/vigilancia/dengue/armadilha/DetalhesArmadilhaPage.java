package br.com.celk.view.vigilancia.dengue.armadilha;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilhaVisita;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

@Private
public class DetalhesArmadilhaPage extends BasePage {

    private Form<DengueArmadilha> form;
    private final DengueArmadilha dengueArmadilha;
    private WebMarkupContainer containerInativacao;
    private WebMarkupContainer containerDengueArmadilhaVisita;
    private WebMarkupContainer containerDadosEdicao;
    private Table tblDengueArmadilhaVisita;
    private List<DengueArmadilhaVisita> lstDengueArmadilhaVisita;

    public DetalhesArmadilhaPage(DengueArmadilha dengueArmadilha) {
        this.dengueArmadilha = dengueArmadilha;
        init();
    }

    private void init() {
        DengueArmadilha proxy = on(DengueArmadilha.class);
 
        addContainerDadosEdicao(getForm());
        getForm().add(new DisabledInputField(path(proxy.getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getDataInstalacao())));
        getForm().add(new DisabledInputField(path(proxy.getDengueTipoImovel().getDescricao())));
        getForm().add(new DisabledInputField(path(proxy.getDengueLocalidade().getLocalidade())));
        getForm().add(new DisabledInputField(path(proxy.getVigilanciaEndereco().getEnderecoFormatadoComCidade())));
        getForm().add(new DisabledInputField(path(proxy.getComplementoLogradouro())));
        getForm().add(new DisabledInputField(path(proxy.getNumeroLogradouro())));
        getForm().add(new DisabledInputField(path(proxy.getNumeroQuarteirao())));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoArmadilha()), DengueArmadilha.TipoArmadilha.values()).setEnabled(false));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), DengueArmadilha.Situacao.values()).setEnabled(false));

        containerInativacao = new WebMarkupContainer("containerInativacao");
        containerInativacao.setVisible(false);
        containerInativacao.add(new DisabledInputField(path(proxy.getDataInativacao())));
        containerInativacao.add(new DisabledInputField(path(proxy.getMotivoInativacao())));
        getForm().add(containerInativacao);

        if (DengueArmadilha.Situacao.INATIVO.value().equals(getForm().getModelObject().getSituacao())) {
            containerInativacao.setVisible(true);
        }

        carregarDengueArmadilhaVisita();
        addContainerDengueArmadilhaVisita(form);

        getForm().add(new VoltarButton("btnVoltar").setEnabled(true));
        add(getForm());

    }

    private Form<DengueArmadilha> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<DengueArmadilha>(new DengueArmadilha()));
            this.form.getModel().setObject(dengueArmadilha);
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesArmadilha");
    }

    private void addContainerDengueArmadilhaVisita(Form form) {
        form.add(containerDengueArmadilhaVisita = new WebMarkupContainer("containerDengueArmadilhaVisita"));
        containerDengueArmadilhaVisita.setOutputMarkupId(true);
        containerDengueArmadilhaVisita.setVisible(false);
        containerDengueArmadilhaVisita.add(tblDengueArmadilhaVisita = new Table("tblDengueArmadilhaVisita", getColumnsDengueArmadilhaVisita(), getCollectionProviderDengueArmadilhaVisita()));
        tblDengueArmadilhaVisita.populate();
        if (!CollectionUtils.isAllEmpty(lstDengueArmadilhaVisita)) {
            containerDengueArmadilhaVisita.setVisible(true);
        }
    }

    private void addContainerDadosEdicao(Form form) {
        DengueArmadilha proxy = on(DengueArmadilha.class);
        form.add(containerDadosEdicao = new WebMarkupContainer("containerDadosEdicao"));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getUsuarioCadastro().getNome())));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getDataCadastro())));
    }

    private void carregarDengueArmadilhaVisita() {
        if (getForm().getModelObject().getCodigo() != null) {
            Long codigoDengueArmadilha = getForm().getModelObject().getCodigo();
            lstDengueArmadilhaVisita = LoadManager.getInstance(DengueArmadilhaVisita.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DengueArmadilhaVisita.PROP_DENGUE_ARMADILHA, DengueArmadilha.PROP_CODIGO), codigoDengueArmadilha))
                    .addProperties(new HQLProperties(DengueArmadilhaVisita.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(DengueArmadilhaVisita.PROP_DATA_VISITA, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();
        }
    }

    private List<IColumn> getColumnsDengueArmadilhaVisita() {
        List<IColumn> columns = new ArrayList<>();
        DengueArmadilhaVisita proxy = on(DengueArmadilhaVisita.class);

        columns.add(getActionColumnDengueArmadilhaVisita());
        columns.add(createColumn(bundle("dataVisita"), proxy.getDataVisita()));
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createColumn(bundle("dengueOcorrenciaDesfecho"), proxy.getDengueOcorrenciaDesfecho().getDescricao()));

        return columns;
    }

    private IColumn getActionColumnDengueArmadilhaVisita() {
        return new MultipleActionCustomColumn<DengueArmadilhaVisita>() {
            @Override
            public void customizeColumn(final DengueArmadilhaVisita rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<DengueArmadilhaVisita>() {
                    @Override
                    public void action(AjaxRequestTarget target, DengueArmadilhaVisita modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesVisitaArmadilhaPage(modelObject));
                    }
                }).setTitleBundleKey("detalhes");
            }
        };
    }

    private ICollectionProvider getCollectionProviderDengueArmadilhaVisita() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (lstDengueArmadilhaVisita == null) {
                    lstDengueArmadilhaVisita = new ArrayList<>();
                }
                return lstDengueArmadilhaVisita;
            }
        };
    }
}
