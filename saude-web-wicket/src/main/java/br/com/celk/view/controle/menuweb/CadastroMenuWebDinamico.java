package br.com.celk.view.controle.menuweb;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.Window;
import br.com.celk.controlemenu.dto.MenuDinamicoDTO;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.annotation.PainelControle;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.controle.menuweb.controle.IMenuDinamicoControler;
import br.com.celk.view.controle.menuweb.controle.IMenuDinamicoNode;
import br.com.celk.view.controle.menuweb.panel.EditarMenuDinamicoPanel;
import br.com.celk.view.controle.menuweb.panel.EditarModuloDinamicoPanel;
import br.com.celk.view.controle.menuweb.panel.EditarSubmenuDinamicoPanel;
import br.com.celk.view.controle.menuweb.panel.EditarProgramaDinamicoPanel;
import br.com.celk.view.controle.menuweb.panel.template.DefaultMenuDinamicoPanel;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import static ch.lambdaj.Lambda.on;
import java.util.LinkedList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.tree.DefaultTableTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.TableTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.TreeColumn;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
@PainelControle
public class CadastroMenuWebDinamico extends BasePage {
    
    private static final String PANEL_ID = "nodePanel";
    
    private TableTree tableTree;
    private final MenuDinamicoProvider provider = new MenuDinamicoProvider();
    private Form form;
    private WebMarkupContainer panelContainer;
    private IMenuDinamicoControler menuDinamicoControler;
    private IMenuDinamicoNode activeNode;
        
    public CadastroMenuWebDinamico() {
        panelContainer = null;
        init();
    }
    
    private void init(){
        form = new Form("form");
        
        tableTree = new DefaultTableTree("treeTable", getColumns(), provider, 999999);
        form.add(tableTree);
        
        form.add(panelContainer = new WebMarkupContainer("panelContainer"));
        panelContainer.setOutputMarkupId(true);
        
        panelContainer.add(new Fragment(getMenuDinamicoController().panelId(), "nodeNaoConfigurado-fragment", panelContainer));
        
        form.add(new AbstractAjaxButton("btnAtualizarMenu") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                atualizarMenu(target);
            }
        });
        
        add(form);
    }
    
    public IMenuDinamicoControler getMenuDinamicoController() {
        if (this.menuDinamicoControler == null) {
            this.menuDinamicoControler = new IMenuDinamicoControler() {

                @Override
                public void changeNode(AjaxRequestTarget target, IMenuDinamicoNode node) {
                    CadastroMenuWebDinamico.this.changeNode(node);
                    target.add(panelContainer);
                }

                @Override
                public void changePanel(AjaxRequestTarget target, DefaultMenuDinamicoPanel panel) {
                    CadastroMenuWebDinamico.this.changePanel(target, panel);
                    target.add(panelContainer);
                    target.appendJavaScript(JScript.scrollToTop());
                }
                
                @Override
                public String newWindowId() {
                    return CadastroMenuWebDinamico.this.newModalId();
                }

                @Override
                public void addWindow(AjaxRequestTarget target, Window window) {
                    CadastroMenuWebDinamico.this.addModal(target, window);
                }

                @Override
                public String panelId() {
                    return PANEL_ID;
                }
            };
        }

        return this.menuDinamicoControler;
    }
    
    private void changeNode(IMenuDinamicoNode node) {
        activeNode = node;
        DefaultMenuDinamicoPanel newInstance = node.getPanel(getMenuDinamicoController().panelId());

        changePanel(null, newInstance);
    }

    private void changePanel(AjaxRequestTarget target, DefaultMenuDinamicoPanel panel) {
        Component activePanel = panelContainer.get(getMenuDinamicoController().panelId());
        if (activePanel != null) {
            activePanel.replaceWith(panel);
        } else {
            panelContainer.add(panel);
        }
        panel.setMenuDinamicoControler(getMenuDinamicoController());
        if (target != null) {
            panel.changePanelAction(target);
        }
    }
    
    private List getColumns() {
        List<IColumn<MenuDinamicoDTO, String>> result = new LinkedList();

        result.add(getActionColumn());
        result.add(new TreeColumn<MenuDinamicoDTO, String>(Model.of("Menu")));

        return result;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<MenuDinamicoDTO>() {

            @Override
            public void customizeColumn(MenuDinamicoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<MenuDinamicoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, MenuDinamicoDTO dto) throws ValidacaoException, DAOException {
                        editar(target, dto);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<MenuDinamicoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, MenuDinamicoDTO dto) throws ValidacaoException, DAOException {
                        remover(target, dto);
                    }
                }).setEnabled(!MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(rowObject.getTipoMenu()) ||
                    (MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(rowObject.getTipoMenu()) && !MenuWeb.LayoutMenu.PADRAO.value().equals(rowObject.getMenuWeb().getLayoutMenu())));
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("menuDinamico");
    }

    @Override
    public void info(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.INFO);
    }

    @Override
    public void warn(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.WARNING);
    }

    @Override
    public void error(AjaxRequestTarget target, String message) {
        message(target, message, FeedbackMessage.ERROR);
    }
    
    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
    }
    
    private void editar(AjaxRequestTarget target, MenuDinamicoDTO dto){
        if(MenuDinamicoDTO.TipoMenu.MODULO.value().equals(dto.getTipoMenu())) {
            EditarModuloDinamicoPanel editarPanel = new EditarModuloDinamicoPanel(PANEL_ID);
            menuDinamicoControler.changePanel(target, editarPanel);
            editarPanel.setDTO(target, dto);
        } else if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(dto.getTipoMenu())) {
            EditarMenuDinamicoPanel editarPanel = new EditarMenuDinamicoPanel(PANEL_ID);
            menuDinamicoControler.changePanel(target, editarPanel);
            editarPanel.setDTO(target, dto);
        } else if(MenuDinamicoDTO.TipoMenu.SUB_MENU.value().equals(dto.getTipoMenu())) {
            EditarSubmenuDinamicoPanel editarPanel = new EditarSubmenuDinamicoPanel(PANEL_ID);
            menuDinamicoControler.changePanel(target, editarPanel);
            editarPanel.setDTO(target, dto);
        } else if(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(dto.getTipoMenu())) {
            EditarProgramaDinamicoPanel editarPanel = new EditarProgramaDinamicoPanel(PANEL_ID);
            menuDinamicoControler.changePanel(target, editarPanel);
            editarPanel.setDTO(target, dto);
        }
    }
    
    private void remover(AjaxRequestTarget target, MenuDinamicoDTO dto) throws ValidacaoException, DAOException {
        MenuWeb proxy = on(MenuWeb.class);
        
        if(MenuDinamicoDTO.TipoMenu.PROGRAMA.value().equals(dto.getTipoMenu())) {
            if(MenuWeb.LayoutMenu.PADRAO.value().equals(dto.getMenuWeb().getLayoutMenu())){
                throw new ValidacaoException(bundle("msgNaoPossivelRemoverProgramaPadrao"));
            } else {
                BOFactoryWicket.getBO(BasicoFacade.class).removerMenuWeb(dto.getMenuWeb());
            }
        } else {
            List<MenuWeb> menuWebList = LoadManager.getInstance(MenuWeb.class)
                .addProperty(path(proxy.getCodigo()))
                .addParameter(new QueryCustomParameter(path(proxy.getMenuWebPai()), dto.getMenuWeb()))
                .start().getList();
        
            if(CollectionUtils.isNotNullEmpty(menuWebList)){
                if(MenuDinamicoDTO.TipoMenu.MODULO.value().equals(dto.getTipoMenu())) {
                    throw new ValidacaoException(bundle("msgNaoPossivelRemoverModuloPoisPossuiUmMaisMenusRelacionados"));
                } else if(MenuDinamicoDTO.TipoMenu.MENU.value().equals(dto.getTipoMenu())) {
                    throw new ValidacaoException(bundle("msgNaoPossivelRemoverMenuPoisPossuiUmMaisSubmenusRelacionados"));
                } else if(MenuDinamicoDTO.TipoMenu.SUB_MENU.value().equals(dto.getTipoMenu())) {
                    throw new ValidacaoException(bundle("msgNaoPossivelRemoverSubmenuPoisPossuiUmMaisProgramasRelacionados"));
                }    
            } else {
                BOFactoryWicket.getBO(BasicoFacade.class).removerMenuWeb(dto.getMenuWeb());
            }
        }
        atualizarMenu(target);
    }
    
    private void atualizarMenu(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        provider.recarregarProvider();
        target.add(tableTree);
        CadastroMenuWebDinamico page = new CadastroMenuWebDinamico();
        setResponsePage(page);
    }
}