package br.com.celk.view.agenda.agendamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import static br.com.celk.system.methods.WicketMethods.bundle;

import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlLancarOcorrenciaContatoListaEspera extends Panel {

    private TextArea<String> textArea;
    private String ocorrencia;

    public PnlLancarOcorrenciaContatoListaEspera(String id) {
        super(id);
        init();
    }

    private void init() {
        setDefaultModel(new CompoundPropertyModel(this));
        setOutputMarkupId(true);

        add(textArea = new TextArea("ocorrencia", new Model()));

        textArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ocorrencia = textArea.getModelObject();
            }
        });

        add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (ocorrencia == null || ocorrencia.trim().length() == 0) {
                    throw new ValidacaoException(bundle("msgInformeOcorrencia"));
                }
                onSalvar(target, ocorrencia);
            }
        });

        add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
    }

    public void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onSalvar(AjaxRequestTarget target, String ocorrencia) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public void setFocus(AjaxRequestTarget target) {
        target.appendJavaScript(JScript.focusComponent(textArea));
    }
}
