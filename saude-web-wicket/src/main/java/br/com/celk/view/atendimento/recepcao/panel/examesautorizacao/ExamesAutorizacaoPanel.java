package br.com.celk.view.atendimento.recepcao.panel.examesautorizacao;

import br.com.celk.atendimento.recepcao.exameautorizacao.ExameSolicitacaoAgendamentoDTO;
import br.com.celk.atendimento.recepcao.exameautorizacao.ExameSolicitacaoAgendamentoDTOParam;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeQueryPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.DlgConfirmarAtendimento;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.InformacoesPacientePanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameAutorizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ExamesAutorizacaoPanel extends RecepcaoCadastroPanel {

    private DateChooser dchDataSolicitacao;
    private InputField txtNumeroRequisicao;
    private InputField txtNome;
    private DropDown dropDownSituacao;
    private Exame exame;
    private UsuarioCadsus usuarioCadsus;
    private SelectionPageableTable pageableTable;
    private Form<ExameSolicitacaoAgendamentoDTOParam> form;
    private DlgConfirmarAtendimento dlgConfirmarAtendimento;
    private DropDown exibirInativos;
    private InputField numeroCartao;
    private InputField nomeMae;
    private InformacoesPacientePanel informacoesPacientePanel;
    private DlgMotivoObject dlgMotivoObject;
    private DlgMotivoObject dlgMotivoObjectCancelarAgendamento;
    private WebMarkupContainer divCodigo;
    private WebMarkupContainer divRefencia;
    boolean parametroReferencia;
    boolean emiteComprovanteAutorizacao;
    boolean criarAgendamento;
    private InputField codigo;
    private InputField referencia;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    public ExamesAutorizacaoPanel(String id, Exame exame) {
        this(id);
        this.exame = exame;
    }

    public ExamesAutorizacaoPanel(String id, UsuarioCadsus usuarioCadsus) {
        this(id);
        this.usuarioCadsus = usuarioCadsus;
    }

    public ExamesAutorizacaoPanel(String id) {
        super(id, bundle("solicitacaoExames"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel<>(new ExameSolicitacaoAgendamentoDTOParam()));
        ExameSolicitacaoAgendamentoDTOParam proxy = on(ExameSolicitacaoAgendamentoDTOParam.class);
        divCodigo = new WebMarkupContainer("divCodigo", new CompoundPropertyModel(form.getModel().getObject()));
        divCodigo.setOutputMarkupId(true);
        divRefencia = new WebMarkupContainer("divReferencia", new CompoundPropertyModel(form.getModel().getObject()));
        divRefencia.setOutputMarkupId(true);

        try {
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
            emiteComprovanteAutorizacao =  RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("emiteComprovanteAutorizacaoExames"));
            criarAgendamento =  RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("agendamentoAutorizacaoExames"));
            divCodigo.add(codigo = new InputField(path(proxy.getCodigo())));
            divCodigo.setVisible(!parametroReferencia);
            form.add(divCodigo);
            divRefencia.add(referencia = new InputField(path(proxy.getReferencia())));
            divRefencia.setVisible(parametroReferencia);
            form.add(divRefencia);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEmpresa.setComponentValue(SessaoAplicacaoImp.getInstance().getEmpresa());
        form.add(txtNumeroRequisicao = new InputField(path(proxy.getNumeroRequisicao())));
        form.add(dchDataSolicitacao = new DateChooser(path(proxy.getDataSolicitacao())));
        form.add(txtNome = new InputField(path(proxy.getNome())));
        form.add(getDropDownSituacao(path(proxy.getSituacao())));

        pageableTable = new SelectionPageableTable("table", getColumns(), getPagerProvider(), 10);
        form.add(pageableTable);

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target);
            }
        });

        form.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                target.focusComponent(dchDataSolicitacao.getData());
            }
        });

        form.add(new AbstractAjaxButton("btnNovaRequisicao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                novaRequisicao(target);
            }
        });

        form.add(informacoesPacientePanel = new InformacoesPacientePanel("panelInformacoesPaciente"));
        pageableTable.addSelectionAction(new ISelectionAction<ExameSolicitacaoAgendamentoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO object) {
                informacoesPacientePanel.carregarPaciente(object.getExameAutorizacao().getExame().getUsuarioCadsus().getCodigo());
                target.add(informacoesPacientePanel);
            }
        });

        add(form);

        if (exame != null && exame.getCodigo() != null) {
            ExameSolicitacaoAgendamentoDTOParam param = (ExameSolicitacaoAgendamentoDTOParam) form.getModel().getObject();
            param.setNumeroRequisicao(exame.getCodigo().toString());
            pageableTable.populate();
            informacoesPacientePanel.carregarPaciente(exame.getUsuarioCadsus().getCodigo());
        } else if (usuarioCadsus != null) {
            ExameSolicitacaoAgendamentoDTOParam param = (ExameSolicitacaoAgendamentoDTOParam) form.getModel().getObject();
            if (RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsus.getUtilizaNomeSocial()) && usuarioCadsus.getApelido() != null) {
                param.setNome(usuarioCadsus.getApelido());
            } else {
                param.setNome(usuarioCadsus.getNome());
            }
            param.setSituacao(Exame.STATUS_AUTORIZADO);
            pageableTable.populate();
            informacoesPacientePanel.carregarPaciente(usuarioCadsus.getCodigo());
        }
    }

    private DropDown getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>(id);

            dropDownSituacao.addChoice(null, BundleManager.getString("todas"));
            dropDownSituacao.addChoice(Exame.STATUS_AGENDADO, BundleManager.getString("agendado"));
            dropDownSituacao.addChoice(Exame.STATUS_AUTORIZADO, BundleManager.getString("autorizado"));
            dropDownSituacao.addChoice(Exame.STATUS_SOLICITADO, BundleManager.getString("solicitado"));
        }
        return dropDownSituacao;
    }

    private void novaRequisicao(AjaxRequestTarget target) {
        getRecepcaoController().changePanel(target, new CadastroRequisicaoExamePanel(getRecepcaoController().panelId(), this));
    }

    private void autorizarRequisicao(AjaxRequestTarget target, Long codigoExameAutorizacao) {
        getRecepcaoController().changePanel(target, new CadastroAutorizacaoExamePanel(getRecepcaoController().panelId(), this, codigoExameAutorizacao));
    }
    
    private void agendarRequisicao(AjaxRequestTarget target, Long codigoExameAutorizacao) {
        getRecepcaoController().changePanel(target, new CadastroAgendamentoExamePanel(getRecepcaoController().panelId(), this, codigoExameAutorizacao));
    }

    @Override
    public void changePanelAction(AjaxRequestTarget target) {
        if (informacoesPacientePanel != null) {
            informacoesPacientePanel.limpar(target);
        }
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ExameSolicitacaoAgendamentoDTO proxy = on(ExameSolicitacaoAgendamentoDTO.class);
        ExameAutorizacao proxySort = on(ExameAutorizacao.class);

        columns.add(getCustomActionColumn());
        if(parametroReferencia){
            columns.add(createSortableColumn(bundle("referencia"), proxySort.getExame().getUsuarioCadsus().getNome(), proxy.getExameAutorizacao().getExame().getUsuarioCadsus().getReferencia()));
        }else{
            columns.add(createSortableColumn(bundle("codigo"), proxySort.getExame().getUsuarioCadsus().getNome(), proxy.getExameAutorizacao().getExame().getUsuarioCadsus().getCodigo()));
        }
        columns.add(createSortableColumn(bundle("paciente"), proxySort.getExame().getUsuarioCadsus().getNome(), proxy.getExameAutorizacao().getExame().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("dataSolicitacao"), proxySort.getExame().getDataSolicitacao(), proxy.getExameAutorizacao().getExame().getDataSolicitacao()));
        columns.add(createSortableColumn(bundle("nRequisicao"), proxySort.getExame().getCodigo(), proxy.getExameAutorizacao().getExame().getCodigo()));
        columns.add(createSortableColumn(bundle("situacao"), proxySort.getExame().getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("estabelecimentoSolicitante"), proxySort.getExame().getEmpresaSolicitante().getDescricao(), proxy.getExameAutorizacao().getExame().getEmpresaSolicitante().getDescricao()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<ExameSolicitacaoAgendamentoDTO>() {
            @Override
            public void customizeColumn(final ExameSolicitacaoAgendamentoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ExameSolicitacaoAgendamentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO modelObject) throws ValidacaoException, DAOException {
                        autorizarRequisicao(target, modelObject.getExameAutorizacao().getCodigo());
                    }
                }).setIcon(Icon.CHECKMARK).setTitleBundleKey("autorizar")
                        .setEnabled((!Exame.STATUS_AUTORIZADO.equals(rowObject.getExameAutorizacao().getExame().getStatus()) && rowObject.getExameAutorizacao().getExame().getDataConfirmacao() == null)
                                && !Exame.STATUS_CANCELADO.equals(rowObject.getExameAutorizacao().getExame().getStatus()));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ExameSolicitacaoAgendamentoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO modelObject) throws ValidacaoException, DAOException {
                        consultarRequisicao(target, modelObject.getExameAutorizacao().getCodigo());
                    }
                }).setTitleBundleKey("detalhes");

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ExameSolicitacaoAgendamentoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgMotivoObject(target, modelObject);
                    }
                }).setTitleBundleKey("cancelar").setQuestionDialogBundleKey(null).setEnabled(!Exame.STATUS_CANCELADO.equals(rowObject.getExameAutorizacao().getExame().getStatus()));

                addAction(ActionType.AGENDAR, rowObject, new IModelAction<ExameSolicitacaoAgendamentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO modelObject) throws ValidacaoException, DAOException {
                        agendarRequisicao(target, modelObject.getExameAutorizacao().getCodigo());
                    }
                }).setEnabled(Exame.STATUS_AUTORIZADO.equals(rowObject.getExameAutorizacao().getExame().getStatus()) && !rowObject.isAgendado() && criarAgendamento);
                
                addAction(ActionType.CLONAR, rowObject, new IModelAction<ExameSolicitacaoAgendamentoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgMotivoObjectCancelarAgendamento(target, modelObject);
                    }
                }).setIcon(Icon.CANCEL).setTitleBundleKey("cancelarAgendamento").setQuestionDialogBundleKey(null)
                        .setEnabled(Exame.STATUS_AUTORIZADO.equals(rowObject.getExameAutorizacao().getExame().getStatus()) && rowObject.isAgendado());

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ExameSolicitacaoAgendamentoDTO>() {
                    public DataReport action(ExameSolicitacaoAgendamentoDTO dto) throws ReportException {
                        ImpressaoExameDTOParam param = new ImpressaoExameDTOParam();
                        param.setCodigoExame(rowObject.getExameAutorizacao().getExame().getCodigo());
                        param.setAtendimento(rowObject.getExameAutorizacao().getExame().getAtendimento());
                        param.setStatusExame(rowObject.getExameAutorizacao().getExame().getStatus());
                        param.setTipoConvenio(rowObject.getExameAutorizacao().getExame().getTipoConvenioRealizado());
                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExame(param);
                    }
                }).setIcon(Icon.PRINT).setTitleBundleKey("imprimirAutorizacao").setQuestionDialogBundleKey(null)
                        .setEnabled(emiteComprovanteAutorizacao);


            }
        };
    }

    private void initDlgMotivoObject(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO dto) {
        if (dlgMotivoObject == null) {
            getRecepcaoController().addWindow(target, dlgMotivoObject = new DlgMotivoObject(getRecepcaoController().newWindowId(), bundle("msgCancelarRequisicao")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Serializable object) throws ValidacaoException, DAOException {
                    ExameSolicitacaoAgendamentoDTO dto = (ExameSolicitacaoAgendamentoDTO) object;
                    BOFactoryWicket.getBO(ExameFacade.class).cancelarExameAutorizacao(null, dto.getExameAutorizacao().getExame(), motivo, true);
                    pageableTable.update(target);
                }
            });
        }
        dlgMotivoObject.setObject(dto);
        dlgMotivoObject.show(target);
    }
    
    private void initDlgMotivoObjectCancelarAgendamento(AjaxRequestTarget target, ExameSolicitacaoAgendamentoDTO dto) {
        if (dlgMotivoObjectCancelarAgendamento == null) {
            getRecepcaoController().addWindow(target, dlgMotivoObjectCancelarAgendamento = new DlgMotivoObject(getRecepcaoController().newWindowId(), bundle("msgCancelarAgendamento")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Serializable object) throws ValidacaoException, DAOException {
                    ExameSolicitacaoAgendamentoDTO dto = (ExameSolicitacaoAgendamentoDTO) object;
                    BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarAgendamentoExame(dto.getExameAutorizacao().getExame(), motivo);
                    pageableTable.update(target);
                }
            });
        }
        dlgMotivoObjectCancelarAgendamento.setObject(dto);
        dlgMotivoObjectCancelarAgendamento.show(target);
    }

    private void consultarRequisicao(AjaxRequestTarget target, Long codigoExameAutorizacao) {
        getRecepcaoController().changePanel(target, new CadastroRequisicaoExamePanel(getRecepcaoController().panelId(), this, codigoExameAutorizacao));
    }

    @Override
    public void limparPainel(AjaxRequestTarget target) {
        limpar(target);
        target.focusComponent(dchDataSolicitacao.getData());
    }

    public void limpar(AjaxRequestTarget target) {
        dchDataSolicitacao.limpar(target);
        txtNumeroRequisicao.limpar(target);
        txtNome.limpar(target);
        dropDownSituacao.limpar(target);
        informacoesPacientePanel.limpar(target);
        codigo.limpar(target);
    }

    public IPagerProvider getPagerProvider() {
        return new CustomizeQueryPagerProvider() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                dataPaging.setParam(getParam());
                DataPagingResult result = BOFactoryWicket.getBO(ExameFacade.class).consultaExameAutorizacaoQueryPager(dataPaging);
                return result;
            }
        };
    }

    private ExameSolicitacaoAgendamentoDTOParam getParam() {
        ExameSolicitacaoAgendamentoDTOParam param = (ExameSolicitacaoAgendamentoDTOParam) form.getModel().getObject();
        return param;
    }

    private void procurar(AjaxRequestTarget target) {
        pageableTable.populate(target);
        informacoesPacientePanel.limpar(target);
    }
}
