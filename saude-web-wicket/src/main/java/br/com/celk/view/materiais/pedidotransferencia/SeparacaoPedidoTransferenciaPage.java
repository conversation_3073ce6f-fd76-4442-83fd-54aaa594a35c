package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.*;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.lote.saida.LotesSaidaChooser;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.TableRow;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.bo.hospital.pedido.dto.PedidoHospitalDTO;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.*;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioEmbarquePedidoTransferenciaDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.hamcrest.Matchers;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;


/**
 * <AUTHOR>
 */

public class SeparacaoPedidoTransferenciaPage extends BasePage {

    private final List<DTOPedidoTransferenciaItensWeb> dtoItens = new ArrayList<DTOPedidoTransferenciaItensWeb>();
    private final List<PedidoTransferenciaItem> pedidoTransferenciaItemsCancelados = new ArrayList<>();
    DlgConfirmacaoOk dlgPedidoCanceladoAutomaticamente;
    private WebMarkupContainer containerItem;
    private CompoundPropertyModel<DTOPedidoTransferenciaItensWeb> modelItem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private LotesSaidaChooser lotesSaidaChooser;
    private DoubleField txtQuantidadeEnv;
    private DoubleField txtEstoqueDisponivel;
    private DoubleField txtPadraoEnvio;
    private DoubleField txtEstoqueUnidade;
    private DateChooser dchDataUltimoPedido;
    private DateChooser dchDataPedido;
    private DoubleField txtQuantidadeSolic;
    private InputField<String> txtStatus;
    private DisabledInputArea<String> txtObservacao;
    private Table<DTOPedidoTransferenciaItensWeb> tableAdd;
    private SelectionTable<PedidoTransferenciaItem> table;
    private Double estoqueDisponivel;
    private Double padraoEnvio;
    private Double estoqueUnidade;
    private Double quantidadeSolic;
    private Double quantidade;
    private Double quantidadeEnv;
    private String status;
    private String observacao;
    private PedidoTransferencia pedidoTransferencia;
    private PedidoTransferenciaItem pedidoTransferenciaItem;
    private List<PedidoTransferenciaItem> pedidoTransferenciaItens = new ArrayList<PedidoTransferenciaItem>();
    private EstoqueEmpresa estoqueEmpresaDestino;
    private EstoqueEmpresa estoqueEmpresaOrigem;
    private DlgImpressaoObject<PedidoTransferencia> dlgConfirmarImpressaoPedidoTransferencia;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoEnvioPedido;
    private DlgConfirmacao dlgConfirmacaoAdicionar;
    private DlgNovoPedidoAlmoxarifado dlgNovoPedidoAlmoxarifado;
    private String msgResultadoSeparacao;
    private DlgMotivoArea dlgConfirmarCancelamento;
    private DlgMotivoArea dlgConfirmarSolicitacao;
    private String adicionarProximoLoteVencerSeparacaoPedido;
    private DTOPedidoTransferencia dTOPedidoTransferencia;

    private WebMarkupContainer containerCodBarraProduto;
    private LongField txtCodigoBarrasProduto;
    private String codigoBarrasProduto;
    private String utilizarLeitoraCodigoBarrasProduto;
    private String tipoResumoImpressaoSeparacaoPedido;
    private String validarSituacaoCodigoBarrasProduto;


    @Deprecated
    public SeparacaoPedidoTransferenciaPage() {
    }

    public SeparacaoPedidoTransferenciaPage(PedidoTransferencia pedidoTransferencia) {
        try {
            this.tipoResumoImpressaoSeparacaoPedido = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("tipoResumoImpressaoSeparacaoPedido");
            this.utilizarLeitoraCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizarLeitoraCodigoBarrasProduto");
            this.validarSituacaoCodigoBarrasProduto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("validarSituacaoCodigoBarrasProduto");
            adicionarProximoLoteVencerSeparacaoPedido = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("AdicionarProximoLoteVencerSeparacaoPedido");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        this.pedidoTransferencia = pedidoTransferencia;
        carregaItens();
        init();
    }

    public void init() {
        final Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(new DisabledInputField("pedidoTransferencia.empresaDestino.descricao"));
        form.add(new DisabledInputField("pedidoTransferencia.descricaoTipo"));
        form.add(dchDataPedido = new DateChooser("pedidoTransferencia.dataPedido"));
        form.add(table = new SelectionTable("table", getColumns(), getCollectionProvider()));
        table.populate();
        table.setScrollY("120px");
        table.setScrollCollapse(false);

        table.addSelectionAction(new ISelectionAction<PedidoTransferenciaItem>() {
            public void onSelection(AjaxRequestTarget target, PedidoTransferenciaItem object) {
                setCampos(target, object);
            }
        });

        dchDataPedido.setEnabled(false);

        containerItem = new WebMarkupContainer("containerItem", modelItem = new CompoundPropertyModel<DTOPedidoTransferenciaItensWeb>(new DTOPedidoTransferenciaItensWeb()));
        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(DTOPedidoTransferenciaItensWeb.PROP_PRODUTO));
        containerItem.add(txtQuantidadeEnv = new DoubleField(DTOPedidoTransferenciaItensWeb.PROP_QUANTIDADE).setNegativeValue(true));
        lotesSaidaChooser = new LotesSaidaChooser(DTOPedidoTransferenciaItensWeb.PROP_LOTES) {
            @Override
            public void confirmarAction(AjaxRequestTarget target) {
                super.confirmarAction(target);
                atualizaCampoQtEnv(target);
            }
        }.setAutoCompleteConsultaProduto(autoCompleteConsultaProduto).setDeposito(pedidoTransferencia.getDeposito()).setTxtQuantidade(txtQuantidadeEnv).setPermiteSugerirLoteAutomaticamente(true);

        containerItem.add(lotesSaidaChooser.registerEvents());
        containerItem.add(txtEstoqueDisponivel = new DisabledDoubleField("estoqueDisponivel", new PropertyModel<Double>(this, "estoqueDisponivel")).setNegativeValue(true));
        containerItem.add(txtPadraoEnvio = new DisabledDoubleField("padraoEnvio", new PropertyModel<Double>(this, "padraoEnvio")).setNegativeValue(true));
        containerItem.add(txtEstoqueUnidade = new DisabledDoubleField("estoqueUnidade", new PropertyModel<Double>(this, "estoqueUnidade")).setNegativeValue(true));
        containerItem.add(dchDataUltimoPedido = new DateChooser(DTOPedidoTransferenciaItensWeb.PROP_DATA_ULTIMO_PEDIDO));
        containerItem.add(txtQuantidadeSolic = new DisabledDoubleField("quantidadeSolic", new PropertyModel<Double>(this, "quantidadeSolic")).setNegativeValue(true));
        containerItem.add(txtStatus = new DisabledInputField("status", new PropertyModel<String>(this, "status")));
        dchDataUltimoPedido.setEnabled(false);
        autoCompleteConsultaProduto.setEnabled(false);
        containerItem.add(txtObservacao = new DisabledInputArea<>("observacao", new PropertyModel<String>(this, "observacao")));
        containerItem.add(tableAdd = new Table<DTOPedidoTransferenciaItensWeb>("tableAdd", getColumnsAdd(), getCollectionProviderAdd()) {

            @Override
            protected Item<DTOPedidoTransferenciaItensWeb> newRowItem(String id, int index, IModel<DTOPedidoTransferenciaItensWeb> model) {
                return new TableRow<DTOPedidoTransferenciaItensWeb>(id, index, model) {

                    @Override
                    public String getDefaultClass() {
                        DTOPedidoTransferenciaItensWeb rowObject = getRowObject();
                        if (rowObject != null) {
                            if (rowObject.getQuantidade() == null || rowObject.getQuantidade().compareTo(0D) == 0) {
                                return "quantidadeZero";
                            }
                            return super.getDefaultClass();
                        }

                        return super.getDefaultClass();
                    }

                    @Override
                    public void renderHead(IHeaderResponse response) {
                        super.renderHead(response);
                        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), "SeparacaoPedidoTransferenciaPageTableRowAdd.css")));
                    }

                };
            }

        });

        form.add(containerCodBarraProduto = new WebMarkupContainer("containerCodBarraProduto"));
        containerCodBarraProduto.setOutputMarkupPlaceholderTag(true);
        containerCodBarraProduto.add(txtCodigoBarrasProduto = new LongField("codigoBarrasProduto", new PropertyModel(this, "codigoBarrasProduto")));
        txtCodigoBarrasProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    carregarItemCodigoBarras(target);
                    target.appendJavaScript(JScript.removeAutoCompleteDrop());
                } catch (ValidacaoException | DAOException e) {
                    txtCodigoBarrasProduto.limpar(target);
                    target.focusComponent(txtCodigoBarrasProduto);
                    modalWarn(target, e);
                }
            }
        });
        containerCodBarraProduto.setVisible(RepositoryComponentDefault.SIM.equals(utilizarLeitoraCodigoBarrasProduto));

        addModal(dlgConfirmacaoAdicionar = new DlgConfirmacao(newModalId(), BundleManager.getString("msgDesejaAdicionarProdutoComQuantidadeZero")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                btnAdicionarAction(target);
            }
        });

        tableAdd.populate();
        tableAdd.setScrollY("120px");
        tableAdd.setScrollCollapse(false);

        containerItem.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (dTOPedidoTransferencia != null && !CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
                    addModal(target, dlgConfirmarSolicitacao = new DlgMotivoArea(newModalId(), BundleManager.getString("msgMotivoInclusaoSeparacao")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
                            pedidoTransferenciaItem.setDescricaoJustificativaSeparacao(motivo);
                            adicionarConfirmacao(target);
                        }
                    });
                    dlgConfirmarSolicitacao.setObject(pedidoTransferenciaItem);
                    dlgConfirmarSolicitacao.show(target);
                } else {
                    adicionarConfirmacao(target);
                }
            }
        });

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
                integrarStatusPedido(pedidoTransferencia);
            }
        }).setDefaultFormProcessing(false));

        form.add(containerItem);
        add(form);

        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
            table.setSelectedObject(pedidoTransferenciaItens.get(0));
            setCampos(null, pedidoTransferenciaItens.get(0));
        }
        autoCompleteConsultaProduto.addPropertiesLoad(new HQLProperties(SubGrupo.class, Produto.PROP_SUB_GRUPO).getProperties());
        autoCompleteConsultaProduto.getTxtDescricao().getTextField().add(new AjaxFormComponentUpdatingBehavior("onChange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (!CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
                    target.focusComponent(txtQuantidadeEnv);
                    if (lotesSaidaChooser != null) {
                        target.appendJavaScript(lotesSaidaChooser.getValidar(true));
                    }
                }
                atualizaCamposProduto(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                List<PedidoTransferenciaItem> pedidoTransferenciaItems = new ArrayList<>();
                if (dTOPedidoTransferencia != null && CollectionUtils.isNotNullEmpty(dTOPedidoTransferencia.getItens())) {
                    for (DTOPedidoTransferenciaItem dtoPedidoTransferenciaItem : dTOPedidoTransferencia.getItens()) {
                        pedidoTransferenciaItems.add(dtoPedidoTransferenciaItem.getPedidoTransferenciaItem());
                    }
                }
                List<Produto> gruposListProduto = Lambda.extract(pedidoTransferenciaItems, Lambda.on(PedidoTransferenciaItem.class).getProduto());

                if (gruposListProduto.contains(object)) {
                    for (int i = 0; i < dTOPedidoTransferencia.getItens().size(); i++) {
                        if (dTOPedidoTransferencia.getItens().get(i).getPedidoTransferenciaItem().getProduto().getCodigo().equals(object.getCodigo())) {
                            dTOPedidoTransferencia.getItens().remove(i);
                            break;
                        }
                    }
                }
                if (dTOPedidoTransferencia != null && !CollectionUtils.isNotNullEmpty(dTOPedidoTransferencia.getItens())) {
                    dTOPedidoTransferencia = null;
                }
            }
        });
    }

    private void integrarStatusPedido(PedidoTransferencia pedidoTransferencia) throws ValidacaoException {
        new IntegracaoPedidoHospital().integrarStatus(pedidoTransferencia, PedidoHospitalDTO.Status.SEPARANDO.value());
    }

    private void integrarPedido(PedidoTransferencia pedidoTransferencia) throws ValidacaoException {
        new IntegracaoPedidoHospital().integrar(pedidoTransferencia.getCodigo(), PedidoHospitalDTO.Status.PROCESSADO);
    }

    private void atualizaCampoQtEnv(AjaxRequestTarget target) {
        txtQuantidadeEnv.setEnabled(Coalesce.asDouble(lotesSaidaChooser.getQuantidadeTotal()) > 0D || RepositoryComponentDefault.NAO.equals(adicionarProximoLoteVencerSeparacaoPedido));
        target.add(txtQuantidadeEnv);
    }

    private void adicionarConfirmacao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        DTOPedidoTransferenciaItensWeb dto = modelItem.getObject();

        if (dto.getProduto() == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneProduto"));
        }

        if (dto.getProduto().getSubGrupo().isExigeGrupo() && RepositoryComponentDefault.NAO.equals(adicionarProximoLoteVencerSeparacaoPedido)) {
            if (Coalesce.asDouble(dto.getQuantidade()).compareTo(0D) > 0 && !CollectionUtils.isNotNullEmpty(dto.getLotes())) {
                throw new ValidacaoException(BundleManager.getString("campoXObrigatorio", BundleManager.getString("lote")));
            }
        }

        if (Coalesce.asDouble(quantidadeEnv) > Coalesce.asDouble(estoqueDisponivel) || Coalesce.asDouble(txtQuantidadeEnv.getComponentValue()) > Coalesce.asDouble(estoqueDisponivel)) {
            throw new ValidacaoException(BundleManager.getString("quantidadeSolicitadaNaoMaiorEstoqueDisponivelVar", Coalesce.asDouble(estoqueDisponivel)));
        }

        if ("Sim".equals(dto.getProduto().getSubGrupo().getFlagControlaLoteFormatado()) && (!CollectionUtils.isNotNullEmpty(dto.getLotes()) || Coalesce.asDouble(dto.getLotes().get(0).getQuantidade()).compareTo(0D) == 0)) {
            if (RepositoryComponentDefault.SIM.equals(adicionarProximoLoteVencerSeparacaoPedido) && 0D < Coalesce.asDouble(quantidadeEnv)) {
                adicionarProximoLoteVencer(target);
            } else {
                dlgConfirmacaoAdicionar.show(target);
            }
        } else {
            btnAdicionarAction(target);
        }
    }

    private void adicionarProximoLoteVencer(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        DTOPedidoTransferenciaItensWeb dto = modelItem.getObject();

        dto.setPedidoItem(pedidoTransferenciaItem);

        dto.setEstoqueEmpresaDestino(estoqueEmpresaDestino);
        dto.setEstoqueEmpresaOrigem(estoqueEmpresaOrigem);

        if (dto.getProduto().getSubGrupo().isExigeGrupo()) {
            List<MovimentoGrupoEstoqueItemDTO> list = PedidoHelper.proximosLotesVencer(estoqueEmpresaOrigem.getRoEmpresa(), dto.getProduto(), quantidadeEnv, pedidoTransferencia.getDeposito());
            if (CollectionUtils.isNotNullEmpty(list)) {
                Double totalQuantidade = Lambda.sum(list, Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getQuantidade());
                dto.setLotes(list);
                dto.setQuantidade(totalQuantidade);
                dto.getPedidoItem().setQuantidade(totalQuantidade);
            } else {
                throw new ValidacaoException(BundleManager.getString("quantidadeSolicitadaNaoMaiorEstoqueDisponivel"));
            }
        } else {
            dto.getPedidoItem().setQuantidade(txtQuantidadeSolic.getComponentValue());
        }

        addTabela(dto, target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        if (this.pedidoTransferenciaItem != null && this.pedidoTransferenciaItem.getProduto() != null && this.pedidoTransferenciaItem.getProduto().getSubGrupo() != null && this.pedidoTransferenciaItem.getProduto().getSubGrupo().isExigeGrupo()) {
            return lotesSaidaChooser.getBtnLotes();
        }
        return txtQuantidadeEnv;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("separacaoPedidoTransferencia");
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaItem.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidadeAbv"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("grupo"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidadeSolicitadaAbv"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_SOLICITADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("consumoMesAnterior"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_CONSUMO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("pacienteKit"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));

        return columns;
    }


    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<PedidoTransferenciaItem>() {
            @Override
            public void customizeColumn(final PedidoTransferenciaItem rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        cancelarItem(target, rowObject);
                        dlgConfirmarCancelamento.setObject(rowObject);
                        dlgConfirmarCancelamento.show(target);
                    }
                }).setTitleBundleKey("cancelar").setQuestionDialogBundleKey(null);
            }
        };
    }

    public void cancelarItem(AjaxRequestTarget target, PedidoTransferenciaItem item) {
        if (dlgConfirmarCancelamento == null) {
            addModal(target, dlgConfirmarCancelamento = new DlgMotivoArea(newModalId(), BundleManager.getString("msgConfirmarCancelamentoItemSolicitado")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, Object object) throws ValidacaoException, DAOException {
                    PedidoTransferenciaItem pti = (PedidoTransferenciaItem) object;
                    pti.setDescricaoJustificativaSeparacao(motivo);
                    pedidoTransferenciaItemsCancelados.add(pti);
                    removerItem(target, (PedidoTransferenciaItem) object);
                }
            });
        }
    }

    private void removerItem(AjaxRequestTarget target, PedidoTransferenciaItem modelObject) throws ValidacaoException, DAOException {
        for (int i = 0; i < pedidoTransferenciaItens.size(); i++) {
            if (pedidoTransferenciaItens.get(i) == modelObject) {
                pedidoTransferenciaItens.remove(i);
                break;
            }
        }
        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
            setCampos(target, pedidoTransferenciaItens.get(0));
        } else {
            limparItem(target);
            habilitarInsercaoNovoProduto(target);
        }
        table.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return pedidoTransferenciaItens;
            }
        };
    }

    private List<ISortableColumn<DTOPedidoTransferenciaItensWeb>> getColumnsAdd() {
        List<ISortableColumn<DTOPedidoTransferenciaItensWeb>> columns = new ArrayList<ISortableColumn<DTOPedidoTransferenciaItensWeb>>();
        ColumnFactory columnFactory = new ColumnFactory(DTOPedidoTransferenciaItensWeb.class);

        columns.add(getCustomColumnAdd());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidadeAbv"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidadeSolicitadaAbv"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_PEDIDO_ITEM, PedidoTransferenciaItem.PROP_QUANTIDADE_SOLICITADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidadeEnviadaAbv"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_QUANTIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("lotes"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_LOTES_FORMATADOS)));
        columns.add(columnFactory.createColumn(BundleManager.getString("dataUltimoPedidoAbv"), VOUtils.montarPath(DTOPedidoTransferenciaItensWeb.PROP_DATA_ULTIMO_PEDIDO)));

        return columns;
    }

    private CustomColumn getCustomColumnAdd() {
        return new CustomColumn<DTOPedidoTransferenciaItensWeb>() {
            @Override
            public Component getComponent(String componentId, final DTOPedidoTransferenciaItensWeb rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItem(target, rowObject);
                    }
                };
            }
        };
    }

    private void removerItem(AjaxRequestTarget target, DTOPedidoTransferenciaItensWeb _dto) {
        dtoItens.remove(_dto);

        List<PedidoTransferenciaItem> pedidoTransferenciaItems = new ArrayList<>();
        if (dTOPedidoTransferencia != null) {
            for (DTOPedidoTransferenciaItem dtoPedidoTransferenciaItem : dTOPedidoTransferencia.getItens()) {
                pedidoTransferenciaItems.add(dtoPedidoTransferenciaItem.getPedidoTransferenciaItem());
            }
        }
        List<Produto> gruposListProduto = Lambda.extract(pedidoTransferenciaItems, Lambda.on(PedidoTransferenciaItem.class).getProduto());

        if (gruposListProduto.contains(_dto.getProduto())) {
            for (int i = 0; i < dTOPedidoTransferencia.getItens().size(); i++) {
                if (dTOPedidoTransferencia.getItens().get(i).getPedidoTransferenciaItem().getProduto().getCodigo().equals(_dto.getProduto().getCodigo())) {
                    dTOPedidoTransferencia.getItens().remove(i);
                    break;
                }
            }
            if (!CollectionUtils.isNotNullEmpty(dTOPedidoTransferencia.getItens())) {
                dTOPedidoTransferencia = null;
            }
            tableAdd.update(target);
        } else {
            pedidoTransferenciaItens.add(_dto.getPedidoItem());
            tableAdd.update(target);
            table.update(target);
            table.setSelectedObject(pedidoTransferenciaItens.get(0));
            modelItem.getObject().setQuantidade(null);
            setCampos(target, pedidoTransferenciaItens.get(0));
            autoCompleteConsultaProduto.setEnabled(false);
            target.add(txtQuantidadeEnv);
        }
    }

    private void remover(AjaxRequestTarget target, PedidoTransferenciaItem _pedidoTransferenciaItem) {
        pedidoTransferenciaItens.remove(_pedidoTransferenciaItem);
        table.update(target);
    }

    private ICollectionProvider getCollectionProviderAdd() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dtoItens;
            }
        };
    }

    private void carregaItens() {
        LoadManager load = LoadManager.getInstance(PedidoTransferenciaItem.class).addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties()).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_DESCRICAO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_FLAG_CONTROLA_GRUPO_ESTOQUE)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_CODIGO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_DESCRICAO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO)).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL)).addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, pedidoTransferencia)).addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaItem.PROP_STATUS, QueryParameter.IGUAL, PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value()));

        if (RepositoryComponentDefault.GRUPO.equals(tipoResumoImpressaoSeparacaoPedido)) {
            load.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)));
            load.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        } else {
            load.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)));
            load.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO, SubGrupo.PROP_DESCRICAO)));
            load.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        }
        pedidoTransferenciaItens = load.start().getList();
    }

    private void setCampos(AjaxRequestTarget target, PedidoTransferenciaItem object) {
        try {
            this.pedidoTransferenciaItem = object;
            boolean exigeGrupo = object.getProduto().getSubGrupo().isExigeGrupo();
            Produto produto = object.getProduto();

            if (Objects.nonNull(target)) {
                limparItem(target);
                target.add(autoCompleteConsultaProduto, txtQuantidadeEnv);
                target.focusComponent(exigeGrupo ? lotesSaidaChooser.getBtnLotes() : txtQuantidadeEnv);
                autoCompleteConsultaProduto.setVO(target, produto);
                target.appendJavaScript(lotesSaidaChooser.getValidar(false));
                atualizaCampoQtEnv(target);
            } else {
                autoCompleteConsultaProduto.setComponentValue(produto);
                lotesSaidaChooser.setProduto(produto);
            }
            carregarCamposEstoques(object.getProduto(), target);

            txtObservacao.setComponentValue(object.getObservacao());
            lotesSaidaChooser.setDeposito(pedidoTransferencia.getDeposito());

            if (txtQuantidadeEnv != null) {
                boolean enable = !exigeGrupo || txtQuantidadeEnv.getValue().isEmpty();
                txtQuantidadeEnv.setEnabled(enable);
                if (enable) {
                    txtQuantidadeEnv.setComponentValue(Objects.nonNull(object.getQuantidadeSolicitada()) ? object.getQuantidadeSolicitada() : Double.MIN_VALUE);
                    if (Objects.nonNull(target)) {
                        target.focusComponent(txtQuantidadeEnv);
                        target.add(txtQuantidadeEnv);
                    }
                }
            }
        } catch (DAOException | ValidacaoException e) {
            e.printStackTrace();
        }
    }

    private void btnAdicionarAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validarFabricanteLotes(modelItem.getObject());
        DTOPedidoTransferenciaItensWeb dto = modelItem.getObject();

        dto.setPedidoItem(pedidoTransferenciaItem);

        dto.setEstoqueEmpresaDestino(estoqueEmpresaDestino);
        dto.setEstoqueEmpresaOrigem(estoqueEmpresaOrigem);

        if (dto.getProduto().getSubGrupo().isExigeGrupo()) {
            dto.setQuantidade(lotesSaidaChooser.getQuantidadeTotal());
            dto.getPedidoItem().setQuantidade(lotesSaidaChooser.getQuantidadeTotal());
        } else {
            if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
                dto.getPedidoItem().setQuantidade(dto.getQuantidade());
            } else {
                if (Objects.nonNull(dto.getQuantidade())) {
                    dto.getPedidoItem().setQuantidade(dto.getQuantidade());
                    dto.setQuantidade(dto.getQuantidade());
                    quantidadeEnv = dto.getQuantidade();
                } else if (Objects.nonNull(txtQuantidadeEnv) && Objects.nonNull(txtQuantidadeEnv.getComponentValue()) && txtQuantidadeEnv.getComponentValue() >= 0D) {
                    dto.getPedidoItem().setQuantidade(txtQuantidadeEnv.getComponentValue());
                    dto.setQuantidade(txtQuantidadeEnv.getComponentValue());
                    quantidadeEnv = txtQuantidadeEnv.getComponentValue();
                }
                dto.getPedidoItem().setQuantidadeSolicitada(quantidade);
                pedidoTransferenciaItem.setQuantidade(quantidadeEnv);
            }
        }

        addTabela(dto, target);
    }

    private void validarFabricanteLotes(DTOPedidoTransferenciaItensWeb dtoItem)throws DAOException, ValidacaoException {

        for (MovimentoGrupoEstoqueItemDTO lote : dtoItem.getLotes()) {
            if (Util.isNull(lote.getFabricante()) || Util.isNull(lote.getFabricante().getCodigo())) {
                throw new ValidacaoException(BundleManager.getString("msgLoteNaoPossuiFabricante", lote.getGrupoEstoque()));
            }
        }
    }

    public void addTabela(DTOPedidoTransferenciaItensWeb dto, AjaxRequestTarget target) {
        dtoItens.add(0, dto);
        remover(target, pedidoTransferenciaItem);
        limparItem(target);
        table.update(target);
        tableAdd.update(target);
        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
            table.setSelectedObject(pedidoTransferenciaItens.get(0));
            setCampos(target, pedidoTransferenciaItens.get(0));
        } else {
            habilitarInsercaoNovoProduto(target);
        }
    }

    private void limparItem(AjaxRequestTarget target) {
        modelItem.setObject(new DTOPedidoTransferenciaItensWeb());
        autoCompleteConsultaProduto.limpar(target);
        lotesSaidaChooser.limpar(target);
        txtQuantidadeEnv.limpar(target);
        txtEstoqueDisponivel.limpar(target);
        txtPadraoEnvio.limpar(target);
        txtEstoqueUnidade.limpar(target);
        dchDataUltimoPedido.limpar(target);
        txtQuantidadeSolic.limpar(target);
        txtStatus.limpar(target);
        txtObservacao.limpar(target);
    }

    private void carregarCamposEstoques(Produto produto, AjaxRequestTarget art) throws DAOException, ValidacaoException {

        EstoqueDepositoView estoqueDeposito = LoadManager.getInstance(EstoqueDepositoView.class).addProperties(new HQLProperties(EstoqueDepositoView.class).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(EstoqueDepositoView.PROP_EMPRESA, pedidoTransferencia.getEmpresaOrigem())).addParameter(new QueryCustom.QueryCustomParameter(EstoqueDepositoView.PROP_DEPOSITO, pedidoTransferencia.getDeposito())).addParameter(new QueryCustom.QueryCustomParameter(EstoqueDepositoView.PROP_PRODUTO, produto)).start().getVO();
        if (estoqueDeposito != null) {
            this.txtEstoqueDisponivel.setComponentValue(estoqueDeposito.getEstoqueDisponivel());
        }

        estoqueEmpresaOrigem = LoadManager.getInstance(EstoqueEmpresa.class).setId(new EstoqueEmpresaPK(produto, pedidoTransferencia.getEmpresaOrigem())).start().getVO();

        estoqueEmpresaDestino = LoadManager.getInstance(EstoqueEmpresa.class).addProperties(new HQLProperties(EstoqueEmpresa.class).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto)).start().getVO();
        if (estoqueEmpresaDestino != null) {
            this.txtPadraoEnvio.setComponentValue(estoqueEmpresaDestino.getQuantidadePadraoDispensacao());
            this.txtEstoqueUnidade.setComponentValue(estoqueEmpresaDestino.getEstoqueDisponivel());
        }

        if (this.pedidoTransferenciaItem.getEstoqueBaseHospitalar() != null) {
            this.txtEstoqueUnidade.setComponentValue(this.pedidoTransferenciaItem.getEstoqueBaseHospitalar());
        }

        List<PedidoTransferenciaItem> lstPedidoItem = LoadManager.getInstance(PedidoTransferenciaItem.class).addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties()).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_DATA_PEDIDO)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_EMPRESA_DESTINO), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO), produto)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_STATUS), QueryCustom.QueryCustomParameter.DIFERENTE, PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA), QueryParameter.DIFERENTE, pedidoTransferencia)).addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_DATA_PEDIDO), QueryCustom.QueryCustomSorter.DECRESCENTE)).addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_CODIGO), QueryCustom.QueryCustomSorter.DECRESCENTE)).start().getList();

        if (CollectionUtils.isNotNullEmpty(lstPedidoItem)) {
            this.dchDataUltimoPedido.setComponentValue(lstPedidoItem.get(0).getPedidoTransferencia().getDataPedido());
            this.txtQuantidadeSolic.setComponentValue(lstPedidoItem.get(0).getQuantidadeSolicitada());
            this.txtStatus.setComponentValue(lstPedidoItem.get(0).getDescricaoStatus());
            this.txtObservacao.setComponentValue(lstPedidoItem.get(0).getObservacao());
        }

        if (art != null) {
            art.add(txtEstoqueDisponivel);
            art.add(txtPadraoEnvio);
            art.add(txtStatus);
            art.add(txtEstoqueUnidade);
            art.add(dchDataUltimoPedido);
            art.add(txtQuantidadeSolic);
            art.appendJavaScript(JScript.initMasks());
        }
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {

        if (!pedidoTransferenciaItens.isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("favorAvaliarAdicionarTodosItensParaSalvar"));
        }

        if (CollectionUtils.isNotNullEmpty(dtoItens)) {
            String permiteGerarPedidoSeparacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("PermiteGerarPedidoSeparacao");

            if (RepositoryComponentDefault.SIM.equals(permiteGerarPedidoSeparacao)) {
                List<DTOPedidoTransferenciaItensWeb> selectItensPendentes = new ArrayList<>();

                for (DTOPedidoTransferenciaItensWeb dtoItem : dtoItens) {
                    if (dtoItem.getPedidoItem() != null && Coalesce.asDouble(dtoItem.getQuantidade()) < Coalesce.asDouble(dtoItem.getPedidoItem().getQuantidadeSolicitada())) {
                        selectItensPendentes.add((DTOPedidoTransferenciaItensWeb) SerializationUtils.clone(dtoItem));
                    }
                }

                if (CollectionUtils.isNotNullEmpty(selectItensPendentes)) {
                    initDlgNovoPedidoTransferenciaItens(target, selectItensPendentes, pedidoTransferencia);
                    return;
                }
            }

            if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItemsCancelados)) {
                for (PedidoTransferenciaItem pedidoTransferenciaItemsCancelado : pedidoTransferenciaItemsCancelados) {
                    String motivo = pedidoTransferenciaItemsCancelado.getDescricaoJustificativaSeparacao();
                    Long codigo = pedidoTransferenciaItemsCancelado.getCodigo();
                    BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferenciaItem(codigo, OrigemProcessoPedido.SEPARACAO, motivo);
                }
            }

            salvarSeparacaoPedido(target);

        } else if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItemsCancelados)) {
            cancelarPedido(pedidoTransferencia, pedidoTransferenciaItemsCancelados.get(pedidoTransferenciaItemsCancelados.size() - 1).getDescricaoJustificativaSeparacao());
        }

    }

    private void salvarSeparacaoPedido(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).iniciarConcluirSeparacaoPedido(dtoItens, pedidoTransferencia);

        if (pedidoSemQuantidadeInformada(dtoItens)) {
            msgResultadoSeparacao = "pedidoXCanceladoAutomaticamenteSemQuantidade";
            initDlgPedidoCanceladoAutomaticamente(target);
        } else {
            msgResultadoSeparacao = "separacaoPedidoXrealizadoComSucesso";
            try {
                String envioPedidoSeparacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("EnvioPedidoSeparacao");

                if (RepositoryComponentDefault.SIM.equals(envioPedidoSeparacao)) {
                    initDlgEnvioPedidoTransferencia(target, pedidoTransferencia);
                } else {
                    verificarImpressaoPedidoTransferencia(target, pedidoTransferencia);
                }
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
    }

    private boolean pedidoSemQuantidadeInformada(List<DTOPedidoTransferenciaItensWeb> dtoItens) {
        return !exists(dtoItens, having(on(DTOPedidoTransferenciaItensWeb.class).getQuantidade(), Matchers.greaterThan(0D)));
    }

    private void fechar(PedidoTransferencia pedidoTransferencia) {
        try {
            Page page = ConsultaSeparacaoPedidoTransferencia.class.newInstance();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString(msgResultadoSeparacao, pedidoTransferencia.getCodigo()));
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void initDlgNovoPedidoTransferenciaItens(AjaxRequestTarget target, List<DTOPedidoTransferenciaItensWeb> itensPendentes, PedidoTransferencia pedidoPai) throws ValidacaoException, DAOException {
        if (dlgNovoPedidoAlmoxarifado == null) {
            addModal(target, dlgNovoPedidoAlmoxarifado = new DlgNovoPedidoAlmoxarifado(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, DTOPedidoTransferencia dtoPedidoTransferencia) throws ValidacaoException, DAOException {
                    salvarSeparacaoPedido(target);
                    dtoPedidoTransferencia.getPedidoTransferencia().setPedidoTransferenciaPai(pedidoTransferencia);
                    PedidoTransferencia pedido = BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarPedidoTransferencia(dtoPedidoTransferencia, OrigemProcessoPedido.LANCAMENTO_WEB, true);
                    new IntegracaoPedidoHospital().integrar(pedido.getCodigo(),PedidoHospitalDTO.Status.PROCESSADO);
                    try {
                        Page page = ConsultaSeparacaoPedidoTransferencia.class.newInstance();
                        setResponsePage(page);
                        getSession().getFeedbackMessages().info(page, BundleManager.getString("msgSeparacaoGeradoNovoPedidoX", pedido.getCodigo()));
                    } catch (InstantiationException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (IllegalAccessException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    salvarSeparacaoPedido(target);
                }
            });
        }
        dlgNovoPedidoAlmoxarifado.show(target, itensPendentes, pedidoPai);
    }

    private void initDlgImpressaoPedidoTransferencia(AjaxRequestTarget target, PedidoTransferencia pedidoTransferencia) {
        addModal(target, dlgConfirmarImpressaoPedidoTransferencia = new DlgImpressaoObject<PedidoTransferencia>(newModalId(), bundle("msgDesejaImprimirPedidoTransferencia")) {
            @Override
            public DataReport getDataReport(PedidoTransferencia object) throws ReportException {
                final RelatorioEmbarquePedidoTransferenciaDTOParam bean = new RelatorioEmbarquePedidoTransferenciaDTOParam();
                bean.setCodigoPedidoTransferenciaList(Collections.singletonList(object.getCodigo()));
                bean.setExibirQuantidadePedido(RepositoryComponentDefault.NAO);
                bean.setOrdenacao(Produto.PROP_DESCRICAO);
                bean.setValidarSituacao(false);
                return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioEmbarquePedidoTransferenciaWeb(bean);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, PedidoTransferencia pedidoTransferencia) throws ValidacaoException, DAOException {
                fechar(pedidoTransferencia);
            }
        });
        dlgConfirmarImpressaoPedidoTransferencia.show(target, pedidoTransferencia);
    }

    private void verificarImpressaoPedidoTransferencia(AjaxRequestTarget target, PedidoTransferencia pedidoTransferencia) {
        try {
            String imprimirPedidoSeparacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ImprimirPedidoSeparacao");

            if (RepositoryComponentDefault.SIM.equals(imprimirPedidoSeparacao)) {
                initDlgImpressaoPedidoTransferencia(target, pedidoTransferencia);
            } else {
                fechar(pedidoTransferencia);
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void initDlgEnvioPedidoTransferencia(AjaxRequestTarget target, PedidoTransferencia pedidoTransferencia) {
        addModal(target, dlgConfirmacaoSimNaoEnvioPedido = new DlgConfirmacaoSimNao(newModalId(), bundle("msgDesejaRealizarEnvioPedidoTransferencia")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                PedidoTransferencia pedidoTransferencia = (PedidoTransferencia) getObject();

                EnviarPedidoTransferenciaDTO dto = new EnviarPedidoTransferenciaDTO();
                dto.setCodigoPedidoTransferencia(pedidoTransferencia.getCodigo());
                dto.setCodigoEmpresaDestino(pedidoTransferencia.getEmpresaDestino().getCodigo());
                dto.setValidarResponsavel(false);

                BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).enviarPedido(dto);
                integrarPedido(pedidoTransferencia);

                verificarImpressaoPedidoTransferencia(target, pedidoTransferencia);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                verificarImpressaoPedidoTransferencia(target, (PedidoTransferencia) getObject());
            }
        });
        dlgConfirmacaoSimNaoEnvioPedido.setObject(pedidoTransferencia);
        dlgConfirmacaoSimNaoEnvioPedido.show(target);
    }

    private void initDlgPedidoCanceladoAutomaticamente(AjaxRequestTarget target) {
        addModal(target, dlgPedidoCanceladoAutomaticamente = new DlgConfirmacaoOk(newModalId(), bundle(msgResultadoSeparacao, pedidoTransferencia.getCodigo())) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(pedidoTransferencia);
            }
        });

        dlgPedidoCanceladoAutomaticamente.show(target);
    }

    private void cancelarPedido(PedidoTransferencia object, String motivo) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferenciaSeparacao(object.getCodigo(), object.getVersion(), OrigemProcessoPedido.SEPARACAO, motivo);

        Page page = new ConsultaSeparacaoPedidoTransferencia();
        getSession().getFeedbackMessages().info(page, bundle("msgRegistrosSalvosSucesso"));
        setResponsePage(page);
    }


    private void habilitarInsercaoNovoProduto(AjaxRequestTarget target) {
        limparItem(target);
        autoCompleteConsultaProduto.setEnabled(true);
    }

    private void atualizaCamposProduto(AjaxRequestTarget target) {
        if (!CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens) && autoCompleteConsultaProduto.getComponentValue() != null) {
            DTOPedidoTransferenciaItensWeb dto = modelItem.getObject();

            Produto produto = dto.getProduto();

            boolean exists = LoadManager.getInstance(EstoqueEmpresa.class).addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM)).exists();

            if (!exists) {
                modalWarn(target, new ValidacaoException(bundle("msgProdutoInativoUnidadeX", pedidoTransferencia.getEmpresaDestino().getDescricao())));
                limparItem(target);
            } else {
                if (dtoItens != null) {
                    List<Produto> gruposList = Lambda.extract(dtoItens, Lambda.on(DTOPedidoTransferenciaItensWeb.class).getProduto());
                    if (gruposList.contains(dto.getProduto())) {
                        modalWarn(target, new ValidacaoException(bundle("itemJaAdicionado")));
                        limparItem(target);
                    } else {
                        PedidoTransferenciaItem pedidoTransferenciaItem = new PedidoTransferenciaItem();

                        Double saldoUnidade = EstoqueEmpresaHelper.getSaldoEmpresa(pedidoTransferencia.getEmpresaDestino(), produto);

                        Double consumoUltimos30Dias = null;
                        Double consumoUltimos90Dias = null;
                        Double consumoUltimos6Meses = null;
                        try {
                            consumoUltimos30Dias = EstoqueEmpresaHelper.getConsumoTrintaDias(pedidoTransferencia.getEmpresaDestino(), produto);
                            consumoUltimos90Dias = EstoqueEmpresaHelper.getConsumoNoventaDias(pedidoTransferencia.getEmpresaDestino(), produto);
                            consumoUltimos6Meses = EstoqueEmpresaHelper.getConsumoSeisMeses(pedidoTransferencia.getEmpresaDestino(), produto);
                        } catch (ValidacaoException e) {
                            Loggable.log.error(e);
                        } catch (DAOException e) {
                            Loggable.log.error(e);
                        }

                        // Campo Qtd Ultimo Pedido
                        List<PedidoTransferenciaItem> pedidoTransferenciaItemList = LoadManager.getInstance(PedidoTransferenciaItem.class).addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PROCESSADO.value(), PedidoTransferenciaItem.StatusPedidoTransferenciaItem.RECEBIDO.value(), PedidoTransferenciaItem.StatusPedidoTransferenciaItem.SEPARANDO.value()))).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_EMPRESA_DESTINO), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO), produto)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, pedidoTransferencia.getCodigo())).addSorter(new QueryCustom.QueryCustomSorter(PedidoTransferenciaItem.PROP_DATA_CADASTRO, QueryCustom.QueryCustomSorter.DECRESCENTE)).start().getList();

                        Double quantidadeUltimoPedido = null;
                        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItemList)) {
                            quantidadeUltimoPedido = pedidoTransferenciaItemList.get(0).getQuantidade();
                        }
                        pedidoTransferenciaItem.setProduto(produto);
                        pedidoTransferenciaItem.setQuantidade(quantidadeEnv);
                        pedidoTransferenciaItem.setObservacao(observacao);
                        pedidoTransferenciaItem.setSaldoEmpresa(saldoUnidade);
                        pedidoTransferenciaItem.setConsumoTrintaDias(consumoUltimos30Dias);
                        pedidoTransferenciaItem.setConsumoNoventaDias(consumoUltimos90Dias);
                        pedidoTransferenciaItem.setConsumoSeisMeses(consumoUltimos6Meses);
                        pedidoTransferenciaItem.setQuantidadeUltimoPedido(quantidadeUltimoPedido);
                        pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.RECEBIDO.value());
                        pedidoTransferenciaItem.setPedidoTransferencia(pedidoTransferencia);
                        this.pedidoTransferenciaItem = pedidoTransferenciaItem;

                        if (dTOPedidoTransferencia == null) {
                            dTOPedidoTransferencia = new DTOPedidoTransferencia();
                            dTOPedidoTransferencia.setPedidoTransferencia(pedidoTransferencia);
                        }

                        DTOPedidoTransferenciaItem dtoPedidoTransferenciaItem = new DTOPedidoTransferenciaItem();
                        dtoPedidoTransferenciaItem.setPedidoTransferenciaItem(pedidoTransferenciaItem);

                        try {
                            dtoPedidoTransferenciaItem.setEstoqueEmpresaDestino(carregarEstoqueEmpresa(produto, pedidoTransferencia.getEmpresaDestino()));
                            if (CollectionUtils.isNotNullEmpty(dTOPedidoTransferencia.getItens())) {
                                dTOPedidoTransferencia.getItens().add(dtoPedidoTransferenciaItem);
                            } else {
                                List<DTOPedidoTransferenciaItem> list = new ArrayList<>();
                                list.add(dtoPedidoTransferenciaItem);
                                dTOPedidoTransferencia.setItens(list);
                            }
                        } catch (DAOException | ValidacaoException e) {
                            Loggable.log.error(e);
                        }

                        setCampos(target, pedidoTransferenciaItem);
                    }
                }
                txtQuantidadeEnv.setEnabled(true);
                target.add(txtQuantidadeEnv);
            }

        } else {
            limparItem(target);
        }
    }

    private EstoqueEmpresa carregarEstoqueEmpresa(Produto produto, Empresa empresaDestino) throws DAOException, ValidacaoException {
        EstoqueEmpresa proxy = on(EstoqueEmpresa.class);
        return LoadManager.getInstance(EstoqueEmpresa.class).addProperty(path(proxy.getEstoqueFisico())).addProperty(path(proxy.getEstoqueMinimo())).addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProduto()), produto)).addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getEmpresa()), empresaDestino)).start().getVO();
    }

    private void carregarItemCodigoBarras(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        String codigoBarrasProduto = StringUtil.getDigits(this.codigoBarrasProduto);
        if (!"0".equals(codigoBarrasProduto) && !"".equals(codigoBarrasProduto)) {
            CodigoBarrasProduto cbp = LoadManager.getInstance(CodigoBarrasProduto.class).addProperties(new HQLProperties(CodigoBarrasProduto.class).getProperties()).addProperties(new HQLProperties(Produto.class, CodigoBarrasProduto.PROP_PRODUTO).getProperties()).addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties()).addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(CodigoBarrasProduto.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_REFERENCIA, codigoBarrasProduto)).addParameter(new QueryCustom.QueryCustomParameter(CodigoBarrasProduto.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, CodigoBarrasProduto.Status.TRANSFERIDO.value())).setMaxResults(1).start().getVO();

            if (cbp != null) {
                if (RepositoryComponentDefault.SIM.equals(validarSituacaoCodigoBarrasProduto)) {
                    if (cbp.isEtiquetaForaEstoque()) {
                        txtCodigoBarrasProduto.limpar(target);
                        target.focusComponent(txtCodigoBarrasProduto);
                        warn(target, bundle("codigoBarrasJaDispensado"));
                    } else {
                        criarGrupoEstoqueCodigoBarras(target, cbp);
                    }
                } else {
                    criarGrupoEstoqueCodigoBarras(target, cbp);
                }
            } else {
                txtCodigoBarrasProduto.limpar(target);
                target.focusComponent(txtCodigoBarrasProduto);
                warn(target, bundle("codigoBarrasIndisponivel"));
            }
        }
    }

    private void criarGrupoEstoqueCodigoBarras(AjaxRequestTarget target, CodigoBarrasProduto cbp) throws ValidacaoException, DAOException {
        EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK();
        estoqueEmpresaPK.setEmpresa(ApplicationSession.get().getSessaoAplicacao().getEmpresa());
        estoqueEmpresaPK.setProduto(cbp.getProduto());

        EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class).addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO)).addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_DESCRICAO)).addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, ApplicationSession.get().getSession().getEmpresa())).start().getVO();

        if (empresaMaterial == null) {
            throw new ValidacaoException("Não foi encontrado um depósito padrão definido para a empresa.");
        }

        GrupoEstoquePK grupoEstoquePK = new GrupoEstoquePK();
        grupoEstoquePK.setGrupo(cbp.getGrupo());
        grupoEstoquePK.setCodigoDeposito(empresaMaterial.getDeposito().getCodigo());
        grupoEstoquePK.setEstoqueEmpresa(new EstoqueEmpresa(estoqueEmpresaPK));
        grupoEstoquePK.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());

        GrupoEstoque ge = LoadManager.getInstance(GrupoEstoque.class).addProperties(new HQLProperties(GrupoEstoque.class).getProperties()).addProperties(new HQLProperties(EstoqueEmpresa.class, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA)).getProperties()).setId(grupoEstoquePK).start().getVO();

        if (ge == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_estoque_etiqueta_produto"));
        }

        boolean cdbJaInserido = false;
        if (CollectionUtils.isNotNullEmpty(dtoItens)) {
            for (DTOPedidoTransferenciaItensWeb pedidoTransferenciaItem : dtoItens) {
                if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItem.getLotes())) {
                    for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : pedidoTransferenciaItem.getLotes()) {
                        if (movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().contains(cbp)) {
                            txtCodigoBarrasProduto.limpar(target);
                            target.focusComponent(txtCodigoBarrasProduto);
                            warn(target, bundle("codigoBarrasJaInseridoNestaDispensacao"));
                            cdbJaInserido = true;
                        }
                    }
                }
            }
        }
        if (!cdbJaInserido) {
            Produto produto = LoadManager.getInstance(Produto.class).addProperties(new HQLProperties(Produto.class).getProperties()).addProperties(new HQLProperties(Unidade.class, Produto.PROP_UNIDADE).getProperties()).addProperties(new HQLProperties(Unidade.class, Produto.PROP_UNIDADE_PRESCRICAO).getProperties()).addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(Produto.PROP_SUB_GRUPO)).getProperties()).addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(Produto.PROP_TIPO_RECEITA)).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO, ge.getId().getEstoqueEmpresa().getId().getProduto())).start().getVO();
            adicionarProdutoEtiqueta(target, produto, cbp, ge);
            txtCodigoBarrasProduto.limpar(target);
        }
    }

    private void adicionarProdutoEtiqueta(AjaxRequestTarget target, Produto produto, CodigoBarrasProduto codigoBarrasProduto, GrupoEstoque ge) throws ValidacaoException, DAOException {
        updateNotification(target);
        PedidoTransferenciaItem item = null;
        boolean produtoContidoLista = false;
        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
            for (PedidoTransferenciaItem pedidoTransferenciaItem : pedidoTransferenciaItens) {
                if (pedidoTransferenciaItem.getProduto().getCodigo().equals(produto.getCodigo())) {
                    produtoContidoLista = true;
                    item = pedidoTransferenciaItem;
                    break;
                }
            }
        }
        codigoBarrasProduto.setPedidoTransferenciaItem(item);
        if (!produtoContidoLista) {
            boolean produtoAdicionadoLista = false;
            Integer aux = 0;
            if (CollectionUtils.isNotNullEmpty(dtoItens)) {
                for (DTOPedidoTransferenciaItensWeb dtoIten : dtoItens) {
                    if (dtoIten.getProduto().getCodigo().equals(produto.getCodigo())) {
                        codigoBarrasProduto.setPedidoTransferenciaItem(dtoIten.getPedidoItem());
                        produtoAdicionadoLista = true;
                        Integer index = getLote(dtoIten.getLotes(), codigoBarrasProduto);
                        MovimentoGrupoEstoqueItemDTO dto = null;
                        if (index != null) {
                            dto = dtoIten.getLotes().get(index);
                        }
                        Double qtd = dtoIten.getQuantidade() + codigoBarrasProduto.getQuantidadeProduto();

                        if (qtd.compareTo(dtoIten.getPedidoItem().getQuantidadeSolicitada()) > 0) {
                            warn(target, bundle("msgQuantidadeMaiorQueSolicitada"));
                            break;
                        }

                        if (dto != null) {
                            dto.setQuantidade(new Dinheiro(dto.getQuantidade()).somar(codigoBarrasProduto.getQuantidadeProduto()).doubleValue());
                            dto.getLstCodigoBarrasProduto().add(codigoBarrasProduto);
                            dtoIten.getLotes().set(index, dto);
                        } else {
                            MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO = montarMovimentoGrupoEstoqueItemDTO(codigoBarrasProduto, ge);
                            dtoIten.getLotes().add(movimentoGrupoEstoqueItemDTO);
                        }
                        dtoIten.getPedidoItem().setQuantidade(qtd);
                        dtoIten.setQuantidade(qtd);
                        dtoItens.set(aux, dtoIten);
                        tableAdd.populate();
                        target.add(tableAdd);
                        break;
                    }
                    aux++;
                }
            }
            if (!produtoAdicionadoLista) {
                target.focusComponent(txtCodigoBarrasProduto);
                warn(target, bundle("msgProdutoNaoEncontrado"));
            }
        } else {
            DTOPedidoTransferenciaItensWeb dto = modelItem.getObject();
            dto.setQuantidade(codigoBarrasProduto.getQuantidadeProduto());
            dto.setPedidoItem(item);
            dto.getPedidoItem().setProduto(item.getProduto());
            dto.getPedidoItem().setQuantidade(codigoBarrasProduto.getQuantidadeProduto());
            dto.setProduto(item.getProduto());

            MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO = montarMovimentoGrupoEstoqueItemDTO(codigoBarrasProduto, ge);

            if (dto.getLotes() == null) {
                dto.setLotes(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
            }
            dto.getLotes().add(movimentoGrupoEstoqueItemDTO);
            EstoqueEmpresa empresaOrigem = LoadManager.getInstance(EstoqueEmpresa.class).setId(new EstoqueEmpresaPK(produto, pedidoTransferencia.getEmpresaOrigem())).start().getVO();

            EstoqueEmpresa empresaDestino = LoadManager.getInstance(EstoqueEmpresa.class).addProperties(new HQLProperties(EstoqueEmpresa.class).getProperties()).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto)).start().getVO();

            dto.setEstoqueEmpresaOrigem(empresaOrigem);
            dto.setEstoqueEmpresaDestino(empresaDestino);

            addCodBarraTabela(dto, target);

            List<PedidoTransferenciaItem> lstPedidoItem = LoadManager.getInstance(PedidoTransferenciaItem.class).addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties()).addProperties(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_DATA_PEDIDO)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_EMPRESA_DESTINO), pedidoTransferencia.getEmpresaDestino())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO), produto)).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_STATUS), QueryCustom.QueryCustomParameter.DIFERENTE, PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value())).addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA), QueryParameter.DIFERENTE, pedidoTransferencia)).addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_DATA_PEDIDO), QueryCustom.QueryCustomSorter.DECRESCENTE)).start().getList();

            if (CollectionUtils.isNotNullEmpty(lstPedidoItem)) {
                dto.setDataUltimoPedido(lstPedidoItem.get(0).getPedidoTransferencia().getDataPedido());
            }
            target.focusComponent(txtCodigoBarrasProduto);
        }
        target.focusComponent(txtCodigoBarrasProduto);
    }

    private MovimentoGrupoEstoqueItemDTO montarMovimentoGrupoEstoqueItemDTO(CodigoBarrasProduto codigoBarrasProduto, GrupoEstoque ge) throws ValidacaoException {
        String grupo = codigoBarrasProduto.getGrupo();
        if (grupo.equals("0")) {
            grupo = "";
        }
        MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO = new MovimentoGrupoEstoqueItemDTO();
        movimentoGrupoEstoqueItemDTO.setProduto(codigoBarrasProduto.getProduto());
        movimentoGrupoEstoqueItemDTO.getLstCodigoBarrasProduto().add(codigoBarrasProduto);
        movimentoGrupoEstoqueItemDTO.setQuantidade(codigoBarrasProduto.getQuantidadeProduto());
        movimentoGrupoEstoqueItemDTO.setDataValidade(ge.getDataValidade());
        movimentoGrupoEstoqueItemDTO.setDeposito(ge.getRoDeposito());
        movimentoGrupoEstoqueItemDTO.setFabricante(ge.getFabricante());
        movimentoGrupoEstoqueItemDTO.setGrupoEstoque(grupo);
        movimentoGrupoEstoqueItemDTO.setQuantidadePersistida(0D);
        movimentoGrupoEstoqueItemDTO.setQuantidadeReservadaPersistida(0D);
        movimentoGrupoEstoqueItemDTO.setEstoqueReservado(0D);
        movimentoGrupoEstoqueItemDTO.setEstoqueEncomendado(0D);
        movimentoGrupoEstoqueItemDTO.setEstoqueFisico(0D);
        movimentoGrupoEstoqueItemDTO.setLocalizacaoEstrutura(ge.getId().getLocalizacaoEstrutura());
        return movimentoGrupoEstoqueItemDTO;
    }

    public void addCodBarraTabela(DTOPedidoTransferenciaItensWeb dto, AjaxRequestTarget target) {
        dtoItens.add(0, dto);
        remover(target, dto.getPedidoItem());
        limparItem(target);
        table.update(target);
        tableAdd.update(target);
        if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaItens)) {
            table.setSelectedObject(pedidoTransferenciaItens.get(0));
            setCampos(target, pedidoTransferenciaItens.get(0));
        }
    }

    private void updateNotification(AjaxRequestTarget target) {
        getSession().getFeedbackMessages().clear();
        updateNotificationPanel(target, false);
    }

    private Integer getLote(List<MovimentoGrupoEstoqueItemDTO> lstLote, CodigoBarrasProduto cbp) {
        if (CollectionUtils.isNotNullEmpty(lstLote)) {
            Integer aux = 0;
            for (MovimentoGrupoEstoqueItemDTO dto : lstLote) {
                String grupo;
                if (dto.getGrupoEstoque().isEmpty()) {
                    grupo = "0";
                } else {
                    grupo = dto.getGrupoEstoque();
                }
                if (grupo.equals(cbp.getGrupo())) {
                    return aux;
                }
                aux++;
            }
        }
        return null;
    }
}