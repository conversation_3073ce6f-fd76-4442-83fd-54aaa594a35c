package br.com.celk.view.controle.importacaoBolsaFamilia.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

public abstract class PnlImportacaoBolsaFamilia extends Panel {

    private final Collection<FileUpload> uploads = new ArrayList();
    private FileUploadField fileUploadField;
    private DropDown<Long> ano;
    private DropDown<String> semestre;

    public PnlImportacaoBolsaFamilia(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");

        Form formArquivo = new Form("formArquivo");
        formArquivo.setMultiPart(true);
        formArquivo.setOutputMarkupPlaceholderTag(true);
        formArquivo.add(fileUploadField = new FileUploadField("uploads", new PropertyModel(this, "uploads")));
        formArquivo.add(ano = DropDownUtil.getAnoDropDown("ano", true, false));
        formArquivo.add(semestre = getDropDownSemestre("semestre"));
        fileUploadField.setOutputMarkupId(true);
        form.add(formArquivo);


        form.add(new VoltarButton("btnVoltar"));
        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target);
                sendFile(target);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if (uploads.isEmpty()) throw new ValidacaoException(bundle("msgNecessarioAdicionarArquivoPoderConfirmar"));
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    private void sendFile(AjaxRequestTarget target) throws ValidacaoException, DAOException {

        String validarCnsCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarCnsCadastroNovo");
        final List<FileUpload> uploads = fileUploadField.getFileUploads();

        if (RepositoryComponentDefault.SIM.equals(validarCnsCadastroNovo)) {
            throw new ValidacaoException(BundleManager.getString("parametroValidarCnsCadastroNovo"));
        }

        if (uploads != null && !uploads.isEmpty()) {
            for (FileUpload upload : uploads) {
                try {
                    File newFile = File.createTempFile("importacao-bolsafamilia", ".json");
                    upload.writeTo(newFile);
                    BOFactoryWicket.getBO(BasicoFacade.class).enviarImportacaoBolsaFamiliaFila(upload.getClientFileName(), newFile, ano.getComponentValue(), semestre.getComponentValue());
                } catch (ValidacaoException | IOException ex) {
                    throw new ValidacaoException(ex.getMessage());
                }
            }
        } else {
            throw new ValidacaoException(BundleManager.getString("escolhaUmArquivoASerImportado"));
        }
    }


    private DropDown getDropDownSemestre(String id) {
        if (semestre == null) {
            semestre = new DropDown(id);
            semestre.addChoice(RepositoryComponentDefault.Semestre.PRIMEIRO_SEMESTRE.value(), RepositoryComponentDefault.Semestre.PRIMEIRO_SEMESTRE.descricao());
            semestre.addChoice(RepositoryComponentDefault.Semestre.SEGUNDO_SEMESTRE.value(), RepositoryComponentDefault.Semestre.SEGUNDO_SEMESTRE.descricao());
            semestre.setRequired(true);
        }
        return semestre;
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}