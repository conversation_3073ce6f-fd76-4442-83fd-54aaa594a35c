package br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.AreaFisicaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaAreaFisica;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

public class AreasFisicasPanel extends Panel {

    private AreaFisicaDTO dto;
    private Form form;
    private WebMarkupContainer containerAreaFisica;
    private CompoundPropertyModel<AreaFisicaDTO> modelAreaFisicaDTO;
    private Table tblAreaFisica;
    private List<AreaFisicaDTO> areaFisicaDTOList;
    private List<AreaFisicaDTO> areaFisicaDTOListExcluir = new ArrayList<>();
    private AreaFisicaDTO areaFisicaDTOEdicao;
    private DropDown areaConstruidaDD;
    private InputField areaIF;

    public AreasFisicasPanel(String id, RequerimentoVigilancia requerimentoVigilancia, boolean enabled) {
        super(id);
        this.dto = new AreaFisicaDTO();

        init(enabled);
    }

    private void init(boolean enabled){
        AreaFisicaDTO proxy = on(AreaFisicaDTO.class);
        form = new Form("form", modelAreaFisicaDTO = new CompoundPropertyModel(dto));

        containerAreaFisica = new WebMarkupContainer("containerAreaFisica");
        containerAreaFisica.setOutputMarkupId(true);

        containerAreaFisica.add(areaConstruidaDD = getDropDownAreaConstruida("areaConstruida"));
        containerAreaFisica.add(areaIF = new InputField("area"));

        containerAreaFisica.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        containerAreaFisica.add(tblAreaFisica = new Table("tblAreasFisicas", getColumns(), getCollectionProvider()));
        tblAreaFisica.populate();

        form.add(containerAreaFisica);
        form.add(new AjaxPreviewBlank());

        form.setEnabled(enabled);
        add(form);

    }

    private DropDown getDropDownAreaConstruida(String id){
        return DropDownUtil.getIEnumDropDown(id, RequerimentoInspecaoSanitariaAreaFisica.AreaConstruida.values(), true);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        AreaFisicaDTO areaFisicaDTO = modelAreaFisicaDTO.getObject();
        areaFisicaDTOList.add(areaFisicaDTO);
        target.add(form);
        modelAreaFisicaDTO.setObject(new AreaFisicaDTO());

    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        AreaFisicaDTO proxy = on(AreaFisicaDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("areaConstruida"), proxy.getAreaConstruidaFormatada()));
        columns.add(createColumn(bundle("area"), proxy.getArea()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AreaFisicaDTO>() {
            @Override
            public void customizeColumn(final AreaFisicaDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AreaFisicaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AreaFisicaDTO modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblAreaFisica, areaFisicaDTOList, modelObject);
                        if(modelObject.getRequerimentoInspecaoSanitariaAreaFisica() != null){
                            areaFisicaDTOListExcluir.add(modelObject);
                        }
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<AreaFisicaDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, AreaFisicaDTO modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });
            }
        };
    }

    private void editar(AjaxRequestTarget target, AreaFisicaDTO modelObject) {
        limparForm(target);
        areaFisicaDTOEdicao = (AreaFisicaDTO) SerializationUtils.clone(modelObject);
        modelAreaFisicaDTO.setObject(areaFisicaDTOEdicao);
        areaFisicaDTOList.remove(modelObject);
        target.add(areaConstruidaDD);
        target.add(areaIF);
    }

    private void limparForm(AjaxRequestTarget target){
        areaConstruidaDD.limpar(target);
        areaIF.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (areaFisicaDTOList == null) {
                    areaFisicaDTOList = new ArrayList<>();
                }
                return areaFisicaDTOList;
            }
        };
    }

    public List<AreaFisicaDTO> getAreaFisicaDTOList() {
        return areaFisicaDTOList;
    }

    public void setAreaFisicaDTOList(List<AreaFisicaDTO> areaFisicaDTOList) {
        this.areaFisicaDTOList = areaFisicaDTOList;
    }

    public List<AreaFisicaDTO> getAreaFisicaDTOListExcluir() {
        return areaFisicaDTOListExcluir;
    }
}

