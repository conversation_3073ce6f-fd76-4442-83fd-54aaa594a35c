package br.com.celk.view.consorcio.licitacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoPedidosLicitacaoDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;




/**
 * <AUTHOR>
 */
@Private

public class DetalhamentoPedidosLicitacaoPage extends RelatorioPage<RelatorioDetalhamentoPedidosLicitacaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private Empresa empresaLogada;
    private DropDown formaApresentacao;
    private DropDown tipoResumo;
    private DropDown situacao;
    private AutoCompleteConsultaPessoa autoCompleteConsultaPessoa;

    @Override
    public void init(Form form) {

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("consorciado"));

        if (getEmpresaLogada() != null) {
            // Se a empresa logada for um consorciado, ele seta automatico no auto complete e desabilita o campo
            if (empresaLogada.getTipoUnidade().equals(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)) {
                autoCompleteConsultaEmpresa.setComponentValue(empresaLogada);
                autoCompleteConsultaEmpresa.setEnabled(false);
            }
        }

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(autoCompleteConsultaPessoa = new AutoCompleteConsultaPessoa("pessoa"));
//        form.add(getDropDownTipoFornecedor());
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(situacao = DropDownUtil.getIEnumDropDown("status", PedidoLicitacaoItem.StatusPedidoLicitacaoItem.values(), true));
        form.add(formaApresentacao = DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioDetalhamentoPedidosLicitacaoDTOParam.FormaApresentacao.values()));
        form.add(tipoResumo = DropDownUtil.getEnumDropDown("tipoResumo", RelatorioDetalhamentoPedidosLicitacaoDTOParam.TipoResumo.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioDetalhamentoPedidosLicitacaoDTOParam.Ordenacao.values()));

        form.add(new InputField("codigoLicitacao"));
        form.add(new InputField("numeroPregao"));

        formaApresentacao.add((new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (formaApresentacao.getComponentValue().equals(RelatorioDetalhamentoPedidosLicitacaoDTOParam.FormaApresentacao.FORNECEDOR)) {
                    situacao.limpar(ajaxRequestTarget);
                    situacao.setComponentValue(PedidoLicitacaoItem.StatusPedidoLicitacaoItem.LICITADO.value());
                    situacao.setEnabled(false);
                } else {
                    if (!(tipoResumo.getComponentValue().equals(RelatorioDetalhamentoPedidosLicitacaoDTOParam.TipoResumo.FORNECEDOR))) {
                        situacao.limpar(ajaxRequestTarget);
                        situacao.setEnabled(true);
                    }
                }
                ajaxRequestTarget.add(formaApresentacao);
                ajaxRequestTarget.add(situacao);
            }
        }));
        tipoResumo.add((new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (tipoResumo.getComponentValue().equals(RelatorioDetalhamentoPedidosLicitacaoDTOParam.TipoResumo.FORNECEDOR)) {
                    situacao.limpar(ajaxRequestTarget);
                    situacao.setComponentValue(PedidoLicitacaoItem.StatusPedidoLicitacaoItem.LICITADO.value());
                    situacao.setEnabled(false);
                } else {
                    if (!(formaApresentacao.getComponentValue().equals(RelatorioDetalhamentoPedidosLicitacaoDTOParam.FormaApresentacao.FORNECEDOR))) {
                        situacao.limpar(ajaxRequestTarget);
                        situacao.setEnabled(true);
                    }
                }
                ajaxRequestTarget.add(formaApresentacao);
                ajaxRequestTarget.add(situacao);
            }
        }));

    }


    private Empresa getEmpresaLogada() {
        empresaLogada = SessaoAplicacaoImp.getInstance().getEmpresa();
        return empresaLogada;
    }


    @Override
    public Class<RelatorioDetalhamentoPedidosLicitacaoDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoPedidosLicitacaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoPedidosLicitacaoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ConsorcioFacade.class).relatorioDetalhamentoPedidosLicitacao(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoPedidosLicitacao");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
