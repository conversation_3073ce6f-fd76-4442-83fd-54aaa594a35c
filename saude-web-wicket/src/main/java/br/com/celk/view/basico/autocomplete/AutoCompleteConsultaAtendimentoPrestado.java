package br.com.celk.view.basico.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrestado;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaAtendimentoPrestado extends AutoCompleteConsulta<AtendimentoPrestado> {

    public AutoCompleteConsultaAtendimentoPrestado(String id) {
        super(id);
    }

    public AutoCompleteConsultaAtendimentoPrestado(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaAtendimentoPrestado(String id, IModel<AtendimentoPrestado> model) {
        super(id, model);
    }

    public AutoCompleteConsultaAtendimentoPrestado(String id, IModel<AtendimentoPrestado> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return AtendimentoPrestado.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AtendimentoPrestado.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), AtendimentoPrestado.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), AtendimentoPrestado.PROP_DESCRICAO);
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(AtendimentoPrestado.PROP_DESCRICAO, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return AtendimentoPrestado.class;
                    }

                };
            }

            @Override
            public List<BuilderQueryCustom.QueryParameter> getSearchParam(String searchCriteria) {
                return Arrays.<BuilderQueryCustom.QueryParameter>asList(new QueryCustom.QueryCustomParameter(AtendimentoPrestado.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("atendimento");
    }

}
