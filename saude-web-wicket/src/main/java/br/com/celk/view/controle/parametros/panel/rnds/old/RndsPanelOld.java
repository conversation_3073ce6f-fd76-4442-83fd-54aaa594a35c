package br.com.celk.view.controle.parametros.panel.rnds.old;

import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.io.FtpImageUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.controle.parametros.panel.template.ParametrosCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.ParametroRnds;
import br.com.ksisolucoes.vo.basico.base.BaseParametroRnds;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.comunicacao.base.BaseGerenciadorArquivo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class RndsPanelOld extends ParametrosCadastroPanel {

    private Form<ParametroRndsDTO> form;

    public RndsPanelOld(String id) {
        super(id, bundle("rnds"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel(new ParametroRndsDTO()));
        ParametroRndsDTO proxy = on(ParametroRndsDTO.class);

        carregar();

        DropDown enumDropDownAmbiente = DropDownUtil.getIEnumDropDown(path(proxy.getParametroRnds().getAmbiente()), ParametroRnds.Ambiente.values());

        FileUploadField fileUploadField = new FileUploadField(path(proxy.getCertificadoUpload()));
        Label lblCertificadoAnexado = new Label(path(proxy.getNomeArquivo()));
        lblCertificadoAnexado.setOutputMarkupId(true);

        Button btnAnexar = new SubmitButton("btnAnexar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws ValidacaoException {
                anexarCertificado(target, fileUploadField, lblCertificadoAnexado);
            }
        }).setDefaultFormProcessing(false);

        AbstractAjaxLink btnRemoverAnexo = new AbstractAjaxLink("btnRemoverAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                removerCertificado(target, lblCertificadoAnexado);
            }
        };
        PasswordField passwordField = new PasswordField(path(proxy.getParametroRnds().getPasswordCertificado()));
        InputField cnsSolicitante = new InputField(path(proxy.getParametroRnds().getCnsSolicitante()));
        InputField identificadorSolicitante = new InputField(path(proxy.getParametroRnds().getIdentificadorSolicitanteRnds()));
        DropDown dropDownUFSolicitante = getDropDownUFSolicitante(path(proxy.getParametroRnds().getUfSolicitante()));
        DropDown<Long> dropDownIntegraVacina = DropDownUtil.getNaoSimLongDropDown(String.valueOf(path(proxy.getParametroRnds().getIntegraVacinacao())), false, false);
        InputField servidorFCS = new InputField(path(proxy.getParametroRnds().getServidorFcs()));


        passwordField.addAjaxUpdateValue();
        cnsSolicitante.addAjaxUpdateValue();
        identificadorSolicitante.addAjaxUpdateValue();
        dropDownUFSolicitante.addAjaxUpdateValue();
        enumDropDownAmbiente.addAjaxUpdateValue();
        dropDownIntegraVacina.addAjaxUpdateValue();
        servidorFCS.addAjaxUpdateValue();

        cnsSolicitante.addRequiredClass();
        identificadorSolicitante.addRequiredClass();
        servidorFCS.addRequiredClass();

        form.add(enumDropDownAmbiente);
        form.add(fileUploadField);
        form.add(lblCertificadoAnexado);
        form.add(btnAnexar);
        form.add(btnRemoverAnexo);
        form.add(passwordField);
        form.add(cnsSolicitante);
        form.add(servidorFCS);
        form.add(identificadorSolicitante);
        form.add(dropDownUFSolicitante);
        form.add(dropDownIntegraVacina);

        add(form);
    }

    private DropDown getDropDownUFSolicitante(String id) {
        DropDown dropDownUFConselhoReg = new DropDown(id);
        dropDownUFConselhoReg.setLabel(new Model(BundleManager.getString("ufConselhoRegistro")));

        List<Estado> estados = LoadManager.getInstance(Estado.class)
                .addProperty(Estado.PROP_SIGLA)
                .addSorter(new QueryCustom.QueryCustomSorter(Estado.PROP_SIGLA))
                .start().getList();

        dropDownUFConselhoReg.addChoice(null, "");
        for (Estado estado : estados) {
            dropDownUFConselhoReg.addChoice(estado.getSigla(), estado.getSigla());
        }
        dropDownUFConselhoReg.setRequired(true);
        return dropDownUFConselhoReg;
    }

    public void removerCertificado(AjaxRequestTarget target, Label lblCertificadoAnexado) {
        getFormObject().setCertificadoUpload(null);
        MensagemAnexoDTO anexoCertificadoDigital = getFormObject().getAnexarCertificadoDigitalDTO();

        anexoCertificadoDigital.setNomeArquivoOriginal(bundle("nenhumCertificadoAnexado"));
        getFormObject().setNomeArquivo(anexoCertificadoDigital.getNomeArquivoOriginal());
        anexoCertificadoDigital.setNomeArquivoUpload(null);
        anexoCertificadoDigital.setOrigem(null);
        anexoCertificadoDigital.setPossuiAnexo(false);
        anexoCertificadoDigital.setGerenciadorArquivo(null);
        getFormObject().setArquivoAnexado(false);
        target.add(lblCertificadoAnexado);
    }

    public void anexarCertificado(AjaxRequestTarget target, FileUploadField fileUploadField, Label lblCertificadoAnexado) throws ValidacaoException {
        getFormObject().setCertificadoUpload(fileUploadField.getFileUpload());

        MensagemAnexoDTO anexoCertificadoDigital = getFormObject().getAnexarCertificadoDigitalDTO();
        FileUpload certificadoUpload = getFormObject().getCertificadoUpload();
        if (certificadoUpload != null) {
            String clientFileName = certificadoUpload.getClientFileName();
            if (!clientFileName.endsWith(".pfx") && !clientFileName.endsWith(".cer")) {
                throw new ValidacaoException(BundleManager.getString("msgCertificadoAnexadoInvalido"));
            }
            try {
                File newFile = certificadoUpload.writeToTempFile();
                anexoCertificadoDigital.setNomeArquivoOriginal(certificadoUpload.getClientFileName());
                anexoCertificadoDigital.setNomeArquivoUpload(newFile.getAbsolutePath());
                anexoCertificadoDigital.setOrigem(GerenciadorArquivo.OrigemArquivo.CERTIFICADO_DIGITAL.value());
                anexoCertificadoDigital.setPossuiAnexo(true);
                getFormObject().setNomeArquivo(clientFileName);
                target.add(lblCertificadoAnexado);

                getFormObject().setArquivoAnexado(true);
            } catch (IOException e) {
                Loggable.log.error(e.getMessage());
            }
        }
    }


    private void carregar() {
        ParametroRnds parametroRnds = LoadManager.getInstance(ParametroRnds.class)
                .addParameter(new QueryCustom.QueryCustomParameter(BaseParametroRnds.PROP_CODIGO, CargaBasicoPadrao.CODIGO_PARAMETROS_PADRAO))
                .start().getVO();
        if (parametroRnds != null) {
            form.getModel().getObject().setOldPassword(parametroRnds.getPasswordCertificado());
        }

        form.getModel().getObject().setParametroRnds(parametroRnds == null ? new ParametroRnds() : parametroRnds);
        carregarCertificadoDigital();
    }

    public void carregarCertificadoDigital() {
        MensagemAnexoDTO anexoCertificadoDigital = getFormObject().getAnexarCertificadoDigitalDTO();
        GerenciadorArquivo certificado = this.getFormObject().getParametroRnds().getCertificadoDigital();

        if (certificado != null) {
            certificado = LoadManager.getInstance(GerenciadorArquivo.class)
                    .addProperties(new HQLProperties(GerenciadorArquivo.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(BaseGerenciadorArquivo.PROP_CODIGO, certificado.getCodigo()))
                    .start().getVO();

            anexoCertificadoDigital.setGerenciadorArquivo(certificado);
            anexoCertificadoDigital.setNomeArquivoOriginal(certificado.getNomeArquivo());
            anexoCertificadoDigital.setNomeArquivoUpload(certificado.getCaminho());
            anexoCertificadoDigital.setOrigem(certificado.getOrigemArquivo());
            anexoCertificadoDigital.setPossuiAnexo(true);
            getFormObject().setArquivoAnexado(false);
            getFile(certificado);
            getFormObject().setNomeArquivo(certificado.getNomeArquivo());
        } else {
            anexoCertificadoDigital.setNomeArquivoOriginal(bundle("nenhumCertificadoAnexado"));
            getFormObject().setNomeArquivo(anexoCertificadoDigital.getNomeArquivoOriginal());
        }
    }

    private File getFile(GerenciadorArquivo gerenciadorArquivo) {
        if (gerenciadorArquivo != null) {
            String path = new FtpImageUtil().downloadImage(gerenciadorArquivo.getCaminho());
            if (path != null) {
                return new File(path);
            }
        }

        return null;
    }

    public ParametroRndsDTO getFormObject() {
        return form.getModel().getObject();
    }

}
