package br.com.celk.view.controle.menuweb;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.view.controle.menuweb.customize.CustomizeConsultaMenuWeb;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.annotation.PainelControle;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
@Private
@PainelControle
public class ConsultaMenuWebPage extends ConsultaPage<MenuWeb, List<BuilderQueryCustom.QueryParameter>> {

    private Long codigo;
    private InputField<String> txtDescricao;
    
    public ConsultaMenuWebPage() {
        super();
    }

    public ConsultaMenuWebPage(IModel<?> model) {
        super(model);
    }

    public ConsultaMenuWebPage(PageParameters parameters) {
        super(parameters);
    }
    
    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters(){
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(MenuWeb.PROP_CODIGO), codigo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(MenuWeb.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.LIKE, txtDescricao.getComponentValue()));
        
        return parameters;
    }
    
    private CustomColumn<MenuWeb> getCustomColumn(){
        return new CustomColumn<MenuWeb>() {

            @Override
            public Component getComponent(String componentId, final MenuWeb rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMenuWebPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroMenuWebPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_menu");
    }

    @Override
    public void initForm(Form form) {
        form.add(new InputField<Long>("codigo", new PropertyModel<Long>(this, "codigo")));
        form.add(txtDescricao = new InputField("txtDescricao", new Model()));
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(MenuWeb.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), MenuWeb.PROP_CODIGO, VOUtils.montarPath(MenuWeb.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(MenuWeb.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("menu_pai"), VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_CODIGO), VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("programa"), VOUtils.montarPath(MenuWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_DESCRICAO)));
        
        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaMenuWeb());
    }

    @Override
    public Class getCadastroPage() {
        return CadastroMenuWebPage.class;
    }

}
