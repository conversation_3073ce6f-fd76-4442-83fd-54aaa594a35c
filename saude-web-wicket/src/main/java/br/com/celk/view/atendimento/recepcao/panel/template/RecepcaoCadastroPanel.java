package br.com.celk.view.atendimento.recepcao.panel.template;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;

/**
 *
 * <AUTHOR>
 */
public abstract class RecepcaoCadastroPanel extends DefaultRecepcaoPanel{

    public RecepcaoCadastroPanel(String id, String titulo) {
        super(id);
        add(new Label("titulo", titulo));
    }

    @Override
    public void postConstruct() {
    }

    @Override
    public void changePanelAction(AjaxRequestTarget target) {
    }
}
