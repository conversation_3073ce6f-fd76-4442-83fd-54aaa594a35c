package br.com.celk.view.unidadesaude.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.QueryRelatorioAtendimentoOdontologicoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioAtendimentoOdontologicoPage extends RelatorioPage<QueryRelatorioAtendimentoOdontologicoDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownTipoProcedimento;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new RequiredPnlDatePeriod("period"));
        form.add(DropDownUtil.getNaoSimDropDown("exibirUsuario"));
        form.add(getDropDownTipoProcedimento());
        
    }
    
    public DropDown<String> getDropDownTipoProcedimento(){
        if (dropDownTipoProcedimento == null) {
            dropDownTipoProcedimento = new DropDown<String>("tipoProcedimento");
            dropDownTipoProcedimento.addChoice(null, BundleManager.getString("ambos"));
            dropDownTipoProcedimento.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("naoFaturavel"));
            dropDownTipoProcedimento.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("faturavel"));
        }
        
        return dropDownTipoProcedimento;
    }
    
    public DropDown<String> getDropDownFormaApresentacao(){
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            dropDownFormaApresentacao.addChoice(BundleManager.getString("geral"), BundleManager.getString("geral"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("profissional"), BundleManager.getString("profissional"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("usuarioCadsus"), BundleManager.getString("usuarioCadsus"));
            dropDownFormaApresentacao.addChoice(BundleManager.getString("cbo"), BundleManager.getString("cbo"));
        }
        
        return dropDownFormaApresentacao;
    }

    @Override
    public Class getDTOParamClass() {
        return QueryRelatorioAtendimentoOdontologicoDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(QueryRelatorioAtendimentoOdontologicoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioAtendimentoOdontologico(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioAtendimentoOdontologico");
    }
}
