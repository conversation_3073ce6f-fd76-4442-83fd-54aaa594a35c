package br.com.celk.view.hospital.aih.dialog;

import br.com.celk.aih.dto.AIHConsultaImpressaoDTO;
import br.com.celk.bo.aih.interfaces.facade.AIHFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioImpressaoLaudoSolicitacaoDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoMudancaProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoProcedimentos;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlImprimirDocumentosAIH extends Panel {

    private Long aihCodigo;
    private Button btnFechar;
    private Form form;
    private Table impressaoAih;

    public PnlImprimirDocumentosAIH(String id) {
        super(id);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(new LoadableObjectModel<Aih>(Aih.class, new Aih())));

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        form.add(impressaoAih = new Table("impressaoAih", getColumns(), getCollectionProvider()));
        impressaoAih.populate();

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        AIHConsultaImpressaoDTO proxy = on(AIHConsultaImpressaoDTO.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("tipo"), proxy.getTipoProcedimento()));
        columns.add(new DateColumn(bundle("dataSolicitacao"), path(proxy.getDataSolicitacao())));
        //columns.add(createColumn(bundle("codigo"), proxy.getCodigo()));
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimento().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider<AIHConsultaImpressaoDTO, Long>() {
            @Override
            public Collection getCollection(Long codigo) throws DAOException, ValidacaoException {
                Aih aih = LoadManager.getInstance(Aih.class)
                        .addProperties(new HQLProperties(Aih.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_CODIGO, aihCodigo))
                        .start().getVO();
                AIHConsultaImpressaoDTO dto = new AIHConsultaImpressaoDTO();
                dto.setCodigo(aih.getCodigo());
                dto.setProcedimento(aih.getProcedimentoSolicitado());
                dto.setTipoProcedimento(Bundle.getStringApplication("laudoAih"));
                dto.setDataSolicitacao(aih.getDataCadastro());
                List<AIHConsultaImpressaoDTO> lista = new ArrayList<AIHConsultaImpressaoDTO>();
                lista.add(dto);
                lista.addAll(BOFactoryWicket.getBO(AIHFacade.class).consultaSolicitacaoMudancaProcedimentoAIH(aihCodigo));
                lista.addAll(BOFactoryWicket.getBO(AIHFacade.class).consultaSolicitacaoProcedimentosAIH(aihCodigo));
                return lista;
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AIHConsultaImpressaoDTO>() {
            @Override
            public void customizeColumn(AIHConsultaImpressaoDTO rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<AIHConsultaImpressaoDTO>() {

                    @Override
                    public DataReport action(AIHConsultaImpressaoDTO modelObject) {
                        if (modelObject.getTipoProcedimento().equals(AIHConsultaImpressaoDTO.TipoProcedimento.SOLICITACAOMUDANCA.getValue())) {
                            try {
                                SolicitacaoMudancaProcedimento smp = new SolicitacaoMudancaProcedimento();
                                smp.setCodigo(modelObject.getCodigo());
                                return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirLaudoSolicitacaoMudancaProcedimento(smp);
                            } catch (ReportException ex) {
                                Logger.getLogger(PnlImprimirDocumentosAIH.class.getName()).log(Level.SEVERE, null, ex);
                            }
                        } else if (modelObject.getTipoProcedimento().equals(AIHConsultaImpressaoDTO.TipoProcedimento.SOLICITACAOESPECIAL.getValue())) {
                            try {
                                SolicitacaoProcedimentos sp = new SolicitacaoProcedimentos();
                                sp.setCodigo(modelObject.getCodigo());
                                return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirSolicitacaoProcedimento(sp);
                            } catch (ReportException ex) {
                                Logger.getLogger(PnlImprimirDocumentosAIH.class.getName()).log(Level.SEVERE, null, ex);
                            }
                        } else if (modelObject.getTipoProcedimento().equals(AIHConsultaImpressaoDTO.TipoProcedimento.LAUDOAIH.getValue())) {
                            try {
                                final RelatorioImpressaoLaudoSolicitacaoDTOParam param = new RelatorioImpressaoLaudoSolicitacaoDTOParam();
                                param.setCodigoAutorizacao(aihCodigo);
                                return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoLaudoSolicitacao(param);
                            } catch (ReportException ex) {
                                Logger.getLogger(PnlImprimirDocumentosAIH.class.getName()).log(Level.SEVERE, null, ex);
                            }
                        }
                        return null;
                    }

                }
                );
            }
        };
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setAih(AjaxRequestTarget target, Long codigo) {
        this.aihCodigo = codigo;
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {

    }
}
