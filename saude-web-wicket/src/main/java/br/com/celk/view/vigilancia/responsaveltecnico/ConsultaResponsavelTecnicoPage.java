package br.com.celk.view.vigilancia.responsaveltecnico;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.cpffield.CpfField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vigilancia.responsaveltecnico.customize.CustomizeConsultaResponsavelTecnico;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaResponsavelTecnicoPage extends ConsultaPage<ResponsavelTecnico, List<BuilderQueryCustom.QueryParameter>> {

    private String nome;
    private String cpf;

    public ConsultaResponsavelTecnicoPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("nome"));
        form.add(new CpfField("cpf"));

        setExibeExpandir(true);
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(ResponsavelTecnico.class);

        ResponsavelTecnico proxy = on(ResponsavelTecnico.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), path(proxy.getNome())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cpf"), path(proxy.getCpfFormatado())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("telefone"), path(proxy.getTelefoneFormatado())));
        columns.add(columnFactory.createColumn(BundleManager.getString("registro"), path(proxy.getDescricaoRegistro())));

        return columns;
    }

    private CustomColumn<ResponsavelTecnico> getCustomColumn() {
        return new CustomColumn<ResponsavelTecnico>() {

            @Override
            public Component getComponent(String componentId, final ResponsavelTecnico rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroResponsavelTecnicoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroResponsavelTecnicoPage(rowObject, true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaResponsavelTecnico()) {
            
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ResponsavelTecnico.PROP_NOME, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ResponsavelTecnico.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome));
        if (this.cpf != null && !this.cpf.trim().equals("")) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ResponsavelTecnico.PROP_CPF), BuilderQueryCustom.QueryParameter.ILIKE, cpf));
        }
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroResponsavelTecnicoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaResponsavelTecnico");
    }
}
