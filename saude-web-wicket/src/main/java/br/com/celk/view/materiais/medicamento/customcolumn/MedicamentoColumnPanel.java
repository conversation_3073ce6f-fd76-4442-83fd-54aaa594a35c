package br.com.celk.view.materiais.medicamento.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.materiais.medicamento.ConsultaMedicamentoPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.CadastroMedicamentoDTO;
import br.com.celk.view.materiais.medicamento.CadastroMedicamentoPage;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.*;

import java.util.ArrayList;

import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class MedicamentoColumnPanel extends Panel implements PermissionContainer {

    private AjaxLink btnEditar;
    private AjaxLink btnExcluir;
    private AjaxLink btnConsultar;
    private AjaxLink btnClonar;
    private AjaxLink btnReativar;
    private AjaxLink btnDesativar;
    private DlgConfirmacao dlgConfirmacao;
    private Produto produto;

    public MedicamentoColumnPanel(String id, Produto produto) {
        super(id);
        this.produto = produto;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                editar(target);
            }
        });
        add(btnExcluir = new AbstractAjaxLink("btnExcluir") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao != null) {
                    dlgConfirmacao.show(target);
                }
            }
        });
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                consultar(target);
            }
        });
        add(btnClonar = new AbstractAjaxLink("btnClonar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                CadastroMedicamentoDTO dto = carregarMedicamento();
                Produto newProduto = (Produto) new DefinerPropertiesCloning().define(dto.getProduto());
                newProduto.setCodigo(null);
                dto.setProduto(newProduto);
                dto.setProdutoConvenioList(new ArrayList<ProdutoConvenio>());
                setResponsePage(new CadastroMedicamentoPage(dto));
            }
        });


        add(btnReativar = new AbstractAjaxLink("btnReativar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                produto.setFlagAtivo((Long) Produto.Situacao.ATIVO.value());
                BOFactoryWicket.save(produto);
                updateTable(target);
            }
        });

        add(btnDesativar = new AbstractAjaxLink("btnDesativar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                EstoqueEmpresa proxy = on(EstoqueEmpresa.class);

                //Faz verificacao se o produto está ativo para alguma empresa, se estiver, nao deixa inativar
                //Se não tiver deixa inativar.
                boolean inativoParaEmpresa = LoadManager.getInstance(EstoqueEmpresa.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProduto()), produto))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getFlagAtivo()), RepositoryComponentDefault.SIM))
                        .exists();

                if (inativoParaEmpresa) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_inativar_produto_pois_mesmo_esta_ativo_outras_unidade"));
                } else {
                    produto.setFlagAtivo((Long) Produto.Situacao.INATIVO.value());
                    BOFactoryWicket.save(produto);
                    setResponsePage(new ConsultaMedicamentoPage());
                }
            }
        });

        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnExcluir.add(new AttributeModifier("title", BundleManager.getString("excluir")));
        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
        btnClonar.add(new AttributeModifier("title", BundleManager.getString("clonar")));
        btnReativar.add(new AttributeModifier("title", BundleManager.getString("ativar")));
        btnDesativar.add(new AttributeModifier("title", BundleManager.getString("desativar")));


        btnReativar.setVisible(produto.getFlagAtivo().equals(Produto.Situacao.INATIVO.value()));
        btnDesativar.setVisible(produto.getFlagAtivo().equals(Produto.Situacao.ATIVO.value()));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteExcluir") + "?") {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    excluir(target);
                }
            });
        }
    }

    private void excluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(ProdutoFacade.class).ExcluirProduto(produto);
        updateTable(target);
    }

    private void consultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        setResponsePage(new CadastroMedicamentoPage(carregarMedicamento(), true));
    }

    public abstract void updateTable(AjaxRequestTarget target);

    private void editar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        setResponsePage(new CadastroMedicamentoPage(carregarMedicamento()));
    }

    public CadastroMedicamentoDTO carregarMedicamento() throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(ProdutoFacade.class).loadCadastroMedicamentoDTO(produto.getCodigo());
    }

}
