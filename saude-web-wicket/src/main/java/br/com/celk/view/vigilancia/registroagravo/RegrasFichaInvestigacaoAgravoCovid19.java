package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoCovid19DTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemMorbidadeCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19;
import br.com.ksisolucoes.vo.vigilancia.investigacao.dto.ExameDto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class RegrasFichaInvestigacaoAgravoCovid19 implements Serializable {
    private final InvestigacaoAgravoCovid19 investigacaoAgravo;
    private final FichaInvestigacaoAgravoCovid19DTO fichaCovidDTO;

    public RegrasFichaInvestigacaoAgravoCovid19(InvestigacaoAgravoCovid19 investigacaoAgravo, FichaInvestigacaoAgravoCovid19DTO fichaCovidDTO) {
        this.fichaCovidDTO = fichaCovidDTO;
        this.investigacaoAgravo = investigacaoAgravo;
    }

    protected void initRegras() {
        configuraInvestigacaoAgravo();
        if (fichaCovidDTO.getTriagem() != null)
            carregaSintomas(InvestigacaoAgravoCovid19.listaSintomas(fichaCovidDTO.getTriagem()));
        if (fichaCovidDTO.getTriagem() != null)
            carregaCondicoes(InvestigacaoAgravoCovid19.listaCondicoes(fichaCovidDTO.getTriagem()));
        inicializaStatusRegistroAgravo();
        fichaCovidDTO.setInvestigacaoAgravoCovid19(investigacaoAgravo);
        fichaCovidDTO.setInvestigacaoAgravoCovid19Old(investigacaoAgravo); //Clona o objeto InvestigacaoAgravo
        configuraTriagem(InvestigacaoAgravoCovid19.buscaTriagemCovid19(fichaCovidDTO.getRegistroAgravo()));
    }

    protected void configuraTriagem(FormularioTriagemCovid19 triagem) {
        fichaCovidDTO.setTriagem(triagem);
        if (fichaCovidDTO.getTriagem() != null)
            fichaCovidDTO.setOutrosObservacao(fichaCovidDTO.getTriagem().getOutrosSintomas());
    }

    public void verificaExameNaInvestigacao(ExameDto exameDtoTesteRapido, ExameDto exameDtoExameExterno) {
        if (fichaCovidDTO.getInvestigacaoAgravoCovid19() != null && fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste() == null) {
            if (exameDtoExameExterno != null && exameDtoExameExterno.compareTo(exameDtoTesteRapido) <= 0) {
                configurarExameNaInvestigacao(exameDtoExameExterno);
            } else if (exameDtoTesteRapido != null) {
                configurarExameNaInvestigacao(exameDtoTesteRapido);
            }
        }
    }

    public boolean isDataPrimeirosSintomasAnteriorDataColeta(RegistroAgravo registroAgravo, Date dataColeta) {
        if (registroAgravo.getDataPrimeirosSintomas().after(dataColeta))
            return false;
        return true;
    }

    protected void inicializaStatusRegistroAgravo() {
        if (!RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value().equals(fichaCovidDTO.getRegistroAgravo().getStatus())) {
            fichaCovidDTO.getRegistroAgravo().setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        }
    }

    public void inicializaProfissionalInvestigacao(Profissional profissional) {
        if (profissional != null) {
            fichaCovidDTO.getRegistroAgravo().setProfissionalInvestigacao(profissional);
        } else {
            fichaCovidDTO.getRegistroAgravo().setProfissionalInvestigacao(investigacaoAgravo.getRegistroAgravo().getProfissional());
        }
    }

    public void configurarExameNaInvestigacao(ExameDto exameDto) {
        if (isDataPrimeirosSintomasAnteriorDataColeta(investigacaoAgravo.getRegistroAgravo(), exameDto.getDataColeta())) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setTipoTeste(exameDto.getTipoTeste());
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setResultadoTeste(exameDto.getResultadoTeste() != null ? exameDto.getResultadoTeste().getValue() : null);
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDataColetaTeste(exameDto.getDataColeta());

            fichaCovidDTO.getInvestigacaoAgravoCovid19().setConcluido(RepositoryComponentDefault.SIM_LONG);
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setColetado(RepositoryComponentDefault.NAO_LONG);
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setSolicitado(RepositoryComponentDefault.NAO_LONG);
        }
    }

    protected void configuraInvestigacaoAgravo() {
        if (investigacaoAgravo == null) {
            return;
        }
        if (investigacaoAgravo.getDataColetaTeste() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDataColetaTeste(investigacaoAgravo.getDataColetaTeste());
        }
        if (investigacaoAgravo.getTipoTeste() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setTipoTeste(investigacaoAgravo.getTipoTeste());
        }
        if (investigacaoAgravo.getResultadoTeste() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setResultadoTeste(investigacaoAgravo.getResultadoTeste());
        }
        if (investigacaoAgravo.getClassificacaoFinal() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setClassificacaoFinal(investigacaoAgravo.getClassificacaoFinal());
        }
        if (investigacaoAgravo.getObservacao() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setObservacao(investigacaoAgravo.getObservacao());
        }
        if (investigacaoAgravo.getProfissionalSaude() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setProfissionalSaude(investigacaoAgravo.getProfissionalSaude());
        } else {
            if (fichaCovidDTO.getTriagem() != null && fichaCovidDTO.getTriagem().getProfissionalSaude() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setProfissionalSaude(InvestigacaoAgravoCovid19.ProfissionalSaude.SIM.getValue().equals(fichaCovidDTO.getTriagem().getProfissionalSaude()) ? InvestigacaoAgravoCovid19.ProfissionalSaude.SIM.getValue() : InvestigacaoAgravoCovid19.ProfissionalSaude.NAO.getValue());
            }
        }
    }

    public void carregaSintomas(List<FormularioTriagemSintomasCovid19> listaSintomas) {
        for (FormularioTriagemSintomasCovid19 sintoma : listaSintomas) {
            if (sintoma.getSintomaCovid19().getCodigo() == 3L) {
                fichaCovidDTO.setDorGarganta(sintoma.getSintomaCovid19());
            } else if (sintoma.getSintomaCovid19().getCodigo() == 23L) {
                fichaCovidDTO.setDispneia(sintoma.getSintomaCovid19());
            } else if (sintoma.getSintomaCovid19().getCodigo() == 1L) {
                fichaCovidDTO.setFebre(sintoma.getSintomaCovid19());
            } else if (sintoma.getSintomaCovid19().getCodigo() == 2L) {
                fichaCovidDTO.setTosse(sintoma.getSintomaCovid19());
            } else if (sintoma.getSintomaCovid19().getCodigo() == 24L) {
                fichaCovidDTO.setOutros(sintoma.getSintomaCovid19());
            }
        }

        configuraSintomas();
    }

    private void configuraSintomas() {
        if (investigacaoAgravo == null) {
            return;
        }
        if (investigacaoAgravo.getDorGarganta() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDorGarganta(investigacaoAgravo.getDorGarganta());
        } else if (fichaCovidDTO.getDorGarganta() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDorGarganta(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        }

        if (investigacaoAgravo.getDispneia() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDispneia(investigacaoAgravo.getDispneia());
        } else if (fichaCovidDTO.getDispneia() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setDispneia(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        }

        if (investigacaoAgravo.getFebre() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setFebre(investigacaoAgravo.getFebre());
        } else if (fichaCovidDTO.getFebre() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setFebre(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        }

        if (investigacaoAgravo.getTosse() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setTosse(investigacaoAgravo.getTosse());
        } else if (fichaCovidDTO.getTosse() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setTosse(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        }

        if (investigacaoAgravo.getOutrosObservacao() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setOutrosObservacao(investigacaoAgravo.getOutrosObservacao());
        } else if (fichaCovidDTO.getOutrosObservacao() != null) {
            fichaCovidDTO.getInvestigacaoAgravoCovid19().setOutrosObservacao(fichaCovidDTO.getTriagem().getOutrosSintomas());
        }
    }

    public void carregaCondicoes(List<FormularioTriagemMorbidadeCovid19> listaCondicoes) {
        for (FormularioTriagemMorbidadeCovid19 condicao : listaCondicoes) {
            if (condicao.getMorbidadeCovid19().getCodigo() == 10) {
                fichaCovidDTO.setDoencaRespDescompensada(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 11) {
                fichaCovidDTO.setDoencaCardiacaCronica(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 2) {
                fichaCovidDTO.setDiabetes(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 12) {
                fichaCovidDTO.setDoencasRenaisAvancado(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 13) {
                fichaCovidDTO.setImunossupressao(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 14) {
                fichaCovidDTO.setGestanteAltoRisco(condicao.getMorbidadeCovid19());
            } else if (condicao.getMorbidadeCovid19().getCodigo() == 15) {
                fichaCovidDTO.setPortadorDoencaCromossomica(condicao.getMorbidadeCovid19());
            }
        }
        configuraCondicoes();
    }

    private void configuraCondicoes() {
        if (investigacaoAgravo != null) {
            if (investigacaoAgravo.getDoencaRespDescompensada() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencaRespDescompensada(investigacaoAgravo.getDoencaRespDescompensada());
            } else if (fichaCovidDTO.getDoencaRespDescompensada() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencaRespDescompensada(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getDoencaCardCronica() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencaCardCronica(investigacaoAgravo.getDoencaCardCronica());
            } else if (fichaCovidDTO.getDoencaCardiacaCronica() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencaCardCronica(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getDiabetes() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDiabetes(investigacaoAgravo.getDiabetes());
            } else if (fichaCovidDTO.getDiabetes() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDiabetes(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getDoencasRenaisAvancado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencasRenaisAvancado(investigacaoAgravo.getDoencasRenaisAvancado());
            } else if (fichaCovidDTO.getDoencasRenaisAvancado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setDoencasRenaisAvancado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getImunossupressao() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setImunossupressao(investigacaoAgravo.getImunossupressao());
            } else if (fichaCovidDTO.getImunossupressao() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setImunossupressao(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getGestanteAltoRisco() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setGestanteAltoRisco(investigacaoAgravo.getGestanteAltoRisco());
            } else if (fichaCovidDTO.getGestanteAltoRisco() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setGestanteAltoRisco(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getPortadorDoencaCromossomica() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setPortadorDoencaCromossomica(investigacaoAgravo.getPortadorDoencaCromossomica());
            } else if (fichaCovidDTO.getPortadorDoencaCromossomica() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setPortadorDoencaCromossomica(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
            }

            if (investigacaoAgravo.getCancelado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setCancelado(investigacaoAgravo.getCancelado());
            }
            if (investigacaoAgravo.getIgnorado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setIgnorado(investigacaoAgravo.getIgnorado());
            }

            if (investigacaoAgravo.getObito() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setObito(investigacaoAgravo.getObito());
            }

            if (investigacaoAgravo.getCura() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setCura(investigacaoAgravo.getCura());
            }
            if (investigacaoAgravo.getInternado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setInternado(investigacaoAgravo.getInternado());
            }
            if (investigacaoAgravo.getInternadoUti() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setInternadoUti(investigacaoAgravo.getInternadoUti());
            }
            if (investigacaoAgravo.getTratamentoDomiciliar() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setTratamentoDomiciliar(investigacaoAgravo.getTratamentoDomiciliar());
            }

            if (investigacaoAgravo.getSolicitado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setSolicitado(investigacaoAgravo.getSolicitado());
            }
            if (investigacaoAgravo.getColetado() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setColetado(investigacaoAgravo.getColetado());
            }
            if (investigacaoAgravo.getConcluido() != null) {
                fichaCovidDTO.getInvestigacaoAgravoCovid19().setConcluido(investigacaoAgravo.getConcluido());
            }
        }
    }

    public boolean habilitaDataObito(Long checkBoxObitoIsHabilitado) {
        if (RepositoryComponentDefault.NAO_LONG.equals(checkBoxObitoIsHabilitado) || checkBoxObitoIsHabilitado == null) {
            fichaCovidDTO.setHabilitarDataObito(false);
            investigacaoAgravo.setObito(RepositoryComponentDefault.NAO_LONG);
        } else {
            fichaCovidDTO.setHabilitarDataObito(true);
            investigacaoAgravo.setObito(RepositoryComponentDefault.SIM_LONG);
        }
        return fichaCovidDTO.isHabilitarDataObito();
    }
}
