package br.com.celk.view.agenda.agendamento;

import br.com.celk.agendamento.dto.CancelamentoAgendamentoDTO;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.agendamento.dialog.DlgContatoAgendamentoUsuarioCadsus;
import br.com.celk.view.agenda.agendamento.dto.CancelamentoAgendamentoViewDTO;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.unidadesaude.exames.manutencaocotacbo.ConsultaCotaCboPage;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
@Private
public class CancelamentoAgendamentoPage extends BasePage {

    private AgendaGradeAtendimentoDTOParam param;
    
    private PageableTable pageableTable;
    private QueryPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam> dataProvider;
    private PnlDatePeriod pnlDatePeriod;
    private AutoCompleteConsultaTipoProcedimento autoCompleteTipoProcedimento;
    private DlgDetalhesAgendamento dialogDetalhesAgendamento;
    private DlgMotivoCancelamentoAgendamento dialog;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    private InputField inputCodigoPaciente;

    private DlgContatoAgendamentoUsuarioCadsus dlgContatoAgendamentoUsuarioCadsus;

    private DlgConfirmacaoSimNao<CancelamentoAgendamentoViewDTO> dlgConfirmacaoSimNao;
    
    public CancelamentoAgendamentoPage(PageParameters parameters) {
        super(parameters);
        init();
    }
    
    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(getParam()));
     
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus"){
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }

        });

        form.add(autoCompleteTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(pnlDatePeriod = new PnlDatePeriod("datePeriod"));
        form.add(inputCodigoPaciente = new InputField("codigoPaciente"));
        form.add(populateDropDownTipoData(new DropDown<AgendaGradeAtendimentoDTOParam.TipoData>("tipoData")));

        autoCompleteTipoProcedimento.setTfd(false);
        autoCompleteTipoProcedimento.setIncluirInativos(true);

        form.add(new ProcurarButton<AgendaGradeAtendimentoDTOParam>("btnProcurar", getPageableTable()){

            @Override
            public AgendaGradeAtendimentoDTOParam getParam() {
                return CancelamentoAgendamentoPage.this.getParam();
                
            }
            
        });
        
        form.add(getPageableTable());
        form.add(dialogDetalhesAgendamento = new DlgDetalhesAgendamento("dialogDetalhesAgendamento"));
        form.add(new InputField("numeroSolicitacao"));
        pnlDatePeriod.setModelObject(new DatePeriod(Data.getDataAtual(), null));
        addModal(dialog = new DlgMotivoCancelamentoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException {
                if (dto.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento() != null) {
                    if (dlgConfirmacaoSimNao == null) {
                        addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao<CancelamentoAgendamentoViewDTO>(newModalId(), bundle("msgDesejaReabrirSolicitacaoAgendamentoPaciente")) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                CancelamentoAgendamentoDTO cancelamentoAgendamentoDTO = new CancelamentoAgendamentoDTO();
                                cancelamentoAgendamentoDTO.setCodigoAgendaGradeAtendimentoHorario(getObject().getAgendaGradeAtendimentoHorarioDTO().getAgendaGradeAtendimentoHorario().getCodigo());
                                cancelamentoAgendamentoDTO.setMotivoCancelamento(getObject().getMotivo());
                                cancelamentoAgendamentoDTO.setValidarUsuarioAgendamento(false);
                                cancelamentoAgendamentoDTO.setValidarAgendamentosDia(false);
                                cancelamentoAgendamentoDTO.setReabrirSolicitacaoAgendamento(true);
                                cancelamentoAgendamentoDTO.setCancelarSolicitacaoAgendamento(false);
                                BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarAgendamento(cancelamentoAgendamentoDTO);
                                CancelamentoAgendamentoPage.this.getPageableTable().update(target);
                            }

                            @Override
                            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                CancelamentoAgendamentoDTO cancelamentoAgendamentoDTO = new CancelamentoAgendamentoDTO();
                                cancelamentoAgendamentoDTO.setCodigoAgendaGradeAtendimentoHorario(getObject().getAgendaGradeAtendimentoHorarioDTO().getAgendaGradeAtendimentoHorario().getCodigo());
                                cancelamentoAgendamentoDTO.setMotivoCancelamento(getObject().getMotivo());
                                cancelamentoAgendamentoDTO.setValidarUsuarioAgendamento(false);
                                cancelamentoAgendamentoDTO.setValidarAgendamentosDia(false);
                                cancelamentoAgendamentoDTO.setReabrirSolicitacaoAgendamento(false);
                                cancelamentoAgendamentoDTO.setCancelarSolicitacaoAgendamento(true);
                                BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarAgendamento(cancelamentoAgendamentoDTO);
                                CancelamentoAgendamentoPage.this.getPageableTable().update(target);
                            }

                            @Override
                            public void configuraButtonCancelar(AbstractAjaxButton btnCancelar) {
                                btnCancelar.setVisible(true);
                                btnCancelar.add(new AttributeModifier("class", "btn-orange"));
                            }

                            @Override
                            public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                                super.configurarButtons(btnConfirmar, btnFechar);
                                btnFechar.add(new AttributeModifier("class", "btn-green"));
                            }

                            @Override
                            public FormComponent getComponentRequestFocus() {
                                return getPnlConfirmacao().getBtnConfirmar();
                            }
                        });
                    }
                    dlgConfirmacaoSimNao.show(target);
                    dlgConfirmacaoSimNao.setObject(new CancelamentoAgendamentoViewDTO(dto, motivo));
                } else {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarAgendamento(dto.getAgendaGradeAtendimentoHorario().getCodigo(), motivo, false, false);
                    getPageableTable().update(target);
                }
            }
        });
        
         Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();

        if (!isActionPermitted(usuario, Permissions.EMPRESA)) {
            autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        }

        add(form);
    }

    private DropDown<AgendaGradeAtendimentoDTOParam.TipoData> populateDropDownTipoData(DropDown<AgendaGradeAtendimentoDTOParam.TipoData> dropDown){

        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO, BundleManager.getString("agendamento"));
        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_CADASTRO, BundleManager.getString("cadastro"));
        
        return dropDown;
    }

    public PageableTable getPageableTable() {
        if (this.pageableTable == null) {
            this.pageableTable = new PageableTable("table", getColumns(), getDataProvider());
            
            this.pageableTable.setScrollX("100%");
        }
        
        return this.pageableTable;
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(AgendaGradeAtendimentoHorarioDTO.class);
        
        columns.add(getActionColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("tipo_procedimento"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO,TipoProcedimento.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade_executante"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("data"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_DATA_ATENDIMENTO_FORMATADO_DIA_HORA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SITUACAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("profissional"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_PROFISSIONAL,Profissional.PROP_DESCRICAO_FORMATADA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade_solicitante"), VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_EMPRESA_ORIGEM,Empresa.PROP_DESCRICAO)));

        return columns;
    }
    
    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorarioDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoHorarioDTO rowObject) {

                addAction(ActionType.REMOVER, rowObject,
                        new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                                dialog.setDto(modelObject, target);
                                dialog.show(target);
                            }
                        }
                ).setQuestionDialogBundleKey(null)
                        .setTitleBundleKey("cancelar");

                addAction(ActionType.CONTATO, rowObject,
                        new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                                viewDlgContato(target, modelObject);
                            }
                        }
                ).setTitleBundleKey("registrarContato");
            }
        };
    }

    private void viewDlgContato(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO agendaGradeAtendimentoHorarioDTO) {
        if (dlgContatoAgendamentoUsuarioCadsus == null) {
            addModal(target, dlgContatoAgendamentoUsuarioCadsus = new DlgContatoAgendamentoUsuarioCadsus(newModalId()));
        }

        dlgContatoAgendamentoUsuarioCadsus.show(target, agendaGradeAtendimentoHorarioDTO);
    }
    
    private IPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam> getDataProvider(){
        if (this.dataProvider == null) {
            this.dataProvider = new QueryPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<AgendaGradeAtendimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentosPager(dataPaging);
                }
            };
        }
        
        return this.dataProvider;    
    }
    
    private AgendaGradeAtendimentoDTOParam getParam(){
        if (this.param == null) {
            this.param = new AgendaGradeAtendimentoDTOParam();
        }
        
        param.setExibirCancelados(true);
        param.setSituacao(AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        param.getConfigureParam().addSorter(VOUtils.montarPath("agendaGradeAtendimentoHorario.dataAgendamento"));
        param.setFlagTfd(RepositoryComponentDefault.NAO);
        
        Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();

        if (!isActionPermitted(usuario, Permissions.EMPRESA)) {
            try {
                usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                param.setEmpresas(usuario.getEmpresasUsuario());
            } catch (SGKException ex) {
                Logger.getLogger(ConsultaCotaCboPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return this.param;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cancelamentoAgendamento");
    }

}