package br.com.celk.view.atendimento.programas;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaProfissionalDTOParam;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.avaliacao.BolsaFamilia;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanAlimentacao;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaManutencaoBolsaFamiliaPage extends ConsultaPage<BolsaFamilia, List<BuilderQueryCustom.QueryParameter>> {

    private QueryConsultaProfissionalDTOParam param;
    private String nome;
    private DropDown dropDownSituacao;
    private Long situacao;
    private DatePeriod periodo;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("nome"));
        form.add(dropDownSituacao = getDropDownSituacao("situacao"));
        form.add(new PnlDatePeriod("periodo"));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        BolsaFamilia proxy = on(BolsaFamilia.class);
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("nome"), proxy.getUsuarioCadsus().getNome(), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("idade"), proxy.getUsuarioCadsus().getDataNascimento(), proxy.getUsuarioCadsus().getIdade()));
        columns.add(createSortableColumn(bundle("dataAtendimento"), proxy.getDataAtendimento()));
        columns.add(new DoubleColumn(bundle("peso"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createSortableColumn(bundle("gestante"), proxy.getGestante(), proxy.getGestanteFormatado()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getSituacaoFormatado()));

        return columns;
    }

    private CustomColumn<BolsaFamilia> getCustomColumn() {
        return new CustomColumn<BolsaFamilia>() {

            @Override
            public Component getComponent(String componentId, final BolsaFamilia rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroManutencaoBolsaFamiliaPage(rowObject, false, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroManutencaoBolsaFamiliaPage(rowObject, true));
                    }
                };
            }

        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return BolsaFamilia.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(BolsaFamilia.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO),
                                VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO),
                                VOUtils.montarPath(BolsaFamilia.PROP_SISVAN_ALIMENTACAO, SisvanAlimentacao.PROP_CODIGO),
                                VOUtils.montarPath(BolsaFamilia.PROP_SISVAN_ALIMENTACAO, SisvanAlimentacao.PROP_DESCRICAO_ALIMENTACAO),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(BolsaFamilia.PROP_DATA_ATENDIMENTO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {

        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (nome != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BolsaFamilia.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, nome))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BolsaFamilia.PROP_DATA_ATENDIMENTO), periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BolsaFamilia.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.IGUAL, situacao));

        return parameters;
    }

    private DropDown getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown(id);

            dropDownSituacao.addChoice("", BundleManager.getString("todos"));
            dropDownSituacao.addChoice(BolsaFamilia.STATUS_ATUALIZADO, BundleManager.getString("atualizado"));
            dropDownSituacao.addChoice(BolsaFamilia.STATUS_PENDENTE, BundleManager.getString("pendente"));
        }
        return dropDownSituacao;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroManutencaoBolsaFamiliaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaManutencaoBolsaFamilia");
    }

}
