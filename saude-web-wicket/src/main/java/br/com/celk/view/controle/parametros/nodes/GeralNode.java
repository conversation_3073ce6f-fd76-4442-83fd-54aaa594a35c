package br.com.celk.view.controle.parametros.nodes;

import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.controle.parametros.NodesParametrosRef;
import br.com.celk.view.controle.parametros.nodes.annotations.ParametrosNode;
import br.com.celk.view.controle.parametros.nodes.base.ParametrosNodeImp;
import br.com.celk.view.controle.parametros.panel.geral.GeralPanel;
import br.com.celk.view.controle.parametros.panel.template.ParametrosCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ParametrosNode(NodesParametrosRef.GERAL)
public class GeralNode extends ParametrosNodeImp {

    private GeralPanel geralPanel;

    @Override
    public ParametrosCadastroPanel getPanel(String id) {
        if (geralPanel == null) {
            geralPanel = new GeralPanel(id);
        }
        return geralPanel;
    }
    
    public GeralPanel getGeralPanel(){
        return geralPanel;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("geral");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.REPORT;
    }

}
