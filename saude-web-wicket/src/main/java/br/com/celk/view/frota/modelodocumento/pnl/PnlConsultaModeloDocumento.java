package br.com.celk.view.frota.modelodocumento.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.frota.modelodocumento.CustomizeConsultaModeloDocumento;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.frota.ModeloDocumento;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaModeloDocumento extends PnlConsulta<ModeloDocumento> {

    public PnlConsultaModeloDocumento(String id, IModel<ModeloDocumento> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaModeloDocumento(String id, IModel<ModeloDocumento> model) {
        super(id, model);
    }

    public PnlConsultaModeloDocumento(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaModeloDocumento(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaModeloDocumento();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("modeloDocumento");
    }

}
