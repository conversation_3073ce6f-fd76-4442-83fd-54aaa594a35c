package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.grupoproduto.pnl.PnlConsultaGrupoProduto;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioGraficoDemonstrativoDispensacaoDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private

public class GraficoDemonstrativoDispensacaoPage extends RelatorioPage<RelatorioGraficoDemonstrativoDispensacaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownTipoGrafico;
    private DropDown<String> dropDownTipoDado;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownTipoPreco;
    private InputField<Long> txtQuantidade;
    private PnlConsultaGrupoProduto pnlConsultaGrupoProduto;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidadeOrigem;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private WebMarkupContainer componenteFormaApresentacao;
    private WebMarkupContainer containerVazioEmpresa;
    private Label lblFormaApresentacao;
    private PnlGrupoSubGrupo pnlGrupoSubGrupo;

    private String periodoInicial;
    private String periodoFinal;
    private GrupoProduto _grupoProduto;
    private GrupoProduto grupoProdutoSubGrupo;
    private SubGrupo subGrupo;
    private Empresa unidadeOrigem;
    private Produto produto;
    private Profissional profissional;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresas")
                .setMultiplaSelecao(true));
        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
        form.add(txtQuantidade = new InputField<Long>("quantidade"));
        txtQuantidade.setComponentValue(10L);
        form.add(new RequiredInputField("periodoInicial", new PropertyModel(this, "periodoInicial")));
        form.add(new RequiredInputField("periodoFinal", new PropertyModel(this, "periodoFinal")));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownTipoGrafico());
        form.add(getDropDownTipoDado());
        form.add(getDropDownTipoPreco());
        form.add(lblFormaApresentacao = new Label("lblFormaApresentacao", BundleManager.getString("grupoProduto")));
        lblFormaApresentacao.setOutputMarkupId(true);

        containerVazioEmpresa = new WebMarkupContainer("componenteFormaApresentacao");
        pnlConsultaGrupoProduto = new PnlConsultaGrupoProduto("componenteFormaApresentacao", new PropertyModel<GrupoProduto>(this, "_grupoProduto"));
        autoCompleteConsultaUnidadeOrigem = new AutoCompleteConsultaEmpresa("componenteFormaApresentacao", new PropertyModel<Empresa>(this, "unidadeOrigem"));
        autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("componenteFormaApresentacao", new PropertyModel<Produto>(this, "produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("componenteFormaApresentacao", new PropertyModel<Profissional>(this, "profissional"));
        pnlGrupoSubGrupo = new PnlGrupoSubGrupo("componenteFormaApresentacao", this);
        form.add(componenteFormaApresentacao = pnlConsultaGrupoProduto);

    }

    public DropDown<String> getDropDownTipoPreco() {
        if (dropDownTipoPreco == null) {
            dropDownTipoPreco = new DropDown<String>("tipoPreco");
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_PRECO_MEDIO, BundleManager.getString("precoMedio"));
            dropDownTipoPreco.addChoice(EstoqueEmpresa.PROP_ULTIMO_PRECO, BundleManager.getString("ultimoPreco"));
        }
        return dropDownTipoPreco;
    }

    public DropDown<String> getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            dropDownFormaApresentacao.addChoice(VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), BundleManager.getString("grupoProduto"));
            dropDownFormaApresentacao.addChoice(Produto.PROP_SUB_GRUPO, BundleManager.getString("subGrupo"));
            dropDownFormaApresentacao.addChoice(DispensacaoMedicamento.PROP_EMPRESA_ORIGEM, BundleManager.getString("empresaOrigem"));
            dropDownFormaApresentacao.addChoice(DispensacaoMedicamentoItem.PROP_PRODUTO, BundleManager.getString("produto"));
            dropDownFormaApresentacao.addChoice(DispensacaoMedicamento.PROP_PROFISSIONAL, BundleManager.getString("profissional"));
            dropDownFormaApresentacao.addChoice(DispensacaoMedicamento.PROP_EMPRESA, BundleManager.getString("unidadeDispensadora"));

            dropDownFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    eventoFormaApresentacao(target);
                }

            });
        }
        return dropDownFormaApresentacao;
    }

    private void eventoFormaApresentacao(AjaxRequestTarget target) {
        String fa = dropDownFormaApresentacao.getComponentValue();

        if (fa != null) {
            if (VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO).equals(fa)) {
                if (componenteFormaApresentacao != pnlConsultaGrupoProduto) {
                    componenteFormaApresentacao.replaceWith(pnlConsultaGrupoProduto);
                    componenteFormaApresentacao = pnlConsultaGrupoProduto;
                    pnlConsultaGrupoProduto.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("grupoProduto"));
                    target.add(lblFormaApresentacao);
                }
            } else if (Produto.PROP_SUB_GRUPO.equals(fa)) {
                if (componenteFormaApresentacao != pnlGrupoSubGrupo) {
                    componenteFormaApresentacao.replaceWith(pnlGrupoSubGrupo);
                    componenteFormaApresentacao = pnlGrupoSubGrupo;
                    pnlGrupoSubGrupo.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("grupoProduto"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_EMPRESA_ORIGEM.equals(fa)) {
                if (componenteFormaApresentacao != autoCompleteConsultaUnidadeOrigem) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaUnidadeOrigem);
                    componenteFormaApresentacao = autoCompleteConsultaUnidadeOrigem;
                    autoCompleteConsultaUnidadeOrigem.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("unidadeOrigem"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamentoItem.PROP_PRODUTO.equals(fa)) {
                if (componenteFormaApresentacao != autoCompleteConsultaProduto) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaProduto);
                    componenteFormaApresentacao = autoCompleteConsultaProduto;
                    autoCompleteConsultaProduto.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("produto"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_PROFISSIONAL.equals(fa)) {
                if (componenteFormaApresentacao != autoCompleteConsultaProfissional) {
                    componenteFormaApresentacao.replaceWith(autoCompleteConsultaProfissional);
                    componenteFormaApresentacao = autoCompleteConsultaProfissional;
                    autoCompleteConsultaProfissional.limpar(target);
                    lblFormaApresentacao.setDefaultModelObject(BundleManager.getString("profissional"));
                    target.add(lblFormaApresentacao);
                }
            } else if (DispensacaoMedicamento.PROP_EMPRESA.equals(fa)) {
                if (componenteFormaApresentacao != containerVazioEmpresa) {
                    componenteFormaApresentacao.replaceWith(containerVazioEmpresa);
                    componenteFormaApresentacao = containerVazioEmpresa;
                    lblFormaApresentacao.setDefaultModelObject("");
                    target.add(componenteFormaApresentacao);
                    target.add(lblFormaApresentacao);
                }
            }
        }

    }

    public DropDown<String> getDropDownTipoDado() {
        if (dropDownTipoDado == null) {
            dropDownTipoDado = new DropDown<String>("tipoDado");
            dropDownTipoDado.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_VALOR, BundleManager.getString("valor"));
            dropDownTipoDado.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_PERCENTUAL, BundleManager.getString("percentual"));
            dropDownTipoDado.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_QUANTIDADE, BundleManager.getString("quantidade"));

        }
        return dropDownTipoDado;
    }

    public DropDown<String> getDropDownTipoGrafico() {
        if (dropDownTipoGrafico == null) {
            dropDownTipoGrafico = new DropDown<String>("tipoGrafico");
            dropDownTipoGrafico.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_LINHAS, BundleManager.getString("linhas"));
            dropDownTipoGrafico.addChoice(RelatorioGraficoDemonstrativoDispensacaoDTOParam.TIPO_BARRAS, BundleManager.getString("barras"));

        }
        return dropDownTipoGrafico;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioGraficoDemonstrativoDispensacaoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioGraficoDemonstrativoDispensacaoDTOParam param) throws ReportException {
        param.setDataInicial(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(periodoInicial)));
        param.setDataFinal(Data.getDataParaUltimoDiaMes(Data.parserMounthYear(periodoFinal)));

        String fa = param.getFormaApresentacao();

        param.setFiltroFormaApresentacao(null);

        if (VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO).equals(fa) && _grupoProduto != null) {
            param.setFiltroFormaApresentacao(Arrays.asList(_grupoProduto));
        } else if (Produto.PROP_SUB_GRUPO.equals(fa)) {
            param.setGrupoProduto(grupoProdutoSubGrupo);
            if (subGrupo != null) {
                param.setFiltroFormaApresentacao(Arrays.asList(subGrupo)); //??
            }
        } else if (DispensacaoMedicamento.PROP_EMPRESA_ORIGEM.equals(fa) && unidadeOrigem != null) {
            param.setFiltroFormaApresentacao(Arrays.asList(unidadeOrigem));
        } else if (DispensacaoMedicamentoItem.PROP_PRODUTO.equals(fa) && produto != null) {
            param.setFiltroFormaApresentacao(Arrays.asList(produto));
        } else if (DispensacaoMedicamento.PROP_PROFISSIONAL.equals(fa) && profissional != null) {
            param.setFiltroFormaApresentacao(Arrays.asList(profissional));
        } else if (DispensacaoMedicamento.PROP_EMPRESA.equals(fa)) {
            param.setFiltroFormaApresentacao(param.getEmpresas()); //??
        }

        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioGraficoDemonstrativoDispensacaoAsync(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("graficoDemonstrativoDispensacao");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

}
