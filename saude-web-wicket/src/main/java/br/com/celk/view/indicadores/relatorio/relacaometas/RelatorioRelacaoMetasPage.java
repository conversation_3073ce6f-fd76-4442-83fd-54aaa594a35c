package br.com.celk.view.indicadores.relatorio.relacaometas;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.indicadores.cadastro.tipoindicador.autocomplete.AutoCompleteConsultaTipoIndicador;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.indicadores.dto.RelatorioRelacaoMetasDTOParam;
import br.com.ksisolucoes.report.indicadores.interfaces.facade.IndicadoresReportFacade;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoMetasPage extends RelatorioPage<RelatorioRelacaoMetasDTOParam> {

    @Override
    public void init(Form form) {
        RelatorioRelacaoMetasDTOParam proxy = on(RelatorioRelacaoMetasDTOParam.class);

        form.add(new AutoCompleteConsultaTipoIndicador(path(proxy.getTipoIndicador()), true));
        form.add(new RequiredDateChooser(path(proxy.getDataExercicio())));
        form.add(new PnlChoicePeriod(path(proxy.getPeriodo())));

    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoMetasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoMetasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(IndicadoresReportFacade.class).relatorioRelacaoMetas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoMetas");
    }

}
