package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.util.ConsorcioHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.Arrays;

import static br.com.celk.system.methods.WicketMethods.bundle;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroGuiaProcedimentoStep1Page extends BasePage{

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa consorciado;
    
    public CadastroGuiaProcedimentoStep1Page() {
        init();
    }
    
    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("consorciado",true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                if (empresa != null){
                    validaConsorciadoBloqueado(target,empresa);
                }
            }
        });

//        validaConsorciadoBloqueado(null, consorciado);


        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validaConsorciadoBloqueado(target,consorciado)){
                    avancar();
                }
            }
        });

        add(form);
    }

    private boolean validaConsorciadoBloqueado(AjaxRequestTarget target, Empresa empresa) {

        if (empresa != null){
            if (Empresa.SituacaoBloqueio.BLOQUEADO_PERIODO.value().equals(empresa.getSituacaoBloqueio()) && DataUtil.getDataAtualSemHora().compareTo(empresa.getDataBloqueioInicial()) >= 0) {
                autoCompleteConsultaEmpresa.limpar(target);
                target.add(autoCompleteConsultaEmpresa);
                MessageUtil.warn(target, autoCompleteConsultaEmpresa, BundleManager.getString("msgConsorciadoXXXEstaBloqueadoDurantePeriaodoXaY", empresa.getDescricao(), DataUtil.getFormatarDiaMesAno(empresa.getDataBloqueioInicial()), DataUtil.getFormatarDiaMesAno(empresa.getDataBloqueioFinal())));
                return false;
            } else if (Empresa.SituacaoBloqueio.BLOQUEADO_INDETERMINADO.value().equals(empresa.getSituacaoBloqueio()) && DataUtil.getDataAtualSemHora().compareTo(empresa.getDataBloqueioInicial()) >= 0) {
                autoCompleteConsultaEmpresa.limpar(target);
                target.add(autoCompleteConsultaEmpresa);
                MessageUtil.warn(target, autoCompleteConsultaEmpresa, BundleManager.getString("msgConsorciadoXXXEstaBloqueadoPeriodoIndeterminado", empresa.getDescricao()));
                return false;
            }
        }else {
            return false;
        }
        return true;
    }

    private void avancar() throws ValidacaoException {
        if(ConsorcioHelper.isConsorciadoAtivo(SessaoAplicacaoImp.getInstance().getEmpresa()) && ConsorcioHelper.isConsorciadoAtivo(consorciado)) {
            setResponsePage(new CadastroGuiaProcedimentoStep1ConsorciadoPage(consorciado));
        } else {
            throw new ValidacaoException(bundle("msgConsorciadoSituacaoInativaContateConsorcio"));
        }
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroGuiaProcedimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
    
}
