package br.com.celk.view.unidadesaude.processos.regulacoesdevolvidas;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.base.BaseEmpresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import ch.lambdaj.Lambda;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus.*;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento.PROP_CODIGO;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento.PROP_PROFISSIONAL;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento.*;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoProcedimento.PROP_FLAG_TFD;
import static ch.lambdaj.Lambda.on;

public class BuildParametrosFiltroRegulacoesDevolvidas {


    private final List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

    public BuildParametrosFiltroRegulacoesDevolvidas addTipoProcedimento(TipoProcedimento tipoProcedimento) {
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_TIPO_PROCEDIMENTO, PROP_FLAG_TFD), RepositoryComponentDefault.NAO));
        if (tipoProcedimento != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(PROP_TIPO_PROCEDIMENTO, BuilderQueryCustom.QueryParameter.IN, Collections.singletonList(tipoProcedimento)));
        }
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addNomePaciente(String nomePaciente) {
        if (nomePaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_USUARIO_CADSUS, PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nomePaciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_USUARIO_CADSUS, PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_USUARIO_CADSUS, PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, nomePaciente))))));
        }
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addCodigoPaciente(Long codigoPaciente) {
        if (codigoPaciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_USUARIO_CADSUS, BaseUsuarioCadsus.PROP_CODIGO), codigoPaciente));
        }
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addPeriodo(DatePeriod periodo) {
        if (periodo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(PROP_DATA_AUTORIZADOR, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, periodo.getDataInicial()));
            parameters.add(new QueryCustom.QueryCustomParameter(PROP_DATA_AUTORIZADOR, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, periodo.getDataFinal()));
        }
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addEmpresas(OperadorValor<List<Empresa>> empresas) {
        if (empresas != null) {
            List<Long> extractCodigoEmpresa = Lambda.extract(empresas.getValue(), on(Empresa.class).getCodigo());
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PROP_EMPRESA, BaseEmpresa.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IN, extractCodigoEmpresa));
        }
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addProfissional(Profissional profissional) {
        parameters.add(new QueryCustom.QueryCustomParameter(PROP_PROFISSIONAL, profissional));
        return this;
    }

    public BuildParametrosFiltroRegulacoesDevolvidas addNumeroSolicitacao(Long numeroSolicitacao) {
        if (Coalesce.asLong(numeroSolicitacao) > 0L) {
            parameters.add(new QueryCustom.QueryCustomParameter(PROP_CODIGO, numeroSolicitacao));
        }
        return this;
    }


    public BuildParametrosFiltroRegulacoesDevolvidas addStatusSolicitacao(List<Long> statusSolicitacao) {
        parameters.add(new QueryCustom.QueryCustomParameter(PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, statusSolicitacao));
        return this;
    }

    public List<BuilderQueryCustom.QueryParameter> build() {
        return parameters;
    }
}
