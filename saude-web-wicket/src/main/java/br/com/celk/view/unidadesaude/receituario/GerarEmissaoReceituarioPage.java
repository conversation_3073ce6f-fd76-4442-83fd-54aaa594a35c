package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.ImprimirActionColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GerarEmissaoReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirMultiplasReceitasDTOParam;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GerarEmissaoReceituarioPage extends BasePage {

    private final GerarEmissaoReceituarioDTO dto;

    private SelectionTable<Receituario> tblReceituarios;
    private Table<ReceituarioItem> tblReceituariosItens;
    private List<ReceituarioItem> receituarioItens;
    private Class retorno;
    private final Profissional profissional;

    public GerarEmissaoReceituarioPage(GerarEmissaoReceituarioDTO dto, Profissional profissional) throws ValidacaoException {
        this.dto = dto;
        this.profissional = profissional;
        init();
    }

    public void init() throws ValidacaoException {

        Form form = new Form("form", null);

        form.add(new DisabledInputField("paciente", new PropertyModel(this, "dto.atendimento.nomePaciente")));
        form.add(new DisabledInputField("idade", new PropertyModel(this, "dto.atendimento.usuarioCadsus.idade")));
        form.add(tblReceituarios = new SelectionTable<Receituario>("tblReceituario", getColumnsReceituario(), getCollectionProvider()));
        tblReceituarios.populate();
        tblReceituarios.addSelectionAction(new ISelectionAction<Receituario>() {
            public void onSelection(AjaxRequestTarget target, Receituario object) {
                procurarReceituarioItem(target, object);
            }
        });

        form.add(tblReceituariosItens = new Table<ReceituarioItem>("tblReceituarioItem", getColumnsReceituarioItem(), getCollectionProviderReceituarioItens()));
        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new EmissaoMedicamentoPacientePage(dto.getAtendimento().getUsuarioCadsus(), profissional));
            }

        });
        form.add(new AbstractAjaxButton("btnFinalizar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                finalizar(target);
            }
        });
        add(form);
        try {
            tblReceituarios.setSelectedObject(dto.getReceituarios().get(0));
        } catch (IndexOutOfBoundsException e) {
            throw new ValidacaoException(BundleManager.getString("medicamentoControladoReceitaInvalida"));
        }
        procurarReceituarioItem(null, dto.getReceituarios().get(0));
        tblReceituariosItens.populate();
    }

    public List<ISortableColumn<Receituario>> getColumnsReceituario() {
        List<ISortableColumn<Receituario>> columns = new ArrayList<ISortableColumn<Receituario>>();
        ColumnFactory columnFactory = new ColumnFactory(Receituario.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("tipoReceita"), VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("profissional"), VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_NOME)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cid"), VOUtils.montarPath(Receituario.PROP_CID, Cid.PROP_CODIGO)));

        return columns;
    }

    private CustomColumn<Receituario> getCustomColumn() {
        return new CustomColumn<Receituario>() {
            @Override
            public Component getComponent(String componentId, final Receituario rowObject) {
                return new ImprimirActionColumnPanel(componentId) {

                    @Override
                    public DataReport onImprimir() throws ReportException {
                        return getImpressaoReceituarioDTOParam(rowObject);
                    }

                };
            }
        };

    }

    private DataReport getImpressaoReceituarioDTOParam(Receituario rowObject) throws ReportException {
        if (TipoReceita.RECEITA_BRANCA.equals(rowObject.getTipoReceita().getTipoReceita())) {
            RelatorioImprimirMultiplasReceitasDTOParam param1 = new RelatorioImprimirMultiplasReceitasDTOParam();
            param1.setLstReceituario(dto.getReceituarios());
            param1.setAtendimento(rowObject.getAtendimento());
            try {
                param1.setExibeDadosPacienteIdentificacaoCompradorReceitaB(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeDadosPacienteIdentificacaoCompradorReceitaB")));
                param1.setExibedadospacientedidentificadomenor18anos(true);
            } catch (DAOException e) {
                throw new ReportException("Erro ao carregar dados do parametro GEM exibeDadosPacienteIdentificacaoCompradorReceitaB");
            }
            return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitasLandscape(param1);
        } else {
            ImpressaoReceituarioDTOParam param = new ImpressaoReceituarioDTOParam();
            verificaTipoReceita(param);
            param.setCodigoReceituario(rowObject.getCodigo());
            param.setPapelTimbrado(false);
            param.setAtendimento(receituarioItens.get(0).getAtendimentoPrincipal());
            param.setTipoReceita(rowObject.getTipoReceita().getTipoReceita());
            return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param);
        }


    }

    private void verificaTipoReceita(ImpressaoReceituarioDTOParam param) {
        boolean antimicrobiano = false;
        for (ReceituarioItem receituarioItem : receituarioItens) {
            if (receituarioItem.getProduto() != null
                    && receituarioItem.getProduto().getSubGrupo() != null
                    && receituarioItem.getProduto().getSubGrupo().getDescricao() != null
                    && receituarioItem.getProduto().getSubGrupo().getDescricao().equalsIgnoreCase("ANTIMICROBIANOS")) {
                antimicrobiano = true;
            }
        }
        param.setAntiMicrobiano(antimicrobiano);
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dto.getReceituarios();
            }
        };
    }

    public List<ISortableColumn<ReceituarioItem>> getColumnsReceituarioItem() {
        List<ISortableColumn<ReceituarioItem>> columns = new ArrayList<ISortableColumn<ReceituarioItem>>();
        ColumnFactory columnFactory = new ColumnFactory(ReceituarioItem.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("medicamento"), VOUtils.montarPath(ReceituarioItem.PROP_NOME_PRODUTO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("unidadeAbv"), VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidadePrescritaAbv"), VOUtils.montarPath(ReceituarioItem.PROP_QUANTIDADE_PRESCRITA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("posologia"), VOUtils.montarPath(ReceituarioItem.PROP_POSOLOGIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("via"), VOUtils.montarPath(ReceituarioItem.PROP_TIPO_VIA_MEDICAMENTO, TipoViaMedicamento.PROP_DESCRICAO)));

        return columns;
    }

    public ICollectionProvider getCollectionProviderReceituarioItens() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return receituarioItens;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("emissaoReceituario");
    }

    private void procurarReceituarioItem(AjaxRequestTarget target, Receituario object) {
        receituarioItens = object.getReceituarioItemList();

        if (target != null) {
            tblReceituariosItens.populate(target);
        }
    }

    private void finalizar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).finalizarEmissaoReceituario(this.dto.getAtendimento().getCodigo(), this.dto.getTabelaCbo().getCbo(), profissional);
        try {
            Page page = (Page) getResponsePage().newInstance();
            setResponsePage(page);
            getSession().getFeedbackMessages().info(page, BundleManager.getString("atendimentoFinalizadoComSucesso"));
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public Class getResponsePage() {
        if (retorno == null) {
            return ConsultaEmissaoReceituarioPage.class;
        }
        return retorno;
    }

    public void setRetorno(Class retorno) {
        this.retorno = retorno;
    }

}
