package br.com.celk.view.vigilancia.dengue.atividade;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAtividade;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class CadastroDengueAtividadePage extends CadastroPage<DengueAtividade> {

    private InputField txtDescricao;

    public CadastroDengueAtividadePage(DengueAtividade object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroDengueAtividadePage(DengueAtividade object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroDengueAtividadePage(DengueAtividade object) {
        this(object, false);
    }

    public CadastroDengueAtividadePage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        DengueAtividade proxy = on(DengueAtividade.class);
        form.add(txtDescricao = new RequiredInputField<String>(path(proxy.getDescricao())));
        txtDescricao.setLabel(new Model(bundle("atividade")));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagInformaPontoEstrategico()), false, true));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagLevantamentoIndice()), false, true));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<DengueAtividade> getReferenceClass() {
        return DengueAtividade.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaDengueAtividadePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroDengueAtividade");
    }

}
