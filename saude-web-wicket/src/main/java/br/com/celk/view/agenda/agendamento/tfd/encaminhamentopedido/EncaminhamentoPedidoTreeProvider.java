package br.com.celk.view.agenda.agendamento.tfd.encaminhamentopedido;

import br.com.celk.component.treetable.pageable.PageableTreeProvider;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.tfd.EloPedidoTfdLoteEncaminhamentoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LoteEncaminhamentoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class EncaminhamentoPedidoTreeProvider extends PageableTreeProvider<EncaminhamentoPedidoDTO, String> {

    private EncaminhamentoPedidoDTOparam param;

    public EncaminhamentoPedidoTreeProvider() {
        carregar();
    }

    public EncaminhamentoPedidoTreeProvider(EncaminhamentoPedidoDTOparam param) {
        this.param = param;
        carregar();
    }

    private void carregar() {
        EloPedidoTfdLoteEncaminhamentoTfd proxy = on(EloPedidoTfdLoteEncaminhamentoTfd.class);

        LoadManager loadPedido = LoadManager.getInstance(EloPedidoTfdLoteEncaminhamentoTfd.class)
                .addProperties(new HQLProperties(EloPedidoTfdLoteEncaminhamentoTfd.class).getProperties())
                .addProperties(new HQLProperties(LoteEncaminhamentoTfd.class, path(proxy.getLoteEncaminhamentoTfd())).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getLoteEncaminhamentoTfd().getDataEncaminhamento()), "desc"))
                .addProperties(new HQLProperties(PedidoTfd.class, path(proxy.getPedidoTfd())).getProperties())
                .addProperties(new HQLProperties(LaudoTfd.class, path(proxy.getPedidoTfd().getLaudoTfd())).getProperties())
                .addProperty(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getNome()))
                .addProperty(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getApelido()))
                .addProperty(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getUtilizaNomeSocial()))
                .addProperty(path(proxy.getPedidoTfd().getLaudoTfd().getProfissional().getNome()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLoteEncaminhamentoTfd()), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, EloPedidoTfdLoteEncaminhamentoTfd.Status.CANCELADO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getPedidoTfd().getDataCadastro()), "desc"));

        if (param != null) {
            loadPedido.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLoteEncaminhamentoTfd().getCodigo()), param.getLote()));
            loadPedido.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLoteEncaminhamentoTfd().getDataEncaminhamento()), param.getPeriodoEncaminhamento()));
            loadPedido.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getNumeroPedido()), param.getNumeroPedido()));
            loadPedido.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getLaudoTfd().getTipoProcedimento()), param.getTipoProcedimento()));
            if (param.getNomePaciente() != null) {
                loadPedido.addParameter(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getNome()), BuilderQueryCustom.QueryParameter.ILIKE, param.getNomePaciente()))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getUtilizaNomeSocial()), RepositoryComponentDefault.SIM_LONG))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getLaudoTfd().getUsuarioCadsus().getApelido()), BuilderQueryCustom.QueryParameter.ILIKE, param.getNomePaciente()))))));
            }
            loadPedido.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTfd().getLaudoTfd().getStatus()), param.getStatus()));
        }

        List<EloPedidoTfdLoteEncaminhamentoTfd> eloList = loadPedido.start().getList();

        for (EloPedidoTfdLoteEncaminhamentoTfd elo : eloList) {
            String id = elo.getLoteEncaminhamentoTfd().getCodigo().toString();
            EncaminhamentoPedidoDTO dtoPai = new EncaminhamentoPedidoDTO();
            if (!getMapPaisAdicionados().containsKey(id)) {
                dtoPai.setDescricao(id);
                dtoPai.setEloPedidoTfdLoteEncaminhamentoTfd(elo);
                dtoPai.setHeader(false);

                getMapPaisAdicionados().put(elo.getLoteEncaminhamentoTfd().getCodigo().toString(), dtoPai);
                getRootList().add(dtoPai);
            } else {
                dtoPai = getMapPaisAdicionados().get(id);
            }

            /**
             * se não tem filhos, coloca um Header
             */
            if (dtoPai.getFilhos() == null || dtoPai.getFilhos().isEmpty()) {
                EncaminhamentoPedidoDTO dtoHeader = new EncaminhamentoPedidoDTO();
                dtoHeader.setHeader(true);
                dtoHeader.getHeaders().add(BundleManager.getString("numeroPedido"));
                dtoHeader.getHeaders().add(BundleManager.getString("paciente"));
                dtoHeader.getHeaders().add(BundleManager.getString("tipoProcedimento"));
                dtoHeader.getHeaders().add(BundleManager.getString("profissional"));
                dtoHeader.getHeaders().add(BundleManager.getString("dataCadastro"));
                dtoHeader.getHeaders().add(BundleManager.getString("situacao"));
                dtoPai.addFilho(dtoHeader);
            }

            EncaminhamentoPedidoDTO dtoFilho = new EncaminhamentoPedidoDTO();
            dtoFilho.setDescricao(id);
            dtoFilho.setPedidoTfd(elo.getPedidoTfd());
            dtoFilho.setEloPedidoTfdLoteEncaminhamentoTfd(elo);
            dtoFilho.setHeader(false);

            dtoFilho.setEloPedidoTfdLoteEncaminhamentoTfd(null);
            dtoPai.addFilho(dtoFilho);
            dtoFilho.setPai(dtoPai);
            getLeafs().add(dtoFilho);

        }
    }

    private void removerRootSemFilhos() {
        List<EncaminhamentoPedidoDTO> temp = new ArrayList<EncaminhamentoPedidoDTO>();
        temp.addAll(getRootList());
        for (EncaminhamentoPedidoDTO root : temp) {
            if (root.getFilhos() == null || root.getFilhos().isEmpty()) {
                getRootList().remove(root);
            } else {
                if (root.getFilhos().size() == 1 && root.getFilhos().get(0).isHeader()) {
                    getRootList().remove(root);
                }
            }
        }
    }

    @Override
    public void recarregarProvider() {
        setRootList(new ArrayList<EncaminhamentoPedidoDTO>());
        setLeafs(new ArrayList<EncaminhamentoPedidoDTO>());
        setMapPaisAdicionados(new HashMap<String, EncaminhamentoPedidoDTO>());
        carregar();
    }

    public void recarregarProvider(EncaminhamentoPedidoDTOparam param) {
        setRootList(new ArrayList<EncaminhamentoPedidoDTO>());
        setLeafs(new ArrayList<EncaminhamentoPedidoDTO>());
        setMapPaisAdicionados(new HashMap<String, EncaminhamentoPedidoDTO>());
        this.param = param;
        carregar();
    }

    @Override
    public void removeRoot(EncaminhamentoPedidoDTO object) {
        for (EncaminhamentoPedidoDTO root : getRootList()) {
            if (root.getFilhos() != null && !root.getFilhos().isEmpty()) {
                List<EncaminhamentoPedidoDTO> temp = new ArrayList<EncaminhamentoPedidoDTO>();
                temp.addAll(root.getFilhos());
                for (EncaminhamentoPedidoDTO filho : temp) {
                    if (filho.getPedidoTfd() != null && filho.getPedidoTfd().getNumeroPedido() != null && filho.getPedidoTfd().getNumeroPedido().equals(object.getPedidoTfd().getNumeroPedido())) {
                        root.getFilhos().remove(filho);
                    }
                }
            }
        }
        removerRootSemFilhos();
    }

}
