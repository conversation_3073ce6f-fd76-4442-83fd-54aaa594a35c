package br.com.celk.view.vigilancia.externo.view.consulta;

import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.SimpleEditorSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilanciaExterno;
import br.com.celk.view.vigilancia.externo.view.components.feedback.FeedBackVigilancia;
import br.com.celk.view.vigilancia.externo.view.components.feedback.IFeedBackVigilancia;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.string.StringValue;
import org.wicketstuff.annotation.mount.MountPath;

/**
 * <AUTHOR>
 */
@MountPath(VigilanciaHelper.URL_PARECER_PROCESSO_ADMINISTRATIVO)
public class VigilanciaConsultaQRcodeParecerProcessoAdministrativoPage extends BasePageVigilanciaExterno implements IFeedBackVigilancia {

    public static String QRCODE = "CHQRC";

    private StatelessForm form;
    private Label lbTitulo;
    private Label lblSituacao;
    private Label lblDataParecer;
    private Label lblProtocolo;
    private String titulo;
    private String situacao;
    private String dataParecer;
    private String protocolo;
    private String txtParecer;
    private RequiredInputArea txtaParecer;
    private ProcessoAdministrativoParecer parecer;
    private FeedBackVigilancia feedBackVigilancia;
    private WebMarkupContainer containerForm;
    private AjaxPreviewBlank ajaxPreviewBlank;

    @Override
    protected void onInitialize() {
        super.onInitialize();

        form = new StatelessForm("form");
        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        feedBackVigilancia = new FeedBackVigilancia("feedBack");
        feedBackVigilancia.setOutputMarkupId(true);
        lbTitulo = new Label("titulo", new PropertyModel<String>(this, "titulo"));
        lbTitulo.setDefaultModel(Model.of(BundleManager.getString("consultaParecerProcessoAdministrativo")));
        lblSituacao = new Label("situacao");
        lblDataParecer = new Label("dataParecer", new PropertyModel<String>(this, "dataParecer"));
        lblProtocolo = new Label("protocolo", new PropertyModel<String>(this, "protocolo"));

        txtaParecer = new RequiredInputArea("txtParecer", new PropertyModel<String>(this, "txtParecer"));
        txtaParecer.add(new EditorBehavior(new SimpleEditorSettings(500, 300, true, true)));
        txtaParecer.setEnabled(false);

        containerForm = new WebMarkupContainer("containerForm");
        containerForm.setOutputMarkupPlaceholderTag(true);
        containerForm.add(lblSituacao, lblProtocolo, lblDataParecer, txtaParecer);
        containerForm.add(ajaxPreviewBlank = new AjaxPreviewBlank());

        form.add(feedBackVigilancia, lbTitulo, containerForm);

        buscarParecer();

        add(form);
    }

    private void buscarParecer() {
        ProcessoAdministrativoParecer parecer = null;
        StringValue qrcode = getPageParameters().get(QRCODE);
        if (!qrcode.isNull() && !qrcode.isEmpty() && qrcode.toString() != null) {
            parecer = LoadManager.getInstance(ProcessoAdministrativoParecer.class)
                    .addProperties(new HQLProperties(ProcessoAdministrativoParecer.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(ProcessoAdministrativoParecer.PROP_CHAVE_Q_RCODE, qrcode.toString()))
                    .setMaxResults(1)
                    .start().getVO();
        }

        if (parecer != null) {
            situacao = ProcessoAdministrativoParecer.Situacao.valueOf(parecer.getSituacao()).descricao();
            protocolo = parecer.getCodigo().toString();
            dataParecer = Data.formatar(parecer.getDataCadastro());
            txtParecer = parecer.getParecer();
            this.parecer = parecer;
        } else {
            error(BundleManager.getString("msgNaoFoiPossivelEncontrarRequerimento"));
            lbTitulo.setDefaultModel(Model.of(BundleManager.getString("ops")));
        }
    }

    @Override
    public String getTituloPrograma() {
        return "";
    }

    @Override
    public String getSufixoVigilancia() {
        ConfiguracaoVigilancia configuracaoVigilancia = null;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }
        if (configuracaoVigilancia != null) {
            Empresa empresa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, configuracaoVigilancia.getEmpresa().getCodigo()))
                    .setMaxResults(1).start().getVO();
            return empresa.getEnderecoCidadeBairroFormatado().toUpperCase();
        }
        return "";
    }

    @Override
    public FeedBackVigilancia getFeedBackVigilancia() {
        return feedBackVigilancia;
    }
}
