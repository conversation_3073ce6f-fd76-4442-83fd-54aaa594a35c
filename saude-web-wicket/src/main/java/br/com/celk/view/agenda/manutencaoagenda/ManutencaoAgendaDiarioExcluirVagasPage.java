package br.com.celk.view.agenda.manutencaoagenda;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.agenda.interfaces.dto.CadastroManutencaoAgendaDiarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaDiarioDTO;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.table.Table;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.agenda.manutencaoagenda.panel.ManutencaoAgendaDadosAgendaPanel;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaOcorrencia;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ManutencaoAgendaDiarioExcluirVagasPage extends BasePage {
    
    private Form<CadastroManutencaoAgendaDiarioDTO> form;
    private CadastroManutencaoAgendaDiarioDTO dto;
    private Table<ManutencaoAgendaDiarioDTO> tblHorarios;
    private InputArea txaMotivo;
    
    public ManutencaoAgendaDiarioExcluirVagasPage(CadastroManutencaoAgendaDiarioDTO dto) {
        this.dto = dto;
        init();
    }

    private void init() {
        getForm().add(new ManutencaoAgendaDadosAgendaPanel("dadosAgenda", dto.getCodigoAgenda()));
        
        CadastroManutencaoAgendaDiarioDTO proxy = on(CadastroManutencaoAgendaDiarioDTO.class);
        
        getForm().add(tblHorarios = new Table("tblHorarios", getColumns(), getCollectionProvider()));
        tblHorarios.populate();
        tblHorarios.setScrollY("135");
        tblHorarios.setScrollXInner("100%");
        
        getForm().add(txaMotivo = new RequiredInputArea<>(path(proxy.getMotivo())));
        
        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmaBloqueioVagas();
            }
        });
        
        add(getForm());
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ManutencaoAgendaDiarioDTO proxy = on(ManutencaoAgendaDiarioDTO.class);

        columns.add(createColumn(BundleManager.getString("data"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial()));
        columns.add(createColumn(BundleManager.getString("horaFinal"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraFinal()));
        columns.add(createColumn(BundleManager.getString("diaSemana"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDiaSemana()));
        columns.add(createColumn(BundleManager.getString("tipoAtendimento"), proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));
        columns.add(createColumn(BundleManager.getString("tempoMedio"), proxy.getAgendaGradeAtendimento().getTempoMedio()));
        columns.add(createColumn(BundleManager.getString("vagas"), proxy.getAgendaGradeAtendimento().getQuantidadeAtendimento()));
        columns.add(createColumn(BundleManager.getString("disponiveis"), proxy.getVagasDisponiveis()));
        columns.add(createColumn(BundleManager.getString("bloqueadas"), proxy.getAgendaGradeAtendimento().getQuantidadeAtendimentoBloqueado()));

        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getHorariosSelecionadosDTOList();
            }
        };
    }
    
    private Form<CadastroManutencaoAgendaDiarioDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(dto));
            form.getModel().getObject().setTipoOcorrencia(AgendaOcorrencia.TipoOcorrencia.EXCLUIR_HORARIOS);
        }
        return form;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("excluirVagas");
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txaMotivo)));
        response.render(OnDomReadyHeaderItem.forScript(JScript.scrollToTopTimeout(500L)));
    }
    
    private void confirmaBloqueioVagas() throws ValidacaoException, DAOException{
        BOFactoryWicket.getBO(AgendamentoFacade.class).excluirVagasManutencaoAgendaDiario(getForm().getModel().getObject());
        
        Page page = new ManutencaoAgendaDiarioPage(getForm().getModel().getObject().getCodigoAgenda());
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("vagasExcluidasSucesso"));
    }
}