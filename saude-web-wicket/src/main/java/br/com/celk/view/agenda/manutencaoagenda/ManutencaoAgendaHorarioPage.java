package br.com.celk.view.agenda.manutencaoagenda;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.agenda.interfaces.dto.CadastroManutencaoAgendaHorarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTOParam;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.cadastro.ConsultaAgendaPage;
import br.com.celk.view.agenda.manutencaoagenda.panel.ManutencaoAgendaDadosAgendaPanel;
import br.com.celk.view.atendimento.tipoatendimentoagenda.autocomplete.AutoCompleteConsultaTipoAtendimentoAgenda;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ManutencaoAgendaHorarioPage extends BasePage {
    
    private Form<ManutencaoAgendaHorarioDTOParam> form;
    private MultiSelectionTable<ManutencaoAgendaHorarioDTO> tblHorarios;
    private List<ManutencaoAgendaHorarioDTO> horariosList;
    private final Long codigoAgenda;
    private PnlDatePeriod pnlDatePeriod;
    private DropDown<Long> dropDownSituacao;
    private Long countResults;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoProfissional;
    
    public ManutencaoAgendaHorarioPage(Long codigoAgenda) {
        this.codigoAgenda = codigoAgenda;
        init();
    }
    
    private void init() {
        getForm().add(new ManutencaoAgendaDadosAgendaPanel("dadosAgenda", codigoAgenda));
        
        ManutencaoAgendaHorarioDTOParam proxy = on(ManutencaoAgendaHorarioDTOParam.class);
                
        getForm().add(pnlDatePeriod = new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        getForm().add(new HoraMinutoField(path(proxy.getHorario())));
        getForm().add(new CheckBoxLongValue(path(proxy.getDomingo())));
        getForm().add(new CheckBoxLongValue(path(proxy.getSegundaFeira())));
        getForm().add(new CheckBoxLongValue(path(proxy.getTercaFeira())));
        getForm().add(new CheckBoxLongValue(path(proxy.getQuartaFeira())));
        getForm().add(new CheckBoxLongValue(path(proxy.getQuintaFeira())));
        getForm().add(new CheckBoxLongValue(path(proxy.getSextaFeira())));
        getForm().add(new CheckBoxLongValue(path(proxy.getSabado())));
        getForm().add(new AutoCompleteConsultaTipoAtendimentoAgenda(path(proxy.getTipoAtendimentoAgenda())));
        getForm().add(getDropDownSituacao(path(proxy.getSituacao())));
        
        getForm().add(new AbstractAjaxButton("btnProcurar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                consultarHorarios(target);
            }
        });
        
        getForm().add(tblHorarios = new MultiSelectionTable("tblHorarios", getColumns(), getCollectionProvider()));
        tblHorarios.populate();
        tblHorarios.setScrollY("135");
        tblHorarios.setScrollXInner("100%");
        
        getForm().add(new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(new ConsultaAgendaPage());
            }
        });
        
        AbstractAjaxButton btnProfissional;
        getForm().add(btnProfissional = new AbstractAjaxButton("btnProfissional") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgConfirmacaoSimNaoProfissional(target);
            }
        });
        btnProfissional.setVisible(habilitarAcaoProfissional());
        
        getForm().add(new AbstractAjaxButton("btnBloquear") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                if(validarHorarioBloqueadoExisteAgendamento(target)){
                    setResponsePage(new ManutencaoAgendaHorarioBloquearVagasPage(initDTO()));
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnDesbloquear") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                CadastroManutencaoAgendaHorarioDTO dto = initDTO();
                validaSituacaoHorario(dto);
                setResponsePage(new ManutencaoAgendaHorarioDesbloquearVagasPage(dto));
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnTipoAtendimento") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                if(validarExisteAgendamento(target)){
                    setResponsePage(new ManutencaoAgendaHorarioAlterarTipoAtendimentoPage(initDTO()));                
                }
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnIncluirVagas") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                setResponsePage(new ManutencaoAgendaHorarioIncluirVagasPage(initDTO()));
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnExcluir") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                if(validarHorarioBloqueadoExisteAgendamento(target) && validarHorarioReservadoExisteAgendamento(target)){
                    setResponsePage(new ManutencaoAgendaHorarioExcluirVagasPage(initDTO()));                
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnReservarHorario") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                if(validarHorarioReservadoExisteAgendamento(target)) {
                    setResponsePage(new ManutencaoAgendaHorarioReservarVagasPage(initDTO()));
                }
            }
        });

        getForm().add(new AbstractAjaxButton("btnRetirarReserva") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarHorarioSelecionado();
                CadastroManutencaoAgendaHorarioDTO dto = initDTO();
                validaSituacaoHorarioReservado(dto);
                setResponsePage(new ManutencaoAgendaHorarioRetirarReservaPage(dto));
            }
        });
        
        add(getForm());
        pnlDatePeriod.setModelObject(new DatePeriod(DataUtil.getDataAtual(), null));
    }

    private void validaSituacaoHorario(CadastroManutencaoAgendaHorarioDTO dto) throws ValidacaoException {
        for (ManutencaoAgendaHorarioDTO manutencaoAgendaHorarioDTO : dto.getHorariosSelecionadosDTOList()) {
            if (!AgendaGradeHorario.Status.BLOQUEADO.value().equals(manutencaoAgendaHorarioDTO.getAgendaGradeHorario().getStatus())) {
                throw new ValidacaoException("É necessário que os horários selecionados estejam Bloqueados para realizar o desbloqueio.");
            }
        }
    }

    private void validaSituacaoHorarioReservado(CadastroManutencaoAgendaHorarioDTO dto) throws ValidacaoException {
        for (ManutencaoAgendaHorarioDTO manutencaoAgendaHorarioDTO : dto.getHorariosSelecionadosDTOList()) {
            if (!AgendaGradeHorario.Status.RESERVADO.value().equals(manutencaoAgendaHorarioDTO.getAgendaGradeHorario().getStatus())) {
                throw new ValidacaoException("É necessário que os horários selecionados estejam Reservados para realizar a liberação.");
            }
        }
    }


    private void initDlgConfirmacaoSimNaoProfissional(AjaxRequestTarget target){
        if (dlgConfirmacaoSimNaoProfissional == null) {
            addModal(target, dlgConfirmacaoSimNaoProfissional = new DlgConfirmacaoSimNao(newModalId(), 
                    bundle("msgDesejaTransferirCompromissosPeriodoSelecionadoNovoProfissional")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    if (!CollectionUtils.isNotNullEmpty(initDTO().getHorariosSelecionadosDTOList())){
                        throw new ValidacaoException(Bundle.getStringApplication("msgNenhumaGradeSelecionada"));
                    }
                    setResponsePage(new ManutencaoAgendaHorarioAlterarProfissionalPage(initDTO()));
                }
            });
        }
        dlgConfirmacaoSimNaoProfissional.show(target);
    }
    
    private boolean habilitarAcaoProfissional(){
        Agenda proxy = on(Agenda.class);
        
        Agenda a = LoadManager.getInstance(Agenda.class)
                .addProperty(path(proxy.getTipoProcedimento().getCodigo()))
                .addProperty(path(proxy.getTipoProcedimento().getTipoProcedimentoClassificacao().getCodigo()))
                .addProperty(path(proxy.getEmpresa().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_CODIGO, codigoAgenda))
                .start().getVO();
        
        return a.getTipoProcedimento() != null && !a.getTipoProcedimento().getTipoProcedimentoClassificacao().pertenceClassificacaoExame()
                && a.getEmpresa() != null && a.getEmpresa().getCodigo() != null;
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<>(id);
            dropDownSituacao.addChoice(null, BundleManager.getString("todos"));
            dropDownSituacao.addChoice(AgendaGradeHorario.Status.AGENDADO.value(), BundleManager.getString("agendado"));
            dropDownSituacao.addChoice(AgendaGradeHorario.Status.BLOQUEADO.value(), BundleManager.getString("bloqueado"));
            dropDownSituacao.addChoice(AgendaGradeHorario.Status.PENDENTE.value(), BundleManager.getString("pendente"));
            dropDownSituacao.addChoice(AgendaGradeHorario.Status.RESERVADO.value(), BundleManager.getString("reservado"));
        }
        return dropDownSituacao;
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ManutencaoAgendaHorarioDTO proxy = on(ManutencaoAgendaHorarioDTO.class);

        columns.add(createColumn(BundleManager.getString("data"), proxy.getAgendaGradeHorario().getDescricaoDataHoraInicial()));
        columns.add(createColumn(BundleManager.getString("horaFinal"), proxy.getAgendaGradeHorario().getDescricaoHoraFinal()));
        columns.add(createColumn(BundleManager.getString("diaSemana"), proxy.getAgendaGradeHorario().getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDiaSemana()));
        columns.add(createColumn(BundleManager.getString("tipoAtendimento"), proxy.getAgendaGradeHorario().getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));
        columns.add(createColumn(BundleManager.getString("tempoMedio"), proxy.getAgendaGradeHorario().getAgendaGradeAtendimento().getTempoMedio()));
        columns.add(createColumn(BundleManager.getString("situacao"), proxy.getAgendaGradeHorario().getDescricaoStatus()));

        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return horariosList;
            }
        };
    }

    private Form<ManutencaoAgendaHorarioDTOParam> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new ManutencaoAgendaHorarioDTOParam()));
            form.getModel().getObject().setCodigoAgenda(codigoAgenda);
        }
        return form;
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("manutencaoAgenda");
    }
    
    private void consultarHorarios(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(this);
        if(findNotificationPanel != null){
            findNotificationPanel.updateNotificationPanel(target);
        }
        getSession().getFeedbackMessages().clear();
        
        if(Data.adjustRangeHour(getForm().getModel().getObject().getPeriodo()).getDataInicial().before(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())){
            throw new ValidacaoException(bundle("msgPeriodoInicialMaiorIgualDataAtual"));
        } else {
            getForm().getModel().getObject().setCount(true);
            countResults = BOFactoryWicket.getBO(AgendamentoFacade.class).countConsultarHorariosManutencaoAgendaHorario(getForm().getModel().getObject());
            
            if(countResults > 1000L){
                horariosList = new ArrayList<>();
                throw new ValidacaoException(bundle("msgNumeroMaximoRegistrosAtingidoFavorRedefinaFiltro", "1000"));
            } else {
                getForm().getModel().getObject().setCount(false);
                horariosList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarHorariosManutencaoAgendaHorario(getForm().getModel().getObject());
            }
            target.add(tblHorarios);
        }
        tblHorarios.clearSelection(target);
    }
    
    private void validarHorarioSelecionado() throws ValidacaoException{
        if(CollectionUtils.isEmpty(tblHorarios.getSelectedObjects())){
            throw new ValidacaoException(bundle("msgSelecionePeloMenosUmHorario"));
        }
    }
    
    private boolean validarExisteAgendamento(AjaxRequestTarget target) {
        Group<ManutencaoAgendaHorarioDTO> byAgendaGradeAtendimento = Lambda.group(tblHorarios.getSelectedObjects(), 
                    by(on(ManutencaoAgendaHorarioDTO.class).getAgendaGradeHorario().getAgendaGradeAtendimento()));
        
        List<AgendaGradeHorario> aghList;
        StringBuilder sb = new StringBuilder();
        for (Group<ManutencaoAgendaHorarioDTO> group : byAgendaGradeAtendimento.subgroups()) {
            aghList = LoadManager.getInstance(AgendaGradeHorario.class)
                    .addProperty(AgendaGradeHorario.PROP_HORA)
                    .addProperty(VOUtils.montarPath(AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_TEMPO_MEDIO))
                    .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeHorario.PROP_STATUS, AgendaGradeHorario.Status.AGENDADO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeHorario.PROP_AGENDA_GRADE_ATENDIMENTO, group.first().getAgendaGradeHorario().getAgendaGradeAtendimento()))
                    .start().getList();
            
            if(CollectionUtils.isNotNullEmpty(aghList)){
                for(AgendaGradeHorario agh : aghList){
                    sb.append("\nData: ");
                    sb.append(agh.getDescricaoDataHoraInicial());
                    sb.append(" - ");
                    sb.append(agh.getDescricaoHoraFinal());
                }
                
                if(!sb.toString().isEmpty()){
                    warn(target, Bundle.getStringApplication("msg_seguintes_horarios_nao_podem_ser_alterados_pois_existem_agendamento_para_mesmo_X", sb.toString()));
                    return false;
                }
            }
        }
        return true;
    }
    
    private boolean validarHorarioBloqueadoExisteAgendamento(AjaxRequestTarget target) {
        /**
         * VALIDA SE EXISTE AGENDAMENTO PARA O HORÁRIO SELECIONADO
         */
        
        AgendaGradeHorario aga;
        StringBuilder sb = new StringBuilder();
        for(ManutencaoAgendaHorarioDTO madDTO : tblHorarios.getSelectedObjects()){
            aga = madDTO.getAgendaGradeHorario();
            
            if(AgendaGradeHorario.Status.AGENDADO.value().equals(aga.getStatus())){
                sb.append("\nData: ");
                sb.append(aga.getDescricaoDataHoraInicial());
                sb.append(" - ");
                sb.append(aga.getDescricaoHoraFinal());
            }            
        }

        if(!sb.toString().isEmpty()){
            warn(target, Bundle.getStringApplication("msg_seguintes_horarios_nao_podem_ser_bloqueados_excluidos_pois_existem_agendamento_para_mesmo_X", sb.toString()));
            return false;
        } else {
            /**
            * VALIDA SE EXISTE O HORÁRIO SELECIONADO JÁ ESTA BLOQUEADO
            */

            for(ManutencaoAgendaHorarioDTO madDTO : tblHorarios.getSelectedObjects()){
                aga = madDTO.getAgendaGradeHorario();

                if(AgendaGradeHorario.Status.BLOQUEADO.value().equals(aga.getStatus())){
                    sb.append("\nData: ");
                    sb.append(aga.getDescricaoDataHoraInicial());
                    sb.append(" - ");
                    sb.append(aga.getDescricaoHoraFinal());
                }
            }

            if(!sb.toString().isEmpty()){
                warn(target, Bundle.getStringApplication("msg_seguintes_horarios_nao_podem_ser_bloqueados_excluidos_pois_ja_estao_bloquados_X", sb.toString()));
                return false;
            }
        }
        
        return true;
    }

    private boolean validarHorarioReservadoExisteAgendamento(AjaxRequestTarget target) {
        /**
         * VALIDA SE EXISTE AGENDAMENTO PARA O HORÁRIO SELECIONADO
         */

        AgendaGradeHorario aga;
        StringBuilder sb = new StringBuilder();
        for(ManutencaoAgendaHorarioDTO madDTO : tblHorarios.getSelectedObjects()){
            aga = madDTO.getAgendaGradeHorario();

            if(AgendaGradeHorario.Status.AGENDADO.value().equals(aga.getStatus())){
                sb.append("\nData: ");
                sb.append(aga.getDescricaoDataHoraInicial());
                sb.append(" - ");
                sb.append(aga.getDescricaoHoraFinal());
            }
        }

        if(!sb.toString().isEmpty()){
            warn(target, Bundle.getStringApplication("msg_seguintes_horarios_nao_podem_ser_reservados_excluidos_pois_existem_agendamento_para_mesmo_X", sb.toString()));
            return false;
        } else {
            /**
             * VALIDA SE EXISTE O HORÁRIO SELECIONADO JÁ ESTA BLOQUEADO
             */

            for(ManutencaoAgendaHorarioDTO madDTO : tblHorarios.getSelectedObjects()){
                aga = madDTO.getAgendaGradeHorario();

                if(AgendaGradeHorario.Status.RESERVADO.value().equals(aga.getStatus())){
                    sb.append("\nData: ");
                    sb.append(aga.getDescricaoDataHoraInicial());
                    sb.append(" - ");
                    sb.append(aga.getDescricaoHoraFinal());
                }
            }

            if(!sb.toString().isEmpty()){
                warn(target, Bundle.getStringApplication("msg_seguintes_horarios_nao_podem_ser_reservados_excluidos_pois_ja_estao_reservados_X", sb.toString()));
                return false;
            }
        }

        return true;
    }
    
    private CadastroManutencaoAgendaHorarioDTO initDTO(){
        CadastroManutencaoAgendaHorarioDTO dto = new CadastroManutencaoAgendaHorarioDTO();
        dto.setHorariosSelecionadosDTOList(tblHorarios.getSelectedObjects());
        dto.setCodigoAgenda(codigoAgenda);
        return dto;
    }
}