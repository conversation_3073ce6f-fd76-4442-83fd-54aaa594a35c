package br.com.celk.view.vigilancia.eventos.columns;

import br.com.celk.component.behavior.AjaxDownload;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EventosVigilanciaAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.File;
import java.io.IOException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

/**
 *
 * <AUTHOR>
 */
public abstract class DownloadAnexoEventosColumnPanel extends Panel {

    private AbstractAjaxLink linkAnexo;
    private AjaxDownload ajaxDownload;
    private EventosVigilanciaAnexoDTO rowObject;

    public DownloadAnexoEventosColumnPanel(String id, EventosVigilanciaAnexoDTO rowObject) {
        super(id);
        this.rowObject = rowObject;
        init();
    }

    private void init() {
        add(linkAnexo = new AbstractAjaxLink("linkAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                downloadArquivo(target);
            }
        }).setEnabled(rowObject.getEventosVigilancia() != null && rowObject.getEventosVigilancia().getCodigo() != null);

        add(ajaxDownload = new AjaxDownload());
        Label label = (Label) new Label("nomeArquivo", rowObject.getNomeArquivoOriginal()).setOutputMarkupId(true);
        if(rowObject.getEventosVigilancia() == null || rowObject.getEventosVigilancia().getCodigo() == null){
            label.add(new AttributeModifier("style", "color: #000000;"));            
        }
        linkAnexo.add(label);
    }
    
    private void downloadArquivo(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        try{
            File f = File.createTempFile("anexo", "evento");
            FileUtils.buscarArquivoFtp(rowObject.getGerenciadorArquivo().getCaminho(), f.getAbsolutePath());
            
            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
            ajaxDownload.initiate(target, rowObject.getNomeArquivoOriginal(), resourceStream);
        }   catch (IOException ex) {
            throw new DAOException(ex.getMessage());
        }
    }
}