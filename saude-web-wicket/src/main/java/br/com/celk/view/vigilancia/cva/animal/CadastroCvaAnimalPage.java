package br.com.celk.view.vigilancia.cva.animal;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.foto.Foto;
import br.com.celk.component.foto.interfaces.IFoto;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.cva.coranimal.autocomplete.AutoCompleteConsultaCvaCorAnimal;
import br.com.celk.view.vigilancia.cva.proprietarioresponsavel.autocomplete.AutoCompleteConsultaCvaProprietarioResponsavelAnimal;
import br.com.celk.view.vigilancia.cva.racaanimal.autocomplete.AutoCompleteConsultaCvaRacaAnimal;
import br.com.celk.view.vigilancia.cva.vacinaanimal.autocomplete.AutoCompleteConsultaCvaVacinaAnimal;
import br.com.celk.view.vigilancia.registroatividadeveterinaria.autocomplete.AutoCompleteConsultaEspecieAnimal;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.EspecieAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaAnimalVacina;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaCorAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaRacaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.CvaVacinaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import ch.lambdaj.Lambda;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class CadastroCvaAnimalPage extends CadastroPage<CvaAnimal> {

    private InputField nomeAnimal;
    private InputField<String> cvaProprietarioRespEndereco;
    private String txtProprietarioRespEndereco;
    private CvaProprietarioResponsavel cvaProprietarioResponsavel;
    private CvaRacaAnimal cvaRacaAnimal;
    private CvaCorAnimal cvaCorAnimal;
    private DropDown dropDownSexo;
    private DropDown dropDownTipoAnimal;
    private EspecieAnimal especieAnimal;
    private DropDown<Long> dropDownCastrado;
    private DropDown<Long> dropDownSituacao;
    private AutoCompleteConsultaEspecieAnimal autoCompleteConsultaEspecieAnimal;
    private AutoCompleteConsultaCvaRacaAnimal autoCompleteConsultaCvaRacaAnimal;
    private AutoCompleteConsultaCvaVacinaAnimal autoCompleteConsultaCvaVacinaAnimal;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaCvaProprietarioResponsavelAnimal autoCompleteConsultaCvaProprietarioResponsavelAnimal;
    private CvaAnimal.Status status;
    private WebMarkupContainer containerDataObito;
    private WebMarkupContainer containerCastracao;
    private DateChooser dataObitoDesaparecimento;
    private boolean editar;
    private GerenciadorArquivo gerenciadorArquivo;
    private CheckBoxLongValue checkBoxCastracaoParticular;
    private WebMarkupContainer containerVacina;
    private CompoundPropertyModel<CvaAnimalVacina> modelVacina;
    private SelectionTable<CvaAnimalVacina> tblVacina;
    private DateChooser dcDataVacina;
    private CvaVacinaAnimal cvaVacinaAnimal;
    private Foto foto;
    private List<CvaAnimalVacina> listAnimalVacina = new ArrayList<>();
    private AbstractAjaxButton btnAdicionar;
    private Date dataVacina;

    public CadastroCvaAnimalPage(CvaAnimal object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        this.editar = editar;
        if (object.getCvaProprietarioResponsavel() != null) {
            if (isEstrangeiro(object.getCvaProprietarioResponsavel())) {
                txtProprietarioRespEndereco = object.getCvaProprietarioResponsavel().getEnderecoFormatadoEstrangeiro();
            } else {
                txtProprietarioRespEndereco = object.getCvaProprietarioResponsavel().getEnderecoFormatadoComCidade();
            }
        }
        carregarVacinas(object.getCodigo());
    }

    private void carregarVacinas(Long codigo) {
        listAnimalVacina =  LoadManager.getInstance(CvaAnimalVacina.class)
                .addProperties(new HQLProperties(CvaAnimalVacina.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CvaAnimalVacina.PROP_CVA_ANIMAL, CvaAnimal.PROP_CODIGO), codigo))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(CvaAnimalVacina.PROP_CVA_VACINA_ANIMAL, CvaVacinaAnimal.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
    }

    public CadastroCvaAnimalPage(CvaAnimal object, boolean viewOnly) {
        super(object, viewOnly);
        autoCompleteConsultaCvaRacaAnimal.setEnabled(false);
        if (object != null && object.getCvaRacaAnimal() != null) {
            especieAnimal = object.getCvaRacaAnimal().getEspecieAnimal();
        }
    }

    public CadastroCvaAnimalPage(CvaAnimal object) {
        this(object, false);
    }

    public CadastroCvaAnimalPage() {
        this(null);
    }

    @Override
    public void init(final Form form) {

        form.add(nomeAnimal = new RequiredInputField<String>(CvaAnimal.PROP_NOME_ANIMAL));
        form.add(dropDownSexo = getSexoTipoAnimalDropDown(CvaAnimal.PROP_SEXO));
        form.add(new DateChooser(CvaAnimal.PROP_DATA_NASCIMENTO));
        form.add(dropDownTipoAnimal = getTipoAnimalDropDown(CvaAnimal.PROP_TIPO_ANIMAL, true));
        form.add(autoCompleteConsultaEspecieAnimal = new AutoCompleteConsultaEspecieAnimal("especieAnimal", new PropertyModel(this, "especieAnimal"), true));
        form.add(autoCompleteConsultaCvaRacaAnimal = new AutoCompleteConsultaCvaRacaAnimal("cvaRacaAnimal", true));
        form.add(new AutoCompleteConsultaCvaCorAnimal("cvaCorAnimal", true));

        form.add(dropDownCastrado = DropDownUtil.getSimNaoLongDropDown(CvaAnimal.PROP_CASTRADO, true, false));
        containerCastracao = new WebMarkupContainer("containerCastracao");
        containerCastracao.setOutputMarkupId(true);
        containerCastracao.setOutputMarkupPlaceholderTag(true);
        containerCastracao.setVisible(false);
        containerCastracao.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(CvaAnimal.PROP_PROFISSIONAL_CASTRACAO));
        autoCompleteConsultaProfissional.setOutputMarkupId(true);
        autoCompleteConsultaProfissional.setCodigoTabelaCbo("223305");
        autoCompleteConsultaProfissional.setCodigoEmpresa(SessaoAplicacaoImp.getInstance().getCodigoEmpresa());
        containerCastracao.add(checkBoxCastracaoParticular = new CheckBoxLongValue(CvaAnimal.PROP_CASTRACAO_PARTICULAR));
        form.add(containerCastracao);
        if (getForm().getModelObject().getCastrado() != null && RepositoryComponentDefault.SIM_LONG.toString().equals(getForm().getModelObject().getCastrado())) {
            containerCastracao.setVisible(true);
        }
        if (getForm().getModelObject().getCastracaoParticular() != null && RepositoryComponentDefault.SIM_LONG.equals(getForm().getModelObject().getCastracaoParticular())) {
            autoCompleteConsultaProfissional.setEnabled(false);
        }
        dropDownCastrado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getForm().getModelObject().getCastrado() != null && RepositoryComponentDefault.SIM_LONG.toString().equals(getForm().getModelObject().getCastrado())) {
                    containerCastracao.setVisible(true);
                } else {
                    containerCastracao.setVisible(false);
                    checkBoxCastracaoParticular.limpar(target);
                    autoCompleteConsultaProfissional.limpar(target);
                    autoCompleteConsultaProfissional.setEnabled(true);
                }
                target.add(containerCastracao);
                target.add(checkBoxCastracaoParticular);
                target.add(autoCompleteConsultaProfissional);
            }
        });
        checkBoxCastracaoParticular.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getForm().getModelObject().getCastracaoParticular() != null && RepositoryComponentDefault.SIM_LONG.equals(getForm().getModelObject().getCastracaoParticular())) {
                    autoCompleteConsultaProfissional.setEnabled(false);
                    autoCompleteConsultaProfissional.limpar(target);
                }else {
                    autoCompleteConsultaProfissional.setEnabled(true);
                }
                target.add(autoCompleteConsultaProfissional);
            }
        });

        form.add(new InputField<Long>(CvaAnimal.PROP_NUMERO_MICROCHIP));
        form.add(dropDownSituacao = getDropDownSituacao(CvaAnimal.PROP_STATUS));
        if (getForm().getModelObject().getStatus() == null) {
            dropDownSituacao.setComponentValue(CvaAnimal.Status.VIVO.value());
        } else {
            dropDownSituacao.setEnabled(false);
        }

        carregarFoto();

        form.add(foto = new Foto() {
            @Override
            public void onChange(AjaxRequestTarget target) {
                carregarFoto();
                IFoto iFoto = foto.getIFoto();
                iFoto.setGerenciadorArquivo(gerenciadorArquivo);
                foto.setIFoto(iFoto);
                target.add(foto);
            }
        });
        foto.setIFoto(new IFoto() {

            @Override
            public Serializable getCodigo() {
                return getForm().getModel().getObject().getCodigo();
            }

            @Override
            public BaseRootVO getObjetoParaSalvar() {
                return getForm().getModel().getObject();
            }

            @Override
            public String getSexoObjeto() {
                return getForm().getModel().getObject().getSexo();
            }

            @Override
            public boolean isAnimal() {
                return true;
            }

            @Override
            public GerenciadorArquivo getGerenciadorArquivo() {
                return gerenciadorArquivo;
            }

            @Override
            public void setGerenciadorArquivo(GerenciadorArquivo gerenciadorArquivo) {
                CadastroCvaAnimalPage.this.gerenciadorArquivo = gerenciadorArquivo;
                getForm().getModel().getObject().setFoto(gerenciadorArquivo);
            }
        });

        containerDataObito = new WebMarkupContainer("containerDataObito");
        containerDataObito.setOutputMarkupId(true);
        containerDataObito.setOutputMarkupPlaceholderTag(true);
        if (getForm().getModelObject().getStatus() != null && getForm().getModelObject().getStatus().equals(CvaAnimal.Status.VIVO.value())) {
            containerDataObito.setVisible(false);
        }
        containerDataObito.add(dataObitoDesaparecimento = new DateChooser(CvaAnimal.PROP_DATA_OBITO_DESAPARECIMENTO));
        dropDownSituacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getForm().getModelObject().getStatus() != null && !getForm().getModelObject().getStatus().equals(CvaAnimal.Status.VIVO.value())) {
                    containerDataObito.setVisible(true);
                    dataObitoDesaparecimento.addRequiredClass();
                } else {
                    containerDataObito.setVisible(false);
                    dataObitoDesaparecimento.removeRequiredClass();
                }
                target.add(containerDataObito);
            }
        });
        form.add(containerDataObito);

        autoCompleteConsultaCvaProprietarioResponsavelAnimal = new AutoCompleteConsultaCvaProprietarioResponsavelAnimal("cvaProprietarioResponsavel", false);
        form.add(cvaProprietarioRespEndereco = (InputField<String>) new InputField<String>("cvaProprietarioRespEndereco", new PropertyModel(this, "txtProprietarioRespEndereco")).setEnabled(false));
        autoCompleteConsultaCvaProprietarioResponsavelAnimal.add(new RemoveListener<CvaProprietarioResponsavel>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, CvaProprietarioResponsavel object) {
                txtProprietarioRespEndereco = null;
                target.add(cvaProprietarioRespEndereco);
            }
        });
        autoCompleteConsultaCvaProprietarioResponsavelAnimal.add(new ConsultaListener<CvaProprietarioResponsavel>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, CvaProprietarioResponsavel object) {
                txtProprietarioRespEndereco = getEnderecoProprietarioResponsavel(object);
                target.add(cvaProprietarioRespEndereco);
            }
        });
        dropDownTipoAnimal.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getForm().getModelObject().getTipoAnimal() != null && getForm().getModelObject().getTipoAnimal().equals(CvaAnimal.TipoAnimal.DE_PROPRIETARIO.value())) {
                    autoCompleteConsultaCvaProprietarioResponsavelAnimal.setRequired(true);
                    autoCompleteConsultaCvaProprietarioResponsavelAnimal.getTxtDescricao().addRequiredClass();
                } else {
                    autoCompleteConsultaCvaProprietarioResponsavelAnimal.setRequired(false);
                    autoCompleteConsultaCvaProprietarioResponsavelAnimal.getTxtDescricao().removeRequiredClass();
                }
                target.add(autoCompleteConsultaCvaProprietarioResponsavelAnimal);
            }
        });
        form.add(autoCompleteConsultaCvaProprietarioResponsavelAnimal);
        form.add(new InputArea(CvaAnimal.PROP_OBSERVACAO));

        autoCompleteConsultaEspecieAnimal.add(new RemoveListener<EspecieAnimal>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, EspecieAnimal object) {
                autoCompleteConsultaCvaRacaAnimal.limpar(target);
                autoCompleteConsultaCvaRacaAnimal.setEnabled(false);
            }
        });
        autoCompleteConsultaEspecieAnimal.add(new ConsultaListener<EspecieAnimal>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, EspecieAnimal object) {
                if (object != null) {
                    autoCompleteConsultaCvaRacaAnimal.setEnabled(true);
                    autoCompleteConsultaCvaRacaAnimal.setCodigoEspecie(object.getCodigo());
                    target.focusComponent(autoCompleteConsultaCvaRacaAnimal.getTxtDescricao().getTextField());
                } else {
                    autoCompleteConsultaCvaRacaAnimal.setEnabled(false);
                }
                target.add(autoCompleteConsultaCvaRacaAnimal);
            }
        });

        autoCompleteConsultaCvaRacaAnimal.getTxtDescricao().getTextField().add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        containerVacina = new WebMarkupContainer("containerVacina", modelVacina = new CompoundPropertyModel<>(new CvaAnimalVacina()));
        containerVacina.setOutputMarkupId(true);

        containerVacina.add(autoCompleteConsultaCvaVacinaAnimal = new AutoCompleteConsultaCvaVacinaAnimal("cvaVacinaAnimal", false));
        autoCompleteConsultaCvaVacinaAnimal.setSituacao(CvaVacinaAnimal.Situacao.ATIVO.value());
        containerVacina.add(dcDataVacina = new DateChooser("dataVacina", new PropertyModel<Date>(this, "dataVacina")));
        dcDataVacina.addAjaxUpdateValue();
        containerVacina.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        btnAdicionar.setDefaultFormProcessing(false);

        containerVacina.add(tblVacina = new SelectionTable("tblVacina", getColumns(), getCollectionProvider()));
        tblVacina.populate();

        form.add(containerVacina);
    }

    @Override
    public Object salvar(CvaAnimal object) throws DAOException, ValidacaoException {
        CvaAnimal salvo = (CvaAnimal) super.salvar(object);
        if (CollectionUtils.isNotNullEmpty(listAnimalVacina)) {
            Lambda.forEach(listAnimalVacina).setCvaAnimal(salvo);
            VOUtils.persistirListaVosModificados(CvaAnimalVacina.class, listAnimalVacina, new QueryCustom.QueryCustomParameter(CvaAnimalVacina.PROP_CVA_ANIMAL, salvo));
        }
        return salvo;
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if(autoCompleteConsultaCvaVacinaAnimal.getComponentValue() == null){
            throw new ValidacaoException(bundle("informeVacina"));
        }

        if(dcDataVacina.getComponentValue().after(DataUtil.getDataAtual())){
            throw new ValidacaoException(bundle("msgNaoPermitidoAdicionarVacinasParaDataFutura"));
        }

        if (CollectionUtils.isNotNullEmpty(listAnimalVacina)) {
            for (CvaAnimalVacina cva : listAnimalVacina) {
                CvaVacinaAnimal cvaVacinaAnimal = (CvaVacinaAnimal) autoCompleteConsultaCvaVacinaAnimal.getComponentValue();
                if (cva.getCvaVacinaAnimal().getCodigo().equals(cvaVacinaAnimal.getCodigo())) {
                    autoCompleteConsultaCvaVacinaAnimal.limpar(target);
                    dcDataVacina.limpar(target);
                    throw new ValidacaoException(bundle("itemJaAdicionado"));
                }
            }

        }
        CvaAnimalVacina cav = new CvaAnimalVacina();
        cav.setCvaAnimal(getForm().getModel().getObject());
        cav.setCvaVacinaAnimal((CvaVacinaAnimal) autoCompleteConsultaCvaVacinaAnimal.getComponentValue());
        cav.setDataVacina(dcDataVacina.getComponentValue());
        cav.setDataCadastro(DataUtil.getDataAtual());
        cav.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
        listAnimalVacina.add(cav);
        tblVacina.update(target);
        dcDataVacina.limpar(target);
        autoCompleteConsultaCvaVacinaAnimal.limpar(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        CvaAnimalVacina proxy = on(CvaAnimalVacina.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("vacina"), proxy.getCvaVacinaAnimal().getDescricao()));
        columns.add(createColumn(bundle("dataVacina"), proxy.getDataVacina()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<CvaAnimalVacina>() {
            @Override
            public void customizeColumn(final CvaAnimalVacina rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target,tblVacina,listAnimalVacina,rowObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return listAnimalVacina;
            }
        };
    }

    @Override

    public FormComponent getComponentRequestFocus() {
        return nomeAnimal;
    }

    @Override
    public Class<CvaAnimal> getReferenceClass() {
        return CvaAnimal.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaCvaAnimalPage.class;
    }

    @Override
    public Page getResponsePageInstance(Object returnObject) throws InstantiationException, IllegalAccessException {
        return new ConsultaCvaAnimalPage((CvaAnimal) returnObject);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroAnimais");
    }

    private DropDown getSexoTipoAnimalDropDown(String id) {
        DropDown dropDown = new RequiredDropDown(id);
        dropDown.addChoice(null, "");
        dropDown.addChoice(RepositoryComponentDefault.SexoAnimal.MACHO.value(), RepositoryComponentDefault.SexoAnimal.MACHO.descricao());
        dropDown.addChoice(RepositoryComponentDefault.SexoAnimal.FEMEA.value(), RepositoryComponentDefault.SexoAnimal.FEMEA.descricao());
        return dropDown;
    }

    public static DropDown<String> getTipoAnimalDropDown(DropDown dropDown) {
        dropDown.addChoice(null, "");
        dropDown.addChoice(CvaAnimal.TipoAnimal.DE_PROPRIETARIO.value(), BundleManager.getString("tpAnimalDeProprietario"));
        dropDown.addChoice(CvaAnimal.TipoAnimal.ERRANTE.value(), BundleManager.getString("tpAnimalErrante"));
        dropDown.addChoice(CvaAnimal.TipoAnimal.COMUNITARIO.value(), BundleManager.getString("tpAnimalComunitario"));

        return dropDown;
    }

    public static DropDown<String> getTipoAnimalDropDown(String id) {
        return getTipoAnimalDropDown(id, false);
    }

    public static DropDown<String> getTipoAnimalDropDown(String id, boolean required) {
        DropDown<String> dropDown;
        if (required) {
            dropDown = new RequiredDropDown<String>(id);
        } else {
            dropDown = new DropDown<String>(id);
        }
        return getTipoAnimalDropDown(dropDown);
    }

    public DropDown<Long> getDropDownSituacao(String id) {
        if (dropDownSituacao == null) {
            dropDownSituacao = new RequiredDropDown<Long>(id);
            dropDownSituacao.addChoice(CvaAnimal.Status.VIVO.value(), BundleManager.getString("rotuloVivo"));
            dropDownSituacao.addChoice(CvaAnimal.Status.MORTO.value(), BundleManager.getString("rotuloMorto"));
            dropDownSituacao.addChoice(CvaAnimal.Status.DESAPARECIDO.value(), BundleManager.getString("rotuloDesaparecido"));
        }
        return dropDownSituacao;
    }

    private boolean isEstrangeiro(CvaProprietarioResponsavel cvaProprietarioResponsavel) {
        return RepositoryComponentDefault.SIM_LONG.equals(cvaProprietarioResponsavel.getFlagEstrangeiro());
    }

    private void carregarFoto() {
        if (getForm().getModel().getObject().getCodigo() != null) {
            CvaAnimal vo = LoadManager.getInstance(CvaAnimal.class)
                    .setId(getForm().getModel().getObject().getCodigo())
                    .addProperty(VOUtils.montarPath(CvaAnimal.PROP_FOTO, GerenciadorArquivo.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(CvaAnimal.PROP_FOTO, GerenciadorArquivo.PROP_CAMINHO))
                    .start().getVO();
            gerenciadorArquivo = vo.getFoto();
        }
    }

    private String getEnderecoProprietarioResponsavel(CvaProprietarioResponsavel object) {
        String endereco = null;
        if (isEstrangeiro(object)) {
            endereco = object.getEnderecoFormatadoEstrangeiro();
        } else {
            VigilanciaEndereco proxy = on(VigilanciaEndereco.class);
            VigilanciaEndereco vo = LoadManager.getInstance(VigilanciaEndereco.class)
                    .addProperty(path(proxy.getBairro()))
                    .addProperty(path(proxy.getCep()))
                    .addProperty(path(proxy.getCidade().getDescricao()))
                    .addProperty(path(proxy.getCidade().getEstado().getSigla()))
                    .addProperty(path(proxy.getLogradouro()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), object.getVigilanciaEndereco().getCodigo()))
                    .start().getVO();
            endereco = vo.getEnderecoFormatadoComCidade();
        }
        return endereco;
    }
}
