package br.com.celk.view.unidadesaude.exames.prestadorservico;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.celk.view.unidadesaude.exames.prestadorservico.customcolumn.PrestadorServicoActionColumnPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeExame;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.RadioGroup;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 */
@Private
public class CadastroPrestadorServicoStep2Page extends BasePage {

    private WebMarkupContainer container;
    private List<ExamePrestadorProcedimento> lstExames = new ArrayList<ExamePrestadorProcedimento>();
    private Table<ExamePrestadorProcedimento> table;
    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;
    private AutoCompleteConsultaExameProcedimento autoCompleteConsultaExameProcedimento;

    private ExamePrestadorProcedimento itemEdicao;

    private TipoExame tipoExame;
    private ExameProcedimento exameProcedimento;
    private ExamePrestadorCadastroDTO examePrestadorCadastroDTO;
    private boolean tipoCadastro = true;
    private boolean consulta;
    private Double valorProcedimento;
    private DoubleField doubleFieldValorProcedimento;
    private Double valorComplementar;
    private DoubleField doubleFieldValorComplementar;
    private RadioGroup radioGroup;


    private WebMarkupContainer containerValorDiferenciado;
    private WebMarkupContainer containerValorComplementar;

    public CadastroPrestadorServicoStep2Page(ExamePrestadorCadastroDTO examePrestadorCadastroDTO, boolean consulta) {
        try {
            this.consulta = consulta;
            this.examePrestadorCadastroDTO = examePrestadorCadastroDTO;
            init();
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void init() throws DAOException, ValidacaoException {
        Form form = new Form("form", new CompoundPropertyModel(examePrestadorCadastroDTO));
        form.add(new DisabledInputField("examePrestador.prestador.descricao"));
        form.add(new DisabledInputField("examePrestador.tipoExame.descricao"));
        form.add(new DisabledInputField("examePrestador.tipoExameSecundario.descricao"));

        container = new WebMarkupContainer("container", new CompoundPropertyModel(this));
        containerValorDiferenciado = new WebMarkupContainer("containerValorDiferenciado", new CompoundPropertyModel(this));
        containerValorComplementar = new WebMarkupContainer("containerValorComplementar", new CompoundPropertyModel(this));
        container.add(containerValorDiferenciado);
        container.add(containerValorComplementar);

        container.add(radioGroup = new RadioGroup("tipoCadastro"));
        radioGroup.add(new AjaxRadio("exame", new Model(true)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                autoCompleteConsultaTipoExame.setEnabled(false);
                autoCompleteConsultaTipoExame.limpar(target);
                autoCompleteConsultaExameProcedimento.setEnabled(true);
                doubleFieldValorProcedimento.setEnabled(true);
                target.add(autoCompleteConsultaTipoExame);
                target.add(autoCompleteConsultaExameProcedimento);
                target.add(doubleFieldValorProcedimento);
            }
        });
        radioGroup.add(new AjaxRadio("tipo", new Model(false)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                autoCompleteConsultaTipoExame.setEnabled(true);
                autoCompleteConsultaExameProcedimento.setEnabled(false);
                autoCompleteConsultaExameProcedimento.limpar(target);
                doubleFieldValorProcedimento.setEnabled(false);
                doubleFieldValorProcedimento.limpar(target);
                target.add(autoCompleteConsultaTipoExame);
                target.add(autoCompleteConsultaExameProcedimento);
                target.add(doubleFieldValorProcedimento);
            }
        });

        container.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame("tipoExame"));
        autoCompleteConsultaTipoExame.setOutputMarkupId(true);
        autoCompleteConsultaTipoExame.addAjaxUpdateValue();

        container.add(autoCompleteConsultaExameProcedimento = new AutoCompleteConsultaExameProcedimento("exameProcedimento"));
        if (examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario() != null) {
            autoCompleteConsultaExameProcedimento.setTipoExameList(Arrays.asList(examePrestadorCadastroDTO.getExamePrestador().getTipoExame(), examePrestadorCadastroDTO.getExamePrestador().getTipoExameSecundario()));
        } else {
            autoCompleteConsultaExameProcedimento.setTipoExame(examePrestadorCadastroDTO.getExamePrestador().getTipoExame());
        }
        autoCompleteConsultaExameProcedimento.setOutputMarkupId(true);
        autoCompleteConsultaExameProcedimento.addAjaxUpdateValue();
        autoCompleteConsultaTipoExame.setEnabled(false);

        containerValorDiferenciado.add(doubleFieldValorProcedimento = new DoubleField("valorProcedimento"));
        containerValorComplementar.add(doubleFieldValorComplementar = new DoubleField("valorComplementar"));
        if (examePrestadorCadastroDTO.isControleFinanceiro()) {
            containerValorDiferenciado.setVisible(false);
            containerValorComplementar.setVisible(true);
        } else {
            containerValorDiferenciado.setVisible(true);
            containerValorComplementar.setVisible(false);
        }
        container.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                btnAdicionarAction(target);
                if (tipoCadastro) {
                    target.focusComponent(autoCompleteConsultaExameProcedimento.getTxtDescricao().getTextField());
                } else {
                    target.focusComponent(autoCompleteConsultaTipoExame.getTxtDescricao().getTextField());
                }
            }
        });


        form.add(container);
        form.add(table = new Table<ExamePrestadorProcedimento>("table", getColumns(), getCollectionProvider()));

        table.setScrollY("200px");
        table.setScrollXInner("100%");
        table.populate();

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        if (consulta) {
            carregaExames();
        }

        add(form);

    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoExame.getTxtDescricao().getTextField();
    }

    private List<ISortableColumn<ExamePrestadorProcedimento>> getColumns() {
        List<ISortableColumn<ExamePrestadorProcedimento>> columns = new ArrayList<ISortableColumn<ExamePrestadorProcedimento>>();

        ColumnFactory columnFactory = new ColumnFactory(ExamePrestadorProcedimento.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_REFERENCIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("codigoProcedimento"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("procedimentoSigtap"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("procedimento"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO)));
        if (examePrestadorCadastroDTO.isControleFinanceiro()) {
            columns.add(columnFactory.createColumn(BundleManager.getString("valorComplementar"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_VALOR_COMPLEMENTAR)));
        } else {
            columns.add(columnFactory.createColumn(BundleManager.getString("valorDiferenciado"), VOUtils.montarPath(ExamePrestadorProcedimento.PROP_VALOR_PROCEDIMENTO)));
        }

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<ExamePrestadorProcedimento>() {
            @Override
            public Component getComponent(String componentId, final ExamePrestadorProcedimento rowObject) {
                return new PrestadorServicoActionColumnPanel(componentId, rowObject) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItem(target, rowObject);
                    }

                    @Override
                    public void onEditar(AjaxRequestTarget target, ExamePrestadorProcedimento rowObject) throws ValidacaoException, DAOException {
                        editarExame(target, rowObject);
                    }
                };
            }
        };
    }

    private void limparItem(AjaxRequestTarget target) {
        autoCompleteConsultaExameProcedimento.limpar(target);
        autoCompleteConsultaTipoExame.limpar(target);
        doubleFieldValorComplementar.limpar(target);
        doubleFieldValorProcedimento.limpar(target);

        exameProcedimento = null;
        tipoExame = null;
        valorComplementar = null;
        valorProcedimento = null;

        itemEdicao = null;
    }

    private void editarExame(AjaxRequestTarget target, ExamePrestadorProcedimento modelObject) {
        limparItem(target);

        itemEdicao = modelObject;

//        tipoExame = modelObject.getExameProcedimento().getTipoExame();
        exameProcedimento = modelObject.getExameProcedimento();
        valorComplementar = modelObject.getValorComplementar();
        valorProcedimento = modelObject.getValorProcedimento();
        tipoCadastro = true;

        autoCompleteConsultaTipoExame.setEnabled(false);
        autoCompleteConsultaExameProcedimento.setEnabled(false);
        radioGroup.setEnabled(false);

        target.add(autoCompleteConsultaExameProcedimento);
        target.add(autoCompleteConsultaTipoExame);
//        target.add(radioGroup);
        target.add(doubleFieldValorComplementar);
        target.add(doubleFieldValorProcedimento);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstExames;
            }
        };
    }

    private void btnAdicionarAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (this.tipoExame != null) {
            List<ExameProcedimento> ExameProcedimentoList = LoadManager.getInstance(ExameProcedimento.class)
                    .addProperties(new HQLProperties(ExameProcedimento.class).getProperties())
                    .addProperties(VOUtils.montarPath(ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameProcedimento.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), tipoExame.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameProcedimento.PROP_SITUACAO, ExameProcedimento.Situacao.ATIVO.value()))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(ExameProcedimentoList)) {
                Double valorCompl = this.valorComplementar;
                for (ExameProcedimento _exameProcedimento : ExameProcedimentoList) {
                    ExamePrestadorProcedimento examePrestadorProcedimento = new ExamePrestadorProcedimento();
                    examePrestadorProcedimento.setExameProcedimento(_exameProcedimento);
                    examePrestadorProcedimento.setValorComplementar(valorCompl);
                    addTabela(examePrestadorProcedimento, target);
                }
            }
        } else if (this.exameProcedimento != null) {
            if (itemEdicao != null) {
                itemEdicao.setExameProcedimento(this.exameProcedimento);
                itemEdicao.setValorComplementar(this.valorComplementar);
                itemEdicao.setValorProcedimento(this.valorProcedimento);
                addTabela(itemEdicao, target);
            } else {
                ExamePrestadorProcedimento examePrestadorProcedimento = new ExamePrestadorProcedimento();
                examePrestadorProcedimento.setExameProcedimento(this.exameProcedimento);
                examePrestadorProcedimento.setValorProcedimento(this.valorProcedimento);
                examePrestadorProcedimento.setValorComplementar(this.valorComplementar);
                addTabela(examePrestadorProcedimento, target);
            }
            table.update(target);
        } else {
            throw new ValidacaoException(BundleManager.getString("informeExameOuTipoExame"));
        }

    }

    private void removerItem(AjaxRequestTarget target, ExamePrestadorProcedimento _dto) {
        for (int i = 0; i < lstExames.size(); i++) {
            if (lstExames.get(i) == _dto) {
                lstExames.remove(i);
            }
        }
        table.update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaExameProcedimento.limpar(target);
        autoCompleteConsultaTipoExame.limpar(target);
        doubleFieldValorComplementar.limpar(target);
        doubleFieldValorProcedimento.limpar(target);
    }

    public void salvar(AjaxRequestTarget art) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(ExameFacade.class).cadastrarExamePrestadorComUnidade(examePrestadorCadastroDTO.getUnidades(), lstExames, examePrestadorCadastroDTO.getExamePrestador(), examePrestadorCadastroDTO.getContratoList());
        try {
            Page page = (Page) getResponsePage().newInstance();
            getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
            setResponsePage(page);
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public Class getResponsePage() {
        return ConsultaPrestadorServicoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroExamePrestador");
    }

    public void addTabela(ExamePrestadorProcedimento dto, AjaxRequestTarget target) {
        for (ExamePrestadorProcedimento examePrestadorProcedimento : lstExames) {
            if (examePrestadorProcedimento.getExameProcedimento().getCodigo().equals(dto.getExameProcedimento().getCodigo())) {
                examePrestadorProcedimento.setValorComplementar(dto.getValorComplementar());
                limpar(target);
                dto.setExamePrestador(examePrestadorCadastroDTO.getExamePrestador());
                limpar(target);
                table.update(target);
                autoCompleteConsultaTipoExame.focus(target);
                return;
            }
        }
        dto.setExameProcedimento(dto.getExameProcedimento());
        dto.setExamePrestador(examePrestadorCadastroDTO.getExamePrestador());
        int idx = 0;
        if (itemEdicao != null) {
            for (int i = 0; i < lstExames.size(); i++) {
                ExamePrestadorProcedimento examePrestadorProcedimento = lstExames.get(i);
                if (itemEdicao == examePrestadorProcedimento) {
                    idx = i;
                }
            }
            lstExames.remove(idx);
            lstExames.add(itemEdicao);
            autoCompleteConsultaExameProcedimento.setEnabled(true);
            autoCompleteConsultaExameProcedimento.limpar(target);
        } else {
            lstExames.add(0, dto);
        }

        itemEdicao = null;

        limpar(target);
        table.update(target);
        autoCompleteConsultaExameProcedimento.focus(target);
        target.add(autoCompleteConsultaExameProcedimento);
    }

    private void carregaExames() throws DAOException, ValidacaoException {
        List<ExamePrestadorProcedimento> lst = LoadManager.getInstance(ExamePrestadorProcedimento.class)
                .addProperties(new HQLProperties(ExamePrestadorProcedimento.class).getProperties())
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO))
                .addProperties(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorProcedimento.PROP_EXAME_PRESTADOR), examePrestadorCadastroDTO.getExamePrestador()))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lst)) {
            this.lstExames.addAll(lst);
        }
    }
}
