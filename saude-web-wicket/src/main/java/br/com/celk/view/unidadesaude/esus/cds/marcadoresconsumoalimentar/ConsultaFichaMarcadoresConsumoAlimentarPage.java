package br.com.celk.view.unidadesaude.esus.cds.marcadoresconsumoalimentar;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaMarcadoresConsumoAlimentar;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaFichaMarcadoresConsumoAlimentarPage extends ConsultaPage<EsusFichaMarcadoresConsumoAlimentar, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa empresa;
    private Profissional profissional;
    private DatePeriod periodo;
    private boolean permissionEmpresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;

    private InputField txtPaciente;
    private String paciente;
    private InputField inputCodigoPaciente;
    private Long codigoPaciente;
    private InputField inputReferenciaPaciente;
    private Long referenciaPaciente;
    private boolean parametroReferencia;
    private WebMarkupContainer containerReferencia;
    private WebMarkupContainer containerCodigo;
    private InputField<String> txtNumeroCartao;
    private String numeroCartao;

    public ConsultaFichaMarcadoresConsumoAlimentarPage() {
    }

    public ConsultaFichaMarcadoresConsumoAlimentarPage(EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar) {
        this.esusFichaMarcadoresConsumoAlimentar = esusFichaMarcadoresConsumoAlimentar;
        setProcurarAoAbrir(true);
        getPageableTable().populate();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(!permissionEmpresa));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa((!isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA)));
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional"));
        if (!isActionPermitted(Permissions.PROFISSIONAL)) {
            autoCompleteConsultaProfissional.setModelObject(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());
            autoCompleteConsultaProfissional.setEnabled(false);
        }
        form.add(new PnlDatePeriod("periodo"));

        form.add(txtPaciente = new InputField<String>("paciente", new PropertyModel<String>(this, "paciente")));
        try {
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        form.add(containerReferencia = new WebMarkupContainer("containerReferencia"));
        containerReferencia.setOutputMarkupId(true);
        containerReferencia.add(inputReferenciaPaciente = new InputField<Long>("referenciaPaciente", new PropertyModel<Long>(this, "referenciaPaciente")));

        form.add(containerCodigo = new WebMarkupContainer("containerCodigo"));
        containerCodigo.setOutputMarkupId(true);
        containerCodigo.add(inputCodigoPaciente = new InputField<Long>("codigoPaciente", new PropertyModel<Long>(this, "codigoPaciente")));
        form.add(txtNumeroCartao = new InputField<String>("numeroCartao", new PropertyModel<String>(this, "numeroCartao")));
        if (parametroReferencia) {
            containerCodigo.setVisible(false);
        } else {
            containerReferencia.setVisible(false);
        }
        txtPaciente.addAjaxUpdateValue();
        txtNumeroCartao.addAjaxUpdateValue();
        inputReferenciaPaciente.addAjaxUpdateValue();
        inputCodigoPaciente.addAjaxUpdateValue();

    }

    public Usuario getUsuarioLogado() {
        return ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        EsusFichaMarcadoresConsumoAlimentar proxy = on(EsusFichaMarcadoresConsumoAlimentar.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("codigo"),proxy.getUsuarioCadsus().getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"),proxy.getUsuarioCadsus().getNome()));
        columns.add(createSortableColumn(bundle("empresa"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("atendimento"), proxy.getAtendimento().getCodigo()));
        columns.add(new DateTimeColumn(bundle("dataAtendimento"), path(proxy.getDataAtendimento())).setPattern("dd/MM/yyyy HH:mm"));
        return columns;
    }

    private CustomColumn<EsusFichaMarcadoresConsumoAlimentar> getCustomColumn() {
        return new CustomColumn<EsusFichaMarcadoresConsumoAlimentar>() {

            @Override
            public Component getComponent(String componentId, final EsusFichaMarcadoresConsumoAlimentar rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        boolean existeEsusIntegracaoCdsIntegrado = LoadManager.getInstance(EsusIntegracaoCds.class)
                                .addProperty(EsusIntegracaoCds.PROP_CODIGO)
                                .addParameter(new QueryCustom.QueryCustomParameter(EsusIntegracaoCds.PROP_EXPORTACAO_ESUS_PROCESSO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                                .addParameter(new QueryCustom.QueryCustomParameter(EsusIntegracaoCds.PROP_ESUS_FICHA_MARCADORES_CONSUMO_ALIMENTAR, rowObject))
                                .exists();

                        if(existeEsusIntegracaoCdsIntegrado){
                            initDlgConfirmacaoSimNao(target, rowObject);
                        } else {
                            setResponsePage(new CadastroFichaMarcadoresConsumoAlimentarStep1Page(rowObject));
                        }
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroFichaMarcadoresConsumoAlimentarStep1Page(rowObject, false));
                    }
                };
            }
        };
    }

    private void initDlgConfirmacaoSimNao(AjaxRequestTarget target, EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar){
        if (dlgConfirmacaoSimNao == null) {
            addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgJaFoiGeradoIntegracaoEsusParaFichaDesejaContinuar")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    setResponsePage(new CadastroFichaMarcadoresConsumoAlimentarStep1Page((EsusFichaMarcadoresConsumoAlimentar) getObject()));
                }
            });
        }
        dlgConfirmacaoSimNao.setObject(esusFichaMarcadoresConsumoAlimentar);
        dlgConfirmacaoSimNao.show(target);
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return EsusFichaMarcadoresConsumoAlimentar.class;
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(EsusFichaMarcadoresConsumoAlimentar.PROP_DATA_ATENDIMENTO, false);
            }
            @Override
            public List<LoadInterceptor> getInterceptors() {
                LoadInterceptor interceptor = new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (paciente != null){
                            HQLHelper hqlExists = hql.getNewInstanceSubQuery();
                            hqlExists.addToSelect("1");
                            hqlExists.addToFrom(" EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar " +
                                    " left join esusFichaMarcadoresConsumoAlimentar.usuarioCadsus usuarioCadsus");
                            hqlExists.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.codigo = " + alias + ".codigo");
                            hqlExists.addToWhereWhithAnd("(" + hql.getConsultaLiked(" usuarioCadsus.nome", paciente, true)
                                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("usuarioCadsus.apelido", paciente, true) + "))");
                            hql.addToWhereWhithAnd("exists (" + hqlExists.getQuery() + ")");
                        }
                        if (numeroCartao != null){
                            HQLHelper hqlExists = hql.getNewInstanceSubQuery();
                            hqlExists.addToSelect("1");
                            hqlExists.addToFrom(" EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar " +
                                    " left join esusFichaMarcadoresConsumoAlimentar.usuarioCadsus usuarioCadsus");
                            hqlExists.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.codigo = " + alias + ".codigo");
                            hqlExists.addToWhereWhithAnd("usuarioCadsus.codigo = ( select ucc.usuarioCadsus from UsuarioCadsusCns ucc where ucc.numeroCartao = " + numeroCartao.replace(".", "") +" and ucc.excluido = 0 and ucc.usuarioCadsus = usuarioCadsus.codigo ) ");
                            hql.addToWhereWhithAnd("exists (" + hqlExists.getQuery() + ")");
                        }
                        if (codigoPaciente != null){
                            HQLHelper hqlExists = hql.getNewInstanceSubQuery();
                            hqlExists.addToSelect("1");
                            hqlExists.addToFrom(" EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar " +
                                    " left join esusFichaMarcadoresConsumoAlimentar.usuarioCadsus usuarioCadsus");
                            hqlExists.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.codigo = " + alias + ".codigo");
                            hqlExists.addToWhereWhithAnd("usuarioCadsus.codigo = " + codigoPaciente );
                            hql.addToWhereWhithAnd("exists (" + hqlExists.getQuery() + ")");
                        }
                        if (referenciaPaciente != null){
                            HQLHelper hqlExists = hql.getNewInstanceSubQuery();
                            hqlExists.addToSelect("1");
                            hqlExists.addToFrom(" EsusFichaMarcadoresConsumoAlimentar esusFichaMarcadoresConsumoAlimentar " +
                                    " left join esusFichaMarcadoresConsumoAlimentar.usuarioCadsus usuarioCadsus");
                            hqlExists.addToWhereWhithAnd("esusFichaMarcadoresConsumoAlimentar.codigo = " + alias + ".codigo");
                            hqlExists.addToWhereWhithAnd("usuarioCadsus.referencia = '" + referenciaPaciente + "' ");
                            hql.addToWhereWhithAnd("exists (" + hqlExists.getQuery() + ")");
                        }
                    }
                };
                return Arrays.asList(interceptor);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(EsusFichaMarcadoresConsumoAlimentar.PROP_EMPRESA, empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(EsusFichaMarcadoresConsumoAlimentar.PROP_PROFISSIONAL, profissional));
        parameters.add(new QueryCustom.QueryCustomParameter(EsusFichaMarcadoresConsumoAlimentar.PROP_DATA_ATENDIMENTO, periodo));
        if (esusFichaMarcadoresConsumoAlimentar != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(EsusFichaMarcadoresConsumoAlimentar.PROP_CODIGO, esusFichaMarcadoresConsumoAlimentar.getCodigo()));
        }
        esusFichaMarcadoresConsumoAlimentar = null;

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroFichaMarcadoresConsumoAlimentarStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaFichaMarcadoresConsumoAlimentar");
    }
}
