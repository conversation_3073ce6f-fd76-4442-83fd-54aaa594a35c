package br.com.celk.view.basico.motivodesligamento.autocomplete;



import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.StringUtil;
import br.com.celk.view.basico.motivodesligamento.customize.CustomizeConsultaMotivoDesligamento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.MotivoDesligamento;
import java.util.ArrayList;
import java.util.List;

import com.amazonaws.util.StringUtils;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaMotivoDesligamento extends AutoCompleteConsulta<MotivoDesligamento> { 

    public AutoCompleteConsultaMotivoDesligamento(String id) {
        super(id);
    }

    public AutoCompleteConsultaMotivoDesligamento(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaMotivoDesligamento(String id, IModel<MotivoDesligamento> model) {
        super(id, model);
    }

    public AutoCompleteConsultaMotivoDesligamento(String id, IModel<MotivoDesligamento> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaMotivoDesligamento();
            }
            
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(MotivoDesligamento.PROP_DESCRICAO), true);
            }

            @Override
            public List getSearchParam(String searchCriteria) {
                List<BuilderQueryCustom.QueryParameter> list = new ArrayList<BuilderQueryCustom.QueryParameter>();
                if (searchCriteria != null && !StringUtils.isNullOrEmpty(searchCriteria.trim())) {
                    list.add(new QueryCustom.QueryCustomParameter(MotivoDesligamento.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, searchCriteria));
                }
                return list;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("motivosDesligamentos");
    }

}
