package br.com.celk.view.agenda.solicitacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.agenda.tipoprocedimentoclassificacao.autocomplete.AutoCompleteConsultaTipoProcedimentoClassificacao;
import br.com.celk.view.basico.doenca.autocomplete.AutoCompleteConsultaDoencaMulti;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoSolicitacoesDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.basico.Doenca;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioRelacaoSolicitacoesPage extends RelatorioPage<RelacaoSolicitacoesDTOParam>{

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    
      
    @Override
    public void init(Form form) {
        
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa").setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional").setMultiplaSelecao(true).setOperadorValor(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente").setMultiplaSelecao(true).setOperadorValor(true));
        form.add(new AutoCompleteConsultaCid("cid").setMultiplaSelecao(true).setOperadorValor(true));
        form.add(new AutoCompleteConsultaTipoProcedimentoClassificacao("tipoProcedimentoClassificacao"));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true).setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaDoencaMulti("doencaList"));
        form.add(DropDownUtil.getIEnumDropDown("condicaoEsus", Doenca.CondicaoEsus.values(), true));
        form.add(new RequiredPnlChoicePeriod("datePeriodo"));
        form.add(DropDownUtil.getEnumDropDown("situacao", RelacaoSolicitacoesDTOParam.Situacao.values()));
        form.add(DropDownUtil.getNaoSimDropDown("visualizarOcorrencia"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelacaoSolicitacoesDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoConsulta", RelacaoSolicitacoesDTOParam.TipoConsulta.values()));
        form.add(DropDownUtil.getEnumDropDown("ordenacao", RelacaoSolicitacoesDTOParam.Ordenacao.values()));
        
        form.add(DropDownUtil.getTipoRelatorioDropDownXls2("tipoArquivo"));
        
    }
    
    @Override
    public Class<RelacaoSolicitacoesDTOParam> getDTOParamClass() {
        return RelacaoSolicitacoesDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelacaoSolicitacoesDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relacaoSolicitacoes(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoSolicitacoes");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return this.autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
