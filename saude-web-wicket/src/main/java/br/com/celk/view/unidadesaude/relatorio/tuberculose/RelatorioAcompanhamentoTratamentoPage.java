package br.com.celk.view.unidadesaude.relatorio.tuberculose;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.prontuario.interfaces.dto.RelatorioAcompanhamentoTratamentosDTOParam;
import br.com.celk.prontuario.interfaces.dto.RelatorioRegistrosSintomaticosDTOParam;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.form.Form;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioAcompanhamentoTratamentoPage extends RelatorioPage<RelatorioAcompanhamentoTratamentosDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        form.add(new InputField("paciente"));
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
//
//        boolean isPermissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA);
//        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isPermissaoEmpresa);
//        autoCompleteConsultaEmpresa.setOperadorValor(true);
//        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioAcompanhamentoTratamentosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAcompanhamentoTratamentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioAcompanhamentoTratamentos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioAcompanhamentoTratamentos");
    }
}

