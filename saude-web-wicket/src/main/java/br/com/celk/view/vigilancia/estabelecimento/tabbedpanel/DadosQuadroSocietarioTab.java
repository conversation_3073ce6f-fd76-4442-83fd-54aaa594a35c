package br.com.celk.view.vigilancia.estabelecimento.tabbedpanel;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroEstabelecimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoQuadroSocietario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class DadosQuadroSocietarioTab extends TabPanel<CadastroEstabelecimentoDTO> {

    private Form<EstabelecimentoQuadroSocietario> form;
    private Table<EstabelecimentoQuadroSocietario> table;
    private List<EstabelecimentoQuadroSocietario> estabelecimentoQuadroSocietarioList;
    private InputField<String> txtCnpjCpf;
    private DropDown<Long> dropDownTipoPessoa;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf");
    private InputField<String> txtNome;
    private InputField<String> txtemail;
    private InputField<String> txtTelefone;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private DropDown<Long> dropDownTipoSocio;


    public DadosQuadroSocietarioTab(String id, CadastroEstabelecimentoDTO object) {
        super(id, object);
        this.estabelecimentoQuadroSocietarioList = object.getEstabelecimentoQuadroSocietarioList();
        init();
    }

    public void init() {
        EstabelecimentoQuadroSocietario proxy = on(EstabelecimentoQuadroSocietario.class);

        form = new Form("form", new CompoundPropertyModel(new EstabelecimentoQuadroSocietario()));

        form.add(dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getTipoPessoa()), Estabelecimento.TipoPessoa.values(), false, true, false));
        form.add(txtCnpjCpf = new InputField<String>(path(proxy.getCnpjCpf())));

        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getFormComponent().getModelObject() != null) {
                    if (Estabelecimento.TipoPessoa.FISICA.value().equals(getFormComponent().getModelObject())) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.remove(attributeModifierCnpj);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCpf);
                        }
                    } else {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.remove(attributeModifierCpf);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCnpj);
                        }
                    }
                    target.add(txtCnpjCpf);
                }
            }
        });
        this.txtCnpjCpf.add(this.attributeModifierCnpj);

        form.add(txtNome = new InputField<String>(path(proxy.getNome())));
        form.add(txtemail = new InputField<String>(path(proxy.getEmail())));
        form.add(txtTelefone = new InputField<String>(path(proxy.getTelefone())));
        form.add(dropDownTipoSocio = DropDownUtil.getIEnumDropDown(path(proxy.getQualificacao()), EstabelecimentoQuadroSocietario.Qualificacao.values(), false, true, false));
        form.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getEndereco())));

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(new AbstractAjaxButton("btnLimpar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparForm(target);
            }
        });

        add(form);

    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        EstabelecimentoQuadroSocietario socio = form.getModel().getObject();
        estabelecimentoQuadroSocietarioList.add(socio);

        table.populate();
        table.update(target);

        object.setEstabelecimentoQuadroSocietarioList(estabelecimentoQuadroSocietarioList);
        limparForm(target);
    }

    private void limparForm(AjaxRequestTarget target) {
        form.getModel().setObject(new EstabelecimentoQuadroSocietario());
        autoCompleteConsultaVigilanciaEndereco.limpar(target);
        target.add(form,autoCompleteConsultaVigilanciaEndereco);
    }


    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        EstabelecimentoQuadroSocietario proxy = on(EstabelecimentoQuadroSocietario.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("cnpjCpf"), proxy.getCnpjCpf()));
        columns.add(createColumn(bundle("nome"), proxy.getNome()));
        columns.add(createColumn(bundle("telefone"), proxy.getTelefone()));
        columns.add(createColumn(bundle("email"), proxy.getEmail()));

        return columns;
    }


    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EstabelecimentoQuadroSocietario>() {
            @Override
            public void customizeColumn(final EstabelecimentoQuadroSocietario rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EstabelecimentoQuadroSocietario>() {

                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoQuadroSocietario modelObject) throws ValidacaoException, DAOException {
                        form.getModel().setObject(VOUtils.cloneObject(modelObject));
                        target.add(form);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoQuadroSocietario>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoQuadroSocietario modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, table, estabelecimentoQuadroSocietarioList, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (estabelecimentoQuadroSocietarioList == null) {
                    estabelecimentoQuadroSocietarioList = new ArrayList<>();
                }
                return estabelecimentoQuadroSocietarioList;
            }
        };
    }


    @Override
    public String getTitle() {
        return BundleManager.getString("socios");
    }
}
