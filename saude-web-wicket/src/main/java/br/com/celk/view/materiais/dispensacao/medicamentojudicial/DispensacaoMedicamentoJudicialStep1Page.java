package br.com.celk.view.materiais.dispensacao.medicamentojudicial;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;

/**
 * <AUTHOR>
 */
@Private
public class DispensacaoMedicamentoJudicialStep1Page extends BasePage{

    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private UsuarioCadsus usuarioCadsus;
    
    @Override
    protected void postConstruct() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", true));
        
        form.add(new SubmitButton("btnAvancar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar(form);
            }
        }));
        
        add(form);
    }
    
    private void avancar(Form form) throws ValidacaoException, DAOException {
        setResponsePage(new DispensacaoMedicamentoJudicialStep2Page(usuarioCadsus));
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("dispensacaoProdutosSolicitados");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaUsuarioCadsus.getTxtDescricao().getTextField())));
    }
    
}
