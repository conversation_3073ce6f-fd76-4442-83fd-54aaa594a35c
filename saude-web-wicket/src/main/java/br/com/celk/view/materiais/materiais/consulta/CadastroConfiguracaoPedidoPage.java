package br.com.celk.view.materiais.materiais.consulta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.ConfiguracaoPedidoTransferenciaDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracao;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracaoItem;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Ramos
 */
@Private
public class CadastroConfiguracaoPedidoPage extends BasePage {

    private Form<ConfiguracaoPedidoTransferenciaDTO> form;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private Table<PedidoTransferenciaConfiguracaoItem> tblItens;
    private LongField txtDiaInicio;
    private LongField txtDiaFim;

    private GrupoProduto grupo;
    private SubGrupo subGrupo;

    private WebMarkupContainer containerItens;
    private CompoundPropertyModel<PedidoTransferenciaConfiguracaoItem> modelItens;

    private boolean permissaoEmpresa;
    private boolean viewOnly;

    public CadastroConfiguracaoPedidoPage(PedidoTransferenciaConfiguracao pedidoTransferenciaConfiguracao, boolean viewOnly) {
        this.viewOnly = viewOnly;
        init();
        if (pedidoTransferenciaConfiguracao != null) {
            carregarItensConfiguracao(null, pedidoTransferenciaConfiguracao.getEmpresa());
            if(viewOnly){
                containerItens.setVisible(false);
                autoCompleteConsultaEmpresa.setEnabled(false);
            } else {
                autoCompleteConsultaEmpresa.setEnabled(false);
            }
        }
    }

    public CadastroConfiguracaoPedidoPage() {
        this(null, false);
    }

    private void carregarItensConfiguracao(AjaxRequestTarget target, Empresa empresa) {
        PedidoTransferenciaConfiguracao pedidoTransferenciaConfiguracao = LoadManager.getInstance(PedidoTransferenciaConfiguracao.class)
                .addProperties(new HQLProperties(PedidoTransferenciaConfiguracao.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(PedidoTransferenciaConfiguracao.PROP_EMPRESA)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaConfiguracao.PROP_EMPRESA), empresa))
                .start().getVO();
        getForm().getModel().getObject().setPedidoTransferenciaConfiguracao(pedidoTransferenciaConfiguracao);

        if (pedidoTransferenciaConfiguracao != null) {
            List<PedidoTransferenciaConfiguracaoItem> listiItens = LoadManager.getInstance(PedidoTransferenciaConfiguracaoItem.class)
                    .addProperties(new HQLProperties(PedidoTransferenciaConfiguracaoItem.class).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_PEDIDO_TRANSFERENCIA_CONFIGURACAO, PedidoTransferenciaConfiguracao.PROP_EMPRESA)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_SUB_GRUPO)).getProperties())
                    .addProperty(VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO))
//                    .addProperty(path(proxy.getSubGrupo().getRoGrupoProduto().getCodigo()))
//                    .addProperty(path(proxy.getSubGrupo().getRoGrupoProduto().getDescricao()))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_PEDIDO_TRANSFERENCIA_CONFIGURACAO), pedidoTransferenciaConfiguracao))
//                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getSubGrupo().getRoGrupoProduto().getDescricao()), QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();

            getForm().getModel().getObject().setListPedidoTransferenciaConfiguracaoItem(listiItens);

        } else {
            getForm().getModel().getObject().setListPedidoTransferenciaConfiguracaoItem(new ArrayList<PedidoTransferenciaConfiguracaoItem>());
        }

        if (target != null) {
            tblItens.update(target);
        }
    }

    private void init() {

        info(bundle("msgEssaTelaTemObjetivoConfigurarPeriodoMesParaGrupoSubgrupoEmQueUnidadePoderaFazerPedidoAlmoxarifado"));

        ConfiguracaoPedidoTransferenciaDTO proxyForm = on(ConfiguracaoPedidoTransferenciaDTO.class);
        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxyForm.getPedidoTransferenciaConfiguracao().getEmpresa())));
        autoCompleteConsultaEmpresa.setEnabled(!viewOnly);
        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                carregarItensConfiguracao(target, object);
            }
        });
        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                getForm().getModel().getObject().setPedidoTransferenciaConfiguracao(new PedidoTransferenciaConfiguracao());
                getForm().getModel().getObject().setListPedidoTransferenciaConfiguracaoItem(new ArrayList<PedidoTransferenciaConfiguracaoItem>());
                tblItens.update(target);
            }
        });

        permissaoEmpresa = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA, ConsultaConfiguracaoPedidoPage.class);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!permissaoEmpresa);

        PedidoTransferenciaConfiguracaoItem proxyItem = on(PedidoTransferenciaConfiguracaoItem.class);

        containerItens = new WebMarkupContainer("containerItens", modelItens = new CompoundPropertyModel<PedidoTransferenciaConfiguracaoItem>(new PedidoTransferenciaConfiguracaoItem()));
        containerItens.setOutputMarkupId(true);

        containerItens.add(txtDiaInicio = (LongField) new LongField(path(proxyItem.getDiaInicio())).setEnabled(!viewOnly));
        containerItens.add(txtDiaFim = (LongField) new LongField(path(proxyItem.getDiaFim())).setEnabled(!viewOnly));

        containerItens.add(getDropDownGrupo("grupo", viewOnly));
        containerItens.add(getDropDownSubGrupo("subGrupo", viewOnly));

        AbstractAjaxButton btnAdicionar;
        containerItens.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarItem(target);
            }
        });
        btnAdicionar.setEnabled(!viewOnly);


        tblItens = new Table("tblItens", getColumns(), getCollectionProvider());

        tblItens.populate();
        tblItens.setEnabled(!viewOnly);
        tblItens.setScrollY("1800");
        getForm().add(tblItens);


        getForm().add(containerItens);
        getForm().add(new AbstractAjaxLink("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                Page page = new ConsultaConfiguracaoPedidoPage();
                setResponsePage(page);
            }
        });
        SubmitButton btnSalvar;
        getForm().add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));
        btnSalvar.setVisible(!viewOnly);

        add(getForm());
    }

    private Form<ConfiguracaoPedidoTransferenciaDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new ConfiguracaoPedidoTransferenciaDTO(new PedidoTransferenciaConfiguracao())));
        }
        return form;
    }


    private void adicionarItem(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        PedidoTransferenciaConfiguracaoItem item = modelItens.getObject();

        if(getForm().getModel().getObject().getPedidoTransferenciaConfiguracao().getEmpresa() == null){
            throw new ValidacaoException(bundle("informeUnidade"));
        }

        if (item.getDiaInicio() == null) {
            throw new ValidacaoException(bundle("campoXObrigatorio", bundle("diaInicio")));
        }

        if (item.getDiaFim() == null) {
            throw new ValidacaoException(bundle("campoXObrigatorio", bundle("diaFim")));
        }

        if (item.getDiaInicio() <= 0) {
            throw new ValidacaoException(bundle("informeDiaInicioInvalido"));
        }
        if (item.getDiaFim() <= 0) {
            throw new ValidacaoException(bundle("informeDiaFimInvalido"));
        }

        if (item.getDiaFim() < item.getDiaInicio()) {
            throw new ValidacaoException(bundle("informeDiaFimMenor"));
        }

        if (item.getDiaInicio() > 31 || item.getDiaFim() > 31) {
            throw new ValidacaoException(bundle("informeDiaInvalido"));
        }


        if(grupo != null && subGrupo == null) {
            item.setTipo(PedidoTransferenciaConfiguracaoItem.Tipo.GRUPO.value());
        } else if(subGrupo != null) {
            item.setTipo(PedidoTransferenciaConfiguracaoItem.Tipo.SUBGRUPO.value());
        } else {
            item.setTipo(PedidoTransferenciaConfiguracaoItem.Tipo.DATA.value());
        }

        item.setGrupo(grupo);
        item.setSubGrupo(subGrupo);


        boolean existeDiaJaAdicionado = existeDiaAdicionado(item, getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem());

        if (existeDiaJaAdicionado) {
            throw new ValidacaoException(bundle("informeIntervaloDiasAdicionado"));
        }


        if (CollectionUtils.isEmpty(getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem())) {
            getForm().getModel().getObject().setListPedidoTransferenciaConfiguracaoItem(new ArrayList<PedidoTransferenciaConfiguracaoItem>());
        }
        getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem().add(item);

        limparItens(target);
        tblItens.update(target);
    }

    private boolean existeDiaAdicionado(PedidoTransferenciaConfiguracaoItem itemNovo, List<PedidoTransferenciaConfiguracaoItem> listPedidoTransferenciaConfiguracaoItem) {

        for (PedidoTransferenciaConfiguracaoItem configuracaoItem : listPedidoTransferenciaConfiguracaoItem) {
            if(itemNovo.getTipo().equals(configuracaoItem.getTipo())){
                boolean condicaoSubGrupo = true;
                boolean condicaoGrupo = true;
                if(itemNovo.getSubGrupo() != null) {
                    condicaoSubGrupo = itemNovo.getSubGrupo().getId().getCodigo().equals(configuracaoItem.getSubGrupo().getId().getCodigo());
                }
                if(itemNovo.getGrupo() != null) {
                    condicaoGrupo = itemNovo.getGrupo().getCodigo().equals(configuracaoItem.getGrupo().getCodigo());
                }
                if(condicaoGrupo && condicaoSubGrupo){
                    return  (itemNovo.getDiaInicio() >= configuracaoItem.getDiaInicio() && itemNovo.getDiaInicio() <= configuracaoItem.getDiaFim())
                            || (itemNovo.getDiaFim() >= configuracaoItem.getDiaInicio() && itemNovo.getDiaFim() <= configuracaoItem.getDiaFim());
                }
            }
        }
        return false;
    }

    private void limparItens(AjaxRequestTarget target) {
        modelItens.setObject(new PedidoTransferenciaConfiguracaoItem());
        dropDownGrupoProduto.setComponentValue(null);
        dropDownSubGrupo.setComponentValue(null);
        target.add(containerItens);
    }


    private DropDown<SubGrupo> getDropDownSubGrupo(String id, boolean viewOnly) {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>(id, new PropertyModel(this, "subGrupo"));
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
            dropDownSubGrupo.setEnabled(!viewOnly);
        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo(String id, boolean viewOnly) {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>(id, new PropertyModel(this, "grupo"));
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    } else {
                        dropDownSubGrupo.removeAllChoices();
                        dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.setEnabled(!viewOnly);
            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        PedidoTransferenciaConfiguracaoItem proxy = on(PedidoTransferenciaConfiguracaoItem.class);

        if(!viewOnly) {
            columns.add(getCustomColumn());
        }
        columns.add(createColumn(bundle("diaInicio"), proxy.getDiaInicio()));
        columns.add(createColumn(bundle("diaFim"), proxy.getDiaFim()));
        columns.add(createColumn(bundle("grupo"), proxy.getGrupo().getDescricao()));
        columns.add(createColumn(bundle("subGrupo"), proxy.getSubGrupo().getDescricao()));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<PedidoTransferenciaConfiguracaoItem>() {
            @Override
            public Component getComponent(String componentId, final PedidoTransferenciaConfiguracaoItem rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject);
                    }
                };
            }
        };
    }

    private void remover(AjaxRequestTarget target, PedidoTransferenciaConfiguracaoItem rowObject) {
        for (int i = 0; i < getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem().size(); i++) {
            if (getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem().get(i) == rowObject) {
                getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem().remove(i);
                break;
            }
        }
        tblItens.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem();
            }
        };
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(getForm().getModel().getObject().getListPedidoTransferenciaConfiguracaoItem())) {
            throw new ValidacaoException(bundle("adicionePeloMenosUmItem"));
        }

        BOFactoryWicket.getBO(MaterialBasicoFacade.class).salvarPedidoTransferenciaConfiguracao(getForm().getModel().getObject());

        Page page = new ConsultaConfiguracaoPedidoPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroConfiguracaoPedAlmoxarifado");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }
}
