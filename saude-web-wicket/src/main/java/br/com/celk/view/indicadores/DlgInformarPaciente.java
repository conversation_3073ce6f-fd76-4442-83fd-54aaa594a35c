package br.com.celk.view.indicadores;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarPaciente extends Window{

    private PnlInformarPaciente pnlInformarPaciente;
    
    public DlgInformarPaciente(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setContent(pnlInformarPaciente = new PnlInformarPaciente(getContentId()) {

            @Override
            public void onOk(AjaxRequestTarget target, UsuarioCadsus paciente) throws ValidacaoException, DAOException {
                DlgInformarPaciente.this.onOk(target, paciente);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
        
        setInitialHeight(70);
        setInitialWidth(600);
        setTitle(BundleManager.getString("selecionePaciente"));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlInformarPaciente.getAutoCompleteConsultaUsuarioCadsus().getTxtDescricao().getTextField();
    }

    public abstract void onOk(AjaxRequestTarget target, UsuarioCadsus paciente) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        pnlInformarPaciente.getAutoCompleteConsultaUsuarioCadsus().limpar(target);
        super.show(target);
    }

}
