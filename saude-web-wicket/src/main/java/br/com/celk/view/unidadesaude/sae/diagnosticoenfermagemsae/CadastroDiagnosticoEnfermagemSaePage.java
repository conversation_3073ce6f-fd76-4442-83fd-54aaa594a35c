package br.com.celk.view.unidadesaude.sae.diagnosticoenfermagemsae;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.agendamento.dialog.DlgIntervencoesSugeridasSAE;
import br.com.celk.view.prontuario.basico.autocompleteresultadoesperado.AutoCompleteResultadoEsperado;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.sae.dto.EloDiagnosticoEnfermagemSaeResultadoEsperadoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ResultadoEsperado;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.EloDiagEnfSaeResEsperIntervEnf;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.EloDiagnosticoEnfermagemSaeResultadoEsperado;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.RequiredTextField;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

public class CadastroDiagnosticoEnfermagemSaePage extends CadastroPage<DiagnosticoEnfermagemSae> {

    private boolean viewOnly;
    private DiagnosticoEnfermagemSae diagnosticoEnfermagemSae;
    private AutoCompleteResultadoEsperado autoCompleteResultadoEsperado;
    private ResultadoEsperado resultadoEsperado;
    private Table<EloDiagnosticoEnfermagemSaeResultadoEsperado> tblElos;
    private List<EloDiagnosticoEnfermagemSaeResultadoEsperado> eloDiagnosticoEnfermagemSaeResultadoEsperados;
    private List<EloDiagEnfSaeResEsperIntervEnf> eloDiagEnfSaeResEsperIntervEnfs;


    public CadastroDiagnosticoEnfermagemSaePage(DiagnosticoEnfermagemSae diagnosticoEnfermagemSae, boolean viewOnly) {
        super(diagnosticoEnfermagemSae, viewOnly);
        this.viewOnly = viewOnly;
        this.diagnosticoEnfermagemSae = diagnosticoEnfermagemSae;
        loadEloDiagnosticoEnfermagemSaeResultadoEsperados();
        loadEloDiagEnfSaeResEsperIntervEnfs();
    }

    public CadastroDiagnosticoEnfermagemSaePage(DiagnosticoEnfermagemSae diagnosticoEnfermagemSae) {
        this(diagnosticoEnfermagemSae, false);
    }

    public CadastroDiagnosticoEnfermagemSaePage() {
    }

    @Override
    public void init(Form<DiagnosticoEnfermagemSae> form) {
        eloDiagnosticoEnfermagemSaeResultadoEsperados = new ArrayList<>();
        eloDiagEnfSaeResEsperIntervEnfs = new ArrayList<>();
        addCampos(form);
        form.add(getBotaoAdicionar());
        addTabelaElos(form);

    }

    private void addTabelaElos(Form<DiagnosticoEnfermagemSae> form) {
        tblElos = new Table("tblElos", getColumns(), getCollectionProvider());
        form.add(tblElos);
        tblElos.populate();
    }

    private void addCampos(Form<DiagnosticoEnfermagemSae> form) {
        DiagnosticoEnfermagemSae diagnostico = on(DiagnosticoEnfermagemSae.class);

        form.add(new RequiredTextField<>(path(diagnostico.getDescricao())));
        form.add(new RequiredTextField<>(path(diagnostico.getReferencia())));
        form.add(new TextField<>(path(diagnostico.getEixoFoco())));
        form.add(new TextField<>(path(diagnostico.getEixoAcao())));
        form.add(new TextField<>(path(diagnostico.getEixoCliente())));
        form.add(new TextField<>(path(diagnostico.getEixoMeios())));
        form.add(new TextField<>(path(diagnostico.getEixoTempo())));
        form.add(new TextField<>(path(diagnostico.getEixoLocalizacao())));
        autoCompleteResultadoEsperado = new AutoCompleteResultadoEsperado("resultadoEsperado", new PropertyModel(this, "resultadoEsperado"));
        form.add(autoCompleteResultadoEsperado);
    }

    private AbstractAjaxButton getBotaoAdicionar() {
        AbstractAjaxButton btnAdicionar;

        btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarAdicaoElo();
                addElo(target);
            }
        };
        btnAdicionar.setEnabled(!viewOnly);

        return btnAdicionar;
    }

    private void addElo(AjaxRequestTarget target) {
        EloDiagnosticoEnfermagemSaeResultadoEsperado elo = new EloDiagnosticoEnfermagemSaeResultadoEsperado();
        elo.setResultado(resultadoEsperado);

        eloDiagnosticoEnfermagemSaeResultadoEsperados.add(elo);
        autoCompleteResultadoEsperado.limpar(target);
        tblElos.update(target);
    }

    private void validarAdicaoElo() throws ValidacaoException {
        if (resultadoEsperado == null) {
            throw new ValidacaoException(bundle("msgInformeResultado"));
        }

        if (Lambda.exists(eloDiagnosticoEnfermagemSaeResultadoEsperados, having(on(EloDiagnosticoEnfermagemSaeResultadoEsperadoDTO.class).getResultadoEsperado().getCodigo(), equalTo(resultadoEsperado.getCodigo())))) {
            throw new ValidacaoException(bundle("msgResultadoJaAdicionada"));
        }
    }

    @Override
    public Object salvar(DiagnosticoEnfermagemSae diagnosticoEnfermagemSae) throws DAOException, ValidacaoException {
        this.validarReferenciaDuplicada(diagnosticoEnfermagemSae);
        if (!isEdicao()) {
            diagnosticoEnfermagemSae.setDataCadastro(DataUtil.getDataAtual());
            diagnosticoEnfermagemSae.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());
            diagnosticoEnfermagemSae.setStatus(DiagnosticoEnfermagemSae.Status.ATIVO.value());
        }
        return BOFactoryWicket.getBO(AtendimentoFacade.class).salvarDiagnostivoEnfermagem(
            diagnosticoEnfermagemSae,
            eloDiagnosticoEnfermagemSaeResultadoEsperados,
            eloDiagEnfSaeResEsperIntervEnfs
        );
    }

    @Override
    public Class<DiagnosticoEnfermagemSae> getReferenceClass() {
        return DiagnosticoEnfermagemSae.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaDiagnosticoEnfermagemSaePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroDiagnosticoEnfermagemSae");
    }


    private void validarReferenciaDuplicada(DiagnosticoEnfermagemSae diagnosticoEnfermagemSae) throws ValidacaoException {
        DiagnosticoEnfermagemSae diagnostico = on(DiagnosticoEnfermagemSae.class);
        boolean existsReferenciaDuplicada = LoadManager.getInstance(DiagnosticoEnfermagemSae.class)
                                                       .addParameter(new QueryCustom.QueryCustomParameter(path(diagnostico.getCodigo()), BuilderQueryCustom.QueryParameter.DIFERENTE, diagnosticoEnfermagemSae.getCodigo()))
                                                       .addParameter(new QueryCustom.QueryCustomParameter(path(diagnostico.getReferencia()), BuilderQueryCustom.QueryParameter.IGUAL, diagnosticoEnfermagemSae.getReferencia()))
                                                       .exists();
        if (existsReferenciaDuplicada) {
            throw new ValidacaoException(bundle("msgDiagnosticoEnfermagemSaeReferencia"));
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return eloDiagnosticoEnfermagemSaeResultadoEsperados;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        EloDiagnosticoEnfermagemSaeResultadoEsperado eloDiagnosticoEnfermagemSaeResultadoEsperado = on(EloDiagnosticoEnfermagemSaeResultadoEsperado.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("descricao"), eloDiagnosticoEnfermagemSaeResultadoEsperado.getResultado().getDescricao()));
        columns.add(createColumn(bundle("referencia"), eloDiagnosticoEnfermagemSaeResultadoEsperado.getResultado().getReferencia()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EloDiagnosticoEnfermagemSaeResultadoEsperado>() {
            @Override
            public void customizeColumn(final EloDiagnosticoEnfermagemSaeResultadoEsperado rowObject) {
                addAction(ActionType.VINCULAR, rowObject, new IModelAction<EloDiagnosticoEnfermagemSaeResultadoEsperado>() {
                    @Override
                    public void action(AjaxRequestTarget target, EloDiagnosticoEnfermagemSaeResultadoEsperado eloDiagnosticoEnfermagemSaeResultadoEsperado) throws ValidacaoException, DAOException {
                        abirModalIntervencao(target, eloDiagnosticoEnfermagemSaeResultadoEsperado);
                    }
                })
                    .setTitleBundleKey("vincularIntervencao")
                    .setIcon(Icon.DOC_EDIT);

                addAction(ActionType.REMOVER, rowObject, new IModelAction<EloDiagnosticoEnfermagemSaeResultadoEsperado>() {
                    @Override
                    public void action(AjaxRequestTarget target, EloDiagnosticoEnfermagemSaeResultadoEsperado modelObject) throws ValidacaoException, DAOException {
                        removeEloLista(target, modelObject);
                    }
                });
            }
        };
    }

    private void abirModalIntervencao(AjaxRequestTarget target, EloDiagnosticoEnfermagemSaeResultadoEsperado eloDiagnosticoEnfermagemSaeResultadoEsperado) {
        DlgIntervencoesSugeridasSAE dlgIntervencoesSugeridasSAE = new DlgIntervencoesSugeridasSAE(newModalId(), eloDiagnosticoEnfermagemSaeResultadoEsperado, eloDiagEnfSaeResEsperIntervEnfs);
        addModal(target, dlgIntervencoesSugeridasSAE);
        dlgIntervencoesSugeridasSAE.show(target);
    }

    private void removeEloLista(AjaxRequestTarget target, EloDiagnosticoEnfermagemSaeResultadoEsperado modelObject) {
        eloDiagnosticoEnfermagemSaeResultadoEsperados.remove(modelObject);
        eloDiagEnfSaeResEsperIntervEnfs = Lambda.filter(Lambda.having(Lambda.on(EloDiagEnfSaeResEsperIntervEnf.class).getResultado().getCodigo(), Matchers.not(modelObject.getResultado().getCodigo())), eloDiagEnfSaeResEsperIntervEnfs);

        target.add(tblElos);
    }

    private void loadEloDiagnosticoEnfermagemSaeResultadoEsperados() {
        if (diagnosticoEnfermagemSae != null && diagnosticoEnfermagemSae.getCodigo() != null) {
            EloDiagnosticoEnfermagemSaeResultadoEsperado eloDiagnosticoEnfermagemSaeResultadoEsperado = on(EloDiagnosticoEnfermagemSaeResultadoEsperado.class);

            eloDiagnosticoEnfermagemSaeResultadoEsperados = LoadManager.getInstance(EloDiagnosticoEnfermagemSaeResultadoEsperado.class)
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getCodigo()))
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getVersion()))
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getResultado().getCodigo()))
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getResultado().getDescricao()))
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getResultado().getReferencia()))
                    .addProperty(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getDiagnostico().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(eloDiagnosticoEnfermagemSaeResultadoEsperado.getDiagnostico().getCodigo()), diagnosticoEnfermagemSae.getCodigo()))
                    .start().getList();
        }
    }

    private void loadEloDiagEnfSaeResEsperIntervEnfs() {
        if (diagnosticoEnfermagemSae != null && diagnosticoEnfermagemSae.getCodigo() != null) {
            EloDiagEnfSaeResEsperIntervEnf eloDiagEnfSaeResEsperIntervEnf = on(EloDiagEnfSaeResEsperIntervEnf.class);

            eloDiagEnfSaeResEsperIntervEnfs = LoadManager.getInstance(EloDiagEnfSaeResEsperIntervEnf.class)
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getCodigo()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getVersion()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getResultado().getCodigo()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getResultado().getDescricao()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getResultado().getReferencia()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getIntervencaoEnfermagem().getCodigo()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getIntervencaoEnfermagem().getDescricao()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getIntervencaoEnfermagem().getReferencia()))
                    .addProperty(path(eloDiagEnfSaeResEsperIntervEnf.getDiagnostico().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(eloDiagEnfSaeResEsperIntervEnf.getDiagnostico().getCodigo()), diagnosticoEnfermagemSae.getCodigo()))
                    .start().getList();
        }
    }
}
