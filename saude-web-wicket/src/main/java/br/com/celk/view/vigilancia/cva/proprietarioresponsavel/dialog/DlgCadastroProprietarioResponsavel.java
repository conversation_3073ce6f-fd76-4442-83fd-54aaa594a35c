package br.com.celk.view.vigilancia.cva.proprietarioresponsavel.dialog;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.panel.PnlCadastro;
import br.com.celk.template.cadastro.window.CadastroWindow;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.cva.proprietarioresponsavel.CvaProprietarioResponsavel;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public class DlgCadastroProprietarioResponsavel extends CadastroWindow<CvaProprietarioResponsavel> {

    private PnlCadastroProprietarioResponsavel pnlCadastro;

    public DlgCadastroProprietarioResponsavel(String id) {
        super(id);
        init();
    }

    public DlgCadastroProprietarioResponsavel(String id, AjaxRequestTarget target) {
        super(id);
        init();
        limpar(target);
    }

    private void init() {
        setOutputMarkupId(true);
        setInitialWidth(880);
        setInitialHeight(585);
        setResizable(false);
    }

    @Override
    public PnlCadastro<CvaProprietarioResponsavel> getPnlCadastro() {
        if (this.pnlCadastro == null) {
            this.pnlCadastro = new PnlCadastroProprietarioResponsavel(getContentId());
        }
        return this.pnlCadastro;
    }

    @Override
    public String getWindowTitle() {
        return BundleManager.getString("cadastroProprietarioResponsavel");
    }

    @Override
    public CvaProprietarioResponsavel salvar(CvaProprietarioResponsavel object) throws DAOException, ValidacaoException {
        //Validações movidas para o SaveCvaProprietarioResponsavel

        return BOFactoryWicket.save(object);
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        super.limpar(target);
        pnlCadastro.limpar(target);
    }

    @Override
    public void show(AjaxRequestTarget target) {
        limpar(target);
        super.show(target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlCadastro.getFocusComponent();
    }

}
