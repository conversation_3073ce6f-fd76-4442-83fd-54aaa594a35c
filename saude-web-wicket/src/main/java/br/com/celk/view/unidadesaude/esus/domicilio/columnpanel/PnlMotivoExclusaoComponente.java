package br.com.celk.view.unidadesaude.esus.domicilio.columnpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.DataUtil;
import br.com.celk.view.cadsus.usuariocadsus.CadastroUsuarioCidadaoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDomicilio;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoExclusaoComponente extends Panel {

    private AbstractAjaxButton btnFechar;
    private DropDown cbxMotivo;
    private Long motivo = -1L;
    private WebMarkupContainer containerObito;
    private DateChooser dchDataObito;
    private Date dtObito;
    private InputField txtNumeroDO;
    private String numeroDo;

    public static final Long OBITO = 3L;


    public PnlMotivoExclusaoComponente(String id) {
        super(id);
        init();
    }
//todo danubio
    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(cbxMotivo = DropDownUtil.getIEnumDropDown("motivo", UsuarioCadsusDomicilio.Motivo.values(), true, true));
        cbxMotivo.addAjaxUpdateValue();

        cbxMotivo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                boolean showObito = OBITO.equals(motivo);

                containerObito.setVisible(showObito);
                dchDataObito.setRequired(showObito);
                if (showObito) {
                    dchDataObito.addRequiredClass();
                } else {
                    if (ajaxRequestTarget != null) {
                        dchDataObito.limpar(ajaxRequestTarget);
                        txtNumeroDO.limpar(ajaxRequestTarget);
                    }
                    dchDataObito.removeRequiredClass();
                }
                if (ajaxRequestTarget != null) {
                    ajaxRequestTarget.add(containerObito);
                    ajaxRequestTarget.add(dchDataObito);
                    ajaxRequestTarget.add(txtNumeroDO);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, motivo, dtObito, numeroDo);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });
        containerObito = new WebMarkupContainer("containerObito");
        containerObito.setOutputMarkupPlaceholderTag(true);
        containerObito.setVisible(UsuarioCadsus.MotivoExclusao.OBITO.value().equals(motivo));

        dchDataObito = new DateChooser("dtObito");
        dchDataObito.setLabel(Model.of(bundle("dataObito")));
        dchDataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        containerObito.add(dchDataObito);

        txtNumeroDO = new InputField("numeroDo");
        txtNumeroDO.setLabel(Model.of(bundle("numeroDO")));
        containerObito.add(txtNumeroDO);

        form.add(containerObito);
        add(form);

        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long motivo, Date dtObito, String numeroDo) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.cbxMotivo.limpar(target);
    }

    private void validarTamanhoNumeroDo(String numeroDO) throws ValidacaoException {
        // VALIDAÇÃO TAMANHO NÚMERO DO SE MOTIVO FOR ÓBITO E NÚMERO FOR PREENCHIDO
        if (UsuarioCadsus.MotivoExclusao.OBITO.value().equals(motivo) && numeroDO.trim().length() != UsuarioCadsusHelper.TAMANHO_OBRIGATORIO_NUMERO_DO) {
            throw new ValidacaoException(bundle("msgNumeroDoInvalido"));
        }
    }
}
