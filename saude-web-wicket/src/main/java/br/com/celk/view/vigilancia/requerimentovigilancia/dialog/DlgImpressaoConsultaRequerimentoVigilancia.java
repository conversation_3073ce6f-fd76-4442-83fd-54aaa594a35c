package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgImpressaoConsultaRequerimentoVigilancia extends Window {

    private PnlImpressaoConsultaRequerimentoVigilancia pnlImpressaoConsultaRequerimentoVigilancia;

    public DlgImpressaoConsultaRequerimentoVigilancia(String id) {
        this(id, false);
    }

    public DlgImpressaoConsultaRequerimentoVigilancia(String id, Boolean isPaginaFiscal) {
        super(id);
        init(isPaginaFiscal, false);
        pnlImpressaoConsultaRequerimentoVigilancia.setIsPaginaFiscal(isPaginaFiscal);
        pnlImpressaoConsultaRequerimentoVigilancia.setAmbienteExterno(false);
    }

    public DlgImpressaoConsultaRequerimentoVigilancia(String id, Boolean isPaginaFiscal, Boolean isAmbienteExterno) {
        super(id);
        init(isPaginaFiscal, isAmbienteExterno);
        pnlImpressaoConsultaRequerimentoVigilancia.setIsPaginaFiscal(isPaginaFiscal);
        pnlImpressaoConsultaRequerimentoVigilancia.setAmbienteExterno(isAmbienteExterno);
    }

    private void init(boolean isPaginaFiscal, boolean isAmbienteExterno) {
        setTitle(getDialogTitle());

        setInitialHeight(85);
        setInitialWidth(500);
        setResizable(false);

        setContent(pnlImpressaoConsultaRequerimentoVigilancia = new PnlImpressaoConsultaRequerimentoVigilancia(getContentId(), isPaginaFiscal, isAmbienteExterno) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgImpressaoConsultaRequerimentoVigilancia.this.onFechar(target);
            }

            @Override
            public DataReport onImprimir(RequerimentoVigilancia requerimentoVigilancia) throws ReportException, ValidacaoException {
                return DlgImpressaoConsultaRequerimentoVigilancia.this.onImprimir(requerimentoVigilancia);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget art) {
                try {
                    onFechar(art);
                } catch (ValidacaoException | DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return true;
            }
        });
    }

    public void show(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        super.show(target);
        pnlImpressaoConsultaRequerimentoVigilancia.setObject(target, requerimentoVigilancia);
    }

    public abstract DataReport onImprimir(RequerimentoVigilancia requerimentoVigilancia) throws ReportException, ValidacaoException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getDialogTitle() {
        return BundleManager.getString("impressoes");
    }
}
