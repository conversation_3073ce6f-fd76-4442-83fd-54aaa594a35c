package br.com.celk.view.unidadesaude.esus.relatorios;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusReportFacade;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.esus.relatorios.RelatorioAcompanhamentoDTOParam;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.siab.relatorios.RelatorioAcompanhamentoCadastroFamiliasPage;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioAcompanhamentoPage extends RelatorioPage<RelatorioAcompanhamentoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;

    @Override
    public void init(Form<RelatorioAcompanhamentoDTOParam> form) {
        RelatorioAcompanhamentoDTOParam proxy = on(RelatorioAcompanhamentoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
        form.add(dropDownArea = new DropDown(path(proxy.getEquipeArea())));
        form.add(dropDownMicroArea = new DropDown(path(proxy.getEquipeMicroArea())));

        initDropDownArea();
    }

    private void initDropDownArea() {
        dropDownArea.removeAllChoices();
        dropDownArea.addAjaxUpdateValue();
        dropDownArea.addChoice(null, BundleManager.getString("todos"));

        LoadManager loadManager = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO));

        final List<Long> empresas = new ArrayList();
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            empresas.add(((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getCodigo());
        } else {
            if (!isActionPermitted(Permissions.EMPRESA)) {
                try {
                    empresas.addAll(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario()));
                } catch (SGKException ex) {
                    Logger.getLogger(RelatorioAcompanhamentoCadastroFamiliasPage.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }

        if (!empresas.isEmpty()) {
            loadManager.addInterceptor(new LoadInterceptor() {
                @Override
                public void customHQL(HQLHelper hql, String alias) {
                    HQLHelper exists = hql.getNewInstanceSubQuery();
                    exists.addToSelect("1");
                    exists.addToFrom("EquipeMicroArea ema JOIN ema.equipeProfissional ep JOIN ep.equipe e JOIN e.empresa emp");
                    exists.addToWhereWhithAnd("ema.equipeArea.codigo = " + alias + ".codigo");
                    exists.addToWhereWhithAnd("emp.codigo in ", empresas);
                    hql.addToWhereWhithAnd("exists(" + exists.getQuery() + ")");
                }
            });
        }

        List<EquipeArea> list = loadManager.start().getList();

        for (EquipeArea equipeArea : list) {
            dropDownArea.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDownArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateDropDownEquipeMicroArea(target);
            }
        });
    }

    private void updateDropDownEquipeMicroArea(AjaxRequestTarget target) {
        dropDownMicroArea.removeAllChoices();

        EquipeArea equipeArea = dropDownArea.getComponentValue();
        if (equipeArea != null) {
            List<EquipeMicroArea> equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                    .addProperty(EquipeMicroArea.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                    .start().getList();

            if (equipeMicroAreas.isEmpty()) {
                dropDownMicroArea.addChoice(null, "");
                dropDownMicroArea.setEnabled(false);
            } else {
                dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
                dropDownMicroArea.setEnabled(true);
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }
            }
        } else {
            dropDownMicroArea.addChoice(null, "");
            dropDownMicroArea.setEnabled(false);
        }

        dropDownMicroArea.limpar(target);
    }

    @Override
    public Class<RelatorioAcompanhamentoDTOParam> getDTOParamClass() {
        return RelatorioAcompanhamentoDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAcompanhamentoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EsusReportFacade.class).relatorioAcompanhamento(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioAcompanhamento");
    }

}
