package br.com.celk.view.hospital.dadoscomplementares;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class DadosComplementaresPage extends BasePage {

    private CompoundPropertyModel<DadosComplementares> model;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private DropDown dropDownTipoDado;
    private List<DadosComplementares> dadosComplementaresList;
    private Table tblDadosComplementares;

    public DadosComplementaresPage() {
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new DadosComplementares()));

        DadosComplementares proxy = on(DadosComplementares.class);

        form.add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(path(proxy.getProcedimento())));
        form.add(dropDownTipoDado = DropDownUtil.getIEnumDropDown(path(proxy.getTipoDado()), DadosComplementares.TipoDado.values(), true));
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(tblDadosComplementares = new Table("table", getColumns(), getCollectionProvider()));
        tblDadosComplementares.populate();

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(HospitalFacade.class).salvarDadosComplementares(dadosComplementaresList);
                DadosComplementaresPage.this.info(target, bundle("msgRegistrosSalvosSucesso"));
            }
        });

        add(form);
        carregarLista();
    }

    private List<IColumn> getColumns() {
        DadosComplementares proxy = on(DadosComplementares.class);

        List<IColumn> columns = new ArrayList();
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimento().getDescricao()));
        columns.add(createColumn(bundle("tipoDado"), proxy.getDescricaoTipoDado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<DadosComplementares>() {
            @Override
            public void customizeColumn(DadosComplementares rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<DadosComplementares>() {
                    @Override
                    public void action(AjaxRequestTarget target, DadosComplementares modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<DadosComplementares>() {
                    @Override
                    public void action(AjaxRequestTarget target, DadosComplementares modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dadosComplementaresList;
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        DadosComplementares dadosComplementares = model.getObject();

        if (dadosComplementares.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        }
        if (dadosComplementares.getTipoDado() == null) {
            throw new ValidacaoException(bundle("msgInformeTipoDado"));
        }

        boolean adicionar = true;
        for (DadosComplementares item : dadosComplementaresList) {
            if (item == dadosComplementares) {
                adicionar = false;
            } else if (item.getProcedimento().equals(dadosComplementares.getProcedimento()) && item.getTipoDado().equals(dadosComplementares.getTipoDado())) {
                throw new ValidacaoException(bundle("msgExisteDadoComplementarMesmoProcedimentoTipoDado"));
            }
        }

        if (adicionar) {
            dadosComplementaresList.add(dadosComplementares);
        }

        tblDadosComplementares.update(target);
        limpar(target);
        target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaProcedimento.getTxtDescricao().getTextField()));
    }

    private void editar(AjaxRequestTarget target, DadosComplementares dadosComplementares) {
        limpar(target);
        model.setObject(dadosComplementares);
        target.add(autoCompleteConsultaProcedimento);
        target.add(dropDownTipoDado);
    }

    private void remover(AjaxRequestTarget target, DadosComplementares dadosComplementares) {
        for (int i = 0; i < dadosComplementaresList.size(); i++) {
            if (dadosComplementaresList.get(i) == dadosComplementares) {
                dadosComplementaresList.remove(i);
                break;
            }
        }

        tblDadosComplementares.update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        model.setObject(new DadosComplementares());
        autoCompleteConsultaProcedimento.limpar(target);
        dropDownTipoDado.limpar(target);
    }

    private void carregarLista() {
        dadosComplementaresList = LoadManager.getInstance(DadosComplementares.class).addProperties(new HQLProperties(DadosComplementares.class).getProperties()).start().getList();
    }

    @Override
    public String getTituloPrograma() {
        return bundle("dadosComplementares");
    }
}
