package br.com.celk.view.controle.importacaoFpo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.controle.importacaoFpo.customcolumn.ArquivoFpoColumnPanel;
import br.com.celk.view.controle.importacaoFpo.customcolumn.SituacaoArquivoFpoColumnPanel;
import br.com.celk.view.controle.importacaoFpo.dialog.DlgImportarFpo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.ArquivoFpo;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;

@Private
public class ImportacaoFpoPage extends BasePage implements IAsyncProcessNotification {

    private Form form;
    private MesAnoField txtCompetencia;
    private Date competencia;
    private AbstractAjaxButton btnImportarFpo;
    private ProcurarButton btnProcurar;
    private PageableTable tblImportacoes;
    private DlgImportarFpo dlgImportarFpo;

    public ImportacaoFpoPage() throws DAOException, ParseException {
        init();
    }

    public void init() throws DAOException, ParseException {
        form = new Form("form", new CompoundPropertyModel(this));

        txtCompetencia = new MesAnoField("competencia");

        tblImportacoes = new PageableTable("tblImportacoes", getColumns(), getPagerProviderInstance());
        tblImportacoes.getDataProvider().setParameters(getParameters());
        tblImportacoes.populate();

        btnProcurar = new ProcurarButton("btnProcurar", tblImportacoes) {
            @Override
            public Object getParam() {
                return getParameters();
            }
        };

        btnImportarFpo = new AbstractAjaxButton("btnImportarFpo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                initDlgImportarFpo(target);
            }
        };

        form.add(txtCompetencia, tblImportacoes, btnProcurar, btnImportarFpo);

        add(form);
    }

    public void initDlgImportarFpo(AjaxRequestTarget target) {
        addModal(target, dlgImportarFpo = new DlgImportarFpo(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                tblImportacoes.update(target);
                target.add(tblImportacoes);
            }
        });

        dlgImportarFpo.show(target);
        tblImportacoes.update(target);
    }


    public List<ISortableColumn<ArquivoFpo>> getColumns() throws ParseException {
        List<ISortableColumn<ArquivoFpo>> columns = new ArrayList<ISortableColumn<ArquivoFpo>>();

        ArquivoFpo on = Lambda.on(ArquivoFpo.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("mes"), on.getMesCompetencia()));
        columns.add(createColumn(bundle("ano"), on.getAnoCompetencia()));
        columns.add(new DateTimeColumn<ArquivoFpo>(bundle("dataGeracao"), path(on.getDataGeracao()), path(on.getDataGeracao())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createColumn(bundle("situacao"), on.getDescricaoSituacao()));
        columns.add(getCustomColumnSituacao());

        return columns;
    }

    public CustomColumn getCustomColumn() {
        return new CustomColumn<ArquivoFpo>() {

            @Override
            public Component getComponent(String componentId, ArquivoFpo rowObject) {

                return new ArquivoFpoColumnPanel(componentId, rowObject) {
                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        tblImportacoes.update(target);
                        target.add(tblImportacoes);
                    }
                };
            }
        };
    }

    public CustomColumn getCustomColumnSituacao() {
        return new CustomColumn<ArquivoFpo>() {

            @Override
            public Component getComponent(String componentId, ArquivoFpo rowObject) {
                return new SituacaoArquivoFpoColumnPanel(componentId, rowObject);
            }
        };
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ArquivoFpo.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ArquivoFpo.class).getProperties(),
                        new HQLProperties(GerenciadorArquivo.class, ArquivoFpo.PROP_GERENCIADOR_ARQUIVO).getProperties(),
                        new HQLProperties(AsyncProcess.class, ArquivoFpo.PROP_ASYNC_PROCESS).getProperties()
                );
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ArquivoFpo.PROP_DATA_GERACAO), false);
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (competencia != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(ArquivoFpo.PROP_COMPETENCIA, competencia));
        }

        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("importacaoFpo");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtCompetencia;
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        tblImportacoes.update(target);
    }

}