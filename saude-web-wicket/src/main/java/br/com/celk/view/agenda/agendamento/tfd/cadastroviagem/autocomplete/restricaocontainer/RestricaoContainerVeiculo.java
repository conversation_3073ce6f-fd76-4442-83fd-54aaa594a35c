package br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.frota.interfaces.dto.QueryConsultaVeiculoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerVeiculo extends Panel implements IRestricaoContainer<QueryConsultaVeiculoDTOParam> {

    private final InputField<String> txtDescricao;
    private final InputField<String> txtPlaca;
    
    private final QueryConsultaVeiculoDTOParam param = new QueryConsultaVeiculoDTOParam();
    
    public RestricaoContainerVeiculo(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("descricao"));
        root.add(txtPlaca = new InputField<String>("placa"));
        
        add(root);
    }

    @Override
    public QueryConsultaVeiculoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
        txtPlaca.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
