package br.com.celk.view.agenda.agendamento.leito;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.dto.AihAnexosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.Collection;

public abstract class DlgReenviarAih extends Window {

    private PnlReenviarAih pnlReenviarAih;

    public DlgReenviarAih(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("reenviarAih");
            }
        });

        setInitialWidth(550);
        setInitialHeight(250);

        setResizable(false);

        setContent(pnlReenviarAih = new PnlReenviarAih(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Aih aih, String motivoReenvio, final Collection<FileUpload> uploads) throws ValidacaoException, DAOException {
                onFechar(target);
                DlgReenviarAih.this.onConfirmar(target, aih, motivoReenvio, uploads);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlReenviarAih.getTxaMotivo();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih, String motivoReenvio, final Collection<FileUpload> uploads) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, Aih aih) {
        show(target);
        pnlReenviarAih.setAih(target, aih);
    }

    private void fechar(AjaxRequestTarget target) {
        //pnlReenviarAih.limpar(target);
        close(target);
    }
}
