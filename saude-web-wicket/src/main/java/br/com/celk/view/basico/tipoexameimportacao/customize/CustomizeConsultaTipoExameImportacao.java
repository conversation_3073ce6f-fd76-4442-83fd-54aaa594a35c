package br.com.celk.view.basico.tipoexameimportacao.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExameImportacao;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaTipoExameImportacao extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoExameImportacao.PROP_DESCRICAO)));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(TipoExameImportacao.PROP_CODIGO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(TipoExameImportacao.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return TipoExameImportacao.class;
    }

}
