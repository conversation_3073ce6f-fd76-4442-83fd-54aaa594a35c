package br.com.celk.view.vigilancia.profissional;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.celk.view.vigilancia.profissional.columns.DownloadAnexoProfissionalColumnPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoExumacaoPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroProfissionalEnderecoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroProfissionalVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaProfissionalAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissionalAnexo;
import br.com.ksisolucoes.vo.vigilancia.endereco.ProfissionalEndereco;
import br.com.ksisolucoes.vo.vigilancia.endereco.TipoEnderecoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaA;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaB;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaTalidomida;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoReceitaTalidomida;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroProfissionalVigilanciaPage extends BasePage {

    private Form<CadastroProfissionalVigilanciaDTO> form;
    private VigilanciaProfissional vigilanciaProfissional;
    private InputField txtNomeProfissional;
    private InputField txtDescricaoAnexo;
    private List<VigilanciaProfissionalAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private WebMarkupContainer containerVigilanciaProfissionalAnexo;
    private Table tblVigilanciaProfissionalAnexo;
    private FileUploadField fileUploadField;
    private FileUpload upload;
    private List<FileUpload> lstUpload;
    private CompoundPropertyModel<VigilanciaProfissionalAnexoDTO> modelVigilanciaProfissionalAnexoDTO;
    private PnlVigilanciaEndereco pnlVigilanciaEndereco;
    private DropDown cbxEstado;
    private boolean enabled;
    private WebMarkupContainer containerEndereco;
    private CompoundPropertyModel<CadastroProfissionalEnderecoDTO> modelProfissionalEndereco;
    private Table tblProfissionalEndereco;
    private List<CadastroProfissionalEnderecoDTO> profissionalEnderecoList;
    private DropDown<TipoEnderecoVigilancia> dropDownEnderecoVigilancia;
    private DropDown<Long> dropDownSolicitaReceituario;
    private CadastroProfissionalEnderecoDTO dtoEnderecoEdicao;
    private DateChooser dchDataEmissao;

    private DlgImpressaoObjectMulti<CadastroProfissionalVigilanciaDTO> dlgConfirmacaoImpressao;

    private UpperField txtCpf;
    private UpperField txtRg;
    private MultiLineLabel messageLabel;
    private WebMarkupContainer containerDadosEdicao;


    public CadastroProfissionalVigilanciaPage() {
        init(true);
    }

    public CadastroProfissionalVigilanciaPage(VigilanciaProfissional vigilanciaProfissional) {
        this.vigilanciaProfissional = vigilanciaProfissional;
        carregarAnexos(vigilanciaProfissional);
        carregarEndereco(vigilanciaProfissional);
        init(true);
    }

    public CadastroProfissionalVigilanciaPage(VigilanciaProfissional vigilanciaProfissional, boolean viewOnly) {
        this.vigilanciaProfissional = vigilanciaProfissional;
        carregarAnexos(vigilanciaProfissional);
        carregarEndereco(vigilanciaProfissional);
        init(false);
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        CadastroProfissionalVigilanciaDTO proxy = on(CadastroProfissionalVigilanciaDTO.class);

        addContainerDadosEdicao(getForm(), proxy);
        getForm().add(txtNomeProfissional = (InputField) new RequiredInputField(path(proxy.getVigilanciaProfissional().getNomeProfissional())).setLabel(new Model(bundle("nomeProfissional"))).setEnabled(enabled));
        getForm().add(new InputField(path(proxy.getVigilanciaProfissional().getNomeDiretor())).setLabel(new Model(bundle("nomeDiretor"))).setEnabled(enabled));
        getForm().add(new AutoCompleteConsultaTabelaCbo(path(proxy.getVigilanciaProfissional().getCbo())).setLabel(new Model(bundle("especialidade"))).setEnabled(enabled));
        getForm().add(new RequiredInputField(path(proxy.getVigilanciaProfissional().getNumeroRegistro())).setLabel(new Model(bundle("nRegistro"))).setEnabled(enabled));
        getForm().add(cbxEstado = (DropDown) DropDownUtil.getUfDropDown(path(proxy.getVigilanciaProfissional().getUf())).setLabel(new Model(bundle("uf"))).setEnabled(enabled));
        cbxEstado.setModelObject(getForm().getModel().getObject().getVigilanciaProfissional().getUf());
        cbxEstado.setComponentValue(getForm().getModel().getObject().getVigilanciaProfissional().getUf());
        cbxEstado.setRequired(true);
        cbxEstado.addAjaxUpdateValue();
        getForm().add(new InputField(path(proxy.getVigilanciaProfissional().getNumeroCadastroVigilanciaSanitaria())).setLabel(new Model(bundle("nCadastroVigilanciaSanitaria"))).setEnabled(enabled));
        getForm().add(new TelefoneField(path(proxy.getVigilanciaProfissional().getTelefone1())).add(new AttributeModifier("class", "telefoneFixo")).setEnabled(enabled));
        getForm().add(new TelefoneField(path(proxy.getVigilanciaProfissional().getTelefone2())).add(new AttributeModifier("class", "telefoneFixo")).setEnabled(enabled));
        getForm().add(new TelefoneField(path(proxy.getVigilanciaProfissional().getCelular())).setEnabled(enabled));
        getForm().add(dchDataEmissao = (DateChooser) new DateChooser(path(proxy.getVigilanciaProfissional().getDataEmissaoRg())).setEnabled(enabled));
        dchDataEmissao.addRequiredClass();
        dchDataEmissao.setLabel((new Model(bundle("dataEmissao"))));

        containerEndereco = new WebMarkupContainer("containerEndereco", modelProfissionalEndereco = new CompoundPropertyModel<>(new CadastroProfissionalEnderecoDTO()));
        containerEndereco.setOutputMarkupId(true);
        CadastroProfissionalEnderecoDTO proxyEndereco = on(CadastroProfissionalEnderecoDTO.class);
        containerEndereco.add(new InputField(path(proxyEndereco.getProfissionalEndereco().getNumero())).setLabel(new Model(bundle("numero"))).setEnabled(enabled));
        containerEndereco.add(new InputField(path(proxyEndereco.getProfissionalEndereco().getComplemento())).setLabel(new Model(bundle("complemento"))).setEnabled(enabled));
        containerEndereco.add(pnlVigilanciaEndereco = (PnlVigilanciaEndereco) new PnlVigilanciaEndereco(path(proxyEndereco.getProfissionalEndereco().getVigilanciaEndereco())).setLabel(Model.of(bundle("endereco"))));
        containerEndereco.add(DropDownUtil.getNaoSimLongDropDown(path(proxyEndereco.getProfissionalEndereco().getFlagPrincipal())));
        containerEndereco.add(getDropDownTipoEndereco(path(proxyEndereco.getProfissionalEndereco().getTipoEnderecoVigilancia())));
        AbstractAjaxButton btnAdicionarEndereco;
        containerEndereco.add(btnAdicionarEndereco = new AbstractAjaxButton("btnAdicionarEndereco") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                adicionarEndereco(target);
            }
        });
        btnAdicionarEndereco.getDefaultFormProcessing();
        containerEndereco.add(tblProfissionalEndereco = new Table("tblProfissionalEndereco", getColumns(), getCollectionProvider()));
        tblProfissionalEndereco.populate();
        getForm().add(containerEndereco);

        //Inicio Anexos
        VigilanciaProfissionalAnexoDTO proxyAnexo = on(VigilanciaProfissionalAnexoDTO.class);

        containerVigilanciaProfissionalAnexo = new WebMarkupContainer("containerVigilanciaProfissionalAnexo", modelVigilanciaProfissionalAnexoDTO = new CompoundPropertyModel<>(new VigilanciaProfissionalAnexoDTO()));
        containerVigilanciaProfissionalAnexo.setOutputMarkupId(true);
        containerVigilanciaProfissionalAnexo.add(fileUploadField = (FileUploadField) new FileUploadField("upload", new PropertyModel<List<FileUpload>>(this, "lstUpload")).setEnabled(enabled));
        containerVigilanciaProfissionalAnexo.add(txtDescricaoAnexo = (InputField) new InputField(path(proxyAnexo.getDescricaoAnexo())).setEnabled(enabled));
        AbstractAjaxButton btnAdicionar;
        containerVigilanciaProfissionalAnexo.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAnexo(target);
            }
        });
        btnAdicionar.setEnabled(enabled);
        containerVigilanciaProfissionalAnexo.add(tblVigilanciaProfissionalAnexo = new Table("tblVigilanciaProfissionalAnexo", getColumnsAnexo(), getCollectionProviderAnexo()));
        tblVigilanciaProfissionalAnexo.populate();
        //Fim Anexos
        getForm().add(dropDownSolicitaReceituario = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVigilanciaProfissional().getEmiteReceita())));
        dropDownSolicitaReceituario.setLabel(new Model(bundle("solicitaReceituario"))).setEnabled(enabled);
        dropDownSolicitaReceituario.add(new Tooltip().setText("msgToolTipoPermiteReceituarioVigilanciaProfi"));
        getForm().add(new CheckBoxLongValue(path(proxy.getVigilanciaProfissional().getFlagSolicReceitaB1())));
        getForm().add(new CheckBoxLongValue(path(proxy.getVigilanciaProfissional().getFlagSolicReceitaB2())));
        getForm().add(new CheckBoxLongValue(path(proxy.getVigilanciaProfissional().getFlagSolicReceitaC2())));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        getForm().add(containerVigilanciaProfissionalAnexo);
        getForm().add(txtCpf = new UpperField(path(proxy.getVigilanciaProfissional().getCpf())));
        txtCpf.setEnabled(enabled);
        txtCpf.setRequired(true);
        txtCpf.addRequiredClass();
        txtCpf.setLabel((new Model(bundle("cpf"))));
        getForm().add(txtRg = new UpperField(path(proxy.getVigilanciaProfissional().getRg())));
        txtRg.setEnabled(enabled);
        txtRg.setRequired(true);
        txtRg.addRequiredClass();
        if (getForm().getModel().getObject().getVigilanciaProfissional().getCpf() != null) {
            txtRg.removeRequiredClass();
            txtRg.setRequired(false);
        }
        if (getForm().getModel().getObject().getVigilanciaProfissional().getRg() != null) {
            txtCpf.removeRequiredClass();
            txtCpf.setRequired(false);
        }
        txtRg.setLabel((new Model(bundle("rg"))));

        txtCpf.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (txtCpf.getComponentValue() != null) {
                    txtRg.setRequired(false);
                    txtRg.removeRequiredClass();
                    dchDataEmissao.setRequired(false);
                    dchDataEmissao.removeRequiredClass();
                } else {
                    txtRg.setRequired(true);
                    txtRg.addRequiredClass();
                    dchDataEmissao.setRequired(true);
                    dchDataEmissao.addRequiredClass();
                }
                ajaxRequestTarget.add(txtRg);
            }
        });
        txtRg.add(new AjaxFormComponentUpdatingBehavior("onblur") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (txtRg.getComponentValue() != null) {
                    txtCpf.setRequired(false);
                    txtCpf.removeRequiredClass();
                } else {
                    txtCpf.setRequired(true);
                    txtCpf.addRequiredClass();
                }
                ajaxRequestTarget.add(txtCpf);
            }
        });

        getForm().add(messageLabel = new MultiLineLabel("messageLabel", new PropertyModel(this, "messageLabel")));
        messageLabel.setDefaultModel(new Model<>(bundle("msgInformeCpfEOuRg")));

        add(getForm());
    }

    private void addContainerDadosEdicao(Form form, CadastroProfissionalVigilanciaDTO proxy) {
        form.add(containerDadosEdicao = new WebMarkupContainer("containerDadosEdicao"));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getVigilanciaProfissional().getUsuarioCadastro().getNome())));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getVigilanciaProfissional().getDataCadastro())));
        containerDadosEdicao.setVisible(getForm().getModel().getObject().getVigilanciaProfissional().getCodigo() != null);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        CadastroProfissionalEnderecoDTO proxy = on(CadastroProfissionalEnderecoDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("endereco"), proxy.getProfissionalEndereco().getVigilanciaEndereco().getEnderecoFormatado()));
        columns.add(createColumn(bundle("complemento"), proxy.getProfissionalEndereco().getComplemento()));
        columns.add(createColumn(bundle("numero"), proxy.getProfissionalEndereco().getNumero()));
        columns.add(createColumn(bundle("tipoEndereco"), proxy.getProfissionalEndereco().getTipoEnderecoVigilancia().getDescricao()));
        columns.add(createColumn(bundle("principal"), proxy.getDescricaoPrincipal()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<CadastroProfissionalEnderecoDTO>() {
            @Override
            public void customizeColumn(final CadastroProfissionalEnderecoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<CadastroProfissionalEnderecoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, CadastroProfissionalEnderecoDTO modelObject) throws ValidacaoException, DAOException {
                        validarRemoverEndereco(modelObject.getProfissionalEndereco());
                        CrudUtils.removerItem(target, tblProfissionalEndereco, profissionalEnderecoList, modelObject);
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<CadastroProfissionalEnderecoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, CadastroProfissionalEnderecoDTO modelObject) throws ValidacaoException, DAOException {
                        editarEndereco(target, modelObject);
                    }
                });
            }
        };
    }

    private void validarRemoverEndereco(ProfissionalEndereco profissionalEndereco) throws ValidacaoException {
        String msg = "msgRemoverEnderecoProfissionalVinculoReceita";
        RequerimentoReceitaA receitaA = LoadManager.getInstance(RequerimentoReceitaA.class)
                .addProperty(RequerimentoReceitaA.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoReceitaA.PROP_PROFISSIONAL_ENDERECO), profissionalEndereco))
                .setMaxResults(1)
                .start().getVO();
        if (receitaA != null) {
            throw new ValidacaoException(bundle(msg));
        }

        RequerimentoReceitaB receitaB = LoadManager.getInstance(RequerimentoReceitaB.class)
                .addProperty(RequerimentoReceitaB.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoReceitaB.PROP_PROFISSIONAL_ENDERECO), profissionalEndereco))
                .setMaxResults(1)
                .start().getVO();
        if (receitaB != null) {
            throw new ValidacaoException(bundle(msg));
        }

        if (profissionalEndereco != null && profissionalEndereco.getCodigo() != null) {
            RequerimentoReceitaTalidomida talidomida = LoadManager.getInstance(RequerimentoReceitaTalidomida.class)
                    .addProperty(RequerimentoReceitaTalidomida.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseRequerimentoReceitaTalidomida.PROP_PROFISSIONAL_ENDERECO), profissionalEndereco))
                    .setMaxResults(1)
                    .start().getVO();
            if (talidomida != null) {
                throw new ValidacaoException(bundle(msg));
            }
        }
    }

    private void editarEndereco(AjaxRequestTarget target, CadastroProfissionalEnderecoDTO dtoEndereco) throws DAOException, ValidacaoException {
        limpar(target);
        CadastroProfissionalEnderecoDTO dtoEnderecoClone = (CadastroProfissionalEnderecoDTO) SerializationUtils.clone(dtoEndereco);
        dtoEnderecoEdicao = dtoEndereco;

        modelProfissionalEndereco.setObject(dtoEnderecoClone);

        target.add(containerEndereco);
    }

    private void limpar(AjaxRequestTarget target) {
        modelProfissionalEndereco.setObject(new CadastroProfissionalEnderecoDTO());
        pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().limpar(target);
        target.add(containerEndereco);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (profissionalEnderecoList == null) {
                    profissionalEnderecoList = new ArrayList<>();
                }
                return profissionalEnderecoList;
            }
        };
    }

    private void adicionarEndereco(AjaxRequestTarget target) throws ValidacaoException {
        CadastroProfissionalEnderecoDTO profissionalEndereco = modelProfissionalEndereco.getObject();
        if (profissionalEndereco.getProfissionalEndereco().getVigilanciaEndereco() == null) {
            throw new ValidacaoException(bundle("informeEndereco"));
        }
        if (profissionalEndereco.getProfissionalEndereco().getTipoEnderecoVigilancia() == null) {
            throw new ValidacaoException(bundle("informeTipoEndereco"));
        }
        profissionalEndereco.getProfissionalEndereco().setVigilanciaProfissional(getForm().getModel().getObject().getVigilanciaProfissional());

        int idx = 0;
        for (int i = 0; i < profissionalEnderecoList.size(); i++) {
            CadastroProfissionalEnderecoDTO endereco = profissionalEnderecoList.get(i);
            if (dtoEnderecoEdicao != null && dtoEnderecoEdicao == endereco) {
                idx = i;
            } else if (existeEndereco(endereco.getProfissionalEndereco(), profissionalEndereco.getProfissionalEndereco())) {
                throw new ValidacaoException(bundle("enderecoJaAdicionado"));
            }
        }

        if (dtoEnderecoEdicao != null && CollectionUtils.isNotNullEmpty(profissionalEnderecoList)) {
            profissionalEnderecoList.remove(idx);
            profissionalEnderecoList.add(idx, profissionalEndereco);
        } else {
            profissionalEnderecoList.add(0, profissionalEndereco);
        }

        pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().limpar(target);
        pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().setModelObject(null);
        target.add(pnlVigilanciaEndereco);
        target.add(containerEndereco);
        modelProfissionalEndereco.setObject(new CadastroProfissionalEnderecoDTO());
        target.focusComponent(pnlVigilanciaEndereco.getAutoCompleteConsultaVigilanciaEndereco().getTxtDescricao().getTextField());

    }

    private boolean existeEndereco(ProfissionalEndereco enderecoAdd, ProfissionalEndereco enderecoJaAdd) {
        if (enderecoAdd.getVigilanciaEndereco().getCodigo().equals(enderecoJaAdd.getVigilanciaEndereco().getCodigo())) {
            if (enderecoAdd.getNumero() != null && enderecoAdd.getComplemento() != null) {
                if (enderecoAdd.getNumero().equals(enderecoJaAdd.getNumero())
                        && enderecoAdd.getComplemento().equals(enderecoJaAdd.getComplemento())) {
                    return true;
                }
            } else if (enderecoAdd.getNumero() != null) {
                if (enderecoAdd.getNumero().equals(enderecoJaAdd.getNumero())) {
                    return true;
                }
            } else if (enderecoAdd.getComplemento() != null) {
                if (enderecoAdd.getComplemento().equals(enderecoJaAdd.getComplemento())) {
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

    private void adicionarAnexo(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        VigilanciaProfissionalAnexoDTO anexoDTO = (VigilanciaProfissionalAnexoDTO) SerializationUtils.clone(modelVigilanciaProfissionalAnexoDTO.getObject());

        upload = fileUploadField.getFileUpload();
        if (upload == null) {
            throw new ValidacaoException(bundle("selecioneAnexo"));
        }

        if (Coalesce.asString(anexoDTO.getDescricaoAnexo()).trim().isEmpty()) {
            throw new ValidacaoException(bundle("informeDescricao"));
        }

        try {
            if (upload.getSize() > 1048576L) {
                throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoAnexo1MB"));
            }

            if (VigilanciaHelper.isExtensionAllowed(upload.getClientFileName())) {
                File file = File.createTempFile("anexo", upload.getClientFileName());

                upload.writeTo(file);
                anexoDTO.setFile(file);
                anexoDTO.setOrigem(GerenciadorArquivo.OrigemArquivo.CADASTRO_PROFISSIONAL_VIGILANCIA.value());
                anexoDTO.setNomeArquivoOriginal(upload.getClientFileName());

                requerimentoVigilanciaAnexoDTOList.add(anexoDTO);

                tblVigilanciaProfissionalAnexo.populate();
                tblVigilanciaProfissionalAnexo.update(target);

                modelVigilanciaProfissionalAnexoDTO.setObject(new VigilanciaProfissionalAnexoDTO());
                txtDescricaoAnexo.limpar(target);
                upload = null;
                target.add(containerVigilanciaProfissionalAnexo);
            } else {
                throw new ValidacaoException(bundle("somentePossivelAnexarPdfJpgJpegPng"));
            }
        } catch (IOException ex) {
            Logger.getLogger(RequerimentoExumacaoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private List<IColumn> getColumnsAnexo() {
        List<IColumn> columns = new ArrayList<>();
        VigilanciaProfissionalAnexoDTO proxy = on(VigilanciaProfissionalAnexoDTO.class);

        columns.add(getActionColumnAnexo());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricaoAnexoFormatado()));
        columns.add(getDownloadAnexoActionColumn());

        return columns;
    }

    private IColumn getDownloadAnexoActionColumn() {
        return new CustomColumn<VigilanciaProfissionalAnexoDTO>(bundle("anexo")) {
            @Override
            public Component getComponent(String componentId, final VigilanciaProfissionalAnexoDTO rowObject) {
                return new DownloadAnexoProfissionalColumnPanel(componentId, rowObject) {
                };
            }
        };
    }

    private IColumn getActionColumnAnexo() {
        return new MultipleActionCustomColumn<VigilanciaProfissionalAnexoDTO>() {
            @Override
            public void customizeColumn(final VigilanciaProfissionalAnexoDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<VigilanciaProfissionalAnexoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, VigilanciaProfissionalAnexoDTO modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < requerimentoVigilanciaAnexoDTOList.size(); i++) {
                            VigilanciaProfissionalAnexoDTO item = requerimentoVigilanciaAnexoDTOList.get(i);
                            if (item == rowObject) {
                                if (item.getGerenciadorArquivo() != null && item.getGerenciadorArquivo().getCodigo() != null) {
                                    getForm().getModel().getObject().getVigilanciaProfissionalAnexoDTOExcluidoList().add(item);
                                }

                                requerimentoVigilanciaAnexoDTOList.remove(i);
                            }
                        }
                        tblVigilanciaProfissionalAnexo.populate();
                        tblVigilanciaProfissionalAnexo.update(target);
                    }
                }).setEnabled(enabled);
            }
        };
    }

    private ICollectionProvider getCollectionProviderAnexo() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (requerimentoVigilanciaAnexoDTOList == null) {
                    requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
                }
                return requerimentoVigilanciaAnexoDTOList;
            }
        };
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(profissionalEnderecoList) && profissionalEnderecoList.size() == 1) {
            profissionalEnderecoList.get(0).getProfissionalEndereco().setFlagPrincipal(RepositoryComponentDefault.SIM_LONG);
        }
        validarEndereco(profissionalEnderecoList);
        getForm().getModel().getObject().setVigilanciaProfissionalAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        getForm().getModel().getObject().setProfissionalEnderecoDTOList(profissionalEnderecoList);
        VigilanciaProfissional vp = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarCadastroProfissionalVigilancia(getForm().getModel().getObject());
        getForm().getModel().getObject().setVigilanciaProfissional(vp);

        String mensagemImpressao = bundle("msgImprimirCadstroProfissionalInstitucional");

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<CadastroProfissionalVigilanciaDTO>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(CadastroProfissionalVigilanciaDTO object) throws ReportException {
                //CadastroProfissionalVigilanciaDTO dto = object;
                DataReport requerimentoSolicitacaoJuridicaReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoProfissionalParaReceita(vp.getCodigo());
                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(requerimentoSolicitacaoJuridicaReport);
                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, CadastroProfissionalVigilanciaDTO object) throws ValidacaoException, DAOException {
                ConsultaProfissionalVigilanciaPage page = new ConsultaProfissionalVigilanciaPage();
                getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
                setResponsePage(page);
            }
        });

        dlgConfirmacaoImpressao.show(target, getForm().getModel().getObject());

    }

    private void validarEndereco(List<CadastroProfissionalEnderecoDTO> profissionalEnderecoList) throws ValidacaoException {
        if (CollectionUtils.isAllEmpty(profissionalEnderecoList)) {
            throw new ValidacaoException(bundle("necessarioUmEndereco"));
        }
        List<CadastroProfissionalEnderecoDTO> principal = Lambda.select(profissionalEnderecoList, having(on(CadastroProfissionalEnderecoDTO.class).getProfissionalEndereco().getFlagPrincipal(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
        if (CollectionUtils.isAllEmpty(principal)) {
            throw new ValidacaoException(bundle("necessarioUmEnderecoPrincipal"));
        } else if (principal.size() > 1) {
            throw new ValidacaoException(bundle("somenteUmEnderecoPrincipal"));
        }

    }

    private Form<CadastroProfissionalVigilanciaDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new CadastroProfissionalVigilanciaDTO()));
            if (vigilanciaProfissional != null) {
                form.getModel().getObject().setVigilanciaProfissional(vigilanciaProfissional);
            } else {
                form.getModel().getObject().setVigilanciaProfissional(new VigilanciaProfissional());
            }
        }
        return form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtNomeProfissional)));
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProfissionaisReceita");
    }

    private void carregarAnexos(VigilanciaProfissional vigilanciaProfissional) {
        if (vigilanciaProfissional != null) {
            List<VigilanciaProfissionalAnexo> list = LoadManager.getInstance(VigilanciaProfissionalAnexo.class)
                    .addProperties(new HQLProperties(VigilanciaProfissionalAnexo.class).getProperties())
                    .addProperties(new HQLProperties(GerenciadorArquivo.class, VigilanciaProfissionalAnexo.PROP_GERENCIADOR_ARQUIVO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaProfissional.class, VigilanciaProfissionalAnexo.PROP_VIGILANCIA_PROFISSIONAL).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaProfissionalAnexo.PROP_VIGILANCIA_PROFISSIONAL, vigilanciaProfissional))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
                VigilanciaProfissionalAnexoDTO anexoDTO;
                for (VigilanciaProfissionalAnexo vpa : list) {
                    anexoDTO = new VigilanciaProfissionalAnexoDTO();
                    anexoDTO.setDescricaoAnexo(vpa.getDescricao());
                    anexoDTO.setNomeArquivoOriginal(vpa.getGerenciadorArquivo().getNomeArquivo());
                    anexoDTO.setGerenciadorArquivo(vpa.getGerenciadorArquivo());
                    anexoDTO.setVigilanciaProfissional(vpa.getVigilanciaProfissional());

                    requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
                }
            }
        }
    }

    private void carregarEndereco(VigilanciaProfissional vigilanciaProfissional) {
        if (vigilanciaProfissional != null) {
            List<ProfissionalEndereco> list = LoadManager.getInstance(ProfissionalEndereco.class)
                    .addProperties(new HQLProperties(ProfissionalEndereco.class).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, ProfissionalEndereco.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(ProfissionalEndereco.PROP_VIGILANCIA_PROFISSIONAL, vigilanciaProfissional))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                profissionalEnderecoList = new ArrayList<>();
                CadastroProfissionalEnderecoDTO dtoEndereco;
                for (ProfissionalEndereco profissionalEndereco : list) {
                    dtoEndereco = new CadastroProfissionalEnderecoDTO();
                    dtoEndereco.setProfissionalEndereco(profissionalEndereco);
                    profissionalEnderecoList.add(dtoEndereco);
                }
            }
        }
    }

    private DropDown<TipoEnderecoVigilancia> getDropDownTipoEndereco(String id) {
        dropDownEnderecoVigilancia = new DropDown<TipoEnderecoVigilancia>(id);
        List<TipoEnderecoVigilancia> list = LoadManager.getInstance(TipoEnderecoVigilancia.class)
                .addSorter(new QueryCustom.QueryCustomSorter(TipoEnderecoVigilancia.PROP_DESCRICAO))
                .start().getList();

        dropDownEnderecoVigilancia.addChoice(null, "");
        for (TipoEnderecoVigilancia tipoEnderecoVigilancia : list) {
            dropDownEnderecoVigilancia.addChoice(tipoEnderecoVigilancia, tipoEnderecoVigilancia.getDescricao());
        }

        return dropDownEnderecoVigilancia;
    }
}