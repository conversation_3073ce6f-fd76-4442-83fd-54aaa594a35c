package br.com.celk.view.vigilancia.questionariopopulacaocaesgatos;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatos;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal;
import net.sf.jasperreports.engine.JRException;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastroQuestionarioPopulacaoCaesGatosPage extends BasePage {

    private PopulacaoCaesGatos populacaoCaesGatos;
    private PopulacaoCaesGatosTipoAnimal populacaoCaesGatosTipoAnimal;
    private CompoundPropertyModel<PopulacaoCaesGatosTipoAnimal> modelAnimais;
    private List<PopulacaoCaesGatosTipoAnimal> lstItens = new ArrayList<>();
    private Table tblItens;
    private WebMarkupContainer containerAnimais;
    private WebMarkupContainer containerCao;
    private WebMarkupContainer containerGato;

    private DropDown<Long> dropDownTipoAnimal;
    private DropDown<String> sexoAnimal;
    private InputField<Long> idadeAnimal;
    private DropDown<String> animalCastrado;
    private DropDown<String> gostariaCastrarAnimal;
    private DropDown<String> acessoRuas;
    private DropDown<String> utilizamColeira;
    private DropDown<Long> quandoMorrePropriedade;
    private InputField quandoMorrePropriedadeOutro;
    private Form<PopulacaoCaesGatos> form;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private boolean viewOnly = true;
    private VoltarButton btnVoltar;

    public CadastroQuestionarioPopulacaoCaesGatosPage() {
        init();
    }

    public CadastroQuestionarioPopulacaoCaesGatosPage(PopulacaoCaesGatos populacaoCaesGatos) {
        this.populacaoCaesGatos = populacaoCaesGatos;
        init();
    }

    public CadastroQuestionarioPopulacaoCaesGatosPage(PopulacaoCaesGatos populacaoCaesGatos, boolean viewOnly) {
        this.populacaoCaesGatos = populacaoCaesGatos;
        this.viewOnly = viewOnly;

        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(populacaoCaesGatos == null ? (populacaoCaesGatos = new PopulacaoCaesGatos()) : populacaoCaesGatos));
        PopulacaoCaesGatos proxy = on(PopulacaoCaesGatos.class);

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getCodigoProfissional()), true));
        form.add(new InputField<Long>(path(proxy.getCodigoMicroarea())).setEnabled(true));
        form.add(new InputField<Long>(path(proxy.getCodigoFamilia())).setEnabled(true));
        form.add(DropDownUtil.getSimNaoDropDown(path(proxy.getCasaCercada())).setEnabled(true));

        form.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getCodigoEndereco()), true));
        autoCompleteConsultaVigilanciaEndereco.getTxtDescricao().addRequiredClass();

        form.add(new InputField<Long>(path(proxy.getNumeroEndereco())).setEnabled(true));
        form.add(new InputField<String>(path(proxy.getComplementoEndereco())).setEnabled(true));

        {
            PopulacaoCaesGatosTipoAnimal proxyPergunta = on(PopulacaoCaesGatosTipoAnimal.class);
            containerAnimais = new WebMarkupContainer("containerAnimais", modelAnimais = new CompoundPropertyModel<>(new PopulacaoCaesGatosTipoAnimal()));
            containerAnimais.setEnabled(true);
            containerAnimais.setOutputMarkupId(true);
            containerAnimais.setVisible(true);

            containerAnimais.add(dropDownTipoAnimal = DropDownUtil.getIEnumDropDown(path(proxyPergunta.getTipoAnimal()), PopulacaoCaesGatosTipoAnimal.TipoAnimal.values()));

            containerAnimais.add(sexoAnimal = getSexoAnimalDropDown(path(proxyPergunta.getSexoAnimal())));
            containerAnimais.add(animalCastrado = DropDownUtil.getSimNaoDropDown(path(proxyPergunta.getAnimalCastrado())));
            containerAnimais.add(gostariaCastrarAnimal = DropDownUtil.getSimNaoDropDown(path(proxyPergunta.getGostariaCastrarAnimal())));

            containerCao = new WebMarkupContainer("containerCao", modelAnimais);
            containerCao.setOutputMarkupId(true);
            containerCao.setOutputMarkupPlaceholderTag(true);

            containerCao.add(acessoRuas = DropDownUtil.getSimNaoDropDown(path(proxyPergunta.getAcessoRuas())));
            containerCao.add(utilizamColeira = DropDownUtil.getSimNaoDropDown(path(proxyPergunta.getUilizamColeira())));
            containerCao.add(quandoMorrePropriedade = getPopulacaoCaesGatosQuandoMorreDropDown(path(proxyPergunta.getQuandoMorrePropriedade())));
            containerCao.add(quandoMorrePropriedadeOutro = new InputField<>(path(proxyPergunta.getQuandoMorrePropriedadeOutro())));
            quandoMorrePropriedadeOutro.setEnabled(false);

            quandoMorrePropriedade.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (quandoMorrePropriedade.getModelObject().equals(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.OUTRO.value())) {
                        quandoMorrePropriedadeOutro.setEnabled(true);
                        quandoMorrePropriedadeOutro.setRequired(true);
                    } else {
                        quandoMorrePropriedadeOutro.setEnabled(false);
                    }
                    target.add(quandoMorrePropriedadeOutro);
                }
            });

            containerGato = new WebMarkupContainer("containerGato", modelAnimais);
            containerGato.setOutputMarkupId(true);
            containerGato.setOutputMarkupPlaceholderTag(true);
            containerGato.setVisible(false);

            idadeAnimal = new InputField<>(path(proxyPergunta.getIdadeAnimal()));
            containerGato.add(idadeAnimal);

            form.add(containerAnimais);
            form.add(containerCao);
            form.add(containerGato);

            form.add(new TextArea<>(path(proxy.getObservacao())).setEnabled(true));

            dropDownTipoAnimal.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    PopulacaoCaesGatosTipoAnimal.TipoAnimal selectedAnimal = PopulacaoCaesGatosTipoAnimal.TipoAnimal.valeuOf(dropDownTipoAnimal.getModelObject());
                    updateVisibility(selectedAnimal, target);
                }
            });

            form.add(new AbstractAjaxButton("btnAdicionarAnimal") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException, JRException, IOException {
                    adicionarAnimal(target);
                }
            });

            form.add(tblItens = new Table("tblItens", getColumnsItem(), getCollectionProviderItem()));
            tblItens.populate();
        }

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }));
        carregarElos();
        form.add(btnVoltar = new VoltarButton("btnVoltar"));

        if (!viewOnly) {
            disableFields(form);
        }
        add(form);
    }

    private void disableFields(MarkupContainer parent){
        for (Component next : parent) {
            next.setEnabled(false);
            btnVoltar.setEnabled(true);
        }
    }

    private void updateVisibility(PopulacaoCaesGatosTipoAnimal.TipoAnimal selectedAnimal, AjaxRequestTarget target) {
        boolean isCao = selectedAnimal == PopulacaoCaesGatosTipoAnimal.TipoAnimal.CAO;
        containerCao.setVisible(isCao);
        containerGato.setVisible(!isCao);

        target.add(containerCao);
        target.add(containerGato);
    }

    private DropDown getPopulacaoCaesGatosQuandoMorreDropDown(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.NAO_RESPONDER.value(),
                PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.NAO_RESPONDER.descricao());
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.ENTERRA.value(),
                PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.ENTERRA.descricao());
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.JOGA_LIXO.value(),
                PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.JOGA_LIXO.descricao());
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.CHAMA_PREFEITURA.value(),
                PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.CHAMA_PREFEITURA.descricao());
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.OUTRO.value(),
                PopulacaoCaesGatosTipoAnimal.QuandoMorreCao.OUTRO.descricao());
        return dropDown;
    }

    private DropDown getSexoAnimalDropDown(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(RepositoryComponentDefault.SexoAnimal.MACHO.value(), RepositoryComponentDefault.SexoAnimal.MACHO.descricao());
        dropDown.addChoice(RepositoryComponentDefault.SexoAnimal.FEMEA.value(), RepositoryComponentDefault.SexoAnimal.FEMEA.descricao());
        return dropDown;
    }

    private void carregarElos() {
        if (populacaoCaesGatos != null && populacaoCaesGatos.getCodigo() != null) {
            lstItens = LoadManager.getInstance(PopulacaoCaesGatosTipoAnimal.class)
                    .addProperties(new HQLProperties(PopulacaoCaesGatosTipoAnimal.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(PopulacaoCaesGatosTipoAnimal.PROP_CODIGO_POPULACAO_CAES_GATOS, populacaoCaesGatos))
                    .start().getList();
        } else {
            lstItens = new ArrayList<>();
        }
    }

    private ICollectionProvider getCollectionProviderItem() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstItens;
            }
        };
    }

    private List<IColumn> getColumnsItem() {
        List<IColumn> columns = new ArrayList();
        PopulacaoCaesGatosTipoAnimal proxyPergunta = on(PopulacaoCaesGatosTipoAnimal.class);

        columns.add(getActionColumnItem());
        columns.add(createColumn(bundle("tipoAnimal"), proxyPergunta.getTipoAnimalTable()));
        columns.add(createColumn(bundle("sexo"), proxyPergunta.getSexoAnimal()));
        columns.add(createColumn(bundle("castrado"), proxyPergunta.getAnimalCastrado()));
        columns.add(createColumn(bundle("idade"), proxyPergunta.getIdadeAnimal()));

        return columns;
    }

    private IColumn getActionColumnItem() {
        return new MultipleActionCustomColumn<PopulacaoCaesGatosTipoAnimal>() {

            @Override
            public void customizeColumn(PopulacaoCaesGatosTipoAnimal rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<PopulacaoCaesGatosTipoAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, PopulacaoCaesGatosTipoAnimal modelObject) throws ValidacaoException, DAOException {
                        populacaoCaesGatosTipoAnimal = modelObject;
                        modelAnimais.setObject((PopulacaoCaesGatosTipoAnimal) SerializationUtils.clone(modelObject));
                        validarTipoAnimal(target, modelObject);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<PopulacaoCaesGatosTipoAnimal>() {
                    @Override
                    public void action(AjaxRequestTarget target, PopulacaoCaesGatosTipoAnimal modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < lstItens.size(); i++) {
                            PopulacaoCaesGatosTipoAnimal item = lstItens.get(i);
                            if (item == rowObject) {
                                lstItens.remove(i);
                            }
                        }
                        tblItens.update(target);
                    }
                });
            }
        };
    }

    private void validarTipoAnimal(AjaxRequestTarget target, PopulacaoCaesGatosTipoAnimal modelObject) {
        if (modelObject.getTipoAnimal().equals(PopulacaoCaesGatosTipoAnimal.TipoAnimal.GATO.value())) {
            containerCao.setVisible(false);
            containerGato.setVisible(true);
            preencherDadosGato(target, modelObject);
        } else {
            containerCao.setVisible(true);
            containerGato.setVisible(false);
            preencherDadosCao(target, modelObject);
        }
        target.add(containerGato);
        target.add(containerCao);
    }

    private void preencherDadosGato(AjaxRequestTarget target, PopulacaoCaesGatosTipoAnimal modelObject) {
        dropDownTipoAnimal.limpar(target);
        idadeAnimal.limpar(target);
        sexoAnimal.limpar(target);
        gostariaCastrarAnimal.limpar(target);
        animalCastrado.limpar(target);

        acessoRuas.limpar(target);
        utilizamColeira.limpar(target);
        quandoMorrePropriedade.limpar(target);
        quandoMorrePropriedadeOutro.limpar(target);

        dropDownTipoAnimal.setComponentValue(modelObject.getTipoAnimal());
        idadeAnimal.setModelObject(modelObject.getIdadeAnimal());
        sexoAnimal.setModelObject(modelObject.getSexoAnimal());
        gostariaCastrarAnimal.setModelObject(modelObject.getGostariaCastrarAnimal());
        animalCastrado.setModelObject(modelObject.getAnimalCastrado());

        target.add(dropDownTipoAnimal, idadeAnimal, sexoAnimal, gostariaCastrarAnimal, animalCastrado, acessoRuas, utilizamColeira, quandoMorrePropriedade, quandoMorrePropriedadeOutro);
    }

    private void preencherDadosCao(AjaxRequestTarget target, PopulacaoCaesGatosTipoAnimal modelObject) {
        dropDownTipoAnimal.limpar(target);
        acessoRuas.limpar(target);
        utilizamColeira.limpar(target);
        quandoMorrePropriedade.limpar(target);
        quandoMorrePropriedadeOutro.limpar(target);
        sexoAnimal.limpar(target);
        gostariaCastrarAnimal.limpar(target);
        animalCastrado.limpar(target);

        idadeAnimal.limpar(target);

        dropDownTipoAnimal.setComponentValue(modelObject.getTipoAnimal());
        acessoRuas.setModelObject(modelObject.getAcessoRuas());
        utilizamColeira.setModelObject(modelObject.getUilizamColeira());
        quandoMorrePropriedade.setModelObject(modelObject.getQuandoMorrePropriedade());
        quandoMorrePropriedadeOutro.setModelObject(modelObject.getQuandoMorrePropriedadeOutro());
        sexoAnimal.setModelObject(modelObject.getSexoAnimal());
        gostariaCastrarAnimal.setModelObject(modelObject.getGostariaCastrarAnimal());
        animalCastrado.setModelObject(modelObject.getAnimalCastrado());

        target.add(dropDownTipoAnimal, acessoRuas, utilizamColeira, quandoMorrePropriedade, quandoMorrePropriedadeOutro, sexoAnimal, gostariaCastrarAnimal, animalCastrado, idadeAnimal);
    }

    private void adicionarAnimal(AjaxRequestTarget target) throws ValidacaoException {
        PopulacaoCaesGatosTipoAnimal elo = modelAnimais.getObject();
        validarAddAnimal(elo);

        if (populacaoCaesGatosTipoAnimal != null) {
            for (int i = 0; i < lstItens.size(); i++) {
                if (lstItens.get(i) == populacaoCaesGatosTipoAnimal) {
                    lstItens.set(i, elo);
                    break;
                }
            }
        } else {
            lstItens.add(elo);
        }
        tblItens.update(target);
        limparItem(target);
    }

    private void limparItem(AjaxRequestTarget target) {
        modelAnimais.setObject(new PopulacaoCaesGatosTipoAnimal());

        populacaoCaesGatosTipoAnimal = null;
        sexoAnimal.limpar(target);
        quandoMorrePropriedade.limpar(target);
        quandoMorrePropriedadeOutro.limpar(target);
        idadeAnimal.limpar(target);
        quandoMorrePropriedadeOutro.setEnabled(false);

        target.add(sexoAnimal, quandoMorrePropriedade, quandoMorrePropriedadeOutro, idadeAnimal);
    }

    private void validarAddAnimal(PopulacaoCaesGatosTipoAnimal elo) throws ValidacaoException {
        if (elo.getSexoAnimal() == null) {
            throw new ValidacaoException(bundle("msgInformeSexo"));
        }

        if (dropDownTipoAnimal.getModelObject() == 0) {
            if (elo.getQuandoMorrePropriedade() == null) {
                throw new ValidacaoException(bundle("msgObrigatorioInformarOqueVoceFazQuandoMorrePropriedade"));
            }
            if (elo.getUilizamColeira() == null) {
                throw new ValidacaoException(bundle("msgObrigatorioInformarUtilizaColeira"));
            }
        }
    }

    private void salvar() throws DAOException, ValidacaoException {
        if (CollectionUtils.isEmpty(lstItens)) {
            throw new ValidacaoException(bundle("msgObrigatorioAoMenosUmAnimal"));
        }

        Serializable save = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarCadastroPopulacaoCaesGatos(populacaoCaesGatos, lstItens);

        Page page = new ConsultaPopulacaoCaesGatosPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, WicketMethods.getMessageResgistroSalvo(PopulacaoCaesGatos.class, save));
    }

    @Override
    public String getTituloPrograma() {return BundleManager.getString("cadastroPopulacaoCaesGatos");
    }
}
