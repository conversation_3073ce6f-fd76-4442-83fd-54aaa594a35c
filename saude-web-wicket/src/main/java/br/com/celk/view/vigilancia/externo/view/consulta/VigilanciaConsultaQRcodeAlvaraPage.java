package br.com.celk.view.vigilancia.externo.view.consulta;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilanciaExterno;
import br.com.celk.view.vigilancia.externo.view.components.feedback.FeedBackVigilancia;
import br.com.celk.view.vigilancia.externo.view.components.feedback.IFeedBackVigilancia;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VeiculoEstabelecimentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.RequerimentoLicencaTransporteHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.string.StringValue;
import org.wicketstuff.annotation.mount.MountPath;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@MountPath(VigilanciaHelper.URL_ALVARA)
public class VigilanciaConsultaQRcodeAlvaraPage extends BasePageVigilanciaExterno implements IFeedBackVigilancia {

    public static String QRCODE = "CHQRC";

    private List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOs = new ArrayList<>();
    private Table<VeiculoEstabelecimentoDTO> tblVeiculos;

    private StatelessForm form;
    private Label lbTitulo;
    private Label lbSituacao;
    private String titulo;
    private String situacao;
    private String protocolo;
    private String tipo;
    private String estabelecimento;
    private String dataRequerimento;
    private String responsavelTecnico;
    private String dataValidade;
    private String nomeEvento;
    private String periodo;
    private String responsavelLegal;
    private String atividadePrincipal;
    private String veiculos;
    private boolean autorizacaoSanitaria;

    private FeedBackVigilancia feedBackVigilancia;
    private WebMarkupContainer containerForm;
    private WebMarkupContainer containerFormAlvaraInicial;
    private WebMarkupContainer containerFormEvento;
    private WebMarkupContainer containerFormBaixa;
    private WebMarkupContainer containerFormLegal;
    private WebMarkupContainer containerFormAtividadeEconomica;
    private WebMarkupContainer containerFormLicenca;

    @Override
    protected void onInitialize() {
        super.onInitialize();

        form = new StatelessForm("form");
        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);
        feedBackVigilancia = new FeedBackVigilancia("feedBack");
        form.add(feedBackVigilancia);
        feedBackVigilancia.setOutputMarkupId(true);

        form.add(lbTitulo = new Label("titulo", new PropertyModel<String>(this, "titulo")));
        lbTitulo.setDefaultModel(Model.of(BundleManager.getString("consultaAlvara")));

        containerForm = new WebMarkupContainer("containerForm");
        containerForm.add(lbSituacao = new Label("situacao"));
        containerForm.add(new Label("protocolo", new PropertyModel<String>(this, "protocolo")));
        containerForm.add(new Label("tipo", new PropertyModel<String>(this, "tipo")));
        containerForm.add(new Label("estabelecimento", new PropertyModel<String>(this, "estabelecimento")));
        containerForm.add(new Label("dataRequerimento", new PropertyModel<String>(this, "dataRequerimento")));
        form.add(containerForm);

        containerFormAlvaraInicial = new WebMarkupContainer("containerFormAlvaraInicial");
        containerFormAlvaraInicial.add(new Label("dataValidade", new PropertyModel<String>(this, "dataValidade")));
        form.add(containerFormAlvaraInicial);

        containerFormEvento = new WebMarkupContainer("containerFormEvento");
        containerFormEvento.add(new Label("nomeEvento", new PropertyModel<String>(this, "nomeEvento")));
        containerFormEvento.add(new Label("periodo", new PropertyModel<String>(this, "periodo")));
        form.add(containerFormEvento);

        containerFormLegal = new WebMarkupContainer("containerFormLegal");
        containerFormLegal.add(new Label("responsavelLegal", new PropertyModel<String>(this, "responsavelLegal")));
        form.add(containerFormLegal);

        containerFormAtividadeEconomica = new WebMarkupContainer("containerFormAtividadeEconomica");
        containerFormAtividadeEconomica.add(new Label("atividadePrincipal", new PropertyModel<String>(this, "atividadePrincipal")));
        form.add(containerFormAtividadeEconomica);

        containerFormBaixa = new WebMarkupContainer("containerFormBaixa");
        containerFormBaixa.add(new Label("responsavelTecnico", new PropertyModel<String>(this, "responsavelTecnico")));
        form.add(containerFormBaixa);

        containerFormLicenca = new WebMarkupContainer("containerFormLicenca");
        tblVeiculos = new Table("tblVeiculos", getColumnsVeiculos(), getCollectionProviderVeiculo());
        tblVeiculos.setScrollY("80");
        containerFormLicenca.add(tblVeiculos);
        tblVeiculos.populate();
        form.add(containerFormLicenca);

        buscarRequerimento();

        add(form);

    }

    private List<IColumn> getColumnsVeiculos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        RequerimentoLicencaTransporteVeiculo proxy = on(RequerimentoLicencaTransporteVeiculo.class);

        columns.add(createColumn(bundle("placa"), proxy.getVeiculoEstabelecimento().getPlacaFormatada()));
        columns.add(createColumn(bundle("tipoVeiculo"), proxy.getVeiculoEstabelecimento().getTipoVeiculo()));
        columns.add(createColumn(bundle("renavam"), proxy.getVeiculoEstabelecimento().getRenavam()));
        columns.add(createColumn(bundle("especificacao"), proxy.getVeiculoEstabelecimento().getEspecificacao()));
        columns.add(createColumn(bundle("observacao"), proxy.getVeiculoEstabelecimento().getRestricoes()));
        if (autorizacaoSanitaria) {
            columns.add(createColumn(bundle("taxa"), proxy.getVeiculoEstabelecimento().getDescricaoCalculoTaxaAutorizacaoSanitaria()));
        } else {
            columns.add(createColumn(bundle("taxa"), proxy.getVeiculoEstabelecimento().getDescricaoCalculoTaxa()));
        }
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoSituacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderVeiculo() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (veiculoEstabelecimentoDTOs == null) {
                    veiculoEstabelecimentoDTOs = new ArrayList<>();
                }
                return veiculoEstabelecimentoDTOs;
            }
        };
    }

    private void buscarRequerimento() {
        RequerimentoVigilancia requerimentoVigilancia = null;
        StringValue qrcode = getPageParameters().get(QRCODE);
        if (!qrcode.isNull() && !qrcode.isEmpty() && qrcode.toString() != null) {
            requerimentoVigilancia = LoadManager.getInstance(RequerimentoVigilancia.class)
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_SITUACAO))
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_TIPO_DOCUMENTO))
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_PROTOCOLO))
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_DATA_REQUERIMENTO))
                    .addProperty(VOUtils.montarPath(RequerimentoVigilancia.PROP_DATA_ENTREGA))
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilancia.PROP_CHAVE_Q_RCODE, qrcode.toString()))
                    .setMaxResults(1)
                    .start().getVO();
        }

        containerFormAlvaraInicial.setVisible(false);
        containerFormEvento.setVisible(false);
        containerFormBaixa.setVisible(false);
        containerFormLegal.setVisible(false);
        containerFormAtividadeEconomica.setVisible(false);
        containerFormLicenca.setVisible(false);

        if (requerimentoVigilancia != null) {
            situacao = requerimentoVigilancia.getDescricaoSituacao();
            tipo = requerimentoVigilancia.getDescricaoTipoDocumento();
            protocolo = requerimentoVigilancia.getProtocoloFormatado();
            estabelecimento = requerimentoVigilancia.getEstabelecimento().getRazaoSocial();
            dataRequerimento = requerimentoVigilancia.getDataRequerimentoFormatado();

            switch (Objects.requireNonNull(TipoSolicitacao.TipoDocumento.valueOf(requerimentoVigilancia.getTipoDocumento()))) {
                case ALVARA_INICIAL:
                case LICENCA_SANITARIA:
                case REVALIDACAO_LICENCA_SANITARIA:
                case ALVARA_REVALIDACAO:
                    RequerimentoAlvara requerimentoAlvara = LoadManager.getInstance(RequerimentoAlvara.class)
                            .addProperties(new HQLProperties(RequerimentoAlvara.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO), requerimentoVigilancia.getEstabelecimento()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_PROTOCOLO), requerimentoVigilancia.getProtocolo()))
                            .addSorter(new QueryCustom.QueryCustomSorter(RequerimentoAlvara.PROP_ANO_BASE, QueryCustom.QueryCustomSorter.DECRESCENTE))
                            .setMaxResults(1).start().getVO();
                    if (requerimentoAlvara != null && requerimentoAlvara.getDataValidadeProvisoria() != null && requerimentoVigilancia != null && !RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(requerimentoVigilancia.getSituacao())) {
                        dataValidade = Data.formatar(requerimentoAlvara.getDataValidadeProvisoria()).concat(" ").concat(BundleManager.getString("provisorio"));
                        containerFormAlvaraInicial.setVisible(true);
                    } else if (requerimentoAlvara != null) {
                        dataValidade = Data.formatar(requerimentoAlvara.getDataValidade());
                        containerFormAlvaraInicial.setVisible(true);
                    }
                    break;
                case ALVARA_PARTICIPANTE_EVENTO:
                    RequerimentoEvento requerimentoEvento = LoadManager.getInstance(RequerimentoEvento.class)
                            .addProperties(new HQLProperties(RequerimentoEvento.class).getProperties())
                            .addProperties(new HQLProperties(EventosVigilancia.class, VOUtils.montarPath(RequerimentoEvento.PROP_EVENTOS_VIGILANCIA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia.getCodigo()))
                            .setMaxResults(1).start().getVO();
                    periodo = requerimentoEvento.getEventosVigilancia().getDescricaoPeriodo();
                    nomeEvento = requerimentoEvento.getEventosVigilancia().getNome();
                    containerFormEvento.setVisible(true);
                    break;
                case BAIXA_RESPONSABILIDADE_TECNICA:
                    setInformacoesBaixaRT(requerimentoVigilancia);
                    break;
                case ALTERACAO_RESPONSABILIDADE_LEGAL:
                    responsavelLegal = requerimentoVigilancia.getEstabelecimento().getRepresentanteNome();
                    containerFormLegal.setVisible(true);
                    break;
                case ALTERACAO_ATIVIDADE_ECONOMICA:
                    EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                            .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                            .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                            .addProperties(new HQLProperties(SetorVigilancia.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, requerimentoVigilancia.getEstabelecimento()))
                            .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                            .setMaxResults(1).start().getVO();
                    if (estabelecimentoAtividade.getAtividadeEstabelecimento().getDescricao() != null) {
                        atividadePrincipal = estabelecimentoAtividade.getAtividadeEstabelecimento().getDescricao();
                        containerFormAtividadeEconomica.setVisible(true);
                    }
                    break;
                case ENTRADA_RESPONSABILIDADE_TECNICA:
                    RequerimentoInclusaoResponsabilidade requerimentoInclusaoResponsabilidade = LoadManager.getInstance(RequerimentoInclusaoResponsabilidade.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoBaixaResponsabilidade.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia.getCodigo()))
                            .setMaxResults(1).start().getVO();
                    responsavelTecnico = requerimentoInclusaoResponsabilidade.getResponsavelTecnico().getNome();
                    containerFormBaixa.setVisible(true);
                    break;
                case LICENCA_TRANSPORTE:
                    lbTitulo.setDefaultModel(Model.of(BundleManager.getString("licencaTransporte")));
                    RequerimentoLicencaTransporte requerimentoLicencaTransporte = RequerimentoLicencaTransporteHelper.carregarLicencaTransporte(requerimentoVigilancia);
                    autorizacaoSanitaria = RequerimentoLicencaTransporte.TipoLicenca.AUTORIZACAO_SANITARIA.value().equals(requerimentoLicencaTransporte.getTipoLicenca());
                    buscarVeiculos(requerimentoVigilancia);
                    tblVeiculos.populate();
                    containerFormLicenca.setVisible(true);
                    break;
                default:

            }

            resolveSituacao(requerimentoVigilancia);

        } else {
            error(BundleManager.getString("msgNaoFoiPossivelEncontrarRequerimento"));
            lbTitulo.setDefaultModel(Model.of(BundleManager.getString("ops")));
            containerForm.setVisible(false);
            return;
        }
        containerForm.setVisible(true);
    }

    private void setInformacoesBaixaRT(RequerimentoVigilancia requerimentoVigilancia) {
        RequerimentoBaixaResponsabilidade requerimentoBaixaResponsabilidade = LoadManager.getInstance(RequerimentoBaixaResponsabilidade.class)
                .addProperties(new HQLProperties(RequerimentoBaixaResponsabilidade.class).getProperties())
                .addProperty(VOUtils.montarPath(RequerimentoBaixaResponsabilidade.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VALIDADE_ALVARA))
                .addProperty(VOUtils.montarPath(RequerimentoBaixaResponsabilidade.PROP_ESTABELECIMENTO, Estabelecimento.PROP_ALVARA))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoBaixaResponsabilidade.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia.getCodigo()))
                .setMaxResults(1).start().getVO();

        if (requerimentoBaixaResponsabilidade != null) {
            if (requerimentoBaixaResponsabilidade.getResponsavelTecnico() != null) {
                responsavelTecnico = requerimentoBaixaResponsabilidade.getResponsavelTecnico().getNome();
                containerFormBaixa.setVisible(true);
            }

            if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.DEFERIDO.value())) {
                containerFormBaixa.setVisible(false);
                tipo = BundleManager.getString("alvaraSanitario");

                Estabelecimento estabelecimentoObj = requerimentoBaixaResponsabilidade.getEstabelecimento();
                if (estabelecimentoObj != null) {
                    if (estabelecimentoObj.getAlvara() != null) {
                        RequerimentoAlvara requerimentoAlvara = LoadManager.getInstance(RequerimentoAlvara.class)
                                .addProperties(new HQLProperties(RequerimentoAlvara.class).getProperties())
                                .addProperty(VOUtils.montarPath(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_PROTOCOLO))
                                .addProperty(VOUtils.montarPath(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_DATA_REQUERIMENTO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoAlvara.PROP_NUMERO_ALVARA), Integer.parseInt(estabelecimentoObj.getAlvara())))
                                .setMaxResults(1).start().getVO();

                        if (requerimentoAlvara.getRequerimentoVigilancia() != null && !requerimentoAlvara.getRequerimentoVigilancia().getDataRequerimentoFormatado().isEmpty()) {
                            protocolo = requerimentoAlvara.getRequerimentoVigilancia().getProtocoloFormatado();
                            dataRequerimento = requerimentoAlvara.getRequerimentoVigilancia().getDataRequerimentoFormatado();
                        }
                    }

                    if (!estabelecimentoObj.getValidadeAlvaraFormatado().isEmpty()) {
                        dataValidade = estabelecimentoObj.getValidadeAlvaraFormatado();
                        containerFormAlvaraInicial.setVisible(true);
                    }
                }
            }
        }
    }

    private void buscarVeiculos(RequerimentoVigilancia requerimentoVigilancia) {
        List<RequerimentoLicencaTransporteVeiculo> requerimentoLicencaTransporteVeiculos = RequerimentoLicencaTransporteHelper.carregarRequerimentoLicencaTransporteVeiculo(requerimentoVigilancia);
        ArrayList<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList = new ArrayList<>();
        for (RequerimentoLicencaTransporteVeiculo requerimentoLicencaTransporteVeiculo : requerimentoLicencaTransporteVeiculos) {
            VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO = new VeiculoEstabelecimentoDTO(requerimentoLicencaTransporteVeiculo.getVeiculoEstabelecimento());
            veiculoEstabelecimentoDTO.setStatus(requerimentoLicencaTransporteVeiculo.getStatus());
            veiculoEstabelecimentoDTOList.add(veiculoEstabelecimentoDTO);
        }
        if (CollectionUtils.isNotNullEmpty(veiculoEstabelecimentoDTOList)) {
            this.veiculoEstabelecimentoDTOs.addAll(veiculoEstabelecimentoDTOList);
        }
    }

    private void resolveSituacao(RequerimentoVigilancia requerimentoVigilancia) {

        if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.PENDENTE.value())
                || requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.RECEBIDO.value())
                || requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.RECEBIDO_PARA_ENTREGA.value())
                || requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.DOCUMENTO_ENTREGUE.value())
                || requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.ENCAMINHADO_PARA_ENTREGA.value())) {
            lbSituacao.add(new AttributeModifier("class", "label label-primary"));
        } else if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.ANALISE.value())) {
            lbSituacao.add(new AttributeModifier("class", "label label-info"));
        } else if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.CADASTRADO.value())) {
            lbSituacao.add(new AttributeModifier("class", "label label-warning"));
        } else if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.CANCELADO.value())
                || requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.INDEFERIDO.value())) {
            lbSituacao.add(new AttributeModifier("class", "label label-danger"));
        } else if (requerimentoVigilancia.getSituacao().equals(RequerimentoVigilancia.Situacao.DEFERIDO.value())) {
            lbSituacao.add(new AttributeModifier("class", "label label-success"));
        } else {
            lbSituacao.add(new AttributeModifier("class", "label label-default"));
        }
    }

    @Override
    public String getTituloPrograma() {
        return "";
    }

    @Override
    public String getSufixoVigilancia() {
        ConfiguracaoVigilancia configuracaoVigilancia = null;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.vigilancia.error(e.getMessage(), e);
        }
        if (configuracaoVigilancia != null) {
            Empresa empresa = LoadManager.getInstance(Empresa.class)
                    .addProperties(new HQLProperties(Empresa.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, configuracaoVigilancia.getEmpresa().getCodigo()))
                    .setMaxResults(1).start().getVO();
            return empresa.getEnderecoCidadeBairroFormatado().toUpperCase();
        }
        return "";
    }

    @Override
    public FeedBackVigilancia getFeedBackVigilancia() {
        return feedBackVigilancia;
    }
}
