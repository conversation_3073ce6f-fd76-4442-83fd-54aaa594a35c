package br.com.celk.view.login;

import br.com.celk.component.behavior.AjaxTimerDelayComponentBehavior;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.IModalMessagePage;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.modalmessage.ModalMessage;
import br.com.celk.io.FtpImageUtil;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.LoginHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.bemvindo.BemVindoPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.home.HomePage;
import br.com.celk.view.login.service.IntegracaoLoginGovBrService;
import br.com.celk.view.senhaexpirada.SenhaExpiradaPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.WebPage;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.link.ResourceLink;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.protocol.http.WebSession;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.string.StringValue;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LoginUnidadePage extends WebPage implements IModalMessagePage {

    private ModalMessage modalMessage;
    private Model<String> modelTituloAba;

    private Form form;
    private Form containerListView;
    private InputField txtBuscaUnidade;
    private ListView<ListView<Empresa>> listView;

    private String login;
    private List<Empresa> empresasDoUsuario = new ArrayList<Empresa>();
    private List<Empresa> empresas = new ArrayList<Empresa>();

    private Usuario usuario;
    private Empresa empresaSelecionada;
    private String buscaUnidade;

    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehavior;

    private List<NodeButtonEventListener> nodeButtonListeners = new ArrayList<NodeButtonEventListener>();
    private transient IntegracaoLoginGovBrService integracaoLoginGovBrService;

    public LoginUnidadePage(Usuario usuario, List<Empresa> empresas) {
        this.usuario = usuario;
        this.empresasDoUsuario = empresas;
        this.empresas.addAll(empresas);
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            this.empresaSelecionada = empresas.get(0);
        }
    }

    public LoginUnidadePage(Usuario usuario, List<Empresa> empresas, PageParameters pp) {
        super(pp);
        this.usuario = usuario;
        this.empresasDoUsuario = empresas;
        this.empresas.addAll(empresas);
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            this.empresaSelecionada = empresas.get(0);
        }

    }

    public LoginUnidadePage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();

        login = usuario.getLogin();

        form = new Form("form");
        ResourceLink<ResourceReference> rlnkFavicon = null;
        try {
            File logoFaviconFile = LogoHelper.getLogoFavicon();
            if (logoFaviconFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoFaviconFile);
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", new ResourceStreamResource(fileResourceStream)){
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex){
            rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
        } finally {
            if (rlnkFavicon == null){
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
            }
        }

        add(rlnkFavicon);

        try {
            String title = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tituloPaginaLogin");
            add(new Label("tituloAba", modelTituloAba = Model.of(title)));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        add(getImagePadrao());

        add(new Label("usuarioLogado", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return usuario.getNome();
            }
        }));

        add(new Label("gemVersion", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return ApplicationSession.get().getVersao();
            }
        }));

        add(new AbstractAjaxLink("linkSair") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                logout(target);
            }
        });

        add(new Label("competenciaAtual", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                Long diaInicioCompetencia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia();
                String competenciaAtual = "";
                if (diaInicioCompetencia != null) {
                    Date compAtual = Data.competenciaData(diaInicioCompetencia.intValue(), Data.getDataAtual());
                    competenciaAtual = new SimpleDateFormat("MM/yyyy").format(compAtual);
                }
                return competenciaAtual;
            }
        }));

        listView = getListView();
        listView.setOutputMarkupId(true);
        containerListView = new Form("containerListView");
        containerListView.setOutputMarkupId(true);
        containerListView.add(listView);
        form.add(containerListView);

        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(new Image("imgChrome", Resources.Images.CHROME.resourceReference()));
        form.add(new Image("imgFirefox", Resources.Images.FIREFOX.resourceReference()));

        String msgErro = "";
        StringValue stringValue = getPageParameters().get("erro");
        if (stringValue != null) {
            msgErro = stringValue.toString();
        }

        WebMarkupContainer panelErro = new WebMarkupContainer("panelErro");
        form.add(panelErro);
        Label lblErro = new Label("msgErro", msgErro);
        panelErro.add(lblErro);
        if (Coalesce.asString(msgErro).isEmpty()) {
            panelErro.setVisible(false);
        }

        form.add(txtBuscaUnidade = new InputField("buscaUnidade", new PropertyModel(this, "buscaUnidade")));
        txtBuscaUnidade.addAjaxUpdateValue();
        ajaxTimerDelayComponentBehavior = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                buscaUnidade(target, value);
            }
        };
        txtBuscaUnidade.add(ajaxTimerDelayComponentBehavior);

        add(form);
    }

    private void acessarSistema() throws ValidacaoException, DAOException {
        if (empresaSelecionada == null) {
            throw new ValidacaoException(BundleManager.getString("informe_unidade"));
        }

        if(usuario.getAceiteTermoUso() == null) {
            usuario.setAceiteTermoUso(RepositoryComponentDefault.NAO_LONG);
        }

        usuario = LoginHelper.iniciarSessao(usuario, empresaSelecionada);

        if (usuario.getProgramaWeb() != null && isMobile()) {
            getSession().setAttribute("mobilePaginaUnica", true);
            Class classe = buscarPrograma(usuario.getProgramaWeb());
            boolean pagePermitted = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), classe.getName());
            if (pagePermitted) {
                setResponsePage(classe);
            } else {
                throw new ValidacaoException(BundleManager.getString("msgUsuarioSemPermissao"));
            }
        } else if (usuario.getDataRegistro() == null) {
            setResponsePage(BemVindoPage.class);
        } else if (usuario.getDiasExpirarSenha() != null
                && Data.adjustRangeHour(Data.addDias(usuario.getDataRegistro(), usuario.getDiasExpirarSenha().intValue())).getDataInicial().before(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
            setResponsePage(SenhaExpiradaPage.class);
        } else if (usuario.getProgramaWeb() != null) {
            if (isMobile()) {
                getSession().setAttribute("mobilePaginaUnica", true);
            } else {
                getSession().setAttribute("mobilePaginaUnica", false);
            }
            Class classe = buscarPrograma(usuario.getProgramaWeb());
            boolean pagePermitted = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), classe.getName());
            if (pagePermitted) {
                setResponsePage(classe);
            } else {
                throw new ValidacaoException(BundleManager.getString("msgUsuarioSemPermissao"));
            }
        } else {
            setResponsePage(HomePage.class);
        }
    }

    private Component getImagePadrao() {
        Image imagemLogo = new Image("gemIco", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.CELK_SAUDE_LOGO.resourceReference();
            }
        });
        ;

        try {
            GerenciadorArquivo logoSist = CargaBasicoPadrao.getInstance().getParametroPadrao().getLogoSistema();
            if (logoSist != null && StringUtils.trimToNull(logoSist.getCaminho()) != null) {
                String path = new FtpImageUtil().downloadImage(logoSist.getCaminho());
                if (path != null) {
                    FileResourceStream fileResourceStream = new FileResourceStream(new File(path));
                    imagemLogo = new Image("gemIco", new ResourceStreamResource(fileResourceStream)) {
                        @Override
                        protected boolean getStatelessHint() {
                            return true;
                        }
                    };

                }
            }
        } catch (Throwable ex) {
            imagemLogo = new Image("gemIco", new LoadableDetachableModel<ResourceReference>() {
                @Override
                protected ResourceReference load() {
                    return Resources.Images.CELK_SAUDE_LOGO.resourceReference();
                }
            });
        }

        return imagemLogo;
    }

    private boolean isMobile() {
        String userAgent = WebSession.get().getClientInfo().getUserAgent().toLowerCase();
        if (userAgent.contains("android")
                || userAgent.contains("webos")
                || userAgent.contains("iphone")
                || userAgent.contains("ipad")
                || userAgent.contains("ipod")
                || userAgent.contains("blackberry")
                || userAgent.contains("windows phone")) {
            return true;
        }
        return false;
    }

    private Class buscarPrograma(ProgramaWeb programaWeb) {
        Class classe = null;
        programaWeb = LoadManager.getInstance(ProgramaWeb.class)
                .setId(programaWeb.getCodigo())
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .start().getVO();
        try {
            String caminhoPagina = programaWeb.getProgramaPaginaPrincipal().getCaminhoPagina();
            if (caminhoPagina != null) {
                classe = Class.forName(caminhoPagina);
            }
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return classe;
    }

    @Override
    public void modalWarn(AjaxRequestTarget target, Throwable ex) {
        modalMessage.warn(target, ex);
    }

    @Override
    public void modalError(AjaxRequestTarget target, Throwable ex) {
        modalMessage.error(target, ex);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(Resources.CSS_JQUERY_UI_CUSTOM));
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(OnDomReadyHeaderItem.forScript("$('body').find('form').keydown(function(ev){\n"
                + "        if(ev.which == 13 && ev.target.nodeName!='TEXTAREA') ev.preventDefault();\n"
                + "    });"));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_UNIDADE_LOGIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_SCROLL_INTO_VIEW));
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtBuscaUnidade)));
        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(txtBuscaUnidade, ajaxTimerDelayComponentBehavior.getCallbackScript().toString())));
    }

    private void erroAutenticacao(AjaxRequestTarget target, String message) {
        PageParameters pp = new PageParameters();
        pp.add("erro", message);

        Page page = new LoginUnidadePage(usuario, empresas, pp);

        setResponsePage(page);


    }

    public void logout(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(UsuarioFacade.class).logout(usuario);
        getSession().invalidate();

        try {
            this.integracaoLoginGovBrService = new IntegracaoLoginGovBrService();
            integracaoLoginGovBrService.logoutGovBr(target);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

      //  setResponsePage(LoginPage.class);
    }

    private ListView<ListView<Empresa>> getListView() {
        LoadableDetachableModel model = new LoadableDetachableModel<List<List<Empresa>>>() {
            @Override
            protected List<List<Empresa>> load() {
                List<List<Empresa>> matrizEmpresas = new ArrayList<List<Empresa>>();
                List<Empresa> linhaEmpresa;

                for (Iterator<Empresa> iterator = empresas.iterator(); iterator.hasNext(); ) {
                    linhaEmpresa = new ArrayList<Empresa>();
                    for (int i = 0; i < 3; i++) {
                        if (iterator.hasNext()) {
                            linhaEmpresa.add(iterator.next());
                        }
                    }
                    matrizEmpresas.add(linhaEmpresa);
                }

                return matrizEmpresas;
            }
        };

        ListView listView = new ListView("nodes", model) {
            @Override
            protected void populateItem(final ListItem item) {

                LoadableDetachableModel modelLinha = new LoadableDetachableModel<List<Empresa>>() {
                    @Override
                    protected List<Empresa> load() {
                        return (List<Empresa>) item.getModelObject();
                    }
                };

                ListView listViewLinha = new ListView("nodesLinha", modelLinha) {
                    @Override
                    protected void populateItem(ListItem item) {
                        item.setOutputMarkupId(true);
                        NodeButtonUnidades nodeButton;
                        item.add(nodeButton = new NodeButtonUnidades("btnNode", (Empresa) item.getModelObject()) {
                            @Override
                            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                LoginUnidadePage.this.empresaSelecionada = getUnidadeNode();
                                try {
                                    acessarSistema();
                                } catch (SGKException ex) {
                                    erroAutenticacao(target, ex.getMessage());
                                }
                            }
                        });
                        NodeButtonEventListener createListener = nodeButton.createListener();
                        nodeButtonListeners.add(createListener);
                    }
                };
                item.add(listViewLinha);
            }
        };
        return listView;
    }

    private void buscaUnidade(AjaxRequestTarget target, String value) {
        empresas.clear();
        if (StringUtils.trimToNull(value) == null) {
            empresas.addAll(empresasDoUsuario);
        } else {
            for (Empresa unidade : empresasDoUsuario) {
                if (StringUtil.removeAcentos(unidade.getDescricao()).toUpperCase().contains(StringUtil.removeAcentos(value).toUpperCase())) {
                    empresas.add(unidade);
                }
            }
        }
        target.add(containerListView);
    }

}
