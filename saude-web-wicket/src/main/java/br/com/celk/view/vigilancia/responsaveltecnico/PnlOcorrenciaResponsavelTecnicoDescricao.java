package br.com.celk.view.vigilancia.responsaveltecnico;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlOcorrenciaResponsavelTecnicoDescricao extends Panel{

    private InputArea txaDescricao;
    private String descricao;

    public PnlOcorrenciaResponsavelTecnicoDescricao(String id){
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        
        add(txaDescricao = new InputArea("descricao", new PropertyModel<String>(this, "descricao")));
        
        add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
    }
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, String descricao){
        txaDescricao.limpar(target);
        this.descricao = descricao;
    }
}