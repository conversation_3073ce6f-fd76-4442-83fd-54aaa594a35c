package br.com.celk.view.unidadesaude.esus.domicilio.tabbedpanel;

import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.esus.domicilio.CadastroDomicilioDTO;
import br.com.celk.esus.domicilio.ComponenteDomicilioDTO;
import br.com.celk.esus.domicilio.EnderecoEstruturadoDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Util;
import br.com.celk.view.unidadesaude.esus.domicilio.ConsultaDomicilioEsusPage;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static org.apache.commons.lang3.math.NumberUtils.LONG_ZERO;

/**
 * <AUTHOR>
 */
public class CadastroDomicilioEsusTabbedPanel extends CadastroTabbedPanel<CadastroDomicilioDTO> implements IAppletAction {

    public CadastroDomicilioEsusTabbedPanel(String id, CadastroDomicilioDTO object, boolean viewOnly, List<ITab> tabs) {
        super(id, object, viewOnly, tabs, true);
    }

    public CadastroDomicilioEsusTabbedPanel(String id, CadastroDomicilioDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public CadastroDomicilioEsusTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<CadastroDomicilioDTO> getReferenceClass() {
        return CadastroDomicilioDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaDomicilioEsusPage.class;
    }

    @Override
    public Object salvar(CadastroDomicilioDTO object) throws DAOException, ValidacaoException {
        if (object.getEnderecoDomicilioEsus() != null) {
            if  (RepositoryComponentDefault.NAO_LONG.equals(object.getEnderecoDomicilioEsus().getFlagForaArea())) {
                if (object.getEquipeArea() == null) { throw new ValidacaoException(Bundle.getStringApplication("msg_informe_equipe_area")); }
                if (getObject().getEnderecoDomicilio() != null && getObject().getEnderecoDomicilio().getEquipeMicroArea() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_microarea"));
                }
            }
            if (object.getEnderecoDomicilioEsus().getLocalizacao() == null
                    && (EnderecoDomicilioEsus.GRUPO_TIPO_IMOVEL_CONDICAO_MORADIA.contains(object.getEnderecoDomicilioEsus().getTipoImovel()))
                    && (!EnderecoDomicilioEsus.GRUPO_TIPO_IMOVEL_MORADIA_TEMPORARIA.contains(object.getEnderecoDomicilioEsus().getTipoImovel()))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_localizacao"));
            }
        }
        if (RepositoryComponentDefault.SIM.equals(object.getEnderecoDomicilio().getFlagBolsaFamilia()) && object.getEnderecoDomicilio().getUsuarioCadsusNis() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_responsavel_nis"));
        }

        if (object.getEnderecoDomicilio().getUsuarioCadsusNis() != null && object.getEnderecoDomicilio().getUsuarioCadsusNis().getNis() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_responsavel_nis_deve_possuir_nis_cadastrado"));
        }

        if (object.getEnderecoDomicilio() != null && object.getEnderecoDomicilio().getEnderecoUsuarioCadsus() != null
            && object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getComplementoLogradouro() != null
            && object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getComplementoLogradouro().length() > 30) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_complemento_enderco_max_30"));
        }

        if (object.isUtilizaEnderecoEstruturado()) {
            newEnderecoEstruturado();
        }
        object.getEnderecoDomicilio().setCidade(object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade());
        if (object.getEquipeArea() != null) {
            object.getEnderecoDomicilio().setArea(object.getEquipeArea().getCodigoArea());
        }
        String cnsDigits = StringUtils.trimToNull(StringUtilKsi.getDigits(object.getNumeroCartaoResponsavelTecnico()));
        if (cnsDigits != null) {
            if (!CnsValidator.validaCns(cnsDigits)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_cns_invalido"));
            }
            object.getEnderecoDomicilioEsus().setNumeroCartaoResponsavelTecnico(new Long(cnsDigits));
        } else {
            object.getEnderecoDomicilioEsus().setNumeroCartaoResponsavelTecnico(null);
        }

        List<ComponenteDomicilioDTO> responsavel = new ArrayList<>();

        for (ComponenteDomicilioDTO dto : object.getComponentes()) {
            if (StringUtils.trimToNull(Coalesce.asString(dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCelular())) != null) {
                dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().setCelular(StringUtilKsi.getDigits(dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCelular()));
                if (dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCelular().charAt(2) != '9'
                        && dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCelular().charAt(2) != '8'
                        && dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCelular().charAt(2) != '7') {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_O_Componente_X_Esta_Com_Telefone_Invalido", dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getDescricaoSocialFormatado()));
                }
            }

            if (RepositoryComponentDefault.SIM_LONG.equals(dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getFlagResponsavelFamiliar())) {
                List<UsuarioCadsusCns> lstUsuarioCadsusCns = new ArrayList();
                lstUsuarioCadsusCns = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).consultaCns((dto.getUsuarioCadsusDomicilio().getUsuarioCadsus()));
                if (CollectionUtils.isAllEmpty(lstUsuarioCadsusCns) && StringUtils.isEmpty(dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().getCpf())) {
                    throw new ValidacaoException(bundle("msgResponsavelSemCnsDefinido"));
                }
                dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().setResponsavel(RepositoryComponentDefault.SIM);
                responsavel.add(dto);
            } else {
                dto.getUsuarioCadsusDomicilio().getUsuarioCadsus().setResponsavel(RepositoryComponentDefault.NAO);
            }
        }

        if (responsavel.size() > 1) {
            throw new ValidacaoException(BundleManager.getString("msgComponentesResponsavelApenasUm"));
        }


        if (EnderecoDomicilioEsus.TipoImovel.ABRIGO.value().equals(object.getEnderecoDomicilioEsus().getTipoImovel())
                && object.getEnderecoDomicilioEsus().getNomeResponsavelTecnico() == null) {
            throw new ValidacaoException(BundleManager.getString("msgResponsavelTecnicoObrigatorio"));
        }

        if (EnderecoDomicilioEsus.TipoImovel.DOMICILIO.value().equals(object.getEnderecoDomicilioEsus().getTipoImovel()) &&
        (object.getEnderecoDomicilioEsus().getNumeroMoradores() != null && object.getEnderecoDomicilioEsus().getNumeroMoradores().longValue() > LONG_ZERO
                && !object.getComponentes().isEmpty() && responsavel.isEmpty()))  {
            throw new ValidacaoException(BundleManager.getString("msgComponentesResponsavelVazio"));
        }

        if (!CollectionUtils.isEmpty(responsavel)) {
            object.setResponsavel(responsavel.get(0));
        }

        if (object.getEquipeArea() != null && object.getEquipeArea().getCidade() != null &&
                object.getEnderecoDomicilio() != null && object.getEnderecoDomicilio().getCidade() != null &&
                !object.getEquipeArea().getCidade().getCodigo().equals(object.getEnderecoDomicilio().getCidade().getCodigo())) {

            throw new ValidacaoException(bundle("msgCidadeAreaDiferenteCidadeEndereco"));
        }

        object.getEnderecoDomicilioEsus().setNumeroMoradores((long) object.getComponentes().size());

        return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).salvarDomicilioEsus(object);
    }

    private void newEnderecoEstruturado() throws DAOException, ValidacaoException{
        EnderecoEstruturadoDTO enderecoEstruturadoDTO = object.getEnderecoEstruturadoDTO();
        EnderecoUsuarioCadsus enderecoUsuarioCadsus;
        if (object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCodigo() != null) {
            enderecoUsuarioCadsus = object.getEnderecoDomicilio().getEnderecoUsuarioCadsus();
        } else {
            enderecoUsuarioCadsus = new EnderecoUsuarioCadsus();
        }
        object.getEnderecoDomicilio().setEnderecoUsuarioCadsus(enderecoUsuarioCadsus);
        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setEnderecoEstruturado(enderecoEstruturadoDTO.getEnderecoEstruturado());
        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setNumeroLogradouro(enderecoEstruturadoDTO.getNumeroLogradouro());
        if (Util.isNotNull(enderecoEstruturadoDTO.getEnderecoEstruturado().getBairro())) {
            object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setBairro(enderecoEstruturadoDTO.getEnderecoEstruturado().getBairro().getDescricao());
        }else{
            throw new ValidacaoException(bundle("msgObrigatorioInformarBairro"));
        }
        if (Util.isNotNull(enderecoEstruturadoDTO.getEnderecoEstruturado().getCep())) {
            object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCep(enderecoEstruturadoDTO.getEnderecoEstruturado().getCep());
        }else{
            throw new ValidacaoException(bundle("msgObrigatorioInformarCEP"));
        }

        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setLogradouro(enderecoEstruturadoDTO.getEnderecoEstruturado().getEnderecoEstruturadoLogradouro().getDescricao());
        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setCidade(enderecoEstruturadoDTO.getCidadeEnderecoEstruturado());
        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setComplementoLogradouro(enderecoEstruturadoDTO.getComplemento());
        object.getEnderecoDomicilio().getEnderecoUsuarioCadsus().setTipoLogradouro(enderecoEstruturadoDTO.getEnderecoEstruturado().getEnderecoEstruturadoLogradouro().getTipoLogradouro());
    }

    @Override
    public void search(AjaxRequestTarget target, String key) {
    }

    @Override
    public void register(AjaxRequestTarget target, String key) {
    }

}
