package br.com.celk.view.comunicacao.mensagem.autocomplete;

import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.messaging.autocomplete.MessagingAutoComplete;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaUsuarioDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;

/**
 *
 * <AUTHOR>
 */
public class MessagingAutoCompleteUsuario extends MessagingAutoComplete<Usuario>{

    private boolean filtrarUsuariosAtivos;

    public MessagingAutoCompleteUsuario(String wicketId) {
        super(wicketId);
    }

    @Override
    public IConsultaConfigurator getConsultaConfiguratorInstance() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return null;
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Usuario, QueryConsultaUsuarioDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaUsuarioDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarUsuario(dataPaging);
                    }

                    @Override
                    public QueryConsultaUsuarioDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaUsuarioDTOParam param = new QueryConsultaUsuarioDTOParam();
                        param.setKeyword(searchCriteria);
                        param.setFiltrarUsuariosAtivos(isFiltrarUsuariosAtivos());
                        return param;
                    }
                    
                };
            }

            @Override
            public Class getReferenceClass() {
                return Usuario.class;
            }
        };
    }

    public boolean isFiltrarUsuariosAtivos() {
        return filtrarUsuariosAtivos;
    }

    public void setFiltrarUsuariosAtivos(boolean filtrarUsuariosAtivos) {
        this.filtrarUsuariosAtivos = filtrarUsuariosAtivos;
    }
}
