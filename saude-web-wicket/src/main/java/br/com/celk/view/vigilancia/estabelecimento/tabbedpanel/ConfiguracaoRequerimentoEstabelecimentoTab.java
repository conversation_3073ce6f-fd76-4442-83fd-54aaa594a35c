package br.com.celk.view.vigilancia.estabelecimento.tabbedpanel;

import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkgroup.CheckBoxGroup;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroEstabelecimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ConfiguracaoRequerimentoEstabelecimentoTab extends TabPanel<CadastroEstabelecimentoDTO> {

    private Form<CadastroEstabelecimentoDTO> form;
    private TextArea txtObservacao;
    private InputField txtAlvara;
    private DateChooser dcDataValidadeAlvara;
    private DisabledInputField txtAreaAlvara;
    private InputField txtNumeroAutorizacaoSanitaria;
    private DateChooser dcDataValidadeAutorizacao;
    private DateChooser dcDataValidadePrimeiroAlvara;
    private DateChooser dcDataValidadeUltimaAutorizacao;
    private DateChooser dcDataValidadePrimeiraLicenca;
    private DateChooser dcDataValidadePrimeiroCredenciamento;
    private DropDown<Long> dropDownEmiteReceita;
    private CheckBoxLongValue chxReceitaB1;
    private CheckBoxLongValue chxReceitaB2;
    private CheckBoxLongValue chxReceitaC2;

    private CheckBoxGroup lstMedicamentosDrogaria;
    private CheckBoxGroup lstMedicamentosFarmacia;
    private CheckBoxGroup lstMedicamentosFarmaciaGrupoIII;
    private WebMarkupContainer containerDrogaria;
    private WebMarkupContainer containerFarmacia;
    private DropDown<Long> dropDownFarmacia;
    private DropDown<Long> dropDownDrogaria;
    private DropDown dropDownFormaFaturamento;
    private AutoCompleteConsultaUsuario autoCompleteUsuarioResponsavel;

    private WebMarkupContainer containerOcorrencias;
    private Table tblOcorrencias;

    private static final String CSS_FILE = "ConfiguracaoRequerimentoEstabelecimentoTab.css";
    private CssResourceReference cssResourceReference;

    public ConfiguracaoRequerimentoEstabelecimentoTab(String id, CadastroEstabelecimentoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        form = new Form("form", new CompoundPropertyModel(object));
        CadastroEstabelecimentoDTO proxy = on(CadastroEstabelecimentoDTO.class);

        dropDownFormaFaturamento = DropDownUtil.getIEnumDropDown(path(proxy.getEstabelecimento().getFormaFaturamento()), Estabelecimento.FormaFaturamento.values());
        dropDownFormaFaturamento.setLabel(new Model(bundle("formaFaturamento")));

        txtObservacao = new TextArea(path(proxy.getEstabelecimento().getObservacaoDestaqueAlvara()));
        txtObservacao.add(new Tooltip().setText("msgTextoExibidoImpressaoAlvaraDepoisValidadeEmDestaque"));
        txtObservacao.setOutputMarkupId(true);

        txtAreaAlvara = new DisabledInputField(path(proxy.getEstabelecimento().getAreaAlvara()));
        txtAreaAlvara.setOutputMarkupId(true);

        // A regra de desabilitar o campo abaixo serve para não deixar o usuário editar um alvará originado via sistema - #14235
        boolean existAlvaraSistema = false;
        if(object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() != null){
            existAlvaraSistema = VigilanciaHelper.existAlvaraSistema(object.getEstabelecimento());
        }
        txtAlvara = new InputField<String>(path(proxy.getEstabelecimento().getAlvara()));
        txtAlvara.setEnabled(!existAlvaraSistema);

        dcDataValidadeAlvara = new DateChooser(path(proxy.getEstabelecimento().getValidadeAlvara()));
        dcDataValidadeAlvara.setEnabled(!existAlvaraSistema);
        dcDataValidadeAlvara.setOutputMarkupPlaceholderTag(true);
        dcDataValidadeAlvara.addAjaxUpdateValue();

        dcDataValidadePrimeiroAlvara = new DateChooser(path(proxy.getEstabelecimento().getDataValidadePrimeiroAlvara()));
        dcDataValidadePrimeiroAlvara.addAjaxUpdateValue();
        dcDataValidadePrimeiroAlvara.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (object.getEstabelecimento() == null || object.getEstabelecimento().getCodigo() == null
                        || !VigilanciaHelper.existAlvaraSistema(object.getEstabelecimento())) {
                    dcDataValidadeAlvara.limpar(target);

                    if (dcDataValidadePrimeiroAlvara.getComponentValue() != null) {
                        dcDataValidadeAlvara.setComponentValue(dcDataValidadePrimeiroAlvara.getComponentValue());
                    }

                    target.add(dcDataValidadeAlvara);
                }
            }
        });

        // A regra de desabilitar o campo abaixo serve para não deixar o usuário editar um alvará originado via sistema - #14235
        boolean existAutorizacaoSistema = false;
        if(object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() != null){
            existAutorizacaoSistema = VigilanciaHelper.existAutorizacaoSanitaria(object.getEstabelecimento());
        }

        txtNumeroAutorizacaoSanitaria = new InputField<String>(path(proxy.getEstabelecimento().getNumeroAutorizacaoSanitaria()));
        txtNumeroAutorizacaoSanitaria.setEnabled(!existAutorizacaoSistema);
        txtNumeroAutorizacaoSanitaria.setOutputMarkupPlaceholderTag(true);

        dcDataValidadeAutorizacao = new DateChooser(path(proxy.getEstabelecimento().getValidadeAutorizacaoSanitaria()));
        dcDataValidadeAutorizacao.setEnabled(!existAutorizacaoSistema);
        dcDataValidadeAutorizacao.setOutputMarkupPlaceholderTag(true);
        dcDataValidadeAutorizacao.setOutputMarkupId(true);
        dcDataValidadeAutorizacao.addAjaxUpdateValue();

        dcDataValidadeUltimaAutorizacao = new DateChooser(path(proxy.getEstabelecimento().getDataValidadePrimeiraAutorizacaoSanitaria()));
        dcDataValidadeUltimaAutorizacao.addAjaxUpdateValue();

        dcDataValidadeUltimaAutorizacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(object.getEstabelecimento() == null || object.getEstabelecimento().getCodigo() == null
                        || !VigilanciaHelper.existAutorizacaoSanitaria(object.getEstabelecimento())){
                    dcDataValidadeAutorizacao.limpar(target);

                    if(dcDataValidadeUltimaAutorizacao.getComponentValue() != null){
                        dcDataValidadeAutorizacao.setComponentValue(dcDataValidadeUltimaAutorizacao.getComponentValue());
                    }

                    target.add(dcDataValidadeAutorizacao);
                }
            }
        });

        dcDataValidadePrimeiraLicenca = new DateChooser(path(proxy.getEstabelecimento().getDataValidadePrimeiraLicenca()));
        dcDataValidadePrimeiroCredenciamento = new DateChooser(path(proxy.getEstabelecimento().getDataValidadePrimeiroCredenciamento()));

        dropDownEmiteReceita = DropDownUtil.getNaoSimLongDropDown(path(proxy.getEstabelecimento().getEmiteReceita()));
        dropDownEmiteReceita.setLabel(new Model(bundle("solicitaReceituario")));
        dropDownEmiteReceita.add(new Tooltip().setText("msgToolTipoPermiteReceituarioVigilanciaEstab"));

        chxReceitaB1 = new CheckBoxLongValue(path(proxy.getEstabelecimento().getFlagSolicReceitaB1()));
        chxReceitaB2 = new CheckBoxLongValue(path(proxy.getEstabelecimento().getFlagSolicReceitaB2()));
        chxReceitaC2 = new CheckBoxLongValue(path(proxy.getEstabelecimento().getFlagSolicReceitaC2()));

        containerDrogaria = new WebMarkupContainer("containerDrogaria");
        containerDrogaria.setOutputMarkupId(true);
        containerDrogaria.setOutputMarkupPlaceholderTag(true);
        containerDrogaria.setVisible(isContainerDrogariaVisible() || isContainerFarmaciaVisible());

        containerFarmacia = new WebMarkupContainer("containerFarmacia");
        containerFarmacia.setOutputMarkupId(true);
        containerFarmacia.setOutputMarkupPlaceholderTag(true);
        containerFarmacia.setVisible(isContainerFarmaciaVisible());

        dropDownDrogaria = DropDownUtil.getNaoSimLongDropDown(path(proxy.getEstabelecimento().getFlagDrogaria()));
        dropDownDrogaria.addAjaxUpdateValue();
        dropDownDrogaria.setOutputMarkupId(true);

        dropDownFarmacia = DropDownUtil.getNaoSimLongDropDown(path(proxy.getEstabelecimento().getFlagFarmacia()));
        dropDownFarmacia.addAjaxUpdateValue();
        dropDownFarmacia.setOutputMarkupId(true);

        dropDownDrogaria.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(dropDownDrogaria.getComponentValue())) {
                    containerDrogaria.setVisible(true);
                } else if (RepositoryComponentDefault.NAO_LONG.equals(dropDownFarmacia.getComponentValue())) {
                    lstMedicamentosDrogaria.limpar(target);
                    lstMedicamentosFarmacia.limpar(target);
                    lstMedicamentosFarmaciaGrupoIII.limpar(target);
                    containerDrogaria.setVisible(false);
                    containerFarmacia.setVisible(false);
                }
                target.add(containerDrogaria);
                target.add(containerFarmacia);
            }
        });

        dropDownFarmacia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(dropDownFarmacia.getComponentValue())) {
                    containerFarmacia.setVisible(true);
                    containerDrogaria.setVisible(true);
                } else if (RepositoryComponentDefault.NAO_LONG.equals(dropDownDrogaria.getComponentValue())) {
                    lstMedicamentosDrogaria.limpar(target);
                    lstMedicamentosFarmacia.limpar(target);
                    lstMedicamentosFarmaciaGrupoIII.limpar(target);
                    containerDrogaria.setVisible(false);
                    containerFarmacia.setVisible(false);
                } else if (RepositoryComponentDefault.SIM_LONG.equals(dropDownDrogaria.getComponentValue())) {
                    containerFarmacia.setVisible(false);
                    lstMedicamentosFarmacia.limpar(target);
                    lstMedicamentosFarmaciaGrupoIII.limpar(target);
                }
                target.add(containerFarmacia);
                target.add(containerDrogaria);
            }
        });

        resolverSomatorios();

        this.cssResourceReference = new CssResourceReference(DadosComplementoEstabelecimentoTab.class, CSS_FILE);

        containerDrogaria.add(lstMedicamentosDrogaria = new CheckBoxGroup("lstMedicamentosDrogaria", Estabelecimento.GrupoMedicamentoDrogaria.values()) {
            @Override
            public CssResourceReference getCssResourceReference() {
                return ConfiguracaoRequerimentoEstabelecimentoTab.this.cssResourceReference;
            }
        });

        containerFarmacia.add(lstMedicamentosFarmacia = new CheckBoxGroup("lstMedicamentosFarmacia", Estabelecimento.GrupoMedicamento.values()) {
            @Override
            public CssResourceReference getCssResourceReference() {
                return ConfiguracaoRequerimentoEstabelecimentoTab.this.cssResourceReference;
            }
        });

        containerFarmacia.add(lstMedicamentosFarmaciaGrupoIII = new CheckBoxGroup("lstMedicamentosFarmaciaGrupoIII", Estabelecimento.GrupoMedicamentoFarmaciaGrupoIII.values()) {
            @Override
            public CssResourceReference getCssResourceReference() {
                return ConfiguracaoRequerimentoEstabelecimentoTab.this.cssResourceReference;
            }
        });

        autoCompleteUsuarioResponsavel = new AutoCompleteConsultaUsuario(path(proxy.getEstabelecimento().getUsuarioResponsavel()));
        autoCompleteUsuarioResponsavel.setOutputMarkupPlaceholderTag(true);
        autoCompleteUsuarioResponsavel.setTipoUsuario(Usuario.TipoUsuario.USUARIO_VIGILANCIA.value());

        containerOcorrencias = new WebMarkupContainer("containerOcorrencias");
        containerOcorrencias.setOutputMarkupId(true);
        tblOcorrencias = new Table("tblOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias());
        tblOcorrencias.populate();
        containerOcorrencias.add(tblOcorrencias);

        form.add(dropDownFormaFaturamento, txtObservacao, txtAreaAlvara, txtAlvara, dcDataValidadeAlvara, dcDataValidadePrimeiroAlvara,
                txtNumeroAutorizacaoSanitaria, dcDataValidadeAutorizacao, dcDataValidadeUltimaAutorizacao, dcDataValidadePrimeiraLicenca,
                dcDataValidadePrimeiroCredenciamento, dropDownEmiteReceita, chxReceitaB1, chxReceitaB2, chxReceitaC2, containerDrogaria,
                containerFarmacia, dropDownDrogaria, dropDownFarmacia, autoCompleteUsuarioResponsavel, containerOcorrencias
        );

        add(form);
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList<>();

        EstabelecimentoOcorrencia proxy = on(EstabelecimentoOcorrencia.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataOcorrencia())));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderOcorrencias() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModel().getObject().getEstabelecimentoOcorrenciaList();
            }
        };
    }

    private boolean isContainerDrogariaVisible() {
        if (object.getEstabelecimento().getFlagDrogaria() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagDrogaria())){
                return true;
            }
        }
        if (object.getEstabelecimento().getFlagFarmacia() != null) {
            return RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagFarmacia());
        }
        return false;
    }

    private boolean isContainerFarmaciaVisible() {
        if (object.getEstabelecimento().getFlagFarmacia() != null) {
            return RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagFarmacia());
        }
        return false;
    }

    private void resolverSomatorios() {
        List<Long> lstMedicamentosDrogaria = Valor.resolveSomatorio(Coalesce.asLong(object.getEstabelecimento().getTipoGrupoDrogariaSomatorio()));
        List<Long> lstMedicamentosFarmacia = Valor.resolveSomatorio(Coalesce.asLong(object.getEstabelecimento().getTipoGrupoFarmaciaSomatorio()));
        List<Long> lstMedicamentosFarmaciaGrupoIII = Valor.resolveSomatorio(Coalesce.asLong(object.getEstabelecimento().getTipoGrupoFarmaciaGrupoIIISomatorio()));

        object.setLstMedicamentosDrogaria(lstMedicamentosDrogaria);
        object.setLstMedicamentosFarmacia(lstMedicamentosFarmacia);
        object.setLstMedicamentosFarmaciaGrupoIII(lstMedicamentosFarmaciaGrupoIII);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("configRequerimentos");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtObservacao;
    }
}
