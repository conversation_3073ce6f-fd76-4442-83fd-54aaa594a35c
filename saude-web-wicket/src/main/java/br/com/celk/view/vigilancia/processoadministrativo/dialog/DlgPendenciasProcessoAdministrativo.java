package br.com.celk.view.vigilancia.processoadministrativo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroProcessoAdministrativoOcorrenciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.DlgProcessoAdministrativoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgPendenciasProcessoAdministrativo extends Window {

    private PnlPendenciasProcessoAdministrativo pnlPendenciasProcessoAdministrativo;

    public DlgPendenciasProcessoAdministrativo(String id) {
        super(id);
        init();
    }


    private void init() {
        setTitle(new LoadableDetachableModel<String>(){

            @Override
            protected String load(){
                return BundleManager.getString("pendencias");
            }
        });

        setInitialWidth(980);
        setInitialHeight(700);
        setResizable(true);

        setContent(pnlPendenciasProcessoAdministrativo = new PnlPendenciasProcessoAdministrativo(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgPendenciasProcessoAdministrativo.this.onFechar(target);
                close(target);
            }
        });
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target){
        super.show(target);
        try {
            pnlPendenciasProcessoAdministrativo.setObject(target);
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.error(e);
        }
    }

}