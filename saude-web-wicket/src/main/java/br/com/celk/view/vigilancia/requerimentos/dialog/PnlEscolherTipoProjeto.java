package br.com.celk.view.vigilancia.requerimentos.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoAnaliseProjetosPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.RequerimentoVistoriaProjetoBasicoArquiteturaPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.analisehidrossanitariodeclaratorio.RequerimentoHidrossanitarioDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.habitesedeclaratorio.RequerimentoHabiteseDeclaratorioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetoArquitetonicoSanitario.RequerimentoProjetoArquitetonicoSanitarioPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoHabitesePadraoPage;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario.RequerimentoProjetoHidrossanitarioPadraoPage;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlEscolherTipoProjeto extends Panel {

    private Form form;
    private AbstractAjaxButton btnFechar;
    private TipoSolicitacao tipoSolicitacaoSelecionado;

    public PnlEscolherTipoProjeto(String id,
                                  Class clazz,
                                  boolean possuiPermissaoPBA,
                                  boolean possuiPermissaoVLTPBA,
                                  boolean possuiPermissaoAPH,
                                  boolean possuiPermissaoVHS,
                                  boolean possuiPermissaoAPHD,
                                  boolean possuiPermissaoHD,
                                  boolean possuiPermissaoPAS) {
        super(id);
        init(clazz, possuiPermissaoPBA, possuiPermissaoVLTPBA, possuiPermissaoAPH, possuiPermissaoVHS, possuiPermissaoAPHD, possuiPermissaoHD, possuiPermissaoPAS);
    }

    private void init(
            final Class clazz,
            boolean possuiPermissaoPBA,
            boolean possuiPermissaoVLTPBA,
            boolean possuiPermissaoAPH,
            boolean possuiPermissaoVHS,
            boolean possuiPermissaoAPHD,
            boolean possuiPermissaoHD,
            boolean possuiPermissaoPAS) {
        setOutputMarkupId(true);

        form = new Form("form", new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);


        form.add(new AbstractAjaxButton("btnPBA") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value());
                setResponsePage(new RequerimentoAnaliseProjetosPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoPBA));

        form.add(new AbstractAjaxButton("btnVLTPBA") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_LAUDO_CONFORMIDADE_PBA.value());
                setResponsePage(new RequerimentoVistoriaProjetoBasicoArquiteturaPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoVLTPBA));

        form.add(new AbstractAjaxButton("btnAPHD") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value());
                setResponsePage(new RequerimentoHidrossanitarioDeclaratorioPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAPHD));

        form.add(new AbstractAjaxButton("btnHD") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value());
                setResponsePage(new RequerimentoHabiteseDeclaratorioPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoHD));

        form.add(new AbstractAjaxButton("btnAPH") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value());
                setResponsePage(new RequerimentoProjetoHidrossanitarioPadraoPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAPH));

        form.add(new AbstractAjaxButton("btnVHS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value());
                setResponsePage(new RequerimentoHabitesePadraoPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoAPH));

        form.add(new AbstractAjaxButton("btnPAS") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                tipoSolicitacaoSelecionado.setTipoDocumento(TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value());
                setResponsePage(new RequerimentoProjetoArquitetonicoSanitarioPage(tipoSolicitacaoSelecionado, clazz));
                onFechar(target);
            }
        }.setVisible(possuiPermissaoPAS));


        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacaoSelecionado = tipoSolicitacao;
    }

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    public void limpar(AjaxRequestTarget target) {
    }

}
