package br.com.celk.view.vigilancia.estabelecimento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.Coalesce;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.estabelecimentoatividade.AutoCompleteConsultaAtividadeEstabelecimentoMulti;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EstabelecimentoSetoresAtividadeDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetoresAtividade;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlvara;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DadosSetoresEstabelecimentoTab extends TabPanel<CadastroEstabelecimentoDTO> {

    private Form<EstabelecimentoSetoresAtividadeDTO> form;
    private Table<EstabelecimentoSetoresAtividadeDTO> table;
    private List<EstabelecimentoSetoresAtividadeDTO> estabelecimentoSetoresDTOList;
    private List<EstabelecimentoSetoresAtividadeDTO> estabelecimentoSetoresDTOSaveList;
    private InputField<String> descricaoSetor;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavelTecnico;
    private boolean edit;
    private EstabelecimentoSetoresAtividadeDTO editedSetor = null;
    private AbstractAjaxButton btnLimpar;
    private AutoCompleteConsultaAtividadeEstabelecimentoMulti autoCompleteConsultaAtividadeEstabelecimentoMulti;

    public DadosSetoresEstabelecimentoTab(String id, CadastroEstabelecimentoDTO object) {
        super(id, object);
        estabelecimentoSetoresDTOList =EstabelecimentoSetoresAtividadeDTO.montaEstabelecimentoSetoresAtividadeDTOLinhasList(object.getEstabelecimentoSetoresList());
        estabelecimentoSetoresDTOSaveList = EstabelecimentoSetoresAtividadeDTO.montaEstabelecimentoSetoresAtividadeDTOList(object.getEstabelecimentoSetoresList());
        object.setEstabelecimentoSetoresDTOList(estabelecimentoSetoresDTOList);
        init();
    }

    //TODO: adaptar no cadastro do estabelecimento para retornar o DTO
    private List<EstabelecimentoSetoresAtividadeDTO> montaDTOList(List<EstabelecimentoSetores> estabelecimentoSetoresList) {
        List<EstabelecimentoSetoresAtividadeDTO> retorno = new ArrayList<>();
        for(EstabelecimentoSetores estabelecimentoSetores: estabelecimentoSetoresList) {
            EstabelecimentoSetoresAtividadeDTO dto = new EstabelecimentoSetoresAtividadeDTO();
            dto.setEstabelecimentoSetores(estabelecimentoSetores);
            List<EstabelecimentoSetoresAtividade> estabelecimentoSetoresAtividadeList = LoadManager.getInstance(EstabelecimentoSetoresAtividade.class)
                    .addProperties(new HQLProperties(EstabelecimentoSetoresAtividade.class).getProperties())
                    .addProperties(new HQLProperties(EstabelecimentoAtividade.class, EstabelecimentoSetoresAtividade.PROP_ESTABELECIMENTO_ATIVIDADE).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoSetoresAtividade.PROP_ESTABELECIMENTO_SETORES, estabelecimentoSetores))
                    .start().getList();
            List<EstabelecimentoAtividade> estabelecimentoAtividadeList = Lambda.extract(estabelecimentoSetoresAtividadeList, on(EstabelecimentoSetoresAtividade.class).getEstabelecimentoAtividade());
            dto.setEstabelecimentoAtividadeMulti(estabelecimentoAtividadeList);

            retorno.add(dto);
        }
        return retorno;
    }

    public void init() {
        EstabelecimentoSetoresAtividadeDTO proxy = on(EstabelecimentoSetoresAtividadeDTO.class);

        form = new Form("form", new CompoundPropertyModel(new EstabelecimentoSetoresAtividadeDTO()));
        form.add(descricaoSetor = new InputField<String>(path(proxy.getEstabelecimentoSetores().getDescricaoSetor())));
        form.add(getAutoCompleteConsultaEstabelecimentoAtividadeMulti(path(proxy.getAtividadeEstabelecimentoMulti())));
        form.add(autoCompleteConsultaResponsavelTecnico = new AutoCompleteConsultaResponsavelTecnico(path(proxy.getEstabelecimentoSetores().getResponsavelTecnico())));

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
                target.focusComponent(descricaoSetor);
            }
        });
        form.add(btnLimpar = new AbstractAjaxButton("btnLimpar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparForm(target);
                target.focusComponent(descricaoSetor);
            }
        });

        add(form);
    }

    private AutoCompleteConsultaAtividadeEstabelecimentoMulti getAutoCompleteConsultaEstabelecimentoAtividadeMulti(String id) {
        autoCompleteConsultaAtividadeEstabelecimentoMulti = new AutoCompleteConsultaAtividadeEstabelecimentoMulti(id);
        autoCompleteConsultaAtividadeEstabelecimentoMulti.setOutputMarkupId(true);
        autoCompleteConsultaAtividadeEstabelecimentoMulti.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaAtividadeEstabelecimentoMulti.setCodigoEstabelecimento(object.getEstabelecimento().getCodigo());
       autoCompleteConsultaAtividadeEstabelecimentoMulti.setEstabelecimentoAtividadeList(object.getEstabelecimentoAtividadeList());

        return autoCompleteConsultaAtividadeEstabelecimentoMulti;
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        EstabelecimentoSetoresAtividadeDTO setor = form.getModel().getObject();
        table.setEnabled(true);
        descricaoSetor.setEnabled(true);
        btnLimpar.setEnabled(true);

        if(edit){
            if (VigilanciaHelper.existsRequerimentoEmAndamento(this.object.getEstabelecimento(), setor.getEstabelecimentoSetores())) {
                throw new ValidacaoException(bundle("naoEPossivelIncluirCNAEEnquantoSetorPossuirAlvaraEmAndamento"));

            }
            if(setor.getAtividadeEstabelecimentoMulti() != null && !setor.getAtividadeEstabelecimentoMulti().isEmpty()) {
                for (AtividadeEstabelecimento atividadeEstabelecimento : setor.getAtividadeEstabelecimentoMulti()) {
                    if (Lambda.extract(estabelecimentoSetoresDTOList, Lambda.on(EstabelecimentoSetoresAtividadeDTO.class).getAtividadeEstabelecimentoMulti()).contains(atividadeEstabelecimento)) {
                        throw new ValidacaoException(bundle("atividadeCnae") + " já informado");
                    }
                }
            } else{
                throw new ValidacaoException(bundle("informeAtividadeCnae"));
            }
            replicaSetorComAtividadeDiferente(setor);
            estabelecimentoSetoresDTOSaveList.add(editedSetor);
        } else{
            if (setor.getEstabelecimentoSetores() == null || Coalesce.asString(setor.getEstabelecimentoSetores().getDescricaoSetor()).trim().isEmpty()) {
                throw new ValidacaoException(bundle("informeDescricaoSetor"));
            }
            if (Lambda.extract(estabelecimentoSetoresDTOList, Lambda.on(EstabelecimentoSetoresAtividadeDTO.class).getEstabelecimentoSetores().getDescricaoSetor()).contains(setor.getEstabelecimentoSetores().getDescricaoSetor())) {
                throw new ValidacaoException(bundle("setorJaInformado"));
            }
            if(setor.getAtividadeEstabelecimentoMulti() != null && !setor.getAtividadeEstabelecimentoMulti().isEmpty()) {
                for (AtividadeEstabelecimento atividadeEstabelecimento : setor.getAtividadeEstabelecimentoMulti()) {
                    if (Lambda.extract(estabelecimentoSetoresDTOList, Lambda.on(EstabelecimentoSetoresAtividadeDTO.class).getAtividadeEstabelecimentoMulti()).contains(atividadeEstabelecimento)) {
                        throw new ValidacaoException(bundle("atividadeCnae") + " já informado");
                    }
                }
            } else {
                throw new ValidacaoException(bundle("informeAtividadeCnae"));
            }
            setor.getEstabelecimentoSetores().setStatus(RepositoryComponentDefault.ATIVO);
            replicaSetorComAtividadeDiferente(setor);
            estabelecimentoSetoresDTOSaveList.add(setor);
        }

        table.populate();
        table.update(target);

        object.setEstabelecimentoSetoresDTOList(estabelecimentoSetoresDTOSaveList);
        limparForm(target);

    }

    private void replicaSetorComAtividadeDiferente(EstabelecimentoSetoresAtividadeDTO setor) {
        for (AtividadeEstabelecimento atividadeEstabelecimento : setor.getAtividadeEstabelecimentoMulti()) {
            EstabelecimentoSetoresAtividadeDTO dto = VOUtils.cloneObject(setor);
            dto.setAtividadeEstabelecimento(atividadeEstabelecimento);
            dto.setAtividadeEstabelecimentoMulti(setor.getAtividadeEstabelecimentoMulti());
            estabelecimentoSetoresDTOList.add(dto);
        }
    }

    private void limparForm(AjaxRequestTarget target) {
        form.getModel().setObject(new EstabelecimentoSetoresAtividadeDTO());
        autoCompleteConsultaResponsavelTecnico.limpar(target);
        autoCompleteConsultaAtividadeEstabelecimentoMulti.limpar(target);
        descricaoSetor.limpar(target);
        target.add(form);
    }


    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        EstabelecimentoSetoresAtividadeDTO proxy = on(EstabelecimentoSetoresAtividadeDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("setor"), proxy.getEstabelecimentoSetores().getDescricaoSetor()));
        columns.add(createColumn(bundle("responsavelTecnico"), proxy.getEstabelecimentoSetores().getResponsavelTecnico().getNome()));
        columns.add(createColumn(bundle("atividadeCnae"), proxy.getAtividadeEstabelecimento().getDescricao()));
        columns.add(createColumn(bundle("validadeAlvara"), proxy.getEstabelecimentoSetores().getDataValidadeAlvara()));
        columns.add(createColumn(bundle("situacao"), proxy.getEstabelecimentoSetores().getStatusFormatado()));

        return columns;
    }


    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EstabelecimentoSetoresAtividadeDTO>() {
            @Override
            public void customizeColumn(final EstabelecimentoSetoresAtividadeDTO rowObject) {
                addAction(ActionType.REATIVAR, rowObject, new IModelAction<EstabelecimentoSetoresAtividadeDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
                        modelObject.getEstabelecimentoSetores().setStatus(RepositoryComponentDefault.ATIVO);
                        table.populate(target);
                        target.focusComponent(descricaoSetor);
                    }
                }).setVisible(RepositoryComponentDefault.INATIVO.equals(rowObject.getEstabelecimentoSetores().getStatus()));

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<EstabelecimentoSetoresAtividadeDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
                        modelObject.getEstabelecimentoSetores().setStatus(RepositoryComponentDefault.INATIVO);
                        table.populate(target);
                        target.focusComponent(descricaoSetor);
                    }
                }).setVisible(RepositoryComponentDefault.ATIVO.equals(rowObject.getEstabelecimentoSetores().getStatus()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoSetoresAtividadeDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
                        removeSetor(target, modelObject);
                        target.focusComponent(descricaoSetor);
                    }
                }).setEnabled(!existsRequerimentoAlvara(rowObject));

                addAction(ActionType.EDITAR, rowObject, new IModelAction<EstabelecimentoSetoresAtividadeDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
                        actionEditarEstabelecimentoSetores(target, modelObject);
                    }
                });
            }
        };
    }

    private void actionEditarEstabelecimentoSetores(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
        form.getModel().setObject(modelObject);
        if(modelObject.getEstabelecimentoSetores().getDescricaoSetor() != null) {
            descricaoSetor.setComponentValue(modelObject.getEstabelecimentoSetores().getDescricaoSetor());
        }
        if(modelObject.getEstabelecimentoSetores().getResponsavelTecnico() != null){
            autoCompleteConsultaResponsavelTecnico.setModelObject(modelObject.getEstabelecimentoSetores().getResponsavelTecnico());
            autoCompleteConsultaResponsavelTecnico.setComponentValue(target, modelObject.getEstabelecimentoSetores().getResponsavelTecnico());
        }

        if(modelObject.getAtividadeEstabelecimentoMulti() != null){
            autoCompleteConsultaAtividadeEstabelecimentoMulti.setConvertedInput(modelObject.getAtividadeEstabelecimentoMulti());
//            autoCompleteConsultaAtividadeEstabelecimentoMulti.setConvertedInput(extractAtividadeEstabelecimentoFromEstabelecimentoAtividade(modelObject.getEstabelecimentoAtividadeMulti()));
            autoCompleteConsultaAtividadeEstabelecimentoMulti.updateModel();
            for(AtividadeEstabelecimento atividadeEstabelecimento: modelObject.getAtividadeEstabelecimentoMulti()) {
                autoCompleteConsultaAtividadeEstabelecimentoMulti.add(target, atividadeEstabelecimento);
            }
        }

        removeSetor(target, modelObject);

        editedSetor = modelObject;
        edit = true;
        table.setEnabled(false);
        table.update(target);
        descricaoSetor.setEnabled(false);
        btnLimpar.setEnabled(false);
        target.add(form);
        target.add(autoCompleteConsultaResponsavelTecnico);
        target.add(descricaoSetor);
        target.add(autoCompleteConsultaAtividadeEstabelecimentoMulti);
    }

    private List<AtividadeEstabelecimento> extractAtividadeEstabelecimentoFromEstabelecimentoAtividade(List<EstabelecimentoAtividade> estabelecimentoAtividadeMulti) {
        return Lambda.extract(estabelecimentoAtividadeMulti, on(EstabelecimentoAtividade.class).getAtividadeEstabelecimento());
    }

    private void removeSetor(AjaxRequestTarget target, EstabelecimentoSetoresAtividadeDTO modelObject) throws ValidacaoException, DAOException {
        List<EstabelecimentoSetoresAtividadeDTO> setoresCrudRemove = Lambda.select(estabelecimentoSetoresDTOList,
                Lambda.having(on(EstabelecimentoSetoresAtividadeDTO.class).getEstabelecimentoSetores().getDescricaoSetor(),
                        Matchers.equalTo(modelObject.getEstabelecimentoSetores().getDescricaoSetor())));
        for(EstabelecimentoSetoresAtividadeDTO setoresRemove: setoresCrudRemove) {
            CrudUtils.removerItem(target, table, estabelecimentoSetoresDTOList, setoresRemove);
        }

        if (object.getEstabelecimentoSetoresList() != null && !object.getEstabelecimentoSetoresList().isEmpty()) {
            object.getEstabelecimentoSetoresList().remove(modelObject.getEstabelecimentoSetores());
        }
        object.setEstabelecimentoSetoresDTOList(estabelecimentoSetoresDTOList);

        Collection<EstabelecimentoSetoresAtividadeDTO> setoresCrudRemove2 = Lambda.selectDistinct(Lambda.select(estabelecimentoSetoresDTOSaveList,
                Lambda.having(on(EstabelecimentoSetoresAtividadeDTO.class).getEstabelecimentoSetores().getDescricaoSetor(),
                        Matchers.equalTo(modelObject.getEstabelecimentoSetores().getDescricaoSetor()))));
        for(EstabelecimentoSetoresAtividadeDTO setoresRemove: setoresCrudRemove2) {
            estabelecimentoSetoresDTOSaveList.remove(setoresRemove);
        }
    }

    private boolean existsRequerimentoAlvara(EstabelecimentoSetoresAtividadeDTO dto) {
        if(dto.getEstabelecimentoSetores().getCodigo() != null) {
            return LoadManager.getInstance(RequerimentoAlvara.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoAlvara.PROP_ESTABELECIMENTO_SETORES, dto.getEstabelecimentoSetores()))
                    .exists();
        } else {
            return false;
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (estabelecimentoSetoresDTOList == null) {
                    estabelecimentoSetoresDTOList = new ArrayList<>();
                }
                return estabelecimentoSetoresDTOList;
            }
        };
    }


    @Override
    public String getTitle() {
        return BundleManager.getString("setores");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return descricaoSetor;
    }

    @Override
    public void onAjaxUpdate(AjaxRequestTarget target) {
        super.onAjaxUpdate(target);
    }
}
