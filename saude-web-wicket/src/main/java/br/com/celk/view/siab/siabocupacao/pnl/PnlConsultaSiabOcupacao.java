package br.com.celk.view.siab.siabocupacao.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.siab.siabocupacao.customize.CustomizeConsultaSiabOcupacao;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.siab.SiabOcupacao;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaSiabOcupacao extends PnlConsulta<SiabOcupacao> { 

    public PnlConsultaSiabOcupacao(String id) {
        super(id);
    }

    public PnlConsultaSiabOcupacao(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaSiabOcupacao(String id, IModel<SiabOcupacao> model) {
        super(id, model);
    }

    public PnlConsultaSiabOcupacao(String id, IModel<SiabOcupacao> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaSiabOcupacao();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("ocupacoes");
    }

}
