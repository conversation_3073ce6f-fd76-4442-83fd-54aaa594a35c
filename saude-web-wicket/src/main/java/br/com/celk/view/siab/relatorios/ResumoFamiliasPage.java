package br.com.celk.view.siab.relatorios;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.BasicoReportFacade;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryResumoFamiliasDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ResumoFamiliasPage extends RelatorioPage<QueryResumoFamiliasDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    private WebMarkupContainer containerTipoResumo;

    @Override
    public void init(Form form) {
        QueryResumoFamiliasDTOParam proxy = on(QueryResumoFamiliasDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        try {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();

            form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));

            if (!isActionPermitted(usuarioLogado, Permissions.PROFISSIONAL)) {
                autoCompleteConsultaProfissional.setEnabled(false);
                if(usuarioLogado.getProfissional() != null){
                    autoCompleteConsultaProfissional.setComponentValue(usuarioLogado.getProfissional());
                }else{
                    warn(bundle("msgNaoFoiEncontradoProfissionalUsuario"));
                    getBtnGerarRelatorio().setEnabled(false);
                }
            }

            form.add(dropDownArea = createDropDownArea());
            form.add(dropDownMicroArea = new DropDown(path(proxy.getEquipeMicroArea())));
            form.add(createDropDownSegmento());
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));

        DropDown<TipoRelatorio> cbxTipoRelatorio = DropDownUtil.getTipoRelatorioPdfXlsDropDown(path(proxy.getTipoArquivo()));
        form.add(cbxTipoRelatorio);
        cbxTipoRelatorio.addAjaxUpdateValue();
        cbxTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onUpdateTipoRelatorio(target);
            }
        });

        form.add(containerTipoResumo = new WebMarkupContainer("containerTipoResumo"));
        containerTipoResumo.setOutputMarkupPlaceholderTag(true);
        containerTipoResumo.setVisible(false);
        containerTipoResumo.add(DropDownUtil.getEnumDropDown(path(proxy.getTipoResumo()), QueryResumoFamiliasDTOParam.TipoResumo.values()));
    }

    private void onUpdateTipoRelatorio(AjaxRequestTarget target) {
        if (TipoRelatorio.XLS2.equals(param.getTipoArquivo())) {
            containerTipoResumo.setVisible(true);
        } else {
            containerTipoResumo.setVisible(false);
        }
        target.add(containerTipoResumo);
    }

    private DropDown<EquipeArea> createDropDownArea() throws DAOException, ValidacaoException {
        DropDown<EquipeArea> dropDown = new DropDown<EquipeArea>("area");

        LoadManager loadManager = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO));

        final List<Long> empresas = new ArrayList();
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            empresas.add(((Empresa) autoCompleteConsultaEmpresa.getComponentValue()).getCodigo());
        } else {
            if (!isActionPermitted(Permissions.EMPRESA)) {
                try {
                    empresas.addAll(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario()));
                } catch (SGKException ex) {
                    Logger.getLogger(RelatorioAcompanhamentoCadastroFamiliasPage.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }

        if (!empresas.isEmpty()) {
            loadManager.addInterceptor(new LoadInterceptor() {
                @Override
                public void customHQL(HQLHelper hql, String alias) {
                    HQLHelper exists = hql.getNewInstanceSubQuery();
                    exists.addToSelect("1");
                    exists.addToFrom("EquipeMicroArea ema JOIN ema.equipeProfissional ep JOIN ep.equipe e JOIN e.empresa emp");
                    exists.addToWhereWhithAnd("ema.equipeArea.codigo = " + alias + ".codigo");
                    exists.addToWhereWhithAnd("emp.codigo in ", empresas);
                    hql.addToWhereWhithAnd("exists(" + exists.getQuery() + ")");
                }
            });
        }

        List<EquipeArea> list = loadManager.start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : list) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
                String descricao;
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }
                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }

    private DropDown createDropDownSegmento() throws DAOException, ValidacaoException {
        DropDown dropDown = new DropDown("segmento");

        List<SegmentoTerritorial> list = LoadManager.getInstance(SegmentoTerritorial.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SegmentoTerritorial.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa().getCidade()))
                .start().getList();

        dropDown.addChoice(null, "");
        for (SegmentoTerritorial segmentoTerritorial : list) {
            dropDown.addChoice(segmentoTerritorial, segmentoTerritorial.getDescricao());
        }

        return dropDown;
    }

    @Override
    public DataReport getDataReport(QueryResumoFamiliasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(BasicoReportFacade.class).resumoFamilias(param);
    }

    @Override
    public String getTituloPrograma() {
        return Bundle.getStringApplication("resumo_familias");
    }

    @Override
    public Class<QueryResumoFamiliasDTOParam> getDTOParamClass() {
        return QueryResumoFamiliasDTOParam.class;
    }

}
