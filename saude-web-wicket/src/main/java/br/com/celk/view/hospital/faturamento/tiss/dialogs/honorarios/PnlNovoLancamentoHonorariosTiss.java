package br.com.celk.view.hospital.faturamento.tiss.dialogs.honorarios;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPacienteTissDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaProfissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import static ch.lambdaj.Lambda.exists;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import static org.hamcrest.Matchers.equalTo;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNovoLancamentoHonorariosTiss extends Panel {

    private CompoundPropertyModel<ItemContaPacienteTissDTO> model;
    private AbstractAjaxButton btnFechar;
    private DateChooser dchEmissaoGuia;
    private DateChooser dchDataExecucao;
    private ProcedimentoCompetencia procedimentoCompetencia;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private LongField txtQuantidade;
    private DoubleField txtPrecoUnitario;
    private HoraMinutoField txtHoraInicial;
    private HoraMinutoField txtHoraFinal;
    private DropDown dropDownTipoViaAcesso;
    private DropDown dropDownTipoTecnicaRealizada;

    private Date dataCompetencia;

    private AbstractAjaxButton btnAdicionar;
    private WebMarkupContainer containerProfissional;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissionalExecutante;
    private DropDown dropDownGrauParticipacao;
    private DropDown dropDownCbo;
    private List<ItemContaProfissional> lstProfissionais;
    private Table<ItemContaProfissional> tblProfissionais;
    private CompoundPropertyModel<ItemContaProfissional> modelProfissional;

    public PnlNovoLancamentoHonorariosTiss(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        dataCompetencia = CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento();

        Form<ItemContaPacienteTissDTO> form = new Form("form", model = new CompoundPropertyModel(new ItemContaPacienteTissDTO()));
        ItemContaPacienteTissDTO proxy = on(ItemContaPacienteTissDTO.class);

        form.add(dchEmissaoGuia = new DateChooser(path(proxy.getDataEmissaoGuia())));
        dchEmissaoGuia.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        form.add(dchDataExecucao = new DateChooser(path(proxy.getDataLancamento())));
        dchDataExecucao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia("procedimentoCompetencia", new PropertyModel(this, "procedimentoCompetencia")));
        autoCompleteConsultaProcedimentoCompetencia.addAjaxUpdateValue();
        autoCompleteConsultaProcedimentoCompetencia.setDataCompetencia(dataCompetencia);
        autoCompleteConsultaProcedimentoCompetencia.add(new ConsultaListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoCompetencia procedimentoCompetencia) {
                setPrecoProcedimento(target, procedimentoCompetencia.getId().getProcedimento());
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaProcedimentoCompetencia.add(new RemoveListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ProcedimentoCompetencia procedimentoCompetencia) {
                txtPrecoUnitario.limpar(target);
            }
        });

        form.add(txtHoraInicial = new HoraMinutoField(path(proxy.getHoraInicial())));
        form.add(txtHoraFinal = new HoraMinutoField(path(proxy.getHoraFinal())));

        form.add(txtQuantidade = new LongField(path(proxy.getQuantidade())));
        txtQuantidade.setVMax(999L);

        form.add(txtPrecoUnitario = new DoubleField(path(proxy.getPrecoUnitario())));
        txtPrecoUnitario.setVMax(99999999D);

        form.add(dropDownTipoViaAcesso = DropDownUtil.getIEnumDropDown(path(proxy.getTipoViaAcesso()), ItemContaPaciente.TipoViaAcesso.values(), true));
        form.add(dropDownTipoTecnicaRealizada = DropDownUtil.getIEnumDropDown(path(proxy.getTipoTecnicaRealizada()), ItemContaPaciente.TipoTecnicaRealizada.values(), true));

        form.add(containerProfissional = new WebMarkupContainer("containerProfissional", modelProfissional = new CompoundPropertyModel(new ItemContaProfissional())));
        containerProfissional.setOutputMarkupId(true);
        { // Profissionais Envolvidos
            ItemContaProfissional prxProfissional = on(ItemContaProfissional.class);
            containerProfissional.add(autoCompleteConsultaProfissionalExecutante = new AutoCompleteConsultaProfissional(path(prxProfissional.getProfissional())));
            autoCompleteConsultaProfissionalExecutante.addAjaxUpdateValue();
            autoCompleteConsultaProfissionalExecutante.addPropertiesLoad(VOUtils.montarPath(Profissional.PROP_CONSELHO_CLASSE, OrgaoEmissor.PROP_CODIGO_CONSELHO_TISS));
            autoCompleteConsultaProfissionalExecutante.add(new ConsultaListener<Profissional>() {
                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                    updateDropDownCbo(target, profissional);
                }
            });
            autoCompleteConsultaProfissionalExecutante.add(new RemoveListener<Profissional>() {
                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional profissional) {
                    dropDownCbo.removeAllChoices();
                    dropDownCbo.setEnabled(false);
                    dropDownCbo.limpar(target);
                }
            });

            containerProfissional.add(dropDownCbo = new DropDown(path(prxProfissional.getTabelaCbo())));
            containerProfissional.add(dropDownGrauParticipacao = DropDownUtil.getIEnumDropDown(path(prxProfissional.getGrauParticipacao()), ItemContaProfissional.GrauParticipacao.values(), false, false, false, true));

            containerProfissional.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    adicionarProfissional(target);
                }
            });

            containerProfissional.add(tblProfissionais = new Table("tblProfissionais", getColumns(), getCollectionProvider()));
            tblProfissionais.populate();
        }

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ItemContaPacienteTissDTO dto = model.getObject();
                dto.setTipo(ItemContaPaciente.Tipo.HONORARIO_TISS.value());
                dto.setStatus(ItemContaPaciente.Status.ABERTO.value());

                if (procedimentoCompetencia != null) {
                    dto.setProcedimento(procedimentoCompetencia.getId().getProcedimento());
                } else {
                    dto.setProcedimento(null);
                }

                validaLancamento(dto);
                dto.setPrecoTotal(new Dinheiro(Coalesce.asDouble(dto.getPrecoUnitario())).multiplicar(Coalesce.asDouble(dto.getQuantidade())).doubleValue());
                onConfirmar(target, dto);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        ItemContaProfissional proxy = on(ItemContaProfissional.class);
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createColumn(bundle("cbo"), proxy.getTabelaCbo().getDescricaoFormatado()));
        columns.add(createColumn(bundle("grauParticipacao"), proxy.getDescricaoGrauParticipacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaProfissional>() {
            @Override
            public void customizeColumn(ItemContaProfissional rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaProfissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaProfissional itemContaProfissional) throws ValidacaoException, DAOException {
                        removerProfissional(target, itemContaProfissional);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstProfissionais;
            }
        };
    }

    private void adicionarProfissional(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        ItemContaProfissional itemContaProfissional = modelProfissional.getObject();
        validarAdicionarProfissional(itemContaProfissional);
        lstProfissionais.add(itemContaProfissional);
        tblProfissionais.update(target);
        limparContainerProfissionais(target);
    }

        private void validarAdicionarProfissional(ItemContaProfissional itemContaProfissional) throws ValidacaoException, DAOException {
        Profissional profissional = itemContaProfissional.getProfissional();
        if (profissional == null) {
            throw new ValidacaoException(bundle("informeProfissional"));
        }

        boolean existsProfissionalLista = exists(lstProfissionais, having(on(ItemContaProfissional.class).getProfissional().getCodigo(), equalTo(profissional.getCodigo())));
        if (existsProfissionalLista) {
            throw new ValidacaoException(bundle("msgProfissionalJaAdicionadoListaEnvolvidos"));
        }

        ValidacaoProcesso validacoesProfissional = new ValidacaoProcesso();

        // Deve possuir Conselho de Classe Configurado
        if (profissional.getConselhoClasse() == null || profissional.getConselhoClasse().getCodigoConselhoTiss() == null) {
            validacoesProfissional.add("* " + bundle("conselhoClasse"));
        }

        // Deve possuir UF do Conselho de Registro Configurado
        if (profissional.getUnidadeFederacaoConselhoRegistro() == null) {
            validacoesProfissional.add("* " + bundle("ufConselhoRegistro"));
        }

        // Deve possuir Número do Registro
        if (profissional.getNumeroRegistro() == null) {
            validacoesProfissional.add("* " + bundle("numeroRegistro"));
        }

        if (itemContaProfissional.getTabelaCbo() == null) {
            Empresa empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa();
            if (this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal() != null) {
                empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal();
            }

            validacoesProfissional.add("* " + bundle("msgCBONaoEncontradoEstabelecimentoX", empresaPrincipal.getDescricao()));
        }

        ValidacaoProcesso validacoes = new ValidacaoProcesso();
        if (!validacoesProfissional.getMensagemList().isEmpty()) {
            validacoes.add(bundle("msgProfissionalXNaoEstaCorretamenteConfigurado", profissional.getNome()));
            validacoes.getValidacaoProcessoList().add(validacoesProfissional);
        }

        if (!validacoes.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacoes);
        }
    }

    private void removerProfissional(AjaxRequestTarget target, ItemContaProfissional itemContaProfissional) {
        for (int i = 0; i < lstProfissionais.size(); i++) {
            ItemContaProfissional item = lstProfissionais.get(i);
            if (item == itemContaProfissional) {
                lstProfissionais.remove(i);
                break;
            }
        }

        tblProfissionais.update(target);
    }

    public void updateDropDownCbo(AjaxRequestTarget target, Profissional profissional) {
        dropDownCbo.removeAllChoices();

        try {
            Empresa empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa();
            if (this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal() != null) {
                empresaPrincipal = this.model.getObject().getContaPaciente().getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal();
            }

            QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
            param.setEmpresa(empresaPrincipal);
            param.setProfissional(profissional);
            List<TabelaCbo> cbos;
            cbos = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);

            if (CollectionUtils.isEmpty(cbos)) {
                dropDownCbo.setEnabled(false);
                dropDownCbo.limpar(target);
                return;
            }

            for (TabelaCbo cbo : cbos) {
                dropDownCbo.addChoice(cbo, cbo.getDescricaoFormatado());
            }

            dropDownCbo.setEnabled(true);
        } catch (DAOException ex) {
            Logger.getLogger(PnlNovoLancamentoHonorariosTiss.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(PnlNovoLancamentoHonorariosTiss.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            target.add(dropDownCbo);
        }
    }

    private void setPrecoProcedimento(AjaxRequestTarget target, Procedimento procedimento) {
        ContaPaciente contaPaciente = model.getObject().getContaPaciente();

        Double preco = null;
        try {
            preco = BOFactory.getBO(HospitalFacade.class).getPrecoProcedimento(procedimento, contaPaciente.getConvenio(), contaPaciente.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getCodigo());
        } catch (DAOException ex) {
            Logger.getLogger(PnlNovoLancamentoHonorariosTiss.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(PnlNovoLancamentoHonorariosTiss.class.getName()).log(Level.SEVERE, null, ex);
        }

        if (preco != null) {
            txtPrecoUnitario.setComponentValue(preco);
            target.add(txtPrecoUnitario);
        } else {
            txtPrecoUnitario.limpar(target);
        }
    }

    private void validaLancamento(ItemContaPacienteTissDTO dto) throws ValidacaoException, DAOException {

        if (dto.getDataEmissaoGuia() == null) {
            throw new ValidacaoException(bundle("msgInformeDataGuiaEmissao"));
        }

        if (dto.getDataLancamento() == null) {
            throw new ValidacaoException(bundle("msgInformeDataExecucao"));
        }

        if (dto.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        }

        if (dto.getHoraInicial() != null && dto.getHoraInicial() != null
                && DataUtil.compareHour(dto.getHoraInicial(), dto.getHoraFinal()) > 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_hora_final_nao_pode_ser_inferior_hora_inicial"));
        }

        if (dto.getQuantidade() == null) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        }

        if (lstProfissionais.isEmpty()) {
            throw new ValidacaoException(bundle("msgAdicioneAoMenosUmProfissional"));
        }
    }

    public void limpar(AjaxRequestTarget target) {
        model.setObject(new ItemContaPacienteTissDTO());
        autoCompleteConsultaProcedimentoCompetencia.limpar(target);
        dchEmissaoGuia.limpar(target);
        dchDataExecucao.limpar(target);
        txtQuantidade.limpar(target);
        txtPrecoUnitario.limpar(target);
        txtHoraInicial.limpar(target);
        txtHoraFinal.limpar(target);
        dropDownTipoViaAcesso.limpar(target);
        dropDownTipoTecnicaRealizada.limpar(target);

        limparContainerProfissionais(target);
    }

    public void limparContainerProfissionais(AjaxRequestTarget target) {
        modelProfissional.setObject(new ItemContaProfissional());
        autoCompleteConsultaProfissionalExecutante.limpar(target);
        dropDownGrauParticipacao.limpar(target);
        dropDownGrauParticipacao.setComponentValue(ItemContaProfissional.GrauParticipacao.CLINICO.value());
        dropDownCbo.removeAllChoices();
        dropDownCbo.setEnabled(false);
        dropDownCbo.limpar(target);
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) {
        limpar(target);
        model.setObject(dto);

        if (dto.getProcedimento() != null) {
            ProcedimentoCompetencia proxy = on(ProcedimentoCompetencia.class);
            procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                    .addProperties(new HQLProperties(ProcedimentoCompetencia.class).getProperties())
                    .addProperties(new HQLProperties(Procedimento.class, path(proxy.getId().getProcedimento())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProcedimento()), dto.getProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getDataCompetencia()), dataCompetencia))
                    .start().getVO();
        }

        if (dto.getDataEmissaoGuia() == null) {
            target.appendJavaScript(JScript.focusComponent(dchEmissaoGuia.getData()));
        }

        autoCompleteConsultaProcedimentoCompetencia.setTipoTabelaProcedimentoList(Arrays.asList(dto.getContaPaciente().getConvenio().getTipoTabelaProcedimento().getCodigo()));

        this.lstProfissionais = dto.getLstProfissionais();
        tblProfissionais.update(target);

        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
