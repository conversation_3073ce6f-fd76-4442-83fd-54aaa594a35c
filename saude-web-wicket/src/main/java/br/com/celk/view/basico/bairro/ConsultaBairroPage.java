package br.com.celk.view.basico.bairro;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaBairroPage extends BasePage {

    private String descricao;
    private Cidade cidade;
    private Empresa empresa;
    private MultiSelectionTableOld<Bairro> table;
    private List<Bairro> bairros;
    private DlgDeletarBairro dlgDeletarBairro;

    public ConsultaBairroPage() {
        initForm();
    }
    
    public void initForm() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(new AutoCompleteConsultaCidade("cidade"));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new InputField<String>("descricao"));
        form.add(table = new MultiSelectionTableOld("table", getColumns(), getCollectionProvider()));
        form.add(new AbstractAjaxButton("btnProcurar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target);
            }
        });
        
        form.add(new AbstractAjaxButton("btnNovo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(CadastroBairroPage.class);
            }
        });
        
        form.add(new AbstractAjaxButton("btnDeletar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (table.getSelectedObjects() != null && table.getSelectedObjects().size() > 0) {
                    dlgDeletarBairro.setModelObject(table.getSelectedObjects());
                    dlgDeletarBairro.show(target);
                }
            }
        });
        table.setScrollY("200px");
        table.populate();

        add(form);
        addModal(dlgDeletarBairro = new DlgDeletarBairro(newModalId()) {
            @Override
            public void onOk(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                procurar(target);
            }
        });
    }

    public List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        Bairro proxy = on(Bairro.class);

        columns.add(createColumn(BundleManager.getString("cidade"), proxy.getCidade().getDescricaoFormatado()));
        columns.add(createColumn(BundleManager.getString("empresa"), proxy.getEmpresa().getDescricaoFormatado()));
        columns.add(createColumn(BundleManager.getString("descricao"), proxy.getDescricao()));

        return columns;
    }
    
    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return bairros;
            }
        };
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaBairro");
    }
    
    private void procurar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (cidade == null) {
            throw new ValidacaoException(BundleManager.getString("campoXObrigatorio", BundleManager.getString("id.cidade")));
        }

        bairros = LoadManager.getInstance(Bairro.class)
                            .addProperties(new HQLProperties(Bairro.class).getProperties())
                            .addProperty(VOUtils.montarPath(Bairro.PROP_EMPRESA, Empresa.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(Bairro.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                            .addProperty(VOUtils.montarPath(Bairro.PROP_CIDADE, Cidade.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(Bairro.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IGUAL, empresa))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_CIDADE), BuilderQueryCustom.QueryParameter.IGUAL, cidade))
                            .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(Bairro.PROP_DESCRICAO)))
                            .start().getList();

        table.populate(target);
    }
}
