package br.com.celk.view.hospital.faturamento.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgConfirmacaoFechamentoConta;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.component.window.WindowUtil;
import static br.com.celk.component.window.WindowUtil.addModal;
import br.com.celk.resources.Icon16;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.faturamento.FechamentoContaHelper;
import br.com.celk.view.hospital.faturamento.dialogs.DlgDadosComplementaresLaqueadura;
import br.com.celk.view.hospital.faturamento.dialogs.DlgDadosComplementaresParto;
import br.com.celk.view.hospital.faturamento.dialogs.DlgDadosComplementaresUtiNeonatal;
import br.com.celk.view.hospital.faturamento.dialogs.DlgLancamentosConfirmadosProcedimentos;
import br.com.celk.view.hospital.faturamento.dialogs.DlgNovoLancamentoProcedimento;
import br.com.celk.view.hospital.faturamento.dialogs.DlgTransferirItemContaPaciente;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.FechamentoContaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.TransferenciaItemContaPacienteDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroPK;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class ProcedimentosTab extends TabPanel<FechamentoContaDTO> {

    private MultiSelectionTable<ItemContaPaciente> tabela;
    private DlgNovoLancamentoProcedimento dlgNovoLancamento;
    private DlgLancamentosConfirmadosProcedimentos dlgLancamentosConfirmadosProcedimentos;
    private AbstractAjaxButton btnLancamentosConfirmados;
    private FechamentoContaDTO fechamentoContaDTO;
    private List<ItemContaPaciente> procedimentosList;
    private List<ItemContaPaciente> procedimentosConfirmadosList;
    private List<ItemContaPaciente> procedimentosErroList;
    private boolean btnConfirmadoOK;
    private int idxItem = -1;
    private DlgTransferirItemContaPaciente dlgTransferirItemContaPaciente;
    private DlgConfirmacao dlgConfirmacao;
    private DlgConfirmacaoFechamentoConta dlgConfirmacaoTodos;
    private List<DadosComplementares> dadosComplementares;
    private DlgDadosComplementaresParto dlgDadosComplementaresParto;
    private DlgDadosComplementaresLaqueadura dlgDadosComplementaresLaqueadura;
    private DlgDadosComplementaresUtiNeonatal dlgDadosComplementaresUtiNeonatal;

    public ProcedimentosTab(String id, FechamentoContaDTO object) {
        super(id, object);
        this.fechamentoContaDTO = object;
        init();
    }

    private void init() {
        add(tabela = new MultiSelectionTable("tabela", getColumns(), getCollectionProvider()));
        tabela.populate();

        add(new AbstractAjaxButton("btnNovoLancamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                idxItem = -1;
                ItemContaPaciente itemContaPaciente = new ItemContaPaciente();
                itemContaPaciente.setContaPaciente(fechamentoContaDTO.getContaPaciente());
                itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.FECHAMENTO_CONTA.value());
                viewDlgNovoLancamento(target, itemContaPaciente);
            }
        });

        add(new AbstractAjaxButton("btnRemoverSelecionado") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isAllEmpty(tabela.getSelectedObjects())) {
                    throw new ValidacaoException(bundle("msgProcedimentoObrigatorio"));
                } else {
                    viewDlgConfirmacao(target);
                }
            }
        });

        add(new AbstractAjaxButton("btnLancamentosConfirmadosTodos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isNotNullEmpty(tabela.getSelectedObjects())) {
                    procedimentosErroList = new ArrayList<ItemContaPaciente>();
                    List<ItemContaPaciente> aux = new ArrayList<ItemContaPaciente>();
                    List<String> listaErro = new ArrayList<String>();
                    for (ItemContaPaciente item : tabela.getSelectedObjects()) {
                        try {
                            FechamentoContaHelper.ajustarItemContaPaciente(item);
                        } catch (Exception e) {
                            procedimentosErroList.add(item);
                            listaErro.add(e.getMessage());
                            aux.add(item);
                        }
                    }
                    for (ItemContaPaciente aux1 : aux) {
                        tabela.getSelectedObjects().remove(aux1);
                    }
                    tabela.update(target);

                    String msg = "";
                    if (CollectionUtils.isNotNullEmpty(procedimentosErroList)) {
                        for (ItemContaPaciente item : procedimentosErroList) {
                            if (procedimentosErroList.indexOf(item) == 0) {
                                msg += bundle("msgProcedimentoInvalido");
                                msg += "\n";
                            }
                            msg += item.getProcedimento().getCodigoDescricaoFormatado();
                            msg += "\n";
                            msg += "\t - " + listaErro.get(procedimentosErroList.indexOf(item));
                            msg += "\n";
                        }
                        msg += bundle("msgDemaisProcessados");
                    }

                    if (CollectionUtils.isNotNullEmpty(procedimentosErroList)) {
                        addModal(target, this, dlgConfirmacaoTodos = new DlgConfirmacaoFechamentoConta(WindowUtil.newModalId(this), bundle("msgTodosSelecionadosConfirmados"), 500, 300) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                changeStatusList(target, procedimentosList, ItemContaPaciente.Status.CONFIRMADO.value());
                            }
                        });
                    } else {
                        addModal(target, this, dlgConfirmacaoTodos = new DlgConfirmacaoFechamentoConta(WindowUtil.newModalId(this), bundle("msgTodosSelecionadosConfirmados")) {
                            @Override
                            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                changeStatusList(target, procedimentosList, ItemContaPaciente.Status.CONFIRMADO.value());
                            }
                        });
                    }

                    if (!msg.isEmpty()) {
                        dlgConfirmacaoTodos.setMessage(target, msg);
                    }
                    dlgConfirmacaoTodos.show(target);
                } else {
                    throw new ValidacaoException(bundle("msgProcedimentoObrigatorio"));
                }
            }
        });

        add(getBtnLanctosConfirmados());
    }

    private void viewDlgNovoLancamento(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgNovoLancamento == null) {
            WindowUtil.addModal(target, this, dlgNovoLancamento = new DlgNovoLancamentoProcedimento(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    adicionaItem(target, itemContaPaciente);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    if (itemContaPaciente != null) {
                        dadosComplementares = LoadManager.getInstance(DadosComplementares.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(DadosComplementares.PROP_PROCEDIMENTO, itemContaPaciente.getProcedimento()))
                                .addSorter(new QueryCustom.QueryCustomSorter(DadosComplementares.PROP_TIPO_DADO))
                                .start().getList();
                        viewDialogsDadosComplementares(target, itemContaPaciente);
                    }
                }
            });
        }

        dlgNovoLancamento.show(target, itemContaPaciente);
    }

    private void viewDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), bundle("msgExcluirRegistrosSelecionados")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    removerProcedimentos(target, tabela.getSelectedObjects());
                    tabela.setSelectedObject(new ArrayList<ItemContaPaciente>());
                }

                @Override
                public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                    btnConfirmar.add(new AttributeModifier("value", bundle("sim")));
                    btnConfirmar.add(new AttributeModifier("class", "btn-red"));
                    btnFechar.add(new AttributeModifier("value", bundle("nao")));
                    btnFechar.add(new AttributeModifier("class", "btn-green"));

                }
            });
        }

        dlgConfirmacao.show(target);
    }

    private void adicionaItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws ValidacaoException {
        if (TipoAtendimento.TipoFaturamento.AIH.value().equals(object.getContaPaciente().getAtendimentoInformacao().getTipoAtendimentoFaturamento().getTipoFaturamento())) {
            Date dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
            List<ProcedimentoRegistro> lstProcedimentoRegistro = LoadManager.getInstance(ProcedimentoRegistro.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA,
                                            ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), itemContaPaciente.getProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_COMPETENCIA,
                                            ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), dataCompetencia))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoRegistro.PROP_ID, ProcedimentoRegistroPK.PROP_PROCEDIMENTO_REGISTRO_CADASTRO,
                                            ProcedimentoRegistroCadastro.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IN,
                                    Arrays.asList(ProcedimentoRegistroCadastro.AIH_ESPECIAL, ProcedimentoRegistroCadastro.AIH_PRINCIPAL, ProcedimentoRegistroCadastro.AIH_SECUNDARIO)))
                    .start().getList();
            if (!CollectionUtils.isNotNullEmpty(lstProcedimentoRegistro)) {
                throw new ValidacaoException(bundle("msgSoPermitidoProcedimentoInstrumentoRegistroAIH"));
            }
        }
        if (idxItem >= 0) {
            fechamentoContaDTO.getListaItensContaPaciente().remove(idxItem);
            fechamentoContaDTO.getListaItensContaPaciente().add(idxItem, itemContaPaciente);
        } else {
            fechamentoContaDTO.getListaItensContaPaciente().add(itemContaPaciente);
        }

        tabela.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("procedimento"), proxy.getProcedimento().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("quantidade"), proxy.getQuantidade()));
        columns.add(createSortableColumn(bundle("preco"), proxy.getPrecoUnitario()));
        columns.add(createSortableColumn(bundle("total"), proxy.getValorTotal()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createSortableColumn(bundle("cbo"), proxy.getCbo().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<ItemContaPaciente>() {
            @Override
            public void customizeColumn(ItemContaPaciente rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente icp) throws ValidacaoException, DAOException {
                        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
                            ItemContaPaciente item = fechamentoContaDTO.getListaItensContaPaciente().get(i);
                            if (item == icp) {
                                idxItem = i;
                                break;
                            }
                        }

                        viewDlgNovoLancamento(target, (ItemContaPaciente) SerializationUtils.clone(icp));
                    }
                });

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        FechamentoContaHelper.ajustarItemContaPaciente(modelObject);
                        changeStatus(target, modelObject, ItemContaPaciente.Status.CONFIRMADO.value());
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        removerProcedimento(target, modelObject);
                    }
                });

                addAction(ActionType.TRANSFERENCIA, rowObject, new IModelAction<ItemContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ItemContaPaciente modelObject) throws ValidacaoException, DAOException {
                        transferirItem(target, modelObject);
                    }
                }).setIcon(Icon16.arrow_switch);
            }
        };
    }

    private void transferirItem(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws ValidacaoException, DAOException {
        if (dlgTransferirItemContaPaciente == null) {
            WindowUtil.addModal(target, this, dlgTransferirItemContaPaciente = new DlgTransferirItemContaPaciente(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, TransferenciaItemContaPacienteDTO transferenciaItemContaPacienteDTO) throws ValidacaoException, DAOException {
                    fechamentoContaDTO.getListaItensContaPacienteTransferidos().add(transferenciaItemContaPacienteDTO);
                    removerItem(target, transferenciaItemContaPacienteDTO.getItemContaPaciente());
                }
            });
        }
        dlgTransferirItemContaPaciente.show(target, itemContaPaciente);
    }

    private void removerItem(AjaxRequestTarget target, ItemContaPaciente modelObject) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == modelObject) {
                fechamentoContaDTO.getListaItensContaPaciente().remove(i);
                tabela.setSelected(target, false, modelObject, null);
                break;
            }
        }
        tabela.update(target);
    }

    private void changeStatus(AjaxRequestTarget target, ItemContaPaciente icp, Long status) {
        for (int i = 0; i < fechamentoContaDTO.getListaItensContaPaciente().size(); i++) {
            if (fechamentoContaDTO.getListaItensContaPaciente().get(i) == icp) {
                tabela.setSelected(target, false, icp, null);
                fechamentoContaDTO.getListaItensContaPaciente().get(i).setStatus(status);
                break;
            }
        }

        tabela.update(target);
        target.add(getBtnLanctosConfirmados());
    }

    private void changeStatusList(AjaxRequestTarget target, List<ItemContaPaciente> listIcp, Long status) {
        for (int i = 0; i < listIcp.size(); i++) {
            ItemContaPaciente icp = listIcp.get(i);
            for (int j = 0; j < fechamentoContaDTO.getListaItensContaPaciente().size(); j++) {
                if (fechamentoContaDTO.getListaItensContaPaciente().get(j) == icp) {
                    fechamentoContaDTO.getListaItensContaPaciente().get(j).setStatus(status);
                    break;
                }
            }
        }
        tabela.update(target);
        tabela.clearSelection(target);
        target.add(btnLancamentosConfirmados);
    }

    private AbstractAjaxButton getBtnLanctosConfirmados() {
        if (btnLancamentosConfirmados == null) {
            btnLancamentosConfirmados = new AbstractAjaxButton("btnLancamentosConfirmados") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    enviaConfirmados(target);
                    dlgLancamentosConfirmadosProcedimentos.show(target);
                }
            };
        }

        btnLancamentosConfirmados.setEnabled(dlgLancamentosConfirmadosProcedimentos != null && !dlgLancamentosConfirmadosProcedimentos.getListaItens().isEmpty() || btnConfirmadoOK);

        return btnLancamentosConfirmados;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                procedimentosList = new ArrayList();

                for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
                    if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.PROCEDIMENTO.value())) {

                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.ABERTO.value())) {
                            procedimentosList.add(itemContaPaciente);
                        }
                        if (itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                            btnConfirmadoOK = true;
                            getBtnLanctosConfirmados();
                        }
                    }
                }

                return procedimentosList;
            }
        };
    }

    private List<ItemContaPaciente> getListConfirmados() {
        procedimentosConfirmadosList = new ArrayList();

        for (ItemContaPaciente itemContaPaciente : fechamentoContaDTO.getListaItensContaPaciente()) {
            if (itemContaPaciente.getTipo().equals(ItemContaPaciente.Tipo.PROCEDIMENTO.value()) && itemContaPaciente.getStatus().equals(ItemContaPaciente.Status.CONFIRMADO.value())) {
                procedimentosConfirmadosList.add(itemContaPaciente);
            }
        }

        return procedimentosConfirmadosList;
    }

    private void enviaConfirmados(AjaxRequestTarget target) {
        if (dlgLancamentosConfirmadosProcedimentos == null) {
            WindowUtil.addModal(target, this, dlgLancamentosConfirmadosProcedimentos = new DlgLancamentosConfirmadosProcedimentos(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                    if (dlgLancamentosConfirmadosProcedimentos.getListaItens().isEmpty()) {
                        btnConfirmadoOK = false;
                    }

                    target.add(getBtnLanctosConfirmados());
                }

                @Override
                public void onReverter(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                    changeStatus(target, itemContaPaciente, ItemContaPaciente.Status.ABERTO.value());
                }
            });
        }

        if (CollectionUtils.isNotNullEmpty(getListConfirmados())) {
            dlgLancamentosConfirmadosProcedimentos.setListItem(target, getListConfirmados());
        }

        target.add(getBtnLanctosConfirmados());
    }

    private void viewDialogsDadosComplementares(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dadosComplementares.isEmpty()) {
            return;
        }

        Long tipoDado = dadosComplementares.remove(0).getTipoDado();
        if (DadosComplementares.TipoDado.PARTO.value().equals(tipoDado)) {
            viewDlgDadosComplementaresParto(target, itemContaPaciente);
        } else if (DadosComplementares.TipoDado.LAQUEADURA.value().equals(tipoDado)) {
            viewDlgDadosComplementaresLaqueadura(target, itemContaPaciente);
        } else if (DadosComplementares.TipoDado.UTI_NEONATAL.value().equals(tipoDado)) {
            viewDlgDadosComplementaresUtiNeonatal(target, itemContaPaciente);
        }
    }

    private void viewDlgDadosComplementaresParto(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgDadosComplementaresParto == null) {
            WindowUtil.addModal(target, this, dlgDadosComplementaresParto = new DlgDadosComplementaresParto(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
                    viewDialogsDadosComplementares(target, itemContaPaciente);
                }
            });
        }
        dlgDadosComplementaresParto.show(target, itemContaPaciente);
    }

    private void viewDlgDadosComplementaresLaqueadura(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgDadosComplementaresLaqueadura == null) {
            WindowUtil.addModal(target, this, dlgDadosComplementaresLaqueadura = new DlgDadosComplementaresLaqueadura(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
                    viewDialogsDadosComplementares(target, itemContaPaciente);
                }
            });
        }
        dlgDadosComplementaresLaqueadura.show(target, itemContaPaciente);
    }

    private void viewDlgDadosComplementaresUtiNeonatal(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        if (dlgDadosComplementaresUtiNeonatal == null) {
            WindowUtil.addModal(target, this, dlgDadosComplementaresUtiNeonatal = new DlgDadosComplementaresUtiNeonatal(WindowUtil.newModalId(this)) {
                @Override
                public void onFechar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
                    viewDialogsDadosComplementares(target, itemContaPaciente);
                }
            });
        }
        dlgDadosComplementaresUtiNeonatal.show(target, itemContaPaciente);
    }

    private void removerProcedimento(AjaxRequestTarget target, ItemContaPaciente modelObject) {
        changeStatus(target, modelObject, ItemContaPaciente.Status.CANCELADO.value());
    }

    private void removerProcedimentos(AjaxRequestTarget target, List<ItemContaPaciente> listIcp) {
        changeStatusList(target, listIcp, ItemContaPaciente.Status.CANCELADO.value());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("procedimentos");
    }
}
