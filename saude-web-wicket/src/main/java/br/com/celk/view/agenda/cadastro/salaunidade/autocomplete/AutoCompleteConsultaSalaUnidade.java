package br.com.celk.view.agenda.cadastro.salaunidade.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SalaUnidade;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaSalaUnidade extends AutoCompleteConsulta<SalaUnidade> {

    public AutoCompleteConsultaSalaUnidade(String id) {
        super(id);
    }

    public AutoCompleteConsultaSalaUnidade(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaSalaUnidade(String id, IModel<SalaUnidade> model) {
        super(id, model);
    }

    public AutoCompleteConsultaSalaUnidade(String id, IModel<SalaUnidade> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {
            @Override
            public Class getReferenceClass() {
                return SalaUnidade.class;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(SalaUnidade.PROP_DESCRICAO, true);
            }

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaAdapter() {

                    @Override
                    public void consultaCustomizeViewProperties(Map<String, String> properties) {
                        properties.put(BundleManager.getString("codigo"), SalaUnidade.PROP_CODIGO);
                        properties.put(BundleManager.getString("descricao"), SalaUnidade.PROP_DESCRICAO);
                        properties.put(BundleManager.getString("unidade"), VOUtils.montarPath(SalaUnidade.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
                    }

                    @Override
                    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
                        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                        filterProperties.put(BundleManager.getString("unidade"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SalaUnidade.PROP_EMPRESA, Empresa.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
                    }

                    @Override
                    public Class getClassConsulta() {
                        return SalaUnidade.class;
                    }

                };
            }

            @Override
            public List<QueryCustom.QueryCustomParameter> getSearchParam(String searchCriteria) {
                return Arrays.asList(new QueryCustom.QueryCustomParameter(SalaUnidade.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, (searchCriteria != null ? searchCriteria.trim() : null)));
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("salaXUnidade");
    }

}
