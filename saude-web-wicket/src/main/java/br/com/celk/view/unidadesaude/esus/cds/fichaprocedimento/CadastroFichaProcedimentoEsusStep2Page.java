package br.com.celk.view.unidadesaude.esus.cds.fichaprocedimento;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.IComponent;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.unidadesaude.esus.cds.procedimentos.FichaProcedimentosDTO;
import br.com.celk.unidadesaude.esus.cds.procedimentos.FichaProcedimentosItemDTO;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.unidadesaude.esus.cds.atendimentoindividual.CadastroFichaAtendimentoIndividualStep2Page;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimento;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimentoItemSigtap;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TipoTabelaProcedimento;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.hamcrest.Matchers;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.util.IntrospectionUtil.getPropertyValue;

/**
 * <AUTHOR>
 */
public class CadastroFichaProcedimentoEsusStep2Page extends BasePage {

    private final EsusFichaProcedimentoItem esusFichaProcedimentoItem;
    private final FichaProcedimentosDTO fichaDTO;
    private final FichaProcedimentosItemDTO itemDTO;

    private DropDown cbxTurno;
    private InputField txtNumeroProntuario;
    private InputField txtCNS;
    private String cns;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private DateChooser dchDataNascimento;
    private DropDown cbxSexo;
    private DropDown cbxLocalAtendimento;
    private CheckBoxLongValue cbEscutaInicial;

    private WebMarkupContainer containerProcedimento;
    private ProcedimentoCompetencia procedimentoCompetencia;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;

    private CompoundPropertyModel<EsusFichaProcedimentoItemSigtap> modelProcedimento;
    private Table<EsusFichaProcedimentoItemSigtap> tblProcedimentos;
    private List<EsusFichaProcedimentoItemSigtap> lstProcedimentos = new ArrayList();
    private WebMarkupContainer containerPPC;

    private List<CheckBoxLongValue> lstCheckBoxPPC = new ArrayList();
    private List<CheckBoxLongValue> lstCheckBoxTesteRapidoPPC = new ArrayList();
    private List<CheckBoxLongValue> lstCheckBoxAdministracaoMedicamentoPPC = new ArrayList();

    private WebMarkupContainer containerAcompanhamentoNutricional;

    private DoubleField txtPesoNutricional;
    private DoubleField txtAlturaNutricional;

    private boolean viewOnly;
    private static final String CUSTOM_CSS = "CadastroFichaProcedimentoEsusStep2Page.css";

    public CadastroFichaProcedimentoEsusStep2Page(FichaProcedimentosDTO fichaDTO) {
        this(fichaDTO, new FichaProcedimentosItemDTO(new EsusFichaProcedimentoItem()));
    }

    public CadastroFichaProcedimentoEsusStep2Page(FichaProcedimentosDTO fichaDTO, FichaProcedimentosItemDTO itemDTO) {
        this(fichaDTO, itemDTO, false);
    }

    public CadastroFichaProcedimentoEsusStep2Page(FichaProcedimentosDTO fichaDTO, FichaProcedimentosItemDTO itemDTO, boolean viewOnly) {
        this.fichaDTO = fichaDTO;
        this.itemDTO = itemDTO;
        this.esusFichaProcedimentoItem = this.itemDTO.getEsusFichaProcedimentoItem();
        this.viewOnly = viewOnly;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(esusFichaProcedimentoItem));

        EsusFichaProcedimentoItem proxy = on(EsusFichaProcedimentoItem.class);

        form.add(cbxTurno = DropDownUtil.getIEnumDropDown(path(proxy.getTurno()), EsusFichaProcedimentoItem.Turno.values(), true));
        form.add(txtNumeroProntuario = new InputField(path(proxy.getNumeroProntuario())));
        form.add(txtCNS = new InputField("cns", new PropertyModel(this, "cns")));
        form.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        autoCompleteConsultaUsuarioCadsus.add(autoCompletePacienteConsultaListner());
        autoCompleteConsultaUsuarioCadsus.add(autoCompletePacienteRemoveListner());
        //autoCompleteConsultaUsuarioCadsus.setEnabled(false);

        form.add(dchDataNascimento = new RequiredDateChooser(path(proxy.getDataNascimento())));
        dchDataNascimento.getData().setMinDate(new DateOption(Data.removeAnos(fichaDTO.getEsusFichaProcedimento().getDataAtendimento(), 130)));
        dchDataNascimento.getData().setMaxDate(new DateOption(fichaDTO.getEsusFichaProcedimento().getDataAtendimento()));

        form.add(cbxSexo = DropDownUtil.getIEnumDropDown(path(proxy.getSexo()), EsusFichaProcedimentoItem.Sexo.values(), true, true));
        form.add(cbxLocalAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getLocalAtendimento()), EsusFichaProcedimentoItem.LocalAtendimento.values(), true, true, false, true));
        form.add(cbEscutaInicial = new CheckBoxLongValue(path(proxy.getEscutaInicial())));

        form.add(containerPPC = new WebMarkupContainer("containerPPC"));
        containerPPC.setOutputMarkupId(true);
        configureFieldsContainerPPC();

        form.add(containerProcedimento = new WebMarkupContainer("containerProcedimento", modelProcedimento = new CompoundPropertyModel(this)));
        containerProcedimento.setOutputMarkupId(true);
        containerProcedimento.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia("procedimentoCompetencia"));
        configurarAutoCompleteConsultaProcedimentoCompetencia();
        containerProcedimento.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProcedimento(target);
            }

            @Override
            public boolean isVisible() {
                return !viewOnly;
            }
        });

        containerProcedimento.add(tblProcedimentos = new Table("tblProcedimentos", getColumns(), getCollectionProvider()));
        tblProcedimentos.populate();

        form.add(new VoltarButton("btnVoltar"));

        form.add(new AbstractAjaxButton("btnSalvarContinuar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar();
            }

            @Override
            public boolean isVisible() {
                return !viewOnly;
            }
        });

        add(form);
        loadValueFields();
        configureComponents();
    }

    private void configurarAutoCompleteConsultaProcedimentoCompetencia() {
        autoCompleteConsultaProcedimentoCompetencia.setTipoTabelaProcedimentoList(Collections.singletonList(TipoTabelaProcedimento.Tipo.SIGTAP.getValue()));
        autoCompleteConsultaProcedimentoCompetencia.setDataCompetencia(CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento());
        autoCompleteConsultaProcedimentoCompetencia.setValidarModalidade(true);
        autoCompleteConsultaProcedimentoCompetencia.setValidarNaoFaturaveis(true);
    }

    private void configureComponents() {
        if (!viewOnly) {
            txtCNS.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    enableFields(target);
                }
            });

            autoCompleteConsultaProcedimentoCompetencia.setValidarNaoFaturaveis(true);

            if (autoCompleteConsultaUsuarioCadsus.getComponentValue() != null) {
                cbxSexo.setEnabled(false);
                dchDataNascimento.setEnabled(false);
            }
        } else {
            cbxTurno.setEnabled(false);
            txtNumeroProntuario.setEnabled(false);
            txtCNS.setEnabled(false);
            dchDataNascimento.setEnabled(false);
            cbxSexo.setEnabled(false);
            cbxLocalAtendimento.setEnabled(false);
            cbEscutaInicial.setEnabled(false);
            containerPPC.setEnabled(false);
            containerProcedimento.setEnabled(false);
        }
    }

    private void configureFieldsContainerPPC() {
        CheckBoxLongValue checkAcupunturaInsercaoAgulhas = new CheckBoxLongValue("acupunturaInsercaoAgulhas", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.ACUPUNTURA_INSERCAO_AGULHAS.sum(), new Model<Long>());
        CheckBoxLongValue checkAdministracaoVitaminaA = new CheckBoxLongValue("administracaoVitaminaA", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.ADMINISTRACAO_VITAMINA_A.sum(), new Model<Long>());
        CheckBoxLongValue checkCateterismoVesicalAlivio = new CheckBoxLongValue("cateterismoVesicalAlivio", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CATETERISMO_VESICAL_ALIVIO.sum(), new Model<Long>());
        CheckBoxLongValue checkCauterizacaoQuimicaPequenasLesoes = new CheckBoxLongValue("cauterizacaoQuimicaPequenasLesoes", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CAUTERIZACAO_QUIMICA_PEQUENAS_LESOES.sum(), new Model<Long>());
        CheckBoxLongValue checkCirurgiaUnha = new CheckBoxLongValue("cirurgiaUnha", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CIRURGIA_UNHA.sum(), new Model<Long>());
        CheckBoxLongValue checkCuidadoEstomas = new CheckBoxLongValue("cuidadoEstomas", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CUIDADO_ESTOMAS.sum(), new Model<Long>());
        CheckBoxLongValue checkCurativoEspecial = new CheckBoxLongValue("curativoEspecial", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CURATIVO_ESPECIAL.sum(), new Model<Long>());
        CheckBoxLongValue checkDrenagemAbscesso = new CheckBoxLongValue("drenagemAbscesso", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.DRENAGEM_ABSCESSO.sum(), new Model<Long>());
        CheckBoxLongValue checkEletrocardiograma = new CheckBoxLongValue("eletrocardiograma", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.ELETROCARDIOGRAMA.sum(), new Model<Long>());
        CheckBoxLongValue checkColetaCitopatologicoColoUterino = new CheckBoxLongValue("coletaCitopatologicoColoUterino", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.COLETA_CITOPATOLOGICO_COLO_UTERINO.sum(), new Model<Long>());
        CheckBoxLongValue checkExamePeDiabetico = new CheckBoxLongValue("examePeDiabetico", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.EXAME_PE_DIABETICO.sum(), new Model<Long>());
        CheckBoxLongValue checkExereseBiopsiaPuncao = new CheckBoxLongValue("exereseBiopsiaPuncao", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.EXERESE_BIOPSIA_PUNCAO_TUMORES_SUPERFICIAIS_PELE.sum(), new Model<Long>());
        CheckBoxLongValue checkFundoscopia = new CheckBoxLongValue("fundoscopia", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.FUNDOSCOPIA.sum(), new Model<Long>());
        CheckBoxLongValue checkInfiltracaoCavidadeSinovial = new CheckBoxLongValue("infiltracaoCavidadeSinovial", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.INFILTRACAO_CAVIDADE_SINOVIAL.sum(), new Model<Long>());
        CheckBoxLongValue checkRemocaoCorpoEstranhoCavidadeAuditivaNasal = new CheckBoxLongValue("remocaoCorpoEstranhoCavidadeAuditivaNasal", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.REMOCAO_CORPO_ESTRANHO_CAVIDADE_AUDITIVA_NASAL.sum(), new Model<Long>());
        CheckBoxLongValue checkRemocaoCorpoEstranhoSubcutaneo = new CheckBoxLongValue("remocaoCorpoEstranhoSubcutaneo", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.REMOCAO_CORPO_ESTRANHO_SUBCUTANEO.sum(), new Model<Long>());
        CheckBoxLongValue checkRetiradaCerume = new CheckBoxLongValue("retiradaCerume", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.RETIRADA_CERUME.sum(), new Model<Long>());
        CheckBoxLongValue checkRetiradaPontosCirurgias = new CheckBoxLongValue("retiradaPontosCirurgias", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.RETIRADA_PONTOS_CIRURGIA.sum(), new Model<Long>());
        CheckBoxLongValue checkSuturaSimples = new CheckBoxLongValue("suturaSimples", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.SUTURA_SIMPLES.sum(), new Model<Long>());
        CheckBoxLongValue checkTriagemOftalmologica = new CheckBoxLongValue("triagemOftalmologica", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.TRIAGEM_OFTALMOLOGICA.sum(), new Model<Long>());
        CheckBoxLongValue checkTamponamentoEpistaxe = new CheckBoxLongValue("tamponamentoEpistaxe", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.TAMPONAMENTO_EPISTAXE.sum(), new Model<Long>());
        CheckBoxLongValue checkCurativoSimples = new CheckBoxLongValue("curativoSimples", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.CURATIVO_SIMPLES.sum(), new Model<Long>());
        CheckBoxLongValue checkAfericaoTemperatura = new CheckBoxLongValue("afericaoTemperatura", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.AFERICAO_TEMPERATURA.sum(), new Model<Long>());
        CheckBoxLongValue checkMedicaoAltura = new CheckBoxLongValue("medicaoAltura", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.MEDICAO_ALTURA.sum(), new Model<Long>());
        CheckBoxLongValue checkMedicaoPeso = new CheckBoxLongValue("medicaoPeso", EsusFichaProcedimentoItem.ProcedimentosPequenasCirurgias.MEDICAO_PESO.sum(), new Model<Long>());

        lstCheckBoxPPC.add(checkAcupunturaInsercaoAgulhas);
        lstCheckBoxPPC.add(checkAdministracaoVitaminaA);
        lstCheckBoxPPC.add(checkCateterismoVesicalAlivio);
        lstCheckBoxPPC.add(checkCauterizacaoQuimicaPequenasLesoes);
        lstCheckBoxPPC.add(checkCirurgiaUnha);
        lstCheckBoxPPC.add(checkCuidadoEstomas);
        lstCheckBoxPPC.add(checkCurativoEspecial);
        lstCheckBoxPPC.add(checkDrenagemAbscesso);
        lstCheckBoxPPC.add(checkEletrocardiograma);
        lstCheckBoxPPC.add(checkColetaCitopatologicoColoUterino);
        lstCheckBoxPPC.add(checkExamePeDiabetico);
        lstCheckBoxPPC.add(checkExereseBiopsiaPuncao);
        lstCheckBoxPPC.add(checkFundoscopia);
        lstCheckBoxPPC.add(checkInfiltracaoCavidadeSinovial);
        lstCheckBoxPPC.add(checkRemocaoCorpoEstranhoCavidadeAuditivaNasal);
        lstCheckBoxPPC.add(checkRemocaoCorpoEstranhoSubcutaneo);
        lstCheckBoxPPC.add(checkRetiradaCerume);
        lstCheckBoxPPC.add(checkRetiradaPontosCirurgias);
        lstCheckBoxPPC.add(checkSuturaSimples);
        lstCheckBoxPPC.add(checkTriagemOftalmologica);
        lstCheckBoxPPC.add(checkTamponamentoEpistaxe);
        lstCheckBoxPPC.add(checkCurativoSimples);
        lstCheckBoxPPC.add(checkAfericaoTemperatura);
        lstCheckBoxPPC.add(checkMedicaoAltura);
        lstCheckBoxPPC.add(checkMedicaoPeso);
        for (CheckBoxLongValue checkBox : lstCheckBoxPPC) {
            containerPPC.add(checkBox);
        }

        CheckBoxLongValue checkGravidez = new CheckBoxLongValue("gravidez", EsusFichaProcedimentoItem.TesteRapidoPPC.GRAVIDEZ.sum(), new Model<Long>());
        CheckBoxLongValue checkDosagemProteinuria = new CheckBoxLongValue("dosagemProteinuria", EsusFichaProcedimentoItem.TesteRapidoPPC.DOSAGEM_PROTEINURIA.sum(), new Model<Long>());
        CheckBoxLongValue checkHIV = new CheckBoxLongValue("hiv", EsusFichaProcedimentoItem.TesteRapidoPPC.HIV.sum(), new Model<Long>());
        CheckBoxLongValue checkHepatiteC = new CheckBoxLongValue("hepatiteC", EsusFichaProcedimentoItem.TesteRapidoPPC.HEPATITE_C.sum(), new Model<Long>());
        CheckBoxLongValue checkSifilis = new CheckBoxLongValue("sifilis", EsusFichaProcedimentoItem.TesteRapidoPPC.SIFILIS.sum(), new Model<Long>());

        lstCheckBoxTesteRapidoPPC.add(checkGravidez);
        lstCheckBoxTesteRapidoPPC.add(checkDosagemProteinuria);
        lstCheckBoxTesteRapidoPPC.add(checkHIV);
        lstCheckBoxTesteRapidoPPC.add(checkHepatiteC);
        lstCheckBoxTesteRapidoPPC.add(checkSifilis);
        for (CheckBoxLongValue checkBox : lstCheckBoxTesteRapidoPPC) {
            containerPPC.add(checkBox);
        }

        CheckBoxLongValue checkOral = new CheckBoxLongValue("oral", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.ORAL.sum(), new Model<Long>());
        CheckBoxLongValue checkIntramuscular = new CheckBoxLongValue("intramuscular", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.INTRAMUSCULAR.sum(), new Model<Long>());
        CheckBoxLongValue checkEndovenosa = new CheckBoxLongValue("endovenosa", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.ENDOVENOSA.sum(), new Model<Long>());
        CheckBoxLongValue checkInalacaoNebulizacao = new CheckBoxLongValue("inalacaoNebulizacao", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.INALACAO_NEBULIZACAO.sum(), new Model<Long>());
        CheckBoxLongValue checkTopica = new CheckBoxLongValue("topica", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.TOPICA.sum(), new Model<Long>());
        CheckBoxLongValue checkPenicilinaTratamentoSifilis = new CheckBoxLongValue("penicilinaTratamentoSifilis", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.PENICILINA_TRATAMENTO_SIFILIS.sum(), new Model<Long>());
        CheckBoxLongValue checkSubcutaneSc = new CheckBoxLongValue("subcutaneSc", EsusFichaProcedimentoItem.AdministracaoMedicamentosPPC.SUBCUTANEA_SC.sum(), new Model<Long>());

        lstCheckBoxAdministracaoMedicamentoPPC.add(checkOral);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkIntramuscular);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkEndovenosa);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkInalacaoNebulizacao);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkTopica);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkPenicilinaTratamentoSifilis);
        lstCheckBoxAdministracaoMedicamentoPPC.add(checkSubcutaneSc);
        for (CheckBoxLongValue checkBox : lstCheckBoxAdministracaoMedicamentoPPC) {
            containerPPC.add(checkBox);
        }

        containerAcompanhamentoNutricional = new WebMarkupContainer("containerAcompanhamentoNutricional");
        containerAcompanhamentoNutricional.setOutputMarkupId(true);
        containerAcompanhamentoNutricional.setEnabled(true);

        txtPesoNutricional = new DoubleField("pesoAcompanhamentoNutricional").setMDec(3);
        txtPesoNutricional.add(new Tooltip().setMessage(BundleManager.getString("msgPesoMinMax", 0.5, 500)));

        txtAlturaNutricional = new DoubleField("alturaAcompanhamentoNutricional").setMDec(1);
        txtAlturaNutricional.add(new Tooltip().setMessage(BundleManager.getString("msgAlturaMinMax", 20, 250)));

        containerAcompanhamentoNutricional.add(txtPesoNutricional);
        containerAcompanhamentoNutricional.add(txtAlturaNutricional);
        containerPPC.add(containerAcompanhamentoNutricional);

        carregarPesoEAltura(esusFichaProcedimentoItem);
    }

    private void carregarPesoEAltura(EsusFichaProcedimentoItem esusFichaProcedimentoItem) {
        if (esusFichaProcedimentoItem.getUsuarioCadsus() != null) {
            if (esusFichaProcedimentoItem.getPesoAcompanhamentoNutricional() == null) {
                txtPesoNutricional.setComponentValue(getPesoAcompanhamentoNutricional(esusFichaProcedimentoItem));
            }
            if (esusFichaProcedimentoItem.getAlturaAcompanhamentoNutricional() == null) {
                txtAlturaNutricional.setComponentValue(getAlturaAcompanhamentoNutricional(esusFichaProcedimentoItem));
            }
        }
    }

    private Double getAlturaAcompanhamentoNutricional(EsusFichaProcedimentoItem esusFichaProcedimentoItem) {
        AtendimentoPrimario atendimentoPrimarioAltura = buildLoadManager(esusFichaProcedimentoItem)
                .addProperty(AtendimentoPrimario.PROP_ALTURA)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ALTURA), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .setMaxResults(1).start().getVO();

        return Coalesce.asDouble(atendimentoPrimarioAltura != null ? atendimentoPrimarioAltura.getAltura() : null);
    }

    private Double getPesoAcompanhamentoNutricional(EsusFichaProcedimentoItem esusFichaProcedimentoItem) {
        AtendimentoPrimario atendimentoPrimarioPeso = buildLoadManager(esusFichaProcedimentoItem)
                .addProperty(AtendimentoPrimario.PROP_PESO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_PESO), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .setMaxResults(1).start().getVO();

        return Coalesce.asDouble(atendimentoPrimarioPeso != null ? atendimentoPrimarioPeso.getPeso() : 0.0);
    }

    private LoadManager buildLoadManager(EsusFichaProcedimentoItem esusFichaProcedimentoItem) {
        return LoadManager.getInstance(AtendimentoPrimario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), esusFichaProcedimentoItem.getUsuarioCadsus().getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtendimentoPrimario.PROP_DATA_AVALIACAO), BuilderQueryCustom.QuerySorter.DECRESCENTE));
    }

    private void loadValueFields() {
        if (esusFichaProcedimentoItem.getCns() != null) {
            cns = StringUtils.leftPad(String.valueOf(esusFichaProcedimentoItem.getCns()), 15, '0');
        }
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxPPC, Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioProcedimentosPequenasCirurgias()));
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxTesteRapidoPPC, Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioTesteRapidoPpc()));
        CheckBoxUtil.selecionarSomatorio(lstCheckBoxAdministracaoMedicamentoPPC, Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioAdministracaoMedicamentosPpc()));

        if (esusFichaProcedimentoItem.getCodigo() != null) {
            lstProcedimentos = LoadManager.getInstance(EsusFichaProcedimentoItemSigtap.class)
                    .addProperties(new HQLProperties(EsusFichaProcedimentoItemSigtap.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EsusFichaProcedimentoItemSigtap.PROP_ESUS_FICHA_PROCEDIMENTO_ITEM, esusFichaProcedimentoItem))
                    .start().getList();
        }
        itemDTO.setLstProcedimentos(lstProcedimentos);
    }

    private void enableFields(AjaxRequestTarget target) {
        getSession().getFeedbackMessages().clear();

        limpar(target, cbxSexo);
        limpar(target, dchDataNascimento);

        if (cns != null) {
            cbxSexo.setEnabled(false);
            dchDataNascimento.setEnabled(false);
            limpar(target, autoCompleteConsultaUsuarioCadsus);

            UsuarioCadsusCns usuarioCadsusCns = null;
            if (CnsValidator.validaCns(cns = StringUtils.leftPad(StringUtil.getDigits(cns), 15, '0'))) {
                usuarioCadsusCns = getUsuarioCadsusCns(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_NUMERO_CARTAO, Long.valueOf(cns)));
            } else {
                warn(target, Bundle.getStringApplication("msg_cns_invalido"));
                return;
            }

            UsuarioCadsus usuarioCadsus = null;
            if (usuarioCadsusCns != null) {
                usuarioCadsus = usuarioCadsusCns.getUsuarioCadsus();
                autoCompleteConsultaUsuarioCadsus.setComponentValue(usuarioCadsus);
                cns = StringUtils.leftPad(String.valueOf(usuarioCadsusCns.getNumeroCartao()), 15, '0');
            }

            txtCNS.setComponentValue(cns);

            if (usuarioCadsus != null) {
                dchDataNascimento.setComponentValue(usuarioCadsus.getDataNascimento());
                if (UsuarioCadsus.SEXO_MASCULINO.equals(usuarioCadsus.getSexo())) {
                    cbxSexo.setComponentValue(EsusFichaProcedimentoItem.Sexo.MASCULINO.value());
                } else {
                    cbxSexo.setComponentValue(EsusFichaProcedimentoItem.Sexo.FEMININO.value());
                }
            } else {
                dchDataNascimento.setEnabled(true);
                cbxSexo.setEnabled(true);
                limpar(target, dchDataNascimento);
                limpar(target, cbxSexo);
            }
        } else {
            cbxSexo.setEnabled(true);
            dchDataNascimento.setEnabled(true);
            limpar(target, autoCompleteConsultaUsuarioCadsus);
        }

        target.add(autoCompleteConsultaUsuarioCadsus);
        target.add(cbxSexo);
        target.add(dchDataNascimento);

        target.appendJavaScript(JScript.initMasks());
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        CadastroFichaProcedimentoEsusStep2Page.this.updateNotificationPanel(target);
    }


    private ConsultaListener<UsuarioCadsus> autoCompletePacienteConsultaListner() {
        return new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                getSession().getFeedbackMessages().clear();

                limpar(target, cbxSexo);
                limpar(target, dchDataNascimento);
                limpar(target, txtCNS);

                cns = usuarioCadsus.getCnsSemPontos();
                if (!CnsValidator.validaCns(cns)) {
                    warn(target, Bundle.getStringApplication("msg_cns_invalido"));
                    return;
                }
                cbxSexo.setEnabled(false);
                dchDataNascimento.setEnabled(false);
                txtCNS.setComponentValue(cns);
                dchDataNascimento.setComponentValue(usuarioCadsus.getDataNascimento());
                if (UsuarioCadsus.SEXO_MASCULINO.equals(usuarioCadsus.getSexo())) {
                    cbxSexo.setComponentValue(EsusFichaProcedimentoItem.Sexo.MASCULINO.value());
                } else {
                    cbxSexo.setComponentValue(EsusFichaProcedimentoItem.Sexo.FEMININO.value());
                }
            }
        };
    }

    private RemoveListener<UsuarioCadsus> autoCompletePacienteRemoveListner() {
        return new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
                getSession().getFeedbackMessages().clear();

                dchDataNascimento.setEnabled(true);
                cbxSexo.setEnabled(true);

                limpar(target, cbxSexo);
                limpar(target, dchDataNascimento);
                limpar(target, txtCNS);

                target.add(cbxSexo);
                target.add(dchDataNascimento);
                target.add(txtCNS);

                target.appendJavaScript(JScript.initMasks());
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
                CadastroFichaProcedimentoEsusStep2Page.this.updateNotificationPanel(target);

            }
        };
    }


    private UsuarioCadsusCns getUsuarioCadsusCns(QueryCustom.QueryCustomParameter parameter) {
        return LoadManager.getInstance(UsuarioCadsusCns.class)
                .addProperty(UsuarioCadsusCns.PROP_CODIGO).addProperty(UsuarioCadsusCns.PROP_NUMERO_CARTAO)
                .addProperties(new HQLProperties(UsuarioCadsus.class, UsuarioCadsusCns.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_EXCLUIDO, BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                .addParameter(parameter)
                .setMaxResults(1).start().getVO();
    }

    private void limpar(AjaxRequestTarget target, IComponent field) {
        if (target != null) {
            field.limpar(target);
        }
    }

    private void adicionarProcedimento(AjaxRequestTarget target) throws ValidacaoException {
        if (procedimentoCompetencia == null) {
            throw new ValidacaoException(bundle("msgInformeProcedimento"));
        }

        if (Lambda.exists(lstProcedimentos, Lambda.having(on(EsusFichaProcedimentoItemSigtap.class).getProcedimento(),
                Matchers.equalTo(procedimentoCompetencia.getId().getProcedimento())))) {
            throw new ValidacaoException(bundle("msgProcedimentoAdicionado", this));
        }

        validaMaximoProcedimentos();

        if (procedimentoCompetencia.getId().getProcedimento().getCodigo().equals(301040079)) {
            throw new ValidacaoException(bundle("msg", this));
        }

        EsusFichaProcedimentoItemSigtap efpis = new EsusFichaProcedimentoItemSigtap();
        efpis.setEsusFichaProcedimentoItem(esusFichaProcedimentoItem);
        efpis.setProcedimento(procedimentoCompetencia.getId().getProcedimento());

        lstProcedimentos.add(efpis);
        tblProcedimentos.update(target);
        autoCompleteConsultaProcedimentoCompetencia.limpar(target);
    }

    private void salvar() throws ValidacaoException, DAOException {
        if (cns != null) {
            esusFichaProcedimentoItem.setCns(Long.valueOf(StringUtil.getDigits(cns)));
        }

        esusFichaProcedimentoItem.setSomatorioProcedimentosPequenasCirurgias(CheckBoxUtil.getSomatorio(lstCheckBoxPPC));
        esusFichaProcedimentoItem.setSomatorioTesteRapidoPpc(CheckBoxUtil.getSomatorio(lstCheckBoxTesteRapidoPPC));
        esusFichaProcedimentoItem.setSomatorioAdministracaoMedicamentosPpc(CheckBoxUtil.getSomatorio(lstCheckBoxAdministracaoMedicamentoPPC));

        validarSalvar();

        if (txtPesoNutricional.getDefaultModelObject() != null) {
            esusFichaProcedimentoItem.setPesoAcompanhamentoNutricional(Double.parseDouble(txtPesoNutricional.getDefaultModelObject().toString()));
        }

        if (txtAlturaNutricional.getDefaultModelObject() != null) {
            esusFichaProcedimentoItem.setAlturaAcompanhamentoNutricional(Double.parseDouble(txtAlturaNutricional.getDefaultModelObject().toString()));
        }

        if (esusFichaProcedimentoItem.getCodigo() == null) {
            fichaDTO.getLstItem().add(itemDTO);
        }

        validaProcedimentosAdicionado();

        EsusFichaProcedimento ficha = BOFactoryWicket.getBO(EsusFacade.class).salvarFichaProcedimento(fichaDTO);
        Page page = new CadastroFichaProcedimentoEsusStep1Page(ficha);
        setResponsePage(page);
    }

    private void validaProcedimentosAdicionado() throws ValidacaoException {
        if (esusFichaProcedimentoItem.getUsuarioCadsus() != null) {
            for (FichaProcedimentosItemDTO item : fichaDTO.getLstItem()) {
                if (item != itemDTO) {
                    if (esusFichaProcedimentoItem.getUsuarioCadsus() != null && item.getEsusFichaProcedimentoItem().getUsuarioCadsus() != null
                            && item.getEsusFichaProcedimentoItem().getUsuarioCadsus().equals(esusFichaProcedimentoItem.getUsuarioCadsus())) {
                        throw new ValidacaoException(bundle("msgJaExisteItemAdicionadoMesmoPaciente", this));
                    }
                }
            }
        }
    }


    private void validarSalvar() throws ValidacaoException {
//        validaCnsUsuarioCadsusAdicionado();

        Date dataAtendimento = Data.adjustRangeHour(fichaDTO.getEsusFichaProcedimento().getDataAtendimento()).getDataInicial();
        Date dataNascimento = Data.adjustRangeHour(esusFichaProcedimentoItem.getDataNascimento()).getDataInicial();

        if (dataNascimento.after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(BundleManager.getString("msgDataNascimentoMaiorAtual"));
        }

        if (dataNascimento.after(dataAtendimento)) {
            throw new ValidacaoException(bundle("msgDataNascimentoNaoPodeSerMaiorQueDataAtendimentoX", Data.formatar(dataAtendimento)));
        }

        if (dataNascimento.before(Data.removeAnos(dataAtendimento, 130))) {
            throw new ValidacaoException(bundle("msgIdadeSuperior130Anos"));
        }

        if (RepositoryComponentDefault.NAO_LONG.equals(esusFichaProcedimentoItem.getEscutaInicial()) && CollectionUtils.isAllEmpty(lstProcedimentos)
                && Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioProcedimentosPequenasCirurgias()) == 0L
                && Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioTesteRapidoPpc()) == 0L
                && Coalesce.asLong(esusFichaProcedimentoItem.getSomatorioAdministracaoMedicamentosPpc()) == 0L) {
            throw new ValidacaoException(bundle("msgNecessarioSejaSelecionadoAdicionadoProcedimento", this));
        }

        if (txtAlturaNutricional.getDefaultModelObject() != null && (Double.parseDouble(txtAlturaNutricional.getDefaultModelObject().toString()) < 20 ||
                Double.parseDouble(txtAlturaNutricional.getDefaultModelObject().toString()) > 250)) {
            throw new ValidacaoException(bundle("msg_altura_paciente_deve_estar_entre_X_e_X", 20, 250));
        }

        if (txtPesoNutricional.getDefaultModelObject() != null && (Double.parseDouble(txtPesoNutricional.getDefaultModelObject().toString()) < 0.5 ||
                Double.parseDouble(txtPesoNutricional.getDefaultModelObject().toString()) > 500)) {
            throw new ValidacaoException(bundle("msg_peso_paciente_deve_estar_entre_X_e_X", 0.5, 500));
        }

    }

    private void validaCnsUsuarioCadsusAdicionado() throws ValidacaoException {
        if (esusFichaProcedimentoItem.getCns() != null || esusFichaProcedimentoItem.getUsuarioCadsus() != null) {
            for (FichaProcedimentosItemDTO item : fichaDTO.getLstItem()) {
                if (item != itemDTO) {
                    if (esusFichaProcedimentoItem.getCns() != null && item.getEsusFichaProcedimentoItem().getCns() != null
                            && item.getEsusFichaProcedimentoItem().getCns().equals(esusFichaProcedimentoItem.getCns())) {
                        throw new ValidacaoException(bundle("msgJaExisteItemAdicionadoMesmoCNS", this));
                    }

                    if (esusFichaProcedimentoItem.getUsuarioCadsus() != null && item.getEsusFichaProcedimentoItem().getUsuarioCadsus() != null
                            && item.getEsusFichaProcedimentoItem().getUsuarioCadsus().equals(esusFichaProcedimentoItem.getUsuarioCadsus())) {
                        throw new ValidacaoException(bundle("msgJaExisteItemAdicionadoMesmoPaciente", this));
                    }
                }
            }
        }
    }

    private List<IColumn> getColumns() {
        EsusFichaProcedimentoItemSigtap proxy = on(EsusFichaProcedimentoItemSigtap.class);

        List<IColumn> columns = new ArrayList();
        if (!viewOnly) {
            columns.add(getActionsColumn());
        }
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimento().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getActionsColumn() {
        return new MultipleActionCustomColumn<EsusFichaProcedimentoItemSigtap>() {
            @Override
            public void customizeColumn(EsusFichaProcedimentoItemSigtap rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EsusFichaProcedimentoItemSigtap>() {
                    @Override
                    public void action(AjaxRequestTarget target, EsusFichaProcedimentoItemSigtap modelObject) throws ValidacaoException, DAOException {
                        removerProcedimento(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerProcedimento(AjaxRequestTarget target, EsusFichaProcedimentoItemSigtap efpis) {
        for (int i = 0; i < lstProcedimentos.size(); i++) {
            EsusFichaProcedimentoItemSigtap item = lstProcedimentos.get(i);
            if (item.equals(efpis) || item.getProcedimento().equals(efpis.getProcedimento())) {
                lstProcedimentos.remove(i);
            }
        }

        tblProcedimentos.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstProcedimentos;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroFichasProcedimentos");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(CadastroFichaProcedimentoEsusStep2Page.class, CUSTOM_CSS)));
    }

    /**
     * A regra diz max de 20 procedimentos, mas a integração valída em 19, foi corrigido para evitar inconsistencias
     *
     * @throws ValidacaoException
     */
    private void validaMaximoProcedimentos() throws ValidacaoException {
        if (lstProcedimentos.size() >= 19) {
            throw new ValidacaoException(bundle("msgMaximo19Procedimentos", this));
        }
    }

}
