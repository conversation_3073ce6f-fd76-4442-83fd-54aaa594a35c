package br.com.celk.view.hospital.aih.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCancelarAih extends Window {

    private PnlCancelarAih pnlCancelarAih;

    public DlgCancelarAih(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("cancelar");
            }
        });

        setInitialWidth(600);
        setInitialHeight(230);

        setResizable(false);

        setContent(pnlCancelarAih = new PnlCancelarAih(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, Aih aih) throws ValidacaoException, DAOException {
                if (aih.getMotivoCancelamento() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
                }

                onFechar(target);
                DlgCancelarAih.this.onConfirmar(target, aih);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlCancelarAih.getTxaObservacao();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Aih aih) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, Aih aih) {
        show(target);
        pnlCancelarAih.setAih(target, aih);
    }

    private void fechar(AjaxRequestTarget target) {
        pnlCancelarAih.limpar(target);
        close(target);
    }
}
