package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.vigilancia.registroagravo.enums.*;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoDoencaChagasDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Lucas
 */

public class FichaInvestigacaoAgravoDoencaDeChagasPage extends FichaInvestigacaoAgravoBasePage {

    private final String CSSFILE = "FichaInvestigacaoAgravoDoencaDeChagasPage.css";
    private InvestigacaoAgravoDoencaChagas investigacaoAgravo;

    //CONTAINER INVESTIGAÇÃO
    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;


    //CONTAINER ANTECEDENTES EPIDEMIOLÓGICOS
    private WebMarkupContainer containerAntecedentesEpidemiologicos;
    private DropDown ddVestigioTristomideos;
    private DateChooser dataVestigioTristomideos;
    private DropDown ddUsoSangueHemoderivadosUltimos120Dias;
    private DropDown ddSorologicoHemoterapia;
    private DropDown ddMaterialTCruzi;
    private DropDown ddMenor9MesesMaeChagasica;
    private DropDown ddTramissaoOral;


    //CONTAINER ANTECEDENTES EPIDEMIOLÓGICOS
    private WebMarkupContainer containerSinaisSintomas;
    private DropDown ddDadosClinicosAssintomatico;
    private DropDown ddDadosClinicosFebrePersistente;
    private DropDown ddDadosClinicosAstenia;
    private DropDown ddDadosClinicosEdma;
    private DropDown ddDadosClinicosHepatomegalia;
    private DropDown ddDadosClinicosEsplenomegalia;
    private DropDown ddDadosClinicosMeningoencefalite;
    private DropDown ddDadosClinicosIcc;
    private DropDown ddDadosClinicosChagomaInoculacaoSinaisRomana;
    private DropDown ddDadosClinicosPoliadenopatia;
    private DropDown ddDadosClinicosTaquicardiaPersistente;
    private InputField txtDadosClinicosOutros;


    //CONTAINER DADOS LABORATÓRIO
    private WebMarkupContainer containerDadosLaboratorio;
    private DateChooser dataParasitologicoDireto;
    private DropDown ddParasitologicoDiretoFrescoGota;
    private DropDown ddParasitologicoDiretoStrout;
    private InputField txtParasitologicoDiretoOutros;
    private DateChooser dataParasitologicoIndireto;
    private DropDown ddParasitologicoIndiretoXenodiagnostico;
    private DropDown ddParasitologicoIndiretoHemocultivo;
    private DateChooser dataColetaS1;
    private DateChooser dataColetaS2;
    private DropDown ddSorologiaElisaIgmS1;
    private DropDown ddSorologiaElisaIgmS2;
    private DropDown ddSorologiaElisaIggS1;
    private DropDown ddSorologiaElisaIggS2;
    private DropDown ddHemoaglutinacaoIgmS1;
    private DropDown ddHemoaglutinacaoIgmS2;
    private DropDown ddHemoaglutinacaoIggS1;
    private DropDown ddHemoaglutinacaoIggS2;
    private DropDown ddImunofluorenscenciaIgmS1;
    private InputField txtImunofluorenscenciaIgmS1;
    private DropDown ddImunofluorenscenciaIgmS2;
    private InputField txtImunofluorenscenciaIgmS2;
    private DropDown ddImunofluorescenciaIggS1;
    private InputField txtImunofluorescenciaIggS1;
    private DropDown ddImunofluorescenciaIggS2;
    private InputField txtImunofluorescenciaIggS2;
    private DateChooser dataColetaHistopatologico;
    private DropDown ddHistopatologico;


    //CONTAINER TRATAMENTO
    private WebMarkupContainer containerTratamento;
    private DropDown ddTipoTratamentoEspecifico;
    private DropDown ddTipoTratamentoSintomatico;
    private DropDown ddDrogaTratamentoEspecifico;
    private InputField txtTempoTratamentoDias;


    //CONTAINER CASO AUTÓCTONE
    private RadioButtonGroup radioGroupCasoAutoctone;
    private WebMarkupContainer containerCasoAutoctone;
    private AutoCompleteConsultaCidade autoCompleteCidadeLocalInfeccao;
    private DisabledInputField estadoLocalInfeccao;
    private AutoCompleteConsultaPais autoCompletePaisLocalInfeccao;
    private DisabledInputField codMunicipioLocalInfeccao;
    private InputField distritoLocalInfeccao;
    private InputField bairroLocalInfeccao;
    private DropDown ddModoProvavelInfeccao;
    private InputField txtModoProvavelInfeccao;
    private DropDown ddLocalProvavelInfeccao;
    private InputField txtLocalProvavelInfeccao;
    private DropDown ddDoencaRelacionadaTrabalho;

    //CONTAINER CONCLUSÃO
    private WebMarkupContainer containerMedidasControle;
    private DropDown ddControleTriatomideos;
    private DropDown ddFiscalizacaoSanitariaUnidadeHemoterapia;
    private DropDown ddImplatacaoNormasBiossegurancaLaboratorio;
    private InputField txtMedidasControleOutros;


    //CONTAINER CONCLUSÃO
    private WebMarkupContainer containerConclusao;
    private DropDown ddClassificacaoFinal;
    private DropDown ddCriterioConfirmacaoDescarte;
    private DropDown ddEvolucaoCaso;
    private DateChooser dataObito;

    //CONTAINER OBS
    private InputArea observacao;

    //CONTAINER ENCERRAMENTO
    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    //TABELA
    private WebMarkupContainer containerDeslocamento;
    private CompoundPropertyModel<InvestigacaoAgravoDoencaChagasDeslocamento> modelDeslocamento;
    private Table tblDeslocamento;
    private List<InvestigacaoAgravoDoencaChagasDeslocamento> deslocamentoList;
    private AutoCompleteConsultaCidade autoCompleteCidadeDeslocamento;
    private DisabledInputField estadoUfDeslocamento;
    private AutoCompleteConsultaPais autoCompletePaisDeslocamento;

    public FichaInvestigacaoAgravoDoencaDeChagasPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoDoencaDeChagasPage.class, CSSFILE))
        );
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoDoencaChagas proxy = on(InvestigacaoAgravoDoencaChagas.class);

        criarContainerInvestigacao(proxy);
        criarContainerAntecedentesEpidemiologicos(proxy);
        criarContainerSinaisSintomas(proxy);
        criarContainerDadosLaboratorio(proxy);
        criarContainerTratamento(proxy);
        criarCasoAutoctone(proxy);
        criarMedidasControle(proxy);
        criarConclusao(proxy);
        criarObservacoes(proxy);
        criarEncerramento(proxy);
        criarDeslocamento();
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarAntecedentesEpidemiologicos();
        carregarCasoAutoctone();
        carregarTratamento();
        carregarDadosLaboratorio();
        carregarConclusao();
        carregarDeslocamento();
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravo == null) {
            investigacaoAgravo = new InvestigacaoAgravoDoencaChagas();
            investigacaoAgravo.setRegistroAgravo(getAgravo());
            investigacaoAgravo.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
        }

        deslocamentoList = investigacaoAgravo.getLocaisDeslocamenInvestigacaoAgravoDoencaChagas();

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().getCodigo());

        if (tabelaCbo != null) {
            investigacaoAgravo.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            investigacaoAgravo.setOcupacaoCbo(tabelaCbo);
        }

        if (investigacaoAgravo.getCidadeLocalInfeccao() != null) {
            Cidade cidadeExposicao = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(investigacaoAgravo.getCidadeLocalInfeccao().getCodigo());
            investigacaoAgravo.setCidadeLocalInfeccao(cidadeExposicao);
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravo)));
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravo = InvestigacaoAgravoDoencaChagas.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoDoencaChagasDTO fichaDTO = new FichaInvestigacaoAgravoDoencaChagasDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoDoencaChagas(investigacaoAgravo);
        fichaDTO.setInvestigacaoAgravoDoencaChagasList(deslocamentoList);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravo.getFlagInformacoesComplementares() == null
                ? "S"
                : investigacaoAgravo.getFlagInformacoesComplementares();
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoDoencaChagasDTO dto = (FichaInvestigacaoAgravoDoencaChagasDTO) fichaDTO;
        validarFicha();
        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoDoencaChegas(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoDoencaChagasDTO fichaDTO = (FichaInvestigacaoAgravoDoencaChagasDTO) getFichaDTO();

        if (investigacaoAgravo.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravo.setUsuarioEncerramento(usuarioLogado);

            investigacaoAgravo.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void atualizarCampoInformacoesComplementares(String flag) {
        investigacaoAgravo.setFlagInformacoesComplementares(flag);
    }

    //CRIAÇÃO DOS CONTAINERS
    private void criarContainerInvestigacao(InvestigacaoAgravoDoencaChagas proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true);
        dataInvestigacao.addRequiredClass();

        dataInvestigacao.getData().setMinDate(new DateOption(investigacaoAgravo.getRegistroAgravo().getDataRegistro()));
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }

    private void criarContainerAntecedentesEpidemiologicos(InvestigacaoAgravoDoencaChagas proxy) {
        containerAntecedentesEpidemiologicos = new WebMarkupContainer("containerAntecedentesEpidemiologicos");
        containerAntecedentesEpidemiologicos.setOutputMarkupId(true);

        dataVestigioTristomideos = new DateChooser(path(proxy.getDataVestigioTristomideos()));
        dataVestigioTristomideos.setEnabled(false);
        dataVestigioTristomideos.getData().setMinDate(new DateOption(investigacaoAgravo.getRegistroAgravo().getDataRegistro()));
        dataVestigioTristomideos.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        ddVestigioTristomideos = DropDownUtil.getIEnumDropDown(path(proxy.getDdVestigioTristomideos()), InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.values(), true, true);

        ddUsoSangueHemoderivadosUltimos120Dias = DropDownUtil.getIEnumDropDown(path(proxy.getDdUsoSangueHemoderivados()), SimNaoIgnoradoEnum.values(), true, true);
        ddSorologicoHemoterapia = DropDownUtil.getIEnumDropDown(path(proxy.getDdSorologicoHemoterapia()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true, true);
        ddSorologicoHemoterapia.setEnabled(false);
        ddMaterialTCruzi = DropDownUtil.getIEnumDropDown(path(proxy.getDdMaterialTCruzi()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true);
        ddMenor9MesesMaeChagasica = DropDownUtil.getIEnumDropDown(path(proxy.getDdMenor9MesesMaeChagasica()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true);
        ddTramissaoOral = DropDownUtil.getIEnumDropDown(path(proxy.getDdTramissaoOral()), SimNaoIgnoradoEnum.values(), true);

        containerAntecedentesEpidemiologicos.add(
                ddVestigioTristomideos, dataVestigioTristomideos, ddUsoSangueHemoderivadosUltimos120Dias,
                ddSorologicoHemoterapia, ddMaterialTCruzi, ddMenor9MesesMaeChagasica,
                ddTramissaoOral
        );
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicos);
    }

    private void criarContainerSinaisSintomas(InvestigacaoAgravoDoencaChagas proxy) {
        containerSinaisSintomas = new WebMarkupContainer("containerSinaisSintomas");
        containerSinaisSintomas.setOutputMarkupId(true);

        ddDadosClinicosAssintomatico = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosAssintomatico()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosFebrePersistente = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosFebrePersistente()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosAstenia = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosAstenia()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosEdma = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosEdma()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosHepatomegalia = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosHepatomegalia()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosEsplenomegalia = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosEsplenomegalia()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosMeningoencefalite = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosMeningoencefalite()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosIcc = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosIcc()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosChagomaInoculacaoSinaisRomana = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosInoculacaoRomana()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosPoliadenopatia = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosPoliadenopatia()), SimNaoIgnoradoEnum.values(), true);
        ddDadosClinicosTaquicardiaPersistente = DropDownUtil.getIEnumDropDown(path(proxy.getDdDadosClinicosTaquicardiaPersistente()), SimNaoIgnoradoEnum.values(), true);
        txtDadosClinicosOutros = new InputField(path(proxy.getTxtDadosClinicosOutros()));

        containerSinaisSintomas.add(
                ddDadosClinicosAssintomatico, ddDadosClinicosFebrePersistente, ddDadosClinicosAstenia,
                ddDadosClinicosEdma, ddDadosClinicosEsplenomegalia, ddDadosClinicosMeningoencefalite,
                ddDadosClinicosIcc, ddDadosClinicosPoliadenopatia, ddDadosClinicosTaquicardiaPersistente,
                txtDadosClinicosOutros, ddDadosClinicosHepatomegalia, ddDadosClinicosChagomaInoculacaoSinaisRomana
        );
        getContainerInformacoesComplementares().add(containerSinaisSintomas);
    }

    private void criarContainerDadosLaboratorio(InvestigacaoAgravoDoencaChagas proxy) {

        containerDadosLaboratorio = new WebMarkupContainer("containerDadosLaboratorio");
        containerDadosLaboratorio.setOutputMarkupId(true);

        dataParasitologicoDireto = new DateChooser(path(proxy.getDataParasitologicoDireto()));
        dataParasitologicoDireto.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas() != null) {
            Date dataAdd = DataUtil.getDataAcrescimoDias(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas(), -120);
            dataParasitologicoDireto.getData().setMinDate(new DateOption(dataAdd));
        }

        ddParasitologicoDiretoFrescoGota = DropDownUtil.getIEnumDropDown(path(proxy.getDdParasitologicoDiretoFrescoGota()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);
        ddParasitologicoDiretoStrout = DropDownUtil.getIEnumDropDown(path(proxy.getDdParasitologicoDiretoStrout()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);
        txtParasitologicoDiretoOutros = new InputField(path(proxy.getTxtParasitologicoDiretoOutros()));

        dataParasitologicoIndireto = new DateChooser(path(proxy.getDataColetaParasitologicoIndireto()));
        dataParasitologicoIndireto.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas() != null) {
            Date dataAdd = DataUtil.getDataAcrescimoDias(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas(), -120);
            dataParasitologicoIndireto.getData().setMinDate(new DateOption(dataAdd));
        }

        ddParasitologicoIndiretoXenodiagnostico = DropDownUtil.getIEnumDropDown(path(proxy.getDdParasitologicoIndiretoXenodiagnostico()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);
        ddParasitologicoIndiretoHemocultivo = DropDownUtil.getIEnumDropDown(path(proxy.getDdParasitologicoIndiretoHemocultivo()), InvestigacaoAgravoLeishmanioseTegumentarAmericanaEnum.PositivoNegativoEnum.values(), true);

        dataColetaS1 = new DateChooser(path(proxy.getDataColetaS1()));
        dataColetaS1.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas() != null) {
            Date dataAdd = DataUtil.getDataAcrescimoDias(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas(), -120);
            dataColetaS1.getData().setMinDate(new DateOption(dataAdd));
        }

        dataColetaS2 = new DateChooser(path(proxy.getDataColetaS2()));
        dataColetaS2.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas() != null) {
            Date dataAdd = DataUtil.getDataAcrescimoDias(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas(), -120);
            dataColetaS2.getData().setMinDate(new DateOption(dataAdd));
        }

        ddSorologiaElisaIgmS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdSorologiaElisaIgmS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddSorologiaElisaIggS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdSorologiaElisaIggS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddHemoaglutinacaoIgmS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdHemoaglutinacaoIgmS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddHemoaglutinacaoIggS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdHemoaglutinacaoIggS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddSorologiaElisaIgmS1.setEnabled(false);
        ddSorologiaElisaIggS1.setEnabled(false);
        ddHemoaglutinacaoIgmS1.setEnabled(false);
        ddHemoaglutinacaoIggS1.setEnabled(false);

        ddSorologiaElisaIgmS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdSorologiaElisaIgmS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddSorologiaElisaIggS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdSorologiaElisaIggS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddHemoaglutinacaoIgmS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdHemoaglutinacaoIgmS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddHemoaglutinacaoIggS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdHemoaglutinacaoIggS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        ddSorologiaElisaIgmS2.setEnabled(false);
        ddSorologiaElisaIggS2.setEnabled(false);
        ddHemoaglutinacaoIgmS2.setEnabled(false);
        ddHemoaglutinacaoIggS2.setEnabled(false);

        ddImunofluorenscenciaIgmS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdImunofluorenscenciaIgmS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        txtImunofluorenscenciaIgmS1 = new InputField(path(proxy.getTxtImunofluorenscenciaIgmS1()));
        ddImunofluorenscenciaIgmS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdImunofluorenscenciaIgmS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        txtImunofluorenscenciaIgmS2 = new InputField(path(proxy.getTxtImunofluorenscenciaIgmS2()));
        ddImunofluorescenciaIggS1 = DropDownUtil.getIEnumDropDown(path(proxy.getDdImunofluorescenciaIggS1()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        txtImunofluorescenciaIggS1 = new InputField(path(proxy.getTxtImunofluorescenciaIggS1()));
        ddImunofluorescenciaIggS2 = DropDownUtil.getIEnumDropDown(path(proxy.getDdImunofluorescenciaIggS2()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.values(), true);
        txtImunofluorescenciaIggS2 = new InputField(path(proxy.getTxtImunofluorescenciaIggS2()));

        txtImunofluorenscenciaIgmS1.setEnabled(false);
        txtImunofluorenscenciaIgmS2.setEnabled(false);
        txtImunofluorescenciaIggS1.setEnabled(false);
        txtImunofluorescenciaIggS2.setEnabled(false);

        dataColetaHistopatologico = new DateChooser(path(proxy.getDataColetaHistopatologico()));
        dataColetaHistopatologico.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if (investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas() != null) {
            Date dataAdd = DataUtil.getDataAcrescimoDias(investigacaoAgravo.getRegistroAgravo().getDataPrimeirosSintomas(), -120);
            dataColetaHistopatologico.getData().setMinDate(new DateOption(dataAdd));
        }

        ddHistopatologico = DropDownUtil.getIEnumDropDown(path(proxy.getDdHistopatologico()), ResultadoHistopatologicoEnum.values(), true);

        containerDadosLaboratorio.add(
                dataParasitologicoDireto, ddParasitologicoDiretoFrescoGota, ddParasitologicoDiretoStrout,
                txtParasitologicoDiretoOutros, dataParasitologicoIndireto, ddParasitologicoIndiretoXenodiagnostico,
                ddParasitologicoIndiretoHemocultivo, dataColetaS1, dataColetaS2,
                ddSorologiaElisaIgmS1, ddSorologiaElisaIgmS2, ddSorologiaElisaIggS1,
                ddSorologiaElisaIggS2, ddHemoaglutinacaoIgmS1, ddHemoaglutinacaoIgmS2,
                ddHemoaglutinacaoIggS1, ddHemoaglutinacaoIggS2, ddImunofluorenscenciaIgmS1,
                txtImunofluorenscenciaIgmS1, ddImunofluorenscenciaIgmS2, txtImunofluorenscenciaIgmS2,
                ddImunofluorescenciaIggS1, txtImunofluorescenciaIggS1, ddImunofluorescenciaIggS2,
                txtImunofluorescenciaIggS2, dataColetaHistopatologico, ddHistopatologico
        );
        getContainerInformacoesComplementares().add(containerDadosLaboratorio);

    }

    private void criarContainerTratamento(InvestigacaoAgravoDoencaChagas proxy) {
        containerTratamento = new WebMarkupContainer("containerTratamento");
        containerTratamento.setOutputMarkupId(true);

        ddTipoTratamentoEspecifico = DropDownUtil.getIEnumDropDown(path(proxy.getDdTipoTratamentoEspecifico()), SimNaoIgnoradoEnum.values(), true);
        ddTipoTratamentoSintomatico = DropDownUtil.getIEnumDropDown(path(proxy.getDdTipoTratamentoSintomatico()), SimNaoIgnoradoEnum.values(), true);

        ddDrogaTratamentoEspecifico = DropDownUtil.getIEnumDropDown(path(proxy.getDdDrogaTratamentoEspecifico()), BenznidazolOutroEnum.values(), true);
        ddDrogaTratamentoEspecifico.setEnabled(false);
        txtTempoTratamentoDias = new InputField(path(proxy.getTxtTempoTratamentoDias()));
        txtTempoTratamentoDias.setEnabled(false);

        containerTratamento.add(
                ddTipoTratamentoEspecifico, ddTipoTratamentoSintomatico, ddDrogaTratamentoEspecifico,
                txtTempoTratamentoDias
        );
        getContainerInformacoesComplementares().add(containerTratamento);
    }

    private void criarCasoAutoctone(InvestigacaoAgravoDoencaChagas proxy) {
        containerCasoAutoctone = new WebMarkupContainer("containerLocalInfeccao");
        containerCasoAutoctone.setOutputMarkupId(true);

        radioGroupCasoAutoctone = new RadioButtonGroup(path(proxy.getCasoAutoctone()));
        radioGroupCasoAutoctone.setRequired(true);
        FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnoradoIndeterminado(radioGroupCasoAutoctone, containerCasoAutoctone, false, true, true, false, false, false);

        autoCompleteCidadeLocalInfeccao = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalInfeccao()));
        codMunicipioLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getCodigo()));
        estadoLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getEstado().getSigla()));

        autoCompletePaisLocalInfeccao = new AutoCompleteConsultaPais(path(proxy.getPaisLocalInfeccao()));
        distritoLocalInfeccao = new InputField(path(proxy.getTxtDistritoLocalInfeccao()));
        bairroLocalInfeccao = new InputField(path(proxy.getTxtBairroLocalInfeccao()));

        ddModoProvavelInfeccao = DropDownUtil.getIEnumDropDown(path(proxy.getDdModoProvavelInfeccao()), ModoProvavelInfeccaoDoencaChagasEnum.values(), true);
        txtModoProvavelInfeccao = new InputField(path(proxy.getTxtModoProvavelInfeccao()));
        txtModoProvavelInfeccao.setEnabled(false);
        ddLocalProvavelInfeccao = DropDownUtil.getIEnumDropDown(path(proxy.getDdLocalProvavelInfeccao()), LocalProvavelInfeccaoEnum.values(), true);
        txtLocalProvavelInfeccao = new InputField(path(proxy.getTxtLocalProvavelInfeccao()));
        txtLocalProvavelInfeccao.setEnabled(false);
        ddDoencaRelacionadaTrabalho = DropDownUtil.getIEnumDropDown(path(proxy.getDdDoencaRelacionadaTrabalho()), SimNaoIgnoradoEnum.values(), true);

        containerCasoAutoctone.add(
                radioGroupCasoAutoctone,
                autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao,
                ddLocalProvavelInfeccao, ddDoencaRelacionadaTrabalho, txtLocalProvavelInfeccao,
                ddModoProvavelInfeccao, txtModoProvavelInfeccao
        );

        getContainerInformacoesComplementares().add(radioGroupCasoAutoctone, containerCasoAutoctone);
    }

    private void criarMedidasControle(InvestigacaoAgravoDoencaChagas proxy) {
        containerMedidasControle = new WebMarkupContainer("containerMedidasControle");
        containerMedidasControle.setOutputMarkupId(true);

        ddControleTriatomideos = DropDownUtil.getIEnumDropDown(path(proxy.getDdMedidasTomadasTriatomideos()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true);
        ddFiscalizacaoSanitariaUnidadeHemoterapia = DropDownUtil.getIEnumDropDown(path(proxy.getDdMedidasTomadasFiscalizacaoSanitaria()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true);
        ddImplatacaoNormasBiossegurancaLaboratorio = DropDownUtil.getIEnumDropDown(path(proxy.getDdMedidasTomadasImplatacaoNormas()), InvestigacaoAgravoAidsCriancaEnum.SimNaoIgnoradoEnum.values(), true);
        txtMedidasControleOutros = new InputField(path(proxy.getTxtMedidasTomadasOutros()));

        containerMedidasControle.add(
                ddControleTriatomideos, ddFiscalizacaoSanitariaUnidadeHemoterapia, ddImplatacaoNormasBiossegurancaLaboratorio,
                txtMedidasControleOutros
        );
        getContainerInformacoesComplementares().add(containerMedidasControle);
    }

    private void criarConclusao(InvestigacaoAgravoDoencaChagas proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddClassificacaoFinal = DropDownUtil.getIEnumDropDown(path(proxy.getDdClassificacaoFinal()), InvestigacaoAgravoCoqueluche.ClassificacaoFinal.values(), true, true);
        ddCriterioConfirmacaoDescarte = DropDownUtil.getIEnumDropDown(path(proxy.getDdCriterioConfirmacaoDescarte()), InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.CriterioConfirmacaoDescarteEnum.values(), true);
        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getDdEvolucaoCaso()), EvolucaoCasoDoencaChagasEnum.values(), true);
        ddEvolucaoCaso.setEnabled(false);
        dataObito = new DateChooser(path(proxy.getDataObito()));
        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataObito.setEnabled(false);

        containerConclusao.add(
                ddClassificacaoFinal, ddCriterioConfirmacaoDescarte, ddEvolucaoCaso,
                dataObito
        );
        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void criarObservacoes(InvestigacaoAgravoDoencaChagas proxy) {

        observacao = new InputArea(path(proxy.getTxtObservacao()));

        getContainerInformacoesComplementares().add(observacao);
    }

    private void criarEncerramento(InvestigacaoAgravoDoencaChagas proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);

        getContainerInformacoesComplementares().add(containerEncerramento);
    }


    //REGRAS
    private void carregarAntecedentesEpidemiologicos() {

        ddVestigioTristomideos.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                boolean validacao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddVestigioTristomideos, InvestigacaoAgravoHantaviroseEnum.SimNaoIgnoradoEnum.SIM.value());
                if (validacao) {
                    dataVestigioTristomideos.setEnabled(true);
                } else {
                    dataVestigioTristomideos.setEnabled(false);
                    dataVestigioTristomideos.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(dataVestigioTristomideos);
            }
        });

        ddUsoSangueHemoderivadosUltimos120Dias.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {

                boolean validacao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddUsoSangueHemoderivadosUltimos120Dias, SimNaoIgnoradoEnum.SIM.value());

                if (validacao) {
                    ddSorologicoHemoterapia.setEnabled(true);
                } else {
                    ddSorologicoHemoterapia.setEnabled(false);
                    ddSorologicoHemoterapia.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(ddSorologicoHemoterapia);
            }
        });

    }

    private void carregarDadosLaboratorio() {

        dataColetaS1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean validacao = dataColetaS1.getComponentValue() != null;

                if (validacao) {
                    ddSorologiaElisaIgmS1.setEnabled(true);
                    ddSorologiaElisaIggS1.setEnabled(true);
                    ddHemoaglutinacaoIgmS1.setEnabled(true);
                    ddHemoaglutinacaoIggS1.setEnabled(true);
                } else {
                    ddSorologiaElisaIgmS1.setEnabled(false);
                    ddSorologiaElisaIggS1.setEnabled(false);
                    ddHemoaglutinacaoIgmS1.setEnabled(false);
                    ddHemoaglutinacaoIggS1.setEnabled(false);
                    ddSorologiaElisaIgmS1.limpar(target);
                    ddSorologiaElisaIggS1.limpar(target);
                    ddHemoaglutinacaoIgmS1.limpar(target);
                    ddHemoaglutinacaoIggS1.limpar(target);
                }

                target.add(ddSorologiaElisaIgmS1, ddSorologiaElisaIggS1,
                        ddHemoaglutinacaoIgmS1, ddHemoaglutinacaoIggS1);
            }
        });

        dataColetaS2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadao = dataColetaS2.getComponentValue() != null;

                if (validadao) {
                    ddSorologiaElisaIgmS2.setEnabled(true);
                    ddSorologiaElisaIggS2.setEnabled(true);
                    ddHemoaglutinacaoIgmS2.setEnabled(true);
                    ddHemoaglutinacaoIggS2.setEnabled(true);
                } else {
                    ddSorologiaElisaIgmS2.setEnabled(false);
                    ddSorologiaElisaIggS2.setEnabled(false);
                    ddHemoaglutinacaoIgmS2.setEnabled(false);
                    ddHemoaglutinacaoIggS2.setEnabled(false);
                    ddSorologiaElisaIgmS2.limpar(target);
                    ddSorologiaElisaIggS2.limpar(target);
                    ddHemoaglutinacaoIgmS2.limpar(target);
                    ddHemoaglutinacaoIggS2.limpar(target);
                }

                target.add(ddSorologiaElisaIgmS2, ddSorologiaElisaIggS2,
                        ddHemoaglutinacaoIgmS2, ddHemoaglutinacaoIggS2);
            }
        });

        ddImunofluorenscenciaIgmS1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddImunofluorenscenciaIgmS1, InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.REAGENTE.value());

                if (validadao) {
                    txtImunofluorenscenciaIgmS1.setEnabled(true);
                } else {
                    txtImunofluorenscenciaIgmS1.setEnabled(false);
                    txtImunofluorenscenciaIgmS1.limpar(target);
                }

                target.add(txtImunofluorenscenciaIgmS1);
            }
        });

        ddImunofluorenscenciaIgmS2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddImunofluorenscenciaIgmS2, InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.REAGENTE.value());

                if (validadao) {
                    txtImunofluorenscenciaIgmS2.setEnabled(true);
                } else {
                    txtImunofluorenscenciaIgmS2.setEnabled(false);
                    txtImunofluorenscenciaIgmS2.limpar(target);
                }

                target.add(txtImunofluorenscenciaIgmS2);
            }
        });

        ddImunofluorescenciaIggS1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddImunofluorescenciaIggS1, InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.REAGENTE.value());

                if (validadao) {
                    txtImunofluorescenciaIggS1.setEnabled(true);
                } else {
                    txtImunofluorescenciaIggS1.setEnabled(false);
                    txtImunofluorescenciaIggS1.limpar(target);
                }

                target.add(txtImunofluorescenciaIggS1);
            }
        });

        ddImunofluorescenciaIggS2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddImunofluorescenciaIggS2, InvestigacaoAgravoFebreMaculosaOutrasRickettsiosesEnum.ReagenteNaoReagenteEnum.REAGENTE.value());

                if (validadao) {
                    txtImunofluorescenciaIggS2.setEnabled(true);
                } else {
                    txtImunofluorescenciaIggS2.setEnabled(false);
                    txtImunofluorescenciaIggS2.limpar(target);
                }

                target.add(txtImunofluorescenciaIggS2);
            }
        });

    }

    private void carregarTratamento() {

        ddTipoTratamentoEspecifico.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadaoSintomatico = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoTratamentoSintomatico, SimNaoIgnoradoEnum.SIM.value());
                boolean validadaoEspecifico = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoTratamentoEspecifico, SimNaoIgnoradoEnum.SIM.value());

                if (validadaoEspecifico || validadaoSintomatico) {
                    ddDrogaTratamentoEspecifico.setEnabled(true);
                    txtTempoTratamentoDias.setEnabled(true);
                } else {
                    ddDrogaTratamentoEspecifico.setEnabled(false);
                    ddDrogaTratamentoEspecifico.limpar(target);

                    txtTempoTratamentoDias.setEnabled(false);
                    txtTempoTratamentoDias.limpar(target);
                }

                target.add(ddDrogaTratamentoEspecifico, txtTempoTratamentoDias);
            }
        });

        ddTipoTratamentoSintomatico.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                boolean validadaoSintomatico = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoTratamentoSintomatico, SimNaoIgnoradoEnum.SIM.value());
                boolean validadaoEspecifico = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddTipoTratamentoEspecifico, SimNaoIgnoradoEnum.SIM.value());

                if (validadaoEspecifico || validadaoSintomatico) {
                    ddDrogaTratamentoEspecifico.setEnabled(true);
                    txtTempoTratamentoDias.setEnabled(true);
                } else {
                    ddDrogaTratamentoEspecifico.setEnabled(false);
                    ddDrogaTratamentoEspecifico.limpar(target);

                    txtTempoTratamentoDias.setEnabled(false);
                    txtTempoTratamentoDias.limpar(target);
                }

                target.add(ddDrogaTratamentoEspecifico, txtTempoTratamentoDias);
            }
        });

    }

    private void carregarConclusao() {

        ddClassificacaoFinal.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {

                boolean validacao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddClassificacaoFinal, InvestigacaoAgravoCoqueluche.ClassificacaoFinal.CONFIRMADO.value());

                if (validacao) {
                    ddCriterioConfirmacaoDescarte.setEnabled(true);
                    ddEvolucaoCaso.setEnabled(true);
                    ddLocalProvavelInfeccao.setEnabled(true);
                } else {
                    ddCriterioConfirmacaoDescarte.setEnabled(false);
                    ddEvolucaoCaso.setEnabled(false);
                    dataObito.setEnabled(false);
                    ddLocalProvavelInfeccao.setEnabled(false);

                    ddCriterioConfirmacaoDescarte.limpar(ajaxRequestTarget);
                    ddEvolucaoCaso.limpar(ajaxRequestTarget);
                    dataObito.limpar(ajaxRequestTarget);
                    ddLocalProvavelInfeccao.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(ddCriterioConfirmacaoDescarte, ddEvolucaoCaso,
                        dataObito);
            }
        });

        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {

                boolean validacaoObitoChagas = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, EvolucaoCasoDoencaChagasEnum.OBITO_POR_CHAGAS.value());
                boolean validacaoObitoOutros = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, EvolucaoCasoDoencaChagasEnum.OBITO_OUTRAS_CAUSAS.value());

                if (validacaoObitoChagas || validacaoObitoOutros) {
                    dataObito.setEnabled(true);
                } else {
                    dataObito.setEnabled(false);
                    dataObito.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(dataObito);
            }
        });

    }

    private void carregarCasoAutoctone() {

        ddLocalProvavelInfeccao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {

                boolean validacao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddLocalProvavelInfeccao, LocalProvavelInfeccaoEnum.OUTRO.value());

                if (validacao) {
                    txtLocalProvavelInfeccao.setEnabled(true);
                } else {
                    txtLocalProvavelInfeccao.setEnabled(false);
                    txtLocalProvavelInfeccao.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(txtLocalProvavelInfeccao);
            }
        });

        ddModoProvavelInfeccao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {

                boolean validacao = !isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddModoProvavelInfeccao, ModoProvavelInfeccaoDoencaChagasEnum.OUTRA.value());

                if (validacao) {
                    txtModoProvavelInfeccao.setEnabled(true);
                } else {
                    txtModoProvavelInfeccao.setEnabled(false);
                    txtModoProvavelInfeccao.limpar(ajaxRequestTarget);
                }

                ajaxRequestTarget.add(txtModoProvavelInfeccao);
            }
        });

        boolean notAutoctone = investigacaoAgravo.getCasoAutoctone() != null && investigacaoAgravo.getCasoAutoctone() == 2L;
        containerCasoAutoctone.setEnabled(notAutoctone);

        autoCompletePaisLocalInfeccao.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoLocalInfeccao.limpar(target);
                    autoCompleteCidadeLocalInfeccao.limpar(target);
                    codMunicipioLocalInfeccao.limpar(target);
                }
            }
        });

        autoCompletePaisLocalInfeccao.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteCidadeLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioLocalInfeccao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoLocalInfeccao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioLocalInfeccao, estadoLocalInfeccao);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);
            }
        });
    }


    //TABELA
    private void criarDeslocamento() {
        InvestigacaoAgravoDoencaChagasDeslocamento proxy = on(InvestigacaoAgravoDoencaChagasDeslocamento.class);
        modelDeslocamento = new CompoundPropertyModel(new InvestigacaoAgravoDoencaChagasDeslocamento());
        containerDeslocamento = new WebMarkupContainer("containerDeslocamento", modelDeslocamento);
        containerDeslocamento.setOutputMarkupId(true);

        autoCompleteCidadeDeslocamento = new AutoCompleteConsultaCidade(path(proxy.getCidadeDeslocamentoTab()));
        estadoUfDeslocamento = new DisabledInputField(path(proxy.getTxtUfEstado()));
        autoCompletePaisDeslocamento = new AutoCompleteConsultaPais(path(proxy.getPaisDeslocamentoTab()));

        autoCompleteCidadeDeslocamento.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                estadoUfDeslocamento.setComponentValue(cidade.getEstado().getSigla());
                target.add(estadoUfDeslocamento);
            }
        });

        AbstractAjaxButton btnAdicionarDeslocamento = new AbstractAjaxButton("btnAdicionarDeslocamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                btnAdicionarDeslocamento(target);
            }
        };
        btnAdicionarDeslocamento.setDefaultFormProcessing(false);
        tblDeslocamento = new Table("tblDeslocamento", getColumns(), getCollectionProvider());
        tblDeslocamento.populate();

        containerDeslocamento.add(btnAdicionarDeslocamento, tblDeslocamento, autoCompleteCidadeDeslocamento,
                estadoUfDeslocamento, autoCompletePaisDeslocamento);
        getContainerInformacoesComplementares().add(containerDeslocamento);
    }

    private void btnAdicionarDeslocamento(AjaxRequestTarget target) throws ValidacaoException {
        InvestigacaoAgravoDoencaChagasDeslocamento deslocamento = modelDeslocamento.getObject();

        if (deslocamento.getPaisDeslocamentoTab() == null) {
            throw new ValidacaoException(bundle("msgInformePais"));
        }

        if (deslocamento.getCidadeDeslocamentoTab() == null) {
            throw new ValidacaoException(bundle("informeCidade"));
        }

        deslocamentoList.add(deslocamento);
        tblDeslocamento.update(target);
        tblDeslocamento.populate();

        modelDeslocamento.setObject(new InvestigacaoAgravoDoencaChagasDeslocamento());
        ComponentUtils.limparContainer(containerDeslocamento, target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        InvestigacaoAgravoDoencaChagasDeslocamento proxy = on(InvestigacaoAgravoDoencaChagasDeslocamento.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("municipio"), proxy.getCidadeDeslocamentoTab().getDescricao()));
        columns.add(createColumn(bundle("uf"), proxy.getCidadeDeslocamentoTab().getEstado().getSigla()));
        columns.add(createColumn(bundle("pais"), proxy.getPaisDeslocamentoTab().getDescricao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<InvestigacaoAgravoDoencaChagasDeslocamento>() {
            @Override
            public void customizeColumn(InvestigacaoAgravoDoencaChagasDeslocamento rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<InvestigacaoAgravoDoencaChagasDeslocamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, InvestigacaoAgravoDoencaChagasDeslocamento modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblDeslocamento, deslocamentoList, modelObject);
                        tblDeslocamento.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (deslocamentoList == null) {
                    deslocamentoList = new ArrayList<>();
                }
                return deslocamentoList;
            }
        };
    }

    private void carregarDeslocamento() {

        autoCompletePaisDeslocamento.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoUfDeslocamento.limpar(target);
                    autoCompleteCidadeDeslocamento.limpar(target);
                }else {
                }
            }
        });
        autoCompletePaisDeslocamento.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                estadoUfDeslocamento.limpar(target);
                autoCompleteCidadeDeslocamento.limpar(target);
            }
        });

        autoCompleteCidadeDeslocamento.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                estadoUfDeslocamento.limpar(target);
                autoCompletePaisDeslocamento.limpar(target);
            }
        });

    }
}
