package br.com.celk.view.materiais.emprestimo.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.TipoEmprestimo;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaTipoEmprestimoPage extends ConsultaPage<OrdemCompra, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField<Long>("descricao", new PropertyModel(this, "descricao")));
        
        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        TipoEmprestimo proxy = on(TipoEmprestimo.class);
        
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("descricao"), proxy.getDescricaoTipoEmprestimo()));
        columns.add(createSortableColumn(bundle("tipo"), proxy.getTipoEmprestimo(), proxy.getDescricaoFlagTipoEmprestimo()));
        columns.add(createSortableColumn(bundle("tipoDocumento"), proxy.getTipoDocumentoEstoque().getDescricao()));
        return columns;
    }

    private CustomColumn<TipoEmprestimo> getCustomColumn() {
        return new CustomColumn<TipoEmprestimo>() {

            @Override
            public Component getComponent(String componentId, final TipoEmprestimo rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoEmprestimoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroTipoEmprestimoPage(rowObject, true));
                    }
                };
            }
        };
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return TipoEmprestimo.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(TipoEmprestimo.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTOQUE, TipoDocumento.PROP_CODIGO),
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTOQUE, TipoDocumento.PROP_DESCRICAO),
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTOQUE, TipoDocumento.PROP_FLAG_TIPO_MOVIMENTO),
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTORNO, TipoDocumento.PROP_CODIGO),
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTORNO, TipoDocumento.PROP_DESCRICAO),
                    VOUtils.montarPath(TipoEmprestimo.PROP_TIPO_DOCUMENTO_ESTORNO, TipoDocumento.PROP_FLAG_TIPO_MOVIMENTO),
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(TipoEmprestimo.PROP_DESCRICAO_TIPO_EMPRESTIMO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(TipoEmprestimo.PROP_DESCRICAO_TIPO_EMPRESTIMO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroTipoEmprestimoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaTipoEmprestimo");
    }
}