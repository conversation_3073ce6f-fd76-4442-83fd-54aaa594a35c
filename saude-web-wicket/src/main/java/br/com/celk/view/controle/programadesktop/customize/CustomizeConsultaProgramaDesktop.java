/*
 *  Copyright 2012 claudio.
 * 
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 * 
 *       http://www.apache.org/licenses/LICENSE-2.0
 * 
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *  under the License.
 */

package br.com.celk.view.controle.programadesktop.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.controle.Menu;
import br.com.ksisolucoes.vo.controle.Modulo;
import br.com.ksisolucoes.vo.controle.Programa;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaProgramaDesktop extends CustomizeConsultaAdapter {

    @Override
    public Class getClassConsulta() {
        return Programa.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(new HQLProperties(Programa.class).getProperties(),
            new String[]{
            VOUtils.montarPath(Programa.PROP_MENU,Menu.PROP_MODULO,Modulo.PROP_NOME),
            VOUtils.montarPath(Programa.PROP_MENU,Menu.PROP_MODULO,Modulo.PROP_CODIGO),
        });
    }

}
