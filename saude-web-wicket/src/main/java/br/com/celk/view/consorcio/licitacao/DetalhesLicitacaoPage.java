package br.com.celk.view.consorcio.licitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.consorcio.licitacao.dialog.DlgConsultaLicitacaoItemOcorrencia;
import br.com.celk.view.consorcio.pedidotransferencialicitacao.CadastroPedidoTransferenciaLicitacaoPage;
import br.com.celk.view.consorcio.tipoconta.autocomplete.AutoCompleteConsultaTipoConta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItemOcorrencia;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
@Private

public class DetalhesLicitacaoPage extends BasePage {

    private Table<LicitacaoItem> tblItens;

    private List<LicitacaoItem> itens = new ArrayList<LicitacaoItem>();

    private Licitacao licitacao;
    private WebMarkupContainer containerTipoConta;
    private AutoCompleteConsultaTipoConta autoCompleteConsultaTipoConta;
    private String utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    private TipoConta tipoConta;
    private DisabledInputField tipoContaDescricao;
    private DlgConsultaLicitacaoItemOcorrencia dlgConsultaLicitacaoItemOcorrencia;


    @Deprecated
    public DetalhesLicitacaoPage() {
    }

    public DetalhesLicitacaoPage(Licitacao licitacao) {
        this.licitacao = licitacao;
        carregarItens();
        init();
    }

    public void init() {
        Form form = new Form("form", new CompoundPropertyModel(this.licitacao));
        form.add(new DisabledInputField(Licitacao.PROP_CODIGO));
        form.add(new DisabledInputField(Licitacao.PROP_NUMERO_PREGAO));
        form.add(new DisabledInputField(Licitacao.PROP_DATA_VALIDADE));
        form.add(new DisabledInputField(Licitacao.PROP_DESCRICAO_STATUS));
        form.add(new DisabledInputField(VOUtils.montarPath(Licitacao.PROP_USUARIO_CADASTRO, Usuario.PROP_NOME)));
        form.add(new DisabledInputField(Licitacao.PROP_DATA_CADASTRO));
        form.add(new DisabledInputField(VOUtils.montarPath(Licitacao.PROP_USUARIO_APROVACAO, Usuario.PROP_NOME)));
        form.add(new DisabledInputField(Licitacao.PROP_DATA_APROVACAO));
        form.add(new DisabledInputField(VOUtils.montarPath(Licitacao.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_NOME)));
        form.add(new DisabledInputField(Licitacao.PROP_DATA_CANCELAMENTO));
        form.add(new DisabledInputArea(Licitacao.PROP_OBSERVACAO));

        containerTipoConta = new WebMarkupContainer("containerTipoConta", new CompoundPropertyModel(this));
        containerTipoConta.add(tipoContaDescricao = new DisabledInputField(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO, Licitacao.PROP_TIPO_CONTA, TipoConta.PROP_DESCRICAO)));

        if (RepositoryComponentDefault.NAO.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())) {
            containerTipoConta.setVisible(false);
            tipoContaDescricao.setEnabled(false);
        } else {
            tipoContaDescricao.setEnabled(false);
        }

        form.add(containerTipoConta);

        form.add(tblItens = new Table("tblItens", getColumnsItens(), getCollectionItens()));
        tblItens.setScrollX("1400px");
        tblItens.setScrollY("500px");
        tblItens.populate();

        form.add(new VoltarButton("btnVoltar"));

        add(form);
    }

    private List<IColumn> getColumnsItens() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(LicitacaoItem.class);
        columns.add(getActionColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("un"), VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("fornecedor"), VOUtils.montarPath(LicitacaoItem.PROP_PESSOA, Pessoa.PROP_DESCRICAO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(LicitacaoItem.PROP_QUANTIDADE_LICITADA)));
        columns.add(new DoubleColumn(BundleManager.getString("precoUn"), VOUtils.montarPath(LicitacaoItem.PROP_PRECO_UNITARIO)).setCasasDecimais(4));
        columns.add(columnFactory.createColumn(BundleManager.getString("total"), VOUtils.montarPath(LicitacaoItem.PROP_PRECO_TOTAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("situacao"), VOUtils.montarPath(LicitacaoItem.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createColumn(BundleManager.getString("justificativa"), VOUtils.montarPath(LicitacaoItem.PROP_JUSTIFICATIVA)));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LicitacaoItem>() {
            @Override
            public void customizeColumn(final LicitacaoItem rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LicitacaoItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, LicitacaoItem modelObject) throws ValidacaoException, DAOException {
                        initDlgConsultaLicitacaoItemOcorrencia(target);
                        dlgConsultaLicitacaoItemOcorrencia.show(target, rowObject);
                    }
                }).setEnabled(existsOcorrencia(rowObject));
            }
        };
    }

    private void initDlgConsultaLicitacaoItemOcorrencia(AjaxRequestTarget target) {
        if (dlgConsultaLicitacaoItemOcorrencia == null) {
            addModal(target, dlgConsultaLicitacaoItemOcorrencia = new DlgConsultaLicitacaoItemOcorrencia(newModalId()) {
                @Override
                public void show(AjaxRequestTarget target, LicitacaoItem licitacaoItem) {
                    super.show(target, licitacaoItem);
                }
            });
        }
    }

    private ICollectionProvider getCollectionItens() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }


    private boolean existsOcorrencia(LicitacaoItem licitacaoItem) {
     return LoadManager.getInstance(LicitacaoItemOcorrencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LicitacaoItemOcorrencia.PROP_LICITACAO_ITEM, licitacaoItem))
                .exists();
    }

        @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesLicitacao");
    }

    private void carregarItens() {
        itens = LoadManager.getInstance(LicitacaoItem.class)
                .addProperties(new HQLProperties(LicitacaoItem.class).getProperties())
                .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), this.licitacao))
                .start().getList();
    }

    public String getUtilizarControleFinanceiroPedidoTransferenciaConsorcio() {
        if (utilizarControleFinanceiroPedidoTransferenciaConsorcio == null) {
            try {
                utilizarControleFinanceiroPedidoTransferenciaConsorcio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("utilizarControleFinanceiroPedidoTransferenciaConsorcio");
            } catch (DAOException ex) {
                Logger.getLogger(CadastroPedidoTransferenciaLicitacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    }
}
