package br.com.celk.view.comunicacao.mensagem.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.comunicacao.mensagem.autocomplete.MessagingAutoCompleteGrupoMensagem;
import br.com.celk.view.comunicacao.mensagem.autocomplete.MessagingAutoCompleteUsuario;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.core.javascript.JsQuery;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlEnviarMensagem extends ViewPanel{

    private Form<MensagemDTO> form;
    private MessagingAutoCompleteUsuario messagingAutoCompleteUsuario;
    private MessagingAutoCompleteGrupoMensagem messagingAutoCompleteGrupoMensagem;
    private InputField txtAssunto;
    private InputArea txaMensagem;
    
    public PnlEnviarMensagem(String id) {
        super(id);
    }

    @Override
    public void postConstruct() {
        super.postConstruct(); //To change body of generated methods, choose Tools | Templates.
        
        MensagemDTO mensagemProxy = on(MensagemDTO.class);
        
        getForm().add(messagingAutoCompleteUsuario = new MessagingAutoCompleteUsuario(path(mensagemProxy.getUsuarios())));
        getForm().add(messagingAutoCompleteGrupoMensagem = new MessagingAutoCompleteGrupoMensagem(path(mensagemProxy.getGrupos())));
        getForm().add(txtAssunto = new InputField(path(mensagemProxy.getAssunto())));
        getForm().add(txaMensagem = new InputArea(path(mensagemProxy.getMensagem())));
        
        getForm().add(new AbstractAjaxButton("linkEnviar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(ComunicacaoFacade.class).enviarMensagem(PnlEnviarMensagem.this.getForm().getModelObject());
                limpar(target);
                fechar(target);
            }
        });
        
        getForm().add(new AbstractAjaxButton("linkFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                fechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(getForm());
    }
    
    private Form<MensagemDTO> getForm(){
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(new MensagemDTO()));
        }
        
        return this.form;
    }
    
    public void limpar(AjaxRequestTarget target){
        messagingAutoCompleteUsuario.limpar(target);
        messagingAutoCompleteGrupoMensagem.limpar(target);
        txtAssunto.limpar(target);
        txaMensagem.limpar(target);
    }
    
    public void setDestinatarios(List<Usuario> usuarios){
        getForm().getModelObject().setUsuarios(usuarios);
    }
    
    public abstract void fechar(AjaxRequestTarget target);

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnLoadHeaderItem.forScript(new JsQuery(txtAssunto).$().chain("focus").getStatement()));
    }

}
