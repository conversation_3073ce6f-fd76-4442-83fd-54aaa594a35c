package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLivro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RequerimentoLivroFechamentoPage extends BasePage {

    private Form<RequerimentoLivroDTO> form;
    private RequerimentoLivro requerimentoLivro;
    private RequerimentoVigilancia requerimentoVigilancia;
    private TipoSolicitacao tipoSolicitacao;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgImpressaoObjectMulti;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private DisabledInputField<String> txtEnderecoEstabelecimento;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private InputField txtNumeroPaginas;
    private DateChooser dchDataFinalizacao;
    private SubmitButton btnSalvar;
    private boolean enabled;
    private boolean finalizarTelaConsulta;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;
    private Class classReturn;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private DropDown<Long> dropDownTipo;

    public RequerimentoLivroFechamentoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true, false);
        this.classReturn = clazz;
    }

    public RequerimentoLivroFechamentoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, boolean finalizarTelaConsulta, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoLivro(requerimentoVigilancia);
        if (!RequerimentoVigilancia.Situacao.getSituacoesFinalizado().contains(requerimentoVigilancia.getSituacao())) {
            init(viewOnly, finalizarTelaConsulta);
        } else {
            init(false, false);
        }
        this.classReturn = clazz;
    }

    private void init(final boolean viewOnly, boolean finalizarTelaConsulta) {
        this.enabled = viewOnly;
        this.finalizarTelaConsulta = finalizarTelaConsulta;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoLivroDTO proxy = on(RequerimentoLivroDTO.class);
        getForm().add(new DisabledInputField<String>(path(proxy.getRequerimentoLivro().getRequerimentoVigilancia().getProtocoloFormatado())));
        getForm().add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoLivro().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setOutputMarkupId(true);
        autoCompleteConsultaEstabelecimento.addAjaxUpdateValue();
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setEnabled(enabled);
        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                carregarTiposAberturaLivro(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                btnSalvar.setEnabled(false);
                target.add(btnSalvar);
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
                if (target != null) {
                    pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
                }
                dropDownTipo.removeAllChoices();
                dropDownTipo.addChoice(null, "");
                limpar(target);
                getSession().getFeedbackMessages().clear();
                updateNotification(target);
            }
        });
        getForm().add(txtEnderecoEstabelecimento = new DisabledInputField<>(path(proxy.getRequerimentoLivro().getRequerimentoVigilancia().getEnderecoFormatado())));
        getForm().add(dropDownTipo = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoLivro().getTipo()), RequerimentoLivro.Tipo.values(), true, "", true));
        dropDownTipo.addAjaxUpdateValue();
        dropDownTipo.setEnabled(enabled);
        dropDownTipo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (autoCompleteConsultaEstabelecimento.getComponentValue() != null) {
                    if (dropDownTipo.getComponentValue() == null){
                        btnSalvar.setEnabled(false);
                        target.add(btnSalvar);
                        pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
                        if (target != null) {
                            pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
                        }
                        limpar(target);
                        getSession().getFeedbackMessages().clear();
                        updateNotification(target);
                    }else{
                        carregarRequerimentoLivroPendente(target);
                    }
                } else {
                    warn(target, bundle("informeEstabelecimento"));
                    dropDownTipo.limpar(target);
                }
            }
        });
        getForm().add(txtNumeroPaginas = (InputField) new DisabledInputField(path(proxy.getRequerimentoLivro().getNumeroPaginas())).setLabel((new Model(bundle("nPaginas")))));
        getForm().add(dchDataFinalizacao = (DateChooser) new RequiredDateChooser(path(proxy.getRequerimentoLivro().getDataFinalizacao())).setLabel(new Model(bundle("dataFinalizacao"))).setEnabled(enabled || finalizarTelaConsulta));

        getForm().add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia(), enabled));

        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper
                .createDadosComumRequerimentoVigilanciaDTOParam(getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia());
        dadosComumParam.setDesabilitaFiscais(true);
        getForm().add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, (enabled || finalizarTelaConsulta)));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        getForm().add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(btnSalvar = (SubmitButton) new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        btnSalvar.setEnabled(enabled || finalizarTelaConsulta);

        getForm().add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().getCodigo(), false).setVisible(!enabled && !finalizarTelaConsulta));

        add(getForm());
    }

    private void carregarTiposAberturaLivro(AjaxRequestTarget target, Estabelecimento object) {
        List<RequerimentoLivro> listRequerimento;

        listRequerimento = LoadManager.getInstance(RequerimentoLivro.class)
                .addProperties(new HQLProperties(RequerimentoLivro.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoLivro.PROP_ESTABELECIMENTO).getProperties())
                .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLivro.PROP_ESTABELECIMENTO, object))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.NOT_IN, RequerimentoVigilancia.Situacao.getSituacoesFinalizado()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(listRequerimento)) {
            List<Long> tipoList = Lambda.extract(listRequerimento, Lambda.on(RequerimentoLivro.class).getTipo());
                dropDownTipo.setEnabled(true);
                dropDownTipo.removeAllChoices();
                dropDownTipo.addChoice(null, "");
                for (Long tipo : tipoList) {
                    dropDownTipo.addChoice(tipo, RequerimentoLivro.Tipo.valueOf(tipo).descricao());
                }
            }
        target.add(dropDownTipo);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().getCpfSolicitante() == null
                && getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }

        getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        getForm().getModel().getObject().setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        atualizarDadosComuns();
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoFechamentoLivro(getForm().getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoAberturaFechamento");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServicoTermoAberturaFechamento");
        }

        addModal(target, dlgImpressaoObjectMulti = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport dataReportLandscape = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                RelatorioRequerimentoLivroVigilanciaTermoDTOParam paramTermo = new RelatorioRequerimentoLivroVigilanciaTermoDTOParam();
                paramTermo.setRequerimentoVigilancia(object);
                paramTermo.setAberturaLivro(false);

                DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).relatorioRequerimentoLivroVigilanciaTermo(paramTermo);

                List<IReport> lstDataReport = new ArrayList();
                if (dataReportLandscape != null && dataReportLandscape.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReportLandscape);
                }
                if (dataReport != null && dataReport.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReport);
                }

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dlgImpressaoObjectMulti.show(target, rv);
    }

    private Form<RequerimentoLivroDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(new RequerimentoLivroDTO()));
            if (requerimentoLivro != null) {
                form.getModel().getObject().setRequerimentoLivro(requerimentoLivro);
            } else {
                form.getModel().getObject().setRequerimentoLivro(new RequerimentoLivro());
                form.getModel().getObject().getRequerimentoLivro().setRequerimentoVigilancia(new RequerimentoVigilancia());
            }
        }
        return form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("fechamentoLivroControle");
    }

    private void carregarRequerimentoLivro(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoLivro = LoadManager.getInstance(RequerimentoLivro.class)
                    .addProperties(new HQLProperties(RequerimentoLivro.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoLivro.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();

            requerimentoLivro.setRequerimentoVigilancia(requerimentoVigilancia);
            if (TipoSolicitacao.TipoDocumento.FECHAMENTO_LIVRO_CONTROLE.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                carregarAnexos(requerimentoVigilancia);
            }
        }
    }

    private void carregarRequerimentoLivroPendente(AjaxRequestTarget target) {
        RequerimentoLivro requerimentoLivro = getForm().getModel().getObject().getRequerimentoLivro();

//       limpar(target);

        this.requerimentoLivro = LoadManager.getInstance(RequerimentoLivro.class)
                .addProperties(new HQLProperties(RequerimentoLivro.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, RequerimentoLivro.PROP_ESTABELECIMENTO).getProperties())
                .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_VIGILANCIA_ENDERECO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLivro.PROP_ESTABELECIMENTO, requerimentoLivro.getEstabelecimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLivro.PROP_TIPO, requerimentoLivro.getTipo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoLivro.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.NOT_IN, RequerimentoVigilancia.Situacao.getSituacoesFinalizado()))
                .start().getVO();

            if (this.requerimentoLivro != null) {
            updateNotification(target);
            getSession().getFeedbackMessages().clear();
            updateNotificationPanel(target);
            getForm().getModel().getObject().setRequerimentoLivro(this.requerimentoLivro);

            if (requerimentoLivro.getEstabelecimento().getVigilanciaEndereco() != null && requerimentoLivro.getEstabelecimento().getVigilanciaEndereco().getCodigo() != null) {
                VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(requerimentoLivro.getEstabelecimento().getVigilanciaEndereco().getCodigo()).start().getVO();
                form.getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            }

            target.add(txtEnderecoEstabelecimento);
            target.add(txtNumeroPaginas);
            target.add(dchDataFinalizacao);
            btnSalvar.setEnabled(true);
            if (pnlDadosComumRequerimentoVigilancia != null) {
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
            }

            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(SetorVigilancia.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_SETOR_VIGILANCIA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, requerimentoLivro.getEstabelecimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();

            if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null
                    && estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(estabelecimentoAtividade.getAtividadeEstabelecimento().getSetorVigilancia());
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
                if (target != null) {
                    pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
                }
            }
        } else {
            warn(target, bundle("msgNaoExisteNenhumLivroControlePendenteEstabelecimentoX", requerimentoLivro.getEstabelecimento().getRazaoSocial()));
            updateNotificationPanel(target);
            updateNotification(target);
            btnSalvar.setEnabled(false);
            autoCompleteConsultaEstabelecimento.limpar(target);
            target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField()));
        }

        target.add(btnSalvar);
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void updateNotification(AjaxRequestTarget target) {
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(RequerimentoLivroFechamentoPage.this);
        if (findNotificationPanel != null) {
            getSession().getFeedbackMessages().clear();
            if (target != null) {
                findNotificationPanel.updateNotificationPanel(target);
            }
        }
    }

    private void limpar(AjaxRequestTarget target) {
        getForm().getModel().getObject().setRequerimentoLivro(new RequerimentoLivro());

        target.add(txtEnderecoEstabelecimento);
        target.add(dropDownTipo);
        target.add(txtNumeroPaginas);
        target.add(dchDataFinalizacao);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
    }

    private void atualizarDadosComuns() {
        getForm().getModel().getObject().getRequerimentoLivro().getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        getForm().getModel().getObject().setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
    }
}