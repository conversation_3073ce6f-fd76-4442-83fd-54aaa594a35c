package br.com.celk.view.frota.registromanutencao;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.RegistroManutencaoItem;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgDetalhesRegistroManutencaoItem extends Window{

    private PnlDetalhesRegistroManutencaoItem pnlDetalhesRegistroManutencaoItem;
    
    public DlgDetalhesRegistroManutencaoItem(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        setInitialWidth(700);
        setInitialHeight(350);
        
        setResizable(false);
        
        setTitle(BundleManager.getString("detalhesItem"));
        
        setContent(pnlDetalhesRegistroManutencaoItem = new PnlDetalhesRegistroManutencaoItem(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public void setModelObject(RegistroManutencaoItem registroManutencaoItem){
        pnlDetalhesRegistroManutencaoItem.setModelObject(registroManutencaoItem);
    }

}
