package br.com.celk.view.materiais.compras.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.FundoConsorcio;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConsultarDetalhesOrdemCompraItem extends Window{
    
    private PnlConsultarDetalhesOrdemCompraItem pnlConsultarDetalhesOrdemCompraItem;
    private FundoConsorcio fundoConsorcio;

    public DlgConsultarDetalhesOrdemCompraItem(String id){
        super(id);
        init();
    }

    public DlgConsultarDetalhesOrdemCompraItem(String id, FundoConsorcio fundoConsorcio) {
        super(id);
        this.fundoConsorcio = fundoConsorcio;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("detalhesItem");
            }
        });

        setInitialWidth(700);
        setInitialHeight(400);
        setResizable(true);
        
        setContent(pnlConsultarDetalhesOrdemCompraItem = new PnlConsultarDetalhesOrdemCompraItem(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public void show(AjaxRequestTarget target, OrdemCompraItem ordemCompraItem){
        show(target);
        pnlConsultarDetalhesOrdemCompraItem.limpar(target);
        try {
            pnlConsultarDetalhesOrdemCompraItem.setOrdemCompraItem(ordemCompraItem, fundoConsorcio);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

    }    
}