package br.com.celk.view.agenda.agendamento.leito;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;

public abstract class DlgClassificarAih extends Window {

    private PnlClassificarAih pnlClassificarAih;
    public DlgClassificarAih(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("classficarAih");
            }
        });

        setInitialWidth(550);
        setInitialHeight(500);

        setResizable(true);

        setContent(pnlClassificarAih = new PnlClassificarAih(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws ValidacaoException, DAOException {
                onFechar(target);
                DlgClassificarAih.this.onConfirmar(target, autorizacaoInternacaoHospitalarDTO, classificacaoRisco, motivo);
            }

            @Override
            public void onConfirmarAutorizar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws ValidacaoException, DAOException {
                onFechar(target);
                DlgClassificarAih.this.onConfirmarAutorizar(target, autorizacaoInternacaoHospitalarDTO, classificacaoRisco, motivo);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlClassificarAih.getInputAreaMotivo();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws ValidacaoException, DAOException;

    public abstract void onConfirmarAutorizar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO, ClassificacaoRisco classificacaoRisco, String motivo) throws ValidacaoException, DAOException;
    public void show(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO autorizacaoInternacaoHospitalarDTO) {
        pnlClassificarAih.setAutorizacaoInternacaoHospitalarDTO(target, autorizacaoInternacaoHospitalarDTO);
        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        close(target);
    }
}
