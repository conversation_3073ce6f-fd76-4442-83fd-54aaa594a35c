package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum ClassificacaoClinicaEnum implements IEnum<SimNaoEnum> {
    PRIMARIA(1L, "Primária"),
    SECUNDARIA(2L, "Secundária"),
    TERCIARIA(3L, "Terciária"),
    LATENTE(4L, "Latente"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    ClassificacaoClinicaEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static ClassificacaoClinicaEnum valueOf(Long value) {
        for (ClassificacaoClinicaEnum v : ClassificacaoClinicaEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
