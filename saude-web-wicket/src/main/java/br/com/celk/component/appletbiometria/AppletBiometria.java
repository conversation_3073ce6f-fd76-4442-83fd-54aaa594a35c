package br.com.celk.component.appletbiometria;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import com.google.gson.Gson;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.jar.Manifest;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.IHeaderContributor;
import org.apache.wicket.markup.html.WebComponent;
import org.apache.wicket.protocol.http.servlet.ServletWebRequest;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.util.value.IValueMap;
import org.apache.wicket.util.value.ValueMap;

/**
 * <AUTHOR>
 * Criado em: Jan 2, 2014
 */
public class AppletBiometria extends WebComponent implements IHeaderContributor {

    private static final long serialVersionUID = 1L;

    private static final String ATTRIBUTE_WIDTH = "width";
    private static final String ATTRIBUTE_HEIGHT = "height";
    private static final String ATTRIBUTE_CODE = "code";
    private static final String ATTRIBUTE_CODEBASE = "codebase";
    private static final String ATTRIBUTE_ID = "id";
    private static final String ATTRIBUTE_ARCHIVE = "archive";

    private String minimalVersion;
    private IValueMap appletAttributes = new ValueMap();
    private IValueMap appletParameters = new ValueMap();
    private HashMap<Object, IAppletAction> appletListener = new LinkedHashMap<Object, IAppletAction>();
    private AbstractDefaultAjaxBehavior behavior;
    private boolean usaBiometria;

    @Deprecated
    public AppletBiometria(String id) {
        super(id);
        
        try {
            init();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void configuracoes(){
        String protocol = ((ServletWebRequest)RequestCycle.get().getRequest()).getContainerRequest().getRequestURL().toString();
        protocol = protocol.substring(0, protocol.indexOf(":"));
        String host = ((ServletWebRequest)RequestCycle.get().getRequest()).getContainerRequest().getServerName();
        int port = ((ServletWebRequest)RequestCycle.get().getRequest()).getContainerRequest().getServerPort();

        try {
            Manifest manifest = new Manifest(Application.get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
            String version = manifest.getMainAttributes().getValue("Gem-Version");
            
            setArchive("applet-biometria-"+version+".jar");
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        setId("bioApplet");
        setCodebase(protocol+"://"+host+":"+port+"/applets");
        setCode("br.com.celk.applet.biometria.AppletMain.class");
        setWidth(49);
        setHeight(49);
        setMinimalVersion("1.6");

//<editor-fold defaultstate="collapsed" desc="JNLP">
//        criar template do jnlp para ver se o java libera a execução da aplicação, hoje está bloqueando por questão de segurança
//        addParameter("jnlp_href","biometria_applet.jnlp");
//        addParameter("jnlp_embedded","PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVV"
//                + "RGLTgiPz4NCjxqbmxwIGhyZWY9ImJpb21ldHJpYV9hcHBsZXQuam5scCI+DQo"
//                + "gICAgPGluZm9ybWF0aW9uPg0KICAgICAgICA8dGl0bGU+QmlvbWV0cmlhIENF"
//                + "TEs8L3RpdGxlPg0KICAgICAgICA8dmVuZG9yPkNFTEsgU2lzdGVtYXM8L3Zlb"
//                + "mRvcj4NCiAgICA8L2luZm9ybWF0aW9uPg0KICAgIDxyZXNvdXJjZXM+DQogIC"
//                + "AgICAgIDxqMnNlIHZlcnNpb249IjEuNysiIC8+DQogICAgICAgIDxqYXIgaHJ"
//                + "lZj0iYmlvbWV0cmlhL2FwcGxldC1iaW9tZXRyaWEtMy4wLjExLVNOQVBTSE9U"
//                + "LmphciIgbWFpbj0idHJ1ZSIgLz4NCiAgICA8L3Jlc291cmNlcz4NCiAgICA8Y"
//                + "XBwbGV0LWRlc2MgDQogICAgICAgICBuYW1lPSJCaW9tZXRyaWEgQ0VMSyINCi"
//                + "AgICAgICAgIG1haW4tY2xhc3M9ImJyLmNvbS5jZWxrLmFwcGxldC5iaW9tZXRy"
//                + "aWEuQXBwbGV0TWFpbiINCiAgICAgICAgIHdpZHRoPSIzMDAiDQogICAgICAgI"
//                + "CBoZWlnaHQ9IjMwMCI+DQogICAgIDwvYXBwbGV0LWRlc2M+DQogICAgIDx1cG"
//                + "RhdGUgY2hlY2s9ImJhY2tncm91bmQiLz4NCjwvam5scD4=");
//</editor-fold>
        
    }
    
    private void init() throws DAOException{
        String parametroBiometria = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("usaBiometria");
        if(RepositoryComponentDefault.SIM.equals(parametroBiometria)){
            usaBiometria = true;
        }else{
            return;
        }
        
        
        configuracoes();
        
        setOutputMarkupId(true);
        
        behavior = new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                for (Map.Entry<Object, IAppletAction> entry : appletListener.entrySet()) {
                    String methodType = RequestCycle.get().getRequest().getRequestParameters().getParameterValue("methodType").toString();
                    if("register".equals(methodType)){
                        entry.getValue().register(target, RequestCycle.get().getRequest().getRequestParameters().getParameterValue("key").toString());
                    }else if("search".equals(methodType)){
                        entry.getValue().search(target, RequestCycle.get().getRequest().getRequestParameters().getParameterValue("key").toString());
                    }
                }
            }

            @Override
            public void renderHead(Component component, IHeaderResponse response) {
                super.renderHead(component, response);
                String script = "var param1Value = appletParameter;";
                script += getCallbackScript();
                
                response.render(JavaScriptHeaderItem.forScript("function appletSearchListener(appletParameter){"
                        + "var param2Value = 'search';"
                            + script
                        + "}", "appletSearch"));
                
                response.render(JavaScriptHeaderItem.forScript("function appletRegisterListener(appletParameter){"
                        + "var param2Value = 'register';"
                            + script
                        + "}", "appletRegister"));
                
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getExtraParameters().put("key", "PLACEHOLDER1");
                attributes.getExtraParameters().put("methodType", "PLACEHOLDER2");
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
            
            @Override
            public CharSequence getCallbackScript() {
              String script = super.getCallbackScript().toString();
              script = script.replace("\"PLACEHOLDER1\"", "param1Value");
              script = script.replace("\"PLACEHOLDER2\"", "param2Value");
              return script;
            }            
            
        };
        add(behavior);        
    }

    public void addActionListener(Object key, IAppletAction action){
        appletListener.put(key, action);
    }
    
    /**
     * Add a parameter to the applet.
     *
     * @param key Name of the parameter.
     * @param value Value for the parameter.
     */
    public void addParameter(final String key, final Object value) {
        appletParameters.put(key, value);
    }

    /**
     * Get the applet attributes already set and assign them to the attribute
     * list for the Javascript code. And we change the tag name to "script".
     *
     * @param tag De current tag which is replaced.
     */
    @Override
    protected void onComponentTag(ComponentTag tag) {
        super.onComponentTag(tag);
        if(usaBiometria){
            if ("applet".equalsIgnoreCase(tag.getName())) {
                final IValueMap tagAttributes = tag.getAttributes();
                final String wicketId = tagAttributes.getString("wicket:id");
                appletAttributes.putAll(tagAttributes);
                tagAttributes.clear();
                tagAttributes.put("wicket:id", wicketId);
            }
            tag.setName("script");
        }
    }

    /**
     * Create Javascript for deployJava.runApplet.
     *
     * @param markupStream MarkupStream to be replaced.
     * @param openTag Tag we are replacing.
     */
    @Override
    public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
        if(usaBiometria){
            final StringBuilder script = new StringBuilder();
            if (appletAttributes.size() > 0) {
    //            final JSONObject jsonAttributes = JSONObject.fromObject(appletAttributes);
                String jsonAttributes = new Gson().toJson(appletAttributes);
                script.append("var attributes = ").append(jsonAttributes).append(";");
            } else {
                script.append("var attributes = {};");
            }
            if (appletParameters.size() > 0) {
    //            final JSONObject jsonParameters = JsonObject.fromObject(appletParameters);
                String jsonParameters = new Gson().toJson(appletParameters);
                script.append("var parameters = ").append(jsonParameters).append(";");
            } else {
                script.append("var parameters = {};");
            }
            if (minimalVersion != null) {
                script.append("var version = \"").append(minimalVersion).append("\";");
            } else {
                script.append("var version = null;");
            }
            script.append("if(deployJava.getJREs() == ''){alert('").append(Bundle.getStringApplication("msg_necessario_java_instalado_redirecionado_pagina")).append("')}");
            script.append("deployJava.runApplet(attributes, parameters, version);");
            replaceComponentTagBody(markupStream, openTag, script.toString());
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if(usaBiometria){
//            response.render(JavaScriptHeaderItem.forReference(Resources.JS_DEPLOY_JAVA));
        }
    }
    
    public void setMinimalVersion(final String version) {
        this.minimalVersion = version;
    }

    public void setWidth(final Integer width) {
        appletAttributes.put(ATTRIBUTE_WIDTH, width);
    }

    public void setHeight(final Integer height) {
        appletAttributes.put(ATTRIBUTE_HEIGHT, height);
    }

    public void setCode(final String code) {
        appletAttributes.put(ATTRIBUTE_CODE, code);
    }

    public void setCodebase(final String codebase) {
        appletAttributes.put(ATTRIBUTE_CODEBASE, codebase);
    }

    public void setId(final String id) {
        appletAttributes.put(ATTRIBUTE_ID, id);
    }

    public void setArchive(final String archive) {
        appletAttributes.put(ATTRIBUTE_ARCHIVE, archive);
    }

    
}
