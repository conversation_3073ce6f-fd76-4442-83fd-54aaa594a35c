package br.com.celk.component.recaptcha;

import br.com.celk.system.SystemHelper;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.IHeaderContributor;
import org.apache.wicket.markup.html.WebComponent;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 * WebComponent para Google reCAPTCHA v3 que funciona de forma invisível
 * 
 * reCAPTCHA v3 retorna uma pontuação (score) de 0.0 a 1.0:
 * - 1.0 = muito provavelmente humano
 * - 0.0 = muito provavelmente bot
 * - Threshold recomendado: 0.5
 * 
 * Uso simples:
 * ReCaptchaV3WebComponent recaptcha = new ReCaptchaV3WebComponent("recaptcha", "submit");
 * form.add(recaptcha);
 * 
 * Validação:
 * if (!recaptcha.isCompleted()) {
 *     error("Falha na verificação reCAPTCHA");
 * }
 */
public class ReCaptchaV3WebComponent extends WebComponent implements IHeaderContributor {

    private static final long serialVersionUID = 1L;

    private String siteKey;
    private boolean enabled = true;
    private String action = "submit"; // Ação para o reCAPTCHA v3
    private String hiddenFieldId;
    private String scoreFieldId;
    private double minScore = 0.5; // Score mínimo para considerar válido

    public ReCaptchaV3WebComponent(String id) {
        super(id);
        init();
    }

    public ReCaptchaV3WebComponent(String id, String action) {
        super(id);
        this.action = action;
        init();
    }

    public ReCaptchaV3WebComponent(String id, String action, double minScore) {
        super(id);
        this.action = action;
        this.minScore = minScore;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        setOutputMarkupPlaceholderTag(true);

        // Gerar IDs únicos baseados no ID do componente
        this.hiddenFieldId = "recaptcha_token_" + getId();
        this.scoreFieldId = "recaptcha_score_" + getId();

        System.out.println("DEBUG ReCaptchaV3 - Component initialized:");
        System.out.println("  Component ID: " + getId());
        System.out.println("  Hidden Field ID: " + hiddenFieldId);
        System.out.println("  Score Field ID: " + scoreFieldId);

        loadConfiguration();
    }

    private void loadConfiguration() {
        String googleReCaptchaKey = CargaBasicoPadrao.getInstance().getParametroPadrao().getGoogleReCaptchaKey();
        if (SystemHelper.isProducao()) {
            if (googleReCaptchaKey == null || googleReCaptchaKey.trim().isEmpty()) {
                setReCaptchaEnabled(false);
                return;
            }
            this.siteKey = googleReCaptchaKey;
        } else {
            this.siteKey = Coalesce.asString(googleReCaptchaKey, "6LczT2UrAAAAADMdo-TAGnSrVEtqD_bqI94uKzwL");
        }

//        // Verificar se o reCAPTCHA está habilitado globalmente
//        try {
//            String recaptchaEnabled = BOFactory.getBO(CommomFacade.class)
//                .modulo(Modulos.SISTEMA)
//                .getParametro("GoogleReCaptchaEnabled");
//            setReCaptchaEnabled("S".equalsIgnoreCase(recaptchaEnabled));
//        } catch (Exception e) {
//            System.out.println("AVISO: Erro ao verificar se reCAPTCHA está habilitado, assumindo habilitado: " + e.getMessage());
//            setReCaptchaEnabled(true);
//        }
    }

    @Override
    protected void onComponentTag(ComponentTag tag) {
        super.onComponentTag(tag);
        
        if (enabled) {
            // Transformar a tag em um span invisível (reCAPTCHA v3 é invisível)
            tag.setName("span");
            tag.put("class", "recaptcha-v3-component");
            tag.put("id", getMarkupId());
            tag.put("style", "display: none;");
        } else {
            // Se desabilitado, criar span vazio e invisível
            tag.setName("span");
            tag.put("style", "display: none;");
        }
    }

    @Override
    public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
        if (enabled) {
            // Gerar campos ocultos para armazenar token e score
            StringBuilder html = new StringBuilder();

            html.append("<!-- Campos ocultos para reCAPTCHA v3 -->");
            html.append("<input type=\"hidden\" id=\"").append(escapeHtml(hiddenFieldId))
                .append("\" name=\"").append(escapeHtml(hiddenFieldId)).append("\" />");
            html.append("<input type=\"hidden\" id=\"").append(escapeHtml(scoreFieldId))
                .append("\" name=\"").append(escapeHtml(scoreFieldId)).append("\" />");

            String htmlContent = html.toString();
            System.out.println("DEBUG ReCaptchaV3 - Generated HTML:");
            System.out.println(htmlContent);

            replaceComponentTagBody(markupStream, openTag, htmlContent);
        } else {
            // Se desabilitado, não renderizar nada
            System.out.println("DEBUG ReCaptchaV3 - Component disabled, no HTML generated");
            replaceComponentTagBody(markupStream, openTag, "");
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (!enabled) {
            return;
        }

        // Carregar script do Google reCAPTCHA v3
        response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js?render=" + siteKey));

        // Script para executar reCAPTCHA v3 quando necessário
        String uniqueId = getMarkupId();
        StringBuilder scriptBuilder = new StringBuilder();

        // Função para executar reCAPTCHA v3
        scriptBuilder.append("function executeRecaptchaV3_").append(escapeJavaScript(uniqueId)).append("() {");
        scriptBuilder.append("  console.log('=== STARTING reCAPTCHA v3 execution ===');");
        scriptBuilder.append("  console.log('Site key: ").append(escapeJavaScript(siteKey)).append("');");
        scriptBuilder.append("  console.log('Action: ").append(escapeJavaScript(action)).append("');");
        scriptBuilder.append("  console.log('Target field ID: ").append(escapeJavaScript(hiddenFieldId)).append("');");
        scriptBuilder.append("  ");
        scriptBuilder.append("  if (typeof grecaptcha === 'undefined') {");
        scriptBuilder.append("    console.error('ERROR: grecaptcha is undefined - Google reCAPTCHA API not loaded');");
        scriptBuilder.append("    return;");
        scriptBuilder.append("  }");
        scriptBuilder.append("  ");
        scriptBuilder.append("  if (!grecaptcha.ready) {");
        scriptBuilder.append("    console.error('ERROR: grecaptcha.ready is undefined');");
        scriptBuilder.append("    return;");
        scriptBuilder.append("  }");
        scriptBuilder.append("  ");
        scriptBuilder.append("  grecaptcha.ready(function() {");
        scriptBuilder.append("    console.log('grecaptcha.ready() called');");
        scriptBuilder.append("    ");
        scriptBuilder.append("    if (!grecaptcha.execute) {");
        scriptBuilder.append("      console.error('ERROR: grecaptcha.execute is undefined');");
        scriptBuilder.append("      return;");
        scriptBuilder.append("    }");
        scriptBuilder.append("    ");
        scriptBuilder.append("    console.log('Calling grecaptcha.execute...');");
        scriptBuilder.append("    grecaptcha.execute('").append(escapeJavaScript(siteKey)).append("', {");
        scriptBuilder.append("      action: '").append(escapeJavaScript(action)).append("'");
        scriptBuilder.append("    }).then(function(token) {");
        scriptBuilder.append("      console.log('=== TOKEN RECEIVED ===');");
        scriptBuilder.append("      console.log('Token: ' + token);");
        scriptBuilder.append("      console.log('Token length: ' + token.length);");
        scriptBuilder.append("      ");
        scriptBuilder.append("      if (!token || token.length === 0) {");
        scriptBuilder.append("        console.error('ERROR: Empty token received from Google');");
        scriptBuilder.append("        return;");
        scriptBuilder.append("      }");
        scriptBuilder.append("      ");
        scriptBuilder.append("      var tokenField = document.getElementById('").append(escapeJavaScript(hiddenFieldId)).append("');");
        scriptBuilder.append("      console.log('Looking for field: ").append(escapeJavaScript(hiddenFieldId)).append("');");
        scriptBuilder.append("      console.log('Field found: ' + (tokenField ? 'YES' : 'NO'));");
        scriptBuilder.append("      ");
        scriptBuilder.append("      if (tokenField) {");
        scriptBuilder.append("        console.log('Field before setting - value: \"' + tokenField.value + '\", length: ' + tokenField.value.length);");
        scriptBuilder.append("        tokenField.value = token;");
        scriptBuilder.append("        tokenField.setAttribute('value', token);");
        scriptBuilder.append("        console.log('Field after setting - value: \"' + tokenField.value + '\", length: ' + tokenField.value.length);");
        scriptBuilder.append("        ");
        scriptBuilder.append("        // Verificar se o valor foi realmente definido");
        scriptBuilder.append("        setTimeout(function() {");
        scriptBuilder.append("          var checkField = document.getElementById('").append(escapeJavaScript(hiddenFieldId)).append("');");
        scriptBuilder.append("          if (checkField) {");
        scriptBuilder.append("            console.log('Field verification - value: \"' + checkField.value + '\", length: ' + checkField.value.length);");
        scriptBuilder.append("            if (checkField.value !== token) {");
        scriptBuilder.append("              console.error('ERROR: Field value was changed or lost!');");
        scriptBuilder.append("              checkField.value = token; // Tentar definir novamente");
        scriptBuilder.append("            }");
        scriptBuilder.append("          }");
        scriptBuilder.append("        }, 100);");
        scriptBuilder.append("        ");
        scriptBuilder.append("        console.log('=== TOKEN SET SUCCESSFULLY ===');");
        scriptBuilder.append("      } else {");
        scriptBuilder.append("        console.error('=== ERROR: FIELD NOT FOUND ===');");
        scriptBuilder.append("        console.log('Available hidden fields:');");
        scriptBuilder.append("        var allInputs = document.querySelectorAll('input[type=\"hidden\"]');");
        scriptBuilder.append("        for (var i = 0; i < allInputs.length; i++) {");
        scriptBuilder.append("          console.log('  - ID: \"' + allInputs[i].id + '\", name: \"' + allInputs[i].name + '\", value: \"' + allInputs[i].value + '\"');");
        scriptBuilder.append("        }");
        scriptBuilder.append("      }");
        scriptBuilder.append("      ");
        scriptBuilder.append("      // Disparar evento customizado");
        scriptBuilder.append("      var event = new CustomEvent('recaptchaV3Completed', { ");
        scriptBuilder.append("        detail: { token: token, action: '").append(escapeJavaScript(action)).append("', componentId: '").append(escapeJavaScript(uniqueId)).append("' }");
        scriptBuilder.append("      });");
        scriptBuilder.append("      document.dispatchEvent(event);");
        scriptBuilder.append("      ");
        scriptBuilder.append("    }).catch(function(error) {");
        scriptBuilder.append("      console.error('=== reCAPTCHA v3 ERROR ===');");
        scriptBuilder.append("      console.error('Error:', error);");
        scriptBuilder.append("      var event = new CustomEvent('recaptchaV3Error', { ");
        scriptBuilder.append("        detail: { error: error, componentId: '").append(escapeJavaScript(uniqueId)).append("' }");
        scriptBuilder.append("      });");
        scriptBuilder.append("      document.dispatchEvent(event);");
        scriptBuilder.append("    });");
        scriptBuilder.append("  });");
        scriptBuilder.append("}");
        
        // Executar automaticamente quando a página carregar
        scriptBuilder.append("setTimeout(executeRecaptchaV3_").append(escapeJavaScript(uniqueId)).append(", 1000);");

        // Função global para re-executar quando necessário (ex: antes de submit)
        scriptBuilder.append("window.refreshRecaptchaV3_").append(escapeJavaScript(uniqueId)).append(" = executeRecaptchaV3_").append(escapeJavaScript(uniqueId)).append(";");

        // Executar novamente antes do submit do formulário
        scriptBuilder.append("document.addEventListener('DOMContentLoaded', function() {");
        scriptBuilder.append("  var forms = document.querySelectorAll('form');");
        scriptBuilder.append("  forms.forEach(function(form) {");
        scriptBuilder.append("    var recaptchaField = form.querySelector('#").append(escapeJavaScript(hiddenFieldId)).append("');");
        scriptBuilder.append("    if (recaptchaField) {");
        scriptBuilder.append("      console.log('Adding submit listener to form with reCAPTCHA v3');");
        scriptBuilder.append("      form.addEventListener('submit', function(e) {");
        scriptBuilder.append("        if (!recaptchaField.value) {");
        scriptBuilder.append("          console.log('No reCAPTCHA token, executing before submit...');");
        scriptBuilder.append("          e.preventDefault();");
        scriptBuilder.append("          executeRecaptchaV3_").append(escapeJavaScript(uniqueId)).append("();");
        scriptBuilder.append("          setTimeout(function() { form.submit(); }, 1000);");
        scriptBuilder.append("        }");
        scriptBuilder.append("      });");
        scriptBuilder.append("    }");
        scriptBuilder.append("  });");
        scriptBuilder.append("});");

        response.render(OnLoadHeaderItem.forScript(scriptBuilder.toString()));
    }

    /**
     * Verifica se o reCAPTCHA v3 foi completado (tem token)
     */
    public boolean isCompleted() {
        if (!enabled) {
            System.out.println("DEBUG ReCaptchaV3 - Component disabled, returning true");
            return true; // Se desabilitado, considera como completado
        }

        try {
            System.out.println("DEBUG ReCaptchaV3 - Looking for parameter: " + hiddenFieldId);

            // Listar todos os parâmetros para debug
            RequestCycle.get().getRequest().getRequestParameters().getParameterNames().forEach(name -> {
                if (name.toString().contains("recaptcha")) {
                    try {
                        String value = RequestCycle.get().getRequest().getRequestParameters().getParameterValue(name.toString()).toString();
                        System.out.println("DEBUG ReCaptchaV3 - Found parameter: " + name + " = " + (value != null && !value.trim().isEmpty() ? value.substring(0, Math.min(20, value.length())) + "..." : "EMPTY_OR_NULL"));
                        System.out.println("DEBUG ReCaptchaV3 - Parameter length: " + (value != null ? value.length() : "null"));
                        System.out.println("DEBUG ReCaptchaV3 - Parameter trimmed length: " + (value != null ? value.trim().length() : "null"));
                    } catch (Exception e) {
                        System.out.println("DEBUG ReCaptchaV3 - Error reading parameter " + name + ": " + e.getMessage());
                    }
                }
            });

            String token = null;
            try {
                token = RequestCycle.get().getRequest()
                    .getRequestParameters()
                    .getParameterValue(hiddenFieldId)
                    .toString();
            } catch (Exception e) {
                System.out.println("DEBUG ReCaptchaV3 - Error getting token: " + e.getMessage());
            }

            boolean completed = token != null && !token.trim().isEmpty();
            System.out.println("DEBUG ReCaptchaV3 - Token for " + hiddenFieldId + ":");
            System.out.println("  Raw token: " + (token != null ? "'" + token + "'" : "null"));
            System.out.println("  Token length: " + (token != null ? token.length() : "null"));
            System.out.println("  Token trimmed: " + (token != null ? "'" + token.trim() + "'" : "null"));
            System.out.println("  Token trimmed length: " + (token != null ? token.trim().length() : "null"));
            System.out.println("  Completed: " + completed);
            return completed;
        } catch (Exception e) {
            System.out.println("DEBUG ReCaptchaV3 - Erro ao verificar token: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Obtém o token do reCAPTCHA v3
     */
    public String getRecaptchaToken() {
        if (!enabled) {
            System.out.println("DEBUG ReCaptchaV3 - getRecaptchaToken: Component disabled");
            return "disabled";
        }

        try {
            String token = RequestCycle.get().getRequest()
                .getRequestParameters()
                .getParameterValue(hiddenFieldId)
                .toString();
            System.out.println("DEBUG ReCaptchaV3 - getRecaptchaToken: " + (token != null && !token.trim().isEmpty() ? token.substring(0, Math.min(20, token.length())) + "..." : "EMPTY_OR_NULL"));
            return token;
        } catch (Exception e) {
            System.out.println("DEBUG ReCaptchaV3 - getRecaptchaToken error: " + e.getMessage());
            return null;
        }
    }

    /**
     * Obtém o score do reCAPTCHA v3 (se disponível)
     */
    public Double getRecaptchaScore() {
        if (!enabled) {
            return 1.0; // Se desabilitado, retorna score máximo
        }

        try {
            String scoreStr = RequestCycle.get().getRequest()
                .getRequestParameters()
                .getParameterValue(scoreFieldId)
                .toString();
            return scoreStr != null ? Double.parseDouble(scoreStr) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Valida o reCAPTCHA v3 (lança exceção se inválido)
     */
    public void validate() {
        if (!enabled) {
            return;
        }
        
        if (!isCompleted()) {
            throw new RuntimeException("Falha na verificação reCAPTCHA v3 - token não encontrado");
        }
        
        // Validação adicional do score pode ser feita aqui se necessário
        // Nota: A validação real do score deve ser feita no backend com a secret key
    }

    /**
     * Escapa strings para uso seguro em HTML
     */
    private String escapeHtml(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }

    /**
     * Escapa strings para uso seguro em JavaScript
     */
    private String escapeJavaScript(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                   .replace("'", "\\'")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }

    // Getters e Setters
    @Override
    public boolean isEnabled() {
        return enabled && super.isEnabled();
    }

    public void setReCaptchaEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getSiteKey() {
        return siteKey;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public double getMinScore() {
        return minScore;
    }

    public void setMinScore(double minScore) {
        this.minScore = minScore;
    }

    public String getHiddenFieldId() {
        return hiddenFieldId;
    }

    public String getScoreFieldId() {
        return scoreFieldId;
    }
}
