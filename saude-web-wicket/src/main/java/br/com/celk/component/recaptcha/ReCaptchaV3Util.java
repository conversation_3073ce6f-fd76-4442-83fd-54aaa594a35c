package br.com.celk.component.recaptcha;

import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Utilitário para validação do Google reCAPTCHA v3 no backend
 */
public class ReCaptchaV3Util {

    private static final String RECAPTCHA_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";

    // Cache de validações com expiração automática
    private static final Map<String, ValidationCache> validationCache = new ConcurrentHashMap<>();
    private static final long CACHE_DURATION_MS = 60000; // 60 segundos (conservador para evitar expiração)

    /**
     * Classe para cache de validação com timestamp
     */
    private static class ValidationCache {
        private final ValidationResult result;
        private final long timestamp;

        public ValidationCache(ValidationResult result) {
            this.result = result;
            this.timestamp = System.currentTimeMillis();
        }

        public ValidationResult getResult() {
            return result;
        }

        public boolean isExpired() {
            return (System.currentTimeMillis() - timestamp) > CACHE_DURATION_MS;
        }

        public long getRemainingTimeSeconds() {
            long elapsed = System.currentTimeMillis() - timestamp;
            return Math.max(0, (CACHE_DURATION_MS - elapsed) / 1000);
        }

        public long getElapsedTimeSeconds() {
            return (System.currentTimeMillis() - timestamp) / 1000;
        }
    }

    /**
     * Resultado da validação do reCAPTCHA v3
     */
    public static class ValidationResult {
        private boolean success;
        private double score;
        private String action;
        private String hostname;
        private String challengeTs;
        private String[] errorCodes;

        // Getters e setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getChallengeTs() {
            return challengeTs;
        }

        public void setChallengeTs(String challengeTs) {
            this.challengeTs = challengeTs;
        }

        public String[] getErrorCodes() {
            return errorCodes;
        }

        public void setErrorCodes(String[] errorCodes) {
            this.errorCodes = errorCodes;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{success=%s, score=%.2f, action='%s', hostname='%s'}",
                    success, score, action, hostname);
        }
    }

    /**
     * Valida um token do reCAPTCHA v3 com cache inteligente
     *
     * @param token          Token retornado pelo reCAPTCHA v3
     * @param expectedAction Ação esperada (opcional)
     * @param minScore       Score mínimo para considerar válido
     * @return Resultado da validação
     */
    public static ValidationResult validateTokenWithCache(String token, String expectedAction, double minScore) {
        if (token == null || token.trim().isEmpty()) {
            Loggable.log.warn("Token reCAPTCHA v3 vazio ou nulo");
            ValidationResult result = new ValidationResult();
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"missing-input-response"});
            return result;
        }

        // Gerar chave única para o cache baseada no token e parâmetros
        String cacheKey = generateCacheKey(token, expectedAction, minScore);

        // Verificar cache primeiro
        ValidationCache cached = validationCache.get(cacheKey);
        if (cached != null) {
            if (!cached.isExpired()) {
                System.out.println("DEBUG ReCaptchaV3Util - Cache hit: Token válido por mais " +
                    cached.getRemainingTimeSeconds() + " segundos");
                return cached.getResult();
            } else {
                System.out.println("DEBUG ReCaptchaV3Util - Cache expirado após " +
                    cached.getElapsedTimeSeconds() + " segundos, removendo e revalidando...");
                validationCache.remove(cacheKey);
            }
        } else {
            System.out.println("DEBUG ReCaptchaV3Util - Cache miss: Executando validação pela primeira vez");
        }

        // Executar validação real
        ValidationResult result = validateTokenDirect(token, expectedAction, minScore);

        // Cachear apenas resultados de sucesso
        if (result.isSuccess()) {
            validationCache.put(cacheKey, new ValidationCache(result));
            System.out.println("DEBUG ReCaptchaV3Util - Resultado cacheado por " + (CACHE_DURATION_MS / 1000) + " segundos");
        }

        // Limpeza periódica do cache
        cleanExpiredCache();

        return result;
    }

    /**
     * Valida um token do reCAPTCHA v3 diretamente com o Google (sem cache)
     *
     * @param token          Token retornado pelo reCAPTCHA v3
     * @param expectedAction Ação esperada (opcional)
     * @param minScore       Score mínimo para considerar válido
     * @return Resultado da validação
     */
    public static ValidationResult validateTokenDirect(String token, String expectedAction, double minScore) {
        ValidationResult result = new ValidationResult();

        if (token == null || token.trim().isEmpty()) {
            Loggable.log.warn("Token reCAPTCHA v3 vazio ou nulo");
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"missing-input-response"});
            return result;
        }

        String secretKey = getSecretKey();
        if (secretKey == null) {
            Loggable.log.error("Chave secreta do reCAPTCHA v3 não configurada");
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"missing-secret-key"});
            return result;
        }

        try {
            // Fazer requisição para o Google
            String response = makeVerificationRequest(secretKey, token);

            // Debug: Mostrar resposta do Google
            System.out.println("DEBUG ReCaptchaV3Util - Google response: " + response);
            System.out.println("DEBUG ReCaptchaV3Util - Secret key used: " + secretKey.substring(0, Math.min(10, secretKey.length())) + "...");
            System.out.println("DEBUG ReCaptchaV3Util - Token used: " + token.substring(0, Math.min(20, token.length())) + "...");

            // Parse da resposta JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonResponse = mapper.readTree(response);

            result.setSuccess(jsonResponse.get("success").asBoolean());
            result.setScore(jsonResponse.has("score") ? jsonResponse.get("score").asDouble() : 0.0);
            result.setAction(jsonResponse.has("action") ? jsonResponse.get("action").asText() : null);
            result.setHostname(jsonResponse.has("hostname") ? jsonResponse.get("hostname").asText() : null);
            result.setChallengeTs(jsonResponse.has("challenge_ts") ? jsonResponse.get("challenge_ts").asText() : null);

            // Parse dos códigos de erro se houver
            if (jsonResponse.has("error-codes")) {
                JsonNode errorCodes = jsonResponse.get("error-codes");
                String[] errors = new String[errorCodes.size()];
                for (int i = 0; i < errorCodes.size(); i++) {
                    errors[i] = errorCodes.get(i).asText();
                }
                result.setErrorCodes(errors);

                // Debug: Mostrar códigos de erro
                System.out.println("DEBUG ReCaptchaV3Util - Error codes from Google:");
                for (String error : errors) {
                    System.out.println("  - " + error);
                }
            }

            // Validações adicionais
            if (result.isSuccess()) {
                // Verificar ação se especificada
                if (expectedAction != null && !expectedAction.equals(result.getAction())) {
                    Loggable.log.warn(String.format("Ação reCAPTCHA v3 não confere. Esperado: %s, Recebido: %s",
                            expectedAction, result.getAction()));
                    result.setSuccess(false);
                    result.setErrorCodes(new String[]{"action-mismatch"});
                    return result;
                }

                // Verificar score mínimo
                if (result.getScore() < minScore) {
                    Loggable.log.warn(String.format("Score reCAPTCHA v3 muito baixo: %.2f (mínimo: %.2f)",
                            result.getScore(), minScore));
                    result.setSuccess(false);
                    result.setErrorCodes(new String[]{"score-too-low"});
                    return result;
                }
            }

            // Debug: Mostrar resultado final
            System.out.println("DEBUG ReCaptchaV3Util - Final result:");
            System.out.println("  Success: " + result.isSuccess());
            System.out.println("  Score: " + result.getScore());
            System.out.println("  Action: " + result.getAction());
            System.out.println("  Hostname: " + result.getHostname());
            System.out.println("  Expected action: " + expectedAction);
            System.out.println("  Min score: " + minScore);

            Loggable.log.info(String.format("Validação reCAPTCHA v3: %s", result.toString()));
            return result;

        } catch (Exception e) {
            Loggable.log.error("Erro ao validar reCAPTCHA v3", e);
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"verification-failed"});
            return result;
        }
    }

    /**
     * Valida um token do reCAPTCHA v3 com o Google (usa cache inteligente)
     *
     * @param token          Token retornado pelo reCAPTCHA v3
     * @param expectedAction Ação esperada (opcional)
     * @param minScore       Score mínimo para considerar válido
     * @return Resultado da validação
     */
    public static ValidationResult validateToken(String token, String expectedAction, double minScore) {
        return validateTokenWithCache(token, expectedAction, minScore);
    }

    /**
     * Valida um token do reCAPTCHA v3 com configurações padrão
     *
     * @param token Token retornado pelo reCAPTCHA v3
     * @return true se válido, false caso contrário
     */
    public static boolean isValid(String token) {
        return validateToken(token, null, 0.5).isSuccess();
    }

    /**
     * Valida um token do reCAPTCHA v3 com score mínimo
     *
     * @param token    Token retornado pelo reCAPTCHA v3
     * @param minScore Score mínimo para considerar válido
     * @return true se válido, false caso contrário
     */
    public static boolean isValid(String token, double minScore) {
        return validateToken(token, null, minScore).isSuccess();
    }

    /**
     * Valida um token do reCAPTCHA v3 com ação e score
     *
     * @param token          Token retornado pelo reCAPTCHA v3
     * @param expectedAction Ação esperada
     * @param minScore       Score mínimo para considerar válido
     * @return true se válido, false caso contrário
     */
    public static boolean isValid(String token, String expectedAction, double minScore) {
        return validateToken(token, expectedAction, minScore).isSuccess();
    }

    /**
     * Faz a requisição de verificação para o Google
     */
    private static String makeVerificationRequest(String secretKey, String token) throws Exception {
        URL url = new URL(RECAPTCHA_VERIFY_URL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // Configurar conexão
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("User-Agent", "Java-reCAPTCHA-v3");
        connection.setDoOutput(true);
        connection.setConnectTimeout(10000); // 10 segundos
        connection.setReadTimeout(10000);    // 10 segundos

        // Preparar dados do POST
        String postData = "secret=" + URLEncoder.encode(secretKey, StandardCharsets.UTF_8.toString()) +
                "&response=" + URLEncoder.encode(token, StandardCharsets.UTF_8.toString());

        // Enviar dados
        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
            wr.writeBytes(postData);
            wr.flush();
        }

        // Ler resposta
        StringBuilder response = new StringBuilder();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
        }

        return response.toString();
    }

    /**
     * Obtém a chave secreta do reCAPTCHA dos parâmetros do sistema
     */
    private static String getSecretKey() {
        try {
            // Tentar carregar chave secreta específica primeiro
            String secretKey = "6LczT2UrAAAAAHrPf4xoP4appjRChsCu4UcKbNG3";

            if (secretKey != null && !secretKey.trim().isEmpty()) {
                System.out.println("DEBUG ReCaptchaV3Util - Using secret key from GoogleReCaptchaSecretKey parameter");
                return secretKey;
            }

            // Se não houver chave secreta específica, tentar usar a mesma chave (para desenvolvimento)
            String googleReCaptchaKey = CargaBasicoPadrao.getInstance().getParametroPadrao().getGoogleReCaptchaKey();
            if (googleReCaptchaKey != null && !googleReCaptchaKey.trim().isEmpty()) {
                System.out.println("DEBUG ReCaptchaV3Util - Using site key as secret key (development mode)");
                return googleReCaptchaKey;
            }

        } catch (Exception e) {
            System.out.println("DEBUG ReCaptchaV3Util - Error loading secret key: " + e.getMessage());
        }

        // Fallback para chave de teste
        System.out.println("DEBUG ReCaptchaV3Util - Using test secret key");
        return "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste do Google
    }

    /**
     * Valida um componente reCAPTCHA v3 diretamente (usa cache inteligente)
     *
     * @param component Componente reCAPTCHA v3
     * @return Resultado da validação
     */
    public static ValidationResult validateComponent(ReCaptchaV3WebComponent component) {
        if (component == null || !component.isEnabled()) {
            ValidationResult result = new ValidationResult();
            result.setSuccess(true); // Se desabilitado, considera válido
            result.setScore(1.0);
            System.out.println("DEBUG ReCaptchaV3Util - Componente desabilitado, considerando válido");
            return result;
        }

        String token = component.getRecaptchaToken();
        String action = component.getAction();
        double minScore = component.getMinScore();

        System.out.println("DEBUG ReCaptchaV3Util - Validando componente: action=" + action + ", minScore=" + minScore);
        return validateTokenWithCache(token, action, minScore);
    }

    /**
     * Gera chave única para cache baseada no token e parâmetros
     */
    private static String generateCacheKey(String token, String expectedAction, double minScore) {
        // Usar hash do token para evitar armazenar token completo
        int tokenHash = token != null ? token.hashCode() : 0;
        return String.format("token_%d_action_%s_score_%.2f", tokenHash, expectedAction, minScore);
    }

    /**
     * Remove entradas expiradas do cache
     */
    private static void cleanExpiredCache() {
        validationCache.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isExpired();
            if (expired) {
                System.out.println("DEBUG ReCaptchaV3Util - Removendo entrada expirada do cache: " + entry.getKey());
            }
            return expired;
        });
    }

    /**
     * Limpa todo o cache (útil para testes)
     */
    public static void clearCache() {
        validationCache.clear();
        System.out.println("DEBUG ReCaptchaV3Util - Cache limpo completamente");
    }

    /**
     * Obtém estatísticas do cache
     */
    public static String getCacheStats() {
        int totalEntries = validationCache.size();
        long expiredEntries = validationCache.values().stream()
            .mapToLong(cache -> cache.isExpired() ? 1 : 0)
            .sum();

        return String.format("Cache stats: %d total, %d expired, %d active",
            totalEntries, expiredEntries, totalEntries - expiredEntries);
    }

    /**
     * Constantes para códigos de erro comuns
     */
    public static class ErrorCodes {
        public static final String MISSING_INPUT_SECRET = "missing-input-secret";
        public static final String INVALID_INPUT_SECRET = "invalid-input-secret";
        public static final String MISSING_INPUT_RESPONSE = "missing-input-response";
        public static final String INVALID_INPUT_RESPONSE = "invalid-input-response";
        public static final String BAD_REQUEST = "bad-request";
        public static final String TIMEOUT_OR_DUPLICATE = "timeout-or-duplicate";
        public static final String ACTION_MISMATCH = "action-mismatch";
        public static final String SCORE_TOO_LOW = "score-too-low";
        public static final String VERIFICATION_FAILED = "verification-failed";
        public static final String MISSING_SECRET_KEY = "missing-secret-key";
    }
}
