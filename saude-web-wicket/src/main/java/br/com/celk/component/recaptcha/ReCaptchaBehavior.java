package br.com.celk.component.recaptcha;

import org.apache.wicket.Component;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 * Behavior para adicionar Google reCAPTCHA a qualquer componente Wicket
 */
public class ReCaptchaBehavior extends Behavior {

    private String siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI";
    private boolean enabled = true;
    private String containerId = "recaptcha-container";
    private String statusId = "recaptcha-status";
    private String hiddenFieldId;

    public ReCaptchaBehavior() {
        loadConfiguration();
    }

    public ReCaptchaBehavior(String containerId) {
        this.containerId = containerId;
        loadConfiguration();
    }

    public ReCaptchaBehavior(String containerId, String hiddenFieldId) {
        this.containerId = containerId;
        this.hiddenFieldId = hiddenFieldId;
        loadConfiguration();
    }

    private void loadConfiguration() {
//        try {
//            // Tentar carregar a chave do sistema de parâmetros
//            this.siteKey = BOFactory.getBO(CommomFacade.class)
//                .modulo(Modulos.SISTEMA)
//                .getParametro("GoogleReCaptchaSiteKey");
//
//            // Verificar se o reCAPTCHA está habilitado
//            String recaptchaEnabled = BOFactory.getBO(CommomFacade.class)
//                .modulo(Modulos.SISTEMA)
//                .getParametro("GoogleReCaptchaEnabled");
//            this.enabled = "S".equalsIgnoreCase(recaptchaEnabled);
//
//        } catch (DAOException e) {
//            // Se não conseguir carregar dos parâmetros, usar valores padrão
//            this.siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste
//            this.enabled = true;
//            System.out.println("AVISO: Usando chave de teste do reCAPTCHA. Configure os parâmetros GoogleReCaptchaSiteKey e GoogleReCaptchaEnabled");
//        }
//
//        // Validar se a chave foi carregada
//        if (this.siteKey == null || this.siteKey.trim().isEmpty()) {
//            this.siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Fallback para chave de teste
//            System.out.println("AVISO: Site key do reCAPTCHA não configurada, usando chave de teste");
//        }
    }

    @Override
    public void renderHead(Component component, IHeaderResponse response) {
        super.renderHead(component, response);

        if (!enabled) {
            return;
        }

        // Carregar script do Google reCAPTCHA
        response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

        // Determinar o ID do campo oculto
        String targetHiddenFieldId = hiddenFieldId != null ? hiddenFieldId : component.getMarkupId() + "_recaptcha_response";
        
        // Script para verificar e renderizar quando a API estiver pronta
        // Usar ID único para evitar conflitos com outros componentes reCAPTCHA
        String uniqueId = component.getMarkupId();

        // Construir o script JavaScript de forma mais segura com escape
        StringBuilder scriptBuilder = new StringBuilder();
        scriptBuilder.append("function checkAndRenderRecaptcha_").append(escapeJavaScript(uniqueId)).append("() {");
        scriptBuilder.append("  if (typeof grecaptcha !== 'undefined' && grecaptcha.render) {");
        scriptBuilder.append("    console.log('Google reCAPTCHA API ready for component ").append(escapeJavaScript(uniqueId)).append("');");
        scriptBuilder.append("    var recaptchaElement = document.getElementById('").append(escapeJavaScript(containerId)).append("');");
        scriptBuilder.append("    var statusElement = document.getElementById('").append(escapeJavaScript(statusId)).append("');");
        scriptBuilder.append("    var hiddenField = document.getElementById('").append(escapeJavaScript(targetHiddenFieldId)).append("');");
        scriptBuilder.append("    console.log('Hidden field found: ' + (hiddenField ? 'YES' : 'NO') + ', ID: ").append(escapeJavaScript(targetHiddenFieldId)).append("');");
        scriptBuilder.append("    if (recaptchaElement && !recaptchaElement.hasChildNodes()) {");
        scriptBuilder.append("      try {");
        scriptBuilder.append("        var widgetId = grecaptcha.render('").append(escapeJavaScript(containerId)).append("', {");
        scriptBuilder.append("          sitekey: '").append(escapeJavaScript(siteKey)).append("',");
        scriptBuilder.append("          callback: function(token) {");
        scriptBuilder.append("            var hiddenField = document.getElementById('").append(escapeJavaScript(targetHiddenFieldId)).append("');");
        scriptBuilder.append("            if (hiddenField) {");
        scriptBuilder.append("              hiddenField.value = token;");
        scriptBuilder.append("              hiddenField.setAttribute('value', token);");
        scriptBuilder.append("              console.log('reCAPTCHA completed - Token set: ' + token.substring(0, 20) + '...');");
        scriptBuilder.append("            } else {");
        scriptBuilder.append("              console.error('Hidden field not found: ").append(escapeJavaScript(targetHiddenFieldId)).append("');");
        scriptBuilder.append("            }");
        scriptBuilder.append("            if (statusElement) statusElement.innerHTML = 'Verificação concluída com sucesso!';");
        scriptBuilder.append("            var event = new CustomEvent('recaptchaCompleted', { detail: { token: token, componentId: '").append(escapeJavaScript(uniqueId)).append("' } });");
        scriptBuilder.append("            document.dispatchEvent(event);");
        scriptBuilder.append("          },");
        scriptBuilder.append("          'expired-callback': function() {");
        scriptBuilder.append("            var hiddenField = document.getElementById('").append(escapeJavaScript(targetHiddenFieldId)).append("');");
        scriptBuilder.append("            if (hiddenField) {");
        scriptBuilder.append("              hiddenField.value = '';");
        scriptBuilder.append("              hiddenField.setAttribute('value', '');");
        scriptBuilder.append("              console.log('reCAPTCHA expired - Token cleared');");
        scriptBuilder.append("            }");
        scriptBuilder.append("            if (statusElement) statusElement.innerHTML = 'Verificação expirada. Complete novamente.';");
        scriptBuilder.append("            var event = new CustomEvent('recaptchaExpired', { detail: { componentId: '").append(escapeJavaScript(uniqueId)).append("' } });");
        scriptBuilder.append("            document.dispatchEvent(event);");
        scriptBuilder.append("          }");
        scriptBuilder.append("        });");
        scriptBuilder.append("        console.log('reCAPTCHA widget rendered with ID: ' + widgetId);");
        scriptBuilder.append("        if (statusElement) statusElement.innerHTML = 'Complete a verificação acima para continuar';");
        scriptBuilder.append("      } catch (e) {");
        scriptBuilder.append("        console.error('Erro ao renderizar reCAPTCHA: ', e);");
        scriptBuilder.append("        if (statusElement) statusElement.innerHTML = 'Erro ao carregar verificação';");
        scriptBuilder.append("      }");
        scriptBuilder.append("    }");
        scriptBuilder.append("  } else {");
        scriptBuilder.append("    console.log('Google reCAPTCHA API not ready yet, retrying...');");
        scriptBuilder.append("    setTimeout(checkAndRenderRecaptcha_").append(escapeJavaScript(uniqueId)).append(", 500);");
        scriptBuilder.append("  }");
        scriptBuilder.append("}");
        scriptBuilder.append("setTimeout(checkAndRenderRecaptcha_").append(escapeJavaScript(uniqueId)).append(", 1000);");

        String initScript = scriptBuilder.toString();

        // Debug: Log do script gerado (remover em produção)
        System.out.println("DEBUG ReCaptchaBehavior - Script gerado para componente " + uniqueId + ":");
        System.out.println(initScript);

        response.render(OnLoadHeaderItem.forScript(initScript));
    }

    /**
     * Verifica se o reCAPTCHA foi completado baseado no request atual
     */
    public boolean isCompleted() {
        if (!enabled) {
            return true; // Se desabilitado, considera como completado
        }

        try {
            String targetHiddenFieldId = hiddenFieldId != null ? hiddenFieldId : "recaptcha_response";
            String response = RequestCycle.get().getRequest()
                .getRequestParameters()
                .getParameterValue(targetHiddenFieldId)
                .toString();
            
            boolean completed = response != null && !response.trim().isEmpty();
            System.out.println("DEBUG reCAPTCHA Behavior - Response: " + response + ", Completed: " + completed);
            return completed;
        } catch (Exception e) {
            System.out.println("DEBUG reCAPTCHA Behavior - Erro ao verificar: " + e.getMessage());
            return false;
        }
    }

    /**
     * Obtém a resposta do reCAPTCHA do request atual
     */
    public String getRecaptchaResponse() {
        if (!enabled) {
            return "disabled";
        }

        try {
            String targetHiddenFieldId = hiddenFieldId != null ? hiddenFieldId : "recaptcha_response";
            return RequestCycle.get().getRequest()
                .getRequestParameters()
                .getParameterValue(targetHiddenFieldId)
                .toString();
        } catch (Exception e) {
            return null;
        }
    }

    // Getters e Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getSiteKey() {
        return siteKey;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public String getStatusId() {
        return statusId;
    }

    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }

    public String getHiddenFieldId() {
        return hiddenFieldId;
    }

    public void setHiddenFieldId(String hiddenFieldId) {
        this.hiddenFieldId = hiddenFieldId;
    }

    /**
     * Escapa strings para uso seguro em JavaScript
     */
    private String escapeJavaScript(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                   .replace("'", "\\'")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
}
