package br.com.celk.component.messaging;

import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MensagensNovaCache {
    private MensagensNovaCache() {
    }

    public static List<QueryConsultaMensagensDTO> get() {
        List<Mensagem> mensagens = ApplicationSession.get().getMensagensNovasCache();
        List<QueryConsultaMensagensDTO> dtoList = new ArrayList<>();
        if (mensagens.isEmpty()) {
            countMessage();
            mensagens = LoadManager.getInstance(Mensagem.class)
                    .addProperty(VOUtils.montarPath(Mensagem.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Mensagem.PROP_MENSAGEM_ORIGEM, Mensagem.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Mensagem.PROP_MENSAGEM_ORIGEM, Mensagem.PROP_MENSAGEM))
                    .addProperty(VOUtils.montarPath(Mensagem.PROP_MENSAGEM_ORIGEM, Mensagem.PROP_USUARIO, Usuario.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Mensagem.PROP_MENSAGEM_ORIGEM, Mensagem.PROP_USUARIO, Usuario.PROP_NOME))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_USUARIO, ApplicationSession.get().getSessaoAplicacao().getUsuario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_EXCLUIDA, RepositoryComponentDefault.SimNaoLong.NAO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_LIDA, RepositoryComponentDefault.SimNaoLong.NAO.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(Mensagem.PROP_DATA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .setMaxResults(10)
                    .start().getList();

            ApplicationSession.get().getMensagensNovasCache().addAll(mensagens);

            QueryConsultaMensagensDTO dto;
            for (Mensagem mensagem : mensagens) {
                dto = new QueryConsultaMensagensDTO();
                dto.setMensagem(mensagem);
                dtoList.add(dto);
            }
        } else {
            QueryConsultaMensagensDTO dto;
            for (Mensagem mensagem : mensagens) {
                dto = new QueryConsultaMensagensDTO();
                dto.setMensagem(mensagem);
                dtoList.add(dto);
            }

        }
        return dtoList;
    }

    public static Long countMessage() {
        Long quantidadeMensagensUsuario = ApplicationSession.get().getQuantidadeMensagensUsuario();
        if (quantidadeMensagensUsuario == 0) {
            Long quantidade = LoadManager.getInstance(Mensagem.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(Mensagem.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_USUARIO, ApplicationSession.get().getSessaoAplicacao().getUsuario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_EXCLUIDA, RepositoryComponentDefault.SimNaoLong.NAO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_LIDA, RepositoryComponentDefault.SimNaoLong.NAO.value()))
                    .start().getVO();
            ApplicationSession.get().setAttribute(ApplicationSession.ATTRIBUTE_QTD_MENSAGEM, quantidade);
        }
        return ApplicationSession.get().getQuantidadeMensagensUsuario();
    }
}
