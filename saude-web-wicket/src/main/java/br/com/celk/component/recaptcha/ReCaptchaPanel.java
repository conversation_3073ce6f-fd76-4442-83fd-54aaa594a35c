package br.com.celk.component.recaptcha;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.markup.html.panel.Panel;

/**
 * Componente reCAPTCHA funcional
 */
public class ReCaptchaPanel extends Panel {

    private HiddenField<String> recaptchaResponse;
    private String siteKey;
    private boolean enabled = true;

    public ReCaptchaPanel(String id) {
        super(id);
        loadConfiguration();
        init();
    }

    private void loadConfiguration() {
        try {
            // Tentar carregar a chave do sistema de parâmetros
            this.siteKey = BOFactory.getBO(CommomFacade.class)
                .modulo(Modulos.SISTEMA)
                .getParametro("GoogleReCaptchaSiteKey");

            // Verificar se o reCAPTCHA está habilitado
            String recaptchaEnabled = BOFactory.getBO(CommomFacade.class)
                .modulo(Modulos.SISTEMA)
                .getParametro("GoogleReCaptchaEnabled");
            this.enabled = "S".equalsIgnoreCase(recaptchaEnabled);

        } catch (DAOException e) {
            // Se não conseguir carregar dos parâmetros, usar valores padrão
            this.siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste
            this.enabled = true;
            System.out.println("AVISO: Usando chave de teste do reCAPTCHA. Configure os parâmetros GoogleReCaptchaSiteKey e GoogleReCaptchaEnabled");
        }

        // Validar se a chave foi carregada
        if (this.siteKey == null || this.siteKey.trim().isEmpty()) {
            this.siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Fallback para chave de teste
            System.out.println("AVISO: Site key do reCAPTCHA não configurada, usando chave de teste");
        }
    }

    private void init() {
        setOutputMarkupId(true);

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField<>("recaptchaResponse");
        recaptchaResponse.setOutputMarkupId(true);
        recaptchaResponse.setOutputMarkupPlaceholderTag(true);
        add(recaptchaResponse);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (enabled) {
            // Usar abordagem mais simples - carregar automaticamente sem callback personalizado
            response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

            // Script para verificar e renderizar quando a API estiver pronta
            // Usar ID único para evitar conflitos com outros componentes reCAPTCHA
            String uniqueId = getMarkupId();
            String initScript = String.format(
                    "function checkAndRenderRecaptcha_%s() { " +
                    "  if (typeof grecaptcha !== 'undefined' && grecaptcha.render) { " +
                    "    console.log('Google reCAPTCHA API ready for component %s'); " +
                    "    var recaptchaElement = document.getElementById('recaptcha-div'); " +
                    "    var statusElement = document.getElementById('recaptcha-status'); " +
                    "    var hiddenField = document.getElementById('%s'); " +
                    "    console.log('Hidden field found: ' + (hiddenField ? 'YES' : 'NO') + ', ID: %s'); " +
                    "    if (recaptchaElement && !recaptchaElement.hasChildNodes()) { " +
                    "      try { " +
                    "        var widgetId = grecaptcha.render('recaptcha-div', { " +
                    "          'sitekey': '%s', " +
                    "          'callback': function(token) { " +
                    "            var hiddenField = document.getElementById('%s'); " +
                    "            if (hiddenField) { " +
                    "              hiddenField.value = token; " +
                    "              hiddenField.setAttribute('value', token); " +
                    "              console.log('reCAPTCHA completed - Token set: ' + token.substring(0, 20) + '...'); " +
                    "            } else { " +
                    "              console.error('Hidden field not found: %s'); " +
                    "            } " +
                    "            if (statusElement) statusElement.innerHTML = 'Verificação concluída com sucesso!'; " +
                    "          }, " +
                    "          'expired-callback': function() { " +
                    "            var hiddenField = document.getElementById('%s'); " +
                    "            if (hiddenField) { " +
                    "              hiddenField.value = ''; " +
                    "              hiddenField.setAttribute('value', ''); " +
                    "              console.log('reCAPTCHA expired - Token cleared'); " +
                    "            } " +
                    "            if (statusElement) statusElement.innerHTML = 'Verificação expirada. Complete novamente.'; " +
                    "          } " +
                    "        }); " +
                    "        console.log('reCAPTCHA widget rendered with ID: ' + widgetId); " +
                    "        if (statusElement) statusElement.innerHTML = 'Complete a verificação acima para continuar'; " +
                    "      } catch (e) { " +
                    "        console.error('Erro ao renderizar reCAPTCHA: ', e); " +
                    "        if (statusElement) statusElement.innerHTML = 'Erro ao carregar verificação'; " +
                    "      } " +
                    "    } " +
                    "  } else { " +
                    "    console.log('Google reCAPTCHA API not ready yet, retrying...'); " +
                    "    setTimeout(checkAndRenderRecaptcha_%s, 500); " +
                    "  } " +
                    "} " +
                    "setTimeout(checkAndRenderRecaptcha_%s, 1000);",
                    uniqueId,
                    uniqueId,
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId(),
                    siteKey,
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId(),
                    uniqueId,
                    uniqueId
            );

            response.render(OnLoadHeaderItem.forScript(initScript));
        }
    }

    public String getRecaptchaResponse() {
        // Primeiro tenta pegar do modelo
        String modelValue = recaptchaResponse.getModelObject();
        if (modelValue != null && !modelValue.trim().isEmpty()) {
            return modelValue;
        }

        // Se não tiver no modelo, tenta pegar do request
        String requestValue = recaptchaResponse.getInput();
        if (requestValue != null && !requestValue.trim().isEmpty()) {
            return requestValue;
        }

        // Última tentativa: pegar diretamente do request HTTP
        try {
            String paramValue = getRequest().getRequestParameters().getParameterValue(recaptchaResponse.getInputName()).toString();
            if (paramValue != null && !paramValue.trim().isEmpty()) {
                return paramValue;
            }
        } catch (Exception e) {
            // Ignora erro silenciosamente
        }

        return null;
    }

    public boolean isCompleted() {
        // Força a atualização do modelo antes de verificar
        updateModel();

        String response = getRecaptchaResponse();
        boolean completed = response != null && !response.trim().isEmpty();
        System.out.println("DEBUG reCAPTCHA - Response: " + response + ", Completed: " + completed);
        return completed;
    }

    private void updateModel() {
        try {
            // Força a conversão do input para o modelo
            recaptchaResponse.processInput();
            recaptchaResponse.updateModel();
        } catch (Exception e) {
            // Ignora erros silenciosamente
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

//    public void setEnabled(boolean enabled) {
//        this.enabled = enabled;
//    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }
}
