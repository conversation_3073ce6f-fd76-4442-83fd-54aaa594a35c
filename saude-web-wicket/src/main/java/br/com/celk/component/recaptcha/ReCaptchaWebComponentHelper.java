package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.html.form.Form;

/**
 * Classe utilitária para facilitar o uso do ReCaptchaWebComponent
 */
public class ReCaptchaWebComponentHelper {

    /**
     * Adiciona um WebComponent reCAPTCHA a um formulário
     * 
     * @param form Formulário onde adicionar o componente
     * @param id ID do componente
     * @return O WebComponent criado
     */
    public static ReCaptchaWebComponent addToForm(Form<?> form, String id) {
        ReCaptchaWebComponent component = new ReCaptchaWebComponent(id);
        form.add(component);
        return component;
    }

    /**
     * Valida um WebComponent reCAPTCHA
     * 
     * @param component O WebComponent do reCAPTCHA
     * @param throwException Se deve lançar exceção em caso de falha
     * @return true se válido, false caso contrário
     * @throws RuntimeException Se throwException=true e validação falhar
     */
    public static boolean validate(ReCaptchaWebComponent component, boolean throwException) {
        if (component == null) {
            if (throwException) {
                throw new RuntimeException("WebComponent reCAPTCHA não encontrado");
            }
            return false;
        }

        if (!component.isEnabled()) {
            return true; // Se desabilitado, considera válido
        }

        boolean isValid = component.isCompleted();
        
        if (!isValid && throwException) {
            throw new RuntimeException("Por favor, complete a verificação reCAPTCHA");
        }
        
        return isValid;
    }

    /**
     * Valida um WebComponent reCAPTCHA (lança exceção se inválido)
     * 
     * @param component O WebComponent do reCAPTCHA
     * @return true se válido
     * @throws RuntimeException Se validação falhar
     */
    public static boolean validate(ReCaptchaWebComponent component) {
        return validate(component, true);
    }

    /**
     * Cria um WebComponent reCAPTCHA com configuração padrão
     * 
     * @param id ID do componente
     * @return WebComponent configurado
     */
    public static ReCaptchaWebComponent create(String id) {
        return new ReCaptchaWebComponent(id);
    }
}
