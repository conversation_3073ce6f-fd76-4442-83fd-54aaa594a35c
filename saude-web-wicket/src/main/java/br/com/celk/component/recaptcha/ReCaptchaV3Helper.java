package br.com.celk.component.recaptcha;

import org.apache.wicket.MarkupContainer;
import org.apache.wicket.markup.html.form.Form;

/**
 * Classe utilitária para facilitar o uso do ReCaptchaV3WebComponent
 */
public class ReCaptchaV3Helper {

    /**
     * Adiciona um WebComponent reCAPTCHA v3 a um formulário
     * 
     * @param form Formulário onde adicionar o componente
     * @param id ID do componente
     * @return O WebComponent criado
     */
    public static ReCaptchaV3WebComponent addToForm(Form<?> form, String id) {
        ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent(id);
        form.add(component);
        return component;
    }

    /**
     * Adiciona um WebComponent reCAPTCHA v3 a um formulário com ação específica
     * 
     * @param form Formulário onde adicionar o componente
     * @param id ID do componente
     * @param action Ação para o reCAPTCHA v3 (ex: "submit", "login", "register")
     * @return O WebComponent criado
     */
    public static ReCaptchaV3WebComponent addToForm(Form<?> form, String id, String action) {
        ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent(id, action);
        form.add(component);
        return component;
    }

    /**
     * Adiciona um WebComponent reCAPTCHA v3 a um formulário com ação e score mínimo
     * 
     * @param form Formulário onde adicionar o componente
     * @param id ID do componente
     * @param action Ação para o reCAPTCHA v3
     * @param minScore Score mínimo para considerar válido (0.0 a 1.0)
     * @return O WebComponent criado
     */
    public static ReCaptchaV3WebComponent addToForm(Form<?> form, String id, String action, double minScore) {
        ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent(id, action, minScore);
        form.add(component);
        return component;
    }

    /**
     * Adiciona um WebComponent reCAPTCHA v3 a qualquer container
     * 
     * @param container Container onde adicionar o componente
     * @param id ID do componente
     * @return O WebComponent criado
     */
    public static ReCaptchaV3WebComponent addToContainer(MarkupContainer container, String id) {
        ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent(id);
        container.add(component);
        return component;
    }

    /**
     * Adiciona um WebComponent reCAPTCHA v3 a qualquer container com ação específica
     * 
     * @param container Container onde adicionar o componente
     * @param id ID do componente
     * @param action Ação para o reCAPTCHA v3
     * @return O WebComponent criado
     */
    public static ReCaptchaV3WebComponent addToContainer(MarkupContainer container, String id, String action) {
        ReCaptchaV3WebComponent component = new ReCaptchaV3WebComponent(id, action);
        container.add(component);
        return component;
    }

    /**
     * Valida um WebComponent reCAPTCHA v3
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @param throwException Se deve lançar exceção em caso de falha
     * @return true se válido, false caso contrário
     * @throws RuntimeException Se throwException=true e validação falhar
     */
    public static boolean validate(ReCaptchaV3WebComponent component, boolean throwException) {
        if (component == null) {
            if (throwException) {
                throw new RuntimeException("WebComponent reCAPTCHA v3 não encontrado");
            }
            return false;
        }

        if (!component.isEnabled()) {
            return true; // Se desabilitado, considera válido
        }

        boolean isValid = component.isCompleted();
        
        if (!isValid && throwException) {
            throw new RuntimeException("Falha na verificação reCAPTCHA v3");
        }
        
        return isValid;
    }

    /**
     * Valida um WebComponent reCAPTCHA v3 (lança exceção se inválido)
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @return true se válido
     * @throws RuntimeException Se validação falhar
     */
    public static boolean validate(ReCaptchaV3WebComponent component) {
        return validate(component, true);
    }

    /**
     * Valida um WebComponent reCAPTCHA v3 com verificação de score
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @param minScore Score mínimo para considerar válido
     * @param throwException Se deve lançar exceção em caso de falha
     * @return true se válido, false caso contrário
     * @throws RuntimeException Se throwException=true e validação falhar
     */
    public static boolean validateWithScore(ReCaptchaV3WebComponent component, double minScore, boolean throwException) {
        if (!validate(component, throwException)) {
            return false;
        }

        if (!component.isEnabled()) {
            return true;
        }

        Double score = component.getRecaptchaScore();
        if (score == null) {
            if (throwException) {
                throw new RuntimeException("Score do reCAPTCHA v3 não disponível");
            }
            return false;
        }

        boolean scoreValid = score >= minScore;
        if (!scoreValid && throwException) {
            throw new RuntimeException(String.format("Score do reCAPTCHA v3 muito baixo: %.2f (mínimo: %.2f)", score, minScore));
        }

        return scoreValid;
    }

    /**
     * Cria um WebComponent reCAPTCHA v3 com configuração padrão
     * 
     * @param id ID do componente
     * @return WebComponent configurado
     */
    public static ReCaptchaV3WebComponent create(String id) {
        return new ReCaptchaV3WebComponent(id);
    }

    /**
     * Cria um WebComponent reCAPTCHA v3 com ação específica
     * 
     * @param id ID do componente
     * @param action Ação para o reCAPTCHA v3
     * @return WebComponent configurado
     */
    public static ReCaptchaV3WebComponent create(String id, String action) {
        return new ReCaptchaV3WebComponent(id, action);
    }

    /**
     * Cria um WebComponent reCAPTCHA v3 com ação e score mínimo
     * 
     * @param id ID do componente
     * @param action Ação para o reCAPTCHA v3
     * @param minScore Score mínimo para considerar válido
     * @return WebComponent configurado
     */
    public static ReCaptchaV3WebComponent create(String id, String action, double minScore) {
        return new ReCaptchaV3WebComponent(id, action, minScore);
    }

    /**
     * Método de conveniência para validação com mensagem customizada
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @param errorMessage Mensagem de erro customizada
     * @throws RuntimeException Se validação falhar
     */
    public static void validateWithMessage(ReCaptchaV3WebComponent component, String errorMessage) {
        if (!validate(component, false)) {
            throw new RuntimeException(errorMessage);
        }
    }

    /**
     * Verifica se o componente está habilitado e configurado
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @return true se está habilitado e configurado
     */
    public static boolean isEnabledAndConfigured(ReCaptchaV3WebComponent component) {
        return component != null && component.isEnabled() && 
               component.getSiteKey() != null && !component.getSiteKey().trim().isEmpty();
    }

    /**
     * Obtém informações de debug do componente
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @return String com informações de debug
     */
    public static String getDebugInfo(ReCaptchaV3WebComponent component) {
        if (component == null) {
            return "Component: null";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("ReCaptchaV3WebComponent Debug Info:\n");
        info.append("  ID: ").append(component.getId()).append("\n");
        info.append("  Enabled: ").append(component.isEnabled()).append("\n");
        info.append("  Site Key: ").append(component.getSiteKey()).append("\n");
        info.append("  Action: ").append(component.getAction()).append("\n");
        info.append("  Min Score: ").append(component.getMinScore()).append("\n");
        info.append("  Hidden Field ID: ").append(component.getHiddenFieldId()).append("\n");
        info.append("  Score Field ID: ").append(component.getScoreFieldId()).append("\n");
        info.append("  Completed: ").append(component.isCompleted()).append("\n");
        info.append("  Token: ").append(component.getRecaptchaToken()).append("\n");
        info.append("  Score: ").append(component.getRecaptchaScore());
        
        return info.toString();
    }

    /**
     * Gera JavaScript para re-executar o reCAPTCHA v3 antes de submit
     * 
     * @param component O WebComponent do reCAPTCHA v3
     * @return String com JavaScript para executar
     */
    public static String getRefreshScript(ReCaptchaV3WebComponent component) {
        if (component == null || !component.isEnabled()) {
            return "";
        }
        
        return "if (typeof window.refreshRecaptchaV3_" + component.getMarkupId() + " === 'function') { " +
               "window.refreshRecaptchaV3_" + component.getMarkupId() + "(); }";
    }

    /**
     * Constantes para ações comuns do reCAPTCHA v3
     */
    public static class Actions {
        public static final String SUBMIT = "submit";
        public static final String LOGIN = "login";
        public static final String REGISTER = "register";
        public static final String CONTACT = "contact";
        public static final String SEARCH = "search";
        public static final String DOWNLOAD = "download";
        public static final String PURCHASE = "purchase";
        public static final String COMMENT = "comment";
    }

    /**
     * Constantes para scores recomendados
     */
    public static class Scores {
        public static final double VERY_STRICT = 0.9;  // Muito restritivo
        public static final double STRICT = 0.7;       // Restritivo
        public static final double NORMAL = 0.5;       // Normal (padrão)
        public static final double LENIENT = 0.3;      // Permissivo
        public static final double VERY_LENIENT = 0.1; // Muito permissivo
    }
}
