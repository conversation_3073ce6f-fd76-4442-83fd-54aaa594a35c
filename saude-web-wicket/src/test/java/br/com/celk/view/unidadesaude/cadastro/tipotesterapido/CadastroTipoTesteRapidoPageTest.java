package br.com.celk.view.unidadesaude.cadastro.tipotesterapido;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.favoritos.FavoritosCache;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.Application;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.RecoveryProperties;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.CacheEntityProperties;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.nota.Nota;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import junit.framework.TestCase;
import org.apache.wicket.*;
import org.apache.wicket.application.ComponentInstantiationListenerCollection;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.page.IPageManager;
import org.apache.wicket.request.IRequestParameters;
import org.apache.wicket.request.Request;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.settings.*;
import org.apache.wicket.util.string.StringValue;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.selectDistinct;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        Application.class,
        ApplicationSession.class,
        ThreadContext.class,
        WicketMethods.class,
        RecoveryProperties.class,
        CacheEntityProperties.class,
        LoadManager.class,
        Component.class,
        CommomFacade.class,
        BasicoFacade.class,
        IPageFactory.class,
        IPageManager.class,
        IParameterModuleContainer.class,
        RequestCycle.class,
        Session.class,
        BOFactory.class,
        BOFactoryWicket.class,
        BehaviorInstantiationListenerCollection.class,
        FavoritosCache.class,
})
@PowerMockIgnore("javax.security.*")
public class CadastroTipoTesteRapidoPageTest extends TestCase {

    @Mock   private LoadManager loadManagerParametro;
    @Mock   private LoadManager loadManagerNota;
    @Mock   private RecoveryProperties recoveryProperties;
    @Mock   private Application application;
    @Mock   private CacheEntityProperties cacheEntityProperties;
    @Mock   private ComponentInstantiationListenerCollection componentInstantiationListenerCollection;
    @Mock   private IDebugSettings debugSettings;
    @Mock   private IPageFactory pageFactory;
    @Mock   private IPageSettings pageSettings;
    @Mock   private IJavaScriptLibrarySettings javaScriptLibrarySettings;
    @Mock   private RequestCycle requestCycle;
    @Mock   private ApplicationSession session;
    @Mock   private IPageManager pageManager;
    @Mock   private CommomFacade commomFacade;
    @Mock   private BasicoFacade basicoFacade;
    @Mock   private IParameterModuleContainer parameterModuloContainer;
    @Mock   private BehaviorInstantiationListenerCollection behaviorInstantiationListeners;
    @Mock   private Request request;
    @Mock   private IRequestParameters requestParameters;
    @Mock   private IResourceSettings resourceSettings;
    @Mock   private Localizer localizer;
    @Mock   private AbstractSessaoAplicacao sessaoAplicacao;
    @Mock   private IFrameworkSettings frameworkSettings;
    @Mock   private FavoritosCache favoritosCache;
    @Mock   private CharSequence charSequence;




    @Before
    public void setUp() throws Exception {
        mockStatic(Application.class);
        mockStatic(ApplicationSession.class);
        mockStatic(ThreadContext.class);
        mockStatic(WicketMethods.class);
        mockStatic(LoadManager.class);
        mockStatic(CacheEntityProperties.class);
        mockStatic(RecoveryProperties.class);
        mockStatic(RequestCycle.class);
        mockStatic(Session.class);
        mockStatic(CommomFacade.class);
        mockStatic(BasicoFacade.class);
        mockStatic(BOFactory.class);
        mockStatic(BOFactoryWicket.class);
        mockStatic(FavoritosCache.class);


        when(FavoritosCache.get()).thenReturn(favoritosCache);


        when(ApplicationSession.get()).thenReturn(session);
        when(session.getSessaoAplicacao()).thenReturn(sessaoAplicacao);
        when(sessaoAplicacao.getUsuario()).thenReturn(getUsuarioSessao());
        when(sessaoAplicacao.getEmpresa()).thenReturn(getEmpresaSessao());


        when(Application.get()).thenReturn(application);
        when(application.getBehaviorInstantiationListeners()).thenReturn(behaviorInstantiationListeners);
        when(application.getResourceSettings()).thenReturn(resourceSettings);
        when(application.getFrameworkSettings()).thenReturn(frameworkSettings);
        when(resourceSettings.getLocalizer()).thenReturn(localizer);


        when(BOFactoryWicket.getBO(CommomFacade.class)).thenReturn(commomFacade);
        when(BOFactory.getBO(CommomFacade.class)).thenReturn(commomFacade);
        when(BOFactoryWicket.getBO(BasicoFacade.class)).thenReturn(basicoFacade);
        when(commomFacade.modulo(any())).thenReturn(parameterModuloContainer);
        when(commomFacade.modulo(Modulos.GERAL).getParametro("tituloPaginaLogin")).thenReturn(getTituloPaginaLogin());
        when(commomFacade.modulo(Modulos.GERAL).getParametro("exibirLogoCelk")).thenReturn(RepositoryComponentDefault.NAO_LONG);


        when(RequestCycle.get()).thenReturn(requestCycle);
        when(requestCycle.getRequest()).thenReturn(request);
        when(requestCycle.urlFor((ResourceReference) any(), any())).thenReturn(charSequence);
        when(request.getRequestParameters()).thenReturn(requestParameters);
        when(requestParameters.getParameterValue("cdPrg")).thenReturn(StringValue.valueOf("123"));


        when(Session.get()).thenReturn(session);
        when(session.getPageManager()).thenReturn(pageManager);


        when(ThreadContext.getApplication()).thenReturn(application);
        when(application.getPageFactory()).thenReturn(pageFactory);
        when(application.getPageSettings()).thenReturn(pageSettings);
        when(application.getJavaScriptLibrarySettings()).thenReturn(javaScriptLibrarySettings);
        when(application.getComponentInstantiationListeners()).thenReturn(componentInstantiationListenerCollection);
        when(application.getDebugSettings()).thenReturn(debugSettings);

        PowerMockito.when(WicketMethods.bundle(anyString())).thenReturn("Mensagem Bundle Teste");
        mockLoadManager(loadManagerParametro, Parametro.class);
        mockLoadManager(loadManagerNota, Nota.class);
    }

    private Empresa getEmpresaSessao() {
        Empresa e = new Empresa(1234L);
        e.setDescricao("EMPRESA TESTE");
        return e;
    }


    @Test
    public void test_DeveRetornarAMesmaDescricaoAoIniciarPagina() {
        CadastroTipoTesteRapidoPage cttr = new CadastroTipoTesteRapidoPage(getTipoTesteRapido());
        CompoundPropertyModel<TipoTesteRapido> formModel = new CompoundPropertyModel<>(getTipoTesteRapido());
        Form<TipoTesteRapido> form = new Form<>("form", formModel);
        cttr.init(form);

        InputField<String> txtDescricao = cttr.getTxtDescricao();
        DropDown<Long> dropDownTipoTeste = cttr.getDropDownTipoTeste();
        AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento = cttr.getAutoCompleteConsultaProcedimento();
        WebMarkupContainer edicaoObservacao = cttr.getEdicaoObservacao();

        Assert.assertEquals(getTipoTesteRapido().getDescricao(),txtDescricao.getComponentValue());
        Assert.assertTrue(dropDownTipoTeste.getValues().containsKey(getTipoTesteRapido().getTipoTeste()));
        Assert.assertEquals(getTipoTesteRapido().getProcedimento(),autoCompleteConsultaProcedimento.getTxtDescricao().getTextField().getComponentValue());
    }

    private TipoTesteRapido getTipoTesteRapido(){
        TipoTesteRapido ttr = new TipoTesteRapido();
        ttr.setCodigo(1234L);
        ttr.setDescricao("TESTE DE TIPO TESTE RAPIDO - HEPATITE B");
        ttr.setObservacao("TESTE OBSERVACAO - HEPATITE B");
        ttr.setTipoTeste((Long) TipoTesteRapido.TipoTeste.HEPATITE_B.value());
        return ttr;
    }

    private Usuario getUsuarioSessao() {
        Usuario u = new Usuario();
        u.setCodigo(234L);
        u.setNome("Cristian");
        return u;
    }

    private Object getTituloPaginaLogin() {
        return "LoginTeste";
    }
    private void mockLoadManager(LoadManager loadManager, Class clazz) {
        String[] properties = {};
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        when(loadManager.setId(any())).thenReturn(loadManager);
        when(loadManager.setLazyMode(true)).thenReturn(loadManager);
        when(loadManager.setLazyMode(false)).thenReturn(loadManager);
        when(loadManager.addProperty(any())).thenReturn(loadManager);
        when(loadManager.addProperties(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.addParameter(any())).thenReturn(loadManager);
        when(loadManager.addGroup(any())).thenReturn(loadManager);
        when(loadManager.addGroup(any())).thenReturn(loadManager);
        when(loadManager.addSorter(any())).thenReturn(loadManager);
        when(loadManager.addSorter(any())).thenReturn(loadManager);
        when((List) loadManager.getList()).thenReturn((List) getObjectList(clazz));
        when(RecoveryProperties.applyAlias("", properties)).thenReturn(properties);
        when(CacheEntityProperties.getInstance()).thenReturn(cacheEntityProperties);
        when(CacheEntityProperties.getInstance().getRecoveryProperties(clazz)).thenReturn(recoveryProperties);
        when(recoveryProperties.getPropertiesValues()).thenReturn(properties);
        when(loadManager.getVO()).thenReturn(getObject(clazz));
    }

    private Object getObjectList(Class clazz) {
        if (clazz.isInstance(getParametro()))return Collections.singletonList(getParametro());
        if (clazz.isInstance(getNota()))return Collections.singletonList(getNota());

        return new ArrayList<Object>();
    }

    private Object getObject(Class clazz) {
        if(clazz.isInstance(getParametro())) return getParametro();
        if(clazz.isInstance(getNota())) return 1L;
        return new Object();
    }

    private Parametro getParametro(){
        Parametro p = new Parametro(1234L);
        p.setDataAtualizacao(DataUtil.getDataAtual());
        return p;
    }

    private Nota getNota(){
        Nota n = new Nota();
        n.setAtivo(1L);
        n.setCodigo(1234L);
        return n;
    }

}
