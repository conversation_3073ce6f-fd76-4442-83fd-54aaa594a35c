package br.com.celk.view.agenda.agendamento.agendamentoListaEspera;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BOFactoryWicket.class, FiltrarAgendasByUnidadeDaSolicitacao.class})
public class FiltrarAgendaGradePorPrioridadeTest {

    @Mock
    private AgendamentoFacade agendamentoBO;
    private FiltrarAgendaGradePorPrioridade filtrarAgendaGradePorPrioridade;
    private SolicitacaoAgendamento solicitacaoAgendamento;

    @Before
    public void setup() {
        PowerMockito.mockStatic(BOFactoryWicket.class);
        PowerMockito.mockStatic(FiltrarAgendasByUnidadeDaSolicitacao.class);
        when(BOFactoryWicket.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        filtrarAgendaGradePorPrioridade = new FiltrarAgendaGradePorPrioridade();
        solicitacaoAgendamento = new SolicitacaoAgendamento();
    }

    @Test
    public void deveRetornarMaisDeUmaAgenda() throws ValidacaoException, DAOException {
        Empresa quitandinha = unidadeQuitandinha();
        Empresa centroIntegradoDeSaude = centroIntegradoDeSaude();

        List<AgendaGradeAtendimentoDTO> agendas = new ArrayList<>();
        agendas.add(agendaGradeAtendimento(quitandinha));
        agendas.add(agendaGradeAtendimento(centroIntegradoDeSaude));

        solicitacaoAgendamento.setEmpresa(quitandinha);

        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(true);
        when(agendamentoBO.odernarPrioridadeConsumoAgenda(quitandinha, agendas)).thenReturn(agendas);
        when(agendamentoBO.filtrarAgendaPorSexoIdade(solicitacaoAgendamento, agendas)).thenReturn(agendas);
        when(FiltrarAgendasByUnidadeDaSolicitacao.filtrar(agendas, solicitacaoAgendamento)).thenReturn(agendas);

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = filtrarAgendaGradePorPrioridade.filtrar(agendas, solicitacaoAgendamento);
        assertNotNull("Lista de agenda não pode ser nula", agendasFiltradas);
        assertFalse("Lista de agenda não pode ser vazia", agendasFiltradas.isEmpty());
        assertEquals("Deve retornar duas agendas", 2, agendasFiltradas.size());
    }

    @Test
    public void deveRetornarApenasUmaAgenda() throws ValidacaoException, DAOException {
        Empresa quitandinha = unidadeQuitandinha();
        Empresa centroIntegradoDeSaude = centroIntegradoDeSaude();

        List<AgendaGradeAtendimentoDTO> agendas = new ArrayList<>();
        agendas.add(agendaGradeAtendimento(quitandinha));
        agendas.add(agendaGradeAtendimento(centroIntegradoDeSaude));

        solicitacaoAgendamento.setEmpresa(quitandinha);

        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(false);
        when(agendamentoBO.odernarPrioridadeConsumoAgenda(quitandinha, agendas)).thenReturn(agendas);
        when(agendamentoBO.filtrarAgendaPorSexoIdade(solicitacaoAgendamento, agendas)).thenReturn(agendas);
        when(FiltrarAgendasByUnidadeDaSolicitacao.filtrar(agendas, solicitacaoAgendamento)).thenReturn(agendas);

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = filtrarAgendaGradePorPrioridade.filtrar(agendas, solicitacaoAgendamento);
        assertNotNull("Lista de agenda não pode ser nula", agendasFiltradas);
        assertFalse("Lista de agenda não pode ser vazia", agendasFiltradas.isEmpty());
        assertEquals("Deve retornar apenas uma agenda", 1, agendasFiltradas.size());
    }

    @Test
    public void deveRetornarListaAgendaVazia() throws ValidacaoException, DAOException {
        Empresa quitandinha = unidadeQuitandinha();

        List<AgendaGradeAtendimentoDTO> agendas = new ArrayList<>();
        solicitacaoAgendamento.setEmpresa(quitandinha);

        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(false);
        when(agendamentoBO.odernarPrioridadeConsumoAgenda(quitandinha, agendas)).thenReturn(agendas);
        when(agendamentoBO.filtrarAgendaPorSexoIdade(solicitacaoAgendamento, agendas)).thenReturn(agendas);
        when(FiltrarAgendasByUnidadeDaSolicitacao.filtrar(agendas, solicitacaoAgendamento)).thenReturn(agendas);

        List<AgendaGradeAtendimentoDTO> agendasFiltradas = filtrarAgendaGradePorPrioridade.filtrar(agendas, solicitacaoAgendamento);
        assertNotNull("Lista de agenda não pode ser nula", agendasFiltradas);
        assertTrue("Lista de agenda deve ser vazia", agendasFiltradas.isEmpty());
    }

    private static Empresa unidadeQuitandinha() {
        Empresa quitandinha = new Empresa(1L);
        quitandinha.setDescricao("Quitandinha");
        quitandinha.setTipoUnidade(TipoEstabelecimento.UNIDADE.value());
        return quitandinha;
    }

    private static Empresa centroIntegradoDeSaude() {
        Empresa centroIntegradoSaude = new Empresa(2L);
        centroIntegradoSaude.setDescricao("Centro Integrado de Saúde");
        centroIntegradoSaude.setTipoUnidade(TipoEstabelecimento.UNIDADE.value());
        return centroIntegradoSaude;
    }

    private AgendaGradeAtendimentoDTO agendaGradeAtendimento(Empresa unidadeSaude) {
        AgendaGradeAtendimentoDTO agendaGrade = new AgendaGradeAtendimentoDTO();
        agendaGrade.setEmpresaAgenda(unidadeSaude);
        agendaGrade.setData(DataUtil.getDataAtual());
        return agendaGrade;
    }
}
