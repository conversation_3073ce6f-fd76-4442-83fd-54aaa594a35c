# Correção do Erro ReCAPTCHA

## Problema Identificado

O erro "ReCAPTCHA couldn't find user-provided function: onRecaptchaCallback" estava ocorrendo devido a conflitos entre diferentes implementações de componentes reCAPTCHA na aplicação.

### Causa Raiz

1. **Conflito de Implementações**: Existiam múltiplos componentes reCAPTCHA:
   - `ReCaptchaPanel` (usado na página de consulta)
   - `SimpleReCaptchaComponent` 
   - `ReCaptchaComponent`

2. **Inconsistência HTML vs JavaScript**: O arquivo HTML do `ReCaptchaPanel` definia atributos `data-callback="onRecaptchaCallback"`, mas o JavaScript usava `grecaptcha.render()` programaticamente, ignorando esses atributos.

3. **Funções Globais Conflitantes**: Múltiplos componentes definiam a mesma função global `onRecaptchaCallback`, causando conflitos.

## Correções Implementadas

### 1. Correção do HTML (ReCaptchaPanel.html)

**Antes:**
```html
<div id="recaptcha-div" class="g-recaptcha"
     data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"
     data-callback="onRecaptchaCallback"
     data-expired-callback="onRecaptchaExpired">
</div>
```

**Depois:**
```html
<div id="recaptcha-div"></div>
```

### 2. Correção do JavaScript (ReCaptchaPanel.java)

**Problema:** Função global `checkAndRenderRecaptcha()` podia conflitar com outros componentes.

**Solução:** Usar nomes únicos baseados no ID do componente:

```java
String uniqueId = getMarkupId();
String initScript = String.format(
    "function checkAndRenderRecaptcha_%s() { " +
    // ... resto da implementação
    "setTimeout(checkAndRenderRecaptcha_%s, 1000);",
    uniqueId, // usado em múltiplos lugares
    uniqueId
);
```

## Benefícios da Correção

1. **Eliminação de Conflitos**: Cada instância do componente usa funções JavaScript únicas
2. **Implementação Consistente**: JavaScript programático sem dependência de atributos HTML
3. **Melhor Debug**: Logs específicos por componente
4. **Compatibilidade**: Não interfere com outros componentes reCAPTCHA existentes

## Como Testar

1. **Acesse a página de consulta de medicamentos**
2. **Abra o console do navegador (F12)**
3. **Verifique os logs:**
   - "Google reCAPTCHA API ready for component [ID]"
   - "reCAPTCHA widget rendered with ID: [widget_id]"
4. **Complete o reCAPTCHA**
5. **Verifique o log:** "reCAPTCHA completed - Token set: [token]..."
6. **Teste a validação:** Tente fazer uma busca sem completar o reCAPTCHA

## Arquivos Modificados

- `saude-web-wicket/src/main/java/br/com/celk/component/recaptcha/ReCaptchaPanel.html`
- `saude-web-wicket/src/main/java/br/com/celk/component/recaptcha/ReCaptchaPanel.java`

## Próximos Passos Recomendados

1. **Testar em ambiente de desenvolvimento**
2. **Verificar se não há regressões em outras páginas**
3. **Considerar consolidar os múltiplos componentes reCAPTCHA em uma implementação única**
4. **Configurar chaves de produção do Google reCAPTCHA quando necessário**
