package br.com.ksisolucoes.server;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.dto.PropertiesMDBeanDTO;
import br.com.ksisolucoes.command.InterfaceCommand;
import br.com.ksisolucoes.command.InterfaceCommandReturn;
import br.com.ksisolucoes.report.CommandDataReport;
import br.com.ksisolucoes.report.CommandFileReport;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.log4j.MDC;

import javax.annotation.Resource;
import javax.ejb.ActivationConfigProperty;
import javax.ejb.EJBContext;
import javax.ejb.MessageDriven;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

//@JMSDestinationDefinitions(
//        value =  {
//                @JMSDestinationDefinition(
//                        name = "java:/queue/HELLOWORLDMDBQueue",
//                        interfaceName = "javax.jms.Queue",
//                        destinationName = "HelloWorldMDBQueue"
//                ),
//                @JMSDestinationDefinition(
//                        name = "java:/topic/HELLOWORLDMDBTopic",
//                        interfaceName = "javax.jms.Topic",
//                        destinationName = "HelloWorldMDBTopic"
//                )
//        }
//)
/**
 *
 * <AUTHOR>
 */
@MessageDriven(name = "CommandServerMDBean", activationConfig = {
    @ActivationConfigProperty(propertyName = "destinationLookup", propertyValue = "queue/RequestExecutor"),
    @ActivationConfigProperty(propertyName = "destinationType", propertyValue = "javax.jms.Queue"),
    @ActivationConfigProperty(propertyName = "acknowledgeMode", propertyValue = "Auto-acknowledge"),
    @ActivationConfigProperty(propertyName = "maxSession", propertyValue = "5"),
    @ActivationConfigProperty(propertyName = "transactionTimeout", propertyValue = "86400"),
})
public class CommandServerMDBean implements MessageListener {

    @Resource
    private EJBContext context;

    @Override
    public void onMessage(Message message) {
        InterfaceCommand command = null;
        Long codigoUsuario = null;
        Long asyncProcess = null;
        Date inicio = new Date();
        try {
            asyncProcess = message.getLongProperty("asyncProcess");
            ObjectMessage objectMessage = (ObjectMessage) message;
            PropertiesMDBeanDTO dto = (PropertiesMDBeanDTO) objectMessage.getObject();

            AbstractSessaoAplicacao sessaoAplicacao = dto.getSessaoAplicacao();
            SessaoAplicacaoContext.setContext(sessaoAplicacao);
            TenantContext.setContext(dto.getTenantId());

            codigoUsuario = sessaoAplicacao.getCodigoUsuario();
            
            MDC.put("tenant", TenantContext.getRealContext());
            MDC.put("usuario", codigoUsuario);
            
            command = dto.getInterfaceCommand();
            command = BOFactory.getBO(CommomFacade.class).executeCommandNewTransaction(command);

            Serializable commandReturn = null;
            if (InterfaceCommandReturn.class.isAssignableFrom(command.getClass())) {
                commandReturn = (Serializable) ((InterfaceCommandReturn) command).getDataReturn();
            }

            if (CommandDataReport.class.isAssignableFrom(command.getClass())) {
                gerarLogRequisicao(inicio, codigoUsuario, ((CommandDataReport)command).getReport().getXML());
            } else if (CommandFileReport.class.isAssignableFrom(command.getClass())) {
                gerarLogRequisicao(inicio, codigoUsuario, ((CommandFileReport)command).getReport().getXML());
            } else {
                gerarLogRequisicao(inicio, codigoUsuario, command.getClass().getName());
            }

            sendResponse(codigoUsuario, commandReturn, AsyncProcess.STATUS_CONCLUIDO_EXITO, asyncProcess, null);

        } catch (Throwable ex) {
            context.setRollbackOnly();

            try {
                //No postgresql quando da erro de sql a transacao morre, arrumar para enviar a resposta em outra transação!
                if (ex instanceof ValidacaoException) {
                    Loggable.log.warn(ex.getMessage(), ex);
                    sendResponse(codigoUsuario, null, AsyncProcess.STATUS_CONCLUIDO_ERRO, asyncProcess, ex.getMessage());
                } else {
                    Loggable.log.error(ex.getMessage(), ex);
                    sendResponse(codigoUsuario, null, AsyncProcess.STATUS_CONCLUIDO_ERRO, asyncProcess, StringUtilKsi.montarMensagemErro(ex));
                }
            } catch (ValidacaoException ex1) {
                Loggable.log.warn(ex1.getMessage());
            } catch (SGKException ex1) {
                Loggable.log.error(ex1.getMessage());
            } catch (JMSException ex1) {
                Loggable.log.error(ex1.getMessage());
            }
        } finally {
            MDC.remove("tenant");
            MDC.remove("usuario");
            SessaoAplicacaoContext.setContext(null);
        }
    }

    private void sendResponse(Long usuario, Serializable commandReturn, Long status, Long asyncProcess, String erro) throws SGKException, JMSException {

        BOFactory.getBO(CommomFacade.class).responseTopic(usuario, commandReturn, status, asyncProcess, erro);

    }

    private void gerarLogRequisicao(Date inicio, Long codigoUsuario, String nome) {
        Date fim = new Date();

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO tempo_processos (cd_tempo_processos,uuid, dt_inicio, dt_fim, inicio,fim,tempo, cd_usuario, tenant,descricao) VALUES (nextval('seq_temp_processos'), 'mdb',");
        sql.append("'").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(inicio)).append("',");
        sql.append("'").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(fim)).append("',");
        sql.append(inicio.getTime()).append(",");
        sql.append(fim.getTime()).append(",");
        sql.append(fim.getTime() - inicio.getTime()).append(",");
        sql.append(codigoUsuario).append(",");
        sql.append("'").append(TenantContext.getRealContext()).append("',");
        sql.append("'").append(nome).append("'");
        sql.append(");");

        Loggable.request.debug(sql.toString());
    }
}
