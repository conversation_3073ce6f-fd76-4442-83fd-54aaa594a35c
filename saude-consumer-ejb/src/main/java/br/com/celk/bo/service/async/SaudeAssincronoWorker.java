package br.com.celk.bo.service.async;

import br.com.celk.amazon.sqs.javamessaging.SQSConnection;
import br.com.celk.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazonaws.regions.Regions;
import javax.ejb.Singleton;
import javax.jms.JMSException;
import javax.jms.Queue;
import javax.jms.Session;
import br.com.celk.bo.service.message.SqsMessageReceiver;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.SystemProperty;
import br.com.celk.amazon.sqs.javamessaging.ProviderConfiguration;
import br.com.celk.amazon.sqs.javamessaging.SQSMessageConsumer;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import java.util.Calendar;
import java.util.GregorianCalendar;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.ejb.DependsOn;
import javax.ejb.Schedule;
import javax.ejb.Startup;
/**
 *
 * <AUTHOR>
 */
@Startup
@Singleton
@DependsOn(value = {"LoadTenantRegistry","CacheHelper"})
public class SaudeAssincronoWorker {
    
    public static SQSConnection connectionDefault = null;
    public static SQSConnection connectionScheduled = null;
    public static SQSConnection connectionCritical = null;

    @PreDestroy
    public void finalizandoSingleton(){
        if(connectionDefault != null){
            try {
                Loggable.log.info("Fechando conexao SQS Default");
                connectionDefault.close();
                Loggable.log.info("Conexao SQS Default fechada com sucesso!");
            } catch (JMSException ex) {
                Loggable.log.info(ex.getMessage(), ex);
            }
        }
        if(connectionScheduled != null){
            try {
                Loggable.log.info("Fechando conexao SQS Agendada");
                connectionScheduled.close();
                Loggable.log.info("Conexao SQS Agendada fechada com sucesso!");
            } catch (JMSException ex) {
                Loggable.log.info(ex.getMessage(), ex);
            }
        }
        if(connectionCritical != null){
            try {
                Loggable.log.info("Fechando conexao SQS para processos criticos");
                connectionCritical.close();
                Loggable.log.info("Conexao SQS Integracoes fechada com sucesso!");
            } catch (JMSException ex) {
                Loggable.log.info(ex.getMessage(), ex);
            }
        }
    }
    
    @PostConstruct
    public void init(){
        if ("true".equals(SystemProperty.PROCESSO_ASSINCRONO_USA_SQS.value())){
            Loggable.log.info("PROCESSO ASSINCRONO Iniciando conexao com SQS");
            try {
                AmazonSQSClientBuilder builder = AmazonSQSClientBuilder.standard().withRegion(Regions.SA_EAST_1);
                SQSConnectionFactory connectionFactory = new SQSConnectionFactory(new ProviderConfiguration().withNumberOfMessagesToPrefetch(0), builder);
        
                //Creating connection default
                connectionDefault = createSQSConnectionAndWorkers(connectionFactory, SystemProperty.PROCESSO_ASSINCRONO_SQS_NAME.value(), Integer.valueOf(SystemProperty.PROCESSO_ASSINCRONO_WORKERS.value()));
                connectionDefault.start();
                Loggable.log.info("PROCESSO ASSINCRONO Conexao com SQS concluida com exito. Queue Geral");
                
                //Creating schedule connection
                connectionScheduled = createSQSConnectionAndWorkers(connectionFactory, SystemProperty.PROCESSO_ASSINCRONO_POR_HORARIO_SQS_NAME.value(), Integer.valueOf(SystemProperty.PROCESSO_ASSINCRONO_POR_HORARIO_WORKERS.value()));
                scheduledQueueActivation();
                Loggable.log.info("PROCESSO ASSINCRONO Conexao com SQS concluida com exito. Queue Por Horario");

                //Creating connection for critical process
                connectionCritical = createSQSConnectionAndWorkers(connectionFactory, SystemProperty.PROCESSO_ASSINCRONO_CRITICO_SQS_NAME.value(), Integer.valueOf(SystemProperty.PROCESSO_ASSINCRONO_CRITICO_WORKERS.value()));
                connectionCritical.start();
                Loggable.log.info("PROCESSO ASSINCRONO Conexao com SQS concluida com exito. Queue Critica");
                
            } catch (JMSException e) {
                throw new RuntimeException(e.getMessage(), e.getCause());
            }
        }
    }

    @Schedule(minute="*/5", hour="*", persistent=false)
    public void scheduledQueueActivation(){
        if ("true".equals(SystemProperty.PROCESSO_ASSINCRONO_USA_SQS.value())){
            try {
                String start = SystemProperty.PROCESSO_ASSINCRONO_POR_HORARIO_INICIO.value();
                String end = SystemProperty.PROCESSO_ASSINCRONO_POR_HORARIO_FIM.value();
                
                start = start.substring(0, 2);
                end = end.substring(0, 2);
                
                boolean active = false;
                int startTime = Integer.valueOf(start);
                int currentTime = new GregorianCalendar().get(Calendar.HOUR_OF_DAY);
                int endTime = Integer.valueOf(end);
                
                if(startTime > endTime){
                    if(currentTime > startTime){
                        active = true;
                    }else if(currentTime < startTime && currentTime < endTime){
                        active = true;
                    }
                }else{
                    if(currentTime > startTime && currentTime < endTime){
                        active = true;
                    }
                }

                if(active){
                    startScheduledQueue();
                }else{
                    stopScheduledQueue();
                }
            } catch (JMSException e) {
                throw new RuntimeException(e.getMessage(), e.getCause());
            }
        }
    }
    
    public static void startScheduledQueue() throws JMSException{
        if(connectionScheduled != null){
            connectionScheduled.start();
        }
    }

    public static void stopScheduledQueue() throws JMSException{
        if(connectionScheduled != null){
            connectionScheduled.stop();
        }
    }
    
    private SQSConnection createSQSConnectionAndWorkers(SQSConnectionFactory connectionFactory, String SQSName, int numberOfWorkers) throws JMSException{
        SQSConnection connection = connectionFactory.createConnection();
        Queue queue = connection.createSession(false, Session.AUTO_ACKNOWLEDGE).createQueue(SQSName);

        for (int i = 0; i < numberOfWorkers; i++){
            SQSMessageConsumer consumer =  (SQSMessageConsumer) connection.createSession(false, Session.AUTO_ACKNOWLEDGE).createConsumer(queue);
            consumer.setMessageListener(new SqsMessageReceiver());
        }
        
        return connection;
    }
}
