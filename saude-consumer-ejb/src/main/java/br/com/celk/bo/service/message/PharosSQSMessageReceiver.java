package br.com.celk.bo.service.message;

import br.com.celk.service.async.PharosQueueMessageDTO;
import br.com.celk.service.async.PharosTopicDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.TextMessage;

/**
 * <AUTHOR>
 */
public class PharosSQSMessageReceiver implements MessageListener {

    @Override
    public void onMessage(Message message) {
        try {
            TextMessage textMessage = (TextMessage) message;
            String body = textMessage.getText();
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            PharosTopicDTO topicDTO = objectMapper.readValue(body, PharosTopicDTO.class);
            PharosQueueMessageDTO messageDto = objectMapper.readValue(topicDTO.getMessage(), PharosQueueMessageDTO.class);

            BOFactory.getBO(VigilanciaFacade.class).atualizarRegistroPharos(messageDto);

        } catch (Exception ex) {
            Loggable.log.error(ex.getCause());
            throw new RuntimeException(ex.getMessage(), ex);
        }


    }
}
