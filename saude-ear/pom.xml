<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saude-2</artifactId>
        <groupId>br.com.celk</groupId>
<version>3.1.285.1-SNAPSHOT</version>
    </parent>

    <artifactId>saude-ear</artifactId>
    <packaging>ear</packaging>
    <name>saude-ear</name>

    <build>
        <finalName>saude-ear</finalName>
        <plugins>
            <plugin>
                <groupId>org.jboss.as.plugins</groupId>
                <artifactId>jboss-as-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-ear-plugin</artifactId>
                <!-- configuring the ear plugin -->
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Dependencies>org.dom4j, org.hornetq, org.apache.velocity, org.apache.commons.beanutils,
                                org.apache.commons.collections, org.apache.commons.cli, org.apache.commons.io,
                                org.picketbox, javax.mail.api, org.infinispan
                            </Dependencies>
                        </manifestEntries>
                    </archive>
                    <version>6</version>
                    <resourcesDir>../resource</resourcesDir>
                    <defaultLibBundleDir>/lib</defaultLibBundleDir>
                    <modules>
                        <ejbModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-utils</artifactId>
                            <bundleFileName>saude-utils.jar</bundleFileName>
                        </ejbModule>

                        <webModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-web-wicket</artifactId>
                            <bundleFileName>saude-web-wicket.war</bundleFileName>
                            <contextRoot>/</contextRoot>
                        </webModule>

                        <webModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-processos-service</artifactId>
                            <bundleFileName>saude-processos-service.war</bundleFileName>
                            <contextRoot>/rest</contextRoot>
                        </webModule>

                        <webModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-app-cidadao-service</artifactId>
                            <bundleFileName>saude-app-cidadao-service.war</bundleFileName>
                            <contextRoot>/app-cidadao</contextRoot>
                        </webModule>

                        <ejbModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-core</artifactId>
                            <bundleFileName>saude-core.jar</bundleFileName>
                        </ejbModule>

                        <ejbModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-consumer-ejb</artifactId>
                            <bundleFileName>saude-consumer-ejb.jar</bundleFileName>
                        </ejbModule>

                        <ejbModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-bo-ejb</artifactId>
                            <bundleFileName>saude-bo-ejb.jar</bundleFileName>
                        </ejbModule>
                        <webModule>
                            <groupId>br.com.celk</groupId>
                            <artifactId>saude-sistema-healthcheck</artifactId>
                            <bundleFileName>saude-sistema-healthcheck.war</bundleFileName>
                            <contextRoot>/healthcheck</contextRoot>
                        </webModule>
                    </modules>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-core</artifactId>
            <type>ejb</type>
            <exclusions>
                <exclusion>
                    <artifactId>postgresql</artifactId>
                    <groupId>org.postgresql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-utils</artifactId>
            <type>ejb</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-bo-ejb</artifactId>
            <type>ejb</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-sistema-healthcheck</artifactId>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-command</artifactId>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-connect</artifactId>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-indra-connect</artifactId>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-consumer-ejb</artifactId>
            <type>ejb</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-web-wicket</artifactId>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-processos-service</artifactId>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-app-cidadao-service</artifactId>
            <type>war</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-integrating-modules</artifactId>
            <type>ejb</type>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-sms</artifactId>
            <type>jar</type>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <configLocation>config/sun_checks.xml</configLocation>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>celk</id>
        </profile>
    </profiles>
</project>
