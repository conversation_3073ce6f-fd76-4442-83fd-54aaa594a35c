/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.celk.util;

import java.text.Normalizer;
import java.util.regex.Pattern;

public class CaracterEspecialUtil {

    private String novoTexto = "";

    public CaracterEspecialUtil builder(String texto) {
        novoTexto = texto == null ? "" : texto;
        return this;
    }

    public String build() {
        return novoTexto;
    }

    public CaracterEspecialUtil removerBarras() {
        novoTexto = Pattern.compile("[\\\\/]+").matcher(novoTexto).replaceAll("");
        return this;
    }

    public CaracterEspecialUtil removerCaracteresEspeciais(String conjuntoCaracteresRemover) {
        novoTexto = Pattern.compile("[" + conjuntoCaracteresRemover + "]").matcher(novoTexto.trim()).replaceAll("");
        return this;
    }

    public CaracterEspecialUtil manterSomenteNumeros() {
        novoTexto = Pattern.compile("[^0-9]").matcher(novoTexto).replaceAll("");
        return this;
    }

    public CaracterEspecialUtil removerNumeros() {
        novoTexto = Pattern.compile("[0-9]").matcher(novoTexto).replaceAll("");
        return this;
    }

    public CaracterEspecialUtil removerEspacosExtras() {
        novoTexto = Pattern.compile("\\s+").matcher(novoTexto).replaceAll(" ").trim();
        return this;
    }

    public CaracterEspecialUtil removerAcentuacao() {
        String normalizado = Normalizer.normalize(novoTexto, Normalizer.Form.NFD);
        normalizado = normalizado.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");
        novoTexto = normalizado.replaceAll("[^\\p{ASCII}]", "");
        return this;
    }

    public CaracterEspecialUtil removerCaracteresEspeciais() {
        novoTexto = Pattern.compile("[^a-zA-Z0-9 ]").matcher(novoTexto).replaceAll("");
        return this;
    }
}
