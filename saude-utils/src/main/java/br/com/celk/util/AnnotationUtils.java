/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.celk.util;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;

/**
 * <AUTHOR>
 * <PERSON>riado em: May 27, 2013
 */
public class AnnotationUtils {

    public static List<Class> findClasses(Class<? extends Annotation> annotation, String path){
        List<Class> classes = new ArrayList();
        try {
            PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = pathMatchingResourcePatternResolver.getResources("classpath*:"+path+"/**/*.class");
            SimpleMetadataReaderFactory simpleMetadataReaderFactory = new SimpleMetadataReaderFactory();
            for (Resource resource : resources) {
                MetadataReader metadataReader = simpleMetadataReaderFactory.getMetadataReader(resource);
                AnnotationMetadata annotationMetadata = metadataReader.getAnnotationMetadata();

                if(annotationMetadata.hasAnnotation(annotation.getName())){
                    Class<?> clazz = Class.forName(metadataReader.getClassMetadata().getClassName());
                    classes.add(clazz);
                }
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(AnnotationUtils.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        } catch (IOException ex) {
            Logger.getLogger(AnnotationUtils.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }
        return classes;
    }
    
}
