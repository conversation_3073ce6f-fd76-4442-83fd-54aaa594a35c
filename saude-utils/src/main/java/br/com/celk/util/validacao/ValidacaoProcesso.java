package br.com.celk.util.validacao;

import br.com.celk.util.StringUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ValidacaoProcesso implements Serializable{
    
    private List<ValidacaoProcesso> validacaoProcessoList;
    private List<String> mensagemList;
    private String recuo = "";

    public ValidacaoProcesso(ValidacaoProcesso validacaoProcesso) {
        this();
        validacaoProcessoList.add(validacaoProcesso);
    }
    
    public ValidacaoProcesso(Throwable t) {
        this(StringUtil.montarMensagemErro(t));
    }

    public ValidacaoProcesso(String msg) {
        this();
        mensagemList.add(msg);
    }
    
    public ValidacaoProcesso() {
        this.validacaoProcessoList = new ArrayList<ValidacaoProcesso>();
        this.mensagemList = new ArrayList<String>();
    }
    
    public void add(Throwable t){
        mensagemList.add(StringUtil.montarMensagemErro(t));
    }
    
    public void add(String mensagem){
        mensagemList.add(mensagem);
    }
    
    public void add(ValidacaoProcesso mensagemValidacao){
        validacaoProcessoList.add(mensagemValidacao);
    }

    public List<ValidacaoProcesso> getValidacaoProcessoList() {
        return validacaoProcessoList;
    }

    public List<String> getMensagemList() {
        return mensagemList;
    }

    @Override
    public String toString() {
        StringBuilder retorno = new StringBuilder();
        for (String string : mensagemList) {
            retorno.append(recuo).append(string).append("\n");
        }
        
        for (ValidacaoProcesso validacaoProcesso : validacaoProcessoList) {
            validacaoProcesso.adicionarRecuo(recuo);
            retorno.append(validacaoProcesso.toString());
        }
        return retorno.toString();
    }
    
    protected void adicionarRecuo(String recuo){
        this.recuo = recuo+"\t";
    }
}
