package br.com.celk.sms.restservice.build;

import br.com.celk.sms.restservice.response.LoteMensagemDTO;
import br.com.celk.sms.restservice.response.MensagemSmsDTO;
import org.junit.Test;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * <AUTHOR>
 */
public class BuildLoteMensagemSmsDTOTest {

    @Test
    public void getNullWhenBuildWithNoParamsTest() {
        LoteMensagemDTO loteMensagemDTO = BuildLoteMensagemDTO.build().getResult();
        assertNull(loteMensagemDTO.getKey());
        assertNull(loteMensagemDTO.getUrl());
        assertNull(loteMensagemDTO.getLstMensagemDTO());
    }

    @Test
    public void getEqualsWhenBuildWithKeyTest() {
        String key = "xyz1234";
        LoteMensagemDTO loteMensagemDTO = BuildLoteMensagemDTO
                .build()
                .setKey(key)
                .getResult();
        assertEquals(loteMensagemDTO.getKey(), key);
    }

    @Test
    public void getEqualsWhenBuildWithUrlTest() throws MalformedURLException {
        URL urlAddress = new URL("http://127.0.0.1/");
        LoteMensagemDTO loteMensagemDTO = BuildLoteMensagemDTO
                .build()
                .setUrl(urlAddress)
                .getResult();
        assertEquals(loteMensagemDTO.getUrl(), urlAddress);
    }

    @Test
    public void getEqualsWhenBuildWithLoteMensagemDTOTest() {
        MensagemSmsDTO mensagemDTO = new MensagemSmsDTO().setMsg("Teste");
        MensagemSmsDTO mensagemDTO2 = new MensagemSmsDTO().setMsg("Teste2");
        LoteMensagemDTO loteDTO = new LoteMensagemDTO().setLstMensagemDTO(Arrays.asList(mensagemDTO, mensagemDTO2));
        LoteMensagemDTO loteMensagemDTO = BuildLoteMensagemDTO
                .build()
                .setLoteMensagemDTO(loteDTO)
                .getResult();
        assertEquals(loteMensagemDTO.getLstMensagemDTO().get(0).getMsg(), mensagemDTO.getMsg());
        assertEquals(loteMensagemDTO.getLstMensagemDTO().get(1).getMsg(), mensagemDTO2.getMsg());
    }

    @Test
    public void getEqualsWhenBuildWithMensagemDTOTest() {
        MensagemSmsDTO mensagemDTO = new MensagemSmsDTO().setMsg("Teste");
        LoteMensagemDTO loteMensagemDTO = BuildLoteMensagemDTO
                .build()
                .setMensagemDTO(mensagemDTO)
                .getResult();
        assertEquals(loteMensagemDTO.getLstMensagemDTO().get(0).getMsg(), mensagemDTO.getMsg());
    }

}
