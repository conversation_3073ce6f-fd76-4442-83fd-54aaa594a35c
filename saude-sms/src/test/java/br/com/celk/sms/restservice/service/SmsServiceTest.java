package br.com.celk.sms.restservice.service;

import br.com.celk.sms.restservice.service.exception.BusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.net.URL;

import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({
        SystemService.class,
        SendMessageService.class
})
public class SmsServiceTest {

    private static final String URL = "http://127.0.0.1/";
    private static final String KEY = "XYZ1234";
    private static final String PHONE = "***********";
    private static final String MESSAGE = "Mensagem de Teste";

    @Mock
    SendMessageService sendMessageMock;

    @Before
    public void setUp() {
        mockStatic(SystemService.class);
        mockStatic(SendMessageService.class);
        when(SendMessageService.build()).thenReturn(sendMessageMock);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsProductionWithNullParamsInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(null, null);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsProductionWithNullUrlInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(null, KEY);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsProductionWithNullKeyInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), null);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsProductionWithEmptyKeyInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), "");
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsProductionWithDirtyKeyInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), " ");
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenEnvironmentIsNotProductionInBuildTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(false);
        SmsService.build(new URL(URL), KEY);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenPhoneIsNullInSendMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).sendMessage(null, MESSAGE);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenPhoneIsDirtyInSendMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).sendMessage(" ", MESSAGE);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenMessageIsDirtyInSendMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).sendMessage(PHONE, " ");
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenPhoneAndMessageIsNullInSendMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).sendMessage(null, null);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenLoteMensagemDTOIsNullInSendMessageLoteTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).sendMessageLote(null);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenMessageIdIsNullInReportMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).reportMessage(null);
    }

    @Test(expected = BusinessException.class)
    public void getErrorWhenMessageIdIsDirtyInReportMessageTest() throws Exception {
        when(SystemService.isProductionEnvironment()).thenReturn(true);
        SmsService.build(new URL(URL), KEY).reportMessage("");
    }

}
