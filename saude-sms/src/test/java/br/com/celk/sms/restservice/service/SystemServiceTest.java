package br.com.celk.sms.restservice.service;

import br.com.celk.sms.restservice.service.exception.BusinessException;
import org.junit.After;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

/**
 * <AUTHOR>
 */
public class SystemServiceTest {

    private static final String ENVIRONMENT = "ambiente";
    private static final String ENVIRONMENT_EMPTY = "";
    private static final String ENVIRONMENT_PRODUCTION = "producao";
    private static final String ENVIRONMENT_DEVELOPMENT = "desenvolvimento";
    private static final String MSG_ERROR_ENVIRONMENT = "A variável de ambiente não deve estar nula ou vazia.";

    private void setEmptyEnvironment() { System.setProperty(ENVIRONMENT, ENVIRONMENT_EMPTY); }
    private void setProductionEnvironment() { System.setProperty(ENVIRONMENT, ENVIRONMENT_PRODUCTION); }
    private void setDevelopmentEnvironment() { System.setProperty(ENVIRONMENT, ENVIRONMENT_DEVELOPMENT); }

    @After
    public void tearDown() {
        System.clearProperty(ENVIRONMENT);
    }

    @Test
    public void getErrorWhenEnvironmentIsNullTest() {
        try {
            SystemService.getEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getErrorWhenEnvironmentIsEmptyTest() {
        setEmptyEnvironment();
        try {
            SystemService.getEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getErrorWhenEnvironmentIsNullInProductionTest() {
        try {
            SystemService.isProductionEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getErrorWhenEnvironmentIsEmptyInProductionTest() {
        setEmptyEnvironment();
        try {
            SystemService.isProductionEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getErrorWhenEnvironmentIsNullInDevelopmentTest() {
        try {
            SystemService.isDevelopmentEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getErrorWhenEnvironmentIsEmptyInDevelopmentTest() {
        setEmptyEnvironment();
        try {
            SystemService.isDevelopmentEnvironment();
            fail();
        } catch (BusinessException e) {
            assertThat(e.getMessage(), is(MSG_ERROR_ENVIRONMENT));
        }
    }

    @Test
    public void getTrueWhenEnvironmentIsProductionInNotNullTest() throws BusinessException {
        setProductionEnvironment();
        assertNotNull(SystemService.getEnvironment());
    }

    @Test
    public void getTrueWhenEnvironmentIsDevelopmentInNotNullTest() throws BusinessException {
        setDevelopmentEnvironment();
        assertNotNull(SystemService.getEnvironment());
    }

    @Test
    public void getTrueWhenEnvironmentIsProductionInProductionTest() throws BusinessException {
        setProductionEnvironment();
        assertTrue(SystemService.isProductionEnvironment());
    }

    @Test
    public void getTrueWhenEnvironmentIsDevelopmentInDevelopmentTest() throws BusinessException {
        setDevelopmentEnvironment();
        assertTrue(SystemService.isDevelopmentEnvironment());
    }

    @Test
    public void getFalseWhenEnvironmentIsDevelopmentInProductionTest() throws BusinessException {
        setDevelopmentEnvironment();
        assertFalse(SystemService.isProductionEnvironment());
    }

    @Test
    public void getFalseWhenEnvironmentIsProductionInDevelopmentTest() throws BusinessException {
        setProductionEnvironment();
        assertFalse(SystemService.isDevelopmentEnvironment());
    }

}
