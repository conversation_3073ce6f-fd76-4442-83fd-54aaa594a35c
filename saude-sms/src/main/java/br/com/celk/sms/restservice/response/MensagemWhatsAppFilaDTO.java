package br.com.celk.sms.restservice.response;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class MensagemWhatsAppFilaDTO implements Serializable {

    private String name;

    private String data;

    private String hora;

    private String tipoConsulta;

    private String tipo;

    private String codigo;

    private String telefone;

    private String link;

    private String local;

    private String phone;

    private String reagendamentoData;

    private String reagendamentoHora;

    private String celkMessageId;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getReagendamentoData() {
        return reagendamentoData;
    }

    public void setReagendamentoData(String reagendamentoData) {
        this.reagendamentoData = reagendamentoData;
    }

    public String getReagendamentoHora() {
        return reagendamentoHora;
    }

    public void setReagendamentoHora(String reagendamentoHora) {
        this.reagendamentoHora = reagendamentoHora;
    }

    public String getCelkMessageId() {
        return celkMessageId;
    }

    public void setCelkMessageId(String celkMessageId) {
        this.celkMessageId = celkMessageId;
    }

    @Override
    public String toString() {
        return "MensagemWhatsAppFilaDTO{" +
                "name='" + name + '\'' +
                ", data='" + data + '\'' +
                ", hora='" + hora + '\'' +
                ", tipoConsulta='" + tipoConsulta + '\'' +
                ", tipo='" + tipo + '\'' +
                ", codigo='" + codigo + '\'' +
                ", telefone='" + telefone + '\'' +
                ", link='" + link + '\'' +
                ", local='" + local + '\'' +
                ", phone='" + phone + '\'' +
                ", reagendamentoData='" + reagendamentoData + '\'' +
                ", reagendamentoHora='" + reagendamentoHora + '\'' +
                ", celkMessageId='" + celkMessageId + '\'' +
                '}';
    }
}
