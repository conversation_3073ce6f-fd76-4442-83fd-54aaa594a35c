package br.com.celk.sms.restservice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DTOReceivedSMSResponseData {

    private String from;
    private String to;
    private String message;
    @JsonProperty(value = "date_receive")
    private String dateReceive;
    @JsonProperty(value = "integration_id")
    private String integrationId;
    @JsonProperty(value = "correlation_id")
    private String correlationId;
    private String id;
    @JsonProperty(value = "isread")
    private boolean isRead;

    public DTOReceivedSMSResponseData() {}

    public DTOReceivedSMSResponseData(String from, String to, String message, String dateReceive, String integrationId, String correlationId, String id, boolean isRead) {
        this.from = from;
        this.to = to;
        this.message = message;
        this.dateReceive = dateReceive;
        this.integrationId = integrationId;
        this.correlationId = correlationId;
        this.id = id;
        this.isRead = isRead;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDateReceive() {
        return dateReceive;
    }

    public void setDateReceive(String dateReceive) {
        this.dateReceive = dateReceive;
    }

    public String getIntegrationId() {
        return integrationId;
    }

    public void setIntegrationId(String integrationId) {
        this.integrationId = integrationId;
    }

    public String getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(boolean read) {
        isRead = read;
    }

    @Override
    public String toString() {
        return "DTOReceivedSMSResponseData{" +
                "from='" + from + '\'' +
                ", to='" + to + '\'' +
                ", message='" + message + '\'' +
                ", dateReceive='" + dateReceive + '\'' +
                ", integrationId='" + integrationId + '\'' +
                ", correlationId='" + correlationId + '\'' +
                ", id='" + id + '\'' +
                ", isRead=" + isRead +
                '}';
    }
}