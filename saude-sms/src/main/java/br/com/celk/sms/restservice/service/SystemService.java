package br.com.celk.sms.restservice.service;

import br.com.celk.sms.restservice.service.exception.BusinessException;

/**
 * <AUTHOR>
 */
public class SystemService {

    private static final String ENVIRONMENT = "ambiente";
    private static final String ENVIRONMENT_EMPTY = "";
    private static final String ENVIRONMENT_PRODUCTION = "producao";
    private static final String ENVIRONMENT_DEVELOPMENT = "desenvolvimento";
    private static final String MSG_ERROR_ENVIRONMENT = "A variável de ambiente não deve estar nula ou vazia.";

    private SystemService() {}

    public static String getEnvironment() throws BusinessException {
        String result;
        try {
            result = System.getProperty(ENVIRONMENT);
            if (ENVIRONMENT_EMPTY.equals(result) || result == null) {
                throw new BusinessException();
            }
        } catch (Exception e) {
            throw new BusinessException(MSG_ERROR_ENVIRONMENT);
        }
        return result;
    }

    public static Boolean isProductionEnvironment() throws BusinessException {
        return ENVIRONMENT_PRODUCTION.equals(getEnvironment());
    }

    public static Boolean isDevelopmentEnvironment() throws BusinessException {
        return ENVIRONMENT_DEVELOPMENT.equals(getEnvironment());
    }

}
