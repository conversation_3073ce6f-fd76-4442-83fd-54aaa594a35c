package br.com.celk.sms.restservice.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class MensagemWhatsAppDTO implements Serializable {

    private String nomeSocial;
    private Long codigoUsuarioCadsus;
    private String urlComprovanteAgendamento;
    private String localAgendamento;
    private String dataAgendamento;
    private String horaAgendamento;
    private String tipoProcedimento;
    private String chaveValidacao;
    private String agendaRemanejadoData;
    private String agendaRemanejadoHora;


    public String getAgendaRemanejadoData() {
        return agendaRemanejadoData;
    }

    public void setAgendaRemanejadoData(String agendaRemanejadoData) {
        this.agendaRemanejadoData = agendaRemanejadoData;
    }

    public String getAgendaRemanejadoHora() {
        return agendaRemanejadoHora;
    }

    public void setAgendaRemanejadoHora(String agendaRemanejadoHora) {
        this.agendaRemanejadoHora = agendaRemanejadoHora;
    }

    public String getChaveValidacao() {
        return chaveValidacao;
    }

    public void setChaveValidacao(String chaveValidacao) {
        this.chaveValidacao = chaveValidacao;
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getHoraAgendamento() {
        return horaAgendamento;
    }

    public void setHoraAgendamento(String horaAgendamento) {
        this.horaAgendamento = horaAgendamento;
    }

    public String getLocalAgendamento() {
        return localAgendamento;
    }

    public void setLocalAgendamento(String localAgendamento) {
        this.localAgendamento = localAgendamento;
    }

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    public String getNomeSocial() {
        return nomeSocial;
    }

    public void setNomeSocial(String nomeSocial) {
        this.nomeSocial = nomeSocial;
    }

    public String getUrlComprovanteAgendamento() {
        return urlComprovanteAgendamento;
    }

    public void setUrlComprovanteAgendamento(String urlComprovanteAgendamento) {
        this.urlComprovanteAgendamento = urlComprovanteAgendamento;
    }
}
