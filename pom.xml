<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>br.com.celk</groupId>
    <artifactId>saude-2</artifactId>
    <packaging>pom</packaging>
    <version>3.1.285.1-SNAPSHOT</version>
    <name>saude-2</name>
    <url>https://www.celk.com.br/</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.jboss.spec.javaee.7.0>1.0.0.Final</version.jboss.spec.javaee.7.0>
        <version.org.jboss.resteasy>3.0.11.Final</version.org.jboss.resteasy>
        <apache.camel.version>2.12.3</apache.camel.version>
        <wicket.version>6.9.0</wicket.version>
        <aws.sdk.version>1.11.769</aws.sdk.version>
        <wicketstuff-gmap3.version>6.24.0</wicketstuff-gmap3.version>
        <commons-lang.version>2.6</commons-lang.version>
        <javax.mail.version>1.4.5-redhat-1</javax.mail.version>
        <hibernate.version>4.3.5.Final</hibernate.version>
        <powermock.version>2.0.2</powermock.version>

        <certificado.keypass>ksi2014</certificado.keypass>
        <certificado.storepass>ksi2014</certificado.storepass>
        <certificado.alias>codesigncert</certificado.alias>
        <certificado.storetype>jks</certificado.storetype>

        <!--<build.type>development</build.type>-->
        <infinispan.version>7.2.3.Final</infinispan.version>
        <surefire-plugin.version>3.0.0-M5</surefire-plugin.version>
        <jacoco-plugin.version>0.8.5</jacoco-plugin.version>
        <cobertura.maxmem>712M</cobertura.maxmem>
        <sonar-plugin.version>3.6.0.1398</sonar-plugin.version>
        <sonar.coverage.exclusions>
            **/*model/**/*.java,
            **/*dto/**/*.java,
            **/*vo/**/*.java,
            **/*config/**/*.java
        </sonar.coverage.exclusions>
        <sonar.cpd.exclusions>
            **/*model/**/*.java,
            **/*dto/**/*.java,
            **/*vo/**/*.java,
            **/*config/**/*.java
            **/*Query*.java
        </sonar.cpd.exclusions>
        <sonar.exclusions>
            **/*.css,
            **/*.html,
            **/*.xml,
            **/*.js,
            **/*WSSTub.java,
        </sonar.exclusions>
        <sonar.issue.ignore.multicriteria>basestatic</sonar.issue.ignore.multicriteria>
        <sonar.issue.ignore.multicriteria.basestatic.ruleKey>java:S3252</sonar.issue.ignore.multicriteria.basestatic.ruleKey>
        <sonar.issue.ignore.multicriteria.basestatic.resourceKey>**/*.java</sonar.issue.ignore.multicriteria.basestatic.resourceKey>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>3.0.0-M2</version>
                    <configuration>
                        <releaseProfiles>release</releaseProfiles>
                        <branchName>b_${project.version}</branchName>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0-M1</version>
                    <executions>
                        <execution>
                            <id>default-deploy</id>
                            <phase>deploy</phase>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <encoding>UTF-8</encoding>
			<useIncrementalCompilation>false</useIncrementalCompilation>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.zeroturnaround</groupId>
                    <artifactId>jrebel-maven-plugin</artifactId>
                    <version>1.1.10</version>
                    <executions>
                        <execution>
                            <id>generate-rebel-xml</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>generate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                            </manifest>
                            <manifestEntries>
                                <Application-Name>${application-name}</Application-Name>
                                <Permissions>all-permissions</Permissions>
                                <Codebase>*</Codebase>
                                <Caller-Allowable-Codebase>*</Caller-Allowable-Codebase>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ejb-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <ejbVersion>3.0</ejbVersion>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>

                <plugin>
                    <groupId>org.jboss.as.plugins</groupId>
                    <artifactId>jboss-as-maven-plugin</artifactId>
                    <version>7.9.Final</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jarsigner-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.9.1</version>
                    <configuration>
                        <locales>pt_BR</locales>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire-plugin.version}</version>
                <configuration>
                    <systemProperties>
                        <java.util.logging.manager>org.jboss.logmanager.LogManager</java.util.logging.manager>
                    </systemProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <!-- jboss-javaee import -->
            <dependency>
                <groupId>org.jboss.spec</groupId>
                <artifactId>jboss-javaee-7.0</artifactId>
                <version>${version.jboss.spec.javaee.7.0}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Celk modules -->
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-connect</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-command</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-ejb-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-wicket-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-indra-connect</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-provider</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-core</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
                <exclusions>
                    <exclusion>
                        <artifactId>postgresql</artifactId>
                        <groupId>org.postgresql</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-utils</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-bo-ejb</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-sistema-healthcheck</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-consumer-ejb</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-web-wicket</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-processos-service</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-app-cidadao-service</artifactId>
                <version>${project.version}</version>
                <type>war</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-integrating-modules</artifactId>
                <version>${project.version}</version>
                <type>ejb</type>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>saude-sms</artifactId>
                <version>${project.version}</version>
                <type>jar</type>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Celk libs -->
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>horus</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>nfe</artifactId>
                <version>4.0</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>celkged</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk.nfe</groupId>
                <artifactId>nfe</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk.tiss</groupId>
                <artifactId>celk-tiss</artifactId>
                <version>030201</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk.horus</groupId>
                <artifactId>celk-horus</artifactId>
                <version>082013.2</version>
            </dependency>
            <dependency>
                <groupId>br.com.ksisolucoes.saude</groupId>
                <artifactId>gem-cadsus</artifactId>
                <version>001</version>
            </dependency>

            <!-- -->
            <dependency>
                <groupId>javax</groupId>
                <artifactId>javaee-web-api</artifactId>
                <version>7.0</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-multipart-provider</artifactId>
                <version>${version.org.jboss.resteasy}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.spec.javax.resource</groupId>
                <artifactId>jboss-connector-api_1.6_spec</artifactId>
                <version>1.0.1.Final-redhat-2</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>xml-apis</artifactId>
                        <groupId>xml-apis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-polly</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-dynamodb</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.12</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.6.0</version>
            </dependency>

            <dependency>
                <groupId>jboss</groupId>
                <artifactId>jboss-ejb3-ext-api</artifactId>
                <version>0.1</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>9.3-1103-jdbc3</version>
            </dependency>

            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${version.org.jboss.resteasy}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-httpclient</groupId>
                        <artifactId>commons-httpclient</artifactId>
                    </exclusion>
                </exclusions>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-sns</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-sqs</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>amazon-sqs-java-messaging-lib</artifactId>
                <version>1.0.8.6</version>
            </dependency>        
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-core</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>other</groupId>
                <artifactId>jaybird-full</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>other</groupId>
                <artifactId>xbean</artifactId>
                <version>0.1</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.jboss.as</groupId>
                <artifactId>jboss-as-messaging</artifactId>
                <version>7.2.0.Final-redhat-8</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis-jaxrpc</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>br.gov.saude</groupId>
                <artifactId>sisprenatalws</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-client</artifactId>
                <version>3.0.11.Final</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>19.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>2.2.7</version>
            </dependency>

            <dependency>
                <groupId>br.com.celk.integracao.laboratorio</groupId>
                <artifactId>saude-integ-laboratorio-connect</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>

            <!-- //////////////////////JASPER/////////////////////////// -->
            <dependency>
                <groupId>org.jfree</groupId>
                <artifactId>jfreechart</artifactId>
                <version>1.0.19</version>
            </dependency>
            <dependency>
                <groupId>commons-digester</groupId>
                <artifactId>commons-digester</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk.jasper</groupId>
                <artifactId>commons-javaflow</artifactId>
                <version>20060411</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>

            <dependency>
                <groupId>br.com.celk.jasper</groupId>
                <artifactId>jasperreports-javaflow</artifactId>
                <version>5.0.1</version>
            </dependency>

            <dependency>
                <groupId>br.com.celk.jasper</groupId>
                <artifactId>jasperreports-fonts</artifactId>
                <version>3.7.6</version>
            </dependency>

            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-awt-util</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-bridge</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-css</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-dom</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-gvt</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-script</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-util</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>xml-apis-ext</artifactId>
                <version>1.3.04</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-svg-dom</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-ext</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-xml</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-parser</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>jasper</groupId>
                <artifactId>batik-anim</artifactId>
                <version>1.7</version>
            </dependency>

            <dependency>
                <groupId>net.sf.barcode4j</groupId>
                <artifactId>barcode4j</artifactId>
                <version>2.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>ant</artifactId>
                        <groupId>org.apache.ant</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-cli</artifactId>
                        <groupId>commons-cli</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- /////////////////////////////////////////////////////// -->
            <dependency>
                <groupId>commons</groupId>
                <artifactId>i18n</artifactId>
                <version>0.1</version>
            </dependency>
            <dependency>
                <artifactId>commons-collections</artifactId>
                <groupId>commons-collections</groupId>
                <version>3.2.1</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>jasper</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>1.2.13</version>
            </dependency>
            <dependency>
                <groupId>com.ibm.icu</groupId>
                <artifactId>icu4j</artifactId>
                <version>68.1</version>
            </dependency>
            <dependency>
                <groupId>other</groupId>
                <artifactId>josql</artifactId>
                <version>2.2</version>
            </dependency>

            <dependency>
                <groupId>org.picketbox</groupId>
                <artifactId>picketbox</artifactId>
                <version>4.0.7.Final</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>bsf</groupId>
                <artifactId>bsf</artifactId>
                <version>2.4.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.7</version>
            </dependency>
            <dependency>
                <groupId>web</groupId>
                <artifactId>jsf-api</artifactId>
                <version>2.1.2</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.53</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jzlib</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>rhino</groupId>
                <artifactId>js</artifactId>
                <version>1.7R1</version>
            </dependency>
            <dependency>
                <groupId>br.com.ksisolucoes</groupId>
                <artifactId>integracao-SIGTAP</artifactId>
                <version>0.1.2</version>
            </dependency>
            <dependency>
                <groupId>other</groupId>
                <artifactId>gentlyweb-utils</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>com.linuxense</groupId>
                <artifactId>javadbf</artifactId>
                <version>0.4.0</version>
            </dependency>
            <dependency>
                <artifactId>spring-core</artifactId>
                <groupId>org.springframework</groupId>
                <version>3.0.6.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.lambdaj</groupId>
                <artifactId>lambdaj</artifactId>
                <version>2.3.3</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.lambdaj</groupId>
                <artifactId>lambdaj</artifactId>
                <version>2.3.3</version>
                <classifier>javadoc</classifier>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>

            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-hibernate4</artifactId>
                <version>2.2.3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.2.3</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>jnlp</artifactId>
                <version>1.7.45</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-core</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-csv</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-bindy</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache-extras.camel-extra</groupId>
                <artifactId>camel-jboss6</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-stream</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.camel</groupId>
                <artifactId>camel-ejb</artifactId>
                <version>${apache.camel.version}</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk</groupId>
                <artifactId>ziputils</artifactId>
                <version>1.2</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.jrimum</groupId>
                <artifactId>jrimum-bopepo</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.jrimum</groupId>
                <artifactId>jrimum-domkee</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.1</version>
            </dependency>
            <dependency>
                <groupId>org.jrimum</groupId>
                <artifactId>jrimum-vallia</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.jrimum</groupId>
                <artifactId>jrimum-utilix</artifactId>
                <version>0.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.jrimum</groupId>
                <artifactId>jrimum-texgit</artifactId>
                <version>0.2.0</version>
            </dependency>

            <!-- ############################# -->
            <!-- DEPENDENCIAS PARA A API PDFBOX -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.57</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.57</version>
            </dependency>
            <!-- ############################# -->
            <!-- Begin - FCM client sender for mobile push notifications-->
            <dependency>
                <groupId>de.bytefish.fcmjava</groupId>
                <artifactId>fcmjava-core-java7</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>de.bytefish.fcmjava</groupId>
                <artifactId>fcmjava-client-java7</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.6</version>
            </dependency>
            <!-- End - FCM client sender for mobile push notifications-->

            <!-- Begin CADSUS PIX/PDQ -->
            <dependency>
                <groupId>br.com.celk.cadsus_v5</groupId>
                <artifactId>cadsus_v5</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>br.com.celk.cadsus_pixpdq</groupId>
                <artifactId>cadsus_pixpdq</artifactId>
                <version>2.0.0</version>
            </dependency>
            <!-- End CADSUS PIX/PDQ -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.wicketstuff</groupId>
                <artifactId>wicketstuff-gmap3</artifactId>
                <version>${wicketstuff-gmap3.version}</version>
            </dependency>

            <dependency>
                <groupId>br.com.celk.boleto</groupId>
                <artifactId>integracao</artifactId>
                <version>1.1.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jboss.resteasy</groupId>
                        <artifactId>resteasy-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>br.com.celk.boleto</groupId>
                <artifactId>common</artifactId>
                <version>1.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- lib de criptografia -->
            <dependency>
                <groupId>org.jasypt</groupId>
                <artifactId>jasypt</artifactId>
                <version>1.9.3</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>2.9.8</version>
            </dependency>

            <dependency>
                <groupId>org.jboss</groupId>
                <artifactId>jboss-ejb-client</artifactId>
                <version>1.0.21.Final-redhat-1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jboss.xnio</groupId>
                        <artifactId>xnio-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jboss.marshalling</groupId>
                        <artifactId>jboss-marshalling</artifactId>
                    </exclusion>
                </exclusions>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-adb</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-local</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-http</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.2.2</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.8.2</version>
            </dependency>
            <dependency>
                <groupId>de.jollyday</groupId>
                <artifactId>jollyday</artifactId>
                <version>0.4.6</version>
            </dependency>
            <dependency>
                <groupId>net.objectlab.kit</groupId>
                <artifactId>datecalc-common</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>net.objectlab.kit</groupId>
                <artifactId>datecalc-joda</artifactId>
                <version>1.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>1.3</version>
            </dependency>

            <!--  WICKET DEPENDENCIES -->
            <dependency>
                <artifactId>wicket-core</artifactId>
                <groupId>org.apache.wicket</groupId>
                <version>${wicket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.odlabs.wiquery</groupId>
                <artifactId>wiquery-jquery-ui</artifactId>
                <version>6.6.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wicket-core</artifactId>
                        <groupId>org.apache.wicket</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.wicket</groupId>
                <artifactId>wicket-extensions</artifactId>
                <version>${wicket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.wicketstuff</groupId>
                <artifactId>wicketstuff-openlayers</artifactId>
                <version>${wicket.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.wicket</groupId>
                <artifactId>wicket-devutils</artifactId>
                <version>${wicket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.wicketstuff</groupId>
                <artifactId>wicketstuff-tinymce</artifactId>
                <version>${wicket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.wicketstuff</groupId>
                <artifactId>wicketstuff-annotation</artifactId>
                <version>${wicket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- LOGGING DEPENDENCIES - LOG4J -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>1.6.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- CELK DEPENDENCIES            -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.1</version>
            </dependency>

            <dependency>
                <groupId>org.wicketstuff</groupId>
                <artifactId>wicketstuff-html5</artifactId>
                <version>${wicket.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jvnet.sorcerer</groupId>
                <artifactId>sorcerer-javac</artifactId>
                <version>0.8</version>
            </dependency>

            <!-- -->


            <dependency>
                <groupId>org.jboss.logmanager</groupId>
                <artifactId>log4j-jboss-logmanager</artifactId>
                <version>1.0.1.Final-redhat-2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jboss-logmanager</artifactId>
                        <groupId>org.jboss.logmanager</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.googlecode.wicked-charts</groupId>
                <artifactId>wicked-charts-wicket6</artifactId>
                <version>1.5.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>saude-wicket-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>saude-web-processors</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.8.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-commons</artifactId>
                <version>${infinispan.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-core</artifactId>
                <version>${infinispan.version}</version>
                <scope>provided</scope>
            </dependency>

			<dependency>
				<groupId>org.assertj</groupId>
				<artifactId>assertj-core</artifactId>
				<version>2.9.1</version>
				<scope>test</scope>
			</dependency>
            
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>saude-utils</module>
        <module>saude-provider</module>
        <module>saude-core</module>
        <module>saude-ejb-utils</module>
        <module>saude-integ-laboratorio</module>
        <module>saude-connect</module>
        <module>saude-indra-connect</module>
        <module>saude-command</module>
        <module>saude-consumer-ejb</module>
        <module>saude-integrating-modules</module>
        <module>saude-sms</module>
        <module>saude-bo-ejb</module>
        <module>saude-wicket</module>
        <module>saude-web-wicket</module>
        <module>saude-app-cidadao-service</module>
        <module>saude-sistema-healthcheck</module>
        <module>saude-processos-service</module>
        <module>saude-ear</module>
        <module>saude-ear-processo</module>
        <module>saude-ear-sistema</module>
    </modules>

    <scm>
        <tag>saude-2-3.1.4.5</tag>
        <connection>
            scm:git:****************************************/celk/saude-server.git
        </connection>
        <developerConnection>
            scm:git:****************************************/celk/saude-server.git
        </developerConnection>
    </scm>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://192.168.0.110:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://192.168.0.110:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>celk</id>
        </profile>

        <profile>
            <id>integration-tests</id>
            <modules>
                <module>saude-integration-tests</module>
            </modules>
        </profile>
    </profiles>
</project>
