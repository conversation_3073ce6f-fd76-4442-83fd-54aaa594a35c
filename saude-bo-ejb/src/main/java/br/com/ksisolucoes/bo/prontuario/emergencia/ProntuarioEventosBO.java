package br.com.ksisolucoes.bo.prontuario.emergencia;

import java.io.File;
import java.io.Serializable;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.integracao.sisreg.ExportarSisreg;
import br.com.ksisolucoes.bo.integracao.sigtap.ImportacaoRegistroSigtapAgendadorProcesso;
import br.com.ksisolucoes.bo.integracao.sigtap.ImportarRegistrosSigtap;
import br.com.ksisolucoes.bo.prontuario.emergencia.prontuarioeventos.DeleteProntuarioEventos;
import br.com.ksisolucoes.bo.prontuario.emergencia.prontuarioeventos.SaveProntuarioEventos;
import br.com.ksisolucoes.bo.prontuario.emergencia.interfaces.facade.*;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.emergencia.ProntuarioEventos;

/**
 * <AUTHOR> G<PERSON>dani
 *
 */
import javax.ejb.Stateless;
import org.jboss.ejb3.annotation.TransactionTimeout;

@Stateless 
public class ProntuarioEventosBO extends BOGenericImpl implements _ProntuarioEventosFacadeLocal, _ProntuarioEventosFacadeRemote {

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteProntuarioEventos(vo));
    }

    /**
     * {@inheritDoc}
     */
    @TransactionTimeout(18000)
    public void importarRegistrosSigtap(String path) throws DAOException, ValidacaoException {
        this.executor.execute(new ImportarRegistrosSigtap(path));
    }
    
    @Override
    public void importarRegistrosSigtapAgendadorProcesso() throws DAOException, ValidacaoException {
        this.executorAsync.execute(new ImportacaoRegistroSigtapAgendadorProcesso());
    }

    @Override
    public File exportarSisreg(String url, String usuario, String senha, String dados) throws ValidacaoException, DAOException {
        return ((ExportarSisreg) this.executor.executeReturn(new ExportarSisreg(url, usuario, senha, dados))).getFile();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new SaveProntuarioEventos(vo));
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Class getReferenceClass() {
        return ProntuarioEventos.class;
    }

    /**
     * {@inheritDoc}
     */
}
