package br.com.ksisolucoes.bo.cadsus;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade._UsuarioCadsusDocumentoFacadeLocal;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade._UsuarioCadsusDocumentoFacadeRemote;
import br.com.ksisolucoes.bo.cadsus.usuariocadsusdocumento.*;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;

import javax.ejb.Stateless;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 */

@Stateless
public class UsuarioCadsusDocumentoBO extends BOGenericImpl implements _UsuarioCadsusDocumentoFacadeLocal, _UsuarioCadsusDocumentoFacadeRemote {

    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteUsuarioCadsusDocumento(vo));
    }

    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new SaveUsuarioCadsusDocumento((UsuarioCadsusDocumento) vo));
        return null;
    }

    @Override
    public final Class getReferenceClass() {
        return UsuarioCadsusDocumento.class;
    }

    public void excluirDocumentos(UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
        this.executor.execute(new ExcluirUsuarioCadsusDocumento(usuarioCadsus));
    }

    public void salvarDocumentos(List<UsuarioCadsusDocumento> listaDocumentos, UsuarioCadsus usuarioCadsus, List<Long> tipoDocumentos) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarUsuarioCadsusDocumento(listaDocumentos, usuarioCadsus, tipoDocumentos));
    }

    @Override
    public void salvarDocumentosImportacao(UsuarioCadsusDocumento usuarioCadsusDocumento, UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarUsuarioCadsusDocumentoImportacao(usuarioCadsusDocumento, usuarioCadsus));
    }


}
