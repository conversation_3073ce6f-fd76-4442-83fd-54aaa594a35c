package br.com.ksisolucoes.bo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.basico.empresamaterial.DeleteEmpresaMaterial;
import br.com.ksisolucoes.bo.basico.empresamaterial.SaveEmpresaMaterial;
import br.com.ksisolucoes.bo.basico.empresamaterial.UpdateFragmentEmpresaMaterial;
import br.com.ksisolucoes.bo.basico.interfaces.facade._EmpresaMaterialFacadeLocal;
import br.com.ksisolucoes.bo.basico.interfaces.facade._EmpresaMaterialFacadeRemote;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import java.util.Map;
import javax.ejb.Stateless;

/**
 * <AUTHOR>
 * <AUTHOR>
 *
 */
@Stateless 
public class EmpresaMaterialBO extends BOGenericImpl implements _EmpresaMaterialFacadeLocal, _EmpresaMaterialFacadeRemote {

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteEmpresaMaterial(vo));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new SaveEmpresaMaterial(vo));
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Class getReferenceClass() {
        return EmpresaMaterial.class;
    }

    public void update(Map<String, Object> props, Serializable id) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateFragmentEmpresaMaterial(props, new EmpresaMaterial((Long) id)));
    }

    public void update(String prop, Object value, Serializable id) throws DAOException, ValidacaoException {
        this.executor.execute(new UpdateFragmentEmpresaMaterial(prop, value, (Long) id));
    }
}
