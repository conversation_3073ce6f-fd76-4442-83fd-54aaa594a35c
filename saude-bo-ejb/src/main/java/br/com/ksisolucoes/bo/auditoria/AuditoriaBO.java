package br.com.ksisolucoes.bo.auditoria;

import java.io.Serializable;
import java.util.List;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.auditoria.auditoria.QueryCarregarAtendimentoAuditoriaProntuario;
import br.com.ksisolucoes.bo.auditoria.auditoria.CarregarAuditoriaProntuario;
import br.com.ksisolucoes.bo.auditoria.auditoria.DeleteAuditoria;
import br.com.ksisolucoes.bo.auditoria.auditoria.LoadFromXml;
import br.com.ksisolucoes.bo.auditoria.auditoria.SaveAuditoria;
import br.com.ksisolucoes.bo.auditoria.interfaces.facade.*;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.AuditoriaProntuarioDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.AuditoriaProntuarioDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.auditoria.Auditoria;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 * <AUTHOR> Giordani
 *
 */
import javax.ejb.Stateless;

@Stateless
public class AuditoriaBO extends BOGenericImpl implements _AuditoriaFacadeLocal, _AuditoriaFacadeRemote {

    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteAuditoria(vo));
    }

    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new SaveAuditoria(vo));
        return null;
    }

    @Override
    public final Class getReferenceClass() {
        return Auditoria.class;
    }

    @Override
    public List<Auditoria> loadFromXml(List<Auditoria> auditoriaList) throws SGKException {
        LoadFromXml loadFromXml = (LoadFromXml) this.executor.executeReturn(new LoadFromXml(auditoriaList));
        return loadFromXml.getAuditoriaList();
    }

    @Override
    public Auditoria loadFromXml(Auditoria auditoria) throws SGKException {
        LoadFromXml loadFromXml = (LoadFromXml) this.executor.executeReturn(new LoadFromXml(auditoria));
        return loadFromXml.getAuditoriaList().get(0);
    }

    @Override
    public AuditoriaProntuarioDTO carregarAuditoriaProntuario(Long numeroAtendimento) throws DAOException, ValidacaoException {
        return ((CarregarAuditoriaProntuario) this.executor.executeReturn(new CarregarAuditoriaProntuario(numeroAtendimento))).getAuditoriaProntuarioDTO();
    }
    
    @Override
    public List<Atendimento> carregarAtendimentoAuditoriaProntuario(AuditoriaProntuarioDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryCarregarAtendimentoAuditoriaProntuario) this.executor.executeReturn(new QueryCarregarAtendimentoAuditoriaProntuario(param))).getResult();
    }
}