package br.com.ksisolucoes.bo.vigilancia;

import br.com.celk.report.HtmlReport;
import br.com.celk.report.vigilancia.RelatorioDenuncias;
import br.com.celk.report.vigilancia.autoinfracao.ImpressaoComprovanteAutoInfracao;
import br.com.celk.report.vigilancia.autoinfracao.RelatorioAutoInfracao;
import br.com.celk.report.vigilancia.autoinfracao.dto.RelatorioAutoInfracaoDTOParam;
import br.com.celk.report.vigilancia.autointimacao.ImpressaoComprovanteAutoIntimacao;
import br.com.celk.report.vigilancia.autointimacao.RelatorioAutoIntimacao;
import br.com.celk.report.vigilancia.autointimacao.dto.RelatorioAutoIntimacaoDTOParam;
import br.com.celk.report.vigilancia.automulta.ImpressaoComprovanteAutoMulta;
import br.com.celk.report.vigilancia.automulta.RelatorioAutoMulta;
import br.com.celk.report.vigilancia.automulta.dto.RelatorioAutoMultaDTOParam;
import br.com.celk.report.vigilancia.autopenalidade.ImpressaoComprovanteAutoPenalidade;
import br.com.celk.report.vigilancia.autopenalidade.RelatorioAutoPenalidade;
import br.com.celk.report.vigilancia.autopenalidade.dto.RelatorioAutoPenalidadeDTOParam;
import br.com.celk.report.vigilancia.cva.RelatorioImpressaoFichaCvaAnimal;
import br.com.celk.report.vigilancia.cva.RelatorioRelacaoMicrochipagem;
import br.com.celk.report.vigilancia.cva.RelatorioTermoAdocao;
import br.com.celk.report.vigilancia.cva.RelatorioTermoResponsabilidade;
import br.com.celk.report.vigilancia.cva.dto.RelatorioRelacaoMicrochipagemDTOParam;
import br.com.celk.report.vigilancia.declaratorios.AbstractReportPdfCarimboPrancha;
import br.com.celk.report.vigilancia.dengue.registrodiarioantivetorial.RelatorioProducaoServicoAntivetorial;
import br.com.celk.report.vigilancia.dto.RelatorioDenunciasDTOParam;
import br.com.celk.report.vigilancia.populacaocaesgatos.RelatorioPopulacaoCaesGatos;
import br.com.celk.report.vigilancia.processoadministrativo.ImpressaoDecisaoProcessoAdministrativo;
import br.com.celk.report.vigilancia.processoadministrativo.ImpressaoParecerProcessoAdministrativo;
import br.com.celk.report.vigilancia.processoadministrativo.RelatorioProcessoAdministrativo;
import br.com.celk.report.vigilancia.processoadministrativo.RelatorioProcessoAdministrativoDTOParam;
import br.com.celk.report.vigilancia.profissional.ImpressaoProfissionalParaReceita;
import br.com.celk.report.vigilancia.registrodiarioantivetorial.dto.RelatorioProducaoServicoAntivetorialDTOParam;
import br.com.celk.report.vigilancia.requerimento.*;
import br.com.celk.report.vigilancia.requerimento.query.QueryImpressaoParecerRequerimento;
import br.com.celk.report.vigilancia.roteiroinspecao.ImpressaoRoteiroInspecao;
import br.com.celk.report.vigilancia.roteiroinspecao.ImpressaoRoteiroInspecaoExterno;
import br.com.celk.report.vigilancia.tempoatendimento.RelatorioTempoAtendimento;
import br.com.celk.report.vigilancia.tempoatendimento.dto.RelatorioTempoAtendimentoDTOParam;
import br.com.celk.vigilancia.dto.*;
import br.com.celk.report.vigilancia.termoajustamentoconduta.ImpressaoComprovanteTermoAjustamentoConduta;
import br.com.celk.vigilancia.dto.ImpressaoParecerRequerimentoDTOParam;
import br.com.celk.vigilancia.dto.RelatorioRelacaoAtividadeVeterinariaDTOParam;
import br.com.celk.vigilancia.dto.RelatorioResumoAtividadeVeterinariaDTOParam;
import br.com.celk.vigilancia.dto.RelatorioSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.vigilancia.financeiro.GerarBoletoVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.enumeration.TipoImpressaoVigilancia;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade._VigilanciaReportFacadeLocal;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade._VigilanciaReportFacadeRemote;
import br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia.GerarAlvaraProvisorio;
import br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia.GerarLicencaSanitariaProvisoria;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.RelatorioImprimirBaixaResponsabilidadeAlvaraInicial;
import br.com.ksisolucoes.report.prontuario.basico.RelatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.PdfCarimboPranchaDTOParam;
import br.com.ksisolucoes.report.vigilancia.*;
import br.com.ksisolucoes.report.vigilancia.cva.RelatorioRelacaoAtividadeVeterinaria;
import br.com.ksisolucoes.report.vigilancia.cva.RelatorioSolicitacaoAgendadaCva;
import br.com.ksisolucoes.report.vigilancia.cva.RelatorioSolicitacaoPendenteCva;
import br.com.ksisolucoes.report.vigilancia.dengue.RelatorioArmadilha;
import br.com.ksisolucoes.report.vigilancia.dengue.RelatorioPontosEstrategicos;
import br.com.ksisolucoes.report.vigilancia.dengue.RelatorioVisitaArmadilha;
import br.com.ksisolucoes.report.vigilancia.dengue.ResumoVisitaArmadilha;
import br.com.ksisolucoes.report.vigilancia.dengue.armadilha.dto.RelatorioArmadilhaDTOParam;
import br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto.RelatorioVisitaArmadilhaDTOParam;
import br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto.ResumoVisitaArmadilhaDTOParam;
import br.com.ksisolucoes.report.vigilancia.dengue.pontosestrategicos.dto.RelatorioPontosEstrategicosDTOParam;
import br.com.ksisolucoes.report.vigilancia.financeiro.ImpressaoMemorando;
import br.com.ksisolucoes.report.vigilancia.financeiro.RelatorioRelacaoFinanceiro;
import br.com.ksisolucoes.report.vigilancia.query.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.cva.animal.CvaAnimal;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;

import javax.ejb.Stateless;
import java.io.File;
import java.util.List;

@Stateless
public class VigilanciaReportBO extends BOGenericImpl implements _VigilanciaReportFacadeLocal, _VigilanciaReportFacadeRemote {

    @Override
    public List<IReport> documentosRequerimento(RequerimentoVigilancia requerimentoVigilancia, TipoImpressaoVigilancia tipoImpressaoVigilancia) throws DAOException, ValidacaoException {
        return ((DocumentosRequerimentoVigilancia) this.executor.executeReturn(new DocumentosRequerimentoVigilancia(requerimentoVigilancia, tipoImpressaoVigilancia))).getDocumentos();
    }

    @Override
    public void enviarImpressaoDocumentosProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarArquivoProcessoAdministrativoEnviarMensagemInterna(processoAdministrativo));
    }

    @Override
    public void enviarImpressaoDocumentosProcessoAdministrativoUsuarioExterno(ProcessoAdministrativo processoAdministrativo) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarArquivoProcessoAdministrativoEnviarUsuarioExterno(processoAdministrativo));
    }

    @Override
    public void enviarArquivoDocumentosRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarArquivoProcessoAdministrativoEnviarMensagemInterna(requerimentoVigilancia));
    }

    @Override
    public List<ImpressaoConsultaRequerimentoVigilanciaDTO> resolverDocumentosTipoRequerimento(RequerimentoVigilancia requerimentoVigilancia, boolean ambienteExterno) throws DAOException, ValidacaoException {
        return ((ResolverDocumentosTipoRequerimento) this.executor.executeReturn(new ResolverDocumentosTipoRequerimento(requerimentoVigilancia, ambienteExterno))).getImpressaoListDTO();
    }

    @Override
    public File historico(Estabelecimento estabelecimento) throws DAOException, ValidacaoException {
        return ((ArquivoHistoricoContribuinte) this.executor.executeReturn(new ArquivoHistoricoContribuinte(estabelecimento))).getFile();
    }

    @Override
    public File historico(VigilanciaPessoa vigilanciaPessoa) throws DAOException, ValidacaoException {
        return ((ArquivoHistoricoContribuinte) this.executor.executeReturn(new ArquivoHistoricoContribuinte(vigilanciaPessoa))).getFile();
    }

    @Override
    public File historico(VigilanciaProfissional vigilanciaProfissional) throws DAOException, ValidacaoException {
        return ((ArquivoHistoricoContribuinte) this.executor.executeReturn(new ArquivoHistoricoContribuinte(vigilanciaProfissional))).getFile();
    }

    @Override
    public DataReport impressaoRequerimentoVigilanciaComprovante(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoVigilanciaComprovante(param));
    }

    @Override
    public DataReport relatorioDespacho(RequerimentoVigilancia param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioDespacho(param));
    }

    @Override
    public DataReport relatorioRelacaoVisita(QueryRelatorioRelacaoVisitasDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoVisita(param));
    }

    @Override
    public DataReport relatorioResumoVisita(QueryRelatorioResumoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioResumoVisita(param));
    }

    @Override
    public DataReport relatorioAlvaras(RelatorioAlvarasDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAlvaras(param));
    }

    @Override
    public DataReport relatorioRequerimento(RelatorioRequerimentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRequerimento(param));
    }

    @Override
    public DataReport relatorioRequerimentoSync(RelatorioRequerimentoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimento(param));
    }

    @Override
    public DataReport relatorioRelacaoFinanceiro(RelatorioRelacaoFinanceiroDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoFinanceiro(param));
    }

    @Override
    public DataReport relatorioResumoAgravos(RelatorioResumoAgravosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioResumoAgravos(param));
    }

    @Override
    public DataReport relatorioAgravos(RelatorioAgravosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAgravos(param));
    }

    @Override
    public DataReport relatorioRelacaoAtividadeVeterinaria(RelatorioRelacaoAtividadeVeterinariaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoAtividadeVeterinaria(param));
    }

    @Override
    public DataReport relatorioResumoAtividadeVeterinaria(RelatorioResumoAtividadeVeterinariaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioResumoAtividadeVeterinaria(param));
    }

    @Override
    public DataReport relatorioSolicitacaoAgendadaCva(RelatorioSolicitacaoAgendamentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioSolicitacaoAgendadaCva(param));
    }

    @Override
    public DataReport relatorioSolicitacaoPendenteCva(RelatorioSolicitacaoAgendamentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioSolicitacaoPendenteCva(param));
    }

    @Override
    public DataReport relatorioDenuncias(RelatorioDenunciasDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioDenuncias(param));
    }

    @Override
    public DataReport impressaoFichaCvaAnimal(CvaAnimal param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoFichaCvaAnimal(param));
    }

    @Override
    public DataReport impressaoQuestionarioPopulacaoCaesGatos(PopulacaoCaesGatos param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioPopulacaoCaesGatos(param));
    }

    @Override
    public DataReport relacaoMicrochipagem(RelatorioRelacaoMicrochipagemDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoMicrochipagem(param));
    }

    @Override
    public DataReport impressaoComprovanteAutoInfracao(Long codigoAutoInfracao,String numeroFormatado, Long situacao) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoComprovanteAutoInfracao(codigoAutoInfracao,numeroFormatado,situacao));
    }

    @Override
    public DataReport impressaoComprovanteAutoMulta(Long codigoAutoMulta, String numeroFormatado) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoComprovanteAutoMulta(codigoAutoMulta ,numeroFormatado));
    }

    @Override
    public DataReport impressaoComprovanteAutoIntimacao(Long codigoAutoIntimacao, String numeroRegistro) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoComprovanteAutoIntimacao(codigoAutoIntimacao,numeroRegistro));
    }

    @Override
    public DataReport impressaoComprovanteAutoPenalidade(Long codigoAutoPenalidade,String numeroFormatado) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoComprovanteAutoPenalidade(codigoAutoPenalidade,numeroFormatado));
    }

    @Override
    public DataReport impressaoRoteiroInspecao(RegistroInspecao registroInspecao) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoRoteiroInspecao(registroInspecao));
    }

    @Override
    public DataReport impressaoRoteiroInspecaoExterno(RoteiroInspecao roteiroInspecao) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoRoteiroInspecaoExterno(roteiroInspecao));
    }

    @Override
    public DataReport impressaoComprovanteTermoAjustamentoConduta(Long codigoTermoAjustamentoConduta, String numeroFormatado, Long situacao, Long flagFinalizado) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoComprovanteTermoAjustamentoConduta(codigoTermoAjustamentoConduta,numeroFormatado,situacao, flagFinalizado));
    }

    public DataReport relatorioAutoPenalidade(RelatorioAutoPenalidadeDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAutoPenalidade(param));
    }

    @Override
    public DataReport relatorioAutoPenalidadeSync(RelatorioAutoPenalidadeDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAutoPenalidade(param));
    }

    @Override
    public DataReport relatorioAutoInfracao(RelatorioAutoInfracaoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAutoInfracao(param));
    }

    @Override
    public DataReport relatorioAutoInfracaoSync(RelatorioAutoInfracaoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAutoInfracao(param));
    }

    @Override
    public DataReport relatorioAutoIntimacao(RelatorioAutoIntimacaoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAutoIntimacao(param));
    }

    @Override
    public DataReport relatorioAutoIntimacaoSync(RelatorioAutoIntimacaoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAutoIntimacao(param));
    }

    @Override
    public DataReport relatorioAutoMulta(RelatorioAutoMultaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAutoMulta(param));
    }

    public DataReport relatorioTempoAtendimento(RelatorioTempoAtendimentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioTempoAtendimento(param));
    }

    @Override
    public DataReport relatorioProcessoAdministrativo(RelatorioProcessoAdministrativoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioProcessoAdministrativo(param));
    }

    @Override
    public DataReport relatorioAutoMultaSync(RelatorioAutoMultaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAutoMulta(param));
    }

    @Override
    public DataReport relatorioProcessoAdministrativoSync(RelatorioProcessoAdministrativoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioProcessoAdministrativo(param));
    }


    @Override
    public DataReport impressaoTermoResponsabilidade(Long codigoTermo) throws ReportException {
        return this.executor.executeDataReport(new RelatorioTermoResponsabilidade(codigoTermo));
    }

    @Override
    public DataReport impressaoTermoAdocao(Long codigoTermo) throws ReportException {
        return this.executor.executeDataReport(new RelatorioTermoAdocao(codigoTermo));
    }

    @Override
    public DataReport relatorioPontosEstrategicos(RelatorioPontosEstrategicosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioPontosEstrategicos(param));
    }

    @Override
    public DataReport relatorioArmadilha(RelatorioArmadilhaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioArmadilha(param));
    }

    @Override
    public DataReport relatorioVisitaArmadilha(RelatorioVisitaArmadilhaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioVisitaArmadilha(param));
    }

    @Override
    public DataReport resumoVisitaArmadilha(ResumoVisitaArmadilhaDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new ResumoVisitaArmadilha(param));
    }

    @Override
    public DataReport relatorioProducaoServicoAntivetorial(RelatorioProducaoServicoAntivetorialDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioProducaoServicoAntivetorial(param));
    }

    @Override
    public DataReport relatorioRequerimentoExumacao(RelatorioAutorizacaoExumacaoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAutorizacaoExumacao(param));
    }

    @Override
    public DataReport relatorioCertidaoNadaConsta(RelatorioCertidaoNadaConstaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioCertidaoNadaConsta(param));
    }

    @Override
    public DataReport relatorioRequerimentoLivroVigilanciaTermo(RelatorioRequerimentoLivroVigilanciaTermoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoLivroVigilanciaTermo(param));
    }

    @Override
    public DataReport impressaoRequerimentoDeclaracaoVisaOutros(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoDeclaracaoVisaOutros(param));
    }

    @Override
    public DataReport impressaoRequerimentoDeclaracaoVisaIsencaoTaxas(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoDeclaracaoVisaIsencaoTaxas(param));
    }

    @Override
    public DataReport impressaoRequerimentoDeclaracaoVisaProdutos(RelatorioRequerimentoVigilanciaComprovanteDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoDeclaracaoVisaProdutos(param));
    }

    @Override
    public DataReport impressaoDeclaracaoBaixaEstabelecimento(Long codigoRequerimento) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoDeclaracaoBaixaEstabelecimento(codigoRequerimento));
    }

    @Override
    public DataReport relatorioRequerimentoAlvaraInicial(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoAlvaraSanitario(dtoParam));
    }

    @Override
    public DataReport relatorioRequerimentoLicencaSanitaria(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoLicencaSanitaria(dtoParam));
    }

    @Override
    public DataReport relatorioRequerimentoAutorizacaoSanitaria(Long codigo, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoAutorizacaoSanitaria(codigo, urlQRCode));
    }

    @Override
    public DataReport relatorioRequerimentoAlvaraCompra(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoAlvaraSanitario(dtoParam));
    }

    @Override
    public DataReport relatorioRequerimentoAlvaraProvisorio(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        RelatorioRequisicaoAlvaraSanitario alvaraInicial = new RelatorioRequisicaoAlvaraSanitario(dtoParam);
        alvaraInicial.setProvisorio(true);
        return this.executor.executeDataReport(alvaraInicial);
    }

    @Override
    public DataReport relatorioRequerimentoLicencaSanitariaProvisoria(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        RelatorioRequerimentoLicencaSanitaria licencaSanitaria = new RelatorioRequerimentoLicencaSanitaria(dtoParam);
        licencaSanitaria.setProvisorio(true);
        return this.executor.executeDataReport(licencaSanitaria);
    }

    @Override
    public DataReport relatorioRequerimentoAlvaraEvento(Long codigo, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoAlvaraEvento(codigo, urlQRCode));
    }

    @Override
    public DataReport relatorioRequerimentoCadastroEvento(Long codigo, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoCadastroEvento(codigo, urlQRCode));
    }

    @Override
    public DataReport gerarAlvaraProvisorio(GerarAlvaraProvisorioDTO dto) throws ReportException, ValidacaoException, DAOException {
        return ((GerarAlvaraProvisorio) this.executor.executeReturn(new GerarAlvaraProvisorio(dto))).getReport();
    }

    @Override
    public DataReport gerarLicencaSanitariaProvisoria(GerarAlvaraProvisorioDTO dto) throws ReportException, ValidacaoException, DAOException {
        return ((GerarLicencaSanitariaProvisoria) this.executor.executeReturn(new GerarLicencaSanitariaProvisoria(dto))).getReport();
    }

    @Override
    public DataReport relatorioImprimirBaixaResponsabilidadeAlvaraInicial(RelatorioAutorizacaoBaixaResponsabilidadeDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImprimirBaixaResponsabilidadeAlvaraInicial(param));
    }

    @Override
    public DataReport relatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara(RelatorioAutorizacaoBaixaResponsabilidadeDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara(param));
    }

    @Override
    public DataReport impressaoRequerimentoLicencaTransporte(Long codigoRequerimento, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoRequerimentoLicencaTransporte(codigoRequerimento, urlQRCode));
    }

    @Override
    public DataReport relatorioRequerimentoReceita(RelatorioRequerimentoReceitaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoReceita(param));
    }

    @Override
    public DataReport impressaoMemorando(RequerimentoVigilancia rv) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoMemorando(rv));
    }

    @Override
    public DataReport processoRelatorioRelacaoEstabelecimentosSemRT(RelatorioRelacaoEstabelecimentosSemRTDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRelacaoEstabelecimentosSemRT(param));
    }

    @Override
    public DataReport relatorioRequerimentoVacinacaoExtramuro(Long codigoRequerimento, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequerimentoVacinacaoExtramuro(codigoRequerimento, urlQRCode));
    }

    @Override
    public DataReport relatorioRelacaoEstabelecimentosSemRT(RelatorioRelacaoEstabelecimentosSemRTDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoEstabelecimentosSemRT(param));
    }

    @Override
    public DataReport impressaoParecerTecnicoPBA(ImpressaoParecerTecnicoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoParecerTecnico(param));
    }
    @Override
    public HtmlReport impressaoParecerTecnicoPBAHtml(ImpressaoParecerTecnicoDTOParam param) throws ReportException, DAOException, ValidacaoException {
        return new ParecerTecnicoPBAHtmlReport(
                ((QueryImpressaoParecerTecnico) this.executor.executeReturn(new QueryImpressaoParecerTecnico(param))).getResult()
        );
    }

    @Override
    public DataReport impressaoConformidadeTecnica(Long codigoParecer) throws ReportException {
        return this.executor.executeDataReport(new RelatorioConformidadeTecnica(codigoParecer));
    }

    @Override
    public DataReport impressaoConformidadeTecnicaVistoria(RelatorioConformidadeTecnicaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioConformidadeTecnicaVistoriaPBA(param));
    }

    @Override
    public DataReport impressaoTermoSolicitacaoServico(Long codigoRequerimento) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoTermoSolicitacaoServico(codigoRequerimento));
    }

    @Override
    public DataReport impressaoDeclaracaoBaixaVeiculo(Long codigoRequerimento) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoDeclaracaoBaixaVeiculos(codigoRequerimento));
    }

    @Override
    public DataReport relatorioListaInspecao(RelatorioListaInspecaoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioListaInspecao(param));
    }

    @Override
    public DataReport relatorioInspecaoSanitaria(RelatorioInspecaoSanitariaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioInspecaoSanitaria(param));
    }

    @Override
    public DataReport impressaoParecerProcessoAdministrativo(ImpressaoParecerProcessoAdministrativoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoParecerProcessoAdministrativo(param));
    }

    @Override
    public DataReport impressaoRequerimentoSolicitacaoJuridica(RelatorioRequerimentoSolicitacaoJuridicaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoRequerimentoSolicitacaoJuridica(param));
    }

    @Override
    public DataReport impressaoParecerRequerimento(ImpressaoParecerRequerimentoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoParecerRequerimento(param));
    }
    @Override
    public HtmlReport impressaoParecerRequerimentoHtml(ImpressaoParecerRequerimentoDTOParam param) throws DAOException, ValidacaoException {
        QueryImpressaoParecerRequerimento query = new QueryImpressaoParecerRequerimento(){
            @Override
            public void addParam(String key, Object object) {
            }
        };
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new ParecerRequerimentoHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoDecisaoProcessoAdministrativo(ImpressaoDecisaoProcessoAdministrativoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoDecisaoProcessoAdministrativo(param));
    }

    @Override
    public DataReport relatorioCadastroIndividualNotificacao(RelatorioCadastroIndividualNotificacaoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioCadastroIndividualNotificacao(param));
    }

    @Override
    public DataReport relatorioSindromeGripal(RelatorioSindromeGripalDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioSindromeGripal(param));
    }

    @Override
    public DataReport impressaoRequerimentoCredenciamentoTreinamento(Estabelecimento estabelecimento, String urlQRCode) throws ReportException {
        return this.executor.executeDataReport(new RelatorioRequisicaoCredenciamentoTreinamento(estabelecimento, urlQRCode));
    }

    @Override
    public DataReport impressaoHabiteseSanitario(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoHabiteseSanitario(param));
    }

    @Override
    public HtmlReport impressaoHabiteseSanitarioHtml(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoHabitese query = new QueryRelatorioImpressaoHabitese();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new HabiteseSanitarioHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoLaudoVistoria(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoLaudoVistoria(param));
    }

    @Override
    public HtmlReport impressaoLaudoVistoriaHtml(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoLaudoVistoria query = new QueryRelatorioImpressaoLaudoVistoria();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new LaudoVistoriaHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoParecerProjetoHidrossanitario(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoParecerProjetoHidrossanitario(param));
    }

    @Override
    public HtmlReport impressaoParecerProjetoHidrossanitarioHtml(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoParecerProjetoHidrossanitario query = new QueryRelatorioImpressaoParecerProjetoHidrossanitario();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new ParecerProjetoHidrossanitarioHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoDeferimentoProjetoHidrossanitario(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoDeferimentoProjetoHidrossanitario(param));
    }

    @Override
    public DataReport relatorioRelacaoInspecoesRealizadas(RelatorioRelacaoInspecoesRealizadasDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoInspecoesRealizadas(param));
    }

    @Override
    public File gerarBoletoVigilancia(EmissaoBoletoVigilanciaDTO dto) throws DAOException, ValidacaoException {
        return ((GerarBoletoVigilancia) this.executor.executeReturn(new GerarBoletoVigilancia(dto))).getFile();
    }

    @Override
    public DataReport impressaoProfissionalParaReceita(Long codigoVigilanciaProfissional) throws ReportException {
        return this.executor.executeDataReport(new ImpressaoProfissionalParaReceita(codigoVigilanciaProfissional));
    }

    @Override
    public DataReport relatorioRelacaoEscalaPlantao(QueryRelatorioRelacaoEscalaPlantaoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoEscalaPlantao(param));
    }

    @Override
    public DataReport impressaoDeferimentoProjetoHidrossanitarioDeclaratorio(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoDeferimentoProjetoHidrossanitarioDeclaratorio(param));
    }

    @Override
    public DataReport impressaoDeferimentoProjetoArquitetonico(ImpressaoParecerProjetoArquitetonicoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoDeferimentoProjetoArquitetonicoSanitario(param));
    }

    @Override
    public DataReport impressaoParecerProjetoHidrossanitarioDeclaratorio(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoParecerProjetoHidrossanitarioDeclaratorio(param));
    }

    @Override
    public HtmlReport impressaoParecerProjetoHidrossanitarioDeclaratorioHtml(ImpressaoParecerProjetoHidrossanitarioDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoParecerProjetoHidrossanitarioDeclaratorio query = new QueryRelatorioImpressaoParecerProjetoHidrossanitarioDeclaratorio();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new ParecerProjetoHidrossanitarioDeclaratorioHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoParecerProjetoArquitetonico(ImpressaoParecerProjetoArquitetonicoDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoParecerProjetoArquitetonico(param));
    }
    @Override
    public HtmlReport impressaoParecerProjetoArquitetonicoHtml(ImpressaoParecerProjetoArquitetonicoDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoParecerProjetoArquitetonico query = new QueryRelatorioImpressaoParecerProjetoArquitetonico();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new ParecerProjetoArquitetonicoHtmlReport(query.getResult());
    }
    @Override
    public DataReport impressaoParecerVistoriaHabiteseDeclaratorio(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoLaudoVistoriaDeclaratorio(param));
    }

    @Override
    public HtmlReport impressaoParecerVistoriaHabiteseDeclaratorioHtml(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException, DAOException, ValidacaoException {
        QueryRelatorioImpressaoLaudoVistoriaDeclaratorio query = new QueryRelatorioImpressaoLaudoVistoriaDeclaratorio();
        query.setDTOParam(param);
        this.executor.executeReturn(query);
        return new ParecerVistoriaHabiteseDeclaratorioHtmlReport(query.getResult());
    }

    @Override
    public DataReport impressaoHabiteseSanitarioDeclaratorio(ImpressaoParecerVistoriaHidrossanitariaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoHabiteseSanitarioDeclaratorio(param));
    }

    @Override
    public DataReport getPdfCarimboPrancha(PdfCarimboPranchaDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new AbstractReportPdfCarimboPrancha(param));
    }

    @Override
    public DataReport relatorioRelacaoPopulacaoCaesGatos(RelatorioRelacaoPopulacaoCaesGatosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoPopulacaoCaesGatos(param));
    }

    @Override
    public DataReport relatorioDispensaSanitaria(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        return this.executor.executeDataReport(new RelatorioDispensaRiscoMedio(dtoParam));
    }

    @Override
    public DataReport relatorioDesobrigacaoAlvara(ImpressaoAlvaraDTOParam dtoParam) throws ReportException {
        return this.executor.executeDataReport(new RelatorioDesobrigacaoAlvara(dtoParam));
    }
}

