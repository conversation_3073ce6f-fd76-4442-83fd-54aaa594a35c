/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.atendimentogeral;


import br.com.celk.atendimento.prontuario.reprocessaAtendimentosEsus.DTOTipoAtendimentoNaoProcessado;
import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.agendamento.exame.DeletarCotaUnidade;
import br.com.ksisolucoes.bo.agendamento.exame.GerarCotaUnidadeManual;
import br.com.ksisolucoes.bo.agendamento.exame.GerarUnidadePrestadoresCompetencia;
import br.com.ksisolucoes.bo.basico.empresa.SalvarEmpresaCaracterizacao;
import br.com.ksisolucoes.bo.basico.empresa.SalvarEmpresaConjunto;
import br.com.ksisolucoes.bo.basico.equipamento.SalvarEquipamento;
import br.com.ksisolucoes.bo.basico.equipe.CadastrarEquipe;
import br.com.ksisolucoes.bo.basico.equipemicroarea.CadastrarEquipeMicroArea;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EmpresaCaracterizacaoDTO;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EmpresaConjuntoDTO;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EquipeProfissionalDTO;
import br.com.ksisolucoes.bo.consorcio.dirf.SalvarConfiguracaoDirf;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConfiguracaoDirfDTO;
import br.com.ksisolucoes.bo.formulario.dto.DTOFormularioValor;
import br.com.ksisolucoes.bo.integracao.bpa.CancelarBpa;
import br.com.ksisolucoes.bo.integracao.bpa.ConfirmarLancamentoProducao;
import br.com.ksisolucoes.bo.integracao.bpa.DadosConfiguracaoBpaDTO;
import br.com.ksisolucoes.bo.integracao.bpa.ExcluirLancamentoBpaConsolidado;
import br.com.ksisolucoes.bo.integracao.bpa.IniciarProcessoBpa;
import br.com.ksisolucoes.bo.integracao.bpa.MontarArquivoBpa;
import br.com.ksisolucoes.bo.integracao.bpa.MontarBpa;
import br.com.ksisolucoes.bo.integracao.bpa.ProcessarBpa;
import br.com.ksisolucoes.bo.integracao.bpa.QueryConsultaLancamentoBpaConsolidado;
import br.com.ksisolucoes.bo.integracao.bpa.SalvarConfiguracaoBpa;
import br.com.ksisolucoes.bo.integracao.bpa.SalvarConfiguracoesBpa;
import br.com.ksisolucoes.bo.integracao.bpa.SalvarLancamentoBpaConsolidado;
import br.com.ksisolucoes.bo.integracao.raas.AtualizarRaasProcesso;
import br.com.ksisolucoes.bo.integracao.raas.CancelarRaasProcesso;
import br.com.ksisolucoes.bo.integracao.raas.DownloadGeracaoRaas;
import br.com.ksisolucoes.bo.integracao.raas.GerarRaas;
import br.com.ksisolucoes.bo.integracao.raas.MontarRaas;
import br.com.ksisolucoes.bo.integracao.raas.ProcessarRaas;
import br.com.ksisolucoes.bo.integracao.raas.QueryGeracaoRaas;
import br.com.ksisolucoes.bo.integracao.raas.SalvarConfiguracaoRaas;
import br.com.ksisolucoes.bo.integracao.raas.dto.GeracaoRaasDTO;
import br.com.ksisolucoes.bo.interfaces.facade._AtendimentoGeralFacadeLocal;
import br.com.ksisolucoes.bo.interfaces.facade._AtendimentoGeralFacadeRemote;
import br.com.ksisolucoes.bo.prontuario.basico.atendimentomedico.ReprocessaAtendimentosEnviarAtendimentoEsus;
import br.com.ksisolucoes.bo.prontuario.basico.reprocessaatendimentosesus.QueryBuscaAtendimentosNaoForamEsus;
import br.com.ksisolucoes.bo.prontuario.basico.reprocessaatendimentosesus.QueryBuscaTiposAtendimentoNaoForamEsus;
import br.com.ksisolucoes.bo.prontuario.basico.reprocessaatendimentosesus.ReprocessaAtendimentosEsus;
import br.com.ksisolucoes.bo.prontuario.basico.exame.DeletarExamesAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.exame.FinalizarExame;
import br.com.ksisolucoes.bo.prontuario.basico.exameprocedimento.GerarExamesProcedimentoByEstrutura;
import br.com.ksisolucoes.bo.prontuario.basico.exameprocedimento.QueryConsultaExameProcedimento;
import br.com.ksisolucoes.bo.prontuario.basico.exameunidadecota.ConsultaCotaUnidade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EmpresaNaturezaTipoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NaturezaProcuraTipoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryPagerImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.naturezaprocuratipoatendimento.CadastrarNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.naturezaprocuratipoatendimento.ClonarNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.naturezaprocuratipoatendimento.RemoverNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.tiporeceita.DeletarTipoReceita;
import br.com.ksisolucoes.bo.prontuario.basico.tiporeceita.QueryConsultaTipoReceita;
import br.com.ksisolucoes.bo.prontuario.basico.tiporeceita.QueryPagerImpressaoReceituario;
import br.com.ksisolucoes.bo.prontuario.basico.tiporeceita.SalvarTipoReceita;
import br.com.ksisolucoes.bo.prontuario.exame.atendimentoexame.FinalizarAtendimentoExame;
import br.com.ksisolucoes.bo.prontuario.exame.atendimentoexame.IniciarAtendimentoExame;
import br.com.ksisolucoes.bo.prontuario.exame.atendimentoexame.SalvarResultadoLaudo;
import br.com.ksisolucoes.bo.prontuario.prescricaoenfermagem.SalvarPrescricaoEnfermagem;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.ConsultaLancamentoBpaConsolidadoDTOParam;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.GerarExamesProcedimentoByEstruturaDTO;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.PequenaCirurgiaDTO;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.PequenaCirurgiaItemDTO;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.ProcedimentoDTO;
import br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia.CadastrarInsumosPequenaCirurgia;
import br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia.CancelarPequenaCirurgia;
import br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia.ConcluirPequenaCirurgia;
import br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia.DeletarPequenaCirurgia;
import br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia.SalvarPequenaCirurgia;
import br.com.ksisolucoes.bo.prontuario.procedimento.procedimento.CadastrarProcedimento;
import br.com.ksisolucoes.bo.prontuario.procedimento.procedimento.RemoverProcedimento;
import br.com.ksisolucoes.bo.siab.raas.ConfiguracaoRaasDto;
import br.com.ksisolucoes.bo.siab.raas.GerarRaasProcessoDTO;
import br.com.ksisolucoes.bo.siab.raas.GerarRaasProcessoDTOParam;
import br.com.ksisolucoes.bo.siab.raas.RaasGeradoDTO;
import br.com.ksisolucoes.bo.unidadesaude.basico.SalvarMotivoDestinoSaida;
import br.com.ksisolucoes.command.AsyncProcessCommand;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado;
import br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem;
import br.com.ksisolucoes.vo.atendimento.RaasProcesso;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaBpa;
import br.com.ksisolucoes.vo.basico.Equipamento;
import br.com.ksisolucoes.vo.basico.EquipamentoProcedimento;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaConvenioBpa;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.prescricaoenfermagem.PrescricaoEnfermagemGrupo;
import br.com.ksisolucoes.vo.prontuario.procedimento.caps.ItemMotivoDestinoSaida;
import br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.jboss.ejb3.annotation.TransactionTimeout;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Stateless  
public class AtendimentoGeralBO extends BOGenericImpl implements _AtendimentoGeralFacadeLocal, _AtendimentoGeralFacadeRemote{

    @Override
    public Serializable cadastrarEquipe(Equipe vo, List<EquipeProfissionalDTO> profissionais) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarEquipe(vo, profissionais));
        return null;
    }

    @Override
    public Serializable cadastrarProcedimento(ProcedimentoDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute( new CadastrarProcedimento( dto ) );
        return null;
    }

    @Override
    public void removerProcedimento(ProcedimentoDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute( new RemoverProcedimento( dto ) );
    }
    
    @Override
    public void processarBpa(Long tipoFinanciamento, Long tipoBpa, Long mes, Long ano, List<Empresa> empresaList) throws DAOException, ValidacaoException {
        this.executor.execute(new ProcessarBpa(tipoFinanciamento, tipoBpa, mes, ano, empresaList));
    }
    
    @Override
    @TransactionTimeout(1200)
    public RaasGeradoDTO gerarRaas(GerarRaasProcessoDTOParam param) throws DAOException, ValidacaoException {
        return ((GerarRaas) this.executor.executeReturn(new GerarRaas(param))).getRaasGeradoDTO();
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public GerarRaasProcessoDTO montarRaas(GerarRaasProcessoDTOParam param) throws DAOException, ValidacaoException {
        return ((MontarRaas) this.executor.executeReturn(new MontarRaas(param))).getGerarRaasProcessoDTO();
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public Long iniciarProcessoBpa(Long tipoFinanciamento, Long tipoBpa, Long mes, Long ano) throws DAOException, ValidacaoException {
        return ((IniciarProcessoBpa)this.executor.executeReturn( new IniciarProcessoBpa(tipoFinanciamento, tipoBpa, mes, ano))).getCodigoBpaProcesso();
    }
    
    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @TransactionTimeout(54000) //15HR
    public void montarBpa(Long codigoBpa, Long tipoBpa, List<Empresa> empresaList) throws DAOException, ValidacaoException {
        this.executorAsync.execute( new MontarBpa(codigoBpa,tipoBpa, empresaList));
    }

    @Override
    public void montarArquivoBpa(Long codigoBpaProcesso) throws DAOException, ValidacaoException {
        this.executor.execute(new MontarArquivoBpa(codigoBpaProcesso));
    }

    @Override
    public void salvarEquipamento(Equipamento equipamento, List<EquipamentoProcedimento> procedimentos) throws DAOException, ValidacaoException{
       this.executor.execute(new SalvarEquipamento(equipamento, procedimentos));
    }

    @Override
    public void salvarTipoReceita(TipoReceita tipoReceita, List<String> cbos, List<Long> codigosEmpresa) throws DAOException, ValidacaoException{
        this.executor.execute(new SalvarTipoReceita(tipoReceita, cbos, codigosEmpresa));
    }

    @Override
    public void deletarTipoReceita(TipoReceita tipoReceita) throws DAOException, ValidacaoException{
        this.executor.execute(new DeletarTipoReceita(tipoReceita));
    }

    @Override
    public DataPagingResult<TipoReceita> getTipoReceitaQueryPager(DataPaging dataPaging) throws SGKException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaTipoReceita());
    }

    @Override
    public void salvarConfiguracoesBpa(DadosConfiguracaoBpaDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarConfiguracoesBpa(dto));
    }

    @Override
    public void cadastrarNaturezaProcuraTipoAtendimento(NaturezaProcuraTipoAtendimento vo, List<EmpresaNaturezaTipoDTO> empresas, List<EloNaturezaTipoEncaminhamento> elosNaturezaTipo) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarNaturezaProcuraTipoAtendimento(vo, empresas, elosNaturezaTipo));
    }

    @Override
    public void removerNaturezaProcuraTipoAtendimento(NaturezaProcuraTipoAtendimento vo) throws DAOException, ValidacaoException {
        this.executor.execute(new RemoverNaturezaProcuraTipoAtendimento(vo));
    }

    @Override
    public void salvarEmpresaConjunto(EmpresaConjuntoDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarEmpresaConjunto(dto));
    }

    @Override
    public void salvarEmpresaCaracterizacao(EmpresaCaracterizacaoDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarEmpresaCaracterizacao(dto));
    }

    @Override
    public PequenaCirurgiaDTO salvarPequenaCirurgia(PequenaCirurgiaDTO dto) throws DAOException, ValidacaoException {
        return ((SalvarPequenaCirurgia)this.executor.executeReturn(new SalvarPequenaCirurgia(dto))).getDto();
    }

    @Override
    public void deletarPequenaCirurgia(Long codigoPequenaCirurgia) throws DAOException, ValidacaoException {
        this.executor.execute(new DeletarPequenaCirurgia(codigoPequenaCirurgia));
    }

    @Override
    public void concluirPequenaCirurgia(AtendimentoItem atendimentoItem) throws DAOException, ValidacaoException {
        this.executor.execute(new ConcluirPequenaCirurgia(atendimentoItem));
    }

    @Override
    public void cancelarPequenaCirurgia(AtendimentoItem atendimentoItem) throws DAOException, ValidacaoException {
        this.executor.execute(new CancelarPequenaCirurgia(atendimentoItem));
    }

    @Override
    public void cadastrarInsumosPequenaCirurgia(List<PequenaCirurgiaItemDTO> dtoList) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarInsumosPequenaCirurgia(dtoList));
    }
    
    @Override
    public void deletarExamesAtendimento(Atendimento atendimento) throws DAOException, ValidacaoException {
        this.executor.execute(new DeletarExamesAtendimento(atendimento));
    }
    

    @Override
    public Long consultaPercentualUtilizadoExames(TipoExame tipoExame, Empresa unidade) throws DAOException, ValidacaoException {
        return ((ConsultaCotaUnidade)this.executor.executeReturn(new ConsultaCotaUnidade(tipoExame, unidade))).getPercentualUtilizado();
    }

    @Override
    public Exame finalizarExame(Atendimento atendimento, Exame exame, List<ExameRequisicao> exameRequisicaos, boolean exameRealizado) throws DAOException, ValidacaoException {
        return ((FinalizarExame)this.executor.executeReturn(new FinalizarExame(atendimento, exame, exameRequisicaos ,exameRealizado))).getExame();
    }

    @Override
    public DataPagingResult consultaExameProcedimento(DataPaging dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging,new QueryConsultaExameProcedimento());
    }

    @Override
    public Class getReferenceClass() {
        return null;
    }

    @Override
    public AtendimentoExame iniciarAtendimentoExame(Atendimento atendimento) throws DAOException, ValidacaoException {
        return ((IniciarAtendimentoExame)this.executor.executeReturn(new IniciarAtendimentoExame(atendimento))).getAtendimentoExame();
    }

    @Override
    public DTOFormularioValor salvarResultadoLaudo(Atendimento atendimento, DTOFormularioValor dTOFormularioValor) throws DAOException, ValidacaoException {
        return ((SalvarResultadoLaudo)this.executor.executeReturn(new SalvarResultadoLaudo(atendimento, dTOFormularioValor))).getdTOFormularioValor();
    }

    @Override
    public void finalizarAtendimentoExame(AtendimentoExame atendimentoExame) throws DAOException, ValidacaoException {
        this.executor.execute(new FinalizarAtendimentoExame(atendimentoExame));
    }

    @Override
    public void gerarUnidadePrestadoresCompetencia(Date competencia) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarUnidadePrestadoresCompetencia(competencia));
    }

    @Override
    public void gerarExamesProcedimentoByEstrutura(GerarExamesProcedimentoByEstruturaDTO dTO) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarExamesProcedimentoByEstrutura(dTO));
    }

    @Override
    public DataPagingResult<Receituario> queryPagerImpressaoReceituario(DataPaging<QueryPagerImpressaoReceituarioDTOParam> dataPaging) throws SGKException {
        return this.executor.executeQueryPager(dataPaging, new QueryPagerImpressaoReceituario(dataPaging.getParam()));
    }

    @Override
    public void gerarCotaUnidadeManual(Date competencia) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarCotaUnidadeManual(competencia));
    }
    
    @Override
    public void deletarCotaUnidade(Date dataCompetencia)throws DAOException, ValidacaoException{
        this.executor.execute(new DeletarCotaUnidade(dataCompetencia));
    }

    @Override
    public void cadastrarEquipeMicroArea(EquipeMicroArea equipeMicroArea) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarEquipeMicroArea(equipeMicroArea));
    }

    @Override
    public void salvarConfiguracaoBpa(ParametroAtendimento pa, Map<EmpresaBpa, List<EmpresaConvenioBpa>> map) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarConfiguracaoBpa(pa, map));
    }

    @Override
    public NaturezaProcuraTipoAtendimentoDTO clonarNaturezaProcuraTipoAtendimento(Long codigo) throws DAOException, ValidacaoException {
        return ((ClonarNaturezaProcuraTipoAtendimento) this.executor.executeReturn(new ClonarNaturezaProcuraTipoAtendimento(codigo))).getNaturezaProcuraTipoAtendimentoDTO();
    }

    @Override
    public PrescricaoEnfermagemGrupo salvarPrescricaoEnfermagem(PrescricaoEnfermagemGrupo prescricaoEnfermagemGrupo, List<PrescricaoEnfermagem> prescricaoEnfermagemList) throws ValidacaoException, DAOException {
        return ((SalvarPrescricaoEnfermagem) this.executor.executeReturn(new SalvarPrescricaoEnfermagem(prescricaoEnfermagemGrupo, prescricaoEnfermagemList))).getPrescricaoEnfermagemGrupo();
    }
    
    @Override
    public LancamentoBpaConsolidado salvarLancamentoBpaConsolidado(LancamentoBpaConsolidado lancamentoBpaConsolidado, List<LancamentoBpaConsolidadoItem> lancamentoBpaConsolidadoItemList) throws ValidacaoException, DAOException {
        return ((SalvarLancamentoBpaConsolidado) this.executor.executeReturn(new SalvarLancamentoBpaConsolidado(lancamentoBpaConsolidado, lancamentoBpaConsolidadoItemList))).getLancamentoBpaConsolidado();
    }
    
    @Override
    public DataPagingResult consultarLancamentoBpaConsolidado(DataPaging<ConsultaLancamentoBpaConsolidadoDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaLancamentoBpaConsolidado(dataPaging.getParam()));
    }
    
    @Override
    public void excluirLancamentoBpaConsolidado(LancamentoBpaConsolidado lancamentoBpaConsolidado) throws ValidacaoException, DAOException {
        this.executor.execute(new ExcluirLancamentoBpaConsolidado(lancamentoBpaConsolidado));
    }
    
    @Override
    public RaasProcesso downloadGeracaoRaas(RaasProcesso raasProcesso) throws DAOException, ValidacaoException {
        return ((DownloadGeracaoRaas) this.executor.executeReturn(new DownloadGeracaoRaas(raasProcesso))).getRaasProcesso();
    }

    @Override
    public void salvarConfiguracaoRaas(ConfiguracaoRaasDto dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarConfiguracaoRaas(dto));
    }
    
    @Override
    public List<GeracaoRaasDTO> consultarGeracaoRaas(GerarRaasProcessoDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryGeracaoRaas) this.executor.executeReturn(new QueryGeracaoRaas(param))).getResult();
    }
    
    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void atualizarRaasProcesso(Long codigoRaasProcesso, String texto, String mensagemErro) throws ValidacaoException, DAOException {
        this.executor.execute(new AtualizarRaasProcesso(codigoRaasProcesso, texto, mensagemErro));
    }
    
    @Override
    public void cancelarRaasProcesso(RaasProcesso raasProcesso) throws ValidacaoException, DAOException {
        this.executor.execute(new CancelarRaasProcesso(raasProcesso));
    }

    @Override
    public MotivoDestinoSaida salvarMotivoDestinoSaida(MotivoDestinoSaida motivoDestinoSaida, List<ItemMotivoDestinoSaida> lista) throws DAOException, ValidacaoException {
        return ((SalvarMotivoDestinoSaida) this.executor.executeReturn(new SalvarMotivoDestinoSaida(motivoDestinoSaida, lista))).getRetorno();
    }
    
    @Override
    public void confirmarLancamentoProducao(ContaPaciente contaPaciente) throws DAOException, ValidacaoException {
        this.executor.execute(new ConfirmarLancamentoProducao(contaPaciente));
    }
    
    @Override
    public AsyncProcess processarRaas(GerarRaasProcessoDTOParam param) throws DAOException, ValidacaoException {
        return (AsyncProcess) ((AsyncProcessCommand)this.executorAsync.executeReturn(new ProcessarRaas(param))).getAsyncObject();
    }

    @Override
    @TransactionTimeout(7200)
    public void cancelarBpa(BpaProcesso bpaProcesso) throws DAOException, ValidacaoException {
        this.executor.execute(new CancelarBpa(bpaProcesso));
    }

    @Override
    public void salvarConfiguracaoDirf(ConfiguracaoDirfDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarConfiguracaoDirf(dto));
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Override
    public List<Atendimento> buscaAtendimentosNaoForamEsus() throws DAOException, ValidacaoException {
        return ((QueryBuscaAtendimentosNaoForamEsus) this.executor.executeReturn(new QueryBuscaAtendimentosNaoForamEsus())).getResult();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Override
    public List<DTOTipoAtendimentoNaoProcessado> buscaTiposAtendimentoNaoForamEsus() throws DAOException, ValidacaoException {
        return ((QueryBuscaTiposAtendimentoNaoForamEsus) this.executor.executeReturn(new QueryBuscaTiposAtendimentoNaoForamEsus())).getResult();
    }

    @Override
    @TransactionTimeout(3600)
    public HashMap<String, Integer> reprocessaAtendimentosNaoForamEsus() throws DAOException, ValidacaoException {
        return ((ReprocessaAtendimentosEsus) this.executor.executeReturn(new ReprocessaAtendimentosEsus())).getContadorErros();
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @Override
    public void reprocessaAtendimentosEnviarAtendimentoEsus(Long id) throws DAOException, ValidacaoException {
        this.executor.execute(new ReprocessaAtendimentosEnviarAtendimentoEsus(id));
    }
}