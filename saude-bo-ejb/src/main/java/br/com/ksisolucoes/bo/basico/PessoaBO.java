/*
 * BOPessoa.java
 *
 * Created on 26 de Agosto de 2003, 08:43
 */

package br.com.ksisolucoes.bo.basico;

import java.io.Serializable;

import br.com.celk.fornecedor.dto.CadastroFornecedorDTO;
import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.basico.interfaces.facade.*;
import br.com.ksisolucoes.bo.basico.pessoa.DeletePessoa;
import br.com.ksisolucoes.bo.basico.pessoa.SavePessoa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.fornecedor.SalvarFornecedor;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.basico.PessoaEndereco;
import br.com.ksisolucoes.vo.basico.PessoaEnderecoPK;
import br.com.ksisolucoes.vo.basico.TipoEndereco;

/**
 * <AUTHOR> Giordani
 *
 */
import javax.ejb.Stateless;

@Stateless 
public class PessoaBO extends BOGenericImpl implements _PessoaFacadeLocal, _PessoaFacadeRemote {
    
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute( new DeletePessoa((Pessoa) vo ));
    }
    
    /**
     * @link PessoaFacade#save(Object, String)
     */
    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute( new SavePessoa( vo ) );
        return null;
    }
    
    @Override
    public final Class getReferenceClass() {
        return Pessoa.class;
    }
    
    public PessoaEndereco getPessoaEndereco(Pessoa pessoa, TipoEndereco tipoEndereco) throws DAOException, ValidacaoException {
        PessoaEnderecoPK pessoaEnderecoPK = new PessoaEnderecoPK();
        pessoaEnderecoPK.setPessoa(pessoa);
        pessoaEnderecoPK.setTipoEndereco(tipoEndereco);

        LoadManager loadManager = LoadManager.getInstance(PessoaEndereco.class)
                .setId(pessoaEnderecoPK).start();

        return loadManager.start().getVO();
    }

    @Override
    public Pessoa salvarFornecedor(CadastroFornecedorDTO dto) throws DAOException, ValidacaoException {
        return ((SalvarFornecedor)this.executor.executeReturn(new SalvarFornecedor(dto))).getFornecedor();

    }
}