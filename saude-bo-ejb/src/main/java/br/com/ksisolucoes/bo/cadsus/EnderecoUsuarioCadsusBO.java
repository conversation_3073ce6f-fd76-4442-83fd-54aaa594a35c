package br.com.ksisolucoes.bo.cadsus;

import br.com.celk.bo.service.rest.endereco.EnderecoDomicilioRestDTO;
import br.com.celk.bo.service.rest.endereco.EnderecoUsuarioCadsusRestDTO;
import br.com.celk.bo.service.rest.endereco.TipoLogradouroRestDTO;
import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.cadsus.cds.esusfichaenderecodomicilioesus.GerarAtualizarEsusFichaEnderecoDomicilioEsus;
import br.com.ksisolucoes.bo.cadsus.enderecodomicilio.ConsultaEnderecoDomicilioRest;
import br.com.ksisolucoes.bo.cadsus.enderecodomicilioesus.SalvarEnderecoDomicilioEsus;
import br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus.CadastrarEnderecoUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus.ConsultaEnderecoUsuarioCadsusRest;
import br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus.QueryConsultaEnderecoUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus.QueryPagerConsultaEnderecoUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus.SaveEnderecoUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.EnderecoUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.EnderecoUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaDominioTipoLogradouroCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade._EnderecoUsuarioCadsusFacadeLocal;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade._EnderecoUsuarioCadsusFacadeRemote;
import br.com.ksisolucoes.bo.cadsus.tipologradourocadsus.ConsultaTipoLogradouroCadsusRest;
import br.com.ksisolucoes.bo.cadsus.tipologradourocadsus.QueryConsultaDominioTipoLogradouroCadsus;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaEnderecoDomicilioEsus;

import javax.ejb.Stateless;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Giordani
 * <AUTHOR> Barbosa Schmichtemberg
 *
 */

@Stateless 
public class EnderecoUsuarioCadsusBO extends BOGenericImpl implements _EnderecoUsuarioCadsusFacadeLocal, _EnderecoUsuarioCadsusFacadeRemote {

    @Override    
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        SaveEnderecoUsuarioCadsus seuc = (SaveEnderecoUsuarioCadsus) this.executor.executeReturn( new SaveEnderecoUsuarioCadsus( vo ) );
        return seuc.getEnderecoUsuarioCadsus();
    }

    @Override
    public final Class getReferenceClass() {
        return EnderecoUsuarioCadsus.class;
    }

    @Override
    public DataPagingResult getPagerConsultaEndereco(DataPaging<EnderecoUsuarioCadsusDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryPagerConsultaEnderecoUsuarioCadsus(dataPaging.getParam()));
    }
 
    @Override
    public List<EnderecoUsuarioCadsusDTO> consultaEnderecoByCodigo(EnderecoUsuarioCadsusDTOParam param) throws DAOException, ValidacaoException {
        QueryConsultaEnderecoUsuarioCadsus qceuc = (QueryConsultaEnderecoUsuarioCadsus) this.executor.executeReturn(new QueryConsultaEnderecoUsuarioCadsus(param));
        return qceuc.getResult();
    }

    @Override
    public Serializable cadastrarEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) throws DAOException, ValidacaoException{
        CadastrarEnderecoUsuarioCadsus ceuc = (CadastrarEnderecoUsuarioCadsus) this.executor.executeReturn(new CadastrarEnderecoUsuarioCadsus(enderecoUsuarioCadsus));
        return ceuc.getEnderecoUsuarioCadsus();
    }

    @Override
    public DataPagingResult<TipoLogradouroCadsus> queryConsultaDominioTipoLogradouroCadsus(DataPaging<QueryConsultaDominioTipoLogradouroCadsusDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaDominioTipoLogradouroCadsus(dataPaging.getParam()));
    }

    @Override
    public EnderecoDomicilioEsus salvarEnderecoDomicilioEsus(EnderecoDomicilioEsus enderecoDomicilioEsus) throws DAOException, ValidacaoException {
        return ((SalvarEnderecoDomicilioEsus)this.executor.executeReturn(new SalvarEnderecoDomicilioEsus(enderecoDomicilioEsus))).getEnderecoDomicilioEsus();
    }

    @Override
    public List<TipoLogradouroRestDTO> consultarTipoLogradouroRest(Long versaoMinima) throws DAOException, ValidacaoException {
        return ((ConsultaTipoLogradouroCadsusRest)this.executor.executeReturn(new ConsultaTipoLogradouroCadsusRest(versaoMinima))).getList();
    }

    @Override
    public List<EnderecoUsuarioCadsusRestDTO> consultarEnderecoRest(Long versaoMinima, int limite) throws DAOException, ValidacaoException {
        return ((ConsultaEnderecoUsuarioCadsusRest)this.executor.executeReturn(new ConsultaEnderecoUsuarioCadsusRest(versaoMinima, limite))).getResult();
    }

    @Override
    public List<EnderecoDomicilioRestDTO> consultarEnderecoDomicilioRest(Long versaoMinima, int limite) throws DAOException, ValidacaoException {
        return ((ConsultaEnderecoDomicilioRest)this.executor.executeReturn(new ConsultaEnderecoDomicilioRest(versaoMinima, limite))).getResult();
    }

    @Override
    public EsusFichaEnderecoDomicilioEsus gerarAtualizarEsusFichaEnderecoDomicilioEsus(EnderecoDomicilioEsus enderecoDomicilioEsus) throws DAOException, ValidacaoException {
        return ((GerarAtualizarEsusFichaEnderecoDomicilioEsus) this.executor.executeReturn(new GerarAtualizarEsusFichaEnderecoDomicilioEsus(enderecoDomicilioEsus))).getEsusFichaEnderecoDomicilioEsus();
    }
 
}
