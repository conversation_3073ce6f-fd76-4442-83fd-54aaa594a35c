package br.com.ksisolucoes.bo.cadsus;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaProfissionalDTOParam;
import br.com.ksisolucoes.bo.cadsus.profissional.*;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import java.io.Serializable;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryProfissionalCargaHorariaGrupoAtendimentoCboDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaDominioProfissionalDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryProfissionalTipoAtendimentoDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.*;
import br.com.ksisolucoes.bo.cadsus.profissionalcargahoraria.QueryConsultaProfissionalCargaHoraria;
import br.com.ksisolucoes.bo.cadsus.profissionalcargahoraria.QueryProfissionalCargaHorariaGrupoAtendimentoCbo;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.DominioProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.hospital.tiss.EloTissProfissionalConvenio;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.List;

/**
 * <AUTHOR> Giordani
 *
 */
import javax.ejb.Stateless;

@Stateless
public class ProfissionalBO extends BOGenericImpl implements _ProfissionalFacadeLocal, _ProfissionalFacadeRemote {

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteProfissional(vo));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new SaveProfissional((Profissional) vo));
        return null;
    }

//    public void updateFragment(Map<String, Object> map, Long codigoProfissional) throws DAOException, ValidacaoException {
//        this.executor.execute( new UpdateFragmentProfissional( map, codigoProfissional ) );
//    }
    /**
     * {@inheritDoc}
     */
    @Override
    public final Class getReferenceClass() {
        return Profissional.class;
    }

    @Override
    public Serializable cadastrarProfissional(Profissional vo, List<ProfissionalCargaHoraria> vinculosAtivos, List<EloTissProfissionalConvenio> elosTiss) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarProfissional(vo, vinculosAtivos, elosTiss));
        return null;
    }

    public DataPagingResult<Profissional> getProfissionalQueryPager(DataPaging<QueryConsultaProfissionalDTOParam> dataPaging) throws SGKException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaProfissional(dataPaging.getParam()));
    }

    @Override
    public void cadastrarProfissionalExterno(Profissional vo, List<ProfissionalCargaHoraria> vinculos) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarProfissionalExterno(vo, vinculos));
    }

    @Override
    public TabelaCbo consultaProfissionalCargaHoraria(QueryConsultaProfissionalCargaHorariaDTOParam param) throws DAOException, ValidacaoException {
        QueryConsultaProfissionalCargaHoraria qcpch = (QueryConsultaProfissionalCargaHoraria) this.executor.executeReturn(new QueryConsultaProfissionalCargaHoraria(param));
        return qcpch.getCbo();
    }

    @Override
    public DataPagingResult<DominioProfissional> consultaDominioProfissional(DataPaging<QueryConsultaDominioProfissionalDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaDominioProfissional(dataPaging.getParam()));
    }

    @Override
    public List<Profissional> consultaProfissionalTipoAtendimento(QueryProfissionalTipoAtendimentoDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryProfissionalTipoAtendimento) this.executor.executeReturn(new QueryProfissionalTipoAtendimento(param))).getProfissionais();
    }

    @Override
    public List<TabelaCbo> consultaCbosProfissional(QueryConsultaProfissionalCargaHorariaDTOParam param) throws DAOException, ValidacaoException {
        QueryConsultaProfissionalCargaHoraria qcpch = (QueryConsultaProfissionalCargaHoraria) this.executor.executeReturn(new QueryConsultaProfissionalCargaHoraria(param));
        return qcpch.getLista();
    }

    @Override
    public TabelaCbo consultaProfissionalCargaHorariaGrupoAtendimentoCbo(QueryProfissionalCargaHorariaGrupoAtendimentoCboDTOParam param) throws DAOException, ValidacaoException {
        QueryProfissionalCargaHorariaGrupoAtendimentoCbo query = (QueryProfissionalCargaHorariaGrupoAtendimentoCbo) this.executor.executeReturn(new QueryProfissionalCargaHorariaGrupoAtendimentoCbo(param));
        return query.getCbo();
    }

    @Override
    public void inativarProfissionais() throws DAOException, ValidacaoException {
        this.executor.execute(new InativarProfissionais());
    }
    
    @Override
    public Long verificarBiometria(String chave) throws DAOException, ValidacaoException {
        return ((ConsultarProfissionalBiometria) this.executor.executeReturn(new ConsultarProfissionalBiometria(chave))).getCodigo();
    }

    @Override
    public void atualizaCbosEquipesDoProfissional(ProfissionalCargaHoraria profissionalCargaHoraria, boolean isCboRemovido) throws DAOException, ValidacaoException {
        this.executor.execute(new AtualizarCbosEquipesDoProfissional(profissionalCargaHoraria, isCboRemovido));
    }

    @Override
    public Equipe consultaEquipeFaturamentoPorProfissional(Profissional profissional, Empresa empresaLogado, Empresa empresaFaturamento, Atendimento atendimento) throws DAOException, ValidacaoException {
        QueryConsultaEquipeFaturamentoProfissional qcefp = (QueryConsultaEquipeFaturamentoProfissional) this.executor.executeReturn(new QueryConsultaEquipeFaturamentoProfissional(profissional, empresaLogado, empresaFaturamento, atendimento));
        return qcefp.getEquipe();
    }
}
