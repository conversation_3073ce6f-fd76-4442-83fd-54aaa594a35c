package br.com.ksisolucoes.bo.basico;

import br.com.celk.bnafar.ArmazenarCredenciaisBnafar;
import br.com.celk.bnafar.ObterTokenBnafar;
import br.com.celk.bnafar.ProcessarReenvioErrosBnafar;
import br.com.celk.bnafar.ProcessarRetornoBnafar;
import br.com.celk.bo.materiais.bnafar.dispensacao.*;
import br.com.celk.bo.materiais.bnafar.entrada.*;
import br.com.celk.bo.materiais.bnafar.estoque.*;
import br.com.celk.bo.materiais.bnafar.saida.*;
import br.com.celk.bo.materiais.dispensacao.QueryHistoricoDispensacoesPaciente;
import br.com.celk.bo.materiais.horus.SalvarListaEmpresaHorus;
import br.com.celk.horus.consulta.QueryConsultaDadosEnviadosHorus;
import br.com.celk.horus.dto.*;
import br.com.celk.horus.envio.ProcessarEnvioHorusDispensacao;
import br.com.celk.horus.envio.ProcessarEnvioHorusSaida;
import br.com.celk.horus.geracaoXml.*;
import br.com.celk.horus.webservice.*;
import br.com.celk.materiais.bnafar.consultaIntegracao.dispensacao.ConsultaIntegracaoBnafarDispensacaoDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.dispensacao.ConsultaIntegracaoBnafarDispensacaoDTOParam;
import br.com.celk.materiais.bnafar.consultaIntegracao.dispensacao.QueryConsultaBnafarDispensacao;
import br.com.celk.materiais.bnafar.consultaIntegracao.entrada.ConsultaIntegracaoBnafarEntradaDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.entrada.ConsultaIntegracaoBnafarEntradaDTOParam;
import br.com.celk.materiais.bnafar.consultaIntegracao.entrada.QueryConsultaBnafarEntrada;
import br.com.celk.materiais.bnafar.consultaIntegracao.estoque.ConsultaIntegracaoBnafarPosEstoqueDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.estoque.ConsultaIntegracaoBnafarPosEstoqueDTOParam;
import br.com.celk.materiais.bnafar.consultaIntegracao.estoque.QueryConsultaBnafarPosEstoque;
import br.com.celk.materiais.bnafar.consultaIntegracao.saida.ConsultaIntegracaoBnafarSaidaDTO;
import br.com.celk.materiais.bnafar.consultaIntegracao.saida.ConsultaIntegracaoBnafarSaidaDTOParam;
import br.com.celk.materiais.bnafar.consultaIntegracao.saida.QueryConsultaBnafarSaida;
import br.com.celk.materiais.bnafar.dto.GeracaoBnafarDTO;
import br.com.celk.materiais.bnafar.dto.QueryBnafarPosEstoqueDTO;
import br.com.celk.materiais.dispensacao.dto.HistoricoDispensacoesPacienteDTO;
import br.com.celk.materiais.dispensacao.dto.HistoricoDispensacoesPacienteDTOParam;
import br.com.celk.service.async.BnafarAssincronoQueueDTO;
import br.com.ksisolucoes.bo.BO;
import br.com.ksisolucoes.bo.basico.interfaces.dto.CadastroProdutoSolicitadoDTO;
import br.com.ksisolucoes.bo.basico.interfaces.dto.ProdutoSolicitadoDTO;
import br.com.ksisolucoes.bo.basico.interfaces.dto.ProdutoSolicitadoDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade._MaterialBasicoFacadeLocal;
import br.com.ksisolucoes.bo.basico.interfaces.facade._MaterialBasicoFacadeRemote;
import br.com.ksisolucoes.bo.basico.produtosolicitado.*;
import br.com.ksisolucoes.bo.basico.produtosolicitado.permissaoestoque.QueryValidarPermissaoGrupoEstoque;
import br.com.ksisolucoes.bo.basico.produtosolicitado.permissaoestoque.SalvarPermissaoGrupoEstoque;
import br.com.ksisolucoes.bo.command.dynamodb.UsuariosBnafar;
import br.com.ksisolucoes.bo.entradas.dispensacao.localizacaoestrutura.QueryConsultaLocalizacaoEstrutura;
import br.com.ksisolucoes.bo.entradas.estoque.centrocusto.QueryConsultaCentroCusto;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.*;
import br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia.DeletarPedidoTransferenciaConfiguracao;
import br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia.SalvarPedidoTransferenciaConfiguracao;
import br.com.ksisolucoes.bo.entradas.estoque.permissao.dto.PermissaoGrupoEstoqueDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.serie.QueryConsultaSerie;
import br.com.ksisolucoes.bo.entradas.estoque.subgruponaocontrolaestoque.SalvarSubGrupoNaoControlaEstoque;
import br.com.ksisolucoes.bo.entradas.estoque.tipodocumento.QueryConsultaTipoDocumento;
import br.com.ksisolucoes.bo.entradas.recebimento.registronotafiscal.CarregarItensXmlNotaFiscal;
import br.com.ksisolucoes.bo.materiais.bnafar.estoque.QueryBnafarPosEstoqueParam;
import br.com.ksisolucoes.bo.materiais.compras.CancelarAutorizacaoFornecimentoItem;
import br.com.ksisolucoes.bo.materiais.compras.SalvarAutorizacaoFornecimento;
import br.com.ksisolucoes.bo.materiais.compras.SalvarOrdemCompra;
import br.com.ksisolucoes.bo.materiais.compras.interfaces.dto.AutorizacaoFornecimentoDTO;
import br.com.ksisolucoes.bo.materiais.compras.interfaces.dto.OrdemCompraDTO;
import br.com.ksisolucoes.bo.materiais.horus.interfaces.dto.ConsultaDadosEnviadosHorusDTOParam;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.*;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.dto.BrasindiceDTO;
import br.com.ksisolucoes.bo.materiais.interfaces.dto.CadastroKitPedidoPacienteDTO;
import br.com.ksisolucoes.bo.materiais.kitpedidopaciente.DeletarKitPedidoPaciente;
import br.com.ksisolucoes.bo.materiais.kitpedidopaciente.SalvarKitPedidoPaciente;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.AtualizarPedidoProcessoSeparacao;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.ConfiguracaoPedidoTransferenciaDTO;
import br.com.ksisolucoes.bo.materiais.riscoproduto.QueryConsultaRiscoProduto;
import br.com.ksisolucoes.bo.materiais.riscoproduto.dto.QueryConsultaRiscoProdutoDTOParam;
import br.com.ksisolucoes.command.AsyncProcessCommand;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.materiais.ordemcompra.RelatorioImpressaoOrdemCompra;
import br.com.ksisolucoes.report.materiais.ordemcompra.RelatorioImpressaoRelacaoOrdemCompra;
import br.com.ksisolucoes.report.materiais.ordemcompra.RelatorioImpressaoResumoAutorizacaoFornecimento;
import br.com.ksisolucoes.report.materiais.ordemcompra.RelatorioResumoOrdemCompra;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioImpressaoOrdemCompraDTOParam;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioResumoAutorizacaoFornecimentoDTOParam;
import br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.ResumoRelacaoOrdemCompraDTOParam;
import br.com.ksisolucoes.report.materiais.pedidocompra.RelatorioImpressaoPedidoCompra;
import br.com.ksisolucoes.report.materiais.pedidocompra.interfaces.dto.RelatorioImpressaoPedidoCompraDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.BrasindiceProcess;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.permissao.PermissaoGrupoEstoqueItem;
import br.com.ksisolucoes.vo.financeiro.Serie;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import br.com.ksisolucoes.vo.materiais.RiscoProduto;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao;
import br.com.ksisolucoes.vo.materiais.bnafar.entrada.BnafarEntrada;
import br.com.ksisolucoes.vo.materiais.bnafar.estoque.BnafarPosEstoque;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import br.com.ksisolucoes.vo.materiais.bnafar.saida.BnafarSaida;
import br.com.ksisolucoes.vo.materiais.horus.EmpresaHorus;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.jboss.ejb3.annotation.TransactionTimeout;

import javax.ejb.Stateless;
import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Stateless
public class MaterialBasicoBO extends BO implements _MaterialBasicoFacadeLocal, _MaterialBasicoFacadeRemote {

    @Override
    public List<ProdutoSolicitadoDTO> consultarProdutoSolicitado(ProdutoSolicitadoDTOParam param) throws DAOException, ValidacaoException {
        QueryConsultaProdutoSolicitado qcpj = (QueryConsultaProdutoSolicitado) this.executor.executeReturn(new QueryConsultaProdutoSolicitado(param));
        qcpj.start();
        return qcpj.getResult();
    }

    @Override
    public Long baixarProdutoSolicitado(List<ProdutoSolicitadoDTO> lista) throws DAOException, ValidacaoException {
        return ((BaixarProdutoSolicitado) this.executor.executeReturn(new BaixarProdutoSolicitado(lista))).getNumeroBaixa();
    }

    @Override
    public Long baixarProdutoSolicitado(List<ProdutoSolicitadoDTO> lista, String justificativa) throws DAOException, ValidacaoException {
        return ((BaixarProdutoSolicitado) this.executor.executeReturn(new BaixarProdutoSolicitado(lista, justificativa))).getNumeroBaixa();
    }

    @Override
    public void entradaProdutoSolicitado(List<ProdutoSolicitadoDTO> lista) throws DAOException, ValidacaoException {
        this.executor.execute(new EntradaProdutoSolicitado(lista));
    }

    @Override
    public DataPagingResult<CentroCusto> getCentroCustoQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaCentroCusto());
    }

    @Override
    public DataPagingResult<TipoDocumento> consultarTipoDocumento(DataPaging<QueryConsultaTipoDocumentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaTipoDocumento(dataPaging.getParam()));
    }

    @Override
    public DataPagingResult<Serie> consultarSerie(DataPaging<QueryConsultaSerieDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaSerie(dataPaging.getParam()));
    }

    @Override
    public OrdemCompra salvarOrdemCompra(OrdemCompraDTO dto) throws DAOException, ValidacaoException {
        return ((SalvarOrdemCompra) this.executor.executeReturn(new SalvarOrdemCompra(dto))).getOrdemCompraSave();
    }

    @Override
    public DataReport relatorioImpressaoOrdemCompra(RelatorioImpressaoOrdemCompraDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoOrdemCompra(param));
    }

    @Override
    public DataReport relatorioImpressaoRelacaoOrdemCompra(RelatorioRelacaoOrdemCompraDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioImpressaoRelacaoOrdemCompra(param));
    }

    @Override
    public DataReport relatorioResumoOrdemCompra(ResumoRelacaoOrdemCompraDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioResumoOrdemCompra(param));
    }

    @Override
    @TransactionTimeout(3600) //timeout de duas horas, se for trocar, verifique o timeout setado no camel
    public AsyncProcess importarTabelaPrecosBrasindice(BrasindiceDTO dto) throws DAOException, ValidacaoException {
        return (AsyncProcess) ((AsyncProcessCommand) this.executorAsync.executeReturn(new ImportarTabelaPrecosBrasindice(dto))).getAsyncObject();
    }

    @Override
    public BrasindiceProcess registrarAndamentoBrasindice(Long codigoBrasindiceProcess, Integer registros, String mensagem) throws DAOException, ValidacaoException {
        return ((RegistrarAndamentoBrasindice) this.executor.executeReturn(new RegistrarAndamentoBrasindice(codigoBrasindiceProcess, registros, mensagem))).getBrasindiceProcess();
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public BrasindiceProcess registrarAndamentoBrasindiceNewTransaction(Long codigoBrasindiceProcess, Integer registros, String mensagem) throws DAOException, ValidacaoException {
        return ((RegistrarAndamentoBrasindice) this.executor.executeReturn(new RegistrarAndamentoBrasindice(codigoBrasindiceProcess, registros, mensagem))).getBrasindiceProcess();
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @TransactionTimeout(1200)
    public void salvarProdutoBrasindice(Object object, BrasindiceDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarProdutoBrasindice((List) object, dto));
    }

    @Override
    @TransactionTimeout(3600) //timeout de duas horas, se for trocar, verifique o timeout setado no camel
    public void cadastrarProcessoBrasindice(BrasindiceDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new CadastrarProcessoBrasindice(dto));
    }

    @Override
    public void removerBrasindiceProcessAdicionais() throws DAOException, ValidacaoException {
        this.executor.execute(new RemoverBrasindiceProcessAdicionais());
    }

    @Override
    public void atualizarPedidoProcessoSeparacao(PedidoTransferencia pedidoTransferencia) throws DAOException, ValidacaoException {
        this.executor.execute(new AtualizarPedidoProcessoSeparacao(pedidoTransferencia));
    }

    @Override
    public void salvarEmpresaHorus(List<EmpresaHorus> listaEmpresaHorus) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarListaEmpresaHorus(listaEmpresaHorus));
    }

    @Override
    public void sincronizarHorus(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new SincronizarHorus(dto));
    }

    @Override
    public void enviarXmlHorus(EnvioXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarXmlHorus(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void gerarXmlDispensacao(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusDispensacao(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void gerarXmlEntrada(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusEntrada(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void gerarXmlSaida(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusSaida(dto));
    }

    @Override
    public void consultarXmlHorusAllDadosMunicipio(ConsultarXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new ConsultarXmlHorusAllDadosMunicipio(dto));
    }

    @Override
    public void consultarXmlHorusDadosTemporariosMunicipio(ConsultarXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new ConsultarXmlHorusDadosTemporariosMunicipio(dto));
    }

    @Override
    public void enviarMensagemErroHorus(Throwable ex) throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarMensagemErroHorus(ex));
    }

    @Override
    public void enviarBnafarSaida() throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarBnafarSaida());
    }

    @Override
    public void reenviarBnafarSaida(Long PKBnafarSaidaIntegracao) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarSaida(PKBnafarSaidaIntegracao));
    }

    @Override
    public void reenviarBnafarSaida(List<Long> codigosBnafarSaidaIntegracaoList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarSaida(codigosBnafarSaidaIntegracaoList));
    }

    @Override
    public void reenviarBnafarSaida(ArrayList<BnafarSaida> bnafarSaidaList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarSaida(bnafarSaidaList));
    }

    @Override
    public List<BnafarSaida> queryBnafarSaidaStatusErro(Long pKBnafarSaidaIntegracao) throws DAOException, ValidacaoException {
        return (((QueryBnafarSaidaStatusErro) this.executor.executeReturn(new QueryBnafarSaidaStatusErro(pKBnafarSaidaIntegracao))).getResult());
    }

    @Override
    public List<BnafarSaida> queryBnafarSaidaStatusErro(List<Long> codigosBnafarSaidaIntegracaoList) throws DAOException, ValidacaoException {
        return (((QueryBnafarSaidaStatusErro) this.executor.executeReturn(new QueryBnafarSaidaStatusErro(codigosBnafarSaidaIntegracaoList))).getResult());
    }

    @Override
    public List<BnafarSaida> queryBnafarSaidaStatusErro(Date dataInicioProcesso, int limit) throws DAOException, ValidacaoException {
        return (((QueryBnafarSaidaStatusErro) this.executor.executeReturn(new QueryBnafarSaidaStatusErro(dataInicioProcesso, limit))).getResult());
    }

    @Override
    public void enviarMensagemErroHorus(String inconsistencias) throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarMensagemErroHorus(inconsistencias));
    }

    @Override
    public DataPagingResult consultarDadosEnviadosHorus(DataPaging<ConsultaDadosEnviadosHorusDTOParam> param) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(param, new QueryConsultaDadosEnviadosHorus(param.getParam()));
    }

    @Override
    public void salvarSubGrupoNaoControlaEstoque(List<SubGrupoNaoControlaEstoque> listaSubGrupo) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarSubGrupoNaoControlaEstoque(listaSubGrupo));
    }

    @Override
    public void salvarPedidoTransferenciaConfiguracao(ConfiguracaoPedidoTransferenciaDTO configuracaoPedidoTransferenciaDTO) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarPedidoTransferenciaConfiguracao(configuracaoPedidoTransferenciaDTO));
    }

    @Override
    public void deletarPedidoTransferenciaConfiguracao(PedidoTransferenciaConfiguracao pedidoTransferenciaConfiguracao) throws DAOException, ValidacaoException {
        this.executor.execute(new DeletarPedidoTransferenciaConfiguracao(pedidoTransferenciaConfiguracao));
    }

    @Override
    public ProdutoSolicitado salvarProdutoSolicitado(CadastroProdutoSolicitadoDTO dto) throws DAOException, ValidacaoException {
        return ((SalvarProdutoSolicitado) this.executor.executeReturn(new SalvarProdutoSolicitado(dto))).getProdutoSolicitado();
    }

    @Override
    public void deletarProdutoSolicitado(ProdutoSolicitado produtoSolicitado) throws DAOException, ValidacaoException {
        this.executor.execute(new DeletarProdutoSolicitado(produtoSolicitado));
    }

    @Override
    public PermissaoGrupoEstoque SalvarPermissaoGrupoEstoque(PermissaoGrupoEstoque permissaoGrupoEstoque, List<PermissaoGrupoEstoqueItem> lstPermissao) throws DAOException, ValidacaoException {
        return ((SalvarPermissaoGrupoEstoque) this.executor.executeReturn(new SalvarPermissaoGrupoEstoque(permissaoGrupoEstoque, lstPermissao))).getPermissaoEstoque();
    }

    @Override
    public boolean ValidarPermissaoGrupoEstoque(PermissaoGrupoEstoqueDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryValidarPermissaoGrupoEstoque) this.executor.executeReturn(new QueryValidarPermissaoGrupoEstoque(param))).getValido();
    }

    @Override
    public CarregarItensXmlNotaFiscalDTO carregarItensXmlNotaFiscal(CarregarItensXmlNotaFiscalDTO dto) throws DAOException, ValidacaoException {
        return ((CarregarItensXmlNotaFiscal) this.executor.executeReturn(new CarregarItensXmlNotaFiscal(dto))).getDTO();
    }

    @Override
    public DataPagingResult<QueryConsultaLocalizacaoEstruturaDTO> queryConsultaLocalizacaoEstrutura(DataPaging<QueryConsultaLocalizacaoEstruturaDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaLocalizacaoEstrutura(dataPaging.getParam()));
    }

    @Override
    public OrdemCompra salvarAutorizacaoFornecimento(AutorizacaoFornecimentoDTO autorizacaoFornecimentoDTO, boolean novaAutorizacao) throws ValidacaoException, DAOException {
        return ((SalvarAutorizacaoFornecimento) this.executor.executeReturn(new SalvarAutorizacaoFornecimento(autorizacaoFornecimentoDTO, novaAutorizacao))).getOrdemCompraSave();
    }

    @Override
    public void cancelarAutorizacaoFornecimentoItem(AutorizacaoFornecimentoItem autorizacaoFornecimentoItem) throws DAOException, ValidacaoException {
        this.executor.execute(new CancelarAutorizacaoFornecimentoItem(autorizacaoFornecimentoItem));
    }

    @Override
    public void deletarKitPedidoPaciente(KitPedidoPaciente kitPedidoPaciente) throws DAOException, ValidacaoException {
        this.executor.execute(new DeletarKitPedidoPaciente(kitPedidoPaciente));
    }

    @Override
    public KitPedidoPaciente salvarKitPedidoPaciente(CadastroKitPedidoPacienteDTO cadastroKitPedidoPacienteDTO) throws DAOException, ValidacaoException {
        return ((SalvarKitPedidoPaciente) this.executor.executeReturn(new SalvarKitPedidoPaciente(cadastroKitPedidoPacienteDTO))).getKitPedidoPaciente();
    }

    @Override
    public DataReport relatorioImpressaoResumoAutorizacaoFornecimento(RelatorioResumoAutorizacaoFornecimentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioImpressaoResumoAutorizacaoFornecimento(param));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void gerarXmlEntradaNew(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusEntradaNew(dto));
    }

    @Override
//    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @TransactionTimeout(50000)
    public void gerarXmlSaidaNew(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusSaidaNew(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    @TransactionTimeout(50000)
    public void gerarXmlDispensacaoNew(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusDispensacaoNew(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void gerarXmlPosicaoEstoqueNew(GeracaoXmlHorusDTO dto) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new GerarXmlHorusPosicaoEstoqueNew(dto));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void consultarXmlHorus(Long codigo, URL enderecoEnvioHorus, String usuario, String senha, List<Long> listCodigoRegistros) throws DAOException, ValidacaoException {
        this.executor.execute(new ConsultarXmlHorus(codigo, enderecoEnvioHorus, usuario, senha, listCodigoRegistros));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public SincronizacaoHorusProcessoEnvio consultarXmlHorusNew(Long codigo, URL enderecoEnvioHorus, String usuario, String senha) throws DAOException, ValidacaoException {
        return ((ConsultarXmlHorusNew) this.executor.executeReturn(new ConsultarXmlHorusNew(codigo, enderecoEnvioHorus, usuario, senha))).getSincronizacaoHorusProcessoEnvio();
    }

    @Override
    public void consultarValidacaoXmlHorusNew(Long codigo) throws DAOException, ValidacaoException {
        this.executor.execute(new ConsultarValidacaoXmlHorusNew(codigo));
    }

    @Override
    public ProcessarEnvioHorusDTO processarEnvioHorusDispensacao(ProcessarEnvioHorusDTO dto, List<QueryXmlHorusDispensacaoDTO> itens) throws DAOException, ValidacaoException {
        return ((ProcessarEnvioHorusDispensacao) this.executor.executeReturn(new ProcessarEnvioHorusDispensacao(dto, itens))).getDto();
    }

    @Override
    public ProcessarEnvioHorusDTO processarEnvioHorusSaida(ProcessarEnvioHorusDTO dto, List<QueryXmlHorusSaidaDTO> itens) throws DAOException, ValidacaoException {
        return ((ProcessarEnvioHorusSaida) this.executor.executeReturn(new ProcessarEnvioHorusSaida(dto, itens))).getDto();
    }

    @Override
    public DataReport relatorioImpressaoPedidoCompra(RelatorioImpressaoPedidoCompraDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoPedidoCompra(param));
    }

    @Override
    public List<HistoricoDispensacoesPacienteDTO> getHistoricoDispensacoesPaciente(HistoricoDispensacoesPacienteDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryHistoricoDispensacoesPaciente) this.executor.executeReturn(new QueryHistoricoDispensacoesPaciente(param))).getResult();
    }

    @Override
    public void gerarBnafarEntrada(GeracaoBnafarDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarBnafarEntrada(dto));
    }

    @Override
    public void enviarBnafarEntrada() throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarBnafarEntrada());
    }

    @Override
    public void reenviarBnafarEntrada(Long PKBnafarEntradaIntegracao) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarEntrada(PKBnafarEntradaIntegracao));
    }

    @Override
    public void reenviarBnafarEntrada(List<Long> codigosBnafarEntradaIntegracaoList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarEntrada(codigosBnafarEntradaIntegracaoList));
    }

    @Override
    public void reenviarBnafarEntrada(ArrayList<BnafarEntrada> bnafarEntradaList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarEntrada(bnafarEntradaList));
    }

    @Override
    public List<BnafarEntrada> queryBnafarEntradaStatusGerado(Date dateInicioIntegracaoBnafar, int limit) throws DAOException, ValidacaoException {
        return (((QueryBnafarEntradaStatusGerado) this.executor.executeReturn(new QueryBnafarEntradaStatusGerado(dateInicioIntegracaoBnafar, limit))).getResult());
    }

    @Override
    public List<BnafarEntrada> queryBnafarEntradaStatusErro(Long PKBnafarEntradaIntegracao) throws DAOException, ValidacaoException {
        return (((QueryBnafarEntradaStatusErro) this.executor.executeReturn(new QueryBnafarEntradaStatusErro(PKBnafarEntradaIntegracao))).getResult());
    }

    @Override
    public List<BnafarEntrada> queryBnafarEntradaStatusErro(List<Long> codigoBnafarEntradaIntegracaoList) throws DAOException, ValidacaoException {
        return (((QueryBnafarEntradaStatusErro) this.executor.executeReturn(new QueryBnafarEntradaStatusErro(codigoBnafarEntradaIntegracaoList))).getResult());
    }

    @Override
    public List<BnafarEntrada> queryBnafarEntradaStatusErro(Date dataInicioProcesso, int limit) throws DAOException, ValidacaoException {
        return (((QueryBnafarEntradaStatusErro) this.executor.executeReturn(new QueryBnafarEntradaStatusErro(dataInicioProcesso, limit))).getResult());
    }

    @Override
    public void processarRetornoBnafarWorker(BnafarAssincronoQueueDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new ProcessarRetornoBnafar(dto));
    }

    @Override
    public Boolean obterTokenBnafar(String login, String senha) throws DAOException, ValidacaoException {
        return (((ObterTokenBnafar) this.executor.executeReturn(new ObterTokenBnafar(login, senha))).getRetorno());
    }

    @Override
    public void armazenarCredenciaisBnafar(UsuariosBnafar usuariosBnafar) throws DAOException, ValidacaoException {
        this.executor.execute(new ArmazenarCredenciaisBnafar(usuariosBnafar));
    }

    @Override
    public void alterarBnafarEntradaIntegracao(Long codigo, String json) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarBnafarEntradaIntegracao(codigo, json));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarEntrada(Long codigo, Long codigoElo) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarEntrada(codigo, codigoElo));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarEntrada(Long codigo, Long codigoElo, Long status) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarEntrada(codigo, codigoElo, status));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void retornarStatusBnafarEntrada(Long codigo) throws DAOException, ValidacaoException {
        this.executor.execute(new RetornarStatusBnafarEntrada(codigo));
    }

    @Override
    public void retornarStatusBnafarSaida(Long codigo) throws DAOException, ValidacaoException {
        this.executor.execute(new RetornarStatusBnafarSaida(codigo));
    }

    @Override
    public void gerarBnafarSaida(GeracaoBnafarDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarBnafarSaida(dto));
    }

    @Override
    public void alterarBnafarSaidaIntegracao(Long codigo, String toJson) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarBnafarSaidaIntegracao(codigo, toJson));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarSaida(Long codigo, Long codigoElo) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarSaida(codigo, codigoElo));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarSaida(Long codigo, Long codigoElo, Long status) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarSaida(codigo, codigoElo, status));
    }

    @Override
    public List<BnafarSaida> queryBnafarSaida(Date dateInicioIntegracaoBnafar, int limit) throws DAOException, ValidacaoException {
        return ((QueryBnafarSaida) this.executor.executeReturn(new QueryBnafarSaida(dateInicioIntegracaoBnafar, limit))).getResult();

    }

    @Override
    public void gerarBnafarDispensacao(GeracaoBnafarDTO dto) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarBnafarDispensacao(dto));
    }

    @Override
    public void atualizarBnafarDispensacao() throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarBnafarDispensacao());
    }

    @Override
    public void gerarEnviarBnafarPosEstoque() throws DAOException, ValidacaoException {
        this.executor.execute(new GerarEnviarBnafarPosEstoque());
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void enviarBnafarPosEstoque() throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarBnafarPosEstoque());
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void reenviarBnafarPosEstoque(ArrayList<BnafarPosEstoque> bnafarPosEstoques) throws DAOException, ValidacaoException {
        this.executor.execute(new EnviarBnafarPosEstoque(bnafarPosEstoques));
    }

    @Override
    public List<BnafarPosEstoque> queryBnafarPosEstoque(QueryBnafarPosEstoqueParam param) throws DAOException, ValidacaoException {
        return ((QueryBnafarPosEstoque) this.executor.executeReturn(new QueryBnafarPosEstoque(param))).getResult();
    }

    @Override
    public List<BnafarPosEstoque> queryBnafarPosEstoqueErro(QueryBnafarPosEstoqueParam param) throws DAOException, ValidacaoException {
        return ((QueryBnafarPosEstoque) this.executor.executeReturn(new QueryBnafarPosEstoque(param))).getResult();
    }

    @Override
    public List<QueryBnafarPosEstoqueDTO> queryBnafarPosEstoqueOrigem(Date data) throws DAOException, ValidacaoException {
        return ((QueryBnafarPosEstoqueOrigem) this.executor.executeReturn(new QueryBnafarPosEstoqueOrigem(data))).getResultado();
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void enviarMensagemErroBnafarPosEstoque(ArrayList<String> sMensagemErros) throws ValidacaoException, DAOException {
        this.executorAsync.execute(new AlertarErroBnafarPosEstoque(sMensagemErros));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarPosEstoque(Long codigo, Long codigoElo, BnafarHelper.StatusRegistro status) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarPosEstoque(codigo, codigoElo, status));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void cancelarStatusBnafarPosEstoqueErro(Long codigo) throws DAOException, ValidacaoException {
        this.executor.execute(new CancelarBnafarPosEstoque(codigo));
    }

    @Override
    public void alterarBnafarPosEstoqueIntegracao(Long codigo, String json) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarBnafarPosEstoqueIntegracao(codigo, json));
    }

    @Override
    public void retornarStatusBnafarPosEstoque(Long codigo, Long pKBnafarPosEstoqueElo, BnafarHelper.StatusRegistro status) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarPosEstoque(codigo, pKBnafarPosEstoqueElo, status));
    }

    @Override
    public List<BnafarDispensacao> queryBnafarDispensacaoStatusGerado(Date dateInicioIntegracaoBnafar, int limit) throws DAOException, ValidacaoException {
        return (((QueryBnafarDispensacaoStatusGerado) this.executor.executeReturn(new QueryBnafarDispensacaoStatusGerado(dateInicioIntegracaoBnafar, limit))).getResult());
    }

    @Override
    public List<BnafarDispensacao> queryBnafarDispensacaoStatusErro(Long PKBnafarDispensacaoIntegracao) throws DAOException, ValidacaoException {
        return (((QueryBnafarDispensacaoStatusErro) this.executor.executeReturn(new QueryBnafarDispensacaoStatusErro(PKBnafarDispensacaoIntegracao))).getResult());
    }

    @Override
    public List<BnafarDispensacao> queryBnafarDispensacaoStatusErro(Date dataInicioProcesso, int limit) throws DAOException, ValidacaoException {
        return (((QueryBnafarDispensacaoStatusErro) this.executor.executeReturn(new QueryBnafarDispensacaoStatusErro(dataInicioProcesso, limit))).getResult());
    }


    @Override
    public List<BnafarDispensacao> queryBnafarDispensacaoStatusErro(List<Long> codigosBnafarEntradaIntegracaoList) throws DAOException, ValidacaoException {
        return (((QueryBnafarDispensacaoStatusErro) this.executor.executeReturn(new QueryBnafarDispensacaoStatusErro(codigosBnafarEntradaIntegracaoList))).getResult());
    }


    @Override
    public void alterarBnafarDispensacaoIntegracao(Long codigo, String json) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarBnafarDispensacaoIntegracao(codigo, json));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarDispensacao(Long codigo, Long PKBnafarDispensacaoElo) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarDispensacao(codigo, PKBnafarDispensacaoElo));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void alterarStatusBnafarDispensacao(Long codigo, Long PKBnafarDispensacaoElo, Long status) throws DAOException, ValidacaoException {
        this.executor.execute(new AlterarStatusBnafarDispensacao(codigo, PKBnafarDispensacaoElo, status));
    }

    @Override
    public void reenviarBnafarDispensacao(Long PKBnafarDispensacaoIntegracao) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarDispensacao(PKBnafarDispensacaoIntegracao));
    }

    @Override
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public void reenviarBnafarDispensacao(ArrayList<BnafarDispensacao> bnafarDispensacaoList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarDispensacao(bnafarDispensacaoList));
    }

    @Override
    public void reenviarBnafarDispensacao(List<Long> codigosBnafarEntradaIntegracaoList) throws DAOException, ValidacaoException {
        this.executor.execute(new ReenviarBnafarDispensacao(codigosBnafarEntradaIntegracaoList));
    }

    public void retornarStatusBnafarDispensacao(Long codigo) throws DAOException, ValidacaoException {
        this.executor.execute(new RetornarStatusBnafarDispensacao(codigo));
    }

    @Override
    public DataPagingResult<ConsultaIntegracaoBnafarEntradaDTO> queryConsultaIntegracaoBnafarEntrada(DataPaging<ConsultaIntegracaoBnafarEntradaDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaBnafarEntrada(dataPaging.getParam()));
    }

    @Override
    public DataPagingResult<ConsultaIntegracaoBnafarSaidaDTO> queryConsultaIntegracaoBnafarSaida(DataPaging<ConsultaIntegracaoBnafarSaidaDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaBnafarSaida(dataPaging.getParam()));
    }

    @Override
    public DataPagingResult<ConsultaIntegracaoBnafarDispensacaoDTO> queryConsultaIntegracaoBnafarDispensacao(DataPaging<ConsultaIntegracaoBnafarDispensacaoDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaBnafarDispensacao(dataPaging.getParam()));
    }

    @Override
    public DataPagingResult<ConsultaIntegracaoBnafarPosEstoqueDTO> queryConsultaIntegracaoBnafarPosicaoEstoque(DataPaging<ConsultaIntegracaoBnafarPosEstoqueDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaBnafarPosEstoque(dataPaging.getParam()));
    }

    @Override
    public void processarReenvioErrosBnafar(BnafarHelper.TipoSincronizacao tipoSincronizacao) throws DAOException, ValidacaoException {
        this.executorAsync.execute(new ProcessarReenvioErrosBnafar(tipoSincronizacao), BnafarHelper.getDescricaoProcessoAssincrono(tipoSincronizacao));
    }

    @Override
    public DataPagingResult<RiscoProduto> queryConsultaRiscoProduto(DataPaging<QueryConsultaRiscoProdutoDTOParam> dataPaging) throws DAOException, ValidacaoException {
        return this.executor.executeQueryPager(dataPaging, new QueryConsultaRiscoProduto(dataPaging.getParam()));
    }
}
