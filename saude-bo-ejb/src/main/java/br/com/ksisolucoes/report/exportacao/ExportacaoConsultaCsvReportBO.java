package br.com.ksisolucoes.report.exportacao;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.exportacao.consulta.dto.RelatorioExportacaoConsultaCsvDTOParam;
import br.com.ksisolucoes.report.exportacao.consulta.interfaces.facade._ExportacaoConsultaCsvReportFacadeLocal;
import br.com.ksisolucoes.report.exportacao.consulta.interfaces.facade._ExportacaoConsultaCsvReportFacadeRemote;

import javax.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ExportacaoConsultaCsvReportBO extends BOGenericImpl implements _ExportacaoConsultaCsvReportFacadeLocal, _ExportacaoConsultaCsvReportFacadeRemote {
    @Override
    public DataReport relatorioConsultas(RelatorioExportacaoConsultaCsvDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioExportacaoConsultaCsv(param));
    }
}
