package br.com.ksisolucoes.report.hospital;

import br.com.celk.unidadesaude.esus.relatorios.dto.RelatorioReservaLeitoAihDTOParam;
import br.com.ksisolucoes.bo.BO;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.hospital.interfaces.dto.*;
import br.com.ksisolucoes.report.hospital.interfaces.dto.tiss.RelatorioImpressaoGuiasTissDTOParam;
import br.com.ksisolucoes.report.hospital.interfaces.facade._HospitalReportFacadeLocal;
import br.com.ksisolucoes.report.hospital.interfaces.facade._HospitalReportFacadeRemote;
import br.com.ksisolucoes.report.hospital.tiss.RelatorioImpressaoGuiasTiss;

import javax.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class HospitalReportBO extends BO implements _HospitalReportFacadeLocal, _HospitalReportFacadeRemote {

    @Override
    public DataReport relatorioRelacaoAtendimentosHpt(RelatorioRelacaoAtendimentosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioRelacaoAtendimentosHpt(param));
    }

    @Override
    public DataReport relatorioOcupacaoHospitalar(RelatorioOcupacaoHospitalarDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioOcupacaoHospitalar(param));
    }

    @Override
    public DataReport relatorioMapaDietas(RelatorioMapaDietasDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioMapaDietas(param));
    }

    @Override
    public DataReport relatorioLiberacaoLeitos(RelatorioLiberacaoLeitoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioLiberacaoLeitos(param));
    }

    @Override
    public DataReport relatorioMapaLeitos(RelatorioMapaLeitosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioMapaLeitos(param));
    }

    @Override
    public DataReport relatorioFaturamento(RelatorioFaturamentoDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioFaturamento(param));
    }

    @Override
    public DataReport relatorioAgendamentosCirurgicosAsync(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioAgendamentosCirurgicos(param));
    }

    @Override
    public DataReport relatorioAgendamentosCirurgicos(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioAgendamentosCirurgicos(param));
    }

    @Override
    public DataReport relatorioExtratoContaFinanceira(RelatorioExtratoContaFinanceiraDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioExtratoContaFinanceira(param));
    }

    @Override
    public DataReport comprovanteAgendamentosCirurgicos(RelatorioAgendamentosCirurgicosDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new ComprovanteAgendamentoCirurgico(param));
    }

    @Override
    public DataReport relatorioImpressaoGuiasTiss(RelatorioImpressaoGuiasTissDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoGuiasTiss(param, true));
    }

    @Override
    public DataReport relatorioImpressaoGuiasTissPortrait(RelatorioImpressaoGuiasTissDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoGuiasTiss(param, false));
    }

    public DataReport relatorioImpressaoEspelhoAIH(RelatorioImpressaoEspelhoAIHDTOParam param) throws ReportException {
        return this.executor.executeDataReport(new RelatorioImpressaoEspelhoAIH(param));
    }

    @Override
    public DataReport relatorioFilaAih(RelatorioFilaAihDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioFilaAih(param));
    }

    @Override
    public DataReport relatorioReservaLeitoAih(RelatorioReservaLeitoAihDTOParam param) throws ReportException {
        return this.executorAsync.executeDataReport(new RelatorioReservaLeitoAih(param));
    }
}
