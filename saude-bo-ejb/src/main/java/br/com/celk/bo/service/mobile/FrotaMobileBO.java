package br.com.celk.bo.service.mobile;

import br.com.celk.bo.service.mobile.interfaces.facade._FrotaMobileFacadeLocal;
import br.com.celk.bo.service.mobile.interfaces.facade._FrotaMobileFacadeRemote;
import br.com.celk.bo.service.rest.frota.DiarioBordoVeiculoRestDTO;
import br.com.celk.bo.service.rest.frota.ImportarPassageirosRestDTO;
import br.com.celk.bo.service.rest.frota.MotoristaRestDTO;
import br.com.celk.bo.service.rest.frota.RotasRestDTO;
import br.com.celk.bo.service.rest.frota.RoteiroPassageiroDTO;
import br.com.celk.bo.service.rest.frota.RoteiroViagemDTO;
import br.com.celk.bo.service.rest.frota.UsuarioFrotaRestDTO;
import br.com.celk.bo.service.rest.frota.VeiculoRestDTO;
import br.com.celk.services.frotamobile.ImportarDiariosBordo;
import br.com.celk.services.frotamobile.GerarMotoristas;
import br.com.celk.services.frotamobile.GerarRoteiroPassageiro;
import br.com.celk.services.frotamobile.GerarRoteiroViagem;
import br.com.celk.services.frotamobile.GerarUsuarios;
import br.com.celk.services.frotamobile.ImportarPassageiros;
import br.com.celk.services.frotamobile.ImportarRotas;
import br.com.celk.services.frotamobile.QueryExportarVeiculos;
import br.com.celk.services.frotamobile.ValidarUsuario;
import br.com.ksisolucoes.bo.BO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import javax.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FrotaMobileBO extends BO implements _FrotaMobileFacadeLocal, _FrotaMobileFacadeRemote{

    @Override
    public List<VeiculoRestDTO> consultarVeiculos() throws DAOException, ValidacaoException {
        QueryExportarVeiculos query = (QueryExportarVeiculos) this.executor.executeReturn(new QueryExportarVeiculos());
        return query.getResult();
    }

    @Override
    public List<MotoristaRestDTO> consultarMotoristas() throws DAOException, ValidacaoException {
       GerarMotoristas command = (GerarMotoristas) this.executor.executeReturn(new GerarMotoristas());
       return command.getResult();
    }

    @Override
    public List<UsuarioFrotaRestDTO> consultaUsuarios() throws DAOException, ValidacaoException {
        GerarUsuarios command = (GerarUsuarios) this.executor.executeReturn(new GerarUsuarios());
        return command.getResult();
    }

    @Override
    public void importarViagens(List<DiarioBordoVeiculoRestDTO> viagens, String ipClient) throws DAOException, ValidacaoException {
        this.executor.execute(new ImportarDiariosBordo(viagens, ipClient));
    }

    @Override
    public UsuarioFrotaRestDTO validateUser(UsuarioFrotaRestDTO user) throws DAOException, ValidacaoException {
        return ((ValidarUsuario) this.executor.executeReturn(new ValidarUsuario(user))).getUsuarioAutenticado();
    }

    @Override
    public void importarViagensPontos(List<RotasRestDTO> viagensPontos, String ipClient) throws DAOException, ValidacaoException {
        this.executor.execute(new ImportarRotas(viagensPontos, ipClient));
    }

    @Override
    public List<RoteiroViagemDTO> consultaRoteiroViagem() throws DAOException, ValidacaoException {
        return ((GerarRoteiroViagem) this.executor.executeReturn(new GerarRoteiroViagem())).getResult();
    }

    @Override
    public List<RoteiroPassageiroDTO> consultaRoteiroPassageiro() throws DAOException, ValidacaoException {
         return ((GerarRoteiroPassageiro) this.executor.executeReturn(new GerarRoteiroPassageiro())).getResult();
    }

    @Override
    public void importarPassageiros(List<ImportarPassageirosRestDTO> dto, String ipClient) throws DAOException, ValidacaoException {
        this.executor.execute(new ImportarPassageiros(dto, ipClient));
    }
    
}
