package br.com.celk.bo.service.appcidadao;

import br.com.celk.appcidadao.dto.MedicamentoContinuoDTO;
import br.com.celk.appcidadao.dto.agendamento.AgendamentoPacienteDTO;
import br.com.celk.appcidadao.dto.agendamento.QueryAgendamentoHistoricoDTOParam;
import br.com.celk.appcidadao.dto.exame.ExameDTO;
import br.com.celk.appcidadao.dto.vacina.QueryConsultaVacinaCalendarioDTOParam;
import br.com.celk.appcidadao.dto.vacina.QueryConsultaVacinaPacienteDTOParam;
import br.com.celk.appcidadao.dto.vacina.VacinaCalendarioDTO;
import br.com.celk.appcidadao.dto.vacina.VacinaDTO;
import br.com.celk.bo.appcidadao.QueryConsultaExamesPaciente;
import br.com.celk.bo.appcidadao.QueryConsultaMedicamentosPaciente;
import br.com.celk.bo.appcidadao.agendamento.QueryAgendamentoHistorico;
import br.com.celk.bo.appcidadao.interfaces.facade.paciente._PacienteFacadeLocal;
import br.com.celk.bo.appcidadao.interfaces.facade.paciente._PacienteFacadeRemote;
import br.com.celk.bo.appcidadao.vacina.QueryConsultaVacinaAprazamento;
import br.com.celk.bo.appcidadao.vacina.QueryConsultaVacinaCalendario;
import br.com.celk.bo.appcidadao.vacina.QueryConsultaVacinaPaciente;
import br.com.ksisolucoes.bo.BO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ejb.Stateless;
import java.util.List;

/**
 * <AUTHOR> Santos
 */
@Stateless
public class PacienteBO extends BO implements _PacienteFacadeLocal, _PacienteFacadeRemote {
    @Override
    public List<MedicamentoContinuoDTO> findMedicamentosByPacienteId(Long pacienteId) throws ValidacaoException, DAOException {
        return ((QueryConsultaMedicamentosPaciente) this.executor.executeReturn(new QueryConsultaMedicamentosPaciente(pacienteId))).getMedicamentosContinuoDTOS();
    }

    @Override
    public List<ExameDTO> findExamesByPacienteId(Long pacienteId) throws ValidacaoException, DAOException {
        return ((QueryConsultaExamesPaciente) this.executor.executeReturn(new QueryConsultaExamesPaciente(pacienteId))).getExamesDTOS();
    }

    @Override
    public List<AgendamentoPacienteDTO> findAgendamentosByPaciente(QueryAgendamentoHistoricoDTOParam param) throws ValidacaoException, DAOException {
        return ((QueryAgendamentoHistorico) this.executor.executeReturn(new QueryAgendamentoHistorico(param))).getResultado();
    }

    @Override
    public List<VacinaDTO> findVacinasByPaciente(QueryConsultaVacinaPacienteDTOParam param) throws ValidacaoException, DAOException {
        return ((QueryConsultaVacinaPaciente) this.executor.executeReturn(new QueryConsultaVacinaPaciente(param))).getResultado();
    }

    @Override
    public List<VacinaCalendarioDTO> findVacinasAprazadasPaciente(QueryConsultaVacinaPacienteDTOParam param) throws ValidacaoException, DAOException {
        return ((QueryConsultaVacinaAprazamento) this.executor.executeReturn(new QueryConsultaVacinaAprazamento(param))).getResultado();
    }

    @Override
    public List<VacinaCalendarioDTO> findVacinasCalendarioPaciente(QueryConsultaVacinaCalendarioDTOParam param) throws ValidacaoException, DAOException {
        return ((QueryConsultaVacinaCalendario) this.executor.executeReturn(new QueryConsultaVacinaCalendario(param))).getResultado();
    }
}
