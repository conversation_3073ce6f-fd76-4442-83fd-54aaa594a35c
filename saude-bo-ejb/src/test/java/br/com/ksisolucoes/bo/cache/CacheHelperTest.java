package br.com.ksisolucoes.bo.cache;

import br.com.celk.bo.cache.CacheHelper;
import br.com.celk.bo.cache.CacheTenant;
import br.com.celk.bo.cache.CacheType;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Modulos;
import org.infinispan.Cache;
import org.infinispan.manager.DefaultCacheManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.concurrent.Callable;

import static junit.framework.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class CacheHelperTest {

    private static final String CALLABLE_VALUE = "inside";

    private static final String CACHED_VALUE = "Test";

    @InjectMocks
    private CacheHelper cacheHelper;
    
    @Mock
    private DefaultCacheManager cacheManager;
    
    @Mock
    private Cache<Object, Object> cache;
    
    @Mock
    private Callable<String> callString;
    
    @Test
    public void testCachedValue() throws Exception {
        assertCachedValue(true);
    }
    
    @Test
    public void testUncachedValue() throws Exception {
        assertCachedValue(false);
    }

    private void assertCachedValue(boolean hasCache) throws Exception {
        when(cacheManager.getCache(eq(CacheType.MODULO.name()))).thenReturn(cache);
        CacheTenant cacheTenant = new CacheTenant(Modulos.AGENDAMENTO, TenantContext.getContext());
        when(cache.get(eq(cacheTenant))).thenReturn(CACHED_VALUE);
        when(cache.containsKey(eq(cacheTenant))).thenReturn(hasCache);

        when(callString.call()).thenReturn(CALLABLE_VALUE);
        
        cacheHelper.initCaches();
        
        String retorno = cacheHelper.getCachedOrBuild(CacheType.MODULO.name(), Modulos.AGENDAMENTO, callString);
        assertEquals(hasCache ? CACHED_VALUE : CALLABLE_VALUE, retorno);
        verify(callString, times(hasCache ? 0 : 1)).call();
    }
}
