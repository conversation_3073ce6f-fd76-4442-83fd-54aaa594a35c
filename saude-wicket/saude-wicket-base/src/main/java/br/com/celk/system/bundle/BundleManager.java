package br.com.celk.system.bundle;

import org.apache.commons.lang.SystemUtils;
import org.apache.wicket.Application;
import org.apache.wicket.Component;
import org.apache.wicket.Localizer;
import org.apache.wicket.model.StringResourceModel;

import java.util.MissingResourceException;

/**
 * <AUTHOR>
 */
public class BundleManager {

    private static Application getApplication() {
        return Application.get();
    }

    private static Localizer getLocalizer() {
        return getApplication().getResourceSettings().getLocalizer();
    }

    public static String getString(String key) {
        try {
            return getLocalizer().getString(key, null);
        } catch (MissingResourceException e) {
            return getString("msg_resource_bundle_exception") + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    public static String getString(String key, Component component) {
        return getLocalizer().getString(key, component, "");
    }

    public static String getString(String key, Component component, Object... parameters) {
        return new StringResourceModel(key, component, null, parameters).getString();
    }

    public static String getString(String key, Object... parameters) {
        return new StringResourceModel(key, null, parameters).getString();
    }

    public static String getStringWithDefault(String key, String defaultValue) {
        return getLocalizer().getString(key, null, defaultValue);
    }

}
