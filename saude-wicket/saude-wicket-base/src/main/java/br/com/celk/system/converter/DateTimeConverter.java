package br.com.celk.system.converter;

import br.com.ksisolucoes.util.Data;
import java.util.Date;
import java.util.Locale;
import org.apache.wicket.util.convert.IConverter;

/**
 *
 * <AUTHOR>
 */
public class DateTimeConverter implements IConverter<Date> {

    private static final int NUMB_DIGITS_IN_DATE = 8;

    public Date convertToObject(String dateStr, Locale locale) {
        if (dateStr.contains(":")) {
            return Data.parserDateHour(dateStr);
        }

        return (getNumberOfDigits(dateStr) >= NUMB_DIGITS_IN_DATE) ? Data.parserDate(dateStr) : null;
    }

    public String convertToString(Date c, Locale locale) {
        return Data.formatarDataHora(c);
    }

    private int getNumberOfDigits(String dateStr) {
        return dateStr.replaceAll("\\D", "").length();
    }
}
