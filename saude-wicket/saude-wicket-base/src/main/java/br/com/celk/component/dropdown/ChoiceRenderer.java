package br.com.celk.component.dropdown;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import java.util.HashMap;
import java.util.Map;
import org.apache.wicket.markup.html.form.IChoiceRenderer;

/**
 *
 * <AUTHOR>
 */
public class ChoiceRenderer<T> implements IChoiceRenderer<T> {

    private Map<T, String> values = new HashMap<T, String>();

    public ChoiceRenderer(Map<T, String> values) {
        this.values = values;
    }
    
    public Object getDisplayValue(T object) {
        return values.get(object);
    }

    public String getIdValue(T object, int index) {
        if (object!=null) {
            if (object instanceof CodigoManager && ((CodigoManager)object).getCodigoManager() != null) {
                return ((CodigoManager)object).getCodigoManager().toString();
            }
            return object.toString();
        }
        return "";
    }

}
