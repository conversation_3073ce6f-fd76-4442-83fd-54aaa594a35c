package br.com.celk.component.behavior.attribute;

import org.apache.wicket.Component;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.markup.ComponentTag;

/**
 *
 * <AUTHOR>
 */
public class AttributeRemover extends Behavior{

    private String attribute;
    private String value;

    public AttributeRemover(String attribute) {
        this.attribute = attribute;
    }

    public AttributeRemover(String attribute, String value) {
        this.attribute = attribute;
        this.value = value;
    }
    
    @Override
    public void onComponentTag(Component component, ComponentTag tag) {
        if (value==null) {
            tag.remove(attribute);
        } else {
            String attributeValue = tag.getAttribute(attribute);
           
            if (attributeValue!=null) {
                attributeValue.replace(value, "");
            }
            
            tag.put(attribute, attributeValue);
        }
    }
    
}
