package br.com.celk.component.authotization;

import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class AuthorizedUserPanel extends Panel{

    public AuthorizedUserPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        AbstractSessaoAplicacao authorizedSession = ApplicationSession.get().getAuthorizedSession();
        
        add(new Label("nomeUsuario", authorizedSession.<Usuario>getUsuario().getNome()));
//        add(new AbstractAjaxLink("btnRemoverAutorizacao") {
//
//            @Override
//            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                getTemplatePage().getAuthorizationController().removerAutorizacao(target);
//            }
//        });
    }
    
}
