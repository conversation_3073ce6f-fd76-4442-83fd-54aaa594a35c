# Resource Bundle file.
#
login=Login
usuario=Usuário
formaBusca=Forma da Busca
senha=Senha
confirmar_senha=Confirmar Senha
agendamento=Agendamento
consulta=Consulta
agenda=Agenda
enviar=Enviar
consulta_agenda=Consulta de Agendas
sair=Sair
data=Data
tipo_atendimento=Tipo Atendimento
paciente=Paciente
ultimaOcorrencia=Última Ocorrência
profissional=Profissional
detalhes_agendamento=Detalhes do Agendamento
detalhes=Detalhes
dados_paciente=Dados do Paciente
dados_agendamento=Dados do Agendamento
nome_completo_mae=Nome completo da Mãe
nome_completo_pai=Nome completo do Pai
identidade=Identidade
cpf=CPF
data_nascimento=Data Nascimento
idade=Idade
demonstrativoProdutos=Demonstratrivo de Produtos
analiseEstoque=Análise do Estoque
unidade_agendamento=Unidade Agendamento
unidade_atendimento=Unidade Atendimento
data_agendamento=Data Agendamento
data_atendimento=Data Atendimento
data_confirmacao=Data Confirmação
data_cancelamento=Data Cancelamento
ordem_atendimento=Ordem Atendimento
usuario_confirmacao=Usuário Confirmação
usuario_cancelamento=Usuário Cancelamento
equipamento=Equipamento
nao_confirmado=Não Confirmado
confirmado=Confirmado
cancelado=Cancelado
tipoProduto=Tipo Produto
todos=Todos
procurar=Procurar
data_inicial=Data Inicial
data_final=Data Final
natureza_procura=Natureza Procura
selecione_natureza_procura=Selecione uma Natureza Procura
selecione_tipo_atendimento=Selecione um Tipo Atendimento
autenticar=Autenticar
unidade=Unidade
usuario_invalido=Usuário Inválido!
bem_vindo_X=Bem Vindo, {0}
autentique_usuario=Autentique o Usuário
sessao_expirou=Sua sessão expirou, autentique-se novamente para acessar o sistema.
ok=Ok
aviso=Aviso
cadastro_agendamentos=Cadastro de Agendamentos
agenda_profissional=Agenda Profissional
horarios=Horários
visibilidade=Visibilidade
observacao=Observação
avancar=Avançar
voltar=Voltar
assinarDigitalmente= Assinar Digitalmente
consultarProntuario=Consultar Prontuário
tipo_agenda=Tipo Agenda
vigencia=Vigência
recomendacoes=Recomendações
a=a
hora_inicio=Hora Início
hora_termino=Hora Término
sala=Sala
atendimentos=Atendimentos
tempo_medio=Tempo Médio
quantidade_atendimentos=Quantidade Atendimentos
min=Min.
adicionar=Adicionar
dia=Dia
cadastro=Cadastro
codigo=Código
descricao=Descrição
pesquisar=Pesquisar
empresa=Unidade
nenhum_registro=Nenhum Registro
pagina_inicial=Página Inicial
nome=Nome
agendar=Agendar
fechar=Fechar
inicio=Início
cota_semanal=Cota Semanal
cota_utilizada=Cota Utilizada
vagas=Vagas
marcado=Marcado
informe_paciente=Informe o Paciente!
agendado_com_sucesso=Agendado com Sucesso!
nome_mae=Nome da Mãe
rg=RG
numero_cartao=Número Cartão
tipo_cartao=Tipo Cartão
informe_natureza_procura=Informe a Natureza Procura!
informe_tipo_atendimento=Informe o Tipo Atendimento!
central_agendamento=Central Agendamento
perfil_usuarios_atendidos=Perfil dos Usuários Atendidos
sexo=Sexo
quantidade_abv=Quant.
faixaEtaria=Faixa Etária
localizacao=Localização
localidade=Localidade
informacoes=Informações
informacoes_paciente=Informações do Paciente
usuario_sus=Usuário SUS
cadastro_atividade=Cadastro de Atividade
basico=Básico
bundle=Bundle
modulo_padrao=Módulo Padrão
empresa_padrao=Empresa Padrão
email=E-mail
menu_pai=Menu Pai
cadastro_menu=Cadastro de Menus
consulta_menu=Consulta Menu
programa=Programa
atendimento=Atendimento
atividade=Atividade
geral=Geral
estatisticas=Estatísticas
medicamentos_dispensados=Medicamentos Dispensados
filtrar=Filtar
exames_realizados=Exames Realizados
estatisticas_atendimento=Estatísticas dos Atendimentos
procedimentos_realizados=Procedimentos Realizados
salvar=Salvar
cancelar=Cancelar
novo=Novo
editar=Editar
excluir=Excluir
administracao=Administração
configuracao=Configuração
alterar_unidade=Alterar Unidade
cadastro_usuario=Cadastro de Usuario
consulta_usuario=Consulta de Usuario
cadastro_programa=Cadastro de Programa
consulta_programa=Consulta de Programa
url=Url
icone=Ícone
nivel=Nível
status=Status
cadastros=Cadastros
sim=Sim
nao=Não
rotulo_nao_avaliaveis=Não Avaliáveis
painel_controle=Painel de Controle
download=Download
defina_parametro_path_file_manager_web=Defina no parametro o path para o gerenciador de arquivos web
deseja_excluir_arquivo_selecionado=Deseja excluir o arquivo selecionado?
nova_pasta=Nova Pasta
tamanho=Tamanho
baixar=Baixar
digite_nome_pasta=Digite o nome para a nova pasta
menu=Menu
parametro_path_file_manager_web_nao_configurado=Para utilizar o Gerenciador de Arquivos, configure o Caminho dos Diretórios em Painel de Controle > Configurações.
teste=Teste
gerenciador_arquivos=Gerenciador de Arquivos
autentique_para_acessar_sistema=Autentique-se para acessar o sistema.
grupo=Grupo
selecione_uma_empresa=Selecione uma unidade
selecione_um_grupo=Selecione um grupo
carregar=Carregar
permissao=Permissão
informe_usuario=Informe o Usuário!
informe_unidade=Informe a Unidade!
informe_grupo=Informe o Grupo!
conta_usuario=Conta do Usuário
ativo=Ativo
inativo=Inativo
deseja_realmente_deletar_o_registro=Deseja realmente deletar o registro?
diretorio_temporario=Diretório Temporário
informe_nova_senha=Informe a nova senha
fraco=Fraco
forte=Forte
bom=Bom
senha_incorreta=Senha Incorreta
senha_atual=Senha Atual
nova_senha=Nova Senha
confirmacao_senha=Confirmação Senha
senha_nao_confere=Senha não confere
email_invalido=E-mail Inválido
confirmacao_senha_diferente_nova_senha=Confirmação da senha diferente da nova senha
alterar_senha=Alterar senha
usuarios=Usuários
empresas_usuario=Empresas Usuário
adicionar_todas=Adicionar Todas
remover=Remover
remover_todas=Remover Todas
grupos_usuario=Grupos Usuário
minha_conta=Minha Conta
celk_saude=CELK Saúde
parent_entity_converter_atributo_entidade=O Component Parent do EntityConverter deve possuir o Atributo 'entidade'
parent_entity_converter_interface_editable_value_holder=O Component Parent do EntityConverter deve implementar a Interface 'EditableValueHolder'
objeto_entity_converter_interface_i_entity_properties=O Objeto a ser convertido pelo EntityConverter deve implementar a Interface 'IEntityProperties'
paginaPrincipal=Página Principal
disponiveis=Disponíveis
selecionadas=Selecionadas
selecionados=Selecionados
registro_salvo_sucesso_codigo_X=Registro salvo com sucesso! Código: {0}
registro_excluido_sucesso=Registro excluído com sucesso!
consulta_grupo=Consulta de Grupos
consulta_funcoes=Consulta de Funções
cadastro_grupo=Cadastro de Grupos
cadastro_funcoes=Cadastro de Funções
caminho_pagina=Caminho Página
publico=Público
cadastro_programa_pagina=Cadastro Programa Página
consulta_programa_pagina=Consulta Programa Página
pagina=Página
download_relatorio=Download Relatório
exibir_relatorio=Exibir Relatório
produto=Produto
filtros=Filtros
estado=Estado
ambos=Ambos
unidade_origem=Unidade Origem
unidadeOrigemGrupo=Un. Origem/Grupo
grupoProduto=Grupo Produto
horario=Horário
confirmar=Confirmar
selecione_paciente=Selecione um Paciente
motivo_cancelamento=Motivo Cancelamento
selecione_motivo=Selecione um Motivo
relacao_agendas=Relação Agendas
periodo=Período:
centro_custo=Centro de Custo
feminino=Feminino
masculino=Masculino
cadastro_paciente=Cadastro de Paciente
documentos=Documentos
certidao=Certidão
excluido=Excluído
numero=Número
complemento=Complemento
uf=UF
data_emissao=Data Emissão
expedicao=Expedição
contatos=Contatos
cns=CNS
ddd=DDD
telefone=Telefone
telefone2=Telefone 2
telefone3=Telefone 3
telefone4=Telefone 4
celular=Celular
endereco=Endereço
cidade=Cidade
bairro=Bairro
tipo_logradouro=Tipo Logradouro
logradouro=Logradouro
cep=CEP
folha=Folha
termo=Termo
livro=Livro
cartorio=Cartório
nascimento=Nascimento
casamento=Casamento
forma_apresentacao=Forma Apresentação
ordenacao=Ordenação
confirmacao=Confirmação
reverter_situacao=Reverter Situação
nao_compareceu=Não Compareceu
tipo_agendamento=Tipo Agendamento
consulta_paciente=Consulta Paciente
relatorios=Relatórios
especialidade_exame=Especialidade/Exame
marcar=Marcar
marcados=Marcados
visualizar=Visualizar
disponivel=Disponível
local=Local
agendamento_consulta_exame_ppi=Agendamento Consulta/Exame (PPI)
data_registro=Data Registro
agendado=Agendado
selecione=Selecione
numero_cartao_cns=Número Cartão CNS
usuariosus_cpf_ja_cadastrado=Já existe um usuário S.U.S. cadastrado com este C.P.F.
cpf_invalido=CPF inválido\!
cns_ja_cadastrado_paciente_X=Cns já cadastrado! Paciente: {0}
cns_invalido=Cns inválido!
gerar_relatorio=Gerar Relatório
pacientes_agendados=Pacientes Agendados
pacientes_na_fila=Pacientes na Fila
data_cadastro=Data Cadastro
prioridade=Prioridade
documentos_obrigatorios=Documentos Obrigatórios
limpar=Limpar
especialidade_exame_invalida=Especialidade/Exame Inválida!
cns_obrigatorio=CNS Obrigatório
relacao_agendamentos=Relação Agendamentos
resumo_agendamentos=Resumo Agendamentos
natureza_agendamento=Natureza Agendamento
tipo_resumo=Tipo Resumo
tipo_ordenacao=Tipo Ordenacao
urgente=Urgente
central_atendimento=Central Atendimento
novo_paciente=Novo Paciente
cancelar_agendamento=Cancelar Agendamento
incluir_na_fila=Incluir na fila
remover_da_fila=Remover da Fila
data_hora=Data/Hora
msg_selecione_paciente_fila_espera=Selecione um paciente na fila.
msg_informe_profissional=Informe o Profissional
msg_max_4_grupos=Informe no máximo 4 Grupos de Atendimento por Cbo
msg_informe_paciente=Informe o Paciente
agendar_paciente=Agendar Paciente
tipo_grafico=Tipo de Gráfico
tipo_dado=Tipo de Dado
grafico_distribuicao_agendamentos=Gráfico Distribuição Agendamentos
distribuicao_agendamentos=Distribuição Agendamentos
graficos=Gráficos
nr_max_serie=Nr. Máx. Série
grafico_distribuicao_mensal_agendamentos=Gráfico Distribuição Mensal Agendamentos
distribuicao_mensal_agendamentos=Distribuição Mensal Agendamentos
dados=Dados
dados_do_encaminhamento=Dados do encaminhamento
dados_do_ultimo_atendimento=Dados do último atendimento
e_necessario_selecionar_uma_agenda=É necessário selecionar uma agenda!
e_necessario_selecionar_uma_paciente=É necessário selecionar uma paciente!
cbo=CBO
tipo_equipe=Tipo Equipe
tipo_relatorio=Tipo Relatório
siab=SIAB
relacao_equipes=Relação das Equipes
detalhado=Detalhado
resumido=Resumido
resumidoTipo=Resumido por Tipo
resumidoProduto=Resumido por Produto
movimentacaoDiariaProdutos=Movimentação Diária dos Produtos
equipe=Equipe
produtosPrograma=Produtos por Programa
gestor=Gestor
indicadores=Indicadores
perfil_atendimentos=Perfil dos Atendimentos
historico_paciente=Histórico do Paciente
ficha_acompanhamento=Ficha de Acompanhamento
sisprenatal=SISPRENATAL
competencia=Competência
relacao_gestantes_pendentes_consulta_puerperal=Relação das Gestantes Pendentes para consulta Puerperal
relacao_exames_realizados=Relação Exames Realizados
relacao_familias_nao_acompanhadas=Relação das Famílias Não Acompanhadas
bolsa_familia=Bolsa Família
consultas_medicas=Consultas Médicas
consultaSerie=Consulta de Série
cadastroSerie=Cadastro de Série
exames=Exames
estoque=Estoque
comEstoque=Com Estoque
semEstoque=Sem Estoque
farmacia=Farmácia
relacao_gestantes_pendentes_fechamento=Relação das Gestantes Pendentes para Fechamento
resumo_doencas_area=Resumo das Doenças por Área
resumo_familia_visitadas_area=Resumo das Famílias Visitadas por Área
teste_pezinho=Teste do Pezinho
relacao_exames_alterados=Relação Exames Alterados
hiperdia=Hiperdia
relacao_paciente_area=Relação dos Pacientes por Área
resumo_paciente_cadastrado_area=Resumo dos Pacientes Cadastrados por Área
indicador_avaliacao_saude_mulher=Indicador Avaliação Saúde Mulher
pmaq=PMAQ
crm=CRM
avaliaTipoDocto=Avalia Tipo Docto.
cadastro_cidade=Cadastro de Cidade
consulta_cidade=Consulta de Cidade
home=Home
entrar=Entrar
confirmacao_presenca=Confirmação Presença
registro_salvo_sucesso=Registro salvo com sucesso!
tipo_procedimento=Tipo Procedimento
orgao_emissor=Órgão Emissor
consulta_agendamento=Consulta Agendamento
unidade_executante=Unidade Executante
unidadeProduto=Unidade Produto
listagemProdutos=Listagem Produtos
unidade_solicitante=Unidade Solicitante
rua=Rua
unidadeDispensadora=Unidade Dispensadora
vagas_ocupadas=Vagas Ocupadas
consulta_solicitacao=Consulta Solicitação
solicitacao=Solicitação
negado=Negado
flagCasaDecimal=Decimal
permiteQtdadeMgMl=Permite Quantidade Ml.
devolvido=Devolvido
pendente=Pendente
fila_espera=Fila Espera
numero_solicitacao=Número Solicitação
tipo_data=Tipo Data
flagSigla=Sigla
data_solicitacao=Data Solicitação
comprovante_agendamento=Comprovante Agendamento
comprovante_solicitacao=Comprovante Solicitação
selecione_solicitacao=Selecione uma Solicitação
acompanhamento_agendamento=Acompanhamento Agendamento
contato=Contato
telefone_2=Telefone 2
telefone_3=Telefone 3
telefone_4=Telefone 4
ocorrencias=Ocorrências
ocorrencia=Ocorrência
anteciparSaldo=Antecipar Saldo
confirmar_contato=Confirmar Contato
lancar_ocorrencia=Lançar Ocorrência
dados_solicitacao=Dados Solicitação
profissional_solicitante=Profissional Solicitante
procedimento=Procedimento
dados_regulacao=Dados Regulação
regulador=Regulador
data_desejada=Data Desejada
profissional_executante=Profissional Executante
tipo_consulta=Tipo Consulta
relatorio_enviado_processamento=Relatório enviado para processamento!
processo=Processo
busca=Busca
relatorio=Relatório
cadsus=CADSUS
consultar=Consultar
consulta_pedido_almoxarifado=Consulta Pedido Almoxarifado
pedido_almoxarifado=Pedido Almoxarifado
movimentacao=Movimentação
materiais=Materiais
aberto=Aberto
aprovado=Aprovado
processado=Processado
recebido=Recebido
pedido=Pedido
data_pedido=Data Pedido
data_embarque=Data Embarque
data_recebimento=Data Recebimento
cadastro_pedido_almoxarifado=Cadastro Pedido Almoxarifado
sub_grupo=Subgrupo
quantidade=Quantidade
dados_item=Dados do Item
fisico=Físico
minimo=Mínimo
total_itens=Total de Itens
subGrupo.roGrupoProduto=Grupo
produto_ja_adicionado=Produto já adicionado
deseja_cancelar_pedido=Deseja cancelar o pedido?
detalhes_pedido_almoxarifado=Detalhes Pedido Almoxarifado
item=Item
quantidade_atendida=Quantidade Atendida
diferenca=Diferença
detalhar=Detalhar
lotes=Lotes
lote=Lote
quantidadeEmbalagem=Qtde. Embalagem
itens=Itens
deseja_remover_item=Deseja remover o item?
informe_os_itens=Informe os itens
valor_superior_limite_produto=O produto {0} está com um valor inválido. O limite é de 8 dígitos e 4 casas decimais
informe_produto=Informe o produto
informe_quantidade=Informe a quantidade
separando=Separando
edicao_nao_permitida_situacao_pedido_foi_modificada=Edição não permitida, Situação do Pedido foi Modificada
nr_solicitacao=Nr. Solicitação
todas=Todas
detalhes_solicitacao=Detalhes da Solicitação
informe_senha=Informe a Senha
ja_existe_paciente_com_esse_cns=Já existe um paciente cadastrado com esse CNS
ja_existe_cartao_definitivo_ativo=Só é permitido adicionar um cartão definitivo e ativo por usuário. Por favor, inative os demais cartões para adicionar este.
receber=Receber
receber_pedido_almoxarifado=Receber Pedido Almoxarifado
responsavel=Responsável
selecione_pedido=Selecione um pedido
alterar=Alterar
qt_recebida=Qt. Recebida
qt_solicitada=Qt. Solicitada
dados_usuario=Dados do Usuário
confirmacao_nova_senha=Confirmação Nova Senha
algumas_alteracoes_necessitam_novo_login_para_entrar_em_funcionamento=Algumas alterações necessitam um novo login para entrarem em funcionamento.
confirmar_voltar_alteracoes_serao_perdidas=Foi solicitado para voltar, se possuir alterações não salvas elas serão PERDIDAS, confirma?
produto_ja_adicionado_atualizar_quantidade=Produto já adicionado, atualizar a quantidade ?
master=Master
administrador=Administrador
normal=Normal
permissoes=Permissões
grupos=Grupos
mapa=Mapa
tipoOrdenacao=Tipo de Ordenação
consumoProdutos=Consumo dos Produtos
agruparUnidade=Agrupar Unidade
selecione_um_usuario=Selecione um usuário
paginas_adicionais=Páginas Adicionais
pagina_ja_adicionada=Página já adicionada
programaPaginaPrincipal=Página Principal
descricaoBundle=Bundle
descricaoProduto=Descrição Produto
informe_uma_pagina=Informe uma página
funcoes=Funções
funcao=Função
unidade_ja_adicionada=Unidade já adicionada
funcao_ja_adicionada=Função já adicionada
diretorioTemporario=Diretório Temporário
moduloPadrao=Módulo Padrão
empresaPadrao=Unidade Padrão
pedido_recebido_sucesso=Pedido recebido com sucesso
selecione_unidade=Selecione uma unidade
qt_enviada=Qt. Enviada
nao_aprovado=Não Aprovado
+_expandir=[+] Expandir
-_recolher=[-] Recolher
+_mais_filtros=[+] Mais Filtros
-_menos_filtros=[-] Menos Filtros
+=[+]
-=[-]
consulta_produto=Consulta Produto
consulta_profissional=Consulta Profissional
veiculo=Veículo
usuarioRecebimento=Usuário do Recebto.
usuarioCancelamento=Usuário do Cancelamento
usuarioConclusao=Usuário de Conclusão
dataCadastro=Data de Cadastro
diasEspera=Dias Espera
dataRecebimento=Data de Recebimento
dataCancelamento=Data de Cancelamento
dataEmbarque=Data de Embarque
dataPedido=Data do Pedido
unidadeCadastro=Unidade de Cadastro
unidadeDestino=Unidade de Destino
unidadeOrigem=Unidade de Origem
quebrarPagina=Quebrar Página
permissoesWeb=Permissões Web
permissoesDesktop=Permissões Desktop
cadastroPermissoesWeb=Cadastro de Permissões Web
cadastroPermissoesDesktop=Cadastro de Permissões Desktop
permissoesSalvasSucesso=Permissões salvas com sucesso.
menus=Menus
programas=Programas
consultaHorarios=Consulta dos Horários
cadastroHorarios=Cadastro dos Horários
controleHorarios=Controle dos Horários
horaInicial=Hora Inicial
horaFinal=Hora Final
segundaFeira=Segunda-Feira
tercaFeira=Terça-Feira
quartaFeira=Quarta-Feira
quintaFeira=Quinta-Feira
sextaFeira=Sexta-Feira
sabado=Sábado
domingo=Domingo
diasSemana=Dias da Semana
diaSemana=Dia da Semana
informeHoraInicial=Informe a hora inicial
informeHoraFinal=Informe a hora final
informePeloMenosUmDiaSemana=Informe pelo menos um dia da semana
conflitoHorariosEncontradoCadastroAtual=Conflito de horários encontrado no cadastro atual
conflitoHorariosEncontradoEmpresas=Conflito de horário encontrado na(s) empresa(s)
consultaTiposVacina=Consulta dos Tipos de Vacina
cadastroTipoVacina=Cadastro do Tipo de Vacina
tiposVacina=Tipos de Vacina
vacinas=Vacinas
subGrupo=SubGrupo
validadeAposAberta=Validade após Aberta
clonar=Clonar
cadastroVacina=Cadastro de Vacina
consultaVacinas=Consulta de Vacinas
doses=Doses
fabricante=Fabricante
un=UN
codigoReferenciaEstado=Código de Referência do Estado
codigoProduto=Código Produto
controlaEstoqueMinimo=Controla Estoque Mínimo
estoqueMinimo=Estoque Mínimo
dataEstoqueFisico=Estoque Físico em
considerarVencidos=Considerar Vencidos
flagControleMinimo=Controla Estoque Mínimo
controleMinimo=Controla Estoque Mínimo
produtoVacina=Vacina
tipoVacina=Tipo de Vacina
quantidadeDose=Doses
produto.unidade=Unidade
produto.descricao=Descricao
numeroDosesDeveSerMaiorQueZero=O número de doses deve ser maior que zero
cadastroFabricante=Cadastro de Fabricante
consultaFabricantes=Consulta dos Fabricantes
fabricantes=Fabricantes
desejaRealmente=Deseja realmente
confirmacaoSenha=Confirmação da Senha
programasWeb=Programas Web
menusWeb=Menus Web
cadastroInsumo=Cadastro de Insumo
consultaInsumos=Consulta dos Insumos
curva=Curva
curvaA=A
curvaB=B
curvaC=C
insumos=Insumos
flagBaixaEstoqueProcessoEnfermagem=Baixa Est. Proc. Enferm.
PasswordValidator.senha=A senha deve possuir no mínimo 6 (seis) caracteres
ativar=Ativar
bloquear=Bloquear
desbloquear=Desbloquear
retirarReserva=Retirar Reserva
acesso=Acesso
bloqueado=Bloqueado
desbloqueado=Desbloqueado
modulo=Módulo
atualizarMenu=Atualizar Menu
pagina_principal=Página Principal
consultaProgramaDesktop=Consulta dos Programas Desktop
programasDesktop=Programas Desktop
relatorioEstoqueVacinas=Relatório de Estoque das Vacinas
estoqueVacinas=Estoque das Vacinas
estoqueFisico=Estoque Físico
painelControle=Painel de Controle
consultaPedidosVacinas=Consulta de Pedidos de Vacinas
cadastroPedidoVacina=Cadastro de Pedido de Vacina
pedidoVacinas=Pedido de Vacinas
pedidos=Pedidos
numeroPedido=Número do Pedido
desejaRealmenteCancelar=Deseja realmente cancelar?
dadosItem=Dados do Item
classeMenuNaoDisponivel=Classe de menu não disponível
informeTipoVacina=Informe o tipo de vacina
informeDoses=Informe as doses
tipoVacinaJaAdicionadoAtualizarDoses=Tipo de vacina já adicionado, atualizar as doses?
produtoJaAdicionadoAtualizarDados=Produto já adicionado, atualizar dados?
dosesPedidas=Doses Pedidas
dosesRecebidas=Doses Recebidas
dosesCanceladas=Doses Canceladas
detalhesItem=Detalhes do Item
detalhesPedidoVacina=Detalhes do Pedido de Vacina
usuarioAlteracao=Usuário da Alteração
dataAlteracao=Data da Alteração
tipoPedido=Tipo do Pedido
usuarioEncaminhamento=Usuário do Encaminhamento
entrada=Entrada
consultaRecebimentosVacinas=Consulta de Recebimentos de Vacinas
nota=Nota
fornecedor=Fornecedor
tipoData=Tipo de Data
fantasia=Fantasia
dataPortaria=Data de Portaria
dataEmissao=Data de Emissão
dadosRecebimento=Dados do Recebimento
valorTotal=Valor Total
dataValidade=Data de Validade
totalDoses=Total de Doses
valorTotalItem=Valor Total do Item
informeProduto=Informe o produto
informeQuantidade=Informe a quantidade
informeValorTotalItem=Informe o valor total do item
dataEncaminhamento=Data do Encaminhamento
usuarioSeparacao=Usuário da Separação
dataSeparacao=Data da Separação
qtSolicitada=Qt. Solicitada
qtEnviada=Qt. Enviada
qtRecebida=Qt. Recebida
encaminhar=Encaminhar
dadosAdicionais=Dados Adicionais
valorUnitario=Valor Unitário
desejaRealmenteExcluir=Deseja realmente excluir
detalhesEntradaVacina=Detalhes de Entrada da Vacina
usuarioConfirmacao=Usuário da Confirmação
dataConfirmacao=Data da Confirmação
informeLote=Informe o Lote
informeDataValidade=Informe a data de validade
validade=Validade
naoHaPaginasParaImprimir=Não há páginas para imprimir
configuracoes=Configurações
cnes=Cnes
sigla=Sigla
cpfCnpj=CPF/CNPJ
selecioneGrupo=Selecione um Grupo
imprimir=Imprimir
consultaPedidosInsumos=Consulta de Pedidos de Insumos
produtoJaAdicionadoAtualizarQuantidade=Produto já adicionado, atualizar a quantidade?
pedidoInsumos=Pedido de Insumos
quantidadePedida=Quantidade Pedida
quantidadeRecebida=Quantidade Recebida
quantidadeCancelada=Quantidade Cancelada
totalItens=Total de Itens
produtos=Produtos
tiposConsulta=Tipos de Consulta
tiposProcedimento=Tipos de Procedimento
cidades=Cidades
estados=Estados
empresas=Unidades
orgaosEmissores=Órgãos Emissores
pessoas=Pessoas
profissionais=Profissionais
unidades=Unidades
tiposLogradouro=Tipos de Logradouro
pacientes=Pacientes
modulos=Módulos
paginas=Páginas
depositos=Depósitos
deposito=Depósito
gruposProduto=Grupos de Produto
fabricantesMedicamento=Fabricantes de Medicamento
erro=Erro
detalhesPedidoInsumo=Detalhes do Pedido de Insumo
cadastroPedidoInsumo=Cadastro de Pedido de Insumo
acessoDesktop=Acesso Desktop
cliqueAqui=Clique Aqui
confirmaEntradaNota=Confirmar entrada da nota
acessoWeb=Acesso Web
relatorioPedidosVacinasRotina=Relatório dos Pedidos das Vacinas de Rotina
pedidosVacinasRotina=Pedidos das Vacinas de Rotina
informePeriodo=Informe o período
percentual=Percentual
relatorioPedidosInsumo=Relatório dos Pedidos de Insumo
pedidosInsumo=Pedidos de Insumo
operacaoRealizadaComSucesso=Operação realizada com sucesso
relatorioRecebimentoVacinas=Relatório Recebimento de Vacinas
recebimentoVacinas=Recebimento de Vacinas
formaApresentacao=Forma de Apresentação
situacaoEstoque=Situação do Estoque
possuiEstoque=Possui Estoque
naoPossuiEstoque=Não Possui Estoque
naoPermitidoAdicionarProdutosSemEstoque=Não é permitido adicionar produtos sem estoque
centroCusto=Centro de Custo
centrosCusto=Centros de Custo
estornarRecebimento=Estornar recebimento
confirmarEstornoRecebimento=Confirmar estorno do recebimento com movimentação na data
resumoMovimentacaoEstoque=Resumo da Movimentação do Estoque
resumoConsumoProdutos=Resumo do Consumo dos Produtos
quantidadeMesesMedia=Meses p/ Calc. da Média
msgMesMediaObrigatorio=Informe a quantidade de meses para calcular a média!
recebimentos=Recebimentos
dataUltimoRecebimento=Data do Último Recebimento
referencia=Referência
confirmarEntradaEstoque=Confirmar Entrada no Estoque
dataPortariaNaoPodeSerMenorDataEmisao=A data de portaria não pode ser menor que a data de emissão da nota
codigoReferencia=Código de Referência
unidadeMovimentacao=Unidade da Movimentação
unidadeDestinoOrigem=Unidade de Destino/Origem
tipoDocumento=Tipo de Documento
tipoRelatorio=Tipo de Relatório
documentoItem=Documento/Item
exibeObservacao=Exibe Observação
tiposDocumento=Tipos de Documento
relatorioMovimentacaoEstoque=Relatório de Movimentação do Estoque
fichaProduto=Ficha do Produto
relatorioFichaProduto=Relatório Ficha do Produto
lancamentoEstoque=Lançamento no Estoque
dtValidade=Dt. Validade
reservado=Reservado
dataEntrada=Data de Entrada
quantidadeMaiorDisponivelLoteSelecionado=Quantidade maior do que o disponível para o lote selecionado
numeroDocumento=Número do Documento
jaExisteProdutoLoteAdicionadoDesejaAtualizar=Já existe este produto/lote adicionar, deseja atualizar?
movimentoEstoque=Movimento Estoque
centroCustoObrigatorioTipoDocumento=O campo centro de custo é obrigatório para o tipo de documento selecionado
unidadeDestinoObrigatorioTipoDocumento=O campo unidade destino é obrigatório para o tipo de documento selecionado
campoLoteObrigatorio=O campo do lote é obrigatório para o produto selecionado
quantidadeDeveSerMaiorQueZero=A quantidade deve ser maior do que zero
informePeloMenosUmMovimento=Informe pelo menos um movimento
empresaDestino=Unidade de Destino
grupoEstoque=Lote
sessaoExpirada=Sessão Expirada
suaSessaoExpirouFacaLogin=Sua sessão expirou, faça o login novamente para utilizar o sistema.
cadastroFamiliaFichaA=Cadastro de Família (Ficha A)
dadosDomicilio=Dados do Domicílio
codigoDomicilio=Código do Domicílio
numeroUsuariosDomicilio=Número de Usuários do Domicílio
dataPreenchimento=Data de Preenchimento
consultaDomicilios=Consulta Domiciliar e Territorial
familia=Família
mae=Mãe
tipoLogradouro=Tipo de Logradouro
ddd2=DDD 2
latitude=Latitude
longitude=Longitude
identificacaoFamilia=Identificação da Família
area=Área
segmento=Segmento
microarea=Microárea
agenteComunitario=Agente Comunitário
numeroComodos=Número de Cômodos
abastecimentoAgua=Abastecimento de Água
esgotamentoSanitario=Esgotamento Sanitário
tipoDomicilio=Tipo de Domicílio
tratamentoAgua=Tratamento da Água
energiaEletrica=Energia Elétrica
destinoLixo=Destino do Lixo
domicilioCobertoPor=Domicílio Coberto por
alguemPossuiPlanoSaude=Alguém possui Plano de Saúde
quantos=Quantos?
agruparEmpresa=Agrupar Empresa
nomePlanoSaude=Nome do Plano de Saúde
emCasoDoencaProcura=Em caso de Doença Procura
qual=Qual?
meioComunicacaoMaisUtiliza=Meio de Comunicação que mais Utiliza
participaGruposComunitarios=Participa de Grupos Comunitários
meioTransporteMaisUtiliza=Meio de Transporte que mais Utiliza
familiaJaCadastrada=Família já cadastrada
anterior=Anterior
proximo=Próximo
finalizar=Finalizar
componentesFamilia=Componentes da Família
dadosPessoais=Dados Pessoais
componente=Componente
responsavelDomicilio=Responsável pelo Domicílio
doencas=Doenças
doenca=Doença
adicionarDoenca=Adicionar Doença
adicionarComponente=Adicionar Componente
limparComponente=Limpar Componente
ocupacao=Ocupação
dataNascimento=Data de Nascimento
enderecoDomicilio.enderecoUsuarioCadsus.cep=CEP
enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro=Número
enderecoDomicilio.enderecoUsuarioCadsus.logradouro=Logradouro
enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro=Tipo de Logradouro
enderecoDomicilio.enderecoUsuarioCadsus.bairro=Bairro
enderecoDomicilio.enderecoUsuarioCadsus.cidade=Cidade
enderecos=Endereços
componenteJaAdicionado=Componente já adicionado
equipeArea=Área
equipeMicroArea=Equipe Micro Área
enderecoDomicilio.microArea=Microárea
enderecoDomicilio.numeroFamilia=Família
ultimaAlteracao=Última Alteração
numeroProntuario=Número do Prontuário
racas=Raças
cbos=CBOs
consultaPacientes=Consulta de Pacientes
cadastroPaciente=Cadastro de Paciente
cidadeNascimento=Cidade do Nascimento
nomeMae=Nome da Mãe
nomePai=Nome do Pai
racaCor=Raca/Cor
dataFixacao=Data de Fixação
estadosCivis=Estados Civis
situacaoConjugalFamiliar=Situação Conjugal / Familiar
estadoCivil=Estado Civil
escolaridades=Escolaridades
escolaridade=Escolaridade
frequentaEscola=Frequenta Escola
paises=Países
paisNascimento=País de Origem
dataNaturalizacao=Data da Naturalização
portariaNaturalizacao=Portaria da Naturalização
dataEntradaBrasil=Data de Entrada no Brasil
outrosDocumentos=Outros Documentos
tituloEleitoral=Título Eleitoral
carteiraProfissional=Carteira Profissional
tituloEleitor=Título de Eleitor
zona=Zona
secao=Seção
pispasep=PIS / PASEP
serie=Série
tipo=Tipo
adicionarCns=Adicionar CNS
selecioneUmEndereco=Selecione um endereço
usuarioCadsus.nome=Nome
usuarioCadsus.nomeSocial=Nome Social
usuarioCadsus.nomeMae=Nome da Mãe
usuarioCadsus.dataNascimento=Data de Nascimento
numeroCartao=CNS
tipoCartao=Tipo do Cartão
frota=Frota
plano_contas=Plano de Contas
planoContas=Plano de Contas
consulta_plano_contas=Consulta de Plano de Contas
cadastroPlanoContas=Cadastro de Plano de Contas
dadosVacinacao=Dados da Vacinação
calendarioVacinacao=Calendário de Vacinação
consultaCalendariosVacinacao=Consulta do Calendário de Vacinação
cadastroCalendarioVacinacao=Cadastro do Calendário de Vacinação
vacina=Vacina
doencasEvitadas=Doenças Evitadas
opcional=Opcional
impressaoCalendario=Impressão do Calendário
aa=à
cadastroTipoVeiculo=Cadastro de Tipo de Veículo
consultaTipoVeiculo=Consulta de Tipo de Veículo
cadastroVeiculoCombustivel=Cadastro de Combustível
consultaVeiculoCombustivel=Consulta de Combustível
veiculoCombustivel=Combustível
combustivel=Combustível
valorLitro=Valor de Referência do Litro
sugerir=Sugerir
tipoOperacao=Tipo de Operação
cadastroTipoOperacao=Cadastro de Tipo de Operação
consultaTipoOperacao=Consulta de Tipo de Operação
flagDataGarantia=Data Garantia
flagKmAtual=KM Atual
flagKmProximaTrocaOleo=KM Próx. Troca Óleo
flagKmProximaRevisao=KM Próx. Revisão
flagDataProximaRevisao=Data Próx. Revisão
flagMotorista=Motorista
flagCombustivel=Combustível
flagAtualizaDadoVeiculo=Atualiza Dado Veículo
idadeInicial=Idade Inicial
idadeFinal=Idade Final
vacinaForaCalendario=Vacina Fora do Calendário
registrarHistoricoVacinacao=Registrar Histórico de Vacinação
controleVacinacao=Controle de Vacinação
dadosPaciente=Dados do Paciente
dose=Dose
historicoVacinacao=Histórico de Vacinação
dataAplicacao=Data da Aplicação
aplicar=Aplicar
naoAplicar=Não Aplicar
reverter=Reverter
aplicada=Aplicada
naoAplicada=Não Aplicada
pendenteOpcional=Pendente / Opcional
lancarHistorico=Lançar Histórico
descricaoVacina=Descrição da Vacina
aplicacaoVacina=Aplicação de Vacina
dadosVacina=Dados da Vacina
cadastroModeloDocumento=Cadastro de Modelo Documento
consultaModeloDocumento=Consulta de Modelo Documento
modeloDocumento=Modelo de Documento
cadastroVeiculo=Cadastro de Veiculo
consultaVeiculo=Consulta de Veiculos
quantidadeLugares=Quantidade de Passageiros
placa=Placa
mesLicenciamento=Mês do Licenciamento
mesAno=Mês/Ano
anoFabricacao=Ano de Fabricação
autonomia=Autonomia
dataCompra=Data da Compra
programasSaude=Programas de Saúde
programaSaude=Programa de Saúde
apenasListaRename=Apenas da Lista do RENAME
movimentacaoDeterminadaData=Movimentação em Determinada Data
estoqueFisicoEm=Estoque Físico em
relacaoPerdas=Relação das Perdas
abaixo=Abaixo
acima=Acima
numeroHabilitacao=Número da Habilitação
dataValidadeHabilitacao=Data de Validade CNH
numeroChn_abv=Nr. CHN
motoristas=Motoristas
motorista=Motorista
consultaMotorista=Consulta de Motorista
cadastroMotorista=Cadastro de Motorista
cadastroRegistroManutencao=Cadastro Registro de Manutenção
lancamentos=Lançamentos
registroManutencao=Registro de Manutenção
documento=Documento
dataManutencao=Data da Manutenção
valor=Valor
informeTipoOperacao=Informe o Tipo de Operação
informeVeiculo=Informe o Veículo
itemJaAdicionadoAtualizar=Item já adicionado, deseja atualizar?
manutencao=Manutenção
dataGarantia=Data de Garantia
kmAtual=Km Atual
kmProximaTrocaOleo=Km Próx. Troca Óleo
kmProximaRevisao=Km Próx. Revisão
dataProximaRevisao=Data Próx. Revisão
copiar=Copiar
moradores=Moradores
cadastroEndereco=Cadastro de Endereço
cadastroMicrorregiao=Cadastro de Microrregião
condicaoDomicilio=Condição do Domicílio
codigoFamilia=Código da Família
vinculoCalendarioVacinacao=Vínculo com o Calendário de Vacinação
imprimirVacinasAplicadas=Imprimir Vacinas Aplicadas
visualizarComponentes=Visualizar Componentes
receitaAzulB=Azul (B)
receitaAmarelaA=Amarela (A)
receitaBrancaC=Branca (C)
receitaBasicaP=Básica (P)
saldoEstoque=Saldo do Estoque
validarEstoque=Validar Estoque
tipoEstoque=Tipo Estoque
pontoReposicao=Ponto Reposição
situacaoProdutos=Situação Produtos
totalizarEstoque=Totalizar Estoque
receita=Receita
controlado=Controlado
exibirLotes=Exibir Lotes
validarValidade=Validar Validade
negativo=Negativo
consultaAssistenteSocial=Consulta de Assistente Social
cadastroAssistenteSocial=Cadastro de Assistente Social
assistenteSocial=Assistente Social
cadastroDeposito=Cadastro de Depósito
consultaDeposito=Consulta de Depósito
cadastroCentroCusto=Cadastro de Centro Custo
consultaCentroCusto=Consulta de Centro Custo
consultaLocalizacao=Consulta da Localização
cadastroLocalizacao=Cadastro da Localização
cadastroInformacoesProfissional=Cadastro de Informações do Profissional
consultaInformacoesProfissional=Consulta de Informações do Profissional
informacoesProfissional=Informações do Profissional
numeroRegistro=Número Registro
dataRegistro=Data Registro
regiaoRegistro=Região Registro
cadastroUnidadeProduto=Cadastro de Unidade de Produtos
consultaUnidadeProduto=Consulta de Unidade de Produtos
unidadeProdutos=Unidade de Produtos
cadastroTipoDocumento=Cadastro do Tipo do Documento
consultaTipoDocumento=Consulta do Tipo do Documento
consultaMateriais=Consulta de Materiais
cadastroMateriais=Cadastro de Materiais
consultaGrupoProduto=Consulta Grupo Produto
consultaLiberacaoReceita=Consulta Liberação de Receita
cadastroLiberacaoReceita=Cadastro Liberação de Receita
dataUsuario=Data da Liberação
usuarioCadsus=Paciente
liberacaoReceita=Liberação Receita
liberado=Liberado
informeDescricao=Informe Descrição
subGrupoJaAdicionadoAtualizarDados=SubGrupo já adicionado, atualizar dados?
cadastroGrupoProdutoSubGrupos=Cadastro Grupo Produto e SubGrupos
informePeloMenosUmSubGrupo=Informe pelo menos um subGrupo
subGrupos=SubGrupos
dadosSubGrupo=Dados do SubGrupo
posologia=Posologia
geraReservaProdutoSolicitado=Reserva Produto Solic.
controlaEstoqueAlmoxarifado=Controla Estoque
controlaLote=Controla Lote
grupoProdutoSubGrupo=Grupo Produto / SubGrupo
medicamento=Medicamento
dadosGrupoProduto=Dados do Grupo Produto
informeDescricaoSubGrupo=Informe a Descrição do SubGrupo
baixaEstoqueProcessoEnfermagemAbv=Baixa Est. Proc. Enferm.
valorItem=Valor Item
precoUnitario=Preço Unitário
precoUnitarioCusto=Preço Unitário/Custo
tipoDocumentoFaturamento=Tipo do Faturamento
tipoDocumentoSaidaEstoque=Tipo Saída Recebimento
tipoMovimento=Tipo do Movimento
exigeProduto=Exige Produto
movimentaEstoque=Movimenta Estoque
consumo=Consumo
retorno=Retorno
consignacao=Consignação
reservar=Reservar
inventario=Inventário
fatura=Fatura
exigeObservacao=Exige Observação
devolucao=Devolução
gerarDuplicata=Gera Duplicata
lancaGrupoEstoque=Lança Grupo Estoque
geraControleNFEntrada=Gera Ctr. NF. Entrada
informaNFSaida=Inf. NF. Saída
baixaControleNFSaida=Baixa Ctl. NF. Saída
imprimeResumo=Imprime Resumo
digitaOC=Digita
NFEntrada=NF. Entrada
interno=Interno
atualizaCusto=Atualiza Custo
exigeUnidadeDestino=Exige Unidade Destino
exigeCentroCusto=Exige Centro de Custo
tipoDigitacaoPrecoNFEntradaAbv=Tp. Dig. Preço NF. Entrada
mascara=Máscara
cadastroTipoViaMedicamento=Cadastro do Tipo de Via Medicamento
consultaTipoViaMedicamento=Consulta do Tipo de Via Medicamento
tipoViaMedicamento=Tipo de Via Medicamento
comMinimo=Com Mínimo
semMinimo=Sem Mínimo
listarPreco=Listar Preço
nomeMotorista=Nome do Motorista
validadeCnh=Validade da CNH
dispensacoesAberto=Dispensações em Aberto
historicoDispensacoes=Histórico de Dispensações
codigoBarras=Código de Barras
dadosReceita=Dados da Receita
tipoReceita=Tipo de Receita
numeroReceita=Número da Receita
medicoDentista=Médico / Dentista
adicionarItem=Adicionar Item
dispensacao=Dispensação
dispensacaoMedicamentos=Dispensação de Medicamentos
identificacao=Identificação
prescrito=Prescrito
basica=Básica
amarela=Amarela
azul=Azul
branca=Branca
qtDia=Qt./Dia
dispensar=Dispensar
saldo=Saldo
diasProximaDispensacao=Dias Pŕox. Dispensação
validadeContinua=Validade Contínua
continua=Contínua
quantidadePrescrita=Quantidade Prescrita
quantidadeMgMlDose=Quantidade Mg/Ml/Dose
quantidadeDia=Quantidade por Dia
duracaoDias=Duração em Dias
quantidadeDispensar=Quantidade a Dispensar
itemParaDispensar=Item para Dispensar
quantidadeTotal=Quantidade Total
quantidadeDispensarMaiorZero=A quantidade a dispensar deve ser maior do que zero.
dispensado=Dispensado
usuarioCadsusDestino=Paciente
empresaOrigem=Unidade de Origem
informePeloMenosUmItem=Informe pelo menos um item
calendario=Caledário
apenasEnderecosComFamilia=Apenas Endereços com Família
pedida=Pedida
recebida=Recebida
cancelada=Cancelada
consultaCalendarios=Consulta de Calendário
cadastroCalendario=Cadastro de Calendário
padrao=Padrão
dataProxLicenciamento=Data Próx. Licenciamento
dataProxSegObrigatorio=Data Próx. Seg. Obrigatório
numeroFrota=Número da Frota
chassi=Chassi
renavam=Renavam
alterarCalendario=Alterar Calendário
salvarConitinuarEditando=Salvar e Continuar Editando
novoComponente=Novo Componente
numeroUsuariosDomicilioDiferenteAdicionadoDesejaContinuar=Número de usuários no domicílio diferente do número de componentes adicionado, deseja continuar mesmo assim?
totalComponentes=Total de Componentes
programasSociais=Programas Sociais
aFamiliaBeneficiariaProgramaBolsaFamilia=A Família é beneficiária do Programa Bolsa Família
nisResponsavel=NIS do Responsável
aFamiliaInscritaCadastroUnicoProgramaSociais=A família está inscrita no Cadastramento Único de Programas Sociais do Governo Federal (CAD-Único)
detalhamentoGastos=Detalhamento dos Gastos
periodoManutencao=Período da Manutenção
previsaoManutencao=Previsão de Manutenção
somenteVencidos=Somente Vencidos
situacaoKm=Situação Km
situacaoPeriodo=Situação Período
consultaPacientesSemCartao=Consulta de Pacientes sem Cartão
cadastroCns=Cadastro do CNS
cartaoCns=Cartão CNS
unidadeResponsavel=Unidade Responsável
cartaoNacionalSaude=Cartão Nacional de Saúde
definitivo=Definitivo
provisorio=Provisório
almoxarifado=Almoxarifado
id.endereco.bairro=Bairro
id.endereco.cep=CEP
id.endereco.tipoLogradouro=Tipo de Logradouro
id.endereco.logradouro=Logradouro
id.endereco.numeroLogradouro=Número
tipoResumo=Tipo de Resumo
editarPaciente=Editar Paciente
consultaSituacaoSaudeAcompanhamentoFamiliaArea=Consulta da Situação de Saúde e Acompanhamento das Famílias na Área (SSA2)
cadastroSituacaoSaudeAcompanhamentoFamiliaArea=Cadastro da Situação de Saúde e Acompanhamento das Famílias na Área (SSA2)
recemNascidos=Recém-Nascidos
nascidosVivosNoMes=Nascidos Vivos no Mês
recemNascidosPesadosAoNascer=RN Pesados ao Nascer
recemNascidosPesadosAoNascerComMenos2500g=RN com Peso < 2500g
criancas=Crianças
criancasComAte4Meses=De 0 a 3 meses e 29 dias
criancasComAte4MesesAleitamentoExclusivo=Aleitamento Exclusivo
criancasComAte4MesesAleitamentoMisto=Aleitamento Misto
criancas0A11Meses=De 0 a 11 meses e 29 dias
criancas0A11MesesComVacinasEmDia=Com Vacinas em Dia
criancas0A11MesesPesadas=Pesadas
criancas0A11MesesDesnutridas=Desnutridas
criancas12A23Meses=De 12 a 23 meses e 29 dias
criancas12A23MesesComVacinasEmDia=Com Vacinas em Dia
criancas12A23MesesPesadas=Pesadas
criancas12A23MesesDesnutridas=Desnutridas
menoresDe2Anos=Menores de 2 anos
criancasMenores2AnosTiveramDiarreia=Que Tiveram Diarréia
criancasMenores2AnosTiveramDiarreiaUsaramTro=Que Tiveram Diarréia e Usaram TRO
criancasMenores2AnosTiveramInfeccaoRespiratoriaAguda=Que Tiveram IRA
ssa2=SSA2
gestantes=Gestantes
gestantesCadastradas=Cadastradas
gestantesComVacinasEmDia=Com Vacinas em Dia
gestantesMenores20Anos=Menores de 20 anos
gestantesAcompanhadas=Acompanhadas
gestantesComPreNatalDoMes=Fez consulta de Pré-Natal do mês
gestantesComPreNatalPrimeiroTri=Com Pré-Natal iniciado no 1º TRI
diabeticos=Diabéticos
hipertensos=Hipertensos
pessoasComTuberculose=Pessoas com Tuberculose
pessoasComHanseniase=Pessoas com Hanseníase
diabeticosCadastrados=Cadastrados
diabeticosAcompanhados=Acompanhados
hipertensosCadastrados=Cadastrados
hipertensosAcompanhados=Acompanhados
pessoasComTuberculoseCadastradas=Cadastrados
pessoasComTuberculoseAcompanhadas=Acompanhados
pessoasComHanseniaseCadastradas=Cadastrados
pessoasComHanseniaseAcompanhadas=Acompanhados
hospitalizados=Hospitalizados
hospitalizadosMenores5AnosPorPneumonia=Menores de 5 anos por Pneumonia
hospitalizadosMenores5AnosPorDesidratacao=Menores de 5 anos por Desidratação
hospitalizadosPorAbusoAlcool=Por Abuso Álcool
hospitalizadosPorComplicacoesDiabetes=Por Complicações do Diabetes
hospitalizadosPorOutrasCausas=Por Outras Causas
internacoesEmHospitalPsiquiatrico=Internações em Hospital Psiquiátrico
menores28Dias=De menores de 28 dias
obitosMenores28DiasPorDiarreia=Por Diarréia
obitosMenores28DiasPorInfeccaoRespiratoria=Por Infecção Respiratória Aguda
obitosMenores28DiasPorOutrasCausas=Por Outras Causas
obitos28DiasA1AnoPorDiarreia=Por Diarréia
obitos28DiasA1AnoPorInfeccaoRespiratoria=Por Infecção Respiratória Aguda
obitos28DiasA1AnoPorOutrasCausas=Por Outras Causas
obitosAte1AnoPorDiarreia=Por Diarréia
obitosAte1AnoPorInfeccaoRespiratoria=Por Infecção Respiratória Aguda
obitosAte1AnoPorOutrasCausas=Por Outras Causas
de28DiasAte11MesesE29Dias=De 28 dias a 11 meses e 29 dias
menores1Ano=De menores de 1 ano
mulheres10A49Anos=De mulheres de 10 a 49 anos
obitosMulheres10A14Anos=De 10 a 14 anos
obitosMulheres15A49Anos=De 15 a 49 anos
outrosObitos=Outros Óbitos
obitosAdolescentes10A19AnosPorViolencia=De adolescentes (10-19) anos por violência
totalFamiliasCadastradas=Total de famílias cadastradas
obitos=Óbitos
usuarioDevePossuirProfissionalRelacionado=O usuário deve possuir um profissional relacionado.
profissionalUsuarioNaoPossuiRegistroValidoEmUmaEquipe=O profissional do usuário não possui registro válido em uma equipe.
profissionalUsuarioRelacionadoMaisDeUmaEquipe=O profissional do usuário está relacionado a mais de uma equipe.
registroJaCadastradoParaMesInformado=Registro já cadastrado para o mês informado.
mes=Mês
ano=Ano
consultaProducaoMarcadoresAvaliacaoComplementar=Consulta de Produção e de Marcadores para Avaliação - Complementar (PMA2-C)
cadastroProducaoMarcadoresAvaliacaoComplementar=Cadastro de Produção e de Marcadores para Avaliação - Complementar (PMA2-C)
tiposConsultaMedica=Tipos de Consulta Médica
demandaAgendada=Demanda Agendada
demandaImediada=Demanda Imediata
cuidadoContinuado=Cuidado Continuado
urgenciaObservacao=Urgência Observação
tiposAtendimentoMedicoEnfermeiro=Tipos de Atendimento do Médico e de Enfermeiro
usuarioAlcool=Usuário de álcool
usuarioDrogas=Usuário de drogas
tiposAtendimentoCirurgiaoDentista=Tipos de Atendimento do Cirurgião Dentista
primeiraConsultaOdontologicaProgramatica=1ª Consulta Odontológica Programática
escovacaoDentalSupervisionada=Escovação Dental Supervisionada
tratamentoConcluido=Tratamento Concluído
atendimentoGestantes=Atendimento a gestantes
instalacoesProtesesDentarias=Instalações de Próteses Dentárias
encaminhamentoSaudeBucal=Encaminhamento da Saúde Bucal
atencaoSecundariaSaudeBucal=Atenção Secundária em Saúde Bucal
marcadorSaudeBucal=Marcador de Saúde Bucal
diagnosticoAlteracaoMucosa=Diagnóstico de alteração na mucosa
atividadesProducao=Atividades / Produção
pma2c=PMA2-C
saudeMental=Saúde Mental
urgencia=Urgência
consultaProducaoMarcadoresAvaliacao=Consulta de Produção e de Marcadores para Avaliação (PMA2)
cadastroProducaoMarcadoresAvaliacao=Cadastro de Produção e de Marcadores para Avaliação (PMA2)
consultasMedicas=Consultas Médicas
residentesForaAreaAbrangencia=Residentes fora da área de abrangência
residentesAreaAbrangenciaEquipe=Residentes na área de abrangência da equipe
menor1=< 1
1a4=1 - 4
5a9=5 - 9
10a14=10 - 14
15a19=15 - 19
20a39=20 - 39
40a49=40 - 49
50a59=50 - 59
60OuMais=60 ou mais
tipoAtendimentoMedicoEnfermeiro=Tipo de Atendimento de Médico e de Enfermeiro
puericultura=Puericultura
prevencaoCancerCervicoUterino=Prevenção do Câncer Cérvico-Uterino
dstAids=DST / AIDS
diabetes=Diabetes
hipertensaoArterial=Hipertensão Arterial
hanseniase=Hanseníase
tuberculose=Tuberculose
solicitacaoMedicaExamesComplementares=Solicitação médica de exames complementares
patologiaClinica=Patologia Clínica
radiodiagnostico=Radiodiagnóstico
cipatologicoCervicoVaginal=Cipatológico Cérvico-Vaginal
ultrassonografiaObstetrica=Ultrassonografia Obstétrica
encaminhamentosMedicos=Encaminhamentos médicos
atendimentoEspecializado=Atendimento Especializado
internacaoHospitalar=Internação Hospitalar
urgenciaEmergencia=Urgência / Emergência
internacaoDomiciliar=Internação Domiciliar
atendimentoEspecificoAt=Atendimento específico para AT
visitaInspecaoSanitaria=Visita de Inspeção Sanitária
atendimentoIndividualEnfermeiro=Atendimento individual Enfermeiro
atendimentoindividualOutrosProfNivelSuperior=Atendimento individual (Outros Prof. nível superior)
curativos=Curativos
inalacoes=Inalações
injecoes=Injeções
retiradaPontos=Retirada de pontos
terapiaReidratacaoOral=Terapia de Reidratação Oral
sutura=Sutura
atendimentoGrupoEducacaoSaude=Atendimento em Grupo - Educação em Saúde
procedimentosColetivos1=Procedimentos Coletivos I (PC I)
reunioes=Reuniões
marcadores=Marcadores
valvulopatiasReumaticasPessoas5a14Anos=Valvulopatias reumáticas em pessoas de 5 a 14 anos
acidenteVascularCerebral=Acidente Vascular Cerebral
infartoAgudoMiocardio=Infarto Agudo do Miocárdio
dhegFormaGrave=DHEG (forma grave)
doencaHemoliticaPerinatal=Doença Hemolítica Perinatal
fraturasColoFemurPessoasMais50Anos=Fraturas de colo de fêmur em > 50 anos
meningiteTuberculosaEmMenores5Anos=Meningite tuberculosa em menores de 5 anos
hanseniaseGrauIncapacidadeIIeIII=Hanseníase com grau de incapacidade II e III
citologiaOncoticaNicIII=Citologia Oncótica NIC III (carcinoma in situ)
visitasDomiciliares=Visitas Domiciliares
medico=Médico
enfermeiro=Enfermeiro
outrosProfissionaisNivelSuperior=Outros profissionais de nível superior
profissionaisNivelMedio=Profissionais de nível médio
pma2=PMA2
exportarArquivosSiab=Exportar Arquivos do SIAB
gerar=Gerar
gerarArquivos=Gerar Arquivos
atualizarRegistros=Atualizar Registros
processando=Processando
concluido=Concluído
aguardandoProcessamento=Aguardando Processamento
fazerDownloadArquivo=Fazer download do arquivo
excluirArquivo=Excluir arquivo
visualizarMensagemErro=Visualizar mensagem de erro
ditarDoencas=Editar Doenças
lancamentoTransferenciaEstoque=Lançamento de Transferência do Estoque
msgQuantidadeDeveMaiorZero=A quantidade informada deve ser maior que 0 (zero).
depositoOrigem=Depósito Origem
depositoDestino=Depósito Destino
transferenciaEstoque=Transferência do Estoque
msgProblemasMovimentosEstoque=Problemas com Movimentos de Estoque
msgQuantidadeMaiorQueDisponivelEstoque=Quantidade maior do que o disponível em Estoque
editarDoencas=Editar Doenças
marcadorPneumoniaPessoasMenores5Anos=Pneumonia em < 5 anos
campoXNaoPodeSerMaiorQueY=Campo '${x}' não pode ser maior do que '${y}'.
campoXNaoPodeSerMenorQueY=Campo '${x}' não pode ser menor do que '${y}'.
campoXMaisYNaoPodeSerMaiorQueZ=Campo '${x}' + '${y}' não pode ser maior do que '${z}'.
grupoGcampoXNaoPodeSerMaiorQueY=Grupo '${g}', campo '${x}' não pode ser maior do que '${y}'.
grupoGcampoXNaoPodeSerMenorQueY=Grupo '${g}', campo '${x}' não pode ser menor do que '${y}'.
grupoGcampoXMaisYNaoPodeSerMaiorQueZ=Grupo '${g}', campo '${x}' + '${y}' não pode ser maior do que '${z}'.
grupoGcampoXNaoPodeSerMaiorQueYMaisZ=Grupo '${g}', campo '${x}' não pode ser maior do que '${y}' + '${z}'.
ate4Meses=Até 4 meses
ate1Ano=Até 1 ano
de1A2Anos=De 1 a 2 anos
grupoCriancasAte4Meses=Crianças / Até 4 meses
grupoCriancasAte1Ano=Crianças / Até 1 ano
grupoCriancasDe1A2Anos=Crianças / De 1 a 2 anos
grupoCriancasMenores2Anos=Crianças / Menores de 2 anos
familias=Famílias
validacaoObitosAte11Meses=Grupo 'Óbitos / De 28 dias a 11 meses e 29 dias', campos 'Por Diarréia' + 'Por Infecção Respiratória Aguda' + 'Por Outras Causas' não pode ser maior que CRIANCAS 'De 0 a 11 meses e 29 dias'
validacaoObitosAdolescentes=Grupo 'Óbitos', campos 'De adolescentes (10-19) anos por violência' não pode ser maior que 'Outros Óbitos' + 'De 15 a 49 anos' + 'De 10 a 14 anos'
total=Total
calculoEstoqueMinimo=Cálculo do Estoque Mínimo
processar=Processar
dataInicial=Período Inicial
dataFinal=Período Final
componentes=Componentes
cadastroComponente=Cadastro de Componente
salvarComponente=Salvar Componente
salvarFamilia=Salvar Família
selecioneArea=Selecione a Área
observacaoObrigatoria=Observação Obrigatória
problemaCarregarParametroTipoDocumentoTransferenciaEntrada=Problema ao Carregar Parametro Tipo Documento Transferência Entrada
informePeloMenosUmMovimentoEstoque=Informe Pelo Menos Um Movimento Estoque
msgDataInventarioNaoPodeSerAnteriorDataAtual=Data de Inventário não pode ser Anterior à Data Atual.
msgDataValidadeNaoPodeSerAnteriorDataAtual=Data de Validade não pode ser Anterior à Data Atual.
movimentacaoEstoque=Movimentação de Estoque
movimentacoes=Movimentações
precoMedio=Preço Médio
precoTotal=Preço Total
lancamentoInventarioIndividual=Lançamento de Inventário Individual
informeDeposito=Informe Depósito
movimentacaoJaAdicionadoAtualizarDados=Movimentaçao já adicionada, Deseja atualizar?
dataInventario=Data do Inventário
msgNaoPermitidoSalvarMaterialSubgrupoConfigurado=Não é permitido salvar um Material com um SubGrupo configurado para medicamento!
nReceita=Nr. Receita
ultDispensacao=Últ. Dispensação
aDispensar=À Dispensar
proxDispensacao=Próx. Dispensação
diasProxDispensacao=Dias Próx. Dispensação
receitas=Receitas
selecionar=Selecionar
saldoDispensadoSucesso=Saldo dispensado com sucesso!
dispensacoes=Dispensações
dataHora=Data / Hora
consultaEstado=Consulta de Estado
cadastroEstado=Cadastro de Estado
cadastroFeriado=Cadastro de Feriado
consultaFeriado=Consulta de Feriado
variavel=Variável
fixo=Fixo
federal=Federal
estadual=Estadual
municipal=Municipal
tipoFeriado=Tipo de Feriado
feriado=Feriado
diaMes=Dia e Mês
copiarEndereco=Copiar Endereço
ocupacoes=Ocupações
cadastroUnidadeExterna=Cadastro de Unidade Externa
consultaUnidadeExterna=Consulta de Unidade Externa
unidadeExterna=Unidade Externa
secretariaSaude=Secretaria de Saúde
atividades=Atividades
fax=Fax
cnpj=CNPJ
cadastroRegionalSaude=Cadastro de Regional de Sáude
consultaRegionalSaude=Consulta de Regional de Sáude
regionalSaude=Regional de Sáude
cadastroBairro=Cadastro de Bairro
consultaBairro=Consulta de Bairro
bairroRua=Bairro / Rua
nomePaciente=Nome Paciente
relacaoUsuarioCadsusSemDomicilio=Relação de Usuário Cadsus sem Domicílio
usuarioSemACS=Usuário sem ACS
enderecoDomicilio=Domicílio
periodoCadastro=Período Cadastro
agruparDomicilio=Agrupar Domicílio
relacaoUsuarioCadsus=Relação de Usuário Cadsus
microArea=Microárea
domicilios=Domicílios
mesAnoNaoPodeSerSuperiorAoMesAnoAtual=O mês/ano informado não pode ser superior ao mês/ano atual.
consultaNoticias=Consulta de Notícias
cadastroNoticia=Cadastro de Notícia
dadosNoticia=Dados da Notícia
noticias=Notícias
comunicacao=Comunicação
titulo=Título
lerNoticia=Ler notícia
noticia=Notícia
ultimasNoticias=Últimas Notícias
msgPrecoMedioDeveMaiorZero=O preço médio informado deve ser maior que 0 (zero).
semCnsAprovado=Sem CNS e Aprovado
semDocumento=Sem Documentos
semCnsNaoAprovado=Sem CNS Não Aprovado
comCns=Com CNS Não Aprovado
relacaoUsuariosProvisorios=Relação de Usuário Provisórios
relacaoProfissionais=Relação de Profissionais
gruposCbos=Grupos CBOs 
grupoCbo=Grupo CBO
subGrupoCbo=SubGrupo CBO
exibirVinculos=Exibir Vínculos
competenciaInicial=Competência Inicial
competenciaFinal=Competência Final
relacaoCargaHoraria=Relação de Carga Horária
cadastroProfissional=Cadastro de Profissional
consultaProfissional=Consulta de Profissional
vinculosProfissional=Vínculos Profissional
dadosProfissional=Dados do Profissional
consultaProfissionais=Consulta de Profissionais
sus=SUS
naoSus=Não SUS
profissional.nome=Nome
codigoCns=Código do CNS
cadastramento=Cadastramento
identidadeNumeroAbv=Ident. Número
identidadeDataEmissaoAbv=Ident. Data de Emissão
identidadeUFAbv=Ident. UF
identidadeOrgaoAbv=Ident. Órgão
nomeLogradouroAbv=N. Logradouro
dadosEndereco=Dados do Endereço
motivosDesligamentos=Motivos de Desligamentos
motivoDesligamento=Motivo de Desligamento
atendimentoSus=Atendimento ao SUS
vinculacao=Vinculação
subTipo=SubTipo
dataDesligamento=Data de Desligamento
cargaHorariaSemanal=Carga Horária Semanal
ambulatorial=Ambulatorial
hospitalar=Hospitalar
informeUnidade=Informe a Unidade
informeCBO=Informe o CBO
informeVinculacao=Informe a Vinculação
informeTipo=Informe o Tipo
informeSubTipo=Informe o SubTipo
tipoEquipe=Tipo de Equipe
orgaoEmissor=Órgão Emissor
vinculosAtivos=Vínculos Ativos
informeDataEntrada=Infome a Data de Entrada
informeCompetenciaInicial=Informe a Competência Inicial
informeCompetenciaFinal=Informe a Competência Final
dadosVinculos=Dados dos Vínculos
vinculoJaAdicionado=Vínculo Já Adicionado
dataDesligamentoNaoPodeMenorDataEntrada=A data de Desligamento não pode ser menor que a data de entrada
competenciaFinalMenorCompetenciaInicial=A Competência Final não pode ser menor que a Competência Inicial
dataDesligamentoMenorDataEntrada=A Data de Desligamento não poder ser menor que a Data de Entrada
informeMotivoDesligamento=Informe o Motivo de Desligamento
msgValidacaoDesligamentoProfissional=Quando for Desligamento do Profissional os Campos Competência Final, Data de Desligamento e Motivo de Desligamento são Obrigatórios
historico=Histórico
relacaoEquipes=Relação de Equipes
digiteParaBuscar=Digite para buscar.
nenhumResultadoEncontrado=Nenhum resultado encontrado.
procurando=Procurando...
naoInformado=Não informado
numeroFamilia=Família
dataPrescricao=Data da Prescrição
dataPrescricaoAbv=Dt Prescrição
receitaJaUtilizada=Receita já utilizada.
informeProfissional=Informe o Profissional
produtoJaAdicionado=Produto já adicionado.
medicamentoJaDispensadoPacienteDataProximaDispensacao=Medicamento {0} já dispensado para o paciente, data da próxima dispensação: {1}
salvarContinuar=Salvar / Continuar
medicamentoJaDispensado=Medicamento já dispensado
medicamentoJaAdicionado=Medicamento já adicionado
loteJaAdicionado=Lote já adicionado
tipoPrescricao=Tipo da Prescrição
msgDataInventarioNaoPodeSerPosteriorDataAtual=Data de Inventário não poder ser posterior a Data Atual
dataProxDispensacao=Data Próx. Dispensação
calculoQuantidade=Cálculo da Quantidade
frequencia=Frequência
intervalo=Intervalo
calcularQuantidade=Calcular Quantidade
periodoTratamento=Período de Tratamento
diaria=Diária
hora=Hora
semanal=Semanal
mensal=Mensal
semana=Semana
origem=Origem
paraReceitaUsoContinuoProdutoUsoContinuoReceitaBasica=Para receitas de uso contínuo, o produto deve ser de uso contínuo e a receita deve ser do tipo básica.
receitaContinua=Receita Contínua
codigoSiab=Código do Siab
consultaOcupacao=Consulta de Ocupação
cadastroOcupacao=Cadastro de Ocupação
produtoNaoPossuiTipoReceitaInformado=Produto não possui o tipo de receita informado.
tipoLiberacao=Tipo de Liberação
acerto=Acerto
consultaTipoMovimentacao=Consulta de Tipos de Movimentação
cadastroTipoMovimentacao=Cadastro de Tipo de Movimentação
consorcio=Consórcio
tipoMovimentacao=Tipo de Movimentação
consultaProcedimentosConsorcio=Consulta de Procedimentos do Consórcio
dadosConsorcio=Dados do Consórcio
formaOrganizacao=Forma de Organização
estrutura=Estrutura
procedimentosConsorcio=Procedimentos do Consórcio
procedimentosSalvosSucesso=Procedimentos salvos com sucesso!
dataCompetencia=Data da Competência
informeQuantidadeDispensar=Informe a quantidade à dispensar
dadosMedicamento=Dados do Medicamento
codigoDCB=Código DCB
nomeDCB=Nome DCB
concentracao=Concentração
codigoMinisterioSaude=Código do Ministério da Saúde 
usoContinuo=Uso Contínuo
continuo=Contínuo
numeroLista=Número da Lista
rename=Rename
dadosDispensacaoPrescricao=Dados da Dispensação/Prescrição
tipoDispensacaoPrescricao=Tipo Dispensação / Prescrição
quantidadeMedicamento=Quantidade do Medicamento
limiteDispensacao=Limite da Dispensação
validadeReceitaContinuaDias=Validade da Receita Contínua (Dias)
gotas=Gotas
ui=UI
ml=ml
nenhuma=Nenhuma
receitaPreparacaoMagistralP=Preparação Magistral
tipoViasMedicamentos=Tipo de Vias de Medicamentos
vias=Vias
via=Via
consultaMedicamentos=Consulta de Medicamentos
produto.subGrupo.roGrupoProduto=Grupo Produto
produto.subGrupo=SubGrupo
cadastroMedicamento=Cadastro de Medicamento
msgNaoPermitidoSalvarMedicamentoSubGrupoConfigurado=Não é permitido salvar um Medicamento com um SubGrupo não configurado para medicamento!
programaSaudeJaAdicionado=Programa de Saúde já Adicionado
viaJaAdicionada=Via já Adicionada
msgCodMinSaudeInvalido=Codigo Ministério da Saúde Inválido, o Codigo deve conter 13 caracteres.
matricula=Matrícula
consultaProdutosPorUnidade=Consulta de Produtos por Unidade
estoqueMinimoAbv=Est. Mínimo
quantidadePadraoAbv=Qt. Padrão
cadastroProdutoPorUnidade=Cadastro de Produto por Unidade
produtosPorUnidade=Produtos por Unidade
id.empresa=Unidade
id.produto=Produto
padraoDispensacao=Padrão Dispensação
informeUnidadeOrigem=Informe a Unidade de Origem
informeUnidadeDestino=Informe a Unidade de Destino
informeAoMenosUmaEmpresaDestino=Informe ao menos uma Empresa de Destino
unidadeSelecionadaNaoPossuiProdutosParaSeremCopiados=Unidade Selecionado não possui Produtos para ser copiado
copiarUnidade=Copiar Unidade
copiarEstoqueEmpresaPorUnidade=Copiar Estoque Empresa por Unidade
unidadeJaAdicionada=Unidade Já Adicionada
copiarPorUnidade=Copiar por Unidade
copiarPorProduto=Copiar por Produto
copiarEstoqueEmpresaPorProduto=Copiar Estoque Empresa por Produto
produtoOrigem=Produto de Origem
produtoDestino=Produto de Destino
informeAoMenosUmProdutoDestino=Informe ao Menos um Produto de Destino
informeProdutoDestino=Informe um Produto de Destino
informeProdutoOrigem=Informe um Produto de Origem
produtoOrigemNaoPossuiEstoqueEmpresa=Produto Origem não possui Estoque Empresa
copiarProduto=Copiar Produto
produto.concentracao=Concentração
produto.codigoDcb=Código DCB
produto.nomeDcb=Nome DCB
dataEmissaoAbv=Dt. Emissão
dataPortariaAbv=Dt. Portaria
recebimento=Recebimento
confirmada=Confirmada
dataEmissaoNaoPodeSerSuperiorDataPortaria=Data de Emissão não pode ser superior a Data de Portaria
dataPortariaNaoPodeSerSuperiorDataAtual=Data de Portaria não pode ser superior a Data Atual
unidadeAbv=UN
id.fornecedor=Fornecedor
id.numeroNotaFiscal=Documento
id.serie=Série
informeAoMenosUmItem=Informe ao menos um item.
naoPermitidoInformarMesmoProdutoPrecoUnitarioDiferente=Não é permitido informar o mesmo produto com Preço Unitário diferente.
prestador=Prestador
prestadoresServico=Prestadores de Serviço
servicosSalvosSucessoConsorcio=Serviços salvos com sucesso! Consórcio:
procedimentosPrestador=Procedimentos por Prestador
consultaProcedimentosPrestador=Consulta de Procedimentos por Prestador
cadastroProcedimentosPrestador=Cadastro de Procedimentos por Prestador
dadosPrincipais=Dados Principais
tipoReceitaNaoPodeSerDispensadoPelaUnidadeLogada=O tipo de receita não pode ser dispensado pela unidade logada.
informeProcedimento=Informe o procedimento!
aReferenciaXJaEstaSendoUtilizada=A referência {0} já está sendo utilizada.
vlReferencia=Vl. Referência
vlPersonalizado=Vl. Personalizado
consultaTipoConta=Consulta de Tipos de Conta
cadastroTipoConta=Cadastro de Tipo de Conta
tipoConta=Tipo de Conta
consultaConta=Consulta de Contas
cadastroConta=Cadastro de Conta
consorciado=Consorciado
dadosConta=Dados da Conta
subcontas=Subcontas
informeTipoConta=Informe o tipo de conta
jaExisteSubcontaComTipoSelecionado=Já existe uma subconta com o tipo selecionado
conta=Conta
saldoAtual=Saldo Atual
valorReservado=Valor Reservado
desejaRealmenteExcluirNotaFiscal=Deseja Realmente Excluir a Entrada
medicamentoNaoPossuiTipoReceitaConfiguradoFavorAjustar=O medicamento não possui o tipo de receita configurado, favor ajustar
usuarioCadastro=Usuário Cadastro
msg_ja_existe_profissional_com_cpf=Já existe um profissional cadastrado com o CPF {0} - {1}
msg_cns_ja_cadastrado_X=Cns já cadastrado! Profissional: {0}
inativar=Inativar
desejaInativarProduto=Deseja Inativar Produto
paraUnidade=para a Unidade
desejaAtivarProduto=Deseja Ativar Produto
oProcedimentoJaEstaAdicionado=O procedimento já está adicionado
registro=Registro
grupoSubGrupo=Grupo / SubGrupo
notaFiscalXConfirmada=Entrada de Materiais/Medicamentos {0} confirmada.
consultaProvisaoOrcamentaria=Consulta de Provisão Orçamentária
cadastroProvisaoOrcamentaria=Cadastro de Provisão Orçamentária
jaExisteProvisaoParaEstaSubConta=Já existe uma provisão para este Tipo de Conta
dadosProvisao=Dados da Provisão
anoBase=Ano Base
valorExtra=Valor Extra
provisaoSalvaSucessoConsorcioConsorciado=Provisão salva com sucesso! Consórcio: {0}, Consorciado {1}
consorciadoNaoPossuiContaPadraoDefinida=O consorciado não possui conta padrão definida
contaPadraoDefinidaParaConsorciadoEstaInativa=A conta padrão definida para o consorciado está inativa
provisaoOrcamentaria=Provisão Orçamentária
lancamentoCreditoProvisionado=Lançamento de Crédito Provisionado
dadosLancamento=Dados do Lançamento
mesesReferencia=Meses de Referência
parametroXNaoEstaDefinido=O parâmetro {0} não está definido
tipoMovimentacaoProvisionamentoDefinidoParametroDeveSerTipoCredito=O tipoMovimentacaoProvisionamento definido no parâmentro deve ser do tipo crédito
valorDeveSerMaiorZero=O valor deve ser maior do que zero
selecioneTipoConta=Selecione o Tipo de Conta
lancamentoRealizadoSucesso=Lançamento realizado com sucesso
creditos=Créditos
creditoEmConta=Crédito em Conta
tiposMovimentacao=Tipos de Movimentação
creditoTotal=Crédito Total
subConta=Tipo de Conta
insiraAlgumaMovimentacao=Insira alguma movimentação
relacaoNotasFiscais=Relação das Entradas
emissao=Emissão
resumoNotasFiscais=Resumo das Entradas
crescente=Crescente
decrescente=Decrescente
quantidadeCompradaAbv=Qnte. Comprada
novoBairro=Novo Bairro
deletar=Deletar
id.cidade=Cidade
id.descricao=Descrição
consultaPedidoTranferencia=Consulta dos Pedidos de Transferência
destino=Destino
pedidoTransferencia=Pedido de Transferência
cadastroPedidoTransferencia=Cadastro do Pedido de Transferência
quantidadeSolicitadaAbv=Qtd. Solicitada
quantidadeSolicitada=Quantidade Solicitada
dataValidadeAbv=Dt. Validade
dataUltimoPedidoAbv=Dt. Últ. Pedido
dataUltimoPedido=Data do Último Pedido
dadosItens=Dados dos Itens
dadosPedido=Dados do Pedido
padraoEnvio=Padrão de Envio
quantidadeEnviada=Quantidade Enviada
estoqueDisponivel=Estoque Disponível
estoqueUnidade=Estoque Unidade
quantidadeSolicitadaNaoMaiorEstoqueDisponivel=Quantidade Solicitada não pode ser maior que o Estoque Disponível
quantidadeSolicitadaDeveMaiorZero=A Quantidade Solicitada informada deve ser maior que 0 (zero).
informeDataPedido=Informe a Data de Pedido
quantidadeSolicitaEstaForaDoPadraoConfirma=Qtdade Solicita esta fora do padrão, Confirma?
cadastroConsorciado=Cadastro de Consorciado
consultaConsorciado=Consulta de Consorciados
contas=Contas
contaPadrao=Conta Padrão
saldoDisponivel=Saldo Disponível
atualizarSaldo=Atualizar Saldo
dadosGuia=Dados da Guia
naoExisteSubContaComTipoContaNaConta=Não exite uma subconta com o tipo {0} na conta {1}
cadastroGuiaProcedimentos=Cadastro da Guia de Procedimentos
cadastrarGuia=Cadastrar Guia
dataAgendamento=Data do Agendamento
prestadores=Prestadores
procedimentosAdicionais=Procedimentos Adicionais
consultaProcedimentosAdicionais=Consulta de Procedimentos Adicionais
cadastroProcedimentosAdicionais=Cadastro de Procedimentos Adicionais
consultaPrestador=Consulta de Prestadores
cadastroPrestador=Cadastro de Prestador
definirPadrao=Definir como padrão
guiaCadastradaSucesso=Guia cadastrada com sucesso
selecionePrestador=Selecione um prestador
nGuia=Nº da Guia
agendadoPara=Agendado Para
novaGuia=Nova Guia
imprimirGuia=Imprimir Guia
dataDeveSerMaiorAtual=A data deve ser maior do que a data atual
reabrirPedido=Reabrir Pedido
cancelarPedido=Cancelar Pedido
imprimirPedido=Imprimir Pedido
desejaRealmenteReabrirPedido=Deseja Realmente Reabrir o Pedido?
dadosEmbarque=Dados do Embarque
enviarPedidoTransferencia=Enviar Pedido Transferência
informeMotorista=Informe o Motorista
informeResponsavelVeiculo=Informe um Responsável ou um veículo
confirmacaoUtilizacaoGuiaProcedimentos=Confirmação de Utilização da Guia de Procedimentos
confirmarGuia=Confirmar Guia
carregarGuia=Carregar Guia
guiasAgendadasHoje=Guias Agendadas para Hoje
nChave=Nº da Chave
chaveInvalidaVerifiqueNumeroInformado=Chave inválida, verifique o número informado e tente novamente
desfazerAlteracoes=Desfazer Alterações
confirmarUtilizacaoGuia=Confirmar Utilização da Guia
confirmarGuiaPacienteValor=Confirmar Guia Nº {0} do paciente {1}, no valor de R${2}?
desejaRealmenteDesfazerAlteracoes=Deseja realmente desfazer as alterações?
confirmacaoUtilizacaoGuia=Confirmação de Utilização da Guia
autorizacao=Autorização
municipio=Município
informeQuantidadeParaProcedimento=Informe a quantidade para o procedimento
guiaXUtilizadaComSucesso=Guia Nº {0} utilizada com sucesso
guiaJaUtilizada=Guia já utilizada
guiaDefinidaNaoPertenceEstePrestador=A Guia definida não pertence a este prestador de serviço
desejaRealmenteCancelarPedidoXcomDestinoX=Deseja Realmente Cancelar o pedido {0} com Destino: {1} ?
detalhesPedido=Detalhes Pedido
pedidosDivergenciaEntrega=Pedidos com Divergência na Entrega
tipoPeriodo=Tipo de Período
dadosConsorciado=Dados do Consorciado
guiasAberto=Guias em Aberto
guiasUtilizadas=Guias Utilizadas
pagamentoGuiasUtilizadas=Pagamento de Guias Utilizadas
dataUtilizacao=Data de Utilização
nGuias=Nº de Guias
guiasSelecionadas=Guias Selecionadas
confirmarPagamento=Confirmar Pagamento
desejaConfirmarPagamentoXGuiasProcedimentoValorY=Deseja confirmar o pagamento de {0} guias de procedimentos no valor de R${1}?
pagamentoRealizadoSucesso=Pagamento realizado com sucesso
detalhesPagamento=Detalhes do Pagamento
nPagamento=Nº do Pagamento
novoPagamento=Novo Pagamento
imprimirRecibo=Imprimir Recibo
pagamentoGuias=Pagamento de Guias
utilizacao=Utilização
empresaPrestador=Prestador
tipoMovimentoPagamentoPrestadorDefinidoParametroDeveSerTipoDebito=O tipoMovimentoPagamentoPrestador definido no parâmentro deve ser do tipo débito
cid=CID
cipe=CIPE
quantidadePrescritaAbv=Qtd. Prescrita
adicionarContinuar=Adicionar / Continuar
medicamentos=Medicamentos
listaMedicamentoPaciente=Lista de Medicamento do Paciente
cadastroMedicamentoControladoPaciente=Cadastro Medicamento Controlado do Paciente
nomeProduto=Nome Medicamento
pedidosEnviadosSeparacao=Pedidos Enviados Para Separação
exibirQuantidadePadrao=Exibir Qtd. Padrão
iniciarNovaPaginaUnidade=Iniciar nova página por Unidade
relacaoProdutosUnidade=Relação de Produtos por Unidade
material=Material
naoAprovado=Não Aprovado
relacaoPedidoTransferencia=Relação Pedido de Transferência
resumoPedidoXEstoque=Resumo do Pedido x Estoque
produtoSemEstoque=Produtos sem Estoque
movimentacaoFinanceira=Movimentação Financeira
movimentacaoConsorciado=Movimentação por consorciado
movimentacaoTipoConta=Movimentação por tipo de conta
informePeloMenosUmaMovimentacao=Informe pelo menos uma movimentação
movimentacaoRealizadaSucesso=Movimentação realizada com sucesso
tiposConta=Tipos de Conta
consorciadoNaoPossuiSubContaTipoInformado=O consorciado não possui uma subconta do tipo informado
quantidadeSolicitadaNaoMaiorEstoqueDisponivelLote=Quantidade Solicitada não pode ser maior que o Estoque Disponível do Lote 
dataAgendamentoDeveSerMaiorIgualDataAtual=A data de agendamento deve ser maior ou igual a data atual
procedimentoJaAdicionado=Procedimento já adicionado
informeDataAgendamento=Informe a data de agendamento
informeHora=Informe a hora
informePaciente=Informe o paciente
saldoContas=Saldo das Contas
relatorioSaldoContas=Relatório Saldo das Contas
totalGeral=Total Geral
unidadeSaude=Unidade Saúde
emissaoReceituario=Emissão do Receituário
emissaoReceituarioPaciente=Emissão do Receituário do Paciente
prontuario=Prontuário
dataSolicitacaoAbv=Dt. Solicitação
usuarioAtendimento=Usuário Atendimento
gerarReceita=Gerar Receita
numeroChave=Nº da Chave
motivosCancelamento=Motivos de Cancelamento
motivoObservacaoCancelamentoDeveSerDefinido=O Motivo ou Observação do Cancelamento deve ser definido.
receitasCadastradas=Receitas Cadastradas
gerarReceitaPaciente=Confirma a geração das receitas do paciente?
receitaPaciente=Receitas do Paciente
dataCadastroAbv=Dt. Cadastro
profissionalXNaoConfiguradoParaUnidade=Profissinal {0} não está configurado para esta Unidade.
atendimentoFinalizadoComSucesso=Atendimento Finalizado com Sucesso!
detalhamentoMovimentacoes=Detalhamento das Movimentações
rotulo_movimentacao=Movimentação
consultaGuiaProcedimentos=Consulta da Guia de Procedimentos
detalhesGuiaProcedimentos=Detalhes da Guia de Procedimentos
prestadorSelecionadoNaoPossuiGuiasUtilizadas=O prestador selecionado não possui guias utilizadas
selecionePeloMenosUmaGuia=Selecione pelo menos uma guia
consultaGuia=Consulta da Guia
guias=Guias
qPrescrita=Q. Prescrita
qAplicada=Q. Aplicada
totalPrescrito=Total Prescrito
totalAplicado=Total Aplicado
guia=Guia
aplicacao=Aplicação
cancelamento=Cancelamento
pagamento=Pagamento
saldoOrcamentario=Saldo Orçamentário
detalhamentoGuiaProcedimento=Detalhamento das Guias de Procedimentos
aberta=Aberta
utilizada=Utilizada
paga=Paga
extratoContas=Extrato das Contas
detalhamentoProcedimentos=Detalhamento dos Procedimentos
usuarioNaoHabilitadoReceitaConfigurarProfissional=Usuário não esta habilitado para gerar Receita, favor configurar o profissional no cadastro do usuario
cancelamentoNaoPermetidoAtendimentoJaPossuiReceituario=Cancelamento não Permitido! Pois este Atendimento já Possui Receituário Gerado.
resumoProcedimentos=Resumo dos Procedimentos
dataPedidoNaoPodeSerSuperiorDataAtual=Data do Pedido não pode ser superior a Data Atual
nomeRelatorio=Nome Relatório
tipoFornecimento=Fornecimento
tipoAtendimento=Tipo Atendimento
separacaoPedidoTransferencia=Separação do Pedido de Transferência
separarPedido=Separar Pedido
selecioneProduto=Selecione um Produto
itensSolicitados=Itens Solicitados
quantidadeEnviadaAbv=Qt. Enviada
estoqueUnidadeAbv=Est.Unidade
adicioneItemSeparacao=Adicione ao menos um item para separação.
separacaoPedidoXrealizadoComSucesso=Separação do Pedido {0} Relizado com Sucesso.
verTodosRelatorios=Ver todos os relatórios
andamento=Andamento
semPaginas=Sem Páginas
relatorioNaoEncontrado=Relatório não encontrado. 
relatorioDeveTerSidoExcluido=Este relatório deve ter sido excluído do servidor. 
graficoDispensacoes=Gráfico das Dispensações
tipoGrafico=Tipo de Gráfico
linhas=Linhas
barras=Barras
judicial=Judicial
ultimos15dias=Últimos 15 dias
ultimos30dias=Últimos 30 dias
ultimos60dias=Últimos 60 dias
ultimos90dias=Últimos 90 dias
outro=Outro
ultimos3meses=Últimos 3 meses
ultimos6meses=Últimos 6 meses
acompanhamentoSolicitacoes=Acompanhamento das Solicitações
solicitacoes=Solicitações
unidadeSolicitante=Unidade Solicitante
gruposProcedimento=Grupos de Procedimento
grupoProcedimento=Grupo de Procedimento
tipoProcedimento=Tipo de Procedimento
classificacao=Classificação
nenhumItemSelecionadoConfirmaNaoAprovacaoPedido=Nenhum item foi selecionado, Confirma a Não Aprovação do Pedido ?
consultas=Consultas
pacientesAtendidos=Pacientes Atendidos
unidadeExecutante=Unidade Executante
agendados=Agendados
atendidos=Atendidos
faltas=Faltas
graficoComparativoFaltas=Gráfico Comparativo de Faltas
tipoAgendamento=Tipo de Agendamento
perfilAtendimentos=Perfil dos Atendimentos
graficoMensalEncaminhamentos=Gráfico Mensal dos Encaminhamentos
encaminhamentos=Encaminhamentos
grupoEncaminhamento=Grupo de Encaminhamento
tipoEncaminhamento=Tipo de Encaminhamento
campoXObrigatorio=Campo {0} é obrigatório.
resumoSolicitacoes=Resumo das Solicitações
agruparUnidadeSaude=Agrupar por Unidade de Saúde
valorizacaoEstoque=Valorização do Estoque
msgTipoReceitaInvalidoParaProduto=Tipo de Receita inválido para o produto!
graficoValorizacaoEstoque=Gráfico de Valorização do Estoque
separarUnidade=Separar por Unidade
diasVencimento=Dias para o Vencimento
produtosVencendo=Produtos Vencendo
1mes=1 mês
2mes=2 meses
3mes=3 meses
6mes=6 meses
1ano=1 ano
graficoProdutosVencendo=Gráfico dos Produtos Vencendo
estatisticaFaltas=Estatística de Faltas
ePermitidoCarregarSomenteReceituarioPorVez=É permitido carregar somente um Receituario por vez!
codigoBarrasDiferenteFavorRepitaOProcesso=Codigo de Barras diferente! favor repita o Processo!
receitaPacienteDispesadaDia=Recita do Paciente {0}, já foi dispensada no dia: {1}
basicoControlado=Básico / Controlado
quantidadeDeveSerMaiorZero=A quantidade deve ser maior do que zero
resumoDispensacoesJudiciais=Resumo das Dispensações Judiciais
graficoDispensacoesJudiciais=Gráfico das Dispensações Judiciais
graficoConsumoProdutos=Gráfico de Consumo dos Produtos
aPagar=A pagar
naoPermitidoConfirmarGuiaSemProcedimentos=Não é permitido confirmar uma guia sem procedimentos
confirmarRealizacaoProcedimentosPrestador=Confirmar a realização dos procedimentos pelo prestador
separarPrestadores=Separar por Prestadores
visivelConsorciados=Visível aos Consorciados
guiasPagar=Guias a Pagar
valorGuias=Valor das Guias
impostoRendaX=Imposto de Renda ({0}%)
valorPagamento=Valor do Pagamento
dadosPagamento=Dados do Pagamento
detalhamento=Detalhamento
associado=Associado
impostoRenda=Imposto de Renda
consultaPagamentos=Consulta dos Pagamentos
situacaoImposto=Situação do Imposto
confirmarPagamentoImpostoRenda=Confirmar Pagamento do Imposto de Renda
baixaImpostoRenda=Baixa do Imposto de Renda
baixarImpostoRenda=Baixar Imposto de Renda
desejaConfirmarBaixaImposto=Deseja confirmar a baixa de impostos no valor de R${0}, referente ao pagamento realizado ao prestador {1}?
detalhesPagamentoGuiasProcedimento=Detalhes do Pagamento das Guias de Procedimento
relacaoPagamentos=Relação dos Pagamentos
detalhamentoPagamentos=Detalhamento dos Pagamentos
codigoPagamento=Código do Pagamento
consultaDispensacoesMedicamentos=Consulta das Dispensações de Medicamentos
consultaDispensacao=Consulta da Dispensação
nrDispensacao=Nr. Dispensação
dataDispensacao=Data da Dispensação
detalhesDispensacao=Detalhes da Dispensação
qtPrescrita=Qt. Prescrita
qtDispensada=Qt. Dispensada
quantidadeDispensada=Quantidade Dispensada
liberacao=Liberação
acessoRestrito=Acesso Restrito
navegadorDesatualizado=Você sabia que seu navegador está desatualizado?
navegadorRecomendacao=Para usufruir da melhor experiência utilizando nossa aplicação, recomendamos que você atualize para a nova versão ou instale outro browser. Apresentamos uma lista dos browsers mais populares logo abaixo.
internetExplorer=Internet Explorer
googleChrome=Google Chrome 
firefox=Firefox
opera=Opera
safari=Safari
opcoes=Opções
selecionePaciente=Selecione o Paciente
unidadesSaude=Unidades de Saúde
tfd=TFD
dispensacoesPeriodo=Dispensações no Período
listaMedicamentos=Lista de Medicamentos
atendimentoMedicoNaoSeraGeradoPorNaoPosuirItemSelecioando=Atendimento Médico não será gerado por não possuir item selecionado.
tiposExame=Tipos de Exame
consultaCotaExamePpi=Consulta Cota dos Exames PPI
cadastroCotaExamePpi=Cadastro Cota dos Exames PPI
cotasExamesPpi=Cotas dos Exames na PPI
tetoFisico=Cota Física
tetoFinanceiro=Cota Financeira
adicionarFavoritos=Adicionar aos favoritos
geracaoManualCota=Geração Manual da Cota
ultimoMes=Último Mês
ult3Meses=Últ. 3 Meses
ult6Meses=Últ. 6 Meses
ultimoAno=Último Ano
consultaCotaExameRecebidoPpi=Consulta Cota dos Exames Recebidos pela PPI
cadastroCotaExameRecebidoPpi=Cadastro Cota dos Exames Recebidos pela PPI
cotasExamesRecebidosPpi=Cotas dos Exames Recebidos pela PPI
secretaria=Secretaria
consultaGrupoProcedimento=Consulta Grupo de Procedimento
cadastroGrupoProcedimento=Cadastro Grupo de Procedimento
cadastroTipoExameImportacao=Cadastro Tipo de Exame Importação
consultaTipoExameImportacao=Consulta Tipo de Exame Importação
quantidadeMaxExamesAbv=Qt. Máx. Exames
convenio=Convênio
realizaAgendamento=Realiza Agendamento
cadastroTipoExameUnidadeAutorizadora=Cadastro Tipo de Exame x Unidade Autorizadora
consultaTipoExameUnidadeAutorizadora=Consulta Tipo de Exame x Unidade AutorizadoraF
tipoExameUnidadeAutorizadora=Tipo de Exame x Unidade Autorizadora
guiasProcedimentos=Guias de Procedimentos
cotasExamesUnidade=Cotas dos Exames da Unidade
consultaCotaExamesUnidade=Consulta Cota dos Exames da Unidade
cadastroCotaExamesUnidade=Cadastro Cota dos Exames da Unidade
%cota=% Cota
%cotaUtilizada=% Cota Utilizada
registroJaCadastrado=Registro já Cadastrado.
cotaTotalNaoPodeSerSuperior100=Cota Total Não Pode Ser Maior Que 100%
consultaCotaUnidade=Consulta Cota da Unidade
consultaCotaPrestador=Consulta Cota do Prestador
cota=Cota
utilizado=Utilizado
lancamento=Lançamento
selecionePeloMenosEmMedicamento=Selecione pelo menos um medicamento
formularios=Formulários
grupoPpi=Grupo PPI
consultaGrupoProcedimentoProcedimento=Consulta Grupo de Procedimento X Procedimento
valorDiferenciado=Valor Diferenciado
valorTabela=Valor Tabela
tipoExameProcedimento=Tipo de Exame x Procedimento
grupoProcedimentoProcedimento=Grupo de Procedimento X Procedimento
cadastroGrupoProcedimentoProcedimento=Cadastro Grupo de Procedimento X Procedimento
cadastroEstruturaProcedimento=Cadastro por Estrutura do Procedimento
cadastroDetalhado=Cadastro Detalhado
procedimentos=Procedimentos
ppiGrupo=Grupo PPI
msgValorDiferenciado=Se o valor não for informado, será utilizado o valor da tabela de procedimento.
formulario=Formulário
novaReceita=Nova Receita
consultaReceitaContinuaPaciente=Consulta Receita Continua do Paciente
cadastroReceitaContinuaPaciente=Cadastro Receita Continua do Paciente
receitaConinuaPaciente=Receita Continua do Paciente
dataPrescricaoNaoPodeSerMaiorAtual=Data da Prescrição não pode ser maior que a atual!
dataPrescricaoForaValidadeReceitaContinua=Data da Prescrição esta fora da validade da Receita Continua!
consultaPedidosLicitacao=Consulta de Pedidos de Licitação
cadastroPedidoLicitacao=Cadastro de Pedido de Licitação
pedidoLicitacao=Pedido de Licitação
ultPreco=Últ. Preço
informeConsorciado=Informe o consorciado
consultaPrestadorServico=Consulta Prestador de Serviço
prestadorServico=Prestador de Serviço
tetoFisico2=Teto Físico
tetoFinanceiro2=Teto Financeiro
cadastroFpoPrestadorServico=Cadastro da FPO do Prestador de Serviço
cadastroExamePrestador=Cadastro Exame para Prestador
informeExameOuTipoExame=Informe o Exame ou o Tipo de Exame
validarPrestadorProcedimento=Exame {0} está configurado na(s) agenda(s) do(s) profissional(is) {1}, deseja realmente remover?
selecaoExames=Seleção Exames
detalhesPrestadorServico=Detalhes do Prestador Serviço
exame=Exame
examesProcedimento=Exames Procedimento
medicamentoCadastroOutraReceitaFavorVerificar=Medicamento já esta cadastrado em outra receita, favor verificar
consultaFornecedor=Consulta de Fornecedor
tipoFornecedor=Tipo de Fornecedor
cadastroFornecedor=Cadastro de Fornecedor
fisica=Física
juridica=Jurídica
exportacao=Exportação
razaoSocial=Razão Social
anoFixacao=Ano Fixação
homePage=Home Page
consultaLicitacoes=Consulta de Licitações
licitacao=Licitação
nPregao=Nº do Pregão
cadastrarPregao=Cadastrar pregão
processoLicitatorio=Processo Licitatório
dataAprovacao=Data de Aprovação
aprovacao=Aprovação
transferenciaCotaEstabelecimento=Transferência de Cota do Estabelecimento
estabelecimento=Estabelecimento
msgConfirmarTransferenciaUnidadeXParaUnidadeXValorX=Confirma a transferência da Unidade: {0} \npara a Unidade {1} do valor de {2} ?
msgValorTransferenciaNaoPodeSerMaiorSaldoUnidade=Valor da transferência não pode ser maior que o saldo da Unidade
cotaExtra=Cota Extra
cotaUtilizada=Cota Utilizada
cotaEstabelecimento=Cota Estabelecimento
valorTransferencia=Valor Transferência
msgUnidadeXSemCotaCadastradaParaCopetenciaAtual=Unidade {0} sem cota cadastrada para a competência atual
transferenciaSalvaComSucesso=Transferência salva com sucesso
componentePossuiAlgumaDoenca=Componente Possui Alguma Doença ?
cadastroLicitacao=Cadastro de Licitação
dadosLicitacao=Dados da Licitação
pedidosLicitacao=Pedidos de Licitação
pedidosAdicionados=Pedidos Adicionados
aprovar=Aprovar
precoUn=Preço Un.
nPedidos=Nº de Pedidos
aumentarCotaPrestador=Aumentar Cota do Prestador
cotaPrestador=Cota Prestador
cotaAumentadaComSucesso=Cota Aumentada Com Sucesso
msgPrestadorNaoRealizaTipoExameCompetencia=O prestador não está cadastrado para realizar este tipo de exame na competência atual
msgConfirmarCotaExtraParaUnidadeXvalorX=Confirmar cota extra para unidade {0} no valor de {1}?
consultaRequisicao=Consulta da Requisição
solicitante=Solicitante
executante=Executante
dataSolicitacao=Data de Solicitação
nomeProfissional=Nome do Profissional
nomeResponsavel=Nome do Responsável
dataAutorizacao=Data Autorização
motivoCancelamento=Motivo do Cancelamento
usuarioAutorizacao=Usuário Autorização
relacaoExames=Relação dos Exames
profissionalSolicitante=Profissional Solicitante
resumoProcedimentosSolicitados=Resumo dos Procedimentos Solicitados
naoRealizado=Não Realizado
autorizado=Autorizado
resumoExames=Resumo dos Exames
estabelecimentoExecutante=Estabelecimento Executante
tipoDado=Tipo de Dado
cadastroRequisicaoAutorizacao=Cadastro da Requisição / Autorização
dadosRequisicao=Dados da Requisição
msgInformeExame=Informe o exame
msgSomenteExamesMesmoTipoExame=Somente exames com o mesmo tipo de exame podem ser adicionados
msgExameSemProcedimentoDefinido=Exame sem procedimento definido
msgExameJaAdicionado=Exame já Adicionado
msgQuantidadeInvalida=Quantidade Invalida
msgExameUltrapassouCotaUnidade=Exame ultrapassou o saldo da cota da unidade, valor do exame {0}
saldoPrestador=Saldo Prestador
msgExameJaAdicionadoSomarQuantidade=Exame já adicionado, deseja somar a quantidade?
cotaUnidade=Cota Unidade
resumoPedidosLicitacao=Resumo dos Pedidos de Licitação
aguardando=Aguardando
licitado=Licitado
existeItemProdutoAberto=Já existe um item com este produto em aberto
desejaImprimirListagemItens=Deseja imprimir a listagem dos itens?
lancamentoPregao=Lançamento do Pregão
todosNaoCotado=Todos Não Cotado
naoCotado=Não Cotado
produtosLicitados=Produtos Licitados
confirmarRealizacaoPregaoLicitacao=Confirmar realização do pregão para a licitação?
desejaRealmenteRemover=Deseja realmente remover?
informeFornecedor=Informe o fornecedor
informePrecoTotal=Informe o preço total
informeJustificativa=Informe a Justificativa
pregaoRealizadoComSucesso=Pregão realizado com sucesso
justificativa=Justificativa
informeNumeroPregao=Informe o número do pregão
informeValidade=Informe a validade
msgDesejaRealmenteRemoverExameX=Deseja Realmente Remover o Exame {0} ?
encaminharPregao=Encaminhar para Pregão
confirmarEncaminharPregao=Deseja Encaminhar a Licitação para Realização do Pregão?
msgAlteracaoNaoPermitidaJaExisteDispensacaoParaReceita=Alteração não permitida, já existe dispensação para receita
cnpjCpf=CNPJ/CPF
detalhamentoLicitacoes=Detalhamento das Licitações
nLicitacao=Nº da Licitação
obrigatorioInformarLicitacaoPregaoProdutoFornecedor=É obrigatório informar o Nº da Licitação, ou do Pregão, ou o Produto ou o Fornecedor
msgExameGeradoComNumero=Exame gerado com o número: {0}
confirmarAprovacaoLicitacao=Confirmar aprovação da licitação?
dispensacaoUsoContinuoAberto=Dispensação de Uso Continuo em Aberto
produtosConsorcio=Produtos do Consórcio
consultaProdutosConsorcio=Consulta dos Produtos do Consórcio
cadastroProdutoConsorcio=Cadastro de Produto do Consórcio
relatorioDispensacaoMedicamentos=Relatório Dispensação de Medicamentos
relacaoDispensacoes=Relação das Dispensações
relacaoDispensacoesReceituario=Relação das Dispensações de Receituário
precoCusto=Preço de Custo
numeroDispensacao=Número Dispensação
visualizarPreco=Visualizar Preço
tipoPreco=Tipo de Preço
detalhamentoPedidosLicitacao=Detalhamento dos Pedidos de Licitação
quantidadeDispensadaAbv=Qtd. Dispens.
relacaoLiberacoes=Relação das Liberações
liberacoesReceitas=Liberações das Receitas
naoDispensada=Não Dispensada
dispensada=Dispensada
ultimoPreco=Último Preço
dataLiberacao=Data Liberação
detalhesLicitacao=Detalhes da Licitação
usuarioAprovacao=Usuário da Aprovação
numeroDispensacoesAbv=Nr. Dispensações
resumoDispensacoes=Resumo das Dispensações
dataAutorizacaoAbv=Dt. Autorização
autorizador=Autorizador
cancelamentoAutorizacaoExame=Cancelamento Autorização Exame
exameCanceladoComSucesso=Exame Cancelado Com Sucesso
dataDeveSerMenorIgualAtual=A data deve ser menor ou igual à data atual
dataMaiorDataAtual=A data informada não pode ser maior que a data atual
dataMenorDataMinima=A data informada é menor do que a data limite: {0}
msgDataFinalNaoPodeSerInferiorDataInicial=A data Final não deve ser anterior à data inicial
jaExisteNotaNumeroFornecedor=Já existe uma entrada cadastrada com o número de documento informado para o fornecedor selecionado
selecioneLicitacao=Selecione a Licitação
itensRecebimento=Itens do Recebimento
licitacaoXpregaoY=Licitação: {0} Pregão: {1}
quantidadeMaiorSaldoDisponivelLicitacao=A quantidade é maior que o saldo disponível para a licitação
valorItemMaiorSaldoDisponivelLicitacao=O valor do item é maior que o saldo disponível para a licitação
produtoNaoLicitado=O produto não está licitado
precoUnitarioItemXDiferePrecoUnitarioLicitacaoY=O preço unitário do item (R${0}) difere do preço unitário da licitação (R${1})
fornecedorDefinidoNotaDifereLicitacao=O fornecedor definido na entrada difere do definido na licitação
informePeloMenosUmLote=Informe pelo menos um lote
informeValorTotal=Informe o valor total
valorNotaDiferenteTotalItensAjustarOuEditarItens=O valor informado para a entrada de materiais/medicamentos (R${0}) é diferente do valor total dos itens (R${1}). Ajuste o valor total da entrada ou altere os itens adicionados. O que deseja fazer?
lotesProduto=Lotes do Produto
ajustarTotalNota=Ajustar Valor Total da Entrada
continuarEditandoItens=Continuar Editando Itens
registroSalvoSucessoCodigoX=Registro salvo com sucesso! Código: {0}
autoCompleteProdutoNaoPreparadoLote=AutoComplete de produto não preparado para trabalhar com lote
unidadeOrigem2=Unidade Origem
graficoDemonstrativoDispensacao=Gráfico Demonstrativo da Dispensação
graficoCruzamentoDispensacaoPeriodo=Gráfico Cruzamento da Dispensação por Período
resumoAnual=Resumo Anual
periodo1=Período 1
periodo2=Período 2
graficoCruzamentoDispensacao=Gráfico Cruzamento da Dispensação
quantidadeCategoriasAbv=Qtd. Categ.
quantidadeSeriesAbv=Qtd. Séries
dadoCalculo=Dado para Cálculo
categoria=Categoria
msgCategoriaSerieNaoPodemSerIgual=A Categoria e Serie não podem ser iguais.
tiposAtendimentos=Tipos de Atendimentos
boletimDiarioAtendimentos=Boletim Diário dos Atendimentos
iniciarNovaPaginaProfissional=Iniciar Nova Página por Profissional
foiFeitaProcuraComponenteDesejaFazerUmNovoCadastro=Foi feita a procura do Componente que deseja fazer um novo cadastro?
boletimProcedimentos=Boletim de Procedimentos
naoFaturavel=Não Faturável
faturavel=Faturável
agrupar=Agrupar
relatorioAtendimentoOdontologico=Relatório Atendimento Odontológico
exibirPaciente=Exibir Paciente
relatorioPendenciasOdontologicas=Relatório Pendências Odontológicas
quantidadeDeDiasAbv=Qtde. de Dias
relatorioPerfilAtendimento=Relatório Perfil Atendimento
relacaoUsuariosAtendidos=Relação Usuarios Atendidos
relacaoAtendimentos=Relação dos Atendimentos
impressaoFichaAgendamento=Impressão da Ficha Agendamento
relacaoEncaminhamentos=Relação dos Encaminhamentos
visualizarOcorrencia=Visualizar Ocorrência
aguardandoAutorizacao=Aguardando Autorização
aguardandoAgendamento=Aguardando Agendamento
naoAutorizado=Não Autorizado
concluidoComRetorno=Concluído com Retorno
tipoEncaminhento=Tipo Encaminhamento
relacaoVagasUnidade=Relação de Vagas por Unidade
apenasVagasDisponiveis=Apenas Vagas Disponíveis
tipoCota=Tipo Cota
relacaoAgendaContato=Relação da Agenda para Contato
totalizarVagas=Totalizar Vagas
tipoAgenda=Tipo de Agenda
relacaoAgendas=Relação das Agendas
tipoConsulta=Tipo Consulta
relacaoAgendasUnidade=Relação das Agendas por Unidade
relacaoVagasXCotas=Relação das Vagas X Cotas
apenasAgendaCotaDisponivel=Apenas Agenda com Cota Disponível
visualizarUnidade=Visualizar Unidade
relacaoAgendamentos=Relação dos Agendamentos
resumoAgendamentos=Resumo dos Agendamentos
selecioneUmItem=Selecione um Item
distribuicaoMensalAgendamentos=Distribuição Mensal dos Agendamentos
numeroMaximoSerieAbv=Nr. Mx. Série
faixaEtaria=Faixa Etária
distribuicaoAgendamentos=Distribuição dos Agendamentos
estatisticaNaoComparecimento=Estatísticas de Não Comparecimento
relacaoAgendaTfd=Relação Agendamento TFD
profissionalOrigem=Profissional Origem
relacaoPacientesNaoCompareceram=Realação Pacientes Não Compareceram
relacaoTfds=Relação dos TFDs
resumoTfd=Resumo TFD
totalTfd=Total TFD
diasMediaEsperaAbv=Dias méd. Espera
relacaoSolicitacoes=Relação das Solicitações
distribuicaoEncaminhamentos=Distribuição Encaminhamentos
encaminhamento=Encaminhamento
distribuicaoMensalEncaminhamentos=Distribuição Mensal dos Encaminhamentos
consultaPedidosTransferencia=Consulta dos Pedidos de Transferência
produtoNaoPossuiSaldoTransferencia=O produto não possui saldo para transferência
transferencia=Transferência
entrega=Entrega
separacao=Separação
itensPedido=Itens do Pedido
detalhesPedidoTransferencia=Detalhes do Pedido de Transferência
usuarioEntrega=Usuário da Entrega
dataEntrega=Data da Entrega
consultaUnidades=Consulta de Unidade
cadastroUnidade=Cadastro de Unidade
empresaMantenedora=Empresa Mantenedora
modAssist=Mód. Assist.
tipoEstabelecimento=Tipo de Estabelecimento
mantenedora=Mantenedora
registroSaudeAbv=Reg. Saúde
distritoSanitarioAbv=Dist. Sanitário
estabelecimentoExterno=Estabelecimento Externo
almoxarifadoOdonto=Almoxarifado Odonto
almoxarifadoMedicamentos=Almoxarifado Medicamentos
almoxarifadoMateriais=Almoxarifado Materiais
pessoa=Pessoa
microRegiao=Microrregião
codigoOrdemCompraAbv=Cód. OC.
codigoPic=Código PIC
depositoPadrao=Depósito Padrão
depositoVencidoPadrao=Depósito Vencido Padrão
digitaDocMovEstoque=Digita Doc. Mov. Estoque
permEstoqueNegativo=Perm. Estoque Negativo
tiposAlmoxarifadoUnidadePodeFazerPedido=Tipos de Almoxarifado que a Unidade Pode Fazer Pedido
odonto=Odonto
tituloJanela=Título Janela
tipoControle=Tipo Controle
sistema=Sistema
manual=Manual
anvisa=Anvisa
licFuncionamento=Lic. Funcionamento
autorizacaoFuncionamentoAnvisa=Nr. da Autorização de Funcionamento (ANVISA)
encaminhamentosEspecialidade=Encaminhamentos por Especialidade
encaminhamentosSituacao=Encaminhamentos por Situação
resumoEncaminhamentos=Resumo dos Encaminhamentos
totalEncaminhamento=Total Encaminhamento
diasMediaEspera=Dias Méd. Espera
porcentagemFalta=% Faltas
cadastroGrupoEstabelecimento=Cadastro Grupo Estabelecimento
consultaGrupoEstabelecimento=Consulta Grupo Estabelecimento
vigilancia=Vigilância
grupoEstabelecimento=Grupo Estabelecimento
cadastroResponsavelTecnico=Cadastro Responsável Técnico
consultaResponsavelTecnico=Consulta Responsável Técnico
responsavelTecnico=Responsável Técnico
boletimProducaoAmbulatorial=Boletim Produção Ambulatorial (BPA)
modalidade=Modalidade
instrRegistro=Instr. Registro
bpa=BPA
consultaAtividadeEstabelecimento=Consulta Atividade Estabelecimento
cadastroAtividadeEstabelecimento=Cadastro Atividade Estabelecimento
atividadeEstabelecimento=Atividade do Estabelecimento
consultaEstabelecimentos=Consulta de Estabelecimentos
cadastroEstabelecimento=Cadastro de Estabelecimento
dadosGerais=Dados Gerais
matriz=Matriz
porte=Porte
nomeFantasia=Nome Fantasia
tipoPessoa=Tipo Pessoa
regional=Regional
enderecoContatos=Endereço / Contatos
inscricaoEstadual=Inscrição Estadual
inscricaoMunicipal=Inscrição Municipal
inicioFuncionamento=Início Funcionamento
certificacaoAnvisa=Certificação Anvisa
numeroCertificacao=Numero da Certificação
outroSistemaCertificacao=Outro Sistema de Certificação
nomeCertificacao=Nome Certificação
importador=Importador
possuiLicencaTransporte=Possui Liçenca de Transporte
objetivoContratoSocial=Objetivo do Contrato Social
orgaoExpedidor=Orgão Expedidor
dadosComplementares=Dados Complementares
atividadeEconomica=Atividade Econômica
site=Site
protocolo=Protocolo
representanteLegal=Representante Legal
pontoReferencia=Ponto de Referência
nomeRepresentante=Nome Representante
dataExpedicao=Data Expedição
cnae=CNAE
terceirizada=Terceirizada
numeroAutorizacaoAnvisa=Nr. Autorização ANVISA
msg_ja_existe_estabelecimento_com_cnpj_cpf=Já existe um estabelecimento cadastrado com o CNPJ/CPF {0} - {1}
precoUnitarioXItemYDiferePrecoUnitarioZLicitacao=O preço unitário (R${0}) do item ({1}) difere do preço unitário da licitação (R${2})
dataEmissaoMaiorDataPortaria=A data de emissão não deve ser posterior a data de portaria.
localFornecimento=Local Fornecimento
dataPortariaMaiorDataEmissao=A data de portaria não deve ser posterior a data de emissão.
acaoNaoImplementada=Ação não implementada
cadastroEntradaMateriaisMedicamentos=Cadastro da Entrada de Materiais/Medicamentos
consultaEntradaMateriaisMedicamentos=Cadastro da Entrada de Materiais/Medicamentos
detalhesEntradaMateriaisMedicamentos=Detalhes da Entrada de Materiais/Medicamentos
entradaMateriaisMedicamentos=Entrada de Materiais/Medicamentos
informeOpcaoDesejada=Informe a opção desejada
consultaSeparacaoPedidos=Consulta da Separação dos Pedidos
desejaSepararItensPedidoTransferencia=Deseja separar os itens para o pedido de transferência?
separacaoPedidosTransferencia=Separação dos Pedidos de Transferência
qtSeparada=Qt. Separada
itensSeparados=Itens Separados
separar=Separar
quantidadeNaoPodeMaiorQuantidadeSolicitada=A quantidade não pode ser maior do que a quantidade solicitada
pedidoXSeparadoComSucesso=Pedido {0} separado com sucesso!
estFisico=Est. Físico
estReservado=Est. Reservado
estDisponivel=Est. Disponível
quantidadeMaiorEstoqueDisponivel=A quantidade informada é maior do que o estoque disponível
manutencaoLicitacao=Manutenção da Licitação
alterarPreco=Alterar o Preço
alterarFornecedor=Alterar o Fornecedor
alterarValorTotal=Alterar o Valor Total
precoUnitarioAtual=Preço Unitário Atual
novoPrecoUnitario=Novo Preço Unitário
informeNovoPreco=Informe o novo preço
fornecedorAtual=Fornecedor Atual
novoFornecedor=Novo Fornecedor
precoTotalAtual=Preço Total Atual
novoPrecoTotal=Novo Preço Total
informeNovoFornecedor=Informe o novo fornecedor
validadeInformadaVencida=A validade informada está menor que a data atual. Confirma?
responsavelJaCadastroEmOutroEstabelicimentoXaX=Responsável já cadastrado em outro estabelecimento. {0} - {1}
conferenciaBpa=Conferência BPA
visualizarAtendimentos=Visualizar Atendimentos
relacaoAtividadeProfissional=Relação Atividade Profissional
profissionaisSemAtividade=Apenas Profissionais sem Atividade Programada
atividadeGrupo=Atividade Grupo
tipoAtividade=Tipo Atividade
relacaoLocalAtividade=Relação Local Atividade
relacaoTipoAtividade=Relação Tipo Atividade
consultaLocalFornecimentoMedicamento=Consulta de Local Fornecimento Medicamento
cadastroLocalFornecimento=Cadastro de Local Fornecedimento Medicamento
localFornecimentoMedicamento=Local Fornecimento Medicamento
vlSus=Vl. SUS
cadastroTipoAtendimento=Cadastro Tipo Atendimento
consultaTipoAtendimento=Consulta Tipo Atendimento
exigeProfissional=Exige Profissional
proximoTipoAtendimentoAbv=Próx. Tipo Atendimento
encaminhaObservacao=Encaminha para Observação
imprimeProntuario=Imprime Prontuário
naturezaProcura=Natureza Procura
cadastroNaturezaProcura=Cadastro de Natureza Procura
consultaNaturezaProcura=Consulta de Natureza Procura
descricaoReduzida=Descrição Reduzida
permiteAgendamento=Permite Agendamento
manutencaoPedidoLicitacao=Manutenção do Pedido de Licitação
nPedido=Nº do Pedido
vlRecebido=Vl. Recebido
vlTotal=Vl. Total
qtdRestante=Qtd. Restante
informeNovoValorTotal=Informe o novo valor total
valorTotalAtual=Valor Total Atual
novoValorTotal=Novo Valor Total
saldoLicitacao=Saldo da Licitação
classificacaoAtendimento=Classificação Atendimento
consultaClassificacaoAtendimento=Consulta Classificação Atendimento
cadastroClassificacaoAtendimento=Cadastro Classificação Atendimento
consultaEncaminhamento=Consulta Encaminhamento
geraEncaminhamento=Gera Encaminhamento
cadastroProgramaSaude=Cadastro Programa Saúde
consultaProgramaSaude=Consulta Programa Saúde
cadastroTipoEncaminhamento=Cadastro Tipo de Encaminhamento
consultaTipoEncaminhamento=Consulta Tipo de Encaminhamento
consumoMesAnterior=Consumo Mês Anterior
favorAvaliarAdicionarTodosItensParaSalvar=Favor avaliar e adicionar todos os itens para Salvar
receitanaoContemNenhumItemValidoParaDispensacao=Receita não contém nenhum item válido para dispensação
loteInexistente=Lote inexistente
aindaExistemItensNaoSeparados=Ainda existem itens não separados
consultaNaturezaTipo=Consulta Natureza / Tipo
cadastroNaturezaTipo=Cadastro Natureza / Tipo
numeroVagasAbv=Nr. Vagas
naturezaTipo=Natureza / Tipo
controleAtendimentoAbv=Ctr. Atendimento
segunda=Segunda
terca=Terça
quarta=Quarta
quinta=Quinta
sexta=Sexta
dias=Dias
visivel=Visível
selecioneUnidade=Selecione a Unidade
profissionalJaAdicionado=Profissional Já Adicionado
relacaoFamilias=Relação das Famílias
resumofamilias=Resumo das Famílias
consultaAliquotasImposto=Consulta das Alíquotas de Imposto
cadastroAliquotaImposto=Cadastro de Alíquota de Imposto
tipoImposto=Tipo de Imposto
anoExercicio=Ano de Exercício
valorMinimo=Valor Mínimo
valorMaximo=Valor Máximo
aliquota%=Alíquota (%)
jaExisteItemValorDentroDesteIntervalo=Já existe um item com um valor dentro deste intervalo
aliquotaNaoPodeMaior100=A alíquota não pode ser maior do que 100
informeValorMinimo=Informe o valor mínimo
informeValorMaximo=Informe o valor máximo
informeAliquota=Informa a alíquota
aliquotaImposto=Alíquota de Imposto
alterarPrecoUnitario=Alterar o Preço Unitário
alterarValorTotalLicitado=Alterar o Valor Total Licitado
consultaProcedimentos=Consulta de Procedimentos
consultaProcedimento=Consulta de Procedimento
tipoAtendimentoAcesso=Tipo Atendimento Acesso
informeTipoAtendimentoAcesso=Informe Tipo Atendimento Acesso
informeTipoAtendimento=Informe Tipo Atendimento
optanteSimples=Optante pelo Simples
inss=INSS
iss=ISS
impostos=Impostos
empresaPrestador.descricao=Descrição
empresaPrestador.cidade=Cidade
consultaTipoReceita=Consulta Tipo Receita
cadastroTipoReceita=Cadastro Tipo Receita
tipoReceita.descrica=Descrição
quantidadeMaxima=Quantidade Máxima
diasMaximoTratamento=Dias Máximo Tratamento
imprimeReceita=Imprime Receita
controlada=Controlada
lista=Lista
masgistral=Masgistral
prestadorNaoPossuiTipoCobrancaImpostoRendaDefinido=O prestador não possui o tipo de cobrança de Imposto de Renda definido
prestadorNaoPossuiTipoCobrancaInssDefinido=O prestador não possui o tipo de cobrança de INSS definido
prestadorNaoPossuiTipoCobrancaIssDefinido=O prestador não possui o tipo de cobrança de ISS definido
impostoInssX=INSS ({0}%)
impostoIssX=ISS({0}%)
confirmarPagamentoImposto=Confirmar Pagamento do Imposto
desejaConfirmarBaixaImpostoRenda=Deseja confirmar a baixa de Imposto de Renda no valor de R${0}, referente ao pagamento realizado ao prestador {1}?
desejaConfirmarBaixaImpostoInss=Deseja confirmar a baixa de INSS no valor de R${0}, referente ao pagamento realizado ao prestador {1}?
desejaConfirmarBaixaImpostoIss=Deseja confirmar a baixa de ISS no valor de R${0}, referente ao pagamento realizado ao prestador {1}?
baixarInss=Baixar INSS
baixarIss=Baixar ISS
tipoAtendimentoCboProcedimento=Tipo Atendimento CBO Procedimento
confirmarEntregaPedido=Confirmar Entrega do Pedido
pedidoEncaminhado=Pedido Encaminhado
pedidoEncaminhadoSucesso=Pedido encaminhado com sucesso!
informeResponsavel=Informe o Responsável
consultaTipoSolicitacao=Consulta Tipo de Solicitação
cadastroTipoSolicitacao=Cadastro Tipo de Solicitação
tipoSolicitacao=Tipo de Solicitação
produtoSelecionadoNaoPossuiEstoqueNoAlmoxarifadoSelelcionado=Produto selecionado não possui estoque no almoxarifado selecionado
buscaAtivaCapsCancelada=Busca Ativa CAPs cancelada
buscaAtivaConcluida=Busca Ativa CAPs concluída
responsavelEntrega=Responsável pela Entrega
cadastroRequerimento=Cadastro de Requerimento
rgCpfSolicitante=RG / CPF
requerimentoVigilancia.nomeSolicitante=Nome
requerimentoVigilancia.tipoSolicitacao=Solicitação 
validadeEspecial=Validade Especial
prazoValidade=Prazo de Validade
cargo=Cargo
registrarRequerimento=Registrar Requerimento
dataValidadeDeveSerMaiorX=Data de Validade deve ser maior que {0}
dataValidadeDeveSerMenorX=Data de Validade deve ser menor que {0}
informePlaca=Informe a Placa
informeTipoVeiculo=Informe Tipo Veículo
informeEspecificacao=Informe Especificação
especificacao=Especificação
restricoes=Restrições
tipoVeiculo=Tipo Veículo
veiculos=Veículos
placaJaInformada=Placa já Informada
observacaoAlvaraSanitario=Observação para Alvará Sanitário
estabelecimentoXNaoPossuiNenhumVeiculoCadastroParaEmissaoLicencaSanitaria=O estabelecimento {0} não possui nenhum veículo cadastrado para a emissão de Licença Sanitária
quantidadeNaoPodeMaiorEstoqueDisponivel=A quantidade não pode ser maior do que o estoque disponível
periodoDeveConterApenas12Meses=O periodo deve conter apenas 12 meses!
produtoSelecionadoNaoConfiguradoTipoReceita=O produto selecionado não esta configurado com um tipo de receita, favor configurar
alvara=Alvará
validadeAlvara=Validade Alvará
consultaRequerimento=Consulta de Requerimento
requerimento=Requerimento
registrarInspecao=Registrar Inspeção
impressaoDocumento=Impressão de Documento
encaminharDocumentoEntrega=Encaminhar Documento para Entrega
receberDocumentoParaEntrega=Receber Documento para Entrega
confirmarEntregaDocumento=Confirmar Entrega do Documento
consultarDetalhesRequerimento=Consulta Detalhes do Requerimento
estabelecimentoXConfirmaRecebimentoRequerimento=Confirma o Recebimento do Requerimento para o Estabelecimento: {0} ?
resultadoInspecao=Resultado da Inspeção
dataRequerimento=Data do Requerimento
estabelecimentoXConfirmaEncaminhamentoDocumento=Confirma o Encaminhamento do Documento para o Estabelecimento: {0} ?
estabelecimentoXConfirmaRecebimentoDocumento=Confirma o Recebimento do Documento para o Estabelecimento: {0} ?
estabelecimentoXConfirmaEntregaDocumento=Confirma a Entrega do Documento para o Estabelecimento: {0} ?
informeDescricaoOcorrencia=Informe a descrição da ocorrência
lancarOcorrencia=Lançar Ocorrência
detalhesRequerimento=Detalhes do Requerimento
detalhesOcorrencia=Detalhes da Ocorrência
dadosSolicitante=Dados do Solicitante
nomeSolicitante=Nome do Solicitante
rgCpf=RG / CPF
resultado=Resultado
deferido=Deferido
indeferido=Indeferido
documentoImpressoRequerimentoAindaNaoFoiGerado=O documento a ser impresso para o requerimento ainda não foi gerado.
obrigatorioInformarUmDosFiltrosProfissionalAreaMicroAreaDoenca=Obrigatório informar um dos filtros: Profissional, Aréa/Micro Aréa ou Doença
semProfissional=Sem Profissional
acao=Ação
tabelaReferenciaImpostoRendaNaoInformada=Tabela de referência para Imposto de Renda não informada
tabelaReferenciaInssNaoInformada=Tabela de referência para INSS não informada
tabelaReferenciaIssNaoInformada=Tabela de referência para ISS não informada
baixaImpostos=Baixa dos Impostos
consultaClassificacaoGrupoEstabelecimento=Consulta da Classificação de Grupo de Estabelecimento
cadastroClassificacaoGrupoEstabelecimento=Cadastro da Classificação de Grupo de Estabelecimento
classificacaoGrupoEstabelecimento=Classificação de Grupo de Estabelecimento
informeEstabelecimento=Informe um Estabelecimento
descricaoEventoTemporada=Descrição Evento / Temporada
msgDataValidadeInicialNaoPodeSerMaiorDataValidadeFinal=Data de Validade Inicial não pode ser maior que a Data de Validade Final.
validadeInicial=Validade Inicial
validadeFinal=Validade Final
numMedicamentoDispensado=Nr. Medicamento Dispensado
consultaAtendimentosMedicos=Consulta de Atendimentos Médicos
cadastroDocumentoPersonalizado=Cadastro de Documento Personalizado
texto=Texto
msgFormatacaoTextDocumentoPersonalizado=Variáveis disponíveis \n -$responsavelTecnicoEstabelecimento \n - $razaoSocialEstabelecimento \n - $enderecoEstabelecimento \n - $dataRequerimento \n - $numeroProcesso \n - $dataImpressao
formatacaoTexto=Formatação do Texto
msgCliqueParaVerOpcoesFormatacaoTextoDisponiveis=Clique aqui para ver as opções de Formatação do Texto Disponíveis
atendimentoNaoConfigurado=Atendimento não configurado
favorConfigurarTipoAtendimento=Favor configurar este Tipo de Atendimento
evolucao=Evolução
atendimentoMedico=Atendimento Médico
chegada=Chegada
dataAtendimento=Data do Atendimento
tempo=Tempo
tiposDocumentoAtendimento=Tipos de Documento de Atendimento
modeloEvolucao=Modelo da Evolução
dadosConsulta=Dados da Consulta
evolucaoRegistradaSucesso=Evolução registrada com sucesso
finalizarAtendimento=Finalizar Atendimento
templateComprovante=Template do Comprovante
templateDocumentoVigilancia=Template do Comprovante
consultaSetorVigilancia=Consulta Setor Vigilância
cadastroSetorVigilancia=Cadastro Setor Vigilância
setorVigilancia=Setor Vigilância
setorResponsavel=Setor Responsável
desejaRealmenteSalvarAtividadeEstabelecimentoSemInformarNenhumSetorResponsavel=Deseja realmente salvar a Atividade do Estabelecimento sem informar nenhum Setor Responsável ?
usuariosSetor=Usuários por Setor
setorVigilanciaSanitaria=Setor da Vigilância Sanitária
informeSetorVigilancia=Informe Setor da Vigilância Sanitária
informeUsuario=Informe Usuário
usuarioJaInformado=Usuário já Informado
pais=País
impressao=Impressão
gerarArquivoRaas=Gerar Arquivo do RAAS
fichaAcolhimento=Ficha de Acolhimento
informePosologia=Informe a Posologia
informeMedicacao=Informe a Medicação
informeGrauParentesco=Informe o Grau de Parentesco
grauParentesco=Grau de Parantesco
informeQualDroga=Informe Qual Droga
informeAtividade=Informe a Atividade
nenhumTipoSintomaCadastrado=Nenhum Tipo de Sintoma Cadastrado
informeTipoSintoma=Informe um tipo de Sintoma
usoMedicacaoAtualPosologia=Uso de Medicação Atual / Posologia
relacaoFamiliarMoradia=Relação Familiar / Moradia
relacoesTrabalhoObs=Relações de Trabalho (Histórico de trabalho, Relação com o INSS, Seguridade Social)
tiposDrogasUsoAbuso=Tipos de Drogas de Uso / Abuso
historiaUsoAbuso=História de Uso / Abuso
historicoInternacoes=Histórico de Internações
estadoMental=Estado Mental
avaliacaoMotivacional=Avaliação Motivacional
avaliacaoTestagem=Avaliação de Testagem
planoTerapeutico=Plano Terapêutico
atividadesTerapeuticas=Atividades Terapêuticas
hipertensao=Hipertensão
alergiaMedicacao=Alergia a Medicação
depressao=Depressão
alergia=Alergia
dst=DST
maFormacaoCongenita=Má formação congênita
asma=Asma
hivAids=HIV / AIDS
outros=Outros
historicoSaude=Histórico de Saúde
tempoTratamento=Tempo em Tratamento
frenquentouOutrosServicosCaps=Frenquentou outros serviços (CAPS)
porQuantoTempo=Por Quanto Tempo
consultouPsiquiatraOutroServico=Consultou com psiquiatra em outro serviço
qualServico=Qual Serviço
consultouNeurologista=Consultou com Neurologista
examesRealizados=Exames Realizados
cirurgiasFeitas=Cirurgias Feitas
outrosDados=Outros Dados
ultimaVezUsou=Última vez que usou
regularidadeUso=Regularidade de Uso
quantidadeUso=Quantidade de uso
tempoUso=Tempo de Uso
comoPorqueComecou=Como e porque começou?
comOqueComecou=Com o que começou?
quandoUsa=Quando usa?
praQueUsa=Pra que usa?
usaAcompanhado=Usa acompanhado?
ondeUsa=Onde usa?
reacoesSubstancia=Reações da substância
quantas=Quantas?
onde=Onde?
quando=Quando?
intensivoIntegral=Intensivo Integral
semiIntensivo=Semi-Intensivo
intensivoMatutino=Intensivo Matutino
intensivoVespertino=Intensivo Vespertino
naoIntensivo=Não-Intensivo
patologias=Patologias
cidPrinc=CID Princ.
cidSec=CID Sec.
salvarCidPrincipalPrincipaisPatologias=Salvar CID Principal nas Principais Patologias
outrosCids=Outros CIDs
definicoesConsulta=Definições da Consulta
diasRetorno=Dias para o Retorno
obsRetorno=Obs. Retorno
conduta=Conduta
cipe=CIPE
atendimentoFinalizadoSucesso=Atendimento finalizado com sucesso
dadosUltimaAvaliacao=Dados da Última Avaliação
historicoClinico=Histórico Clínico
idadeAtendimento=Idade no Atendimento
dadosColetados=Dados Coletados
temperatura=Temperatura
pesoKg=Peso (Kg)
glicemia=Glicemia
perimetroCefalicoCm=Perímetro Cefálico (cm)
alturaCm=Altura (cm)
cinturaCm=Cintura (cm)
frequenciaCardiaca=Frequência Cardíaca
pa=PA
historicoAvaliacoes=Histórico de Avaliações
principaisPatologias=Principais Patologias
pas=PAS
pad=PAD
tipoGlicemia=Tipo de Glicemia
fr=FR
fc=FC
msgDesejaImprimirComprovanteRequerimento=Deseja imprimir um comprovante do Requerimento?
cadastroReceitaPaciente=Cadastro da Receita do Paciente
medicacao=Medicação
tipoDroga=Tipo de Droga
overdose=Overdose
tipoSintoma=Tipo de Sintoma
sintoma=Sintoma
fichaAcolhimentoRegistradaSucesso=Ficha Acolhimento Registrada com Sucesso
aguardandoCadastro=Aguardando cadastro
atendendo=Atendendo
avaliacao=Avaliação
alergico=Alérgico
avaliacaoRegistradaSucesso=Avaliação registrada com sucesso
foraNormalidade=Fora da Normalidade
emJejum=Em Jejum
posPrandial=Pós Prandial
prePrandial=Pré Prandial
naoEspecificado=Não especificado
campoTemperaturaValorInvalido=Campo 'Temperatura', valor inválido! O valor deve estar entre 25 e 45.
campoXValorInvalido=Campo '${label}', valor inválido!
campoXValorInvalidoPressao=Campo '${label}', valor inválido! O valor deve estar entre 40 e 999.
religiao=Religião
localTrabalho=Local de Trabalho
localViagem=Local da Viagem
telefoneTrabalho=Telefone do Trabalho
parentescoResponsavel=Parentesco do Responsável
emCasoUrgenciaChamar=Em caso de urgência chamar
profissionalResponsavel=Profissional Responsável
atendimentoEnfermagem=Atendimento de Enfermagem
anotacao=Anotação
consultaDocumentoPersonalizado=Consulta de Documento Personalizado
reacao=Reação
efeitoColateral=Efeito Colateral
estagioPaciente=Estágio do Paciente
preContemplacao=Pré-Contemplação
contemplacao=Contemplação
preparacao=Preparação
integracao=Integração
integracaoExames=Integração de Exames
cadastroConvenio=Cadastro de Convênio
consultaConvenio=Consulta de Convênio
hospital=Hospital
porQueUsa=Por que usa?
setorAtendimento=Setor Atendimento
consultaPrescricoesAtendimento=Consulta das Prescrições do Atendimento
prescricaoAtedimento=Prescrição Atendimento
dispensacaoPrescricao=Dispensação de Prescrição
dadosPrescricao=Dados da Prescrição
consultaQuartos=Consulta de Quartos
cadastroQuarto=Cadastro de Quarto
cadastroLeitoQuarto=Cadastro de Leito no Quarto
informeLeito=Informe a descrição do Leito
leitoJaInformado=Leito já informado
tipoLeito=Tipo de Leito
dadosLeitoInternacao=Dados do Leito Internação
dadosLeito=Dados do Leito
leito=Leito
cadastroQuartosLeitos=Cadastro de Quartos / Leitos
setor=Setor
tipoQuarto=Tipo de Quarto
liberacaoLeitos=Liberação dos Leitos
confirmaLiberacaoLeitoXQuartoY=Confirma a liberação do Leito '{0}' do Quarto '{1}' ?
processos=Processos
liberar=Liberar
dispensacaoSalvaSucesso=Dispensação salva com sucesso
consultaAtendimentosEnfermagem=Consulta dos Atendimentos de Enfermagem
tipoAtendimentoInternacao=Tipo de Atendimento de Internação
tipoAtendimentoAlta=Tipo de Atendimento de Alta
quartoInternacao=Quarto de Internação
quartoJaInformado=Quarto já Informado
informeQuarto=Informe um quarto
quarto=Quarto
fichaAcolhimentoAindaNaoFoiGerada=Ficha de Acolhimento ainda não foi gerada.
identificador=Identificador
nenhumAtendimentoReituarioEncontradoParaPaciente=Nenhum atendimento ou receituário encontrado para o paciente
produtoJaAdicionadoAdicionarMesmoAssim=Produto já adicionado. Adicionar mesmo assim?
consultaDevolucoesMedicamento=Consulta de Devoluções de Medicamento
devolucaoMedicamento=Devolução de Medicamentos
dadosDevolucao=Dados da Devolução
itemParaDevolver=Item para Devolver
produtoSemSaldoDevolucao=Produto sem saldo para devolução
saldoXInsuficienteDevolucao=Saldo ({0}) insuficiente para devolução
saldoXLoteYInsuficienteDevolucao=Saldo ({0}) do lote '{1}' insuficiente para devolução
informeMotivoCancelamento=Informe o motivo do cancelamento
informeSintoma=Informe o Sintoma
tipoDrogaJaInformado=Tipo de Droga já informado
diagnostico=Diagnóstico
origemPaciente=Origem Paciente (RAAS) 
flagIdentificavel=Identificável
exemplos=Exemplos
horaInvalida=Hora inválida
horarioPlanoTerapeuticoInvalido=Horário do Plano Terapêutico inválido
consultaTipoSintoma=Consulta Tipo de Sintoma
cadastroTipoSintoma=Cadastro Tipo de Sintoma
dicaResposta=Dica de Resposta
armazenaSugestao=Armazena Sugestão
nrMaximoCid=Nr. Máximo de CID
imprimeTermoAutorizacao=Imprime Termo de Autorização
transferivel=Transferível
impressaoObstetrica=Impressão Obstétrica
relatorioBMPO=Anexo XXI - BMPO
anual=Anual
trimestral=Trimestral
balancoCompleto=Balanço Completo
balancoAquisicao=Balanço de Aquisições
periodicidade=Periodicidade
nrCrf=Nr. CRF
ufCrf=UF. CRF
exercicio=Exercício
tipoBalanco=Tipo Balanço
relatorioRMNRA=Anexo XXIV - RMNRA
mesAnoExercicio=Mês/Ano Exercício
relacaoMensalReceitasControladas=Relação Mensal de Receitas Controladas
tituloImpressaoProntuario=Título da Impressão do Prontuário
cadastroGrupoCboAtendimento=Cadastro de Grupo CBO Atendimento
consultaGrupoCboAtendimento=Consulta de Grupo CBO Atendimento
CboGrupoAtendimento=CBO Grupo Atendimento
informeCbo=Informe o CBO
componenteXNaoEncontrado=Componente (id: {0}) não encontrado
componenteXNaoImplementaInterfaceTempStoreComponent=O componente ({0}) não implementa a interface ITempStoreComponent
consultaGruposAtendimentoCbo=Consulta de Grupos de Atendimento por Cbo
cadastroGruposAtendimentoCbo=Cadastro de Grupo de Atendimento por Cbo
grupoAtendimentoCbo=Grupo de Atendimento por Cbo
dadosGrupo=Dados do Grupo
cboJaAdicionado=CBO já adicionado
informeGrupo=Informe o Grupo
consultaEncaminhamentos=Consulta dos Encaminhamentos
cadastroEncaminhamento=Cadastro de Encaminhamento
selecioneEncaminhamento=Selecione um encaminhamento
selecioneTipoAtendimento=Selecione um tipo de atendimento
convenios=Convênios
convenioPrincipal=Convênio Principal
convenioSalvoComSucesso=Convênio Salvo com Sucesso.
cnpjInvalido=CNPJ Inválido
convenioComEmpresa=Convênio com Empresa
detalhesConvenio=Detalhes Convênio
atualizaTabelaPreco=Atualiza Tabela de Preço
controlaEstoque=Controla Estoque
temperaturaRetal=Temperatura Retal
frequenciaRespiratoria=Frequência Respiratória
frequenciaCardiacaFetal=Frequência Cardíaca Fetal
diurese=Diurese
saturacaoOxigenio=Saturação do Oxigênio (%)
evacuacao=Evacuação
receituario=Receituário
novaPrescricao=Nova Prescrição
selecioneTipoReceita=Selecione um Tipo de Receita
nomeMedicamento=Nome do Medicamento
anotacoes=Anotações
finalizarPrescricao=Finalizar Prescrição
receitaBasica=Receita Básica
receitaAzul=Receita Azul
receitaAmarela=Receita Amarela
receitaBranca=Receita Branca
receitaMagistral=Receita Magistral
desejaImprimirReceita=Deseja imprimir a receita?
desejaImprimirCuidados/Procedimentos=Deseja Imprimir cuidados e procedimentos?
desejaImprimirExame=Deseja imprimir o exame?
exigeEvolucao=Exige Evolução
subconvenio=Empresa
imprimeFichaCadastral=Imprime Ficha Cadastral
disponivelDevolucao=Disponível para Devolução
quantidadeDevolver=Quantidade a Devolver
no=Nó
configurarTipoAtendimento=Configurar Tipo de Atendimento
tipoDeAtendimento=Tipo de Atendimento
informeNo=Informe o nó
itemJaAdicionado=Item Já Adicionado
selecioneNo=Selecione o nó
paraOrdenarCliqueArraste=* Para ordenar os nós clique sobre a linha e arraste
consultaTabelaPreco=Consulta Tabela de Preço
cadastroTabelaPreco=Cadastro Tabela de Preço
tabelaPreco=Tabela de Preço
preco=Preço
paraDefinirEncaminhamentoPadraoSelecione=* Para definir o encaminhamento padrão, selecione o mesmo na tabela
informePrecoUnitario=Informe o Preço Unitário
alta=Alta
dataAlta=Data da Alta
extratoPaciente=Extrato do Paciente
msgCasoNaoPossuirQuartosAdicionadosSeraoConsideradosQuartosConvenioPrincipal=Caso não possuir quartos adicionados, Serão considerados os quartos do convênio Principal
importarQuartosConvenioPrincipal=Importar Quartos do Convenio Principal
atender=Atender
transferenciaSetor=Transferência de Setor
confirmarAtendimento=Confirmar Atendimento
confirmarAtendimentoPacienteProfissionalAbaixo=Confirmar o Atendimento para o Paciente e Profissional Abaixo?
altaInternacao=Alta/Internação
consultaTabelaPrecoProcedimento=Consulta da Tabela de Preço de Procedimentos
cadastroTabelaPrecoProcedimento=Cadastro de Tabela de Preço de Procedimentos
tabelaPrecoProcedimento=Tabela de Preço dos Procedimentos
reservaMaiorSaldo=Reserva Maior que Saldo
autorizar=Autorizar
autorizarAgendar=Autorizar/Agendar
confirmacaoGuiasAberto=Confirmação de Guias em Aberto
processarConfirmacaoGuias=Processar Confirmação de Guias
confirmarUtilizacao=Confirmar Utilização
guiasConfirmadasSucesso=Guias Confirmadas com Sucesso!
responsavelTransporte=Responsável pelo Transporte
confirmarCancelamentoSeparacaoPedido=Confirmar o cancelamento da separação para o pedido?
cancelarSeparacao=Cancelar Separação
relacaoPedidosTransferenciaFornecedor=Relação dos Pedidos de Transferência por Fornecedor
resumoPedidosTransferencia=Resumo dos Pedidos de Transferência
resumoExamesExecutados=Resumo de Exames Executados
dataAvaliacao=Data da Avaliação
jaExisteTabelaPrecoProdutoXConvenioXMesAnoXPrecoCarregado=Já existe uma Tabela de Preço para o Produto: {0}, do Convênio: {1} para o Mês/Ano: {2}. O preço será carregado.
informeUmMotivoCancelamentoDescricao=Informe um Motivo de Cancelamento ou uma Descrição
jaExisteTabelaPrecoProcedimento=Já existe uma Tabela de Preço do Procedimento: {0}, do Convênio: {1}. O preço será carregado.
tabelaPrecoProcedimentoSalvoComSucesso=Tabela de Preços de Procedimentos Salvo com Sucesso.
listarProdutos=Listar Produtos
encaminhado=Encaminhado
separado=Separado
fechado=Fechado
confirmarFechamentoPedidoCancelandoSaldoRestante=Confirmar o fechamento do pedido cancelando o saldo restante?
listaProdutos=Lista Produtos
mesVigencia=Mês de Vigência
consultaHistoricoPaciente=Consulta de Histórico do Paciente
dataChegada=Data de Chegada
dataSaida=Data de Saída
motivoAlta=Motivo de Alta
profissionalAlta=Profissional da Alta
descricaoEvolucao=Descrição da Evolução
descricaoDocumento=Descrição do Documento
documentoEmEdicao=Documento em edição
logadoComo=Logado Como:
consultaRegistroManutencao=Consulta dos Registros de Manutenção
descricaoAlergia=Descrição da Alergia
usuarioNaoIdentificaveis=Usuários não Identificáveis
usuarioJaAdicionado=Usuário já adicionado
relatorioGiroEstoque=Relatório de Giro de Estoque
nenhumAtendimentoAbertoEncontradoPaciente=Nenhum atendimento aberto encontrado para o paciente
novaDispensacao=Nova Dispensação
modelo=Modelo
limpeza=Limpeza
oculpado=Oculpado
consultaAtendimento=Consulta de Atendimento
jaExisteDescricaoConvenio=Já existe um convênio cadastrado com o Nome: {0}.
estoqueMinimoAbaixoEstoqueAtual=Exibir somente com estoque mínimo abaixo do estoque atual
controladas=Controladas
relatorioPerfilAtendimentoHospitalar=Relatório Perfil de Atendimento Hospitalar
relatorioRelacaoAtendimentos=Relação de Atendimentos
consultaAtendimentos=Consulta de Atendimentos
dataDeAlta=Data de Alta
modeloParecer=Modelo do Parecer
parecer=Parecer
descricaoParecer=Descrição do Parecer
atoOperatorio=Ato Operatório
instrumentador=Instrumentador
auxiliar=Auxiliar
anestesista=Anestesista
cirurgiao=Cirurgião
diagnosticoPreOperatorio=Diagnóstico Pré-Operatório
diagnosticoPosOperatorio=Diagnóstico Pós-Operatório
relatorioImediatoPatologista=Relatório Imediato do Patologista
exameRagiologicoAto=Exame Radiológico no Ato
acidenteDuranteOperacao=Acidente Durante a Operação
atoCirurgico=Ato Cirúrgico
acessoNegado=Acesso Negado
computadorNaoAutorizadoAcesso=Este computador não está autorizado a acessar a página requisitada
laudoTfd=Laudo do TFD
solicitacaoExames=Solicitação de Exames
selecioneConvenio=Selecione o Convênio
particular=Particular
solicitacaoApac=Solicitação APAC
exameApac=Exame APAC
solicitacaoBpaI=Solicitação BPA-I
solicitacoesSalvas=Solicitações Salvas
msg_cancelamento_realizado_atraves_tela_atendimento=Cancelamento realizado através da Tela de Atendimento
laudoBpaI=Laudo BPA-I
desejaCancelarLaudoAih=Deseja Cancelar o Laudo de AIH?
prescricaoInterna=Prescrição Interna
requisicaoExames=Requisição de Exames
tipoExame=Tipo de Exame
examesSolicitados=Exames Solicitados
atendimentoConcluidoImpressaoProntuario=Atendimento concluído, faça a impressão do prontuário
msgImprimirProntuario=Para finalizar o atendimento é necessário imprimir o prontuário!
desejaCancelarLoteAih=Deseja Cancelar o Lote de AIH?
laudoAih=Laudo AIH
encaminhamentoEspecialista=Encaminhamento Especialista
laudoApac=Laudo APAC
voltarAtendimento=Voltar Atendimento
edicaoMedicamentoControladoPaciente=Cadastro Medicamento Controlado do Paciente
tratamentoContinuo=Tratamento Contínuo
dadosExame=Dados do Exame
transferenciaLeito=Transferência de Leito
novoLeito=Novo Leito
maisDetalhes=Mais Detalhes
resumoMovimentacoes=Resumo das Movimentações
dataResultado=Data do Resultado
cancelarExame=Cancelar Exame
avaliacaoObstetrica=Avaliação Obstétrica
acessoCompartilhado=Acesso Compartilhado
cadastroRecemNascido=Cadastro do Recém Nascido
classificacaoRisco=Classificação de Risco
imc=IMC
desconhecido=Desconhecido
evolucaoRecemNascido=Evolução do Recém Nascido
requisicaoExameCitopatologicoColoUtero=Requisição de Exame Citopatológico - Colo do Útero
requisicaoExameHistopatologicoColoUtero=Requisição de Exame Histopatológico - Colo do Útero
saudeMulher=Saúde da Mulher
tipoRequisicao=Tipo de Requisição
existeRequisicaoXpendenteFavorCancelarCadastrar=Já existe uma requisição de '{0}' pendente, favor cancelar para poder cadastrar. 
informeResultado=Informe o Resultado.
cancelarRequisicao=Cancelar Requisição
requisicaoMamografia=Requisição de Mamografia
detalhesExame=Detalhes do Exame
medicamentoProcedimento=Medicamento x Procedimento
medicamentoProcedimento.produto=Medicamento
solicitacaoProcedimentos=Solicitação de Procedimentos
novaSolicitacao=Nova Solicitação
atendimentoAtual=Somente Atendimento Atual
informeProcedimentos=Informe um procedimento
avaliacaoNutricionista=Avaliação Nutricionista
controleInfeccaoHospitalar=Controle de Infecção Hospitalar
conclusaoObstetrica=Conclusão Obstétrica
solicitacaoEletrocardiograma=Solicitação do Eletrocardiograma
limparSelecao=Limpar Seleção
biometria=Biometria
sinaisVitais=Sinais Vitais
cancelarEncaminhamento=Cancelar Encaminhamento
confirmarEncaminhamento=Confirmar Encaminhamento
impressaoProntuario=Impressão do Prontuário
internacao=Internação
procedimentosResumido=Procedimentos Resumido
procedimentosDetalhado=Procedimentos Detalhado
exameSalvoComSucesso=Exame salvo com sucesso. Deseja imprimir?
selecionePeloMenosUmExamePadrao=Selecione pelo menos um exame padrão
comorbidades=Comorbidades
comorbidade=Comorbidade
selecioneComorbidade=Selecione uma Comorbidade
solicitacaoExameHepatiteVirais=Solicitação do Exame de Hepatites Virais
preNatal=Pré-Natal
avaliacaoGestante=Avaliação Gestante
bcf=BCF
prescricaoInternaEnfermagem=Prescrição Interna
aplicarMedicamento=Aplicar Medicamento
dataHoraAplicacao=Data/Hora Aplicação
dadosAntropometricos=Dados Antropométricos
dadosSisvan=Dados SISVAN
tipoAlimentacao=Tipo de Alimentação
pesoNascer=Peso ao Nascer (gr)
intercorrencia=Intercorrência
dadosGestante=Dados Gestante
tratamentoOdontologico=Tratamento Odontológico
msgEncontradoFichaClinicaAbertaPacienteConcluirCancelarFichasContinuarProcesso=Foi encontrado mais de uma Ficha Clínica em aberto para o Paciente onde deveria existir apenas uma, favor concluir ou cancelar as Fichas extras para continuar o processo.
tratamento=Tratamento
dente=Dente
face=Face
registrarTrabalho=Registrar Trabalho
cancelarPlanoTratamento=Cancelar Plano de Tratamento
historicoOdontograma=Histórico Odontograma
detalhesTratamento=Detalhes do Tratamento
msgNaoFoiEncontradoFichaClinicaPaciente=Não foi encontrado a Ficha Clínica do Paciente, favor cadastrar para poder continuar
msgExisteTratamentoAbertoDenteFaceSituacao=Já existe um tratamento em aberto para este Dente, Face e Situação
desejaExcluirRegistroSelecionado=Deseja Excluir o Registro Selecionado?
historicoTrabalhosExecutados=Histórico dos Trabalhos Executados
msgProcedimentoXJaAdicionado=Procedimento {0} já adicionado.
odontograma=Odontograma
procedimentoIndividual=Procedimento Individual
emAndamento=Em Andamento
descricaoTrabalhoRealizado=Descrição do Trabalho Realizado
adicionePeloMenosUmProcedimento=Adicione pelo menos um procedimento!
informeSituacaoTratamento=Informe a situação do tratamento
trabalhoRealizado=Trabalho Realizado
informeDescricaoTrabalhoRealizado=Informe a Descrição do Trabalho Realizado
transferenciaPaciente=Transferência de Paciente
imprimirComprovante=Imprimir Comprovante
excluirTransferencia=Excluir Transferência
visualizarMotivo=Visualizar Motivo
motivoTransferencia=Motivo da Transferência
fichaClinica=Ficha Clínica
fichaClinicaOdontologica=Ficha Clínica Odontológica
curvaCrescimento=Curva de Crescimento
cadastroFichaOdontologica=Cadastro da Ficha Odontológica
situacaoDentaria=Situação Dentária
tipoSituacao=Tipo Situação
graficoCurvaCrescimento=Gráfico da Curva de Crescimento
pesoPorIdade=Peso por idade
idadeEmMeses=Idade (meses)
pesoPorComprimento=Peso por comprimento
pesoPorEstatura=Peso por estatura
comprimentoEstaturaPorIdade=Comprimento/Estatura por idade
imcPorIdade=IMC por idade
idadeEmAnos=Idade (anos)
idadeEmAnoMeses=Idade (anos/meses)
estaturaPorIdade=Estatura por idade
indiceMassaCorporal=Índice de massa corporal
comprimentoCm=Comprimento (cm)
comprimentoAlturaCm=Comprimento/altura (cm)
selecioneTipoExame=Selecione o Tipo de Exame
selecioneTipoAmostra=Selecione o Tipo de Amostra
lote_data_validade_nao_pode_ser_nulo=Lote e data de validade não podem ser nulos
necessario_um_diagnostico=Necessário um diagnostico de infecção pelo hiv
necessario_salvar_para_excluir=Necessário salvar antes de excluir
selecioneTipoDocumento=Selecione o tipo de documento
vacinaEmDia=Vacina em Dia
concluirTratamento=Concluir Tratamento
dataConclusao=Data da Conclusão
concluir=Concluir
consultaFichaOdontologica=Consulta da Ficha Odontológica
informarResultado=Informar Resultado
autorizacaoExames=Autorização de Exames
alterarItemPregao=Alterar o Item do Pregão
situacaoAtendimento=Situação do Atendimento
solicitacoesLacen=Solicitações LACEN
tipoIMC=Tipo IMC
gestante=Gestante
solicitacaoProcedimentosAih=Solicitação de Procedimentos (AIH)
msgSoPermitidoImprimirExameAutorizadoTipoExame=Só é permitido imprimir exame autorizado para este Tipo de Exame
historicoAplicacao=Histórico da Aplicação
horaAplicacao=Hora Aplicação
solicitacaoMudancaProcedimentoAih=Solicitação de Mudança de Procedimento (AIH)
procedimentosAtendimento=Procedimentos do Atendimentos
msgAtendimentoNaoPossuiAih=Não é possível cadastrar uma solicitação pois o atendimento não possui uma AIH
desejaImprimirSolicitacao=Deseja imprimir a Solicitação?
procedimentoAnterior=Procedimento Anterior
novoProcedimento=Novo Procedimento
cidPrincipal=CID Principal
visualizarImpressao=Visualizar impressão
testeRapido=Teste Rápido
frequenciaCardiacaAbrev=F.C
saturacaoOxigenioAbrev=% Sat.Ox.
ultimaEvolucao=Última Evolução
desativado=Desativado
isolado=Isolado
aguardandoLimpeza=Aguardando Limpeza
auxiliar_1=Auxiliar 1
auxiliar_2=Auxiliar 2
auxiliar_3=Auxiliar 3
encaminhamentoCEO=Encaminhamento CEO
peso=Peso
altura=Altura
msg_existe_prescricao_data_x=Já existe uma prescrição para a data {0}
chamarPainel=Chamar no Painel
padronizado=Padronizado
prescricoesPaciente=Prescrições do Paciente
informeMedicamento=Informe o Medicamento
frequenciaQuantidade=Frequência / Quantidade
impressaoGeradaComSucesso=Impressão Gerada com Sucesso
frequenciaQuantidade2=Frequência ou Quantidade
gerarImpressao=Gerar Impressão
dieta=Dieta
cuidadosProcedimentos=Cuidados / Procedimentos / Orientações / Observações
complementoPosologia=Complemento de Posologia
consultaComplementoPosologia=Consulta de Complemento de Posologia
cadastroComplementoPosologia=Cadastro de Complemento de Posologia
medicamentoNaoPadronizado=Medicamento Não Padronizado
justificativaUsoMedicamentoNaoPadronizado=Justificativa para uso do medicamento não padronizado
desejaVisualizarImpressaoPrescricao=Deseja Visualizar a Impressão da Prescrição?
procedimentosCuidados=Procedimentos / Cuidados
procedimentosEspeciais=Procedimentos Especiais
12/12=12/12
8/8=8/8
6/6=6/6
4/4=4/4
descricaoPosologia=Descrição da Posologia
prescricao=Prescrição
aprazamento=Aprazamento
prescricaoEnfermagem=Prescrição Enfermagem
cuidados=Cuidados
msgExistePrescricaoDataAtualSomenteSeraPossivelRealizarNovaPrescricaoApartirDestaData=Já existe uma prescrição para a data atual. Somente será possível realizar uma nova prescrição a partir desta data.
orientacoesAlta=Orientações de Alta
imprimeNotaAlta=Imprime Nota de Alta
msgParaFinalizarAtendimentoNecessarioImprimirNotaAlta=Para finalizar o atendimento é necessário imprimir a nota de alta!
resumoEmprestimos=Resumo dos Empréstimos
condicaoSituacoesSaude=Condição/Situações de Saúde
selecioneCondicaoSituacoesSaude=Selecione uma Condição/Situações de Saúde
alergias=Alergias
desejaRealmenteVoltarAtendimento=Deseja Realmente Voltar o Atendimento?
ultimaReceita=Última Receita
dadosClinicos=Dados Clínicos
sairAtendimento=Sair do Atendimento
naoSeraoSalvasCxAlta=SUAS ALTERAÇÕES NÃO SERÃO SALVAS. 
temCertezaDesfazer=Tem certeza que deseja sair do atendimento?
salvarAtendimento=Salvar Atendimento
historicoResumido=Histórico Resumido
msgNaoConfiguradoTipoEncaminhamentoAlta=Não foi Configurado um Tipo de Encaminhamento para a Alta
altaOuEncaminhamento=Alta ou Encaminhamento
desejaImprimirNotaAlta=Deseja imprimir a Nota de Alta?
notaAlta=Nota de Alta
msgNaoFoiEncontradoUsuarioProfissionalInformado=Não foi encontrado um usuário do sistema para o profissional informado
msgSenhaInvalida=Senha Inválida
emergencia=Emergência
modeloConsulta=Modelo da Consulta
solicitarMateriais=Solicitar Materiais
receituarioSN=Receituário SN
montarPrescricao=Montar Prescrição
modeloPadrao=Modelo Padrão
compartilhado=Compartilhado
selecionePadrao=Selecione um Padrão
receituarioPadrao=Receituário Padrão
msgSelecioneMedicamentoParaMontarPrescricao=Selecione ao menos um Medicamento para Montar a Prescrição.
numeroAtendimento=Número do Atendimento
motivoConsulta=Motivo da Consulta
formularioTabagismo=Formulário Tabagismo
mlh=Ml/h
bi=B.I.
somaBi=Soma B.I.
solucoes=Soluções
utilizaBi=Utiliza B.I.
hidratacao=Hidratação
sedacao=Sedação
informeSolucao=Informe a Solução
informeIntervalo=Informe o intervalo.
outra=Outra
SN=S/N
msgAdicionePeloMenosUmMedicamentoComponentesSolucao=Adicione pelo menos um medicamento aos componentes da solução.
componentesSolucao=Componentes da Solução
formularioAbordagemTratametoTabagismo=Formulário de Abordagem e Tratameto do Tabagismo
trabalhoParto=Trabalho de Parto
atendimentoPrincipal=Atendimento Principal
salvarComoPadrao=Salvar como Padrão
24/24=24/24
imprimirDocumentosAIH=Imprimir Documentos AIH
hrInicio=Hr. Início
anexo=Anexo
turno=Turno
msgResgitroExistenteMes=Já existe registro para o mês solicitado.
laboratorio=Laboratório
cnsInvalido=CNS Inválido
informePrestadorProfissional=Informe o prestador ou o profissional
procedimentoInvalidoCompetenciaAtual=Procedimento inválido para a competência atual
informePrestador=Informe o prestador
naoExisteProcedimentoServicoCadastrado=No cadastro de conjunto da unidade, não existe um serviço especializado configurado para atender a classificação de serviço que o procedimento faz parte. Verifique o cadastro do procedimento para identificar de quais serviços ele faz parte.
prestadorNaoHabilitadoServico=O prestador selecionado não está habilitado para executar o procedimento ou não foi definido o responsável
procedimentoInvalidoSexoPaciente=Procedimento inválido para o sexo do paciente
procedimentoInvalidoCboTerceiro=O procedimento selecionado não pode ser executado pelo CBO configurado para o responsável pelo prestador
cnsPrestadorNaoDefinido=O CNS do responsável pelo prestador de serviço, configurado nos serviços especializados da unidade, não foi definido.
cboResponsavelTerceiroObrigatorio=O CBO do responsável pelo prestador de serviço, configurado nos serviços especializados da unidade, não foi definido.
procedimentoNaoFaturadoBpa=O procedimento selecionado não pode ser faturado por meio do BPA
empresaPrincipal=Empresa Principal
idadePacienteProcedimento=A idade do paciente não permite a aplicação do procedimento
cnsProfissionalNaoDefinido=O CNS do profissional não foi definido
novoReceituario=Novo Receituário
idadeMinimaMeses=Idade Mínima (meses)
idadeMaximaMeses=Idade Máxima (meses)
idadeMeses=Idade (meses)
kit=Kit
informeKit=Informe o kit.
kitJaAdicionado=Kit já adicionado.
kitNaoCadastrado=Kit não cadastrado.
msgInformeDescricaoReceituario=Informe a Descrição do Receituário
hrSelecionadoEntre0e23=O horário selecionado deve estar entre 0 e 23
naoUtilizar=Não Utilizar
reutilizar=Reutilizar
desejaRealmenteNaoUtilizar=Deseja realmente não utilizar o item?
desejaRealmenteReutilizar=Deseja realmente reutilizar o item?
dataFechamento=Data de Fechamento
usuarioFechamento=Usuário Fechamento
contasFechadasPorMim=Contas Fechadas Por Mim
competencia_abv=Comp
saida=Saída
fechamento=Fechamento
atendimento_abv=Nr Atend
msgIntervalo24H=O valor do campo Intervalo deve ser de 1 a 24.
importacaoDoSigtap=Importação do SIGTAP
pacienteSemCnsDefinido=CNS do paciente não definido
naoExisteEstoqueCadastradoParaEsteProduto=Não existe estoque cadastrado para este produto
cadastroMotivoVisita=Cadastro Motivo da Visita
consultaMotivoVisita=Consulta Motivo da Visita
motivoVisita=Motivo da Visita
registroVisitas=Registro das Visitas
consultaRegistroVisitas=Consulta Registro das Visitas
descricaoVisita=Descrição da Visita
dataVisitaNaoSuperior=Data da Visita não pode ser superior a data atual
situacaoConta=Situação da Conta
desejaRealmenteAgendarHorariosSelecionados=Deseja realmente agendar os horários selecionados?
verDescricaoVisita=Visualizar descrição da visita
relacaoVisitas=Relação das Visitas
cadastroRegistroVisita=Cadastro do Registro da Visita
responsavelEstabelecimento=Responsável do Estabelecimento
motivo=Motivo
dataVisita=Data da Visitação
resumoVisita=Resumo das Visitas
relatorioResumoMotivo=Relatório de Resumo das Visitas
relatorioRelacaoMotivo=Relatório de Relação das Visitas
numeracaoinicial=Numeração inicial
nrVisita=Numero de Visitas
nrRegistro=Nr. Registro
selecioneProfissional=Por favor, selecione um profissional
informePeloMenosUmProfissional=Por favor, informe pelo menos um profissional
informarCartao=Informar Cartão
localEmbarque=Local de Embarque
consultaRegistroVisita=Consulta do Registro da Visita
motivoOcorrenciaContaPaciente=Motivo de Ocorrência da Conta do Paciente
novaOcorrencia=Nova Ocorrência
msgSelecioneMotivo=Selecione um Motivo!
msgInformeDescricao=Informe a Descrição!
controleSinaisVitais=Controle de Sinais Vitais
proprio=Próprio
informeNomeResponsavel=Informe o nome do responsável
vizualizarAgendamentos=Visualizar Agendamentos
nenhumHorarioAgendado=Nenhum horário agendado
relacaoListaEsperaContato=Relação da Lista de Espera para Contato
relacaoListaEspera=Relação da Lista de Espera
erroObterHorario=Erro ao obter o horário da agenda
mamografia=Mamografia
erroConexaoWebService=Erro ao estabelecer uma conexão com o webservice
cepNaoEncontrado=CEP não encontrado
tipoExameJaAdicionado=Tipo de exame já adicionado.
selecionePeloMenosUmTipoExame=Selecione pelo menos um tipo de exame.
resumoPorCidOuCiap=Resumo por CID ou CIAP
tipoCid=Tipo do CID
cidNotificavel=CID Notificável
tipoTotal=Tipo do Total
distribuicaoCotasAgenda=Distribuição das Cotas da Agenda
consultaDistribuicaoCotasAgenda=Consulta Distribuição das Cotas da Agenda
cadastroDistribuicaoCotasAgenda=Cadastro Distribuição das Cotas da Agenda
msgJaExisteCotaParaEsseTipoProcedimentoEstabelecimento=Já existe uma cota cadastrada para este tipo de procedimento e estabelecimento.
visualizarCota=Visualizar Cota
cancelarLicitacaoItem=Cancelar item da Licitação?
suporte=Suporte
ligue0800=Ligue 0800-642-1056
msgItemCanceladoLicitacao=Produto informado está cancelado na licitação.
msgProdutoSemPreco=Produto selecionado está sem preço na licitação.
msgProdutoSemLicitacao=O Produto selecionado não foi encontrado na licitação.
msgProdutoSemSaldoLicitacao=O Produto selecionado não possui mais saldo na licitação
msgMesmoCBOMaisDeUmaVezTipoAtendimento=Não é permitido adicionar o mesmo CBO mais de uma vez para o tipo de atendimento.
msgLicitacaoInvalida=Não foi possível adicionar o produto. Verifique se ele está licitado ou dentro da data de validade da licitação.
msgQuantidadeSolicitada=A quantidade informada é maior do que a quantidade licitada.
msgQuantidadeAgendamentosRealizadosUltrapassouLimiteCotaFavorRealizeAgendamentosNovamente=A quantidade de agendamentos realizados ultrapassou o limite da cota. Favor realize os agendamentos novamente.
oxigenoterapia=Oxigenoterapia
manutencaoAlta=Manutenção da Alta
manutencaoDadosAlta=Manutenção dos Dados da Alta
ocorrenciasAlta=Ocorrências da Alta
manutencaoCompetenciaContaPaciente=Manutenção da Competência da Conta do Paciente
msgInformeCompetencia=Informe a Competência
alterarCompetencia=Alterar Competência
msgSelecioneConta=Selecione ao menos uma Conta!
msgDataAltaMaiorDataAtual=Data da Alta não pode ser maior que a Data Atual!
solicitacaoExame=Solicitação de Exame
msgValidarTipoExame=Favor informar o Tipo de Exame.
msgTipoExameNaoConfigurado=O tipo de exame selecionado não está configurado, por favor, configure antes de prosseguir
exameNaoEncontrado=Nenhum exame em aberto encontrado para o tipo selecionado
requisicaoExameNaoEncontrada=Não foi encontrado nenhuma requisição de exame pendente para o código informado
informeCodigoExame=Por favor, informe o código do exame
insiraPeloMenosUmExame=Por favor, insira pelo menos um Exame
carregarExames=Carregar Exames
consultaModeloLaudoExame=Consulta Modelo de Laudo de Exame
cadastroModeloLaudoExame=Cadastro Modelo de Laudo de Exame
modeloLaudo=Modelo de Laudo
emissao_laudo=Emissão Laudo
digitacaoLaudo=Digitação do Laudo
confirmacaoExame=Confirmação de Exame
exameIndividual=Exame Individual
digitarLaudo=Digitar Laudo
anexarLaudo=Anexar Laudo
limparAnexo=Limpar Anexo
flagVisualizarProcedimentosFichaAtendimento=Visualizar Procedimentos Ficha Atendimento
dataExame=Data do Exame
laudar=Laudar
msgNaturezaProcuraNaoConfigurada=Natureza Procura não configurada
contaPaciente=Conta do Paciente
msgDesejaTransferirItemContaSelecionada=Deseja transferir o item para a conta selecionada?
pacienteNaoPossuiContaSusAberto=O Paciente selecionado não possui conta SUS em aberto.
laudoConfirmado=Laudo salvo com sucesso
msg_laudo_exame=Imprimir laudo do exame?
limparLaudo=Limpar laudo
msg_data_laudo_superior=Data do Laudo não pode ser superior a data atual.
msg_exame_sem_laudo=Existe(m) exame(s) sem laudo. Favor, conferir.
efetivarLaudo=Efetivar Laudo
msgErroProcesso=Ocorreu um erro ao realizar a ação, por favor repita o processo
anexar=Anexar
removerAnexo=Remover Anexo
nenhumAnexoAdicionado=Nenhum anexo adicionado
somentePossivelAnexarPdf=Somente é possível anexar arquivos com a extensão .pdf
laudado=Laudado
registroConselhoNaoInformado=Registro do Conselho não Informado
novaConta=Nova Conta
cadastroContaPaciente=Cadastro da Conta do Paciente
dataAbertura=Data de Abertura
consultaAtendimentoExame=Consulta Atendimento Exame
detalhesAtendimentoExame=Detalhes do Atendimento Exame
tecnico=Técnico
situacao=Situação
digitadorLaudo=Digitador Laudo
msgProfissionalSemDocumentoDefinido=O Profissional não tem Documento definido.
msgLaudoAIHCanceladoSucesso=Laudo de AIH Cancelada com Sucesso.
msgLaudoAIHReenviadoSucesso=Laudo de AIH Reenviada com Sucesso.
ganhosPerdas=Ganhos e Perdas
ganhoPerda=Ganho/Perda
volume=Volume
volumeMl=Volume (ml)
msgDesejaRealmenteInativar=Deseja Realmente Inativar?
lanctosInativados=Lançamentos Inativados
lanctosInativadosAbv=Lanc. Inativados
analiseGanhosPerdas=Análise Ganhos/Perdas
analise=Análise
ganho=Ganho
perda=Perda
balanco=Balanço
totalGanhosMl=Total de Ganhos (ml)
totalPerdasMl=Total de Perdas (ml)
balancoTotalMl=Balanço Total (ml)
detalhesLanctos=Detalhes dos Lançamentos
tipoGanhoPerda=Tipo de Ganho/Perda
consultaTipoGanhoPerda=Consulta do Tipo de Ganho/Perda
cadastroTipoGanhoPerda=Cadastro do Tipo de Ganho/Perda
balancoHidrico=Balanço Hídrico
horariosTurno=Horários dos Turnos
manha=Manhã
tarde=Tarde
noite=Noite
msgBalancoDiaXContemDadosParciais=Atenção! O balanço do dia {0} contém dados parciais.
atendimentoExame=Atendimento exame
selecioneEstabelecimento=Por favor, selecione um estabelecimento
fracionado=Fracionado
contemDiluente=Contém Diluente
responsavelLaudo=Responsável do laudo
dataEntregaNaoPodeSerMenorAtual=A data da entrega não pode ser menor que a data atual
profissionalExecutante=Profissional Executante
selecioneHorario=Por favor, selecione um horário
informeProfissionalResponsavel=Por favor, informe o Profissional Responsável
msgNaoPossivelFocoPosExcessao=Não foi possível setar o foco pós exceção
mesclarContasPaciente=Mesclar Contas do Paciente
selecioneContaClicandoLinha=* Selecione uma conta clicando sobre a linha
selecioneContaPrincipal=Selecione a Conta Principal
msgSelecioneContaParaContinuar=Selecione uma Conta para Continuar
contasPaciente=Contas do Paciente
dadosContaPrincipal=Dados da Conta Principal
mesclarContas=Mesclar Contas
separarContasMescladasPaciente=Separar Contas Mescladas do Paciente
separarContas=Separar Contas
separarConta=Separar Conta
familiaBeneficiariaBolsaFamilia=Família é beneficiária do Bolsa Família
familiaEstaInscritaCADUnico=Família está inscrita no CAD-Único
responsavelNIS=Responsável do NIS
nis=NIS
avaliacaoAlterada=Avaliação Alterada
programaNacionalControleTabagismo=Programa nacional de controle do tabagismo
cessouHabitoFumar=Cessou hábito de fumar
abandonouGrupo=Abandonou o grupo
avAlterada=Av. Alterada
msgInformeProfissionalOuResponsavel=Por favor, informe o campo profissional ou responsável
msgEstabelecimentoNaoHabilitadoProcedimento=Estabelecimento não habilitado para realizar este procedimento
inserirEmHoras=Inserir em Horas
msgInformeCidPrinc=Por favor, informe o CID Princ.
msgDesejaImprimirEtiqueta=Deseja imprimir a etiqueta?
consultaConfiguracaoProcessoCid=Consulta da configuração do processo do CID
cadastroConfiguracaoProcessoCid=Cadastro da configuração do processo do CID
configuracaoProcessoCID=Configuração do Processo do CID
dataInicio=Data de início
msgDataInicioNaoPodeMaiorDataAtual=A data de início não pode ser maior que a data atual
contasPacienteMescladas=Contas do Paciente Mescladas
unidadePrescricao=Unidade da prescrição
recemNascido=Recém Nascido
agora=Agora
solucao=Solução
tipoViagem=Tipo de Viagem
msgProdutoCadastrado=Produto já Cadastrado.
msgRegistroExistenteMes=Já existe registro para o mês solicitado
reprocessar=Reprocessar
registroMesAnterior=Registro do Mês Anterior foi Reprocessado.
msgRelatorioResumoSaldoEstoqueEmProcesso=Já existe um relatório em andamento. Tente novamente em alguns instantes.
horaPrescricao=Hora Prescrição
dadosAcompanhante=Dados Acompanhante
msgInformeNome=Informe o nome.
msgSelecioneSexo=Selecione o sexo.
msgInformeDataNascimento=Informe a data de nascimento.
msgInformeTelefone=Informe o telefone.
msgInformeGrauParentesco=Informe o grau de parentesco.
msgDigiteUmDosDocumentosObrigatoriosCpfIdentidade=Digite um dos documentos obrigatórios (CPF ou Identidade).
msgSeguintesCamposIdentidadeSaoObrigatoriosNumeroUfDataEmissaoOrgaoEmissor=Os seguintes campos da Identidade são obrigatórios:\nNúmero\nUF\nData Emissão\nOrgão Emissor
cadastrarAcompanhante=Cadastrar Acompanhante
msgHorariosDefinidosSucesso=Os horários foram definidos com sucesso!
permitirDispensacaoMais=Permitir Dispensação a Mais
historicoSolicitacaoMateriais=Historico de Solicitação de Materiais
mostrarMedicamentos=Mostrar medicamentos
mostrarMateriais=Mostrar materiais
adep=ADEP
adepDescricao=ADEP - Administração Eletrônica da Prescrição
warnMedNaoPadronizadoPrazo48Hrs=Atenção: Medicamento não padronizado. Prazo de até 48h para farmácia liberar.
especialidadeLeito=Especialidade do Leito
configuracoesBalancoHidrico=Configurações do Balanço Hídrico
msgSelecioneGrupoAtendimento=Selecione um Grupo de Atendimento.
msgGrupoAtendimentoJaAdicionado=Grupo de Atendimento já Adicionado!
gruposConfigurados=Grupos Configurados
configurarBalancoHidrico=Configurar Balanço Hídrico
configuracoesSalvasComSucesso=Configurações Salvas com Sucesso!
cpfInvalido=CPF inválido.
consultaDispensacaoPrescricao=Consulta Dispensação da Prescrição
nPrescricao=Nr. Prescrição
receberEmailMsg=Receber e-mails das mensagens internas
solicitar=Solicitar
itensDisponiveis=Itens Disponíveis
ACM=A.C.M.
msgNaoPossivelSNeACM=Não é possível ser S/N e A.C.M. ao mesmo tempo, por favor, remova um
justificativaAdep=Justificativa ADEP
layoutMenu=Layout Menu
justificativaNaoPadronizado=Justificativa Não Padronizado
vincularProduto=Vincular produto
itemJaAdicionadoRemovaParaTrocarVinculo=Não é possível trocar o vínculo de um item que já foi adicionado, por favor, remova o item.
msgAtendimentoNaoFinalizado=Existe um receituario com este código, entretanto, o atendimento do mesmo não está finalizado. Por favor, solicitar que este atendimento seja salvo antes de prosseguir
vincular=Vincular
minuto_invalido=Minuto inválido!
tipoIntervalo=Tipo Intervalo
novoAtendimento=Novo Atendimento
cadastroAtendimento=Cadastro de Atendimento
msgFormularioGeradoSucesso=Formulário gerado com sucesso. Deseja imprimir?
enviarRelatorioErro=Enviar Relatório de Erros
msgDataNaoPodeSerInferior3MesesDataAtual=A data não pode ser inferior a 3 meses da data atual.
agendamentoSemCadastro=Agendamento sem Cadastro
msgInformeNomeEOuUnidadeOrigem=Informe o nome e/ou a unidade de origem.
msgSelecioneSubconvenio=Selecione o subconvênio.
msgTipoAtendimentoSelecionadoNaoEstaDisponivelParaEsseProcesso=O tipo de atendimento selecionado não está disponível para esse processo.
msgNumeroVagasDisponiveisDeveSerMaiorIgualNumeroVagasSolicitadas=O número de vagas disponíveis deve ser maior ou igual o número de vagas solicitadas.
numeroVagasSolicitadas=Número de Vagas Solicitadas
msgConfirmaAgendamento=Confirma o agendamento?
msgNumeroVagasDisponiveisAposDataSelecionadaDeveSerMaiorIgualNumeroVagasSolicitadas=O número de vagas disponíveis após a data selecionada deve ser maior ou igual o número de vagas solicitadas.
prescricaoOculos=Prescrição de Óculos
esferica=Esférica
cilindro=Cilindro
eixo=Eixo
longe=Longe
perto=Perto
olhoDireito=Olho Direito
olhoEsquerdo=Olho Esquerdo
distanciaPupilar=Distância Pupilar
msgInformeCidadeOuEmpresa=É Obrigatorio informar a Cidade ou a Empresa Principal!
tipoFinanciamento=Tipo Financiamento
msgInformePaciente=Informe o paciente.
profissionalBPA=Profissional B.P.A
tipoAnexo=Tipo de Anexo
consultaTipoAnexo=Consulta do Tipo de Anexo
cadastroTipoAnexo=Cadastro do Tipo de Anexo
consultaAnexos=Consulta de Anexos
cadastroAnexos=Cadastro de Anexos
dataDocumento=Data do Documento
palavraChave=Palavra-Chave
msgSomentePermitidoEditarExcluirAnexoIncluidoMesmoDia=Só é permitido ser Editado/Excluído os anexos incluídos no dia.
tiposPermitidosAnexoPaciente=Tipos permitidos: PDF, JPG, JPEG, JPE, PNG
msgExistemAnexosTiposNaoPermitidos=Existe(m) anexo(s) de tipo(s) não permitido(s), favor removê-lo(s). Tipos permitidos: PDF, JPG, PNG.
msgExistemAnexosTiposNaoPermitidosPacientes=Existe(m) anexo(s) de tipo(s) não permitido(s), favor removê-lo(s). Tipos permitidos: PDF, JPG, JPEG, JPE, PNG.
msgNecessarioAnexarPeloMenosArquivoPoderSalvar=É necessário anexar pelo menos um arquivo para poder salvar!
apef=APEF
apefDescricao=Aprazamento de Enfermagem
relacaoParentescoComResponsavelFamiliar=Relação de parentesco com o responsável familiar
anexos=Anexos
arquivosAnexados=Arquivos Anexados
motivoInativacaoExclusao=Motivo da Inativação / Exclusão
qualFamiliar=Qual Familiar
qualInstituicao=Qual Instituição
msgDesejaImprimirEndereco=Deseja Imprimir o Endereço?
msgProcedimentoNaoConfigurado=Não foi encontrado um procedimento para faturamento. Favor configurar no cadastro de procedimento.
msgDataDocumentoNaoPodeSerMaiorDataAtual=A Data do Documento não pode ser maior que a Data Atual.
msgConflitoDeHorario=O paciente já está agendado para esse horário.
suspenderItem=Suspender Item
suspender=Suspender
msgSelecionePeloMenosUmMedicamentoParaSuspender=Selecione pelo menos um medicamento para suspender.
justificarSuspensao=Justificar Suspensão
medicamentosSuspensosComSucesso=Medicamentos suspensos com sucesso.
justificativaSuspensao=Justificativa da Suspensão
suspenderMedicamento=Suspender Medicamento
medicamentoSuspenso=Medicamento Suspenso
msgTodaQuantidadePrescritaJaFoiDispensadaPelaFarmacia=Toda quantidade prescrita já foi dispensada pela farmácia.
ajuda=?
atalhos=Teclas de Atalho
atalhoSalvar=ALT + "S"
atalhoNovo=ALT + "N"
atalhoProcurar=ALT + "P"
atalhoAvancar=ALT + "Seta para Direita"
atalhoVoltar=ALT + "Seta para Esquerda"
consultaTipoTabelaProcedimento=Consulta Tipo de Tabela do Procedimento
cadastroTipoTabelaProcedimento=Cadastro Tipo de Tabela do Procedimento
tipoTabelaProcedimento=Tipo de Tabela do Procedimento
domicilio=Domicílio
mArea=M Área
selecioneEndereco=Selecione o Endereço
dadosVisita=Dados da Visita
profissionalNaoVinculadoEquipe=O Profissional não está vinculado a uma equipe
profissionalVinculadoMaisUmaEquipe=O Profissional está vinculado a mais de uma equipe
relatorioEnviado=Relatório Enviado
guiaSolicitacao=Guia de Solicitação
dadosGuiaSolicitacao=Dados da Guia de Solicitação
indicacaoClinica=Indicação Clínica
dadosAutorizacao=Dados da Autorização
numeroGuia=Número Guia
numeroGuiaPrincipal=Número Guia Principal
senhaAutorizacao=Senha de Autorização
validadeSenha=Validade da Senha
totalMatMed=Total Mat/Med
valorMatMed=Valor Mat/Med
reverterConfirmacao=Reverter Confirmação
msgProdutoInformaDevePossuirVinculoTabelaBrasindice=O produto informado deve possuir um vínculo com a tabela brasíndice.
informeHonorario=Informe o Honorário.
informeDespesa=Informe a Despesa.
despesas=Despesas
msgInformeProfissionalSolicitante=Informe o profissional solicitante.
msgProfissionalSolicitanteDevePossuirNumeroRegistro=O profissional solicitante deve possuir número do registro.
exportacaoTiss=Exportação Tiss
registroAnsOrigem=Registro ANS Origem
registroAnsDestino=Registro ANS Destino
ufConselhoRegistro=UF do Conselho de Registro
caraterAtendimentoTiss=Caráter do Atendimento (TISS)
tipoAtendimentoTiss=Tipo do Atendimento (TISS)
tipoTabelaTiss=Tipo de Tabela (TISS)
tipoDespesaTiss=Tipo de Despesa (TISS)
codigoUnidadeTiss=Código Unidade (TISS)
tiss=TISS
codigoContratadoExecutante=Cód. do Contratado Executante
codigoContratadoExecutanteOrigem=Cód. do Contratado Exec. Origem
codigoContratadoExecutanteDestino=Cód. do Contratado Exec. Destino
msgInformeCodigoContratadoExecutante=Informe o Cód. do Contratado Executante!
msgInformeCodigoContratadoExecutanteOrigem=Informe o Cód. do Contratado Exec. Origem!
msgInformeCodigoContratadoExecutanteDestino=Informe o Cód. do Contratado Exec. Destino!
msgJaExisteContratadoEsteCovenio=Já existe um Contratado para este Convênio!
msgJaExisteExecutanteEsteCovenio=Já existe um Executante para este Convênio!
cadastroOrgaoEmissor=Cadastro de Orgão Emissor
consultaOrgaoEmissor=Consulta de Orgão Emissor
saude=Saúde
codigoConselhoTiss=Cód. Conselho (TISS)
modeloArquivo=Modelo de Arquivo
verifiqueModeloArquivoSelecionado=Por favor, verifique se o modelo de arquivo selecionado está correto
codigoMedicamento=Código Medicamento
codigoLaboratorio=Código Laboratório
codigoApresentacao=Código Apresentação
produtoBrasindice=Produto Brasíndice
brasindice=Brasíndice
tuss=TUSS
versao=Versão
dataVigencia=Data Vigência
viculosProdutos=Vínculos com Produtos
vinculoProduto=Vínculo Produto
fatorConversao=Fator de Conversão
vinculos=Vínculos
medLabApr=Med/Lab/Apres.
dataEmissaoGuia=Data Emissão da Guia
dataExecucao=Data de Execução
msgInformeDataGuiaEmissao=Informe a Data Emissão da Guia
msgInformeDataExecucao=Informe a Data de Execução
produtosBrasindice=Produtos Brasíndice
regraPrecoProdutoConvenio=Regra de preço de produto por convênio
matMed=Mat/Med
tabelaOrigem=Tabela de Origem
tipoCalculoPercentual=Tipo de Cálculo Percentual
tipoPrecoBrasindice=Tipo de Preço Brasíndice
processadoSucesso=Processado com sucesso
msgEmpresaPrincipalNaoConfiguradaParaEmpresaLogada=A empresa principal não está configurada para a empresa logada.
msgNumeroPrestadorNaoConfiguradoEmpresaPrincipalX=O número do prestador IPE não está configurado para a empresa principal: {0}.
competenciaNaoPodeSerMenorDataChegadaAtendimentoDataChegadaX=A competência não pode ser menor que a data de chegada do atendimento. Data de chegada: {0}.
msgSomentePermitidoAnexoTipoArquivoPdf=Somente é permitido anexo com o tipo de arquivo em PDF.
msgInformeQuantidadeDias=Informe a quantidade de dias.
msgValorUltrapassouLimiteFavorVerificar=O valor ultrapassou o limite. Favor verificar.
tipoTabela=Tipo Tabela
msgProcedimentoTabelaRepetido=Já existe um procedimento para este tipo de tabela.
msgMedicamentoObrigatorio=É necessario selecionar ao menos um medicamento.
msgProcedimentoObrigatorio=É necessario selecionar ao menos um procedimento.
removerSelecionados=Remover Selecionados
msgExcluirRegistrosSelecionados=Deseja excluir o(s) resgistro(s) selecionado(s)?
outrasDespesas=Outras Despesas
msgInformeDataLancamento=Informe a Data de Lançamento
materialMedicamentoAbv=Mat/Med
msgCodigoTUSSBrasindiceNaoInformado=O Código TUSS da Tabela Brasíndice do Produto selecionado não foi informado!
msgNaoEncontradoProcedimentoCodigoTUSSX=Não foi encontrado um Procedimento para o Código TUSS {0} para o Produto informado!
selecionarTodos=Selecionar Tudo
tipoDespesa=Tipo de Despesa
informarPrestador=Informar Prestador
msgCboInvalidoProfissionalExame=Os seguintes procedimentos 
cboProfissionalIncompativelCboProcedimentoExames=O CBO do profissional é incompatível com o CBO dos seguintes procedimentos:
msgResponsavelEmpresaPrestador=O Prestador é incompatível com os seguintes procedimentos:
msgDesejaProcessar=Deseja processar mesmo assim?
msgInformeDataAutorizacao=Informe a Data da Autorização!
msgInformeNumeroAutorizacao=Informe o Número da Autorização!
msgInformeAoMenosProfissionalCnsCpf=Informe ao menos um dos três campos: Profissional, Código do CNS ou CPF!
procedimentoConsolidado=Procedimento Consolidado
nivelEnsino=Nível de Ensino
msgJaExisteGrupoAtendimentoConfiguradoCboX=Já existe um grupo de atendimento configurado para o CBO: {0}.
tipoLayoutExportacaoTiss=Tipo de Layout Exportação Tiss
msgExameObrigatorio=É necessário selecionar ao menos um Exame.
laudo=Laudo
imprimirLaudo=Imprimir Laudo
desejaCancelarLaudoAihPacienteX=Deseja Cancelar o Laudo de AIH do Paciente: {0} ?
tipoTeste=Tipo de Teste
orgaoEmissorNaoInformado=Org. Emissor não informado
selecioneSala=Selecione a sala.
imprimirReceita=Imprimir Receita
imprimirPrescricao=Imprimir Prescrição
adiantamento=Adiantamento
msgPacienteNaoCompareceuUltimoAgendamentoEspecDataAgendamentoXLocalX=Paciente não compareceu no último agendamento realizado para esta especialidade.\nData do Agendamento: {0} - Local: {1}.
msgPacienteNaoCompareceuUltimoAgendamentoRealizadoParaEsteTipoDataAgendamentoXLocalX=Paciente não compareceu no último agendamento realizado para este tipo de procedimento.\nData do Agendamento: {0} - Local: {1}.
atendimentoRN=Atendimento RN
acidente=Acidente
laudosPendentes=Laudos Pendentes
cancelarLaudo=Cancelar Laudo
estrategia=Estratégia
baixaEstoque=Baixa em Estoque
insumosMedicamentosGastos=Insumos/Medicamentos Gastos
msgNaoPossivelAdicionarMedicamentoDiferenteAplicando=Não é possível adicionar este medicamento pois ele é diferente do que está sendo aplicado.
contraceptivo=Contraceptivo
msgObrigatorioTipoAtendimento=É obrigatorio informar o Tipo de Atendimento
marcadoresConsumoAlimentar=Marcadores de Consumo Alimentar
confirmarAnexo=Confirmar Anexo
antecedentesPessoais=Antecedentes Pessoais
outrosProblemas=Outros Problemas
codigoTce=Código do TCE
retirarFila=Retirar da Fila
imprimirDadosPainelChamada=Imprimir Dados Painel Chamada
rotulo_relatorio_atendimento_painel=Relatório de Chamada no Painel
historicoAtendimentoPaciente=Histórico de Atendimentos do Paciente
desativarReativar=Desativar/Reativar
msg_paciente_informado_pertence_outra_unidade=O paciente informado pertence a área {0}, deseja agendar?
pacienteData=Paciente/Data
relacaoEntradaPaciente=Relação de Entrada do Paciente
msgNaoHaRegistroaParaFiltrosAplicados=Não há registros para os filtros aplicados.
gerar_relatorio_xls=Gerar Relatório XLS
periodoBloqueio=Período Bloqueio
cadastroCotaUtilizacaoGuia=Cadastro da Cota
consultaCotaUtilizacaoGuia=Consulta Cota de Utilização das Guias
saldoCota=Saldo da Cota Disponível
cotaMensal=Cota Mensal
pedido_de_vacina_almoxarifado=Pedido de Vacina ao Almoxarifado
informe_vacina=Informe a Vacina
informe_laboratorio=Informe o Laboratório
informe_almoxarifado=Informe o Almoxarifado
necessarioSelecionarUmLote=Selecione Um Lote
fundoConsorcio=Fundo Consorcio
cadastroFundoMunicipal=Cadastro Fundo Municipal
consultaFundoMunicipal=Consulta Fundo Municipal
impressaoNoProntuario=Impresso no Prontuário
rotulo_risco_alto=ALTO RISCO
rotulo_risco_medio=MÉDIO RISCO
rotulo_risco_baixo=BAIXO RISCO
rotulo_quantidade_total_estratificacao=Total de pessoas com estratificação de risco:
rotulo_quantidade=Quantidade
rotulo_percentual=Percentual
rotulo_classificacao_risco=Classificação de Risco
dataTriagem=Data da Triagem
relatorioErrosImportacaoExames=Relatório Erros Importação Exames
relacaoExamesExternosTestesRapidosCovid19=Relação de Exames Externos e Testes Rápidos COVID-19
unidadeReferencia=Unidade Referência
resultadoDoExameComCid=Resultado do exame com C.I.D.
relacaoHipertensosSemAderencia=Relação de Hipertensos - Indicador 06
relacaoDiabeticosSemAderencia=Relação de Diabéticos - Indicador 07
relacaoCriancasSemAderencia=Relação de Crianças - Indicador 05
relacaoMulheresSemAderencia=Relação de Mulheres - Indicador 04
procedimentosRealizadosPrestadorServico=Procedimentos realizados - Prestador de Serviço
auto_referido=Auto Referido
msg_atendimento_salvo_anteriormente=Este atendimento havia sido salvo anteriormente.
caraterInternacao=Caráter de Internação
listarAtendimentosFinalizados=Listar atendimentos finalizados
Semestre=Semestre
Falha_ao_receber_resposta_de_mensagem=Falha ao receber resposta de mensagem
procSigtap=Proc. SIGTAP
procedimentoSIGTAP=Procedimento SIGTAP
prestador=Prestador
estabelecimentoAutorizado=Estabelecimento Autorizado
salvarAutorizarClassificacao=Salvar e autorizar AIH na classificação de risco
msg_texto_carimbo_aprovacao_projeto_x=Este documento refere-se exclusivamente ao Projeto {0} registrado junto à {1}. Os proprietários devem providenciar os demais vistos. licenças e/ou autorizações da Administração Pública Municipal.
exportarAgenda=Exportar Agenda
diluente=Diluente
volumeDiluicao=Volume Diluiçao
tempoInfusao=Tempo Infusão
restituicao=Restituição
velocidadeInfusaoViaEndovenosa=Vel. Infusão Via Endovenosa
msg_id_ja_cadastrado=ID já utilizado em outro produto. {0}
msgDiaAltaMenorQueDiaInicial=O dia de previsão da alta está anterior a data inicial
msgReservaLeito=Já existe reserva de leito para o período informado \n Data início: {0} | Data alta: {1} \n AIH: {2} \n Nome Paciente: {3}
relatorioReservaLeitoAih=Relatório Reserva de Leito por AIH
msgFiltroReservaData =Os campos de filtro por data devem ser ambos preenchidos

codigoUnidade=Código de Unidade
descricaoUnidade=Nome da Unidade
periodoInicial=Período Inicial
periodoFinal=Período Final
quantidadeProcedimentos=Quantidade de Procedimentos
codigoInterno=Codigo Interno
codigoUnificado=Codigo Unificado
descricaoProcedimento=Descrição Procedimento
cpfProfissionalExecutante=CPF Profissional Executante
nomeProfissionalExecutante=Nome Profissional Executante
hrAgendamento=Hora Agendamento
dtNascimento=Data Nascimento
numeroLogradouro=Numero Logradouro
ibge=Ibge
munSolicitante=Município Solicitante
ibgeSolicitante=IBGE Solicitante
cnesSolicitante=CNES Solicitante
unidadeFantasia=Unidade Fantasia
operadorSolicitante=Operador Solicitante
operadorAutorizador=Operador Autorizador
valorProcedimento=Valor Procedimento
cpfProfissionalSolicitante=CPF Profissional Solicitante
nomeProfissionalSolicitante=Nome Profissional Solicitante
gerar_relatorio_csv=Gerar Relatório CSV
codigoRequisicaoExame=Codigo Requisicao Exame
exportacaoAgendaLaboratorio=Exportação Agenda Laboratório
rotulo_empresa2=Empresa
flagListaMedicamentoPublico=Lista Medicamento Público
detalhes_estabelecimento=Detalhes do Estabelecimento
descricaoMedicamento=Código - {0} - {1}
exportacaoAgendaConsulta=Exportação Agenda de Consulta
msgReservaLeitoRealizada=Reserva realizada com sucesso
restricaoSexoIdade = Restringir Sexo/Idade
notaFiscal=Nota Fiscal
rotulo_observacao=Observação
informeRaca=O paciente informado precisa ter a raça informada
produtoSemMovimentacao=Produtos sem movimentação
data_inicial_data_final_preenchidos=Os campos de Data Inicial e Data Final devem ser ambos preenchidos
idSolicitacaoBranet=ID Solicitação Branet
desejaTrocarLeito=O leito {0} está ocupado pelo paciente {1}. Deseja selecionar outro leito?
rotulo_acionista= Acionista
rotulo_acionista_controlador= Acionista Controlador
rotulo_acionista_diretor= Acionista Diretor
rotulo_acionista_presidente= Acionista Presidente
rotulo_administrador_c7= AdministradorC7
rotulo_administradora_de_consorcio_de_empresas_ou_grupo_de_empresas= Administradora de Consórcio de Empresas ou Grupo de Empresas
rotulo_conselheiro_de_administracao= Conselheiro de Administração
rotulo_curador_representante= CURADOR/REPRESENTANTE
rotulo_diretor= Diretor
rotulo_interventor= Interventor
rotulo_inventariante= Inventariante
rotulo_liquidante= Liquidante
rotulo_mae_representante= MAE/REPRESENTANTE
rotulo_pai_representante= PAI/REPRESENTANTE
rotulo_presidente= Presidente
rotulo_procurador= Procurador
rotulo_secretario= Secretário
rotulo_sindico_condominio_ou_falencia= Síndico (Condomínio ou Falência)
rotulo_sociedade_consorciada= Sociedade Consorciada
rotulo_sociedade_filiada= Sociedade Filiada
rotulo_socio= Sócio
rotulo_socio_capitalista= Sócio Capitalista
rotulo_socio_comanditado= Sócio Comanditado
rotulo_socio_comanditario= Sócio Comanditário
rotulo_socio_de_industria= Sócio de Indústria
rotulo_socio_residente_ou_domiciliado_no_exterior= Sócio Residente ou Domiciliado no Exterior
rotulo_socio_gerente= Sócio-Gerente
rotulo_socio_ou_acionista_incapaz_ou_relativamente_incapaz_exceto_menor= Sócio ou Acionista Incapaz ou Relativamente Incapaz (exceto menor)
rotulo_socio_ou_acionista_menor_assistido_representado= Sócio ou Acionista Menor (Assistido/Representado)
rotulo_socio_ostensivo= Sócio Ostensivo
rotulo_tabeliao= Tabelião
rotulo_tesoureiro= Tesoureiro
rotulo_titular_de_empresa_individual_imobiliaria= Titular de Empresa Individual Imobiliária
rotulo_tutor= Tutor
rotulo_gerente_delegado= Gerente-Delegado
rotulo_socio_pessoa_juridica_domiciliado_no_exterior= Sócio Pessoa Jurídica Domiciliado no Exterior
rotulo_socio_pessoa_fisica_residente_ou_domiciliado_no_exterior= Sócio Pessoa Física Residente ou Domiciliado no Exterior
rotulo_diplomata= Diplomata
rotulo_consul= Cônsul
rotulo_representante_de_organizacao_internacional= Representante de Organização Internacional
rotulo_oficial_de_registro= Oficial de Registro
rotulo_responsavel= Responsável
rotulo_ministro_de_estado_das_relacoes_exteriores= Ministro de Estado das Relações Exteriores
rotulo_socio_pessoa_fisica_residente_no_brasil= Sócio Pessoa Física Residente no Brasil
rotulo_socio_pessoa_juridica_domiciliado_no_brasil= Sócio Pessoa Jurídica Domiciliado no Brasil
rotulo_socio_administrador= Sócio-Administrador
rotulo_empresario= Empresário
rotulo_candidato_a_cargo_politico_eletivo= Candidato a Cargo Político Eletivo
rotulo_socio_com_capital= Sócio com Capital
rotulo_socio_sem_capital= Sócio sem Capital
rotulo_fundador= Fundador
rotulo_socio_comanditado_residente_no_exterior= Sócio Comanditado Residente no Exterior
rotulo_socio_comanditario_pessoa_fisica_residente_no_exterior= Sócio Comanditário Pessoa Física Residente no Exterior
rotulo_socio_comanditario_pessoa_juridica_domiciliado_no_exterior= Sócio Comanditário Pessoa Jurídica Domiciliado no Exterior
rotulo_socio_comanditario_incapaz= Sócio Comanditário Incapaz
rotulo_produtor_rural= Produtor Rural
rotulo_consul_honorario= Cônsul Honorário
rotulo_responsavel_indigena= Responsável Indígena
rotulo_representante_da_instituicao_extraterritorial= Representante da Instituição Extraterritorial
rotulo_cotas_em_tesouraria= Cotas em Tesouraria
rotulo_administrador_judicial= Administrador Judicial
rotulo_titular_pessoa_fisica= TITULAR PESSOA FÍSICA
rotulo_titular_pessoa_fisica= TITULAR PESSOA FÍSICA
rotulo_titular_pessoa_juridica_domiciliada_no_brasil= TITULAR PESSOA JURÍDICA DOMICILIADA NO BRASIL
rotulo_titular_pessoa_juridica_domiciliada_no_exterior= TITULAR PESSOA JURÍDICA DOMICILIADA NO EXTERIOR
rotulo_sociedade_de_comando= SOCIEDADE DE COMANDO
rotulo_empresa_lider= EMPRESA LIDER
rotulo_empresa_consorciada= EMPRESA CONSORCIADA
rotulo_socio_representante_de_condominio= SÓCIO/REPRESENTANTE DE CONDOMÍNIO
rotulo_socio_participante= SÓCIO PARTICIPANTE
rotulo_socio_espolio= SOCIO/ESPOLIO
rotulo_conselheiro_de_administracao= CONSELHEIRO DE ADMINISTRACAO
rotulo_administrador_do_grupo= ADMINISTRADOR DO GRUPO
rotulo_administrador_de_sociedade_filiada= ADMINISTRADOR DE SOCIEDADE FILIADA
rotulo_administrador_judicial_pf= ADMINISTRADOR JUDICIAL - PF
rotulo_administrador_judicial_pj= ADMINISTRADOR JUDICIAL - PJ
rotulo_admj_profissional_responsavel= ADMJ - PROFISSIONAL RESPONSÁVEL
rotulo_administrador_judicial_gestor= ADMINISTRADOR JUDICIAL/GESTOR
rotulo_administrador_de_fato= ADMINISTRADOR DE FATO
rotulo_conselheiro_de_administracao_de_fato= CONSELHEIRO DE ADMINISTRAÇÃO DE FATO
rotulo_gestor_judicial= GESTOR JUDICIAL
rotulo_administrador_provisorio= ADMINISTRADOR PROVISÓRIO
rotulo_representante= REPRESENTANTE
rotulo_pai_assistente= PAI/ASSISTENTE
rotulo_mae_assistente= MAE/ASSISTENTE
rotulo_curador_assistente= CURADOR/ASSISTENTE
rotulo_delegado= DELEGADO
rotulo_comissario= COMISSARIO
rotulo_representante_legal= REPRESENTANTE LEGAL
rotulo_espolio= ESPOLIO
rotulo_preposto= PREPOSTO
rotulo_filial_na_mesma_uf_da_sede= FILIAL NA MESMA UF DA SEDE
rotulo_filial_em_outra_uf= FILIAL EM OUTRA UF
rotulo_filial_com_sede_fora_da_uf= FILIAL COM SEDE FORA DA UF
rotulo_integrante_de_agrupamento= INTEGRANTE DE AGRUPAMENTO
qualificacao= Qualificação
areaTotalImovel= Área Total do Imóvel
areaConstruidaImovel= Área Construída do Imóvel
naturezaImovel= Natureza do Imóvel
unidade_responsavel=Unidade Responsável
regulacao=Regulação
aihSemLeitoQuarto=Não foi encontrado registro de quarto na AIH {0}
qtdeTotalProcedimentos=Qtde. Total Procedimentos
procedimentosRealizadosPrestadorConsolidadoComparativo=Relatório Procedimentos Realizados por Prestador Consolidado e Comparativo
estabelecimentoSaude=Estabelecimento de Saúde
rotulo_comparativo=Comparativo
rotulo_cirurgico_ortopedica:Cirúrgico Ortopédica
rotulo_uti_adulto_tipo1:UTI Adulto Tipo I
rotulo_uti_adulto_tipo2:UTI Adulto Tipo II
rotulo_uti_adulto_tipo3:UTI Adulto Tipo III
rotulo_uti_pediatrica_tipo1:UTI Pediátrica Tipo I
rotulo_uti_pediatrica_tipo2:UTI Pediátrica Tipo II
rotulo_uti_pediatrica_tipo3:UTI Pediátrica Tipo III
rotulo_uti_neonatal_tipo1:UTI Neonatal Tipo I
rotulo_uti_neonatal_tipo2:UTI Neonatal Tipo II
rotulo_uti_neonatal_tipo3:UTI Neonatal Tipo III

##################-Bolsa Familia AMB-3918-###############
pbf = PBF
programa_bolsa_familia = Programa Bolsa Família
#########################################################
consultaDeFatorRiscoSanitario=Consulta de Fator de Risco Sanitário
cadastroDeFatorRiscoSanitario=Cadastro de Fator de Risco Sanitário
questionarioFatorRisco=Questionario Fator Risco
aumentarRiscoCondicao = Aumentar Condicao de Risco
estrategiaEsusPniRnds=Estrategia Esus Pni RNDS
rotulo_portaria_344=PORTARIA 344/1998
rotulo_grupo_alta_vig=Grupo Alta Vigilância
testeRapidoConjunto=Teste Rápido Conjunto
consultaTesteRapidoConjunto=Consulta Teste Rápido Conjunto
testeRapidoTipo=Tipo de Teste
rotulo_gem_paciente=Paciente
rotulo_gem_senha=Senha
cepObrigatorio=O campo CEP é obrigatório. Por favor, preencha o CEP.
enderecoNaoPossuiCep=O endereço selecionado não possui CEP.
habilitarCadastroConjuntoDiagnostico=Habilitar Cadastro de Conjunto Diagnóstico
rotulo_formato_data_invalido=A data precisa estar no formato (dd/MM/yyyy HH:mm) e formato de hora até 23:59
rotulo_maior_data_atual=A data não pode ser maior que a data atual
rotulo_solicitacao_inexistente=A solicitacao agendamento deve estar cadastrada
rotulo_tipo_ocorrencia_invalido=O código do tipo ocorrência está inválido, favor informar (3) para confirmação de contato ou (4) para lançar histórico
rotulo_id_solicitacao_obrigatorio=Parametro idSolicitacao não pode estar vazio!
rotulo_descricao_obrigatorio=Parametro descricao deve estar preenchido!
rotulo_data_hora_obrigatorio=Parametro dataHora não pode estar vazio!
rotulo_nome_usuario_obrigatorio=Parametro nomeUsuario deve estar preenchido!
rotulo_tipo_ocorrencia_obrigatorio=Parametro tipoOcorrencia não pode estar null!
rotulo_ag_solicitacao_cancelada=A solicitacao de agendamento não deve estar cancelada
rotulo_risco_baixo_sepse=Risco Baixo
rotulo_risco_medio_sepse=Risco Médio
rotulo_risco_alto_sepse=Risco Alto
disponivelRecepcao=Disponível na Recepção
salvar_finalizar=Salvar/Finalizar
rotulo_rascunho=Rascunho do 
msgAdicionarNoFichaAcolhimento=Para cadastrar uma ficha de acolhimento é necessário adicionar o nó Ficha de acolhimento ao tipo de atendimento: {0}.
autorizarReservarLeito=Autorizar/Reservar Leito
rotulo_codigo_referencia_vacina = Código
decrementarSaldo=Decrementar Saldo
flagDesobrigacaoAlvara=Desobrigação de Alvará Sanitário
obmAmpp=OBM
registroSanitarioAmpp=Código do registro sanitário
usoExclusivoHpt=Uso exclusivo hospitalar
obms=Obms Medicamentos
flagHabilitarObm=Habilitar OBM
msgProcedimentoRepetidoMesmoTipoTabela=Já existe um procedimento para o tipo de tabela selecionado.
score=Score
consultarScore=Consultar Score
msgProcessoDesabilitado  = Conforme atualização das diretrizes do Ministério da Saúde, o envio de dados de Entrada e Dispensação de medicamentos ao BNAFAR foi descontinuado.
msgDataAtualizacaoLista = Os dados exibidos na consulta de medicamentos representam os estoques disponíveis nas unidades de saúde em tempo real, refletindo com precisão a situação atual no exato momento da pesquisa.
resultadoHemoglobinaGl=Hemoglobina (g/L)
resultadoHematocrito=Hematócrito (%)