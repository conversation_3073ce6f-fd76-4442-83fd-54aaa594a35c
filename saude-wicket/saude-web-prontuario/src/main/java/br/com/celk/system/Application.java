package br.com.celk.system;

import br.com.celk.component.resource.FtpReportResource;
import br.com.celk.component.resource.StaticReportResource;
import br.com.celk.system.authorization.AuthorizationStrategy;
import br.com.celk.system.authorization.UnauthorizationStrategy;
import br.com.celk.system.converter.DateTimeConverter;
import br.com.celk.system.properties.SystemPropertiesEnum;
import br.com.celk.system.request.CelkRequestListener;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.session.CelkSessionStore;
import br.com.celk.view.atendimento.prontuario.DefaultConsultaAtendimentoPage;
import br.com.celk.view.controle.AcessoNegadoPage;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DoubleConverter;
import org.apache.commons.beanutils.converters.LongConverter;
import org.apache.wicket.ConverterLocator;
import org.apache.wicket.IConverterLocator;
import org.apache.wicket.Session;
import org.apache.wicket.markup.html.SecurePackageResourceGuard;
import org.apache.wicket.protocol.http.WebApplication;
import org.apache.wicket.request.Request;
import org.apache.wicket.request.Response;
import org.apache.wicket.request.resource.SharedResourceReference;
import org.apache.wicket.request.resource.caching.FilenameWithVersionResourceCachingStrategy;
import org.apache.wicket.request.resource.caching.version.CachingResourceVersion;
import org.apache.wicket.request.resource.caching.version.LastModifiedResourceVersion;
import org.apache.wicket.session.HttpSessionStore;
import org.apache.wicket.session.ISessionStore;
import org.apache.wicket.util.IProvider;
import org.apache.wicket.util.lang.Bytes;
import org.apache.wicket.util.time.Duration;
import org.wicketstuff.annotation.scan.AnnotatedMountScanner;

public class Application extends WebApplication implements Serializable{

    @Override
    public Class<DefaultConsultaAtendimentoPage> getHomePage() {
        return DefaultConsultaAtendimentoPage.class;
    }

    public static Application get() {
        return (Application) WebApplication.get();
    }

    @Override
    protected void init() {
        super.init();
        //Resources
        getResourceSettings().setResourcePollFrequency(Duration.ONE_SECOND);
        getResourceSettings().setCachingStrategy(new FilenameWithVersionResourceCachingStrategy(new CachingResourceVersion(new LastModifiedResourceVersion())));

        //Security
        getSecuritySettings().setAuthorizationStrategy(new AuthorizationStrategy());
        getSecuritySettings().setUnauthorizedComponentInstantiationListener(new UnauthorizationStrategy());
        
        getApplicationSettings().setPageExpiredErrorPage(DefaultConsultaAtendimentoPage.class);
        getApplicationSettings().setAccessDeniedPage(AcessoNegadoPage.class);
        
        getMarkupSettings().setDefaultMarkupEncoding("UTF-8");
        getMarkupSettings().setCompressWhitespace(true);
        
        getSharedResources().add("report", new FtpReportResource());
        mountResource("report", new SharedResourceReference("report"));
        getSharedResources().add("staticReport", new StaticReportResource());
        mountResource("staticReport", new SharedResourceReference("staticReport"));

        new AnnotatedMountScanner().scanPackage(SystemPropertiesEnum.PAGES_SCAN_PACKAGE.toString()).mount(this);
        
        //Session Store
        getRequestCycleListeners().add(new CelkRequestListener());
        setSessionStoreProvider(new IProvider<ISessionStore>() {

            @Override
            public ISessionStore get() {
                return new CelkSessionStore(new HttpSessionStore());
            }
        });
        getStoreSettings().setInmemoryCacheSize(50);
        getStoreSettings().setAsynchronousQueueCapacity(100);
        getStoreSettings().setMaxSizePerSession(Bytes.kilobytes(50));
        
        ConvertUtils.register(new LongConverter(null), Long.class);
        ConvertUtils.register(new DoubleConverter(null), Double.class);
        ConvertUtils.register(new org.apache.commons.beanutils.converters.DateConverter(null), Date.class);
        
        SecurePackageResourceGuard guard = new SecurePackageResourceGuard();
        guard.addPattern("+*.htm");
        getResourceSettings().setPackageResourceGuard(guard);
    }

    @Override
    public Session newSession(Request request, Response response) {
        return new ApplicationSession(request);
    }

    @Override
    protected IConverterLocator newConverterLocator() {
        ConverterLocator converterLocator = new ConverterLocator();

        converterLocator.set(Date.class, new DateTimeConverter());
        converterLocator.set(Timestamp.class, new DateTimeConverter());
        converterLocator.set(java.sql.Date.class, new DateTimeConverter());

        return converterLocator;
    }

}
