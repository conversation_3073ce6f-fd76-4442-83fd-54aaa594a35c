package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.vo.basico.Bairro;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class RequiredAutoCompleteBairro extends AutoCompleteBairro {

    public RequiredAutoCompleteBairro(String id) {
        super(id);
        init();
    }

    public RequiredAutoCompleteBairro(String id, IModel<Bairro> object) {
        super(id, object);
        init();
    }
    
    private void init(){
        setRequired(true);
        addRequiredClass();
    }
}
