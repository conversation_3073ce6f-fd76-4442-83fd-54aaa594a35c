package br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete;



import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.restricaocontainer.RestricaoContainerDominioTabelaCbo;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaDominioTabelaCboDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.prontuario.procedimento.DominioTabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTabelaCbo extends AutoCompleteConsulta<TabelaCbo> { 
    
    private Procedimento procedimento;
    private Profissional profissional;
    private Empresa empresa;
    private boolean filtrarAtivos;

    public AutoCompleteConsultaTabelaCbo(String id) {
        super(id);
    }

    public AutoCompleteConsultaTabelaCbo(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaTabelaCbo(String id, IModel<TabelaCbo> model) {
        super(id, model);
    }

    public AutoCompleteConsultaTabelaCbo(String id, IModel<TabelaCbo> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(DominioTabelaCbo.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(DominioTabelaCbo.PROP_TABELA_CBO,TabelaCbo.PROP_CBO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(DominioTabelaCbo.PROP_TABELA_CBO,TabelaCbo.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerDominioTabelaCbo(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<TabelaCbo, QueryConsultaDominioTabelaCboDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaDominioTabelaCboDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(AtendimentoFacade.class).queryConsultaTabelaCbo(dataPaging);
                    }

                    @Override
                    public QueryConsultaDominioTabelaCboDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaDominioTabelaCboDTOParam param = new QueryConsultaDominioTabelaCboDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaDominioTabelaCboDTOParam param) {
                        param.setProcedimento(procedimento);
                        param.setProfissional(profissional);
                        param.setEmpresa(empresa);
                        param.setFiltrarAtivos(isFiltrarAtivos());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(TabelaCbo.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return TabelaCbo.class;
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("cbos");
    }
    
    public AutoCompleteConsultaTabelaCbo setProcedimento(Procedimento procedimento) {
        this.procedimento = procedimento;
        return this;
    }
    
    public AutoCompleteConsultaTabelaCbo setProfissionalUnidade(Profissional profissional, Empresa empresa) {
        this.profissional = profissional;
        this.empresa = empresa;
        return this;
    }

    public AutoCompleteConsultaTabelaCbo setProcedimento(Procedimento procedimento, boolean filtrarAtivos) {
        this.procedimento = procedimento;
        this.filtrarAtivos = filtrarAtivos;
        return this;
    }

    public AutoCompleteConsultaTabelaCbo setProfissionalUnidade(Profissional profissional, Empresa empresa, boolean filtrarAtivos) {
        this.profissional = profissional;
        this.empresa = empresa;
        this.filtrarAtivos = filtrarAtivos;
        return this;
    }

    public boolean isFiltrarAtivos() {
        return filtrarAtivos;
    }

    public void setFiltrarAtivos(boolean filtrarAtivos) {
        this.filtrarAtivos = filtrarAtivos;
    }
}
