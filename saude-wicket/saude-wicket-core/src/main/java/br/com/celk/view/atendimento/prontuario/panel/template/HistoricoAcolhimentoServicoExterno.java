package br.com.celk.view.atendimento.prontuario.panel.template;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.AcolhimentoServicoExterno;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.util.VOUtils.montarPath;

public class HistoricoAcolhimentoServicoExterno implements Serializable {

    private Atendimento atendimento;

    public HistoricoAcolhimentoServicoExterno(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public List<EvolucaoProntuario> buildProntuarios() {
        List<EvolucaoProntuario> evolucoes = new ArrayList<>();
        List<AcolhimentoServicoExterno> acolhimentos = findAcolhimento(atendimento);
        for (AcolhimentoServicoExterno acolhimento : acolhimentos) {
            EvolucaoProntuario evolucaoProntuario = buildEvolucaoProntuario(acolhimento);
            evolucoes.add(evolucaoProntuario);
        }

        return evolucoes;
    }

    private EvolucaoProntuario buildEvolucaoProntuario(AcolhimentoServicoExterno acolhimento) {
        EvolucaoProntuario evolucao = new EvolucaoProntuario();
        Atendimento atendimento = this.buildAtendimento(acolhimento);

        evolucao.setDescricao(acolhimento.getDadoAtendimento());
        evolucao.setDataRegistro(acolhimento.getDataCadastro());
        evolucao.setAtendimento(atendimento);
        return evolucao;
    }

    private Atendimento buildAtendimento(AcolhimentoServicoExterno acolhimento) {
        Atendimento atendimento = new Atendimento();
        atendimento.setProfissional(this.buildProfissional(acolhimento));
        atendimento.setNaturezaProcuraTipoAtendimento(this.buildNatureza());
        atendimento.setEmpresa(acolhimento.getEmpresa());
        return atendimento;
    }

    private NaturezaProcuraTipoAtendimento buildNatureza() {
        NaturezaProcuraTipoAtendimento natureza = new NaturezaProcuraTipoAtendimento();
        TipoAtendimento tipoAtendimento = new TipoAtendimento();
        tipoAtendimento.setDescricao("Acolhimento por Serviço Externo");
        natureza.setTipoAtendimento(tipoAtendimento);
        return natureza;
    }

    private Profissional buildProfissional(AcolhimentoServicoExterno acolhimento) {
        Usuario usuario = acolhimento.getUsuario();
        Profissional profissional = acolhimento.getUsuario().getProfissional();
        boolean hasProfissional = profissional != null && profissional.getNome() != null;
        return hasProfissional ? profissional : new Profissional(usuario.getCodigo(), usuario.getNome());
    }

    private List<AcolhimentoServicoExterno> findAcolhimento(Atendimento atendimento) {
        return LoadManager.getInstance(AcolhimentoServicoExterno.class)
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_DADO_ATENDIMENTO))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_DATA_CADASTRO))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_USUARIO_CADSUS))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_USUARIO, Usuario.PROP_CODIGO))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_USUARIO, Usuario.PROP_NOME))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperties(montarPath(AcolhimentoServicoExterno.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(AcolhimentoServicoExterno.PROP_USUARIO, Usuario.PROP_PROFISSIONAL)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(montarPath(AcolhimentoServicoExterno.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), atendimento.getUsuarioCadsus().getCodigo()))
                .start().getList();
    }
}
