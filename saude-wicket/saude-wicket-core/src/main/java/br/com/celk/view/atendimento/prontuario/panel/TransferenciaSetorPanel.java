package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.temp.behavior.TempBehavior;
import br.com.celk.component.temp.behavior.TempFormBehavior;
import br.com.celk.component.temp.interfaces.TempHelper;
import br.com.celk.component.temp.interfaces.impl.DefaultTempStoreStrategy;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.web.transferenciasetor.dto.TransferenciaSetorDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import java.util.List;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import static ch.lambdaj.Lambda.*;
import static ch.lambdaj.collection.LambdaCollections.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import static org.hamcrest.CoreMatchers.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import ch.lambdaj.collection.LambdaList;
import ch.lambdaj.group.Group;
import java.util.ArrayList;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.model.IModel;


/**
 *
 * <AUTHOR> felipe
 */
public class TransferenciaSetorPanel extends ProntuarioCadastroPanel {

    private IModel<TransferenciaSetorDTO> model;
    private DropDown<Empresa> cbxEmpresas;
    private DropDown<NaturezaProcuraTipoAtendimento> cbxEncaminhamentos;
    private List<EmpresaNaturezaProcuraTipoAtendimento> setoresTransferencia;

    public TransferenciaSetorPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form", model = new CompoundPropertyModel<TransferenciaSetorDTO>(new TransferenciaSetorDTO()));

        TransferenciaSetorDTO proxy = on(TransferenciaSetorDTO.class);

        form.add(cbxEmpresas = new DropDown(path(proxy.getEmpresa())));
        form.add(cbxEncaminhamentos = new DropDown(path(proxy.getNaturezaProcuraTipoAtendimento())));

        add(form);

        form.add(new TempFormBehavior(new DefaultTempStoreStrategy(getAtendimento(), getIdentificador().toString(), TransferenciaSetorDTO.class)));

        initDropDowns();
    }

    private void initDropDowns() {
            setoresTransferencia = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
            .addProperties(new HQLProperties(EmpresaNaturezaProcuraTipoAtendimento.class).getProperties())
            .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class, EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO).getProperties())
            .addProperties(new HQLProperties(NaturezaProcura.class, VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA)).getProperties())
            .addProperties(new HQLProperties(TipoAtendimento.class, VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO)).getProperties())
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_TRANSFERIVEL), RepositoryComponentDefault.SIM))
            .addParameter(new QueryCustom.QueryCustomParameter(Coalesce.asString(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, NaturezaProcura.PROP_PERMITE_AGENDAMENTO), RepositoryComponentDefault.NAO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.SIM))                    
            .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO)))
            .start().getList();
            
            Group<EmpresaNaturezaProcuraTipoAtendimento> group = group(setoresTransferencia, by(on(EmpresaNaturezaProcuraTipoAtendimento.class).getEmpresa()));
            
            cbxEmpresas.addChoice(null, "");

            for (Group<EmpresaNaturezaProcuraTipoAtendimento> subGroup : group.subgroups()) {
                cbxEmpresas.addChoice(subGroup.first().getEmpresa(), subGroup.first().getEmpresa().getDescricao());
            }

            cbxEncaminhamentos.addChoice(null, "");

            cbxEmpresas.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    cbxEncaminhamentos.removeAllChoices(target);
                    popularEncaminhamentos();
                    new TempHelper().save(target, cbxEncaminhamentos);
                }
            });

            popularEncaminhamentos();
            
            this.cbxEmpresas.add(new TempBehavior());
            this.cbxEncaminhamentos.add(new TempBehavior());
    }
    
    private void popularEncaminhamentos(){
        cbxEncaminhamentos.addChoice(null, "");
        if (model.getObject().getEmpresa()!=null) {
            LambdaList<EmpresaNaturezaProcuraTipoAtendimento> filter = with(new ArrayList(setoresTransferencia))
                    .retain(having(on(EmpresaNaturezaProcuraTipoAtendimento.class).getEmpresa(), equalTo(model.getObject().getEmpresa())))
                    .sort(on(EmpresaNaturezaProcuraTipoAtendimento.class).getNaturezaProcuraTipoAtendimento().getNaturezaTipoAtendimentoDescricao());
            for (EmpresaNaturezaProcuraTipoAtendimento setoresTransferenciaDTO : filter) {
                cbxEncaminhamentos.addChoice(setoresTransferenciaDTO.getNaturezaProcuraTipoAtendimento(), setoresTransferenciaDTO.getNaturezaProcuraTipoAtendimento().getNaturezaTipoAtendimentoDescricao());
            }
        }
    }
}
