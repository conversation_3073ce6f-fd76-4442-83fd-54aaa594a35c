package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoSimNao<T> extends Window {

    private PnlConfirmacao pnlConfirmacao;
    private String message;
    private T object;
    private int height;
    private int width;


    public DlgConfirmacaoSimNao(String id) {
        this(id, "");
    }

    public DlgConfirmacaoSimNao(String id, String message) {
        this(id, message, 60, 500);
    }

    public DlgConfirmacaoSimNao(String id, int height, int width) {
        this(id, "", height, width);
    }

    public DlgConfirmacaoSimNao(String id, String message, int height, int width) {
        super(id);
        this.message = message;
        this.height = height;
        this.width = width;
        init();
    }

    private void init() {
        setTitle(getDialogTitle());

        setInitialHeight(height);
        setInitialWidth(width);
        setResizable(false);

        setContent(pnlConfirmacao = new PnlConfirmacao(getContentId(), message) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoSimNao.this.onConfirmar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoSimNao.this.onFechar(target);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onSolicitarAgendamento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoSimNao.this.onSolicitarAgendamento(target);
            }

            @Override
            public void onCancelarProtocoloSepse(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoSimNao.this.onCancelarProtocoloSepse(target);
            }

            @Override
            public String getConfirmarLabel() {
                return DlgConfirmacaoSimNao.this.getConfirmarLabel();
            }

            @Override
            public String getFecharLabel() {
                return DlgConfirmacaoSimNao.this.getFecharLabel();
            }

            @Override
            public void configuraButtonCancelar(AbstractAjaxButton btnCancelar) {
                super.configuraButtonCancelar(btnCancelar);
                DlgConfirmacaoSimNao.this.configuraButtonCancelar(btnCancelar);
            }

            @Override
            public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                super.configurarButtons(btnConfirmar, btnFechar);
                DlgConfirmacaoSimNao.this.configurarButtons(btnConfirmar, btnFechar);
            }

            @Override
            public void configuraBtnSolicitarAgendamento(AbstractAjaxButton btnSolicitarAgendamento, boolean visible) {
                super.configuraBtnSolicitarAgendamento(btnSolicitarAgendamento, visible);
                DlgConfirmacaoSimNao.this.configuraBtnSolicitarAgendamento(btnSolicitarAgendamento, visible);
            }

            @Override
            public void configuraBtnCancelarProtocoloSepse(AbstractAjaxButton btnCancelarProtocoloSepse, boolean visible) {
                super.configuraBtnSolicitarAgendamento(btnCancelarProtocoloSepse, visible);
                DlgConfirmacaoSimNao.this.configuraBtnCancelarProtocoloSepse(btnCancelarProtocoloSepse, visible);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    close(target);
                    onFechar(target);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, DlgConfirmacaoSimNao.this, ex);
                } catch (DAOException ex) {
                    MessageUtil.modalError(target, DlgConfirmacaoSimNao.this, ex);
                }
                return false;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("sim");
    }

    public String getFecharLabel() {
        return BundleManager.getString("nao");
    }

    public String getDialogTitle() {
        return BundleManager.getString("confirmar");
    }
    

    public void setMessage(AjaxRequestTarget target, String message) {
        pnlConfirmacao.setMessage(target, message);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlConfirmacao.getBtnFechar();
    }

    public void configuraButtonCancelar(AbstractAjaxButton btnCancelar) {
    }
    
    public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
    }

    public PnlConfirmacao getPnlConfirmacao() {
        return pnlConfirmacao;
    }

    public T getObject() {
        return object;
    }

    public void setObject(T object) {
        this.object = object;
    }

    public void onSolicitarAgendamento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void configuraBtnSolicitarAgendamento(AbstractAjaxButton btnSolicitarAgendamento, boolean visible) {
    }

    public void onCancelarProtocoloSepse(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void configuraBtnCancelarProtocoloSepse(AbstractAjaxButton btnCancelarProtocoloSepse, boolean visible) {
    }

}
