package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.agendamento.diario.PnlAgendamentoDiario;
import br.com.celk.view.atendimento.prontuario.panel.agendamento.horario.PnlAgendamentoHorario;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgEnviarSolicitacaoParaRegulacao;
import br.com.celk.view.atendimento.prontuario.panel.encaminhamento.MarcacaoAgendamentoDiarioEncaminhamentoEspecialistaPanel;
import br.com.celk.view.atendimento.prontuario.panel.encaminhamento.MarcacaoAgendamentoHorarioEncaminhamentoEspecialistaPanel;
import br.com.celk.view.atendimento.prontuario.panel.encaminhamento.MarcacaoAgendamentoPersonalizadoEncaminhamentoEspecialistaPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.EnviarSolicitacaoAgendamentoParaRegulacaoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoConsulta;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.List;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

public abstract class MarcacaoAgendamentoExameBpaiPanel extends ProntuarioCadastroPanel {

    private InputField txtTipoProcedimento;

    private DropDown dropDownEmpresa;
    private Empresa unidadeAgenda;

    private WebMarkupContainer containerSelecionarEmpresaAgenda;
    private Panel panelAgendamento;

    private AgendaGradeAtendimentoDTOParam param;
    private List<AgendaGradeAtendimentoDTO> agendasDisponiveis;
    private AbstractAjaxButton btnEnviarParaRegulacao;
    private WebMarkupContainer containerSolicitacaoProfissional;
    private WebMarkupContainer containerAgendamento;
    private String tipoControleRegulacao;
    private DlgEnviarSolicitacaoParaRegulacao dlgEnviarSolicitacaoParaRegulacao;
    private SolicitacaoAgendamento solicitacaoAgendamento;

    public MarcacaoAgendamentoExameBpaiPanel(String id, List<AgendaGradeAtendimentoDTO> agendasDisponiveis, AgendaGradeAtendimentoDTOParam param, SolicitacaoAgendamento solicitacaoAgendamento) {
        super(id, "Agendamento Solicitação de Exames (BPA-I)");
        this.agendasDisponiveis = agendasDisponiveis;
        this.param = param;
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        add(txtTipoProcedimento = new DisabledInputField("tipoProcedimento", new Model()));
        if(param.getTipoProcedimento() != null && param.getTipoProcedimento().getCodigo() != null){
            TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                    .addProperty(TipoProcedimento.PROP_CODIGO)
                    .addProperty(TipoProcedimento.PROP_DESCRICAO)
                    .setId(param.getTipoProcedimento().getCodigo())
                    .start().getVO();

            txtTipoProcedimento.setComponentValue(tp != null && tp.getCodigo() != null ? tp.getDescricao() : null);
        }

        add(containerAgendamento = new WebMarkupContainer("containerAgendamento"));
        containerAgendamento.setOutputMarkupPlaceholderTag(true);
        if(CollectionUtils.isAllEmpty(this.agendasDisponiveis)){
            containerAgendamento.setVisible(false);
        }

        containerSelecionarEmpresaAgenda = new WebMarkupContainer("containerSelecionarEmpresaAgenda");
        containerSelecionarEmpresaAgenda.setOutputMarkupPlaceholderTag(true);
        containerSelecionarEmpresaAgenda.setVisible(false);
        containerAgendamento.add(containerSelecionarEmpresaAgenda);

        containerSelecionarEmpresaAgenda.add(dropDownEmpresa = getDropDownEmpresa());

        containerAgendamento.add(panelAgendamento = new EmptyPanel("panelAgenda"));
        panelAgendamento.setOutputMarkupId(true);

        try {
            if(CollectionUtils.isNotNullEmpty(agendasDisponiveis)){
                configurarPanel();
            }
        } catch (ValidacaoException e) {
            warn(e.getMessage());
        }

        Form formBtn = new Form("formBtn");

        containerSolicitacaoProfissional = new WebMarkupContainer("containerSolicitacaoProfissional");
        containerSolicitacaoProfissional.setOutputMarkupId(true);
        containerSolicitacaoProfissional.setOutputMarkupPlaceholderTag(true);
        containerSolicitacaoProfissional.add(btnEnviarParaRegulacao = new AbstractAjaxButton("btnEnviarParaRegulacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgEnviarSolicitacaoParaRegulacao(target);
            }
        });
        btnEnviarParaRegulacao.setOutputMarkupPlaceholderTag(true);

        formBtn.add(containerSolicitacaoProfissional);
        add(formBtn);

        if(!RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())
                || solicitacaoAgendamento == null){
            containerSolicitacaoProfissional.setVisible(false);
        }
    }

    private void initDlgEnviarSolicitacaoParaRegulacao(AjaxRequestTarget target) {
        if (dlgEnviarSolicitacaoParaRegulacao == null) {
            dlgEnviarSolicitacaoParaRegulacao = new DlgEnviarSolicitacaoParaRegulacao(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException, DAOException {
                    EnviarSolicitacaoAgendamentoParaRegulacaoDTO dto = new EnviarSolicitacaoAgendamentoParaRegulacaoDTO();
                    dto.setSolicitacaoAgendamento(solicitacaoAgendamento);
                    dto.setOrigemSolicitacaoAgendamento(EnviarSolicitacaoAgendamentoParaRegulacaoDTO.OrigemSolicitacaoAgendamento.EXAME_PADRAO.value());

                    BOFactoryWicket.getBO(AgendamentoFacade.class).enviarSolicitacaoAgendamentoParaRegulacao(dto);

                    retornarPanelConsulta(target);
                }
            };
            getProntuarioController().addWindow(target, dlgEnviarSolicitacaoParaRegulacao);
        }
        dlgEnviarSolicitacaoParaRegulacao.show(target, solicitacaoAgendamento);
    }

    private void configurarPanel() throws ValidacaoException {
        Group<AgendaGradeAtendimentoDTO> byEmpresa = Lambda.group(agendasDisponiveis, by(on(AgendaGradeAtendimentoDTO.class).getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa()));
        List<Group<AgendaGradeAtendimentoDTO>> subgroups = byEmpresa.subgroups();
        if (subgroups.size() > 1) {
            carregarEmpresas(subgroups);
        } else {
            Empresa unidadeAgenda = subgroups.get(0).first().getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa();
            loadAgendaEmpresa(unidadeAgenda);
        }
    }

    private void loadAgendaEmpresa(Empresa unidadeAgenda) throws ValidacaoException {
        param.setEmpresaAgenda(unidadeAgenda);

        Panel panel = new EmptyPanel("panelAgenda");

        Long tipoAgenda = AgendamentoHelper.getTipoAgenda(param.getTipoProcedimento(), unidadeAgenda);
        if (TipoProcedimento.TipoAgenda.DIARIO.value().equals(tipoAgenda)) {
            panel = new PnlAgendamentoDiario("panelAgenda", param) { //, getAtendimento().getProfissional()) {
                @Override
                public void closeAgendamentoAction(AjaxRequestTarget target) {
                    retornarPanelConsulta(target);
                }
            };
        } else if (TipoProcedimento.TipoAgenda.HORARIO.value().equals(tipoAgenda)) {
            panel = new PnlAgendamentoHorario("panelAgenda", param) { //, getAtendimento().getProfissional()) {
                @Override
                public void closeAgendamentoAction(AjaxRequestTarget target) {
                    retornarPanelConsulta(target);
                }
            };
        }

        panelAgendamento.replaceWith(panel);
        panelAgendamento = panel;
    }

    public abstract void retornarPanelConsulta(AjaxRequestTarget target);

    private DropDown getDropDownEmpresa() {
        DropDown dropDown = new DropDown("unidadeAgenda", new PropertyModel(this, "unidadeAgenda"));
        this.unidadeAgenda = null;

        dropDown.addAjaxUpdateValue();
        dropDown.setOutputMarkupId(true);

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onChangeDropDownEmpresa(target);
            }
        });

        return dropDown;
    }

    private void onChangeDropDownEmpresa(AjaxRequestTarget target) {
        if (unidadeAgenda != null) {
            try {
                loadAgendaEmpresa(unidadeAgenda);
            } catch (ValidacaoException ex) {
                MessageUtil.modalWarn(target, MarcacaoAgendamentoExameBpaiPanel.this, ex);
                Panel emptyPanel = new EmptyPanel("panelAgenda");
                panelAgendamento.replaceWith(emptyPanel);
                panelAgendamento = emptyPanel;
            }
        } else {
            Panel emptyPanel = new EmptyPanel("panelAgenda");
            panelAgendamento.replaceWith(emptyPanel);
            panelAgendamento = emptyPanel;
        }

        target.add(panelAgendamento);
    }

    private void carregarEmpresas(List<Group<AgendaGradeAtendimentoDTO>> subgroups) {
        dropDownEmpresa.addChoice(null, "");

        Empresa e;
        for (Group<AgendaGradeAtendimentoDTO> group : subgroups) {
            e = group.first().getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getEmpresa();
            dropDownEmpresa.addChoice(e, e.getDescricao());
        }

        containerSelecionarEmpresaAgenda.setVisible(true);
    }

    private void clear(AjaxRequestTarget target) {
        dropDownEmpresa.removeAllChoices();
        containerSelecionarEmpresaAgenda.setVisible(false);
        target.add(containerSelecionarEmpresaAgenda);

        Panel emptyPanel = new EmptyPanel("panelAgenda");
        panelAgendamento.replaceWith(emptyPanel);
        panelAgendamento = emptyPanel;
        target.add(panelAgendamento);
    }

    private void agendamentoDiario(AjaxRequestTarget target, EncaminhamentoConsulta encaminhamentoConsulta, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException {
        getProntuarioController().changePanel(target, new MarcacaoAgendamentoDiarioEncaminhamentoEspecialistaPanel(getProntuarioController().panelId(), getAtendimento().getUsuarioCadsus(), dadosAgendamentoDTO, encaminhamentoConsulta));
    }

    private void agendamentoHorario(AjaxRequestTarget target, EncaminhamentoConsulta encaminhamentoConsulta, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException {
        getProntuarioController().changePanel(target, new MarcacaoAgendamentoHorarioEncaminhamentoEspecialistaPanel(getProntuarioController().panelId(), getAtendimento().getUsuarioCadsus(), dadosAgendamentoDTO, encaminhamentoConsulta));
    }

    private void agendamentoPersonalizada(AjaxRequestTarget target, EncaminhamentoConsulta encaminhamentoConsulta, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException {
        getProntuarioController().changePanel(target, new MarcacaoAgendamentoPersonalizadoEncaminhamentoEspecialistaPanel(getProntuarioController().panelId(), getAtendimento().getUsuarioCadsus(), dadosAgendamentoDTO, encaminhamentoConsulta));
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }
}