package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.atendimento.prontuario.interfaces.dto.AtendimentoProntuarioDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.HistoricoClinicoDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.ProntuariosDemmandPaggingDTOParam;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.PanelHistoricoClinicoAtendimentoGenerico;
import br.com.celk.view.atendimento.prontuario.panel.acolhimentoexterno.HistoricoClinicoAcolhimentoExternoUtils;
import br.com.celk.view.atendimento.prontuario.panel.exame.exameexternocovid.utils.HistoricoClinicoExameExternoUtils;
import br.com.celk.view.atendimento.prontuario.panel.exame.testerapido.utils.HistoricoClinicoUtils;
import br.com.celk.view.atendimento.prontuario.panel.outraunidade.PacienteAtendidoOutraUnidadeUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentosDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.GrupoProblemasCondicoes;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxCheckBox;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class AtendimentosInternoTab extends TabPanel<NoHistoricoClinicoDTO> {

    private ProntuariosDemmandPaggingDTOParam dtoParam;
    private WebMarkupContainer containerRepeater;
    private RepeatingView repeaterHistoricoClinicoAtendimentos;
    private WebMarkupContainer containerProblemas;
    private Table tblProblemasCondicao;
    private List<GrupoProblemasCondicoes> listProblemas;
    private HistoricoClinicoDTO ultimoHistorico;

    public AtendimentosInternoTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        this.dtoParam = new ProntuariosDemmandPaggingDTOParam(object.getAtendimento());
        init();
    }

    private void init() {
        setDefaultModel(new CompoundPropertyModel(dtoParam));

        add(getDropDownPeriodo());
        add(getDropDownGrupoAtendimentoCbo());
        add(getDropDownTipoAtendimento());
        add(getDropDownTipoRegistro());

        add(containerProblemas = new WebMarkupContainer("containerProblemas"));
        containerProblemas.setOutputMarkupId(true);
        containerProblemas.add(tblProblemasCondicao = new Table("tblProblemasCondicao", getColumns(), getCollectionProvider()));
        tblProblemasCondicao.populate();
        tblProblemasCondicao.setScrollY("1800");

        InputField txtDescricao = new InputField("descricao");
        txtDescricao.addAjaxUpdateValue();
        add(txtDescricao);

        txtDescricao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        add(new AjaxCheckBox("meusAtendimentos") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        add(containerRepeater = new WebMarkupContainer("containerRepeater"));
        containerRepeater.setOutputMarkupId(true);
        containerRepeater.add(repeaterHistoricoClinicoAtendimentos = new RepeatingView("repeaterHistoricoClinicoAtendimentos"));
        add(new AbstractAjaxButton("btnCarregarMais") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                procurar(target, true);
            }
        });

        carregarProblemasCondicao();
    }

    private void carregarProblemasCondicao(){
        if (this.dtoParam.getAtendimento().getUsuarioCadsus() != null) {
            listProblemas = LoadManager.getInstance(GrupoProblemasCondicoes.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(GrupoProblemasCondicoes.PROP_USUARIO_CAD_SUS, this.dtoParam.getAtendimento().getUsuarioCadsus()))
                    .start().getList();
            if (CollectionUtils.isEmpty(listProblemas)) {
                containerProblemas.setVisible(false);
            }
        } else {
            containerProblemas.setVisible(false);
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        GrupoProblemasCondicoes proxy = on(GrupoProblemasCondicoes.class);

        columns.add(createColumn(bundle("ciap"), proxy.getCiap().getDescricaoVO()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getDescricaoFormatado()));
        columns.add(createColumn(bundle("dataInicial"), proxy.getDataInicial()));
        columns.add(createColumn(bundle("dataFinal"), proxy.getDataFinal()));
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoFormatada()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return listProblemas;
            }
        };
    }

    private void addRepeater(HistoricoClinicoDTO historicoClinicoDTO) {
        repeaterHistoricoClinicoAtendimentos.add(new PanelHistoricoClinicoAtendimentoGenerico(repeaterHistoricoClinicoAtendimentos.newChildId(), historicoClinicoDTO, ultimoHistorico));
        ultimoHistorico = historicoClinicoDTO;
    }

    private DropDown<Integer> getDropDownPeriodo() {
        DropDown<Integer> cbxPeriodo = new DropDown("periodo");

        cbxPeriodo.addChoice(null, bundle("todos"));
        cbxPeriodo.addChoice(3, bundle("ultimos3meses"));
        cbxPeriodo.addChoice(6, bundle("ultimos6meses"));
        cbxPeriodo.addChoice(12, bundle("ultimoAno"));

        cbxPeriodo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxPeriodo;
    }

    private DropDown<GrupoAtendimentoCbo> getDropDownGrupoAtendimentoCbo() {
        DropDown<GrupoAtendimentoCbo> cbxGrupoAtendimentoCbo = new DropDown("grupoAtendimentoCbo");
        cbxGrupoAtendimentoCbo.addChoice(null, bundle("todos"));

        List<GrupoAtendimentoCbo> grupos = LoadManager.getInstance(GrupoAtendimentoCbo.class)
                .addProperty(GrupoAtendimentoCbo.PROP_CODIGO)
                .addProperty(GrupoAtendimentoCbo.PROP_DESCRICAO)
                .addSorter(new QueryCustom.QueryCustomSorter(GrupoAtendimentoCbo.PROP_DESCRICAO))
                .start().getList();

        for (GrupoAtendimentoCbo item : grupos) {
            cbxGrupoAtendimentoCbo.addChoice(item, item.getDescricao());
        }

        cbxGrupoAtendimentoCbo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxGrupoAtendimentoCbo;
    }

    private DropDown<TipoAtendimento> getDropDownTipoAtendimento() {
        DropDown<TipoAtendimento> cbxTipoAtendimento = new DropDown("tipoAtendimento");
        cbxTipoAtendimento.addChoice(null, bundle("todos"));

        try {

            AtendimentosDTOParam param = new AtendimentosDTOParam();
            param.setAtendimentoAtual(this.dtoParam.getAtendimento());
            param.setCodigoPaciente(this.dtoParam.getAtendimento().getUsuarioCadsus().getCodigo());

            List<TipoAtendimento> consultarTiposAtendimentoPrincipalDisponiveis = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarTiposAtendimentoPrincipalDisponiveis(param);
            for (TipoAtendimento item : consultarTiposAtendimentoPrincipalDisponiveis) {
                cbxTipoAtendimento.addChoice(item, item.getDescricao());
            }

        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        cbxTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxTipoAtendimento;
    }

    private DropDown getDropDownTipoRegistro() {
        DropDown<Long> cbxTipoRegistro = DropDownUtil.getIEnumDropDown("tipoRegistroProntuario", AtendimentoProntuario.TipoRegistro.values(), true, bundle("todos"), false, false, true);

        cbxTipoRegistro.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxTipoRegistro;
    }

    private void procurar(AjaxRequestTarget target) {
        procurar(target, false);
    }

    private void procurar(AjaxRequestTarget target, boolean carregarMais) {
        if (carregarMais) {
            dtoParam.moreResults();
        } else {
            dtoParam.resetControlPagging();
            repeaterHistoricoClinicoAtendimentos.removeAll();
        }

        try {
            List<HistoricoClinicoDTO> historicosClinicosDTO = new ArrayList<>();

            // BUSCA HISTÓRICO DE ATENDIMENTOS PRONTUÁRIO
            historicosClinicosDTO.addAll(getAtendimentosProntuario());

            if (dtoParam.getTipoRegistroProntuario() == null || AtendimentoProntuario.TipoRegistro.TESTE_RAPIDO.value().equals(dtoParam.getTipoRegistroProntuario())) {
                // BUSCA HISTÓRICO DE TESTES RÁPIDOS
                historicosClinicosDTO.addAll(HistoricoClinicoUtils.getTestesRapidosRealizados(dtoParam));
            }

            if ((dtoParam.getTipoRegistroProntuario() == null || AtendimentoProntuario.TipoRegistro.EXAME_EXTERNO_COVID.value().equals(dtoParam.getTipoRegistroProntuario()))
                    && dtoParam.getTipoAtendimento() == null && dtoParam.getGrupoAtendimentoCbo() == null) {
                // BUSCA HISTÓRICO DE EXAME EXTERNO COVID
                historicosClinicosDTO.addAll(HistoricoClinicoExameExternoUtils.getHistoricosClinicos(dtoParam));
            }

            if ((dtoParam.getTipoRegistroProntuario() == null || AtendimentoProntuario.TipoRegistro.OCORRENCIA.value().equals(dtoParam.getTipoRegistroProntuario()))) {
                // BUSCA ATENDIMENTO OUTRA UNIDADE
                historicosClinicosDTO.addAll(PacienteAtendidoOutraUnidadeUtils.getHistoricosClinicos(dtoParam));
            }

            if ((dtoParam.getTipoRegistroProntuario() == null || AtendimentoProntuario.TipoRegistro.NOTIFICACAO.value().equals(dtoParam.getTipoRegistroProntuario()))) {
                List<HistoricoClinicoDTO> notificacoes = (List<HistoricoClinicoDTO>) HistoricoClinicoUtils.getCidsNotificaveis(dtoParam);
                if (CollectionUtils.isNotNullEmpty(notificacoes)) {
                    historicosClinicosDTO.addAll(notificacoes);
                }
            }

            if ((dtoParam.getTipoRegistroProntuario() == null
                    || (!dtoParam.isMeusAtendimentos() && dtoParam.getGrupoAtendimentoCbo() == null && dtoParam.getTipoAtendimento() == null
                    && AtendimentoProntuario.TipoRegistro.ACOLHIMENTO_SERVICO_EXTERNO.value().equals(dtoParam.getTipoRegistroProntuario())))) {
                // BUSCA ATENDIMENTO ACOLHIMENTO SERVIÇO EXTERNO
                historicosClinicosDTO.addAll(HistoricoClinicoAcolhimentoExternoUtils.getHistoricosClinicos(dtoParam));
            }

            // ORDENA REGISTROS POR DATA
            Collections.sort(historicosClinicosDTO);

            for (HistoricoClinicoDTO historicoClinicoDTO : historicosClinicosDTO) {
                addRepeater(historicoClinicoDTO);
            }

            if (target != null) {
                target.add(containerRepeater);
            }
        } catch (DAOException | ValidacaoException ex) {
            Logger.getLogger(AtendimentosInternoTab.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private List<HistoricoClinicoDTO> getAtendimentosProntuario() throws DAOException, ValidacaoException {
        List<HistoricoClinicoDTO> historicoClinicoDTOS = new ArrayList<>();

        List<AtendimentoProntuarioDTO> atendimentoProntuarioList = BOFactoryWicket.getBO(AtendimentoFacade.class).prontuariosDemmandPagging(dtoParam);
        if (CollectionUtils.isNotNullEmpty(atendimentoProntuarioList)) {
            Group<AtendimentoProntuarioDTO> byAtendimento = Lambda.group(atendimentoProntuarioList, Lambda.by(Lambda.on(AtendimentoProntuarioDTO.class).getAtendimento().getAtendimentoPrincipal()));
            for (Group<AtendimentoProntuarioDTO> subgroup : byAtendimento.subgroups()) {
                if (AtendimentoProntuario.Origem.INTERNO.value().equals(subgroup.findAll().get(0).getFlagOrigem())) {
                    historicoClinicoDTOS.addAll(HistoricoClinicoUtils.criaListagemHistoricosClinicosFromAtendimentosProntuario(subgroup.findAll()));
                }
            }

            Group<AtendimentoProntuarioDTO> byCodigo = Lambda.group(atendimentoProntuarioList, Lambda.by(Lambda.on(AtendimentoProntuarioDTO.class).getCodigoAtendimentoPrincipalExterno()));
            for (Group<AtendimentoProntuarioDTO> subgroup : byCodigo.subgroups()) {
                if (!AtendimentoProntuario.Origem.INTERNO.value().equals(subgroup.findAll().get(0).getFlagOrigem())) {
                    historicoClinicoDTOS.addAll(HistoricoClinicoUtils.criaListagemHistoricosClinicosFromAtendimentosProntuario(subgroup.findAll()));
                }
            }
        }
        return historicoClinicoDTOS;
    }

    @Override
    public void onSelectionTab() {
        procurar(null);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }

    @Override
    public String getTitle() {
        return bundle("atendimentosInternos");
    }
}
