package br.com.celk.view.atendimento.prontuario.panel.tratamentoodontologico.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgHistoricoTrabalhosExecutados extends Window {

    private PnlHistoricoTrabalhosExecutados pnlHistorico;

    public DlgHistoricoTrabalhosExecutados(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(250);
        setInitialWidth(600);
        setTitle(BundleManager.getString("historicoTrabalhosExecutados"));

        setContent(pnlHistorico = new PnlHistoricoTrabalhosExecutados(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

        });
    }

    public void show(AjaxRequestTarget target, Long codigoOdontoPlano) {
        pnlHistorico.setCodigo(target, codigoOdontoPlano);
        show(target);
    }

}
