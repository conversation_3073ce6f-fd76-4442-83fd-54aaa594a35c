package br.com.celk.view.atendimento.prontuario.panel.encaminhamento.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoConsultaDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCancelarEncaminhamento extends Panel {

    private Form<EncaminhamentoConsultaDTO> form;

    public PnlCancelarEncaminhamento(String id) {
        super(id);
        init();
    }

    private void init() {
        EncaminhamentoConsultaDTO proxy = on(EncaminhamentoConsultaDTO.class);

        form = new Form<EncaminhamentoConsultaDTO>("form", new CompoundPropertyModel(new EncaminhamentoConsultaDTO()));

        form.setOutputMarkupId(true);

        form.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getTipoEncaminhamento().getDescricaoFormatado())));
        form.add(new DisabledInputField(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getDescricaoStatus())));
        form.add(new InputArea(path(proxy.getEncaminhamentoConsulta().getEncaminhamento().getDescricaoCancelamento())));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCancelarEncaminhamentoEspecialista(target)) {
                    onConfirmar(target, PnlCancelarEncaminhamento.this.form.getModel().getObject());
                }
            }
        });

        form.add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public boolean validarCancelarEncaminhamentoEspecialista(AjaxRequestTarget target) {
        try {
            if (form.getModel().getObject().getEncaminhamentoConsulta().getEncaminhamento().getDescricaoCancelamento() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo_cancelamento"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, EncaminhamentoConsultaDTO encaminhamentoConsultaDTO) throws ValidacaoException, DAOException;

    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        form.getModel().setObject(null);
    }

    public void setEncaminhamentoConsultaDTO(EncaminhamentoConsultaDTO dto) {
        form.getModel().setObject(dto);
    }
}