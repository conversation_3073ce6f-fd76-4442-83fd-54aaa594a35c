package br.com.celk.component.treetable.pageable.customdatatable;

import java.util.List;
import java.util.Set;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.DataTable;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.tree.AbstractTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.ITreeProvider;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.ITreeDataProvider;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeModel;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.TreeDataProvider;
import org.apache.wicket.markup.repeater.IItemReuseStrategy;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.markup.repeater.RefreshingView;
import org.apache.wicket.markup.repeater.data.IDataProvider;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.lang.Args;
import org.apache.wicket.util.visit.IVisit;
import org.apache.wicket.util.visit.IVisitor;

/**
 * A tree with tabular markup.
 *
 * <AUTHOR>
 *
 * @param <T> The model object type
 * @param <S> the type of the sort property
 */
public abstract class CustomTableTree<T, S> extends AbstractTree<T> {

    private static final long serialVersionUID = 1L;

    private final CustomDataTable<T, S> table;

    /**
     * Constructor
     *
     * @param id component id
     * @param columns list of IColumn objects
     * @param dataProvider imodel for data provider
     */
    public CustomTableTree(final String id, final List<? extends IColumn<T, S>> columns,
            final ITreeProvider<T> dataProvider, int rowsPerPage) {
        this(id, columns, dataProvider, rowsPerPage, null);
    }

    /**
     * Constructor
     *
     * @param id component id
     * @param columns list of IColumn objects
     * @param provider provider of the tree
     * @param state the expansion state
     */
    public CustomTableTree(final String id, final List<? extends IColumn<T, S>> columns,
            final ITreeProvider<T> provider, int rowsPerPage, IModel<Set<T>> state) {
        super(id, provider, state);

        Args.notEmpty(columns, "columns");
        for (IColumn<T, S> column : columns) {
            if (column instanceof CustomITreeColumn<?, ?>) {
                ((CustomITreeColumn<T, S>) column).setTree(this);
            }
        }
        
        this.table = newDataTable("table", columns, newDataProvider(provider), provider, rowsPerPage, this);
        add(table);

        // see #updateBranch(Object, AjaxRequestTarget)
        setOutputMarkupId(true);
    }

    /**
     * Factory method for the wrapped {@link DataTable}.
     *
     * Note: If overwritten, the DataTable's row items have to output their
     * markupId, or {@link #updateNode(Object, AjaxRequestTarget)} will fail.
     *
     * @param id
     * @param columns
     * @param dataProvider
     * @param rowsPerPage
     * @return nested data table
     */
    protected CustomDataTable<T, S> newDataTable(String id, List<? extends IColumn<T, S>> columns,
            IDataProvider<T> dataProvider, ITreeProvider treeProvider, int rowsPerPage, CustomTableTree treeTable) {
        return new CustomDataTable<T, S>(id, columns, dataProvider, treeProvider, rowsPerPage, treeTable) {
            private static final long serialVersionUID = 1L;

            @Override
            protected Item<T> newRowItem(String id, int index, IModel<T> model) {
                Item<T> item = CustomTableTree.this.newRowItem(id, index, model);

                // see #update(Node);
                item.setOutputMarkupId(true);

                return item;
            }

        };
    }

    /**
     * Get the nested table.
     *
     * @return the nested table
     */
    public CustomDataTable<T, S> getTable() {
        return table;
    }

    /**
     * Sets the item reuse strategy. This strategy controls the creation of
     * {@link Item}s.
     *
     * @see RefreshingView#setItemReuseStrategy(IItemReuseStrategy)
     * @see IItemReuseStrategy
     *
     * @param strategy item reuse strategy
     * @return this for chaining
     */
    @Override
    public final CustomTableTree<T, S> setItemReuseStrategy(final IItemReuseStrategy strategy) {
        table.setItemReuseStrategy(strategy);

        super.setItemReuseStrategy(strategy);

        return this;
    }

    /**
     * For updating of a single branch the whole table is added to the ART.
     */
    @Override
    public void updateBranch(T node, AjaxRequestTarget target) {
        if (target != null) {
            // TableTree always outputs markupId
            target.add(this);
        }
    }

    /**
     * For an update of a node the complete row item is added to the ART.
     */
    @Override
    public void updateNode(T t, final AjaxRequestTarget target) {
        if (target != null) {
            final IModel<T> model = getProvider().model(t);
            visitChildren(Item.class, new IVisitor<Item<T>, Void>() {
                @Override
                public void component(Item<T> item, IVisit<Void> visit) {
                    NodeModel<T> nodeModel = (NodeModel<T>) item.getModel();

                    if (model.equals(nodeModel.getWrappedModel())) {
                        // row items are configured to output their markupId
                        target.add(item);
                        visit.stop();
                        return;
                    }
                    visit.dontGoDeeper();
                }
            });
            model.detach();
        }
    }

    /**
     * Hook method to create an {@link ITreeDataProvider}.
     *
     * @param provider the tree provider
     * @return the data provider
     */
    protected ITreeDataProvider<T> newDataProvider(ITreeProvider<T> provider) {
        return new TreeDataProvider<T>(provider) {
            private static final long serialVersionUID = 1L;

            @Override
            protected boolean iterateChildren(T object) {
                return CustomTableTree.this.getState(object) == AbstractTree.State.EXPANDED;
            }
        };
    }

    /**
     * Create a row item for the nested {@link DataTable}.
     *
     * @param id component id
     * @param index index of row
     * @param model model for row
     * @return row item
     */
    protected Item<T> newRowItem(String id, int index, IModel<T> model) {
        Item<T> item = new Item<T>(id, index, model);

        return item;
    }
}
