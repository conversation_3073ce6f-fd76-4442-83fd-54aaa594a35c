package br.com.celk.view.basico.empresa.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.MatchMode;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaEmpresa extends CustomizeConsultaAdapter implements Serializable{

    private boolean validaUsuarioEmpresa;
    private List<Long> tiposEstabelicimento;

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustomParameter(Empresa.PROP_DESCRICAO, QueryParameter.ILIKE, MatchMode.ANYWHERE));
        filterProperties.put(BundleManager.getString("codigo"), new QueryCustomParameter(Empresa.PROP_CODIGO, QueryParameter.IGUAL));
        filterProperties.put(BundleManager.getString("cnes"), new QueryCustomParameter(Empresa.PROP_CNES, QueryParameter.IGUAL));
        filterProperties.put(BundleManager.getString("sigla"), new QueryCustomParameter(Empresa.PROP_SIGLA, QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("cpfCnpj"), new QueryCustomParameter(Empresa.PROP_CNPJ, QueryParameter.IGUAL));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("referencia"), Empresa.PROP_REFERENCIA);
        properties.put(BundleManager.getString("cnes"), Empresa.PROP_CNES);
        properties.put(BundleManager.getString("sigla"), Empresa.PROP_SIGLA);
        properties.put(BundleManager.getString("descricao"), Empresa.PROP_DESCRICAO);
        properties.put(BundleManager.getString("cpfCnpj"), Empresa.PROP_CNPJ);
    }

    @Override
    public Class getClassConsulta() {
        return Empresa.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(
                new HQLProperties(Empresa.class).getProperties(),
                new HQLProperties(EmpresaMaterial.class, Empresa.PROP_EMPRESA_MATERIAL).getProperties(),
                new HQLProperties(RegionalSaude.class, VOUtils.montarPath(Empresa.PROP_MICRORREGIAO, Microrregiao.PROP_REGIONAL_SAUDE)).getProperties(),
                new String[]{
                    VOUtils.montarPath(Empresa.PROP_EMPRESA_MANTENEDORA, EmpresaMantenedora.PROP_CODIGO),
                    VOUtils.montarPath(Empresa.PROP_EMPRESA_MANTENEDORA, EmpresaMantenedora.PROP_NOME_MANTENEDORA),
                }
                );
    }

    @Override
    public void consultaCustomizeParameters(List<QueryParameter> parameters) {
        try {
            if (validaUsuarioEmpresa) {
                Usuario usuario = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
                if (!usuario.isNivelAdminOrMaster()) {
                    if (usuario.getEmpresasUsuario() == null) {
                        usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                    }
                    if (usuario.getEmpresasUsuario() != null) {
                        parameters.add(new QueryCustomParameter(Empresa.PROP_CODIGO, QueryParameter.IN, ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getEmpresasUsuario()));
                    } else {
                        parameters.add(new QueryCustomParameter(Empresa.PROP_CODIGO, ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCodigo()));
                    }
                }
            }
            parameters.add(new QueryCustomParameter(Empresa.PROP_TIPO_UNIDADE, QueryParameter.IN, tiposEstabelicimento));
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public void setTiposEstabelecimento(List<Long> tiposEstabelicimento) {
        this.tiposEstabelicimento = tiposEstabelicimento;
    }
    
    public void setValidaUsuarioEmpresa(boolean validaUsuarioEmpresa) {
        this.validaUsuarioEmpresa = validaUsuarioEmpresa;
    }
}
