package br.com.celk.view.atendimento.prontuario.button;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.prontuario.interfaces.NodeButtonEventListener;
import br.com.celk.view.atendimento.prontuario.nodes.IProntuarioNode;
import java.util.Map;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;

/**
 *
 * <AUTHOR>
 */
public abstract class NodeButton extends AbstractAjaxLink {

    private AttributeAppender validacaoNode = new AttributeAppender("class", " warning");
    private boolean active;
    private IProntuarioNode prontuarioNode;
    
    public NodeButton(String id, IProntuarioNode prontuarioNode) {
        super(id);
        this.prontuarioNode = prontuarioNode;
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        add(new WebMarkupContainer("icon").add(new AttributeModifier("class", "icon32 "+prontuarioNode.getIcone().clazz())));
        add(new Label("labelNode", prontuarioNode.getTitulo()));
    }

    public AttributeAppender getValidacaoNode() {
        return validacaoNode;
    }

    public IProntuarioNode getProntuarioNode() {
        return prontuarioNode;
    }
    
    public NodeButtonEventListener createListener(){
        return new NodeButtonEventListener() {

            @Override
            public void validar(AjaxRequestTarget target, Map<NodesAtendimentoRef, ValidacaoProcesso> validacoes) {
                if(validacoes.containsKey(getProntuarioNode().getIdentificador())){
                    if (!getBehaviors().contains(getValidacaoNode())) {
                        add(getValidacaoNode());
                        if (target!=null) {
                            target.add(NodeButton.this);
                        }
                    }
                } else {
                    if (getBehaviors().contains(getValidacaoNode())) {
                        remove(getValidacaoNode());
                        if (target!=null) {
                            target.add(NodeButton.this);
                        }
                    }
                }
            }

            @Override
            public void ativar(AjaxRequestTarget target, IProntuarioNode prontuarioNode) {
                if(prontuarioNode.getIdentificador().equals(getProntuarioNode().getIdentificador())){
                    if (!active) {
                        active = true;
                        if (target!=null) {
                            target.appendJavaScript("$('#"+getMarkupId()+"').addClass('active')");
                        }
                    }
                } else {
                    if (active) {
                        active = false;
                        if (target!=null) {
                            target.appendJavaScript("$('#"+getMarkupId()+"').removeClass('active')");
                        }
                    }
                }
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (active) {
            response.render(OnDomReadyHeaderItem.forScript("$('#"+getMarkupId()+"').addClass('active')"));
        }
    }

}
