package br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.temp.behavior.TempBehavior;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapido;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlInformarResultadoTesteRapidoCovidInfluenzaAB extends Panel{

    private LoadableObjectModel<TesteRapidoRealizado> model;
    private DropDown dropDownResultado;
    private InputArea descricaoResultado;
    private InputField txtLote;
    private DateChooser dchDataValidade;
    private Long tipoTeste;
    private Long codigoAtendimento;
    private List<TesteRapidoRealizado> testeRapidoRealizadosDesteAtendimento;
    private WebMarkupContainer containerTipoAmostra;
    private DropDown dropDownResultadoInfluenzaA;
    private DropDown dropDownResultadoInfluenzaB;
    private InputField txtLoteInfluenza;
    private DateChooser dchDataValidadeInfluenza;



    public PnlInformarResultadoTesteRapidoCovidInfluenzaAB(String id){
        super(id);
        init();
    }

    public PnlInformarResultadoTesteRapidoCovidInfluenzaAB(String id, Long codigoAtendimento){
        super(id);
        this.codigoAtendimento = codigoAtendimento;
        init();
    }
    private void init(){
        Form form = new Form<TesteRapidoRealizado>("form", new CompoundPropertyModel(model = new LoadableObjectModel<TesteRapidoRealizado>(TesteRapidoRealizado.class, false)));
        TesteRapidoRealizado proxy = on(TesteRapidoRealizado.class);
        containerTipoAmostra = new WebMarkupContainer("containerTipoAmostra");

        form.add(new DisabledInputField(path(proxy.getTipoTesteRapido().getDescricaoTipoTeste())));
        form.add(dropDownResultado= DropDownUtil.getIEnumDropDown(path(proxy.getResultado()), TesteRapidoRealizado.ResultadoCovidInfluenza.values(),false, ""));
        form.add(txtLote = new InputField(path(proxy.getLote())));
        form.add(dchDataValidade = new DateChooser(path(proxy.getDataValidade())));
        dchDataValidade.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));
        form.add(descricaoResultado = new InputArea(path(proxy.getDescricaoResultado())));
        form.add(dropDownResultadoInfluenzaA = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoInfluenzaA()), TesteRapidoRealizado.ResultadoCovidInfluenza.values(),false, ""));
        form.add(dropDownResultadoInfluenzaB = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoInfluenzaB()), TesteRapidoRealizado.ResultadoCovidInfluenza.values(), false, ""));
        form.add(txtLoteInfluenza = new InputField(path(proxy.getLoteInfluenza())));
        form.add(dchDataValidadeInfluenza = new DateChooser(path(proxy.getDataValidadeInfluenza())));
        dchDataValidadeInfluenza.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));

        form.add(containerTipoAmostra);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarInformarResultado(target)){
                    onConfirmar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
        carregaTesteRapidoRealisadoNoAtendimento();
    }
    
    public boolean validarInformarResultado(AjaxRequestTarget target) {
        try {
            if(dropDownResultado.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_resultado"));
            }
            if(txtLote.getComponentValue() == null || txtLoteInfluenza.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_lote"));
            }
            if(dchDataValidade.getComponentValue() == null || dchDataValidadeInfluenza.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_validade"));
            }

        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }

    private void carregaTesteRapidoRealisadoNoAtendimento(){

        testeRapidoRealizadosDesteAtendimento = LoadManager.getInstance(TesteRapidoRealizado.class)
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_RESULTADO))
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_NR_TESTE_RAPIDO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO), codigoAtendimento))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, TesteRapidoRealizado.Status.CANCELADO.value()))
                .start().getList();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException;
    
    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){
        dropDownResultadoInfluenzaA.clearInput();
    }

    public void setTesteRapidoRealizado(TesteRapidoRealizado testeRapidoRealizado, AjaxRequestTarget target) {
        this.model.setObject(testeRapidoRealizado);
        this.tipoTeste = testeRapidoRealizado.getTipoTesteRapido().getTipoTeste();
    }

}