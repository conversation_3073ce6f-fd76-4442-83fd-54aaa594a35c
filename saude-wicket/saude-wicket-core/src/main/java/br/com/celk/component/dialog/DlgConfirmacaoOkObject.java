package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoOkObject<T extends Serializable> extends Window {

    private PnlConfirmacaoOk pnlConfirmacaoOk;
    private String message;
    private String imagem;

    public DlgConfirmacaoOkObject(String id) {
        super(id);
        this.message = "";
        init();
    }

    public DlgConfirmacaoOkObject(String id, String message) {
        super(id);
        this.message = message;
        init();
    }

    public DlgConfirmacaoOkObject(String id, String message, String imagem) {
        super(id);
        this.message = message;
        this.imagem = imagem;
        init();
    }

    private void init() {
        setDefaultModel(newModel());
        setTitle(getDialogTitle());

        setResizable(false);

        setContent(pnlConfirmacaoOk = new PnlConfirmacaoOk(getContentId(), prepareMessage(message), imagem) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                T object = DlgConfirmacaoOkObject.this.getModelObject();
                onAction(target, object);
            }

            @Override
            public String getConfirmarLabel() {
                return DlgConfirmacaoOkObject.this.getConfirmarLabel();
            }

        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    T object = DlgConfirmacaoOkObject.this.getModelObject();
                    onAction(target, object);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, DlgConfirmacaoOkObject.this, ex);
                } catch (DAOException ex) {
                    MessageUtil.modalWarn(target, DlgConfirmacaoOkObject.this, ex);
                }

                return false;
            }
        });
    }

    private void onAction(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException {
        close(target);
        onConfirmar(target, object);
    }

    @Override
    public int getInitialHeight() {
        return 60;
    }

    @Override
    public int getInitialWidth() {
        return 500;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, T modelObject) throws ValidacaoException, DAOException;

    public String getConfirmarLabel() {
        return BundleManager.getString("ok");
    }

    public String getDialogTitle() {
        return BundleManager.getString("aviso");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        pnlConfirmacaoOk.setMessage(target, prepareMessage(message));
    }

    public void setImagem(String imagem) {
        pnlConfirmacaoOk.setIMG(imagem);
    }

    public void show(AjaxRequestTarget target, T object) {
        ((IModel<T>) getDefaultModel()).setObject(object);
        super.show(target);
    }

    public T getModelObject() {
        return (T) getDefaultModel().getObject();
    }

    public IModel<T> newModel() {
        return new Model<T>();
    }

    private String prepareMessage(String message) {
        if (StringUtils.trimToNull(message) != null) {
            message = StringUtils.replace(message, "<html>", "");
            message = StringUtils.replace(message, "<br>", "\n");

            message = message.replaceAll("</br>", "\n");
            message = message.replaceAll("</html>", "");
        }

        return message;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlConfirmacaoOk.getBtnConfirmar();
    }

}
