package br.com.celk.component.wizard;

import br.com.celk.system.javascript.JScript;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.wizard.IDefaultButtonProvider;
import org.apache.wicket.extensions.wizard.Wizard;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.IFormSubmittingComponent;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.resource.PackageResourceReference;

/**
 *
 * <AUTHOR>
 */
public class AjaxWizard extends Panel implements IAjaxWizard, IAjaxWizardModelListener{

    public static final String BUTTONS_ID = "buttons";
    public static final String FEEDBACK_ID = "feedback";
    public static final String HEADER_ID = "header";
    public static final String OVERVIEW_ID = "overview";
    public static final String FORM_ID = "form";
    public static final String VIEW_ID = "view";
    private static final long serialVersionUID = 1L;
    private Form<?> form;
    private IAjaxWizardModel wizardModel;
    private final boolean addDefaultCssStyle;
    private AjaxWizardHeader wizardHeader;

    public AjaxWizard(final String id) {
        this(id, true);
    }

    public AjaxWizard(final String id, final boolean addDefaultCssStyle) {
        super(id);
        this.addDefaultCssStyle = addDefaultCssStyle;
    }

    public AjaxWizard(final String id, final IAjaxWizardModel wizardModel) {
        this(id, wizardModel, true);
    }

    public AjaxWizard(final String id, final IAjaxWizardModel wizardModel, final boolean addDefaultCssStyle) {
        super(id);

        init(wizardModel);
        this.addDefaultCssStyle = addDefaultCssStyle;

    }

    public void addDefaultCssStyle(final IHeaderResponse response) {
        response.render(CssHeaderItem.forReference(new PackageResourceReference(Wizard.class, "Wizard.css")));
    }

    @Override
    public void renderHead(final IHeaderResponse response) {
        super.renderHead(response);
        if (addDefaultCssStyle) {
            addDefaultCssStyle(response);
        }
    }

    public final IAjaxWizardStep getActiveStep() {
        return getAjaxWizardModel().getActiveStep();
    }

    public Form<?> getForm() {
        return form;
    }

    @Override
    public final IAjaxWizardModel getAjaxWizardModel() {
        return wizardModel;
    }

    @Override
    public boolean isVersioned() {
        return false;
    }

    @Override
    public void onActiveStepChanged(final IAjaxWizardStep newStep) {
        form.replace(newStep.getView(VIEW_ID, this, this));
        wizardHeader.update();
    }
    
    @Override
    public void onActiveStepChanged(AjaxRequestTarget target, final IAjaxWizardStep newStep) {
        onActiveStepChanged(newStep);
        target.appendJavaScript(JScript.scrollToTop());
        wizardHeader.update(target);
    }

    @Override
    public void onCancel(AjaxRequestTarget target) {
    }

    @Override
    public void onFinish(AjaxRequestTarget target) {
    }

    protected void init(final IAjaxWizardModel wizardModel) {
        setOutputMarkupId(true);
        if (wizardModel == null) {
            throw new IllegalArgumentException("argument wizardModel must be not null");
        }

        this.wizardModel = wizardModel;
        
        form = newForm(FORM_ID);
        addOrReplace(form);
        form.addOrReplace(newHeader(HEADER_ID));
        form.addOrReplace(newFeedbackPanel(FEEDBACK_ID));
        form.addOrReplace(new WebMarkupContainer(VIEW_ID));
        form.addOrReplace(newButtonBar(BUTTONS_ID));
        form.addOrReplace(newOverviewBar(OVERVIEW_ID));

        wizardModel.addListener(this);

        wizardModel.reset();
    }

    protected Component newHeader(final String id) {
        return wizardHeader = new AjaxWizardHeader(id, wizardModel);
    }
    
    protected Component newButtonBar(final String id) {
        return new AjaxWizardBar(id, this);
    }

    protected Component newFeedbackPanel(final String id) {
        return new WebMarkupContainer(id).setVisible(false);
    }

    protected <E> Form<E> newForm(final String id) {
        return new Form<E>(id);
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        Component buttonBar = form.get(BUTTONS_ID);
        if (buttonBar instanceof IDefaultButtonProvider) {
            IFormSubmittingComponent defaultButton = ((IAjaxDefaultButtonProvider) buttonBar).getDefaultButton(wizardModel);
            form.setDefaultButton(defaultButton);
        }
    }

    protected Component newOverviewBar(final String id) {
        return new WebMarkupContainer(id).setVisible(false);
    }

}
