package br.com.celk.component.duracaofield;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.DataUtil;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.convert.IConverter;

import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class DiaMesField extends InputField<Date> {

    public DiaMesField(String id, IModel model) {
        super(id, model, Date.class);
        init();
    }

    public DiaMesField(String id) {
        super(id, Date.class);
        init();
    }

    private void init() {
        add(new AttributeModifier("class", "diaMes"));
        add(new AttributeModifier("maxlength", "5"));
        add(new AttributeModifier("size", "5"));
    }

    @Override
    public <C> IConverter<C> getConverter(Class<C> type) {
        return new IConverter<C>() {

            @Override
            public C convertToObject(String value, Locale locale) {
                if (value != null) {
                    return (C) DataUtil.getDayMonthFromDate(value);
                }
                return (C) "__/__";
            }

            @Override
            public String convertToString(C value, Locale locale) {
                if (value != null) {
                    return DataUtil.getFormatarDiaMes((Date) value);
                }
                return "__/__";
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }
}
