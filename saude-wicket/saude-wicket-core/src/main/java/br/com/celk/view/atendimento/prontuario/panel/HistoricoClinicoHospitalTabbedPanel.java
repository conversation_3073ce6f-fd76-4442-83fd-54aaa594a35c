package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
public class HistoricoClinicoHospitalTabbedPanel extends CadastroTabbedPanel<NoHistoricoClinicoDTO> {

    public HistoricoClinicoHospitalTabbedPanel(String id, NoHistoricoClinicoDTO object, boolean viewOnly, List<ITab> tabs, boolean buttonBackVisible) {
        super(id, object, viewOnly, tabs, buttonBackVisible);
    }
    
    public HistoricoClinicoHospitalTabbedPanel(String id, NoHistoricoClinicoDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public HistoricoClinicoHospitalTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<NoHistoricoClinicoDTO> getReferenceClass() {
        return NoHistoricoClinicoDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }
}
