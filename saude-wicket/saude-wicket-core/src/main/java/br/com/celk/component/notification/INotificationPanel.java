package br.com.celk.component.notification;

import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface INotificationPanel {
    
    public void info(AjaxRequestTarget target, String message);
    
    public void warn(AjaxRequestTarget target, String message);

    public void error(AjaxRequestTarget target, String message);
    
    public void message(AjaxRequestTarget target, String message, int lvl);
    
    public void clearNotifications(AjaxRequestTarget target);

    public void updateNotificationPanel(AjaxRequestTarget target);

    public void updateNotificationPanel(AjaxRequestTarget target, boolean scrollToTop);

}
