package br.com.celk.component.link;

import br.com.celk.component.link.behavior.FormSubmitBehavior;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.IFormSubmittingComponent;

/**
 *
 * <AUTHOR>
 */
public abstract class AbstractAjaxFormSubmittingLink<T extends Component> extends AjaxLink<T> implements IFormSubmittingComponent {

    private Form form;
    private boolean defaultFormProcessing = true;

    public AbstractAjaxFormSubmittingLink(String id, Form form) {
        super(id);
        this.form = form;
    }

    @Override
    protected void disableLink(ComponentTag tag) {
        String clazz = (String) tag.getAttributes().get("class");

        if (clazz == null) {
            clazz = "";
        }

        clazz += " disabled";

        tag.getAttributes().put("class", clazz);

        super.disableLink(tag);
    }

    @Override
    protected AjaxEventBehavior newAjaxEventBehavior(String event) {
        return new FormSubmitBehavior(form);
    }

    @Override
    public void onClick(AjaxRequestTarget target) {
        try {
            onAction(target);
        } catch (ValidacaoException ex) {
//            TemplatePage findParent = findParent(TemplatePage.class);
//            findParent.error(ex.getMessage());
//            if(!updateNotificationPanel){
//                findParent.updateNotificationPanel(target);
//                target.appendJavaScript(JScript.scrollToTop());
//            }
            MessageUtil.modalWarn(target, this, ex);
        } catch (DAOException ex) {
//            TemplatePage findParent = findParent(TemplatePage.class);
//            findParent.fatal(ex.getMessage());
//            if(!updateNotificationPanel){
//                findParent.updateNotificationPanel(target);
//                target.appendJavaScript(JScript.scrollToTop());
//            }
            MessageUtil.modalError(target, this, ex);
        } finally {
//            if(updateNotificationPanel){
//                updateParent(target);
//            }
            update(target);
        }
    }

    private void update(AjaxRequestTarget target) {
        INotificationPanel findParent = findParent(INotificationPanel.class);

        if (findParent != null) {
            findParent.updateNotificationPanel(target, false);
            target.appendJavaScript(JScript.initMasks());
            target.appendJavaScript(JScript.initTextAreaLimit());
            target.appendJavaScript(JScript.removeEnterSubmitFromForm());
            target.appendJavaScript(JScript.initExpandLinks());
        }
    }

    public abstract void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public Component setDefaultFormProcessing(boolean defaultFormProcessing) {
        this.defaultFormProcessing = defaultFormProcessing;
        return this;
    }

    @Override
    public String getInputName() {
        String inputName = Form.getRootFormRelativeId(this);
        return inputName;
    }

    @Override
    public Form<?> getForm() {
        return form;
    }

    @Override
    public boolean getDefaultFormProcessing() {
        return defaultFormProcessing;
    }

    @Override
    public void onSubmit() {
    }
    
    @Override
    public void onAfterSubmit() {
    }

    @Override
    public void onError() {
    }
}
