package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarAtendimento extends Window{ 

    private PnlConfirmarAtendimento pnlConfirmarAtendimento;
    
    public DlgConfirmarAtendimento(String id) {
        super(id);
        init();
    }

    private void init(){
        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("confirmarAtendimento");
            }
        });
        
        setInitialWidth(510);
        setInitialHeight(250);
        
        setResizable(false);
        
        setContent(pnlConfirmarAtendimento = new PnlConfirmarAtendimento(getContentId()) {
            @Override
            public void onOk(AjaxRequestTarget target, Atendimento atendimento, Profissional profissional) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmarAtendimento.this.onOk(target, atendimento, profissional);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });
        
        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }
    
    public abstract void onOk(AjaxRequestTarget target, Atendimento atendimento, Profissional profissional) throws ValidacaoException, DAOException;
    
    public void onFechar(AjaxRequestTarget target) {}
    
    public void show(AjaxRequestTarget target, Atendimento atendimento, Profissional profissional){
        pnlConfirmarAtendimento.setAtendimento(atendimento);
        pnlConfirmarAtendimento.setProfissional(profissional);
        show(target);
    }
    
    private void fechar(AjaxRequestTarget target){
        DlgConfirmarAtendimento.this.onFechar(target);
        close(target);
    }
}
