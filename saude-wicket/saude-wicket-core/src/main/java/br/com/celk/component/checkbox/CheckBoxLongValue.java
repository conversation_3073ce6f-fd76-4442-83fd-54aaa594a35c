package br.com.celk.component.checkbox;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.IComponent;
import br.com.celk.component.temp.interfaces.ITempBehaviorStrategy;
import br.com.celk.component.temp.interfaces.ITempStoreComponent;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import java.util.Locale;

import org.apache.wicket.ajax.AjaxChannel;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.IAjaxIndicatorAware;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.ajax.markup.html.AjaxIndicatorAppender;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.IOnChangeListener;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.convert.IConverter;

/**
 * <AUTHOR>
 */
public class CheckBoxLongValue extends FormComponent<Long> implements IComponent<Long>, ITempStoreComponent, IAjaxIndicatorAware {
    private final Long longValue;

    private ITempBehaviorStrategy tempBehaviorStrategy;
    private AjaxIndicatorAppender indicatorAppender;

    public CheckBoxLongValue(String id) {
        this(id, RepositoryComponentDefault.SIM_LONG, null);
    }

    public CheckBoxLongValue(String id, Long value) {
        this(id, value, null);
    }

    public CheckBoxLongValue(String id, Long value, IModel<Long> model) {
        super(id, model);
        setType(String.class);
        this.longValue = value;

        setOutputMarkupId(true);

        add(new AjaxFormComponentUpdatingBehavior("onclick") {

            private static final long serialVersionUID = 1L;

            @Override
            protected AjaxChannel getChannel() {
                return CheckBoxLongValue.this.getChannel();
            }

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                CheckBoxLongValue.this.onUpdate(target);
            }
        });
    }

    @Override
    public Long getComponentValue() {
        return getModelObject();
    }

    @Override
    public void setComponentValue(Long value) {
        setModelObject(value);
    }

    @Override
    public void addAjaxUpdateValue() {
        add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                setModelObject(getModelObject());
            }
        });
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        setComponentValue(null);
        clearInput();
        target.add(this);
    }

    /**
     * @see org.apache.wicket.markup.html.form.IOnChangeListener#onSelectionChanged()
     */
    public void onSelectionChanged() {
        convertInput();
        updateModel();
        onSelectionChanged(getModelObject());
    }

    /**
     * Template method that can be overridden by clients that implement
     * IOnChangeListener to be notified by onChange events of a select element.
     * This method does nothing by default. <p> Called when a option is selected
     * of a dropdown list that wants to be notified of this event. This method
     * is to be implemented by clients that want to be notified of selection
     * events.
     *
     * @param newSelection The newly selected object of the backing model NOTE
     *                     this is the same as you would get by calling getModelObject() if the new
     *                     selection were current
     */
    protected void onSelectionChanged(Long newSelection) {
    }

    /**
     * Whether this component's onSelectionChanged event handler should called
     * using javascript if the selection changes. If true, a roundtrip will be
     * generated with each selection change, resulting in the model being
     * updated (of just this component) and onSelectionChanged being called.
     * This method returns false by default.
     *
     * @return True if this component's onSelectionChanged event handler should
     * called using javascript if the selection changes
     */
    protected boolean wantOnSelectionChangedNotifications() {
        return false;
    }

    /**
     * @see org.apache.wicket.MarkupContainer#getStatelessHint()
     */
    @Override
    protected boolean getStatelessHint() {
        if (wantOnSelectionChangedNotifications()) {
            return false;
        }
        return super.getStatelessHint();
    }

    /**
     * Processes the component tag.
     *
     * @param tag Tag to modify
     * @see org.apache.wicket.Component#onComponentTag(ComponentTag)
     */
    @Override
    protected void onComponentTag(final ComponentTag tag) {
        checkComponentTag(tag, "input");
        checkComponentTagAttribute(tag, "type", "checkbox");

        final String value = getValue();
        final IConverter converter = getConverter(String.class);
        final Long checked = (Long) converter.convertToObject(value, getLocale());

        if (longValue.equals(checked)) {
            tag.put("checked", "checked");
        } else {
            // In case the attribute was added at design time
            tag.remove("checked");
        }

        // remove value attribute, because it overrides the browser's submitted value, eg a [input
        // type="checkbox" value=""] will always submit as false
        tag.remove("value");

        // Should a roundtrip be made (have onSelectionChanged called) when the
        // checkbox is clicked?
        if (wantOnSelectionChangedNotifications()) {
            CharSequence url = urlFor(IOnChangeListener.INTERFACE, new PageParameters());

            Form<?> form = findParent(Form.class);
            if (form != null) {
                tag.put("onclick", form.getJsForInterfaceUrl(url));
            } else {
                // NOTE: do not encode the url as that would give invalid
                // JavaScript
                tag.put("onclick", "window.location.href='" + url
                        + (url.toString().indexOf('?') > -1 ? "&" : "?") + getInputName()
                        + "=' + this.checked;");
            }

        }

        super.onComponentTag(tag);
    }

    public Long getLongValue() {
        return longValue;
    }

    /**
     * Final because we made {@link #convertInput()} final and it no longer
     * delegates to
     * {@link #getConverter(Class)}
     *
     * @see org.apache.wicket.Component#getConverter(java.lang.Class)
     */
    @Override
    public final <C> IConverter<C> getConverter(Class<C> type) {
        if (String.class.equals(type)) {
            @SuppressWarnings("unchecked")
            IConverter<C> converter = (IConverter<C>) new CheckBoxConverter();
            return converter;
        } else {
            return super.getConverter(type);
        }
    }

    protected AjaxChannel getChannel() {
        return null;
    }

    protected void onUpdate(AjaxRequestTarget target) {
    }

    @Override
    public ITempBehaviorStrategy getTempBehaviorStrategy() {
        return tempBehaviorStrategy;
    }

    @Override
    public void setTempBehaviorStrategy(ITempBehaviorStrategy tempBehaviorStrategy) {
        this.tempBehaviorStrategy = tempBehaviorStrategy;
    }

    public CheckBoxLongValue addBusyIndicator() {
        indicatorAppender = new AjaxIndicatorAppender();
        add(indicatorAppender);
        return this;
    }

    @Override
    public String getAjaxIndicatorMarkupId() {
        if (indicatorAppender != null) {
            return indicatorAppender.getMarkupId();
        } else {
            return "";
        }
    }

    /**
     * Converter specific to the check box
     *
     * <AUTHOR>
     */
    private class CheckBoxConverter implements IConverter<Long> {

        @Override
        public String convertToString(Long value, Locale locale) {
            return value.toString();
        }

        @Override
        public Long convertToObject(String string, Locale locale) {
            if ("on".equals(string) || "true".equals(string)
                    || RepositoryComponentDefault.SIM.equals(string)
                    || RepositoryComponentDefault.SIM_LONG.toString().equals(string)
                    || longValue.toString().equals(string)) {
                return CheckBoxLongValue.this.getLongValue();
            } else {
                return 0L;
            }
        }
    }
}
