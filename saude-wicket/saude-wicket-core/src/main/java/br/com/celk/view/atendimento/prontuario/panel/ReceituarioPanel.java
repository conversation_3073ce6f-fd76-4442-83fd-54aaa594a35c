package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalHelper;
import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalService;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.autenticacao.TokenGenerator;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.AssinaturaDigitalBry;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.provedor.PscService;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.link.AjaxActionMultiReportLink;
import br.com.celk.component.table.CustomColorMultiSelectionTableRow;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.TableColorEnum;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringContainsIgnoreCase;
import br.com.celk.view.atendimento.prontuario.dialog.assinaturadigital.DlgInformarSenhaCertificado;
import br.com.celk.view.atendimento.prontuario.panel.receituario.EdicaoMedicamentoPacientePanel;
import br.com.celk.view.atendimento.prontuario.panel.receituario.view.CadastroReceituarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtil;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.receituario.NoReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioMedicamentoNaoPadronizadoDTO;
import br.com.ksisolucoes.bo.prontuario.web.historico.dto.MedicamentosEmUsoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.export.PdfUtil;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;
import br.com.ksisolucoes.vo.produto.ProdutoCidDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import com.lowagie.text.DocumentException;
import org.apache.commons.collections.ComparatorUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.*;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.select;

/**
 * <AUTHOR>
 */
public class ReceituarioPanel extends ProntuarioCadastroPanel {

    private SelectionTable<Receituario> tblReceituarios;
    private Table tblItens;
    private MultiSelectionTable<ReceituarioItem> tblHistorico;
    private Integer periodo;
    private DatePeriod datePeriod;
    private TipoReceita tipoReceita;
    private DropDown dropDownTipoReceita;

    private List<Receituario> receituarios;
    private MultiSelectionTable tblMedicamentos;
    private List<MedicamentosEmUsoDTO> lstHistoricoMedicamento;
    private List<MedicamentosEmUsoDTO> lstHistoricoMedicamentoAux;
    private WebMarkupContainer containerHistorico;
    private WebMarkupContainer containerMedicamento;
    private boolean origemPainelSoap;
    private boolean exibeDlg;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;
    private DlgInformarSenhaCertificado dlgSolicitacaoSenha;
    private String separaReceituarioAutomaticamente;

    public ReceituarioPanel(String id) {
        super(id, bundle("receituario"));
    }

    public ReceituarioPanel(String id, boolean origemPainelSoap, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("receituario"));
        this.origemPainelSoap = origemPainelSoap;
        this.containerTelaSoap = containerTelaSoap;
    }

    public ReceituarioPanel(String id, boolean origemPainelSoap, boolean exibeDlg, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("receituario"));
        this.origemPainelSoap = origemPainelSoap;
        this.exibeDlg = exibeDlg;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");

        form.add(new AbstractAjaxButton("btnNovaPrescricao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                NoReceituarioDTO noReceituarioDTO = new NoReceituarioDTO();
                noReceituarioDTO.setReceituario(new Receituario());
                noReceituarioDTO.setAtendimento(getAtendimento());
                noReceituarioDTO.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
//
                CadastroReceituarioPanel cadastroReceituarioPanel = new CadastroReceituarioPanel(getProntuarioController().panelId(), noReceituarioDTO, false, false, origemPainelSoap, containerTelaSoap);
                getProntuarioController().changePanel(target, cadastroReceituarioPanel);
            }

        });

        form.add(tblReceituarios = new SelectionTable("tblReceituarios", getColumnsReceituario(), getCollectionProviderReceituario()));
        tblReceituarios.populate();
        tblReceituarios.addSelectionAction(new ISelectionAction() {
            @Override
            public void onSelection(AjaxRequestTarget target, Serializable object) {
                tblItens.populate(target);
            }
        });

        form.add(tblItens = new Table("tblItens", getColumnsItens(), getCollectionProviderItens()));

        form.add(containerHistorico = new WebMarkupContainer("containerHistorico"));
        containerHistorico.add(tblHistorico = new MyCustomTable("tblHistorico", getColumnsHistorico(), getCollectionProviderHistorico()));
        containerHistorico.add(getDropDownFiltroHistorico());
        containerHistorico.add(new AbstractAjaxButton("btnCopiarMedicamentos") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                copiarMedicamentosSelecionados(target);
            }
        });

        tblHistorico.setScrollX("1050px");
        tblHistorico.populate();

        form.add(containerMedicamento = new WebMarkupContainer("containerMedicamento"));
        containerMedicamento.add(new AbstractAjaxButton("btnNovoMedicamento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new CadastroNovoMedicamentoEmUsoPanel(getProntuarioController().panelId(), origemPainelSoap, containerTelaSoap));
            }
        });

        containerMedicamento.add(tblMedicamentos = new MultiSelectionTable("tblMedicamentos", getColumnsMedicamentos(), getCollectionProviderMedicamentos()));
        containerMedicamento.add(new AbstractAjaxButton("btnGerarReceitas") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                copiarMedicamentosPacienteSelecionados(target);
            }
        });

        tblMedicamentos.setScrollX("1050px");
        tblMedicamentos.populate();
        containerMedicamento.add(dropDownTipoReceita = getDropDownTipoReceita("tipoReceita"));

        datePeriod = new DatePeriod(Data.removeMeses(DataUtil.getDataAtual(), 12), DataUtil.getDataAtual());

        form.add(new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())) {
                    getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                } else if (RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())) {
                    getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), SoapDTO.ContainerTelaSoap.CONTAINER_BOTOES));
                }
            }
        }.setDefaultFormProcessing(false).setVisible(origemPainelSoap));

        add(form);
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
        try {
            separaReceituarioAutomaticamente = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("separaReceituarioAutomaticamente");
            consultarReceituarios();
            consultarMedicamentos();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (CollectionUtils.isNotNullEmpty(receituarios)) {
            tblReceituarios.setSelectedObject(receituarios.get(0));
            tblItens.populate();
        }

    }

    private DropDown getDropDownTipoReceita(String id) {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown(id);

            LoadManager load = LoadManager.getInstance(TipoReceita.class);
            List<String> receitas = Arrays.asList(TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_AZUL, TipoReceita.RECEITA_AMARELA);
            load.addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, BuilderQueryCustom.QueryParameter.IN, receitas));

            List<TipoReceita> tipoReceitaList = load.start().getList();
            dropDownTipoReceita.addChoice(null, bundle("todas"));

            if (CollectionUtils.isNotNullEmpty(tipoReceitaList)) {
                for (TipoReceita tr : tipoReceitaList) {
                    dropDownTipoReceita.addChoice(tr, tr.getDescricao());
                }
            }

            dropDownTipoReceita.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    lstHistoricoMedicamentoAux.clear();
                    TipoReceita tr = (TipoReceita) dropDownTipoReceita.getComponentValue();

                    if (tr != null) {
                        lstHistoricoMedicamentoAux = select(lstHistoricoMedicamento, Lambda.having(on(MedicamentosEmUsoDTO.class).getDescricaoTipoReceita(), StringContainsIgnoreCase.containsStringIgnoreCase(tr.getDescricao())));
                    } else {
                        lstHistoricoMedicamentoAux = new ArrayList<>();
                        lstHistoricoMedicamentoAux.addAll(lstHistoricoMedicamento);
                    }

                    tblMedicamentos.update(target);
                }
            });
        }

        return dropDownTipoReceita;
    }

    private void atualizarTblReceituarios(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        consultarReceituarios();
        consultarMedicamentos();
        tblReceituarios.update(target);
        if (CollectionUtils.isNotNullEmpty(receituarios)) {
            tblReceituarios.setSelectedObject(receituarios.get(0));
            tblItens.populate(target);
        } else {
            tblItens.limpar(target);
        }
        tblMedicamentos.update(target);
    }

    private void clonarReceituarioSelecionado(AjaxRequestTarget target, ReceituarioItem receituarioItem) throws DAOException, ValidacaoException {
        ReceituarioItem proxy = on(ReceituarioItem.class);

        List<ReceituarioItem> itensSelecionados = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Receituario.class, path(proxy.getReceituario())).getProperties()).addProperty(path(proxy.getReceituario().getAtendimento().getCidPrincipal().getCodigo()))
                .addProperty(path(proxy.getReceituario().getTipoReceita().getDescricao())).addProperty(path(proxy.getReceituario().getTipoReceita().getTipoReceita()))
                .addProperty(path(proxy.getProduto().getUnidade().getCodigo())).addProperty(path(proxy.getProduto().getUnidade().getDescricao())).
                addProperty(path(proxy.getProduto().getUsoContinuo())).addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo()))
                .addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()))
                .addProperty(path(proxy.getReceituario().getProfissional().getNome()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getCodigo()), receituarioItem.getReceituario().getCodigo()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(itensSelecionados)) {
            BOFactoryWicket.getBO(ReceituarioFacade.class).copiarMedicamentosSelecionadosReceituarios(receituarioItem, getAtendimento(), itensSelecionados);
        }

        atualizarTblReceituarios(target);
        tblHistorico.clearSelection(target);
    }

    private void copiarMedicamentosSelecionados(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<ReceituarioItem> itensSelecionados = tblHistorico.getSelectedObjects();

        boolean permiteAdicionarMecidamentoInativoReceita = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("permiteAdicionarMecidamentoInativoReceita"));

        if (!permiteAdicionarMecidamentoInativoReceita) {
            if (CollectionUtils.isNotNullEmpty(itensSelecionados)) {
                String medicamentosInativos = "";

                for (ReceituarioItem receituarioItem : itensSelecionados) {
                    if (receituarioItem.getProduto() != null &&
                            RepositoryComponentDefault.INATIVO.equals(receituarioItem.getProduto().getFlagAtivo())) {

                        //Inclui separador para quando tem mais de um produto.
                        if (!medicamentosInativos.isEmpty()) {
                            medicamentosInativos += ", ";
                        }
                        if (receituarioItem.getProduto().getDescricao() != null) {
                            medicamentosInativos += receituarioItem.getProduto().getDescricao();
                        } else {
                            medicamentosInativos += receituarioItem.getNomeProduto();
                        }
                    }
                }

                if (!medicamentosInativos.isEmpty()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_copiar_medicamentos_inativos", medicamentosInativos));
                }
            }
        }
        BOFactoryWicket.getBO(ReceituarioFacade.class).copiarMedicamentosSelecionadosReceituarios(null, getAtendimento(), itensSelecionados);
        atualizarTblReceituarios(target);
        tblHistorico.clearSelection(target);
    }

    private void copiarMedicamentosPacienteSelecionados(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<Long> codigosMedicamentos = new ArrayList<Long>();
        List<MedicamentosEmUsoDTO> medicamentosSelecionados = (List<MedicamentosEmUsoDTO>) tblMedicamentos.getSelectedObjects();
        String medicamentosInativos = "";

        verificarQuantidadeMaximaMedicamento(medicamentosSelecionados);

        boolean permiteAdicionarMecidamentoInativoReceita = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("permiteAdicionarMecidamentoInativoReceita"));

        if (!permiteAdicionarMecidamentoInativoReceita) {
            for (MedicamentosEmUsoDTO medicamento : medicamentosSelecionados) {
                codigosMedicamentos.add(medicamento.getCodigoMedicamentoPaciente());

                if (medicamento.getProduto() != null &&
                        RepositoryComponentDefault.INATIVO.equals(medicamento.getProduto().getFlagAtivo())) {

                    //Inclui separador para quando tem mais de um produto.
                    if (!medicamentosInativos.isEmpty()) {
                        medicamentosInativos += ", ";
                    }

                    if (medicamento.getProduto().getDescricao() != null) {
                        medicamentosInativos += medicamento.getProduto().getDescricao();
                    } else {
                        medicamentosInativos += medicamento.getNomeProduto();
                    }
                }
            }
        }

        if (!medicamentosInativos.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_copiar_medicamentos_inativos", medicamentosInativos));
        }

        if (CollectionUtils.isNotNullEmpty(codigosMedicamentos)) {
            BOFactoryWicket.getBO(ReceituarioFacade.class).confirmaEmissaoReceituario(getAtendimento().getCodigo(), codigosMedicamentos, getAtendimento().getProfissional().getCodigo(), true);
        }

        atualizarTblReceituarios(target);
        tblMedicamentos.clearSelection(target);
    }

    private void verificarQuantidadeMaximaMedicamento(List<MedicamentosEmUsoDTO> medicamentosSelecionados) throws ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(medicamentosSelecionados)) {
            int count = 0;
            for (MedicamentosEmUsoDTO medicamento : medicamentosSelecionados) {
                if (TipoReceita.RECEITA_BRANCA.equals(medicamento.getTipoReceitaSelecionada())) {
                    count++;
                    if (count > 3) {
                        throw new ValidacaoException(bundle("msgPortariaSvsMs344"));
                    }
                }
            }
        }
    }

    private DropDown getDropDownFiltroHistorico() {
        DropDown dropDown = new DropDown("filtroHistorico", new PropertyModel(this, "periodo"));

        dropDown.addChoice(12, BundleManager.getString("ultimoAno", this));
        dropDown.addChoice(3, BundleManager.getString("ultimos3meses", this));
        dropDown.addChoice(6, BundleManager.getString("ultimos6meses", this));
        dropDown.addChoice(999, BundleManager.getString("todos", this));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (periodo != 999) {
                    datePeriod = new DatePeriod(Data.removeMeses(DataUtil.getDataAtual(), periodo), DataUtil.getDataAtual());
                } else {
                    datePeriod = null;
                }
                tblHistorico.populate(target);
            }
        });
        return dropDown;
    }

    //<editor-fold defaultstate="collapsed" desc="TABELA DE HISTORICO">
    private IColumn getColumnsActionHistorico() {
        return new MultipleActionCustomColumn<ReceituarioItem>() {
            @Override
            public void customizeColumn(ReceituarioItem rowObject) {
                if (rowObject.getProduto() == null || RepositoryComponentDefault.SIM_LONG.equals(rowObject.getProduto().getFlagAtivo())) {
                    addAction(ActionType.CLONAR, rowObject, new IModelAction<ReceituarioItem>() {
                        @Override
                        public void action(AjaxRequestTarget target, ReceituarioItem modelObject) throws ValidacaoException, DAOException {
                            clonarReceituarioSelecionado(target, modelObject);
                        }
                    }).setTitleBundleKey("copiarReceita");
                }
            }
        };
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ReceituarioItem proxy = on(ReceituarioItem.class);

        columns.add(getColumnsActionHistorico());
        columns.add(createColumn(bundle("data"), proxy.getReceituario().getDataCadastro()));
        columns.add(createColumn(bundle("medicamento"), proxy.getNomeProduto()));
        columns.add(createColumn(bundle("posologia"), proxy.getPosologia()));
        columns.add(createColumn(bundle("cid"), proxy.getReceituario().getCidReceituarioAtendimento().getCodigo()));
        columns.add(createColumn(bundle("tipoAtendimento"), proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getReceituario().getProfissional().getNome()));
        columns.add(createColumn(bundle("prescrito"), proxy.getQuantidadePrescrita()));
        columns.add(createColumn(bundle("formaFarmaceutica"), proxy.getProduto().getDescricaoFarmaceuticaFormatada()));
        columns.add(createColumn(bundle("qtdDispensada"), proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada()));
        columns.add(createColumn(bundle("inicioTratamento"), proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getDataDispensacao()));
        columns.add(createColumn(bundle("fimPrevistoTratamento"), proxy.getDispensacaoMedicamentoItem().getDataProximaDispensacao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                ReceituarioItem proxy = on(ReceituarioItem.class);
                List<ReceituarioItem> itens = LoadManager.getInstance(ReceituarioItem.class)
                        .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                        .addProperties(new HQLProperties(Receituario.class, path(proxy.getReceituario())).getProperties())
                        .addProperty(path(proxy.getReceituario().getAtendimento().getCidPrincipal().getCodigo()))
                        .addProperty(path(proxy.getReceituario().getTipoReceita().getDescricao()))
                        .addProperty(path(proxy.getProduto().getUnidade().getCodigo()))
                        .addProperty(path(proxy.getProduto().getFlagAtivo()))
                        .addProperty(path(proxy.getProduto().getUnidade().getDescricao())).addProperty(path(proxy.getReceituario().getTipoReceita().getTipoReceita()))
                        .addProperty(path(proxy.getProduto().getUsoContinuo())).addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo()))
                        .addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()))
                        .addProperty(path(proxy.getReceituario().getProfissional().getNome())).addProperty(path(proxy.getProduto().getFormaFarmaceutica()))
                        .addProperty(path(proxy.getDispensacaoMedicamentoItem().getCodigo())).addProperty(path(proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada()))
                        .addProperty(path(proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getCodigo()))
                        .addProperty(path(proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getDataDispensacao()))
                        .addProperty(path(proxy.getDispensacaoMedicamentoItem().getDataProximaDispensacao()))
                        .addProperty(path(proxy.getDiasTratamento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getAtendimento()), BuilderQueryCustom.QueryParameter.DIFERENTE, getAtendimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getTipoReceita().getTipoReceita()), BuilderQueryCustom.QueryParameter.NOT_IN, Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getDataCadastro()), datePeriod))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getUsuarioCadsus()), getAtendimento().getUsuarioCadsus()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getFlagLivre()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.SIM_LONG, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_LONG))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getReceituario().getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getNomeProduto()))).start().getList();

                return itens;
            }
        };
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="TABELA ITENS">
    private ICollectionProvider getCollectionProviderItens() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                Collection retorno = new ArrayList();
                Receituario receituario = tblReceituarios.getSelectedObject();
                if (receituario != null) {
                    retorno = LoadManager.getInstance(ReceituarioItem.class)
                            .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                            .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_FLAG_EMITE_LME))
                            .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, receituario))
                            .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                            .addSorter(new QueryCustom.QueryCustomSorter(ReceituarioItem.PROP_NOME_PRODUTO))
                            .start().getList();
                }
                return retorno;
            }
        };
    }

    private List<IColumn> getColumnsItens() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ReceituarioItem proxy = on(ReceituarioItem.class);

        columns.add(getActionColumnDocumentos());
        columns.add(createColumn(bundle("medicamento"), proxy.getNomeProduto()));
        columns.add(createColumn(bundle("posologia"), proxy.getPosologia()));
        columns.add(createColumn(bundle("prescrito"), proxy.getQuantidadePrescrita()));

        return columns;
    }

    private IColumn getActionColumnDocumentos() {
        return new MultipleActionCustomColumn<ReceituarioItem>() {

            @Override
            public void customizeColumn(ReceituarioItem rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new AjaxActionMultiReportLink<ReceituarioItem>() {
                    @Override
                    public List<DataReport> getDataReports(AjaxRequestTarget target, ReceituarioItem modelObject) throws ValidacaoException, DAOException, ReportException {
                        return impressaoDocumentos(target, modelObject);
                    }
                }).setTitleBundleKey("imprimirDocumentos").setVisible(existsDocumentos(rowObject));
            }
        };
    }
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="TABELA RECEITUARIOS">
    private List<IColumn> getColumnsReceituario() {
        List<IColumn> columns = new ArrayList<IColumn>();
        Receituario proxy = on(Receituario.class);

        columns.add(getActionColumnReceituario());
        columns.add(createColumn(bundle("tipoReceita"), proxy.getTipoReceita().getDescricao()));

        return columns;
    }

    private IColumn getActionColumnReceituario() {
        return new MultipleActionCustomColumn<Receituario>() {

            @Override
            public void customizeColumn(Receituario rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Receituario>() {

                    @Override
                    public void action(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException {
//                        receituarioController.iniciarPrescricao(target, modelObject.getTipoReceita(), modelObject);
                        NoReceituarioDTO noReceituarioDTO = new NoReceituarioDTO();
                        if (modelObject != null) {
                            if (modelObject.getDocumentoAssinado() != null) {
                                DocumentoAssinado da = new AssinaturaDigitalBry().carregarDocumentoAssinado(modelObject.getDocumentoAssinado().getCodigo());
                                modelObject.setDocumentoAssinado(da);
                            }
                            noReceituarioDTO.setReceituario(modelObject);
                            noReceituarioDTO.setAtendimento(getAtendimento());
                            noReceituarioDTO.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
                            noReceituarioDTO.setReceituarioItemDTOList(carregarItensReceituario(modelObject));
                            noReceituarioDTO.setEdicao(true);
                        }
                        CadastroReceituarioPanel cadastroReceituarioPanel = new CadastroReceituarioPanel(getProntuarioController().panelId(), noReceituarioDTO, false, false, origemPainelSoap, containerTelaSoap);
                        getProntuarioController().changePanel(target, cadastroReceituarioPanel);
                    }
                });
                addAction(ActionType.IMPRIMIR, rowObject, new AjaxActionMultiReportLink<Receituario>() {
                    @Override
                    public List<DataReport> getDataReports(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException, ReportException {
                        if (rowObject.getDocumentoAssinado() != null) {
                            DocumentoAssinado da = getDocumentoAssinado(rowObject);
                            if (da != null && RepositoryComponentDefault.SIM_LONG.equals(da.getFlagAssinado())) {
                                imprimirDocumentoAssinado(rowObject, target);
                            } else {
                                return impressaoReceita(modelObject);
                            }
                        } else {
                            return impressaoReceita(modelObject);
                        }
                        return Collections.emptyList();
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Receituario>() {

                    @Override
                    public void action(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ReceituarioFacade.class).excluirReceituario(modelObject, false);
                        atualizarTblReceituarios(target);
                    }
                });

                AbstractAssinaturaDigitalHelper assinaturaDigitalHelper = null;
                AbstractAssinaturaDigitalService service = new AbstractAssinaturaDigitalService();
                try {
                    assinaturaDigitalHelper = service.carregarProvedorServico();
                } catch (ValidacaoException e) {
                    throw new RuntimeException(e);
                }
                AbstractAssinaturaDigitalHelper finalAssinaturaDigitalHelper = assinaturaDigitalHelper;
                addAction(ActionType.ASSINAR_DIGITALMENTE, rowObject, new IModelAction<Receituario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Receituario modelObject) throws ValidacaoException, DAOException {
                        AssinaturaDigitalUtilDTO dto = new AssinaturaDigitalUtilDTO();
                        if (!TipoReceita.RECEITA_BRANCA.equals(rowObject.getTipoReceita().getTipoReceita())
                                && !TipoReceita.RECEITA_BASICA.equals(rowObject.getTipoReceita().getTipoReceita())) {
                            throw new ValidacaoException("Assinatura Digital disponivel apenas para receita branca ou básica");
                        }
                        dto.setAtendimento(getAtendimento());
                        dto.setHelper(finalAssinaturaDigitalHelper);
                        dto.setAjaxPreviewBlank(ajaxPreviewBlank);
                        dto.setDocumentoAssinado(modelObject.getDocumentoAssinado());
                        dto.setOrigemTipoDocumento(DocumentoAssinado.OrigemTipoDocumento.RECEITA);
                        dto.setReceituario(modelObject);
//                        dto.setTipoDocumento(modelObject.getTipoDocumentoAtendimento().getDescricao());
                        try {
                            Usuario usuario = AssinaturaDigitalUtil.loadUsuario();
                            if (Usuario.TipoCertificado.PFX.value().equals(AssinaturaDigitalUtil.loadUsuario().getTipoCertificado())) {
                                inserirSenhaCertificado(target, dto, modelObject);
                            } else {
                                DocumentoAssinado documentoAssinado = assinarSalvarDocumento(dto, modelObject, null);

                                PscService pscService = AssinaturaDigitalUtil.preparePsc(documentoAssinado, usuario);
                                target.appendJavaScript("setTimeout(\"window.open('" + pscService.getAuthUrl() + "','_blank')\", 100);");
                            }
                        } catch (Exception e) {
                            throw new ValidacaoException(e);
                        }
                    }
                }).setVisible(assinaturaDigitalHelper.isAssinaturaEnabled())
                        .setEnabled(assinaturaDigitalHelper.isAssinaturaEnabled() &&
                                !assinaturaDigitalHelper.isDocumentoAssinado(rowObject.getDocumentoAssinado()));
            }
        };
    }

    private static DocumentoAssinado getDocumentoAssinado(Receituario rowObject) throws ValidacaoException {
        AbstractAssinaturaDigitalService service = new AbstractAssinaturaDigitalService();
        AbstractAssinaturaDigitalHelper assinaturaDigitalHelper = service.carregarProvedorServico();
        return assinaturaDigitalHelper.carregarDocumentoAssinado(rowObject.getDocumentoAssinado().getCodigo());
    }

    public void imprimirDocumentoAssinado(Receituario rowObject, AjaxRequestTarget target) {
        AbstractAssinaturaDigitalHelper assinaturaDigitalHelper = null;
        AbstractAssinaturaDigitalService service = new AbstractAssinaturaDigitalService();

        try {
            assinaturaDigitalHelper = service.carregarProvedorServico();
        } catch (ValidacaoException e) {
            throw new RuntimeException("Erro ao carregar provedor de serviço de assinatura digital", e);
        }

        DocumentoAssinado documentoAssinado = assinaturaDigitalHelper.carregarDocumentoAssinado(
                rowObject.getDocumentoAssinado().getCodigo());

        if (RepositoryComponentDefault.SIM_LONG.equals(documentoAssinado.getFlagAssinado())) {
            try {
                File arquivoTemporario = File.createTempFile(UUID.randomUUID().toString(), ".pdf");
                FileUtils.buscarArquivoFtp(
                        documentoAssinado.getGerenciadorArquivoAssinado().getCaminho(),
                        arquivoTemporario.getAbsolutePath()
                );

                IResourceStream resourceStream = new FileResourceStream(
                        new org.apache.wicket.util.file.File(arquivoTemporario)
                );

                ajaxPreviewBlank.initiate(
                        target,
                        documentoAssinado.getGerenciadorArquivoAssinado().getNomeArquivo(),
                        resourceStream
                );
            } catch (IOException | ValidacaoException e) {
                Loggable.log.error("Erro ao processar documento assinado", e);
            }
        }
    }


    private void inserirSenhaCertificado(AjaxRequestTarget target, AssinaturaDigitalUtilDTO dto, Receituario modelObject) {
        getProntuarioController().addWindow(target, dlgSolicitacaoSenha = new DlgInformarSenhaCertificado(getProntuarioController().newWindowId(), dto) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                assinarSalvarDocumento(dto, modelObject, target);
            }
        });
        dlgSolicitacaoSenha.show(target);
    }

    private DocumentoAssinado assinarSalvarDocumento(AssinaturaDigitalUtilDTO dto, Receituario modelObject, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        try {
            modelObject.setToken(TokenGenerator.generateUniqueToken());
            DataReport dataReport = imprimirDocumento(modelObject, dto);
            AssinaturaDigitalUtil util = new AssinaturaDigitalUtil(dto, dataReport);
            DocumentoAssinado da = util.assinarDocumentoDataReport();
            modelObject.setDocumentoAssinado(da);
            BOFactory.save(modelObject);

            if (target != null)
                util.exibirDocumento(target);
            return da;
        } catch (ReportException e) {
            throw new ValidacaoException(e);
        }

    }

    public DataReport imprimirDocumento(Receituario object, AssinaturaDigitalUtilDTO dto) throws ReportException {
        ImpressaoReceituarioDTOParam param1 = new ImpressaoReceituarioDTOParam();
        param1.setCodigoReceituario(object.getCodigo());
        param1.setTipoReceita(object.getTipoReceita().getTipoReceita());
        param1.setAtendimento(object.getAtendimento());
        param1.setAssinadoDigitalmente(true);
        param1.setDataAssinatura(new Date());

        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param1);
    }

    public String configurarMensagem(Receituario object, ImpressaoReceituarioDTOParam param1) {
        Receituario receituarioAssinatura = getReceituario(object);

        String orgaoEmissorDescricao = receituarioAssinatura.getProfissional().getOrgaoEmissor() != null
                ? receituarioAssinatura.getProfissional().getOrgaoEmissor().getDescricaoFormatado()
                : " ";

       return Bundle.getStringApplication(
                "rotulo_msg_assinatura_digital",
                param1.getDataAssinatura(),
                object.getProfissional().getNome(),
                orgaoEmissorDescricao,
                object.getUsuarioCadsus().getNome(),
                object.getToken()
        );
    }

    private Receituario getReceituario(Receituario receituario) {
       return LoadManager.getInstance(Receituario.class)
                .addProperty(VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_ORGAO_EMISSOR, OrgaoEmissor.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Receituario.PROP_PROFISSIONAL, Profissional.PROP_ORGAO_EMISSOR, OrgaoEmissor.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_CODIGO, receituario.getCodigo()))
                .start().getVO();
    }


    private List<DataReport> impressaoReceita(Receituario receituario) throws ReportException {
        List<DataReport> lstDataReport = new ArrayList<>();
        List<ReceituarioItemDTO> itens = carregarItensReceituario(receituario);
        if (CollectionUtils.isNotNullEmpty(itens)) {
            LaudoMedicamentosEspeciais lme = null;
            for (ReceituarioItemDTO receituarioItem : itens) {
                if (receituarioItem.getReceituarioItem() != null && receituarioItem.getReceituarioItem().getProduto() != null && RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getReceituarioItem().getProduto().getFlagEmiteLme()) && receituarioItem.getReceituarioItem().getLaudoMedicamentoEspecial() != null) {
                    lme = LoadManager.getInstance(LaudoMedicamentosEspeciais.class).setId(receituarioItem.getReceituarioItem().getLaudoMedicamentoEspecial().getCodigo()).start().getVO();
                    DataReport report = BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioLaudoMedicamentosEspeciais(lme);
                    lstDataReport.add(report);
                }
            }
        }

        lstDataReport.addAll(imprimirPrescricao(receituario));
        return lstDataReport;
    }

    private List<DataReport> impressaoDocumentos(AjaxRequestTarget target, ReceituarioItem item) throws ValidacaoException {
        List<DataReport> lstDataReport = new ArrayList<>();
        List<ProdutoCidDocumento> produtoCidDocumentos = carregarDocumentos(item);
        if (CollectionUtils.isNotNullEmpty(produtoCidDocumentos)) {
            List<InputStream> ios = new ArrayList<>();
            File arquivoImpressao = null;
            try {
                for (ProdutoCidDocumento produtoCidDocumento : produtoCidDocumentos) {
                    File file = File.createTempFile("documentos", "lme");
                    FileUtils.buscarArquivoFtp(produtoCidDocumento.getGerenciadorArquivo().getCaminho(), file.getAbsolutePath());

                    if (file != null && FileUtils.isValidFile(file, FileUtils.MAGIC_NUMBER_PDF)) {
                        ios.add(new FileInputStream(file));
                    }
                }
                arquivoImpressao = PdfUtil.mergePdf(ios, "prontuario");
            } catch (IOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            } catch (DocumentException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
            if (arquivoImpressao != null) {
                IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(arquivoImpressao));
                ajaxPreviewBlank.initiate(target, "documentos_lme.pdf", resourceStream);
            }
        }
        return lstDataReport;
    }

    private ICollectionProvider getCollectionProviderReceituario() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return receituarios;
            }
        };
    }

    private List<Receituario> consultarReceituarios() throws DAOException, ValidacaoException {
        return receituarios = LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(TipoReceita.class, Receituario.PROP_TIPO_RECEITA).getProperties())
                .addProperties(new HQLProperties(DocumentoAssinado.class, Receituario.PROP_DOCUMENTO_ASSINADO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_ATENDIMENTO), getAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA), BuilderQueryCustom.QueryParameter.NOT_IN, Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)))
                .addInterceptor(new LoadInterceptorReceituarioItemLivre(false))
                .start().getList();
    }
    //</editor-fold>

    private List<IColumn> getColumnsMedicamentos() {
        List<IColumn> columns = new ArrayList<IColumn>();

        MedicamentosEmUsoDTO proxy = on(MedicamentosEmUsoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("medicamento", this), proxy.getNomeProduto()));
        columns.add(createColumn(bundle("posologia", this), proxy.getPosologia()));
        columns.add(createColumn(bundle("ultimaReceita", this), proxy.getDataUltimaReceita()));
        columns.add(createColumn(bundle("tipoReceita", this), proxy.getDescricaoTipoReceita()));
        columns.add(createColumn(bundle("formaFarmaceutica"), proxy.getProduto().getDescricaoFarmaceuticaFormatada()));
        columns.add(createColumn(bundle("qtdDispensada"), proxy.getDispensacaoMedicamentoItem().getQuantidadeDispensada()));
        columns.add(createColumn(bundle("inicioTratamento"), proxy.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getDataDispensacao()));
        columns.add(createColumn(bundle("fimPrevistoTratamento"), proxy.getDispensacaoMedicamentoItem().getDataProximaDispensacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<MedicamentosEmUsoDTO>() {
            @Override
            public void customizeColumn(MedicamentosEmUsoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<MedicamentosEmUsoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, MedicamentosEmUsoDTO modelObject) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new EdicaoMedicamentoPacientePanel(getProntuarioController().panelId(), modelObject.getCodigoMedicamentoPaciente(), origemPainelSoap, containerTelaSoap));
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<MedicamentosEmUsoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, MedicamentosEmUsoDTO modelObject) throws ValidacaoException, DAOException {
                        Long codigoMedicamentoPaciente = modelObject.getCodigoMedicamentoPaciente();
                        BOFactoryWicket.getBO(AtendimentoFacade.class).excluirMedicamentoPaciente(codigoMedicamentoPaciente);
                        consultarMedicamentos();
                        tblMedicamentos.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderMedicamentos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstHistoricoMedicamentoAux;
            }
        };
    }

    private List<MedicamentosEmUsoDTO> consultarMedicamentos() throws DAOException, ValidacaoException {
        lstHistoricoMedicamento = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarMedicamentosEmUsoPaciente(getAtendimento().getUsuarioCadsus().getCodigo(), getAtendimento().getTabelaCbo());
        MedicamentosEmUsoDTO medicamentosOn = on(MedicamentosEmUsoDTO.class);
        lstHistoricoMedicamento = Lambda.sort(lstHistoricoMedicamento, medicamentosOn, ComparatorUtils.reversedComparator(new ArgumentComparator(medicamentosOn.getDataUltimaReceita())));
        lstHistoricoMedicamentoAux = new ArrayList<>();
        lstHistoricoMedicamentoAux.addAll(lstHistoricoMedicamento);
        return lstHistoricoMedicamentoAux;
    }

    @Override
    public void changePanelAction(AjaxRequestTarget target) {
        if (exibeDlg) {
            MessageUtil.modalWarn(target, ReceituarioPanel.this, new ValidacaoException(Bundle.getStringApplication("msg_existe_medicamento_necessario_manualmente")));
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerMedicamento)));

    }

    public List<DataReport> imprimirPrescricao(Receituario receituario) throws ReportException {
        ImpressaoReceituarioDTOParam param = new ImpressaoReceituarioDTOParam();
        List<DataReport> lstDataReport = new ArrayList<>();
        param.setCodigoReceituario(receituario.getCodigo());
        param.setTipoReceita(receituario.getTipoReceita().getTipoReceita());
        if (exibeDadosPacienteIdentificacaoCompradorReceitaB()) {
            param.setExibeDadosPacienteIdentificacaoCompradorReceitaB(true);
        }
        if (exibeDadosPacienteIdentificadoMenor18anos()) {
            param.setExibedadospacientedidentificadomenor18anos(true);
        }
        param.setAtendimento(getAtendimento());
        lstDataReport.add(BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param));

        List<ReceituarioItem> receituarioItemList = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO)).getProperties())
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Receituario.class, VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO)).getProperties())
                .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO), receituario))
                .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_CID, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ReceituarioItem.PROP_CODIGO)))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(receituarioItemList)) {
            lstDataReport.add(BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoReceituarioCidInformado(receituarioItemList));
        }

        return lstDataReport;
    }

    private boolean exibeDadosPacienteIdentificacaoCompradorReceitaB() {
        boolean exibeDadosPacienteIdentificacaoCompradorReceitaB = false;
        try {
            exibeDadosPacienteIdentificacaoCompradorReceitaB = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeDadosPacienteIdentificacaoCompradorReceitaB"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        return exibeDadosPacienteIdentificacaoCompradorReceitaB;
    }
    private boolean exibeDadosPacienteIdentificadoMenor18anos() {
        boolean exibeDadosPacienteIdentificadoMenor18anos = false;
        UsuarioCadsus usuarioCadsus = null;
        for (Receituario receituario : receituarios) {
            usuarioCadsus = receituario.getUsuarioCadsus();
        }
        try {
            exibeDadosPacienteIdentificadoMenor18anos = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeDadosPacienteIdentificadoMenor18anos"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        if (usuarioCadsus != null && usuarioCadsus.getDataNascimento() == null) {
            usuarioCadsus = carregarDataNascimentoUsuarioCadsus(usuarioCadsus);
        }
        return exibeDadosPacienteIdentificadoMenor18anos || usuarioCadsus.getIdade() >= 18L;
    }

    private List<ProdutoCidDocumento> carregarDocumentos(ReceituarioItem receituarioItem) {
        List<ProdutoCidDocumento> cidDocumentoList = new ArrayList<>();
        if (receituarioItem.getProduto() != null) {
            cidDocumentoList = LoadManager.getInstance(ProdutoCidDocumento.class)
                    .addProperties(new HQLProperties(ProdutoCidDocumento.class).getProperties())
                    .addProperties(new HQLProperties(GerenciadorArquivo.class, ProdutoCidDocumento.PROP_GERENCIADOR_ARQUIVO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(ProdutoCidDocumento.PROP_PRODUTO, receituarioItem.getProduto()))
                    .start().getList();
        }
        return cidDocumentoList;
    }

    private UsuarioCadsus carregarDataNascimentoUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        UsuarioCadsus usuarioCadsusDataNascimento = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, usuarioCadsus.getCodigo()))
                .start().getVO();

        usuarioCadsus.setDataNascimento(usuarioCadsusDataNascimento.getDataNascimento());

        return usuarioCadsus;
    }

    private boolean existsDocumentos(ReceituarioItem receituarioItem) {
        if (receituarioItem != null && receituarioItem.getProduto() != null) {
            if (RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getProduto().getFlagEmiteLme())) {
                List<ProdutoCidDocumento> list = LoadManager.getInstance(ProdutoCidDocumento.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(ProdutoCidDocumento.PROP_PRODUTO, receituarioItem.getProduto()))
                        .start().getList();
                return CollectionUtils.isNotNullEmpty(list);
            }
        }
        return false;
    }

    private List<ReceituarioItemDTO> carregarItensReceituario(Receituario receituario) {
        List<ReceituarioItemDTO> retorno = new ArrayList();
        if (receituario != null) {
            List<ReceituarioItem> list = LoadManager.getInstance(ReceituarioItem.class)
                    .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                    .addProperties(new HQLProperties(Produto.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO)).getProperties())
                    .addProperties(new HQLProperties(SubGrupo.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_SUB_GRUPO)).getProperties())
                    .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE)).getProperties())
                    .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO, Produto.PROP_TIPO_RECEITA)).getProperties())
                    .addProperties(new HQLProperties(TipoReceita.class, VOUtils.montarPath(ReceituarioItem.PROP_TIPO_RECEITA_PRODUTO_NAO_CADASTRADO)).getProperties())
                    .addProperties(new HQLProperties(MedicamentoPaciente.class, VOUtils.montarPath(ReceituarioItem.PROP_MEDICAMENTO_PACIENTE)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, receituario))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(ReceituarioItem.PROP_NOME_PRODUTO))
                    .start().getList();

            ReceituarioItemNaoPadronizado rinp;
            ReceituarioMedicamentoNaoPadronizadoDTO rinpDTO;
            ReceituarioItemDTO riDTO;
            for (ReceituarioItem receituarioItem : list) {
                rinp = LoadManager.getInstance(ReceituarioItemNaoPadronizado.class)
                        .addProperties(new HQLProperties(ReceituarioItemNaoPadronizado.class).getProperties())
                        .addProperties(new HQLProperties(Profissional.class, ReceituarioItemNaoPadronizado.PROP_PROFISSIONAL).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, ReceituarioItemNaoPadronizado.PROP_EMPRESA).getProperties())
                        .addProperties(new HQLProperties(Cid.class, ReceituarioItemNaoPadronizado.PROP_CID).getProperties())
                        .addProperties(new HQLProperties(ReceituarioItem.class, ReceituarioItemNaoPadronizado.PROP_RECEITUARIO_ITEM).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItemNaoPadronizado.PROP_RECEITUARIO_ITEM, receituarioItem))
                        .start().getVO();

                if (rinp != null) {
                    rinpDTO = new ReceituarioMedicamentoNaoPadronizadoDTO();
                    rinpDTO.setReceituarioItemNaoPadronizado(rinp);

                    riDTO = new ReceituarioItemDTO();

                    riDTO.setReceituarioItem(receituarioItem);
                    riDTO.setReceituarioMedicamentoNaoPadronizadoDTO(rinpDTO);
                    retorno.add(riDTO);
                } else {
                    riDTO = new ReceituarioItemDTO();
                    TipoReceita tr;

                    if (receituarioItem.getProduto() != null) {
                        tr = receituarioItem.getProduto().getTipoReceita();
                        if (RepositoryComponentDefault.SIM.equals(separaReceituarioAutomaticamente)) {
                            if (RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getProduto().getFlagEmiteLme()))
                                riDTO.setReceita(2);
                            else
                                riDTO.setReceita(1);
                        }
                    } else {
                        tr = receituarioItem.getTipoReceitaProdutoNaoCadastrado();
                    }

                    if (TipoReceita.RECEITA_AMARELA.equals(tr.getTipoReceita())
                            || TipoReceita.RECEITA_AZUL.equals(tr.getTipoReceita())) {
                        riDTO.setReceita(0);
                    }

                    if (riDTO.getReceita() == null && receituarioItem.getTipoReceitaProdutoNaoCadastrado() != null
                            && receituarioItem.getTipoReceitaProdutoNaoCadastrado().getCodigo() != null) {
                        riDTO.setReceita(receituarioItem.getTipoReceitaProdutoNaoCadastrado().getCodigo().intValue());
                    }

                    riDTO.setReceituarioItem(receituarioItem);
                    retorno.add(riDTO);
                }
            }
        }
        return retorno;
    }

    private class MyCustomTable extends MultiSelectionTable {

        private final LinkedHashMap<Long, TableColorEnum> map;

        public MyCustomTable(String id, List<IColumn> columns, ICollectionProvider collectionProvider) {
            super(id, columns, collectionProvider);
            map = new LinkedHashMap<Long, TableColorEnum>();
        }

        @Override
        protected Item newRowItem(String id, int index, IModel model) {
            return new CustomColorMultiSelectionTableRow(id, index, model, MyCustomTable.this) {
                @Override
                public TableColorEnum getColor() {
                    ReceituarioItem item = ((DTOSelection<ReceituarioItem>) getRowObject()).getBean();
                    if (map.isEmpty()) {
                        map.put(item.getReceituario().getCodigo(), TableColorEnum.POSITIVA);
                        return TableColorEnum.POSITIVA;
                    }
                    if (map.containsKey(item.getReceituario().getCodigo())) {
                        return map.get(item.getReceituario().getCodigo());
                    }
                    TableColorEnum classe = (TableColorEnum) map.values().toArray()[map.values().size() - 1];
                    if (classe.equals(TableColorEnum.POSITIVA)) {
                        classe = TableColorEnum.NEGATIVA;
                    } else {
                        classe = TableColorEnum.POSITIVA;
                    }

                    map.put(item.getReceituario().getCodigo(), classe);
                    return classe;
                }
            };
        }
    }
}

