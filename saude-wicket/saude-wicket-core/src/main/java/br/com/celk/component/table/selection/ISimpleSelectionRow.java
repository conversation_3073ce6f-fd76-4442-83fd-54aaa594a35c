package br.com.celk.component.table.selection;

import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface ISimpleSelectionRow<T> {

    public void onSelection();
    
    public void onSelection(AjaxRequestTarget target);
    
    public T getRowObject();
    
    public void setSelected(boolean selected);
    
    public void setSelected(AjaxRequestTarget target, boolean selected);

}
