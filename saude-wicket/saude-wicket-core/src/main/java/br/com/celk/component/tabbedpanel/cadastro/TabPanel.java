package br.com.celk.component.tabbedpanel.cadastro;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class TabPanel<T> extends Panel implements ITabPanel<T> {

    protected T object;
    
    public TabPanel(String id, T object) {
        super(id);
        this.object = object;
    }

    @Override
    public void onAjaxUpdate(AjaxRequestTarget target) {
        FormComponent componentRequestFocus = getComponentRequestFocus();
        if (componentRequestFocus != null) {
            target.focusComponent(componentRequestFocus);
        }
    }

    @Override
    public void onSelectionTab() {
    }

    public FormComponent getComponentRequestFocus(){
        return null;
    }
    
}
