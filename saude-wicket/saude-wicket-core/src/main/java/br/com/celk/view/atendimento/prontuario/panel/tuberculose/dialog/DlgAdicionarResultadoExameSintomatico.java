package br.com.celk.view.atendimento.prontuario.panel.tuberculose.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.tuberculose.dto.TuberculoseResultadoSintomaticoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAdicionarResultadoExameSintomatico extends Window{

    private PnlAdicionarResultadoExameSintomatico pnlAdicionarResultadoExameSintomatico;

    public DlgAdicionarResultadoExameSintomatico(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("registroSintomatico");
            }
        });
                
        setInitialWidth(720);
        setInitialHeight(400);
        setResizable(true);
        
        setContent(pnlAdicionarResultadoExameSintomatico = new PnlAdicionarResultadoExameSintomatico(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, TuberculoseResultadoSintomaticoDTO tuberculoseResultadoSintomaticoDTO) throws ValidacaoException, DAOException {
                close(target);
                DlgAdicionarResultadoExameSintomatico.this.onConfirmar(target, tuberculoseResultadoSintomaticoDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, TuberculoseResultadoSintomaticoDTO tuberculoseSintomatico) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico){
        show(target);
        pnlAdicionarResultadoExameSintomatico.setObject(target, tuberculoseSintomatico);
    }    
}