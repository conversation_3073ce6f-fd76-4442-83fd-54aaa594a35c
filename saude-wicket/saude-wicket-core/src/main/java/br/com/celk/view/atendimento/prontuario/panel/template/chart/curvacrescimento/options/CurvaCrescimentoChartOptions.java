package br.com.celk.view.atendimento.prontuario.panel.template.chart.curvacrescimento.options;

import com.googlecode.wickedcharts.highcharts.options.ChartOptions;
import com.googlecode.wickedcharts.highcharts.options.CreditOptions;
import com.googlecode.wickedcharts.highcharts.options.HorizontalAlignment;
import com.googlecode.wickedcharts.highcharts.options.Legend;
import com.googlecode.wickedcharts.highcharts.options.LegendLayout;
import com.googlecode.wickedcharts.highcharts.options.Options;
import com.googlecode.wickedcharts.highcharts.options.PlotOptions;
import com.googlecode.wickedcharts.highcharts.options.PlotOptionsChoice;
import com.googlecode.wickedcharts.highcharts.options.SeriesType;
import com.googlecode.wickedcharts.highcharts.options.VerticalAlignment;
import com.googlecode.wickedcharts.highcharts.options.ZoomType;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class CurvaCrescimentoChartOptions implements Serializable{

    private Options options;

    public Options getOptions() {
        return options;
    }

    public CurvaCrescimentoChartOptions build(){
        options = new Options();
        
        options
            .setChartOptions(new ChartOptions()
                .setType(SeriesType.LINE)
                .setZoomType(ZoomType.X));
        options
            .setCredits(new CreditOptions().setEnabled(Boolean.FALSE));
        
        options
            .setLegend(new Legend()
                .setLayout(LegendLayout.VERTICAL)
                .setAlign(HorizontalAlignment.RIGHT)
                .setVerticalAlign(VerticalAlignment.MIDDLE)
                .setBorderWidth(0));
        
        options.setPlotOptions(new PlotOptionsChoice().setLine(new PlotOptions().setConnectNulls(Boolean.TRUE)));
        
        return this;
    }
}
