package br.com.celk.component.lazypanel;

import org.apache.wicket.Component;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxChannel;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.IRequestHandler;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.handler.resource.ResourceReferenceRequestHandler;

public abstract class LazyPanel extends Panel {

    /**
     * The component id which will be used to load the lazily loaded component.
     */
    public static final String LAZY_LOAD_COMPONENT_ID = "content";
    // state,
    // 0:add loading component
    // 1:loading component added, waiting for ajax replace
    // 2:ajax replacement completed
    private byte state = 0;

    public LazyPanel(final String id) {
        this(id, null);
    }

    public LazyPanel(final String id, final IModel<?> model) {
        super(id, model);

        setOutputMarkupId(true);

        add(new AbstractDefaultAjaxBehavior() {

            @Override
            protected void respond(final AjaxRequestTarget target) {
                if (state < 2) {
                    Component component = getLazyLoadComponent(LAZY_LOAD_COMPONENT_ID, target);
                    LazyPanel.this.replace(component);
                    setState((byte) 2);
                }
                target.add(LazyPanel.this);

            }

            @Override
            public void renderHead(final Component component, final IHeaderResponse response) {
                super.renderHead(component, response);
                if (state < 2) {
                    handleCallbackScript(response, getCallbackScript().toString());
                }
            }

            @Override
            protected AjaxChannel getChannel() {
                return LazyPanel.this.getChannel();
            }
        });
    }

    protected AjaxChannel getChannel() {
        return null;
    }

    protected void handleCallbackScript(final IHeaderResponse response, final String callbackScript) {
        response.render(OnDomReadyHeaderItem.forScript(callbackScript));
    }

    @Override
    protected void onBeforeRender() {
        if (state == 0) {
            add(getLoadingComponent(LAZY_LOAD_COMPONENT_ID));
            setState((byte) 1);
        }
        super.onBeforeRender();
    }

    private void setState(final byte state) {
        this.state = state;
        getPage().dirty();
    }

    /**
     *
     * @param markupId The components markupid.
     * @return The component that must be lazy created. You may call
     * setRenderBodyOnly(true) on this component if you need the body only.
     */
    public abstract Component getLazyLoadComponent(String markupId, AjaxRequestTarget target);

    /**
     * @param markupId The components markupid.
     * @return The component to show while the real component is being created.
     */
    public Component getLoadingComponent(final String markupId) {
        IRequestHandler handler = new ResourceReferenceRequestHandler(
                AbstractDefaultAjaxBehavior.INDICATOR);
        return new Label(markupId, "<img alt=\"Loading...\" src=\"" + RequestCycle.get().urlFor(handler) + "\"/>").setEscapeModelStrings(false);
    }
}
