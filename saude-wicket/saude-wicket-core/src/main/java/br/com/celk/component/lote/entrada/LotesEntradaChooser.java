package br.com.celk.component.lote.entrada;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.window.IWindows;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.IModelComparator;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LotesEntradaChooser extends FormComponentPanel<List<MovimentoGrupoEstoqueItemDTO>> {

    private AbstractAjaxButton btnLotes;
    private DlgLotesEntrada dlgLotesEntrada;
    private AutoCompleteConsulta<Produto> autoCompleteConsultaProduto;
    private InputField txtQuantidade;
    private String fabricante;
    private boolean isNotaFiscal = false;
    private LocalizacaoEstrutura localizacaoEstrutura;

    private Produto produto;
    
    private ConsultaListener<Produto> consultaListenerProduto = new ConsultaListener<Produto>() {

        @Override
        public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
            dlgLotesEntrada.limpar(target);
            LotesEntradaChooser.this.produto = object;
        }
    };

    public LotesEntradaChooser(String id) {
        super(id);
        init();
    }

    public LotesEntradaChooser(String id, IModel<List<MovimentoGrupoEstoqueItemDTO>> model) {
        super(id, model);
        init();
    }
    
    public LotesEntradaChooser(String id, IModel<List<MovimentoGrupoEstoqueItemDTO>> model, boolean isNotaFiscal) {
        super(id, model);
        this.isNotaFiscal = isNotaFiscal;
        init();
       
    }
    
    private void init(){
        add(btnLotes = new AbstractAjaxButton("btnLotes") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (dlgLotesEntrada!=null) {
                    dlgLotesEntrada.show(target, produto, fabricante, localizacaoEstrutura);
                }
            }
        });
        btnLotes.setDefaultFormProcessing(false);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();
        if (dlgLotesEntrada==null) {
            IWindows findParent = findParent(IWindows.class);
            if (findParent != null) {
                findParent.addModal(dlgLotesEntrada = new DlgLotesEntrada(findParent.newModalId(), isNotaFiscal) {

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        if (txtQuantidade!=null) {
                            target.appendJavaScript("$('#"+txtQuantidade.getMarkupId()+"').val('"+Valor.adicionarFormatacaoMonetaria(dlgLotesEntrada.getQuantidadeTotal(), 0)+"')");
                            txtQuantidade.setModelObject(dlgLotesEntrada.getQuantidadeTotal());
                        }
                        confirmarAction(target);
                    }

                    @Override
                    public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        target.focusComponent(btnLotes);
                    }
                });
            }
        }
    }

    public void confirmarAction(AjaxRequestTarget target){
        
    }
    
    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        if (dlgLotesEntrada!=null) {
            dlgLotesEntrada.setLotes(getModelObject());
        }
    }
    
    @Override
    public IModelComparator getModelComparator() {
        return new IModelComparator()
	{
		@Override
		public boolean compare(Component component, Object b)
		{
			final Object a = component.getDefaultModelObject();
			if (a == null && b == null)
			{
				return true;
			}
			if (a == null || b == null)
			{
				return false;
			}
			return a == b;
		}
	};
    }

    @Override
    protected void convertInput() {
        super.convertInput();
        setConvertedInput(dlgLotesEntrada.getLotes());
    }

    public LotesEntradaChooser registerEvents(){
        if (autoCompleteConsultaProduto!=null) {
            autoCompleteConsultaProduto.add(consultaListenerProduto);
            
            autoCompleteConsultaProduto.setOnAddAction(getOnAddActionProduto());
            autoCompleteConsultaProduto.setOnDeleteAction(getOnDeleteActionProduto());
        }
        
        return this;
    }
    
    private String getOnAddActionProduto(){
        String onAddAction = "if(item.exigeGrupo == undefined){"
                    + "     alert('"+BundleManager.getString("autoCompleteProdutoNaoPreparadoLote")+"');"
                    + "} else {"
                    + "     if(item.exigeGrupo == 'S'){"
                    + "         $('#"+btnLotes.getMarkupId()+"').removeAttr('disabled');"
                    + "         $('#"+btnLotes.getMarkupId()+"').focus();";
            
            if (txtQuantidade!=null) {
                onAddAction += "$('#"+txtQuantidade.getMarkupId()+"').attr('disabled', 'disabled');";
            }
            
            onAddAction += "} else {"
                    + "         $('#"+btnLotes.getMarkupId()+"').attr('disabled', 'disabled');";
            
            if (txtQuantidade!=null) {
                onAddAction += "$('#"+txtQuantidade.getMarkupId()+"').removeAttr('disabled');";
                onAddAction += "$('#"+txtQuantidade.getMarkupId()+"').focus();";
            }
            
            onAddAction += "}"
                    + "}";
            return onAddAction;
    }
    
    private String getOnDeleteActionProduto(){
        String onDeleteAction = "$('#"+btnLotes.getMarkupId()+"').attr('disabled', 'disabled');";
            
            if (txtQuantidade!=null) {
                onDeleteAction += "$('#"+txtQuantidade.getMarkupId()+"').removeAttr('disabled');";
                onDeleteAction += "$('#"+txtQuantidade.getMarkupId()+"').val('0');";
                onDeleteAction += "$('#"+txtQuantidade.getMarkupId()+"').focus();";
            }
            
            return onDeleteAction;
    }
    
    public LotesEntradaChooser setAutoCompleteConsultaProduto(AutoCompleteConsulta<Produto> autoCompleteConsultaProduto) {
        this.autoCompleteConsultaProduto = autoCompleteConsultaProduto;
        return this;
    }

    public LotesEntradaChooser setTxtQuantidade(InputField txtQuantidade) {
        this.txtQuantidade = txtQuantidade;
        return this;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura) {
        this.localizacaoEstrutura = localizacaoEstrutura;
    }
    
    public void limpar(AjaxRequestTarget target){
        autoCompleteConsultaProduto.limpar(target);
        produto = null;
        setModelObject(new ArrayList<MovimentoGrupoEstoqueItemDTO>());
        setConvertedInput(null);
        dlgLotesEntrada.limpar(target);
        target.appendJavaScript(getValidar());
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(getValidar()));
    }
    
    public String getValidar(){
        String validar = "";
        if (produto!=null) {
            if (this.produto.getSubGrupo().isExigeGrupo()) {
                validar += " $('#"+btnLotes.getMarkupId()+"').removeAttr('disabled');";
                if (txtQuantidade!=null) {
                    validar += " $('#"+txtQuantidade.getMarkupId()+"').attr('disabled', 'disabled');";
                } 
            } else {
                validar += "$('#"+btnLotes.getMarkupId()+"').attr('disabled', 'disabled');";
                if (txtQuantidade!=null) {
                    validar += "$('#"+txtQuantidade.getMarkupId()+"').removeAttr('disabled');";
                } 
            }
        } else {
            validar += "$('#"+btnLotes.getMarkupId()+"').attr('disabled', 'disabled');";
            if (txtQuantidade!=null) {
                validar += "$('#"+txtQuantidade.getMarkupId()+"').removeAttr('disabled');";
            }
        }
        return validar;
    }
    
    public Double getQuantidadeTotal(){
        return dlgLotesEntrada.getQuantidadeTotal();
    }

    public AbstractAjaxButton getBtnLotes() {
        return btnLotes;
    }

    public void setFabricante(String fabricante) {
        this.fabricante = fabricante;
    }
}
