package br.com.celk.component.cnsfield;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.IComponent;
import br.com.celk.component.interfaces.LimparListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.ComponentWicketUtil;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.cadsus.usuariocadsus.dialog.DlgCnsPaciente;
import br.com.celk.view.cadsus.usuariocadsus.dialog.DlgServicoCnsUsuarioCadsus;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ServicoCnsUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusWSDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.string.Strings;
import org.apache.wicket.validation.IValidatable;
import org.apache.wicket.validation.IValidator;
import org.apache.wicket.validation.ValidationError;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by laudecir on 22/11/17.
 */
public class CnsField extends FormComponentPanel<String> implements IComponent {

    private InputField<String> field;
    private AbstractAjaxLink btnCartoesPaciente;
    private AbstractAjaxLink btnConsultaWS;

    private DlgCnsPaciente dlgCnsPaciente;
    private DlgServicoCnsUsuarioCadsus dlgConsultaWS;

    private boolean consumerWS;
    private UsuarioCadsus usuarioCadsus;
    private String nmUsuarioCadsusCnsDuplicado;

    private Boolean utilizarServicoWS;
    private boolean adicionarMascara = true;
    private Boolean validarSeJaExiste = true;

    private List<ConsultaListener> consultaListeners = new ArrayList();
    private List<LimparListener> limparListeners = new ArrayList();

    public CnsField(String id) {
        super(id);
        init();
    }

    public CnsField(String id, IModel<String> model) {
        super(id, model);
        init();
    }

    public CnsField(String id, IModel<String> model, boolean adicionarMascara) {
        super(id, model);
        this.adicionarMascara = adicionarMascara;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        configureField();
        createButtons();
        createDialogs();
    }

    private void configureField() {
        add(field = new InputField("field", new CompoundPropertyModel(getModel())) {
            @Override
            protected void convertInput() {
                String[] value = getInputAsArray();
                String tmp = value != null && value.length > 0 ? value[0] : null;
                if (getConvertEmptyInputStringToNull() && Strings.isEmpty(tmp)) {
                    setConvertedInput(null);
                } else {
                    String cns = StringUtilKsi.getDigits(tmp);
                    if (StringUtils.trimToNull(cns) == null) {
                        setConvertedInput(cns);
                    } else {
                        setConvertedInput(cns);
                    }
                }
            }

            @Override
            public String getInput() {
                String[] value = getInputAsArray();
                String tmp = value != null && value.length > 0 ? value[0] : null;
                if (value == null || value.length == 0) {
                    return null;
                } else {
                    String cns = StringUtilKsi.getDigits(tmp);
                    if (StringUtils.trimToNull(cns) == null) {
                        return null;
                    }
                    return trim(value[0]);
                }
            }
        });
        field.addBusyIndicator();
        if(adicionarMascara){
            field.add(new AttributeModifier("class", "cns"));
        } else {
            field.add(new AttributeModifier("class", "number"));
        }
        field.add(new IValidator<String>() {
            @Override
            public void validate(IValidatable<String> validatable) {
                String numeroCns = validatable.getValue();
                if (!CnsValidator.validaCns(numeroCns.replaceAll("[^0-9]", ""))) {
                    validatable.error(new ValidationError().addKey("msgCnsInformadoInvalido"));
                } else {
                    if(validarSeJaExiste){
                        if (hasUsuarioCadsusCns(numeroCns)) {
                            validatable.error(new ValidationError().setMessage(BundleManager.getString("msgJaExistePacienteCadastradoCNSX", nmUsuarioCadsusCnsDuplicado)));
                        }
                    }
                }
            }
        });

        field.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                String numeroCNS = field.getComponentValue();
                if (StringUtils.trimToNull(numeroCNS) != null) {
                    consumerWS = !consumerWS; // Este recurso serve para que o listener não utilize o WS pelas duas vezes que ele é invocado
                    if (consumerWS && isUtilizarServicoWS()) {
                        listenerCNS(target, numeroCNS);
                    }
                } else {
                    limpar(target);
                }
                updateNotification(target);
            }

            @Override
            protected void onError(AjaxRequestTarget target, RuntimeException e) {
                updateNotification(target);
            }
        });
    }

    private void updateNotification(AjaxRequestTarget target) {
        target.add(this);
        try {
            Page page = this.findPage();
            Method updateNotificationPanel = page.getClass().getMethod("updateNotificationPanel", AjaxRequestTarget.class, boolean.class);
            if (updateNotificationPanel != null) {
                updateNotificationPanel.invoke(page, target, true);
            }
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e ) {
            MessageUtil.modalWarn(target, this, e);
        }
    }

    private void createButtons() {
        add(btnCartoesPaciente = new AbstractAjaxLink("btnCartoesPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                showDialogCartoes(target);
            }

            @Override
            public boolean isVisible() {
                return usuarioCadsus != null && usuarioCadsus.getCodigo() != null;
            }
        });

        add(btnConsultaWS = new AbstractAjaxLink("btnConsultaWS") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                showDialogConsultaWS(target);
            }

            @Override
            public boolean isVisible() {
                return isUtilizarServicoWS();
            }
        });
    }

    private void createDialogs() {
        add(dlgCnsPaciente = new DlgCnsPaciente("cartoesDialog") {
            @Override
            public void onConfirmar(AjaxRequestTarget target, UsuarioCadsusCns usuarioCadsusCns) {
                onConfirmarCartaoUsuario(target, usuarioCadsusCns);
            }
        });

        add(dlgConsultaWS = new DlgServicoCnsUsuarioCadsus("consultaWSDialog") {
            @Override
            public void onSelected(AjaxRequestTarget target, UsuarioCadsusWSDTO dto) {
                field.setComponentValue(dto.getCns());
                callConsultaListener(target, dto);
                updateNotification(target);
            }
        });
    }

    private void listenerCNS(AjaxRequestTarget target, String numeroCNS) {
        try {
            ServicoCnsUsuarioCadsusDTO servicoCnsUsuarioCadsusDTO = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).usuarioCadsusWS(numeroCNS);
            UsuarioCadsusWSDTO usuarioCadsusWSDTO = servicoCnsUsuarioCadsusDTO.getUsuarioCadsusWSDTO();

            callConsultaListener(target, usuarioCadsusWSDTO);
        } catch (DAOException | ValidacaoException e) {
        }
    }

    private void showDialogCartoes(AjaxRequestTarget target) {
        dlgCnsPaciente.show(target, usuarioCadsus);
    }

    private void showDialogConsultaWS(AjaxRequestTarget target) {
        dlgConsultaWS.show(target);
    }

    public void onConfirmarCartaoUsuario(AjaxRequestTarget target, UsuarioCadsusCns usuarioCadsusCns) {
        field.setComponentValue(usuarioCadsusCns.getNumeroCartao().toString());
        target.add(field);
    }

    private Boolean isUtilizarServicoWS() {
        if (usuarioCadsus != null && usuarioCadsus.getCodigo() != null) {
            utilizarServicoWS = false;
        }

        if (utilizarServicoWS == null) {
            try {
                String utilizaServicoIntegracao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("UtilizaServicoIntegracaoCadsusPixPDQ");
                utilizarServicoWS = RepositoryComponentDefault.SIM.equals(utilizaServicoIntegracao);
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return utilizarServicoWS;
    }

    private boolean hasUsuarioCadsusCns(String numeroCns) {
        LoadManager loadManager = LoadManager.getInstance(UsuarioCadsusCns.class)
                .addProperty(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_NUMERO_CARTAO, Long.valueOf(numeroCns)))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SITUACAO), QueryCustom.QueryCustomParameter.DIFERENTE, UsuarioCadsus.SITUACAO_EXCLUIDO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SITUACAO), QueryCustom.QueryCustomParameter.DIFERENTE, UsuarioCadsus.SITUACAO_INATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                ;

        if (this.usuarioCadsus != null && this.usuarioCadsus.getCodigo() != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_USUARIO_CADSUS, QueryCustom.QueryCustomParameter.DIFERENTE, this.usuarioCadsus));
        }
        UsuarioCadsusCns usuarioCadsusCns = loadManager.setMaxResults(1).start().getVO();
        nmUsuarioCadsusCnsDuplicado = (usuarioCadsusCns != null && usuarioCadsusCns.getUsuarioCadsus() != null) ? usuarioCadsusCns.getUsuarioCadsus().getNome() : "";

        return usuarioCadsusCns != null;
    }

    public void add(ConsultaListener<UsuarioCadsusWSDTO> listener) {
        consultaListeners.add(listener);
    }

    public void remove(ConsultaListener<UsuarioCadsusWSDTO> listener) {
        consultaListeners.remove(listener);
    }

    public void add(LimparListener<UsuarioCadsusWSDTO> listener) {
        limparListeners.add(listener);
    }

    public void remove(LimparListener<UsuarioCadsusWSDTO> listener) {
        limparListeners.remove(listener);
    }

    private void callConsultaListener(AjaxRequestTarget target, UsuarioCadsusWSDTO usuarioCadsusWSDTO) {
        for (ConsultaListener consultaListener : consultaListeners) {
            consultaListener.valueObjectLoaded(target, usuarioCadsusWSDTO);
        }
    }

    private void callLimparListener(AjaxRequestTarget target) {
        for (LimparListener limparListener : limparListeners) {
            limparListener.valueObjectUnLoaded(target);
        }
    }

    @Override
    protected void onBeforeRender() {
        if (isValid()) {
            String modelObject = getConvertedInput();

            if (modelObject != null) {
                setModelObject(modelObject);
            }
        }
        super.onBeforeRender();
    }

    @Override
    protected void onInvalid() {
        validateHighlightError();
    }

    @Override
    protected void onValid() {
        validateHighlightError();
    }

    private void validateHighlightError() {
        if (hasErrorMessage()) {
            addErrorClass();
        } else {
            removeErrorClass();
        }
    }

    public void addErrorClass() {
        ComponentWicketUtil.addErrorClass(this);
    }

    public void removeErrorClass() {
        ComponentWicketUtil.removeErrorClass(this);
    }

    public void addRequiredClass() {
        ComponentWicketUtil.addRequiredClass(this);
    }

    public void removeRequiredClass() {
        ComponentWicketUtil.removeRequiredClass(this);
    }

    @Override
    protected void convertInput() {
        setConvertedInput(field.getConvertedInput());
    }

    @Override
    public Object getComponentValue() {
        return getModel().getObject();
    }

    @Override
    public void setComponentValue(Object value) {
        getModel().setObject((String) value);
    }

    @Override
    public void addAjaxUpdateValue() {
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        field.limpar(target);
        setComponentValue(null);
        setConvertedInput(null);
        clearInput();
        target.add(this);

        if (isUtilizarServicoWS()) {
            callLimparListener(target);
        }
    }

    public CnsField setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        return this;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initMasks()));
    }


    public Boolean getValidarSeJaExiste() {
        return validarSeJaExiste;
    }

    public void setValidarSeJaExiste(Boolean validarSeJaExiste) {
        this.validarSeJaExiste = validarSeJaExiste;
    }
}
