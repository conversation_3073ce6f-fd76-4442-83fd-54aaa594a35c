package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

/**
 * <AUTHOR>
 */
public abstract class DlgImpressaoHtml<T> extends Window {

    private PnlImpressaoHtml pnlImpressaoHtml;
    private String message;
    private final AssinaturaDigitalUtilDTO dto;

    public DlgImpressaoHtml(String id) {
        this(id, new AssinaturaDigitalUtilDTO());
    }

    public DlgImpressaoHtml(String id, AssinaturaDigitalUtilDTO dto) {
        super(id);
        this.dto = dto;
    }

    public DlgImpressaoHtml(String id, String message) {
        this(id, message, new AssinaturaDigitalUtilDTO());
    }

    public DlgImpressaoHtml(String id, String message, AssinaturaDigitalUtilDTO dto) {
        super(id);
        this.message = message;
        this.dto = dto;

        init();
    }

    private void init() {
        setDefaultModel(newModel());
        setTitle(getDialogTitle());

        setInitialHeight(60);
        setInitialWidth(500);
        setResizable(false);

        setContent(pnlImpressaoHtml = new PnlImpressaoHtml(getContentId(), message, dto) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgImpressaoHtml.this.onFechar(target, DlgImpressaoHtml.this.getModelObject());
                close(target);
            }

            @Override
            public void onAssinarDigitalmente(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgImpressaoHtml.this.onAssinarDigitalmente(target, DlgImpressaoHtml.this.getModelObject());
                onFechar(target);
            }

            @Override
            public HtmlReport onImprimir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                return DlgImpressaoHtml.this.getHtmlReport(target, DlgImpressaoHtml.this.getModelObject());
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    onFechar(target, DlgImpressaoHtml.this.getModelObject());
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, DlgImpressaoHtml.this, ex);
                    return false;
                } catch (DAOException ex) {
                    MessageUtil.modalError(target, DlgImpressaoHtml.this, ex);
                    return false;
                }
                return true;
            }
        });

    }

    public IModel<T> newModel() {
        return new Model();
    }

    public String getDialogTitle() {
        return BundleManager.getString("imprimir");
    }

    public T getModelObject() {
        return (T) getDefaultModel().getObject();
    }

    @Override
    public void show(AjaxRequestTarget target) {
        throw new UnsupportedOperationException();
    }

    public void show(AjaxRequestTarget target, T object) {
        ((IModel<T>) getDefaultModel()).setObject(object);
        super.show(target);
    }

    public void onFechar(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException {
    }

    public void onAssinarDigitalmente(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException {
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        pnlImpressaoHtml.setMessage(target, message);
    }

    public void setLabelImprimir(AjaxRequestTarget target, String label) {
        pnlImpressaoHtml.setLabelImprimir(target, label);
    }

    public abstract HtmlReport getHtmlReport(AjaxRequestTarget target, T object) throws ValidacaoException, DAOException;
}
