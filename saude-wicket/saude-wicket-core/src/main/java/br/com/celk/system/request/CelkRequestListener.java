package br.com.celk.system.request;

import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.log.Loggable;
import java.util.Date;
import java.util.UUID;
import org.apache.log4j.MDC;
import org.apache.wicket.request.cycle.AbstractRequestCycleListener;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 *
 * <AUTHOR>
 */
public class CelkRequestListener extends AbstractRequestCycleListener {

    @Override
    public void onBeginRequest(RequestCycle cycle) {
        String host = cycle.getRequest().getClientUrl().getHost();
        TenantContext.setContext(host);
        
        MDC.put("tenant", host);
        
        String uuid = UUID.randomUUID().toString();
        MDC.put("trace", uuid);

        AbstractSessaoAplicacao asa = ApplicationSession.get().getSession();
        SessaoAplicacaoContext.setContext(asa);

        if (asa != null) {
            MDC.put("usuario", asa.getCodigoUsuario());
        }

//        if (asa != null) {
//            ServletWebRequest request = (ServletWebRequest) cycle.getRequest();
//            String uuid = UUID.randomUUID().toString();
//            HttpServletRequest servletRequest = (HttpServletRequest) request.getContainerRequest();
//            servletRequest.setAttribute("requestID", uuid);
//            StringBuilder sql = new StringBuilder();
//            sql.append("INSERT INTO tempo_processos (cd_tempo_processos,uuid, dt_inicio, dt_fim, inicio,fim,tempo, cd_usuario, tenant,descricao) VALUES (nextval('seq_temp_processos'),");
//            sql.append("'").append(uuid).append("',");
//            sql.append("'").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(cycle.getStartTime()))).append("', ");
//            sql.append(" null,");
//            sql.append(cycle.getStartTime()).append(",");
//            sql.append("null").append(",");
//            sql.append("null").append(",");
//            sql.append(asa.getCodigoUsuario()).append(",");
//            sql.append("'").append(host).append("'").append(",");
//            sql.append("'").append(cycle.getRequest().getUrl().toString()).append("'");
//            sql.append(");");
////            Loggable.request.debug(sql.toString());
//        }

        }

    @Override
    public void onEndRequest(RequestCycle cycle) {
//        if(SessaoAplicacaoContext.getContext() != null && cycle != null && cycle.getRequest() != null && cycle.getRequest().getUrl() != null){
//            String url = cycle.getRequest().getUrl().toString();
//            Date fim = new Date();
//            long tempo = fim.getTime() - cycle.getStartTime();
//            if(tempo > 5000){       
//                Loggable.log.info(String.format("Request Executada - requestPath: %s requestTempo: %d", url, tempo));
//            }
//        } 
        
        TenantContext.setContext(null);
        SessaoAplicacaoContext.setContext(null);
        MDC.remove("tenant");
        MDC.remove("usuario");
        MDC.remove("trace");

//        Date fim = new Date();
//
//        ServletWebRequest request = (ServletWebRequest) cycle.getRequest();
//        HttpServletRequest servletRequest = (HttpServletRequest) request.getContainerRequest();
//        servletRequest.getParameterMap();
//        String uuid = (String) servletRequest.getAttribute("requestID");

//        StringBuilder sql = new StringBuilder();
//        if (uuid != null) {
//            sql.append("UPDATE tempo_processos SET fim = ");
//            sql.append(fim.getTime());
//            sql.append(", tempo = ").append(fim.getTime() - cycle.getStartTime()).append(" ");
//            sql.append(", dt_fim = '").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(fim)).append("' ");
//            sql.append("WHERE uuid = '");
//            sql.append(uuid).append("';");
//
////            Loggable.request.debug(sql.toString());
//        }

        // TODO: SERÁ VISTO FUTURAMENTE COMO SERÁ CONTROLADO O TEMPO DE INATIVIDADE DA SESSÃO
//        ApplicationSession as = ApplicationSession.get();
//        if(as != null){
//            String[] ignoreLastRequestDate = (String[]) servletRequest.getParameterMap().get("ignoreLastRequestDate");
//            if(!cycle.getRequest().getUrl().toString().contains("ILinkListener")){
//                if(ignoreLastRequestDate == null || !"true".equals(ignoreLastRequestDate[0]) ){
//                    if(ApplicationSession.get().getLastRequestDate() != null){
//                        long diferenca = new Dinheiro(DataUtil.getDataAtual().getTime()).subtrair(new Dinheiro(ApplicationSession.get().getLastRequestDate().getTime())).longValue();
//                        long timeout = (ApplicationSession.get().getTempoLimite() * 60 * 1000);
//                        if (diferenca > timeout) {
//                            invalidarSessao(EventoSistema.NivelCriticidade.AVISO.value(), EventoSistema.TipoEvento.SISTEMA.value(), "Sessão expirada por exceder o tempo limite de uso.");
////                            String url = RequestCycle.get().urlFor(SessaoExpiradaPage.class, null).toString();
//// RequestCycle.get().replaceAllRequestHandlers(new RedirectRequestHandler(url));
//                        }else{
//                            String[] aceito = (String[]) servletRequest.getParameterMap().get("aceito");
//                            if(aceito == null || !"true".equals(aceito[0]) ){
//                                ApplicationSession.get().setLastRequestDate(DataUtil.getDataAtual());
//                            }
//
//                        }
//                    }else{
//                        String[] aceito = (String[]) servletRequest.getParameterMap().get("aceito");
//                        if(aceito == null || !"true".equals(aceito[0]) ){
//                            ApplicationSession.get().setLastRequestDate(DataUtil.getDataAtual());
//                        }
//                    }
//                }
//            }
//        }
    }

//    private void invalidarSessao(Long nivelCriticidade, Long tipoEvento, String descricao) {
//        EventoSistemaDTO eventoSistemaDTO = new EventoSistemaDTO();
//        eventoSistemaDTO.setNivelCriticidade(nivelCriticidade);
//        eventoSistemaDTO.setKeyword(Bundle.getStringApplication("key_sessao_expirada"));
//        eventoSistemaDTO.setTipoEvento(tipoEvento);
//        eventoSistemaDTO.setDataRegistro(DataUtil.getDataAtual());
//        eventoSistemaDTO.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.SESSAO.value());
//        eventoSistemaDTO.setFonteEvento("Sessão Expirada");
//        eventoSistemaDTO.setDescricao(descricao);
//        try {
//            BOFactoryWicket.getBO(CommomFacade.class).gerarEventoSistema(eventoSistemaDTO);
//        } catch (DAOException | ValidacaoException e) {
//            Loggable.log.error(e.getMessage(), e);
//        }
//        ApplicationSession.get().invalidate();
//    }

}