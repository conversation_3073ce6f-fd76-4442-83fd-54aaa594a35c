package br.com.celk.component.ajaxcalllistener;

import org.apache.wicket.Component;
import org.apache.wicket.ajax.attributes.AjaxCallListener;

/**
 * <AUTHOR>
 */
public class DefaultListenerLoading extends AjaxCallListener {

    private boolean resizeSidebar;

    public DefaultListenerLoading() {
        this(true);
    }

    public DefaultListenerLoading(boolean resizeSidebar) {
        this.resizeSidebar = resizeSidebar;
    }

    @Override
    public CharSequence getBeforeHandler(Component component) {
        return "loading(true);";
    }

    @Override
    public CharSequence getSuccessHandler(Component component) {
        if (resizeSidebar) {
           return "stopLoading(); resizeSidebar();";
        }
        return "stopLoading();";
    }

}
