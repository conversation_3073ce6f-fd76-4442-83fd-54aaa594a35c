package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.vo.prontuario.operacao.AtoOperatorio;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtoOperatorioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.operacao.interfaces.dto.QueryImpressaoAtoOperatorioDTOParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.operacao.AtoOperatorioItem;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import java.util.Collection;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;

/**
 *
 * <AUTHOR>
 */
public class AtoOperatorioPanel extends ProntuarioCadastroPanel {

    private Form<AtoOperatorioDTO> form;
    private InputField<String> descricaoAtoCirurgico;
    private Table<AtoOperatorioItem> tblAtoOperatorioItens;
    private WebMarkupContainer containerAtoOperatorioItem;
    private CompoundPropertyModel<AtoOperatorioItem> modelAtoOperatorioItem;
    private CompoundPropertyModel<AtoOperatorioDTO> modelPrincipal;
    private DlgImpressaoObject<AtoOperatorio> dlgConfirmacaoImpressao;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;

    public AtoOperatorioPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        form = new Form("form", modelPrincipal = new CompoundPropertyModel(initDTO()));
        containerAtoOperatorioItem = new WebMarkupContainer("containerAtoOperatorioItem", modelAtoOperatorioItem = new CompoundPropertyModel(new AtoOperatorioItem()));

        AtoOperatorioDTO dtoProxy = on(AtoOperatorioDTO.class);

        form.add(new DisabledInputField(path(dtoProxy.getAtoOperatorio().getProfissionalCirurgiao().getNome())).add(new TempBehaviorV2()));

        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia(path(dtoProxy.getProcedimentoCompetencia())));
        autoCompleteConsultaProcedimentoCompetencia.add(new TempBehaviorV2());

        form.add(new AutoCompleteConsultaProfissional(path(dtoProxy.getAtoOperatorio().getPrimeiroAuxiliar())).add(new TempBehaviorV2()));
        form.add(new AutoCompleteConsultaProfissional(path(dtoProxy.getAtoOperatorio().getSegundoAuxiliar())).add(new TempBehaviorV2()));
        form.add(new AutoCompleteConsultaProfissional(path(dtoProxy.getAtoOperatorio().getTerceiroAuxiliar())).add(new TempBehaviorV2()));
        form.add(new AutoCompleteConsultaProfissional(path(dtoProxy.getAtoOperatorio().getProfissionalAnestesista())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getDiagnosticoPreOperatorio())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getTipoOperacao())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getDiagnosticoPosOperatorio())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getRelatorioImediatoPatologista())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getExameRadiologicoAto())).add(new TempBehaviorV2()));
        form.add(new InputArea(path(dtoProxy.getAtoOperatorio().getAcidenteDuranteOperacao())).add(new TempBehaviorV2()));

//        form.add(new Label("nodeTitle", new LoadableDetachableModel<String>() {
//            @Override
//            protected String load() {
//                return BundleManager.getString("atoOperatorio");
//            }
//        }));

        containerAtoOperatorioItem.add(descricaoAtoCirurgico = new InputField<String>(AtoOperatorioItem.PROP_DESCRICAO_ATO_CIRURGICO));
        containerAtoOperatorioItem.add(new AbstractAjaxButton("btnAdicionarDescricaoAtoCirurgico") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAtoOperatorioItem(target);
            }
        });

        containerAtoOperatorioItem.add(tblAtoOperatorioItens = new Table("tblAtoOperatorioItens", getColumns(), getCollectionProvider()));
        tblAtoOperatorioItens.populate();

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                gerarImpressao(target);
            }
        }));

        form.add(containerAtoOperatorioItem);
        form.getModelObject().getAtoOperatorio().setProfissionalCirurgiao(getAtendimento().getProfissional());
        add(form);

        try {
            carregar(getAtendimento());
            autoCompleteConsultaProcedimentoCompetencia.setProfissional(modelPrincipal.getObject().getAtoOperatorio().getProfissionalCirurgiao());
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), AtoOperatorioDTO.class)));
    }

    private void carregar(Atendimento atendimento) throws DAOException, ValidacaoException {
        AtoOperatorio atoOperatorio = LoadManager.getInstance(AtoOperatorio.class)
                .addProperties(new HQLProperties(AtoOperatorio.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtoOperatorio.PROP_ATENDIMENTO), atendimento))
                .start().getVO();

        if (atoOperatorio != null && atoOperatorio.getCodigo() != null) {
            modelPrincipal.getObject().setAtoOperatorio(atoOperatorio);
            carregarListas(atoOperatorio);
        }
    }

    private void gerarImpressao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtoOperatorio savedObject = salvar();

        modelPrincipal.getObject().setAtoOperatorio(savedObject);

        carregarListas(savedObject);

        new TempHelperV2().save(form);
        showDlgImpressao(target, savedObject);
    }

    public void carregarListas(AtoOperatorio atoOperatorio) throws DAOException, ValidacaoException {
        List<AtoOperatorioItem> lstAtoOperatorio = LoadManager.getInstance(AtoOperatorioItem.class)
                .addProperties(new HQLProperties(AtoOperatorioItem.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AtoOperatorioItem.PROP_ATO_OPERATORIO, atoOperatorio))
                .start().getList();

        modelPrincipal.getObject().setLstAtoOperatorio(lstAtoOperatorio);
    }

    private AtoOperatorio salvar() throws ValidacaoException, DAOException {
        AtoOperatorioDTO object = modelPrincipal.getObject();
        if (object.getProcedimentoCompetencia() != null) {
            object.getAtoOperatorio().setProcedimento(object.getProcedimentoCompetencia().getId().getProcedimento());
        }
        object.getAtoOperatorio().setAtendimento(getAtendimento());
        object.getAtoOperatorio().setUsuarioCadsus(getAtendimento().getUsuarioCadsus());

        return BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAtoOperatorioDto(object);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return modelPrincipal.getObject().getLstAtoOperatorio();
            }
        };
    }

    private void adicionarAtoOperatorioItem(AjaxRequestTarget target) {
        AtoOperatorioItem atoOperatorioModel = modelAtoOperatorioItem.getObject();

        if (atoOperatorioModel.getDescricaoAtoCirurgico() != null) {
            modelPrincipal.getObject().getLstAtoOperatorio().add(atoOperatorioModel);
            tblAtoOperatorioItens.update(target);
            modelAtoOperatorioItem.setObject(new AtoOperatorioItem());
            descricaoAtoCirurgico.limpar(target);
        }

        new TempHelperV2().save(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(AtoOperatorioItem.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("descricao"), AtoOperatorioItem.PROP_DESCRICAO_ATO_CIRURGICO));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AtoOperatorioItem>() {
            @Override
            public void customizeColumn(AtoOperatorioItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtoOperatorioItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtoOperatorioItem modelObject) throws ValidacaoException, DAOException {
                        removerAtoOperatorioItem(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerAtoOperatorioItem(AjaxRequestTarget target, AtoOperatorioItem rowObject) {
        for (int i = 0; i < modelPrincipal.getObject().getLstAtoOperatorio().size(); i++) {
            if (modelPrincipal.getObject().getLstAtoOperatorio().get(i) == rowObject) {
                modelPrincipal.getObject().getLstAtoOperatorio().remove(i);
            }
        }
        tblAtoOperatorioItens.update(target);
        new TempHelperV2().save(form);
    }

    private AtoOperatorioDTO initDTO() {
        AtoOperatorioDTO dto = new AtoOperatorioDTO();
        dto.setAtoOperatorio(new AtoOperatorio());
        dto.setLstAtoOperatorio(new ArrayList<AtoOperatorioItem>());
        return dto;
    }

    private void showDlgImpressao(AjaxRequestTarget target, AtoOperatorio atoOperatorio) {
        if (dlgConfirmacaoImpressao == null) {
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<AtoOperatorio>(getProntuarioController().newWindowId(), BundleManager.getString("impressaoGeradaSucesso", AtoOperatorioPanel.this)) {
                @Override
                public DataReport getDataReport(AtoOperatorio object) throws ReportException {
                    QueryImpressaoAtoOperatorioDTOParam param = new QueryImpressaoAtoOperatorioDTOParam();
                    param.setCodigoAtoOperacao(object.getCodigo());
                    DataReport dataReport = BOFactoryWicket.getBO(AtendimentoFacade.class).relatorioImpressaoAtoOperatorio(param);
                    return dataReport;
                }

                @Override
                public String getDialogTitle() {
                    return BundleManager.getString("visualizarImpressao", AtoOperatorioPanel.this);
                }
            });
            dlgConfirmacaoImpressao.setLabelImprimir(target, BundleManager.getString("visualizar"));
        }
        dlgConfirmacaoImpressao.show(target, atoOperatorio);
    }
}