package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;

/**
 * <AUTHOR>
 */
public abstract class PnlMotivoDesativacaoNumeracao extends Panel {

    private WebMarkupContainer notificationMessage;
    private AbstractAjaxButton btnFechar;
    private DisabledInputField<String> txtNumeracao;
    private TextArea<String> txtMotivo;
    private DateChooser txtDataCadastro;

    private String message;
    private String motivo;
    private String numeracao;
    private Date dataDesativacao;

    public PnlMotivoDesativacaoNumeracao(String id) {
        super(id);
        init();
    }

    private void init() {
        add(notificationMessage = new WebMarkupContainer("notificationMessage"));
        notificationMessage.setOutputMarkupId(true);
        notificationMessage.setVisible(false);
        notificationMessage.add(new Label("message", new PropertyModel(this, "message")));

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtDataCadastro = new DateChooser("dataDesativacao", new PropertyModel(this, "dataDesativacao")));
        txtDataCadastro.setOutputMarkupId(true);
        txtDataCadastro.setEnabled(false);

        form.add(txtNumeracao = new DisabledInputField<String>("numeracao", new PropertyModel(this, "numeracao")));
        txtNumeracao.setOutputMarkupId(true);

        form.add(txtMotivo = new TextArea<String>("motivo", new PropertyModel(this, "motivo")));
        txtMotivo.setRequired(true);
        txtMotivo.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, motivo, dataDesativacao);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
                limpar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

        txtMotivo.add(new AttributeModifier("maxlength", getMaxLengthMotivo()));
    }

    public abstract Long getMaxLengthMotivo();

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, Date dataDesativacao) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txtDataCadastro.limpar(target);
        this.txtNumeracao.limpar(target);
        this.txtMotivo.setModelValue(null);
        target.focusComponent(txtMotivo);
        notificationMessage.setVisible(false);
    }

    public void setNumeracaoFormatada(String numeracao) {
        txtNumeracao.setComponentValue(numeracao);
    }

    public void setDataCadastro(Date dataCadastro) {
        txtDataCadastro.setComponentValue(dataCadastro);
    }
}
