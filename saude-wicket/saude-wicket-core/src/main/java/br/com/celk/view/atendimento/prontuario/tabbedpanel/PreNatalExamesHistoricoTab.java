package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class PreNatalExamesHistoricoTab extends TabPanel<NoHistoricoClinicoDTO> {

    private WebMarkupContainer containerExame;

    public PreNatalExamesHistoricoTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        NoHistoricoClinicoDTO proxy = on(NoHistoricoClinicoDTO.class);

        containerExame = new WebMarkupContainer("containerExame");

        containerExame.add(DropDownUtil.getIEnumDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getVdrl()), PreNatal.Vdrl.values(), true));
        containerExame.add(new DateChooserAjax(path(proxy.getDadosClinicoDTO().getPreNatal().getDataVdrl())));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getExameClinicoNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getExameMamasNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getExameOdontologicoNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getPelvisNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getPapanicolauNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getColposcopiaNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getDadosClinicoDTO().getPreNatal().getExameClinicoCervix()), true, false));
        containerExame.setEnabled(false);

        add(containerExame);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    @Override
    public String getTitle() {
        return bundle("exames");
    }

}