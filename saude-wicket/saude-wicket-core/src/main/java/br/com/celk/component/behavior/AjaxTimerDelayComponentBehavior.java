package br.com.celk.component.behavior;

import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 *
 * <AUTHOR>
 */
public abstract class AjaxTimerDelayComponentBehavior extends AbstractDefaultAjaxBehavior {

    @Override
    protected void respond(AjaxRequestTarget target) {
        String value = RequestCycle.get().getRequest().getRequestParameters().getParameterValue("value").toString();
        onAction(target, value);
    }

    @Override
    protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
        super.updateAjaxAttributes(attributes);
        attributes.getExtraParameters().put("value", "PLACEHOLDER1");
    }

    @Override
    public CharSequence getCallbackScript() {
        String script = super.getCallbackScript().toString();
              script = script.replace("\"PLACEHOLDER1\"", "param1Value");
              return script;
    }
    
    
    
    public abstract void onAction (AjaxRequestTarget target, String value);
}
