package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoDesfazerAtendimento extends Window {


    public DlgConfirmacaoDesfazerAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(getDialogTitle());

        setInitialHeight(60);
        setInitialWidth(500);
        setResizable(false);

        setContent(new PnlConfirmacaoDesfazerAtendimento(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoDesfazerAtendimento.this.onConfirmar(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoDesfazerAtendimento.this.onFechar(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getDialogTitle() {
        return BundleManager.getString("sairAtendimento");
    }
}
