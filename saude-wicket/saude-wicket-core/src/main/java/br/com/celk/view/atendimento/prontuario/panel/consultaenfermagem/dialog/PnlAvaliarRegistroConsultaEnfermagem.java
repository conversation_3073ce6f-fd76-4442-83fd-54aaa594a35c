package br.com.celk.view.atendimento.prontuario.panel.consultaenfermagem.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.window.WindowUtil;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.consultaenfermagem.AtendimentoEnfermagemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlAvaliarRegistroConsultaEnfermagem extends Panel {

    public PnlAvaliarRegistroConsultaEnfermagem(String id, AtendimentoEnfermagemDTO atendimentoEnfermagemDTO) {
        super(id);
        init(atendimentoEnfermagemDTO);
    }

    private void init(AtendimentoEnfermagemDTO atendimentoEnfermagemDTO) {
        setOutputMarkupId(true);
        Form form = new Form("form", new CompoundPropertyModel(atendimentoEnfermagemDTO));
        AtendimentoEnfermagemDTO proxy = on(AtendimentoEnfermagemDTO.class);

        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoEnfermagem().getAvaliacao()), AtendimentoEnfermagem.Avaliacao.values(), true, true));
        form.add(new InputArea(path(proxy.getAtendimentoEnfermagem().getObservacaoAvaliacao())));

        AbstractAjaxButton btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgConfirmacaoAvaliacao(target, atendimentoEnfermagemDTO);
//                onConfirmar(target, atendimentoEnfermagemDTO);
            }
        };

        AbstractAjaxButton btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        };
        btnFechar.setDefaultFormProcessing(false);


        form.add(btnFechar, btnConfirmar);
        add(form);
    }

    private void initDlgConfirmacaoAvaliacao(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagemDTO) {
        DlgConfirmacaoSimNao dlgConfirmacaoReverterSituacao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgConfirmacaoAvaliacaoAtendimentoEnfermagem")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                PnlAvaliarRegistroConsultaEnfermagem.this.onConfirmar(target, atendimentoEnfermagemDTO);
            }
        };
        WindowUtil.addModal(target, this, dlgConfirmacaoReverterSituacao);
        dlgConfirmacaoReverterSituacao.show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
