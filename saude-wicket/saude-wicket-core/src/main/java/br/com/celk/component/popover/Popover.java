package br.com.celk.component.popover;

import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.resources.Resources;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.odlabs.wiquery.core.options.Options;

import static br.com.celk.system.methods.WicketMethods.bundle;
import org.apache.wicket.Component;
import org.apache.wicket.behavior.Behavior;

/**
 * Created by maicon on 01/02/16.
 */
public class Popover extends Behavior {

    private boolean sameParent;
    private Options options;

    public Popover() {
        this(false);
    }

    public Popover(boolean sameParent) {
        this.sameParent = sameParent;
        this.options = new Options();
    }

    public static enum Placement {

        LEFT("'left'"),
        RIGHT("'right'"),
        TOP("'top'"),
        BOTTOM("'bottom'");

        private String option;

        Placement(String option) {
            this.option = option;
        }

        public String getOption() {
            return option;
        }
    }

    public static enum Trigger {

        CLICK("'click'"),
        HOVER("'hover'"),
        FOCUS("'focus'"),
        MANUAL("'manual'");

        private String option;

        Trigger(String option) {
            this.option = option;
        }

        public String getOption() {
            return option;
        }
    }

    /**
     * default title value if `title` tag isn't present
     *
     * @param bundleKey
     */
    public Popover setText(String bundleKey) {
        StringBuilder sb = new StringBuilder();
        sb.append("'").append(bundle(bundleKey)).append("'");
        options.put("title", sb.toString());
        return this;
    }

    /**
     * default title value if `title` tag isn't present
     *
     * @param text
     */
    public Popover setHtmlText(String text) {
        setHtml(true);
        StringBuilder sb = new StringBuilder();
        sb.append("'").append(text).append("'");
        options.put("title", sb.toString());
        return this;
    }

    /**
     * default content value if `data-content` attribute isn't present
     *
     * @param bundleKey
     * @return
     */
    public Popover setContent(String bundleKey) {
        StringBuilder sb = new StringBuilder();
        sb.append("'").append(bundle(bundleKey)).append("'");
        options.put("content", sb.toString());
        return this;
    }

    /**
     * default content value if `data-content` attribute isn't present
     *
     * @param text
     * @return
     */
    public Popover setHtmlContent(String text) {
        setHtml(true);
        StringBuilder sb = new StringBuilder();
        String[] split = text.split("[\\r\\n]+");
        if (split.length > 0) {
            text = StringUtils.join(split, "<br>");
        }
        sb.append("'").append(text).append("'");
        options.put("content", sb.toString());
        return this;
    }

    /**
     * Insert html into the tooltip. If false, jquery's text method will be used
     * to insert content into the dom. Use text if you're worried about XSS
     * attacks.
     *
     * @param html
     */
    public Popover setHtml(boolean html) {
        options.put("html", html);
        return this;
    }

    /**
     * how to position the tooltip - top | bottom | left | right
     *
     * @param placement
     */
    public Popover setPlacement(Placement placement) {
        options.put("placement", placement.getOption());
        return this;
    }

    /**
     * If a selector is provided, tooltip objects will be delegated to the
     * specified targets.
     *
     * @param selector
     */
    public Popover setSelector(boolean selector) {
        options.put("selector", String.valueOf(selector));
        return this;
    }

    /**
     * how tooltip is triggered - click | hover | focus | manual
     *
     * @param trigger
     */
    public Popover setTrigger(Trigger trigger) {
        options.put("trigger", trigger.getOption());
        return this;
    }

    @Override
    public void renderHead(Component component, IHeaderResponse response) {
        super.renderHead(component, response);
        options.setOwner(component);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_WICKET_BOOTSTRAP));
        response.render(CssHeaderItem.forReference(Resources.CSS_WICKET_BOOTSTRAP));
        response.render(
                OnDomReadyHeaderItem.forScript(
                        sameParent ? "$('#" + component.getMarkupId() + "').popover(" + options.getJavaScriptOptions() + ");"
                                   : "$('#" + component.getMarkupId() + "').after(' <a href=\"#\" id=\"lkpvr" + component.getMarkupId() + "\" class=\"icon info\" tabindex=\"-1\" />');" +
                                     "$('#lkpvr" + component.getMarkupId() + "').popover(" + options.getJavaScriptOptions() + ");"
                )
        );
    }

}
