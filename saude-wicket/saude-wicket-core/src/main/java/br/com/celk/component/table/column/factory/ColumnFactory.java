package br.com.celk.component.table.column.factory;

import br.com.celk.component.table.column.*;
import br.com.celk.component.beanproperties.BeanProperties;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ColumnFactory implements Serializable {
    
    private BeanProperties beanProperties;

    public ColumnFactory(Class referenceClass) {
        this.beanProperties = new BeanProperties(referenceClass);
    }

    public Column createColumn(String header, String prop){
        Class<Column> columnClass = ColumnEnum.getColumnClass(beanProperties.getPropertyClass(prop));
        return SimpleColumnFactory.createColumn(header, prop, columnClass);
    }
    
    public Column createSortableColumn(String header, String prop){
        return createSortableColumn(header, prop, prop);
    }
    
    public Column createSortableColumn(String header, String prop, Class columnClass){
        return SimpleColumnFactory.createSortableColumn(header, prop, prop, columnClass);
    }
    
    public Column createSortableColumn(String header, String sortProp, String prop){
        Class<Column> columnClass = ColumnEnum.getColumnClass(beanProperties.getPropertyClass(prop));
        return SimpleColumnFactory.createSortableColumn(header, sortProp, prop, columnClass);
    }
    
}
