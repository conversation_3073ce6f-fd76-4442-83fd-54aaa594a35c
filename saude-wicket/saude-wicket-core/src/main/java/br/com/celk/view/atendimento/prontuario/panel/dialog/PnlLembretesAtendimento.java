package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.window.WindowUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.LembreteAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 25/01/18.
 */
public abstract class PnlLembretesAtendimento extends Panel {

    private Form form;
    private List<LembreteAtendimento> lembreteAtendimentoList = new ArrayList<>();
    private Table table;
    private MultiLineLabel label;
    private Atendimento atendimento;
    private DlgCadastroLembretesAtendimento dlgCadastroLembretesAtendimento;
    private DlgLembretesAtendimentoDesativados dlgLembretesAtendimentoDesativados;
    private boolean visualizar;

    public PnlLembretesAtendimento(String id, Atendimento atendimento, boolean visualizar){
        super(id);
        this.atendimento = atendimento;
        this.visualizar = visualizar;
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);


        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }.setVisible(!visualizar));

        form.add(new AbstractAjaxButton("btnVisualizarLembretesDesativados") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                visualizarLembretesDesativados(target);
            }
        }.setVisible(!visualizar));

        List<LembreteAtendimento> lembretes = new ConsultaLembretesAtendimento(atendimento).findLembretesAtendimento();
        table = new Table("table", getColumns(), getCollectionProvider());
        table.setVisible(!lembretes.isEmpty());
        form.add(table);
        form.add(label = new MultiLineLabel("message", ConsultaAnotacaoAtendimento.findAnotacoesFromPaciente(atendimento)));

        table.populate();

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        LembreteAtendimento proxy = on(LembreteAtendimento.class);

        columns.add(visualizar ? getActionColumnVisualizar() :  getActionColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricaoAbreviado()));
        columns.add(createColumn(bundle("ativo"), proxy.getFlagAtivoFormatado()));
        columns.add(createColumn(bundle("publico"), proxy.getFlagPublicoFormatado()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LembreteAtendimento>() {
            @Override
            public void customizeColumn(LembreteAtendimento rowObject) {
                boolean isMesmoAtendimento = rowObject.getAtendimento() != null
                        && atendimento.getCodigo().equals(rowObject.getAtendimento().getCodigo());

                addAction(ActionType.EDITAR, rowObject, new IModelAction<LembreteAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LembreteAtendimento modelObject) throws ValidacaoException, DAOException {
                        initDlgCadastroLembretesAtendimento(target, modelObject, false);
                    }
                }).setEnabled(isMesmoAtendimento);

                addAction(ActionType.REMOVER, rowObject, new IModelAction<LembreteAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LembreteAtendimento modelObject) throws ValidacaoException, DAOException {
                        BOFactory.delete(modelObject);
                        carregarLembretes(target);
                    }
                }).setEnabled(isMesmoAtendimento);

                addAction(ActionType.DESATIVAR, rowObject, new IModelAction<LembreteAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LembreteAtendimento modelObject) throws ValidacaoException, DAOException {
                        modelObject.setFlagAtivo(RepositoryComponentDefault.NAO_LONG);
                        BOFactory.save(modelObject);
                        carregarLembretes(target);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LembreteAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LembreteAtendimento modelObject) throws ValidacaoException, DAOException {
                        initDlgCadastroLembretesAtendimento(target, modelObject, true);
                    }
                });
            }
        };
    }

    private IColumn getActionColumnVisualizar() {
        return new MultipleActionCustomColumn<LembreteAtendimento>() {
            @Override
            public void customizeColumn(LembreteAtendimento rowObject) {
               addAction(ActionType.CONSULTAR, rowObject, new IModelAction<LembreteAtendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, LembreteAtendimento modelObject) throws ValidacaoException, DAOException {
                        initDlgCadastroLembretesAtendimento(target, modelObject, true);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lembreteAtendimentoList;
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        initDlgCadastroLembretesAtendimento(target, null, false);
    }

    private void initDlgCadastroLembretesAtendimento(AjaxRequestTarget target, LembreteAtendimento lembreteAtendimento, boolean consulta) {
        if (dlgCadastroLembretesAtendimento == null) {
            WindowUtil.addModal(target, this, dlgCadastroLembretesAtendimento = new DlgCadastroLembretesAtendimento(WindowUtil.newModalId(this)) {
                @Override
                public void onSalvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    carregarLembretes(target);
                }
            });
        }

        dlgCadastroLembretesAtendimento.showObject(target, atendimento, lembreteAtendimento, consulta);
    }

    private void visualizarLembretesDesativados(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        initDlgLembretesAtendimentoDesativados(target);
    }

    private void initDlgLembretesAtendimentoDesativados(AjaxRequestTarget target) {
        if (dlgLembretesAtendimentoDesativados == null) {
            WindowUtil.addModal(target, this, dlgLembretesAtendimentoDesativados = new DlgLembretesAtendimentoDesativados(WindowUtil.newModalId(this)) {
                @Override
                public void onVoltar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    carregarLembretes(target);
                }
            });
        }

        dlgLembretesAtendimentoDesativados.show(target, atendimento);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setObject(AjaxRequestTarget target, Atendimento atendimento){
        this.atendimento = atendimento;
        carregarLembretes(target);
    }

    private void carregarLembretes(AjaxRequestTarget target){
        lembreteAtendimentoList = new ConsultaLembretesAtendimento(atendimento).findLembretesAtendimento();
        table.setVisible(!lembreteAtendimentoList.isEmpty());
        if(target != null){
            table.update(target);
        }
    }
}