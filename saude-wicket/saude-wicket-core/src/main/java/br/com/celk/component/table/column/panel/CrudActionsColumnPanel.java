package br.com.celk.component.table.column.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public abstract class CrudActionsColumnPanel<T extends Serializable> extends Panel implements PermissionContainer {

    @Permission(type = Permissions.EDITAR)
    private AjaxLink btnEditar;

    @Permission(type = Permissions.DELETAR)
    private AjaxLink btnExcluir;

    @Permission(type = Permissions.CONSULTAR)
    private AjaxLink btnConsultar;

    private T object;
    private DlgConfirmacao dlgConfirmacao;

    public CrudActionsColumnPanel(String id) {
        super(id);
        init();
    }

    public CrudActionsColumnPanel(String id, T object) {
        super(id);
        this.object = object;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onEditar(target);
            }

            @Override
            public boolean isEnabled() {
                return isEditarEnabled();
            }

            @Override
            public boolean isVisible() {
                return isEditarVisible();
            }

        });
        add(btnExcluir = new AbstractAjaxLink("btnExcluir") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao != null) {
                    dlgConfirmacao.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return isExcluirEnabled();
            }

            @Override
            public boolean isVisible() {
                return isExcluirVisible();
            }

        });
        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onConsultar(target);
            }

            @Override
            public boolean isEnabled() {
                return isConsultarEnabled();
            }

            @Override
            public boolean isVisible() {
                return isConsultarVisible();
            }

        });

        btnEditar.add(new AttributeModifier("title", getEditarTitle()));
        btnExcluir.add(new AttributeModifier("title", getExcluirTitle()));
        btnConsultar.add(new AttributeModifier("title", getConsultarTitle()));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), getExcluirMsg()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    onExcluir(target);
                }

                @Override
                public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
                    btnConfirmar.add(new AttributeModifier("value", BundleManager.getString("sim")));
                    btnConfirmar.add(new AttributeModifier("class", "btn-red"));
                    btnFechar.add(new AttributeModifier("value", BundleManager.getString("nao")));
                    btnFechar.add(new AttributeModifier("class", "btn-green"));
                }

            });
        }
    }

    public abstract void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public boolean isEditarEnabled() {
        return true;
    }

    public boolean isExcluirEnabled() {
        return true;
    }

    public boolean isConsultarEnabled() {
        return true;
    }

    public boolean isEditarVisible() {
        return true;
    }

    public boolean isExcluirVisible() {
        return true;
    }

    public boolean isConsultarVisible() {
        return true;
    }

    public String getEditarTitle() {
        return BundleManager.getString("editar");
    }

    public String getExcluirTitle() {
        return BundleManager.getString("excluir");
    }

    public String getConsultarTitle() {
        return BundleManager.getString("consultar");
    }

    public AjaxLink getBtnConsultar() {
        return btnConsultar;
    }

    public AjaxLink getBtnEditar() {
        return btnEditar;
    }

    public AjaxLink getBtnExcluir() {
        return btnExcluir;
    }

    public String getExcluirMsg() {
        return BundleManager.getString("desejaRealmenteExcluir") + "?";
    }

    public T getObject() {
        return object;
    }
}
