package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxTimerDelayComponentBehavior;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Resources;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.vacina.calendario.settings.VacinaAplicacaoTableRow;
import br.com.celk.view.vacinaaplicacao.dlg.DlgDetalhesVacinaAplicacao;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import ch.lambdaj.Lambda;
import org.apache.wicket.Application;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.component.window.WindowUtil.addModal;
import static br.com.celk.component.window.WindowUtil.newModalId;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.filter;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class HistoricoVacinasTab extends TabPanel<NoHistoricoClinicoDTO> {

    private String buscaVacina;
    private List<VacinaAplicacao> vacinaAplicacaoList = new ArrayList();
    private List<VacinaAplicacao> vacinaHistoricoList = new ArrayList();
    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehavior;
    private DlgDetalhesVacinaAplicacao dlgDetalhes;
    private Table tblHistorico;
    private InputField txtBuscaVacina;

    public HistoricoVacinasTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        add(txtBuscaVacina = new InputField("buscaVacina", new PropertyModel(this, "buscaVacina")));
        txtBuscaVacina.addAjaxUpdateValue();

        ajaxTimerDelayComponentBehavior = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                buscaVacina(target, value);
            }
        };
        txtBuscaVacina.add(ajaxTimerDelayComponentBehavior);
        
        add(tblHistorico = new Table("tblHistorico", getColumnsHistorico(), getCollectionProviderHistorico()) {

            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new VacinaAplicacaoTableRow(id, index, model, tblHistorico);
            }

        });

        tblHistorico.setScrollY("400");
        tblHistorico.populate();
    }
    
    private List<IColumn> getColumnsHistorico(){
        List<IColumn> columns = new ArrayList<IColumn>();

        VacinaAplicacao proxy = on(VacinaAplicacao.class);
        
        columns.add(getActionColumn());
        columns.add(new DateColumn(bundle("dataAplicacao"), path(proxy.getDataAplicacao())));
        columns.add(createColumn(bundle("vacina"), proxy.getDescricaoVacina()));
        columns.add(createColumn(bundle("dose"), proxy.getDescricaoDoses()));
        columns.add(createColumn(bundle("estrategia"), proxy.getEstrategia().getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<VacinaAplicacao>() {

            @Override
            public void customizeColumn(VacinaAplicacao rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<VacinaAplicacao>() {

                    @Override
                    public void action(AjaxRequestTarget target, VacinaAplicacao modelObject) throws ValidacaoException, DAOException {
                        consultar(target, modelObject);
                    }
                });
            }
        };
    }
    
    private void consultar(AjaxRequestTarget target, VacinaAplicacao vacinaAplicacao) {
        initDlgDetalhes(target);
        dlgDetalhes.show(target, vacinaAplicacao.getCodigo());
    }
    
    private void initDlgDetalhes(AjaxRequestTarget target) {
        if (dlgDetalhes == null) {
            addModal(target, this, dlgDetalhes = new DlgDetalhesVacinaAplicacao(newModalId(this)));
        }
    }
    
    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object object) throws DAOException, ValidacaoException {
                return vacinaAplicacaoList;
            }
        };
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_UNIDADE_LOGIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_SCROLL_INTO_VIEW));
        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(txtBuscaVacina, ajaxTimerDelayComponentBehavior.getCallbackScript().toString())));
    }

    @Override
    public String getTitle() {
        return bundle("vacinas");
    }
    
    private void buscaVacina(AjaxRequestTarget target, String value) {
        vacinaAplicacaoList.clear();

        if (value != null && value.length() > 0 && CollectionUtils.isNotNullEmpty(vacinaHistoricoList)) {
            vacinaAplicacaoList = filter(
                    Lambda.having(on(VacinaAplicacao.class).getDescricaoVacinaUpper(), Matchers.containsString(value.toUpperCase())),
                    vacinaHistoricoList
            );
        } else {
            atualizarHistoricoList();
        }

        tblHistorico.update(target);
    }

    private void atualizarHistoricoList() {
        if (CollectionUtils.isEmpty(vacinaHistoricoList)) {
            vacinaHistoricoList = LoadManager.getInstance(VacinaAplicacao.class)
                    .addProperty(VacinaAplicacao.PROP_CODIGO)
                    .addProperty(VacinaAplicacao.PROP_DATA_APLICACAO)
                    .addProperty(VacinaAplicacao.PROP_DOSE)
                    .addProperty(VacinaAplicacao.PROP_DESCRICAO_VACINA)
                    .addProperty(VacinaAplicacao.PROP_STATUS)
                    .addProperty(VacinaAplicacao.PROP_OBSERVACAO)
                    .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_ESTRATEGIA, Calendario.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(VacinaAplicacao.PROP_ESTRATEGIA, Calendario.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS), object.getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, VacinaAplicacao.StatusVacinaAplicacao.CANCELADA.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(VacinaAplicacao.PROP_STATUS)))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(VacinaAplicacao.PROP_DATA_APLICACAO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();
        }

        vacinaAplicacaoList.clear();
        vacinaAplicacaoList.addAll(vacinaHistoricoList);
    }

    @Override
    public void onSelectionTab() {
        super.onSelectionTab();
        atualizarHistoricoList();
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtBuscaVacina;
    }
}
