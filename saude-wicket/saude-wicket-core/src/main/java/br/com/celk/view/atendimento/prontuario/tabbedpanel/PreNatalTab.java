package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.view.atendimento.prontuario.panel.HistoricoClinicoTabbedPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import br.com.ksisolucoes.vo.prontuario.basico.Puerperio;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public final class PreNatalTab extends TabPanel<NoHistoricoClinicoDTO> {

    public PreNatalTab(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
        super(id, noHistoricoClinicoDTO);
        init();
    }

    public void init() {
        List<ITab> tabs = new ArrayList();
        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(object) {

            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new DadosPreNatalTab(id, noHistoricoClinicoDTO);
            }
        });

        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(object) {

            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new HistoricoObstetricoTab(id, noHistoricoClinicoDTO);
            }
        });

        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(object) {

            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new PreNatalExamesHistoricoTab(id, noHistoricoClinicoDTO);
            }
        });

        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(object) {

            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new HistorioAvalicaoGestanteTab(id, noHistoricoClinicoDTO);
            }
        });

        if (hasPuerperio()) {
            tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(object) {

                @Override
                public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                    return new PreNatalPartoTab(id, noHistoricoClinicoDTO);
                }
            });
        }

        add(new HistoricoClinicoTabbedPanel("wizard", object, false, tabs, false));
    }

    private boolean hasPuerperio() {
        boolean exists = LoadManager.getInstance(Puerperio.class)
                .addProperty(Puerperio.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Puerperio.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), object.getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_DESFECHO, Puerperio.Desfecho.PARTO.value()))
                .exists();

        return exists;
    }

    @Override
    public void onSelectionTab() {
        if (object.getDadosClinicoDTO().getPuerperio() == null) {
            Puerperio puerperio = LoadManager.getInstance(Puerperio.class)
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_DATA_PARTO))
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_TIPO_PARTO))
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_LOCAL_OCORRENCIA))
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_ESTABELECIMENTO))
                    .addProperty(VOUtils.montarPath(Puerperio.PROP_IDADE_GESTACIONAL))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Puerperio.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), object.getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_DESFECHO, Puerperio.Desfecho.PARTO.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(Puerperio.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();

            object.getDadosClinicoDTO().setPuerperio(puerperio);
        }

        if (object.getDadosClinicoDTO().getPreNatal() == null) {
            PreNatal preNatal = LoadManager.getInstance(PreNatal.class)
                    .addProperties(new HQLProperties(PreNatal.class).getProperties())
                    .addProperty(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_TIPO_SANGUINEO))
                    .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), object.getUsuarioCadsus().getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(PreNatal.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();
            object.getDadosClinicoDTO().setPreNatal(preNatal);
        }
    }

    @Override
    public String getTitle() {
        return bundle("preNatalParto");
    }
}