<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:cc="http://creativecommons.org/ns#"
        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
        version="1.0"
        width="104"
        height="255"
        viewBox="0 0 1040 2549.9999"
        preserveAspectRatio="xMidYMid meet"
        id="svg2"
        name="71"
        inkscape:version="0.92.2 (unknown)"
        sodipodi:docname="tooth-bottom-5.svg">
  <defs
     id="defs44" />
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1863"
     inkscape:window-height="1059"
     id="namedview171"
     showgrid="false"
     inkscape:zoom="0.37587619"
     inkscape:cx="-202.01503"
     inkscape:cy="183.8021"
     inkscape:window-x="1977"
     inkscape:window-y="21"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2" />
  <metadata
     id="metadata376">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <!--<defs id="defs374" />-->
  <g
     id="g110"
     transform="matrix(1,0,0,-1,136.74357,2219.8428)">
    <path
       d="m 42.166158,2199.8428 c -23,-14 -34.0000005,-29 -39.0000005,-56 -13.9999995,-75 20.0000005,-334 71.0000005,-539 15,-59 35.000042,-199 50.000042,-345 58,-567.00001 116,-853.00001 209,-1038.00002 40,-80 116,-182.999989 151,-205.999989 42,-28 95,-17.9999999 122,23 39,57.99999 43,92.999989 31,260.999999 -11,164 -6,510.00001 12,735.00001 18,232 41,419 55,457 26,68 44,165 56,299 17,209 1,307 -62,370 -52,52 -97,59 -382,59 -222.000002,0 -245.000042,-2 -274.000042,-20 z m 594.000042,-40 c 64,-28 86,-78 91,-209 5,-120 -12,-294 -39,-399 -45,-175 -206,-310 -328,-274 -85,26 -182,127 -222,232 -44.000002,116 -117.000042,547 -105.000042,615 13,66 20,67 306.000042,60 214,-6 262,-10 297,-25 z m -332,-896 c 101,-49 203,-27 293,64 24,24 45,42 46,40 2,-2 -3,-55 -10,-118 -28,-258 -37,-477 -32,-801.00001 5,-297.00001 4,-332.00001 -12,-367.000009 -32,-71.9999899 -71,-62.99999 -146,32.999999 -139,179.00001 -211,455.00001 -274,1052.99992 -27,258.0001 -29,244.0001 31,178.0001 29,-32 73,-66 104,-82 z"
       style="fill:#2d2d2d;stroke:none"
       id="contorno"
       inkscape:connector-curvature="0" />
    <path
       d="m 62.166158,2174.8428 c -12,-8 -25,-31 -29,-50 -12,-68 61.00004,-499 105.000042,-615 40,-105 137,-206 222,-232 122,-36 283,99 328,274 27,105 44,279 39,399 -3,83 -9,113 -27,147 -37,73 -62,79 -361,87 -225,6 -258.000042,5 -277.000042,-10 z"
       style="fill:#ffffff;stroke:none"
       id="coroa"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
    <path
       d="M 607.1154,914.1201 H 199.5568 c -10.4351,78.1794 -20.6068,160.8621 -30.3906,253.7304 -11,106.0001 -20,201.9884 -20,212.9884 0,16 10.9961,8 50.9961,-35 124,-136 273.0117,-142.9883 397.0117,-17.9883 24,24 44.9961,42 45.9961,40 2,-2 -3,-55.0078 -10,-118.0078 -12.5044,-115.2189 -20.6836,-223.2345 -26.0547,-335.7227 z"
       style="fill:#ffffff;stroke:none"
       id="colo"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
    <path
       d="M 548.6779,35.331051 C 522.8478,28.827141 490.0451,54.842771 443.1701,114.84276 325.7566,266.04437 256.3159,488.8851 199.5568,914.1201 h 407.5586 c -6.6559,-139.3973 -8.7241,-285.96687 -5.957,-465.27342 5,-297.00002 4.0078,-332.01173 -11.9922,-367.011729 -12,-26.99999 -24.9902,-42.60155 -40.4883,-46.5039 z"
       style="fill:#ffffff;stroke:none"
       id="raiz"
       inkscape:connector-curvature="0">
      <title></title>
    </path>
  </g>
</svg>
