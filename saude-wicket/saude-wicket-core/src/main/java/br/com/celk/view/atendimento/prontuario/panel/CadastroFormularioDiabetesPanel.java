package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Icon32;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.FormularioDiabetes;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.Iterator;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroFormularioDiabetesPanel extends ProntuarioCadastroPanel {

    private Form<FormularioDiabetes> form;
    private FormularioDiabetes formularioDiabetes;
    private boolean consulta;
    private Long scoreTotal = 0L;
    private Long classificacao = null;

    private InputArea txaObservacao;
    private AttributeModifier modifier;
    private WebMarkupContainer container;
    private String descricaoRisco;
    private Label lblDescricaoRisco;
    private Button btnVoltar;

    private WebMarkupContainer containerConsulta;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Profissional profissional;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    private TabelaCbo tabelaCbos;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa estabelecimento;
    private Date dataCadastro;
    private String formulario;

    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;

    private CheckBoxLongValue checkBoxGlicemiaJejum;
    private CheckBoxLongValue checkBoxToleranciaDiminuida;
    private RadioButtonGroup radioGroupDiagnostico;
    private WebMarkupContainer containerDiagnostico;

    private WebMarkupContainer containerControleMetabolico;
    private RadioButtonGroup radioGroupMetabolico;

    private WebMarkupContainer containerControlePressorico;
    private RadioButtonGroup radioGroupPressorico;

    private CheckBoxLongValue checkBoxHipoglicemia;
    private CheckBoxLongValue checkBoxCetoacidose;
    private CheckBoxLongValue checkBoxSindrome;

    private CheckBoxLongValue checkBoxRetinopatiaDiabetica;
    private CheckBoxLongValue checkBoxDoencaRenal;
    private CheckBoxLongValue checkBoxInsuficienciaRenal;
    private CheckBoxLongValue checkBoxNeuropatiaDiabetica;
    private CheckBoxLongValue checkBoxPeDiabetico;
    private CheckBoxLongValue checkBoxNeuropatiaSensitivo;

    private CheckBoxLongValue checkBoxDoencaArterial;
    private CheckBoxLongValue checkBoxAcidenteVascular;
    private CheckBoxLongValue checkBoxDoencaVascular;
    private WebMarkupContainer containerClassificacaoPreDiabetes;
    private WebMarkupContainer containerInternacaoPorComplicacaoAgudaNosUltimosMeses;
    private WebMarkupContainer containerMicroangiopatia;
    private WebMarkupContainer containerMacroangiopatia;

    public CadastroFormularioDiabetesPanel(String id, FormularioDiabetes formularioDiabetes, boolean consulta) {
        super(id, bundle("estratificacaoRiscoDiabetes"));
        this.formularioDiabetes = formularioDiabetes;
        this.consulta = consulta;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        FormularioDiabetes proxy = on(FormularioDiabetes.class);

        getForm().add(txaObservacao = new InputArea(path(proxy.getObservacao())));

        getForm().add(container = new WebMarkupContainer("estratificacaoRisco"));
        container.setOutputMarkupPlaceholderTag(true);
        container.add(modifier = new AttributeModifier("class", "icon32 " + Icon32.ball_green.clazz()));
        getForm().add(lblDescricaoRisco = new Label("descricaoRisco", new PropertyModel<String>(this, "descricaoRisco")));
        lblDescricaoRisco.setOutputMarkupPlaceholderTag(true);
        descricaoRisco = bundle("estratificacaoRiscoBaixoRisco");

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        if (formularioDiabetes != null && formularioDiabetes.getCodigo() != null) {
            calcularScoreTotal(null);
        }

        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (!consulta) {
                    viewDlgSimNao(target);
                } else {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }
            }
        }.setDefaultFormProcessing(false));

        getForm().add(containerConsulta = new WebMarkupContainer("containerConsulta"));
        containerConsulta.setOutputMarkupPlaceholderTag(true);
        containerConsulta.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));
        containerConsulta.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbos", new PropertyModel(this, "tabelaCbos")));
        containerConsulta.add(new DateChooser("dataCadastro", new PropertyModel(this, "dataCadastro")));
        containerConsulta.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("estabelecimento", new PropertyModel(this, "estabelecimento")));
        containerConsulta.add(new InputField("formulario", new PropertyModel(this, "formulario")));
        if (consulta) {
            profissional = formularioDiabetes.getEstratificacaoRisco().getProfissional();
            tabelaCbos = formularioDiabetes.getEstratificacaoRisco().getCbo();
            estabelecimento = formularioDiabetes.getEstratificacaoRisco().getEmpresa();
            dataCadastro = formularioDiabetes.getEstratificacaoRisco().getDataCadastro();
            formulario = formularioDiabetes.getEstratificacaoRisco().getDescricaoFormulario();
            disableFields(getForm());
            containerConsulta.setVisible(true);
        } else {
            containerConsulta.setVisible(false);
        }
        
        getForm().add(containerClassificacaoPreDiabetes = new WebMarkupContainer("containerClassificacaoPreDiabetes"));
        containerClassificacaoPreDiabetes.add(checkBoxGlicemiaJejum = new CheckBoxLongValue(path(proxy.getFlagGlicemiaAlterada())));
        checkBoxGlicemiaJejum.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validaCamposHabilitados(target);
            }
        });
        containerClassificacaoPreDiabetes.add(checkBoxToleranciaDiminuida = new CheckBoxLongValue(path(proxy.getFlagToleranciaDiminuida())));
        checkBoxToleranciaDiminuida.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validaCamposHabilitados(target);
            }
        });

        getForm().add(containerDiagnostico = new WebMarkupContainer("containerDiagnostico"));
        containerDiagnostico.setOutputMarkupPlaceholderTag(true);
        containerDiagnostico.add(radioGroupDiagnostico = new RadioButtonGroup(path(proxy.getFlagDiagnostico())));
        radioGroupDiagnostico.add(new AjaxRadio("diabetesMellitusTipoDois", new Model((Long) FormularioDiabetes.Diagnostico.TIPO_DOIS.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        radioGroupDiagnostico.add(new AjaxRadio("diabetesMellitusTipoUm", new Model((Long) FormularioDiabetes.Diagnostico.TIPO_UM.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        getForm().add(containerControleMetabolico = new WebMarkupContainer("containerControleMetabolico"));
        containerControleMetabolico.setOutputMarkupPlaceholderTag(true);
        containerControleMetabolico.add(radioGroupMetabolico = new RadioButtonGroup(path(proxy.getFlagControleMetabolico())));
        radioGroupMetabolico.add(new AjaxRadio("adequadoHemoglobinaGlicada", new Model((Long) FormularioDiabetes.ControleMetabolico.ADEQUADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        radioGroupMetabolico.add(new AjaxRadio("inadequadoHemoglobinaGlicada", new Model((Long) FormularioDiabetes.ControleMetabolico.INADEQUADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        getForm().add(containerControlePressorico = new WebMarkupContainer("containerControlePressorico"));
        containerControlePressorico.setOutputMarkupPlaceholderTag(true);
        containerControlePressorico.add(radioGroupPressorico = new RadioButtonGroup(path(proxy.getFlagControlePressorico())));
        radioGroupPressorico.add(new AjaxRadio("adequadoPressaoArterial", new Model((Long) FormularioDiabetes.ControlePressorico.ADEQUADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        radioGroupPressorico.add(new AjaxRadio("inadequadoPressaoArterial", new Model((Long) FormularioDiabetes.ControlePressorico.INADEQUADO.value())) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        getForm().add(containerInternacaoPorComplicacaoAgudaNosUltimosMeses = new WebMarkupContainer("containerInternacaoPorComplicacaoAgudaNosUltimosMeses"));
        containerInternacaoPorComplicacaoAgudaNosUltimosMeses.add(checkBoxHipoglicemia = new CheckBoxLongValue(path(proxy.getFlagHipoglicemia())));
        checkBoxHipoglicemia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerInternacaoPorComplicacaoAgudaNosUltimosMeses.add(checkBoxCetoacidose = new CheckBoxLongValue(path(proxy.getFlagCetoacidose())));
        checkBoxCetoacidose.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerInternacaoPorComplicacaoAgudaNosUltimosMeses.add(checkBoxSindrome = new CheckBoxLongValue(path(proxy.getFlagSindromeHiperosmolar())));
        checkBoxSindrome.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        getForm().add(containerMicroangiopatia = new WebMarkupContainer("containerMicroangiopatia"));
        containerMicroangiopatia.add(checkBoxRetinopatiaDiabetica = new CheckBoxLongValue(path(proxy.getFlagRetinopatiaDiabetica())));
        checkBoxRetinopatiaDiabetica.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMicroangiopatia.add(checkBoxDoencaRenal = new CheckBoxLongValue(path(proxy.getFlagDoencaRenal())));
        checkBoxDoencaRenal.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMicroangiopatia.add(checkBoxInsuficienciaRenal = new CheckBoxLongValue(path(proxy.getFlagInsuficienciaRenal())));
        checkBoxInsuficienciaRenal.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMicroangiopatia.add(checkBoxNeuropatiaDiabetica = new CheckBoxLongValue(path(proxy.getFlagNeuropatiaDiabetica())));
        checkBoxNeuropatiaDiabetica.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMicroangiopatia.add(checkBoxPeDiabetico = new CheckBoxLongValue(path(proxy.getFlagPeDiabetico())));
        checkBoxPeDiabetico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMicroangiopatia.add(checkBoxNeuropatiaSensitivo = new CheckBoxLongValue(path(proxy.getFlagNeuropatiaSensitivo())));
        checkBoxNeuropatiaSensitivo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        getForm().add(containerMacroangiopatia = new WebMarkupContainer("containerMacroangiopatia"));
        containerMacroangiopatia.add(checkBoxDoencaArterial = new CheckBoxLongValue(path(proxy.getFlagDoencaArterial())));
        checkBoxDoencaArterial.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMacroangiopatia.add(checkBoxAcidenteVascular = new CheckBoxLongValue(path(proxy.getFlagAcidenteVascular())));
        checkBoxAcidenteVascular.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        containerMacroangiopatia.add(checkBoxDoencaVascular = new CheckBoxLongValue(path(proxy.getFlagDoencaVascular())));
        checkBoxDoencaVascular.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });

        add(getForm());
        validaCamposHabilitados(null);
        desabilitaCamposeSeConsulta();
    }

    private void desabilitaCamposeSeConsulta() {
        if (consulta) {
            container.setEnabled(false);
            containerConsulta.setEnabled(false);
            containerControleMetabolico.setEnabled(false);
            containerControlePressorico.setEnabled(false);
            containerDiagnostico.setEnabled(false);
            containerClassificacaoPreDiabetes.setEnabled(false);
            containerInternacaoPorComplicacaoAgudaNosUltimosMeses.setEnabled(false);
            containerMicroangiopatia.setEnabled(false);
            containerMacroangiopatia.setEnabled(false);
        }
    }

    private void validaCamposHabilitados(AjaxRequestTarget target) {
        Long flagToleranciaDiminuida = getForm().getModel().getObject().getFlagToleranciaDiminuida();
        Long flagGlicemiaAlterada = getForm().getModel().getObject().getFlagGlicemiaAlterada();

        boolean habilitarCamposGrupo2 = !RepositoryComponentDefault.SIM_LONG.equals(flagToleranciaDiminuida)
                && !RepositoryComponentDefault.SIM_LONG.equals(flagGlicemiaAlterada);

        habilitarCamposDiagnostico(target, habilitarCamposGrupo2);
        calcularScoreTotal(target);
    }

    private void habilitarCamposDiagnostico(AjaxRequestTarget target, boolean habilitar) {
        if (!habilitar) {
            radioGroupDiagnostico.limpar();
        }
        containerDiagnostico.setEnabled(habilitar);
        if (target != null) {
            target.add(containerDiagnostico);
        }
    }

    private void disableFields(MarkupContainer parent) {
        Iterator<Component> iterator = parent.iterator();
        while (iterator.hasNext()) {
            Component next = iterator.next();
            next.setEnabled(false);
            btnVoltar.setEnabled(true);
        }
    }

    private void calcularScoreTotal(AjaxRequestTarget target) {
        Icon32 icon;
        classificacao = EstratificacaoRisco.Risco.VERDE.value();
        icon = Icon32.ball_green;
        descricaoRisco = bundle("estratificacaoRiscoBaixoRisco");
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagGlicemiaAlterada())
                || RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagToleranciaDiminuida())) {
            classificacao = EstratificacaoRisco.Risco.VERDE.value();
            icon = Icon32.ball_green;
            descricaoRisco = bundle("estratificacaoRiscoBaixoRisco");
        } else if (FormularioDiabetes.Diagnostico.TIPO_DOIS.value().equals(getForm().getModel().getObject().getFlagDiagnostico())) {
            if (FormularioDiabetes.ControlePressorico.ADEQUADO.value().equals(getForm().getModel().getObject().getFlagControlePressorico())
            && FormularioDiabetes.ControleMetabolico.ADEQUADO.value().equals(getForm().getModel().getObject().getFlagControleMetabolico())) {
                if (!RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagHipoglicemia())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagCetoacidose())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagSindromeHiperosmolar())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagRetinopatiaDiabetica())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagDoencaRenal())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagInsuficienciaRenal())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagNeuropatiaDiabetica())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagPeDiabetico())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagNeuropatiaSensitivo())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagDoencaArterial())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagAcidenteVascular())
                    && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagDoencaVascular())
                ) {
                    classificacao = EstratificacaoRisco.Risco.AMARELO.value();
                    icon = Icon32.ball_yellow;
                    descricaoRisco = bundle("estratificacaoRiscoMedioRisco");
                } else {
                    classificacao = EstratificacaoRisco.Risco.VERMELHO.value();
                    icon = Icon32.ball_red;
                    descricaoRisco = bundle("estratificacaoRiscoAltoRisco");
                }
            } else if (FormularioDiabetes.ControlePressorico.INADEQUADO.value().equals(getForm().getModel().getObject().getFlagControlePressorico())
                    || FormularioDiabetes.ControleMetabolico.INADEQUADO.value().equals(getForm().getModel().getObject().getFlagControleMetabolico())) {
                classificacao = EstratificacaoRisco.Risco.VERMELHO.value();
                icon = Icon32.ball_red;
                descricaoRisco = bundle("estratificacaoRiscoAltoRisco");
            }
        } else if (FormularioDiabetes.Diagnostico.TIPO_UM.value().equals(getForm().getModel().getObject().getFlagDiagnostico())) {
            classificacao = EstratificacaoRisco.Risco.VERMELHO.value();
            icon = Icon32.ball_red;
            descricaoRisco = bundle("estratificacaoRiscoAltoRisco");
        }

        if (container.getBehaviors().contains(modifier)) {
            container.remove(modifier);
        }
        modifier = new AttributeModifier("class", "icon32 " + icon.clazz());
        container.add(modifier);
        if (target != null) {
            target.add(container);
            target.add(lblDescricaoRisco);
        }
    }

    public void viewDlgSimNao(AjaxRequestTarget target) {
        if (dlgConfirmacaoSimNao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgAsInformacoesDoFormularioNaoSeraoSalvas")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgConfirmacaoSimNao.show(target);
    }

    private Form<FormularioDiabetes> getForm() {
        if (this.form == null) {
            if (this.formularioDiabetes == null) {
                this.formularioDiabetes = new FormularioDiabetes();
            }
            this.form = new Form<>("form", new CompoundPropertyModel<>(formularioDiabetes));
        }
        return this.form;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (!RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagGlicemiaAlterada())
                && !RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getFlagToleranciaDiminuida())
                && getForm().getModel().getObject().getFlagDiagnostico() == null) {
            throw new ValidacaoException(bundle("msgInformeGrupoClassificacaoPreDiabetesOuDiagnostico"));
        }
        EstratificacaoRisco estratificacaoRisco = getForm().getModel().getObject().getEstratificacaoRisco();
        if (estratificacaoRisco == null) {
            estratificacaoRisco = new EstratificacaoRisco();
        }
        estratificacaoRisco.setAtendimento(getAtendimento());
        estratificacaoRisco.setCbo(getAtendimento().getTabelaCbo());
        estratificacaoRisco.setEmpresa(getAtendimento().getEmpresa());
        estratificacaoRisco.setFlagClassificacaoRisco(classificacao);
        estratificacaoRisco.setFormulario(ConfiguracaoEstratificacao.Formulario.DIABETES.value());
        estratificacaoRisco.setProfissional(getAtendimento().getProfissional());
        estratificacaoRisco = BOFactoryWicket.save(estratificacaoRisco);
        FormularioDiabetes formularioDiabetes = getForm().getModel().getObject();
        formularioDiabetes.setFlagRisco(classificacao);
        formularioDiabetes.setEstratificacaoRisco(estratificacaoRisco);
        formularioDiabetes.setScore(scoreTotal);
        BOFactoryWicket.save(formularioDiabetes);

        EstratificacaoRiscoPanel panel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
        getProntuarioController().changePanel(target, panel);

        ConfiguracaoEstratificacao configuracaoEstratificacao = LoadManager.getInstance(ConfiguracaoEstratificacao.class)
                .addProperties(new HQLProperties(ConfiguracaoEstratificacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ConfiguracaoEstratificacao.PROP_FORMULARIO, ConfiguracaoEstratificacao.Formulario.DIABETES.value()))
                .start().getVO();
        if (configuracaoEstratificacao != null) {
            if (classificacao == EstratificacaoRisco.Risco.VERDE.value() && configuracaoEstratificacao.getDoencaBaixoRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaBaixoRisco());
            } else if (classificacao == EstratificacaoRisco.Risco.AMARELO.value() && configuracaoEstratificacao.getDoencaMedioRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaMedioRisco());
            } else if (classificacao == EstratificacaoRisco.Risco.VERMELHO.value() && configuracaoEstratificacao.getDoencaAltoRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaAltoRisco());
            }
        }
    }

    private void adicionarDoencaUsuario(Doenca doencaConfiguracao) throws ValidacaoException, DAOException {
        BOFactory.getBO(AtendimentoFacade.class).salvarDoencaAPartirEstratificacaoRisco(getAtendimento().getUsuarioCadsus(), doencaConfiguracao, getAtendimento());
    }
}