package br.com.celk.component.tinymce;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.temp.interfaces.ITempStoreComponent;
import br.com.celk.component.temp.interfaces.impl.EditorTempBehaviorStrategy;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import sun.awt.EventListenerAggregate;
import wicket.contrib.tinymce.TinyMceBehavior;
import wicket.contrib.tinymce.settings.TinyMCESettings;

import java.util.Collection;
import java.util.Collections;
import java.util.EventListener;

/**
 *
 * <AUTHOR>
 */
public class EditorBehavior extends TinyMceBehavior{

    private TinyMCESettings settings;
    
    public EditorBehavior(AbstractEditorSettings abstractEditorSettings) {
        this(abstractEditorSettings.internalConfigure().createSettings());
    }


    private EditorBehavior(TinyMCESettings settings) {
        super(settings);
        this.settings = settings;
    }

    public TinyMCESettings getSettings() {
        return settings;
    }

    @Override
    public void bind(Component component) {
        super.bind(component);
        if (component instanceof ITempStoreComponent) {
            ((ITempStoreComponent)component).setTempBehaviorStrategy(new EditorTempBehaviorStrategy());
        }
    }

}
