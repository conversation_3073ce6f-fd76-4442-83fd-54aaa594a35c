package br.com.celk.component.treetable.pageable.customdatatable;

import br.com.celk.bo.treetable.interfaces.dto.TreeTableDTO;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;

public class ICustomColumnTreeTable<T extends TreeTableDTO> extends CustomAbstractColumn<TreeTableDTO, String> {

    private String rootPath;
    private String leafPath;
    private int headerIndex;
    private boolean tabLeaf;

    public ICustomColumnTreeTable(String rootPath, String leafPath, int headerIndex, IModel<String> displayModel) {
        this(rootPath, leafPath, headerIndex, displayModel, false);
    }

    public ICustomColumnTreeTable(String rootPath, String leafPath, int headerIndex, IModel<String> displayModel, boolean tabLeaf) {
        super(displayModel);
        this.rootPath = rootPath;
        this.leafPath = leafPath;
        this.headerIndex = headerIndex;
        this.tabLeaf = tabLeaf;
    }

    public ICustomColumnTreeTable(IModel<String> displayModel) {
        super(displayModel);
    }

    @Override
    public void populateItem(Item<ICellPopulator<TreeTableDTO>> cellItem, String componentId, IModel<TreeTableDTO> rowModel) {
        Object root = rowModel.getObject().getRoot();
        Object leaf = rowModel.getObject().getLeaf();
        Component component = new EmptyPanel(componentId);
        if (root != null && rootPath != null) {
            IModel model = new PropertyModel(rowModel.getObject(), rootPath);
            component = new Label(componentId, model).add(new AttributeModifier("class", "table-texto"));
        } else if (leaf != null && leafPath != null) {
            IModel model = new PropertyModel(rowModel.getObject(), leafPath);
            if (tabLeaf) {
                component = new Label(componentId, model).add(new AttributeModifier("class", "table-texto-tab"));
            } else {
                component = new Label(componentId, model).add(new AttributeModifier("class", "table-texto"));
            }
        } else if (rowModel.getObject().isHeader()) {
            IModel model = new PropertyModel(rowModel.getObject(), "headers");
            List<String> headers = (List<String>) model.getObject();
            if (headerIndex < headers.size()) {
                component = new Label(componentId, headers.get(headerIndex)).add(new AttributeModifier("class", "header-bold"));
            }
        }
        cellItem.add(component);
    }

}
