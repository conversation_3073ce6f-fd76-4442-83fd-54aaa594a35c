package br.com.celk.view.atendimento.prontuario.panel.consultaenfermagem;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.LineBreakColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.v3.TempHelperV3;
import br.com.celk.component.temp.v3.behavior.TempFormBehaviorV3;
import br.com.celk.component.temp.v3.behavior.interfaces.ILoadListenerV3;
import br.com.celk.component.temp.v3.store.interfaces.impl.DefaultTempStoreStrategyV3;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Util;
import br.com.celk.view.atendimento.prontuario.panel.consultaenfermagem.dialog.DlgAvaliarRegistroConsultaEnfermagem;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.consultaenfermagem.AtendimentoEnfermagemDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.consultaenfermagem.AtendimentoEnfermagemPrescricaoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.ResultadoEsperado;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnostico;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemDiagnosticoPlanejamento;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemIntervencaoPrescricao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.equalTo;

/**
 * <AUTHOR>
 */
public class ConsultaEnfermagemPanel extends ProntuarioCadastroPanel {

    private Form<AtendimentoEnfermagemDTO> form;
    private List<AtendimentoEnfermagemDTO> atendimentoEnfermagemList = new ArrayList();
    private List<AtendimentoEnfermagemDTO> atendimentoEnfermagemHistoricoList = new ArrayList();
    private DlgAvaliarRegistroConsultaEnfermagem dlgAvaliacaoAtendimento;
    private Table tblAtendimentoEnfermagem;

    public ConsultaEnfermagemPanel(String id) {
        super(id, BundleManager.getString("consultaEnfermagem"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel(new AtendimentoEnfermagemDTO(new AtendimentoEnfermagem())));

        AbstractAjaxButton btnNovaConsulta = new AbstractAjaxButton("btnNovaConsulta") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                CadastroConsultaEnfermagemPanel panel = new CadastroConsultaEnfermagemPanel(getProntuarioController().panelId(), ConsultaEnfermagemPanel.this.form);
                getProntuarioController().changePanel(target, panel);
            }

            @Override
            public boolean isEnabled() {
                return !exists(atendimentoEnfermagemList, having(on(AtendimentoEnfermagemDTO.class).getAtendimentoEnfermagem().getAtendimento(), equalTo(getAtendimento())));
            }
        };

        tblAtendimentoEnfermagem = new Table("tblAtendimentoEnfermagem", getColumnsAtendimentoEnfermagem(), getCollectionProviderAtendimentoEnfermagem());
        carregarListaAtendimentoEnfermagem();

        Table tblHistoricoAtendimentoEnfermagem = new Table("tblHistoricoAtendimentoEnfermagem", getColumnsHistoricoAtendimentoEnfermagem(), getCollectionProviderHistoricoAtendimentoEnfermagem());
        carregarListaHistoricoAtendiemntoEnfermagem();

        form.add(new TempFormBehaviorV3(new DefaultTempStoreStrategyV3(getAtendimento(),
                getIdentificador().toString(),
                AtendimentoEnfermagemDTO.class,
                getAtendimento().getProfissional(),
                getAtendimento().getTabelaCbo()))
                .add(new ILoadListenerV3<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void afterLoad(AtendimentoEnfermagemDTO modelObject) {
                        if (modelObject.getAtendimentoEnfermagem() != null && modelObject.getAtendimentoEnfermagem().getCodigo() == null) {
                            modelObject.getAtendimentoEnfermagem().setAtendimento(getAtendimento());
                            atendimentoEnfermagemList.add(modelObject);
                        } else if (modelObject.getAtendimentoEnfermagem() != null && modelObject.getAtendimentoEnfermagem().getCodigo() != null) {
                            for (int i = 0; i < atendimentoEnfermagemList.size(); i++) {
                                AtendimentoEnfermagemDTO atendimentoEnfermagemDTO = atendimentoEnfermagemList.get(i);
                                if (atendimentoEnfermagemDTO.getAtendimentoEnfermagem().getCodigo().equals(modelObject.getAtendimentoEnfermagem().getCodigo())) {
                                    atendimentoEnfermagemList.set(i, modelObject);
                                }
                            }
                        }
                    }
                }));

        tblAtendimentoEnfermagem.populate();
        tblHistoricoAtendimentoEnfermagem.populate();

        form.add(btnNovaConsulta, tblAtendimentoEnfermagem, tblHistoricoAtendimentoEnfermagem);

        add(form);
    }

    private List<IColumn> getColumnsAtendimentoEnfermagem() {
        List<IColumn> columns = new ArrayList();
        AtendimentoEnfermagemDTO proxy = on(AtendimentoEnfermagemDTO.class);

        columns.add(getActionColumnAtendimentoEnfermagem());
        columns.add(createColumn(bundle("data"), proxy.getAtendimentoEnfermagem().getAtendimento().getDataHoraAtendimento()));
        columns.add(createColumn(bundle("profissional"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("cbo"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getCboProfissional().getDescricaoFormatado()));
        columns.add(createColumn(bundle("conselhoClasse"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getRegistroFormatado()));
        columns.add(new LineBreakColumn(bundle("diagnosticos"), path(proxy.getDescricaoDiagnosticosFormatado())));
        columns.add(new LineBreakColumn(bundle("resultadosEsperados"), path(proxy.getDescricaoResultadosFormatado())));

        return columns;
    }

    private IColumn getActionColumnAtendimentoEnfermagem() {
        return new MultipleActionCustomColumn<AtendimentoEnfermagemDTO>() {
            @Override
            public void customizeColumn(AtendimentoEnfermagemDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagemDTO) {
                        CadastroConsultaEnfermagemPanel panel = new CadastroConsultaEnfermagemPanel(getProntuarioController().panelId(), atendimentoEnfermagemDTO, true);
                        getProntuarioController().changePanel(target, panel);
                    }
                }).setVisible(!rowObject.getAtendimentoEnfermagem().getAtendimento().getCodigo().equals(getAtendimento().getCodigo()));

                addAction(ActionType.EDITAR, rowObject, new IModelAction<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) {
                        CadastroConsultaEnfermagemPanel panel = new CadastroConsultaEnfermagemPanel(getProntuarioController().panelId(), form);
                        getProntuarioController().changePanel(target, panel);
                    }
                }).setVisible(!Util.isNull(rowObject.getAtendimentoEnfermagem().getAtendimento())
                        && rowObject.getAtendimentoEnfermagem().getAtendimento().getCodigo().equals(getAtendimento().getCodigo()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) {
                        removerAtendimentoEnfermagem(target, atendimentoEnfermagem);
                    }
                }).setIcon(Icon.ROUND_DELETE_NEW)
                        .setVisible(!Util.isNull(rowObject.getAtendimentoEnfermagem().getAtendimento())
                                && rowObject.getAtendimentoEnfermagem().getAtendimento().getCodigo().equals(getAtendimento().getCodigo()));

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) {
                        initDlgAvaliacaoAtendimento(target, atendimentoEnfermagem);
                    }
                }).setIcon(Icon.CHECKNEW).setTitleBundleKey("avaliar")
                        .setVisible(!rowObject.getAtendimentoEnfermagem().getAtendimento().getCodigo().equals(getAtendimento().getCodigo()));

            }
        };
    }

    private void removerAtendimentoEnfermagem(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) {
        atendimentoEnfermagemList.remove(atendimentoEnfermagem);
        new TempHelperV3().delete(form);
        tblAtendimentoEnfermagem.update(target);
    }

    private void initDlgAvaliacaoAtendimento(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) {
        if (dlgAvaliacaoAtendimento == null) {
            dlgAvaliacaoAtendimento = new DlgAvaliarRegistroConsultaEnfermagem(getProntuarioController().newWindowId(), atendimentoEnfermagem) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagem) throws DAOException, ValidacaoException {
                    atendimentoEnfermagem.getAtendimentoEnfermagem().setSituacao(AtendimentoEnfermagem.Situacao.AVALIADO.value());
                    BOFactoryWicket.save(atendimentoEnfermagem.getAtendimentoEnfermagem());
                    getProntuarioController().changePanel(target, new ConsultaEnfermagemPanel(getProntuarioController().panelId()));
                }
            };
            getProntuarioController().addWindow(target, dlgAvaliacaoAtendimento);
        }
        dlgAvaliacaoAtendimento.show(target);
    }

    private ICollectionProvider getCollectionProviderAtendimentoEnfermagem() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) {
                return atendimentoEnfermagemList;
            }
        };
    }

    private List<IColumn> getColumnsHistoricoAtendimentoEnfermagem() {
        List<IColumn> columns = new ArrayList();
        AtendimentoEnfermagemDTO proxy = on(AtendimentoEnfermagemDTO.class);

        columns.add(getActionColumnHistoricoAtendimentosEnfermagem());
        columns.add(createColumn(bundle("data"), proxy.getAtendimentoEnfermagem().getAtendimento().getDataHoraAtendimento()));
        columns.add(createColumn(bundle("profissional"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("cbo"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getCboProfissional().getDescricaoFormatado()));
        columns.add(createColumn(bundle("conselhoClasse"), proxy.getAtendimentoEnfermagem().getAtendimento().getProfissional().getRegistroFormatado()));
        columns.add(new LineBreakColumn(bundle("diagnosticos"), path(proxy.getDescricaoDiagnosticosFormatado())));
        columns.add(new LineBreakColumn(bundle("resultadosEsperados"), path(proxy.getDescricaoResultadosFormatado())));
        columns.add(createColumn(bundle("resultadoAlcancado"), proxy.getAtendimentoEnfermagem().getDescricaoFormatadaAvaliacao()));

        return columns;
    }

    private IColumn getActionColumnHistoricoAtendimentosEnfermagem() {
        return new MultipleActionCustomColumn<AtendimentoEnfermagemDTO>() {
            @Override
            public void customizeColumn(AtendimentoEnfermagemDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AtendimentoEnfermagemDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoEnfermagemDTO atendimentoEnfermagemDTO) {
                        CadastroConsultaEnfermagemPanel panel = new CadastroConsultaEnfermagemPanel(getProntuarioController().panelId(), atendimentoEnfermagemDTO, true);
                        getProntuarioController().changePanel(target, panel);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderHistoricoAtendimentoEnfermagem() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) {
                return atendimentoEnfermagemHistoricoList;
            }
        };
    }

    private void carregarListaAtendimentoEnfermagem() {
        List<AtendimentoEnfermagem> list = carregarAtendimentoEnfermagem(AtendimentoEnfermagem.Situacao.PENDENTE);
        carregarDto(list, atendimentoEnfermagemList);
    }

    private void carregarListaHistoricoAtendiemntoEnfermagem() {

        List<AtendimentoEnfermagem> historicoList = carregarAtendimentoEnfermagem(AtendimentoEnfermagem.Situacao.AVALIADO);

        carregarDto(historicoList, atendimentoEnfermagemHistoricoList);
    }

    private void carregarDto(List<AtendimentoEnfermagem> atendimentoEnfermagemList, List<AtendimentoEnfermagemDTO> dtoAtendimentoEnfermagemList) {
        for (AtendimentoEnfermagem atendimentoEnfermagem : atendimentoEnfermagemList) {
            AtendimentoEnfermagemDTO atendimentoEnfermagemDTO = new AtendimentoEnfermagemDTO();
            atendimentoEnfermagemDTO.setAtendimentoEnfermagem(atendimentoEnfermagem);
            atendimentoEnfermagemDTO.setAtendimentoPrimario(carregarAtendimentoPrimario(atendimentoEnfermagem.getAtendimento().getAtendimentoPrincipal()));
            carregarListaAtendimentoEnfermagemDiagnosticos(atendimentoEnfermagemDTO, atendimentoEnfermagem);
            carregarListaAtendimentoEnfermagemPrescricao(atendimentoEnfermagemDTO, atendimentoEnfermagem);

            dtoAtendimentoEnfermagemList.add(atendimentoEnfermagemDTO);
        }
    }

    private List<AtendimentoEnfermagem> carregarAtendimentoEnfermagem(AtendimentoEnfermagem.Situacao situacao) {
        AtendimentoEnfermagem proxy = on(AtendimentoEnfermagem.class);
        return LoadManager.getInstance(AtendimentoEnfermagem.class)
                .addProperties(new HQLProperties(AtendimentoEnfermagem.class).getProperties())
                .addProperty(path(proxy.getAtendimento().getCodigo()))
                .addProperty(path(proxy.getAtendimento().getStatus()))
                .addProperty(path(proxy.getAtendimento().getDataAtendimento()))
                .addProperty(path(proxy.getAtendimento().getAtendimentoPrincipal().getCodigo()))
                .addProperty(path(proxy.getAtendimento().getProfissional().getCodigo()))
                .addProperty(path(proxy.getAtendimento().getProfissional().getNome()))
                .addProperty(path(proxy.getAtendimento().getProfissional().getConselhoClasse().getCodigo()))
                .addProperty(path(proxy.getAtendimento().getProfissional().getConselhoClasse().getSigla()))
                .addProperty(path(proxy.getAtendimento().getProfissional().getNumeroRegistro()))
                .addProperty(path(proxy.getAtendimento().getEmpresa().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEnfermagem.PROP_SITUACAO), situacao.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEnfermagem.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .start().getList();
    }

    private AtendimentoPrimario carregarAtendimentoPrimario(Atendimento atendimento) {
        return LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoPrimario.PROP_ATENDIMENTO, atendimento))
                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoPrimario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .setMaxResults(1)
                .start().getVO();
    }

    private AtendimentoEnfermagemDTO carregarListaAtendimentoEnfermagemDiagnosticos(AtendimentoEnfermagemDTO atendimentoEnfermagemDTO, AtendimentoEnfermagem atendimentoEnfermagem) {
        AtendimentoEnfermagemDiagnostico proxy = on(AtendimentoEnfermagemDiagnostico.class);
        List<AtendimentoEnfermagemDiagnostico> diagnosticos = LoadManager.getInstance(AtendimentoEnfermagemDiagnostico.class)
                .addProperties(new HQLProperties(AtendimentoEnfermagemDiagnostico.class).getProperties())
                .addProperty(path(proxy.getDiagnosticoNaoPadronizado()))
                .addProperty(path(proxy.getDiagnosticoEnfermagemSae().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEnfermagemDiagnostico.PROP_ATENDIMENTO_ENFERMAGEM), atendimentoEnfermagem))
                .start().getList();

        atendimentoEnfermagemDTO.setAtendimentoEnfermagemDiagnosticoList(diagnosticos);
        for (AtendimentoEnfermagemDiagnostico diagnostico : diagnosticos) {
            atendimentoEnfermagemDTO.getAtendimentoEnfermagemDiagnosticoPlanejamentoList().addAll(carregarListaAtendimentoEnfermagemPlanejamento(diagnostico));
        }

        return atendimentoEnfermagemDTO;
    }

    private List<AtendimentoEnfermagemDiagnosticoPlanejamento> carregarListaAtendimentoEnfermagemPlanejamento(AtendimentoEnfermagemDiagnostico atendimentoEnfermagemDiagnostico) {
        AtendimentoEnfermagemDiagnosticoPlanejamento proxy = on(AtendimentoEnfermagemDiagnosticoPlanejamento.class);
        return LoadManager.getInstance(AtendimentoEnfermagemDiagnosticoPlanejamento.class)
                .addProperties(new HQLProperties(AtendimentoEnfermagemDiagnosticoPlanejamento.class).getProperties())
                .addProperties(new HQLProperties(ResultadoEsperado.class, AtendimentoEnfermagemDiagnosticoPlanejamento.PROP_RESULTADO_ESPERADO).getProperties())
                .addProperty(path(proxy.getResultadoEsperadoNaoPadronizado()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnostico().getCodigo()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnostico().getDiagnosticoNaoPadronizado()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnostico().getDiagnosticoEnfermagemSae().getCodigo()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnostico().getDiagnosticoEnfermagemSae().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEnfermagemDiagnosticoPlanejamento.PROP_ATENDIMENTO_ENFERMAGEM_DIAGNOSTICO), atendimentoEnfermagemDiagnostico))
                .start().getList();
    }

    private AtendimentoEnfermagemDTO carregarListaAtendimentoEnfermagemPrescricao(AtendimentoEnfermagemDTO atendimentoEnfermagemDTO, AtendimentoEnfermagem atendimentoEnfermagem) {
        AtendimentoEnfermagemIntervencaoPrescricao proxy = on(AtendimentoEnfermagemIntervencaoPrescricao.class);
        List<AtendimentoEnfermagemIntervencaoPrescricao> prescricoesList = LoadManager.getInstance(AtendimentoEnfermagemIntervencaoPrescricao.class)
                .addProperties(new HQLProperties(AtendimentoEnfermagemIntervencaoPrescricao.class).getProperties())
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getCodigo()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getResultadoEsperado().getCodigo()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getResultadoEsperado().getDescricao()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getResultadoEsperadoNaoPadronizado()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getIntervencaoEnfermagem().getCodigo()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getIntervencaoEnfermagem().getDescricao()))
                .addProperty(path(proxy.getAtendimentoEnfermagemDiagnosticoPlanejamento().getIntervencaoEnfermagemNaoPadronizado()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoEnfermagemIntervencaoPrescricao.PROP_ATENDIMENTO_ENFERMAGEM), atendimentoEnfermagem))
                .start().getList();

        for (AtendimentoEnfermagemIntervencaoPrescricao prescricao : prescricoesList) {
            AtendimentoEnfermagemPrescricaoDTO atendimentoEnfermagemPrescricaoDTO = new AtendimentoEnfermagemPrescricaoDTO();
            atendimentoEnfermagemPrescricaoDTO.setAtendimentoEnfermagemIntervencaoPrescricao(prescricao);

            atendimentoEnfermagemDTO.getAtendimentoEnfermagemIntervencaoPrescricaoList().add(atendimentoEnfermagemPrescricaoDTO);
        }

        return atendimentoEnfermagemDTO;
    }
}
