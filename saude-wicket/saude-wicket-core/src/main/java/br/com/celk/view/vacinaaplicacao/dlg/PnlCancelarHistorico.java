package br.com.celk.view.vacinaaplicacao.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCancelarHistorico extends Panel{

    private WebMarkupContainer dialogRoot;
    private InputArea<String> textArea;
    private String motivoCancelamento;
    
    public PnlCancelarHistorico(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        dialogRoot = new WebMarkupContainer("dialogRoot");

        dialogRoot.setOutputMarkupId(true);
        
        dialogRoot.add(textArea = new InputArea("motivoCancelamento", new Model()));
        
        textArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                motivoCancelamento = textArea.getModelObject();                    
            }
        });
        
        dialogRoot.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCancelamento(target)){
                    onConfirmar(target);
                }
            }
        });
        
        dialogRoot.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        
        add(dialogRoot);
    }
    
    public boolean validarCancelamento(AjaxRequestTarget target) {
        try {
            if(motivoCancelamento == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo_cancelamento"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
    
    public void update(AjaxRequestTarget target){
        target.add(dialogRoot);
    }
    
    public void limpar(AjaxRequestTarget target){
        textArea.limpar(target);
        target.focusComponent(textArea);
    }
    
    public String getMotivoCancelamento() {
        return motivoCancelamento;
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
    
    public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException{}
    
}
