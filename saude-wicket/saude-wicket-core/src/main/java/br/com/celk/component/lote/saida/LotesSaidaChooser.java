package br.com.celk.component.lote.saida;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.window.IWindows;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vacina.produtovacina.autocomplete.AutoCompleteConsultaProdutoVacinaFabricante;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.vacina.ProdutoVacina;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.IModelComparator;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LotesSaidaChooser extends FormComponentPanel<List<MovimentoGrupoEstoqueItemDTO>> {
    private AutoCompleteConsulta<ProdutoVacina> autoCompleteConsultaProdutoVacinaFabricante;
    private AbstractAjaxButton btnLotes;
    private DlgLotesSaida dlgLotesSaida;
    private AutoCompleteConsulta<Produto> autoCompleteConsultaProduto;
    private InputField txtQuantidade;
    private boolean adicionado;
    private boolean permiteSugerirLoteAutomaticamente = false;
    
    private Produto produto;
    private Deposito deposito;
    
    private ConsultaListener<Produto> consultaListenerProduto = new ConsultaListener<Produto>() {

        @Override
        public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
            adicionarDialog(target);
            dlgLotesSaida.limpar(target);
            LotesSaidaChooser.this.produto = object;
        }
    };
    private ConsultaListener<ProdutoVacina> consultaListenerProdutoVacina = new ConsultaListener<ProdutoVacina>() {

        @Override
        public void valueObjectLoaded(AjaxRequestTarget target, ProdutoVacina object) {
            if (object.getProduto().getSubGrupo().isExigeGrupo()) {
                adicionarDialog(target);
                dlgLotesSaida.limpar(target);
                LotesSaidaChooser.this.produto = object.getProduto();
                btnLotes.setEnabled(true);
            } else {
                btnLotes.setEnabled(false);
                LotesSaidaChooser.this.produto = null;
                dlgLotesSaida.limpar(target);
            }
            target.add(btnLotes);
        }
    };

    public LotesSaidaChooser(String id) {
        super(id);
        init();
    }

    public LotesSaidaChooser(String id, IModel<List<MovimentoGrupoEstoqueItemDTO>> model) {
        super(id, model);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        add(btnLotes = new AbstractAjaxButton("btnLotes") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarDialog(target);
                dlgLotesSaida.show(target, produto, deposito, permiteSugerirLoteAutomaticamente);
            }
        });
    }

    private void adicionarDialog(AjaxRequestTarget target){
        if(! adicionado){
            IWindows findParent = findParent(IWindows.class);
            if (findParent != null) {
                findParent.addModal(target, dlgLotesSaida);
            }
            adicionado = true;
        }
    }
    
    @Override
    protected void onInitialize() {
        super.onInitialize();
        if(dlgLotesSaida == null){
            IWindows findParent = findParent(IWindows.class);
            if (findParent != null) {
                dlgLotesSaida = new DlgLotesSaida(findParent.newModalId()) {

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        if (txtQuantidade!=null) {
                            target.appendJavaScript("$('#"+txtQuantidade.getMarkupId()+"').val('"+Valor.adicionarFormatacaoMonetaria(dlgLotesSaida.getQuantidadeTotal(), 0)+"')");
                            txtQuantidade.setModelObject(dlgLotesSaida.getQuantidadeTotal());
                            txtQuantidade.setComponentValue(dlgLotesSaida.getQuantidadeTotal());
                        }
                        confirmarAction(target);
                    }

                    @Override
                    public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                        target.focusComponent(btnLotes);
                    }
                };
            }
        }
    }

    public void confirmarAction(AjaxRequestTarget target){

    }
    
    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        if (dlgLotesSaida!=null) {
            dlgLotesSaida.setLotes(getModelObject());
        }
    }

    @Override
    public IModelComparator getModelComparator() {
        return new IModelComparator()
	{
		@Override
		public boolean compare(Component component, Object b)
		{
			final Object a = component.getDefaultModelObject();
			if (a == null && b == null)
			{
				return true;
			}
			if (a == null || b == null)
			{
				return false;
			}
			return a == b;
		}
	};
    }

    @Override
    protected void convertInput() {
        super.convertInput();
        setConvertedInput(dlgLotesSaida.getLotes());
    }

    public LotesSaidaChooser registerEvents(){
        if (autoCompleteConsultaProduto!=null) {
            autoCompleteConsultaProduto.add(consultaListenerProduto);
            
            autoCompleteConsultaProduto.setOnAddAction(getOnAddActionProduto());
            autoCompleteConsultaProduto.setOnDeleteAction(getOnDeleteActionProduto());
            
        }
        if (autoCompleteConsultaProdutoVacinaFabricante != null) {
            autoCompleteConsultaProdutoVacinaFabricante.add(consultaListenerProdutoVacina);

            autoCompleteConsultaProdutoVacinaFabricante.add(new RemoveListener<ProdutoVacina>() {
                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, ProdutoVacina object) {
                    txtQuantidade.setComponentValue(0);
                    btnLotes.setEnabled(false);
                    target.add(txtQuantidade);
                    target.add(btnLotes);
                }
            });
        }

        return this;
    }
    
    private String getOnAddActionProduto(){

        String onAddAction = "if(item.exigeGrupo == undefined){"
                + "     console.info(item);"
                + "     alert('" + BundleManager.getString("autoCompleteProdutoNaoPreparadoLote") + "');"
                + "} else {"
                + "     if(item.exigeGrupo == 'S'){"
                + "         $('#" + btnLotes.getMarkupId() + "').removeAttr('disabled');"
                + "         $('#" + btnLotes.getMarkupId() + "').focus();";
        if (txtQuantidade != null) {
            onAddAction += "$('#" + txtQuantidade.getMarkupId() + "').attr('disabled', 'disabled');";
        }

        onAddAction += "} else {"
                + "         $('#" + btnLotes.getMarkupId() + "').attr('disabled', 'disabled');";

        if (txtQuantidade != null) {
            onAddAction += "$('#" + txtQuantidade.getMarkupId() + "').removeAttr('disabled');";
            onAddAction += "$('#" + txtQuantidade.getMarkupId() + "').focus();";
        }

        onAddAction += "}"
                + "}";
        return onAddAction;
    }

    private String getOnDeleteActionProduto(){
        String onDeleteAction = "$('#" + btnLotes.getMarkupId() + "').attr('disabled', 'disabled');";

        if (txtQuantidade != null) {
            onDeleteAction += "$('#" + txtQuantidade.getMarkupId() + "').removeAttr('disabled');";
            onDeleteAction += "$('#" + txtQuantidade.getMarkupId() + "').val('0');";
            onDeleteAction += "$('#" + txtQuantidade.getMarkupId() + "').focus();";
        }

        return onDeleteAction;
    }

    public LotesSaidaChooser setAutoCompleteConsultaProduto(AutoCompleteConsulta<Produto> autoCompleteConsultaProduto) {
        this.autoCompleteConsultaProduto = autoCompleteConsultaProduto;
        return this;
    }

    public LotesSaidaChooser setAutoCompleteConsultaProdutoVacinaFabricante(AutoCompleteConsultaProdutoVacinaFabricante autoCompleteConsultaProdutoVacinaFabricante) {
        this.autoCompleteConsultaProdutoVacinaFabricante = (AutoCompleteConsulta<ProdutoVacina>) autoCompleteConsultaProdutoVacinaFabricante;
        return this;
    }

    public LotesSaidaChooser setTxtQuantidade(InputField txtQuantidade) {
        this.txtQuantidade = txtQuantidade;
        return this;
    }
    
    public void limpar(AjaxRequestTarget target){
        if (autoCompleteConsultaProduto != null) {
            autoCompleteConsultaProduto.limpar(target);
        } else if (autoCompleteConsultaProdutoVacinaFabricante != null) {
            autoCompleteConsultaProdutoVacinaFabricante.limpar(target);
        }
        produto = null;
        setModelObject(null);
        setConvertedInput(null);
        dlgLotesSaida.limpar(target);
        target.appendJavaScript(getValidar(false));
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(getValidar(false)));
    }
    
    public Double getQuantidadeTotal(){
        return dlgLotesSaida.getQuantidadeTotal();
    }

    public LotesSaidaChooser setDeposito(Deposito deposito) {
        this.deposito = deposito;
        return this;
    }

    public String getValidar(boolean produtoNovo){
        String validar = "";
        if (produto != null) {
            if (this.produto.getSubGrupo().isExigeGrupo()) {
                validar+= " $('#" + btnLotes.getMarkupId() + "').removeAttr('disabled');";
                if (txtQuantidade!=null) {
                    validar += "$('#" + txtQuantidade.getMarkupId() + "').attr('disabled', 'disabled');";
                } 
            } else {
                validar += " $('#" + btnLotes.getMarkupId() + "').attr('disabled', 'disabled');";
                if (txtQuantidade!=null && !produtoNovo) {
                    validar += "$('#" + txtQuantidade.getMarkupId() + "').removeAttr('disabled');";
                }
                if (produtoNovo) {
                    validar += "$('#" + txtQuantidade.getMarkupId() + "').attr('disabled', 'disabled');";
                }
            }
        } else {
            validar += " $('#" + btnLotes.getMarkupId() + "').attr('disabled', 'disabled');";
            if (txtQuantidade!=null && !produtoNovo) {
                validar += "$('#" + txtQuantidade.getMarkupId() + "').removeAttr('disabled');";
            }
            if (produtoNovo) {
                validar += "$('#" + txtQuantidade.getMarkupId() + "').attr('disabled', 'disabled');";
            }
        }
        return validar;
    }
    
    public AbstractAjaxButton getBtnLotes() {
        return btnLotes;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public LotesSaidaChooser setPermiteSugerirLoteAutomaticamente(boolean permiteSugerirLoteAutomaticamente) {
        this.permiteSugerirLoteAutomaticamente = permiteSugerirLoteAutomaticamente;
        return this;
    }
}
