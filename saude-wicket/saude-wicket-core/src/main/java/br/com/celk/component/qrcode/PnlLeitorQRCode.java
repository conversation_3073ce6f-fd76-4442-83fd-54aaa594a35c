package br.com.celk.component.qrcode;

import br.com.celk.component.behavior.AjaxTimerDelayComponentBehavior;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.resources.Resources;
import br.com.celk.system.javascript.JScript;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlLeitorQRCode extends Panel {

    private String qrcode;
    private AjaxTimerDelayComponentBehavior ajaxTimerDelayComponentBehavior;
    private InputField inputQrcodeHidden = new InputField<>("qrcode", new PropertyModel<>(this, "qrcode"));

    public PnlLeitorQRCode(String id) {
        super(id);
        init();
    }

    public void init() {
        inputQrcodeHidden.add(new AttributeModifier("id", new Model<String>("qrcodeContent")));
        inputQrcodeHidden.setOutputMarkupId(true);

        inputQrcodeHidden.addAjaxUpdateValue();
        ajaxTimerDelayComponentBehavior = new AjaxTimerDelayComponentBehavior() {
            @Override
            public void onAction(AjaxRequestTarget target, String value) {
                getValueQrcode(target, value);
            }
        };
        inputQrcodeHidden.add(ajaxTimerDelayComponentBehavior);
        add(inputQrcodeHidden);

        add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                PnlLeitorQRCode.this.onFechar(target);
            }
        });
    }

    private void getValueQrcode(AjaxRequestTarget target, String value) {
        onConfirmar(target, value);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String qrcode);

    public abstract void onFechar(AjaxRequestTarget target);

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_QR_SCANNER));
        response.render(OnDomReadyHeaderItem.forScript(JScript.timerDelayComponent(inputQrcodeHidden, ajaxTimerDelayComponentBehavior.getCallbackScript().toString())));
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(inputQrcodeHidden)));
    }
}


