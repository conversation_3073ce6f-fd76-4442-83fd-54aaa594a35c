package br.com.celk.component.inputfield.upper;

import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class DisabledUpperField extends UpperField {

    public DisabledUpperField(String id) {
        super(id);
        init();
    }

    public DisabledUpperField(String id, Class<String> type) {
        super(id, type);
        init();
    }

    public DisabledUpperField(String id, IModel<String> model) {
        super(id, model);
        init();
    }

    public DisabledUpperField(String id, IModel<String> model, Class<String> type) {
        super(id, model, type);
        init();
    }

    private void init(){
        setEnabled(false);
    }

}
