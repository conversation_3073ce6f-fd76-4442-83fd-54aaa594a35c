package br.com.celk.view.atendimento.prontuario.panel.receituario.view;

import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalHelper;
import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalService;
import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.autenticacao.TokenGenerator;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.AssinaturaDigitalBry;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.provedor.PscService;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dialog.DlgImpressaoDataReport;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.tabbedpanel.cadastro.CadastroReceituarioTabbedPanelBar;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.interfaces.IProntuarioController;
import br.com.celk.view.atendimento.prontuario.panel.ReceituarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtil;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.receituario.NoReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirMultiplasReceitasDTOParam;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.DocumentoAssinado;
import br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import ch.lambdaj.Lambda;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporter;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import wicket.contrib.tinymce.ajax.TinyMceAjaxSubmitModifier;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public class ReceituarioTabbedPanel extends CadastroTabbedPanel<NoReceituarioDTO> {

    public NoReceituarioDTO noReceituarioDTO;
    private IProntuarioController prontuarioController;
    private boolean origemPainelSoap;
    private CadastroReceituarioTabbedPanelBar cadastroReceituarioTabbedPanelBar;
    private List<Receituario> lstReceituariosVoltar = new ArrayList<>();
    private SoapDTO.ContainerTelaSoap containerTelaSoap;
    private DlgImpressaoObjectMulti<RelatorioImprimirMultiplasReceitasDTOParam> dlgImpressaoObjectMulti;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private DlgImpressaoDataReport<Receituario> dlgImpressaoDataReport;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public ReceituarioTabbedPanel(String id, NoReceituarioDTO object, boolean viewOnly, List<ITab> tabs, IProntuarioController prontuarioController, boolean origemPainelSoap, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, object, viewOnly, tabs, true);
        this.prontuarioController = prontuarioController;
        this.noReceituarioDTO = object;
        this.origemPainelSoap = origemPainelSoap;
        this.containerTelaSoap = containerTelaSoap;
        cadastroReceituarioTabbedPanelBar.getBtnSalvar().add(new TinyMceAjaxSubmitModifier());
    }

    public ReceituarioTabbedPanel(String id, NoReceituarioDTO object, List<ITab> tabs) {
        super(id, object, tabs);
    }

    public ReceituarioTabbedPanel(String id, List<ITab> tabs) {
        super(id, tabs);
    }

    @Override
    public Class<NoReceituarioDTO> getReferenceClass() {
        return NoReceituarioDTO.class;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }

    @Override
    public void voltar(AjaxRequestTarget target) {
        if (CollectionUtils.isNotNullEmpty(lstReceituariosVoltar)) {
            for (Receituario receituario : lstReceituariosVoltar) {
                if (receituario.getTipoReceita() != null
                        && (TipoReceita.RECEITA_AZUL.equals(receituario.getTipoReceita().getTipoReceita()) || TipoReceita.RECEITA_AMARELA.equals(receituario.getTipoReceita().getTipoReceita()))) {
                    ReceituarioPanel receituarioPanel = new ReceituarioPanel(prontuarioController.panelId(), origemPainelSoap, true, containerTelaSoap);
                    prontuarioController.changePanel(target, receituarioPanel);
                } else {
                    ReceituarioPanel receituarioPanel = new ReceituarioPanel(prontuarioController.panelId(), origemPainelSoap, containerTelaSoap);
                    prontuarioController.changePanel(target, receituarioPanel);
                }
            }
        } else {
            ReceituarioPanel receituarioPanel = new ReceituarioPanel(prontuarioController.panelId(), origemPainelSoap, containerTelaSoap);
            prontuarioController.changePanel(target, receituarioPanel);
        }
    }

    @Override
    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<Receituario> lstReceituarios = salvarReceituario(target);
        RelatorioImprimirMultiplasReceitasDTOParam relatorioImprimirMultiplasReceitasDTOParam = new RelatorioImprimirMultiplasReceitasDTOParam();
        relatorioImprimirMultiplasReceitasDTOParam.setLstReceituario(lstReceituarios);
        relatorioImprimirMultiplasReceitasDTOParam.setAtendimento(noReceituarioDTO.getAtendimento());
        validarCamposUsuarioCadususDado(noReceituarioDTO.getUsuarioCadsusDado());
        salvarUsuarioCadsusDado(noReceituarioDTO.getUsuarioCadsusDado());

        if (existeReceituarioImprimir(lstReceituarios)) {
            AssinaturaDigitalUtilDTO dto = new AssinaturaDigitalUtilDTO();
            WindowUtil.addModal(target, ReceituarioTabbedPanel.this, dlgImpressaoDataReport = new DlgImpressaoDataReport<Receituario>(WindowUtil.newModalId(ReceituarioTabbedPanel.this), bundle("msgReceitaSalvaSucessoImprimir"), dto) {

                @Override
                public List<IReport> getDataReport(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                    List<DataReport> dataReports = null;
                    try {
                        dataReports = ReceituarioTabbedPanel.this.getDataReports(relatorioImprimirMultiplasReceitasDTOParam);
                    } catch (ReportException e) {
                        throw new ValidacaoException(e);
                    }

                    return new ArrayList<>(dataReports);
                }

                @Override
                public void onAssinarDigitalmente(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                    AbstractAssinaturaDigitalHelper helper = new AbstractAssinaturaDigitalService().carregarProvedorServico();
                    List<Receituario> lstReceituariosV2 = new ArrayList<>();
                    if (lstReceituarios.size() > 1) {
                        lstReceituariosV2.addAll(extractMedicamentosAssinatura(lstReceituarios));
                    } else {
                        lstReceituariosV2.addAll(lstReceituarios);
                    }
                    try {
                        for (Receituario receituario : lstReceituariosV2) {
                            if (!TipoReceita.RECEITA_BRANCA.equals(receituario.getTipoReceita().getTipoReceita())
                                    && !TipoReceita.RECEITA_BASICA.equals(receituario.getTipoReceita().getTipoReceita())
                                    && !TipoReceita.RECEITA_MAGISTRAL.equals(receituario.getTipoReceita().getTipoReceita())) {
                                throw new ValidacaoException("Assinatura Digital disponivel apenas para receita branca ou básica");
                            }

                            object = receituario;
                            dto.setAtendimento(noReceituarioDTO.getAtendimento());
                            dto.setHelper(helper);
                            dto.setDocumentoAssinado(object.getDocumentoAssinado());
                            dto.setOrigemTipoDocumento(DocumentoAssinado.OrigemTipoDocumento.RECEITA);
                            dto.setReceituario(object);

                            Usuario usuario = AssinaturaDigitalUtil.loadUsuario();
                            if (Usuario.TipoCertificado.PFX.value().equals(AssinaturaDigitalUtil.loadUsuario().getTipoCertificado())) {
                                assinarSalvarDocumento(dto, object, target, receituario);
                            } else {
                                DocumentoAssinado documentoAssinado = assinarSalvarDocumento(dto, object, null, receituario);

                                PscService pscService = AssinaturaDigitalUtil.preparePsc(documentoAssinado, usuario);
                                target.appendJavaScript("setTimeout(\"window.open('" + pscService.getAuthUrl() + "','_blank')\", 100);");
                            }
                        }
                    } catch (Exception e) {
                        throw new ValidacaoException(e);
                    }
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                    voltar(target);
                }
            });

            dlgImpressaoDataReport.show(target, noReceituarioDTO.getReceituario());
        } else {
            voltar(target);
        }
    }

    private List<Receituario> extractMedicamentosAssinatura(List<Receituario> receituarioList) {
        List<Receituario> auxList = new ArrayList<>();

        for (Receituario receituario : receituarioList) {
            if (!TipoReceita.RECEITA_BRANCA.equals(receituario.getTipoReceita().getTipoReceita())
                    && !TipoReceita.RECEITA_BASICA.equals(receituario.getTipoReceita().getTipoReceita())
                    && !TipoReceita.RECEITA_MAGISTRAL.equals(receituario.getTipoReceita().getTipoReceita())) {
                continue;
            }
            auxList.add(receituario);
        }

        return auxList;
    }

    private DocumentoAssinado assinarSalvarDocumento(AssinaturaDigitalUtilDTO dto, Receituario modelObject, AjaxRequestTarget target, Receituario receituario) throws ValidacaoException, DAOException, ReportException {
        modelObject.setToken(TokenGenerator.generateUniqueToken());
        BOFactory.save(modelObject);
        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        ImpressaoReceituarioDTOParam param1 = getDTOParam(receituario, dto);

        DataReport dataReport = BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param1);

        AssinaturaDigitalUtil util = new AssinaturaDigitalUtil(dto, dataReport);
        DocumentoAssinado da = util.assinarDocumentoDataReport();

        modelObject.setDocumentoAssinado(da);
        BOFactory.save(modelObject);

        if (target != null) {
            dlgImpressaoDataReport.close(target);
            util.exibirDocumento(target, ajaxPreviewBlank);
        }

        return da;
    }

    private ImpressaoReceituarioDTOParam getDTOParam(Receituario receituario, AssinaturaDigitalUtilDTO dto) {
        ImpressaoReceituarioDTOParam param1 = new ImpressaoReceituarioDTOParam();
        if (receituario != null && receituario.getCodigo() != null) {
            param1.setCodigoReceituario(receituario.getCodigo());
            param1.setTipoReceita(receituario.getTipoReceita().getTipoReceita());
            param1.setAtendimento(receituario.getAtendimento());
            param1.setAssinadoDigitalmente(true);
            param1.setDataAssinatura(new Date());

            return param1;
        }

        return new ImpressaoReceituarioDTOParam();
    }

    private List<Receituario> salvarReceituario(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (noReceituarioDTO.getReceituario().getProfissional() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_profissional"));
        }
        if (noReceituarioDTO.getAtendimento() == null) {
            noReceituarioDTO.setAtendimento(prontuarioController.getAtendimento());
        }
        if (noReceituarioDTO.getReceituario().getAtendimento() == null) {
            noReceituarioDTO.getReceituario().setAtendimento(prontuarioController.getAtendimento());
        }
        if (noReceituarioDTO.getReceituario().getUsuarioCadsus() == null) {
            noReceituarioDTO.getReceituario().setUsuarioCadsus(prontuarioController.getAtendimento().getUsuarioCadsus());
        }

        if (noReceituarioDTO.getDescricaoReceituario() != null) {
            List<ReceituarioItemDTO> itensDTO = new ArrayList<>();
            ReceituarioItem ri = new ReceituarioItem();
            ri.setDescricaoReceituario(noReceituarioDTO.getDescricaoReceituario());
            if (CollectionUtils.isNotNullEmpty(noReceituarioDTO.getReceituarioItemDTOList())) {
                ReceituarioItemDTO riDTO = new ReceituarioItemDTO();
                riDTO.setReceituarioItem(ri);
                noReceituarioDTO.getReceituarioItemDTOList().add(riDTO);
            } else {
                itensDTO.add(new ReceituarioItemDTO(ri));
                noReceituarioDTO.setReceituarioItemDTOList(itensDTO);
            }
        }

        List<Receituario> lstReceituarios = finalizarPrescricao(target);
        lstReceituariosVoltar = lstReceituarios;

        return lstReceituarios;
    }

    private void validarCamposUsuarioCadususDado(UsuarioCadsusDado usuarioCadsusDado) {
        if (usuarioCadsusDado != null) {
            if (noReceituarioDTO.getPeso() != null) usuarioCadsusDado.setPeso(noReceituarioDTO.getPeso());

            if (noReceituarioDTO.getAltura() != null) usuarioCadsusDado.setAltura(noReceituarioDTO.getAltura());
        }
    }

    private void salvarUsuarioCadsusDado(UsuarioCadsusDado usuarioCadsusDado) throws DAOException, ValidacaoException {
        if (usuarioCadsusDado != null) {
            BOFactoryWicket.save(usuarioCadsusDado);
        }
    }

    private List<DataReport> addRelatorioLaudoMedicamentosEspeciais(RelatorioImprimirMultiplasReceitasDTOParam param) throws ReportException {
        List<DataReport> lstDataReport = new ArrayList<DataReport>();
        if (CollectionUtils.isNotNullEmpty(param.getLstReceituario())) {
            List<Long> codigosLMEList = LoadManager.getInstance(ReceituarioItem.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(ReceituarioItem.PROP_LAUDO_MEDICAMENTO_ESPECIAL, LaudoMedicamentosEspeciais.PROP_CODIGO)))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, QueryCustom.QueryCustomParameter.IN, param.getLstReceituario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_LAUDO_MEDICAMENTO_ESPECIAL, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                    .start().getList();

            for (Long codigoLME : codigosLMEList) {
                DataReport dataReport = BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioLaudoMedicamentosEspeciais(new LaudoMedicamentosEspeciais(codigoLME));
                if (dataReport != null && dataReport.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReport);
                }
            }
        }
        return lstDataReport;
    }

    private List<DataReport> getDataReports(RelatorioImprimirMultiplasReceitasDTOParam object) throws ReportException {
        List<DataReport> lstDataReport = new ArrayList<>();
        List<ReceituarioItem> listComCid = new ArrayList<>();
        List<Receituario> receituariosBasico = new ArrayList<>();
        List<Receituario> receituariosAntibiotico = new ArrayList<>();
        List<Receituario> receituariosBranca = new ArrayList<>();
        List<Receituario> receituariosAmarela = new ArrayList<>();
        List<Receituario> receituariosAzul = new ArrayList<>();
        List<Receituario> receituariosLME = new ArrayList<>();
        List<Receituario> receituariosManipulados = new ArrayList<>();
        String separaReceituarioAutomaticamente = null;
        try {
            separaReceituarioAutomaticamente = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("separaReceituarioAutomaticamente");
        } catch (DAOException e) {
            separaReceituarioAutomaticamente = RepositoryComponentDefault.NAO;
        }
        DataReport dataReport = null;
        DataReport dataReportLandscape = null;
        RelatorioImprimirMultiplasReceitasDTOParam param = new RelatorioImprimirMultiplasReceitasDTOParam();
        param.setAtendimento(noReceituarioDTO.getAtendimento());
        if (RepositoryComponentDefault.SIM.equals(separaReceituarioAutomaticamente)) {
            for (Receituario receituario : object.getLstReceituario()) {
                switch (receituario.getTipoReceita().getTipoReceita()) {
                    case TipoReceita.RECEITA_BASICA:
                        receituariosBasico.add(receituario);
                        break;
                    case TipoReceita.RECEITA_ANTIMICROBIANA:
                        receituariosAntibiotico.add(receituario);
                        break;
                    case TipoReceita.RECEITA_BRANCA:
                        receituariosBranca.add(receituario);
                        break;
                    case TipoReceita.RECEITA_AMARELA:
                        receituariosAmarela.add(receituario);
                        break;
                    case TipoReceita.RECEITA_AZUL:
                        receituariosAzul.add(receituario);
                        break;
                    case TipoReceita.RECEITA_MAGISTRAL:
                        receituariosManipulados.add(receituario);
                        break;
                }
                for (ReceituarioItem receituarioItem : receituario.getReceituarioItemList()) {
                    if (receituarioItem.getCid() != null) {
                        listComCid.add(receituarioItem);
                    }
                    if (receituarioItem.getProduto() != null && RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getProduto().getFlagEmiteLme())) {
                        receituariosLME.add(receituario);
                    }
                }
            }
            if (CollectionUtils.isNotNullEmpty(listComCid)) {
                lstDataReport.add(BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoReceituarioCidInformado(listComCid));
            }
            lstDataReport = addReceituarioToDataReport(lstDataReport, receituariosBasico);
            lstDataReport = addReceituarioToDataReport(lstDataReport, receituariosAmarela);
            lstDataReport = addReceituarioToDataReport(lstDataReport, receituariosAzul);
            lstDataReport = addReceituarioToDataReport(lstDataReport, receituariosManipulados);
            param.setAntibiotico(false);
            if (exibeDadosPacienteIdentificacaoCompradorReceitaB()) {
                param.setExibeDadosPacienteIdentificacaoCompradorReceitaB(true);
            }
            if (exibeDadosPacienteIdentificadoMenor18anos()) {
                param.setExibedadospacientedidentificadomenor18anos(true);
            }
            if (CollectionUtils.isNotNullEmpty(receituariosBranca)) {
                param.setLstReceituario(receituariosBranca);
                DataReport dataReportBranca = BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitasLandscape(param);
                if (dataReportBranca != null && dataReportBranca.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReportBranca);
                }
            }
            if (CollectionUtils.isNotNullEmpty(receituariosLME)) {
                param.setLstReceituario(receituariosLME);
                addRelatorioLaudoMedicamentosEspeciais(lstDataReport, param);
            }
            if (CollectionUtils.isNotNullEmpty(receituariosAntibiotico)) {
                param.setLstReceituario(receituariosAntibiotico);
                param.setAntibiotico(true);
                DataReport dataReportAntimicrobiano = BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitasLandscape(param);
                if (dataReportAntimicrobiano != null && dataReportAntimicrobiano.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReportAntimicrobiano);
                }
            }
        } else {
            for (Receituario receituario : object.getLstReceituario()) {
                if (TipoReceita.RECEITA_BASICA.equals(receituario.getTipoReceita().getTipoReceita())) {
                    receituariosBasico.add(receituario);
                }
                if (TipoReceita.RECEITA_ANTIMICROBIANA.equals(receituario.getTipoReceita().getTipoReceita())) {
                    receituariosAntibiotico.add(receituario);
                }
                for (ReceituarioItem receituarioItem : receituario.getReceituarioItemList()) {
                    if (receituarioItem.getCid() != null) {
                        listComCid.add(receituarioItem);
                    }
                    if (receituarioItem.getProduto() != null && RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getProduto().getFlagEmiteLme())) {
                        receituariosLME.add(receituario);
                    }
                }
            }
            if (CollectionUtils.isNotNullEmpty(receituariosLME)) {
                param.setLstReceituario(receituariosLME);
                addRelatorioLaudoMedicamentosEspeciais(lstDataReport, param);
            }
            if (CollectionUtils.isNotNullEmpty(listComCid)) {
                lstDataReport.add(BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoReceituarioCidInformado(listComCid));
            }
            if (CollectionUtils.isNotNullEmpty(receituariosBasico)) {
                for (Receituario receituario : receituariosBasico) {
                    ImpressaoReceituarioDTOParam param1 = new ImpressaoReceituarioDTOParam();
                    param1.setCodigoReceituario(receituario.getCodigo());
                    param1.setTipoReceita(receituario.getTipoReceita().getTipoReceita());
                    param1.setAtendimento(noReceituarioDTO.getAtendimento());
                    lstDataReport.add(BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param1));
                    object.getLstReceituario().remove(receituario);
                }
            }
            if (exibeDadosPacienteIdentificacaoCompradorReceitaB()) {
                param.setExibeDadosPacienteIdentificacaoCompradorReceitaB(true);
            }
            if (exibeDadosPacienteIdentificadoMenor18anos()) {
                param.setExibedadospacientedidentificadomenor18anos(true);
            }
            if (CollectionUtils.isNotNullEmpty(receituariosAntibiotico)) {
                param.setLstReceituario(receituariosAntibiotico);
                param.setAntibiotico(true);
                DataReport dataReportAntimicrobiano = BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitasLandscape(param);
                if (dataReportAntimicrobiano != null && dataReportAntimicrobiano.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReportAntimicrobiano);
                }
            }
            param.setAntibiotico(false);
            List<Receituario> lstReceituario = object.getLstReceituario();
            param.setLstReceituario(lstReceituario);
            param.getLstReceituario().removeAll(receituariosAntibiotico);
            dataReport = BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitas(param);
            dataReportLandscape = BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoMultiplasReceitasLandscape(param);
            if (dataReport != null && dataReport.getJasperPrint().getPages().size() > 0) {
                lstDataReport.add(dataReport);
            }
            if (dataReportLandscape != null && dataReportLandscape.getJasperPrint().getPages().size() > 0) {
                lstDataReport.add(dataReportLandscape);
            }
            param.getLstReceituario().addAll(receituariosAntibiotico);
        }
        return lstDataReport;
    }

    private List<DataReport> addReceituarioToDataReport(List<DataReport> lstDataReport, List<Receituario> receituarios) throws ReportException {
        if (CollectionUtils.isNotNullEmpty(receituarios)) {
            for (Receituario receituario : receituarios) {
                ImpressaoReceituarioDTOParam param1 = new ImpressaoReceituarioDTOParam();
                param1.setCodigoReceituario(receituario.getCodigo());
                param1.setTipoReceita(receituario.getTipoReceita().getTipoReceita());
                param1.setAtendimento(noReceituarioDTO.getAtendimento());
                lstDataReport.add(BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituario(param1));
            }
        }
        return lstDataReport;
    }

    private boolean exibeDadosPacienteIdentificacaoCompradorReceitaB() {
        boolean exibeDadosPacienteIdentificacaoCompradorReceitaB = false;
        try {
            exibeDadosPacienteIdentificacaoCompradorReceitaB = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeDadosPacienteIdentificacaoCompradorReceitaB"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        return exibeDadosPacienteIdentificacaoCompradorReceitaB;
    }

    private boolean exibeDadosPacienteIdentificadoMenor18anos() {
        boolean exibeDadosPacienteIdentificadoMenor18anos = false;
        try {
            exibeDadosPacienteIdentificadoMenor18anos = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeDadosPacienteIdentificadoMenor18anos"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        return exibeDadosPacienteIdentificadoMenor18anos || noReceituarioDTO.getUsuarioCadsus().getIdade() >= 18L;
    }

    private void addRelatorioLaudoMedicamentosEspeciais(List<DataReport> lstDataReport, RelatorioImprimirMultiplasReceitasDTOParam param) throws ReportException {
        if (CollectionUtils.isNotNullEmpty(param.getLstReceituario())) {
            List<Long> codigosLMEList = LoadManager.getInstance(ReceituarioItem.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(ReceituarioItem.PROP_LAUDO_MEDICAMENTO_ESPECIAL, LaudoMedicamentosEspeciais.PROP_CODIGO)))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, QueryCustom.QueryCustomParameter.IN, param.getLstReceituario()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_LAUDO_MEDICAMENTO_ESPECIAL, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                    .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                    .start().getList();

            for (Long codigoLME : codigosLMEList) {
                DataReport dataReport = BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioLaudoMedicamentosEspeciais(new LaudoMedicamentosEspeciais(codigoLME));
                if (dataReport != null && dataReport.getJasperPrint().getPages().size() > 0) {
                    lstDataReport.add(dataReport);
                }
            }
        }
    }

    @Override
    protected Component newButtonBar(String id) {
        cadastroReceituarioTabbedPanelBar = new CadastroReceituarioTabbedPanelBar(id, this, isViewOnly(), isButtonBackHistory()) {

            @Override
            public Class getResponsePage() {
                return ReceituarioTabbedPanel.this.getResponsePage();
            }

            public void salvarImprimir(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                try {
                    salvarImprimirReceituario(target);
                    if (dlgImpressaoDataReport == null) {
                        initDlgConfirmacaoOk(target);
                    }
                } catch (IOException | ReportException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        };

        return cadastroReceituarioTabbedPanelBar;
    }

    public void salvarImprimirReceituario(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException, IOException {
        List<Receituario> lstReceituarios = salvarReceituario(target);
        RelatorioImprimirMultiplasReceitasDTOParam relatorioImprimirMultiplasReceitasDTOParam = new RelatorioImprimirMultiplasReceitasDTOParam();
        relatorioImprimirMultiplasReceitasDTOParam.setLstReceituario(lstReceituarios);
        relatorioImprimirMultiplasReceitasDTOParam.setAtendimento(noReceituarioDTO.getAtendimento());
        validarCamposUsuarioCadususDado(noReceituarioDTO.getUsuarioCadsusDado());
        salvarUsuarioCadsusDado(noReceituarioDTO.getUsuarioCadsusDado());

        if (new AssinaturaDigitalBry().isAssinaturaEnabled()) {
            if (existeReceituarioImprimir(lstReceituarios)) {
                AssinaturaDigitalUtilDTO dto = new AssinaturaDigitalUtilDTO();
                WindowUtil.addModal(target, ReceituarioTabbedPanel.this, dlgImpressaoDataReport = new DlgImpressaoDataReport<Receituario>(WindowUtil.newModalId(ReceituarioTabbedPanel.this), bundle("msgReceitaSalvaSucessoImprimir"), dto) {

                    @Override
                    public List<IReport> getDataReport(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                        List<DataReport> reports = null;
                        try {
                            reports = getDataReports(relatorioImprimirMultiplasReceitasDTOParam);
                            List<IReport> iReportList = new ArrayList<>(reports);
                            initDlgConfirmacaoOk(target);
                            return iReportList;
                        } catch (ReportException e) {
                            throw new ValidacaoException(e);
                        }
                    }

                    @Override
                    public void onAssinarDigitalmente(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                        AbstractAssinaturaDigitalHelper helper = new AbstractAssinaturaDigitalService().carregarProvedorServico();
                        List<Receituario> lstReceituariosV2 = new ArrayList<>();
                        if (lstReceituarios.size() > 1) {
                            lstReceituariosV2.addAll(extractMedicamentosAssinatura(lstReceituarios));
                        } else {
                            lstReceituariosV2.addAll(lstReceituarios);
                        }
                        try {
                            for (Receituario receituario : lstReceituariosV2) {
                                if (!TipoReceita.RECEITA_BRANCA.equals(receituario.getTipoReceita().getTipoReceita())
                                        && !TipoReceita.RECEITA_BASICA.equals(receituario.getTipoReceita().getTipoReceita())
                                        && !TipoReceita.RECEITA_MAGISTRAL.equals(receituario.getTipoReceita().getTipoReceita())) {
                                    throw new ValidacaoException("Assinatura Digital disponivel apenas para receita branca ou básica");
                                }
                                object = receituario;
                                dto.setAtendimento(noReceituarioDTO.getAtendimento());
                                dto.setHelper(helper);
                                dto.setDocumentoAssinado(receituario.getDocumentoAssinado());
                                dto.setOrigemTipoDocumento(DocumentoAssinado.OrigemTipoDocumento.RECEITA);
                                dto.setReceituario(receituario);
//                        dto.setTipoDocumento(object.getTipoDocumentoAtendimento().getDescricao());

                                Usuario usuario = AssinaturaDigitalUtil.loadUsuario();
                                if (Usuario.TipoCertificado.PFX.value().equals(AssinaturaDigitalUtil.loadUsuario().getTipoCertificado())) {
                                    assinarSalvarDocumento(dto, object, target, receituario);
                                } else {
                                    DocumentoAssinado documentoAssinado = assinarSalvarDocumento(dto, object, null, receituario);

                                    PscService pscService = AssinaturaDigitalUtil.preparePsc(documentoAssinado, usuario);
                                    target.appendJavaScript("setTimeout(\"window.open('" + pscService.getAuthUrl() + "','_blank')\", 100);");
                                }
                            }
                        } catch (Exception e) {
                            throw new ValidacaoException(e);
                        }
                    }

                    @Override
                    public void onFechar(AjaxRequestTarget target, Receituario object) throws ValidacaoException, DAOException {
                        voltar(target);
                    }
                });

                dlgImpressaoDataReport.show(target, noReceituarioDTO.getReceituario());
            }
        } else {
            if (existeReceituarioImprimir(lstReceituarios)) {

                List<DataReport> reports = getDataReports(relatorioImprimirMultiplasReceitasDTOParam);
                File filePrint;
                for (DataReport report : reports) {
                    filePrint = criaFileReport(report.getJasperPrint(), TipoRelatorio.PDF);
                    openResource(target, filePrint);
                }
            }
            initDlgConfirmacaoOk(target);
        }
    }

    public void initDlgConfirmacaoOk(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoOk = new DlgConfirmacaoOk(WindowUtil.newModalId(this), Bundle.getStringApplication("msg_salvar_imprimir_receituario"), 70, 400) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    if (dlgImpressaoDataReport != null) {
                        dlgImpressaoDataReport.close(target);
                    }
                    voltar(target);
                }
            });
        }
        dlgConfirmacaoOk.show(target);
    }

    public File criaFileReport(JasperPrint jasperPrint, TipoRelatorio tipoRelatorio) throws IOException {
        File newFile = File.createTempFile("receituario", tipoRelatorio.descricao());

        try (OutputStream outputStream = Files.newOutputStream(newFile.toPath())) {
            JRExporter exporter = new net.sf.jasperreports.engine.export.JRPdfExporter();
            exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, outputStream);

            exporter.exportReport();
            return newFile;
        } catch (JRException ex) {
            Loggable.log.error("Erro ao gerar relatório: " + ex.getMessage(), ex);
        }

        return null;
    }

    public boolean existeReceituarioImprimir(List<Receituario> lstReceituarios) {
        List<Long> extract = Lambda.extract(lstReceituarios, Lambda.on(Receituario.class).getCodigo());
        if (CollectionUtils.isNotNullEmpty(extract)) {
            List<Receituario> lstReceituariosTipoReceita = LoadManager.getInstance(Receituario.class)
                    .addProperty(VOUtils.montarPath(Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_FLAG_IMPRIMIR_RECEITA))
                    .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, extract))
                    .start().getList();
            for (Receituario receituario : lstReceituariosTipoReceita) {
                if (RepositoryComponentDefault.SIM.equals(receituario.getTipoReceita().getFlagImprimirReceita())) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<Receituario> finalizarPrescricao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (CollectionUtils.isAllEmpty(noReceituarioDTO.getReceituarioItemDTOList())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nenhum_medicamento_informado"));
        }
        if (noReceituarioDTO.getReceituario().getDocumentoAssinado() != null) {
            noReceituarioDTO.getReceituario().getDocumentoAssinado().setDocumentoOrigem(noReceituarioDTO.getReceituario().getCodigo());
            BOFactory.save(noReceituarioDTO.getReceituario().getDocumentoAssinado());
            noReceituarioDTO.getReceituario().setDocumentoAssinado(null);
        }
        return BOFactoryWicket.getBO(ReceituarioFacade.class).salvarReceitaBranca(noReceituarioDTO);
    }
}
