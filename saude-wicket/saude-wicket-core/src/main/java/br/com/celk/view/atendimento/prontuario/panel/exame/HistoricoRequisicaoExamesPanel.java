package br.com.celk.view.atendimento.prontuario.panel.exame;

import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioHistoricoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.interfaces.IVoltarHistoricoAction;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.bo.prontuario.web.exame.dto.QueryConsultaHistoricoExamesDTO;
import br.com.ksisolucoes.bo.prontuario.web.exame.dto.QueryConsultaHistoricoExamesDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class HistoricoRequisicaoExamesPanel extends ProntuarioHistoricoPanel{
    
    private Form<QueryConsultaHistoricoExamesDTOParam> form;
    private IVoltarHistoricoAction voltarHistoricoAction;
    private Table tblExames;

    public HistoricoRequisicaoExamesPanel(String id) {
        super(id, BundleManager.getString("requisicaoExames"));
    }
    
    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form("form", new CompoundPropertyModel<QueryConsultaHistoricoExamesDTOParam>(new QueryConsultaHistoricoExamesDTOParam()));
        QueryConsultaHistoricoExamesDTOParam proxyParam = on(QueryConsultaHistoricoExamesDTOParam.class);
        
        form.getModelObject().setAtendimento(getAtendimento());
        form.add(getCbxPeriodo(path(proxyParam.getMeses()))); 
        form.add(getCbxTipoExame(path(proxyParam.getTipoExame())));
        form.add(tblExames = new Table("tblExames", getColumns(), getCollectionProvider()));
        tblExames.populate();
        
        add(form);
    }
    
    private DropDown<Integer> getCbxPeriodo(String id){
        DropDown<Integer> cbxPeriodo = new DropDown<Integer>(id);
        
        cbxPeriodo.addChoice(3, bundle("ultimos90dias"));
        cbxPeriodo.addChoice(6, bundle("ultimos6meses"));
        cbxPeriodo.addChoice(12, bundle("ultimoAno"));
        cbxPeriodo.addChoice(null, bundle("todos"));
        
        cbxPeriodo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });
        
        return cbxPeriodo;
    }
    
    private List<IColumn> getColumns(){
        QueryConsultaHistoricoExamesDTO on = on(QueryConsultaHistoricoExamesDTO.class);
        List<IColumn> columns = new ArrayList<IColumn>();
        
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("tipoExame"), on.getDescricaoTipoExame()));
        columns.add(createColumn(bundle("examesSolicitados"), on.getReferencias()));
        
        return columns;
    }
    
    private IColumn getCustomColumn(){
        return new MultipleActionCustomColumn<QueryConsultaHistoricoExamesDTO>() {

            @Override
            public void customizeColumn(QueryConsultaHistoricoExamesDTO rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<QueryConsultaHistoricoExamesDTO>() {

                    @Override
                    public DataReport action(QueryConsultaHistoricoExamesDTO modelObject) throws ReportException {
                        ImpressaoExameDTOParam param = new ImpressaoExameDTOParam();
                        List<Long> codigo = new ArrayList<Long>();
                        codigo.add(modelObject.getCodigoExame());
                        param.setCodigosExames(codigo);
                        param.setTipoConvenio(TipoExame.CONVENIO_NAO_SUS);
                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoRequisicaoExame(param);
                    }

                });
            }
        };
    }
    
    private DropDown<TipoExame> getCbxTipoExame(String id){
        DropDown<TipoExame> cbxTipoExame = new DropDown<TipoExame>(id);
        List<TipoExame> consultarTipoExame = LoadManager.getInstance(TipoExame.class)
                .addProperties(new HQLProperties(TipoExame.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TipoExame.PROP_DESCRICAO)))
                .start().getList();

        cbxTipoExame.addChoice(null, bundle("todos"));

        for (TipoExame tipoExame : consultarTipoExame) {
            cbxTipoExame.addChoice(tipoExame, tipoExame.getDescricao());
        }
        
        cbxTipoExame.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });
        
        return cbxTipoExame;
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {              
                return BOFactoryWicket.getBO(ExameFacade.class).consultaHistoricoExames(form.getModelObject());
            }
        };
    }
    
    private void procurar(AjaxRequestTarget target){
        tblExames.populate(target);
    }
    
    @Override
    public DefaultProntuarioPanel newProntuarioPanel(String id) {
        return voltarHistoricoAction.getPanelVoltar(id);
    }

    public void setVoltarHistoricoAction(IVoltarHistoricoAction voltarHistoricoAction) {
        this.voltarHistoricoAction = voltarHistoricoAction;
    }
}
