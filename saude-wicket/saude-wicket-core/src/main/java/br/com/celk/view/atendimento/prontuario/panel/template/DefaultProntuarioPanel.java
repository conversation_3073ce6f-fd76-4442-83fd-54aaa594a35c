package br.com.celk.view.atendimento.prontuario.panel.template;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.view.atendimento.prontuario.interfaces.IProntuarioController;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DefaultProntuarioPanel extends ViewPanel{
    private NodesAtendimentoRef identificador;
    private IProntuarioController prontuarioController;

    public DefaultProntuarioPanel(String id) {
        super(id);
    }

    public Atendimento getAtendimento() {
        return getProntuarioController().getAtendimento();
    }

    public void setIdentificador(NodesAtendimentoRef tempGroup) {
        this.identificador = tempGroup;
    }

    public NodesAtendimentoRef getIdentificador() {
        return identificador;
    }

    public IProntuarioController getProntuarioController() {
        return prontuarioController;
    }
    
    public void setProntuarioController(IProntuarioController prontuarioController) {
        this.prontuarioController = prontuarioController;
    }

    public abstract void changePanelAction(AjaxRequestTarget target);

}
