package br.com.celk.view.atendimento.prontuario.panel.exame.presenter;

import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame;
import java.io.Serializable;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface IExamePresenter extends Serializable{

    public void setExame(Exame exame);
    
    public List<ExameProcedimentoDTO> itensList();
    
    public void finalizarExame(AjaxRequestTarget target, ExameCadastroAprovacaoDTO dto) throws ValidacaoException, DAOException;
    
    public Long getTipoConvenio();

    public void adicionarItem(AjaxRequestTarget target, ExameProcedimentoDTO exameRequisicao) throws ValidacaoException, DAOException;
    
    public void adicionarRequisicaoPadraoExame(AjaxRequestTarget target, List<RequisicaoPadraoExame> requisicaoPadraoExameList) throws ValidacaoException, DAOException;

    public void editarItem(AjaxRequestTarget target, ExameProcedimentoDTO exameRequisicao) throws ValidacaoException, DAOException;

    public void removerItem(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException;

    public void validarExameParaAdicionar(ExameProcedimentoDTO exameProcedimentoDTO)throws ValidacaoException, DAOException;
}
