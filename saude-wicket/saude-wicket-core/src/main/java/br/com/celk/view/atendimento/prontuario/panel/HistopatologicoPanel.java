package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.report.prontuario.programasaude.interfaces.RelatorioCadastroPreventivoDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class HistopatologicoPanel extends ProntuarioCadastroPanel {

    private Form<Preventivo> form;
    private RadioButtonGroup radioGroupMotivoExame;
    private RadioButtonGroup radioGroupFezPreventivo;
    private RadioButtonGroup radioGroupUsaDiu;
    private RadioButtonGroup radioGroupEstaGravida;
    private RadioButtonGroup radioGroupUsaPilulaAnticoncepcional;
    private RadioButtonGroup radioGroupUsaHormonioRemedioMenopausa;
    private RadioButtonGroup radioGroupTratamentoRadioterapia;
    private RadioButtonGroup radioGroupSangramentoAposRelacao;
    private RadioButtonGroup radioGroupSangramentoAposMenopausa;
    private RadioButtonGroup radioGroupExameColo;
    private RadioButtonGroup radioGroupExameDoencaSexualmenteTransmissivel;
    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;
    private DropDown<Long> dropDownAnoQuandoFez;
    private DateChooser dchDataUltimaMenstruacao;
    private CheckBoxSimNao checkBoxLembraDataUltimaMenstruacao;

    private Exame exame;
    private TipoExame tipoExame;
    private Atendimento atendimento;
    private ExameRequisicao exameRequisicao;
    private boolean visible;

    private AbstractAjaxButton btnVoltar;
    private AbstractAjaxButton btnGerarRequisicao;

    public HistopatologicoPanel(String id, TipoExame tipoExame) {
        super(id, bundle("requisicaoExameHistopatologicoColoUtero"));
        this.tipoExame = tipoExame;
    }

    public HistopatologicoPanel(String id, Exame exame, TipoExame tipoExame) {
        super(id, bundle("requisicaoExameHistopatologicoColoUtero"));
        this.exame = exame;
        this.tipoExame = tipoExame;
    }

    public HistopatologicoPanel(String id, ExameRequisicao exameRequisicao, boolean visible) {
        super(id, bundle("requisicaoExameHistopatologicoColoUtero"));
        this.exame = exameRequisicao.getExame();
        this.tipoExame = exameRequisicao.getExame().getTipoExame();
        this.exameRequisicao = exameRequisicao;
        this.visible = visible;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Preventivo proxyPreventivo = on(Preventivo.class);

        getForm().add(radioGroupMotivoExame = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getMotivoExame())));
        radioGroupMotivoExame.add(new AjaxRadio("rastreamento", new Model((String) Preventivo.MotivoExame.RASTREAMENTO.value())));
        radioGroupMotivoExame.add(new AjaxRadio("repeticao", new Model((String) Preventivo.MotivoExame.REPETICAO.value())));
        radioGroupMotivoExame.add(new AjaxRadio("seguimento", new Model((String) Preventivo.MotivoExame.SEGUIMENTO.value())));

        getForm().add(radioGroupFezPreventivo = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getFezPreventivo())));
        radioGroupFezPreventivo.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                dropDownAnoQuandoFez.setEnabled(true);
                target.add(dropDownAnoQuandoFez);
            }
        });
        radioGroupFezPreventivo.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                dropDownAnoQuandoFez.setEnabled(false);
                target.add(dropDownAnoQuandoFez);
            }
        });
        radioGroupFezPreventivo.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                dropDownAnoQuandoFez.setEnabled(false);
                target.add(dropDownAnoQuandoFez);
            }
        });
        radioGroupFezPreventivo.add(dropDownAnoQuandoFez = DropDownUtil.getAnoDropDown(path(proxyPreventivo.getAnoQuandoFez()), false, false));

        getForm().add(radioGroupUsaDiu = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getUsaDiu())));
        radioGroupUsaDiu.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupUsaDiu.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupUsaDiu.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupEstaGravida = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getEstaGravida())));
        radioGroupEstaGravida.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupEstaGravida.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupEstaGravida.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupUsaPilulaAnticoncepcional = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getUsaPilulaAnticoncepcional())));
        radioGroupUsaPilulaAnticoncepcional.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupUsaPilulaAnticoncepcional.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupUsaPilulaAnticoncepcional.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupUsaHormonioRemedioMenopausa = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getUsaHormonioRemedioMenopausa())));
        radioGroupUsaHormonioRemedioMenopausa.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupUsaHormonioRemedioMenopausa.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupUsaHormonioRemedioMenopausa.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupTratamentoRadioterapia = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getTratamentoRadioterapia())));
        radioGroupTratamentoRadioterapia.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupTratamentoRadioterapia.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupTratamentoRadioterapia.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(dchDataUltimaMenstruacao = new DateChooser(path(proxyPreventivo.getDataUltimaMenstruacao())));
        getForm().add(checkBoxLembraDataUltimaMenstruacao = new CheckBoxSimNao(path(proxyPreventivo.getLembraDataUltimaMenstruacao())) {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM.equals(checkBoxLembraDataUltimaMenstruacao.getComponentValue())) {
                    dchDataUltimaMenstruacao.setEnabled(false);
                    dchDataUltimaMenstruacao.setComponentValue(null);
                } else {
                    dchDataUltimaMenstruacao.setEnabled(true);
                }
                target.add(dchDataUltimaMenstruacao);
                target.add(checkBoxLembraDataUltimaMenstruacao);
                target.appendJavaScript(JScript.initMasks());
            }

        });

        getForm().add(radioGroupSangramentoAposRelacao = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getSangramentoAposRelacao())));
        radioGroupSangramentoAposRelacao.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupSangramentoAposRelacao.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupSangramentoAposRelacao.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupSangramentoAposMenopausa = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getSangramentoAposMenopausa())));
        radioGroupSangramentoAposMenopausa.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupSangramentoAposMenopausa.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));
        radioGroupSangramentoAposMenopausa.add(new AjaxRadio("naoSabe", new Model((String) RepositoryComponentDefault.NAO_SABE)));

        getForm().add(radioGroupExameColo = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getExameColo())));
        radioGroupExameColo.add(new AjaxRadio("normal", new Model((String) Preventivo.ExameColo.NORMAL.value())));
        radioGroupExameColo.add(new AjaxRadio("ausente", new Model((String) Preventivo.ExameColo.AUSENTE.value())));
        radioGroupExameColo.add(new AjaxRadio("alterado", new Model((String) Preventivo.ExameColo.ALTERADO.value())));
        radioGroupExameColo.add(new AjaxRadio("coloNaoVisualizado", new Model((String) Preventivo.ExameColo.COLO_NAO_VISUALIZADO.value())));

        getForm().add(radioGroupExameDoencaSexualmenteTransmissivel = (RadioButtonGroup) new RadioButtonGroup(path(proxyPreventivo.getExameDoencaSexualmenteTransmissivel())));
        radioGroupExameDoencaSexualmenteTransmissivel.add(new AjaxRadio("sim", new Model((String) RepositoryComponentDefault.SIM)));
        radioGroupExameDoencaSexualmenteTransmissivel.add(new AjaxRadio("nao", new Model((String) RepositoryComponentDefault.NAO)));

        getForm().add(new InputArea(path(proxyPreventivo.getObservacao())));

        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SaudeMulherPanel(getProntuarioController().panelId()));
            }
        });

        getForm().add(btnGerarRequisicao = new AbstractAjaxButton("btnGerarRequisicao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarGerarRequisicao(target);
            }
        });
        
        if (visible) {
            btnGerarRequisicao.setVisible(false);
            btnVoltar.setVisible(false);
        }

        add(getForm());

        if (this.exame != null) {
            carregarPreventivo();
        }

        disabledForm();
    }

    private void salvarGerarRequisicao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (tipoExame.getExameProcedimentoPadrao() == null) {
            throw new ValidacaoException(bundle("tipoExameSemProcedimentoPadrao", this));
        }

        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();
        if (this.exame != null) {
            dto.setCodigoExameCadastrado(this.exame.getCodigo());
            dto.setDataSolicitacao(this.exame.getDataSolicitacao());
            dto.setAtendimento(this.exame.getAtendimento());
            dto.setCodigoUnidade(this.exame.getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(this.exame.getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(this.exame.getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(this.exame.getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(this.exame.getAtendimento().getUsuarioCadsus().getNomeSocial());
        } else {
            dto.setDataSolicitacao(DataUtil.getDataAtual());
            dto.setAtendimento(getAtendimento());
            dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
            dto.setOrigem(Exame.Origem.SAUDE_MULHER.value());
        }

        ExameProcedimentoDTO exameProcedimento = new ExameProcedimentoDTO();

        exameProcedimento.setExameProcedimento(tipoExame.getExameProcedimentoPadrao());
        exameProcedimento.setQuantidade(1L);
        exameProcedimento.setComplemento(null);
        exameProcedimento.setValor(0D);

        List<ExameProcedimentoDTO> item = new ArrayList<ExameProcedimentoDTO>();
        item.add(exameProcedimento);

        dto.setExameProcedimentoDTOs(item);

        Preventivo preventivo = getForm().getModelObject();
        validarCadastro(preventivo);

        preventivo.setAtendimento(getAtendimento());
        preventivo.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        preventivo.setProfissional(getAtendimento().getProfissional());

        if (dchDataUltimaMenstruacao.getComponentValue() != null) {
            preventivo.setLembraDataUltimaMenstruacao(null);
        }

        Long codigoExame = BOFactoryWicket.getBO(ExameFacade.class).cadastrarSolicitacaoExamePreventivo(dto, preventivo);
        viewDialogImpressao(target, codigoExame);
    }

    private void viewDialogImpressao(AjaxRequestTarget target, Long codigoExame) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), bundle("desejaImprimirRequisicao", this)) {
                @Override
                public DataReport getDataReport(Long codigoExame) throws ReportException {
                    RelatorioCadastroPreventivoDTOParam param = new RelatorioCadastroPreventivoDTOParam();
                    param.setAtendimento(getAtendimento());
                    param.setCodigoExame(codigoExame);

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioCadastroHistopatologico(param, false);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long object) throws ValidacaoException, DAOException {
                    DefaultProntuarioPanel panel = new SaudeMulherPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, panel);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
        dlgConfirmacaoImpressao.show(target, codigoExame);
    }

    private Form<Preventivo> getForm() {
        if (this.form == null) {
            this.form = new Form<Preventivo>("form", new CompoundPropertyModel<Preventivo>(new Preventivo()));
        }
        return this.form;
    }

    private void validarCadastro(Preventivo preventivo) throws ValidacaoException {
        if (preventivo.getMotivoExame() == null || preventivo.getFezPreventivo() == null || preventivo.getUsaDiu() == null || preventivo.getEstaGravida() == null
                || preventivo.getUsaPilulaAnticoncepcional() == null || preventivo.getUsaHormonioRemedioMenopausa() == null || preventivo.getTratamentoRadioterapia() == null
                || (preventivo.getDataUltimaMenstruacao() == null && (RepositoryComponentDefault.NAO.equals(checkBoxLembraDataUltimaMenstruacao.getComponentValue())
                || checkBoxLembraDataUltimaMenstruacao.getComponentValue() == null))
                || preventivo.getSangramentoAposRelacao() == null || preventivo.getSangramentoAposMenopausa() == null
                || preventivo.getExameColo() == null || preventivo.getExameDoencaSexualmenteTransmissivel() == null) {
            throw new ValidacaoException(bundle("informeTodosDadosCadastro", this));
        }

        if (RepositoryComponentDefault.SIM.equals(preventivo.getFezPreventivo()) && preventivo.getAnoQuandoFez() == null) {
            throw new ValidacaoException(bundle("informeAno", this));
        }
    }

    private void carregarPreventivo() {
        if (this.exame != null) {
            atendimento = exame.getAtendimento();
        } else {
            atendimento = getAtendimento();
        }
        if (exameRequisicao == null) {
            exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                    .addProperty(ExameRequisicao.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, tipoExame.getExameProcedimentoPadrao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), atendimento.getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), ExameRequisicao.Status.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Exame.STATUS_AUTORIZADO, Exame.STATUS_DESVINCULADO, Exame.STATUS_RECEBIDO, Exame.STATUS_SOLICITADO)))
                    .start().getVO();
        }

        if (exameRequisicao != null) {
            Preventivo preventivo = (Preventivo) LoadManager.getInstance(Preventivo.class)
                    .addProperties(new HQLProperties(Preventivo.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Preventivo.PROP_EXAME_REQUISICAO, ExameRequisicao.PROP_CODIGO), exameRequisicao.getCodigo()))
                    .start().getVO();

            if (preventivo != null) {
                getForm().setModelObject(preventivo);
            }
        }
    }

    private void disabledForm() {
        dropDownAnoQuandoFez.setEnabled(false);

        if (RepositoryComponentDefault.SIM.equals(getForm().getModelObject().getFezPreventivo())) {
            dropDownAnoQuandoFez.setEnabled(true);
        }

        if (RepositoryComponentDefault.SIM.equals(getForm().getModelObject().getLembraDataUltimaMenstruacao())) {
            dchDataUltimaMenstruacao.setEnabled(false);
        }
    }

    public AbstractAjaxButton getBtnVoltar() {
        return btnVoltar;
    }

    public AbstractAjaxButton getBtnGerarRequisicao() {
        return btnGerarRequisicao;
    }
}
