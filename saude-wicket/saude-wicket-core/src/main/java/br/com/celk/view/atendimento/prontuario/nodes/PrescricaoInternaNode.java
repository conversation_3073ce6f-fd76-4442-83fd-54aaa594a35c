package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.PrescricaoInternaPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.PRESCRICAO_INTERNA)
public class PrescricaoInternaNode extends ProntuarioNodeImp {

    @Override
    public DefaultProntuarioPanel getPanel(String id) {
        return new PrescricaoInternaPanel(id, getTitulo());
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("prescricaoInterna");
    }
    
    @Override
    public Icon32 getIcone() {
        return Icon32.MEDICAL_POT_PILLS;
    }
    
}
