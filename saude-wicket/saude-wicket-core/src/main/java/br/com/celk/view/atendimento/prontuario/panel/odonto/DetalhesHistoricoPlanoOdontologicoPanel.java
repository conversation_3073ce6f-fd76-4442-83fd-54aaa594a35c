package br.com.celk.view.atendimento.prontuario.panel.odonto;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.view.atendimento.prontuario.panel.HistoricoOdontologicoPanel;
import br.com.celk.view.atendimento.prontuario.panel.OdontogramaHistoricoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.HistoricoOdontologicoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DetalhesHistoricoPlanoOdontologicoPanel extends ProntuarioCadastroPanel {

    private Form form;
    private HistoricoOdontologicoDTO historicoOdontologicoDTO;
    private List<AtendimentoItem> list;

    public DetalhesHistoricoPlanoOdontologicoPanel(String id, HistoricoOdontologicoDTO historicoOdontologicoDTO) {
        super(id, bundle("detalhesTratamento"));
        this.historicoOdontologicoDTO = historicoOdontologicoDTO;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        HistoricoOdontologicoDTO proxy = on(HistoricoOdontologicoDTO.class);

        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getDescricaoLocalFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getSituacaoDente().getDescricaoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getDescricaoStatus())));

        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getDataCadastro())));
        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getAtendimento().getProfissional().getNome())));
        getForm().add(new DisabledInputArea(path(proxy.getAtendimentoOdontoPlano().getObservacao())));

        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoExecucao().getDataCadastro())));
        getForm().add(new DisabledInputField(path(proxy.getAtendimentoOdontoExecucao().getAtendimento().getProfissional().getNome())));
        getForm().add(new DisabledInputArea(path(proxy.getAtendimentoOdontoExecucao().getDescricaoTrabalho())));

        getForm().add(new Table("table", getColumns(), getCollectionProvider()).populate());

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                OdontogramaHistoricoPanel odontogramaHistoricoPanel = new OdontogramaHistoricoPanel(getProntuarioController().panelId(), bundle("historicoOdontograma"));
                getProntuarioController().changePanel(target, odontogramaHistoricoPanel);
            }
        });

        add(getForm());
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return list;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        AtendimentoItem proxy = on(AtendimentoItem.class);

        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimentoCompetencia().getId().getProcedimento().getDescricaoFormatado()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private Form<ExameRequisicao> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(historicoOdontologicoDTO));
            carregarProcedimentos();
        }
        return this.form;
    }

    private void carregarProcedimentos() {
        list = new ArrayList();
        if (historicoOdontologicoDTO.getAtendimentoOdontoExecucao() != null) {
            AtendimentoItem proxy = on(AtendimentoItem.class);
            list = LoadManager.getInstance(AtendimentoItem.class)
                    .addProperty(path(proxy.getCodigo()))
                    .addProperty(path(proxy.getDataHoraAplicacao()))
                    .addProperty(path(proxy.getQuantidade()))
                    .addProperty(path(proxy.getProcedimentoCompetencia().getId().getProcedimento().getCodigo()))
                    .addProperty(path(proxy.getProcedimentoCompetencia().getId().getProcedimento().getReferencia()))
                    .addProperty(path(proxy.getProcedimentoCompetencia().getId().getProcedimento().getDescricao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoOdontoExecucao()), historicoOdontologicoDTO.getAtendimentoOdontoExecucao()))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getProcedimentoCompetencia().getId().getProcedimento().getDescricao())))
                    .start().getList();

        }
    }
}
