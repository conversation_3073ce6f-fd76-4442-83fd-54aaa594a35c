package br.com.celk.view.basico.integracaosimas.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.integracaosimas.autocomplete.restricaocontainer.RestricaoContainerIndicadorSimas;
import br.com.celk.view.basico.integracaosimas.autocomplete.settings.IndicadorSimasAutoCompleteSettings;
import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaIndicadorSimasDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.geral.IndicadorSimas;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaIndicadorSimas extends AutoCompleteConsulta<IndicadorSimas> {


    public AutoCompleteConsultaIndicadorSimas(String id) {
        super(id);
    }

    public AutoCompleteConsultaIndicadorSimas(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaIndicadorSimas(String id, IModel<IndicadorSimas> model) {
        super(id, model);
    }

    public AutoCompleteConsultaIndicadorSimas(String id, IModel<IndicadorSimas> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(IndicadorSimas.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(IndicadorSimas.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(IndicadorSimas.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerIndicadorSimas(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<IndicadorSimas, QueryConsultaIndicadorSimasDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaIndicadorSimasDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarIndicadorSimas(dataPaging);
                    }

                    @Override
                    public QueryConsultaIndicadorSimasDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaIndicadorSimasDTOParam param = new QueryConsultaIndicadorSimasDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaIndicadorSimasDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(IndicadorSimas.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return IndicadorSimas.class;
            }

            @Override
            public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
                return new IndicadorSimasAutoCompleteSettings();
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("indicadorSimas");
    }

}