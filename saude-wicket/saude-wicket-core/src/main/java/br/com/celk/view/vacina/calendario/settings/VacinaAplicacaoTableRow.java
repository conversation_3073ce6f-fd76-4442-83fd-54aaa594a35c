package br.com.celk.view.vacina.calendario.settings;

import br.com.celk.component.table.TableRow;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import org.apache.wicket.Component;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.resource.CssResourceReference;

/**
 *
 * <AUTHOR>
 */
public class VacinaAplicacaoTableRow extends TableRow<VacinaAplicacao> {

    private static final String CSS_FILE = "VacinaAplicacaoTableRow.css";
    private static final String APLICADO = "aplicado";
    
    public VacinaAplicacaoTableRow(String id, int index, IModel<VacinaAplicacao> model, Component table) {
        super(id, index, model);
    }
    
    @Override
    public String getDefaultClass() {
        if (getRowObject()!=null) {
            if (VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value().equals(getRowObject().getStatus())) {
                return APLICADO;
            }
            return super.getDefaultClass();
        }
        
        return super.getDefaultClass();
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(VacinaAplicacaoTableRow.class, CSS_FILE)));
    }

}
