package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v3.TempHelperV3;
import br.com.celk.component.temp.v3.behavior.TempBehaviorV3;
import br.com.celk.component.temp.v3.behavior.TempFormBehaviorV3;
import br.com.celk.component.temp.v3.store.interfaces.impl.DefaultTempStoreStrategyV3;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.EvolucaoEditorSettings;
import br.com.celk.component.tinymce.util.TextEditorHelper;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.interfaces.IHistoricoNodeStrategy;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCidSecundario;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgProblemas;
import br.com.celk.view.atendimento.prontuario.panel.evolucao.HistoricoEvolucaoProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.HistoricoAcolhimentoServicoExterno;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioHistoricoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.interfaces.IVoltarHistoricoAction;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.CidsNotificaveisTab;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.EvolucaoUnidadeTab;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.MedicamentoEmUsoTab;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.EstabelecimentoCerestCnaeDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoEspecialidadesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoProntuarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoProblemasCondicoesAlergiasDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.EvolucaoDTOParam;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioCountDTO;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EvolucaoEspecialidadesPanel extends ProntuarioCadastroPanel implements PermissionContainer {

    private Form<EvolucaoEspecialidadesDTO> form;
    private NoHistoricoClinicoDTO noHistoricoClinicoDTO;
    List<EvolucaoProntuario> evolucaoProntuarioList;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaCid autoCompleteConsultaCidSecundario;
    private DlgInformarCidSecundario dlgInformarCidSecundario;

    private WebMarkupContainer componenteAcessoCompartilhado;
    private WebMarkupContainer containerAnotacao;

    private InputArea txaDescricao;
    private InputArea txaDescricaoAnotacao;
    private InputField<Long> txtDiasRetorno;
    private DropDown cbxAcessoCompartilhado;
    private DropDown<TipoDocumentoAtendimento> dropDownTipoDocumentoAtendimento;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;

    private String id;
    private String titulo;
    private Date dataEvolucao;
    private String CSS_FILE = "DadosEnfermagemPanel.css";
    private String descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest;
    private boolean enable;

    private DlgProblemas dlgProblemas;

    public EvolucaoEspecialidadesPanel(String id, String titulo) {
        super(id, titulo);
        this.id = id;
        this.titulo = titulo;
        this.enable = true;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        EvolucaoEspecialidadesDTO proxy = on(EvolucaoEspecialidadesDTO.class);
        form = new Form("form", new CompoundPropertyModel(new EvolucaoEspecialidadesDTO()));

        form.add(getDropDownTipoDocumentoAtendimento());
        form.add(txaDescricao = new InputArea(path(proxy.getDescricaoEvolucao())));

        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));
        autoCompleteConsultaCid.setSexo(getAtendimento().getUsuarioCadsus().getSexo());
        autoCompleteConsultaCid.add(new Tooltip().setText("msgInfCidSexo"));
        autoCompleteConsultaCid.setValidaCategoria(false);
        autoCompleteConsultaCid.ocultarCidsInativos();
        autoCompleteConsultaCid.getTxtDescricao().addRequiredClass();
        autoCompleteConsultaCid.addAjaxUpdateValue();

        try {
            Cid parametroCid = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("defineCidUtilizadoTelaAtendimento");
            autoCompleteConsultaCid.setModelObject(parametroCid);
        } catch (DAOException ex) {
            Loggable.log.error(ex);
        }

        autoCompleteConsultaCid.add(new ConsultaListener<Cid>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cid cid) {
                validarEstabelecimentoCerestCnae(target, cid);

                ClassificacaoCids classificacaoCid = AtendimentoHelper.getClassificacaoCid(cid);

                //validarNotificacaoCid(target, cid, classificacaoCid);
                //Preencheu Notificação = SIM se profissional preencheu CID Notificável e paciente não tem notificação;
                RegistroAgravo registroAgravo = registroAgravoPendente(classificacaoCid,Arrays.asList(cid));
                if (classificacaoCid != null && registroAgravo == null) {
                    form.getModel().getObject().setFlagPreencheuNotificacao(RepositoryComponentDefault.SIM_LONG);
                } else if (classificacaoCid != null && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(classificacaoCid.getPermiteNotificacaoConcomitante(), RepositoryComponentDefault.NAO_LONG)) && registroAgravo != null) {
                    //Preencheu Notificação = NÃO se profissional preencheu CID Notificável e Permite Notificação Concomitante = Não e
                    // paciente não tem notificação sem data de encerramento;
                    form.getModel().getObject().setFlagPreencheuNotificacao(RepositoryComponentDefault.NAO_LONG);
                } else if (classificacaoCid != null && RepositoryComponentDefault.SIM_LONG.equals(classificacaoCid.getPermiteNotificacaoConcomitante()) && registroAgravo != null) {
                    //Preencheu Notificação = SIM se profissional preencheu CID Notificáve1l e Permite Notificação Concomitante = Sim
                    // e paciente não tem notificação sem data de encerramento.
                    form.getModel().getObject().setFlagPreencheuNotificacao(RepositoryComponentDefault.SIM_LONG);
                }
            }
        });

        autoCompleteConsultaCid.add(new RemoveListener<Cid>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid object) {
                form.getModelObject().setPermiteOutraNotificacaoCID(RepositoryComponentDefault.NAO_LONG);
                new TempHelperV3().save(form);
            }
        });

        autoCompleteConsultaCid.add(new TempBehaviorV3());

        // CID Secundario
        form.add(autoCompleteConsultaCidSecundario = new AutoCompleteConsultaCid(path(proxy.getCidSecundario())));
        autoCompleteConsultaCidSecundario.setSexo(getAtendimento().getUsuarioCadsus().getSexo());
        autoCompleteConsultaCidSecundario.setValidaCategoria(false);
        autoCompleteConsultaCidSecundario.ocultarCidsInativos();

        autoCompleteConsultaCidSecundario.add(new Tooltip().setText("msgInfCidSexo"));

        if(autoCompleteConsultaCidSecundario.bloqueiaCidPorCboEAtendimento(getAtendimento())) {
            autoCompleteConsultaCidSecundario.setEnabled(false);
        }


        autoCompleteConsultaCidSecundario.add(new RemoveListener<Cid>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid object) {
                form.getModelObject().setPermiteOutraNotificacaoCID(RepositoryComponentDefault.NAO_LONG);
                new TempHelperV3().save(form);
            }
        });

        autoCompleteConsultaCidSecundario.add(new TempBehaviorV3());

        form.add(new AbstractAjaxLink("btnAdicionarCidSecundarioAtendimento") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarCidSecundario(target);
            }
        });

        form.add(new AbstractAjaxLink("btnVisualizarCids") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarCidSecundario(target);
            }
        });

        txaDescricao.add(new EditorBehavior(new EvolucaoEditorSettings(400, 300, EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));
        txaDescricao.add(new TempBehaviorV3());
        form.add(txtDiasRetorno = new InputField(path(proxy.getDiasRetorno())));
        txtDiasRetorno.addAjaxUpdateValue();
        txtDiasRetorno.add(new TempBehaviorV3());
        componenteAcessoCompartilhado = new WebMarkupContainer("componenteAcessoCompartilhado");
        componenteAcessoCompartilhado.add(cbxAcessoCompartilhado = DropDownUtil.getNaoSimLongDropDown(path(proxy.getAcessoCompartilhado())));
        componenteAcessoCompartilhado.add(new Label("lblAcessoCompartilhado", BundleManager.getString("acessoCompartilhado")));
        componenteAcessoCompartilhado.setOutputMarkupPlaceholderTag(true);
        form.add(componenteAcessoCompartilhado);
        if (!isAcessoRestrito()) {
            componenteAcessoCompartilhado.setVisible(false);
        }
        cbxAcessoCompartilhado.add(new TempBehaviorV3());

        carregarAtendimentoMDDA();
        carregarInformacoes();

        form.add(new TempFormBehaviorV3(new DefaultTempStoreStrategyV3(getAtendimento(), getIdentificador().toString(), EvolucaoEspecialidadesDTO.class, getAtendimento().getProfissional(), getAtendimento().getTabelaCbo())));

        getDataUltimaEvolucao();
        carregarHistoricoEvolucao();


        carregarHistoricoAcolhimentoExterno();

        noHistoricoClinicoDTO = new NoHistoricoClinicoDTO();
        noHistoricoClinicoDTO.setAtendimento(getAtendimento());
        noHistoricoClinicoDTO.setUsuarioCadsus(getAtendimento() != null ? getAtendimento().getUsuarioCadsus() : null);
        noHistoricoClinicoDTO.setEvolucaoProntuarioList(evolucaoProntuarioList);

        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(noHistoricoClinicoDTO) {
            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new EvolucaoUnidadeTab(id, noHistoricoClinicoDTO, evolucaoProntuarioList);
            }
        });
        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(noHistoricoClinicoDTO) {
            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new MedicamentoEmUsoTab(id, noHistoricoClinicoDTO);
            }
        });
        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(noHistoricoClinicoDTO) {
            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new CidsNotificaveisTab(id, noHistoricoClinicoDTO);
            }
        });

        form.add(new AbstractAjaxLink("btnListaProblemas") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgProblemas(target);
            }
        });

        form.add(new EvolucaoUnidadeTabbedPanel("wizard", noHistoricoClinicoDTO, false, tabs, false));

        initContainerAnotacao();
        add(form);

        new TempHelperV3().save(form);
    }

    private void viewDlgInformarCidSecundario(AjaxRequestTarget target) {
        if (dlgInformarCidSecundario == null) {
            getProntuarioController().addWindow(target, dlgInformarCidSecundario = new DlgInformarCidSecundario(getProntuarioController().newWindowId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, List<Cid> cidSecundarioList) throws ValidacaoException, DAOException {
                    adicionarCidSecundario(cidSecundarioList);
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                }
            });
        }
        dlgInformarCidSecundario.show(target, form.getModel().getObject().getCidSecundarioList());
    }

    private RegistroAgravo registroAgravoPendente(ClassificacaoCids classificacaoCid, List<Cid> cids) {
        LoadManager loadManager = LoadManager.getInstance(RegistroAgravo.class)
                .addProperty(RegistroAgravo.PROP_CODIGO)
                .addProperty(RegistroAgravo.PROP_DATA_REGISTRO)
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_CID, BuilderQueryCustom.QueryParameter.IN, cids))
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE,RegistroAgravo.Status.CANCELADO.value()));
        if (classificacaoCid !=null && classificacaoCid.getFichaInvestigacaoAgravo()!=null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN,
                    Arrays.asList(RegistroAgravo.Status.INVESTIGACAO_CONCLUIDA.value())));
        } else {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(RegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN,
                    Arrays.asList(RegistroAgravo.Status.CONCLUIDO.value())));//monitoramento concluido
        }

        loadManager.addSorter(new QueryCustom.QueryCustomSorter(RegistroAgravo.PROP_DATA_REGISTRO, BuilderQueryCustom.QuerySorter.DECRESCENTE));

        return loadManager.setMaxResults(1).start().getVO();
    }

    private void adicionarCidSecundario(List<Cid> cidSecundarioList) {
        form.getModel().getObject().setCidSecundarioList(cidSecundarioList);
        new TempHelperV3().save(form);
    }

    private void initContainerAnotacao() {
        form.add(containerAnotacao = new WebMarkupContainer("containerAnotacao"));
        containerAnotacao.setOutputMarkupId(true);

        containerAnotacao.add(txaDescricaoAnotacao = new InputArea(EvolucaoProntuarioDTO.PROP_DESCRICAO_ANOTACAO));
        txaDescricaoAnotacao.add(new TempBehaviorV3());
    }

    private void viewDlgProblemas(AjaxRequestTarget target) {
        NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO = new NoProblemasCondicoesAlergiasDTO();
        noProblemasCondicoesAlergiasDTO.setAtendimento(getAtendimento());
        noProblemasCondicoesAlergiasDTO.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        getProntuarioController().addWindow(target, dlgProblemas = new DlgProblemas(getProntuarioController().newWindowId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, List<GrupoProblemasCondicoes> doencaList) throws ValidacaoException, DAOException {
                //adicionarCondicoesSaude(condutaList);
            }
        });
        dlgProblemas.show(target, noProblemasCondicoesAlergiasDTO);
    }

    @Override
    public IHistoricoNodeStrategy getHistoricoNodeStrategyInstance() {
        return new IHistoricoNodeStrategy() {

            @Override
            public int count() {
                try {
                    Atendimento atendimento = getAtendimento();
                    UsuarioCadsus usuarioCadsus = atendimento.getUsuarioCadsus();

                    HistoricoEvolucaoProntuarioDTOParam param = new HistoricoEvolucaoProntuarioDTOParam();
                    param.setAtendimentoAtual(atendimento);
                    param.setCodigoPaciente(usuarioCadsus.getCodigo());
                    param.setMeusAtendimentos(false);
                    List<HistoricoEvolucaoProntuarioCountDTO> prontuario = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarHistoricoEvolucaoProntuarioCount(param);
                    return (prontuario.get(0)).getTotal().intValue();
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return 0;
            }

            @Override
            public ProntuarioHistoricoPanel newHistoricoPanel(String id) {
                HistoricoEvolucaoProntuarioPanel historicoEvolucaoProntuarioPanel = new HistoricoEvolucaoProntuarioPanel(id);

                historicoEvolucaoProntuarioPanel.setVoltarHistoricoAction(new IVoltarHistoricoAction() {

                    @Override
                    public DefaultProntuarioPanel getPanelVoltar(String id) {
                        return new EvolucaoEspecialidadesPanel(id, BundleManager.getString("evolucao"));
                    }
                });

                return historicoEvolucaoProntuarioPanel;
            }
        };
    }

    private void carregarAtendimentoMDDA() {
        AtendimentoMDDA proxy = on(AtendimentoMDDA.class);
        List<AtendimentoMDDA> atendimentoMDDAList = LoadManager.getInstance(AtendimentoMDDA.class)
                .addProperties(new HQLProperties(AtendimentoMDDA.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getAtendimentoPrincipal()), getAtendimento().getAtendimentoPrincipal()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(atendimentoMDDAList)) {
            form.getModelObject().setAtendimentoMDDA(atendimentoMDDAList.get(0));
            new TempHelperV2().save(form);
        }
    }

    private void carregarInformacoes() {
        UsuarioCadsusDado proxy = on(UsuarioCadsusDado.class);
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperty(UsuarioCadsusDado.PROP_DESCRICAO_ANOTACAO)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
                .start().getVO();

        if (usuarioCadsusDado != null) {
            form.getModelObject().setDescricaoAnotacao(usuarioCadsusDado.getDescricaoAnotacao());
            new TempHelperV2().save(form);
        }
    }

    public void carregarHistoricoEvolucao() {
        noHistoricoClinicoDTO = new NoHistoricoClinicoDTO();
        noHistoricoClinicoDTO.setAtendimento(getAtendimento());
        noHistoricoClinicoDTO.setUsuarioCadsus(getAtendimento() != null ? getAtendimento().getUsuarioCadsus() : null);
        noHistoricoClinicoDTO.setEvolucaoProntuarioList(evolucaoProntuarioList);
        try {
            evolucaoProntuarioList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarEvolucaoProntuario(noHistoricoClinicoDTO);
        } catch (DAOException ex) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void getDataUltimaEvolucao() {
        AtendimentoProntuario ap = LoadManager.getInstance(AtendimentoProntuario.class)
                .addProperties(AtendimentoProntuario.PROP_CODIGO)
                .addProperties(AtendimentoProntuario.PROP_DATA)
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_TIPO_REGISTRO, AtendimentoProntuario.TipoRegistro.EVOLUCAO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoProntuario.PROP_DATA, "desc"))
                .setMaxResults(1).start().getVO();
        if (ap != null && dataEvolucao == null) {
            dataEvolucao = ap.getData();
        }
    }

    private DropDown getDropDownTipoDocumentoAtendimento() {
        if (dropDownTipoDocumentoAtendimento == null) {
            dropDownTipoDocumentoAtendimento = DropDownUtil.getDropDownTipoDocumentoAtendimento("tipoDocumento", "", getAtendimento(), TipoDocumentoAtendimento.TipoDocumento.TIPO_EVOLUCAO);

            dropDownTipoDocumentoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    TipoDocumentoAtendimento object = dropDownTipoDocumentoAtendimento.getModelObject();
                    txaDescricao.limpar(target);
                    if (object != null) {
                        String text = TextEditorHelper.builder(object.getModelo())
                                .setProfissional(getAtendimento().getProfissional())
                                .setUsuarioCadsus(getAtendimento().getUsuarioCadsus())
                                .setDataHoraChegada(getAtendimento().getDataChegada())
                                .setEmpresa(ApplicationSession.get().getSession().getEmpresa())
                                .parse();

                        form.getModelObject().setDescricaoEvolucao(Coalesce.asString(form.getModelObject().getDescricaoEvolucao()) + " \n " + text);
                        target.add(txaDescricao);
                        new TempHelperV3().save(form);
                    }
                }
            });
        }
        return dropDownTipoDocumentoAtendimento;
    }

    public String getDescricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest(AjaxRequestTarget target) {
        if (descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest == null) {
            try {
                descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest");
            } catch (DAOException | ValidacaoRuntimeException e) {
                MessageUtil.modalError(target, this, e);
            }
        }
        return descricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest;
    }

    private void validarEstabelecimentoCerestCnae(AjaxRequestTarget target, Cid cid) {
        try {
            List<EstabelecimentoCerestCnaeDTO> estabelecimentoCerestCnaeDTOList = AtendimentoHelper.verificarVinculoEstabelecimentoCidCnae(getAtendimento(), cid);

            if (CollectionUtils.isNotNullEmpty(estabelecimentoCerestCnaeDTOList)) {
                String msg = getDescricaoNexoTecnicoEpidemiologicoPrevidenciarioCerest(target);
                if (msg != null) {
                    List<String> descricaoAtividadeFormatadoList = Lambda.extract(estabelecimentoCerestCnaeDTOList, Lambda.on(EstabelecimentoCerestCnaeDTO.class).getEstabelecimentoCerestCnae().getTabelaCnae().getDescricaoAtividadeFormatado());
                    String cnaeFormatado = StringUtils.join(descricaoAtividadeFormatadoList, ", ");
                    msg = msg + ". CNAE's: " + cnaeFormatado;
                    MessageUtil.warn(target, this, msg);
                }
            }
        } catch (ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

    public WebMarkupContainer getComponenteAcessoCompartilhado() {
        return componenteAcessoCompartilhado;
    }

    private boolean isAcessoRestrito() {
        EvolucaoDTOParam param = new EvolucaoDTOParam();
        param.setCodigoAtendimento(getAtendimento().getCodigo());
        try {
            return BOFactoryWicket.getBO(AtendimentoFacade.class).isAcessoRestrito(param);
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
            return false;
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (form.getModel().getObject().getDescricaoAnotacao() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerAnotacao)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerAnotacao)));

        }
    }

    private void carregarHistoricoAcolhimentoExterno() {
        evolucaoProntuarioList = evolucaoProntuarioList != null ? evolucaoProntuarioList : new ArrayList<EvolucaoProntuario>();
        evolucaoProntuarioList.addAll(new HistoricoAcolhimentoServicoExterno(getAtendimento()).buildProntuarios());
    }
}
