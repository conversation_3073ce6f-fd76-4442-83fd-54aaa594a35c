package br.com.celk.component.foto.interfaces;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;

import java.io.Serializable;

/**
 * Created by maicon on 16/08/16.
 */
public interface IFoto extends Serializable {

    Serializable getCodigo();
    BaseRootVO getObjetoParaSalvar();
    String getSexoObjeto();
    boolean isAnimal();
    GerenciadorArquivo getGerenciadorArquivo();

    void setGerenciadorArquivo(GerenciadorArquivo gerenciadorArquivo);
}
