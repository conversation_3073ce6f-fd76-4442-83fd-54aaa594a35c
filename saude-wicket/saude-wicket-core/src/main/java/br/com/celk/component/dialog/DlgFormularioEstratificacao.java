package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgFormularioEstratificacao extends Window {

    private List<ConfiguracaoEstratificacao.Formulario> formularioList;

    public DlgFormularioEstratificacao(String id, List<ConfiguracaoEstratificacao.Formulario> formularioList) {
        super(id);
        this.formularioList = formularioList;
        init();
    }

    private void init() {
        setTitle(getDialogTitle());

        setInitialHeight(80);
        setInitialWidth(600);
        setResizable(false);

        setContent(new PnlFormularioEstratificacao(getContentId(), formularioList) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long value) throws ValidacaoException, DAOException {
                close(target);
                DlgFormularioEstratificacao.this.onConfirmar(target, value);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgFormularioEstratificacao.this.onFechar(target);
            }

        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget art) {
                try {
                    onFechar(art);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return true;
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long value) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getDialogTitle() {
        return BundleManager.getString("novoFormulario");
    }
}
