package br.com.celk.view.atendimento.prontuario.documentos;

import br.com.celk.report.HtmlReport;
import br.com.celk.report.HtmlReportTemplateType;
import br.com.celk.report.templatebuilder.IHtmlTemplateBuilder;
import br.com.ksisolucoes.bo.prontuario.validaatendimentoexterno.URLBuilderValidaAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoAtendimento;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import org.apache.commons.codec.binary.Base64OutputStream;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class DocumentoHtmlQRCodeReport extends HtmlReport {

    private DocumentoAtendimento documentoAtendimento;

    public DocumentoHtmlQRCodeReport(DocumentoAtendimento documentoAtendimento) {
        super(HtmlReportTemplateType.CAB_ROD_TIMBRADO);
        this.documentoAtendimento = documentoAtendimento;
    }

    @Override
    public void customizeReport(IHtmlTemplateBuilder templateBuilder) {
        try {
            BitMatrix bitMatrix =
                    new com.google.zxing.qrcode.QRCodeWriter().encode(
                            URLBuilderValidaAtendimento.getURL(documentoAtendimento.getAtendimento().getCodigo()),
                            com.google.zxing.BarcodeFormat.QR_CODE, 600, 600);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Base64OutputStream encoded = new Base64OutputStream(baos);
            MatrixToImageWriter.writeToStream(bitMatrix, "png", encoded);
            String result = "data:image/png;base64," + new String(baos.toByteArray(), 0, baos.size(), StandardCharsets.UTF_8);
            templateBuilder.addParameter("CONTENT", documentoAtendimento.getDescricao() + "</br><center><img height=\"130\" width=\"130\" src=\"" + result + "\"></img>" +
                    "</br>" + documentoAtendimento.getAtendimento().getCodigo() + "</center>");
        } catch (WriterException | IOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }

}
