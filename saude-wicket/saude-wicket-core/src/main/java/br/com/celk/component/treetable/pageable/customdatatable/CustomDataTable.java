package br.com.celk.component.treetable.pageable.customdatatable;

import br.com.celk.component.treetable.pageable.PageableTreeProvider;
import br.com.celk.component.treetable.pageable.PagingToolbarTree;
import java.util.Iterator;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.AbstractToolbar;
import org.apache.wicket.extensions.markup.html.repeater.data.table.DataTable;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IStyledColumn;
import org.apache.wicket.extensions.markup.html.repeater.tree.AbstractTree;
import org.apache.wicket.extensions.markup.html.repeater.tree.ISortableTreeProvider;
import org.apache.wicket.extensions.markup.html.repeater.tree.ITreeProvider;
import org.apache.wicket.extensions.markup.html.repeater.tree.table.NodeModel;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.navigation.paging.IPageableItems;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.IItemReuseStrategy;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.markup.repeater.RefreshingView;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.markup.repeater.data.IDataProvider;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.lang.Args;
import org.apache.wicket.util.string.Strings;
import org.apache.wicket.util.visit.IVisit;
import org.apache.wicket.util.visit.IVisitor;

public class CustomDataTable<T, S> extends Panel implements IPageableItems {

    static abstract class CssAttributeBehavior extends Behavior {

        private static final long serialVersionUID = 1L;

        protected abstract String getCssClass();

        /**
         * @see Behavior#onComponentTag(Component, ComponentTag)
         */
        @Override
        public void onComponentTag(final Component component, final ComponentTag tag) {
            String className = getCssClass();
            if (!Strings.isEmpty(className)) {
                tag.append("class", className, " ");
            }
        }
    }

    private static final long serialVersionUID = 1L;

    private final CustomDataGridView<T> datagrid;
    private final WebMarkupContainer body;
    private final List<? extends IColumn<T, S>> columns;
    private final ToolbarsContainer topToolbars;
    private final ToolbarsContainer bottomToolbars;
    private final Caption caption;
    private final ITreeProvider treeProvider;
    private final PagingToolbarTree pagingToolbarTree;
    private final int initialRowsPerPage;
    private final CustomTableTree treeTable;

    private transient Branch<T> currentBranch;
    private transient Branch<T> previousBranch;
    private Integer contadorFilhosPgAtual;
    private Integer contadorFilhosPgAnteriores;
    private Integer index = 0;

    /**
     * Constructor
     *
     * esta classe é uma data table criada para ser usada internamente por uma CustomTableTree
     * 
     * @param id component id
     * @param columns list of IColumn objects
     * @param dataProvider imodel for data provider
     * @param treeProvider the tree provider
     * @param rowsPerPage number of rows per page
     */
    public CustomDataTable(final String id, final List<? extends IColumn<T, S>> columns,
            final IDataProvider<T> dataProvider, final ITreeProvider treeProvider, int rowsPerPage, final CustomTableTree treeTable) {
        super(id);

        this.treeProvider = treeProvider;
        initialRowsPerPage = rowsPerPage;
        this.treeTable = treeTable;
        Args.notEmpty(columns, "columns");

        this.columns = columns;
        this.caption = new Caption("caption", getCaptionModel());
        add(caption);
        body = newBodyContainer("body");
        datagrid = newDataGridView(columns, dataProvider);
        datagrid.setItemsPerPage(rowsPerPage);
        body.add(datagrid);
        add(body);
        topToolbars = new ToolbarsContainer("topToolbars");
        bottomToolbars = new ToolbarsContainer("bottomToolbars");
        add(topToolbars);
        add(bottomToolbars);
        this.addTopToolbar(new CustomHeadersToolbar<S>(this, (ISortableTreeProvider<T, S>) treeProvider));
        this.addBottomToolbar(new CustomNoRecordsToolbar(this));
        this.addBottomToolbar(this.pagingToolbarTree = new PagingToolbarTree(this, (PageableTreeProvider) treeProvider));
    }

    public CustomDataGridView<T> newDataGridView(final List<? extends IColumn<T, S>> columns, final IDataProvider<T> dataProvider) {
        return new CustomDataGridView<T>("rows", columns, dataProvider) {
            private static final long serialVersionUID = 1L;

            @SuppressWarnings({"rawtypes", "unchecked"})
            @Override
            protected Item newCellItem(final String id, final int index, final IModel model) {
                Item item = CustomDataTable.this.newCellItem(id, index, model);
                final IColumn<T, S> column = CustomDataTable.this.columns.get(index);
                if (column instanceof IStyledColumn) {
                    item.add(new CssAttributeBehavior() {
                        private static final long serialVersionUID = 1L;

                        @Override
                        protected String getCssClass() {
                            return ((IStyledColumn<T, S>) column).getCssClass();
                        }
                    });
                }
                return item;
            }

            /**
             *
             * @return SOMENTE o numero de itens PAI (Root) da tabela
             */
            @Override
            protected long internalGetItemCount() {
                PageableTreeProvider provider = (PageableTreeProvider) treeProvider;
                return provider.getRootList().size();
            }

            /**
             *
             * @return a pagina atual * (itens por pagina + numero de
             * filhos nas paginas anteriores)
             */
            @Override
            public long getFirstItemOffset() {
                contadorFilhosPgAtual = 0;
                contadorFilhosPgAnteriores = 0;
                index = 0;
                Iterator<? extends T> iterator = CustomDataTable.this.iterator(0, Integer.MAX_VALUE);
                while (iterator.hasNext()) {
                    iterator.next();
                }
                return (getCurrentPage() * getItemsPerPage()) + contadorFilhosPgAnteriores.longValue();
            }

            /**
             *
             * @return o numero selecionado no navigator somado à quantidade
             * total de FILHOS dos pais expandidos da pagina atual
             */
            @Override
            public long getViewSize() {
                    Integer rpp = pagingToolbarTree.getPagingNavigatorTree().getRowsPerPage();

                    if (rpp == null) {
                        rpp = initialRowsPerPage;
                    }
                    Integer resultado = rpp + contadorFilhosPgAtual;

                    return resultado.longValue();
            }

            @Override
            public long getPageCount() {
                long total = getItemCount();
                long itemsPerPage;
                if (pagingToolbarTree.getPagingNavigatorTree().getRowsPerPage() == null) {
                    itemsPerPage = initialRowsPerPage;
                } else {
                    itemsPerPage = pagingToolbarTree.getPagingNavigatorTree().getRowsPerPage();
                }
                long count = total / itemsPerPage;

                if (itemsPerPage * count < total) {
                    count++;
                }

                return count;
            }

            /**
             *
             * @return o numero de linhas, independente se for pai, filho ou
             * cabeçalho
             */
            @Override
            public long getRowCount() {
                return dataProvider.size();
            }

            @Override
            protected Item<T> newRowItem(final String id, final int index, final IModel<T> model) {
                return CustomDataTable.this.newRowItem(id, index, model);
            }

        };
    }

    /**
     * Returns the model for table's caption. The caption wont be rendered if
     * the model has empty value.
     *
     * @return the model for table's caption
     */
    protected IModel<String> getCaptionModel() {
        return null;
    }

    /**
     * Create the MarkupContainer for the <tbody> tag. Users may subclass it to
     * provide their own (modified) implementation.
     *
     * @param id
     * @return A new markup container
     */
    protected WebMarkupContainer newBodyContainer(final String id) {
        return new WebMarkupContainer(id);
    }

    /**
     * Set the 'class' attribute for the tbody tag.
     *
     * @param cssStyle
     */
    public final void setTableBodyCss(final String cssStyle) {
        body.add(AttributeModifier.replace("class", cssStyle));
    }

    /**
     * Adds a toolbar to the datatable that will be displayed after the data
     *
     * @param toolbar toolbar to be added
     *
     * @see AbstractToolbar
     */
    public void addBottomToolbar(final CustomAbstractToolbar toolbar) {
        addToolbar(toolbar, bottomToolbars);
    }

    /**
     * Adds a toolbar to the datatable that will be displayed before the data
     *
     * @param toolbar toolbar to be added
     *
     * @see AbstractToolbar
     */
    public void addTopToolbar(final CustomAbstractToolbar toolbar) {
        addToolbar(toolbar, topToolbars);
    }

    /**
     * @return the container with the toolbars at the top
     */
    public final ToolbarsContainer getTopToolbars() {
        return topToolbars;
    }

    /**
     * @return the container with the toolbars at the bottom
     */
    public final ToolbarsContainer getBottomToolbars() {
        return bottomToolbars;
    }

    /**
     * @return the container used for the table body
     */
    public final WebMarkupContainer getBody() {
        return body;
    }

    /**
     * @return the component used for the table caption
     */
    public final Caption getCaption() {
        return caption;
    }

    /**
     * @return dataprovider
     */
    public final IDataProvider<T> getDataProvider() {
        return datagrid.getDataProvider();
    }

    /**
     * @return array of column objects this table displays
     */
    public final List<? extends IColumn<T, S>> getColumns() {
        return columns;
    }

    /**
     * @see
     * org.apache.wicket.markup.html.navigation.paging.IPageable#getCurrentPage()
     */
    @Override
    public final long getCurrentPage() {
        return datagrid.getCurrentPage();
    }

    /**
     * @see
     * org.apache.wicket.markup.html.navigation.paging.IPageable#getPageCount()
     */
    @Override
    public final long getPageCount() {
        return datagrid.getPageCount();
    }

    /**
     * @return total number of rows in this table
     */
    public final long getRowCount() {
        return datagrid.getRowCount();
    }

    /**
     * @return number of rows per page
     */
    @Override
    public final long getItemsPerPage() {
        return datagrid.getItemsPerPage();
    }

    /**
     * @see
     * org.apache.wicket.markup.html.navigation.paging.IPageable#setCurrentPage(long)
     */
    @Override
    public final void setCurrentPage(final long page) {
        datagrid.setCurrentPage(page);
        onPageChanged();
    }

    /**
     * Sets the item reuse strategy. This strategy controls the creation of
     * {@link Item}s.
     *
     * @see RefreshingView#setItemReuseStrategy(IItemReuseStrategy)
     * @see IItemReuseStrategy
     *
     * @param strategy item reuse strategy
     * @return this for chaining
     */
    public final CustomDataTable<T, S> setItemReuseStrategy(final IItemReuseStrategy strategy) {
        datagrid.setItemReuseStrategy(strategy);
        return this;
    }

    /**
     * Sets the number of items to be displayed per page
     *
     * @param items number of items to display per page
     *
     */
    public void setItemsPerPage(final long items) {
        datagrid.setItemsPerPage(items);
    }

    /**
     * @see
     * org.apache.wicket.markup.html.navigation.paging.IPageableItems#getItemCount()
     */
    @Override
    public long getItemCount() {
        return datagrid.getItemCount();
    }

    private void addToolbar(final CustomAbstractToolbar toolbar, final ToolbarsContainer container) {
        Args.notNull(toolbar, "toolbar");

        container.getRepeatingView().add(toolbar);
    }

    /**
     * Factory method for Item container that represents a cell in the
     * underlying DataGridView
     *
     * @see Item
     *
     * @param id component id for the new data item
     * @param index the index of the new data item
     * @param model the model for the new data item
     *
     * @return DataItem created DataItem
     */
    public Item<IColumn<T, S>> newCellItem(final String id, final int index,
            final IModel<IColumn<T, S>> model) {
        return new Item<IColumn<T, S>>(id, index, model);
    }

    /**
     * Factory method for Item container that represents a row in the underlying
     * DataGridView
     *
     * @see Item
     *
     * @param id component id for the new data item
     * @param index the index of the new data item
     * @param model the model for the new data item.
     *
     * @return DataItem created DataItem
     */
    protected Item<T> newRowItem(final String id, final int index, final IModel<T> model) {
        return new Item<T>(id, index, model);
    }

    /**
     * @see org.apache.wicket.Component#onDetach()
     */
    @Override
    protected void onDetach() {
        super.onDetach();

        for (IColumn<T, S> column : columns) {
            column.detach();
        }
    }

    /**
     * Event listener for page-changed event
     */
    protected void onPageChanged() {
        // noop
    }

    /**
     * This class acts as a panel that will contain the toolbar. It makes
     * sure that the table row group (e.g. thead) tags are only visible when
     * they contain rows in accordance with the HTML specification.
     *
     * <AUTHOR>
     */
    private static class ToolbarsContainer extends WebMarkupContainer {

        private static final long serialVersionUID = 1L;

        private final RepeatingView toolbars;

        /**
         * Constructor
         *
         * @param id
         */
        private ToolbarsContainer(final String id) {
            super(id);
            toolbars = new RepeatingView("toolbars");
            add(toolbars);
        }

        public RepeatingView getRepeatingView() {
            return toolbars;
        }

        @Override
        public void onConfigure() {
            super.onConfigure();

            toolbars.configure();

            Boolean visible = toolbars.visitChildren(new IVisitor<Component, Boolean>() {
                @Override
                public void component(Component object, IVisit<Boolean> visit) {
                    object.configure();
                    if (object.isVisible()) {
                        visit.stop(Boolean.TRUE);
                    } else {
                        visit.dontGoDeeper();
                    }
                }
            });
            if (visible == null) {
                visible = false;
            }
            setVisible(visible);
        }
    }

    /**
     * A caption for the table. It renders itself only if
     * {@link DataTable#getCaptionModel()} has non-empty value.
     */
    private static class Caption extends Label {

        /**
         */
        private static final long serialVersionUID = 1L;

        /**
         * Construct.
         *
         * @param id the component id
         * @param model the caption model
         */
        public Caption(String id, IModel<String> model) {
            super(id, model);
        }

        @Override
        protected void onConfigure() {
            setRenderBodyOnly(Strings.isEmpty(getDefaultModelObjectAsString()));

            super.onConfigure();
        }

        @Override
        protected IModel<String> initModel() {
            // don't try to find the model in the parent
            return null;
        }
    }

    public Iterator<? extends T> iterator(long first, long count) {
        currentBranch = new Branch<T>(null, treeProvider.getRoots());

        Iterator<T> iterator = new Iterator<T>() {
            @Override
            public boolean hasNext() {
                while (currentBranch != null) {
                    if (currentBranch.hasNext()) {
                        return true;
                    }
                    currentBranch = currentBranch.parent;
                }

                return false;
            }

            @Override
            public T next() {
                if (!hasNext()) {
                    throw new IllegalStateException();
                }

                T next = currentBranch.next();

                previousBranch = currentBranch;

                if (iterateChildren(next)) {
                    currentBranch = new Branch<T>(previousBranch, treeProvider.getChildren(next));

                    Long firstIndex = getCurrentPage() * getItemsPerPage();

                    if (index.longValue() >= (firstIndex + contadorFilhosPgAnteriores.longValue()) && 
                            index.longValue() < (firstIndex + getItemsPerPage() + contadorFilhosPgAtual.longValue() + contadorFilhosPgAnteriores.longValue())) {
                        Iterator children = treeProvider.getChildren(next);
                        while (children.hasNext()) {
                            children.next();
                            contadorFilhosPgAtual++;
                        }
                    }
                    if (index.longValue() < (firstIndex + contadorFilhosPgAnteriores)) {
                        Iterator children = treeProvider.getChildren(next);
                        while (children.hasNext()) {
                            children.next();
                            contadorFilhosPgAnteriores++;
                        }

                    }
                }

                index++;
                return next;
            }

            @Override
            public void remove() {
                throw new UnsupportedOperationException();
            }
        };

        for (int i = 0; i < first; i++) {
            iterator.next();
        }

        return iterator;
    }

    protected boolean iterateChildren(T object) {
        return treeTable.getState(object) == AbstractTree.State.EXPANDED;
    }

    private static class Branch<T> implements Iterator<T> {

        private Branch<T> parent;

        private Iterator<? extends T> children;

        public Branch(Branch<T> parent, Iterator<? extends T> children) {
            this.parent = parent;
            this.children = children;
        }

        public NodeModel<T> wrapModel(IModel<T> model) {
            boolean[] branches = new boolean[getDepth()];

            Branch<T> branch = this;
            for (int c = branches.length - 1; c >= 0; c--) {
                branches[c] = branch.hasNext();

                branch = branch.parent;
            }

            return new NodeModel<T>(model, branches);
        }

        public int getDepth() {
            if (parent == null) {
                return 1;
            } else {
                return parent.getDepth() + 1;
            }
        }

        @Override
        public boolean hasNext() {
            return children.hasNext();
        }

        @Override
        public T next() {
            if (!hasNext()) {
                throw new IllegalStateException();
            }

            return children.next();
        }

        @Override
        public void remove() {
            throw new UnsupportedOperationException();
        }
    }

}
