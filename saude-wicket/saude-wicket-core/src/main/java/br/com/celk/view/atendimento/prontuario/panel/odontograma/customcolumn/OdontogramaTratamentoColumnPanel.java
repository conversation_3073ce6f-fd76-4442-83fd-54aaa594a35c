package br.com.celk.view.atendimento.prontuario.panel.odontograma.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgMotivo;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.tratamentoodontologico.dialog.DlgHistoricoTrabalhosExecutados;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class OdontogramaTratamentoColumnPanel extends Panel implements PermissionContainer {

    private Atendimento atendimento;
    private AtendimentoOdontoPlano atendimentoOdontoPlano;

    private AjaxLink btnRegistrarTrabalho;
    private AjaxLink btnEditar;
    private AjaxLink btnCancelar;
    private AjaxLink btnHistorico;

    private DlgMotivo dlgMotivo;
    private DlgConfirmacao dlgConfirmacao;
    private DlgHistoricoTrabalhosExecutados dlgHistoricoTrabalhosExecutados;

    public OdontogramaTratamentoColumnPanel(String id, Atendimento atendimento, AtendimentoOdontoPlano atendimentoOdontoPlano) {
        super(id);
        this.atendimento = atendimento;
        this.atendimentoOdontoPlano = atendimentoOdontoPlano;
        init();
    }

    private void init() {
        add(btnRegistrarTrabalho = new AbstractAjaxLink("btnRegistrarTrabalho") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                OdontogramaTratamentoColumnPanel.this.registrarTrabalho(target, atendimentoOdontoPlano, false);
            }

            @Override
            public boolean isEnabled() {
                return AtendimentoOdontoPlano.Status.PENDENTE.value().equals(atendimentoOdontoPlano.getStatus())
                        || (AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value().equals(atendimentoOdontoPlano.getStatus()) && atendimentoOdontoPlano.getAtendimentoExecucao() != null && !atendimentoOdontoPlano.getAtendimentoExecucao().getCodigo().equals(atendimento.getCodigo()));
            }

        });

        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                OdontogramaTratamentoColumnPanel.this.registrarTrabalho(target, atendimentoOdontoPlano, true);
            }

            @Override
            public boolean isEnabled() {
                return atendimentoOdontoPlano.getAtendimentoExecucao() != null && atendimentoOdontoPlano.getAtendimentoExecucao().getCodigo().equals(atendimento.getCodigo())
                        && (AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value().equals(atendimentoOdontoPlano.getStatus()) || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlano.getStatus()));
            }

        });

        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (atendimentoOdontoPlano.getAtendimento().getCodigo().equals(atendimento.getCodigo())) {
                    initDlgConfirmacao(target);
                    dlgConfirmacao.show(target);
                } else {
                    initDlgMotivo(target);
                    dlgMotivo.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return AtendimentoOdontoPlano.Status.PENDENTE.value().equals(atendimentoOdontoPlano.getStatus())
                        || AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlano.getStatus());
            }

        });

        add(btnHistorico = new AbstractAjaxLink("btnHistorico") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgHistoricoTrabalhosExecutados(target);
                dlgHistoricoTrabalhosExecutados.show(target, atendimentoOdontoPlano.getCodigo());
            }

            @Override
            public boolean isEnabled() {
                return AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value().equals(atendimentoOdontoPlano.getStatus())
                        || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlano.getStatus());
            }

        });

        btnRegistrarTrabalho.add(new AttributeModifier("title", BundleManager.getString("registrarTrabalho")));
        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
        btnHistorico.add(new AttributeModifier("title", BundleManager.getString("historico")));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaExcluirRegistroSelecionado")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    excluirPlanoTratamento(target, atendimentoOdontoPlano);
                    updateTable(target);
                }

            });
        }
    }

    private void initDlgMotivo(AjaxRequestTarget target) {
        if (dlgMotivo == null) {
            WindowUtil.addModal(target, this, dlgMotivo = new DlgMotivo(WindowUtil.newModalId(this), BundleManager.getString("cancelarPlanoTratamento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
                    cancelarPlanoTratamento(target, atendimentoOdontoPlano, motivo);
                    updateTable(target);
                }
            });
        }
    }

    private void initDlgHistoricoTrabalhosExecutados(AjaxRequestTarget target) {
        if (dlgHistoricoTrabalhosExecutados == null) {
            WindowUtil.addModal(target, this, dlgHistoricoTrabalhosExecutados = new DlgHistoricoTrabalhosExecutados(WindowUtil.newModalId(this)));
        }
    }

    public abstract void registrarTrabalho(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, boolean edit);
    public abstract void cancelarPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, String motivo) throws ValidacaoException, DAOException;
    public abstract void excluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException;
    public abstract void updateTable(AjaxRequestTarget target);
}
