package br.com.celk.view.atendimento.prontuario.panel.soapenfermagem;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.v3.TempHelperV3;
import br.com.celk.component.temp.v3.behavior.TempBehaviorV3;
import br.com.celk.component.temp.v3.behavior.TempFormBehaviorV3;
import br.com.celk.component.temp.v3.store.interfaces.impl.DefaultTempStoreStrategyV3;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.EvolucaoEditorSettings;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.diagnosticoenfermagem.autocomplete.AutoCompleteConsultaDiagnosticoEnfermagem;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapEnfermagemDTO;
import br.com.ksisolucoes.bo.prontuario.interfaces.dto.GrupoPerguntaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaRoteiro;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoDiagnosticoEnfermagem;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoSoapEnfermagem;
import br.com.ksisolucoes.vo.prontuario.basico.DiagnosticoEnfermagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class SoapEnfermagemPanel extends ProntuarioCadastroPanel implements PermissionContainer {

    private String id;
    private String titulo;
    private Form<SoapEnfermagemDTO> form;

    private WebMarkupContainer containerSubjetivo;
    private WebMarkupContainer containerObjetivo;
    private WebMarkupContainer containerAvaliacao;
    private WebMarkupContainer containerPlano;

    private InputArea txaSubjetivo;
    private InputArea txaObjetivo;
    private InputArea txaAvaliacao;
    private InputArea txaPlano;

    private WebMarkupContainer containerDiagnosticosEnfermagem;
    private CompoundPropertyModel<AtendimentoDiagnosticoEnfermagem> modelDiagnosticosEnfermagem;
    private Table tblDiagnosticosEnfermagem;
    private AutoCompleteConsultaDiagnosticoEnfermagem autoCompleteConsultaDiagnosticoEnfermagem;
    private DlgRoteiroProntuario dlgRoteiroEnfermagem;
    private AjaxReportLink btnImprimirPlano;

    public SoapEnfermagemPanel(String id) {
        super(id, BundleManager.getString("consultaEnfermagem"));
        this.id = id;
        this.titulo = BundleManager.getString("consultaEnfermagem");
    }

    public SoapEnfermagemPanel(String id, String titulo) {
        super(id, titulo);
        this.id = id;
        this.titulo = titulo;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        SoapEnfermagemDTO proxy = on(SoapEnfermagemDTO.class);

        {
            containerSubjetivo = new WebMarkupContainer("containerSubjetivo");
            containerSubjetivo.add(txaSubjetivo = new InputArea(path(proxy.getAtendimentoSoapEnfermagem().getSubjetivo())));

            containerSubjetivo.setOutputMarkupId(true);
            getForm().add(containerSubjetivo);
        }
        {
            containerObjetivo = new WebMarkupContainer("containerObjetivo");
            containerObjetivo.add(txaObjetivo = new InputArea(path(proxy.getAtendimentoSoapEnfermagem().getObjetivo())));

            containerObjetivo.add(new AbstractAjaxLink("linkRoteiro") {
                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    initDlgRoteiroEnfermagem(target);
                }
            });

            containerObjetivo.setOutputMarkupId(true);
            getForm().add(containerObjetivo);
        }

        {
            containerAvaliacao = new WebMarkupContainer("containerAvaliacao");
            containerAvaliacao.add(txaAvaliacao = new InputArea(path(proxy.getAtendimentoSoapEnfermagem().getAvaliacao())));

            containerDiagnosticosEnfermagem = new WebMarkupContainer("containerDiagnosticosEnfermagem", modelDiagnosticosEnfermagem = new CompoundPropertyModel<>(new AtendimentoDiagnosticoEnfermagem()));
            containerAvaliacao.add(containerDiagnosticosEnfermagem);

            AtendimentoDiagnosticoEnfermagem proxyDiagnosticoEnfermagem = on(AtendimentoDiagnosticoEnfermagem.class);

            containerDiagnosticosEnfermagem.add(autoCompleteConsultaDiagnosticoEnfermagem = new AutoCompleteConsultaDiagnosticoEnfermagem(path(proxyDiagnosticoEnfermagem.getDiagnosticoEnfermagem())));
            containerDiagnosticosEnfermagem.add(new AbstractAjaxButton("btnAdicionarDiagnosticoEnfermagem") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    adicionarDiagnosticoEnfermagem(target);
                }
            });
            containerDiagnosticosEnfermagem.add(tblDiagnosticosEnfermagem = new Table("tblDiagnosticosEnfermagem", getColumnsDiagnosticoEnfermagem(), getCollectionProviderDiagnosticoEnfermagem()));
            tblDiagnosticosEnfermagem.populate();
            tblDiagnosticosEnfermagem.setScrollY("1800");

            containerAvaliacao.setOutputMarkupId(true);
            getForm().add(containerAvaliacao);
        }

        {
            containerPlano = new WebMarkupContainer("containerPlano");
            containerPlano.add(txaPlano = new InputArea(path(proxy.getAtendimentoSoapEnfermagem().getPlano())));
            containerPlano.add(btnImprimirPlano = new AjaxReportLink("btnImprimirPlano") {
                @Override
                public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoPlanoEnfermagem(getAtendimento().getCodigo(), getForm().getModel().getObject().getAtendimentoSoapEnfermagem().getPlano());
                }
            });

            containerPlano.setOutputMarkupId(true);
            getForm().add(containerPlano);
        }

        txaSubjetivo.add(new EditorBehavior(new EvolucaoEditorSettings(780, 200, EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));
        txaObjetivo.add(new EditorBehavior(new EvolucaoEditorSettings(780, 200, EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));
        txaAvaliacao.add(new EditorBehavior(new EvolucaoEditorSettings(780, 200, EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));
        txaPlano.add(new EditorBehavior(new EvolucaoEditorSettings(780, 200, EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));

        txaSubjetivo.setOutputMarkupPlaceholderTag(true);
        txaObjetivo.setOutputMarkupPlaceholderTag(true);
        txaAvaliacao.setOutputMarkupPlaceholderTag(true);
        txaPlano.setOutputMarkupPlaceholderTag(true);

        txaSubjetivo.addAjaxUpdateValue();
        txaObjetivo.addAjaxUpdateValue();
        txaAvaliacao.addAjaxUpdateValue();
        txaPlano.addAjaxUpdateValue();

        txaSubjetivo.add(new TempBehaviorV3());
        txaObjetivo.add(new TempBehaviorV3());
        txaAvaliacao.add(new TempBehaviorV3());
        txaPlano.add(new TempBehaviorV3());

        add(getForm());
        getForm().add(new TempFormBehaviorV3(new DefaultTempStoreStrategyV3(getAtendimento(), getIdentificador().toString(), SoapEnfermagemDTO.class, getAtendimento().getProfissional(), getAtendimento().getTabelaCbo())));

        new TempHelperV3().save(getForm());
    }

    private Form<SoapEnfermagemDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel<>(new SoapEnfermagemDTO(new AtendimentoSoapEnfermagem())));
        }
        return form;
    }

    private void eventoRemoverDiagnosticoEnfermagem(AjaxRequestTarget target, DiagnosticoEnfermagem object) {
        String avaliacao = getForm().getModel().getObject().getAtendimentoSoapEnfermagem().getAvaliacao();
        if(avaliacao != null) {
            avaliacao = avaliacao.replaceAll(object.getDescricao(), "");
            avaliacao = avaliacao.replaceAll(Coalesce.asString(object.getPlano()), "");
        }

        getForm().getModel().getObject().getAtendimentoSoapEnfermagem().setAvaliacao(avaliacao);
        target.add(txaAvaliacao);
    }

    private void eventoAdicionarDiagnosticoEnfermagem(AjaxRequestTarget target, DiagnosticoEnfermagem object) {
        if(getForm().getModel().getObject().getAtendimentoSoapEnfermagem().getAvaliacao() == null) {
            getForm().getModel().getObject().getAtendimentoSoapEnfermagem().setAvaliacao("");
        }
        StringBuilder builder = new StringBuilder(getForm().getModel().getObject().getAtendimentoSoapEnfermagem().getAvaliacao());
        if(CollectionUtils.isNotNullEmpty(getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList())) {
            builder.append("<br/>\n");
        }
        builder.append(object.getDescricao());
        builder.append(" ");
        builder.append(Coalesce.asString(object.getPlano()));

        txaAvaliacao.limpar(target);
        getForm().getModel().getObject().getAtendimentoSoapEnfermagem().setAvaliacao(builder.toString());
        target.add(txaAvaliacao);
    }

    private List<IColumn> getColumnsDiagnosticoEnfermagem() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AtendimentoDiagnosticoEnfermagem proxy = on(AtendimentoDiagnosticoEnfermagem.class);

        columns.add(getActionColumnDiagnosticoEnfermagem());
        columns.add(createColumn(bundle("descricaoBanco"), proxy.getDiagnosticoEnfermagem().getDescricao()));

        return columns;
    }

    private IColumn getActionColumnDiagnosticoEnfermagem() {
        return new MultipleActionCustomColumn<AtendimentoDiagnosticoEnfermagem>() {
            @Override
            public void customizeColumn(AtendimentoDiagnosticoEnfermagem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoDiagnosticoEnfermagem>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoDiagnosticoEnfermagem modelObject) throws ValidacaoException, DAOException {
                        removerDiagnosticoEnfermagem(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerDiagnosticoEnfermagem(AjaxRequestTarget target, AtendimentoDiagnosticoEnfermagem atendimentoDiagnosticoEnfermagem) {
        limparDiagnosticoEnfermagem(target);
        eventoRemoverDiagnosticoEnfermagem(target, atendimentoDiagnosticoEnfermagem.getDiagnosticoEnfermagem());
        for (int i = 0; i < getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList().size(); i++) {
            if (getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList().get(i) == atendimentoDiagnosticoEnfermagem) {
                getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList().remove(i);
                new TempHelperV3().save(getForm());
                break;
            }
        }
        tblDiagnosticosEnfermagem.update(target);
    }

    private ICollectionProvider getCollectionProviderDiagnosticoEnfermagem() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList();
            }
        };
    }

    private void adicionarDiagnosticoEnfermagem(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtendimentoDiagnosticoEnfermagem object = modelDiagnosticosEnfermagem.getObject();
        if(object.getDiagnosticoEnfermagem() == null) {
            throw new ValidacaoException(bundle("informeDiagnosticoEnfermagem"));
        }
        if(CollectionUtils.isNotNullEmpty(getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList())) {
            for (AtendimentoDiagnosticoEnfermagem atendimentoDiagnosticoEnfermagem : getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList()) {
                if(object.getDiagnosticoEnfermagem().getCodigo().equals(atendimentoDiagnosticoEnfermagem.getDiagnosticoEnfermagem().getCodigo())) {
                    throw new ValidacaoException(bundle("itemJaAdicionado"));
                }
            }
        }
        eventoAdicionarDiagnosticoEnfermagem(target, object.getDiagnosticoEnfermagem());
        getForm().getModel().getObject().getAtendimentoDiagnosticoEnfermagemList().add(object);
        limparDiagnosticoEnfermagem(target);
        tblDiagnosticosEnfermagem.update(target);
        target.focusComponent(autoCompleteConsultaDiagnosticoEnfermagem.getTxtDescricao().getTextField());

        new TempHelperV3().save(getForm());
    }

    public void limparDiagnosticoEnfermagem(AjaxRequestTarget target) {
        modelDiagnosticosEnfermagem.setObject(new AtendimentoDiagnosticoEnfermagem());
        autoCompleteConsultaDiagnosticoEnfermagem.limpar(target);
    }

    private void initDlgRoteiroEnfermagem(AjaxRequestTarget target) {
        WindowUtil.addModal(target, this, dlgRoteiroEnfermagem = new DlgRoteiroProntuario(WindowUtil.newModalId(this), PerguntaRoteiro.Tipo.ROTEIRO_ENFERMAGEM.value()) {
            @Override
            public void onGerar(AjaxRequestTarget target, List<GrupoPerguntaDTO> listGrupoPerguntaDTO) throws ValidacaoException, DAOException {
                montarObjetivoFromRoteiroEnfermagem(target, listGrupoPerguntaDTO);
            }
        });
        dlgRoteiroEnfermagem.show(target);
    }

    private void montarObjetivoFromRoteiroEnfermagem(AjaxRequestTarget target, List<GrupoPerguntaDTO> listGrupoPerguntaDTO) {
        if(CollectionUtils.isNotNullEmpty(listGrupoPerguntaDTO)) {
            StringBuilder objetivoRoteiro = new StringBuilder();
            for (GrupoPerguntaDTO grupoPerguntaDTO : listGrupoPerguntaDTO) {
                objetivoRoteiro.append(grupoPerguntaDTO.getDescricaoPerguntasGrupoHtml());
            }
            String objetivoField = getForm().getModel().getObject().getAtendimentoSoapEnfermagem().getObjetivo();
            String novoObjetivo;

            if (objetivoField != null) {
                novoObjetivo = objetivoField.concat(objetivoRoteiro.toString());
            } else {
                novoObjetivo = objetivoRoteiro.toString();
            }
            txaObjetivo.limpar(target);
            getForm().getModel().getObject().getAtendimentoSoapEnfermagem().setObjetivo(novoObjetivo);

            new TempHelperV3().save(getForm());

            target.add(txaObjetivo);
        }
    }

}