/*
 * Copyright 2012 claudio.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.component.link;

import br.com.celk.system.report.ReportExporter;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import java.io.File;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.request.handler.resource.ResourceReferenceRequestHandler;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.SharedResourceReference;

/**
 *
 * <AUTHOR>
 */
public abstract class ReportLink extends Link {
    
    public static final String TARGET_BLANK = "_blank";
    public static final String TARGET_SELF = "_self";
    public static final String TARGET_PARENT = "_parent";
    public static final String TARGET_TOP = "_top";
    
    private String target;

    
    public ReportLink(String id) {
        this(id, TARGET_BLANK);
    }
    
    public ReportLink(String id, String target) {
        super(id);
        if (target != null) {
            add(new AttributeModifier("target",target));
        }
    }
 
    @Override
    protected void disableLink(ComponentTag tag) {
        String clazz = (String) tag.getAttributes().get("class");
        
        if (clazz==null) {
            clazz = "";
        }
        
        clazz += " disabled ";
        
        tag.getAttributes().put("class", clazz);
        
        super.disableLink(tag);
    }
    
    @Override
    public void onClick() {
        try {
            DataReport dr = getDataReport();
            
            File newFile = new ReportExporter(dr, TipoRelatorio.PDF).createFile();

            ResourceReference resourceReference = new SharedResourceReference("staticReport");

            ResourceReferenceRequestHandler referenceRequestHandler = new ResourceReferenceRequestHandler(resourceReference);      
            //TODO Quando a impressao nao pode carregar o arquivo devera encaminhar para uma pagina avisando que o recurso não foi carregado. Atualmente carrega a mesma pagina
            referenceRequestHandler.getPageParameters().add("retornoLocal", newFile.getAbsolutePath());

            getRequestCycle().scheduleRequestHandlerAfterCurrent(referenceRequestHandler);
        } catch (Throwable ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } 
    }
    
    public abstract DataReport getDataReport() throws ReportException, ValidacaoException, DAOException;

}
