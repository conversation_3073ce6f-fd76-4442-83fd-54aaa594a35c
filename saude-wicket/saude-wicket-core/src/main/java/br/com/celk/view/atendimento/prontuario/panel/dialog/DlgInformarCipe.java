package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoCipe;
import br.com.ksisolucoes.vo.prontuario.basico.CondutaAtendimento;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarCipe extends Window{

    private PnlInformarCipe pnlInformarCipe;

    public DlgInformarCipe(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("cipe");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(300);
        setResizable(true);
        
        setContent(pnlInformarCipe = new PnlInformarCipe(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<AtendimentoCipe> cipeList) throws ValidacaoException, DAOException {
                close(target);
                DlgInformarCipe.this.onConfirmar(target, cipeList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, List<AtendimentoCipe> cipeList) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, List<AtendimentoCipe> cipeList){
        show(target);
        pnlInformarCipe.setObject(target, cipeList);
    }    
}