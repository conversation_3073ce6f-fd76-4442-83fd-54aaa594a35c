package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoFechamentoConta extends Panel {

    private WebMarkupContainer image;
    private MultiLineLabel label;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnCancelar;

    private final String IMG = "img-warn";

    private String message;

    public PnlConfirmacaoFechamentoConta(String id, String message) {
        super(id);
        this.message = message;
        init();
    }

    private void init() {

        add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        add(label = new MultiLineLabel("message", message));

        label.setOutputMarkupId(true);

        add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target);
            }

        });

        add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(btnCancelar = new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        configurarButtons(btnConfirmar, btnFechar);
        configuraButtonCancelar(btnCancelar);

        btnConfirmar.setDefaultFormProcessing(false);
        btnFechar.setDefaultFormProcessing(false);
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("confirmar");
    }

    public String getFecharLabel() {
        return BundleManager.getString("fechar");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

    public void configurarButtons(AbstractAjaxButton btnConfirmar, AbstractAjaxButton btnFechar) {
        btnConfirmar.add(new AttributeModifier("value", getConfirmarLabel()));
        btnFechar.add(new AttributeModifier("value", getFecharLabel()));
    }

    public void configuraButtonCancelar(AbstractAjaxButton btnCancelar) {
        btnCancelar.setVisible(false);
    }
}
