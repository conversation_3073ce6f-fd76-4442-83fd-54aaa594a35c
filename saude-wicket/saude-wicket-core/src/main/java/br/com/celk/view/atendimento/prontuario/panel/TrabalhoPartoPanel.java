package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.TrabalhoParto;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class TrabalhoPartoPanel extends ProntuarioCadastroPanel {

    private Form<TrabalhoParto> form;
    private List<TrabalhoParto> historico;
    private Table tblHistoricoTrabalhoParto;
    private InputField txtData;
    private InputField txtPressaoArterial;
    private InputField txtTemperaturaAxilar;
    private InputField txtTorusSistole;
    private InputField txtContracoesEm;
    private InputField txtBatimentoCardiacoFeto;
    private DropDown<Long> dropDownDilatacaoColo;
    private DropDown<Long> dropDownAlturaApresentacao;

    public TrabalhoPartoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        carregarHistorico();
        form = new Form("form", new CompoundPropertyModel(new TrabalhoParto()));

        TrabalhoParto proxy = on(TrabalhoParto.class);

        form.add(txtData = new InputField(path(proxy.getHora())));
        txtData.add(new TempBehaviorV2());
        
        form.add(txtPressaoArterial = new InputField(path(proxy.getPressaoArterial())));
        txtPressaoArterial.add(new TempBehaviorV2());
        
        form.add(txtTemperaturaAxilar = new InputField(path(proxy.getTemperaturaAxiliar())));
        txtTemperaturaAxilar.add(new TempBehaviorV2());
        
        form.add(txtTorusSistole = new InputField(path(proxy.getTorusSistole())));
        txtTorusSistole.add(new TempBehaviorV2());
        
        form.add(txtContracoesEm = new InputField(path(proxy.getContracoesEm())));
        txtContracoesEm.add(new TempBehaviorV2());
        
        form.add(txtBatimentoCardiacoFeto = new InputField(path(proxy.getBatimentoCardiacoFeto())));
        txtBatimentoCardiacoFeto.add(new TempBehaviorV2());
        
        form.add(getDropDownDilatacaoColo(path(proxy.getDilatacaoColo())).add(new TempBehaviorV2()));
        form.add(getDropDownAlturaApresentacao(path(proxy.getAlturaApresentacao())).add(new TempBehaviorV2()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getBolsasDagua()), TrabalhoParto.BolsasDagua.values(), true).add(new TempBehaviorV2()));
        
        form.add(tblHistoricoTrabalhoParto = new Table("tblHistoricoTrabalhoParto", getColumnsTrabalhoParto(), getCollectionProviderTrabalhoParto()));
        tblHistoricoTrabalhoParto.populate();

        add(form);
        
        form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), TrabalhoParto.class)));
        if (form.getModelObject().getHora() == null) {
            Date dataAtual = DataUtil.getDataAtual();
            txtData.setModelObject(dataAtual);
            new TempHelperV2().save(form);
        }
    }
    
    private DropDown<Long> getDropDownDilatacaoColo(String id) {
        if (dropDownDilatacaoColo == null) {
            dropDownDilatacaoColo = new DropDown<Long>(id);
            dropDownDilatacaoColo.addChoice(null, "");

            for (Long i = 1L; i < 11L; i++) {
                dropDownDilatacaoColo.addChoice(i, i.toString());
            }
        }
        return dropDownDilatacaoColo;
    }
    
    private DropDown<Long> getDropDownAlturaApresentacao(String id) {
        if (dropDownAlturaApresentacao == null) {
            dropDownAlturaApresentacao = new DropDown<Long>(id);
            dropDownAlturaApresentacao.addChoice(null, "");

            for (Long i = 1L; i < 11L; i++) {
                dropDownAlturaApresentacao.addChoice(i, i.toString());
            }
        }
        return dropDownAlturaApresentacao;
    }

    private List<IColumn> getColumnsTrabalhoParto() {
        List<IColumn> columns = new ArrayList<IColumn>();

        TrabalhoParto proxy = on(TrabalhoParto.class);

        columns.add(new DateColumn(bundle("data"), path(proxy.getHora())).setPattern("dd/MM/yyyy - HH:mm"));
        columns.add(createColumn(bundle("pressaoArterial", this), proxy.getPressaoArterial()));
        columns.add(createColumn(bundle("temperaturaAxilar", this), proxy.getTemperaturaAxiliar()));
        columns.add(createColumn(bundle("tonusSistole", this), proxy.getTorusSistole()));
        columns.add(createColumn(bundle("contracoesEm", this), proxy.getContracoesEm()));
        columns.add(createColumn(bundle("batimentoCardiacoFeto", this), proxy.getBatimentoCardiacoFeto()));
        columns.add(createColumn(bundle("dilatacaoColoCm", this), proxy.getDilatacaoColo()));
        columns.add(createColumn(bundle("alturaApresentada", this), proxy.getAlturaApresentacao()));
        columns.add(createColumn(bundle("bolsasDagua", this), proxy.getDescricaoBolsasDagua()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderTrabalhoParto() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return historico;
            }
        };
    }
    
    private void carregarHistorico(){
        historico = LoadManager.getInstance(TrabalhoParto.class)
                .addProperties(new HQLProperties(TrabalhoParto.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TrabalhoParto.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TrabalhoParto.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL), getAtendimento().getAtendimentoPrincipal()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TrabalhoParto.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TrabalhoParto.PROP_HORA), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }
}
