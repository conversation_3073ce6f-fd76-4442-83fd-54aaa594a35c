/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.celk.component.table.column;

import br.com.celk.component.css.TextAlign;
import br.com.ksisolucoes.util.Bundle;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class DayColumn <T> extends Column<T> {

    private String pattern = "EEEE";
    
    public DayColumn(String displayModel, String propertyExpression) {
        super(displayModel, propertyExpression);
    }
    
    public DayColumn(String displayModel, String sortProperty, String propertyExpression) {
        super(displayModel, sortProperty, propertyExpression);
    }

    @Override
    public void populateItem(Item<ICellPopulator<T>> item, String componentId, IModel<T> rowModel) {
        IModel<Date> createLabelModel = (IModel<Date>) createLabelModel(rowModel);

        if (createLabelModel.getObject()!=null) {
            item.add(new Label(componentId, new SimpleDateFormat(pattern, Bundle.getLocale()).format(createLabelModel.getObject())));
        } else {
            item.add(new Label(componentId, ""));
        }

        item.add(new AttributeModifier("class", TextAlign.CENTER.value()));
    }

    public DayColumn setPattern(String pattern) {
        this.pattern = pattern;
        return this;
    }
    
}