package br.com.celk.component.odontograma.behavior;

import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * Created by laudecir on 08/12/17.
 */
public class OdontoCSSEventBehavior extends AjaxEventBehavior {

    private String attribute;

    public OdontoCSSEventBehavior(String event, String attribute) {
        super(event);
        this.attribute = attribute;
    }

    @Override
    protected void onEvent(AjaxRequestTarget target) {
//        OdontoScript.css(target, this.getComponent(), this.attribute, OdontogramaDTO.Cores.AMALGAMA.getCor());
    }
}
