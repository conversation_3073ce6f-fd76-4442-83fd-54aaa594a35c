package br.com.celk.component.passwordfield;

import br.com.celk.component.behavior.attribute.AttributeRemover;
import br.com.celk.component.interfaces.IComponent;
import br.com.celk.system.util.ComponentWicketUtil;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.html.form.PasswordTextField;
import org.apache.wicket.model.IModel;
import org.odlabs.wiquery.core.IWiQueryPlugin;
import org.odlabs.wiquery.core.javascript.JsQuery;
import org.odlabs.wiquery.core.javascript.JsStatement;

/**
 * <AUTHOR>
 */
public class PasswordField extends PasswordTextField implements IComponent<String>, IWiQueryPlugin {

    private AttributeAppender errorClassAppender = new AttributeAppender("class", " error");
    private AttributeRemover erroClassRemover = new AttributeRemover("class", "error");

    public PasswordField(String id, IModel<String> model) {
        super(id, model);
        init();
    }

    public PasswordField(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        setRequired(false);
        setResetPassword(false);
        setConvertEmptyInputStringToNull(true);
    }

    @Override
    public String getComponentValue() {
        return getModelObject();
    }

    @Override
    public void setComponentValue(String value) {
        setModelObject(value);
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        setComponentValue(null);
        target.add(this);
    }

    @Override
    public void addAjaxUpdateValue() {
        add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                setModelObject(getModelObject());
            }
        });
    }

    @Override
    protected void onInvalid() {
        validateHighlightError();
    }

    @Override
    protected void onValid() {
        validateHighlightError();
    }

    private void validateHighlightError() {
        if (hasErrorMessage()) {
            addErrorClass();
        } else {
            removeErrorClass();
        }
    }

    public void addErrorClass() {
        ComponentWicketUtil.addErrorClass(this);
    }

    public void removeErrorClass() {
        ComponentWicketUtil.removeErrorClass(this);
    }

    public void addRequiredClass() {
        add(new AttributeAppender("class", " required"));
    }

    public void removeRequiredClass() {
        ComponentWicketUtil.removeRequiredClass(this);
    }

    @Override
    public JsStatement statement() {
        return new JsQuery(this).$().append(".noCapslock();");
    }
}
