package br.com.celk.component.cepField;

import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class RequiredCepField extends CepField {

    public RequiredCepField(String id) {
        super(id);
        init();
    }

    public RequiredCepField(String id, IModel<String> model) {
        super(id, model);
        init();
    }
    
    private void init(){
        setRequired(true);
        addRequiredClass();
    }

}
