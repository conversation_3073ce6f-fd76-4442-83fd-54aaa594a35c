package br.com.celk.system.authorization.interfaces.action;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface IAuthorizationAction extends Serializable{

    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
}
