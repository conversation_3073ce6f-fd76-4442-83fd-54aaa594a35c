package br.com.celk.component.table.column.panel;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class EditDeleteActionsColumnPanel extends Panel implements PermissionContainer{

    @Permission(type= Permissions.EDITAR)
    private AjaxLink btnEditar;
    
    @Permission(type= Permissions.DELETAR)
    private AjaxLink btnExcluir;
    
    private DlgConfirmacao dlgConfirmacao;
    
    public EditDeleteActionsColumnPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onEditar(target);
            }

            @Override
            public boolean isEnabled() {
                return isEditarEnabled();
            }
            
            @Override
            public boolean isVisible() {
                return isEditarVisible();
            }
            
        });
        add(btnExcluir = new AbstractAjaxLink("btnExcluir") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao!=null) {
                    dlgConfirmacao.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return isExcluirEnabled();
            }
            
            @Override
            public boolean isVisible() {
                return isExcluirVisible();
            }
            
        });
        
        btnEditar.add(new AttributeModifier("title", getEditarTitle()));
        btnExcluir.add(new AttributeModifier("title", getExcluirTitle()));
    }
    
    private void initDlgConfirmacao(AjaxRequestTarget target){
        if (dlgConfirmacao==null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), getExcluirMsg()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    onExcluir(target);
                }
            });
        }
    }

    public abstract void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public boolean isEditarEnabled(){
        return true;
    }
    
    public boolean isExcluirEnabled(){
        return true;
    }
    
    public boolean isEditarVisible(){
        return true;
    }
    
    public boolean isExcluirVisible(){
        return true;
    }
    
    public String getEditarTitle(){
        return BundleManager.getString("editar");
    }
    
    public String getExcluirTitle(){
        return BundleManager.getString("excluir");
    }

    public AjaxLink getBtnEditar() {
        return btnEditar;
    }

    public AjaxLink getBtnExcluir() {
        return btnExcluir;
    }
    
    public String getExcluirMsg(){
        return BundleManager.getString("desejaRealmenteExcluir")+"?";
    }
    
}
