package br.com.celk.view.vacina.vacinacalendario.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class VacinaCalendarioAutoCompleteSettings extends AbstractAutoCompleteSettings {
    
    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<String, String>();

        propertiesMap.put("id", o.getIdentificador());

        if (o instanceof VacinaCalendario) {
            VacinaCalendario bean = (VacinaCalendario)o;
            propertiesMap.put("name", bean.getDescricaoVO() + 
                    (!"".equals(Coalesce.asString(bean.getDosesDescricao())) ? 
                            (" - " + bean.getDescricaoIdadeFormatado() + " - " + bean.getDosesDescricao()) 
                            : (" - " + bean.getDescricaoIdadeFormatado())));
        }
        
        return propertiesMap;
    }

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();
        builder.append("<li>");
            builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
                builder.append("<div class=\"nivel-1\"> '+item.name+' </div>");
            builder.append("</div>");
        builder.append("</li>");
        
        return builder.toString();
    }
}