package br.com.celk.component.table;

import br.com.celk.component.consulta.dataprovider.IDataProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.options.DataTableOptions;
import br.com.celk.component.view.AbstractCollectionView;
import br.com.celk.component.view.CollectionView;
import org.apache.wicket.Component;
import org.apache.wicket.WicketRuntimeException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.util.lang.Args;
import org.odlabs.wiquery.core.javascript.JsQuery;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Table<T extends Serializable> extends Panel implements ITable<T>{

    private static final String CELL_REPEATER_ID = "cells";
    private static final String CELL_ITEM_ID = "cell";
    
    private AbstractCollectionView<T> collectionView;
    private WebMarkupContainer table;
    private WebMarkupContainer body;
    private List<IColumn> columns;
    private boolean populated = false;
    private ICollectionProvider collectionProvider;
    private TableHeader header;
    
    private DataTableOptions options;
    
    public Table(String id, List<ISortableColumn<T>> columns, ICollectionProvider collectionProvider) {
        super(id);

        Args.notEmpty(columns, "columns");

        this.columns = customizeColumns(columns);
        this.collectionProvider = collectionProvider;
        
        table = new WebMarkupContainer("table");
        
        options = new DataTableOptions(table);
        options.setFilter(false);
        options.setInfo(false);
        options.setLanguage("{\"sInfoEmpty\":\"\",\"sEmptyTable\": \"\",\"sZeroRecords\": \"\"}");
        options.setSort(false);
        options.setDestroy(true);
        options.setScrollX("100%");
        options.setScrollCollapse(true);
        options.setPaginate(false);
        
        table.add(header = new TableHeader("header", this){
            @Override
            public void onSelectHeader(AjaxRequestTarget target ) {
                Table.this.onSelectHeader(target);
            }
        });
        header.setOutputMarkupId(true);
        
        table.setOutputMarkupId(true);
        
        body = newBodyContainer("body");
        body.setOutputMarkupId(true);
        
        collectionView = getCollectionView("rows", collectionProvider, collectionViewStrategy);

        body.add(collectionView);
        table.add(body);
        add(table);

        setOutputMarkupId(true);
        setVersioned(false);
    }
    
    public void recreateHeader(AjaxRequestTarget target){
        table.remove(header);
        header = new TableHeader("header", this){
            @Override
            public void onSelectHeader(AjaxRequestTarget target) {
                Table.this.onSelectHeader(target);
            }
        };
        header.setOutputMarkupId(true);
        table.add(header);
        target.add(header);
    }
    
    public List<IColumn> customizeColumns(List<ISortableColumn<T>> columns){
        return  new ArrayList<IColumn>(columns);
    }

    @Override
    public Component populate() {
        populated = true;
        return this;
    }
    
    @Override
    public void populate(AjaxRequestTarget target) {
        populate();
        update(target);
    }
    
    @Override
    public boolean isPopulated(){
        return populated;
    }
    
    @Override
    public void update(AjaxRequestTarget target){
        target.add(this);
    }

    protected WebMarkupContainer newBodyContainer(final String id) {
        return new WebMarkupContainer(id);
    }
    
    public AbstractCollectionView getCollectionView(String id, ICollectionProvider collectionProvider1, final ICollectionViewStrategy collectionViewStrategy){
        return new CollectionView<T>("rows", collectionProvider1) {

            @Override
            protected void populateItem(Item<T> item) {
                collectionViewStrategy.populateItem(item);
            }

            @Override
            public Item<T> newItem(String id, int index, IModel<T> model) {
                return collectionViewStrategy.newItem(id, index, model);
            }

            @Override
            protected Iterator<IModel<T>> getItemModels() {
                if (isPopulated()) {
                    return collectionViewStrategy.getItemModels(super.getItemModels());
                }
                return newEmptyIterator();
            }

        };
    }

    public Iterator<IModel<T>> newEmptyIterator(){
        return new Iterator<IModel<T>>() {

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public IModel<T> next() {
                return new Model();
            }

            @Override
            public void remove() {
            }
        };
    }
    
    @Override
    public List<IColumn> getColumns() {
        return columns;
    }

    public WebMarkupContainer getBody() {
        return body;
    }

    public WebMarkupContainer getTable() {
        return table;
    }

    public AbstractCollectionView<T> getCollectionView() {
        return collectionView;
    }

    protected Item<ICellPopulator<T>> newCellItem(String id, int index, IModel<ICellPopulator<T>> model) {
        return new TableCell<ICellPopulator<T>> (id, index, model);
    }

    protected Item<T> newRowItem(String id, int index, IModel<T> model) {
        return new TableRow(id, index, model);
    }

    @Override
    protected void onDetach() {
        super.onDetach();

        for (IColumn column : columns) {
            column.detach();
        }
    }

    @Override
    public void limpar(AjaxRequestTarget target){
        populated = false;
        target.add(this);
    }

    @Override
    public IDataProvider getDataProvider() {
        return collectionProvider;
    }

    public ICollectionProvider getCollectionProvider() {
        return collectionProvider;
    }

    public void setScrollY(String scrollX) {
        options.setScrollY(scrollX);
    }
    
    public void setScrollX(String scrollX) {
        options.setScrollX(scrollX);
    }

    public void setScrollXInner(String scrollX) {
        options.setScrollXInner(scrollX);
    }
    
    public void setScrollCollapse(boolean scrollCollapse) {
        options.setScrollCollapse(scrollCollapse);
    }
    
    public void setColumnsDefs(String columnsDefs){
        options.setColumnsDefs(columnsDefs);
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(new JsQuery(table).$().chain("dataTable", options.getJavaScriptOptions()).render()));
    }
    
    public interface ICollectionViewStrategy<T> extends Serializable{
        
        public void populateItem(Item<T> item);

        public Item<T> newItem(String id, int index, IModel<T> model);

        public Iterator<IModel<T>> getItemModels(Iterator<IModel<T>> superItemModels);
        
    }
    
    public ICollectionViewStrategy<T> collectionViewStrategy = new ICollectionViewStrategy<T>() {

        @Override
        public void populateItem(Item<T> item) {
            RepeatingView cells = new RepeatingView(CELL_REPEATER_ID);
            item.add(cells);

            int populatorsNumber = Table.this.columns.size();
            for (int i = 0; i < populatorsNumber; i++) {
                ICellPopulator<T> populator = Table.this.columns.get(i);
                IModel<ICellPopulator<T>> populatorModel = new Model<ICellPopulator<T>>(populator);
                Item<ICellPopulator<T>> cellItem = Table.this.newCellItem(cells.newChildId(), i, populatorModel);

                cells.add(cellItem);

                populator.populateItem(cellItem, CELL_ITEM_ID, item.getModel());

                if (cellItem.get("cell") == null) {
                    throw new WicketRuntimeException(
                            populator.getClass().getName()
                            + ".populateItem() failed to add a component with id ["
                            + CELL_ITEM_ID
                            + "] to the provided [cellItem] object. Make sure you call add() on cellItem and make sure you gave the added component passed in 'componentId' id. ( *cellItem*.add(new MyComponent(*componentId*, rowModel) )");
                }
            }
        }

        @Override
        public Item<T> newItem(String id, int index, IModel<T> model) {
            return Table.this.newRowItem(id, index, model);
        }

        @Override
        public Iterator<IModel<T>> getItemModels(Iterator<IModel<T>> superItemModels) {
            return superItemModels;
        }
    };

    public void onSelectHeader(AjaxRequestTarget target){};

    public Iterator<Item<T>> getItems(){
        return collectionView.getItems();
    }
}
