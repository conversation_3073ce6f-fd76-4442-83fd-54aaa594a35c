package br.com.celk.component.dropdown.util;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.RequiredDropDown;
import br.com.celk.helper.vacinas.AplicarVacinaHelper;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import br.com.ksisolucoes.vo.vacina.GrupoAtendimentoVacinacaoEsus;
import br.com.ksisolucoes.vo.vacina.LocalAplicacao;
import br.com.ksisolucoes.vo.vacina.ViaAdministracao;
import br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.FatorRiscoSanitario;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DropDownUtil {

    public static DropDown setEnumChoices(DropDown dropDown, Enum[] enums, String blankLabel) {
        if (blankLabel != null) {
            dropDown.addChoice(null, blankLabel);
        }
        for (Enum value : enums) {
            dropDown.addChoice(value, value.toString());
        }
        return dropDown;
    }

    public static DropDown setEnumChoices(DropDown dropDown, Enum[] enums) {
        for (Enum value : enums) {
            dropDown.addChoice(value, value.toString());
        }
        return dropDown;
    }

    public static DropDown getEnumDropDown(String id, Enum[] enums, String blankLabel) {
        return setEnumChoices(new DropDown(id), enums, blankLabel);
    }

    public static DropDown getEnumDropDown(String id, Enum[] enums) {
        return setEnumChoices(new DropDown(id), enums, null);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums) {
        return setIEnumChoices(new DropDown(id), enums, false);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank) {
        return setIEnumChoices(new DropDown(id), enums, addBlank);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, boolean required) {
        if (required) {
            return setIEnumChoices(new RequiredDropDown(id), enums, addBlank);
        }
        return setIEnumChoices(new DropDown(id), enums, addBlank);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, boolean orderByValue, boolean orderAsc) {
        return getIEnumDropDown(id, enums, addBlank, false, orderByValue, orderAsc);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, boolean required, boolean orderByValue, boolean orderAsc) {
        return getIEnumDropDown(id, enums, addBlank, null, required, orderByValue, orderAsc);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, String blankLabel, boolean required, boolean orderByValue, boolean orderAsc) {
        if (required) {
            return setIEnumChoices(new RequiredDropDown(id), enums, addBlank, blankLabel, orderByValue, orderAsc);
        }

        return setIEnumChoices(new DropDown(id), enums, addBlank, blankLabel, orderByValue, orderAsc);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, String labelBlank) {
        return setIEnumChoices(new DropDown(id), enums, addBlank, labelBlank);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, String labelBlank, boolean orderByValue, boolean orderAsc) {
        return setIEnumChoices(new DropDown(id), enums, addBlank, labelBlank, orderByValue, orderAsc);
    }

    public static DropDown getIEnumDropDown(String id, IEnum[] enums, boolean addBlank, String labelBlank, boolean required) {
        if (required) {
            return setIEnumChoices(new RequiredDropDown(id), enums, addBlank, labelBlank);
        }
        return setIEnumChoices(new DropDown(id), enums, addBlank, labelBlank);
    }

    public static DropDown getIEnumDropDown(String id, IModel model, IEnum[] enums) {
        return setIEnumChoices(new DropDown(id, model), enums, false);
    }

    public static DropDown getIEnumDropDown(String id, IModel model, IEnum[] enums, boolean addBlank) {
        return setIEnumChoices(new DropDown(id, model), enums, addBlank);
    }

    public static DropDown getIEnumDropDown(String id, IModel model, IEnum[] enums, boolean addBlank, boolean required) {
        if (required) {
            return setIEnumChoices(new RequiredDropDown(id, model), enums, addBlank);
        }
        return setIEnumChoices(new DropDown(id, model), enums, addBlank);
    }

    public static DropDown getIEnumDropDownWithId(String id, IEnum[] enums, boolean addBlank, boolean required) {
        if (required) {
            return setIEnumChoicesWithId(new RequiredDropDown(id), enums, addBlank, "");
        }
        return setIEnumChoicesWithId(new DropDown(id), enums, addBlank, "");
    }

    public static DropDown<String> getSexoDropDown(DropDown dropDown) {
        dropDown.addChoice(null, "");
        dropDown.addChoice(UsuarioCadsus.SEXO_MASCULINO, BundleManager.getString("masculino"));
        dropDown.addChoice(UsuarioCadsus.SEXO_FEMININO, BundleManager.getString("feminino"));

        return dropDown;
    }

    public static DropDown<String> getSexoDropDown(String id) {
        return getSexoDropDown(id, false);
    }

    public static DropDown<String> getSexoDropDown(String id, IModel model, boolean required) {
        DropDown<String> dropDown;
        if (required) {
            dropDown = new RequiredDropDown<String>(id, model);
        } else {
            dropDown = new DropDown<String>(id, model);
        }
        return getSexoDropDown(dropDown);
    }

    public static DropDown<String> getSexoDropDown(String id, boolean required) {
        DropDown<String> dropDown;
        if (required) {
            dropDown = new RequiredDropDown<String>(id);
        } else {
            dropDown = new DropDown<String>(id);
        }
        return getSexoDropDown(dropDown);
    }

    public static DropDown<Long> getTipoAnimal(DropDown<Long> dropDown) {
        dropDown.addChoice(null, "");
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.TipoAnimal.CAO.value(), PopulacaoCaesGatosTipoAnimal.TipoAnimal.CAO.descricao());
        dropDown.addChoice(PopulacaoCaesGatosTipoAnimal.TipoAnimal.GATO.value(), PopulacaoCaesGatosTipoAnimal.TipoAnimal.GATO.descricao());
        return dropDown;
    }


    public static DropDown<String> getSimNaoDropDown(String id) {
        return getSimNaoDropDown(id, false);
    }

    public static DropDown<String> getSimNaoDropDown(String id, boolean addBlank) {
        return getSimNaoDropDown(id, addBlank, "");
    }

    public static DropDown<String> getSimNaoDropDown(String id, boolean addBlank, String labelBlank) {
        DropDown<String> dropDown = new DropDown<String>(id);

        if (addBlank) {
            dropDown.addChoice(null, labelBlank);
        }

        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));

        return dropDown;
    }

    public static DropDown<Long> getSimNaoLongDropDown(String id) {
        return getSimNaoLongDropDown(id, false, false);
    }

    public static DropDown<Integer> getSimNaoIntegerDropDown(String id) {
        return getSimNaoIntegerDropDown(id, false, false);
    }

    public static DropDown<Long> getSimNaoLongDropDown(String id, boolean addBlank, boolean required) {
        return getSimNaoLongDropDown(id, addBlank, required, null);
    }


    public static DropDown<Long> getClassePrincipioAtivoLongDropDown(String id, boolean addBlank, boolean required) {
        return getClassePrincipioAtivoLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getNaturezaExposicaoLongDropDown(String id, boolean addBlank, boolean required) {
        return getNaturezaExposicaoLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getTempoExposicaoLongDropDown(String id, boolean addBlank, boolean required) {
        return getTempoExposicaoLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getTipoTrabalhoLongDropDown(String id, boolean addBlank, boolean required) {
        return getTipoTrabalhoLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getSimNaoNaoOutroMotivoLongDropDown(String id, boolean addBlank, boolean required) {
        return getSimNaoNaoOutroMotivoLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getSimNaoNaoOutroMotivoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.SimNaoNaoOutroMotivoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getIdadeLongDropDown(String id, boolean addBlank, boolean required) {
        return getIdadeLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getIdadeLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.IdadeLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getExelenteMuitoBoaBoaRegularRuimLongDropDown(String id, boolean addBlank, boolean required) {
        return getExelenteMuitoBoaBoaRegularRuimLongDropDown(id, addBlank, required, null);
    }

    public static DropDown<Long> getExelenteMuitoBoaBoaRegularRuimLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.ExelenteMuitoBoaBoaRegularRuimLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getSimNaoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.SimNaoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getClassePrincipioAtivoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.ClassePrincipioAtivoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getNaturezaExposicaoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.NaturezaExposicaoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getTempoExposicaoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.TempoExposicaoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Long> getTipoTrabalhoLongDropDown(String id, boolean addBlank, boolean required, String labelBlank) {
        return getIEnumDropDown(id, RepositoryComponentDefault.TipoTrabalhoLong.values(), addBlank, labelBlank, required);
    }

    public static DropDown<Integer> getSimNaoIntegerDropDown(String id, boolean addBlank, boolean required) {
        return getIEnumDropDown(id, RepositoryComponentDefault.SimNaoInteger.values(), addBlank, required);
    }

    public static DropDown<Long> getSimNaoLongDropDown(String id, IModel model, boolean addBlank, boolean required) {
        if (required) {
            return setSimNaoLongChoices(new RequiredDropDown<Long>(id, model), addBlank);
        } else {
            return setSimNaoLongChoices(new DropDown<Long>(id, model), addBlank);
        }
    }

    public static DropDown<String> getNaoSimDropDown(String id) {
        return setNaoSimChoices(new DropDown<String>(id), false);
    }

    public static DropDown<String> getNaoSimDropDown(String id, boolean addBlank) {
        return setNaoSimChoices(new DropDown<String>(id), addBlank);
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id) {
        return setNaoSimLongChoices(new DropDown<Long>(id));
    }

    public static DropDown<Long> getSimNaoNaoSimLongDropDown(String id, Long primeiraOpcao) {
        if (RepositoryComponentDefault.SIM_LONG.equals(primeiraOpcao)) {
            return setSimNaoLongChoices(new DropDown<Long>(id));
        }
        return setNaoSimLongChoices(new DropDown<Long>(id));
    }

    public static DropDown<Long> getNaoSimSimNaoLongDropDown(String id, Long primeiraOpcao) {
        if (RepositoryComponentDefault.NAO_LONG.equals(primeiraOpcao)) {
            return setNaoSimLongChoices(new DropDown<Long>(id));
        }
        return setSimNaoLongChoices(new DropDown<Long>(id));
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id, String labelOpcaoNula) {
        return setNaoSimLongChoices(new DropDown<Long>(id), labelOpcaoNula);
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id, boolean addBlank) {
        return setNaoSimLongChoices(new DropDown<Long>(id), addBlank);
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id, boolean addBlank, boolean required) {
        return setNaoSimLongChoices(id, addBlank, required);
    }

    public static DropDown<Integer> getNaoSimIntegerDropDown(String id) {
        return setNaoSimIntegerChoices(new DropDown<Integer>(id));
    }

    public static DropDown<Boolean> getNaoSimBooleanDropDown(String id) {
        return setNaoSimBooleanChoices(new DropDown<Boolean>(id));
    }

    public static DropDown<Boolean> getNaoSimBooleanDropDown(String id, IModel model) {
        return setNaoSimBooleanChoices(new DropDown<Boolean>(id, model));
    }

    public static DropDown<Boolean> getSimNaoBooleanDropDown(String id) {
        return setSimNaoBooleanChoices(new DropDown<Boolean>(id));
    }

    public static DropDown<String> getNaoSimDropDown(String id, IModel model) {
        return setNaoSimChoices(new DropDown<String>(id, model), false);
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id, IModel model) {
        return setNaoSimLongChoices(new DropDown<Long>(id, model));
    }

    public static DropDown<Long> getNaoSimLongDropDown(String id, boolean addBlank, IModel model) {
        return setNaoSimLongChoices(new DropDown<Long>(id, model), addBlank);
    }

    public static DropDown<Integer> getNaoSimIntegerDropDown(String id, IModel model) {
        return setNaoSimIntegerChoices(new DropDown<Integer>(id, model));
    }

    public static DropDown<Long> getTipoAtendimentoDropDown(String id) {
        return setTipoAtendimentoChoices(new DropDown<Long>(id));
    }

    public static DropDown<TipoRelatorio> getTipoRelatorioPdfXlsDropDown(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.PDF, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.CSV, BundleManager.getString("csv"));
        dropDown.addChoice(TipoRelatorio.HTML, BundleManager.getString("html"));
        dropDown.addChoice(TipoRelatorio.TXT, BundleManager.getString("txt"));
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));

        return dropDown;
    }

    public static DropDown<EquipeArea> getAreaDropDown(String id) {
        DropDown<EquipeArea> dropDown = new DropDown<>(id);
        List<EquipeArea> equipeAreaList = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDown.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : equipeAreaList) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        return dropDown;
    }

    public static DropDown<TipoRelatorio> getTipoRelatorioDropDown(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.PDF, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.XLS, BundleManager.getString("xls"));

        return dropDown;
    }

    public static DropDown<TipoRelatorio> getTipoRelatorioDropDownXls2(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.PDF, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));

        return dropDown;
    }

    public static DropDown<TipoRelatorio> getTipoRelatorioDropDownPdfXlsCsv(String id) {
        DropDown<TipoRelatorio> dropDown = new DropDown(id);
        dropDown.addChoice(TipoRelatorio.PDF, BundleManager.getString("pdf"));
        dropDown.addChoice(TipoRelatorio.XLS2, BundleManager.getString("xls"));
        dropDown.addChoice(TipoRelatorio.CSV, BundleManager.getString("csv"));

        return dropDown;
    }

    public static DropDown<String> getCurvaProdutoDropDown(String id) {
        DropDown<String> dropDown = new DropDown<String>(id);

        dropDown.addChoice(null, BundleManager.getString("selecione"));
        dropDown.addChoice(Produto.CURVA_A, BundleManager.getString("curvaA"));
        dropDown.addChoice(Produto.CURVA_B, BundleManager.getString("curvaB"));
        dropDown.addChoice(Produto.CURVA_C, BundleManager.getString("curvaC"));

        return dropDown;
    }

    public static DropDown<String> getCriticidadeProdutoDropDown(String id) {
        DropDown<String> dropDown = new DropDown<String>(id);

        dropDown.addChoice(null, BundleManager.getString("selecione"));
        dropDown.addChoice(Produto.CRITICIDADE_X, BundleManager.getString("criticidadeX"));
        dropDown.addChoice(Produto.CRITICIDADE_Y, BundleManager.getString("criticidadeY"));
        dropDown.addChoice(Produto.CRITICIDADE_Z, BundleManager.getString("criticidadeZ"));

        return dropDown;
    }


    public static DropDown<SubGrupo> getDropDownSubGrupo(DropDown<SubGrupo> dropDownSubGrupo) {
        if (dropDownSubGrupo == null) {
            dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
        }
        return dropDownSubGrupo;
    }

    public static DropDown<GrupoProduto> getDropDownGrupo(DropDown<GrupoProduto> dropDownGrupoProduto, DropDown<SubGrupo> dropDownSubGrupo, AutoCompleteConsultaProduto autoCompleteConsultaProduto) {
        if (dropDownGrupoProduto == null) {
            dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            DropDown<GrupoProduto> finalDropDownGrupoProduto = dropDownGrupoProduto;
            dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    if (autoCompleteConsultaProduto != null) {
                        autoCompleteConsultaProduto.limpar(target);
                        autoCompleteConsultaProduto.clearInput();
                        autoCompleteConsultaProduto.setSubGrupoFixo(null);
                        autoCompleteConsultaProduto.setEnabled(false);
                    }
                    GrupoProduto grupoProduto = finalDropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return dropDownGrupoProduto;
    }

    public static DropDown getDropDownTipoProduto(DropDown dropDownTipoProduto, boolean vacinaOption) {
        if (dropDownTipoProduto == null) {
            dropDownTipoProduto = new DropDown("tipoProduto");
            dropDownTipoProduto.addChoice(null, BundleManager.getString("todos"));
            dropDownTipoProduto.addChoice(TipoProduto.TIPO_PRODUTO_MATERIAL, BundleManager.getString("material"));
            dropDownTipoProduto.addChoice(TipoProduto.TIPO_PRODUTO_MEDICAMENTO, BundleManager.getString("medicamento"));
            if (vacinaOption)
                dropDownTipoProduto.addChoice(TipoProduto.TIPO_PRODUTO_VACINA, BundleManager.getString("vacina"));
        }
        return dropDownTipoProduto;
    }

    /**
     * Método que retorna os meses do ano para um dropDown onde janeiro é igual
     * a 1.
     *
     * @param id do componente
     * @return DropDown contendo os meses do ano
     */
    public static DropDown<Long> getMesesDropDown(String id, boolean required) {
        return getMesesDropDown(id, required, true);
    }

    public static DropDown<Long> getMesesDropDown(String id, boolean required, boolean addBlank) {
        DropDown<Long> dropDown = null;
        if (required) {
            dropDown = new RequiredDropDown<Long>(id);
        } else {
            dropDown = new DropDown<Long>(id);
        }

        return setMesesChoices(dropDown, addBlank);
    }

    public static DropDown<Long> setMesesChoices(DropDown<Long> dropDown, boolean addBlank) {
        Calendar c = GregorianCalendar.getInstance();
        c.set(Calendar.MONTH, Calendar.JANUARY);

        if (addBlank) {
            dropDown.addChoice(null, "");
        }

        do {
            dropDown.addChoice(new Long(c.get(Calendar.MONTH) + 1), new SimpleDateFormat("MMMM", Bundle.getLocale()).format(c.getTime()));

            c.add(Calendar.MONTH, 1);
        } while (c.get(Calendar.MONTH) != Calendar.JANUARY);

        return dropDown;
    }

    /**
     * Método que retorna os anos para um dropDown em order decrescente, do
     * anoAtual até 1900.
     *
     * @param id do componente
     * @return DropDown contendo os anos
     */
    public static DropDown<Long> getAnoDropDown(String id, boolean required, boolean proximoAno) {
        return getAnoDropDown(id, required, true, proximoAno);
    }

    /**
     * Método que retorna os anos para um dropDown em order decrescente, do
     * anoAtual+1 até 1900.
     *
     * @param id do componente
     * @return DropDown contendo os anos
     */
    public static DropDown<Long> getAnoDropDown(String id, boolean required) {
        return getAnoDropDown(id, required, true, true);
    }

    public static DropDown<Long> getAnoDropDown(String id, boolean required, boolean addBlank, boolean proximoAno) {
        return getAnoDropDown(id, required, addBlank, 1901, proximoAno);
    }

    public static DropDown<Long> getAnoDropDown(String id, boolean required, boolean addBlank, int anoInicial, boolean proximoAno) {
        DropDown<Long> dropDown = null;
        if (required) {
            dropDown = new RequiredDropDown<Long>(id);
        } else {
            dropDown = new DropDown<Long>(id);
        }

        return setAnoChoices(dropDown, addBlank, proximoAno, anoInicial);
    }

    public static DropDown<Long> setAnoChoices(DropDown<Long> dropDown, boolean addBlank, boolean addNextYear) {
        return setAnoChoices(dropDown, addBlank, addNextYear, 1901);
    }

    public static DropDown<Long> setAnoChoices(DropDown<Long> dropDown, boolean addBlank, boolean addNextYear, int initialYear) {
        try {
            Integer x;
            if (addNextYear) {
                x = Data.getAno(Data.getDataAtual()) + 1;
            } else {
                x = Data.getAno(Data.getDataAtual());
            }

            if (addBlank) {
                dropDown.addChoice(null, "");
            }

            do {
                dropDown.addChoice(x.longValue(), x.toString());

                x -= 1;
            } while (x >= initialYear);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return dropDown;
    }

    public static DropDown<String> setNaoSimChoices(DropDown<String> dropDown, boolean addBlank) {
        if (addBlank) {
            dropDown.addChoice(null, "");
        }
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));

        return dropDown;
    }

    public static DropDown<Long> setNaoSimLongChoices(String id, boolean addBlank, boolean required) {
        if (required) {
            return setNaoSimLongChoices(new RequiredDropDown(id), addBlank);
        } else {
            return setNaoSimLongChoices(new DropDown<Long>(id), addBlank);
        }
    }

    public static DropDown<Long> setNaoSimLongChoices(DropDown<Long> dropDown, boolean addBlank) {
        if (addBlank) {
            dropDown.addChoice(null, "");
        }

        return setNaoSimLongChoices(dropDown);
    }

    public static DropDown<Long> setNaoSimLongChoices(DropDown<Long> dropDown, String labelOpcaoNula) {
        dropDown.addChoice(null, labelOpcaoNula);


        return setNaoSimLongChoices(dropDown);
    }

    public static DropDown<Long> setSimNaoLongChoices(DropDown<Long> dropDown, boolean addBlank) {
        if (addBlank) {
            dropDown.addChoice(null, "");
        }

        return setSimNaoLongChoices(dropDown);
    }

    public static DropDown<Long> setNaoSimLongChoices(DropDown<Long> dropDown) {
        dropDown.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));
        dropDown.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));

        return dropDown;
    }

    public static DropDown<Long> setSimNaoLongChoices(DropDown<Long> dropDown) {
        dropDown.addChoice(RepositoryComponentDefault.SIM_LONG, BundleManager.getString("sim"));
        dropDown.addChoice(RepositoryComponentDefault.NAO_LONG, BundleManager.getString("nao"));

        return dropDown;
    }

    public static DropDown<Long> setTipoAtendimentoChoices(DropDown<Long> dropDown) {
        dropDown.addChoice(TipoAtendimentoAgenda.TIPO_CONSULTA, BundleManager.getString("consulta"));
        dropDown.addChoice(TipoAtendimentoAgenda.TIPO_REGULACAO, BundleManager.getString("regulacao"));
        return dropDown;
    }

    public static DropDown<Integer> setNaoSimIntegerChoices(DropDown<Integer> dropDown) {
        dropDown.addChoice(RepositoryComponentDefault.NAO_INTEGER, BundleManager.getString("nao"));
        dropDown.addChoice(RepositoryComponentDefault.SIM_INTEGER, BundleManager.getString("sim"));

        return dropDown;
    }

    public static DropDown<String> setSimNaoChoices(DropDown<String> dropDown) {
        dropDown.addChoice(RepositoryComponentDefault.SIM, BundleManager.getString("sim"));
        dropDown.addChoice(RepositoryComponentDefault.NAO, BundleManager.getString("nao"));

        return dropDown;
    }

    public static DropDown<Boolean> setNaoSimBooleanChoices(DropDown<Boolean> dropDown) {
        dropDown.addChoice(false, BundleManager.getString("nao"));
        dropDown.addChoice(true, BundleManager.getString("sim"));

        return dropDown;
    }

    public static DropDown<Boolean> setSimNaoBooleanChoices(DropDown<Boolean> dropDown) {
        dropDown.addChoice(true, BundleManager.getString("sim"));
        dropDown.addChoice(false, BundleManager.getString("nao"));

        return dropDown;
    }

    public static DropDown setIEnumChoices(DropDown dropDown, IEnum[] enums, boolean addBlank) {
        return setIEnumChoices(dropDown, enums, addBlank, "");
    }

    public static DropDown setIEnumChoices(DropDown dropDown, IEnum[] enums, boolean addBlank, String labelBlank, final boolean orderByValue, final boolean orderAsc) {
        Arrays.sort(enums, new Comparator<IEnum>() {
            @Override
            public int compare(IEnum item1, IEnum item2) {
                IEnum orderPrimary = item1;
                IEnum orderSecondary = item2;

                if (!orderAsc) {
                    orderPrimary = item2;
                    orderSecondary = item1;
                }

                int compare;
                if (orderPrimary.value() != null && orderSecondary.value() != null) {
                    if (orderByValue) {
                        if (orderPrimary.value() instanceof Long) {
                            compare = ((Long) orderPrimary.value()).compareTo(((Long) orderSecondary.value()));
                        } else if (orderPrimary.value() instanceof Integer) {
                            compare = ((Integer) orderPrimary.value()).compareTo(((Integer) orderSecondary.value()));
                        } else {
                            compare = orderPrimary.value().toString().compareTo(orderSecondary.value().toString());
                        }
                    } else {
                        compare = orderPrimary.descricao().compareTo(orderSecondary.descricao());
                    }
                } else {
                    if (orderPrimary.value() == null && orderSecondary.value() != null) {
                        compare = -1;
                    } else if (orderPrimary.value() != null && orderSecondary.value() == null) {
                        compare = 1;
                    } else {
                        compare = 0;
                    }
                }

                return compare;
            }
        });

        return setIEnumChoices(dropDown, enums, addBlank, labelBlank);
    }

    public static DropDown setIEnumChoices(DropDown dropDown, IEnum[] enums, boolean addBlank, String labelBlank) {
        if (addBlank) {
            dropDown.addChoice(null, Coalesce.asString(labelBlank));
        }

        for (IEnum value : enums) {
            dropDown.addChoice(value.value(), value.descricao());
        }

        return dropDown;
    }

    public static DropDown setIEnumChoicesWithId(DropDown dropDown, IEnum[] enums, boolean addBlank, String labelBlank) {
        if (addBlank) {
            dropDown.addChoice(null, Coalesce.asString(labelBlank));
        }

        for (IEnum value : enums) {
            dropDown.addChoice(value.value(), value.value().toString() + " - " + value.descricao());
        }

        return dropDown;
    }

    public static DropDown getUrgenteSimNaoDropDown(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(RepositoryComponentDefault.URGENTE_SIM, BundleManager.getString("sim"));
        dropDown.addChoice(RepositoryComponentDefault.URGENTE_NAO, BundleManager.getString("nao"));

        return dropDown;
    }

    public static DropDown getUrgenteNaoSimDropDown(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(RepositoryComponentDefault.URGENTE_NAO, BundleManager.getString("nao"));
        dropDown.addChoice(RepositoryComponentDefault.URGENTE_SIM, BundleManager.getString("sim"));

        return dropDown;
    }

    public static DropDown getUfDropDown(String id) {
        DropDown dropDown = new DropDown(id);
        List<Estado> estados = LoadManager.getInstance(Estado.class).addProperties(new HQLProperties(Estado.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Estado.PROP_CODIGO, QueryCustom.QueryCustomParameter.NOT_IN, Arrays.asList(0L, 99L)))
                .start().getList();
        dropDown.addChoice(null, "");
        for (Estado estado : estados) {
            dropDown.addChoice(estado.getSigla(), estado.getSigla());
        }
        dropDown.setChoices(estados);
        return dropDown;
    }

    public static DropDown getUfDropDown2(String id) {
        DropDown dropDown = new DropDown(id);
        List<Estado> estados = LoadManager.getInstance(Estado.class).addProperties(new HQLProperties(Estado.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Estado.PROP_CODIGO, QueryCustom.QueryCustomParameter.NOT_IN, Arrays.asList(0L, 99L)))
                .start().getList();
        dropDown.addChoice(null, "");
        for (Estado estado : estados) {
            dropDown.addChoice(estado, estado.getSigla());
        }
        dropDown.setChoices(estados);
        return dropDown;
    }

    public static DropDown getDropDownFatorRisco(String id, FatorRiscoSanitario fatorRiscoSanitario) {
        DropDown dropDown = new DropDown(id);
        dropDown.removeAllChoices();
        if (fatorRiscoSanitario != null) {
            List<ClassificacaoGrupoEstabelecimento> list = VigilanciaHelper.findListClassificacao(fatorRiscoSanitario.getClassificacaoRisco().getCodigo());
            dropDown.removeAllChoices();
            for (ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento : list) {
                dropDown.addChoice(classificacaoGrupoEstabelecimento.getCodigo(), classificacaoGrupoEstabelecimento.getDescricao());
            }

            dropDown.setChoices(list);
        } else {
            dropDown.addChoice(null, "");
        }
        return dropDown;
    }

    public static DropDown getDropDownGrupoAtendimentoVacinacao(String id, boolean relatorio) {
        DropDown dropDown = new DropDown(id);

        List<GrupoAtendimentoVacinacaoEsus> grupos = AplicarVacinaHelper.getInstance().buscarGrupoAtendimentoVacinacaoEsusList();
        dropDown.removeAllChoices();

        if (relatorio) {
            dropDown.addChoice(null, " ");
        } else {
            dropDown.addChoice(null, "Todos");
        }

        grupos.forEach(grupo -> dropDown.addChoice(grupo.getCodigo(), grupo.getDescricao()));
        dropDown.setChoices(grupos);

        return dropDown;
    }

    public static DropDown getDropDownLocalAplicacao(String id, boolean addBlank) {
        DropDown dropDown = new DropDown(id);

        List<LocalAplicacao> locais = AplicarVacinaHelper.getInstance().buscarLocalAplicacaoList();
        dropDown.removeAllChoices();

        if (addBlank) {
            dropDown.addChoice(null, " ");
        }
        locais.forEach(local -> dropDown.addChoice(local, local.getDescricao()));
        dropDown.setChoices(locais);

        return dropDown;
    }

    public static DropDown getDropDownLocalAplicacaoVia(String id, boolean addBlank, ViaAdministracao viaAdministracao) {
        DropDown dropDown = new DropDown(id);

        List<LocalAplicacao> locais = AplicarVacinaHelper.getInstance().getLocalAplicacaoList(viaAdministracao);
        dropDown.removeAllChoices();

        if (addBlank) {
            dropDown.addChoice(null, " ");
        }
        locais.forEach(local -> dropDown.addChoice(local, local.getDescricao()));
        dropDown.setChoices(locais);

        return dropDown;
    }

    public static DropDown getDropDownViaAdministracao(String id, boolean addBlank) {
        DropDown dropDown = new DropDown(id);

        List<ViaAdministracao> vias = AplicarVacinaHelper.getInstance().buscarViaAdministracaoList();
        dropDown.removeAllChoices();

        if (addBlank) {
            dropDown.addChoice(null, " ");
        }

        vias.forEach(via -> dropDown.addChoice(via, via.getDescricao()));
        dropDown.setChoices(vias);

        return dropDown;

    }

    public static DropDown<Long> getDropDown1a5(String id, boolean required) {
        return getDropDown1a5(id, false, null, required);
    }

    public static DropDown<Long> getDropDown1a5(String id, boolean addBlank, String blankLabel, boolean required) {
        DropDown dropDown = new DropDown(id);

        if (addBlank) {
            dropDown.addChoice(null, blankLabel);
        }

        dropDown.setRequired(required);
        dropDown.setOutputMarkupId(true);
        dropDown.addAjaxUpdateValue();

        dropDown.addChoice(0L, Bundle.getStringApplication("discordo_completamente"));
        dropDown.addChoice(1L, Bundle.getStringApplication("discordo_razoavelmente"));
        dropDown.addChoice(2L, Bundle.getStringApplication("discordo_ligeiramente"));
        dropDown.addChoice(3L, Bundle.getStringApplication("nao_sei_dizer"));
        dropDown.addChoice(4L, Bundle.getStringApplication("concordo_ligeiramente"));
        dropDown.addChoice(5L, Bundle.getStringApplication("concordo_razoavelmente"));
        dropDown.addChoice(6L, Bundle.getStringApplication("concordo_completamente"));

        return dropDown;
    }

    public static DropDown<Long> getEnviadosDropDown(String id, boolean addBlank, boolean required) {
        DropDown dropDown = new DropDown(id);

        if (addBlank) {
            dropDown.addChoice(null, " ");
        }

        dropDown.setRequired(required);
        dropDown.setOutputMarkupId(true);
        dropDown.addAjaxUpdateValue();

        dropDown.addChoice(1L, Bundle.getStringApplication("ar"));
        dropDown.addChoice(2L, Bundle.getStringApplication("email"));
        dropDown.addChoice(3L, Bundle.getStringApplication("edital"));

        return dropDown;
    }

    public static DropDown<Long> getDropDown0e1(String id, boolean required) {
        return getDropDown1a5(id, false, null, required);
    }

    public static DropDown<Long> getDropDown0e1(String id, boolean addBlank, String blankLabel, boolean required) {
        DropDown dropDown = new DropDown(id);

        if (addBlank) {
            dropDown.addChoice(null, blankLabel);
        }

        dropDown.setRequired(required);
        dropDown.setOutputMarkupId(true);
        dropDown.addAjaxUpdateValue();

        dropDown.addChoice(0L, "Discordo");
        dropDown.addChoice(1L, "Concordo");

        return dropDown;
    }

    public static DropDown getDropDown0a4(String id) {
        return getDropDown0a4(id, false);
    }

    public static DropDown getDropDown0a4(String id, boolean addBlank) {
        DropDown dropDown = new DropDown(id);

        dropDown.setOutputMarkupId(true);
        dropDown.addAjaxUpdateValue();

        if (addBlank) {
            dropDown.addChoice(null, "");
        }

        dropDown.addChoice(0L, "0");
        dropDown.addChoice(1L, "1");
        dropDown.addChoice(2L, "2");
        dropDown.addChoice(3L, "3");
        dropDown.addChoice(4L, "4");

        return dropDown;
    }

    public static DropDown getDropDown0e1StartBack(String id, boolean addBlank, String blankLabel, boolean required) {
        DropDown dropDown = new DropDown(id);

        if (addBlank) {
            dropDown.addChoice(null, blankLabel);
        }

        dropDown.setRequired(required);
        dropDown.setOutputMarkupId(true);
        dropDown.addAjaxUpdateValue();

        dropDown.addChoice(0L, "Nada");
        dropDown.addChoice(0, "Pouco");
        dropDown.addChoice(0D, "Moderado");
        dropDown.addChoice(1, "Muito");
        dropDown.addChoice(1L, "Extremamente");

        return dropDown;
    }

    public static DropDown<TipoDocumentoAtendimento> getDropDownTipoDocumentoAtendimento(String id, String textoOpcaoNull, Atendimento atendimento, TipoDocumentoAtendimento.TipoDocumento tipoDocumento) {
        DropDown<TipoDocumentoAtendimento> dropDown = new DropDown<>(id, new Model<>());

        List<TipoDocumentoAtendimento> tipoDocumentoAtendimentoList = new ArrayList<>();
        try {
            tipoDocumentoAtendimentoList = BOFactoryWicket.getBO(AtendimentoFacade.class).getTipoDocumentoAtendimentoEvolucao(atendimento, tipoDocumento);
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.error(e);
        }

        dropDown.addChoice(null, textoOpcaoNull);

        for (TipoDocumentoAtendimento tipoDocumentoAtendimento : tipoDocumentoAtendimentoList) {
            dropDown.addChoice(tipoDocumentoAtendimento, tipoDocumentoAtendimento.getDescricao());
        }

        return dropDown;
    }

    public static DropDown<TipoDocumentoAtendimento> getDropDownTipoDocumentoRecepcao(String id, String textoOpcaoNull, TipoDocumentoAtendimento.TipoDocumento tipoDocumento) {
        DropDown<TipoDocumentoAtendimento> dropDown = new DropDown<>(id, new Model<>());

        List<TipoDocumentoAtendimento> tipoDocumentoAtendimentoList = new ArrayList<>();
        try {
            tipoDocumentoAtendimentoList = BOFactoryWicket.getBO(AtendimentoFacade.class).getTipoDocumentoAtendimentoRecepcao(tipoDocumento);
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.error(e);
        }

        dropDown.addChoice(null, textoOpcaoNull);

        for (TipoDocumentoAtendimento tipoDocumentoAtendimento : tipoDocumentoAtendimentoList) {
            dropDown.addChoice(tipoDocumentoAtendimento, tipoDocumentoAtendimento.getDescricao());
        }

        return dropDown;
    }

    public static DropDown<Long> getDropDownTipoFinanciamento(String id) {
        DropDown<Long> dropDownTipoFinanciamento = new DropDown<Long>(id);

        dropDownTipoFinanciamento.addChoice(null, "");
        for (Aih.TipoFinanciamento list : Aih.TipoFinanciamento.values()) {
            dropDownTipoFinanciamento.addChoice(list.value(), list.descricao());
        }

        return dropDownTipoFinanciamento;
    }

    public static DropDown<LeitoQuarto> getDropDownLeitoQuarto(String id, LeitoQuarto leitoQuartoOcupado, LeitoQuarto leitoQuartoReservado) {
        DropDown dropDownLeitoQuarto = new DropDown<LeitoQuarto>(id);
        List<LeitoQuarto> lst = LoadManager.getInstance(LeitoQuarto.class)
                .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.IGUAL, LeitoQuarto.Situacao.LIBERADO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_EMPRESA, Empresa.PROP_CODIGO), ApplicationSession.get().getSessaoAplicacao().getEmpresa().getCodigo()))
                .start().getList();
        if (leitoQuartoOcupado != null) {
            dropDownLeitoQuarto.addChoice(leitoQuartoOcupado, leitoQuartoOcupado.getDescricao());
        }
        if (leitoQuartoReservado != null) {
            dropDownLeitoQuarto.addChoice(leitoQuartoReservado, leitoQuartoReservado.getDescricao());
        }
        if (CollectionUtils.isNotNullEmpty(lst)) {
            for (LeitoQuarto _leitoQuarto : lst) {
                dropDownLeitoQuarto.addChoice(_leitoQuarto, _leitoQuarto.getDescricao());
            }
        }

        return dropDownLeitoQuarto;
    }
}
