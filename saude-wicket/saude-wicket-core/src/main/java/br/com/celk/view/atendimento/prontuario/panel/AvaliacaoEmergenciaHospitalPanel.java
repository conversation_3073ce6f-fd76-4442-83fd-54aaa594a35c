package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.msdropdown.MsDropDown;
import br.com.celk.component.msdropdown.MsItem;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.temp.behavior.TempBehavior;
import br.com.celk.component.temp.interfaces.TempHelper;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.behavior.interfaces.ILoadListener;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.ScoreNewsCalculator;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoPrimarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ComorbidadePaciente;
import br.com.ksisolucoes.vo.basico.IndiceImc;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.DropDownChoice;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.SharedResourceReference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class AvaliacaoEmergenciaHospitalPanel extends ProntuarioCadastroPanel {

    private Form<AtendimentoPrimarioDTO> form;
    private List<AtendimentoPrimario> historico;
    private List<ComorbidadePaciente> comorbidadePacienteList;
    private Table tblHistoricoAvaliacoes;
    private InputField txtDataAvaliacao;
    private InputField txtMotivoNaoCompareceuUnidadeSaude;
    private InputArea txaMotivoConsulta;
    private AttributeModifier modifierVermelho = new AttributeModifier("style", "color: rgb(255, 102, 102);");
    private WebMarkupContainer containerDadosAdicionais;
    private WebMarkupContainer containerNivelConsciencia;
    private WebMarkupContainer dadosAntropometricos;
    private WebMarkupContainer sinaisVitais;
    private WebMarkupContainer glicemia;
    private WebMarkupContainer containerClassificacaoRisco;
    private String textoImc;
    private String textoSituacaoImc;
    private Label lblSituacaoImc;
    private Label lblImc;
    private Component txtAltura;
    private Component txtPeso;
    private Component txtGlicemia;
    private Label lblAvaliacaoGlicemia;
    private Label lblResultadoNivelConsciencia;
    private RadioButtonGroup radioGroup;
    private MsDropDown<ClassificacaoRisco> cbxClassificaoRisco;
    private DropDown<String> cbxAlergico;
    private InputArea txaDescricaoAlergia;
    private LongField txtResultadoNivelConsciencia;
    private DropDown<Long> dropDownAtendimentoRN;
    private DropDown dropDownSubRisco;
    private Label lblResultadoSinaisVitais;
    private Component txtPAS;
    private Component txtPAD;
    private Component txtFreqCardiaca;
    private Component txtTemperatura;
    private Component txtSaturacaoOxigenio;
    private Component txtFreqRespiratoria;


    public AvaliacaoEmergenciaHospitalPanel(String id) {
        super(id, BundleManager.getString("avaliacao"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        carregarAtendimentoPrimario();
        form = new Form("form", new CompoundPropertyModel(carregarAtendimentoPrimario()));
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                .addProperty(UsuarioCadsusDado.PROP_PESO_NASCER)
                .addProperty(UsuarioCadsusDado.PROP_DESCRICAO_ALERGICO)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus().getCodigo()))
                .start().getVO();
        AtendimentoPrimarioDTO proxy = on(AtendimentoPrimarioDTO.class);

        form.add(txtDataAvaliacao = new InputField(path(proxy.getAtendimentoPrimario().getDataAvaliacao())));
        txtDataAvaliacao.add(new TempBehaviorV2());
        form.add(txaMotivoConsulta = new InputArea(path(proxy.getAtendimentoPrimario().getMotivoConsulta())));
        txaMotivoConsulta.add(new TempBehaviorV2());
        form.add(txtMotivoNaoCompareceuUnidadeSaude = new InputField(path(proxy.getAtendimentoPrimario().getMotivoNaoProcurouUnidadeSaude())));
        txtMotivoNaoCompareceuUnidadeSaude.add(new TempBehaviorV2());

        form.add(dadosAntropometricos = new WebMarkupContainer("dadosAntropometricos"));
        dadosAntropometricos.add(txtPeso = new DoubleField(path(proxy.getAtendimentoPrimario().getPeso())).setMDec(3).setVMax(300D).add(new TempBehaviorV2()));
        dadosAntropometricos.add(txtAltura = new DoubleField(path(proxy.getAtendimentoPrimario().getAltura())).setMDec(1).add(new TempBehaviorV2()));
        txtPeso.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoImc(art);
            }
        });

        txtAltura.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoImc(art);
            }
        });

        dadosAntropometricos.add(new DoubleField(path(proxy.getAtendimentoPrimario().getPerimetroCefalico())).add(new TempBehaviorV2()));
        dadosAntropometricos.add(lblImc = new Label("textoImc", new PropertyModel<String>(this, "textoImc")));
        dadosAntropometricos.add(lblSituacaoImc = new Label("textoSituacaoImc", new PropertyModel<String>(this, "textoSituacaoImc")));
        lblImc.setOutputMarkupId(true);
        lblSituacaoImc.setOutputMarkupId(true);
        
        form.add(containerDadosAdicionais = new WebMarkupContainer("containerDadosAdicionais"));
        containerDadosAdicionais.add(DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getProvaLaco()), AtendimentoPrimario.ProvaLaco.values(), true, false).add(new TempBehaviorV2()));


        form.add(sinaisVitais = new WebMarkupContainer("sinaisVitais"));
        sinaisVitais.add(txtPAS = new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialSistolica())).setLabel(new Model(bundle("pas"))).add(new TempBehaviorV2()));
        sinaisVitais.add(txtPAD = new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialDiastolica())).setLabel(new Model(bundle("pad"))).add(new TempBehaviorV2()));
        sinaisVitais.add(txtFreqCardiaca = new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaCardiaca())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtFreqRespiratoria = new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaRespiratoria())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtTemperatura = new DoubleField(path(proxy.getAtendimentoPrimario().getTemperatura())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtSaturacaoOxigenio = new LongField(path(proxy.getAtendimentoPrimario().getSaturacaoOxigenio())).add(new TempBehaviorV2()));

        txtPAS.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtPAD.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtFreqCardiaca.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtFreqRespiratoria.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtTemperatura.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtSaturacaoOxigenio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        sinaisVitais.add(lblResultadoSinaisVitais = new Label("lblResultadoSinaisVitais"));
        lblResultadoSinaisVitais.setOutputMarkupId(true);
        lblResultadoSinaisVitais.setDefaultModel(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return "";
            }
        });

        DropDownChoice<AtendimentoPrimario.NivelConsciencia> dropDownNivelConsciencia = DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getNivelConscienciaAvpu()), AtendimentoPrimario.NivelConsciencia.values(), true, false);
        dropDownNivelConsciencia.add(new TempBehaviorV2());
        dropDownNivelConsciencia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });
        sinaisVitais.add(dropDownNivelConsciencia);

        DropDownChoice<AtendimentoPrimario.UsoOxigenacaoSuplementar> dropDownOxigenacao = DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getUsoOxigenacaoSuplementar()), AtendimentoPrimario.UsoOxigenacaoSuplementar.values(), true, false);
        dropDownOxigenacao.add(new TempBehaviorV2());
        dropDownOxigenacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });
        sinaisVitais.add(dropDownOxigenacao);
        
        form.add(containerNivelConsciencia = new WebMarkupContainer("containerNivelConsciencia"));
        containerNivelConsciencia.add(txtResultadoNivelConsciencia = (LongField) new LongField(path(proxy.getAtendimentoPrimario().getResultadoNivelConsciencia())).add(new TempBehaviorV2()));
        txtResultadoNivelConsciencia.setVMin(0L);
        txtResultadoNivelConsciencia.setVMax(99L);
        txtResultadoNivelConsciencia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarResultadoNivelConsciencia(target);
            }
        });
        containerNivelConsciencia.add(lblResultadoNivelConsciencia = new Label("lblResultadoNivelConsciencia"));
        lblResultadoNivelConsciencia.setOutputMarkupId(true);
        lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return "";
            }
        });

        form.add(cbxAlergico = (DropDown<String>) DropDownUtil.getNaoSimDropDown(path(proxy.getAtendimentoPrimario().getFlagAlergico())).add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarAlergia(target);
                new TempHelper().save(target, txaDescricaoAlergia);
            }
        }).add(new TempBehavior()));
        form.add(txaDescricaoAlergia = new InputArea(path(proxy.getAtendimentoPrimario().getDescricaoAlergia())));
        txaDescricaoAlergia.add(new TempBehavior());

        form.add(new InputArea(path(proxy.getAtendimentoPrimario().getHistorico())).add(new TempBehaviorV2()));
        
        form.add(tblHistoricoAvaliacoes = new Table("tblHistoricoAvaliacoes", getColumnsHistoricoAvaliacoes(), getCollectionProviderHistoricoAvaliacoes()));
        tblHistoricoAvaliacoes.populate();
        tblHistoricoAvaliacoes.setScrollY("272px");
        tblHistoricoAvaliacoes.setScrollX("768px");

        form.add(glicemia = new WebMarkupContainer("glicemia"));
        glicemia.add(txtGlicemia = new LongField(path(proxy.getAtendimentoPrimario().getGlicemia())).add(new TempBehaviorV2()));
        txtGlicemia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarGlicemia(target);
            }
        });
        glicemia.add(radioGroup = (RadioButtonGroup) new RadioButtonGroup(path(proxy.getAtendimentoPrimario().getGlicemiaTipo())).add(new TempBehaviorV2()));
        radioGroup.add(new AjaxRadio("emJejum", new Model(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                avaliarGlicemia(target);
            }
        });

        radioGroup.add(new AjaxRadio("posPrandial", new Model(AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL)) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                avaliarGlicemia(target);
            }
        });
        glicemia.add(lblAvaliacaoGlicemia = new Label("lblAvaliacaoGlicemia"));
        lblAvaliacaoGlicemia.setOutputMarkupId(true);
        lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return "";
            }
        });

        form.add(containerClassificacaoRisco = new WebMarkupContainer("containerClassificacaoRisco"));
        containerClassificacaoRisco.add(getCbxClassificacaoRisco(path(proxy.getAtendimentoPrimario().getAtendimento().getClassificacaoRisco())).add(new TempBehaviorV2()));
        containerClassificacaoRisco.add(getDropDownSubRisco(path(proxy.getSubClassificacaoRisco())).add(new TempBehaviorV2()));

        add(form);

        dropDownAtendimentoRN = new DropDown<Long>(path(proxy.getAtendimentoPrimario().getAtendimentoRN()));
        dropDownAtendimentoRN.addChoice(RepositoryComponentDefault.NAO_LONG, "Não");
        dropDownAtendimentoRN.addChoice(RepositoryComponentDefault.SIM_LONG, "Sim");
        form.add(dropDownAtendimentoRN.add(new TempBehaviorV2()));
        form.add(DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getIndicadorAcidente()), AtendimentoInformacao.IndicadorAcidente.values()).add(new TempBehaviorV2()));

        if (usuarioCadsusDado != null && StringUtils.trimToNull(usuarioCadsusDado.getDescricaoAlergico()) != null) {
            cbxAlergico.setModelObject(RepositoryComponentDefault.SIM);
            txaDescricaoAlergia.setComponentValue(usuarioCadsusDado.getDescricaoAlergico());
            new TempHelper().save(cbxAlergico, RepositoryComponentDefault.SIM);
        }

        if (form.getModelObject().getAtendimentoPrimario().getFlagAlergico() == null) {
            cbxAlergico.setModelObject(RepositoryComponentDefault.NAO);
            new TempHelper().save(cbxAlergico, RepositoryComponentDefault.NAO);
        }

        form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), AtendimentoPrimarioDTO.class)).add(new ILoadListener<AtendimentoPrimarioDTO>() {
            @Override
            public void afterLoad(AtendimentoPrimarioDTO modelObject) {
                comorbidadePacienteList = modelObject.getComorbidadePacienteList();
            }
        }));

        if (form.getModelObject().getAtendimentoPrimario().getDataAvaliacao() == null) {
            Date dataAtual = DataUtil.getDataAtual();
            txtDataAvaliacao.setModelObject(dataAtual);
            new TempHelperV2().save(form);
        }

        avaliarAlergia(null);
        eventoImc(null);

        avaliarGlicemia(null);
        carregaDropDown(null);
        avaliarResultadoNivelConsciencia(null);
        if (form.getModelObject().getAtendimentoPrimario().getGlicemiaTipo() == null) {
            form.getModelObject().getAtendimentoPrimario().setGlicemiaTipo(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM);
        }
    }

    private List<IColumn> getColumnsHistoricoAvaliacoes() {
        List<IColumn> columns = new ArrayList<IColumn>();

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        columns.add(createColumn(bundle("data"), proxy.getAtendimento().getDataAtendimento()));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pa"), proxy.getPressaoArterial()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("frequenciaCardiacaAbrev"), proxy.getFrequenciaCardiaca()));
        columns.add(createColumn(bundle("saturacaoOxigenioAbrev"), proxy.getSaturacaoOxigenio()));
        columns.add(createColumn(bundle("resultadoNivelConsciencia"), proxy.getResultadoNivelConsciencia()));
        columns.add(createColumn(bundle("imc"), proxy.getImc()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistoricoAvaliacoes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return historico;
            }
        };
    }

    private AtendimentoPrimarioDTO carregarAtendimentoPrimario() {
        AtendimentoPrimarioDTO dto = new AtendimentoPrimarioDTO();
        try {
            dto = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarDadosAtendimentoPrimario(getAtendimento().getUsuarioCadsus().getCodigo(), getAtendimento());
            historico = dto.getAtendimentoPrimarioList();
            comorbidadePacienteList = dto.getComorbidadePacienteList();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (dto.getAtendimentoPrimario() == null) {
            dto.setAtendimentoPrimario(new AtendimentoPrimario());
            dto.getAtendimentoPrimario().setAtendimento(getAtendimento());
        }
        return dto;
    }
    
    private void eventoImc(AjaxRequestTarget target) {
        textoImc = "IMC: ";
        textoSituacaoImc = "";

        Long menorIdade = LoadManager.getInstance(IndiceImc.class)
                .addGroup(new QueryCustom.QueryCustomGroup(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryGroup.MIN))
                .start().getVO();
        if (getAtendimento().getUsuarioCadsus().getIdadeEmMeses() >= menorIdade) {
            Double imc = null;
            IndiceImc indiceImc = null;
            double altura = Coalesce.asDouble(form.getModelObject().getAtendimentoPrimario().getAltura(), 0D);
            if (altura != 0D && form.getModelObject().getAtendimentoPrimario().getPeso() != null) {
                imc = CalculosUtil.calculoImc((altura / 100), form.getModelObject().getAtendimentoPrimario().getPeso());
                textoImc = textoImc + Valor.adicionarFormatacaoMonetaria(imc);
                boolean gestante = UsuarioCadsusHelper.isGestante(getAtendimento().getUsuarioCadsus().getCodigo());
                Long idadeGestacional = null;
                if (gestante) {
                    PreNatal preNatal = form.getModel().getObject().getPreNatal();
                    if (preNatal != null && preNatal.getDataUltimaMenstruacao() != null) {
                        idadeGestacional = Data.calcularIdadeGestacional(preNatal.getDataUltimaMenstruacao(), getAtendimento().getDataChegada());
                    }
                }
                LoadManager load = LoadManager.getInstance(IndiceImc.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, imc))
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, imc))
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_SEXO, BuilderQueryCustom.QueryParameter.IGUAL, getAtendimento().getUsuarioCadsus().getSexo(), HQLHelper.RESOLVE_CHAR_TYPE, getAtendimento().getUsuarioCadsus().getSexo()));
                if (gestante) {
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_GESTANTE, gestante ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG));
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_GESTACIONAL, BuilderQueryCustom.QueryParameter.IGUAL, idadeGestacional));
                } else {
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
                }

                List<IndiceImc> lstIndiceImc = load.start().getList();

                if (lstIndiceImc.size() < 1 || lstIndiceImc.isEmpty()) {
                    textoSituacaoImc = BundleManager.getString("desconhecido");
                } else {
                    textoSituacaoImc = lstIndiceImc.get(0).getSituacao();
                    indiceImc = lstIndiceImc.get(0);
                }
            }
            form.getModelObject().getAtendimentoPrimario().setImc(imc);
            form.getModelObject().getAtendimentoPrimario().setIndiceImc(indiceImc);
            new TempHelperV2().save(form);
        }

        if (target != null) {
            target.add(lblSituacaoImc);
            target.add(lblImc);
        }
    }

    private MsDropDown<ClassificacaoRisco> getCbxClassificacaoRisco(String id) {
        if (cbxClassificaoRisco == null) {
            cbxClassificaoRisco = new MsDropDown<ClassificacaoRisco>(id);

            List<ClassificacaoRisco> lstClassificacao = LoadManager.getInstance(ClassificacaoRisco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoRisco.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                    .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();

            for (ClassificacaoRisco classificacaoRisco : lstClassificacao) {
                cbxClassificaoRisco.addChoice(new MsItem<ClassificacaoRisco>(classificacaoRisco, classificacaoRisco.getDescricao(), urlFor(new SharedResourceReference(Resources.class, classificacaoRisco.getCaminhoImagem()), null).toString()));
            }

            cbxClassificaoRisco.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubRisco.removeAllChoices();
                    if  (target != null) {
                        carregaDropDown(target);
                    }
                }
            });
        }
        return cbxClassificaoRisco;
    }

    private void avaliarAlergia(AjaxRequestTarget target) {
        boolean enable = RepositoryComponentDefault.SIM.equals(form.getModelObject().getAtendimentoPrimario().getFlagAlergico());
        txaDescricaoAlergia.setEnabled(enable);
        if (target != null) {
            if (!enable) {
                txaDescricaoAlergia.limpar(target);
            } else {
                target.add(txaDescricaoAlergia);
            }
        }
    }

    private void avaliarGlicemia(AjaxRequestTarget target) {
        if (Coalesce.asLong(form.getModelObject().getAtendimentoPrimario().getGlicemia()) > 0) {
            if (form.getModelObject().getAtendimentoPrimario().getGlicemiaTipo() == null || AtendimentoPrimario.TIPO_GLICEMIA_JEJUM.equals(form.getModelObject().getAtendimentoPrimario().getGlicemiaTipo())) {
                Long glicemiaPosJejumInicial = null;
                Long glicemiaPosJejumFinal = null;

                try {
                    IParameterModuleContainer ipmc = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
                    glicemiaPosJejumInicial = ipmc.getParametro("GlicemiaPosJejumInicial");
                    glicemiaPosJejumFinal = ipmc.getParametro("GlicemiaPosJejumFinal");
                } catch (Exception ex) {
                    form.setEnabled(false);
                    if (target != null) {
                        MessageUtil.modalWarn(target, this, ex);
                        target.add(form);
                    } else {
                        warn(ex.getMessage());
                    }
                    return;
                }

                if (form.getModelObject().getAtendimentoPrimario().getGlicemia() >= glicemiaPosJejumInicial
                        && form.getModelObject().getAtendimentoPrimario().getGlicemia() <= glicemiaPosJejumFinal) {
                    lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                        @Override
                        protected String load() {
                            return bundle("normal");
                        }
                    });
                    setLabelNormal();
                } else {
                    lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                        @Override
                        protected String load() {
                            return bundle("foraNormalidade");
                        }
                    });
                    setLabelVermelho();
                }
            } else {
                Long glicemiaPosPrandialInicial = null;
                Long glicemiaPosPrandialFinal = null;

                try {
                    IParameterModuleContainer ipmc = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
                    glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPosPrandialInicial");
                    glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPosPrandialFinal");
                } catch (Exception ex) {
                    form.setEnabled(false);
                    if (target != null) {
                        MessageUtil.modalWarn(target, this, ex);
                        target.add(form);
                    } else {
                        warn(ex.getMessage());
                    }
                    return;
                }

                if (form.getModelObject().getAtendimentoPrimario().getGlicemia() >= glicemiaPosPrandialInicial
                        && form.getModelObject().getAtendimentoPrimario().getGlicemia() <= glicemiaPosPrandialFinal) {
                    lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                        @Override
                        protected String load() {
                            return bundle("normal");
                        }
                    });
                    setLabelNormal();
                } else {
                    lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                        @Override
                        protected String load() {
                            return bundle("foraNormalidade");
                        }
                    });
                    setLabelVermelho();
                }
            }
        } else {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return "";
                }
            });
        }

        if (target != null) {
            target.add(lblAvaliacaoGlicemia);
        }
    }

    private void setLabelNormal() {
        if (lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.remove(modifierVermelho);
        }
    }

    private void setLabelVermelho() {
        if (!lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.add(modifierVermelho);
        }
    }
    private void avaliarResultadoNivelConsciencia(AjaxRequestTarget target) {
        Long resultadoNivelConsciencia = form.getModelObject().getAtendimentoPrimario().getResultadoNivelConsciencia();
        
        if (Coalesce.asLong(resultadoNivelConsciencia) > 0) {
            if (resultadoNivelConsciencia >= 3 && resultadoNivelConsciencia <= 8) {
                lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {

                    @Override
                    protected String load() {
                        return bundle("grave");
                    }
                });
                setLabelResultadoNivelConscienciaNormal();
            } else if (resultadoNivelConsciencia >= 9 && resultadoNivelConsciencia <= 12) {
                lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {

                    @Override
                    protected String load() {
                        return bundle("moderado");
                    }
                });
                setLabelResultadoNivelConscienciaNormal();
            } else if (resultadoNivelConsciencia >= 13 && resultadoNivelConsciencia <= 15) {
                lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {

                    @Override
                    protected String load() {
                        return bundle("leve");
                    }
                });
                setLabelResultadoNivelConscienciaNormal();
            } else {
                lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {

                    @Override
                    protected String load() {
                        return bundle("resultadoInvalido");
                    }
                });
                setLabelResultadoNivelConscienciaVermelho();
            }
        } else {
            lblResultadoNivelConsciencia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return "";
                }
            });
        }

        if (target != null) {
            target.add(lblResultadoNivelConsciencia);
        }
    }

    private void carregaDropDown(AjaxRequestTarget target){
        List<SubClassificacaoRisco> subClassificacaoRiscoList = LoadManager.getInstance(SubClassificacaoRisco.class)
                .addProperties(new HQLProperties(SubClassificacaoRisco.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SubClassificacaoRisco.PROP_CLASSIFICACAO_RISCO, cbxClassificaoRisco.getComponentValue()))
                .addSorter(new QueryCustom.QueryCustomSorter(SubClassificacaoRisco.PROP_VALOR, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(subClassificacaoRiscoList)) {
            dropDownSubRisco.addChoice(null,"");
            for (SubClassificacaoRisco subClassificacaoRisco : subClassificacaoRiscoList) {
                dropDownSubRisco.addChoice(subClassificacaoRisco, subClassificacaoRisco.getDescricao());
            }
        }
        if (target != null) {
            target.add(dropDownSubRisco);
        }

        new TempHelperV2().save(form);
    }

    private DropDown<SubClassificacaoRisco> getDropDownSubRisco(String id){
        dropDownSubRisco = new DropDown(id);
        dropDownSubRisco.addChoice(null,"");
        return dropDownSubRisco;
    }
    private void setLabelResultadoNivelConscienciaNormal() {
        if (lblResultadoNivelConsciencia.getBehaviors().contains(modifierVermelho)) {
            lblResultadoNivelConsciencia.remove(modifierVermelho);
        }
    }

    private void setLabelResultadoNivelConscienciaVermelho() {
        if (!lblResultadoNivelConsciencia.getBehaviors().contains(modifierVermelho)) {
            lblResultadoNivelConsciencia.add(modifierVermelho);
        }
    }

    private void calcularNEWS(AjaxRequestTarget target) {
        AtendimentoPrimarioDTO proxy = form.getModelObject();
        Long score = ScoreNewsCalculator.calcularNEWS(proxy);
        proxy.getAtendimentoPrimario().setScoreNewsSinaisVitais(score);

        mostrarResultado(score, target);
    }

    private void mostrarResultado(Long score, AjaxRequestTarget target) {
        String situacaoNews = bundle(ScoreNewsCalculator.determinarRisco(score));

        lblResultadoSinaisVitais.setDefaultModelObject(situacaoNews);
        if (target != null) {
            target.add(lblResultadoSinaisVitais);
        }
    }

}
