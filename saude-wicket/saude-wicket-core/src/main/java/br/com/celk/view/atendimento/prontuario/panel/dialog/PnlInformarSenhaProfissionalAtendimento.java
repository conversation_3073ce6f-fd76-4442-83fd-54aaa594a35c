package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

public abstract class PnlInformarSenhaProfissionalAtendimento extends Panel {

    private InputField<String> inputUsuario;
    private PasswordField inputSenha;
    private Usuario usuario;
    private String senha;

    public PnlInformarSenhaProfissionalAtendimento(String id, Usuario usuario) {
        super(id);
        this.usuario = usuario;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(inputUsuario = new InputField("usuario", new PropertyModel(this, "usuario.login")));
        inputUsuario.setEnabled(false);
        form.add(inputSenha = new PasswordField("senha"));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (senha == null) {
                    throw new ValidacaoException(BundleManager.getString("informe_senha"));
                }

                if (!usuario.getSenha().equals(Util.criptografarSenha(senha))) {
                    throw new ValidacaoException(BundleManager.getString("msgSenhaInvalida"));
                } else if (Usuario.STATUS_INATIVO.equals(usuario.getStatus())) {
                    throw new ValidacaoException(BundleManager.getString("msgUsuarioProfissionalInativo"));
                }

                PnlInformarSenhaProfissionalAtendimento.this.onConfirmar(target, usuario, false);
            }
        });

        add(form);
    }

    public void setObject(Usuario usuario) {
        this.usuario = usuario;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Usuario usuario, boolean isValidar) throws DAOException, ValidacaoException;
}
