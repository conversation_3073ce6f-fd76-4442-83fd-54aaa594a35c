package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgInformarCnsPaciente;
import br.com.celk.view.atendimento.prontuario.panel.exame.testerapido.dialog.DlgTipoExameTesteRapido;
import br.com.celk.view.atendimento.prontuario.panel.exame.testerapido.view.CadastroTesteRapidoGravidesPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.testerapido.view.CadastroTesteRapidoHanseniasePanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.view.CadastroTesteRapidoIstPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.view.CadastroTesteRapidoPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.view.DetalhesTesteRapidoPanel;
import br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog.DlgCancelarTesteRapido;
import br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog.DlgDetalhesTesteRapido;
import br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog.DlgInformarResultadoAidsAvancado;
import br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog.DlgInformarResultadoTesteRapido;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.exame.ExameHelper;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoTesteRapidoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.exame.ResultadoAidsAvancado;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.extensions.markup.html.repeater.data.table.AbstractColumn;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.markup.html.basic.Label;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class TesteRapidoPanel extends ProntuarioCadastroPanel {

    private Table tblHistorico;
    private Table tblTesteRapidoPendentes;
    private Integer periodo;
    private WebMarkupContainer containerExamesPendentes;
    private DlgInformarResultadoTesteRapido dlgInformarResultadoTesteRapido;
    private DlgCancelarTesteRapido dlgCancelarTesteRapido;
    private DlgDetalhesTesteRapido dlgDetalhesTesteRapido;
    private DlgTipoExameTesteRapido dlgTipoTesteRapido;
    private DlgImpressaoObject<List<TesteRapidoRealizado>> dlgConfirmacaoImpressao;
    private DlgInformarResultadoAidsAvancado dlgInformarResultadoAidsAvancado;

    private DlgInformarCnsPaciente dlgInformarCnsPaciente;

    public TesteRapidoPanel(String id) {
        super(id, bundle("testeRapido"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");

        form.add(new AbstractAjaxButton("btnNovoExame") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                selecionarTipoExame(target);
            }
        });

        containerExamesPendentes = new WebMarkupContainer("containerExamesPendentes", new CompoundPropertyModel(this));
        containerExamesPendentes.add(tblTesteRapidoPendentes = new Table("tblTesteRapidoPendentes", getColumnsExamesPendentes(), getCollectionProviderExamesPendentes()));
        tblTesteRapidoPendentes.populate();

        form.add(getDropDownFiltroHistorico());
        form.add(tblHistorico = new Table("tblHistorico", getColumnsHistorico(), getCollectionProviderHistorico()));
        tblHistorico.populate();
        periodo = 3;

        form.add(containerExamesPendentes);
        add(form);
    }

    private DropDown getDropDownFiltroHistorico() {
        DropDown dropDown = new DropDown("filtroHistorico", new PropertyModel(this, "periodo"));
        dropDown.addChoice(3, BundleManager.getString("ultimos3meses", this));
        dropDown.addChoice(6, BundleManager.getString("ultimos6meses", this));
        dropDown.addChoice(12, BundleManager.getString("ultimoAno", this));
        dropDown.addChoice(999, BundleManager.getString("todos", this));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tblHistorico.update(target);
            }
        });
        return dropDown;
    }

    private List<IColumn> getColumnsExamesPendentes() {
        List<IColumn> columns = new ArrayList<IColumn>();
        TesteRapidoRealizado proxy = on(TesteRapidoRealizado.class);

        columns.add(getActionColumnExamesPendentes());
        columns.add(createColumn(bundle("data"), proxy.getTesteRapido().getAtendimento().getDataAtendimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getTipoTesteRapido().getDescricaoTipoTeste()));
        columns.add(createColumn(bundle("utilizaFormIst"), proxy.getDescricaoUtilizaFormularioIst()));
        columns.add(new AbstractColumn<TesteRapidoRealizado, String>(Model.of(bundle("resultado"))) {
            @Override
            public void populateItem(Item<ICellPopulator<TesteRapidoRealizado>> cellItem, String componentId, IModel<TesteRapidoRealizado> rowModel) {
                TesteRapidoRealizado item = rowModel.getObject();
                String resultado;
                if (TipoTesteRapido.TipoTeste.COVID_MAIS_INFLUENZA_AB.descricao().equals(item.getTipoTesteRapido().getDescricaoTipoTeste())) {
                    resultado = item.getDescricaoResultadoExameCovidInfluenza();
                } else {
                    resultado = item.getDescricaoResultadoExame();
                }
                cellItem.add(new Label(componentId, resultado));
            }
        });

        return columns;
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();
        TesteRapidoRealizado proxy = on(TesteRapidoRealizado.class);

        columns.add(getActionColumnHistorico());
        columns.add(createColumn(bundle("atendimento"), proxy.getTesteRapido().getAtendimento().getCodigo()));
        columns.add(createColumn(bundle("data"), proxy.getTesteRapido().getAtendimento().getDataAtendimento()));
        columns.add(createColumn(bundle("estabelecimento"), proxy.getTesteRapido().getAtendimento().getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("profissional"), proxy.getTesteRapido().getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getTipoTesteRapido().getDescricaoTipoTeste()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(new AbstractColumn<TesteRapidoRealizado, String>(Model.of(bundle("resultado"))) {
            @Override
            public void populateItem(Item<ICellPopulator<TesteRapidoRealizado>> cellItem, String componentId, IModel<TesteRapidoRealizado> rowModel) {
                TesteRapidoRealizado item = rowModel.getObject();
                String resultado;
                if (TipoTesteRapido.TipoTeste.COVID_MAIS_INFLUENZA_AB.descricao().equals(item.getTipoTesteRapido().getDescricaoTipoTeste())) {
                    resultado = item.getDescricaoResultadoExameCovidInfluenza();
                } else {
                    resultado = item.getDescricaoResultadoExame();
                }
                cellItem.add(new Label(componentId, resultado));
            }
        });
        return columns;
    }

    private IColumn getActionColumnHistorico() {
        return new MultipleActionCustomColumn<TesteRapidoRealizado>() {
            @Override
            public void customizeColumn(TesteRapidoRealizado rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                        TesteRapidoRealizado testeRapidoRealizadoAux = LoadManager.getInstance(TesteRapidoRealizado.class)
                                .addProperties(new HQLProperties(TesteRapidoIst.class, VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_TESTE_RAPIDO_IST)).getProperties())
                                .addProperties(new HQLProperties(ResultadoAidsAvancado.class, TesteRapidoRealizado.PROP_RESULTADO_AIDS_AVANCADO).getProperties())
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_STATUS))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_DESCRICAO_RESULTADO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_DESCRICAO_CANCELAMENTO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_LOTE))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_DATA_VALIDADE))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_DATA_CANCELAMENTO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_TIPO_TESTE))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_CANCELAMENTO, Atendimento.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_NOME))
                                .setId(testeRapidoRealizado.getCodigo())
                                .start().getVO();

                        if (TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                            dlgInformarResultadoAidsAvancado = null;
                            mostrarDlgInformarResultadoAidsAvancado(target, testeRapidoRealizadoAux, true);
                        } else {
                            if (dlgDetalhesTesteRapido == null) {
                                getProntuarioController().addWindow(target, dlgDetalhesTesteRapido = new DlgDetalhesTesteRapido(getProntuarioController().newWindowId()) {
                                });
                            }
                            dlgDetalhesTesteRapido.show(target, testeRapidoRealizadoAux);
                        }
                    }
                });
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                        if (TipoTesteRapido.TipoTeste.GRAVIDEZ.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                            TesteRapidoRealizado testeRapidoRealizadoAux = LoadManager.getInstance(TesteRapidoRealizado.class)
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_DUM))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_TEMPO_AMENORREIA))
                                    .setId(testeRapidoRealizado.getCodigo())
                                    .start().getVO();
                            getProntuarioController().changePanel(target, new CadastroTesteRapidoGravidesPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, false));
                        } else {
                            TesteRapidoRealizado testeRapidoRealizadoAux = LoadManager.getInstance(TesteRapidoRealizado.class)
                                    .addProperties(new HQLProperties(TesteRapido.class, VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO)).getProperties())
                                    .addProperties(new HQLProperties(TesteRapidoIst.class, VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_TESTE_RAPIDO_IST)).getProperties())
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_STATUS))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_CANCELAMENTO, Atendimento.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_DESCRICAO))
                                    .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_DESCRICAO))
                                    .setId(testeRapidoRealizado.getCodigo())
                                    .start().getVO();

                            if (TipoTesteRapido.TipoTeste.COVID_19.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_COVID_19, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            } else if (TipoTesteRapido.TipoTeste.DENGUE.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_DENGUE, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            } else if (TipoTesteRapido.TipoTeste.INFLUENZA.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_INFLUENZA, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            } else if (TipoTesteRapido.TipoTeste.HANSENIASE.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_HANSENIASE, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            } else if (TipoTesteRapido.TipoTeste.HIV_SIFILIS.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_HIV_SIFILIS, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            }  else if (TipoTesteRapido.TipoTeste.TB_LAM.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_TUBERCULOSE, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            }  else if (TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_AIDS_AVANCADO, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            } else if (testeRapidoRealizadoAux.getTesteRapido().getTesteRapidoIst() != null) {
                                CadastroTesteRapidoIstPanel cadastroTesteRapidoIstPanel = new CadastroTesteRapidoIstPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux,true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoIstPanel);
                            } else if (TipoTesteRapido.TipoTeste.COVID_MAIS_INFLUENZA_AB.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
                                CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux, TesteRapido.TIPO_TESTE_COVID_MAIS_INFLUENZA_AB, true);
                                getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                            }else {
                                DetalhesTesteRapidoPanel detalhesTesteRapidoPanel = new DetalhesTesteRapidoPanel(getProntuarioController().panelId(), testeRapidoRealizadoAux);
                                getProntuarioController().changePanel(target, detalhesTesteRapidoPanel);
                            }
                        }

                    }
                }).setIcon(Icon.DOC_LINES).setTitleBundleKey("detalhes");

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        //TODO: Testar aqui
                        TesteRapidoRealizado testeRapidoRealizadoAux = LoadManager.getInstance(TesteRapidoRealizado.class)
                                .addProperties(VOUtils.mergeProperties(new HQLProperties(TesteRapidoRealizado.class).getProperties()))
                                .setId(modelObject.getCodigo())
                                .start().getVO();
                        desfazerResultado(target, testeRapidoRealizadoAux);
                    }
                }).setTitleBundleKey("desfazerResultado")
                        .setIcon(Icon.UNDO)
                        .setQuestionDialogBundleKey("msgDesejaRealmenteDesfazerResultadoDesteExame")
                        .setVisible(TesteRapidoRealizado.Status.CONCLUIDO.value().equals(rowObject.getStatus())
                                && rowObject.getAtendimentoResultado() != null && getAtendimento().getCodigo().equals(rowObject.getAtendimentoResultado().getCodigo()));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<TesteRapidoRealizado>() {
                    @Override
                    public DataReport action(TesteRapidoRealizado modelObject) throws ReportException {
                        ImpressaoTesteRapidoDTOParam param = new ImpressaoTesteRapidoDTOParam();

                        if (TipoTesteRapido.TR_UM.equals(modelObject.getNrTesteRapido())){
                            List<Long> codigo = new ArrayList<>();
                            codigo.add(modelObject.getCodigo());
                            codigo.add(getCodigoTrDois(modelObject.getAtendimentoResultado().getCodigo()));
                            param.setCodigoTesteRapidoRealizados(codigo);
                        }else {
                            param.setCodigoTesteRapidoRealizados(Arrays.asList(modelObject.getCodigo()));
                        }

                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoTesteRapido(param);
                    }
                }).setTitleBundleKey("imprimir").setEnabled(podeImprimir(rowObject));
                if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_UM.equals(rowObject.getNrTesteRapido()))) {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_UM).setTitleBundleKey("rotulo_teste_rapido_1_tr1");
                } else if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_DOIS.equals(rowObject.getNrTesteRapido()))) {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_DOIS).setTitleBundleKey("rotulo_teste_rapido_2_tr2");
                } else if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_TRES.equals(rowObject.getNrTesteRapido()))) {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_TRES).setTitleBundleKey("rotulo_teste_rapido_3_tr3");
                } else if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_QUATRO.equals(rowObject.getNrTesteRapido()))) {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_QUATRO).setTitleBundleKey("rotulo_teste_rapido_4_tr4");
                }

            }
        };
    }

    private boolean podeImprimir(TesteRapidoRealizado rowObject){
        return  !TipoTesteRapido.TipoTeste.GRAVIDEZ.value().equals(rowObject.getTipoTesteRapido().getTipoTeste())
                    && !TesteRapidoRealizado.Status.CANCELADO.value().equals(rowObject.getStatus())
                    && !naoPodeImprimir(rowObject);
    }
    private boolean naoPodeImprimir (TesteRapidoRealizado rowObject){
         return  TipoTesteRapido.TipoTeste.HIV.value().equals(rowObject.getTipoTesteRapido().getTipoTeste())
                       && (TesteRapidoRealizado.NumeroTesteRapido.TR_DOIS.value().equals(rowObject.getNrTesteRapido()));
    }

    private boolean isTesteRapidoHivConcluido(TesteRapidoRealizado rowObject){
        return TipoTesteRapido.TipoTeste.HIV.value().equals(rowObject.getTipoTesteRapido().getTipoTeste())
            && TesteRapidoRealizado.Status.CONCLUIDO.value().equals(rowObject.getStatus());
    }

    private IColumn getActionColumnExamesPendentes() {
        return new MultipleActionCustomColumn<TesteRapidoRealizado>() {
            @Override
            public void customizeColumn(TesteRapidoRealizado rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        if (TipoTesteRapido.TipoTeste.GRAVIDEZ.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())) {
                            getProntuarioController().changePanel(target, new CadastroTesteRapidoGravidesPanel(getProntuarioController().panelId(), modelObject));
                        } else if (modelObject.getTesteRapido().getTesteRapidoIst() != null) {
                            getProntuarioController().changePanel(target, new CadastroTesteRapidoIstPanel(getProntuarioController().panelId(), modelObject));
                        } else if (TipoTesteRapido.TipoTeste.COVID_19.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_COVID_19);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.DENGUE.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_DENGUE);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.INFLUENZA.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_INFLUENZA);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.HANSENIASE.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_HANSENIASE);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.HIV_SIFILIS.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_HIV_SIFILIS);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        }  else if (TipoTesteRapido.TipoTeste.TB_LAM.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())) {
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_TUBERCULOSE);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())) {
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_AIDS_AVANCADO);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        } else if (TipoTesteRapido.TipoTeste.COVID_MAIS_INFLUENZA_AB.value().equals(modelObject.getTipoTesteRapido().getTipoTeste())){
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_COVID_MAIS_INFLUENZA_AB);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        }else {
                            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), modelObject, TesteRapido.TIPO_TESTE_OUTROS);
                            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
                        }

                    }
                });

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        cancelarExame(target, modelObject);
                    }
                }).setTitleBundleKey("cancelarExame").setQuestionDialogBundleKey(null);

                addAction(ActionType.CLONAR, rowObject, new IModelAction<TesteRapidoRealizado>() {
                    @Override
                    public void action(AjaxRequestTarget target, TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        informarResultado(target, modelObject);
                    }
                }).setTitleBundleKey("informarResultado").setIcon(Icon.LABORATORY).setEnabled(rowObject.getTesteRapido().getTesteRapidoIst() == null);

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<TesteRapidoRealizado>() {
                    @Override
                    public DataReport action(TesteRapidoRealizado modelObject) throws ReportException {
                        ImpressaoTesteRapidoDTOParam param = new ImpressaoTesteRapidoDTOParam();
                        if (TipoTesteRapido.TR_UM.equals(modelObject.getNrTesteRapido())){
                           List<Long> codigo = new ArrayList<>();
                           codigo.add(modelObject.getCodigo());
                           if (modelObject.getAtendimentoResultado() != null) {
                               codigo.add(getCodigoTrDois(modelObject.getAtendimentoResultado().getCodigo()));
                           }
                           param.setCodigoTesteRapidoRealizados(codigo);
                        }else {
                            param.setCodigoTesteRapidoRealizados(Arrays.asList(modelObject.getCodigo()));
                        }

                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoTesteRapido(param);
                    }
                }).setTitleBundleKey("imprimir").setEnabled(podeImprimir(rowObject));
                if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_UM.equals(rowObject.getNrTesteRapido())))
                {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_UM).setTitleBundleKey("rotulo_teste_rapido_1_tr1");
                }else if (isTesteRapidoHivConcluido(rowObject) && (TipoTesteRapido.TR_DOIS.equals(rowObject.getNrTesteRapido())))
                {
                    addAction(ActionType.WARN, rowObject, new IModelAction<TesteRapidoRealizado>() {
                        @Override
                        public void action(AjaxRequestTarget target, final TesteRapidoRealizado modelObject) throws ValidacaoException, DAOException {
                        }
                    }).setIcon(Icon.TR_DOIS).setTitleBundleKey("rotulo_teste_rapido_2_tr2");
                }
            }
        };
    }


    private Long getCodigoTrDois(Long nrAtendimento){

        TesteRapidoRealizado testeRapidoRealizado = LoadManager.getInstance(TesteRapidoRealizado.class)
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO), nrAtendimento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_TIPO_TESTE), TipoTesteRapido.TipoTeste.HIV.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_NR_TESTE_RAPIDO, TesteRapidoRealizado.NumeroTesteRapido.TR_DOIS.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, TesteRapidoRealizado.Status.CANCELADO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_RESULTADO, BuilderQueryCustom.QueryParameter.IN, Arrays.asList(TesteRapidoRealizado.Resultado.NAO_REAGENTE.value(), TesteRapidoRealizado.Resultado.REAGENTE.value() )))
                .start().getVO();

        if (testeRapidoRealizado != null) {
            return testeRapidoRealizado.getCodigo();
        }else {
            return null;
        }
    }


    private void informarResultado(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) {
        if (TipoTesteRapido.TipoTeste.AIDS_AVANCADO.value().equals(testeRapidoRealizado.getTipoTesteRapido().getTipoTeste())) {
            dlgInformarResultadoAidsAvancado = null;
            mostrarDlgInformarResultadoAidsAvancado(target, testeRapidoRealizado, false);
        } else if (dlgInformarResultadoTesteRapido == null) {
            getProntuarioController().addWindow(target, dlgInformarResultadoTesteRapido = new DlgInformarResultadoTesteRapido(getProntuarioController().newWindowId(), getAtendimento().getCodigo() ) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                    testeRapidoRealizado.setStatus(TesteRapidoRealizado.Status.CONCLUIDO.value());
                    testeRapidoRealizado.setAtendimentoResultado(getAtendimento());

                    salvarTesteRapido(target, testeRapidoRealizado);

                    TipoTesteRapido tipoTesteRapido = getTipoTesteRapido(testeRapidoRealizado.getTipoTesteRapido());
                    if (!TipoTesteRapido.TipoTeste.GRAVIDEZ.value().equals(tipoTesteRapido.getTipoTeste())) {
                        mostrarDlgImpressãoTesteRapido(target, testeRapidoRealizado);
                    }
                }

            });
            dlgInformarResultadoTesteRapido.show(target, testeRapidoRealizado);
        } else {
            dlgInformarResultadoTesteRapido.show(target, testeRapidoRealizado);
        }
    }

    private void mostrarDlgInformarResultadoAidsAvancado(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado, boolean somenteLeitura) {
        if (dlgInformarResultadoAidsAvancado == null) {
            getProntuarioController().addWindow(target, dlgInformarResultadoAidsAvancado = new DlgInformarResultadoAidsAvancado(getProntuarioController().newWindowId(), getAtendimento().getCodigo(), somenteLeitura) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                    testeRapidoRealizado.setStatus(TesteRapidoRealizado.Status.CONCLUIDO.value());
                    testeRapidoRealizado.setAtendimentoResultado(getAtendimento());

                    ResultadoAidsAvancado resultadoAidsAvancadoSalvo = BOFactoryWicket.save(testeRapidoRealizado.getResultadoAidsAvancado());
                    testeRapidoRealizado.setResultadoAidsAvancado(resultadoAidsAvancadoSalvo);
                    salvarTesteRapido(target, testeRapidoRealizado);

                    mostrarDlgImpressãoTesteRapido(target, testeRapidoRealizado);
                }

            });
        }
        dlgInformarResultadoAidsAvancado.show(target, testeRapidoRealizado);
    }

    private TipoTesteRapido getTipoTesteRapido(TipoTesteRapido tipoTesteRapido) {
        TipoTesteRapido tipoTesteRapidoProxy = on(TipoTesteRapido.class);

        return LoadManager.getInstance(TipoTesteRapido.class)
                .addProperty(path(tipoTesteRapidoProxy.getCodigo()))
                .addProperty(path(tipoTesteRapidoProxy.getVersion()))
                .addProperty(path(tipoTesteRapidoProxy.getDescricao()))
                .addProperty(path(tipoTesteRapidoProxy.getTipoTeste()))
                .setId(tipoTesteRapido.getCodigo())
                .start()
                .getVO();
    }

    private void cancelarExame(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
        if (dlgCancelarTesteRapido == null) {
            getProntuarioController().addWindow(target, dlgCancelarTesteRapido = new DlgCancelarTesteRapido(getProntuarioController().newWindowId()) {

                @Override
                public void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                    testeRapidoRealizado.setStatus(TesteRapidoRealizado.Status.CANCELADO.value());
                    testeRapidoRealizado.setAtendimentoCancelamento(getAtendimento());
                    testeRapidoRealizado.setDataCancelamento(DataUtil.getDataAtual());
                    testeRapidoRealizado.setUsuarioCancelamento(ApplicationSession.get().getSessaoAplicacao().getUsuario());

                    salvarTesteRapido(target, testeRapidoRealizado);
                }

            });
        }
        dlgCancelarTesteRapido.show(target, testeRapidoRealizado);
    }

    private void salvarTesteRapido(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws DAOException, ValidacaoException {
        TesteRapidoRealizado proxy = on(TesteRapidoRealizado.class);

        List<TesteRapidoRealizado> trrList = LoadManager.getInstance(TesteRapidoRealizado.class)
                .addProperties(new HQLProperties(TesteRapidoRealizado.class).getProperties())
                .addProperties(new HQLProperties(TipoTesteRapido.class, path(proxy.getTipoTesteRapido())).getProperties())
                .addProperties(new HQLProperties(TesteRapido.class, path(proxy.getTesteRapido())).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, path(proxy.getTesteRapido().getAtendimento())).getProperties())
                .addProperties(new HQLProperties(Empresa.class, path(proxy.getTesteRapido().getAtendimento().getEmpresa())).getProperties())
                .addProperties(new HQLProperties(Profissional.class, path(proxy.getTesteRapido().getAtendimento().getProfissional())).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, path(proxy.getAtendimentoResultado())).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, path(proxy.getAtendimentoCancelamento())).getProperties())
                .addProperties(new HQLProperties(TabelaCbo.class, path(proxy.getTabelaCboBPA())).getProperties())
                .addProperties(new HQLProperties(ResultadoAidsAvancado.class, path(proxy.getResultadoAidsAvancado())).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_TESTE_RAPIDO, testeRapidoRealizado.getTesteRapido()))
                .start().getList();

        for (TesteRapidoRealizado trr : trrList) {
            if (trr.getCodigo().equals(testeRapidoRealizado.getCodigo())) {
                trr.setStatus(testeRapidoRealizado.getStatus());
                trr.setAtendimentoResultado(testeRapidoRealizado.getAtendimentoResultado());
                trr.setDescricaoResultado(testeRapidoRealizado.getDescricaoResultado());
                trr.setResultado(testeRapidoRealizado.getResultado());
                trr.setDescricaoCancelamento(testeRapidoRealizado.getDescricaoCancelamento());
                trr.setAtendimentoCancelamento(testeRapidoRealizado.getAtendimentoCancelamento());
                trr.setLote(testeRapidoRealizado.getLote());
                trr.setDataValidade(testeRapidoRealizado.getDataValidade());
                trr.setUsuarioCancelamento(testeRapidoRealizado.getUsuarioCancelamento());
                trr.setDataCancelamento(testeRapidoRealizado.getDataCancelamento());
                trr.setNrTesteRapido(testeRapidoRealizado.getNrTesteRapido());
                trr.setResultadoHemoglobinaGl(testeRapidoRealizado.getResultadoHemoglobinaGl());
                trr.setResultadoHematocrito(testeRapidoRealizado.getResultadoHematocrito());
                trr.setResultadoAidsAvancado(testeRapidoRealizado.getResultadoAidsAvancado());
                trr.setResultadoInfluenzaA(testeRapidoRealizado.getResultadoInfluenzaA());
                trr.setResultadoInfluenzaB(testeRapidoRealizado.getResultadoInfluenzaB());
                trr.setLoteInfluenza(testeRapidoRealizado.getLoteInfluenza());
                trr.setDataValidadeInfluenza(testeRapidoRealizado.getDataValidadeInfluenza());
            }
        }

        BOFactoryWicket.getBO(AtendimentoFacade.class).salvarTesteRapido(null, trrList);
        if (TesteRapidoRealizado.Status.CONCLUIDO.value().equals(testeRapidoRealizado.getStatus())) {
            TipoTesteRapido ttr = LoadManager.getInstance(TipoTesteRapido.class).setId(testeRapidoRealizado.getTipoTesteRapido().getCodigo()).start().getVO();
            if (TipoTesteRapido.TipoTeste.COVID_19.value().equals(ttr.getTipoTeste())) {
                BOFactoryWicket.getBO(VigilanciaFacade.class).informarResultadoExamesInvestigacaoAgravo(testeRapidoRealizado.getAtendimentoResultado().getUsuarioCadsus(), testeRapidoRealizado);
            } else if (TipoTesteRapido.TipoTeste.DENGUE.value().equals(ttr.getTipoTeste())) {
                BOFactoryWicket.getBO(VigilanciaFacade.class).informarResultadoExamesInvestigacaoAgravo(testeRapidoRealizado.getAtendimentoResultado().getUsuarioCadsus(), testeRapidoRealizado);
            } else if (TipoTesteRapido.TipoTeste.INFLUENZA.value().equals(ttr.getTipoTeste())) {
                BOFactoryWicket.getBO(VigilanciaFacade.class).informarResultadoExamesInvestigacaoAgravo(testeRapidoRealizado.getAtendimentoResultado().getUsuarioCadsus(), testeRapidoRealizado);
            } else if (TipoTesteRapido.TipoTeste.HANSENIASE.value().equals(ttr.getTipoTeste())) {
                BOFactoryWicket.getBO(VigilanciaFacade.class).informarResultadoExamesInvestigacaoAgravo(testeRapidoRealizado.getAtendimentoResultado().getUsuarioCadsus(), testeRapidoRealizado);
            }
        }
        removerExamePendente(target);
        atualizarHistorico(target);
    }

    private void removerExamePendente(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        tblTesteRapidoPendentes.update(target);
        target.add(tblTesteRapidoPendentes);
    }

    private void atualizarHistorico(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        tblHistorico.update(target);
        target.add(tblHistorico);
    }

    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                DatePeriod datePeriod;
                if (periodo != 999) {
                    Date dataAtual = DataUtil.getDataAtual();
                    datePeriod = new DatePeriod(Data.removeMeses(dataAtual, periodo), dataAtual);
                } else {
                    datePeriod = null;
                }
                TesteRapidoRealizado proxy = Lambda.on(TesteRapidoRealizado.class);
                return LoadManager.getInstance(TesteRapidoRealizado.class)
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_RESULTADO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_STATUS))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_NR_TESTE_RAPIDO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_TIPO_TESTE))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TESTE_RAPIDO, TesteRapido.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, Usuario.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IN,
                                Arrays.asList(TesteRapidoRealizado.Status.CONCLUIDO.value(), TesteRapidoRealizado.Status.CANCELADO.value())))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTesteRapido().getAtendimento().getDataAtendimento()), datePeriod))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTesteRapido().getAtendimento().getUsuarioCadsus()), getAtendimento().getUsuarioCadsus()))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .start().getList();
            }
        };
    }

    private ICollectionProvider getCollectionProviderExamesPendentes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                TesteRapidoRealizado proxy = Lambda.on(TesteRapidoRealizado.class);
                    return LoadManager.getInstance(TesteRapidoRealizado.class)
                            .addProperties(new HQLProperties(TesteRapidoRealizado.class).getProperties())
                            .addProperties(new HQLProperties(TipoTesteRapido.class, path(proxy.getTipoTesteRapido())).getProperties())
                            .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_TIPO_TESTE))
                            .addProperties(new HQLProperties(TesteRapido.class, path(proxy.getTesteRapido())).getProperties())
                            .addProperties(new HQLProperties(TesteRapidoIst.class, path(proxy.getTesteRapido().getTesteRapidoIst())).getProperties())
                            .addProperties(new HQLProperties(Atendimento.class, path(proxy.getTesteRapido().getAtendimento())).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IGUAL, TesteRapidoRealizado.Status.PENDENTE.value()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTesteRapido().getAtendimento().getUsuarioCadsus()), getAtendimento().getUsuarioCadsus()))
                            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getTesteRapido().getAtendimento().getDataAtendimento()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .start().getList();
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                TesteRapidoRealizado proxy = Lambda.on(TesteRapidoRealizado.class);
                             return LoadManager.getInstance(TesteRapidoRealizado.class)
                            .addProperties(new HQLProperties(TesteRapidoRealizado.class).getProperties())
                            .addProperties(new HQLProperties(TipoTesteRapido.class, path(proxy.getTipoTesteRapido())).getProperties())
                            .addProperties(new HQLProperties(TesteRapido.class, path(proxy.getTesteRapido())).getProperties())
                            .addProperties(new HQLProperties(TesteRapidoIst.class, path(proxy.getTesteRapido().getTesteRapidoIst())).getProperties())
                            .addProperty(path(proxy.getTesteRapido().getAtendimento().getEmpresaBpa().getCodigo()))
                            .addProperty(path(proxy.getTesteRapido().getAtendimento().getProfissional().getCodigo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), TesteRapidoRealizado.Status.PENDENTE.value()))
                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTesteRapido().getAtendimento()), getAtendimento()))
                            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .start().getList();
            }
        };
    }

    //    private void selecionarTipoExame(AjaxRequestTarget target) {
//        getProntuarioController().addWindow(target, dlgTipoExameTesteRapido = new DlgTipoExameTesteRapido(getProntuarioController().newWindowId()) {
//            @Override
//            public void onUpdate(AjaxRequestTarget target, TipoTesteRapido tipoTesteRapido) {
//                try {
//                    validarTipoExameSelecionado(target, tipoTesteRapido);
//                } catch (ValidacaoException e) {
//                    MessageUtil.modalWarn(target, this, e);
//                } catch (DAOException e) {
//                    MessageUtil.modalWarn(target, this, e);
//                }
//            }
//        });
//        dlgTipoExameTesteRapido.show(target);
//    }
    private void validarTipoExameSelecionado(AjaxRequestTarget target, TipoTesteRapido tipoTesteRapido) throws ValidacaoException, DAOException {
        if (tipoTesteRapido != null) {
            ExameHelper.validarTesteRapidoPendente(tipoTesteRapido, getAtendimento().getUsuarioCadsus());

            AtendimentoHelper.validaProcedimentoCboProfissionalAtendimento(tipoTesteRapido.getProcedimento(), getAtendimento().getProfissional(), getAtendimento().getEmpresa());

            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId());
            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
        } else {
            selecionarTipoExame(target);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerExamesPendentes)));
    }

    private void desfazerResultado(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws DAOException, ValidacaoException {
        testeRapidoRealizado.setStatus(TesteRapidoRealizado.Status.PENDENTE.value());
        testeRapidoRealizado.setAtendimentoResultado(null);
        testeRapidoRealizado.setResultado(null);
        testeRapidoRealizado.setLote(null);
        testeRapidoRealizado.setDataValidade(null);
        testeRapidoRealizado.setDescricaoResultado(null);
        testeRapidoRealizado.setDataCancelamento(null);
        testeRapidoRealizado.setUsuarioCancelamento(null);

        salvarTesteRapido(target, testeRapidoRealizado);
    }

    private void informarCnsPaciente(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, final Long tipoExame) {
        if (dlgInformarCnsPaciente == null) {
            getProntuarioController().addWindow(target, dlgInformarCnsPaciente = new DlgInformarCnsPaciente(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, String numeroCartao) throws ValidacaoException, DAOException {
                    UsuarioCadsusCns usuarioCadsusCns = new UsuarioCadsusCns();
                    usuarioCadsusCns.setUsuarioCadsus(usuarioCadsus);
                    usuarioCadsusCns.setExcluido(RepositoryComponentDefault.NAO_LONG);
                    usuarioCadsusCns.setNumeroCartao(new Long(StringUtil.getDigits(numeroCartao)));

                    BOFactoryWicket.getBO(UsuarioCadsusFacade.class).cadastrarUsuarioCadsusCns(Arrays.asList(usuarioCadsusCns), usuarioCadsusCns.getUsuarioCadsus(), false);

                    redirecionaCadastroExameRapido(target, tipoExame);
                }
            });
        }
        dlgInformarCnsPaciente.show(target, usuarioCadsus);
    }

    private void selecionarTipoExame(AjaxRequestTarget target) {
        getProntuarioController().addWindow(target, dlgTipoTesteRapido = new DlgTipoExameTesteRapido(getProntuarioController().newWindowId()) {
            @Override
            public void onUpdate(AjaxRequestTarget target, Long tipoExame) {
                try {
                    validarTipoExameSelecionado(target, tipoExame);
                } catch (ValidacaoException e) {
                    MessageUtil.modalWarn(target, this, e);
                } catch (DAOException e) {
                    MessageUtil.modalWarn(target, this, e);
                }
            }
        });
        dlgTipoTesteRapido.show(target);
    }

    private void validarTipoExameSelecionado(AjaxRequestTarget target, Long tipoExame) throws ValidacaoException, DAOException {
        if (TesteRapido.TIPO_TESTE_GRAVIDEZ.equals(tipoExame) && RepositoryComponentDefault.SEXO_MASCULINO.equals(getAtendimento().getUsuarioCadsus().getSexo())) {
            throw new ValidacaoException(bundle("msgNaoPossivelRealisarTesteRapidoGestantePacienteMasculino"));
        }
        if (!TesteRapido.TIPO_TESTE_COVID_19.equals(tipoExame) && (!validaCnsCPF(getAtendimento().getUsuarioCadsus()))) {
            informarCnsPaciente(target, getAtendimento().getUsuarioCadsus(), tipoExame);
        } else {
            redirecionaCadastroExameRapido(target, tipoExame);
        }
    }
    private boolean validaCnsCPF(UsuarioCadsus usuarioCadsus){
        return  (Util.isNotNull(usuarioCadsus.getCns()) && !"".equals(usuarioCadsus.getCns()))
             || (Util.isNotNull(usuarioCadsus.getCpf()) && !"".equals(usuarioCadsus.getCpf()));
    }

    private void redirecionaCadastroExameRapido(AjaxRequestTarget target, Long tipoExame) throws ValidacaoException {
        if (TesteRapidoIst.TIPO_TESTE_IST.equals(tipoExame)) {
            // REDIRECIONA TELA TESTE RÁPIDO IST
            CadastroTesteRapidoIstPanel cadastroTesteRapidoIstPanel = new CadastroTesteRapidoIstPanel(getProntuarioController().panelId());
            getProntuarioController().changePanel(target, cadastroTesteRapidoIstPanel);
        } else if (TesteRapido.TIPO_TESTE_GRAVIDEZ.equals(tipoExame)) {
            // REDIRECIONA TELA TESTE RÁPIDO GRAVIDEZ
            CadastroTesteRapidoGravidesPanel cadastroTesteRapidoGravidesPanel = new CadastroTesteRapidoGravidesPanel(getProntuarioController().panelId());
            getProntuarioController().changePanel(target, cadastroTesteRapidoGravidesPanel);
        } else if (TesteRapido.TIPO_TESTE_HANSENIASE.equals(tipoExame)) {
            // Redireciona para a tela de Teste Rápido Hanseniase
            CadastroTesteRapidoHanseniasePanel cadastroTesteRapidoHanseniasePanel = new CadastroTesteRapidoHanseniasePanel(getProntuarioController().panelId());
            getProntuarioController().changePanel(target, cadastroTesteRapidoHanseniasePanel);
        } else {
            //REDIRECIONA PARA O TESTE RAPIDO TUBERCULOSE , COVID E OUTROS
            CadastroTesteRapidoPanel cadastroTesteRapidoPanel = new CadastroTesteRapidoPanel(getProntuarioController().panelId(), tipoExame);
            getProntuarioController().changePanel(target, cadastroTesteRapidoPanel);
        }
    }

    private void mostrarDlgImpressãoTesteRapido(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) {
        initDialogImpressao(target);
        List<TesteRapidoRealizado> testeRapidoRealizadoSalvoList = new ArrayList<>();
        testeRapidoRealizadoSalvoList.add(testeRapidoRealizado);
        dlgConfirmacaoImpressao.show(target, testeRapidoRealizadoSalvoList);
    }

    private void initDialogImpressao(final AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<List<TesteRapidoRealizado>>(getProntuarioController().newWindowId(), bundle("resultadoSalvoComSucessoDesejaImprimir")) {
                @Override
                public DataReport getDataReport(List<TesteRapidoRealizado> testeRapidoRealizado) throws ReportException {
                    ImpressaoTesteRapidoDTOParam param = new ImpressaoTesteRapidoDTOParam();
                    param.setCodigoTesteRapidoRealizados(Lambda.extract(testeRapidoRealizado, on(TesteRapidoRealizado.class).getCodigo()));
                    param.setImprimeSomenteResultado(true);

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoTesteRapido(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, List<TesteRapidoRealizado> testeRapidoRealizado) throws ValidacaoException, DAOException {
                    getProntuarioController().changePanel(target, new TesteRapidoPanel(getProntuarioController().panelId()));
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }
}
