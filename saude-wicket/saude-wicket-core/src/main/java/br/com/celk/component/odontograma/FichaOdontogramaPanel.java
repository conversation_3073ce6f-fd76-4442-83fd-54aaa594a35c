package br.com.celk.component.odontograma;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.resources.AdultoSVG;
import br.com.celk.resources.InfantilSVG;
import br.com.celk.resources.Resources;
import br.com.celk.resources.ToothSVG;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.unidadesaude.odontograma.dto.*;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.interfaces.IProntuarioController;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import org.apache.commons.lang.ObjectUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.*;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.Request;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.util.string.StringValue;
import org.hamcrest.Matchers;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;

import static ch.lambdaj.Lambda.on;

/**
 * Created by laudecir
 */
public class FichaOdontogramaPanel extends Panel {

    private String CSS_FILE = "FichaOdontogramaPanel.css";

    private WebMarkupContainer container;

    private WebMarkupContainer containerAdultoSuperior;
    private WebMarkupContainer containerAdultoInferior;

    private WebMarkupContainer containerInfantilSuperior;
    private WebMarkupContainer containerInfantilInferior;

    private ListView<ToothSVG> adultoSuperiorListView;
    private ListView<ToothSVG> adultoInferiorListView;

    private ListView<ToothSVG> infantilSuperiorListView;
    private ListView<ToothSVG> infantilInferiorListView;

    private WebMarkupContainer containerProteses;
    private CheckBoxLongValue cbxProteseSuperior;
    private CheckBoxLongValue cbxProteseInferior;

    private Long proteseSuperior;
    private Long proteseInferior;

    private SituacaoDente situacaoProteseSuperior;
    private SituacaoDente situacaoProteseInferior;

    private UsuarioCadsusDado usuarioCadsusDado;
    private AtendimentoOdontoFicha atendimentoOdontoFicha;

    private IProntuarioController prontuarioController;

    private OdontogramaJsonDTO odontogramaJsonDTO;

    public FichaOdontogramaPanel(String id, IProntuarioController prontuarioController, AtendimentoOdontoFicha atendimentoOdontoFicha) {
        super(id);
        this.prontuarioController = prontuarioController;
        this.atendimentoOdontoFicha = atendimentoOdontoFicha;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form");
        setDefaultModel(new CompoundPropertyModel(this));

        form.add(containerProteses = new WebMarkupContainer("containerProteses"));
        containerProteses.setOutputMarkupId(true);

        containerProteses.add(cbxProteseSuperior = new CheckBoxLongValue("proteseSuperior"));
        cbxProteseSuperior.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean utilizaProtese = RepositoryComponentDefault.SIM_LONG.equals(proteseSuperior);
                try {
                    if (utilizaProtese) {
                        AtendimentoOdontoPlano atendimentoOdontoPlano = gerarAtendimentoOdontoPlanoProtese(situacaoProteseSuperior, true);

                        SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoProteseSuperior);
                        situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                        situacaoJsonDTO.setCodigoAtendimento(prontuarioController.getAtendimento().getCodigo());

                        getOdontogramaJsonDTO().setProteseSuperior(true);
                        getOdontogramaJsonDTO().setSituacaoProteseSuperior(situacaoJsonDTO);
                    } else {
                        SituacaoJsonDTO situacaoJsonDTO = getOdontogramaJsonDTO().getSituacaoProteseSuperior();
                        if (situacaoJsonDTO != null) {
                            excluirRegistroAtendimentoOdontoPlanoProtese(situacaoJsonDTO.getCodigoOdontoPlano());
                        }

                        getOdontogramaJsonDTO().setProteseSuperior(false);
                        getOdontogramaJsonDTO().setSituacaoProteseSuperior(null);
                    }

                    saveJSON(target);
                    updateTable(target);

                    target.appendJavaScript("OdontogramFicha.setEnableArch('upper')");
                } catch (ValidacaoException | DAOException e) {
                    warn(e.getMessage());
                    Loggable.log.trace(e);

                    if (utilizaProtese) {
                        proteseSuperior = RepositoryComponentDefault.NAO_LONG;
                        target.add(cbxProteseSuperior);
                    }
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
        });

        containerProteses.add(cbxProteseInferior = new CheckBoxLongValue("proteseInferior"));
        cbxProteseInferior.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean utilizaProtese = RepositoryComponentDefault.SIM_LONG.equals(proteseInferior);
                try {
                    if (utilizaProtese) {
                        AtendimentoOdontoPlano atendimentoOdontoPlano = gerarAtendimentoOdontoPlanoProtese(situacaoProteseInferior, false);

                        SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoProteseInferior);
                        situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                        situacaoJsonDTO.setCodigoAtendimento(prontuarioController.getAtendimento().getCodigo());

                        getOdontogramaJsonDTO().setProteseInferior(true);
                        getOdontogramaJsonDTO().setSituacaoProteseInferior(situacaoJsonDTO);
                    } else {
                        SituacaoJsonDTO situacaoJsonDTO = getOdontogramaJsonDTO().getSituacaoProteseInferior();
                        if (situacaoJsonDTO != null) {
                            excluirRegistroAtendimentoOdontoPlanoProtese(situacaoJsonDTO.getCodigoOdontoPlano());
                        }

                        getOdontogramaJsonDTO().setProteseInferior(false);
                        getOdontogramaJsonDTO().setSituacaoProteseInferior(null);
                    }

                    saveJSON(target);
                    updateTable(target);

                    target.appendJavaScript("OdontogramFicha.setEnableArch('lower')");
                } catch (ValidacaoException | DAOException e) {
                    warn(e.getMessage());
                    Loggable.log.trace(e);

                    if (utilizaProtese) {
                        proteseInferior = RepositoryComponentDefault.NAO_LONG;
                        target.add(cbxProteseInferior);
                    }
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
        });

        try {
            situacaoProteseSuperior = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("SituacaoProteseSuperior");
            situacaoProteseInferior = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("SituacaoProteseInferior");
        } catch (ValidacaoRuntimeException | DAOException e) {
            containerProteses.setVisible(false);
            Loggable.log.error(e.getMessage(), e);
        }

        form.add(container = new WebMarkupContainer("container"));
        container.setOutputMarkupId(true);

        container.add(containerAdultoSuperior = new WebMarkupContainer("containerAdultoSuperior"));
        containerAdultoSuperior.setOutputMarkupId(true);
        containerAdultoSuperior.add(adultoSuperiorListView = listView("adultoSuperior", AdultoSVG.SUPERIORES));
        adultoSuperiorListView.setOutputMarkupId(true);

        container.add(containerAdultoInferior = new WebMarkupContainer("containerAdultoInferior"));
        containerAdultoInferior.setOutputMarkupId(true);
        containerAdultoInferior.add(adultoInferiorListView = listView("adultoInferior", AdultoSVG.INFERIORES));
        adultoInferiorListView.setOutputMarkupId(true);

        container.add(containerInfantilSuperior = new WebMarkupContainer("containerInfantilSuperior"));
        containerInfantilSuperior.setOutputMarkupId(true);
        containerInfantilSuperior.add(infantilSuperiorListView = listView("infantilSuperior", InfantilSVG.SUPERIORES));
        infantilSuperiorListView.setOutputMarkupId(true);

        container.add(containerInfantilInferior = new WebMarkupContainer("containerInfantilInferior"));
        containerInfantilInferior.setOutputMarkupId(true);
        containerInfantilInferior.add(infantilInferiorListView = listView("infantilInferior", InfantilSVG.INFERIORES));
        infantilInferiorListView.setOutputMarkupId(true);

        container.add(new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                RequestCycle requestCycle = RequestCycle.get();
                Request request = requestCycle.getRequest();
                {
                    StringValue json = request.getPostParameters().getParameterValue("json");
                    saveJSON(target, json.toString(), true);
                }

                target.appendJavaScript("OdontogramFicha.unlock()");
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.setMethod(AjaxRequestAttributes.Method.POST);
            }

            @Override
            public void renderHead(Component component, IHeaderResponse response) {
                super.renderHead(component, response);

                List<EloDenteSituacao> eloDenteSituacaoList = LoadManager.getInstance(EloDenteSituacao.class)
                        .addProperties(new HQLProperties(Dente.class, EloDenteSituacao.PROP_DENTE).getProperties())
                        .addProperties(new HQLProperties(SituacaoDente.class, EloDenteSituacao.PROP_SITUACAO_DENTE).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloDenteSituacao.PROP_DENTE, Dente.PROP_CODIGO), QueryCustom.QueryCustomParameter.IN, Dente.DENTES_PADRAO))
                        .start().getList();

                List<DenteJsonDTO> denteJsonList = new ArrayList();
                List<Dente> dentes = Lambda.extract(eloDenteSituacaoList, Lambda.on(EloDenteSituacao.class).getDente());
                Set<Dente> dentesDistinctValues = new HashSet<>(dentes);
                for (Dente dente : dentesDistinctValues) {
                    String id = dente.getIdentificador();
                    denteJsonList.add(new DenteJsonDTO(id));
                }

                String denteJSON = convertListToJSON(denteJsonList, DenteJsonDTO.class);

                List<SituacaoJsonDTO> situacaoJsonList = new ArrayList();
                List<SituacaoDente> situacaoDentes = Lambda.extract(eloDenteSituacaoList, Lambda.on(EloDenteSituacao.class).getSituacaoDente());
                Set<SituacaoDente> situacaoDistinctValues = new HashSet<>(situacaoDentes);
                for (SituacaoDente situacaoDente : situacaoDistinctValues) {
                    situacaoJsonList.add(new SituacaoJsonDTO(situacaoDente));
                }

                String situacaoJSON = convertListToJSON(situacaoJsonList, SituacaoJsonDTO.class);

                List<DenteSituacaoJsonDTO> denteSituacaoJsonDTOList = new ArrayList();
                Group<EloDenteSituacao> byDente = Lambda.group(eloDenteSituacaoList, Lambda.by(Lambda.on(EloDenteSituacao.class).getDente()));
                for (Group<EloDenteSituacao> group : byDente.subgroups()) {
                    String id = group.first().getDente().getIdentificador();
                    DenteSituacaoJsonDTO denteSituacaoJsonDTO = new DenteSituacaoJsonDTO();
                    denteSituacaoJsonDTO.setId(id);

                    List<EloDenteSituacao> all = group.findAll();
                    List codigosSituacao = Lambda.extract(all, Lambda.on(EloDenteSituacao.class).getSituacaoDente().getIdentificador());
                    denteSituacaoJsonDTO.setSituacaoList(codigosSituacao);
                    denteSituacaoJsonDTOList.add(denteSituacaoJsonDTO);
                }

                String denteSituacaoJSON = convertListToJSON(denteSituacaoJsonDTOList, DenteSituacaoJsonDTO.class);

                StringBuilder script = new StringBuilder();
                script.append("OdontogramFicha.init(");
                script.append("'").append(getCallbackUrl()).append("'");
                if (denteJSON != null) {
                    script.append(", '").append(denteJSON).append("'");
                }
                if (situacaoJSON != null) {
                    script.append(", '").append(situacaoJSON).append("'");
                }
                if (denteSituacaoJSON != null) {
                    script.append(", '").append(denteSituacaoJSON).append("'");
                }

                script.append(", ").append(prontuarioController.getAtendimento().getCodigo());

                String odontogramaJson = getUsuarioCadsusDado().getOdontogramaJson();
                if (odontogramaJson != null) {
                    script.append(", '").append(odontogramaJson).append("'");
                }

                script.append(")");
                response.render(OnEventHeaderItem.forScript("'" + component.getMarkupId() + "'", "domready", script.toString()));
            }
        });

        add(form);
    }

    private String convertObjectToJSON(Object object) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(object);
        } catch (IOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return null;
    }

    public Object convertJsonToObject(String json, Class clazz) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return null;
    }

    private String convertListToJSON(List list, Class typeList) {
        String json = null;
        if (CollectionUtils.isNotNullEmpty(list)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, typeList);
                json = objectMapper.writerWithType(collectionType).writeValueAsString(list);
            } catch (JsonProcessingException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return json;
    }

    private ListView<ToothSVG> listView(String id, List<? extends ToothSVG> svgs) {
        ListView listView = new ListView<ToothSVG>(id, new CompoundPropertyModel(svgs)) {
            @Override
            protected void populateItem(ListItem<ToothSVG> item) {
                item.setOutputMarkupId(true);

                ToothSVG svg = item.getModel().getObject();

                item.add(new AttributeModifier("tooth-id", (Serializable) svg.id()));

                item.add(new AbstractDefaultAjaxBehavior() {
                    @Override
                    protected void respond(AjaxRequestTarget target) {
                        RequestCycle requestCycle = RequestCycle.get();
                        Request request = requestCycle.getRequest();
                        try {
                            StringValue json = request.getPostParameters().getParameterValue("json");

                            OdontogramaTratamentoJsonDTO dto = (OdontogramaTratamentoJsonDTO) convertJsonToObject(json.toString(), OdontogramaTratamentoJsonDTO.class);

                            StringValue action = request.getPostParameters().getParameterValue("action");
                            if ("aplicar".equals(action.toString())) {
                                StringValue face = request.getPostParameters().getParameterValue("face");
                                adicionarTratamento(target, dto, face.toString());
                            } else if ("reverter".equals(action.toString())) {
                                reverterSituacaoDente(target, dto);
                            } else if ("restaurar".equals(action.toString())) {
                                StringValue face = request.getPostParameters().getParameterValue("face");
                                definirProximoTratamentoDente(target, dto, face.toString());
                            } else if ("atualizarFaces".equals(action.toString())) {
                                atualizarFacesOdontoPlano(target, dto);
                            }
                        } catch (DAOException e) {
                            MessageUtil.modalError(target, FichaOdontogramaPanel.this, e);
                             br.com.ksisolucoes.util.log.Loggable.log.error(e);
                        } catch (ValidacaoException e) {
                            MessageUtil.modalWarn(target, FichaOdontogramaPanel.this, e);
                             br.com.ksisolucoes.util.log.Loggable.log.error(e);
                        }

                        target.appendJavaScript("OdontogramFicha.unlock()");
                    }

                    @Override
                    protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                        super.updateAjaxAttributes(attributes);
                        attributes.setMethod(AjaxRequestAttributes.Method.POST);
                    }

                    @Override
                    public void renderHead(Component component, IHeaderResponse response) {
                        super.renderHead(component, response);
                        response.render(OnLoadHeaderItem.forScript("OdontogramFicha.registerWicketComponent('" + component.getMarkupId() + "', '" + getCallbackUrl().toString() + "')"));
                    }
                });

                item.add(new Label("numeroDente", new Model((Serializable) svg.id())));

                ResourceReference resourceImage = svg.resourceReference();
                Image tooth = new Image("imagem", resourceImage);
                item.add(tooth);

                Image faces = new Image("faces", svg.resourceReferenceFace());
                item.add(faces);
            }
        };

        return listView;
    }

    public void excluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException {
        Long codigoOdontoPlano = atendimentoOdontoPlano.getCodigo();
        Long tipoEvolucao = atendimentoOdontoPlano.getTipoEvolucao();

//        if (excluir) {
            BOFactoryWicket.delete(atendimentoOdontoPlano);
//        } else {
//            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.BAIXADO.value());
//            atendimentoOdontoPlano.setDataCancelamento(DataUtil.getDataAtual());
//            atendimentoOdontoPlano.setMotivoCancelamento(motivo);
//            atendimentoOdontoPlano.setAtendimentoCancelamento(prontuarioController.getAtendimento());
//            BOFactoryWicket.save(atendimentoOdontoPlano);
//        }

        if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)) {
            boolean refreshOdontograma = false;
            if (getOdontogramaJsonDTO().getSituacaoProteseSuperior() != null && codigoOdontoPlano.equals(getOdontogramaJsonDTO().getSituacaoProteseSuperior().getCodigoOdontoPlano())) {
                refreshOdontograma = true;
                getOdontogramaJsonDTO().setProteseSuperior(false);
                getOdontogramaJsonDTO().setSituacaoProteseSuperior(null);
            } else if (getOdontogramaJsonDTO().getSituacaoProteseInferior() != null && codigoOdontoPlano.equals(getOdontogramaJsonDTO().getSituacaoProteseInferior().getCodigoOdontoPlano())) {
                refreshOdontograma = true;
                getOdontogramaJsonDTO().setProteseInferior(false);
                getOdontogramaJsonDTO().setSituacaoProteseInferior(null);
            }

            if (refreshOdontograma) {
                saveJSON(target);
                refresh(target.getHeaderResponse());
            }
        } else if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao)) {
            if (br.com.celk.util.CollectionUtils.isNotNullEmpty(getOdontogramaJsonDTO().getTratamentos())) {
                OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO = Lambda.selectFirst(getOdontogramaJsonDTO().getTratamentos(), Lambda.having(on(OdontogramaTratamentoJsonDTO.class).getId(), Matchers.equalTo(String.valueOf(atendimentoOdontoPlano.getDente().getCodigo()))));
                if (odontogramaTratamentoJsonDTO != null) {
                    if (odontogramaTratamentoJsonDTO.getStatus() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getStatus().getCodigoOdontoPlano())) {
                        definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, null);
                    } else if (odontogramaTratamentoJsonDTO.getCoroa() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getCoroa().getCodigoOdontoPlano())) {
                        definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "coroa");
                    } else if (odontogramaTratamentoJsonDTO.getColo() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getColo().getCodigoOdontoPlano())) {
                        definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "colo");
                    } else if (odontogramaTratamentoJsonDTO.getRaiz() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getRaiz().getCodigoOdontoPlano())) {
                        definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "raiz");
                    }
                }
            }
        }

        updateTable(target);
    }

    private void reverterSituacaoDente(AjaxRequestTarget target, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO) throws ValidacaoException, DAOException {
        if (odontogramaTratamentoJsonDTO != null) {
            Long codigoOdontoPlano = odontogramaTratamentoJsonDTO.getStatus().getCodigoOdontoPlano();

            if (codigoOdontoPlano != null) {
                AtendimentoOdontoPlano atendimentoOdontoPlano = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                        .setId(codigoOdontoPlano)
                        .setMaxResults(1)
                        .start().getVO();
                if (atendimentoOdontoPlano != null) {
                    boolean hasExecucao = LoadManager.getInstance(AtendimentoOdontoExecucao.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoExecucao.PROP_ATENDIMENTO_ODONTO_PLANO, atendimentoOdontoPlano))
                            .exists();

                    if (hasExecucao) {
                        atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.CANCELADO.value());
                        atendimentoOdontoPlano.setObservacao("Cancelado automaticamente ao reverter a situação aplicada");
                        BOFactoryWicket.save(atendimentoOdontoPlano);
                    } else {
                        BOFactoryWicket.delete(atendimentoOdontoPlano);
                    }

                    updateTable(target);
                }
            }

            definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, null);
        }
    }

    public void adicionarTratamentoDente(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException {
        OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO = null;
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(getOdontogramaJsonDTO().getTratamentos())) {
            odontogramaTratamentoJsonDTO = Lambda.selectFirst(getOdontogramaJsonDTO().getTratamentos(), Lambda.having(on(OdontogramaTratamentoJsonDTO.class).getId(), Matchers.equalTo(String.valueOf(atendimentoOdontoPlano.getDente().getCodigo()))));
        }

        if (odontogramaTratamentoJsonDTO == null) {
            odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO();
            odontogramaTratamentoJsonDTO.setId(String.valueOf(atendimentoOdontoPlano.getDente().getCodigo()));
        }

        SituacaoDente situacaoDente = atendimentoOdontoPlano.getSituacaoDente();
        SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
        situacaoJsonDTO.setCadastrado(true);
        situacaoJsonDTO.setCodigoAtendimento(atendimentoOdontoPlano.getAtendimento().getCodigo());

        if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
            situacaoJsonDTO.setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
        } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
            situacaoJsonDTO.setTratamentoUrgente(true);
        }

        boolean historico = SituacaoDente.TipoSituacao.HISTORICO.value().equals(situacaoDente.getTipoSituacao());
        if (historico) {
            situacaoJsonDTO.setConcluido(true);
        }

        if (situacaoJsonDTO.isDesabilitaAoAplicar()) {
            odontogramaTratamentoJsonDTO.setStatus(situacaoJsonDTO);
            odontogramaTratamentoJsonDTO.setInativo(true);
        } else {
            boolean distal = atendimentoOdontoPlano.getFaceDistal() != null;
            boolean mesial = atendimentoOdontoPlano.getFaceMesial() != null;
            boolean oclusal = atendimentoOdontoPlano.getFaceOclusal() != null;
            boolean palatal = atendimentoOdontoPlano.getFacePalatal() != null;
            boolean vestibular = atendimentoOdontoPlano.getFaceVestibular() != null;
            if (distal || mesial || oclusal || palatal || vestibular) {
                FacesOdontogramaJsonDTO facesJsonDTO = new FacesOdontogramaJsonDTO();

                if (distal) {
                    if (situacaoJsonDTO.isConcluido() || RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFaceDistal())) {
                        facesJsonDTO.setDistal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
                        atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        facesJsonDTO.setDistal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
                        atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.NAO_LONG);
                    }
                } else {
                    facesJsonDTO.setDistal(null);
                }

                if (mesial) {
                    if (situacaoJsonDTO.isConcluido() || RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFaceMesial())) {
                        facesJsonDTO.setMesial(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
                        atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        facesJsonDTO.setMesial(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
                        atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.NAO_LONG);
                    }
                } else {
                    facesJsonDTO.setMesial(null);
                }

                if (oclusal) {
                    if (situacaoJsonDTO.isConcluido() || RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFaceOclusal())) {
                        facesJsonDTO.setOclusal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
                        atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        facesJsonDTO.setOclusal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
                        atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.NAO_LONG);
                    }
                } else {
                    facesJsonDTO.setOclusal(null);
                }

                if (palatal) {
                    if (situacaoJsonDTO.isConcluido() || RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFacePalatal())) {
                        facesJsonDTO.setPalatal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
                        atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        facesJsonDTO.setPalatal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
                        atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.NAO_LONG);
                    }
                } else {
                    facesJsonDTO.setPalatal(null);
                }

                if (vestibular) {
                    if (situacaoJsonDTO.isConcluido() || RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFaceVestibular())) {
                        facesJsonDTO.setVestibular(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
                        atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        facesJsonDTO.setVestibular(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
                        atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.NAO_LONG);
                    }
                } else {
                    facesJsonDTO.setVestibular(null);
                }

                odontogramaTratamentoJsonDTO.setFaces(facesJsonDTO);
                odontogramaTratamentoJsonDTO.setCoroa(situacaoJsonDTO);
                atendimentoOdontoPlano.setTipoAplicacaoDente(AtendimentoOdontoPlano.TipoAplicacao.COROA.value());
            } else {
                if (situacaoJsonDTO.getTipoPreenchimento() == null || SituacaoDente.TipoPreenchimento.COMPLETO.value().toString().equals(situacaoJsonDTO.getTipoPreenchimento())) {
                    odontogramaTratamentoJsonDTO.setColo(situacaoJsonDTO);
                    odontogramaTratamentoJsonDTO.setRaiz(situacaoJsonDTO);
                    atendimentoOdontoPlano.setTipoAplicacaoDente(AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value());
                } else if (SituacaoDente.TipoPreenchimento.COLO.value().toString().equals(situacaoJsonDTO.getTipoPreenchimento())) {
                    odontogramaTratamentoJsonDTO.setColo(situacaoJsonDTO);
                    if (odontogramaTratamentoJsonDTO.getRaiz() != null && SituacaoDente.TipoPreenchimento.COMPLETO.value().toString().equals(odontogramaTratamentoJsonDTO.getRaiz().getTipoPreenchimento())) {
                        odontogramaTratamentoJsonDTO.setRaiz(null);
                    }
                    atendimentoOdontoPlano.setTipoAplicacaoDente(AtendimentoOdontoPlano.TipoAplicacao.COLO.value());
                } else {
                    odontogramaTratamentoJsonDTO.setRaiz(situacaoJsonDTO);
                    if (odontogramaTratamentoJsonDTO.getColo() != null && SituacaoDente.TipoPreenchimento.COMPLETO.value().toString().equals(odontogramaTratamentoJsonDTO.getColo().getTipoPreenchimento())) {
                        odontogramaTratamentoJsonDTO.setColo(null);
                    }
                    atendimentoOdontoPlano.setTipoAplicacaoDente(AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value());
                }
            }
        }

        AtendimentoOdontoPlano save = BOFactoryWicket.save(atendimentoOdontoPlano);

        situacaoJsonDTO.setCodigoOdontoPlano(save.getCodigo());

        boolean proximoTratamentoDefinido = false;
        if (situacaoJsonDTO.isConcluido()) {
            String face = null;
            if (AtendimentoOdontoPlano.TipoAplicacao.COROA.value().equals(atendimentoOdontoPlano.getTipoAplicacaoDente())) {
                face = "coroa";
            } else if (AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value().equals(atendimentoOdontoPlano.getTipoAplicacaoDente())
                    || AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value().equals(atendimentoOdontoPlano.getTipoAplicacaoDente())) {
                face = "raiz";
            } else if (AtendimentoOdontoPlano.TipoAplicacao.COLO.value().equals(atendimentoOdontoPlano.getTipoAplicacaoDente())) {
                face = "colo";
            }

            proximoTratamentoDefinido = definirProximoTratamentoDente(
                    odontogramaTratamentoJsonDTO,
                    face,
                    Arrays.asList(AtendimentoOdontoPlano.Status.PENDENTE, AtendimentoOdontoPlano.Status.EM_ANDAMENTO),
                    false
            );
        }

        if (getOdontogramaJsonDTO().getTratamentos().contains(odontogramaTratamentoJsonDTO)) {
            int indexOf = this.getOdontogramaJsonDTO().getTratamentos().indexOf(odontogramaTratamentoJsonDTO);
            getOdontogramaJsonDTO().getTratamentos().remove(indexOf);
            getOdontogramaJsonDTO().getTratamentos().add(indexOf, odontogramaTratamentoJsonDTO);
        } else {
            getOdontogramaJsonDTO().getTratamentos().add(odontogramaTratamentoJsonDTO);
        }

        saveJSON(target);

        StringBuilder feedback = new StringBuilder();
        if (proximoTratamentoDefinido) {
            feedback.append("A situação ")
                    .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                    .append(" é histórico e portanto foi definida e concluída automaticamente, e no lugar desta foi aplicado um tratamento que ainda está pendente.");

        } else {
            if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlano.getStatus())) {
                feedback.append("A situação ")
                        .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                        .append(" é histórico e portanto foi definida e concluída automaticamente.");
            } else {
                feedback.append("O tratamento ")
                        .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                        .append(" foi adicionado ao plano!");
            }
        }

        String json = convertObjectToJSON(odontogramaTratamentoJsonDTO);

        StringBuilder script = new StringBuilder();

        script.append("OdontogramFicha.reload(")
                .append("JSON.parse('").append(json).append("')")
                .append(", function() { OdontogramFicha.showMessage('").append(odontogramaTratamentoJsonDTO.getId()).append("', '").append(feedback).append("'); }")
                .append(");");

        target.appendJavaScript(script.toString());
    }

    private void adicionarTratamento(AjaxRequestTarget target, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO, String face) throws ValidacaoException, DAOException {
        if (odontogramaTratamentoJsonDTO != null) {
            Dente dente = LoadManager.getInstance(Dente.class).setId(Long.valueOf(odontogramaTratamentoJsonDTO.getId())).start().getVO();
            AtendimentoOdontoPlano atendimentoOdontoPlano = null;
            if (odontogramaTratamentoJsonDTO.isInativo()) {
                if (!odontogramaTratamentoJsonDTO.getStatus().isCadastrado()) {
                    boolean tratamentoUrgente = odontogramaTratamentoJsonDTO.getStatus().isTratamentoUrgente();
                    SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(Long.valueOf(odontogramaTratamentoJsonDTO.getStatus().getCodigo())).start().getVO();
                    atendimentoOdontoPlano = gerarAtendimentoOdontoPlano(dente, situacaoDente, null, tratamentoUrgente, AtendimentoOdontoPlano.TipoAplicacao.COMPLETO);

                    odontogramaTratamentoJsonDTO.getStatus().setCadastrado(true);
                    odontogramaTratamentoJsonDTO.getStatus().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                    if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                        odontogramaTratamentoJsonDTO.getStatus().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                    } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                        odontogramaTratamentoJsonDTO.getStatus().setTratamentoUrgente(true);
                    }
                }
            } else {
                if ("colo".equals(face)) {
                    if (odontogramaTratamentoJsonDTO.getColo() != null && !odontogramaTratamentoJsonDTO.getColo().isCadastrado()) {
                        odontogramaTratamentoJsonDTO.getColo().setCadastrado(true);

                        Long codigoSituacao = Long.valueOf(odontogramaTratamentoJsonDTO.getColo().getCodigo());

                        SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(SituacaoDente.PROP_CODIGO, codigoSituacao))
                                .start().getVO();

                        AtendimentoOdontoPlano.TipoAplicacao tipoAplicacao = AtendimentoOdontoPlano.TipoAplicacao.COLO;
                        if (SituacaoDente.TipoPreenchimento.COMPLETO.value().equals(situacaoDente.getTipoPreenchimento())
                                || odontogramaTratamentoJsonDTO.getRaiz() != null && odontogramaTratamentoJsonDTO.getRaiz().getCodigo().equals(odontogramaTratamentoJsonDTO.getColo().getCodigo())) {
                            tipoAplicacao = AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ;
                        }

                        boolean tratamentoUrgente = odontogramaTratamentoJsonDTO.getColo().isTratamentoUrgente();
                        atendimentoOdontoPlano = gerarAtendimentoOdontoPlano(dente, situacaoDente, null, tratamentoUrgente, tipoAplicacao);
                        odontogramaTratamentoJsonDTO.getColo().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                        if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                            odontogramaTratamentoJsonDTO.getColo().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                        } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                            odontogramaTratamentoJsonDTO.getColo().setTratamentoUrgente(true);
                        }

                        if (AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value().equals(tipoAplicacao.value())) {
                            odontogramaTratamentoJsonDTO.getRaiz().setCadastrado(true);
                            odontogramaTratamentoJsonDTO.getRaiz().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                            if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                                odontogramaTratamentoJsonDTO.getRaiz().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                                odontogramaTratamentoJsonDTO.getRaiz().setTratamentoUrgente(true);
                            }
                        }
                    }
                } else if ("raiz".equals(face)) {
                    if (odontogramaTratamentoJsonDTO.getRaiz() != null && !odontogramaTratamentoJsonDTO.getRaiz().isCadastrado()) {
                        odontogramaTratamentoJsonDTO.getRaiz().setCadastrado(true);

                        Long codigoSituacao = Long.valueOf(odontogramaTratamentoJsonDTO.getRaiz().getCodigo());

                        SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(SituacaoDente.PROP_CODIGO, codigoSituacao))
                                .start().getVO();

                        AtendimentoOdontoPlano.TipoAplicacao tipoAplicacao = AtendimentoOdontoPlano.TipoAplicacao.RAIZ;
                        if (SituacaoDente.TipoPreenchimento.COMPLETO.value().equals(situacaoDente.getTipoPreenchimento())
                                || odontogramaTratamentoJsonDTO.getColo() != null && odontogramaTratamentoJsonDTO.getColo().getCodigo().equals(odontogramaTratamentoJsonDTO.getRaiz().getCodigo())) {
                            tipoAplicacao = AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ;
                        }

                        boolean tratamentoUrgente = odontogramaTratamentoJsonDTO.getRaiz().isTratamentoUrgente();
                        atendimentoOdontoPlano = gerarAtendimentoOdontoPlano(dente, situacaoDente, null, tratamentoUrgente, tipoAplicacao);
                        odontogramaTratamentoJsonDTO.getRaiz().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                        if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                            odontogramaTratamentoJsonDTO.getRaiz().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                        } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                            odontogramaTratamentoJsonDTO.getRaiz().setTratamentoUrgente(true);
                        }

                        if (AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value().equals(tipoAplicacao.value())) {
                            odontogramaTratamentoJsonDTO.getColo().setCadastrado(true);
                            odontogramaTratamentoJsonDTO.getColo().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                            if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                                odontogramaTratamentoJsonDTO.getColo().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                                odontogramaTratamentoJsonDTO.getColo().setTratamentoUrgente(true);
                            }
                        }
                    }
                } else if ("coroa".equals(face)) {
                    if (odontogramaTratamentoJsonDTO.getCoroa() != null && !odontogramaTratamentoJsonDTO.getCoroa().isCadastrado()) {
                        odontogramaTratamentoJsonDTO.getCoroa().setCadastrado(true);

                        Long codigoSituacao = Long.valueOf(odontogramaTratamentoJsonDTO.getCoroa().getCodigo());

                        SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(SituacaoDente.PROP_CODIGO, codigoSituacao))
                                .start().getVO();

                        boolean tratamentoUrgente = odontogramaTratamentoJsonDTO.getCoroa().isTratamentoUrgente();
                        atendimentoOdontoPlano = gerarAtendimentoOdontoPlano(dente, situacaoDente, odontogramaTratamentoJsonDTO.getFaces(), tratamentoUrgente, AtendimentoOdontoPlano.TipoAplicacao.COROA);
                        odontogramaTratamentoJsonDTO.getCoroa().setCodigoOdontoPlano(atendimentoOdontoPlano.getCodigo());
                        if (atendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                            odontogramaTratamentoJsonDTO.getCoroa().setCodigoFicha(atendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                        } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlano.getFlagUrgente())) {
                            odontogramaTratamentoJsonDTO.getCoroa().setTratamentoUrgente(true);
                        }
                    }
                }
            }

            if (atendimentoOdontoPlano != null) {
                boolean proximoTratamentoDefinido = false;
                if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlano.getStatus())) {
                    proximoTratamentoDefinido = definirProximoTratamentoDente(
                            odontogramaTratamentoJsonDTO,
                            face,
                            Arrays.asList(AtendimentoOdontoPlano.Status.PENDENTE, AtendimentoOdontoPlano.Status.EM_ANDAMENTO),
                            false
                    );
                }

                if (getOdontogramaJsonDTO().getTratamentos().contains(odontogramaTratamentoJsonDTO)) {
                    int indexOf = this.getOdontogramaJsonDTO().getTratamentos().indexOf(odontogramaTratamentoJsonDTO);
                    getOdontogramaJsonDTO().getTratamentos().remove(indexOf);
                    getOdontogramaJsonDTO().getTratamentos().add(indexOf, odontogramaTratamentoJsonDTO);
                } else {
                    getOdontogramaJsonDTO().getTratamentos().add(odontogramaTratamentoJsonDTO);
                }

                saveJSON(target);

                StringBuilder feedback = new StringBuilder();
                if (proximoTratamentoDefinido) {
                    feedback.append("A situação ")
                            .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                            .append(" é histórico e portanto foi definida e concluída automaticamente, e no lugar desta foi aplicado um tratamento que ainda está pendente.");

                    String json = convertObjectToJSON(odontogramaTratamentoJsonDTO);

                    StringBuilder script = new StringBuilder("JSON.parse('").append(json).append("')");
                    script.append(", function() { OdontogramFicha.showMessage('").append(odontogramaTratamentoJsonDTO.getId()).append("', '").append(feedback).append("'); }");
                    script.insert(0, "OdontogramFicha.reload(").append(")");

                    target.appendJavaScript(script.toString());
                } else {
                    if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlano.getStatus())) {
                        feedback.append("A situação ")
                                .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                                .append(" é histórico e portanto foi definida e concluída automaticamente.");
                        target.getHeaderResponse().render(OnDomReadyHeaderItem.forScript("OdontogramFicha.showMessage('" + odontogramaTratamentoJsonDTO.getId() + "', '" + feedback.toString() + "')"));
                    } else {
                        feedback.append("O tratamento ")
                                .append(atendimentoOdontoPlano.getSituacaoDente().getDescricao())
                                .append(" foi adicionado ao plano!");
                        target.getHeaderResponse().render(OnDomReadyHeaderItem.forScript("OdontogramFicha.showMessage('" + odontogramaTratamentoJsonDTO.getId() + "', '" + feedback.toString() + "')"));
                    }
                }

                updateTable(target);
            }
        }
    }

    public boolean definirProximoTratamentoDente(AjaxRequestTarget target, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO, String face) {
        // TENTA RESTAURAR PRIMEIRO OS TRATAMENTOS QUE ESTÃO PENDENTE OU EM ANDAMENTO
        boolean definido = definirProximoTratamentoDente(
                odontogramaTratamentoJsonDTO,
                face,
                Arrays.asList(AtendimentoOdontoPlano.Status.PENDENTE, AtendimentoOdontoPlano.Status.EM_ANDAMENTO),
                false
        );

        // CASO NÃO EXISTA TRATAMENTOS PENDENTES OU EM ANDAMENTO, APLICA ENTÃO OS TRATAMENTOS CONCLUÍDOS
        if (!definido) {
            definido = definirProximoTratamentoDente(
                    odontogramaTratamentoJsonDTO,
                    face,
                    Arrays.asList(AtendimentoOdontoPlano.Status.CONCLUIDO, AtendimentoOdontoPlano.Status.HISTORICO),
                    true
            );
        }

        int indexOf = this.getOdontogramaJsonDTO().getTratamentos().indexOf(odontogramaTratamentoJsonDTO);
        getOdontogramaJsonDTO().getTratamentos().remove(indexOf);
        getOdontogramaJsonDTO().getTratamentos().add(indexOf, odontogramaTratamentoJsonDTO);

        saveJSON(target);

        String json = convertObjectToJSON(odontogramaTratamentoJsonDTO);

        StringBuilder script = new StringBuilder("JSON.parse('").append(json).append("')");
        if (definido) {
            script.append(", function() { OdontogramFicha.showMessage('").append(odontogramaTratamentoJsonDTO.getId()).append("', 'A situação anterior foi redefinida!'); }");
        } else {
            script.append(", function() { OdontogramFicha.showMessage('" + odontogramaTratamentoJsonDTO.getId() + "', 'Revertido!'); }");
        }

        script.insert(0, "OdontogramFicha.reload(").append(")");

        target.getHeaderResponse().render(OnDomReadyHeaderItem.forScript(script.toString()));

        return definido;
    }

    private boolean definirProximoTratamentoDente(OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO, String face, final List<AtendimentoOdontoPlano.Status> statusList, boolean limpar) {
        boolean filtrarFicha = true, coloRaizRestaurada = false, coloRestaurado = false, raizRestaurada = false, coroaRestaurada = false;

        QueryCustom.QueryCustomParameter defaultParams = new QueryCustom.QueryCustomParameter(
                new BuilderQueryCustom.QueryGroupAnd(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), prontuarioController.getAtendimento().getUsuarioCadsus())),
                new BuilderQueryCustom.QueryGroupAnd(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_DENTE, Dente.PROP_CODIGO), Long.valueOf(odontogramaTratamentoJsonDTO.getId()))),
                new BuilderQueryCustom.QueryGroupAnd(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_EVOLUCAO, AtendimentoOdontoPlano.Tipo.DENTE.value()))
        );

        List<AtendimentoOdontoPlano.Status> auxList = new ArrayList(statusList);
        do {
            AtendimentoOdontoPlano.Status status = auxList.remove(0);

            LoadManager loadManager = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                    .addParameter(defaultParams)
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_STATUS, status.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoOdontoPlano.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE_NULLS_LAST))
                    .setMaxResults(1);

            if (this.atendimentoOdontoFicha != null) {
                if (filtrarFicha) {
                    loadManager.addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, this.atendimentoOdontoFicha));
                } else {
                    loadManager.addParameter(
                            new QueryCustom.QueryCustomParameter(
                                    new BuilderQueryCustom.QueryGroupAnd(
                                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, QueryCustom.QueryCustomParameter.DIFERENTE, this.atendimentoOdontoFicha))),
                                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_FLAG_URGENTE, RepositoryComponentDefault.SIM_LONG)))
                                    )
                            )
                    );
                }
            }

            AtendimentoOdontoPlano ultimoAtendimentoOdontoPlano = loadManager.newInstance().start().getVO();
            if (ultimoAtendimentoOdontoPlano != null) {
                if (AtendimentoOdontoPlano.TipoAplicacao.COMPLETO.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())
                        && !coroaRestaurada && !coloRaizRestaurada && !coloRestaurado && !raizRestaurada) {
                    SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(ultimoAtendimentoOdontoPlano.getSituacaoDente().getCodigo()).start().getVO();
                    SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
                    situacaoJsonDTO.setCadastrado(true);
                    situacaoJsonDTO.setDesabilitaAoAplicar(true);
                    situacaoJsonDTO.setCodigoAtendimento(ultimoAtendimentoOdontoPlano.getAtendimento().getCodigo());
                    situacaoJsonDTO.setCodigoOdontoPlano(ultimoAtendimentoOdontoPlano.getCodigo());

                    if (ultimoAtendimentoOdontoPlano.getAtendimentoOdontoFicha() != null) {
                        situacaoJsonDTO.setCodigoFicha(ultimoAtendimentoOdontoPlano.getAtendimentoOdontoFicha().getCodigo());
                    } else if (RepositoryComponentDefault.SIM_LONG.equals(ultimoAtendimentoOdontoPlano.getFlagUrgente())) {
                        situacaoJsonDTO.setTratamentoUrgente(true);
                    }

                    if (AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(ultimoAtendimentoOdontoPlano.getStatus()) || AtendimentoOdontoPlano.Status.HISTORICO.value().equals(ultimoAtendimentoOdontoPlano.getStatus())) {
                        situacaoJsonDTO.setConcluido(true);
                    }

                    odontogramaTratamentoJsonDTO.setInativo(true);
                    odontogramaTratamentoJsonDTO.setStatus(situacaoJsonDTO);

                    return true;
                } else {
                    odontogramaTratamentoJsonDTO.setInativo(false);
                    odontogramaTratamentoJsonDTO.setStatus(null);

                    if (face == null) {
                        if (!coroaRestaurada) {
                            AtendimentoOdontoPlano atendimentoOdontoPlanoCoroa;
                            if (AtendimentoOdontoPlano.TipoAplicacao.COROA.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoCoroa = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoCoroa = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COROA.value()))
                                        .start().getVO();
                            }

                            coroaRestaurada = definirTratamentoCoroa(atendimentoOdontoPlanoCoroa, odontogramaTratamentoJsonDTO);
                        }

                        if (!coloRaizRestaurada && !(coloRestaurado && raizRestaurada)) {
                            AtendimentoOdontoPlano atendimentoOdontoPlanoColoRaiz;
                            AtendimentoOdontoPlano atendimentoOdontoPlanoColo;
                            AtendimentoOdontoPlano atendimentoOdontoPlanoRaiz;

                            if (AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoColoRaiz = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoColoRaiz = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value()))
                                        .start().getVO();
                            }

                            if (AtendimentoOdontoPlano.TipoAplicacao.COLO.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoColo = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoColo = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COLO.value()))
                                        .start().getVO();
                            }

                            if (AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoRaiz = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoRaiz = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value()))
                                        .start().getVO();
                            }

                            if (!coloRestaurado && atendimentoOdontoPlanoColo != null && atendimentoOdontoPlanoColo.getCodigo() > ultimoAtendimentoOdontoPlano.getCodigo()) {
                                ultimoAtendimentoOdontoPlano = atendimentoOdontoPlanoColo;
                            }

                            if (!raizRestaurada && atendimentoOdontoPlanoRaiz != null && atendimentoOdontoPlanoRaiz.getCodigo() > ultimoAtendimentoOdontoPlano.getCodigo()) {
                                ultimoAtendimentoOdontoPlano = atendimentoOdontoPlanoRaiz;
                            }

                            if (!(coloRestaurado && raizRestaurada)
                                    && atendimentoOdontoPlanoColoRaiz != null
                                    && atendimentoOdontoPlanoColoRaiz.getCodigo() >= ultimoAtendimentoOdontoPlano.getCodigo()) {
                                coloRaizRestaurada = definirTratamentoColoRaiz(atendimentoOdontoPlanoColoRaiz, odontogramaTratamentoJsonDTO);
                            } else {
                                if (!coloRestaurado && atendimentoOdontoPlanoColo != null) {
                                    coloRestaurado = definirTratamentoColo(atendimentoOdontoPlanoColo, odontogramaTratamentoJsonDTO);
                                }

                                if (!raizRestaurada && atendimentoOdontoPlanoRaiz != null) {
                                    raizRestaurada = definirTratamentoRaiz(atendimentoOdontoPlanoRaiz, odontogramaTratamentoJsonDTO);
                                }
                            }
                        }

                        if (coroaRestaurada && (coloRaizRestaurada || (coloRestaurado && raizRestaurada))) {
                            break;
                        }
                    } else {
                        if ("coroa".equals(face)) {
                            AtendimentoOdontoPlano atendimentoOdontoPlanoCoroa;
                            if (AtendimentoOdontoPlano.TipoAplicacao.COROA.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoCoroa = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoCoroa = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COROA.value()))
                                        .start().getVO();
                            }

                            coroaRestaurada = definirTratamentoCoroa(atendimentoOdontoPlanoCoroa, odontogramaTratamentoJsonDTO);
                            if (coroaRestaurada) {
                                break;
                            }
                        } else {
                            AtendimentoOdontoPlano atendimentoOdontoPlanoColoRaiz;
                            AtendimentoOdontoPlano atendimentoOdontoPlanoColo;
                            AtendimentoOdontoPlano atendimentoOdontoPlanoRaiz;

                            if (AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoColoRaiz = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoColoRaiz = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COLO_RAIZ.value()))
                                        .start().getVO();
                            }

                            if (AtendimentoOdontoPlano.TipoAplicacao.COLO.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoColo = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoColo = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.COLO.value()))
                                        .start().getVO();
                            }

                            if (AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value().equals(ultimoAtendimentoOdontoPlano.getTipoAplicacaoDente())) {
                                atendimentoOdontoPlanoRaiz = ultimoAtendimentoOdontoPlano;
                            } else {
                                atendimentoOdontoPlanoRaiz = loadManager.newInstance()
                                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_TIPO_APLICACAO_DENTE, AtendimentoOdontoPlano.TipoAplicacao.RAIZ.value()))
                                        .start().getVO();
                            }

                            if (!coloRestaurado && atendimentoOdontoPlanoColo != null && atendimentoOdontoPlanoColo.getCodigo() > ultimoAtendimentoOdontoPlano.getCodigo()) {
                                ultimoAtendimentoOdontoPlano = atendimentoOdontoPlanoColo;
                            }

                            if (!raizRestaurada && atendimentoOdontoPlanoRaiz != null && atendimentoOdontoPlanoRaiz.getCodigo() > ultimoAtendimentoOdontoPlano.getCodigo()) {
                                ultimoAtendimentoOdontoPlano = atendimentoOdontoPlanoRaiz;
                            }

                            if (!(coloRestaurado && raizRestaurada)
                                    && atendimentoOdontoPlanoColoRaiz != null
                                    && atendimentoOdontoPlanoColoRaiz.getCodigo() >= ultimoAtendimentoOdontoPlano.getCodigo()) {
                                coloRaizRestaurada = definirTratamentoColoRaiz(atendimentoOdontoPlanoColoRaiz, odontogramaTratamentoJsonDTO);
                                if (coloRaizRestaurada) {
                                    break;
                                }
                            } else {
                                if (!coloRestaurado && atendimentoOdontoPlanoColo != null) {
                                    coloRestaurado = definirTratamentoColo(atendimentoOdontoPlanoColo, odontogramaTratamentoJsonDTO);
                                }

                                if (!raizRestaurada && atendimentoOdontoPlanoRaiz != null) {
                                    raizRestaurada = definirTratamentoRaiz(atendimentoOdontoPlanoRaiz, odontogramaTratamentoJsonDTO);
                                }

                                if (coloRestaurado && raizRestaurada) {
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(auxList) && filtrarFicha && this.atendimentoOdontoFicha != null) {
                // IRÁ TENTAR RESTAURAR COM AS MESMAS SITUAÇÕES, PORÉM PARA FICHAS DIFERENTES
                filtrarFicha = false;
                auxList = new ArrayList(statusList);
            }
        } while(CollectionUtils.isNotNullEmpty(auxList));

        if (face == null && (coroaRestaurada || coloRaizRestaurada || coloRestaurado || raizRestaurada) ||
                face != null && ("coroa".equals(face) && coroaRestaurada || coloRaizRestaurada || "colo".equals(face) && coloRestaurado || "raiz".equals(face) && raizRestaurada)) {
            return true;
        } else if (limpar) {
            if (face != null) {
                if ("coroa".equals(face)) {
                    odontogramaTratamentoJsonDTO.setCoroa(null);
                    odontogramaTratamentoJsonDTO.setFaces(null);
                } else if (ObjectUtils.equals(odontogramaTratamentoJsonDTO.getColo(), odontogramaTratamentoJsonDTO.getRaiz())) {
                    odontogramaTratamentoJsonDTO.setColo(null);
                    odontogramaTratamentoJsonDTO.setRaiz(null);
                } else if ("colo".equals(face)) {
                    odontogramaTratamentoJsonDTO.setColo(null);
                } else if ("raiz".equals(face)) {
                    odontogramaTratamentoJsonDTO.setRaiz(null);
                }
            }

            odontogramaTratamentoJsonDTO.setInativo(false);
            odontogramaTratamentoJsonDTO.setStatus(null);
        }

        return false;
    }

    private boolean definirTratamentoCoroa(AtendimentoOdontoPlano atendimentoOdontoPlanoCoroa, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO) {
        if (atendimentoOdontoPlanoCoroa != null) {
            SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(atendimentoOdontoPlanoCoroa.getSituacaoDente().getCodigo()).start().getVO();
            SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
            situacaoJsonDTO.setCodigoAtendimento(atendimentoOdontoPlanoCoroa.getAtendimento().getCodigo());
            situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlanoCoroa.getCodigo());
            situacaoJsonDTO.setCadastrado(true);

            if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlanoCoroa.getStatus()) || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlanoCoroa.getStatus())) {
                situacaoJsonDTO.setConcluido(true);
            }

            if (atendimentoOdontoPlanoCoroa.getAtendimentoOdontoFicha() != null) {
                situacaoJsonDTO.setCodigoFicha(atendimentoOdontoPlanoCoroa.getAtendimentoOdontoFicha().getCodigo());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFlagUrgente())) {
                situacaoJsonDTO.setTratamentoUrgente(true);
            }

            odontogramaTratamentoJsonDTO.setCoroa(situacaoJsonDTO);

            FacesOdontogramaJsonDTO facesJsonDTO = new FacesOdontogramaJsonDTO();

            if (RepositoryComponentDefault.NAO_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceDistal())) {
                facesJsonDTO.setDistal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceDistal())) {
                facesJsonDTO.setDistal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
            }

            if (RepositoryComponentDefault.NAO_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceMesial())) {
                facesJsonDTO.setMesial(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceMesial())) {
                facesJsonDTO.setMesial(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
            }

            if (RepositoryComponentDefault.NAO_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceOclusal())) {
                facesJsonDTO.setOclusal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceOclusal())) {
                facesJsonDTO.setOclusal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
            }

            if (RepositoryComponentDefault.NAO_LONG.equals(atendimentoOdontoPlanoCoroa.getFacePalatal())) {
                facesJsonDTO.setPalatal(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFacePalatal())) {
                facesJsonDTO.setPalatal(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
            }

            if (RepositoryComponentDefault.NAO_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceVestibular())) {
                facesJsonDTO.setVestibular(FacesOdontogramaJsonDTO.Status.PENDENTE.value().toString());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoCoroa.getFaceVestibular())) {
                facesJsonDTO.setVestibular(FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString());
            }

            odontogramaTratamentoJsonDTO.setFaces(facesJsonDTO);
            return true;
        } else {
            odontogramaTratamentoJsonDTO.setCoroa(null);
            odontogramaTratamentoJsonDTO.setFaces(null);
        }

        return false;
    }

    private boolean definirTratamentoColoRaiz(AtendimentoOdontoPlano atendimentoOdontoPlanoColoRaiz, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO) {
        if (atendimentoOdontoPlanoColoRaiz != null) {
            SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(atendimentoOdontoPlanoColoRaiz.getSituacaoDente().getCodigo()).start().getVO();
            SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
            situacaoJsonDTO.setCodigoAtendimento(atendimentoOdontoPlanoColoRaiz.getAtendimento().getCodigo());
            situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlanoColoRaiz.getCodigo());
            situacaoJsonDTO.setCadastrado(true);

            if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlanoColoRaiz.getStatus()) || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlanoColoRaiz.getStatus())) {
                situacaoJsonDTO.setConcluido(true);
            }

            if (atendimentoOdontoPlanoColoRaiz.getAtendimentoOdontoFicha() != null) {
                situacaoJsonDTO.setCodigoFicha(atendimentoOdontoPlanoColoRaiz.getAtendimentoOdontoFicha().getCodigo());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoColoRaiz.getFlagUrgente())) {
                situacaoJsonDTO.setTratamentoUrgente(true);
            }

            odontogramaTratamentoJsonDTO.setRaiz(situacaoJsonDTO);
            odontogramaTratamentoJsonDTO.setColo(situacaoJsonDTO);
            return true;
        }

        return false;
    }

    private boolean definirTratamentoColo(AtendimentoOdontoPlano atendimentoOdontoPlanoColo, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO) {
        if (atendimentoOdontoPlanoColo != null) {
            SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(atendimentoOdontoPlanoColo.getSituacaoDente().getCodigo()).start().getVO();
            SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
            situacaoJsonDTO.setCodigoAtendimento(atendimentoOdontoPlanoColo.getAtendimento().getCodigo());
            situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlanoColo.getCodigo());
            situacaoJsonDTO.setCadastrado(true);

            if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlanoColo.getStatus()) || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlanoColo.getStatus())) {
                situacaoJsonDTO.setConcluido(true);
            }

            if (atendimentoOdontoPlanoColo.getAtendimentoOdontoFicha() != null) {
                situacaoJsonDTO.setCodigoFicha(atendimentoOdontoPlanoColo.getAtendimentoOdontoFicha().getCodigo());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoColo.getFlagUrgente())) {
                situacaoJsonDTO.setTratamentoUrgente(true);
            }

            odontogramaTratamentoJsonDTO.setColo(situacaoJsonDTO);
            return true;
        } else {
            odontogramaTratamentoJsonDTO.setColo(null);
        }

        return false;
    }

    private boolean definirTratamentoRaiz(AtendimentoOdontoPlano atendimentoOdontoPlanoRaiz, OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO) {
        if (atendimentoOdontoPlanoRaiz != null) {
            SituacaoDente situacaoDente = LoadManager.getInstance(SituacaoDente.class).setId(atendimentoOdontoPlanoRaiz.getSituacaoDente().getCodigo()).start().getVO();
            SituacaoJsonDTO situacaoJsonDTO = new SituacaoJsonDTO(situacaoDente);
            situacaoJsonDTO.setCodigoAtendimento(atendimentoOdontoPlanoRaiz.getAtendimento().getCodigo());
            situacaoJsonDTO.setCodigoOdontoPlano(atendimentoOdontoPlanoRaiz.getCodigo());
            situacaoJsonDTO.setCadastrado(true);

            if (AtendimentoOdontoPlano.Status.HISTORICO.value().equals(atendimentoOdontoPlanoRaiz.getStatus()) || AtendimentoOdontoPlano.Status.CONCLUIDO.value().equals(atendimentoOdontoPlanoRaiz.getStatus())) {
                situacaoJsonDTO.setConcluido(true);
            }

            if (atendimentoOdontoPlanoRaiz.getAtendimentoOdontoFicha() != null) {
                situacaoJsonDTO.setCodigoFicha(atendimentoOdontoPlanoRaiz.getAtendimentoOdontoFicha().getCodigo());
            } else if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoPlanoRaiz.getFlagUrgente())) {
                situacaoJsonDTO.setTratamentoUrgente(true);
            }

            odontogramaTratamentoJsonDTO.setRaiz(situacaoJsonDTO);
            return true;
        } else {
            odontogramaTratamentoJsonDTO.setRaiz(null);
        }

        return false;
    }

    private void atualizarFacesOdontoPlano(AjaxRequestTarget target, OdontogramaTratamentoJsonDTO dto) throws ValidacaoException, DAOException {
        Long codigoOdontoPlano = dto.getCoroa().getCodigoOdontoPlano();
        if (codigoOdontoPlano != null && dto.getFaces() != null) {
            AtendimentoOdontoPlano atendimentoOdontoPlano = LoadManager.getInstance(AtendimentoOdontoPlano.class).setId(codigoOdontoPlano).start().getVO();
            if (atendimentoOdontoPlano != null) {
                if (dto.getFaces().getDistal() == null) {
                    atendimentoOdontoPlano.setFaceDistal(null);
                } else {
                    if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(dto.getFaces().getDistal())) {
                        atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.NAO_LONG);
                    }
                }

                if (dto.getFaces().getMesial() == null) {
                    atendimentoOdontoPlano.setFaceMesial(null);
                } else {
                    if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(dto.getFaces().getMesial())) {
                        atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.NAO_LONG);
                    }
                }

                if (dto.getFaces().getOclusal() == null) {
                    atendimentoOdontoPlano.setFaceOclusal(null);
                } else {
                    if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(dto.getFaces().getOclusal())) {
                        atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.NAO_LONG);
                    }
                }

                if (dto.getFaces().getPalatal() == null) {
                    atendimentoOdontoPlano.setFacePalatal(null);
                } else {
                    if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(dto.getFaces().getPalatal())) {
                        atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.NAO_LONG);
                    }
                }

                if (dto.getFaces().getVestibular() == null) {
                    atendimentoOdontoPlano.setFaceVestibular(null);
                } else {
                    if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(dto.getFaces().getVestibular())) {
                        atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.NAO_LONG);
                    }
                }

                BOFactoryWicket.save(atendimentoOdontoPlano);
                updateTable(target);
            }
        }

        if (getOdontogramaJsonDTO().getTratamentos().contains(dto)) {
            int indexOf = this.getOdontogramaJsonDTO().getTratamentos().indexOf(dto);
            getOdontogramaJsonDTO().getTratamentos().remove(indexOf);
            getOdontogramaJsonDTO().getTratamentos().add(indexOf, dto);
        } else {
            getOdontogramaJsonDTO().getTratamentos().add(dto);
        }

        saveJSON(target);
    }

    public void updateTable(AjaxRequestTarget target) {
    }

    private AtendimentoOdontoPlano gerarAtendimentoOdontoPlanoProtese(SituacaoDente situacaoDente, boolean superior) throws ValidacaoException, DAOException {
        AtendimentoOdontoPlano atendimentoOdontoPlano = new AtendimentoOdontoPlano();
        atendimentoOdontoPlano.setSituacaoDente(situacaoDente);
        atendimentoOdontoPlano.setDataCadastro(DataUtil.getDataAtual());
        atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.HISTORICO.value());
        atendimentoOdontoPlano.setTipoEvolucao(AtendimentoOdontoPlano.Tipo.ARCADA.value());
        atendimentoOdontoPlano.setObservacao("Registrado quando selecionada a opção \"" + (superior ? "Superior" : "Inferior") + "\" do campo \"Prótese\".");
        atendimentoOdontoPlano.setAtendimento(prontuarioController.getAtendimento());
        atendimentoOdontoPlano.setAtendimentoOdontoFicha(atendimentoOdontoFicha);

        if (superior) {
            atendimentoOdontoPlano.setArcadaSuperior(RepositoryComponentDefault.SIM_LONG);
        } else {
            atendimentoOdontoPlano.setArcadaInferior(RepositoryComponentDefault.SIM_LONG);
        }

        AtendimentoOdontoPlano save = BOFactoryWicket.save(atendimentoOdontoPlano);
        return save;
    }

    private void excluirRegistroAtendimentoOdontoPlanoProtese(Long codigo) throws ValidacaoException, DAOException {
        if (codigo != null) {
            AtendimentoOdontoPlano atendimentoOdontoPlano = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                    .setId(codigo)
                    .setMaxResults(1)
                    .start().getVO();
            if (atendimentoOdontoPlano != null) {
                BOFactoryWicket.delete(atendimentoOdontoPlano);
            }
        }
    }

    private AtendimentoOdontoPlano gerarAtendimentoOdontoPlano(Dente dente, SituacaoDente situacaoDente, FacesOdontogramaJsonDTO faces, boolean tratamentoUrgente, AtendimentoOdontoPlano.TipoAplicacao tipoAplicacao) throws ValidacaoException, DAOException {
        AtendimentoOdontoPlano atendimentoOdontoPlano = new AtendimentoOdontoPlano();
        atendimentoOdontoPlano.setDente(dente);
        atendimentoOdontoPlano.setSituacaoDente(situacaoDente);
        atendimentoOdontoPlano.setAtendimento(prontuarioController.getAtendimento());
        atendimentoOdontoPlano.setAtendimentoOdontoFicha(this.atendimentoOdontoFicha);
        atendimentoOdontoPlano.setDataCadastro(DataUtil.getDataAtual());
        atendimentoOdontoPlano.setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());

        if (tratamentoUrgente) {
            atendimentoOdontoPlano.setFlagUrgente(RepositoryComponentDefault.SIM_LONG);
        }

        if (tipoAplicacao != null) {
            atendimentoOdontoPlano.setTipoAplicacaoDente(tipoAplicacao.value());
        }

        if (faces != null) {
            atendimentoOdontoPlano.setFaces(faces.getDescricaoFormatado());

            if (faces.getDistal() != null) {
                if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(faces.getDistal())) {
                    atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.SIM_LONG);
                } else {
                    atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.NAO_LONG);
                }
            } else {
                atendimentoOdontoPlano.setFaceDistal(null);
            }

            if (faces.getMesial() != null) {
                if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(faces.getMesial())) {
                    atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.SIM_LONG);
                } else {
                    atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.NAO_LONG);
                }
            } else {
                atendimentoOdontoPlano.setFaceMesial(null);
            }

            if (faces.getOclusal() != null) {
                if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(faces.getOclusal())) {
                    atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.SIM_LONG);
                } else {
                    atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.NAO_LONG);
                }
            } else {
                atendimentoOdontoPlano.setFaceOclusal(null);
            }

            if (faces.getPalatal() != null) {
                if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(faces.getPalatal())) {
                    atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.SIM_LONG);
                } else {
                    atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.NAO_LONG);
                }
            } else {
                atendimentoOdontoPlano.setFacePalatal(null);
            }

            if (faces.getVestibular() != null) {
                if (FacesOdontogramaJsonDTO.Status.CONCLUIDO.value().toString().equals(faces.getVestibular())) {
                    atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.SIM_LONG);
                } else {
                    atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.NAO_LONG);
                }
            } else {
                atendimentoOdontoPlano.setFaceVestibular(null);
            }
        }

        if (SituacaoDente.TipoSituacao.PROCEDIMENTO.value().equals(situacaoDente.getTipoSituacao())) {
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.PENDENTE.value());
        } else {
            atendimentoOdontoPlano.setAtendimentoExecucao(prontuarioController.getAtendimento());
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.HISTORICO.value());
        }

        AtendimentoOdontoPlano save = BOFactoryWicket.save(atendimentoOdontoPlano);
        return save;
    }

    public void saveJSON(AjaxRequestTarget target) {
        String json = convertObjectToJSON(odontogramaJsonDTO);
        saveJSON(target, json, false);
    }

    private void saveJSON(AjaxRequestTarget target, String json, boolean convertJsonToObject) {
        try {
            getUsuarioCadsusDado().setOdontogramaJson(json);
            this.usuarioCadsusDado = BOFactoryWicket.save(usuarioCadsusDado);
            target.getHeaderResponse().render(OnDomReadyHeaderItem.forScript("OdontogramFicha.setJSON('" + json + "')"));

            if (convertJsonToObject) {
                this.odontogramaJsonDTO = (OdontogramaJsonDTO) convertJsonToObject(json, OdontogramaJsonDTO.class);
            }

            target.add(containerProteses);
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        if (usuarioCadsusDado == null) {
            usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, prontuarioController.getAtendimento().getUsuarioCadsus().getCodigo()))
                    .start().getVO();
            if (usuarioCadsusDado == null) {
                usuarioCadsusDado = new UsuarioCadsusDado();
                usuarioCadsusDado.setCodigo(prontuarioController.getAtendimento().getUsuarioCadsus().getCodigo());
            }
        }
        return usuarioCadsusDado;
    }

    private void updateOdontogramaJS(IHeaderResponse response) {
        String json = getUsuarioCadsusDado().getOdontogramaJson();
        if (json != null) {
            this.odontogramaJsonDTO = (OdontogramaJsonDTO) convertJsonToObject(json, OdontogramaJsonDTO.class);
            this.proteseSuperior = getOdontogramaJsonDTO().isProteseSuperior() ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG;
            this.proteseInferior = getOdontogramaJsonDTO().isProteseInferior() ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG;
        }

        response.render(OnDomReadyHeaderItem.forScript("OdontogramFicha.setJSON(" + (json != null ? ("'" + json + "'") : "") + ")"));
    }

    public void refresh(IHeaderResponse response) {
        updateOdontogramaJS(response);
        response.render(OnLoadHeaderItem.forScript("OdontogramFicha.refresh()"));
    }

    public OdontogramaJsonDTO getOdontogramaJsonDTO() {
        if (odontogramaJsonDTO == null) {
            odontogramaJsonDTO = new OdontogramaJsonDTO();
        }
        return odontogramaJsonDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        response.render(CssHeaderItem.forReference(Resources.CSS_ODONTOGRAMA));
        response.render(CssHeaderItem.forReference(new CssResourceReference(FichaOdontogramaPanel.class, CSS_FILE)));

        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_SERIALIZE_OBJECT_MIN));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SVG_INJECTOR));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_NOTIFY));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_ODONTOGRAMA));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FICHA_ODONTOGRAMA));

        updateOdontogramaJS(response);
    }

}
