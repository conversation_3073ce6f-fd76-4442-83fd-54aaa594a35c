package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDetalhesAtendimentoPrimarioGemelar extends Window{

    private PnlDetalhesAtendimentoPrimarioGemelar pnlDetalhesAtendimentoPrimarioGemelar;
    private boolean profMedico;

    public DlgDetalhesAtendimentoPrimarioGemelar(String id,boolean profMedico){
        super(id);
        this.profMedico=profMedico;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("detalhesInformacaoFetal");
            }
        });
                
        setInitialWidth(650);
        setInitialHeight(400);
        setResizable(true);
        
        setContent(pnlDetalhesAtendimentoPrimarioGemelar = new PnlDetalhesAtendimentoPrimarioGemelar(getContentId(),profMedico) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public void show(AjaxRequestTarget target, AtendimentoPrimario atendimentoPrimario){
        show(target);
        pnlDetalhesAtendimentoPrimarioGemelar.setObject(target, atendimentoPrimario);
    }
}