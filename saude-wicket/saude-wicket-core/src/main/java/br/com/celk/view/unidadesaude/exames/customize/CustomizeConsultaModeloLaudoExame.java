package br.com.celk.view.unidadesaude.exames.customize;

import br.com.ksisolucoes.agendamento.exame.dto.ModeloLaudoExameDTOParam;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.exame.ModeloLaudoExame;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaModeloLaudoExame extends CustomizeConsultaAdapter {

    private ModeloLaudoExameDTOParam param;

    public CustomizeConsultaModeloLaudoExame(ModeloLaudoExameDTOParam param) {
        this.param = param;
    }

    @Override
    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
        filterProperties.put(Bundle.getStringApplication("rotulo_descricao"), new QueryCustom.QueryCustomParameter(ModeloLaudoExame.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE));
        filterProperties.put(Bundle.getStringApplication("rotulo_referencia"), new QueryCustom.QueryCustomParameter(ModeloLaudoExame.PROP_REFERENCIA, BuilderQueryCustom.QueryParameter.IGUAL));
    }

    @Override
    public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
        parameters.add(new QueryCustom.QueryCustomParameter(ModeloLaudoExame.PROP_EXAME_PROCEDIMENTO, BuilderQueryCustom.QueryParameter.IGUAL, param.getExameProcedimento(), HQLHelper.NOT_RESOLVE_TYPE, param.getExameProcedimento()));
        parameters.add(new QueryCustom.QueryCustomParameter(ModeloLaudoExame.PROP_TIPO_EXAME, BuilderQueryCustom.QueryParameter.IGUAL, param.getTipoExame(), HQLHelper.NOT_RESOLVE_TYPE, param.getTipoExame()));
    }
    
    @Override
    public void consultaCustomizeSorters(List<BuilderQueryCustom.QuerySorter> sorters) {
        sorters.add(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ModeloLaudoExame.PROP_REFERENCIA)));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(Bundle.getStringApplication("rotulo_referencia"), ModeloLaudoExame.PROP_REFERENCIA);
        properties.put(Bundle.getStringApplication("rotulo_descricao"), ModeloLaudoExame.PROP_DESCRICAO);
    }

    @Override
    public Class getClassConsulta() {
        return ModeloLaudoExame.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(ModeloLaudoExame.class).getProperties();
    }
}
