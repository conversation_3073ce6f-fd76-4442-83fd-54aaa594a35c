package br.com.celk.view.atendimento.prontuario.panel.tuberculose.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomatico;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmacaoSintomatico extends Window{

    private PnlConfirmacaoSintomatico pnlConfirmacaoSintomatico;

    public DlgConfirmacaoSintomatico(String id, TuberculoseSintomatico tuberculoseSintomatico){
        super(id);
        init(tuberculoseSintomatico);
    }

    private void init(TuberculoseSintomatico tuberculoseSintomatico) {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("confirmacaoSintomatico");
            }
        });
                
        setInitialWidth(500);
        setInitialHeight(150);
        setResizable(true);
        
        setContent(pnlConfirmacaoSintomatico = new PnlConfirmacaoSintomatico(getContentId(), tuberculoseSintomatico) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico, Long tipoEntrada) throws ValidacaoException, DAOException {
                close(target);
                DlgConfirmacaoSintomatico.this.onConfirmar(target, tuberculoseSintomatico, tipoEntrada);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico, Long tipoEntrada) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, TuberculoseSintomatico tuberculoseSintomatico){
        show(target);
//        pnlConfirmacaoSintomatico.setObject(target, tuberculoseSintomatico);
    }    
}