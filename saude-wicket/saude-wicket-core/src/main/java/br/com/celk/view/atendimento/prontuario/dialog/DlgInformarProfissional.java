package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarProfissional extends Window{ 

    private PnlInformarProfissional pnlInformarProfissional;
    
    public DlgInformarProfissional(String id) {
        super(id);
        init();
    }

    private void init(){
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("informeProfissional");
            }
        });
        
        setInitialWidth(700);
        setInitialHeight(50);
        
        setResizable(false);
        
        setContent(pnlInformarProfissional = new PnlInformarProfissional(getContentId()) {

            @Override
            public void onOk(AjaxRequestTarget target, Profissional profissional) throws ValidacaoException, DAOException {
                if(profissional != null){
                    close(target);
                    DlgInformarProfissional.this.onOk(target, (Atendimento)DlgInformarProfissional.this.getDefaultModel().getObject(), profissional);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });
        
        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                target.appendJavaScript("$('.token-input-dropdown-celk').css('display', 'none');");
                fechar(target);
                return true;
            }
        });
    }
    
    public abstract void onOk(AjaxRequestTarget target, Atendimento atendimento, Profissional profissional) throws ValidacaoException, DAOException;
    
    public void onFechar(AjaxRequestTarget target) {}
    
    
    public void show(AjaxRequestTarget target, Atendimento atendimento){
        
        pnlInformarProfissional.setEmpresa(atendimento.getEmpresa());
        setDefaultModel(Model.of(atendimento));
        show(target);
        pnlInformarProfissional.limpar(target);
    }
    
    private void fechar(AjaxRequestTarget target){
        DlgInformarProfissional.this.onFechar(target);
        close(target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlInformarProfissional.getFocusComponent();
    }
    
}
