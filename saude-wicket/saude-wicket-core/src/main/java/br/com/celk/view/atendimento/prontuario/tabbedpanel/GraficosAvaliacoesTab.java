package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.historicoclinico.graficosavaliacoes.EvolucaoGlicemiaChart;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.historicoclinico.graficosavaliacoes.EvolucaoImcChart;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.historicoclinico.graficosavaliacoes.EvolucaoPressaoArterialChart;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GraficosAvaliacoesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GraficosAvaliacoesPacienteDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GraficosAvaliacoesSeriesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import ch.lambdaj.group.Group;
import com.googlecode.wickedcharts.highcharts.options.Options;
import com.googlecode.wickedcharts.wicket6.highcharts.Chart;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class GraficosAvaliacoesTab extends TabPanel<NoHistoricoClinicoDTO> {

    private Form<GraficosAvaliacoesDTO> form;
    private Atendimento atendimento;
    private DropDown dropDownTipoGrafico;
    private DropDown dropDownTipoAtendimento;
    private Chart chartAvaliacoes;

    public GraficosAvaliacoesTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        atendimento = object.getAtendimento();
        init();
    }

    public void init() {
        GraficosAvaliacoesDTO proxy = on(GraficosAvaliacoesDTO.class);
        
        getForm().add(dropDownTipoGrafico = DropDownUtil.getIEnumDropDown(path(proxy.getTipoGrafico()), GraficosAvaliacoesDTO.TipoGrafico.values(), false, false));
        dropDownTipoGrafico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                target.add(dropDownTipoGrafico);
                atualizarGrafico(target, true);
            }
        });
        getForm().add(new PnlDatePeriod(path(proxy.getPeriodo())));
        getForm().add(getDropDownTipoAtendimento(path(proxy.getTipoAtendimento())));
        getForm().add(new AbstractAjaxButton("btnGerarGrafico") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                atualizarGrafico(target, false);
            }

        });
        
        getForm().add(chartAvaliacoes = new Chart("chart", gerarGrafico(null)));
        
        add(getForm());
    }
    
    public DropDown getDropDownTipoAtendimento(String id) {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown(id);

            dropDownTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    target.add(dropDownTipoAtendimento);
                    atualizarGrafico(target, false);
                }
            });
        }
        return dropDownTipoAtendimento;
    }
    
    private Form<GraficosAvaliacoesDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<GraficosAvaliacoesDTO>("form", new CompoundPropertyModel<GraficosAvaliacoesDTO>(new GraficosAvaliacoesDTO()));
            this.form.getModel().getObject().setTipoGrafico(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_PRESSAO_ARTERIAL.value());
            this.form.getModel().getObject().setCarregarTipoAtendimento(true);
        }
        return this.form;
    }
    
    private void atualizarGrafico(AjaxRequestTarget target, boolean carregarTipoAtendimento){   
        getForm().getModel().getObject().setCarregarTipoAtendimento(carregarTipoAtendimento);
        chartAvaliacoes.setOptions(gerarGrafico(target));
        target.add(chartAvaliacoes);
    }
    
    private Options gerarGrafico(AjaxRequestTarget target) {
        GraficosAvaliacoesDTO grafico = getForm().getModel().getObject();
        grafico.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        
        List<GraficosAvaliacoesSeriesDTO> dtoSeries = new ArrayList<GraficosAvaliacoesSeriesDTO>();
        GraficosAvaliacoesSeriesDTO dtoPaciente = gerarSeriePaciente(target, grafico);
        
        if(dtoPaciente != null){
            dtoSeries.add(dtoPaciente);
        }
        
        if(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_IMC.value().equals(grafico.getTipoGrafico())){
            EvolucaoImcChart options = new EvolucaoImcChart();
            options.setLineSeries(dtoSeries);

            return options.startChart();
        } else if(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_PRESSAO_ARTERIAL.value().equals(grafico.getTipoGrafico())){
            EvolucaoPressaoArterialChart options = new EvolucaoPressaoArterialChart();
            options.setLineSeries(dtoSeries);

            return options.startChart();
        }
        
        EvolucaoGlicemiaChart options = new EvolucaoGlicemiaChart();
        options.setLineSeries(dtoSeries);

        return options.startChart();
    }
    
    private GraficosAvaliacoesSeriesDTO gerarSeriePaciente(AjaxRequestTarget target, GraficosAvaliacoesDTO grafico) {
        GraficosAvaliacoesSeriesDTO dto = null;
        GraficosAvaliacoesPacienteDTO graficosAvaliacoesPacienteDTO = null;
        
        try{
            graficosAvaliacoesPacienteDTO = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarGraficosAvaliacoesHistorioClinico(grafico);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
        if(graficosAvaliacoesPacienteDTO == null || CollectionUtils.isEmpty(graficosAvaliacoesPacienteDTO.getGlicemia())
                || CollectionUtils.isEmpty(graficosAvaliacoesPacienteDTO.getImc()) || CollectionUtils.isEmpty(graficosAvaliacoesPacienteDTO.getPad())
                || CollectionUtils.isEmpty(graficosAvaliacoesPacienteDTO.getPas())){
            getForm().getModel().getObject().setTipoAtendimento(null);
            if(target != null){
                dropDownTipoAtendimento.removeAllChoices(target);
                MessageUtil.warn(target, this, BundleManager.getString("msgSemDadosGerarGrafico"));
            } else {
                dropDownTipoAtendimento.removeAllChoices();
                warn(BundleManager.getString("msgSemDadosGerarGrafico"));
            }
        } else {
            if(target != null){
                INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(this);
                if(findNotificationPanel != null){
                    getSession().getFeedbackMessages().clear();
                    findNotificationPanel.updateNotificationPanel(target);
                }
            }
            
            if(grafico.isCarregarTipoAtendimento()){
                carregarTipoAtendimento(target, graficosAvaliacoesPacienteDTO.getTipoAtendimentoList());
            }
            
            dto = new GraficosAvaliacoesSeriesDTO();

            if(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_GLICEMIA.value().equals(grafico.getTipoGrafico())){
                dto.setSerieGlicemia(graficosAvaliacoesPacienteDTO.getGlicemia());
            } else if(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_IMC.value().equals(grafico.getTipoGrafico())){
                dto.setSerieImc(graficosAvaliacoesPacienteDTO.getImc());
            } else if(GraficosAvaliacoesDTO.TipoGrafico.EVOLUCAO_PRESSAO_ARTERIAL.value().equals(grafico.getTipoGrafico())){
                dto.setSeriePad(graficosAvaliacoesPacienteDTO.getPad());
                dto.setSeriePas(graficosAvaliacoesPacienteDTO.getPas());
            }
            dto.setDataAtendimento(graficosAvaliacoesPacienteDTO.getDataAvaliacao());
        }
        return dto;
    }
    
    private void carregarTipoAtendimento(AjaxRequestTarget target, List<TipoAtendimento> tipoAtendimentoList){
        getForm().getModel().getObject().setTipoAtendimento(null);
        if(target != null){
            dropDownTipoAtendimento.removeAllChoices(target);
        } else {
            dropDownTipoAtendimento.removeAllChoices();
        }
        
        dropDownTipoAtendimento.addChoice(null, bundle("todos"));
            
        TipoAtendimento on = on(TipoAtendimento.class);
        Collections.sort(tipoAtendimentoList, new Comparator<TipoAtendimento>() {
            @Override
            public int compare(TipoAtendimento o1, TipoAtendimento o2) {
                return o1.getDescricao().compareTo(o2.getDescricao());
            }
        });
        Group<TipoAtendimento> group = Lambda.group(tipoAtendimentoList, Lambda.by(on.getCodigo()));

        if(group != null){
            for (TipoAtendimento tipoAtendimento : group.findAll()) {
                    dropDownTipoAtendimento.addChoice(tipoAtendimento, tipoAtendimento.getDescricao());
            }
        }
        
        if(target != null){
            target.add(dropDownTipoAtendimento);
        }
    }
    
    @Override
    public String getTitle() {
        return bundle("graficos");
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);      
        
        response.render(JavaScriptHeaderItem.forScript(
            "Highcharts.setOptions({"
            +"lang: {"
            +"months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],"
            +"shortMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],"
            +"weekdays: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],"
            +"loading: ['Atualizando o gráfico...aguarde'],"
            +"contextButtonTitle: 'Exportar gráfico',"
            +"decimalPoint: ',',"
            +"thousandsSep: '.',"
            +"downloadJPEG: 'Baixar imagem JPEG',"
            +"downloadPDF: 'Baixar arquivo PDF',"
            +"downloadPNG: 'Baixar imagem PNG',"
            +"downloadSVG: 'Baixar vetor SVG',"
            +"printChart: 'Imprimir gráfico',"
            +"rangeSelectorFrom: 'De',"
            +"rangeSelectorTo: 'Para',"
            +"rangeSelectorZoom: 'Zoom',"
            +"resetZoom: 'Limpar Zoom',"
            +"resetZoomTitle: 'Voltar Zoom para nível 1:1',"
            +"}"
            +"});", "scriptLangHighcharts"));
    }
}
