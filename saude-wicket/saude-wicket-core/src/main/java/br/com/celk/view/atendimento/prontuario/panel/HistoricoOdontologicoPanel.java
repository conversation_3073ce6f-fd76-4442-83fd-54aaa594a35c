package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.odonto.DetalhesHistoricoOdontologicoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.HistoricoOdontologicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.HistoricoOdontologicoDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ResumoSituacaoDenteDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.argument.Argument;
import ch.lambdaj.function.compare.ArgumentComparator;
import ch.lambdaj.group.Group;
import org.apache.commons.collections.ComparatorUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxCheckBox;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.core.AllOf;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class HistoricoOdontologicoPanel extends ProntuarioCadastroPanel {

    // Filtros
    private Long tipoEvolucao;
    private Dente dente;
    private Profissional profissional;
    private boolean meusAtendimentos;

    private Table tblHistorico;
    private Table tblResumoCPOD;
    private List<HistoricoOdontologicoDTO> historicoList = new ArrayList();
    private List<ResumoSituacaoDenteDTO> resumoCPODList = new ArrayList();

    private DropDown<Dente> dropDownDente;
    private DropDown<Profissional> dropDownProfissional;

    public HistoricoOdontologicoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(tblResumoCPOD = new Table("tblResumoCPOD", getResumoCPODColumns(), getResumoCPODCollectionProvider()));
        tblResumoCPOD.populate();

        RadioButtonGroup radioButtonGroup = new RadioButtonGroup("tipoEvolucao");
        radioButtonGroup.add(new AjaxRadio("todos", new Model(null)));
        radioButtonGroup.add(new AjaxRadio("dente", new Model(AtendimentoOdontoPlano.Tipo.DENTE.value())));
        radioButtonGroup.add(new AjaxRadio("sextante", new Model(AtendimentoOdontoPlano.Tipo.SEXTANTE.value())));
        radioButtonGroup.add(new AjaxRadio("arcada", new Model(AtendimentoOdontoPlano.Tipo.ARCADA.value())));
        radioButtonGroup.add(new AjaxRadio("outro", new Model(AtendimentoOdontoPlano.Tipo.OUTRO.value())));

        radioButtonGroup.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                onChangeTipoEvolucao(target);
                tblHistorico.update(target);
            }
        });

        form.add(radioButtonGroup);

        form.add(dropDownDente = new DropDown("dente"));
        dropDownDente.setEnabled(false);
        dropDownDente.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tblHistorico.update(target);
            }
        });

        form.add(dropDownProfissional = new DropDown("profissional"));
        dropDownProfissional.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                tblHistorico.update(target);
            }
        });

        form.add(new AjaxCheckBox("meusAtendimentos") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onChangeMeusAtendimentos(target);
                tblHistorico.update(target);
            }
        });

        form.add(tblHistorico = new Table("tblHistorico", getHistoricoColumns(), getHistoricoCollectionProvider()));
        tblHistorico.populate();

        add(form);
        carregarHistorico();
    }

    private List<IColumn> getHistoricoColumns() {
        List<IColumn> columns = new ArrayList();
        HistoricoOdontologicoDTO proxy = on(HistoricoOdontologicoDTO.class);

        columns.add(getCustomActionColumn());
        columns.add(createColumn(bundle("data"), proxy.getAtendimentoOdontoPlano().getDataCadastro()));
        columns.add(createColumn(bundle("local"), proxy.getAtendimentoOdontoPlano().getDescricaoLocalFormatado()));
        columns.add(createColumn(bundle("tratamento"), proxy.getAtendimentoOdontoPlano().getSituacaoDente().getDescricaoFormatado()));
        columns.add(createColumn(bundle("situacao"), proxy.getAtendimentoOdontoPlano().getDescricaoStatus()));
        columns.add(createColumn(bundle("dataExecucaoAbv"), proxy.getAtendimentoOdontoExecucao().getDataCadastro()));
        columns.add(createColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        columns.add(createColumn(bundle("unidade"), proxy.getUnidade().getDescricao()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<HistoricoOdontologicoDTO>() {
            @Override
            public void customizeColumn(HistoricoOdontologicoDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<HistoricoOdontologicoDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, HistoricoOdontologicoDTO modelObject) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new DetalhesHistoricoOdontologicoPanel(getProntuarioController().panelId(), modelObject));
                    }

                }).setTitleBundleKey("detalhesTratamento");
            }
        };
    }

    private ICollectionProvider getHistoricoCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getFilteredList();
            }
        };
    }

    private List getFilteredList() {
        List<HistoricoOdontologicoDTO> filteredList = new ArrayList();

        if (CollectionUtils.isNotNullEmpty(historicoList)) {
            HistoricoOdontologicoDTO proxy = on(HistoricoOdontologicoDTO.class);

            Collection<Matcher<HistoricoOdontologicoDTO>> filters = new ArrayList();
            if (tipoEvolucao != null) {
                Matcher tipoEvolucaoFilter = Lambda.having(proxy.getAtendimentoOdontoPlano().getTipoEvolucao(), Matchers.equalTo(tipoEvolucao));
                filters.add(tipoEvolucaoFilter);
            }

            if (dente != null) {
                Matcher denteFilter = Lambda.having(proxy.getAtendimentoOdontoPlano().getDente(), Matchers.equalTo(dente));
                filters.add(denteFilter);
            }

            if (profissional != null) {
                Matcher profissionalFilter = Lambda.having(proxy.getProfissional(), Matchers.equalTo(profissional));
                filters.add(profissionalFilter);
            }

            if (meusAtendimentos) {
                Profissional profissional = getAtendimento().getProfissional();
                Matcher meusAtendimentosFilter = Lambda.having(proxy.getProfissional(), Matchers.equalTo(profissional));
                filters.add(meusAtendimentosFilter);
            }

            if (CollectionUtils.isNotNullEmpty(filters)) {
                AllOf<HistoricoOdontologicoDTO> allOf = new AllOf(filters);
                filteredList = Lambda.select(historicoList, allOf);
            } else {
                return historicoList;
            }
        }

        return filteredList;
    }

    private List<IColumn> getResumoCPODColumns() {
        List<IColumn> columns = new ArrayList();

        ResumoSituacaoDenteDTO proxy = on(ResumoSituacaoDenteDTO.class);

        columns.add(createColumn(bundle("cariado"), proxy.getQuantidadeCariado()));
        columns.add(createColumn(bundle("perdido"), proxy.getQuantidadePerdido()));
        columns.add(createColumn(bundle("obturadoRestaurado"), proxy.getQuantidadeObturadoRestaurado()));
        return columns;
    }

    private ICollectionProvider getResumoCPODCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return resumoCPODList;
            }
        };
    }

    private void onChangeTipoEvolucao(AjaxRequestTarget target) {
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao)) {
            dropDownDente.setEnabled(true);
        } else {
            dropDownDente.setEnabled(false);
        }

        dropDownDente.limpar(target);
    }

    private void onChangeMeusAtendimentos(AjaxRequestTarget target) {
        if (meusAtendimentos) {
            dropDownProfissional.setEnabled(false);
        } else {
            dropDownProfissional.setEnabled(true);
        }

        dropDownProfissional.limpar(target);
    }

    private void carregarHistorico() {
        try {
            HistoricoOdontologicoDTOParam param = new HistoricoOdontologicoDTOParam();
            param.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
            historicoList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarHistoricoOdontologico(param);
            if (CollectionUtils.isNotNullEmpty(historicoList)) {
                populateDropDownDente();
                populateDropDownProfissional();
                contabilizarResumoCPOD();
            }
        } catch (DAOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    private void populateDropDownDente() {
        dropDownDente.addChoice(null, "");

        Argument<String> byDente = Lambda.argument(Lambda.on(HistoricoOdontologicoDTO.class).getAtendimentoOdontoPlano().getDente().getNome());
        Collection<HistoricoOdontologicoDTO> distinct = Lambda.selectDistinctArgument(historicoList, byDente);
        if (CollectionUtils.isNotNullEmpty(distinct)) {
            List<HistoricoOdontologicoDTO> sortedList = Lambda.sort(distinct, byDente);
            for (HistoricoOdontologicoDTO historicoOdontologicoDTO : sortedList) {
                Dente dente = historicoOdontologicoDTO.getAtendimentoOdontoPlano().getDente();
                if (dente != null) {
                    dropDownDente.addChoice(dente, dente.getNome());
                }
            }
        }
    }

    private void populateDropDownProfissional() {
        dropDownProfissional.addChoice(null, "");

        Argument<String> byProfissional = Lambda.argument(Lambda.on(HistoricoOdontologicoDTO.class).getProfissional().getNome());
        Collection<HistoricoOdontologicoDTO> distinct = Lambda.selectDistinctArgument(historicoList, byProfissional);
        if (CollectionUtils.isNotNullEmpty(distinct)) {
            List<HistoricoOdontologicoDTO> sortedList = Lambda.sort(distinct, byProfissional);
            for (HistoricoOdontologicoDTO historicoOdontologicoDTO : sortedList) {
                Profissional profissional = historicoOdontologicoDTO.getProfissional();
                if (profissional != null) {
                    dropDownProfissional.addChoice(profissional, profissional.getNome());
                }
            }
        }
    }

    private void contabilizarResumoCPOD() {
        resumoCPODList = new ArrayList();

        HistoricoOdontologicoDTO on = Lambda.on(HistoricoOdontologicoDTO.class);

        int cariados = Lambda.select(historicoList, Lambda.having(on.getAtendimentoOdontoPlano().getSituacaoDente().getCariado(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG))).size();
        int perdidos = Lambda.select(historicoList, Lambda.having(on.getAtendimentoOdontoPlano().getSituacaoDente().getPerdido(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG))).size();
        int obturadosRestaurados = Lambda.select(historicoList, Lambda.having(on.getAtendimentoOdontoPlano().getSituacaoDente().getObturadoRestaurado(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG))).size();

        ResumoSituacaoDenteDTO resumoSituacaoDenteDTO = new ResumoSituacaoDenteDTO();
        resumoSituacaoDenteDTO.setQuantidadeCariado(Long.valueOf(cariados));
        resumoSituacaoDenteDTO.setQuantidadePerdido(Long.valueOf(perdidos));
        resumoSituacaoDenteDTO.setQuantidadeObturadoRestaurado(Long.valueOf(obturadosRestaurados));

        resumoCPODList.add(resumoSituacaoDenteDTO);
    }

}
