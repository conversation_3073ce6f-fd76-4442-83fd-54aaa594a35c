package br.com.celk.component.view;

import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.iterator.ModelIterator;
import java.util.Iterator;

/**
 *
 * <AUTHOR>
 */
public abstract class CollectionView<T> extends AbstractCollectionView<T> {

    public CollectionView(String id, ICollectionProvider collectionProvider) {
        super(id, collectionProvider);
    }

    @Override
    public Iterator getIterator(ICollectionProvider collectionProvider1) {
        return new ModelIterator(collectionProvider1);
    }
    
}
