package br.com.celk.view.atendimento.prontuario.dialog.prenatal;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatalPesoRecemNascido;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlConcluirPreNatal extends Panel {

    private CompoundPropertyModel<PreNatal> model;
    private DateChooser dchDataParto;
    private DateChooserAjax dchDataocorrencia;
    private DropDown dropDownDesfecho;
    private DropDown<Long> dropDownLocalOcorrencia;
    private DropDown<Long> dropDownTipoParto;

    private WebMarkupContainer containerParto;
    private WebMarkupContainer containerPeso;

    private String nome;

    private DoubleField txtPesoRn;
    private InputField txtIdadeGestacional;
    private InputField txtEstabelecimento;
    private DropDown cbxFebre;
    
    private Table tblPeso;
    private Double pesoRN;
    private List<PreNatalPesoRecemNascido> pesoRNList;

    public PnlConcluirPreNatal(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new PreNatal()));
        setOutputMarkupId(true);
        PreNatal proxy = on(PreNatal.class);

        form.add(new DisabledInputField(path(proxy.getUsuarioCadsus().getNomeSocial())));
        form.add(dropDownDesfecho = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getDesfecho()), PreNatal.Desfecho.values()));
        dchDataocorrencia = (DateChooserAjax) new DateChooserAjax(path(proxy.getDataOcorrencia())).setEnabled(false);
        dchDataocorrencia.setEnabled(false);
        dchDataocorrencia.setOutputMarkupId(true);
        dchDataocorrencia.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        form.add(dchDataocorrencia);
        dropDownDesfecho.add(new AjaxFormComponentUpdatingBehavior("change") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableDataParto(target);
            }
        });

        containerParto = new WebMarkupContainer("containerParto");
        containerParto.setEnabled(false);
        containerParto.setOutputMarkupId(true);
        containerParto.add(dchDataParto = new DateChooser(path(proxy.getDataParto())));
        dchDataParto.addRequiredClass();
        dchDataParto.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        dropDownTipoParto = DropDownUtil.getIEnumDropDown(path(proxy.getTipoParto()), PreNatal.TipoParto.values(), true);
        containerParto.add(dropDownTipoParto);

        txtIdadeGestacional = new InputField(path(proxy.getIdadeGestacional()));
        txtIdadeGestacional.add(new Tooltip().setText("idadeGestacional2"));
        containerParto.add(txtIdadeGestacional);

        dropDownLocalOcorrencia = DropDownUtil.getIEnumDropDown(path(proxy.getLocalOcorrencia()), PreNatal.LocalOcorrencia.values(), true);
        containerParto.add(dropDownLocalOcorrencia);
        containerParto.add(txtEstabelecimento = new InputField(path(proxy.getEstabelecimento())));
        txtEstabelecimento.addRequiredClass();

        containerParto.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPresencaAcompanhante()), true, false));
        cbxFebre = DropDownUtil.getSimNaoLongDropDown(path(proxy.getFebre()), true, false);
        cbxFebre.add(new Tooltip().setText("msgTooltipFebre"));
        containerParto.add(cbxFebre);

        containerParto.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getInfeccao()), true, false));
        containerParto.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getHemorragia()), true, false));
        containerParto.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getProblemaMamas()), true, false));
        
        containerPeso = new WebMarkupContainer("containerPeso");
        containerPeso.add(txtPesoRn = new DoubleField("pesoRN", new PropertyModel(this, "pesoRN")).setVMax(9.999D));
        containerPeso.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarPeso(target);
            }
        });
        containerPeso.add(tblPeso = new Table("tblPeso", getColumns(), getCollectionProvider()));
        tblPeso.populate();

        containerParto.add(containerPeso);
        form.add(containerParto);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCadastro(target)) {
                    model.getObject().setDataParto(model.getObject().getDataParto());
                    model.getObject().setDesfecho(model.getObject().getDesfecho());
                    onConfirmar(target, model.getObject(), pesoRNList);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }
    
    private void adicionarPeso(AjaxRequestTarget target) throws ValidacaoException {
        if (pesoRN == null) {
            throw new ValidacaoException(bundle("informePeso"));
        }

        PreNatalPesoRecemNascido pnprn = new PreNatalPesoRecemNascido();
        pnprn.setPeso(pesoRN);
        pnprn.setPreNatal(this.model.getObject());
        pesoRNList.add(pnprn);

        tblPeso.populate();
        tblPeso.update(target);

        limparPeso(target);
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        PreNatalPesoRecemNascido proxy = on(PreNatalPesoRecemNascido.class);

        columns.add(getActionColumn());
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PreNatalPesoRecemNascido>() {
            @Override
            public void customizeColumn(PreNatalPesoRecemNascido rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<PreNatalPesoRecemNascido>() {
                    @Override
                    public void action(AjaxRequestTarget target, PreNatalPesoRecemNascido modelObject) throws ValidacaoException, DAOException {
                        removerPeso(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerPeso(AjaxRequestTarget target, PreNatalPesoRecemNascido preNatalPesoRecemNascido) {
        limparPeso(target);
        for (int i = 0; i < pesoRNList.size(); i++) {
            if (pesoRNList.get(i) == preNatalPesoRecemNascido) {
                pesoRNList.remove(i);
                break;
            }
        }
        tblPeso.update(target);
    }
    
    private void limparPeso(AjaxRequestTarget target) {
        txtPesoRn.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return pesoRNList;
            }
        };
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if (teveParto()) {
                if (model.getObject().getDataParto() == null && (PreNatal.Desfecho.OBITO.value().equals(model.getObject().getDesfecho()) || PreNatal.Desfecho.PARTO.value().equals(model.getObject().getDesfecho()))) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_parto"));
                } else if (model.getObject().getDataParto() != null && Data.adjustRangeHour(model.getObject().getDataParto()).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_data_nao_pode_ser_maior_data_atual"));
                }
                if (model.getObject().getEstabelecimento() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_estabelecimento_obrigatorio"));
                }
                if(CollectionUtils.isEmpty(pesoRNList)){
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pelo_menos_um_peso"));                    
                }
            }
            if (model.getObject().getDataOcorrencia() != null && DataUtil.getDataAtual().before(model.getObject().getDataOcorrencia())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_ocorrencia_maior_atual"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, PreNatal preNatal, List<PreNatalPesoRecemNascido> pesoRNList) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setDTO(AjaxRequestTarget target, PreNatal preNatal) {
        this.model.setObject(preNatal);
        dchDataParto.limpar(target);
        dropDownDesfecho.limpar(target);

        this.model.getObject().setUsuarioCadsus(preNatal.getUsuarioCadsus());
        this.model.getObject().setDataParto(null);
        this.model.getObject().setDesfecho(PreNatal.Desfecho.PARTO.value());

        target.add(dropDownDesfecho);
        pesoRNList = new ArrayList<>();
        limparPeso(target);

        enableDataParto(target);
    }

    private boolean teveParto() {
        return PreNatal.Desfecho.PARTO.value().equals(model.getObject().getDesfecho());
    }

    private void enableDataParto(AjaxRequestTarget target) {
        dchDataocorrencia.limpar(target);
        dchDataParto.limpar(target);
        txtEstabelecimento.limpar(target);
        dropDownLocalOcorrencia.limpar(target);
        dropDownTipoParto.limpar(target);
        pesoRNList.clear();

        if (teveParto()) {
            dchDataParto.addRequiredClass();
            txtEstabelecimento.addRequiredClass();
        } else {
            dchDataParto.removeRequiredClass();
            txtEstabelecimento.removeRequiredClass();
        }
        dchDataocorrencia.setEnabled(!teveParto());
        containerParto.setEnabled(teveParto());
        containerPeso.setEnabled(teveParto());

        target.add(dchDataocorrencia);
        target.add(dchDataParto);
        target.add(txtEstabelecimento);
        target.add(containerParto);
        target.add(dropDownLocalOcorrencia);
        target.add(dropDownTipoParto);
        tblPeso.update(target);
    }
}
