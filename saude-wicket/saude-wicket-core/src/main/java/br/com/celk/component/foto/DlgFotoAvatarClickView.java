package br.com.celk.component.foto;

import br.com.celk.component.foto.interfaces.IFoto;
import br.com.celk.component.window.Window;
import br.com.ksisolucoes.util.Bundle;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.request.resource.IResource;

/**
 * <AUTHOR>
 */
class DlgFotoAvatarClickView extends Window {


    private FotoAvatarClickView pnl;


    DlgFotoAvatarClickView(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(Bundle.getStringApplication("rotulo_vizualizar"));
        setInitialWidth(430);
        setInitialHeight(305);

        setContent(pnl = new FotoAvatarClickView(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) {
                DlgFotoAvatarClickView.this.onFechar(target);
            }
        });

    }

    public void onFechar(AjaxRequestTarget target) {
        DlgFotoAvatarClickView.this.close(target);
    }


    void setImageAvatarResource(AjaxRequestTarget target, IResource imageAvatarResource) {
        pnl.getImagem().setImageResource(imageAvatarResource);
        pnl.getImagem().setOutputMarkupId(true);
        if (target != null) {
            target.add(pnl.getImagem());
        }
    }

    public void setIFoto(IFoto iFoto) {
        pnl.setIFoto(iFoto);
    }

}
