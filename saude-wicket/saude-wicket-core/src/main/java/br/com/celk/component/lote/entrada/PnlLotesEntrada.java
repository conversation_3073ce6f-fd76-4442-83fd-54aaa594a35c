package br.com.celk.component.lote.entrada;

import br.com.celk.component.autocomplete.AutoCompleteFabricante;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.utils.parametros.ParametrosMateriaisUtil;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.fabricante.autocomplete.AutoCompleteConsultaFabricante;
import br.com.celk.view.basico.fabricante.dialog.DlgCadastroFabricante;
import br.com.celk.view.basico.localizacaoestrutura.autocomplete.AutoCompleteConsultaLocalizacaoEstrutura;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlLotesEntrada extends Panel {

    private Table<MovimentoGrupoEstoqueItemDTO> tblLotes;
    private CompoundPropertyModel<MovimentoGrupoEstoqueItemDTO> modelLote;
    private InputField txtProduto;
    private InputField txtLote;
    private DoubleField txtQuantidade;
    private DoubleField txtTotal;
    private DateChooser dchDataValidade;
    private String fabricante;
    boolean isNotaFiscal = false;

    private List<MovimentoGrupoEstoqueItemDTO> lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private List<MovimentoGrupoEstoqueItemDTO> lotesTemp = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
    private Produto produto;
    private AutoCompleteConsultaLocalizacaoEstrutura autoCompleteConsultaLocalizacaoEstrutura;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteFabricante autoCompleteFabricante;
    private String utilizaLocalizacaoEstoque;
    private WebMarkupContainer containerLocalizacao;
    private AutoCompleteConsultaFabricante autoCompleteConsultaFabricante;
    private DlgCadastroFabricante dlgCadastroFabricante;
    private AbstractAjaxLink btnCadFabricante;
    private LocalizacaoEstrutura localizacaoEstrutura;

    public PnlLotesEntrada(String id) {
        super(id);
        init();
    }

    public PnlLotesEntrada(String id, boolean isNotaFiscal) {
        super(id);
        this.isNotaFiscal = isNotaFiscal;
        init();
    }

    public PnlLotesEntrada(String id, IModel<?> model) {
        super(id, model);
        init();
    }

    private void init() {
        Form form = new Form("form", modelLote = new CompoundPropertyModel(new MovimentoGrupoEstoqueItemDTO()));

        form.add(txtProduto = new DisabledInputField("produto", new PropertyModel(this, "produto.descricao")));
        form.add(txtLote = new UpperField(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_GRUPO_ESTOQUE)));
        txtLote.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                carregarLoteExistente(target);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });
        WebMarkupContainer containerLaboratorio = new WebMarkupContainer("containeLaboratorio");
        containerLaboratorio.add(autoCompleteFabricante = new AutoCompleteFabricante("fabricante", new PropertyModel(this, "fabricante")));
        form.add(containerLaboratorio);
        if (!isNotaFiscal) {
            containerLaboratorio.add(new AttributeAppender("style", "display:none"));
        }
        form.add(txtQuantidade = new DoubleField(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_QUANTIDADE)).setMDec(0));
        form.add(dchDataValidade = new DateChooser(VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_DATA_VALIDADE)));

        form.add(autoCompleteConsultaFabricante = new AutoCompleteConsultaFabricante("fabricante"));
        autoCompleteConsultaFabricante.add(new RemoveListener<Fabricante>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Fabricante object) {
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        form.add(btnCadFabricante = new AbstractAjaxLink("btnCadFabricante") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgCadastroFabricante(target);
            }
        });

        containerLocalizacao = new WebMarkupContainer("containerLocalizacao");

        containerLocalizacao.setOutputMarkupId(true);
        form.add(containerLocalizacao);

        containerLocalizacao.add(autoCompleteConsultaLocalizacaoEstrutura = new AutoCompleteConsultaLocalizacaoEstrutura(MovimentoGrupoEstoqueItemDTO.PROP_LOCALIZACAO_ESTRUTURA));
        autoCompleteConsultaLocalizacaoEstrutura.setLocalizacaoEstruturaBase(getLocalizacaoEstrutura());
        autoCompleteConsultaLocalizacaoEstrutura.setExibirApenasVisivelSim(true);

        containerLocalizacao.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(MovimentoGrupoEstoqueItemDTO.PROP_EMPRESA));

        List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, ApplicationSession.get().getSession().getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                .start().getList();

        if(CollectionUtils.isNotNullEmpty(empresaSetorList)){
            List<Long> codigoSetorList = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor().getCodigo());
            codigoSetorList.add(ApplicationSession.get().getSession().getEmpresa().getCodigo());
            autoCompleteConsultaEmpresa.setCodigoEmpresaList(codigoSetorList);
        }

        form.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarLote(target);
            }
        });

        form.add(tblLotes = new Table("tblLotes", getColumns(), getCollectionProvider()));
        tblLotes.populate();

        form.add(txtTotal = new DisabledDoubleField("total", new LoadableDetachableModel<Double>() {

            @Override
            protected Double load() {
                return getQuantidadeTotalInterno();
            }
        }));

        form.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmar(target);
            }
        });

        form.add(new AbstractAjaxButton("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onCancelar(target);
                cancelar(target);
            }
        });

        add(form);

        if(RepositoryComponentDefault.NAO.equals(getUtilizaLocalizacaoEstoque())){
            containerLocalizacao.setVisible(false);
        }
    }

    private void initDlgCadastroFabricante(AjaxRequestTarget target){
        if(dlgCadastroFabricante == null){
            WindowUtil.addModal(target, this, dlgCadastroFabricante = new DlgCadastroFabricante(WindowUtil.newModalId(this)) {
                @Override
                public void onSalvar(AjaxRequestTarget target, Fabricante fabricante) throws ValidacaoException, DAOException {
                    autoCompleteConsultaFabricante.limpar(target);
                    autoCompleteConsultaFabricante.setComponentValue(fabricante);
                    target.add(autoCompleteConsultaFabricante);
                }
            });
        }
        dlgCadastroFabricante.showDlg(target);
    }

    public void confirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotesTemp) {
            MovimentoGrupoEstoqueItemDTO clone = new DefinerPropertiesCloning().define(movimentoGrupoEstoqueItemDTO);
            lotes.add(clone);
        }
        onConfirmar(target);
    }

    public void cancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        lotesTemp = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotes) {
            MovimentoGrupoEstoqueItemDTO clone = new DefinerPropertiesCloning().define(movimentoGrupoEstoqueItemDTO);
            lotesTemp.add(clone);
        }
        autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(MovimentoGrupoEstoqueItemDTO.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("lote"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_GRUPO_ESTOQUE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("validade"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_DATA_VALIDADE)));
        columns.add(new DoubleColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_QUANTIDADE)).setCasasDecimais(0));
        columns.add(columnFactory.createColumn(BundleManager.getString("fabricante"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_FABRICANTE, Fabricante.PROP_DESCRICAO)));

        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            columns.add(columnFactory.createColumn(BundleManager.getString("localizacaoEstrutura"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_MASCARA)));
            columns.add(columnFactory.createColumn(BundleManager.getString("setor"), VOUtils.montarPath(MovimentoGrupoEstoqueItemDTO.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        }

        return columns;
    }

    private void carregarLoteExistente(AjaxRequestTarget target) {
        try {
            autoCompleteConsultaFabricante.setEnabled(true);
            btnCadFabricante.setEnabled(true);
            if (txtLote.getComponentValue() != null) {
                AbstractSessaoAplicacao sessaoAplicacao = br.com.celk.system.session.ApplicationSession.get().getSession();
                EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                        .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, sessaoAplicacao.getCodigoEmpresa()))
                        .start().getVO();
                if (empresaMaterial == null || empresaMaterial.getDeposito() == null || empresaMaterial.getDeposito().getCodigo() == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO)));
                }

                GrupoEstoque loteExistente = LoadManager.getInstance(GrupoEstoque.class)
                        .addProperties(new HQLProperties(GrupoEstoque.class).getProperties())
                        .addProperties(new HQLProperties(Fabricante.class, GrupoEstoque.PROP_FABRICANTE).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_GRUPO), txtLote.getComponentValue()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_ESTOQUE_EMPRESA, EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CODIGO), getProduto().getCodigo()))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(GrupoEstoque.PROP_DATA_VALIDADE)))
                        .setMaxResults(1).start().getVO();

                MovimentoGrupoEstoqueItemDTO loteDaLista = null;
                if(CollectionUtils.isNotNullEmpty(lotesTemp)){
                    loteDaLista = Lambda.selectFirst(lotesTemp, Lambda.having(on(MovimentoGrupoEstoqueItemDTO.class).getGrupoEstoque(), Matchers.equalTo(txtLote.getComponentValue())));
                }

                if (loteExistente != null && loteExistente.getDataValidade() != null) {
                    modelLote.getObject().setDataValidade(loteExistente.getDataValidade());
                    autoCompleteConsultaFabricante.limpar(target);
                    modelLote.getObject().setFabricante(loteExistente.getFabricante());
                    dchDataValidade.setEnabled(false);
                    if(loteExistente.getFabricante() != null){
                        autoCompleteConsultaFabricante.setEnabled(false);
                        btnCadFabricante.setEnabled(false);
                    }

                    habilitarLocalizacaoEstrutura(target);
                } else if (loteDaLista != null && loteDaLista.getDataValidade() != null){
                    modelLote.getObject().setDataValidade(loteDaLista.getDataValidade());
                    autoCompleteConsultaFabricante.limpar(target);
                    modelLote.getObject().setFabricante(loteDaLista.getFabricante());
                    dchDataValidade.setEnabled(false);
                    if(loteDaLista.getFabricante() != null) {
                        autoCompleteConsultaFabricante.setEnabled(false);
                        btnCadFabricante.setEnabled(false);
                    }

                    habilitarLocalizacaoEstrutura(target);
                } else {
                    modelLote.getObject().setDataValidade(null);
                    autoCompleteConsultaFabricante.limpar(target);
                    modelLote.getObject().setFabricante(null);
                    dchDataValidade.setEnabled(true);

                    habilitarLocalizacaoEstrutura(target);
                }
            } else {
                modelLote.getObject().setDataValidade(null);
                autoCompleteConsultaFabricante.limpar(target);
                modelLote.getObject().setFabricante(null);
                dchDataValidade.setEnabled(true);
            }
            target.add(dchDataValidade);
            target.add(autoCompleteConsultaFabricante);
            target.add(btnCadFabricante);
            target.focusComponent(txtQuantidade);
            target.appendJavaScript(JScript.initMasks());
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<MovimentoGrupoEstoqueItemDTO>() {

            @Override
            public Component getComponent(String componentId, final MovimentoGrupoEstoqueItemDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {

                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerLote(target, rowObject);
                    }
                };
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lotesTemp;
            }
        };
    }

    private void adicionarLote(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        MovimentoGrupoEstoqueItemDTO lote = modelLote.getObject();
        if (StringUtils.trimToNull(lote.getGrupoEstoque()) == null) {
            throw new ValidacaoException(BundleManager.getString("informeLote"));
        }
        if (lote.getQuantidade() == 0D) {
            throw new ValidacaoException(BundleManager.getString("informeQuantidade"));
        }
        if (lote.getDataValidade() == null) {
            throw new ValidacaoException(BundleManager.getString("informeValidade"));
        }
        if(fabricante != null){
            lote.setLaboratorioFabricanteGrupoEstoque(fabricante.toUpperCase());
        }
        if (lote.getFabricante() == null) {
            throw new ValidacaoException(BundleManager.getString("informeFabricante"));
        }

        if (lote.getEmpresa() != null && produto != null) {
            boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                    .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), lote.getEmpresa()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                    .exists();

            if (!exists) {
                throw new ValidacaoException(BundleManager.getString("msgProdutoInativoOuNaoRelacionadoNoSetorX", lote.getEmpresa()));
            }
        }

        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque()) && lote.getLocalizacaoEstrutura() == null){
            throw new ValidacaoException(BundleManager.getString("informeLocalizacaoEstrutura"));
        } else if(lote.getLocalizacaoEstrutura() == null){
            lote.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
        }
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotesTemp) {
            if (movimentoGrupoEstoqueItemDTO.getGrupoEstoque().equals(lote.getGrupoEstoque()) && movimentoGrupoEstoqueItemDTO.getLocalizacaoEstrutura().getCodigo().equals(lote.getLocalizacaoEstrutura().getCodigo())
                    && (lote.getEmpresa() != null && movimentoGrupoEstoqueItemDTO.getEmpresa().getCodigo().equals(lote.getEmpresa().getCodigo())
                            || lote.getEmpresa() == null && ApplicationSession.get().getSession().getEmpresa().getCodigo().equals(movimentoGrupoEstoqueItemDTO.getEmpresa().getCodigo()))) {
                throw new ValidacaoException(BundleManager.getString("loteJaAdicionado"));
            }
        }



        if (lote.getDataValidade().before(Data.adjustRangeHour(Data.getDataAtual()).getDataInicial())) {
            exibirConfirmacaoValidade(target);
        } else {
            atualizarLoteAdicionado(target);
        }
    }

    private DlgConfirmacaoSimNao dlgConfirmacao;

    private void exibirConfirmacaoValidade(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), BundleManager.getString("validadeInformadaVencida")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    atualizarLoteAdicionado(target);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.focusComponent(txtLote);
                }
            });
        }
        dlgConfirmacao.show(target);
    }

    private void atualizarLoteAdicionado(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        MovimentoGrupoEstoqueItemDTO lote = modelLote.getObject();

        AbstractSessaoAplicacao sessaoAplicacao = br.com.celk.system.session.ApplicationSession.get().getSession();
        EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO, Deposito.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaMaterial.PROP_CODIGO, sessaoAplicacao.getCodigoEmpresa()))
                .start().getVO();

        lote.setEmpresa(lote.getEmpresa() != null ? lote.getEmpresa() : sessaoAplicacao.<Empresa>getEmpresa());
        lote.setDeposito(empresaMaterial.getDeposito());

        lote.setProduto(produto);
        lotesTemp.add(lote);
        modelLote.setObject(new MovimentoGrupoEstoqueItemDTO());
        tblLotes.update(target);
        limparLote(target);
        target.add(txtTotal);
        target.focusComponent(txtLote);
    }

    private void removerLote(AjaxRequestTarget target, MovimentoGrupoEstoqueItemDTO dto) {
        lotesTemp.remove(dto);
        tblLotes.update(target);
        target.add(txtTotal);
    }

    private void limparLote(AjaxRequestTarget target) {
        txtLote.limpar(target);
        txtQuantidade.limpar(target);
        dchDataValidade.limpar(target);
        autoCompleteConsultaFabricante.limpar(target);
        autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        autoCompleteConsultaEmpresa.limpar(target);
//        autoCompleteFabricante.limpar(target);

    }

    public void limpar(AjaxRequestTarget target) {
        lotes = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        lotesTemp = new ArrayList<MovimentoGrupoEstoqueItemDTO>();
        produto = null;
        txtProduto.limpar(target);
        txtLote.limpar(target);
        txtQuantidade.limpar(target);
        dchDataValidade.limpar(target);
        autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
        autoCompleteConsultaFabricante.limpar(target);
        autoCompleteConsultaEmpresa.limpar(target);
        txtTotal.limpar(target);
        tblLotes.update(target);
    }

    private Double getQuantidadeTotalInterno() {
        Double total = 0D;
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotesTemp) {
            total = new Dinheiro(total).somar(movimentoGrupoEstoqueItemDTO.getQuantidade()).doubleValue();
        }
        return Coalesce.asDouble(total);
    }

    public Double getQuantidadeTotal() {
        Double total = 0D;
        for (MovimentoGrupoEstoqueItemDTO movimentoGrupoEstoqueItemDTO : lotes) {
            total = new Dinheiro(total).somar(movimentoGrupoEstoqueItemDTO.getQuantidade()).doubleValue();
        }
        return Coalesce.asDouble(total);
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setLotes(List<MovimentoGrupoEstoqueItemDTO> lotes) {
        this.lotesTemp = lotes;
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public abstract void onCancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public InputField getTxtLote() {
        return txtLote;
    }

    public List<MovimentoGrupoEstoqueItemDTO> getLotes() {
        return lotes;
    }

    private void habilitarLocalizacaoEstrutura(AjaxRequestTarget target){
        if(target != null){
            autoCompleteConsultaLocalizacaoEstrutura.limpar(target);
            autoCompleteConsultaEmpresa.limpar(target);
        }

        if(RepositoryComponentDefault.SIM.equals(getUtilizaLocalizacaoEstoque())){
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(true);
            autoCompleteConsultaEmpresa.setEnabled(true);
        } else {
            autoCompleteConsultaLocalizacaoEstrutura.setEnabled(false);
            autoCompleteConsultaEmpresa.setEnabled(false);
        }

        if(target != null){
            target.add(autoCompleteConsultaLocalizacaoEstrutura);
            target.add(autoCompleteConsultaEmpresa);
        }
    }

    public void setFabricante(String fabricante) {
        this.fabricante = fabricante;
    }

    public LocalizacaoEstrutura getLocalizacaoEstrutura() {
        return localizacaoEstrutura;
    }

    public void setLocalizacaoEstrutura(LocalizacaoEstrutura localizacaoEstrutura, AjaxRequestTarget target) {
        this.localizacaoEstrutura = localizacaoEstrutura;
        if (autoCompleteConsultaLocalizacaoEstrutura != null) {
            autoCompleteConsultaLocalizacaoEstrutura.setLocalizacaoEstruturaBase(getLocalizacaoEstrutura());
        }
        if (target != null) {
            target.add(autoCompleteConsultaLocalizacaoEstrutura);
        }
    }

    public String getUtilizaLocalizacaoEstoque() {
        if (utilizaLocalizacaoEstoque == null) {
            try {
                utilizaLocalizacaoEstoque = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            } catch (DAOException ex) {
                Logger.getLogger(PnlLotesEntrada.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizaLocalizacaoEstoque;
    }

}
