package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DorCronicaDTO;
import br.com.ksisolucoes.vo.prontuario.basico.IndiceOswestry;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class IndiceOswestryTab extends TabPanel<DorCronicaDTO> {

    public static final String SELECIONE = "selecione";
    private DorCronicaDTO dorCronicaDTO;
    private InputField txtResultado;
    private long count;
    private DropDown cbxItem1;
    private DropDown cbxItem2;
    private DropDown cbxItem3;
    private DropDown cbxItem4;
    private DropDown cbxItem5;
    private DropDown cbxItem6;
    private DropDown cbxItem7;
    private DropDown cbxItem8;
    private DropDown cbxItem9;
    private DropDown cbxItem10;

    public IndiceOswestryTab(String id, DorCronicaDTO object) {
        super(id, object);
        dorCronicaDTO = object;
        init();
    }

    private void init() {
        setDefaultModel(new CompoundPropertyModel(dorCronicaDTO));
        DorCronicaDTO proxy = on(DorCronicaDTO.class);
        add(cbxItem1 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getIntencidadeDor()), IndiceOswestry.IntencidadeDor.values(), true, SELECIONE));
        add(cbxItem2 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getCuidadosPessoais()), IndiceOswestry.CuidadosPessoais.values(), true, SELECIONE));
        add(cbxItem3 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getPesos()), IndiceOswestry.Pesos.values(), true, SELECIONE));
        add(cbxItem4 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getAndar()), IndiceOswestry.Andar.values(), true, SELECIONE));
        add(cbxItem5 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getSentar()), IndiceOswestry.Sentar.values(), true, SELECIONE));
        add(cbxItem6 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getDePe()), IndiceOswestry.DePe.values(), true, SELECIONE));
        add(cbxItem7 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getSono()), IndiceOswestry.Sono.values(), true, SELECIONE));
        add(cbxItem8 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getVidaSexual()), IndiceOswestry.VidaSexual.values(), true, SELECIONE));
        add(cbxItem9 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getVidaSocial()), IndiceOswestry.VidaSexual.values(), true, SELECIONE));
        add(cbxItem10 = DropDownUtil.getIEnumDropDown(path(proxy.getIndiceOswestry().getViagens()), IndiceOswestry.Viagens.values(), true, SELECIONE));

        cbxItem1.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem2.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem3.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem4.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem5.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem6.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem7.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem8.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem9.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });
        cbxItem10.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularResultado(target);
            }
        });

        add(txtResultado = new InputField(path(proxy.getResultado())));
        txtResultado.setOutputMarkupId(true);
        txtResultado.setEnabled(false);
        txtResultado.addAjaxUpdateValue();
    }

    private void calcularResultado(AjaxRequestTarget target) {
        StringBuilder resultado;
        count = 0L;
        long totalIndiceOswestry = 0L;
        if (dorCronicaDTO.getIndiceOswestry().getIntencidadeDor() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getIntencidadeDor();
        }
        if (dorCronicaDTO.getIndiceOswestry().getCuidadosPessoais() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getCuidadosPessoais();
        }
        if (dorCronicaDTO.getIndiceOswestry().getPesos() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getPesos();
        }
        if (dorCronicaDTO.getIndiceOswestry().getAndar() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getAndar();
        }

        if (dorCronicaDTO.getIndiceOswestry().getSentar() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getSentar();
        }
        if (dorCronicaDTO.getIndiceOswestry().getDePe() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getDePe();
        }
        if (dorCronicaDTO.getIndiceOswestry().getSono() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getSono();
        }
        if (dorCronicaDTO.getIndiceOswestry().getVidaSexual() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getVidaSexual();
        }
        if (dorCronicaDTO.getIndiceOswestry().getVidaSocial() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getVidaSocial();
        }
        if (dorCronicaDTO.getIndiceOswestry().getViagens() != null) {
            totalIndiceOswestry += dorCronicaDTO.getIndiceOswestry().getViagens();
        }

        if (!isAnyEmpty()) {
            resultado = new StringBuilder(String.valueOf((totalIndiceOswestry * 100) / 50));
        } else {
            dorCronicaDTO.setTotalIndiceOswestry(Util.isNotEmptyAnyCount(dorCronicaDTO.getIndiceOswestry().getIntencidadeDor(),
                    dorCronicaDTO.getIndiceOswestry().getCuidadosPessoais(),
                    dorCronicaDTO.getIndiceOswestry().getPesos(),
                    dorCronicaDTO.getIndiceOswestry().getAndar(),
                    dorCronicaDTO.getIndiceOswestry().getSentar(),
                    dorCronicaDTO.getIndiceOswestry().getDePe(),
                    dorCronicaDTO.getIndiceOswestry().getSono(),
                    dorCronicaDTO.getIndiceOswestry().getVidaSexual(),
                    dorCronicaDTO.getIndiceOswestry().getVidaSocial(),
                    dorCronicaDTO.getIndiceOswestry().getViagens()));
            resultado = new StringBuilder(String.valueOf((totalIndiceOswestry * 100) / (dorCronicaDTO.getTotalIndiceOswestry() * 5)));
        }
        resultado.append("%");
        dorCronicaDTO.setResultado(resultado.toString());
        if (target != null) {
            target.add(txtResultado);
        }
    }

    private boolean isAnyEmpty() {
        return dorCronicaDTO.getIndiceOswestry() != null && (
                Util.isEmptyAny(dorCronicaDTO.getIndiceOswestry().getIntencidadeDor(),
                        dorCronicaDTO.getIndiceOswestry().getCuidadosPessoais(),
                        dorCronicaDTO.getIndiceOswestry().getPesos(),
                        dorCronicaDTO.getIndiceOswestry().getAndar(),
                        dorCronicaDTO.getIndiceOswestry().getSentar(),
                        dorCronicaDTO.getIndiceOswestry().getDePe(),
                        dorCronicaDTO.getIndiceOswestry().getSono(),
                        dorCronicaDTO.getIndiceOswestry().getVidaSexual(),
                        dorCronicaDTO.getIndiceOswestry().getVidaSocial(),
                        dorCronicaDTO.getIndiceOswestry().getViagens()));

    }

    @Override
    public String getTitle() {
        return bundle("indiceOswestryCapacidade");
    }
}
