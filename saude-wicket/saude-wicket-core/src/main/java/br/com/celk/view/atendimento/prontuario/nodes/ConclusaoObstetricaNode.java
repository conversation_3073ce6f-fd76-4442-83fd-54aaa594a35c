package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.ConclusaoObstetricaPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.CONCLUSAO_OBSTETRICA)
public class ConclusaoObstetricaNode extends ProntuarioNodeImp{

    @Override
    public DefaultProntuarioPanel getPanel(String id) {
        return new ConclusaoObstetricaPanel(id, getTitulo());
    }
    
    @Override
    public Icon32 getIcone() {
        return Icon32.TO_DO_LIST_CHECKED_ALL;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("conclusaoObstetrica");
    }
    
}
