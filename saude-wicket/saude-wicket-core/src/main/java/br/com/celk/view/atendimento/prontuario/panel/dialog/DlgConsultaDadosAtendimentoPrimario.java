package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.panel.DadosConsultaAtendimentoPrimarioPanel;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConsultaDadosAtendimentoPrimario extends Window {

    private DadosConsultaAtendimentoPrimarioPanel dadosConsultaAtendimentoPrimarioPanel ;
    private Atendimento atendimento;

    public DlgConsultaDadosAtendimentoPrimario(String id, Atendimento atendimento){
        super(id);
        this.atendimento = atendimento;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
            @Override
            protected String load(){
                return BundleManager.getString("dadosAtendimentoPrimario");
            }
        });
                
        setInitialWidth(640);
        setInitialHeight(640);
        setResizable(true);
        
        setContent(dadosConsultaAtendimentoPrimarioPanel = new DadosConsultaAtendimentoPrimarioPanel(getContentId(), atendimento) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, AtendimentoPrimario atendimentoPrimario){
        show(target);
        try {
            dadosConsultaAtendimentoPrimarioPanel.setObject(target, atendimentoPrimario);
        } catch (DAOException  | ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

}