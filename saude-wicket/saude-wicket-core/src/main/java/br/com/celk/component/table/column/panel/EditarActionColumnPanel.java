package br.com.celk.component.table.column.panel;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.EditarActionLink;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class EditarActionColumnPanel extends EditarActionLink {

    public EditarActionColumnPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        setAction(new IAction() {

            @Override
            public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onEditar(target);
            }
        });
    }

    public abstract void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
}
