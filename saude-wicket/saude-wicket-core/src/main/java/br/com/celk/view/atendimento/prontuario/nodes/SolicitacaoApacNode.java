package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoApacPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.exameapac.dto.ExameApacDTOParam;
import br.com.ksisolucoes.agendamento.exameapac.dto.ExameRequisicaoApacDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.SOLICITACAO_APAC)
public class SolicitacaoApacNode extends ProntuarioNodeImp{
    
    private Atendimento atendimento;

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        try {
            ExameApacDTOParam param = new  ExameApacDTOParam();
            param.setAtendimento(atendimento);
        
            List<ExameRequisicaoApacDTO> consultaExameApacSolicitado = BOFactoryWicket.getBO(ExameFacade.class).consultaExameRequisicaoApac(param);
            
            boolean possuiSolicitacaoApac = !consultaExameApacSolicitado.isEmpty();
            if (possuiSolicitacaoApac) {
                return new SolicitacaoApacPanel(id);
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return new SolicitacaoApacPanel(id);
    }
    
    @Override
    public Boolean validar(Atendimento atendimento) {
        this.atendimento = atendimento;
        return true;
    }
    
    @Override
    public Icon32 getIcone() {
        return Icon32.LAYOUT_HEADER;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("laudoApac");
    }
    
}