package br.com.celk.view.atendimento.prontuario.panel.solicitacaoexames.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCancelarTesteRapido extends Window{
    
    private PnlCancelarTesteRapido pnlCancelarTesteRapido;
    
    public DlgCancelarTesteRapido(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("cancelarExame");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(300);
        setResizable(true);
        
        setContent(pnlCancelarTesteRapido = new PnlCancelarTesteRapido(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException {
                close(target);
                DlgCancelarTesteRapido.this.onConfirmar(target, testeRapidoRealizado);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
        
    public void show(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado){
        show(target);
        pnlCancelarTesteRapido.limpar(target);
        pnlCancelarTesteRapido.setTesteRapidoRealizado(testeRapidoRealizado);
        
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, TesteRapidoRealizado testeRapidoRealizado) throws ValidacaoException, DAOException;
    
    public void onCancelar(AjaxRequestTarget target) {}
}