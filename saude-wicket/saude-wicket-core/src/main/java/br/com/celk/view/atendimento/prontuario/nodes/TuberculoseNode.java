package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.tuberculose.TuberculosePanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.TUBERCULOSE)
public class TuberculoseNode extends ProntuarioNodeImp{

    @Override
    public DefaultProntuarioPanel getPanel(String id) {
        return new TuberculosePanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.TUBERCULOSE;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("tuberculose");
    }

}
