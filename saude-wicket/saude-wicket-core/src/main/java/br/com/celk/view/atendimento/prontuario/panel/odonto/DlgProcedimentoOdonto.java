package br.com.celk.view.atendimento.prontuario.panel.odonto;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoExecucao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public class DlgProcedimentoOdonto extends Window {

    private PnlProcedimentosOdonto pnlProcedimentosOdonto;

    public DlgProcedimentoOdonto(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("procedimentos", this);
            }
        });

        setInitialWidth(850);
        setInitialHeight(305);
//        setResizable(true);

        setContent(pnlProcedimentosOdonto = new PnlProcedimentosOdonto(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, AtendimentoOdontoExecucao execucao) {
        pnlProcedimentosOdonto.limpar(target);
        pnlProcedimentosOdonto.setAtendimentoOdontoExecucao(execucao);
        show(target);

    }

}
