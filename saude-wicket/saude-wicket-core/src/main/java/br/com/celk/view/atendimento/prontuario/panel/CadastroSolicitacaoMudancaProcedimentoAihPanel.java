package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.solicitacaomudancaprocedimentoDTO.SolicitacaoMudancaProcedimentoDTO;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCid;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCidPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoMudancaProcedimento;
import static ch.lambdaj.Lambda.on;
import java.util.Arrays;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class CadastroSolicitacaoMudancaProcedimentoAihPanel extends ProntuarioCadastroPanel {

    private Form<SolicitacaoMudancaProcedimentoDTO> form;
    private Aih aih;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private String procedimentoAnteriorFormatado;
    private InputArea inputAreaJustificativa;
    private AutoCompleteConsultaCid autoCompleteConsultaCidPrincipal;
    private DlgImpressaoObject<SolicitacaoMudancaProcedimento> dlgConfirmacaoImpressao;
    private AutoCompleteConsultaCid autoCompleteConsultaCidSec;
    private AutoCompleteConsultaCid autoCompleteConsultaCidCausasAssociadas;

    public CadastroSolicitacaoMudancaProcedimentoAihPanel(String id) {
        super(id, bundle("solicitacaoMudancaProcedimentoAih"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        getProcedimentoAnterior();

        form = new Form<SolicitacaoMudancaProcedimentoDTO>("form", new CompoundPropertyModel<SolicitacaoMudancaProcedimentoDTO>(new SolicitacaoMudancaProcedimentoDTO()));

        SolicitacaoMudancaProcedimentoDTO proxy = on(SolicitacaoMudancaProcedimentoDTO.class);

        form.add(new DisabledInputField<String>("procedimentoAnteriorFormatado", new Model<String>(procedimentoAnteriorFormatado)));

        form.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia(path(proxy.getProcedimentoCompetencia())));
        autoCompleteConsultaProcedimentoCompetencia.setLabel(new Model(BundleManager.getString("novoProcedimento",this)));
        autoCompleteConsultaProcedimentoCompetencia.setRequired(true);
        autoCompleteConsultaProcedimentoCompetencia.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.AIH_PRINCIPAL));
        autoCompleteConsultaProcedimentoCompetencia.setProfissional(getAtendimento().getProfissional());
        autoCompleteConsultaProcedimentoCompetencia.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        autoCompleteConsultaProcedimentoCompetencia.add(new ConsultaListener<ProcedimentoCompetencia>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoCompetencia object) {
                if (object != null) {
                    boolean existsProcedCid = LoadManager.getInstance(ProcedimentoCid.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), object)))
                            .exists();

                    if (existsProcedCid) {
                        autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(object);
                        autoCompleteConsultaCidSec.setProcedimentoCompetencia(object);
                        autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(object);
                    } else {
                        autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(null);
                        autoCompleteConsultaCidSec.setProcedimentoCompetencia(null);
                        autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(null);
                    }
                } else {
                    autoCompleteConsultaCidPrincipal.setProcedimentoCompetencia(null);
                    autoCompleteConsultaCidSec.setProcedimentoCompetencia(null);
                    autoCompleteConsultaCidCausasAssociadas.setProcedimentoCompetencia(null);
                }
            }
        });
        form.add(new InputField(path(proxy.getSolicitacaoMudancaProcedimento().getDiagnosticoInicial())));
        form.add(autoCompleteConsultaCidPrincipal = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidPrincipal())));
        autoCompleteConsultaCidPrincipal.setLabel(new Model(BundleManager.getString("cidPrincipal", this)));
        autoCompleteConsultaCidPrincipal.setRequired(true);
        autoCompleteConsultaCidPrincipal.setSituacaoPrincipal(RepositoryComponentDefault.SIM);
        form.add(this.autoCompleteConsultaCidSec = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidSecundario())));
        autoCompleteConsultaCidSec.setSituacaoPrincipal(RepositoryComponentDefault.NAO);
        form.add(autoCompleteConsultaCidCausasAssociadas = new AutoCompleteConsultaCid(path(proxy.getSolicitacaoMudancaProcedimento().getCidCausasAssociadas())));
        form.add(inputAreaJustificativa = new InputArea(path(proxy.getSolicitacaoMudancaProcedimento().getJustificativa())));
        inputAreaJustificativa.setLabel(new Model(BundleManager.getString("justificativaSolicitacao", this)));
        inputAreaJustificativa.setRequired(true);

        form.add(new AbstractAjaxButton("btnSalvarSolicitacao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        add(form);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {

        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setNovoProcedimento(form.getModel().getObject().getProcedimentoCompetencia().getId().getProcedimento());
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setProfissionalSolicitante(getAtendimento().getProfissional());
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setAtendimentoOrigem(getAtendimento());
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setAutorizacaoInternacaoHospitalar(aih);
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setProcedimentoAnterior(getProcedimentoAnterior());
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setDataSolicitacao(DataUtil.getDataAtual());
        form.getModel().getObject().getSolicitacaoMudancaProcedimento().setStatus(SolicitacaoMudancaProcedimento.Status.AGUARDANDO_ANALISE.value());

        SolicitacaoMudancaProcedimento solicitacao = BOFactoryWicket.getBO(AtendimentoFacade.class).salvarSolicitacaoMudancaProcedimento(form.getModelObject(), false);
        form.getModel().getObject().setSolicitacaoMudancaProcedimento(solicitacao);
        DefaultProntuarioPanel panel = new SolicitacaoMudancaProcedimentoAihPanel(getProntuarioController().panelId(), bundle("solicitacaoMudancaProcedimentoAih"));
        getProntuarioController().changePanel(target, panel);

        if (solicitacao != null) {
            imprimir(target);
            dlgConfirmacaoImpressao.show(target, solicitacao);
        }
    }

    private void imprimir(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<SolicitacaoMudancaProcedimento>(getProntuarioController().newWindowId(), bundle("solicitacaoSalvaDesejaImprimir",this)) {
                @Override
                public DataReport getDataReport(SolicitacaoMudancaProcedimento object) throws ReportException {
                    return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirLaudoSolicitacaoMudancaProcedimento(object);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, SolicitacaoMudancaProcedimento object) throws ValidacaoException, DAOException {
                    dlgConfirmacaoImpressao.close(target);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private Procedimento getProcedimentoAnterior() {
        if (aih != null) {
            form.getModelObject().setProcedimentoAnteriorFormatado(aih.getProcedimentoSolicitado());
            return aih.getProcedimentoSolicitado();
        }
        return null;
    }

    private Aih getAih() {
        try {
            ContaPaciente contaPaciente = BOFactory.getBO(HospitalFacade.class).encontrarContaPaciente(getAtendimento().getAtendimentoPrincipal(), false);
        
            Aih aih = null;

            if(contaPaciente != null){
                aih = (Aih) LoadManager.getInstance(Aih.class)
                        .addProperties(new HQLProperties(Aih.class).getProperties())
                        .addProperty(VOUtils.montarPath(Aih.PROP_PROCEDIMENTO_SOLICITADO, Procedimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(Aih.PROP_PROCEDIMENTO_SOLICITADO, Procedimento.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_CONTA_PACIENTE, contaPaciente))
                        .start().getVO();
            }

            if (aih != null) {
                return aih;
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }
}
