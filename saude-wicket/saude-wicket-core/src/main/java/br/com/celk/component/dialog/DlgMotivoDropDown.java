package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoDropDown<T extends Serializable> extends Window {

    private String titulo;
    private T object;
    private PnlMotivoDropDown pnlMotivoDropDown;

    public DlgMotivoDropDown(String id, String titulo) {
        super(id);
        this.titulo = titulo;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(200);

        setResizable(false);

        setTitle(titulo);

        setContent(pnlMotivoDropDown = new PnlMotivoDropDown(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                pnlMotivoDropDown.limpar(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoDropDown.this.onConfirmar(target, motivo, DlgMotivoDropDown.this.object);
            }

            @Override
            public Long getMaxLengthMotivo() {
                return DlgMotivoDropDown.this.getMaxLengthMotivo();
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, T object) throws ValidacaoException, DAOException;

    public Long getMaxLengthMotivo() {
        return 200L;// Tamanho default
    }

    public void setObject(T object) {
        this.object = object;
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.pnlMotivoDropDown.setMessage(target, message);
        if (message != null) {
            setInitialHeight(100);
//            target.add(this);
        }
    }
}
