package br.com.celk.view.prontuario.basico.classificacaocid.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.prontuario.basico.classificacaocid.autocomplete.restricaocontainer.RestricaoContainerClassificacaoCid;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaClassificacaoCidDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaClassificacaoCid extends AutoCompleteConsulta<ClassificacaoCids> {

    private boolean isCarregarFichaInvestigacaoAgravo;

    public AutoCompleteConsultaClassificacaoCid(String id) {
        super(id);
    }

    public AutoCompleteConsultaClassificacaoCid(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaClassificacaoCid(String id, IModel<ClassificacaoCids> model) {
        super(id, model);
    }

    public AutoCompleteConsultaClassificacaoCid(String id, IModel<ClassificacaoCids> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(ClassificacaoCids.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(ClassificacaoCids.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(ClassificacaoCids.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerClassificacaoCid(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<ClassificacaoCids, QueryConsultaClassificacaoCidDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaClassificacaoCidDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarClassificacaoCid(dataPaging);
                    }

                    @Override
                    public QueryConsultaClassificacaoCidDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaClassificacaoCidDTOParam param = new QueryConsultaClassificacaoCidDTOParam();
                        param.setKeyword(searchCriteria);
                        param.setCarregarFichaInvestigacaoAgravo(isCarregarFichaInvestigacaoAgravo);
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaClassificacaoCidDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(ClassificacaoCids.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return ClassificacaoCids.class;
            }

        };
    }

    public AutoCompleteConsultaClassificacaoCid carregarFichaInvestigacaoAgravo( boolean isCarregar){
        this.isCarregarFichaInvestigacaoAgravo = isCarregar;
        return this;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("classificacaoCids");
    }

}