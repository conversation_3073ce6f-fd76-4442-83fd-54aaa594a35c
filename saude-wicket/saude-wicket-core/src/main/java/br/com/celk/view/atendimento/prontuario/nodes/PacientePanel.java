package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.unidadesaude.usuariocadsus.CadastroPacientePanel;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

public class PacientePanel extends ProntuarioCadastroPanel {
    private Panel panel;
    private Atendimento atendimento;

    public PacientePanel(String id, Atendimento atendimento) {
        super(id, BundleManager.getString("paciente"));
        this.atendimento = atendimento;
        init(true);
    }

    public void init(boolean enabled) {
        add(panel = new CadastroPacientePanel("panel", atendimento.getUsuarioCadsus(), enabled, true) {
            @Override
            public void retornaPagina(AjaxRequestTarget target) {
            }

            @Override
            public void onSalvar(AjaxRequestTarget target) {
                target.focusComponent(panel);
                info("Registro salvo com sucesso!");
            }
        });

        panel.setOutputMarkupId(true);
    }

}
