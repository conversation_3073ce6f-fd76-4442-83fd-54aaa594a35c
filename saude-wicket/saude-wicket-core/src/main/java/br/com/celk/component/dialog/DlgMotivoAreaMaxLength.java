package br.com.celk.component.dialog;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoAreaMaxLength<T> extends Window {

    private String titulo;
    private PnlMotivoAreaMaxLength pnlMotivoAreaMaxLength;
    private T object;
    private Long maxLength;
    private String message;

    public DlgMotivoAreaMaxLength(String id, String titulo, Long maxLength) {
        super(id);
        this.titulo = titulo;
        this.maxLength = maxLength;
        init();
    }

    public DlgMotivoAreaMaxLength(String id, String titulo, Long maxLength, String message) {
        super(id);
        this.titulo = titulo;
        this.maxLength = maxLength;
        this.message = message;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        if(message != null){
            setInitialHeight(250);

        } else {
            setInitialHeight(160);
        }
        setResizable(false);

        setTitle(titulo);

        setContent(pnlMotivoAreaMaxLength = new PnlMotivoAreaMaxLength(getContentId(), maxLength, message) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoAreaMaxLength.this.onFechar(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoAreaMaxLength.this.onConfirmar(target, motivo, object);
            }
        });
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlMotivoAreaMaxLength.getTextFieldFocus();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, T object) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target) {
        pnlMotivoAreaMaxLength.limpar(target);
        super.show(target);
    }

    public void setObject(T object) {
        this.object = object;
    }
}