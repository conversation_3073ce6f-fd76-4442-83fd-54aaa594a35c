package br.com.celk.component.action.link;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IModelActionPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class ModelActionLinkPanel extends AbstractActionLinkPanel implements IModelActionPanel{
    
    private IModelAction iColumnAction;

    public ModelActionLinkPanel(String id, ActionType actionType) {
        super(id, actionType);
    }
    
    @Override
    public void setAction(IModelAction iColumnAction) {
        this.iColumnAction = iColumnAction;
    }

    @Override
    public void setModelObject(Serializable modelObject) {
        setDefaultModel(new Model(modelObject));
    }

    @Override
    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if(iColumnAction == null){
            throw new UnsupportedOperationException(BundleManager.getString("acaoNaoImplementada"));
        }
        iColumnAction.action(target, getDefaultModel().getObject());
    }

}
