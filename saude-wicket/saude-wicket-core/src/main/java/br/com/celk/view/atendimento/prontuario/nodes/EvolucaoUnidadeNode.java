package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.EvolucaoUnidadePanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.EVOLUCAO_MEDICO)
public class EvolucaoUnidadeNode extends ProntuarioNodeImp{

    @Override
    public DefaultProntuarioPanel getPanel(String id) {
        return new EvolucaoUnidadePanel(id, getTitulo());
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.REPORT;
    }
    
    @Override
    public String getTitulo() {
        return BundleManager.getString("evolucao");
    }
    
}
