package br.com.celk.view.cadsus.usuariocadsus.foto;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.webcam.DlgCapturarFotoPaciente;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 * <AUTHOR>
 */
public abstract class FotoComponenteAvatarClick extends Panel {

    private boolean isViewOnly;
    private AjaxButton btnCapturarImagem;
    private AbstractAjaxButton btnRemoverAnexo;
    private NonCachingImage imagem;
    private AbstractAjaxButton btnOk;
    private AbstractAjaxButton btnFechar;
    private FotoComponenteCallback callback;
    private UsuarioCadsus usuarioCadsus;
    private DlgCapturarFotoPaciente dlgCapturarFotoPaciente;
    private WebMarkupContainer containerFile;
    private WebMarkupContainer containerAvatar;
    private boolean isImagedModified;

    FotoComponenteAvatarClick(String id, boolean isViewOnly) {
        super(id);
        this.isViewOnly = isViewOnly;
        init();

    }

    private void init() {
        containerAvatar = new WebMarkupContainer("containerAvatar");
        containerAvatar.add(imagem = new NonCachingImage("imgAvatar", ""));
        containerAvatar.setOutputMarkupId(true);
        add(containerAvatar);
        add(callback = new FotoComponenteCallback(){
            @Override
            protected void onResponseCallback(AjaxRequestTarget target, String imagem64) throws ValidacaoException, DAOException {
                imagem.setImageResource(null);
                imagem.add(new AttributeModifier("src", imagem64));
                isImagedModified = true;
                target.add(containerAvatar);

            }
        });
        add(btnOk = new AbstractAjaxButton("btnOk") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(isImagedModified) {
                    if (usuarioCadsus != null && usuarioCadsus.getCodigo() != null) {
                        BOFactory.getBO(UsuarioCadsusFacade.class).salvarFoto(usuarioCadsus, callback.getAnexo());
                    }else {
                        if (callback.getAnexo() != null) {
                            if (callback.getAnexo().isPossuiAnexo()) {
                                GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(callback.getAnexo());
                                usuarioCadsus.setFoto(gerenciadorArquivo);
                            }
                        }
                    }
                }
                onChange(target);
                onFechar(target);
            }
        });
        btnOk.setDefaultFormProcessing(false);

        add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onChange(target);
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);
        add(dlgCapturarFotoPaciente = new DlgCapturarFotoPaciente("dlgCapturarFotoPaciente") {

            @Override
            public void onConfirmar(AjaxRequestTarget target, MensagemAnexoDTO dto, String imagemString) {
                callback.setAnexo(dto);
                imagem.add(new AttributeModifier("src", new Model<>(imagemString)));
                isImagedModified = true;
                onChange(target);
            }
        });
        btnCapturarImagem = new AbstractAjaxButton("btnCapturarImagem") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgCapturarFotoPaciente.show(target);
            }
        };
        btnCapturarImagem.setDefaultFormProcessing(false);

        btnRemoverAnexo = new AbstractAjaxButton("btnRemoverAnexo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                imagem.add(new AttributeModifier("src", new Model<>(ImagemAvatarHelper.carregarAvatarBase64(usuarioCadsus))));
                callback.setAnexo(null);
                isImagedModified = true;
                if(getUsuarioCadsus() !=null){
                    getUsuarioCadsus().setFoto(null);
                }
                target.add(imagem);
                onChange(target);

            }
        };
        btnRemoverAnexo.setDefaultFormProcessing(false);
        containerFile = new WebMarkupContainer("containerFile");

        if(isViewOnly){
            containerAvatar.add(new AttributeModifier("style", ""));
            btnRemoverAnexo.add(new AttributeModifier("style", "display: none"));
            btnCapturarImagem.add(new AttributeModifier("style", "display: none"));
            containerFile.add(new AttributeModifier("style", "display: none"));
            imagem.add(new AttributeModifier("style", "border: 1px solid #C6D880; border-radius: 4px; max-width:800px; max-height:600px; position:relative"));
            isViewOnly = false;
        }

        add(containerFile);
        add(btnRemoverAnexo);
        add(btnCapturarImagem);


    }

    protected abstract void onChange(AjaxRequestTarget target);

    public NonCachingImage getImagem() {
        return imagem;
    }

    public abstract void onFechar(AjaxRequestTarget target);

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        imagem.setImageResource(ImagemAvatarHelper.carregarAvatarResource(usuarioCadsus));
    }
    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }
}
