package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoDesativacao extends Panel {

    private WebMarkupContainer notificationMessage;
    private String message;

    private AbstractAjaxButton btnFechar;
    private InputField<String> txtMotivo;
    private DateChooser txtDataDesativacao;


    private String motivo;
    private Date dataDesativacao;

    public PnlMotivoDesativacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);


        add(notificationMessage = new WebMarkupContainer("notificationMessage"));
        notificationMessage.setOutputMarkupId(true);
        notificationMessage.setVisible(false);
        notificationMessage.add(new Label("message", new PropertyModel(this, "message")));

        Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(txtDataDesativacao = new DateChooser("dataDesativacao", new PropertyModel(this, "dataDesativacao")));
        txtDataDesativacao.getData().setMinDate(new DateOption(new Date()));
        form.add(txtMotivo = new RequiredInputField<String>("motivo"));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, motivo, dataDesativacao);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
                limpar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);

        txtMotivo.add(new AttributeModifier("maxlength", getMaxLengthMotivo()));
    }

    public abstract Long getMaxLengthMotivo();

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, Date dataDesativacao) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txtDataDesativacao.limpar(target);
        this.txtMotivo.limpar(target);
        target.focusComponent(txtMotivo);
        notificationMessage.setVisible(false);
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        notificationMessage.setVisible(true);
        target.add(notificationMessage);
    }
}
