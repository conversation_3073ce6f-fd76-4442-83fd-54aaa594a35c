package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR> Leonardo
 */
public class AutoCompleteLancamentoAtividadeVigilanciaTipoAtividade extends AutoComplete<String> {

    private List<String> tiposAtividades = new ArrayList<String>();

    public AutoCompleteLancamentoAtividadeVigilanciaTipoAtividade(String id, IModel object) {
        super(id, object);
        init();
    }

    public AutoCompleteLancamentoAtividadeVigilanciaTipoAtividade(String id) {
        super(id);
        init();
    }
    
    private void init() {
        getAutoCompleteSettings().setThrottleDelay(600);
    }
    
    @Override
    protected Iterator<String> getChoices(String input) {
        tiposAtividades.clear();
        if (StringUtils.trimToNull(input) != null) {
            tiposAtividades = LoadManager.getInstance(LancamentoAtividadesVigilancia.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(LancamentoAtividadesVigilancia.PROP_TIPO_ATIVIDADE, QueryCustom.QueryCustomGroup.DISTINCT))
                    .addParameter(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_TIPO_ATIVIDADE, BuilderQueryCustom.QueryParameter.ILIKE, input))
                    .setMaxResults(10)
                    .start().getList();
        }
        return tiposAtividades.iterator();
    }

    @Override
    public String getTextValue(String object) {
        return object;
    }

}
