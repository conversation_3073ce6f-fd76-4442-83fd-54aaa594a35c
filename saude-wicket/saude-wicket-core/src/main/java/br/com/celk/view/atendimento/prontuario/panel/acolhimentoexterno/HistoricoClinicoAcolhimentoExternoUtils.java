package br.com.celk.view.atendimento.prontuario.panel.acolhimentoexterno;

import br.com.celk.atendimento.prontuario.builder.HistoricoClinicoBuilder;
import br.com.celk.atendimento.prontuario.builder.HistoricoClinicoItemBuilder;
import br.com.celk.atendimento.prontuario.interfaces.dto.HistoricoClinicoDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.HistoricoClinicoItemDTO;
import br.com.celk.atendimento.prontuario.interfaces.dto.ProntuariosDemmandPaggingDTOParam;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.AcolhimentoServicoExterno;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.ArrayList;
import java.util.List;

public class HistoricoClinicoAcolhimentoExternoUtils {

    private HistoricoClinicoAcolhimentoExternoUtils() {}

    public static List<HistoricoClinicoDTO> getHistoricosClinicos(ProntuariosDemmandPaggingDTOParam dtoParam) throws ValidacaoException, DAOException {
        // BUSCA TESTES RÁPIDOS
        List<AcolhimentoServicoExterno> acolhimentoServicoExternoList = BOFactoryWicket.getBO(AtendimentoFacade.class).getHistoricosClinicosFromAcolhimentoServicoExterno(dtoParam);

        return HistoricoClinicoAcolhimentoExternoUtils.criaListagemHistoricosClinicos(acolhimentoServicoExternoList);
    }

    public static List<HistoricoClinicoDTO> criaListagemHistoricosClinicos(List<AcolhimentoServicoExterno> acolhimentoServicoExternoList) {
        List<HistoricoClinicoDTO> historicosClinicosDTO = new ArrayList<>();

        for (AcolhimentoServicoExterno acolhimentoServicoExterno : acolhimentoServicoExternoList) {

            // CRIA HISTÓRICO CLINICO DTO
            HistoricoClinicoDTO historicoClinicoDTO = new HistoricoClinicoBuilder().builder()
                    .setDataAtentimentoFormatada(DataUtil.getFormatarDiaMesAno(acolhimentoServicoExterno.getDataCadastro()))
                    .setDescricaoEmpresa(acolhimentoServicoExterno.getEmpresa().getDescricao())
                    .setTipoAtendimento("Acolhimento serviço externo")
                    .setHistoricoClinicoItensDTO(new ArrayList<>())
                    .build();

            // CRIA HISTÓRICO CLINICO ITEM DTO
            HistoricoClinicoItemDTO historicoClinicoItemDTO = new HistoricoClinicoItemBuilder().builder()
                    .setDataAtentimentoFormatada(DataUtil.getFormatarDiaMesAno(acolhimentoServicoExterno.getDataCadastro()))
                    .setDescricaoProfissional(getDescricaoProfissional(acolhimentoServicoExterno.getUsuario()))
                    .setDescricaoTipoRegistro("Acolhimento serviço externo")
                    .setDescricaoHistorico(acolhimentoServicoExterno.getDadoAtendimento())
                    .build();
            historicoClinicoDTO.getHistoricoClinicoItensDTO().add(historicoClinicoItemDTO);

            historicosClinicosDTO.add(historicoClinicoDTO);
        }

        return  historicosClinicosDTO;
    }

    private static String getDescricaoProfissional(Usuario usuario) {
        if (usuario.getProfissional() != null && usuario.getProfissional().getNome() != null && !usuario.getProfissional().getNome().isEmpty()) {
            return usuario.getProfissional().getNome();
        }

        return usuario.getNome();
    }
}
