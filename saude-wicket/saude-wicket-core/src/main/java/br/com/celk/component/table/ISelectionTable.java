package br.com.celk.component.table;

import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public interface ISelectionTable<T extends Serializable, E> {

    public void onSelection(T object, E row);
    
    public void onSelection(AjaxRequestTarget target, T object, E row);
    
    public T getSelectedObject();
    
    public void clearSelection();
    
    public void clearSelection(AjaxRequestTarget target);

}
