package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ActionAtendimentoWebDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
@Deprecated
public abstract class DlgAcoesAtendimentoOld extends Window {

    private PnlAcoesAtendimentoOld pnlAcoesAtendimentoOld;

    public DlgAcoesAtendimentoOld(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("atendimento");
            }
        });

        setInitialWidth(900);
        setInitialHeight(300);
        setResizable(true);

        setContent(pnlAcoesAtendimentoOld = new PnlAcoesAtendimentoOld(getContentId()) {

            @Override
            public void onAtender(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesAtendimentoOld.this.onAtender(target, dto);
            }

            @Override
            public void onEvoluir(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesAtendimentoOld.this.onEvoluir(target, dto);
            }

            @Override
            public void onCancelarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesAtendimentoOld.this.onCancelarAtendimento(target, dto);
            }

            @Override
            public void onLiberarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgAcoesAtendimentoOld.this.onLiberarAtendimento(target, dto);
            }

            @Override
            public void onVoltar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                voltar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                voltar(target);
                return true;
            }
        });
    }

    private void voltar(AjaxRequestTarget target) {
        close(target);
        DlgAcoesAtendimentoOld.this.onVoltar(target);
    }

    public abstract void onAtender(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onEvoluir(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onCancelarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onLiberarAtendimento(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) throws ValidacaoException, DAOException;

    public abstract void onVoltar(AjaxRequestTarget target);

    public void show(AjaxRequestTarget target, ActionAtendimentoWebDTO dto) {
        show(target);
        pnlAcoesAtendimentoOld.setDTO(target, dto);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.initMasks()));
        response.render(OnLoadHeaderItem.forScript(JScript.initTextAreaLimit()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeAutoCompleteDrop()));
        response.render(OnLoadHeaderItem.forScript(JScript.removeEnterSubmitFromForm()));
    }

}
