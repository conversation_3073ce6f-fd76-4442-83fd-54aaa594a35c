package br.com.celk.view.atendimento.prontuario.panel.laudotfd;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dialog.DlgJustificativaObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.LaudoTfdPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTO;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirLaudoTfdCompletoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.model.CompoundPropertyModel;
import static ch.lambdaj.Lambda.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class LaudoTfdCadastroPanel extends ProntuarioCadastroPanel {

    private Form<LaudoTfdDTO> form;
    private Form<ProcedimentoSolicitadoTfd> formProcedimentoTratamentoSolicitado;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimentoSolicitado;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private InputArea txaObservacaoUrgencia;
    private InputField txaObservacao;
    private Table tblProcedimentos;
    private DlgImpressaoObject<LaudoTfdDTO> dlgConfirmacaoImpressao;
    private WebMarkupContainer containerDescricaoUrgencia;
    private Profissional profissional;
    private Empresa empresa;
    private DlgJustificativaObject dlgJustificativaObject;
    private DlgConfirmacaoOk dlgConfirmacaoOk1;

    public LaudoTfdCadastroPanel(String id) {
        super(id, bundle("laudoTfd"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        empresa = getAtendimento().getEmpresa();
        profissional = getAtendimento().getProfissional();
        LaudoTfdDTO laudoProxy = on(LaudoTfdDTO.class);

        getForm().add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(laudoProxy.getLaudoTfd().getProfissional()), true)
                .setPeriodoEmpresa(true)
                .setCodigoEmpresa(empresa.getCodigo())
                .setLabel(new Model(bundle("profissional"))));
        if (getForm().getModel().getObject().getLaudoTfd().getProfissional() == null) {
            getForm().getModel().getObject().getLaudoTfd().setProfissional(profissional);
        }

        getForm().add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento(path(laudoProxy.getLaudoTfd().getTipoProcedimento()), true).setTfd(true));
        getForm().add(DropDownUtil.getIEnumDropDown(path(laudoProxy.getLaudoTfd().getCaraterAtendimento()), LaudoTfd.CaraterAtendimento.values(), false, true));
        getForm().add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(path(laudoProxy.getLaudoTfd().getProcedimento()), true));
        getForm().add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(laudoProxy.getLaudoTfd().getCid()), true));
        getForm().add(containerDescricaoUrgencia = new WebMarkupContainer("containerDescricaoUrgencia"));
        containerDescricaoUrgencia.setOutputMarkupId(true);
        containerDescricaoUrgencia.add(getDropDownUrgente(path(laudoProxy.getLaudoTfd().getFlagUrgente())));
        containerDescricaoUrgencia.add(txaObservacaoUrgencia = new InputArea(path(laudoProxy.getLaudoTfd().getObservacaoUrgente())));
        getForm().add(new RequiredInputArea(path(laudoProxy.getLaudoTfd().getHistoricoDoenca())));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getExameFisico())));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getDiagnosticoProvavel())));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getExameComplementarRealizado())));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getTratamentoRealizado())));

        formProcedimentoTratamentoSolicitado = new Form("formProcedimentoTratamentoSolicitado", new CompoundPropertyModel<ProcedimentoSolicitadoTfd>(new ProcedimentoSolicitadoTfd()));

        ProcedimentoSolicitadoTfd procedimentoProxy = on(ProcedimentoSolicitadoTfd.class);

        formProcedimentoTratamentoSolicitado.add(autoCompleteConsultaProcedimentoSolicitado = new AutoCompleteConsultaProcedimento(path(procedimentoProxy.getProcedimento())));
        formProcedimentoTratamentoSolicitado.add(txaObservacao = new InputField(path(procedimentoProxy.getObservacao())));

        formProcedimentoTratamentoSolicitado.add(new AbstractAjaxButton("btnAdicionarProcedimento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProcedimentoSolicitado(target);
            }
        });

        formProcedimentoTratamentoSolicitado.add(new AbstractAjaxButton("btnNovoProcedimento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                novoProcedimentoSolicitado(target);
            }
        }.setDefaultFormProcessing(false));

        formProcedimentoTratamentoSolicitado.add(tblProcedimentos = new Table("tblProcedimentos", getColumns(), getCollectionProvider()));
        tblProcedimentos.populate();

        getForm().add(formProcedimentoTratamentoSolicitado);

        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getJustificativaTfd())));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getJustificativaAcompanhante())));
        getForm().add(DropDownUtil.getIEnumDropDown(path(laudoProxy.getLaudoTfd().getTransporteRecomendavel()), LaudoTfd.TransporteRecomendavel.values()));
        getForm().add(new InputArea(path(laudoProxy.getLaudoTfd().getJustificativaTransporte())));

        getForm().add(new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new LaudoTfdPanel(getProntuarioController().panelId()));
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnConcluirLaudo") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                LaudoTfdDTO modelObject = (LaudoTfdDTO) getForm().getModelObject();
                validarRegra(target, modelObject);
            }

            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                if (Encaminhamento.SIM.equals(LaudoTfdCadastroPanel.this.getForm().getModelObject().getLaudoTfd().getFlagUrgente())) {
                    target.appendJavaScript(JScript.toggleFieldset(containerDescricaoUrgencia));
                }
            }
        });

        autoCompleteConsultaTipoProcedimento.add(new ConsultaListener<TipoProcedimento>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                avaliarTipoProcedimento(target, object);
            }
        });

        autoCompleteConsultaTipoProcedimento.add(new RemoveListener<TipoProcedimento>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoProcedimento object) {
                avaliarTipoProcedimento(target, object);
            }
        });

        if (getForm().getModelObject().getLaudoTfd() != null) {
            txaObservacaoUrgencia.setEnabled(Encaminhamento.SIM.equals(getForm().getModelObject().getLaudoTfd().getFlagUrgente()));
            avaliarTipoProcedimento(null, getForm().getModelObject().getLaudoTfd().getTipoProcedimento());
        }

        add(form);
    }

    private DropDown getDropDownUrgente(String id) {
        DropDown<Long> cbxUrgente = new DropDown(id);

        cbxUrgente.addChoice(Encaminhamento.NAO, bundle("nao"));
        cbxUrgente.addChoice(Encaminhamento.SIM, bundle("sim"));

        cbxUrgente.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enable = getForm().getModelObject().getLaudoTfd().getFlagUrgente().equals(Encaminhamento.SIM);
                txaObservacaoUrgencia.setEnabled(enable);

                if (!enable) {
                    txaObservacaoUrgencia.limpar(target);
                } else {
                    target.add(txaObservacaoUrgencia);
                }
                JScript.toggleFieldset(target, containerDescricaoUrgencia);
            }
        });

        return cbxUrgente;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ProcedimentoSolicitadoTfd proxy = on(ProcedimentoSolicitadoTfd.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("procedimento", this), proxy.getProcedimento().getDescricao()));
        columns.add(createColumn(bundle("observacao", this), proxy.getObservacao()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ProcedimentoSolicitadoTfd>() {

            @Override
            public void customizeColumn(ProcedimentoSolicitadoTfd rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ProcedimentoSolicitadoTfd>() {

                    @Override
                    public void action(AjaxRequestTarget target, ProcedimentoSolicitadoTfd modelObject) throws ValidacaoException, DAOException {
                        editarProcedimentoSolicitado(target, modelObject);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ProcedimentoSolicitadoTfd>() {

                    @Override
                    public void action(AjaxRequestTarget target, ProcedimentoSolicitadoTfd modelObject) throws ValidacaoException, DAOException {
                        removerProcedimentoSolicitado(target, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModelObject().getProcedimentoSolicitadoTfdList();
            }
        };
    }

    private void avaliarTipoProcedimento(AjaxRequestTarget target, TipoProcedimento tipoProcedimento) {
        if (tipoProcedimento != null) {
            if (tipoProcedimento.getProcedimento() != null) {
                if (target != null) {
                    autoCompleteConsultaProcedimento.limpar(target);
                }
                autoCompleteConsultaProcedimento.setModelObject(tipoProcedimento.getProcedimento());
            } else if (target != null) {
                target.add(autoCompleteConsultaProcedimento);
            }

            if (tipoProcedimento.getTipoProcedimentoClassificacao() != null) {
                autoCompleteConsultaProcedimento.setEnabled(TipoProcedimentoClassificacao.EXAMES.equals(tipoProcedimento.getTipoProcedimentoClassificacao().getCodigo()));
            } else {
                autoCompleteConsultaProcedimento.setEnabled(false);
            }

            if (target != null) {
                AgendaGradeAtendimentoHorario agah = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                        .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                        .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, tipoProcedimento))
                        .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .setMaxResults(1)
                        .start().getVO();
                if (agah != null) {
                    if (AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agah.getStatus())) {
                        MessageUtil.warn(target, this, Bundle.getStringApplication("msgPacienteNaoCompareceuUltimoAgendamentoRealizadoParaEsteTipoDataAgendamentoXLocalX",
                                agah.getDataHoraAgendamentoFormatado(), agah.getLocalAgendamento().getDescricao()));
                    }
                }
            }
        } else {
            if (target != null) {
                autoCompleteConsultaProcedimento.limpar(target);
            }
            autoCompleteConsultaProcedimento.setEnabled(false);
        }
    }

    private void adicionarProcedimentoSolicitado(AjaxRequestTarget target) throws ValidacaoException {
        ProcedimentoSolicitadoTfd modelObject = formProcedimentoTratamentoSolicitado.getModelObject();

        if (modelObject.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento", this));
        }

        boolean found = false;
        List<ProcedimentoSolicitadoTfd> procedimentoSolicitadoTfdList = getForm().getModelObject().getProcedimentoSolicitadoTfdList();
        for (int i = 0; i < procedimentoSolicitadoTfdList.size(); i++) {
            if (procedimentoSolicitadoTfdList.get(i) == modelObject) {
                found = true;
                break;
            }
        }

        if (!found) {
            getForm().getModelObject().getProcedimentoSolicitadoTfdList().add(modelObject);
        }
        limparProcedimentoSolicitado(target);
        tblProcedimentos.update(target);
    }

    private void editarProcedimentoSolicitado(AjaxRequestTarget target, ProcedimentoSolicitadoTfd object) {
        limparProcedimentoSolicitado(target);
        formProcedimentoTratamentoSolicitado.setModelObject(object);
    }

    private void removerProcedimentoSolicitado(AjaxRequestTarget target, ProcedimentoSolicitadoTfd modelObject) {
        for (int i = 0; i < getForm().getModelObject().getProcedimentoSolicitadoTfdList().size(); i++) {
            if (getForm().getModelObject().getProcedimentoSolicitadoTfdList().get(i) == modelObject) {
                getForm().getModelObject().getProcedimentoSolicitadoTfdList().remove(i);
                break;
            }
        }
        tblProcedimentos.update(target);
    }

    private void limparProcedimentoSolicitado(AjaxRequestTarget target) {
        formProcedimentoTratamentoSolicitado.setModelObject(new ProcedimentoSolicitadoTfd());
        autoCompleteConsultaProcedimentoSolicitado.limpar(target);
        txaObservacao.limpar(target);
        autoCompleteConsultaProcedimentoSolicitado.focus(target);
    }

    private void novoProcedimentoSolicitado(AjaxRequestTarget target) {
        formProcedimentoTratamentoSolicitado.setModelObject(new ProcedimentoSolicitadoTfd());

        tblProcedimentos.update(target);
        autoCompleteConsultaProcedimentoSolicitado.limpar(target);
        txaObservacao.limpar(target);
        autoCompleteConsultaProcedimentoSolicitado.focus(target);
    }

    private void salvarLaudo(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        LaudoTfdDTO modelObject = getForm().getModelObject();

        modelObject.getLaudoTfd().setEmpresa(empresa);
        modelObject.getLaudoTfd().setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        modelObject.getLaudoTfd().setAtendimento(getAtendimento());

        if (modelObject.getLaudoTfd().getDataLaudo() == null) {
            modelObject.getLaudoTfd().setDataLaudo(DataUtil.getDataAtual());
        }

        modelObject = BOFactoryWicket.getBO(TfdFacade.class).registroTfdAtendimento(modelObject);
        getProntuarioController().addWindow(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<LaudoTfdDTO>(getProntuarioController().newWindowId(), bundle("desejaImprimirLaudoTfd", this)) {
            @Override
            public DataReport getDataReport(LaudoTfdDTO object) throws ReportException {
                RelatorioImprimirLaudoTfdCompletoDTOParam param = new RelatorioImprimirLaudoTfdCompletoDTOParam();
                param.setLaudoTfd(object.getLaudoTfd());
                return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoLaudoTfdCompleto(param);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, LaudoTfdDTO object) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new LaudoTfdPanel(getProntuarioController().panelId()));
            }
        });

        dlgConfirmacaoImpressao.show(target, modelObject);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
        if (Encaminhamento.SIM.equals(getForm().getModelObject().getLaudoTfd().getFlagUrgente())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerDescricaoUrgencia)));
        }
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaTipoProcedimento.getTxtDescricao().getTextField())));
    }

    public void setDto(LaudoTfdDTO dto) {
        getForm().setModelObject(dto);
    }

    private Form<LaudoTfdDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<LaudoTfdDTO>("form", new CompoundPropertyModel<LaudoTfdDTO>(new LaudoTfdDTO()));
            this.form.getModelObject().setLaudoTfd(new LaudoTfd());
            this.form.getModelObject().setProcedimentoSolicitadoTfdList(new ArrayList<ProcedimentoSolicitadoTfd>());
        }

        return this.form;
    }

    public void validarRegra(AjaxRequestTarget target, LaudoTfdDTO dto) throws ValidacaoException, DAOException {
        LaudoTfd lt = AgendamentoHelper.validarTipoRegra(dto.getLaudoTfd().getTipoProcedimento(), getAtendimento().getUsuarioCadsus());
        if (lt != null) {
            if (TipoProcedimento.TipoRegra.EMITIR_AVISO.value().equals(lt.getTipoProcedimento().getTipoRegra())) {
                if (dlgConfirmacaoOk1 == null) {
                    dlgConfirmacaoOk1 = new DlgConfirmacaoOk(getProntuarioController().newWindowId()) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            salvarLaudo(target);
                        }
                    };
                }
                getProntuarioController().addWindow(target, dlgConfirmacaoOk1);
                Long quantidadeDias = lt.getTipoProcedimento().getQuantidadeDias();
                dlgConfirmacaoOk1.setMessage(target, bundle("msgTempoLimiteProcedimentoDias", quantidadeDias));
                dlgConfirmacaoOk1.show(target);
            } else if (TipoProcedimento.TipoRegra.EXIGIR_JUSTIFICATIVA.value().equals(lt.getTipoProcedimento().getTipoRegra())) {
                if (dlgJustificativaObject == null) {
                    dlgJustificativaObject = new DlgJustificativaObject(getProntuarioController().newWindowId(), bundle("justificativa")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, String motivo, Serializable object) throws ValidacaoException, DAOException {
                            getForm().getModelObject().getLaudoTfd().setJustificativaLimiteDias(motivo);
                            salvarLaudo(target);
                        }
                    };
                }
                getProntuarioController().addWindow(target, dlgJustificativaObject);
                dlgJustificativaObject.show(target);
            } else if (TipoProcedimento.TipoRegra.NEGAR.value().equals(lt.getTipoProcedimento().getTipoRegra())) {
                Long quantidadeDias = lt.getTipoProcedimento().getQuantidadeDias();
                throw new ValidacaoException(bundle("msgTempoLimiteProcedimentoDias", quantidadeDias));
            }
        } else {
            salvarLaudo(target);
        }
    }

}
