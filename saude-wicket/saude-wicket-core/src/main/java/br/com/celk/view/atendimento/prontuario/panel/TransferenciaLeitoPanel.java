package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.behavior.interfaces.ILoadListener;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.TransferenciaLeitoPanelDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoTransferenciaLeito;
import br.com.ksisolucoes.vo.prontuario.hospital.ConvenioQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import static ch.lambdaj.Lambda.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.RadioGroup;
import static br.com.celk.system.methods.WicketMethods.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;

/**
 *
 * <AUTHOR>
 */
public class TransferenciaLeitoPanel extends ProntuarioCadastroPanel {

    private Form<TransferenciaLeitoPanelDTO> form;
    private DropDown<Empresa> dropDownEmpresa;
    private SimpleSelectionTable<LeitoQuarto> tblLeitoQuarto;

    public TransferenciaLeitoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        TransferenciaLeitoPanelDTO on = on(TransferenciaLeitoPanelDTO.class);

        getForm().add(new DisabledInputField<String>("descricaoConvenio", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                if (getAtendimento().getConvenio() != null) {
                    return getAtendimento().getConvenio().getDescricao();
                }
                return "";
            }
        }));
        getForm().add(getDropDownSetor());

        getForm().add(tblLeitoQuarto = new SimpleSelectionTable("tblLeitoQuarto", getColumnsQuartos(), getCollectionProviderExames()));
        tblLeitoQuarto.populate();

        tblLeitoQuarto.addSelectionAction(new ISelectionAction<LeitoQuarto>() {

            @Override
            public void onSelection(AjaxRequestTarget target, LeitoQuarto object) {
                if(getForm().getModelObject().getAtendimentoTransferenciaLeito() == null){
                    getForm().getModelObject().setAtendimentoTransferenciaLeito(new AtendimentoTransferenciaLeito());
                }
                getForm().getModelObject().getAtendimentoTransferenciaLeito().setLeitoQuarto(object);
                new TempHelperV2().save(getForm());
            }
        });
        
        getForm().add(new AbstractAjaxButton("btnLimparSelecao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form object) throws ValidacaoException, DAOException {
                tblLeitoQuarto.clearSelection(target);
                ((RadioGroup) tblLeitoQuarto.getBody()).setModelObject(null);
                tblLeitoQuarto.update(target);
                tblLeitoQuarto.populate(target);
                form.getModelObject().getAtendimentoTransferenciaLeito().setLeitoQuarto(null);
                new TempHelperV2().save(form);
            }
        });

        getForm().add(new InputArea(path(on.getAtendimentoTransferenciaLeito().getObservacaoTransferencia())).add(new TempBehaviorV2()));

        add(getForm());
        carregarLeitos(null);

        getForm().add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), TransferenciaLeitoPanelDTO.class))
            .add(new ILoadListener<TransferenciaLeitoPanelDTO>() {

                @Override
                public void afterLoad(TransferenciaLeitoPanelDTO modelObject) {
                    tblLeitoQuarto.setSelectedObject(modelObject.getAtendimentoTransferenciaLeito().getLeitoQuarto());
                }
            }
        ));
    }

    public DropDown getDropDownSetor() {
        if (this.dropDownEmpresa == null) {
            this.dropDownEmpresa = new DropDown<Empresa>(VOUtils.montarPath(TransferenciaLeitoPanelDTO.PROP_ATENDIMENTO_TRANSFERENCIA_LEITO, AtendimentoTransferenciaLeito.PROP_LEITO_QUARTO, LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_EMPRESA));

            dropDownEmpresa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    carregarLeitos(target);
                }
            });

            List<Empresa> setores = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_TIPO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_UNIDADE))
                    .addSorter(new QueryCustom.QueryCustomSorter(Empresa.PROP_DESCRICAO))
                    .start().getList();
            this.dropDownEmpresa.addChoice(null, "Todos");
            if (CollectionUtils.isNotNullEmpty(setores)) {
                for (Empresa _empresa : setores) {
                    this.dropDownEmpresa.addChoice(_empresa, _empresa.getDescricao());
                }
            }
        }
        return dropDownEmpresa;
    }

    private void carregarLeitos(AjaxRequestTarget target) {
        List<LeitoQuarto> leitos = new ArrayList<LeitoQuarto>();
        if (getAtendimento().getConvenio() != null) {
            List<ConvenioQuarto> convenioQuartoList = LoadManager.getInstance(ConvenioQuarto.class)
                    .addProperty(VOUtils.montarPath(ConvenioQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ConvenioQuarto.PROP_CONVENIO, getAtendimento().getConvenio()))
                    .start().getList();
            List<Long> quartosList = new ArrayList<Long>();
            for (ConvenioQuarto convenioQuarto : convenioQuartoList) {
                quartosList.add(convenioQuarto.getQuartoInternacao().getCodigo());
            }

            if (CollectionUtils.isNotNullEmpty(quartosList)) {
                leitos = LoadManager.getInstance(LeitoQuarto.class)
                        .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                        .addProperties(new HQLProperties(QuartoInternacao.class, LeitoQuarto.PROP_QUARTO_INTERNACAO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_SITUACAO), LeitoQuarto.Situacao.LIBERADO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IN, quartosList))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_EMPRESA), dropDownEmpresa.getComponentValue()))
                        .start().getList();

            }
        }
        getForm().getModelObject().setLstLeitoQuarto(leitos);
        if(getForm().getModelObject().getAtendimentoTransferenciaLeito()!=null && getForm().getModelObject().getAtendimentoTransferenciaLeito().getLeitoQuarto() != null && getForm().getModelObject().getAtendimentoTransferenciaLeito().getLeitoQuarto().getCodigo() != null){
            getForm().getModelObject().getAtendimentoTransferenciaLeito().setLeitoQuarto(new LeitoQuarto());
            tblLeitoQuarto.setSelectedObject(null);
        }
        if (target != null) {
            tblLeitoQuarto.update(target);
        }
        new TempHelperV2().save(getForm());
    }

    private Form<TransferenciaLeitoPanelDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<TransferenciaLeitoPanelDTO>("form", new CompoundPropertyModel<TransferenciaLeitoPanelDTO>(new TransferenciaLeitoPanelDTO()));
        }
        return this.form;
    }

    private List<IColumn> getColumnsQuartos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        LeitoQuarto on = on(LeitoQuarto.class);

        columns.add(createColumn(bundle("quarto"), on.getQuartoInternacao().getDescricao()));
        columns.add(createColumn(bundle("setor"), on.getQuartoInternacao().getEmpresa().getDescricao()));
        columns.add(createColumn(bundle("tipoLeito"), on.getQuartoInternacao().getDescricaoTipoQuarto()));
        columns.add(createColumn(bundle("leito"), on.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderExames() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModelObject().getLstLeitoQuarto();
            }
        };
    }
}
