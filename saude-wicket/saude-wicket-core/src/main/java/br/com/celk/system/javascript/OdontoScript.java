package br.com.celk.system.javascript;

import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.odlabs.wiquery.core.javascript.JsQuery;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class OdontoScript implements Serializable{

    public static void registerCallbackURL(IHeaderResponse response, String id, String callbackURL) {
        response.render(OnDomReadyHeaderItem.forScript("Odontogram.registerCallBackURL('" + id + "', '" + callbackURL + "')"));
    }

    public static void setColor(AjaxRequestTarget target, Component component, String attribute, String value) {
        target.appendJavaScript(new JsQuery(component).$().css(attribute, value).render());
    }

    public static void css(AjaxRequestTarget target, Component component, String attribute, String value) {
        target.appendJavaScript(new JsQuery(component).$().css(attribute, value).render());
    }

}
