package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.PuerperioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.PUERPERIO)
public class PuerperioNode extends ProntuarioNodeImp {

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new PuerperioPanel(id, getTitulo());
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.PUERPERIO;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("puerperio");
    }

    @Override
    public Boolean validar(Atendimento atendimento) {
        if (UsuarioCadsus.SEXO_FEMININO.equals(atendimento.getUsuarioCadsus().getSexo())) {
            return true;
        }
        return false;
    }
}
