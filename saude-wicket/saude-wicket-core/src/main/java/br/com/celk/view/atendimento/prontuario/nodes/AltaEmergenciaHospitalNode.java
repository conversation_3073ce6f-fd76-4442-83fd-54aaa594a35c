package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.AltaEmergenciaHospitalPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.ALTA_EMERGENCIA_HOSPITAL)
public class AltaEmergenciaHospitalNode extends ProntuarioNodeImp {

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new AltaEmergenciaHospitalPanel(id, getTitulo());
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("altaOuEncaminhamento");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.USER_GO;
    }
}
