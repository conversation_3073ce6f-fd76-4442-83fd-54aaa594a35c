package br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.link.ReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlImpressaoPrescricaoInternaReceitaControlada extends Panel {

    private WebMarkupContainer image;
    private MultiLineLabel lblMsg;
    private ReportLink btnImprimirPrescricao;
    private ReportLink btnImprimirReceita;
    private AbstractAjaxButton btnFechar;
    private Label lblImprimirPrescricao;
    private Label lblImprimirReceita;
    private Label lblFechar;
    private final String IMG = "img-info";
    private ImpressaoReceituarioDTOParam param;

    public PnlImpressaoPrescricaoInternaReceitaControlada(String id) {
        super(id);
        init();
    }

    private void init() {
        add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        add(lblMsg = new MultiLineLabel("message", BundleManager.getString("desejaVisualizarImpressaoPrescricao")));
        lblMsg.setOutputMarkupId(true);

        add(btnImprimirPrescricao = new ReportLink("btnImprimirPrescricao") {
            @Override
            public DataReport getDataReport() throws ReportException {
                return PnlImpressaoPrescricaoInternaReceitaControlada.this.getPrescricaoInternaDataReport();
            }
        });

        add(btnImprimirReceita = new ReportLink("btnImprimirReceita") {
            @Override
            public DataReport getDataReport() throws ReportException {
                return PnlImpressaoPrescricaoInternaReceitaControlada.this.getReceitaControladaDataReport();
            }

            @Override
            public boolean isVisible() {
                return isVisibleImprimirReceita();
            }
        });

        add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        btnFechar.add(lblFechar = new Label("lblFechar", BundleManager.getString("fechar")));
        lblFechar.setOutputMarkupId(true);
        btnImprimirPrescricao.add(lblImprimirPrescricao = new Label("lblImprimirPrescricao", BundleManager.getString("visualizar")));
        lblImprimirPrescricao.setOutputMarkupId(true);
        btnImprimirReceita.add(lblImprimirReceita = new Label("lblImprimirReceita", BundleManager.getString("imprimirReceita")));
        lblImprimirReceita.setOutputMarkupId(true);
    }

    private DataReport getPrescricaoInternaDataReport() throws ReportException {
        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoPrescricaoInterna(param);
    }

    private DataReport getReceitaControladaDataReport() throws ReportException {
        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioImpressaoReceituarioPrescricaoInterna(param);
    }

    private boolean isVisibleImprimirReceita() {
        try {
            String exigeImpressaoReceituarioControlado = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ExigeImpressaoReceituarioControlado");
            if (exigeImpressaoReceituarioControlado.equals(RepositoryComponentDefault.SIM)) {
                ReceituarioItem proxy = on(ReceituarioItem.class);

                List<Long> receituarioItens = LoadManager.getInstance(ReceituarioItem.class)
                        .addGroup(new QueryCustom.QueryCustomGroup(path(proxy.getCodigo())))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getCodigo()), param.getCodigoReceituario()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getProduto().getTipoReceita().getTipoReceita()), TipoReceita.RECEITA_BRANCA))
                        .start().getList();

                if (!receituarioItens.isEmpty()) {
                    param.setReceituarioItens(receituarioItens);
                    return true;
                }
            }
        } catch (DAOException ex) {
            Logger.getLogger(PnlImpressaoPrescricaoInternaReceitaControlada.class.getName()).log(Level.SEVERE, null, ex);
        }

        return false;
    }

    abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setParam(ImpressaoReceituarioDTOParam param) {
        this.param = param;
    }
}
