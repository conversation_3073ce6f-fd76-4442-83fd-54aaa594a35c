package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Icon32;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.FormularioSaudeMental;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Santos
 */
public class CadastroFormularioSaudeMentalPanel extends ProntuarioCadastroPanel {

    private Form<FormularioSaudeMental> form;
    private FormularioSaudeMental formularioSaudeMental;
    private boolean consulta;
    private Long scoreTotal = 0L;
    private Long classificacao = null;

    private InputArea txaObservacao;
    private AttributeModifier modifier;
    private WebMarkupContainer container;
    private String descricaoRisco;
    private Label lblDescricaoRisco;
    private Label lblScore;
    private String score;
    private Button btnVoltar;

    private WebMarkupContainer containerConsulta;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Profissional profissional;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    private TabelaCbo tabelaCbos;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa estabelecimento;
    private Date dataCadastro;
    private String formulario;

    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;


    public CadastroFormularioSaudeMentalPanel(String id, FormularioSaudeMental formularioSaudeMental, boolean consulta) {
        super(id, bundle("estratificacaoRiscoSaudeMental"));
        this.formularioSaudeMental = formularioSaudeMental;
        this.consulta = consulta;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        FormularioSaudeMental proxy = on(FormularioSaudeMental.class);

        grupo1(proxy);
        grupo2(proxy);
        grupo3(proxy);
        grupo4(proxy);
        grupo5(proxy);

        getForm().add(txaObservacao = new InputArea(path(proxy.getObservacao())));

        getForm().add(container = new WebMarkupContainer("estratificacaoRisco"));
        container.setOutputMarkupPlaceholderTag(true);
        container.add(modifier = new AttributeModifier("class", "icon32 " + Icon32.ball_green.clazz()));
        getForm().add(lblDescricaoRisco = new Label("descricaoRisco", new PropertyModel<String>(this, "descricaoRisco")));
        lblDescricaoRisco.setOutputMarkupPlaceholderTag(true);
        descricaoRisco = bundle("estratificacaoRiscoBaixoRisco");
        getForm().add(lblScore = new Label("score", new PropertyModel<String>(this, "score")));
        lblScore.setOutputMarkupPlaceholderTag(true);
        score = bundle("scoreTotalXPontos", 0);

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                if (!consulta) {
                    viewDlgSimNao(target);
                } else {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }
            }
        }.setDefaultFormProcessing(false));

        getForm().add(containerConsulta = new WebMarkupContainer("containerConsulta"));
        containerConsulta.setOutputMarkupPlaceholderTag(true);
        containerConsulta.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));
        containerConsulta.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbos", new PropertyModel(this, "tabelaCbos")));
        containerConsulta.add(new DateChooser("dataCadastro", new PropertyModel(this, "dataCadastro")));
        containerConsulta.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("estabelecimento", new PropertyModel(this, "estabelecimento")));
        containerConsulta.add(new InputField("formulario", new PropertyModel(this, "formulario")));
        if (consulta) {
            profissional = formularioSaudeMental.getEstratificacaoRisco().getProfissional();
            tabelaCbos = formularioSaudeMental.getEstratificacaoRisco().getCbo();
            estabelecimento = formularioSaudeMental.getEstratificacaoRisco().getEmpresa();
            dataCadastro = formularioSaudeMental.getEstratificacaoRisco().getDataCadastro();
            formulario = formularioSaudeMental.getEstratificacaoRisco().getDescricaoFormulario();
            disableFields(getForm());
            containerConsulta.setVisible(true);
        } else {
            containerConsulta.setVisible(false);
        }

        add(getForm());
        calcularScoreTotal(null);
    }

    private void disableFields(MarkupContainer parent) {
        for (Component next : parent) {
            next.setEnabled(false);
            btnVoltar.setEnabled(true);
        }
    }

    private void calcularScoreTotal(AjaxRequestTarget target) {
        Icon32 icon;
        calcularTotal();
        if (scoreTotal <= 40) {
            classificacao = EstratificacaoRisco.Risco.VERDE.value();
            icon = Icon32.ball_green;
            descricaoRisco = bundle("estratificacaoRiscoBaixoRisco");
        } else if (scoreTotal <= 70) {
            classificacao = EstratificacaoRisco.Risco.AMARELO.value();
            icon = Icon32.ball_yellow;
            descricaoRisco = bundle("estratificacaoRiscoMedioRisco");
        } else {
            classificacao = EstratificacaoRisco.Risco.VERMELHO.value();
            icon = Icon32.ball_red;
            descricaoRisco = bundle("estratificacaoRiscoAltoRisco");
        }
        if (container.getBehaviors().contains(modifier)) {
            container.remove(modifier);
        }
        score = bundle("scoreTotalXPontos", scoreTotal);
        modifier = new AttributeModifier("class", "icon32 " + icon.clazz());
        container.add(modifier);
        if (target != null) {
            target.add(container);
            target.add(lblDescricaoRisco);
            target.add(lblScore);
        }
    }

    private void viewDlgSimNao(AjaxRequestTarget target) {
        if (dlgConfirmacaoSimNao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgAsInformacoesDoFormularioNaoSeraoSalvas")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }
            });
        }
        dlgConfirmacaoSimNao.show(target);
    }

    private void grupo1(FormularioSaudeMental proxy) {
        adicionaCampoDropDown(proxy.getFlagAnsiedadeMedoPersistente());
        adicionaCampoDropDown(proxy.getFlagInsoniaHipersonia());
        adicionaCampoDropDown(proxy.getFlagMedoIntenso());
        adicionaCampoDropDown(proxy.getFlagCrisesConversivasDissociativas());
        adicionaCampoDropDown(proxy.getFlagAlteracaoApetite());
        adicionaCampoDropDown(proxy.getFlagPreocupacaoExcessivaPeso());
        adicionaCampoDropDown(proxy.getFlagQueixaSomaticaPersistenteHipocondriaca());
        adicionaCampoDropDown(proxy.getFlagPensamentosComportamentosRepetitivosConjuntoRituais());
        adicionaCampoDropDown(proxy.getFlagPensamentosInutilidadeSentimentoCulpa());
        adicionaCampoDropDown(proxy.getFlagTristezaPersistente());
        adicionaCampoDropDown(proxy.getFlagPrejuizoAtividadeSexual());
        adicionaCampoDropDown(proxy.getFlagDesorientacaoTemporalEspacial());
    }

    private void grupo2(FormularioSaudeMental proxy) {
        adicionaCampoDropDown(proxy.getFlagIdeacaoSuicidaSemPlanejamento());
        adicionaCampoDropDown(proxy.getFlagIdeacaoSuicidaTentativaSuicidio());
        adicionaCampoDropDown(proxy.getFlagApatiaDiminuicaoDesempenhoSocial());
        adicionaCampoDropDown(proxy.getFlagHumorInstavel());
        adicionaCampoDropDown(proxy.getFlagHeteroagressividadeAutolesivo());
        adicionaCampoDropDown(proxy.getFlagDesinibicaoSocialSexual());
        adicionaCampoDropDown(proxy.getFlagAumentoAtividadeMotora());
        adicionaCampoDropDown(proxy.getFlagHumorAnormalmenteElevado());
        adicionaCampoDropDown(proxy.getFlagDelirio());
        adicionaCampoDropDown(proxy.getFlagAlucinacao());
        adicionaCampoDropDown(proxy.getFlagAlteracaoCursoPensamento());
        adicionaCampoDropDown(proxy.getFlagPerdaCapacidadeJulgamentoRealidade());
        adicionaCampoDropDown(proxy.getFlagAlteracaoMemoria());
    }

    private void grupo3(FormularioSaudeMental proxy) {
        adicionaCampoDropDown(proxy.getFlagDeliriumTremens());
        adicionaCampoDropDown(proxy.getFlagTremorAssociadoHalitoEtilicoSudoreseEtilica());
        adicionaCampoDropDown(proxy.getFlagIncapacidadeReducaoControleUsoDrogas());
        adicionaCampoDropDown(proxy.getFlagManifestacaoComportamentoRiscoSiTerceiros());
        adicionaCampoDropDown(proxy.getFlagConsumoProgressivoSubstanciaPsicoativa());
        adicionaCampoDropDown(proxy.getFlagUsoAbusivoSubstanciaPsicoativa());
    }

    private void grupo4(FormularioSaudeMental proxy) {
        adicionaCampoDropDown(proxy.getFlagDificuldadeCompreenderTransmitirInformacao());
        adicionaCampoDropDown(proxy.getFlagMovimentosCorporaisComportamentaisRepetitivos());
        adicionaCampoDropDown(proxy.getFlagDificuldadeAdquirirDesenvolverHabilidadesEscolares());
        adicionaCampoDropDown(proxy.getFlagDificuldadeAdquirirDesenvolverHabilidadesMotoras());
        adicionaCampoDropDown(proxy.getFlagSeveraDificuldadeInteracaoSocial());
        adicionaCampoDropDown(proxy.getFlagDesatencaoInterrupcaoPrematuraTarefas());
        adicionaCampoDropDown(proxy.getFlagComportamentoProvocativo());
        adicionaCampoDropDown(proxy.getFlagComportamentoReacoesEmocionais());
    }

    private void grupo5(FormularioSaudeMental proxy) {
        adicionaCampoDropDown(proxy.getFlagResistenciaTratamentoRefratariedade());
        adicionaCampoDropDown(proxy.getFlagRecorrenciaRecaida());
        adicionaCampoDropDown(proxy.getFlagExposicaoContinuaStresse());
        adicionaCampoDropDown(proxy.getFlagPrecariedadeSuporteFamiliar());
        adicionaCampoDropDown(proxy.getFlagTestemunhaViolencia());
        adicionaCampoDropDown(proxy.getFlagAutorVitimaViolencia());
        adicionaCampoDropDown(proxy.getFlagPerdaFuncionalidadeFamiliarSocial());
        adicionaCampoDropDown(proxy.getFlagPerdaProgressivaCapacidadeFuncionalOcupacionalSocial());
        adicionaCampoDropDown(proxy.getFlagVulnerabilidadeSocial());
        adicionaCampoDropDown(proxy.getFlagHistoricoFamiliarTranstornoMental());
        adicionaCampoDropDown(proxy.getFlagComorbidadeOutraCondicaoCronicaAssociada());
        adicionaCampoDropDown(proxy.getFlagFaixaEtariaMenores18Maiores60());
        adicionaCampoDropDown(proxy.getFlagAbandoAtrasoEscolar());
    }

    private void calcularTotal() {
        scoreTotal = 0L;
        FormularioSaudeMental formularioSaudeMental = getForm().getModel().getObject();
        somaValoresRespostasGrupo1(formularioSaudeMental);
        somaValoresRespostasGrupo2(formularioSaudeMental);
        somaValoresRespostasGrupo3(formularioSaudeMental);
        somaValoresRespostasGrupo4(formularioSaudeMental);
        somaValoresRespostasGrupo5(formularioSaudeMental);
    }

    private void somaValoresRespostasGrupo1(FormularioSaudeMental formularioSaudeMental) {
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAnsiedadeMedoPersistente(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagInsoniaHipersonia(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagMedoIntenso(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagCrisesConversivasDissociativas(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAlteracaoApetite(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPreocupacaoExcessivaPeso(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagQueixaSomaticaPersistenteHipocondriaca(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPensamentosComportamentosRepetitivosConjuntoRituais(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPensamentosInutilidadeSentimentoCulpa(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagTristezaPersistente(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPrejuizoAtividadeSexual(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDesorientacaoTemporalEspacial(), 2L);
    }

    private void somaValoresRespostasGrupo2(FormularioSaudeMental formularioSaudeMental) {
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagIdeacaoSuicidaSemPlanejamento(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagIdeacaoSuicidaTentativaSuicidio(), 10L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagApatiaDiminuicaoDesempenhoSocial(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagHumorInstavel(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagHeteroagressividadeAutolesivo(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDesinibicaoSocialSexual(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAumentoAtividadeMotora(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagHumorAnormalmenteElevado(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDelirio(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAlucinacao(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAlteracaoCursoPensamento(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPerdaCapacidadeJulgamentoRealidade(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAlteracaoMemoria(), 2L);
    }

    private void somaValoresRespostasGrupo3(FormularioSaudeMental formularioSaudeMental) {
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDeliriumTremens(), 10L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagTremorAssociadoHalitoEtilicoSudoreseEtilica(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagIncapacidadeReducaoControleUsoDrogas(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagManifestacaoComportamentoRiscoSiTerceiros(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagConsumoProgressivoSubstanciaPsicoativa(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagUsoAbusivoSubstanciaPsicoativa(), 8L);
    }

    private void somaValoresRespostasGrupo4(FormularioSaudeMental formularioSaudeMental) {
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDificuldadeCompreenderTransmitirInformacao(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagMovimentosCorporaisComportamentaisRepetitivos(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDificuldadeAdquirirDesenvolverHabilidadesEscolares(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDificuldadeAdquirirDesenvolverHabilidadesMotoras(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagSeveraDificuldadeInteracaoSocial(), 8L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagDesatencaoInterrupcaoPrematuraTarefas(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagComportamentoProvocativo(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagComportamentoReacoesEmocionais(), 4L);
    }

    private void somaValoresRespostasGrupo5(FormularioSaudeMental formularioSaudeMental) {
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagResistenciaTratamentoRefratariedade(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagRecorrenciaRecaida(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagExposicaoContinuaStresse(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPrecariedadeSuporteFamiliar(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagTestemunhaViolencia(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAutorVitimaViolencia(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPerdaFuncionalidadeFamiliarSocial(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagPerdaProgressivaCapacidadeFuncionalOcupacionalSocial(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagVulnerabilidadeSocial(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagHistoricoFamiliarTranstornoMental(), 2L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagComorbidadeOutraCondicaoCronicaAssociada(), 4L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagFaixaEtariaMenores18Maiores60(), 6L);
        scoreTotal += getValorResposta(formularioSaudeMental.getFlagAbandoAtrasoEscolar(), 2L);
    }


    private Long getValorResposta(Long respostaQuestao, Long valorQuestao) {
        if (RepositoryComponentDefault.SIM_LONG.equals(respostaQuestao)) {
            return valorQuestao;
        }
        return 0L;
    }

    private void adicionaCampoDropDown(Object proxiedValue) {
        DropDown<Long> dropDown;
        getForm().add(dropDown = DropDownUtil.getSimNaoLongDropDown(path(proxiedValue), true, false));
        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularScoreTotal(target);
            }
        });
    }

    private Form<FormularioSaudeMental> getForm() {
        if (this.form == null) {
            if (this.formularioSaudeMental == null) {
                this.formularioSaudeMental = new FormularioSaudeMental();
            }
            this.form = new Form<>("form", new CompoundPropertyModel<>(formularioSaudeMental));
        }
        return this.form;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (scoreTotal == 0L) {
            classificacao = EstratificacaoRisco.Risco.VERDE.value();
        }
        EstratificacaoRisco estratificacaoRisco = getForm().getModel().getObject().getEstratificacaoRisco();
        if (estratificacaoRisco == null) {
            estratificacaoRisco = new EstratificacaoRisco();
        }
        estratificacaoRisco.setAtendimento(getAtendimento());
        estratificacaoRisco.setCbo(getAtendimento().getTabelaCbo());
        estratificacaoRisco.setEmpresa(getAtendimento().getEmpresa());
        estratificacaoRisco.setFlagClassificacaoRisco(classificacao);
        estratificacaoRisco.setFormulario(ConfiguracaoEstratificacao.Formulario.SAUDE_MENTAL.value());
        estratificacaoRisco.setProfissional(getAtendimento().getProfissional());
        estratificacaoRisco = BOFactoryWicket.save(estratificacaoRisco);
        FormularioSaudeMental formularioSaudeMental = getForm().getModel().getObject();
        formularioSaudeMental.setFlagRisco(classificacao);
        formularioSaudeMental.setEstratificacaoRisco(estratificacaoRisco);
        formularioSaudeMental.setScore(scoreTotal);
        BOFactoryWicket.save(formularioSaudeMental);

        EstratificacaoRiscoPanel panel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
        getProntuarioController().changePanel(target, panel);

        ConfiguracaoEstratificacao configuracaoEstratificacao = LoadManager.getInstance(ConfiguracaoEstratificacao.class)
                .addProperties(new HQLProperties(ConfiguracaoEstratificacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ConfiguracaoEstratificacao.PROP_FORMULARIO, ConfiguracaoEstratificacao.Formulario.SAUDE_MENTAL.value()))
                .start().getVO();
        if (configuracaoEstratificacao != null) {
            if (classificacao.equals(EstratificacaoRisco.Risco.VERDE.value()) && configuracaoEstratificacao.getDoencaBaixoRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaBaixoRisco());
            } else if (classificacao.equals(EstratificacaoRisco.Risco.AMARELO.value()) && configuracaoEstratificacao.getDoencaMedioRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaMedioRisco());
            } else if (classificacao.equals(EstratificacaoRisco.Risco.VERMELHO.value()) && configuracaoEstratificacao.getDoencaAltoRisco() != null) {
                adicionarDoencaUsuario(configuracaoEstratificacao.getDoencaAltoRisco());
            }
        }
    }

    private void adicionarDoencaUsuario(Doenca doencaConfiguracao) throws ValidacaoException, DAOException {
        BOFactory.getBO(AtendimentoFacade.class).salvarDoencaAPartirEstratificacaoRisco(getAtendimento().getUsuarioCadsus(), doencaConfiguracao, getAtendimento());
    }
}