package br.com.celk.component.lote.saida.dialog;

import br.com.celk.component.table.ISelectionTable;
import br.com.celk.component.table.SelectionRow;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.resource.CssResourceReference;

/**
 *
 * <AUTHOR>
 */
public class LoteTableRow extends SelectionRow<MovimentoGrupoEstoqueItemDTO> {

    private static final String CSS_FILE = "LoteTableRow.css";
    private static final String VENCIDO = "vencido";
    private static final String VENCIDO_HIGH_LIGHTED = "vencidoSelected";

    public LoteTableRow(String id, int index, IModel<MovimentoGrupoEstoqueItemDTO> model, ISelectionTable table) {
        super(id, index, model, table);
    }
    
    @Override
    public String getDefaultClass() {
        if (getRowObject()!=null) {
            if (Data.getDataAtual().after(Data.adjustRangeHour(Coalesce.asDate(getRowObject().getDataValidade())).getDataFinal())) {
                return VENCIDO;
            }
        }
        
        return super.getDefaultClass();
    }

    @Override
    public String getHighlightedClass() {
        if (getRowObject()!=null) {
            if (Data.getDataAtual().after(Data.adjustRangeHour(Coalesce.asDate(getRowObject().getDataValidade())).getDataFinal())) {
                return VENCIDO_HIGH_LIGHTED;
            }
        }

        return super.getHighlightedClass();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(LoteTableRow.class, CSS_FILE)));
    }

}
