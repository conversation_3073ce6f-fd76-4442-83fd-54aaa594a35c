package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoOkObject extends Panel {

    private WebMarkupContainer image;
    private MultiLineLabel label;
    private AbstractAjaxButton btnConfirmar;

    private String IMG = "img-warn";
    private String message;

    public PnlConfirmacaoOkObject(String id, String message, String IMG) {
        super(id);
        this.message = message;
        if (IMG != null) {
            this.IMG = IMG;
        }
        init();
    }

    private void init() {

        add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        add(label = new MultiLineLabel("message", message));

        label.setOutputMarkupId(true);

        add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target);
            }

        });

        btnConfirmar.add(new AttributeModifier("value", getConfirmarLabel()));

        btnConfirmar.setDefaultFormProcessing(false);
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public String getConfirmarLabel() {
        return BundleManager.getString("ok");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public AbstractAjaxButton getBtnConfirmar() {
        return btnConfirmar;
    }

    public void setIMG(String IMG) {
        this.IMG = IMG;
    }
}
