package br.com.celk.view.atendimento.prontuario.panel.template.chart.historicoclinico.graficosavaliacoes;

import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.historicoclinico.graficosavaliacoes.options.GraficosAvaliacoesCharOptions;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.GraficosAvaliacoesSeriesDTO;
import br.com.ksisolucoes.util.Data;
import com.googlecode.wickedcharts.highcharts.options.Axis;
import com.googlecode.wickedcharts.highcharts.options.AxisType;
import com.googlecode.wickedcharts.highcharts.options.DateTimeLabelFormat;
import com.googlecode.wickedcharts.highcharts.options.Function;
import com.googlecode.wickedcharts.highcharts.options.Options;
import com.googlecode.wickedcharts.highcharts.options.Title;
import com.googlecode.wickedcharts.highcharts.options.Tooltip;
import com.googlecode.wickedcharts.highcharts.options.series.Coordinate;
import com.googlecode.wickedcharts.highcharts.options.series.CustomCoordinatesSeries;
import java.io.Serializable;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class EvolucaoPressaoArterialChart implements Serializable {

    private List<GraficosAvaliacoesSeriesDTO> lineSeries;

    public Options startChart() {
        Options options = new GraficosAvaliacoesCharOptions().build().getOptions();
        options.setTitle(new Title(bundle("evolucaoPressaoArterial")));
        
        Axis xAxis = new Axis();
        xAxis.setType(AxisType.DATETIME);
 
        DateTimeLabelFormat dateTimeLabelFormat = new DateTimeLabelFormat()
            .setProperty(DateTimeLabelFormat.DateTimeProperties.MONTH, "%a. %d/%m/%y" )
            .setProperty(DateTimeLabelFormat.DateTimeProperties.YEAR, "%Y")
            .setProperty(DateTimeLabelFormat.DateTimeProperties.HOUR, "%H:%Mh");
        xAxis.setDateTimeLabelFormats(dateTimeLabelFormat);
        xAxis.setTitle(new Title(bundle("dataAvaliacao")));
        options.setxAxis(xAxis);
        
        Axis yAxisPad = new Axis();
        yAxisPad.setTitle(new Title(bundle("pressaoArterial")));
        yAxisPad.setMin(0);
        options.setyAxis(yAxisPad);
        
        Tooltip tooltip = new Tooltip();
        tooltip
            .setFormatter(new Function(
                "return '<b>PAS/PAD: </b>'+ this.y + '<br/>'+Highcharts.dateFormat('%a, %e de %b de %Y %H:%Mh', this.x);"));
        options
            .setTooltip(tooltip);
        
        List<Coordinate<String, Number>> coordinatePad = new ArrayList<Coordinate<String, Number>>();
        List<Coordinate<String, Number>> coordinatePas = new ArrayList<Coordinate<String, Number>>();
        for (GraficosAvaliacoesSeriesDTO dto : getLineSeries()) {
            if (dto.getSeriePad() != null) {
                for (int i = 0; i < dto.getSeriePad().size(); i++) {
                    try {
                        int dia = Data.getDia(dto.getDataAtendimento().get(i));
                        int mes = Data.getMes(dto.getDataAtendimento().get(i)) - 1;
                        int ano = Data.getAno(dto.getDataAtendimento().get(i));
                        int hora = DataUtil.getHora(dto.getDataAtendimento().get(i));
                        int minuto = DataUtil.getMinuto(dto.getDataAtendimento().get(i));
                        int segundo = DataUtil.getSegundo(dto.getDataAtendimento().get(i));
                        
                        coordinatePad.add(new Coordinate<String, Number>("Date.UTC("+ano+",  "+mes +", "+dia+", "+hora+", "+minuto+", "+segundo+")", dto.getSeriePad().get(i)));
                    } catch (ParseException ex) {
                        Logger.getLogger(EvolucaoImcChart.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
            if (dto.getSeriePas() != null) {
                for (int i = 0; i < dto.getSeriePas().size(); i++) {
                    try {
                        int dia = Data.getDia(dto.getDataAtendimento().get(i));
                        int mes = Data.getMes(dto.getDataAtendimento().get(i)) - 1;
                        int ano = Data.getAno(dto.getDataAtendimento().get(i));
                        int hora = DataUtil.getHora(dto.getDataAtendimento().get(i));
                        int minuto = DataUtil.getMinuto(dto.getDataAtendimento().get(i));
                        int segundo = DataUtil.getSegundo(dto.getDataAtendimento().get(i));
                        
                        coordinatePas.add(new Coordinate<String, Number>("Date.UTC("+ano+",  "+mes +", "+dia+", "+hora+", "+minuto+", "+segundo+")", dto.getSeriePas().get(i)));
                    } catch (ParseException ex) {
                        Logger.getLogger(EvolucaoImcChart.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
        }
        
        CustomCoordinatesSeries<String, Number> seriesPas = new CustomCoordinatesSeries<String, Number>();
        seriesPas.setName(bundle("pas"));
        seriesPas.setData(coordinatePas);
        
        CustomCoordinatesSeries<String, Number> seriesPad = new CustomCoordinatesSeries<String, Number>();
        seriesPad.setName(bundle("pad"));
        seriesPad.setData(coordinatePad);
        
        options.addSeries(seriesPad);
        options.addSeries(seriesPas);

        return options;
    }

    public List<GraficosAvaliacoesSeriesDTO> getLineSeries() {
        return lineSeries;
    }

    public void setLineSeries(List<GraficosAvaliacoesSeriesDTO> lineSeries) {
        this.lineSeries = lineSeries;
    }
}
