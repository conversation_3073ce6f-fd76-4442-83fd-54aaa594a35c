package br.com.celk.component.cepField.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.template.Panel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;

/**
 * Created by laudecir on 28/05/18.
 */
public abstract class PnlBuscarEndereco extends Panel {

    private Form<CepWSDTOParam> form;

    private SelectionTable<CepWSDTO> table;
    private List<CepWSDTO> result;

    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;
    private UpperField txtLogradouro;

    private AbstractAjaxButton btnFechar;

    public PnlBuscarEndereco(String id) {
        super(id);
        init();
    }

    private void init() {
        CepWSDTOParam proxy = Lambda.on(CepWSDTOParam.class);

        form = new Form("form", new CompoundPropertyModel(new CepWSDTOParam()));

        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade(CoreMethods.path(proxy.getCidade())));
        form.add(txtLogradouro = new UpperField(CoreMethods.path(proxy.getLogradouro())));

        form.add(table = new SelectionTable("selectionTable", getColumns(), getCollectionProvider()));
        table.setScrollY("180px");
        table.setScrollX("800px");
        table.addSelectionAction(new ISelectionAction<CepWSDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, CepWSDTO object) {
                PnlBuscarEndereco.this.onSelected(target, object);
            }
        });

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                procurar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

        setOutputMarkupPlaceholderTag(true);
    }

    private void procurar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        CepWSDTOParam param = form.getModelObject();
        validarFiltros(param);

        result = BOFactoryWicket.getBO(BasicoFacade.class).consultaCepWS(param);
        table.populate(target);
    }

    private void validarFiltros(CepWSDTOParam param) throws ValidacaoException {
        if (param.getCidade() == null) {
            throw new ValidacaoException(bundle("msgSelecioneFiltroCidade"));
        }

        if (StringUtils.trimToNull(param.getLogradouro()) == null || param.getLogradouro().trim().length() < 3) {
            throw new ValidacaoException(bundle("msgPreenchaFiltroLogradouroPeloMenos3Letras"));
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        CepWSDTO proxy = Lambda.on(CepWSDTO.class);

        columns.add(createColumn(bundle("cep"), proxy.getCep()));
        columns.add(createColumn(bundle("logradouro"), proxy.getLogradouro()));
        columns.add(createColumn(bundle("complemento"), proxy.getComplemento()));
        columns.add(createColumn(bundle("bairro"), proxy.getBairro()));
        columns.add(createColumn(bundle("cidade"), proxy.getCidadeUfFormatado()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection<CepWSDTO> getCollection(Object param) throws DAOException, ValidacaoException {
                if (result == null) {
                    result = new ArrayList<>();
                }
                return result;
            }
        };
    }

    public void limpar(AjaxRequestTarget target) {
        Cidade cidade = ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCidade();

        CepWSDTOParam param = new CepWSDTOParam();
        param.setCidade(cidade);

        form.getModel().setObject(param);
        table.limpar(target);
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onSelected(AjaxRequestTarget target, CepWSDTO dto);
}