package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.AvaliacaoEmergenciaHospitalPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.AVALIACAO_EMERGENCIA_HOSPITAL)
public class AvaliacaoEmergenciaHospitalNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new AvaliacaoEmergenciaHospitalPanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.TEMPERATURE_4;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("avaliacao");
    }
}
