package br.com.celk.view.unidadesaude.anexo;

import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.comunicacao.base.AnexoDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.hospital.SolicitacaoAgendamentoAnexo;
import br.com.ksisolucoes.vo.prontuario.hospital.AihAnexo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AbstractAjaxBehavior;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.ExternalLink;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.handler.resource.ResourceStreamRequestHandler;
import org.apache.wicket.request.resource.ContentDisposition;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public abstract class PanelArquivosAnexados extends Panel {

    private ExternalLink linkAnexo;
    private ViewAnexoBehavior viewAnexoBehavior;
    private boolean exibirVisualizacao;
    private AjaxPreviewBlank ajaxPreviewBlank;


    public PanelArquivosAnexados(String id, AnexoPacienteElo elo) {
        super(id);
        init(elo);
    }

    public PanelArquivosAnexados(String id, SolicitacaoAgendamentoAnexo solicitacaoAgendamentoAnexo) {
        super(id);
        init(solicitacaoAgendamentoAnexo);
    }

    public PanelArquivosAnexados(String id, AihAnexo aihAnexo) {
        super(id);
        init(aihAnexo);
    }

    public PanelArquivosAnexados(String id, AnexoDocumento anexoDocumento) {
        super(id);
        initDocumento(anexoDocumento);
    }

    public PanelArquivosAnexados(String id, AnexoDocumento anexoDocumento, boolean exibirVisualizacao) {
        super(id);
        this.exibirVisualizacao = exibirVisualizacao;
        initDocumento(anexoDocumento);
    }

    private void init(final AnexoPacienteElo elo) {
        add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(viewAnexoBehavior = new ViewAnexoBehavior(elo.getGerenciadorArquivo()));
        add(linkAnexo = new ExternalLink("linkAnexo", "#"));
        add(new AbstractAjaxLink("linkRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                removerAnexo(target, elo);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }
        });
        add(new AbstractAjaxLink("linkAnexoView") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                vizualizarAnexo(target);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }

            @Override
            public boolean isVisible() {
                return exibirVisualizacao;
            }
        });
    }

    private void init(final SolicitacaoAgendamentoAnexo solicitacaoAgendamentoAnexo) {
        add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(viewAnexoBehavior = new ViewAnexoBehavior(solicitacaoAgendamentoAnexo.getGerenciadorArquivo()));

        add(linkAnexo = new ExternalLink("linkAnexo", "#"));
        add(new AbstractAjaxLink("linkRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                removerAnexo(target, solicitacaoAgendamentoAnexo);
            }
            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }
        });
        add(new AbstractAjaxLink("linkAnexoView") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                vizualizarAnexo(target);
            }
        });
    }

    private void init(final AihAnexo aihAnexo) {
        add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(viewAnexoBehavior = new ViewAnexoBehavior(aihAnexo.getGerenciadorArquivo()));
        add(linkAnexo = new ExternalLink("linkAnexo", "#"));
        add(new AbstractAjaxLink("linkRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                removerAnexo(target, aihAnexo);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }
        });
        add(new AbstractAjaxLink("linkAnexoView") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                vizualizarAnexo(target);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }

            @Override
            public boolean isVisible() {
                return exibirVisualizacao;
            }
        });
    }

    private void initDocumento(final AnexoDocumento anexoDocumento) {
        add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(viewAnexoBehavior = new ViewAnexoBehavior(anexoDocumento.getGerenciadorArquivo()));
        add(linkAnexo = new ExternalLink("linkAnexo", "#"));
        add(new AbstractAjaxLink("linkRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                removerAnexoDocumento(target, anexoDocumento);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }
        });
        add(new AbstractAjaxLink("linkAnexoView") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                vizualizarAnexo(target);
            }

            @Override
            public boolean isEnabled() {
                return !isViewOnly();
            }

            @Override
            public boolean isVisible() {
                return exibirVisualizacao;
            }
        });
    }

    @Override
    protected void onBeforeRender() {
        CharSequence urlAnexo = viewAnexoBehavior.getUrlAnexo();
        ExternalLink externalLink = new ExternalLink("linkAnexo", urlAnexo.toString());
        linkAnexo.replaceWith(externalLink);
        linkAnexo = externalLink;
        linkAnexo.add(new Label("nomeArquivo", viewAnexoBehavior.getGerenciadorArquivo().getNomeArquivo()).setOutputMarkupId(true));

        super.onBeforeRender();
    }

    private class ViewAnexoBehavior extends AbstractAjaxBehavior {

        private GerenciadorArquivo gerenciadorArquivo;

        public ViewAnexoBehavior(GerenciadorArquivo gerenciadorArquivo) {
            this.gerenciadorArquivo = gerenciadorArquivo;
        }

        @Override
        public void onRequest() {
            try {
                File file = File.createTempFile(gerenciadorArquivo.getNomeArquivo(), "");
                try {
                    FileUtils.buscarArquivoFtp(gerenciadorArquivo.getCaminho(), file.getAbsolutePath());
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }

                IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));

                ResourceStreamRequestHandler handler = new ResourceStreamRequestHandler(resourceStream, gerenciadorArquivo.getNomeArquivo());
                handler.setContentDisposition(ContentDisposition.INLINE);
                getComponent().getRequestCycle().scheduleRequestHandlerAfterCurrent(handler);
            } catch (IOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }

        public GerenciadorArquivo getGerenciadorArquivo() {
            return gerenciadorArquivo;
        }

        public String getUrlAnexo() {
            String url = getCallbackUrl().toString();

            url = url + (url.contains("?") ? "&" : "?");
            url = url + "antiCache=" + System.currentTimeMillis();

            return url;
        }

    }

    private void vizualizarAnexo(AjaxRequestTarget target) {
        File f = null;
        try {
            f = File.createTempFile("anexo", "arquivos");
            FileUtils.buscarArquivoFtp(viewAnexoBehavior.getGerenciadorArquivo().getCaminho(), f.getAbsolutePath());
            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
            ajaxPreviewBlank.initiate(target, viewAnexoBehavior.getGerenciadorArquivo().getNomeArquivo(), resourceStream);
        } catch (IOException | ValidacaoException e) {
            Loggable.log.error(e);
        }
    }

    public abstract void removerAnexo(AjaxRequestTarget target, AnexoPacienteElo elo) throws ValidacaoException, DAOException;

    public abstract void removerAnexo(AjaxRequestTarget target, SolicitacaoAgendamentoAnexo anexo) throws ValidacaoException, DAOException;

    public abstract void removerAnexo(AjaxRequestTarget target, AihAnexo anexo) throws ValidacaoException, DAOException;

    public abstract void removerAnexoDocumento(AjaxRequestTarget target, AnexoDocumento anexoDocumento) throws ValidacaoException, DAOException;

    public abstract boolean isViewOnly();
}
