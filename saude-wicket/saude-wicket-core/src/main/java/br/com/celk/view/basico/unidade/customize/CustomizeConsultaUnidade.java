package br.com.celk.view.basico.unidade.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaUnidade extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(Unidade.PROP_DESCRICAO, (String) BuilderQueryCustom.QueryParameter.CONSULTA_LIKED));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(Unidade.PROP_CODIGO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(Unidade.PROP_DESCRICAO));
        properties.put(BundleManager.getString("un"), VOUtils.montarPath(Unidade.PROP_UNIDADE));
    }

    @Override
    public Class getClassConsulta() {
        return Unidade.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(Unidade.class).getProperties();
    }

}
