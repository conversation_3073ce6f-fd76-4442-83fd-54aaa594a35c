package br.com.celk.view.atendimento.prontuario.panel.tuberculose.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConclusaoTuberculose extends Window{

    private PnlConclusaoTuberculose pnlConclusaoTuberculose;

    public DlgConclusaoTuberculose(String id, TuberculoseAcompanhamento tuberculoseAcompanhamento){
        super(id);
        init(tuberculoseAcompanhamento);
    }

    private void init(TuberculoseAcompanhamento tuberculoseAcompanhamento) {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("encerramentoAcompanhamento");
            }
        });
                
        setInitialWidth(450);
        setInitialHeight(160);
        setResizable(true);
        
        setContent(pnlConclusaoTuberculose = new PnlConclusaoTuberculose(getContentId(), tuberculoseAcompanhamento) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, TuberculoseAcompanhamento tuberculoseAcompanhamento) throws ValidacaoException, DAOException {
                close(target);
                DlgConclusaoTuberculose.this.onConfirmar(target, tuberculoseAcompanhamento);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, TuberculoseAcompanhamento tuberculoseAcompanhamento) throws ValidacaoException, DAOException;
    
}