package br.com.celk.view.hospital.autocomplete.honorarioipe;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaDespesasIpeDTOParam;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaHonorariosIpeDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerHonorarioIpe extends Panel implements IRestricaoContainer<QueryConsultaHonorariosIpeDTOParam> {

    private InputField<String> txtCodigo;
    private InputField<String> txtDescricao;
    
    private QueryConsultaHonorariosIpeDTOParam param = new QueryConsultaHonorariosIpeDTOParam();
    
    public RestricaoContainerHonorarioIpe(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtCodigo = new InputField<String>("codigo"));
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaHonorariosIpeDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtCodigo.limpar(target);
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtCodigo;
    }

}
