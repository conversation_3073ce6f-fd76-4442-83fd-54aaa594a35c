package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

public abstract class DlgInformarEquipe extends Window {

    private final List<EquipeProfissional> equipesProfissionalList;
    private PnlInformarEquipe pnlInformarEquipe;

    protected DlgInformarEquipe(String id, List<EquipeProfissional> equipesProfissionalList) {
        super(id);
        this.equipesProfissionalList = equipesProfissionalList;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("rotulo_equipe_do_profissional");
            }
        });

        setInitialWidth(500);
        setInitialHeight(50);
        setResizable(true);

        setContent(pnlInformarEquipe = new PnlInformarEquipe(getContentId(), equipesProfissionalList) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, EquipeProfissional equipesProfissional) throws ValidacaoException, DAOException {
                close(target);
                DlgInformarEquipe.this.onConfirmar(target, equipesProfissional);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, EquipeProfissional equipesProfissional) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, List<EquipeProfissional> equipesDoProfissional) {
        show(target);
        pnlInformarEquipe.setObject(target, equipesDoProfissional);
    }
}