package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.atendimento.condicaopaciente.ClassificacaoAtendimentoPorCondicaoSaude;
import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.v3.TempHelperV3;
import br.com.celk.component.temp.v3.behavior.TempBehaviorV3;
import br.com.celk.component.temp.v3.behavior.TempFormBehaviorV3;
import br.com.celk.component.temp.v3.store.interfaces.impl.DefaultTempStoreStrategyV3;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.EvolucaoEditorSettings;
import br.com.celk.component.tinymce.util.TextEditorHelper;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Resources;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.dialog.prenatal.DlgConcluirPreNatal;
import br.com.celk.view.atendimento.prontuario.interfaces.IProntuarioController;
import br.com.celk.view.atendimento.prontuario.panel.dialog.*;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.soap.AtendimentosAnterioresTab;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.soap.GrupoProblemasCondicaoTab;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.soap.MedicamentoEmUsoTab;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.basico.diagnosticoenfermagem.autocomplete.AutoCompleteConsultaDiagnosticoEnfermagemSae;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.LoadInterceptorClassificacaoCbo;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapMedicoDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.covid.CondutaCovid19;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanAlimentacao;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanDoenca;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanIntercorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.request.resource.ResourceReference;
import org.hamcrest.Matchers;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class SoapPanel extends ProntuarioCadastroPanel implements PermissionContainer {

    List<EvolucaoProntuario> evolucaoProntuarioList;
    private Form<SoapDTO> form;
    private AtendimentoSoap atendimentoSoap;
    private WebMarkupContainer containerSubjetivo;
    private WebMarkupContainer containerObjetivo;
    private WebMarkupContainer containerAvaliacao;
    private WebMarkupContainer containerPlano;
    private WebMarkupContainer containerGrupoProblemasCondicoes;
    private WebMarkupContainer containerAcoesPlano;
    private WebMarkupContainer containerExames;
    private WebMarkupContainer containerdadosAtendimentoPrimario;
    private WebMarkupContainer containerGrupoProblemasCondicao;
    private AbstractAjaxLink linkAtestados;
    private AbstractAjaxLink linkExames;
    private AbstractAjaxLink linkLembretes;
    private AbstractAjaxLink linkPrescricaoMedicamentos;
    private AbstractAjaxLink linkLaudoApac;
    private AbstractAjaxLink linkEncaminhamentos;
    private CompoundPropertyModel<GrupoProblemasCondicoes> modelGrupoProblemasCondicoes;
    private DlgLembretesAtendimento dlgLembretesAtendimento;
    //Subcomponentes do Subjetivo
    private WebMarkupContainer containerCiapMotivoConsulta;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiapMotivoConsulta;
    //Subcomponentes do Plano
    private WebMarkupContainer containerCiapIntervencao;
    private DropDown<TipoDocumentoAtendimento> dropDownTipoDocumentoAtendimento;
    private DlgInformarSoapCiap dlgInformarSoapCiapIntervencao;
    private PnlInformarClassificacaoAtendimento pnlInformarClassificacaoAtendimento;
    private InputArea txaSubjetivo;
    private InputArea txaObjetivo;
    private InputArea txaAvaliacao;
    private InputArea txaPlano;
    private DropDown<ClassificacaoAtendimento> cbxClassificacaoAtendimento;
    private InputField<Long> txtDiasRetorno;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private WebMarkupContainer containerDadosAtendimento;
    private WebMarkupContainer containerProfissionalAuxiliar;
    private WebMarkupContainer containerGestante;
    private WebMarkupContainer containerGraficos;
    private WebMarkupContainer containerRegistroDiarreia;
    private WebMarkupContainer containerNasf;
    private DlgConcluirPreNatal dlgConcluirPreNatal;
    private DropDown<Conduta> cbxConduta;
    private DropDown<Long> cbxCondutaCovid;
    private DropDown cbxDiarreia;
    private DropDown cbxComSangue;
    private DateChooser chDataPrimeirosSintomas;
    private InputField txtResultado;
    private DropDown cbxPlanoTratamento;
    private final String id;
    private final String titulo;
    private CheckBox checkFiltrarCidCiap;
    private CheckBoxLongValue checkAtendimentoCompartilhado;
    private DropDown<TabelaCbo> cbxCboProfissionalAuxiliar;
    private DlgInformarConduta dlgInformarConduta;
    private DlgInformarCipe dlgInformarCipe;
    private CheckBoxLongValue checkBoxNasfAvaliacaoDiagnostico;
    private CheckBoxLongValue checkBoxNasfProcedimentosClinicosTerapeuticos;
    private CheckBoxLongValue checkBoxNasfPrescricaoTerapeutica;
    private DropDown cbxAcessoCompartilhado;
    private DropDown dropDownRacionalidadeSaude;
    private DropDown dropDownTipoAtendimento;
    private DropDown dropDownVacina;
    private DropDown dropDownAtencaoDomiciliar;
    private DropDown<SisvanAlimentacao> dropDownSisvanAlimentacao;
    private Component txtVaginal;
    private Component txtCesariana;
    private Component txtAbortos;
    private InputField txtIdadeGestacionalDum;
    private LongField txtNumeroGestasPrevias;
    private LongField txtNumeroPartos;
    private DateChooser dchDumGestante;
    private DateChooser dchPrimeiraUsg;
    private DateChooser dchDppDum;
    private DateChooser dchDppUsg;
    private DateChooser dchDataUltimaGestacao;
    private DropDown dropDownGravidezPlanejada;
    private DropDown<String> dropDownSexo;
    private String sexo;
    private LongField txtAlturaUterina;
    private DlgAjudaSoap dlgAjudaSoap;
    private AutoCompleteConsultaCid autoCompleteConsultaCidGrupoProblemasCondicoes;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiapGrupoProblemasCondicoes;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiapIntervencao;
    private Table tblGrupoProblemasCondicoes;
    private Table tblCiap2MotivoConsulta;
    private Table tblCiapIntervencao;
    private DropDown dropDownSituacaoGrupoProblemasCondicoes;
    private DlgInformarClassificacaoAtendimento dlgInformarClassificacaoAtendimento;
    private CheckBoxLongValue checkBoxAdicionarListaProblemas;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;
    private final List<CheckBoxLongValue> lstCheckBoxNasf = new ArrayList<>();
    private NoHistoricoClinicoDTO noHistoricoClinicoDTO;
    private SolicitacaoExamesPanel solicitacaoExamesPanel;
    private SoapMedicoDTOParam soapMedicoDTOParam;

    private final String CSS_FILE = "SoapPanel.css";
    private AbstractAjaxButton btnEncerrarPreNatal;

    private IParameterModuleContainer unidadeSaudeParam;

    private DoubleField txtPeso;
    private DoubleField txtAltura;
    private Label lblImc;
    private Label lblSituacaoImc;
    private String textoImc;
    private String textoSituacaoImc;

    private Boolean enableBolsaFamilia = Boolean.FALSE;
    private Integer idadeParaValidarAlimentacao = null;
    private Long idadeEmMeses = null;
    private final boolean isGestante = false;

    private final AttributeModifier modifierVerde = new AttributeModifier("style", "color: #388E3C;");
    private final AttributeModifier modifierVermelho = new AttributeModifier("style", "color: #E53935; display: block; text-align: center;");

    private DropDown<Long> cbxTipoGlicemia;
    private LongField txtGlicemia;
    private Label lblAvaliacaoGlicemia;

    private DoubleField txtPerimetroCefalico;
    private DoubleField txtCircunferenciaAbdominal;

    private AutoCompleteConsultaDiagnosticoEnfermagemSae autoCompleteConsultaDiagnosticoEnfermagemSae;
    private AbstractAjaxLink btnAdicionarCipe;
    private WebMarkupContainer containerCipe;


    public SoapPanel(String id) {
        super(id, BundleManager.getString("evolucaoSoap"));
        this.id = id;
        this.titulo = BundleManager.getString("evolucaoSoap");
    }

    public SoapPanel(String id, String titulo) {
        super(id, titulo);
        this.id = id;
        this.titulo = titulo;
    }

    public SoapPanel(String id, String titulo, AtendimentoSoap atendimentoSoap) {
        super(id, titulo);
        this.id = id;
        this.titulo = titulo;
        this.atendimentoSoap = atendimentoSoap;
    }

    public SoapPanel(String id, String titulo, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, titulo);
        this.id = id;
        this.titulo = titulo;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        SoapDTO proxy = on(SoapDTO.class);

        getForm().add(new TempFormBehaviorV3(new DefaultTempStoreStrategyV3(getAtendimento(), getIdentificador().toString(), SoapDTO.class, getAtendimento().getProfissional(), getAtendimento().getTabelaCbo())));

        AbstractAjaxLink linkAjuda;
        getForm().add(linkAjuda = new AbstractAjaxLink("linkAjuda") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                if (dlgAjudaSoap == null) {
                    getProntuarioController().addWindow(target, dlgAjudaSoap = new DlgAjudaSoap(getProntuarioController().newWindowId()) {
                    });
                }
                dlgAjudaSoap.show(target);
            }
        });

        linkAjuda.add(new Image("ajudaIcon", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.INFO.resourceReference();
            }
        }));
        linkAjuda.add(new AttributeModifier("title", BundleManager.getString("atalhos")));

        addTabs();

        // SUBJETIVO
        {
            containerSubjetivo = new WebMarkupContainer("containerSubjetivo");

            containerSubjetivo.add(containerCiapMotivoConsulta = new WebMarkupContainer("containerCiapMotivoConsulta"));

            containerCiapMotivoConsulta.add(autoCompleteConsultaCiapMotivoConsulta = new AutoCompleteConsultaCiap("ciapMotivoConsulta", new Model<Ciap>()));
            autoCompleteConsultaCiapMotivoConsulta.addAjaxUpdateValue();

            containerCiapMotivoConsulta.add(new AbstractAjaxButton("btnAdicionarCiap2MotivoConsulta") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    adicionarCiap2MotivoConsulta(target);
                }
            });

            containerCiapMotivoConsulta.add(tblCiap2MotivoConsulta = new Table("tblCiap2MotivoConsulta", getColumnsTabelaCiap2MotivoConsulta(), getCollectionProviderTabelaCiap2MotivoConsulta()));
            tblCiap2MotivoConsulta.populate();
            tblCiap2MotivoConsulta.setScrollY("300");
//
            containerSubjetivo.add(getDropDownTipoDocumentoAtendimento());

            containerSubjetivo.add(txaSubjetivo = new InputArea(path(proxy.getAtendimentoSoap().getSubjetivo())));
            txaSubjetivo.setOutputMarkupId(true);

            containerSubjetivo.setOutputMarkupId(true);
            getForm().add(containerSubjetivo);
        }

        // OBJETIVO
        {
            containerObjetivo = new WebMarkupContainer("containerObjetivo");
            containerObjetivo.add(txaObjetivo = new InputArea(path(proxy.getAtendimentoSoap().getObjetivo())));

            try {
                this.unidadeSaudeParam = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }

            validaSisvanAlimentacao();

            containerObjetivo.add(getContainerDadosAntropometricos(proxy));

            containerObjetivo.add(dropDownVacina = DropDownUtil.getSimNaoLongDropDown(path(proxy.getVacinaEmDia()), true, false));

            containerExames = new WebMarkupContainer("containerExames");
            containerExames.setOutputMarkupId(true);
            containerExames.setOutputMarkupPlaceholderTag(true);

            containerExames.add(solicitacaoExamesPanel = new SolicitacaoExamesPanel("solicitacaoExamesPanel", SoapDTO.ContainerTelaSoap.CONTAINER_EXAMES, false) {
                @Override
                public Atendimento getAtendimento() {
                    return SoapPanel.this.getAtendimento();
                }

                @Override
                public IProntuarioController getProntuarioController() {
                    return SoapPanel.this.getProntuarioController();
                }
            });
            solicitacaoExamesPanel.setOutputMarkupId(true);
            solicitacaoExamesPanel.setOutputMarkupPlaceholderTag(true);


            containerObjetivo.add(containerExames);


            // GESTANTE
            {
                containerGestante = new WebMarkupContainer("containerGestante");
                containerGestante.add(dropDownGravidezPlanejada = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGravidezPlanejada()), true, false));
                containerGestante.add(dchDataUltimaGestacao = new DateChooser(path(proxy.getDataUltimaGestacao())));
                containerGestante.add(txtNumeroGestasPrevias = new LongField(path(proxy.getNumeroGestasPrevias())));
                containerGestante.add(txtVaginal = new LongField(path(proxy.getVaginal())).setVMin(Long.MIN_VALUE).setVMax(99L).add(new TempBehaviorV3()));
                containerGestante.add(txtCesariana = new LongField(path(proxy.getCesariana())).setVMin(Long.MIN_VALUE).setVMax(99L).add(new TempBehaviorV3()));
                containerGestante.add(txtAbortos = new LongField(path(proxy.getAbortos())).setVMin(Long.MIN_VALUE).setVMax(99L).add(new TempBehaviorV3()));
                containerGestante.add(txtNumeroPartos = new LongField(path(proxy.getNumeroPartos())));

                containerGestante.add(dchDumGestante = new DateChooser(path(proxy.getDumGestante())));
                dchDumGestante.add((new Tooltip().setText("msg_dum_dpp")));
                dchDumGestante.addAjaxUpdateValue();
                dchDumGestante.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        dchDppDum.limpar(target);

                        if (dchDumGestante.getComponentValue() != null) {
                            dchDppDum.setComponentValue(Data.addDias(dchDumGestante.getComponentValue(), 280));
                            target.add(dchDppDum);
                            target.appendJavaScript(JScript.initMasks());
                            setIdadeGestacional(target);
                        }
                    }
                });
                containerGestante.add(dchDppDum = new DateChooser(path(proxy.getDppDum())));
                dchDppDum.add(new Tooltip().setText("msg_dum_dpp"));
                dchDppDum.addAjaxUpdateValue();
                dchDppDum.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        dchDumGestante.limpar(target);

                        if (dchDppDum.getComponentValue() != null) {
                            dchDumGestante.setComponentValue(Data.removeDias(dchDppDum.getComponentValue(), 280));
                            target.add(dchDumGestante);
                            target.appendJavaScript(JScript.initMasks());
                            setIdadeGestacional(target);
                        }

                    }
                });


                containerGestante.add(dchPrimeiraUsg = new DateChooser(path(proxy.getDataPrimeiraUsg())));
                containerGestante.add(txtIdadeGestacionalDum = new InputField(path(proxy.getDescricaoIdadeGestacionalDum())));
                txtIdadeGestacionalDum.setEnabled(false);
                containerGestante.add(dchDppUsg = new DateChooser(path(proxy.getDppUsg())));

                dchPrimeiraUsg.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

                txtNumeroGestasPrevias.setVMax(99L);
                txtNumeroPartos.setVMax(99L);

                containerGestante.add(btnEncerrarPreNatal = new AbstractAjaxButton("btnEncerrarPreNatal") {
                    @Override
                    public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                        viewDlgConcluirPreNatal(target, getPreNatal());
                    }
                });

                containerGestante.add(txtAlturaUterina = new LongField(path(proxy.getAtendimentoPrimario().getAlturaUterina())));

                dchDumGestante.add(new TempBehaviorV3());
                dchPrimeiraUsg.add(new TempBehaviorV3());
                dchDppUsg.add(new TempBehaviorV3());
                dchDppDum.add(new TempBehaviorV3());
                dropDownGravidezPlanejada.add(new TempBehaviorV3());
                txtIdadeGestacionalDum.add(new TempBehaviorV3());
                txtNumeroGestasPrevias.add(new TempBehaviorV3());
                txtAlturaUterina.add(new TempBehaviorV3());

                containerGestante.setOutputMarkupId(true);

                if (getAtendimento().getUsuarioCadsus().getIdade() < 60L && getAtendimento().getUsuarioCadsus().getIdade() < 9L) {
                    containerGestante.setVisible(false);
                } else containerGestante.setVisible(!UsuarioCadsus.SEXO_MASCULINO.equals(getAtendimento().getUsuarioCadsus().getSexo()));
                containerObjetivo.add(containerGestante);
            }

            containerObjetivo.setOutputMarkupId(true);
            getForm().add(containerObjetivo);
        }

        containerGraficos = new WebMarkupContainer("containerGraficosSoap");
        containerGraficos.setOutputMarkupId(true);
        containerObjetivo.add(containerGraficos);

        containerGraficos.add(new GraficoRegistroEspecializadoPanel("graficoRegistroEspecializadoPanel", getAtendimento()) {
        });

        // -------------- CURVA DE CRESCIMENTO -------------------
        {
            WebMarkupContainer containerCurvaCrescimento = new WebMarkupContainer("containerCurvaCrescimento");
            containerCurvaCrescimento.setOutputMarkupId(true);

            if (getAtendimento().getUsuarioCadsus().getIdade() < 2L) {
                CurvaCrescimentoPanel curvaCrescimentoPanel = new CurvaCrescimentoPanel("panel", bundle("crianca"));
                curvaCrescimentoPanel.setProntuarioController(getProntuarioController());
                containerCurvaCrescimento.add(curvaCrescimentoPanel);
            } else {
                containerCurvaCrescimento.add(new EmptyPanel("panel"));
                containerCurvaCrescimento.setVisible(false);
            }

            containerObjetivo.add(containerCurvaCrescimento);
        }

        // -------------- INICIO AVALIAÇÃO -------------------
        {
            containerAvaliacao = new WebMarkupContainer("containerAvaliacao");
            containerAvaliacao.add(txaAvaliacao = new InputArea(path(proxy.getAtendimentoSoap().getAvaliacao())));

            containerGrupoProblemasCondicoes = new WebMarkupContainer("containerGrupoProblemasCondicoes", modelGrupoProblemasCondicoes = new CompoundPropertyModel<>(new GrupoProblemasCondicoes()));
            containerAvaliacao.add(containerGrupoProblemasCondicoes);

            GrupoProblemasCondicoes proxyGrupoProblemasCondicoes = on(GrupoProblemasCondicoes.class);

            containerGrupoProblemasCondicoes.add(autoCompleteConsultaCiapGrupoProblemasCondicoes = new AutoCompleteConsultaCiap(path(proxyGrupoProblemasCondicoes.getCiap())));

            containerGrupoProblemasCondicoes.add(autoCompleteConsultaCidGrupoProblemasCondicoes = new AutoCompleteConsultaCid(path(proxyGrupoProblemasCondicoes.getCid()), false).setSexo(getAtendimento().getUsuarioCadsus().getSexo()));
            autoCompleteConsultaCidGrupoProblemasCondicoes.add(new Tooltip().setText("msgInfCidSexo"));
            autoCompleteConsultaCidGrupoProblemasCondicoes.setValidaCategoria(false);
            autoCompleteConsultaCidGrupoProblemasCondicoes.ocultarCidsInativos();
            containerGrupoProblemasCondicoes.add(dropDownSituacaoGrupoProblemasCondicoes = DropDownUtil.getIEnumDropDown(path(proxyGrupoProblemasCondicoes.getSituacao()), GrupoProblemasCondicoes.Situacao.values()));
            containerGrupoProblemasCondicoes.add(checkBoxAdicionarListaProblemas = new CheckBoxLongValue("adicionarListaProblemas", RepositoryComponentDefault.SIM_LONG, new Model<Long>()));

            containerGrupoProblemasCondicoes.add(new AbstractAjaxButton("btnAdicionarGrupoProblemasCondicoes") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    adicionarGrupoProblemasCondicoes(target);
                }
            });

            containerGrupoProblemasCondicoes.add(tblGrupoProblemasCondicoes = new Table("tblGrupoProblemasCondicoes", getColumnsGrupoProblemasCondicoes(), getCollectionProviderGrupoProblemasCondicoes()));
            tblGrupoProblemasCondicoes.populate();
            tblGrupoProblemasCondicoes.setScrollY("1800");


            containerAvaliacao.setOutputMarkupId(true);
            getForm().add(containerAvaliacao);
        }

        // -------------- INICIO PLANO --------------------------------
        {
            containerPlano = new WebMarkupContainer("containerPlano");
            containerPlano.add(txaPlano = new InputArea(path(proxy.getAtendimentoSoap().getPlano())));
            containerPlano.add(containerCiapIntervencao = new WebMarkupContainer("containerCiapIntervencao"));

            containerCiapIntervencao.add(autoCompleteConsultaCiapIntervencao = new AutoCompleteConsultaCiap(path(proxy.getCiapIntervencao())));
            autoCompleteConsultaCiapIntervencao.addAjaxUpdateValue();
            autoCompleteConsultaCiapIntervencao.setOutputMarkupId(true);

            containerCiapIntervencao.add(new AbstractAjaxButton("btnAdicionarCiapIntervencao") {
                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    adicionarCiapIntervencao(target);
                }
            });

            containerCiapIntervencao.add(tblCiapIntervencao = new Table("tblCiapIntervencao", getColumnCiapIntervencao(), getCollectionProviderCiapIntervencao()));
            tblCiapIntervencao.populate();
            tblCiapIntervencao.setScrollY("300");

            // Botões
            {
                containerAcoesPlano = new WebMarkupContainer("containerAcoesPlano");
                containerAcoesPlano.setOutputMarkupId(true);
                containerPlano.add(containerAcoesPlano);

                // Atestados
                containerAcoesPlano.add(linkAtestados = new AbstractAjaxLink("linkAtestados") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new DocumentosPanel(getProntuarioController().panelId(), BundleManager.getString("documentos"), true, SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                    }

                    @Override
                    public boolean isVisible() {
                        return getProntuarioController().isNodePermitted(NodesAtendimentoRef.DOCUMENTOS);
                    }
                });

                linkAtestados.add(new Image("linkAtestados", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.DOCUMENT_PREPARE.resourceReference();
                    }
                }));
                linkAtestados.add(new AttributeModifier("title", BundleManager.getString("documentos")));
                linkAtestados.add(new Label("labelAtestados", BundleManager.getString("documentos")));

                // Exames
                containerAcoesPlano.add(linkExames = new AbstractAjaxLink("linkExames") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                    }

                    @Override
                    public boolean isVisible() {
                        return getProntuarioController().isNodePermitted(NodesAtendimentoRef.SOLICITACAO_EXAMES_UNIDADE);
                    }
                });

                linkExames.add(new Image("linkExames", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.TUBES.resourceReference();
                    }
                }));
                linkExames.add(new AttributeModifier("title", BundleManager.getString("exames")));
                linkExames.add(new Label("labelExames", BundleManager.getString("exames")));

                // Lembretes
                containerAcoesPlano.add(linkLembretes = new AbstractAjaxLink("linkLembretes") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        initDlgCadastroLembretesAtendimento(target);
                    }
                });

                linkLembretes.add(new Image("linkLembretes", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.MEDICAL_RECORD.resourceReference();
                    }
                }));
                linkLembretes.add(new AttributeModifier("title", BundleManager.getString("lembretes")));
                linkLembretes.add(new Label("labelLembretes", BundleManager.getString("lembretes")));

                // Prescrição de Medicamentos
                containerAcoesPlano.add(linkPrescricaoMedicamentos = new AbstractAjaxLink("linkPrescricaoMedicamentos") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new ReceituarioPanel(getProntuarioController().panelId(), true, SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                    }

                    @Override
                    public boolean isVisible() {
                        return getProntuarioController().isNodePermitted(NodesAtendimentoRef.RECEITUARIO);
                    }
                });

                linkPrescricaoMedicamentos.add(new Image("linkPrescricaoMedicamentos", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.PILLS.resourceReference();
                    }
                }));
                linkPrescricaoMedicamentos.add(new AttributeModifier("title", BundleManager.getString("prescricaoMedicamentos")));
                linkPrescricaoMedicamentos.add(new Label("labelPrescricaoMedicamentos", BundleManager.getString("prescricaoMedicamentos")));

                // Encaminhamentos
                containerAcoesPlano.add(linkEncaminhamentos = new AbstractAjaxLink("linkEncaminhamentos") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new EncaminhamentoEspecialistaPanel(getProntuarioController().panelId(), false, true, SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                    }

                    @Override
                    public boolean isVisible() {
                        return getProntuarioController().isNodePermitted(NodesAtendimentoRef.ENCAMINHAMENTO_ESPECIALISTA);
                    }
                });

                linkEncaminhamentos.add(new Image("linkEncaminhamentos", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.STETHOSCOPE.resourceReference();
                    }
                }));
                linkEncaminhamentos.add(new AttributeModifier("title", BundleManager.getString("encaminhamentos")));
                linkEncaminhamentos.add(new Label("labelEncaminhamentos", BundleManager.getString("encaminhamentos")));

                // Laudo Apac
                containerAcoesPlano.add(linkLaudoApac = new AbstractAjaxLink("linkLaudoApac") {
                    @Override
                    public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new SolicitacaoApacPanel(getProntuarioController().panelId(), true, SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
                    }

                    @Override
                    public boolean isVisible() {
                        return getProntuarioController().isNodePermitted(NodesAtendimentoRef.SOLICITACAO_APAC);
                    }
                });

                linkLaudoApac.add(new Image("linkLaudoApac", new LoadableDetachableModel<ResourceReference>() {
                    @Override
                    protected ResourceReference load() {
                        return Resources.Images.LAYOUT_HEADER.resourceReference();
                    }
                }));
                linkLaudoApac.add(new AttributeModifier("title", BundleManager.getString("laudoApac")));
                linkLaudoApac.add(new Label("labelLaudoApac", BundleManager.getString("laudoApac")));

            }

            containerPlano.setOutputMarkupId(true);
            getForm().add(containerPlano);
        }

        // editores de texto, manipula-se com javascript para setar o tamanho  horizontal automático.
        txaSubjetivo.add(new EditorBehavior(new EvolucaoEditorSettings(EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));

        txaObjetivo.add(new EditorBehavior(new EvolucaoEditorSettings(EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));

        txaAvaliacao.add(new EditorBehavior(new EvolucaoEditorSettings(EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));

        txaPlano.add(new EditorBehavior(new EvolucaoEditorSettings(EvolucaoEditorSettings.Config.MINIMAL_CONFIG)));

        txaSubjetivo.addAjaxUpdateValue();
        txaObjetivo.addAjaxUpdateValue();
        txaAvaliacao.addAjaxUpdateValue();
        txaPlano.addAjaxUpdateValue();

        txaSubjetivo.add(new TempBehaviorV3());
        txaObjetivo.add(new TempBehaviorV3());
        txaAvaliacao.add(new TempBehaviorV3());
        txaPlano.add(new TempBehaviorV3());
        autoCompleteConsultaCiapMotivoConsulta.add(new TempBehaviorV3());
        autoCompleteConsultaCiapIntervencao.add(new TempBehaviorV3());

        // INICIO DADOS DO ATENDIMENTO
        {

            getForm().add(containerDadosAtendimento = new WebMarkupContainer("containerDadosAtendimento"));
            containerDadosAtendimento.setOutputMarkupPlaceholderTag(true);

            cbxClassificacaoAtendimento = new DropDown(path(proxy.getClassificacaoAtendimento()));

            containerDadosAtendimento.add(dropDownTipoAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getTipoAtendimentoEsus()), EsusFichaAtendIndividualItem.TipoAtendimento.values(), true, false, true));
            containerDadosAtendimento.add(dropDownAtencaoDomiciliar = DropDownUtil.getIEnumDropDown(path(proxy.getAtencaoDomiciliar()), EsusFichaAtendIndividualItem.ModalidadeAD.values(), true, false, true));

            containerDadosAtendimento.add(txtDiasRetorno = new InputField(path(proxy.getDiasRetorno())));
            txtDiasRetorno.addAjaxUpdateValue();

            containerDadosAtendimento.add(cbxCondutaCovid = getCbxCondutaCovid(path(proxy.getCondutaCovid())));
            containerDadosAtendimento.add(cbxConduta = getCbxConduta(path(proxy.getConduta())));
            containerDadosAtendimento.add(new AbstractAjaxLink("btnAdicionarConduta") {

                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    viewDlgInformarConduta(target);
                }
            });

            cbxClassificacaoAtendimento.add(new TempBehaviorV3());
            dropDownTipoAtendimento.add(new TempBehaviorV3());
            dropDownAtencaoDomiciliar.add(new TempBehaviorV3());
            dropDownVacina.add(new TempBehaviorV3());
            txtDiasRetorno.add(new TempBehaviorV3());
            cbxConduta.add(new TempBehaviorV3());
            cbxCondutaCovid.add(new TempBehaviorV3());

            containerDadosAtendimento.add(dropDownRacionalidadeSaude = DropDownUtil.getIEnumDropDown(path(proxy.getRacionalidadeSaude()), EsusFichaAtendIndividualItem.RacionalidadeSaude.values(), true, false, true));
            dropDownRacionalidadeSaude.setLabel(new Model(bundle("racionalidadeSaude"))).add(new TempBehaviorV3());

            DropDown dropDownLocalAtendimento = DropDownUtil.getIEnumDropDown(path(proxy.getLocalAtendimento()), Empresa.LocalAtendimento.values(), false, false, false, true);
            dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL.value());
            dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.UNIDADE_PRONTO_ATENDIMENTO.value());
            dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.CACON_UNACON.value());
            dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_URGENCIA_EMERGENCIA.value());
            dropDownLocalAtendimento.removeChoice(Empresa.LocalAtendimento.HOSPITAL_SOS_DEMAIS_SETORES.value());
            dropDownLocalAtendimento.add(new TempBehaviorV3());
            containerDadosAtendimento.add(dropDownLocalAtendimento);

            validarVisibilidadeCipe();

            containerCipe = new WebMarkupContainer("containerCipe");
            containerCipe.add(autoCompleteConsultaDiagnosticoEnfermagemSae = new AutoCompleteConsultaDiagnosticoEnfermagemSae(path(proxy.getCipe())));
            autoCompleteConsultaDiagnosticoEnfermagemSae.addAjaxUpdateValue();
            autoCompleteConsultaDiagnosticoEnfermagemSae.setOutputMarkupId(true);
            autoCompleteConsultaDiagnosticoEnfermagemSae.add(new TempBehaviorV3());
            containerCipe.add(btnAdicionarCipe = new AbstractAjaxLink("btnAdicionarCipe") {

                @Override
                public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    viewDlgInformarCipe(target);
                }
            });

            containerCipe.setEnabled(form.getModel().getObject().isVisibleCipe());
            containerCipe.setVisible(form.getModel().getObject().isVisibleCipe());
            containerDadosAtendimento.add(containerCipe);

            validarVisibilidadeCid();
            autoCompleteConsultaCidGrupoProblemasCondicoes.setEnabled(getForm().getModel().getObject().isVisibleCid());

            initContainerProfisAuxiliar(proxy);
        }
//            initContainerAlergia(proxy);

        initContainerNasf();

        initContainerRegistroDiarreia(proxy);
        initContainerGestante();

        configuraEventosListeners();

        add(getForm());
        configurePanel();
        // carrega informações do atendimento primário e atendimento MDDA
        carregarInformacoesAuxiliares();

        containerDadosAtendimento.add(getCbxClassificacaoAtendimento());
        cbxClassificacaoAtendimento.addRequiredClass();
        cbxClassificacaoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dlgInformarClassificacaoAtendimento = null;
                target.add(cbxClassificacaoAtendimento);
            }
        });

        containerDadosAtendimento.add(new AbstractAjaxLink("btnAdicionarClassificacaoAtendimento") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgInformarClassificacaoAtendimento(target);
            }
        });
        avaliarGlicemia(null);
    }

    private void adicionaClassificacaoAtendimentoCiap(AjaxRequestTarget target, Ciap ciapProblemaCondicao) {
        try {
            if (ciapProblemaCondicao != null) {
                String cdEsusCiap = CiapHelper.getClassificacaoAtendimentoCodigoAB(ciapProblemaCondicao.getReferencia());
                ClassificacaoAtendimento classificacaoAtendimento = (ClassificacaoAtendimento) HibernateSessionFactory.getSession().createCriteria(ClassificacaoAtendimento.class)
                        .add(Restrictions.eq(ClassificacaoAtendimento.PROP_CODIGO_ESUS, cdEsusCiap))
                        .uniqueResult();

                if (classificacaoAtendimento != null) {
                    if (form.getModel().getObject().getClassificacaoAtendimento() == null) {
                        cbxClassificacaoAtendimento.setComponentValue(classificacaoAtendimento);
                    } else {
                        AtendimentoClassificacaoAtendimento ca = new AtendimentoClassificacaoAtendimento();
                        ca.setClassificacaoAtendimento(classificacaoAtendimento);
                        form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList().add(ca);
                        adicionarClassificacaoAtendimento(form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList());
                    }
                    target.add(cbxClassificacaoAtendimento);
                }
            }
        } catch (DAOException e) {
            e.printStackTrace();
        }
    }

    private void removeClassificacaoAtendimento(AjaxRequestTarget target, Ciap ciapProblemaCondicao) {
        try {
            if (ciapProblemaCondicao != null) {
                String cdEsusCiap = CiapHelper.getClassificacaoAtendimentoCodigoAB(ciapProblemaCondicao.getReferencia());
                ClassificacaoAtendimento classificacaoAtendimento = (ClassificacaoAtendimento) HibernateSessionFactory.getSession().createCriteria(ClassificacaoAtendimento.class)
                        .add(Restrictions.eq(ClassificacaoAtendimento.PROP_CODIGO_ESUS, cdEsusCiap))
                        .uniqueResult();

                if (classificacaoAtendimento != null) {
                    if (classificacaoAtendimento.equals(form.getModel().getObject().getClassificacaoAtendimento())) {
                        cbxClassificacaoAtendimento.setComponentValue(null);

                    } else {
                        List<AtendimentoClassificacaoAtendimento> atendClassifiAtendList = Lambda.select(form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList(),
                                Lambda.having(on(AtendimentoClassificacaoAtendimento.class).getClassificacaoAtendimento().getCodigo(),
                                        Matchers.not(Matchers.equalTo(classificacaoAtendimento.getCodigo()))));
                        adicionarClassificacaoAtendimento(atendClassifiAtendList);
                    }

//                if(form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList().size() > 0) {
//                    if(form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList().get(0).getClassificacaoAtendimento().getCodigoEsus() != null) {
//                        String cdEsusCiapList = form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList().get(0).getClassificacaoAtendimento().getCodigoEsus();
//                        ClassificacaoAtendimento classificacaoAtendimentoList = (ClassificacaoAtendimento) HibernateSessionFactory.getSession().createCriteria(ClassificacaoAtendimento.class)
//                                .add(Restrictions.eq(ClassificacaoAtendimento.PROP_CODIGO_ESUS, cdEsusCiapList))
//                                .uniqueResult();
//                        cbxClassificacaoAtendimento.setComponentValue(classificacaoAtendimentoList);
//                    } else {
//                        cbxClassificacaoAtendimento.setComponentValue(null);
//                    }
//                }

                    target.add(cbxClassificacaoAtendimento);
                }
            }
        } catch (DAOException e) {
            e.printStackTrace();
        }
    }

    private void viewDlgInformarClassificacaoAtendimento(AjaxRequestTarget target) {
        if (dlgInformarClassificacaoAtendimento == null) {
            getProntuarioController().addWindow(target, dlgInformarClassificacaoAtendimento = new DlgInformarClassificacaoAtendimento(getProntuarioController().newWindowId(), form.getModel().getObject().getClassificacaoAtendimentoRemovidosList()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) throws ValidacaoException, DAOException {
                    adicionarClassificacaoAtendimento(atendimentoClassificacaoAtendimentoList);
                    new TempHelperV3().save(form);
                }
            });
        }
        dlgInformarClassificacaoAtendimento.show(target, getAtendimento(), form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList());
    }

    private void adicionarClassificacaoAtendimento(List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) {
        form.getModel().getObject().setAtendimentoClassificacaoAtendimentoList(atendimentoClassificacaoAtendimentoList);
        new TempHelperV3().save(form);
    }

    private WebMarkupContainer getContainerDadosAntropometricos(SoapDTO proxy) {
        WebMarkupContainer containerDadosAntropometricos = new WebMarkupContainer("containerDadosAntropometricos");
        containerDadosAntropometricos.setOutputMarkupPlaceholderTag(true);

        containerDadosAntropometricos.add(getCampoPeso(proxy));
        containerDadosAntropometricos.add(getCampoAltura(proxy));
        containerDadosAntropometricos.add(getlblImc());
        containerDadosAntropometricos.add(getLblSituacaoImc());
        containerDadosAntropometricos.add(getCampoPerimetroCefalico(proxy));
        containerDadosAntropometricos.add(getCampoCircunferenciaAbdominal(proxy));

        containerDadosAntropometricos.add(new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialSistolica())).setLabel(new Model(bundle("pas"))).add(new TempBehaviorV3()));
        containerDadosAntropometricos.add(new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialDiastolica())).setLabel(new Model(bundle("pad"))).add(new TempBehaviorV3()));
        containerDadosAntropometricos.add(new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaCardiaca())).add(new TempBehaviorV3()));
        containerDadosAntropometricos.add(new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaRespiratoria())).add(new TempBehaviorV3()));
        containerDadosAntropometricos.add(new DoubleField(path(proxy.getAtendimentoPrimario().getTemperatura())).setVMax(45D).add(new TempBehaviorV3()));
        containerDadosAntropometricos.add(new LongField(path(proxy.getAtendimentoPrimario().getSaturacaoOxigenio())).add(new TempBehaviorV3()));


        containerDadosAntropometricos.add(getCampoGlicemia(proxy));
        containerDadosAntropometricos.add(getCampoTipoGlicemia(proxy));
        containerDadosAntropometricos.add(getLblAvaliacaoGlicemica());

        containerDadosAntropometricos.add(getDropDownSisvanAlimentacao(path(proxy.getAtendimentoPrimario().getSisvanAlimentacao()), idadeParaValidarAlimentacao, idadeEmMeses, enableBolsaFamilia));

        return containerDadosAntropometricos;
    }

    private void validaSisvanAlimentacao() {
        try {
            idadeEmMeses = Data.getIdadeEmMeses(getAtendimento().getUsuarioCadsus().getDataNascimento());
            idadeParaValidarAlimentacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("idadeParaValidarAlimentacao");

            if (getAtendimento().getEnderecoDomicilio() != null) {
                EnderecoDomicilio enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                        .addProperty(EnderecoDomicilio.PROP_FLAG_BOLSA_FAMILIA)
                        .setId(getAtendimento().getEnderecoDomicilio().getCodigo())
                        .start().getVO();

                enableBolsaFamilia = RepositoryComponentDefault.SIM.equals(enderecoDomicilio.getFlagBolsaFamilia())
                        && idadeEmMeses < idadeParaValidarAlimentacao;
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private Label getLblAvaliacaoGlicemica() {
        lblAvaliacaoGlicemia = new Label("textoAvaliacaoGlicemia", new Model<>());
        lblAvaliacaoGlicemia.setOutputMarkupId(true);
        return lblAvaliacaoGlicemia;
    }

    private DropDown<Long> getCampoTipoGlicemia(SoapDTO proxy) {
        cbxTipoGlicemia = new DropDown<>(path(proxy.getAtendimentoPrimario().getGlicemiaTipo()));
        cbxTipoGlicemia.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM, bundle("emJejum"));
        cbxTipoGlicemia.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL, bundle("posPrandial"));
        cbxTipoGlicemia.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL, bundle("prePrandial"));
        cbxTipoGlicemia.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO, bundle("naoEspecificado"));

        cbxTipoGlicemia.add(new TempBehaviorV3());
        cbxTipoGlicemia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarGlicemia(target);
            }
        });
        return cbxTipoGlicemia;
    }

    private LongField getCampoGlicemia(SoapDTO proxy) {
        txtGlicemia = new LongField(path(proxy.getAtendimentoPrimario().getGlicemia()));
        txtGlicemia.add(new TempBehaviorV3());
        txtGlicemia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarGlicemia(target);
            }
        });
        return txtGlicemia;
    }

    private DoubleField getCampoCircunferenciaAbdominal(SoapDTO proxy) {
        txtCircunferenciaAbdominal = new DoubleField(path(proxy.getAtendimentoPrimario().getCircunferenciaAbdomen()));
        txtCircunferenciaAbdominal.add(new TempBehaviorV3());
        return txtCircunferenciaAbdominal;
    }

    private DoubleField getCampoPerimetroCefalico(SoapDTO proxy) {
        txtPerimetroCefalico = new DoubleField(path(proxy.getAtendimentoPrimario().getPerimetroCefalico()));
        txtPerimetroCefalico.add(new TempBehaviorV3());
        if (getAtendimento().getUsuarioCadsus().getIdade() >= 2L) {
            txtPerimetroCefalico.setEnabled(false);
        }
        return txtPerimetroCefalico;
    }

    private Label getLblSituacaoImc() {
        lblSituacaoImc = new Label("textoSituacaoImc", new PropertyModel<>(this, "textoSituacaoImc"));
        lblSituacaoImc.setOutputMarkupId(true);
        return lblSituacaoImc;
    }

    private Label getlblImc() {
        lblImc = new Label("textoImc", new PropertyModel<>(this, "textoImc"));
        lblImc.setOutputMarkupId(true);
        return lblImc;
    }

    private DoubleField getCampoAltura(SoapDTO proxy) {
        txtAltura = new DoubleField(path(proxy.getAtendimentoPrimario().getAltura()));
        txtAltura.setMDec(1).setVMax(280D).add(new TempBehaviorV3());
        txtAltura.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onEventIMC(target);
            }
        });
        return txtAltura;
    }

    private DoubleField getCampoPeso(SoapDTO proxy) {
        txtPeso = new DoubleField(path(proxy.getAtendimentoPrimario().getPeso()));
        txtPeso.setMDec(3).setVMax(350D).add(new TempBehaviorV3());
        txtPeso.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onEventIMC(target);
            }
        });
        return txtPeso;
    }

    public void onEventIMC(AjaxRequestTarget target) {
        AtendimentoPrimario atendimentoPrimario = getForm().getModel().getObject().getAtendimentoPrimario();
        Long menorIdade = getValorMenorIdade();
        textoImc = "";
        textoSituacaoImc = "";

        if (getAtendimento().getUsuarioCadsus().getIdadeEmMeses() >= menorIdade) {
            Double imc = null;
            double altura = Coalesce.asDouble(atendimentoPrimario.getAltura(), 0D);

            if (altura != 0D && atendimentoPrimario.getPeso() != null) {
                imc = CalculosUtil.calculoImc((altura / 100), atendimentoPrimario.getPeso());
                boolean isPacienteGestante = isPacienteGestante();
                Long idadeGestacional = null;

                if (isPacienteGestante) {
                    idadeGestacional = getIdadeGestacional();
                }

                setSituacaoImc(atendimentoPrimario, imc, isPacienteGestante, idadeGestacional);
            }

            atendimentoPrimario.setImc(imc);
            new TempHelperV3().save(getForm());
        }

        if (target != null) {
            target.add(lblImc);
            target.add(lblSituacaoImc);
        }
    }

    private void setSituacaoImc(AtendimentoPrimario atendimentoPrimario, Double imc, boolean isPacienteGestante, Long idadeGestacional) {
        List<IndiceImc> lstIndiceImc = getIndicesImcs(imc, isPacienteGestante, idadeGestacional);

        if (lstIndiceImc.size() == 1) {
            atendimentoPrimario.setIndiceImc(lstIndiceImc.get(0));
            textoSituacaoImc = lstIndiceImc.get(0).getSituacao();
        } else {
            textoSituacaoImc = bundle("desconhecido");
        }

        textoImc = "IMC: ".concat(Valor.adicionarFormatacaoMonetaria(imc)).concat(" - ");
    }

    private Long getIdadeGestacional() {
        Date dumGestante = (dchDumGestante.getComponentValue() != null ? dchDumGestante.getComponentValue() : getForm().getModel().getObject().getDumGestante());
        if (dumGestante != null) {
            return Data.calcularIdadeGestacional(dumGestante, getAtendimento().getDataChegada());
        }
        return null;
    }

    private void setIdadeGestacional(AjaxRequestTarget target) {

        Date dumGestante = (dchDumGestante.getComponentValue() != null ? dchDumGestante.getComponentValue() : getForm().getModel().getObject().getDumGestante());
        if (dumGestante != null) {
            txtIdadeGestacionalDum.setComponentValue(Data.calcularIdadeGestacional(dumGestante, getAtendimento().getDataChegada()).toString().concat(" semanas"));
            if (target != null) {
                target.add(txtIdadeGestacionalDum);
            }
        }
    }

    private boolean isPacienteGestante() {
        return UsuarioCadsusHelper.isGestante(getAtendimento().getUsuarioCadsus().getCodigo());
    }

    private List<IndiceImc> getIndicesImcs(Double imc, boolean gestante, Long idadeGestacional) {
        LoadManager load = LoadManager.getInstance(IndiceImc.class)
                .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, imc))
                .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, imc))
                .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_SEXO, BuilderQueryCustom.QueryParameter.IGUAL, getAtendimento().getUsuarioCadsus().getSexo(), HQLHelper.RESOLVE_CHAR_TYPE, getAtendimento().getUsuarioCadsus().getSexo()));
        if (gestante) {
            load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_GESTANTE, gestante ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG));
            load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_GESTACIONAL, BuilderQueryCustom.QueryParameter.IGUAL, idadeGestacional));
        } else {
            load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
            load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
        }

        return load.start().getList();
    }

    private Long getValorMenorIdade() {
        return LoadManager.getInstance(IndiceImc.class)
                .addGroup(new QueryCustom.QueryCustomGroup(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryGroup.MIN))
                .start().getVO();
    }

    private void avaliarGlicemia(AjaxRequestTarget target) {
        try {
            IParameterModuleContainer ipmc = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            Long tipoGlicemia = getForm().getModel().getObject().getAtendimentoPrimario().getGlicemiaTipo();
            Long glicemiaInformada = getForm().getModel().getObject().getAtendimentoPrimario().getGlicemia();

            if (AtendimentoPrimario.TIPO_GLICEMIA_JEJUM.equals(tipoGlicemia)) {
                Long glicemiaPosJejumInicial = ipmc.getParametro("GlicemiaPosJejumInicial");
                Long glicemiaPosJejumFinal = ipmc.getParametro("GlicemiaPosJejumFinal");
                atualizaLabelGlicemia(glicemiaPosJejumInicial, glicemiaPosJejumFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPrePrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPrePrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPosPrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPosPrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO.equals(tipoGlicemia)) {
                atualizaLabelGlicemia(null, null, glicemiaInformada);

            } else {
                atualizaLabelGlicemia(null, null, null);
            }

            boolean enable = !Coalesce.asLong(getForm().getModel().getObject().getAtendimentoPrimario().getGlicemia()).equals(0L);
            cbxTipoGlicemia.setEnabled(enable);
            if (target != null) {
                target.add(lblAvaliacaoGlicemia);
                target.add(cbxTipoGlicemia);
                if (!enable) {
                    cbxTipoGlicemia.limpar(target);
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void atualizaLabelGlicemia(Long glicemiaInicial, Long glicemiaFinal, Long glicemiaInformada) {
        if (Coalesce.asLong(glicemiaInformada) == 0L) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return "";
                }
            });
            setLabelNormal();
        } else if (glicemiaInicial == null || glicemiaFinal == null) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("desconhecido");
                }
            });
            setLabelNormal();
        } else if (glicemiaInformada >= glicemiaInicial && glicemiaInformada <= glicemiaFinal) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("normal");
                }
            });
            setLabelNormal();
        } else {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("foraNormalidade");
                }
            });
            setLabelVermelho();
        }
    }

    private void setLabelNormal() {
        if (lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.remove(modifierVermelho);
        }
    }

    private void setLabelVermelho() {
        if (!lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.add(modifierVermelho);
        }
    }

    private DropDown getDropDownTipoDocumentoAtendimento() {
        if (dropDownTipoDocumentoAtendimento == null) {
            dropDownTipoDocumentoAtendimento = new DropDown<TipoDocumentoAtendimento>("tipoDocumento", new Model());
            List<TipoDocumentoAtendimento> lstTipo = LoadManager.getInstance(TipoDocumentoAtendimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoDocumentoAtendimento.PROP_TIPO_MODELO, TipoDocumentoAtendimento.TipoDocumento.TIPO_EVOLUCAO.value()))
                    .start().getList();

            dropDownTipoDocumentoAtendimento.addChoice(null, "");

            for (TipoDocumentoAtendimento tipoDocumentoAtendimento : lstTipo) {
                dropDownTipoDocumentoAtendimento.addChoice(tipoDocumentoAtendimento, tipoDocumentoAtendimento.getDescricao());
            }

            dropDownTipoDocumentoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    TipoDocumentoAtendimento object = dropDownTipoDocumentoAtendimento.getModelObject();
                    txaSubjetivo.limpar(target);
                    if (object != null) {
                        String text = TextEditorHelper.builder(object.getModelo())
                                .setProfissional(getAtendimento().getProfissional())
                                .setUsuarioCadsus(getAtendimento().getUsuarioCadsus())
                                .setDataHoraChegada(getAtendimento().getDataChegada())
                                .setEmpresa(ApplicationSession.get().getSession().getEmpresa())
                                .parse();

                        form.getModelObject().setDescricaoEvolucao(Coalesce.asString(form.getModelObject().getDescricaoEvolucao()) + " \n " + text);
                        target.add(txaSubjetivo);
                        new TempHelperV3().save(form);
                    }
                }
            });
        }
        return dropDownTipoDocumentoAtendimento;
    }

    private Form<SoapDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel<>(new SoapDTO()));
        }
        return form;
    }

    private List<IColumn> getColumnsGrupoProblemasCondicoes() {
        List<IColumn> columns = new ArrayList<>();
        GrupoProblemasCondicoes proxy = on(GrupoProblemasCondicoes.class);

        columns.add(getActionColumnGrupoProblemasCondicoes());
        columns.add(createColumn(bundle("ciap"), proxy.getCiap().getDescricaoVO()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getDescricaoFormatado()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoFormatada()));

        return columns;
    }

    private List<IColumn> getColumnsTabelaCiap2MotivoConsulta() {
        List<IColumn> columns = new ArrayList<>();
        AtendimentoSoapCiap proxy = on(AtendimentoSoapCiap.class);

        columns.add(getActionColumnCiap2MotivoConsulta());
        columns.add(createColumn(bundle("ciap"), proxy.getCiap().getDescricaoVO()));

        return columns;
    }

    private List<IColumn> getColumnCiapIntervencao() {
        List<IColumn> columns = new ArrayList<>();
        AtendimentoSoapCiap proxy = on(AtendimentoSoapCiap.class);

        columns.add(getActionColumnCiapIntervencao());
        columns.add(createColumn(bundle("ciap"), proxy.getCiap().getDescricaoVO()));

        return columns;
    }

    private IColumn getActionColumnGrupoProblemasCondicoes() {
        return new MultipleActionCustomColumn<GrupoProblemasCondicoes>() {
            @Override
            public void customizeColumn(GrupoProblemasCondicoes rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<GrupoProblemasCondicoes>() {
                    @Override
                    public void action(AjaxRequestTarget target, GrupoProblemasCondicoes modelObject) throws ValidacaoException, DAOException {
                        removerGrupoProblemasCondicoes(target, modelObject);
                    }
                });
            }
        };
    }

    private IColumn getActionColumnCiap2MotivoConsulta() {
        return new MultipleActionCustomColumn<AtendimentoSoapCiap>() {
            @Override
            public void customizeColumn(AtendimentoSoapCiap rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoSoapCiap>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoSoapCiap modelObject) throws ValidacaoException, DAOException {
                        removerCiap2MotivoConsulta(target, modelObject);
                    }
                });
            }
        };
    }

    private IColumn getActionColumnCiapIntervencao() {
        return new MultipleActionCustomColumn<AtendimentoSoapCiap>() {
            @Override
            public void customizeColumn(AtendimentoSoapCiap rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoSoapCiap>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoSoapCiap modelObject) throws ValidacaoException, DAOException {
                        removerCiapIntervencao(target, modelObject);
                    }
                });
            }
        };
    }

    private void removerGrupoProblemasCondicoes(AjaxRequestTarget target, GrupoProblemasCondicoes grupoProblemasCondicoes) {
        limparGrupoProblemasCondicoes(target);
        for (int i = 0; i < getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().size(); i++) {
            if (getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().get(i) == grupoProblemasCondicoes) {
                removeClassificacaoAtendimento(target, getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().get(i).getCiap());
                getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().remove(i);
                new TempHelperV3().save(getForm());
                break;
            }
        }
        tblGrupoProblemasCondicoes.update(target);
    }

    private void removerCiap2MotivoConsulta(AjaxRequestTarget target, AtendimentoSoapCiap atendimentoSoapCiap) {
        for (int i = 0; i < getForm().getModel().getObject().getAtendimentoSoapCiapListMotivoConsulta().size(); i++) {
            if (atendimentoSoapCiap.getCiap().equals(getForm().getModel().getObject().getAtendimentoSoapCiapListMotivoConsulta().get(i).getCiap())) {
                getForm().getModel().getObject().getAtendimentoSoapCiapListMotivoConsulta().remove(i);
                new TempHelperV3().save(getForm());
                break;
            }
        }

        tblCiap2MotivoConsulta.update(target);

        autoCompleteConsultaCiapMotivoConsulta.limpar(target);
    }

    private void removerCiapIntervencao(AjaxRequestTarget target, AtendimentoSoapCiap atendimentoSoapCiap) {
        for (int i = 0; i < getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao().size(); i++) {
            if (atendimentoSoapCiap.getCiap().equals(getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao().get(i).getCiap())) {
                getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao().remove(i);
                new TempHelperV3().save(getForm());
                break;
            }
        }
        tblCiapIntervencao.update(target);
        autoCompleteConsultaCiapIntervencao.limpar(target);
    }

    private ICollectionProvider getCollectionProviderGrupoProblemasCondicoes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList();
            }
        };
    }

    private ICollectionProvider getCollectionProviderTabelaCiap2MotivoConsulta() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getAtendimentoSoapCiapListMotivoConsulta();
            }
        };
    }

    private ICollectionProvider getCollectionProviderCiapIntervencao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao();
            }
        };
    }

    private void adicionarGrupoProblemasCondicoes(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        GrupoProblemasCondicoes gpc = modelGrupoProblemasCondicoes.getObject();

        if (getForm().getModel().getObject().isVisibleCid() && gpc.getCid() == null && gpc.getCiap() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_mensagem_cid_ciap"));
        } else if (!getForm().getModel().getObject().isVisibleCid() && gpc.getCiap() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menos_um_ciap"));
        }
        validarCondicaoCiap(gpc.getCiap(), gpc.getCid());

        gpc.setUsuarioCadSus(getAtendimento().getUsuarioCadsus());
        gpc.setAtendimento(getAtendimento());

        getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().add(gpc);

        adicionaClassificacaoAtendimentoCiap(target, gpc.getCiap());

        new TempHelperV3().save(getForm());

        limparGrupoProblemasCondicoes(target);
        tblGrupoProblemasCondicoes.update(target);
        target.focusComponent(autoCompleteConsultaCiapGrupoProblemasCondicoes.getTxtDescricao().getTextField());
    }

    private void adicionarCiap2MotivoConsulta(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        Ciap ciap = (Ciap) autoCompleteConsultaCiapMotivoConsulta.getModelObject();
        if (ciap == null) {
            throw new ValidacaoException(bundle("informeCiap"));
        }

        validarCiapSexoPaciente(ciap);

        List<AtendimentoSoapCiap> atendimentoSoapCiapList = getForm().getModel().getObject().getAtendimentoSoapCiapListMotivoConsulta();

        for (AtendimentoSoapCiap item : atendimentoSoapCiapList) {
            if (item.getCiap().getCodigo().equals(ciap.getCodigo())) {
                throw new ValidacaoException(bundle("ciapJaAdicionado"));
            }
        }

        AtendimentoSoapCiap ca = new AtendimentoSoapCiap();
        ca.setCiap(ciap);
        atendimentoSoapCiapList.add(ca);

        getForm().getModel().getObject().setAtendimentoSoapCiapListMotivoConsulta(atendimentoSoapCiapList);

        new TempHelperV3().save(getForm());

        autoCompleteConsultaCiapMotivoConsulta.limpar(target);
        tblCiap2MotivoConsulta.update(target);
        target.focusComponent(autoCompleteConsultaCiapMotivoConsulta.getTxtDescricao().getTextField());
    }

    public void validarCiapSexoPaciente(Ciap ciap) throws ValidacaoException {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, getAtendimento());
        if (ciapInvalido) {
            throw new ValidacaoException(Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
        }
    }

    private void adicionarCiapIntervencao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        Ciap ciap = (Ciap) autoCompleteConsultaCiapIntervencao.getModelObject();
        if (ciap == null) {
            throw new ValidacaoException(bundle("informeCiap"));
        }

        validarCiapSexoPaciente(ciap);

        List<AtendimentoSoapCiap> atendimentoSoapCiapList = getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao();

        for (AtendimentoSoapCiap item : atendimentoSoapCiapList) {
            if (item.getCiap().getCodigo().equals(ciap.getCodigo())) {
                throw new ValidacaoException(bundle("ciapJaAdicionado"));
            }
        }

        AtendimentoSoapCiap ca = new AtendimentoSoapCiap();
        ca.setCiap(ciap);

        atendimentoSoapCiapList.add(ca);

        getForm().getModel().getObject().setAtendimentoSoapCiapListIntervencao(atendimentoSoapCiapList);

        new TempHelperV3().save(getForm());

        autoCompleteConsultaCiapIntervencao.limpar(target);
        tblCiapIntervencao.update(target);
        target.focusComponent(autoCompleteConsultaCiapIntervencao.getTxtDescricao().getTextField());
    }

    public void limparGrupoProblemasCondicoes(AjaxRequestTarget target) {
        modelGrupoProblemasCondicoes.setObject(new GrupoProblemasCondicoes());
        autoCompleteConsultaCiapGrupoProblemasCondicoes.limpar(target);
        autoCompleteConsultaCidGrupoProblemasCondicoes.limpar(target);
        dropDownSituacaoGrupoProblemasCondicoes.limpar(target);
    }

    private void validarCondicaoCiap(Ciap ciap, Cid cid) throws ValidacaoException {
        validatItemJaAdicionado(ciap, cid);
        validarCiapSexoPaciente(ciap);


        if (RepositoryComponentDefault.SIM_LONG.equals(checkBoxAdicionarListaProblemas.getComponentValue())) {
            List<GrupoProblemasCondicoes> grupoProblemasCondicoes = LoadManager.getInstance(GrupoProblemasCondicoes.class)
                    .addProperties(new HQLProperties(GrupoProblemasCondicoes.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(GrupoProblemasCondicoes.PROP_USUARIO_CAD_SUS, getAtendimento().getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(GrupoProblemasCondicoes.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, GrupoProblemasCondicoes.SITUACAO_INATIVO))
                    .start().getList();

            for (GrupoProblemasCondicoes item : grupoProblemasCondicoes) {
                if (item != null && ciap != null && item.getCiap() != null) {
                    if (item.getCiap().getCodigo().equals(ciap.getCodigo())) {
                        throw new ValidacaoException(BundleManager.getString("ciapJaAdicionada"));
                    }
                }
            }

            for (GrupoProblemasCondicoes item : grupoProblemasCondicoes) {
                if (item != null && cid != null && item.getCid() != null) {
                    if (item.getCid().getCodigo().equals(cid.getCodigo())) {
                        throw new ValidacaoException(BundleManager.getString("cidJaAdicionada"));
                    }
                }
            }
        }
    }

    private void validatItemJaAdicionado(Ciap ciap, Cid cid) throws ValidacaoException {
        List<GrupoProblemasCondicoes> grupoProblemasCondicoesList = getForm().getModel().getObject().getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList();
        boolean cidJaAdicionado = Lambda.exists(grupoProblemasCondicoesList, Lambda.having(on(GrupoProblemasCondicoes.class).getCid(), Matchers.equalTo(cid)).and(Lambda.having(on(GrupoProblemasCondicoes.class).getCid(), Matchers.notNullValue())));
        boolean ciapJaAdicionado = Lambda.exists(grupoProblemasCondicoesList, Lambda.having(on(GrupoProblemasCondicoes.class).getCiap(), Matchers.equalTo(ciap)).and(Lambda.having(on(GrupoProblemasCondicoes.class).getCiap(), Matchers.notNullValue())));

        if (cidJaAdicionado) throw new ValidacaoException(BundleManager.getString("cidJaAdicionada"));
        if (ciapJaAdicionado) throw new ValidacaoException(BundleManager.getString("ciapJaAdicionada"));
    }

    private void carregarInformacoesAuxiliares() {
        if (getForm().getModel().getObject().isCarregarAtendimentoPrincipal()) {
            AtendimentoPrimario atendimentoPrimario = getForm().getModel().getObject().getAtendimentoPrimario();
            if (atendimentoPrimario == null || atendimentoPrimario.getCodigo() == null) {
                atendimentoPrimario = carregarAtendimentoPrimario(getAtendimento().getAtendimentoPrincipal());
                if (atendimentoPrimario == null) {
                    atendimentoPrimario = new AtendimentoPrimario();
                    atendimentoPrimario.setAtendimento(getAtendimento());
                }
                getForm().getModel().getObject().setAtendimentoPrimario(atendimentoPrimario);
                getForm().getModel().getObject().setCarregarAtendimentoPrincipal(false);
            }
        }

        onEventIMC(null);

        new TempHelperV3().save(getForm());
    }

    private void configurePanel() {
        EquipeProfissional equipeProfissional = LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(EquipeProfissional.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATENDIMENTO_NASFS))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, getAtendimento().getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), getAtendimento().getEmpresa()))
                .setMaxResults(1).start().getVO();

        if (equipeProfissional == null || (equipeProfissional != null && equipeProfissional.getEquipe() != null
                && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(equipeProfissional.getEquipe().getAtendimentoNasfs())))) {
            containerNasf.setVisible(false);
            getForm().getModel().getObject().setNasfs(0L);
            new TempHelperV3().save(getForm());
        }

        if (getForm().getModel().getObject().isCarregarCondutaLocalAtendimento()) {
            if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getConduta() != null) {
                getForm().getModel().getObject().setConduta(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getConduta());
            }
            if (getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getLocalAtendimento() != null && getForm().getModel().getObject().getLocalAtendimento() == null) {
                getForm().getModel().getObject().setLocalAtendimento(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getLocalAtendimento());
            }

            Long tipoAtendimentoEsus = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoEsus();
            if (tipoAtendimentoEsus != null) {
                if (TipoAtendimento.TipoAtendimentoEsus.ESCUTA_INICIAL_ORIENTACAO.value().equals(tipoAtendimentoEsus)) {
                    getForm().getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.ESCUTA_INICIAL_ORIENTACAO.value());
                } else if (TipoAtendimento.TipoAtendimentoEsus.CONSULTA_DIA.value().equals(tipoAtendimentoEsus)) {
                    getForm().getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_NO_DIA.value());
                } else if (TipoAtendimento.TipoAtendimentoEsus.ATENDIMENTO_URGENCIA.value().equals(tipoAtendimentoEsus)) {
                    getForm().getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.ATENDIMENTO_URGENCIA.value());
                } else if (TipoAtendimento.TipoAtendimentoEsus.CONSULTA_AGENDADA.value().equals(tipoAtendimentoEsus)) {
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_AGENDADA.value());
                } else if (TipoAtendimento.TipoAtendimentoEsus.CONSULTA_AGENDADA_PROGRAMADA_CUIDADO_CONTINUADO.value().equals(tipoAtendimentoEsus)) {
                    form.getModel().getObject().setTipoAtendimentoEsus((Long) EsusFichaAtendIndividualItem.TipoAtendimento.CONSULTA_AGENDADA_PROGRAMADA_CUIDADO_CONTINUADO.value());
                }
            }

            getForm().getModel().getObject().setCarregarCondutaLocalAtendimento(false);
            new TempHelperV3().save(getForm());
        }

        if (getForm().getModel().getObject().getAtendimentoCompartilhado() != null) {
            containerProfissionalAuxiliar.setVisible(RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getAtendimentoCompartilhado()));
        }

        if (TipoAtendimento.TipoClassificacao.ODONTOLOGICA.value().equals(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao())) {
            containerDadosAtendimento.setVisible(false);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getDiarreia())) {
            response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerRegistroDiarreia)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerRegistroDiarreia)));
        }

//        response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerAlergia)));
//        carregaCiapAtendimentoPrimario();
        response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerGestante)));
        response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerGraficos)));

        if (containerTelaSoap != null) {
            if (SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.value().equals(containerTelaSoap.value())) {
                response.render(OnLoadHeaderItem.forScript("window.scrollTo(0, $('#" + containerAcoesPlano.getMarkupId() + "').offset().top);"));
            } else if (SoapDTO.ContainerTelaSoap.CONTAINER_EXAMES.value().equals(containerTelaSoap.value())) {
                response.render(OnLoadHeaderItem.forScript("window.scrollTo(0, $('#" + containerExames.getMarkupId() + "').offset().top);"));
            }
        }

        response.render(OnDomReadyHeaderItem.forScript(getShortchut()));

    }

    private String getShortchut() {

        String sb = "\n" +
                "function checkEventObj(_event_) {\n" +
                "    // --- IE explorer \n" +
                "    if (window.event)\n" +
                "        return window.event;\n" +
                "    // --- Netscape and other explorers \n" +
                "    else\n" +
                "        return _event_;\n" +
                "}\n" +
                "\n" +
                "function execute(_event_) {\n" +
                "\n" +
                "    // --- Retrieve event object from current web explorer s\n" +
                "\n" +
                "    var winObj = checkEventObj(_event_);\n" +
                "\n" +
                "    var intKeyCode = winObj.keyCode;\n" +
                "    var intAltKey = winObj.altKey;\n" +
                "    var intCtrlKey = winObj.ctrlKey;\n" +
                "\n" +
                "    if (intAltKey) {\n" +
                "        if (intKeyCode == 83) { // 83 = S\n" +
                "            focusSubjetivo();" +
                "        }\n" +
                "        if (intKeyCode == 79) { // 79 = O\n" +
                "            focusObjetivo();" +
                "        }\n" +
                "        if (intKeyCode == 65) { // 65 = A\n" +
                "            focusAvaliacao();" +
                "        }\n" +
                "        if (intKeyCode == 80) { // 80 = P\n" +
                "            focusPlano();" +
                "        }\n" +
                "        if (intKeyCode == 76) { // 76 = L\n" +
                "            focusGrupoProblemasCondicao();" +
                "        }\n" +

                "        if (intKeyCode == 90) { // 90 = Z - DOCUMENTOS\n" +
                "            clickDocumentos();" +
                "        }\n" +
                "        if (intKeyCode == 88) { // 88 = X - EXAMES\n" +
                "            clickExames();" +
                "        }\n" +
                "        if (intKeyCode == 67) { // 67 = C - LEMBRETES\n" +
                "            clickLembretes();" +
                "        }\n" +
                "        if (intKeyCode == 86) { // 86 = V RECEITUÁRIO\n" +
                "            clickReceituario();" +
                "        }\n" +
                "        if (intKeyCode == 66) { // 66 = B - ENCAMINHAMENTO ESPECIALISTA\n" +
                "            clickEncaminhamentoEspecialista();" +
                "        }\n" +
                "        if (intKeyCode == 78) { // 78 = N - LAUDO APAC\n" +
                "            clickLaudoApac();" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "document.onkeydown = execute;" +
                "var focusSubjetivo = function() {" +
                "var _obj = tinyMCE.editors['" + txaSubjetivo.getMarkupId() + "'];" +
                "_obj.focus();" +
                "_obj.selection.select(_obj.getBody(), true);" +
                "_obj.selection.collapse(false);" +
                "var offset = $('#" + containerSubjetivo.getMarkupId() + "').offset();" +
                "window.scrollTo(0, offset.top);" +
                "};" +
                "var focusObjetivo = function() {" +
                "var _obj = tinyMCE.editors['" + txaObjetivo.getMarkupId() + "'];" +
                "_obj.focus();" +
                "_obj.selection.select(_obj.getBody(), true);" +
                "_obj.selection.collapse(false);" +
                "var offset = $('#" + containerObjetivo.getMarkupId() + "').offset();" +
                "window.scrollTo(0, offset.top);" +
                "};" +
                "var focusAvaliacao = function() {" +
                "var _obj = tinyMCE.editors['" + txaAvaliacao.getMarkupId() + "'];" +
                "_obj.focus();" +
                "_obj.selection.select(_obj.getBody(), true);" +
                "_obj.selection.collapse(false);" +
                "var offset = $('#" + containerAvaliacao.getMarkupId() + "').offset();" +
                "window.scrollTo(0, offset.top);" +
                "};" +
                "var focusPlano = function() {" +
                "var _obj = tinyMCE.editors['" + txaPlano.getMarkupId() + "'];" +
                "_obj.focus();" +
                "_obj.selection.select(_obj.getBody(), true);" +
                "_obj.selection.collapse(false);" +
                "var offset = $('#" + containerPlano.getMarkupId() + "').offset();" +
                "window.scrollTo(0, offset.top);" +
                "};" +
                "var focusGrupoProblemasCondicao = function() {" +
                "var offset = $('#" + containerGrupoProblemasCondicao.getMarkupId() + "').offset();" +
                "window.scrollTo(0, offset.top);" +
                "};" +
                "var clickDocumentos = function() {" +
                "$('#" + linkAtestados.getMarkupId() + "').click();" +
                "};" +
                "var clickExames = function() {" +
                "$('#" + linkExames.getMarkupId() + "').click();" +
                "};" +
                "var clickEncaminhamentoEspecialista = function() {" +
                "$('#" + linkEncaminhamentos.getMarkupId() + "').click();" +
                "};" +
                "var clickLembretes = function() {" +
                "$('#" + linkLembretes.getMarkupId() + "').click();" +
                "};" +
                "var clickLaudoApac = function() {" +
                "$('#" + linkLaudoApac.getMarkupId() + "').click();" +
                "};" +
                "var clickReceituario = function() {" +
                "$('#" + linkPrescricaoMedicamentos.getMarkupId() + "').click();" +
                "};";

        return sb;
    }

    private void configuraEventosListeners() {
//        txtDiasRetorno.add(new AjaxFormComponentUpdatingBehavior("onchange") {
//            @Override
//            protected void onUpdate(AjaxRequestTarget target) {
//                updateCbxConduta(Coalesce.asLong(txtDiasRetorno.getComponentValue()) > 0);
//                target.add(cbxConduta);
//            }
//        });
    }

    private void initContainerNasf() {
        containerNasf = new WebMarkupContainer("containerNasf");
        containerNasf.setOutputMarkupId(true);

        containerNasf.add(checkBoxNasfAvaliacaoDiagnostico = new CheckBoxLongValue("nasfAvaliacaoDiagnostico", EsusFichaAtendIndividualItem.Nasf.AVALIACAO_DIAGNOSTICO.sum(), new Model<Long>()));
        containerNasf.add(checkBoxNasfProcedimentosClinicosTerapeuticos = new CheckBoxLongValue("nasfProcedimentosClinicosTerapeuticos", EsusFichaAtendIndividualItem.Nasf.PROCEDIMENTOS_CLINICOS_TERAPEUTICOS.sum(), new Model<Long>()));
        containerNasf.add(checkBoxNasfPrescricaoTerapeutica = new CheckBoxLongValue("nasfPrescricaoTerapeutica", EsusFichaAtendIndividualItem.Nasf.PRESCRICAO_TERAPEUTICA.sum(), new Model<Long>()));

        checkBoxNasfAvaliacaoDiagnostico.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        checkBoxNasfProcedimentosClinicosTerapeuticos.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        checkBoxNasfPrescricaoTerapeutica.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atualizarNasf(target);
            }
        });

        lstCheckBoxNasf.add(checkBoxNasfAvaliacaoDiagnostico);
        lstCheckBoxNasf.add(checkBoxNasfProcedimentosClinicosTerapeuticos);
        lstCheckBoxNasf.add(checkBoxNasfPrescricaoTerapeutica);

        containerDadosAtendimento.add(containerNasf);

        new TempHelperV3().save(getForm());

        CheckBoxUtil.selecionarSomatorio(lstCheckBoxNasf, Coalesce.asLong(getForm().getModel().getObject().getNasfs()));
    }

    private UsuarioCadsusDado getUsuarioCadsusDado() {
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperty(UsuarioCadsusDado.PROP_IDADE_GESTACIONAL)
                .addProperty(UsuarioCadsusDado.PROP_GESTANTE)
                .addProperty(UsuarioCadsusDado.PROP_DUM)
                .setId(getAtendimento().getUsuarioCadsus().getCodigo())
                .start().getVO();

        if (usuarioCadsusDado == null) {
            try {
                usuarioCadsusDado = new UsuarioCadsusDado();
                usuarioCadsusDado.setCodigo(getAtendimento().getUsuarioCadsus().getCodigo());
                usuarioCadsusDado.setGestante(getAtendimento().getFlagGestante());
                usuarioCadsusDado.setIdadeGestacional(getAtendimento().getIdadeGestacional());
                usuarioCadsusDado.setDataDados(DataUtil.getDataAtual());
                BOFactoryWicket.save(usuarioCadsusDado);
            } catch (DAOException | ValidacaoException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }

        return usuarioCadsusDado;
    }

    private PreNatal getPreNatal() {
        PreNatal preNatal = new PreNatal();

        if (RepositoryComponentDefault.SIM_LONG.equals(getUsuarioCadsusDado().getGestante())) {
            preNatal = LoadManager.getInstance(PreNatal.class)
                    .addProperty(PreNatal.PROP_CODIGO)
                    .addProperty(PreNatal.PROP_DATA_ULTIMA_MENSTRUACAO)
                    .addProperty(PreNatal.PROP_GRAVIDEZ_PLANEJADA)
                    .addProperty(PreNatal.PROP_PARTOS)
                    .addProperty(PreNatal.PROP_GESTACOES)
                    .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), getAtendimento().getUsuarioCadsus().getCodigo()))
                    .start().getVO();
        }

        return preNatal;

    }


    private void initContainerGestante() {
        if (isPacienteGestante() && !getForm().getModel().getObject().getJaCarregouPreNatal()) {   //getForm().getModel().getObject().isCarregarDadosPreNatal()) {
            UsuarioCadsusDado usuarioCadsusDado = getUsuarioCadsusDado();

            if (usuarioCadsusDado != null) {
                PreNatal preNatal = getPreNatal();
                if (preNatal != null) {
                    if (preNatal.getDataUltimaMenstruacao() != null) {
                        dchDumGestante.setComponentValue(preNatal.getDataUltimaMenstruacao());
                    }
                    if (preNatal.getGravidezPlanejada() != null) {
                        dropDownGravidezPlanejada.setComponentValue(preNatal.getGravidezPlanejada());
                    }
                    if (preNatal.getPartos() != null) {
                        txtNumeroPartos.setComponentValue(preNatal.getPartos());
                    }
                    if (preNatal.getPartos() != null) {
                        txtNumeroGestasPrevias.setComponentValue(preNatal.getGestacoes());
                    }
                    if (preNatal.getPartos() != null) {
                        txtNumeroGestasPrevias.setComponentValue(preNatal.getGestacoes());
                    }
                }

                if (getForm().getModel().getObject().getDumGestante() == null) {
                    dchDumGestante.setComponentValue(AtendimentoHelper.getUltimoAtendimentoGestante(getAtendimento()));
                }

                //getForm().getModel().getObject().setCarregarDadosPreNatal(false);
                getForm().getModel().getObject().setJaCarregouPreNatal(true);
                new TempHelperV3().save(getForm());
            }
        }


        if (getAtendimento().getUsuarioCadsus().getSexo() != null) {
            sexo = getAtendimento().getUsuarioCadsus().getSexo();
        }

        if (UsuarioCadsus.SEXO_MASCULINO.equals(sexo)) {
            dchDumGestante.setEnabled(false);
            dropDownGravidezPlanejada.setEnabled(false);
            txtNumeroGestasPrevias.setEnabled(false);
            txtNumeroPartos.setEnabled(false);
        } else {
            dchDumGestante.setEnabled(true);
            dropDownGravidezPlanejada.setEnabled(true);
            txtNumeroGestasPrevias.setEnabled(true);
            txtNumeroPartos.setEnabled(true);
            setDtPrimeiraUSGeDPPUSG();
            setIdadeGestacional(null);
        }

        if (dchDppDum.getComponentValue() == null)
            calculaDppDum(null);


    }


    private void setDtPrimeiraUSGeDPPUSG() {

        Atendimento atendimentoOld = AtendimentoHelper.getUltimoAtendimentoGestanteInfo(getAtendimento());
        if (atendimentoOld != null) {
            if (atendimentoOld.getDataPrimeiraUsg() != null) {
                dchPrimeiraUsg.setComponentValue(atendimentoOld.getDataPrimeiraUsg());
            }
            if (atendimentoOld.getDppUsg() != null) {
                dchDppUsg.setComponentValue(atendimentoOld.getDppUsg());
            }
        }
    }


    private void initContainerRegistroDiarreia(SoapDTO proxy) {
        getForm().add(containerRegistroDiarreia = new WebMarkupContainer("containerRegistroDiarreia"));
        containerRegistroDiarreia.setOutputMarkupId(true);

        containerRegistroDiarreia.add(cbxDiarreia = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDiarreia())));
        cbxDiarreia.addAjaxUpdateValue();
        cbxDiarreia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enabled = RepositoryComponentDefault.SIM_LONG.equals(cbxDiarreia.getComponentValue());
                enabledFieldsDiarreia(target, enabled);
            }
        });

        containerRegistroDiarreia.add(cbxComSangue = DropDownUtil.getNaoSimLongDropDown(path(proxy.getAtendimentoMDDA().getComSangue()), true));
        containerRegistroDiarreia.add(chDataPrimeirosSintomas = new DateChooser(path(proxy.getAtendimentoMDDA().getDataPrimeirosSintomas())));
        containerRegistroDiarreia.add(txtResultado = new InputField(path(proxy.getAtendimentoMDDA().getResultadoExameLaboratorial())));
        containerRegistroDiarreia.add(cbxPlanoTratamento = DropDownUtil.getEnumDropDown(path(proxy.getAtendimentoMDDA().getPlanoTratamento()), AtendimentoMDDA.PlanoTratamento.values(), ""));

        cbxComSangue.add(new TempBehaviorV3());
        chDataPrimeirosSintomas.add(new TempBehaviorV3());
        txtResultado.add(new TempBehaviorV3());
        cbxPlanoTratamento.add(new TempBehaviorV3());
        cbxDiarreia.add(new TempBehaviorV3());

        carregarAtendimentoMDDA();

        boolean enabled = RepositoryComponentDefault.SIM_LONG.equals(cbxDiarreia.getComponentValue());
        enabledFieldsDiarreia(null, enabled);


    }


    private void initContainerProfisAuxiliar(SoapDTO proxy) {
        containerDadosAtendimento.add(checkAtendimentoCompartilhado = new CheckBoxLongValue(path(proxy.getAtendimentoCompartilhado())));
        checkAtendimentoCompartilhado.setOutputMarkupId(true);
        checkAtendimentoCompartilhado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getAtendimentoCompartilhado())) {
                    containerProfissionalAuxiliar.setVisible(true);
                } else {
                    containerProfissionalAuxiliar.setVisible(false);
                    autoCompleteConsultaProfissional.limpar(target);
                    cbxCboProfissionalAuxiliar.removeAllChoices();
                    cbxCboProfissionalAuxiliar.limpar(target);

                    new TempHelperV3().save(autoCompleteConsultaProfissional);
                    new TempHelperV3().save(cbxCboProfissionalAuxiliar);
                }
                target.add(containerProfissionalAuxiliar);
                new TempHelperV3().save(checkAtendimentoCompartilhado);
            }
        });

        containerDadosAtendimento.add(containerProfissionalAuxiliar = new WebMarkupContainer("containerProfissionalAuxiliar"));
        containerProfissionalAuxiliar.setOutputMarkupPlaceholderTag(true);
        containerProfissionalAuxiliar.setVisible(false);

        containerProfissionalAuxiliar.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissionalAuxiliar())));
        autoCompleteConsultaProfissional.setPossuiCnsSim(true);
        autoCompleteConsultaProfissional.add(new TempBehaviorV3());

//        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {
                populateCboProfissionalAuxiliar(target);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                populateCboProfissionalAuxiliar(target);
            }
        });

        containerProfissionalAuxiliar.add(cbxCboProfissionalAuxiliar = new DropDown<>(path(proxy.getCboProfissionalAuxiliar())));
        cbxCboProfissionalAuxiliar.add(new TempBehaviorV3());
        populateCboProfissionalAuxiliar(null);
    }

    private void viewDlgInformarCiapIntervencao(AjaxRequestTarget target) {
        if (dlgInformarSoapCiapIntervencao == null) {
            getProntuarioController().addWindow(target, dlgInformarSoapCiapIntervencao = new DlgInformarSoapCiap(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<AtendimentoSoapCiap> atendimentoSoapCiapList) throws ValidacaoException, DAOException {
                    adicionarAtendimentoSoapCiapIntervencao(atendimentoSoapCiapList);
                }
            });
        }
        dlgInformarSoapCiapIntervencao.show(target, getForm().getModel().getObject().getAtendimentoSoapCiapListIntervencao());
    }

    private void adicionarAtendimentoSoapCiapMotivo(List<AtendimentoSoapCiap> atendimentoSoapCiapList) {
        getForm().getModel().getObject().setAtendimentoSoapCiapListMotivoConsulta(atendimentoSoapCiapList);
        new TempHelperV3().save(getForm());
    }

    private void adicionarAtendimentoSoapCiapIntervencao(List<AtendimentoSoapCiap> atendimentoSoapCiapList) {
        getForm().getModel().getObject().setAtendimentoSoapCiapListIntervencao(atendimentoSoapCiapList);
        new TempHelperV3().save(getForm());
    }

    private void atualizarNasf(AjaxRequestTarget target) {
        getForm().getModel().getObject().setNasfs(CheckBoxUtil.getSomatorio(lstCheckBoxNasf));
        new TempHelperV3().save(getForm());
    }

    private DropDown getCbxConduta(String id) {
        if (cbxConduta == null) {
            cbxConduta = new DropDown(id) {
                @Override
                protected void onConfigure() {
//                    updateCbxConduta(Coalesce.asLong(txtDiasRetorno.getComponentValue()) > 0);
                    updateCbxConduta(false);
                }
            };
            cbxConduta.addAjaxUpdateValue();
            cbxConduta.setOutputMarkupId(true);
        }
        return cbxConduta;
    }

    private DropDown getCbxCondutaCovid(String id) {
        if (cbxCondutaCovid == null) {
            cbxCondutaCovid = new DropDown(id);
            cbxCondutaCovid.removeAllChoices();

            cbxCondutaCovid.addChoice(null, "");
            CondutaCovid19[] condutasCovid19s = CondutaCovid19.values();
            for (CondutaCovid19 condutaCovid19s : condutasCovid19s) {
                cbxCondutaCovid.addChoice(condutaCovid19s.value(), condutaCovid19s.descricao());
            }
        }
        return cbxCondutaCovid;
    }

    private void updateCbxConduta(boolean apenasRetorno) {
        cbxConduta.removeAllChoices();

        LoadManager load = LoadManager.getInstance(Conduta.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Conduta.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(Conduta.PROP_TIPO_CONDUTA, Conduta.TipoConduta.CONSULTA_MEDICA.value()));

//        if (apenasRetorno) {
//            load.addParameter(new QueryCustom.QueryCustomParameter(Conduta.PROP_FLAG_RETORNO, RepositoryComponentDefault.SIM_LONG));
//        }

        try {
            Conduta parametroConduta = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("defineCondutaUtilizadoTelaAtendimento");
            if (parametroConduta != null) {
                cbxConduta.addChoice(null, "");
                cbxConduta.addChoice(parametroConduta, parametroConduta.getDescricao());
                getForm().getModelObject().setConduta(parametroConduta);
            } else {
                List<Conduta> lstConduta = load.start().getList();
                if (CollectionUtils.isNotNullEmpty(lstConduta)) {
                    cbxConduta.addChoice(null, "");

                    for (Conduta item : lstConduta) {
                        cbxConduta.addChoice(item, item.getDescricao());
                    }

                    Conduta conduta = null;
                    if (lstConduta.size() == 1) {
                        conduta = lstConduta.get(0);
                    } else if (cbxConduta.getModel() != null) {
                        conduta = cbxConduta.getComponentValue();
                    }

                    if (conduta != null && (RepositoryComponentDefault.SIM_LONG.equals(conduta.getFlagRetorno()) || !apenasRetorno)) {
                        cbxConduta.setComponentValue(conduta);
                    } else if (cbxConduta.getModel() != null) {
                        cbxConduta.setComponentValue(null);
                    }
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex);
        }
        new TempHelperV3().save(getForm());
    }

    private void viewDlgInformarConduta(AjaxRequestTarget target) {
        if (dlgInformarConduta == null) {
            getProntuarioController().addWindow(target, dlgInformarConduta = new DlgInformarConduta(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<CondutaAtendimento> condutaList) throws ValidacaoException, DAOException {
                    adicionarConduta(condutaList);
                }
            });
        }
        dlgInformarConduta.show(target, getForm().getModel().getObject().getCondutaList(), Coalesce.asLong(txtDiasRetorno.getComponentValue()) > 0);
    }

    private void viewDlgInformarCipe(AjaxRequestTarget target) {
        if (dlgInformarCipe == null) {
            getProntuarioController().addWindow(target, dlgInformarCipe = new DlgInformarCipe(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, List<AtendimentoCipe> cipeList) throws ValidacaoException, DAOException {
                    adicionarCipe(cipeList);
                }
            });
        }
        dlgInformarCipe.show(target, getForm().getModel().getObject().getCipeList());
    }

    private void adicionarConduta(List<CondutaAtendimento> condutaList) {
        getForm().getModel().getObject().setCondutaList(condutaList);
        new TempHelperV3().save(getForm());
    }

    private void adicionarCipe(List<AtendimentoCipe> cipeList) {
        getForm().getModel().getObject().setCipeList(cipeList);
        new TempHelperV3().save(getForm());
    }

    private DropDown getCbxClassificacaoAtendimento() {
        List<ClassificacaoAtendimento> caList;
        caList = LoadManager.getInstance(ClassificacaoAtendimento.class)
                .addProperty(ClassificacaoAtendimento.PROP_CODIGO)
                .addProperty(ClassificacaoAtendimento.PROP_CODIGO_ESUS)
                .addProperty(ClassificacaoAtendimento.PROP_DESCRICAO)
                .addProperty(ClassificacaoAtendimento.PROP_CLASSIFICACAO_ESUS)
                .addInterceptor(new LoadInterceptorClassificacaoCbo(getAtendimento().getProfissional(), getAtendimento().getEmpresa()))
                .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoAtendimento.PROP_ORDEM))
                .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoAtendimento.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoAtendimento.PROP_CLASSIFICACAO_ESUS, BuilderQueryCustom.QueryParameter.DIFERENTE,
                        ClassificacaoAtendimento.ClassificacaoEsus.VIGILANCIA_SAUDE_BUCAL.value(), HQLHelper.NOT_RESOLVE_TYPE, ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoAtendimento.PROP_SITUACAO, ClassificacaoAtendimento.Situacao.ATIVO.value()))
                .start().getList();
        ClassificacaoAtendimento classAtendPadrao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getClassificacaoAtendimento();
        try {
            ClassificacaoAtendimentoPorCondicaoSaude classifAtendCondicaoSaude = new ClassificacaoAtendimentoPorCondicaoSaude(caList, getAtendimento(), form.getModel().getObject().getClassificacaoAtendimento(), form.getModel().getObject().getAtendimentoClassificacaoAtendimentoList());

            if (classifAtendCondicaoSaude.isMostraClassificacaoPorCondicaosaude()) {
                if (form.getModel().getObject().getClassificacaoAtendimento() == null) {
                    form.getModel().getObject().setClassificacaoAtendimento(classifAtendCondicaoSaude.getClassificacaoAtendimento());
                }
                adicionarClassificacaoAtendimento(classifAtendCondicaoSaude.getAtendimentoClassificacaoAtendimentoList());
            } else if (getAtendimento().getClassificacaoAtendimento() == null && classAtendPadrao != null) {
                getForm().getModelObject().setClassificacaoAtendimento(classAtendPadrao);
                cbxClassificacaoAtendimento.addChoice(classAtendPadrao, classAtendPadrao.getDescricao());
            } else {
                if (getForm().getModelObject().getClassificacaoAtendimento() == null) {
                    ClassificacaoAtendimento classificacaoAtendimento = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("defineTipoAtendimentoUtilizadoTelaAtendimento");
                    if (classificacaoAtendimento != null) {
                        getForm().getModelObject().setClassificacaoAtendimento(classificacaoAtendimento);
                    }
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex);
        }
        cbxClassificacaoAtendimento.addChoice(null, "");
        boolean classificacaoPadrao = false;
        if (CollectionUtils.isNotNullEmpty(caList)) {
            for (ClassificacaoAtendimento ca : caList) {
                if (RepositoryComponentDefault.SEXO_FEMININO.equals(getAtendimento().getUsuarioCadsus().getSexo())
                        || (!"ABP001".equals(ca.getCodigoEsus())
                        && !"ABP022".equals(ca.getCodigoEsus())
                        && !"ABP002".equals(ca.getCodigoEsus()))
                ) {
                    cbxClassificacaoAtendimento.addChoice(ca, ca.getDescricao());
                    if (ca.equals(classAtendPadrao)) {
                        classificacaoPadrao = true;
                    }
                }
            }
        }
        return cbxClassificacaoAtendimento;
    }

    private void populateCboProfissionalAuxiliar(AjaxRequestTarget target) {
        cbxCboProfissionalAuxiliar.removeAllChoices();

        if (target != null) {
            cbxCboProfissionalAuxiliar.limpar(target);
        }

        Profissional profissionalAuxiliar = (Profissional) autoCompleteConsultaProfissional.getComponentValue();
        if (profissionalAuxiliar != null) {
            try {
                List<TabelaCbo> cbos = encontrarCboProfissional(getAtendimento().getEmpresa(), profissionalAuxiliar);
                if (CollectionUtils.isNotNullEmpty(cbos)) {
                    if (cbos.size() == 1) {
                        TabelaCbo cbo = cbos.get(0);
                        cbxCboProfissionalAuxiliar.addChoice(cbo, cbo.getDescricao());
                        cbxCboProfissionalAuxiliar.setComponentValue(cbo);
                    } else {
                        cbxCboProfissionalAuxiliar.addChoice(null, "");
                        for (TabelaCbo cbo : cbos) {
                            cbxCboProfissionalAuxiliar.addChoice(cbo, cbo.getDescricao());
                        }
                    }
                }
            } catch (DAOException | ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
        }
        new TempHelperV3().save(cbxCboProfissionalAuxiliar);
        if (target != null) {
            target.add(cbxCboProfissionalAuxiliar);
        }
    }

    private List<TabelaCbo> encontrarCboProfissional(Empresa empresa, Profissional profissional) throws ValidacaoException, DAOException {
        QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
        param.setEmpresa(empresa);
        param.setProfissional(profissional);

        List<TabelaCbo> cbos = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);
        if (CollectionUtils.isNotNullEmpty(cbos)) {
            return cbos;
        } else {
            return null;
        }
    }

    private void enabledFieldsDiarreia(AjaxRequestTarget target, Cid cid) {
        boolean contains = Arrays.asList("A09", "K591", "P783", "K580").contains(cid.getCodigo());
        if (contains) {
            target.appendJavaScript(JScript.showFieldset(containerRegistroDiarreia));
            cbxDiarreia.setComponentValue(RepositoryComponentDefault.NAO_LONG);
            enabledFieldsDiarreia(target, false);
            new TempHelperV3().save(cbxDiarreia);
        } else {
            enabledFieldsDiarreia(target, false);
        }
    }

    private void enabledFieldsDiarreia(AjaxRequestTarget target, boolean enabled) {
        cbxComSangue.setEnabled(enabled);
        chDataPrimeirosSintomas.setEnabled(enabled);
        txtResultado.setEnabled(enabled);
        cbxPlanoTratamento.setEnabled(enabled);

        if (target != null) {
            if (!enabled) {
                cbxDiarreia.limpar(target);
                cbxComSangue.limpar(target);
                chDataPrimeirosSintomas.limpar(target);
                txtResultado.limpar(target);
                cbxPlanoTratamento.limpar(target);
                new TempHelperV3().save(cbxDiarreia);
            } else {
                target.add(cbxDiarreia);
                target.add(cbxComSangue);
                target.add(chDataPrimeirosSintomas);
                target.add(txtResultado);
                target.add(cbxPlanoTratamento);
            }

            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void validarVisibilidadeCid() {
        List<TabelaCbo> tabelaCbos = new ArrayList<>();
        try {
            tabelaCbos = encontrarCboProfissional(getAtendimento().getEmpresa(), getAtendimento().getProfissional());
        } catch (ValidacaoException e) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, e);
        } catch (DAOException e) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, e);
        }

        boolean isMedico = false;
        if (CollectionUtils.isNotNullEmpty(tabelaCbos)) {
            for (TabelaCbo tabelaCbo : tabelaCbos) {
                isMedico = TabelaCbo.NivelEnsino.SUPERIOR.value().equals(tabelaCbo.getNivelEnsino()) && TabelaCbo.MEDICO.equals(tabelaCbo.getTipoProfissionalSaude());
            }
        }
        getForm().getModel().getObject().setVisibleCid(isMedico);
        new TempHelperV3().save(getForm());
    }

    private void validarVisibilidadeCipe() {
        List<TabelaCbo> tabelaCbos = new ArrayList<>();
        try {
            tabelaCbos = encontrarCboProfissional(getAtendimento().getEmpresa(), getAtendimento().getProfissional());
        } catch (ValidacaoException | DAOException e) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, e);
        }

        boolean isEnfermeiro = false;
        if (CollectionUtils.isNotNullEmpty(tabelaCbos)) {
            for (TabelaCbo tabelaCbo : tabelaCbos) {
                isEnfermeiro = tabelaCbo.getCbo().startsWith(TabelaCbo.INICIO_CBO_ENFERMEIRO);
            }
        }

        getForm().getModel().getObject().setVisibleCipe(isEnfermeiro);

        new TempHelperV3().save(getForm());
    }

    private AtendimentoPrimario carregarAtendimentoPrimario(Atendimento atendimento) {
        AtendimentoPrimario atendimentoPrimario = LoadManager.getInstance(AtendimentoPrimario.class)
                .addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties())
                .addProperties(new HQLProperties(IndiceImc.class, AtendimentoPrimario.PROP_INDICE_IMC).getProperties())
                .addProperties(new HQLProperties(RelacaoCinturaQuadril.class, AtendimentoPrimario.PROP_RELACAO_CINTURA_QUADRIL).getProperties())
                .addProperties(new HQLProperties(SisvanAlimentacao.class, AtendimentoPrimario.PROP_SISVAN_ALIMENTACAO).getProperties())
                .addProperties(new HQLProperties(SisvanDoenca.class, AtendimentoPrimario.PROP_SISVAN_DOENCA).getProperties())
                .addProperties(new HQLProperties(SisvanIntercorrencia.class, AtendimentoPrimario.PROP_SISVAN_INTERCORRENCIA).getProperties())
                .addProperties(new HQLProperties(Ciap.class, AtendimentoPrimario.PROP_CIAP).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, AtendimentoPrimario.PROP_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL)).getProperties())
                .addProperties(new HQLProperties(ClassificacaoRisco.class, VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_CLASSIFICACAO_RISCO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoPrimario.PROP_ATENDIMENTO, atendimento))
                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoPrimario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1).start().getVO();

        return atendimentoPrimario;
    }

    private void carregarHistoricoEvolucao() {
        noHistoricoClinicoDTO = new NoHistoricoClinicoDTO();
        noHistoricoClinicoDTO.setAtendimento(getAtendimento());
        noHistoricoClinicoDTO.setUsuarioCadsus(getAtendimento() != null ? getAtendimento().getUsuarioCadsus() : null);
        noHistoricoClinicoDTO.setEvolucaoProntuarioList(evolucaoProntuarioList);
        try {
            evolucaoProntuarioList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarEvolucaoProntuario(noHistoricoClinicoDTO);
        } catch (DAOException ex) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(EvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void carregarAtendimentoMDDA() {
        if (getForm().getModel().getObject().getAtendimentoMDDA() == null) {

            AtendimentoMDDA proxy = on(AtendimentoMDDA.class);
            List<AtendimentoMDDA> atendimentoMDDAList = LoadManager.getInstance(AtendimentoMDDA.class)
                    .addProperties(new HQLProperties(AtendimentoMDDA.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getAtendimentoPrincipal()), getAtendimento().getAtendimentoPrincipal()))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(atendimentoMDDAList)) {
                getForm().getModel().getObject().setAtendimentoMDDA(atendimentoMDDAList.get(0));
                getForm().getModel().getObject().setDiarreia(RepositoryComponentDefault.SIM_LONG);
                new TempHelperV3().save(getForm());
            }
        }
    }

    private void initDlgCadastroLembretesAtendimento(AjaxRequestTarget target) {
        if (dlgLembretesAtendimento == null) {
            WindowUtil.addModal(target, this, dlgLembretesAtendimento = new DlgLembretesAtendimento(WindowUtil.newModalId(this), getAtendimento()) {
            });
        }

        dlgLembretesAtendimento.show(target, getAtendimento());
    }

    private void addTabs() {
        List<ITab> tabs = new ArrayList();

        if (soapMedicoDTOParam == null) {
            soapMedicoDTOParam = new SoapMedicoDTOParam();
        }

        if (soapMedicoDTOParam.getAtendimento() == null) {
            soapMedicoDTOParam.setAtendimento(getAtendimento());
            soapMedicoDTOParam.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        }

        //  TAB LISTA DE PROBLEMAS
        tabs.add(new CadastroTab<SoapMedicoDTOParam>(soapMedicoDTOParam) {
            @Override
            public ITabPanel<SoapMedicoDTOParam> newTabPanel(String id, SoapMedicoDTOParam soapMedicoDTOParam) {
                return new GrupoProblemasCondicaoTab(id, soapMedicoDTOParam, getAtendimento().getUsuarioCadsus().getSexo());
            }
        });

        //  TAB MEDICAMENTOS EM USO
        tabs.add(new CadastroTab<SoapMedicoDTOParam>(soapMedicoDTOParam) {
            @Override
            public ITabPanel<SoapMedicoDTOParam> newTabPanel(String id, SoapMedicoDTOParam soapMedicoDTOParam) {
                return new MedicamentoEmUsoTab(id, soapMedicoDTOParam);
            }
        });

        //  TAB ATENDIMENTOS ANTERIORES
        tabs.add(new CadastroTab<SoapMedicoDTOParam>(soapMedicoDTOParam) {
            @Override
            public ITabPanel<SoapMedicoDTOParam> newTabPanel(String id, SoapMedicoDTOParam soapMedicoDTOParam) {
                return new AtendimentosAnterioresTab(id, soapMedicoDTOParam);
            }
        });

        SoapMedicoTabbedPanel tabbedPanel = new SoapMedicoTabbedPanel("wizard", soapMedicoDTOParam, false, tabs, false);

        containerGrupoProblemasCondicao = new WebMarkupContainer("containerGrupoProblemasCondicao");
        containerGrupoProblemasCondicao.setOutputMarkupId(true);
        containerGrupoProblemasCondicao.setOutputMarkupPlaceholderTag(true);
        add(containerGrupoProblemasCondicao);

        containerGrupoProblemasCondicao.add(tabbedPanel);
    }

    private void calculaDppDum(AjaxRequestTarget target) {
        Date dumGestante = getForm().getModel().getObject().getDumGestante();
        if (dumGestante != null) {
            getForm().getModel().getObject().setDppDum(Data.addDias(dumGestante, 280));

            if (target != null) {
                target.add(dchDppDum);
                target.appendJavaScript(JScript.initMasks());
            }
        }
    }

    private void viewDlgConcluirPreNatal(AjaxRequestTarget target, PreNatal preNatal) throws DAOException, ValidacaoException {
        if (dlgConcluirPreNatal == null) {
            getProntuarioController().addWindow(target, dlgConcluirPreNatal = new DlgConcluirPreNatal(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, PreNatal preNatal, List<PreNatalPesoRecemNascido> pesoRNList) throws ValidacaoException, DAOException {
                    concluirPreNatal(target, preNatal, pesoRNList);
                }
            });
        }

        dlgConcluirPreNatal.show(target, preNatal);
    }

    private void concluirPreNatal(AjaxRequestTarget target, PreNatal modelObject, List<PreNatalPesoRecemNascido> pesoRNList) throws DAOException, ValidacaoException {
        if (br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(pesoRNList)) {
            Lambda.forEach(pesoRNList).setAtendimento(getAtendimento());
        }

        BOFactoryWicket.getBO(AtendimentoFacade.class).concluirPreNatal(modelObject, pesoRNList);

        btnEncerrarPreNatal.setEnabled(!existePreNatalPendente());
        target.add(btnEncerrarPreNatal);
    }

    private boolean existePreNatalPendente() {
        return LoadManager.getInstance(PreNatal.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .start().getVO() != null;
    }

    private DropDown<SisvanAlimentacao> getDropDownSisvanAlimentacao(String id, Integer idadeParaValidarAlimentacao, Long idadeEmMeses, boolean enableBolsaFamilia) {
        if (dropDownSisvanAlimentacao == null) {
            dropDownSisvanAlimentacao = new DropDown<SisvanAlimentacao>(id);
            dropDownSisvanAlimentacao.add(new TempBehaviorV3());
            dropDownSisvanAlimentacao.addChoice(null, "");

            if (idadeEmMeses >= idadeParaValidarAlimentacao && !enableBolsaFamilia) {
                dropDownSisvanAlimentacao.setEnabled(false);
                return dropDownSisvanAlimentacao;
            }

            List<SisvanAlimentacao> list = LoadManager.getInstance(SisvanAlimentacao.class)
                    .addProperties(new HQLProperties(SisvanAlimentacao.class).getProperties())
                    .addSorter(new QueryCustom.QueryCustomSorter(SisvanAlimentacao.PROP_CODIGO_SISVAN, QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();

            for (SisvanAlimentacao sisvanAlimentacao : list) {
                dropDownSisvanAlimentacao.addChoice(sisvanAlimentacao, sisvanAlimentacao.getDescricaoAlimentacao());
            }
        }
        return dropDownSisvanAlimentacao;
    }

}