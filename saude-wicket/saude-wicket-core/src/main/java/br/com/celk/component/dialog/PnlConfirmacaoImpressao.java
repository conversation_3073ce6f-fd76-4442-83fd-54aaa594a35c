/*
 * Copyright 2013 joao.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.ReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoImpressao extends Panel {

    private WebMarkupContainer image;
    private MultiLineLabel label;
    private AbstractAjaxLink btnConfirmar;
    private ReportLink btnImprmir;
    
    private final String IMG = "img-warn";
    
    private String message;
    
    public PnlConfirmacaoImpressao(String id, String message) {
        super(id);
        this.message = message;
        init();
    }

    private void init() {
   
        add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));
        
        add(label = new MultiLineLabel("message", message));
        
        label.setOutputMarkupId(true);
        
        add(btnConfirmar = new AbstractAjaxLink("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onConfirmar(target);
            }

        });
        
        add(btnImprmir = new ReportLink("btnImprmir") {

            @Override
            public DataReport getDataReport() throws ReportException {
                return PnlConfirmacaoImpressao.this.getDataReport();
            }

            
        });
        
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public String getConfirmarLabel(){
        return BundleManager.getString("confirmar");
    }
    
    public String getFecharLabel(){
        return BundleManager.getString("fechar");
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }
    
    public abstract DataReport getDataReport() throws ReportException;
    
}
