package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.RegistroEspecializadoPanel;
import br.com.celk.view.atendimento.prontuario.panel.SoapPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoLacenPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameHepatiteDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoHepatite;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ExameHepatiteViewPanel extends ProntuarioCadastroPanel {

    private Form<RequisicaoHepatite> form;
    private WebMarkupContainer containerOne;
    private WebMarkupContainer containerTwo;

    private InputField<String> txtNumeroNotificacao;
    private InputField<String> txtAlt;
    private InputField<String> txtAst;

    private CheckBoxLongValue checkBoxDoadoresSaudeOrgao;
    private CheckBoxLongValue checkBoxUsuarioDrogasInjetInalaveis;
    private CheckBoxLongValue checkBoxHistoriaTransfusaoSanguinea;
    private CheckBoxLongValue checkBoxGestante;
    private CheckBoxLongValue checkBoxContatoDomicSexualPortHeptB;
    private CheckBoxLongValue checkBoxContatoSexualPortadorHepatiteC;
    private CheckBoxLongValue checkBoxPacienteAntiHBCReagProvBancoSangue;

    private CheckBoxLongValue checkBoxDiagnosticoAminotransferases;
    private CheckBoxLongValue checkBoxHemodialise;
    private CheckBoxLongValue checkBoxRNMaePortadoraHepatiteB;
    private CheckBoxLongValue checkBoxSusceptibilidadeHepatiteA;
    private CheckBoxLongValue checkBoxMonitHeptBAposSeisMeses;
    private CheckBoxLongValue checkBoxAcidentePerfuroCortante;
    private CheckBoxLongValue checkBoxAmbulatorioDstAidsHiv;
    private CheckBoxLongValue checkBoxReceptorSangueOrgao;
    private CheckBoxLongValue checkBoxOutros;

    private List<CheckBoxLongValue> lstCheckBoxCondicaoEpidemiologica = new ArrayList<CheckBoxLongValue>();
    private List<CheckBoxLongValue> lstCheckBoxCondicaoClinicaPaciente = new ArrayList<CheckBoxLongValue>();

    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;

    private TipoExame tipoExame;
    private Exame exame;
    private Long flagOrigem;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;

    private final String CSS_FILE = "ExameHepatiteViewPanel.css";
    
    public ExameHepatiteViewPanel(String id, Exame exame, TipoExame tipoExame, Long flagOrigem) {
        super(id, bundle("solicitacaoExameHepatiteVirais"));
        this.exame = exame;
        this.tipoExame = tipoExame;
        this.flagOrigem = flagOrigem;
    }

    public ExameHepatiteViewPanel(String id, Exame exame, TipoExame tipoExame, Long flagOrigem, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("solicitacaoExameHepatiteVirais"));
        this.exame = exame;
        this.tipoExame = tipoExame;
        this.flagOrigem = flagOrigem;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        RequisicaoHepatite proxy = on(RequisicaoHepatite.class);

        /* INICIO - inicializar container's */
        containerOne = new WebMarkupContainer("containerOne");
        containerTwo = new WebMarkupContainer("containerTwo");
        /* FIM- inicializar container's */

        /* INICIO - containerOne */
        containerOne.add(checkBoxDoadoresSaudeOrgao = (CheckBoxLongValue) new CheckBoxLongValue("doadoresSaudeOrgao", RequisicaoHepatite.CondicoesEpidemiologicas.DOADOR_SANGUE_ORGAO.value(), new Model<Long>()));
        containerOne.add(checkBoxUsuarioDrogasInjetInalaveis = (CheckBoxLongValue) new CheckBoxLongValue("usuarioDrogasInjetInalaveis", RequisicaoHepatite.CondicoesEpidemiologicas.USUARIO_DROGAS_INJET_INALAVEIS.value(), new Model<Long>()));
        containerOne.add(checkBoxHistoriaTransfusaoSanguinea = (CheckBoxLongValue) new CheckBoxLongValue("historiaTransfusaoSanguinea", RequisicaoHepatite.CondicoesEpidemiologicas.HISTORIA_TRANSFUSAO_SANGUINEA.value(), new Model<Long>()));
        containerOne.add(checkBoxGestante = (CheckBoxLongValue) new CheckBoxLongValue("gestante", RequisicaoHepatite.CondicoesEpidemiologicas.GESTANTE.value(), new Model<Long>()));
        containerOne.add(checkBoxContatoDomicSexualPortHeptB = (CheckBoxLongValue) new CheckBoxLongValue("contatoDomicSexualPortHeptB", RequisicaoHepatite.CondicoesEpidemiologicas.CONT_DOMIC_SEX_PORT_HEPT_B.value(), new Model<Long>()));
        containerOne.add(checkBoxContatoSexualPortadorHepatiteC = (CheckBoxLongValue) new CheckBoxLongValue("contatoSexualPortadorHepatiteC", RequisicaoHepatite.CondicoesEpidemiologicas.CONT_SEX_PORT_HEPT_C.value(), new Model<Long>()));
        containerOne.add(checkBoxPacienteAntiHBCReagProvBancoSangue = (CheckBoxLongValue) new CheckBoxLongValue("pacienteAntiHBCReagProvBancoSangue", RequisicaoHepatite.CondicoesEpidemiologicas.PACT_ANTI_HBC_REAG_PROVNT_BAN_SANGUE.value(), new Model<Long>()));

        lstCheckBoxCondicaoEpidemiologica.add(checkBoxDoadoresSaudeOrgao);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxUsuarioDrogasInjetInalaveis);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxHistoriaTransfusaoSanguinea);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxGestante);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxContatoDomicSexualPortHeptB);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxContatoSexualPortadorHepatiteC);
        lstCheckBoxCondicaoEpidemiologica.add(checkBoxPacienteAntiHBCReagProvBancoSangue);
        /* FIM - containerOne */

        /* INICIO - containerTwo */
        containerTwo.add(checkBoxDiagnosticoAminotransferases = (CheckBoxLongValue) new CheckBoxLongValue("diagnosticoAminotransferases", RequisicaoHepatite.CondicoesClinicasPaciente.DIAGNOSTICO_AMINOTRANSFERASES.value(), new Model<Long>()) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                disableComponents(target);
            }
        });
        containerTwo.add(txtAlt = new InputField<String>(path(proxy.getAlt())));
        containerTwo.add(txtAst = new InputField<String>(path(proxy.getAst())));

        containerTwo.add(checkBoxHemodialise = (CheckBoxLongValue) new CheckBoxLongValue("hemodialise", RequisicaoHepatite.CondicoesClinicasPaciente.HEMODIALISE.value(), new Model<Long>()));
        containerTwo.add(checkBoxRNMaePortadoraHepatiteB = (CheckBoxLongValue) new CheckBoxLongValue("rNMaePortadoraHepatiteB", RequisicaoHepatite.CondicoesClinicasPaciente.RN_MAE_PORT_HEPT_B.value(), new Model<Long>()));
        containerTwo.add(checkBoxSusceptibilidadeHepatiteA = (CheckBoxLongValue) new CheckBoxLongValue("susceptibilidadeHepatiteA", RequisicaoHepatite.CondicoesClinicasPaciente.SUSCEPTIBILIDADE_HEPT_A.value(), new Model<Long>()));
        containerTwo.add(checkBoxMonitHeptBAposSeisMeses = (CheckBoxLongValue) new CheckBoxLongValue("monitHeptBAposSeisMeses", RequisicaoHepatite.CondicoesClinicasPaciente.MONIT_HEPT_B_APOS_SEIS_MESES.value(), new Model<Long>()));
        containerTwo.add(checkBoxAcidentePerfuroCortante = (CheckBoxLongValue) new CheckBoxLongValue("acidentePerfuroCortante", RequisicaoHepatite.CondicoesClinicasPaciente.ACIDENTE_PERFURO_CORTANTE.value(), new Model<Long>()));
        containerTwo.add(checkBoxAmbulatorioDstAidsHiv = (CheckBoxLongValue) new CheckBoxLongValue("ambulatorioDstAidsHiv", RequisicaoHepatite.CondicoesClinicasPaciente.AMBULATORIO_DST_AIDS_HIV.value(), new Model<Long>()));
        containerTwo.add(checkBoxReceptorSangueOrgao = (CheckBoxLongValue) new CheckBoxLongValue("receptorSangueOrgao", RequisicaoHepatite.CondicoesClinicasPaciente.RECEPTOR_SANGUE_ORGAO.value(), new Model<Long>()));
        containerTwo.add(checkBoxOutros = (CheckBoxLongValue) new CheckBoxLongValue("outros", RequisicaoHepatite.CondicoesClinicasPaciente.OUTROS.value(), new Model<Long>()));

        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxDiagnosticoAminotransferases);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxHemodialise);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxRNMaePortadoraHepatiteB);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxSusceptibilidadeHepatiteA);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxMonitHeptBAposSeisMeses);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxAcidentePerfuroCortante);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxAmbulatorioDstAidsHiv);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxReceptorSangueOrgao);
        lstCheckBoxCondicaoClinicaPaciente.add(checkBoxOutros);
        /* FIM - containerTwo */

        getForm().add(txtNumeroNotificacao = new InputField<String>(path(proxy.getNumeroNotificacao())));
        getForm().add(containerOne);
        getForm().add(containerTwo);

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
            }

            @Override
            public boolean isVisible() {
                return SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.equals(containerTelaSoap);
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarExameHepatite(target);
            }
        });

        add(getForm());
        if (this.tipoExame == null) {
            this.tipoExame = new TipoExame();
        } else {
            carregarExameHepatite();
        }
        disableComponents(null);
    }

    private Form<RequisicaoHepatite> getForm() {
        if (this.form == null) {
            this.form = new Form<RequisicaoHepatite>("form", new CompoundPropertyModel<RequisicaoHepatite>(new RequisicaoHepatite()));
        }

        return this.form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }

    private void salvarExameHepatite(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tipoExame.getExameProcedimentoPadrao() == null) {
            throw new ValidacaoException(bundle("tipoExameSemProcedimentoPadrao", this));
        }

        RequisicaoHepatite requisicaoHepatite = getForm().getModel().getObject();

        requisicaoHepatite.setSomaCondicaoEpidemiologica(CheckBoxUtil.getSomatorio(lstCheckBoxCondicaoEpidemiologica));
        requisicaoHepatite.setSomaCondicaoClinica(CheckBoxUtil.getSomatorio(lstCheckBoxCondicaoClinicaPaciente));

        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();
        if (this.exame != null) {
            dto.setCodigoExameCadastrado(this.exame.getCodigo());
            dto.setDataSolicitacao(this.exame.getDataSolicitacao());
            dto.setAtendimento(this.exame.getAtendimento());
            dto.setCodigoUnidade(this.exame.getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(this.exame.getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(this.exame.getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(this.exame.getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(this.exame.getAtendimento().getUsuarioCadsus().getNomeSocial());
        } else {
            dto.setDataSolicitacao(DataUtil.getDataAtual());
            dto.setAtendimento(getAtendimento());
            dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
            dto.setOrigem(flagOrigem);
        }

        ExameProcedimentoDTO exameProcedimento = new ExameProcedimentoDTO();

        exameProcedimento.setExameProcedimento(tipoExame.getExameProcedimentoPadrao());
        exameProcedimento.setQuantidade(1L);
        exameProcedimento.setComplemento(null);
        exameProcedimento.setValor(0D);

        List<ExameProcedimentoDTO> item = new ArrayList<ExameProcedimentoDTO>();
        item.add(exameProcedimento);

        dto.setExameProcedimentoDTOs(item);
        Long codigoExameCadastrado = BOFactoryWicket.getBO(ExameFacade.class).cadastrarSolicitacaoExameHepatite(dto, requisicaoHepatite);

        initDialogImpressao(target);
        dlgConfirmacaoImpressao.show(target, codigoExameCadastrado);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), bundle("desejaImprimir", this)) {
                @Override
                public DataReport getDataReport(Long codigoExameCadastrado) throws ReportException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addParameter(new QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    ImpressaoExameHepatiteDTOParam param = new ImpressaoExameHepatiteDTOParam();
                    param.setCodigoExame(codigoExameCadastrado);
                    param.setAtendimento(exameCadastrado.getAtendimento());

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameHepatite(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long codigoExameCadastrado) throws ValidacaoException, DAOException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO))
                            .addParameter(new QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    Boolean isLacen = RepositoryComponentDefault.Tipo.LACEN.value().equals(exameCadastrado.getTipoExame().getTipo());
                    if(isLacen){
                        getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                    } else {
                        if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                        } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                        } else {
                            getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                        }
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void carregarExameHepatite() {
        ExameRequisicao exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperty(ExameRequisicao.PROP_CODIGO)
                .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, tipoExame.getExameProcedimentoPadrao()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), ExameRequisicao.Status.ABERTO.value()))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Exame.STATUS_AUTORIZADO, Exame.STATUS_DESVINCULADO, Exame.STATUS_RECEBIDO, Exame.STATUS_SOLICITADO)))
                .start().getVO();

        if (exameRequisicao != null) {
            RequisicaoHepatite requisicaoHepatite = LoadManager.getInstance(RequisicaoHepatite.class)
                    .addParameter(new QueryCustomParameter(RequisicaoHepatite.PROP_EXAME_REQUISICAO, exameRequisicao))
                    .start().getVO();

            if (requisicaoHepatite != null) {
                getForm().setModelObject(requisicaoHepatite);

                CheckBoxUtil.selecionarSomatorio(lstCheckBoxCondicaoEpidemiologica, requisicaoHepatite.getSomaCondicaoEpidemiologica());
                CheckBoxUtil.selecionarSomatorio(lstCheckBoxCondicaoClinicaPaciente, requisicaoHepatite.getSomaCondicaoClinica());
            }
        }
    }

    private void disableComponents(AjaxRequestTarget target) {
        boolean enable = checkBoxDiagnosticoAminotransferases != null && Coalesce.asLong(checkBoxDiagnosticoAminotransferases.getComponentValue()) != 0;
        txtAlt.setEnabled(enable);
        txtAst.setEnabled(enable);

        if (target != null) {
            txtAlt.limpar(target);
            txtAst.limpar(target);
            target.add(txtAlt);
            target.add(txtAst);
        }
    }
}
