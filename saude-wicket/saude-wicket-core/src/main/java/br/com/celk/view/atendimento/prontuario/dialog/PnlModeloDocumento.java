package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.dropdown.DropDown;
import static br.com.celk.system.methods.WicketMethods.bundle;

import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlModeloDocumento extends Panel{

    private DropDown<TipoDocumentoAtendimento> dropDownTipoDocumentoAtendimento;

    private Atendimento atendimento;
    
    public PnlModeloDocumento(String id, Atendimento atendimento) {
        super(id);
        this.atendimento = atendimento;
        init();
    }
    
    private void init(){
        Form form = new Form("form");
        
        form.add(getDropDownTipoExame());
        
        add(form);
    }

    public DropDown getDropDownTipoExame() {
        if (this.dropDownTipoDocumentoAtendimento == null) {
            dropDownTipoDocumentoAtendimento = DropDownUtil.getDropDownTipoDocumentoAtendimento("tipoDocumento", bundle("selecioneTipoDocumento"), atendimento, TipoDocumentoAtendimento.TipoDocumento.TIPO_DOCUMENTOS);
                
            dropDownTipoDocumentoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    TipoDocumentoAtendimento tipoDocumentoAtendimento = dropDownTipoDocumentoAtendimento.getComponentValue();
                    PnlModeloDocumento.this.onUpdate(target, tipoDocumentoAtendimento);
                }
            });
        }
        return dropDownTipoDocumentoAtendimento;
    }

    public abstract void onUpdate(AjaxRequestTarget target, TipoDocumentoAtendimento tipoDocumentoAtendimento);
}
