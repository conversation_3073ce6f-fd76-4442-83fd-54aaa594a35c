package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoPreNatalDTO;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class PreNatalExamesTab extends TabPanel<NoPreNatalDTO> {

    private WebMarkupContainer containerExame;
    private IModel<NoPreNatalDTO> model;

    public PreNatalExamesTab(String id, NoPreNatalDTO object) {
        super(id, object);
        init(object);
    }

    public void init(NoPreNatalDTO noPreNatalDTO) {
        setDefaultModel(new CompoundPropertyModel<NoPreNatalDTO>(noPreNatalDTO));
        NoPreNatalDTO proxy = on(NoPreNatalDTO.class);

        containerExame = new WebMarkupContainer("containerExame");

        containerExame.add(DropDownUtil.getIEnumDropDown(path(proxy.getPreNatal().getVdrl()), PreNatal.Vdrl.values(), true));
        containerExame.add(new DateChooserAjax(path(proxy.getPreNatal().getDataVdrl())));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getExameClinicoNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getExameMamasNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getExameOdontologicoNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getPelvisNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getPapanicolauNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getColposcopiaNormal()), true, false));
        containerExame.add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getPreNatal().getExameClinicoCervix()), true, false));

        add(containerExame);

    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    @Override
    public String getTitle() {
        return bundle("exames");
    }

}
