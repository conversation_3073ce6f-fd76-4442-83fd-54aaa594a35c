package br.com.celk.view.materiais.produto.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.materiais.produto.pnl.restricaocontainer.RestricaoContainerProduto;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import java.io.Serializable;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaProduto extends PnlConsulta<Produto> {

    private InputField txtUnidade;
    private boolean incluirInativos = false;

    private String flagMedicamento;
    private boolean validarEstoqueEmpresaAtivo = true;
    private Long tipoProduto;
    private Empresa empresa;

    public PnlConsultaProduto(String id, IModel<Produto> model) {
        super(id, model);
        init();
    }

    public PnlConsultaProduto(String id, IModel<Produto> model, boolean required) {
        super(id, model, required);
        init();
    }

    public PnlConsultaProduto(String id) {
        super(id);
        init();
    }

    public PnlConsultaProduto(String id,  boolean required) {
        super(id, required);
        init();
    }

    private void init(){
        add(txtUnidade = new DisabledInputField("unidade", new PropertyModel(this, "vo.unidade.unidade")));

        add(new ConsultaListener<Produto>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                target.add(txtUnidade);
            }
        });
    }

    @Override
    public int getMinDialogHeight() {
        return 500;
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(Produto.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(Produto.PROP_REFERENCIA)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(Produto.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("grupo"), VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_RO_GRUPO_PRODUTO, GrupoProduto.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("sub_grupo"), VOUtils.montarPath(Produto.PROP_SUB_GRUPO, SubGrupo.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(Produto.PROP_CODIGO, Produto.PROP_FLAG_ATIVO),VOUtils.montarPath(Produto.PROP_DESCRICAO_SITUACAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("unidade"), VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerProduto(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<Produto, QueryConsultaProdutoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaProdutoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(ProdutoFacade.class).getProdutoQueryPager(dataPaging);
                    }

                    @Override
                    public QueryConsultaProdutoDTOParam getLoadByIdParam(Serializable objectIdReference) {
                        QueryConsultaProdutoDTOParam param = new QueryConsultaProdutoDTOParam();
                        param.setReferencia((String)objectIdReference);
                        return param;
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(Produto.PROP_DESCRICAO), true);
                    }

                    @Override
                    public void customizeParam(QueryConsultaProdutoDTOParam param) {
                        param.setFlagMedicamento(flagMedicamento);
                        param.setValidarEstoqueEmpresaAtivo(!isMultiplaSelecao()&&validarEstoqueEmpresaAtivo);
                        param.setEmpresa(empresa);
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setTipoProduto(tipoProduto);
                        param.setIncluirInativos(incluirInativos);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return Produto.class;
            }
        };
    }

    public PnlConsultaProduto setEmpresa(Empresa empresa) {
        this.empresa = empresa;
        return this;
    }

    public PnlConsultaProduto setValidarEstoqueEmpresaAtivo(boolean validarEstoqueEmpresaAtivo) {
        this.validarEstoqueEmpresaAtivo = validarEstoqueEmpresaAtivo;
        return this;
    }

    public PnlConsultaProduto setFlagMedicamento(String flagMedicamento) {
        this.flagMedicamento = flagMedicamento;
        return this;
    }

    public PnlConsultaProduto setTipoProduto(Long tipoProduto){
        this.tipoProduto = tipoProduto;
        return this;
    }

    public PnlConsultaProduto setIncluirInativos(boolean incluirInativos) {
        this.incluirInativos = incluirInativos;
        return this;
    }

    public boolean getIncluirInativos() {
        return this.incluirInativos;
    }


    @Override
    public String getTitle() {
        return BundleManager.getString("produtos");
    }

}
