package br.com.celk.view.vacina.caderneta.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.VacinaCalendarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.VacinaAprazamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * <AUTHOR>
 */
public abstract class DlgVacinaAprazamento extends Window {

    private PnlVacinaAprazamento pnlVacinaAprazamento;

    public DlgVacinaAprazamento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("aprazamento");
            }
        });

        setInitialWidth(600);
        setInitialHeight(140);
        setResizable(true);
        pnlVacinaAprazamento = new PnlVacinaAprazamento(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, VacinaAprazamento vacinaAprazamento) throws ValidacaoException, DAOException {
                close(target);
                DlgVacinaAprazamento.this.onConfirmar(target, vacinaAprazamento);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }
        };
        setContent(pnlVacinaAprazamento);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, VacinaAprazamento vacinaAprazamento) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, VacinaCalendarioDTO dto, UsuarioCadsus usuarioCadsus) {
        show(target);
        pnlVacinaAprazamento.setDTO(target, dto, usuarioCadsus);
    }
}