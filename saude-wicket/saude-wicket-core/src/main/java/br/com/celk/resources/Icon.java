package br.com.celk.resources;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 *
 * novos icones baixados do site http://iconizer.net
 */
public enum Icon implements Serializable, Icons{
    ARROW_BOTTOM("arrow-bottom"),
    ARROW_LEFT("arrow-left"),
    CALENDAR("calendar"),
    CANCEL("cancel"),
    DOC_DELETE("doc-delete"),
    DOC_EDIT("doc-edit"),
    <PERSON><PERSON><PERSON>("wrench"),
    ZOOM("zoom"),
    PRINT("print"),
    PRINTER("printer"),
    PRINTER2("printer2"),
    HAND_PRO("hand-pro"),
    CHECKMARK("checkmark"),
    CLIPBOARD_COPY("clipboard-copy"),
    CLIPBOARD_PAST("clipboard-past"),
    DOWNLOAD("download"),
    ICON_BLOCKED("icon padlock-closed"),
    ICON_UNBLOCKED("icon padlock-open"),
    NOTEPAD("notepad"),
    UNDO("undo"),
    REDO("redo"),
    HAND_CONTRA("hand-contra"),
    USERS("users"),
    REFRESH("refresh"),
    PENCIL("pencil"),
    SHOP_CART("icon shop-cart"),
    ARROW_TWO_HEAD("arrow-two-head"),
    COGS("cogs"),
    CLOCK("clock"),
    ROUND_CHECKMARK("round-checkmark"),
    ROUND_CHECKMARK_NEW("round-checkmark-new"),
    ROUND_DELETE("round-delete"),
    ROUND_DELETE_NEW("round-delete-new"),
    ROUND_PLUS("round-plus"),
    SPECHBUBBLE_SQ_LINE("spechbubble-sq-line"),
    TAG("tag"),
    FOLDER("folder"),
    PHONE("phone"),
    ROUND_MINUS("round-minus"),
    HOME("home"),
    DB("db"),
    MAIL("mail"),
    MONITOR("monitor"),
    CELL_PHONE("cell-phone"),
    DOC_LINES("doc-lines"),
    FILTER("filter"),
    TRANSFER("transfer"),
    BED("bed"),
    DOC_PLUS("doc-plus"),
    FOLDER_DELETE("folder-delete"),
    PILL("pill"),
    PUSH_PIN("push-pin"),
    CONTACT_CARD("contact-card"),
    CALC("calc"),
    ROUND_ARROW("round-arrow"),
    MONEY("money"),
    COLOR_PICKER("color-picker"),
    RELOAD("reload"),
    CALENDAR_EDIT("calendar-edit"),
    BOOK("book"),
    ARROW_SWITCH("arrow-switch"),
    LABORATORY("laboratory"),
    MEMBERS("members"),
    ARROW_DOWN("arrow-down"),
    EMERGENCY("emergency"),
    WARN("attention"),
    BIOMETRY("biometry"),
    DOC_ARROW_RIGHT("doc-arrow-right"),
    CODE_BAR("code_bar"),
    CLIP("clip"),
    CHECKNEW("check-new"),
    BALANCE("balance"),
    TRACK("track"),
    EYE("eye"),
    BOOK_SIDE("book-side"),
    ERROR("error"),
    QR_CODE("qr-code"),
    PICTURE("picture"),
    NOTEPAD_CHECKED("notepad-checked"),
    HELP("help"),
    TR_UM("tr-um"),
    TR_DOIS("tr-dois"),
    TR_TRES("tr-tres"),
    TR_QUATRO("tr-quatro"),
    JSON("json"),
    ASSINATURA_DIGITAL("sign")
    ;

    private String clazz;

    private Icon(String clazz) {
        this.clazz = clazz;
    }

    @Override
    public String clazz(){
        return this.clazz;
    }
}
