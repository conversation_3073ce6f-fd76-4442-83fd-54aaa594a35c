package br.com.celk.component.temp.interfaces.impl;

import br.com.celk.component.autocompleteconsultabootstrap.AutoCompleteConsultaBootstrap;
import br.com.celk.component.temp.interfaces.ITempBehaviorStrategy;
import org.apache.wicket.Component;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.odlabs.wiquery.core.javascript.JsQuery;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaBootstrapTempBehaviorStrategy implements ITempBehaviorStrategy{

    @Override
    public void onBind(Component component) {
        if (!(component instanceof AutoCompleteConsultaBootstrap)) {
            throw new IllegalStateException();
        }
        ((AutoCompleteConsultaBootstrap)component).setOnAddAction("$('#"+component.getMarkupId()+"').tempComponent('save');");
        ((AutoCompleteConsultaBootstrap)component).setOnDeleteAction("$('#"+component.getMarkupId()+"').tempComponent('save');");
    }

    @Override
    public void onRenderHead(Component component, IHeaderResponse response) {
        String getValue = "function(input){"
                + "     return $('#"+((AutoCompleteConsultaBootstrap)component).getTxtDescricao().getTextField().getMarkupId()+"').val();"
                + "}";

        String getFormId = "function(input){"
                + "     return "+component.findParent(Form.class).getMarkupId() +";"
                + "}";

        response.render(OnDomReadyHeaderItem.forScript(new JsQuery(component).$().chain("tempComponent", "{getValue: "+getValue+", getFormId: "+getFormId+"}").getStatement().toString()));
    }

}
