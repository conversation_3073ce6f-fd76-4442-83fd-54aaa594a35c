package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.CondutaAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.FornecimentoOdontoAtend;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class DlgInformarTipoFornecimento extends Window {

    private PnlInformarTipoFornecimento pnlInformarTipoFornecimento;

    public DlgInformarTipoFornecimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("tipoFornecimento");
            }
        });

        setInitialWidth(600);
        setInitialHeight(300);
        setResizable(true);

        setContent(pnlInformarTipoFornecimento = new PnlInformarTipoFornecimento(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<FornecimentoOdontoAtend> fornecimentoOdontoAtendList) throws ValidacaoException, DAOException {
                close(target);
                DlgInformarTipoFornecimento.this.onConfirmar(target, fornecimentoOdontoAtendList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<FornecimentoOdontoAtend> fornecimentoOdontoAtendList) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target, List<FornecimentoOdontoAtend> fornecimentoOdontoAtendList) {
        show(target);
        pnlInformarTipoFornecimento.setObject(target, fornecimentoOdontoAtendList);
    }
}