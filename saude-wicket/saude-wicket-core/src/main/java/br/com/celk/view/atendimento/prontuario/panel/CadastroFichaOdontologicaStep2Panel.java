package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.odontograma.FichaOdontogramaPanel;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.situacaodente.autocomplete.AutoCompleteConsultaSituacaoDente;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.TecidosMoles;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroFichaOdontologicaStep2Panel extends ProntuarioCadastroPanel {

    private Form form;
    private AtendimentoOdontoFicha atendimentoOdontoFicha;
    private CompoundPropertyModel<AtendimentoOdontoPlano> model;
    private DlgImpressaoObject<AtendimentoOdontoFicha> dlgConfirmacaoImpressao;

    private FichaOdontogramaPanel fichaOdontogramaPanel;

    private WebMarkupContainer containerTratamento;
    private WebMarkupContainer containerEvolucao;
    private WebMarkupContainer containerDente;
    private WebMarkupContainer containerSextante;
    private WebMarkupContainer containerArcada;
    private WebMarkupContainer containerTecidosMoles;
    private WebMarkupContainer containerOutro;

    private CheckBox cbxDistal;
    private CheckBox cbxMesial;
    private CheckBox cbxOclusal;
    private CheckBox cbxPalatal;
    private CheckBox cbxVestibular;

    private DropDown<Dente> dropDownDente;
    private AutoCompleteConsultaSituacaoDente autoCompleteConsultaSituacaoDente;

    private Table<AtendimentoOdontoPlano> tblAtendimentoOdontoPlano;

    private DropDown<TecidosMoles> dropDownTecidosMoles;

    public CadastroFichaOdontologicaStep2Panel(String id, AtendimentoOdontoFicha atendimentoOdontoFicha) {
        super(id, BundleManager.getString("cadastroFichaOdontologica"));
        this.atendimentoOdontoFicha = atendimentoOdontoFicha;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        getForm().add(fichaOdontogramaPanel = new FichaOdontogramaPanel("odontograma", getProntuarioController(), atendimentoOdontoFicha) {
            @Override
            public void updateTable(AjaxRequestTarget target) {
                tblAtendimentoOdontoPlano.update(target);
            }
        });


        /* FIM - containerDadosComplementares */
        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, false);
            }
        });

        getForm().add(new AbstractAjaxButton("btnGerarImpressao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, true);
            }
        });

        addContainerTratamento();

        add(getForm());
    }

    private Form<AtendimentoOdontoFicha> getForm() {
        if (this.form == null) {
            this.form = new Form("form");
        }
        return this.form;
    }

    private void addContainerTratamento() {
        AtendimentoOdontoPlano proxy = Lambda.on(AtendimentoOdontoPlano.class);
        form.add(containerTratamento = new WebMarkupContainer("containerTratamento", model = new CompoundPropertyModel(new AtendimentoOdontoPlano())));
        containerTratamento.setOutputMarkupId(true);

        containerTratamento.add(containerEvolucao = new WebMarkupContainer("containerEvolucao"));
        containerEvolucao.setOutputMarkupId(true);

        RadioButtonGroup radioButtonGroup = new RadioButtonGroup(path(proxy.getTipoEvolucao()));
        radioButtonGroup.add(new AjaxRadio("dente", new Model(AtendimentoOdontoPlano.Tipo.DENTE.value())));
        radioButtonGroup.add(new AjaxRadio("sextante", new Model(AtendimentoOdontoPlano.Tipo.SEXTANTE.value())));
        radioButtonGroup.add(new AjaxRadio("arcada", new Model(AtendimentoOdontoPlano.Tipo.ARCADA.value())));
        radioButtonGroup.add(new AjaxRadio("tecidosMoles", new Model(AtendimentoOdontoPlano.Tipo.TECIDOS_MOLES.value())));
        radioButtonGroup.add(new AjaxRadio("outro", new Model(AtendimentoOdontoPlano.Tipo.OUTRO.value())));

        radioButtonGroup.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                onChangeTipoEvolucao(target);
            }
        });

        containerEvolucao.add(radioButtonGroup);

        containerEvolucao.add(containerDente = new WebMarkupContainer("containerDente"));
        containerDente.setOutputMarkupId(true);
        containerDente.add(getDropDownDente(path(proxy.getDente())));

        containerDente.add(cbxDistal = new CheckBox("distal", new Model<Boolean>()));
        containerDente.add(cbxOclusal = new CheckBox("oclusal", new Model<Boolean>()));
        containerDente.add(cbxMesial = new CheckBox("mesial", new Model<Boolean>()));
        containerDente.add(cbxPalatal = new CheckBox("palatal", new Model<Boolean>()));
        containerDente.add(cbxVestibular = new CheckBox("vestibular", new Model<Boolean>()));

        containerEvolucao.add(containerSextante = new WebMarkupContainer("containerSextante"));
        containerSextante.setOutputMarkupId(true);
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS1())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS2())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS3())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS4())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS5())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS6())));

        containerEvolucao.add(containerArcada = new WebMarkupContainer("containerArcada"));
        containerArcada.setOutputMarkupId(true);
        containerArcada.add(new CheckBoxLongValue(path(proxy.getArcadaInferior())));
        containerArcada.add(new CheckBoxLongValue(path(proxy.getArcadaSuperior())));

        containerEvolucao.add(containerTecidosMoles = new WebMarkupContainer("containerTecidosMoles"));
        containerTecidosMoles.setOutputMarkupId(true);
        containerTecidosMoles.add(getDropDownTecidosMoles(path(proxy.getTecidosMoles())));

        containerEvolucao.add(containerOutro = new WebMarkupContainer("containerOutro"));
        containerOutro.setOutputMarkupId(true);
        containerOutro.add(new InputField(path(proxy.getOutro())));

        containerTratamento.add(tblAtendimentoOdontoPlano = new Table("tblAtendimentoOdontoPlano", getColumns(), getCollectionProvider()));
        tblAtendimentoOdontoPlano.populate();
//        tblAtendimentoOdontoPlano.setEnabled(enabledForm || existeTratamentoUrgencia);

        model.getObject().setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());
        onChangeTipoEvolucao(null);

        containerTratamento.add(autoCompleteConsultaSituacaoDente = new AutoCompleteConsultaSituacaoDente(path(proxy.getSituacaoDente())));
        containerTratamento.add(new InputArea(path(proxy.getObservacao())));
        containerTratamento.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarSalvar(target);
            }
        });
    }

    private DropDown<Dente> getDropDownDente(String id) {
        dropDownDente = new DropDown(id);
        dropDownDente.addChoice(null, "");

        List<Dente> denteList = LoadManager.getInstance(Dente.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Dente.PROP_NOME))
                .start().getList();

        for (Dente dente : denteList) {
            dropDownDente.addChoice(dente, dente.getNome());
        }

        return dropDownDente;
    }

    private void onChangeTipoEvolucao(AjaxRequestTarget target) {
        containerDente.setVisible(false);
        containerSextante.setVisible(false);
        containerArcada.setVisible(false);
        containerTecidosMoles.setVisible(false);
        containerOutro.setVisible(false);

        Long tipoEvolucao = model.getObject().getTipoEvolucao();
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao)) {
            containerDente.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.SEXTANTE.value().equals(tipoEvolucao)) {
            containerSextante.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)) {
            containerArcada.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.TECIDOS_MOLES.value().equals(tipoEvolucao)) {
            containerTecidosMoles.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.OUTRO.value().equals(tipoEvolucao)) {
            containerOutro.setVisible(true);
        }

        if (target != null) {
            target.add(containerEvolucao);
        }
    }

    private DropDown<TecidosMoles> getDropDownTecidosMoles(String id) {
        dropDownTecidosMoles = new DropDown(id);
        dropDownTecidosMoles.addChoice(null, "");

        List<TecidosMoles> tecidosMolesList = LoadManager.getInstance(TecidosMoles.class)
                .addSorter(new QueryCustom.QueryCustomSorter(TecidosMoles.PROP_DESCRICAO))
                .start().getList();

        for (TecidosMoles tecidosMoles : tecidosMolesList) {
            dropDownTecidosMoles.addChoice(tecidosMoles, StringUtil.getStringMaxPrecision(tecidosMoles.getDescricao(),120));
        }

        return dropDownTecidosMoles;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        AtendimentoOdontoPlano proxy = on(AtendimentoOdontoPlano.class);
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("local"), proxy.getDescricaoLocalFormatado()));
        columns.add(createColumn(bundle("situacaoTratamento"), proxy.getSituacaoDente().getDescricaoFormatado()));
        columns.add(createColumn(bundle("status"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AtendimentoOdontoPlano>() {
            @Override
            public void customizeColumn(AtendimentoOdontoPlano rowObject) {
                // TODO: não permitir excluir um tratamento que já está em andamento
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoOdontoPlano>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException {
                         fichaOdontogramaPanel.excluirPlanoTratamento(target, atendimentoOdontoPlano);
                    }
                }).setQuestionDialogBundleKey("desejaExcluirItemSelecionado");
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                List<AtendimentoOdontoPlano> atendimentoOdontoPlanoList = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, atendimentoOdontoFicha))
                        .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoOdontoPlano.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                        .start().getList();

                if (CollectionUtils.isEmpty(atendimentoOdontoPlanoList)) {
                    atendimentoOdontoPlanoList = new ArrayList();
                }

                return atendimentoOdontoPlanoList;
            }
        };
    }

    private void adicionarSalvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtendimentoOdontoPlano atendimentoOdontoPlano = model.getObject();
        validarCampos(atendimentoOdontoPlano);

        atendimentoOdontoPlano.setAtendimentoOdontoFicha(atendimentoOdontoFicha);
        atendimentoOdontoPlano.setAtendimento(getAtendimento());
        atendimentoOdontoPlano.setDataCadastro(DataUtil.getDataAtual());

        if (SituacaoDente.TipoSituacao.PROCEDIMENTO.value().equals(atendimentoOdontoPlano.getSituacaoDente().getTipoSituacao())) {
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.PENDENTE.value());
        } else {
            atendimentoOdontoPlano.setAtendimentoExecucao(getAtendimento());
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.HISTORICO.value());
        }

        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(atendimentoOdontoPlano.getTipoEvolucao())) {
            // TODO: verificar se já existe tratamento pendente com a mesma situação para o mesmo dente
//            boolean existeTratamentoPendente = LoadManager.getInstance(AtendimentoOdontoPlano.class)
//                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_DENTE, atendimentoOdontoPlano.getDente()))
//                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_FACE, atendimentoOdontoPlano.getFace()))
//                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, atendimentoOdontoPlano.getSituacaoDente()))
//                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, atendimentoOdontoFicha))
//                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AtendimentoOdontoPlano.Status.PENDENTE.value(), AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value())))
//                    .exists();
//
//            if (existeTratamentoPendente) {
//                throw new ValidacaoException(BundleManager.getString("msgExisteTratamentoAbertoDenteFaceSituacao"));
//            }

            List facesList = new ArrayList();

            if (Boolean.TRUE.equals(cbxPalatal.getModelObject())) {
                atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("P");
            }

            if (Boolean.TRUE.equals(cbxVestibular.getModelObject())) {
                atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.NAO_LONG);
                facesList.add("V");
            }

            if (Boolean.TRUE.equals(cbxDistal.getModelObject())) {
                atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("D");
            }

            if (Boolean.TRUE.equals(cbxMesial.getModelObject())) {
                atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.NAO_LONG);
                facesList.add("M");
            }

            if (Boolean.TRUE.equals(cbxOclusal.getModelObject())) {
                atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("O");
            }

            if (!facesList.isEmpty()) {
                String faces = Lambda.join(facesList, "-");
                atendimentoOdontoPlano.setFaces(faces);
            }

            fichaOdontogramaPanel.adicionarTratamentoDente(target, atendimentoOdontoPlano);
        } else {
            BOFactoryWicket.save(atendimentoOdontoPlano);
        }

        cbxPalatal.setModelObject(Boolean.FALSE);
        cbxVestibular.setModelObject(Boolean.FALSE);
        cbxDistal.setModelObject(Boolean.FALSE);
        cbxMesial.setModelObject(Boolean.FALSE);
        cbxOclusal.setModelObject(Boolean.FALSE);

        ComponentUtils.limparContainer(containerTratamento, target);
        onChangeTipoEvolucao(target);
        autoCompleteConsultaSituacaoDente.limpar(target);

        AtendimentoOdontoPlano newAtendimentoOdontoPlano = new AtendimentoOdontoPlano();
        newAtendimentoOdontoPlano.setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());

        model.setObject(newAtendimentoOdontoPlano);

        onChangeTipoEvolucao(target);

        tblAtendimentoOdontoPlano.update(target);
    }

    private void validarCampos(AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException {
        ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();

        Long tipoEvolucao = atendimentoOdontoPlano.getTipoEvolucao();
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao) && atendimentoOdontoPlano.getDente() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("dente")));
        }

        if (AtendimentoOdontoPlano.Tipo.SEXTANTE.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getSextanteS1() == null
                && atendimentoOdontoPlano.getSextanteS2() == null
                && atendimentoOdontoPlano.getSextanteS3() == null
                && atendimentoOdontoPlano.getSextanteS4() == null
                && atendimentoOdontoPlano.getSextanteS5() == null
                && atendimentoOdontoPlano.getSextanteS6() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("sextante")));
        }

        if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getArcadaInferior() == null
                && atendimentoOdontoPlano.getArcadaSuperior() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("arcada")));
        }

        if (AtendimentoOdontoPlano.Tipo.OUTRO.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getOutro() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("outro")));
        }

        if (atendimentoOdontoPlano.getSituacaoDente() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("situacaoTratamento")));
        }

        if (!validacaoProcesso.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacaoProcesso);
        }
    }

    private void salvar(AjaxRequestTarget target, boolean imprimir) throws DAOException, ValidacaoException {
        if (atendimentoOdontoFicha.getAtendimento() == null) {
            atendimentoOdontoFicha.setAtendimento(getAtendimento());
        }

        AtendimentoOdontoFicha atendimentoOdontoFichaSave = BOFactoryWicket.getBO(AtendimentoFacade.class).salvarAtendimentoOdontoFichaOdontograma(atendimentoOdontoFicha, getAtendimento());

        if (atendimentoOdontoFichaSave != null) {
            if (imprimir) {
                initDialogImpressao(target);
                dlgConfirmacaoImpressao.show(target, atendimentoOdontoFichaSave);
            } else {
                DefaultProntuarioPanel panel = new FichaOdontologicaPanel(getProntuarioController().panelId());
                getProntuarioController().changePanel(target, panel);
            }
        }
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<AtendimentoOdontoFicha>(getProntuarioController().newWindowId(), bundle("impressaoGeradaSucesso", this)) {
                @Override
                public DataReport getDataReport(AtendimentoOdontoFicha object) throws ReportException {
                    return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirFichaClinicaOdontologica(object);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, AtendimentoOdontoFicha object) throws ValidacaoException, DAOException {
                    DefaultProntuarioPanel panel = new FichaOdontologicaPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, panel);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }
}
