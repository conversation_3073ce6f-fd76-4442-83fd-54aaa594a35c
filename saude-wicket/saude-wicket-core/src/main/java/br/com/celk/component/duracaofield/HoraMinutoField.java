package br.com.celk.component.duracaofield;

import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.util.Data;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.convert.IConverter;

/**
 *
 * <AUTHOR>
 */
public class HoraMinutoField extends InputField<Date> {

    public HoraMinutoField(String id, IModel<Date> model) {
        super(id, model, Date.class);
        init();
    }

    public HoraMinutoField(String id) {
        super(id, Date.class);
        init();
    }
    
    private void init(){
        add(new AttributeModifier("class", "hora"));
        add(new AttributeModifier("maxlength", "5"));
        add(new AttributeModifier("size", "5"));
    }

    @Override
    public <C> IConverter<C> getConverter(Class<C> type) {
        return new IConverter<C>() {

            @Override
            public C convertToObject(String value, Locale locale) {
                if(value != null){
                    return (C) Data.getHora(value);
                }
                return (C) "__:__";
            }

            @Override
            public String convertToString(C value, Locale locale) {
                if(value != null){
                    return new SimpleDateFormat("HH:mm").format(((Date)value).getTime());
                }
                return "__:__";
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

}
