package br.com.celk.component.link.behavior;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.link.AbstractAjaxFormSubmittingLink;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.system.javascript.JScript;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormSubmitBehavior;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class FormSubmitBehavior extends AjaxFormSubmitBehavior {

    public FormSubmitBehavior(Form<?> form) {
        super(form, "onclick");
    }

    @Override
    protected void onSubmit(AjaxRequestTarget target) {
        if (getComponent() instanceof AbstractAjaxFormSubmittingLink) {
            ((AbstractAjaxFormSubmittingLink) getComponent()).onClick(target);
        }
    }

    @Override
    protected void onError(AjaxRequestTarget target) {
        INotificationPanel findParent = getComponent().findParent(INotificationPanel.class);

        if (findParent != null) {
            findParent.updateNotificationPanel(target);
            target.add(getForm());
            target.appendJavaScript(JScript.scrollToTop());
            target.appendJavaScript(JScript.initMasks());
            target.appendJavaScript(JScript.initTextAreaLimit());
            target.appendJavaScript(JScript.removeEnterSubmitFromForm());
            target.appendJavaScript(JScript.dirtyForms());
            target.appendJavaScript(JScript.initExpandLinks());
            JScript.setDirtyForm(target, getForm());
        }
    }

    @Override
    protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
        super.updateAjaxAttributes(attributes);
        attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
    }
}
