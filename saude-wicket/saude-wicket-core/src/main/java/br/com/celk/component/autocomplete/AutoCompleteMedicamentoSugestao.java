package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.prontuario.basico.MedicamentoDescricao;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.model.IModel;

import java.util.Iterator;
import java.util.List;

import static br.com.ksisolucoes.util.VOUtils.montarPath;

/**
 * <AUTHOR>
 */
public class AutoCompleteMedicamentoSugestao extends AutoComplete<String> {

    private Long codigoProficional;

    public AutoCompleteMedicamentoSugestao(String id, IModel<String> object) {
        super(id, object);
        init();
    }

    public AutoCompleteMedicamentoSugestao(String id) {
        super(id);
        init();
    }

    public AutoCompleteMedicamentoSugestao(String id, Long codigoProficional) {
        super(id);
        this.codigoProficional = codigoProficional;
        init();
    }

    private void init() {
        getAutoCompleteSettings().setThrottleDelay(600);
    }

    @Override
    protected Iterator<String> getChoices(String input) {
        List<MedicamentoDescricao> medicamentosDescricao = getMedicamentoDescricaos(input, 20);
        return medicamentosDescricao.stream().map(MedicamentoDescricao::getDescricao).iterator();
    }

    public MedicamentoDescricao getMedicamentoDescricao() {
        if (getComponentValue() != null) {
            List<MedicamentoDescricao> medicamentosDescricao = getMedicamentoDescricaos((String) getComponentValue(), 1);
            if (medicamentosDescricao != null && !medicamentosDescricao.isEmpty()) {
                return medicamentosDescricao.get(0);
            }
        }
        return null;
    }

    private List<MedicamentoDescricao> getMedicamentoDescricaos(String input, int maxResults) {
        List<MedicamentoDescricao> medicamentosDescricao = LoadManager.getInstance(MedicamentoDescricao.class)
                .addProperties(new HQLProperties(MedicamentoDescricao.class).getProperties())
                .addProperty(montarPath(MedicamentoDescricao.PROP_TIPO_RECEITA, TipoReceita.PROP_CODIGO))
                .addProperty(montarPath(MedicamentoDescricao.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO))
                .addProperty(montarPath(MedicamentoDescricao.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA))
                .addInterceptor((LoadInterceptor) (hql, alias) -> {
                    hql.addToWhereWhithAnd("(" + alias + ".profissional is null or " + alias + ".profissional = " + codigoProficional + ")");
                    if (StringUtils.trimToNull(input) != null) {
                        hql.addToWhereWhithAnd(hql.getConsultaLiked(alias + ".descricao", input));
                    }
                })
                .setMaxResults(maxResults)
                .start().getList();
        return medicamentosDescricao;
    }

    @Override
    public String getTextValue(String object) {
        return object;
    }

    public boolean getMedicamentoSelecionado() {
        if (getComponentValue() == null) {
            return false;
        }

        String valorAtual = ((String) getComponentValue()).trim();
        if (valorAtual.isEmpty()) {
            return false;
        }

        Iterator<String> choices = getChoices(valorAtual);
        while (choices.hasNext()) {
            String choice = choices.next();
            if (choice.equalsIgnoreCase(valorAtual)) {
                return true;
            }
        }
        return false;
    }

    public void setCodigoProficional(Long codigoProficional) {
        this.codigoProficional = codigoProficional;
    }


}
