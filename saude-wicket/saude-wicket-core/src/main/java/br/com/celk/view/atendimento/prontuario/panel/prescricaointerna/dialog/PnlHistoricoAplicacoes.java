package br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamento;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlHistoricoAplicacoes extends Panel {

    private LoadableObjectModel<ReceituarioItem> model;
    private Table<AtendimentoMedicamento> tblAtendimentoMedicamento;

    public PnlHistoricoAplicacoes(String id) {
        super(id);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();
        init();
    }

    private void init() {
        Form<ReceituarioItem> form = new Form<ReceituarioItem>("form", new CompoundPropertyModel(model = new LoadableObjectModel<ReceituarioItem>(ReceituarioItem.class, false)));

        ReceituarioItem proxy = on(ReceituarioItem.class);

        form.add(new DisabledInputField(path(proxy.getNomeProduto())));

        form.add(tblAtendimentoMedicamento = new Table("tblAtendimentoMedicamento", getColumnsHistorico(), getCollectionProviderHistorico()));
        tblAtendimentoMedicamento.populate();

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    private List<IColumn> getColumnsHistorico() {
        List<IColumn> columns = new ArrayList<IColumn>();

        AtendimentoMedicamento proxy = on(AtendimentoMedicamento.class);

        columns.add(new DateTimeColumn<ReceituarioItem>(bundle("horaAplicacao", this), path(proxy.getDataHoraAplicacao()), path(proxy.getDataHoraAplicacao())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createColumn(bundle("profissional", this), proxy.getAtendimento().getProfissional().getDescricaoFormatado()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderHistorico() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return LoadManager.getInstance(AtendimentoMedicamento.class)
                        .addProperties(new HQLProperties(AtendimentoMedicamento.class).getProperties())
                        .addProperty(VOUtils.montarPath(AtendimentoMedicamento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(AtendimentoMedicamento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoMedicamento.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO), model.getObject().getReceituario()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoMedicamento.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_PRODUTO), model.getObject().getProduto()))
                        .start().getList();
            }
        };
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {}

    public void setReceituarioItem(ReceituarioItem receituarioItem) {
        model.setObject(receituarioItem);
    }

}
