package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.BalancoHidrico;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgLanctosGanhosPerdasInativados extends Window {

    private PnlLanctosGanhosPerdasInativados panel;

    public DlgLanctosGanhosPerdasInativados(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("lanctosInativados"));

        setInitialWidth(750);
        setInitialHeight(205);
        setResizable(true);

        setContent(panel = new PnlLanctosGanhosPerdasInativados(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void setLstGanhosPerdas(List<BalancoHidrico> lstInativos) {
        panel.setLst(lstInativos);
    }
}
