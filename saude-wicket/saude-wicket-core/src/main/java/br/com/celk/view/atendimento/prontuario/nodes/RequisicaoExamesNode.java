package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.RequisicaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.REQUISICAO_EXAMES)
public class RequisicaoExamesNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new RequisicaoExamesPanel(id);
    }
    
    @Override
    public Icon32 getIcone() {
        return Icon32.TUBES;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("requisicaoExames");
    }
    
}
