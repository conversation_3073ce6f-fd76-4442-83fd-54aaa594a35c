package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;

public abstract class DlgAjudaRegistroEspecializado extends Window {

    private PnlAjudaRegistroEspecializado pnlAjudaRegistroEspecializado;

    public DlgAjudaRegistroEspecializado(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("atalhos"));

        setInitialWidth(450);
        setInitialHeight(130);
        setResizable(true);

        setContent(pnlAjudaRegistroEspecializado = new PnlAjudaRegistroEspecializado(getContentId()) {
        });
    }

}