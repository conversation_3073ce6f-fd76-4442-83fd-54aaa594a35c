package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.ValidarFilaEsperaNoAtendimento;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.*;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.msdropdown.MsDropDown;
import br.com.celk.component.msdropdown.MsItem;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.solicitacao.AplicarValidacaoStatusSolicitacao;
import br.com.celk.solicitacao.ValidarSolicitacaoAgendamento;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.prontuario.nodes.SolicitacaoExamesNode;
import br.com.celk.view.atendimento.prontuario.panel.*;
import br.com.celk.view.atendimento.prontuario.panel.encaminhamento.dialog.DlgUnidadeResponsavelSolicitacaoAgendamento;
import br.com.celk.view.atendimento.prontuario.panel.exame.AutorizacaoExameSusPanel;
import br.com.celk.view.atendimento.prontuario.panel.exame.controller.ExameViewUtil;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgBloquearExameRequisicao;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgConcluirExameRequisicao;
import br.com.celk.view.atendimento.prontuario.panel.exame.dialog.DlgExamesTuberculose;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.agendamento.exame.ExameHelper;
import br.com.ksisolucoes.agendamento.exame.dto.*;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryProfissionalCargaHorariaGrupoAtendimentoCboDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.exame.interfaces.dto.SolicitacaoExameDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.geral.interfaces.dto.LoadInterceptorNotExistsExameExameBpaApac;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.exame.ExameUnidadeProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExame;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.SharedResourceReference;
import org.hamcrest.Matchers;
import org.joda.time.LocalDate;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.solicitacaoagendamento.ValidacaoDadosPacienteAtendimento.validaDadosPaciente;
import static br.com.celk.system.javascript.JScript.*;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.CoreMatchers.not;

/**
 * <AUTHOR>
 */
public class ExameSusPanel extends ProntuarioCadastroPanel {

    private Form<Exame> form;
    private Form<ExameProcedimentoDTO> formItem;
    private LongField txtQuantidade;
    private InputField txtComplemento;
    private InputField txtPeso;
    private InputField txtAltura;
    private Table tblItens;
    private AutoCompleteConsultaExameProcedimento autoCompleteExameProcedimento;
    private MultiSelectionTableOld<RequisicaoPadraoExame> tblExamesPadrao;
    private WebMarkupContainer containerExameItem;
    private WebMarkupContainer containerDescricaoDadoClinico;
    private WebMarkupContainer containerPrioridade;
    private CompoundPropertyModel<ExameProcedimentoDTO> modelExameItem;
    private List<RequisicaoPadraoExame> requisicoesPadraoExames = new ArrayList<RequisicaoPadraoExame>();
    private TipoExame tipoExame;
    private RequisicaoPadrao requisicaoPadrao;
    private Exame exame;
    private boolean existsExamesPendentes = true;
    private final List<ExameProcedimento> exameProcedimentoRelacionados = new ArrayList();
    private ExameProcedimentoDTO exameRequisicao;
    private DlgImpressaoObject<List<Exame>> dlgConfirmacaoImpressao;
    private final List<ExameProcedimentoDTO> itensList = new ArrayList<ExameProcedimentoDTO>();
    private final List<ExameProcedimentoDTO> examesEmAberto = new ArrayList<ExameProcedimentoDTO>();
    private final Long tipoConvenio;
    private DlgConfirmacaoSimNao dlgConfirmacao;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private NodoAtendimentoWeb nodoAtendimentoWeb;
    private DlgJustificativaObject dlgJustificativaObject;
    private DlgConfirmacaoObject<ExameProcedimentoDTO> dlgConfirmacaoObject;
    private DlgConfirmacaoOk dlgListaExamesDependentes;
    private List<ExameProcedimento> lstExameProcedimento;
    private DlgConfirmacaoOk dlgConfirmacaoAdicionar;
    private final SoapDTO.ContainerTelaSoap containerTelaSoap;
    private InputArea txaDescricaoDadoClinico;
    private ExameProcedimento exameProcedimentoPrincipal;
    private InputArea txaJustificativaClassificacaoRisco;
    private UsuarioCadsusDado usuarioCadsusDado;

    private DlgConcluirExameRequisicao dlgConcluirExameRequisicao;
    private DlgBloquearExameRequisicao dlgBloquearExameRequisicao;
    private DropDown dropDownEnviarRegulacao;
    private InputArea txaDescricaoEnviarRegulacao;
    private WebMarkupContainer containerSolicitacaoProfissional;
    private String tipoControleRegulacao;
    private WebMarkupContainer containerNaoColocarListaEsperaSus;
    private CheckBoxLongValue checkNaoColocarListaEsperaSus;
    private DlgUnidadeResponsavelSolicitacaoAgendamento dlgUnidadeResponsavelSolicitacaoAgendamento;
    private final boolean edicao;
    private DlgExamesTuberculose dlgExamesTuberculose;
    private DlgExamesTuberculose dlgExamesTuberculoseAvancar;
    private List<Exame> exames = new ArrayList<>();
    private List<Exame> examesImpressos = new ArrayList<>();
    private List<Long> codigosExame = new ArrayList<>();
    private MsDropDown<ClassificacaoRisco> cbxClassificaoRisco;
    private DropDown dropDownSubRisco;
    private WebMarkupContainer containerClassificacaoRisco;
    private MultiSelectionTable<SintomaFatorRisco> tblSintomaFatorRisco;
    private WebMarkupContainer containerLblClassRisco;
    private Label lblSugestaoClassificacaoRisco;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private DropDown<Boolean> cbxRegistrarCid;
    private DateChooser dataDesejada;
    private Label lblDataDesejada;
    private List<ExameProcedimentoDTO> examesJustificativa = new ArrayList();


    public ExameSusPanel(String id, Exame exame, TipoExame tipoExame, Long tipoConvenio, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("solicitacaoExames") + " (" + bundle("sus") + ")");
        this.tipoExame = tipoExame;
        this.exame = exame;
        this.tipoConvenio = tipoConvenio;
        this.containerTelaSoap = containerTelaSoap;
        this.edicao = exame != null && exame.getCodigo() != null;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        form = new Form<Exame>("form", new CompoundPropertyModel<Exame>(getExame()));
        Exame exameProxy = on(Exame.class);


        if (form.getModelObject().getProfissional() == null) {
            form.getModelObject().setProfissional(getAtendimento().getProfissional());
        }
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", true));
        autoCompleteConsultaProfissional.setCodigoEmpresa(getAtendimento().getEmpresa().getCodigo());
        if (carregarPermissaoNoInformarProfissional() != null) {
            autoCompleteConsultaProfissional.setGrupoAtendimentoCbo(nodoAtendimentoWeb.getGrupoAtendimentoCbo());
            if (getExame().getCodigo() == null) {
                form.getModelObject().setProfissional(null);
            }
        } else {
            autoCompleteConsultaProfissional.setEnabled(false);
        }

        form.add(new DisabledInputField(path(exameProxy.getTipoExame().getDescricao())));
        if (tipoExame != null) {
            form.getModelObject().setTipoExame(tipoExame);
        }

        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(exameProxy.getCid())));
        autoCompleteConsultaCid.add(new TempBehaviorV2());

        try {
            IParameterModuleContainer modulo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            String obrigatorioCidRequisicaoExame = modulo.getParametro("ObrigatorioCidRequisicaoExame");
            autoCompleteConsultaCid.setRequired(RepositoryComponentDefault.SIM.equals(obrigatorioCidRequisicaoExame));
        } catch (DAOException ex) {
            Logger.getLogger(EvolucaoUnidadePanel.class.getName()).log(Level.SEVERE, null, ex);
        }


        form.add(getDropDownRequisicoesPadroes());
        form.add(tblExamesPadrao = new MultiSelectionTableOld("tblExamesPadrao", getColumnsRequisicoesPadrao(), getCollectionProviderRequisicoesPadrao()));
        tblExamesPadrao.setScrollY("140px");
        tblExamesPadrao.populate();

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarRequisicaoPadrao(target);
            }
        });

        formItem = new Form<ExameProcedimentoDTO>("formItem", new CompoundPropertyModel<ExameProcedimentoDTO>(getExameRequisicao()));

        ExameProcedimentoDTO itemProxy = on(ExameProcedimentoDTO.class);

        containerExameItem = new WebMarkupContainer("containerExameItem", modelExameItem = new CompoundPropertyModel<ExameProcedimentoDTO>(getExameRequisicao()));
        containerExameItem.setOutputMarkupId(true);

        containerExameItem.add(autoCompleteExameProcedimento = new AutoCompleteConsultaExameProcedimento(path(itemProxy.getExameProcedimento())));
        autoCompleteExameProcedimento.setSexo(getAtendimento().getUsuarioCadsus().getSexo());
        autoCompleteExameProcedimento.setIdadeEmMese(getAtendimento().getUsuarioCadsus().getIdadeEmMeses());
        autoCompleteExameProcedimento.setTipoExame(form.getModelObject().getTipoExame());

        containerExameItem.add(txtQuantidade = new LongField(path(itemProxy.getQuantidade()))
                .setInitialValue(1L)
                .setVMax(99L));
        containerExameItem.add(txtComplemento = new InputField(path(itemProxy.getComplemento())));

        AbstractAjaxButton btnAdicionarExame;
        containerExameItem.add(btnAdicionarExame = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparExameProcedimentoPrincipal(exameProcedimentoPrincipal);
                validarExameParaAdicionar(target, modelExameItem.getObject(), true);
                target.focusComponent(autoCompleteExameProcedimento.getTxtDescricao().getTextField());
            }
        });
        btnAdicionarExame.add(new AjaxEventBehavior("keydown") {
            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);

                attributes.getDynamicExtraParameters()
                        .add("var eventKeycode = Wicket.Event.keyCode(attrs.event);" +
                                "return {keycode: eventKeycode};");

                attributes.setAllowDefault(true);
            }

            protected void onEvent(AjaxRequestTarget target) {
            }
        });
        formItem.add(tblItens = new Table("tblItens", getColumns(), getCollectionProvider()));
        tblItens.populate();

        form.add(containerExameItem);
        form.add(formItem);

        form.add(containerSolicitacaoProfissional = new WebMarkupContainer("containerSolicitacaoProfissional"));
        containerSolicitacaoProfissional.setOutputMarkupPlaceholderTag(true);
        containerSolicitacaoProfissional.add(dropDownEnviarRegulacao = getDropDownEnviarRegulacao(path(exameProxy.getFlagEnviarRegulacao())));
        containerSolicitacaoProfissional.add(txaDescricaoEnviarRegulacao = new InputArea(path(exameProxy.getDescricaoEnviarRegulacao())));

        dropDownEnviarRegulacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean enable = form.getModel().getObject().getFlagEnviarRegulacao().equals(RepositoryComponentDefault.SIM_LONG);
                if (enable) {
                    target.add(txaDescricaoEnviarRegulacao);
                } else {
                    txaDescricaoEnviarRegulacao.limpar(target);
                }
                toggleFieldset(target, containerSolicitacaoProfissional);
            }
        });

        form.add(containerNaoColocarListaEsperaSus = new WebMarkupContainer("containerNaoColocarListaEsperaSus"));
        containerNaoColocarListaEsperaSus.setOutputMarkupId(true);
        containerNaoColocarListaEsperaSus.add(checkNaoColocarListaEsperaSus = new CheckBoxLongValue(path(exameProxy.getNaoColocarListaEsperaSus())));

        checkNaoColocarListaEsperaSus.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean edicao = form.getModel().getObject() != null && form.getModel().getObject().getCodigo() != null
                        && form.getModel().getObject().getNaoColocarListaEsperaSus() != null;
                regrasControleRegulacaoSolicitacaoProfissional(target, edicao, tipoExame.getTipoProcedimento());
            }
        });

        form.add(containerPrioridade = new WebMarkupContainer("containerPrioridade"));
        DropDown cbxPrioridadeLaboratorio = DropDownUtil.getIEnumDropDown(path(exameProxy.getPrioridadeLaboratorio()), Exame.PrioridadeLaboratorio.values(), true, "");
        cbxPrioridadeLaboratorio.addAjaxUpdateValue();
        containerPrioridade.add(cbxPrioridadeLaboratorio);
        form.add(containerPrioridade);

        form.add(containerClassificacaoRisco = new WebMarkupContainer("containerClassificacaoRisco"));
        containerClassificacaoRisco.add(getCbxClassificacaoRisco("classificacaoDeRisco").add(new TempBehaviorV2()));
        containerClassificacaoRisco.add(txaJustificativaClassificacaoRisco = new InputArea("justificativaClassificacaoRisco"));
        form.add(containerClassificacaoRisco);


        try {
            String permiteInformarPrioridadeSolicitacaoExames = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("permiteInformarPrioridadeSolicitacaoExames");
            containerPrioridade.setVisible(RepositoryComponentDefault.SIM.equals(permiteInformarPrioridadeSolicitacaoExames));

            boolean habilitaClassificacaoRiscoExame = habilitaClassificacaoRiscoExame();
            containerClassificacaoRisco.setVisible(habilitaClassificacaoRiscoExame);

            if (habilitaClassificacaoRiscoExame) {
                containerPrioridade.setVisible(false);
            }

            containerSolicitacaoProfissional.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())
                    && RepositoryComponentDefault.SIM.equals(tipoExame.getAgendado()));
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }


        form.add(containerDescricaoDadoClinico = new WebMarkupContainer("containerDescricaoDadoClinico"));
        containerDescricaoDadoClinico.setOutputMarkupId(true);
        loadUsuarioCadsusDado();
        exame.setUsuarioCadsusDado(usuarioCadsusDado);
        containerDescricaoDadoClinico.add(txtAltura = new InputField("usuarioCadsusDado.altura"));
        containerDescricaoDadoClinico.add(txtPeso = new InputField("usuarioCadsusDado.peso"));
        containerDescricaoDadoClinico.add(txaDescricaoDadoClinico = new InputArea(path(exameProxy.getDescricaoDadoClinico())));
        if(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())){
            txaDescricaoDadoClinico.addRequiredClass();
        }
        Boolean habilitaDataDesejadaEmSolicitacaoDeExames = false;

        try {
            habilitaDataDesejadaEmSolicitacaoDeExames = RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("HabilitaDataDesejadaEmSolicitacaoDeExamesProcedimento"));
        } catch (DAOException e) {
            throw new RuntimeException(e);
        }
        form.add(lblDataDesejada = new Label("lblDataDesejada", bundle("dataDesejada")));
        lblDataDesejada.setOutputMarkupId(habilitaDataDesejadaEmSolicitacaoDeExames);
        lblDataDesejada.setVisible(habilitaDataDesejadaEmSolicitacaoDeExames);

        form.add(dataDesejada = new DateChooser(path(exameProxy.getDataDesejada())));
        dataDesejada.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));
        dataDesejada.setVisible(habilitaDataDesejadaEmSolicitacaoDeExames);

        AbstractAjaxButton btnImprimir;
        form.add(btnImprimir = new AbstractAjaxButton("btnImprimir") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarImprimirExame(target);
            }
        });
        btnImprimir.setVisible(false);

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
            }

            @Override
            public boolean isVisible() {
                return SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.equals(containerTelaSoap);
            }
        }.setDefaultFormProcessing(false));

        AbstractAjaxButton btnAvancar;

        form.add(btnAvancar = new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarExameAgrupandoPrestadores(target);
            }
        });

        AbstractAjaxButton btnSalvar;
        form.add(btnSalvar = new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarImprimirExame(target);
            }
        });

        add(form);

        if (tipoExameAutorizacao()) {
            btnSalvar.setVisible(false);
        } else {
            btnAvancar.setVisible(false);
        }

        try {
            configurarPainel();
            getExamesEmAberto();
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        regrasControleRegulacaoSolicitacaoProfissional(null, form.getModel().getObject() != null && form.getModel().getObject().getCodigo() != null, tipoExame.getTipoProcedimento());
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itensList;
            }
        };
    }


    private boolean habilitaClassificacaoRiscoExame() throws DAOException {
        String habilitaClassificacaoRiscoExame = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("habilitaClassificacaoRiscoExame");
        return RepositoryComponentDefault.SIM.equals(habilitaClassificacaoRiscoExame);
    }

    private void adicionarRequisicaoPadrao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        lstExameProcedimento = new ArrayList<ExameProcedimento>();

        if (tblExamesPadrao.getSelectedObjects() == null || tblExamesPadrao.getSelectedObjects().isEmpty()) {
            throw new ValidacaoException(bundle("selecionePeloMenosUmExamePadrao"));
        }
        ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();
        examesJustificativa.clear();
        for (RequisicaoPadraoExame rpe : tblExamesPadrao.getSelectedObjects()) {
            String msg = "";
            msg += ExameHelper.validarExameDependente(rpe.getExameProcedimento(), getAtendimento().getUsuarioCadsus());
            if (!msg.isEmpty()) {
                lstExameProcedimento.add(rpe.getExameProcedimento());
                continue;
            }

            ExameProcedimentoDTO exameProcedimentoDTO = new ExameProcedimentoDTO();
            exameProcedimentoDTO.setExameProcedimento(rpe.getExameProcedimento());
            exameProcedimentoDTO.setComplemento(rpe.getComplemento());
            exameProcedimentoDTO.setQuantidade(rpe.getQuantidade());

            if (existExamePadraoAdicionado(exameProcedimentoDTO)) continue;

            ExameProcedimento ep = ExameHelper.validarRegrasExameProcedimento(exameProcedimentoDTO.getExameProcedimento(), getAtendimento().getUsuarioCadsus());
            if(ep != null) {
                Long regra = ep.getTipoRegra() != null ? ep.getTipoRegra() : ep.getTipoExame().getTipoRegra();
                if ((ExameProcedimento.TipoRegra.EXIGIR_JUSTIFICATIVA.value().equals(regra))) {
                    examesJustificativa.add(exameProcedimentoDTO);
                }
            }

            try {
                if (!validarExameParaAdicionar(target, exameProcedimentoDTO, true)) {
                    return;
                }
            } catch (ValidacaoException ex) {
                ValidacaoProcesso vp = new ValidacaoProcesso(rpe.getExameProcedimento().getDescricaoProcedimento());

                vp.add(new ValidacaoProcesso(ex.getMessage()));
                validacaoProcesso.add(vp);
            }
        }
        if (!validacaoProcesso.getValidacaoProcessoList().isEmpty()) {
            Loggable.log.debug(validacaoProcesso.toString());
            WindowUtil.addModal(target, this, dlgConfirmacaoOk = new DlgConfirmacaoOk(WindowUtil.newModalId(this), validacaoProcesso.toString(), 150, 500) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
            dlgConfirmacaoOk.show(target);
        }
        if (CollectionUtils.isNotNullEmpty(lstExameProcedimento)) {
            String msg = bundle("msgExamesListaSolicitadoDependente") + " \n";
            for (ExameProcedimento ep : lstExameProcedimento) {
                msg += ep.getDescricaoFormatado() + "\n";
            }
            initDlgListaExamesDependentes(target, msg);
        }
    }

    private boolean exigePreenchimentoPesoAltura() {
        for (ExameProcedimentoDTO dto : itensList) {
            if (RepositoryComponentDefault.SIM_LONG.equals(dto.getExameProcedimento().getFlagInformaAlturaPeso())) {
                return true;
            }
        }
        return false;
    }

    private void initDialogExamesTuberculose(AjaxRequestTarget target, StringBuilder msg) {
        getProntuarioController().addWindow(target, dlgExamesTuberculose = new DlgExamesTuberculose(getProntuarioController().newWindowId()) {
            @Override
            public DataReport onImprimir(AjaxRequestTarget target, Long hiv, Long retratamento) throws ReportException {
                return null;
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long hiv, Long retratamento) throws ValidacaoException, DAOException {
                confirmarExames(target, hiv, retratamento);
            }
        });
        dlgExamesTuberculose.show(target, msg, null, null);
    }

    private void initDialogExamesTuberculoseAvancar(AjaxRequestTarget target, StringBuilder msg) {
        getProntuarioController().addWindow(target, dlgExamesTuberculoseAvancar = new DlgExamesTuberculose(getProntuarioController().newWindowId()) {
            @Override
            public DataReport onImprimir(AjaxRequestTarget target, Long hiv, Long retratamento) throws ReportException {
                return null;
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Long hiv, Long retratamento) throws ValidacaoException, DAOException {
                confirmarExamesAvancar(target, hiv, retratamento);
            }
        });
        dlgExamesTuberculoseAvancar.show(target, msg, null, null);
    }

    private void confirmarExamesAvancar(AjaxRequestTarget target, Long hiv, Long retratamento) throws ValidacaoException, DAOException {
        for (ExameProcedimentoDTO exameProcedimentoDTO : itensList) {
            if (RepositoryComponentDefault.SIM_LONG.equals(exameProcedimentoDTO.getExameProcedimento().getFlagExameTuberculose())) {
                exameProcedimentoDTO.setHiv(hiv);
                exameProcedimentoDTO.setRetratamento(retratamento);
            }
        }
        confirmarAvancar(target);
    }

    private void confirmarAvancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        ExameCadastroAprovacaoDTO dto = criarExameCadastroAprovacaoDTO();
        validarDataDesejada(((BaseExame) getExame()).getDataDesejada());
        dto.getExameProcedimentoDTOs().addAll(itensList);
        dto.setCodigoExameCadastrado(getExame().getCodigo());

        if(getExame().getDataDesejada() != null){
            dto.setDataDesejada(((BaseExame) getExame()).getDataDesejada());
        }
        if (carregarPermissaoNoInformarProfissional() != null) {
            TabelaCbo tabelaCbo = getTabelaCbo(target);
            if (tabelaCbo != null && tabelaCbo.getCbo() != null) {
                dto.setCodigoCbo(tabelaCbo.getCbo());
            }
        }

        BOFactoryWicket.getBO(ExameFacade.class).salvarExameSusPorPrestadores(dto, getExame());

        DefaultProntuarioPanel panel;
        if (carregarPermissaoNoInformarProfissional() != null) {
            panel = new AutorizacaoExameSusPanel(getProntuarioController().panelId(), this.tipoExame, (Profissional) autoCompleteConsultaProfissional.getComponentValue(), this.containerTelaSoap);
        } else {
            panel = new AutorizacaoExameSusPanel(getProntuarioController().panelId(), this.tipoExame, this.containerTelaSoap);
        }
        getProntuarioController().changePanel(target, panel);
    }

    private void confirmarExames(AjaxRequestTarget target, Long hiv, Long retratamento) throws ValidacaoException, DAOException {
        for (ExameProcedimentoDTO exameProcedimentoDTO : itensList) {
            if (RepositoryComponentDefault.SIM_LONG.equals(exameProcedimentoDTO.getExameProcedimento().getFlagExameTuberculose())) {
                exameProcedimentoDTO.setHiv(hiv);
                exameProcedimentoDTO.setRetratamento(retratamento);
            }
        }
        salvar(target);
    }

    private boolean existExamePadraoAdicionado(ExameProcedimentoDTO exameProcedimentoDTO) {
        if (CollectionUtils.isNotNullEmpty(itensList)) {
            for (ExameProcedimentoDTO dto : itensList) {
                if (dto.getExameProcedimento().getCodigo().equals(exameProcedimentoDTO.getExameProcedimento().getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void viewDlgConcluirExameRequisicao(AjaxRequestTarget target, ExameRequisicao examesPendentes, final ExameProcedimentoDTO exameProcedimentoDTO) throws DAOException, ValidacaoException {
        getProntuarioController().addWindow(target, dlgConcluirExameRequisicao = new DlgConcluirExameRequisicao(getProntuarioController().newWindowId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ExameRequisicao exameRequisicao) throws ValidacaoException, DAOException {
                if (exameRequisicao.getSolicitacaoAgendamento() != null) {
                    BOFactoryWicket.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(exameRequisicao
                                    .getSolicitacaoAgendamento()
                                    .getCodigo(),
                            Bundle.getStringApplication("msg_cancelamento_realizado_atraves_tela_atendimento"),
                            true);
                }

                salvarExameRequisicao(exameRequisicao);
                close(target);
                validarExameParaAdicionar(target, exameProcedimentoDTO, false);
            }
        });
        dlgConcluirExameRequisicao.show(target, examesPendentes);
    }


    private void salvarExameRequisicao(ExameRequisicao modelObject) throws DAOException, ValidacaoException {
        ExameRequisicao requisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_CODIGO, modelObject.getCodigo()))
                .start().getVO();
        if (requisicao != null) {
            requisicao.getExame().setCid((Cid) autoCompleteConsultaCid.getComponentValue());
            requisicao.setStatus(modelObject.getStatus());
            requisicao.setDataResultado(modelObject.getDataResultado());
            requisicao.setDescricaoResultado(modelObject.getDescricaoResultado());
            BOFactory.save(requisicao);
        }
    }

    private List<ExameRequisicao> getExamesPendentes() {
        ExameRequisicao proxy = Lambda.on(ExameRequisicao.class);

        LoadManager loadManager = LoadManager.getInstance(ExameRequisicao.class)
                .addProperties(ExameRequisicao.PROP_CODIGO)
                .addProperties(ExameRequisicao.PROP_STATUS)
                .addProperty(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_DATA_SOLICITACAO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO));

        addFilterStatusEqualsAbertoOrIsNull(proxy, loadManager);

        loadManager
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), QueryCustom.QueryCustomParameter.IGUAL, ExameRequisicao.Status.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getCodigo()), QueryCustom.QueryCustomParameter.DIFERENTE, getAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getUsuarioCadsus().getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getTipoExame().getTipo()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.Tipo.LACEN.value()))
                .addInterceptor(new LoadInterceptorNotExistsExameExameBpaApac(LoadInterceptorNotExistsExameExameBpaApac.Tipo.EXAME_REQUISICAO));

        return loadManager.start().getList();
    }

    private UsuarioCadsusDado loadUsuarioCadsusDado() {
        return usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addProperties(new HQLProperties(UsuarioCadsusDado.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus()))
                .start().getVO();
    }

    private void addFilterStatusEqualsAbertoOrIsNull(ExameRequisicao proxy, LoadManager loadManager) {
        loadManager.addParameter(new QueryCustom.QueryCustomParameter(
                new BuilderQueryCustom.QueryGroupAnd(
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IGUAL, ExameRequisicao.Status.ABERTO.value()))),
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IS_NULL))))));
    }

    private void getExamesEmAberto() throws ValidacaoException, DAOException {
        List<SolicitacaoExameDTO> examesAtendimento = BOFactoryWicket.getBO(ExameFacade.class).consultarExamesAdicionadoAtendimento(getAtendimento());
        List<SolicitacaoExameDTO> outrosExamesAtendimento = select(examesAtendimento, having(on(SolicitacaoExameDTO.class).getExame(), not(this.exame)));
        for (SolicitacaoExameDTO se : outrosExamesAtendimento) {
            ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO = BOFactoryWicket.getBO(ExameFacade.class).carregarExame(se.getExame().getCodigo());
            examesEmAberto.addAll(exameCadastroAprovacaoDTO.getExameProcedimentoDTOs());
        }
    }


    private void initDlgListaExamesDependentes(AjaxRequestTarget target, String msg) {
        if (dlgListaExamesDependentes == null) {
            WindowUtil.addModal(target, this, dlgListaExamesDependentes = new DlgConfirmacaoOk(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        if (msg != null && !msg.isEmpty()) {
            dlgListaExamesDependentes.setMessage(target, msg);
            dlgListaExamesDependentes.show(target);
        }
    }

    private ICollectionProvider getCollectionProviderRequisicoesPadrao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return requisicoesPadraoExames;
            }
        };
    }

    private List<IColumn> getColumnsRequisicoesPadrao() {
        List<IColumn> columns = new ArrayList<IColumn>();

        RequisicaoPadraoExame proxy = on(RequisicaoPadraoExame.class);

        columns.add(createColumn(bundle("descricao"), proxy.getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getExameProcedimento().getTipoExame().getDescricao()));

        return columns;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ExameProcedimentoDTO itemProxy = on(ExameProcedimentoDTO.class);

        columns.add(getActionsColumn());
        columns.add(createColumn(bundle("exame"), itemProxy.getExameProcedimento().getDescricaoFormatado()));
        columns.add(createColumn(bundle("tipoExame"), itemProxy.getExameProcedimento().getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("complemento"), itemProxy.getComplemento()));
        columns.add(createColumn(bundle("quantidade"), itemProxy.getQuantidade()));

        return columns;
    }

    private IColumn getActionsColumn() {
        return new MultipleActionCustomColumn<ExameProcedimentoDTO>() {
            @Override
            public void customizeColumn(ExameProcedimentoDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ExameProcedimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException {
                        editarItem(target, modelObject);
                    }
                }).setEnabled(!rowObject.isAmostra());
                addAction(ActionType.REMOVER, rowObject, new IModelAction<ExameProcedimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException {
                        removerItem(modelObject);
                        updateItens(target);
                    }
                });
            }
        };
    }

    public void editarItem(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) {
        limparItem(target);
        modelObject.setEdicao(true);
        setExameRequisicao(modelObject);
        showContainerItem(target);
    }

    public void removerItem(ExameProcedimentoDTO modelObject) {
        for (int i = 0; i < itensList.size(); i++) {
            if (itensList.get(i) == modelObject) {
                itensList.remove(i);
                break;
            }
        }

        List<ExameProcedimentoDTO> amostras =
                Lambda.select(itensList,
                        Lambda.having(Lambda.on(ExameProcedimentoDTO.class).isAmostra(), Matchers.equalTo(true))
                                .and(Lambda.having(Lambda.on(ExameProcedimentoDTO.class).getExamePrincipal(), Matchers.equalTo(modelObject))));
        for (ExameProcedimentoDTO amostra : amostras) {
            itensList.remove(amostra);
        }
    }

    private Exame getExame() {
        if (this.exame == null) {
            this.exame = new Exame();
        }

        return this.exame;
    }

    private ExameProcedimentoDTO getExameRequisicao() {
        if (this.exameRequisicao == null) {
            this.exameRequisicao = new ExameProcedimentoDTO();
        }

        return this.exameRequisicao;
    }

    public void updateItens(AjaxRequestTarget target) {
        tblItens.update(target);
    }

    public void limparItem(AjaxRequestTarget target) {
        setExameRequisicao(null);
        autoCompleteExameProcedimento.setEnabled(true);
        autoCompleteExameProcedimento.limpar(target);
        txtQuantidade.limpar(target);
        txtComplemento.limpar(target);
    }

    public AutoCompleteConsulta getAutoCompleteExame() {
        return autoCompleteExameProcedimento;
    }

    public void configurarPainel() throws ValidacaoException, DAOException {
        if (getExame().getCodigo() != null) {
            ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO;
            exameCadastroAprovacaoDTO = BOFactoryWicket.getBO(ExameFacade.class).carregarExame(getExame().getCodigo());

            itensList.clear();
            itensList.addAll(exameCadastroAprovacaoDTO.getExameProcedimentoDTOs());
        }
    }

    public void setExameRequisicao(ExameProcedimentoDTO exameProcedimentoDTOexame) {
        this.exameRequisicao = VOUtils.cloneObject(exameProcedimentoDTOexame);
        modelExameItem.setObject(getExameRequisicao());
    }

    public void showContainerItem(AjaxRequestTarget target) {
        target.appendJavaScript(JScript.showFieldset(containerExameItem));
        target.appendJavaScript(JScript.initExpandLinks());
    }

    public void adicionarItem(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO) throws ValidacaoException, DAOException {
        Double valorProcedimento = exameProcedimentoDTO.getExameProcedimento().getValorProcedimento();
        if (Coalesce.asDouble(valorProcedimento) == 0D) {
            valorProcedimento = ProcedimentoHelper.getValorServicoAmbulatorial(exameProcedimentoDTO.getExameProcedimento().getProcedimento());
        }
        exameProcedimentoDTO.setValor(valorProcedimento);

        if (exameProcedimentoDTO.isEdicao()) {
            int idx = 0;
            for (ExameProcedimentoDTO dto : itensList) {
                if (dto.getExameProcedimento().equals(exameProcedimentoDTO.getExameProcedimento())) {
                    itensList.remove(idx);
                    break;
                }
                idx++;
            }
        }

        initDlgListaExamesDependentes(target, ExameHelper.validarExameDependente(exameProcedimentoDTO.getExameProcedimento(), getAtendimento().getUsuarioCadsus()));
        itensList.add(exameProcedimentoDTO);
        updateItens(target);
        limparItem(target);
        adicionaExamesRelacionados(target, exameProcedimentoDTO);
        updateItens(target);
        limparItem(target);
        exameProcedimentoDTO.setEdicao(false);
    }

    private void adicionaExamesRelacionados(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO) throws ValidacaoException, DAOException {
        final Date dataCompetencia;
        try {
            dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
        } catch (ValidacaoException ex) {
            Logger.getLogger(ExameSusPanel.class.getName()).log(Level.SEVERE, null, ex);
            throw new DAOException(ex);
        }

        LoadManager load = LoadManager.getInstance(ExameProcedimentoEloExame.class);
        load.addProperties(new HQLProperties(ExameProcedimentoEloExame.class).getProperties());
        load.addProperties(new HQLProperties(ExameProcedimento.class, ExameProcedimentoEloExame.PROP_EXAME_PROCEDIMENTO_RELACIONADO).getProperties());
        load.addParameter(new QueryCustom.QueryCustomParameter(ExameProcedimentoEloExame.PROP_EXAME_PROCEDIMENTO, exameProcedimentoDTO.getExameProcedimento()));
        load.addParameter(new QueryCustom.QueryCustomParameter(ExameProcedimentoEloExame.PROP_EXAME_PROCEDIMENTO_RELACIONADO, QueryCustom.QueryCustomParameter.NOT_IN, exameProcedimentoRelacionados));
        load.addParameter(new QueryCustom.QueryCustomParameter(ExameProcedimentoEloExame.PROP_EXAME_PROCEDIMENTO_RELACIONADO, QueryCustom.QueryCustomParameter.NOT_IN, exameProcedimentoPrincipal));

        load.addInterceptor(new LoadInterceptor() {
            @Override
            public void customHQL(HQLHelper hql, String alias) {
                HQLHelper hqlProcedimento = hql.getNewInstanceSubQuery();
                hqlProcedimento.addToSelect("pc.id.procedimento.codigo");
                hqlProcedimento.addToFrom("ProcedimentoCompetencia pc");
                hqlProcedimento.addToWhereWhithAnd("pc.id.dataCompetencia = ", dataCompetencia);
                hqlProcedimento.addToWhereWhithAnd("coalesce(pc.tipoSexo, '" + ProcedimentoCompetencia.SEXO_INDIFERENTE + "') in ", Arrays.asList(getAtendimento().getUsuarioCadsus().getSexo(), ProcedimentoCompetencia.SEXO_INDIFERENTE, ProcedimentoCompetencia.SEXO_NAO_APLICA));
                hqlProcedimento.addToWhereWhithAnd("pc.id.procedimento.codigo = " + alias + ".exameProcedimentoRelacionado.procedimento.codigo");
                hqlProcedimento.addToWhereWhithAnd("pc.valorIdadeMaxima >= ", getAtendimento().getUsuarioCadsus().getIdadeEmMeses());
                hqlProcedimento.addToWhereWhithAnd("pc.valorIdadeMinima <= ", getAtendimento().getUsuarioCadsus().getIdadeEmMeses());

                StringBuilder whereProcedimento = new StringBuilder();
                whereProcedimento.append("1 = case when (").append(alias).append(".exameProcedimentoRelacionado.procedimento.codigo is not null)")
                        .append(" then case when ").append(alias).append(".exameProcedimentoRelacionado.procedimento.codigo = (").append(hqlProcedimento.getQuery()).append(")")
                        .append(" then 1 else 0 end else 1 end");

                hql.addToWhereWhithAnd(whereProcedimento.toString());
            }
        });

        List<ExameProcedimentoEloExame> examesRelacionados = load.start().getList();
        if (!examesRelacionados.isEmpty()) {
            List<ExameProcedimento> examesProcedimentos = Lambda.extract(examesRelacionados, Lambda.on(ExameProcedimentoEloExame.class).getExameProcedimentoRelacionado());

            /* Remove da lista padrão para não duplicar o exame na lista no caso possuir exame relacionado */
            List<ExameProcedimentoDTO> listExamesJustificativa = Lambda.select(examesJustificativa, Lambda.having(Lambda.on(ExameProcedimentoDTO.class).getExameProcedimento(), Matchers.isIn(examesProcedimentos))
                    .and(Lambda.having(Lambda.on(ExameProcedimentoDTO.class), Matchers.not(exameProcedimentoDTO))));
            if (CollectionUtils.isNotNullEmpty(listExamesJustificativa)) {
                for (ExameProcedimentoDTO exameRemover : listExamesJustificativa) {
                    examesProcedimentos.remove(exameRemover.getExameProcedimento());
                }
            }
            if (!CollectionUtils.isNotNullEmpty(examesProcedimentos)) return;

            /**
             * Alternativa criada para conseguir analisar os exames adicionado manualmente e evitar exames duplicado. Se ao adicionar um EXAME que contem outros exames relacionado ao mesmo, e na mesma lista haver exames repetidos,
             * será eliminado o adicionado manualmente e preservado o que está com relação.
             */
            List<ExameProcedimentoDTO> listExamesRelacionadosAdicionadosManualmente = Lambda.select(itensList, Lambda.having(Lambda.on(ExameProcedimentoDTO.class).getExameProcedimento(), Matchers.isIn(examesProcedimentos))
                    .and(Lambda.having(Lambda.on(ExameProcedimentoDTO.class), Matchers.not(exameProcedimentoDTO))));
            if (CollectionUtils.isNotNullEmpty(listExamesRelacionadosAdicionadosManualmente)) {
                itensList.removeAll(listExamesRelacionadosAdicionadosManualmente);
            }

            exameProcedimentoRelacionados.addAll(examesProcedimentos);
            if (exameProcedimentoDTO.getExameProcedimento() != null) {
                exameProcedimentoPrincipal = exameProcedimentoDTO.getExameProcedimento();
            }
            for (ExameProcedimento exameProcedimentoRelacionado : examesProcedimentos) {
                ExameProcedimentoDTO dto = new ExameProcedimentoDTO();
                dto.setExameRelacionado(true);
                dto.setExamePrincipal(exameProcedimentoDTO);
                if (exameProcedimentoRelacionado.equals(exameProcedimentoDTO.getExameProcedimento())) {
                    dto.setAmostra(true);
                }
                dto.setExameProcedimento(exameProcedimentoRelacionado);
                dto.setQuantidade(1L);
                validarCotaQuantidade(target, dto);
            }
            exameProcedimentoRelacionados.removeAll(examesProcedimentos);
        }
    }

    public void validarRegra(AjaxRequestTarget target, ExameProcedimentoDTO dto) throws ValidacaoException, DAOException {
        ExameProcedimento ep = ExameHelper.validarRegrasExameProcedimento(dto.getExameProcedimento(), getAtendimento().getUsuarioCadsus());
        if (ep != null) {

            Long regra = ep.getTipoRegra() != null ? ep.getTipoRegra() : ep.getTipoExame().getTipoRegra();
            Long quantidadeDias = ep.getQuantidadeDias() == null ? ep.getTipoExame().getQuantidadeDias() : ep.getQuantidadeDias();

            if (ExameProcedimento.TipoRegra.EMITIR_AVISO.value().equals(regra)) {
                dlgConfirmacaoObject = new DlgConfirmacaoObject<ExameProcedimentoDTO>(getProntuarioController().newWindowId()) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target, ExameProcedimentoDTO modelObject) throws ValidacaoException, DAOException {
                        adicionarItem(target, modelObject);
                    }
                };
                getProntuarioController().addWindow(target, dlgConfirmacaoObject);
                dlgConfirmacaoObject.setMessage(target, bundle("msgTempoLimiteExameXDiasX", ep.getDescricaoFormatado(), quantidadeDias));
                dlgConfirmacaoObject.show(target, dto);
            } else if (ExameProcedimento.TipoRegra.EXIGIR_JUSTIFICATIVA.value().equals(regra)) {
                if (dlgJustificativaObject == null) {
                    dlgJustificativaObject = new DlgJustificativaObject(getProntuarioController().newWindowId(), bundle("justificativa")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target, String motivo, Serializable object) throws ValidacaoException, DAOException {
                            if(!examesJustificativa.isEmpty()){
                                for (ExameProcedimentoDTO dto : examesJustificativa){
                                    dto.setJustificativa(motivo);
                                    adicionarItem(target, dto);
                                }
                                examesJustificativa.clear();
                            }else{
                                ((ExameProcedimentoDTO) object).setJustificativa(motivo);
                                adicionarItem(target, (ExameProcedimentoDTO) object);
                            }

                        }
                    };
                    getProntuarioController().addWindow(target, dlgJustificativaObject);
                }
                dlgJustificativaObject.setObject(dto);
                dlgJustificativaObject.show(target);
            } else if (ExameProcedimento.TipoRegra.NEGAR.value().equals(regra)) {
                exameProcedimentoRelacionados.clear();
                throw new ValidacaoException(bundle("msgTempoLimiteExameXDiasX", ep.getDescricaoFormatado(), quantidadeDias));
            }
        } else {
            adicionarItem(target, dto);
        }
    }

    public boolean validarExameParaAdicionar(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO, boolean validaPendente) throws ValidacaoException, DAOException {

        if (exameProcedimentoDTO.getExameProcedimento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_exame"));
        }
        if (Coalesce.asLong(exameProcedimentoDTO.getQuantidade()) <= 0L) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_deve_ser_maior_que_zero"));
        }

        if (!itensList.isEmpty()) {
            if (exameProcedimentoDTO.getExameProcedimento().getTipoExame() == null
                    || !exameProcedimentoDTO.getExameProcedimento().getTipoExame().equals(itensList.get(0).getExameProcedimento().getTipoExame())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_somente_exames_mesmo_tipo_exame"));
            }
            if (exameProcedimentoDTO.getExameProcedimento().getProcedimento() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_exame_sem_procedimento_definido"));
            }
        }

        for (ExameProcedimentoDTO item_ : itensList) {
            if (item_.getExameProcedimento() != null && exameProcedimentoDTO.getExameProcedimento() != null) {
                if (item_.getExameProcedimento().equals(exameProcedimentoDTO.getExameProcedimento())
                        && item_.getExameProcedimento() != exameProcedimentoDTO.getExameProcedimento()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_exame_ja_adicionado"));
                }
            }
        }

        if (validaPendente && existsExamesPendentes) {
            List<ExameRequisicao> examesPendentes = getExamesPendentes();
            if (!br.com.celk.util.CollectionUtils.isAllEmpty(examesPendentes) && examesPendentes.size() > 0) {
                for (ExameRequisicao ep : examesPendentes) {
                    if (ep.getExameProcedimento().getCodigo().equals(exameProcedimentoDTO.getExameProcedimento().getCodigo())) {
                        if (validaExamePendente(ep, exameProcedimentoDTO.getExameProcedimento())) {
                            if (tipoExame.getTipoProcedimento() != null && TipoProcedimento.TipoValidacao.BLOQUEIO.value().equals(tipoExame.getTipoProcedimento().getFlagBloqueiaPendente())) {
                                throw new ValidacaoException(Bundle.getStringApplication("msgBloqueiaCasoExisteProcedimentoPendente", exameProcedimentoDTO.getExameProcedimento().getDescricaoFormatado()));
                            } else if (form.getModelObject().getTipoExame().getTipoProcedimento() != null &&
                                    RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getTipoExame().getTipoProcedimento().getFlagValidaPendente())) {
                                viewDlgConcluirExameRequisicao(target, ep, exameProcedimentoDTO);
                                return false;
                            }
                        } else {
                            ep.setStatus(ExameRequisicao.Status.CONCLUIDO.value());
                            ep.setDataResultado(DataUtil.getDataAtual());
                            ep.setDescricaoResultado("Realizado");
                            salvarExameRequisicao(ep);
                        }
                    }
                }
            } else {
                existsExamesPendentes = false;
            }
        }

        for (ExameProcedimentoDTO sed : examesEmAberto) {
            if (sed.getExameProcedimento().getCodigo() != null && exameProcedimentoDTO.getExameProcedimento() != null) {
                if (sed.getExameProcedimento().getCodigo().equals(exameProcedimentoDTO.getExameProcedimento().getCodigo())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_solicitacao_pendente_exame"));
                }
            }
        }

        validarCotaQuantidade(target, exameProcedimentoDTO);
        return true;
    }

    private boolean validaExamePendente(ExameRequisicao exameRequisicao, ExameProcedimento exameProcedimento) {
        int diasValidacaoExamePendente = exameProcedimento.getDiasValidacaoExamePendente() == null ? 0 : exameProcedimento.getDiasValidacaoExamePendente().intValue();
        if (diasValidacaoExamePendente == 0) {
            TipoExame tipoExame = getTipoExame(exameProcedimento);
            tipoExame.getDiasValidacaoExamePendente();
            diasValidacaoExamePendente = tipoExame.getDiasValidacaoExamePendente() == null ? 0 : tipoExame.getDiasValidacaoExamePendente().intValue();
        }
        if (diasValidacaoExamePendente > 0) {
            return exameRequisicao.getExame().getDataSolicitacao() != null && exameRequisicao.getExame().getDataSolicitacao().after(Data.removeDias(DataUtil.getDataAtual(), diasValidacaoExamePendente));
        } else {
            return true;
        }
    }

    private TipoExame getTipoExame(ExameProcedimento exameProcedimento) {
        return tipoExame = LoadManager.getInstance(TipoExame.class)
                .addProperties(new HQLProperties(TipoExame.class).getProperties())
                .addProperty(VOUtils.montarPath(TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_BLOQUEIA_PENDENTE))
                .setId(exameProcedimento.getTipoExame().getCodigo())
                .start().getVO();
    }

    private void validarCotaQuantidade(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO) throws DAOException, ValidacaoException {
        int diaInicioCompetencia = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
        Date dataCompetencia = Data.competenciaData(diaInicioCompetencia, DataUtil.getDataAtual());

        ExameUnidadeCompetencia exameUnidadeCompetencia = LoadManager.getInstance(ExameUnidadeCompetencia.class)
                .addProperty(ExameUnidadeCompetencia.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_TIPO_EXAME, exameProcedimentoDTO.getExameProcedimento().getTipoExame()))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_DATA_COMPETENCIA, dataCompetencia))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_EMPRESA, getAtendimento().getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameUnidadeCompetencia.PROP_EMPRESA, Empresa.PROP_TIPO_UNIDADE), Empresa.TIPO_ESTABELECIMENTO_UNIDADE))
                .start().getVO();

        Empresa empresa = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        TipoAtendimento tipoAtendimento = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento();
        ProcedimentoHelper.validaQuantidadeMaxima(exameProcedimentoDTO, empresa, tipoAtendimento);

        if (exameUnidadeCompetencia != null) {
            ExameUnidadeProcedimentoCompetencia eupc = LoadManager.getInstance(ExameUnidadeProcedimentoCompetencia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeProcedimentoCompetencia.PROP_EXAME_UNIDADE_COMPETENCIA, exameUnidadeCompetencia))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeProcedimentoCompetencia.PROP_EXAME_PROCEDIMENTO, exameProcedimentoDTO.getExameProcedimento()))
                    .start().getVO();

            if (eupc != null) {
                Dinheiro cotaQuantidade = new Dinheiro(Coalesce.asLong(eupc.getTetoFisico()))
                        .subtrair(new Dinheiro(Coalesce.asLong(eupc.getTetoFisicoRealizado())))
                        .subtrair(new Dinheiro(Coalesce.asLong(exameProcedimentoDTO.getQuantidade())));

                if (cotaQuantidade.compareTo(Dinheiro.ZERO) < 0) {
                    initDlgConfirmacao(target, exameProcedimentoDTO, eupc.getTetoFisico() - Coalesce.asLong(eupc.getTetoFisicoRealizado()));
                    return;
                }
            }
        }
        validarRegra(target, exameProcedimentoDTO);
    }

    private void initDlgConfirmacao(AjaxRequestTarget target, final ExameProcedimentoDTO dto, Long saldoDisponivel) {
        if (dlgConfirmacao == null) {
            dlgConfirmacao = new DlgConfirmacaoSimNao(getProntuarioController().newWindowId(), bundle("msgExameNaoPossuiCotaFisicaDisponivelDesejaAdicionar", dto.getExameProcedimento().getDescricaoProcedimento(), saldoDisponivel)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    validarRegra(target, dto);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacao);
        }
        dlgConfirmacao.show(target);
    }

    private DropDown getDropDownRequisicoesPadroes() {
        DropDown dropDownRequisicoesPadrao = new DropDown("requisicoesPadroes", new PropertyModel(this, "requisicaoPadrao"));
        dropDownRequisicoesPadrao.addChoice(null, bundle("selecione"));

        List<RequisicaoPadrao> requisicaoPadraoList = LoadManager.getInstance(RequisicaoPadrao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoPadrao.PROP_STATUS, RequisicaoPadrao.STATUS_ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoPadrao.PROP_TIPO_EXAME, tipoExame))
                .addSorter(new QueryCustom.QueryCustomSorter(RequisicaoPadrao.PROP_DESCRICAO))
                .start().getList();
        for (RequisicaoPadrao rp : requisicaoPadraoList) {
            dropDownRequisicoesPadrao.addChoice(rp, rp.getDescricao());
        }

        dropDownRequisicoesPadrao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurarRequisicoesPadraoExames(requisicaoPadrao);
                tblExamesPadrao.updateAndClearSelection(target);
            }
        });
        return dropDownRequisicoesPadrao;
    }

    private void procurarRequisicoesPadraoExames(RequisicaoPadrao requisicaoPadrao) {
        if (requisicaoPadrao != null) {
            try {
                Profissional profissional = (Profissional) autoCompleteConsultaProfissional.getComponentValue();
                if (profissional == null) {
                    profissional = getAtendimento().getProfissional();
                }
                requisicoesPadraoExames = BOFactoryWicket.getBO(AtendimentoFacade.class).requisicoesPadraoExames(requisicaoPadrao, profissional);
            } catch (DAOException ex) {
                Logger.getLogger(ExameNaoSusViewPanel.class.getName()).log(Level.SEVERE, null, ex);
            } catch (ValidacaoException ex) {
                Logger.getLogger(ExameNaoSusViewPanel.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else {
            requisicoesPadraoExames = new ArrayList<RequisicaoPadraoExame>();
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(OnLoadHeaderItem.forScript(JScript.showFieldset(containerExameItem)));
        response.render(OnLoadHeaderItem.forScript(JScript.focusComponent(autoCompleteExameProcedimento.getTxtDescricao().getTextField())));
        response.render(OnDomReadyHeaderItem.forScript(JScript.scrollToTopTimeout(500L)));

        if(RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getFlagEnviarRegulacao())){
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(containerSolicitacaoProfissional)));
        }

        if(form.getModelObject() != null && RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getNaoColocarListaEsperaSus())){
            response.render(OnLoadHeaderItem.forScript(toggleFieldset(containerNaoColocarListaEsperaSus)));
        }
    }

    private DropDown<Long> getDropDownEnviarRegulacao(String id){
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(RepositoryComponentDefault.NAO_LONG, WicketMethods.bundle("nao", this));
        dropDown.addChoice(RepositoryComponentDefault.SIM_LONG, WicketMethods.bundle("sim", this));

        return dropDown;
    }

    private ExameCadastroAprovacaoDTO criarExameCadastroAprovacaoDTO() {
        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();

        dto.setMotivo(getExame().getDescricaoDadoClinico());
        dto.setPrioridadeLaboratorio(getExame().getPrioridadeLaboratorio());
        dto.setClassificacaoDeRisco(getExame().getClassificacaoDeRisco());
        dto.setJustificativaClassificacaoRisco(getExame().getJustificativaClassificacaoRisco());
        dto.setUsuarioCadsusDado(usuarioCadsusDado);
        dto.setDataSolicitacao(DataUtil.getDataAtual());
        dto.setAtendimento(getAtendimento());
        dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
        dto.setCodigoProfissional(((Profissional) autoCompleteConsultaProfissional.getComponentValue()).getCodigo());
        dto.setNomeProfissional(((Profissional) autoCompleteConsultaProfissional.getComponentValue()).getNome());
        dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
        dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
        dto.setNaoColocarListaEsperaSus(getExame().getNaoColocarListaEsperaSus());
        dto.setFlagEnviarRegulacao(getExame().getFlagEnviarRegulacao());
        dto.setDescricaoEnviarRegulacao(getExame().getDescricaoEnviarRegulacao());
        dto.setDataDesejada(getExame().getDataDesejada());
        Cid cid = (Cid) autoCompleteConsultaCid.getComponentValue();
        if (cid != null) {
            dto.setCid(cid);
            dto.setCodigoCid(cid.getCodigo());
            dto.setDescricaoCid(cid.getDescricao());
        }
        if (getAtendimento().getTabelaCbo() != null) {
            dto.setCodigoCbo(getAtendimento().getTabelaCbo().getCbo());
        }

        return dto;
    }

    public void salvarExameAgrupandoPrestadores(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        StringBuilder msg = new StringBuilder();
        for (ExameProcedimentoDTO exameProcedimentoDTO : itensList) {
            if (RepositoryComponentDefault.SIM_LONG.equals(exameProcedimentoDTO.getExameProcedimento().getFlagExameTuberculose())) {
                msg.append(exameProcedimentoDTO.getExameProcedimento().getDescricaoProcedimento());
                msg.append("\n");
            }
        }

        if (!"".equals(msg.toString())) {
            initDialogExamesTuberculoseAvancar(target, msg);
        } else {
            confirmarAvancar(target);
        }
    }

    private TabelaCbo getTabelaCbo(AjaxRequestTarget target) {
        TabelaCbo tabelaCbo = null;
        try {
            QueryProfissionalCargaHorariaGrupoAtendimentoCboDTOParam paramCbo = new QueryProfissionalCargaHorariaGrupoAtendimentoCboDTOParam();
            paramCbo.setEmpresa(getAtendimento().getEmpresa());
            paramCbo.setProfissional((Profissional) autoCompleteConsultaProfissional.getComponentValue());
            paramCbo.setGrupoAtendimentoCbo(carregarPermissaoNoInformarProfissional().getGrupoAtendimentoCbo());
            tabelaCbo = BOFactoryWicket.getBO(ProfissionalFacade.class).consultaProfissionalCargaHorariaGrupoAtendimentoCbo(paramCbo);

            if (tabelaCbo == null) {
                if (target != null) {
                    MessageUtil.warn(target, this, BundleManager.getString("msgProfissionalNaoPossuiCboCompativelExameSolicitado"));
                }
                return null;
            }
        } catch (ValidacaoException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }

        return tabelaCbo;
    }

    public void salvarImprimirExame(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (habilitaClassificacaoRiscoExame()) {
            if (cbxClassificaoRisco != null && cbxClassificaoRisco.getComponentValue() != null &&
            ClassificacaoRisco.Classificacao.SEM_CLASSIFICACAO.value().equals(cbxClassificaoRisco.getComponentValue().getNivelGravidade())) {
                throw new ValidacaoException(Bundle.getStringApplication("msgClassificacaoRiscoInvalida"));
            }
            if (txaJustificativaClassificacaoRisco.getComponentValue() == null || "".equals(txaJustificativaClassificacaoRisco.getComponentValue())) {
                throw new ValidacaoException(Bundle.getStringApplication("msgObrigatorioJustificativaClassificacaoRisco"));
            }
        }
        if (exigePreenchimentoPesoAltura()) {
            if (txtAltura.getComponentValue() == null || "".equals(txtAltura.getComponentValue())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_deve_ser_informado_campos_peso_altura"));
            }
            if (txtPeso.getComponentValue() == null || "".equals(txtPeso.getComponentValue())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_deve_ser_informado_campos_peso_altura"));
            }
        }

        validaDadosPaciente(getAtendimento().getUsuarioCadsus());
        IMTableSolicitacaoAgendamentoToExamePadrao imTable = this.buscarIMTableSolicitacaoAgendamentoToExamePadrao(exame);
        new AplicarValidacaoStatusSolicitacao(new ValidarSolicitacaoAgendamento()).validar(imTable.getSolicitacaoAgendamento());
        if (enabledImprimir(exame)) {
            initDialogImpressao(target);
        } else {
            initDlgConfirmacaoOk(target);
        }

        StringBuilder msg = new StringBuilder();
        for (ExameProcedimentoDTO exameProcedimentoDTO : itensList) {
            if (RepositoryComponentDefault.SIM_LONG.equals(exameProcedimentoDTO.getExameProcedimento().getFlagExameTuberculose())) {
                msg.append(exameProcedimentoDTO.getExameProcedimento().getDescricaoProcedimento());
                msg.append("\n");
            }
        }

        if (!"".equals(msg.toString())) {
            initDialogExamesTuberculose(target, msg);
        } else {
            salvar(target);
        }
    }

    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        getExame().setJustificativaClassificacaoRisco(form.getModel().getObject().getJustificativaClassificacaoRisco());
        ExameCadastroAprovacaoDTO dto = criarExameCadastroAprovacaoDTO();

        if (getExame().getCodigo() != null) {
            dto.setCodigoExameCadastrado(getExame().getCodigo());
        }
        dto.getExameProcedimentoDTOs().addAll(itensList);
        dto.setCid(getExame().getCid());
        dto.setDataDesejada(exame.getDataDesejada());

        codigosExame = acaoSalvar(target, dto);

        exames = LoadManager.getInstance(Exame.class)
                .addProperties(new HQLProperties(Exame.class).getProperties())
                .addProperties(new HQLProperties(TipoExame.class, Exame.PROP_TIPO_EXAME).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, codigosExame))
                .start().getList();

        if (enabledImprimir(exames)) {
            dlgConfirmacaoImpressao.show(target, exames);
        } else {
            dlgConfirmacaoOk.show(target);
        }
    }

    private boolean enabledImprimir(Exame exame) {
        return Exame.STATUS_AUTORIZADO.equals(getExame().getStatus())
                || RepositoryComponentDefault.SIM_LONG.equals(getExame().getTipoExame().getFlagImprimirSemAutorizacao());
    }

    private boolean enabledImprimir(List<Exame> exames) {
        for (Exame exame : exames) {
            if (!Exame.STATUS_AUTORIZADO.equals(exame.getStatus()) && RepositoryComponentDefault.NAO_LONG.equals(getExame().getTipoExame().getFlagImprimirSemAutorizacao())) {
                return false;
            }
        }

        return RepositoryComponentDefault.SIM_LONG.equals(getExame().getTipoExame().getFlagImprimirSemAutorizacao());
    }

    public List<Long> acaoSalvar(AjaxRequestTarget target, ExameCadastroAprovacaoDTO dto) throws ValidacaoException, DAOException {
        return BOFactoryWicket.getBO(ExameFacade.class).gerarSolicitacoesExame(dto, getExame(), true);
    }

    private void initDlgConfirmacaoOk(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            dlgConfirmacaoOk = new DlgConfirmacaoOk(getProntuarioController().newWindowId(), BundleManager.getString("exameCadastradoComSucesso", this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    DefaultProntuarioPanel panel = null;

                    if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                        panel = new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap);
                    } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                        panel = new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap);
                    } else {
                        panel = new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap);
                    }
                    getProntuarioController().changePanel(target, panel);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoOk);
        }
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<List<Exame>>(getProntuarioController().newWindowId(), bundle("desejaImprimirExame")) {
                @Override
                public DataReport getDataReport(List<Exame> object) throws ReportException {
                    if (object.isEmpty()) {
                        object.addAll(examesImpressos);
                        examesImpressos = new ArrayList<Exame>();
                    }

                    Exame exame = object.get(0);
                    object.remove(0);

                    examesImpressos.add(exame);

                    return ExameViewUtil.imprimirExame(exame);
                }

                @Override
                public void afterClose(AjaxRequestTarget target, List<Exame> object) throws ValidacaoException, DAOException {
                    if (object.isEmpty() && !examesImpressos.isEmpty()) {
                        object.addAll(examesImpressos);
                        examesImpressos = new ArrayList<>();
                    }

                    Boolean isLacen = RepositoryComponentDefault.Tipo.LACEN.value().equals(object.get(0).getTipoExame().getTipo());

                    if (isLacen) {
                        getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                    } else {
                        if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                        } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                        } else {
                            onFecharImpressao(target);
                        }
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void onFecharImpressao(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        List<IMTableSolicitacaoAgendamentoToExamePadrao> imTableList = this.buscarIMTableSolicitacoesAgendamentoToExamePadrao();

        if (imTableList.size() == 1) {
            IMTableSolicitacaoAgendamentoToExamePadrao  imTable = imTableList.get(0);
            SolicitacaoAgendamento solicitacaoAgendamento = imTable.getSolicitacaoAgendamento();
            boolean realizarAgendamento = this.realizarAgendamento(imTable);

            if(RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(exame.getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG))
                    && realizarAgendamento && RepositoryComponentDefault.NAO_LONG.equals(form.getModel().getObject().getNaoColocarListaEsperaSus())
                    && AtendimentoHelper.isPermiteAgendarNoAtendimento(form.getModel().getObject().getTipoExame().getTipoProcedimento()) ) {
                AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
                param.setApenasAgendasComVagas(true);
                param.setEmpresaOrigem(getAtendimento().getEmpresa());
                param.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
                param.setTipoProcedimento(exame.getTipoExame().getTipoProcedimento());
                param.setTipoAgendaList(Arrays.asList(TipoProcedimento.TipoAgenda.DIARIO, TipoProcedimento.TipoAgenda.HORARIO));
                param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_CONSULTA, TipoAtendimentoAgenda.TIPO_INTERNA));
                param.setProfissionalVagaInterna(getAtendimento().getProfissional());

                List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade = consultarVagasDisponiveisProximidade(param);

                if(solicitacaoAgendamento != null && AtendimentoHelper.obrigatorioAgendamentoExamePadrao(exame, form.getModel().getObject().getTipoExame().getTipoProcedimento())){

                    param.setSomenteVagaInterna(this.validaFilaEsperaAtendimento(solicitacaoAgendamento));

                    Empresa empresaResponsavelSolicitacaoAgendamento = AgendamentoHelper.empresaResponsavelSolicitacaoAgendamento(getAtendimento().getUsuarioCadsus().getCodigo());
                    if(!edicao && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()) && empresaResponsavelSolicitacaoAgendamento == null){
                        EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO = new EmpresaResponsavelSolicitacaoAgendamentoDTO();
                        empresaResponsavelSolicitacaoAgendamentoDTO.setParam(param);
                        empresaResponsavelSolicitacaoAgendamentoDTO.setAgendasDisponiveis(agendasDisponiveisProximidade);
                        empresaResponsavelSolicitacaoAgendamentoDTO.setSolicitacaoAgendamento(solicitacaoAgendamento);

                        initDlgUnidadeResponsavelSolicitacaoAgendamento(target, empresaResponsavelSolicitacaoAgendamentoDTO);
                        return;
                    } else {
                        if(empresaResponsavelSolicitacaoAgendamento != null) {
                            solicitacaoAgendamento.setEmpresa(empresaResponsavelSolicitacaoAgendamento);
                            BOFactoryWicket.save(solicitacaoAgendamento);
                        }

                        onAgendar(target, agendasDisponiveisProximidade, param);
                        return;
                    }
                }
            } else if (solicitacaoAgendamento != null && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())){
                Empresa empresaResponsavelSolicitacaoAgendamento = AgendamentoHelper.empresaResponsavelSolicitacaoAgendamento(getAtendimento().getUsuarioCadsus().getCodigo());
                if(!edicao && RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()) && empresaResponsavelSolicitacaoAgendamento == null){
                    EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO = new EmpresaResponsavelSolicitacaoAgendamentoDTO();
                    empresaResponsavelSolicitacaoAgendamentoDTO.setSolicitacaoAgendamento(solicitacaoAgendamento);

                    initDlgUnidadeResponsavelSolicitacaoAgendamento(target, empresaResponsavelSolicitacaoAgendamentoDTO);
                    return;
                } else {
                    if(empresaResponsavelSolicitacaoAgendamento != null) {
                        solicitacaoAgendamento.setEmpresa(empresaResponsavelSolicitacaoAgendamento);
                        BOFactoryWicket.save(solicitacaoAgendamento);
                    }
                }
            }
        }

        DefaultProntuarioPanel panel = new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap);
        getProntuarioController().changePanel(target, panel);
    }

    private void initDlgUnidadeResponsavelSolicitacaoAgendamento(AjaxRequestTarget target, EmpresaResponsavelSolicitacaoAgendamentoDTO empresaResponsavelSolicitacaoAgendamentoDTO) {
        if (dlgUnidadeResponsavelSolicitacaoAgendamento == null) {
            dlgUnidadeResponsavelSolicitacaoAgendamento = new DlgUnidadeResponsavelSolicitacaoAgendamento(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, EmpresaResponsavelSolicitacaoAgendamentoDTO dto) throws ValidacaoException, DAOException {
                    dto.getSolicitacaoAgendamento().setEmpresa(dto.getEmpresa());
                    BOFactoryWicket.save(dto.getSolicitacaoAgendamento());

                    if (CollectionUtils.isNotNullEmpty(dto.getAgendasDisponiveis())) {
                        onAgendar(target, dto.getAgendasDisponiveis(), dto.getParam());
                    } else {
                        DefaultProntuarioPanel panel = new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap);
                        getProntuarioController().changePanel(target, panel);
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgUnidadeResponsavelSolicitacaoAgendamento);
        }
        dlgUnidadeResponsavelSolicitacaoAgendamento.show(target, empresaResponsavelSolicitacaoAgendamentoDTO);
    }

    private boolean existeAgendamentoExamePadrao(SolicitacaoAgendamento solicitacaoAgendamento){
        return LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(AgendaGradeAtendimentoHorario.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN, java.util.Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO_PRINCIPAL, BuilderQueryCustom.QueryParameter.IS_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO))
                .exists();
    }

    private List<AgendaGradeAtendimentoDTO> consultarVagasDisponiveisProximidade(AgendaGradeAtendimentoDTOParam param) throws DAOException, ValidacaoException {
        List<AgendaGradeAtendimentoDTO> agendasDisponiveis = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
        List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade = BOFactoryWicket.getBO(AgendamentoFacade.class).validarAgendasProximidadeSolicitanteExecutante(getAtendimento().getEmpresa(), agendasDisponiveis);

        return agendasDisponiveisProximidade;
    }

    private void onAgendar(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> agendasDisponiveisProximidade, AgendaGradeAtendimentoDTOParam param) throws ValidacaoException, DAOException {
        List<SolicitacaoAgendamento> solicitacaoAgendamentoList = LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_ATENDIMENTO_ORIGEM, getAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, form.getModel().getObject().getTipoExame().getTipoProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, SolicitacaoAgendamento.STATUS_CANCELADO))
                .addSorter(new QueryCustom.QueryCustomSorter(SolicitacaoAgendamento.PROP_DATA_CADASTRO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        SolicitacaoAgendamento solicitacaoAgendamento = solicitacaoAgendamentoList.get(0);

        param.setSolicitacaoAgendamento(solicitacaoAgendamento);

        getProntuarioController().changePanel(target, new MarcacaoAgendamentoExamePadraoPanel(getProntuarioController().panelId(), agendasDisponiveisProximidade, param, solicitacaoAgendamento) {
            @Override
            public void retornarPanelConsulta(AjaxRequestTarget target) {
                DefaultProntuarioPanel panel = new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap);
                getProntuarioController().changePanel(target, panel);
            }
        });
    }

    private boolean tipoExameAutorizacao() {
        boolean autorizaPorTipoExameUnidade = LoadManager.getInstance(TipoExameUnidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoExameUnidade.PROP_EMPRESA), getAtendimento().getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoExameUnidade.PROP_TIPO_EXAME), this.tipoExame))
                .exists();
        if (autorizaPorTipoExameUnidade) {
            return RepositoryComponentDefault.SIM_LONG.equals(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getFlagAutorizaExame());
        }
        return autorizaPorTipoExameUnidade;
    }

    public NodoAtendimentoWeb carregarPermissaoNoInformarProfissional() {
        if (nodoAtendimentoWeb == null) {
            NodoAtendimentoWeb proxy = on(NodoAtendimentoWeb.class);

            nodoAtendimentoWeb = LoadManager.getInstance(NodoAtendimentoWeb.class)
                    .addProperties(new HQLProperties(NodoAtendimentoWeb.class).getProperties())
                    .addProperties(new HQLProperties(GrupoAtendimentoCbo.class, path(proxy.getGrupoAtendimentoCbo())).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoAtendimento()), getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getClasseNodo()), SolicitacaoExamesNode.class.getName()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getInformarProfissional()), RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();
        }

        return nodoAtendimentoWeb;
    }

    public String getTipoControleRegulacao() {
        if(tipoControleRegulacao == null){
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

    private void regrasControleRegulacaoSolicitacaoProfissional(AjaxRequestTarget target, boolean edicaoCadastro, TipoProcedimento tipoProcedimento){
        if(target != null){
            dropDownEnviarRegulacao.limpar(target);
            txaDescricaoEnviarRegulacao.limpar(target);
        }

        if (form.getModel().getObject() != null && RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getNaoColocarListaEsperaSus())) {
            if (containerSolicitacaoProfissional.isVisible()) {
                containerSolicitacaoProfissional.setVisible(false);

                if (target != null) {
                    ComponentUtils.limparContainer(containerSolicitacaoProfissional, target);
                }
            }

            return;
        } else if (!containerSolicitacaoProfissional.isVisible()) {
            containerSolicitacaoProfissional.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()));
            if (target != null) {
                target.add(containerSolicitacaoProfissional);
            }
        }

        if(tipoProcedimento != null && tipoProcedimento.getCodigo() != null && tipoProcedimento.getRegulado() == null){
            tipoProcedimento = LoadManager.getInstance(TipoProcedimento.class)
                    .addProperty(TipoProcedimento.PROP_CODIGO)
                    .addProperty(TipoProcedimento.PROP_REGULADO)
                    .setId(tipoProcedimento.getCodigo())
                    .start().getVO();
        }

        if(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())){
            if(edicaoCadastro){
                if(RepositoryComponentDefault.SIM_LONG.equals(form.getModel().getObject().getFlagEnviarRegulacao())
                        || tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())) {
                    dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    if(tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())){
                        dropDownEnviarRegulacao.setEnabled(false);
                    }

                    if (target != null) {
                        showFieldset(target, containerSolicitacaoProfissional);
                    }
                }
            } else if(tipoProcedimento != null && RepositoryComponentDefault.SIM.equals(tipoProcedimento.getRegulado())){
                dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                dropDownEnviarRegulacao.setEnabled(false);

                if(target != null) {
                    showFieldset(target, containerSolicitacaoProfissional);
                }
            } else {
                dropDownEnviarRegulacao.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                dropDownEnviarRegulacao.setEnabled(true);

                if(target != null) {
                    hideFieldset(target, containerSolicitacaoProfissional);
                }
            }
        }

        if(target != null){
            target.add(dropDownEnviarRegulacao);
            target.add(txaDescricaoEnviarRegulacao);
        }
    }

    private boolean validaFilaEsperaAtendimento(SolicitacaoAgendamento solicitacaoAgendamento) throws DAOException {
        return new ValidarFilaEsperaNoAtendimento().setSolicitacaoAgendamento(solicitacaoAgendamento)
                .setAtendimento(getAtendimento())
                .setFlagRetorno(null)
                .validar();
    }

    private List<IMTableSolicitacaoAgendamentoToExamePadrao> buscarIMTableSolicitacoesAgendamentoToExamePadrao() throws ValidacaoException, DAOException {
        boolean deveExecutarConsulta = !exames.isEmpty() && !codigosExame.isEmpty();
        return deveExecutarConsulta ? BOFactoryWicket.getBO(ExameFacade.class).consultarIMTableSolicitacoesAgendamentoPorCodigosExame(codigosExame)
                : new ArrayList<>();
    }

    private IMTableSolicitacaoAgendamentoToExamePadrao buscarIMTableSolicitacaoAgendamentoToExamePadrao(Exame exame) throws ValidacaoException, DAOException {
        boolean deveExecutarConsulta = exame != null && exame.getCodigo() != null;
        return deveExecutarConsulta ? BOFactoryWicket.getBO(ExameFacade.class).consultarIMTableSolicitacaoAgendamentoPorCodigoExame(exame.getCodigo())
                : new IMTableSolicitacaoAgendamentoToExamePadrao();
    }

    private boolean realizarAgendamento(IMTableSolicitacaoAgendamentoToExamePadrao imTable) {
        boolean realizarAgendamento = true;
        if (imTable != null && imTable.getCodigo() != null) {
            SolicitacaoAgendamento solicitacaoAgendamento = imTable.getSolicitacaoAgendamento();
            realizarAgendamento = !existeAgendamentoExamePadrao(solicitacaoAgendamento);
        }
        return realizarAgendamento;
    }

    public List<Exame> getExames() {
        if (CollectionUtils.isEmpty(exames)) {
            for (ExameProcedimentoDTO exameProcedimentoDTO : itensList) {
                exames.add(exameProcedimentoDTO.getExameRequisicao().getExame());
            }
        }

        return exames;
    }

    private MsDropDown<ClassificacaoRisco> getCbxClassificacaoRisco(String id) {
        if (cbxClassificaoRisco == null) {
            cbxClassificaoRisco = new MsDropDown<ClassificacaoRisco>(id);
            cbxClassificaoRisco.setOutputMarkupId(true);

            List<ClassificacaoRisco> lstClassificacao = LoadManager.getInstance(ClassificacaoRisco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoRisco.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(ClassificacaoRisco.PROP_FLAG_ATIVAR_CR, BuilderQueryCustom.QueryParameter.DIFERENTE, RepositoryComponentDefault.NAO_LONG))
                    .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();

            for (ClassificacaoRisco classificacaoRisco : lstClassificacao) {
                cbxClassificaoRisco.addChoice(new MsItem<ClassificacaoRisco>(classificacaoRisco, classificacaoRisco.getDescricao(), urlFor(new SharedResourceReference(Resources.class, classificacaoRisco.getCaminhoImagem()), null).toString()));
            }
        }
        return cbxClassificaoRisco;
    }

    private void limparExameProcedimentoPrincipal(ExameProcedimento exameProcedimentoPrincipal) {
        if (exameProcedimentoPrincipal != null) {
            this.exameProcedimentoPrincipal = null;
        }
    }

    public void validarDataDesejada(java.util.Date dataDesejada) throws ValidacaoException {
        if (dataDesejada == null) {
            dataDesejada = ((BaseExame) this.form.getModelObject()).getDataDesejada();
        }

        if (dataDesejada == null) {
            return;
        }

        Date dataAtual = DataUtil.getDataAtual();
        if (dataDesejada.before(dataAtual)) {
            throw new ValidacaoException(bundle("msg_data_menor_data_atual"));
        }

        if (tipoExame == null || tipoExame.getCodigo() == null) {
            return;
        }

        tipoExame = LoadManager.getInstance(TipoExame.class)
            .addProperties(new HQLProperties(TipoExame.class).getProperties())
            .setId(tipoExame.getCodigo())
            .start().getVO();
        Long diasPermitidos = tipoExame.getDiasValidadeAutorizacao();
        if (diasPermitidos != null && diasPermitidos > 0) {
            LocalDate dataLimite = new LocalDate(dataAtual).plusDays(diasPermitidos.intValue());
            LocalDate dataDes = new LocalDate(dataDesejada);
            if (dataDes.isAfter(dataLimite)) {
                throw new ValidacaoException(bundle("dataDesejadaExame", diasPermitidos));
            }
        }
    }

}
