package br.com.celk.component.lote.entrada;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgLotesEntrada extends Window{

    private PnlLotesEntrada pnlLotesEntrada;
    private boolean isNotaFiscal = false;
    
    public DlgLotesEntrada(String id, IModel<?> model) {
        super(id, model);
        init();
    }

    public DlgLotesEntrada(String id) {
        super(id);
        init();
    }
    public DlgLotesEntrada(String id, boolean isNotaFiscal) {
        super(id);
        this.isNotaFiscal = isNotaFiscal;
        init();
    }
    
    private void init(){
        setInitialWidth(600);
        setInitialHeight(450);
        
        setContent(pnlLotesEntrada = new PnlLotesEntrada(getContentId(), isNotaFiscal) {

            @Override
            public void onCancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgLotesEntrada.this.cancelar(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgLotesEntrada.this.onConfirmar(target);
                DlgLotesEntrada.this.onFechar(target);
                close(target);
            }
        });
        
        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    pnlLotesEntrada.cancelar(target);
                    DlgLotesEntrada.this.onFechar(target);
                } catch (DAOException ex) {
                    MessageUtil.modalError(target, DlgLotesEntrada.this, ex);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, DlgLotesEntrada.this, ex);
                }
                return true;
            }
        });
        
        setTitle(BundleManager.getString("lotes"));
    }

    private void cancelar(AjaxRequestTarget target) throws DAOException, ValidacaoException{
        close(target);
    }
    
    public void show(AjaxRequestTarget target, Produto produto, String fabricante) {
        super.show(target);
        pnlLotesEntrada.setFabricante(fabricante);
        pnlLotesEntrada.setProduto(produto);
    }

    public void show(AjaxRequestTarget target, Produto produto, String fabricante, LocalizacaoEstrutura localizacaoEstrutura) {
        super.show(target);
        pnlLotesEntrada.setFabricante(fabricante);
        pnlLotesEntrada.setProduto(produto);
        pnlLotesEntrada.setLocalizacaoEstrutura(localizacaoEstrutura, target);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlLotesEntrada.getTxtLote();
    }
    
    public List<MovimentoGrupoEstoqueItemDTO> getLotes() {
        return pnlLotesEntrada.getLotes();
    }
    
    public void setLotes(List<MovimentoGrupoEstoqueItemDTO> lotes) {
        pnlLotesEntrada.setLotes(lotes);
    }
    
    public Double getQuantidadeTotal(){
        return pnlLotesEntrada.getQuantidadeTotal();
    }
    
    public void limpar(AjaxRequestTarget target){
        pnlLotesEntrada.limpar(target);
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;

}
