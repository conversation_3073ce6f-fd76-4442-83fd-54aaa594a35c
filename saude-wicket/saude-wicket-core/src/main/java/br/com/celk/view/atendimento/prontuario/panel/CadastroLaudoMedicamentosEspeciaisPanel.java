package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.LaudoMedicamentosEspeciaisDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.receituario.NoReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciais;
import br.com.ksisolucoes.vo.prontuario.basico.LaudoMedicamentosEspeciaisItem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroLaudoMedicamentosEspeciaisPanel extends ProntuarioCadastroPanel {

    private Form<LaudoMedicamentosEspeciaisDTO> form;
    private boolean enable;
    private Table tblItens;
    private DropDown<Long> cbxPacRealizouTratamento;
    private RequiredInputField<String> txtObsevacaoPacienteRealizouPrev;
    private DropDown<Long> cbxPacCapaz;
    private RequiredInputField<String> txtPacCapaz;
    private LaudoMedicamentosEspeciais laudoMedicamentosEspeciais;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private LongField qtd1;
    private LongField qtd2;
    private LongField qtd3;
    private LongField qtd4;
    private LongField qtd5;
    private LongField qtd6;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidade;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private NoReceituarioDTO noReceituarioDTO;
    private ReceituarioItemDTO itemDTO;
    private boolean origemPainelSoap;
    private boolean clonarAction;
    private Button btnAdicionar;
    private DoubleField txtPeso;
    private DoubleField txtAltura;

    public CadastroLaudoMedicamentosEspeciaisPanel(String id, String titulo) {
        super(id, titulo);
    }

    public CadastroLaudoMedicamentosEspeciaisPanel(String id, String titulo, LaudoMedicamentosEspeciais laudoMedicamentosEspeciais) {
        super(id, titulo);
        this.laudoMedicamentosEspeciais = laudoMedicamentosEspeciais;
    }

    public CadastroLaudoMedicamentosEspeciaisPanel(String id, String titulo, LaudoMedicamentosEspeciais laudoMedicamentosEspeciais, boolean clonarAction) {
        super(id, titulo);
        this.laudoMedicamentosEspeciais = laudoMedicamentosEspeciais;
        this.clonarAction = clonarAction;
    }


    public CadastroLaudoMedicamentosEspeciaisPanel(String id, NoReceituarioDTO noReceituarioDTO, ReceituarioItemDTO itemDTO, boolean origemPainelSoap, LaudoMedicamentosEspeciais laudoMedicamentosEspeciais) {
        super(id, bundle("medicamentosInsumosNaoPadronizados"));
        this.noReceituarioDTO = noReceituarioDTO;
        this.itemDTO = itemDTO;
        this.laudoMedicamentosEspeciais = laudoMedicamentosEspeciais;
        this.origemPainelSoap = origemPainelSoap;
    }

    private void carregarProduto(Produto produto) {
        autoCompleteConsultaProduto.setComponentValue(produto);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        enable = true;
        this.form = new Form<LaudoMedicamentosEspeciaisDTO>("form", new CompoundPropertyModel<LaudoMedicamentosEspeciaisDTO>(new LaudoMedicamentosEspeciaisDTO()));
        LaudoMedicamentosEspeciaisDTO proxy = on(LaudoMedicamentosEspeciaisDTO.class);

        if(clonarAction) {
            cloneLaudoMedicamentoEspecial(laudoMedicamentosEspeciais);
        } else {
            carregarDados(laudoMedicamentosEspeciais);
        }

        form.add(autoCompleteConsultaUnidade = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getLaudoMedicamentosEspeciais().getEmpresa())).setRequired(true).setLabel(Model.of(bundle("unidade"))));
        autoCompleteConsultaUnidade.getTxtDescricao().addRequiredClass();
        form.add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getLaudoMedicamentosEspeciais().getProfissional())).setRequired(true).setLabel(Model.of(bundle("profissional"))));
        autoCompleteConsultaProfissional.getTxtDescricao().addRequiredClass();

        autoCompleteConsultaUnidade.setEnabled(false);
        autoCompleteConsultaProfissional.setEnabled(false);
        
        form.add(autoCompleteConsultaCid = (AutoCompleteConsultaCid) new AutoCompleteConsultaCid(path(proxy.getLaudoMedicamentosEspeciais().getCid())).setRequired(true));
        autoCompleteConsultaCid.setLabel(Model.of(bundle("diagnosticoInicial")));
        form.add(new InputArea(path(proxy.getLaudoMedicamentosEspeciais().getAnamnese())));
        form.add(cbxPacRealizouTratamento = DropDownUtil.getSimNaoLongDropDown(path(proxy.getLaudoMedicamentosEspeciais().getPacienteRealizouTratamentoPreventivo())));
        cbxPacRealizouTratamento.addAjaxUpdateValue();
        cbxPacRealizouTratamento.setOutputMarkupId(true);
        cbxPacRealizouTratamento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtObsevacaoPacienteRealizouPrev.limpar(target);
                txtObsevacaoPacienteRealizouPrev.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(cbxPacRealizouTratamento.getComponentValue()));
                target.add(txtObsevacaoPacienteRealizouPrev);
            }
        });
        form.add(txtPeso = new DoubleField(path(proxy.getLaudoMedicamentosEspeciais().getPeso())));
        txtPeso.setVMax(500D);
        txtPeso.setMDec(3);
        txtPeso.setRequired(true);
        txtPeso.addRequiredClass();
        txtPeso.setLabel(Model.of(bundle("peso")));

        form.add(txtAltura = new DoubleField(path(proxy.getLaudoMedicamentosEspeciais().getAltura())));
        txtAltura.setVMax(250D);
        txtAltura.setMDec(1);
        txtAltura.setRequired(true);
        txtAltura.addRequiredClass();
        txtAltura.setLabel(Model.of(bundle("altura")));

        form.add(txtObsevacaoPacienteRealizouPrev = new RequiredInputField<>(path(proxy.getLaudoMedicamentosEspeciais().getObsPacienteRealizouTratamentoPreventivo())));
        txtObsevacaoPacienteRealizouPrev.setEnabled(!RepositoryComponentDefault.NAO_LONG.equals(cbxPacRealizouTratamento.getComponentValue()));
        txtObsevacaoPacienteRealizouPrev.setLabel(Model.of(bundle("observacao")));

        form.add(cbxPacCapaz = DropDownUtil.getNaoSimLongDropDown(path(proxy.getLaudoMedicamentosEspeciais().getPacienteConsideradoIncapaz())));
        cbxPacCapaz.addAjaxUpdateValue();
        cbxPacCapaz.setOutputMarkupId(true);
        cbxPacCapaz.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                txtPacCapaz.limpar(target);
                txtPacCapaz.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(cbxPacCapaz.getComponentValue()));
                target.add(txtPacCapaz);
            }
        });

        form.add(txtPacCapaz = new RequiredInputField<>(path(proxy.getLaudoMedicamentosEspeciais().getNomeResponsavel())));
        txtPacCapaz.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(cbxPacCapaz.getComponentValue()));

        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProduto())).setMedicamento(RepositoryComponentDefault.SIM).setLme(true));
        form.add(qtd1 = new LongField(path(proxy.getQuantidade1())));
        qtd1.setOutputMarkupId(true);
        qtd1.addAjaxUpdateValue();
        form.add(qtd2 = new LongField(path(proxy.getQuantidade2())));
        qtd2.setOutputMarkupId(true);
        qtd2.addAjaxUpdateValue();
        form.add(qtd3 = new LongField(path(proxy.getQuantidade3())));
        qtd3.setOutputMarkupId(true);
        qtd3.addAjaxUpdateValue();
        form.add(qtd4 = new LongField(path(proxy.getQuantidade4())));
        qtd4.setOutputMarkupId(true);
        qtd4.addAjaxUpdateValue();
        form.add(qtd5 = new LongField(path(proxy.getQuantidade5())));
        qtd5.setOutputMarkupId(true);
        qtd5.addAjaxUpdateValue();
        form.add(qtd6 = new LongField(path(proxy.getQuantidade6())));
        qtd6.setOutputMarkupId(true);
        qtd6.addAjaxUpdateValue();

        form.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                LaudoMedicamentosEspeciaisDTO modelObject = CadastroLaudoMedicamentosEspeciaisPanel.this.form.getModel().getObject();
                if (CollectionUtils.isNotNullEmpty(modelObject.getLaudoMedicamentosEspeciaisItemList()) && modelObject.getLaudoMedicamentosEspeciaisItemList().size() == 5) {
                    throw new ValidacaoException("A tabela de medicamentos não pode ter mais do que 5 itens.");
                }

                validaAdicionar(modelObject);

                LaudoMedicamentosEspeciaisItem laudoMedicamentosEspeciaisItem = new LaudoMedicamentosEspeciaisItem();
                laudoMedicamentosEspeciaisItem.setProduto(modelObject.getProduto());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao1Mes(modelObject.getQuantidade1());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao2Mes(modelObject.getQuantidade2());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao3Mes(modelObject.getQuantidade3());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao4Mes(modelObject.getQuantidade4());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao5Mes(modelObject.getQuantidade5());
                laudoMedicamentosEspeciaisItem.setQuantidadeSolicitacao6Mes(modelObject.getQuantidade6());
                CrudUtils.adicionarItem(target, tblItens, modelObject.getLaudoMedicamentosEspeciaisItemList(), laudoMedicamentosEspeciaisItem);

                autoCompleteConsultaProduto.limpar(target);
                qtd1.limpar(target);
                qtd2.limpar(target);
                qtd3.limpar(target);
                qtd4.limpar(target);
                qtd5.limpar(target);
                qtd6.limpar(target);

            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(this);
                if (findNotificationPanel != null) {
                    getSession().getFeedbackMessages().clear();
                    findNotificationPanel.updateNotificationPanel(target);
                }

                LaudoMedicamentosEspeciaisDTO laudoMedicamentosEspeciaisDTO = CadastroLaudoMedicamentosEspeciaisPanel.this.form.getModel().getObject();
                laudoMedicamentosEspeciaisDTO.getLaudoMedicamentosEspeciais().setAtendimento(getAtendimento());

                salvar(target, laudoMedicamentosEspeciaisDTO);
                voltar(target);
            }
        });

        form.add(tblItens = new Table("tblItens", getColumns(), getCollectionProvider()));
        tblItens.populate();
        if (CollectionUtils.isNotNullEmpty(noReceituarioDTO.getReceituarioItemDTOList())) {
            carregarProduto(noReceituarioDTO.getReceituarioItemDTOList().get(noReceituarioDTO.getReceituarioItemDTOList().size() - 1).getReceituarioItem().getProduto());
        }

        add(this.form);
    }

    public LaudoMedicamentosEspeciais salvar(AjaxRequestTarget target, LaudoMedicamentosEspeciaisDTO laudoMedicamentosEspeciaisDTO) throws ValidacaoException, DAOException {
        return BOFactoryWicket.getBO(AtendimentoFacade.class).salvarLaudoMedicamentosEspeciais(laudoMedicamentosEspeciaisDTO);
    }

    public void voltar(AjaxRequestTarget target) {
        LaudoMedicamentosEspeciaisPanel panel = new LaudoMedicamentosEspeciaisPanel(getProntuarioController().panelId(), bundle("laudoMedicamentosEspeciais"));
        getProntuarioController().changePanel(target, panel);
    }

    private void validaAdicionar(LaudoMedicamentosEspeciaisDTO modelObject) throws ValidacaoException {
        if (modelObject.getProduto() == null) {
            throw new ValidacaoException("Por favor, informe o medicamento.");
        }
        for (LaudoMedicamentosEspeciaisItem laudoMedicamentosEspeciaisItem : modelObject.getLaudoMedicamentosEspeciaisItemList()) {
            if (laudoMedicamentosEspeciaisItem.getProduto().getCodigo().equals(modelObject.getProduto().getCodigo())) {
                throw new ValidacaoException("Este medicamento já foi adicionado");
            }
        }
        if (modelObject.getQuantidade1() == null || modelObject.getQuantidade1() == 0L) {
            throw new ValidacaoException("o campo 1º mês deve ser maior que 0");
        }
        if (!temMedicamento(modelObject.getProduto())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_medicamento_nao_esta_no_receituario", modelObject.getProduto()));
        }
    }

    public NoReceituarioDTO getNoReceituarioDTO() {
        return noReceituarioDTO;
    }

    public ReceituarioItemDTO getItemDTO() {
        return itemDTO;
    }

    private boolean temMedicamento(Produto produto) {
        for (int i = 0; i < noReceituarioDTO.getReceituarioItemDTOList().size(); i ++) {
            if (noReceituarioDTO.getReceituarioItemDTOList().get(i).getReceituarioItem().getProduto().equals(produto)) {
                return true;
            }
        }
        return false;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        LaudoMedicamentosEspeciaisItem proxy = on(LaudoMedicamentosEspeciaisItem.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("medicamento"), proxy.getProduto().getDescricao()));
        columns.add(createColumn(bundle("1mes2"), proxy.getQuantidadeSolicitacao1Mes()));
        columns.add(createColumn(bundle("2mes2"), proxy.getQuantidadeSolicitacao2Mes()));
        columns.add(createColumn(bundle("3mes2"), proxy.getQuantidadeSolicitacao3Mes()));
        columns.add(createColumn(bundle("4mes2"), proxy.getQuantidadeSolicitacao4Mes()));
        columns.add(createColumn(bundle("5mes2"), proxy.getQuantidadeSolicitacao5Mes()));
        columns.add(createColumn(bundle("6mes2"), proxy.getQuantidadeSolicitacao6Mes()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<LaudoMedicamentosEspeciaisItem>() {
            @Override
            public void customizeColumn(LaudoMedicamentosEspeciaisItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<LaudoMedicamentosEspeciaisItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, LaudoMedicamentosEspeciaisItem modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblItens, form.getModel().getObject().getLaudoMedicamentosEspeciaisItemList(), modelObject);
                        removerProdutoReceituario(target);
                    }
                });
            }
        };
    }

    private void removerProdutoReceituario(AjaxRequestTarget target) {
        if (itemDTO != null && itemDTO.getReceituarioItem() != null) {
            btnAdicionar.setEnabled(true);
            target.add(btnAdicionar);
            autoCompleteConsultaProduto.limpar(target);
//            carregarProduto(itemDTO.getReceituarioItem().getProduto());
            target.add(autoCompleteConsultaProduto);
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (form.getModel().getObject().getLaudoMedicamentosEspeciaisItemList() == null) {
                    form.getModel().getObject().setLaudoMedicamentosEspeciaisItemList(new ArrayList<LaudoMedicamentosEspeciaisItem>());
                }
                return form.getModel().getObject().getLaudoMedicamentosEspeciaisItemList();
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    private void carregarDados(LaudoMedicamentosEspeciais laudoMedicamentosEspeciais) {
        if (laudoMedicamentosEspeciais != null && laudoMedicamentosEspeciais.getCodigo() != null) {
            form.getModel().getObject().setLaudoMedicamentosEspeciais(laudoMedicamentosEspeciais);
            form.getModel().getObject().setLaudoMedicamentosEspeciaisItemList(LoadManager.getInstance(LaudoMedicamentosEspeciaisItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(LaudoMedicamentosEspeciaisItem.PROP_LAUDO_MEDICAMENTOS_ESPECIAIS, laudoMedicamentosEspeciais))
                    .start().<LaudoMedicamentosEspeciaisItem>getList());
        } else {
            LaudoMedicamentosEspeciais lme = new LaudoMedicamentosEspeciais();
            if(noReceituarioDTO !=null && noReceituarioDTO.getReceituario() != null) {
                if (noReceituarioDTO.getReceituario().getEmpresa() != null) {
                    lme.setEmpresa(noReceituarioDTO.getReceituario().getEmpresa());
                }
                if (noReceituarioDTO.getReceituario().getProfissional() != null) {
                    lme.setProfissional(noReceituarioDTO.getReceituario().getProfissional());
                }
            }
            form.getModel().getObject().setLaudoMedicamentosEspeciais(lme);

        }
    }

    private void cloneLaudoMedicamentoEspecial(LaudoMedicamentosEspeciais laudoMedicamentosEspeciais) {
        LaudoMedicamentosEspeciais newLaudo = VOUtils.cloneObject(laudoMedicamentosEspeciais);
        newLaudo.setCodigo(null);

        List<LaudoMedicamentosEspeciaisItem> newLaudoItemList = null;
        List<LaudoMedicamentosEspeciaisItem> itemList = LoadManager.getInstance(LaudoMedicamentosEspeciaisItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(LaudoMedicamentosEspeciaisItem.PROP_LAUDO_MEDICAMENTOS_ESPECIAIS, laudoMedicamentosEspeciais))
                .start().<LaudoMedicamentosEspeciaisItem>getList();

        if(CollectionUtils.isNotNullEmpty(itemList)) {
            newLaudoItemList = new ArrayList<>();
            for (LaudoMedicamentosEspeciaisItem laudoMedicamentosEspeciaisItem : itemList) {
                LaudoMedicamentosEspeciaisItem newItem = VOUtils.cloneObject(laudoMedicamentosEspeciaisItem);
                newItem.setCodigo(null);
                newLaudoItemList.add(newItem);
            }
        }


        LaudoMedicamentosEspeciaisDTO dto = new LaudoMedicamentosEspeciaisDTO();
        dto.setLaudoMedicamentosEspeciais(newLaudo);
        dto.setLaudoMedicamentosEspeciaisItemList(newLaudoItemList);

        form.getModel().setObject(dto);
    }


}
