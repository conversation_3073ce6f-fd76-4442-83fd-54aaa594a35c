package br.com.celk.component.report.behavior;

import br.com.celk.component.report.RelatorioNaoEncontradoPage;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.util.async.AsyncProcessUtil;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AbstractAjaxBehavior;
import org.apache.wicket.request.handler.resource.ResourceReferenceRequestHandler;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.SharedResourceReference;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
public class ViewReportBehavior extends AbstractAjaxBehavior {

    @Override
    public void onRequest() {
        try {
            String retornoLocal = ApplicationSession.get().getAsyncCache().get(getAsyncProcess());
            if (retornoLocal == null) {
                //logNotCache();
                retornoLocal = AsyncProcessUtil.processarRetornoLocal(getAsyncProcess());
                if (retornoLocal != null) {
                    ApplicationSession.get().getAsyncCache().put(getAsyncProcess(), retornoLocal);
                    //logAddCache();

                    BOFactoryWicket.getBO(BasicoFacade.class).removerAsyncProcess(Arrays.asList(getAsyncProcess()));
                } else {
                    getComponent().setResponsePage(RelatorioNaoEncontradoPage.class);
                }
            }

            if (retornoLocal != null) {
                //Um Shared resource eh application scoped
                ResourceReference resourceReference = new SharedResourceReference("staticReport");

                ResourceReferenceRequestHandler referenceRequestHandler = new ResourceReferenceRequestHandler(resourceReference);

                String patharquivo = gerarArquivo(retornoLocal).getPath();
                String titulo = getAsyncProcess() != null ? StringUtil.removeAcentos(getAsyncProcess().getNomeProcesso()).replace(" ", "_")
                        + getTipoRelatorio().descricao() : "relatorio_gem_saude" + getTipoRelatorio().descricao();
                titulo = titulo.replace("\n", "");

                referenceRequestHandler.getPageParameters().add("tipoRelatorio", getTipoRelatorio().value());
                referenceRequestHandler.getPageParameters().add("retornoLocal", patharquivo);
                referenceRequestHandler.getPageParameters().add("retornolocalTitle", titulo);

                //logVariables(patharquivo, titulo);

                getComponent().getRequestCycle().scheduleRequestHandlerAfterCurrent(referenceRequestHandler);
            }
        } catch (ValidacaoException | DAOException | ClassNotFoundException | IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public TipoRelatorio getTipoRelatorio() {
        return new EnumUtil().resolveValue(TipoRelatorio.values(), getAsyncProcess().getTipoRelatorio());
    }

    public void ajaxDownload(AjaxRequestTarget target) {
        String url = getCallbackUrl().toString();

        url = url + (url.contains("?") ? "&" : "?");
        url = url + "antiCache=" + System.currentTimeMillis();

        target.appendJavaScript("setTimeout(\"window.location.href='" + url + "'\", 100);");
    }

    public File gerarArquivo(String retornoLocal) throws DAOException, IOException, ClassNotFoundException {
        //Relatório já processado em EnviarRespostaTopio#armazenarRetorno
        return new File(retornoLocal);
    }

    public String getReportUrl() throws DAOException {
        return getCallbackUrl().toString();
    }

    private AsyncProcess getAsyncProcess() {
        return (AsyncProcess) getComponent().getDefaultModel().getObject();
    }

    private void logNotCache() {
        if ("br.com.celk.view.vigilancia.fichainvestigacaoagravo.ImpressaoFichaInvestigacaoAgravoPage".equals(getAsyncProcess().getJifClass())) {
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - Relatório não está no cache do Async, buscando no repositório");
        }
    }

    private void logAddCache() {
        if ("br.com.celk.view.vigilancia.fichainvestigacaoagravo.ImpressaoFichaInvestigacaoAgravoPage".equals(getAsyncProcess().getJifClass())) {
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - Adicionando no cache!");
        }
    }

    private void logVariables(String patharquivo, String titulo) {
        if ("br.com.celk.view.vigilancia.fichainvestigacaoagravo.ImpressaoFichaInvestigacaoAgravoPage".equals(getAsyncProcess().getJifClass())) {
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - Criando parametros da pagina");
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - tipoRelatorio: " + getTipoRelatorio().value());
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - retornoLocal: " + patharquivo);
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - retornolocalTitle: " + titulo);
            Loggable.log.info("ImpressaoFichaInvestigacaoAgravoPage - LINK: " + getCallbackUrl().toString());
        }
    }
}
