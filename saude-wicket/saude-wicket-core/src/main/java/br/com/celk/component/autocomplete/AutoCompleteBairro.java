package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.basico.Cidade;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteBairro extends AutoComplete<Bairro> {

    private List<Bairro> bairros = new ArrayList<Bairro>();
    private Cidade cidade;
    
    public AutoCompleteBairro(String id, IModel object) {
        super(id, object);
        init();
    }

    public AutoCompleteBairro(String id) {
        super(id);
        init();
    }
    
    private void init() {
        getAutoCompleteSettings().setThrottleDelay(600);
        add(new AttributeModifier("class", "uppercase"));
    }
    
    @Override
    protected Iterator<Bairro> getChoices(String input) {
        bairros.clear();
            if (StringUtils.trimToNull(input)!=null && cidade != null) {
                bairros = LoadManager.getInstance(Bairro.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, input))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Bairro.PROP_CIDADE), cidade))
                        .setMaxResults(10)
                        .start().getList();
            }
        return bairros.iterator();
    }

    @Override
    public String getTextValue(Bairro object) {
        return object.getDescricao();
    }
    
    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Cidade getCidade() {
        return cidade;
    }

}
