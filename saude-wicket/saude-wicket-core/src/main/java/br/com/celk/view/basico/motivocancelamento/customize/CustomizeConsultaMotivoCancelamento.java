package br.com.celk.view.basico.motivocancelamento.customize;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.MotivoCancelamento;
import java.util.Map;

/**
 *
 * <AUTHOR> Vinicius
 */
public class CustomizeConsultaMotivoCancelamento extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(MotivoCancelamento.PROP_DESCRICAO), QueryParameter.ILIKE));
        filterProperties.put(BundleManager.getString("codigo"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(MotivoCancelamento.PROP_CODIGO), QueryParameter.IGUAL));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("codigo"), VOUtils.montarPath(MotivoCancelamento.PROP_CODIGO));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(MotivoCancelamento.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return MotivoCancelamento.class;
    }

}
