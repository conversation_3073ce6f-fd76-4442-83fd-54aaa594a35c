package br.com.celk.component.resource;

import br.com.celk.system.report.ReportExporter;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.util.log.Loggable;
import java.io.*;
import org.apache.wicket.request.resource.IResource;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.string.StringValue;
import org.apache.wicket.util.time.Duration;

/**
 *
 * <AUTHOR>
 */
public class FtpReportResource implements IResource {

    @Override
    public void respond(IResource.Attributes attributes) {
        try {
            StringValue retornoLocal = attributes.getParameters().get("retornoLocal");

            File file = salvarPdf(retornoLocal.toString());
            FileResourceStream fileResourceStream = new FileResourceStream(file);
            ResourceStreamResource resource = new ResourceStreamResource(fileResourceStream);
            resource.setCacheDuration(Duration.NONE);
            resource.respond(attributes);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public File salvarPdf(String retornoLocal) throws DAOException, IOException, ClassNotFoundException {
        File f = new File(retornoLocal);
        ObjectInputStream ois = new ObjectInputStream(new FileInputStream(f));
        final DataReport dr;
        dr = (DataReport) ois.readObject();

        File newFile = new ReportExporter(dr, TipoRelatorio.PDF).createFile();
        
        return newFile;
    }
}
