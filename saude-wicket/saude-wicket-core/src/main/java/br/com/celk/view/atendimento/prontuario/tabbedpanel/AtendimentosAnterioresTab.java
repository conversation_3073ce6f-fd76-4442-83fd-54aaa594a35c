package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.view.atendimento.prontuario.panel.TabAtendimentosTabbedPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR> <PERSON>
 */
public class AtendimentosAnterioresTab extends TabPanel<NoHistoricoClinicoDTO> {

    private NoHistoricoClinicoDTO noHistoricoClinicoDTO;

    public AtendimentosAnterioresTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        noHistoricoClinicoDTO = object;
        init();
    }

    private void init() {
        List<ITab> tabs = new ArrayList<ITab>();

        tabs.add(new CadastroTab<NoHistoricoClinicoDTO>(noHistoricoClinicoDTO) {
            @Override
            public ITabPanel<NoHistoricoClinicoDTO> newTabPanel(String id, NoHistoricoClinicoDTO noHistoricoClinicoDTO) {
                return new AtendimentosInternoTab(id, noHistoricoClinicoDTO);
            }
        });

        add(new TabAtendimentosTabbedPanel("wizard", noHistoricoClinicoDTO, false, tabs, false));
    }

    @Override
    public String getTitle() {
        return bundle("atendimentos_anteriores");
    }
}
