package br.com.celk.view.unidadesaude.justificativapriorizacao.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaJustificativaPriorizacaoDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerJustificativaPriorizacao extends Panel implements IRestricaoContainer<QueryConsultaJustificativaPriorizacaoDTOParam> {

    private InputField<String> txtJustificativa;

    private QueryConsultaJustificativaPriorizacaoDTOParam param = new QueryConsultaJustificativaPriorizacaoDTOParam();

    public RestricaoContainerJustificativaPriorizacao(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtJustificativa = new InputField<String>("justificativa"));
        
        add(root);
    }

    @Override
    public QueryConsultaJustificativaPriorizacaoDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtJustificativa.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtJustificativa;
    }

}
