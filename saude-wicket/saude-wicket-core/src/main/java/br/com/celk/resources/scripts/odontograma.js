function WicketComponent(markupid, callBackURL) {
    this.markupid = markupid;
    this.callBackURL = callBackURL;

}

var TAGs = {
    TEETH: "ck-teeth",
    TOOTH: "ck-tooth",
    SVG_TOOTH: "ck-svg-tooth",
    SVG_FACES: "ck-svg-faces",
}

class Teeth {
    constructor(JQObject) {
        this.JQObject = JQObject;
    }

    get id() {
        return this.JQObject.id;
    }

    init() {
        var JQ = $(this.JQObject);

        var _tooths = [];
        var _JQTooths = JQ.find(TAGs.TOOTH).get();
        _JQTooths.forEach(function(JQTooth) {
            var tooth = new Tooth(JQTooth);
            _tooths.push(tooth);
            tooth.init();
        });

        this.tooths = _tooths;
    }
}

class Tooth {
    constructor(JQObject) {
        this.JQObject = JQObject;
    }

    get id() {
        return this.JQObject.id;
    }

    init() {
        var JQ = $(this.JQObject);

        var id = JQ.attr('tooth-id');
        this.OdontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);

        var _JQFaces = JQ.find(TAGs.SVG_FACES)[0];
        var _faces = new Faces(_JQFaces);
        _faces.init(this.OdontogramaTratamentoJsonDTO.faces);

        this.faces = _faces;
    }
}

class Faces {
    constructor(JQObject) {
        this.JQObject = JQObject;
        this.mesial;
        this.distal;
        this.palatal;
        this.oclusal;
        this.vestibular;
    }

    get id() {
        return this.JQObject.id;
    }

    init(OdontogramaFacesJsonDTO) {
        var JQ = $(this.JQObject);

        var onClick = function(e) {
            e.preventDefault();
            var id = e.target.id;
            var status = OdontogramaFacesJsonDTO[id];
            if (!status) {
                $(this).css('fill', 'rgba(239, 83, 80, 1)');
                OdontogramaFacesJsonDTO[id] = StatusFaces.PENDENTE;
            } else {
                if (status === StatusFaces.PENDENTE) {
                    $(this).css('fill', 'rgba(91, 192, 222, 1)');
                    OdontogramaFacesJsonDTO[id] = StatusFaces.CONCLUIDO;
                } else if (status === StatusFaces.CONCLUIDO) {
                    $(this).css('fill', '#ffffff');
                    delete OdontogramaFacesJsonDTO[id];
                }
            }
        };

        var callback = function() {
            var svg = JQ.find('svg');
            svg.click(onClick);

//            var _JQMesial = JQ.find('path[id=mesial]');
//            var _JQDistal = JQ.find('path[id=distal]');
//            var _JQPalatal = JQ.find('path[id=palatal]');
//            var _JQOclusal = JQ.find('rect[id=oclusal]');
//            var _JQVestibular = JQ.find('path[id=vestibular]');
        };

        var $img = JQ.find('img[src*=".svg"]').get();
        SVGInjector($img, {}, callback);
    }
}

/**
 * Singleton do Odontograma.
 * @type {Object}
 */
var Odontogram = {

    init: function(callBackURL, denteJSON, situacaoJSON, denteSituacaoJSON, codigoAtendimento) {
        Odontogram.callBackURLPanel = callBackURL;

        Odontogram.enable = false;
        Odontogram.wicketComponents = [];

//        Odontogram.denteList = JSON.parse(denteJSON);
        Odontogram.situacaoList = JSON.parse(situacaoJSON);
        Odontogram.denteSituacaoList = JSON.parse(denteSituacaoJSON);
        Odontogram.codigoAtendimento = codigoAtendimento;
        Odontogram.codigoFicha;

        /* TODO: Reestruturar o JAVASCRIPT
            Odontogram.teeth = [];

            var _JQTeeths = $(TAGs.TEETH).get();
            _JQTeeths.forEach(function(JQTeeth) {
                var teeth = new Teeth(JQTeeth);
                teeth.init();
            });
        */

        Odontogram.initDialog();
        Odontogram.initNotification();
        Odontogram.injectSVGs();
    },

    injectSVGs: function() {
        var $faces = $('ck-svg-faces').find('img[src*=".svg"]').get();
        var $teeth = $('ck-svg-tooth').find('img[src*=".svg"]').get();

        if ($faces && $teeth) {
            SVGInjector($faces);
            SVGInjector($teeth, {
                each: function(svg) { Odontogram.addListeners(svg) }
            }, function() {
                Odontogram.refresh();
            });
        }
    },

    lock: function () {
        loading(true);
    },

    unlock: function () {
        stopLoading();
    },

    initDialog: function() {
        Odontogram.dialog = $('div[id=dialog]');

        Odontogram.dialog.close = function() {
            var buttons = this.getButtons();
            buttons.setVisible(true);
            buttons.hide();

            this.removeClass('on');
            $(this).hide();
        };

        Odontogram.dialog.apply = function(dto) {
            var situacao = dto.situacao;
            var target = dto.target;
            var svg = dto.svg;

            var id = $(svg).attr('name');

            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
            if (!odontogramaTratamentoJsonDTO) {
                odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);
                Odontogram.getTratamentos().push(odontogramaTratamentoJsonDTO);
            }

            odontogramaTratamentoJsonDTO.inativo = false;

            var div = $(svg).closest('ck-tooth');

            if (!situacao.codigo) {
                if (target.id === 'coroa') {
                    var situacaoCoroa = odontogramaTratamentoJsonDTO.coroa;
                    if (situacaoCoroa) {
                        odontogramaTratamentoJsonDTO.coroa = null;
                        var svgTooth = $(svg).closest('ck-svg-tooth')
                        $(svgTooth).removeAttr('data-abbreviation');

                        var coroa = $(svg).find('path[id=coroa]');
                        Odontogram.setColor(coroa, 'ffffff');
                        var title = coroa.find("title");
                        title.empty();

                        // Ao definir NENHUM tratamento para coroa do dente, deve limpar as faces
                        var faces = div.find('ck-svg-faces > svg');

                        $(faces).find("path[id=palatal]").css('fill', '#ffffff');
                        $(faces).find("path[id=vestibular]").css('fill', '#ffffff');
                        $(faces).find("path[id=distal]").css('fill', '#ffffff');
                        $(faces).find("path[id=mesial]").css('fill', '#ffffff');
                        $(faces).find("rect[id=oclusal]").css('fill', '#ffffff');

                        odontogramaTratamentoJsonDTO.faces = {};
                    }
                } else {
                    var colo = $(svg).find('path[id=colo]');
                    var raiz = $(svg).find('path[id=raiz]');

                    var titleColo = colo.find("title");
                    var titleRaiz = raiz.find("title");

                    var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                    var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                    if (target.id === 'colo') {
                        if (situacaoColo) {
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                odontogramaTratamentoJsonDTO.colo = null;
                                odontogramaTratamentoJsonDTO.raiz = null;
                                Odontogram.setColor(colo, 'ffffff');
                                Odontogram.setColor(raiz, 'ffffff');
                                titleColo.empty();
                                titleRaiz.empty();
                            } else {
                                odontogramaTratamentoJsonDTO.colo = null;
                                Odontogram.setColor(colo, 'ffffff');
                                titleColo.empty();
                            }
                        }
                    } else if (target.id === 'raiz') {
                        if (situacaoRaiz) {
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                odontogramaTratamentoJsonDTO.raiz = null;
                                odontogramaTratamentoJsonDTO.colo = null;
                                Odontogram.setColor(raiz, 'ffffff');
                                Odontogram.setColor(colo, 'ffffff');
                                titleRaiz.empty();
                                titleColo.empty();
                            } else {
                                odontogramaTratamentoJsonDTO.raiz = null;
                                Odontogram.setColor(raiz, 'ffffff');
                                titleRaiz.empty();
                            }
                        }
                    }
                }

                Odontogram.toJSON();

                var markupid = div.attr('id');
                var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                if (wicketComponent) {
                    var url = wicketComponent.callBackURL;
                    Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'restaurar', face: target.id });
                }
            } else {
//                // Restaura o dente quando escolhido uma nova situação (dente inativo)
//                if (odontogramaTratamentoJsonDTO.inativo) {
//                    delete odontogramaTratamentoJsonDTO['raiz'];
//                    delete odontogramaTratamentoJsonDTO['colo'];
//                    delete odontogramaTratamentoJsonDTO['coroa'];
//                    delete odontogramaTratamentoJsonDTO['faces'];
//                    delete odontogramaTratamentoJsonDTO['status'];
//                    delete odontogramaTratamentoJsonDTO['inativo'];
//                    delete odontogramaTratamentoJsonDTO['concluido'];
//                    delete odontogramaTratamentoJsonDTO['cadastrado'];
//
//                    $(div).removeClass('off');
//                    $(div).removeAttr('disabled');
//
//                    var linkNovaSituacao = div.find('a[id=nova]');
//                    linkNovaSituacao.attr('visible', false);
//
//                    var svgTooth = $(svg).closest('ck-svg-tooth')
//                    $(svgTooth).removeAttr('data-abbreviation');
//
//                    var cor = 'FFFFFF';
//                    var colo = $(svg).find('path[id=colo]');
//                    var raiz = $(svg).find('path[id=raiz]');
//                    var coroa = $(svg).find('path[id=coroa]');
//
//                    var faces = div.find('ck-svg-faces > svg');
//                    var distal = $(faces).find("path[id=distal]");
//                    var mesial = $(faces).find("path[id=mesial]");
//                    var oclusal = $(faces).find("rect[id=oclusal]");
//                    var palatal = $(faces).find("path[id=palatal]");
//                    var vestibular = $(faces).find("path[id=vestibular]");
//
//                    Odontogram.setColor(colo, cor);
//                    Odontogram.setColor(raiz, cor);
//                    Odontogram.setColor(coroa, cor);
//
//                    Odontogram.setColor(distal, cor);
//                    Odontogram.setColor(mesial, cor);
//                    Odontogram.setColor(oclusal, cor);
//                    Odontogram.setColor(palatal, cor);
//                    Odontogram.setColor(vestibular, cor);
//
//                    var titleColo = colo.find("title");
//                    var titleRaiz = raiz.find("title");
//                    var titleCoroa = coroa.find("title");
//
//                    titleColo.empty();
//                    titleRaiz.empty();
//                    titleCoroa.empty();
//                }

                situacao.codigoAtendimento = Odontogram.codigoAtendimento;
                situacao.tratamentoUrgente = Odontogram.tratamentoUrgente;
                if (situacao.desabilitaAoAplicar) {
                    odontogramaTratamentoJsonDTO.inativo = true;
                    odontogramaTratamentoJsonDTO.status = situacao;

                    if (odontogramaTratamentoJsonDTO.status.tipoSituacao === TipoSituacao.HISTORICO) {
                        odontogramaTratamentoJsonDTO.status.concluido = true;
                    }

                    $(div).attr('disabled', true);

                    var linkReverter = div.find('a[id=reverter]');
                    linkReverter.attr('visible', true);

                    Odontogram.setDataAbbreviation(svg, situacao.referencia);

                    var cor = situacao.cor;
                    if (cor) {
                        var colo = $(svg).find('path[id=colo]');
                        var raiz = $(svg).find('path[id=raiz]');
                        var coroa = $(svg).find('path[id=coroa]');

                        var faces = div.find('ck-svg-faces > svg');
                        var distal = $(faces).find("path[id=distal]");
                        var mesial = $(faces).find("path[id=mesial]");
                        var oclusal = $(faces).find("rect[id=oclusal]");
                        var palatal = $(faces).find("path[id=palatal]");
                        var vestibular = $(faces).find("path[id=vestibular]");

                        Odontogram.setColor(colo, cor);
                        Odontogram.setColor(raiz, cor);
                        Odontogram.setColor(coroa, cor);

                        Odontogram.setColor(distal, cor);
                        Odontogram.setColor(mesial, cor);
                        Odontogram.setColor(oclusal, cor);
                        Odontogram.setColor(palatal, cor);
                        Odontogram.setColor(vestibular, cor);

                        var titleColo = colo.find("title");
                        var titleRaiz = raiz.find("title");
                        var titleCoroa = coroa.find("title");

                        titleColo.empty();
                        titleColo.append(situacao.descricao);

                        titleRaiz.empty();
                        titleRaiz.append(situacao.descricao);

                        titleCoroa.empty();
                        titleCoroa.append(situacao.descricao);
                    }

                    var markupid = div.attr('id');
                    var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                    if (wicketComponent) {
                        var url = wicketComponent.callBackURL;
                        Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'aplicar' });
                    }
    //                $(div).attr('ready', 'true');
                } else {
                    if (target.id === 'coroa') { // if (situacao.tipoAplicacao === TipoAplicacao.COROA) {
                        // Ao mudar o tratamento que outrora estava cadastrado/concluído, deve limpar as faces
                        var situacaoAnterior = odontogramaTratamentoJsonDTO.coroa;
                        if (situacaoAnterior && (situacaoAnterior.cadastrado || situacaoAnterior.concluido)) {
                            var faces = div.find('ck-svg-faces > svg');

                            $(faces).find("path[id=palatal]").css('fill', '#ffffff');
                            $(faces).find("path[id=vestibular]").css('fill', '#ffffff');
                            $(faces).find("path[id=distal]").css('fill', '#ffffff');
                            $(faces).find("path[id=mesial]").css('fill', '#ffffff');
                            $(faces).find("rect[id=oclusal]").css('fill', '#ffffff');

                            odontogramaTratamentoJsonDTO.faces = {};
                        }

                        odontogramaTratamentoJsonDTO.coroa = situacao;
                        Odontogram.setDataAbbreviation(svg, situacao.referencia);
                        var coroa = $(svg).find('path[id=coroa]');
                        var title = coroa.find("title");
                        title.empty();
                        title.append(situacao.descricao);
                    } else {
                        var colo = $(svg).find('path[id=colo]');
                        var raiz = $(svg).find('path[id=raiz]');

                        var titleColo = colo.find("title");
                        var titleRaiz = raiz.find("title");

                        if (!situacao.tipoPreenchimento || situacao.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                            odontogramaTratamentoJsonDTO.colo = situacao;
                            odontogramaTratamentoJsonDTO.raiz = situacao;
                            Odontogram.setColor(colo, situacao.cor);
                            Odontogram.setColor(raiz, situacao.cor);

                            titleColo.empty();
                            titleColo.append(situacao.descricao);

                            titleRaiz.empty();
                            titleRaiz.append(situacao.descricao);
                        } else if (situacao.tipoPreenchimento === TipoPreenchimento.COLO) {
                            var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                            if (situacaoRaiz && situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                                Odontogram.setColor(raiz, 'ffffff');
                                odontogramaTratamentoJsonDTO.raiz = null;
                                titleRaiz.empty();
                            }

                            odontogramaTratamentoJsonDTO.colo = situacao;
                            Odontogram.setColor(colo, situacao.cor);

                            titleColo.empty();
                            titleColo.append(situacao.descricao);
                        } else if (situacao.tipoPreenchimento === TipoPreenchimento.RAIZ) {
                            var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                            if (situacaoColo && situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                                Odontogram.setColor(colo, 'ffffff');
                                odontogramaTratamentoJsonDTO.colo = null;
                                titleColo.empty();
                            }

                            odontogramaTratamentoJsonDTO.raiz = situacao;
                            Odontogram.setColor(raiz, situacao.cor);

                            titleRaiz.empty();
                            titleRaiz.append(situacao.descricao);
                        }
                    }

                    Odontogram.toJSON();
                }
            }

            this.close();
        };

        Odontogram.dialog.getButtons = function() {
            if (!Odontogram.dialog.buttons) {
                var setVisible = function(visible) {
                    $(this).attr('visible', visible);
                };

                var _buttons = $(this).find('ck-buttons');
                _buttons.setVisible = setVisible;

                _buttons.hide = function() {
                    this.setVisible(false);

                    this.btnReverter.setVisible(false);
                    this.btnConcluir.setVisible(false);
                    this.btnAplicar.setVisible(false);
                    this.btnNovo.setVisible(false);

                    delete _buttons.target;
                    delete _buttons.odontogramaTratamentoJsonDTO;
                };

                _buttons.show = function(target, odontogramaTratamentoJsonDTO) {
                    _buttons.target = target;
                    _buttons.odontogramaTratamentoJsonDTO = odontogramaTratamentoJsonDTO;

                    this.setVisible(true);
                };

                var concluirTratamento = function(target, odontogramaTratamentoJsonDTO) {
                    var face = target.id;
                    if (face === 'coroa') {
                        odontogramaTratamentoJsonDTO.coroa.concluido = true;

                        var faces = $(target).closest('ck-tooth').find('ck-svg-faces > svg');

                        if (odontogramaTratamentoJsonDTO.faces.palatal) {
                            odontogramaTratamentoJsonDTO.faces.palatal = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=palatal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.vestibular) {
                            odontogramaTratamentoJsonDTO.faces.vestibular = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=vestibular]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.distal) {
                            odontogramaTratamentoJsonDTO.faces.distal = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=distal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.mesial) {
                            odontogramaTratamentoJsonDTO.faces.mesial = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=mesial]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.oclusal) {
                            odontogramaTratamentoJsonDTO.faces.oclusal = StatusFaces.CONCLUIDO;
                            $(faces).find("rect[id=oclusal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }
                    } else {
                        var situacao;
                        var svg = $(target).closest('svg');

                        var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                        var situacaoRaiz = odontogramaTratamentoJsonDTO.colo;
                        if (face === 'colo') {
                            odontogramaTratamentoJsonDTO.colo.concluido = true;
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                odontogramaTratamentoJsonDTO.raiz.concluido = true;
                                var raiz = svg.find('path[id=raiz]');
                                $(raiz).css('fill', 'rgba(91, 192, 222, 1)');
                            }
                        } else {
                            odontogramaTratamentoJsonDTO.raiz.concluido = true;
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                odontogramaTratamentoJsonDTO.colo.concluido = true;
                                var colo = svg.find('path[id=colo]');
                                $(colo).css('fill', 'rgba(91, 192, 222, 1)');
                            }
                        }

                        $(target).css('fill', 'rgba(91, 192, 222, 1)');
                    }
                };

                var _btnConcluir = _buttons.find('input[id=concluir]');
                _btnConcluir.setVisible = setVisible;
                _btnConcluir.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    if (target && odontogramaTratamentoJsonDTO) {
                        if (isHistorico(target, odontogramaTratamentoJsonDTO)) {
                            concluirTratamento(target, odontogramaTratamentoJsonDTO);
                            Odontogram.toJSON();
                        } else {
                            var div = $(target).closest('ck-tooth');

                            var face = target.id;
                            var situacao;
                            if (face === 'coroa') {
                                situacao = odontogramaTratamentoJsonDTO.coroa;
                            } else if (face === 'colo') {
                                situacao = odontogramaTratamentoJsonDTO.colo;
                            } else if (face === 'raiz') {
                                situacao = odontogramaTratamentoJsonDTO.raiz;
                            }

                            if (!situacao.tratamentoUrgente && situacao.codigoFicha !== Odontogram.codigoFicha) {
                                Odontogram.message(div, "Não é possível realizar a evolução deste tratamento pois ele não pertence a ficha selecionada.");
                                return;
                            }

                            var markupid = div.attr('id');
                            var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                            if (wicketComponent) {
                                var url = wicketComponent.callBackURL;
                                Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'evoluir', face: target.id });
                            }
                        }

                        Odontogram.dialog.close();
                    }
                });

                var isHistorico = function(target, odontogramaTratamentoJsonDTO) {
                    var tipoSituacao;
                    var face = target.id;

                    if (face === 'coroa') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.coroa.tipoSituacao;
                    } else if (face === 'colo') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.colo.tipoSituacao;
                    } else if (face === 'raiz') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.raiz.tipoSituacao;
                    }

                    if (tipoSituacao && tipoSituacao === TipoSituacao.HISTORICO) {
                        return true;
                    }
                    return false;
                };

                // Define o tratamento como concluido quando tipo for HISTORICO
                var concluiTratamentoQuandoHistorico = function(target, odontogramaTratamentoJsonDTO) {
                    if (isHistorico(target, odontogramaTratamentoJsonDTO)) {
                        concluirTratamento(target, odontogramaTratamentoJsonDTO);
                    }
                };

                var _btnAplicar = _buttons.find('input[id=aplicar]');
                _btnAplicar.setVisible = setVisible;
                _btnAplicar.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    var div = $(target).closest('ck-tooth');

//                    if (!Odontogram.validarEvolucao(div, odontogramaTratamentoJsonDTO)) {
//                        Odontogram.dialog.close();
//                        return;
//                    }

                    concluiTratamentoQuandoHistorico(target, odontogramaTratamentoJsonDTO);

                    var markupid = div.attr('id');
                    var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                    if (wicketComponent) {
                        var url = wicketComponent.callBackURL;
                        Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'aplicar', face: target.id });
                    }

                    Odontogram.dialog.close();
                });

                var _btnNovo = _buttons.find('input[id=novo]');
                _btnNovo.setVisible = setVisible;
                _btnNovo.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    var face = target.id;
                    if (face === 'coroa') {
                        delete odontogramaTratamentoJsonDTO.coroa;
                        delete odontogramaTratamentoJsonDTO.faces;

                        var svgTooth = $(target).closest('ck-svg-tooth')
                        $(svgTooth).removeAttr('data-abbreviation');

                        var div = $(target).closest('ck-tooth');
                        var divFaces = $(div).find('ck-svg-faces > svg');

                        $(divFaces).find("path[id=palatal]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=vestibular]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=distal]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=mesial]").css('fill', '#ffffff');
                        $(divFaces).find("rect[id=oclusal]").css('fill', '#ffffff');
                    } else {
                        var svg = $(target).closest('svg');

                        var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                        var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                        if (face === 'colo') {
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                var raiz = svg.find('path[id=raiz]');
                                $(raiz).find("title").empty();
                                $(raiz).css('fill', '#ffffff');
                                delete odontogramaTratamentoJsonDTO.raiz;
                            }
                            delete odontogramaTratamentoJsonDTO.colo;
                        } else {
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                var colo = svg.find('path[id=colo]');
                                $(colo).find("title").empty();
                                $(colo).css('fill', '#ffffff');
                                delete odontogramaTratamentoJsonDTO.colo;
                            }
                            delete odontogramaTratamentoJsonDTO.raiz;
                        }

                        $(target).find("title").empty();
                        $(target).css('fill', '#ffffff');
                    }

                    // Odontogram.toJSON();
                    Odontogram.dialog.close();
                });


                var _btnReverter = _buttons.find('input[id=reverter]');
                _btnReverter.setVisible = setVisible;
                _btnReverter.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    if (target && odontogramaTratamentoJsonDTO) {
                        var div = $(target).closest('ck-tooth');
                        var markupid = div.attr('id');
                        var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                        if (wicketComponent) {
                            var url = wicketComponent.callBackURL;
                            Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'restaurar', face: target.id });
                        }
                    }

                    Odontogram.dialog.close();
                });

                _buttons.btnReverter = _btnReverter;
                _buttons.btnConcluir = _btnConcluir;
                _buttons.btnAplicar = _btnAplicar;
                _buttons.btnNovo = _btnNovo;

                Odontogram.dialog.buttons = _buttons;
            }

            return Odontogram.dialog.buttons;
        };

        Odontogram.dialog.show = function(target, situacoes) {
            var svg = $(target).closest('svg');

            var offset = svg.offset();
            var left = offset.left;
            var top = offset.top;

            var centerPosition = left - 5;
            var bottomPosition = top + 100;

            this.addClass('on');

            $(this).show();

            this.css('left', centerPosition);
            this.css('top', bottomPosition);

            this.children("fieldset").remove();

            var buttons = this.getButtons();

            var id = $(svg).attr('name');

            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
            if (odontogramaTratamentoJsonDTO) {
                var situacao = odontogramaTratamentoJsonDTO[target.id];
                if (situacao) {
//                    if (situacao.concluido) {
//                        situacoes.push(MenuOptions.NEW);
//                    } else if (situacao.cadastrado) {
//                        if (target.id !== 'coroa') {
                            if (situacao.cadastrado) {
                                if (situacao.concluido) {
                                    buttons.btnNovo.setVisible(true);
                                } else {
                                    buttons.btnConcluir.setVisible(true);
                                }
                            } else {
                                buttons.btnReverter.setVisible(true);
                                buttons.btnAplicar.setVisible(true);
                            }

                            buttons.show(target, odontogramaTratamentoJsonDTO);
//                        }
//                    }
                } else if (odontogramaTratamentoJsonDTO.inativo) {
                    buttons.btnReverter.setVisible(true);
                    buttons.show(target, odontogramaTratamentoJsonDTO);
                }
            }

            // Balanceia os itens da lista
            function balanceList(size, fact) {
                if (size <= 15) {
                    return size;
                }

                size = Math.round(size / fact);

                return balanceList(size, fact++);
            };

            var size = situacoes.length;
            var i, j, balancedList, chunk = Math.round(size / 2);//balanceList(size, 2);

            var fieldset = $("<fieldset>");
            fieldset.insertBefore(buttons);

            var table = $("<table>");
            for (i = 0, j = situacoes.length; i < j; i += chunk) {
                balancedList = situacoes.slice(i, i + chunk);

                var row = 0;
                balancedList.forEach(function(situacao) {
                    row++;

                    var tr = table.find("tr[row=" + row +"]");
                    if (tr.length === 0) {
                        var tr = $("<tr>");
                        tr.attr('row', row);
                        table.append(tr);
                    }

                    var td = $('<td>');

                    var span = $('<span>');
                        var value = situacao.descricao + (situacao.referencia ? " (" + situacao.referencia + ")" : "");
                        span.append(value);

                    td.append(span);

                    td.click({ situacao: situacao, target: target, svg: svg }, function(e) {
                        e.preventDefault();
                        var dto = e.data;
                        Odontogram.dialog.apply(dto);
                    });

                    tr.append(td);
                });

                fieldset.append(table);
            }
        };

        var close = Odontogram.dialog.find('a[id=close]');
        close.click(function(e) {
            e.preventDefault();
            Odontogram.dialog.close();
        });
    },

    initNotification: function() {
        $.notify.defaults({
            clickToHide : true,
            autoHide : true,
            autoHideDelay : 6500,
            arrowShow : true,
            arrowSize : 5,
            elementPosition : 'top right',
            globalPosition : 'top right',
            className : '',
            showAnimation : 'slideDown',
            showAnimation : 'slideDown',
            showDuration : 400,
            hideAnimation : 'slideUp',
            hideDuration : 200,
            gap : 10
        });
    },

    message: function(component, message) {
        // var notification = $('div[id=notifications]');
        $.notify(
            component,
            message
        );
    },

    setDataAbbreviation: function(svg, ref) {
        var div = $(svg).closest('ck-svg-tooth');
        $(div).attr('data-abbreviation', ref.substring(0, 5));
    },

    setColor: function(path, color) {
        $(path).css('fill', "#" + color);
    },

//    validarEvolucao: function(div, odontogramaTratamentoJsonDTO) {
//        var valid = false;
//
//        if (!odontogramaTratamentoJsonDTO || (!odontogramaTratamentoJsonDTO.status && !odontogramaTratamentoJsonDTO.coroa && !odontogramaTratamentoJsonDTO.colo && !odontogramaTratamentoJsonDTO.raiz)) {
//            Odontogram.message(div, "Por favor, selecione o tratamento a ser realizado neste dente");
//        } else {
//            if (odontogramaTratamentoJsonDTO.status) {
//                if (odontogramaTratamentoJsonDTO.status.cadastrado) {
//                    Odontogram.message(div, "O tratamento deste dente já foi cadastrado.");
//                } else {
//                    valid = true;
//                }
//            } else if ((!odontogramaTratamentoJsonDTO.coroa || odontogramaTratamentoJsonDTO.coroa.cadastrado) &&
//                       (!odontogramaTratamentoJsonDTO.colo  || odontogramaTratamentoJsonDTO.colo.cadastrado)  &&
//                       (!odontogramaTratamentoJsonDTO.raiz  || odontogramaTratamentoJsonDTO.raiz.cadastrado)) {
//                Odontogram.message(div, "Os tratamentos aplicados neste dente já foram cadastrados, favor concluí-los ou então iniciar um novo.");
//            } else {
//                valid = true;
//            }
//        }
//        return valid;
//    },

    addListeners: function(svg) {
//        var watermark = $(svg).find("path[id=watermark]")
//        if (watermark) {
//            $(watermark).css('display', 'block');
////                var contorno = $(svg).find('#contorno');
////                $(contorno).css('filter', "url(#opacityFilter)");
//        }

        var id = $(svg).attr('name');
        var div = $(svg).closest('ck-tooth');
        var markupid = div.attr('id');

//        var linkAplicar = div.find('a[id=aplicar]');
//        linkAplicar.click({id: id, markupid: markupid}, function(e) {
//            e.preventDefault();
//            Odontogram.dialog.close();
//
//            var id = e.data.id;
//            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
//
//            if (Odontogram.validarEvolucao(div, odontogramaTratamentoJsonDTO)) {
//                var markupid = e.data.markupid;
//                var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
//                if (wicketComponent) {
//                    var url = wicketComponent.callBackURL;
//                    Odontogram.post(wicketComponent.callBackURL, odontogramaTratamentoJsonDTO);
//                }
//            }
//        });

        var linkReverter = div.find('a[id=reverter]');

        linkReverter.click({id: id}, function(e) {
            e.preventDefault();
            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];

            var div = $(this).closest('ck-tooth');
            $(div).removeClass('off');
            $(div).removeAttr('disabled');

//            if (odontogramaTratamentoJsonDTO.status) { odontogramaTratamentoJsonDTO.status = null; }
//            if (odontogramaTratamentoJsonDTO.inativo) { odontogramaTratamentoJsonDTO.inativo = false; }
//
//            Odontogram.toJSON();
//            Odontogram.reload(odontogramaTratamentoJsonDTO);

            $(this).attr('visible', false);

            var markupid = div.attr('id');
            var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
            if (wicketComponent) {
                var url = wicketComponent.callBackURL;
                Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'reverter' });
            }
        });

        var linkNovaSituacao = div.find('a[id=nova]');
        linkNovaSituacao.click({id: id}, function(e) {
            e.preventDefault();
            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];

            var div = $(this).closest('ck-tooth');

            if (odontogramaTratamentoJsonDTO.status.concluido) {
                $(div).removeClass('off');
                $(div).removeAttr('disabled');

                odontogramaTratamentoJsonDTO.status = null;
                odontogramaTratamentoJsonDTO.colo = null;
                odontogramaTratamentoJsonDTO.raiz = null;
                odontogramaTratamentoJsonDTO.coroa = null;
                odontogramaTratamentoJsonDTO.faces = null;

                Odontogram.reload(odontogramaTratamentoJsonDTO);

                $(this).attr('visible', false);
            } else {
                Odontogram.message(div, "É necessário que a situação deste dente seja concluída ou cancelada para poder selecionar uma nova situação");
            }
        });

//        var linkNovoTratamento = div.find('a[id=new]');
//
//        linkNovoTratamento.click({id: id}, function(e) {
//            e.preventDefault();
//            var id = e.data.id;
//            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
//            odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);
//
//            var div = $(this).closest('ck-tooth');
//            $(div).removeClass('off');
//            $(div).removeAttr('disabled');
//
//            $(this).attr('visible', false);
//
//            Odontogram.toJSON();
//        });

        var DTOListener = function(id, tipoAplicacao) {
            this.id = id;
            this.tipoAplicacao = tipoAplicacao;
        };

        var ClickListener = function(e) {
            e.preventDefault();
            var div = $(this).closest('ck-tooth');
            var disabled = $(div).attr('disabled');
            if (disabled) {
                return;
            }

            var dto = e.data;
            Odontogram.showDialog(this, dto);
        };

        var coroa = $(svg).find('path[id=coroa]');
        $(coroa).click(new DTOListener(id, TipoAplicacao.COROA), ClickListener);


        //var raizDTOListener = new DTOListener(id, TipoAplicacao.RAIZ);

        var colo = $(svg).find('path[id=colo]');
        $(colo).click(new DTOListener(id, TipoAplicacao.RAIZ), ClickListener);

        var raiz = $(svg).find('path[id=raiz]');
        $(raiz).click(new DTOListener(id, TipoAplicacao.RAIZ), ClickListener);


        var faces = div.find('ck-svg-faces > svg');
        $(faces).click({id: id}, function(e) {
            e.preventDefault();
            var face = e.target;
            if (face.id == 'faces') {
                return;
            }

            var div = $(this).closest('ck-tooth');
            var disabled = $(div).attr('disabled');
            if (disabled) {
                return;
            }

            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
            if (!odontogramaTratamentoJsonDTO) {
                odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);
                Odontogram.getTratamentos().push(odontogramaTratamentoJsonDTO);
            }

            if (!odontogramaTratamentoJsonDTO.coroa) {
                Odontogram.message(div, "Para selecionar as faces da coroa é necessário que seja definido a situação/tipo de tratamento para a mesma");
                return;
            } else if (odontogramaTratamentoJsonDTO.coroa.concluido) {
                Odontogram.message(div, "O tratamento definido para a coroa deste dente já foi concluído, favor realizar um novo procedimento");
                return;
            }

            if (!odontogramaTratamentoJsonDTO.faces) {
                odontogramaTratamentoJsonDTO.faces = new OdontogramaFacesJsonDTO();
            }

            var status = odontogramaTratamentoJsonDTO.faces[face.id];
            if (!status) {
                $(face).css('fill', 'rgba(239, 83, 80, 1)');
                odontogramaTratamentoJsonDTO.faces[face.id] = StatusFaces.PENDENTE;
            } else {
                if (status === StatusFaces.PENDENTE) {
                    $(face).css('fill', 'rgba(91, 192, 222, 1)');
                    odontogramaTratamentoJsonDTO.faces[face.id] = StatusFaces.CONCLUIDO;
                } else if (status === StatusFaces.CONCLUIDO) {
                    $(face).css('fill', '#ffffff');
                    delete odontogramaTratamentoJsonDTO.faces[face.id];
                }
            }

            if (odontogramaTratamentoJsonDTO.coroa.cadastrado) {
                var markupid = div.attr('id');
                var wicketComponent = $.grep(Odontogram.wicketComponents, function(item) { return item.markupid == markupid})[0];
                if (wicketComponent) {
                    var url = wicketComponent.callBackURL;
                    Odontogram.post(url, odontogramaTratamentoJsonDTO, { action: 'atualizarFaces' });
                }
            } else {
                Odontogram.toJSON();
            }
        });
    },

    showDialog: function(target, dto) {
        Odontogram.dialog.close();
        var id = dto.id;
        var tipoAplicacao = dto.tipoAplicacao;

        var denteSituacao = $.grep(Odontogram.denteSituacaoList, function(denteSituacao) { return denteSituacao.id == id })[0];
        if (denteSituacao) {
            var situacoes = [];
            denteSituacao.situacaoList.forEach(function(codigo) {
                var situacao = $.grep(Odontogram.situacaoList, function(situacao) {
                    return situacao.codigo === codigo && (situacao.desabilitaAoAplicar || !situacao.tipoAplicacao || (!tipoAplicacao || tipoAplicacao === situacao.tipoAplicacao));
                })[0];

                if (situacao) { situacoes.push(situacao); }
            });

            if (situacoes) {
                situacoes.sort(function(oldest, newest) {
                    return oldest.descricao.localeCompare(newest.descricao);
                });

                Odontogram.dialog.show(target, situacoes);
                return;
            }
        }

        var div = $(target).closest('ck-tooth');
        Odontogram.message(div, "Não existe nenhum tratamento/situação configurado para este dente");
    },

    registerWicketComponent: function(markupid, url) {
        var wicketComponent = new WicketComponent(markupid, url);
        Odontogram.wicketComponents.push(wicketComponent);
    },

    post: function(callBackURL, object, extras) {
        Odontogram.lock();

        var attrs = Object.assign({ json: JSON.stringify(object) }, extras);
        Wicket.Ajax.post({
            'u' : callBackURL,
            'ep': attrs
        });
    },

    setEnable: function(enable) {
        Odontogram.enable = enable;
    },

    setEnableArch: function(arch) {
        var enable = true;

        var protese = false;
        var situacaoProtese;
        if (!Odontogram.enable) {
            enable = false;
        } else {
            if ('upper' === arch) {
                if (Odontogram.getOdontogramaDTO().proteseSuperior) {
                    enable = false;
                    protese = true;
                    situacaoProtese = Odontogram.getOdontogramaDTO().situacaoProteseSuperior;
                }
            } else {
                if (Odontogram.getOdontogramaDTO().proteseInferior) {
                    enable = false;
                    protese = true;
                    situacaoProtese = Odontogram.getOdontogramaDTO().situacaoProteseInferior;
                }
            }
        }

        var DIVs = $("ck-tooth[" + arch + "]").get();
        DIVs.forEach(function(div) {
            if (enable) {
                $(div).removeAttr('disabled');
                $(div).removeAttr('status');

                var id = $(div).attr('tooth-id');
                var odontogramaTratamentoJsonDTO = $.grep(Odontogram.getTratamentos(), function(item) { return item.id == id })[0];
                if (odontogramaTratamentoJsonDTO) {
                    Odontogram.reload(odontogramaTratamentoJsonDTO);
                } else {
                    var svg = $(div).find('ck-svg-tooth');
                    $(svg).removeAttr('data-abbreviation');

                    var colo = $(svg).find('path[id=colo]');
                    var raiz = $(svg).find('path[id=raiz]');
                    var coroa = $(svg).find('path[id=coroa]');

                    var faces = $(div).find('ck-svg-faces > svg');
                    var distal = $(faces).find("path[id=distal]");
                    var mesial = $(faces).find("path[id=mesial]");
                    var oclusal = $(faces).find("rect[id=oclusal]");
                    var palatal = $(faces).find("path[id=palatal]");
                    var vestibular = $(faces).find("path[id=vestibular]");

                    var cor = 'FFFFFF';

                    Odontogram.setColor(colo, cor);
                    Odontogram.setColor(raiz, cor);
                    Odontogram.setColor(coroa, cor);

                    Odontogram.setColor(distal, cor);
                    Odontogram.setColor(mesial, cor);
                    Odontogram.setColor(oclusal, cor);
                    Odontogram.setColor(palatal, cor);
                    Odontogram.setColor(vestibular, cor);

                    var titleColo = colo.find("title");
                    var titleRaiz = raiz.find("title");
                    var titleCoroa = coroa.find("title");

                    titleColo.empty();
                    titleRaiz.empty();
                    titleCoroa.empty();
                }
            } else {
                $(div).attr('disabled', true);
                $(div).attr('status', 'off');

                var svg = $(div).find('ck-svg-tooth');
                var cor = 'D3D3D3';
                if (protese) {
                    if (situacaoProtese) {
                        var ref = situacaoProtese.referencia;
                        $(svg).attr('data-abbreviation', ref.substring(0, 5));
                        if (situacaoProtese.cor) {
                            cor = situacaoProtese.cor;
                        }
                    } else {
                        $(svg).attr('data-abbreviation', 'N/I');
                        cor = 'F5F5DC';
                    }
                } else {
                    $(svg).removeAttr('data-abbreviation');
                }

                var colo = $(svg).find('path[id=colo]');
                var raiz = $(svg).find('path[id=raiz]');
                var coroa = $(svg).find('path[id=coroa]');

                var faces = $(div).find('ck-svg-faces > svg');
                var distal = $(faces).find("path[id=distal]");
                var mesial = $(faces).find("path[id=mesial]");
                var oclusal = $(faces).find("rect[id=oclusal]");
                var palatal = $(faces).find("path[id=palatal]");
                var vestibular = $(faces).find("path[id=vestibular]");

                Odontogram.setColor(colo, cor);
                Odontogram.setColor(raiz, cor);
                Odontogram.setColor(coroa, cor);

                Odontogram.setColor(distal, cor);
                Odontogram.setColor(mesial, cor);
                Odontogram.setColor(oclusal, cor);
                Odontogram.setColor(palatal, cor);
                Odontogram.setColor(vestibular, cor);

                var titleColo = colo.find("title");
                var titleRaiz = raiz.find("title");
                var titleCoroa = coroa.find("title");

                titleColo.empty();
                titleRaiz.empty();
                titleCoroa.empty();
            }
        });
    },

    showMessage: function(id, message) { // Dispara mensagem de sucesso após adicionar tratamento/situação no dente.
        var div = $('ck-tooth[tooth-id=' + id + ']');
        Odontogram.message(div, message);
    },

    setJSON: function(json) {
        if (json) {
            Odontogram.odontogramaJsonDTO = JSON.parse(json);
        } else if (Odontogram.odontogramaJsonDTO) {
            delete Odontogram.odontogramaJsonDTO;
        }
    },

    setTratamentoUrgente(tratamentoUrgente) {
        Odontogram.codigoFicha = null;
        Odontogram.tratamentoUrgente = tratamentoUrgente;
    },

    setFicha(codigoFicha) {
        Odontogram.codigoFicha = codigoFicha;
    },

    refresh: function(callback) {
        Odontogram.setEnableArch('upper');
        Odontogram.setEnableArch('lower');

        if (callback) {
            callback();
        }
    },

    reload: function(odontogramaTratamentoJsonDTO, callback) {
        var id = odontogramaTratamentoJsonDTO.id;

        var svg = $("svg[name=" + id + "]");
        var div = $(svg).closest('ck-tooth');
//        $(div).attr('status', 'pending');

        if (odontogramaTratamentoJsonDTO.inativo && odontogramaTratamentoJsonDTO.status) {
            $(div).attr('disabled', true);

            var situacao = odontogramaTratamentoJsonDTO.status;

            var linkReverter = div.find('a[id=reverter]');
            var linkNovaSituacao = div.find('a[id=nova]');

            if (situacao.concluido) {
                linkReverter.attr('visible', false);
                linkNovaSituacao.attr('visible', true);
            } else {
                if (situacao.codigoAtendimento && situacao.codigoAtendimento === Odontogram.codigoAtendimento) {
                    linkReverter.attr('visible', true);
                    linkNovaSituacao.attr('visible', false);
                } else {
                    linkReverter.attr('visible', false);
                    linkNovaSituacao.attr('visible', true);
                }
            }

            Odontogram.setDataAbbreviation(svg, situacao.referencia);

            var cor = situacao.cor;
            if (!cor) {
                cor = 'D3D3D3';
            }
            if (cor) {
                var colo = $(svg).find('path[id=colo]');
                var raiz = $(svg).find('path[id=raiz]');
                var coroa = $(svg).find('path[id=coroa]');

                var faces = div.find('ck-svg-faces > svg');
                var distal = $(faces).find("path[id=distal]");
                var mesial = $(faces).find("path[id=mesial]");
                var oclusal = $(faces).find("rect[id=oclusal]");
                var palatal = $(faces).find("path[id=palatal]");
                var vestibular = $(faces).find("path[id=vestibular]");

                Odontogram.setColor(colo, cor);
                Odontogram.setColor(raiz, cor);
                Odontogram.setColor(coroa, cor);

                Odontogram.setColor(distal, cor);
                Odontogram.setColor(mesial, cor);
                Odontogram.setColor(oclusal, cor);
                Odontogram.setColor(palatal, cor);
                Odontogram.setColor(vestibular, cor);

                var titleColo = colo.find("title");
                var titleRaiz = raiz.find("title");
                var titleCoroa = coroa.find("title");

                titleColo.empty();
                titleColo.append(situacao.descricao);

                titleRaiz.empty();
                titleRaiz.append(situacao.descricao);

                titleCoroa.empty();
                titleCoroa.append(situacao.descricao);
            }
        } else {
            var linkReverter = div.find('a[id=reverter]');
            var linkNovaSituacao = div.find('a[id=nova]');

            linkReverter.attr('visible', false);
            linkNovaSituacao.attr('visible', false);

            $(div).removeClass('off');
            $(div).removeAttr('disabled');

            var situacaoCoroa = odontogramaTratamentoJsonDTO.coroa;
            var coroa = $(svg).find('path[id=coroa]');
            coroa.css('fill', '#ffffff');
            if (situacaoCoroa) {
                Odontogram.setDataAbbreviation(svg, situacaoCoroa.referencia);
                var title = coroa.find("title");
                title.empty();
                title.append(situacaoCoroa.descricao);
            } else {
                var svgTooth = $(svg).closest('ck-svg-tooth')
                $(svgTooth).removeAttr('data-abbreviation');
                var title = coroa.find("title");
                title.empty();
            }

            var situacaoColo = odontogramaTratamentoJsonDTO.colo;
            var colo = $(svg).find('path[id=colo]');
            if (situacaoColo) {
                if (situacaoColo.concluido) {
                    colo.css('fill', 'rgba(91, 192, 222, 1)');
                } else {
                    Odontogram.setColor(colo, situacaoColo.cor);
                }
                var title = colo.find("title");
                title.empty();
                title.append(situacaoColo.descricao);
            } else {
                colo.find("title").empty();
                colo.css('fill', '#ffffff');
            }

            var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
            var raiz = $(svg).find('path[id=raiz]');
            if (situacaoRaiz) {
                if (situacaoRaiz.concluido) {
                    raiz.css('fill', 'rgba(91, 192, 222, 1)');
                } else {
                    Odontogram.setColor(raiz, situacaoRaiz.cor);
                }
                var title = raiz.find("title");
                title.empty();
                title.append(situacaoRaiz.descricao);
            } else {
                raiz.find("title").empty();
                raiz.css('fill', '#ffffff');
            }

            var faces = odontogramaTratamentoJsonDTO.faces;
            var divFaces = $(div).find('ck-svg-faces > svg');
            if (faces) {
                var reloadFace = function(face) {
                    var svgFace = $(divFaces).find("path[id=" + face + "]");
                    if (face === 'oclusal') {
                        svgFace = $(divFaces).find("rect[id=" + face + "]");
                    }

                    var status = faces[face];
                    if (status) {
                        if (status === StatusFaces.PENDENTE) {
                            $(svgFace).css('fill', 'rgba(239, 83, 80, 1)');
                        } else {
                            $(svgFace).css('fill', 'rgba(91, 192, 222, 1)');
                        }
                    } else {
                        $(svgFace).css('fill', '#ffffff');
                    }
                };

                reloadFace('palatal');
                reloadFace('vestibular');
                reloadFace('distal');
                reloadFace('mesial');
                reloadFace('oclusal');
            } else {
                odontogramaTratamentoJsonDTO.faces = {};

                $(divFaces).find("path[id=palatal]").css('fill', '#ffffff');
                $(divFaces).find("path[id=vestibular]").css('fill', '#ffffff');
                $(divFaces).find("path[id=distal]").css('fill', '#ffffff');
                $(divFaces).find("path[id=mesial]").css('fill', '#ffffff');
                $(divFaces).find("rect[id=oclusal]").css('fill', '#ffffff');
            }
        }

        if (callback) {
            callback();
        }
    },

    getOdontogramaDTO: function() {
        if (!Odontogram.odontogramaJsonDTO) {
            Odontogram.odontogramaJsonDTO = {
                proteseSuperior : false,
                proteseInferior : false,
                tratamentos     : []
            };
        }

        return Odontogram.odontogramaJsonDTO;
    },

    getTratamentos: function() {
        var tratamentos = Odontogram.getOdontogramaDTO().tratamentos;
        return tratamentos;
    },

    toJSON: function() {
        var url = Odontogram.callBackURLPanel;
        Odontogram.post(url, Odontogram.getOdontogramaDTO());
    },

};