package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.basico.diagnosticoenfermagem.autocomplete.AutoCompleteConsultaDiagnosticoEnfermagemSae;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoProblemasCondicoesAlergiasDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Ciap;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.GrupoProblemasCondicoes;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlProblemas extends ProntuarioCadastroPanel {

    private Form<NoProblemasCondicoesAlergiasDTO> form;
    private NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap;
    private AutoCompleteConsultaDiagnosticoEnfermagemSae autoCompleteConsultaDiagnosticoEnfermagemSae;
    private DateChooser dataInicial;
    private DateChooser dataFinal;
    private AbstractAjaxButton btnAdicionarGrupoProblemasCondicao;
    private Table tblProblemasCondicao;
    private DropDown dropDownSituacao;
    private List<GrupoProblemasCondicoes> problemasCondicoesList = new ArrayList<>();
    private InputArea txtDescricao;

    public PnlProblemas(String id, NoProblemasCondicoesAlergiasDTO problemasCondicoesList) {
        super(id, "Lista Problemas");
        noProblemasCondicoesAlergiasDTO = problemasCondicoesList;
        init();
    }

    private void init() {
        form= new Form("form" , new CompoundPropertyModel<NoProblemasCondicoesAlergiasDTO>(new NoProblemasCondicoesAlergiasDTO()));

        NoProblemasCondicoesAlergiasDTO proxy = on(NoProblemasCondicoesAlergiasDTO.class);

        form.add(autoCompleteConsultaCiap = (AutoCompleteConsultaCiap) new AutoCompleteConsultaCiap(path(proxy.getGrupoProblemasCondicoes().getCiap())));
        form.add(autoCompleteConsultaCid = (AutoCompleteConsultaCid) new AutoCompleteConsultaCid(path(proxy.getGrupoProblemasCondicoes().getCid())));
        form.add(autoCompleteConsultaDiagnosticoEnfermagemSae = (AutoCompleteConsultaDiagnosticoEnfermagemSae) new AutoCompleteConsultaDiagnosticoEnfermagemSae(path(proxy.getGrupoProblemasCondicoes().getCipe())));
        form.add(dataInicial = (DateChooser) new DateChooser(path(proxy.getGrupoProblemasCondicoes().getDataInicial())));
        form.add(dataFinal = (DateChooser) new DateChooser(path(proxy.getGrupoProblemasCondicoes().getDataFinal())));
        form.add(dropDownSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getGrupoProblemasCondicoes().getSituacao()), GrupoProblemasCondicoes.Situacao.values()));
        form.add(txtDescricao = new InputArea(path(proxy.getGrupoProblemasCondicoes().getDescricao())));

        form.add(btnAdicionarGrupoProblemasCondicao = new AbstractAjaxButton("btnAdicionarGrupoProblemasCondicao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarCondicao(target);
            }
        });

        form.add(tblProblemasCondicao = new Table("tblProblemasCondicao", getColumns(), getCollectionProvider()));
        tblProblemasCondicao.populate();
        tblProblemasCondicao.setScrollY("1800");

        add(form);
        carregarProblemasCondicao(null);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, problemasCondicoesList);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        carregarProblemasCondicao(null);

        add(form);
    }

    private void adicionarCondicao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (autoCompleteConsultaCiap.getComponentValue() == null && autoCompleteConsultaCid.getComponentValue() == null && txtDescricao.getComponentValue() == null && autoCompleteConsultaDiagnosticoEnfermagemSae.getComponentValue() == null){
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_mensagem_cid_ciap_cipe_descricao"));
        }
        validarCondicaoCiap((Ciap) autoCompleteConsultaCiap.getComponentValue(), (Cid) autoCompleteConsultaCid.getComponentValue());

        GrupoProblemasCondicoes grupoProblemasCondicoes = new GrupoProblemasCondicoes();

        grupoProblemasCondicoes.setCiap((Ciap) autoCompleteConsultaCiap.getComponentValue());
        grupoProblemasCondicoes.setCid((Cid) autoCompleteConsultaCid.getComponentValue());
        grupoProblemasCondicoes.setCipe((DiagnosticoEnfermagemSae) autoCompleteConsultaDiagnosticoEnfermagemSae.getComponentValue());
        grupoProblemasCondicoes.setSituacao((Long) dropDownSituacao.getComponentValue());
        grupoProblemasCondicoes.setDataInicial(dataInicial.getComponentValue());
        grupoProblemasCondicoes.setDataFinal(dataFinal.getComponentValue());
        grupoProblemasCondicoes.setUsuarioCadSus(noProblemasCondicoesAlergiasDTO.getUsuarioCadsus());
        grupoProblemasCondicoes.setAtendimento(noProblemasCondicoesAlergiasDTO.getAtendimento());
        grupoProblemasCondicoes.setDescricao((String) txtDescricao.getComponentValue());

        BOFactoryWicket.save(grupoProblemasCondicoes);
        carregarProblemasCondicao(target);
        limparCampos(target);
    }

    private void validarCondicaoCiap(Ciap ciap, Cid cid) throws ValidacaoException, DAOException {

        List<GrupoProblemasCondicoes> grupoProblemasCondicoes = LoadManager.getInstance(GrupoProblemasCondicoes.class)
                .addProperties(new HQLProperties(GrupoProblemasCondicoes.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(GrupoProblemasCondicoes.PROP_USUARIO_CAD_SUS, noProblemasCondicoesAlergiasDTO.getUsuarioCadsus()))
                .start().getList();

        for (GrupoProblemasCondicoes item : grupoProblemasCondicoes) {
            if (item != null && ciap != null && item.getCiap() != null) {
                if (item.getCiap().getCodigo().equals(ciap.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("ciapJaAdicionada"));
                }
            }
        }

        for (GrupoProblemasCondicoes item : grupoProblemasCondicoes) {
            if (item != null && cid != null && item.getCid() != null) {
                if (item.getCid().getCodigo().equals(cid.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("cidJaAdicionada"));
                }
            }
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return form.getModel().getObject().getGrupoProblemasCondicoesList();
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        GrupoProblemasCondicoes proxy = on(GrupoProblemasCondicoes.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("ciap"), proxy.getCiap().getDescricaoVO()));
        columns.add(createColumn(bundle("cid"), proxy.getCid().getDescricaoFormatado()));
        columns.add(createColumn(bundle("cipe"), proxy.getCipe().getDescricaoFormatado()));
        columns.add(createColumn(bundle("dataInicial"), proxy.getDataInicial()));
        columns.add(createColumn(bundle("dataFinal"), proxy.getDataFinal()));
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("situacao"), proxy.getSituacaoFormatada()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<GrupoProblemasCondicoes>() {
            @Override
            public void customizeColumn(GrupoProblemasCondicoes rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<GrupoProblemasCondicoes>() {
                    @Override
                    public void action(AjaxRequestTarget target, GrupoProblemasCondicoes modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblProblemasCondicao, form.getModel().getObject().getGrupoProblemasCondicoesList(), modelObject);
                        BOFactoryWicket.delete(modelObject);
                    }
                });
            }
        };
    }

    private void carregarProblemasCondicao(AjaxRequestTarget target){
        if (noProblemasCondicoesAlergiasDTO != null) {
            List<GrupoProblemasCondicoes> load = LoadManager.getInstance(GrupoProblemasCondicoes.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(GrupoProblemasCondicoes.PROP_USUARIO_CAD_SUS, noProblemasCondicoesAlergiasDTO.getUsuarioCadsus()))
                    .start().getList();

            form.getModel().getObject().setGrupoProblemasCondicoesList(load);
        }
        if (target != null){
            tblProblemasCondicao.update(target);
        }
    }

    public void limparCampos(AjaxRequestTarget target){
        autoCompleteConsultaCid.limpar(target);
        autoCompleteConsultaCiap.limpar(target);
        autoCompleteConsultaDiagnosticoEnfermagemSae.limpar(target);
        dataInicial.limpar(target);
        dataFinal.limpar(target);
        dropDownSituacao.limpar(target);
        txtDescricao.limpar(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, List<GrupoProblemasCondicoes> doencaList) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setObject(NoProblemasCondicoesAlergiasDTO noProblemasCondicoesAlergiasDTO) {
        this.noProblemasCondicoesAlergiasDTO = noProblemasCondicoesAlergiasDTO;
        carregarProblemasCondicao(null);
    }
}