package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoCidNotificavelDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AtendimentoCidNotificavel;
import org.apache.wicket.markup.head.IHeaderResponse;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> Schmoeller
 */
public class CidsNotificaveisTab extends TabPanel<NoHistoricoClinicoDTO> {

    private NoHistoricoClinicoDTO noHistoricoClinicoDTO;
    private List<AtendimentoCidNotificavelDTO> atendimentoCidNotificavelDtoList;
    private Table<AtendimentoCidNotificavel> table;


    public CidsNotificaveisTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        this.noHistoricoClinicoDTO = object;
        init();
    }

    private void init() {
        try {
            atendimentoCidNotificavelDtoList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarAtendimentoCidNotificavel(this.noHistoricoClinicoDTO.getAtendimento().getCodigo(),
                    this.noHistoricoClinicoDTO.getAtendimento().getUsuarioCadsus().getCodigo());
        } catch (DAOException e) {
            Loggable.log.error(e);
        } catch (ValidacaoException e) {
            Loggable.log.error(e);
        }

        add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
    }

    @Override
    public String getTitle() {
        return bundle("cidsNotificaveis");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    private List<ISortableColumn<AtendimentoCidNotificavelDTO>> getColumns() {
        List<ISortableColumn<AtendimentoCidNotificavelDTO>> columns = new ArrayList<ISortableColumn<AtendimentoCidNotificavelDTO>>();
        AtendimentoCidNotificavelDTO proxy = on(AtendimentoCidNotificavelDTO.class);

        ColumnFactory columnFactory = new ColumnFactory(AtendimentoCidNotificavelDTO.class);

        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cid"), path(proxy.getCid().getDescricaoFormatado())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dtAtendimento"), path(proxy.getAtendimento().getDataHoraMinutos())));
//        columns.add(columnFactory.createSortableColumn(BundleManager.getString("preencheuNotificacao"), path(proxy.getAtendimentoCidNotificavel().getFlagPreencheuNotificacaoFormatado())));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return atendimentoCidNotificavelDtoList;
            }
        };
    }

}
