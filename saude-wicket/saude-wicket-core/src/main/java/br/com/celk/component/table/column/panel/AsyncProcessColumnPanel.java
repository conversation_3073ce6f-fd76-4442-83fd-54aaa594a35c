package br.com.celk.component.table.column.panel;

import br.com.celk.component.behavior.DownloadReportBehavior;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class AsyncProcessColumnPanel extends Panel{

    private DownloadReportBehavior downloadReportBehavior;
    private Link btnImprimir;
    private AsyncProcess asyncProcess;
    
    public AsyncProcessColumnPanel(String id, final AsyncProcess asyncProcess) {
        super(id);
        
        this.asyncProcess = asyncProcess;
        
        add(btnImprimir = new Link("btnImprimir") {

            @Override
            public boolean isVisible() {
                return asyncProcess.getStatus().equals(AsyncProcess.STATUS_CONCLUIDO_EXITO);
            }

            @Override
            public void onClick() {
            }
            
        });
        
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        add(downloadReportBehavior = new DownloadReportBehavior());
        try {
            btnImprimir.add(new AttributeModifier("href", downloadReportBehavior.getReportUrl(asyncProcess)));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

}
