package br.com.celk.view.vacinaaplicacao.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgDetalhesVisitaDomiciliar extends Window{

    private PnlDetalhesVisitaDomiciliar pnlDetalhesVisitaDomiciliar;
    private VisitaDomiciliar visitaDomiciliar;

    public DlgDetalhesVisitaDomiciliar(String id, VisitaDomiciliar visitaDomiciliar) {
        super(id);
        this.visitaDomiciliar = visitaDomiciliar;
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        setInitialWidth(1300);
        setInitialHeight(750);
        
        setResizable(false);
        
        setTitle(BundleManager.getString("detalhes"));
        
        setContent(pnlDetalhesVisitaDomiciliar = new PnlDetalhesVisitaDomiciliar(getContentId(), visitaDomiciliar) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, Long codigoVacinaAplicacao) {
        pnlDetalhesVisitaDomiciliar.carregarVisitaDomiciliar(codigoVacinaAplicacao);
        show(target);
    }

}
