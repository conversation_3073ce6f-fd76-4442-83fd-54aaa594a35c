package br.com.celk.component.action.link;

import br.com.celk.system.bundle.BundleManager;

/**
 *
 * <AUTHOR>
 */
public class AlterarItemPregaoActionLink extends AbstractLinkPanel{

    public AlterarItemPregaoActionLink(String id) {
        super(id);
        init();
    }

    private void init() {
        setDirtyForm(true);
    }
    
    @Override
    public String getCustomTitle() {
        return BundleManager.getString("alterarItemPregao");
    }

    @Override
    public String getCustomClass() {
        return "icon cogs";
    }

}
