package br.com.celk.view.atendimento.prontuario.panel.odontograma;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.AutoCompleteConsultaProcedimentoCompetencia;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgInformarCid;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.RegistrarTrabalhoOdontologicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.procedimentocompetencia.interfaces.dto.ProcedimentoCompetenciaDTO;
import br.com.ksisolucoes.bo.prontuario.web.procedimento.dto.QueryConsultaProcedimentosCompativeisDTOParam;
import br.com.ksisolucoes.dao.HQLConvertKeyToProperties;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class EvoluirDentePanel extends ProntuarioCadastroPanel {

    private Long codigoPlanoOdonto;
    private boolean edit;
    private MultiSelectionTableOld<Procedimento> tblSituacaoDenteProcedimento;
    private List<Procedimento> situacaoDenteProcedimentoList;
    private CompoundPropertyModel<RegistrarTrabalhoOdontologicoDTO> model = new CompoundPropertyModel(new RegistrarTrabalhoOdontologicoDTO());
    private Table<AtendimentoItem> tblItens;
    private WebMarkupContainer containerProcedimentoIndividual;
    private CompoundPropertyModel<AtendimentoItem> modelAtendimentoItem;
    private AutoCompleteConsultaProcedimentoCompetencia autoCompleteConsultaProcedimentoCompetencia;
    private DoubleField txtQuantidade;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private boolean cidObrigatorio;
    private DlgInformarCid dlgInformarCid;

    public EvoluirDentePanel(String id, Long codigoPlanoOdonto, boolean edit) {
        super(id, bundle("odontograma"));
        this.codigoPlanoOdonto = codigoPlanoOdonto;
        this.edit = edit;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form", model);

        RegistrarTrabalhoOdontologicoDTO proxy = on(RegistrarTrabalhoOdontologicoDTO.class);

        form.add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getDescricaoLocalFormatado())));

        form.add(new DisabledInputField(path(proxy.getAtendimentoOdontoPlano().getSituacaoDente().getDescricao())));

        form.add(getRadioGroupTratamento(path(proxy.getAtendimentoOdontoPlano().getStatus())));

        form.add(new InputArea(path(proxy.getAtendimentoOdontoExecucao().getDescricaoTrabalho())));

        form.add(tblSituacaoDenteProcedimento = new MultiSelectionTableOld("tblSituacaoDenteProcedimento", getColumnsSituacaoDenteProcedimento(), getCollectionProviderSituacaoDenteProcedimento()));
        tblSituacaoDenteProcedimento.setScrollY("200px");
        tblSituacaoDenteProcedimento.populate();

        form.add(new AbstractAjaxButton("btnAdicionarProcedimento") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProcedimento(target);
            }
        });

        form.add(containerProcedimentoIndividual = new WebMarkupContainer("containerProcedimentoIndividual", modelAtendimentoItem = new CompoundPropertyModel(new AtendimentoItem())));
        AtendimentoItem proxyItem = on(AtendimentoItem.class);
        containerProcedimentoIndividual.add(autoCompleteConsultaProcedimentoCompetencia = new AutoCompleteConsultaProcedimentoCompetencia(path(proxyItem.getProcedimentoCompetencia())));
        autoCompleteConsultaProcedimentoCompetencia.setTipoTabelaProcedimentoList(
                Arrays.asList(TipoTabelaProcedimento.Tipo.PROPRIA.getValue(), getAtendimento().getConvenio().getTipoTabelaProcedimento().getCodigo())
        );
        autoCompleteConsultaProcedimentoCompetencia.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
        if (TipoTabelaProcedimento.Tipo.SIGTAP.getValue().equals(getAtendimento().getConvenio().getTipoTabelaProcedimento().getCodigo())) {
            autoCompleteConsultaProcedimentoCompetencia.setValidarComplexidadeProcedimento(true);
            autoCompleteConsultaProcedimentoCompetencia.setValidarModalidade(true);
            autoCompleteConsultaProcedimentoCompetencia.setValidarProcedimentoServico(true);
            autoCompleteConsultaProcedimentoCompetencia.setValidarNaoFaturaveis(true);
            autoCompleteConsultaProcedimentoCompetencia.setValidarProcedimentoRegistro(Arrays.asList(ProcedimentoRegistroCadastro.BPA_CONSOLIDADO, ProcedimentoRegistroCadastro.BPA_INDIVIDUAL));
            autoCompleteConsultaProcedimentoCompetencia.setProfissional(getAtendimento().getProfissional());
            autoCompleteConsultaProcedimentoCompetencia.setUnidadeAtendimento(getAtendimento().getEmpresa());
        }

        autoCompleteConsultaProcedimentoCompetencia.add(new ConsultaListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoCompetencia object) {
                cidObrigatorio = false;
                if (object != null) {
                    cidObrigatorio = LoadManager.getInstance(ProcedimentoCid.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), object)))
                            .exists();
                }

                resetAutoCompleteConsultaCid(target, cidObrigatorio);
                if (cidObrigatorio) {
                    autoCompleteConsultaCid.setProcedimentoCompetencia(object);
                }
                target.add(autoCompleteConsultaCid);
            }
        });

        autoCompleteConsultaProcedimentoCompetencia.add(new RemoveListener<ProcedimentoCompetencia>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ProcedimentoCompetencia object) {
                cidObrigatorio = false;
                resetAutoCompleteConsultaCid(target, false);
            }
        });

        containerProcedimentoIndividual.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxyItem.getCid())));
        autoCompleteConsultaCid.setEnabled(false);

        containerProcedimentoIndividual.add(txtQuantidade = new DoubleField(path(proxyItem.getQuantidade())));
        containerProcedimentoIndividual.add(new AbstractAjaxButton("btnAdicionarProcedimentoIndividual") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarProcedimentoIndividual(target);
            }
        });

        form.add(tblItens = new Table("tblItens", getColumnsProcedimento(), getCollectionProviderProcedimento()));
        tblItens.populate();

        form.add(new AbstractAjaxButton("btnVoltar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar(target);
            }

        });
        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, true);
            }

        });

        add(form);
        carregarInformacoes(false);
    }

    private RadioButtonGroup getRadioGroupTratamento(String id) {
        RadioButtonGroup radioButtonGroup = new RadioButtonGroup(id);

        radioButtonGroup.add(new AjaxRadio("andamento", new Model(AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value())));
        radioButtonGroup.add(new AjaxRadio("concluido", new Model(AtendimentoOdontoPlano.Status.CONCLUIDO.value())));

        return radioButtonGroup;
    }

    private List<IColumn> getColumnsSituacaoDenteProcedimento() {
        List<IColumn> columns = new ArrayList<IColumn>();

        Procedimento proxy = on(Procedimento.class);
        columns.add(createColumn(bundle("procedimento"), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderSituacaoDenteProcedimento() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return situacaoDenteProcedimentoList;
            }
        };
    }

    private List<IColumn> getColumnsProcedimento() {
        List<IColumn> columns = new ArrayList<IColumn>();

        AtendimentoItem proxy = on(AtendimentoItem.class);
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("procedimento"), proxy.getProcedimentoCompetencia().getId().getProcedimento().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidade()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AtendimentoItem>() {
            @Override
            public void customizeColumn(AtendimentoItem rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtendimentoItem>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoItem atendimentoItem) throws ValidacaoException, DAOException {
                        remover(target, atendimentoItem);
                    }

                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderProcedimento() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return model.getObject().getAtendimentoItemList();
            }
        };
    }

    private void adicionarProcedimento(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (model.getObject().getAtendimentoOdontoPlano().getStatus() == null) {
            throw new ValidacaoException(bundle("informeSituacaoTratamento"));
        }
        if (AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value().equals(model.getObject().getAtendimentoOdontoPlano().getStatus())
                && (model.getObject().getAtendimentoOdontoExecucao() == null || Coalesce.asString(model.getObject().getAtendimentoOdontoExecucao().getDescricaoTrabalho()).length() == 0)) {
            throw new ValidacaoException(bundle("informeEvolucao"));
        }
        
        List<Procedimento> selectedObjects = tblSituacaoDenteProcedimento.getSelectedObjects();

        ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();
        for (Procedimento procedimento : selectedObjects) {
            if (containsProcedimentoList(procedimento)) {
                validacaoProcesso.add(bundle("msgProcedimentoXJaAdicionado", procedimento.getDescricao()));
            }
        }

        if (!validacaoProcesso.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacaoProcesso);
        }

        validarCidProcedimento(target);
    }

    private void validarCidProcedimento(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (tblSituacaoDenteProcedimento.getSelectedObjects().isEmpty()) {
            throw new ValidacaoException(bundle("msgSelecioneAoMenosUmProcedimento"));
        }
        viewDlgInformarCid(target, getProcedimentoCompetencia(tblSituacaoDenteProcedimento.getSelectedObjects().get(0)));
    }

    private void viewDlgInformarCid(AjaxRequestTarget target, ProcedimentoCompetencia procedimentoCompetencia) throws ValidacaoException, DAOException {
        if (dlgInformarCid == null) {
            WindowUtil.addModal(target, this, dlgInformarCid = new DlgInformarCid(WindowUtil.newModalId(this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Cid cid, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException {
                    adicionarProcedimentoCid(procedimentoCompetenciaDTO.getProcedimentoCompetencia(), cid);
                    tblSituacaoDenteProcedimento.getSelectedObjects().remove(procedimentoCompetenciaDTO.getProcedimentoCompetencia().getId().getProcedimento());
                }

                @Override
                public void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tblSituacaoDenteProcedimento.getSelectedObjects())) {
                        validarCidProcedimento(target);
                    } else {
                        tblSituacaoDenteProcedimento.clearSelection(target);
                        tblItens.update(target);
                    }
                }
            });
        }
        if (procedimentoCompetencia != null) {
            boolean existsProcedCid = LoadManager.getInstance(ProcedimentoCid.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(new HQLConvertKeyToProperties(VOUtils.montarPath(ProcedimentoCid.PROP_ID, ProcedimentoCidPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia)))
                    .exists();

            if (existsProcedCid) {
                dlgInformarCid.show(target, new ProcedimentoCompetenciaDTO(procedimentoCompetencia));
            } else {
                adicionarProcedimentoCid(procedimentoCompetencia, null);
                tblSituacaoDenteProcedimento.getSelectedObjects().remove(procedimentoCompetencia.getId().getProcedimento());
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(tblSituacaoDenteProcedimento.getSelectedObjects())) {
                    validarCidProcedimento(target);
                } else {
                    tblSituacaoDenteProcedimento.clearSelection(target);
                    tblItens.update(target);
                }
            }
        }
    }

    private void adicionarProcedimentoCid(ProcedimentoCompetencia procedimentoCompetencia, Cid cid) {
        if (!exists(model.getObject().getAtendimentoItemList(), having(on(AtendimentoItem.class).getProcedimentoCompetencia(), Matchers.equalTo(procedimentoCompetencia)))) {
            AtendimentoItem atendimentoItem = new AtendimentoItem();
            atendimentoItem.setQuantidade(1D);
            atendimentoItem.setProcedimentoCompetencia(procedimentoCompetencia);
            atendimentoItem.setCid(cid);
            model.getObject().getAtendimentoItemList().add(atendimentoItem);
        }
    }

    private void adicionarProcedimentoIndividual(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtendimentoItem atendimentoItem = modelAtendimentoItem.getObject();

        if (validarAdicionarProcedimentoIndividual(atendimentoItem)) {
            model.getObject().getAtendimentoItemList().add(atendimentoItem);

            modelAtendimentoItem.setObject(new AtendimentoItem());
            autoCompleteConsultaProcedimentoCompetencia.limpar(target);
            resetAutoCompleteConsultaCid(target, false);
            txtQuantidade.limpar(target);
            tblItens.update(target);
        }
        salvar(target, false);
    }

    private boolean validarAdicionarProcedimentoIndividual(AtendimentoItem atendimentoItem) throws ValidacaoException {
        if (atendimentoItem.getProcedimentoCompetencia() == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        }
        if (Coalesce.asDouble(atendimentoItem.getQuantidade()) == 0D) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        } else if (Coalesce.asDouble(atendimentoItem.getQuantidade()) < 1D) {
            throw new ValidacaoException(bundle("msgQuantidadeNaoPodeSerMenorUm"));
        }
        if (containsProcedimentoList(atendimentoItem.getProcedimentoCompetencia().getId().getProcedimento())) {
            throw new ValidacaoException(bundle("msgProcedimentoXJaAdicionado", atendimentoItem.getProcedimentoCompetencia().getId().getProcedimento().getDescricao()));
        }
        if (model.getObject().getAtendimentoOdontoPlano().getStatus() == null) {
            throw new ValidacaoException(bundle("informeSituacaoTratamento"));
        }
        if (AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value().equals(model.getObject().getAtendimentoOdontoPlano().getStatus())
                && (model.getObject().getAtendimentoOdontoExecucao() == null || Coalesce.asString(model.getObject().getAtendimentoOdontoExecucao().getDescricaoTrabalho()).length() == 0)) {
            throw new ValidacaoException(bundle("informeEvolucao"));
        }
        if (autoCompleteConsultaCid.isEnabled() && atendimentoItem.getCid() == null) {
            throw new ValidacaoException(bundle("informeCid"));
        }
        return true;
    }

    private ProcedimentoCompetencia getProcedimentoCompetencia(Procedimento procedimento) throws DAOException, ValidacaoException {
        return AtendimentoHelper.validaProcedimentoCompetencia(procedimento, getAtendimento().getUsuarioCadsus());
    }

    private boolean containsProcedimentoList(Procedimento procedimento) {
        List<AtendimentoItem> atendimentoItemList = model.getObject().getAtendimentoItemList();

        for (AtendimentoItem atendimentoItem : atendimentoItemList) {
            if (atendimentoItem.getProcedimentoCompetencia().getId().getProcedimento().equals(procedimento)) {
                return true;
            }
        }

        return false;
    }

    private void remover(AjaxRequestTarget target, AtendimentoItem atendimentoItem) {
        boolean remove = model.getObject().getAtendimentoItemList().remove(atendimentoItem);

        if (!remove) {
            List<AtendimentoItem> atendimentoItemList = model.getObject().getAtendimentoItemList();
            for (int i = 0; i < atendimentoItemList.size(); i++) {
                AtendimentoItem item = atendimentoItemList.get(i);

                if (item.getProcedimentoCompetencia().equals(atendimentoItem.getProcedimentoCompetencia())) {
                    model.getObject().getAtendimentoItemList().remove(i);
                }
            }
        }

        tblItens.update(target);
    }

    private void salvar(AjaxRequestTarget target, boolean voltar) throws DAOException, ValidacaoException {
        model.getObject().setAtendimento(getAtendimento());
        model.getObject().setProcessoOdontograma(true);
        BOFactoryWicket.getBO(AtendimentoFacade.class).registrarTrabalhoOdontologico(model.getObject());
        carregarInformacoes(true);
        if(voltar){
            voltar(target);            
        }
    }

    private void voltar(AjaxRequestTarget target) {
        AtendimentoOdontoFicha atendimentoOdontoFicha = model.getObject().getAtendimentoOdontoPlano().getAtendimentoOdontoFicha();
        getProntuarioController().changePanel(target, new OdontogramaTratamentoPanel(getProntuarioController().panelId(), bundle("tratamentoOdontologico"), atendimentoOdontoFicha));
    }

    private void carregarInformacoes(boolean carregarAtendimentoExecucao) {
        AtendimentoOdontoPlano atendimentoOdontoPlano = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                .setId(codigoPlanoOdonto)
                .start().getVO();

        // Seta o status para nulo para obrigar o usuário a informar a situação do tratamento (Em andamento ou Concluído)
        atendimentoOdontoPlano.setStatus(null);
        if (atendimentoOdontoPlano.getTipoEvolucao() == null) {
            atendimentoOdontoPlano.setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());
        }

        model.getObject().setAtendimentoOdontoPlano(atendimentoOdontoPlano);
        carregarProcedimento(atendimentoOdontoPlano);

        if (edit || carregarAtendimentoExecucao) {
            carregarAtendimentoExecucao(atendimentoOdontoPlano);
        }
    }

    private void carregarProcedimento(AtendimentoOdontoPlano atendimentoOdontoPlano) {
        try {
            QueryConsultaProcedimentosCompativeisDTOParam param = new QueryConsultaProcedimentosCompativeisDTOParam();

            param.setEmpresa(getAtendimento().getEmpresa());
            param.setSituacaoDente(atendimentoOdontoPlano.getSituacaoDente());
            param.setProfissional(getAtendimento().getProfissional());
            param.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());
            param.setValidarComplexidadeProcedimento(true);
            param.setValidarModalidade(true);
            param.setValidarProcedimentoServico(true);
            param.setValidarNaoFaturaveis(false);
            param.setToFrom(ProcedimentoHelper.getFromSituacaoDenteProcedimento());

            List<ProcedimentoCompetencia> list = BOFactoryWicket.getBO(AtendimentoFacade.class).consultaProcedimentosClassificacao(param);
            
            if(CollectionUtils.isNotNullEmpty(list)){
                if(CollectionUtils.isEmpty(situacaoDenteProcedimentoList)){
                    situacaoDenteProcedimentoList = new ArrayList<Procedimento>();
                }
                for(ProcedimentoCompetencia item : list){                    
                    situacaoDenteProcedimentoList.add(item.getId().getProcedimento());                    
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void carregarAtendimentoExecucao(AtendimentoOdontoPlano atendimentoOdontoPlano) {
        AtendimentoOdontoExecucao atendimentoOdontoExecucao = LoadManager.getInstance(AtendimentoOdontoExecucao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoExecucao.PROP_ATENDIMENTO, getAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoExecucao.PROP_ATENDIMENTO_ODONTO_PLANO, atendimentoOdontoPlano))
                .start().getVO();

        if (atendimentoOdontoExecucao != null) {
            model.getObject().setAtendimentoOdontoExecucao(atendimentoOdontoExecucao);
            carregarAtendimentoItens(atendimentoOdontoExecucao);
        }
    }

    private void carregarAtendimentoItens(AtendimentoOdontoExecucao atendimentoOdontoExecucao) {
        List<AtendimentoItem> atendimentoItensList = LoadManager.getInstance(AtendimentoItem.class)
                .addProperties(new HQLProperties(AtendimentoItem.class).getProperties())
                .addProperty(VOUtils.montarPath(AtendimentoItem.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(AtendimentoItem.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(AtendimentoItem.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(AtendimentoItem.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoItem.PROP_ATENDIMENTO_ODONTO_EXECUCAO, atendimentoOdontoExecucao))
                .start().getList();

        model.getObject().setAtendimentoItemList(atendimentoItensList);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }

    private void resetAutoCompleteConsultaCid(AjaxRequestTarget target, boolean enabled) {
        autoCompleteConsultaCid.limpar(target);
        autoCompleteConsultaCid.setEnabled(enabled);
    }

}
