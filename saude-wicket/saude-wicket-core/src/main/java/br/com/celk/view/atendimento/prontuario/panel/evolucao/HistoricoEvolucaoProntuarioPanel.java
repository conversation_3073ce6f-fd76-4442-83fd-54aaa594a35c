package br.com.celk.view.atendimento.prontuario.panel.evolucao;

import br.com.celk.component.checkbox.CheckBox;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.nodes.EvolucaoProcedimentoNode;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioHistoricoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.interfaces.IVoltarHistoricoAction;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioDTO;
import br.com.ksisolucoes.bo.prontuario.web.evolucao.dto.HistoricoEvolucaoProntuarioDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NodoAtendimentoWeb;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class HistoricoEvolucaoProntuarioPanel extends ProntuarioHistoricoPanel {

    private Form<HistoricoEvolucaoProntuarioDTOParam> form;
    private SelectionTable<HistoricoEvolucaoProntuarioDTO> tblHistoricoEvolucao;
    private WebMarkupContainer containerEvolucao;
    private CompoundPropertyModel<HistoricoEvolucaoProntuarioDTO> modelEvolucao;
    private IVoltarHistoricoAction voltarHistoricoAction;

    public HistoricoEvolucaoProntuarioPanel(String id) {
        super(id, BundleManager.getString("evolucao"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        form = new Form("form", new CompoundPropertyModel<HistoricoEvolucaoProntuarioDTOParam>(new HistoricoEvolucaoProntuarioDTOParam()));
        HistoricoEvolucaoProntuarioDTOParam proxyParam = on(HistoricoEvolucaoProntuarioDTOParam.class);

        form.add(getCbxPeriodo(path(proxyParam.getMeses())));
        form.add(getCbxGrupoAtendimento(path(proxyParam.getGrupoAtendimentoCbo())));
        form.add(getCbxTipoAtendimento(path(proxyParam.getTipoAtendimento())));

        form.add(new CheckBox(path(proxyParam.getMeusAtendimentos())) {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (form.getModelObject().getMeusAtendimentos()) {
                    form.getModelObject().setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
                } else {
                    form.getModelObject().setCodigoProfissional(null);
                }
                procurar(target);
            }
        });

        form.add(tblHistoricoEvolucao = new SelectionTable("tblHistoricoEvolucao", getColumns(), getCollectionProvider()));
        tblHistoricoEvolucao.populate();

        tblHistoricoEvolucao.addSelectionAction(new ISelectionAction<HistoricoEvolucaoProntuarioDTO>() {

            @Override
            public void onSelection(AjaxRequestTarget target, HistoricoEvolucaoProntuarioDTO object) {
                modelEvolucao.setObject(object);
                target.add(containerEvolucao);
            }
        });

        containerEvolucao = new WebMarkupContainer("containerEvolucao", modelEvolucao = new CompoundPropertyModel<HistoricoEvolucaoProntuarioDTO>(new HistoricoEvolucaoProntuarioDTO()));
        containerEvolucao.setOutputMarkupId(true);

        HistoricoEvolucaoProntuarioDTO proxyDto = on(HistoricoEvolucaoProntuarioDTO.class);

        containerEvolucao.add(new DisabledInputField(path(proxyDto.getDescricaoUnidade())));
        containerEvolucao.add(new DisabledInputField(path(proxyDto.getNomeProfissional())));
        containerEvolucao.add(new DisabledInputField(path(proxyDto.getDescricaoCbo())));
        containerEvolucao.add(new DisabledInputField(path(proxyDto.getDescricaoTipoAtendimento())));
        containerEvolucao.add(new DisabledInputField(path(proxyDto.getDataAtendimento())));
        containerEvolucao.add(new DisabledInputField(path(proxyDto.getCodigoCid())));
        containerEvolucao.add(new Label(path(proxyDto.getHtmlEvolucao())).setEscapeModelStrings(false));

        form.add(containerEvolucao);

        add(form);

        form.getModelObject().setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
        form.getModelObject().setMeses(3);
        form.getModelObject().setMeusAtendimentos(false);

        Long permiteHistorico = getPermiteHistoricoEvolucaoAtendimento();
        form.getModelObject().setPermiteHistorico(permiteHistorico);
    }

    private Long getPermiteHistoricoEvolucaoAtendimento() {
        TipoAtendimento tipoAtendimento = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento();
        NodoAtendimentoWeb proxy = on(NodoAtendimentoWeb.class);
        NodoAtendimentoWeb nodoAtendimentoWeb = LoadManager.getInstance(NodoAtendimentoWeb.class)
                .addProperty(NodoAtendimentoWeb.PROP_CODIGO)
                .addProperty(NodoAtendimentoWeb.PROP_PERMITE_HISTORICO)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getClasseNodo()), EvolucaoProcedimentoNode.class.getName()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoAtendimento()), tipoAtendimento))
                .start().getVO();
        return nodoAtendimentoWeb == null ? null : nodoAtendimentoWeb.getPermiteHistorico();
    }

    private DropDown<Integer> getCbxPeriodo(String id) {
        DropDown<Integer> cbxPeriodo = new DropDown<Integer>(id);

        cbxPeriodo.addChoice(3, bundle("ultimos3meses", this));
        cbxPeriodo.addChoice(6, bundle("ultimos6meses", this));
        cbxPeriodo.addChoice(12, bundle("ultimoAno", this));
        cbxPeriodo.addChoice(null, bundle("todos", this));

        cbxPeriodo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxPeriodo;
    }

    private DropDown<GrupoAtendimentoCbo> getCbxGrupoAtendimento(String id) {
        DropDown<GrupoAtendimentoCbo> cbxGrupoAtendimentoCbo = new DropDown<GrupoAtendimentoCbo>(id);
        List<GrupoAtendimentoCbo> grupos = LoadManager.getInstance(GrupoAtendimentoCbo.class)
                .start().getList();

        cbxGrupoAtendimentoCbo.addChoice(null, bundle("todos"));

        for (GrupoAtendimentoCbo grupoAtendimentoCbo : grupos) {
            cbxGrupoAtendimentoCbo.addChoice(grupoAtendimentoCbo, grupoAtendimentoCbo.getDescricao());
        }

        cbxGrupoAtendimentoCbo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxGrupoAtendimentoCbo;
    }

    private DropDown<TipoAtendimento> getCbxTipoAtendimento(String id) {
        DropDown<TipoAtendimento> cbxTipoAtendimento = new DropDown<TipoAtendimento>(id);
        try {
            HistoricoEvolucaoProntuarioDTOParam param = new HistoricoEvolucaoProntuarioDTOParam();
            param.setAtendimentoAtual(getAtendimento());
            param.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            List<TipoAtendimento> consultarTiposAtendimentoDisponiveis = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarTiposAtendimentoDisponiveis(param);

            cbxTipoAtendimento.addChoice(null, bundle("todos"));

            for (TipoAtendimento tipoAtendimento : consultarTiposAtendimentoDisponiveis) {
                cbxTipoAtendimento.addChoice(tipoAtendimento, tipoAtendimento.getDescricao());
            }
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        cbxTipoAtendimento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurar(target);
            }
        });

        return cbxTipoAtendimento;
    }

    private List<IColumn> getColumns() {
        HistoricoEvolucaoProntuarioDTO on = on(HistoricoEvolucaoProntuarioDTO.class);
        List<IColumn> columns = new ArrayList<IColumn>();

        columns.add(new DateTimeColumn<HistoricoEvolucaoProntuarioDTO>(bundle("data"), path(on.getDataAtendimento())).setPattern("dd/MM/yyyy HH:mm:ss"));
        String parametro = null;
        try {
            parametro = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("informacaoHistoricoEvolucaoProntuario");
        } catch (DAOException ex) {
            Logger.getLogger(HistoricoEvolucaoProntuarioPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
        if (parametro.equals("profissional")) {
            columns.add(createColumn(bundle("profissional"), on.getNomeProfissional()));
        } else {
            columns.add(createColumn(bundle("tipoAtendimento"), on.getDescricaoTipoAtendimento()));
        }

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                form.getModelObject().setAtendimentoAtual(getAtendimento());
                return BOFactoryWicket.getBO(AtendimentoFacade.class).consultarHistoricoEvolucaoProntuario(form.getModelObject());
            }
        };
    }

    private void procurar(AjaxRequestTarget target) {
        modelEvolucao.setObject(new HistoricoEvolucaoProntuarioDTO());
        tblHistoricoEvolucao.populate(target);
        target.add(containerEvolucao);
    }

    @Override
    public DefaultProntuarioPanel newProntuarioPanel(String id) {
        return voltarHistoricoAction.getPanelVoltar(id);
    }

    public void setVoltarHistoricoAction(IVoltarHistoricoAction voltarHistoricoAction) {
        this.voltarHistoricoAction = voltarHistoricoAction;
    }

}
