package br.com.celk.view.atendimento.prontuario.panel;


import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.FormularioPopulacaoExpostaAgrotoxicoRastreio;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.Component;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.Iterator;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroFormularioPopulacaoExpostaAgrotoxicoRastreioPanel extends ProntuarioCadastroPanel {

    private Form<FormularioPopulacaoExpostaAgrotoxicoRastreio> form;
    private FormularioPopulacaoExpostaAgrotoxicoRastreio formularioPopulacaoExpostaAgrotoxicoRastreio;
    private boolean consulta;

    private InputArea txaObservacao;
    private Button btnVoltar;

    private WebMarkupContainer containerConsulta;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Profissional profissional;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;
    private TabelaCbo tabelaCbos;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa estabelecimento;
    private Date dataCadastro;
    private String formulario;

    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private WebMarkupContainer containerHistoriaAnterior;
    private InputField outraAtividadeField;
    private InputField outrasAtividadesAtualField;
    private InputField outrasFormasContatoAtualField;
    private InputField outrosSintomasField;
    private WebMarkupContainer containerHistoriaAtual;


    public CadastroFormularioPopulacaoExpostaAgrotoxicoRastreioPanel(String id, FormularioPopulacaoExpostaAgrotoxicoRastreio formularioPopulacaoExpostaAgrotoxicoRastreio, boolean consulta) {
        super(id, bundle("estratificacaoRiscoPopulacaoExpostaAgrotoxicoRastreio"));
        this.formularioPopulacaoExpostaAgrotoxicoRastreio = formularioPopulacaoExpostaAgrotoxicoRastreio;
        this.consulta = consulta;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        FormularioPopulacaoExpostaAgrotoxicoRastreio proxy = on(FormularioPopulacaoExpostaAgrotoxicoRastreio.class);

        EnderecoUsuarioCadsus enderecoUsuarioCadsus = getEnderecoUsuarioCadsus();
        getAtendimento().getUsuarioCadsus().setEnderecoUsuarioCadsus(enderecoUsuarioCadsus);

        getEstratificacaoRisco().setAtendimento(getAtendimento());
        addCamposIdentificacao(proxy);
        addCamposHistoriaAnterior(proxy);
        addCamposHisotriaAtual(proxy);
        getForm().add(txaObservacao = new InputArea(path(proxy.getObservacao())));
        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });
        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (!consulta) {
                    viewDlgSimNao(target);
                } else {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }
            }
        }.setDefaultFormProcessing(false));

        getForm().add(containerConsulta = new WebMarkupContainer("containerConsulta"));
        containerConsulta.setOutputMarkupPlaceholderTag(true);
        containerConsulta.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel(this, "profissional")));
        containerConsulta.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo("tabelaCbos", new PropertyModel(this, "tabelaCbos")));
        containerConsulta.add(new DateChooser("dataCadastro", new PropertyModel(this, "dataCadastro")));
        containerConsulta.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("estabelecimento", new PropertyModel(this, "estabelecimento")));
        containerConsulta.add(new InputField("formulario", new PropertyModel(this, "formulario")));


        if (consulta) {
            profissional = formularioPopulacaoExpostaAgrotoxicoRastreio.getEstratificacaoRisco().getProfissional();
            tabelaCbos = formularioPopulacaoExpostaAgrotoxicoRastreio.getEstratificacaoRisco().getCbo();
            estabelecimento = formularioPopulacaoExpostaAgrotoxicoRastreio.getEstratificacaoRisco().getEmpresa();
            dataCadastro = formularioPopulacaoExpostaAgrotoxicoRastreio.getEstratificacaoRisco().getDataCadastro();
            formulario = formularioPopulacaoExpostaAgrotoxicoRastreio.getEstratificacaoRisco().getDescricaoFormulario();
            disableFields(getForm());
            containerConsulta.setVisible(true);
        }else{
            containerConsulta.setVisible(false);
        }

        add(getForm());
    }

    private EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return LoadManager.getInstance(EnderecoUsuarioCadsus.class)
                          .addParameter(new QueryCustom.QueryCustomParameter(EnderecoUsuarioCadsus.PROP_CODIGO, getAtendimento().getUsuarioCadsus().getEnderecoUsuarioCadsus().getCodigo()))
                          .start()
                          .getVO();
    }

    private class HistoriaAtualBehavior extends AjaxEventBehavior{

        public HistoriaAtualBehavior() {
            super("onchange");
        }

        @Override
        protected void onEvent(AjaxRequestTarget ajaxRequestTarget) {
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getTemContatoAgrotoxico())){
                containerHistoriaAtual.setEnabled(true);
            }else{
                containerHistoriaAtual.setEnabled(false);
            }
            ajaxRequestTarget.add(containerHistoriaAtual);
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(getForm().getModel().getObject().getAtividadeAtualOutros())){
                outrasAtividadesAtualField.setEnabled(true);
            }else {
                outrasAtividadesAtualField.setEnabled(false);
                outrasAtividadesAtualField.limpar(ajaxRequestTarget);
            }
            ajaxRequestTarget.add(outrasAtividadesAtualField);
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(getForm().getModel().getObject().getFormaContatoAtualOutras())){
                outrasFormasContatoAtualField.setEnabled(true);
            }else {
                outrasFormasContatoAtualField.setEnabled(false);
                outrasFormasContatoAtualField.limpar(ajaxRequestTarget);
            }
            ajaxRequestTarget.add(outrasFormasContatoAtualField);
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(getForm().getModel().getObject().getSintomasOutros())){
                outrosSintomasField.setEnabled(true);
            }else {
                outrosSintomasField.setEnabled(false);
                outrosSintomasField.limpar(ajaxRequestTarget);
            }
            ajaxRequestTarget.add(outrosSintomasField);
        }
    }

    private void addCamposHisotriaAtual(FormularioPopulacaoExpostaAgrotoxicoRastreio proxy) {
        containerHistoriaAtual = new WebMarkupContainer("historiaAtual");
        containerHistoriaAtual.setOutputMarkupId(true);
        getForm().add(containerHistoriaAtual);
        addDropDown(path(proxy.getTemContatoAgrotoxico()), getForm(), new HistoriaAtualBehavior());
        containerHistoriaAtual.add(DropDownUtil.getIEnumDropDown(path(proxy.getContatoPorAtual()), FormularioPopulacaoExpostaAgrotoxicoRastreio.ContatoPor.values(), true, false));
        containerHistoriaAtual.add(new LongField(path(proxy.getTempoExposicaoAtual())));
        containerHistoriaAtual.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoTempoExposicaoAtual()), FormularioPopulacaoExpostaAgrotoxicoRastreio.UnidadeTempoExposicao.values(), true, false));
        containerHistoriaAtual.add(new DateChooser(path(proxy.getDataUltimoContato())));
        containerHistoriaAtual.add(new InputField(path(proxy.getAgrotoxicosTemContato())));
        addDropDown(path(proxy.getAtividadeAtualAgricultura()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualPecuaria()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualIndustria()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualDesinsetizacao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualAgenteEndemias()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualUsoDomestico()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getAtividadeAtualOutros()), containerHistoriaAtual, new HistoriaAtualBehavior());
        containerHistoriaAtual.add(outrasAtividadesAtualField = new InputField(path(proxy.getOutrasAtividadesAtual())));
        addDropDown(path(proxy.getFormaContatoAtualPreparo()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualDiluicao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualTratamentoSementes()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualAplicacao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualColheita()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualSupervisao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualArmazenamento()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualDescarteEmbalagem()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualLimpezaEquipamento()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualLavagemRoupa()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualCarga()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualTransporte()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualControleExpedicao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualProducao()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualContaminacaoAmbiental()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getFormaContatoAtualOutras()), containerHistoriaAtual, new HistoriaAtualBehavior());
        containerHistoriaAtual.add(outrasFormasContatoAtualField = new InputField(path(proxy.getOutrasFormasContatoAtual())));
        containerHistoriaAtual.add(new LongField(path(proxy.getNroIntoxicacoes())));
        addDropDown(path(proxy.getSintomasGastrointestinais()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasSensorio()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasPele()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasCardiovascular()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasRespiratoria()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasNaoLembra()), containerHistoriaAtual, null);
        addDropDown(path(proxy.getSintomasOutros()), containerHistoriaAtual, new HistoriaAtualBehavior());
        containerHistoriaAtual.add(outrosSintomasField = new InputField(path(proxy.getOutrosSintomas())));
        addDropDown(path(proxy.getTemAgrotoxicoUnidade()), containerHistoriaAtual, null);
        containerHistoriaAtual.setEnabled(false);
        if(formularioPopulacaoExpostaAgrotoxicoRastreio != null){
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getTemContatoAgrotoxico())){
                containerHistoriaAtual.setEnabled(true);
            }
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getAtividadeAtualOutros())){
                outrasAtividadesAtualField.setEnabled(true);
            }else{
                outrasAtividadesAtualField.setEnabled(false);
            }
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getFormaContatoAtualOutras())){
                outrasFormasContatoAtualField.setEnabled(true);
            }else{
                outrasFormasContatoAtualField.setEnabled(false);
            }
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getSintomasOutros())){
                outrosSintomasField.setEnabled(true);
            }else{
                outrosSintomasField.setEnabled(false);
            }
        }
    }

    private class HistoriaAnteriorBehavior extends AjaxEventBehavior{

        public HistoriaAnteriorBehavior() {
            super("onchange");
        }

        @Override
        protected void onEvent(AjaxRequestTarget ajaxRequestTarget) {
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(getForm().getModel().getObject().getContatoAgrotoxico())){
                containerHistoriaAnterior.setEnabled(true);
            }else {
                containerHistoriaAnterior.setEnabled(false);
            }
            ajaxRequestTarget.add(containerHistoriaAnterior);

            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(getForm().getModel().getObject().getAtividadeOutras())){
                outraAtividadeField.setEnabled(true);
            }else {
                outraAtividadeField.setEnabled(false);
                outraAtividadeField.limpar(ajaxRequestTarget);
            }
            ajaxRequestTarget.add(outraAtividadeField);
        }
    }

    private void addCamposHistoriaAnterior(FormularioPopulacaoExpostaAgrotoxicoRastreio proxy) {
        containerHistoriaAnterior = new WebMarkupContainer("historiaAnterior");
        containerHistoriaAnterior.setOutputMarkupId(true);
        getForm().add(containerHistoriaAnterior);
        addDropDown(path(proxy.getContatoAgrotoxico()), null, new HistoriaAnteriorBehavior());
        containerHistoriaAnterior.add(DropDownUtil.getIEnumDropDown(path(proxy.getContatoPor()), FormularioPopulacaoExpostaAgrotoxicoRastreio.ContatoPor.values(), true, false));
        addDropDown(path(proxy.getAtividadeAgricultura()), containerHistoriaAnterior, null);
        addDropDown(path(proxy.getAtividadePecuaria()), containerHistoriaAnterior, null);
        addDropDown(path(proxy.getAtividadeAvicultura()), containerHistoriaAnterior, null);
        addDropDown(path(proxy.getAtividadePiscicultura()), containerHistoriaAnterior, null);
        addDropDown(path(proxy.getAtividadeOutras()), containerHistoriaAnterior, new HistoriaAnteriorBehavior());
        containerHistoriaAnterior.add(outraAtividadeField = new InputField(path(proxy.getOutraAtividade())));
        containerHistoriaAnterior.add(new InputField(path(proxy.getNomeAgrotoxico())));
        containerHistoriaAnterior.add(new LongField(path(proxy.getTempoExposicao())));
        containerHistoriaAnterior.add(DropDownUtil.getIEnumDropDown(path(proxy.getUnidadeTempoExposicao()), FormularioPopulacaoExpostaAgrotoxicoRastreio.UnidadeTempoExposicao.values(), true, false));
        containerHistoriaAnterior.setEnabled(false);
        if(formularioPopulacaoExpostaAgrotoxicoRastreio != null){
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getContatoAgrotoxico())){
                containerHistoriaAnterior.setEnabled(true);
            }
            if(RepositoryComponentDefault.SimNaoLong.SIM.value().equals(formularioPopulacaoExpostaAgrotoxicoRastreio.getAtividadeOutras())){
                outraAtividadeField.setEnabled(true);
            }else{
                outraAtividadeField.setEnabled(false);
            }
        }
    }

    private void addCamposIdentificacao(FormularioPopulacaoExpostaAgrotoxicoRastreio proxy) {
        getForm().add(new DisabledInputField(path(proxy.getEstratificacaoRisco().getAtendimento().getUsuarioCadsus().getNome())));
        getForm().add(new DisabledInputField(path(proxy.getEstratificacaoRisco().getAtendimento().getUsuarioCadsus().getIdade())));
        getForm().add(new DisabledInputField(path(proxy.getEstratificacaoRisco().getAtendimento().getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getEstratificacaoRisco().getAtendimento().getUsuarioCadsus().getSexoFormatado())));
        getForm().add(new DisabledInputField(path(proxy.getEstratificacaoRisco().getAtendimento().getUsuarioCadsus().getTelefoneFormatado())));
        addDropDown(path(proxy.getZonaRural()), null, null);
        DropDown<Long> dropGestante = DropDownUtil.getSimNaoLongDropDown(path(proxy.getGestante()), true, false);
        boolean mulherEntre9e60 = UsuarioCadsus.SEXO_FEMININO.equalsIgnoreCase(getAtendimento().getUsuarioCadsus().getSexo())
                && getAtendimento().getUsuarioCadsus().getIdade() >= 9 && getAtendimento().getUsuarioCadsus().getIdade() <= 60;
        if(!mulherEntre9e60){
            dropGestante.setEnabled(false);
        }
        getForm().add(dropGestante);
        getForm().add(new InputField(path(proxy.getOcupacao())));
    }

    private void addDropDown(String path, WebMarkupContainer container, AjaxEventBehavior behavior){
        DropDown<Long> drop = DropDownUtil.getSimNaoLongDropDown(path, true, false);
        drop.addAjaxUpdateValue();
        if(behavior != null)
            drop.add(behavior);
        if(container != null) {
            container.add(drop);
        }else
            getForm().add(drop);

    }

    private void disableFields(MarkupContainer parent) {
        Iterator<Component> iterator = parent.iterator();
        while (iterator.hasNext()) {
            Component next = iterator.next();
            next.setEnabled(false);
            btnVoltar.setEnabled(true);
        }
    }


    public void viewDlgSimNao(AjaxRequestTarget target) {
        if (dlgConfirmacaoSimNao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgAsInformacoesDoFormularioNaoSeraoSalvas")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    EstratificacaoRiscoPanel estratificacaoRiscoPanel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
                    getProntuarioController().changePanel(target, estratificacaoRiscoPanel);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgConfirmacaoSimNao.show(target);
    }


    private Form<FormularioPopulacaoExpostaAgrotoxicoRastreio> getForm() {
        if (this.form == null) {
            if (this.formularioPopulacaoExpostaAgrotoxicoRastreio == null) {
                this.formularioPopulacaoExpostaAgrotoxicoRastreio = new FormularioPopulacaoExpostaAgrotoxicoRastreio();
            }
            this.form = new Form<>("form", new CompoundPropertyModel<>(formularioPopulacaoExpostaAgrotoxicoRastreio));
        }
        return this.form;
    }

    private EstratificacaoRisco getEstratificacaoRisco(){
        if(getForm().getModel().getObject().getEstratificacaoRisco() == null){
            getForm().getModel().getObject().setEstratificacaoRisco(new EstratificacaoRisco());
        }
        return getForm().getModel().getObject().getEstratificacaoRisco();
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        EstratificacaoRisco estratificacaoRisco = getEstratificacaoRisco();
        if (estratificacaoRisco == null) {
            estratificacaoRisco = new EstratificacaoRisco();
        }
        estratificacaoRisco.setAtendimento(getAtendimento());
        estratificacaoRisco.setCbo(getAtendimento().getTabelaCbo());
        estratificacaoRisco.setEmpresa(getAtendimento().getEmpresa());
        estratificacaoRisco.setFormulario(ConfiguracaoEstratificacao.Formulario.POPULACOES_EXPOSTA_AGROTOXICO_RASTREIO.value());
        estratificacaoRisco.setProfissional(getAtendimento().getProfissional());
        estratificacaoRisco.setFlagClassificacaoRisco(EstratificacaoRisco.Risco.CINZA.value());
        estratificacaoRisco = BOFactoryWicket.save(estratificacaoRisco);
        FormularioPopulacaoExpostaAgrotoxicoRastreio formularioPopulacaoExpostaAgrotoxicoRastreio = getForm().getModel().getObject();
        formularioPopulacaoExpostaAgrotoxicoRastreio.setEstratificacaoRisco(estratificacaoRisco);
        BOFactoryWicket.save(formularioPopulacaoExpostaAgrotoxicoRastreio);

        EstratificacaoRiscoPanel panel = new EstratificacaoRiscoPanel(getProntuarioController().panelId());
        getProntuarioController().changePanel(target, panel);
    }

}