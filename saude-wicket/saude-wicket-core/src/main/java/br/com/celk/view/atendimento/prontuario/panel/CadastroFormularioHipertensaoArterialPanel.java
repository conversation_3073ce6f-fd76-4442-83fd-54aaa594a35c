package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.resources.Icon32;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;
import br.com.ksisolucoes.vo.prontuario.basico.FormularioHipertensaoArterial;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.Arrays;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroFormularioHipertensaoArterialPanel extends ProntuarioCadastroPanel {

    private Form<FormularioHipertensaoArterial> form;
    private IModel<FormularioHipertensaoArterial> model;
    private FormularioHipertensaoArterial formularioHipertensaoArterial;

    private WebMarkupContainer containerFatoresRisco;
    private WebMarkupContainer containerLesaoOrgaoVivo;
    private WebMarkupContainer containerDoencaCardiovascularDoencaRenal;

    private DropDown dropDownSexo;
    private DropDown dropDownIdade;
    private DropDown dropDownTabagismo;
    private DropDown dropDownDoencaCardioPrematura;
    private DropDown dropDownDislipidemia;
    private DropDown dropDownResistenciaInsulina;
    private DropDown dropDownObesidade;

    private DropDown dropDownHipertrofiaVentricularEsquerda;
    private DropDown dropDownEspessuraMediointimalCarotidaPlacaCarotidea;
    private DropDown dropDownVelocidadeOndePulsoCarotidoFemoral;
    private DropDown dropDownIndiceTornozeloBraquial;
    private DropDown dropDownDoencaRenalCronica;
    private DropDown dropDownAlbuminuria;

    private DropDown dropDownDoencaCerebrovascular;
    private DropDown dropDownDoencaArteriaCoronaria;
    private DropDown dropDownDiabete;

    private LongField txtPas;
    private LongField txtPad;

    private WebMarkupContainer containerScore;
    private AttributeModifier modifierScore;
    private Label lblDescricaoRisco;
    private Label lblScore;

    private Long codigoEstratificacao;
    private boolean viewOnly;

    private String CSS_FILE = "CadastroFormularioHipertensaoArterialPanel.css";

    public CadastroFormularioHipertensaoArterialPanel(String id) {
        this(id, null, false);
    }

    public CadastroFormularioHipertensaoArterialPanel(String id, Long codigoEstratificacao, boolean viewOnly) {
        super(id, bundle("formularioHipertensaoArterial"));
        this.codigoEstratificacao = codigoEstratificacao;
        this.viewOnly = viewOnly;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        FormularioHipertensaoArterial proxy = on(FormularioHipertensaoArterial.class);

        addGrupoFatorRisco(proxy);
        addGrupoLesaoOrgaoAlvo(proxy);
        addGrupoDoencaCardiovascularDoencaRenal(proxy);
        addGrupoPressaoArterial(proxy);
        addGrupoDiabete(proxy);
        addGrupoScore();

        getForm().add(new InputArea(path(proxy.getObservacao())));

        AbstractAjaxButton btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                voltar(target);
            }
        };
        btnVoltar.setDefaultFormProcessing(false);
        getForm().add(btnVoltar);

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        if (viewOnly) {
            ComponentUtils.disableComponentsContainer(getForm());
            btnVoltar.setEnabled(true);
        }

        add(getForm());
    }

    private void addGrupoFatorRisco(FormularioHipertensaoArterial proxy) {
        getForm().add(containerFatoresRisco = new WebMarkupContainer("containerFatoresRisco"));
        containerFatoresRisco.setOutputMarkupId(true);

        containerFatoresRisco.add(dropDownSexo = DropDownUtil.getNaoSimLongDropDown(path(proxy.getSexoMasculino())));
        containerFatoresRisco.add(dropDownIdade = DropDownUtil.getNaoSimLongDropDown(path(proxy.getIdade())));
        containerFatoresRisco.add(dropDownTabagismo = DropDownUtil.getNaoSimLongDropDown(path(proxy.getTabagismo())));
        containerFatoresRisco.add(dropDownDoencaCardioPrematura = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDoencaCardioPrematura())));
        containerFatoresRisco.add(dropDownDislipidemia = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDislipidemia())));
        containerFatoresRisco.add(dropDownResistenciaInsulina = DropDownUtil.getNaoSimLongDropDown(path(proxy.getResistenciaInsulina())));
        containerFatoresRisco.add(dropDownObesidade = DropDownUtil.getNaoSimLongDropDown(path(proxy.getObesidade())));

        dropDownSexo.add(getBehavior());
        dropDownIdade.add(getBehavior());
        dropDownTabagismo.add(getBehavior());
        dropDownDoencaCardioPrematura.add(getBehavior());
        dropDownDislipidemia.add(getBehavior());
        dropDownResistenciaInsulina.add(getBehavior());
        dropDownObesidade.add(getBehavior());

        if (UsuarioCadsus.SEXO_MASCULINO.equals(getAtendimento().getUsuarioCadsus().getSexo())) {
            dropDownSexo.setComponentValue(RepositoryComponentDefault.SIM_LONG);

            if (getAtendimento().getUsuarioCadsus().getIdade() >= 55) {
                dropDownIdade.setComponentValue(RepositoryComponentDefault.SIM_LONG);
            }
        } else if (getAtendimento().getUsuarioCadsus().getIdade() >= 65) {
            dropDownIdade.setComponentValue(RepositoryComponentDefault.SIM_LONG);
        }
    }

    private void addGrupoLesaoOrgaoAlvo(FormularioHipertensaoArterial proxy) {
        getForm().add(containerLesaoOrgaoVivo = new WebMarkupContainer("containerLesaoOrgaoVivo"));
        containerLesaoOrgaoVivo.setOutputMarkupId(true);

        containerLesaoOrgaoVivo.add(dropDownHipertrofiaVentricularEsquerda = DropDownUtil.getNaoSimLongDropDown(path(proxy.getHipertrofiaVentricularEsquerda())));
        containerLesaoOrgaoVivo.add(dropDownEspessuraMediointimalCarotidaPlacaCarotidea = DropDownUtil.getNaoSimLongDropDown(path(proxy.getEspessuraMediointimalCarotidaPlacaCarotidea())));
        containerLesaoOrgaoVivo.add(dropDownVelocidadeOndePulsoCarotidoFemoral = DropDownUtil.getNaoSimLongDropDown(path(proxy.getVelocidadeOndaPulsoCarotidoFemoral())));
        containerLesaoOrgaoVivo.add(dropDownIndiceTornozeloBraquial = DropDownUtil.getNaoSimLongDropDown(path(proxy.getIndiceTornozeloBranquial())));
        containerLesaoOrgaoVivo.add(dropDownDoencaRenalCronica = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDoencaRenalCronica())));
        containerLesaoOrgaoVivo.add(dropDownAlbuminuria = DropDownUtil.getNaoSimLongDropDown(path(proxy.getAlbuminuria())));

        dropDownHipertrofiaVentricularEsquerda.add(getBehavior());
        dropDownEspessuraMediointimalCarotidaPlacaCarotidea.add(getBehavior());
        dropDownVelocidadeOndePulsoCarotidoFemoral.add(getBehavior());
        dropDownIndiceTornozeloBraquial.add(getBehavior());
        dropDownDoencaRenalCronica.add(getBehavior());
        dropDownAlbuminuria.add(getBehavior());
    }

    private void addGrupoDoencaCardiovascularDoencaRenal(FormularioHipertensaoArterial proxy) {
        getForm().add(containerDoencaCardiovascularDoencaRenal = new WebMarkupContainer("containerDoencaCardiovascularDoencaRenal"));
        containerDoencaCardiovascularDoencaRenal.setOutputMarkupId(true);

        containerDoencaCardiovascularDoencaRenal.add(dropDownDoencaCerebrovascular = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDoencaCerebrovascular())));
        containerDoencaCardiovascularDoencaRenal.add(dropDownDoencaArteriaCoronaria = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDoencaArteriaCoronaria())));

        dropDownDoencaCerebrovascular.add(getBehavior());
        dropDownDoencaArteriaCoronaria.add(getBehavior());
    }

    private void addGrupoPressaoArterial(FormularioHipertensaoArterial proxy) {
        getForm().add(txtPas = new LongField(path(proxy.getPas())));
        getForm().add(txtPad = new LongField(path(proxy.getPad())));

        txtPas.addAjaxUpdateValue();
        txtPas.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    Long pas = Coalesce.asLong(txtPas.getModelObject());
                    if (pas > 0 && (pas < 100 || pas > 250)) {
                        txtPas.limpar(target);
                        throw new ValidacaoException("PAS deve ser informado no intervalo entre 100 à 250");
                    }

                    CadastroFormularioHipertensaoArterialPanel.this.onEvent(target);
                } catch (ValidacaoException e) {
                    MessageUtil.modalWarn(target, CadastroFormularioHipertensaoArterialPanel.this, e);
                }
            }
        });

        txtPad.addAjaxUpdateValue();
        txtPad.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    Long pad = Coalesce.asLong(txtPad.getModelObject());
                    if (pad > 0 && (pad < 60 || pad > 190)) {
                        txtPad.limpar(target);
                        throw new ValidacaoException("PAD deve ser informado no intervalo entre 60 à 190");
                    }

                    CadastroFormularioHipertensaoArterialPanel.this.onEvent(target);
                } catch (ValidacaoException e) {
                    MessageUtil.modalWarn(target, CadastroFormularioHipertensaoArterialPanel.this, e);
                }
            }
        });
    }

    private void addGrupoDiabete(FormularioHipertensaoArterial proxy) {
        getForm().add(dropDownDiabete = DropDownUtil.getNaoSimLongDropDown(path(proxy.getDiabete())));

        dropDownDiabete.add(getBehavior());
    }

    private void addGrupoScore() {
        getForm().add(lblDescricaoRisco = new Label("descricaoRisco", new Model()));
        lblDescricaoRisco.setOutputMarkupPlaceholderTag(true);

        getForm().add(lblScore = new Label("descricaoScore", new Model()));
        lblScore.setOutputMarkupPlaceholderTag(true);

        getForm().add(containerScore = new WebMarkupContainer("resultadoScore"));
        containerScore.setOutputMarkupPlaceholderTag(true);

        calcularScore();
        calcularRisco();
    }

    private Behavior getBehavior() {
        return new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                CadastroFormularioHipertensaoArterialPanel.this.onEvent(target);
            }
        };
    }

    private void calcularScore() {
        FormularioHipertensaoArterial formulario = model.getObject();

        long score = 0;

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getSexoMasculino())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getIdade())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getTabagismo())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getDoencaCardioPrematura())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getDislipidemia())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getResistenciaInsulina())) {
            score++;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(formulario.getObesidade())) {
            score++;
        }

        formulario.setScore(score);

        lblScore.setDefaultModel(new Model<>(bundle("scoreTotalXPontos", score)));
    }

    private void calcularRisco() {
        FormularioHipertensaoArterial formulario = model.getObject();

        EstratificacaoRisco.Risco risco;

        if (Arrays.asList(
                formulario.getHipertrofiaVentricularEsquerda(),
                formulario.getEspessuraMediointimalCarotidaPlacaCarotidea(),
                formulario.getVelocidadeOndaPulsoCarotidoFemoral(),
                formulario.getIndiceTornozeloBranquial(),
                formulario.getDoencaRenalCronica(),
                formulario.getAlbuminuria(),
                formulario.getDoencaCerebrovascular(),
                formulario.getDoencaArteriaCoronaria(),
                formulario.getDiabete()).contains(RepositoryComponentDefault.SIM_LONG)) {

            risco = EstratificacaoRisco.Risco.VERMELHO;

        } else {

            Long pas = Coalesce.asLong(formulario.getPas());
            Long pad = Coalesce.asLong(formulario.getPad());

            if (pas >= 180 || pad >= 110) {
                risco = EstratificacaoRisco.Risco.VERMELHO;
            } else {
                risco = EstratificacaoRisco.Risco.CINZA;

                if (pas != 0) {
                    if (pas <= 139) {
                        if (formulario.getScore() == 0) {
                            risco = EstratificacaoRisco.Risco.CINZA;
                        } else if (formulario.getScore() >= 1 && formulario.getScore() <= 2) {
                            risco = EstratificacaoRisco.Risco.VERDE;
                        } else {
                            risco = EstratificacaoRisco.Risco.AMARELO;
                        }
                    } else if (pas <= 159) {
                        if (formulario.getScore() == 0) {
                            risco = EstratificacaoRisco.Risco.VERDE;
                        } else if (formulario.getScore() <= 2) {
                            risco = EstratificacaoRisco.Risco.AMARELO;
                        } else {
                            risco = EstratificacaoRisco.Risco.VERMELHO;
                        }
                    } else if (pas <= 179) {
                        if (formulario.getScore() == 0) {
                            risco = EstratificacaoRisco.Risco.AMARELO;
                        } else {
                            risco = EstratificacaoRisco.Risco.VERMELHO;
                        }
                    }
                }

                if (pad != 0) {
                    EstratificacaoRisco.Risco riscoAux = EstratificacaoRisco.Risco.CINZA;
                    if (pad <= 89) {
                        if (formulario.getScore() == 0) {
                            riscoAux = EstratificacaoRisco.Risco.CINZA;
                        } else if (formulario.getScore() >= 1 && formulario.getScore() <= 2) {
                            riscoAux = EstratificacaoRisco.Risco.VERDE;
                        } else {
                            riscoAux = EstratificacaoRisco.Risco.AMARELO;
                        }
                    } else if (pad <= 99) {
                        if (formulario.getScore() == 0) {
                            riscoAux = EstratificacaoRisco.Risco.VERDE;
                        } else if (formulario.getScore() <= 2) {
                            riscoAux = EstratificacaoRisco.Risco.AMARELO;
                        } else {
                            riscoAux = EstratificacaoRisco.Risco.VERMELHO;
                        }
                    } else if (pad <= 109) {
                        if (formulario.getScore() == 0) {
                            riscoAux = EstratificacaoRisco.Risco.AMARELO;
                        } else {
                            riscoAux = EstratificacaoRisco.Risco.VERMELHO;
                        }
                    }

                    if (riscoAux.value() < risco.value()) {
                        risco = riscoAux;
                    }
                }

            }

        }

        formulario.setFlagRisco(risco.value());

        Icon32 icon;
        if (EstratificacaoRisco.Risco.VERMELHO.equals(risco)) {
            icon = Icon32.ball_red;
        } else if (EstratificacaoRisco.Risco.AMARELO.equals(risco)) {
            icon = Icon32.ball_yellow;
        } else if (EstratificacaoRisco.Risco.VERDE.equals(risco)) {
            icon = Icon32.ball_green;
        } else {
            icon = Icon32.ball_grey;
        }

        lblDescricaoRisco.setDefaultModel(new Model<>(risco.descricao()));

        if (modifierScore != null && containerScore.getBehaviors().contains(modifierScore)) {
            containerScore.remove(modifierScore);
        }

        modifierScore = new AttributeModifier("class", "icon32 " + icon.clazz());
        containerScore.add(modifierScore);
    }

    private void onEvent(AjaxRequestTarget target) {
        calcularScore();
        calcularRisco();

        if (target != null) {
            target.add(containerScore);
            target.add(lblDescricaoRisco);
            target.add(lblScore);
        }
    }

    private Form<FormularioHipertensaoArterial> getForm() {
        if (this.form == null) {
            if (codigoEstratificacao != null) {
                this.formularioHipertensaoArterial = LoadManager.getInstance(FormularioHipertensaoArterial.class)
                        .addProperties(new HQLProperties(FormularioHipertensaoArterial.class).getProperties())
                        .addProperties(new HQLProperties(EstratificacaoRisco.class, FormularioHipertensaoArterial.PROP_ESTRATIFICACAO_RISCO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FormularioHipertensaoArterial.PROP_ESTRATIFICACAO_RISCO, EstratificacaoRisco.PROP_CODIGO), codigoEstratificacao))
                        .start().getVO();
            } else {
                this.formularioHipertensaoArterial = new FormularioHipertensaoArterial();
            }

            this.form = new Form("form", model = new CompoundPropertyModel(formularioHipertensaoArterial));
        }

        return this.form;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        BOFactory.getBO(AtendimentoFacade.class).salvarFormularioHipertensaoArterial(getAtendimento(), model.getObject());
        voltar(target);
    }

    private void voltar(AjaxRequestTarget target) {
        getProntuarioController().changePanel(target, new EstratificacaoRiscoPanel(getProntuarioController().panelId()));
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }
}