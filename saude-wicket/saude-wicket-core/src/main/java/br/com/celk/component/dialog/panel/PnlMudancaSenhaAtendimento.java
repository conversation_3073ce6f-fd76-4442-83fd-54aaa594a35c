package br.com.celk.component.dialog.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.io.Serializable;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public abstract class PnlMudancaSenhaAtendimento extends Panel {

    private Form form;


    private Long codigoUsuarioCadsus;
    private String nomeUsuarioCadsus;
    private String senha;


    private Long tipoSenha;
    private Long radioAtendimentoComumButton;
    private Long radioAtendimentoPrioritarioButton;


    private Long radioAtendimento;
    private InputField txtNome;


    private InputField txtSenha;
    private RadioButtonGroup radioAtendimentoGroup;
    private AjaxRadio radioAtendimentoComum;
    private AjaxRadio radioAtendimentoPrioritario;

    private String parametroGem;
    private Atendimento atendimento;

    private WebMarkupContainer containerSenhaDiv;


    public PnlMudancaSenhaAtendimento(String id,Atendimento atendimento) {
        super(id);
        this.atendimento = atendimento;
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);

        try {
            parametroGem =  BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("chamarUtilizando");
        } catch (DAOException e) {
            e.printStackTrace(); // voltar aqui pra ver qual melhor saida
        }

        Atendimento proxy = on(Atendimento.class);

        form.add(txtNome = new DisabledInputField("nomeUsuarioCadsus", new PropertyModel<Long>(this, "nomeUsuarioCadsus")));
        txtNome.setComponentValue(atendimento.getUsuarioCadsus().getNome());


        form.add(containerSenhaDiv =  new WebMarkupContainer("containerSenhaDiv"));
        containerSenhaDiv.setOutputMarkupId(true);
        containerSenhaDiv.add(txtSenha = new InputField("senha", new PropertyModel<String>(this, "senha")));
        txtSenha.setComponentValue(atendimento.getSenha());

        containerSenhaDiv.add(radioAtendimentoGroup = new RadioButtonGroup(path(proxy.getTipoSenha()), new PropertyModel<>(this, path(proxy.getTipoSenha()))));
        radioAtendimentoGroup.setOutputMarkupId(true);
        radioAtendimentoGroup.add(radioAtendimentoComum = new AjaxRadio("radioAtendimentoComumButton", new Model(Atendimento.TipoSenha.COMUM.value())));
        radioAtendimentoGroup.add(radioAtendimentoPrioritario = new AjaxRadio("radioAtendimentoPrioritarioButton", new Model(Atendimento.TipoSenha.PRIORITARIO.value())));
        radioAtendimentoGroup.setModelObject(atendimento.getTipoSenha());


        containerSenhaDiv.setVisible(parametroGem.equalsIgnoreCase(Atendimento.ParamGemAtedimento.SENHA.descricao()));
        radioAtendimentoComum.setOutputMarkupId(true);
        radioAtendimentoPrioritario.setOutputMarkupId(true);

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                atendimento.setSenha(Long.parseLong(txtSenha.getValue()));
                atendimento.setTipoSenha((Long) radioAtendimentoGroup.getModelObject());
                onConfirmar(target,atendimento);
                limpar(target);
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Serializable modelObject) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        txtSenha.limpar(target);
        radioAtendimentoGroup.limpar();
        txtNome.limpar(target);
    }

}