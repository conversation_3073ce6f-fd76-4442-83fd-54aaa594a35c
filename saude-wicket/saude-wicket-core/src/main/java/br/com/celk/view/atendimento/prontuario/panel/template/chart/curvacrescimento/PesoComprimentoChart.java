package br.com.celk.view.atendimento.prontuario.panel.template.chart.curvacrescimento;

import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.view.atendimento.prontuario.panel.template.chart.curvacrescimento.options.CurvaCrescimentoChartOptions;
import br.com.ksisolucoes.bo.prontuario.web.chart.dto.CurvaCrescimentoSeriesDTO;
import com.googlecode.wickedcharts.highcharts.options.Axis;
import com.googlecode.wickedcharts.highcharts.options.Marker;
import com.googlecode.wickedcharts.highcharts.options.Options;
import com.googlecode.wickedcharts.highcharts.options.TickmarkPlacement;
import com.googlecode.wickedcharts.highcharts.options.Title;
import com.googlecode.wickedcharts.highcharts.options.series.Coordinate;
import com.googlecode.wickedcharts.highcharts.options.series.CoordinatesSeries;
import com.googlecode.wickedcharts.highcharts.options.series.SimpleSeries;
import java.awt.Color;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PesoComprimentoChart implements Serializable{
    
    private List<CurvaCrescimentoSeriesDTO> lineSeries;
    
    public Options startChart() {
        Options options = new CurvaCrescimentoChartOptions().build().getOptions();

        options
            .setTitle(new Title(bundle("pesoPorComprimento")));

        options
            .setyAxis(new Axis()
                .setTitle(new Title(bundle("pesoKg"))));
        
        List<String> categorias = new ArrayList<String>();
        boolean categoriasDefinidas = false;
        
        for(CurvaCrescimentoSeriesDTO dto : getLineSeries()){
            if(dto.isPaciente() && dto.getSerie() != null){
                List<Coordinate<Number, Number>> list = new ArrayList<Coordinate<Number, Number>>();
                
                for (int i = 0; i < dto.getSerie().size(); i++) {
                    Coordinate<Number, Number> coordinate = new Coordinate((dto.getSerieSecundaria().get(i).doubleValue()-45)*10, dto.getSerie().get(i));
                    list.add(coordinate);
                }
                
                options
                    .addSeries(new CoordinatesSeries()
                        .setMarker(dto.isMarker() ? new Marker(Boolean.TRUE) : new Marker(Boolean.FALSE))
                        .setName(dto.getName())
                        .setColor(Color.RED)
                        .setData(
                            list));
            } else if(!dto.isPaciente()){                
                if(!categoriasDefinidas){
                    categoriasDefinidas = true;
                    for (int i = 0; i < dto.getSerieCategoria().size(); i++) {
                        categorias.add(dto.getSerieCategoria().get(i).toString());
                    }
                }
                
                options
                    .addSeries(new SimpleSeries()
                        .setMarker(dto.isMarker() ? new Marker(Boolean.TRUE) : new Marker(Boolean.FALSE))
                        .setName(dto.getName())
                        .setData(
                            dto.getSerie()));

                options
                    .setxAxis(new Axis()
                        .setTickmarkPlacement(TickmarkPlacement.ON)
                        .setGridLineWidth(1)
                        .setCategories(categorias)
                        .setEndOnTick(Boolean.FALSE)
                        .setShowFirstLabel(Boolean.FALSE)
                        .setShowLastLabel(Boolean.TRUE)
                        .setTickInterval(50F)
                        .setStartOnTick(Boolean.TRUE)
                        .setTitle(new Title(bundle("comprimentoCm"))));
            }
        }
        
        return options;
    }

    public List<CurvaCrescimentoSeriesDTO> getLineSeries() {
        return lineSeries;
    }

    public void setLineSeries(List<CurvaCrescimentoSeriesDTO> lineSeries) {
        this.lineSeries = lineSeries;
    }
}