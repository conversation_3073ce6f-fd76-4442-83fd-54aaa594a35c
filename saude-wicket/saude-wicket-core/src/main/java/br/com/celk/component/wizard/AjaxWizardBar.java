package br.com.celk.component.wizard;

import org.apache.wicket.markup.html.form.IFormSubmittingComponent;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class AjaxWizardBar extends Panel implements IAjaxDefaultButtonProvider{

    public AjaxWizardBar(String id, IAjaxWizard wizard) {
        super(id);
        
//        add(new <PERSON><PERSON>Button("cancel", wizard));
        add(new AjaxPreviousButton("previous", wizard));
        add(new AjaxNextButton("next", wizard));
        add(new AjaxFinishButton("finish", wizard));
    }

    @Override
    public IFormSubmittingComponent getDefaultButton(IAjaxWizardModel model) {
        if (model.isNextAvailable())
        {
            return (IFormSubmittingComponent) get("next");
        } else if (model.isLastAvailable()) {
            return (IFormSubmittingComponent) get("last");
        } else if (model.isLastStep(model.getActiveStep())) {
            return (IFormSubmittingComponent) get("finish");
        }
        return null;
    }
    
}
