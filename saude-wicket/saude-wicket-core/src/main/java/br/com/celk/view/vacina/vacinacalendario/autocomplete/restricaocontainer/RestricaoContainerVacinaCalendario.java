package br.com.celk.view.vacina.vacinacalendario.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.QueryConsultaVacinaCalendarioDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerVacinaCalendario extends Panel implements IRestricaoContainer<QueryConsultaVacinaCalendarioDTOParam> {

    private InputField txtDescricao;
    
    private QueryConsultaVacinaCalendarioDTOParam param = new QueryConsultaVacinaCalendarioDTOParam();
    
    public RestricaoContainerVacinaCalendario(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel<QueryConsultaVacinaCalendarioDTOParam>(param));
        
        root.add(txtDescricao = new InputField("descricao", String.class));
        txtDescricao.addAjaxUpdateValue();
        
        add(root);
    }

    @Override
    public QueryConsultaVacinaCalendarioDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
        
        target.add(txtDescricao);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}