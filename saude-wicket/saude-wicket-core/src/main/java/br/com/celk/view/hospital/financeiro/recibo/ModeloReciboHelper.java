package br.com.celk.view.hospital.financeiro.recibo;

import br.com.celk.bo.hospital.financeiro.interfaces.dto.ReciboContaFinanceiraDTO;
import br.com.celk.bo.hospital.financeiro.interfaces.dto.ReciboContaFinanceiraDTOParam;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.financeiro.ModeloRecibo;
import java.text.ParseException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ModeloReciboHelper {

    public static String builder(Long codigoModeloRecibo, Long codigoItemContaFinanceira) throws ValidacaoException, DAOException {
        if (codigoModeloRecibo == null || codigoItemContaFinanceira == null) {
            return null;
        }

        ModeloRecibo modeloRecibo = LoadManager.getInstance(ModeloRecibo.class).setId(codigoModeloRecibo).start().getVO();
        if (modeloRecibo == null) {
            throw new ValidacaoException(bundle("msgModeloReciboNaoDisponivel"));
        }

        ReciboContaFinanceiraDTOParam param = new ReciboContaFinanceiraDTOParam();
        param.setCodigoItemContaFinanceira(codigoItemContaFinanceira);
        ReciboContaFinanceiraDTO dto = BOFactory.getBO(HospitalFacade.class).reciboContaFinanceira(param);
        if (dto == null) {
            throw new ValidacaoException(bundle("msgNaoFoiEncontradoRegistroPagamentoContaFinanceiraItemCodigoX", codigoItemContaFinanceira));
        }

        String modeloCompilado = modeloRecibo.getModelo();

        try {
            modeloCompilado = modeloCompilado.replaceAll("\\|@Paciente\\|", dto.getPaciente());
            modeloCompilado = modeloCompilado.replaceAll("\\|@CpfPaciente\\|", dto.getCpfPaciente());
            modeloCompilado = modeloCompilado.replaceAll("\\|@ResponsavelPaciente\\|", dto.getResponsavelPaciente());
            modeloCompilado = modeloCompilado.replaceAll("\\|@CpfResponsavel\\|", dto.getCpfResponsavel());
            modeloCompilado = modeloCompilado.replaceAll("\\|@TipoAtendimento\\|", dto.getTipoAtendimento());
            modeloCompilado = modeloCompilado.replaceAll("\\|@Estalecimento\\|", dto.getEstabelecimento());
            modeloCompilado = modeloCompilado.replaceAll("\\|@CnpjEstabelecimento\\|", dto.getCnpjEstabelecimento());
            modeloCompilado = modeloCompilado.replaceAll("\\|@UsuarioEmitente\\|", dto.getUsuarioEmitente());
            modeloCompilado = modeloCompilado.replaceAll("\\|@DataRecebimento\\|", dto.getDataRecebimento());
            modeloCompilado = modeloCompilado.replaceAll("\\|@ValorRecebido\\|", dto.getValorRecebido());
            modeloCompilado = modeloCompilado.replaceAll("\\|@ValorRecebidoExtenso\\|", dto.getValorRecebidoExtenso());
            modeloCompilado = modeloCompilado.replaceAll("\\|@FormaPagamento\\|", dto.getFormaPagamento());
        } catch (ParseException ex) {
            Logger.getLogger(ModeloReciboHelper.class.getName()).log(Level.SEVERE, null, ex);
            throw new DAOException(ex);
        }

        return modeloCompilado;
    }

}
