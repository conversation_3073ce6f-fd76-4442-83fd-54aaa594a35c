package br.com.celk.view.vacinaaplicacao.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.template.Panel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import br.com.ksisolucoes.vo.vacina.VacinaCalendarioAprazamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.model.Model;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNaoAplicar extends Panel {

    private WebMarkupContainer dialogRoot;
    private TextArea<String> textArea;
    private AbstractAjaxButton btnFechar;
    private VacinaCalendario vacinaCalendario;
    private UsuarioCadsus usuarioCadsus;
    private WebMarkupContainer messageContainer;
    private MultiLineLabel messageLabel;
    private WebMarkupContainer messageContainerDias;
    private MultiLineLabel messageLabelDias;
    private String observacao;
    
    public PnlNaoAplicar(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        dialogRoot = new WebMarkupContainer("dialogRoot");

        dialogRoot.setOutputMarkupId(true);
        
        dialogRoot.add(textArea = new TextArea("observacao", new Model()));
        
        textArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                observacao = textArea.getModelObject();
            }
        });
        
        dialogRoot.add(new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target);
            }
        });

        dialogRoot.add(messageContainer = new WebMarkupContainer("messageContainer"));
        messageContainer.add(messageLabel = new MultiLineLabel("messageLabel"));
        messageContainer.setOutputMarkupId(true);
        dialogRoot.add(messageContainerDias = new WebMarkupContainer("messageContainerDias"));
        messageContainerDias.add(messageLabelDias = new MultiLineLabel("messageLabelDias"));
        messageContainerDias.setOutputMarkupId(true);

        dialogRoot.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        
        btnFechar.setDefaultFormProcessing(false);
        add(dialogRoot);
    }
    //TODO - Refatorar esse metodo
    private boolean verificaIntervaloMinimoEntreDosagem() {
        if (vacinaCalendario.getIntervaloMinimoDose() != null && RepositoryComponentDefault.NAO_LONG.equals(vacinaCalendario.getFlagAplicarAntesIntervalo())) {
            String msgPrazo = null;
            List<VacinaCalendarioAprazamento> list = LoadManager.getInstance(VacinaCalendarioAprazamento.class)
                    .addProperties(new HQLProperties(VacinaCalendarioAprazamento.class).getProperties())
                    .addProperties(new HQLProperties(VacinaCalendario.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO)).getProperties())
                    .addProperties(new HQLProperties(TipoVacina.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO, VacinaCalendario.PROP_TIPO_VACINA)).getProperties())
                    .addProperties(new HQLProperties(VacinaCalendario.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO_ORIGEM)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO_ORIGEM), vacinaCalendario))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(list)) {
                for (VacinaCalendarioAprazamento vacinaCalendarioAprazamento : list) {
                    VacinaAplicacao vacinaAplicacao = LoadManager.getInstance(VacinaAplicacao.class)
                            .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_TIPO_VACINA, TipoVacina.PROP_CODIGO), vacinaCalendarioAprazamento.getVacinaCalendario().getTipoVacina().getCodigo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                            .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .setMaxResults(1).start().getVO();
                    if (vacinaAplicacao != null) {
                        int diferenca = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), DataUtil.getDataAtual());
                        if (vacinaCalendario.getIntervaloMinimoDose().compareTo(new Long(diferenca)) > 0) {
                            msgPrazo = BundleManager.getString("msgIntervaloMinimoEntreDoseXDias", vacinaCalendario.getIntervaloMinimoDose());
                            messageLabelDias.setDefaultModel(new Model(msgPrazo));
                            messageContainerDias.setVisible(true);
                            return false;
                        }
                    }
                }
            }
            VacinaAplicacao vacinaAplicacao = LoadManager.getInstance(VacinaAplicacao.class)
                    .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_TIPO_VACINA, TipoVacina.PROP_CODIGO), vacinaCalendario.getTipoVacina().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();
            if (vacinaAplicacao != null) {
                int diferenca = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), DataUtil.getDataAtual());
                if (vacinaCalendario.getIntervaloMinimoDose().compareTo(new Long(diferenca)) > 0) {
                    msgPrazo = BundleManager.getString("msgIntervaloMinimoEntreDoseXDias", vacinaCalendario.getIntervaloMinimoDose());
                    messageLabelDias.setDefaultModel(new Model(msgPrazo));
                    messageContainerDias.setVisible(true);
                    return false;
                }
            }
        }
        messageContainerDias.setVisible(false);
        return true;
    }

    private void verificaIntervaloMaximoEntreDosagem() {
        if (vacinaCalendario.getIntervaloMaximoDose() != null && RepositoryComponentDefault.NAO_LONG.equals(vacinaCalendario.getFlagAplicarAposIntervalo())) {
            String msgPrazo = null;
            List<VacinaCalendarioAprazamento> list = LoadManager.getInstance(VacinaCalendarioAprazamento.class)
                    .addProperties(new HQLProperties(VacinaCalendarioAprazamento.class).getProperties())
                    .addProperties(new HQLProperties(VacinaCalendario.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO)).getProperties())
                    .addProperties(new HQLProperties(TipoVacina.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO, VacinaCalendario.PROP_TIPO_VACINA)).getProperties())
                    .addProperties(new HQLProperties(VacinaCalendario.class, VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO_ORIGEM)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaCalendarioAprazamento.PROP_VACINA_CALENDARIO_ORIGEM), vacinaCalendario))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(list)) {
                for (VacinaCalendarioAprazamento vacinaCalendarioAprazamento : list) {
                    VacinaAplicacao vacinaAplicacao = LoadManager.getInstance(VacinaAplicacao.class)
                            .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_TIPO_VACINA, TipoVacina.PROP_CODIGO), vacinaCalendarioAprazamento.getVacinaCalendario().getTipoVacina().getCodigo()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                            .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .setMaxResults(1).start().getVO();
                    if (vacinaAplicacao != null) {
                        int diferenca = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), DataUtil.getDataAtual());
                        if (new Long(diferenca).compareTo(vacinaCalendario.getIntervaloMaximoDose()) > 0) {
                            msgPrazo = BundleManager.getString("msgIntervaloMaximoEntreDoseXDias", vacinaCalendario.getIntervaloMaximoDose());
                        }
                    }
                }
            }
            VacinaAplicacao vacinaAplicacao = LoadManager.getInstance(VacinaAplicacao.class)
                    .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_TIPO_VACINA, TipoVacina.PROP_CODIGO), vacinaCalendario.getTipoVacina().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VacinaAplicacao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(VacinaAplicacao.PROP_DATA_APLICACAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1).start().getVO();
            if (vacinaAplicacao != null) {
                int diferenca = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), DataUtil.getDataAtual());
                if (new Long(diferenca).compareTo(vacinaCalendario.getIntervaloMaximoDose()) > 0) {
                    msgPrazo = BundleManager.getString("msgIntervaloMaximoEntreDoseXDias", vacinaCalendario.getIntervaloMaximoDose());
                }
            }
            if (msgPrazo != null) {
                messageLabelDias.setDefaultModel(new Model(msgPrazo));
                messageContainerDias.setVisible(true);
            } else {
                messageContainerDias.setVisible(false);
            }
        } else {
            messageContainerDias.setVisible(false);
        }
    }

    private void validarVacinaForaPrazo() {
        if (vacinaCalendario != null) {
            String msgPacienteForaIdade = null;
            Long idadeLimiteVacinaMeses = vacinaCalendario.getIdade();
            if (RepositoryComponentDefault.SIM_LONG.equals(vacinaCalendario.getFlagAplicarAntesLimite())) {
                if (idadeLimiteVacinaMeses > usuarioCadsus.getIdadeEmMeses()) {
                    msgPacienteForaIdade = BundleManager.getString("msgIdadeMinimaAplicacaoX", idadeFormatado(idadeLimiteVacinaMeses));
                }
            }

            Long idadeMaximaLimiteVacinaMeses = vacinaCalendario.getIdadeLimite();
            if (RepositoryComponentDefault.SIM_LONG.equals(vacinaCalendario.getFlagAplicarAposLimite())) {
                if (idadeMaximaLimiteVacinaMeses <= usuarioCadsus.getIdadeEmMeses()) {
                    msgPacienteForaIdade = BundleManager.getString("msgIdadeMaximaAplicacaoX", idadeFormatado(idadeMaximaLimiteVacinaMeses));
                }
            }

            if (msgPacienteForaIdade != null) {
                messageLabel.setDefaultModel(new Model(msgPacienteForaIdade));
                messageContainer.setVisible(true);
            } else {
                messageContainer.setVisible(false);
            }
        } else {
            messageContainer.setVisible(false);
        }
    }

    public String idadeFormatado(Long idade) {
        if (idade != null) {
            if (idade == 0) {
                return Bundle.getStringApplication("rotulo_ao_nascer");
            }
            if (idade % 12 == 0) {
                idade = idade / 12;

                if (idade == 1L) {
                    return idade + " " + Bundle.getStringApplication("rotulo_ano");
                }
                return idade + " " + Bundle.getStringApplication("rotulo_anos");
            } else {
                if (idade == 1L) {
                    return idade + " " + Bundle.getStringApplication("rotulo_mes");
                }
                return idade + " " + Bundle.getStringApplication("rotulo_meses");
            }
        }
        return "";
    }

    public void update(AjaxRequestTarget target){
        target.add(dialogRoot);
    }
    
    public String getObservacao() {
        return observacao;
    }

    public void setVacinaCalendarioIdadeMeses(VacinaCalendario vacinaCalendario, UsuarioCadsus usuarioCadsus) {
        this.vacinaCalendario = vacinaCalendario;
        this.usuarioCadsus = usuarioCadsus;
        validarVacinaForaPrazo();
        boolean interValoMinimoOk = verificaIntervaloMinimoEntreDosagem();
        if(interValoMinimoOk) {
            verificaIntervaloMaximoEntreDosagem();
        }
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
    
    public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException{}

}
