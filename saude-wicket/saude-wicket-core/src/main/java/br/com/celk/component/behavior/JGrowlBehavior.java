package br.com.celk.component.behavior;

import java.io.Serializable;
import java.util.Iterator;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Session;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.feedback.FeedbackMessages;
import org.apache.wicket.feedback.IFeedbackMessageFilter;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;

public class J<PERSON>rowlBehavior extends AbstractDefaultAjaxBehavior {

    private Component reporter;
    
    public JGrowlBehavior() {
    }
    
    public JGrowlBehavior(Component reporter) {
        this.reporter = reporter;
    }

    public static final int INFO_STICKY = 250;

    @Override
    protected void respond(AjaxRequestTarget target) {
        final String feedbackMsg = renderFeedback();
        if (!StringUtils.isEmpty(feedbackMsg)) {
            target.appendJavaScript(feedbackMsg);
        }
    }

    @Override
    public void renderHead(Component component, final IHeaderResponse response) {
        super.renderHead(component, response);
        final String feedbackMsg = renderFeedback();
        if (!StringUtils.isEmpty(feedbackMsg)) {
            response.render(JavaScriptHeaderItem.forScript(feedbackMsg, ""));
        }
    }

    private String renderFeedback() {
        final FeedbackMessages fm = Session.get().getFeedbackMessages();

        final Iterator<FeedbackMessage> iter = fm.iterator();
        final StringBuilder sb = new StringBuilder();
        while (iter.hasNext()) {
            final FeedbackMessage message = iter.next();
            if ((message.getReporter() != null && !message.getReporter().equals(reporter)) || message.isRendered()) {
                continue;
            }
            final String cssClassSuffix = (message.getLevel() == INFO_STICKY) ? "INFO" : message.getLevelAsString();
            final Serializable serializable = message.getMessage();
            final String msg = (serializable == null) ? StringUtils.EMPTY : serializable.toString();
            sb.append("$.jGrowl(\"").append(msg).append('\"');
            sb.append(", {");
            sb.append("theme: \'jgrowl-").append(cssClassSuffix).append("\'");
            if (message.getLevel() > FeedbackMessage.INFO) {
                sb.append(", sticky: true");
            }
            sb.append("}");
            sb.append(");");
            message.markRendered();
            Session.get().getFeedbackMessages().clear(new MessageFilter(message));
        }
        return sb.toString();
    }
    
    class MessageFilter implements IFeedbackMessageFilter{

        private FeedbackMessage message;
        
        public MessageFilter(FeedbackMessage message) {
            this.message = message;
        }
        
        public boolean accept(FeedbackMessage message) {
            return this.message.equals(message);
        }
        
    }
}
