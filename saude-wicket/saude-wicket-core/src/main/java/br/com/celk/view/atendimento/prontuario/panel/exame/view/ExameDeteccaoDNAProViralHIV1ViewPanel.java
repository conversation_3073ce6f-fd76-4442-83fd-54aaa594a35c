package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoLacenPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameDeteccaoDNAProViralHIV1DTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoDeteccaoDNAProViralHIV1;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ExameDeteccaoDNAProViralHIV1ViewPanel extends ProntuarioCadastroPanel {

    private Form<RequisicaoDeteccaoDNAProViralHIV1> form;
    private WebMarkupContainer containerOne;
    private WebMarkupContainer containerTwo;
    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;

    private DropDown dropDownMotivoDaSolicitacao;
    private InputField txtOutroMotivo;

    private TipoExame tipoExame;
    private Exame exame;

    public ExameDeteccaoDNAProViralHIV1ViewPanel(String id, Exame exame, TipoExame tipoExame) {
        super(id, bundle("deteccaoDNAProviralHIV1"));
        this.exame = exame;
        this.tipoExame = tipoExame;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        RequisicaoDeteccaoDNAProViralHIV1 proxy = on(RequisicaoDeteccaoDNAProViralHIV1.class);

        containerOne = new WebMarkupContainer("containerOne");
        containerOne.add(dropDownMotivoDaSolicitacao = DropDownUtil.getIEnumDropDown(path(proxy.getMotivoSolicitacao()), RequisicaoDeteccaoDNAProViralHIV1.MotivoSolicitacao.values()));

        containerTwo = new WebMarkupContainer("containerTwo");
        containerTwo.setOutputMarkupId(true);
        containerTwo.setOutputMarkupPlaceholderTag(true);
        containerTwo.add(txtOutroMotivo = new InputField(path(proxy.getJustificativa())));
        containerTwo.setVisible(false);

        dropDownMotivoDaSolicitacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                if (dropDownMotivoDaSolicitacao.getComponentValue().equals(RequisicaoDeteccaoDNAProViralHIV1.MotivoSolicitacao.DIAGNOSTICO.value())) {
                    containerTwo.setVisible(false);
                    txtOutroMotivo.limpar(art);
                    txtOutroMotivo.addRequiredClass();
                } else {
                    containerTwo.setVisible(true);
                    txtOutroMotivo.removeRequiredClass();
                }
                art.add(containerTwo);
            }
        });
        
        containerOne.add(containerTwo);
        getForm().add(containerOne);

        getForm().add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarExameDeteccaoDNAProViralHIV1ViewPanel(target);
            }
        });

        add(getForm());
        if (this.tipoExame == null) {
            this.tipoExame = new TipoExame();
        } else {
            carregarExameDeteccaoDNAProViralHIV1();
        }
        if (dropDownMotivoDaSolicitacao.getComponentValue() != null) {
            if (dropDownMotivoDaSolicitacao.getComponentValue().equals(RequisicaoDeteccaoDNAProViralHIV1.MotivoSolicitacao.DIAGNOSTICO.value())) {
                containerTwo.setVisible(false);
            } else {
                containerTwo.setVisible(true);
            }
        }
    }

    private Form<RequisicaoDeteccaoDNAProViralHIV1> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new RequisicaoDeteccaoDNAProViralHIV1()));
        }

        return this.form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
//        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }

    private void salvarExameDeteccaoDNAProViralHIV1ViewPanel(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tipoExame.getExameProcedimentoPadrao() == null) {
            throw new ValidacaoException(bundle("tipoExameSemProcedimentoPadrao", this));
        }

        RequisicaoDeteccaoDNAProViralHIV1 requisicaoDeteccaoDNAProViralHIV1 = getForm().getModel().getObject();

        requisicaoDeteccaoDNAProViralHIV1.setJustificativa(form.getModel().getObject().getJustificativa());
        requisicaoDeteccaoDNAProViralHIV1.setDataCadastro(DataUtil.getDataAtual());

        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();
        if (this.exame != null) {
            dto.setCodigoExameCadastrado(this.exame.getCodigo());
            dto.setDataSolicitacao(this.exame.getDataSolicitacao());
            dto.setAtendimento(this.exame.getAtendimento());
            dto.setCodigoUnidade(this.exame.getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(this.exame.getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(this.exame.getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(this.exame.getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(this.exame.getAtendimento().getUsuarioCadsus().getNomeSocial());
        } else {
            dto.setDataSolicitacao(DataUtil.getDataAtual());
            dto.setAtendimento(getAtendimento());
            dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
            dto.setOrigem(Exame.Origem.LACEN.value());
        }

        ExameProcedimentoDTO exameProcedimento = new ExameProcedimentoDTO();

        exameProcedimento.setExameProcedimento(tipoExame.getExameProcedimentoPadrao());
        exameProcedimento.setQuantidade(1L);
        exameProcedimento.setComplemento(null);
        exameProcedimento.setValor(0D);

        List<ExameProcedimentoDTO> item = new ArrayList<>();
        item.add(exameProcedimento);

        dto.setExameProcedimentoDTOs(item);
        Long codigoExameCadastrado = BOFactoryWicket.getBO(ExameFacade.class).cadastrarSolicitacaoExameDeteccaoDNAProViralHIV1(dto, requisicaoDeteccaoDNAProViralHIV1);

        initDialogImpressao(target);
        dlgConfirmacaoImpressao.show(target, codigoExameCadastrado);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), bundle("desejaImprimirExame", this)) {
                @Override
                public DataReport getDataReport(Long codigoExameCadastrado) throws ReportException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();
                    
                    ImpressaoExameDeteccaoDNAProViralHIV1DTOParam param = new ImpressaoExameDeteccaoDNAProViralHIV1DTOParam();
                    param.setCodigoExame(codigoExameCadastrado);
                    param.setAtendimento(exameCadastrado.getAtendimento());

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoExameDeteccaoDNAProViralHIV1(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long codigoExameCadastrado) {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO))
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    Boolean isLacen = RepositoryComponentDefault.Tipo.LACEN.value().equals(exameCadastrado.getTipoExame().getTipo());
                    if(isLacen){
                        getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                    } else {
                        getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId()));
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void carregarExameDeteccaoDNAProViralHIV1() {
        ExameRequisicao exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperty(ExameRequisicao.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, tipoExame.getExameProcedimentoPadrao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), ExameRequisicao.Status.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Exame.STATUS_AUTORIZADO, Exame.STATUS_DESVINCULADO, Exame.STATUS_RECEBIDO, Exame.STATUS_SOLICITADO)))
                .start().getVO();

        if (exameRequisicao != null) {
            RequisicaoDeteccaoDNAProViralHIV1 requisicaoDeteccaoDNAProViralHIV1 = LoadManager.getInstance(RequisicaoDeteccaoDNAProViralHIV1.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoDeteccaoDNAProViralHIV1.PROP_EXAME_REQUISICAO, exameRequisicao))
                    .start().getVO();

            if (requisicaoDeteccaoDNAProViralHIV1 != null) {
                getForm().setModelObject(requisicaoDeteccaoDNAProViralHIV1);
            }
        }
    }
}