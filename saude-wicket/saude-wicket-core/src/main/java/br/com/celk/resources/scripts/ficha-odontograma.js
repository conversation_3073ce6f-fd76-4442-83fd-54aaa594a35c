function WicketComponent(markupid, callBackURL) {
    this.markupid = markupid;
    this.callBackURL = callBackURL;
}

var OdontogramFicha = {

    init: function(callBackURL, denteJSON, situacaoJSON, denteSituacaoJSON, codigoAtendimento, json) {
        OdontogramFicha.callBackURLPanel = callBackURL;

//        OdontogramFicha.odontogramaJsonDTO = null;
//
//        if (json) {
//            OdontogramFicha.setJSON(json);
//        }

        OdontogramFicha.wicketComponents = [];

        OdontogramFicha.codigoAtendimento = codigoAtendimento;

//        OdontogramFicha.denteList = JSON.parse(denteJSON);
        OdontogramFicha.situacaoList = JSON.parse(situacaoJSON);
        OdontogramFicha.denteSituacaoList = JSON.parse(denteSituacaoJSON);

        OdontogramFicha.initDialog();
        OdontogramFicha.initNotification();
        OdontogramFicha.injectSVGs();
    },

    injectSVGs: function() {
        var $faces = $('ck-svg-faces').find('img[src*=".svg"]').get();
        var $teeth = $('ck-svg-tooth').find('img[src*=".svg"]').get();

        if ($faces && $teeth) {
            SVGInjector($faces);
            SVGInjector($teeth, {
                each: function(svg) { OdontogramFicha.addListeners(svg) }
            }, function() {
                OdontogramFicha.refresh();
            });
        }
    },

    lock: function () {
        loading(true);
    },

    unlock: function () {
        stopLoading();
    },

    initDialog: function() {
        OdontogramFicha.dialog = $('div[id=dialog]');

        OdontogramFicha.dialog.close = function() {
            var buttons = this.getButtons();
            buttons.setVisible(true);
            buttons.hide();

            this.removeClass('on');
            $(this).hide();
        };

        OdontogramFicha.dialog.apply = function(dto) {
            var situacao = dto.situacao;
            var target = dto.target;
            var svg = dto.svg;

            var id = $(svg).attr('name');

            var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];
            if (!odontogramaTratamentoJsonDTO) {
                odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);
                OdontogramFicha.getTratamentos().push(odontogramaTratamentoJsonDTO);
            }

            odontogramaTratamentoJsonDTO.inativo = false;

            var div = $(svg).closest('ck-tooth');

            if (!situacao.codigo) {
                if (target.id === 'coroa') {
                    var situacaoCoroa = odontogramaTratamentoJsonDTO.coroa;
                    if (situacaoCoroa) {
                        odontogramaTratamentoJsonDTO.coroa = null;
                        var svgTooth = $(svg).closest('ck-svg-tooth')
                        $(svgTooth).removeAttr('data-abbreviation');

                        var coroa = $(svg).find('path[id=coroa]');
                        OdontogramFicha.setColor(coroa, 'ffffff');
                        var title = coroa.find("title");
                        title.empty();

                        // Ao definir NENHUM tratamento para coroa do dente, deve limpar as faces
                        var faces = div.find('ck-svg-faces > svg');

                        $(faces).find("path[id=palatal]").css('fill', '#ffffff');
                        $(faces).find("path[id=vestibular]").css('fill', '#ffffff');
                        $(faces).find("path[id=distal]").css('fill', '#ffffff');
                        $(faces).find("path[id=mesial]").css('fill', '#ffffff');
                        $(faces).find("rect[id=oclusal]").css('fill', '#ffffff');

                        odontogramaTratamentoJsonDTO.faces = {};
                    }
                } else {
                    var colo = $(svg).find('path[id=colo]');
                    var raiz = $(svg).find('path[id=raiz]');

                    var titleColo = colo.find("title");
                    var titleRaiz = raiz.find("title");

                    var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                    var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                    if (target.id === 'colo') {
                        if (situacaoColo) {
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                odontogramaTratamentoJsonDTO.colo = null;
                                odontogramaTratamentoJsonDTO.raiz = null;
                                OdontogramFicha.setColor(colo, 'ffffff');
                                OdontogramFicha.setColor(raiz, 'ffffff');
                                titleColo.empty();
                                titleRaiz.empty();
                            } else {
                                odontogramaTratamentoJsonDTO.colo = null;
                                OdontogramFicha.setColor(colo, 'ffffff');
                                titleColo.empty();
                            }
                        }
                    } else if (target.id === 'raiz') {
                        if (situacaoRaiz) {
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                odontogramaTratamentoJsonDTO.raiz = null;
                                odontogramaTratamentoJsonDTO.colo = null;
                                OdontogramFicha.setColor(raiz, 'ffffff');
                                OdontogramFicha.setColor(colo, 'ffffff');
                                titleRaiz.empty();
                                titleColo.empty();
                            } else {
                                odontogramaTratamentoJsonDTO.raiz = null;
                                OdontogramFicha.setColor(raiz, 'ffffff');
                                titleRaiz.empty();
                            }
                        }
                    }
                }

                OdontogramFicha.toJSON();

                var markupid = div.attr('id');
                var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
                if (wicketComponent) {
                    var url = wicketComponent.callBackURL;
                    OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'restaurar', face: target.id });
                }
            } else {
//                // Restaura o dente quando escolhido uma nova situação (dente inativo)
//                if (odontogramaTratamentoJsonDTO.inativo) {
//                    delete odontogramaTratamentoJsonDTO['raiz'];
//                    delete odontogramaTratamentoJsonDTO['colo'];
//                    delete odontogramaTratamentoJsonDTO['coroa'];
//                    delete odontogramaTratamentoJsonDTO['faces'];
//                    delete odontogramaTratamentoJsonDTO['status'];
//                    delete odontogramaTratamentoJsonDTO['inativo'];
//                    delete odontogramaTratamentoJsonDTO['concluido'];
//                    delete odontogramaTratamentoJsonDTO['cadastrado'];
//
//                    $(div).removeClass('off');
//                    $(div).removeAttr('disabled');
//
//                    var linkNovaSituacao = div.find('a[id=nova]');
//                    linkNovaSituacao.attr('visible', false);
//
//                    var svgTooth = $(svg).closest('ck-svg-tooth')
//                    $(svgTooth).removeAttr('data-abbreviation');
//
//                    var cor = 'FFFFFF';
//                    var colo = $(svg).find('path[id=colo]');
//                    var raiz = $(svg).find('path[id=raiz]');
//                    var coroa = $(svg).find('path[id=coroa]');
//
//                    var faces = div.find('ck-svg-faces > svg');
//                    var distal = $(faces).find("path[id=distal]");
//                    var mesial = $(faces).find("path[id=mesial]");
//                    var oclusal = $(faces).find("rect[id=oclusal]");
//                    var palatal = $(faces).find("path[id=palatal]");
//                    var vestibular = $(faces).find("path[id=vestibular]");
//
//                    OdontogramFicha.setColor(colo, cor);
//                    OdontogramFicha.setColor(raiz, cor);
//                    OdontogramFicha.setColor(coroa, cor);
//
//                    OdontogramFicha.setColor(distal, cor);
//                    OdontogramFicha.setColor(mesial, cor);
//                    OdontogramFicha.setColor(oclusal, cor);
//                    OdontogramFicha.setColor(palatal, cor);
//                    OdontogramFicha.setColor(vestibular, cor);
//
//                    var titleColo = colo.find("title");
//                    var titleRaiz = raiz.find("title");
//                    var titleCoroa = coroa.find("title");
//
//                    titleColo.empty();
//                    titleRaiz.empty();
//                    titleCoroa.empty();
//                }

                situacao.codigoAtendimento = OdontogramFicha.codigoAtendimento;
                situacao.tratamentoUrgente = OdontogramFicha.tratamentoUrgente;
                if (situacao.desabilitaAoAplicar) {
                    odontogramaTratamentoJsonDTO.inativo = true;
                    odontogramaTratamentoJsonDTO.status = situacao;

                    if (odontogramaTratamentoJsonDTO.status.tipoSituacao === TipoSituacao.HISTORICO) {
                        odontogramaTratamentoJsonDTO.status.concluido = true;
                    }

                    $(div).attr('disabled', true);

                    var linkReverter = div.find('a[id=reverter]');
                    linkReverter.attr('visible', true);

                    OdontogramFicha.setDataAbbreviation(svg, situacao.referencia);

                    var cor = situacao.cor;
                    if (cor) {
                        var colo = $(svg).find('path[id=colo]');
                        var raiz = $(svg).find('path[id=raiz]');
                        var coroa = $(svg).find('path[id=coroa]');

                        var faces = div.find('ck-svg-faces > svg');
                        var distal = $(faces).find("path[id=distal]");
                        var mesial = $(faces).find("path[id=mesial]");
                        var oclusal = $(faces).find("rect[id=oclusal]");
                        var palatal = $(faces).find("path[id=palatal]");
                        var vestibular = $(faces).find("path[id=vestibular]");

                        OdontogramFicha.setColor(colo, cor);
                        OdontogramFicha.setColor(raiz, cor);
                        OdontogramFicha.setColor(coroa, cor);

                        OdontogramFicha.setColor(distal, cor);
                        OdontogramFicha.setColor(mesial, cor);
                        OdontogramFicha.setColor(oclusal, cor);
                        OdontogramFicha.setColor(palatal, cor);
                        OdontogramFicha.setColor(vestibular, cor);

                        var titleColo = colo.find("title");
                        var titleRaiz = raiz.find("title");
                        var titleCoroa = coroa.find("title");

                        titleColo.empty();
                        titleColo.append(situacao.descricao);

                        titleRaiz.empty();
                        titleRaiz.append(situacao.descricao);

                        titleCoroa.empty();
                        titleCoroa.append(situacao.descricao);
                    }

                    var markupid = div.attr('id');
                    var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
                    if (wicketComponent) {
                        var url = wicketComponent.callBackURL;
                        OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'aplicar' });
                    }
    //                $(div).attr('ready', 'true');
                } else {
                    if (target.id === 'coroa') { // if (situacao.tipoAplicacao === TipoAplicacao.COROA) {
                        // Ao mudar o tratamento que outrora estava cadastrado/concluído, deve limpar as faces
                        var situacaoAnterior = odontogramaTratamentoJsonDTO.coroa;
                        if (situacaoAnterior && (situacaoAnterior.cadastrado || situacaoAnterior.concluido)) {
                            var faces = div.find('ck-svg-faces > svg');

                            $(faces).find("path[id=palatal]").css('fill', '#ffffff');
                            $(faces).find("path[id=vestibular]").css('fill', '#ffffff');
                            $(faces).find("path[id=distal]").css('fill', '#ffffff');
                            $(faces).find("path[id=mesial]").css('fill', '#ffffff');
                            $(faces).find("rect[id=oclusal]").css('fill', '#ffffff');

                            odontogramaTratamentoJsonDTO.faces = {};
                        }

                        odontogramaTratamentoJsonDTO.coroa = situacao;
                        OdontogramFicha.setDataAbbreviation(svg, situacao.referencia);
                        var coroa = $(svg).find('path[id=coroa]');
                        var title = coroa.find("title");
                        title.empty();
                        title.append(situacao.descricao);
                    } else {
                        var colo = $(svg).find('path[id=colo]');
                        var raiz = $(svg).find('path[id=raiz]');

                        var titleColo = colo.find("title");
                        var titleRaiz = raiz.find("title");

                        if (!situacao.tipoPreenchimento || situacao.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                            odontogramaTratamentoJsonDTO.colo = situacao;
                            odontogramaTratamentoJsonDTO.raiz = situacao;
                            OdontogramFicha.setColor(colo, situacao.cor);
                            OdontogramFicha.setColor(raiz, situacao.cor);

                            titleColo.empty();
                            titleColo.append(situacao.descricao);

                            titleRaiz.empty();
                            titleRaiz.append(situacao.descricao);
                        } else if (situacao.tipoPreenchimento === TipoPreenchimento.COLO) {
                            var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                            if (situacaoRaiz && situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                                OdontogramFicha.setColor(raiz, 'ffffff');
                                odontogramaTratamentoJsonDTO.raiz = null;
                                titleRaiz.empty();
                            }

                            odontogramaTratamentoJsonDTO.colo = situacao;
                            OdontogramFicha.setColor(colo, situacao.cor);

                            titleColo.empty();
                            titleColo.append(situacao.descricao);
                        } else if (situacao.tipoPreenchimento === TipoPreenchimento.RAIZ) {
                            var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                            if (situacaoColo && situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO) {
                                OdontogramFicha.setColor(colo, 'ffffff');
                                odontogramaTratamentoJsonDTO.colo = null;
                                titleColo.empty();
                            }

                            odontogramaTratamentoJsonDTO.raiz = situacao;
                            OdontogramFicha.setColor(raiz, situacao.cor);

                            titleRaiz.empty();
                            titleRaiz.append(situacao.descricao);
                        }
                    }

                    OdontogramFicha.toJSON();
                }
            }

            this.close();
        };

        OdontogramFicha.dialog.getButtons = function() {
            if (!OdontogramFicha.dialog.buttons) {
                var setVisible = function(visible) {
                    $(this).attr('visible', visible);
                };

                var _buttons = $(this).find('ck-buttons');
                _buttons.setVisible = setVisible;

                _buttons.hide = function() {
                    this.setVisible(false);

                    this.btnReverter.setVisible(false);
                    this.btnAplicar.setVisible(false);
                    this.btnNovo.setVisible(false);

                    delete _buttons.target;
                    delete _buttons.odontogramaTratamentoJsonDTO;
                };

                _buttons.show = function(target, odontogramaTratamentoJsonDTO) {
                    _buttons.target = target;
                    _buttons.odontogramaTratamentoJsonDTO = odontogramaTratamentoJsonDTO;

                    this.setVisible(true);
                };

                var concluirTratamento = function(target, odontogramaTratamentoJsonDTO) {
                    var face = target.id;
                    if (face === 'coroa') {
                        odontogramaTratamentoJsonDTO.coroa.concluido = true;

                        var faces = $(target).closest('ck-tooth').find('ck-svg-faces > svg');

                        if (odontogramaTratamentoJsonDTO.faces.palatal) {
                            odontogramaTratamentoJsonDTO.faces.palatal = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=palatal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.vestibular) {
                            odontogramaTratamentoJsonDTO.faces.vestibular = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=vestibular]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.distal) {
                            odontogramaTratamentoJsonDTO.faces.distal = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=distal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.mesial) {
                            odontogramaTratamentoJsonDTO.faces.mesial = StatusFaces.CONCLUIDO;
                            $(faces).find("path[id=mesial]").css('fill', 'rgba(91, 192, 222, 1)');
                        }

                        if (odontogramaTratamentoJsonDTO.faces.oclusal) {
                            odontogramaTratamentoJsonDTO.faces.oclusal = StatusFaces.CONCLUIDO;
                            $(faces).find("rect[id=oclusal]").css('fill', 'rgba(91, 192, 222, 1)');
                        }
                    } else {
                        var situacao;
                        var svg = $(target).closest('svg');

                        var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                        var situacaoRaiz = odontogramaTratamentoJsonDTO.colo;
                        if (face === 'colo') {
                            odontogramaTratamentoJsonDTO.colo.concluido = true;
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                odontogramaTratamentoJsonDTO.raiz.concluido = true;
                                var raiz = svg.find('path[id=raiz]');
                                $(raiz).css('fill', 'rgba(91, 192, 222, 1)');
                            }
                        } else {
                            odontogramaTratamentoJsonDTO.raiz.concluido = true;
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                odontogramaTratamentoJsonDTO.colo.concluido = true;
                                var colo = svg.find('path[id=colo]');
                                $(colo).css('fill', 'rgba(91, 192, 222, 1)');
                            }
                        }

                        $(target).css('fill', 'rgba(91, 192, 222, 1)');
                    }
                };

                var isHistorico = function(target, odontogramaTratamentoJsonDTO) {
                    var tipoSituacao;
                    var face = target.id;

                    if (face === 'coroa') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.coroa.tipoSituacao;
                    } else if (face === 'colo') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.colo.tipoSituacao;
                    } else if (face === 'raiz') {
                        tipoSituacao = odontogramaTratamentoJsonDTO.raiz.tipoSituacao;
                    }

                    if (tipoSituacao && tipoSituacao === TipoSituacao.HISTORICO) {
                        return true;
                    }
                    return false;
                };

                // Define o tratamento como concluido quando tipo for HISTORICO
                var concluiTratamentoQuandoHistorico = function(target, odontogramaTratamentoJsonDTO) {
                    if (isHistorico(target, odontogramaTratamentoJsonDTO)) {
                        concluirTratamento(target, odontogramaTratamentoJsonDTO);
                    }
                };

                var _btnAplicar = _buttons.find('input[id=aplicar]');
                _btnAplicar.setVisible = setVisible;
                _btnAplicar.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    var div = $(target).closest('ck-tooth');

                    concluiTratamentoQuandoHistorico(target, odontogramaTratamentoJsonDTO);

                    var markupid = div.attr('id');
                    var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
                    if (wicketComponent) {
                        var url = wicketComponent.callBackURL;
                        OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'aplicar', face: target.id });
                    }

                    OdontogramFicha.dialog.close();
                });

                var _btnNovo = _buttons.find('input[id=novo]');
                _btnNovo.setVisible = setVisible;
                _btnNovo.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    var face = target.id;
                    if (face === 'coroa') {
                        delete odontogramaTratamentoJsonDTO.coroa;
                        delete odontogramaTratamentoJsonDTO.faces;

                        var svgTooth = $(target).closest('ck-svg-tooth')
                        $(svgTooth).removeAttr('data-abbreviation');

                        var div = $(target).closest('ck-tooth');
                        var divFaces = $(div).find('ck-svg-faces > svg');

                        $(divFaces).find("path[id=palatal]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=vestibular]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=distal]").css('fill', '#ffffff');
                        $(divFaces).find("path[id=mesial]").css('fill', '#ffffff');
                        $(divFaces).find("rect[id=oclusal]").css('fill', '#ffffff');
                    } else {
                        var svg = $(target).closest('svg');

                        var situacaoColo = odontogramaTratamentoJsonDTO.colo;
                        var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
                        if (face === 'colo') {
                            if (situacaoColo.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoRaiz && (situacaoColo.codigoOdontoPlano === situacaoRaiz.codigoOdontoPlano || situacaoColo.codigo === situacaoRaiz.codigo))) {
                                var raiz = svg.find('path[id=raiz]');
                                $(raiz).find("title").empty();
                                $(raiz).css('fill', '#ffffff');
                                delete odontogramaTratamentoJsonDTO.raiz;
                            }
                            delete odontogramaTratamentoJsonDTO.colo;
                        } else {
                            if (situacaoRaiz.tipoPreenchimento === TipoPreenchimento.COMPLETO
                                || (situacaoColo && (situacaoRaiz.codigoOdontoPlano === situacaoColo.codigoOdontoPlano || situacaoRaiz.codigo === situacaoColo.codigo))) {
                                var colo = svg.find('path[id=colo]');
                                $(colo).find("title").empty();
                                $(colo).css('fill', '#ffffff');
                                delete odontogramaTratamentoJsonDTO.colo;
                            }
                            delete odontogramaTratamentoJsonDTO.raiz;
                        }

                        $(target).find("title").empty();
                        $(target).css('fill', '#ffffff');
                    }

                    // OdontogramFicha.toJSON();
                    OdontogramFicha.dialog.close();
                });

                var _btnReverter = _buttons.find('input[id=reverter]');
                _btnReverter.setVisible = setVisible;
                _btnReverter.click(function(e) {
                    e.preventDefault();
                    var target = _buttons.target;
                    var odontogramaTratamentoJsonDTO = _buttons.odontogramaTratamentoJsonDTO;

                    if (target && odontogramaTratamentoJsonDTO) {
                        var div = $(target).closest('ck-tooth');
                        var markupid = div.attr('id');
                        var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
                        if (wicketComponent) {
                            var url = wicketComponent.callBackURL;
                            OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'restaurar', face: target.id });
                        }
                    }

                    OdontogramFicha.dialog.close();
                });

                _buttons.btnReverter = _btnReverter;
                _buttons.btnAplicar = _btnAplicar;
                _buttons.btnNovo = _btnNovo;

                OdontogramFicha.dialog.buttons = _buttons;
            }

            return OdontogramFicha.dialog.buttons;
        };

        OdontogramFicha.dialog.show = function(target, situacoes) {
            var svg = $(target).closest('svg');

            var offset = svg.offset();
            var left = offset.left;
            var top = offset.top;

            var centerPosition = left - 5;
            var bottomPosition = top + 100;

            this.addClass('on');

            $(this).show();

            this.css('left', centerPosition);
            this.css('top', bottomPosition);

            this.children("fieldset").remove();

            var buttons = this.getButtons();

            var id = $(svg).attr('name');

            var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];
            if (odontogramaTratamentoJsonDTO) {
                var situacao = odontogramaTratamentoJsonDTO[target.id];
                if (situacao) {
                    if (situacao.cadastrado) {
                        if (situacao.concluido) {
                            buttons.btnNovo.setVisible(true);
                            buttons.show(target, odontogramaTratamentoJsonDTO);
                        }
                    } else {
                        buttons.btnReverter.setVisible(true);
                        buttons.btnAplicar.setVisible(true);
                        buttons.show(target, odontogramaTratamentoJsonDTO);
                    }
                } else if (odontogramaTratamentoJsonDTO.inativo) {
                    buttons.btnReverter.setVisible(true);
                    buttons.show(target, odontogramaTratamentoJsonDTO);
                }
            }

            // Balanceia os itens da lista
            function balanceList(size, fact) {
                if (size <= 15) {
                    return size;
                }

                size = Math.round(size / fact);

                return balanceList(size, fact++);
            };

            var size = situacoes.length;
            var i, j, balancedList, chunk = Math.round(size / 2);//balanceList(size, 2);

            var fieldset = $("<fieldset>");
            fieldset.insertBefore(buttons);

            var table = $("<table>");
            for (i = 0, j = situacoes.length; i < j; i += chunk) {
                balancedList = situacoes.slice(i, i + chunk);

                var row = 0;
                balancedList.forEach(function(situacao) {
                    row++;

                    var tr = table.find("tr[row=" + row +"]");
                    if (tr.length === 0) {
                        var tr = $("<tr>");
                        tr.attr('row', row);
                        table.append(tr);
                    }

                    var td = $('<td>');

                    var span = $('<span>');
                        var value = situacao.descricao + (situacao.referencia ? " (" + situacao.referencia + ")" : "");
                        span.append(value);

                    td.append(span);

                    td.click({ situacao: situacao, target: target, svg: svg }, function(e) {
                        e.preventDefault();
                        var dto = e.data;
                        OdontogramFicha.dialog.apply(dto);
                    });

                    tr.append(td);
                });

                fieldset.append(table);
            }
        };

        var close = OdontogramFicha.dialog.find('a[id=close]');
        close.click(function(e) {
            e.preventDefault();
            OdontogramFicha.dialog.close();
        });
    },

    initNotification: function() {
        $.notify.defaults({
            clickToHide : true,
            autoHide : true,
            autoHideDelay : 6500,
            arrowShow : true,
            arrowSize : 5,
            elementPosition : 'top right',
            globalPosition : 'top right',
            className : '',
            showAnimation : 'slideDown',
            showAnimation : 'slideDown',
            showDuration : 400,
            hideAnimation : 'slideUp',
            hideDuration : 200,
            gap : 10
        });
    },

    message: function(component, message) {
        // var notification = $('div[id=notifications]');
        $.notify(
            component,
            message
        );
    },

    setDataAbbreviation: function(svg, ref) {
        var div = $(svg).closest('ck-svg-tooth');
        $(div).attr('data-abbreviation', ref.substring(0, 5));
    },

    setColor: function(path, color) {
        $(path).css('fill', "#" + color);
    },

    addListeners: function(svg) {
        var id = $(svg).attr('name');
        var div = $(svg).closest('ck-tooth');
        var markupid = div.attr('id');

        var linkReverter = div.find('a[id=reverter]');

        linkReverter.click({id: id}, function(e) {
            e.preventDefault();
            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];

            var div = $(this).closest('ck-tooth');
            $(div).removeClass('off');
            $(div).removeAttr('disabled');

            $(this).attr('visible', false);

            var markupid = div.attr('id');
            var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
            if (wicketComponent) {
                var url = wicketComponent.callBackURL;
                OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'reverter' });
            }
        });

        var linkNovaSituacao = div.find('a[id=nova]');
        linkNovaSituacao.click({id: id}, function(e) {
            e.preventDefault();
            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];

            var div = $(this).closest('ck-tooth');

            if (odontogramaTratamentoJsonDTO.status.concluido) {
                $(div).removeClass('off');
                $(div).removeAttr('disabled');

                odontogramaTratamentoJsonDTO.status = null;
                odontogramaTratamentoJsonDTO.colo = null;
                odontogramaTratamentoJsonDTO.raiz = null;
                odontogramaTratamentoJsonDTO.coroa = null;
                odontogramaTratamentoJsonDTO.faces = null;

                OdontogramFicha.reload(odontogramaTratamentoJsonDTO);

                $(this).attr('visible', false);
            } else {
                OdontogramFicha.message(div, "É necessário que a situação deste dente seja concluída ou cancelada para poder selecionar uma nova situação");
            }
        });

        var DTOListener = function(id, tipoAplicacao) {
            this.id = id;
            this.tipoAplicacao = tipoAplicacao;
        };

        var ClickListener = function(e) {
            e.preventDefault();
            var div = $(this).closest('ck-tooth');
            var disabled = $(div).attr('disabled');
            if (disabled) {
                return;
            }

            var dto = e.data;
            OdontogramFicha.showDialog(this, dto);
        };

        var coroa = $(svg).find('path[id=coroa]');
        $(coroa).click(new DTOListener(id, TipoAplicacao.COROA), ClickListener);

        var colo = $(svg).find('path[id=colo]');
        $(colo).click(new DTOListener(id, TipoAplicacao.RAIZ), ClickListener);

        var raiz = $(svg).find('path[id=raiz]');
        $(raiz).click(new DTOListener(id, TipoAplicacao.RAIZ), ClickListener);


        var faces = div.find('ck-svg-faces > svg');
        $(faces).click({id: id}, function(e) {
            e.preventDefault();
            var face = e.target;
            if (face.id == 'faces') {
                return;
            }

            var div = $(this).closest('ck-tooth');
            var disabled = $(div).attr('disabled');
            if (disabled) {
                return;
            }

            var id = e.data.id;
            var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];
            if (!odontogramaTratamentoJsonDTO) {
                odontogramaTratamentoJsonDTO = new OdontogramaTratamentoJsonDTO(id);
                OdontogramFicha.getTratamentos().push(odontogramaTratamentoJsonDTO);
            }

            if (!odontogramaTratamentoJsonDTO.coroa) {
                OdontogramFicha.message(div, "Para selecionar as faces da coroa é necessário que seja definido a situação/tipo de tratamento para a mesma");
                return;
            } else if (odontogramaTratamentoJsonDTO.coroa.concluido) {
                OdontogramFicha.message(div, "O tratamento definido para a coroa deste dente já foi concluído, favor realizar um novo procedimento");
                return;
            }

            if (!odontogramaTratamentoJsonDTO.faces) {
                odontogramaTratamentoJsonDTO.faces = new OdontogramaFacesJsonDTO();
            }

            var status = odontogramaTratamentoJsonDTO.faces[face.id];
            if (!status) {
                $(face).css('fill', 'rgba(239, 83, 80, 1)');
                odontogramaTratamentoJsonDTO.faces[face.id] = StatusFaces.PENDENTE;
            } else {
                if (status === StatusFaces.PENDENTE) {
                    $(face).css('fill', 'rgba(91, 192, 222, 1)');
                    odontogramaTratamentoJsonDTO.faces[face.id] = StatusFaces.CONCLUIDO;
                } else if (status === StatusFaces.CONCLUIDO) {
                    $(face).css('fill', '#ffffff');
                    delete odontogramaTratamentoJsonDTO.faces[face.id];
                }
            }

            if (odontogramaTratamentoJsonDTO.coroa.cadastrado) {
                var markupid = div.attr('id');
                var wicketComponent = $.grep(OdontogramFicha.wicketComponents, function(item) { return item.markupid == markupid})[0];
                if (wicketComponent) {
                    var url = wicketComponent.callBackURL;
                    OdontogramFicha.post(url, odontogramaTratamentoJsonDTO, { action: 'atualizarFaces' });
                }
            } else {
                OdontogramFicha.toJSON();
            }
        });
    },

    showDialog: function(target, dto) {
        OdontogramFicha.dialog.close();
        var id = dto.id;
        var tipoAplicacao = dto.tipoAplicacao;

        var denteSituacao = $.grep(OdontogramFicha.denteSituacaoList, function(denteSituacao) { return denteSituacao.id == id })[0];
        if (denteSituacao) {
            var situacoes = [];
            denteSituacao.situacaoList.forEach(function(codigo) {
                var situacao = $.grep(OdontogramFicha.situacaoList, function(situacao) {
                    return situacao.codigo === codigo && (situacao.desabilitaAoAplicar || !situacao.tipoAplicacao || (!tipoAplicacao || tipoAplicacao === situacao.tipoAplicacao));
                })[0];

                if (situacao) { situacoes.push(situacao); }
            });

            if (situacoes) {
                situacoes.sort(function(oldest, newest) {
                    return oldest.descricao.localeCompare(newest.descricao);
                });

                OdontogramFicha.dialog.show(target, situacoes);
                return;
            }
        }

        var div = $(target).closest('ck-tooth');
        OdontogramFicha.message(div, "Não existe nenhum tratamento/situação configurado para este dente");
    },

    registerWicketComponent: function(markupid, url) {
        var wicketComponent = new WicketComponent(markupid, url);
        OdontogramFicha.wicketComponents.push(wicketComponent);
    },

    post: function(callBackURL, object, extras) {
        OdontogramFicha.lock();

        var attrs = Object.assign({ json: JSON.stringify(object) }, extras);
        Wicket.Ajax.post({
            'u' : callBackURL,
            'ep': attrs
        });
    },

    setEnableArch: function(arch) {
        var enable = true;

        var protese = false;
        var situacaoProtese;
        if ('upper' === arch) {
            if (OdontogramFicha.getOdontogramaDTO().proteseSuperior) {
                enable = false;
                protese = true;
                situacaoProtese = OdontogramFicha.getOdontogramaDTO().situacaoProteseSuperior;
            }
        } else {
            if (OdontogramFicha.getOdontogramaDTO().proteseInferior) {
                enable = false;
                protese = true;
                situacaoProtese = OdontogramFicha.getOdontogramaDTO().situacaoProteseInferior;
            }
        }

        var DIVs = $("ck-tooth[" + arch + "]").get();
        DIVs.forEach(function(div) {
            if (enable) {
                $(div).removeAttr('disabled');
                $(div).removeAttr('status');

                var id = $(div).attr('tooth-id');
                var odontogramaTratamentoJsonDTO = $.grep(OdontogramFicha.getTratamentos(), function(item) { return item.id == id })[0];
                if (odontogramaTratamentoJsonDTO) {
                    OdontogramFicha.reload(odontogramaTratamentoJsonDTO);
                } else {
                    var svg = $(div).find('ck-svg-tooth');
                    $(svg).removeAttr('data-abbreviation');

                    var colo = $(svg).find('path[id=colo]');
                    var raiz = $(svg).find('path[id=raiz]');
                    var coroa = $(svg).find('path[id=coroa]');

                    var faces = $(div).find('ck-svg-faces > svg');
                    var distal = $(faces).find("path[id=distal]");
                    var mesial = $(faces).find("path[id=mesial]");
                    var oclusal = $(faces).find("rect[id=oclusal]");
                    var palatal = $(faces).find("path[id=palatal]");
                    var vestibular = $(faces).find("path[id=vestibular]");

                    var cor = 'FFFFFF';

                    OdontogramFicha.setColor(colo, cor);
                    OdontogramFicha.setColor(raiz, cor);
                    OdontogramFicha.setColor(coroa, cor);

                    OdontogramFicha.setColor(distal, cor);
                    OdontogramFicha.setColor(mesial, cor);
                    OdontogramFicha.setColor(oclusal, cor);
                    OdontogramFicha.setColor(palatal, cor);
                    OdontogramFicha.setColor(vestibular, cor);

                    var titleColo = colo.find("title");
                    var titleRaiz = raiz.find("title");
                    var titleCoroa = coroa.find("title");

                    titleColo.empty();
                    titleRaiz.empty();
                    titleCoroa.empty();
                }
            } else {
                $(div).attr('disabled', true);
                $(div).attr('status', 'off');

                var svg = $(div).find('ck-svg-tooth');
                var cor = 'D3D3D3';
                if (protese) {
                    if (situacaoProtese) {
                        var ref = situacaoProtese.referencia;
                        $(svg).attr('data-abbreviation', ref.substring(0, 5));
                        if (situacaoProtese.cor) {
                            cor = situacaoProtese.cor;
                        }
                    } else {
                        $(svg).attr('data-abbreviation', 'N/I');
                        cor = 'F5F5DC';
                    }
                } else {
                    $(svg).removeAttr('data-abbreviation');
                }

                var colo = $(svg).find('path[id=colo]');
                var raiz = $(svg).find('path[id=raiz]');
                var coroa = $(svg).find('path[id=coroa]');

                var faces = $(div).find('ck-svg-faces > svg');
                var distal = $(faces).find("path[id=distal]");
                var mesial = $(faces).find("path[id=mesial]");
                var oclusal = $(faces).find("rect[id=oclusal]");
                var palatal = $(faces).find("path[id=palatal]");
                var vestibular = $(faces).find("path[id=vestibular]");

                OdontogramFicha.setColor(colo, cor);
                OdontogramFicha.setColor(raiz, cor);
                OdontogramFicha.setColor(coroa, cor);

                OdontogramFicha.setColor(distal, cor);
                OdontogramFicha.setColor(mesial, cor);
                OdontogramFicha.setColor(oclusal, cor);
                OdontogramFicha.setColor(palatal, cor);
                OdontogramFicha.setColor(vestibular, cor);

                var titleColo = colo.find("title");
                var titleRaiz = raiz.find("title");
                var titleCoroa = coroa.find("title");

                titleColo.empty();
                titleRaiz.empty();
                titleCoroa.empty();
            }
        });
    },

    showMessage: function(id, message) { // Dispara mensagem de sucesso após adicionar tratamento/situação no dente.
        var div = $('ck-tooth[tooth-id=' + id + ']');
        OdontogramFicha.message(div, message);
    },

    setJSON: function(json) {
        if (json) {
            OdontogramFicha.odontogramaJsonDTO = JSON.parse(json);
        } else if (OdontogramFicha.odontogramaJsonDTO) {
            delete OdontogramFicha.odontogramaJsonDTO;
        }
    },

    refresh: function(callback) {
        OdontogramFicha.setEnableArch('upper');
        OdontogramFicha.setEnableArch('lower');

        if (callback) {
            callback();
        }
    },

    reload: function(odontogramaTratamentoJsonDTO, callback) {
        var id = odontogramaTratamentoJsonDTO.id;

        var svg = $("svg[name=" + id + "]");
        var div = $(svg).closest('ck-tooth');

        if (odontogramaTratamentoJsonDTO.inativo && odontogramaTratamentoJsonDTO.status) {
            $(div).attr('disabled', true);

            var situacao = odontogramaTratamentoJsonDTO.status;

            var linkReverter = div.find('a[id=reverter]');
            var linkNovaSituacao = div.find('a[id=nova]');

            if (situacao.concluido) {
                linkReverter.attr('visible', false);
                linkNovaSituacao.attr('visible', true);
            } else {
                if (situacao.codigoAtendimento && situacao.codigoAtendimento === OdontogramFicha.codigoAtendimento) {
                    linkReverter.attr('visible', true);
                    linkNovaSituacao.attr('visible', false);
                } else {
                    linkReverter.attr('visible', false);
                    linkNovaSituacao.attr('visible', true);
                }
            }

            OdontogramFicha.setDataAbbreviation(svg, situacao.referencia);

            var cor = situacao.cor;
            if (!cor) {
                cor = 'D3D3D3';
            }
            if (cor) {
                var colo = $(svg).find('path[id=colo]');
                var raiz = $(svg).find('path[id=raiz]');
                var coroa = $(svg).find('path[id=coroa]');

                var faces = div.find('ck-svg-faces > svg');
                var distal = $(faces).find("path[id=distal]");
                var mesial = $(faces).find("path[id=mesial]");
                var oclusal = $(faces).find("rect[id=oclusal]");
                var palatal = $(faces).find("path[id=palatal]");
                var vestibular = $(faces).find("path[id=vestibular]");

                OdontogramFicha.setColor(colo, cor);
                OdontogramFicha.setColor(raiz, cor);
                OdontogramFicha.setColor(coroa, cor);

                OdontogramFicha.setColor(distal, cor);
                OdontogramFicha.setColor(mesial, cor);
                OdontogramFicha.setColor(oclusal, cor);
                OdontogramFicha.setColor(palatal, cor);
                OdontogramFicha.setColor(vestibular, cor);

                var titleColo = colo.find("title");
                var titleRaiz = raiz.find("title");
                var titleCoroa = coroa.find("title");

                titleColo.empty();
                titleColo.append(situacao.descricao);

                titleRaiz.empty();
                titleRaiz.append(situacao.descricao);

                titleCoroa.empty();
                titleCoroa.append(situacao.descricao);
            }
        } else {
            var linkReverter = div.find('a[id=reverter]');
            var linkNovaSituacao = div.find('a[id=nova]');

            linkReverter.attr('visible', false);
            linkNovaSituacao.attr('visible', false);

            $(div).removeClass('off');
            $(div).removeAttr('disabled');

            var situacaoCoroa = odontogramaTratamentoJsonDTO.coroa;
            var coroa = $(svg).find('path[id=coroa]');
            coroa.css('fill', '#ffffff');
            if (situacaoCoroa) {
                OdontogramFicha.setDataAbbreviation(svg, situacaoCoroa.referencia);
                var title = coroa.find("title");
                title.empty();
                title.append(situacaoCoroa.descricao);
            } else {
                var svgTooth = $(svg).closest('ck-svg-tooth')
                $(svgTooth).removeAttr('data-abbreviation');
                var title = coroa.find("title");
                title.empty();
            }

            var situacaoColo = odontogramaTratamentoJsonDTO.colo;
            var colo = $(svg).find('path[id=colo]');
            if (situacaoColo) {
                if (situacaoColo.concluido) {
                    colo.css('fill', 'rgba(91, 192, 222, 1)');
                } else {
                    OdontogramFicha.setColor(colo, situacaoColo.cor);
                }
                var title = colo.find("title");
                title.empty();
                title.append(situacaoColo.descricao);
            } else {
                colo.find("title").empty();
                colo.css('fill', '#ffffff');
            }

            var situacaoRaiz = odontogramaTratamentoJsonDTO.raiz;
            var raiz = $(svg).find('path[id=raiz]');
            if (situacaoRaiz) {
                if (situacaoRaiz.concluido) {
                    raiz.css('fill', 'rgba(91, 192, 222, 1)');
                } else {
                    OdontogramFicha.setColor(raiz, situacaoRaiz.cor);
                }
                var title = raiz.find("title");
                title.empty();
                title.append(situacaoRaiz.descricao);
            } else {
                raiz.find("title").empty();
                raiz.css('fill', '#ffffff');
            }

            var faces = odontogramaTratamentoJsonDTO.faces;
            var divFaces = $(div).find('ck-svg-faces > svg');
            if (faces) {
                var reloadFace = function(face) {
                    var svgFace = $(divFaces).find("path[id=" + face + "]");
                    if (face === 'oclusal') {
                        svgFace = $(divFaces).find("rect[id=" + face + "]");
                    }

                    var status = faces[face];
                    if (status) {
                        if (status === StatusFaces.PENDENTE) {
                            $(svgFace).css('fill', 'rgba(239, 83, 80, 1)');
                        } else {
                            $(svgFace).css('fill', 'rgba(91, 192, 222, 1)');
                        }
                    } else {
                        $(svgFace).css('fill', '#ffffff');
                    }
                };

                reloadFace('palatal');
                reloadFace('vestibular');
                reloadFace('distal');
                reloadFace('mesial');
                reloadFace('oclusal');
            } else {
                odontogramaTratamentoJsonDTO.faces = {};

                $(divFaces).find("path[id=palatal]").css('fill', '#ffffff');
                $(divFaces).find("path[id=vestibular]").css('fill', '#ffffff');
                $(divFaces).find("path[id=distal]").css('fill', '#ffffff');
                $(divFaces).find("path[id=mesial]").css('fill', '#ffffff');
                $(divFaces).find("rect[id=oclusal]").css('fill', '#ffffff');
            }
        }

        if (callback) {
            callback();
        }
    },

    getOdontogramaDTO: function() {
        if (!OdontogramFicha.odontogramaJsonDTO) {
            OdontogramFicha.odontogramaJsonDTO = {
                proteseSuperior : false,
                proteseInferior : false,
                tratamentos     : []
            };
        }

        return OdontogramFicha.odontogramaJsonDTO;
    },

    getTratamentos: function() {
        var tratamentos = OdontogramFicha.getOdontogramaDTO().tratamentos;
        return tratamentos;
    },

    toJSON: function() {
        var url = OdontogramFicha.callBackURLPanel;
        OdontogramFicha.post(url, OdontogramFicha.getOdontogramaDTO());
    },

};