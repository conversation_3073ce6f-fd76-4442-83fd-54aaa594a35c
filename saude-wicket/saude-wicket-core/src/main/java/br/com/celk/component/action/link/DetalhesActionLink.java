package br.com.celk.component.action.link;

import br.com.celk.system.bundle.BundleManager;

/**
 *
 * <AUTHOR>
 */
public class DetalhesActionLink extends AbstractLinkPanel {

    public DetalhesActionLink(String id) {
        super(id);
        init();
    }

    private void init() {
//        getBtnAction();
    }
    
    @Override
    public String getCustomTitle() {
        return BundleManager.getString("detalhar");
    }

    @Override
    public String getCustomClass() {
        return "icon zoom";
    }
}
