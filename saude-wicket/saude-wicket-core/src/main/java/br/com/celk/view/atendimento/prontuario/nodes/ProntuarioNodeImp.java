package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

/**
 *
 * <AUTHOR>
 */
public abstract class ProntuarioNodeImp implements IProntuarioNode{
    
    private Atendimento atendimento;

    @Override
    public Boolean validar(Atendimento atendimento) {
        return true;
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.DOCUMENT_LAYOUT;
    }

    @Override
    public final NodesAtendimentoRef getIdentificador() {
        if(this.getClass().isAnnotationPresent(ProntuarioNode.class)){
            return this.getClass().getAnnotation(ProntuarioNode.class).value();
        }
        return null;
    }

    @Override
    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    @Override
    public Atendimento getAtendimento() {
        return atendimento;
    }
}
