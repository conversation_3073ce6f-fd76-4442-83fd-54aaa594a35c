package br.com.celk.view.atendimento.prontuario.documentos;

import br.com.celk.report.HtmlReport;
import br.com.celk.report.HtmlReportTemplateType;
import br.com.celk.report.templatebuilder.IHtmlTemplateBuilder;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoAtendimento;

/**
 *
 * <AUTHOR>
 */
public class DocumentoHtmlReportAssinatura extends HtmlReport{

    private DocumentoAtendimento documentoAtendimento;
    private Profissional profissional;
    private UsuarioCadsus usuarioCadsus;

    public DocumentoHtmlReportAssinatura(DocumentoAtendimento documentoAtendimento, Profissional profissional, UsuarioCadsus usuarioCadsus) {
        super(HtmlReportTemplateType.CAB_ROD_TIMBRADO_ASSINATURA);
        this.documentoAtendimento = documentoAtendimento;
        this.profissional = profissional;
        this.usuarioCadsus = usuarioCadsus;
    }
    
    @Override
    public void customizeReport(IHtmlTemplateBuilder templateBuilder) {
        templateBuilder.addParameter("CONTENT", documentoAtendimento.getDescricao());
        if (profissional != null && usuarioCadsus != null) {
            String value = "Este documento foi emitido eletronicamente em " + Data.formatarDataHora(DataUtil.getDataAtual()) + " para o paciente " + usuarioCadsus.getNome() + " pelo profissional " + profissional.getNome() + " – (" + profissional.getConselhoClasse().getCodigo() + ") " + profissional.getConselhoClasse().getDescricao() +
                    ". A autenticidade da assinatura digital deste documento pode ser verificada acessando https://validar.iti.gov.br/ e fazendo o upload deste arquivo, ou escaneando o QR Code abaixo com um dispositivo móvel.";
            templateBuilder.addParameter("FOOTER_ASSINATURA", value);
        } else {
            templateBuilder.addParameter("FOOTER_ASSINATURA", "");
        }
    }

}
