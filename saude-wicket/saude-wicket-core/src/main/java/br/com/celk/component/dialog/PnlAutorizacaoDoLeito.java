package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ReservaLeito;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.OcorrenciaAih;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlAutorizacaoDoLeito extends Panel {

    private CompoundPropertyModel<AutorizacaoInternacaoHospitalarDTO> model;

    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnExcluirReserva;
    private RequiredInputArea txtMotivo;
    private AutorizacaoInternacaoHospitalarDTO aihDto;
    private String motivo;
    private RequiredDateChooser dataInicial;
    private DateChooser dataPrevisaoAlta;
    private HoraMinutoField horaInicioField;
    private HoraMinutoField horaAltaField;
    private PageableTable table;
    private List<ReservaLeito> reservaLeitoList = new ArrayList<ReservaLeito>();
    private boolean isBtnExcluir = false;
    private boolean ocorrencia = false;
    private DlgConfirmacaoSimNao dlgConfirmacaoExclusao;

    public PnlAutorizacaoDoLeito(String id) {
        super(id);
        init();
    }

    public PnlAutorizacaoDoLeito(String id, AutorizacaoInternacaoHospitalarDTO aihDto) {
        super(id);
        this.aihDto = aihDto;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", model = new CompoundPropertyModel(new AutorizacaoInternacaoHospitalarDTO()));
        AutorizacaoInternacaoHospitalarDTO proxy = on(AutorizacaoInternacaoHospitalarDTO.class);
        form.add(new DisabledInputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getNome())));
        form.add(new DisabledInputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getEmpresa().getDescricao())));
        form.add(new DisabledInputField<String>(path(proxy.getProcedimentoLeito().getId().getProcedimentoTipoLeito().getDescricao())));
        form.add(new DisabledInputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().getDescricao())));
        form.add(new DisabledInputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().getDescricaoTipoLeito())));
        form.add(new DisabledInputField<String>(path(proxy.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().getDescricaoSexo())));
        form.add(dataInicial = new RequiredDateChooser(path(proxy.getDataInicial())));
        dataInicial.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));
        form.add(dataPrevisaoAlta = new DateChooser(path(proxy.getDataPrevisaoAlta())));
        dataPrevisaoAlta.add(new Tooltip().setText("msgCampoVazioDataAlta"));

        form.add(table = new PageableTable("table", getColumns(), getPagerProviderInstance()));

        form.add(horaInicioField = new HoraMinutoField(path(proxy.getHoraInicial())));
        form.add(horaAltaField = new HoraMinutoField(path(proxy.getHoraAlta())));

        dataInicial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dataInicial.getComponentValue() != null) {
                    dataPrevisaoAlta.getData().setMinDate(new DateOption(dataInicial.getComponentValue()));
                    target.add(dataPrevisaoAlta);
                }
                dataPrevisaoAlta.add(new Tooltip().setText("msgCampoVazioDataAlta"));
            }
        });

        form.add(txtMotivo = new RequiredInputArea<String>("motivo", new PropertyModel(this, "motivo")));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ajustarDatas(model.getObject());
                validarDiferencaDias(model.getObject());
                validarDataMenorQueAtual(model.getObject().getDataInicial());
                validarReservaLeito(model.getObject(), reservaLeitoList);
                onConfirmar(target, motivo, model.getObject());

                boolean geraNumeracaoAIH = false;

                try {
                    geraNumeracaoAIH = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("GeraNumeracaoAIH").equals(Aih.AUTORIZACAO_AIH);
                } catch (DAOException e) {
                    Loggable.log.error(e);
                }

                ReservaLeito reserva;
                if (model.getObject().getReservaLeito() == null && !Aih.Status.AGENDADA.value().equals(model.getObject().getAutorizacaoInternacaoHospitalar().getStatus())) {
                    reserva = criarReservaLeito(model.getObject());
                } else {
                    carregaLeitoQuarto(model.getObject());
                    geraOcorrenciaAihEditadaExcluida(model.getObject().getAutorizacaoInternacaoHospitalar(), model.getObject().getMotivo(), model.getObject().getDataInicial(), model.getObject().getDataPrevisaoAlta());
                    reserva = editarReservaLeito(model.getObject());
                }
                BOFactoryWicket.save(reserva);
                limpar(target);
                if (!geraNumeracaoAIH) {
                    onFechar(target);
                }
                if (model.getObject().getReservaLeito() != null) {
                    onEditar(target, aihDto);
                }
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
                limpar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        form.add(btnExcluirReserva = new AbstractAjaxButton("btnExcluirReserva") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                if (dlgConfirmacaoExclusao == null) {
                    WindowUtil.addModal(target, this, dlgConfirmacaoExclusao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), bundle("msgConfirmacaoExclusaoReserva")) {
                        @Override
                        public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                            ocorrencia = true;
                            geraOcorrenciaAihEditadaExcluida(model.getObject().getAutorizacaoInternacaoHospitalar(), motivo, model.getObject().getDataInicial(), model.getObject().getDataPrevisaoAlta());
                            aihDto.getAutorizacaoInternacaoHospitalar().setStatus(Aih.Status.AUTORIZADA.value());
                            aihDto.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().setSituacao(LeitoQuarto.Situacao.LIBERADO.value());
                            BOFactoryWicket.save(aihDto.getAutorizacaoInternacaoHospitalar());
                            onExcluir(target, aihDto);
                        }

                        @Override
                        public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        }
                    });
                }
                dlgConfirmacaoExclusao.show(target);
            }
        });
        if (aihDto != null) {
            btnExcluirReserva.setEnabled(aihDto.isBtnExcluir());
        } else {
            btnExcluirReserva.setEnabled(false);
        }

        add(form);
        txtMotivo.add(new AttributeModifier("maxlength", getMaxLengthMotivo()));
    }

    private void ajustarDatas(AutorizacaoInternacaoHospitalarDTO dto) {
        if (dto.getDataPrevisaoAlta() == null) {
            dto.setDataPrevisaoAlta(dto.getDataInicial());
        }

        if (dto.getHoraInicial() == null) {
            dto.setHoraInicial(DataUtil.zerarHora(DataUtil.getDataAtual()));
        }

        if (dto.getHoraAlta() == null) {
            dto.setHoraAlta(DataUtil.finalHora(DataUtil.getDataAtual()));
        }

        dto.setDataInicial(inserirHoraNaData(dto.getDataInicial(), dto.getHoraInicial()));
        dto.setDataPrevisaoAlta(inserirHoraNaData(dto.getDataPrevisaoAlta(), dto.getHoraAlta()));
    }

    private void validarReservaLeito(AutorizacaoInternacaoHospitalarDTO dto, List<ReservaLeito> reservaLeitoList) throws ValidacaoException {
        for (ReservaLeito listaEstadia : reservaLeitoList) {
            boolean isReservaLeitoNull = model.getObject().getReservaLeito() == null;
            boolean isAgendada = Aih.Status.AGENDADA.value().equals(model.getObject().getAutorizacaoInternacaoHospitalar().getStatus());

            if ((isReservaLeitoNull && !isAgendada) || (!listaEstadia.getCodigo().equals(model.getObject().getReservaLeito() != null ? model.getObject().getReservaLeito().getCodigo() : null))) {
                if (dto.getDataInicial().before(listaEstadia.getDataAlta()) &&
                        dto.getDataPrevisaoAlta().after(listaEstadia.getDataInicio())) {
                    throwExceptionReserva(listaEstadia);
                }

                if (dto.getDataInicial().equals(listaEstadia.getDataInicio()) &&
                        dto.getDataPrevisaoAlta().equals(listaEstadia.getDataAlta())) {
                    throwExceptionReserva(listaEstadia);
                }
            }
        }
    }

    private void validarDataMenorQueAtual(Date dataInicial) throws ValidacaoException {
        if (dataInicial.before(DataUtil.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("msgDiaInicialMenorQueDataAtual"));
        }
    }

    private void throwExceptionReserva(ReservaLeito reservaLeito) throws ValidacaoException {
        throw new ValidacaoException(Bundle.getStringApplication("msgReservaLeito",
                reservaLeito.getDataInicio(),
                reservaLeito.getDataAlta(),
                reservaLeito.getAih().getCodigo(),
                reservaLeito.getAih().getUsuarioCadSus().getNome()));
    }

    private void validarDiferencaDias(AutorizacaoInternacaoHospitalarDTO dto) throws ValidacaoException {
        if (dto.getDataPrevisaoAlta() != null) {
            if (dto.getDataInicial().after(dto.getDataPrevisaoAlta()) || dto.getDataInicial().equals(dto.getDataPrevisaoAlta())) {
                throw new ValidacaoException(Bundle.getStringApplication("msgDiaAltaMenorIgualQueDiaInicial"));
            }
        }
    }

    private ReservaLeito criarReservaLeito(AutorizacaoInternacaoHospitalarDTO dto) throws DAOException {
        ReservaLeito reserva = new ReservaLeito();
        LeitoQuarto leitoQuarto = (LeitoQuarto) HibernateSessionFactory.getSession().get(LeitoQuarto.class, dto.getAutorizacaoInternacaoHospitalar().getLeitoQuarto().getCodigo());
        reserva.setLeitoQuarto(leitoQuarto);
        reserva.setAih(dto.getAutorizacaoInternacaoHospitalar());
        reserva.setDataAlta(dto.getDataPrevisaoAlta());
        reserva.setDataInicio(dto.getDataInicial());
        return reserva;
    }

    private ReservaLeito editarReservaLeito(AutorizacaoInternacaoHospitalarDTO dto) throws DAOException {
        ReservaLeito reserva;
        if (dto.getReservaLeito() != null) {
            reserva = dto.getReservaLeito();
            reserva.setDataAlta(dto.getDataPrevisaoAlta());
            reserva.setDataInicio(dto.getDataInicial());
        } else {
            reserva = new ReservaLeito();
            reserva.setDataAlta(model.getObject().getDataPrevisaoAlta());
            reserva.setDataInicio(model.getObject().getDataInicial());
            reserva.setAih(model.getObject().getAutorizacaoInternacaoHospitalar());
            reserva.setLeitoQuarto(model.getObject().getAutorizacaoInternacaoHospitalar().getLeitoQuarto());
        }

        return reserva;
    }

    private Date inserirHoraNaData(Date dataModificacao, Date tempo) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(dataModificacao);
        calendar.set(Calendar.HOUR_OF_DAY, DataUtil.getHora(tempo));
        calendar.set(Calendar.MINUTE, DataUtil.getMinuto(tempo));
        calendar.set(Calendar.SECOND, DataUtil.getSegundo(tempo));
        return calendar.getTime();
    }

    private void geraOcorrenciaAihEditadaExcluida(Aih aih, String observacao, Date dataInicial, Date dataPrevisaoAlta) throws DAOException, ValidacaoException {
        OcorrenciaAih ocorrenciaAih = new OcorrenciaAih();
        ocorrenciaAih.setDataCadastro(DataUtil.getDataAtual());
        ocorrenciaAih.setUsuarioCadastro(br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario());
        ocorrenciaAih.setDescricao(getDescricaoOcorrenciaAutorizacaoAih(aih, observacao, dataInicial, dataPrevisaoAlta));
        ocorrenciaAih.setAih(aih);
        BOFactoryWicket.save(ocorrenciaAih);
    }

    private String getDescricaoOcorrenciaAutorizacaoAih(Aih aih, String observacao, Date dataInicial, Date dataPrevisaoAlta) throws DAOException {
        StringBuilder stringBuilder = new StringBuilder();

        if (!ocorrencia) {
            stringBuilder.append(BundleManager.getString("aihAgendadaEditada"));
        } else {
            stringBuilder.append(BundleManager.getString("aihAgendadaExcluida"));
        }

        stringBuilder.append(BundleManager.getString("estabelecimentoX", aih.getEstabelecimentoAutorizado().getDescricaoFormatado()));

        if (geraNumeracaoAih()) {
            stringBuilder.append(", ");
            stringBuilder.append(BundleManager.getString("nAutorizacaoAihX", aih.getNroAutorizacao()));
        }

        if (aih.getLeitoQuarto() != null) {
            stringBuilder.append(", ");
            stringBuilder.append(BundleManager.getString("leitoX", aih.getLeitoQuarto().getDescricao()));

            if (aih.getLeitoQuarto().getQuartoInternacao() != null) {
                stringBuilder.append(", ");
                stringBuilder.append(BundleManager.getString("descricaoX", aih.getLeitoQuarto().getQuartoInternacao().getDescricao()));
            }

            stringBuilder.append(", ");
            stringBuilder.append(BundleManager.getString("tipoLeitoX", aih.getLeitoQuarto().getDescricaoTipoLeito()));
            stringBuilder.append(", ");
            stringBuilder.append(BundleManager.getString("sexoX", aih.getLeitoQuarto().getDescricaoSexo()));
        }

        stringBuilder.append(", ");
        stringBuilder.append(BundleManager.getString("observacaoX", observacao));
        stringBuilder.append(", ");
        stringBuilder.append(BundleManager.getString("dataInicioX", DataUtil.getFormatarDiaMesAno(dataInicial)));
        stringBuilder.append(", ");
        stringBuilder.append(BundleManager.getString("dataPrevisaoAltaX", DataUtil.getFormatarDiaMesAno(dataPrevisaoAlta)));
        stringBuilder.append(";");

        return stringBuilder.toString();
    }

    private boolean geraNumeracaoAih() throws DAOException {
        return BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("GeraNumeracaoAIH").equals(Aih.AUTORIZACAO_AIH);
    }

    private List<ISortableColumn> getColumns() {
        List<ISortableColumn> columns = new ArrayList<ISortableColumn>();
        ReservaLeito proxy = on(ReservaLeito.class);
        columns.add(createSortableColumn(BundleManager.getString("dataInicio"), proxy.getDataInicioComHora()));
        columns.add(createSortableColumn(BundleManager.getString("dataPrevisaoAlta"), proxy.getDataPrevisaoAltaComHora()));
        columns.add(createSortableColumn(BundleManager.getString("aih"), proxy.getAih().getCodigo()));
        return columns;
    }

    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ReservaLeito.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ReservaLeito.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(ReservaLeito.PROP_DATA_INICIO),
                                VOUtils.montarPath(ReservaLeito.PROP_DATA_ALTA),
                                VOUtils.montarPath(ReservaLeito.PROP_AIH),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ReservaLeito.PROP_DATA_INICIO), true);
            }
        };
    }

    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        Date dataAtual = DataUtil.getDataAtual();
        parameters.add(new QueryCustom.QueryCustomParameter(ReservaLeito.PROP_DATA_ALTA, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, dataAtual));
        parameters.add(new QueryCustom.QueryCustomParameter(ReservaLeito.PROP_AIH, BuilderQueryCustom.QueryParameter.IS_NOT_NULL));
        if (aihDto != null) {
            LeitoQuarto leitoQuarto = aihDto.getAutorizacaoInternacaoHospitalar().getLeitoQuarto();
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReservaLeito.PROP_LEITO_QUARTO, LeitoQuarto.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, leitoQuarto.getCodigo()));
        }

        return parameters;
    }

    private void carregaReservaLeitos(AutorizacaoInternacaoHospitalarDTO aihDto) {
        Date dataAtual = DataUtil.getDataAtual();
        LeitoQuarto leitoQuarto = aihDto.getAutorizacaoInternacaoHospitalar().getLeitoQuarto();
        reservaLeitoList = LoadManager.getInstance(ReservaLeito.class)
                .addProperties(new HQLProperties(ReservaLeito.class).getProperties())
                .addProperty(VOUtils.montarPath(ReservaLeito.PROP_AIH, Aih.PROP_USUARIO_CAD_SUS, UsuarioCadsus.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(ReservaLeito.PROP_DATA_ALTA, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, dataAtual))
                .addParameter(new QueryCustom.QueryCustomParameter(ReservaLeito.PROP_AIH, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReservaLeito.PROP_LEITO_QUARTO, LeitoQuarto.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, leitoQuarto.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(ReservaLeito.PROP_DATA_INICIO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }

    private void carregaLeitoQuarto(AutorizacaoInternacaoHospitalarDTO aihDto) {
        if (aihDto.getAutorizacaoInternacaoHospitalar().getLeitoQuarto() == null) {
            LeitoQuarto leitoQuarto = LoadManager.getInstance(LeitoQuarto.class)
                    .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LeitoQuarto.PROP_AIH, Aih.PROP_CODIGO), aihDto.getAutorizacaoInternacaoHospitalar().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_SITUACAO, LeitoQuarto.Situacao.OCUPADO.value()))
                    .start().getVO();

            aihDto.getAutorizacaoInternacaoHospitalar().setLeitoQuarto(leitoQuarto);
        }
    }

    public abstract Long getMaxLengthMotivo();

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public abstract void onExcluir(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public abstract void onEditar(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO object) throws ValidacaoException, DAOException;

    public void setObject(AjaxRequestTarget target, AutorizacaoInternacaoHospitalarDTO aihDto) {
        limpar(target);
        carregaReservaLeitos(aihDto);
        this.model.setObject(aihDto);
        this.aihDto = aihDto;
        table.getDataProvider().setParameters(getParameters());
        table.populate();
        target.add(this);
    }

    public void setObjectEditar(AjaxRequestTarget target, String motivo, AutorizacaoInternacaoHospitalarDTO aihDto) {
        limpar(target);
        carregaReservaLeitos(aihDto);
        this.model.setObject(aihDto);
        this.aihDto = aihDto;
        this.aihDto.setMotivo(motivo);
        table.getDataProvider().setParameters(getParameters());
        table.populate();
        target.add(this);
    }

    public void limpar(AjaxRequestTarget target) {
        //model.setObject(new AutorizacaoInternacaoHospitalarDTO());
        txtMotivo.limpar(target);
        motivo = null;
        target.add(this);
    }
}