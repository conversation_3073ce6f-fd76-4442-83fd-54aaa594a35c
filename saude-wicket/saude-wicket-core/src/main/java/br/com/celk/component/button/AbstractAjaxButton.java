package br.com.celk.component.button;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import net.sf.jasperreports.engine.JRException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.IModel;

import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public abstract class AbstractAjaxButton extends AjaxButton{

    public AbstractAjaxButton(String id) {
        super(id);
    }

    public AbstractAjaxButton(String id, IModel<String> model) {
        super(id, model);
    }
    
    @Override
    protected final void onSubmit(AjaxRequestTarget target, org.apache.wicket.markup.html.form.Form<?> form) {
        try {
            onAction(target, (Form) form);
        } catch (ValidacaoRuntimeException exception) {
            MessageUtil.modalWarn(target, this, exception);
        } catch (ValidacaoException exception) {
                MessageUtil.modalWarn(target, this, exception);
            JScript.setDirtyForm(target, getForm());
        } catch (Throwable exception) {
            Loggable.log.error(exception.getMessage(), exception);
            MessageUtil.modalError(target, this, exception);
            JScript.setDirtyForm(target, getForm());
        } finally{
            update(target);
        }
    }

    private void update(AjaxRequestTarget target) {
        INotificationPanel findParent = findParent(INotificationPanel.class);

        if (findParent != null) {
            findParent.updateNotificationPanel(target, false);
            target.appendJavaScript(JScript.initMasks());
            target.appendJavaScript(JScript.initExpandLinks());
            target.appendJavaScript(JScript.moveControlsWindow());
            target.appendJavaScript(JScript.removeEnterSubmitFromForm());
        }
    }
    private void updateError(AjaxRequestTarget target) {
        INotificationPanel findParent = findParent(INotificationPanel.class);

        if (findParent != null) {
            findParent.updateNotificationPanel(target);
            target.add(getForm());
            target.appendJavaScript(JScript.scrollToTop());
            target.appendJavaScript(JScript.initMasks());
            target.appendJavaScript(JScript.initTextAreaLimit());
            target.appendJavaScript(JScript.initExpandLinks());
            target.appendJavaScript(JScript.moveControlsWindow());
            target.appendJavaScript(JScript.removeEnterSubmitFromForm());
            target.appendJavaScript(JScript.dirtyForms());
            JScript.setDirtyForm(target, getForm());
        }
    }
    
    @Override
    protected void onError(AjaxRequestTarget target, org.apache.wicket.markup.html.form.Form<?> form) {
        updateError(target);
    }
    
    public abstract void onAction(AjaxRequestTarget target, Form form) throws Exception;

    @Override
    protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
        super.updateAjaxAttributes(attributes);
        attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
    }

}
