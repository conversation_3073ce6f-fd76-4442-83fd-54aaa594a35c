package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoEstratificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlFormularioEstratificacao extends Panel {

    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private Long formulario;
    private final String IMG = "img-info";
    private List<ConfiguracaoEstratificacao.Formulario> listFormulario;

    public PnlFormularioEstratificacao(String id, List<ConfiguracaoEstratificacao.Formulario> listFormulario) {
        super(id);
        this.listFormulario = listFormulario;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        DropDown d = new DropDown("formulario");
        d.addAjaxUpdateValue();
        for (ConfiguracaoEstratificacao.Formulario formulario : listFormulario) {
            d.addChoice(formulario.value(), formulario.descricao());
        }
        form.add(d);

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, formulario);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        add(form);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Long value) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

}
