package br.com.celk.view.materiais.produto.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.entradas.estoque.ProdutoBrasindice;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class ProdutoBrasindiceAutoCompleteSettings extends AbstractAutoCompleteSettings {

    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<String, String>();

        propertiesMap.put("id", o.getIdentificador());
        propertiesMap.put("name", o.getDescricaoVO());

        if (o instanceof ProdutoBrasindice) {
            propertiesMap.put("laboratorio", Coalesce.asString(StringUtils.trimToNull(((ProdutoBrasindice) o).getDescricaoLaboratorioFormatada()), BundleManager.getString("naoInformado")));
            propertiesMap.put("apresentacao", Coalesce.asString(StringUtils.trimToNull(((ProdutoBrasindice) o).getDescricaoApresentacaoFormatada()), BundleManager.getString("naoInformado")));
            propertiesMap.put("tiss", Coalesce.asString(StringUtils.trimToNull(((ProdutoBrasindice) o).getCodigoTiss()), BundleManager.getString("naoInformado")));
            propertiesMap.put("tuss", Coalesce.asString(StringUtils.trimToNull(((ProdutoBrasindice) o).getCodigoTuss()), BundleManager.getString("naoInformado")));
        }

        return propertiesMap;
    }

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();

        builder.append("<li>");
        builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
        builder.append("<div class=\"nivel-1\">'+item.name+'</div>");
        builder.append("<div class=\"nivel-2\" >" + BundleManager.getString("apresentacao") + ": '+item.apresentacao+'</div>");
        builder.append("<div class=\"nivel-2\" >" + BundleManager.getString("laboratorio") + ": '+item.laboratorio+'</div>");
        builder.append("<div class=\"nivel-3\" >" + BundleManager.getString("tiss") + ": '+item.tiss+' | "+BundleManager.getString("tuss") +": '+item.tuss+'</div>");
        builder.append("</div>");
        builder.append("</li>");

        return builder.toString();
    }

}
