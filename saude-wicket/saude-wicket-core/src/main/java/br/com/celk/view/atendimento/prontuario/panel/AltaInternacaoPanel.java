package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.dto.EncaminhamentoAltaDTO;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.interfaces.LoadInterceptorEloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import java.util.List;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AltaInternacaoPanel extends ProntuarioCadastroPanel {

    private IModel<EncaminhamentoAltaDTO> model;
    private DateChooser dchDataAlta;
    private DropDown cbxMotivo;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private WebMarkupContainer containerOrientacoes;
    private InputArea txaOrientacoes;
    private WebMarkupContainer containerHistorico;
    private InputArea txaHistoricoResumido;

    public AltaInternacaoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        Form form = new Form("form", model = new CompoundPropertyModel<EncaminhamentoAltaDTO>(new EncaminhamentoAltaDTO()));

        boolean enabledForm = carregarEloNaturezaTipoEncaminhamento();
        form.setEnabled(enabledForm);

        EncaminhamentoAltaDTO proxy = on(EncaminhamentoAltaDTO.class);

        form.add(new DisabledInputField(path(proxy.getEloNaturezaTipoEncaminhamento().getEncaminhamentoTipo().getDescricao())));
        form.add(dchDataAlta = new DateChooser(path(proxy.getDataAlta())));
        form.add(cbxMotivo = DropDownUtil.getIEnumDropDown(path(proxy.getMotivo()), AtendimentoAlta.MotivoAlta.values(), true));
        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));

        form.add(containerOrientacoes = new WebMarkupContainer("containerOrientacoes"));
        containerOrientacoes.setOutputMarkupId(true);

        containerOrientacoes.add(txaOrientacoes = new InputArea(path(proxy.getOrientacoesAlta())));

        form.add(containerHistorico = new WebMarkupContainer("containerHistorico"));
        containerHistorico.setOutputMarkupId(true);
        containerHistorico.add(txaHistoricoResumido = new InputArea(path(proxy.getHistoricoResumido())));

        add(form);

        if (enabledForm) {
            dchDataAlta.add(new TempBehaviorV2());
            cbxMotivo.add(new TempBehaviorV2());
            autoCompleteConsultaCid.add(new TempBehaviorV2());
            txaOrientacoes.add(new TempBehaviorV2());
            txaHistoricoResumido.add(new TempBehaviorV2());
            form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), EncaminhamentoAltaDTO.class)));
        }
    }

    private boolean carregarEloNaturezaTipoEncaminhamento() {
        List<EloNaturezaTipoEncaminhamento> elos = LoadManager.getInstance(EloNaturezaTipoEncaminhamento.class)
                .addProperties(new HQLProperties(EloNaturezaTipoEncaminhamento.class).getProperties())
                .addProperties(new HQLProperties(EncaminhamentoTipo.class, EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO).getProperties())
                .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class, EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoAtendimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), getAtendimento().getNaturezaProcuraTipoAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_VISIVEL), RepositoryComponentDefault.SIM_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_TIPO), EncaminhamentoTipo.Tipo.ALTA.value()))
                .addInterceptor(new LoadInterceptorEloNaturezaTipoEncaminhamento(getAtendimento().getEmpresa()))
                .start().getList();

        if (elos.isEmpty()) {
            warn(BundleManager.getString("msgNaoConfiguradoTipoEncaminhamentoAlta"));
            return false;
        } else {
            model.getObject().setEloNaturezaTipoEncaminhamento(elos.get(0));
        }

        return true;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnLoadHeaderItem.forScript(JScript.initExpandLinks()));

        if (model.getObject().getOrientacoesAlta() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerOrientacoes)));
        }

        if (model.getObject().getHistoricoResumido() != null) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerHistorico)));
        }
    }
}
