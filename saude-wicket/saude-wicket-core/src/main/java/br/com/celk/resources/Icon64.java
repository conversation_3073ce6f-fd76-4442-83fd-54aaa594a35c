package br.com.celk.resources;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public enum Icon64 implements Serializable, Icons {
    ball_blue("ball_blue64"),
    ball_green("ball_green64"),
    ball_gray("ball_gray64"),
    ball_red("ball_red64"),
    ball_orange("ball_orange64"),
    ball_yellow("ball_yellow64"),;

    private String clazz;

    private Icon64(String clazz) {
        this.clazz = clazz;
    }

    @Override
    public String clazz() {
        return this.clazz;
    }
}
