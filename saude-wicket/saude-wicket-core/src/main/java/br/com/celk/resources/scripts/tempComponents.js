
(function ($) {
    
    var defaultSettings= {
        onBind: function(input){},
        getValue: function(input){},
        getFormId: function(input){},
        onSaveSuccess: function(input){}
    };
    
    var methods = {
        init: function(options) {
            var settings = $.extend({}, defaultSettings, options || {});

            return this.each(function () {
                $(this).data("settings", settings);
                $(this).data("tempComponent", new $.TempComponent(this));
            });
        },
        getValue: function() {
            return this.data("tempComponent").getValue();
        },
        
        save: function() {
            this.data("tempComponent").save();
            return this;
        },
        onSaveSuccess: function() {
            this.data("tempComponent").onSaveSuccess();
            return this;
        },
        addInterval: function(intervalId) {
            this.data("tempComponent").addInterval(intervalId);
            return this;
        },
        getInterval: function() {
            return this.data("tempComponent").getInterval();
        }
    }
    
    $.fn.tempComponent = function(method){
        if(methods[method]) {
            return methods[method].apply(this, Array.prototype.slice.call(arguments, 1));
        } else {
            return methods.init.apply(this, arguments);
        }
    };
    
    $.TempComponent = function (input){
        var intervals = {};
        $(this).data("intervals", intervals);

        $(input).data("settings").onBind(input);

        this.getValue = function(){
            return $(input).data("settings").getValue(input);
        }
        this.save= function(){
            $(getFormId(input)).tempForm('save', input);
        }
        this.onSaveSuccess= function(){
            $(input).data("settings").onSaveSuccess(input);
        }
        this.addInterval= function(intervalId){
            var componentId = input.id;
            if(componentId in intervals){
                clearInterval(intervals[componentId]);
            }
            intervals[componentId] = intervalId;
        }
        this.getInterval= function(){
            var componentId = input.id;
            if(componentId in intervals){
                return intervals[componentId];
            }
            return null;
        }
        
        function getFormId(input){
            return $(input).data("settings").getFormId(input);
        }
    };
    
}(jQuery));
