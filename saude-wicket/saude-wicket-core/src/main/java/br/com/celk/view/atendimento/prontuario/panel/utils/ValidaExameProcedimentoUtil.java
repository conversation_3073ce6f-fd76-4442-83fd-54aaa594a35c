package br.com.celk.view.atendimento.prontuario.panel.utils;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

public class ValidaExameProcedimentoUtil {

    public static void getExamesPendentes(Object tipoExame, Atendimento atendimento, ExameProcedimentoDTO dto) throws DAOException, ValidacaoException {
        ExameRequisicao proxy = Lambda.on(ExameRequisicao.class);
        List<Exame> listaExames = new ArrayList<Exame>();

        if (tipoExame instanceof ExameBpai) {
            ExameBpai proxyBpai = Lambda.on(ExameBpai.class);
            List<ExameBpai> examesBpai = LoadManager.getInstance(ExameBpai.class)
                    .addProperty(VOUtils.montarPath(ExameBpai.PROP_EXAME, Exame.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyBpai.getExame().getAtendimento().getUsuarioCadsus().getCodigo()), QueryCustom.QueryCustomParameter.IGUAL, atendimento.getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyBpai.getExame().getStatus()), QueryCustom.QueryCustomParameter.IGUAL, Exame.STATUS_SOLICITADO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxyBpai.getExame().getOrigem()), QueryCustom.QueryCustomParameter.IGUAL, Exame.Origem.BPAI.value()))
                    .start().getList();

            if (!CollectionUtils.isAllEmpty(examesBpai)) {
                for (ExameBpai exameBpa : examesBpai) {
                    listaExames.add(exameBpa.getExame());
                }
            }

        } else if (tipoExame instanceof ExameApac) {
            ExameApac exameApac = Lambda.on(ExameApac.class);
            List<ExameApac> examesApac = LoadManager.getInstance(ExameApac.class)
                    .addProperty(VOUtils.montarPath(ExameApac.PROP_EXAME, Exame.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(exameApac.getExame().getAtendimento().getUsuarioCadsus().getCodigo()), QueryCustom.QueryCustomParameter.IGUAL, atendimento.getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(exameApac.getExame().getStatus()), QueryCustom.QueryCustomParameter.IGUAL, Exame.STATUS_SOLICITADO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(exameApac.getExame().getOrigem()), QueryCustom.QueryCustomParameter.IGUAL, Exame.Origem.LAUDO_APAC.value()))
                    .start().getList();

            if (!CollectionUtils.isAllEmpty(examesApac)) {
                for (ExameApac exameApa : examesApac) {
                    listaExames.add(exameApa.getExame());
                }
            }
        }

        if (!CollectionUtils.isAllEmpty(listaExames)) {
            List<ExameRequisicao> examesRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), QueryCustom.QueryCustomParameter.IGUAL, ExameRequisicao.Status.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getCodigo()), QueryCustom.QueryCustomParameter.DIFERENTE, atendimento.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getAtendimento().getUsuarioCadsus().getCodigo()), QueryCustom.QueryCustomParameter.IGUAL, atendimento.getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getExame().getTipoExame().getTipo()), QueryCustom.QueryCustomParameter.DIFERENTE, RepositoryComponentDefault.Tipo.LACEN.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_EXAME, QueryCustom.QueryCustomParameter.IN, listaExames)).start().getList();

            if (CollectionUtils.isNotNullEmpty(examesRequisicao)) {
                validarExameRequisicao(dto, examesRequisicao);
            }
        }
    }

    private static void validarExameRequisicao(ExameProcedimentoDTO dto, List<ExameRequisicao> examesPendentes) throws DAOException, ValidacaoException {

        ExameProcedimento exameProcedimento = (ExameProcedimento) HibernateSessionFactory.getSession().createCriteria(ExameProcedimento.class)
                .add(Restrictions.eq(ExameProcedimento.PROP_CODIGO, dto.getExameProcedimento().getCodigo()))
                .uniqueResult();

        for (ExameRequisicao ep : examesPendentes) {
            if (ep.getExameProcedimento().getCodigo().equals(exameProcedimento.getCodigo())) {
                if (exameProcedimento.getTipoExame() != null
                        && exameProcedimento.getTipoExame().getTipoProcedimento() != null
                        && TipoProcedimento.TipoValidacao.BLOQUEIO.value().equals(exameProcedimento.getTipoExame().getTipoProcedimento().getFlagBloqueiaPendente())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msgBloqueiaCasoExisteProcedimentoPendente", dto.getExameProcedimento().getDescricaoFormatado()));
                }
            }
        }
    }
}
