package br.com.celk.view.materiais.produto.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.QueryConsultaMedicamentoCatmatDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR> 26/04/2018
 */
public class RestricaoContainerDominioMedicamentoCatmat extends Panel implements IRestricaoContainer<QueryConsultaMedicamentoCatmatDTOParam> {

    private InputField<String> txtDescricao;

    private QueryConsultaMedicamentoCatmatDTOParam param = new QueryConsultaMedicamentoCatmatDTOParam();

    public RestricaoContainerDominioMedicamentoCatmat(String id) {
        super(id);

        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));

        root.add(txtDescricao = new InputField<String>("descricao"));

        add(root);
    }

    @Override
    public QueryConsultaMedicamentoCatmatDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }
    
}
