package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.soapenfermagem.SoapEnfermagemPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.SOAP_ENFERMAGEM)
public class SoapEnfermagemNode extends ProntuarioNodeImp{

    @Override
    public DefaultProntuarioPanel getPanel(String id) {
        return new SoapEnfermagemPanel(id, getTitulo());
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.DOCUMENT_PREPARE;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("consultaEnfermagem");
    }

}
