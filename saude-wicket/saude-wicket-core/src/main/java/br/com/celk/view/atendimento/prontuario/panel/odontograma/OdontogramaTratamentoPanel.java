package br.com.celk.view.atendimento.prontuario.panel.odontograma;

import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.notification.INotificationPanel;
import br.com.celk.component.odontograma.OdontogramaPanel;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.unidadesaude.odontograma.dto.OdontogramaJsonDTO;
import br.com.celk.unidadesaude.odontograma.dto.OdontogramaTratamentoJsonDTO;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.atendimento.prontuario.panel.odontograma.customcolumn.OdontogramaTratamentoColumnPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.tratamentoodontologico.AtendimentoOdontoPlanoDTO;
import br.com.celk.view.prontuario.situacaodente.autocomplete.AutoCompleteConsultaSituacaoDente;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.ConsultaAtendimentoOdontoPlanoDTOParam;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.TecidosMoles;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class OdontogramaTratamentoPanel extends ProntuarioCadastroPanel {

    private boolean fichaUnica;
    private boolean existeFichaClinica;

    private AtendimentoOdontoFicha atendimentoOdontoFicha;
    private List<AtendimentoOdontoPlano> atendimentoOdontoPlanoList;

    private Form<AtendimentoOdontoPlanoDTO> form;

    private WebMarkupContainer containerTratamento;
    private WebMarkupContainer containerEvolucao;
    private WebMarkupContainer containerDente;
    private WebMarkupContainer containerSextante;
    private WebMarkupContainer containerArcada;
    private WebMarkupContainer containerTecidosMoles;
    private WebMarkupContainer containerOutro;

    private CheckBox cbxDistal;
    private CheckBox cbxMesial;
    private CheckBox cbxOclusal;
    private CheckBox cbxPalatal;
    private CheckBox cbxVestibular;

    private CompoundPropertyModel<AtendimentoOdontoPlano> model;

    private Table<AtendimentoOdontoPlano> tblAtendimentoOdontoPlano;
    private DropDown dropDownUrgente;
    private InputArea txtObservacao;
    private DropDown<Dente> dropDownDente;
    private DropDown<TecidosMoles> dropDownTecidosMoles;
    private DropDown<AtendimentoOdontoFicha> cbxDropDownFichaOdonto;
    private AutoCompleteConsultaSituacaoDente autoCompleteConsultaSituacaoDente;
    private DropDown<Long> cbxFace;
    private AbstractAjaxButton btnAdicionar;
    private DropDown<Long> dropDownManutencao;
    private OdontogramaPanel odontogramaPanel;

    public OdontogramaTratamentoPanel(String id, String titulo) {
        super(id, titulo);
    }

    public OdontogramaTratamentoPanel(String id, String titulo, AtendimentoOdontoFicha atendimentoOdontoFicha) {
        super(id, titulo);
        this.atendimentoOdontoFicha = atendimentoOdontoFicha;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        carregarTratamentoUrgencia();
        carregarFichaClinica();

        form = new Form("form", model = new CompoundPropertyModel(new AtendimentoOdontoPlano()));

        AtendimentoOdontoPlano proxy = on(AtendimentoOdontoPlano.class);

        form.add(getDropDownOndontoFicha(path(proxy.getAtendimentoOdontoFicha())).setEnabled(existeFichaClinica));
        cbxDropDownFichaOdonto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                atendimentoOdontoFicha = cbxDropDownFichaOdonto.getModelObject();
                odontogramaPanel.setFichaOdonto(target.getHeaderResponse(), atendimentoOdontoFicha);

                if (atendimentoOdontoFicha != null) {
                    odontogramaPanel.refresh(target.getHeaderResponse(), true);
                    carregarListaPlanoTratamento(false);
                    containerTratamento.setEnabled(true);
                } else {
                    odontogramaPanel.refresh(target.getHeaderResponse(), false);
                    carregarListaPlanoTratamento(true);
                    containerTratamento.setEnabled(false);
                }

                tblAtendimentoOdontoPlano.populate(target);
                target.add(containerTratamento);
                odontogramaPanel.setEnableProtese(target, containerTratamento.isEnabled());
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading(false));
            }
        });

        form.add(dropDownUrgente = DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagUrgente())));
        dropDownUrgente.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onActionUrgente(target);
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading(false));
            }
        });

        form.add(dropDownManutencao = DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagManutencao())));
        dropDownManutencao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onActionManutencao(target);
            }
        });
//        if (existeTratamentoUrgencia) {
////            form.getModel().getObject().getAtendimentoOdontoPlano().setFlagUrgente(RepositoryComponentDefault.SIM_LONG);
//        }
//        if (CollectionUtils.isNotNullEmpty(Coalesce.asList(atendimentoOdontoPlanoList))) {
////            dropDownUrgente.setEnabled(false);
//        }

        form.add(odontogramaPanel = new OdontogramaPanel("odontograma", getProntuarioController()) {
            @Override
            public void updateTable(AjaxRequestTarget target) {
                carregarListaPlanoTratamento(false);
                tblAtendimentoOdontoPlano.update(target);
            }
        });

        addContainerTratamento(proxy);

        if (!existeFichaClinica) {
            containerTratamento.setEnabled(false);
            odontogramaPanel.setEnableProtese(null, false);
            cbxDropDownFichaOdonto.setEnabled(false);
            dropDownUrgente.setEnabled(true);
            dropDownManutencao.setEnabled(true);
        } else if (!fichaUnica && atendimentoOdontoFicha == null) {
            containerTratamento.setEnabled(false);
            odontogramaPanel.setEnableProtese(null, false);
        }

        add(form);
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        if (atendimentoOdontoFicha != null) {
            cbxDropDownFichaOdonto.setComponentValue(atendimentoOdontoFicha);
        }
    }

    private void addContainerTratamento(AtendimentoOdontoPlano proxy) {
        form.add(containerTratamento = new WebMarkupContainer("containerTratamento"));
        containerTratamento.setOutputMarkupId(true);

        containerTratamento.add(containerEvolucao = new WebMarkupContainer("containerEvolucao"));
        containerEvolucao.setOutputMarkupId(true);

        RadioButtonGroup radioButtonGroup = new RadioButtonGroup(path(proxy.getTipoEvolucao()));
        radioButtonGroup.add(new AjaxRadio("dente", new Model(AtendimentoOdontoPlano.Tipo.DENTE.value())));
        radioButtonGroup.add(new AjaxRadio("sextante", new Model(AtendimentoOdontoPlano.Tipo.SEXTANTE.value())));
        radioButtonGroup.add(new AjaxRadio("arcada", new Model(AtendimentoOdontoPlano.Tipo.ARCADA.value())));
        radioButtonGroup.add(new AjaxRadio("tecidosMoles", new Model(AtendimentoOdontoPlano.Tipo.TECIDOS_MOLES.value())));
        radioButtonGroup.add(new AjaxRadio("outro", new Model(AtendimentoOdontoPlano.Tipo.OUTRO.value())));

        radioButtonGroup.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                onChangeTipoEvolucao(target);
            }
        });

        containerEvolucao.add(radioButtonGroup);

        containerEvolucao.add(containerDente = new WebMarkupContainer("containerDente"));
        containerDente.setOutputMarkupId(true);
        containerDente.add(getDropDownDente(path(proxy.getDente())));

        containerDente.add(cbxDistal = new CheckBox("distal", new Model<Boolean>()));
        containerDente.add(cbxOclusal = new CheckBox("oclusal", new Model<Boolean>()));
        containerDente.add(cbxMesial = new CheckBox("mesial", new Model<Boolean>()));
        containerDente.add(cbxPalatal = new CheckBox("palatal", new Model<Boolean>()));
        containerDente.add(cbxVestibular = new CheckBox("vestibular", new Model<Boolean>()));

        containerEvolucao.add(containerSextante = new WebMarkupContainer("containerSextante"));
        containerSextante.setOutputMarkupId(true);
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS1())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS2())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS3())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS4())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS5())));
        containerSextante.add(new CheckBoxLongValue(path(proxy.getSextanteS6())));

        containerEvolucao.add(containerArcada = new WebMarkupContainer("containerArcada"));
        containerArcada.setOutputMarkupId(true);
        containerArcada.add(new CheckBoxLongValue(path(proxy.getArcadaInferior())));
        containerArcada.add(new CheckBoxLongValue(path(proxy.getArcadaSuperior())));

        containerEvolucao.add(containerTecidosMoles = new WebMarkupContainer("containerTecidosMoles"));
        containerTecidosMoles.setOutputMarkupId(true);
        containerTecidosMoles.add(getDropDownTecidosMoles(path(proxy.getTecidosMoles())));

        containerEvolucao.add(containerOutro = new WebMarkupContainer("containerOutro"));
        containerOutro.setOutputMarkupId(true);
        containerOutro.add(new InputField(path(proxy.getOutro())));

        containerTratamento.add(tblAtendimentoOdontoPlano = new Table("tblAtendimentoOdontoPlano", getColumns(), getCollectionProvider()));
        tblAtendimentoOdontoPlano.populate();
//        tblAtendimentoOdontoPlano.setEnabled(enabledForm || existeTratamentoUrgencia);

        model.getObject().setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());
        onChangeTipoEvolucao(null);

        containerTratamento.add(autoCompleteConsultaSituacaoDente = new AutoCompleteConsultaSituacaoDente(path(proxy.getSituacaoDente())));
        containerTratamento.add(txtObservacao = new InputArea(path(proxy.getObservacao())));
        containerTratamento.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (cbxDropDownFichaOdonto.getModelObject() == null
                        && RepositoryComponentDefault.NAO_LONG.equals(dropDownUrgente.getComponentValue())
                        && RepositoryComponentDefault.NAO_LONG.equals(dropDownManutencao.getComponentValue())) {
                    throw new ValidacaoException(bundle("msgEscolhaFichaOdontoContinuar"));
                }
                adicionarSalvar(target);
            }
        });
    }

    private void onChangeTipoEvolucao(AjaxRequestTarget target) {
        containerDente.setVisible(false);
        containerSextante.setVisible(false);
        containerArcada.setVisible(false);
        containerTecidosMoles.setVisible(false);
        containerOutro.setVisible(false);

        Long tipoEvolucao = model.getObject().getTipoEvolucao();
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao)) {
            containerDente.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.SEXTANTE.value().equals(tipoEvolucao)) {
            containerSextante.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)) {
            containerArcada.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.TECIDOS_MOLES.value().equals(tipoEvolucao)) {
            containerTecidosMoles.setVisible(true);
        } else if (AtendimentoOdontoPlano.Tipo.OUTRO.value().equals(tipoEvolucao)) {
            containerOutro.setVisible(true);
        }

        if (target != null) {
            target.add(containerEvolucao);
        }
    }

    private void updateNotification(AjaxRequestTarget target) {
        INotificationPanel findNotificationPanel = MessageUtil.findNotificationPanel(OdontogramaTratamentoPanel.this);
        if (findNotificationPanel != null) {
            getSession().getFeedbackMessages().clear();
            if (target != null) {
                findNotificationPanel.updateNotificationPanel(target, false);
            }
        }
    }

    private void onActionUrgente(AjaxRequestTarget target) {
        atendimentoOdontoFicha = null;
//        ComponentUtils.limparContainer(containerTratamento, target, dropDownUrgente, dropDownManutencao);

        containerTratamento.setEnabled(false);
        odontogramaPanel.setEnableProtese(target, false);

        if (RepositoryComponentDefault.SIM_LONG.equals(dropDownUrgente.getComponentValue())) {
            carregarTratamentoUrgencia();

            odontogramaPanel.setFichaOdonto(target.getHeaderResponse(), null);
            odontogramaPanel.setTratamentoUrgente(target.getHeaderResponse(), true);
            odontogramaPanel.refresh(target.getHeaderResponse(), true);

            containerTratamento.setEnabled(true);

            cbxDropDownFichaOdonto.setEnabled(false);
            cbxDropDownFichaOdonto.removeAllChoices();
            cbxDropDownFichaOdonto.limpar(target);
        } else if (RepositoryComponentDefault.NAO_LONG.equals(dropDownUrgente.getComponentValue())) {
            carregarFichaClinica();
            carregarOdontoFicha();

            tblAtendimentoOdontoPlano.update(target);
            odontogramaPanel.setTratamentoUrgente(target.getHeaderResponse(), false);

            if (existeFichaClinica) {
                cbxDropDownFichaOdonto.setEnabled(true);
                target.add(cbxDropDownFichaOdonto);
            } else {
                cbxDropDownFichaOdonto.setEnabled(false);

                dropDownUrgente.setEnabled(true);
                dropDownManutencao.setEnabled(true);

                target.add(cbxDropDownFichaOdonto);
                target.add(dropDownUrgente);
                target.add(dropDownManutencao);
            }

            if (atendimentoOdontoFicha != null) {
                containerTratamento.setEnabled(true);
                odontogramaPanel.setFichaOdonto(target.getHeaderResponse(), atendimentoOdontoFicha);
                odontogramaPanel.refresh(target.getHeaderResponse(), true);
            } else {
                odontogramaPanel.setFichaOdonto(target.getHeaderResponse(), null);
                odontogramaPanel.refresh(target.getHeaderResponse(), false);
            }
        }

        ComponentUtils.limparContainer(containerTratamento, target);
        odontogramaPanel.setEnableProtese(target, containerTratamento.isEnabled());

        getSession().getFeedbackMessages().clear();
        updateNotification(target);
    }

    private void onActionManutencao(AjaxRequestTarget target) {
        atendimentoOdontoFicha = null;
        cbxDropDownFichaOdonto.limpar(target);
//        ComponentUtils.limparContainer(containerTratamento, target, dropDownUrgente, dropDownManutencao);
//        odontogramaPanel.setFichaOdonto(target, null);

        if (RepositoryComponentDefault.SIM_LONG.equals(dropDownManutencao.getComponentValue())) {
            carregarOdontoFicha();

            cbxDropDownFichaOdonto.setEnabled(true);
            containerTratamento.setEnabled(true);
        }

        target.add(cbxDropDownFichaOdonto);
        target.add(containerTratamento);

        getSession().getFeedbackMessages().clear();
        updateNotification(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        AtendimentoOdontoPlano proxy = on(AtendimentoOdontoPlano.class);
        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("local"), proxy.getDescricaoLocalFormatado()));
        columns.add(createColumn(bundle("situacaoTratamento"), proxy.getSituacaoDente().getDescricaoFormatado()));
        columns.add(createColumn(bundle("status"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("urgente"), proxy.getDescricaoUrgente()));
        columns.add(createColumn(bundle("manutencao"), proxy.getDescricaoManutencao()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacao()));

        return columns;
    }

    private CustomColumn<AtendimentoOdontoPlano> getCustomColumn() {
        return new CustomColumn<AtendimentoOdontoPlano>() {
            @Override
            public Component getComponent(String componentId, final AtendimentoOdontoPlano atendimentoOdontoPlano) {
                return new OdontogramaTratamentoColumnPanel(componentId, getAtendimento(), atendimentoOdontoPlano) {
                    @Override
                    public void registrarTrabalho(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, boolean edit) {
                        getProntuarioController().changePanel(target, new EvoluirDentePanel(getProntuarioController().panelId(), atendimentoOdontoPlano.getCodigo(), edit));
                    }

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        if (RepositoryComponentDefault.SIM_LONG.equals(dropDownUrgente.getComponentValue())) {
                            carregarListaPlanoTratamento(true);
                        } else {
                            carregarListaPlanoTratamento(false);
                        }

                        tblAtendimentoOdontoPlano.update(target);
                    }

                    @Override
                    public void excluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException {
                        OdontogramaTratamentoPanel.this.cancelarExcluirPlanoTratamento(target, atendimentoOdontoPlano);
                    }

                    @Override
                    public void cancelarPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, String motivo) throws ValidacaoException, DAOException {
                        OdontogramaTratamentoPanel.this.cancelarExcluirPlanoTratamento(target, atendimentoOdontoPlano, motivo);
                    }
                };
            }
        };
    }

    private void cancelarExcluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException, DAOException {
        cancelarExcluirPlanoTratamento(target, atendimentoOdontoPlano, null, true);
    }

    private void cancelarExcluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, String motivo) throws ValidacaoException, DAOException {
        cancelarExcluirPlanoTratamento(target, atendimentoOdontoPlano, motivo, false);
    }

    private void cancelarExcluirPlanoTratamento(AjaxRequestTarget target, AtendimentoOdontoPlano atendimentoOdontoPlano, String motivo, boolean excluir) throws ValidacaoException, DAOException {
        Long codigoOdontoPlano = atendimentoOdontoPlano.getCodigo();
        Long tipoEvolucao = atendimentoOdontoPlano.getTipoEvolucao();

        if (excluir) {
            BOFactoryWicket.delete(atendimentoOdontoPlano);
        } else {
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.CANCELADO.value());
            atendimentoOdontoPlano.setDataCancelamento(DataUtil.getDataAtual());
            atendimentoOdontoPlano.setMotivoCancelamento(motivo);
            atendimentoOdontoPlano.setAtendimentoCancelamento(getAtendimento());
            BOFactoryWicket.save(atendimentoOdontoPlano);
        }

        OdontogramaJsonDTO odontogramaJsonDTO = odontogramaPanel.getOdontogramaJsonDTO();
        if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)) {
            boolean refreshOdontograma = false;
            if (odontogramaJsonDTO.getSituacaoProteseSuperior() != null && codigoOdontoPlano.equals(odontogramaJsonDTO.getSituacaoProteseSuperior().getCodigoOdontoPlano())) {
                refreshOdontograma = true;
                odontogramaJsonDTO.setProteseSuperior(false);
                odontogramaJsonDTO.setSituacaoProteseSuperior(null);
            } else if (odontogramaJsonDTO.getSituacaoProteseInferior() != null && codigoOdontoPlano.equals(odontogramaJsonDTO.getSituacaoProteseInferior().getCodigoOdontoPlano())) {
                refreshOdontograma = true;
                odontogramaJsonDTO.setProteseInferior(false);
                odontogramaJsonDTO.setSituacaoProteseInferior(null);
            }

            if (refreshOdontograma) {
                odontogramaPanel.setOdontogramaJsonDTO(odontogramaJsonDTO);
                odontogramaPanel.saveJSON(target);
                odontogramaPanel.refresh(target.getHeaderResponse(), true);
            }
        } else if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao)) {
            if (CollectionUtils.isNotNullEmpty(odontogramaJsonDTO.getTratamentos())) {
                OdontogramaTratamentoJsonDTO odontogramaTratamentoJsonDTO = Lambda.selectFirst(odontogramaJsonDTO.getTratamentos(), Lambda.having(on(OdontogramaTratamentoJsonDTO.class).getId(), Matchers.equalTo(String.valueOf(atendimentoOdontoPlano.getDente().getCodigo()))));
                if (odontogramaTratamentoJsonDTO != null) {
                    if (odontogramaTratamentoJsonDTO.getStatus() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getStatus().getCodigoOdontoPlano())) {
                        odontogramaPanel.definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, null);
                    } else if (odontogramaTratamentoJsonDTO.getCoroa() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getCoroa().getCodigoOdontoPlano())) {
                        odontogramaPanel.definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "coroa");
                    } else if (odontogramaTratamentoJsonDTO.getColo() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getColo().getCodigoOdontoPlano())) {
                        odontogramaPanel.definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "colo");
                    } else if (odontogramaTratamentoJsonDTO.getRaiz() != null && codigoOdontoPlano.equals(odontogramaTratamentoJsonDTO.getRaiz().getCodigoOdontoPlano())) {
                        odontogramaPanel.definirProximoTratamentoDente(target, odontogramaTratamentoJsonDTO, "raiz");
                    }
                }
            }
        }

    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return atendimentoOdontoPlanoList;
            }
        };
    }

    private DropDown<Dente> getDropDownDente(String id) {
        dropDownDente = new DropDown(id);
        dropDownDente.addChoice(null, "");

        List<Dente> denteList = LoadManager.getInstance(Dente.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Dente.PROP_NOME))
                .start().getList();

        for (Dente dente : denteList) {
            dropDownDente.addChoice(dente, dente.getNome());
        }

        return dropDownDente;
    }

    private DropDown<TecidosMoles> getDropDownTecidosMoles(String id) {
        dropDownTecidosMoles = new DropDown(id);
        dropDownTecidosMoles.addChoice(null, "");

        List<TecidosMoles> tecidosMolesList = LoadManager.getInstance(TecidosMoles.class)
                .addSorter(new QueryCustom.QueryCustomSorter(TecidosMoles.PROP_DESCRICAO))
                .start().getList();

        for (TecidosMoles tecidosMoles : tecidosMolesList) {
            dropDownTecidosMoles.addChoice(tecidosMoles, StringUtil.getStringMaxPrecision(tecidosMoles.getDescricao(),120));
        }

        return dropDownTecidosMoles;
    }

    private DropDown<AtendimentoOdontoFicha> getDropDownOndontoFicha(String id) {
        cbxDropDownFichaOdonto = new DropDown(id);

        carregarOdontoFicha();

        return cbxDropDownFichaOdonto;
    }

    private void carregarOdontoFicha() {
        if (!fichaUnica) {
            List<AtendimentoOdontoFicha> atendimentoOdontoFichaList = LoadManager.getInstance(AtendimentoOdontoFicha.class)
                    .addProperties(new HQLProperties(AtendimentoOdontoFicha.class).getProperties())
                    .addProperty(VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoFicha.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AtendimentoOdontoFicha.Status.PENDENTE.value(), AtendimentoOdontoFicha.Status.EM_ANDAMENTO.value())))
                    .start().getList();

            cbxDropDownFichaOdonto.addChoice(null, "");
            for (AtendimentoOdontoFicha ficha : atendimentoOdontoFichaList) {
                StringBuilder sb = new StringBuilder(ficha.getAtendimento().getEmpresa().toString());
                sb.append(" ");
                sb.append(DataUtil.getFormatarDiaMesAno(ficha.getDataInicioTratamento()));
                cbxDropDownFichaOdonto.addChoice(ficha, sb.toString());
            }
        } else {
            if (atendimentoOdontoFicha != null) {
                StringBuilder sb = new StringBuilder(atendimentoOdontoFicha.getAtendimento().getEmpresa().toString());
                sb.append(" ");
                sb.append(DataUtil.getFormatarDiaMesAno(atendimentoOdontoFicha.getDataInicioTratamento()));
                cbxDropDownFichaOdonto.addChoice(atendimentoOdontoFicha, sb.toString());
            }
            cbxDropDownFichaOdonto.setEnabled(false);
        }
    }

    private void adicionarSalvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AtendimentoOdontoPlano atendimentoOdontoPlano = model.getObject();
        validarCampos(atendimentoOdontoPlano);
        if (atendimentoOdontoFicha == null) {
            carregarFichaClinica();
        }
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(atendimentoOdontoPlano.getTipoEvolucao())
                && RepositoryComponentDefault.NAO_LONG.equals(dropDownUrgente.getComponentValue())) {
            boolean existeTratamentoPendente = LoadManager.getInstance(AtendimentoOdontoPlano.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_DENTE, atendimentoOdontoPlano.getDente()))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_FACE, atendimentoOdontoPlano.getFace()))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, atendimentoOdontoPlano.getSituacaoDente()))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, atendimentoOdontoFicha))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AtendimentoOdontoPlano.Status.PENDENTE.value(), AtendimentoOdontoPlano.Status.EM_ANDAMENTO.value())))
                    .exists();

            if (existeTratamentoPendente) {
                throw new ValidacaoException(BundleManager.getString("msgExisteTratamentoAbertoDenteFaceSituacao"));
            }
        }

        atendimentoOdontoPlano.setAtendimento(getAtendimento());
        atendimentoOdontoPlano.setDataCadastro(DataUtil.getDataAtual());

        boolean urgente = RepositoryComponentDefault.SIM_LONG.equals(dropDownUrgente.getComponentValue());

        if (!urgente) {
            atendimentoOdontoPlano.setAtendimentoOdontoFicha(atendimentoOdontoFicha);
        }

        if (SituacaoDente.TipoSituacao.PROCEDIMENTO.value().equals(atendimentoOdontoPlano.getSituacaoDente().getTipoSituacao())) {
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.PENDENTE.value());
        } else {
            atendimentoOdontoPlano.setAtendimentoExecucao(getAtendimento());
            atendimentoOdontoPlano.setStatus(AtendimentoOdontoPlano.Status.HISTORICO.value());
        }

        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(atendimentoOdontoPlano.getTipoEvolucao())) {
            List facesList = new ArrayList();

            if (Boolean.TRUE.equals(cbxPalatal.getModelObject())) {
                atendimentoOdontoPlano.setFacePalatal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("P");
            }

            if (Boolean.TRUE.equals(cbxVestibular.getModelObject())) {
                atendimentoOdontoPlano.setFaceVestibular(RepositoryComponentDefault.NAO_LONG);
                facesList.add("V");
            }

            if (Boolean.TRUE.equals(cbxDistal.getModelObject())) {
                atendimentoOdontoPlano.setFaceDistal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("D");
            }

            if (Boolean.TRUE.equals(cbxMesial.getModelObject())) {
                atendimentoOdontoPlano.setFaceMesial(RepositoryComponentDefault.NAO_LONG);
                facesList.add("M");
            }

            if (Boolean.TRUE.equals(cbxOclusal.getModelObject())) {
                atendimentoOdontoPlano.setFaceOclusal(RepositoryComponentDefault.NAO_LONG);
                facesList.add("O");
            }

            if (!facesList.isEmpty()) {
                String faces = Lambda.join(facesList, "-");
                atendimentoOdontoPlano.setFaces(faces);
            }

            odontogramaPanel.adicionarTratamento(target, atendimentoOdontoPlano);
        } else {
            BOFactoryWicket.save(atendimentoOdontoPlano);
        }

        cbxPalatal.setModelObject(Boolean.FALSE);
        cbxVestibular.setModelObject(Boolean.FALSE);
        cbxDistal.setModelObject(Boolean.FALSE);
        cbxMesial.setModelObject(Boolean.FALSE);
        cbxOclusal.setModelObject(Boolean.FALSE);

        ComponentUtils.limparContainer(containerTratamento, target);

        onChangeTipoEvolucao(target);

        AtendimentoOdontoPlano newAtendimentoOdontoPlano = new AtendimentoOdontoPlano();
        newAtendimentoOdontoPlano.setTipoEvolucao(AtendimentoOdontoPlano.Tipo.DENTE.value());

        model.setObject(newAtendimentoOdontoPlano);

        if (urgente) {
            newAtendimentoOdontoPlano.setFlagUrgente(RepositoryComponentDefault.SIM_LONG);
            carregarListaPlanoTratamento(true);
        } else {
            carregarFichaClinica();
            if (!existeFichaClinica) {
                cbxDropDownFichaOdonto.setEnabled(false);
                dropDownUrgente.setEnabled(true);
                dropDownManutencao.setEnabled(true);

                containerTratamento.setEnabled(false);
                target.add(containerTratamento);
                odontogramaPanel.setEnableProtese(target, containerTratamento.isEnabled());
            }
        }

        tblAtendimentoOdontoPlano.update(target);
    }

    private void validarCampos(AtendimentoOdontoPlano atendimentoOdontoPlano) throws ValidacaoException {
        ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();

        Long tipoEvolucao = atendimentoOdontoPlano.getTipoEvolucao();
        if (AtendimentoOdontoPlano.Tipo.DENTE.value().equals(tipoEvolucao) && atendimentoOdontoPlano.getDente() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("dente")));
        }

        if (AtendimentoOdontoPlano.Tipo.SEXTANTE.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getSextanteS1() == null
                && atendimentoOdontoPlano.getSextanteS2() == null
                && atendimentoOdontoPlano.getSextanteS3() == null
                && atendimentoOdontoPlano.getSextanteS4() == null
                && atendimentoOdontoPlano.getSextanteS5() == null
                && atendimentoOdontoPlano.getSextanteS6() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("sextante")));
        }

        if (AtendimentoOdontoPlano.Tipo.ARCADA.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getArcadaInferior() == null
                && atendimentoOdontoPlano.getArcadaSuperior() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("arcada")));
        }

        if (AtendimentoOdontoPlano.Tipo.OUTRO.value().equals(tipoEvolucao)
                && atendimentoOdontoPlano.getOutro() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("outro")));
        }

        if (atendimentoOdontoPlano.getSituacaoDente() == null) {
            validacaoProcesso.add(bundle("campoXObrigatorio", bundle("situacaoTratamento")));
        }

        if (!validacaoProcesso.getMensagemList().isEmpty()) {
            throw new ValidacaoException(validacaoProcesso);
        }
    }

    private void carregarFichaClinica() {
        List<AtendimentoOdontoFicha> atendimentoOdontoFichaList = LoadManager.getInstance(AtendimentoOdontoFicha.class)
                .addProperties(new HQLProperties(AtendimentoOdontoFicha.class).getProperties())
                .addProperty(VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoFicha.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AtendimentoOdontoFicha.Status.PENDENTE.value(), AtendimentoOdontoFicha.Status.EM_ANDAMENTO.value())))
                .start().getList();

        if (CollectionUtils.isEmpty(atendimentoOdontoFichaList)) {
            this.existeFichaClinica = false;
            warn(BundleManager.getString("msgNaoFoiEncontradoFichaClinicaPaciente"));
        } else {
            this.existeFichaClinica = true;

            if (atendimentoOdontoFichaList.size() == 1) {
                atendimentoOdontoFicha = atendimentoOdontoFichaList.get(0);
                carregarListaPlanoTratamento(false);
                fichaUnica = true;
            } else {
                fichaUnica = false;
                if (atendimentoOdontoFicha == null) {
                    warn(BundleManager.getString("msgEscolhaFichaOdontoContinuar"));
                }
            }
        }
    }

    private void carregarListaPlanoTratamento(boolean apenasUrgentes) {
        ConsultaAtendimentoOdontoPlanoDTOParam param = new ConsultaAtendimentoOdontoPlanoDTOParam();
        param.setAtendimento(getAtendimento());
        param.setAtendimentoOdontoFicha(atendimentoOdontoFicha);
        param.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());

        param.setApenasUrgentes(apenasUrgentes);
        if (!apenasUrgentes && dropDownUrgente != null && RepositoryComponentDefault.SIM_LONG.equals(dropDownUrgente.getComponentValue())) {
            param.setApenasUrgentes(true);
        }

        try {
            atendimentoOdontoPlanoList = BOFactoryWicket.getBO(AtendimentoFacade.class).consultarAtendimentoOdontoPlano(param);
        } catch (DAOException | ValidacaoException ex) {
            Logger.getLogger(OdontogramaTratamentoPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private boolean carregarTratamentoUrgencia() {
        carregarListaPlanoTratamento(true);
        return !Coalesce.asList(atendimentoOdontoPlanoList).isEmpty();
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        odontogramaPanel.setFichaOdonto(response, atendimentoOdontoFicha);

        if (atendimentoOdontoFicha == null && RepositoryComponentDefault.NAO_LONG.equals(dropDownUrgente.getComponentValue())) {
            odontogramaPanel.setTratamentoUrgente(response, false);
            odontogramaPanel.refresh(response, false);
        } else {
            if (RepositoryComponentDefault.SIM_LONG.equals(dropDownUrgente.getComponentValue())) {
                odontogramaPanel.setTratamentoUrgente(response, true);
            }

            odontogramaPanel.refresh(response, true);
        }

    }
}
