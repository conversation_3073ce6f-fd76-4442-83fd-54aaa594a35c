package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.temp.behavior.TempBehavior;
import br.com.celk.component.temp.behavior.TempFormBehavior;
import br.com.celk.component.temp.interfaces.TempHelper;
import br.com.celk.component.temp.interfaces.impl.DefaultTempStoreStrategy;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.DataUtil;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.dto.EncaminhamentoAltaDTO;
import br.com.ksisolucoes.bo.prontuario.web.encaminhamento.interfaces.LoadInterceptorEloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import java.util.List;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import static ch.lambdaj.Lambda.*;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.Arrays;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AltaPanel extends ProntuarioCadastroPanel{

    private IModel<EncaminhamentoAltaDTO> model;
    private DropDown<EloNaturezaTipoEncaminhamento> cbxEncaminhamentos;
    private InputField txtDataAlta;
    private DropDown cbxMotivo;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private DropDown<Boolean> cbxSalvarCid;
    private WebMarkupContainer containerObservacaoPatologia;
    private InputArea txaObservacao;
    
    public AltaPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form", model = new CompoundPropertyModel<EncaminhamentoAltaDTO>(new EncaminhamentoAltaDTO()));
        
        EncaminhamentoAltaDTO proxy = on(EncaminhamentoAltaDTO.class);
        
        form.add(getCbxEncaminhamentos(path(proxy.getEloNaturezaTipoEncaminhamento())));
        form.add(txtDataAlta = new InputField(path(proxy.getDataAlta())));
        form.add(cbxMotivo = DropDownUtil.getIEnumDropDown(path(proxy.getMotivo()), AtendimentoAlta.MotivoAlta.values(), true));
        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));
        
        form.add(containerObservacaoPatologia = new WebMarkupContainer("containerObservacaoPatologia"));
        containerObservacaoPatologia.setOutputMarkupId(true);
        
        containerObservacaoPatologia.add(cbxSalvarCid = DropDownUtil.getNaoSimBooleanDropDown(path(proxy.getSalvarCidPrincipaisPatologias())));
        containerObservacaoPatologia.add(txaObservacao = new InputArea(path(proxy.getObservacaoPatologia())));
        
        add(form);
        
        autoCompleteConsultaCid.add(new ConsultaListener<Cid>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cid object) {
                avaliarCidPrincipal(target, object);
            }
        });
        
        autoCompleteConsultaCid.add(new RemoveListener<Cid>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cid object) {
                avaliarCidPrincipal(target, null);
            }
        });
        
        cbxSalvarCid.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarSalvaCid(target);
            }
            
        });
        
        form.add(new TempFormBehavior(new DefaultTempStoreStrategy(getAtendimento(), getIdentificador().toString(), EncaminhamentoAltaDTO.class)));
        txtDataAlta.add(new TempBehavior());
        cbxMotivo.add(new TempBehavior());
        autoCompleteConsultaCid.add(new TempBehavior());
        cbxSalvarCid.add(new TempBehavior());
        txaObservacao.add(new TempBehavior());
        
        cbxSalvarCid.setEnabled(false);
        txaObservacao.setEnabled(false);
        avaliarCidPrincipal(null, model.getObject().getCid());
        avaliarSalvaCid(null);
        
        enableAltaFields();
    }
    
    private DropDown getCbxEncaminhamentos(String id){
        if (this.cbxEncaminhamentos == null) {
            this.cbxEncaminhamentos = new DropDown(id);
                List<EloNaturezaTipoEncaminhamento> elos = LoadManager.getInstance(EloNaturezaTipoEncaminhamento.class)
                        .addProperties(new HQLProperties(EloNaturezaTipoEncaminhamento.class).getProperties())
                        .addProperties(new HQLProperties(EncaminhamentoTipo.class, EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO).getProperties())
                        .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class, EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO).getProperties())
                        .addProperties(new HQLProperties(TipoAtendimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), getAtendimento().getNaturezaProcuraTipoAtendimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_VISIVEL), RepositoryComponentDefault.SIM_LONG))
                        .addInterceptor(new LoadInterceptorEloNaturezaTipoEncaminhamento(getAtendimento().getEmpresa()))
                        .start().getList();
                
                cbxEncaminhamentos.addChoice(null, "");
                
                for (EloNaturezaTipoEncaminhamento eloNaturezaTipoEncaminhamento : elos) {
                    cbxEncaminhamentos.addChoice(eloNaturezaTipoEncaminhamento, eloNaturezaTipoEncaminhamento.getEncaminhamentoTipo().getDescricao());
                }
            
            this.cbxEncaminhamentos.add(new TempBehavior());
            this.cbxEncaminhamentos.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    boolean enableAltaFields = enableAltaFields();
                    cbxMotivo.limpar(target);
                    txtDataAlta.limpar(target);
                    autoCompleteConsultaCid.limpar(target);
                    new TempHelper().save(cbxMotivo, null);
                    new TempHelper().save(autoCompleteConsultaCid, null);
                            
                    if (enableAltaFields && txtDataAlta.getModelObject() == null) {
                        Date dataAtual = DataUtil.getDataAtual();
                        txtDataAlta.setModelObject(dataAtual);
                        new TempHelper().save(txtDataAlta, Data.formatar(dataAtual));
                    } else {
                        new TempHelper().save(txtDataAlta, null);
                    }
                    
                    avaliarCidPrincipal(target, null);
                    target.appendJavaScript(JScript.initMasks());
                }
            });
        }
        
        return this.cbxEncaminhamentos;
    }
    
    private void avaliarCidPrincipal(AjaxRequestTarget target, Cid object){
        if(object != null){
            cbxSalvarCid.setEnabled(true);
        }else{
            cbxSalvarCid.setModelObject(false);
            new TempHelper().save(cbxSalvarCid, null);
            cbxSalvarCid.setEnabled(false);
            txaObservacao.setEnabled(false);
            if (target!=null) {
                txaObservacao.limpar(target);
                new TempHelper().save(txaObservacao, null);
            }
        }
        if (cbxSalvarCid.getModelObject()==null) {
            cbxSalvarCid.setModelObject(false);
            new TempHelper().save(cbxSalvarCid, null);
        }
        if (target!=null) {
            target.add(cbxSalvarCid);
            if (cbxSalvarCid.getModelObject()) {
                JScript.showFieldset(target, containerObservacaoPatologia);
            } else {
                JScript.hideFieldset(target, containerObservacaoPatologia);
            }
        }
    }
    
    private void avaliarSalvaCid(AjaxRequestTarget target){
        txaObservacao.setEnabled(cbxSalvarCid.getModelObject());
        if (target!=null) {
            target.add(txaObservacao);
            JScript.toggleFieldset(target, containerObservacaoPatologia);
        }
    }
    
    private boolean enableAltaFields(){
        EloNaturezaTipoEncaminhamento elo = model.getObject().getEloNaturezaTipoEncaminhamento();

        boolean enable = false;
        if (elo != null && elo.getEncaminhamentoTipo() != null) {
            EncaminhamentoTipo encaminhamentoTipo = elo.getEncaminhamentoTipo();
            if (encaminhamentoTipo.getTipo() == null) {
                encaminhamentoTipo = LoadManager.getInstance(EncaminhamentoTipo.class)
                    .setId(elo.getEncaminhamentoTipo().getCodigo())
                    .start().getVO();

                model.getObject().getEloNaturezaTipoEncaminhamento().setEncaminhamentoTipo(encaminhamentoTipo);
            }

            if (Arrays.asList(EncaminhamentoTipo.Tipo.ALTA.value(), EncaminhamentoTipo.Tipo.INTERNACAO.value(), EncaminhamentoTipo.Tipo.ALTA_MEDICACAO.value())
                    .contains(encaminhamentoTipo.getTipo())) {
                enable = true;
            }
        }

        cbxMotivo.setEnabled(enable);
        txtDataAlta.setEnabled(enable);
        autoCompleteConsultaCid.setEnabled(enable);
        
        return enable;
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        if (model.getObject().getSalvarCidPrincipaisPatologias()) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerObservacaoPatologia)));
        }
    }
}
