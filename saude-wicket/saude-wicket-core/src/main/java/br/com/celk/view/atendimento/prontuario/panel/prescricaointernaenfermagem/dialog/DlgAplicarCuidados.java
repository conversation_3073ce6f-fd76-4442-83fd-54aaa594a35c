package br.com.celk.view.atendimento.prontuario.panel.prescricaointernaenfermagem.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Cuidados;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgAplicarCuidados extends Window {

    private PnlAplicarCuidados pnlAplicarCuidados;
    private String label;

    public DlgAplicarCuidados(String id, String label) {
        super(id);
        this.label = label;
        init();

    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("aplicarMedicamento", this);
            }
        });

        setInitialWidth(600);
        setInitialHeight(95);
        setResizable(true);

        setContent(pnlAplicarCuidados = new PnlAplicarCuidados(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, Cuidados cuidados, Date dataHoraAplicacao) throws ValidacaoException, DAOException {
                close(target);
                DlgAplicarCuidados.this.onConfirmar(target, cuidados, dataHoraAplicacao);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public String getInfoLabel() {
                return BundleManager.getString(label);
            }

        });
    }

    public void show(AjaxRequestTarget target, Cuidados cuidados) {
        show(target);
        pnlAplicarCuidados.limpar(target);
        pnlAplicarCuidados.setCuidados(cuidados);
        pnlAplicarCuidados.setDataHoraAplicacao(DataUtil.getDataAtual());
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Cuidados cuidado, Date dataHoraAplicacao) throws ValidacaoException, DAOException;

    public void onCancelar(AjaxRequestTarget target) {
    }
}
