package br.com.celk.view.materiais.produto.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.factory.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.materiais.produto.autocomplete.restricaocontainer.RestricaoContainerDominioMedicamentoObmAmpp;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.QueryConsultaMedicamentoCatmatDTOParam;
import br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto.QueryConsultaMedicamentoObmAmppDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MedicamentoCatmat;
import br.com.ksisolucoes.vo.entradas.estoque.MedicamentoObmAmpp;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

public class AutoCompleteConsultaMedicamentoObmAmpp extends AutoCompleteConsulta<MedicamentoObmAmpp> {



    public AutoCompleteConsultaMedicamentoObmAmpp(String id) {
        super(id);
    }

    public AutoCompleteConsultaMedicamentoObmAmpp(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaMedicamentoObmAmpp(String id, IModel<MedicamentoObmAmpp> model) {
        super(id, model);
    }

    public AutoCompleteConsultaMedicamentoObmAmpp(String id, IModel<MedicamentoObmAmpp> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {


            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(MedicamentoObmAmpp.class);
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(MedicamentoObmAmpp.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerDominioMedicamentoObmAmpp(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<MedicamentoObmAmpp, QueryConsultaMedicamentoObmAmppDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaMedicamentoObmAmppDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(ProdutoFacade.class).consultarMedicamentoObmAmpp(dataPaging);
                    }

                    @Override
                    public QueryConsultaMedicamentoObmAmppDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaMedicamentoObmAmppDTOParam param = new QueryConsultaMedicamentoObmAmppDTOParam();
                        param.setDescricao(searchCriteria.trim().toUpperCase());
                        return param;
                    }

                    @Override
                    public void customizeParam(QueryConsultaMedicamentoObmAmppDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(MedicamentoObmAmpp.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return MedicamentoObmAmpp.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("obms");
    }
}
