package br.com.celk.view.atendimento.prontuario.panel.receituario.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgPrescricoesSugeridas extends Window{

    private PnlPrescricoesSugeridas pnlPrescricoesSugeridas;

    public DlgPrescricoesSugeridas(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("prescricoesSugeridas");
            }
        });
                
        setInitialWidth(850);
        setInitialHeight(350);
        setResizable(true);
        
        setContent(pnlPrescricoesSugeridas = new PnlPrescricoesSugeridas(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, List<ReceituarioItemDTO> receituarioItemDTOList) throws ValidacaoException, DAOException {
                close(target);
                DlgPrescricoesSugeridas.this.onConfirmar(target, receituarioItemDTOList);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, List<ReceituarioItemDTO> receituarioItemDTOList) throws ValidacaoException, DAOException;

    @Override
    public void show(AjaxRequestTarget target){
        super.show(target);
        pnlPrescricoesSugeridas.setObject(target);
    }
}