package br.com.celk.component.table.column;

import br.com.celk.component.css.TextAlign;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Valor;
import java.math.BigDecimal;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class BigDecimalColumn<T> extends Column<T> {

    private int casasDecimais = 2;
    private boolean coalesceValue = true;
    private boolean showZero = true;
    
    public BigDecimalColumn(String displayModel, String sortProperty, String propertyExpression) {
        super(displayModel, sortProperty, propertyExpression);
    }

    public BigDecimalColumn(String displayModel, String propertyExpression) {
        super(displayModel, propertyExpression);
    }

    @Override
    public void populateItem(Item<ICellPopulator<T>> item, String componentId, IModel<T> rowModel) {
        IModel<BigDecimal> createLabelModel = (IModel<BigDecimal>) createLabelModel(rowModel);
        
        BigDecimal value;
        
        if (coalesceValue) {
            value = Coalesce.asBigDecimal(createLabelModel.getObject());
        } else {
            value = createLabelModel.getObject();
        }
        
        if (value.compareTo(BigDecimal.ZERO)==0 && !showZero) {
            value = null;
        }
        
        item.add(new Label(componentId, value!=null ? Valor.adicionarFormatacaoMonetaria(value, casasDecimais) : ""));
        item.add(new AttributeModifier("class", TextAlign.RIGTH.value()));
    }

    public BigDecimalColumn setCasasDecimais(int casasDecimais) {
        this.casasDecimais = casasDecimais;
        return this;
    }
    
    public BigDecimalColumn setCoalesceValue(boolean coalesceValue) {
        this.coalesceValue = coalesceValue;
        return this;
    }
    
    public BigDecimalColumn setShowZero(boolean showZero) {
        this.showZero = showZero;
        return this;
    }

}
