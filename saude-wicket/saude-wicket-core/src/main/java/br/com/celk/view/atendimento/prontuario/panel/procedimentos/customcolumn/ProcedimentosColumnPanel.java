package br.com.celk.view.atendimento.prontuario.panel.procedimentos.customcolumn;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.InsumosProcedimentosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class ProcedimentosColumnPanel extends Panel {

    private AbstractAjaxLink btnEditar;
    private AbstractAjaxLink btnRemover;
    private AbstractAjaxLink btnInsumos;
    private InsumosProcedimentosDTO dto;

    public ProcedimentosColumnPanel(String id, InsumosProcedimentosDTO dto) {
        super(id);
        this.dto = dto;
        init();
    }

    private void init() {
        add(btnEditar = new AbstractAjaxLink("btnEditar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onEditar(target, dto);
            }
        });
        add(btnRemover = new AbstractAjaxLink("btnRemover") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onRemover(target, dto);
            }
        });
        add(btnInsumos = new AbstractAjaxLink("btnInsumos") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onInsumos(target, dto);
            }
        });

        btnEditar.add(new AttributeModifier("title", BundleManager.getString("editar")));
        btnRemover.add(new AttributeModifier("title", BundleManager.getString("remover")));
        btnInsumos.add(new AttributeModifier("title", BundleManager.getString("insumos")));
    }

    public abstract void onEditar(AjaxRequestTarget target, InsumosProcedimentosDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onRemover(AjaxRequestTarget target, InsumosProcedimentosDTO dto) throws ValidacaoException, DAOException;

    public abstract void onInsumos(AjaxRequestTarget target, InsumosProcedimentosDTO dto) throws ValidacaoException, DAOException;

}
