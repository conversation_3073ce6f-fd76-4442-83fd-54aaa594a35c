package br.com.celk.component.autocomplete;

import br.com.celk.component.inputfield.InputField;
import java.io.Serializable;
import java.util.Iterator;
import org.apache.wicket.extensions.ajax.markup.html.autocomplete.*;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public abstract class AutoComplete<T extends Serializable> extends InputField {

    private AutoCompleteBehavior autoCompleteBehavior;
    private AutoCompleteSettings autoCompleteSettings = new AutoCompleteSettings();
    private IAutoCompleteRenderer<T> autoCompleteRenderer = new AbstractAutoCompleteTextRenderer<T>() {

        @Override
        protected String getTextValue(T object) {
            return AutoComplete.this.getTextValue(object);
        }
    };
    
    
    public AutoComplete(String id) {
        super(id);
        init();
    }

    public AutoComplete(String id, IModel<T> object) {
        super(id, object);
        init();
    }    
    
    private void init(){
        getAutoCompleteSettings().setThrottleDelay(600);
    }

    public AutoCompleteBehavior<T> getAutoCompleteBehavior(){
        if (this.autoCompleteBehavior == null) {
            this.autoCompleteBehavior = new AutoCompleteBehavior<T>(autoCompleteRenderer, autoCompleteSettings) {

                @Override
                protected Iterator<T> getChoices(final String input) {
                    return AutoComplete.this.getChoices(input);
                }
            };
        }
        
        return this.autoCompleteBehavior;
    }

    @Override
    protected void onBeforeRender() {
        if (autoCompleteBehavior == null) {
            add(getAutoCompleteBehavior());
        }
        super.onBeforeRender();
    }
    
    @Override
    protected void onComponentTag(final ComponentTag tag) {
        super.onComponentTag(tag);
        tag.put("autocomplete", "off");
    }
    
    public AutoCompleteSettings getAutoCompleteSettings() {
        return autoCompleteSettings;
    }
    
    public void setAutoCompleteRenderer(IAutoCompleteRenderer<T> autoCompleteRenderer) {
        this.autoCompleteRenderer = autoCompleteRenderer;
    }
    
    public abstract String getTextValue(T object);
    
    protected abstract Iterator<T> getChoices(String input);
    
}
