package br.com.celk.view.atendimento.prontuario.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlInformarProfissional extends Panel{

    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private LoadableObjectModel<Profissional> model;
    
    public PnlInformarProfissional(String id) {
        super(id);
        init();
    }
    
    private void init(){
        Form form = new Form("form");
        
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", model = new LoadableObjectModel<Profissional>(Profissional.class, null, false)));
        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
        form.add(new AbstractAjaxButton("btnOk") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onOk(target, model.getObject());
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public abstract void onOk(AjaxRequestTarget target, Profissional profissional) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target){
        autoCompleteConsultaProfissional.limpar(target);
    }
    
    public void setEmpresa(Empresa empresa){
        autoCompleteConsultaProfissional.setCodigoEmpresa(empresa.getCodigo());
        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
    }
    
    public FormComponent getFocusComponent(){
        return autoCompleteConsultaProfissional.getTxtDescricao().getTextField();
    }
    
}
