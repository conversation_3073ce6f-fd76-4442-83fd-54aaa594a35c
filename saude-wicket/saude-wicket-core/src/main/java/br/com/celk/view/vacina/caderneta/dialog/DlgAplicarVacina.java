package br.com.celk.view.vacina.caderneta.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.AplicacaoVacinaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * <AUTHOR>
 */
public abstract class DlgAplicarVacina extends Window {

    private final VacinaCalendario vacinaCalendario;
    private final UsuarioCadsus usuarioCadsus;
    private final boolean reaplicarVacina;

    public DlgAplicarVacina(String id, VacinaCalendario vacinaCalendario, UsuarioCadsus usuarioCadsus, boolean reaplicarVacina) {
        super(id);
        this.vacinaCalendario = vacinaCalendario;
        this.usuarioCadsus = usuarioCadsus;
        this.reaplicarVacina = reaplicarVacina;
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("aplicacaoVacina");
            }
        });

        setInitialWidth(1230);
        setInitialHeight(550);
        setResizable(true);

        PnlAplicarVacina pnlAplicarVacina = new PnlAplicarVacina(getContentId(), vacinaCalendario, usuarioCadsus, reaplicarVacina) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, AplicacaoVacinaDTO aplicacaoVacinaDTO) throws ValidacaoException, DAOException {
                close(target);
                DlgAplicarVacina.this.onConfirmar(target, aplicacaoVacinaDTO);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        };

        setContent(pnlAplicarVacina);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AplicacaoVacinaDTO aplicacaoVacinaDTO) throws ValidacaoException, DAOException;

}