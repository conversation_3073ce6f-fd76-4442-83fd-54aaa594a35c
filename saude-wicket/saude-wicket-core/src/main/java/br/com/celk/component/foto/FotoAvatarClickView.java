package br.com.celk.component.foto;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.foto.interfaces.IFoto;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.panel.Panel;

/**
 * <AUTHOR>
 */
abstract class FotoAvatarClickView extends Panel {

    private NonCachingImage imagem;
    private AbstractAjaxButton btnOk;
    private AbstractAjaxButton btnFechar;
    private IFoto iFoto;
    private WebMarkupContainer containerFile;
    private WebMarkupContainer containerAvatar;

    FotoAvatarClickView(String id) {
        super(id);
        init();

    }

    private void init() {
        containerAvatar = new WebMarkupContainer("containerAvatar");
        containerAvatar.add(imagem = new NonCachingImage("imgAvatar", ""));
        add(containerAvatar);
        add(btnOk = new AbstractAjaxButton("btnOk") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnOk.setDefaultFormProcessing(false);

        add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);
    }

    public NonCachingImage getImagem() {
        return imagem;
    }

    public abstract void onFechar(AjaxRequestTarget target);

    public void setIFoto(IFoto iFoto) {
        this.iFoto = iFoto;
        imagem.setImageResource(ImagemAvatarHelper.carregarAvatarResource(iFoto));
    }
}