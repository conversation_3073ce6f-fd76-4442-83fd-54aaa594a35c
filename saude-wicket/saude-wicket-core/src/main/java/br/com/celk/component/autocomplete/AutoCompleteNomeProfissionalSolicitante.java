package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteNomeProfissionalSolicitante extends AutoComplete<String> {

    private List<String> nomes = new ArrayList<String>();

    public AutoCompleteNomeProfissionalSolicitante(String id, IModel object) {
        super(id, object);
        init();
    }

    public AutoCompleteNomeProfissionalSolicitante(String id) {
        super(id);
        init();
    }

    private void init() {
        getAutoCompleteSettings().setThrottleDelay(800);
        add(new AttributeModifier("class", "uppercase"));
    }

    @Override
    protected Iterator<String> getChoices(String input) {
        nomes.clear();

        String trimmedInput = StringUtils.trimToNull(input);
        if (trimmedInput == null || trimmedInput.length() <= 2) {
            return nomes.iterator();
        }

        nomes = buscarNomesProfissionais(trimmedInput);

        return nomes.iterator();
    }

    private List<String> buscarNomesProfissionais(String input) {
        return LoadManager.getInstance(AtendimentoExame.class)
                .addGroup(new QueryCustom.QueryCustomGroup(AtendimentoExame.PROP_NOME_PROFISSIONAL, BuilderQueryCustom.QueryGroup.DISTINCT))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoExame.PROP_NOME_PROFISSIONAL), BuilderQueryCustom.QueryParameter.ILIKE, input))
                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoExame.PROP_NOME_PROFISSIONAL, "asc"))
                .setMaxResults(10)
                .start().getList();
    }

    @Override
    public String getTextValue(String object) {
        return object;
    }
}
