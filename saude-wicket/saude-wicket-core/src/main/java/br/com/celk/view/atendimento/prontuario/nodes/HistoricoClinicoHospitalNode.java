package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.HistoricoClinicoHospitalPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.HISTORICO_CLINICO_HOSPITAL)
public class HistoricoClinicoHospitalNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new HistoricoClinicoHospitalPanel(id, getTitulo());
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("historicoClinico");
    }

}