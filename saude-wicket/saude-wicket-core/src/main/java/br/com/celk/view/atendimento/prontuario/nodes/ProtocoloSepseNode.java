package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.ProtocoloSepsePanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;


@ProntuarioNode(NodesAtendimentoRef.PROTOCOLO_SEPSE)
public class ProtocoloSepseNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new ProtocoloSepsePanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.BLOGS;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("protocoloSepse");
    }

}
