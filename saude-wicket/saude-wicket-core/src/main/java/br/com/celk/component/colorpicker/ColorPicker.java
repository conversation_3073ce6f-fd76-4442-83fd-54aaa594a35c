package br.com.celk.component.colorpicker;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.resources.Resources;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.core.javascript.JsQuery;

/**
 *
 * <AUTHOR>
 */
public class ColorPicker extends FormComponentPanel<String> {

    private InputField<String> hiddenInput;
    
    public ColorPicker(String id, IModel<String> model) {
        super(id, model);
        init();
    }

    public ColorPicker(String id) {
        super(id);
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        add(hiddenInput = new InputField<String>("hiddenInput", new Model<String>()));
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        response.render(CssHeaderItem.forReference(Resources.CSS_COLOR_PICKER));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_COLOR_PICKER));

        StringBuilder script = new StringBuilder();
        script.append("var opened").append(getMarkupId()).append("=false;")
                .append("if($('#").append(hiddenInput.getMarkupId()).append("').val()==''){")
                .append("     $('#").append(getMarkupId()).append(" .colorSelector2 div').css('backgroundColor', '#FFFFFF');")
                .append("     $('#").append(getMarkupId()).append(" .colorSelector2 div').addClass('emptyColor');")
                .append("}")
                .append("$('#").append(getMarkupId()).append(" .colorSelector2 div').css('backgroundColor', '#'+$('#").append(hiddenInput.getMarkupId()).append("').val());")
                .append("$('#").append(getMarkupId()).append(" .colorpickerHolder2').ColorPicker(")
                .append(" {       ")
                .append("     flat: true,")
                .append("     color: '#'+$('#").append(hiddenInput.getMarkupId()).append("').val(),")
                .append("     onSubmit: function(hsb, hex, rgb, el) {")
                .append("         $('#").append(getMarkupId()).append(" .colorSelector2 div').css('backgroundColor', '#' + hex);")
                .append("         $('#").append(getMarkupId()).append(" .colorSelector2 div').removeClass('emptyColor');")
                .append("         $('#").append(getMarkupId()).append(" .colorpickerHolder2').stop().animate({height: opened").append(getMarkupId()).append(" ? 0 :  173}, 200);")
                .append("         opened").append(getMarkupId()).append(" = !opened").append(getMarkupId()).append(";")
                .append("         $('#").append(hiddenInput.getMarkupId()).append("').val(hex);")
                .append("     },")
                .append("     onBeforeShow: function () {")
                .append("         $(this).ColorPickerSetColor($('#").append(hiddenInput.getMarkupId()).append("').val());")
                .append("     }")
                .append(" });");

        boolean enabled = isEnabled();
        if (enabled) { enabled = this.getParent().isEnabled(); }
        if (enabled) {
            // binding events
            script.append(" $('#").append(getMarkupId()).append(" .colorSelector2').bind('click', function() {")
                    .append("     $('#").append(getMarkupId()).append(" .colorpickerHolder2').stop().animate({height: opened").append(getMarkupId()).append(" ? 0 :  173}, 200);")
                    .append("     opened").append(getMarkupId()).append(" = !opened").append(getMarkupId()).append(";")
                    .append(" });")
                    .append(" $('#").append(getMarkupId()).append(" .remove-color').bind('click', function() {")
                    .append("     $('#").append(hiddenInput.getMarkupId()).append("').val('');")
                    .append("     $('#").append(getMarkupId()).append(" .colorSelector2 div').css('backgroundColor', '#FFFFFF');")
                    .append("     $('#").append(getMarkupId()).append(" .colorSelector2 div').addClass('emptyColor');")
                    .append("     $(this).ColorPickerSetColor('#FFFFFF');")
                    .append(" });");
        } else {
            // close component
            script.append(new JsQuery(this).$(".colorpickerHolder2").append(".stop().animate({height: 0}, 200);").render());

            // unbinding events
            script.append(new JsQuery(this).$(".colorSelector2").chain("unbind", "'click'").render());
            script.append(new JsQuery(this).$(".remove-color").chain("unbind", "'click'").render());
        }

        response.render(OnLoadHeaderItem.forScript(script.toString()));
    }

    public void limpar(AjaxRequestTarget target) {
        hiddenInput.setModelObject(null);
        clearInput();
        target.appendJavaScript(new JsQuery(hiddenInput).$().chain("val", "''").render()); // clear input val
        target.add(this);
    }

    @Override
    protected void convertInput() {
        super.convertInput();
        setConvertedInput(hiddenInput.getConvertedInput());
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender(); //To change body of generated methods, choose Tools | Templates.
        hiddenInput.setModelObject(getModelObject());
    }

}
