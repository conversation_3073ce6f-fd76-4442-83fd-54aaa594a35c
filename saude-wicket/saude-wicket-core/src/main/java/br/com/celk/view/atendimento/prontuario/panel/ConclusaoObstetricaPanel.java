package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.ObstetriciaExameGeral;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.markup.html.form.Form;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoFichaTrabalhoPartoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.log.Loggable;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class ConclusaoObstetricaPanel extends ProntuarioCadastroPanel {

    private Form<ObstetriciaExameGeral> form;
    private DlgImpressaoObject<ObstetriciaExameGeral> dlgConfirmacaoImpressao;
 
    public ConclusaoObstetricaPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        carregar();

        ObstetriciaExameGeral proxy = on(ObstetriciaExameGeral.class);

        getForm().add(new InputArea(path(proxy.getConclusao())).add(new TempBehaviorV2()));
        getForm().add(new AutoCompleteConsultaProfissional(path(proxy.getObstetra())).add(new TempBehaviorV2()));
        getForm().add(new DateChooserAjax(path(proxy.getData())).add(new TempBehaviorV2()));

        getForm().add(new AbstractAjaxButton("btnImprimirFichaTrabalhoParto") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target);
            }
        });

        add(getForm());


        getForm().add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), ObstetriciaExameGeral.class)));

    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        ObstetriciaExameGeral obstetriciaExameGeral = form.getModel().getObject();

        validarObstetriciaExameGeral(obstetriciaExameGeral);
        
        ObstetriciaExameGeral obstetriciaExameGeralSave = BOFactoryWicket.save(obstetriciaExameGeral);
        carregar();
        new TempHelperV2().save(getForm());
        
        if(obstetriciaExameGeralSave.getCodigo() != null){
            initDialogImpressao(target);
            dlgConfirmacaoImpressao.show(target, obstetriciaExameGeralSave);
        }
    }
    
    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<ObstetriciaExameGeral>(getProntuarioController().newWindowId(), bundle("desejaImprimirFichaTrabalhoParto", this)) {
                @Override
                public DataReport getDataReport(ObstetriciaExameGeral obstetriciaExameGeral) throws ReportException {
                    
                    if(obstetriciaExameGeral.getDataImpressao() == null){
                        obstetriciaExameGeral.setDataImpressao(DataUtil.getDataAtual());

                        try {
                            BOFactoryWicket.save(obstetriciaExameGeral);
                        } catch (DAOException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        } catch (ValidacaoException ex) {
                            Loggable.log.error(ex.getMessage(), ex);
                        }

                        carregar();
                        new TempHelperV2().save(getForm());
                    }
                    
                    RelatorioImpressaoFichaTrabalhoPartoDTOParam param = new RelatorioImpressaoFichaTrabalhoPartoDTOParam();
                    param.setCodigoAtendimentoPrincipal(getAtendimento().getAtendimentoPrincipal().getCodigo());
                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoFichaTrabalhoParto(param);
                }               

                @Override
                public void onFechar(AjaxRequestTarget target, ObstetriciaExameGeral object) throws ValidacaoException, DAOException {
                    DefaultProntuarioPanel panel = new ConclusaoObstetricaPanel(getProntuarioController().panelId(), BundleManager.getString("conclusaoObstetrica"));
                    getProntuarioController().changePanel(target, panel);
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private Form<ObstetriciaExameGeral> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(new ObstetriciaExameGeral()));
        }
        return this.form;
    }

    private void carregar() {
        ObstetriciaExameGeral obstetriciaExameGeral = LoadManager.getInstance(ObstetriciaExameGeral.class)
                .addProperties(new HQLProperties(ObstetriciaExameGeral.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ObstetriciaExameGeral.PROP_ATENDIMENTO, Atendimento.PROP_ATENDIMENTO_PRINCIPAL), getAtendimento().getAtendimentoPrincipal()))
                .start().getVO();

        if (obstetriciaExameGeral != null) {
            getForm().getModel().setObject(obstetriciaExameGeral);
        }
    }

    private void validarObstetriciaExameGeral(ObstetriciaExameGeral obstetriciaExameGeral) throws ValidacaoException {
        if (obstetriciaExameGeral.getCodigo() == null) {
            throw new ValidacaoException(bundle("avaliacaoObstetricaNaoRealizada", this));
        }
        
        if (obstetriciaExameGeral.getConclusao() == null) {
            throw new ValidacaoException(bundle("informeConclusao", this));
        }
        
        if (obstetriciaExameGeral.getObstetra() == null) {
            throw new ValidacaoException(bundle("informeObstetra", this));
        }
        
        if (obstetriciaExameGeral.getData() == null) {
            throw new ValidacaoException(bundle("informeData", this));
        }
    }

}