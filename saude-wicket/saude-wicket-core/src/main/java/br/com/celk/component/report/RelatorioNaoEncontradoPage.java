package br.com.celk.component.report;

import br.com.celk.resources.Resources;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.WebPage;

/**
 *
 * <AUTHOR>
 */
public class RelatorioNaoEncontradoPage extends WebPage{

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(Resources.CSS_JQUERY_UI_CUSTOM));
        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(Resources.CSS_CELK));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
    }

}
