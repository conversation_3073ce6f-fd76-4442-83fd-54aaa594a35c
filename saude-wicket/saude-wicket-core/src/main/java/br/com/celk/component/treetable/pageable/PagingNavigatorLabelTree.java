package br.com.celk.component.treetable.pageable;

import org.apache.wicket.util.io.IClusterable;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.navigation.paging.IPageableItems;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.StringResourceModel;

/**
 *
 * <AUTHOR>
 *
 */
public class PagingNavigatorLabelTree extends Label {

    private static final long serialVersionUID = 1L;

    /**
     * Construct.
     *
     * @param id
     * @param pageable
     */
    public PagingNavigatorLabelTree(final String id, final IPageableItems pageable) {
        super(id);
        setDefaultModel(new StringResourceModel("NavigatorLabel", this,
                new Model<LabelModelObject>(new LabelModelObject(pageable))));
    }

    private static class LabelModelObject implements IClusterable {

        private static final long serialVersionUID = 1L;
        private final IPageableItems pageable;

        /**
         * Construct.
         *
         * @param table
         */
        public LabelModelObject(final IPageableItems table) {
            pageable = table;
        }

        /**
         * @return "z" in "Showing x to y of z"
         */
        public long getOf() {
            return pageable.getPageCount();
        }

        /**
         * @return "x" in "Showing x to y of z"
         */
        public long getFrom() {
            if (getOf() == 0) {
                return 0;
            }
            return pageable.getCurrentPage();
        }

        /**
         * @return "y" in "Showing x to y of z"
         */
        public long getTo() {
            if (getOf() == 0) {
                return 0;
            }
            return Math.min(getOf(), getFrom() + pageable.getItemsPerPage() - 1);
        }
    }
}
