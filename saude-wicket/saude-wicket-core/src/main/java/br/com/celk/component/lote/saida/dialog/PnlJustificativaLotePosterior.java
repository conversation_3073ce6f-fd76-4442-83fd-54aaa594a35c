package br.com.celk.component.lote.saida.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 * Created by sulivan on 11/12/17.
 */
public abstract class PnlJustificativaLotePosterior extends Panel {

    private InputArea<String> txtJustificatica;
    private String justificatica;
    private AbstractAjaxButton btnFechar;

    public PnlJustificativaLotePosterior(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtJustificatica = new InputArea("justificatica"));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (justificatica == null) {
                    throw new ValidacaoException("Por favor, informe a justificativa.");
                }
                onConfirmar(target, justificatica);
                limpar(target, false);
            }
        });

        form.add(btnFechar = (AbstractAjaxButton) new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);

        txtJustificatica.add(new AttributeModifier("maxlength", getMaxLengthMotivo()));
    }

    public abstract Long getMaxLengthMotivo();

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, String justificatica) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target, boolean exibirBotaoFechar) {
        if(exibirBotaoFechar){
            btnFechar.setVisible(true);
        } else {
            btnFechar.setVisible(false);
        }
        this.txtJustificatica.limpar(target);
        target.focusComponent(txtJustificatica);
    }
}