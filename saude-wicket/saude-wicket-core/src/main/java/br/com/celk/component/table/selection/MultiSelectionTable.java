package br.com.celk.component.table.selection;

import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.*;
import br.com.celk.component.table.fieldcolumn.SelectionCheckColumnPanel;
import br.com.celk.component.view.AbstractCollectionView;
import br.com.celk.component.view.DTOSelectionCollectionView;
import br.com.ksisolucoes.util.VOUtils;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MultiSelectionTable<T extends Serializable> extends Table implements IMultiSelectionTable<T, IMultiSelectionRow<DTOSelection<T>>> {

    private List<T> selectedObjects = new ArrayList<T>();
    private List<IMultiSelectionRow<DTOSelection<T>>> selectedRows = new ArrayList<IMultiSelectionRow<DTOSelection<T>>>();
    private List<ISelectionAction> selectionActions = new ArrayList<ISelectionAction>();
    private List<ISelectionAction> unselectionActions = new ArrayList<ISelectionAction>();
    private boolean selectAll = false;

    public MultiSelectionTable(String id, List<ISortableColumn<T>> columns, ICollectionProvider collectionProvider) {
        super(id, columns, collectionProvider);

        setColumnsDefs("[{ \"sWidth\": \"5px\", \"aTargets\": [ 0 ] }]");
    }

    @Override
    protected Item newCellItem(String id, int index, IModel model) {
        return new MultiSelectionCell<ICellPopulator<DTOSelection>>(id, index, model);
    }

    @Override
    protected Item newRowItem(String id, int index, IModel model) {
        return new MultiDTOSelectionRow<T>(id, index, model, this);
    }

    @Override
    public List customizeColumns(List columns) {
        List<IColumn> finalColumns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(DTOSelection.class);

        finalColumns.add(getRadioColumn());

        for (IColumn column : (List<IColumn>)columns) {
            if (column instanceof IPropertyColumn) {
                if(column.isSortable()){
                    finalColumns.add(columnFactory.createSortableColumn((String)((IPropertyColumn)column).getDisplayModel().getObject(), 
                            column.getSortProperty().toString(),
                        VOUtils.montarPath(DTOSelection.PROP_BEAN, ((IPropertyColumn)column).getPropertyExpression()), 
                        ((IPropertyColumn)column).getClass()));
                }else if(column instanceof Column){
                    Column aux = columnFactory.createColumn((String)((IPropertyColumn)column).getDisplayModel().getObject(), 
                        VOUtils.montarPath(DTOSelection.PROP_BEAN, ((IPropertyColumn)column).getPropertyExpression()), 
                        ((IPropertyColumn)column).getClass());
                    aux.setPropertyMap(((Column) column).getPropertyMap());
                    finalColumns.add(aux);
                }else{
                    finalColumns.add(columnFactory.createColumn((String)((IPropertyColumn)column).getDisplayModel().getObject(), 
                        VOUtils.montarPath(DTOSelection.PROP_BEAN, ((IPropertyColumn)column).getPropertyExpression()), 
                        ((IPropertyColumn)column).getClass()));
                }
                
            } else {
                finalColumns.add(column);
            }
        }

        return finalColumns;
    }

    private SelectionColumn<DTOSelection<T>> getRadioColumn(){
        SelectionColumn selectionColumn = new SelectionColumn<DTOSelection<T>>() {

            @Override
            public Component getComponent(String componentId, DTOSelection<T> rowObject) {
                final SelectionCheckColumnPanel selectionCheckColumnPanel = new SelectionCheckColumnPanel(componentId, rowObject, DTOSelection.PROP_SELECTED);
                selectionCheckColumnPanel.getCheckBox().setOutputMarkupId(true);

                selectionCheckColumnPanel.setEnabled(isEnableCheckSelectionRow(rowObject.getBean()));

                selectionCheckColumnPanel.getCheckBox().add(new AjaxFormComponentUpdatingBehavior("onclick"){

                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        selectionCheckColumnPanel.findParent(IMultiSelectionRow.class).setSelected(target, selectionCheckColumnPanel.getCheckBox().getComponentValue());
                    }

                });
                return selectionCheckColumnPanel;
            }
        };

        selectionColumn.setLabelHeader(getLabelHeader());

        return selectionColumn;
    }

    public String getLabelHeader(){
        return null;
    }

    @Override
    public AbstractCollectionView getCollectionView(String id, ICollectionProvider collectionProvider1, final ICollectionViewStrategy collectionViewStrategy) {
        return new DTOSelectionCollectionView(id, collectionProvider1) {

            @Override
            protected void populateItem(Item item) {
                collectionViewStrategy.populateItem(item);
            }

            @Override
            public Item newItem(String id, int index, IModel model) {
                return collectionViewStrategy.newItem(id, index, model);
            }

            @Override
            protected Iterator getItemModels() {
                if (isPopulated()) {
                    return collectionViewStrategy.getItemModels(super.getItemModels());
                }
                return newEmptyIterator();
            }
        };
    }

    public boolean isEnableCheckSelectionRow(T object) {
        return true;
    }

    public void addSelectionAction(ISelectionAction<T> selectionActionajaxSelection){
        selectionActions.add(selectionActionajaxSelection);
    }

    public void removeSelectionAction(ISelectionAction<T> selectionAction){
        selectionActions.remove(selectionAction);
    }

    public void addUnselectionAction(ISelectionAction<T> selectionActionajaxSelection){
        unselectionActions.add(selectionActionajaxSelection);
    }

    public void removeUnselectionAction(ISelectionAction<T> selectionAction){
        unselectionActions.remove(selectionAction);
    }

    @Override
    public void setSelected(boolean selected, T object, IMultiSelectionRow<DTOSelection<T>> row){
        if (selected) {
            if (isEnableCheckSelectionRow(object) && !contemInstancia(object, selectedObjects)) {
                this.selectedObjects.add(object);
                this.selectedRows.add(row);
            }
        } else {
            this.selectedObjects.remove(object);
            removeLista(object, selectedObjects);
            this.selectedRows.remove(row);
            setSelectAll(false);
        }

//        if(this.selectedRows.size() == 1){
//            setSelectAll(true);
//        }
    }

    @Override
    public void setSelected(AjaxRequestTarget target, boolean selected, T object, IMultiSelectionRow<DTOSelection<T>> row) {
        if (selected) {
            if (isEnableCheckSelectionRow(object) &&  !contemInstancia(object, selectedObjects)) {
                for (ISelectionAction selectionAction : selectionActions) {
                    selectionAction.onSelection(target, object);
                }
                this.selectedObjects.add(object);
                this.selectedRows.add(row);
            }
        } else {
            for (ISelectionAction selectionAction : unselectionActions) {
                selectionAction.onSelection(target, object);
            }
            this.selectedObjects.remove(object);
            removeLista(object, selectedObjects);
            this.selectedRows.remove(row);
            setSelectAll(false);
            update(target);
        }

    }

    @Override
    public void clearSelection() {
        for (IMultiSelectionRow<DTOSelection<T>> iMultiSelectionRow : selectedRows) {
            if(iMultiSelectionRow != null && iMultiSelectionRow.getRowObject() != null){
                iMultiSelectionRow.getRowObject().setSelected(false);
            }
        }
        this.selectedObjects.clear();
        this.selectedRows.clear();
    }

    @Override
    public void clearSelection(AjaxRequestTarget target) {
        for (IMultiSelectionRow<DTOSelection<T>> iMultiSelectionRow : selectedRows) {
            iMultiSelectionRow.getRowObject().setSelected(false);
        }
        this.selectedObjects.clear();
        this.selectedRows.clear();
        update(target);
    }

    @Override
    public List<T> getSelectedObjects() {
        return selectedObjects;
    }

    public void setSelectedObject(List<T> selectedObjects) {
        this.selectedObjects = selectedObjects;
    }

    public List<IMultiSelectionRow<DTOSelection<T>>> getSelectedRows() {
        return selectedRows;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        clearSelection(target);
        super.limpar(target);
    }

    @Override
    public boolean isSelectAll() {
        return selectAll;
    }

    @Override
    public void setSelectAll(boolean selectAll) {
        this.selectAll = selectAll;
    }

    @Override
    public Component populate() {
        clearSelection();
        return super.populate();
    }

    @Override
    public void update(AjaxRequestTarget target) {
        super.update(target);
    }

    public void updateAndClearSelection(AjaxRequestTarget target) {
        clearSelection();
        super.update(target);
    }

    public boolean contemInstancia(T object, List<T> lista) {
        boolean b = false;
        for (Object o : lista) {
            if (o.toString().equals(object.toString())) {
                b = true;
                break;
            }
        }
        return b;
    }

    public void removeLista(T object, List<T> lista) {
        int index = 0;
        boolean controle = false;
        for (int i = 0; i < lista.size(); i++) {
            Object o = lista.get(i);
            if (o.toString().equals(object.toString())) {
                index = i;
                controle = true;
                break;
            }
        }
        if (controle) {
            lista.remove(index);
        }
    }

    public Iterator<Item<T>> getItems(){
        return super.getItems();
    }

    @Override
    public void upadateAfterCheckHeader(AjaxRequestTarget target){
        update(target);
    }
}
