package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.AvaliacaoEmergenciaHospitalPanel;
import br.com.celk.view.atendimento.prontuario.panel.AvaliacaoEmergenciaUpaPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.AVALIACAO_EMERGENCIA_UPA)
public class AvaliacaoEmergenciaUpaNode extends ProntuarioNodeImp{

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new AvaliacaoEmergenciaUpaPanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.TEMPERATURE_4;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("avaliacao");
    }
}
