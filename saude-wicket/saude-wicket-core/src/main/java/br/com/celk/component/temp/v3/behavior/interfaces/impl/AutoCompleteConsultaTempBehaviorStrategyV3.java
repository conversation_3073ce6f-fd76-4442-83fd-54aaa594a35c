package br.com.celk.component.temp.v3.behavior.interfaces.impl;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.temp.interfaces.ITempBehaviorStrategy;
import br.com.celk.component.temp.v3.behavior.TempFormBehaviorV3;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTempBehaviorStrategyV3 implements ITempBehaviorStrategy{

    @Override
    public void onBind(final Component component) {
        ((AutoCompleteConsulta)component).add(new ConsultaListener() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Object object) {
                List<TempFormBehaviorV3> behaviors = component.findParent(Form.class).getBehaviors(TempFormBehaviorV3.class);
                if (!behaviors.isEmpty()) {
                    behaviors.get(0).save();
                }
            }
        });
        ((AutoCompleteConsulta)component).add(new RemoveListener() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Object object) {
                List<TempFormBehaviorV3> behaviors = component.findParent(Form.class).getBehaviors(TempFormBehaviorV3.class);
                if (!behaviors.isEmpty()) {
                    behaviors.get(0).save();
                }
            }
        });
    }

    @Override
    public void onRenderHead(Component component, IHeaderResponse response) {
    }

}