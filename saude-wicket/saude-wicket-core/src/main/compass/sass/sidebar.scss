@import "defines.scss";

nav#sidebar{
	border-right: 1px solid $colorBorder;
    float: left;
	padding-top: 10px;
	z-index: 1;
	font-size: 12px;
	
	ul{
		margin-bottom: 20px;
		border-top: 1px solid #DDDEE3;
		border-bottom: 1px solid #DDDEE3;

		ul{
			display:none;
			
			float: left;
			position: absolute;
			background: $colorBg;
			border: 1px solid darken($colorBg, 7.5%);
			top: 0;
			left: 100%;
			z-index: 1;
			width: 150px;
			margin-bottom: 0;
			
			li{
				padding: 5px 10px 0;
				border: none;
				
				&:hover, &.active, &:active{
					background: none;
				}
			}
			
		}

	}
	
	li{
		list-style: none;
		padding: 5px 10px 5px 15px;
		border-bottom: 1px solid darken($colorBg, 4%);
		border-top: 1px solid lighten($colorBg, 3%);
		position: relative;
		
		&:hover, &.active, &:active{
			background: darken($colorBg, 2.5%);
		}
		
		&:hover{
			
			ul{
				display: block;
			}
		}

		&.header{
			@include background(linear-gradient(#F2F4F0, #F3F5F9));	
			@include border-radius(5px 5px 0 0);
			/*@include pie();*/		
			font-size:14px;
			padding: 5px 10px;
			font-weight: bold;
			border-bottom: 1px solid $colorBorder;
		}
		
	}
        
    .group-buttons.theme-silver {
            background: none repeat scroll 0 0 transparent;
            box-shadow: none;
            padding: 0;
            border-radius: 0 0 0 0;
        li{
            background: none repeat scroll 0 0 transparent;
            border: medium none;
            padding: 0;
            width: 100%;
            &:first-child a{
                border-radius: 7px 0 0 0;
                border-top: 1px solid rgba(0, 0, 0, 1);
            }
            &:last-child a{
                border-radius: 0 0 0 7px;
            }
            a{
                background-color: #F5F5F5;
                background-image: linear-gradient(to bottom, #FFFFFF, #E6E6E6);
                background-repeat: repeat-x;
                /*border-color: rgba(0, 0, 0, 0.1) #B3B3B3;*/
                border-image: none;
                border-radius: 0 0 0 0;
                border-right: 1px solid rgba(0, 0, 0, 0.1);
                border-style: none solid solid;
                border-width: medium 1px 1px;
                box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
                color: #333333;
                cursor: pointer;
                display: block;
                margin: 0;
                padding: 4px 12px;
                text-align: center;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
                padding: 6px 0;
                &:hover, &.active{
                    background: linear-gradient(#0ec6c3,#0a8a91);
                    border-color: #333333;
                    border-right-color: rgba(0,0,0,0.1);
                    text-decoration: none;
                    transition: background-position 0.1s linear 0s;
                    text-shadow: none;
                }
                &.active:after {
                    /*border-right-color: #C1C1C1;*/
                }
                &.warning{
                    border-color: #E55E68  !important;
                    &:before{
                        color: #E55E68;
                    }
                }
            }
        }
    }
    .group-buttons {
        background: none repeat scroll 0 0 #02A093;
        border: medium none;
        border-radius: 10px 0 0 10px;
        box-shadow: 0 0 7px #1E716A inset;
        margin-left: 10px;
        overflow: hidden;
        text-align: center;
        width: 118px;
        
        li {
            background: none repeat scroll 0 0 transparent;
            border-bottom-color: #08887D;
            border-top-color: #0EC1B2;
            padding: 0;
            &:first-child {
                border-top: medium none;
            }
            &:last-child {
                border-bottom: medium none;
            }
            
            a {
                border-radius: 5px 5px 5px 5px;
                color: #E7F5F4;
                display: block;
                margin: 5px;
                padding: 5px;
                text-decoration: none;
                text-shadow: 0 1px 0 #0B897F;
                &.active:after {
                        border-bottom: 7px solid transparent;
                        border-right: 7px solid #FFFFFF;
                        border-top: 7px solid transparent;
                        content: "";
                        margin-top: -5px;
                        position: absolute;
                        right: 0;
                        top: 50%;
                    }
                &:hover, &.active {
                    background: none repeat scroll 0 0 #07BAAB;
                    color: #FFFFFF;
                    img {
                        opacity: 1;
                    }
                }
                &.warning{
                    border: 2px solid #FFD324 !important;
                    &:before {
                        /*color: #FFD324;*/
                        content: "";
                        background: url('../images/css/icons/icon-warn.png');
                        /*font-size: 17px;
                        font-style: italic;
                        font-weight: bold;*/
                        position: absolute;
                        right: 10px;
                        width: 16px;
                        height: 16px;
                    }
                }
                img {
                    display: block;
                    margin: 0 auto 4px;
                    opacity: 0.9;
                }
                span {
                    display: block;
                    margin: 0 auto 4px;
                    opacity: 0.9;
                }
            }
        }
    }	
}

section.with-sidebar{
	float: left;
}

section.section-atendimento{
	float: left;
        padding: 0 10px;
}
