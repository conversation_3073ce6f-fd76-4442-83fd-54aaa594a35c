@import "defines.scss";

.negrito{
    font-weight: bold;
}

tr th { background-position: right; background-repeat:no-repeat; }
tr th.wicket_orderDown {
    background-image: url(../images/css/order_arrow_down.png); }
tr th.wicket_orderUp {
    background-image: url(../images/css/order_arrow_up.png); }
tr th.wicket_orderNone {
    background-image: url(../images/css/order_arrow_off.png);
}

.img-warn{
    background-position: right;
    background-repeat: no-repeat;
    background-image: url(../images/css/ex.warning.png);
    height: 48px;
    width: 48px;
    float: left;
    margin: 5px;
}

.img-error{
    background-position: right;
    background-repeat: no-repeat;
    background-image: url(../images/css/ex.error.png);
    height: 48px;
    width: 48px;
    float: left;
    margin: 5px;
}

.img-info{
    background-position: right;
    background-repeat: no-repeat;
    background-image: url(../images/css/ex.info.png);
    height: 48px;
    width: 48px;
    float: left;
    margin: 5px;
}

.img-question{
    background-position: right;
    background-repeat: no-repeat;
    background-image: url(../images/css/ex.question.png);
    height: 48px;
    width: 48px;
    float: left;
    margin: 5px;
}

.img-sign-document {
    background-position: right;
    background-repeat: no-repeat;
    background-image: url(../images/css/sign_document.png);
    height: 64px;
    width: 64px;
    float: left;
    margin: 5px;
}

#floating-wrapper {
}

#floating.fixed {
    position: fixed;
    top: 0;
}

.report-link{
    text-decoration: underline;
}

div.wicket-aa {
    font-family: "Lucida Grande","Lucida Sans Unicode",Tahoma,Verdana;
    font-size: 12px;
    background-color: white;
    border-width: 1px;
    border-color: #cccccc;
    border-style: solid;
    padding: 2px;
    margin: 1px 0 0 0;
    text-align:left;
}
div.wicket-aa ul { list-style:none; padding: 2px; margin:0; }
div.wicket-aa ul li.selected { background-color: #DBF4EC; padding: 2px; margin:0; }

.hudson{
    background-position: right;
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    margin: 0px;
}

.hudson.blue{
    background-image: url(../images/hudson/blue.png);
}
.hudson.red{
    background-image: url(../images/hudson/red.png);
}
.hudson.yellow{
    background-image: url(../images/hudson/yellow.png);
}
.hudson.grey{
    background-image: url(../images/hudson/grey.png);
}
.hudson.green{
    background-image: url(../images/hudson/green.png);
}
.hudson.orange{
    background-image: url(../images/hudson/orange.png);
}

.uppercase{
    text-transform:uppercase;
}

.lowercase{
    text-transform:lowercase;
}

.celk-icon{
    background-position: right;
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    margin: 0px;
}

.celk-icon.loading{
    background: url("../images/loading/ajax-loader.gif");
}
.celk-icon.calendar{
    background-image: url(../images/relatorio/calendar.png);
}
.celk-icon.ampulheta{
    background-image: url(../images/relatorio/ampulheta.png);
}


fieldset h2 select{
    margin: 0;
    left: 10px;
}

fieldset h2 label{
    font-size: 14px;
}

input[disabled].loading{
    background: url("../images/loading/ajax-loader.gif") no-repeat -100% 0 #E5E5E5;
    display: inline-block;
    opacity: 0.65;
    position: relative;
    background-position-x: 2px;
    background-position-y: 2px;
    color: transparent;
}

.dataTable tr.negativa{
    background: #D3D5D9;
}

.dataTable tr.positiva{
    background: #FFFFFF;
}

.dataTable tr.amarela{
    background: #f9f9b5
}

.dataTable tr.laranja{
    background: #ff953f
}

.dataTable tr.verde{
    background: rgb(102, 204, 102);
}

.dataTable tr.vermelha{
    background: rgb(255, 102, 102);
}

.dataTable tr.vermelha_light{
    background: rgb(255, 153, 153);
}

.dataTable tr.verde_light{
    background: #aaea98;
}


.dataTable{
    margin: 0;
}

.dataTables_wrapper{
    margin: 0;
}

.dataTables_scroll{
    margin: 10px 0 5px;
}

.dataTables_scrollBody .dataTable{
    -webkit-border-radius: 0 0 5px 5px;
    -moz-border-radius: 0 0 5px 5px;
    -ms-border-radius: 0 0 5px 5px;
    -o-border-radius: 0 0 5px 5px;
    border-radius: 0 0 5px 5px;
    border-top: none;
}

.dataTables_scrollHeadInner .dataTable{
    -webkit-border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -ms-border-radius: 5px 5px 0 0;
    -o-border-radius: 5px 5px 0 0;
    border-radius: 5px 5px 0 0;
    margin: 0;
    border-bottom: none;
}

fieldset.label-270 label{
    width: 270px;
}

button.ui-datepicker-trigger{
    display: none;
}



/******* FIM MENU ********/

/*********** Bubble Relatorio *************/
.div-bubble, .div-bubble div{
    float: left !important;
    text-align: left !important;
}

.bubble-menu {
    /*position: relative;*/
    float: left;
    margin-left: 30px;
    text-align: left;
}

.bubble-menu .icon.clipboard-copy {
    margin-top: 13px;
}

.bubble-menu a {
    display: block;
    color: #354049;
    text-shadow: none;
}

.bubble-menu ul {
    list-style-type: none;
}

.bubble-menu li {
    float: left;
    position: relative;
    text-align: center;
}

.bubble-menu ul.bubble-sub-menu {
    position: absolute;
    left: -70px;
    margin-top: 15px;
    z-index: 90;
    display:none;
    width: 420px;
}

.bubble-menu ul.bubble-sub-menu li {
    text-align: left;
}

.bubble-sub-menu a {
    display: inline-block;
}

/*#bubble-menu li:hover ul.bubble-sub-menu {
        display: block;
}*/

.egg{
    position:relative;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
    background-color:#fff;
    border-radius: 3px 3px 3px 3px;
    border: 1px solid rgba(100, 100, 100, 0.4);
    width: 100%;
}
.bubble-menu .egg-container{
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 100px;
    max-height: 400px;
    width: 100%;
}
.report-panel .egg-container{
    overflow-y: auto;
    min-height: 200px;
    /*max-height: 400px;*/
}
/*.egg_Body{border-top:1px solid #D1D8E7;color:#808080;}
.egg_Message{font-size:13px;font-weight:normal;overflow:hidden}*/
.bubble-menu .comment_ui{
    width:98%;
    padding:6px 4px 3px 6px;
}
.report-panel .comment_ui{
    width:98%;
    padding:6px 0px 3px 4px;
}
.comment_ui
{
    border-bottom:1px solid #e5eaf1;clear:left;float:none;overflow:hidden; cursor:pointer;
}
.comment_ui:hover{
    background-color: #F7F7F7;
}
.dddd
{
    background-color:#f2f2f2;border-bottom:1px solid #e5eaf1;clear:left;float:none;overflow:hidden;margin-bottom:2px;padding:6px 4px 3px 6px;width:431px;
}
.comment_text{padding:2px 0 4px; color:#333333; cursor: pointer;}
.bubble-menu .comment_text{width: 100%;}
.report-panel .comment_text{width: 100%;}
.comment_actual_text{display:inline;padding-left:.4em;cursor: pointer;}

ol {
    list-style:none;
    margin:0 auto;
    width:500px;
    margin-top: 20px;
}
.count{
    padding: 0px 3px;
    border-radius: 2px 2px 2px 2px;
    background-color: #F03D25;
    font-size: 9px;
    font-weight: bold;
    color: #fff;
    top: 8px;
    left: 13px;
}
.toppointer{
    height: 11px;
    position: absolute;
    top: -11px;
    width: 20px;
    right: 330px;
}
.clean { display:none}

.top-img{
    background-image: url("../images/relatorio/top.png");
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
    display: inline-block;
}

.images-img{
    background-image: url("../images/relatorio/images.png");
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
    display: inline-block;
    float: left;
}

/******* FIM BUBBLE RELATORIO ********/


span.accesskey{
    text-decoration: underline;
    color: white;
    text-shadow: 0 1px 0 #252B27;
    font-weight: bold;
    vertical-align: top;
}

.textarea-only{
    width:99%;
}

.no-resize{
    resize: none;
}

.warning h1{
    border-bottom: 0px;
    padding-bottom: 0px;
}

.atendimento-node-validacao{
    border: 1px solid #FFD324;
    color: #FFF6BF !important;
}

/**** Histórico Prontuario*****/
.bubble-historico{
    &.active div.bubble{ display: block; }


    div.bubble {
        p{
            font-size: 10px;
            padding: 3px 9px;
            text-align: right;
        }
        background: none repeat scroll 0 0 $colorBg;
        border: 1px solid $colorBorderNivel2;
        @include border-radius(3px);
        position: absolute;
        right: -5px;
        top: 25px;
        z-index: 15;
        padding: 2px 0px;
        display: none;
        @include box-shadow(0 1px 5px rgba(0, 0, 0, 0.33));

        &:after {
            border-bottom: 6px solid $colorBg;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            content: "";
            display: inline-block;
            position: absolute;
            right: 7px;
            top: -6px;
        }

        ul{ display: block};

        li {
            display: block;
            list-style: none;
            border-bottom: 1px solid #E4E6E5;
            padding: 8px;
            height: 10px;
            white-space: pre;
            text-align: right;

            &:hover, &:active, &.active{
                background: $colorBorderNivel2;
                a {
                    color: #FFF;
                }
            }
            a {
                color: $color;
                @include text-shadow(none);
            }

            span{
                margin-left: 7px;
                margin-top: -1px;
            }
        }
    }

}

.bubble-historico{
    float: right !important;

    .count{
        background-color: #F03D25;
        @include border-radius(2px);
        color: #FFFFFF;
    }

    div.bubble{
        right: 10px;
        top: 26px;
        &:after{
            left: auto !important;
            right: 20px !important;
        }
        .bubble-child{
            max-height: 380px;
            width: 230px;
            overflow: auto;
            li{
                white-space: normal;
                width: 230px;
                &:hover{
                    span {color: #FFF;}
                }
                .hudson{
                    float: left !important;
                    margin-right: 2px;;
                }
                a{
                    float: left;
                    text-align: left;
                }
                span{
                    color: #58605B;
                    display: block;
                    font-size: 11px;
                    margin: 3px 0 0;
                    text-align: left;
                    @include text-shadow(none);
                }
                a.icon{
                    float: right;
                    margin-left: 5px;
                    margin-right: 0;
                }
            }
        }
    }
}

/**** Links Prontuario*****/

.icon-text-link{
    text-indent: 20px !important;
    width: auto !important;
    margin-right: 10px;
}

.prontuario-bottom-links{
    background: linear-gradient(#0ec6c3,#0a8a91);
    border-radius: 5px;
    border: 1px solid #0d8280;
}

.prontuario-bottom-links a:first-child{
    border-right: 1px solid #0d8280;
}

.prontuario-bottom-links a:last-child{
    border-right: 1px solid #0d8280;
}

.icon32-text-link{
    display: inline-block;
    width: 90px;
    height: initial;
    text-align: center;
    vertical-align: top;
    color: #fff;
    padding: 6px 0;
}

.btn-voltar-prontuario {
    float: right;
    border-left: 1px solid #0D8280;
}
.icon32-text-link span{
    display: block;
    margin: 0 auto 4px;
    opacity: 0.9;
}

/*****Fieldset Anterior Próximo Prontuario******/

.prev-next.first div.field{
    display: inherit;
}

.prev-next div.field{
    display: none;
}

.prev-next h2 .expand-link{
    font-size: inherit;
}

/*******Container de Mensagens na Home******/

div.warning.ver-todas{
    text-align: center;
    padding: 5px 10px 5px 10px;

}

div.warning.mensagem{
    text-decoration: none !important;
}

div.warning.mensagem:hover{
    background: #FFD644;
}

div.warning.mensagem span.data-mensagem{
    float: right;
}

div.warning.mensagem span.nome-usuario{
    font-weight: bold;
}

/***************Login Bloqueado Message*****************/
div#warning-fixed.warning.login-bloqueado{
    height: 30px;
    font-size: 15px;
    font-weight: bold;
}

div#warning-fixed.warning.login-bloqueado .icon32{
    float: left;
    opacity: 1;
    cursor: default;
}

div#warning-fixed.warning.login-bloqueado .text-login{
    font-size: 15px;
    font-weight: bold;
    vertical-align: middle;
}

/*****************Bem Vindo******************/

div.bemvindo{

}

div.bemvindo h1{
    font-size: 35px;
    text-align:center;
    margin: 7px 15px 0;
}

input.destaque{
    color: red;
}

.tree-theme-windows span.tree-content {
    text-align: left;
}

.tree-theme-windows .tree-folder-closed {
    background-image: none;
}

.tree-theme-windows .tree-folder-open {
    background-image: none;
}

.tree-theme-windows .tree-folder-other {
    background-image: none;
}

.header-bold {
    font-weight: bold;
}

.link-pagina-atual {
    font-weight: bold;
    vertical-align: middle;
}

.link-pagina-outros {
    vertical-align: middle;
}
.table-texto {
    text-align: left;
    margin-left: 5px;
    margin-right: 5px;
}

.table-texto-tab {
    text-align: left;
    margin-left: 25px;
    margin-right: 5px;
}

.img-phone{
    background-repeat: no-repeat;
    background-image: url(../images/logos/phone.png);
    height: 20px;
    width: 26px;
    float: left;
}


.nav {
  margin-top: 5px;
  padding-left: 0;
  list-style: none;
  white-space: nowrap;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 5px 15px;
}
.nav > li > a:hover,
.nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.nav > li.disabled > a {
  color: #777777;
}
.nav > li.disabled > a:hover,
.nav > li.disabled > a:focus {
  color: #777777;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  background-color: #eeeeee;
  border-color: #337ab7;
}
.nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #eeeeee #eeeeee #dddddd;
}
.nav-tabs > li.selected > a,
.nav-tabs > li.selected > a:hover,
.nav-tabs > li.selected > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-bottom-color: transparent;
  cursor: default;

}
.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
  border: 1px solid #939393;
  background: #CCC;
}
.nav-tabs.nav-justified > .selected > a,
.nav-tabs.nav-justified > .selected > a:hover,
.nav-tabs.nav-justified > .selected > a:focus {
  border: 1px solid #00BAAA;
  background: #ffffff;
  font-weight: bold;
  -webkit-box-shadow: 0px 0px 15px -7px rgba(0,0,0,0.75);
  -moz-box-shadow:    0px 0px 15px -7px rgba(0,0,0,0.75);
  box-shadow:         0px 0px 15px -7px rgba(0,0,0,0.75);
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #00BAAA;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs.nav-justified > .selected > a,
  .nav-tabs.nav-justified > .selected > a:hover,
  .nav-tabs.nav-justified > .selected > a:focus {
    border-bottom-color: transparent;
    background: transparent;
    font-weight: bold;
    -webkit-box-shadow: 0px 0px 15px -7px rgba(0,0,0,0.75);
    -moz-box-shadow:    0px 0px 15px -7px rgba(0,0,0,0.75);
    box-shadow:         0px 0px 15px -7px rgba(0,0,0,0.75);
  }
}


.nav-tabs-down {
  border-top: 1px solid #dddddd;
  margin-bottom: 5px;
}
.nav-tabs-down > li {
  float: left;
  margin-top: -1px;
}
.nav-tabs-down > li > a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 0px 0px 4px 4px;
}
.nav-tabs-down > li > a:hover {
  border-color: #eeeeee #eeeeee #dddddd;
}
.nav-tabs-down > li.selected > a,
.nav-tabs-down > li.selected > a:hover,
.nav-tabs-down > li.selected > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-top-color: transparent;
  cursor: default;

}
.nav-tabs-down.nav-justified {
  width: 100%;
  border-top: 0;
}
.nav-tabs-down.nav-justified > li {
  float: none;
}
.nav-tabs-down.nav-justified > li > a {
  text-align: center;
  margin-top: 5px;
}
.nav-tabs-down.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-tabs-down.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs-down.nav-justified > li > a {
    margin-top: 0;
  }
}
.nav-tabs-down.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
  border: 1px solid #939393;
  background: #CCC;
}
.nav-tabs-down.nav-justified > .selected > a,
.nav-tabs-down.nav-justified > .selected > a:hover,
.nav-tabs-down.nav-justified > .selected > a:focus {
  border: 1px solid #00BAAA;
  background: #ffffff;
  font-weight: bold;
  -webkit-box-shadow: 0px 0px 15px -7px rgba(0,0,0,0.75);
  -moz-box-shadow:    0px 0px 15px -7px rgba(0,0,0,0.75);
  box-shadow:         0px 0px 15px -7px rgba(0,0,0,0.75);
}
@media (min-width: 768px) {
  .nav-tabs-down.nav-justified > li > a {
    border-top: 1px solid #00BAAA;
    border-radius: 0px 0px 4px 4px;
  }
  .nav-tabs-down.nav-justified > .selected > a,
  .nav-tabs-down.nav-justified > .selected > a:hover,
  .nav-tabs-down.nav-justified > .selected > a:focus {
    border-top-color: transparent;
    background: transparent;
    font-weight: bold;
    -webkit-box-shadow: 0px 0px 15px -7px rgba(0,0,0,0.75);
    -moz-box-shadow:    0px 0px 15px -7px rgba(0,0,0,0.75);
    box-shadow:         0px 0px 15px -7px rgba(0,0,0,0.75);
  }
}
.info-message > p{
   border: 1px solid rgb(255, 211, 36);
   border-radius: 4px;
   padding: 10px 20px;
   margin-top: 10px;
   background: rgb(213, 237, 248);
   border-color: rgb(146, 202, 228);
   color: rgb(32, 87, 145);
}
.info-message-field > p{
   border: 1px solid rgb(255, 211, 36);
   border-radius: 4px;
   padding: 10px 20px;
   margin-top: 10px;
   background: rgb(213, 237, 248);
   border-color: rgb(146, 202, 228);
   color: rgb(32, 87, 145);
   margin-left: 1%;
   margin-right: 1%;
   margin-top: 0%;
}