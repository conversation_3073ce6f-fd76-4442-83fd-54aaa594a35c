package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueEmpresaSemLoteVencidoDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryEstoqueEmpresaSemLoteVencidoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import java.util.Map;
import org.hibernate.LockMode;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryEstoqueEmpresaSemLoteVencido extends CommandQuery<QueryEstoqueEmpresaSemLoteVencido> {
    
    private QueryEstoqueEmpresaSemLoteVencidoDTOParam param;
    private List<QueryEstoqueEmpresaSemLoteVencidoDTO> result;

    public QueryEstoqueEmpresaSemLoteVencido(QueryEstoqueEmpresaSemLoteVencidoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryEstoqueEmpresaSemLoteVencidoDTO.class.getName());
        
        String subQuery = "(select sum(coalesce(ge.estoqueFisico,0) + coalesce(ge.estoqueEncomendado,0) - coalesce(ge.estoqueReservado,0))"
                + " from GrupoEstoque ge"
                + " where ge.id.estoqueEmpresa.id.empresa = empresa"
                + " and ge.id.estoqueEmpresa.id.produto = produto"
                + " and coalesce(ge.dataValidade, :dataAtual) >= :dataAtual "
                + " and ge.id.codigoDeposito != "
                + "(select coalesce(em.depositoVencido.codigo, 100) from EmpresaMaterial em where em.codigo = empresa.codigo))";

        hql.addToSelect(subQuery, "estoque");
        
        hql.addToFrom("EstoqueEmpresa estoqueEmpresa"
                + " left join estoqueEmpresa.id.produto produto"
                + " left join estoqueEmpresa.id.empresa empresa");
        
        hql.addToWhereWhithAnd("produto =", param.getProduto());
        hql.addToWhereWhithAnd("empresa =", param.getEmpresa());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>)result);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setDate("dataAtual", DataUtil.getDataAtual());
    }
    
    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(1);
    }
    
    @Override
    public List<QueryEstoqueEmpresaSemLoteVencidoDTO> getResult() {
        return result;
    }

}