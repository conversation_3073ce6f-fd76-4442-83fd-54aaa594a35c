package br.com.ksisolucoes.bo.vigilancia.talonarioreceitaa;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.TalonarioReceitaADTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RegistroVisita;
import br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA;

/**
 *
 * <AUTHOR>
 */
public class SalvarTalonarioReceitaA extends AbstractCommandTransaction {

    private TalonarioReceitaADTO talonarioReceitaADTO;
    private RegistroVisita registroVisita;

    public SalvarTalonarioReceitaA(TalonarioReceitaADTO talonarioReceitaADTO) {
        this.talonarioReceitaADTO = talonarioReceitaADTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(CollectionUtils.isEmpty(talonarioReceitaADTO.getReceitaAList())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menor_um_talao"));
        }
        
        for (TalonarioReceitaA tra : talonarioReceitaADTO.getReceitaAList()) {
            BOFactory.save(tra);
        }
    }

    public RegistroVisita getRegistroVisita() {
        return registroVisita;
    }
}
