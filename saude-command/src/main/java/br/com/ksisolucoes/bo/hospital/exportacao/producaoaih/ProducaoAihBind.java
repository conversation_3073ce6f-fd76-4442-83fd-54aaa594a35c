package br.com.ksisolucoes.bo.hospital.exportacao.producaoaih;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoaih.dto.ProducaoAihDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.MotivoSaidaEmergencia;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoAlta;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 1600, crlf = "UNIX")
public class ProducaoAihBind implements IBindVoExport<ProducaoAihDTO> {

    @DataField(pos = 1, length = 8, paddingChar = '0', align = "R", required = true)
    private Long numeroLote;
    @DataField(pos = 2, length = 3, paddingChar = '0', align = "R", required = true)
    private Long quantidadeAihLote;
    @DataField(pos = 3, length = 6, paddingChar = '0', align = "R", pattern = "yyyyMM", required = true)
    private Date anoMesApresentacao;
    @DataField(pos = 4, length = 3, paddingChar = '0', align = "R", required = true)
    private Long sequencialAihLote;
    @DataField(pos = 5, length = 10, paddingChar = '0', align = "R", required = true)
    private String orgaoEmissorAih;
    @DataField(pos = 6, length = 7, paddingChar = '0', align = "R", required = true)
    private String cnesHospital;
    @DataField(pos = 7, length = 6, paddingChar = '0', align = "R", required = true)
    private Long municipioHospital;
    @DataField(pos = 8, length = 13, paddingChar = '0', align = "R", required = true)
    private String numeroAih;
    @DataField(pos = 9, length = 2, paddingChar = '0', align = "R", required = true)
    private Long identificacaoAih;
    @DataField(pos = 10, length = 2, paddingChar = '0', align = "R", required = true)
    private Long especialidadeAih;
    @DataField(pos = 11, length = 45, paddingChar = '0', align = "L", required = true)
    private String filler;
    @DataField(pos = 12, length = 2, paddingChar = '0', align = "R", required = true)
    private Long modalidadeAih;
    @DataField(pos = 13, length = 3, paddingChar = '0', align = "R", required = true)
    private Long sequencialAih5;
    @DataField(pos = 14, length = 13, paddingChar = '0', align = "R", required = true)
    private String numeroAihProxima;
    @DataField(pos = 15, length = 13, paddingChar = '0', align = "R", required = true)
    private String numeroAihAnterior;
    @DataField(pos = 16, length = 8, paddingChar = '0', align = "R", required = true, pattern = "yyyyMMdd")
    private Date dataEmissaoAih;
    @DataField(pos = 17, length = 8, paddingChar = '0', align = "R", required = true, pattern = "yyyyMMdd")
    private Date dataInternacao;
    @DataField(pos = 18, length = 8, paddingChar = '0', align = "R", required = true, pattern = "yyyyMMdd")
    private Date dataSaida;
    @DataField(pos = 19, length = 10, paddingChar = '0', align = "R", required = true)
    private Long procedimentoSolicitado;
    @DataField(pos = 20, length = 1, paddingChar = '0', align = "R", required = true)
    private Long mudancaProcedimento;
    @DataField(pos = 21, length = 10, paddingChar = '0', align = "R", required = true)
    private Long procedimentoRealizado;
    @DataField(pos = 22, length = 2, paddingChar = '0', align = "R", required = true)
    private Long caraterInternacao;
    @DataField(pos = 23, length = 2, paddingChar = '0', align = "R", required = true)
    private Long motivoSaidaPermanencia;
    @DataField(pos = 24, length = 1, paddingChar = '0', align = "R", required = true)
    private Long identificadorDocMedicoSolicitante;
    @DataField(pos = 25, length = 15, paddingChar = '0', align = "R", required = true)
    private String documentoMedicoSolicitante;
    @DataField(pos = 26, length = 1, paddingChar = '0', align = "R", required = true)
    private Long identificadorDocMedicoResponsavel;
    @DataField(pos = 27, length = 15, paddingChar = '0', align = "R", required = true)
    private String documentoMedicoResponsavel;
    @DataField(pos = 28, length = 1, paddingChar = '0', align = "R", required = true)
    private Long identificadorDocDiretorClinico;
    @DataField(pos = 29, length = 15, align = "L", required = true)
    private String documentoDiretorClinico;
    @DataField(pos = 30, length = 1, paddingChar = '0', align = "R", required = true)
    private Long identificadorDocMedicoAutorizador;
    @DataField(pos = 31, length = 15, paddingChar = '0', align = "R", required = true)
    private String documentoMedicoAutorizador;
    @DataField(pos = 32, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoPrincipal;
    @DataField(pos = 33, length = 4, paddingChar = '0', align = "R", required = true)
    private String filler5;
    @DataField(pos = 34, length = 4, paddingChar = '0', align = "R", required = true)
    private String filler6;
    @DataField(pos = 35, length = 4, paddingChar = '0', align = "R", required = true)
    private String filler7;
    @DataField(pos = 36, length = 3, paddingChar = '0', align = "R", required = true)
    private String filler1;
    @DataField(pos = 37, length = 70, align = "L", required = true)
    private String nomePaciente;
    @DataField(pos = 38, length = 8, align = "R", required = true, pattern = "yyyyMMdd")
    private Date dataNascimentoPaciente;
    @DataField(pos = 39, length = 1, align = "L", required = true)
    private String sexoPaciente;
    @DataField(pos = 40, length = 2, paddingChar = '0', align = "R", required = true)
    private Long racaCor;
    @DataField(pos = 41, length = 70, align = "L", required = true)
    private String nomeMaePaciente;
    @DataField(pos = 42, length = 70, align = "L", required = true)
    private String nomeResponsavelPaciente;
    @DataField(pos = 43, length = 1, paddingChar = '0', align = "R", required = true)
    private Long tipoDocumentoPaciente;
    @DataField(pos = 44, length = 4, paddingChar = '0', align = "R", required = true)
    private String etniaIndigena;
    @DataField(pos = 45, length = 5, paddingChar = '0', align = "R", required = true)
    private String codigoSolicitacaoLiberacao;
    @DataField(pos = 46, length = 2, paddingChar = '0', align = "R", required = true)
    private String filler2;
    @DataField(pos = 47, length = 15, paddingChar = '0', align = "R", required = true)
    private Long numeroCnsPaciente;
    @DataField(pos = 48, length = 3, paddingChar = '0', align = "R", required = true)
    private Long nacionalidadePaciente;
    @DataField(pos = 49, length = 3, paddingChar = '0', align = "R", required = true)
    private Long codigoTipoLogradouro;
    @DataField(pos = 50, length = 50, align = "L", required = true)
    private String logradouroPaciente;
    @DataField(pos = 51, length = 7, paddingChar = '0', align = "R", required = true)
    private String numeroLogradouroPaciente;
    @DataField(pos = 52, length = 15, align = "L", required = true, clip = true)
    private String complementoEnderecoPaciente;
    @DataField(pos = 53, length = 30, align = "L", required = true)
    private String bairroPaciente;
    @DataField(pos = 54, length = 6, paddingChar = '0', align = "R", required = true)
    private Long codMunicipioEnderecoPaciente;
    @DataField(pos = 55, length = 2, paddingChar = '0', align = "R", required = true)
    private String ufEnderecoPaciente;
    @DataField(pos = 56, length = 8, paddingChar = '0', align = "R", required = true)
    private String cepEnderecoPaciente;
    @DataField(pos = 57, length = 15, paddingChar = '0', align = "R", required = true)
    private Long numeroProntuario;
    @DataField(pos = 58, length = 4, paddingChar = '0', align = "R", required = true)
    private String numeroEnfermaria;
    @DataField(pos = 59, length = 4, paddingChar = '0', align = "R", required = true)
    private String numeroLeito;
    @DataField(pos = 60, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse1indicadorDocProfissional;
    @DataField(pos = 61, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse1identificacaoProfissional;
    @DataField(pos = 62, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse1cboProfissional;
    @DataField(pos = 63, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse1identificadorEquipe;
    @DataField(pos = 64, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse1identificadorPrestadorServico;
    @DataField(pos = 65, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse1identificacaoPrestadorServico;
    @DataField(pos = 66, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse1indicadorDocumentoExecutor;
    @DataField(pos = 67, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse1identificadorExecutor;
    @DataField(pos = 68, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse1codigoProcedimento;
    @DataField(pos = 69, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse1quantidadeProcedimento;
    @DataField(pos = 70, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse1competencia;
    @DataField(pos = 71, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse1servico;
    @DataField(pos = 72, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse1classificacao;
    @DataField(pos = 73, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse2indicadorDocProfissional;
    @DataField(pos = 74, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse2identificacaoProfissional;
    @DataField(pos = 75, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse2cboProfissional;
    @DataField(pos = 76, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse2identificadorEquipe;
    @DataField(pos = 77, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse2identificadorPrestadorServico;
    @DataField(pos = 78, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse2identificacaoPrestadorServico;
    @DataField(pos = 79, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse2indicadorDocumentoExecutor;
    @DataField(pos = 80, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse2identificadorExecutor;
    @DataField(pos = 81, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse2codigoProcedimento;
    @DataField(pos = 82, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse2quantidadeProcedimento;
    @DataField(pos = 83, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse2competencia;
    @DataField(pos = 84, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse2servico;
    @DataField(pos = 85, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse2classificacao;
    @DataField(pos = 86, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse3indicadorDocProfissional;
    @DataField(pos = 87, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse3identificacaoProfissional;
    @DataField(pos = 88, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse3cboProfissional;
    @DataField(pos = 89, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse3identificadorEquipe;
    @DataField(pos = 90, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse3identificadorPrestadorServico;
    @DataField(pos = 91, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse3identificacaoPrestadorServico;
    @DataField(pos = 92, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse3indicadorDocumentoExecutor;
    @DataField(pos = 93, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse3identificadorExecutor;
    @DataField(pos = 94, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse3codigoProcedimento;
    @DataField(pos = 95, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse3quantidadeProcedimento;
    @DataField(pos = 96, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse3competencia;
    @DataField(pos = 97, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse3servico;
    @DataField(pos = 98, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse3classificacao;
    @DataField(pos = 99, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse4indicadorDocProfissional;
    @DataField(pos = 100, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse4identificacaoProfissional;
    @DataField(pos = 101, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse4cboProfissional;
    @DataField(pos = 102, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse4identificadorEquipe;
    @DataField(pos = 103, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse4identificadorPrestadorServico;
    @DataField(pos = 104, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse4identificacaoPrestadorServico;
    @DataField(pos = 105, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse4indicadorDocumentoExecutor;
    @DataField(pos = 106, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse4identificadorExecutor;
    @DataField(pos = 107, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse4codigoProcedimento;
    @DataField(pos = 108, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse4quantidadeProcedimento;
    @DataField(pos = 109, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse4competencia;
    @DataField(pos = 110, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse4servico;
    @DataField(pos = 111, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse4classificacao;
    @DataField(pos = 112, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse5indicadorDocProfissional;
    @DataField(pos = 113, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse5identificacaoProfissional;
    @DataField(pos = 114, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse5cboProfissional;
    @DataField(pos = 115, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse5identificadorEquipe;
    @DataField(pos = 116, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse5identificadorPrestadorServico;
    @DataField(pos = 117, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse5identificacaoPrestadorServico;
    @DataField(pos = 118, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse5indicadorDocumentoExecutor;
    @DataField(pos = 119, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse5identificadorExecutor;
    @DataField(pos = 120, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse5codigoProcedimento;
    @DataField(pos = 121, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse5quantidadeProcedimento;
    @DataField(pos = 122, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse5competencia;
    @DataField(pos = 123, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse5servico;
    @DataField(pos = 124, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse5classificacao;
    @DataField(pos = 125, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse6indicadorDocProfissional;
    @DataField(pos = 126, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse6identificacaoProfissional;
    @DataField(pos = 127, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse6cboProfissional;
    @DataField(pos = 128, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse6identificadorEquipe;
    @DataField(pos = 129, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse6identificadorPrestadorServico;
    @DataField(pos = 130, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse6identificacaoPrestadorServico;
    @DataField(pos = 131, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse6indicadorDocumentoExecutor;
    @DataField(pos = 132, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse6identificadorExecutor;
    @DataField(pos = 133, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse6codigoProcedimento;
    @DataField(pos = 134, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse6quantidadeProcedimento;
    @DataField(pos = 135, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse6competencia;
    @DataField(pos = 136, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse6servico;
    @DataField(pos = 137, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse6classificacao;
    @DataField(pos = 138, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse7indicadorDocProfissional;
    @DataField(pos = 139, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse7identificacaoProfissional;
    @DataField(pos = 140, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse7cboProfissional;
    @DataField(pos = 141, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse7identificadorEquipe;
    @DataField(pos = 142, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse7identificadorPrestadorServico;
    @DataField(pos = 143, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse7identificacaoPrestadorServico;
    @DataField(pos = 144, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse7indicadorDocumentoExecutor;
    @DataField(pos = 145, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse7identificadorExecutor;
    @DataField(pos = 146, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse7codigoProcedimento;
    @DataField(pos = 147, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse7quantidadeProcedimento;
    @DataField(pos = 148, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse7competencia;
    @DataField(pos = 149, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse7servico;
    @DataField(pos = 150, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse7classificacao;
    @DataField(pos = 151, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse8indicadorDocProfissional;
    @DataField(pos = 152, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse8identificacaoProfissional;
    @DataField(pos = 153, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse8cboProfissional;
    @DataField(pos = 154, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse8identificadorEquipe;
    @DataField(pos = 155, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse8identificadorPrestadorServico;
    @DataField(pos = 156, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse8identificacaoPrestadorServico;
    @DataField(pos = 157, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse8indicadorDocumentoExecutor;
    @DataField(pos = 158, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse8identificadorExecutor;
    @DataField(pos = 159, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse8codigoProcedimento;
    @DataField(pos = 160, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse8quantidadeProcedimento;
    @DataField(pos = 161, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse8competencia;
    @DataField(pos = 162, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse8servico;
    @DataField(pos = 163, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse8classificacao;
    @DataField(pos = 164, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse9indicadorDocProfissional;
    @DataField(pos = 165, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse9identificacaoProfissional;
    @DataField(pos = 166, length = 6, paddingChar = '0', align = "R", required = true)
    private String pse9cboProfissional;
    @DataField(pos = 167, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse9identificadorEquipe;
    @DataField(pos = 168, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse9identificadorPrestadorServico;
    @DataField(pos = 169, length = 14, paddingChar = '0', align = "R", required = true)
    private String pse9identificacaoPrestadorServico;
    @DataField(pos = 170, length = 1, paddingChar = '0', align = "R", required = true)
    private Long pse9indicadorDocumentoExecutor;
    @DataField(pos = 171, length = 15, paddingChar = '0', align = "R", required = true)
    private String pse9identificadorExecutor;
    @DataField(pos = 172, length = 10, paddingChar = '0', align = "R", required = true)
    private Long pse9codigoProcedimento;
    @DataField(pos = 173, length = 3, paddingChar = '0', align = "R", required = true)
    private Long pse9quantidadeProcedimento;
    @DataField(pos = 174, length = 6, align = "R", pattern = "yyyyMM", required = true)
    private Date pse9competencia;
    @DataField(pos = 175, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse9servico;
    @DataField(pos = 176, length = 3, paddingChar = '0', align = "R", required = true)
    private String pse9classificacao;
    @DataField(pos = 177, length = 19, paddingChar = '0', align = "R", required = true)
    private String filler3;
    @DataField(pos = 178, length = 1, paddingChar = '0', align = "R", required = true)
    private Long saidaUtiNeonatal;
    @DataField(pos = 179, length = 4, paddingChar = '0', align = "R", required = true)
    private Long pesoNascer;
    @DataField(pos = 180, length = 1, paddingChar = '0', align = "R", required = true)
    private Long numeroMesesGestao;
    @DataField(pos = 181, length = 14, paddingChar = '0', align = "R", required = true)
    private String cnpjEmpregador;
    @DataField(pos = 182, length = 6, paddingChar = '0', align = "R", required = true)
    private String codigoCbor;
    @DataField(pos = 183, length = 3, paddingChar = '0', align = "R", required = true)
    private String codigoCnaer;
    @DataField(pos = 184, length = 1, paddingChar = '0', align = "R", required = true)
    private String tipoVinculoPrevidencia;
    @DataField(pos = 185, length = 1, paddingChar = '0', align = "R", required = true)
    private Long quantidadeNascidosVivos;
    @DataField(pos = 186, length = 1, paddingChar = '0', align = "R", required = true)
    private Long quantidadeNascidosMortos;
    @DataField(pos = 187, length = 1, paddingChar = '0', align = "R", required = true)
    private Long quantidadeSaidaAlta;
    @DataField(pos = 188, length = 1, paddingChar = '0', align = "R", required = true)
    private Long quantidadeSaidaTransferencia;
    @DataField(pos = 189, length = 1, paddingChar = '0', align = "R", required = true)
    private Long quantidadeSaidaObito;
    @DataField(pos = 190, length = 10, paddingChar = '0', align = "R", required = true)
    private String fiiler;
    @DataField(pos = 191, length = 2, paddingChar = '0', align = "R", required = true)
    private Long numeroFilhos;
    @DataField(pos = 192, length = 1, paddingChar = '0', align = "R", required = true)
    private Long grauInstrucao;
    @DataField(pos = 193, length = 4, paddingChar = '0', align = "R", required = true)
    private String cidNotificacaoIndicacao;
    @DataField(pos = 194, length = 2, paddingChar = '0', align = "R", required = true)
    private Long tipoMetodoContraceptivo;
    @DataField(pos = 195, length = 2, paddingChar = '0', align = "R", required = true)
    private Long tipoMetodoContraceptivo2;
    @DataField(pos = 196, length = 1, paddingChar = '0', align = "R", required = true)
    private Long gestaoAltoRisco;
    @DataField(pos = 197, length = 35, paddingChar = '0', align = "R", required = true)
    private String reservado;
    @DataField(pos = 198, length = 12, paddingChar = '0', align = "R", required = true)
    private String numeroPreNatal;
    @DataField(pos = 199, length = 32, paddingChar = '0', align = "R", required = true)
    private String numeroDocumentoPaciente;
    @DataField(pos = 200, length = 2, paddingChar = '0', align = "R", required = true)
    private String telefonePacienteDdd;
    @DataField(pos = 201, length = 9, paddingChar = '0', align = "R", required = true)
    private String telefonePaciente;
    @DataField(pos = 202, length = 50, align = "L", required = true)
    private String justificativaCns;
    @DataField(pos = 203, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario1;
    @DataField(pos = 204, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario1Class;
    @DataField(pos = 205, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario2;
    @DataField(pos = 206, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario2Class;
    @DataField(pos = 207, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario3;
    @DataField(pos = 208, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario3Class;
    @DataField(pos = 209, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario4;
    @DataField(pos = 210, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario4Class;
    @DataField(pos = 211, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario5;
    @DataField(pos = 212, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario5Class;
    @DataField(pos = 213, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario6;
    @DataField(pos = 214, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario6Class;
    @DataField(pos = 215, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario7;
    @DataField(pos = 216, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario7Class;
    @DataField(pos = 217, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario8;
    @DataField(pos = 218, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario8Class;
    @DataField(pos = 219, length = 4, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario9;
    @DataField(pos = 220, length = 1, paddingChar = '0', align = "R", required = true)
    private String diagnosticoSecundario9Class;
    @DataField(pos = 221, length = 165, paddingChar = '0', align = "R", required = true)
    private String filler4;

    @Override
    public void buildProperties(ProducaoAihDTO vo) {

        numeroLote = vo.getLoteAih().getCodigo();
        quantidadeAihLote = vo.getLoteAih().getQuantidadeAih();
        anoMesApresentacao = vo.getLoteAih().getDataApresentacao();
        sequencialAihLote = vo.getAih().getSequencialAihLote();

        if (vo.getEmpresaPrincipal() != null) {
            orgaoEmissorAih = vo.getEmpresaPrincipal().getOrgaoEmissor();
            cnesHospital = vo.getEmpresaPrincipal().getCnes();
        }
        if (vo.getCidade() != null) {
            municipioHospital = vo.getCidade().getCodigo();
        }
        numeroAih = vo.getAih().getNroAutorizacao();
        //identificacaoAih = 1L;
        //especialidadeAih
        modalidadeAih = 2L;
        sequencialAih5 = 0L;
        dataEmissaoAih = (vo.getAih().getDtAutorizacao());
        if (vo.getAtendimentoInformacao() != null) {
            dataInternacao = vo.getAtendimentoInformacao().getDataChegada();
        }
        if (vo.getAtendimentoAlta() != null) {
            dataSaida = vo.getAtendimentoAlta().getDataAlta();
            motivoSaidaPermanencia = getMotivoSaidaPermanenciaSISAIH(vo.getAtendimentoAlta().getMotivoAlta());
        }
        if (vo.getProcedimentoSolicitado() != null) {
            procedimentoSolicitado = vo.getProcedimentoSolicitado().getCodigo();
        }
        if (vo.isExistsSoliMudancaProcedimento()) {
            mudancaProcedimento = 1L;
        } else {
            mudancaProcedimento = 2L;
        }
        if (vo.getProcedimentoRealizado() != null) {
            procedimentoRealizado = vo.getProcedimentoRealizado().getCodigo();
        }
        caraterInternacao = vo.getAih().getCaraterInternacao();
        if (vo.getLeitoQuarto() != null && vo.getLeitoQuarto().getEspecialidadeLeito() != null) {
            especialidadeAih = vo.getLeitoQuarto().getEspecialidadeLeito().getCodigo();
        }

        identificadorDocMedicoSolicitante = vo.getAih().getTpDocProfSol();
        documentoMedicoSolicitante = vo.getAih().getNroDocProfSol();

        if (vo.getProfissionalResponsavel().getCodigoCns() != null) {
            identificadorDocMedicoResponsavel = 2L;
            documentoMedicoResponsavel = vo.getProfissionalResponsavel().getCodigoCns();
        }

        if (vo.getAih().getNumeroAutorizacaoAihAnterior() != null) {
            numeroAihAnterior = vo.getAih().getNumeroAutorizacaoAihAnterior().toString();
        }
        if (vo.getAih().getNumeroAutorizacaoAihPosterior() != null) {
            numeroAihProxima = vo.getAih().getNumeroAutorizacaoAihPosterior().toString();
        }

        if (vo.getProfissionalDiretor().getCodigoCns() != null) {
            identificadorDocDiretorClinico = 2L;
            documentoDiretorClinico = vo.getProfissionalDiretor().getCodigoCns();
        } else if (vo.getProfissionalDiretor().getCpf() != null) {
            identificadorDocDiretorClinico = 1L;
            documentoDiretorClinico = vo.getProfissionalDiretor().getCpf();
        }

        identificadorDocMedicoAutorizador = vo.getAih().getTpDocProfAut();
        documentoMedicoAutorizador = vo.getAih().getNroDocProfAut();
        if (vo.getCidPrincipal() != null) {
            diagnosticoPrincipal = vo.getCidPrincipal().getCodigo().substring(0, 4);
        }

        if (vo.getAih().getCidSecundario() != null) {
            diagnosticoSecundario1 = vo.getAih().getCidSecundario().getCodigo();
            //sugestão do anderson, mas segundo o layout do documento existe a opção 0, 1;
            diagnosticoSecundario1Class = "1";
        }

        if (vo.getUsuarioCadsus() != null) {
            nomePaciente = vo.getUsuarioCadsus().getNome();
            dataNascimentoPaciente = vo.getUsuarioCadsus().getDataNascimento();
            sexoPaciente = vo.getUsuarioCadsus().getSexo();
            if (vo.getUsuarioCadsus().getEtniaIndigena() != null && vo.getUsuarioCadsus().getEtniaIndigena().getCodigoSus() != null) {
                etniaIndigena = vo.getUsuarioCadsus().getEtniaIndigena().getCodigoSus();
            }
        }

        if (vo.getRaca() != null) {
            racaCor = vo.getRaca().getCodigo();
        } else {
            racaCor = 99L;
        }
        nomeMaePaciente = vo.getUsuarioCadsus().getNomeMae();

        if (vo.getResponsavel() != null) {
            nomeResponsavelPaciente = vo.getResponsavel().getNome();
        } else {
            nomeResponsavelPaciente = vo.getUsuarioCadsus().getNome();
        }

//        tipoDocumentoPaciente = 3L;
//        if (vo.getUsuarioCadsusDocumento() != null) {
//            numeroDocumentoPaciente = vo.getUsuarioCadsusDocumento().getNumeroMatricula();
//        }
        setDocumentos(vo);
        //etiniaIndigina
        codigoSolicitacaoLiberacao = resolverSomatorioCodigoSolicitacaoLiberacao(vo);
        justificativaCns = vo.getContaPaciente().getLiberacaoCriticaJustificativaCns();
        numeroCnsPaciente = vo.getNroCnsPaciente();
        if (vo.getUsuarioCadsus().getPaisNascimento() != null) {
            nacionalidadePaciente = vo.getUsuarioCadsus().getPaisNascimento().getCodigo();
        }
        if (vo.getTipoLogradouroCadsus() != null) {
            codigoTipoLogradouro = vo.getTipoLogradouroCadsus().getCodigo();
        }
        if (vo.getEnderecoUsuarioCadsus() != null) {
            logradouroPaciente = StringUtil.removeAcentos(vo.getEnderecoUsuarioCadsus().getLogradouro());
            numeroLogradouroPaciente = vo.getEnderecoUsuarioCadsus().getNumeroLogradouro();
            complementoEnderecoPaciente = StringUtil.removeAcentos(vo.getEnderecoUsuarioCadsus().getComplementoLogradouro());
            bairroPaciente = StringUtil.removeAcentos(vo.getEnderecoUsuarioCadsus().getBairro());
            cepEnderecoPaciente = vo.getEnderecoUsuarioCadsus().getCep();
        }
        if (vo.getCidadePaciente() != null) {
            codMunicipioEnderecoPaciente = vo.getCidadePaciente().getCodigo();
        }
        if (vo.getEstado() != null) {
            ufEnderecoPaciente = vo.getEstado().getSigla();
        }
        if (vo.getUsuarioCadsus() != null) {
            numeroProntuario = vo.getUsuarioCadsus().getCodigo();
        }
        if (vo.getUsuarioCadsus().getTelefone() != null) {
            telefonePacienteDdd = vo.getUsuarioCadsus().getTelefone().substring(0, 2);
            telefonePaciente = vo.getUsuarioCadsus().getTelefone().substring(2);
        }

        if (vo.getQuartoInternacao() != null) {
            numeroEnfermaria = vo.getQuartoInternacao().getNumeroEnfermariaAih();
        }
        if (vo.getLeitoQuarto() != null) {
            numeroLeito = vo.getLeitoQuarto().getNumeroLeitoAih();
        }

        if (vo.getComplementarParto() != null) {
            this.numeroPreNatal = vo.getComplementarParto().getNumeroPrenatal();
            this.quantidadeNascidosVivos = vo.getComplementarParto().getQuantidadeVivos();
            this.quantidadeNascidosMortos = vo.getComplementarParto().getQuantidadeMortos();
            this.quantidadeSaidaAlta = vo.getComplementarParto().getQuantidadeAlta();
            this.quantidadeSaidaTransferencia = vo.getComplementarParto().getQuantidadeTransferencia();
            this.quantidadeSaidaObito = vo.getComplementarParto().getQuantidadeObito();
        }

        if (vo.getComplementarLaqueadura() != null) {
            this.numeroFilhos = vo.getComplementarLaqueadura().getQuantidadeFilhos();
            this.grauInstrucao = vo.getComplementarLaqueadura().getGrauInstrucao();
            this.tipoMetodoContraceptivo = vo.getComplementarLaqueadura().getTipoContraceptivo1();
            this.tipoMetodoContraceptivo2 = vo.getComplementarLaqueadura().getTipoContraceptivo2();
            this.gestaoAltoRisco = vo.getComplementarLaqueadura().getGestacaoAltoRisco();

            if (vo.getComplementarLaqueadura().getCid() != null) {
                this.cidNotificacaoIndicacao = vo.getComplementarLaqueadura().getCid().getCodigo();
            }
        }

        if (vo.getComplementarUtiNeonatal() != null) {
//            this.pse9competencia = vo.getComplementarUtiNeonatal().getDataCompetencia();
//            this.pse9quantidadeProcedimento = vo.getComplementarUtiNeonatal().getQuantidadeDias();
            this.numeroMesesGestao = vo.getComplementarUtiNeonatal().getMesesGestacao();
            this.pesoNascer = vo.getComplementarUtiNeonatal().getPesoNascimento();
            this.saidaUtiNeonatal = vo.getComplementarUtiNeonatal().getMotivoSaida();
        }
    }

    private Long getMotivoSaidaPermanenciaSISAIH(Long motivo) {
        Long motivoSaidaPermanenciaSISAIH = null;

        if (AtendimentoAlta.MotivoAlta.CURADO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Alta.CURADO.getValue();
        } else if (AtendimentoAlta.MotivoAlta.EVASAO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Alta.EVASAO.getValue();
        } else if (AtendimentoAlta.MotivoAlta.LIBERADO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Alta.OUTROS_MOTIVOS.getValue();
        } else if (AtendimentoAlta.MotivoAlta.MELHORADO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Alta.MELHORADO.getValue();
        } else if (AtendimentoAlta.MotivoAlta.OBITO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Obito.DECLARACAO_OBITO_FORNECIDA_MEDICO_ASSISTENTE.getValue();
        } else if (AtendimentoAlta.MotivoAlta.PEDIDO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.OutrosMotivos.ENCERRAMENTO_ADMINISTRATIVO.getValue();
        } else if (AtendimentoAlta.MotivoAlta.TRANSFERIDO.value().equals(motivo)) {
            motivoSaidaPermanenciaSISAIH = MotivoSaidaEmergencia.Transferencia.TRANSFERIDO_OUTRO_ESTABELECIMENTO.getValue();
        } else if (AtendimentoAlta.MotivoAlta.INTERNACAO.value().equals(motivo)) {
            // Sem definição
        }

        return motivoSaidaPermanenciaSISAIH;
    }

    public Empresa carregarEmpresaPrincipal(Empresa empresa) {
        return LoadManager.getInstance(Empresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, empresa.getCodigo()))
                .addProperties(new HQLProperties(Empresa.class, Empresa.PROP_EMPRESA_PRINCIPAL).getProperties())
                .addProperties(new HQLProperties(Empresa.class, Empresa.PROP_PROFISSIONAL_DIRETOR).getProperties())
                .addProperties(new HQLProperties(Empresa.class).getProperties())
                .start().getVO();
    }

    public UsuarioCadsusDocumento carregarDocumento(UsuarioCadsus u, Long tp) {
        return LoadManager.getInstance(UsuarioCadsusDocumento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS, u))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_TIPO_DOCUMENTO, tp))
                .addProperties(new HQLProperties(UsuarioCadsusDocumento.class).getProperties())
                .start().getVO();
    }

    private void setDocumentos(ProducaoAihDTO vo) {
        UsuarioCadsusDocumento doc = null;

        if ((vo.getUsuarioCadsus().getCpf() != null && !vo.getUsuarioCadsus().getCpf().isEmpty())) {
            tipoDocumentoPaciente = 4L;
            numeroDocumentoPaciente = vo.getUsuarioCadsus().getCpf();
        } else if ((vo.getUsuarioCadsus().getRg() != null && !vo.getUsuarioCadsus().getRg().isEmpty())) {
            tipoDocumentoPaciente = 2L;
            numeroDocumentoPaciente = vo.getUsuarioCadsus().getRg();
        } else if (carregarDocumento(vo.getUsuarioCadsus(), TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO) != null) {
            tipoDocumentoPaciente = 6L;
            numeroDocumentoPaciente = carregarDocumento(vo.getUsuarioCadsus(), TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO).getNumeroMatricula();
        } else {
            tipoDocumentoPaciente = 5L;
        }
    }

    private String resolverSomatorioCodigoSolicitacaoLiberacao(ProducaoAihDTO vo) {
        Long soma = 6L;

        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaTempoPermanencia())) {
            soma += 1L;
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaIdadeMenor())) {
            soma += 2L;
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaIdadeMaior())) {
            soma += 4L;
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaQuantidadeMaxima())) {
            soma += 8L;
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaTelefone())) {
            soma += 64L;
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(vo.getContaPaciente().getLiberacaoCriticaCns())) {
            soma += 128L;
        }

        if (soma > 6L) {
            return soma.toString();
        } else {
            return null;
        }
    }

    public Long getPse1indicadorDocProfissional() {
        return pse1indicadorDocProfissional;
    }

    public void setPse1indicadorDocProfissional(Long pse1indicadorDocProfissional) {
        this.pse1indicadorDocProfissional = pse1indicadorDocProfissional;
    }

    public String getPse1identificacaoProfissional() {
        return pse1identificacaoProfissional;
    }

    public void setPse1identificacaoProfissional(String pse1identificacaoProfissional) {
        this.pse1identificacaoProfissional = pse1identificacaoProfissional;
    }

    public String getPse1cboProfissional() {
        return pse1cboProfissional;
    }

    public void setPse1cboProfissional(String pse1cboProfissional) {
        this.pse1cboProfissional = pse1cboProfissional;
    }

    public Long getPse1identificadorEquipe() {
        return pse1identificadorEquipe;
    }

    public void setPse1identificadorEquipe(Long pse1identificadorEquipe) {
        this.pse1identificadorEquipe = pse1identificadorEquipe;
    }

    public Long getPse1identificadorPrestadorServico() {
        return pse1identificadorPrestadorServico;
    }

    public void setPse1identificadorPrestadorServico(Long pse1identificadorPrestadorServico) {
        this.pse1identificadorPrestadorServico = pse1identificadorPrestadorServico;
    }

    public String getPse1identificacaoPrestadorServico() {
        return pse1identificacaoPrestadorServico;
    }

    public void setPse1identificacaoPrestadorServico(String pse1identificacaoPrestadorServico) {
        this.pse1identificacaoPrestadorServico = pse1identificacaoPrestadorServico;
    }

    public Long getPse1indicadorDocumentoExecutor() {
        return pse1indicadorDocumentoExecutor;
    }

    public void setPse1indicadorDocumentoExecutor(Long pse1indicadorDocumentoExecutor) {
        this.pse1indicadorDocumentoExecutor = pse1indicadorDocumentoExecutor;
    }

    public String getPse1identificadorExecutor() {
        return pse1identificadorExecutor;
    }

    public void setPse1identificadorExecutor(String pse1identificadorExecutor) {
        this.pse1identificadorExecutor = pse1identificadorExecutor;
    }

    public Date getPse1competencia() {
        return pse1competencia;
    }

    public void setPse1competencia(Date pse1competencia) {
        this.pse1competencia = pse1competencia;
    }

    public String getPse1servico() {
        return pse1servico;
    }

    public void setPse1servico(String pse1servico) {
        this.pse1servico = pse1servico;
    }

    public String getPse1classificacao() {
        return pse1classificacao;
    }

    public void setPse1classificacao(String pse1classificacao) {
        this.pse1classificacao = pse1classificacao;
    }

    public Long getPse2indicadorDocProfissional() {
        return pse2indicadorDocProfissional;
    }

    public void setPse2indicadorDocProfissional(Long pse2indicadorDocProfissional) {
        this.pse2indicadorDocProfissional = pse2indicadorDocProfissional;
    }

    public String getPse2identificacaoProfissional() {
        return pse2identificacaoProfissional;
    }

    public void setPse2identificacaoProfissional(String pse2identificacaoProfissional) {
        this.pse2identificacaoProfissional = pse2identificacaoProfissional;
    }

    public String getPse2cboProfissional() {
        return pse2cboProfissional;
    }

    public void setPse2cboProfissional(String pse2cboProfissional) {
        this.pse2cboProfissional = pse2cboProfissional;
    }

    public Long getPse2identificadorEquipe() {
        return pse2identificadorEquipe;
    }

    public void setPse2identificadorEquipe(Long pse2identificadorEquipe) {
        this.pse2identificadorEquipe = pse2identificadorEquipe;
    }

    public Long getPse2identificadorPrestadorServico() {
        return pse2identificadorPrestadorServico;
    }

    public void setPse2identificadorPrestadorServico(Long pse2identificadorPrestadorServico) {
        this.pse2identificadorPrestadorServico = pse2identificadorPrestadorServico;
    }

    public String getPse2identificacaoPrestadorServico() {
        return pse2identificacaoPrestadorServico;
    }

    public void setPse2identificacaoPrestadorServico(String pse2identificacaoPrestadorServico) {
        this.pse2identificacaoPrestadorServico = pse2identificacaoPrestadorServico;
    }

    public Long getPse2indicadorDocumentoExecutor() {
        return pse2indicadorDocumentoExecutor;
    }

    public void setPse2indicadorDocumentoExecutor(Long pse2indicadorDocumentoExecutor) {
        this.pse2indicadorDocumentoExecutor = pse2indicadorDocumentoExecutor;
    }

    public String getPse2identificadorExecutor() {
        return pse2identificadorExecutor;
    }

    public void setPse2identificadorExecutor(String pse2identificadorExecutor) {
        this.pse2identificadorExecutor = pse2identificadorExecutor;
    }

    public Date getPse2competencia() {
        return pse2competencia;
    }

    public void setPse2competencia(Date pse2competencia) {
        this.pse2competencia = pse2competencia;
    }

    public String getPse2servico() {
        return pse2servico;
    }

    public void setPse2servico(String pse2servico) {
        this.pse2servico = pse2servico;
    }

    public String getPse2classificacao() {
        return pse2classificacao;
    }

    public void setPse2classificacao(String pse2classificacao) {
        this.pse2classificacao = pse2classificacao;
    }

    public Long getPse3indicadorDocProfissional() {
        return pse3indicadorDocProfissional;
    }

    public void setPse3indicadorDocProfissional(Long pse3indicadorDocProfissional) {
        this.pse3indicadorDocProfissional = pse3indicadorDocProfissional;
    }

    public String getPse3identificacaoProfissional() {
        return pse3identificacaoProfissional;
    }

    public void setPse3identificacaoProfissional(String pse3identificacaoProfissional) {
        this.pse3identificacaoProfissional = pse3identificacaoProfissional;
    }

    public String getPse3cboProfissional() {
        return pse3cboProfissional;
    }

    public void setPse3cboProfissional(String pse3cboProfissional) {
        this.pse3cboProfissional = pse3cboProfissional;
    }

    public Long getPse3identificadorEquipe() {
        return pse3identificadorEquipe;
    }

    public void setPse3identificadorEquipe(Long pse3identificadorEquipe) {
        this.pse3identificadorEquipe = pse3identificadorEquipe;
    }

    public Long getPse3identificadorPrestadorServico() {
        return pse3identificadorPrestadorServico;
    }

    public void setPse3identificadorPrestadorServico(Long pse3identificadorPrestadorServico) {
        this.pse3identificadorPrestadorServico = pse3identificadorPrestadorServico;
    }

    public String getPse3identificacaoPrestadorServico() {
        return pse3identificacaoPrestadorServico;
    }

    public void setPse3identificacaoPrestadorServico(String pse3identificacaoPrestadorServico) {
        this.pse3identificacaoPrestadorServico = pse3identificacaoPrestadorServico;
    }

    public Long getPse3indicadorDocumentoExecutor() {
        return pse3indicadorDocumentoExecutor;
    }

    public void setPse3indicadorDocumentoExecutor(Long pse3indicadorDocumentoExecutor) {
        this.pse3indicadorDocumentoExecutor = pse3indicadorDocumentoExecutor;
    }

    public String getPse3identificadorExecutor() {
        return pse3identificadorExecutor;
    }

    public void setPse3identificadorExecutor(String pse3identificadorExecutor) {
        this.pse3identificadorExecutor = pse3identificadorExecutor;
    }

    public Date getPse3competencia() {
        return pse3competencia;
    }

    public void setPse3competencia(Date pse3competencia) {
        this.pse3competencia = pse3competencia;
    }

    public String getPse3servico() {
        return pse3servico;
    }

    public void setPse3servico(String pse3servico) {
        this.pse3servico = pse3servico;
    }

    public String getPse3classificacao() {
        return pse3classificacao;
    }

    public void setPse3classificacao(String pse3classificacao) {
        this.pse3classificacao = pse3classificacao;
    }

    public Long getPse4indicadorDocProfissional() {
        return pse4indicadorDocProfissional;
    }

    public void setPse4indicadorDocProfissional(Long pse4indicadorDocProfissional) {
        this.pse4indicadorDocProfissional = pse4indicadorDocProfissional;
    }

    public String getPse4identificacaoProfissional() {
        return pse4identificacaoProfissional;
    }

    public void setPse4identificacaoProfissional(String pse4identificacaoProfissional) {
        this.pse4identificacaoProfissional = pse4identificacaoProfissional;
    }

    public String getPse4cboProfissional() {
        return pse4cboProfissional;
    }

    public void setPse4cboProfissional(String pse4cboProfissional) {
        this.pse4cboProfissional = pse4cboProfissional;
    }

    public Long getPse4identificadorEquipe() {
        return pse4identificadorEquipe;
    }

    public void setPse4identificadorEquipe(Long pse4identificadorEquipe) {
        this.pse4identificadorEquipe = pse4identificadorEquipe;
    }

    public Long getPse4identificadorPrestadorServico() {
        return pse4identificadorPrestadorServico;
    }

    public void setPse4identificadorPrestadorServico(Long pse4identificadorPrestadorServico) {
        this.pse4identificadorPrestadorServico = pse4identificadorPrestadorServico;
    }

    public String getPse4identificacaoPrestadorServico() {
        return pse4identificacaoPrestadorServico;
    }

    public void setPse4identificacaoPrestadorServico(String pse4identificacaoPrestadorServico) {
        this.pse4identificacaoPrestadorServico = pse4identificacaoPrestadorServico;
    }

    public Long getPse4indicadorDocumentoExecutor() {
        return pse4indicadorDocumentoExecutor;
    }

    public void setPse4indicadorDocumentoExecutor(Long pse4indicadorDocumentoExecutor) {
        this.pse4indicadorDocumentoExecutor = pse4indicadorDocumentoExecutor;
    }

    public String getPse4identificadorExecutor() {
        return pse4identificadorExecutor;
    }

    public void setPse4identificadorExecutor(String pse4identificadorExecutor) {
        this.pse4identificadorExecutor = pse4identificadorExecutor;
    }

    public Date getPse4competencia() {
        return pse4competencia;
    }

    public void setPse4competencia(Date pse4competencia) {
        this.pse4competencia = pse4competencia;
    }

    public String getPse4servico() {
        return pse4servico;
    }

    public void setPse4servico(String pse4servico) {
        this.pse4servico = pse4servico;
    }

    public String getPse4classificacao() {
        return pse4classificacao;
    }

    public void setPse4classificacao(String pse4classificacao) {
        this.pse4classificacao = pse4classificacao;
    }

    public Long getPse5indicadorDocProfissional() {
        return pse5indicadorDocProfissional;
    }

    public void setPse5indicadorDocProfissional(Long pse5indicadorDocProfissional) {
        this.pse5indicadorDocProfissional = pse5indicadorDocProfissional;
    }

    public String getPse5identificacaoProfissional() {
        return pse5identificacaoProfissional;
    }

    public void setPse5identificacaoProfissional(String pse5identificacaoProfissional) {
        this.pse5identificacaoProfissional = pse5identificacaoProfissional;
    }

    public String getPse5cboProfissional() {
        return pse5cboProfissional;
    }

    public void setPse5cboProfissional(String pse5cboProfissional) {
        this.pse5cboProfissional = pse5cboProfissional;
    }

    public Long getPse5identificadorEquipe() {
        return pse5identificadorEquipe;
    }

    public void setPse5identificadorEquipe(Long pse5identificadorEquipe) {
        this.pse5identificadorEquipe = pse5identificadorEquipe;
    }

    public Long getPse5identificadorPrestadorServico() {
        return pse5identificadorPrestadorServico;
    }

    public void setPse5identificadorPrestadorServico(Long pse5identificadorPrestadorServico) {
        this.pse5identificadorPrestadorServico = pse5identificadorPrestadorServico;
    }

    public String getPse5identificacaoPrestadorServico() {
        return pse5identificacaoPrestadorServico;
    }

    public void setPse5identificacaoPrestadorServico(String pse5identificacaoPrestadorServico) {
        this.pse5identificacaoPrestadorServico = pse5identificacaoPrestadorServico;
    }

    public Long getPse5indicadorDocumentoExecutor() {
        return pse5indicadorDocumentoExecutor;
    }

    public void setPse5indicadorDocumentoExecutor(Long pse5indicadorDocumentoExecutor) {
        this.pse5indicadorDocumentoExecutor = pse5indicadorDocumentoExecutor;
    }

    public String getPse5identificadorExecutor() {
        return pse5identificadorExecutor;
    }

    public void setPse5identificadorExecutor(String pse5identificadorExecutor) {
        this.pse5identificadorExecutor = pse5identificadorExecutor;
    }

    public Date getPse5competencia() {
        return pse5competencia;
    }

    public void setPse5competencia(Date pse5competencia) {
        this.pse5competencia = pse5competencia;
    }

    public String getPse5servico() {
        return pse5servico;
    }

    public void setPse5servico(String pse5servico) {
        this.pse5servico = pse5servico;
    }

    public String getPse5classificacao() {
        return pse5classificacao;
    }

    public void setPse5classificacao(String pse5classificacao) {
        this.pse5classificacao = pse5classificacao;
    }

    public Long getPse6indicadorDocProfissional() {
        return pse6indicadorDocProfissional;
    }

    public void setPse6indicadorDocProfissional(Long pse6indicadorDocProfissional) {
        this.pse6indicadorDocProfissional = pse6indicadorDocProfissional;
    }

    public String getPse6identificacaoProfissional() {
        return pse6identificacaoProfissional;
    }

    public void setPse6identificacaoProfissional(String pse6identificacaoProfissional) {
        this.pse6identificacaoProfissional = pse6identificacaoProfissional;
    }

    public String getPse6cboProfissional() {
        return pse6cboProfissional;
    }

    public void setPse6cboProfissional(String pse6cboProfissional) {
        this.pse6cboProfissional = pse6cboProfissional;
    }

    public Long getPse6identificadorEquipe() {
        return pse6identificadorEquipe;
    }

    public void setPse6identificadorEquipe(Long pse6identificadorEquipe) {
        this.pse6identificadorEquipe = pse6identificadorEquipe;
    }

    public Long getPse6identificadorPrestadorServico() {
        return pse6identificadorPrestadorServico;
    }

    public void setPse6identificadorPrestadorServico(Long pse6identificadorPrestadorServico) {
        this.pse6identificadorPrestadorServico = pse6identificadorPrestadorServico;
    }

    public String getPse6identificacaoPrestadorServico() {
        return pse6identificacaoPrestadorServico;
    }

    public void setPse6identificacaoPrestadorServico(String pse6identificacaoPrestadorServico) {
        this.pse6identificacaoPrestadorServico = pse6identificacaoPrestadorServico;
    }

    public Long getPse6indicadorDocumentoExecutor() {
        return pse6indicadorDocumentoExecutor;
    }

    public void setPse6indicadorDocumentoExecutor(Long pse6indicadorDocumentoExecutor) {
        this.pse6indicadorDocumentoExecutor = pse6indicadorDocumentoExecutor;
    }

    public String getPse6identificadorExecutor() {
        return pse6identificadorExecutor;
    }

    public void setPse6identificadorExecutor(String pse6identificadorExecutor) {
        this.pse6identificadorExecutor = pse6identificadorExecutor;
    }

    public Date getPse6competencia() {
        return pse6competencia;
    }

    public void setPse6competencia(Date pse6competencia) {
        this.pse6competencia = pse6competencia;
    }

    public String getPse6servico() {
        return pse6servico;
    }

    public void setPse6servico(String pse6servico) {
        this.pse6servico = pse6servico;
    }

    public String getPse6classificacao() {
        return pse6classificacao;
    }

    public void setPse6classificacao(String pse6classificacao) {
        this.pse6classificacao = pse6classificacao;
    }

    public Long getPse7indicadorDocProfissional() {
        return pse7indicadorDocProfissional;
    }

    public void setPse7indicadorDocProfissional(Long pse7indicadorDocProfissional) {
        this.pse7indicadorDocProfissional = pse7indicadorDocProfissional;
    }

    public String getPse7identificacaoProfissional() {
        return pse7identificacaoProfissional;
    }

    public void setPse7identificacaoProfissional(String pse7identificacaoProfissional) {
        this.pse7identificacaoProfissional = pse7identificacaoProfissional;
    }

    public String getPse7cboProfissional() {
        return pse7cboProfissional;
    }

    public void setPse7cboProfissional(String pse7cboProfissional) {
        this.pse7cboProfissional = pse7cboProfissional;
    }

    public Long getPse7identificadorEquipe() {
        return pse7identificadorEquipe;
    }

    public void setPse7identificadorEquipe(Long pse7identificadorEquipe) {
        this.pse7identificadorEquipe = pse7identificadorEquipe;
    }

    public Long getPse7identificadorPrestadorServico() {
        return pse7identificadorPrestadorServico;
    }

    public void setPse7identificadorPrestadorServico(Long pse7identificadorPrestadorServico) {
        this.pse7identificadorPrestadorServico = pse7identificadorPrestadorServico;
    }

    public String getPse7identificacaoPrestadorServico() {
        return pse7identificacaoPrestadorServico;
    }

    public void setPse7identificacaoPrestadorServico(String pse7identificacaoPrestadorServico) {
        this.pse7identificacaoPrestadorServico = pse7identificacaoPrestadorServico;
    }

    public Long getPse7indicadorDocumentoExecutor() {
        return pse7indicadorDocumentoExecutor;
    }

    public void setPse7indicadorDocumentoExecutor(Long pse7indicadorDocumentoExecutor) {
        this.pse7indicadorDocumentoExecutor = pse7indicadorDocumentoExecutor;
    }

    public String getPse7identificadorExecutor() {
        return pse7identificadorExecutor;
    }

    public void setPse7identificadorExecutor(String pse7identificadorExecutor) {
        this.pse7identificadorExecutor = pse7identificadorExecutor;
    }

    public Date getPse7competencia() {
        return pse7competencia;
    }

    public void setPse7competencia(Date pse7competencia) {
        this.pse7competencia = pse7competencia;
    }

    public String getPse7servico() {
        return pse7servico;
    }

    public void setPse7servico(String pse7servico) {
        this.pse7servico = pse7servico;
    }

    public String getPse7classificacao() {
        return pse7classificacao;
    }

    public void setPse7classificacao(String pse7classificacao) {
        this.pse7classificacao = pse7classificacao;
    }

    public Long getPse8indicadorDocProfissional() {
        return pse8indicadorDocProfissional;
    }

    public void setPse8indicadorDocProfissional(Long pse8indicadorDocProfissional) {
        this.pse8indicadorDocProfissional = pse8indicadorDocProfissional;
    }

    public String getPse8identificacaoProfissional() {
        return pse8identificacaoProfissional;
    }

    public void setPse8identificacaoProfissional(String pse8identificacaoProfissional) {
        this.pse8identificacaoProfissional = pse8identificacaoProfissional;
    }

    public String getPse8cboProfissional() {
        return pse8cboProfissional;
    }

    public void setPse8cboProfissional(String pse8cboProfissional) {
        this.pse8cboProfissional = pse8cboProfissional;
    }

    public Long getPse8identificadorEquipe() {
        return pse8identificadorEquipe;
    }

    public void setPse8identificadorEquipe(Long pse8identificadorEquipe) {
        this.pse8identificadorEquipe = pse8identificadorEquipe;
    }

    public Long getPse8identificadorPrestadorServico() {
        return pse8identificadorPrestadorServico;
    }

    public void setPse8identificadorPrestadorServico(Long pse8identificadorPrestadorServico) {
        this.pse8identificadorPrestadorServico = pse8identificadorPrestadorServico;
    }

    public String getPse8identificacaoPrestadorServico() {
        return pse8identificacaoPrestadorServico;
    }

    public void setPse8identificacaoPrestadorServico(String pse8identificacaoPrestadorServico) {
        this.pse8identificacaoPrestadorServico = pse8identificacaoPrestadorServico;
    }

    public Long getPse8indicadorDocumentoExecutor() {
        return pse8indicadorDocumentoExecutor;
    }

    public void setPse8indicadorDocumentoExecutor(Long pse8indicadorDocumentoExecutor) {
        this.pse8indicadorDocumentoExecutor = pse8indicadorDocumentoExecutor;
    }

    public String getPse8identificadorExecutor() {
        return pse8identificadorExecutor;
    }

    public void setPse8identificadorExecutor(String pse8identificadorExecutor) {
        this.pse8identificadorExecutor = pse8identificadorExecutor;
    }

    public Date getPse8competencia() {
        return pse8competencia;
    }

    public void setPse8competencia(Date pse8competencia) {
        this.pse8competencia = pse8competencia;
    }

    public String getPse8servico() {
        return pse8servico;
    }

    public void setPse8servico(String pse8servico) {
        this.pse8servico = pse8servico;
    }

    public String getPse8classificacao() {
        return pse8classificacao;
    }

    public void setPse8classificacao(String pse8classificacao) {
        this.pse8classificacao = pse8classificacao;
    }

    public Long getPse9indicadorDocProfissional() {
        return pse9indicadorDocProfissional;
    }

    public void setPse9indicadorDocProfissional(Long pse9indicadorDocProfissional) {
        this.pse9indicadorDocProfissional = pse9indicadorDocProfissional;
    }

    public String getPse9identificacaoProfissional() {
        return pse9identificacaoProfissional;
    }

    public void setPse9identificacaoProfissional(String pse9identificacaoProfissional) {
        this.pse9identificacaoProfissional = pse9identificacaoProfissional;
    }

    public String getPse9cboProfissional() {
        return pse9cboProfissional;
    }

    public void setPse9cboProfissional(String pse9cboProfissional) {
        this.pse9cboProfissional = pse9cboProfissional;
    }

    public Long getPse9identificadorEquipe() {
        return pse9identificadorEquipe;
    }

    public void setPse9identificadorEquipe(Long pse9identificadorEquipe) {
        this.pse9identificadorEquipe = pse9identificadorEquipe;
    }

    public Long getPse9identificadorPrestadorServico() {
        return pse9identificadorPrestadorServico;
    }

    public void setPse9identificadorPrestadorServico(Long pse9identificadorPrestadorServico) {
        this.pse9identificadorPrestadorServico = pse9identificadorPrestadorServico;
    }

    public String getPse9identificacaoPrestadorServico() {
        return pse9identificacaoPrestadorServico;
    }

    public void setPse9identificacaoPrestadorServico(String pse9identificacaoPrestadorServico) {
        this.pse9identificacaoPrestadorServico = pse9identificacaoPrestadorServico;
    }

    public Long getPse9indicadorDocumentoExecutor() {
        return pse9indicadorDocumentoExecutor;
    }

    public void setPse9indicadorDocumentoExecutor(Long pse9indicadorDocumentoExecutor) {
        this.pse9indicadorDocumentoExecutor = pse9indicadorDocumentoExecutor;
    }

    public String getPse9identificadorExecutor() {
        return pse9identificadorExecutor;
    }

    public void setPse9identificadorExecutor(String pse9identificadorExecutor) {
        this.pse9identificadorExecutor = pse9identificadorExecutor;
    }

    public Date getPse9competencia() {
        return pse9competencia;
    }

    public void setPse9competencia(Date pse9competencia) {
        this.pse9competencia = pse9competencia;
    }

    public String getPse9servico() {
        return pse9servico;
    }

    public void setPse9servico(String pse9servico) {
        this.pse9servico = pse9servico;
    }

    public String getPse9classificacao() {
        return pse9classificacao;
    }

    public void setPse9classificacao(String pse9classificacao) {
        this.pse9classificacao = pse9classificacao;
    }

    public Long getPse1codigoProcedimento() {
        return pse1codigoProcedimento;
    }

    public void setPse1codigoProcedimento(Long pse1codigoProcedimento) {
        this.pse1codigoProcedimento = pse1codigoProcedimento;
    }

    public Long getPse1quantidadeProcedimento() {
        return pse1quantidadeProcedimento;
    }

    public void setPse1quantidadeProcedimento(Long pse1quantidadeProcedimento) {
        this.pse1quantidadeProcedimento = pse1quantidadeProcedimento;
    }

    public Long getPse2codigoProcedimento() {
        return pse2codigoProcedimento;
    }

    public void setPse2codigoProcedimento(Long pse2codigoProcedimento) {
        this.pse2codigoProcedimento = pse2codigoProcedimento;
    }

    public Long getPse2quantidadeProcedimento() {
        return pse2quantidadeProcedimento;
    }

    public void setPse2quantidadeProcedimento(Long pse2quantidadeProcedimento) {
        this.pse2quantidadeProcedimento = pse2quantidadeProcedimento;
    }

    public Long getPse3codigoProcedimento() {
        return pse3codigoProcedimento;
    }

    public void setPse3codigoProcedimento(Long pse3codigoProcedimento) {
        this.pse3codigoProcedimento = pse3codigoProcedimento;
    }

    public Long getPse3quantidadeProcedimento() {
        return pse3quantidadeProcedimento;
    }

    public void setPse3quantidadeProcedimento(Long pse3quantidadeProcedimento) {
        this.pse3quantidadeProcedimento = pse3quantidadeProcedimento;
    }

    public Long getPse4codigoProcedimento() {
        return pse4codigoProcedimento;
    }

    public void setPse4codigoProcedimento(Long pse4codigoProcedimento) {
        this.pse4codigoProcedimento = pse4codigoProcedimento;
    }

    public Long getPse4quantidadeProcedimento() {
        return pse4quantidadeProcedimento;
    }

    public void setPse4quantidadeProcedimento(Long pse4quantidadeProcedimento) {
        this.pse4quantidadeProcedimento = pse4quantidadeProcedimento;
    }

    public Long getPse5codigoProcedimento() {
        return pse5codigoProcedimento;
    }

    public void setPse5codigoProcedimento(Long pse5codigoProcedimento) {
        this.pse5codigoProcedimento = pse5codigoProcedimento;
    }

    public Long getPse5quantidadeProcedimento() {
        return pse5quantidadeProcedimento;
    }

    public void setPse5quantidadeProcedimento(Long pse5quantidadeProcedimento) {
        this.pse5quantidadeProcedimento = pse5quantidadeProcedimento;
    }

    public Long getPse6codigoProcedimento() {
        return pse6codigoProcedimento;
    }

    public void setPse6codigoProcedimento(Long pse6codigoProcedimento) {
        this.pse6codigoProcedimento = pse6codigoProcedimento;
    }

    public Long getPse6quantidadeProcedimento() {
        return pse6quantidadeProcedimento;
    }

    public void setPse6quantidadeProcedimento(Long pse6quantidadeProcedimento) {
        this.pse6quantidadeProcedimento = pse6quantidadeProcedimento;
    }

    public Long getPse7codigoProcedimento() {
        return pse7codigoProcedimento;
    }

    public void setPse7codigoProcedimento(Long pse7codigoProcedimento) {
        this.pse7codigoProcedimento = pse7codigoProcedimento;
    }

    public Long getPse7quantidadeProcedimento() {
        return pse7quantidadeProcedimento;
    }

    public void setPse7quantidadeProcedimento(Long pse7quantidadeProcedimento) {
        this.pse7quantidadeProcedimento = pse7quantidadeProcedimento;
    }

    public Long getPse8codigoProcedimento() {
        return pse8codigoProcedimento;
    }

    public void setPse8codigoProcedimento(Long pse8codigoProcedimento) {
        this.pse8codigoProcedimento = pse8codigoProcedimento;
    }

    public Long getPse8quantidadeProcedimento() {
        return pse8quantidadeProcedimento;
    }

    public void setPse8quantidadeProcedimento(Long pse8quantidadeProcedimento) {
        this.pse8quantidadeProcedimento = pse8quantidadeProcedimento;
    }

    public Long getPse9codigoProcedimento() {
        return pse9codigoProcedimento;
    }

    public void setPse9codigoProcedimento(Long pse9codigoProcedimento) {
        this.pse9codigoProcedimento = pse9codigoProcedimento;
    }

    public Long getPse9quantidadeProcedimento() {
        return pse9quantidadeProcedimento;
    }

    public void setPse9quantidadeProcedimento(Long pse9quantidadeProcedimento) {
        this.pse9quantidadeProcedimento = pse9quantidadeProcedimento;
    }

    public Long getIdentificacaoAih() {
        return identificacaoAih;
    }

    public void setIdentificacaoAih(Long identificacaoAih) {
        this.identificacaoAih = identificacaoAih;
    }

    
}
