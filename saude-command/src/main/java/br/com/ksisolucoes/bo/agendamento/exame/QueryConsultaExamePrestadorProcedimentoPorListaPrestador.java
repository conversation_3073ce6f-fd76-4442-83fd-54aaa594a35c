package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.base.BaseEmpresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimento;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestador.PROP_PRESTADOR;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestadorProcedimento.PROP_EXAME_PRESTADOR;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameProcedimento.PROP_PROCEDIMENTO;

public class QueryConsultaExamePrestadorProcedimentoPorListaPrestador extends CommandQuery {

    private static final long serialVersionUID = -4750001307418343754L;

    private final List<Long> idsEmpresas;
    private List<ExamePrestadorProcedimento> list;

    public QueryConsultaExamePrestadorProcedimentoPorListaPrestador(List<Long> idsEmpresas) {
        this.idsEmpresas = idsEmpresas;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ExamePrestadorProcedimento.class.getName());
        hql.addToSelect("epp.codigo", BaseExamePrestadorProcedimento.PROP_CODIGO);
        hql.addToSelect("epp.valorProcedimento", BaseExamePrestadorProcedimento.PROP_VALOR_PROCEDIMENTO);
        hql.addToSelect("epp.valorComplementar", BaseExamePrestadorProcedimento.PROP_VALOR_COMPLEMENTAR);
        hql.addToSelect("examePrestador.codigo", this.propBinding(PROP_EXAME_PRESTADOR, BaseExamePrestador.PROP_CODIGO));
        hql.addToSelect("examePrestador.prestador.codigo", this.propBinding(PROP_EXAME_PRESTADOR, PROP_PRESTADOR, BaseEmpresa.PROP_CODIGO));
        hql.addToSelect("exameProcedimento.codigo", this.propBinding(PROP_EXAME_PROCEDIMENTO, BaseExameProcedimento.PROP_CODIGO));
        hql.addToSelect("exameProcedimento.procedimento.codigo", this.propBinding(PROP_EXAME_PROCEDIMENTO, PROP_PROCEDIMENTO, BaseProcedimento.PROP_CODIGO));
        hql.addToFrom("ExamePrestadorProcedimento epp " +
                      "left join epp.examePrestador examePrestador " +
                      "left join examePrestador.prestador prestador" +
                      "left join epp.exameProcedimento exameProcedimento " +
                      "left join exameProcedimento.procedimento procedimento ");
        hql.addToWhereWhithAnd("epp.examePrestador.prestador.codigo in ", idsEmpresas);
    }

    @Override
    public List<ExamePrestadorProcedimento> getResult() {
        return list;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public String propBinding(String... args) {
        return VOUtils.montarPath(args);
    }
}
