package br.com.ksisolucoes.bo.vacina.tipovacina;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.EloTipoVacinaDose;
import br.com.ksisolucoes.vo.vacina.ItemPedidoVacina;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.TipoVacinaInsumo;
import org.hibernate.criterion.Restrictions;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DeletarTipoVacina extends AbstractCommandTransaction {

    private TipoVacina tipoVacina;
    private List<EloTipoVacinaDose> lstEloTipoVacinaDoses;
    private List<ItemPedidoVacina> lstItemPedidoVacinas;

    public DeletarTipoVacina(TipoVacina tipoVacina) {
        this.tipoVacina = tipoVacina;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        TipoVacinaInsumo proxy = on(TipoVacinaInsumo.class);

        lstEloTipoVacinaDoses = LoadManager.getInstance(EloTipoVacinaDose.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EloTipoVacinaDose.PROP_TIPO_VACINA, tipoVacina))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(lstEloTipoVacinaDoses)) {
            for (EloTipoVacinaDose eloTipoVacinaDose : lstEloTipoVacinaDoses) {
                BOFactory.delete(eloTipoVacinaDose);
            }
        }

        List<TipoVacinaInsumo> insumoList = getSession().createCriteria(TipoVacinaInsumo.class)
                .add(Restrictions.eq(path(proxy.getTipoVacina()), tipoVacina))
                .list();

        if (CollectionUtils.isNotNullEmpty(insumoList)) {
            for (TipoVacinaInsumo tvi : insumoList) {
                BOFactory.delete(tvi);
            }
        }

        lstItemPedidoVacinas = LoadManager.getInstance(ItemPedidoVacina.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ItemPedidoVacina.PROP_TIPO_VACINA, tipoVacina))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(lstItemPedidoVacinas)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_exite_vinculo_tipo_vacina_pedido_vacina"));
        }

        BOFactory.delete(tipoVacina);
    }

}
