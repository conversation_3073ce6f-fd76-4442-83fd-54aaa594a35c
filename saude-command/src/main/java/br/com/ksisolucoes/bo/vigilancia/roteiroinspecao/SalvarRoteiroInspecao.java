package br.com.ksisolucoes.bo.vigilancia.roteiroinspecao;

import br.com.celk.vigilancia.fiscalnarua.*;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.EloRoteiroAtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroItemInspecao;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarRoteiroInspecao extends AbstractCommandTransaction {

    private RoteiroInspecao roteiroInspecao;
    private List<RoteiroItemInspecao> lstRoteiroItemInspecao;
    private List<EloRoteiroAtividadeEstabelecimento> lstAtividadesEstabelecimento;

    public SalvarRoteiroInspecao(RoteiroInspecao roteiroInspecao, List<RoteiroItemInspecao> lstRoteiroItemInspecao, List<EloRoteiroAtividadeEstabelecimento> lstAtividadesEstabelecimento) {
        this.roteiroInspecao = roteiroInspecao;
        this.lstRoteiroItemInspecao = lstRoteiroItemInspecao;
        this.lstAtividadesEstabelecimento = lstAtividadesEstabelecimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.save(roteiroInspecao);
        VOUtils.persistirListaVosModificados(RoteiroItemInspecao.class, lstRoteiroItemInspecao, new QueryCustom.QueryCustomParameter(RoteiroItemInspecao.PROP_ROTEIRO_INSPECAO, roteiroInspecao));

        BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(
                new RoteiroInspecaoDTO(roteiroInspecao)), TipoMensagemSqsFiscalRua.ROTEIRO_INSPECAO);

        if (lstAtividadesEstabelecimento != null) {
            VOUtils.persistirListaVosModificados(EloRoteiroAtividadeEstabelecimento.class, lstAtividadesEstabelecimento, new QueryCustom.QueryCustomParameter(EloRoteiroAtividadeEstabelecimento.PROP_ROTEIRO_INSPECAO, roteiroInspecao));

            for (EloRoteiroAtividadeEstabelecimento elo : lstAtividadesEstabelecimento) {
                BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(
                    new RoteiroEloTipoAtividadeDTO(elo)), TipoMensagemSqsFiscalRua.ROTEIRO_ELO_TIPO_ATIVIDADE);
            }
        }

        for(RoteiroItemInspecao x: lstRoteiroItemInspecao){
            x.setRoteiroInspecao(roteiroInspecao);

            BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(
                    new RoteiroInspecaoEloItemDTO(x)), TipoMensagemSqsFiscalRua.ROTEIRO_ELO_ITEM_ROTEIRO);
        }
    }

    public RoteiroInspecao getRoteiroInspecao() {
        return roteiroInspecao;
    }
}
