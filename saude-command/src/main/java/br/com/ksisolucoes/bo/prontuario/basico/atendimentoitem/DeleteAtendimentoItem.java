/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.atendimentoitem;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeleteAtendimentoItem extends DeleteVO<AtendimentoItem> {

    public DeleteAtendimentoItem(AtendimentoItem vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<ItemContaPaciente> itemContaPacienteList = getSession().createCriteria(ItemContaPaciente.class)
                .add(Restrictions.eq(ItemContaPaciente.PROP_ATENDIMENTO_ITEM, this.vo))
                .list();

        for (ItemContaPaciente itemContaPaciente : itemContaPacienteList) {
            BOFactory.delete(itemContaPaciente);
        }
    }
}
