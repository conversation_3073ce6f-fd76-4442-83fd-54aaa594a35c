package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentovistoriapbaconformidadetecnicaresposta;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnicaResposta;

/**
 *
 * <AUTHOR>
 */
public class SaveRequerimentoVistoriaPBAConformidadeTecnicaResposta extends SaveVO<RequerimentoVistoriaPBAConformidadeTecnicaResposta> {

    public SaveRequerimentoVistoriaPBAConformidadeTecnicaResposta(RequerimentoVistoriaPBAConformidadeTecnicaResposta vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataResposta() == null) {
            this.vo.setDataResposta(DataUtil.getDataAtual());
        }
        this.vo.setDataUsuario(DataUtil.getDataAtual());
    }
}