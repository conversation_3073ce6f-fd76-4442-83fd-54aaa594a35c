package br.com.ksisolucoes.bo.entradas.estoque.grupoestoque;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.dto.QueryMovimentoGrupoEstoqueItemDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.requisicaopadraoexame.query.RequisicaoPadraoExameDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import org.hamcrest.Matchers;
import org.hibernate.Session;

import java.util.*;

import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.sort;
import static org.apache.commons.collections.ComparatorUtils.chainedComparator;
import static org.apache.commons.collections.ComparatorUtils.reversedComparator;

public class QueryMovimentoGrupoEstoqueItem extends CommandQuery<QueryMovimentoGrupoEstoqueItem> {

    private QueryMovimentoGrupoEstoqueItemDTOParam param;

    private List<MovimentoGrupoEstoqueItemDTO> movimentoGrupoEstoqueItens;

    public QueryMovimentoGrupoEstoqueItem(QueryMovimentoGrupoEstoqueItemDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("prod.codigo", "produto.codigo");
        hql.addToSelect("prod.referencia", "produto.referencia");
        hql.addToSelect("prod.descricao", "produto.descricao");
        hql.addToSelect("emp.codigo", "empresa.codigo");
        hql.addToSelect("emp.referencia", "empresa.referencia");
        hql.addToSelect("emp.descricao", "empresa.descricao");
        hql.addToSelect("emp.sigla", "empresa.sigla");
        hql.addToSelect("dep.codigo", "deposito.codigo");
        hql.addToSelect("dep.descricao", "deposito.descricao");
        hql.addToSelect("upper(ge.id.grupo)", "grupoEstoque");
        hql.addToSelect("ge.dataUltimaEntrada", "ultimaDataEntrada");
        hql.addToSelect("ge.estoqueFisico", "estoqueFisico");
        hql.addToSelect("ge.estoqueReservado", "estoqueReservado");
        hql.addToSelect("ge.estoqueEncomendado", "estoqueEncomendado");
        hql.addToSelect("ge.dataValidade", "dataValidade");
        hql.addToSelect("ge.id.localizacaoEstrutura.codigo", "localizacaoEstrutura.codigo");
        hql.addToSelect("ge.id.localizacaoEstrutura.mascara", "localizacaoEstrutura.mascara");
        hql.addToSelect("ge.id.localizacaoEstrutura.descricaoEstrutura", "localizacaoEstrutura.descricaoEstrutura");
        hql.addToSelect("ge.id.localizacaoEstrutura.version", "localizacaoEstrutura.version");
        hql.addToSelect("ge.id.localizacaoEstrutura.localizacao.codigo", "localizacaoEstrutura.localizacao.codigo");
        hql.addToSelect("ge.id.localizacaoEstrutura.localizacao.descricao", "localizacaoEstrutura.localizacao.descricao");
        hql.addToSelect("f.codigo", "fabricante.codigo");
        hql.addToSelect("f.descricao", "fabricante.descricao");

        hql.addToFrom(GrupoEstoque.class.getName(), "ge "
                + " left join ge.id.estoqueEmpresa ee "
                + " left join ee.id.produto prod "
                + " left join ee.id.empresa emp "
                + " left join ge.fabricante f ");

        hql.addToFrom(Deposito.class.getName(), "dep");

        hql.setTypeSelect(MovimentoGrupoEstoqueItemDTO.class.getName());

        hql.addToWhereWhithAnd("ge.id.codigoDeposito = dep.codigo");

        hql.addToWhereWhithAnd("ge.id.grupo = ", this.param.getGrupoEstoque());

        if(this.param.getLocalizacaoEstrutura() != null && this.param.getLocalizacaoEstrutura().getCodigo() != null){
            hql.addToWhereWhithAnd("ge.id.localizacaoEstrutura.codigo = ", this.param.getLocalizacaoEstrutura().getCodigo());
        }

        hql.addToWhereWhithAnd("ge.estoqueFisico > 0");

        hql.addToWhereWhithAnd("emp.codigo = ", this.param.getCodigoEmpresa());

        hql.addToWhereWhithAnd("dep.codigo = ", this.param.getCodigoDeposito());

        hql.addToWhereWhithAnd("prod = ", new Produto(this.param.getCodigoProduto()));

        if (param.isValidarDataValidade()) {
            hql.addToWhereWhithAnd("ge.dataValidade >= ", this.param.getDataValidade());
        }

        if (this.param.isApenasComDisponivel()) {
            hql.addToWhereWhithAnd("(ge.estoqueFisico + ge.estoqueEncomendado - ge.estoqueReservado)>0");
        }

        if (this.param.isContextoReserva()) {
//            hql.addToSelect( "re.quantidade","quantidadeOriginal" );
//            hql.addToSelect("re.quantidade", "estoqueReservado");
            hql.addToSelect("(coalesce(re.quantidade,0)-coalesce(re.quantidadeBaixada,0))", "quantidadeReservadaPersistida");

            hql.addToFrom(Reserva.class.getName(), "re ");

            hql.addToWhereWhithAnd("re.quantidade - coalesce(re.quantidadeBaixada) > 0");

            hql.addToWhereWhithAnd("ge = re.id.grupoEstoque");

            hql.addToWhereWhithAnd("re.documento = ", this.param.getCodigo());
            hql.addToWhereWhithAnd("re.item = ", this.param.getItem());
            hql.addToWhereWhithAnd("re.tipoReserva = ", this.param.getTipo());
            hql.addToWhereWhithAnd("re.status <> ", Reserva.STATUS_CANCELADO);
        }

        if (param.isOrdenarPorDataValidade()) {
            hql.addToOrder("ge.dataValidade");
        }
//        hql.addToOrder("ge.dataValidade");
//        hql.addToOrder("ge.estoqueFisico");

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.movimentoGrupoEstoqueItens = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.movimentoGrupoEstoqueItens)) {
            Date dataAtual = DataUtil.getDataAtual();
            List lotesVencidos = Lambda.select(this.movimentoGrupoEstoqueItens, Lambda.having(Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getDataValidade(), Matchers.lessThan(dataAtual)));
            List lotesAVencer = Lambda.select(this.movimentoGrupoEstoqueItens, Lambda.having(Lambda.on(MovimentoGrupoEstoqueItemDTO.class).getDataValidade(), Matchers.greaterThanOrEqualTo(dataAtual)));

            Comparator byDataValidade = new ArgumentComparator(on(MovimentoGrupoEstoqueItemDTO.class).getDataValidade());
            Comparator byEstoqueFisico = new ArgumentComparator(on(MovimentoGrupoEstoqueItemDTO.class).getEstoqueFisico());

            List<MovimentoGrupoEstoqueItemDTO> sorterList = new ArrayList();
            sorterList.addAll(sort(lotesAVencer, on(RequisicaoPadraoExameDTO.class), chainedComparator(byDataValidade, byEstoqueFisico)));
            sorterList.addAll(sort(lotesVencidos, on(RequisicaoPadraoExameDTO.class), chainedComparator(reversedComparator(byDataValidade), byEstoqueFisico)));

            this.movimentoGrupoEstoqueItens.clear();
            this.movimentoGrupoEstoqueItens.addAll(sorterList);
        }
    }

    public List<MovimentoGrupoEstoqueItemDTO> getMovimentoGrupoEstoqueItens() {
        return this.movimentoGrupoEstoqueItens;
    }

}
