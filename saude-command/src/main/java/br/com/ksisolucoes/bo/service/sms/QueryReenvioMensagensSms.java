package br.com.ksisolucoes.bo.service.sms;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryReenvioMensagensSms extends CommandQuery<QueryReenvioMensagensSms> {

    private Collection<SmsControleIntegracao> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(SmsControleIntegracao.class.getName());

        hql.addToSelect("smsci.codigo", true);
        
        hql.addToSelect("uSmsMsg.codigo", "smsMensagem.usuarioCadsus.codigo");
        hql.addToSelect("uSmsMsg.nome", "smsMensagem.usuarioCadsus.nome");
        hql.addToSelect("uSmsMsg.celular", "smsMensagem.usuarioCadsus.celular");
        
        hql.addToSelect("sc.codigo", "smsCadastro.codigo");
        hql.addToSelect("sc.mensagem", "smsCadastro.mensagem");
        hql.addToSelect("uSc.codigo", "smsCadastro.usuarioCadsus.codigo");
        hql.addToSelect("uSc.nome", "smsCadastro.usuarioCadsus.nome");
        hql.addToSelect("uSc.celular", "smsCadastro.usuarioCadsus.celular");

        hql.addToSelect("agah.codigo", "agendaGradeAtendimentoHorario.codigo");
        hql.addToSelect("agah.dataAgendamento", "agendaGradeAtendimentoHorario.dataAgendamento");
        hql.addToSelect("agah.flagEncaixe", "agendaGradeAtendimentoHorario.flagEncaixe");
        hql.addToSelect("agah.chaveValidacao", "agendaGradeAtendimentoHorario.chaveValidacao");

        hql.addToSelect("empresa.codigo", "agendaGradeAtendimentoHorario.localAgendamento.codigo");
        hql.addToSelect("empresa.descricao", "agendaGradeAtendimentoHorario.localAgendamento.descricao");

        hql.addToSelect("tipoProcedimento.codigo", "agendaGradeAtendimentoHorario.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.tipoProcedimento.descricao");

        hql.addToFrom("SmsControleIntegracao smsci "
                + " left join smsci.smsCadastro sc"
                + " left join smsci.agendaGradeAtendimentoHorario agah"
                + " left join agah.localAgendamento empresa"
                + " left join agah.tipoProcedimento tipoProcedimento"
                + " left join sc.usuarioCadsus uSc"
                + " left join smsci.smsMensagem smsMsg"
                + " left join smsMsg.usuarioCadsus uSmsMsg");

        hql.addToWhereWhithAnd("smsci.tipoMensagem = ", SmsControleIntegracao.TipoMensagem.MENSAGEM_SMS.value());
        hql.addToWhereWhithAnd("smsci.statusSms = ", SmsControleIntegracao.StatusSms.REENVIAR.value());
        hql.addToWhereWhithAnd("(uSc.celular is not null or uSmsMsg.celular is not null)");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return list;
    }
}