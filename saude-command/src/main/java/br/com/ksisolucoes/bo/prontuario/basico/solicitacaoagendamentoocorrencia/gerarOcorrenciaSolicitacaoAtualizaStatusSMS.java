package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamentoocorrencia;

import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;

/**
 *
 * <AUTHOR>
 */
public class gerarOcorrenciaSolicitacaoAtualizaStatusSMS extends AbstractCommandTransaction {

    private SolicitacaoAgendamentoOcorrencia.TipoOcorrencia tipoOcorrencia;
    private String descricao;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private SmsControleIntegracao smsControleIntegracao;
    private Long status;

    public gerarOcorrenciaSolicitacaoAtualizaStatusSMS(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia tipoOcorrencia, String descricao, SolicitacaoAgendamento solicitacaoAgendamento, SmsControleIntegracao smsControleIntegracao, Long status) {
        this.tipoOcorrencia = tipoOcorrencia;
        this.descricao = descricao;
        this.solicitacaoAgendamento = solicitacaoAgendamento;
        this.smsControleIntegracao = smsControleIntegracao;
        this.status = status;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(tipoOcorrencia, descricao, solicitacaoAgendamento);

        if (solicitacaoAgendamento != null) {
            SmsControleIntegracao smsci = (SmsControleIntegracao) getSession().get(SmsControleIntegracao.class, smsControleIntegracao.getCodigo());

            if (SmsControleIntegracao.StatusSms.ERRO.value().equals(status)) {
                smsci.setTipoErro(SmsControleIntegracao.TipoErro.ERRO_ENVIO.value());
                smsci.setDescricaoErro(smsci.getSmsMensagem().getMensagemOperadora());
            }

            smsci.setStatusSms(status);
            BOFactory.save(smsci);
        }

    }
}
