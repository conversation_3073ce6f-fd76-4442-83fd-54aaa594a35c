package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * Created by laudecir
 */
public class QueryPendenciasAutosIntimacaoAguardandoRecebimento extends CommandQuery<QueryPendenciasAutosIntimacaoAguardandoRecebimento> {

    private List<AutoIntimacao> result;

    public QueryPendenciasAutosIntimacaoAguardandoRecebimento() {
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AutoIntimacao.class.getName());

        hql.addToSelect("autoIntimacao.codigo", "codigo");
        hql.addToSelect("autoIntimacao.situacao", "situacao");

        StringBuilder sbFrom = new StringBuilder("AutoIntimacao autoIntimacao ");
        sbFrom.append(" LEFT JOIN autoIntimacao.estabelecimento estabelecimento");
        sbFrom.append(" LEFT JOIN autoIntimacao.vigilanciaPessoa vigilanciaPessoa");
        sbFrom.append(" LEFT JOIN autoIntimacao.registroInspecao registroInspecao");
        sbFrom.append(" LEFT JOIN registroInspecao.requerimentoVigilancia requerimentoVigilancia");
        hql.addToFrom(sbFrom.toString());

        hql.addToWhereWhithAnd("autoIntimacao.situacao = ", AutoIntimacao.Status.AGUARDANDO_RECEBIMENTO.value());

        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null) {
            StringBuilder existsProfissional = new StringBuilder("exists(select 1");
            existsProfissional.append("                      from AutoIntimacaoFiscal fiscal");
            existsProfissional.append("                     where fiscal.autoIntimacao.codigo = autoIntimacao.codigo");
            existsProfissional.append("                       and fiscal.profissional.codigo = :codigoProfissional)");
            hql.addToWhereWhithAnd(existsProfissional.toString());
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {

        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null) {
            query.setParameter("codigoProfissional", profissional.getCodigo());
        }

    }

    @Override
    public List<AutoIntimacao> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}