package br.com.ksisolucoes.bo.geral.estruturaequipamentorevisao;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;

public class FinalizarEstruturaEquipamentoRevisao extends AbstractCommandTransaction {

    private Produto produto;
    private boolean aprovar = false;
    private boolean substituicao;

    public FinalizarEstruturaEquipamentoRevisao(Produto produto) {
        this.produto = produto;
    }

    public FinalizarEstruturaEquipamentoRevisao(Produto produto, boolean aprovar) {
        this.produto = produto;
        this.aprovar = aprovar;
    }

    public FinalizarEstruturaEquipamentoRevisao(Produto produto, boolean aprovar, boolean substituicao) {
        this.produto = produto;
        this.aprovar = aprovar;
        this.substituicao = substituicao;
    }

    @Override()
    public void execute() throws DAOException, ValidacaoException {
        /*
         * VALIDACAO
         * ---------
         * Valida se estrutura ja no esta em revisao.
         *---------------------------------------------------------------------*/
        if (!EstruturaEquipamento.STATUS_REVISAO.equals(this.produto.getStatusEstrutura())) {
            throw new ValidacaoException(Bundle.getStringBO("2194"));
        }

        /**
         * Alterando status do produtos
         */


        Produto prod = (Produto) getSession().get(Produto.class, this.produto.getCodigo());
        prod.setStatusEstrutura( Produto.STATUS_ESTRUTURA_FINALIZADO);
        prod.setUsuarioEstrutura((Usuario) this.sessao.getUsuario());
//        Map map = new HashMap();
//
//        map.put(Produto.PROP_STATUS_ESTRUTURA, Produto.STATUS_ESTRUTURA_FINALIZADO);
//        map.put(Produto.PROP_USUARIO_ESTRUTURA, this.sessao.getUsuario());
//
//        BOFactory.getBO(ProdutoFacade.class).update(map, this.produto.getCodigo());

        /**
         * REGRA
         * -----
         * Verificar pelo parametro se  para aprovar a estrutura aps finalizar
         */
//        if (RepositoryComponentDefault.SIM.equals(CargaBasicoPadrao.getInstance().getParametroPadrao().getFlagEstruturaFinalizarAprovar()) ||
//                this.aprovar) {

            new AprovarEstruturaRevisao(this.produto, this.substituicao).start();
//        }

    }
}