package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameApac;

/**
 *
 * <AUTHOR>
 */
public class CancelarExameApac extends AbstractCommandTransaction<CancelarExameApac> {

    private Atendimento atendimento;
    private ExameApac exameApac;
    private boolean cancelarExame;

    public CancelarExameApac(Atendimento atendimento, ExameApac exameApac, boolean cancelarExame) {
        this.atendimento = atendimento;
        this.exameApac = exameApac;
        this.cancelarExame = cancelarExame;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        exameApac = HibernateUtil.rechargeVO(ExameApac.class, exameApac.getCodigo(), exameApac.getVersion());
        
        exameApac.setStatus(ExameApac.Status.CANCELADO.value());
        BOFactory.save(exameApac);
        
        if(cancelarExame){
            BOFactory.getBO(ExameFacade.class).cancelarExame(exameApac.getExame(), Bundle.getStringApplication("msg_cancelamento_apac_atendimento"), true);
        }
        
        if (atendimento != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.LAUDO_APAC.value(), atendimento, exameApac.getCodigo());
        }
    }
}
