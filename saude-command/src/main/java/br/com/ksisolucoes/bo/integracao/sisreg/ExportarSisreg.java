package br.com.ksisolucoes.bo.integracao.sisreg;

import br.com.celk.util.service.rest.WebserviceResponse;
import br.com.celk.util.service.rest.WebserviceUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.io.FileUtils;
import org.jfree.util.Log;
import org.json.CDL;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ExportarSisreg extends AbstractCommandTransaction {

    private final List<String> DATE_HOUR_FIELDS = Arrays.asList("@timestamp", "data_atualizacao", "data_atualizacao_marcacao",
            "data_atualizacao_solicitacao", "data_desejada", "data_internacao", "data_previsao_alta", "data_reserva",
            "data_solicitacao", "data_cancelamento", "dt_atualizacao", "data_aprovacao",
            "data_confirmacao", "data_marcacao", "data_observacao");

    public final List<String> DATE_FIELDS = Arrays.asList("dt_nascimento_usuario");
    private final List<String> HOUR_FIELDS = Arrays.asList("hora_alta", "hora_autorizacao", "hora_solicitacao");
    private String url;
    private String usuario;
    private String senha;
    private String dados;
    private File file;

    public ExportarSisreg(String url, String usuario, String senha, String dados) {
        this.url = url;
        this.usuario = usuario;
        this.senha = senha;
        this.dados = dados;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validateSize();

        try {
            validateUrl(url);
            WebserviceResponse webserviceResponse = new WebserviceUtil().sendGetRequest(url, getHeaders(), dados);
            validateResponseDataSize(webserviceResponse.getResponseMessage());
            createCsvFile(webserviceResponse.getResponseMessage());
        } catch (IOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

    private void validateUrl(String url) throws ValidacaoException {
        if (!url.endsWith("_search")) {
            throw new ValidacaoException(Bundle.getStringApplication("url_invalida_nao_possui_search"));
        }
    }

    private void validateResponseDataSize(String response) throws ValidacaoException {
        JSONObject total = ((JSONObject) new JSONObject(response).get("hits")).getJSONObject("total");
        Integer value = total.getInt("value");
        String relation = total.getString("relation");

        if (value.equals(10000) && relation.equals("gte")) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_retornou_mais_que_dez_mil"));
        }
    }

    private void validateSize() throws ValidacaoException {
        Integer size = null;

        try {
            size = (new JSONObject(dados)).getInt("size");
        } catch (JSONException e) {
            if (e.getMessage().contains("not found")) {
                Loggable.log.info(e.getMessage());
            } else {
                throw new ValidacaoException(e);
            }
        }

        if (size != null && size > 10000) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_size_max"));
        }
    }

    private void createCsvFile(String response) throws IOException, ValidacaoException {
        file = new File("./exportacao_sisreg.csv");

        JSONArray jsonArrayData = createJsonArray(response);
        String csvString = CDL.toString(orderJsonArray(jsonArrayData));

        if(csvString == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_consulta_nao_retornou_nenhum_dado"));
        }

        csvString = csvString.replaceAll("null", "");
        csvString = csvString.replaceAll("NULL", "");

        FileUtils.writeStringToFile(file, csvString);
    }

    private JSONArray createJsonArray(String response) {
        JSONArray hitsArray = ((JSONObject) new JSONObject(response).get("hits")).getJSONArray("hits");
        JSONArray jsonArrayData = new JSONArray();

        for (int i = 0; i < hitsArray.length(); i++) {
            JSONObject source = ((JSONObject) hitsArray.get(i)).getJSONObject("_source");

            JSONArray laudoArray = new JSONArray();
            JSONArray procedimentosArray = new JSONArray();

            if (source.has("laudo") && (source.get("laudo") instanceof JSONArray)) {
                laudoArray = source.getJSONArray("laudo");
                source.remove("laudo");
            }

            if (source.has("procedimentos") && (source.get("procedimentos") instanceof JSONArray)) {
                procedimentosArray = source.getJSONArray("procedimentos");
                source.remove("procedimentos");
            }

            if (laudoArray.length() > 0) {
                for (int j =  0; j < laudoArray.length(); j++) {
                    JSONObject jsonLaudo = (JSONObject) laudoArray.get(j);
                    Iterator<String> iteratorJsonLaudoKeys = jsonLaudo.keys();

                    formatFields(jsonLaudo);

                    while(iteratorJsonLaudoKeys.hasNext()) {
                        String key = iteratorJsonLaudoKeys.next();
                        source.put("laudo_" + (j + 1) + "_" + key, jsonLaudo.get(key));
                    }
                }
            }

            if (procedimentosArray.length() > 0) {
                for (int k =  0; k < procedimentosArray.length(); k++) {
                    JSONObject jsonProcedimentos = (JSONObject) procedimentosArray.get(k);
                    Iterator<String> iteratorJsonProcedimentosKeys = jsonProcedimentos.keys();

                    formatFields(jsonProcedimentos);

                    while(iteratorJsonProcedimentosKeys.hasNext()) {
                        String key = iteratorJsonProcedimentosKeys.next();
                        source.put("procedimentos_" + (k + 1) + "_" + key, jsonProcedimentos.get(key));
                    }
                }
            }

            formatFields(source);

            jsonArrayData.put(source);
        }

        return jsonArrayData;
    }

    private void formatFields(JSONObject jsonObject) {
        formatDateHourFields(jsonObject);
        formatDateFields(jsonObject);
        formatHourFields(jsonObject);
    }

    private void formatDateFields(JSONObject jsonObject) {
        for (String dateField : DATE_FIELDS) {
            if (jsonObject.has(dateField) && jsonObject.get(dateField) != JSONObject.NULL) {
                Date date;

                try {
                    date = new SimpleDateFormat("yyyy-MM-dd").parse(jsonObject.get(dateField).toString().trim());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                if (date != null) {
                    jsonObject.put(dateField, new SimpleDateFormat("dd/MM/yyyy").format(date));
                }
            }
        }
    }

    private void formatDateHourFields(JSONObject jsonObject) {
        for (String dateHourField : DATE_HOUR_FIELDS) {
            if (jsonObject.has(dateHourField) && jsonObject.get(dateHourField) != JSONObject.NULL) {
                Date date;

                try {
                    date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(jsonObject.get(dateHourField).toString().trim());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                if (date != null) {
                    jsonObject.put(dateHourField, new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(date));
                }
            }
        }
    }

    private void formatHourFields(JSONObject jsonObject) {
        for (String hourField : HOUR_FIELDS) {
            if (jsonObject.has(hourField) && jsonObject.get(hourField) != JSONObject.NULL) {
                Date hour;
                try {
                    hour = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(jsonObject.get(hourField).toString());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                if (hour != null) {
                    jsonObject.put(hourField, new SimpleDateFormat("HH:mm:ss").format(hour));
                }
            }
        }
    }

    private JSONArray orderJsonArray(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(i);
            SortedSet<String> keys = new TreeSet<>(jsonObject.keySet());
            JSONObject jsonObjectAux = new JSONObject();

            try {
                Field changeMap = jsonObjectAux.getClass().getDeclaredField("map");
                changeMap.setAccessible(true);
                changeMap.set(jsonObjectAux, new LinkedHashMap<>());
                changeMap.setAccessible(false);
            } catch (IllegalAccessException | NoSuchFieldException e) {
                Loggable.log.info(e.getMessage());
            }

            for (String key : keys) {
                String value = jsonObject.get(key).toString();
                jsonObjectAux.put(key, value);
            }

            jsonArray.put(i, jsonObjectAux);
        }

        return jsonArray;

    }

    private Map<String, String> getHeaders() throws ValidacaoException {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", getBasicAuthorizationHeader());
        return headers;
    }

    private String getBasicAuthorizationHeader() {
        String valorEncode = usuario + ":" + senha;
        return "Basic " + Base64.getEncoder().encodeToString(valorEncode.getBytes());
    }

    public File getFile() {
        return file;
    }
}
