package br.com.ksisolucoes.bo.entradas.recebimento.registronotafiscal;

import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;

import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class CadastrarAprovarItensNotaFiscal extends AbstractCommandTransaction {

    private RegistroNotaFiscal registroNotaFiscal;
    private List<RegistroItemNotaFiscal> itens;

    public CadastrarAprovarItensNotaFiscal(RegistroNotaFiscal registroNotaFiscal, List<RegistroItemNotaFiscal> itens) {
        this.registroNotaFiscal = registroNotaFiscal;
        this.itens = itens;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        registroNotaFiscal = BOFactory.getBO(RegistroNotaFiscalFacade.class).cadastrarNotaFiscal(this.registroNotaFiscal, itens);

        Set<RegistroItemNotaFiscal> setRegistroItemNotaFiscal = new HashSet(itens);
        BOFactory.getBO(RegistroNotaFiscalFacade.class).aprovarRegistroItemNotaFiscal(setRegistroItemNotaFiscal);
    }

    public RegistroNotaFiscal getRegistroNotaFiscal() {
        return registroNotaFiscal;
    }

}
