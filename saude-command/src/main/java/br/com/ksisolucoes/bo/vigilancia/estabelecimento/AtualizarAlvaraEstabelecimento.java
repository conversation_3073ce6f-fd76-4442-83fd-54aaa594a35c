package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlvara;
import org.hibernate.criterion.Restrictions;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class AtualizarAlvaraEstabelecimento extends AbstractCommandTransaction {

    private RequerimentoVigilancia requerimentoVigilancia;

    public AtualizarAlvaraEstabelecimento(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Estabelecimento estabelecimento = (Estabelecimento) getSession().createCriteria(Estabelecimento.class)
                .add(Restrictions.eq(Estabelecimento.PROP_CODIGO, requerimentoVigilancia.getEstabelecimento().getCodigo()))
                .uniqueResult();

        if (estabelecimento != null) {
            String nroAlvara = null;
            Date validadeAlvara = null;
            Date dataValidadePrimeiroAlvara = estabelecimento.getDataValidadePrimeiroAlvara();

            RequerimentoAlvara rai = (RequerimentoAlvara) getSession().createCriteria(RequerimentoAlvara.class)
                    .add(Restrictions.eq(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .uniqueResult();
            if (rai != null && rai.getNumeroAlvara() != null) {
                nroAlvara = rai.getNumeroAlvara().toString();
                validadeAlvara = BOFactory.getBO(VigilanciaFacade.class).calcularDataValidadeRequerimentos(requerimentoVigilancia, rai.getAnoBase());
            }

            if (nroAlvara != null) {
                rai.setDataValidade(validadeAlvara);

                estabelecimento.setAlvara(nroAlvara);
                estabelecimento.setValidadeAlvara(validadeAlvara);
                if (dataValidadePrimeiroAlvara == null) {
                    estabelecimento.setDataValidadePrimeiroAlvara(validadeAlvara);
                }

                if (rai.getEstabelecimentoSetores() != null) {
                    rai.getEstabelecimentoSetores().setDataValidadeAlvara(validadeAlvara);
                    BOFactory.save(rai.getEstabelecimentoSetores());
                }

                BOFactory.save(rai);
                BOFactory.getBO(VigilanciaFacade.class).salvarEstabelecimentoAlvara(requerimentoVigilancia, estabelecimento, rai);
                BOFactory.save(estabelecimento);
            }

            requerimentoVigilancia.setEstabelecimento(estabelecimento);
            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAlvaraFiscais(requerimentoVigilancia, requerimentoVigilancia.getDataFinalizacao());
        }
    }
}