/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.tuberculose.tuberculoseacompanhamentoresultados;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamentoResultados;

/**
 * <AUTHOR>
 */
public class SaveTuberculoseAcompanhamentoResultados extends SaveVO<TuberculoseAcompanhamentoResultados> {

    public SaveTuberculoseAcompanhamentoResultados(TuberculoseAcompanhamentoResultados vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setUsuarioUpdate(getSessao().<Usuario>getUsuario());
        this.vo.setDataUsuario(DataUtil.getDataAtual());
        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }

}
