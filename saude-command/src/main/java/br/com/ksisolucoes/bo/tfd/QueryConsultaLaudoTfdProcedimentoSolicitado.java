/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.tfd;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.LaudoTfdDTO;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.QueryConsultaLaudoTfdProcedimentoSolicitadoDTOParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameApac;
import br.com.ksisolucoes.vo.prontuario.basico.ExameBpai;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaLaudoTfdProcedimentoSolicitado extends CommandQuery{

    private List<LaudoTfdDTO> dtoList;
    private QueryConsultaLaudoTfdProcedimentoSolicitadoDTOParam param;

    public QueryConsultaLaudoTfdProcedimentoSolicitado(QueryConsultaLaudoTfdProcedimentoSolicitadoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        dtoList = new ArrayList<LaudoTfdDTO>();
        LoadManager load = LoadManager.getInstance(LaudoTfd.class);
        load.addProperties(new HQLProperties(LaudoTfd.class).getProperties());
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_EXAME_APAC, ExameApac.PROP_EXAME, ExameApac.PROP_CODIGO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_EXAME_APAC, ExameApac.PROP_EXAME, Exame.PROP_CODIGO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_EXAME_BPAI, ExameBpai.PROP_EXAME, ExameBpai.PROP_CODIGO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_EXAME_BPAI, ExameBpai.PROP_EXAME, Exame.PROP_CODIGO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO));
        load.addProperty(VOUtils.montarPath(LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO));

        load.addParameter(new QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_DATA_CADASTRO), param.getPeriodo()));
        if (param.isListarUltimoAtendimento()) {
            load.addParameter(new QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_ATENDIMENTO,Atendimento.PROP_CODIGO), param.getCodigosAtendimento()));
        } else {
            load.addParameter(new QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_USUARIO_CADSUS), param.getUsuarioCadSus()));
            if(param.getCodigosAtendimento() != null){
                load.addParameter(new QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_ATENDIMENTO,Atendimento.PROP_CODIGO),
                        QueryParameter.NOT_IN,
                        param.getCodigosAtendimento().get(0),
                        HQLHelper.NOT_RESOLVE_TYPE,
                        -1L));
            }
        }
        if(CollectionUtils.isNotNullEmpty(param.getSituacaoList())){
            load.addParameter(new QueryCustomParameter(VOUtils.montarPath(LaudoTfd.PROP_STATUS), QueryParameter.IN, param.getSituacaoList()));
        }
        load.addSorter(new QueryCustom.QueryCustomSorter(LaudoTfd.PROP_DATA_CADASTRO, BuilderQueryCustom.QuerySorter.DECRESCENTE));

        List<LaudoTfd> laudos = load.start().getList();

        for (LaudoTfd laudoTfd : laudos) {
            List<ProcedimentoSolicitadoTfd> procedimentos = LoadManager.getInstance(ProcedimentoSolicitadoTfd.class)
                    .addParameter(new QueryCustomParameter(ProcedimentoSolicitadoTfd.PROP_LAUDO_TFD, laudoTfd))
                    .start().getList();

            LaudoTfdDTO dto = new LaudoTfdDTO();
            dto.setLaudoTfd(laudoTfd);
            dto.setProcedimentoSolicitadoTfdList(procedimentos);

            dtoList.add(dto);
        }
    }

    public List<LaudoTfdDTO> getDtoList() {
        return dtoList;
    }
    
}
