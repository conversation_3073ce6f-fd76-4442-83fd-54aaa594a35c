package br.com.ksisolucoes.bo.integracao.pni.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.vacina.pni.PniVacinado;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\,", crlf = "UNIX")
public class GeracaoPniVacinadoBind implements IBindVoExport<PniVacinado> {

    @DataField(pos = 1, required = true)
    private Long codigoSistemaOrigem;
    @DataField(pos = 2, required = true)
    private String codigoChaveSistemaOrigem;
    @DataField(pos = 3, required = true)
    private String codigoCnesEmpresa;
    @DataField(pos = 4, required = false)
    private String numeroCartao;
    @DataField(pos = 5, required = true)
    private String nomePaciente;
    @DataField(pos = 6, required = true)
    private String nomeMae;
    @DataField(pos = 7, required = true)
    private String sexo;
    @DataField(pos = 8, required = true)
    private String dataNascimento;
    @DataField(pos = 9, required = false)
    private String codigoRaca;
    @DataField(pos = 10, required = false)
    private String siglaZona;
    @DataField(pos = 11, required = true)
    private String codigoPais;
    @DataField(pos = 12, required = true)
    private String codigoCidade;
    @DataField(pos = 13, required = false)
    private String descricaoBairro;
    @DataField(pos = 14, required = false)
    private String descricaoEndereco;
    @DataField(pos = 15, required = false)
    private String numeroEndereco;
    @DataField(pos = 16, required = false)
    private String descricaoComplemento;
    @DataField(pos = 17, required = false)
    private String cep;
    @DataField(pos = 18, required = false)
    private String telefone;
    @DataField(pos = 19, required = false)
    private String email;
    @DataField(pos = 20, required = false)
    private Long codigoEtnia;
    @DataField(pos = 21, required = false)
    private Long codigoBairro;
    @DataField(pos = 22, required = false)
    private String codigoMunicipioBairro;
    @DataField(pos = 23, required = false)
    private Long codigoTipoDocumento;
    @DataField(pos = 24, required = false)
    private String numeroDocumento;
    @DataField(pos = 25, required = false)
    private Long codigoTipoSaidaPaciente;
    @DataField(pos = 26, required = false)
    private String nomeSocial;
    @DataField(pos = 27, required = false)
    private String codigoPaisNascimento;
    @DataField(pos = 28, required = false)
    private String codigoCidadeNascimento;
    @DataField(pos = 29, required = true)
    private String flagExclusao;
    @DataField(pos = 30, required = true)
    private String dataExclusao;

    @Override
    public void buildProperties(PniVacinado vo) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

        codigoSistemaOrigem = vo.getPni().getCodigoSistemaOrigem();
        codigoChaveSistemaOrigem = "\"" + vo.getUsuarioCadsus().getCodigo().toString() + "\"";
        codigoCnesEmpresa = "\"" + vo.getEmpresa().getCnes() + "\"";
        if (vo.getNumeroCartao() != null) {
            numeroCartao = "\"" + vo.getNumeroCartao().toString() + "\"";
        } else {
            numeroCartao = "\"" + "" + "\"";
        }
        nomePaciente = "\"" + vo.getNomePaciente() + "\"";
        nomeMae = "\"" + vo.getNomeMae() + "\"";
        sexo = "\"" + vo.getSexo() + "\"";
        dataNascimento = "\"" + sdf.format(vo.getDataNascimento()) + "\"";
        if (vo.getCodigoRaca() != null) {
            codigoRaca = "\"" + vo.getCodigoRaca().toString() + "\"";
        } else {
            codigoRaca = "\"" + "" + "\"";
        }
        siglaZona = "\"" + Coalesce.asString(vo.getSiglaZonaDomiciliar()) + "\"";
        if (vo.getCodigoPais() != null) {
            codigoPais = "\"" + vo.getCodigoPais().toString() + "\"";
        } else {
            codigoPais = "\"" + "" + "\"";
        }
        if (vo.getCodigoCidade() != null) {
            codigoCidade = "\"" + vo.getCodigoCidade().toString() + "\"";
        } else {
            codigoCidade = "\"" + "" + "\"";
        }
        descricaoBairro = "\"" + Coalesce.asString(vo.getDescricaoBairro()) + "\"";
        descricaoEndereco = "\"" + Coalesce.asString(vo.getDescricaoEndereco()) + "\"";
        numeroEndereco = "\"" + Coalesce.asString(vo.getNumeroEndereco()) + "\"";
        descricaoComplemento = "\"" + Coalesce.asString(vo.getDescricaoComplemento()) + "\"";
        cep = "\"" + Coalesce.asString(vo.getCep()) + "\"";
        telefone = "\"" + Coalesce.asString(vo.getTelefone()) + "\"";
        email = "\"" + Coalesce.asString(vo.getEmail()) + "\"";
        codigoEtnia = vo.getCodigoEtnia();
        codigoBairro = vo.getCodigoBairro();
        if (vo.getCodigoMunicipioBairro() != null) {
            codigoMunicipioBairro = "\"" + Coalesce.asString(vo.getCodigoMunicipioBairro().toString()) + "\"";
        } else {
            codigoMunicipioBairro = "\"" + "" + "\"";
        }
        codigoTipoDocumento = vo.getCodigoTipoDocumento();
        numeroDocumento = "\"" + Coalesce.asString(vo.getNumeroDocumento()) + "\"";
        codigoTipoSaidaPaciente = null;
        nomeSocial = "\"" + Coalesce.asString(vo.getNomeSocial()) + "\"";
        if (vo.getCodigoPaisNascimento() != null) {
            codigoPaisNascimento = "\"" + vo.getCodigoPaisNascimento().toString() + "\"";
        } else {
            codigoPaisNascimento = "\"" + "" + "\"";
        }
        if (vo.getCodigoCidadeNascimento() != null) {
            codigoCidadeNascimento = "\"" + vo.getCodigoCidadeNascimento().toString() + "\"";
        } else {
            codigoCidadeNascimento = "\"" + "" + "\"";
        }
        flagExclusao = "\"" + "N" + "\"";
        dataExclusao= "\"" + "" + "\"";
    }
}
