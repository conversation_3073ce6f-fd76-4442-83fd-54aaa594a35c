package br.com.ksisolucoes.bo.entradas.recebimento.registronotafiscal;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.recebimento.interfaces.facade.RegistroNotaFiscalFacade;
import br.com.ksisolucoes.bo.materiais.recebimento.interfaces.dto.NotaFiscalOrdemCompraDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Licitacao;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalLicitacao;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CadastrarNotaFiscalOrdemCompra extends AbstractCommandTransaction {

    private RegistroNotaFiscal registroNotaFiscal;
    private Licitacao licitacao;
    private List<NotaFiscalOrdemCompraDTO> itens;

    public CadastrarNotaFiscalOrdemCompra(RegistroNotaFiscal registroNotaFiscal, List<NotaFiscalOrdemCompraDTO> itens, Licitacao licitacao) {
        this.registroNotaFiscal = registroNotaFiscal;
        this.itens = itens;
        this.licitacao = licitacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Set<RegistroItemNotaFiscal> registroItemNotaFiscalList = new LinkedHashSet<RegistroItemNotaFiscal>();
        for(NotaFiscalOrdemCompraDTO dto : itens){
            registroItemNotaFiscalList.add(dto.getRegistroItemNotaFiscal());
        }

        registroNotaFiscal.setItemNotaFiscalSet(registroItemNotaFiscalList);
        
        registroNotaFiscal.setEmpresa(getSessao().<Empresa>getEmpresa());
        
        registroNotaFiscal.setValorIpi(0D);
        registroNotaFiscal.setValorMercadoria(0D);
        
        TipoDocumento tipoDocumento = (TipoDocumento) getSession().get(TipoDocumento.class, registroNotaFiscal.getTipoDocumento().getCodigo());
        
        if (RepositoryComponentDefault.SIM.equals(tipoDocumento.getFlagDigitaOrdemCompra()) && registroNotaFiscal.getCodigo() != null) {
            BOFactory.getBO(RegistroNotaFiscalFacade.class).removerOrdemCompraEloNotaFiscal(registroNotaFiscal, itens);
        }
        
        if (RepositoryComponentDefault.SIM.equals(tipoDocumento.getFlagLicitacao())) {
            if (registroNotaFiscal.getCodigo()!=null) {
                List<RegistroItemNotaFiscalLicitacao> elos = LoadManager.getInstance(RegistroItemNotaFiscalLicitacao.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroItemNotaFiscalLicitacao.PROP_REGISTRO_ITEM_NOTA_FISCAL, RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), registroNotaFiscal))
                        .start().getList();

                for (RegistroItemNotaFiscalLicitacao registroItemNotaFiscalLicitacao : elos) {
                    BOFactory.delete(registroItemNotaFiscalLicitacao);
                }
            }
        }
        
        this.registroNotaFiscal = BOFactory.getBO(RegistroNotaFiscalFacade.class).save(registroNotaFiscal);
        
        if (RepositoryComponentDefault.SIM.equals(tipoDocumento.getFlagDigitaOrdemCompra())) {
            BOFactory.getBO(RegistroNotaFiscalFacade.class).gerarOrdemCompraEloNotaFiscal(itens);
        }
        
        if (RepositoryComponentDefault.SIM.equals(tipoDocumento.getFlagLicitacao())) {
            for (NotaFiscalOrdemCompraDTO dto : itens) {
                LicitacaoItem licitacaoItem = (LicitacaoItem) getSession().createCriteria(LicitacaoItem.class)
                        .add(Restrictions.eq(VOUtils.montarPath(LicitacaoItem.PROP_LICITACAO), licitacao))
                        .add(Restrictions.eq(VOUtils.montarPath(LicitacaoItem.PROP_PRODUTO), dto.getRegistroItemNotaFiscal().getProduto()))
                        .add(Restrictions.eq(VOUtils.montarPath(LicitacaoItem.PROP_STATUS), LicitacaoItem.StatusLicitacaoItem.LICITADO.value()))
                        .uniqueResult();
                
                RegistroItemNotaFiscal loadItem = (RegistroItemNotaFiscal) getSession().get(RegistroItemNotaFiscal.class, dto.getRegistroItemNotaFiscal().getCodigo());
                
                if (CollectionUtils.isNotNullEmpty(dto.getRegistroItemNotaFiscal().getRecebimentoGruposEstoque())) {
                    for (RecebimentoGrupoEstoque recebimentoGrupoEstoque : dto.getRegistroItemNotaFiscal().getRecebimentoGruposEstoque()) {
                        RegistroItemNotaFiscalLicitacao registroItemNotaFiscalLicitacao = new RegistroItemNotaFiscalLicitacao();
                        registroItemNotaFiscalLicitacao.setRegistroItemNotaFiscal(loadItem);
                        registroItemNotaFiscalLicitacao.setLicitacaoItem(licitacaoItem);
                        registroItemNotaFiscalLicitacao.setLote(recebimentoGrupoEstoque.getGrupoEstoque());
                        BOFactory.save(registroItemNotaFiscalLicitacao);
                    }
                } else {
                    RegistroItemNotaFiscalLicitacao registroItemNotaFiscalLicitacao = new RegistroItemNotaFiscalLicitacao();
                    registroItemNotaFiscalLicitacao.setRegistroItemNotaFiscal(loadItem);
                    registroItemNotaFiscalLicitacao.setLicitacaoItem(licitacaoItem);
                    registroItemNotaFiscalLicitacao.setLote(GrupoEstoque.GRUPO_ESTOQUE_PADRAO);
                    BOFactory.save(registroItemNotaFiscalLicitacao);
                }
            }
        }
    }

    public RegistroNotaFiscal getRegistroNotaFiscal() {
        return registroNotaFiscal;
    }

}