package br.com.ksisolucoes.bo.prontuario.basico.atendimentoinformacao;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;

/**
 * <AUTHOR>
 * <PERSON>riado em: Jul 10, 2013
 */
public class CancelarAtendimentoInformacao extends AbstractCommandTransaction{

    private Atendimento atendimento;

    public CancelarAtendimentoInformacao(Atendimento atendimento) {
        this.atendimento = atendimento;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        AtendimentoInformacao aiList = (AtendimentoInformacao) getSession().createCriteria(AtendimentoInformacao.class)
            .add(Restrictions.eq(AtendimentoInformacao.PROP_SEQUENCIA_CICLO, Coalesce.asLong(atendimento.getSequencialCiclo())))
            .createCriteria(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL)
            .add(Restrictions.eq(Atendimento.PROP_CODIGO, atendimento.getAtendimentoPrincipal().getCodigo()))
            .uniqueResult();
        if(aiList != null){
            aiList.setStatusAtendimento(AtendimentoInformacao.StatusAtendimento.CANCELADO.value());
            
            BOFactory.save(aiList);
        }
    }
}
