package br.com.ksisolucoes.bo.prontuario.hospital.leitoquarto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoDesativacao;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;

/**
 *
 * <AUTHOR>
 */
public class ReativarLeitoQuarto extends AbstractCommandTransaction{
    private LeitoQuarto leitoQuarto;

    public ReativarLeitoQuarto(LeitoQuarto leitoQuarto) {
        this.leitoQuarto = leitoQuarto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.leitoQuarto.setSituacao(LeitoQuarto.Situacao.LIBERADO.value());
        BOFactory.save(leitoQuarto);
        
        LeitoDesativacao leitoDesativacao = LoadManager.getInstance(LeitoDesativacao.class)
                .addProperties(new HQLProperties(LeitoDesativacao.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoDesativacao.PROP_LEITO_QUARTO, this.leitoQuarto))
                .addParameter(new QueryCustom.QueryCustomParameter(LeitoDesativacao.PROP_DATA_REATIVACAO, QueryCustom.QueryCustomParameter.IS_NULL))
                .start().getVO();
        
        if (leitoDesativacao != null) {
            leitoDesativacao.setLeitoQuarto(leitoQuarto);
            leitoDesativacao.setDataReativacao(DataUtil.getDataAtual());
            leitoDesativacao.setUsuarioReativacao((Usuario) getSessao().getUsuario());
            BOFactory.save(leitoDesativacao);
        }
    }
}
