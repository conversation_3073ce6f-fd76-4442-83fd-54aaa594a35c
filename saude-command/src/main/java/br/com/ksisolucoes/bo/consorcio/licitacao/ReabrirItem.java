package br.com.ksisolucoes.bo.consorcio.licitacao;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.LicitacaoItem;

/**
 *
 * <AUTHOR>
 */
public class ReabrirItem extends AbstractCommandTransaction {

    private LicitacaoItem licitacaoItem; 

    public ReabrirItem(LicitacaoItem licitacaoItem) {
        this.licitacaoItem = licitacaoItem;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        licitacaoItem.setStatus(LicitacaoItem.StatusLicitacaoItem.ABERTO.value());
        licitacaoItem.setPessoa(null);
        licitacaoItem.setPessoaOriginal(null);
        licitacaoItem.setPrecoUnitario(null);
        licitacaoItem.setPrecoUnitarioOriginal(null);
        licitacaoItem.setPrecoTotal(null);
        licitacaoItem.setPrecoTotalOriginal(null);
        licitacaoItem.setJustificativa(null);
        this.licitacaoItem = BOFactory.save(licitacaoItem);
    }

    public LicitacaoItem getLicitacaoItem() {
        return licitacaoItem;
    }
    
}
