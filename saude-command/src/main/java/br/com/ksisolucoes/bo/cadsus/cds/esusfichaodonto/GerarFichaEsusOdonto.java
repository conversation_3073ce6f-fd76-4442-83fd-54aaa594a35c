package br.com.ksisolucoes.bo.cadsus.cds.esusfichaodonto;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.esus.ProcedimentosEsusDTO;
import br.com.celk.esus.ProcedimentosSiaDTO;
import br.com.celk.esus.ProcedimentosSiaDTOParam;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaIndividualOdontoDTO;
import br.com.ksisolucoes.bo.esus.cds.interfaces.dto.CadastroFichaIndividualOdontoItemDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdonto;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaOdontoItemProcedimento;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;

import java.util.*;

import static br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper.carregarNumeroCartao;

/**
 * Created by sulivan on 13/07/17.
 */
public class GerarFichaEsusOdonto extends AbstractCommandTransaction {

    private Atendimento atendimento;

    public GerarFichaEsusOdonto(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // Item
        EsusFichaOdontoItem item = new EsusFichaOdontoItem();
        item.setAtendimento(atendimento);
        item.setTurno(atendimento.getDataAtendimento() != null ? Data.turno(atendimento.getDataAtendimento()).value() + 1 : null);
        item.setNumeroProntuario(atendimento.getCodigo().toString());
        item.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        item.setDataNascimento(atendimento.getUsuarioCadsus().getDataNascimento());
        item.setLocalAtendimento(atendimento.getLocalAtendimento() != null ? atendimento.getLocalAtendimento() : Atendimento.LocalAtendimentoEsus.UBS.value());
        Long numeroCartao = carregarNumeroCartao(atendimento.getUsuarioCadsus());
        if (numeroCartao != null) {
            item.setNumeroCartao(numeroCartao);
        }
        if (RepositoryComponentDefault.SEXO_MASCULINO.equals(atendimento.getUsuarioCadsus().getSexo())) {
            item.setSexo(0L);
        } else {
            item.setSexo(1L);
        }

        // Consultar Atendimento Primário
        AtendimentoPrimario atendimentoPrimario = (AtendimentoPrimario) getSession().createCriteria(AtendimentoPrimario.class)
                .add(Restrictions.eq(AtendimentoPrimario.PROP_ATENDIMENTO, atendimento))
                .uniqueResult();

        if (atendimentoPrimario != null && atendimentoPrimario.getGestante() != null && RepositoryComponentDefault.SIM_LONG.equals(atendimentoPrimario.getGestante())) {
            Criteria cAtendimentoPreNatal = getSession().createCriteria(AtendimentoPreNatal.class)
                    .add(Restrictions.eq(AtendimentoPreNatal.PROP_ATENDIMENTO, atendimento));
            cAtendimentoPreNatal.createCriteria(AtendimentoPreNatal.PROP_PRE_NATAL);

            AtendimentoPreNatal apn = (AtendimentoPreNatal) cAtendimentoPreNatal.uniqueResult();

            if (apn != null && PreNatal.Status.ABERTO.value().equals(apn.getPreNatal().getStatus())) {
                item.setFlagGestante(RepositoryComponentDefault.SIM_LONG);
            } else {
                item.setFlagGestante(RepositoryComponentDefault.NAO_LONG);
            }
        } else {
            item.setFlagGestante(RepositoryComponentDefault.NAO_LONG);
        }

        // Consultar Atendimentos Odonto Plano Urgente
        List<AtendimentoOdontoPlano> atendOdontoPlanoList = getSession().createCriteria(AtendimentoOdontoPlano.class)
                .add(Restrictions.eq(AtendimentoOdontoPlano.PROP_FLAG_URGENTE, RepositoryComponentDefault.SIM_LONG))
                .add(Restrictions.eq(AtendimentoOdontoPlano.PROP_STATUS, AtendimentoOdontoPlano.Status.CONCLUIDO.value()))
                .add(Restrictions.eq(AtendimentoOdontoPlano.PROP_ATENDIMENTO, atendimento))
                .addOrder(Order.desc(AtendimentoOdontoPlano.PROP_CODIGO))
                .list();

        if(CollectionUtils.isNotNullEmpty(atendOdontoPlanoList)){
            item.setCodigoTipoAtendimento(EsusFichaOdontoItem.TipoAtendimento.ATENDIMENTO_URGENCIA.value()); // Atendimento Urgente
        } else {
            item.setCodigoTipoAtendimento(EsusFichaOdontoItem.TipoAtendimento.CONSULTA_AGENDADA.value()); //agendada (verificar)
        }

        populaConduta(item);

        if(Coalesce.asLong(item.getCodigoTipoConduta()) <= 0L){
            throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_vigilancia_saude_bucal_dados_consulta"));
        }

        if (atendimento.getClassificacaoAtendimento() != null) {
            ClassificacaoAtendimento classificacaoAtendimento = (ClassificacaoAtendimento) getSession().get(ClassificacaoAtendimento.class, atendimento.getClassificacaoAtendimento().getCodigo());
            try {
                if (classificacaoAtendimento.getCodigoEsus() != null) {
                    EsusFichaOdontoItem.VigilanciaSaudeBucal somatorio = EsusFichaOdontoItem.VigilanciaSaudeBucal.value(Long.parseLong(classificacaoAtendimento.getCodigoEsus()));
                    if (somatorio != null) {
                        item.setCodigoTipoVigilanciaSaudeBucal(somatorio.sum());
                    } else {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_codigo_esus_classificacao_atendimento_X_obrigatorio", classificacaoAtendimento.getDescricao()));
                    }
                } else {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_codigo_esus_classificacao_atendimento_X_obrigatorio", classificacaoAtendimento.getDescricao()));
                }
            } catch (NumberFormatException nfe) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_formato_codigo_esus_classificacao_atendimento_X_deve_ser_apenas_numeros", classificacaoAtendimento.getDescricao()));
            }
        } else {
            item.setCodigoTipoVigilanciaSaudeBucal(EsusFichaOdontoItem.VigilanciaSaudeBucal.NAO_IDENTIFICADO.sum()); //99 = (NÃO IDENTIFICADO)
        }

//        //TODO -    QUEBRA GALHO PARA O SUPERTE #16070 - MELHORAR
//        if (!EsusFichaOdontoItem.TipoAtendimento.ATENDIMENTO_URGENCIA.value().equals(item.getCodigoTipoAtendimento())) {
//            item.setCodigoTipoConsulta(AtendimentoOdontoFicha.TipoConsultaEsus.CONSULTA_DE_RETORNO.getValue());
//        }

        // Consultar Atendimentos Odonto Ficha
        AtendimentoOdontoFicha atendimentoOdontoFicha = LoadManager.getInstance(AtendimentoOdontoFicha.class)
                .addProperties(AtendimentoOdontoFicha.PROP_CODIGO)
                .addProperties(AtendimentoOdontoFicha.PROP_GRAVIDA)
                .addProperties(AtendimentoOdontoFicha.PROP_FLAG_POSSUI_NECESSIDADES_ESPECIAIS)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoFicha.PROP_ATENDIMENTO,
                                                                    Atendimento.PROP_USUARIO_CADSUS), atendimento.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoOdontoFicha.PROP_STATUS, QueryCustom.QueryCustomParameter.IN,
                                                                    Arrays.asList(AtendimentoOdontoFicha.Status.PENDENTE.value(),
                                                                    AtendimentoOdontoFicha.Status.EM_ANDAMENTO.value())))
                .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoOdontoFicha.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .setMaxResults(1).start().getVO();

        if(atendimentoOdontoFicha != null){
            item.setCodigoTipoAtendimento(EsusFichaOdontoItem.TipoAtendimento.CONSULTA_AGENDADA.value()); //agendada
            item.setFlagNecessidadeEspecial(atendimentoOdontoFicha.getFlagPossuiNecessidadesEspeciais());
            item.setFlagGestante(atendimentoOdontoFicha.getGravida());
        }

        UsuarioCadsusEsus usuarioCadsusEsus = LoadManager.getInstance(UsuarioCadsusEsus.class)
                .addProperty(UsuarioCadsusEsus.PROP_POSSUI_DEFICIENCIA)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), atendimento.getUsuarioCadsus()))
                .start().getVO();
        if (usuarioCadsusEsus != null) {
            item.setFlagNecessidadeEspecial(usuarioCadsusEsus.getPossuiDeficiencia());
        } else {
            item.setFlagNecessidadeEspecial(RepositoryComponentDefault.NAO_LONG);
        }

        CadastroFichaIndividualOdontoItemDTO cadastroFichaIndividualOdontoItemDTO = new CadastroFichaIndividualOdontoItemDTO();
        if (atendimento.getCodigoTipoConsulta() != null) {
            item.setCodigoTipoConsulta(atendimento.getCodigoTipoConsulta());
        }
        if (atendimento.getTipoAtendimentoOdonto() != null) {
            item.setCodigoTipoAtendimento(atendimento.getTipoAtendimentoOdonto());
        }
        populaTipoFornecimento(item);

        cadastroFichaIndividualOdontoItemDTO.setEsusFichaOdontoItem(item);

        // Consultar Procedimentos e-sus
        List<ProcedimentosEsusDTO> procedimentosEsusList = BOFactory.getBO(UsuarioCadsusFacade.class).queryConsultaProcedimentosEsus(Arrays.asList(atendimento), ProcedimentoEsus.FichaIntegracao.ODONTOLOGICA.value());
        List<Procedimento> procedimentoList = Lambda.extract(procedimentosEsusList, Lambda.on(ProcedimentosEsusDTO.class).getProcedimento());

        // Consultar Procedimentos sia
        ProcedimentosSiaDTOParam param = new ProcedimentosSiaDTOParam();
        param.setAtendimentosList(Arrays.asList(atendimento));
        param.setProcedimentosList(procedimentoList);
        param.setValidarProcedimentoEloEsusOdonto(true);
        List<ProcedimentosSiaDTO> procedimentosSiaList = BOFactory.getBO(UsuarioCadsusFacade.class).queryConsultaProcedimentosSia(param);

        cadastroFichaIndividualOdontoItemDTO.getEsusFichaOdontoItemProcedimentoList().addAll(populaProcedimentosEsus(procedimentosEsusList));
        cadastroFichaIndividualOdontoItemDTO.getEsusFichaOdontoItemProcedimentoList().addAll(populaProcedimentosSia(procedimentosSiaList));

        // Ficha
        EsusFichaOdonto esusFichaOdonto = new EsusFichaOdonto();
        esusFichaOdonto.setEmpresa(atendimento.getEmpresaBpa());
        esusFichaOdonto.setProfissionalPrincipal(atendimento.getProfissional());
        esusFichaOdonto.setCboPrincipal(atendimento.getTabelaCbo());
        esusFichaOdonto.setDataAtendimento(Coalesce.asDate(atendimento.getDataAtendimento(), atendimento.getDataChegada()));
        esusFichaOdonto.setDataAtendimentoFinal(atendimento.getDataFechamento());

        if (atendimento.getProfissionalAuxiliar() != null) {
            esusFichaOdonto.setProfissionalSecundario(atendimento.getProfissionalAuxiliar());
            if (atendimento.getTabelaCboAuxiliar() != null) {
                esusFichaOdonto.setCboSecundario(atendimento.getTabelaCboAuxiliar());
            } else {
                QueryConsultaProfissionalCargaHorariaDTOParam paramConsulta = new QueryConsultaProfissionalCargaHorariaDTOParam();
                paramConsulta.setEmpresa(atendimento.getEmpresa());
                paramConsulta.setProfissional(atendimento.getProfissionalAuxiliar());

                TabelaCbo cbo = BOFactory.getBO(ProfissionalFacade.class).consultaProfissionalCargaHoraria(paramConsulta);
                esusFichaOdonto.setCboSecundario(cbo);
            }
        }

        List<EquipeProfissional> equipeProfissionalList = LoadManager.getInstance(EquipeProfissional.class)
                .addProperty(EquipeProfissional.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_CNES))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_STATUS, EquipeProfissional.STATUS_ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeProfissional.PROP_PROFISSIONAL, atendimento.getProfissional()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), atendimento.getEmpresaBpa()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(equipeProfissionalList)) {
            Equipe equipe = equipeProfissionalList.get(0).getEquipe();
            if (equipe != null && equipe.getEquipeCnes() != null) {
                esusFichaOdonto.setCodigoIne(Coalesce.asString(equipe.getEquipeCnes()));
            }
        }
        CadastroFichaIndividualOdontoDTO cadastroFichaIndividualOdontoDTO = new CadastroFichaIndividualOdontoDTO();
        cadastroFichaIndividualOdontoDTO.setEsusFichaOdonto(esusFichaOdonto);
        cadastroFichaIndividualOdontoDTO.getEsusFichaOdontoItemList().add(cadastroFichaIndividualOdontoItemDTO);

        // Obs: O registro EsusIntegracaoCds é gerado ao salvar a ficha
        BOFactory.getBO(EsusFacade.class).salvarFichaIndividualOdonto(cadastroFichaIndividualOdontoDTO);
    }

    private void populaConduta(EsusFichaOdontoItem item) {
        List<Long> condutaAtendimentoAdicionarList = new ArrayList<>();
        if (atendimento.getConduta() != null && atendimento.getConduta().getCodigo() != null) {
            Conduta conduta = (Conduta) getSession().get(Conduta.class, atendimento.getConduta().getCodigo());

            if (conduta != null && conduta.getCodigoEsus() != null && !Conduta.TipoConduta.CONSULTA_MEDICA.value().equals(atendimento.getConduta().getTipoConduta())) {
                condutaAtendimentoAdicionarList.add(conduta.getCodigoEsus());
            }
        }

        List<CondutaAtendimento> condutaAtendimentoList = getSession().createCriteria(CondutaAtendimento.class)
                .add(Restrictions.eq(CondutaAtendimento.PROP_ATENDIMENTO, atendimento))
                .list();

        if (CollectionUtils.isNotNullEmpty(condutaAtendimentoList)){
            List<CondutaAtendimento> condutaAtendimentoCodigoEsusList = Lambda.select(condutaAtendimentoList, Lambda.having(Lambda.on(CondutaAtendimento.class).getConduta().getCodigoEsus(), Matchers.notNullValue()));
            if (CollectionUtils.isNotNullEmpty(condutaAtendimentoCodigoEsusList)) {
                for(CondutaAtendimento ca : condutaAtendimentoCodigoEsusList){
                    if (Conduta.TipoConduta.CONSULTA_MEDICA.value().equals(ca.getConduta().getTipoConduta())) {
                        continue;
                    } else if(CollectionUtils.isNotNullEmpty(condutaAtendimentoAdicionarList) && condutaAtendimentoAdicionarList.contains(ca.getConduta().getCodigoEsus())){
                        continue;
                    }
                    condutaAtendimentoAdicionarList.add(ca.getConduta().getCodigoEsus());
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(condutaAtendimentoAdicionarList)) {
            Long somatorio = 0L;
            EsusFichaOdontoItem.Conduta conduta;
            for(Long i : condutaAtendimentoAdicionarList){
                conduta = EsusFichaOdontoItem.Conduta.value(i);

                if(conduta != null){
                    somatorio = new Dinheiro(somatorio).somar(conduta.sum().doubleValue()).longValue();
                }
            }
            item.setCodigoTipoConduta(somatorio);
        }
    }

    private void populaTipoFornecimento(EsusFichaOdontoItem item) {
        List<Long> tipoFornecimentoAtendimentoAdicionarList = new ArrayList<>();
        if (atendimento.getTipoFornecimentoOdonto() != null) {
            tipoFornecimentoAtendimentoAdicionarList.add(atendimento.getTipoFornecimentoOdonto());
        }

        List<FornecimentoOdontoAtend> fornecimentoOdontoAtendList = getSession().createCriteria(FornecimentoOdontoAtend.class)
                .add(Restrictions.eq(FornecimentoOdontoAtend.PROP_ATENDIMENTO, atendimento))
                .list();

        if (CollectionUtils.isNotNullEmpty(fornecimentoOdontoAtendList)) {
            for (FornecimentoOdontoAtend fornecimentoOdontoAtend : fornecimentoOdontoAtendList) {
                if (CollectionUtils.isNotNullEmpty(tipoFornecimentoAtendimentoAdicionarList) && tipoFornecimentoAtendimentoAdicionarList.contains(fornecimentoOdontoAtend.getTipoFornecimentoOdonto())) {
                    continue;
                }
                tipoFornecimentoAtendimentoAdicionarList.add(fornecimentoOdontoAtend.getTipoFornecimentoOdonto());
            }
        }

        if (CollectionUtils.isNotNullEmpty(tipoFornecimentoAtendimentoAdicionarList)) {
            Long somatorio = 0L;
            EsusFichaOdontoItem.Fornecimento fornecimento;
            for(Long codigoFornecimento : tipoFornecimentoAtendimentoAdicionarList){
                fornecimento = EsusFichaOdontoItem.Fornecimento.valorDe(codigoFornecimento);
                if(fornecimento != null){
                    somatorio = new Dinheiro(somatorio).somar(fornecimento.sum().doubleValue()).longValue();
                }
            }
            item.setCodigoTipoFornecimento(somatorio);
        }
    }

    private List<EsusFichaOdontoItemProcedimento> populaProcedimentosEsus(List<ProcedimentosEsusDTO> procedimentosEsusList) {
        List<EsusFichaOdontoItemProcedimento> esusFichaOdontoItemProcedimentoList = new ArrayList<>();
        Map<String, EsusFichaOdontoItemProcedimento> mapProcedimentos = new HashMap<>();

        if (CollectionUtils.isNotNullEmpty(procedimentosEsusList)) {
            EsusFichaOdontoItemProcedimento itemProcedimento;
            ProcedimentoEsus procedimentoEsus;

            for (ProcedimentosEsusDTO dto : procedimentosEsusList) {
                procedimentoEsus = (ProcedimentoEsus) getSession().get(ProcedimentoEsus.class, dto.getCodigoProcedimentoEsus());

                if (procedimentoEsus != null) {
                    String codigoEsus = dto.getCodigoEsus();
                    itemProcedimento = mapProcedimentos.get(codigoEsus);

                    if (itemProcedimento == null) {
                        itemProcedimento = new EsusFichaOdontoItemProcedimento();
                        itemProcedimento.setProcedimentoEsus(procedimentoEsus);
                        itemProcedimento.setQuantidade(Coalesce.asDouble(dto.getQuantidadeSum()).longValue());
                        mapProcedimentos.put(codigoEsus, itemProcedimento);
                    } else {
                        itemProcedimento.setQuantidade(itemProcedimento.getQuantidade() + Coalesce.asDouble(dto.getQuantidadeSum()).longValue());
                    }
                }
            }

            esusFichaOdontoItemProcedimentoList.addAll(mapProcedimentos.values());
        }
        return esusFichaOdontoItemProcedimentoList;
    }

    private List<EsusFichaOdontoItemProcedimento> populaProcedimentosSia(List<ProcedimentosSiaDTO> procedimentosSiaList) {
        List<EsusFichaOdontoItemProcedimento> esusFichaOdontoItemProcedimentoList = new ArrayList();
        if (CollectionUtils.isNotNullEmpty(procedimentosSiaList)) {
            EsusFichaOdontoItemProcedimento itemProcedimento;
            for (ProcedimentosSiaDTO dto : procedimentosSiaList) {
                itemProcedimento = new EsusFichaOdontoItemProcedimento();
                itemProcedimento.setProcedimento(dto.getProcedimento());
                itemProcedimento.setQuantidade(Coalesce.asDouble(dto.getQuantidadeSum()).longValue());
                esusFichaOdontoItemProcedimentoList.add(itemProcedimento);
            }
        }
        return esusFichaOdontoItemProcedimentoList;
    }
}
