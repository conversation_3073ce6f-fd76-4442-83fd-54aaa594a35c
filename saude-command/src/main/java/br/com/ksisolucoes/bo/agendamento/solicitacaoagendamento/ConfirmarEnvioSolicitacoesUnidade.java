package br.com.ksisolucoes.bo.agendamento.solicitacaoagendamento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.recebimentosolicitacoes.dto.ConfirmacaoEnvioSolicitacoesDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.HibernateUtil.rechargeVO;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class ConfirmarEnvioSolicitacoesUnidade extends AbstractCommandTransaction {
    
    private ConfirmacaoEnvioSolicitacoesDTO dto;

    public ConfirmarEnvioSolicitacoesUnidade(ConfirmacaoEnvioSolicitacoesDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Empresa empresaLogada = SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa();
        String descricao = Bundle.getStringApplication("msg_solicitacao_recebida_estabelecimento", empresaLogada.getDescricao());

        LoteSolicitacaoAgendamento lote = dto.getLoteSolicitacaoAgendamento();
        lote = rechargeVO(LoteSolicitacaoAgendamento.class, lote.getCodigo(), lote.getVersion());
        lote.setStatus(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.RECEBIDO.value());
        BOFactory.save(lote);
        
        for(LoteSolicitacaoAgendamentoItem item : dto.getLoteSolicitacaoAgendamentoItemList()){
            item.setDataAvaliacao(DataUtil.getDataAtual());
            item.setUsuarioAvaliacao(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
            item.setLoteSolicitacaoAgendamento(lote);
            
            BOFactory.save(item);
            
            BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, descricao, item.getSolicitacaoAgendamento());
        }
    }
    
}