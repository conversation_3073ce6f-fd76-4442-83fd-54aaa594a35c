package br.com.ksisolucoes.bo.vigilancia.faturamento;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.helper.FaturamentoVigilanciaHelper;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RegistroVisita;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GerarFaturamentoRegistroVisita extends AbstractCommandTransaction {

    private List<AtividadesVigilancia> atividadesVigilanciaList;
    private RegistroVisita registroVisita;
    private LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO;
    private List<Profissional> profissionalList;

    public GerarFaturamentoRegistroVisita(RegistroVisita registroVisita, List<Profissional> profissionalList, List<AtividadesVigilancia> atividadesVigilanciaList) {
        this.registroVisita = registroVisita;
        this.profissionalList = profissionalList;
        this.atividadesVigilanciaList = atividadesVigilanciaList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(profissionalList) && CollectionUtils.isNotNullEmpty(atividadesVigilanciaList)) {
            List<LancamentoAtividadesVigilancia> lancamentoAtividadesVigilanciaList = LoadManager.getInstance(LancamentoAtividadesVigilancia.class)
                    .addProperties(new HQLProperties(LancamentoAtividadesVigilancia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilancia.PROP_REGISTRO_VISITA, registroVisita))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(lancamentoAtividadesVigilanciaList)) {
                for (LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia : lancamentoAtividadesVigilanciaList) {
                    List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = LoadManager.getInstance(LancamentoAtividadesVigilanciaItem.class)
                            .addProperties(new HQLProperties(LancamentoAtividadesVigilanciaItem.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(LancamentoAtividadesVigilanciaItem.PROP_LANCAMENTO_ATIVIDADES_VIGILANCIA, lancamentoAtividadesVigilancia))
                            .start().getList();
                    if (CollectionUtils.isNotNullEmpty(lancamentoAtividadesVigilanciaItemList)) {
                        for (LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem : lancamentoAtividadesVigilanciaItemList) {
                            BOFactory.delete(lancamentoAtividadesVigilanciaItem);
                        }
                    }
                    BOFactory.delete(lancamentoAtividadesVigilancia);
                }
            }

            for (Profissional profissional : profissionalList) {
                lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();

                LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = gerarLancamentoAtividadeVigilancia(profissional, registroVisita);

                List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = new ArrayList();

                lancamentoAtividadesVigilanciaDTO.setFaturavel(false); //não gera item de conta (BPA)
                gerarAtividade(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList);

                lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
                lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilanciaItemList(lancamentoAtividadesVigilanciaItemList);
                BOFactory.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
            }
        }
    }

    private void gerarAtividade(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList) {
        for (AtividadesVigilancia atividadesVigilancia : atividadesVigilanciaList) {
            LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();
            lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia(atividadesVigilancia);
            lancamentoAtividadesVigilanciaItem.setPontuacao(atividadesVigilancia.getPontuacao());
            lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
            lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
            lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
            if (atividadesVigilancia.getProcedimento() != null && !RepositoryComponentDefault.NAO.equals(atividadesVigilancia.getProcedimento().getFlagFaturavel())) {
                lancamentoAtividadesVigilanciaDTO.setFaturavel(true);
            }
        }
    }

    private LancamentoAtividadesVigilancia gerarLancamentoAtividadeVigilancia(Profissional profissional, RegistroVisita registroVisita) {
        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        lancamentoAtividadesVigilancia.setProfissional(profissional);
        lancamentoAtividadesVigilancia.setTipoAtividade("Registro da Visita");
        lancamentoAtividadesVigilancia.setDataAtividade(Coalesce.asDate(registroVisita.getDataVisita(), registroVisita.getDataCadastro()));
        lancamentoAtividadesVigilancia.setRegistroVisita(registroVisita);
        if(registroVisita.getEstabelecimento() != null) {
            lancamentoAtividadesVigilancia.setEstabelecimento(registroVisita.getEstabelecimento());
            lancamentoAtividadesVigilancia.setFlagTipo(LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value());
            lancamentoAtividadesVigilancia.setCnpjCpfPessoa(registroVisita.getEstabelecimento().getCnpjCpf());
            lancamentoAtividadesVigilancia.setAtividadeEstabelecimento(FaturamentoVigilanciaHelper.getAtividadePrincipalEstabelecimento(registroVisita.getEstabelecimento()));
        } else {
            lancamentoAtividadesVigilancia.setDescricaoOutros(registroVisita.getDescricaoEstabelecimento());
            lancamentoAtividadesVigilancia.setFlagTipo(LancamentoAtividadesVigilancia.TipoPessoa.OUTROS.value());
        }
        return lancamentoAtividadesVigilancia;
    }
}
