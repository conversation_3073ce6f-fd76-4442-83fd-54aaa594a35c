package br.com.ksisolucoes.bo.prontuario.basico.encaminhamento;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoConsulta;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoConsultaCriterio;

import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class ExcluirEncaminhamentoConsulta extends AbstractCommandTransaction {

    private EncaminhamentoConsulta encaminhamentoConsulta;

    public ExcluirEncaminhamentoConsulta(Object vo) {
        encaminhamentoConsulta = (EncaminhamentoConsulta) vo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<EncaminhamentoConsultaCriterio> criterios = getSession().createCriteria(EncaminhamentoConsultaCriterio.class)
                .add(Restrictions.eq(EncaminhamentoConsultaCriterio.PROP_ENCAMINHAMENTO_CONSULTA, encaminhamentoConsulta))
                .list();

        for (EncaminhamentoConsultaCriterio criterio : criterios) {
            BOFactory.delete(criterio);
        }

        BOFactory.getBO(CadastroFacade.class).delete(encaminhamentoConsulta);

        BOFactory.getBO(CadastroFacade.class).delete(encaminhamentoConsulta.getEncaminhamento());

    }
}
