/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

/**
 *
 * <AUTHOR>
 */
public class CancelarSolicitacaoProcessoAgendamento extends AbstractCommandTransaction{

    private String motivoCancelamento;
    private Long codigoSolicitacao;

    public CancelarSolicitacaoProcessoAgendamento(String motivoCancelamento, Long codigoSolicitacao) {
        this.motivoCancelamento = motivoCancelamento;
        this.codigoSolicitacao = codigoSolicitacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        BOFactory.getBO(AgendamentoFacade.class).cancelarSolicitacaoAgendamento(codigoSolicitacao, motivoCancelamento, true);

        SolicitacaoAgendamento solicitacao = (SolicitacaoAgendamento) getSession().get(SolicitacaoAgendamento.class, codigoSolicitacao);
        solicitacao.setDataAutorizador(Data.getDataAtual());
        solicitacao.setUsuarioAutorizador(new Usuario(Usuario.USUARIO_ADMINISTRADOR));
        solicitacao.setObservacaoAutorizador(Bundle.getStringApplication("rotulo_solicitacao_cancelada")+": "+motivoCancelamento);
        
        BOFactory.getBO(CadastroFacade.class).save(solicitacao);
    }

}
