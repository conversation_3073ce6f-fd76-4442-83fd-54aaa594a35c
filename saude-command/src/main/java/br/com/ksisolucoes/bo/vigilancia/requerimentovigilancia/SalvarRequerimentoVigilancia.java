package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoVigilancia extends AbstractCommandTransaction<SalvarRequerimentoVigilancia> {

    private List<Long> excessao_validacao_geral = Arrays.asList(TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value(),
            TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value(),
            TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value());

    private RequerimentoVigilanciaSolicitacaoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;

    public SalvarRequerimentoVigilancia(RequerimentoVigilanciaSolicitacaoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoVigilancia requerimentoVigilancia = dto.getRequerimentoVigilancia();
        if (requerimentoVigilancia.getTipoDocumento() != null && !excessao_validacao_geral.contains(requerimentoVigilancia.getTipoDocumento())) {
            validacoesGerais(requerimentoVigilancia);
        }
        // regras implementadas no SaveRequerimentoVigilancia
        this.requerimentoVigilancia = BOFactory.save(requerimentoVigilancia);
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    private void validacoesGerais(RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = null;
        try {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
        }
        if (configuracaoVigilancia != null && RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagObrigatorioInformarTelefone())) {
            if (requerimentoVigilancia.getTelefoneSolicitante() == null && requerimentoVigilancia.getCelularSolicitante() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_celular_telefone_obrigatorio"));
            }
        }
    }
}
