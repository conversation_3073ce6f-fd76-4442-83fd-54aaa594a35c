package br.com.ksisolucoes.bo.prontuario.hospital.itemcontapaciente;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaProfissional;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeleteItemContaPaciente extends DeleteVO<ItemContaPaciente> {

    public DeleteItemContaPaciente(ItemContaPaciente vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<ItemContaProfissional> listProfissionais = getSession().createCriteria(ItemContaProfissional.class)
                .add(Restrictions.eq(ItemContaProfissional.PROP_ITEM_CONTA_PACIENTE, this.vo))
                .list();

        for (ItemContaProfissional profissional : listProfissionais) {
            BOFactory.delete(profissional);
        }
    }

}
