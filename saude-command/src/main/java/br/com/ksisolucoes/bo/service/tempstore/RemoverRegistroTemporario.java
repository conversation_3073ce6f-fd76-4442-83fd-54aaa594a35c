package br.com.ksisolucoes.bo.service.tempstore;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.TempStore;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import static ch.lambdaj.Lambda.*;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemoverRegistroTemporario extends AbstractCommandTransaction {

    private CodigoManager codigoManager;
    private String agrupador;
    private String campo;
    private Long codigoTempStore;

    public RemoverRegistroTemporario(CodigoManager codigoManager, String agrupador) {
        this.codigoManager = codigoManager;
        this.agrupador = agrupador;
    }

    public RemoverRegistroTemporario(CodigoManager codigoManager, String agrupador, String campo) {
        this.codigoManager = codigoManager;
        this.agrupador = agrupador;
        this.campo = campo;
    }

    public RemoverRegistroTemporario(CodigoManager codigoManager, String agrupador, String campo, Long codigoTempStore) {
        this.codigoManager = codigoManager;
        this.agrupador = agrupador;
        this.campo = campo;
        this.codigoTempStore = codigoTempStore;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        TempStore on = on(TempStore.class);
        
        Criteria createCriteria = getSession().createCriteria(TempStore.class);
        
        createCriteria.add(Restrictions.eq(path(on.getCodigoTabelaReferencia()), codigoManager.getCodigoManager()));
        createCriteria.add(Restrictions.eq(path(on.getTabelaReferencia()), codigoManager.getClass().getSimpleName()));
        
        if(codigoTempStore != null){
            createCriteria.add(Restrictions.eq(path(on.getCodigo()), codigoTempStore));            
        }
        
        if (campo!=null) {
            createCriteria.add(Restrictions.eq(path(on.getCampo()), campo));
        }

        if (agrupador!=null) {
            createCriteria.add(Restrictions.eq(path(on.getAgrupador()), agrupador));
        }
        
        List<TempStore> temps = createCriteria.list();
        
        for (TempStore tempStore : temps) {
            BOFactory.delete(tempStore);
        }
        
    }

}
