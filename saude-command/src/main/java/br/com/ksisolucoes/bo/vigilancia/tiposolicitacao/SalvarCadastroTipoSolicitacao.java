package br.com.ksisolucoes.bo.vigilancia.tiposolicitacao;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.FiscalNaRuaUtil;
import br.com.celk.vigilancia.fiscalnarua.SqsSincronizadorFiscalRuaDto;
import br.com.celk.vigilancia.fiscalnarua.TipoMensagemSqsFiscalRua;
import br.com.celk.vigilancia.fiscalnarua.TipoSolicitacaoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroTipoSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo;
import ch.lambdaj.Lambda;

/**
 * <AUTHOR>
 */
public class SalvarCadastroTipoSolicitacao extends AbstractCommandTransaction<SalvarCadastroTipoSolicitacao> {

    private final CadastroTipoSolicitacaoDTO dto;
    private TipoSolicitacao tipoSolicitacao;
    private TipoSolicitacao tsBeforeSave;
    private Usuario usuarioExecucao;

    public SalvarCadastroTipoSolicitacao(CadastroTipoSolicitacaoDTO dto) {
        this.dto = dto;
        this.usuarioExecucao = dto.getUsuarioExecucao();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getTipoSolicitacao().getCodigo() != null) {
            tsBeforeSave = LoadManager.getInstance(TipoSolicitacao.class)
                    .addProperties(new HQLProperties(TipoSolicitacao.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoSolicitacao.PROP_CODIGO, dto.getTipoSolicitacao().getCodigo()))
                    .setMaxResults(1).start().getVO();
        }

        if (dto.getTipoSolicitacao().getFlagEnviarAppFiscalRua() == null) {
            dto.getTipoSolicitacao().setFlagEnviarAppFiscalRua(RepositoryComponentDefault.NAO_LONG);
        }

        tipoSolicitacao = BOFactory.getBO(CadastroFacade.class).save(dto.getTipoSolicitacao());
        dto.setTipoSolicitacao(tipoSolicitacao);

        if (CollectionUtils.isNotNullEmpty(dto.getTipoSolicitacaoAnexoList())) {
            Lambda.forEach(dto.getTipoSolicitacaoAnexoList()).setTipoSolicitacao(tipoSolicitacao);
        }

        VOUtils.persistirListaVosModificados(TipoSolicitacaoAnexo.class, dto.getTipoSolicitacaoAnexoList(),
                new QueryCustom.QueryCustomParameter(TipoSolicitacaoAnexo.PROP_TIPO_SOLICITACAO, tipoSolicitacao));


        integrarFiscalNaRua();
    }

    private void integrarFiscalNaRua() throws DAOException, ValidacaoException {
        if (FiscalNaRuaUtil.habilitaAplicativoFiscalNaRua()) {
            if (tsBeforeSave == null &&
                    RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getAtivo()) &&
                    RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getFlagEnviarAppFiscalRua())
            ) {
                BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(new TipoSolicitacaoDTO(this.tipoSolicitacao)), TipoMensagemSqsFiscalRua.TIPO_SOLICITACAO);

            } else {
                if (RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getAtivo()) &&
                        RepositoryComponentDefault.SIM_LONG.equals(tipoSolicitacao.getFlagEnviarAppFiscalRua())) {

                    if (tsBeforeSave != null && RepositoryComponentDefault.NAO_LONG.equals(tsBeforeSave.getFlagEnviarAppFiscalRua())) {
                        BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(new TipoSolicitacaoDTO(this.tipoSolicitacao)), TipoMensagemSqsFiscalRua.TIPO_SOLICITACAO);
                        BOFactory.getBO(VigilanciaFacade.class).reenviarRequerimentosTipoSolicitacaoFiscalRua(tipoSolicitacao, usuarioExecucao);
                    }

                    if (!tsBeforeSave.getDescricao().equalsIgnoreCase(tipoSolicitacao.getDescricao()) ||
                            !tsBeforeSave.getTipoRequerimento().equals(tipoSolicitacao.getTipoRequerimento())
                    ) {
                        BOFactory.getBO(VigilanciaFacade.class).enviarMensagemSqsSincronizadorFiscalRua(new SqsSincronizadorFiscalRuaDto(new TipoSolicitacaoDTO(this.tipoSolicitacao)), TipoMensagemSqsFiscalRua.TIPO_SOLICITACAO);
                    }
                }
            }
        }
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }
}
