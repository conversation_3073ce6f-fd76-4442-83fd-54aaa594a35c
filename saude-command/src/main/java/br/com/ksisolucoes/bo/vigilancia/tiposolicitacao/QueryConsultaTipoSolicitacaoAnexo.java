package br.com.ksisolucoes.bo.vigilancia.tiposolicitacao;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacaoAnexo;
import org.hibernate.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaTipoSolicitacaoAnexo extends CommandQuery<QueryConsultaTipoSolicitacaoAnexo> {

    private List<TipoSolicitacaoAnexo> result;
    private TipoSolicitacao tipoSolicitacao;
    private List<AtividadeEstabelecimento> atividadeEstabelecimentoList = new ArrayList<>();
    private boolean somenteObrigatorios;

    public QueryConsultaTipoSolicitacaoAnexo(TipoSolicitacao tipoSolicitacao, List<EstabelecimentoAtividade> estabelecimentoAtividadeList, boolean somenteObrigatorios) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.somenteObrigatorios = somenteObrigatorios;
        decomporListaEstabelecimentoAtividade(estabelecimentoAtividadeList);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(TipoSolicitacaoAnexo.class.getName());

        hql.addToSelect("tipoSolicitacaoAnexo.codigo", "codigo");
        hql.addToSelect("tipoSolicitacaoAnexo.descricao", "descricao");
        hql.addToSelect("tipoSolicitacaoAnexo.obrigatorio", "obrigatorio");
        hql.addToSelect("tipoSolicitacaoAnexo.flagPermiteRepetir", "flagPermiteRepetir");
        hql.addToSelect("tipoSolicitacaoAnexo.tipoDocumento", "tipoDocumento");
        hql.addToSelect("tipoSolicitacaoAnexo.observacao", "observacao");
        hql.addToSelect("tipoSolicitacaoAnexo.regiaoAbastecidaAgua", "regiaoAbastecidaAgua");
        hql.addToSelect("tipoSolicitacaoAnexo.regiaoCobertaRedeEsgoto", "regiaoCobertaRedeEsgoto");
        hql.addToSelect("tipoSolicitacaoAnexo.usoEdificacao", "usoEdificacao");

        hql.addToSelect("tipoSolicitacao.codigo", "tipoSolicitacao.codigo");
        hql.addToSelect("tipoSolicitacao.descricao", "tipoSolicitacao.descricao");
        hql.addToSelect("tipoSolicitacao.tipoDocumento", "tipoSolicitacao.tipoDocumento");
        hql.addToSelect("tipoSolicitacao.tipoRequerimento", "tipoSolicitacao.tipoRequerimento");
        hql.addToSelect("tipoSolicitacao.tipoRequerimento", "tipoSolicitacao.tipoRequerimento");

        hql.addToSelect("atividadeEstabelecimento.codigo", "atividadeEstabelecimento.codigo");
        hql.addToSelect("atividadeEstabelecimento.descricao", "atividadeEstabelecimento.descricao");

        hql.addToFrom("TipoSolicitacaoAnexo tipoSolicitacaoAnexo"
                + " left join tipoSolicitacaoAnexo.tipoSolicitacao tipoSolicitacao"
                + " left join tipoSolicitacaoAnexo.atividadeEstabelecimento atividadeEstabelecimento"
        );

        hql.addToWhereWhithAnd("tipoSolicitacaoAnexo.tipoSolicitacao = ", tipoSolicitacao);
        hql.addToWhereWhithAnd("tipoSolicitacaoAnexo.tipoDocumento = ", tipoSolicitacao.getTipoDocumento());
        if (CollectionUtils.isNotNullEmpty(atividadeEstabelecimentoList)) {
            hql.addToWhereWhithAnd("(atividadeEstabelecimento in :atividadeEstabelecimentoList or atividadeEstabelecimento is null)");
        }

        if(somenteObrigatorios) {
            hql.addToWhereWhithAnd("tipoSolicitacaoAnexo.obrigatorio = ", RepositoryComponentDefault.SIM_LONG);
        }

        hql.addToOrder("tipoSolicitacaoAnexo.descricao asc");
    }

    public List<TipoSolicitacaoAnexo> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(atividadeEstabelecimentoList)) {
            query.setParameterList("atividadeEstabelecimentoList", atividadeEstabelecimentoList);
        }
    }

    private void decomporListaEstabelecimentoAtividade(List<EstabelecimentoAtividade> estabelecimentoAtividadeList) {
        if (CollectionUtils.isNotNullEmpty(estabelecimentoAtividadeList)) {
            for (EstabelecimentoAtividade estabelecimentoAtividade : estabelecimentoAtividadeList) {
                atividadeEstabelecimentoList.add(estabelecimentoAtividade.getAtividadeEstabelecimento());
            }
        }
    }
}
