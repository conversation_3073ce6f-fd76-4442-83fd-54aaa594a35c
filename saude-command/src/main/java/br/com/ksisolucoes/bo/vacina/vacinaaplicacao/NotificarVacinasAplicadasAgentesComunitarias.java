package br.com.ksisolucoes.bo.vacina.vacinaaplicacao;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vacina.RegistroVacina;
import java.util.Arrays;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class NotificarVacinasAplicadasAgentesComunitarias extends AbstractCommandTransaction {

    public NotificarVacinasAplicadasAgentesComunitarias() {
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long count =  (Long) getSession().createCriteria(RegistroVacina.class)
            .setProjection(Projections.count(RegistroVacina.PROP_CODIGO))
            .add(Restrictions.eq(RegistroVacina.PROP_SITUACAO, RegistroVacina.Situacao.PENDENTE.value()))
            .uniqueResult();
        
        if(Coalesce.asLong(count) > 0){
            Usuario usuario = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VACINAS).getParametro("usuarioVacinasACS");

            MensagemDTO mensagemDTO = new MensagemDTO();
            mensagemDTO.setAssunto(Bundle.getStringApplication("msg_existem_vacinas_serem_confirmadas"));
            mensagemDTO.setUsuarios(Arrays.asList(usuario));
            mensagemDTO.setMensagem("O sistema encontrou vacinas aplicadas pelas agentes comunitárias que devem ser avaliadas e confirmadas na tela de Confirmação da Carteira de Vacinação.");

            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        }
    }
}