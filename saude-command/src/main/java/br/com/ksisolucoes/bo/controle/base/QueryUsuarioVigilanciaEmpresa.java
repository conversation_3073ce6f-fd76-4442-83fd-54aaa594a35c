/*
 * QueryUsuarioEmpresa.java
 *
 * Created on 07 de Novembro de 2005,13:52
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */

package br.com.ksisolucoes.bo.controle.base;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Graciano
 */
public class QueryUsuarioVigilanciaEmpresa extends CommandQuery<QueryUsuarioVigilanciaEmpresa> {

    private EmpresasUsuarioDTO dto;
    private List<Empresa> empresas = new ArrayList<Empresa>();

    public QueryUsuarioVigilanciaEmpresa(EmpresasUsuarioDTO dto) {
        this.dto = dto;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        LoadManager load = LoadManager.getInstance(UsuarioEmpresa.class);
        if (CollectionUtils.isNotNullEmpty(dto.getTiposEstabelecimento())) {
            load.addParameter(new QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_TIPO_UNIDADE), BuilderQueryCustom.QueryParameter.IN, dto.getTiposEstabelecimento()));
        }
        List<UsuarioEmpresa> usuarioEmpresaList = load
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO, Usuario.PROP_CODIGO), dto.getUsuario().getCodigo()))
                .addProperties(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperties(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_REFERENCIA))
                .addProperties(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                .addSorter(new QueryCustomSorter(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_DESCRICAO)))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(usuarioEmpresaList)) {
            for (UsuarioEmpresa usuarioEmpresa : usuarioEmpresaList) {
                empresas.add(usuarioEmpresa.getEmpresa());
            }
        }
    }

    public List<Empresa> getEmpresas() {
        return this.empresas;
    }
}
