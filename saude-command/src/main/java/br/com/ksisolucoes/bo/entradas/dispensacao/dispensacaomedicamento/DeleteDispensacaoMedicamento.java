package br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamento;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamentoitem.DeleteDispensacaoMedicamentoItem;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public class DeleteDispensacaoMedicamento extends DeleteVO {
    
    private static final long serialVersionUID = 1L;
    private DispensacaoMedicamento dispensacaoMedicamento ;
    
    public DeleteDispensacaoMedicamento(Object vo) {
        super( vo );
        this.dispensacaoMedicamento = (DispensacaoMedicamento)vo;
    }
    
	/**
	 * {@inheritDoc}
	 */
	
        
    public void antesDelete() throws ValidacaoException, DAOException{       
        throw new UnsupportedOperationException( "No  permitido excluso de DispensacaoMedicamento" );
//        this.excluirItens();
    }
    
    /**Excluir os itens da dispensacao medicamento
     */
    public void excluirItens() throws ValidacaoException, DAOException{
        //Excluindo ItensFaturaProforma
        this.dispensacaoMedicamento.setItensDispensacaoMedicamentoSet(null);
        Set<DispensacaoMedicamentoItem> setItens = this.dispensacaoMedicamento.getItensDispensacaoMedicamentoSet();
        if(setItens != null){
            for( DispensacaoMedicamentoItem item  : setItens ){
                new DeleteDispensacaoMedicamentoItem( item ).start();
            }
        }
    }
}