/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
class QueryGrupoEstoqueProduto extends CommandQuery<QueryGrupoEstoqueProduto> {

    private Empresa empresa;

    private Produto produto;

    private Long codigoDeposito;

    private List<GrupoEstoque> grupoEstoqueList;

    QueryGrupoEstoqueProduto(Empresa empresa, Produto produto) {
        this(empresa, produto, null);
    }

    public QueryGrupoEstoqueProduto(Empresa empresa, Produto produto, Long codigoDeposito) {
        this.empresa = empresa;
        this.produto = produto;
        this.codigoDeposito = codigoDeposito;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("ge.id.estoqueEmpresa.id.empresa.codigo", "id.estoqueEmpresa.id.empresa.codigo");
        hql.addToSelect("ge.id.estoqueEmpresa.id.empresa.referencia", "id.estoqueEmpresa.id.empresa.referencia");
        hql.addToSelect("ge.id.estoqueEmpresa.id.empresa.descricao", "id.estoqueEmpresa.id.empresa.descricao");
        hql.addToSelect("ge.id.estoqueEmpresa.id.produto.codigo", "id.estoqueEmpresa.id.produto.codigo");
        hql.addToSelect("ge.id.estoqueEmpresa.id.produto.referencia", "id.estoqueEmpresa.id.produto.referencia");
        hql.addToSelect("ge.id.estoqueEmpresa.id.produto.descricao", "id.estoqueEmpresa.id.produto.descricao");
        hql.addToSelect("ge.id.codigoDeposito", "id.codigoDeposito");
        hql.addToSelect("ge.id.localizacaoEstrutura.codigo", "id.localizacaoEstrutura.codigo");
        hql.addToSelect("ge.id.localizacaoEstrutura.mascara", "id.localizacaoEstrutura.mascara");
        hql.addToSelect("ge.id.localizacaoEstrutura.descricaoEstrutura", "id.localizacaoEstrutura.descricaoEstrutura");
        hql.addToSelect("ge.id.localizacaoEstrutura.localizacao.codigo", "id.localizacaoEstrutura.localizacao.codigo");
        hql.addToSelect("ge.id.localizacaoEstrutura.localizacao.descricao", "id.localizacaoEstrutura.localizacao.descricao");
        hql.addToSelect("ge.id.grupo", "id.grupo");
        hql.addToSelect("ge.estoqueFisico","estoqueFisico");
        hql.addToSelect("ge.estoqueReservado","estoqueReservado");
        hql.addToSelect("ge.estoqueDevolucao","estoqueDevolucao");
        hql.addToSelect("ge.estoqueReservadoDevolucao","estoqueReservadoDevolucao");
        hql.addToSelect("ge.estoqueEncomendado","estoqueEncomendado");
        hql.addToSelect("ge.estoqueNaoConforme","estoqueNaoConforme");
        hql.addToSelect("ge.numeroUltimoMovimento","numeroUltimoMovimento");
        hql.addToSelect("ge.dataValidade","dataValidade");

        hql.addToFrom(GrupoEstoque.class.getName(), "ge");
        hql.setTypeSelect(GrupoEstoque.class.getName());

        hql.addToWhereWhithAnd("ge.id.estoqueEmpresa.id.empresa.codigo = ", this.empresa.getCodigo());
        hql.addToWhereWhithAnd("ge.id.estoqueEmpresa.id.produto.codigo = ", this.produto.getCodigo(), HQLHelper.RESOLVE_CHAR_TYPE);
        hql.addToWhereWhithAnd("ge.id.codigoDeposito = ", this.codigoDeposito);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.grupoEstoqueList = hql.getBeanList((List)result);
    }

    public List<GrupoEstoque> getGrupoEstoqueList() {
        return grupoEstoqueList;
    }
}
