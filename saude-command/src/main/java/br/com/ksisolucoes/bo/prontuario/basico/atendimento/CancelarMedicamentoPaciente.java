package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.MedicamentoPaciente;

/**
 *
 * <AUTHOR>
 */
public class CancelarMedicamentoPaciente extends AbstractCommandTransaction{
    
    private MedicamentoPaciente medicamentoPaciente;

    public CancelarMedicamentoPaciente(MedicamentoPaciente medicamentoPaciente) {
        this.medicamentoPaciente = medicamentoPaciente;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        medicamentoPaciente = HibernateUtil.rechargeVO(MedicamentoPaciente.class, medicamentoPaciente.getCodigo(), medicamentoPaciente.getVersion());
        
        medicamentoPaciente.setStatus(MedicamentoPaciente.Status.CANCELADO.value());
        medicamentoPaciente.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());
        medicamentoPaciente.setDataCancelamento(DataUtil.getDataAtual());
        
        BOFactory.save(medicamentoPaciente);
    }
    
}
