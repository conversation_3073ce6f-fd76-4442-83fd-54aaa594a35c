package br.com.ksisolucoes.bo.agendamento.regulacao;

import br.com.ksisolucoes.agendamento.regulacao.dto.RegistrarAgendamentoRequest;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.integracao.branet.IntegracaoProfissionalBranet;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.hibernate.Query;

import java.util.*;

import static ch.lambdaj.Lambda.*;

public class QuebrarSolicitacaoAgendamentoWQR extends AbstractCommandTransaction<QuebrarSolicitacaoAgendamentoWQR> {

    private final RegistrarAgendamentoRequest agendamentoRequest;
    private Long solicitacaoId;

    public QuebrarSolicitacaoAgendamentoWQR(RegistrarAgendamentoRequest agendamentoRequest, Long solicitacaoId) {
        this.agendamentoRequest = agendamentoRequest;
        this.solicitacaoId = solicitacaoId;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        SolicitacaoAgendamento solicitacaoAgendamento = getSolicitacaoAgendamento(solicitacaoId);
        List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame = LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoAgendamentoExame.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO), solicitacaoAgendamento.getCodigo()))
                .start().getList();
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExame)) {
            List<ExameProcedimento> extractExameProcedimentos = extract(lstSolicitacaoAgendamentoExame, on(SolicitacaoAgendamentoExame.class).getExameProcedimento());
            if (agendamentoRequest.getEhSolicitacaoQuebrada()) {
                SolicitacaoAgendamento solicitacaoAgendamentoPai = getSolicitacaoAgendamento(agendamentoRequest.getSolicitacaoAgendamentoID());
                solicitacaoAgendamento.setSolicitacaoAgendamentoPai(solicitacaoAgendamento);
                solicitacaoAgendamento = VOUtils.cloneObject(solicitacaoAgendamento);
                solicitacaoAgendamento = BOFactory.save(solicitacaoAgendamento);
                solicitacaoId = solicitacaoAgendamento.getCodigo();
                updateSolicitacao(solicitacaoAgendamentoPai, solicitacaoAgendamento);
                gerarOcorrencia(solicitacaoAgendamento, "rotulo_solicitacao_agendamento_filha", solicitacaoAgendamentoPai.getCodigo());
                gerarOcorrencia(solicitacaoAgendamentoPai, "rotulo_solicitacao_agendamento_pai", solicitacaoAgendamento.getCodigo());
            }
        }
    }

    private void updateSolicitacao(SolicitacaoAgendamento pai, SolicitacaoAgendamento filha) {
        ArrayList<String> ids = new ArrayList<>();
        for (Long id: agendamentoRequest.getExamesNaoAgendados()) {
            ids.add(id.toString());
        }
        String examesNaoAgendados = String.join(",", ids);
        // a solicitação filha é que é agendada; logo, todos os exames agendados devem ser passados da pai para a filha
        String updateSolicitacao = "update SolicitacaoAgendamentoExame as sae set sae.solicitacaoAgendamento.codigo = :codigoFilha " +
                "where sae.solicitacaoAgendamento.codigo = :codigoPai and sae.codigo not in (" + examesNaoAgendados + ")";
        Query query = getSession().createQuery(updateSolicitacao);
        query.setParameter("codigoFilha", filha.getCodigo());
        query.setParameter("codigoPai", pai.getCodigo());
        query.executeUpdate();
    }

    private void gerarOcorrencia(SolicitacaoAgendamento solicitacaoAgendamento, String mensagem, Long codigoSolicitacao) throws ValidacaoException, DAOException {
        String descricao = Bundle.getStringApplication(mensagem, codigoSolicitacao);
        SolicitacaoAgendamentoOcorrencia.TipoOcorrencia tipoOcorrencia = SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO;
        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(tipoOcorrencia, descricao, solicitacaoAgendamento);
    }

    private SolicitacaoAgendamento getSolicitacaoAgendamento(Long solicitacaoAgendamentoID) {
        return LoadManager.getInstance(SolicitacaoAgendamento.class)
                .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SITUACAO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SEXO))
                .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamento.PROP_CODIGO, solicitacaoAgendamentoID))
                .start().getVO();
    }

    public Long getSolicitacaoId() {
        return solicitacaoId;
    }
}
