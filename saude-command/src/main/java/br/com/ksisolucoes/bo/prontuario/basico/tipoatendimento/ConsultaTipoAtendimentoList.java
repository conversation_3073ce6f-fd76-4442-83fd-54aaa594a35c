/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.basico.tipoatendimento;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHorariaHelper;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import static ch.lambdaj.Lambda.on;
import java.util.List;

/**
 * <AUTHOR>
 * Criado em: Jun 13, 2013
 */
public class ConsultaTipoAtendimentoList extends CommandQuery{

    private List<TipoAtendimento> tipoAtendimentoList;
    private AtendimentoWebDTOParam param;

    public ConsultaTipoAtendimentoList(AtendimentoWebDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException{
        hql.setTypeSelect(TipoAtendimento.class.getName());
        
        hql.addToSelectAndGroup("npta.tipoAtendimento.codigo", path(on(TipoAtendimento.class).getCodigo()));
        hql.addToSelectAndGroup("npta.tipoAtendimento.descricao", path(on(TipoAtendimento.class).getDescricao()));
        
        if(param.getEmpresa() != null){
            hql.addToWhereWhithAnd("enpta.empresa = ", param.getEmpresa());
        }else{
            List<Long> empresas = null;
            if(param.getUsuario() != null && RepositoryComponentDefault.NAO.equals(param.getUsuario().getFlagIdentificavel())){
                empresas = BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(param.getUsuario());
            }else if (getSessao()!=null) {
                empresas = BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(getSessao().<Usuario>getUsuario());
            }

            if (empresas!=null) {
                hql.addToWhereWhithAnd("enpta.empresa.codigo in ", empresas);
            }
        }

        hql.addToFrom("EmpresaNaturezaProcuraTipoAtendimento enpta "
                + "join enpta.naturezaProcuraTipoAtendimento npta");

        if(param.getUsuario() != null){
            if(param.getUsuario().getProfissional() != null){
                hql.addToFrom("EloGrupoAtendimentoCbo eloGrupoAtendimentoCbo");
                hql.addToFrom("EloTipoAtendimentoGrupoAtendimentoCbo eloTipoAtendimentoGrupoAtendimentoCbo");
                hql.addToFrom("ProfissionalCargaHoraria profissionalCargaHoraria");

                hql.addToWhereWhithAnd("eloGrupoAtendimentoCbo.grupoAtendimentoCbo = eloTipoAtendimentoGrupoAtendimentoCbo.grupoAtendimentoCbo");
                hql.addToWhereWhithAnd("eloTipoAtendimentoGrupoAtendimentoCbo.tipoAtendimento = npta.tipoAtendimento");
                hql.addToWhereWhithAnd("eloGrupoAtendimentoCbo.tabelaCbo = profissionalCargaHoraria.tabelaCbo");

                hql.addToWhereWhithAnd(new ProfissionalCargaHorariaHelper().getWhereValidaCargaHoraria("profissionalCargaHoraria", null, param.getUsuario().getProfissional().getCodigo(),null, "enpta.empresa.codigo",true));
            }else{
                if(RepositoryComponentDefault.NAO.equals(param.getUsuario().getFlagIdentificavel())){
                    hql.addToFrom("EloGrupoAtendimentoCboUsuario eloGrupoAtendimentoCboUsuario");
                    hql.addToFrom("EloTipoAtendimentoGrupoAtendimentoCbo eloTipoAtendimentoGrupoAtendimentoCbo");

                    hql.addToWhereWhithAnd("eloGrupoAtendimentoCboUsuario.grupoAtendimentoCbo = eloTipoAtendimentoGrupoAtendimentoCbo.grupoAtendimentoCbo");
                    hql.addToWhereWhithAnd("eloTipoAtendimentoGrupoAtendimentoCbo.tipoAtendimento = npta.tipoAtendimento");
                    hql.addToWhereWhithAnd("eloGrupoAtendimentoCboUsuario.usuario = ", param.getUsuario());
                }
            }
        }        
        
        hql.addToOrder("npta.tipoAtendimento.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        tipoAtendimentoList = hql.getBeanList((List)result);
    }

    public List<TipoAtendimento> getTipoAtendimentoList() {
        return tipoAtendimentoList;
    }
}
