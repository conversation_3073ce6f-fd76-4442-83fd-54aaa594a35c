package br.com.ksisolucoes.bo.vacina.itempedidoinsumo;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.insumo.ItemPedidoInsumo;

/**
 *
 * <AUTHOR>
 */
public class CadastrarItemPedidoInsumo extends AbstractCommandTransaction {

    private ItemPedidoInsumo itemPedidoInsumo;

    public CadastrarItemPedidoInsumo(ItemPedidoInsumo itemPedidoVacina) {
        this.itemPedidoInsumo = itemPedidoVacina;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (itemPedidoInsumo.getCodigo()==null) {
            itemPedidoInsumo.setDataCadastro(Data.getDataAtual());
            itemPedidoInsumo.setStatus(ItemPedidoInsumo.StatusItemPedidoInsumo.ABERTO.value());
        }
        itemPedidoInsumo.setUsuario(getSessao().<Usuario>getUsuario());
        itemPedidoInsumo.setDataUsuario(Data.getDataAtual());
        
        BOFactory.save(itemPedidoInsumo);
    }

}
