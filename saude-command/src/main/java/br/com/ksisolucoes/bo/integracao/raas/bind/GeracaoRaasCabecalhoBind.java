package br.com.ksisolucoes.bo.integracao.raas.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.bo.integracao.raas.dto.GeracaoRaasDTO;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 159, crlf = "UNIX")
public class GeracaoRaasCabecalhoBind implements IBindVoExport<GeracaoRaasDTO> {
    
    @DataField(pos = 1, length = 2, paddingChar = '0', align = "R", required = true)
    private Long codigoLinha;
    @DataField(pos = 2, length = 5, align = "L", required = true)
    private String indicadorInicioCabecalho;
    @DataField(pos = 3, length = 6, align = "R", pattern = "yyyyMM")
    private Date anoMesMovimento;
    @DataField(pos = 4, length = 6, paddingChar = '0', align = "R", required = true)
    private Long quantidadeFolhas;
    @DataField(pos = 5, length = 4, align = "R", required = true)
    private Long controleDominio;
    @DataField(pos = 6, length = 30, align = "L", required = true)
    private String nomeOrgaoOrigemResponsavel;
    @DataField(pos = 7, length = 6, align = "L", required = true)
    private String siglaOrgaoOrigemResponsavel;
    @DataField(pos = 8, length = 14, paddingChar = '0', align = "R", required = true)
    private Long cgcPrestadorOrgao;
    @DataField(pos = 9, length = 40, align = "L", required = true)
    private String nomeOrgaoDestino;
    @DataField(pos = 10, length = 1, align = "L", required = true)
    private String indicadorOrgaoDestino;
    @DataField(pos = 11, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataGeracaoRemessa;
    @DataField(pos = 12, length = 15, align = "L", required = true)
    private String versao;
    @DataField(pos = 13, length = 7, align = "L", required = true)
    private String versaoBdsia;
    @DataField(pos = 14, length = 15, align = "L", required = true)
    private String fimCabecalho;

    @Override
    public void buildProperties(GeracaoRaasDTO vo) {
        codigoLinha = vo.getRaas().getLinha();
        indicadorInicioCabecalho = vo.getRaas().getIndicadorInicio();
        anoMesMovimento = vo.getRaas().getCompetencia();
        quantidadeFolhas = vo.getRaas().getQuantidadeFolhas();
        controleDominio = vo.getRaas().getCampoControle();
        nomeOrgaoOrigemResponsavel = vo.getRaas().getNomeOrgaoOrigem();
        siglaOrgaoOrigemResponsavel = vo.getRaas().getSiglaOrgaoOrigem();
        cgcPrestadorOrgao = vo.getRaas().getCgcPrestador();
        nomeOrgaoDestino = vo.getRaas().getNomeOrgaoDestino();
        indicadorOrgaoDestino = vo.getRaas().getIndicadorOrgaoDestino();
        dataGeracaoRemessa = vo.getRaas().getDataGeracao();
        versao = vo.getRaas().getVersao();
        versaoBdsia = vo.getRaas().getVersaoBdsia();
//        fimCabecalho = System.lineSeparator();
    }
    
}
