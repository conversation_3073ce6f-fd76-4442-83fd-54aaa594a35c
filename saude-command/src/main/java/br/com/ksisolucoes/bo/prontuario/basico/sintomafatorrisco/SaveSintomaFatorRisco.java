package br.com.ksisolucoes.bo.prontuario.basico.sintomafatorrisco;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Bairro;
import br.com.ksisolucoes.vo.prontuario.basico.SintomaFatorRisco;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoPrioridade;

/**
 *
 * <AUTHOR>
 */
public class SaveSintomaFatorRisco extends SaveVO<SintomaFatorRisco> {

    public SaveSintomaFatorRisco(SintomaFatorRisco vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(vo.getDataCadastro() == null){
            vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if(vo.getUsuario() == null) {
            vo.setUsuario(this.getSessao().getUsuario());
        }
    }

    public SintomaFatorRisco getSintomaFatorRisco() {
        return vo;
    }

}
