package br.com.ksisolucoes.bo.prontuario.historico;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.web.historico.dto.MedicamentosEmUsoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.MedicamentoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryMedicamentosEmUsoPaciente extends CommandQuery<QueryMedicamentosEmUsoPaciente> {

    private Long codigoPaciente;
    private TabelaCbo cboAtendimento;
    private List<MedicamentosEmUsoDTO> result;

    public QueryMedicamentosEmUsoPaciente(Long codigoPaciente, TabelaCbo cboAtendimento) {
        this.codigoPaciente = codigoPaciente;
        this.cboAtendimento = cboAtendimento;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("medicamentoPaciente.nomeProduto", "nomeProduto");
        hql.addToSelect("medicamentoPaciente.posologia", "posologia");
        hql.addToSelect("medicamentoPaciente.codigo", "codigoMedicamentoPaciente");
        hql.addToSelect("medicamentoPaciente.quantidadePrescrita", "quantidadePrescrita");
        hql.addToSelect("cid.codigo", "cid.codigo");

        hql.addToSelect("tipoReceita.tipoReceita", "tipoReceitaSelecionada");
        hql.addToSelect("tipoReceita.descricao", "descricaoTipoReceita");

        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("produto.formaFarmaceutica", "produto.formaFarmaceutica");
        hql.addToSelect("produto.flagAtivo", "produto.flagAtivo");

        hql.addToSelect("unidade.unidade", "produto.unidade.unidade");

        hql.setTypeSelect(MedicamentosEmUsoDTO.class.getName());

        StringBuilder from = new StringBuilder("MedicamentoPaciente medicamentoPaciente");
        from.append(" left join medicamentoPaciente.usuarioCadsus usuarioCadsus");
        from.append(" left join medicamentoPaciente.cid cid");
        from.append(" left join medicamentoPaciente.tipoReceita tipoReceita");
        from.append(" left join medicamentoPaciente.produto produto");
        from.append(" left join produto.unidade unidade");
        hql.addToFrom(from.toString());

        if (cboAtendimento != null) {
            StringBuilder eloGrupoAtendimentoCboFrom = new StringBuilder("EloGrupoAtendimentoCbo eloGrupoAtendimentoCbo");
            eloGrupoAtendimentoCboFrom.append(" left join eloGrupoAtendimentoCbo.grupoAtendimentoCbo grupoAtendimentoCbo");
            eloGrupoAtendimentoCboFrom.append(" left join eloGrupoAtendimentoCbo.tabelaCbo cboGrupo");
            hql.addToFrom(eloGrupoAtendimentoCboFrom.toString());

            hql.addToWhereWhithAnd("cboGrupo.cbo = :cboAtendimento");

            StringBuilder whereGrupoAtendimentoCbo = new StringBuilder("(grupoAtendimentoCbo.filtrarMedicamentos = :NAO ");
            whereGrupoAtendimentoCbo.append(" or grupoAtendimentoCbo.codigo in (");
            whereGrupoAtendimentoCbo.append(" select gac2.codigo from ProdutoGrupoAtendimento pga");
            whereGrupoAtendimentoCbo.append(" left join pga.grupoAtendimentoCbo gac2");
            whereGrupoAtendimentoCbo.append(" left join pga.produto produto2 ");
            whereGrupoAtendimentoCbo.append(" where produto2 = medicamentoPaciente.produto )");
            whereGrupoAtendimentoCbo.append(" )");
            hql.addToWhereWhithAnd(whereGrupoAtendimentoCbo.toString());
        }

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", codigoPaciente);
        hql.addToWhereWhithAnd("medicamentoPaciente.status != ", MedicamentoPaciente.Status.CANCELADO.value());
        hql.addToWhereWhithAnd("tipoReceita.tipoReceita not in ", Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS));


        hql.addToOrder("3 desc");
        hql.addToOrder("tipoReceita.descricao");
        hql.addToOrder("medicamentoPaciente.nomeProduto");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (cboAtendimento != null) {
            query.setParameter("NAO", RepositoryComponentDefault.NAO_LONG);
            query.setParameter("cboAtendimento", cboAtendimento.getCbo());
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        LoadManager lm;
        for (MedicamentosEmUsoDTO medicamentosEmUsoDTO : getResult()) {
            lm = LoadManager.getInstance(ReceituarioItem.class)
                    .addProperty(ReceituarioItem.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_DATA_DISPENSACAO))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_QUANTIDADE_DISPENSADA))
                    .addProperty(VOUtils.montarPath(ReceituarioItem.PROP_DISPENSACAO_MEDICAMENTO_ITEM, DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), codigoPaciente))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE, Receituario.Situacao.CANCELADO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA), BuilderQueryCustom.QueryParameter.NOT_IN, Arrays.asList(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, TipoReceita.RECEITA_SOLICITACAO_MATERIAIS)))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO), QueryCustom.QueryCustomSorter.DECRESCENTE));

            if(medicamentosEmUsoDTO.getProduto() != null && medicamentosEmUsoDTO.getProduto().getCodigo() != null){
                lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_PRODUTO), medicamentosEmUsoDTO.getProduto()));
            } else if(medicamentosEmUsoDTO.getNomeProduto() != null){
                lm.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ReceituarioItem.PROP_NOME_PRODUTO), medicamentosEmUsoDTO.getNomeProduto()));
            }

            ReceituarioItem receituarioItem = lm.setMaxResults(1).start().getVO();

            if (receituarioItem != null) {
                medicamentosEmUsoDTO.setDataUltimaReceita(receituarioItem.getReceituario().getDataCadastro());
                medicamentosEmUsoDTO.setDispensacaoMedicamentoItem(receituarioItem.getDispensacaoMedicamentoItem());
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<MedicamentosEmUsoDTO> getResult() {
         return result;
    }

}
