package br.com.ksisolucoes.bo.prontuario.hospital.itemcontapaciente;

import br.com.celk.faturamento.FechamentoContaHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;

/**
 *
 * <AUTHOR>
 */
public class SaveItemContaPaciente extends SaveVO<ItemContaPaciente> {

    public SaveItemContaPaciente(ItemContaPaciente vo) throws DAOException, ValidacaoException {
        super(vo);
        //BOFactory.save(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(ItemContaPaciente.Status.CANCELADO.value().equals(this.vo.getStatus())){
            FechamentoContaHelper.validarBpaGerado(this.vo.getContaPaciente());
        }

        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataUsuario() == null) {
            this.vo.setDataUsuario(DataUtil.getDataAtual());
        }

        if (this.vo.getEmpresaFaturamento() == null) {
            this.vo.setEmpresaFaturamento(vo.getContaPaciente().getEmpresa());
        }

        if (this.vo.getDataLancamento() == null) {
            this.vo.setDataLancamento(DataUtil.getDataAtual());
        }
        
        if(Coalesce.asDouble(this.vo.getPrecoTotal()) == 0D) {
            this.vo.setPrecoTotal(new Dinheiro(Coalesce.asDouble(this.vo.getPrecoUnitario())).multiplicar(Coalesce.asDouble(this.vo.getQuantidade())).doubleValue());
        }

        if (vo.getProcedimentoBpa() == null) {
            vo.setProcedimentoBpa(vo.getProcedimento());
        }

        if (vo.getFlagDengue() == null) {
            vo.setFlagDengue(RepositoryComponentDefault.NAO_LONG);
        }


    }
}
