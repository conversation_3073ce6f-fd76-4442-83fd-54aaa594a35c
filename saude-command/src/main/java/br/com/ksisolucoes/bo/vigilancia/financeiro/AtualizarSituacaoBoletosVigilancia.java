package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import ch.lambdaj.Lambda;

import java.util.List;

public class AtualizarSituacaoBoletosVigilancia extends AbstractCommandTransaction {

    @Override
    public void execute() throws DAOException, ValidacaoException {

        int first = 0;
        int limit = 100;
        int retorno;
        try {
            do {
                getSession().flush();
                getSession().clear();
                retorno = processar(first, limit);
                Loggable.log.info("Processando integração de Boletos WS... " + (first + retorno));
                first += limit;
            } while (retorno >= limit);
        } catch (Exception ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private int processar(int first, int limit) throws ValidacaoException, DAOException {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = BOFactory.getBO(VigilanciaFacade.class).queryConsultaBoletosIntegracaoWs(first, limit);
        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            List<Long> codigosFinanceiroList = Lambda.extract(vigilanciaFinanceiroList, Lambda.on(VigilanciaFinanceiro.class).getCodigo());

            BOFactory.getBO(VigilanciaFacade.class).processarAtualizacaoIntegracaoBoletoVigilancia(codigosFinanceiroList, "Boleto atualizado automaticamente através do processo agendado");
        }
        return vigilanciaFinanceiroList.size();
    }

}
