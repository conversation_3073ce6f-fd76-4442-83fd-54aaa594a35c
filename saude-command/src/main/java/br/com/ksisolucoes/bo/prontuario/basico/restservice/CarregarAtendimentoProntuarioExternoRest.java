package br.com.ksisolucoes.bo.prontuario.basico.restservice;

import br.com.celk.bo.service.rest.historicopaciente.AtendimentoProntuarioRestDTO;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.restservice.dto.GravarAtendimentoProntuarioExternoDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.integracao.exportacao.consultas.ConsultaCns;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao;
import br.com.ksisolucoes.vo.prontuario.integracao.IntegracaoAtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import ch.lambdaj.function.compare.ArgumentComparator;
import org.apache.commons.collections.ComparatorUtils;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregarAtendimentoProntuarioExternoRest extends AbstractCommandTransaction {

    private GravarAtendimentoProntuarioExternoDTOParam param;

    List<ConfiguracaoEmpresaIntegracao> lstConfiguracao;
    private Long cns;

    IntegracaoAtendimentoProntuario iap;

    public CarregarAtendimentoProntuarioExternoRest(GravarAtendimentoProntuarioExternoDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (param == null || param.getUsuarioCadsus() == null) {
            return;
        }

        carregarCNS();

        if (cns == null) {
            return;
        }

        carregarConfiguracao();

        for (ConfiguracaoEmpresaIntegracao cei : lstConfiguracao) {
            Client client = ClientBuilder.newClient();
            String urlEmpresa;
            urlEmpresa = cei.getUrl();
            urlEmpresa += montarURL(cei.getEmpresa().getCodigo());

            Long data = new Date().getTime();

            Response response = client.target(urlEmpresa)
                    .request(MediaType.APPLICATION_JSON)
                    .header("id", cei.getId())
                    .header("data", data)
                    .header("hash", Util.criptografarSenha(cei.getId() + data + cei.getChave()))
                    .buildGet().invoke();

            if (Response.Status.NO_CONTENT.equals(Response.Status.fromStatusCode(response.getStatus()))) {
                Loggable.log.warn("Resposta: " + response.getStatus() + " - " + response.getStatusInfo().getReasonPhrase() +
                        " - Empresa : " + cei.getEmpresa().getDescricao());
            } else if (Response.Status.OK.equals(Response.Status.fromStatusCode(response.getStatus())) &&
                    MediaType.APPLICATION_JSON.equals(response.getMediaType().toString())) {
                List<AtendimentoProntuarioRestDTO> lstRetorno = response.readEntity(new GenericType<List<AtendimentoProntuarioRestDTO>>() {
                });

                if (!lstRetorno.isEmpty()) {
                    lstRetorno = Lambda.sort(lstRetorno, Lambda.on(AtendimentoProntuarioRestDTO.class), ComparatorUtils.reversedComparator(new ArgumentComparator(Lambda.on(AtendimentoProntuarioRestDTO.class).getCodigo())));
                }

                salvarIntegracaoAtendimentoProntuario(cei.getEmpresa(), cns, lstRetorno.get(0).getCodigo());

                for (AtendimentoProntuarioRestDTO restDTO : lstRetorno) {
                    AtendimentoProntuario ap = new AtendimentoProntuario();

                    ap.setData(restDTO.getData());
                    ap.setDescricao(restDTO.getDescricao());
                    ap.setTipoRegistro(restDTO.getTipoRegistro());
                    ap.setAcessoCompartilhado(restDTO.getAcessoCompartilhado());
                    ap.setNomeProfissional(restDTO.getNomeProfissional());
                    ap.setCnesExterno(restDTO.getCnesExterno());
                    ap.setDescricaoEmpresaOrigem(restDTO.getDescricaoEmpresaOrigem());
                    ap.setCodigoAtendimentoPrincipalExterno(restDTO.getCodigoAtendimentoPrincipal());
                    ap.setDataAtendimentoPrincipalExterno(restDTO.getDataAtendimentoPrincipal());
                    ap.setDescricaoTipoAtendimentoPrincipalExterno(restDTO.getDescricaoTipoAtendimentoPrincipal());

                    if (restDTO.getCbo() != null) {
                        TabelaCbo cbo = LoadManager.getInstance(TabelaCbo.class).setId(restDTO.getCbo()).start().getVO();
                        ap.setTabelaCbo(cbo);
                    }

                    ap.setEmpresa(cei.getEmpresa());
                    ap.setUsuarioCadsus(param.getUsuarioCadsus());
                    ap.setFlagOrigem(AtendimentoProntuario.Origem.EXTERNO.value());
                    BOFactory.save(ap);
                }
            } else {
                Loggable.log.warn("Erro: Retorno de status: " + response.getStatus() + " - Media Type: " + response.getMediaType() +
                        " - Erro na conexão do servidor da empresa : " + cei.getEmpresa().getDescricao());
            }
        }
    }

    public void carregarCNS() throws DAOException, ValidacaoException {

        ConsultaCns consultaCns = new ConsultaCns();
        consultaCns.setUsuarioCadsus(param.getUsuarioCadsus());
        consultaCns.start();

        List<UsuarioCadsusCns> lstCNS = consultaCns.getCnsList();
        List<Long> lst = new ArrayList<Long>();

        for (UsuarioCadsusCns c : lstCNS) {
            if (RepositoryComponentDefault.NAO_LONG.equals(c.getExcluido())) {
                lst.add(c.getNumeroCartao());
            }
        }

        Collections.sort(lst);
        Collections.reverse(lst);

        if (br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(lst)) {
            cns = lst.get(0);
        }
    }

    public void carregarConfiguracao() {
        lstConfiguracao = LoadManager.getInstance(ConfiguracaoEmpresaIntegracao.class)
                .start().getList();
    }

    public IntegracaoAtendimentoProntuario carregarVersao(Long empresa, Long cns) {
        iap = LoadManager.getInstance(IntegracaoAtendimentoProntuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoAtendimentoProntuario.PROP_EMPRESA, Empresa.PROP_CODIGO), empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(IntegracaoAtendimentoProntuario.PROP_CNS), cns))
                .start().getVO();
        if (iap == null) {
            iap = new IntegracaoAtendimentoProntuario();
            iap.setCodigoProntuario(0L);
        }
        return iap;
    }

    public String montarURL(Long codigoEmpresa) {
        String url = "";
        String revisao = Coalesce.asString(carregarVersao(codigoEmpresa, cns).getCodigoProntuario().toString(), "0");

        url = "/rest/atendimentoProntuario/1.0/000000";
        url += "/consultar/" + revisao + "/" + cns.toString();
        return url;
    }

    public void salvarIntegracaoAtendimentoProntuario(Empresa empresa, Long cns, Long codigoAtendimentoProntuario) throws DAOException, ValidacaoException {
        if (iap.getCodigo() == null) {
            iap = new IntegracaoAtendimentoProntuario(null, empresa, cns, codigoAtendimentoProntuario);
        } else {
            iap.setCodigoProntuario(codigoAtendimentoProntuario);
        }
        BOFactory.save(iap);
    }
}
