package br.com.ksisolucoes.bo.vigilancia.faturamento;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimentoProducao;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import org.hibernate.criterion.Restrictions;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
public class GerarFaturamentoAlvara extends AbstractCommandTransaction {

    private LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO;
    private Estabelecimento estabelecimento;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private Date dataFaturamento;

    public GerarFaturamentoAlvara(Estabelecimento estabelecimento, Date dataFaturamento) {
        this.estabelecimento = estabelecimento;
        this.dataFaturamento = dataFaturamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
//        lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();
//        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = gerarLancamentoAtividadeVigilancia();
//        configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
//
//        validaConfiguracaoVigilancia(configuracaoVigilancia);
//
//        List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = new ArrayList();
//
////        gerarAtividadePadrao(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList, configuracaoVigilancia);
//
//        geraAtividadesSecundarias(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList);
//
//        if (CollectionUtils.isEmpty(lancamentoAtividadesVigilanciaItemList)) {
//            return;
//        }
//        lancamentoAtividadesVigilanciaDTO.setFaturavel(true);
//        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
//        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilanciaItemList(lancamentoAtividadesVigilanciaItemList);
//        BOFactory.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
    }

//    private void gerarAtividadePadrao(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList, ConfiguracaoVigilancia atividadesVigilanciaEstabelecimentoCadastro) {
//        LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();
//        lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia((AtividadesVigilancia) getSession().get(AtividadesVigilancia.class, configuracaoVigilancia.getAtividadesVigilanciaLicenca().getCodigo()));
//        lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
//        lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
//        lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
//    }

    private void geraAtividadesSecundarias(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList) {
        List<EstabelecimentoAtividade> estabelecimentoAtividadeList = getSession().createCriteria(EstabelecimentoAtividade.class).add(Restrictions.eq(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, estabelecimento)).list();
        for (EstabelecimentoAtividade estabelecimentoAtividade : estabelecimentoAtividadeList) {
            List<AtividadeEstabelecimentoProducao> atividadeEstabelecimentoProducaoList = getAtividadeEstabelecimentoProducaoList(estabelecimentoAtividade);
            for (AtividadeEstabelecimentoProducao atividadeEstabelecimentoProducao : atividadeEstabelecimentoProducaoList) {
                LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();
                lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia(atividadeEstabelecimentoProducao.getAtividadesVigilancia());
                lancamentoAtividadesVigilanciaItem.setPontuacao(atividadeEstabelecimentoProducao.getAtividadesVigilancia().getPontuacao());
                lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
                lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
                lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
            }
        }
    }

    private List getAtividadeEstabelecimentoProducaoList(EstabelecimentoAtividade estabelecimentoAtividade) {
        return getSession().createCriteria(AtividadeEstabelecimentoProducao.class)
                .add(Restrictions.eq(AtividadeEstabelecimentoProducao.PROP_ATIVIDADE_ESTABELECIMENTO, estabelecimentoAtividade.getAtividadeEstabelecimento()))
                .add(Restrictions.eq(AtividadeEstabelecimentoProducao.PROP_TIPO_ATIVIDADE, AtividadeEstabelecimentoProducao.TipoAtividade.LICENCA.value()))
                .list();
    }

//    private boolean isProcedimentoInseridoAnteriormente(List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList, AtividadeEstabelecimentoProducao atividadeEstabelecimentoProducao) {
//        return atividadeEstabelecimentoProducao.getAtividadesVigilancia() != null
//                && atividadeEstabelecimentoProducao.getAtividadesVigilancia().getProcedimento() != null
//                && atividadeEstabelecimentoProducao.getAtividadesVigilancia().getProcedimento().getReferencia() != null
//                && !Lambda.exists(lancamentoAtividadesVigilanciaItemList, having(on(LancamentoAtividadesVigilanciaItem.class).getAtividadesVigilancia().getProcedimento().getReferencia(),
//                equalTo(atividadeEstabelecimentoProducao.getAtividadesVigilancia().getProcedimento().getReferencia())));
//    }

    private LancamentoAtividadesVigilancia gerarLancamentoAtividadeVigilancia() {
        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        //if (!SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
            lancamentoAtividadesVigilancia.setProfissional(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());
        //}
        lancamentoAtividadesVigilancia.setDataAtividade(dataFaturamento);
        return lancamentoAtividadesVigilancia;
    }

    private void validaConfiguracaoVigilancia(ConfiguracaoVigilancia configuracaoVigilancia) throws ValidacaoException {
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException("Por favor, informe a configuração da Vigilância Sanitária");
        }
//        if (configuracaoVigilancia.getAtividadesVigilanciaLicenca() == null) {
//            throw new ValidacaoException("Por favor, na configuração da vigilância, informe a atividade padrão para o licenciamento");
//        }
    }
}
