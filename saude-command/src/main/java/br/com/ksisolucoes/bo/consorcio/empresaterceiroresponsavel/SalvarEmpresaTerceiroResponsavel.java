package br.com.ksisolucoes.bo.consorcio.empresaterceiroresponsavel;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioEmpresaTerceiroResponsavelItemDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.EmpresaTerceiroResponsavel;

/**
 * <AUTHOR> 19/01/2018
 */
public class SalvarEmpresaTerceiroResponsavel extends AbstractCommandTransaction {

    private ConsorcioEmpresaTerceiroResponsavelItemDTO dto;

    public SalvarEmpresaTerceiroResponsavel(ConsorcioEmpresaTerceiroResponsavelItemDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(EmpresaTerceiroResponsavel.class, dto.getEmpresaTerceiroResponsavelList(), new QueryCustom.QueryCustomParameter(EmpresaTerceiroResponsavel.PROP_EMPRESA, dto.getEmpresa()));
    }
}