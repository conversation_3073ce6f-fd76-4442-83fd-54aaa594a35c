package br.com.ksisolucoes.bo.consorcio.procedimentoprestador;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.CommandQueryConsulta;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioPrestadorServicoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryProcedimentosPrestadorParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProcedimentoPrestadorServico extends CommandQueryConsulta {

    private QueryProcedimentosPrestadorParam param;

    public QueryConsultaProcedimentoPrestadorServico(QueryProcedimentosPrestadorParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("consorcioPrestadorServico.codigo","consorcioPrestadorServico.codigo");
        
        hql.addToSelect("consorcioPrestador.codigo","consorcioPrestadorServico.consorcioPrestador.codigo");
        hql.addToSelect("consorcioPrestador.flagManterPrecoSus","consorcioPrestadorServico.consorcioPrestador.flagManterPrecoSus");
        
        hql.addToSelect("empresaConsorcio.codigo","consorcioPrestadorServico.consorcioPrestador.empresaConsorcio.codigo");
        hql.addToSelect("empresaConsorcio.descricao","consorcioPrestadorServico.consorcioPrestador.empresaConsorcio.descricao");
        hql.addToSelect("empresaConsorcio.telefone","consorcioPrestadorServico.consorcioPrestador.empresaConsorcio.telefone");
        
        hql.addToSelect("empresaPrestador.codigo","consorcioPrestadorServico.consorcioPrestador.empresaPrestador.codigo");
        hql.addToSelect("empresaPrestador.cidade.descricao","consorcioPrestadorServico.consorcioPrestador.empresaPrestador.cidade.descricao");
        hql.addToSelect("empresaPrestador.descricao","consorcioPrestadorServico.consorcioPrestador.empresaPrestador.descricao");
        hql.addToSelect("empresaPrestador.telefone","consorcioPrestadorServico.consorcioPrestador.empresaPrestador.telefone");
        
        hql.addToSelect("consorcioProcedimento.codigo","consorcioPrestadorServico.consorcioProcedimento.codigo");
        hql.addToSelect("consorcioProcedimento.procedimento.codigo","consorcioPrestadorServico.consorcioProcedimento.procedimento.codigo");
        hql.addToSelect("consorcioProcedimento.referencia","consorcioPrestadorServico.consorcioProcedimento.referencia");
        hql.addToSelect("consorcioProcedimento.procedimento.descricao","consorcioPrestadorServico.consorcioProcedimento.procedimento.descricao");
        hql.addToSelect("consorcioProcedimento.valorProcedimento","consorcioPrestadorServico.consorcioProcedimento.valorProcedimento");
        hql.addToSelect("consorcioProcedimento.descricaoProcedimento","consorcioPrestadorServico.consorcioProcedimento.descricaoProcedimento");

        hql.setFrom("ConsorcioPrestadorServico consorcioPrestadorServico"
                + " left join consorcioPrestadorServico.consorcioPrestador consorcioPrestador"
                + " left join consorcioPrestador.empresaConsorcio empresaConsorcio"
                + " left join consorcioPrestador.empresaPrestador empresaPrestador"
                + " left join empresaPrestador.cidade cidade"
                + " left join consorcioPrestadorServico.consorcioProcedimento consorcioProcedimento"
                + " left join consorcioProcedimento.procedimento procedimento");

        hql.setTypeSelect(ConsorcioPrestadorServicoDTO.class.getName());
        hql.addToWhereWhithAnd("consorcioPrestador.status = ", ConsorcioPrestador.StatusConsorcioPrestador.ATIVO.value());

        if (this.param.getEmpresaConsorcio() != null) {
            hql.addToWhereWhithAnd("empresaConsorcio = ", this.param.getEmpresaConsorcio());
        }
        if (this.param.getEmpresaPrestador() != null) {
            hql.addToWhereWhithAnd("empresaPrestador = ", this.param.getEmpresaPrestador());
        }
        if (this.param.getConsorcioProcedimento() != null) {
            hql.addToWhereWhithAnd("consorcioPrestadorServico.consorcioProcedimento = ", this.param.getConsorcioProcedimento());
        }
        
        if(this.param.getCampoOrdenacao() != null){
            String orderType = Coalesce.asString(this.param.getTipoOrdenacao(), "asc");
            
            if (orderType.equals("asc")) {
                orderType = "desc";
            } else if (orderType.equals("desc")) {
                orderType = "asc";
            }
                
            hql.addToOrder(param.getCampoOrdenacao()+" "+ orderType);
        } else{
            hql.addToOrder("empresaPrestador.descricao asc");
            hql.addToOrder("consorcioProcedimento.descricaoProcedimento asc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected String getAlias() {
        return "cps";
    }

    @Override
    public boolean addProperties() {
        return false;
    }
}