package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoalvara;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EstabelecimentoAtividadeSetorDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoAlvaraDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlvara;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoAlvaraInicialSetores extends AbstractCommandTransaction<SalvarRequerimentoAlvaraInicialSetores> {

    private RequerimentoAlvaraDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<EstabelecimentoAtividadeSetorDTO> estabelecimentoSetorDTOList;
    private boolean gerarOcorrenciaCadastro = false;

    public SalvarRequerimentoAlvaraInicialSetores(RequerimentoAlvaraDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ArrayList<Long> protocolosList = new ArrayList();
        ArrayList<String> setorList = new ArrayList();
        estabelecimentoSetorDTOList = new ArrayList<>();
        if (dto.getRequerimentoAlvara().getCodigo() == null && CollectionUtils.isNotNullEmpty(dto.getEstabelecimentoSetoresDTOList())) {
            RequerimentoAlvara requerimentoAlvara;
            RequerimentoVigilancia rv;

            for (EstabelecimentoAtividadeSetorDTO dtoSetor : dto.getEstabelecimentoSetoresDTOList()) {
                RequerimentoAlvaraDTO requerimentoAlvaraDTO = dto;
                rv = VOUtils.cloneObject(this.dto.getRequerimentoAlvara().getRequerimentoVigilancia());
                requerimentoAlvara = VOUtils.cloneObject(this.dto.getRequerimentoAlvara());
                requerimentoAlvara.setRequerimentoVigilancia(rv);
                requerimentoAlvara.setEstabelecimentoSetores(dtoSetor.getEstabelecimentoSetores());
                requerimentoAlvara.setNumeroAlvara(null);
                requerimentoAlvara.setDataValidade(null);
                requerimentoAlvaraDTO.setRequerimentoAlvara(requerimentoAlvara);
                if (dto.getRequerimentoAlvara().getRequerimentoVigilancia().getDataRequerimento() == null) {
                    dto.getRequerimentoAlvara().getRequerimentoVigilancia().setDataRequerimento(DataUtil.getDataAtual());
                }

                requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoAlvaraInicial(requerimentoAlvaraDTO);
                protocolosList.add(requerimentoVigilancia.getProtocolo());
                setorList.add(dtoSetor.getEstabelecimentoSetores().getDescricaoSetor());

                dtoSetor.setRequerimentoVigilancia(requerimentoVigilancia);

                dtoSetor.getEstabelecimentoSetores().setDataValidadeAlvara(dto.getRequerimentoAlvara().getDataValidade());

                EstabelecimentoSetores setores = BOFactory.save(dtoSetor.getEstabelecimentoSetores());
                dtoSetor.setEstabelecimentoSetores(setores);

                this.estabelecimentoSetorDTOList.add(dtoSetor);
            }
        } else {
            requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoAlvaraInicial(dto);
        }
        if (CollectionUtils.isNotNullEmpty(protocolosList)) {
            requerimentoVigilancia.setProtocoloList(protocolosList);
            requerimentoVigilancia.setSetorList(setorList);
        }

        dto.setEstabelecimentoSetoresDTOList(this.estabelecimentoSetorDTOList);
        dto.setRequerimentoVigilancia(this.requerimentoVigilancia);
    }

    public RequerimentoAlvaraDTO getDto() {
        return dto;
    }
}