package br.com.ksisolucoes.bo.prontuario.hospital.trabalhoparto;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.TrabalhoParto;

/**
 *
 * <AUTHOR>
 */
public class SaveTrabalhoParto extends SaveVO<TrabalhoParto> {

    public SaveTrabalhoParto(TrabalhoParto vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getHora()==null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data"));
        } else if(this.vo.getHora().after(Data.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_invalida_data_nao_pode_ser_maior_que_data_atual"));
        }
        
        if(this.vo.getBatimentoCardiacoFeto() != null && this.vo.getBatimentoCardiacoFeto()>200L) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_batimento_cardiaco_feto_deve_ser_entre_0_200"));
        }  
    }

}
