package br.com.ksisolucoes.bo.materiais.riscoproduto;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.materiais.riscoproduto.dto.QueryConsultaRiscoProdutoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.materiais.RiscoProduto;

import java.util.List;
import java.util.Map;

public class QueryConsultaRiscoProduto extends CommandQueryPager<QueryConsultaRiscoProduto> {

    private QueryConsultaRiscoProdutoDTOParam param;

    public QueryConsultaRiscoProduto(QueryConsultaRiscoProdutoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.setTypeSelect(RiscoProduto.class.getName());

        hql.addToSelect("riscoProduto.codigo","codigo");
        hql.addToSelect("riscoProduto.descricao","descricao");

        hql.addToFrom("RiscoProduto riscoProduto");

        hql.addToWhereWhithAnd("riscoProduto.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd("riscoProduto.status = ", RiscoProduto.Status.ATIVO.value());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("riscoProduto.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("riscoProduto.codigo || ' ' || riscoProduto.descricao",param.getKeyword()));

        if(param.getPropSort() != null){
            hql.addToOrder("riscoProduto."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("riscoProduto.descricao");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String,Object>>)result);
    }
}
