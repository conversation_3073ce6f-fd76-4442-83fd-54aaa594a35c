package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AgendadorProcesso;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AtualizarAgendadorProcessoUsuarios extends AbstractCommandTransaction {

    private List<Usuario> usuarioList;
    private AgendadorProcesso agendadorProcesso;
    private List<Usuario> usuariosRemovidosList;

    public AtualizarAgendadorProcessoUsuarios(AgendadorProcesso agendadorProcesso, List<Usuario> usuarioList, List<Usuario> usuariosRemovidosList) {
        this.agendadorProcesso = agendadorProcesso;
        this.usuarioList = usuarioList;
        this.usuariosRemovidosList = usuariosRemovidosList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.getBO(UsuarioFacade.class).deletarAgendadorProcessoUsuarios(agendadorProcesso, usuariosRemovidosList);
        BOFactory.getBO(UsuarioFacade.class).salvarAgendadorProcessoUsuarios(agendadorProcesso, usuarioList);
    }

}
