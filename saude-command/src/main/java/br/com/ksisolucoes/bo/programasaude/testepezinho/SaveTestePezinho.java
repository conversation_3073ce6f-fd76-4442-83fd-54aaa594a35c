package br.com.ksisolucoes.bo.programasaude.testepezinho;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.programasaude.testepezinhohistorico.SaveTestePezinhoHistorico;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.programasaude.TestePezinho;
import br.com.ksisolucoes.vo.programasaude.TestePezinhoHistorico;
import br.com.ksisolucoes.vo.programasaude.TestePezinhoHistoricoPK;
import java.sql.PreparedStatement;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;

/**
 * <AUTHOR> Giordani
 *
 */
public class SaveTestePezinho extends SaveVO {

	private static final long serialVersionUID = 1L;
        private TestePezinho testePezinho;
        private boolean primeiraColeta = false;

    public SaveTestePezinho(Object vo) {
        super( vo );
        this.testePezinho = (TestePezinho) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(this.testePezinho.getId().getCodigo() ==null){
            this.testePezinho.getId().setCodigo(getNewCodigoTestePezinho(this.testePezinho.getId().getEmpresa()));
            primeiraColeta = true;
        }

        if(testePezinho.getStatus() == null){
            testePezinho.setStatus(TestePezinho.STATUS_ATENDITO);
        }

        if(testePezinho.getDataAtendimento() == null){
            testePezinho.setDataAtendimento(Data.getDataAtual());
        }

    }

    @Override
    protected void depoisSave() throws ValidacaoException, DAOException {
        /**
         * Gera Historico teste pezinho
         */
        String descricaoTestePezinho = null;
        if (TestePezinho.STATUS_ATENDITO.equals(testePezinho.getStatus())) {
            descricaoTestePezinho = Bundle.getStringApplication("msg_realizacao_coleta");
        }else if(TestePezinho.STATUS_CONCLUIDO.equals(testePezinho.getStatus())){
            descricaoTestePezinho = Bundle.getStringApplication("rotulo_teste_pezinho_concluido").toUpperCase();
        }else if(TestePezinho.STATUS_ENCAMINHADO_CRIANCA_SAUDAVEL.equals(testePezinho.getStatus())){
            descricaoTestePezinho = Bundle.getStringApplication("rotulo_teste_pezinho_concluido_com_encaminhamento");
        }else{
            descricaoTestePezinho = Bundle.getStringApplication("rotulo_desconhecido");
        }

        TestePezinhoHistorico testePezinhoHistorico = new TestePezinhoHistorico(new TestePezinhoHistoricoPK());
        testePezinhoHistorico.getId().setTestePezinho(testePezinho);
        testePezinhoHistorico.setUsuario(testePezinho.getUsuario());
        testePezinhoHistorico.setDescricao(descricaoTestePezinho);

        new SaveTestePezinhoHistorico(testePezinhoHistorico).start();

    }

    public TestePezinho getTestePezinho() {
        return testePezinho;
    }


	/**
	 * {@inheritDoc}
	 *
	 * @return {@inheritDoc}
	 */
	
public Long getNewCodigoTestePezinho(Empresa empresa) throws DAOException {
       Long maxId = this.getMaxId(empresa);

       if (maxId != null) {
           long id = maxId.longValue();
           return new Long(id) +1;
       } else {
           return new Long(1);
       }
   }

   public Long getMaxId(Empresa empresa) throws DAOException {
       try {
           String update = "update Teste_Pezinho " +
                   "           set nr_teste = nr_teste " +
                   "         where empresa = ? " +
                   "           and nr_teste = (select max(tp.nr_teste) " +
                   "                            from Teste_Pezinho tp " +
                   "                           where tp.empresa = ?)";
           SQLQuery statement = getSession().createSQLQuery(update);
           statement.setLong(1, empresa.getCodigo());
           statement.setLong(2, empresa.getCodigo());
           statement.executeUpdate();
       } catch (HibernateException hibernateException) {
           throw new DAOException(hibernateException.getMessage());
       }

       getSession().flush();

       HQLHelper hql = new HQLHelper();
       hql.addToSelect("max(tp.id.codigo)");
       hql.addToFrom(TestePezinho.class.getName() + " tp");
       hql.addToWhereWhithAnd(" tp.id.empresa.codigo = :codigoEmpresa");

       Query query = getSession().createQuery(hql.getQuery());

       hql.setParameterValue(query, "codigoEmpresa", empresa.getCodigo());

       Object result = query.uniqueResult();

       if (result != null) {
           return (Long) result;
       } else {
           return null;
       }
   }
}
