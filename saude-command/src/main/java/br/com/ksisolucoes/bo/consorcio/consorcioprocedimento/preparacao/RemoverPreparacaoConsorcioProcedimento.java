package br.com.ksisolucoes.bo.consorcio.consorcioprocedimento.preparacao;

import br.com.celk.consorcio.dto.PreparacaoConsorcioProcedimentoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PreparacaoConsorcioProcedimento;
import br.com.ksisolucoes.vo.consorcio.PreparacaoConsorcioProcedimentoItem;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemoverPreparacaoConsorcioProcedimento extends AbstractCommandTransaction {

    private PreparacaoConsorcioProcedimentoDTO dto;

    public RemoverPreparacaoConsorcioProcedimento(PreparacaoConsorcioProcedimentoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        List<PreparacaoConsorcioProcedimentoItem> lstPreparacaoItem = LoadManager.getInstance(PreparacaoConsorcioProcedimentoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PreparacaoConsorcioProcedimentoItem.PROP_PREPARACAO_CONSORCIO_PROCEDIMENTO, dto.getPreparacaoConsorcioProcedimento()))
                .start().getList();

        for (PreparacaoConsorcioProcedimentoItem pei : lstPreparacaoItem) {
            BOFactory.delete(pei);
        }

        PreparacaoConsorcioProcedimento preparacao = LoadManager.getInstance(PreparacaoConsorcioProcedimento.class)
                .setId(dto.getPreparacaoConsorcioProcedimento().getCodigo())
                .start().getVO();

        BOFactory.delete(preparacao);

    }

}
