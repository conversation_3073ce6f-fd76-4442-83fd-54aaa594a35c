package br.com.ksisolucoes.bo.encaminhamento;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaEncaminhamentosPaciente extends CommandQuery<QueryConsultaEncaminhamentosPaciente> {

    private Long codigoUsuarioCadsus;
    private List<EncaminhamentoDTO> result;

    public QueryConsultaEncaminhamentosPaciente(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("usuarioCadsus.codigo", "encaminhamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "encaminhamento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.telefone", "encaminhamento.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "encaminhamento.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "encaminhamento.usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "encaminhamento.usuarioCadsus.telefone4");
        hql.addToSelect("usuarioCadsus.celular", "encaminhamento.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.email", "encaminhamento.usuarioCadsus.email");

        hql.addToSelect("unidadeEncaminhamento.codigo", "encaminhamento.unidadeEncaminhamento.codigo");
        hql.addToSelect("unidadeEncaminhamento.descricao", "encaminhamento.unidadeEncaminhamento.descricao");

        hql.addToSelect("tipoEncaminhamento.codigo", "encaminhamento.tipoEncaminhamento.codigo");
        hql.addToSelect("tipoEncaminhamento.descricao", "encaminhamento.tipoEncaminhamento.descricao");

        hql.addToSelect("profissional.codigo", "encaminhamento.profissional.codigo");
        hql.addToSelect("profissional.referencia", "encaminhamento.profissional.referencia");
        hql.addToSelect("profissional.nome", "encaminhamento.profissional.nome");

        hql.addToSelect("encaminhamento.codigo", "encaminhamento.codigo");
        hql.addToSelect("encaminhamento.dataCadastro", "encaminhamento.dataCadastro");
        hql.addToSelect("encaminhamento.status", "encaminhamento.status");
        
        hql.addToSelect("encaminhamentoAgendamento.codigo", "encaminhamento.encaminhamentoAgendamento.codigo");
        hql.addToSelect("encaminhamentoAgendamento.dataAgendamento", "encaminhamento.encaminhamentoAgendamento.dataAgendamento");

        hql.addToSelect("localAgendamento.codigo", "encaminhamento.encaminhamentoAgendamento.localAgendamento.codigo");
        hql.addToSelect("localAgendamento.referencia", "encaminhamento.encaminhamentoAgendamento.localAgendamento.referencia");
        hql.addToSelect("localAgendamento.descricao", "encaminhamento.encaminhamentoAgendamento.localAgendamento.descricao");

        hql.addToSelect("usuarioContato.codigo", "encaminhamento.usuarioContato.codigo");
        hql.addToSelect("usuarioContato.nome", "encaminhamento.usuarioContato.nome");

        hql.setTypeSelect(EncaminhamentoDTO.class.getName());
        hql.addToFrom("Encaminhamento encaminhamento"
                + " left join encaminhamento.unidadeEncaminhamento unidadeEncaminhamento"
                + " left join encaminhamento.tipoEncaminhamento tipoEncaminhamento"
                + " left join encaminhamento.encaminhamentoAgendamento encaminhamentoAgendamento"
                + " left join encaminhamentoAgendamento.localAgendamento localAgendamento"
                + " left join encaminhamento.profissional profissional"
                + " left join encaminhamento.usuarioCadsus usuarioCadsus"
                + " left join encaminhamento.usuarioContato usuarioContato");

        hql.addToWhereWhithAnd("usuarioCadsus.codigo =", codigoUsuarioCadsus);
        
        hql.addToOrder("encaminhamento.dataCadastro desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<EncaminhamentoDTO> getResult() {
        return result;
    }

}
