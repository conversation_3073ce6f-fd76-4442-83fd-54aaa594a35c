package br.com.ksisolucoes.bo.vigilancia.financeiro.vigilanciafinanceiro;

import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 * <AUTHOR>
 */
public class SaveVigilanciaFinanceiro extends SaveVO<VigilanciaFinanceiro> {

    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro = null;

    public SaveVigilanciaFinanceiro(VigilanciaFinanceiro vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        carregarConfiguracaoVigilanciaFinanceiro();
        if (configuracaoVigilanciaFinanceiro != null && ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
            configuraProtocoloMemorando();
        }
        if(this.vo.getIsento() == null) {
            this.vo.setIsento(RepositoryComponentDefault.NAO_LONG);
        }

        if(RepositoryComponentDefault.SIM_LONG.equals(this.vo.getIsento())){
            this.vo.setStatus(VigilanciaFinanceiro.Status.PAGO.value());
        }

        if(this.vo.getDataVencimento() != null) {
            this.vo.setDataVencimento(Data.adjustRangeHour(this.vo.getDataVencimento()).getDataFinal());
        }

        if(this.vo.getFlagComplementar() == null) {
            this.vo.setFlagComplementar(RepositoryComponentDefault.NAO_LONG);
        }
    }

    private void carregarConfiguracaoVigilanciaFinanceiro() throws ValidacaoException {
        configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        configuracaoVigilanciaFinanceiro = (ConfiguracaoVigilanciaFinanceiro) getSession().get(ConfiguracaoVigilanciaFinanceiro.class, configuracaoVigilanciaFinanceiro.getCodigo());
    }

    private void configuraProtocoloMemorando() throws ValidacaoException, DAOException {
        if (configuracaoVigilanciaFinanceiro.getAnoBaseGeralMemorando() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msgInformeAnoBaseMemorandoConfiguracoesReceituarioVigilancia"));
        }

        long sequencial = 0L;
        if (configuracaoVigilanciaFinanceiro != null && configuracaoVigilanciaFinanceiro.getIniciarSequencialMemorando() != 0L) {
            sequencial = configuracaoVigilanciaFinanceiro.getIniciarSequencialMemorando();
        }
        sequencial++;
        String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilanciaFinanceiro.getAnoBaseGeralMemorando()));
        Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
        this.vo.setNumeracao(nextId);

        configuracaoVigilanciaFinanceiro.setIniciarSequencialMemorando(sequencial);
        BOFactory.save(configuracaoVigilanciaFinanceiro);
    }
}