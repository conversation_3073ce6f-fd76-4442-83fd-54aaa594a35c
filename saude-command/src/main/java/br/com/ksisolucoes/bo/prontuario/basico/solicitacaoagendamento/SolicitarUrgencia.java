package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoPrioridade;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class SolicitarUrgencia extends AbstractCommandTransaction<SolicitarUrgencia> {

    private SolicitacaoAgendamento sa;
    private String tipoControleRegulacao;

    public SolicitarUrgencia(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.sa = solicitacaoAgendamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (sa == null) {
            return;
        }

        SolicitacaoPrioridade solicitacaoPrioridadePendente = LoadManager.getInstance(SolicitacaoPrioridade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, sa))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR)))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_STATUS, SolicitacaoPrioridade.Status.PENDENTE.value()))
                .setMaxResults(1)
                .start().getVO();

        if (solicitacaoPrioridadePendente != null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_uma_solicitacao_pendente_para_este_item"));
        }

        SolicitacaoAgendamento solicitacaoAgendamento = (SolicitacaoAgendamento) getSession().load(SolicitacaoAgendamento.class, this.sa.getCodigo());

        if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao())) {
            solicitacaoAgendamento.setSolicitarPrioridade(RepositoryComponentDefault.SIM_LONG);
            solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
            solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
            solicitacaoAgendamento.setObservacaoUrgente(sa.getObservacaoUrgente());
        }else {
            solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_URGENTE);
            solicitacaoAgendamento.setObservacaoUrgente(sa.getObservacaoUrgente());

        }
        BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoPrioridade(solicitacaoAgendamento);

        BOFactory.save(solicitacaoAgendamento);
        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, Bundle.getStringApplication("msg_solicitado_urgencia"), solicitacaoAgendamento);
    }

    public String getTipoControleRegulacao() {
        if(tipoControleRegulacao == null){
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

}
