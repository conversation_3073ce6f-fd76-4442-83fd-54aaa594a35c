package br.com.ksisolucoes.bo.documentos;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoEncaminhamentoProtese;
import br.com.ksisolucoes.vo.prontuario.basico.DocumentoParecerMedico;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;

/**
 *
 * <AUTHOR>
 */
public class SalvarDocumentoEncaminhamentoProtese extends AbstractCommandTransaction {
    
    private DocumentoEncaminhamentoProtese documentoEncaminhamentoProtese;
    private DocumentoAtendimento documentoAtendimento;
    private Atendimento atendimento;
    private TipoDocumentoAtendimento tipoDocumentoAtendimento;
    private String descricaoDocumentoAtendimento;

    public SalvarDocumentoEncaminhamentoProtese(DocumentoEncaminhamentoProtese documentoEncaminhamentoProtese, Atendimento atendimento, TipoDocumentoAtendimento tipoDocumentoAtendimento, String descricaoDocumentoAtendimento) {
        this.documentoEncaminhamentoProtese = documentoEncaminhamentoProtese;
        this.atendimento = atendimento;
        this.tipoDocumentoAtendimento = tipoDocumentoAtendimento;
        this.descricaoDocumentoAtendimento = descricaoDocumentoAtendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {        
        if(documentoEncaminhamentoProtese.getDocumentoAtendimento() == null){
            documentoAtendimento = new DocumentoAtendimento();
        } else{
            documentoAtendimento = documentoEncaminhamentoProtese.getDocumentoAtendimento();
        }
        
        documentoAtendimento.setDescricao(descricaoDocumentoAtendimento);
        documentoAtendimento.setTipoDocumentoAtendimento(tipoDocumentoAtendimento);
        documentoAtendimento.setAtendimento(atendimento);
        documentoAtendimento.setProfissional(atendimento.getProfissional());
        documentoAtendimento = BOFactory.save(documentoAtendimento);
        
        documentoEncaminhamentoProtese.setDocumentoAtendimento(documentoAtendimento);
        BOFactory.save(documentoEncaminhamentoProtese);
        
    }
    
    public DocumentoAtendimento getDocumentoAtendimento(){
        return documentoAtendimento;
    }
    
}
