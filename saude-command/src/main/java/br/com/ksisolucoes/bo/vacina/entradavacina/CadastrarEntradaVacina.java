/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vacina.entradavacina;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vacina.EntradaVacina;
import br.com.ksisolucoes.vo.vacina.ItemEntradaVacina;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarEntradaVacina extends AbstractCommandTransaction {

    private EntradaVacina entradaVacina;
    private List<ItemEntradaVacina> itens;

    public CadastrarEntradaVacina(EntradaVacina entradaVacina, List<ItemEntradaVacina> itens) {
        this.entradaVacina = entradaVacina;
        this.itens = itens;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        validacoes();
        
        entradaVacina.setStatus(EntradaVacina.StatusEntradaVacina.ABERTO.getValue());
        entradaVacina.setEmpresa((Empresa)SessaoAplicacaoImp.getInstance().getEmpresa());
        entradaVacina.setUsuario((Usuario)SessaoAplicacaoImp.getInstance().getUsuario());
        entradaVacina.setDataUsuario(Data.getDataAtual());
        if(entradaVacina.getCodigo() == null){
            entradaVacina.setDataCadastro(Data.getDataAtual());
        }

        BOFactory.save(entradaVacina);
        List<ItemEntradaVacina> itensExistentes = LoadManager.getInstance(ItemEntradaVacina.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ItemEntradaVacina.PROP_ENTRADA_VACINA), this.entradaVacina))
                .start().getList();
        
        Dinheiro totalItens = Dinheiro.ZERO;
        for (ItemEntradaVacina itemEntradaVacina : itens) {
            itemEntradaVacina.setEntradaVacina(entradaVacina);
            BOFactory.save(itemEntradaVacina);
            totalItens = new Dinheiro(itemEntradaVacina.getValorTotal()).somar(totalItens);
            itensExistentes.remove(itemEntradaVacina);
        }
        
        if(! totalItens.equals(new Dinheiro(entradaVacina.getValorTotal()))){
            throw new ValidacaoException(Bundle.getStringApplication("msg_total_nota_diferente_itens"));
        }
        
        for (ItemEntradaVacina itemEntradaVacina : itensExistentes) {
            BOFactory.delete(itemEntradaVacina);
        }
    }

    private void validacoes() throws ValidacaoException {
        if(Data.adjustRangeHour(Data.getDataAtual()).getDataFinal().before(entradaVacina.getDataEmissao())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_emissao_deve_ser_menor_igual_hoje"));
        }
        if(Data.adjustRangeHour(Data.getDataAtual()).getDataFinal().before(entradaVacina.getDataPortaria())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_portaria_menor_hoje"));
        }
    }

    public EntradaVacina getEntradaVacina() {
        return entradaVacina;
    }
    
}
