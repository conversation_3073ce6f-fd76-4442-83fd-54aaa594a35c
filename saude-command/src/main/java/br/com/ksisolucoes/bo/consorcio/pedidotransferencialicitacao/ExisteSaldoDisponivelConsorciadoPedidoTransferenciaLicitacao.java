package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.*;
import ch.lambdaj.Lambda;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * Created by sulivan on 18/07/18.
 */
public class ExisteSaldoDisponivelConsorciadoPedidoTransferenciaLicitacao extends AbstractCommandTransaction {

    private PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;
    private boolean existeSaldoDisponivel = true;

    public ExisteSaldoDisponivelConsorciadoPedidoTransferenciaLicitacao(PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
        this.pedidoTransferenciaLicitacao = pedidoTransferenciaLicitacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        SubConta subConta = HibernateUtil.lockTable(SubConta.class, pedidoTransferenciaLicitacao.getSubContaMedicamento().getCodigo());
        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

        if (Conta.StatusConta.INATIVO.value().equals(conta.getStatus())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_conta_X_consorciado_esta_inativa", conta.getDescricao()));
        }

        List<PedidoTransferenciaLicitacaoItem> itemList = getSession().createCriteria(PedidoTransferenciaLicitacaoItem.class)
                .add(Restrictions.eq(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
                .add(Restrictions.eq(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                .list();

        if(CollectionUtils.isNotNullEmpty(itemList)) {
            Double saldoDisponivel = calculaSaldoDisponivel(subConta, pedidoTransferenciaLicitacao.getAnoCadastro());
            Double totalPrecoItens = Lambda.sum(itemList, Lambda.on(PedidoTransferenciaLicitacaoItem.class).getTotalItem());

            if(Coalesce.asDouble(saldoDisponivel).compareTo(Coalesce.asDouble(totalPrecoItens)) < 0) {
                existeSaldoDisponivel = false;
            }
        }
    }

    private Double calculaSaldoDisponivel(SubConta subConta, Long ano) throws DAOException {
        if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno"))) {
            if (ano == null)
                return null;
            SubContaAno subContaAno = (SubContaAno) getSession().createCriteria(SubContaAno.class)
                    .add(Restrictions.eq(VOUtils.montarPath(SubContaAno.PROP_SUB_CONTA, SubConta.PROP_CODIGO), subConta.getCodigo()))
                    .add(Restrictions.eq(SubContaAno.PROP_ANO, ano))
                    .uniqueResult();
            return new Dinheiro(subContaAno.getSaldoAtual()).subtrair(subContaAno.getValorReservado()).doubleValue();
        }
        return new Dinheiro(subConta.getSaldoAtual()).subtrair(subConta.getValorReservado()).doubleValue();
    }

    public boolean isExisteSaldoDisponivel() {
        return existeSaldoDisponivel;
    }
}
