package br.com.ksisolucoes.bo.prontuario.basico.atendimento.odonto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SalvarAtendimentoOdontoFicha extends AbstractCommandTransaction {

    private AtendimentoOdontoFicha atendimentoOdontoFicha;
    private List<AtendimentoOdontoPlano> atendimentoOdontoPlanoList;
    private Atendimento atendimentoAtual;

    public SalvarAtendimentoOdontoFicha(AtendimentoOdontoFicha atendimentoOdontoFicha, List<AtendimentoOdontoPlano> atendimentoOdontoPlanoList, Atendimento atendimentoAtual) {
        this.atendimentoOdontoFicha = atendimentoOdontoFicha;
        this.atendimentoOdontoPlanoList = atendimentoOdontoPlanoList;
        this.atendimentoAtual = atendimentoAtual;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (atendimentoOdontoFicha.getDataInicioTratamento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_inicio_tratamento"));
        } else if (atendimentoOdontoFicha.getDataInicioTratamento().after(DataUtil.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_inicio_tratamento_maior_atual"));
        }

        gerarAtendimentoItem();
        
        if (atendimentoOdontoFicha.getCodigo() == null && atendimentoOdontoFicha.getAtendimento().getCodigo().equals(atendimentoAtual.getCodigo())) {
            atendimentoOdontoFicha.setFlagInicioTratamento(RepositoryComponentDefault.SIM_LONG);
        }

        atendimentoOdontoFicha = BOFactory.save(atendimentoOdontoFicha);
        atualizarAlergiaUsuarioCadsusDado();

        gerarAtendimentoProntuario();

        for (AtendimentoOdontoPlano item : atendimentoOdontoPlanoList) {
            item.setAtendimento(atendimentoOdontoFicha.getAtendimento());
            item.setAtendimentoOdontoFicha(atendimentoOdontoFicha);

            if (SituacaoDente.TipoSituacao.PROCEDIMENTO.value().equals(item.getSituacaoDente().getTipoSituacao())) {
                item.setStatus(AtendimentoOdontoPlano.Status.PENDENTE.value());
            } else {
                item.setAtendimentoExecucao(atendimentoAtual);
                item.setStatus(AtendimentoOdontoPlano.Status.HISTORICO.value());
            }
        }

        VOUtils.persistirListaVosModificados(AtendimentoOdontoPlano.class, atendimentoOdontoPlanoList, new QueryCustom.QueryCustomParameter(AtendimentoOdontoPlano.PROP_ATENDIMENTO_ODONTO_FICHA, atendimentoOdontoFicha));
    }

    private void gerarAtendimentoProntuario() throws DAOException, ValidacaoException {
        Atendimento atendimento = atendimentoOdontoFicha.getAtendimento();

        AtendimentoProntuario atendimentoProntuario = (AtendimentoProntuario) getSession().createCriteria(AtendimentoProntuario.class)
                .add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO_ODONTO_FICHA, atendimentoOdontoFicha))
                .add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO, atendimento))
                .uniqueResult();

        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.TRATAMENTO_ODONTOLOGICO.value(), atendimento, atendimentoOdontoFicha.getCodigo());
        }

        atendimentoProntuario = new AtendimentoProntuario();

        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setDescricao(getDescricaoAtendimento());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.TRATAMENTO_ODONTOLOGICO.value());
        atendimentoProntuario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(atendimento.getEmpresa());
        atendimentoProntuario.setProfissional(atendimento.getProfissional());
        atendimentoProntuario.setAtendimento(atendimento);
        atendimentoProntuario.setAtendimentoOdontoFicha(atendimentoOdontoFicha);
        atendimentoProntuario.setTabelaCbo(atendimento.getTabelaCbo());

        BOFactory.save(atendimentoProntuario);
    }

    private String getDescricaoAtendimento() {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append(Bundle.getStringApplication("msg_iniciado_tratamento_odontologico_data_x", Data.formatar(atendimentoOdontoFicha.getDataInicioTratamento())));

        return descricao.toString();
    }

    private void gerarAtendimentoItem() throws DAOException, ValidacaoException {
//        if (atendimentoOdontoFicha.getCodigo() == null && atendimentoOdontoFicha.getAtendimento().getCodigo().equals(atendimentoAtual.getCodigo())) {
//            try {
//                IParameterModuleContainer modulo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
//                Procedimento procedimentoInicioTratamentoOdontologico = modulo.getParametro("procedimentoInicioTratamentoOdontologico");
//                Date dataCompetencia = CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento();
//
//                ProcedimentoCompetencia procedimentoCompetencia = (ProcedimentoCompetencia) getSession().createCriteria(ProcedimentoCompetencia.class)
//                        .add(Restrictions.eq(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), dataCompetencia))
//                        .add(Restrictions.eq(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), procedimentoInicioTratamentoOdontologico))
//                        .uniqueResult();
//
//                BOFactory.getBO(AtendimentoFacade.class).gerarAtendimentoItemOdontoFicha(atendimentoAtual, procedimentoCompetencia);
//            } catch (ValidacaoRuntimeException ex) {
//                Loggable.log.error(ex.getMessage(), ex);
//            } catch (DAOException ex) {
//                Loggable.log.error(ex.getMessage(), ex);
//            }
//        }
    }

    private void atualizarAlergiaUsuarioCadsusDado() throws DAOException, ValidacaoException {
        UsuarioCadsusDado usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), atendimentoOdontoFicha.getAtendimento().getUsuarioCadsus().getCodigo()))
                .uniqueResult();

        if (usuarioCadsusDado == null) {
            usuarioCadsusDado = new UsuarioCadsusDado();
            usuarioCadsusDado.setCodigo(atendimentoOdontoFicha.getAtendimento().getUsuarioCadsus().getCodigo());
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoFicha.getAlergicoMedicamento())
                && StringUtils.trimToNull(atendimentoOdontoFicha.getAlergicoMedicamentoQual()) != null) {

            String[] alergias = org.springframework.util.StringUtils.trimArrayElements(StringUtils.split(atendimentoOdontoFicha.getAlergicoMedicamentoQual(), ","));
            alergias = org.springframework.util.StringUtils.mergeStringArrays(alergias, org.springframework.util.StringUtils.trimArrayElements(StringUtils.split(usuarioCadsusDado.getDescricaoAlergico(), ",")));
            usuarioCadsusDado.setDescricaoAlergico(StringUtils.join(alergias, ", "));
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(atendimentoOdontoFicha.getAlergicoAnestesia())
                && StringUtils.trimToNull(atendimentoOdontoFicha.getAlergicoAnestesiaQual()) != null) {

            String[] alergias = org.springframework.util.StringUtils.trimArrayElements(StringUtils.split(atendimentoOdontoFicha.getAlergicoAnestesiaQual(), ","));
            alergias = org.springframework.util.StringUtils.mergeStringArrays(alergias, org.springframework.util.StringUtils.trimArrayElements(StringUtils.split(usuarioCadsusDado.getDescricaoAlergico(), ",")));
            usuarioCadsusDado.setDescricaoAlergico(StringUtils.join(alergias, ", "));
        }

        BOFactory.save(usuarioCadsusDado);
    }

    public AtendimentoOdontoFicha getAtendimentoOdontoFicha() {
        return atendimentoOdontoFicha;
    }
}
