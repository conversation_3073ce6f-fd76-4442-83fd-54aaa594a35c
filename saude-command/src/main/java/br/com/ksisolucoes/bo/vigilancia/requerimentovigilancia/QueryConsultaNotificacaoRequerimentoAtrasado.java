package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.celk.vigilancia.dto.NotificarRequerimentoAtrasadoDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Silvio.
 */
public class QueryConsultaNotificacaoRequerimentoAtrasado extends CommandQuery<QueryConsultaNotificacaoRequerimentoAtrasado> {

    private List<NotificarRequerimentoAtrasadoDTO> list; 
    private Date dataReferencia;

    public QueryConsultaNotificacaoRequerimentoAtrasado(Date dataReferencia) {
        this.dataReferencia = dataReferencia;
    }
        
//SELECT t1.cd_req_vigilancia FROM requerimento_vigilancia t1, requerimento_vigilancia_fiscal t2
//where t1.situacao in (9,10,0,12,13)
//and t1.cd_req_vigilancia = t2.cd_req_vigilancia
//and t1.cd_req_vigilancia in  (
//	SELECT t3.cd_req_vigilancia FROM requerimento_vigilancia t3, ocorrencia_req_vigilancia t4
//	where t3.situacao in (9,10,0,12,13)
//	and t3.cd_req_vigilancia = t4.cd_req_vigilancia
//	group by 1
//	having max(t4.data_ocorrencia) < {ts '2019-06-14 15:55:14'})
    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(NotificarRequerimentoAtrasadoDTO.class.getName());

        hql.addToSelect("rv.codigo", "requerimentoVigilancia.codigo");
        hql.addToSelect("rv.protocolo", "requerimentoVigilancia.protocolo");
        hql.addToSelect("usuario.codigo", "usuario.codigo");
        hql.addToSelect("usuario.nome", "usuario.nome");
        
        hql.addToFrom("Usuario usuario ");
        hql.addToFrom("RequerimentoVigilanciaFiscal rvf "
                + "left join rvf.profissional profissional "
                + "left join rvf.requerimentoVigilancia rv ");

        hql.addToWhereWhithAnd("usuario.profissional = profissional");
        hql.addToWhereWhithAnd("rv.situacao in :situacaoList");
        hql.addToWhereWhithAnd("rv.codigo in ("
                + "select rv2.codigo from RequerimentoVigilancia rv2, OcorrenciaRequerimentoVigilancia orv "
                + "where rv2 = orv.requerimentoVigilancia "
                + "and rv2.situacao in :situacaoList "
                + "group by 1 having max(orv.dataOcorrencia) < :dataReferencia "
                + ")");
        
        hql.addToOrder("rv.codigo");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("situacaoList", Arrays.asList(RequerimentoVigilancia.Situacao.PENDENTE.value(), 
                            RequerimentoVigilancia.Situacao.CADASTRADO.value(),
                            RequerimentoVigilancia.Situacao.ANALISE.value(),
                            RequerimentoVigilancia.Situacao.EM_INSPECAO.value(),
                            RequerimentoVigilancia.Situacao.EM_REINSPECAO.value()));
        query.setParameter("dataReferencia", dataReferencia);
    }

    @Override
    public List<NotificarRequerimentoAtrasadoDTO> getResult() {
        return list;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}