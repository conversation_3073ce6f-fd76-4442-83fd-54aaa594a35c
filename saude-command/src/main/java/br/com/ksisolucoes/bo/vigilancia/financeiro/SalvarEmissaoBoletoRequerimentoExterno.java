package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ValorTaxaRequerimentoVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ValorTaxaRequerimentoVigilanciaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaFinanceiroBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarEmissaoBoletoRequerimentoExterno extends AbstractCommandTransaction<SalvarEmissaoBoletoRequerimentoExterno> {

    private VigilanciaFinanceiroBoletoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<VigilanciaFinanceiro> vigilanciaFinanceiroList;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;
    private BigDecimal totalTaxa;
    private BigDecimal quantidadeTaxa;

    public SalvarEmissaoBoletoRequerimentoExterno(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // gera boleto/memorando unico
        vigilanciaFinanceiroList = new ArrayList<>();
        configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro().getFormaCobranca())) {
            FinanceiroVigilanciaHelper.validateConnectionBoleto();
        }
        this.dto = getBoletoDto();

        FinanceiroVigilanciaHelper.validarBoletoRequerimento(dto, configuracaoVigilanciaFinanceiro);
        VigilanciaFinanceiro vigilanciaFinanceiro = FinanceiroVigilanciaHelper.processarGeracaoFinanceiroRequerimento(this.dto, configuracaoVigilanciaFinanceiro);
        VigilanciaFinanceiro saveVF = BOFactory.getBO(CadastroFacade.class).save(vigilanciaFinanceiro);
        vigilanciaFinanceiroList.add(saveVF);

        requerimentoVigilancia.setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.EMISAO_BOLETO.value());
        RequerimentoVigilancia save = BOFactory.save(requerimentoVigilancia);
        gerarOcorrenciaRequerimentoVigilancia(save, saveVF);
    }



    private VigilanciaFinanceiroBoletoDTO getBoletoDto() throws ValidacaoException {
        ValorTaxaRequerimentoVigilanciaDTO dtoTaxa = null;
        VigilanciaFinanceiroBoletoDTO dtoFinanceiro = null;
        ValorTaxaRequerimentoVigilanciaDTOParam param = new ValorTaxaRequerimentoVigilanciaDTOParam();
        param.setRequerimentoVigilancia(requerimentoVigilancia);
        try {
            dtoTaxa = BOFactory.getBO(VigilanciaFacade.class).valorTaxaRequerimentoVigilancia(param);
        } catch (DAOException | ValidacaoException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        if(dtoTaxa != null) {

            dtoFinanceiro = new VigilanciaFinanceiroBoletoDTO();
            dtoFinanceiro.setRequerimentoVigilancia(requerimentoVigilancia);
            dtoFinanceiro.setValorBoleto(getValorBoleto(dtoTaxa.getTotalTaxa()));
            dtoFinanceiro.setQuantidadeTaxa(dtoTaxa.getQuantidadeTaxa());
            dtoFinanceiro.setDataVencimento(FinanceiroVigilanciaHelper.calcularDataVencto(configuracaoVigilanciaFinanceiro));
            if(dtoTaxa.getTaxaVigente() != null) {
                dtoFinanceiro.setValorTaxa(new BigDecimal(dtoTaxa.getTaxaVigente().getValorIndice()));
            }
        }
        return dtoFinanceiro;
    }

    private Double getValorBoleto(BigDecimal totalTaxa) {
        return new Dinheiro(totalTaxa).doubleValue();
    }

    private void gerarOcorrenciaRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia, VigilanciaFinanceiro vigilanciaFinanceiro) throws DAOException, ValidacaoException {
        String descricaoOcorrencia = null;
        if (requerimentoVigilancia.getFlagIsentoMei() != null || (vigilanciaFinanceiro != null && Coalesce.asDouble(vigilanciaFinanceiro.getValor()) == 0D)) {
            String descAuxIsencao = RequerimentoVigilancia.Isencao.MEI.value().equals(requerimentoVigilancia.getFlagIsentoMei()) ? RequerimentoVigilancia.Isencao.MEI.descricao() :
                    Coalesce.asString(requerimentoVigilancia.getDescricaoIsentoOutro());
            descricaoOcorrencia = Bundle.getStringApplication("msg_financeiro_gerado_isento_x", descAuxIsencao);
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(descricaoOcorrencia, requerimentoVigilancia, null);
        } else {
            if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                descricaoOcorrencia = Bundle.getStringApplication("msg_boleto_emitido") + " (Nº " + vigilanciaFinanceiro.getCodigo() + ")";
            } else {
                descricaoOcorrencia = Bundle.getStringApplication("msg_memorando_emitido");
            }
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(descricaoOcorrencia, requerimentoVigilancia, null);
        }
    }

    public List<VigilanciaFinanceiro> getVigilanciaFinanceiroList() {
        return vigilanciaFinanceiroList;
    }
}
