package br.com.ksisolucoes.bo.integracao.bpa;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ContaContaPacienteConsorcioManualOcorrenciasDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.GerarContaContaPacienteConsorcioManualDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.integracao.bpa.dto.BoletimProducaoAmbulatorialDTO;
import br.com.ksisolucoes.bo.integracao.bpa.dto.GrupoBpa;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.bo.procedimentos.interfaces.facade.ProcedimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.ConsultaProcedimentoRegistroDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.*;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.Bpa;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.*;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import com.amazonaws.util.StringUtils;
import org.hibernate.Query;
import org.hibernate.criterion.Restrictions;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class MontarBpa extends AbstractCommandTransaction<MontarBpa> {

    private String PREFIXO_LOG_TIPO = "";
    private final Long codigoBpaProcesso;
    private final RetornoValidacao retornoValidacao = new RetornoValidacao();
    private final Long tipoBpa;
    private final List<Empresa> empresaList;
    private BpaProcesso bpaProcesso = null;
    private Long totalFolhas = 0L;
    private Long somatorioCodigosProcedimento = 0L;
    private Long somatorioQuantidades = 0L;
    private Date competencia;
    private boolean semRegistros = true;
    private boolean fimProcessoComErro = false;

    public MontarBpa(Long codigoBpaProcesso, Long tipoBpa, List<Empresa> empresaList) {
        this.codigoBpaProcesso = codigoBpaProcesso;
        this.tipoBpa = tipoBpa;
        this.empresaList = empresaList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        PREFIXO_LOG_TIPO = ProcedimentoDetalheCadastro.REGISTRO_SISCAN.equals(this.tipoBpa) ? "SISCAN" : "BPA";
        Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - INICIO PROCESSO N°-").concat(String.valueOf(this.codigoBpaProcesso)));
        int countEmpresas = 0;
        try {
            bpaProcesso = (BpaProcesso) this.getSession().get(BpaProcesso.class, this.codigoBpaProcesso);

            bpaProcesso.setStatus(BpaProcesso.STATUS_BPA_GERADO);

            bpaProcesso = (BpaProcesso) getSession().merge(bpaProcesso);

            Long ano = bpaProcesso.getAno();
            Long mes = bpaProcesso.getMes();
            mes--;

            int diaInicioCompetencia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia().intValue();
            DatePeriod periodo = Data.periodoDataCompetencia(diaInicioCompetencia, mes.intValue(), ano.intValue());
            competencia = Data.competenciaData(diaInicioCompetencia, periodo.getDataInicial());
            validarParametros();

            gerarContaManualConsorcio(periodo, bpaProcesso);

            Group<Empresa> group = Lambda.group(empresaList, by(on(Empresa.class).getCnes()));
            for (Group<Empresa> empresaGroup : group.subgroups()) {
                List<Empresa> empresas = empresaGroup.findAll();
                countEmpresas += empresas.size();
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - PROCESSANDO EMPRESA ").concat(String.valueOf(countEmpresas)).concat("/").concat(String.valueOf(empresaList.size())));
                List<BoletimProducaoAmbulatorialDTO> dtoList;
                if (!ProcedimentoDetalheCadastro.REGISTRO_SISCAN.equals(this.tipoBpa)) {
                    QueryConsultaBpa query = new QueryConsultaBpa();
                    query.setTipoBpa(tipoBpa);
                    query.setCompetencia(competencia);
                    query.setTipoFinanciamento(bpaProcesso.getTipoFinanciamento());
                    query.setEmpresaList(empresas);
                    query.leitura().start();
                    dtoList = query.getProducao();
                } else {
                    QueryConsultaBpaSiscan querySiscan = new QueryConsultaBpaSiscan();
                    querySiscan.setTipoBpa(tipoBpa);
                    querySiscan.setCompetencia(competencia);
                    querySiscan.setTipoFinanciamento(bpaProcesso.getTipoFinanciamento());
                    querySiscan.setEmpresaList(empresas);
                    querySiscan.leitura().start();
                    dtoList = querySiscan.getProducao();
                }

                if (CollectionUtils.isAllEmpty(dtoList)) {
                    continue;
                } else {
                    semRegistros = false;
                }

                // monta map
                Map<GrupoBpa, List<BoletimProducaoAmbulatorialDTO>> bpaMap = montarBpaMap(dtoList);

                //Gera registro de BPA
                gerarRegistroBpa(bpaMap);

                getSession().flush();
                getSession().clear();

                // códigos para fazer update no item da conta
                List<Long> codigoItemContaPacienteList = Lambda.extract(dtoList, Lambda.on(BoletimProducaoAmbulatorialDTO.class).getCodigoItemContaPaciente());
                updateItemContaPaciente(codigoItemContaPacienteList, bpaProcesso);
            }

            if (semRegistros) {
                bpaProcesso.setStatus(BpaProcesso.STATUS_SEM_REGISTROS);
                getSession().saveOrUpdate(bpaProcesso);
                return;
            }

            if (!retornoValidacao.isValido()) {
                throw new ValidacaoException(retornoValidacao);
            }

            Long codigoControle = ((somatorioCodigosProcedimento + somatorioQuantidades) % 1111) + 1111;
            bpaProcesso.setControleDadosArquivo(codigoControle);
            bpaProcesso.setTotalFolha(totalFolhas);
            getSession().saveOrUpdate(bpaProcesso);

            BOFactory.getBO(AtendimentoGeralFacade.class).montarArquivoBpa(bpaProcesso.getCodigo());
        } catch (ValidacaoException e) {
            String msg = "";
            if (!retornoValidacao.isVazio()) {
                for (MensagemValidacao mv : retornoValidacao.getMensagemValidacaoMap().values()) {
                    msg += mv.getMensagem() + "\n";
                }
            } else {
                msg = montarErro(e);
            }

            if (bpaProcesso.getMensagemErro() != null) {
                bpaProcesso.setMensagemErro(bpaProcesso.getMensagemErro().concat(msg));
            } else {
                bpaProcesso.setMensagemErro(msg);
            }
            bpaProcesso.setStatus(BpaProcesso.STATUS_ERRO);
            getSession().saveOrUpdate(bpaProcesso);
            fimProcessoComErro = true;
        } catch (DAOException e) {
            if (bpaProcesso.getMensagemErro() != null) {
                bpaProcesso.setMensagemErro(bpaProcesso.getMensagemErro().concat(montarErro(e)));
            } else {
                bpaProcesso.setMensagemErro(montarErro(e));
            }
            bpaProcesso.setStatus(BpaProcesso.STATUS_ERRO);
            getSession().saveOrUpdate(bpaProcesso);
            Loggable.log.error(e.getMessage(), e);
            fimProcessoComErro = true;
        } catch (Exception e) {
            if (bpaProcesso.getMensagemErro() != null) {
                bpaProcesso.setMensagemErro(bpaProcesso.getMensagemErro().concat(montarErro(e)));
            } else {
                bpaProcesso.setMensagemErro(montarErro(e));
            }
            bpaProcesso.setStatus(BpaProcesso.STATUS_ERRO);
            getSession().saveOrUpdate(bpaProcesso);
            Loggable.log.error(e.getMessage(), e);
            fimProcessoComErro = true;
        } finally {
            if (fimProcessoComErro) {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - FIM DO PROCESSAMENTO (COM ERROS) - Processo Nº-").concat(String.valueOf(bpaProcesso.getCodigo())));
            } else if (semRegistros) {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - FIM DO PROCESSAMENTO (SEM REGISTROS) - Processo Nº-").concat(String.valueOf(bpaProcesso.getCodigo())));
            } else {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - FIM DO PROCESSAMENTO (SUCESSO) - Processo Nº-").concat(String.valueOf(bpaProcesso.getCodigo())));
            }
        }
    }

    private Map<GrupoBpa, List<BoletimProducaoAmbulatorialDTO>> montarBpaMap(List<BoletimProducaoAmbulatorialDTO> dtoList) {
        Map<GrupoBpa, List<BoletimProducaoAmbulatorialDTO>> bpaMap = new LinkedHashMap<>();

        Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - Montando bpaMap - ").concat(String.valueOf(dtoList.size())).concat(" registros processados..."));
        for (BoletimProducaoAmbulatorialDTO dto : dtoList) {
            GrupoBpa bpaAux = new GrupoBpa();
            bpaAux.setDto(dto);

            List<BoletimProducaoAmbulatorialDTO> list = bpaMap.get(bpaAux);
            if (list == null) {
                list = new ArrayList<>();
                bpaMap.put(bpaAux, list);
            }

            list.add(dto);
        }
        return bpaMap;
    }

    private void gerarRegistroBpa(Map<GrupoBpa, List<BoletimProducaoAmbulatorialDTO>> bpaMap) throws DAOException {
        Long procedimentoRegistroInicial = null;
        Empresa empresaAnterior = null;
        String cnsProfissionalAnterior = null;
        TabelaCbo cboAnterior = null;
        int idxFolha = 0;
        Long linha = 0L;
        int count = 0;
        int sizeLog = Math.min(bpaMap.size(), 1000);
        for (Map.Entry<GrupoBpa, List<BoletimProducaoAmbulatorialDTO>> entry : bpaMap.entrySet()) {
            if (count % sizeLog == 0) {
                Loggable.log.info(PREFIXO_LOG_TIPO.concat(" - Gerando registro - Gerados ").concat(String.valueOf(count)).concat(" registros..."));
            }
            count++;

            GrupoBpa grupo = entry.getKey();
            BoletimProducaoAmbulatorialDTO dto = grupo.getDto();

            somatorioCodigosProcedimento += Long.parseLong(entry.getKey().getDto().getProcedimento().getReferencia());

            Long quantidadeGrupo = 0L;
            for (BoletimProducaoAmbulatorialDTO bpaDto : entry.getValue()) {
                quantidadeGrupo += bpaDto.getQuantidade();
            }
            somatorioQuantidades += quantidadeGrupo;

            if (procedimentoRegistroInicial == null) {
                procedimentoRegistroInicial = dto.getProcedimentoRegistro();
            }
            if (empresaAnterior == null) {
                empresaAnterior = dto.getEmpresa();
            }

            boolean bpaIndividual = ProcedimentoRegistroCadastro.BPA_INDIVIDUAL.equals(dto.getProcedimentoRegistro()) ||
                    ProcedimentoRegistroCadastro.ESUS_APS.equals(dto.getProcedimentoRegistro());

            if (bpaIndividual && cnsProfissionalAnterior == null) {
                cnsProfissionalAnterior = dto.getCnsProfissional();
            }

            if (!procedimentoRegistroInicial.equals(dto.getProcedimentoRegistro())) {
                procedimentoRegistroInicial = dto.getProcedimentoRegistro();
                idxFolha = 0;
                linha = 0L;
            }

            if (empresaAnterior.getCnes() == null || dto.getEmpresa().getCnes() == null) {
                if (!empresaAnterior.equals(dto.getEmpresa())) {
                    empresaAnterior = dto.getEmpresa();
                    idxFolha = 0;
                    linha = 0L;
                }
            } else {
                if (!empresaAnterior.getCnes().equals(dto.getEmpresa().getCnes())) {
                    empresaAnterior = dto.getEmpresa();
                    idxFolha = 0;
                    linha = 0L;
                }
            }
            if (bpaIndividual) {
                if (dto.getCnsProfissional() != null && !dto.getCnsProfissional().equals(cnsProfissionalAnterior)) {
                    cnsProfissionalAnterior = dto.getCnsProfissional();
                    idxFolha = 0;
                    linha = 0L;
                }
                if (dto.getTabelaCbo() != null && !dto.getTabelaCbo().equals(cboAnterior)) {
                    cboAnterior = dto.getTabelaCbo();
                    idxFolha = 0;
                    linha = 0L;
                }
            }

            int folha;
            if (bpaIndividual) {
                folha = 1 + (idxFolha / 99);
                if (linha % 99 == 0) {
                    linha = 0L;
                    totalFolhas++;
                }
            } else {
                folha = 1 + (idxFolha / 20);
                if (linha % 20 == 0) {
                    linha = 0L;
                    totalFolhas++;
                }
            }

            Bpa bpa = new Bpa();
            bpa.setBpaProcesso(bpaProcesso);
            bpa.setFolha(Long.valueOf(folha));
            bpa.setSequencia(++linha);
            bpa.setEmpresa(dto.getEmpresa());
            if (dto.getCidade() != null) {
                bpa.setCidade(dto.getCidade());
            } else if (dto.getCodigoCidade() != null) {
                bpa.setCidade((Cidade) getSession().get(Cidade.class, dto.getCodigoCidade()));
            }
            if (dto.getCid() != null) {
                bpa.setCid(dto.getCid());
            } else if (dto.getCodigoCid() != null) {
                bpa.setCid((Cid) getSession().get(Cid.class, dto.getCodigoCid().trim()));
            }
            bpa.setCaraterAtendimento(dto.getCaraterAtendimento());
            bpa.setDataAtendimento(dto.getDataAtendimento());
            bpa.setCnesEquipe(dto.getCnesEquipe());

            if (bpaIndividual) {
                if (dto.getProfissional() != null && dto.getProfissional().getCodigo() != null) {
                    bpa.setProfissional((Profissional) getSession().get(Profissional.class, dto.getProfissional().getCodigo()));
                } else if (dto.getCodigoProfissional() != null) {
                    bpa.setProfissional((Profissional) getSession().get(Profissional.class, dto.getCodigoProfissional()));
                }
                bpa.setCnsProfissional(dto.getCnsProfissional());

                UsuarioCadsus uc = null;
                if (dto.getCodigoUsuarioCadsus() != null) {
                    uc = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, dto.getCodigoUsuarioCadsus());
                    bpa.setUsuarioCadsus(uc);
                }
                if (dto.getEnderecoUsuarioCadsus() != null && dto.getEnderecoUsuarioCadsus().getCodigo() != null) {
                    bpa.setEnderecoUsuarioCadsus(new EnderecoUsuarioCadsus(dto.getEnderecoUsuarioCadsus().getCodigo()));
                } else if (dto.getCodigoEnderecoUsuarioCadsus() != null) {
                    bpa.setEnderecoUsuarioCadsus(new EnderecoUsuarioCadsus(dto.getCodigoEnderecoUsuarioCadsus()));
                } else if (dto.getCodigoEnderecoUsuarioCadsus() == null && bpa.getUsuarioCadsus() != null) {
                    bpa.setEnderecoUsuarioCadsus(bpa.getUsuarioCadsus().getEnderecoUsuarioCadsus());
                }

                bpa.setCns(Coalesce.asString(dto.getNumeroCns()));
                bpa.setIdade(dto.getIdade());

                if (dto.getServicoClassificacao() != null) {
                    String[] servicoClassificacao = dto.getServicoClassificacao().split(Pattern.quote("/"));

                    if (servicoClassificacao.length >= 1 && !"".equals(servicoClassificacao[0])) {
                        bpa.setServico(Long.parseLong(servicoClassificacao[0]));
                    }
                    if (servicoClassificacao.length > 1) {
                        bpa.setClassificacao(Long.parseLong(servicoClassificacao[1]));
                    }
                }
            } else {
                if (dto.isExigeIdade()) {
                    bpa.setIdade(dto.getIdade());
                } else {
                    bpa.setIdade(999L);
                }
            }
            bpa.setQuantidade(quantidadeGrupo);

            if (dto.getDataCompetencia() != null) {
                bpa.setCompetenciaProcedimento(dto.getDataCompetencia());
            } else {
                bpa.setCompetenciaProcedimento(competencia);
            }
            bpa.setProcedimento(dto.getProcedimento());

            if (dto.getTabelaCbo() != null) {
                if (dto.getTabelaCbo().getCbo() == null || dto.getTabelaCbo().getCbo().trim().equals("")) {
                    dto.setTabelaCbo(null);
                } else {
                    bpa.setTabelaCbo((TabelaCbo) getSession().get(TabelaCbo.class, dto.getTabelaCbo().getCbo()));
                }
            }

            bpa.setTipoBpa(ProcedimentoRegistroCadastro.BPA_CONSOLIDADO.equals(dto.getProcedimentoRegistro()) ? Bpa.TIPO_BPA.CONSOLIDADO.value() : Bpa.TIPO_BPA.INDIVIDUAL.value());

            getSession().save(bpa);

            validarBpa(bpa, dto);

            idxFolha++;

            if (++count % 100 == 0) {
                getSession().flush();
                getSession().clear();
            }
        }
    }

    private void updateItemContaPaciente(List<Long> codigoItemContaPacienteList, BpaProcesso bpaProcesso) {
        if (CollectionUtils.isNotNullEmpty(codigoItemContaPacienteList)) {
            String update = "UPDATE item_conta_paciente "
                    + " SET cd_bpa_processo = ? "
                    + " WHERE cd_it_conta_paciente in(:codigoItemContaPacienteList) ";
            do {
                List<Long> codigos = codigoItemContaPacienteList.stream().limit(500).collect(Collectors.toList());

                Query queryItemContaPaciente = getSession().createSQLQuery(update);
                queryItemContaPaciente.setLong(0, bpaProcesso.getCodigo());
                queryItemContaPaciente.setParameterList("codigoItemContaPacienteList", codigos);
                queryItemContaPaciente.executeUpdate();

                codigoItemContaPacienteList.removeAll(codigos);

                getSession().flush();
                getSession().clear();
            } while (CollectionUtils.isNotNullEmpty(codigoItemContaPacienteList));
        }
    }

    private void gerarContaManualConsorcio(DatePeriod periodo, BpaProcesso bpaProcesso) throws DAOException, ValidacaoException {
        String gerarContaConsorcio = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("gerarContaGeracaoBPA");
        ContaContaPacienteConsorcioManualOcorrenciasDTO retornoValidacaoContaConsorcio = new ContaContaPacienteConsorcioManualOcorrenciasDTO();
        if (RepositoryComponentDefault.SIM.equals(gerarContaConsorcio)) {
            retornoValidacaoContaConsorcio = BOFactory.getBO(ConsorcioFacade.class).gerarContaContaPacienteConsorcioManual(new GerarContaContaPacienteConsorcioManualDTO(periodo, GerarContaContaPacienteConsorcioManualDTO.TipoConta.BPA.value()));
        }
        bpaProcesso.setMensagemErro(retornoValidacaoContaConsorcio.getMsg());
    }

    private String montarErro(Throwable t) {
        return StringUtil.montarMensagemErro(t);
    }

    private boolean isValidarCNS(BoletimProducaoAmbulatorialDTO registro) {
        return RepositoryComponentDefault.SIM_LONG.equals(Coalesce.asLong(registro.getFlagValidarCns(), RepositoryComponentDefault.SIM_LONG));
    }

    private void validarBpa(Bpa bpa, BoletimProducaoAmbulatorialDTO registro) throws DAOException {
        boolean obrigatorioInformarRaca = RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("obrigatorioInformarRaca"));

        if (Bpa.TIPO_BPA.INDIVIDUAL.value().equals(bpa.getTipoBpa())) {

            if (bpa.getUsuarioCadsus() == null) {
                String erro = Bundle.getStringApplication("msg_conta_X_sem_paciente", registro.getNumeroAtendimento());
                retornoValidacao.add(erro, erro, null);

            } else if (isValidarCNS(registro) &&
                    StringUtils.isNullOrEmpty(bpa.getUsuarioCadsus().getCpf()) &&
                    !CnsValidator.validaCns(Coalesce.asString(bpa.getCns()))
            ) {
                String erro = Bundle.getStringApplication("msg_paciente_codigo_cns_invalido", bpa.getUsuarioCadsus().getDescricaoFormatado());
                retornoValidacao.add(erro, erro, null);

            } else if (!isValidarCNS(registro) &&
                    !StringUtils.isNullOrEmpty(bpa.getUsuarioCadsus().getCpf()) &&
                    !CpfCnpJValidator.CPFIsValid(bpa.getUsuarioCadsus().getCpf())
            ) {
                String erro = Bundle.getStringApplication("msg_paciente_codigo_cpf_invalido", bpa.getUsuarioCadsus().getDescricaoFormatado());
                retornoValidacao.add(erro, erro, null);
            }

            if (bpa.getProfissional() == null && bpa.getCnsProfissional() == null) {
                String erro = Bundle.getStringApplication("msg_conta_X_sem_profissional",
                        registro.getNumeroAtendimento());
                retornoValidacao.add(erro, erro, null);
            } else if (!CnsValidator.validaCns(bpa.getCnsProfissional())) {
                String erro = Bundle.getStringApplication("msg_profissional_codigo_cns_invalido",
                        bpa.getProfissional().getDescricaoFormatado());
                retornoValidacao.add(erro, erro, null);
            }

            Raca racaUsuarioCadsus = getRaca(bpa.getUsuarioCadsus());

            if (bpa.getUsuarioCadsus() != null && racaUsuarioCadsus == null && obrigatorioInformarRaca) {
                String erro = Bundle.getStringApplication("msg_valida_cor_raca",
                        bpa.getUsuarioCadsus().getDescricaoNomeFormatado() != null ? bpa.getUsuarioCadsus().getDescricaoNomeFormatado() : bpa.getUsuarioCadsus().getCodigo());
                retornoValidacao.add(erro, erro, null);
            }

            validaQuantidadeMaxima(bpa, registro);
        }
    }

    private Raca getRaca(UsuarioCadsus usuarioCadsus) {
        if (usuarioCadsus == null) {
            return null;
        }
        if (usuarioCadsus.getRaca() != null) {
            return usuarioCadsus.getRaca();
        } else {
            if (usuarioCadsus.getCodigo() == null) return null;

            UsuarioCadsus usuarioCadsusRaca = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_RACA))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, usuarioCadsus.getCodigo()))
                    .start().getVO();

            return usuarioCadsusRaca == null ? null : usuarioCadsusRaca.getRaca();
        }
    }

    //TODO: Pegar dados que vem da consulta de BPA, não tem necessidaded de pegar no load manager (Avaliar se não é melhor colocar o valor maximo no lugar de verificar)
    private void validaQuantidadeMaxima(Bpa bpa, BoletimProducaoAmbulatorialDTO registro) {
        if (excedeQuantidadeMaxima(bpa)) {
            bpa = LoadManager.getInstance(Bpa.class).setId(bpa.getCodigo()).start().getVO();
            ItemContaPaciente icp = null;
            if (registro != null) {
                icp = LoadManager.getInstance(ItemContaPaciente.class)
                        .addProperties(new HQLProperties(ContaPaciente.class, ItemContaPaciente.PROP_CONTA_PACIENTE).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(ItemContaPaciente.PROP_CODIGO, registro.getCodigoItemContaPaciente()))
                        .setMaxResults(1).start().getVO();
            }
            String erro = "";
            if (icp != null && icp.getContaPaciente() != null) {
                erro = Bundle.getStringApplication("msg_quantidade_maxima_procedimento", icp.getContaPaciente().getDocumento() + " === " + registro != null ? registro.getNumeroAtendimento() : "",
                        bpa.getProcedimento().getDescricaoFormatado(), bpa.getEmpresa().getDescricaoFormatado(), bpa.getUsuarioCadsus().getNome());
                retornoValidacao.add(erro, erro, null);
            } else {
                erro = "NÃO FOI ENCONTRADO DOCUMENTO PARA O PROCEDIMENTO: "
                        + bpa.getProcedimento().getDescricaoFormatado() + " DA UNIDADE: " + bpa.getEmpresa().getDescricaoFormatado() + " REALIZADO NO PACIENTE: " + bpa.getUsuarioCadsus().getNome();
                retornoValidacao.add(erro, erro, null);
            }
        }
    }

    //TODO: Pegar da consulta do BPA
    private boolean excedeQuantidadeMaxima(Bpa bpa) {
        ProcedimentoCompetencia procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), bpa.getProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), bpa.getCompetenciaProcedimento()))
                .start().getVO();
        return procedimentoCompetencia != null && bpa.getQuantidade() > procedimentoCompetencia.getQuantidadeMaximaExecucao();
    }

    private void validarParametros() throws ValidacaoException {
        try {
            IParameterModuleContainer parameterModuleContainerUnidadeSaude = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            Date dataCompetencia = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);
            List<ProcedimentoRegistro> lstProcedimentoRegistro = null;
            ConsultaProcedimentoRegistroDTOParam param = new ConsultaProcedimentoRegistroDTOParam();
            Procedimento procedimentoFaturadoVisitaDenuncia = null;

            Long flagBpaGeraVigilanciaSanitaria = CargaBasicoPadrao.getInstance().getParametroAtendimento().getFlagBpaGeraVigilanciaSanitaria();
            if (flagBpaGeraVigilanciaSanitaria != null && RepositoryComponentDefault.SIM_LONG.equals(flagBpaGeraVigilanciaSanitaria)) {
                procedimentoFaturadoVisitaDenuncia = parameterModuleContainerUnidadeSaude.getParametro("procedimentoFaturadoVisitaDenuncia");
                param.setProcedimentos(Collections.singletonList(procedimentoFaturadoVisitaDenuncia));
            }

            param.setProcedimentoRegistroCadastros(Collections.singletonList(new ProcedimentoRegistroCadastro(ProcedimentoRegistroCadastro.BPA_CONSOLIDADO)));
            param.setDataCompetencia(dataCompetencia);

            lstProcedimentoRegistro = BOFactory.getBO(ProcedimentoFacade.class).consultaProcedimentoRegistro(param);

            if (CollectionUtils.isEmpty(lstProcedimentoRegistro)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_informado_parametro_gem_procedimentoFaturadoVisitaDenuncia_deve_ser_consolidado"));
            }

            ProcedimentoCompetencia procedimentoCompetencia = (ProcedimentoCompetencia) getSession().createCriteria(ProcedimentoCompetencia.class)
                    .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), procedimentoFaturadoVisitaDenuncia))
                    .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), dataCompetencia))
                    .uniqueResult();

            if (procedimentoCompetencia != null) {
                boolean procedimentoExigeIdade = this.getSession().createCriteria(ProcedimentoDetalhe.class)
                        .add(Restrictions.eq(VOUtils.montarPath(ProcedimentoDetalhe.PROP_ID, ProcedimentoDetalhePK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoCompetencia))
                        .add(Restrictions.eq(VOUtils.montarPath(ProcedimentoDetalhe.PROP_ID, ProcedimentoDetalhePK.PROP_DETALHE, ProcedimentoDetalheCadastro.PROP_CODIGO), ProcedimentoDetalheCadastro.EXIGE_IDADE))
                        .list().size() > 0;

                if (procedimentoExigeIdade) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_informado_parametro_gem_procedimentoFaturadoVisitaDenuncia_nao_deve_exigir_idade"));
                }
            }
        } catch (Exception e) {
            retornoValidacao.add(e.getMessage(), e.getMessage(), null);
            throw new ValidacaoException(e.getMessage());
        }
    }
}
