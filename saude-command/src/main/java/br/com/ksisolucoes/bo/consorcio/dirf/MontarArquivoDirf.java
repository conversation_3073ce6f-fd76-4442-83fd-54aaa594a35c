package br.com.ksisolucoes.bo.consorcio.dirf;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.RegistroDirfDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.RegistroDirfValorDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.consorcio.ConfiguracaoDirf;
import br.com.ksisolucoes.vo.consorcio.GeracaoDirf;
import ch.lambdaj.Lambda;
import org.apache.commons.io.IOUtils;
import org.hamcrest.Matchers;

import javax.swing.text.NumberFormatter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class MontarArquivoDirf extends AbstractCommandTransaction {

    private Long codigoGeracao;
    private ConfiguracaoDirf configuracaoDirf;

    public MontarArquivoDirf(Long codigoGeracao) {
        this.codigoGeracao = codigoGeracao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        GeracaoDirf geracaoDirf = HibernateUtil.lockTable(GeracaoDirf.class, codigoGeracao);

        configuracaoDirf = LoadManager.getInstance(ConfiguracaoDirf.class).start().getVO();

        FileWriter writer = null;
        try {

            File tmpFile = File.createTempFile("DIRF_", ".dec");
            tmpFile.deleteOnExit();

            writer = new FileWriter(tmpFile.getAbsolutePath());

            // ------------ PRIMEIRA LINHA ------------------------------

            StringBuilder builder = new StringBuilder();
            builder.append(GeracaoDirf.Registro.DIRF.descricao());
            builder.append("|");
            builder.append(DataUtil.getAnoAtual());
            builder.append("|");
            builder.append(configuracaoDirf.getAno());
            builder.append("|");
            builder.append("N");
            builder.append("|");
            builder.append("|");
            builder.append(configuracaoDirf.getIdentificadorEstruturaLeiaute());
            builder.append("|");

            writer.write(builder.toString());
            writer.write("\n");

            // ------------- SEGUNDA LINHA -------------------------------
            builder = new StringBuilder();
            builder.append(GeracaoDirf.Registro.RESPO.descricao());
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getCpfResponsavelPreenchimento()));
            builder.append("|");
            builder.append(StringUtil.removeAcentos(configuracaoDirf.getNomeResponsavelPreenchimento()));
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getTelefoneResponsavelPreenchimento()), 0, 2);
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getTelefoneResponsavelPreenchimento()).substring(2));
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getRamalResponsavelPreenchimento()));
            builder.append("|");
            builder.append(configuracaoDirf.getFaxResponsavelPreenchimento() != null ? StringUtil.getDigits(configuracaoDirf.getFaxResponsavelPreenchimento()).substring(2) : "");
            builder.append("|");
            builder.append(configuracaoDirf.getEmailResponsavelPreenchimento() != null ? configuracaoDirf.getEmailResponsavelPreenchimento() : "");
            builder.append("|");

            writer.write(builder.toString());
            writer.write("\n");

            // -------------- TERCEIRA LINHA -----------------------------
            builder = new StringBuilder();
            builder.append(GeracaoDirf.Registro.DECPJ.descricao());
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getCnpjDeclarante()));
            builder.append("|");
            builder.append(StringUtil.removeAcentos(configuracaoDirf.getNomeEmpresarial()));
            builder.append("|");
            builder.append("2");
            builder.append("|");
            builder.append(StringUtil.getDigits(configuracaoDirf.getCpfResponsavelCnpj()));
            builder.append("|");
            builder.append("N");//6
            builder.append("|");
            builder.append("N");//7
            builder.append("|");
            builder.append("N");//8
            builder.append("|");
            builder.append("N");//9
            builder.append("|");
            builder.append("N");//10
            builder.append("|");
            builder.append("N");//11
            builder.append("|");
            builder.append("N");//12
            builder.append("|");
            builder.append("N");//13
            builder.append("|");
            builder.append("|");

            writer.write(builder.toString());
            writer.write("\n");

            // --------------- REGISTROS -----------------------------------
            QueryGeracaoDirf queryGeracaoDirf = new QueryGeracaoDirf(geracaoDirf.getAno());
            queryGeracaoDirf.start();

            List<RegistroDirfDTO> dtoList = queryGeracaoDirf.getDtoList();

            List<RegistroDirfDTO> dtoListFisica;
            List<RegistroDirfDTO> dtoListJuridica;
            dtoListFisica = Lambda.select(dtoList, having(on(RegistroDirfDTO.class).getTipoPessoa(), Matchers.equalTo(Pessoa.PESSOA_FISICA)));
            dtoListJuridica = Lambda.select(dtoList, having(on(RegistroDirfDTO.class).getTipoPessoa(), Matchers.equalTo(Pessoa.PESSOA_JURIDICA)));

            if (CollectionUtils.isNotNullEmpty(dtoListFisica)) {

                dtoListFisica = Lambda.sort(dtoListFisica, on(RegistroDirfDTO.class).getCpfCnpj());

                int flush = 0;
                writer.write("IDREC|0588|");
                for (RegistroDirfDTO dto : dtoListFisica) {
//                    if (validaExibicao(dto)) {
                        builder = new StringBuilder();

                        builder.append("\n");
                        builder.append("BPFDEC|");
                        builder.append(StringUtil.getStringMaxPrecision(StringUtil.getDigits(dto.getCpfCnpj()), 11));
                        builder.append("|");
                        builder.append(StringUtil.removeAcentos(StringUtil.getStringMaxPrecision(dto.getDeclarante().replaceAll("(\\||\\;|\\.|\\-|\\/)", ""), 60)));
                        builder.append("||N|N|"); // Data atribuída pelo laudo da moléstia grave, Indicador de identificação do alimentando, ndicador de identificação da previdência complementar

                        builder.append(getLinhaValoresRTRT(dto));
                        if (validaExibicaoRTPO(dto)) {
                            builder.append(getLinhaValoresRTPO(dto));
                        }
                        if (validaExibicaoRTIRF(dto)){
                            builder.append(getLinhaValoresRTIRF(dto));
                        }

                        writer.write(builder.toString());

                        flush++;
                        if (flush % 100 == 0) {
                            writer.flush();
                        }
//                    }
                }
            }

            if (CollectionUtils.isNotNullEmpty(dtoListJuridica)) {

                dtoListJuridica = Lambda.sort(dtoListJuridica, on(RegistroDirfDTO.class).getCpfCnpj());

                writer.write("\n");
                writer.write("IDREC|1708|");
                int flush = 0;
                for (RegistroDirfDTO dto : dtoListJuridica) {
//                    if (validaExibicao(dto)) {
                        builder = new StringBuilder();
                        builder.append("\n");
                        builder.append("BPJDEC|");
                        builder.append(StringUtil.getStringMaxPrecision(StringUtil.getDigits(dto.getCpfCnpj()), 14));
                        builder.append("|");
                        builder.append(StringUtil.removeAcentos(StringUtil.getStringMaxPrecision(dto.getDeclarante().replaceAll("(\\||\\;|\\.|\\-|\\/)", ""), 150)));
                        builder.append("|");

                        builder.append(getLinhaValoresRTRT(dto));

                        if (validaExibicaoRTIRF(dto)){
                            builder.append(getLinhaValoresRTIRF(dto));
                        }
                        writer.write(builder.toString());

                        flush++;
                        if (flush % 100 == 0) {
                            writer.flush();
                        }
//                    }
                }
            }

            writer.write("\n");
            writer.write("FIMDIRF|");
            writer.flush();

            GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(tmpFile, GerenciadorArquivo.OrigemArquivo.DIRF.value(), getNomeArquivo());

            if (gerenciadorArquivo != null) {
                geracaoDirf.setGerenciadorArquivo(gerenciadorArquivo);
                geracaoDirf.setStatus(GeracaoDirf.Status.PROCESSADO.value());
            } else {
                geracaoDirf.setStatus(GeracaoDirf.Status.ERRO.value());
            }

            BOFactory.save(geracaoDirf);

            geracaoDirf.setStatus(GeracaoDirf.Status.PROCESSADO.value());
            BOFactory.save(geracaoDirf);

        } catch (IOException ex) {

            geracaoDirf.setStatus(GeracaoDirf.Status.ERRO.value());
            BOFactory.save(geracaoDirf);

            throw new DAOException(ex);
        } finally {
            IOUtils.closeQuietly(writer);
        }

    }

    private String getNomeArquivo() {
        String prefix = "DIRF_";
        String cnpjEmpresa = configuracaoDirf.getNomeEmpresarial();
        Long ano = configuracaoDirf.getAno();
        return new StringBuilder(prefix).append(cnpjEmpresa).append(ano).toString();
    }

    private NumberFormatter formatter;

    private String formatarValor(Double valor) {

        if (formatter == null) {
            DecimalFormat format = new DecimalFormat();
            format.setGroupingUsed(false);
            format.setMinimumFractionDigits(2);
            format.setMaximumFractionDigits(2);

            formatter = new NumberFormatter(format);
            formatter.setAllowsInvalid(false);
            formatter.setMinimum(0.00);
            formatter.setMaximum(999.99);
        }

        String valorFormatado = null;

        try {
            valorFormatado = formatter.valueToString(valor);
        } catch (ParseException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        return valorFormatado;
    }

    private String getLinhaValoresRTRT(RegistroDirfDTO dto) {
        List<RegistroDirfValorDTO> listValoresImposto = dto.getListValoresImposto();

        StringBuilder rtrtBuilder = new StringBuilder();

        rtrtBuilder.append("\n");
        rtrtBuilder.append(GeracaoDirf.Registro.RTRT.descricao());
        rtrtBuilder.append("|");

        for (int mes = 1; mes <= 12; mes++) {
            RegistroDirfValorDTO valorDTO = Lambda.selectUnique(listValoresImposto, Lambda.having(on(RegistroDirfValorDTO.class).getMes(), Matchers.equalTo(Long.valueOf(mes))));

            if (valorDTO != null && Coalesce.asDouble(valorDTO.getPagamentoMes()) > 0D) {

                BigDecimal totalGuias = new Dinheiro(BigDecimal.valueOf(valorDTO.getPagamentoMes()))
                        .somar(new Dinheiro(BigDecimal.valueOf(valorDTO.getInssMes())))
                        .somar(new Dinheiro(BigDecimal.valueOf(valorDTO.getImpostoRendaMes())))
                        .somar(new Dinheiro(BigDecimal.valueOf(valorDTO.getIssMes())))
                        .bigDecimalValue();

                rtrtBuilder.append(StringUtil.getDigits(String.valueOf(totalGuias)));
            }

            rtrtBuilder.append("|");
        }

        rtrtBuilder.append("|"); // décimo terceiro

        return rtrtBuilder.toString();
    }

    private String getLinhaValoresRTPO(RegistroDirfDTO dto) {
        List<RegistroDirfValorDTO> listValoresImposto = dto.getListValoresImposto();

        StringBuilder rtrtBuilder = new StringBuilder();

        rtrtBuilder.append("\n");
        rtrtBuilder.append(GeracaoDirf.Registro.RTPO.descricao());
        rtrtBuilder.append("|");

        for (int mes = 1; mes <= 12; mes++) {
            RegistroDirfValorDTO valorDTO = Lambda.selectUnique(listValoresImposto, Lambda.having(on(RegistroDirfValorDTO.class).getMes(), Matchers.equalTo(Long.valueOf(mes))));

            if (valorDTO != null && Coalesce.asDouble(valorDTO.getInssMes()) > 0D) {
                rtrtBuilder.append(StringUtil.getDigits(formatarValor(valorDTO.getInssMes())));
            }

            rtrtBuilder.append("|");
        }

        rtrtBuilder.append("|"); // décimo terceiro

        return rtrtBuilder.toString();
    }

    private String getLinhaValoresRTIRF(RegistroDirfDTO dto) {
        List<RegistroDirfValorDTO> listValoresImposto = dto.getListValoresImposto();

        StringBuilder rtrtBuilder = new StringBuilder();

        rtrtBuilder.append("\n");
        rtrtBuilder.append(GeracaoDirf.Registro.RTIRF.descricao());
        rtrtBuilder.append("|");

        for (int mes = 1; mes <= 12; mes++) {
            RegistroDirfValorDTO valorDTO = Lambda.selectUnique(listValoresImposto, Lambda.having(on(RegistroDirfValorDTO.class).getMes(), Matchers.equalTo(Long.valueOf(mes))));

            if (valorDTO != null && Coalesce.asDouble(valorDTO.getImpostoRendaMes()) > 0D) {
                rtrtBuilder.append(StringUtil.getDigits(formatarValor(valorDTO.getImpostoRendaMes())));
            }

            rtrtBuilder.append("|");
        }

        rtrtBuilder.append("|"); // décimo terceiro

        return rtrtBuilder.toString();
    }

    private boolean validaExibicaoRTPO(RegistroDirfDTO dto) {
        List<RegistroDirfValorDTO> listValoresImposto = dto.getListValoresImposto();
        for (RegistroDirfValorDTO valorDTO : listValoresImposto) {
            if (valorDTO != null && Coalesce.asDouble(valorDTO.getInssMes()) > 0D) {
                return true;
            }
        }
        return false;
    }

    private boolean validaExibicaoRTIRF(RegistroDirfDTO dto) {
        List<RegistroDirfValorDTO> listValoresImposto = dto.getListValoresImposto();
        for (RegistroDirfValorDTO valorDTO : listValoresImposto) {
            if (valorDTO != null && Coalesce.asDouble(valorDTO.getImpostoRendaMes()) > 0D) {
                return true;
            }
        }
        return false;
    }

}
