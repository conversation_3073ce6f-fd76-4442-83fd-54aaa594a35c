/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.receituario;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ReceituarioItemComponentesDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.receituario.NoReceituarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ReceituarioFacade;
import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hamcrest.Matchers;

import java.util.*;

import static ch.lambdaj.Lambda.*;

/**
 * <AUTHOR>
 */
public class SalvarReceitaBranca extends AbstractCommandTransaction<SalvarReceitaBranca> {

    private final Receituario receituario;
    private final List<ReceituarioItemDTO> receituarioItens;
    private Receituario receituarioIteracao;
    private Long codigoReceituarioIteracao;
    private final NoReceituarioDTO receituarioDTO;
    private final String anotacao;

    private final List<Receituario> lstSaveReceituario;

    private final List<Long> codigos = new ArrayList<Long>();
    private String separaReceituarioAutomaticamente;

    public SalvarReceitaBranca(NoReceituarioDTO receituarioDTO) {
        this.receituario = receituarioDTO.getReceituario();
        this.anotacao = receituarioDTO.getReceituario().getAnotacao();
        this.receituarioItens = receituarioDTO.getReceituarioItemDTOList();
        this.receituarioDTO = receituarioDTO;
        lstSaveReceituario = new ArrayList<>();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        ReceituarioItem receituarioItemMagistral = null;
        TipoReceita tipoReceitaMagistral = LoadManager.getInstance(TipoReceita.class)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, TipoReceita.RECEITA_MAGISTRAL))
                .setMaxResults(1)
                .start().getVO();
        ListIterator<ReceituarioItemDTO> listIterator = receituarioItens.listIterator();

        separaReceituarioAutomaticamente = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("separaReceituarioAutomaticamente");

        while (listIterator.hasNext()) {
            ReceituarioItemDTO next = listIterator.next();
            if (next.getReceituarioItem().getProduto() == null
                    && next.getReceituarioItem().getTipoReceitaProdutoNaoCadastrado() == null) {
                receituarioItemMagistral = next.getReceituarioItem();
                receituarioItemMagistral.setTipoReceitaProdutoNaoCadastrado(tipoReceitaMagistral);
                listIterator.remove();
                break;
            }
        }

        Group<ReceituarioItemDTO> group = group(receituarioItens, by(on(ReceituarioItemDTO.class).getReceita()));

        receituarioIteracao = this.receituario;
        codigoReceituarioIteracao = null;

        for (Group<ReceituarioItemDTO> subgroup : group.subgroups()) {

            List<ReceituarioItemDTO> selectBasica = select(subgroup.findAll(), Lambda.having(on(ReceituarioItemDTO.class).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getTipoReceita(), Matchers.equalTo(TipoReceita.RECEITA_BASICA)));
            List<ReceituarioItemDTO> selectAntimicrobiana = select(subgroup.findAll(), Lambda.having(on(ReceituarioItemDTO.class).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getTipoReceita(), Matchers.equalTo(TipoReceita.RECEITA_ANTIMICROBIANA)));
            List<ReceituarioItemDTO> selectBranca = select(subgroup.findAll(), Lambda.having(on(ReceituarioItemDTO.class).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getTipoReceita(), Matchers.equalTo(TipoReceita.RECEITA_BRANCA)));
            List<ReceituarioItemDTO> selectAzul = select(subgroup.findAll(), Lambda.having(on(ReceituarioItemDTO.class).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getTipoReceita(), Matchers.equalTo(TipoReceita.RECEITA_AZUL)));
            List<ReceituarioItemDTO> selectAmarela = select(subgroup.findAll(), Lambda.having(on(ReceituarioItemDTO.class).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado().getTipoReceita(), Matchers.equalTo(TipoReceita.RECEITA_AMARELA)));

            if (CollectionUtils.isNotNullEmpty(selectBranca)) {
                TipoReceita tipoReceita;
                if (selectBranca.get(0).getReceituarioItem().getProduto() != null) {
                    tipoReceita = selectBranca.get(0).getReceituarioItem().getProduto().getTipoReceita();
                } else {
                    tipoReceita = selectBranca.get(0).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado();
                }

                if (CollectionUtils.isNotNullEmpty(selectBasica) && RepositoryComponentDefault.NAO.equals(separaReceituarioAutomaticamente)) {
                    selectBranca.addAll(selectBasica);
                }
                gerarReceituario(selectBranca, tipoReceita, selectBranca.get(0).getReceita(), selectBranca.get(0).getUuid().toString());
            }

            if (CollectionUtils.isNotNullEmpty(selectAntimicrobiana)) {
                TipoReceita tipoReceita;
                if (selectAntimicrobiana.get(0).getReceituarioItem().getProduto() != null) {
                    tipoReceita = selectAntimicrobiana.get(0).getReceituarioItem().getProduto().getTipoReceita();
                } else {
                    tipoReceita = selectAntimicrobiana.get(0).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado();
                }
                gerarReceituario(selectAntimicrobiana, tipoReceita, selectAntimicrobiana.get(0).getReceita(), selectAntimicrobiana.get(0).getUuid().toString());
            }

            if (CollectionUtils.isNotNullEmpty(selectBasica) && (RepositoryComponentDefault.SIM.equals(separaReceituarioAutomaticamente) || CollectionUtils.isEmpty(selectBranca))) {
                TipoReceita tipoReceita;
                if (selectBasica.get(0).getReceituarioItem().getProduto() != null) {
                    tipoReceita = selectBasica.get(0).getReceituarioItem().getProduto().getTipoReceita();
                } else {
                    tipoReceita = selectBasica.get(0).getReceituarioItem().getTipoReceitaProdutoNaoCadastrado();
                }
                gerarReceituario(selectBasica, tipoReceita, selectBasica.get(0).getReceita(), selectBasica.get(0).getUuid().toString());
            }

            if (CollectionUtils.isNotNullEmpty(selectAzul)) {
                for (ReceituarioItemDTO itemDTOAzul : selectAzul) {
                    TipoReceita tipoReceita;
                    if (itemDTOAzul.getReceituarioItem().getProduto() != null) {
                        tipoReceita = itemDTOAzul.getReceituarioItem().getProduto().getTipoReceita();
                    } else {
                        tipoReceita = itemDTOAzul.getReceituarioItem().getTipoReceitaProdutoNaoCadastrado();
                    }
                    gerarReceituario(Collections.singletonList(itemDTOAzul), tipoReceita, itemDTOAzul.getReceita(), itemDTOAzul.getUuid().toString());
                }
            }

            if (CollectionUtils.isNotNullEmpty(selectAmarela)) {
                for (ReceituarioItemDTO itemDTOAmarela : selectAmarela) {
                    TipoReceita tipoReceita;
                    if (itemDTOAmarela.getReceituarioItem().getProduto() != null) {
                        tipoReceita = itemDTOAmarela.getReceituarioItem().getProduto().getTipoReceita();
                    } else {
                        tipoReceita = itemDTOAmarela.getReceituarioItem().getTipoReceitaProdutoNaoCadastrado();
                    }
                    gerarReceituario(Collections.singletonList(itemDTOAmarela), tipoReceita, itemDTOAmarela.getReceita(), itemDTOAmarela.getUuid().toString());
                }
            }

            this.codigos.add(codigoReceituarioIteracao);

        }
        if (receituarioItemMagistral != null) {
            salvarReceituarioMagistral(receituarioItemMagistral);
        }
    }

    private void gerarReceituario(List<ReceituarioItemDTO> subgroup, TipoReceita tipoReceita, Integer nroReceita, String uuid) throws ValidacaoException, DAOException {
        for (ReceituarioItemDTO item : subgroup) {
            if (item.getReceituarioMedicamentoNaoPadronizadoDTO() != null) {
                item.getReceituarioItem().setReceituarioItemNaoPadronizado(item.getReceituarioMedicamentoNaoPadronizadoDTO().getReceituarioItemNaoPadronizado());
            }
        }

        List<ReceituarioItem> itens = extract(subgroup, on(ReceituarioItemDTO.class).getReceituarioItem());
        if (receituarioIteracao == null) {
            receituarioIteracao = new DefinerPropertiesCloning().define(this.receituario);
            itens = new DefinerPropertiesCloning().define(itens);
        }
        if (nroReceita != null) {
            HashMap<Integer, String> mapAnotacoes = receituarioDTO.getMapAnotacoes();
            receituarioIteracao.setAnotacao(mapAnotacoes.get(nroReceita));
        } else {
            receituarioIteracao.setAnotacao(receituarioDTO.getMapAnotacoesReceitasDivididasAutomaticamente().get(uuid));
        }

        List<ReceituarioItemComponentesDTO> receituarioItemComponentesDTOList = new ArrayList<>();

        for (ReceituarioItem item : itens) {
            ReceituarioItemComponentesDTO receituarioItemComponentesDTO = new ReceituarioItemComponentesDTO();
            receituarioItemComponentesDTO.setReceituarioItem(item);
            receituarioItemComponentesDTOList.add(receituarioItemComponentesDTO);
        }
        receituarioIteracao.setTipoReceita(tipoReceita);

        Receituario saveReceituario = BOFactory.getBO(ReceituarioFacade.class).salvarReceituarioReturn(receituarioIteracao, receituarioItemComponentesDTOList);
        codigoReceituarioIteracao = saveReceituario.getCodigo();
        lstSaveReceituario.add(saveReceituario);

        receituarioIteracao = null;

    }

    private void salvarReceituarioMagistral(ReceituarioItem receituarioItem) throws ValidacaoException, DAOException {
        ReceituarioItemComponentesDTO receituarioItemComponentesDTO = new ReceituarioItemComponentesDTO();
        receituarioItemComponentesDTO.setReceituarioItem(receituarioItem);

        if (receituarioIteracao == null) {
            receituarioIteracao = new DefinerPropertiesCloning().define(this.receituario);
        }

        receituarioIteracao.setTipoReceita(getTipoReceitaMagistral());
        Receituario saveReceituario = BOFactory.getBO(ReceituarioFacade.class).salvarReceituarioReturn(receituarioIteracao, Collections.singletonList(receituarioItemComponentesDTO));
        lstSaveReceituario.add(saveReceituario);

    }

    private TipoReceita getTipoReceitaMagistral() {
        return LoadManager.getInstance(TipoReceita.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoReceita.PROP_TIPO_RECEITA), TipoReceita.RECEITA_MAGISTRAL))
                .start().getVO();
    }

    public List<Long> getCodigos() {
        return codigos;
    }

    public List<Receituario> getReceituarios() {
        return lstSaveReceituario;
    }

}
