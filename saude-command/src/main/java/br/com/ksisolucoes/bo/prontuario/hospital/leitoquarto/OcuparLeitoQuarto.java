package br.com.ksisolucoes.bo.prontuario.hospital.leitoquarto;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;

/**
 * <AUTHOR>
 * Criado em: Apr 17, 2013
 */
public class OcuparLeitoQuarto extends AbstractCommandTransaction{

    private Long codigoLeito;
    private Long codigoAtendimento;
    private LeitoQuarto leitoHospital;

    public OcuparLeitoQuarto(Long codigoLeito, Long codigoAtendimento) {
        this.codigoLeito = codigoLeito;
        this.codigoAtendimento = codigoAtendimento;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        leitoHospital = HibernateUtil.lockTable(LeitoQuarto.class, codigoLeito);
        
        if(! LeitoQuarto.Situacao.LIBERADO.value().equals(leitoHospital.getSituacao())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_leito_definido_nao_liberado"));
        }
        
        leitoHospital.setSituacao(LeitoQuarto.Situacao.OCUPADO.value());

        Atendimento atendimento = (Atendimento) getSession().get(Atendimento.class, codigoAtendimento);
        leitoHospital.setAtendimento(atendimento);
        leitoHospital.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        
        BOFactory.save(leitoHospital);
    }

    public LeitoQuarto getLeitoQuarto() {
        return leitoHospital;
    }

}
