/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia;
import br.com.ksisolucoes.vo.exame.ExameProfissionalCompetencia;
import br.com.ksisolucoes.vo.exame.ExameProfissionalSemana;
import br.com.ksisolucoes.vo.exame.ExameUnidadeProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalCompetenciaPercentual;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProfissionalSemanaPercentual;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestadorCompetencia;
import org.hibernate.SQLQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DeletarCotaUnidade extends AbstractCommandTransaction {

    private Date competencia;

    public DeletarCotaUnidade(Date competencia) {
        this.competencia = competencia;
    }


    @Override
    public void execute() throws ValidacaoException {
        deletarCompetenciaUnidade();
        deletarCotaProfissional();

        Loggable.log.info("Cota da Competência " + DataUtil.getFormatarMesAno(competencia) + " deletada com sucesso!");
    }

    private void logCompetenciaNaoCadastrada(String ref) {
        Loggable.log.info("nao existe cota cadastrada para " + ref);
    }

    private boolean competenciaCadastrada(List<Long> idCompetencia) {
        return !idCompetencia.isEmpty();
    }
    private void deletarCompetenciaUnidade() {
        List<Long> idExamePrestadorCompetenciaList = getExamePrestadorCompetenciaList();
        if(competenciaCadastrada(idExamePrestadorCompetenciaList)) {
            deletarExamePrestadorCompetencia(idExamePrestadorCompetenciaList);

            List<Long> idExameUnidadeCompetenciaList = getExameUnidadeCompetenciaList();
            List<Long> idExameUnidadeProcedimentoCompetenciaList
                    = getExameUnidadeProcedimentoCompetenciaList(idExameUnidadeCompetenciaList);
            deletarExameUnidadeProcedimentoCompetencia(idExameUnidadeProcedimentoCompetenciaList);
            deletarExameUnidadeCompetencia(idExameUnidadeCompetenciaList);
            List<Long> idExameUnidadePrestadorCompetenciaList = getExameUnidadePrestadorCompetenciaList();
            deletarExameUnidadePrestadorCompetencia(idExameUnidadePrestadorCompetenciaList);
        } else {
            logCompetenciaNaoCadastrada(ExamePrestadorCompetencia.REF);
        }
    }

    private void deletarExamePrestadorCompetencia(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExamePrestadorCompetencia.REF);
        String delete = "delete from exame_prestador_competencia where cd_prestador_competencia in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarExameUnidadeProcedimentoCompetencia(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameUnidadeProcedimentoCompetencia.REF);
        String delete = "delete from exame_unidade_proc_competencia where cd_uni_exa_proc_comp in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarExameUnidadeCompetencia(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameUnidadeCompetencia.REF);
        String delete = "delete from exame_unidade_competencia where cd_uni_exa_competencia in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarExameUnidadePrestadorCompetencia(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameUnidadePrestadorCompetencia.REF);
        String delete = "delete from exame_unidade_prestador_comp where cd_uni_pre_competencia in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void logDeletados(List<?> list) { Loggable.log.info("deletados " + list.size() + " itens"); }

    /**
     * Deleta colunas da exame_profissional_competencia com a competencia informada e todas as suas dependencias:
     * exa_prof_comp_percentual, exa_prof_semana_percentual e exame_profissional_semana
     */
    private void deletarCotaProfissional() {
        List<Long> idCotaProfissionalList = getCotaProfissionalList();
        if(competenciaCadastrada(idCotaProfissionalList)) {
            List<Long> idCotaSemanaList = getCotaSemanaList(idCotaProfissionalList);
            List<Long> idCotaSemanaPercentualList = getCotaSemanaPercentualList(idCotaSemanaList);
            deletarCotaSemanaPercentual(idCotaSemanaPercentualList);
            deletarCotaSemana(idCotaSemanaList);
            List<Long> idCompetenciaPercentualList = getCompetenciaPercentualList(idCotaProfissionalList);
            deletarCotaCompetenciaPercentual(idCompetenciaPercentualList);
            deletarCotaExameProfissional(idCotaProfissionalList);
        } else {
            logCompetenciaNaoCadastrada(ExameProfissionalCompetencia.REF);
        }
    }

    private void deletarCotaExameProfissional(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameProfissionalCompetencia.REF);
        String delete = "delete from exame_profissional_competencia where cd_profissional_competencia in(:codigoList)";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarCotaSemanaPercentual(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameProfissionalCompetenciaPercentual.REF);
        String delete = "delete from exa_prof_semana_percentual where cd_exa_prof_semana_percentual in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarCotaSemana(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameProfissionalSemana.REF);
        String delete = "delete from exame_profissional_semana where cd_exame_profissional_semana in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletarCotaCompetenciaPercentual(List<Long> codigoList) {
        Loggable.log.info("deletando " + ExameProfissionalCompetenciaPercentual.REF);
        String delete = "delete from exa_prof_comp_percentual where cd_exa_prof_comp_percentual in(:codigoList) ";
        deletar(delete, codigoList);
        logDeletados(codigoList);
    }

    private void deletar(String delete, List<Long> codigoList) {
        SQLQuery query = getSession().createSQLQuery(delete);
        if(CollectionUtils.isNotNullEmpty(codigoList)) {
            query.setParameterList("codigoList", codigoList);
            query.executeUpdate();
        }
    }

    private List<Long> getExamePrestadorCompetenciaList() {
        return getSession().createCriteria(ExamePrestadorCompetencia.class)
                .add(Restrictions.eq(ExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, competencia))
                .setProjection(Projections.property(ExamePrestadorCompetencia.PROP_CODIGO))
                .list();
    }

    private List<Long> getExameUnidadeCompetenciaList() {
        return getSession().createCriteria(ExameUnidadeCompetencia.class)
                .add(Restrictions.eq(ExameUnidadeCompetencia.PROP_DATA_COMPETENCIA, competencia))
                .setProjection(Projections.property(ExameUnidadeCompetencia.PROP_CODIGO))
                .list();
    }

    private List<Long> getExameUnidadeProcedimentoCompetenciaList(List<Long> idsExameUnidadeCompetenciaList) {
        return getSession().createCriteria(ExameUnidadeProcedimentoCompetencia.class)
                .add(Restrictions.in(VOUtils.montarPath(ExameUnidadeProcedimentoCompetencia.PROP_EXAME_UNIDADE_COMPETENCIA,
                        ExameUnidadeCompetencia.PROP_CODIGO), idsExameUnidadeCompetenciaList))
                .setProjection(Projections.property(ExameUnidadeProcedimentoCompetencia.PROP_CODIGO))
                .list();
    }

    private List<Long> getExameUnidadePrestadorCompetenciaList() {
        return getSession().createCriteria(ExameUnidadePrestadorCompetencia.class)
                .add(Restrictions.eq(ExameUnidadePrestadorCompetencia.PROP_DATA_COMPETENCIA, competencia))
                .setProjection(Projections.property(ExameUnidadePrestadorCompetencia.PROP_CODIGO))
                .list();
    }

    private List<Long> getCotaSemanaList(List<Long> idsCotaProfissionalList) {
        return getSession().createCriteria(ExameProfissionalSemana.class)
                .add(Restrictions.in(VOUtils.montarPath(ExameProfissionalSemana.PROP_EXAME_PROFISSIONAL_COMPETENCIA,
                        ExameProfissionalCompetencia.PROP_CODIGO), idsCotaProfissionalList))
                .setProjection(Projections.property(ExameProfissionalSemana.PROP_CODIGO))
                .list();
    }

    private List<Long> getCotaSemanaPercentualList(List<Long> cotaSemanaList) {
        return getSession().createCriteria(ExameProfissionalSemanaPercentual.class)
                .add(Restrictions.in(VOUtils.montarPath(ExameProfissionalSemanaPercentual.PROP_EXAME_PROFISSIONAL_SEMANA,
                        ExameProfissionalSemana.PROP_CODIGO), cotaSemanaList))
                .setProjection(Projections.property(ExameProfissionalSemanaPercentual.PROP_CODIGO))
                .list();
    }

    private List<Long> getCompetenciaPercentualList(List<Long> idsCotaProfissionalList) {
        return getSession().createCriteria(ExameProfissionalCompetenciaPercentual.class)
                .add(Restrictions.in(VOUtils.montarPath(ExameProfissionalCompetenciaPercentual.PROP_EXAME_PROFISSIONAL_COMPETENCIA,
                        ExameProfissionalCompetencia.PROP_CODIGO), idsCotaProfissionalList))
                .setProjection(Projections.property(ExameProfissionalCompetenciaPercentual.PROP_CODIGO))
                .list();
    }

    private List<Long> getCotaProfissionalList() {
        return getSession().createCriteria(ExameProfissionalCompetencia.class)
                .add(Restrictions.eq(ExameProfissionalCompetencia.PROP_DATA_COMPETENCIA, competencia))
                .setProjection(Projections.property(ExameProfissionalCompetencia.PROP_CODIGO))
                .list();
    }
}
