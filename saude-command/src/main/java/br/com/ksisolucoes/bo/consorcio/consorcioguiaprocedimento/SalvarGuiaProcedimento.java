package br.com.ksisolucoes.bo.consorcio.consorcioguiaprocedimento;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.cota.dominio.CotaUtilizacaoGuiasHelper;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioGuiaProcedimentoItemDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.SalvarGuiaProcedimentoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.consorcio.util.ConsorcioHelper;
import com.amazonaws.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarGuiaProcedimento extends AbstractCommandTransaction {

    private final SalvarGuiaProcedimentoDTO dto;
    private ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
    private final List<ConsorcioGuiaProcedimentoItemDTO> itens;
    private Double aliquotaPadraoINSSPatronal;
    private TipoConta tipoContaProcedimentosFisico;
    private final String codigoSisregGuia;
    private final String chaveSisregGuia;
    private final Date dataSolicitacaoSisregGuia;
    private final ConsorcioPrestadorAgendaHorario consorcioPrestadorAgendaHorario;
    private boolean efetuarBaixaSaldoNoPagamentoDaGuia = false;

    public SalvarGuiaProcedimento(SalvarGuiaProcedimentoDTO dto) {
        this.dto = dto;
        this.consorcioGuiaProcedimento = this.dto.getConsorcioGuiaProcedimento();
        this.itens = this.dto.getItens();
        this.codigoSisregGuia = this.dto.getCodigoSisregGuia();
        this.chaveSisregGuia = this.dto.getChaveSisregGuia();
        this.dataSolicitacaoSisregGuia = this.dto.getDataSolicitacaoSisregGuia();
        this.consorcioPrestadorAgendaHorario = this.dto.getConsorcioPrestadorAgendaHorario();
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        efetuarBaixaSaldoNoPagamentoDaGuia = isEfetuarBaixaSaldoNoPagamentoDaGuia();
        validacoesGerais();

        /*
            O imposto de INSS Patronal é calculado no momento do cadastro da Guia, sendo este salvo na coluna setValorProcedimentoImposto
            Este imposto aplica-se somente a prestadores de pessoa física, e diferente dos outros impostos é calculado no momento do cadastro da guia
            O valor reservado neste caso é o valor da guia + INSS Patronal, sende este valor debitado da conta no momento do pagamento da guia.

         */

        aliquotaPadraoINSSPatronal = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("aliquotaPadraoINSSPatronal");
        tipoContaProcedimentosFisico = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("tipoContaProcedimentosFisico");

        if(!dto.isAuditoriaGuias()) {
            boolean isPrestadorPessoaFisica = Pessoa.PESSOA_FISICA.equals(this.consorcioGuiaProcedimento.getConsorcioPrestador().getEmpresaPrestador().getFlagFisicaJuridica());

            if(Coalesce.asDouble(aliquotaPadraoINSSPatronal) > 0D && tipoContaProcedimentosFisico != null){ //com imposto INSS Patronal
                if(isPrestadorPessoaFisica){
                    salvarGuiaProcedimentoComImpostoINSSPatronal();
                } else {
                    salvarGuiaProcedimentoSemImpostoINSSPatronal();
                }
            } else {
                salvarGuiaProcedimentoSemImpostoINSSPatronal();
            }
        } else
            salvarGuiaProcedimento();
    }

    private boolean isEfetuarBaixaSaldoNoPagamentoDaGuia() {
        try {
            return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO)
                    .getParametro("efetuarBaixaDoSaldoNoPagametoDaGuia"));
        } catch (DAOException e) {
            return false;
        }
    }

    private void salvarGuiaProcedimentoSemImpostoINSSPatronal() throws DAOException, ValidacaoException {
        SubConta subConta = HibernateUtil.lockTable(SubConta.class, consorcioGuiaProcedimento.getSubConta().getCodigo());

        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

        if(this.consorcioGuiaProcedimento.getStatus() == null) {
            this.consorcioGuiaProcedimento.setStatus(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value());
        }

        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);
        String time = String.valueOf(Data.getDataAtual().getTime());
        time = time.substring(time.length()-6, time.length()-1);
        this.consorcioGuiaProcedimento.setNumeroChave(time+this.consorcioGuiaProcedimento.getCodigo());
        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);

        if(this.consorcioPrestadorAgendaHorario != null) {
            this.consorcioPrestadorAgendaHorario.setConsorcioGuiaProcedimento(this.consorcioGuiaProcedimento);
            BOFactory.save(this.consorcioPrestadorAgendaHorario);
        }

        Double valorTotalReservar = 0D;

        for (ConsorcioGuiaProcedimentoItemDTO dto : itens) {
            ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem = dto.getConsorcioGuiaProcedimentoItem();
            consorcioGuiaProcedimentoItem.setStatus(ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
            consorcioGuiaProcedimentoItem.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);

            if (ConsorcioHelper.isProcedimentoSisregPorGuia(consorcioGuiaProcedimentoItem.getConsorcioProcedimento().getProcedimento().getCodigo())) {
                if (consorcioGuiaProcedimentoItem.getCodigoSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setCodigoSisreg(codigoSisregGuia);
                }
                if (consorcioGuiaProcedimentoItem.getNumeroSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setNumeroSisreg(chaveSisregGuia);
                }
                if (consorcioGuiaProcedimentoItem.getDataSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setDataSisreg(dataSolicitacaoSisregGuia);
                }
            }
            BOFactory.save(consorcioGuiaProcedimentoItem);
            CotaUtilizacaoGuiasHelper.recalculaValorCota(consorcioGuiaProcedimento);

            if (Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().getEmpresa().getTipoUnidade())) {
                consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(consorcioGuiaProcedimentoItem.getQuantidade());
                dto.setValorTotal(new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidadeAplicacao())).doubleValue());
            }

            valorTotalReservar = new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(consorcioGuiaProcedimentoItem.getQuantidade()).somar(valorTotalReservar).doubleValue();

        }

        if (!efetuarBaixaSaldoNoPagamentoDaGuia) {
            BOFactory.getBO(ConsorcioFacade.class).gerarValorReservado(new ValorReservadoDTO(subConta, consorcioGuiaProcedimento.getAnoCadastro(), valorTotalReservar));
        }

        if(Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().getEmpresa().getTipoUnidade())){
            BOFactory.getBO(ConsorcioFacade.class).confirmarUtilizacaoGuia(consorcioGuiaProcedimento, itens);
        }
    }

    private void salvarGuiaProcedimentoComImpostoINSSPatronal() throws DAOException, ValidacaoException {

        Double impostoINSSPatronalDecimal = new Dinheiro(Coalesce.asDouble(aliquotaPadraoINSSPatronal)).dividir(100D).doubleValue();


        SubConta subConta = HibernateUtil.lockTable(SubConta.class, consorcioGuiaProcedimento.getSubConta().getCodigo());

        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

        if(this.consorcioGuiaProcedimento.getStatus() == null) {
            this.consorcioGuiaProcedimento.setStatus(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value());
        }

        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);
        String time = String.valueOf(Data.getDataAtual().getTime());
        time = time.substring(time.length()-6, time.length()-1);
        this.consorcioGuiaProcedimento.setNumeroChave(time+this.consorcioGuiaProcedimento.getCodigo());
        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);

        this.consorcioPrestadorAgendaHorario.setConsorcioGuiaProcedimento(this.consorcioGuiaProcedimento);
        BOFactory.save(this.consorcioPrestadorAgendaHorario);

        Double valorTotalReservar = 0D;

        for (ConsorcioGuiaProcedimentoItemDTO dto : itens) {
            ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem = dto.getConsorcioGuiaProcedimentoItem();

            Double valorTotalImpostoINSSPatronalProcedimento;
            Double valorTotalProcedimento;
            // cálculos do imposto
            valorTotalProcedimento = new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidade())).doubleValue();
            valorTotalImpostoINSSPatronalProcedimento = new Dinheiro(valorTotalProcedimento).multiplicar(impostoINSSPatronalDecimal).doubleValue();

            consorcioGuiaProcedimentoItem.setValorProcedimentoImposto(valorTotalImpostoINSSPatronalProcedimento);
            consorcioGuiaProcedimentoItem.setStatus(ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
            consorcioGuiaProcedimentoItem.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);

            if (ConsorcioHelper.isProcedimentoSisregPorGuia(consorcioGuiaProcedimentoItem.getConsorcioProcedimento().getProcedimento().getCodigo())) {
                if(consorcioGuiaProcedimentoItem.getCodigoSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setCodigoSisreg(codigoSisregGuia);
                }
                if(consorcioGuiaProcedimentoItem.getNumeroSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setNumeroSisreg(chaveSisregGuia);
                }
                if(consorcioGuiaProcedimentoItem.getDataSisreg() == null) {
                    consorcioGuiaProcedimentoItem.setDataSisreg(dataSolicitacaoSisregGuia);
                }
            }

            BOFactory.save(consorcioGuiaProcedimentoItem);
            CotaUtilizacaoGuiasHelper.recalculaValorCota(consorcioGuiaProcedimento);

            if (Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().getEmpresa().getTipoUnidade())) {
                consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(consorcioGuiaProcedimentoItem.getQuantidade());
                dto.setValorTotal(new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidadeAplicacao())).doubleValue());
            }

            valorTotalReservar = new Dinheiro(valorTotalProcedimento).somar(valorTotalReservar).somar(valorTotalImpostoINSSPatronalProcedimento).doubleValue();

        }

        BOFactory.getBO(ConsorcioFacade.class).gerarValorReservado(new ValorReservadoDTO(subConta, consorcioGuiaProcedimento.getAnoCadastro(), valorTotalReservar));

        if (Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().getEmpresa().getTipoUnidade())) {
            BOFactory.getBO(ConsorcioFacade.class).confirmarUtilizacaoGuia(consorcioGuiaProcedimento, itens);
        }
    }

    private void salvarGuiaProcedimento() throws DAOException, ValidacaoException {
        SubConta subConta = HibernateUtil.lockTable(SubConta.class, consorcioGuiaProcedimento.getSubConta().getCodigo());

        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

        if(this.consorcioGuiaProcedimento.getStatus() == null) {
            this.consorcioGuiaProcedimento.setStatus(ConsorcioGuiaProcedimento.StatusGuiaProcedimentoAudit.PENDENTE.value());
        }

        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);
        String time = String.valueOf(Data.getDataAtual().getTime());
        time = time.substring(time.length()-6, time.length()-1);
        this.consorcioGuiaProcedimento.setNumeroChave(time+this.consorcioGuiaProcedimento.getCodigo());
        this.consorcioGuiaProcedimento = BOFactory.save(this.consorcioGuiaProcedimento);

        Double valorTotalReservar = 0D;

        for (ConsorcioGuiaProcedimentoItemDTO dto : itens) {
            ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem = dto.getConsorcioGuiaProcedimentoItem();
            consorcioGuiaProcedimentoItem.setStatus(ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem.ABERTA.value());
            consorcioGuiaProcedimentoItem.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);

            BOFactory.save(consorcioGuiaProcedimentoItem);
            CotaUtilizacaoGuiasHelper.recalculaValorCota(consorcioGuiaProcedimento);

            if (Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().getEmpresa().getTipoUnidade())) {
                consorcioGuiaProcedimentoItem.setQuantidadeAplicacao(consorcioGuiaProcedimentoItem.getQuantidade());
                dto.setValorTotal(new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(Coalesce.asLong(consorcioGuiaProcedimentoItem.getQuantidadeAplicacao())).doubleValue());
            }

            valorTotalReservar = new Dinheiro(consorcioGuiaProcedimentoItem.getValorProcedimento()).multiplicar(consorcioGuiaProcedimentoItem.getQuantidade()).somar(valorTotalReservar).doubleValue();

        }

        BOFactory.getBO(ConsorcioFacade.class).gerarValorReservado(new ValorReservadoDTO(subConta, consorcioGuiaProcedimento.getAnoCadastro(), valorTotalReservar));
    }


    private void validacoesGerais() throws ValidacaoException {
        if(!StringUtils.isNullOrEmpty(consorcioGuiaProcedimento.getCnsPaciente() )){
            if(! CnsValidator.validaCns(consorcioGuiaProcedimento.getCnsPaciente().replaceAll("[^0-9]", ""), false)){
                throw new ValidacaoException(Bundle.getStringApplication("msg_cns_invalido"));
            }
        }
    }

    public SalvarGuiaProcedimentoDTO getSalvarGuiaProcedimentoDTO() {
        return this.dto;
    }

    public void recalcularValorUtilizadoCota(){

    }


}
