package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.covid19.FichaCovidUtils;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.SalvarSoapNode;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV3;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.service.TempStore;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.hamcrest.Matchers;

import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper.validarCamposGestacao;
import static br.com.ksisolucoes.vo.cadsus.ProfissionalHelper.isMedicCbo;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.SOAP, refClass = SoapDTO.class)
public class ValidarSoapNode extends AbstractCommandValidacaoV3<SoapDTO> {

    public ValidarSoapNode(Atendimento atendimento) {
        super(atendimento, true, atendimento.getProfissional());
    }

    @Override
    public SoapDTO executarValidacao(SoapDTO object) throws DAOException, ValidacaoException {
        if (object != null) {

            if (FichaCovidUtils.isCondutaCovidInvalida(getAtendimento(), object.getCondutaCovid())) {
                validacao(Bundle.getStringApplication("msg_informe_conduta_covid"));
            }

            if (object.getAtendimentoPrimario() != null && object.getAtendimentoPrimario().getAtendimento() != null
                    && object.getAtendimentoPrimario().getAtendimento().getProfissional() != null && object.getAtendimentoPrimario().getAtendimento().getEmpresa() != null) {

                TabelaCbo tabelaCbo = object.getAtendimentoPrimario().getAtendimento().getProfissional().getCboProfissional(object.getAtendimentoPrimario().getAtendimento().getEmpresa());
                if (isMedicCbo(tabelaCbo) && isCidAvaliacaoNotInformed(object)) {
                    validacao(Bundle.getStringApplication("msg_validacao_cid_avaliacao"));
                }
            }

            validarESUS(object);

            validarSoapObrigatorio(object);

            validarDadosAtendimentoPrimario(object);

            validarExigenciaPesoAltura(object.getAtendimentoPrimario());

            validarCiapSexoPaciente(object.getCiapMotivoConsulta());
            validarCiapSexoPaciente(object.getCiapIntervencao());
            validarCiapSexoPaciente(object);

            GrupoProblemasCondicoes grupoProblemasCondicoesCid = null;
            GrupoProblemasCondicoes grupoProblemasCondicoesCiap = null;

            if (CollectionUtils.isNotNullEmpty(object.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList())) {
                grupoProblemasCondicoesCid = Lambda.selectFirst(object.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList(),
                        having(on(GrupoProblemasCondicoes.class).getCid(), Matchers.notNullValue()));

                grupoProblemasCondicoesCiap = Lambda.selectFirst(object.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList(),
                        having(on(GrupoProblemasCondicoes.class).getCiap(), Matchers.notNullValue())
                                .and(having(on(GrupoProblemasCondicoes.class).getCiap().getReferencia(), Matchers.not(Matchers.isIn(CiapHelper.getOcorrenciasList().keySet().toArray())))));

                if (grupoProblemasCondicoesCiap == null) {
                    grupoProblemasCondicoesCiap = Lambda.selectFirst(object.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList(),
                            having(on(GrupoProblemasCondicoes.class).getCiap(), Matchers.notNullValue()));
                }
            }

            try {
                Long obrigatorioInformarCIAP = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("obrigatorioInformarCIAP");

                if (object.isVisibleCid()) {
                    obrigatorioInformarCIAP = RepositoryComponentDefault.NAO_LONG;

//                    if(grupoProblemasCondicoesCid == null || grupoProblemasCondicoesCid.getCid() == null){
//                        validacao(Bundle.getStringApplication("msg_adicione_pelo_menos_um_cid_grupo_problema_condicao_detectada"));
//                    } else {
//                        atendimento.setCidPrincipal(grupoProblemasCondicoesCid.getCid());
//                    }
                }

                if (RepositoryComponentDefault.SIM_LONG.equals(obrigatorioInformarCIAP) && grupoProblemasCondicoesCiap == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menos_um_ciap_grupo_problema_condicao_detectada"));
                }
//                if (RepositoryComponentDefault.SIM_LONG.equals(obrigatorioInformarCIAP) && object.getCiapMotivoConsulta() == null) {
//                    throw new ValidacaoException("Por favor, informe ao menos um CIAP do Motivo da Consulta.");
//                }
                if (RepositoryComponentDefault.SIM_LONG.equals(obrigatorioInformarCIAP) && object.getCiapIntervencao() == null) {
                    throw new ValidacaoException("Por favor, informe ao menos um CIAP da Intervenção.");
                }
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }

            if (grupoProblemasCondicoesCiap != null) {
                atendimento.setCiap(grupoProblemasCondicoesCiap.getCiap());
            }

            List<UsuarioCadsus> lstUsuarioCadsusValidacao = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, getAtendimento().getUsuarioCadsus()))
                    .setMaxResults(1).start().getList();


            Date dataNascimento = null;
            if (CollectionUtils.isNotNullEmpty(lstUsuarioCadsusValidacao)) {
                dataNascimento = lstUsuarioCadsusValidacao.get(0).getDataNascimento();
            }

            if (object.getDumGestante() != null && dataNascimento != null) {
                if (object.getDumGestante().before(dataNascimento)) {
                    validacao(Bundle.getStringApplication("msg_data_dum_menor_menstruacao"));
                }
            }

            if (RepositoryComponentDefault.SIM_LONG.equals(object.getDiarreia())) {
                if (object.getAtendimentoMDDA().getComSangue() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_diarreia_com_sangue"));
                }

                if (object.getAtendimentoMDDA().getDataPrimeirosSintomas() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_data_primeiros_sintomas"));
                }
            }

            if (!TipoAtendimento.TipoClassificacao.ODONTOLOGICA.value().equals(getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao())) {
                validarDadosGrupoDadosAtendimento(object);
            }

            validarCamposGestacao(atendimento, object.getDumGestante(), this);

            if(!isCidAvaliacaoNotInformed(object)) {
                for(GrupoProblemasCondicoes p :  object.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList()) {
                    if(p.getCid() != null && p.getCid().getFlagRegistroDiarreia() != null) {
                        validarRelacaoCidComRegistroDiarreia(p.getCid(), object.getDiarreia());
                    }
                }
            }
        }

        return object;
    }

    private void validarRelacaoCidComRegistroDiarreia(Cid cid, Long registroDiarreia) {
        if(RepositoryComponentDefault.SIM_LONG.equals(cid.getFlagRegistroDiarreia()) && RepositoryComponentDefault.NAO_LONG.equals(registroDiarreia)) {
            validacao(Bundle.getStringApplication("msg_erro_cid_informado_exige_registro_diarreia"));
        }
    }


    private boolean isCidAvaliacaoNotInformed(SoapDTO soapDTO) {
        return soapDTO.getProblemaCondicaoDetectadaDTO() == null
                || soapDTO.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList() == null
                || soapDTO.getProblemaCondicaoDetectadaDTO().getGrupoProblemasCondicoesList().isEmpty();
    }

    private void validarDadosGrupoDadosAtendimento(SoapDTO object) {
        //            O campo conduta deve ser obrigatório quando o tipo do atendimento estiver configurado para o e-sus
        //            tipo_atendimento.tp_atendimento_esus <> NULO
        if (atendimento != null && atendimento.getNaturezaProcuraTipoAtendimento() != null && atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento() != null
                && atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoEsus() != null && object.getConduta() == null && Coalesce.asLong(object.getDiasRetorno()) == 0) {
            validacao(Bundle.getStringApplication("msg_informe_conduta"));
        } else {
            validarRetorno(object);
        }

        if (object.getProfissionalAuxiliar() != null && object.getCboProfissionalAuxiliar() == null) {
            validacao(Bundle.getStringApplication("msg_informe_cbo_profissional_auxiliar"));
        }
    }

    private void validarDadosAtendimentoPrimario(SoapDTO obj) {
        if (obj != null && obj.getAtendimentoPrimario() != null && obj.getAtendimentoPrimario().getAtendimento() != null) {
            if (obj.getAtendimentoPrimario().getPeso() != null) {
                if (obj.getAtendimentoPrimario().getPeso() < 0D || obj.getAtendimentoPrimario().getPeso() > 500000) {
                    validacao(Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_peso")));
                }
            }

            if (obj.getAtendimentoPrimario().getTemperatura() != null) {
                if (obj.getAtendimentoPrimario().getTemperatura() != 0D && (obj.getAtendimentoPrimario().getTemperatura() < 25D || obj.getAtendimentoPrimario().getTemperatura() > 45D)) {
                    validacao(Bundle.getStringApplication("campoTemperaturaValorInvalido"));
                }
            }

            if (obj.getAtendimentoPrimario().getPressaoArterialSistolica() != null && !obj.getAtendimentoPrimario().getPressaoArterialSistolica().equals(0L)) {
                if (obj.getAtendimentoPrimario().getPressaoArterialSistolica() < 0 || obj.getAtendimentoPrimario().getPressaoArterialSistolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pas")));
                }
            }
            if (obj.getAtendimentoPrimario().getPressaoArterialDiastolica() != null && !obj.getAtendimentoPrimario().getPressaoArterialDiastolica().equals(0L)) {
                if (obj.getAtendimentoPrimario().getPressaoArterialDiastolica() < 0 || obj.getAtendimentoPrimario().getPressaoArterialDiastolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pad")));
                }
            }
        }
    }

    private void validarSoapObrigatorio(SoapDTO object) throws ValidacaoException {
        if (object.getAtendimentoSoap() != null) {
            if ((StringUtils.trimToNull(object.getAtendimentoSoap().getSubjetivo()) == null)
                    && (StringUtils.trimToNull(object.getAtendimentoSoap().getObjetivo()) == null)
                    && (StringUtils.trimToNull(object.getAtendimentoSoap().getPlano()) == null)
                    && (StringUtils.trimToNull(object.getAtendimentoSoap().getAvaliacao()) == null)) {

                throw new ValidacaoException("Informe ao menos um campo de informações do SOAP (Subjetivo, Objetivo, Avaliação, Plano).");
            }
        } else {
            throw new ValidacaoException("Informe ao menos um campo de informações do SOAP (Subjetivo, Objetivo, Avaliação, Plano). ");
        }
    }

    private void validarRetorno(SoapDTO dto) {
//        if (Coalesce.asLong(dto.getDiasRetorno()) > 0) {
//            if (dto.getConduta() == null || RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(dto.getConduta().getFlagRetorno(), RepositoryComponentDefault.NAO_LONG))) {
//                validacao(Bundle.getStringApplication("msg_informe_conduta_retorno"));
//            }
//        } else
        if (dto.getConduta() != null && RepositoryComponentDefault.SIM_LONG.equals(dto.getConduta().getFlagRetorno())
                && Coalesce.asLong(dto.getDiasRetorno()) <= 0) {
            validacao(Bundle.getStringApplication("msg_informe_dias_retorno"));
        }
    }

    private void validarESUS(SoapDTO object) throws ValidacaoException, DAOException {
        Long tipoClassificacao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao();
        if (TipoAtendimento.TipoClassificacao.ENFERMAGEM_MEDICA.value().equals(tipoClassificacao)) {
            if (TabelaCbo.NivelEnsino.SUPERIOR.value().equals(getAtendimento().getTabelaCbo().getNivelEnsino())) {
                getSessao().putClientProperty("VALIDACAO_ESUS_CONDUTA_" + getAtendimento().getCodigo(), true);
                getSessao().putClientProperty("VALIDACAO_ESUS_CLASS_ATEND_" + getAtendimento().getCodigo(), true);
                if (object == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_tipo_atendimento"));
                } else {
                    if (object.getConduta() == null && object.getClassificacaoAtendimento() == null && object.getClassificacaoAtendimento() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_tipo_atendimento_classificacao_atendimento"));
                    } else if (object.getConduta() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta"));
                    } else if (object.getClassificacaoAtendimento() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_classificacao_atendimento"));
                    } else if (object.getTipoAtendimentoEsus() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_tipo_atendimento"));
                    }
                }
            }
        }

        if (!CboFichaEsusItem.verificarCboPodeInformarCid10(getAtendimento().getProfissional().getCboProfissional(getAtendimento().getEmpresa()).getCbo())
                && ClassificacaoAtendimento.ClassificacaoEsus.OUTROS.value().equals(object.getClassificacaoAtendimento().getClassificacaoEsus()) && atendimento.getCiap() == null){

            Ciap ciap = LoadManager.getInstance(Ciap.class).addParameter(new QueryCustom.QueryCustomParameter(Ciap.PROP_REFERENCIA,"A29")).start().getVO();
            atendimento.setCiap(ciap);

        }
    }

    public void validarCiapSexoPaciente(SoapDTO dto) {

        if (CollectionUtils.isNotNullEmpty(dto.getAtendimentoSoapCiapListIntervencao())) {
            for (AtendimentoSoapCiap ciapIntervencao : dto.getAtendimentoSoapCiapListIntervencao()) {
                if (CiapHelper.isInvalidoCiapSexoPaciente(ciapIntervencao.getCiap(), getAtendimento())) {
                    validacao(Bundle.getStringApplication("intevencaoEOuProcedimentos") + ": " + Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(dto.getAtendimentoSoapCiapListMotivoConsulta())) {
            for (AtendimentoSoapCiap ciapMotivoConsulta : dto.getAtendimentoSoapCiapListMotivoConsulta()) {
                if (CiapHelper.isInvalidoCiapSexoPaciente(ciapMotivoConsulta.getCiap(), getAtendimento())) {
                    validacao(Bundle.getStringApplication("motivoConsulta") + ": " + Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(dto.getAtendimentoSoapCiapListSubjetivo())) {
            for (AtendimentoSoapCiap ciapSubjetivo : dto.getAtendimentoSoapCiapListSubjetivo()) {
                if (CiapHelper.isInvalidoCiapSexoPaciente(ciapSubjetivo.getCiap(), getAtendimento())) {
                    validacao(Bundle.getStringApplication("listaProblemas") + ": " + Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
                }
            }
        }

    }

    public void validarCiapSexoPaciente(Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, getAtendimento());
        if (ciapInvalido) {
            validacao(Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
        }
    }

    @Override
    public void processar(SoapDTO obj) throws DAOException, ValidacaoException {
    }

    @Override
    public void processar(SoapDTO object, boolean lastTemp, TempStore tempStore) throws DAOException, ValidacaoException {
        if (object != null) {
            new SalvarSoapNode(atendimento, object, lastTemp, tempStore.getProfissional(), tempStore.getTabelaCbo()).start();

            //Salvar apenas se o registro for o mesmo profissional (lastTemp) que esta finalizando ou registro novo
            //Essa condição é para evitar erro A different object with the same identifier value was already associated with the session
            //Quando mais de um profissional acessa o nó, gera registro no tempStore para cada profissional de atendimentoPrimario dentro DTO do nó
            //ao salvar vai mudar o version e gerar erro no próximo registro.
            if (lastTemp || (object.getAtendimentoPrimario() != null && object.getAtendimentoPrimario().getCodigo() == null)) {
                if (CiapHelper.registraAvaliacao(object.getAtendimentoPrimario())) {
                    object.getAtendimentoPrimario().setAtendimento(atendimento);
                    BOFactory.getBO(AtendimentoFacade.class).registrarAvaliacaoPrimaria(object.getAtendimentoPrimario(), false, atendimento.getProfissional());
                }

                BOFactory.getBO(AtendimentoFacade.class).registrarProgramasSaude(atendimento, object.getAtendimentoPrimario(), false);
            }

        }
    }

    private void validarExigenciaPesoAltura(AtendimentoPrimario atendimentoPrimario) throws DAOException, ValidacaoException {
        Long idadeUsuarioCadsus = getAtendimento().getUsuarioCadsus().getIdade();
        Integer idadeInicialParaAcompanhamentoSemestralAdulto = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("idadeInicialParaAcompanhamentoSemestralAdulto");
        Integer idadeFinalParaAcompanhamentoSemestralAdulto = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("idadeFinalParaAcompanhamentoSemestralAdulto");
        String parametroExigePesoAltura = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exigePesoAltura");

        if (RepositoryComponentDefault.SIM.equals(parametroExigePesoAltura)) {
            if (idadeUsuarioCadsus >= idadeInicialParaAcompanhamentoSemestralAdulto && idadeUsuarioCadsus <= idadeFinalParaAcompanhamentoSemestralAdulto) {
                if (atendimentoPrimario.getAltura() == null && atendimentoPrimario.getPeso() == null) {
                    validacao(Bundle.getStringApplication("msg_obrigatorio_informar_altura_peso"));
                } else if (atendimentoPrimario.getAltura() == null && atendimentoPrimario.getPeso() != null) {
                    validacao(Bundle.getStringApplication("msg_obrigatorio_informar_altura"));
                } else if (atendimentoPrimario.getAltura() != null && atendimentoPrimario.getPeso() == null) {
                    validacao(Bundle.getStringApplication("msg_obrigatorio_informar_peso"));
                }
            }
        }
    }
}
