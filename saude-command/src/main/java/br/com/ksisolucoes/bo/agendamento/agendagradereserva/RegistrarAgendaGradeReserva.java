package br.com.ksisolucoes.bo.agendamento.agendagradereserva;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeReservaDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeReserva;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * Created by sulivan on 29/01/19.
 */
public class RegistrarAgendaGradeReserva extends AbstractCommandTransaction {

    private AgendaGradeReservaDTO dto;
    private AgendaGradeReserva agendaGradeReserva;

    public RegistrarAgendaGradeReserva(AgendaGradeReservaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validacoes();

        agendaGradeReserva = BOFactory.save(dto.getAgendaGradeReserva());

        BOFactory.getBO(AgendamentoFacade.class).gerarAgendaGradeReservaOcorrencia(agendaGradeReserva.getCodigo(),
                Bundle.getStringApplication("msg_reserva_horario_cadastrada_X_as_X", Data.formatar(agendaGradeReserva.getDataInicio()),
                        Data.formatarHora(agendaGradeReserva.getDataInicio()), Data.formatarHora(agendaGradeReserva.getDataFim())));
    }
    private void validacoes() throws ValidacaoException, DAOException {
        if (dto.getAgendaGradeReserva().getTempoReserva() % dto.getAgendaGradeReserva().getAgendaGradeAtendimento().getTempoMedio() != 0L) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_valor_tempo_consulta_deve_ser_multiplo_valor_tempo_medio_agenda_tempo_medio_x",
                    dto.getAgendaGradeReserva().getAgendaGradeAtendimento().getTempoMedio()));
        }

        try{
            Date data = dto.getAgendaGradeReserva().getAgendaGradeAtendimento().getAgendaGrade().getData();
            Date horaInicio = dto.getHoraInicial();

            Date dataFormatada = Data.addMinutos(Data.getDateTime(data, horaInicio), dto.getAgendaGradeReserva().getTempoReserva().intValue());

            if (DataUtil.compareHour(dataFormatada, dto.getAgendaGradeReserva().getAgendaGradeAtendimento().getAgendaGrade().getHoraFinal()) > 0) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_soma_horario_tempo_consulta_nao_podem_ultrapassar_hora_final_definada_agenda"));
            }
            dto.getAgendaGradeReserva().setDataInicio(DataUtil.mergeDataHora(data, horaInicio));
            dto.getAgendaGradeReserva().setDataFim(dataFormatada);

            List<AgendaGradeAtendimentoHorario> conflitoHorarios = BOFactory.getBO(AgendamentoFacade.class)
                    .consultarConflitoHorariosAgendaPersonalizada(dto.getAgendaGradeReserva().getAgendaGradeAtendimento(), horaInicio, dto.getAgendaGradeReserva().getDataFim());

            if (CollectionUtils.isNotNullEmpty(conflitoHorarios)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_horario_conflita_horario_agendado_favor_verificar_tempo_consulta"));
            }

            List<AgendaGradeReserva> conflitoHorariosReserva = BOFactory.getBO(AgendamentoFacade.class)
                    .consultarConflitoHorariosAgendaGradeReserva(dto.getAgendaGradeReserva().getAgendaGradeAtendimento(), horaInicio, dto.getAgendaGradeReserva().getDataFim());

            if (CollectionUtils.isNotNullEmpty(conflitoHorariosReserva)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_horario_conflita_horario_reservado_favor_verificar_tempo_consulta"));
            }
        } catch (ParseException ex) {
            throw new ValidacaoException(ex.getMessage());
        }
    }

    public AgendaGradeReserva getAgendaGradeReserva() {
        return agendaGradeReserva;
    }
}
