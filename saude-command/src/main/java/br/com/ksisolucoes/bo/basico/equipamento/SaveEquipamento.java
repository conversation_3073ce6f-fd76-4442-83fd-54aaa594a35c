package br.com.ksisolucoes.bo.basico.equipamento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipamento;
import org.hibernate.Query;

/**
 * <AUTHOR>
 */
public class SaveEquipamento extends SaveVO {

    private Equipamento equipamento;

    public SaveEquipamento(Object vo) {
        super(vo);
        this.equipamento = (Equipamento) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.equipamento.getDescricao() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_digite_descricao"));
        }
        if (this.equipamento.getTipoEquipamento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_tipo_equipamento"));
        }
        if (this.equipamento.getCodigo() == null) {
            this.equipamento.setCodigo(getProximoCodigo());
        }

        this.equipamento.setDataAtualizacao(Data.getDataAtual());

        if (this.equipamento.getStatus() == null) {
            this.equipamento.setStatus(Equipamento.Status.ATIVO.value());
        }

        if (this.equipamento.getDataCadastro() == null) {
            this.equipamento.setDataCadastro(DataUtil.getDataAtual());
        }
    }

    public String getProximoCodigo() throws DAOException {
        HQLHelper hql = new HQLHelper();
        hql.addToSelect(" (max(cast(e.codigo as long)) +1)");
        hql.addToFrom(Equipamento.class.getName(), "e");

        Query query = this.getSession().createQuery(hql.getQuery());

        query.setFetchSize(0);

        Object obj = query.uniqueResult();

        if (obj != null) {
            return obj.toString();
        } else {
            return "1";
        }
    }

}
