package br.com.ksisolucoes.bo.consorcio.conta;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaContaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.consorcio.Conta;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaConta extends CommandQueryPager<QueryConsultaConta> {

    private QueryConsultaContaDTOParam param;

    public QueryConsultaConta(QueryConsultaContaDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("co.codigo", true);
        hql.addToSelect("co.descricao", true);
        
        hql.setTypeSelect(Conta.class.getName());
        hql.addToFrom("Conta co");
        
        hql.addToWhereWhithAnd("co.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("co.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("co.codigo || ' ' || co.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("co."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("co.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
