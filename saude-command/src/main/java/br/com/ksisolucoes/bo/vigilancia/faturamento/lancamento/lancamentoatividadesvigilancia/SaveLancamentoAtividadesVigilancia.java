package br.com.ksisolucoes.bo.vigilancia.faturamento.lancamento.lancamentoatividadesvigilancia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;

/**
 * <AUTHOR>
 */
public class SaveLancamentoAtividadesVigilancia extends SaveVO<LancamentoAtividadesVigilancia> {

    public SaveLancamentoAtividadesVigilancia(LancamentoAtividadesVigilancia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getDataCadastro() == null) {
            vo.setDataCadastro(DataUtil.getDataAtual());
        }
        vo.setDataAlteracao(DataUtil.getDataAtual());
        vo.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());

        if(vo.getTipoAtividade() == null ){
            if(vo.getRequerimentoVigilancia() != null && vo.getRequerimentoVigilancia().getTipoSolicitacao() != null && vo.getRequerimentoVigilancia().getTipoSolicitacao().getDescricao() != null){
                vo.setTipoAtividade(vo.getRequerimentoVigilancia().getTipoSolicitacao().getDescricao());
            }
        }
        if(this.vo.getNomePessoa() == null) {
            if (this.vo.getEstabelecimento() != null) {
                this.vo.setNomePessoa(this.vo.getEstabelecimento().getRazaoSocial());
            } else if (this.vo.getVigilanciaPessoa() != null) {
                this.vo.setNomePessoa(this.vo.getVigilanciaPessoa().getNome());
            }
        }
    }
}