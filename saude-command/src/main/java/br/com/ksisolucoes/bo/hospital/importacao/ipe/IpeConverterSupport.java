package br.com.ksisolucoes.bo.hospital.importacao.ipe;

import br.com.ksisolucoes.util.log.Loggable;
import java.util.List;
import org.apache.camel.Exchange;
import org.apache.camel.TypeConversionException;
import org.apache.camel.support.TypeConverterSupport;

/**
 *
 * <AUTHOR>
 */
public class IpeConverterSupport {
    
    public TypeConverterSupport getTypeConverter(){
        return new TypeConverterSupport() {
                @Override
                public <T> T convertTo(Class<T> type, Exchange exchng, Object o) throws TypeConversionException {
                    String r = "";
                    List<String> l = (List<String>) o;
                    for (String string : l) {
                        r += string + System.lineSeparator();
                    }

                    return (T) r;
                }
            };
    }
}



