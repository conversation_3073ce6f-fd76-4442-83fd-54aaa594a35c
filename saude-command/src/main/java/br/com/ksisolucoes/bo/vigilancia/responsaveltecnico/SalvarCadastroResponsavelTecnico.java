package br.com.ksisolucoes.bo.vigilancia.responsaveltecnico;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroResponsavelTecnicoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ResponsavelTecnicoAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnicoAnexo;
import java.util.List;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SalvarCadastroResponsavelTecnico extends AbstractCommandTransaction<SalvarCadastroResponsavelTecnico> {
    
    private final CadastroResponsavelTecnicoDTO dto;
    private ResponsavelTecnico responsavelTecnico;

    public SalvarCadastroResponsavelTecnico(CadastroResponsavelTecnicoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        responsavelTecnico = BOFactory.save(dto.getResponsavelTecnico());

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaResponsavelTecnico(dto.getCamposAlterados(), responsavelTecnico);

        List<ResponsavelTecnicoAnexoDTO> anexoList = dto.getResponsavelTecnicoAnexoDTOList();
        if (CollectionUtils.isNotNullEmpty(anexoList)) {
            GerenciadorArquivo ga;
            ResponsavelTecnicoAnexo rta;
            for (ResponsavelTecnicoAnexoDTO anexoDTO : anexoList) {
                if(anexoDTO.getResponsavelTecnico() != null && anexoDTO.getResponsavelTecnico().getCodigo() != null){
                    continue;
                }
                ga = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(anexoDTO.getFile(), anexoDTO.getOrigem(), anexoDTO.getNomeArquivoOriginal());
                
                rta = new ResponsavelTecnicoAnexo();
                rta.setResponsavelTecnico(responsavelTecnico);
                rta.setGerenciadorArquivo(ga);
                rta.setDescricao(anexoDTO.getDescricaoAnexo());
        
                BOFactory.save(rta);
            }
        }
        
        List<ResponsavelTecnicoAnexoDTO> anexoExcluidoList = dto.getResponsavelTecnicoAnexoDTOExcluidoList();
        if (CollectionUtils.isNotNullEmpty(anexoExcluidoList)) {
            for (ResponsavelTecnicoAnexoDTO anexoExcluido : anexoExcluidoList) {
                List<ResponsavelTecnicoAnexo> list = this.getSession().createCriteria(ResponsavelTecnicoAnexo.class)
                        .add(Restrictions.eq(ResponsavelTecnicoAnexo.PROP_GERENCIADOR_ARQUIVO, anexoExcluido.getGerenciadorArquivo()))
                        .add(Restrictions.eq(ResponsavelTecnicoAnexo.PROP_RESPONSAVEL_TECNICO, anexoExcluido.getResponsavelTecnico()))
                        .list();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    for (ResponsavelTecnicoAnexo anexo : list) {
                        BOFactory.delete(anexo);
                        BOFactory.delete(anexo.getGerenciadorArquivo());
                    }
                }
            }
        }
    }

    public ResponsavelTecnico getResponsavelTecnico() {
        return responsavelTecnico;
    }
}