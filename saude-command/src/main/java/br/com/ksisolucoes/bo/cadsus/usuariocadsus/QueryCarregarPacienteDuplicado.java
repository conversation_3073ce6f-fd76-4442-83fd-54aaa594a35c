package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.PacienteDuplicadoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelatorioPacienteDuplicadoDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelatorioPacienteDuplicadoDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryCarregarPacienteDuplicado extends CommandQuery<QueryCarregarPacienteDuplicado> implements ITransferDataReport<QueryRelatorioPacienteDuplicadoDTOParam, QueryRelatorioPacienteDuplicadoDTO> {

    private QueryRelatorioPacienteDuplicadoDTOParam param;
    private Collection<QueryRelatorioPacienteDuplicadoDTO> result;

    public QueryCarregarPacienteDuplicado() {
    }
    
    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        hql.setTypeSelect(QueryRelatorioPacienteDuplicadoDTO.class.getName());
        
        hql.addToSelect("view.usuarioCadsusList", "usuarioCadsusList");
        hql.addToSelect("view.informacao", "informacao");
        hql.addToSelect("view.tipoDocumento", "tipoDocumento");
        
        hql.addToFrom("ViewPacientesDuplicados view");
        
        hql.addToWhereWhithAnd("view.tipoDocumento <>", TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO);
        
        hql.addToOrder("view.tipoDocumento asc");
    }
    
    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for(QueryRelatorioPacienteDuplicadoDTO dto : result){
            List<UsuarioCadsus> list = consultarDadosPaciente(dto.getUsuarioCadsusList());
            for(UsuarioCadsus uc : list){
                PacienteDuplicadoDTO pacienteDuplicado = new PacienteDuplicadoDTO();
                pacienteDuplicado.setCodigoPaciente(uc.getCodigo());
                pacienteDuplicado.setNome(uc.getNome());
                pacienteDuplicado.setNomeMae(uc.getNomeMae());
                pacienteDuplicado.setCns(uc.getCns());
                pacienteDuplicado.setCpf(uc.getCpfFormatado());
                pacienteDuplicado.setDataNascimento(uc.getDataNascimento());
                dto.getPacienteList().add(pacienteDuplicado);
            }
        }
    }
    
    private List<UsuarioCadsus> consultarDadosPaciente(String codigoUsuarioCadList){
        List<Long> longList = Lists.transform(Arrays.asList(codigoUsuarioCadList.replaceAll("[{}]", "").split(",")), Longs.stringConverter());
        
        return LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(UsuarioCadsus.PROP_CODIGO)
                .addProperty(UsuarioCadsus.PROP_NOME)
                .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                .addProperty(UsuarioCadsus.PROP_CPF)
                .addProperty(UsuarioCadsus.PROP_NOME_MAE)
                .addParameter(new QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, longList))
                .start().getList();
    }
    
    @Override
    protected void result(HQLHelper hql, Object object) {
        result = hql.getBeanList((List) object);
    }

    @Override
    public Collection<QueryRelatorioPacienteDuplicadoDTO> getResult() {
        return this.result;
    }
    
    @Override
    public void setDTOParam(QueryRelatorioPacienteDuplicadoDTOParam param) {
        this.param = param;
    }
}
