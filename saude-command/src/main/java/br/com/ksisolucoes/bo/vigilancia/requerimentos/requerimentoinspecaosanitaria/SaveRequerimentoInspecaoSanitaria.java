package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoinspecaosanitaria;


import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;

/**
 * <AUTHOR>
 */
public class SaveRequerimentoInspecaoSanitaria extends SaveVO<RequerimentoInspecaoSanitaria> {

    public SaveRequerimentoInspecaoSanitaria(RequerimentoInspecaoSanitaria vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}