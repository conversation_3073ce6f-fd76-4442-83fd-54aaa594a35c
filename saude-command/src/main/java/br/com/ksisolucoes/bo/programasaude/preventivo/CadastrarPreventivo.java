package br.com.ksisolucoes.bo.programasaude.preventivo;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.programasaude.interfaces.facade.PreventivoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.basico.ParametroProcedimento;
import br.com.ksisolucoes.vo.programasaude.Preventivo;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CadastrarPreventivo extends AbstractCommandTransaction {

    private Preventivo preventivo;

    public CadastrarPreventivo(Preventivo preventivo) {
        this.preventivo = preventivo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ProcedimentoCompetencia procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .setLazyMode(true)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), CargaBasicoPadrao.getInstance().getParametroProcedimento().getPropertyValue(ParametroProcedimento.PROP_PROCEDIMENTO_PREVENTIVO)))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO)))
                .start().getVO();

        if (procedimentoCompetencia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_preventivo_nao_valido_para_data_competencia"));
        }

        preventivo = BOFactory.getBO(PreventivoFacade.class).save(preventivo);

        salvarAtendimentoProntuario();

        BOFactory.getBO(AtendimentoFacade.class).gerarProcedimento(AtendimentoItem.ORIGEM_ATENDIMENTO_ENFERMAGEM,
                Bundle.getStringApplication("rotulo_preventivo"),
                AtendimentoItem.TIPO_ORIGEM_PREVENTIVO,
                preventivo.getProfissional(),
                procedimentoCompetencia.getId().getProcedimento(),
                preventivo.getAtendimento().getCodigo());
    }

    public Preventivo getPreventivo() {
        return this.preventivo;
    }

    private void salvarAtendimentoProntuario() throws DAOException, ValidacaoException {
         AtendimentoProntuario atendimentoProntuario = (AtendimentoProntuario) getSession().createCriteria(AtendimentoProntuario.class)
                .add(Restrictions.eq(AtendimentoProntuario.PROP_PREVENTIVO, preventivo))
                .add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO, preventivo.getAtendimento()))
                .uniqueResult();

        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.PREVENTIVO.value(), preventivo.getAtendimento(), preventivo.getCodigo());
        }

        atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setDescricao(getDescricaoPreventivoFormatado());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.PREVENTIVO.value());
        atendimentoProntuario.setUsuarioCadsus(preventivo.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(preventivo.getAtendimento().getEmpresa());
        atendimentoProntuario.setProfissional(preventivo.getProfissional());
        atendimentoProntuario.setAtendimento(preventivo.getAtendimento());
        atendimentoProntuario.setPreventivo(preventivo);
        if(preventivo.getAtendimento() != null){
            atendimentoProntuario.setTabelaCbo(preventivo.getAtendimento().getTabelaCbo());            
        }

        BOFactory.save(atendimentoProntuario);
    }

    private String getDescricaoPreventivoFormatado() {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append("<u>Requisição de Exame Citopatológico - Colo do Útero</u>");
        descricao.append("\n<br/>");
        descricao.append(Bundle.getStringApplication("rotulo_motivo_exame"));
        descricao.append(": ");
        descricao.append(preventivo.getMotivoExameDescricao());
        descricao.append("   ");
        descricao.append(Bundle.getStringApplication("rotulo_inspecao_colo"));
        descricao.append(": ");
        descricao.append(preventivo.getExameColoDescricao());

        return descricao.toString();
    }
}
