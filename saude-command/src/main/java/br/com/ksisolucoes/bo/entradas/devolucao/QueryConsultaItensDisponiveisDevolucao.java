package br.com.ksisolucoes.bo.entradas.devolucao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DevolucaoMedicamentoItem2DTO;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.QueryConsultaItensDisponiveisDevolucaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.vo.saidas.devolucao.DevolucaoMedicamento;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaItensDisponiveisDevolucao extends CommandQuery<QueryConsultaItensDisponiveisDevolucao> {

    private QueryConsultaItensDisponiveisDevolucaoDTOParam param;
    private List<DevolucaoMedicamentoItem2DTO> result;

    public QueryConsultaItensDisponiveisDevolucao(QueryConsultaItensDisponiveisDevolucaoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {

        hql.setTypeSelect(DevolucaoMedicamentoItem2DTO.class.getName());

        hql.addToSelectAndGroup("produto.codigo", "produto.codigo");
        hql.addToSelectAndGroup("produto.referencia", "produto.referencia");
        hql.addToSelectAndGroup("produto.descricao", "produto.descricao");

        hql.addToSelectAndGroup("unidade.codigo", "unidade.codigo");
        hql.addToSelectAndGroup("unidade.unidade", "unidade.unidade");

        hql.addToSelect("sum(coalesce(dispensacaoItemLote.quantidade,0) - coalesce(dispensacaoItemLote.quantidadeDevolvida,0))", "quantidadeDispensada");

        hql.addToSelectAndGroup("dispensacaoMedicamento.dataReceita", "dataPrescricao");
        hql.addToSelectAndGroup("dispensacaoMedicamento.dataDispensacao", "dataDispensacao");

        hql.addToSelectAndGroup("receituarioItem.codigo", "dispensacaoMedicamentoItem.receituarioItem.codigo");
        hql.addToSelectAndGroup("receituarioItem.nomeProduto", "dispensacaoMedicamentoItem.receituarioItem.nomeProduto");
        hql.addToSelectAndGroup("receituario.codigo", "dispensacaoMedicamentoItem.receituarioItem.receituario.codigo");
        hql.addToSelectAndGroup("receituario.dataReceituario", "dispensacaoMedicamentoItem.receituarioItem.receituario.dataReceituario");

        hql.addToSelectAndGroup("grupoEstoque.id.grupo", "grupoEstoque.id.grupo");

        hql.addToSelectAndGroup("dispensacaoMedicamentoItem.codigo", "dispensacaoMedicamentoItem.codigo");
        hql.addToSelectAndGroup("dispensacaoMedicamentoItem.posologia", "dispensacaoMedicamentoItem.posologia");
        hql.addToSelectAndGroup("dispensacaoMedicamentoItem.tipo", "dispensacaoMedicamentoItem.tipo");
        hql.addToSelectAndGroup("produto.codigo", "dispensacaoMedicamentoItem.produto.codigo");
        hql.addToSelectAndGroup("produto.descricao", "dispensacaoMedicamentoItem.produto.descricao");
        hql.addToSelectAndGroup("produto.referencia", "dispensacaoMedicamentoItem.produto.referencia");
        hql.addToSelectAndGroup("dispensacaoItemLote.codigo", "codigoDispensacaoItemLote");

        hql.addToFrom("DispensacaoItemLote dispensacaoItemLote "
                + " left join dispensacaoItemLote.grupoEstoque grupoEstoque"
                + " left join dispensacaoItemLote.dispensacaoMedicamentoItem dispensacaoMedicamentoItem"
                + " left join dispensacaoMedicamentoItem.receituarioItem receituarioItem"
                + " left join receituarioItem.receituario receituario"
                + " left join dispensacaoMedicamentoItem.dispensacaoMedicamento dispensacaoMedicamento"
                + " left join dispensacaoMedicamentoItem.produto produto"
                + " left join produto.unidade unidade"
                + " left join dispensacaoMedicamento.atendimento atendimento"
                + " left join atendimento.atendimentoPrincipal atendimentoPrincipal"
                + " left join dispensacaoMedicamento.usuarioCadsusDestino usuarioCadsus");

        hql.addToWhereWhithAnd("coalesce(dispensacaoMedicamentoItem.quantidadeDevolvida,0) < coalesce(dispensacaoMedicamentoItem.quantidadeDispensada,0)");

        hql.addToWhereWhithAnd("usuarioCadsus = ", param.getUsuarioCadsus());
        Long parametroTipoConsulta = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("TipoConsulta");
        if (DevolucaoMedicamento.TIPO_CONSULTA_ATENDIMENTO.equals(parametroTipoConsulta)) {
            hql.addToWhereWhithAnd("atendimentoPrincipal = ", param.getAtendimento());
        } else {
            Long parametroDiasDevolucao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("diasDevolução");
            hql.addToWhereWhithAnd("dispensacaoMedicamento.dataDispensacao >= ", Data.removeDias(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial(), parametroDiasDevolucao.intValue()));
        }

        hql.addToOrder("produto.descricao");
        hql.addToOrder("receituario.dataReceituario asc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected Object executeQuery(Query query) {
        return super.executeQuery(query);
    }

    @Override
    public List<DevolucaoMedicamentoItem2DTO> getResult() {
        return result;
    }
}
