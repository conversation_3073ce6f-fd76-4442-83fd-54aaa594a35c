package br.com.ksisolucoes.bo.web.programas;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class AdicionarFavorito extends AbstractCommandTransaction {

    private Long codigoPrograma; 
    private ProgramaFavorito programaFavorito;

    public AdicionarFavorito(Long codigoPrograma) {
        this.codigoPrograma = codigoPrograma;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long indice = (Long) getSession().createCriteria(ProgramaFavorito.class)
                .setProjection(Projections.max(ProgramaFavorito.PROP_INDICE))
                .add(Restrictions.eq(VOUtils.montarPath(ProgramaFavorito.PROP_USUARIO, Usuario.PROP_CODIGO), getSessao().getCodigoUsuario()))
                .uniqueResult();
        
        ProgramaWeb programaWeb = (ProgramaWeb) getSession().get(ProgramaWeb.class, codigoPrograma);
        
        programaFavorito = (ProgramaFavorito) getSession().createCriteria(ProgramaFavorito.class)
                .add(Restrictions.eq(VOUtils.montarPath(ProgramaFavorito.PROP_USUARIO, Usuario.PROP_CODIGO), getSessao().getCodigoUsuario()))
                .add(Restrictions.eq(VOUtils.montarPath(ProgramaFavorito.PROP_PROGRAMA_WEB), programaWeb))
                .uniqueResult();
        
        if (programaFavorito == null) {
            programaFavorito = new ProgramaFavorito();
            programaFavorito.setProgramaWeb(programaWeb);
            programaFavorito.setUsuario(getSessao().<Usuario>getUsuario());
            if (Coalesce.asLong(indice) == 0L) {
                programaFavorito.setIndice(Coalesce.asLong(indice));
            } else {
                programaFavorito.setIndice(indice+1);
            }

            BOFactory.save(programaFavorito);
        }
        
    }

    public ProgramaFavorito getProgramaFavorito() {
        return programaFavorito;
    }
    
}
