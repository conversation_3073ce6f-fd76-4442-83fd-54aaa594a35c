package br.com.ksisolucoes.bo.portal.menu;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.portal.PortalMenu;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class ConsultarMenu extends CommandQuery{

    private List<PortalMenuDTO> result;
    
    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        PortalMenuDTO pDto = on(PortalMenuDTO.class);

        hql.setTypeSelect(PortalMenuDTO.class.getName());
        
        hql.addToSelect("menu.codigo", path(pDto.getPortalMenu().getCodigo()));
        hql.addToSelect("menu.bundle", path(pDto.getPortalMenu().getBundle()));
        hql.addToSelect("menu.status", path(pDto.getPortalMenu().getStatus()));
        hql.addToSelect("menu.nivel", path(pDto.getPortalMenu().getNivel()));
        hql.addToSelect("Mpagina.codigo", path(pDto.getPortalMenu().getPaginaPrincipal().getCodigo()));
        hql.addToSelect("Mpagina.caminhoPagina", path(pDto.getPortalMenu().getPaginaPrincipal().getCaminhoPagina()));
        
        hql.addToSelect("pai.codigo", path(pDto.getPortalMenu().getMenuPai().getCodigo()));
        hql.addToSelect("pai.bundle", path(pDto.getPortalMenu().getMenuPai().getBundle()));
        hql.addToSelect("pai.status", path(pDto.getPortalMenu().getMenuPai().getStatus()));
        hql.addToSelect("pai.nivel", path(pDto.getPortalMenu().getMenuPai().getNivel()));
        hql.addToSelect("Ppagina.codigo", path(pDto.getPortalMenu().getMenuPai().getPaginaPrincipal().getCodigo()));
        hql.addToSelect("Ppagina.caminhoPagina", path(pDto.getPortalMenu().getMenuPai().getPaginaPrincipal().getCaminhoPagina()));
        
        hql.addToWhereWhithAnd("menu.status =", PortalMenu.Status.ATIVO.getValue());
        
        hql.addToFrom("PortalMenu menu"
                + " left join menu.paginaPrincipal Mpagina"
                + " left join menu.menuPai pai"
                + " left join pai.paginaPrincipal Ppagina");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        Collections.sort(result, new Comparator<PortalMenuDTO>() {
            @Override
            public int compare(PortalMenuDTO o1, PortalMenuDTO o2) {
                return o1.getPortalMenu().getDescricaoMenu().compareTo(o2.getPortalMenu().getDescricaoMenu());
            }
        });

        List<PortalMenuDTO> dtoList = new ArrayList<PortalMenuDTO>();
        
        for (PortalMenuDTO result1 : result) {
            if(result1.getPortalMenu().getMenuPai() == null){
                dtoList.add(result1);
            }
        }
        result.removeAll(dtoList);
        
        for (PortalMenuDTO r : result) {
            for (PortalMenuDTO dto : dtoList) {
                if(dto.getPortalMenu().equals(r.getPortalMenu().getMenuPai())){
                    dto.getSubmenu().add(r);
                    break;
                }
            }
        }
        
        result = dtoList;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List)result, false);
    }

    @Override
    public List<PortalMenuDTO> getResult() {
        return result;
    }
}
