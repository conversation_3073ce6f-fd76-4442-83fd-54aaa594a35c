package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque.SaveMovimentoEstoque;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoMedicamentoAplicacaoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.PrescricaoInternaEnfermagemDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.RegistroHorarioAtendimentoMedicamentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedicamentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.Cuidados;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Restrictions;
import org.jetbrains.annotations.NotNull;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.PRESCRICAO_INTERNA_ENFERMAGEM, refClass = PrescricaoInternaEnfermagemDTO.class)
public class ValidarPrescricaoInternaEnfermagemNode extends AbstractCommandValidacaoV2<PrescricaoInternaEnfermagemDTO> {

    public ValidarPrescricaoInternaEnfermagemNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public PrescricaoInternaEnfermagemDTO executarValidacao(PrescricaoInternaEnfermagemDTO object) throws DAOException, ValidacaoException {
        return object;
    }

    @Override
    public void processar(PrescricaoInternaEnfermagemDTO object) throws DAOException, ValidacaoException {
        if (object != null) {
            ReceituarioItem ri;
            Cuidados c;
            
            List<ReceituarioItem> receituarioItems = new ArrayList<>();
            registroAtendimentoMedicamento(object, receituarioItems);

            registroAtendimentoSolucao(object, receituarioItems);

            registroAtendimentoCuidado(object);

            if (!receituarioItems.isEmpty()) {
                gerarAtendimentoProntuario(receituarioItems);
            }
        }
    }

    private void registroAtendimentoMedicamento(PrescricaoInternaEnfermagemDTO object, List<ReceituarioItem> receituarioItems) throws DAOException, ValidacaoException {
        ReceituarioItem ri;
        if(CollectionUtils.isNotNullEmpty(object.getRegistroHorarioAtendimentoMedicamentoDTOList())){
            for (RegistroHorarioAtendimentoMedicamentoDTO registroAtendimentoMedicamento : object.getRegistroHorarioAtendimentoMedicamentoDTOList()) {
                if(CollectionUtils.isNotNullEmpty(registroAtendimentoMedicamento.getLstAtendimentoAplicacaoDTO())){
                    for (AtendimentoMedicamentoAplicacaoDTO dto : registroAtendimentoMedicamento.getLstAtendimentoAplicacaoDTO()) {
                        AtendimentoMedicamento am = dto.getAtendimentoMedicamento();
                        if (am != null && am.getCodigo() == null) {
                            am.setAtendimento(getAtendimento());
                            am.setDataHoraAplicacao(dto.getDataHoraAplicacao());
                            am.setReceituarioItem(dto.getReceituarioItem());
                            am = BOFactory.save(am);

                            ri = (ReceituarioItem) getSession().load(ReceituarioItem.class, dto.getReceituarioItem().getCodigo());
                            ri.setAtendimentoMedicamento(am);
                            if (registroAtendimentoMedicamento.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(ri.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                ri.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                            }
                            ri = BOFactory.save(ri);

                            dto.setReceituarioItem(ri);

                            AtendimentoItem atendimentoItem = gerarAtendimentoItem(am.getAtendimento());
                            gerarInsumos(dto, atendimentoItem);
                            receituarioItems.add(dto.getReceituarioItem());
                        } else if (am != null && am.getCodigo() != null && registroAtendimentoMedicamento.isFinalizado()) {
                            ri = (ReceituarioItem) getSession().load(ReceituarioItem.class, am.getReceituarioItem().getCodigo());
                            ri.setAtendimentoMedicamento(am);
                            if (registroAtendimentoMedicamento.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(ri.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                ri.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                            }
                            BOFactory.save(ri);
                        }
                    }
                } else if(registroAtendimentoMedicamento.isFinalizado()){
                    registroAtendimentoMedicamento.getReceituarioItem().setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                    BOFactory.save(registroAtendimentoMedicamento.getReceituarioItem());

                } else if (registroAtendimentoMedicamento.isAprovado()) {
                    ri = (ReceituarioItem) getSession().load(ReceituarioItem.class, registroAtendimentoMedicamento.getReceituarioItem().getCodigo());
                    ri.setFlagAprovarAdministracaoMedicamento(RepositoryComponentDefault.SIM_LONG);
                    ri.setDataHoraAprovacao(registroAtendimentoMedicamento.getReceituarioItem().getDataHoraAprovacao());
                    AtendimentoMedicamento am = getAtendimentoMedicamento(registroAtendimentoMedicamento);
                    am = BOFactory.save(am);
                    ri.setAtendimentoMedicamento(am);
                    ri = BOFactory.save(ri);
                    registroAtendimentoMedicamento.setReceituarioItem(ri);
                    gerarAtendimentoItem(am.getAtendimento());
                    receituarioItems.add(ri);

                }
            }
        }
    }

    private @NotNull AtendimentoMedicamento getAtendimentoMedicamento(RegistroHorarioAtendimentoMedicamentoDTO registroAtendimentoMedicamento) {
        AtendimentoMedicamento am = new AtendimentoMedicamento();
        am.setAtendimento(getAtendimento());
        am.setDataHoraAplicacao(registroAtendimentoMedicamento.getReceituarioItem().getDataHoraAprovacao());
        am.setReceituarioItem(registroAtendimentoMedicamento.getReceituarioItem());
        return am;
    }

    private void registroAtendimentoSolucao(PrescricaoInternaEnfermagemDTO object, List<ReceituarioItem> receituarioItems) throws DAOException, ValidacaoException {
        ReceituarioItem ri;
        if(CollectionUtils.isNotNullEmpty(object.getRegistroHorarioAtendimentoSolucaoDTOList())){
            for (RegistroHorarioAtendimentoMedicamentoDTO registroAtendimentoSolucao : object.getRegistroHorarioAtendimentoSolucaoDTOList()) {
                if(CollectionUtils.isNotNullEmpty(registroAtendimentoSolucao.getLstAtendimentoAplicacaoDTO())){
                    for (AtendimentoMedicamentoAplicacaoDTO dto : registroAtendimentoSolucao.getLstAtendimentoAplicacaoDTO()) {
                        AtendimentoMedicamento am = dto.getAtendimentoMedicamento();
                        if (am != null && am.getCodigo() == null) {
                            am.setAtendimento(getAtendimento());
                            am.setDataHoraAplicacao(dto.getDataHoraAplicacao());
                            am.setReceituarioItem(dto.getReceituarioItem());
                            am = BOFactory.save(am);

                            ri = (ReceituarioItem) getSession().load(ReceituarioItem.class, dto.getReceituarioItem().getCodigo());
                            ri.setAtendimentoMedicamento(am);
                            if (registroAtendimentoSolucao.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(ri.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                ri.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                            }
                            ri = BOFactory.save(ri);

                            dto.setReceituarioItem(ri);

                            AtendimentoItem atendimentoItem = gerarAtendimentoItem(am.getAtendimento());
                            gerarInsumos(dto, atendimentoItem);
                            receituarioItems.add(dto.getReceituarioItem());
                        } else if (am != null && am.getCodigo() != null && registroAtendimentoSolucao.isFinalizado()) {
                            ri = (ReceituarioItem) getSession().load(ReceituarioItem.class, am.getReceituarioItem().getCodigo());
                            ri.setAtendimentoMedicamento(am);
                            if (registroAtendimentoSolucao.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(ri.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                ri.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                            }
                            BOFactory.save(ri);
                        }
                    }
                } else if(registroAtendimentoSolucao.isFinalizado()){
                    registroAtendimentoSolucao.getReceituarioItem().setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                    BOFactory.save(registroAtendimentoSolucao.getReceituarioItem());
                }
            }
        }
    }

    private void registroAtendimentoCuidado(PrescricaoInternaEnfermagemDTO object) throws DAOException, ValidacaoException {
        Cuidados c;
        if(CollectionUtils.isNotNullEmpty(object.getRegistroHorarioAtendimentoCuidadoDTOList())){
            for (RegistroHorarioAtendimentoMedicamentoDTO registroAtendimentoCuidado : object.getRegistroHorarioAtendimentoCuidadoDTOList()) {
                if(CollectionUtils.isNotNullEmpty(registroAtendimentoCuidado.getLstAtendimentoAplicacaoDTO())){
                    for (AtendimentoMedicamentoAplicacaoDTO dto : registroAtendimentoCuidado.getLstAtendimentoAplicacaoDTO()) {
                        AtendimentoMedicamento am = dto.getAtendimentoMedicamento();
                        if (am != null && am.getCodigo() == null) {
                            am.setAtendimento(getAtendimento());
                            am.setDataHoraAplicacao(dto.getDataHoraAplicacao());
                            am.setCuidados(dto.getCuidados());
                            am.setReceituarioItem(dto.getReceituarioItem());
                            am = BOFactory.save(am);

                            c = (Cuidados) getSession().load(Cuidados.class, dto.getCuidados().getCodigo());
                            if (registroAtendimentoCuidado.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(c.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                c.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                                c = BOFactory.save(c);
                            }

                            dto.setCuidados(c);

                            AtendimentoItem atendimentoItem = gerarAtendimentoItem(am.getAtendimento());
                            gerarInsumos(dto, atendimentoItem);
                        } else if (am != null && am.getCodigo() != null && registroAtendimentoCuidado.isFinalizado()) {
                            c = (Cuidados) getSession().load(Cuidados.class, am.getCuidados().getCodigo());
                            if (registroAtendimentoCuidado.isFinalizado()
                                    && RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(c.getFlagFinalizadoAtendimento(), RepositoryComponentDefault.NAO_LONG))) {
                                c.setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                                BOFactory.save(c);
                            }
                        }
                    }
                } else if(registroAtendimentoCuidado.isFinalizado()){

                    registroAtendimentoCuidado.getCuidados().setFlagFinalizadoAtendimento(RepositoryComponentDefault.SIM_LONG);
                    BOFactory.save(registroAtendimentoCuidado.getCuidados());
                }
            }
        }
    }

    private AtendimentoItem gerarAtendimentoItem(Atendimento atendimento) throws DAOException, ValidacaoException {
        AtendimentoItem atendimentoItem = new AtendimentoItem();

        Procedimento procedimento;
        
        AtendimentoItem existeAtendimentoItem = (AtendimentoItem) this.getSession().createCriteria(AtendimentoItem.class)
                .add(Restrictions.eq(AtendimentoItem.PROP_ATENDIMENTO, getAtendimento()))
                .add(Restrictions.ne(AtendimentoItem.PROP_STATUS, AtendimentoItem.STATUS_CANCELADO))
                .setMaxResults(1).uniqueResult();

        if (existeAtendimentoItem != null) {
            procedimento = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("ProcedimentoAdministracaoMedicamentoNaoFaturavel");
            if (procedimento == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", "ProcedimentoAdministracaoMedicamentoNaoFaturavel"));
            }
        } else {
            procedimento = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("ProcedimentoAdministracaoMedicamento");
            if (procedimento == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_parametro_X_nao_definido", "ProcedimentoAdministracaoMedicamento"));
            }
        }
        ProcedimentoCompetencia procedimentoCompetencia = AtendimentoHelper.validaProcedimentoCompetencia(procedimento, atendimento.getUsuarioCadsus());

        atendimentoItem.setProcedimentoCompetencia(procedimentoCompetencia);
        atendimentoItem.setAtendimento(atendimento);
        atendimentoItem.setItem(1L);
        atendimentoItem.setDataHora(DataUtil.getDataAtual());
        atendimentoItem.setStatus(AtendimentoItem.STATUS_APLICADO);
        atendimentoItem.setProfissional(atendimento.getProfissional());
        atendimentoItem.setQuantidade(1D);
        atendimentoItem.setOrigem(AtendimentoItem.ORIGEM_ATENDIMENTO_ENFERMAGEM);
        atendimentoItem.setTipoOrigem(AtendimentoItem.TIPO_ORIGEM_PRESCRICAO_INTERNA);
        atendimentoItem.setCompetenciaAtendimento(atendimento.getCompetenciaAtendimento());

        BOFactory.save(atendimentoItem);

        return atendimentoItem;
    }

    private void gerarInsumos(AtendimentoMedicamentoAplicacaoDTO dto, AtendimentoItem atendimentoItem) throws DAOException, ValidacaoException {
        for (AtendimentoMedicamentoItem item : dto.getAtendimentoMedicamentoItems()) {
            Produto produto = (Produto) getSession().get(Produto.class, item.getProduto().getCodigo());
            try {
                if (RepositoryComponentDefault.SIM.equals(item.getBaixaEstoque())) {
                    movimentarEstoque(atendimentoItem.getCodigo().toString(), atendimentoItem.getItem(), produto, item.getQuantidade(), item.getGrupoEstoque());
                }

                item.setAtendimentoMedicamento(dto.getAtendimentoMedicamento());
                BOFactory.save(item);
            } catch (ValidacaoException ex) {
                throw new ValidacaoException(
                        Bundle.getStringApplication(
                                "msg_procedimento_X",
                                atendimentoItem.getProcedimentoCompetencia().getId().getProcedimento().getDescricao(), StringUtilKsi.removeHtmlString(ex.getMessage())
                        )
                );
            }
        }
    }

    private void movimentarEstoque(String documento, Long item, Produto produto, Double quantidade, String grupoEstoque) throws ValidacaoException, DAOException {
        MovimentoEstoquePK pk = new MovimentoEstoquePK();
        pk.setDataMovimentacao(DataUtil.getDataAtual());
        pk.setEmpresa(atendimento.getEmpresa());
        MovimentoEstoque me = new MovimentoEstoque(pk);
        me.setProduto(produto);
        me.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        me.setNomePessoa(atendimento.getNomePaciente());
        me.setObservacao("");
        me.setNumeroDocumento(documento);
        me.setItemDocumento(item);
        me.setQuantidade(quantidade);
        me.setGrupoEstoque(grupoEstoque);
        me.setTipoDocumento((TipoDocumento) BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("TipoDocumentoSaidaProduto"));

        new SaveMovimentoEstoque(me).start();
    }

    private void gerarAtendimentoProntuario(List<ReceituarioItem> receituarioItems) throws DAOException, ValidacaoException {
        AtendimentoProntuario ap = new AtendimentoProntuario();

        ap.setData(DataUtil.getDataAtual());
        ap.setTipoRegistro(AtendimentoProntuario.TipoRegistro.ADMINISTRACAO_MEDICAMENTOS.value());
        ap.setDescricao(getDescricaoPrescricao(receituarioItems));
        ap.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        ap.setEmpresa(atendimento.getEmpresa());
        ap.setProfissional(atendimento.getProfissional());
        ap.setAtendimento(atendimento);
        ap.setTabelaCbo(atendimento.getTabelaCbo());

        BOFactory.save(ap);
    }

    private String getDescricaoPrescricao(List<ReceituarioItem> receituarioItems) {
        StringBuilder descricao = null;

        for (ReceituarioItem receituarioItem : receituarioItems) {
            if (descricao == null) {
                descricao = new StringBuilder();

                if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
                    descricao.append("<b>Registrado por: </b>");
                    descricao.append(getSessao().getUsuario().getNome());
                    descricao.append("\n<br/>");
                }

                descricao.append("<b><u>");
                descricao.append(Bundle.getStringApplication("rotulo_administracao_medicamentos"));
                descricao.append(": </b></u>");

                if(RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getFlagAprovarAdministracaoMedicamento()) &&
                        receituarioItem.getAtendimentoMedicamento().getDataHoraAplicacao()!=null &&
                        receituarioItem.getAtendimentoMedicamento().getDataHoraAplicacao().equals( receituarioItem.getDataHoraAprovacao())){
                    descricao.append(" - APROVADO");
                }
            }

            descricao.append("<br/>\n");
            descricao.append(receituarioItem.getNomeProduto());
            descricao.append(" - ");
            descricao.append(receituarioItem.getPosologia());

            if(RepositoryComponentDefault.SIM_LONG.equals(receituarioItem.getFlagAprovarAdministracaoMedicamento()) &&
                    receituarioItem.getAtendimentoMedicamento().getDataHoraAplicacao()!= null &&
                    receituarioItem.getAtendimentoMedicamento().getDataHoraAplicacao().equals( receituarioItem.getDataHoraAprovacao())){
                descricao.append(Data.formatarDataHora(receituarioItem.getDataHoraAprovacao()));
            }else{

                descricao.append(Data.formatarDataHora(receituarioItem.getAtendimentoMedicamento().getDataHoraAplicacao()));
            }
        }

        return descricao != null ? descricao.toString() : null;
    }
}
