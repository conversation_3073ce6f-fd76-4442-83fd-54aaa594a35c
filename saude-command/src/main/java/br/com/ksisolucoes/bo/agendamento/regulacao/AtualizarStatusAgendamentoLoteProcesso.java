package br.com.ksisolucoes.bo.agendamento.regulacao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.prontuario.basico.AgendamentoLoteProcesso;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class AtualizarStatusAgendamentoLoteProcesso extends AbstractCommandTransaction<AtualizarStatusAgendamentoLoteProcesso> {

    private final Long codigo;
    private final Long status;
    private final String mensagemErro;
    private final Date dataFinalizacao;
    private final Long qtdeAgendada;
    private final Long qtdeNaoAgendada;
    private final String solicitacoesAgendadas;
    private final Long quantidade;

    public AtualizarStatusAgendamentoLoteProcesso(Long codigo, Long status, String mensagemErro, Date dataFinalizacao, Long qtdeAgendada, Long qtdeNaoAgendada, String solicitacoesAgendadas, Long quantidade) {
        this.codigo = codigo;
        this.status = status;
        this.mensagemErro = mensagemErro;
        this.dataFinalizacao = dataFinalizacao;
        this.qtdeAgendada = qtdeAgendada;
        this.qtdeNaoAgendada = qtdeNaoAgendada;
        this.solicitacoesAgendadas = solicitacoesAgendadas;
        this.quantidade = quantidade;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AgendamentoLoteProcesso entity = LoadManager.getInstance(AgendamentoLoteProcesso.class)
                .addProperties(new HQLProperties(AgendamentoLoteProcesso.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_CODIGO, codigo))
                .start().getVO();

        if (status != null) {
            entity.setStatus(status);
            entity.setMensagemErro(mensagemErro);

            if (!AgendamentoLoteProcesso.Status.GERANDO.value().equals(status) &&
                    !AgendamentoLoteProcesso.Status.FILA_ESPERA.value().equals(status)) {
                entity.setDataFinalizacao(dataFinalizacao);
            }
        }

        entity.setQuantidadeAgendada(qtdeAgendada);
        entity.setQuantidadeErro(qtdeNaoAgendada);

        if (quantidade != null && quantidade > 0L) {
            entity.setQuantidade(quantidade);
        }

        if (StringUtils.isNotEmpty(solicitacoesAgendadas)) {
            entity.setSolicitacoesAgendadas("Solicitações Agendadas: " + solicitacoesAgendadas);
        }

        BOFactory.newTransactionSave(entity);
    }
}
