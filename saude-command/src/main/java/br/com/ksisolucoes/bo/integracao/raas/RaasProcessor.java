package br.com.ksisolucoes.bo.integracao.raas;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasAtencaoPsicossocialDadosPacienteBind;
import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasAtencaoPsicossocialProcedimentoBind;
import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasCabecalhoBind;
import br.com.ksisolucoes.bo.integracao.raas.dto.GeracaoRaasDTO;
import br.com.ksisolucoes.bo.integracao.raas.dto.RaasPsiDTO;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.atendimento.raas.RaasPsiItem;
import java.util.ArrayList;
import java.util.List;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class RaasProcessor<T extends IBindVoExport> implements Processor {

    private final GeracaoRaasCabecalhoBind raasCabecalhoBind;
    private final GeracaoRaasAtencaoPsicossocialDadosPacienteBind raasAtencaoPsicossocialDadosPacienteBind;
    private final GeracaoRaasAtencaoPsicossocialProcedimentoBind raasAtencaoPsicossocialProcedimentoBind;

    public RaasProcessor(GeracaoRaasCabecalhoBind raasCabecalhoBind, GeracaoRaasAtencaoPsicossocialDadosPacienteBind raasAtencaoPsicossocialDadosPacienteBind,
            GeracaoRaasAtencaoPsicossocialProcedimentoBind raasAtencaoPsicossocialProcedimentoBind) {
        this.raasCabecalhoBind = raasCabecalhoBind;
        this.raasAtencaoPsicossocialDadosPacienteBind = raasAtencaoPsicossocialDadosPacienteBind;
        this.raasAtencaoPsicossocialProcedimentoBind = raasAtencaoPsicossocialProcedimentoBind;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        List<GeracaoRaasDTO> list = (List<GeracaoRaasDTO>) exchange.getIn().getBody();
        List<T>  result = new ArrayList<T>();
        
        for (GeracaoRaasDTO geracaoRaasDTO : list) {
            GeracaoRaasCabecalhoBind cabecalhoBind = VOUtils.cloneObject((GeracaoRaasCabecalhoBind) raasCabecalhoBind);
            cabecalhoBind.buildProperties(geracaoRaasDTO);
            result.add((T) cabecalhoBind);
            
            for (RaasPsiDTO raasPsiDTO : geracaoRaasDTO.getRaasPsiDTOList()) {
                GeracaoRaasAtencaoPsicossocialDadosPacienteBind cabelhoPsicossocialBind = VOUtils.cloneObject((GeracaoRaasAtencaoPsicossocialDadosPacienteBind) raasAtencaoPsicossocialDadosPacienteBind);
                cabelhoPsicossocialBind.buildProperties(raasPsiDTO.getRaasPsi());
                result.add((T) cabelhoPsicossocialBind);
                
                for (RaasPsiItem raasPsiItem : raasPsiDTO.getRaasPsiItemList()) {
                    GeracaoRaasAtencaoPsicossocialProcedimentoBind cabelhoPsicossocialProcedimentoBind = VOUtils.cloneObject((GeracaoRaasAtencaoPsicossocialProcedimentoBind) raasAtencaoPsicossocialProcedimentoBind);
                    cabelhoPsicossocialProcedimentoBind.buildProperties(raasPsiItem);
                    result.add((T) cabelhoPsicossocialProcedimentoBind);
                }
            }
        }
        
        exchange.getOut().setBody(result);
    }
}