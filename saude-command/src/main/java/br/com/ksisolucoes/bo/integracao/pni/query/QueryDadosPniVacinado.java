package br.com.ksisolucoes.bo.integracao.pni.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.vacina.pni.interfaces.dto.QueryDadosPniVacinadoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryDadosPniVacinado extends CommandQuery {

    private Date competencia;
    private List<QueryDadosPniVacinadoDTO> result;

    public Date getCompetencia() {
        return competencia;
    }

    public void setCompetencia(Date competencia) {
        this.competencia = competencia;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryDadosPniVacinadoDTO.class.getName());

        //hql.addToSelectAndGroup("va.codigo", "vacinaAplicacao.codigo");

        hql.addToSelectAndGroup("va.usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelectAndGroup("uc.nome", "usuarioCadsus.nome");
        hql.addToSelectAndGroup("uc.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelectAndGroup("uc.sexo", "usuarioCadsus.sexo");
        hql.addToSelectAndGroup("uc.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelectAndGroup("uc.telefone", "usuarioCadsus.telefone");
        hql.addToSelectAndGroup("uc.email", "usuarioCadsus.email");
        hql.addToSelectAndGroup("uc.nacionalidade", "usuarioCadsus.nacionalidade");
        hql.addToSelectAndGroup("uc.apelido", "usuarioCadsus.apelido");
        hql.addToSelectAndGroup("uce.maeDesconhecido", "usuarioCadsusEsus.maeDesconhecido");
        hql.addToSelectAndGroup("e.codigo", "empresa.codigo");
        hql.addToSelectAndGroup("r.codigo", "usuarioCadsus.raca.codigo");
        hql.addToSelectAndGroup("c.codigo", "usuarioCadsus.enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelectAndGroup("euc.bairro", "usuarioCadsus.enderecoUsuarioCadsus.bairro");
        hql.addToSelectAndGroup("euc.logradouro", "usuarioCadsus.enderecoUsuarioCadsus.logradouro");
        hql.addToSelectAndGroup("euc.numeroLogradouro", "usuarioCadsus.enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelectAndGroup("euc.complementoLogradouro", "usuarioCadsus.enderecoUsuarioCadsus.complementoLogradouro");
        hql.addToSelectAndGroup("euc.cep", "usuarioCadsus.enderecoUsuarioCadsus.cep");
        hql.addToSelectAndGroup("ei.codigo", "usuarioCadsus.etniaIndigena.codigo");
        hql.addToSelectAndGroup("pa.codigoCns", "vacinaAplicacao.profissionalAplicacao.codigoCns");
        hql.addToSelectAndGroup("st.tipoSegmento", "tipoSegmento");

        hql.addToSelectAndGroup("pn.codigo", "usuarioCadsus.paisNascimento.codigo");
        hql.addToSelectAndGroup("pn.codigoPni", "usuarioCadsus.paisNascimento.codigoPni");

        hql.addToSelectAndGroup("cn.codigo", "usuarioCadsus.cidadeNascimento.codigo");

        hql.addToSelect("(select cast(ucc.numeroCartao as text) from UsuarioCadsusCns ucc where ucc.usuarioCadsus.codigo = va.usuarioCadsus.codigo and ucc.excluido = 0)", "cnsPaciente");

        hql.addToFrom(VacinaAplicacao.class.getName(), "va "
                + " left join va.empresa e "
                + " left join va.profissionalAplicacao pa "
                + " left join va.usuarioCadsus uc "
                + " left join uc.usuarioCadsusEsus uce "
                + " left join va.produtoVacina pv "
                + " left join uc.raca r "
                + " left join uc.paisNascimento pn "
                + " left join uc.cidadeNascimento cn "
                + " left join va.enderecoUsuarioCadsus euc "
                + " left join euc.cidade c "
                + " left join uc.etniaIndigena ei "
                + " left join uc.enderecoDomicilio ed "
                + " left join ed.equipeMicroArea ema "
                + " left join ema.equipeArea ea"
                + " left join ea.segmentoTerritorial st");

        hql.addToWhereWhithAnd("va.status in ", Arrays.asList(VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value(), VacinaAplicacao.StatusVacinaAplicacao.REAPLICADA.value()));
        hql.addToWhereWhithAnd("va.dataAplicacao between :dataInicial and :dataFinal");
        hql.addToWhereWhithAnd("pv.codigoPni is not null");
        hql.addToWhereWhithAnd("e.flagGeraProdVacinaEsus <> ", RepositoryComponentDefault.SIM_LONG);

    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        query.setDate("dataInicial", Data.adjustRangeDay(competencia).getDataInicial());
        query.setDate("dataFinal", Data.adjustRangeDay(competencia).getDataFinal());
    }

    @Override
    public List<QueryDadosPniVacinadoDTO> getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
    
}
