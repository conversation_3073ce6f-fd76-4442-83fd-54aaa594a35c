package br.com.ksisolucoes.bo.agendamento.tfd.laudotfd;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.QueryPagerConsultaCancelamentoAgendamentoLaudoDTOparam;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.QueryPagerConsultaCancelamentoLaudoDTO;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaCancelamentoAgendamentoTfd extends CommandQueryPager<QueryPagerConsultaCancelamentoAgendamentoTfd> {

    private QueryPagerConsultaCancelamentoAgendamentoLaudoDTOparam param;

    public QueryPagerConsultaCancelamentoAgendamentoTfd(QueryPagerConsultaCancelamentoAgendamentoLaudoDTOparam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryPagerConsultaCancelamentoLaudoDTO.class.getName());

        hql.addToSelect("laudoTfd.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_CODIGO));
        hql.addToSelect("laudoTfd.dataCadastro", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_DATA_CADASTRO));
        hql.addToSelect("laudoTfd.status", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_STATUS));
        hql.addToSelect("pedidoTfd.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_CODIGO));
        hql.addToSelect("pedidoTfd.numeroPedido", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_NUMERO_PEDIDO));
        hql.addToSelect("empresa.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_EMPRESA, Empresa.PROP_CODIGO));
        hql.addToSelect("empresa.descricao", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
        hql.addToSelect("usuarioCadsus.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("usuarioCadsus.nome", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        hql.addToSelect("usuarioCadsus.apelido", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO));
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL));
        hql.addToSelect("tipoProcedimento.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO));
        hql.addToSelect("tipoProcedimento.descricao", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO));
        hql.addToSelect("solicitacaoAgendamento.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO));
        hql.addToSelect("solicitacaoAgendamento.dataAgendamento", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO));
        hql.addToSelect("unidadeExecutante.codigo", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_CODIGO));
        hql.addToSelect("unidadeExecutante.descricao", VOUtils.montarPath(QueryPagerConsultaCancelamentoLaudoDTO.PROP_LAUDO_TFD, LaudoTfd.PROP_PEDIDO_TFD, PedidoTfd.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_DESCRICAO));

        hql.addToFrom("LaudoTfd laudoTfd "
                + " left join laudoTfd.pedidoTfd pedidoTfd "
                + " left join laudoTfd.empresa empresa "
                + " left join laudoTfd.usuarioCadsus usuarioCadsus "
                + " left join laudoTfd.tipoProcedimento tipoProcedimento "
                + " left join pedidoTfd.solicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.unidadeExecutante unidadeExecutante");

        if (param.getNomePaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" usuarioCadsus.nome", param.getNomePaciente(), true)
                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("usuarioCadsus.apelido", param.getNomePaciente(), true) + "))");
        }
        if (param.getTipoProcedimento() != null) {
            hql.addToWhereWhithAnd("tipoProcedimento.codigo = ", param.getTipoProcedimento().getCodigo());
        }
        if (param.getLocalAgendamento() != null) {
            hql.addToWhereWhithAnd("unidadeExecutante.codigo = ", param.getLocalAgendamento().getCodigo());
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento >= ", Data.adjustRangeHour(param.getPeriodo()).getDataInicial());
            hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento <= ", Data.adjustRangeHour(param.getPeriodo()).getDataFinal());
        }
        hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento is not null ");
        hql.addToWhereWhithAnd("solicitacaoAgendamento.dataAgendamento >= ", DataUtil.getDataAtual());

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
