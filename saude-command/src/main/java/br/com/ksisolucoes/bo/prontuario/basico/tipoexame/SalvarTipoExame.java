package br.com.ksisolucoes.bo.prontuario.basico.tipoexame;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExameCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Marques
 */
public class SalvarTipoExame extends AbstractCommandTransaction<SalvarTipoExame> {

    private TipoExame tipoExame;
    private List<TabelaCbo> tabelaCboList;

    public SalvarTipoExame(TipoExame tipoExame, List<TabelaCbo> tabelaCboList) {
        this.tipoExame = tipoExame;
        this.tabelaCboList = tabelaCboList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        tipoExame = BOFactory.save(tipoExame);

        List<TipoExameCbo> tipoExameCboList = new ArrayList();
        for (TabelaCbo tabelaCbo : tabelaCboList) {
            TipoExameCbo tipoExameCbo = new TipoExameCbo();
            tipoExameCbo.setCbo(tabelaCbo);
            tipoExameCbo.setTipoExame(tipoExame);

            tipoExameCboList.add(tipoExameCbo);
        }

        VOUtils.persistirListaVosModificados(TipoExameCbo.class, tipoExameCboList, new QueryCustom.QueryCustomParameter(TipoExameCbo.PROP_TIPO_EXAME, tipoExame));
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }
}