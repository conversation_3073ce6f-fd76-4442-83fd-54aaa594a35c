package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentonadaconsta;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoCertidaoNadaConstaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoCertidaoNadaConsta extends AbstractCommandTransaction<SalvarRequerimentoCertidaoNadaConsta> {

    private final RequerimentoCertidaoNadaConstaDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia = null;

    public SalvarRequerimentoCertidaoNadaConsta(RequerimentoCertidaoNadaConstaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        verificaNadaConsta();
        boolean gerarOcorrenciaCadastro = false;
        if (dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().getCodigo() == null) {
            dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().setNomeSolicitante(dto.getRequerimentoNadaConsta().getResponsavelTecnico().getNome());
            if (dto.getRequerimentoNadaConsta().getResponsavelTecnico().getCpf() == null || dto.getRequerimentoNadaConsta().getResponsavelTecnico().getCpf().isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_responsavel_sem_cpf_favor_informar"));
            }
            dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().setRgCpfSolicitante(dto.getRequerimentoNadaConsta().getResponsavelTecnico().getCpf());
            dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value());

            carregarConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
            }
            configurarNumeracao();

            gerarOcorrenciaCadastro = true;
        }

        dto.getRequerimentoNadaConsta().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoNadaConsta().getEstabelecimento());
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoNadaConsta().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), gerarOcorrenciaCadastro);

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);

        dto.getRequerimentoNadaConsta().setRequerimentoVigilancia(requerimentoVigilancia);

        BOFactory.save(dto.getRequerimentoNadaConsta());

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());

        if(gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    private void verificaNadaConsta() throws ValidacaoException {
        LoadManager load = LoadManager.getInstance(EstabelecimentoResponsavelTecnico.class)
                .addProperties(new HQLProperties(EstabelecimentoResponsavelTecnico.class).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_CODIGO), dto.getRequerimentoNadaConsta().getResponsavelTecnico().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), dto.getRequerimentoNadaConsta().getEstabelecimento().getCodigo()));
        List<EstabelecimentoResponsavelTecnico> lstEstabelecimentoResponsavelTecnicos = load.start().getList();
        if (CollectionUtils.isNotNullEmpty(lstEstabelecimentoResponsavelTecnicos)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_responsavel_ja_cadastro_em_outro_estabelicimento_X", lstEstabelecimentoResponsavelTecnicos.get(0).getEstabelecimento().getRazaoSocial()));
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if (configuracaoVigilancia.getAnoBaseGeral() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
        }

        long sequencial = 0L;
        if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getIniciarNumeracaoNadaConsta()) > 0L) {
            sequencial = configuracaoVigilancia.getIniciarNumeracaoNadaConsta();
        }
        sequencial++;
        String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
        Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
        dto.getRequerimentoNadaConsta().setNumeracao(nextId);

        configuracaoVigilancia.setIniciarNumeracaoNadaConsta(sequencial);

        BOFactory.save(configuracaoVigilancia);

    }
}