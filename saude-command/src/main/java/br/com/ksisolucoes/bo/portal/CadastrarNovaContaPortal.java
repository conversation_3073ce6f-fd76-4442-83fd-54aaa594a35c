package br.com.ksisolucoes.bo.portal;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.portal.interfaces.CadastroUsuarioDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.Usuario.TipoUsuario;
import br.com.ksisolucoes.vo.portal.UsuarioPortal;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CadastrarNovaContaPortal extends AbstractCommandTransaction{

    private CadastroUsuarioDTO dto;
    private Long codigoUsuarioPortal;

    public CadastrarNovaContaPortal(CadastroUsuarioDTO dto) {
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        String chaveValidacao = StringUtil.randomStringOfLength(10);
        Long numeroCartao = Long.valueOf(dto.getUsuarioPortal().getUsuario().getLogin().replaceAll("[^0-9]", ""));
        String cpf = dto.getCpf().replaceAll("[^0-9]", "");
        
        List<UsuarioCadsusCns> cnsList = LoadManager.getInstance(UsuarioCadsusCns.class)
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO), dto.getDataNascimento()))
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CPF), cpf))
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_SITUACAO), BuilderQueryCustom.QueryParameter.DIFERENTE,UsuarioCadsus.SITUACAO_EXCLUIDO))
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), RepositoryComponentDefault.NAO_EXCLUIDO))
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), numeroCartao))
            .start().getList();
        if(cnsList.isEmpty() || cnsList.size() > 1){
            throw new ValidacaoException(Bundle.getStringApplication("naoFoiPossivelLocalizarCadastroUnidadeSaude"));
        }
        
        UsuarioPortal up = (UsuarioPortal) getSession().createCriteria(UsuarioPortal.class)
                .add(Restrictions.eq(UsuarioPortal.PROP_USUARIO_CADSUS, cnsList.get(0).getUsuarioCadsus()))
                .uniqueResult();
        if(up != null){
            throw new ValidacaoException(Bundle.getStringApplication("jaExisteCadastroEsseCns"));
        }
        
        if (dto.getConfirmacaoSenha()!=null
                && !dto.getConfirmacaoSenha().equals(dto.getUsuarioPortal().getUsuario().getSenha())) {
            throw new ValidacaoException(Bundle.getStringApplication("confirmacao_senha_diferente_senha"));
        }
        String senhaCriptografada = Util.criptografarSenha(dto.getUsuarioPortal().getUsuario().getSenha());

        Usuario usuario = new Usuario();
        usuario.setTipoUsuario(TipoUsuario.USUARIO_PORTAL.value());
        usuario.setEmail(dto.getUsuarioPortal().getUsuario().getEmail());
        usuario.setLogin(numeroCartao.toString());
        usuario.setNome(cnsList.get(0).getUsuarioCadsus().getNome());
        usuario.setSenha(senhaCriptografada);
        usuario.setStatus(Usuario.STATUS_ATIVO);
        usuario.setNivel(Usuario.NIVEL_NORMAL);
        usuario.setFlagUsuarioTemporario(RepositoryComponentDefault.NAO_LONG);
        BOFactory.save(usuario);
        
        UsuarioPortal usuarioPortal = new UsuarioPortal();
        usuarioPortal.setUsuario(usuario);
        usuarioPortal.setUsuarioCadsus(cnsList.get(0).getUsuarioCadsus());
        usuarioPortal.setDataSolicitacao(DataUtil.getDataAtual());
        usuarioPortal.setChaveVerificacao(chaveValidacao);
        usuarioPortal.setNivelAcesso(UsuarioPortal.NivelAcesso.NORMAL.value());

        usuarioPortal = BOFactory.save(usuarioPortal);
        codigoUsuarioPortal = usuarioPortal.getCodigo();
        
        getSession().flush();//Utilizado para nao enviar o email em caso de erro devido a concorrencia.

        try{
            Email.create()
                .assunto("Cadastro no Portal do Cidadão")
                .para(dto.getUsuarioPortal().getUsuario().getEmail())
                .mensagem("Bem vindo ao Portal do Cidadão!\n\n"
                        + "Para utilizar os serviços do Portal, você deve liberar sua conta utilizando o código abaixo.\n\n"
                        + "Código de liberação: "+chaveValidacao)
                .send();
        }catch(Throwable ex){
            Loggable.log.warn("Email: "+dto.getUsuarioPortal().getUsuario().getEmail()+ " - "+ex.getMessage());
            throw new ValidacaoException("Não foi possível enviar o e-mail. Verifique se o e-mail definido está correto e tente novamente.");
        }
    }

    public Long getCodigoUsuarioPortal() {
        return codigoUsuarioPortal;
    }
    
}
