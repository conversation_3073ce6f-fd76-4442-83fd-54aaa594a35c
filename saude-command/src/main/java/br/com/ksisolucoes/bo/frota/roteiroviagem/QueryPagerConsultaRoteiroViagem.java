package br.com.ksisolucoes.bo.frota.roteiroviagem;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.frota.interfaces.dto.QueryPagerConsultaRoteiroViagemDTO;
import br.com.ksisolucoes.bo.frota.interfaces.dto.QueryPagerConsultaRoteiroViagemDTOparam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.frota.Motorista;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagemPassageiro;
import br.com.ksisolucoes.vo.frota.Veiculo;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaRoteiroViagem extends CommandQueryPager<QueryPagerConsultaRoteiroViagem> {

    private QueryPagerConsultaRoteiroViagemDTOparam param;

    public QueryPagerConsultaRoteiroViagem(QueryPagerConsultaRoteiroViagemDTOparam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryPagerConsultaRoteiroViagemDTO.class.getName());

        hql.addToSelect("(select count(*) from RoteiroViagemPassageiro rvp where (rvp.roteiro.codigo = roteiroViagem.codigo) and (rvp.usuarioCadsus is not null) and (rvp.status = " + RoteiroViagemPassageiro.Status.AGENDADO.value() + "))", QueryPagerConsultaRoteiroViagemDTO.PROP_QUANTIDADE_PASSAGEIROS);

        hql.addToSelect("roteiroViagem", new HQLProperties(RoteiroViagem.class, "roteiroViagem").getSingleProperties());
        
        hql.addToSelect("roteiroViagem.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_CODIGO));
        hql.addToSelect("roteiroViagem.dataUsuario", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_DATA_USUARIO));
        hql.addToSelect("roteiroViagem.dataCadastro", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_DATA_CADASTRO));
        hql.addToSelect("roteiroViagem.dataChegada", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_DATA_CHEGADA));
        hql.addToSelect("roteiroViagem.localSaida", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_LOCAL_SAIDA));
        hql.addToSelect("roteiroViagem.dataSaida", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_DATA_SAIDA));
        hql.addToSelect("roteiroViagem.status", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_STATUS));

        hql.addToSelect("usuario.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_USUARIO, Usuario.PROP_CODIGO));
        hql.addToSelect("usuario.nome", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_USUARIO, Usuario.PROP_NOME));
        
        hql.addToSelect("empresa.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_EMPRESA, Empresa.PROP_CODIGO));
        hql.addToSelect("empresa.descricao", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_EMPRESA, Empresa.PROP_DESCRICAO));

        hql.addToSelect("veiculo.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_VEICULO, Veiculo.PROP_CODIGO));
        hql.addToSelect("veiculo.referencia", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_VEICULO, Veiculo.PROP_REFERENCIA));
        hql.addToSelect("veiculo.descricao", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_VEICULO, Veiculo.PROP_DESCRICAO));
        hql.addToSelect("veiculo.quantidadeLugares", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_VEICULO, Veiculo.PROP_QUANTIDADE_LUGARES));
        
        hql.addToSelect("cidade.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_CIDADE, Cidade.PROP_CODIGO));
        hql.addToSelect("cidade.descricao", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_CIDADE, Cidade.PROP_DESCRICAO));
        
        hql.addToSelect("motorista.codigo", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_MOTORISTA, Motorista.PROP_CODIGO));
        hql.addToSelect("motorista.nome", VOUtils.montarPath(QueryPagerConsultaRoteiroViagemDTO.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_MOTORISTA, Motorista.PROP_NOME));

        hql.addToFrom("RoteiroViagem roteiroViagem "
                + " left join roteiroViagem.veiculo veiculo "
                + " left join roteiroViagem.empresa empresa "
                + " left join roteiroViagem.motorista motorista "
                + " left join roteiroViagem.cidade cidade "
                + " left join roteiroViagem.usuario usuario "
        );


        if (param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("empresa.codigo = ", param.getEmpresa().getCodigo());
        }
        if (param.getVeiculo() != null) {
            hql.addToWhereWhithAnd("veiculo.codigo = ", param.getVeiculo().getCodigo());
        }
        if (param.getMotorista()!= null) {
            hql.addToWhereWhithAnd("motorista.codigo = ", param.getMotorista().getCodigo());
        }
        if (param.getDestino()!= null) {
            hql.addToWhereWhithAnd("cidade.codigo = ", param.getDestino().getCodigo());
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("roteiroViagem.dataSaida >= ", Data.adjustRangeHour(param.getPeriodo()).getDataInicial());
            hql.addToWhereWhithAnd("roteiroViagem.dataSaida <= ", Data.adjustRangeHour(param.getPeriodo()).getDataFinal());
        }
        if(param.getCodigo() != null){
            hql.addToWhereWhithAnd("roteiroViagem.codigo = ", param.getCodigo());
        }
        if (param.getConfigureParam() != null && param.getConfigureParam().getProperties() != null) {
            for (Map.Entry<String, String> entry : param.getConfigureParam().getProperties().entrySet()) {
                if (entry.getKey().equals(VOUtils.montarPath(RoteiroViagem.PROP_STATUS)) && !entry.getValue().equals("Todos")) {
                    hql.addToWhereWhithAnd("roteiroViagem.status = ", Long.valueOf(entry.getValue()));
                }
            }
        }
        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
