package br.com.ksisolucoes.bo.programasaude.preventivoocorrencia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.programasaude.PreventivoOcorrencia;

/**
 * <AUTHOR>
 */
public class SavePreventivoOcorrencia extends SaveVO<PreventivoOcorrencia> {

    public SavePreventivoOcorrencia(PreventivoOcorrencia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getDataOcorrencia() == null) {
            this.vo.setDataOcorrencia(DataUtil.getDataAtual());
        }
    }
}
