package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.unidadesaude.exames.AutorizacaoExameDTO;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.agendamento.exame.CotasExamesHelper;
import br.com.ksisolucoes.agendamento.exame.dto.QueryConsultaExamePrestadorCompetenciaDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.ValidarCotaUnidadeDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.exame.ExameUnidadeProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.hibernate.Criteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutorizarExame extends AbstractCommandTransaction {

    private AutorizacaoExameDTO autorizacaoExameDTO;
    private Double valorLimiteUltrapassarCota;

    public AutorizarExame(AutorizacaoExameDTO autorizacaoExameDTO) {
        this.autorizacaoExameDTO = autorizacaoExameDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        valorLimiteUltrapassarCota = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("valorMaximoUltrapassarCotaProfissionalCBO");
        ExamePrestadorCompetencia examePrestadorCompetencia = HibernateUtil.lockTable(ExamePrestadorCompetencia.class, autorizacaoExameDTO.getCodigoExamePrestadorCompetencia());

        Exame exame = HibernateUtil.lockTable(Exame.class, autorizacaoExameDTO.getCodigoExame());
        exame.setLocalExame(examePrestadorCompetencia.getEmpresa());
        exame.setEmpresaAutorizadora(SessaoAplicacaoImp.getInstance().getEmpresa());
        exame.setDataCompetencia(autorizacaoExameDTO.getCompetencia());

        Empresa empresaSolicitante = empresaSolicitante(exame);

        List<ExameRequisicao> requisicoes = getSession().createCriteria(ExameRequisicao.class)
                .add(Restrictions.eq(ExameRequisicao.PROP_EXAME, exame))
                .add(Restrictions.eq(ExameRequisicao.PROP_STATUS, ExameRequisicao.Status.ABERTO.value()))
                .list();

        if(br.com.celk.util.CollectionUtils.isEmpty(requisicoes)){
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_procedimentos_serem_autorizados_favor_verificar_detalhes_requisicao_consulta_requisição"));
        }

        if (!validarPrestador(examePrestadorCompetencia, requisicoes)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_prestador_nao_possui_cota_suficiente"));
        }

        validarCotaUnidade(requisicoes, exame, examePrestadorCompetencia, empresaSolicitante);

        for (ExameRequisicao exameRequisicao : requisicoes) {
            BOFactory.getBO(ExameFacade.class).gerarExameCotaRealizado(examePrestadorCompetencia.getCodigo(), exameRequisicao.getCodigo(), autorizacaoExameDTO.getTabelaCbo(), autorizacaoExameDTO.isConsomeCotaOutraUnidadeAutorizadora());
        }

        BOFactory.getBO(ExameFacade.class).gerarProtocoloAutorizacaoExame(exame.getCodigo());
    }

    private Empresa empresaSolicitante(Exame exame) throws DAOException, ValidacaoException {
        String consomeCotaOutraUnidadeAutorizadora = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("consomeCotaOutraUnidadeAutorizadora");
        Empresa consomeCotaDaUnidade = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("consomeCotaDaUnidade");
        Empresa.validaConsomeCotaDaUnidade();
        if (RepositoryComponentDefault.SIM.equals(consomeCotaOutraUnidadeAutorizadora) && consomeCotaDaUnidade != null && autorizacaoExameDTO.isConsomeCotaOutraUnidadeAutorizadora()) {
            return (Empresa) this.getSession().get(Empresa.class, consomeCotaDaUnidade.getCodigo());
        }
        return exame.getEmpresaSolicitante();
    }

    private boolean validarPrestador(ExamePrestadorCompetencia epc, List<ExameRequisicao> requisicoes) throws ValidacaoException {
        if (epc != null) {
            Double cotaUtilizadaExames = getCotaUtilizadaExames(requisicoes, requisicoes.get(0).getExameProcedimento().getTipoExame());
            Double cotaRecursoProprioExames = getCotaRecursoProprioUtilizadaExames(requisicoes, epc.getEmpresa());
            if (epc.getTetoRecursoProprio() == null || epc.getTetoRecursoProprio() == 0D) {
                double saldoFinanceiroPrestador = epc.getTetoFinanceiro() - epc.getTetoFinanceiroRealizado();
                if (saldoFinanceiroPrestador > 0) {
                    return cotaUtilizadaExames <= saldoFinanceiroPrestador;
                }
            } else {
                double saldoFinanceiroPrestador = epc.getTetoFinanceiro() - epc.getTetoFinanceiroRealizado();
                double saldoRecursoProprioPrestador = epc.getTetoRecursoProprio() - epc.getTetoRecursoProprioRealizado();
                if (saldoFinanceiroPrestador > 0 || saldoRecursoProprioPrestador > 0) {
                    return cotaUtilizadaExames <= saldoFinanceiroPrestador && cotaRecursoProprioExames <= saldoRecursoProprioPrestador;
                }
            }
        }
        return false;
    }

    private Double getCotaUtilizadaExames(List<ExameRequisicao> list, TipoExame tipoExame) throws ValidacaoException {
        Double totalCotaUtilizada = null;
        // Se o tipo do teto estiver configurado como físico, atualizar e validar as cotas pela quantidade (#8276)
        if (CotasExamesHelper.isTipoTetoFisico(tipoExame)) {
            totalCotaUtilizada = getQuantidadeTotalExames(list);
        } else {
            totalCotaUtilizada = getValorServicoAmbulatorial(list);
        }

        return totalCotaUtilizada;
    }

    private Double getCotaRecursoProprioUtilizadaExames(List<ExameRequisicao> list, Empresa exameUnidadeCompetencia) throws ValidacaoException {
        Double totalCotaRecursoProprioUtilizado = 0D;

        for (ExameRequisicao exameRequisicao : list) {
            Dinheiro valorTotalRecursoProprioUtilizado = new Dinheiro(0D);
            Double valorComplementar = ProcedimentoHelper.getRecursoProprioProcedimento(exameRequisicao.getExameProcedimento(), exameUnidadeCompetencia);
            valorTotalRecursoProprioUtilizado = valorTotalRecursoProprioUtilizado.somar(new Dinheiro(Coalesce.asDouble(valorComplementar)).multiplicar(exameRequisicao.getQuantidade()));

            totalCotaRecursoProprioUtilizado += valorTotalRecursoProprioUtilizado.doubleValue();
        }

        return totalCotaRecursoProprioUtilizado;
    }

    private Double getValorServicoAmbulatorial(List<ExameRequisicao> list) {
        Double valorServicoAmbulatorial = 0D;

        for (ExameRequisicao exameRequisicao : list) {
            valorServicoAmbulatorial += exameRequisicao.getValorProcedimentoTotal();
        }

        return valorServicoAmbulatorial;
    }

    private Double getQuantidadeTotalExames(List<ExameRequisicao> list) {
        Double quantidade = 0D;

        for (ExameRequisicao exameRequisicao : list) {
            quantidade += exameRequisicao.getQuantidade();
        }

        return quantidade;
    }

    private void validarCotaUnidade(List<ExameRequisicao> list, Exame exame, ExamePrestadorCompetencia epc, Empresa empresaSolicitante) throws ValidacaoException, DAOException {
        Date competenciaExame = exame.getDataCompetencia();
        if (competenciaExame == null) {
            Long diaInicioCompetencia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia();
            if (diaInicioCompetencia != null) {
                competenciaExame = Data.competenciaData(diaInicioCompetencia.intValue(), Data.getDataAtual());
            }
        }

        Long codigoProfissional = null;
        if (exame.getProfissional() != null) {
            codigoProfissional = exame.getProfissional().getCodigo();
        }
        String codigoCbo = null;
        if(autorizacaoExameDTO.getTabelaCbo() != null){
            codigoCbo = autorizacaoExameDTO.getTabelaCbo().getCbo();
        } else if (exame.getTabelaCbo() != null) {
            codigoCbo = exame.getTabelaCbo().getCbo();
        }

        Long codigoPrestador = null;
        if (epc != null && epc.getEmpresa() != null) {
            codigoPrestador = epc.getEmpresa().getCodigo();
        }

        ValidarCotaUnidadeDTO validarCotaUnidadeDTO = BOFactory.getBO(ExameFacade.class).validarCotaUnidade(competenciaExame, empresaSolicitante.getCodigo(), exame.getTipoExame().getCodigo(), codigoProfissional, codigoCbo, codigoPrestador);
        Double cotaExame = 0D;
        for (ExameRequisicao exameRequisicao : list) {
            cotaExame = exameRequisicao.getValorProcedimentoTotal();
            // Se o tipo do teto estiver configurado como físico, atualizar e validar as cotas pela quantidade (#8276)
            if (CotasExamesHelper.isTipoTetoFisico(exameRequisicao.getExameProcedimento().getTipoExame())) {
                cotaExame = Double.valueOf(exameRequisicao.getQuantidade());
            } else if (epc.getEmpresa() != null && epc.getEmpresa().getCodigo() != null) {
                Double valorExaProc = ProcedimentoHelper.getValorExameProcedimento(exameRequisicao.getExameProcedimento(), epc.getEmpresa());
                cotaExame = new Dinheiro(valorExaProc).multiplicar(Coalesce.asLong(exameRequisicao.getQuantidade(), 1L)).doubleValue();
            }
            if (validarCotaUnidadeDTO.getCotaUtilizada() != null) {
                validarCotaUnidadeDTO.setCotaUtilizada(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaUtilizada())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }

            if (validarCotaUnidadeDTO.getCotaCboUtilizada() != null) {
                validarCotaUnidadeDTO.setCotaCboUtilizada(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaCboUtilizada())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }

            if (validarCotaUnidadeDTO.getCotaProfissionalUtilizada() != null) {
                validarCotaUnidadeDTO.setCotaProfissionalUtilizada(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizada())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }

            if (validarCotaUnidadeDTO.getCotaProfissionalUtilizadaPercentual() != null) {
                validarCotaUnidadeDTO.setCotaProfissionalUtilizadaPercentual(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizadaPercentual())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }

            if (validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizada() != null) {
                validarCotaUnidadeDTO.setCotaProfissionalSemanaUtilizada(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizada())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }

            if (validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizadaPercentual() != null) {
                validarCotaUnidadeDTO.setCotaProfissionalSemanaUtilizadaPercentual(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizadaPercentual())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
            }
        }

        if (validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizadaPercentual() != null) {
            validarCotaUnidadeDTO.setCotaUnidade(new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaUnidade())).somar(Coalesce.asDouble(cotaExame)).doubleValue());
        }

        Dinheiro cotaUnidade = new Dinheiro(validarCotaUnidadeDTO.getCotaUnidade());
        Dinheiro cotaUtilizada = new Dinheiro(validarCotaUnidadeDTO.getCotaUtilizada());

        // Avalia se o tipo do exame é controle fisico, não deve utilizar o limite
        if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
            ExameCotaPpi exameCotaPpi = (ExameCotaPpi) getSession().createCriteria(ExameCotaPpi.class)
                    .add(Restrictions.eq(ExameCotaPpi.PROP_TIPO_EXAME, exame.getTipoExame()))
                    .uniqueResult();

            if (exameCotaPpi != null && ExameCotaPpi.TipoTeto.FISICO.value().equals(exameCotaPpi.getTipoTeto())) {
                valorLimiteUltrapassarCota = 0D;
            }
        }

        if (validarCotaUnidadeDTO.isValidarCota()) {
            if (cotaUnidade.doubleValue() == 0D) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_cota_definida_tipo_exame", exame.getTipoExame().getDescricao()));
            }

            if (cotaUnidade.subtrair(cotaUtilizada).doubleValue() < 0D) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_unidade_atingido_apenas_exames_urgentes_podem_ser_adicionados"));
            }

            if (validarCotaUnidadeDTO.getCotaProfissionalSemanaPercentual() != null) {
                Dinheiro cotaProfissionalSemanaPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemanaPercentual()));
                Dinheiro cotaProfissionalSemanaUtilizadaPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizadaPercentual()));
                if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                    cotaProfissionalSemanaPercentual = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissionalSemanaPercentual);
                }
                if (cotaProfissionalSemanaPercentual.subtrair(cotaProfissionalSemanaUtilizadaPercentual).doubleValue() < 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_limite_semanal_profissional_percentual_atingido_prestador_x", epc.getEmpresa().getDescricao()));
                } else if (validarCotaUnidadeDTO.getCotaProfissional() != null) {
                    Dinheiro cotaProfissionalPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalPercentual()));
                    Dinheiro cotaProfissionalUtilizadaPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizadaPercentual()));
                    if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                        cotaProfissionalPercentual = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissionalPercentual);
                    }
                    if (cotaProfissionalPercentual.subtrair(cotaProfissionalUtilizadaPercentual).doubleValue() < 0D) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_percentual_profissional_atingido_prestador_x", epc.getEmpresa().getDescricao()));
                    }
                }
            } else if (validarCotaUnidadeDTO.getCotaProfissionalPercentual() != null) {
                Dinheiro cotaProfissionalPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalPercentual()));
                Dinheiro cotaProfissionalUtilizadaPercentual = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizadaPercentual()));
                if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                    cotaProfissionalPercentual = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissionalPercentual);
                }
                if (cotaProfissionalPercentual.subtrair(cotaProfissionalUtilizadaPercentual).doubleValue() < 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_percentual_profissional_atingido_prestador_x", epc.getEmpresa().getDescricao()));
                }
            } else  if (validarCotaUnidadeDTO.getCotaProfissionalSemana() != null) {
                Dinheiro cotaProfissionalSemana = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemana()));
                Dinheiro cotaProfissionalSemanaUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalSemanaUtilizada()));
                if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                    cotaProfissionalSemana = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissionalSemana);
                }
                if (cotaProfissionalSemana.subtrair(cotaProfissionalSemanaUtilizada).doubleValue() < 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_limite_semanal_profissional_atingido"));
                } else if (validarCotaUnidadeDTO.getCotaProfissional() != null) {
                    Dinheiro cotaProfissional = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissional()));
                    Dinheiro cotaProfissionalUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizada()));
                    if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                        cotaProfissional = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissional);
                    }
                    if (cotaProfissional.subtrair(cotaProfissionalUtilizada).doubleValue() < 0D) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_profissional_atingido_apenas_exames_urgentes_podem_ser_adicionados"));
                    }
                }
            } else if (validarCotaUnidadeDTO.getCotaProfissional() != null) {
                Dinheiro cotaProfissional = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissional()));
                Dinheiro cotaProfissionalUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaProfissionalUtilizada()));
                if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                    cotaProfissional = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaProfissional);
                }
                if (cotaProfissional.subtrair(cotaProfissionalUtilizada).doubleValue() < 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_profissional_atingido_apenas_exames_urgentes_podem_ser_adicionados"));
                }
            } else if (validarCotaUnidadeDTO.getCotaCbo() != null) {
                Dinheiro cotaCbo = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaCbo()));
                Dinheiro cotaCboUtilizada = new Dinheiro(Coalesce.asDouble(validarCotaUnidadeDTO.getCotaCboUtilizada()));
                if (Coalesce.asDouble(valorLimiteUltrapassarCota) > 0D) {
                    cotaCbo = new Dinheiro(Coalesce.asDouble(valorLimiteUltrapassarCota)).somar(cotaCbo);
                }
                if (cotaCbo.subtrair(cotaCboUtilizada).doubleValue() < 0D) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_cbo_atingido_apenas_exames_urgentes_podem_ser_adicionados"));
                }
            }
            // Avalia as cotas pela quantidade de exames (#7444)
            ExameUnidadeProcedimentoCompetencia exameUnidadeProcedimentoCompetencia;
            for (ExameRequisicao exameRequisicao : list) {
                exameUnidadeProcedimentoCompetencia = (ExameUnidadeProcedimentoCompetencia) getSession().createCriteria(ExameUnidadeProcedimentoCompetencia.class)
                        .add(Restrictions.eq(ExameUnidadeProcedimentoCompetencia.PROP_EXAME_PROCEDIMENTO, exameRequisicao.getExameProcedimento()))
                        .createCriteria(ExameUnidadeProcedimentoCompetencia.PROP_EXAME_UNIDADE_COMPETENCIA)
                        .add(Restrictions.eq(ExameUnidadeCompetencia.PROP_CODIGO, validarCotaUnidadeDTO.getCodigoExameUnidadeCompetencia()))
                        .uniqueResult();

                if (exameUnidadeProcedimentoCompetencia != null) {
                    Dinheiro cotaQuantidade = new Dinheiro(Coalesce.asLong(exameUnidadeProcedimentoCompetencia.getTetoFisico()))
                            .subtrair(new Dinheiro(Coalesce.asLong(exameUnidadeProcedimentoCompetencia.getTetoFisicoRealizado())))
                            .subtrair(new Dinheiro(Coalesce.asLong(exameRequisicao.getQuantidade())));

                    if (cotaQuantidade.compareTo(Dinheiro.ZERO) < 0) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_exame_X_ultrapassou_cota_fisica_Y_definida_estabelecimento_saldo_disponivel_Z", exameUnidadeProcedimentoCompetencia.getExameProcedimento().getDescricaoProcedimento(), exameUnidadeProcedimentoCompetencia.getTetoFisico(), exameUnidadeProcedimentoCompetencia.getTetoFisico() - Coalesce.asLong(exameUnidadeProcedimentoCompetencia.getTetoFisicoRealizado())));
                    }
                }
            }
        }

        QueryConsultaExamePrestadorCompetenciaDTOParam queryParam = new QueryConsultaExamePrestadorCompetenciaDTOParam();
        List<Long> codigoExameProcedimentoList = new ArrayList<Long>();
        for (ExameRequisicao exameRequisicao : list) {
            codigoExameProcedimentoList.add(exameRequisicao.getExameProcedimento().getCodigo());
        }
        queryParam.setCodigoExameProcedimentoList(codigoExameProcedimentoList);
        queryParam.setDataCompetencia(competenciaExame);
        queryParam.setCodigoExamePrestadorCompetencia(autorizacaoExameDTO.getCodigoExamePrestadorCompetencia());
        QueryConsultaExamePrestadorCompetencia query = new QueryConsultaExamePrestadorCompetencia(queryParam);
        query.start();
        List<ExamePrestadorCompetencia> prestadores = new ArrayList<ExamePrestadorCompetencia>(query.getResult());
        if (CollectionUtils.isEmpty(prestadores)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_prestador_invalido"));
        }
        ExamePrestadorCompetencia examePrestadorCompetencia = prestadores.get(0);
        TipoExame tipoExame = examePrestadorCompetencia.getTipoExame();

        Double cotaUtilizadaExames = getCotaUtilizadaExames(list, tipoExame);
        if (examePrestadorCompetencia.getTetoRecursoProprio() != null && examePrestadorCompetencia.getTetoRecursoProprio() > 0) {
            if (examePrestadorCompetencia.getSaldoFinanceiro() < cotaUtilizadaExames || examePrestadorCompetencia.getSaldoRecursoProprio() < cotaUtilizadaExames) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_prestador_selecionado_nao_possui_saldo"));
            }
        } else {
            if (examePrestadorCompetencia.getSaldoFinanceiro() < cotaUtilizadaExames) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_prestador_selecionado_nao_possui_saldo"));
            }
        }

        Criteria criteriaExamePrestador = getSession().createCriteria(ExamePrestador.class)
                .add(org.hibernate.criterion.Restrictions.eq(ExamePrestador.PROP_PRESTADOR, examePrestadorCompetencia.getEmpresa()));
        if (examePrestadorCompetencia.getTipoExame() != null && examePrestadorCompetencia.getTipoExame().getCodigo() != null) {
            criteriaExamePrestador.add(org.hibernate.criterion.Restrictions.or(
                    org.hibernate.criterion.Restrictions.eq(ExamePrestador.PROP_TIPO_EXAME, examePrestadorCompetencia.getTipoExame()),
                    org.hibernate.criterion.Restrictions.eq(ExamePrestador.PROP_TIPO_EXAME_SECUNDARIO, examePrestadorCompetencia.getTipoExame())));
        } else {
            criteriaExamePrestador.add(org.hibernate.criterion.Restrictions.isNull(ExamePrestador.PROP_TIPO_EXAME));
        }

        ExamePrestador examePrestador = (ExamePrestador) criteriaExamePrestador.uniqueResult();
        if (examePrestador != null) {
            Criteria criteria = getSession().createCriteria(ExameUnidadePrestadorCompetencia.class)
                    .add(Restrictions.eq(ExameUnidadePrestadorCompetencia.PROP_EMPRESA, empresaSolicitante))
                    .add(Restrictions.eq(ExameUnidadePrestadorCompetencia.PROP_PRESTADOR, examePrestadorCompetencia.getEmpresa()));
            if (ExamePrestador.TipoCota.MENSAL.value().equals(examePrestador.getTipoCota())) {
                criteria.add(Restrictions.eq(ExameUnidadePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaExame));
            } else if (ExamePrestador.TipoCota.ANUAL.value().equals(examePrestador.getTipoCota())) {
                criteria.add(Restrictions.ge(ExameUnidadePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaExame));
            }

            if (examePrestadorCompetencia.getTipoExame() != null && examePrestadorCompetencia.getTipoExame().getCodigo() != null) {
                criteria.add(Restrictions.eq(ExameUnidadePrestadorCompetencia.PROP_TIPO_EXAME, examePrestadorCompetencia.getTipoExame()));
            }

            ExameUnidadePrestadorCompetencia exameUnidadePrestadorCompetencia = (ExameUnidadePrestadorCompetencia) criteria.uniqueResult();
            if (exameUnidadePrestadorCompetencia != null) {
                if (exameUnidadePrestadorCompetencia.getSaldoFinanceiro() < getCotaUtilizadaExames(list, tipoExame)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_prestador_selecionado_nao_possui_saldo_unidade_valor_cota_X_saldo_Y",
                            exameUnidadePrestadorCompetencia.getTetoFinanceiro(), exameUnidadePrestadorCompetencia.getSaldoFinanceiro()));
                }
            }
        }
    }
}
