package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoprorrogacaoprazo;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProrrogacaoPrazo;

/**
 *
 * <AUTHOR>
 */
public class SaveRequerimentoProrrogacaoPrazo extends SaveVO<RequerimentoProrrogacaoPrazo> {

    public SaveRequerimentoProrrogacaoPrazo(RequerimentoProrrogacaoPrazo vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if(this.vo.getCnpjCpf() != null){
            if (!CpfCnpJValidator.CPFIsValid(StringUtil.getDigits(vo.getCnpjCpf())) && !CpfCnpJValidator.CNPJIsValid(StringUtil.getDigits(vo.getCnpjCpf()))) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_cnpj_cpf_invalido"));
            }
            
            this.vo.setCnpjCpf(StringUtil.getDigits(this.vo.getCnpjCpf()));
        }

        if(this.vo.getAutoIntimacao() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido", this.vo.getAutoIntimacao().getNumero()));
        } else {
            this.vo.setNumeroAutoIntimacao(this.vo.getAutoIntimacao().getNumero());
        }

        if(this.vo.getEstabelecimento() != null) {
            this.vo.setNome(this.vo.getEstabelecimento().getRazaoSocial());
        } else if(this.vo.getVigilanciaPessoa() != null) {
            this.vo.setNome(this.vo.getVigilanciaPessoa().getNome());
        }
        if (this.vo.getRequerimentoVigilancia() != null && this.vo.getRequerimentoVigilancia().getCodigo() != null) {
            RequerimentoVigilancia rv = (RequerimentoVigilancia) getSession().get(RequerimentoVigilancia.class, this.vo.getRequerimentoVigilancia().getCodigo());
            rv.setNome(this.vo.getNome());
            rv.setVigilanciaEndereco(this.vo.getVigilanciaEndereco());
            rv.setTelefone(this.vo.getTelefone());
            rv.setCnpjCpf(StringUtil.getDigits(this.vo.getCnpjCpf()));
            BOFactory.save(rv);
        }
    }
}