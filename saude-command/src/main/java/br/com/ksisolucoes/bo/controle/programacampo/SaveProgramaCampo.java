package br.com.ksisolucoes.bo.controle.programacampo;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Programa;
import br.com.ksisolucoes.vo.controle.ProgramaCampo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.hibernate.Query;

/**
 * <AUTHOR>
 *
 */
public class SaveProgramaCampo extends AbstractCommandTransaction {

    private List< ProgramaCampo > programasCampo;
    private Programa programa;

    public SaveProgramaCampo( Object vo ) {
        if ( vo instanceof ProgramaCampo ){
            this.programasCampo = Arrays.asList( (ProgramaCampo) vo );
        } else if ( vo instanceof Collection ){
            this.programasCampo = new ArrayList< ProgramaCampo >( (Collection) vo );
        }
    }

    public void execute() throws DAOException, ValidacaoException {
        if ( this.programasCampo != null ){
            for ( ProgramaCampo programaCampo : this.programasCampo ){
                if ( this.programa == null ){
                    this.programa = programaCampo.getId().getPrograma();
                }
                new SaveProgramaCampoUnit(programaCampo).start();
            }
        }

        this.excluirProgramaCampoSet();
    }

    private void excluirProgramaCampoSet() throws DAOException, ValidacaoException {
//        Filtro< ProgramaCampo > filtro = new Filtro< ProgramaCampo >( ProgramaCampo.class );
//        filtro.addIgual( ProgramaCampo.PROP_RO_PROGRAMA, this.programa );

        Set setBD = new HashSet<ProgramaCampo>( LoadManager.getInstance(ProgramaCampo.class)
                .setLazyMode(true)
                .addParameter(new QueryCustomParameter(ProgramaCampo.PROP_RO_PROGRAMA, this.programa)).start().<ProgramaCampo>getList() );

        lacoDB:
            for (Iterator iDB = setBD.iterator(); iDB.hasNext();) {
                Object voDB = iDB.next();

                for (Iterator i = this.programasCampo.iterator(); i.hasNext();) {
                    Object vo = i.next();

                    if(voDB.equals(vo)) {
                        continue lacoDB;
                    }
                }
                new DeleteProgramaCampo(voDB).start();
            }
    }
}

class SaveProgramaCampoUnit extends SaveVO {

	private static final long serialVersionUID = 1L;
    private ProgramaCampo programaCampo;

    public SaveProgramaCampoUnit(Object vo) {
        super( vo );
        this.programaCampo = (ProgramaCampo) vo;
    }

	/**
	 * {@inheritDoc}
	 *
	 * @return {@inheritDoc}
	 */
	

    protected void antesSave() throws ValidacaoException, DAOException {

        if ( this.programaCampo.getId().getCodigo() == null ){
            this.programaCampo.getId().setCodigo(getIdToItemNotaFiscal( this.programaCampo ) );
        }
    }

	public Long getIdToItemNotaFiscal(ProgramaCampo programaCampo) throws br.com.ksisolucoes.dao.exception.DAOException {
        Long maxId = this.getMaxId(programaCampo);

        if (maxId != null) {
            long id = maxId.longValue();
            id++;
            return new Long(id);
        } else {
            return new Long(1);
        }
    }

    public Long getMaxId(ProgramaCampo programaCampo) throws br.com.ksisolucoes.dao.exception.DAOException {
        HQLHelper hql = new HQLHelper();
        hql.addToSelect("max(pc.id.codigo)");
        hql.addToFrom(ProgramaCampo.class.getName() + " pc");
        hql.addToWhereWhithAnd(" pc.id.programa.codigo = :codigoPrograma");

        Query query = getSession().createQuery(hql.getQuery());

        hql.setParameterValue(query, "codigoPrograma", programaCampo.getId().getPrograma().getCodigo());

        Object result = query.uniqueResult();

        if (result != null) {
            return (Long) result;
        } else {
            return null;
        }
    }

}