/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.celk.esus.domicilio.MotivoExclusaoDomicilioDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDomicilio;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.ConvenioPaciente;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarUsuarioCadsusInterno extends AbstractCommandTransaction {

    private UsuarioCadsusEndereco usuarioCadsusEndereco;
    private UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO;
    private UsuarioCadsus usuarioCadsus;
    private UsuarioCadsusEsus usuarioCadsusEsus;
    private List<ConvenioPaciente> lstConvenioPaciente;

    public CadastrarUsuarioCadsusInterno(UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO) {
        this.usuarioCadsusEnderecoDTO = usuarioCadsusEnderecoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.usuarioCadsusEndereco = this.usuarioCadsusEnderecoDTO.getUsuarioCadsusEndereco();
        this.lstConvenioPaciente = this.usuarioCadsusEnderecoDTO.getLstConvenioPaciente();
        usuarioCadsusEsus = usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus();

        if (usuarioCadsusEsus != null) {
            usuarioCadsus = usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus().getUsuarioCadsus();

            if (usuarioCadsusEsus.getDataNaturalizado() != null && usuarioCadsusEsus.getDataNaturalizado().after(DataUtil.getDataAtual())) {
                throw new ValidacaoException("A data de naturalização não pode ser maior que a data atual");
            }

            if (usuarioCadsusEsus.getDtEntradaBrasil() != null && usuarioCadsusEsus.getDtEntradaBrasil().after(DataUtil.getDataAtual())) {
                throw new ValidacaoException("A data de entrada no Brasil não pode ser maior que a data atual");
            }

            if (usuarioCadsusEsus.getDataObito() != null && usuarioCadsusEsus.getDataObito().after(DataUtil.getDataAtual())) {
                throw new ValidacaoException("A data de óbito não pode ser maior que a data atual");
            }

        } else {
            usuarioCadsus = usuarioCadsusEnderecoDTO.getUsuarioCadsus();
        }

        if (this.usuarioCadsusEndereco.getId().getEndereco() == null
                && !UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_um_endereco"));
        }

        if (usuarioCadsus.getFlagDocumento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_paciente_apresentou_documentos"));
        }

        EnderecoDomicilio novoDomicilio = null;
        if (this.usuarioCadsusEndereco.getId().getEndereco() != null) {
            novoDomicilio = (EnderecoDomicilio) getSession().createCriteria(EnderecoDomicilio.class)
                    .add(Restrictions.eq(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, this.usuarioCadsusEndereco.getId().getEndereco()))
                    .uniqueResult();
        }

        if (novoDomicilio != null) {
            UsuarioCadsusDomicilio ucd = LoadManager.getInstance(UsuarioCadsusDomicilio.class)
                    .addProperty(VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_ENDERECO_DOMICILIO, EnderecoDomicilio.PROP_CODIGO), novoDomicilio.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_FLAG_RESPONSAVEL_FAMILIAR), RepositoryComponentDefault.SIM_LONG))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDomicilio.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                    .setMaxResults(1).start().getVO();

            if (ucd != null && ucd.getUsuarioCadsus() != null && !ucd.getUsuarioCadsus().getCodigo().equals(usuarioCadsus.getCodigo())) {
                usuarioCadsus.setFlagResponsavelFamiliar(RepositoryComponentDefault.NAO_LONG);
                usuarioCadsus.setResponsavelFamiliar(ucd.getUsuarioCadsus());
            }
        }
        usuarioCadsus.setEnderecoDomicilio(novoDomicilio);

        if (this.usuarioCadsusEndereco.getId().getEndereco() != null) {
            usuarioCadsus.setMunicipioResidencia(this.usuarioCadsusEndereco.getId().getEndereco().getCidade());
        }

        if (UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {
            usuarioCadsus.setExcluido(RepositoryComponentDefault.EXCLUIDO);
        } else if (UsuarioCadsus.SITUACAO_INATIVO.equals(usuarioCadsus.getSituacao())) {
            usuarioCadsus.setDataInativacao(Data.getDataAtual());
        }

        String validarDocumentoCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarDocumentoCadastroNovo");
        String validarCnsCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarCnsCadastroNovo");
        String validarTelefoneCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarTelefoneCadastroNovo");
        if (!UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {

            if (usuarioCadsus.getDataCadastro() == null || Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().compareTo(Data.adjustRangeHour(usuarioCadsus.getDataCadastro()).getDataInicial()) == 0) {
                if (RepositoryComponentDefault.SIM.equals(validarDocumentoCadastroNovo)) {
                    // Valida obrigatoriedade dos documentos e se os documentos estão corretos
                    UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), true, false, true);
                } else {
                    // Não valida obrigatoriedade dos documentos, apenas se os documentos estão corretos
                    UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), false, false, true);
                }
                Long nacionalidade = null;
                if (usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus() != null
                        && usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus().getUsuarioCadsus() != null) {
                    nacionalidade = usuarioCadsusEnderecoDTO.getUsuarioCadsusEsus().getUsuarioCadsus().getNacionalidade();
                }
                if (RepositoryComponentDefault.SIM.equals(validarCnsCadastroNovo) && !UsuarioCadsus.Nacionalidade.ESTRANGEIRO.value().equals(nacionalidade)) {
                    // Valida se foi informado um cns e se o mesmo é ativo
                    UsuarioCadsusHelper.validarCns(true, usuarioCadsusEnderecoDTO.getCartoes());
                }
                if (RepositoryComponentDefault.SIM.equals(validarTelefoneCadastroNovo)) {
                    // Valida se foi informado pelo menos um telefone
                    UsuarioCadsusHelper.validarTelefoneUsuarioCadsus(usuarioCadsus);
                }
            } else {
                // Valida se foi informado pelo um documento, incluíndo cns como documento
                UsuarioCadsusHelper.validarIdentidadeCertidao(usuarioCadsusEnderecoDTO.getDocumentos(), true, true, false, usuarioCadsusEnderecoDTO.getCartoes());
            }
        }

        //gravacao da foto
        if (usuarioCadsusEnderecoDTO.getAnexoDTO() != null) {
            if (usuarioCadsusEnderecoDTO.getAnexoDTO().isPossuiAnexo()) {
                GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(usuarioCadsusEnderecoDTO.getAnexoDTO());
                usuarioCadsus.setFoto(gerenciadorArquivo);
            }
        }


        usuarioCadsus = BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        if (usuarioCadsus.getReferencia() == null){
            usuarioCadsus.setReferencia(usuarioCadsus.getCodigo().toString());
            usuarioCadsus = BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        }

        if (usuarioCadsusEsus != null) {
            BOFactory.getBO(UsuarioCadsusFacade.class).salvarUsuarioCadsusEsus(usuarioCadsusEsus);
        }

        if (CollectionUtils.isNotNullEmpty(lstConvenioPaciente)) {
            salvarConvenioPaciente(lstConvenioPaciente);
        }

        if (UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {
            boolean exists = ((Number) this.getSession().createCriteria(UsuarioCadsusDomicilio.class)
                    .setProjection(Projections.count(UsuarioCadsusDomicilio.PROP_STATUS))
                    .add(Restrictions.ne(UsuarioCadsusDomicilio.PROP_STATUS, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                    .add(Restrictions.eq("usuarioCadsus", usuarioCadsus))
                    .uniqueResult()).longValue() > 0;
            if (exists) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_paciente_relacionado_familia_processir_remover_familia"));
            }
        }

        if (usuarioCadsusEsus != null && RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusEsus.getVisitaFamiliarFrequentemente())
                && usuarioCadsusEsus.getGrauParentesco() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_informe_qual_familiar_visita_com_frequencia"));
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).saveUsuarioCadsusDocumentos(this.usuarioCadsusEnderecoDTO.getDocumentos(), usuarioCadsus, this.usuarioCadsusEnderecoDTO.getTipoDocumentos());
        
        if (UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao()) || UsuarioCadsus.SITUACAO_INATIVO.equals(usuarioCadsus.getSituacao())) {
            BOFactory.getBO(UsuarioCadsusFacade.class).salvarUsuarioCadsusExclusao(usuarioCadsus);
        } else if (UsuarioCadsus.SITUACAO_ATIVO.equals(usuarioCadsus.getSituacao())) {
            BOFactory.getBO(UsuarioCadsusFacade.class).cancelarUsuarioCadsusExclusao(usuarioCadsus);
        }

        new CadastrarUsuarioCadsusCns(this.usuarioCadsusEnderecoDTO.getCartoes(), usuarioCadsus, false).start();

        new GerarNumeroProntuarioUsuarioCadsus(usuarioCadsus, usuarioCadsusEnderecoDTO.getNumeroProntuario()).start();

        if(this.usuarioCadsusEnderecoDTO.getMotivoNovoEndereco() != null){
            MotivoExclusaoDomicilioDTO motivoExclusaoDomicilioDTO = new MotivoExclusaoDomicilioDTO();
            motivoExclusaoDomicilioDTO.setUsuarioCadsus(usuarioCadsus);
            motivoExclusaoDomicilioDTO.setEnderecoDomicilio(novoDomicilio);
            motivoExclusaoDomicilioDTO.setEnderecoUsuarioCadsus(this.usuarioCadsusEndereco.getId().getEndereco());
            motivoExclusaoDomicilioDTO.setMotivoExlusao((Long) UsuarioCadsusDomicilio.Motivo.OUTROS.value());
            motivoExclusaoDomicilioDTO.setLocalizaDomicilio(false);
            motivoExclusaoDomicilioDTO.setEnviarMSG(true);
            motivoExclusaoDomicilioDTO.setMotivo(this.usuarioCadsusEnderecoDTO.getMotivoNovoEndereco());

            new ResolverUsuarioCadsusDomicilio(motivoExclusaoDomicilioDTO).start();
        } else {
            new ResolverUsuarioCadsusDomicilio(usuarioCadsus, novoDomicilio, this.usuarioCadsusEndereco.getId().getEndereco(), false).start();
        }
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    private void salvarConvenioPaciente(List<ConvenioPaciente> lstConvenioPaciente) throws DAOException, ValidacaoException {
        List<ConvenioPaciente> aux = LoadManager.getInstance(ConvenioPaciente.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConvenioPaciente.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(aux)) {
            aux.removeAll(lstConvenioPaciente);
            for (ConvenioPaciente convenioPaciente : aux) {
                BOFactory.delete(convenioPaciente);
            }
        }
        for (ConvenioPaciente convenioPaciente : lstConvenioPaciente) {
            convenioPaciente.setUsuarioCadsus(usuarioCadsus);
            BOFactory.save(convenioPaciente);
        }
    }
}
