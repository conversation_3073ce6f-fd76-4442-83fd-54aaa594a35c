package br.com.ksisolucoes.bo.prontuario.basico.atendimento.programassaude;

import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.AuxilioBrasilDTO;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.BolsaFamiliaDTO;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.SisvanDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

/**
 *
 * <AUTHOR>
 */
public class RegistrarProgramasSaude extends AbstractCommandTransaction<RegistrarProgramasSaude> {

    private final Atendimento atendimento;
    private final AtendimentoPrimario atendimentoPrimario;
    private Boolean isPuerpera;
    private final boolean salvarSisvan;

    public RegistrarProgramasSaude(Atendimento atendimento, AtendimentoPrimario atendimentoPrimario, boolean salvarSisvan) {
        this.atendimento = atendimento;
        this.atendimentoPrimario = atendimentoPrimario;
        this.salvarSisvan = salvarSisvan;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        UsuarioCadsusDado usuarioCadsusDado = (UsuarioCadsusDado) getSession().get(UsuarioCadsusDado.class, atendimentoPrimario.getAtendimento().getUsuarioCadsus().getCodigo());

        if(salvarSisvan){
            salvarSisvan(usuarioCadsusDado);
        }

        TipoAtendimento tipoAtendimento = null;
        if(atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento() != null
            && atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento() != null
                && atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo() != null){
            tipoAtendimento = atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento();
        } else if (atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento() != null
                && atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento().getCodigo() != null){
            NaturezaProcuraTipoAtendimento npta = (NaturezaProcuraTipoAtendimento) getSession().get(NaturezaProcuraTipoAtendimento.class, atendimentoPrimario.getAtendimento().getNaturezaProcuraTipoAtendimento().getCodigo());

            if(npta != null){
                tipoAtendimento = npta.getTipoAtendimento();
            }
        }

        if (tipoAtendimento != null &&
                tipoAtendimento.getTipoAtendimentoEsus() != null &&
                TipoAtendimento.TipoClassificacao.ENFERMAGEM_MEDICA.value().equals(tipoAtendimento.getTipoClassificacao()) &&
                !RepositoryComponentDefault.NAO_LONG.equals(atendimentoPrimario.getAtendimento().getFlagConsulta()) &&
                this.isProfissionalNivelSuperior()) {

            UsuarioCadsus usuarioCadSus = (UsuarioCadsus) getSession()
                    .get(UsuarioCadsus.class, atendimentoPrimario.getAtendimento().getUsuarioCadsus().getCodigo());

            if (usuarioCadSus != null && this.isBeneficiarioBolsaFamilia(usuarioCadSus)) {
                salvarBolsaFamilia(usuarioCadsusDado);
                salvarAuxilioBrasil(usuarioCadsusDado);
            }
        }
    }

    private void salvarSisvan(UsuarioCadsusDado usuarioCadsusDado) throws DAOException, ValidacaoException {
        SisvanDTO dto = new SisvanDTO();

        dto.setAtendimentoPrimario(atendimentoPrimario);
        dto.setUsuarioCadsusDado(usuarioCadsusDado);

        BOFactory.getBO(AtendimentoFacade.class).salvarSisvan(dto);
    }

    private void salvarBolsaFamilia(UsuarioCadsusDado usuarioCadsusDado) throws DAOException, ValidacaoException {
        BolsaFamiliaDTO dto = new BolsaFamiliaDTO();

        dto.setAtendimentoPrimario(atendimentoPrimario);
        dto.setUsuarioCadsusDado(usuarioCadsusDado);

        BOFactory.getBO(AtendimentoFacade.class).salvarBolsaFamilia(atendimento, dto);
    }

    private void salvarAuxilioBrasil(UsuarioCadsusDado usuarioCadsusDado) throws DAOException, ValidacaoException {
        AuxilioBrasilDTO dto = new AuxilioBrasilDTO();

        dto.setAtendimentoPrimario(atendimentoPrimario);
        dto.setUsuarioCadsusDado(usuarioCadsusDado);
        dto.setAtendimento(atendimento);
        dto.setUsuarioCadsus(atendimento.getUsuarioCadsus());

        BOFactory.getBO(AtendimentoFacade.class).salvarAuxilioBrasil(dto);
    }

    private boolean isProfissionalNivelSuperior() {
        return TabelaCbo.NivelEnsino.SUPERIOR.value().equals(atendimento.getTabelaCbo().getNivelEnsino());

    }

    private boolean isBeneficiarioBolsaFamilia(UsuarioCadsus usuarioCadSus) {
        return RepositoryComponentDefault.SIM_LONG.equals(usuarioCadSus.getBeneficiarioBolsaFamilia());
    }

}