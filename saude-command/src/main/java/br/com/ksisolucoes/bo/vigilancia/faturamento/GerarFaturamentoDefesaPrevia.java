package br.com.ksisolucoes.bo.vigilancia.faturamento;

import br.com.celk.vigilancia.helper.FaturamentoVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
public class GerarFaturamentoDefesaPrevia extends AbstractCommandTransaction {

    private LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO;
    private RequerimentoVigilancia requerimentoVigilancia;
    private Date dataFaturamento;

    public GerarFaturamentoDefesaPrevia(RequerimentoVigilancia requerimentoVigilancia) {
        if(requerimentoVigilancia != null) {
            this.dataFaturamento = requerimentoVigilancia.getDataFinalizacao();
        }
        this.requerimentoVigilancia= requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        FaturamentoVigilanciaHelper.validarConfiguracaoVigilanciaAtividades(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DEFESA_PREVIA_DEFERIMENTO);

        lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();

        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = gerarLancamentoAtividadeVigilancia();

        List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = new ArrayList();

        gerarAtividadePadrao(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList);

        lancamentoAtividadesVigilanciaDTO.setFaturavel(false); //não gera item de conta (BPA)
        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilanciaItemList(lancamentoAtividadesVigilanciaItemList);
        BOFactory.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
    }

    private void gerarAtividadePadrao(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList) throws DAOException {

        LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();

        AtividadesVigilancia atividadesVigilanciaDefault;
        atividadesVigilanciaDefault = FaturamentoVigilanciaHelper.getAtividadeVigilanciaDefault(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DEFESA_PREVIA_DEFERIMENTO);

        lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia(atividadesVigilanciaDefault);
        lancamentoAtividadesVigilanciaItem.setPontuacao(atividadesVigilanciaDefault.getPontuacao());
        lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
        lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
    }

    private LancamentoAtividadesVigilancia gerarLancamentoAtividadeVigilancia() {
        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        lancamentoAtividadesVigilancia.setProfissional(SessaoAplicacaoImp.getInstance().getUsuario().getProfissional());
        lancamentoAtividadesVigilancia.setDataAtividade(dataFaturamento);
        lancamentoAtividadesVigilancia.setRequerimentoVigilancia(requerimentoVigilancia);
        lancamentoAtividadesVigilancia.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DEFESA_PREVIA_DEFERIMENTO.descricao());
        return lancamentoAtividadesVigilancia;
    }

}
