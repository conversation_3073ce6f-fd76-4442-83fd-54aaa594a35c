package br.com.ksisolucoes.bo.vigilancia.responsaveltecnico;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarEloRequerimentoVigilanciaResponsavelTecnico extends AbstractCommandTransaction {

    private final RequerimentoVigilancia requerimentoVigilancia;
    private List<EloRequerimentoVigilanciaResponsavelTecnico> eloList;
    private List<EloRequerimentoVigilanciaResponsavelTecnico> eloExcluirList;

    public SalvarEloRequerimentoVigilanciaResponsavelTecnico(RequerimentoVigilancia requerimentoVigilancia, List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoList, List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoExcluirList) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.eloList = eloRequerimentoVigilanciaResponsavelTecnicoList;
        this.eloExcluirList = eloRequerimentoVigilanciaResponsavelTecnicoExcluirList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        if(CollectionUtils.isNotNullEmpty(eloList)){
            Lambda.forEach(eloList).setRequerimentoVigilancia(requerimentoVigilancia);
            VOUtils.persistirListaVosModificados(EloRequerimentoVigilanciaResponsavelTecnico.class, eloList,
                    new QueryCustom.QueryCustomParameter(EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia));
        }

        if(CollectionUtils.isNotNullEmpty(eloExcluirList)){
            for(EloRequerimentoVigilanciaResponsavelTecnico elo : eloExcluirList){
                BOFactory.delete(elo);
            }
        }
    }

}
