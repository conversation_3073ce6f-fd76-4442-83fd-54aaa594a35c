package br.com.ksisolucoes.bo.basico.atividade;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaAtividadeDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Atividade;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAtividade extends CommandQueryPager<QueryConsultaAtividade> {

    private QueryConsultaAtividadeDTOParam param;

    public QueryConsultaAtividade(QueryConsultaAtividadeDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("at.codigo", true);
        hql.addToSelect("at.descricao", true);
        
        hql.setTypeSelect(Atividade.class.getName());
        hql.addToFrom("Atividade at");
        
        hql.addToWhereWhithAnd("at.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("at.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("at.codigo || ' ' || at.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("at."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("at.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
