package br.com.ksisolucoes.bo.vigilancia.parecer;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.EnviarEmailDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroOcorrenciaRequerimentoVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FaturamentoRequerimentoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.GerarParecerRequerimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class GerarParecerRequerimento extends AbstractCommandTransaction {

    private GerarParecerRequerimentoDTO dto;
    private RequerimentoVigilanciaParecer requerimentoVigilanciaParecer;

    public GerarParecerRequerimento(GerarParecerRequerimentoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoVigilancia requerimentoVigilancia = (RequerimentoVigilancia) getSession().createCriteria(RequerimentoVigilancia.class)
                .add(Restrictions.eq(RequerimentoVigilancia.PROP_CODIGO, this.dto.getRequerimentoVigilancia().getCodigo()))
                .setMaxResults(1)
                .setLockMode(LockMode.PESSIMISTIC_WRITE)
                .uniqueResult();
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();

        Long numInicial = configuracaoVigilancia.getNumeroParecerInicial();
        configuracaoVigilancia.setNumeroParecerInicial(configuracaoVigilancia.getNumeroParecerInicial()+1);
        BOFactory.save(configuracaoVigilancia);

        RequerimentoVigilanciaParecer requerimentoVigilanciaParecer = new RequerimentoVigilanciaParecer();
        requerimentoVigilanciaParecer.setDataCadastro(DataUtil.getDataAtual());
        requerimentoVigilanciaParecer.setDataParecer(this.dto.getData());
        requerimentoVigilanciaParecer.setDataSaida(DataUtil.getDataAtual());
        requerimentoVigilanciaParecer.setSituacao(this.dto.getSituacao());
        requerimentoVigilanciaParecer.setDescricao(this.dto.getDescricao());
        requerimentoVigilanciaParecer.setDescricaoParecer(this.dto.getParecer());
        requerimentoVigilanciaParecer.setRequerimentoVigilancia(requerimentoVigilancia);
        requerimentoVigilanciaParecer.setUsuario(getSessao().getUsuario());
        requerimentoVigilanciaParecer.setNumeroParecer(numInicial + "/" + configuracaoVigilancia.getAnoBaseGeral());

        this.requerimentoVigilanciaParecer = BOFactory.save(requerimentoVigilanciaParecer);

        for (AtividadesVigilancia atividadesVigilancia : this.dto.getAtividadesVigilanciaList()) {
            RequerimentoVigilanciaParecerAtividade requerimentoVigilanciaParecerAtividade = new RequerimentoVigilanciaParecerAtividade();
            requerimentoVigilanciaParecerAtividade.setAtividadesVigilancia(atividadesVigilancia);
            requerimentoVigilanciaParecerAtividade.setRequerimentoVigilanciaParecer(this.requerimentoVigilanciaParecer);
            BOFactory.save(requerimentoVigilanciaParecerAtividade);
        }

        for (Profissional profissional : this.dto.getProfissionalList()) {
            RequerimentoVigilanciaFiscal fiscal = LoadManager.getInstance(RequerimentoVigilanciaFiscal.class)
                    .addProperties(new HQLProperties(RequerimentoVigilanciaFiscal.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaFiscal.PROP_PROFISSIONAL, profissional))
                    .setMaxResults(1).start().getVO();

            if (fiscal == null) {
                fiscal = new RequerimentoVigilanciaFiscal();
                fiscal.setProfissional(profissional);
                fiscal.setRequerimentoVigilancia(requerimentoVigilancia);
                fiscal = BOFactory.save(fiscal);
            }
            RequerimentoVigilanciaParecerFiscal requerimentoVigilanciaParecerFiscal = new RequerimentoVigilanciaParecerFiscal();
            requerimentoVigilanciaParecerFiscal.setProfissional(profissional);
            requerimentoVigilanciaParecerFiscal.setRequerimentoVigilanciaParecer(this.requerimentoVigilanciaParecer);
            BOFactory.save(requerimentoVigilanciaParecerFiscal);
        }

        // Gerar Ocorrência do Requerimento
        CadastroOcorrenciaRequerimentoVigilanciaDTO dto = new CadastroOcorrenciaRequerimentoVigilanciaDTO();
        dto.setProfissionalList(this.dto.getProfissionalList());
        dto.setRequerimentoVigilancia(requerimentoVigilancia);
        dto.setData(DataUtil.getDataAtual());
        dto.setSituacao(this.dto.getSituacao());
        dto.setDescricao("Lançamento de parecer");
        dto.setMotivo("Lançamento de parecer " + this.dto.getDescricao() + " Nº: " + this.requerimentoVigilanciaParecer.getNumeroParecer() + " para a situação: " + (this.dto.getSituacao().equals(1L) ? "Parado" : "Em andamento"));
        BOFactory.getBO(VigilanciaFacade.class).gerarOcorrenciaRequerimentoVigilanciaProfissional(dto);

        // Gerar lancamento de atividades
        FaturamentoRequerimentoDTOParam param = new FaturamentoRequerimentoDTOParam();
        param.setEstabelecimento(dto.getRequerimentoVigilancia().getEstabelecimento());
        param.setVigilanciaPessoa(dto.getRequerimentoVigilancia().getVigilanciaPessoa());
        param.setProfissionalList(this.dto.getProfissionalList());
        param.setAtividadesVigilanciaList(this.dto.getAtividadesVigilanciaList());
        param.setDataFaturamento(this.dto.getData());
        param.setObservacao(this.dto.getDescricao());
        param.setRequerimentoVigilancia(requerimentoVigilancia);
        param.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.LANCAMENTO_PARECER.descricao());

        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoRequerimento(param);

        // enviar email do parecer (caso de denúncia)
        if(TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
            if(requerimentoVigilancia.getEmailSolicitante() != null) {
                EnviarEmailDTO enviarEmailDTO = new EnviarEmailDTO();
                enviarEmailDTO.setEmailDestino(requerimentoVigilancia.getEmailSolicitante());
                enviarEmailDTO.setAssunto("Parecer referente ao Requerimento de Denúncia/Reclamação Nº " + requerimentoVigilancia.getProtocoloFormatado());
                enviarEmailDTO.setMensagem(getDescricaoParecerEmail());
                BOFactory.getBO(ComunicacaoFacade.class).enviarEmail(enviarEmailDTO);
            }
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, this.requerimentoVigilanciaParecer, this.dto.getAnexoDTOList(), this.dto.getAnexoExcluidoDTOList());

        if(RequerimentoVigilancia.SituacaoOcorrencia.PARADO.value().equals(dto.getSituacao())) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailCadastroParecerRequerimento(requerimentoVigilancia, this.requerimentoVigilanciaParecer.getDescricaoParecer());
        }
    }

    public String getDescricaoParecerEmail(){
        StringBuilder builder = new StringBuilder();
        builder.append("Lançamento de Parecer ");
        builder.append( " Nº ");
        builder.append(this.requerimentoVigilanciaParecer.getNumeroParecer());
        builder.append( ", ");
        builder.append(this.dto.getDescricao());
        builder.append("\n");
        builder.append(this.dto.getParecer());
        return builder.toString();
    }

    public RequerimentoVigilanciaParecer getRequerimenroVigilanciaParecer() {
        return requerimentoVigilanciaParecer;
    }
}
