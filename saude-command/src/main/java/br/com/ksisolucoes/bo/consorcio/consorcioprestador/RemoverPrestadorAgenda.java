package br.com.ksisolucoes.bo.consorcio.consorcioprestador;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorAgenda;

/**
 *
 * <AUTHOR>
 */
public class RemoverPrestadorAgenda extends AbstractCommandTransaction {

    private ConsorcioPrestadorAgenda consorcioPrestadorAgenda;

    public RemoverPrestadorAgenda(ConsorcioPrestadorAgenda consorcioPrestadorAgenda) {
        this.consorcioPrestadorAgenda = consorcioPrestadorAgenda;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        consorcioPrestadorAgenda = (ConsorcioPrestadorAgenda) getSession().get(ConsorcioPrestadorAgenda.class, consorcioPrestadorAgenda.getCodigo());
        if(consorcioPrestadorAgenda != null) {
            BOFactory.delete(consorcioPrestadorAgenda);
        }
    }

}
