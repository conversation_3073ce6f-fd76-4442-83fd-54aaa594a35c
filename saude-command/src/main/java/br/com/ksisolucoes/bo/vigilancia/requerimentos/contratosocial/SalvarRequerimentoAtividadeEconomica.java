package br.com.ksisolucoes.bo.vigilancia.requerimentos.contratosocial;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoAtividadeEconomicaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoAtividade;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoAtividadeEconomica extends AbstractCommandTransaction<SalvarRequerimentoAtividadeEconomica> {

    private final RequerimentoAtividadeEconomicaDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<EstabelecimentoAtividade> estabelecimentoAtividadeList;
    private boolean gerarOcorrenciaCadastro = false;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarRequerimentoAtividadeEconomica(RequerimentoAtividadeEconomicaDTO dto) {
        this.dto = dto;
        this.estabelecimentoAtividadeList = new ArrayList<EstabelecimentoAtividade>(dto.getEstabelecimentoAtividadeList());
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
        if (CollectionUtils.isAllEmpty(estabelecimentoAtividadeList)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_obrgatorio_ao_menos_uma_atividade"));
        }

        boolean existsAtividadePrincipal = Lambda.exists(estabelecimentoAtividadeList, having(on(EstabelecimentoAtividade.class).getFlagPrincipal(), equalTo(RepositoryComponentDefault.SIM_LONG)));
        if (!existsAtividadePrincipal) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_atividade_principal"));
        }

        RequerimentoAtividadeEconomica requerimentoAtividadeEconomica = dto.getRequerimentoAtividadeEconomica();

        boolean salvarEstabelecimentoAtividade = false;
        if (requerimentoAtividadeEconomica.getRequerimentoVigilancia().getCodigo() == null) {
            dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoAtividadeEconomica().getEstabelecimento());
            dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value());
            dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            if (ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(configuracaoVigilancia.getFlagTipoGestaoRequerimento())) {
                dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value());
            } else {
                dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
            }
            salvarEstabelecimentoAtividade = true;
            gerarOcorrenciaCadastro = true;
        }

        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoAtividadeEconomica().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), gerarOcorrenciaCadastro);

        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
        }

        dto.getRequerimentoAtividadeEconomica().setRequerimentoVigilancia(requerimentoVigilancia);
        BOFactory.save(requerimentoAtividadeEconomica);

        {
            if (salvarEstabelecimentoAtividade && CollectionUtils.isNotNullEmpty(estabelecimentoAtividadeList)) {
                for (EstabelecimentoAtividade estb : estabelecimentoAtividadeList) {
                    AtividadeEstabelecimento atvd = LoadManager.getInstance(AtividadeEstabelecimento.class)
                            .setId(estb.getAtividadeEstabelecimento().getCodigo())
                            .start().getVO();

                    RequerimentoAtividadeEconomicaAtividade raea = new RequerimentoAtividadeEconomicaAtividade();
                    raea.setAtividadeEstabelecimento(estb.getAtividadeEstabelecimento());
                    raea.setRequerimentoAtividadeEconomica(requerimentoAtividadeEconomica);
                    raea.setFlagPrincipal(estb.getFlagPrincipal());

                    raea.setIsentoTaxa(estb.getIsentoTaxa());
                    raea.setQuantidadeTaxa(atvd.getQuantidadeTaxa());
                    raea.setQuantidadeCobrada(estb.getQuantidadeTaxa().longValue());

                    if (atvd.getTaxa() == null) {
                        throw new ValidacaoException("Atividade do Estabelecimento sem taxa Definida: " + atvd.getDescricao());
                    }

                    raea.setValorIndice(FinanceiroVigilanciaHelper.getTaxaIndiceVigente(atvd.getTaxa()).getValorIndice());

                    BOFactory.save(raea);
                }
            }
        }
        {
            if (CollectionUtils.isNotNullEmpty(dto.getRequerimentoAtividadeEconomicaCnaeList())) {
                for (RequerimentoAtividadeEconomicaCnae requerimentoAtividadeEconomicaCnae : dto.getRequerimentoAtividadeEconomicaCnaeList()) {
                    requerimentoAtividadeEconomicaCnae.setRequerimentoAtividadeEconomica(requerimentoAtividadeEconomica);
                }
                VOUtils.persistirListaVosModificados(RequerimentoAtividadeEconomicaCnae.class, dto.getRequerimentoAtividadeEconomicaCnaeList(), new QueryCustom.QueryCustomParameter(RequerimentoAtividadeEconomicaCnae.PROP_REQUERIMENTO_ATIVIDADE_ECONOMICA, requerimentoAtividadeEconomica));
            }
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());

        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}