package br.com.ksisolucoes.bo.controle.programaweb;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaCaminhoMenuWebDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.Arrays;
import java.util.List;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryBuscaCaminhoMenuWeb extends CommandQuery<QueryBuscaCaminhoMenuWeb> {

    private ProgramaWeb programaWeb;
    private List<QueryBuscaCaminhoMenuWebDTO> caminhos;

    public QueryBuscaCaminhoMenuWeb(ProgramaWeb programaWeb) {
        this.programaWeb = programaWeb;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryBuscaCaminhoMenuWebDTO.class.getName());

        hql.addToSelect("t1.descricao", "descricaoLabel");
        hql.addToSelect("t4.descricao || ':;' || t3.descricao || ':;' || t2.descricao || ':;' || t1.descricao", "caminho");

        hql.addToFrom("MenuWeb t1, MenuWeb t2, MenuWeb t3, MenuWeb t4 ");

        hql.addToWhereWhithAnd("t1.programaWeb = ", programaWeb);
        hql.addToWhereWhithAnd("t2.codigo = t1.menuWebPai.codigo");
        hql.addToWhereWhithAnd("t2.menuWebPai.codigo = t3.codigo");
        hql.addToWhereWhithAnd("t3.menuWebPai.codigo = t4.codigo");

        hql.addToWhereWhithAnd("t1.layoutMenu in(:layoutMenuList)");
        hql.addToOrder("t1.layoutMenu desc");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);

        Long layoutMenu = MenuWeb.LayoutMenu.PADRAO.value();
        try {
            layoutMenu = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("layoutMenu");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (MenuWeb.LayoutMenu.HOSPITAL.value().equals(layoutMenu)) {
            query.setParameterList("layoutMenuList", Arrays.asList(MenuWeb.LayoutMenu.PADRAO.value(), MenuWeb.LayoutMenu.HOSPITAL.value()));
        } else if (MenuWeb.LayoutMenu.UNIDADE_SAUDE.value().equals(layoutMenu)) {
            query.setParameterList("layoutMenuList", Arrays.asList(MenuWeb.LayoutMenu.PADRAO.value(), MenuWeb.LayoutMenu.UNIDADE_SAUDE.value()));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.caminhos = hql.getBeanList((List) result);
    }

    public List<QueryBuscaCaminhoMenuWebDTO> getCaminhos() {
        return caminhos;
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(1);
    }

}
