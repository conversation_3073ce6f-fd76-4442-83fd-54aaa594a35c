package br.com.ksisolucoes.bo.service.tempstore;

import br.com.celk.bo.service.tempstore.interfaces.dto.CarregarRegistroTemporarioDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.TempStore;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import static ch.lambdaj.Lambda.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CarregarRegistroTemporario extends AbstractCommandTransaction {

    private CarregarRegistroTemporarioDTO dto;
    private Map<String, String> result = new HashMap<String, String>();
    private List<TempStore> resultList = new ArrayList<TempStore>();

    public CarregarRegistroTemporario(CarregarRegistroTemporarioDTO dto) {
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        TempStore on = on(TempStore.class);
        
        Criteria createCriteria = getSession().createCriteria(TempStore.class);
        
        createCriteria.add(Restrictions.eq(path(on.getCodigoTabelaReferencia()), dto.getCodigoManager().getCodigoManager()));
        createCriteria.add(Restrictions.eq(path(on.getTabelaReferencia()), dto.getCodigoManager().getClass().getSimpleName()));
        
        if (dto.getCampo()!=null) {
            createCriteria.add(Restrictions.eq(path(on.getCampo()), dto.getCampo()));
        }

        if (dto.getAgrupador()!=null) {
            createCriteria.add(Restrictions.eq(path(on.getAgrupador()), dto.getAgrupador()));
        }

        if (dto.getProfissional() != null) {
            createCriteria.add(Restrictions.eq(path(on.getProfissional()), dto.getProfissional()));
        }
        
        List<TempStore> temps = (List<TempStore>) createCriteria.list();
        
        if(dto.isLoadTempAll()){
            resultList.addAll(temps);
        } else {
            for (TempStore tempStore : temps) {
                result.put(tempStore.getCampo(), tempStore.getValor());
            }
        }
    }

    public Map<String, String> getResult() {
        return result;
    }

    public List<TempStore> getResultList() {
        return resultList;
    }

}
