package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoinspecaosanitaria;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.FuncionarioEnvolvidoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaFuncionarioEnvolvido;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;

import java.util.List;

public class SalvarRequerimentoInspecaoSanitariaFuncionariosEnvolvidos extends AbstractCommandTransaction<SalvarRequerimentoInspecaoSanitariaFuncionariosEnvolvidos> {

    private RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria;
    private List<FuncionarioEnvolvidoDTO> funcionariosEnvolvidos;
    private List<FuncionarioEnvolvidoDTO> funcionariosEnvolvidosExcluir;

    public SalvarRequerimentoInspecaoSanitariaFuncionariosEnvolvidos(RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria, List<FuncionarioEnvolvidoDTO> funcionariosEnvolvidos, List<FuncionarioEnvolvidoDTO> funcionariosEnvolvidosExcluir) {
        this.requerimentoInspecaoSanitaria = requerimentoInspecaoSanitaria;
        this.funcionariosEnvolvidos = funcionariosEnvolvidos;
        this.funcionariosEnvolvidosExcluir = funcionariosEnvolvidosExcluir;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoInspecaoSanitariaFuncionarioEnvolvido funcionarioEnvolvido;
        for (FuncionarioEnvolvidoDTO dto : funcionariosEnvolvidos){

            funcionarioEnvolvido = dto.getRequerimentoInspecaoSanitariaFuncionarioEnvolvido();
            if (dto.getRequerimentoInspecaoSanitariaFuncionarioEnvolvido() == null){
                funcionarioEnvolvido = new RequerimentoInspecaoSanitariaFuncionarioEnvolvido();
            }
            funcionarioEnvolvido.setRequerimentoInspecaoSanitaria(requerimentoInspecaoSanitaria);
            funcionarioEnvolvido.setNumeroFuncionarios(dto.getNumeroFuncionarios());
            funcionarioEnvolvido.setNumeroTurnosHorarios(dto.getNumeroTurnosHorarios());
            funcionarioEnvolvido.setArea(dto.getArea());
            BOFactory.save(funcionarioEnvolvido);
        }

        for (FuncionarioEnvolvidoDTO dto : funcionariosEnvolvidosExcluir){
            BOFactory.delete(dto.getRequerimentoInspecaoSanitariaFuncionarioEnvolvido());
        }
    }
}
