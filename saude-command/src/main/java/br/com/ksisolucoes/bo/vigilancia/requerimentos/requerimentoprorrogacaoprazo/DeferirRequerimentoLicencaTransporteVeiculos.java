package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoprorrogacaoprazo;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoLicencaTransporteDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaCancelamentoFinalizacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporte;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporteVeiculo;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DeferirRequerimentoLicencaTransporteVeiculos extends AbstractCommandTransaction<DeferirRequerimentoLicencaTransporteVeiculos> {

    private final RequerimentoLicencaTransporteDTO dto;
    private RequerimentoVigilancia rv;
    private RequerimentoLicencaTransporte rlt;
    private Estabelecimento estabelecimento;
    private List<VeiculoLicencaTransporteEstabelecimento> veiculoNovoList;
    private List<RequerimentoLicencaTransporteVeiculo> veiculosLicencaTransporteList;


    public DeferirRequerimentoLicencaTransporteVeiculos(RequerimentoLicencaTransporteDTO dto) {
        this.dto = dto;
        rv = loadRV();
        estabelecimento = loadEstabelecimento(rv);
        rv.setEstabelecimento(estabelecimento);
        rlt = this.dto.getRequerimentoLicencaTransporte();
        rlt.setRequerimentoVigilancia(rv);
        rlt.setEstabelecimento(estabelecimento);

        veiculoNovoList = dto.getVeiculoNovoList();
        veiculosLicencaTransporteList = dto.getRequerimentoLicencaTransporteVeiculoList();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (!containsVeiculosPendentes()) {
            rv.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO.value());
            rv.setDataFinalizacao(DataUtil.getDataAtual());

            RequerimentoVigilanciaCancelamentoFinalizacaoDTO requerimentoVigilanciaCancelamentoFinalizacaoDTO = new RequerimentoVigilanciaCancelamentoFinalizacaoDTO();
            requerimentoVigilanciaCancelamentoFinalizacaoDTO.setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO);
            requerimentoVigilanciaCancelamentoFinalizacaoDTO.setDataFinalizacao(DataUtil.getDataAtual());
            requerimentoVigilanciaCancelamentoFinalizacaoDTO.setRequerimentoVigilancia(rv);
            requerimentoVigilanciaCancelamentoFinalizacaoDTO.setMotivo("Deferimento/Indeferimento dos itens");
            requerimentoVigilanciaCancelamentoFinalizacaoDTO.setUrlAlvara(dto.getUrlQRCode());

            rlt.getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value());
            rlt.getRequerimentoVigilancia().getTipoSolicitacao().setTipoDocumento(TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value());

            Date dataValidade = BOFactory.getBO(VigilanciaFacade.class).calcularDataValidadeRequerimentos(rlt.getRequerimentoVigilancia(), rlt.getAnoBase());
            rlt.setDataValidade(dataValidade);

            if (rv.getEstabelecimento() != null) {
                if (estabelecimento.getDataValidadePrimeiraLicenca() == null && rlt.getDataValidade() != null) {
                    estabelecimento.setDataValidadePrimeiraLicenca(rlt.getDataValidade());
                    estabelecimento = BOFactory.save(estabelecimento);
                    rlt.setEstabelecimento(estabelecimento);
                    rv.setEstabelecimento(estabelecimento);
                    rlt.setRequerimentoVigilancia(rv);
                }
            }

            rlt = BOFactory.save(this.dto.getRequerimentoLicencaTransporte());
            rlt = HibernateUtil.rechargeVO(RequerimentoLicencaTransporte.class, rlt.getCodigo(), rlt.getVersion());
            rv = BOFactory.getBO(VigilanciaFacade.class).cancelarFinalizarRequerimentoVigilancia(requerimentoVigilanciaCancelamentoFinalizacaoDTO);
            rlt.setRequerimentoVigilancia(rv);

            salvarListVeiculosEstabelecimento();
            salvarListVeiculosAdicionais();
            criarRLTVDeferidos();
            deletarDeferidosVeiculoNovo(veiculoNovoList);

            if (CollectionUtils.isNotNullEmpty(dto.getRequerimentoLicencaTransporteVeiculoFaturadosAnteriormenteList())) {
                veiculosLicencaTransporteList.removeAll(dto.getRequerimentoLicencaTransporteVeiculoFaturadosAnteriormenteList());
            }

            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoLicenciamento(rv, veiculosLicencaTransporteList);
        }
    }

    private RequerimentoVigilancia loadRV() {
        return (RequerimentoVigilancia) getSession().createCriteria(RequerimentoVigilancia.class)
                .add(Restrictions.eq(RequerimentoVigilancia.PROP_CODIGO, dto.getRequerimentoLicencaTransporte().getRequerimentoVigilancia().getCodigo()))
                .setMaxResults(1).uniqueResult();
    }

    private Estabelecimento loadEstabelecimento(RequerimentoVigilancia rv) {
        return (Estabelecimento) getSession().createCriteria(Estabelecimento.class)
                .add(Restrictions.eq(Estabelecimento.PROP_CODIGO, rv.getEstabelecimento().getCodigo()))
                .setMaxResults(1).uniqueResult();
    }
    private boolean containsVeiculosPendentes() {
        boolean retorno = false;

        if (CollectionUtils.isNotNullEmpty(dto.getRequerimentoLicencaTransporteVeiculoList())) {
            List<RequerimentoLicencaTransporteVeiculo> veiculosPendentes = Lambda.select(veiculosLicencaTransporteList, Lambda.having(Lambda.on(RequerimentoLicencaTransporteVeiculo.class).getStatus(), Matchers.equalTo(RequerimentoLicencaTransporteVeiculo.Status.PENDENTE.value())));
            retorno = CollectionUtils.isNotNullEmpty(veiculosPendentes);
        }

        if (CollectionUtils.isNotNullEmpty(dto.getVeiculoNovoList())) {
            List<VeiculoLicencaTransporteEstabelecimento> veiculosNovosPendentes = Lambda.select(veiculoNovoList, Lambda.having(Lambda.on(VeiculoLicencaTransporteEstabelecimento.class).getStatus(), Matchers.equalTo(VeiculoLicencaTransporteEstabelecimento.Status.PENDENTE.value())));
            retorno = CollectionUtils.isNotNullEmpty(veiculosNovosPendentes);
        }

        return retorno;
    }

    private void salvarListVeiculosEstabelecimento() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(RequerimentoLicencaTransporteVeiculo.class, veiculosLicencaTransporteList,
                new QueryCustom.QueryCustomParameter(RequerimentoLicencaTransporteVeiculo.PROP_REQUERIMENTO_LICENCA_TRANSPORTE, rlt));
    }

    private void salvarListVeiculosAdicionais() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(VeiculoLicencaTransporteEstabelecimento.class, veiculoNovoList,
                new QueryCustom.QueryCustomParameter(VeiculoLicencaTransporteEstabelecimento.PROP_REQUERIMENTO_LICENCA_TRANSPORTE, rlt));
    }

    private void criarRLTVDeferidos() throws DAOException, ValidacaoException {
        List<RequerimentoLicencaTransporteVeiculo> listDeferidos = dto.getRequerimentoLicencaTransporteVeiculoList();
        List<VeiculoEstabelecimento> veiculosNovos = criarVeiculoEstabelecimento();

        for (VeiculoEstabelecimento x : veiculosNovos) {
            if (VeiculoEstabelecimento.Situacao.ATIVO.value().equals(x.getSituacao())) {
                RequerimentoLicencaTransporteVeiculo rltv = new RequerimentoLicencaTransporteVeiculo();

                rltv.setProtocolo(rlt.getRequerimentoVigilancia().getProtocolo());
                rltv.setVeiculoEstabelecimento(x);
                rltv.setRequerimentoLicencaTransporte(rlt);
                rltv.setStatus(RequerimentoLicencaTransporteVeiculo.Status.DEFERIDO.value());

                BOFactory.save(rltv);
                listDeferidos.add(rltv);
            }
        }

        HibernateSessionFactory.getSession().flush();

        veiculosLicencaTransporteList = listDeferidos;
    }

    private List<VeiculoEstabelecimento> criarVeiculoEstabelecimento() throws DAOException, ValidacaoException {
        List<VeiculoEstabelecimento> listVEDeferidos = new ArrayList<>();

        for (VeiculoLicencaTransporteEstabelecimento x : veiculoNovoList) {
            if (VeiculoLicencaTransporteEstabelecimento.Status.DEFERIDO.value().equals(x.getStatus())) {

                VeiculoEstabelecimento ve = new VeiculoEstabelecimento();

                ve.setEstabelecimento(rlt.getEstabelecimento());
                ve.setPlaca(x.getPlaca());
                ve.setTipoVeiculo(x.getTipoVeiculo());
                ve.setRenavam(x.getRenavam());
                ve.setEspecificacao(x.getMarcaModelo());
                ve.setRestricoes(x.getObservacoes());
                ve.setRefrigerado(x.getRefrigerado());
                ve.setSituacao(VeiculoEstabelecimento.Situacao.ATIVO.value());
                BOFactory.save(ve);
                listVEDeferidos.add(ve);
            }
        }
        HibernateSessionFactory.getSession().flush();

        return listVEDeferidos;
    }

    private List<VeiculoEstabelecimento> carregarVeiculosEstabelecimento() {
        return LoadManager.getInstance(VeiculoEstabelecimento.class)
                .addProperties(new HQLProperties(VeiculoEstabelecimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VeiculoEstabelecimento.PROP_ESTABELECIMENTO, rlt.getEstabelecimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VeiculoEstabelecimento.PROP_SITUACAO, VeiculoEstabelecimento.Situacao.ATIVO.value()))
                .start()
                .getList();
    }

    private List<RequerimentoLicencaTransporteVeiculo> carregarVeiculosDeferidos() {
        return LoadManager.getInstance(RequerimentoLicencaTransporteVeiculo.class)
                .addProperties(new HQLProperties(RequerimentoLicencaTransporteVeiculo.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLicencaTransporteVeiculo.PROP_REQUERIMENTO_LICENCA_TRANSPORTE, rlt))
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoLicencaTransporteVeiculo.PROP_STATUS, RequerimentoLicencaTransporteVeiculo.Status.DEFERIDO.value()))
                .start()
                .getList();
    }

    private void deletarDeferidosVeiculoNovo(List<VeiculoLicencaTransporteEstabelecimento> list) throws DAOException, ValidacaoException {
        for (VeiculoLicencaTransporteEstabelecimento x : list) {
            if (VeiculoLicencaTransporteEstabelecimento.Status.DEFERIDO.value().equals(x.getStatus())) {
                BOFactory.delete(x);
            }
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return rv;
    }
}