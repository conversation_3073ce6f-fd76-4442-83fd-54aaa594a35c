package br.com.ksisolucoes.bo.service.tempstore;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.TempStore;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class ArmazenarRegistroTemporario extends AbstractCommandTransaction {

    private CodigoManager codigoManager;
    private String agrupador;
    private String campo;
    private String valor;
    private TabelaCbo tabelaCbo;
    private Profissional profissional;

    public ArmazenarRegistroTemporario(CodigoManager codigoManager, String campo, String valor) {
        this.codigoManager = codigoManager;
        this.campo = campo;
        this.valor = valor;
    }

    public ArmazenarRegistroTemporario(CodigoManager codigoManager, String agrupador, String campo, String valor) {
        this.codigoManager = codigoManager;
        this.agrupador = agrupador;
        this.campo = campo;
        this.valor = valor;
    }

    public ArmazenarRegistroTemporario(CodigoManager codigoManager, String agrupador, String campo, String valor, Profissional profissional, TabelaCbo tabelaCbo) {
        this.codigoManager = codigoManager;
        this.agrupador = agrupador;
        this.campo = campo;
        this.valor = valor;
        this.profissional = profissional;
        this.tabelaCbo = tabelaCbo;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        TempStore on = on(TempStore.class);
        
        Criteria createCriteria = getSession().createCriteria(TempStore.class);
        
        createCriteria.add(Restrictions.eq(path(on.getCodigoTabelaReferencia()), codigoManager.getCodigoManager()));
        createCriteria.add(Restrictions.eq(path(on.getTabelaReferencia()), codigoManager.getClass().getSimpleName()));
        createCriteria.add(Restrictions.eq(path(on.getCampo()), campo));

        if (agrupador!=null) {
            createCriteria.add(Restrictions.eq(path(on.getAgrupador()), agrupador));
        }
        
        if(tabelaCbo != null){
            createCriteria.add(Restrictions.eq(path(on.getProfissional()), profissional));
        }
        
        TempStore tempStore = (TempStore) createCriteria.uniqueResult();
        
        if (tempStore == null) {
            tempStore = new TempStore();
        }
        
        tempStore.setCodigoTabelaReferencia((Long)codigoManager.getCodigoManager());
        tempStore.setTabelaReferencia(codigoManager.getClass().getSimpleName());
        tempStore.setAgrupador(agrupador);
        tempStore.setCampo(campo);
        tempStore.setValor(valor);
        tempStore.setData(Data.getDataAtual());
        
        if(tabelaCbo != null){
            tempStore.setTabelaCbo(tabelaCbo);
            tempStore.setProfissional(profissional);
        }
        
        BOFactory.save(tempStore);
    }

}
