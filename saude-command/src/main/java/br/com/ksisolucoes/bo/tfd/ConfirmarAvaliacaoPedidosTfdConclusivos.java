package br.com.ksisolucoes.bo.tfd;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.tfd.pedidotfdagendamento.dto.PedidoTfdDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class ConfirmarAvaliacaoPedidosTfdConclusivos extends AbstractCommandTransaction<ConfirmarAvaliacaoPedidosTfdConclusivos> {

    private LaudoTfd laudoTfd;
    private Long numeroCartao;

    public ConfirmarAvaliacaoPedidosTfdConclusivos(PedidoTfdDTO dto) {
        this.laudoTfd = dto.getLaudoTfd();
        this.numeroCartao = dto.getNumeroCartao();
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (this.numeroCartao == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_obrigatorio_X", Bundle.getStringApplication("rotulo_cns")));
        }
        laudoTfd = HibernateUtil.rechargeVO(LaudoTfd.class, this.laudoTfd.getCodigo(), this.laudoTfd.getVersion());
        laudoTfd.setNumeroCartao(numeroCartao);
        cadastrarCnsUsuario(this.laudoTfd.getNumeroCartao());
        BOFactory.save(laudoTfd);
        
        PedidoTfd pedidoTfd = this.laudoTfd.getPedidoTfd();

        PedidoTfd newPedidoTfd = new PedidoTfd();
        newPedidoTfd.setLaudoTfd(laudoTfd);
        newPedidoTfd.setNumeroPedido(pedidoTfd.getNumeroPedido());
        newPedidoTfd.setRegionalSaude(pedidoTfd.getRegionalSaude());
        newPedidoTfd.setTipoTfd(pedidoTfd.getTipoTfd());
        newPedidoTfd.setAltura(pedidoTfd.getAltura());
        newPedidoTfd.setPeso(pedidoTfd.getPeso());
        newPedidoTfd.setDataPedido(pedidoTfd.getDataPedido());
        newPedidoTfd.setUsuario((Usuario)SessaoAplicacaoImp.getInstance().getUsuario());
        pedidoTfd.setDataImpressaoPedido(Data.getDataAtual());

        if(RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaRegulacaoTFD"))){
            newPedidoTfd.setParecer(PedidoTfd.Parecer.PENDENTE_REGULACAO.value());
        }

        pedidoTfd = newPedidoTfd;

        pedidoTfd = BOFactory.getBO(TfdFacade.class).salvarPedidoTfdSemValidaNumeroPedido(pedidoTfd,false);

        laudoTfd.setPedidoTfd(pedidoTfd);
        if(RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaRegulacaoTFD"))) {
            laudoTfd.setStatus(LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value());
        } else {
            laudoTfd.setStatus(LaudoTfd.StatusLaudoTfd.RECEBIDO.value());
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(laudoTfd.getUsuarioCadsus(),
                TipoOcorrencia.TIPO_TFD, Bundle.getStringApplication("msg_reavaliado_pedido_inconclusivo_liberado_encaminhar_regionar"),
                laudoTfd);

    }
    
    private void cadastrarCnsUsuario(Long numeroCartao) throws DAOException, ValidacaoException {
        UsuarioCadsusCns usuarioCadsusCns = (UsuarioCadsusCns) getSession().createCriteria(UsuarioCadsusCns.class)
                .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), this.laudoTfd.getUsuarioCadsus()))
                .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), Coalesce.asLong(numeroCartao)))
                .add(Restrictions.eq(UsuarioCadsusCns.PROP_EXCLUIDO,  RepositoryComponentDefault.NAO_EXCLUIDO))
                .setMaxResults(1).uniqueResult();
        if(usuarioCadsusCns == null){
            usuarioCadsusCns = new UsuarioCadsusCns();
            usuarioCadsusCns.setUsuarioCadsus(laudoTfd.getUsuarioCadsus());
            usuarioCadsusCns.setNumeroCartao(numeroCartao);
            
            usuarioCadsusCns.setDataAlteracao(Data.getDataAtual());
            usuarioCadsusCns.setDataAtribuicao(Data.getDataAtual());
        }
        usuarioCadsusCns.setExcluido(RepositoryComponentDefault.NAO_EXCLUIDO);

        BOFactory.save(usuarioCadsusCns);
    }
}
