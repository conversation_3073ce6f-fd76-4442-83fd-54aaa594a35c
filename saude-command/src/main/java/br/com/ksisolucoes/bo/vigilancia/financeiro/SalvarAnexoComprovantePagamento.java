package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import org.hibernate.criterion.Restrictions;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class SalvarAnexoComprovantePagamento extends AbstractCommandTransaction<SalvarAnexoComprovantePagamento> {

    private final RequerimentoVigilancia requerimentoVigilancia;
    private final RequerimentoVigilanciaAnexoDTO anexoDTO;
    private final RequerimentoVigilanciaAnexo anexoExcluido;
    private final GerenciadorArquivo gerenciadorArquivoExcluido;
    private final VigilanciaFinanceiro vigilanciaFinanceiro;
    private Date dataPagamento;

    public SalvarAnexoComprovantePagamento(RequerimentoVigilancia requerimentoVigilancia, VigilanciaFinanceiro vigilanciaFinanceiro, RequerimentoVigilanciaAnexoDTO anexoDTO, RequerimentoVigilanciaAnexo anexoExcluido, Date dataPagamento) {
        this.anexoDTO = anexoDTO;
        this.anexoExcluido = anexoExcluido;
        this.dataPagamento = dataPagamento;
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
        this.gerenciadorArquivoExcluido = null;
    }

    public SalvarAnexoComprovantePagamento(VigilanciaFinanceiro vigilanciaFinanceiro, RequerimentoVigilanciaAnexoDTO anexoDTO, GerenciadorArquivo gerenciadorArquivoExcluido, Date dataPagamento) {
        this.anexoDTO = anexoDTO;
        this.gerenciadorArquivoExcluido = gerenciadorArquivoExcluido;
        this.dataPagamento = dataPagamento;
        this.vigilanciaFinanceiro = vigilanciaFinanceiro;
        this.requerimentoVigilancia = null;
        this.anexoExcluido = null;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        VigilanciaFinanceiro vigilanciaFinanceiro = (VigilanciaFinanceiro) this.getSession().createCriteria(VigilanciaFinanceiro.class)
                .add(Restrictions.eq(VigilanciaFinanceiro.PROP_CODIGO, this.vigilanciaFinanceiro.getCodigo()))
                .setMaxResults(1)
                .uniqueResult();
        if (anexoDTO != null) {
            GerenciadorArquivo ga;
            RequerimentoVigilanciaAnexo rva;
            if (anexoDTO.getFile() != null) {
                ga = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(anexoDTO.getFile(), anexoDTO.getOrigem(), anexoDTO.getNomeArquivoOriginal());

                if(requerimentoVigilancia != null) {
                    rva = new RequerimentoVigilanciaAnexo();
                    rva.setRequerimentoVigilancia(requerimentoVigilancia);
                    rva.setGerenciadorArquivo(ga);
                    rva.setDescricao(anexoDTO.getDescricaoAnexo());

                    BOFactory.save(rva);
                }

                vigilanciaFinanceiro.setAnexoComprovantePagamento(ga);
                if(requerimentoVigilancia != null) {
                    vigilanciaFinanceiro.setStatus(VigilanciaFinanceiro.Status.AGUARDANDO_PAGAMENTO.value());
                } else {
                    vigilanciaFinanceiro.setStatus(VigilanciaFinanceiro.Status.PAGO.value());
                }
                vigilanciaFinanceiro.setDataPagamento(dataPagamento);
                BOFactory.save(vigilanciaFinanceiro);
            }
        }

        if (anexoExcluido != null && anexoExcluido.getCodigo() != null) {
            if(requerimentoVigilancia != null) {
                RequerimentoVigilanciaAnexo anexo = (RequerimentoVigilanciaAnexo) this.getSession().createCriteria(RequerimentoVigilanciaAnexo.class)
                        .add(Restrictions.eq(RequerimentoVigilanciaAnexo.PROP_CODIGO, anexoExcluido.getCodigo()))
                        .setMaxResults(1)
                        .uniqueResult();
                if (anexo != null) {
                    if (anexo.getDataIntegracao() == null) {
                        BOFactory.delete(anexo);
                        BOFactory.delete(anexo.getGerenciadorArquivo());
                    } else {
                        anexo.setDataIntegracao(null);
                        anexo.setStatus(RequerimentoVigilanciaAnexo.Status.EXCLUIDO.value());
                        BOFactory.save(anexo);
                    }
                    if (anexoDTO.getFile() == null) {
                        vigilanciaFinanceiro.setAnexoComprovantePagamento(null);
                        vigilanciaFinanceiro.setStatus(VigilanciaFinanceiro.Status.EMITIDO.value());
                        vigilanciaFinanceiro.setDataPagamento(null);
                        BOFactory.save(vigilanciaFinanceiro);
                    }
                }
            } else if(gerenciadorArquivoExcluido != null && gerenciadorArquivoExcluido.getCodigo() != null) {
                if (anexoDTO.getFile() == null) {
                    vigilanciaFinanceiro.setAnexoComprovantePagamento(null);
                    vigilanciaFinanceiro.setStatus(VigilanciaFinanceiro.Status.EMITIDO.value());
                    vigilanciaFinanceiro.setDataPagamento(null);
                    BOFactory.save(vigilanciaFinanceiro);
                }
            }
        }
    }
}