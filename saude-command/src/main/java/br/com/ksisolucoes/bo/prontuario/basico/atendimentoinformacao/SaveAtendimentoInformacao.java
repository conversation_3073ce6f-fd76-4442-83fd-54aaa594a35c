package br.com.ksisolucoes.bo.prontuario.basico.atendimentoinformacao;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.restservice.dto.GravarAtendimentoProntuarioExternoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.integracao.ConfiguracaoEmpresaIntegracao;

/**
 *
 * <AUTHOR>
 */
public class SaveAtendimentoInformacao extends SaveVO<AtendimentoInformacao> {

    private static final long serialVersionUID = 1L;

    public SaveAtendimentoInformacao(AtendimentoInformacao vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {

        if (vo.getCodigo() == null) {
            //Chamada do webservice para verificação de prontuarios externos, executado em modo assincróno.
            GravarAtendimentoProntuarioExternoDTOParam param = new GravarAtendimentoProntuarioExternoDTOParam();
            param.setUsuarioCadsus(vo.getUsuarioCadsus());
            
            Long count = LoadManager.getInstance(ConfiguracaoEmpresaIntegracao.class)
                .addGroup(new QueryCustom.QueryCustomGroup(ConfiguracaoEmpresaIntegracao.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                .start().getVO();
            if(count > 0){
                BOFactory.getBO(AtendimentoFacade.class).salvarAtendimentoProntuarioService(param);
            }
        }

        if (this.vo.getSequenciaCiclo() == null) {
            vo.setSequenciaCiclo(0L);
        }
    }
}
