package br.com.ksisolucoes.bo.integracao.raas;

import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasCabecalhoBind;
import br.com.celk.services.mobile.integracao.ListAggregationStrategy;
import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasAtencaoPsicossocialDadosPacienteBind;
import br.com.ksisolucoes.bo.integracao.raas.bind.GeracaoRaasAtencaoPsicossocialProcedimentoBind;
import br.com.ksisolucoes.bo.integracao.raas.dto.GeracaoRaasDTO;
import br.com.ksisolucoes.bo.interfaces.facade.AtendimentoGeralFacade;
import br.com.ksisolucoes.bo.siab.raas.GerarRaasProcessoDTOParam;
import br.com.ksisolucoes.bo.siab.raas.RaasGeradoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.RaasProcesso;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.RoutesBuilder;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.dataformat.bindy.fixed.BindyFixedLengthDataFormat;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.spi.DataFormat;
import org.apache.camel.spi.PackageScanClassResolver;
import org.apacheextras.camel.jboss.JBossPackageScanClassResolver;

/**
 *
 * <AUTHOR>
 */
public class GerarRaas extends AbstractCommandTransaction {

    CamelContext context;
    private ProducerTemplate template;
    private GerarRaasProcessoDTOParam param;
    private List<GeracaoRaasDTO> raasList = new ArrayList<GeracaoRaasDTO>();
    private String retorno;
    private RaasGeradoDTO raasGeradoDTO = new RaasGeradoDTO();

    public GerarRaas(GerarRaasProcessoDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            raasList = BOFactory.getBO(AtendimentoGeralFacade.class).consultarGeracaoRaas(param);
            if (!CollectionUtils.isNotNullEmpty(raasList)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existem_itens_para_exportar"));
            }

            PackageScanClassResolver jbossResolver = new JBossPackageScanClassResolver();
            context = new DefaultCamelContext();
            context.setPackageScanClassResolver(jbossResolver);

            template = context.createProducerTemplate();
            context.addRoutes(getRouteList());
            context.start();
            retorno = template.requestBody("direct:geracaoRaas", raasList, String.class) + System.lineSeparator();

            raasGeradoDTO.setTexto(retorno);
            raasGeradoDTO.setStatus(RaasProcesso.Status.STATUS_ARQUIVO_GERADO);
        } catch (Exception ex) {
            throw new ValidacaoException(ex);
        } finally {
            try {
                if (context != null) {
                    context.stop();
                }
            } catch (Exception ex) {
                Logger.getLogger(GerarRaas.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private RoutesBuilder getRouteList() {
        RoutesBuilder routeBuilders = new RoutesBuilder() {
            @Override
            public void addRoutesToCamelContext(CamelContext context) throws Exception {
                context.addRoutes(new RouteBuilder() {
                    @Override
                    public void configure() throws Exception {
                        DataFormat df = new BindyFixedLengthDataFormat(GeracaoRaasCabecalhoBind.class);
                        from("direct:geracaoRaas")
                                .process(new RaasProcessor(new GeracaoRaasCabecalhoBind(), new GeracaoRaasAtencaoPsicossocialDadosPacienteBind(),
                                                new GeracaoRaasAtencaoPsicossocialProcedimentoBind()))
                                .split(body(), new ListAggregationStrategy())
                                .marshal(df)
                                .convertBodyTo(String.class)
                                .end()
                                .process(new Processor() {
                                    @Override
                                    public void process(Exchange exchange) throws Exception {
                                        getProcess(exchange);
                                    }
                                })
                                .convertBodyTo(String.class);
                    }
                });
            }
        };

        return routeBuilders;
    }

    public void getProcess(Exchange exchange) {
        Object obj = exchange.getIn().getBody();
        List<String> f = (List<String>) obj;
        StringBuilder sb = new StringBuilder();
        for (String string : f) {
            sb.append(string);
        }
        sb.replace(sb.toString().lastIndexOf("\n"), sb.toString().lastIndexOf("\n") + 1, "");
        exchange.getOut().setBody(sb.toString(), String.class);
    }

//    private void atualizaRaasProcesso(String msgErro) throws DAOException, ValidacaoException {
//        BOFactory.getBO(AtendimentoGeralFacade.class).atualizarRaasProcesso(param.getCodigoRaasProcesso(), retorno, msgErro);
//    }

    public RaasGeradoDTO getRaasGeradoDTO() {
        return raasGeradoDTO;
    }
}
