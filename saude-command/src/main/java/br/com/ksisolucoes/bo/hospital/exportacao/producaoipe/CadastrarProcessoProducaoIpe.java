package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ProducaoIpeProcesso;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarProcessoProducaoIpe extends AbstractCommandTransaction {

    List<Long> listaNotas;
    private Long codIpeProcesso;
    private Long tipoConta;
    private ProducaoIpeProcesso pip = new ProducaoIpeProcesso();

    public CadastrarProcessoProducaoIpe(List<Long> listaNotas, Long tipoConta) {
        this.listaNotas = listaNotas;
        this.tipoConta = tipoConta;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        if (tipoConta == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_conta_ipe_nao_configurado"));
        }

        pip.setUsuarioGeracao(getSessao().getUsuario());
        pip.setDataGeracao(DataUtil.getDataAtual());
        pip.setStatus(ProducaoIpeProcesso.STATUS_GERANDO_TEXTO);

        pip = BOFactory.save(pip);
        codIpeProcesso = pip.getCodigo();

        if (TipoAtendimento.TipoContaIpe.INTERNACAO_HOSPITALAR.value().equals(tipoConta) || TipoAtendimento.TipoContaIpe.CONTA_AMBULATORIAL.value().equals(tipoConta)) {
            BOFactory.getBO(HospitalFacade.class).geraProducaoIpeHospitalarAmbulatorial(codIpeProcesso, listaNotas, tipoConta);
        }
    }

    public Long getCodIpeProcesso() {
        return codIpeProcesso;
    }

    public ProducaoIpeProcesso getProducaoIpeProcesso() {
        return pip;
    }
}
