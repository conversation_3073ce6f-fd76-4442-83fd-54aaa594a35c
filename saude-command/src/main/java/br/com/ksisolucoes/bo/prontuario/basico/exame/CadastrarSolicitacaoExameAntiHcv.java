package br.com.ksisolucoes.bo.prontuario.basico.exame;

import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoAntiHcv;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CadastrarSolicitacaoExameAntiHcv extends AbstractCommandTransaction {
    
    private ExameCadastroAprovacaoDTO param;
    private RequisicaoAntiHcv requisicaoAntiHcv;
    private Long codigoExameCadastrado;

    public CadastrarSolicitacaoExameAntiHcv(ExameCadastroAprovacaoDTO param, RequisicaoAntiHcv requisicaoAntiHcv) {
        this.param = param;
        this.requisicaoAntiHcv = requisicaoAntiHcv;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(param.getExameProcedimentoDTOs().isEmpty()){
            throw new ValidacaoException(Bundle.getStringApplication("msg_exame_sem_procedimento"));
        }
        
        param.setTipoConvenio(TipoExame.CONVENIO_SUS);
        param.setGeraAtendimentoProntuario(Boolean.TRUE);
        
        codigoExameCadastrado = BOFactory.getBO(ExameFacade.class).salvarExame(param);
        
        ExameRequisicao exameRequisicao = (ExameRequisicao) getSession().createCriteria(ExameRequisicao.class)
            .add(Restrictions.eq(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO), codigoExameCadastrado))
            .add(Restrictions.eq(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, param.getExameProcedimentoDTOs().get(0).getExameProcedimento()))
            .uniqueResult();
        
        requisicaoAntiHcv.setExameRequisicao(exameRequisicao);
        BOFactory.save(requisicaoAntiHcv);
    }
    
    public Long getCodigoExameCadastrado() {
        return codigoExameCadastrado;
    }
}