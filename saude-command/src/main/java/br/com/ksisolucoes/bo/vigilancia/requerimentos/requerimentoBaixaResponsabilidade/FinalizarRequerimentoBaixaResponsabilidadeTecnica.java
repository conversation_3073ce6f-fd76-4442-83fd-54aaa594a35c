package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoBaixaResponsabilidade;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoSetores;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaResponsabilidade;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class FinalizarRequerimentoBaixaResponsabilidadeTecnica extends AbstractCommandTransaction<FinalizarRequerimentoBaixaResponsabilidadeTecnica> {

    private final RequerimentoVigilancia requerimentoVigilancia;

    public FinalizarRequerimentoBaixaResponsabilidadeTecnica(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Estabelecimento estabelecimento = (Estabelecimento) getSession().get(Estabelecimento.class, requerimentoVigilancia.getEstabelecimento().getCodigo());

        RequerimentoBaixaResponsabilidade rbr = (RequerimentoBaixaResponsabilidade) getSession().createCriteria(RequerimentoBaixaResponsabilidade.class)
                .add(Restrictions.eq(RequerimentoBaixaResponsabilidade.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .uniqueResult();
        
        if(rbr != null && rbr.getEstabelecimentoSetores() == null){
            EstabelecimentoResponsavelTecnico estabelecimentoResponsavelTecnico = (EstabelecimentoResponsavelTecnico) getSession().createCriteria(EstabelecimentoResponsavelTecnico.class)
                .add(Restrictions.eq(EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, rbr.getResponsavelTecnico()))
                .add(Restrictions.eq(EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO, estabelecimento))
                .uniqueResult();
            
            if (estabelecimentoResponsavelTecnico != null) {
                BOFactory.delete(estabelecimentoResponsavelTecnico);
            }
            BOFactory.getBO(VigilanciaFacade.class).atualizarEstabelecimentoDataSemResponsavelTecnico(estabelecimento);
        } else if (rbr.getEstabelecimentoSetores() != null){
            EstabelecimentoSetores estabelecimentoSetores = (EstabelecimentoSetores) getSession().get(EstabelecimentoSetores.class, rbr.getEstabelecimentoSetores().getCodigo());
            estabelecimentoSetores.setResponsavelTecnico(null);
            BOFactory.save(estabelecimentoSetores);
        }

        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.BAIXA_RESPONSABILIDADE_DEFERIMENTO);

    }
}