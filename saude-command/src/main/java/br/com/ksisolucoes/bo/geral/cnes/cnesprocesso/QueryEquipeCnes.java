package br.com.ksisolucoes.bo.geral.cnes.cnesprocesso;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.integracao.cnes.dto.QueryEquipeCnesDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Equipe;
import org.hibernate.LockMode;
import org.hibernate.Query;

import java.util.List;

/**
 * Created by sulivan on 27/06/17.
 */
public class QueryEquipeCnes extends CommandQuery<QueryEquipeCnes> {

    private QueryEquipeCnesDTOParam param;
    private List<Equipe> equipeList;

    public QueryEquipeCnes(QueryEquipeCnesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(Equipe.class.getName());

        hql.addToSelect("equipe.codigo", "codigo");
        hql.addToSelect("equipe.sequenciaEquipe", "sequenciaEquipe");
        hql.addToSelect("equipe.referencia", "referencia");
        hql.addToSelect("equipe.dataAtivacao", "dataAtivacao");
        hql.addToSelect("equipe.dataDesativacao", "dataDesativacao");
        hql.addToSelect("equipe.tipoQuilombo", "tipoQuilombo");
        hql.addToSelect("equipe.tipoAssentado", "tipoAssentado");
        hql.addToSelect("equipe.tipoGeral", "tipoGeral");
        hql.addToSelect("equipe.tipoEscola", "tipoEscola");
        hql.addToSelect("equipe.tipoPronasci", "tipoPronasci");
        hql.addToSelect("equipe.tipoIndigena", "tipoIndigena");
        hql.addToSelect("equipe.dataAtualizacao", "dataAtualizacao");
        hql.addToSelect("equipe.tipoDesativacao", "tipoDesativacao");
        hql.addToSelect("equipe.ativo", "ativo");
        hql.addToSelect("equipe.dataInativacao", "dataInativacao");
        hql.addToSelect("equipe.versionAll", "versionAll");
        hql.addToSelect("equipe.equipeCnes", "equipeCnes");
        hql.addToSelect("equipe.permiteAlteracaoPaciente", "permiteAlteracaoPaciente");
        hql.addToSelect("equipe.version", "version");

        hql.addToSelect("equipeArea.codigo", "equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "equipeArea.descricao");
        hql.addToSelect("equipeArea.dataAtualizacao", "equipeArea.dataAtualizacao");
        hql.addToSelect("equipeArea.version", "equipeArea.version");

        hql.addToSelect("tipoEquipe.codigo", "tipoEquipe.codigo");
        hql.addToSelect("tipoEquipe.descricao", "tipoEquipe.descricao");
        hql.addToSelect("tipoEquipe.version", "tipoEquipe.version");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");
        hql.addToSelect("empresa.version", "empresa.version");

        hql.addToSelect("motivoDesativacao.codigo", "motivoDesativacao.codigo");
        hql.addToSelect("motivoDesativacao.descricao", "motivoDesativacao.descricao");
        hql.addToSelect("motivoDesativacao.version", "motivoDesativacao.version");

        hql.addToSelect("cnesProcesso.codigo", "cnesProcesso.codigo");
        hql.addToSelect("cnesProcesso.version", "cnesProcesso.version");

        hql.addToFrom("Equipe equipe"
                + " LEFT JOIN equipe.empresa empresa"
                + " LEFT JOIN equipe.tipoEquipe tipoEquipe"
                + " LEFT JOIN equipe.equipeArea equipeArea"
                + " LEFT JOIN equipeArea.cidade cidade"
                + " LEFT JOIN equipe.motivoDesativacao motivoDesativacao"
                + " LEFT JOIN equipe.cnesProcesso cnesProcesso");

        hql.addToWhereWhithAnd("equipe.equipeCnes = :codigoEquipeCnes");
        hql.addToWhereWhithAnd("equipe.ativo = 'S'");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("codigoEquipeCnes", param.getCodigoEquipeCnes());
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(1);
        query.setLockMode("equipe", LockMode.PESSIMISTIC_WRITE);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.equipeList = hql.getBeanList((List) result, false);
    }

    public Equipe getEquipe() {
        if(CollectionUtils.isNotNullEmpty(equipeList)){
            return equipeList.get(0);
        }
        return null;
    }
}
