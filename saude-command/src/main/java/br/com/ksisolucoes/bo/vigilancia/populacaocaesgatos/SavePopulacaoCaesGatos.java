package br.com.ksisolucoes.bo.vigilancia.populacaocaesgatos;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatos;
import br.com.ksisolucoes.vo.vigilancia.PopulacaoCaesGatosTipoAnimal;

import java.util.Date;
import java.util.List;

public class SavePopulacaoCaesGatos extends SaveVO<PopulacaoCaesGatos> {
    private PopulacaoCaesGatos populacaoCaesGatos;
    private List<PopulacaoCaesGatosTipoAnimal> lstPopulacaoCaesGatosTipoAnimal;
    private String horaAtual;

    public SavePopulacaoCaesGatos(PopulacaoCaesGatos vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo != null) {
            this.vo.setDataAlteracao(DataUtil.getDataAtual());
            this.vo.setCodigoUsuario(getSessao().<Usuario>getUsuario());

            if (this.vo.getDataCadastro() == null) {
                this.vo.setDataCadastro(DataUtil.getDataAtual());
            }
        }
    }
}
