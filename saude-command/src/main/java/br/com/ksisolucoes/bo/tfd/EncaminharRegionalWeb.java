package br.com.ksisolucoes.bo.tfd;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.tfd.interfaces.facade.TfdFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.tfd.dto.EncaminhamentoLaudoTfdDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.EloPedidoTfdLoteEncaminhamentoTfd;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.LoteEncaminhamentoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class EncaminharRegionalWeb extends AbstractCommandTransaction {

    private Long codigoLoteEncaminhamentoTfd;
    private List<EncaminhamentoLaudoTfdDTO> encaminhamentoLaudoTfdDTOList;
    private String responsavel;

    public EncaminharRegionalWeb(Long codigoLoteEncaminhamentoTfd, List<EncaminhamentoLaudoTfdDTO> encaminhamentoLaudoTfdDTOList, String responsavel) {
        this.codigoLoteEncaminhamentoTfd = codigoLoteEncaminhamentoTfd;
        this.encaminhamentoLaudoTfdDTOList = encaminhamentoLaudoTfdDTOList;
        this.responsavel = responsavel;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        PedidoTfd pedido = (PedidoTfd) getSession().createCriteria(PedidoTfd.class)
                .add(Restrictions.eq(PedidoTfd.PROP_CODIGO, encaminhamentoLaudoTfdDTOList.get(0).getLaudoTfd().getPedidoTfd().getCodigo()))
                .uniqueResult();
        
        LoteEncaminhamentoTfd loteEncaminhamentoTfd;
        if(codigoLoteEncaminhamentoTfd == null){
            loteEncaminhamentoTfd = new LoteEncaminhamentoTfd();
            loteEncaminhamentoTfd.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
            loteEncaminhamentoTfd.setRegionalSaude(pedido.getRegionalSaude());
            loteEncaminhamentoTfd.setDataEncaminhamento(DataUtil.getDataAtual());
        } else {
            loteEncaminhamentoTfd = (LoteEncaminhamentoTfd) getSession().get(LoteEncaminhamentoTfd.class, codigoLoteEncaminhamentoTfd);
        }
        loteEncaminhamentoTfd.setResponsavel(responsavel);
        BOFactory.getBO(CadastroFacade.class).save(loteEncaminhamentoTfd);
        
        
        SolicitacaoAgendamento sa;
        Long statusLaudo;
        EloPedidoTfdLoteEncaminhamentoTfd elo;
        LaudoTfd laudoTfd;
        for (EncaminhamentoLaudoTfdDTO dto : encaminhamentoLaudoTfdDTOList) {
            if(dto.isCancelarElo()){
                PedidoTfd pedidoTfd = HibernateUtil.rechargeVO(PedidoTfd.class, dto.getLaudoTfd().getPedidoTfd().getCodigo(), dto.getLaudoTfd().getPedidoTfd().getVersion());
                BOFactory.getBO(TfdFacade.class).cancelarEncaminhamentoTfdWeb(dto.getElo(), pedidoTfd, Bundle.getStringApplication("msg_encaminhamento_cancelado"));
            } else if(dto.getElo() == null){
                laudoTfd = HibernateUtil.rechargeVO(LaudoTfd.class, dto.getLaudoTfd().getCodigo(), dto.getLaudoTfd().getVersion());
                statusLaudo = laudoTfd.getStatus();
                laudoTfd.setStatus(LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value());

                BOFactory.getBO(CadastroFacade.class).save(laudoTfd);

                PedidoTfd pedidoTfd = HibernateUtil.rechargeVO(PedidoTfd.class, laudoTfd.getPedidoTfd().getCodigo(), laudoTfd.getPedidoTfd().getVersion());
                pedidoTfd.setDataEncaminhamentoRegional(DataUtil.getDataAtual());
                pedidoTfd.setLoteEncaminhamento(loteEncaminhamentoTfd);

                if(LaudoTfd.StatusLaudoTfd.RECEBIDO.value().equals(statusLaudo)){
                    BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(laudoTfd.getUsuarioCadsus(),
                        TipoOcorrencia.TIPO_TFD,
                        Bundle.getStringApplication("msg_encaminhado_pedido_regional"),
                        laudoTfd);
                } else if(LaudoTfd.StatusLaudoTfd.AUTORIZADO.value().equals(statusLaudo)){
                    sa = gerarSolicitacaoAgendamento(pedidoTfd);

                    pedidoTfd.setSolicitacaoAgendamento(sa);
                    pedidoTfd.setParecer(PedidoTfd.Parecer.AGUARDANDO.value());
                    pedidoTfd = BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);

                    BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(laudoTfd.getUsuarioCadsus(),
                        TipoOcorrencia.TIPO_TFD,
                        Bundle.getStringApplication("msg_retorno_tfd_codigo_solicitacao_X", sa.getCodigo()), laudoTfd);
                }

                elo = new EloPedidoTfdLoteEncaminhamentoTfd();
                elo.setLoteEncaminhamentoTfd(loteEncaminhamentoTfd);
                elo.setPedidoTfd(pedidoTfd);
                BOFactory.getBO(CadastroFacade.class).save(elo);
            }
        }
    }
    
    private SolicitacaoAgendamento gerarSolicitacaoAgendamento(PedidoTfd pedidoTfd) throws DAOException, ValidacaoException {
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();

        LaudoTfd laudoTfd = pedidoTfd.getLaudoTfd();

        solicitacaoAgendamento.setTipoProcedimento(laudoTfd.getTipoProcedimento());
        solicitacaoAgendamento.setEmpresa(laudoTfd.getEmpresa());
        solicitacaoAgendamento.setProfissional(laudoTfd.getProfissional());
        solicitacaoAgendamento.setUsuarioCadsus(laudoTfd.getUsuarioCadsus());
        solicitacaoAgendamento.setCid(laudoTfd.getCid());
        solicitacaoAgendamento.setDataSolicitacao(laudoTfd.getDataLaudo());
        solicitacaoAgendamento.setStatus(SolicitacaoAgendamento.STATUS_FILA_ESPERA);
        solicitacaoAgendamento.setObservacaoUrgente(laudoTfd.getObservacaoUrgente());
        solicitacaoAgendamento.setProcedimento(laudoTfd.getProcedimento());
        solicitacaoAgendamento.setNomeProfissionalOrigem(laudoTfd.getNomeProfissional());
        solicitacaoAgendamento.setTipoConsulta(SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO);

        if (LaudoTfd.SIM.equals(laudoTfd.getFlagUrgente())) {
            solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_URGENTE);
        } else {
            solicitacaoAgendamento.setPrioridade(SolicitacaoAgendamento.PRIORIDADE_ELETIVO);
        }

        Long codigoSolicitacao = BOFactory.getBO(AgendamentoFacade.class).solicitarAgendamento(solicitacaoAgendamento);

        return (SolicitacaoAgendamento) getSession().get(SolicitacaoAgendamento.class, codigoSolicitacao);
    }

}
