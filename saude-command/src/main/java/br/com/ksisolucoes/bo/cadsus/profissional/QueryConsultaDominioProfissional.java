/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.profissional;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaDominioProfissionalDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.cadsus.DominioProfissional;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHorariaHelper;
import org.hibernate.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioProfissional extends CommandQueryPager<QueryConsultaDominioProfissional> {

    private QueryConsultaDominioProfissionalDTOParam param;

    public QueryConsultaDominioProfissional(QueryConsultaDominioProfissionalDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("profissional.codigo", "profissional.codigo");
        hql.addToSelect("profissional.referencia", "profissional.referencia");
        hql.addToSelect("profissional.nome", "profissional.nome");
        hql.addToSelect("profissional.numeroRegistro", "profissional.numeroRegistro");
        hql.addToSelect("dominioProfissional.codigo", true);
        hql.addToSelect("dominioProfissional.nome", true);
        hql.addToSelect("dominioProfissional.registro", true);
        hql.setTypeSelect(DominioProfissional.class.getName());
        hql.setFrom("DominioProfissional dominioProfissional"
                + " left join dominioProfissional.profissional profissional");
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioProfissional.keyword", this.param.getKeyword(), false, true));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioProfissional.nome", this.param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioProfissional.registro", this.param.getRegistro()));

        if (QueryConsultaDominioProfissionalDTOParam.Exibir.SOMENTE_INTERNO.equals(this.param.getExibir())) {
            hql.addToWhereWhithAnd("pr.tipo is null");
        } else if (QueryConsultaDominioProfissionalDTOParam.Exibir.SOMENTE_EXTERNO.equals(this.param.getExibir())) {
            hql.addToWhereWhithAnd("pr.tipo = ", Profissional.TIPO_EXTERNO);
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("dominioProfissional." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("dominioProfissional.nome");
        }
        
        if (param.isPossuiCnsSim()) {
            hql.addToWhereWhithAnd("profissional.naoPossuiCns = ", RepositoryComponentDefault.NAO_LONG);
        }

        if (getSessao() != null) {
            Long codigoEmpresa = getSessao().getCodigoEmpresa();
            if (param.getCodigoEmpresa() != null) {
                codigoEmpresa = param.getCodigoEmpresa();
            }

            if (param.isPeriodoUnidade()) {
                if (param.getTipoAtendimento() != null) {
                    hql.addToWhereWhithAnd(" exists (select 1 from ProfissionalCargaHoraria pch, EloTipoAtendimentoGrupoAtendimentoCbo elo, EloGrupoAtendimentoCbo eloGACbo"
                            + " where " + new ProfissionalCargaHorariaHelper().getWhereValidaCargaHoraria("pch", codigoEmpresa, null, "profissional.codigo", true)
                            + " and eloGACbo.tabelaCbo = pch.tabelaCbo "
                            + " and elo.tipoAtendimento = :tipoAtendimento "
                            + " and eloGACbo.grupoAtendimentoCbo = elo.grupoAtendimentoCbo)");
                } else {
                    hql.addToWhereWhithAnd(" exists (" + new ProfissionalCargaHorariaHelper().getSelectValidaCargaHoraria("1", codigoEmpresa, null, "profissional.codigo", false) + ")");
                }
            }

            if (param.getProcedimento() != null) {
                String sql = " exists (select 1 from ProfissionalCargaHoraria pch, ProcedimentoCbo pc"
                        + " where pch.tabelaCbo = pc.id.tabelaCbo"
                        + " and pch.profissional.codigo = profissional.codigo"
                        + " and pch.empresa.codigo = " + codigoEmpresa
                        + " and pc.id.procedimentoCompetencia.id.procedimento.codigo = " + this.param.getProcedimento().getCodigo()
                        + " and pc.id.procedimentoCompetencia.id.dataCompetencia = :dataCompetenciaParametro";
                if (param.getCodigoTabelaCbo() != null) {
                    sql += " and pch.tabelaCbo = " + "'" + param.getCodigoTabelaCbo() + "'";
                } else if (param.getCodigoGrupoTabelaCbo() != null) {
                    sql += " and  substring(pch.tabelaCbo.cbo, 0 , 5) = " + "'" + param.getCodigoGrupoTabelaCbo() + "'";
                }
                sql += ")";
                hql.addToWhereWhithAnd(sql);
            }

            if (param.getCodigoTabelaCbo() != null) {
                hql.addToWhereWhithAnd(" exists (select 1 from ProfissionalCargaHoraria pch "
                        + " where pch.tabelaCbo = " + "'" + param.getCodigoTabelaCbo() + "'"
                        + " and pch.profissional.codigo = profissional.codigo"
                        + " and pch.empresa.codigo = " + codigoEmpresa + ")");
            } else if (param.getCodigoGrupoTabelaCbo() != null) {
                hql.addToWhereWhithAnd(" exists (select 1 from ProfissionalCargaHoraria pch "
                        + " where substring(pch.tabelaCbo.cbo, 0 , 5) =  " + "'" + param.getCodigoGrupoTabelaCbo() + "'"
                        + " and pch.profissional.codigo = profissional.codigo"
                        + " and pch.empresa.codigo = " + codigoEmpresa + ")");
            }
            
            if (param.getGrupoAtendimentoCbo() != null) {
                hql.addToWhereWhithAnd(" exists (select 1 from ProfissionalCargaHoraria pch, EloGrupoAtendimentoCbo elo "
                        + " where pch.tabelaCbo = elo.tabelaCbo"
                        + " and elo.grupoAtendimentoCbo.codigo = " + param.getGrupoAtendimentoCbo().getCodigo()
                        + " and pch.profissional.codigo = profissional.codigo"
                        + " and pch.empresa.codigo = " + codigoEmpresa + ")");
            }
        }
    }

    @Override
    protected void setParameters(Query query) {
        if (param.isPeriodoUnidade()) {
            if (param.getTipoAtendimento() != null) {
                query.setParameter("tipoAtendimento", param.getTipoAtendimento());
            }
        }
        if (this.param.getProcedimento() != null) {
            try {
                query.setDate("dataCompetenciaParametro", (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO));
            } catch (ValidacaoException ex) {
                throw new ValidacaoRuntimeException(ex);
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
