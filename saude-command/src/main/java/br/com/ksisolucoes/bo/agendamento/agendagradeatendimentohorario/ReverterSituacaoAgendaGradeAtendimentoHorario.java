/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.agendagradeatendimentohorario;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;

/**
 *
 * <AUTHOR>
 */
public class ReverterSituacaoAgendaGradeAtendimentoHorario extends AbstractCommandTransaction {

    private Long codigo;

    public ReverterSituacaoAgendaGradeAtendimentoHorario(Long codigo) {
        this.codigo = codigo;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (codigo == null) {
            return;
        }
        
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = (AgendaGradeAtendimentoHorario) this.getSession().get(AgendaGradeAtendimentoHorario.class, this.codigo);
        
        if (agendaGradeAtendimentoHorario == null) {
            return;
        }
        
        if (AgendaGradeAtendimentoHorario.STATUS_AGENDADO.equals(agendaGradeAtendimentoHorario.getStatus())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_horario_ja_agendado"));
        }
        
        
        agendaGradeAtendimentoHorario.setStatus(AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        
        BOFactory.getBO(CadastroFacade.class).save(agendaGradeAtendimentoHorario);
        
//        Exame exame = agendaGradeAtendimentoHorario.getExame();
//
//        if (exame != null) {
//            exame.setCompareceu(null);
//            BOFactory.getBO(CadastroFacade.class).save(exame);
//        }
//
    }
    
}
