package br.com.ksisolucoes.bo.consorcio.pedidolicitacaoitem;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QuerySaldoTransferenciaProdutoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoLicitacaoItem;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QuerySaldoTransferenciaProduto extends CommandQuery<QuerySaldoTransferenciaProduto> {

    private Long saldo;
    private final QuerySaldoTransferenciaProdutoDTOParam param;
    private List<PedidoLicitacaoItem> result;

    public QuerySaldoTransferenciaProduto(QuerySaldoTransferenciaProdutoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("pedidoLicitacaoItem.valorTotalLicitado", true);
        hql.addToSelect("pedidoLicitacaoItem.valorRecebido", true);
        hql.addToSelect("pedidoLicitacaoItem.precoReal", true);
        hql.addToSelect("pedidoLicitacaoItem.dataValidade", true);

        hql.setTypeSelect(PedidoLicitacaoItem.class.getName());
        hql.addToFrom("EloLicitacaoPedido eloLicitacaoPedido"
                + " left join eloLicitacaoPedido.licitacaoItem licitacaoItem"
                + " left join licitacaoItem.licitacao licitacao"
                + " left join eloLicitacaoPedido.pedidoLicitacaoItem pedidoLicitacaoItem"
                + " left join pedidoLicitacaoItem.produto produto"
                + " left join pedidoLicitacaoItem.pedidoLicitacao pedidoLicitacao"
                + " left join pedidoLicitacao.empresa empresa");

        hql.addToWhereWhithAnd("produto = ", param.getProduto());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("pedidoLicitacaoItem.status = ", PedidoLicitacaoItem.StatusPedidoLicitacaoItem.LICITADO.value());
        hql.addToWhereWhithAnd("pedidoLicitacaoItem.dataValidade >= ", Data.adjustRangeHour(Data.getDataAtual()).getDataInicial());
        hql.addToWhereWhithAnd("licitacao.codigo = ", param.getCodigoLicitacao());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (this.result.isEmpty()) {
            saldo = 0L;
        } else {
            saldo = 1L;
        }
    }

    public Long getSaldo() {
        return saldo;
    }

}
