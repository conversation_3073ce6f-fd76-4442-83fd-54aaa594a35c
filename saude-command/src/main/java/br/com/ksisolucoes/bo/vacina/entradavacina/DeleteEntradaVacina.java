/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vacina.entradavacina;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.EntradaVacina;
import br.com.ksisolucoes.vo.vacina.ItemEntradaVacina;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeleteEntradaVacina extends DeleteVO<EntradaVacina> {

    public DeleteEntradaVacina(EntradaVacina vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        vo = (EntradaVacina) getSession().get(EntradaVacina.class, vo.getCodigo());
        if(! EntradaVacina.StatusEntradaVacina.ABERTO.getValue().equals(vo.getStatus())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_somente_entrada_aberta_pode_ser_excluida"));
        }
        
        List<ItemEntradaVacina> itens = getSession().createCriteria(ItemEntradaVacina.class)
                .add(Restrictions.eq(ItemEntradaVacina.PROP_ENTRADA_VACINA, vo))
                .list();
        if(CollectionUtils.isNotNullEmpty(itens)){
            for (ItemEntradaVacina itemEntradaVacina : itens) {
                BOFactory.delete(itemEntradaVacina);
            }
        }
    }
    
}
