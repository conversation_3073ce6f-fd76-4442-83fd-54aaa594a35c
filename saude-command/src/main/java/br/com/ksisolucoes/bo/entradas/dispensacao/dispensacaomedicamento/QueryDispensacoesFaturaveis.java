package br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamento;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoContaPacienteDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryDispensacoesFaturaveis extends CommandQuery<QueryDispensacoesFaturaveis> {

    private Long codigoDispensacaoMedicamento;
    private List<DispensacaoMedicamentoContaPacienteDTO> result;

    public QueryDispensacoesFaturaveis(Long codigoDispensacaoMedicamento) {
        this.codigoDispensacaoMedicamento = codigoDispensacaoMedicamento;
    }

    @Override
    protected void createQuery(HQLHelper hql) {        
        hql.addToSelect("dm.codigo", "dispensacaoMedicamento.codigo");
        hql.addToSelect("dm.dataDispensacao", "dispensacaoMedicamento.dataDispensacao");
        hql.addToSelect("dm.version", "dispensacaoMedicamento.version");
        
        hql.addToSelect("profissional.codigo", "dispensacaoMedicamento.profissionalFaturamentoDispensacao.codigo");
        hql.addToSelect("profissional.nome", "dispensacaoMedicamento.profissionalFaturamentoDispensacao.nome");
        hql.addToSelect("profissional.version", "dispensacaoMedicamento.profissionalFaturamentoDispensacao.version");
        
        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.version", "usuarioCadsus.version");
        
        hql.addToSelect("procedimento.codigo", "procedimento.codigo");
        hql.addToSelect("procedimento.descricao", "procedimento.descricao");
        hql.addToSelect("procedimento.version", "procedimento.version");

        hql.addToSelect("endereco.codigo", "enderecoUsuarioCadsus.codigo");
        hql.addToSelect("endereco.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("endereco.complementoLogradouro", "enderecoUsuarioCadsus.complementoLogradouro");
        hql.addToSelect("endereco.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("endereco.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("endereco.version", "enderecoUsuarioCadsus.version");
        
        hql.addToSelect("empresa.codigo", "dispensacaoMedicamento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "dispensacaoMedicamento.empresa.descricao");
        hql.addToSelect("empresa.version", "dispensacaoMedicamento.empresa.version");
        
        hql.addToSelect("empresaPrincipal.codigo", "dispensacaoMedicamento.empresa.empresaPrincipal.codigo");
        hql.addToSelect("empresaPrincipal.descricao", "dispensacaoMedicamento.empresa.empresaPrincipal.descricao");
        hql.addToSelect("empresaPrincipal.version", "dispensacaoMedicamento.empresa.empresaPrincipal.version");
        
        hql.addToSelect("cbo.cbo", "cbo.cbo");
        hql.addToSelect("cbo.version", "cbo.version");

        hql.setTypeSelect(DispensacaoMedicamentoContaPacienteDTO.class.getName());
        hql.addToFrom("DispensacaoMedicamento dm "
                + " join dm.profissionalFaturamentoDispensacao profissional"
                + " join dm.tabelaCboFaturamentoDispensacao cbo"
                + " join dm.procedimentoFaturamentoDispensacao procedimento"
                + " join dm.usuarioCadsusDestino usuarioCadsus"
                + " join usuarioCadsus.enderecoUsuarioCadsus endereco"
                + " join dm.empresa empresa"
                + " left join empresa.empresaPrincipal empresaPrincipal");

        hql.addToWhereWhithAnd("dm.codigo = ", codigoDispensacaoMedicamento);
        hql.addToWhereWhithAnd("coalesce(dm.faturarDispensa, :nao) = :sim");
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("sim", RepositoryComponentDefault.SIM_LONG);
        query.setParameter("nao", RepositoryComponentDefault.NAO_LONG);
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
    
    @Override
    public List<DispensacaoMedicamentoContaPacienteDTO> getResult() {
        return result;
    }
    
}