/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.programasaude;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.programasaude.interfaces.dto.GrupoProgramaSaudeDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.GrupoProgramaSaude;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarGrupoProgramaSaude extends AbstractCommandTransaction{

    private GrupoProgramaSaudeDTO dto;

    public SalvarGrupoProgramaSaude(GrupoProgramaSaudeDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        GrupoProgramaSaude grupoProgramaSaude = dto.getGrupoProgramaSaude();

        if(grupoProgramaSaude.getDescricao() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao"));
        }
        if(grupoProgramaSaude.getEmpresa() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_empresa"));
        }
        if(grupoProgramaSaude.getProgramaSaude() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_programa_saude"));
        }

        BOFactory.getBO(CadastroFacade.class).save(grupoProgramaSaude);

        List<ProgramaSaudeUsuario> list = LoadManager.getInstance(ProgramaSaudeUsuario.class)
                .addParameter(new QueryCustomParameter(ProgramaSaudeUsuario.PROP_GRUPO_PROGRAMA_SAUDE, grupoProgramaSaude))
                .start().getList();
        if(CollectionUtils.isNotNullEmpty(dto.getDtoList())){
            list.removeAll(dto.getDtoList());
            
            for (ProgramaSaudeUsuario psu : dto.getDtoList()) {
                psu.setGrupoProgramaSaude(grupoProgramaSaude);
                psu.setProgramaSaude(grupoProgramaSaude.getProgramaSaude());
                BOFactory.getBO(CadastroFacade.class).save(psu);
            }
        }
        for (ProgramaSaudeUsuario psu : list) {
            psu.setGrupoProgramaSaude(null);
            psu.setProgramaSaude(grupoProgramaSaude.getProgramaSaude());
            BOFactory.getBO(CadastroFacade.class).save(psu);
        }

    }
}
