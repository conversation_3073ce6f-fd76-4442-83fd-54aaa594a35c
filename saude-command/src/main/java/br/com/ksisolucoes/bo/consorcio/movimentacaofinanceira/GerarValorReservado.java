package br.com.ksisolucoes.bo.consorcio.movimentacaofinanceira;

import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.consorcio.SubContaAno;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class GerarValorReservado extends AbstractCommandTransaction {

    private ValorReservadoDTO dto;
    private SubConta subConta;
    private Double valorReservar;

    public GerarValorReservado(ValorReservadoDTO dto) {
        this.dto = dto;
        this.subConta = dto.getSubConta();
        this.valorReservar = dto.getValorReservar();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
//        if( dto.getSubContaImposto() == null) {
        gerarValorReservadoSemImposto();
//        } else {
//            gerarValorReservadoComImposto();
//        }
    }

    private void gerarValorReservadoSemImposto() throws DAOException, ValidacaoException {
        subConta = HibernateUtil.lockTable(SubConta.class, subConta.getCodigo());

        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

        SubContaAno subContaAno = carregarSubContaAno(subConta, dto.getAno());

        Double valorReservadoTotalSubContaAno = new Dinheiro(subContaAno.getValorReservado())
                .somar(valorReservar).doubleValue();

        Double valorReservadoTotalSubConta = new Dinheiro(this.subConta.getValorReservado())
                .somar(valorReservar).doubleValue();

        Double valorReservadoTotalConta = new Dinheiro(conta.getValorReservado())
                .somar(valorReservar).doubleValue();

        if (!Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().<Empresa>getEmpresa().getTipoUnidade())
                && !RepositoryComponentDefault.SIM_LONG.equals(conta.getFlagReservaMaiorSaldo())
                && dto.isValidarValorReservado()) {
            if (dto.controlaSaldoPorAno()) {
                if (valorReservadoTotalSubContaAno > subContaAno.getSaldoAtual()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_insuficiente_para_reserva"));
                }
            } else {
                if (valorReservadoTotalSubConta > this.subConta.getSaldoAtual()) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_insuficiente_para_reserva"));
                }
            }
        }

        subContaAno.setValorReservado(valorReservadoTotalSubContaAno);
        this.subConta.setValorReservado(valorReservadoTotalSubConta);
        conta.setValorReservado(valorReservadoTotalConta);

        BOFactory.save(subContaAno);
        BOFactory.save(subConta);
        BOFactory.save(conta);
    }

    private SubContaAno carregarSubContaAno(SubConta subConta, Long ano) throws DAOException, ValidacaoException {
        SubContaAno subContaAno = HibernateUtil.lockTable(SubContaAno.class,
                Restrictions.eq(VOUtils.montarPath(SubContaAno.PROP_SUB_CONTA, SubConta.PROP_CODIGO), subConta.getCodigo()),
                Restrictions.eq(SubContaAno.PROP_ANO, ano));

        if (subContaAno == null) {
            subContaAno = new SubContaAno();
            subContaAno.setSubConta(subConta);
            subContaAno.setAno(ano);
            subContaAno = BOFactory.save(subContaAno);
            subContaAno = HibernateUtil.lockTable(SubContaAno.class, subContaAno.getCodigo());
        }

        return subContaAno;
    }

//    private void gerarValorReservadoComImposto() throws DAOException, ValidacaoException {
//        subConta = HibernateUtil.lockTable(SubConta.class, subConta.getCodigo());
//        Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());
//
//        SubConta subContaImposto;
//        subContaImposto = HibernateUtil.lockTable(SubConta.class, dto.getSubContaImposto().getCodigo());
//
//        Double valorReservadoTotalSubConta = new Dinheiro(this.subConta.getValorReservado())
//                .somar(valorReservar)
//                .somar(Coalesce.asDouble(dto.getValorReservarImposto())).doubleValue();
//
//        Double valorReservadoTotalSubContaImposto = new Dinheiro(subContaImposto.getValorReservado())
//                .somar(Coalesce.asDouble(dto.getValorReservarImposto())).doubleValue();
//
//        Double valorReservadoTotalConta = new Dinheiro(conta.getValorReservado())
//                .somar(valorReservar)
//                .somar(Coalesce.asDouble(dto.getValorReservarImposto())).doubleValue();
//
//        if(! Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(getSessao().<Empresa>getEmpresa().getTipoUnidade())
//                && ! RepositoryComponentDefault.SIM_LONG.equals(conta.getFlagReservaMaiorSaldo())){
//            if (valorReservadoTotalSubConta > this.subConta.getSaldoAtual()) {
//                throw new ValidacaoException(Bundle.getStringApplication("msg_saldo_insuficiente_para_reserva"));
//            }
//        }
//
//        this.subConta.setValorReservado(valorReservadoTotalSubConta);
//        subContaImposto.setValorReservado(valorReservadoTotalSubContaImposto);
//
//        conta.setValorReservado(valorReservadoTotalConta);
//
//        BOFactory.save(subConta);
//        BOFactory.save(subContaImposto);
//        BOFactory.save(conta);
//    }

}
