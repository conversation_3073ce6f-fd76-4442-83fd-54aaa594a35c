package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.*;

public class FiltroExamesUnidadeOrigemESolicitacao {

    private FiltroExamesUnidadeOrigemESolicitacao() {
    }

    public static List<SolicitacaoAgendamentoExame> filtrar(Empresa empresa,
                                                            List<ExamePrestadorProcedimento> examesPrestadorProcedimentos,
                                                            List<SolicitacaoAgendamentoExame> examesSolicitacaoAgendamento) {
        List<SolicitacaoAgendamentoExame> examesFiltrados = new ArrayList<>();
        List<ExamePrestadorProcedimento> examesPrestadorRealiza = filter(having(on(ExamePrestadorProcedimento.class).getExamePrestador().getPrestador().getCodigo(), Matchers.equalTo(empresa.getCodigo())), examesPrestadorProcedimentos);
        List<Long> examesPrestadorIds = extract(examesPrestadorRealiza, on(ExamePrestadorProcedimento.class).getExameProcedimento().getCodigo());
        for (SolicitacaoAgendamentoExame exame : examesSolicitacaoAgendamento) {
            if (examesPrestadorIds.contains(exame.getExameProcedimento().getCodigo())) {
                examesFiltrados.add(exame);
            }
        }
        return examesFiltrados;
    }
}
