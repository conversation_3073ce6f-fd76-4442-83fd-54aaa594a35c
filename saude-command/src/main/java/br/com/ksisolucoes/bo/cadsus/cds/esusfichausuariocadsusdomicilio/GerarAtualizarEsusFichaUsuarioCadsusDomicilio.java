package br.com.ksisolucoes.bo.cadsus.cds.esusfichausuariocadsusdomicilio;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaEnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaUsuarioCadsusDomicilio;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.util.esus.EsusValidacoesFichaCadastroDomiciliarHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 14/08/17.
 */
public class GerarAtualizarEsusFichaUsuarioCadsusDomicilio extends AbstractCommandTransaction {

    private UsuarioCadsusDomicilio usuarioCadsusDomicilio;
    private EsusFichaUsuarioCadsusDomicilio esusFichaUsuarioCadsusDomicilio;

    public GerarAtualizarEsusFichaUsuarioCadsusDomicilio(UsuarioCadsusDomicilio usuarioCadsusDomicilio) {
        this.usuarioCadsusDomicilio = usuarioCadsusDomicilio;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        EnderecoDomicilioEsus enderecoDomicilioEsus = (EnderecoDomicilioEsus) getSession().createCriteria(EnderecoDomicilioEsus.class)
                .add(Restrictions.eq(EnderecoDomicilioEsus.PROP_ENDERECO_DOMICILIO, usuarioCadsusDomicilio.getEnderecoDomicilio()))
                .uniqueResult();

        if (enderecoDomicilioEsus != null) {
            EsusIntegracaoCds proxy = on(EsusIntegracaoCds.class);

            EsusIntegracaoCds esusIntegracaoCds = LoadManager.getInstance(EsusIntegracaoCds.class)
                    .addProperty(path(proxy.getCodigo()))
                    .addProperty(path(proxy.getEsusFichaEnderecoDomicilioEsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus()), enderecoDomicilioEsus))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1)
                    .start().getVO();

            if (esusIntegracaoCds != null && esusIntegracaoCds.getEsusFichaEnderecoDomicilioEsus() != null && esusIntegracaoCds.getEsusFichaEnderecoDomicilioEsus().getCodigo() != null) {
                EsusFichaEnderecoDomicilioEsus esusFichaEnderecoDomicilioEsus = (EsusFichaEnderecoDomicilioEsus) getSession().get(EsusFichaEnderecoDomicilioEsus.class, esusIntegracaoCds.getEsusFichaEnderecoDomicilioEsus().getCodigo());

                atualizarEsusFichaUsuarioCadsusDomicilio(enderecoDomicilioEsus, esusFichaEnderecoDomicilioEsus);
            }
        }
    }

    private void atualizarEsusFichaUsuarioCadsusDomicilio(EnderecoDomicilioEsus enderecoDomicilioEsus, EsusFichaEnderecoDomicilioEsus esusFichaEnderecoDomicilioEsus) throws ValidacaoException, DAOException {
        UsuarioCadsusDomicilio proxy = on(UsuarioCadsusDomicilio.class);

        EsusFichaUsuarioCadsusDomicilio esusFichaUsuarioCadsusDomicilio;
        if (usuarioCadsusDomicilio != null) {
            esusFichaUsuarioCadsusDomicilio = (EsusFichaUsuarioCadsusDomicilio) getSession().createCriteria(EsusFichaUsuarioCadsusDomicilio.class)
                    .add(Restrictions.eq(EsusFichaUsuarioCadsusDomicilio.PROP_ESUS_FICHA_ENDERECO_DOMICILIO_ESUS, esusFichaEnderecoDomicilioEsus))
                    .add(Restrictions.eq(EsusFichaUsuarioCadsusDomicilio.PROP_USUARIO_CADSUS_DOMICILIO, usuarioCadsusDomicilio))
                    .setLockMode(LockMode.PESSIMISTIC_WRITE)
                    .uniqueResult();
            if (usuarioCadsusDomicilio.getUsuarioCadsus() == null) {
                UsuarioCadsus usuarioCadsus = (UsuarioCadsus) this.getSession().createCriteria(UsuarioCadsus.class)
                        .add(Restrictions.eq(UsuarioCadsus.PROP_CODIGO, usuarioCadsusDomicilio.getUsuarioCadsus().getCodigo()))
                        .uniqueResult();
                usuarioCadsusDomicilio.setUsuarioCadsus(usuarioCadsus);
            }

            boolean responsavel = RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusDomicilio.getUsuarioCadsus().getFlagResponsavelFamiliar());
            boolean responsavelMudouse = RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsusDomicilio.getUsuarioCadsus().getFlagResponsavelFamiliar())
                    && RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusDomicilio.getUsuarioCadsus().getResponsavelAnterior())
                    && UsuarioCadsusDomicilio.Motivo.MUDOU_SE.value().equals(usuarioCadsusDomicilio.getMotivoExclusao());
            if (responsavel || responsavelMudouse) {
                if (esusFichaUsuarioCadsusDomicilio == null) {
                    esusFichaUsuarioCadsusDomicilio = new EsusFichaUsuarioCadsusDomicilio();
                }
                esusFichaUsuarioCadsusDomicilio.setEsusFichaEnderecoDomicilioEsus(esusFichaEnderecoDomicilioEsus);
                esusFichaUsuarioCadsusDomicilio.setUsuarioCadsusDomicilio(usuarioCadsusDomicilio);
                esusFichaUsuarioCadsusDomicilio.setMotivoExclusao(usuarioCadsusDomicilio.getMotivoExclusao());
                esusFichaUsuarioCadsusDomicilio.setProntuario(usuarioCadsusDomicilio.getProntuario());
                esusFichaUsuarioCadsusDomicilio.setResideDesde(usuarioCadsusDomicilio.getUsuarioCadsus().getResideDesde());
                esusFichaUsuarioCadsusDomicilio.setRendaFamiliar(usuarioCadsusDomicilio.getUsuarioCadsus().getRendaFamiliar());
                esusFichaUsuarioCadsusDomicilio.setStatus(usuarioCadsusDomicilio.getStatus());

                esusFichaUsuarioCadsusDomicilio.setResponsavel(RepositoryComponentDefault.SIM);

                esusFichaUsuarioCadsusDomicilio.setDataNascimento(usuarioCadsusDomicilio.getUsuarioCadsus().getDataNascimento());
                esusFichaUsuarioCadsusDomicilio.setNome(usuarioCadsusDomicilio.getUsuarioCadsus().getNome());

                esusFichaUsuarioCadsusDomicilio.setNumeroCartao(null);
                if (!"".equals(Coalesce.asString(usuarioCadsusDomicilio.getUsuarioCadsus().getCns()))) {
                    esusFichaUsuarioCadsusDomicilio.setNumeroCartao(new Long(usuarioCadsusDomicilio.getUsuarioCadsus().getCnsSemPontos()));
                }


                EsusValidacoesFichasDTOParam paramValidacao = new EsusValidacoesFichasDTOParam();
                EsusFichaEnderecoDomicilioEsus efede = esusFichaEnderecoDomicilioEsus;
                Profissional profissional;
                Empresa empresa;

                paramValidacao.setRetorno(EsusValidacoesFichasDTOParam.Retorno.EXCEPTION);
                List<CboFichaEsusItem> cboFichaEsusItemList = LoadManager.getInstance(CboFichaEsusItem.class)
                        .addProperty(CboFichaEsusItem.PROP_CODIGO)
                        .addProperty(VOUtils.montarPath(CboFichaEsusItem.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CboFichaEsusItem.PROP_CBO_FICHA_ESUS, CboFichaEsus.PROP_FICHA), CboFichaEsus.TipoFicha.FICHA_CADASTRO_DOMICILIAR.value()))
                        .start().getList();

                paramValidacao.setCboFichaEsusItemList(cboFichaEsusItemList);
                paramValidacao.setDomicilioVazio(false);

                profissional = efede.getProfissional();
                empresa = efede.getEmpresa();

                paramValidacao.setEmpresa(empresa);
                paramValidacao.setProfissional(profissional);
                paramValidacao.setEsusFichaEnderecoDomicilioEsus(efede);
                paramValidacao.setValidarComponenteDomicilio(true);
                paramValidacao.setResponsavelFamiliar(esusFichaUsuarioCadsusDomicilio);
                paramValidacao.setTabelaCbo(efede.getTabelaCbo());
                paramValidacao.setNaoValidarCNS(usuarioCadsusDomicilio.isNaoValidarCNS());

                String inconsistencia = EsusValidacoesFichaCadastroDomiciliarHelper.validateFamilyAccountable(paramValidacao);

                if (!inconsistencia.isEmpty()) {
                    throw new ValidacaoException(inconsistencia);
                }

                BOFactory.save(esusFichaUsuarioCadsusDomicilio);
            } else if (esusFichaUsuarioCadsusDomicilio != null) {
                BOFactory.delete(esusFichaUsuarioCadsusDomicilio);
            }
        }
    }

    public EsusFichaUsuarioCadsusDomicilio getEsusFichaUsuarioCadsusDomicilio() {
        return esusFichaUsuarioCadsusDomicilio;
    }
}
