package br.com.ksisolucoes.bo.integracao.pni.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.vacina.pni.interfaces.dto.QueryPniItensBoletimMovimentacaoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryPniItensBoletimMovimentacao extends CommandQuery {

    private Empresa empresa;
    private Date competencia;
    private List<QueryPniItensBoletimMovimentacaoDTO> result;

    public QueryPniItensBoletimMovimentacao() {
    }

    public QueryPniItensBoletimMovimentacao(Empresa empresa, Date competencia) {
        this.empresa = empresa;
        this.competencia = competencia;
    }

    public Date getCompetencia() {
        return competencia;
    }

    public void setCompetencia(Date competencia) {
        this.competencia = competencia;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryPniItensBoletimMovimentacaoDTO.class.getName());

        hql.addToSelect("((SELECT coalesce(t1.codigoPni, 0) FROM ProdutoVacina t1 LEFT JOIN t1.produto t2 WHERE t2.codigo = p.codigo))", "codigoProdutoPni");
        hql.addToGroup("1");
        hql.addToSelect("((SELECT coalesce(t1.quantidadeDose, 1) FROM ProdutoVacina t1 LEFT JOIN t1.produto t2 WHERE t2.codigo = p.codigo))", "codigoApresentacao");
        hql.addToGroup("2");
        hql.addToSelect("coalesce((SELECT t1.estoqueFisico "
                + "       FROM MovimentoEstoque t1 WHERE t1.id.empresa = me.id.empresa AND t1.produto = p "
                + "               AND t1.id.numeroLancamento = (SELECT max(t2.id.numeroLancamento) FROM MovimentoEstoque t2"
                + "                                          WHERE t2.id.empresa = me.id.empresa AND t2.produto = p AND t2.dataLancamento < :dataInicial)), 0)", "saldoDisponivelAnterior");
        hql.addToGroup("3");

        hql.addToSelect("coalesce((SELECT t1.estoqueFisico "
                + "       FROM MovimentoEstoque t1 WHERE t1.id.empresa = me.id.empresa AND t1.produto = p "
                + "               AND t1.id.numeroLancamento = (SELECT max(t2.id.numeroLancamento) FROM MovimentoEstoque t2"
                + "                                          WHERE t2.id.empresa = me.id.empresa AND t2.produto = p AND t2.dataLancamento between :dataInicial and :dataFinal)), 0)", "saldoDisponivel");
        hql.addToGroup("4");
        
        hql.addToSelectAndGroup("me.id.empresa.codigo", "empresa.codigo");
        hql.addToSelectAndGroup("me.id.empresa.cnes", "empresa.cnes");
        hql.addToSelectAndGroup("f.codigoProdutor", "codigoProdutor");
        hql.addToSelectAndGroup("td.tipoMovimentacaoVacina", "tipoMovimentacaoVacina");
        hql.addToSelect("sum(me.quantidade)", "quantidade");

        hql.addToFrom(MovimentoEstoque.class.getName(), "me "
                + " left join me.tipoDocumento td"
                + " left join me.produto p"
                + " left join p.fabricante f");

        hql.addToWhereWhithAnd("me.id.empresa = ", empresa);
        hql.addToWhereWhithAnd("td.tipoMovimentacaoVacina is not null");
        hql.addToWhereWhithAnd("me.id.dataMovimentacao between :dataInicial and :dataFinal");
        hql.addToWhereWhithAnd("exists(SELECT 1 FROM ProdutoVacina t1 LEFT JOIN t1.produto t2 WHERE t2.codigo = p.codigo)");
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        query.setDate("dataInicial", competencia);
        query.setDate("dataFinal", Data.adjustRangeHour(Data.getUltimoDiaUltimoMes(competencia)).getDataFinal());
    }


    @Override
    public List<QueryPniItensBoletimMovimentacaoDTO> getResult() {
        return this.result;
    }
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }
    
}