package br.com.ksisolucoes.bo.cadsus.usuariocadsus.dominio;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.usuariocadsus.QueryConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.cadsus.usuariocadsus.enums.UsuarioCadsusAliasSort;
import br.com.ksisolucoes.bo.command.CommandQueryConsulta;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.dao.HQLHelper.getStringQuery;

public class QueryConsultaDominioUsuarioCadsusQueryPager extends CommandQueryConsulta {

    private final QueryConsultaUsuarioCadsusDTOParam param;

    public QueryConsultaDominioUsuarioCadsusQueryPager(QueryConsultaUsuarioCadsusDTOParam param) {
        this.param = param;
    }



    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ConsultaUsuarioCadsusDTO.class.getName());

        hql.addToSelect("u.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("u.referencia", "usuarioCadsus.referencia");
        hql.addToSelect("u.nome", "usuarioCadsus.nome");
        hql.addToSelect("u.apelido", "usuarioCadsus.apelido");
        hql.addToSelect("u.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("u.rg", "usuarioCadsus.rg");
//        hql.addToSelect("uc.numeroCartao", "numeroCartao");
        hql.addToSelect("u.cpf", "usuarioCadsus.cpf");
        hql.addToSelect("u.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("u.situacao", "usuarioCadsus.situacao");
        hql.addToSelect("u.flagUnificado", "usuarioCadsus.flagUnificado");
        hql.addToSelect("u.flagSimplificado", "usuarioCadsus.flagSimplificado");
        hql.addToSelect("u.appCidadaoAtivo", "usuarioCadsus.appCidadaoAtivo");
        hql.addToSelect("u.utilizaNomeSocial", "usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("u.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("u.version", "usuarioCadsus.version");

//        hql.addToFrom("UsuarioCadsusCns uc left join uc.usuarioCadsus u");
        hql.addToFrom("DominioUsuarioCadsus dom join dom.usuarioCadsus u");
        hql.addToWhereWhithAnd("dom.usuarioCadsus = u.codigo");
//        hql.addToWhereWhithAnd("(uc.dataAtribuicao IS NULL OR uc.dataAtribuicao = (SELECT MAX(ucc2.dataAtribuicao) FROM UsuarioCadsusCns ucc2 WHERE ucc2.usuarioCadsus = u.codigo))");

//
//        hql.addToFrom("UsuarioCadsusCns uc"
//                + " right join uc.usuarioCadsus u");
//        hql.addToFrom("DominioUsuarioCadsus dom");
//        hql.addToWhereWhithAnd("dom.usuarioCadsus = u.codigo");
////        hql.addToWhereWhithAnd("uc.dataAtribuicao = (SELECT MAX(ucc2.dataAtribuicao) FROM UsuarioCadsusCns ucc2 WHERE ucc2.usuarioCadsus = u.codigo)");
//        hql.addToWhereWhithAnd("uc.dataAtribuicao = (SELECT COALESCE(MAX(ucc2.dataAtribuicao), uc.dataAtribuicao) FROM UsuarioCadsusCns ucc2 WHERE ucc2.usuarioCadsus = u.codigo)");
////        hql.addToWhereWhithAnd("uc.dataAtribuicao = (SELECT MAX(ucc2.dataAtribuicao) FROM UsuarioCadsusCns ucc2 WHERE ucc2.usuarioCadsus = uc.usuarioCadsus)");


        if (!param.isExibirExcluidos()) { hql.addToWhereWhithAnd("u.situacao <> ", UsuarioCadsus.SITUACAO_EXCLUIDO); }
        if (!param.isExibirInativos()) { hql.addToWhereWhithAnd("u.situacao <> ", UsuarioCadsus.SITUACAO_INATIVO); }
        if (param.getDataNascimento() != null) { hql.addToWhereWhithAnd("u.dataNascimento = ", param.getDataNascimento()); }
        if (param.getCodigo() != null) { hql.addToWhereWhithAnd("u.codigo = ", param.getCodigo()); }
        if (param.getReferencia() != null) { hql.addToWhereWhithAnd("u.referencia = ", param.getReferencia()); }
        if (param.getCpf() != null) { hql.addToWhereWhithAnd("u.cpf = ", param.getCpf().replaceAll("[^0-9]", "")); }
        if (param.getRg() != null) { hql.addToWhereWhithAnd("u.rg = ", param.getRg()); }
        if (param.getSituacao() != null) { hql.addToWhereWhithAnd("u.situacao = ", param.getSituacao()); }
        if (param.getMunicipioResidencia() != null && filtrarMunicipioResidencia()) { hql.addToWhereWhithAnd("u.municipioResidencia = ", param.getMunicipioResidencia().getCodigo()); }
        if (param.getNome() != null) {
            hql.addToWhereWhithAnd("(textual_search_no_accents(dom.nome, :nomeUsuarioCadsus) = 'true' " +
                    " or textual_search_no_accents(dom.nomeReferencia, :nomeUsuarioCadsus) = 'true')");
        }
        if (param.getNomeMae() != null) { hql.addToWhereWhithAnd("textual_search_no_accents(dom.nomeMae, :nomeMae) = 'true'"); }
        if (param.getNumeroCartao() != null) {
            Long cns = br.com.celk.util.Coalesce.asLong(param.getNumeroCartao().replaceAll("[^0-9]", ""));
            if (cns != 0L) { hql.addToWhereWhithAnd("dom.cns = ", cns); }
        }

        if (param.getCampoOrdenacao() != null) {
            String orderType = Coalesce.asString(param.getTipoOrdenacao(), "asc");
            String orderField = param.getCampoOrdenacao();
            applySortField(hql, orderType, orderField);
        }
    }

    private void applySortField(HQLHelper hql, String orderType, String orderField) {
        hql.addToOrder((UsuarioCadsusAliasSort.isPredefined(orderField) ? UsuarioCadsusAliasSort.getSortField(orderField) : "u." + orderField) + " "
            + orderType);
    }

    private boolean filtrarMunicipioResidencia() {
        String filtrarMunicipioResidencia = null;
        try {
            filtrarMunicipioResidencia = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("filtraPorMunicipioResidencia");
        } catch (DAOException ex) {
            Logger.getLogger(QueryConsultaUsuarioCadsus.class.getName()).log(Level.SEVERE, null, ex);
        }
        return RepositoryComponentDefault.SIM.equals(filtrarMunicipioResidencia);
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
        if(param.getNome() != null){
           query.setParameter("nomeUsuarioCadsus", getStringQuery(param.getNome()));
        }
        if(param.getNomeMae() != null){
           query.setParameter("nomeMae", getStringQuery(param.getNomeMae()));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected String getAlias() {
        return "uc";
    }

    @Override
    public boolean addProperties() {
        return false;
    }
}
