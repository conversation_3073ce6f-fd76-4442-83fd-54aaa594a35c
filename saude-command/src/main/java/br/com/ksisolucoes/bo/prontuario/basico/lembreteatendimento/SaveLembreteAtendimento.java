package br.com.ksisolucoes.bo.prontuario.basico.lembreteatendimento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.LembreteAtendimento;

/**
 * Created by sulivan on 25/01/18.
 */
public class SaveLembreteAtendimento extends SaveVO<LembreteAtendimento> {

    public SaveLembreteAtendimento(LembreteAtendimento vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getFlagAtivo() == null) {
            this.vo.setFlagAtivo(RepositoryComponentDefault.SIM_LONG);
        }
    }
}
