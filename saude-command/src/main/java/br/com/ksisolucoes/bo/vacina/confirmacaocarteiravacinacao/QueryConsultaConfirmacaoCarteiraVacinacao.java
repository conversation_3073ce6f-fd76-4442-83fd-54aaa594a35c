package br.com.ksisolucoes.bo.vacina.confirmacaocarteiravacinacao;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.ConsultaConfirmacaoCarteiraVacinacaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.RegistroVacina;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaConfirmacaoCarteiraVacinacao extends CommandQueryPager<QueryConsultaConfirmacaoCarteiraVacinacao> {
    
    private ConsultaConfirmacaoCarteiraVacinacaoDTOParam param;

    public QueryConsultaConfirmacaoCarteiraVacinacao(ConsultaConfirmacaoCarteiraVacinacaoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("uc.codigo","codigo");
        hql.addToSelect("uc.nome","nome");
        hql.addToSelect("uc.apelido","apelido");
        hql.addToSelect("uc.utilizaNomeSocial","utilizaNomeSocial");
        hql.addToSelect("uc.dataNascimento","dataNascimento");
        hql.addToSelect("uc.nomeMae","nomeMae");
        
        hql.setTypeSelect(UsuarioCadsus.class.getName());
        hql.addToFrom("UsuarioCadsus uc ");

        if (param.getNomePaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" uc.nome", param.getNomePaciente(), true)
                    + " OR (uc.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("uc.apelido", param.getNomePaciente(), true) + "))");
        }
        hql.addToWhereWhithAnd("uc.codigo in(select t2.codigo from RegistroVacina t1 left join t1.usuarioCadsus t2 where t1.situacao = :situacaoRegistroVacina group by t2.codigo)");
        
        if (this.param.getCampoOrdenacao() != null) {
            String orderType = Coalesce.asString(this.param.getTipoOrdenacao(), "asc");
            String orderField = this.param.getCampoOrdenacao();

            hql.addToOrder("uc." + orderField + " " + orderType);
        } else {
            hql.addToOrder("uc.nome " + Coalesce.asString(this.param.getTipoOrdenacao(), "asc"));
        }
        
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
        query.setParameter("situacaoRegistroVacina", RegistroVacina.Situacao.PENDENTE.value());
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String,Object>>)result);
    }
}