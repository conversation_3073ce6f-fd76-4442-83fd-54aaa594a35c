package br.com.ksisolucoes.bo.agendamento.exame.exameimportacaocovid;

import br.com.celk.importacaoexamecovid.dto.ImportacaoExameCovidDTO;
import br.com.celk.importacaoexamecovid.dto.UsurioCadsusImportacaoExameCovidDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid;

public class SalvarImportacaoExameCovid extends AbstractCommandTransaction<SalvarImportacaoExameCovid> {

    private ImportacaoExameCovidDTO importacaoExameCovidDTO;
    private UsurioCadsusImportacaoExameCovidDTO usuarioImportacao;
    private ExameExternoCovid exameExternoCovid;

    public SalvarImportacaoExameCovid(ImportacaoExameCovidDTO importacaoExameCovidDTO, UsurioCadsusImportacaoExameCovidDTO usuarioImportacao) {
        this.importacaoExameCovidDTO = importacaoExameCovidDTO;
        this.usuarioImportacao = usuarioImportacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        exameExternoCovid = new BuildExameExternoCovid()
                                                .setImportacaoExameDTO(importacaoExameCovidDTO)
                                                .setUsuario(getSessao().getUsuario())
                                                .setEmpresa(getSessao().getEmpresa())
                                                .setUsuarioImportacao(usuarioImportacao)
                                                .build();
        BOFactory.save(exameExternoCovid);
    }

    public ExameExternoCovid getExameExternoCovid() {
        return exameExternoCovid;
    }
}
