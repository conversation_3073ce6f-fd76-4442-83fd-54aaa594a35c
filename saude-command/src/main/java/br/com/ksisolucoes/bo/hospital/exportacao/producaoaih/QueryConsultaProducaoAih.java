package br.com.ksisolucoes.bo.hospital.exportacao.producaoaih;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoaih.dto.ProducaoAihDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresLaqueadura;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresParto;
import br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementaresUtiNeonatal;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.hibernate.Query;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProducaoAih extends CommandQuery<QueryConsultaProducaoAih> {

    private List<ProducaoAihDTO> queryResult;
    private List<List<ProducaoAihDTO>> result;
    private Long mes;
    private Long ano;
    private DatePeriod periodo;

    public QueryConsultaProducaoAih(Long mes, Long ano) {
        this.mes = mes;
        this.ano = ano;
        mes--;
        int diaInicioCompetencia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia().intValue();
        DatePeriod periodo = Data.periodoDataCompetencia(diaInicioCompetencia, mes.intValue(), ano.intValue());
        this.periodo = periodo;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ProducaoAihDTO.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToSelect("icp.codigo", "itemContaPaciente.codigo");
        hql.addToSelect("icp.atribuirValorPara", "itemContaPaciente.atribuirValorPara");
        hql.addToSelect("icp.dataLancamento", "itemContaPaciente.dataLancamento");
        hql.addToSelect("icp.indicadorEquipeAih", "itemContaPaciente.indicadorEquipeAih");
        hql.addToSelect("coalesce(icp.quantidade, 1)", "itemContaPaciente.quantidade");

        hql.addToSelect("profissionalItemConta.codigo", "profissionalItemConta.codigo");
        hql.addToSelect("profissionalItemConta.cpf", "profissionalItemConta.cpf");
        hql.addToSelect("profissionalItemConta.codigoCns", "profissionalItemConta.codigoCns");

        hql.addToSelect("empresaPrestador.codigo", "empresaPrestador.codigo");
        hql.addToSelect("empresaPrestador.cnpj", "empresaPrestador.cnpj");
        hql.addToSelect("empresaPrestador.cnes", "empresaPrestador.cnes");

        hql.addToSelect("itemProcedimento.codigo", "itemProcedimento.codigo");
        hql.addToSelect("itemProcedimento.referencia", "itemProcedimento.referencia");

        hql.addToSelect("leitoQuarto.numeroLeitoAih", "leitoQuarto.numeroLeitoAih");
        hql.addToSelect("leitoQuarto.descricao", "leitoQuarto.descricao");

        hql.addToSelect("especialidadeLeito.codigo", "leitoQuarto.especialidadeLeito.codigo");

        hql.addToSelect("quartoInternacao.numeroEnfermariaAih", "quartoInternacao.numeroEnfermariaAih");
        hql.addToSelect("quartoInternacao.descricao", "quartoInternacao.descricao");

        hql.addToSelect("cpp.dataFechamento", "contaPaciente.dataFechamento");
        hql.addToSelect("cpp.codigo", "contaPaciente.codigo");
        hql.addToSelect("cpp.liberacaoCriticaJustificativaCns", "contaPaciente.liberacaoCriticaJustificativaCns");
        hql.addToSelect("cpp.liberacaoCriticaCns", "contaPaciente.liberacaoCriticaCns");
        hql.addToSelect("cpp.liberacaoCriticaIdadeMenor", "contaPaciente.liberacaoCriticaIdadeMenor");
        hql.addToSelect("cpp.liberacaoCriticaIdadeMaior", "contaPaciente.liberacaoCriticaIdadeMaior");
        hql.addToSelect("cpp.liberacaoCriticaQuantidadeMaxima", "contaPaciente.liberacaoCriticaQuantidadeMaxima");
        hql.addToSelect("cpp.liberacaoCriticaTelefone", "contaPaciente.liberacaoCriticaTelefone");
        hql.addToSelect("cpp.liberacaoCriticaTempoPermanencia", "contaPaciente.liberacaoCriticaTempoPermanencia");

        hql.addToSelect("aih", new HQLProperties(Aih.class, "aih").getProperties());
        hql.addToSelect("aih.codigo", "aih.codigo");

        hql.addToSelect("profissionalAih.codigo", "profissionalAih.codigo");
        hql.addToSelect("profissionalAih.cpf", "profissionalAih.cpf");
        hql.addToSelect("profissionalAih.codigoCns", "profissionalAih.codigoCns");

        hql.addToSelect("profissionalResponsavel.codigo", "profissionalResponsavel.codigo");
        hql.addToSelect("profissionalResponsavel.nome", "profissionalResponsavel.nome");
        hql.addToSelect("profissionalResponsavel.cpf", "profissionalResponsavel.cpf");
        hql.addToSelect("profissionalResponsavel.codigoCns", "profissionalResponsavel.codigoCns");

        hql.addToSelect("loteAih.codigo", "loteAih.codigo");
        hql.addToSelect("loteAih.quantidadeAih", "loteAih.quantidadeAih");
        hql.addToSelect("loteAih.dataApresentacao", "loteAih.dataApresentacao");

        hql.addToSelect("atendimento.codigo", "atendimento.codigo");

        hql.addToSelect("atendimentoPrincipal.codigo", "atendimentoPrincipal.codigo");

        hql.addToSelect("convenio.codigo", "convenio.codigo");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");

        hql.addToSelect("empresaPrincipal.codigo", "empresaPrincipal.codigo");
        hql.addToSelect("empresaPrincipal.descricao", "empresaPrincipal.descricao");
        hql.addToSelect("empresaPrincipal.cnes", "empresaPrincipal.cnes");
        hql.addToSelect("empresaPrincipal.orgaoEmissor", "empresaPrincipal.orgaoEmissor");

        hql.addToSelect("profissionalDiretor.codigo", "profissionalDiretor.codigo");
        hql.addToSelect("profissionalDiretor.nome", "profissionalDiretor.nome");
        hql.addToSelect("profissionalDiretor.cpf", "profissionalDiretor.cpf");
        hql.addToSelect("profissionalDiretor.codigoCns", "profissionalDiretor.codigoCns");

//        hql.addToSelect("empresaMantenedora.codigo", "empresaMantenedora.codigo");
//        hql.addToSelect("empresaMantenedora.nomeMantenedora", "empresaMantenedora.nomeMantenedora");
//        hql.addToSelect("empresaMantenedora.cnes", "empresaMantenedora.cnes");
//        hql.addToSelect("empresaMantenedora.orgaoEmissor", "empresaMantenedora.orgaoEmissor");
        hql.addToSelect("cidade.codigo", "cidade.codigo");
        hql.addToSelect("cidade.descricao", "cidade.descricao");

        hql.addToSelect("ai.dataChegada", "atendimentoInformacao.dataChegada");
        hql.addToSelect("aa.motivoAlta", "atendimentoAlta.motivoAlta");
        hql.addToSelect("aa.dataAlta", "atendimentoAlta.dataAlta");

        hql.addToSelect("procedimentoSolicitado.codigo", "procedimentoSolicitado.codigo");
        hql.addToSelect("procedimentoSolicitado.descricao", "procedimentoSolicitado.descricao");
        hql.addToSelect("procedimentoRealizado.codigo", "procedimentoRealizado.codigo");
        hql.addToSelect("procedimentoRealizado.descricao", "procedimentoRealizado.descricao");

//        hql.addToSelect("diretorClinico.cpf", "diretorClinico.cpf");
//        hql.addToSelect("diretorClinico.codigoCns", "diretorClinico.codigoCns");
        hql.addToSelect("cidPrincipal.codigo", "cidPrincipal.codigo");
        hql.addToSelect("cidSecundario.codigo", "cidSecundario.codigo");
        hql.addToSelect("cidCausaAssociada.codigo", "cidCausaAssociada.codigo");

        hql.addToSelect("cbo.cbo", "tabelaCbo.cbo");
        
        hql.addToSelect("ucs.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("ucs.nome", "usuarioCadsus.nome");
        hql.addToSelect("ucs.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("ucs.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("ucs.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("ucs.responsavel", "usuarioCadsus.responsavel");
        hql.addToSelect("ucs.telefone", "usuarioCadsus.telefone");
        hql.addToSelect("ucs.cpf", "usuarioCadsus.cpf");
        hql.addToSelect("ucs.rg", "usuarioCadsus.rg");
        hql.addToSelect("etniaIndigena.codigoSus", "usuarioCadsus.etniaIndigena.codigoSus");
        hql.addToSelect("pn.codigo", "usuarioCadsus.paisNascimento.codigo");

        hql.addToSelect("raca.codigo", "raca.codigo");

        hql.addToSelect("eucs.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("eucs.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("eucs.complementoLogradouro", "enderecoUsuarioCadsus.complementoLogradouro");
        hql.addToSelect("eucs.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("eucs.cep", "enderecoUsuarioCadsus.cep");

        hql.addToSelect("tipoLogradouroCadsus.codigo", "tipoLogradouroCadsus.codigo");

        hql.addToSelect("cidadePaciente.codigo", "cidadePaciente.codigo");

        hql.addToSelect("estado.codigo", "estado.codigo");
        hql.addToSelect("estado.sigla", "estado.sigla");

        hql.addToSelect("responsavel.codigo", "responsavel.codigo");
        hql.addToSelect("responsavel.nome", "responsavel.nome");
        hql.addToSelect("case when exists(Select 1 from SolicitacaoMudancaProcedimento where autorizacaoInternacaoHospitalar = aih) then true else false end", "existsSoliMudancaProcedimento");
        hql.addToSelect("(select max(numeroCartao) from UsuarioCadsusCns cns where cns.usuarioCadsus = ucs)", "nroCnsPaciente");
        hql.addToSelect("(select ucd.numeroMatricula from UsuarioCadsusDocumento ucd where ucd.usuarioCadsus = ucs and ucd.tipoDocumento.codigo = :tipoDocumentoCertidaoNascimento and ucd.situacaoExcluido = :nao )", "usuarioCadsusDocumento.numeroMatricula");

        hql.addToSelect("complementarParto", new HQLProperties(DadosComplementaresParto.class, "complementarParto").getProperties());
        hql.addToSelect("complementarUtiNeonatal", new HQLProperties(DadosComplementaresUtiNeonatal.class, "complementarUtiNeonatal").getProperties());
        hql.addToSelect("complementarLaqueadura", new HQLProperties(DadosComplementaresLaqueadura.class, "complementarLaqueadura").getProperties());

        hql.addToFrom("ItemContaPaciente icp"
                + " left join icp.contaPaciente cp"
                + " left join cp.contaPacientePrincipal cpp"
                + " left join cpp.convenio convenio"
                + " left join icp.procedimento itemProcedimento "
                + " left join icp.profissional profissionalItemConta "
                + " left join icp.empresaPrestador empresaPrestador "
                + " left join cpp.atendimentoAlta aa "
                + " left join cpp.atendimentoInformacao ai "
                + " left join ai.leitoQuarto leitoQuarto "
                + " left join leitoQuarto.quartoInternacao quartoInternacao "
                + " left join leitoQuarto.especialidadeLeito especialidadeLeito "
                + " left join ai.atendimentoPrincipal ap"
                + " left join icp.cbo cbo"
                + " left join icp.complementarParto complementarParto"
                + " left join icp.complementarUtiNeoNatal complementarUtiNeonatal"
                + " left join icp.complementarLaqueadura complementarLaqueadura");

        hql.addToFrom("Aih aih "
                + " left join aih.loteAih loteAih "
                + " left join aih.contaPaciente contaPaciente "
                + " left join aih.atendimento atendimento "
                + " left join aih.profissional profissionalAih "
                + " left join aih.profissionalResponsavel profissionalResponsavel "
                + " left join atendimento.atendimentoPrincipal atendimentoPrincipal "
                + " left join atendimentoPrincipal.empresa empresa "
                + " left join empresa.empresaPrincipal empresaPrincipal "
                + " left join empresaPrincipal.profissionalDiretor profissionalDiretor"
                //                + " left join empresa.empresaMantenedora empresaMantenedora "
                + " left join empresa.cidade cidade "
                + " left join aih.procedimentoSolicitado procedimentoSolicitado "
                + " left join aih.procedimentoRealizado procedimentoRealizado "
                //                + " left join empresaMantenedora.diretorClinico diretorClinico "
                + " left join aih.cidPrincipal cidPrincipal "
                + " left join aih.cidSecundario cidSecundario "
                + " left join aih.cidCausaAssociada cidCausaAssociada "
                + " left join aih.usuarioCadSus ucs "
                + " left join ucs.etniaIndigena etniaIndigena "
                + " left join ucs.raca raca "
                + " left join atendimento.enderecoUsuarioCadsus eucs "
                + " left join ucs.paisNascimento pn "
                + " left join eucs.tipoLogradouro tipoLogradouroCadsus "
                + " left join eucs.cidade cidadePaciente "
                + " left join cidadePaciente.estado estado "
                + " left join atendimento.responsavel responsavel ");

        hql.addToWhereWhithAnd("itemProcedimento.codigo is not null");
        hql.addToWhereWhithAnd("contaPaciente = cpp");
        hql.addToWhereWhithAnd("cpp.competenciaAtendimento", periodo);
        hql.addToWhereWhithAnd("cpp.status =", ContaPaciente.Status.FECHADA.value());
        hql.addToWhereWhithAnd("icp.status =", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToWhereWhithAnd("convenio = ", getConvenioSus());
        hql.addToWhereWhithAnd("aih.status = ", Aih.Status.AUTORIZADA.value());
        hql.addToOrder("cpp.competenciaAtendimento");
        // O procedimento principal da AIH deve sempre ser o primeiro da lista
        hql.addToOrder("(CASE WHEN itemProcedimento.codigo <> procedimentoRealizado.codigo THEN 1 ELSE 0 END)");
        // Os procedimentos com dados complementares devem ficar no primeiro bloco de 9 procedimentos
        hql.addToOrder("(CASE WHEN (complementarParto.codigo is not null OR complementarUtiNeonatal.codigo is not null OR complementarLaqueadura.codigo is not null) THEN 1 ELSE 0 END)");
    }

    public List<List<ProducaoAihDTO>> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.queryResult = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        this.result = CollectionUtils.groupList(queryResult, "contaPaciente.codigo");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("tipoDocumentoCertidaoNascimento", TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO);
        query.setLong("nao", RepositoryComponentDefault.NAO_LONG);
        super.setParameters(hql, query);
    }

    private Convenio getConvenioSus() {
        Convenio convenioSus = null;
        try {
            convenioSus = (Convenio) BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");
        } catch (DAOException ex) {
            Logger.getLogger(QueryConsultaProducaoAih.class.getName()).log(Level.SEVERE, null, ex);
        }
        return convenioSus;
    }
}
