package br.com.ksisolucoes.bo.consorcio.consorcioprestador;

import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ConsorcioPrestadorAgendaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorAgendaHorario;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Na<PERSON>et
 */
public class ExcluirAgendaPrestador extends AbstractCommandTransaction {

    private final List<ConsorcioPrestadorAgendaDTO> dtos;

    public ExcluirAgendaPrestador(List<ConsorcioPrestadorAgendaDTO> dtos) {
        this.dtos = dtos;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        for (ConsorcioPrestadorAgendaDTO dto : dtos) {
            if(dto.getAgendaHorarioList() != null) {
                Optional<ConsorcioPrestadorAgendaHorario> guias = dto.getAgendaHorarioList().stream().filter(o -> o.getConsorcioGuiaProcedimento()  != null).findAny();
                if(!guias.isPresent()){
                    for(ConsorcioPrestadorAgendaHorario cpah: dto.getAgendaHorarioList()) BOFactory.delete(cpah);
                }
            }
            BOFactory.delete(dto.getConsorcioPrestadorAgenda());
        }
    }

}
