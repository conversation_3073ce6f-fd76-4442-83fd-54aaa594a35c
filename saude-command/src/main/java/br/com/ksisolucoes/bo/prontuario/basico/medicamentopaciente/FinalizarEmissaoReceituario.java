/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.medicamentopaciente;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class FinalizarEmissaoReceituario extends AbstractCommandTransaction<FinalizarEmissaoReceituario> {

    private Long codigoAtendiento;
    private String codigoTabelaCbo;
    private Profissional profissional;

    public FinalizarEmissaoReceituario(Long codigoAtendiento, String codigoTabelaCbo, Profissional profissional) {
        this.codigoAtendiento = codigoAtendiento;
        this.codigoTabelaCbo = codigoTabelaCbo;
        this.profissional = profissional;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        Atendimento atendimento = (Atendimento) this.getSession().get(Atendimento.class, this.codigoAtendiento);
        
        TabelaCbo tabelaCbo = (TabelaCbo) this.getSession().get(TabelaCbo.class, this.codigoTabelaCbo);
        
        atendimento.setStatus(Atendimento.STATUS_FINALIZADO);
        atendimento.setDataAtendimento(Data.getDataAtual());
        atendimento.setDataFechamento(Data.getDataAtual());
        
        atendimento.setProfissional(profissional);
        atendimento.setTabelaCbo(tabelaCbo);
        
        List<Receituario> receituarios = this.getSession().createCriteria(Receituario.class)
                .add(Restrictions.eq(Receituario.PROP_ATENDIMENTO, atendimento))
                .add(Restrictions.ne(Receituario.PROP_SITUACAO, Receituario.Situacao.CANCELADO.value()))
                .setMaxResults(1)
                .list();
        
        atendimento.setCidPrincipal(receituarios.get(0).getCid());
        
        BOFactory.getBO(CadastroFacade.class).save(atendimento);
        
    }
    
    
    
}
