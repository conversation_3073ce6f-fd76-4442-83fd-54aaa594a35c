/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoHistorico;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class LiberarAtendimentoPendente extends AbstractCommandTransaction{

    private Long codigoAtendimento;
    private String descricaoOcorrencia;

    public LiberarAtendimentoPendente(Long codigoAtendimento, String descricaoOcorrencia) {
        this.codigoAtendimento = codigoAtendimento;
        this.descricaoOcorrencia = descricaoOcorrencia;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        Date dataAtual = Data.getDataAtual();
        
        Atendimento atendimento = (Atendimento) getSession().get(Atendimento.class, codigoAtendimento);
        atendimento.setStatus(Atendimento.STATUS_FINALIZADO);
        atendimento.setDataFechamento(dataAtual);
        
        Atendimento clone = (Atendimento) new DefinerPropertiesCloning().define(atendimento);
        
        clone.setProfissional(null);
        clone.setDataAtendimento(null);
        clone.setDataFechamento(null);

        if(AtendimentoHelper.permiteReclassificacao(clone)) {
            clone.setFlagPermiteReclassificacao(RepositoryComponentDefault.SIM_LONG);
        } else {
            clone.setFlagPermiteReclassificacao(RepositoryComponentDefault.NAO_LONG);
        }

        clone.setAtendimentoOrigem(atendimento);
        clone.setStatus(Atendimento.STATUS_AGUARDANDO);
        clone.setUsuarioAtendendo(null);

        BOFactory.save(clone);
        AtendimentoHistorico atendimentoHistorico = new AtendimentoHistorico();
        atendimentoHistorico.setAtendimento(atendimento);
        atendimentoHistorico.setDataHistorico(dataAtual);
        atendimentoHistorico.setUsuario((Usuario) sessao.getUsuario());
        atendimentoHistorico.setProfissional(((Usuario) sessao.getUsuario()).getProfissional());
        atendimentoHistorico.setDescricaoHistorico(Bundle.getStringApplication("rotulo_liberacao_atendimento")+": "+descricaoOcorrencia);

        BOFactory.save(atendimentoHistorico);
        
    }

}
