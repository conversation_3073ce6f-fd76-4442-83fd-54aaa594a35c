/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.localizacao;

import br.com.ksisolucoes.bo.command.DeleteVO;


/**
 * <AUTHOR>
 *
 */
public class DeleteLocalizacao extends DeleteVO {
    
    /**
     * Construtor default
     */
    public DeleteLocalizacao(Object vo) {
        super( vo );
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
}