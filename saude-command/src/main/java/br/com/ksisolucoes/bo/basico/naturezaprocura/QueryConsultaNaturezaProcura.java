package br.com.ksisolucoes.bo.basico.naturezaprocura;

import br.com.ksisolucoes.bo.basico.dto.QueryConsultaNaturezaProcuraDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaNaturezaProcura extends CommandQueryPager<QueryConsultaNaturezaProcura> {

    private QueryConsultaNaturezaProcuraDTOParam param;

    public QueryConsultaNaturezaProcura(QueryConsultaNaturezaProcuraDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("np.codigo", true);
        hql.addToSelect("np.descricao", true);
        
        hql.setTypeSelect(NaturezaProcura.class.getName());
        hql.addToFrom("NaturezaProcura np");
        
        hql.addToWhereWhithAnd("np.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("np.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("np.codigo || ' ' || np.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("np."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("np.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
