package br.com.ksisolucoes.bo.frota.motorista;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.frota.Motorista;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SaveMotorista extends SaveVO<Motorista> {

    public SaveMotorista(Motorista vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getTelefone() != null) {
            vo.setTelefone(StringUtils.trimToNull(StringUtilKsi.getDigits(vo.getTelefone())));
        }
        if (vo.getCelular() != null) {
            vo.setCelular(StringUtils.trimToNull(StringUtilKsi.getDigits(vo.getCelular())));
        }
        if (vo.getCep() != null) {
            vo.setCep(StringUtils.trimToNull(StringUtilKsi.getDigits(vo.getCep())));
        }

        if (vo.getProfissional() != null) {
            Motorista motorista = (Motorista) getSession().createCriteria(Motorista.class)
                    .add(Restrictions.eq(VOUtils.montarPath(Motorista.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), vo.getProfissional().getCodigo()))
                    .uniqueResult();

            if (motorista != null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_profissional_X_definido_motorista_Y", motorista.getProfissional().getNome(), motorista.getNome()));
            }
        }
    }

}
