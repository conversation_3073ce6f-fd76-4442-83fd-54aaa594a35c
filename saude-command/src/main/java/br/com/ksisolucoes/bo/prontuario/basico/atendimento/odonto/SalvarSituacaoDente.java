package br.com.ksisolucoes.bo.prontuario.basico.atendimento.odonto;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDenteProcedimento;
import static ch.lambdaj.Lambda.forEach;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarSituacaoDente extends AbstractCommandTransaction {

    private SituacaoDente situacaoDente;
    private List<SituacaoDenteProcedimento> situacaoDenteProcedimentoList;

    public SalvarSituacaoDente(SituacaoDente situacaoDente, List<SituacaoDenteProcedimento> situacaoDenteProcedimentoList) {
        this.situacaoDente = situacaoDente;
        this.situacaoDenteProcedimentoList = situacaoDenteProcedimentoList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.save(situacaoDente);

        if (CollectionUtils.isNotNullEmpty(situacaoDenteProcedimentoList)) {
            forEach(situacaoDenteProcedimentoList).setSituacaoDente(situacaoDente);
            VOUtils.persistirListaVosModificados(SituacaoDenteProcedimento.class, situacaoDenteProcedimentoList, new QueryCustom.QueryCustomParameter(SituacaoDenteProcedimento.PROP_SITUACAO_DENTE, situacaoDente));
        }
    }

    public SituacaoDente getSituacaoDente() {
        return situacaoDente;
    }
}
