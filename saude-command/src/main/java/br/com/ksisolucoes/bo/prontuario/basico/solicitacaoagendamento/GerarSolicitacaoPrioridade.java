package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoPrioridade;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class GerarSolicitacaoPrioridade extends AbstractCommandTransaction<GerarSolicitacaoPrioridade> {

    private SolicitacaoAgendamento solicitacaoAgendamento;

    public GerarSolicitacaoPrioridade(SolicitacaoAgendamento solicitacaoAgendamento) {
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (solicitacaoAgendamento == null) {
            return;
        }

        SolicitacaoPrioridade solicitacaoPrioridadePendente = LoadManager.getInstance(SolicitacaoPrioridade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SolicitacaoPrioridade.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_FILA_ESPERA_PRESTADOR)))
                .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoPrioridade.PROP_STATUS, SolicitacaoPrioridade.Status.PENDENTE.value()))
                .setMaxResults(1)
                .start().getVO();
        if (solicitacaoPrioridadePendente != null) {
            return;
        }

        SolicitacaoPrioridade solicitacaoPrioridade = new SolicitacaoPrioridade();
        solicitacaoPrioridade.setSolicitacaoAgendamento(solicitacaoAgendamento);
        solicitacaoPrioridade.setStatus(SolicitacaoPrioridade.Status.PENDENTE.value());
        BOFactory.save(solicitacaoPrioridade);
    }

}
