package br.com.ksisolucoes.bo.agendamento.proximidadesolicitanteexecutante;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutante;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutanteItem;

import java.util.ArrayList;

/**
 * Created by sulivan on 01/08/19.
 */
public class DeletarProximidadeSolicitanteExecutante extends AbstractCommandTransaction {

    private ProximidadeSolicitanteExecutante pse;

    public DeletarProximidadeSolicitanteExecutante(ProximidadeSolicitanteExecutante pse){
        this.pse = pse;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(ProximidadeSolicitanteExecutanteItem.class, new ArrayList(),
                new QueryCustom.QueryCustomParameter(ProximidadeSolicitanteExecutanteItem.PROP_PROXIMIDADE_SOLICITANTE_EXECUTANTE, pse));

        BOFactory.delete(pse);
    }
}
