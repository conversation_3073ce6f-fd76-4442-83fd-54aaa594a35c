package br.com.ksisolucoes.bo.agendamento.exame.ordenarAgenda;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutante;
import br.com.ksisolucoes.vo.agendamento.ProximidadeSolicitanteExecutanteItem;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;

public class BuscarProximidades extends AbstractCommandTransaction {

    private List<ProximidadeSolicitanteExecutanteItem> proximidades;
    private final Empresa empresaSolicitante;
    private TipoExame tipoExame;

    public BuscarProximidades(Empresa empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public BuscarProximidades(Empresa empresaSolicitante, TipoExame tipoExame) {
        this.empresaSolicitante = empresaSolicitante;
        this.tipoExame = tipoExame;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (tipoExame != null) {
            proximidades = getSession().createCriteria(ProximidadeSolicitanteExecutanteItem.class)
                    .addOrder(Order.asc(ProximidadeSolicitanteExecutanteItem.PROP_ORDEM))
                    .createCriteria(ProximidadeSolicitanteExecutanteItem.PROP_PROXIMIDADE_SOLICITANTE_EXECUTANTE)
                    .add(Restrictions.eq(ProximidadeSolicitanteExecutante.PROP_EMPRESA_SOLICITANTE, empresaSolicitante))
                    .add(Restrictions.eq(ProximidadeSolicitanteExecutante.PROP_TIPO_EXAME, tipoExame))
                    .list();
        } else {
            proximidades = getSession().createCriteria(ProximidadeSolicitanteExecutanteItem.class)
                    .addOrder(Order.asc(ProximidadeSolicitanteExecutanteItem.PROP_ORDEM))
                    .createCriteria(ProximidadeSolicitanteExecutanteItem.PROP_PROXIMIDADE_SOLICITANTE_EXECUTANTE)
                    .add(Restrictions.eq(ProximidadeSolicitanteExecutante.PROP_EMPRESA_SOLICITANTE, empresaSolicitante))
                    .list();
        }

    }

    public List<ProximidadeSolicitanteExecutanteItem> getProximidades() {
        return proximidades != null ? proximidades : new ArrayList<ProximidadeSolicitanteExecutanteItem>();
    }
}
