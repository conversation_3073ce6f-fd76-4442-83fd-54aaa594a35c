package br.com.ksisolucoes.bo.prontuario.basico.encaminhamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoParametrosFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.EncaminhamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarPedidoEncaminhamento extends AbstractCommandTransaction {

    private EncaminhamentoConsultaDTO encaminhamentoConsultaDTO;
    private EncaminhamentoConsulta encaminhamentoConsulta;
    private String tipoControleRegulacao;

    public GerarPedidoEncaminhamento(EncaminhamentoConsultaDTO encaminhamentoConsultaDTO) {
        this.encaminhamentoConsultaDTO = encaminhamentoConsultaDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        encaminhamentoConsulta = encaminhamentoConsultaDTO.getEncaminhamentoConsulta();

        encaminhamentoConsulta.getEncaminhamento().setTipo(Encaminhamento.Tipo.ENCAMINHAMENTO_CONSULTA.value());

        Encaminhamento encaminhamento = BOFactory.getBO(EncaminhamentoFacade.class).save(encaminhamentoConsulta.getEncaminhamento());

        if(StringUtilKsi.htmlIsEmpty(Coalesce.asString(encaminhamentoConsulta.getMotivoConsulta())) && RepositoryComponentDefault.SIM_LONG.equals(br.com.celk.util.Coalesce.asLong(encaminhamentoConsulta.getEncaminhamento().getFlagEnviarRegulacao(), RepositoryComponentDefault.NAO_LONG))){
            encaminhamentoConsulta.setMotivoConsulta(encaminhamentoConsulta.getEncaminhamento().getDescricaoEnviarRegulacao());
        }

        if (StringUtilKsi.htmlIsEmpty(Coalesce.asString(encaminhamentoConsulta.getMotivoConsulta()))) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo_consulta"));
        }

        BOFactory.getBO(CadastroFacade.class).save(encaminhamentoConsulta);

        List<EncaminhamentoConsultaCriterio> criterios = encaminhamentoConsultaDTO.getCriterios();
        if (CollectionUtils.isNotNullEmpty(criterios)) {
            Lambda.forEach(criterios).setEncaminhamentoConsulta(encaminhamentoConsulta);
        }

        VOUtils.persistirListaVosModificados(EncaminhamentoConsultaCriterio.class, criterios, new QueryCustom.QueryCustomParameter(EncaminhamentoConsultaCriterio.PROP_ENCAMINHAMENTO_CONSULTA, encaminhamentoConsulta));

        salvarAtendimentoProntuario(encaminhamento);
    }

    public EncaminhamentoConsulta getEncaminhamentoConsulta() {
        return encaminhamentoConsulta;
    }

    private void salvarAtendimentoProntuario(Encaminhamento encaminhamento) throws DAOException, ValidacaoException {
        Atendimento atendimento = encaminhamento.getAtendimento();
        AtendimentoProntuario atendimentoProntuario = LoadManager.getInstance(AtendimentoProntuario.class)
                .addProperties(new HQLProperties(AtendimentoProntuario.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_ENCAMINHAMENTO, encaminhamento))
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoProntuario.PROP_ATENDIMENTO, atendimento))
                .start().getVO();

        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.ENCAMINHAMENTO.value(), atendimento, encaminhamento.getCodigo());
        }

        atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setAtendimento(atendimento);
        atendimentoProntuario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(atendimento.getEmpresa());
        atendimentoProntuario.setProfissional(atendimento.getProfissional());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.ENCAMINHAMENTO.value());
        atendimentoProntuario.setEncaminhamento(encaminhamento);
        atendimentoProntuario.setDescricao(getDescricaoEncaminhamento(encaminhamento));
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setTabelaCbo(atendimento.getTabelaCbo());

        BOFactory.save(atendimentoProntuario);
    }

    private String getDescricaoEncaminhamento(Encaminhamento encaminhamento) {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append("<u>Encaminhamento Especialista</u>");
        descricao.append("\n<br />");
        descricao.append(encaminhamento.getTipoEncaminhamento().getDescricao());

        if(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            descricao.append(" - Enviar para regulação: ");

            if (RepositoryComponentDefault.NAO_LONG.equals(encaminhamento.getFlagEnviarRegulacao())) {
                descricao.append("Não");
            } else {
                descricao.append("Sim");
                descricao.append("\n<br />");
                descricao.append("Descrição: ").append(encaminhamento.getDescricaoEnviarRegulacao());
            }
        } else {
            AgendamentoParametrosFacade bo = BOFactory.getBO(AgendamentoParametrosFacade.class);
            if (!bo.getParamHabilitaClassificacaoRiscoEncaminhamentoEspecialista()) {
                descricao.append(" - Urgente: ");

                if (RepositoryComponentDefault.URGENTE_NAO.equals(encaminhamento.getFlagUrgencia())) {
                    descricao.append("Não");
                } else {
                    descricao.append("Sim");
                }
            } else {
                if (encaminhamento.getClassificacaoRisco() != null) {
                    descricao.append(" - Classificação de Risco: ");
                    descricao.append(encaminhamento.getClassificacaoRisco().getDescricao());
                }
            }

        }

        return descricao.toString();
    }

    public String getTipoControleRegulacao() {
        if(tipoControleRegulacao == null){
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }

}
