package br.com.ksisolucoes.bo.vigilancia.agravo.registroAgravoAcidenteTrabalhoGrave;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoAcidenteTrabalhoGraveDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.FichaInvestigacaoAgravo;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoGrave;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteTrabalhoGrave.SituacaoMercadoTrab;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Date;

/**
 * <AUTHOR> Santos
 */
public class SalvarFichaInvestigacaoAgravoAcidenteTrabalhoGrave extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoAcidenteTrabalhoGraveDTO investigacaoAgravoDTO;

    public SalvarFichaInvestigacaoAgravoAcidenteTrabalhoGrave(FichaInvestigacaoAgravoAcidenteTrabalhoGraveDTO investigacaoAgravoDTO) {
        this.investigacaoAgravoDTO = investigacaoAgravoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(investigacaoAgravoDTO.getRegistroAgravo().getCodigo());
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional(getSessao(),registroAgravo);

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setEscolaridade(registroAgravo.getUsuarioCadsus().getNivelEscolaridade());
        registroAgravo.setEndereco(registroAgravo.getUsuarioCadsus().getEnderecoUsuarioCadsus());
        registroAgravo.setDataPrimeirosSintomas(investigacaoAgravoDTO.getRegistroAgravo().getDataPrimeirosSintomas());
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(investigacaoAgravoDTO.isEncerrarFicha(),registroAgravo);

        InvestigacaoAgravoAcidenteTrabalhoGrave investigacaoAgravo = investigacaoAgravoDTO.getInvestigacaoAgravo();
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        UsuarioCadsus usuarioCadsus = FichaInvestigacaoHelper.getInstance().getUsuarioCadSus(registroAgravo.getUsuarioCadsus().getCodigo());
        UsuarioCadsusEsus usuarioCadsusEsus = FichaInvestigacaoHelper.getInstance().getUsuarioCadSusEsus(usuarioCadsus.getCodigo());
        boolean alterarDadosUsuario = investigacaoAgravo.getFlagInformacoesComplementares() != null &&
                investigacaoAgravo.getFlagInformacoesComplementares().equals(RepositoryComponentDefault.SIM);
        if (alterarDadosUsuario) {
            usuarioCadsus.setTabelaCbo(investigacaoAgravo.getOcupacao());
            usuarioCadsusEsus.setSituacaoMercadoTrabalho(investigacaoAgravo.getSituacaoMercadoTrab() == null ? null : SituacaoMercadoTrab.valueOf(investigacaoAgravo.getSituacaoMercadoTrab()).sum());
        }

        if (!isTemFichas(investigacaoAgravo.getCodigo())) {
            BOFactory.save(investigacaoAgravo);
            BOFactory.save(registroAgravo);
            if (alterarDadosUsuario) {
                BOFactory.save(usuarioCadsus);
                BOFactory.save(usuarioCadsusEsus);
            }

        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private boolean isTemFichas(Long idInvestigacaoAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoAcidenteTrabalhoGrave.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoAcidenteTrabalhoGrave.PROP_REGISTRO_AGRAVO,
                        RegistroAgravo.PROP_CODIGO), investigacaoAgravoDTO.getRegistroAgravo().getCodigo()));
        if (idInvestigacaoAgravo != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoAcidenteTrabalhoGrave.PROP_CODIGO),
                    BuilderQueryCustom.QueryParameter.DIFERENTE, idInvestigacaoAgravo));
        }
        return loadManager.start().exists();
    }
}
