/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CancelarAtendimentoReceituario extends AbstractCommandTransaction {

    private Atendimento atendimento;

    public CancelarAtendimentoReceituario(Atendimento atendimento) {
        this.atendimento = atendimento;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.getBO(AtendimentoFacade.class).cancelar(atendimento);
        
        List<Receituario> receituarios = this.getSession().createCriteria(Receituario.class)
                .add(Restrictions.eq(Receituario.PROP_ATENDIMENTO, atendimento))
                .add(Restrictions.ne(Receituario.PROP_SITUACAO, Receituario.Situacao.CANCELADO.value()))
                .list();
        
        for (Receituario receituario : receituarios) {
            receituario.setSituacao(Receituario.Situacao.CANCELADO.value());
            receituario.setReceituarioItemList(new ArrayList<ReceituarioItem>());
            BOFactory.save(receituario);
        }
    }
    
}
