package br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus;

import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import org.apache.commons.lang.StringUtils;


/**
 * <AUTHOR>
 *
 */
public class SaveEnderecoUsuarioCadsus extends SaveVO {

	private static final long serialVersionUID = 1L;

        private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
        
    public SaveEnderecoUsuarioCadsus(Object vo) {
        super( vo );
        this.enderecoUsuarioCadsus =(EnderecoUsuarioCadsus) vo;
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (Coalesce.asString(enderecoUsuarioCadsus.getLogradouro()).equals("")) {
            addRetornoValidacao(Bundle.getStringApplication("rotulo_logradouro"));
        }

        if ( !enderecoUsuarioCadsus.getRetornoValidacao().isValido() ){
            throw new ValidacaoException( enderecoUsuarioCadsus.getRetornoValidacao() );
        }

        if (this.enderecoUsuarioCadsus.getDataAlteracao()!=null) {
            this.enderecoUsuarioCadsus.setDataAlteracao(Data.getDataAtual());
        }
        if(this.enderecoUsuarioCadsus.getExcluido() == null){
            this.enderecoUsuarioCadsus.setExcluido(EnderecoUsuarioCadsus.ST_EXCLUIDO_NAO);
        }
        if(this.enderecoUsuarioCadsus.getAtivo() == null){
            this.enderecoUsuarioCadsus.setAtivo(EnderecoUsuarioCadsus.ST_ATIVO_SIM);
        }

        enderecoUsuarioCadsus.setTelefone(Coalesce.asString(enderecoUsuarioCadsus.getTelefone()).replaceAll("[^0-9]", ""));
        enderecoUsuarioCadsus.setFax(Coalesce.asString(enderecoUsuarioCadsus.getFax()).replaceAll("[^0-9]", ""));
        enderecoUsuarioCadsus.setTelefoneReferencia(Coalesce.asString(enderecoUsuarioCadsus.getTelefoneReferencia()).replaceAll("[^0-9]", ""));

        String cep = StringUtils.trimToNull(Coalesce.asString(enderecoUsuarioCadsus.getCep()).replaceAll("[^0-9]", ""));
        if (EnderecoUsuarioCadsus.ST_ATIVO_SIM.equals(enderecoUsuarioCadsus.getAtivo())) {
            if (cep != null) {
                boolean cepInvalido = !EnderecoHelper.isCepValido(cep);
                if (cepInvalido) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_cep_invalido", enderecoUsuarioCadsus.getEnderecoFormatado()));
                }
            }
    }

        enderecoUsuarioCadsus.setCep(cep);
    }

    private void addRetornoValidacao(String stringApplication) {
        if (enderecoUsuarioCadsus.getRetornoValidacao().isValido()) {
            enderecoUsuarioCadsus.getRetornoValidacao().add(Bundle.getStringApplication("msg_campos_obrigatorios_faltantes"));
        }
        enderecoUsuarioCadsus.getRetornoValidacao().add(stringApplication+", ");
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

	/**
	 * {@inheritDoc}
	 *
	 * @return {@inheritDoc}
	 */

    
}