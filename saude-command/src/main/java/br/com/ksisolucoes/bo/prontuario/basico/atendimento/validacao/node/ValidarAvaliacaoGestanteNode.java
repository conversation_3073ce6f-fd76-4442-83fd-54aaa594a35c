package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV2;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoPrimarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.AVALIACAO_GESTANTE, refClass = AtendimentoPrimarioDTO.class)
public class ValidarAvaliacaoGestanteNode extends AbstractCommandValidacaoV2<AtendimentoPrimarioDTO> {

    private final boolean isPuerpera = false;

    public ValidarAvaliacaoGestanteNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public AtendimentoPrimarioDTO executarValidacao(AtendimentoPrimarioDTO object) throws DAOException, ValidacaoException {
        if (registraAvaliacao(object)) {
            if (object.getAtendimentoPrimario().getPrioridade() == null) {
                object.getAtendimentoPrimario().setPrioridade(Atendimento.PRIORIDADE_NORMAL);
            }
            if (object.getAtendimentoPrimario().getDataAvaliacao() == null) {
                validacao(Bundle.getStringApplication("campoXObrigatorio", Bundle.getStringApplication("rotulo_data_avaliacao")));
            }
            if (object.getAtendimentoPrimario().getPeso() != null) {
                if (object.getAtendimentoPrimario().getPeso() < 0D || object.getAtendimentoPrimario().getPeso() > 500000) {
                    validacao(Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_peso")));
                }
            }
            if (object.getAtendimentoPrimario().getPressaoArterialSistolica() != null && !object.getAtendimentoPrimario().getPressaoArterialSistolica().equals(0L)) {
                if (object.getAtendimentoPrimario().getPressaoArterialSistolica() < 0 || object.getAtendimentoPrimario().getPressaoArterialSistolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pas")));
                }
            }
            if (object.getAtendimentoPrimario().getPressaoArterialDiastolica() != null && !object.getAtendimentoPrimario().getPressaoArterialDiastolica().equals(0L)) {
                if (object.getAtendimentoPrimario().getPressaoArterialDiastolica() < 0 || object.getAtendimentoPrimario().getPressaoArterialDiastolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pad")));
                }
            }
        } else {
            validacao(Bundle.getStringApplication("msg_informe_pelo_menos_um_campo"));
        }

        return object;
    }

    @Override
    public void processar(AtendimentoPrimarioDTO obj) throws DAOException, ValidacaoException {
        if (registraAvaliacao(obj)) {
            PreNatal preNatal = (PreNatal) getSession().createCriteria(PreNatal.class)
                    .add(Restrictions.eq(VOUtils.montarPath(PreNatal.PROP_STATUS), PreNatal.Status.ABERTO.value()))
                    .add(Restrictions.eq(VOUtils.montarPath(PreNatal.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                    .uniqueResult();

            if (preNatal != null) {
                preNatal.setDataUltimaConsulta(DataUtil.getDataAtual());
                preNatal.setNumeroConsulta(obj.getPreNatal().getNumeroConsulta());
                BOFactory.save(preNatal);
                obj.getAtendimentoPrimario().setNumeroConsulta(preNatal.getNumeroConsulta());
            }

            obj.getAtendimentoPrimario().setAtendimento(getAtendimento());
            BOFactory.getBO(AtendimentoFacade.class).registrarAvaliacaoPrimaria(obj.getAtendimentoPrimario(), getAtendimento().getProfissional());
            BOFactory.getBO(AtendimentoFacade.class).registrarProgramasSaude(atendimento, obj.getAtendimentoPrimario(), true);

            BOFactory.getBO(AtendimentoFacade.class).registrarAvaliacaoPrimariaGemelar(obj.getAtendimentoPrimario(), obj.getAtendimentoPrimarioGemelar());

            getAtendimento().setClassificacaoRisco(obj.getAtendimentoPrimario().getAtendimento().getClassificacaoRisco());
            BOFactory.save(getAtendimento());
        }
    }

    private boolean registraAvaliacao(AtendimentoPrimarioDTO obj) {
        if (obj != null && obj.getAtendimentoPrimario() != null && obj.getPreNatal() != null) {
            return Coalesce.asLong(obj.getPreNatal().getNumeroConsulta()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getIdadeGestacional()) > 0L
                    || Coalesce.asDouble(obj.getAtendimentoPrimario().getPeso()) > 0D
                    || Coalesce.asDouble(obj.getAtendimentoPrimario().getAltura()) > 0D
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getPressaoArterialDiastolica()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getPressaoArterialSistolica()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getGlicemia()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getEdema()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getAlturaUterina()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getBcf()) > 0L
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getMovFetal()) > 0L
                    || obj.getAtendimentoPrimario().getSisvanDoenca() != null
                    || obj.getAtendimentoPrimario().getSisvanIntercorrencia() != null
                    || (CollectionUtils.isNotNullEmpty(obj.getAtendimentoPrimarioGemelar()))
                    || Coalesce.asLong(obj.getAtendimentoPrimario().getGlicemia()) > 0L;
        }
        return false;
    }
}
