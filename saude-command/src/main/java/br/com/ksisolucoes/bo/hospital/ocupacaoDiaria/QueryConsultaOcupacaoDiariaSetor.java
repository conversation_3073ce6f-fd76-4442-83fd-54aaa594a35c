package br.com.ksisolucoes.bo.hospital.ocupacaoDiaria;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaOcupacaoDiariaSetorDTO;
import br.com.ksisolucoes.bo.prontuario.basico.QueryPagerAtendimentoWeb;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import org.hibernate.Session;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.vo.prontuario.basico.Atendimento.STATUS_OBSERVACAO;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaOcupacaoDiariaSetor extends CommandQuery<QueryConsultaOcupacaoDiariaSetor> {

    private Empresa empresa;
    private List<QueryConsultaOcupacaoDiariaSetorDTO> result;
    private String parametroTempo;

    public QueryConsultaOcupacaoDiariaSetor(Empresa empresa) {
        this.empresa = empresa;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryConsultaOcupacaoDiariaSetorDTO.class.getName());

        hql.addToSelect("quartoInternacao.codigo", "quartoInternacao.codigo");
        hql.addToSelect("quartoInternacao.descricao", "quartoInternacao.descricao");
        hql.addToSelect("leitoQuarto.codigo", "leitoQuarto.codigo");
        hql.addToSelect("leitoQuarto.descricao", "leitoQuarto.descricao");
        hql.addToSelect("usuarioCadsus.codigo", "atendimento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "atendimento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "atendimento.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "atendimento.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.dataNascimento", "atendimento.usuarioCadsus.dataNascimento");
        hql.addToSelect("atendimento.codigo", "atendimento.codigo");
        hql.addToSelect("atendimento.dataChegada", "atendimento.dataChegada");
        hql.addToSelect("atendimento.dataAtendimento", "atendimento.dataAtendimento");
        hql.addToSelect("atendimento.dataObservacao", "atendimento.dataObservacao");
        hql.addToSelect("atendimento.status", "atendimento.status");
        hql.addToSelect("tipoAtendimento.codigo", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.descricao", "atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");
        hql.addToSelect("convenio.codigo", "atendimento.convenio.codigo");
        hql.addToSelect("convenio.descricao", "atendimento.convenio.descricao");

        hql.addToFrom("LeitoQuarto leitoQuarto "
                + " left join leitoQuarto.quartoInternacao quartoInternacao "
                + " left join quartoInternacao.empresa empresa"
                + " left join leitoQuarto.atendimento atendimento with atendimento.status in (" + Atendimento.STATUS_AGUARDANDO + "," + Atendimento.STATUS_ATENDENDO + ") "
                + " left join atendimento.usuarioCadsus usuarioCadsus "
                + " left join atendimento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento "
                + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join atendimento.convenio convenio");


        hql.addToWhereWhithAnd("empresa.codigo = " + empresa.getCodigo());
        hql.addToWhereWhithAnd("quartoInternacao.situacao != " + QuartoInternacao.Situacao.EXCLUIDO.value().toString());
        hql.addToWhereWhithAnd("leitoQuarto.situacao != " + LeitoQuarto.Situacao.EXCLUIDO.value());

        hql.addToOrder("quartoInternacao.descricao");
        hql.addToOrder("leitoQuarto.descricao");
    }

    @Override
    public List<QueryConsultaOcupacaoDiariaSetorDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        getParametroTempo();
        for (QueryConsultaOcupacaoDiariaSetorDTO cods : this.getResult()) {
            if (cods.getAtendimento() != null) {
                cods.setTempoAtendimento(getTempoAtendimento(parametroTempo, cods.getAtendimento().getDataObservacao(),
                        cods.getAtendimento().getStatus(),
                        cods.getAtendimento().getDataChegada(),
                        cods.getAtendimento().getDataCadastro()));
            }
        }

    }

    private void getParametroTempo() {
        try {
            parametroTempo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("CalculoTempo");
        } catch (DAOException ex) {
            Logger.getLogger(QueryPagerAtendimentoWeb.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public String getTempoAtendimento(String parametroTempo, Date dataObservacao, Long status, Date dataChegada, Date dataCadastro) {
        if (STATUS_OBSERVACAO.equals(status) && dataObservacao != null) {
            return Data.imprimirIntervaloHoras(dataObservacao, DataUtil.getDataAtual());
        } else {
            if ("C".equals(parametroTempo)) {
                return Data.imprimirIntervaloHoras(dataChegada, DataUtil.getDataAtual());
            } else {
                return Data.imprimirIntervaloHoras(dataCadastro, DataUtil.getDataAtual());
            }
        }
    }
}
