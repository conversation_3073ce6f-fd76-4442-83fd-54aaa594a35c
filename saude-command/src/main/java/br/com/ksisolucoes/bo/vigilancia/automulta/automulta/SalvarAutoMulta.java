package br.com.ksisolucoes.bo.vigilancia.automulta.automulta;

import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.GerarChaveProcessoAdministrativoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.SalvarAutoMultaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMultaFiscal;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarAutoMulta extends AbstractCommandTransaction {

    private AutoMulta autoMulta;
    private List<AutoMultaFiscal> lstFiscal;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private PnlRequerimentoVigilanciaAnexoDTO anexoDTO;

    public SalvarAutoMulta(SalvarAutoMultaDTO salvarAutoMultaDTO) {
        this.autoMulta = salvarAutoMultaDTO.getAutoMulta();
        this.lstFiscal = salvarAutoMultaDTO.getLstFiscal();
        this.anexoDTO = salvarAutoMultaDTO.getAnexoDTO();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean inNew = false;
        if (autoMulta.getCodigo() == null) {
            inNew = true;
        }

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracao();

        autoMulta = BOFactory.save(autoMulta);

        for (AutoMultaFiscal item : lstFiscal) {
            item.setAutoMulta(autoMulta);
        }
        VOUtils.persistirListaVosModificados(AutoMultaFiscal.class, lstFiscal, new QueryCustom.QueryCustomParameter(AutoMultaFiscal.PROP_AUTO_MULTA, autoMulta));

        // anexos do auto de multa
        if (anexoDTO != null) {
            BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(autoMulta, anexoDTO.getRequerimentoVigilanciaAnexoDTOList(), anexoDTO.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        }

        if (inNew) {
            if (autoMulta.getRequerimentoVigilancia() != null && autoMulta.getRequerimentoVigilancia().getCodigo() != null) {
                gerarOcorrenciaRequerimentoVigilancia(autoMulta, autoMulta.getRequerimentoVigilancia());
                enviarEmailResponsavelRequerimentoVigilancia(autoMulta, autoMulta.getRequerimentoVigilancia());
            }
            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAutoMulta(autoMulta.getDataMulta(), autoMulta);
            if (autoMulta.getProcessoAdministrativoAutenticacao() == null || autoMulta.getProcessoAdministrativoAutenticacao().getCodigo() == null) {
                gerarChaveProcesso();
                autoMulta = BOFactory.save(autoMulta);
            }
        }
    }

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if (autoMulta.getNumero() == null && autoMulta.getCodigo() == null) {
            if (configuracaoVigilancia.getAnoBaseGeral() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
            }
            if (configuracaoVigilancia.getNumAutoMulta() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numeracao_base_auto_multa"));
            }

            long sequencial = 0L;
            if (Coalesce.asLong(configuracaoVigilancia.getNumAutoMulta()) > 0L) {
                sequencial = configuracaoVigilancia.getNumAutoMulta();
            }
            sequencial++;
            String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
            Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
            autoMulta.setNumero(nextId);

            configuracaoVigilancia.setNumAutoMulta(sequencial);

            BOFactory.save(configuracaoVigilancia);
        }
    }

    private void carregarConfiguracaoVigilancia() throws ValidacaoException {
        configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
    }

    private void gerarChaveProcesso() throws ValidacaoException, DAOException {
        GerarChaveProcessoAdministrativoDTO dto = new GerarChaveProcessoAdministrativoDTO();
        dto.setCodigoAuto(autoMulta.getCodigo());
        dto.setCpf(AutosHelper.getCpfAuto(autoMulta));
        dto.setNome(AutosHelper.getNomeResponsavel(autoMulta));
        dto.setEmail(AutosHelper.getEmailAutuado(autoMulta));
        dto.setTelefone(AutosHelper.getTelefoneAutuado(autoMulta));
        ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao = BOFactory.getBO(VigilanciaFacade.class).gerarChaveProcessoAdministrativo(dto);
        if (processoAdministrativoAutenticacao == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
        }
        this.autoMulta.setProcessoAdministrativoAutenticacao(processoAdministrativoAutenticacao);
    }

    private void gerarOcorrenciaRequerimentoVigilancia(AutoMulta autoMulta, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String mensagemOcorrencia = "Registro do Auto de Multa Nº " + autoMulta.getNumeroFormatado();
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(mensagemOcorrencia, requerimentoVigilancia, null);
    }

    private void enviarEmailResponsavelRequerimentoVigilancia(AutoMulta autoMulta, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String assunto = "Registro do Auto de Multa Nº " + autoMulta.getNumeroFormatado();
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        sb.append("<body>");
        sb.append("<p>Informativo do registro de Auto de Multa na Vigilância Sanitária.</p>");
        sb.append("<br/>");
        sb.append("<strong>Protocolo: </strong>").append(requerimentoVigilancia.getProtocoloFormatado());
        sb.append("<br/>");
        sb.append("<strong>Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoTipoDocumento());
        sb.append("<br/>");
        sb.append("<strong>Situação do Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoSituacao());
        sb.append("<br/>");
        sb.append("<strong>Ambiente de recursos disponível em: </strong>");
        String realContext = TenantContext.getRealContext();
        if (realContext.equals("localhost")) {
            realContext = realContext.concat(":8080");
        }
        sb.append(realContext);
        sb.append(("/vigilancia"));
        sb.append("</body>");
        sb.append("</html>");
        BOFactory.getBO(VigilanciaFacade.class).enviarEmailRequerimentoVigilancia(requerimentoVigilancia, sb.toString(), assunto);
    }
}
