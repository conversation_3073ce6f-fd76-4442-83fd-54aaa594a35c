package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryProdutosUnidadePorGrupoSubGrupo extends CommandQuery<QueryProdutosUnidadePorGrupoSubGrupo> {

    private Empresa empresa;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private List<Produto> result;

    public QueryProdutosUnidadePorGrupoSubGrupo(Empresa empresa, GrupoProduto grupoProduto, SubGrupo subGrupo) {
        this.empresa = empresa;
        this.grupoProduto = grupoProduto;
        this.subGrupo = subGrupo;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(Produto.class.getName());

        hql.addToSelect("produto.codigo", "codigo");

        hql.addToFrom("Produto produto"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto");

        hql.addToWhereWhithAnd("NOT EXISTS(SELECT 1 FROM EstoqueEmpresa ee WHERE ee.roProduto = produto AND ee.roEmpresa = :paramEmpresa)");
        hql.addToWhereWhithAnd("grupoProduto = ", grupoProduto);
        hql.addToWhereWhithAnd("subGrupo = ", subGrupo);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("paramEmpresa", empresa);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String,Object>>) result);
    }

    @Override
    public List<Produto> getResult() {
        return result;
    }

}
