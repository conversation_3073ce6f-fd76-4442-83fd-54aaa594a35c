package br.com.ksisolucoes.bo.prontuario.nodos;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NodoAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaNodosAtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaNodosAtendimentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.EloNodoTipoExame;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import ch.lambdaj.group.Group;
import org.hamcrest.Matchers;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaNodosAtendimento extends CommandQuery {

    private QueryConsultaNodosAtendimentoDTOParam param;
    private List<QueryConsultaNodosAtendimentoDTO> internalResult;
    private List<NodoAtendimentoDTO> result;

    public QueryConsultaNodosAtendimento(QueryConsultaNodosAtendimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("nodoAtendimento.codigo", "nodoAtendimentoWeb.codigo");
        hql.addToSelect("nodoAtendimento.classeNodo", "nodoAtendimentoWeb.classeNodo");
        hql.addToSelect("nodoAtendimento.ordem", "nodoAtendimentoWeb.ordem");
        hql.addToSelect("tipoAtendimento.codigo", "nodoAtendimentoWeb.tipoAtendimento.codigo");
        hql.addToSelect("nodoAtendimento.apenasMunicipio", "nodoAtendimentoWeb.apenasMunicipio");
        hql.addToSelect("nodoAtendimento.sexo", "nodoAtendimentoWeb.sexo");
        hql.addToSelect("nodoAtendimento.flagPermiteReclassificacao", "nodoAtendimentoWeb.flagPermiteReclassificacao");
        hql.addToSelect("nodoAtendimento.permiteCorrecao", "nodoAtendimentoWeb.permiteCorrecao");
        hql.addToSelect("nodoAtendimento.exibirAtendimento", "nodoAtendimentoWeb.exibirAtendimento");
        hql.addToSelect("nodoAtendimento.permiteHistorico", "nodoAtendimentoWeb.permiteHistorico");
        hql.addToSelect("nodoAtendimento.permiteAtendimentoParalelo", "nodoAtendimentoWeb.permiteAtendimentoParalelo");
        hql.addToSelect("nodoAtendimento.visivelUsuariosTemporarios", "nodoAtendimentoWeb.visivelUsuariosTemporarios");
        hql.addToSelect("nodoAtendimento.informarProfissional", "nodoAtendimentoWeb.informarProfissional");
        hql.addToSelect("nodoAtendimento.informarEstabelecimentoOrigem", "nodoAtendimentoWeb.informarEstabelecimentoOrigem");
        hql.addToSelect("tipoAtendimento.descricao", "nodoAtendimentoWeb.tipoAtendimento.descricao");
        hql.addToSelect("eloTipoAtendimentoGrupoAtendimentoCbo.codigo", "eloTipoAtendimentoGrupoAtendimentoCbo.codigo");
        hql.addToSelect("eloTipoAtendimentoGrupoAtendimentoCbo.exigeEvolucao", "eloTipoAtendimentoGrupoAtendimentoCbo.exigeEvolucao");
        hql.addToSelect("eloTipoAtendimentoGrupoAtendimentoCbo.exigeEncaminhamento", "eloTipoAtendimentoGrupoAtendimentoCbo.exigeEncaminhamento");
        hql.addToSelect("eloTipoAtendimentoGrupoAtendimentoCbo.validaPrescricaoInterna", "eloTipoAtendimentoGrupoAtendimentoCbo.validaPrescricaoInterna");
        hql.addToSelect("grupoAtendimentoCbo.codigo", "eloTipoAtendimentoGrupoAtendimentoCbo.grupoAtendimentoCbo.codigo");
        hql.addToSelect("grupoAtendimentoCbo.descricao", "eloTipoAtendimentoGrupoAtendimentoCbo.grupoAtendimentoCbo.descricao");
        hql.addToSelect("convenio.codigo", "convenio.codigo");
        hql.addToSelect("convenio.descricao", "convenio.descricao");
        

        hql.addToSelect("grupoAtendimentoCboProfissional.codigo", "nodoAtendimentoWeb.grupoAtendimentoCbo.codigo");
        hql.addToSelect("grupoAtendimentoCboProfissional.descricao", "nodoAtendimentoWeb.grupoAtendimentoCbo.descricao");
        
        hql.addToSelect("eloNodoTipoExame.codigo", "eloNodoTipoExame.codigo");
        hql.addToSelect("eloNodoTipoExame.tipoAcao", "eloNodoTipoExame.tipoAcao");
        hql.addToSelect("empresaTipoExame.codigo", "eloNodoTipoExame.empresa.codigo");
        hql.addToSelect("empresaTipoExame.descricao", "eloNodoTipoExame.empresa.descricao");
        hql.addToSelect("tipoExame.codigo", "eloNodoTipoExame.tipoExame.codigo");
        hql.addToSelect("tipoExame.descricao", "eloNodoTipoExame.tipoExame.descricao");
        hql.addToSelect("tipoAtendimentoTipoExame.codigo", "eloNodoTipoExame.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimentoTipoExame.descricao", "eloNodoTipoExame.tipoAtendimento.descricao");

        hql.setTypeSelect(QueryConsultaNodosAtendimentoDTO.class.getName());
        hql.addToFrom("EloNodoTipoAtendimentoGrupoAtendimentoCbo elo"
                + " right join elo.nodoAtendimento nodoAtendimento"
                + " left join nodoAtendimento.tipoAtendimento tipoAtendimento"
                + " left join nodoAtendimento.grupoAtendimentoCbo grupoAtendimentoCboProfissional"
                + " left join elo.eloTipoAtendimentoGrupoAtendimentoCbo eloTipoAtendimentoGrupoAtendimentoCbo"
                + " left join eloTipoAtendimentoGrupoAtendimentoCbo.grupoAtendimentoCbo grupoAtendimentoCbo");

        hql.addToFrom("EloNodoConvenio eloNodoConvenio"
                + " right join eloNodoConvenio.nodoAtendimento nodoAtendimentoC"
                + " left join eloNodoConvenio.convenio convenio");

        hql.addToFrom("EloNodoTipoExame eloNodoTipoExame "
                + " right join eloNodoTipoExame.nodoAtendimentoWeb nodoAtendimentoWebTipoExame "
                + " left join eloNodoTipoExame.empresa empresaTipoExame "
                + " left join eloNodoTipoExame.tipoExame tipoExame "
                + " left join eloNodoTipoExame.tipoAtendimento tipoAtendimentoTipoExame");

        hql.addToWhereWhithAnd("nodoAtendimento = nodoAtendimentoC");

        hql.addToWhereWhithAnd("nodoAtendimentoWebTipoExame = nodoAtendimento");

        hql.addToWhereWhithAnd("tipoAtendimento = ", param.getTipoAtendimento());

        hql.addToOrder("nodoAtendimento.ordem");

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.internalResult = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        result = new LinkedList<NodoAtendimentoDTO>();
        Group<QueryConsultaNodosAtendimentoDTO> groupList = group(internalResult, by(on(QueryConsultaNodosAtendimentoDTO.class).getNodoAtendimentoWeb().getCodigo()));

        for (Group<QueryConsultaNodosAtendimentoDTO> subGroup : groupList.subgroups()) {
            NodoAtendimentoDTO dto = new NodoAtendimentoDTO();
            dto.setClassName(subGroup.first().getNodoAtendimentoWeb().getClasseNodo());
            dto.setSexo(subGroup.first().getNodoAtendimentoWeb().getSexo());
            dto.setFlagPermiteReclassificacao(subGroup.first().getNodoAtendimentoWeb().getFlagPermiteReclassificacao());
            dto.setApenasMunicipio(subGroup.first().getNodoAtendimentoWeb().getApenasMunicipio());
            dto.setVisivelUsuariosTemporarios(subGroup.first().getNodoAtendimentoWeb().getVisivelUsuariosTemporarios());
            dto.setPermiteCorrecao(subGroup.first().getNodoAtendimentoWeb().getPermiteCorrecao());
            dto.setExibirAtendimento(subGroup.first().getNodoAtendimentoWeb().getExibirAtendimento());
            dto.setPermiteAtendimentoParalelo(subGroup.first().getNodoAtendimentoWeb().getPermiteAtendimentoParalelo());
            dto.setInformarProfissional(subGroup.first().getNodoAtendimentoWeb().getInformarProfissional());
            dto.setInformarEstabelecimentoOrigem(subGroup.first().getNodoAtendimentoWeb().getInformarEstabelecimentoOrigem());
            dto.setGrupoProfissional(subGroup.first().getNodoAtendimentoWeb().getGrupoAtendimentoCbo());
            dto.setPermiteHistorico(subGroup.first().getNodoAtendimentoWeb().getPermiteHistorico());

            { //Grupos de Cbo
                List<EloTipoAtendimentoGrupoAtendimentoCbo> extractGrupos = extract(
                        select(subGroup.findAll(), having(on(QueryConsultaNodosAtendimentoDTO.class).getEloTipoAtendimentoGrupoAtendimentoCbo(), Matchers.notNullValue())),
                        on(QueryConsultaNodosAtendimentoDTO.class).getEloTipoAtendimentoGrupoAtendimentoCbo());
                
                Group<EloTipoAtendimentoGrupoAtendimentoCbo> groupGrupos = group(extractGrupos, by(on(EloTipoAtendimentoGrupoAtendimentoCbo.class).getCodigo()));

                dto.setGrupo(new ArrayList<EloTipoAtendimentoGrupoAtendimentoCbo>());

                for (Group<EloTipoAtendimentoGrupoAtendimentoCbo> sub : groupGrupos.subgroups()) {
                    dto.getGrupo().add(sub.first());
                }
            }

            { // Convenios
                List<Convenio> extractConvenios = extract(
                        select(subGroup.findAll(), having(on(QueryConsultaNodosAtendimentoDTO.class).getConvenio(), Matchers.notNullValue())),
                        on(QueryConsultaNodosAtendimentoDTO.class).getConvenio());

                Group<Convenio> groupConvenios = group(extractConvenios, by(on(Convenio.class).getCodigo()));

                dto.setConvenios(new ArrayList<Convenio>());

                for (Group<Convenio> sub : groupConvenios.subgroups()) {
                    dto.getConvenios().add(sub.first());
                }
            }
           
            { //Tipo Exame
                List<EloNodoTipoExame> extractTipoExame = extract(
                        select(subGroup.findAll(), having(on(QueryConsultaNodosAtendimentoDTO.class).getEloNodoTipoExame(), Matchers.notNullValue())),
                        on(QueryConsultaNodosAtendimentoDTO.class).getEloNodoTipoExame());

                Group<EloNodoTipoExame> groupTipoExame = group(extractTipoExame, by(on(EloNodoTipoExame.class).getCodigo()));

                dto.setLstEloNodoTipoExame(new ArrayList<EloNodoTipoExame>());

                for (Group<EloNodoTipoExame> sub : groupTipoExame.subgroups()) {
                    dto.getLstEloNodoTipoExame().add(sub.first());
                }
            }

            result.add(dto);
        }
    }
    
    @Override
    public List<NodoAtendimentoDTO> getResult() {
        return result;
    }

}
