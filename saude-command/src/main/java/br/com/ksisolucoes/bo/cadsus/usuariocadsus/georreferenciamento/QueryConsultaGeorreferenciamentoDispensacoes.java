package br.com.ksisolucoes.bo.cadsus.usuariocadsus.georreferenciamento;

import br.com.celk.cadsus.relatorio.GeorreferenciamentoUsuarioCadsusDispensacoesDTO;
import br.com.celk.cadsus.relatorio.GeorrefereniamentoUsuarioCadsusDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaGeorreferenciamentoDispensacoes extends CommandQuery<QueryConsultaGeorreferenciamentoDispensacoes> {

    private List<GeorreferenciamentoUsuarioCadsusDispensacoesDTO> result;
    private final GeorrefereniamentoUsuarioCadsusDTOParam param;

    public QueryConsultaGeorreferenciamentoDispensacoes(GeorrefereniamentoUsuarioCadsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(GeorreferenciamentoUsuarioCadsusDispensacoesDTO.class.getName());

        hql.addToSelect("dispensacaoMedicamentoItem.codigo", "dispensacaoMedicamentoItem.codigo");

        hql.addToSelect("dispensacaoMedicamento.codigo", "dispensacaoMedicamentoItem.dispensacaoMedicamento.codigo");
        hql.addToSelect("dispensacaoMedicamento.dataDispensacao", "dispensacaoMedicamentoItem.dispensacaoMedicamento.dataDispensacao");

        hql.addToSelect("empresaOrigem.codigo", "dispensacaoMedicamentoItem.dispensacaoMedicamento.empresaOrigem.codigo");
        hql.addToSelect("empresaOrigem.descricao", "dispensacaoMedicamentoItem.dispensacaoMedicamento.empresaOrigem.descricao");

        hql.addToSelect("tipoReceita.codigo", "dispensacaoMedicamentoItem.dispensacaoMedicamento.tipoReceita.codigo");
        hql.addToSelect("tipoReceita.descricao", "dispensacaoMedicamentoItem.dispensacaoMedicamento.tipoReceita.descricao");

        hql.addToSelect("produto.codigo", "dispensacaoMedicamentoItem.produto.codigo");
        hql.addToSelect("produto.descricao", "dispensacaoMedicamentoItem.produto.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");

        hql.addToSelect("enderecoUsuarioCadsus.codigo", "enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");

        hql.addToSelect("enderecoDomicilio.codigo", "enderecoDomicilio.codigo");
        hql.addToSelect("enderecoDomicilio.latitude", "enderecoDomicilio.latitude");
        hql.addToSelect("enderecoDomicilio.longitude", "enderecoDomicilio.longitude");


        hql.addToFrom("DispensacaoMedicamentoItem dispensacaoMedicamentoItem "
                        + " left join dispensacaoMedicamentoItem.dispensacaoMedicamento dispensacaoMedicamento "
                        + " left join dispensacaoMedicamentoItem.produto produto"
                        + " left join dispensacaoMedicamento.tipoReceita tipoReceita"
                        + " left join dispensacaoMedicamento.usuarioCadsusDestino usuarioCadsus "
                        + " left join dispensacaoMedicamento.empresaOrigem empresaOrigem "
                        + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                        + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                        + " left join enderecoUsuarioCadsus.cidade cidade"
                        + " left join cidade.estado estado"
        );

        hql.addToWhereWhithAnd("produto = ", param.getProdutoMedicamento());
        hql.addToWhereWhithAnd("dispensacaoMedicamento.dataDispensacao ", param.getPeriodoDispensacao());
        hql.addToWhereWhithAnd("tipoReceita = ", param.getTipoReceita());
        hql.addToWhereWhithAnd("empresaOrigem =  ", param.getEmpresaDispensacao());
        hql.addToWhereWhithAnd(" enderecoUsuarioCadsus is not null ");

        hql.addToOrder("dispensacaoMedicamento.codigo desc");
    }

    public List<GeorreferenciamentoUsuarioCadsusDispensacoesDTO> getResult() {
        return result;
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(param.getMaxLimit().intValue() + 1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            if (this.result.size() >= param.getMaxLimit()) {
                return;
            }
            for (GeorreferenciamentoUsuarioCadsusDispensacoesDTO geoDTO : this.result) {
                if (geoDTO.getEnderecoDomicilio() != null
                        && geoDTO.getEnderecoDomicilio().getLatitude() != null
                        && geoDTO.getEnderecoDomicilio().getLongitude() != null) {
                    geoDTO.setLatitude(new Double(geoDTO.getEnderecoDomicilio().getLatitude()));
                    geoDTO.setLongitude(new Double(geoDTO.getEnderecoDomicilio().getLongitude()));
                    continue;
                }
                LatitudeLongitudeEndereco latLng = new LatitudeLongitudeEndereco(geoDTO.getEnderecoUsuarioCadsus());
                if (latLng.getLongitude() != 0.0 && latLng.getLatitude() != 0.0) {
                    geoDTO.setLatitude(latLng.getLatitude());
                    geoDTO.setLongitude(latLng.getLongitude());
                }
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
