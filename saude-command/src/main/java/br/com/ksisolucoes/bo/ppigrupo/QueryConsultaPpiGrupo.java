package br.com.ksisolucoes.bo.ppigrupo;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.formulario.Formulario;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaPpiGrupo extends CommandQueryPager<QueryConsultaPpiGrupo> {

    private QueryConsultaPpiGrupoDTOParam param;

    public QueryConsultaPpiGrupo(QueryConsultaPpiGrupoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("pg.codigo", true);
        hql.addToSelect("pg.descricao", true);
        
        hql.setTypeSelect(Formulario.class.getName());
        hql.addToFrom("PpiGrupo pg");
        
        hql.addToWhereWhithAnd(hql.getConsultaLiked("pg.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("pg.codigo || ' ' || pg.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("pg."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("pg.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
