package br.com.ksisolucoes.bo.entradas.recebimento.registronotafiscal;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque.SaveMovimentoEstoque;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscal;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroItemNotaFiscalLicitacao;
import br.com.ksisolucoes.vo.entradas.recebimento.RegistroNotaFiscal;
import br.com.ksisolucoes.vo.financeiro.Serie;
import org.hibernate.Criteria;
import org.hibernate.LockMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EstornarNotaFiscal extends AbstractCommandTransaction {

    private RegistroNotaFiscal registroNotaFiscal;
    RegistroItemNotaFiscal itemNotaFiscal;

    public EstornarNotaFiscal(RegistroNotaFiscal registroNotaFiscal) {
        this.registroNotaFiscal = registroNotaFiscal;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        /*
         * Carrega a lista de itens.
         */
        Criteria cRegistroNotaFiscalItens = getSession().createCriteria(RegistroItemNotaFiscal.class);

        cRegistroNotaFiscalItens.add(Restrictions.eq(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_REGISTRO_NOTA_FISCAL), registroNotaFiscal));
        cRegistroNotaFiscalItens.add(Restrictions.in(VOUtils.montarPath(RegistroItemNotaFiscal.PROP_STATUS), Arrays.asList(RegistroItemNotaFiscal.STATUS_RECEBIDO, RegistroItemNotaFiscal.STATUS_RECEBIDO_PARCIAL)));

        List<RegistroItemNotaFiscal> registroItemNotaFiscalList = cRegistroNotaFiscalItens.list();

        TipoDocumento tipoDocumentoEstornoNota = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("TipoDocumentoEstornoNotas");

        /*
         * Realiza as validações de concorrência.
         */
        {
            if (!RegistroNotaFiscal.STATUS_ENCERRADO.equals(registroNotaFiscal.getStatus())) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_nota_ja_modificada_por_outro_processo"));
            }

            // Verifica que o estoque foi utilizado por outro processo do sistema
            for (RegistroItemNotaFiscal registroItemNotaFiscal : registroItemNotaFiscalList) {
                MovimentoEstoque movimentoEstoque = (MovimentoEstoque) getSession().createCriteria(MovimentoEstoque.class)
                        .add(Restrictions.eq(MovimentoEstoque.PROP_REGISTRO_ITEM_NOTA_FISCAL, registroItemNotaFiscal))
                        .addOrder(Order.desc(VOUtils.montarPath(MovimentoEstoque.PROP_ID, MovimentoEstoquePK.PROP_NUMERO_LANCAMENTO)))
                        .setMaxResults(1).uniqueResult();

                Long countMovimentoEstoque = (Long) getSession().createCriteria(MovimentoEstoque.class)
                        .setProjection(Projections.count(VOUtils.montarPath(MovimentoEstoque.PROP_ID, MovimentoEstoquePK.PROP_NUMERO_LANCAMENTO)))
                        .add(Restrictions.gt(VOUtils.montarPath(MovimentoEstoque.PROP_ID, MovimentoEstoquePK.PROP_NUMERO_LANCAMENTO), movimentoEstoque.getId().getNumeroLancamento()))
                        .add(Restrictions.eq(VOUtils.montarPath(MovimentoEstoque.PROP_ID, MovimentoEstoquePK.PROP_EMPRESA), movimentoEstoque.getId().getEmpresa()))
                        .add(Restrictions.eq(VOUtils.montarPath(MovimentoEstoque.PROP_PRODUTO), movimentoEstoque.getProduto()))
                        .add(Restrictions.eq(VOUtils.montarPath(MovimentoEstoque.PROP_GRUPO_ESTOQUE), movimentoEstoque.getGrupoEstoque()))
                        .createCriteria(MovimentoEstoque.PROP_TIPO_DOCUMENTO)
                        .add(Restrictions.ne(VOUtils.montarPath(TipoDocumento.PROP_CODIGO), tipoDocumentoEstornoNota.getCodigo()))
                        .add(Restrictions.eq(VOUtils.montarPath(TipoDocumento.PROP_FLAG_TIPO_MOVIMENTO), TipoDocumento.IS_SAIDA))
                        .uniqueResult();

                if (countMovimentoEstoque > 0) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_impossivel_estornar_nota_estoque_produto_X_modificado_outro_procedimento", movimentoEstoque.getProduto().getDescricao()));
                }
            }
        }

        /*
         * Realiza as modificações na Nota Fiscal.
         */
        registroNotaFiscal = (RegistroNotaFiscal) HibernateUtil.lockTable(RegistroNotaFiscal.class, this.registroNotaFiscal.getCodigo());

        registroNotaFiscal.setStatus(RegistroNotaFiscal.STATUS_ABERTO); //CONSTANTE STATUS_ABERTO, RÓTULO PENDENTE
        registroNotaFiscal.setDataEstorno(DataUtil.getDataAtual());
        registroNotaFiscal.setUsuarioEstorno((Usuario) getSessao().getUsuario());
        registroNotaFiscal.setDataConfirmacao(null);
        /*
         * Realiza as modificações nos Itens da Nota Fiscal.
         */

        List<MovimentoGrupoEstoqueItemDTO> movList = new ArrayList<MovimentoGrupoEstoqueItemDTO>();

        for (RegistroItemNotaFiscal registroItemNotaFiscal_ : registroItemNotaFiscalList) {

            itemNotaFiscal = (RegistroItemNotaFiscal) HibernateUtil.lockTable(RegistroItemNotaFiscal.class, registroItemNotaFiscal_.getCodigo());

            itemNotaFiscal.setStatus(RegistroItemNotaFiscal.STATUS_NORMAL);

            /*
             * Atualiza o campo qtd estoque.
             */
            if (itemNotaFiscal.getQuantidadeEstoque() != 0) {
                itemNotaFiscal.setQuantidadeEstoque(0D); //O PROCESSO DE APROVAR ALTERA ESTA QUANTIDADE QUANDO A MESMA É DIFERENTE DE ZERO.
            }

            /*
             * Carrega e atualiza os possíveis Elos com a Licitacao (Criados na ação de aprovar)
             */
            if (RepositoryComponentDefault.SIM.equals(itemNotaFiscal.getRegistroNotaFiscal().getTipoDocumento().getFlagLicitacao())) {

                List<RegistroItemNotaFiscalLicitacao> elosLicitacao = getSession().createCriteria(RegistroItemNotaFiscalLicitacao.class)
                        .add(org.hibernate.criterion.Restrictions.eq(RegistroItemNotaFiscalLicitacao.PROP_REGISTRO_ITEM_NOTA_FISCAL, itemNotaFiscal))
                        .list();

                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(elosLicitacao)) {
                    List<List<RegistroItemNotaFiscalLicitacao>> groupList = br.com.ksisolucoes.util.CollectionUtils.groupList(elosLicitacao, VOUtils.montarPath(RegistroItemNotaFiscalLicitacao.PROP_LICITACAO_ITEM, LicitacaoItem.PROP_CODIGO));

                    for (List<RegistroItemNotaFiscalLicitacao> list : groupList) {
                        LicitacaoItem licitacaoItem = HibernateUtil.lockTable(LicitacaoItem.class, list.get(0).getLicitacaoItem().getCodigo());

                        licitacaoItem.setQuantidadeRecebida(new Dinheiro(Coalesce.asLong(licitacaoItem.getQuantidadeRecebida())).subtrair(itemNotaFiscal.getQuantidade()).longValue()); //ao Aprovar soma-se
                        licitacaoItem.setPrecoTotalRecebido(new Dinheiro(Coalesce.asDouble(licitacaoItem.getPrecoTotalRecebido())).subtrair(itemNotaFiscal.getValorItem()).round().doubleValue()); //ao Aprovar soma-se
                        BOFactory.save(licitacaoItem);

                        String atualizaQuantidadeRecebidaPedidoLicitacaoNF = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("atualizaQuantidadeRecebidaPedidoLicitacaoNF");
                        boolean ocPossuiPedido = false;

                        if (RepositoryComponentDefault.SIM.equals(atualizaQuantidadeRecebidaPedidoLicitacaoNF)) {
                            List<OrdemCompraEloNota> eloOcList = LoadManager.getInstance(OrdemCompraEloNota.class)
                                    .addProperties(new HQLProperties(OrdemCompraItem.class, OrdemCompraEloNota.PROP_ORDEM_COMPRA_ITEM).getProperties())
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(OrdemCompraEloNota.PROP_QUANTIDADE), itemNotaFiscal))
                                    .start().getList();

                            if (!eloOcList.isEmpty()) {
                                PedidoTransferenciaLicitacaoEloOrdemCompra proxy = on(PedidoTransferenciaLicitacaoEloOrdemCompra.class);
                                List<PedidoTransferenciaLicitacaoEloOrdemCompra> pedidoTransferenciaLicitacaoEloOrdemCompraList;

                                for(OrdemCompraEloNota elo : eloOcList) {
                                    pedidoTransferenciaLicitacaoEloOrdemCompraList = LoadManager.getInstance(PedidoTransferenciaLicitacaoEloOrdemCompra.class)
                                            .addProperty(path(proxy.getPedidoTransferenciaLicitacaoItem().getCodigo()))
                                            .addProperty(path(proxy.getOrdemCompraItem().getCodigo()))
                                            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getOrdemCompraItem()), BuilderQueryCustom.QueryParameter.IGUAL, elo.getOrdemCompraItem()))
                                            .start().getList();

                                    for (PedidoTransferenciaLicitacaoEloOrdemCompra eloPed : pedidoTransferenciaLicitacaoEloOrdemCompraList) {
                                        List<EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem> eloLicitacaoPedidoList;

                                        Long qtdadeRecebida = itemNotaFiscal.getQuantidade().longValue();

                                        ocPossuiPedido = true;

                                        eloLicitacaoPedidoList = getSession().createCriteria(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.class)
                                                .createAlias(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_LICITACAO_ITEM, EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_LICITACAO_ITEM)
                                                .createAlias(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM, EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM)
                                                .add(Restrictions.eq(VOUtils.montarPath(EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO_ITEM, PedidoTransferenciaLicitacaoItem.PROP_CODIGO), eloPed.getPedidoTransferenciaLicitacaoItem().getCodigo()))
                                                .addOrder(br.com.ksisolucoes.system.consulta.Order.asc(PedidoLicitacaoItem.PROP_CODIGO))
                                                .setLockMode(LockMode.PESSIMISTIC_READ)
                                                .list();

                                        for (EloPedidoLicitacaoItemPedidoTransferenciaLicitacaoItem eloLicPed : eloLicitacaoPedidoList) {
                                            if (qtdadeRecebida <= 0) {
                                                break;
                                            }

                                            qtdadeRecebida = setQuantidadeRecebidaLicitacao(eloLicPed.getPedidoLicitacaoItem(), qtdadeRecebida);
                                        }
                                    }
                                }
                            }

                            if (!ocPossuiPedido) {
                                Long qtdadeRecebida = itemNotaFiscal.getQuantidade().longValue();

                                List<EloLicitacaoPedido> eloLicitacaoPedidoList;
                                eloLicitacaoPedidoList = getSession().createCriteria(EloLicitacaoPedido.class)
                                        .add(Restrictions.eq(EloLicitacaoPedido.PROP_LICITACAO_ITEM, licitacaoItem))
                                        .createCriteria(EloLicitacaoPedido.PROP_PEDIDO_LICITACAO_ITEM)
                                        .add(Restrictions.eq(PedidoLicitacaoItem.PROP_PRODUTO, itemNotaFiscal.getProduto()))
                                        .add(Restrictions.ne(PedidoLicitacaoItem.PROP_STATUS, PedidoLicitacaoItem.StatusPedidoLicitacaoItem.CANCELADO.value()))
                                        .createCriteria(PedidoLicitacaoItem.PROP_PEDIDO_LICITACAO)
                                        .add(Restrictions.eq(PedidoLicitacao.PROP_EMPRESA, SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa()))
                                        .add(Restrictions.ne(PedidoLicitacao.PROP_STATUS, PedidoLicitacao.StatusPedidoLicitacao.CANCELADO.value()))
                                        .addOrder(br.com.ksisolucoes.system.consulta.Order.asc(PedidoLicitacaoItem.PROP_CODIGO))
                                        .setLockMode(LockMode.PESSIMISTIC_READ)
                                        .list();

                                for (EloLicitacaoPedido eloLicPed : eloLicitacaoPedidoList) {
                                    if (qtdadeRecebida <= 0) {
                                        break;
                                    }

                                    qtdadeRecebida = setQuantidadeRecebidaLicitacao(eloLicPed.getPedidoLicitacaoItem(), qtdadeRecebida);
                                }
                            }
                        }
                    }
                }
            }

            /*
             * Realiza os procedimentos reversos com a movimentação de estoque
             */

            if (MovimentoEstoque.IS_ENTRA_ESTOQUE.equals(itemNotaFiscal.getRegistroNotaFiscal().getTipoDocumento().getFlagEntradaEstoque()) && !itemNotaFiscal.isProdutoOcasional()) {

                List<RecebimentoGrupoEstoque> recebimentoGrupoEstoqueList = new ArrayList<>();

                MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
                MovimentoEstoquePK id = new MovimentoEstoquePK(itemNotaFiscal.getRegistroNotaFiscal().getEmpresa(), null);
                movimentoEstoque.setId(id);
                movimentoEstoque.setEmpresaDestino(itemNotaFiscal.getRegistroNotaFiscal().getEmpresaDestino());
                movimentoEstoque.setTipoDocumento(tipoDocumentoEstornoNota);
                movimentoEstoque.setProduto(itemNotaFiscal.getProduto());
                movimentoEstoque.setNumeroDocumento(Coalesce.asString(itemNotaFiscal.getRegistroNotaFiscal().getCodigo()));
                movimentoEstoque.setItemDocumento(itemNotaFiscal.getItem());
                movimentoEstoque.setQuantidade(itemNotaFiscal.getQuantidade());

                movimentoEstoque.setMovimentosGrupoEstoqueItem(movList);

                movimentoEstoque.setRegistroItemNotaFiscal(this.itemNotaFiscal);
                movimentoEstoque.setNotaFiscalEntrada(this.itemNotaFiscal.getRegistroNotaFiscal().getNumeroNotaFiscal());
                movimentoEstoque.setDataPortaria(this.itemNotaFiscal.getRegistroNotaFiscal().getDataPortaria());
                movimentoEstoque.setEstoqueFisico(this.itemNotaFiscal.getQuantidadeEstoque());

                movimentoEstoque.setPessoa(this.itemNotaFiscal.getRegistroNotaFiscal().getFornecedor());
                movimentoEstoque.setNomePessoa(this.itemNotaFiscal.getRegistroNotaFiscal().getFornecedor().getDescricao());

                /**
                 *    RECUPERA OS GRUPO_ESTOQUE DO PRODUTO PARA REALIZAR A MOVIMENTAÇÃO
                 */
                if (this.itemNotaFiscal.getProduto() != null && this.itemNotaFiscal.getProduto().getSubGrupo().isExigeGrupo()) {
                    List<MovimentoGrupoEstoqueItemDTO> movimentos = new ArrayList();

                    if (CollectionUtils.isEmpty(recebimentoGrupoEstoqueList)) {
                        recebimentoGrupoEstoqueList = getRecebimentoGrupoEstoqueList(this.itemNotaFiscal.getItem(), this.itemNotaFiscal.getRegistroNotaFiscal().getNumeroNotaFiscal(),
                                this.itemNotaFiscal.getRegistroNotaFiscal().getEmpresa(), this.itemNotaFiscal.getRegistroNotaFiscal().getFornecedor(), this.itemNotaFiscal.getRegistroNotaFiscal().getSerie());
                    }

                    for (RecebimentoGrupoEstoque recebimentoGrupoEstoque : recebimentoGrupoEstoqueList) {
                        MovimentoGrupoEstoqueItemDTO movimento = new MovimentoGrupoEstoqueItemDTO();
                        movimento.setGrupoEstoque(recebimentoGrupoEstoque.getGrupoEstoque());
                        movimento.setLocalizacaoEstrutura(recebimentoGrupoEstoque.getLocalizacaoEstrutura());
                        movimento.setEmpresa(recebimentoGrupoEstoque.getEmpresaSetor());
                        movimento.setQuantidade(recebimentoGrupoEstoque.getQuantidadeLote());
                        movimento.setDataValidade(recebimentoGrupoEstoque.getDataVencimento());
                        movimento.setFabricante(recebimentoGrupoEstoque.getFabricante());
                        movimentos.add(movimento);
                    }
                    movimentoEstoque.setMovimentosGrupoEstoqueItem(movimentos);
                }

                new SaveMovimentoEstoque(movimentoEstoque).start();
            }
        }
    }

    private List<RecebimentoGrupoEstoque> getRecebimentoGrupoEstoqueList(Long item, Long numeroNotaFiscal, Empresa empresa, Pessoa fornecedor, Serie serie) {
        RecebimentoGrupoEstoque proxy = on(RecebimentoGrupoEstoque.class);

        return LoadManager.getInstance(RecebimentoGrupoEstoque.class)
                .addProperties(new HQLProperties(RecebimentoGrupoEstoque.class).getProperties())
                .addProperties(new HQLProperties(LocalizacaoEstrutura.class, RecebimentoGrupoEstoque.PROP_LOCALIZACAO_ESTRUTURA).getProperties())
                .addProperties(new HQLProperties(Empresa.class, RecebimentoGrupoEstoque.PROP_EMPRESA_SETOR).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroItemNotaFiscal().getItem()), item))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroItemNotaFiscal().getRegistroNotaFiscal().getNumeroNotaFiscal()), numeroNotaFiscal))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroItemNotaFiscal().getRegistroNotaFiscal().getEmpresa()), empresa))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroItemNotaFiscal().getRegistroNotaFiscal().getFornecedor()), fornecedor))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getRegistroItemNotaFiscal().getRegistroNotaFiscal().getSerie()), serie))
                .start().getList();
    }

    public RegistroNotaFiscal getRegistroNotaFiscal() {
        return registroNotaFiscal;
    }

    private Long setQuantidadeRecebidaLicitacao(PedidoLicitacaoItem pedidoLicitacaoItem, Long qtdadeRecebida) throws DAOException, ValidacaoException {
        Long novaQuantidade = new Dinheiro(Coalesce.asLong(pedidoLicitacaoItem.getQuantidadeRecebida())).subtrair(new Dinheiro(qtdadeRecebida)).longValue();

        if (novaQuantidade < 0) {
            qtdadeRecebida = new Dinheiro(qtdadeRecebida).subtrair(new Dinheiro(pedidoLicitacaoItem.getQuantidadeRecebida())).longValue();

            pedidoLicitacaoItem.setQuantidadeRecebida(0L);
        } else {
            qtdadeRecebida = 0L;

            pedidoLicitacaoItem.setQuantidadeRecebida(novaQuantidade);
        }

        BOFactory.getBO(CadastroFacade.class).save(pedidoLicitacaoItem);

        return qtdadeRecebida;
    }
}
