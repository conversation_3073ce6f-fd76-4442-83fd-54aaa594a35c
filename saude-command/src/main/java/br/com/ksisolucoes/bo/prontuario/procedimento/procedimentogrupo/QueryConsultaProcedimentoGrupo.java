package br.com.ksisolucoes.bo.prontuario.procedimento.procedimentogrupo;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaProcedimentoGrupoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupo;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProcedimentoGrupo extends CommandQueryPager<QueryConsultaProcedimentoGrupo> {

    private QueryConsultaProcedimentoGrupoDTOParam param;

    public QueryConsultaProcedimentoGrupo(QueryConsultaProcedimentoGrupoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("pro.codigo", true);
        hql.addToSelect("pro.descricao", true);
        
        hql.setTypeSelect(ProcedimentoGrupo.class.getName());
        hql.addToFrom("ProcedimentoGrupo pro");
        
        hql.addToWhereWhithAnd("pro.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("pro.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("pro.codigo || ' ' || pro.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("pro."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("pro.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
