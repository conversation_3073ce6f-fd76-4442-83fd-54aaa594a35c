/*
 * Created on 12/11/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.recebimento.recebimentogrupoestoque;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.recebimento.RecebimentoGrupoEstoque;

/**
 * <AUTHOR>
 *
 */
public class SaveRecebimentoGrupoEstoque extends SaveVO{

    private RecebimentoGrupoEstoque recebimentoGrupoEstoque;
    
    public SaveRecebimentoGrupoEstoque(Object vo) {
        super( vo );
        this.recebimentoGrupoEstoque = (RecebimentoGrupoEstoque) vo;
    }
    
    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if(this.recebimentoGrupoEstoque.getQuantidadeTransferencia() == null){
            this.recebimentoGrupoEstoque.setQuantidadeTransferencia(0D);
        }
        if( this.recebimentoGrupoEstoque.getDataCadastro() == null ) {
            this.recebimentoGrupoEstoque.setDataCadastro( Data.getDataAtual() );
        }
        if( this.recebimentoGrupoEstoque.getLocalizacaoEstrutura() == null ) {
            this.recebimentoGrupoEstoque.setLocalizacaoEstrutura(EstoqueHelper.getLocalizacaoEstruturaPadrao());
        }
    }
}