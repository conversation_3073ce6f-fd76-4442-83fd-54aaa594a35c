/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.procedimento.tabelacbo;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaDominioTabelaCboDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.prontuario.procedimento.DominioTabelaCbo;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioTabelaCbo extends CommandQueryPager<QueryConsultaDominioTabelaCbo> {

    private QueryConsultaDominioTabelaCboDTOParam param;

    public QueryConsultaDominioTabelaCbo(QueryConsultaDominioTabelaCboDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("tabelaCbo.cbo", "tabelaCbo.cbo");
        hql.addToSelect("tabelaCbo.descricao", "tabelaCbo.descricao");
        hql.addToSelect("dominioTabelaCbo.descricao", true);
        hql.addToSelect("tabelaCbo.nivelEnsino", "tabelaCbo.nivelEnsino");

        hql.setTypeSelect(DominioTabelaCbo.class.getName());
        hql.addToFrom("DominioTabelaCbo dominioTabelaCbo"
                + " left join dominioTabelaCbo.tabelaCbo tabelaCbo ");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("tabelaCbo.cbo", param.getCodigo()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tabelaCbo.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("dominioTabelaCbo.keyword", param.getKeyword()));

        if (param.getProcedimento() != null) {
            hql.addToWhereWhithAnd("(exists (select 1 from ProcedimentoCbo pCbo "
                    + " where pCbo.id.procedimentoCompetencia.id.dataCompetencia = :dataCompetencia"
                    + " and pCbo.id.procedimentoCompetencia.id.procedimento.codigo = :codigoProcedimento"
                    + " and pCbo.id.tabelaCbo.cbo = tabelaCbo))");
        }
        
        if (param.getProfissional() != null && param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("(exists (select 1 from ProfissionalCargaHoraria pch "
                    + " where pch.profissional.codigo = " + param.getProfissional().getCodigo()
                    + " and pch.empresa.codigo = " + param.getEmpresa().getCodigo()
                    + " and pch.tabelaCbo = tabelaCbo))");
        }

        if (param.isFiltrarAtivos()) {
            hql.addToWhereWhithAnd("tabelaCbo.ativo = ", RepositoryComponentDefault.SIM_LONG);
        }

        hql.addToOrder("tabelaCbo.descricao");

    }

    @Override
    protected void setParameters(Query query) {
        if (param.getProcedimento() != null) {
            Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
            query.setDate("dataCompetencia", parametro.getDataCompetenciaProcedimento());
            query.setLong("codigoProcedimento", param.getProcedimento().getCodigo());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

}
