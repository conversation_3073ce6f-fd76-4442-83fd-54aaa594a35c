package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestadorCompetencia;

import java.util.List;

public class SalvarCotaEstabelecimentosModificados extends AbstractCommandTransaction<ExameUnidadePrestadorCompetencia> {
    private List<ExameUnidadePrestadorCompetencia> cotaEstabelecimentosModificados;
    private ExamePrestadorCompetencia prestador;

    public SalvarCotaEstabelecimentosModificados(List<ExameUnidadePrestadorCompetencia> cotaEstabelecimentosModificados, ExamePrestadorCompetencia prestador) {
        this.cotaEstabelecimentosModificados = cotaEstabelecimentosModificados;
        this.prestador = prestador;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ExamePrestadorCompetencia prestadorSessao = (ExamePrestadorCompetencia) getSession().get(ExamePrestadorCompetencia.class, prestador.getCodigo());
        for (ExameUnidadePrestadorCompetencia cotaEstabelecimentosModificado : cotaEstabelecimentosModificados) {
            ExameUnidadePrestadorCompetencia cotaEstabelecimentosModificadoSessao = (ExameUnidadePrestadorCompetencia) getSession().get(ExameUnidadePrestadorCompetencia.class, cotaEstabelecimentosModificado.getCodigo());
            prestadorSessao.setTetoFinanceiro(prestadorSessao.getTetoFinanceiro() + (cotaEstabelecimentosModificado.getTetoFinanceiro() - cotaEstabelecimentosModificadoSessao.getTetoFinanceiro()));
            cotaEstabelecimentosModificadoSessao.setTetoFinanceiro(cotaEstabelecimentosModificado.getTetoFinanceiro());
            BOFactory.save(cotaEstabelecimentosModificadoSessao);
        }
        BOFactory.save(prestadorSessao);
    }
}
