package br.com.ksisolucoes.bo.materiais.importacao.brasindice;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.BrasindiceProcess;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import java.util.List;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class RemoverBrasindiceProcessAdicionais extends AbstractCommandTransaction{

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<BrasindiceProcess> list =  getSession().createCriteria(BrasindiceProcess.class)
                .setMaxResults(10)
                .createCriteria(BrasindiceProcess.PROP_ASYNC_PROCESS)
                .addOrder(Order.desc(AsyncProcess.PROP_DATA_REGISTRO))
                .list();

        if(br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(list)){
            List<BrasindiceProcess> deletar =  getSession().createCriteria(BrasindiceProcess.class)
                    .add(Restrictions.lt(BrasindiceProcess.PROP_CODIGO, list.get(list.size() - 1).getCodigo()))
                    .list();

            for (BrasindiceProcess brasindiceProcess : deletar) {
                BOFactory.delete(brasindiceProcess);
            }
        }
    }
    
}
