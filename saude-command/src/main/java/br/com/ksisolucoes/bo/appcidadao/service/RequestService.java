package br.com.ksisolucoes.bo.appcidadao.service;

import br.com.ksisolucoes.bo.appcidadao.utils.AppCidadaoUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ejb.Stateless;
import javax.ws.rs.client.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.concurrent.ExecutionException;

@Stateless
public class RequestService {


//    private Invocation.Builder getTarget(String url) throws ValidacaoException {
//        Client client = ClientBuilder.newClient();
//        WebTarget target = client.target(url);
//        return target
//                .request(MediaType.APPLICATION_JSON)
//                .header("Authorization",
//                        "Bearer " + getAppClientCredationTokenService().getToken());
//
//    }

    private AppClientCredationTokenService getAppClientCredationTokenService() {
        return new AppClientCredationTokenService();
    }

    public void post(String url, String json) throws ExecutionException, InterruptedException, ValidacaoException {
        Client client = ClientBuilder.newClient();
        WebTarget target = client.target(url);
        try {
            Response response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildPost(Entity.json(json)).submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        } catch (Exception e) {
            getAppClientCredationTokenService().destroy();
            Response response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildPost(Entity.json(json)).submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        }finally {
            client.close();
        }
    }

    public void put(String url, String json) throws ExecutionException, InterruptedException, ValidacaoException {
        Client client = ClientBuilder.newClient();
        WebTarget target = client.target(url);
        try {
            Response response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildPut(Entity.json(json)).submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        } catch (Exception e) {
            getAppClientCredationTokenService().destroy();
            Response response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildPut(Entity.json(json)).submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        }finally {
            client.close();
        }
    }

    public Response get(String url) throws ExecutionException, InterruptedException, ValidacaoException {
        Client client = ClientBuilder.newClient();
        WebTarget target = client.target(url);
        Response response;
        try {
            response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildGet().submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        } catch (Exception e) {
            getAppClientCredationTokenService().destroy();
            response = target
                    .request(MediaType.APPLICATION_JSON)
                    .header("Authorization",
                            "Bearer " + getAppClientCredationTokenService().getToken())
                    .buildGet().submit(Response.class).get();
            AppCidadaoUtils.tratarErro(response);
        }finally {
            client.close();
        }
        return response;
    }
}
