package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import java.util.List;

/**
 *
 * <AUTHOR> Colombo
 */
public class QueryPaginaWebPublica extends CommandQuery<QueryPaginaWebPublica> {

    private String url;

    private boolean publicPage;

    public QueryPaginaWebPublica(String url) {
        this.url = url;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("pp.codigo", "codigo");

        hql.setTypeSelect(ProgramaPagina.class.getName());
        hql.addToFrom("ProgramaPagina pp ");

        hql.addToWhereWhithAnd("pp.flagPublico = ", RepositoryComponentDefault.SIM);
        hql.addToWhereWhithAnd("pp.caminhoPagina = ", url);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        publicPage = hql.getBeanList((List) result).size() > 0;
    }

    public boolean isPublicPage() {
        return publicPage;
    }
}
