package br.com.ksisolucoes.bo.prontuario.basico.reprocessaatendimentosesus;

import br.com.ksisolucoes.dao.HQLHelper;

public class ReprocessaAtendimentosEsusQueryHelper {
    public static HQLHelper addFrom(HQLHelper hql){
        hql.addToFrom(
                "atendimento " +
                        "left join natureza_procura_tp_atendimento on " +
                        "natureza_procura_tp_atendimento.cd_nat_proc_tp_atendimento = atendimento.cd_nat_proc_tp_atendimento " +
                        "left join tipo_atendimento on " +
                        "natureza_procura_tp_atendimento.cd_tp_atendimento = tipo_atendimento.cd_tp_atendimento " +
                        "left join esus_integracao_cds " +
                        "on atendimento.nr_atendimento = esus_integracao_cds.nr_atendimento " +
                        "left join tabela_cbo " +
                        "on atendimento.cd_cbo = tabela_cbo.cd_cbo");
        return hql;
    }

    public static HQLHelper addWhere(HQLHelper hql){
        hql
                .addToWhereWhithAnd("tipo_atendimento.tp_atendimento_esus notnull ")
                .addToWhereWhithAnd("esus_integracao_cds.nr_atendimento is null ")
                .addToWhereWhithAnd("atendimento.status = 4 ")
                // Se for diferente de um lanca ficha de atendimento, senao, lanca apenas procedimento
                // Quando lanca de procedimento, eh necessario verificar se o procedimento vai para o esus
                .addToWhereWhithAnd("coalesce(atendimento.correcao, 0) <> 1 ")
                .addToWhereWhithAnd("(coalesce(tabela_cbo.nivel_ensino, -1) <> 0 " +
                        "OR (" +
                        "select " +
                        "max(1) " +
                        "from " +
                        "atendimento_item " +
                        "where " +
                        "atendimento_item.cd_procedimento is not null " +
                        "and atendimento_item.nr_atendimento = atendimento.nr_atendimento) = 1)");
        return hql;
    }

}
