/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vigilancia.denuncia.denuncia;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.pendencia.PendenciaDia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 *
 * <AUTHOR>
 */
public class SaveDenuncia extends SaveVO<Denuncia> {

    public SaveDenuncia(Denuncia vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {

        if (this.vo.getEmailDenunciante() != null && !Util.isEmailValido(this.vo.getEmailDenunciante())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_email_denunciante_invalido"));
        }

        Usuario usuario;
        if (getSessao() == null) {
            usuario = new Usuario(Usuario.USUARIO_ADMINISTRADOR);
        } else {
            usuario = getSessao().<Usuario>getUsuario();
        }

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }

        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(usuario);
        }

        if (this.vo.getStatus() == null) {
            this.vo.setStatus(Denuncia.Status.PENDENTE.value());
        }

        if (SessaoAplicacaoImp.getInstance() != null) {
            this.vo.setEmpresa(SessaoAplicacaoImp.getInstance().getEmpresa());
        }

        if (this.vo.getTelefoneDenunciado() != null) {
            this.vo.setTelefoneDenunciado(StringUtil.getDigits(this.vo.getTelefoneDenunciado()));
        }

        if (this.vo.getTelefoneDenunciante() != null) {
            this.vo.setTelefoneDenunciante(StringUtil.getDigits(this.vo.getTelefoneDenunciante()));
        }

        if (this.vo.getCnpjCpfDenunciante() != null) {
            String cpfCnpj = StringUtil.getDigits(this.vo.getCnpjCpfDenunciante());

            if (Pessoa.PESSOA_FISICA.equals(this.vo.getTipoPessoaDenunciante()) && !CpfCnpJValidator.CPFIsValid(cpfCnpj)) {
                throw new ValidacaoException(VigilanciaHelper.createMessageCpfInvalid(vo.getRequerimentoVigilancia().getProtocolo()));

            } else if (Pessoa.PESSOA_JURIDICA.equals(this.vo.getTipoPessoaDenunciante()) && !CpfCnpJValidator.CNPJIsValid(cpfCnpj)) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_cnpj_invalido_solicitante_requerimento"));
            }
            this.vo.setCnpjCpfDenunciante(cpfCnpj);
        }

    }

    @Override
    protected void depoisSave() throws ValidacaoException, DAOException {
        super.depoisSave();

        gerarPendencia();
    }

    public void gerarPendencia() throws DAOException, ValidacaoException {
        PendenciaDia pd = LoadManager.getInstance(PendenciaDia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_CODIGO_ROTINA, vo.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_CODIGO_ROTINA, QueryCustom.QueryCustomParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(PendenciaDia.PROP_TIPO_ROTINA, PendenciaDia.TipoRotina.DENUNCIA.value()))
                .start().getVO();

        if (pd == null) {

            pd = new PendenciaDia();
            pd.setDataCadastro(Data.getDataAtual());
            pd.setTipoRotina(PendenciaDia.TipoRotina.DENUNCIA.value());
            pd.setDescricao(vo.getDenunciado());
            pd.setCodigoRotina(vo.getCodigo());
            pd.setDataPendencia(vo.getDataCadastro());

            if (getSessao() == null) {
                pd.setUsuario(new Usuario(Usuario.USUARIO_ADMINISTRADOR));
            } else {
                pd.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
            }

        } else {
            pd.setDataPendencia(vo.getDataCadastro());
        }

        pd.setSituacao(PendenciaDia.Situacao.FINALIZADO.value());

        BOFactory.save(pd);
    }
}
