package br.com.ksisolucoes.bo.controle.menudinamico;

import br.com.celk.controlemenu.dto.EdicaoMenuDinamicoDTO;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SalvarSubmenuDinamico extends AbstractCommandTransaction<SalvarSubmenuDinamico> {
    
    private final EdicaoMenuDinamicoDTO dto;

    public SalvarSubmenuDinamico(EdicaoMenuDinamicoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validarExisteSubmenu();
        BOFactory.save(dto.getSubmenu());
    }
    
    private void validarExisteSubmenu() throws ValidacaoException{
        Criteria criteria = this.getSession().createCriteria(MenuWeb.class)
            .setProjection(Projections.count(MenuWeb.PROP_CODIGO))
            .add(Restrictions.eq(MenuWeb.PROP_DESCRICAO, Coalesce.asString(dto.getSubmenu().getDescricao())))
            .add(Restrictions.eq(MenuWeb.PROP_LAYOUT_MENU, dto.getSubmenu().getLayoutMenu()))
            .add(Restrictions.eq(MenuWeb.PROP_MENU_WEB_PAI, dto.getSubmenu().getMenuWebPai()))
            .add(Restrictions.isNull(MenuWeb.PROP_MENU_MODULO))
            .add(Restrictions.isNull(MenuWeb.PROP_PROGRAMA_WEB))
            .createCriteria(MenuWeb.PROP_MENU_WEB_PAI)
            .add(Restrictions.eq(MenuWeb.PROP_MENU_WEB_PAI, dto.getSubmenu().getMenuWebPai().getMenuWebPai()));
        
        if(dto.getSubmenu().getCodigo() != null){
            criteria.add(Restrictions.ne(MenuWeb.PROP_CODIGO, dto.getSubmenu().getCodigo()));
        }
        
        if(((Number) criteria.uniqueResult()).longValue() > 0){
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_submenu_com_esta_descricao"));
        }
    }
}