package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.agendamento.exame.dto.QueryConsultaExamePrestadorCompetenciaDTOParam;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prestadorservico.interfaces.dto.ExamePrestadorCompetenciaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadePrestadorCompetencia;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ConsultarExamePrestadorCompetencia extends AbstractCommandTransaction<ConsultarExamePrestadorCompetencia> {

    private List<Long> codigos;
    private List<ExamePrestadorCompetencia> retorno;
    private List<ExamePrestadorCompetenciaDTO> listDTO = new ArrayList<>();
    private Date competencia;
    private Double saldoMinimo;
    private boolean retornoDTO;
    private Empresa empresaAtendimento;

    public ConsultarExamePrestadorCompetencia(List<Long> codigos, Date competencia, Double saldoMinimo) {
        this.codigos = codigos;
        this.competencia = competencia;
        this.saldoMinimo = saldoMinimo;
        this.retornoDTO = false;
    }

    public ConsultarExamePrestadorCompetencia(List<Long> codigos, Date competencia, Double saldoMinimo, boolean retornoDTO, Empresa empresaAtendimento) {
        this.codigos = codigos;
        this.competencia = competencia;
        this.saldoMinimo = saldoMinimo;
        this.retornoDTO = retornoDTO;
        this.empresaAtendimento = empresaAtendimento;
    }

    public Collection<ExamePrestadorCompetencia> getRetorno() {
        return retorno;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        QueryConsultaExamePrestadorCompetenciaDTOParam queryParam = new QueryConsultaExamePrestadorCompetenciaDTOParam();
        queryParam.setCodigoExameProcedimentoList(codigos);
        queryParam.setDataCompetencia(competencia);
        queryParam.setSaldoMinimo(saldoMinimo);
        QueryConsultaExamePrestadorCompetencia query = new QueryConsultaExamePrestadorCompetencia(queryParam);
        query.start();

        List<ExamePrestadorCompetencia> list = query.getResult();
        if (retornoDTO) {
            if (br.com.celk.util.CollectionUtils.isNotNullEmpty(list)) {
                for (ExamePrestadorCompetencia examePrestadorCompetencia : list) {
                    ExamePrestadorCompetenciaDTO dto = new ExamePrestadorCompetenciaDTO();
                    dto.setExamePrestadorCompetencia(examePrestadorCompetencia);
                    dto.setSaldoCotas(getSaldoCotas(examePrestadorCompetencia));
                    listDTO.add(dto);
                }
            }
        } else {
            retorno = list;
        }
    }

    private Double getSaldoCotas(ExamePrestadorCompetencia examePrestadorCompetencia) {
        ExameUnidadePrestadorCompetencia exameUnidadePrestadorCompetencia = LoadManager.getInstance(ExameUnidadePrestadorCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadePrestadorCompetencia.PROP_TIPO_EXAME, examePrestadorCompetencia.getTipoExame()))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadePrestadorCompetencia.PROP_PRESTADOR, examePrestadorCompetencia.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadePrestadorCompetencia.PROP_EMPRESA, empresaAtendimento))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadePrestadorCompetencia.PROP_DATA_COMPETENCIA, competencia))
                .start().getVO();

        if (exameUnidadePrestadorCompetencia != null) {
            return new Dinheiro(Coalesce.asDouble(exameUnidadePrestadorCompetencia.getTetoFinanceiro())).subtrair(Coalesce.asDouble(exameUnidadePrestadorCompetencia.getTetoFinanceiroUtilizado())).doubleValue();
        }
        return examePrestadorCompetencia != null ? examePrestadorCompetencia.getSaldoFinanceiro() : 0D;
    }

    public List<ExamePrestadorCompetenciaDTO> getListDTO() {
        return listDTO;
    }
}