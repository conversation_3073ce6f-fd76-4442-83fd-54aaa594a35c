package br.com.ksisolucoes.bo.integracao.raas.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.atendimento.raas.RaasPsiItem;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 112, crlf = "UNIX")
public class GeracaoRaasAtencaoPsicossocialProcedimentoBind implements IBindVoExport<RaasPsiItem> {
    
    @DataField(pos = 1, length = 2, paddingChar = '0', align = "R", required = true)
    private Long codigoLinhaAtencaoPsicossocialProcedimento;
    @DataField(pos = 2, length = 2, align = "R")
    private Long codigoUnidadeFederacaoAtencaoPsicossocialProcedimento;
    @DataField(pos = 3, length = 6, align = "R", pattern = "yyyyMM")
    private Date anoMesAtencaoPsicossocialProcedimento;
    @DataField(pos = 4, length = 7, paddingChar = '0', align = "R")
    private Long codigoUnidadePrestadoraServicosAtencaoPsicossocialProcedimento;
    @DataField(pos = 5, length = 15, align = "R")
    private Long cartaoNacionalSaudePacienteAtencaoPsicossocialProcedimento;
    @DataField(pos = 6, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataInicialValidadeAtencaoPsicossocialProcedimento;
    @DataField(pos = 7, length = 10, paddingChar = '0', align = "R")
    private Long codigoSigtapProcedimentoAtencaoPsicossocialProcedimento;
    @DataField(pos = 8, length = 6, align = "L")
    private String codigoCboExecutanteAtencaoPsicossocialProcedimento;
    @DataField(pos = 9, length = 15, align = "R")
    private Long numeroCnsExecutanteAtencaoPsicossocialProcedimento;
    @DataField(pos = 10, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataExecucaoProcedimentoAtencaoPsicossocialProcedimento;
    @DataField(pos = 11, length = 3, align = "R")
    private Long codigoServicoAtencaoPsicossocialProcedimento;
    @DataField(pos = 12, length = 3, paddingChar = '0', align = "R")
    private Long codigoClassificacaoAtencaoPsicossocialProcedimento;
    @DataField(pos = 13, length = 6, paddingChar = '0', align = "R")
    private Long quantidadeProcedimentoRealizadoAtencaoPsicossocialProcedimento;
    @DataField(pos = 14, length = 3, align = "L")
    private String origemInformacoesAtencaoPsicossocialProcedimento;
    @DataField(pos = 15, length = 1, align = "L")
    private String localRealizacaoExame;
    // A configuração abaixo segue o layout correto
//    @DataField(pos = 16, paddingChar = '0', length = 11, align = "L")
    @DataField(pos = 16, paddingChar = ' ', length = 11, align = "L")
    private String cpfPaciente;
    @DataField(pos = 17, length = 4, align = "L")
    private String reservadoAtencaoPsicossocialProcedimento;
    @DataField(pos = 18, length = 2, align = "L")
    private String fimCabecalhoAtencaoPsicossocialProcedimento;

    @Override
    public void buildProperties(RaasPsiItem vo) {
        codigoLinhaAtencaoPsicossocialProcedimento = vo.getLinha();
        codigoUnidadeFederacaoAtencaoPsicossocialProcedimento = vo.getUnidadeFederacao();
        anoMesAtencaoPsicossocialProcedimento = vo.getCompetencia();
        codigoUnidadePrestadoraServicosAtencaoPsicossocialProcedimento = vo.getUnidadePrestadoraServico();
        cartaoNacionalSaudePacienteAtencaoPsicossocialProcedimento = vo.getCartaoNacionalSaude();
        dataInicialValidadeAtencaoPsicossocialProcedimento = vo.getDataInicioValidade();
        codigoSigtapProcedimentoAtencaoPsicossocialProcedimento = vo.getCodigoProcedimento();
        codigoCboExecutanteAtencaoPsicossocialProcedimento = vo.getCodigoCboExecutante();
        numeroCnsExecutanteAtencaoPsicossocialProcedimento = vo.getCnsExecutante();
        dataExecucaoProcedimentoAtencaoPsicossocialProcedimento = vo.getDataExecucaoProcedimento();
        codigoServicoAtencaoPsicossocialProcedimento = vo.getServico();
        codigoClassificacaoAtencaoPsicossocialProcedimento = vo.getClassificacao();
        quantidadeProcedimentoRealizadoAtencaoPsicossocialProcedimento = vo.getQuantidadeRealizada();
        origemInformacoesAtencaoPsicossocialProcedimento = vo.getOrigemInformacoes();
        localRealizacaoExame = vo.getLocalRealizacao();
//        cpfPaciente = vo.getCpfPaciente();
        cpfPaciente = "           ";
//        fimCabecalhoAtencaoPsicossocialProcedimento = System.lineSeparator();
    }
    
}
