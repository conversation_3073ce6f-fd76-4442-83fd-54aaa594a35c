package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia.TipoOcorrencia;

/**
 * <AUTHOR>
 */
public class ConfirmarContatoSolicitacaoAgendamentoSms extends AbstractCommandTransaction {

    private Long codigoSolicitacao;
    private String observacao;

    public ConfirmarContatoSolicitacaoAgendamentoSms(Long codigoSolicitacao, String observacao) {
        this.codigoSolicitacao = codigoSolicitacao;
        this.observacao = observacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        SolicitacaoAgendamento solicitacaoAgendamento = (SolicitacaoAgendamento) getSession().get(SolicitacaoAgendamento.class, codigoSolicitacao);

        solicitacaoAgendamento.setDataConfirmacaoUsuario(DataUtil.getDataAtual());
        solicitacaoAgendamento.setDataUltimoContato(DataUtil.getDataAtual());
        solicitacaoAgendamento.setSituacaoContato(SolicitacaoAgendamento.SituacaoContato.CONTACTADO_VIA_SMS.value());

        BOFactory.getBO(CadastroFacade.class).save(solicitacaoAgendamento);

        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(TipoOcorrencia.SOLICITACAO,
                Bundle.getStringApplication("rotulo_contato_paciente_confirmado_sms") + " - " + observacao,
                solicitacaoAgendamento);
    }

}
