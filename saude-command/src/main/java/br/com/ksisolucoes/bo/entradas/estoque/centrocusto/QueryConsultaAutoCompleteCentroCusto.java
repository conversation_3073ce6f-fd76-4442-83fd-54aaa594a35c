package br.com.ksisolucoes.bo.entradas.estoque.centrocusto;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaCentroCustoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.entradas.estoque.CentroCusto;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAutoCompleteCentroCusto extends CommandQueryPager<QueryConsultaCentroCustoDTOParam> {

    private QueryConsultaCentroCustoDTOParam param;

    public QueryConsultaAutoCompleteCentroCusto(QueryConsultaCentroCustoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("c.codigo", true);
        hql.addToSelect("c.descricao", true);
        
        hql.setTypeSelect(CentroCusto.class.getName());
        hql.addToFrom("CentroCusto c");
        
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("c."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("c.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
