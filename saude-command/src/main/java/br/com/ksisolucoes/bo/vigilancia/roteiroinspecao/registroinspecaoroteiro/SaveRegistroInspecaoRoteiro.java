package br.com.ksisolucoes.bo.vigilancia.roteiroinspecao.registroinspecaoroteiro;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoRoteiro;

/**
 *
 * <AUTHOR>
 */
public class SaveRegistroInspecaoRoteiro extends SaveVO<RegistroInspecaoRoteiro> {

    public SaveRegistroInspecaoRoteiro(RegistroInspecaoRoteiro vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getStatus() == null) {
            this.vo.setStatus(RegistroInspecaoRoteiro.Status.EM_INSPECAO.value());
        }
    }

}
