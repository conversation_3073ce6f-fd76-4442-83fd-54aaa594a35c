package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.OcorrenciaRequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.hibernate.criterion.Restrictions;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class CancelarFinalizarRequerimentoVigilancia extends AbstractCommandTransaction<CancelarFinalizarRequerimentoVigilancia> {

    private final RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;

    public CancelarFinalizarRequerimentoVigilancia(RequerimentoVigilanciaCancelamentoFinalizacaoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getRequerimentoVigilancia() != null) {
            dto.getRequerimentoVigilancia().setSituacao(dto.getSituacao().value());
            dto.getRequerimentoVigilancia().setDataIntegracao(null);


            if (RequerimentoVigilancia.Situacao.CANCELADO.value().equals(dto.getSituacao().value())) {
                if (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                    BOFactory.getBO(VigilanciaFacade.class).atualizarTalonarioADisponivel(dto.getRequerimentoVigilancia());
                } else if (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_TALIDOMIDA.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                    BOFactory.getBO(VigilanciaFacade.class).atualizarTalonarioTalidomidaDisponivel(dto.getRequerimentoVigilancia());
                } else if (TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                    BOFactory.getBO(VigilanciaFacade.class).cancelarRequerimentoDenuncia(dto.getRequerimentoVigilancia());
                } else if (TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento()) ||
                        TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento()) ||
                        TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento()) ||
                        TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())){
                    BOFactory.getBO(VigilanciaFacade.class).cancelarAtualizarDataAlvaraSetores(dto.getRequerimentoVigilancia());
                }


                dto.getRequerimentoVigilancia().setMotivoCancelamento(dto.getMotivo());
                requerimentoVigilancia = BOFactory.save(dto.getRequerimentoVigilancia());
                cancelarFinanceiro(requerimentoVigilancia);
                BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("requerimento_cancelado_motivo_X", dto.getMotivo()), requerimentoVigilancia, null);

            } else if (RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(dto.getSituacao().value())
                    || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(dto.getSituacao().value())
                    || RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(dto.getSituacao().value())) {

                if (dto.getDataFinalizacao() != null) {
                    dto.getRequerimentoVigilancia().setDataFinalizacao(dto.getDataFinalizacao());
                }

                if (RequerimentoVigilancia.Origem.INTERNO.value().equals(dto.getRequerimentoVigilancia().getOrigem())) {
                    dto.getRequerimentoVigilancia().setSituacaoAprovacao(null);

                } else if (RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(dto.getSituacao().value())
                        || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(dto.getSituacao().value())) {
                    dto.getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
                }

                if(dto.getFlagDesobrigacaoAlvara() != null) {
                    dto.getRequerimentoVigilancia().setFlagDesobrigacaoAlvara(dto.getFlagDesobrigacaoAlvara());
                }

                dto.getRequerimentoVigilancia().setMotivoFinalizacao(dto.getMotivo());
                requerimentoVigilancia = BOFactory.save(dto.getRequerimentoVigilancia());

                if (RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(dto.getSituacao().value())) {
                    List<OcorrenciaRequerimentoVigilancia> ocorrencias = LoadManager.getInstance(OcorrenciaRequerimentoVigilancia.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(OcorrenciaRequerimentoVigilancia.PROP_REQUERIMENTO_VIGILANCIA, dto.getRequerimentoVigilancia()))
                            .addParameter(new QueryCustom.QueryCustomParameter(OcorrenciaRequerimentoVigilancia.PROP_DESCRICAO, Bundle.getStringApplication("msg_situacao_requerimento_revertido_apos_finalizar")))
                            .addSorter(new QueryCustom.QueryCustomSorter(OcorrenciaRequerimentoVigilancia.PROP_DATA_OCORRENCIA, "desc"))
                            .start().getList();
                    if (ocorrencias != null && !ocorrencias.isEmpty()) {
                        dto.setMotivo(Bundle.getStringApplication("msg_situacao_requerimento_finalizado_apos_reversao") + "\n" + dto.getMotivo());
                    }
                    BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("requerimento_deferido_motivo_X_data_finalizacao_X",
                            dto.getMotivo() != null ? dto.getMotivo() : "Não informado", Data.formatar(dto.getDataFinalizacao())), requerimentoVigilancia, null);

                    if (TipoSolicitacao.TipoDocumento.ALTERACAO_RESPONSABILIDADE_LEGAL.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoRepresentanteLegal(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALTERACAO_ATIVIDADE_ECONOMICA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoAtividadeEconomica(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.BAIXA_RESPONSABILIDADE_TECNICA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoBaixaResponsabilidadeTecnica(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ENTRADA_RESPONSABILIDADE_TECNICA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoInclusaoResponsabilidadeTecnica(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(requerimentoVigilancia.getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(requerimentoVigilancia.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento())) {

                        if (dto.getAtividades() != null && !dto.getAtividades().isEmpty()) {
                            VigilanciaHelper.saveEstabelecimentoAtividadeLicenciada(dto.getAtividades());

                            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_atividade_licenciaveis",
                                    VigilanciaHelper.getOcorrenciasAtividadesLicenciaveis(dto.getAtividades(), requerimentoVigilancia)), requerimentoVigilancia, null);
                        }

                        BOFactory.getBO(VigilanciaFacade.class).atualizarAlvaraEstabelecimento(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALTERACAO_RAZAO_SOCIAL.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarRazaoSocialEstabelecimento(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALTERACAO_ENDERECO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarEnderecoEstabelecimento(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.BAIXA_ESTABELECIMENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoBaixaEstabelecimento(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).deferirRequerimentoBaixaVeiculo(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALVARA_PARTICIPANTE_EVENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAlvaraFiscais(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoCadastroEvento(requerimentoVigilancia, dto);
                    } else if (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value().equals(requerimentoVigilancia.getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_B.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.RECEITA_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.DECLARACAO_CARTORIO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_CARTORIO_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_ISENCAO_TAXAS.value().equals(requerimentoVigilancia.getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_OUTROS.value().equals(requerimentoVigilancia.getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.DECLARACAO_VISA_PRODUTOS.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DECLARACAO_VISA_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.EXUMACAO_RESTOS_MORTAIS_FINALIZACAO);
                    } else if (TipoSolicitacao.TipoDocumento.PEDIDO_DOCUMENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.PEDIDO_DOCUMENTO_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.VACINACAO_EXTRAMURO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.VACINACAO_EXTRAMURO_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.CERTIDAO_NADA_CONSTA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.CERTIDAO_NADACONSTA_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.ABERTURA_LIVRO_CONTROLE.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.ABERTURA_LIVRO_CONTROLE_ANALISE);
                    } else if (TipoSolicitacao.TipoDocumento.FECHAMENTO_LIVRO_CONTROLE.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.FECHAMENTO_LIVRO_CONTROLE_DEFERIMENTO);
                    } else if (TipoSolicitacao.TipoDocumento.TREINAMENTOS_ALIMENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarCredenciamentoEstabelecimento(requerimentoVigilancia);
                        BOFactory.getBO(VigilanciaFacade.class).atualizarRequerimentoCredenciamento(requerimentoVigilancia, dto);
                    } else if (TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoDenuncia(requerimentoVigilancia);
                    } else if (TipoSolicitacao.TipoDocumento.AUTORIZACAO_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarAutorizacaoSanitariaEstabelecimento(requerimentoVigilancia);
                    }

                } else if (RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(dto.getSituacao().value())) {
                    BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("requerimento_indeferido_motivo_X_data_finalizacao_X",
                            dto.getMotivo() != null ? dto.getMotivo() : "Não informado", Data.formatar(dto.getDataFinalizacao())), requerimentoVigilancia, null);
                    if (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_A.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarTalonarioADisponivel(dto.getRequerimentoVigilancia());
                    }
                    if (TipoSolicitacao.TipoDocumento.REQUISICAO_RECEITUARIO_TALIDOMIDA.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).atualizarTalonarioTalidomidaDisponivel(dto.getRequerimentoVigilancia());
                    }
                    if (TipoSolicitacao.TipoDocumento.ALVARA_CADASTRO_EVENTO.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).finalizarRequerimentoCadastroEvento(requerimentoVigilancia, dto);
                    }
                } else {
                    if (dto.getDataFinalizacao() != null) {
                        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("requerimento_finalizado_motivo_X_data_finalizacao_X",
                                dto.getMotivo(), Data.formatar(dto.getDataFinalizacao())), requerimentoVigilancia, null);
                    } else {
                        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("requerimento_finalizado_motivo_X",
                                dto.getMotivo()), requerimentoVigilancia, null);
                    }
                }

                if (dto.getPnlRequerimentoVigilanciaAnexoDTO() != null) {
                    BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList(), dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoExcluidoDTOList(), false, true);
                }

                if (RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(dto.getSituacao().value())
                        || RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(dto.getSituacao().value())) {
                    anexarDocumento();
                    if (TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.EXUMACAO_RESTOS_MORTAIS_FINALIZACAO);
                    }
                }
            }
            try {
                if (requerimentoVigilancia == null) {
                    if (TipoSolicitacao.TipoDocumento.PROJETO_BASICO_ARQUITETURA.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.HABITE_SE_DECLARATORIO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.ANALISE_PROJETO_HIDROSSANITARIO_DECLARATORIO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                            || TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value().equals(dto.getRequerimentoVigilancia().getTipoDocumento())
                    ) {
                        requerimentoVigilancia = BOFactory.save(dto.getRequerimentoVigilancia());
                    } else {
                        requerimentoVigilancia = dto.getRequerimentoVigilancia();
                    }
                }
                BOFactory.getBO(VigilanciaFacade.class).enviarEmailSituacaoRequerimentoVigilancia(requerimentoVigilancia, false);
            } catch (ValidacaoException | DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
    }

    private void cancelarFinanceiro(RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        List<VigilanciaFinanceiro> vigilanciaFinanceiroList = getVigilanciaFinanceiro(requerimentoVigilancia);
        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            for (VigilanciaFinanceiro vigilanciaFinanceiro : vigilanciaFinanceiroList) {
                if (RepositoryComponentDefault.SIM_LONG.equals(vigilanciaFinanceiro.getIsento())) continue;
                vigilanciaFinanceiro.setDescricaoMotivoCancelamento(dto.getMotivo());
                BOFactory.getBO(VigilanciaFacade.class).cancelarVigilanciaFinanceiro(vigilanciaFinanceiro, false, true);
            }
        }
    }

    private List<VigilanciaFinanceiro> getVigilanciaFinanceiro(RequerimentoVigilancia requerimentoVigilancia) {
        return LoadManager.getInstance(VigilanciaFinanceiro.class)
                .addProperties(new HQLProperties(VigilanciaFinanceiro.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, VigilanciaFinanceiro.PROP_ANEXO_BOLETO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaFinanceiro.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaFinanceiro.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, VigilanciaFinanceiro.Status.CANCELADO.value()))
                .start().getList();
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    private void anexarDocumento() throws DAOException, ValidacaoException {
        try {
            if (requerimentoVigilancia != null && requerimentoVigilancia.getTipoDocumento() != null) {
                DataReport dataReport = null;
                Long origemArquivo = null;
                String descricaoAnexo = "";

                if (requerimentoVigilancia.getTipoDocumento() != null) {
                    descricaoAnexo = TipoSolicitacao.TipoDocumento.valueOf(requerimentoVigilancia.getTipoDocumento()).descricao();
                }

                switch (TipoSolicitacao.TipoDocumento.valueOf(requerimentoVigilancia.getTipoDocumento())) {
                    case EXUMACAO_RESTOS_MORTAIS:
                        RelatorioAutorizacaoExumacaoDTOParam paramExumacao = new RelatorioAutorizacaoExumacaoDTOParam();
                        paramExumacao.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(paramExumacao);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.REQUERIMENTO_EXUMACAO_RESTOS_MORTAIS.value();
                        break;
                    case ABERTURA_LIVRO_CONTROLE:
                        RelatorioRequerimentoLivroVigilanciaTermoDTOParam paramTermoAbertura = new RelatorioRequerimentoLivroVigilanciaTermoDTOParam();
                        paramTermoAbertura.setRequerimentoVigilancia(requerimentoVigilancia);
                        paramTermoAbertura.setAberturaLivro(true);
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoLivroVigilanciaTermo(paramTermoAbertura);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ABERTURA_FECHAMENTO_LIVRO_CONTROLE.value();
                        break;
                    case FECHAMENTO_LIVRO_CONTROLE:
                        RelatorioRequerimentoLivroVigilanciaTermoDTOParam paramTermoFechamento = new RelatorioRequerimentoLivroVigilanciaTermoDTOParam();
                        paramTermoFechamento.setRequerimentoVigilancia(requerimentoVigilancia);
                        paramTermoFechamento.setAberturaLivro(false);
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoLivroVigilanciaTermo(paramTermoFechamento);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ABERTURA_FECHAMENTO_LIVRO_CONTROLE.value();
                        break;
                    case CERTIDAO_NADA_CONSTA:
                        RelatorioCertidaoNadaConstaDTOParam paramNadaConsta = new RelatorioCertidaoNadaConstaDTOParam();
                        paramNadaConsta.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioCertidaoNadaConsta(paramNadaConsta);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.CERTIDAO_NADA_CONSTA.value();
                        break;
                    case ALVARA_INICIAL:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraInicial(getImpressaoAlvaraDTOParam(requerimentoVigilancia, true));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALVARA_INICIAL.value();
                        break;
                    case REVALIDACAO_LICENCA_SANITARIA:
                    case LICENCA_SANITARIA:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoLicencaSanitaria(getImpressaoAlvaraDTOParam(requerimentoVigilancia, true));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALVARA_INICIAL.value();
                        break;
                    case AUTORIZACAO_SANITARIA:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAutorizacaoSanitaria(requerimentoVigilancia.getCodigo(), dto.getUrlAlvara());
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.AUTORIZACAO_SANITARIA.value();
                        break;
                    case ALVARA_REVALIDACAO:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraInicial(getImpressaoAlvaraDTOParam(requerimentoVigilancia, true));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALVARA_REVALIDACAO.value();
                        break;
                    case ALVARA_PARTICIPANTE_EVENTO:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraEvento(requerimentoVigilancia.getCodigo(), dto.getUrlAlvara());
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALVARA_EVENTO.value();
                        break;
                    case REQUISICAO_RECEITUARIO_A:
                        RelatorioRequerimentoReceitaDTOParam paramReceitaA = new RelatorioRequerimentoReceitaDTOParam();
                        paramReceitaA.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        paramReceitaA.setTipoReceita(RelatorioRequerimentoReceitaDTOParam.TipoReceita.RECEITA_A);
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoReceita(paramReceitaA);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.REQUERIMENTO_RECEITA_A.value();
                        break;
                    case REQUISICAO_RECEITUARIO_TALIDOMIDA:
                        RelatorioRequerimentoReceitaDTOParam paramReceitaTalidomida = new RelatorioRequerimentoReceitaDTOParam();
                        paramReceitaTalidomida.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        paramReceitaTalidomida.setTipoReceita(RelatorioRequerimentoReceitaDTOParam.TipoReceita.RECEITA_TALIDOMIDA);
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoReceita(paramReceitaTalidomida);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.REQUERIMENTO_RECEITA_TALIDOMIDA.value();
                        break;
                    case REQUISICAO_RECEITUARIO_B:
                        RelatorioRequerimentoReceitaDTOParam paramReceitaB = new RelatorioRequerimentoReceitaDTOParam();
                        paramReceitaB.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        paramReceitaB.setTipoReceita(RelatorioRequerimentoReceitaDTOParam.TipoReceita.RECEITA_B);
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoReceita(paramReceitaB);
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.REQUERIMENTO_RECEITA_B.value();
                        break;
                    case BAIXA_RESPONSABILIDADE_TECNICA:
                        RelatorioAutorizacaoBaixaResponsabilidadeDTOParam paramBaixaResponsabilidade = new RelatorioAutorizacaoBaixaResponsabilidadeDTOParam();
                        paramBaixaResponsabilidade.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                        paramBaixaResponsabilidade.setEstabelecimento(requerimentoVigilancia.getEstabelecimento());
                        paramBaixaResponsabilidade.setRequerimentoVigilancia(requerimentoVigilancia);
                        if (VigilanciaHelper.existAlvaraSistema(requerimentoVigilancia.getEstabelecimento())) {
                            dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioImprimirBaixaResponsabilidadeRevalidacaoAlvara(paramBaixaResponsabilidade);
                        } else {
                            dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioImprimirBaixaResponsabilidadeAlvaraInicial(paramBaixaResponsabilidade);
                        }
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.BAIXA_RESPONSABILIDADE_TECNICA.value();
                        break;
                    case ALTERACAO_RESPONSABILIDADE_LEGAL:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraInicial(getImpressaoAlvaraDTOParam(requerimentoVigilancia, false));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALTERACAO_RESPONSABILIDADE_LEGAL.value();
                        break;
                    case ALTERACAO_ATIVIDADE_ECONOMICA:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraInicial(getImpressaoAlvaraDTOParam(requerimentoVigilancia, false));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ALTERACAO_ATIVIDADE_ECONOMICA.value();
                        break;
                    case ENTRADA_RESPONSABILIDADE_TECNICA:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoAlvaraInicial(getImpressaoAlvaraDTOParam(requerimentoVigilancia, false));
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.ENTRADA_RESPONSABILIDADE_TECNICA.value();
                        break;
                    case LICENCA_TRANSPORTE:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).impressaoRequerimentoLicencaTransporte(requerimentoVigilancia.getCodigo(), dto.getUrlAlvara());
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.LICENCA_TRANSPORTE.value();
                        break;
                    case BAIXA_ESTABELECIMENTO:
                        dataReport = BOFactory.getBO(VigilanciaReportFacade.class).impressaoDeclaracaoBaixaEstabelecimento(requerimentoVigilancia.getCodigo());
                        origemArquivo = GerenciadorArquivo.OrigemArquivo.BAIXA_ESTABELECIMENTO.value();
                        break;
                    default:
                        dataReport = null;
                }

                if (dataReport != null && dataReport.getJasperPrint() != null && CollectionUtils.isNotNullEmpty(dataReport.getJasperPrint().getPages())) {
                    List<RequerimentoVigilanciaAnexo> anexoList = getSession().createCriteria(RequerimentoVigilanciaAnexo.class)
                            .add(Restrictions.eq(RequerimentoVigilanciaAnexo.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                            .add(Restrictions.eq(RequerimentoVigilanciaAnexo.PROP_STATUS, RequerimentoVigilanciaAnexo.Status.CADASTRADO.value()))
                            .list();

                    List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
                    RequerimentoVigilanciaAnexoDTO anexoDTO;
                    if (CollectionUtils.isEmpty(anexoList)) {
                        for (RequerimentoVigilanciaAnexo rva : anexoList) {
                            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
                            anexoDTO.setDescricaoAnexo(rva.getDescricao());
                            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
                            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

                            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
                        }
                    }

                    File file = File.createTempFile(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase(), ".pdf");
                    JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

                    anexoDTO = new RequerimentoVigilanciaAnexoDTO();
                    anexoDTO.setFile(file);
                    anexoDTO.setOrigem(origemArquivo);
                    anexoDTO.setNomeArquivoOriginal(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase() + ".pdf");
                    anexoDTO.setDescricaoAnexo(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase());

                    requerimentoVigilanciaAnexoDTOList.add(anexoDTO);

                    BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, requerimentoVigilanciaAnexoDTOList, new ArrayList<RequerimentoVigilanciaAnexoDTO>(), false);
                }
            }
        } catch (ReportException | JRException | IOException ex) {
            Logger.getLogger(CancelarFinalizarRequerimentoVigilancia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private ImpressaoAlvaraDTOParam getImpressaoAlvaraDTOParam(RequerimentoVigilancia requerimentoVigilancia, boolean setarRequerimentoVigilancia) {
        ImpressaoAlvaraDTOParam dtoParam = new ImpressaoAlvaraDTOParam();
        if (setarRequerimentoVigilancia) dtoParam.setRequerimentoVigilancia(requerimentoVigilancia);

        dtoParam.setEstabelecimento(requerimentoVigilancia.getEstabelecimento());
        dtoParam.setChaveQrCode(requerimentoVigilancia.getChaveQRcode());
        return dtoParam;
    }
}