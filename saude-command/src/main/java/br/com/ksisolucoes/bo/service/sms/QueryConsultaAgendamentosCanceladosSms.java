package br.com.ksisolucoes.bo.service.sms;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import org.hibernate.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAgendamentosCanceladosSms extends CommandQuery<QueryConsultaAgendamentosCanceladosSms> {

    private Collection<AgendaGradeAtendimentoHorario> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AgendaGradeAtendimentoHorario.class.getName());

        hql.addToSelect("agah.codigo", true);
        hql.addToSelect("agah.dataAgendamento", true);
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        hql.addToSelect("uc.celular", "usuarioCadsus.celular");
        hql.addToSelect("la.descricao", "localAgendamento.descricao");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");
        hql.addToSelect("tp.descricaoAbreviada", "tipoProcedimento.descricaoAbreviada");

        hql.addToFrom("AgendaGradeAtendimentoHorario agah"
                + " join agah.usuarioCadsus uc"
                + " join agah.tipoProcedimento tp"
                + " join agah.localAgendamento la");

        hql.addToWhereWhithAnd("agah.status = ", AgendaGradeAtendimentoHorario.STATUS_CANCELADO);
        hql.addToWhereWhithAnd("uc.celular is not null");
        hql.addToWhereWhithAnd("tp.enviaSms = ", RepositoryComponentDefault.SIM);

        hql.addToWhereWhithAnd("(not exists(select 1"
                             + "              from SmsControleIntegracao sci"
                             + "             where sci.agendaGradeAtendimentoHorario = agah"
                             + "               and sci.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.CANCELAMENTO_AGENDAMENTO.value()
                             + "               and sci.statusSms <> " + SmsControleIntegracao.StatusSms.REENVIAR.value() + ") "
                             + " or exists(select 1"
                             + "             from SmsControleIntegracao sci"
                             + "            where sci.agendaGradeAtendimentoHorario = agah"
                             + "              and sci.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.CANCELAMENTO_AGENDAMENTO.value()
                             + "              and sci.statusSms = " + SmsControleIntegracao.StatusSms.REENVIAR.value() + "))");

        hql.addToWhereWhithAnd("(agah.solicitacaoAgendamento is null"
                             + " or exists(select 1"
                             + "             from SmsControleIntegracao sci"
                             + "            where sci.agendaGradeAtendimentoHorario = agah"
                             + "              and sci.tipoMensagem in("
                                                                        + SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO.value()
                                                                        + ", "
                                                                        + SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO_LOCAL.value()
                                                                        + ", "
                                                                        + SmsControleIntegracao.TipoMensagem.REMANEJAMENTO_AGENDAMENTO.value()
                                                                        + ")"
                             + "              and sci.statusSms in("
                                                                    + SmsControleIntegracao.StatusSms.ENCAMINHADO.value()
                                                                    + ", "
                                                                    + SmsControleIntegracao.StatusSms.ENVIADO.value()
                                                                    + ", "
                                                                    + SmsControleIntegracao.StatusSms.REENVIAR.value()
                                                                    + ")))");

        hql.addToWhereWhithAnd("agah.dataAgendamento >= :dataAtual");
        hql.addToWhereWhithAnd("agah.flagCanceladoSms = ", RepositoryComponentDefault.NAO_LONG);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        Date dataAtual = DataUtil.getDataAtual();
        query.setDate("dataAtual", dataAtual);
    }

    @Override
    public Collection getResult() {
        return list;
    }
}
