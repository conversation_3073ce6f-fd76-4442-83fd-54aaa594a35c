/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.enderecousuariocadsus;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.EnderecoUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.dao.HQLHelper.getStringQuery;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaEnderecoUsuarioCadsus extends CommandQueryPager<QueryPagerConsultaEnderecoUsuarioCadsus> {

    private EnderecoUsuarioCadsusDTOParam param;

    public QueryPagerConsultaEnderecoUsuarioCadsus(EnderecoUsuarioCadsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        ConsultaEnderecoUsuarioCadsusInitialProperties.initHql(hql, param);
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
        if(param.getEndereco() != null && !param.getEndereco().isEmpty()){
            query.setParameter("enderecoKeyword", getStringQuery(param.getEndereco()));
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
