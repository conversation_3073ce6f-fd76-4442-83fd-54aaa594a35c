/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryPagerConsultaAgendamentosAcompanhamento extends CommandQueryPager<QueryPagerConsultaAgendamentosAcompanhamento> {


    private AgendaGradeAtendimentoDTOParam param;

    public QueryPagerConsultaAgendamentosAcompanhamento(AgendaGradeAtendimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AgendaGradeAtendimentoHorarioDTO.class.getName());

        hql.addToSelect(" max(solicitacaoAgendamento.dataUltimoContato) ", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataUltimoContato");

        if (getDataPaging().getType().equals(DataPaging.Type.ALVO_LIST)) {
            HQLHelper hqlSubDataAgendamento = hql.getNewInstanceSubQuery();

            hqlSubDataAgendamento.addToSelect("min(agah.dataAgendamento)");

            hqlSubDataAgendamento.addToFrom("AgendaGradeAtendimentoHorario agah"
                    + " left join agah.solicitacaoAgendamento sa");

            hqlSubDataAgendamento.addToWhereWhithAnd("sa.codigo = solicitacaoAgendamento.codigo");
            hqlSubDataAgendamento.addToWhereWhithAnd("agah.status = :statusAgendado");
            if (AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO.equals(param.getTipoData())) {
                hqlSubDataAgendamento.addToWhereWhithAnd(" agah.dataAgendamento ", Data.adjustRangeHour(param.getDatePeriod()));
            } else {
                hqlSubDataAgendamento.addToWhereWhithAnd(" agah.dataCadastro ", Data.adjustRangeHour(param.getDatePeriod()));
            }

            hql.addToSelect("(" + hqlSubDataAgendamento.getQuery() + ")", "agendaGradeAtendimentoHorario.dataAgendamento");

            HQLHelper hqlSubStatus = hql.getNewInstanceSubQuery();

            hqlSubStatus.addToSelect("min(agah.status)");

            hqlSubStatus.addToFrom("AgendaGradeAtendimentoHorario agah"
                    + " left join agah.solicitacaoAgendamento sa");

            hqlSubStatus.addToWhereWhithAnd("sa.codigo = solicitacaoAgendamento.codigo");
            hqlSubStatus.addToWhereWhithAnd("agah.status in :statusList");
            if (AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO.equals(param.getTipoData())) {
                hqlSubStatus.addToWhereWhithAnd(" agah.dataAgendamento ", Data.adjustRangeHour(param.getDatePeriod()));
            } else {
                hqlSubStatus.addToWhereWhithAnd(" agah.dataCadastro ", Data.adjustRangeHour(param.getDatePeriod()));
            }
            hql.addToSelect("(" + hqlSubStatus.getQuery() + ")", "agendaGradeAtendimentoHorario.status");
        }

        hql.addToSelectAndGroup("agendaGradeAtendimentoHorario.nomePaciente", "agendaGradeAtendimentoHorario.nomePaciente");
        hql.addToSelectAndGroup("usuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.codigo");
        hql.addToSelectAndGroup("usuarioCadsus.referencia", "agendaGradeAtendimentoHorario.usuarioCadsus.referencia");
        hql.addToSelectAndGroup("usuarioCadsus.nome", "agendaGradeAtendimentoHorario.usuarioCadsus.nome");
        hql.addToSelectAndGroup("usuarioCadsus.apelido", "agendaGradeAtendimentoHorario.usuarioCadsus.apelido");
        hql.addToSelectAndGroup("usuarioCadsus.utilizaNomeSocial", "agendaGradeAtendimentoHorario.usuarioCadsus.utilizaNomeSocial");

        hql.addToSelectAndGroup("tipoProcedimento.codigo", "agendaGradeAtendimentoHorario.tipoProcedimento.codigo");
        hql.addToSelectAndGroup("tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.tipoProcedimento.descricao");

        hql.addToSelectAndGroup("solicitacaoAgendamento.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.codigo");
        hql.addToSelectAndGroup("solicitacaoAgendamento.prioridade", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.prioridade");
        hql.addToSelectAndGroup("solicitacaoAgendamento.dataSolicitacao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao");

        hql.addToSelectAndGroup("unidadeExecutante.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.unidadeExecutante.codigo");
        hql.addToSelectAndGroup("unidadeExecutante.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.unidadeExecutante.descricao");

        hql.addToSelectAndGroup("empresaOrigem.codigo", "agendaGradeAtendimentoHorario.empresaOrigem.codigo");
        hql.addToSelectAndGroup("empresaOrigem.descricao", "agendaGradeAtendimentoHorario.empresaOrigem.descricao");

        if (param.getUnidadeReferencia() != null) {
            hql.addToSelectAndGroup("coalesce(empresaEquipe.descricao, coalesce(empresaEndereco.descricao, enderecoEstrEquipeMicroAreaEquipeProfEquipeEmpr.descricao))", "unidadeReferencia");

            hql.addToFrom(" AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario "
                    + " right join agendaGradeAtendimentoHorario.solicitacaoAgendamento solicitacaoAgendamento "
                    + " left join agendaGradeAtendimentoHorario.empresaOrigem empresaOrigem "
                    + " left join agendaGradeAtendimentoHorario.tipoProcedimento tipoProcedimento "
                    + " left join agendaGradeAtendimentoHorario.usuarioCadsus usuarioCadsus "
                    + " left join solicitacaoAgendamento.unidadeExecutante unidadeExecutante "
                    + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio "
                    + " left join enderecoDomicilio.equipeMicroArea enderecoDomicilioEquipeMicroArea"
                    + " left join enderecoDomicilio.cidade enderecoDomicilioCidade "
                    + " left join usuarioCadsus.enderecoUsuarioCadsus enderecoUsuarioCadsus "
                    + " left join enderecoUsuarioCadsus.enderecoEstruturado enderecoEstruturado "
                    + " left join enderecoEstruturado.equipeMicroArea enderecoEstruturadoEquipeMicroArea "
                    + " left join enderecoEstruturadoEquipeMicroArea.equipeProfissional enderecoEstrEquipeMicroAreaEquipeProf "
                    + " left join enderecoEstrEquipeMicroAreaEquipeProf.equipe enderecoEstrEquipeMicroAreaEquipeProfEquipe "
                    + " left join enderecoEstrEquipeMicroAreaEquipeProfEquipe.empresa enderecoEstrEquipeMicroAreaEquipeProfEquipeEmpr "
                    + " left join usuarioCadsus.equipe usuarioCadsusEquipe "
                    + " left join usuarioCadsusEquipe.empresa usuarioCadsusEquipeEmpresa"
                    + " LEFT JOIN enderecoUsuarioCadsus.empresa empresaEndereco "
                    + " LEFT JOIN enderecoDomicilioEquipeMicroArea.empresa empresaEquipe"
            );
        }else{
            hql.addToFrom(" AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario "
                    + " right join agendaGradeAtendimentoHorario.solicitacaoAgendamento solicitacaoAgendamento "
                    + " left join agendaGradeAtendimentoHorario.empresaOrigem empresaOrigem "
                    + " left join agendaGradeAtendimentoHorario.tipoProcedimento tipoProcedimento "
                    + " left join agendaGradeAtendimentoHorario.usuarioCadsus usuarioCadsus "
                    + " left join solicitacaoAgendamento.unidadeExecutante unidadeExecutante ");
            }

        if (param.getDatePeriod() != null) {
            if (AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO.equals(param.getTipoData())) {
                hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.dataAgendamento ", Data.adjustRangeHour(param.getDatePeriod()));
            } else {
                hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.dataCadastro ", Data.adjustRangeHour(param.getDatePeriod()));
            }
        }

        hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.agendaGradeAtendimentoPrincipal is null ");
        hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.solicitacaoAgendamento is not null ");

        hql.addToWhereWhithAnd(" solicitacaoAgendamento.situacaoContato = ", SolicitacaoAgendamento.SituacaoContato.NAO_CONTACTADO.value());

        hql.addToWhereWhithAnd(" usuarioCadsus.referencia = ", param.getReferencia());

        if (param.getNomeUsuarioCadsus() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" agendaGradeAtendimentoHorario.nomePaciente", param.getNomeUsuarioCadsus(), true)
                    + " OR " + hql.getConsultaLiked(" usuarioCadsus.nome", param.getNomeUsuarioCadsus(), true)
                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("usuarioCadsus.apelido", param.getNomeUsuarioCadsus(), true) + "))");
        }

        if(param.getCns() != null) {
            hql.addToWhereWhithAnd("(select ucc.numeroCartao from UsuarioCadsusCns ucc where ucc.usuarioCadsus = usuarioCadsus.codigo and ucc.numeroCartao = :codigoCns) = ", param.getCns());
        }

        if (param.getDataNascimento() != null) {
            hql.addToWhereWhithAnd(" usuarioCadsus.dataNascimento = ", param.getDataNascimento());
        }

        if (param.getSituacao() == null) {
            // GARANTE PARA QUANDO POSSUIR UM AGENDAMENTO QUE FOI BAIXADO E A DATA DESTE AGENDAMENTO É MENOR QUE A DATA ATUAL E NÃO FOI FEITO CONTATO NÃO LISTAR;
            hql.addToWhereWhithAnd("((agendaGradeAtendimentoHorario.status = " + AgendaGradeAtendimentoHorario.STATUS_AGENDADO
                    + ") OR (agendaGradeAtendimentoHorario.status = " + AgendaGradeAtendimentoHorario.STATUS_CANCELADO
                    + " AND (coalesce(agendaGradeAtendimentoHorario.dataCancelamento, current_timestamp) >= :dataInicio"
                    + " OR NOT EXISTS(SELECT 1 FROM AgendaGradeAtendimentoHorario agah "
                    + " LEFT JOIN agah.solicitacaoAgendamento sa "
                    + " WHERE sa.codigo = solicitacaoAgendamento.codigo AND agah.status = " + AgendaGradeAtendimentoHorario.STATUS_AGENDADO + "))))");

        } else {
            hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.status = ", param.getSituacao());

            if (AgendaGradeAtendimentoHorario.STATUS_CANCELADO.equals(param.getSituacao())) {
                hql.addToWhereWhithAnd("not exists(SELECT 1 FROM AgendaGradeAtendimentoHorario agah "
                        + " left join agah.solicitacaoAgendamento sa "
                        + "WHERE sa.codigo = solicitacaoAgendamento.codigo AND agah.status = " + AgendaGradeAtendimentoHorario.STATUS_AGENDADO + ")");
            }
        }

        if (param.getEmpresas() != null) {
            hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.empresaOrigem.codigo in ", param.getEmpresas());
        } else {
            hql.addToWhereWhithAnd(" agendaGradeAtendimentoHorario.empresaOrigem = ", param.getEmpresaOrigem());
        }

        if (param.getUnidadeReferencia() != null) {
            hql.addToWhereWhithAnd("(" +
                    "(exists(SELECT 1 FROM EquipeProfissional ep " +
                    "left join ep.equipeMicroArea ema " +
                    "left join ep.equipe e " +
                    "left join e.equipeArea ea " +
                    "left join ea.cidade c " +
                    "left join e.empresa epr " +
                    "WHERE ema.codigo = enderecoDomicilioEquipeMicroArea.codigo " +
                    "AND c.codigo = enderecoDomicilioCidade.codigo " +
                    "AND e.ativo = '" + RepositoryComponentDefault.SIM + "' " +
                    "AND ep.status = " + EquipeProfissional.STATUS_ATIVO + " " +
                    "AND epr.codigo = " + param.getUnidadeReferencia().getCodigo() + ")) " +
                    " or " +
                    "(enderecoEstrEquipeMicroAreaEquipeProfEquipeEmpr.codigo = " + param.getUnidadeReferencia().getCodigo() + ") " +
                    " or " +
                    "(usuarioCadsusEquipeEmpresa.codigo = " + param.getUnidadeReferencia().getCodigo() + ")" +
                    ")");
        }

        if (param.getTipoProcedimento() != null) {
            hql.addToWhereWhithAnd(" tipoProcedimento.codigo = ", param.getTipoProcedimento().getCodigo());
        }

        if (param.getRegulado() != null) {
            hql.addToWhereWhithAnd("tipoProcedimento.regulado = ", param.getRegulado());
        }

        if (param.getCampoOrdenacao() != null) {
            if ("nomePaciente".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("agendaGradeAtendimentoHorario.nomePaciente " + param.getTipoOrdenacao());
            } else if ("agendaGradeAtendimentoHorario.tipoProcedimento.descricao".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("tipoProcedimento.descricao " + param.getTipoOrdenacao());
            } else if ("dataAgendamentoFormatadoDataHora".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("2 " + param.getTipoOrdenacao());
            } else if ("agendaGradeAtendimentoHorario.solicitacaoAgendamento.unidadeExecutante.descricao".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("unidadeExecutante.descricao " + param.getTipoOrdenacao());
            } else if ("dataUltimoContatoFormatado".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("1 " + param.getTipoOrdenacao());
            } else if ("agendaGradeAtendimentoHorario.situacao".equals(param.getCampoOrdenacao())) {
                hql.addToOrder("agendaGradeAtendimentoHorario.status " + param.getTipoOrdenacao());
            } else {
                hql.addToOrder(param.getCampoOrdenacao() + " " + param.getTipoOrdenacao());
            }
        } else {
            hql.addToOrder(" 1 nulls first, 2");
        }
    }

    @Override
    protected void setParameters(Query query) {
        if (getDataPaging().getType().equals(DataPaging.Type.ALVO_LIST)) {
            query.setParameterList("statusList", Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, AgendaGradeAtendimentoHorario.STATUS_CANCELADO));
            query.setLong("statusAgendado", AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        }
        if (param.getSituacao() == null) {
            query.setDate("dataInicio", Data.adjustRangeHour(param.getDatePeriod()).getDataInicial());
        }
        if (param.getCns() != null) {
            query.setLong("codigoCns", param.getCns());
        }
    }

    @Override
    protected HQLHelper customHQLCount(HQLHelper hql) {
        hql.setSelect("count(distinct solicitacaoAgendamento.codigo)");
        hql.setGroup("");
        return super.customHQLCount(hql);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

}
