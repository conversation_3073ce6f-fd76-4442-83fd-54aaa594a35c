package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.PainelControleProgramaWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryPermissaoPaginaUsuario extends CommandQuery<QueryPermissaoPaginaUsuario> {

    private Usuario usuario;
    private String url;

    private List<ProgramaWeb> programas;

    private boolean pagePermitted = false;

    public QueryPermissaoPaginaUsuario(Usuario usuario, String url) {
        this.usuario = usuario;
        this.url = url;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("pw.codigo", "codigo");

        hql.setTypeSelect(ProgramaWeb.class.getName());
        hql.addToFrom("EloProgramaWebPagina elo "
                + " left join elo.programaWeb pw "
                + " left join elo.programaPagina pp ");

        hql.addToWhereWhithAnd("pp.caminhoPagina = ", url);
        hql.addToWhereWhithAnd("pw.ativo = ", RepositoryComponentDefault.SIM);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        List<PainelControleProgramaWeb> painelControleProgramaWebs = LoadManager.getInstance(PainelControleProgramaWeb.class)
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PainelControleProgramaWeb.PROP_PROGRAMA_WEB), BuilderQueryCustom.QueryParameter.IN, programas))
                .start().getList();
        
        if (CollectionUtils.isEmpty(painelControleProgramaWebs) && usuario.isNivelAdminOrMaster()) {
            pagePermitted = true;
        } else if (CollectionUtils.isNotNullEmpty(programas)) {
            List<ProgramaWeb> progs = new QueryProgramasWebUsuarioGrupo(usuario, programas).start().getProgramas();
            progs.addAll(new QueryProgramasWebUsuarioPainelControle(usuario, programas).start().getProgramas());
            pagePermitted = !progs.isEmpty();
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.programas = hql.getBeanList((List) result);
    }

    public boolean isPagePermitted() {
        return pagePermitted;
    }
}
