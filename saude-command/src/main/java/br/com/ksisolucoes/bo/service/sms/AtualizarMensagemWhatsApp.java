package br.com.ksisolucoes.bo.service.sms;

import br.com.celk.sms.restservice.response.*;
import br.com.celk.sms.restservice.service.exception.BusinessException;
import br.com.celk.sms.restservice.service.exception.SMSException;
import br.com.celk.util.DataUtil;
import br.com.celk.whatsapp.WhatsAppHelper;
import br.com.celk.whatsapp.WhatsAppReceivedStatusResponse;
import br.com.celk.whatsapp.WhatsApptService;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.service.sms.SmsConsulta;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 */
public class AtualizarMensagemWhatsApp extends AbstractCommandTransaction<AtualizarMensagemWhatsApp> {

    private final Long codigo;
    private SmsMensagem mensagem;
    private SmsControleIntegracao smsControleIntegracao;

    public AtualizarMensagemWhatsApp(SmsControleIntegracao smsControleIntegracao) {
        this.smsControleIntegracao = smsControleIntegracao;
        this.codigo = smsControleIntegracao.getSmsMensagem().getCodigo();
    }

    @Override
    public void execute() throws DAOException {
        try {
            setMessage();
            atualizarMensagensWhatsApp();
        } catch (DAOException | ValidacaoException ex) {
            throw new DAOException(ex);
        }
    }

    private void atualizarMensagensWhatsApp() throws DAOException, ValidacaoException {
        RestResponse statusMessageWhatsApp = getStatusMessageWhatsApp();
        saveOrUpdateStatusWhatsApp(statusMessageWhatsApp);
    }

    private void setMessage() throws ValidacaoException {
        if (codigo == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campos_obrigatorios_nao_informados"));
        }
        mensagem = (SmsMensagem) getSession().get(SmsMensagem.class, codigo);
        if (mensagem == null || mensagem.getMensagemId() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_sms_nao_encontrada"));
        }
    }

    private RestResponse getStatusMessageWhatsApp() throws DAOException {
        try {
            String url = WhatsAppHelper.getURLProviderStatus();
            return WhatsApptService.build(new URL(url)).getStatusWhatsApp(smsControleIntegracao.getSmsMensagem().getMensagemId());
        } catch (SMSException | BusinessException | MalformedURLException e) {
            Loggable.log.error("Erro na comunicação com o serviço de SMS", e);

            return null;
        }
    }

    private void saveOrUpdateStatusWhatsApp(RestResponse response) throws ValidacaoException, DAOException {
        SmsConsulta consulta = new SmsConsulta();
        consulta.setData(DataUtil.getDataAtual());
        consulta.setSmsMensagem(mensagem);

        WhatsAppReceivedStatusResponse responseBody = null;
        if (response != null) {
            responseBody = (WhatsAppReceivedStatusResponse) RndsUtil.stringJsonToObject(response.getBody(), WhatsAppReceivedStatusResponse.class);
            if (Long.valueOf(response.getStatusCode()) != null) {
                consulta.setCodigoRetorno(Long.valueOf(response.getStatusCode()));
                consulta.setMensagemRetorno(responseBody == null ? null : responseBody.getStatus());
            }
            mensagem.setStatus(SmsMensagem.StatusSms.RECEBIDO.value());
            BOFactory.save(mensagem);
        }
        BOFactory.save(consulta);

        smsControleIntegracao = (SmsControleIntegracao) getSession().get(SmsControleIntegracao.class, smsControleIntegracao.getCodigo());
        SmsControleIntegracao.StatusMensagemWhatsAppEnum byDescricao = SmsControleIntegracao.StatusMensagemWhatsAppEnum.getByDescricao(responseBody == null ? null : responseBody.getStatus());
        smsControleIntegracao.setStatusWhatsapp(byDescricao == null ? null : byDescricao.descricao());
        BOFactory.save(smsControleIntegracao);
    }
}