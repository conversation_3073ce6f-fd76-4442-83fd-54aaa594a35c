package br.com.ksisolucoes.bo.portal.servicos.agendamento;

import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryAgendamentoHistoricoPortal extends CommandQuery<QueryAgendamentoHistoricoPortal> {

    private HistoricoPacienteDTOParam param;
    private List<HistoricoPacienteDTO> resultado;

    public QueryAgendamentoHistoricoPortal(HistoricoPacienteDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("agendaGradeAtendimentoHorario.codigo", "agendaGradeAtendimentoHorario.codigo");
        hql.addToSelect("agendaGradeAtendimentoHorario.status", "agendaGradeAtendimentoHorario.status");
        hql.addToSelect("agendaGradeAtendimentoHorario.dataAgendamento", "agendaGradeAtendimentoHorario.dataAgendamento");
        hql.addToSelect("localAgendamento.codigo", "agendaGradeAtendimentoHorario.localAgendamento.codigo");
        hql.addToSelect("localAgendamento.descricao", "agendaGradeAtendimentoHorario.localAgendamento.descricao");
        
        hql.addToSelect("tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao");
        hql.addToSelect("solicitacaoAgendamento.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.codigo");
        hql.addToSelect("solicitacaoAgendamento.dataSolicitacao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao");
        hql.addToSelect("profissional.nome", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome");
        hql.addToSelect("solicitacaoAgendamento.prioridade", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.prioridade");
        hql.addToSelect("solicitacaoAgendamento.status", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.status");
      

        hql.setTypeSelect(HistoricoPacienteDTO.class.getName());
        hql.addToFrom("AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario"
                + " right join agendaGradeAtendimentoHorario.solicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.tipoProcedimento tipoProcedimento"
                + " left join solicitacaoAgendamento.profissional profissional"
                + " left join agendaGradeAtendimentoHorario.localAgendamento localAgendamento");

        hql.addToWhereWhithAnd("solicitacaoAgendamento.usuarioCadsus.codigo = ", this.param.getUsuarioCadsus().getCodigo());
        hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoProcedimento.flagTfd = ", "N");

        if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.tipoProcedimento.descricao")) {
            hql.addToOrder("tipoProcedimento.descricao" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao")) {
            hql.addToOrder("solicitacaoAgendamento.dataSolicitacao" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.profissional.nome")) {
            hql.addToOrder("profissional.nome" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.prioridade")) {
            hql.addToOrder("solicitacaoAgendamento.prioridade" + (param.isAscending() ? " asc" : " desc"));
        } else if (param.getPropSort().equals("agendaGradeAtendimentoHorario.dataAgendamento")) {
            hql.addToOrder("agendaGradeAtendimentoHorario.dataAgendamento" + (param.isAscending() ? " asc" : " desc"));
        }

        if (HistoricoPacienteDTO.TipoAgendamento.PENDENTE.value().equals(param.getAgendamentoOP())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", Arrays.asList(
                    SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO,
                    SolicitacaoAgendamento.STATUS_AGUARDANDO_ANALISE,
                    SolicitacaoAgendamento.STATUS_FILA_ESPERA,
                    SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE,
                    SolicitacaoAgendamento.STATUS_DEVOLVIDO));
        } else if (HistoricoPacienteDTO.TipoAgendamento.AGENDADO.value().equals(param.getAgendamentoOP())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", Arrays.asList(
                    SolicitacaoAgendamento.STATUS_AGENDADO,
                    SolicitacaoAgendamento.STATUS_REGULACAO_AGENDADO,
                    SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE));
        } else if (HistoricoPacienteDTO.TipoAgendamento.NEGADO.value().equals(param.getAgendamentoOP())) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", Arrays.asList(
                    SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO,
                    SolicitacaoAgendamento.STATUS_CANCELADO));
        } else if (HistoricoPacienteDTO.TipoAgendamento.AMBOS.value().equals(param.getAgendamentoOP())) {
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result);
    }

    public List<HistoricoPacienteDTO> getResultado() {
        if (param.getPropSort().equals("agendaGradeAtendimentoHorario.solicitacaoAgendamento.status")) {
            Collections.sort(resultado, new Comparator<HistoricoPacienteDTO>() {
                @Override
                public int compare(HistoricoPacienteDTO o1, HistoricoPacienteDTO o2) {
                    int aux = o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDescricaoSituacao().compareTo(o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDescricaoSituacao());
                    if (param.isAscending()) {
                        aux = aux * -1;
                    }
                    if (aux == 0) {
                        return o1.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao().compareTo(o2.getAgendaGradeAtendimentoHorario().getSolicitacaoAgendamento().getDataSolicitacao());
                    } else {
                        return aux;
                    }
                }
            });
        }
        return resultado;
    }

}
