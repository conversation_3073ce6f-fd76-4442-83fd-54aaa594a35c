/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

/**
 *
 * <AUTHOR>
 */
public class AtualizarTelefonesUsuarioCadsus extends AbstractCommandTransaction<AtualizarTelefonesUsuarioCadsus> {

    private UsuarioCadsus usuarioCadsus;

    public AtualizarTelefonesUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        String telefone = usuarioCadsus.getTelefone();
        String telefone2 = usuarioCadsus.getTelefone2();
        String telefone3 = usuarioCadsus.getTelefone3();
        String telefone4 = usuarioCadsus.getTelefone4();
        String celular = usuarioCadsus.getCelular();
        String email = usuarioCadsus.getEmail();
        
        UsuarioCadsus usuarioCadsus_ = (UsuarioCadsus) this.getSession().get(UsuarioCadsus.class, this.usuarioCadsus.getCodigo());
        
        if (telefone != null) {
            usuarioCadsus_.setTelefone(telefone);
        }
        if (telefone2 != null) {
            usuarioCadsus_.setTelefone2(telefone2);
        }
        if (telefone3 != null) {
            usuarioCadsus_.setTelefone3(telefone3);   
        }
        if (telefone4 != null) {
            usuarioCadsus_.setTelefone4(telefone4);
        }
        if (celular != null) {
            usuarioCadsus_.setCelular(celular);
        }
        if(email != null){
            usuarioCadsus_.setEmail(email);
        }

        BOFactory.getBO(CadastroFacade.class).save(usuarioCadsus_);
        
    }
    
}
