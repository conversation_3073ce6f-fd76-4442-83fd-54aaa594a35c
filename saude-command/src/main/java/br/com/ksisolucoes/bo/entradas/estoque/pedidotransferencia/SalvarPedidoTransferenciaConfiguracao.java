package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.ConfiguracaoPedidoTransferenciaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracao;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaConfiguracaoItem;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import ch.lambdaj.Lambda;
import org.hibernate.Criteria;

import java.util.List;

/**
 * <AUTHOR> Ramos
 */
public class SalvarPedidoTransferenciaConfiguracao extends AbstractCommandTransaction {

    private final ConfiguracaoPedidoTransferenciaDTO dto;

    public SalvarPedidoTransferenciaConfiguracao(ConfiguracaoPedidoTransferenciaDTO configuracaoPedidoTransferenciaDTO) {
        this.dto = configuracaoPedidoTransferenciaDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        PedidoTransferenciaConfiguracao pedidoTransferenciaConfiguracao = dto.getPedidoTransferenciaConfiguracao();
        List<PedidoTransferenciaConfiguracaoItem> itemPedidoConfiguracaoList = dto.getListPedidoTransferenciaConfiguracaoItem();

        pedidoTransferenciaConfiguracao.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        pedidoTransferenciaConfiguracao.setDataCadastro(DataUtil.getDataAtual());

        pedidoTransferenciaConfiguracao =  BOFactory.getBO(CadastroFacade.class).save(pedidoTransferenciaConfiguracao);


        if (CollectionUtils.isNotNullEmpty(itemPedidoConfiguracaoList)) {
            for (PedidoTransferenciaConfiguracaoItem pedidoTransferenciaConfiguracaoItem : itemPedidoConfiguracaoList) {
                pedidoTransferenciaConfiguracaoItem.setPedidoTransferenciaConfiguracao(pedidoTransferenciaConfiguracao);
                pedidoTransferenciaConfiguracaoItem.setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
                pedidoTransferenciaConfiguracaoItem.setDataCadastro(DataUtil.getDataAtual());
                if(pedidoTransferenciaConfiguracaoItem.getSubGrupo() != null){
                    pedidoTransferenciaConfiguracaoItem.setGrupo(pedidoTransferenciaConfiguracaoItem.getSubGrupo().getRoGrupoProduto());
                }
            }
            Lambda.forEach(itemPedidoConfiguracaoList).setPedidoTransferenciaConfiguracao(pedidoTransferenciaConfiguracao);
            Lambda.forEach(itemPedidoConfiguracaoList).setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
            Lambda.forEach(itemPedidoConfiguracaoList).setDataCadastro(DataUtil.getDataAtual());
        }

        VOUtils.persistirListaVosModificados(PedidoTransferenciaConfiguracaoItem.class, itemPedidoConfiguracaoList,
                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaConfiguracaoItem.PROP_PEDIDO_TRANSFERENCIA_CONFIGURACAO,PedidoTransferenciaConfiguracao.PROP_CODIGO), pedidoTransferenciaConfiguracao.getCodigo()));

    }


}