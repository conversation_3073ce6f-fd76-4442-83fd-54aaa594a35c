package br.com.ksisolucoes.bo.frota.registromanutencao;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.frota.RegistroManutencao;
import br.com.ksisolucoes.vo.frota.RegistroManutencaoItem;
import br.com.ksisolucoes.vo.frota.Veiculo;
import br.com.ksisolucoes.vo.frota.VeiculoDados;
import java.util.List;
import org.hibernate.Criteria;

/**
 *
 * <AUTHOR>
 */
public class CadastrarRegistroManutencao extends AbstractCommandTransaction {

    private RegistroManutencao registroManutencao;
    private List<RegistroManutencaoItem> itens;

    private static final int PRECISAO_VALOR = 4;

    public CadastrarRegistroManutencao(RegistroManutencao registroManutencao, List<RegistroManutencaoItem> itens) {
        this.registroManutencao = registroManutencao;
        this.itens = itens;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validacoes();

        registroManutencao.setDataUsuario(Data.getDataAtual());

        if (registroManutencao.getEmpresa() == null) {
            registroManutencao.setEmpresa(getSessao().<Empresa>getEmpresa());
        }
        if (registroManutencao.getUsuario() == null) {
            registroManutencao.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (registroManutencao.getDataCadastro() == null) {
            registroManutencao.setDataCadastro(Data.getDataAtual());
        }

        BOFactory.save(registroManutencao);

        List<RegistroManutencaoItem> itensExistentes = LoadManager.getInstance(RegistroManutencaoItem.class)
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(RegistroManutencaoItem.PROP_REGISTRO_MANUTENCAO), this.registroManutencao))
                .start().getList();

        for (RegistroManutencaoItem registroManutencaoItem : itens) {
            registroManutencaoItem.setRegistroManutencao(registroManutencao);
            BOFactory.save(registroManutencaoItem);
            itensExistentes.remove(registroManutencaoItem);

            if (RepositoryComponentDefault.SIM.equals(registroManutencaoItem.getTipoOperacao().getFlagAtualizaDadoVeiculo())) {
                Criteria cVeiculoDados = getSession().createCriteria(VeiculoDados.class)
                        .add(Restrictions.eq(VOUtils.montarPath(VeiculoDados.PROP_VEICULO), registroManutencaoItem.getVeiculo()));

                VeiculoDados veiculoDados = (VeiculoDados) cVeiculoDados.uniqueResult();

                if (veiculoDados == null) {
                    veiculoDados = new VeiculoDados();
                    veiculoDados.setVeiculo(registroManutencaoItem.getVeiculo());
                }

                veiculoDados.setKmAtual(registroManutencaoItem.getKmAtual());
                veiculoDados.setKmProximaTrocaOleo(registroManutencaoItem.getKmProximaTrocaOleo());
                veiculoDados.setKmProximaRevisao(registroManutencaoItem.getKmProximaRevisao());
                veiculoDados.setDataProximaRevisao(registroManutencaoItem.getDataProximaRevisao());
                veiculoDados.setDataProximoLicenciamento(registroManutencaoItem.getDataProximoLicenciamento());
                veiculoDados.setDataProximoSeguroObrigatorio(registroManutencaoItem.getDataProximoSeguroObrigatorio());

                BOFactory.save(veiculoDados);
            }
            Veiculo veiculo = registroManutencaoItem.getVeiculo();
            veiculo = (Veiculo) getSession().get(Veiculo.class, veiculo.getCodigo());
            boolean veiculoFoiAlterado = false;
            if (RepositoryComponentDefault.SIM.equals(registroManutencaoItem.getTipoOperacao().getDataProximoLicenciamento())) {
                veiculo.setDataLicenciamento(registroManutencaoItem.getDataProximoLicenciamento());
                veiculoFoiAlterado = true;
            }
            if (RepositoryComponentDefault.SIM.equals(registroManutencaoItem.getTipoOperacao().getDataProximoSeguroObrigatorio())) {
                veiculo.setDataSeguroObrigatorio(registroManutencaoItem.getDataProximoSeguroObrigatorio());
                veiculoFoiAlterado = true;
            }
            if (veiculoFoiAlterado) {
                BOFactory.save(veiculo);
            }

        }

        for (RegistroManutencaoItem registroManutencaoItem : itensExistentes) {
            BOFactory.delete(registroManutencaoItem);
        }
    }

    public RegistroManutencao getRegistroManutencao() {
        return registroManutencao;
    }

    private void validacoes() throws ValidacaoException {
        if (CollectionUtils.isEmpty(itens)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_pelo_menos_um_item"));
        }
        if (Data.adjustRangeHour(Data.getDataAtual()).getDataFinal().before(registroManutencao.getDataManutencao())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_manutencao_maior_atual"));
        }
        Dinheiro valorTotalInformado = new Dinheiro(registroManutencao.getValorTotal(), PRECISAO_VALOR);
        validaValorTotalInformado(valorTotalInformado,itens);
    }

    private void validaValorTotalInformado(Dinheiro valorTotalInformado, List<RegistroManutencaoItem> itens) throws ValidacaoException {
        Dinheiro valorTotalCalculado = getSomatorioValor(itens);
        if (!valorTotalCalculado.equals(valorTotalInformado)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_total_nota_diferente_itens"));
        }
    }

    private Dinheiro getSomatorioValor(List<RegistroManutencaoItem> itens) {
        Dinheiro totalItens = new Dinheiro(0D, PRECISAO_VALOR);
        for (RegistroManutencaoItem registroManutencaoItem : itens) {
            totalItens = totalItens.somar(registroManutencaoItem.getValor(), PRECISAO_VALOR);
        }
        return totalItens;
    }
}
