package br.com.ksisolucoes.bo.prontuario.basico.kitProdutos;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.ItemKitProduto;
import br.com.ksisolucoes.vo.entradas.estoque.KitProduto;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class ExcluiKitProduto extends AbstractCommandTransaction {

    private KitProduto kitProduto;

    public ExcluiKitProduto(KitProduto kitProduto) {
        this.kitProduto = kitProduto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        Criteria cKit = getSession().createCriteria(ItemKitProduto.class);
        cKit.add(Restrictions.eq(ItemKitProduto.PROP_PRODUTO_PRINCIPAL, kitProduto.getProdutoPrincipal()));

        List<ItemKitProduto> lista = cKit.list();
        
        for (ItemKitProduto itemKitProduto : lista) {
            BOFactory.delete(itemKitProduto);
        }
        
        BOFactory.delete(kitProduto);
    }
}
