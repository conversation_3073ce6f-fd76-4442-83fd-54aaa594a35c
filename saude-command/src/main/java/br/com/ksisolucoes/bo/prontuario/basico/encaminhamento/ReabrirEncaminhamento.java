package br.com.ksisolucoes.bo.prontuario.basico.encaminhamento;

import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.EncaminhamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;

/**
 *
 * <AUTHOR>
 */
public class ReabrirEncaminhamento extends AbstractCommandTransaction {

    private Encaminhamento encaminhamento;
    private String motivo;

    public ReabrirEncaminhamento(Encaminhamento encaminhamento, String motivo) {
        this.encaminhamento = encaminhamento;
        this.motivo = motivo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
            encaminhamento = HibernateUtil.rechargeVO(Encaminhamento.class, encaminhamento.getCodigo(), encaminhamento.getVersion());

        if (Encaminhamento.STATUS_CONCLUIDO.equals(encaminhamento.getStatus())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_encaminhamento_concluido_nao_pode_ser_reaberto"));
        }

        encaminhamento.setStatus(Encaminhamento.STATUS_PENDENTE);
        encaminhamento.setUsuarioCancelamento(null);
        encaminhamento.setDataCancelamento(null);
        encaminhamento.setDescricaoCancelamento(null);
        encaminhamento.setDataContatoPaciente(null);

        encaminhamento = (Encaminhamento) BOFactory.getBO(EncaminhamentoFacade.class).save(encaminhamento);

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(encaminhamento.getUsuarioCadsus(), TipoOcorrencia.TIPO_AGENDAMENTO, Bundle.getStringApplication("msg_encaminhamento_reaberto") + ": " + motivo, encaminhamento);
    }
}
