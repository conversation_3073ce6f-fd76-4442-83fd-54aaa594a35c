package br.com.ksisolucoes.bo.prontuario.basico.naturezaprocuratipoatendimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EmpresaNaturezaTipoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;

import java.util.ArrayList;
import java.util.List;

import static ch.lambdaj.Lambda.forEach;

/**
 *
 * <AUTHOR>
 */
public class CadastrarNaturezaProcuraTipoAtendimento extends AbstractCommandTransaction {

    private NaturezaProcuraTipoAtendimento npta;
    private List<EmpresaNaturezaTipoDTO> empresas;
    private List<EloNaturezaTipoEncaminhamento> elosNaturezaEncaminhamento;

    public CadastrarNaturezaProcuraTipoAtendimento(NaturezaProcuraTipoAtendimento npta, List<EmpresaNaturezaTipoDTO> empresas, List<EloNaturezaTipoEncaminhamento> elosNaturezaEncaminhamento) {
        this.npta = npta;
        this.empresas = empresas;
        this.elosNaturezaEncaminhamento = elosNaturezaEncaminhamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validacoes();
        BOFactory.getBO(CadastroFacade.class).save(npta);

        List<EmpresaNaturezaProcuraTipoAtendimento> empresasExistentes = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), npta))
                .start().getList();
        List<EmpresaNaturezaProcuraTipoAtendimento> empresasParaExcluir = new ArrayList<EmpresaNaturezaProcuraTipoAtendimento>();
        empresa:
        for (EmpresaNaturezaProcuraTipoAtendimento empresaNaturezaProcuraTipoAtendimento : empresasExistentes) {
            for (EmpresaNaturezaTipoDTO empresaNaturezaTipoDTO : empresas) {
                EmpresaNaturezaProcuraTipoAtendimento enpta = empresaNaturezaTipoDTO.getEmpresaNaturezaProcuraTipoAtendimento();
                if (empresaNaturezaProcuraTipoAtendimento.equals(enpta)) {
                    continue empresa;
                }
            }
            empresasParaExcluir.add(empresaNaturezaProcuraTipoAtendimento);
        }

        for (EmpresaNaturezaProcuraTipoAtendimento empresaNaturezaProcuraTipoAtendimento : empresasParaExcluir) {
            BOFactory.getBO(CadastroFacade.class).delete(empresaNaturezaProcuraTipoAtendimento);
        }

        getSession().flush();

        if (CollectionUtils.isNotNullEmpty(empresas)) {
            for (EmpresaNaturezaTipoDTO empresaNaturezaTipoDTO : empresas) {
                EmpresaNaturezaProcuraTipoAtendimento enpta = empresaNaturezaTipoDTO.getEmpresaNaturezaProcuraTipoAtendimento();
                BOFactory.getBO(CadastroFacade.class).save(enpta);
            }
        }

        if (CollectionUtils.isNotNullEmpty(elosNaturezaEncaminhamento)) {
            forEach(elosNaturezaEncaminhamento).setNaturezaProcuraTipoAtendimento(npta);
        }
        if (elosNaturezaEncaminhamento != null) {
            VOUtils.persistirListaVosModificados(EloNaturezaTipoEncaminhamento.class, elosNaturezaEncaminhamento, new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), npta));
        }

    }

    private void validacoes() throws ValidacaoException, DAOException {
        // Valida se já existe um cadastro com a mesma Natureza Procura e o mesmo Tipo do Procedimento, ou mesma Natureza Procura e o mesmo Tipo do Exame
        if (npta.getTipoProcedimento() != null) {
            LoadManager loadTipoProc = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class);
            if (npta.getCodigo() != null) {
                loadTipoProc.addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.DIFERENTE, npta.getCodigo()));
            }

            loadTipoProc.addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, npta.getNaturezaProcura()));
            loadTipoProc.addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO, npta.getTipoProcedimento()));
            NaturezaProcuraTipoAtendimento nptaTipoProc = loadTipoProc.setMaxResults(1).start().getVO();

            if (nptaTipoProc != null) {
                if (nptaTipoProc.getTipoProcedimento().equals(npta.getTipoProcedimento())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_cadastro_mesmo_tipo_procedimento_informado"));
                }
            }
        }
    }
}
