package br.com.ksisolucoes.bo.smsappservice;

//import br.com.celk.smsappservice.ServiceException;
//import br.com.celk.smsappservice.dto.SmsSend;
//import br.com.celk.smsappservice.dto.SmsSendResponse;
//import br.com.celk.smsappservice.servicewrappers.EnviarSms;
import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.smsappservice.dto.EnviarSMSDTO;
import br.com.ksisolucoes.bo.smsappservice.dto.SmsDTOParam;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;

/**
 *
 * <AUTHOR>
 */
public class EnviarSMS extends AbstractCommandTransaction<EnviarSMS> {

    private EnviarSMSDTO enviarSMSDTO;

    public EnviarSMS(EnviarSMSDTO enviarSMSDTO) {
        this.enviarSMSDTO = enviarSMSDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoHorario agah = (AgendaGradeAtendimentoHorario) this.getSession().get(AgendaGradeAtendimentoHorario.class, enviarSMSDTO.getDocumentoOrigem());
        UsuarioCadsus usuarioCadsus = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, agah.getUsuarioCadsus().getCodigo());

        BOFactory.getBO(AgendamentoFacade.class).gerarOcorrenciaSolicitacaoAgendamento(SolicitacaoAgendamentoOcorrencia.TipoOcorrencia.SOLICITACAO, Bundle.getStringApplication("obs_ocorrencia_sms", enviarSMSDTO.getTelefone()), agah.getSolicitacaoAgendamento());

        this.getSession().flush();

        SmsDTOParam param = new SmsDTOParam();
        param.setMessage(enviarSMSDTO.getMensagem());
        param.setPhone(enviarSMSDTO.getTelefone());
        param.setOrigem(SmsMensagem.OrigemSms.PROCESSO_DESCONHECIDO);
        param.setUsuarioCadsus(usuarioCadsus);
        
            BOFactory.getBO(SmsFacade.class).enviarSmsAsync(param);
        
    }

    public EnviarSMSDTO getEnviarSMSDTORetorno() {
        return enviarSMSDTO;
    }
}
