/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.procedimento.pequenacirurgia;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.PequenaCirurgiaItemDTO;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.PequenaCirurgiaProdutoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgia;
import br.com.ksisolucoes.vo.prontuario.procedimento.PequenaCirurgiaProduto;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarInsumosPequenaCirurgia extends AbstractCommandTransaction{

    private List<PequenaCirurgiaItemDTO> dtoList;

    public CadastrarInsumosPequenaCirurgia(List<PequenaCirurgiaItemDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        TipoDocumento tipoDocumento = (TipoDocumento) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_TIPO_DOCUMENTO_BAIXA_ENFERMAGEM);
        if(CollectionUtils.isNotNullEmpty(dtoList)){
            for (PequenaCirurgiaItemDTO dto : dtoList) {
                PequenaCirurgia pequenaCirurgia = (PequenaCirurgia) getSession().get(PequenaCirurgia.class, dto.getPequenaCirurgia().getCodigo());
                pequenaCirurgia.setStatus(PequenaCirurgia.StatusPequenaCirurgia.BAIXADO.value());

                List<PequenaCirurgiaProduto> produtoList = LoadManager.getInstance(PequenaCirurgiaProduto.class)
                        .addParameter(new QueryCustomParameter(PequenaCirurgiaProduto.PROP_PEQUENA_CIRURGIA, dto.getPequenaCirurgia()))
                        .start().getList();
                for (PequenaCirurgiaProduto pequenaCirurgiaProduto : produtoList) {
                    BOFactory.getBO(CadastroFacade.class).delete(pequenaCirurgiaProduto);
                }

                int itemDocumento = 0;
                for (PequenaCirurgiaProdutoDTO produtoDto : dto.getPequenaCirurgiaProdutoDTOs()) {
                    PequenaCirurgiaProduto pcp = produtoDto.getPequenaCirurgiaProduto();
                    pcp.setLote(produtoDto.getDescricaoLotes());
                    pcp.setPequenaCirurgia(pequenaCirurgia);

                    BOFactory.getBO(CadastroFacade.class).save(pcp);

                    if(RepositoryComponentDefault.SIM.equals(pcp.getProduto().getFlagBaixaEstoqueProcessoEnfermagem())){
                        MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
                        movimentoEstoque.setTipoDocumento(tipoDocumento);
                        movimentoEstoque.setProduto(pcp.getProduto());
                        movimentoEstoque.setProfissional(pequenaCirurgia.getAtendimento().getProfissional());
                        movimentoEstoque.setUsuarioCadsus(pequenaCirurgia.getAtendimento().getUsuarioCadsus());
                        movimentoEstoque.setNumeroDocumento(Coalesce.asString(pequenaCirurgia.getAtendimento().getCodigo()));
                        movimentoEstoque.setItemDocumento(new Long(++itemDocumento));
                        movimentoEstoque.setQuantidade(pcp.getQuantidadeUtilizada());

                        movimentoEstoque.setMovimentosGrupoEstoqueItem(produtoDto.getMovimentoGrupoEstoqueItemDTOList());

                        BOFactory.getBO(MovimentoEstoqueFacade.class).save(movimentoEstoque, false);
                        getSession().flush();
                        getSession().evict(movimentoEstoque);
                    }
                }

            }
        }
    }

}
