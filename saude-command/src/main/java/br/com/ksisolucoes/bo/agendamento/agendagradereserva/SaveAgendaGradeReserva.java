package br.com.ksisolucoes.bo.agendamento.agendagradereserva;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeReserva;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 * Created by sulivan on 29/01/19.
 */
public class SaveAgendaGradeReserva extends SaveVO<AgendaGradeReserva> {

    public SaveAgendaGradeReserva(AgendaGradeReserva vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getSituacao() == null) {
            vo.setSituacao(AgendaGradeReserva.Situacao.CONFIRMADO.value());
        }

        if (vo.getDataCadastro() == null) {
            vo.setDataCadastro(DataUtil.getDataAtual());
        }

        if (vo.getUsuarioCadastro() == null) {
            vo.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
        }
    }
}