package br.com.ksisolucoes.bo.consorcio.licitacao;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.Licitacao;

/**
 *
 * <AUTHOR>
 */
public class IniciarPregao extends AbstractCommandTransaction {

    private Licitacao licitacao; 

    public IniciarPregao(Licitacao licitacao) {
        this.licitacao = licitacao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        licitacao.setStatus(Licitacao.StatusLicitacao.PREGAO.value());
        this.licitacao = BOFactory.save(licitacao);
    }

    public Licitacao getLicitacao() {
        return licitacao;
    }
    
}
