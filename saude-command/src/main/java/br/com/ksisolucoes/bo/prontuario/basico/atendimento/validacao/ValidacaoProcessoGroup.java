package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.validacao.ValidacaoProcesso;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ValidacaoProcessoGroup implements Serializable{

    private NodesAtendimentoRef identificador;
    private ValidacaoProcesso validacaoProcesso;

    public ValidacaoProcessoGroup(NodesAtendimentoRef identificador, ValidacaoProcesso validacaoProcesso) {
        this.identificador = identificador;
        this.validacaoProcesso = validacaoProcesso;
    }

    public NodesAtendimentoRef getIdentificador() {
        return identificador;
    }

    public void setIdentificador(NodesAtendimentoRef identificador) {
        this.identificador = identificador;
    }

    public ValidacaoProcesso getValidacaoProcesso() {
        return validacaoProcesso;
    }

    public void setValidacaoProcesso(ValidacaoProcesso validacaoProcesso) {
        this.validacaoProcesso = validacaoProcesso;
    }
    
}
