package br.com.ksisolucoes.bo.geral.estruturaequipamento;

import br.com.ksisolucoes.bo.geral.interfaces.dto.EstruturaEquipamentoDTOParam;
import java.util.LinkedHashSet;
import java.util.List;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.geral.EstruturaEquipamento;

public class ProcessarEstruturaEquipamentoToProdutos extends AbstractCommandTransaction {
    
    private List<Produto> produtoList;
    
    private int niveis;
    private String procedencia;
    private Double quantidadeNecessaria;
    
    public ProcessarEstruturaEquipamentoToProdutos(List<Produto> produtoList) {
        this(produtoList, null);
    }
    
    public ProcessarEstruturaEquipamentoToProdutos(List<Produto> produtoList, String procedencia) {
        this(produtoList, 0, procedencia);
    }

    public ProcessarEstruturaEquipamentoToProdutos(List<Produto> produtoList, int niveis) {
        this(produtoList, niveis, null);
    }
    
    public ProcessarEstruturaEquipamentoToProdutos(List<Produto> produtoList, int niveis, String procedencia) {
        this(produtoList, niveis, procedencia, 1D);
    }
    
    public ProcessarEstruturaEquipamentoToProdutos(List<Produto> produtoList, int niveis, String procedencia, Double quantidadeNecessaria) {
        this.produtoList = produtoList;
        this.niveis = niveis;
        this.procedencia = procedencia;
        this.quantidadeNecessaria = quantidadeNecessaria;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        for (Produto produto : this.produtoList) {
            EstruturaEquipamentoDTOParam bean = new EstruturaEquipamentoDTOParam();
            bean.setProduto(produto);
            bean.setNiveis(this.niveis);
//            bean.setProcedencia(this.procedencia);
//            bean.setCalcularPeso(true);
            MontarEstruturaEquipamento montar = new MontarEstruturaEquipamento(bean);
            montar.setQuantidade(this.quantidadeNecessaria);
            montar.start();
            produto.setEstruturaEquipamentoSet(new LinkedHashSet<EstruturaEquipamento>(montar.getEstruturaEquipamentoList()));
            produto.setEstruturaEquipamentoList(montar.getEstruturaEquipamentoList());
        }
    }

    public List<Produto> getProdutoList() {
        return this.produtoList;
    }
}
