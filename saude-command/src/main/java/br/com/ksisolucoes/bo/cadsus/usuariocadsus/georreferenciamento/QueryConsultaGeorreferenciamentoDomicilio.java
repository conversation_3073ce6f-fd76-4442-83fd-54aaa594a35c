package br.com.ksisolucoes.bo.cadsus.usuariocadsus.georreferenciamento;

import br.com.celk.cadsus.relatorio.GeorreferenciamentoUsuarioCadsusDomicilioDTO;
import br.com.celk.cadsus.relatorio.GeorrefereniamentoUsuarioCadsusDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaGeorreferenciamentoDomicilio extends CommandQuery<QueryConsultaGeorreferenciamentoDomicilio> {

    private List<GeorreferenciamentoUsuarioCadsusDomicilioDTO> result;
    private final GeorrefereniamentoUsuarioCadsusDTOParam param;

    public QueryConsultaGeorreferenciamentoDomicilio(GeorrefereniamentoUsuarioCadsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(GeorreferenciamentoUsuarioCadsusDomicilioDTO.class.getName());

        hql.addToSelect("enderecoDomicilio.codigo", "enderecoDomicilio.codigo");
        hql.addToSelect("enderecoDomicilio.numeroFamilia", "enderecoDomicilio.numeroFamilia");
        hql.addToSelect("enderecoDomicilio.latitude", "enderecoDomicilio.latitude");
        hql.addToSelect("enderecoDomicilio.longitude", "enderecoDomicilio.longitude");
        hql.addToSelect("equipeMicroArea.codigo", "enderecoDomicilio.equipeMicroArea.codigo");
        hql.addToSelect("empresa.codigo", "enderecoDomicilio.equipeMicroArea.empresa.codigo");
        hql.addToSelect("empresa.descricao", "enderecoDomicilio.equipeMicroArea.empresa.descricao");

        hql.addToSelect("enderecoUsuarioCadsus.codigo", "enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");

        hql.addToSelect("cidade.codigo", "enderecoUsuarioCadsus.cidade.codigo");
        hql.addToSelect("cidade.descricao", "enderecoUsuarioCadsus.cidade.descricao");
        hql.addToSelect("estado.codigo", "enderecoUsuarioCadsus.cidade.estado.codigo");
        hql.addToSelect("estado.descricao", "enderecoUsuarioCadsus.cidade.estado.descricao");

        hql.addToSelect("(select min(uc.nome) from UsuarioCadsus uc left join uc.enderecoDomicilio ed "
                + " where uc.flagResponsavelFamiliar = " + RepositoryComponentDefault.SIM_LONG + " and ed = enderecoDomicilio"
                + " and ed.excluido = " + RepositoryComponentDefault.NAO_EXCLUIDO + ")", "responsavel");

        hql.addToFrom("EnderecoDomicilioEsus enderecoDomicilioEsus "
                + " left join enderecoDomicilioEsus.enderecoDomicilio enderecoDomicilio"
                + " right join enderecoDomicilio.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join cidade.estado estado"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro "
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.empresa empresa"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join equipeMicroArea.equipeProfissional equipeProfissional"
        );

        hql.addToWhereWhithAnd("enderecoDomicilioEsus.situacaoMoradia = ", param.getSituacaoMoradia());
        hql.addToWhereWhithAnd("enderecoDomicilioEsus.tipoDomicilio = ", param.getTipoDomicilio());
        hql.addToWhereWhithAnd("enderecoDomicilioEsus.localizacao = ", param.getLocalizacao());
        hql.addToWhereWhithAnd("equipeArea = ", param.getEquipeArea());

        hql.addToWhereWhithAnd(" enderecoUsuarioCadsus is not null ");

        hql.addToOrder("enderecoDomicilioEsus.codigo desc");
    }

    public List<GeorreferenciamentoUsuarioCadsusDomicilioDTO> getResult() {
        return result;
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(param.getMaxLimit().intValue() + 1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            if (this.result.size() >= param.getMaxLimit()) {
                return;
            }
            for (GeorreferenciamentoUsuarioCadsusDomicilioDTO geoDTO : this.result) {
                if (geoDTO.getEnderecoDomicilio() != null
                        && geoDTO.getEnderecoDomicilio().getLatitude() != null
                        && geoDTO.getEnderecoDomicilio().getLongitude() != null) {
                    geoDTO.setLatitude(new Double(geoDTO.getEnderecoDomicilio().getLatitude()));
                    geoDTO.setLongitude(new Double(geoDTO.getEnderecoDomicilio().getLongitude()));
                    continue;
                }
                LatitudeLongitudeEndereco latLng = new LatitudeLongitudeEndereco(geoDTO.getEnderecoUsuarioCadsus());
                if (latLng.getLongitude() != 0.0 && latLng.getLatitude() != 0.0) {
                    geoDTO.setLatitude(latLng.getLatitude());
                    geoDTO.setLongitude(latLng.getLongitude());
                }
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
