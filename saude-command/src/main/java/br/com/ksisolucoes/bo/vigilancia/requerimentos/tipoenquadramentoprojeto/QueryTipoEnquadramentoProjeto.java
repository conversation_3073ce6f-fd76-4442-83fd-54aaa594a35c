package br.com.ksisolucoes.bo.vigilancia.requerimentos.tipoenquadramentoprojeto;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaTipoEnquadramentoProjetoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.Doenca;

import java.util.List;
import java.util.Map;

public class QueryTipoEnquadramentoProjeto extends CommandQueryPager<QueryTipoEnquadramentoProjeto> {

    private QueryConsultaTipoEnquadramentoProjetoDTOParam param;

    public QueryTipoEnquadramentoProjeto(QueryConsultaTipoEnquadramentoProjetoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("tipoEnquadramentoProjeto.codigo",true);
        hql.addToSelect("tipoEnquadramentoProjeto.descricao",true);

        hql.setTypeSelect(Doenca.class.getName());
        hql.addToFrom("TipoEnquadramentoProjeto tipoEnquadramentoProjeto");

        hql.addToWhereWhithAnd("tipoEnquadramentoProjeto.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoEnquadramentoProjeto.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoEnquadramentoProjeto.codigo || ' ' || tipoEnquadramentoProjeto.descricao",param.getKeyword()));

        hql.addToOrder("tipoEnquadramentoProjeto.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String,Object>>)result);
    }

}
