package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryUltimoAtendimentoUsuarioCadsusHospitalDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryUltimoAtendimentoUsuarioCadsusHospital extends CommandQuery<QueryUltimoAtendimentoUsuarioCadsusHospital>{

    private Long codigoUsuarioCadsus;
    private QueryUltimoAtendimentoUsuarioCadsusHospitalDTO dto;

    public QueryUltimoAtendimentoUsuarioCadsusHospital(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("atendimentoInformacao.dataChegada","dataChegada");
        hql.addToSelect("empresa.descricao","descricaoEmpresa");

        hql.setTypeSelect(QueryUltimoAtendimentoUsuarioCadsusHospitalDTO.class.getName());
        hql.addToFrom("AtendimentoInformacao atendimentoInformacao "
                + " left join atendimentoInformacao.empresa empresa"
                + " left join atendimentoInformacao.usuarioCadsus usuarioCadsus");

        final List<Long> status = Arrays.asList(AtendimentoInformacao.StatusAtendimento.ABERTO.value(),AtendimentoInformacao.StatusAtendimento.CONCLUIDO.value());

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ",this.codigoUsuarioCadsus);
        hql.addToWhereWhithAnd("atendimentoInformacao.statusAtendimento in ", status);

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        hqlSub.addToSelect(" max(a.dataChegada) ");
        hqlSub.addToFrom("AtendimentoInformacao a "
                + " left join a.usuarioCadsus usuarioCadsusSub ");
        hqlSub.addToWhereWhithAnd("usuarioCadsusSub.codigo = ",this.codigoUsuarioCadsus);
        hqlSub.addToWhereWhithAnd("a.statusAtendimento in ", status);

        hql.addToWhereWhithAnd("atendimentoInformacao.dataChegada = ("+hqlSub.getQuery()+")");

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        try {
            this.dto = hql.getBean((List<Map<String, Object>>) result);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public QueryUltimoAtendimentoUsuarioCadsusHospitalDTO getDto() {
        return dto;
    }

}
