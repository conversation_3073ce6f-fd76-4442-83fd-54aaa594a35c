package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.hospitalhambulatorial.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.ProducaoIpeHospHambDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProducaoIpeDesignativoDespesas extends CommandQuery<QueryConsultaProducaoIpeDesignativoDespesas> {

    private List<Long> listaNotas;
    private List<ProducaoIpeHospHambDTO> result;

    public QueryConsultaProducaoIpeDesignativoDespesas(List<Long> listaNotas) {
        this.listaNotas = listaNotas;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ProducaoIpeHospHambDTO.class.getName());

        hql.addToSelectAndGroup("icp.numeroFolhaIpe", "itemContaPaciente.numeroFolhaIpe");
        hql.addToSelectAndGroup("icp.numeroNota", "itemContaPaciente.numeroNota");
        
        hql.addToSelectAndGroup("ap.codigo", "atendimentoPrincipal.codigo");
        hql.addToSelectAndGroup("ap.numeroRegistroConvenio", "atendimentoPrincipal.numeroRegistroConvenio");

        hql.addToSelectAndGroup("ai.dataChegada", "atendimentoInformacao.dataChegada");
        hql.addToSelectAndGroup("ai.dataSaida", "atendimentoInformacao.dataSaida");

        hql.addToSelect("count(icp.codigo)", "quantLancamentosDespesas");
        
        hql.addToSelectAndGroup("empresaPrincipal.codigo", "empresaPrincipal.codigo");
        hql.addToSelectAndGroup("empresaPrincipal.descricao", "empresaPrincipal.descricao");
        hql.addToSelectAndGroup("empresaPrincipal.numeroPrestadorIpe", "empresaPrincipal.numeroPrestadorIpe");

        hql.addToSelectAndGroup("tipoPrestadorIpe.codigo", "tipoPrestadorIpe.codigo");
        hql.addToSelectAndGroup("tipoPrestadorIpe.tipo", "tipoPrestadorIpe.tipo");

        hql.addToSelect("Sum(Coalesce(icp.precoUnitario,0)) ", "valorTotalDespesas");

        hql.addToFrom("ItemContaPaciente icp "
                + " left join icp.contaPaciente cp "
                + " left join cp.atendimentoInformacao ai "
                + " left join ai.empresa empresa "
                + " left join ai.atendimentoPrincipal ap "
                + " left join empresa.empresaPrincipal empresaPrincipal "
                + " left join empresaPrincipal.tipoPrestadorIpe tipoPrestadorIpe");

        hql.addToGroup("icp.numeroFolhaIpe");
        hql.addToGroup("cp.codigo");
        hql.addToWhereWhithAnd("icp.numeroNota in ", listaNotas);
        hql.addToWhereWhithAnd("icp.tipo in ", Arrays.asList(ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value(), ItemContaPaciente.Tipo.PROCEDIMENTO.value()));
        hql.addToWhereWhithAnd("icp.status = ", ItemContaPaciente.Status.CONFIRMADO.value());
        hql.addToOrder("cp.dataFechamento");
    }

    public List<ProducaoIpeHospHambDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
