package br.com.ksisolucoes.bo.service.sms;

import java.io.Serializable;

public class ConsultaAgendamentosViagemDTO implements Serializable {

    private String codigoRoteiro;
    private String dataSaida;
    private String codCadSus;
    private String nome;
    private String celular;
    private String  destino;
    private String  localEmbarque;

    public String getCodigoRoteiro() {
        return codigoRoteiro;
    }

    public void setCodigoRoteiro(String codigoRoteiro) {
        this.codigoRoteiro = codigoRoteiro;
    }

    public String getDataSaida() {
        return dataSaida;
    }

    public void setDataSaida(String dataSaida) {
        this.dataSaida = dataSaida;
    }

    public String getCodCadSus() {
        return codCadSus;
    }

    public void setCodCadSus(String codCadSus) {
        this.codCadSus = codCadSus;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getDestino() {
        return destino;
    }

    public void setDestino(String destino) {
        this.destino = destino;
    }

    public String getLocalEmbarque() {
        return localEmbarque;
    }

    public void setLocalEmbarque(String localEmbarque) {
        this.localEmbarque = localEmbarque;
    }
}
