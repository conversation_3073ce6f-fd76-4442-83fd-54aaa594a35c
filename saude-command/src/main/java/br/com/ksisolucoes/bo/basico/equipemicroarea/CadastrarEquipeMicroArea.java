/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.basico.equipemicroarea;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;

/**
 *
 * <AUTHOR>
 */
public class CadastrarEquipeMicroArea extends AbstractCommandTransaction {

    private EquipeMicroArea equipeMicroArea;

    public CadastrarEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        if (equipeMicroArea.getCodigo() == null) {
            EquipeMicroArea ema = LoadManager.getInstance(EquipeMicroArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA), equipeMicroArea.getMicroArea()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_AREA), equipeMicroArea.getEquipeArea()))
                    .start().getVO();

            if (ema != null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_micro_area_ja_existe"));
            }
        }
        BOFactory.save(equipeMicroArea);
    }
}
