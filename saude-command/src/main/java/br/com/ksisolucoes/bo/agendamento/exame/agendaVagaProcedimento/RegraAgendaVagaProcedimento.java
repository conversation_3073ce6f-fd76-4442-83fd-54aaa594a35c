package br.com.ksisolucoes.bo.agendamento.exame.agendaVagaProcedimento;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;

import java.util.List;

public interface RegraAgendaVagaProcedimento {

    AgendaGradeAtendimentoDTO executar(AgendaGradeAtendimentoDTO agenda, List<AgendaGradeAtendimentoPacienteDTO> horarios, int vagas);

}
