/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.hospital.pedido;


import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.pedido.dto.ItemPedidoHospitalDTO;
import br.com.ksisolucoes.bo.hospital.pedido.dto.PedidoHospitalDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.hospital.pedido.PedidoTransferenciaIntegracaoOcorrencia;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Marques
 */
public class SalvarDadosPedidoHospital extends AbstractCommandTransaction {

    private final PedidoHospitalDTO pedidoHospitalDTO;

    private PedidoTransferencia pedidoTransferencia;

    private PedidoTransferenciaIntegracaoOcorrencia pedidoTransferenciaIntegracaoOcorrencia = null;

    public SalvarDadosPedidoHospital(PedidoHospitalDTO pedidoHospitalDTO) {
        this.pedidoHospitalDTO = pedidoHospitalDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            String jsonRetorno = loadJson();
            loadPedidoTransferenciaIntegracaoOcorrencia();
            savePedidoTransferencia();
            saveItens();
            savePedidoTransferenciaIntegracoOcorrencia(jsonRetorno);
        } catch (Exception e) {
            throw new ValidacaoException(e);
        }
    }

    private void savePedidoTransferencia() throws DAOException, ValidacaoException {
        Empresa empresaOrigem = loadEmpresaOrigem();
        Empresa empresaDestino = loadEmpresaDestino();
        if (pedidoTransferenciaIntegracaoOcorrencia == null) {
            pedidoTransferencia = new PedidoTransferencia();
            pedidoTransferencia.setEmpresaOrigem(empresaOrigem);
            pedidoTransferencia.setEmpresaDestino(empresaDestino);
            pedidoTransferencia.setEmpresa(empresaDestino);
            Usuario usuario = new Usuario(Usuario.USUARIO_ADMINISTRADOR);
            pedidoTransferencia.setUsuario(usuario);
            pedidoTransferencia.setUsuarioIntegracao(pedidoHospitalDTO.getNomeUsuario());
            pedidoTransferencia.setDeposito(loadDeposito());
            pedidoTransferencia.setFlagVacina(RepositoryComponentDefault.NAO_LONG);
            pedidoTransferencia.setDataCadastro(new Date());
            pedidoTransferencia.setStatus(PedidoTransferencia.STATUS_SEPARACAO_NAO);
            pedidoTransferencia.setStatusSeparacao(PedidoTransferencia.STATUS_SEPARACAO_NAO);
            pedidoTransferencia.setTipo(PedidoTransferencia.Tipo.MENSAL.value());
        } else {
            pedidoTransferencia = loadPedidoTransferencia();
        }
        pedidoTransferencia.setDataPedido(pedidoHospitalDTO.getDataEnvio());

        pedidoTransferencia = BOFactory.save(pedidoTransferencia);
    }

    private void saveItens() throws DAOException, ValidacaoException {
        List<PedidoTransferenciaItem> itemList = new ArrayList<>();
        long numeroItem = 1L;
        for (ItemPedidoHospitalDTO item : pedidoHospitalDTO.getItemPedidoHospitalDTOList()) {
            PedidoTransferenciaItem pedidoTransferenciaItem = new PedidoTransferenciaItem();
            pedidoTransferenciaItem.setQuantidadeSolicitada(item.getQuantidadeSolicitada());
            pedidoTransferenciaItem.setQuantidade(item.getQuantidadeSolicitada());
            pedidoTransferenciaItem.setQuantidadeConsumo(item.getQuantidadeConsumoMesAnterior());
            pedidoTransferenciaItem.setEstoqueBaseHospitalar(item.getEstoqueBaseHospitalar());
            pedidoTransferenciaItem.setPedidoTransferencia(pedidoTransferencia);
            pedidoTransferenciaItem.setProduto(loadItem(item.getCdProdutoAmbulatorial()));
            pedidoTransferenciaItem.setItem(numeroItem++);
            pedidoTransferenciaItem.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value());
            pedidoTransferenciaItem.setDataCadastro(new Date());

            for (PedidoTransferenciaItem i : itemList) {
                if (i.getProduto().getReferencia().equals(item.getCdProdutoAmbulatorial())) {
                    throw new ValidacaoException(
                        "Erro ao receber pedido: \n" +
                        "Não é permitido inserir o mesmo produto no pedido, por favor avaliar!" + System.lineSeparator() +
                        "\n- Código do Produto Enviado: " + item.getCdProdutoHospital() + System.lineSeparator() +
                        "\n- Código do Produto Integração: " + item.getCdProdutoAmbulatorial()
                    );
                }
            }
            itemList.add(pedidoTransferenciaItem);
        }
        for (PedidoTransferenciaItem pedidoTransferenciaItem : itemList) {
            BOFactory.save(pedidoTransferenciaItem);
        }
    }

    private Produto loadItem(String codigo) {
        return LoadManager.getInstance(Produto.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_REFERENCIA, codigo))
                .start().getVO();
    }

    private void savePedidoTransferenciaIntegracoOcorrencia(String jsonRetorno) throws DAOException, ValidacaoException {
        Empresa empresaDestino = loadEmpresaDestino();
        PedidoTransferenciaIntegracaoOcorrencia pedidoTransferenciaIntegracaoOcorrencia;

        if (this.pedidoTransferenciaIntegracaoOcorrencia == null) {
            pedidoTransferenciaIntegracaoOcorrencia = new PedidoTransferenciaIntegracaoOcorrencia();
            pedidoTransferenciaIntegracaoOcorrencia.setCodigoPedidoTransferencia(pedidoHospitalDTO.getCodigoPedidoTransferencia());
            pedidoTransferenciaIntegracaoOcorrencia.setFlagStatus(pedidoHospitalDTO.getStatus());
            pedidoTransferenciaIntegracaoOcorrencia.setFlagStatusPedido(PedidoTransferencia.STATUS_ABERTO);
            pedidoTransferenciaIntegracaoOcorrencia.setOrigem(empresaDestino.getCodigo());
        } else {
            pedidoTransferenciaIntegracaoOcorrencia = this.pedidoTransferenciaIntegracaoOcorrencia;
        }

        pedidoTransferenciaIntegracaoOcorrencia.setDataEnvio(pedidoHospitalDTO.getDataEnvio());
        pedidoTransferenciaIntegracaoOcorrencia.setCodigoPedidoTransferenciaAmbulatorial(pedidoTransferencia.getCodigo());
        pedidoTransferenciaIntegracaoOcorrencia.setJsonRetorno(jsonRetorno);

        BOFactory.save(pedidoTransferenciaIntegracaoOcorrencia);
    }


    private void loadPedidoTransferenciaIntegracaoOcorrencia() {
        try {
            Empresa empresaDestino = loadEmpresaDestino(); // Carregar a empresa destino

            pedidoTransferenciaIntegracaoOcorrencia = LoadManager.getInstance(PedidoTransferenciaIntegracaoOcorrencia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            VOUtils.montarPath(PedidoTransferenciaIntegracaoOcorrencia.PROP_CODIGO_PEDIDO_TRANSFERENCIA),
                            pedidoHospitalDTO.getCodigoPedidoTransferencia()))
                    .addParameter(new QueryCustom.QueryCustomParameter(
                            VOUtils.montarPath(PedidoTransferenciaIntegracaoOcorrencia.PROP_ORIGEM),
                            empresaDestino.getCodigo())) // Filtro para empresa de origem
                    .start().getVO();
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }


    private PedidoTransferencia loadPedidoTransferencia() {
        return LoadManager.getInstance(PedidoTransferencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferencia.PROP_CODIGO),
                        pedidoTransferenciaIntegracaoOcorrencia.getCodigoPedidoTransferenciaAmbulatorial()))
                .start().getVO();
    }

    private Empresa loadEmpresaOrigem() throws ValidacaoException {
        try {
            return LoadManager.getInstance(Empresa.class)
                    .addProperty(Empresa.PROP_DESCRICAO)
                    .addProperty(Empresa.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_REFERENCIA), pedidoHospitalDTO.getOrigem()))
                    .start().getVO();
        } catch (Exception e) {
            throw new ValidacaoException(Bundle.getStringApplication("empresa_ref_nao_encontrada", pedidoHospitalDTO.getOrigem()));
        }
    }

    private Empresa loadEmpresaDestino() throws ValidacaoException {
        try {
            return LoadManager.getInstance(Empresa.class)
                    .addProperty(Empresa.PROP_DESCRICAO)
                    .addProperty(Empresa.PROP_CODIGO)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Empresa.PROP_REFERENCIA), pedidoHospitalDTO.getDestino()))
                    .start().getVO();
        } catch (Exception e) {
            throw new ValidacaoException(Bundle.getStringApplication("empresa_ref_nao_encontrada", pedidoHospitalDTO.getDestino()));
        }
    }

    private Deposito loadDeposito() {
        return LoadManager.getInstance(Deposito.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Deposito.PROP_CODIGO), 0L))
                .start().getVO();
    }

    private String loadJson() throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(pedidoHospitalDTO);
    }
}
