package br.com.ksisolucoes.bo.basico.enderecoestruturadologradouro;

import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaEndEstruturadoLogradouroDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoLogradouro;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaEnderecoEstruturadoLogradouro extends CommandQueryPager<QueryConsultaEnderecoEstruturadoLogradouro> {

    private QueryConsultaEndEstruturadoLogradouroDTOParam param;

    public QueryConsultaEnderecoEstruturadoLogradouro(QueryConsultaEndEstruturadoLogradouroDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("eel.codigo", true);
        hql.addToSelect("eel.descricao", true);
        hql.addToSelect("tipoLogradouro.codigo", "tipoLogradouro.codigo");

        hql.setTypeSelect(EnderecoEstruturadoLogradouro.class.getName());
        hql.addToFrom("EnderecoEstruturadoLogradouro eel" +
                " join eel.tipoLogradouro tipoLogradouro ");

        hql.addToWhereWhithAnd("eel.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("eel.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("eel.codigo || ' ' || eel.descricao", param.getKeyword()));
        if (param.getCidade() != null) {
            hql.addToWhereWhithAnd("exists(SELECT 1 FROM EnderecoEstruturado t1 "
                    + "join t1.bairro t2 "
                    + "WHERE t1.enderecoEstruturadoLogradouro = eel.codigo AND t2.cidade.codigo = :cidade)");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("eel." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("eel.descricao");
        }
    }

    @Override
    protected void setParameters(Query query) {
        if(param.getCidade() != null) {
            query.setParameter("cidade", param.getCidade().getCodigo());
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
