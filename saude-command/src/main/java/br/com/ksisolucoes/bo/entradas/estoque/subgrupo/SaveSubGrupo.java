/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.subgrupo;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 *
 */
public class SaveSubGrupo extends SaveVO{

    private SubGrupo subGrupo;

    /**
     *
     */
    public SaveSubGrupo(Object vo) {
        super( vo );
        this.subGrupo = (SubGrupo) vo;
    }

    protected void antesSave() throws ValidacaoException, DAOException {

        /*
         * Caso o cdigo do subgrupo no tenha sido definido pelo usurio
         *  o sistema verifica o ultimo cgido e atribui ao subgrupo.
         *
         */

        if (StringUtils.trimToNull(subGrupo.getDescricao()) == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_descricao")));
        }
        
        if (subGrupo.getFlagPosologia() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_posologia")));
        }
        
        if (subGrupo.getFlagControlado() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_controlado")));
        }
        
        if (subGrupo.getFlagControlaGrupoEstoque() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_controlada_grupo_estoque")));
        }
        
        if(this.subGrupo.getId().getCodigo() == null) {
            Long codigo = getIdToSubGrupo(new GrupoProduto(this.subGrupo.getId().getCodigoGrupoProduto()));
            this.subGrupo.getId().setCodigo(codigo);
        }
        if (this.subGrupo.getId().getCodigoGrupoProduto() == null) {
            if (this.subGrupo.getRoGrupoProduto() == null || this.subGrupo.getRoGrupoProduto().getCodigo() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_grupo_produto")));
            }
            this.subGrupo.getId().setCodigoGrupoProduto(this.subGrupo.getRoGrupoProduto().getCodigo());
        }
    }


    public Long getMaxId(GrupoProduto grupoProduto) throws DAOException {

        HQLHelper hql = new HQLHelper();

        hql.addToSelect("max(subGrupo.id.codigo)");
        hql.addToFrom(SubGrupo.class.getName(), "subGrupo");
        hql.addToWhereWhithAnd("subGrupo.roGrupoProduto.codigo = " + grupoProduto.getCodigo().toString());
        
        Long codigo = (Long) getSession().createQuery(hql.getQuery()).uniqueResult();

        if( codigo != null) {
            return codigo;
        }
        else {
        	return null;
        }
    }

	/* (non-Javadoc)
	 * @see br.com.ksisolucoes.dao.estoque.interfaces.SubGrupoDAOInterface#getIdToSubGrupo(br.com.ksisolucoes.vo.estoque.GrupoProduto)
	 */
    public Long getIdToSubGrupo(GrupoProduto grupoProduto) throws DAOException {
    	Long maxId = this.getMaxId(grupoProduto);

    	if(maxId != null) {
			long id = maxId.longValue();
    		id++;
    		return new Long(id);
    	}
    	else {
    		return new Long(1);
    	}
    }

}