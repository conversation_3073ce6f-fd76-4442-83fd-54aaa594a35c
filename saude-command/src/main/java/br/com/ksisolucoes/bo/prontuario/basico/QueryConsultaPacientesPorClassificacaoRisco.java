package br.com.ksisolucoes.bo.prontuario.basico;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.PacientesPorClassificacaoRiscoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

import java.util.List;
import java.util.Map;

/**
 * author: pedro.celk
 */
public class QueryConsultaPacientesPorClassificacaoRisco extends CommandQuery<QueryConsultaPacientesPorClassificacaoRisco> {

    private final AgendamentoListaEsperaDTOParam param;
    private String tipoControleRegulacao;

    private List<PacientesPorClassificacaoRiscoDTO> result;

    public QueryConsultaPacientesPorClassificacaoRisco(AgendamentoListaEsperaDTOParam param) {
        this.param = param;
    }

    /*
    AVISO:
    ao alterar esta consulta de contagem, alterar também a consulta da classe QueryConsultaAgendamentoListaEspera
     */
    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        getTipoControleRegulacao();
        hql.setTypeSelect(PacientesPorClassificacaoRiscoDTO.class.getName());

        hql.addToSelect("count(solicitacaoAgendamento.codigo)", "pacientes");

        hql.addToSelectAndGroup("classificacaoRisco.codigo", "classificacaoRisco.codigo");
        hql.addToSelectAndGroup("classificacaoRisco.descricao", "classificacaoRisco.descricao");
        hql.addToSelectAndGroup("classificacaoRisco.nivelGravidade", "classificacaoRisco.nivelGravidade");


        hql.addToFrom("SolicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.tipoProcedimento tipoProcedimento "
                + " left join solicitacaoAgendamento.procedimento procedimento "
                + " left join solicitacaoAgendamento.empresa empresa "
                + " left join solicitacaoAgendamento.usuarioCadsus usuarioCadsus "
                + " left join solicitacaoAgendamento.examePrestadorCompetencia examePrestadorCompetencia "
                + " left join solicitacaoAgendamento.unidadeOrigem unidadeOrigem "
                + " left join solicitacaoAgendamento.classificacaoRisco classificacaoRisco "
                + " left join solicitacaoAgendamento.subclassificacaoFilaEspera subclassificacaoFilaEspera"
                + " left join examePrestadorCompetencia.empresa empresaPrestador "
                + " left join solicitacaoAgendamento.profissional profissional "
                + " left join solicitacaoAgendamento.profissionalDesejado profissionalDesejado "
                + " left join solicitacaoAgendamento.cid cid ");

        hql.addToWhereWhithAnd("tipoProcedimento = ", param.getTipoProcedimento());
        hql.addToWhereWhithAnd("unidadeOrigem = ", param.getOrigemSolicitacao());
        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getCodigoPaciente());

        //Conforme SolicitacaoAgendamento.getSolicitarPrioridadeFormatadoData();
        if (param.getPrioridade() != null) {
            if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(tipoControleRegulacao)) {

                hql.addToWhereWhithAnd("(solicitacaoAgendamento.solicitarPrioridade = " + RepositoryComponentDefault.NAO_LONG
                        + " or solicitacaoAgendamento.flagAvaliacaoAprovado = " + RepositoryComponentDefault.SIM_LONG + ")");

                hql.addToWhereWhithAnd("solicitacaoAgendamento.flagDevolvido <> ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado <> ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.prioridade = ", param.getPrioridade());

            } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(tipoControleRegulacao)) {
                if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(param.getPrioridade())) { //ATENDIDA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagAvaliacaoAprovado <> ", RepositoryComponentDefault.NAO_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.prioridade = ", param.getPrioridade());
                } else if (SolicitacaoAgendamento.STATUS_BLOQUEADO.equals(param.getPrioridade())) { //BLOQUEADA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado = ", RepositoryComponentDefault.SIM_LONG);
                } else if (SolicitacaoAgendamento.STATUS_DEVOLVIDO.equals(param.getPrioridade())) { //DEVOLVIDA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagDevolvido =", RepositoryComponentDefault.SIM_LONG);
                } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(param.getPrioridade())) { //SOLICITADA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.solicitarPrioridade <> ", RepositoryComponentDefault.NAO_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagAvaliacaoAprovado <> ", RepositoryComponentDefault.SIM_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado <> ", RepositoryComponentDefault.SIM_LONG);
                }
            }
        }
        if (param.getProfissionalDesejado() != null) {
            hql.addToWhereWhithAnd("profissionalDesejado =", param.getProfissionalDesejado());
        }
        if (param.getCns() != null) {
            hql.addToWhereWhithAnd("(SELECT min(t1.numeroCartao) FROM UsuarioCadsusCns t1 WHERE t1.usuarioCadsus.codigo = usuarioCadsus.codigo AND t1.excluido = 0) = ", param.getCns());
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasBloqueada())){
            hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado = ", RepositoryComponentDefault.SIM_LONG);
        }
        if (param.isPrestador()) {
            if(param.getEmpresas() != null) {
                hql.addToWhereWhithAnd("empresaPrestador in ", param.getEmpresas());
            } else if (!param.isPermissaoEmpresa()){
                hql.addToWhereWhithAnd("empresaPrestador in ", param.getLstEmpresasUsuario());
            }
        } else {
            if(param.getEmpresas() != null) {
                hql.addToWhereWhithAnd("empresa in ", param.getEmpresas());
            } else if (!param.isPermissaoEmpresa()){
                hql.addToWhereWhithAnd("empresa in ", param.getLstEmpresasUsuario());
            }
        }

        if (param.getCodigoSolicitacao() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.codigo = ", new Long(param.getCodigoSolicitacao()));
        }

        if (param.getClassificacaoRisco() != null && param.getClassificacaoRisco().getCodigo() != null) {
            hql.addToWhereWhithAnd("classificacaoRisco.codigo = ", param.getClassificacaoRisco().getCodigo());
        }

        if (param.getSubClassificacao() != null && param.getSubClassificacao().getCodigo() != null) {
            hql.addToWhereWhithAnd("subclassificacaoFilaEspera.codigo = ", param.getSubClassificacao().getCodigo());
        }

        if (!param.isPermissaoTipoProcedimento() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoAgenda tipoProcAgend left join tipoProcAgend.tipoProcedimento tipoProc left join tipoProcAgend.empresa emp ");
            sb.append("where tipoProc.codigo = tipoProcedimento.codigo ");
            sb.append("and emp.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (!param.isPermissaoVisualizarTipoProcAgend() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoEmpresaAgendar tipoProcEmpAgend left join tipoProcEmpAgend.tipoProcedimento tipoProc2 left join tipoProcEmpAgend.empresa emp2 ");
            sb.append("where tipoProc2.codigo = tipoProcedimento.codigo ");
            sb.append("and emp2.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        hql.addToWhereWhithAnd("usuarioCadsus.codigo =", param.getCodigoPaciente());

        if (param.getPaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" usuarioCadsus.nome", param.getPaciente(), true)
                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked(" usuarioCadsus.apelido", param.getPaciente(), true) + "))");
        }
        hql.addToWhereWhithAnd(hql.getConsultaLiked("solicitacaoAgendamento.numeracaoAuxiliar ", param.getNumeracaoAuxiliar()));
        hql.addToWhereWhithAnd("usuarioCadsus.dataNascimento = ", param.getDataNascimento());
        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);
        hql.addToWhereWhithAnd("procedimento = ", param.getProcedimento());
        if (param.getTipoConsulta() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoConsulta in ", param.getTipoConsulta());
        }

        if (param.getApenasBloqueada() == null || RepositoryComponentDefault.NAO_LONG.equals(param.getApenasBloqueada())) {
            if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasReagendamento())) {
                hql.addToWhereWhithAnd("tipoProcedimento.flagReagendamento = ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.status in (:statusList)");
            } else {
                hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", param.getSituacaoList());
            }
        }

        String ordenarFaixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("OrdenarFaixaEtaria");
        FaixaEtaria faixaEtaria = null;
        if (Coalesce.asString(ordenarFaixaEtaria, RepositoryComponentDefault.NAO).equals(RepositoryComponentDefault.SIM)) {
            faixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("FaixaEtaria");
            if (faixaEtaria != null) {
                hql.addToFrom("FaixaEtariaItem faixaEtariaItem");
                hql.addToWhereWhithAnd("faixaEtariaItem.id.faixaEtaria = ", faixaEtaria);
                hql.addToWhereWhithAnd("extract(year from age(usuarioCadsus.dataNascimento)) * 12 + extract(month from age(usuarioCadsus.dataNascimento)) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal");
            }
        }

        if (param.getExameProcedimento() != null) {
            hql.addToWhereWhithAnd(" exists(select 1 from SolicitacaoAgendamentoExame sae left join sae.solicitacaoAgendamento sa left join sae.exameProcedimento ep WHERE sa.codigo = solicitacaoAgendamento.codigo AND ep.codigo = " + param.getExameProcedimento().getCodigo() + ") ");
        }

        hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila = ", Coalesce.asLong(param.getTipoFila(), SolicitacaoAgendamento.TIPO_FILA_NORMAL));

        param.setParametroGem(tipoControleRegulacao);
    }

    public void getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<PacientesPorClassificacaoRiscoDTO> getResult() {
        return result;
    }

    public Integer getQuantidade() {
        return result.size();
    }
}
