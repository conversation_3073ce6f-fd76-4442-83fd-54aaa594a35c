/*
 * QueryAnaliseEstoque.java
 *
 * Created on 07 de Novembro de 2005,13:52
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */

package br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamentoitem;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;

/**
 * Retorna os itens pendentes para um determinado usurio do sus. Este processo
 *  utilizado para a efetivao das dispensaes ainda em aberto.
 *
 * <AUTHOR> Graciano
 */
public class QueryDispensacaoMedicamentoItemForLiberacaoItem extends CommandQuery<QueryDispensacaoMedicamentoItemForLiberacaoItem> {
    
    private UsuarioCadsus usuarioCadsus;
    private Produto produto;
    
    private List<DispensacaoMedicamentoItem> listDispensacaoMedicamentoItem;
    
    /**
     * Creates a new instance of QueryResumoConsumoProduto
     */
    public QueryDispensacaoMedicamentoItemForLiberacaoItem( UsuarioCadsus usuarioCadsus, Produto produto ){
        this.usuarioCadsus = usuarioCadsus;
        this.produto = produto;
    }

    @Override
    protected void customProcess(org.hibernate.Session session) throws ValidacaoException, DAOException {
//        Long limiteUltimaDispensacao = CargaBasicoPadrao.getInstance().getParametroPadrao().getIntervaloLimiteDispensacao();
        List< Long > empresas = null;
        try {
            
            if ( this.sessao.<br.com.ksisolucoes.vo.controle.Usuario>getUsuario() != null){

                Usuario usuario = this.sessao.<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();
                if(usuario.getEmpresasUsuario() == null && !usuario.isNivelMaster() ){
                    usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                }

                empresas = this.sessao.<br.com.ksisolucoes.vo.controle.Usuario>getUsuario().getEmpresasUsuario();
            }
        } catch (SGKException ex) {
            Loggable.log.warn( ex.getMessage(), ex );
        }
         /*
          * MONTAGEM DA QUERY
          * -----------------
          *---------------------------------------------------------------------*/
        LoadManager load = LoadManager.getInstance(DispensacaoMedicamentoItem.class);

        load.addProperties(new HQLProperties(DispensacaoMedicamentoItem.class).getProperties())
            .addProperties(new HQLProperties(DispensacaoMedicamento.class,VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO)).getProperties())
            .addProperties(new HQLProperties(Produto.class,VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO)).getProperties())
            .addParameter(new QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_USUARIO_CADSUS_DESTINO,UsuarioCadsus.PROP_CODIGO), this.usuarioCadsus.getCodigo()))
            .addParameter(new QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO,Produto.PROP_CODIGO), this.produto.getCodigo()))
            .addParameter(new QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO), QueryParameter.MAIOR, Data.getDataAtual()))
//            .addSorter(new QueryCustomSorter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO,DispensacaoMedicamento.PROP_ID,DispensacaoMedicamentoPK.PROP_CODIGO)))
//            .addSorter(new QueryCustomSorter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_ITEM)))
            .addSorter(new QueryCustomSorter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DATA_ULTIMA_DISPENSACAO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
            .addInterceptor(new LoadInterceptor() {
            public void customHQL(HQLHelper hqlh, String alias) {
                hqlh.addToWhereWhithAnd(alias+".id not in (select lr.dispensacaoMedicamentoItem.id from LiberacaoReceita lr where lr.dispensacaoMedicamentoItem.id = "+alias+".id " +
                        " and lr.status = "+LiberacaoReceita.STATUS_ABERTO+")");
            }
        });

//        if ( empresas != null &&
//                empresas.size() > 0 ){
//            load.addParameter(new QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO,DispensacaoMedicamento.PROP_EMPRESA,Empresa.PROP_CODIGO), QueryParameter.IN,empresas));
//        }

        listDispensacaoMedicamentoItem = load.start().getList();


//        HQLHelper hql = new HQLHelper();
//
//        hql.addToSelect("i.id");
//        hql.addToSelect("p");
//        hql.addToSelect("i.quantidadePrescrita");
//        hql.addToSelect("i.quantidadeDispensada");
//        hql.addToSelect("i.status");
//        hql.addToSelect("i.dataValidadeReceita");
//        hql.addToSelect("i.dataProximaDispensacao");
//
//        /*....................................*/
//        hql.setTypeSelect(DispensacaoMedicamentoItem.class.getName());
//
//        hql.addToFrom( DispensacaoMedicamentoItem.class.getName() + " i join i.produto p " );
//
//        if ( empresas != null &&
//                empresas.size() > 0 ){
//            hql.addToWhereWhithAnd("i.roDispensacaoMedicamento.roEmpresa.codigo in ( :empresas )");
//        }
//        hql.addToWhereWhithAnd("i.usuarioCadsusDestino.codigo = :usuarioCadsus");
//        hql.addToWhereWhithAnd("p.codigo = :produto");
//        hql.addToWhereWhithAnd("i.id not in (select lr.dispensacaoMedicamentoItem.id from LiberacaoReceita lr where lr.dispensacaoMedicamentoItem.id = i.id)");
//        hql.addToWhereWhithAnd("i.id.dispensacaoMedicamento.dataDispensacao >= :dataDispensacao ");
//
//        hql.addToOrder("i.id.dispensacaoMedicamento.id.codigo");
//        hql.addToOrder("i.id.item");
//
        /*
         * PEGA A SESSION
         * --------------
         *---------------------------------------------------------------------*/
//        Session session = null;
//        try {
//            session = br.com.ksisolucoes.server.HibernateSessionFactory.getSession();
//        } catch (DAOException e) {
//            throw new DAOException(e);
//        }
//        /*--------------------------------------------------------------------*/
//        Query query = session.createQuery(hql.getQuery());
//        Loggable.log.debug( query.toString() );
//        /*-------PARAMETROS-------*/
//        if ( empresas != null &&
//                empresas.size() > 0 ){
//            hql.setParameterValue(query, "empresas", empresas);
//        }
//        hql.setParameterValue(query, "usuarioCadsus", this.usuarioCadsus.getCodigo());
//        hql.setParameterValue(query, "produto", this.produto.getCodigo());
//        hql.setParameterValue(query, "dataDispensacao", Data.removeDias(Data.getDataAtual(), limiteUltimaDispensacao.intValue()) );
        /*
         * EXECUTA A QUERY
         * ---------------
         *---------------------------------------------------------------------*/
//        this.listDispensacaoMedicamentoItem = query.list();
        /*--------------------------------------------------------------------*/
           /*
            * CONTROLE DA SESSION
            * -------------------
            * Realiza o fechamento e desconxo da session.
            *---------------------------------------------------------------------*/
//        session.disconnect();
//        session.close();
        /*---------------------------------------------------------------------*/
        
        //Collections.sort(this.listRelatorioAnaliseEstoque);
    }
    
    public List<DispensacaoMedicamentoItem> getListDispensacaoMedicamento(){
        return listDispensacaoMedicamentoItem;
    }
    
}

