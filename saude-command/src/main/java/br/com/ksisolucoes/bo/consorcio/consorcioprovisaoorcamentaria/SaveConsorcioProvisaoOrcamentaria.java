package br.com.ksisolucoes.bo.consorcio.consorcioprovisaoorcamentaria;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioProvisaoOrcamentaria;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SaveConsorcioProvisaoOrcamentaria extends SaveVO<ConsorcioProvisaoOrcamentaria> {

    public SaveConsorcioProvisaoOrcamentaria(ConsorcioProvisaoOrcamentaria vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        this.vo.setDataUsuario(Data.getDataAtual());
    }

}
