package br.com.ksisolucoes.bo.entradas.estoque.tipodocumento;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaTipoDocumentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaTipoDocumento extends CommandQueryPager<QueryConsultaTipoDocumento> {

    private QueryConsultaTipoDocumentoDTOParam param;

    public QueryConsultaTipoDocumento(QueryConsultaTipoDocumentoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("tp.codigo", true);
        hql.addToSelect("tp.descricao", true);
        hql.addToSelect("tp.flagTipoMovimento", true);
        
        hql.setTypeSelect(TipoDocumento.class.getName());
        hql.addToFrom("TipoDocumento tp");
        
        hql.addToWhereWhithAnd("tp.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tp.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tp.codigo || ' ' || tp.descricao",param.getKeyword()));
        
        if (param.isApenasInterno()) {
            hql.addToWhereWhithAnd("tp.flagInterno = ", RepositoryComponentDefault.SIM);
        }
        
        if (param.isApenasNFEntrada()) {
            hql.addToWhereWhithAnd("tp.flagNFEntrada = ", RepositoryComponentDefault.SIM);
        }
        
        if (param.isPossuiTipoMovimentacaoVacina()) {
            hql.addToWhereWhithAnd("tp.tipoMovimentacaoVacina is not null");
        }
        
        if(param.getPropSort() != null){
            hql.addToOrder("tp."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("tp.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
