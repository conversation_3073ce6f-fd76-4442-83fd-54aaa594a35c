package br.com.ksisolucoes.bo.hospital.faturamento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.LoadInterceptorTipoAtendimentoOnAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServico;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoPK;
import java.util.Date;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class GerarItemContaFromExame extends AbstractCommandTransaction<GerarItemContaFromExame> {

    private final List<ExameRequisicao> requisicoes;

    public GerarItemContaFromExame(List<ExameRequisicao> requisicoes) {
        this.requisicoes = requisicoes;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        final Atendimento atendimento = requisicoes.get(0).getExame().getAtendimento();

        if(atendimento == null){
            return;
        }

        ContaPaciente contaPaciente = BOFactory.getBO(HospitalFacade.class).encontrarContaPaciente(atendimento);

        if (contaPaciente == null) {
            return;
        }

        for (final ExameRequisicao exameRequisicao : requisicoes) {
            Criteria cItConta = this.getSession().createCriteria(ItemContaPaciente.class);
            cItConta.add(Restrictions.eq(ItemContaPaciente.PROP_ATENDIMENTO, atendimento));
            cItConta.add(Restrictions.eq(ItemContaPaciente.PROP_EXAME_PROCEDIMENTO, exameRequisicao.getExameProcedimento()));
            cItConta.add(Restrictions.eq(ItemContaPaciente.PROP_TIPO, ItemContaPaciente.Tipo.EXAME.value()));
            cItConta.createCriteria(ItemContaPaciente.PROP_CONTA_PACIENTE)
                .add(Restrictions.eq(ContaPaciente.PROP_CONVENIO, atendimento.getConvenio()));
            ItemContaPaciente itemContaPaciente = (ItemContaPaciente) cItConta.uniqueResult();

            if (itemContaPaciente == null) {
                itemContaPaciente = new ItemContaPaciente();
            }

            itemContaPaciente.setContaPaciente(contaPaciente);
            itemContaPaciente.setExameRequisicao(exameRequisicao);

            itemContaPaciente.setAtendimento(atendimento);
            
            itemContaPaciente.setExameProcedimento(exameRequisicao.getExameProcedimento());
            itemContaPaciente.setProcedimento(exameRequisicao.getExameProcedimento().getProcedimento());

            TipoAtendimento tipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                    .addProperty(TipoAtendimento.PROP_CODIGO)
                    .addInterceptor(new LoadInterceptorTipoAtendimentoOnAtendimento(atendimento.getAtendimentoPrincipal().getCodigo()))
                    .start().getVO();

            Double preco = BOFactory.getBO(HospitalFacade.class).getPrecoProcedimento(exameRequisicao.getExameProcedimento().getProcedimento(), atendimento.getConvenio(), tipoAtendimento.getCodigo());

            if (preco != null) {
                itemContaPaciente.setPrecoUnitario(preco);
            }

            itemContaPaciente.setQuantidade(exameRequisicao.getQuantidade().doubleValue());

            itemContaPaciente.setTipo(ItemContaPaciente.Tipo.EXAME.value());
            itemContaPaciente.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.EXAME.value());
            itemContaPaciente.setStatus(ItemContaPaciente.Status.ABERTO.value());
            itemContaPaciente.setUsuario(this.getSessao().getUsuario());
            itemContaPaciente.setDataUsuario(DataUtil.getDataAtual());

            Empresa empresaPrincipal = atendimento.getEmpresa();
            if (empresaPrincipal.getEmpresaPrincipal() != null) {
                empresaPrincipal = empresaPrincipal.getEmpresaPrincipal();
            }

            final Date dataCompetencia = CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento();
            Long count = LoadManager.getInstance(ProcedimentoServico.class)
                    .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(ProcedimentoServico.PROP_ID, ProcedimentoServicoPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), BuilderQueryCustom.QueryGroup.COUNT))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoServico.PROP_ID, ProcedimentoServicoPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), exameRequisicao.getExameProcedimento().getProcedimento()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoServico.PROP_ID, ProcedimentoServicoPK.PROP_PROCEDIMENTO_COMPETENCIA, ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), dataCompetencia))
                    .start().getVO();
            if (count > 0L) {
                List<EmpresaServicoClassificacao> escList = LoadManager.getInstance(EmpresaServicoClassificacao.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaServicoClassificacao.PROP_EMPRESA), empresaPrincipal))
                        .addInterceptor(new LoadInterceptor() {
                            @Override
                            public void customHQL(HQLHelper hql, String alias) {
                                String exists = "(exists(select 1 from ProcedimentoServicoClassificacao psc, ProcedimentoServico ps "
                                + " where psc = ps.id.procedimentoServicoClassificacao"
                                + " and " + alias + ".procedimentoServicoClassificacao = psc"
                                + " and ps.id.procedimentoCompetencia.id.procedimento.codigo = " + exameRequisicao.getExameProcedimento().getProcedimento().getCodigo()
                                + " and ps.id.procedimentoCompetencia.id.dataCompetencia = :dataCompetencia))";

                                hql.addToWhereWhithAnd(exists);
                                hql.getRestrictions().getRestrictions().put("dataCompetencia", dataCompetencia);
                            }
                        }).start().getList();
                if (escList.size() == 1) {
                    itemContaPaciente.setEmpresaPrestador(escList.get(0).getEmpresaTerceiro());
                }
            }

            BOFactory.save(itemContaPaciente);
        }
    }

}
