package br.com.ksisolucoes.bo.rnds;

import br.com.celk.integracao.dto.CredenciaisRndsDto;
import br.com.celk.rnds.IntegradorRndsService;
import br.com.celk.rnds.rnds.IntegradorRNDSHelper;
import br.com.celk.service.CelkMicroServicosService;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

public class SalvarCredenciaisRndsDynamo extends AbstractCommandTransaction<SalvarCredenciaisRndsDynamo> {
    private CredenciaisRndsDto credenciaisRndsDto;

    public SalvarCredenciaisRndsDynamo(CredenciaisRndsDto credenciaisRndsDto) {
        this.credenciaisRndsDto = credenciaisRndsDto;
        this.credenciaisRndsDto.setTenant(TenantContext.getRealContext());
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        validarParametrosRnds(credenciaisRndsDto);
        credenciaisRndsDto.setCnsSolicitante(credenciaisRndsDto.getCnsSolicitante().replace(".", ""));
        IntegradorRNDSHelper.postCredenciais(RndsUtil.convertDtoToJson(credenciaisRndsDto));
        CelkMicroServicosService.removeInstance(TenantContext.getRealContext());
    }

    private void validarParametrosRnds(CredenciaisRndsDto parametroRnds) throws ValidacaoException {
        if (parametroRnds.getCnsSolicitante() == null) {
            throw new ValidacaoException("Obrigatório informar um CNS do solicitante.");
        }
        if (parametroRnds.getUfSolicitante() == null) {
            throw new ValidacaoException("Obrigatório informar a UF do solicitante.");
        }
        if (parametroRnds.getCertificado() == null && parametroRnds.isNew()) {
            throw new ValidacaoException("Obrigatório anexar um certificado digital.");
        }
        if (parametroRnds.getPasswordCertificate() == null && parametroRnds.isNew()) {
            throw new ValidacaoException("Obrigatório informar a senha para o certificado digital.");
        }
    }
}
