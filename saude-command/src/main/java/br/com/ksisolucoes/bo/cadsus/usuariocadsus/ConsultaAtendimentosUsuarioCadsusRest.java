/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.celk.bo.service.rest.usuariocadsus.AtendimentosPacienteRestDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import java.util.Map;

/** 
 *
 * <AUTHOR>
 */
public class ConsultaAtendimentosUsuarioCadsusRest extends CommandQuery<ConsultaAtendimentosUsuarioCadsusRest> {

    private List<AtendimentosPacienteRestDTO> result;

    public ConsultaAtendimentosUsuarioCadsusRest() {
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        AtendimentosPacienteRestDTO p = on(AtendimentosPacienteRestDTO.class);
        
        hql.addToSelect("atendimento.usuarioCadsus.codigo", path(p.getCodigoPaciente()));
        hql.addToSelect("count(*)", path(p.getQuantidade()));
        
        hql.setTypeSelect(AtendimentosPacienteRestDTO.class.getName());
        hql.addToFrom("Atendimento atendimento");
        
        hql.addToGroup("atendimento.usuarioCadsus.codigo");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<AtendimentosPacienteRestDTO> getResult() {
        return result;
    }
}
