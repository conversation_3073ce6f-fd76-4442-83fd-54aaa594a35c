/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacaoitem;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProdutoPedidoTransferenciaLicitacaoItem extends CommandQueryPager<QueryConsultaProdutoPedidoTransferenciaLicitacaoItem> {

    private QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam param;

    public QueryConsultaProdutoPedidoTransferenciaLicitacaoItem(QueryConsultaProdutoPedidoTransferenciaLicitacaoItemDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("produto.codigo", true);
        hql.addToSelect("produto.referencia", true);
        hql.addToSelect("produto.descricao", true);
        hql.addToSelect("unidade.descricao", "unidade.descricao");
        hql.addToSelect("unidade.unidade", "unidade.unidade");
        
        hql.addToSelect("subGrupo.id.codigo", "subGrupo.id.codigo");
        hql.addToSelect("subGrupo.id.codigoGrupoProduto", "subGrupo.id.codigoGrupoProduto");
        hql.addToSelect("subGrupo.flagControlaGrupoEstoque", "subGrupo.flagControlaGrupoEstoque");
        hql.addToSelect("subGrupo.descricao", "subGrupo.descricao");
        hql.addToSelect("grupoProduto.codigo", "subGrupo.roGrupoProduto.codigo");
        hql.addToSelect("grupoProduto.descricao", "subGrupo.roGrupoProduto.descricao");

        hql.setTypeSelect(Produto.class.getName());
        hql.setFrom("PedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem"
                + " left join pedidoTransferenciaLicitacaoItem.produto produto"
                + " left join pedidoTransferenciaLicitacaoItem.pedidoTransferenciaLicitacao pedidoTransferenciaLicitacao"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto"
                + " left join produto.unidade unidade");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("produto.descricao", this.param.getKeyword(), false, true));
        hql.addToWhereWhithAnd("subGrupo = ", this.param.getSubGrupo() );
        hql.addToWhereWhithAnd("grupoProduto = ", this.param.getGrupoProduto() );
        hql.addToWhereWhithAnd("pedidoTransferenciaLicitacao = ", this.param.getPedidoTransferenciaLicitacao() );
        if (param.getStatus()!=null) {
            hql.addToWhereWhithAnd("pedidoTransferenciaLicitacaoItem.status = ", param.getStatus().value() );
        }
        
        if(param.getPropSort() != null){
            hql.addToOrder("produto."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("produto.descricao");
        }
        
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

}
