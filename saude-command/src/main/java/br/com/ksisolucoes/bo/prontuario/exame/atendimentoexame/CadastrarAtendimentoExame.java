/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.prontuario.exame.atendimentoexame;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem;
import java.util.List;

/**
 * <AUTHOR>
 * Criado em: Jun 4, 2013
 */
public class CadastrarAtendimentoExame extends AbstractCommandTransaction{

    private AtendimentoExame atendimentoExame;
    private List<AtendimentoExameItem> valueObjects;

    public CadastrarAtendimentoExame(AtendimentoExame atendimentoExame, List<AtendimentoExameItem> valueObjects) {
        this.atendimentoExame = atendimentoExame;
        this.valueObjects = valueObjects;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        atendimentoExame = BOFactory.save(atendimentoExame);
        
        List<AtendimentoExameItem> itens = LoadManager.getInstance(AtendimentoExameItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoExameItem.PROP_ATENDIMENTO_EXAME, atendimentoExame))
                .start().getList();
        itens.removeAll(valueObjects);
        for (AtendimentoExameItem atendimentoExameItem : itens) {
            atendimentoExameItem.setStatus(AtendimentoExameItem.StatusAtendimentoExame.CANCELADO.value());
            atendimentoExameItem.setDataCancelamento(DataUtil.getDataAtual());
            atendimentoExameItem.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());
            
            BOFactory.save(atendimentoExameItem);
        }
        
        for (AtendimentoExameItem atendimentoExameItem : valueObjects) {
            atendimentoExameItem.setAtendimentoExame(atendimentoExame);
            
            atendimentoExameItem.setDataCadastro(DataUtil.getDataAtual());
            atendimentoExameItem.setStatus(AtendimentoExameItem.StatusAtendimentoExame.NORMAL.value());
            atendimentoExameItem.setUsuario(getSessao().<Usuario>getUsuario());
            
            BOFactory.save(atendimentoExameItem);
        }
    }
    
    public Long getCodigoAtendimentoExame() {
        return atendimentoExame.getCodigo();
    }
}
