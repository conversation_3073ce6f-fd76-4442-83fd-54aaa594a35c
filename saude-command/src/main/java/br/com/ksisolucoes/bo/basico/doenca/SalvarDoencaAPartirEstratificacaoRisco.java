package br.com.ksisolucoes.bo.basico.doenca;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarDoencaAPartirEstratificacaoRisco extends AbstractCommandTransaction {

    private final UsuarioCadsus usuarioCadsus;
    private final Doenca doenca;
    private final Atendimento atendimento;

    public SalvarDoencaAPartirEstratificacaoRisco(UsuarioCadsus usuarioCadsus, Doenca doenca, Atendimento atendimento) {
        this.usuarioCadsus = usuarioCadsus;
        this.doenca = doenca;
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (doenca != null) {
            Doenca doencaComCampoDoencaPrincipal = LoadManager.getInstance(Doenca.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Doenca.PROP_CODIGO, doenca.getCodigo()))
                    .start().getVO();

            if (doencaComCampoDoencaPrincipal.getDoencaPrincipal() != null)
                VOUtils.removerListaVos(UsuarioCadsusDoenca.class,
                        new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()),
                        new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDoenca.PROP_DOENCA, Doenca.PROP_DOENCA_PRINCIPAL, Doenca.PROP_CODIGO), doencaComCampoDoencaPrincipal.getDoencaPrincipal().getCodigo())
                );
        }
        List<UsuarioCadsusDoenca> doencas = LoadManager.getInstance(UsuarioCadsusDoenca.class)
                .addProperties(new HQLProperties(UsuarioCadsusDoenca.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), atendimento.getUsuarioCadsus().getCodigo()))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(doencas)) {
            boolean adicionar = true;
            for (UsuarioCadsusDoenca doenca : doencas) {
                if (doenca.getCodigo().equals(doenca.getCodigo())) {
                    adicionar = false;
                }
            }
            if (adicionar) {
                UsuarioCadsusDoenca usuarioCadsusDoenca = new UsuarioCadsusDoenca();
                usuarioCadsusDoenca.setDoenca(doenca);
                usuarioCadsusDoenca.setUsuarioCadsus(atendimento.getUsuarioCadsus());
                BOFactory.save(usuarioCadsusDoenca);
            }

        } else {
            UsuarioCadsusDoenca usuarioCadsusDoenca = new UsuarioCadsusDoenca();
            usuarioCadsusDoenca.setDoenca(doenca);
            usuarioCadsusDoenca.setUsuarioCadsus(atendimento.getUsuarioCadsus());
            BOFactory.save(usuarioCadsusDoenca);
        }
    }

}
