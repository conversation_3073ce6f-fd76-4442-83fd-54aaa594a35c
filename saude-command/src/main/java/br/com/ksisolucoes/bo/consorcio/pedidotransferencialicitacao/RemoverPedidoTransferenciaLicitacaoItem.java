package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 * Created by sulivan on 12/06/18.
 */
public class RemoverPedidoTransferenciaLicitacaoItem extends AbstractCommandTransaction {

    private PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;
    private PedidoTransferenciaLicitacaoItem item;

    public RemoverPedidoTransferenciaLicitacaoItem(PedidoTransferenciaLicitacaoItem item) {
        this.item = item;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        pedidoTransferenciaLicitacao = item.getPedidoTransferenciaLicitacao();
        boolean utilizarControleFinanceiro = false;
        Conta conta = null;
        SubConta subConta = null;

        if(item.getPedidoTransferenciaLicitacao().getSubContaMedicamento() != null && item.getPedidoTransferenciaLicitacao().getSubContaMedicamento().getCodigo() != null){
            utilizarControleFinanceiro = true;

            subConta = HibernateUtil.lockTable(SubConta.class, item.getPedidoTransferenciaLicitacao().getSubContaMedicamento().getCodigo());
            conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

            pedidoTransferenciaLicitacao = (PedidoTransferenciaLicitacao) getSession().get(PedidoTransferenciaLicitacao.class, item.getPedidoTransferenciaLicitacao().getCodigo());

            if(PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.APROVADO.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())) {
                List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE_ENVIADA)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(itemList)) {
                    BOFactory.getBO(ConsorcioFacade.class).removerValorReservado(new ValorReservadoDTO(subConta, pedidoTransferenciaLicitacao.getAnoCadastro(), Coalesce.asDouble(Lambda.sum(itemList, Lambda.on(PedidoTransferenciaLicitacaoItem.class).getTotalItem()))));
                }
            }
        }

        item.setDataCancelamento(DataUtil.getDataAtual());
        item.setUsuarioCancelamento(SessaoAplicacaoImp.getInstance().getUsuario());
        item.setStatus(PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.CANCELADO.value());
        BOFactory.save(item);

        if(utilizarControleFinanceiro){
            pedidoTransferenciaLicitacao = BOFactory.getBO(ConsorcioFacade.class).atualizarSituacaoControleFinanceiroPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacao, subConta);
        }
    }

    public PedidoTransferenciaLicitacao getPedidoTransferenciaLicitacao() {
        return pedidoTransferenciaLicitacao;
    }
}
