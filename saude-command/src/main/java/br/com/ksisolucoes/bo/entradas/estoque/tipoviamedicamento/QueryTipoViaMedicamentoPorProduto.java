/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.entradas.estoque.tipoviamedicamento;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import java.util.List;
import java.util.Map;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryTipoViaMedicamentoPorProduto extends CommandQuery<QueryTipoViaMedicamentoPorProduto> {

    private String codigoProduto;

    private List<TipoViaMedicamento> tipoViaMedicamentos;

    public QueryTipoViaMedicamentoPorProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    @Override
    protected void createQuery(HQLHelper hql) {


        for (String path : new HQLProperties(TipoViaMedicamento.class).getProperties()) {
            hql.addToSelect("tipoViaMedicamento." + path, path);
        }

        hql.setTypeSelect(TipoViaMedicamento.class.getName());
        hql.addToFrom("ProdutoTipoVia produtoTipoVia "
                + " join produtoTipoVia.produto produto "
                + " join produtoTipoVia.tipoViaMedicamento tipoViaMedicamento ");

        if (this.codigoProduto != null)
        hql.addToWhereWhithAnd("produto.codigo = ", this.codigoProduto);

        hql.addToOrder("tipoViaMedicamento.ordem" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_LAST);
        hql.addToOrder("tipoViaMedicamento.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.tipoViaMedicamentos = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (this.tipoViaMedicamentos == null || this.tipoViaMedicamentos.isEmpty()) {
            this.tipoViaMedicamentos = LoadManager.getInstance(TipoViaMedicamento.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoViaMedicamento.PROP_ORDEM, QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_LAST))
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoViaMedicamento.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();
        }
    }

    public List<TipoViaMedicamento> getTipoViaMedicamentos() {
        return tipoViaMedicamentos;
    }

}
