package br.com.ksisolucoes.bo.encaminhamento;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.encaminhamento.interfaces.dto.EncaminhamentoDTO;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusOcorrencia;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Encaminhamento;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ConfirmarContatoEncaminhamento extends AbstractCommandTransaction {

    private Encaminhamento e;
    private Long tipoOcorrencia;

    public ConfirmarContatoEncaminhamento(Encaminhamento encaminhamento, Long tipoOcorrencia) {
        this.e = encaminhamento;
        this.tipoOcorrencia = tipoOcorrencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Encaminhamento encaminhamento = (Encaminhamento) getSession().get(Encaminhamento.class, e.getCodigo());
        
        Date dataAtual = Data.getDataAtual();

        encaminhamento.setDataContatoPaciente(dataAtual);
        encaminhamento.setUsuarioContato((Usuario) getSessao().getUsuario());

        UsuarioCadsusOcorrencia usuarioCadsusOcorrencia = new UsuarioCadsusOcorrencia();

        usuarioCadsusOcorrencia.setDataOcorrencia(dataAtual);
        usuarioCadsusOcorrencia.setUsuario((Usuario) getSessao().getUsuario());
        usuarioCadsusOcorrencia.setUsuarioCadsus(encaminhamento.getUsuarioCadsus());
        usuarioCadsusOcorrencia.setTipoOcorrencia(new TipoOcorrencia(tipoOcorrencia));
        if (Encaminhamento.STATUS_NAO_AUTORIZADO.equals(encaminhamento.getStatus())) {
            usuarioCadsusOcorrencia.setDescricao(Bundle.getStringApplication("rotulo_paciente_informado"));
        } else {
            usuarioCadsusOcorrencia.setDescricao(Bundle.getStringApplication("rotulo_paciente_confirmado"));
        }
        usuarioCadsusOcorrencia.setEncaminhamento(encaminhamento);

        BOFactory.getBO(CadastroFacade.class).save(usuarioCadsusOcorrencia);
    }

}
