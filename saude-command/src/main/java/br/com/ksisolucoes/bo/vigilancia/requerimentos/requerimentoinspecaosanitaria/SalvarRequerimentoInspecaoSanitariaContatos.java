package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoinspecaosanitaria;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.PessoaContactadaEmpresaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaPessoaContato;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;

import java.util.List;

public class SalvarRequerimentoInspecaoSanitariaContatos extends AbstractCommandTransaction<SalvarRequerimentoInspecaoSanitariaContatos> {

    private RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria;
    private List<PessoaContactadaEmpresaDTO> contatos;
    private List<PessoaContactadaEmpresaDTO> contatosExcluir;

    public SalvarRequerimentoInspecaoSanitariaContatos(RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria, List<PessoaContactadaEmpresaDTO> contatos, List<PessoaContactadaEmpresaDTO> contatosExcluir) {
        this.requerimentoInspecaoSanitaria = requerimentoInspecaoSanitaria;
        this.contatos = contatos;
        this.contatosExcluir = contatosExcluir;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoInspecaoSanitariaPessoaContato contato;
        for (PessoaContactadaEmpresaDTO dto : contatos){

            contato = dto.getRequerimentoInspecaoSanitariaPessoaContato();
            if (dto.getRequerimentoInspecaoSanitariaPessoaContato() == null){
                contato = new RequerimentoInspecaoSanitariaPessoaContato();
            }
            contato.setRequerimentoInspecaoSanitaria(requerimentoInspecaoSanitaria);
            contato.setNome(dto.getNome());
            contato.setCargo(dto.getCargo());
            contato.setTelefone(dto.getTelefone());
            contato.setEmail(dto.getEmail());
            contato.setFax(dto.getFax());
            BOFactory.save(contato);
        }
        for (PessoaContactadaEmpresaDTO dto : contatosExcluir){
            BOFactory.delete(dto.getRequerimentoInspecaoSanitariaPessoaContato());
        }
    }
}
