package br.com.ksisolucoes.bo.vigilancia.requerimentos.projetoarquitetonico;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoProjetoArquitetonicoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;

public class SalvarRequerimentoProjetoArquitetonico extends AbstractCommandTransaction<SalvarRequerimentoProjetoArquitetonico> {

    private final RequerimentoProjetoArquitetonicoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarRequerimentoProjetoArquitetonico(RequerimentoProjetoArquitetonicoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        boolean isNew = false;
        if (dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().getCodigo() == null) {
            isNew = true;
            dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.PROJETO_ARQUITETONICO_SANITARIO.value());
        }
        requerimentoVigilancia =
                BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoProjetoArquitetonicoSanitario().getRequerimentoVigilancia()));

        requerimentoVigilancia =
                VigilanciaHelper.atualizarGestaoRequerimento(
                        requerimentoVigilancia,
                        dto.getListFiscais(),
                        dto.getListSetorVigilancia(),
                        isNew
                );

        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(
                dto.getRequerimentoProjetoArquitetonicoSanitario().getCodigo() != null
                        ? Bundle.getStringApplication("msg_requerimento_editado")
                        : Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null
        );

        dto.getRequerimentoProjetoArquitetonicoSanitario().setRequerimentoVigilancia(requerimentoVigilancia);

        ajustarSetorResponsavelRequerimentoExterno();

        BOFactory.save(dto.getRequerimentoProjetoArquitetonicoSanitario());

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getListAnexos(), dto.getListAnexosExcluidos(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getListSetorVigilancia(), dto.getListSetorVigilanciaExcluidos());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getListFiscais(), dto.getListFiscaisExcluidos());
        BOFactory.getBO(VigilanciaFacade.class).salvarTipoProjetoRequerimentoVigilancia(requerimentoVigilancia, dto.getListTipoProjetos(), dto.getListTiposProjetosExcluidos());
        BOFactory.getBO(VigilanciaFacade.class).salvarAnexosPranchaRequerimentoProjetoArquitetonico(dto.getRequerimentoProjetoArquitetonicoSanitario(), dto.getListAnexosPrancha(), dto.getListAnexosPranchaExcluidos());
        BOFactory.getBO(VigilanciaFacade.class).salvarAnexosMemorialRequerimentoProjetoArquitetonico(dto.getRequerimentoProjetoArquitetonicoSanitario(), dto.getListAnexosMemorial(), dto.getListAnexosMemorialExcluidos());

        if (isNew) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }

    private void ajustarSetorResponsavelRequerimentoExterno() throws ValidacaoException {
        if (dto.getRequerimentoProjetoArquitetonicoSanitario().getCodigo() == null && RequerimentoVigilancia.Origem.EXTERNO.value().equals(requerimentoVigilancia.getOrigem())) {
            configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));

            } else if (configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos() != null) {
                EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaAnaliseProjetos());

                if (CollectionUtils.isEmpty(dto.getListSetorVigilancia())) {
                    dto.setListSetorVigilancia(new ArrayList<EloRequerimentoVigilanciaSetorVigilancia>());
                }
                dto.getListSetorVigilancia().add(elo);
            }
        }
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}