package br.com.ksisolucoes.bo.prontuario.basico.testerapidoconjunto;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoConjunto;

public class SaveTesteRapidoConjunto extends SaveVO<TesteRapidoConjunto> {

    public SaveTesteRapidoConjunto(TesteRapidoConjunto vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {

        this.vo.setUsuario(getSessao().<Usuario>getUsuario());

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }

        this.vo.setDataUsuario(DataUtil.getDataAtual());

        if (this.vo.getStatus() == null) {
            this.vo.setStatus(TesteRapidoConjunto.STATUS_ATIVO);
        }
    }

}