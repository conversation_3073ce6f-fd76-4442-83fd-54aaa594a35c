package br.com.ksisolucoes.bo.vigilancia.autointimacao.autointimacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AutoIntimacaoExigenciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.GerarChaveProcessoAdministrativoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.SalvarAutoIntimacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoEmail;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoFiscal;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import ch.lambdaj.Lambda;
import org.hibernate.Criteria;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarAutoIntimacao extends AbstractCommandTransaction {

    private AutoIntimacao autoIntimacao;
    private List<AutoIntimacaoFiscal> lstFiscal;
    private List<AutoIntimacaoExigenciaDTO> lstExigenciasDTO;
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private List<AutoIntimacaoExigencia> lstExigencias;
    private boolean salvarApenasAutoIntimacao;
    private boolean cadastro;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    private List<AutoIntimacaoEmail> listEmails;

    private List<RequerimentoVigilanciaAnexo> lstRequerimentoVigilanciaAnexo;


    public SalvarAutoIntimacao(SalvarAutoIntimacaoDTO salvarAutoIntimacaoDTO) {
        this.autoIntimacao = salvarAutoIntimacaoDTO.getAutoIntimacao();
        this.lstFiscal = salvarAutoIntimacaoDTO.getLstFiscal();
        this.lstExigenciasDTO = salvarAutoIntimacaoDTO.getLstExigenciasDTO();
        this.pnlRequerimentoVigilanciaAnexoDTO = salvarAutoIntimacaoDTO.getPnlRequerimentoVigilanciaAnexoDTO();
        this.salvarApenasAutoIntimacao = salvarAutoIntimacaoDTO.isSalvarApenasAutoIntimacao();
        this.listEmails = salvarAutoIntimacaoDTO.getListAutoIntimacaoEmail();
        this.lstRequerimentoVigilanciaAnexo = salvarAutoIntimacaoDTO.getLstRequerimentoVigilanciaAnexo();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (this.autoIntimacao.getCodigo() == null) {
            cadastro = true;
        }

        validacoesRelacoesRegistros();

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracao();

        this.autoIntimacao = BOFactory.save(this.autoIntimacao);
        if (this.autoIntimacao.getProcessoAdministrativoAutenticacao() == null) {
            gerarChaveProcesso();
        }

        if (!salvarApenasAutoIntimacao) {

            if (CollectionUtils.isNotNullEmpty(lstFiscal)) {
                Lambda.forEach(lstFiscal).setAutoIntimacao(autoIntimacao);
                VOUtils.persistirListaVosModificados(AutoIntimacaoFiscal.class, lstFiscal, new QueryCustom.QueryCustomParameter(AutoIntimacaoFiscal.PROP_AUTO_INTIMACAO, autoIntimacao));
            }
            if (CollectionUtils.isNotNullEmpty(lstExigenciasDTO)) {
                lstExigencias = new ArrayList<>();
                for (int i = 0; i < lstExigenciasDTO.size(); i++) {
                    lstExigencias.add(i, lstExigenciasDTO.get(i).getAutoIntimacaoExigencia());
                }
                Lambda.forEach(lstExigencias).setAutoIntimacao(autoIntimacao);
                VOUtils.persistirListaVosModificados(AutoIntimacaoExigencia.class, lstExigencias, new QueryCustom.QueryCustomParameter(AutoIntimacaoExigencia.PROP_AUTO_INTIMACAO, autoIntimacao));
            }

            BOFactory.getBO(VigilanciaFacade.class).salvarAutoIntimacaoExigenciaAnexo(lstExigenciasDTO);

            if (CollectionUtils.isNotNullEmpty(listEmails)) {
                Lambda.forEach(listEmails).setAutoIntimacao(autoIntimacao);
                VOUtils.persistirListaVosModificados(AutoIntimacaoEmail.class, listEmails, new QueryCustom.QueryCustomParameter(AutoIntimacaoEmail.PROP_AUTO_INTIMACAO, autoIntimacao));
            }

            if (pnlRequerimentoVigilanciaAnexoDTO != null) {
                BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(autoIntimacao, pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoDTOList(), pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoExcluidoDTOList());
            }

            if (CollectionUtils.isNotNullEmpty(lstRequerimentoVigilanciaAnexo)) {
                Lambda.forEach(lstRequerimentoVigilanciaAnexo).setAutoIntimacao(autoIntimacao);
                VOUtils.persistirListaVosModificados(RequerimentoVigilanciaAnexo.class, lstRequerimentoVigilanciaAnexo, new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_AUTO_INTIMACAO, autoIntimacao));
            }
        }

        if (cadastro) {
            if (autoIntimacao.getRequerimentoVigilancia() != null && autoIntimacao.getRequerimentoVigilancia().getCodigo() != null) {
                gerarOcorrenciaRequerimentoVigilancia(autoIntimacao, autoIntimacao.getRequerimentoVigilancia());
                enviarEmailResponsavelRequerimentoVigilancia(autoIntimacao, autoIntimacao.getRequerimentoVigilancia());
            }
            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAutoIntimacao(autoIntimacao.getDataIntimacao(), autoIntimacao);
        }
    }

    private void validacoesRelacoesRegistros() throws ValidacaoException {
        // validar unicidade das relações
        if (hasAutoInfracao(autoIntimacao.getCodigo())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_auto_infracao_ja_vinculado_outro_auto"));
        }

        if (hasAutoMulta(autoIntimacao.getCodigo())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_validacao_auto_multa_ja_vinculado_outro_auto"));
        }
    }

    private void gerarChaveProcesso() throws ValidacaoException, DAOException {
        if (this.autoIntimacao.getAutoInfracao() != null) {
            AutoInfracao autoInfracao = (AutoInfracao) this.getSession().get(AutoInfracao.class, this.autoIntimacao.getAutoInfracao().getCodigo());
            if (autoInfracao.getProcessoAdministrativoAutenticacao() != null) {
                this.autoIntimacao.setProcessoAdministrativoAutenticacao(autoInfracao.getProcessoAdministrativoAutenticacao());
            } else {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
        } else if (this.autoIntimacao.getAutoMulta() != null) {
            AutoMulta autoMulta = (AutoMulta) this.getSession().get(AutoMulta.class, this.autoIntimacao.getAutoMulta().getCodigo());
            if (autoMulta.getProcessoAdministrativoAutenticacao() != null) {
                this.autoIntimacao.setProcessoAdministrativoAutenticacao(autoMulta.getProcessoAdministrativoAutenticacao());
            } else {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
        } else {
            GerarChaveProcessoAdministrativoDTO dto = new GerarChaveProcessoAdministrativoDTO();
            dto.setCodigoAuto(autoIntimacao.getCodigo());
            dto.setCpf(AutosHelper.getCpfAuto(autoIntimacao));
            dto.setNome(AutosHelper.getNomeResponsavel(autoIntimacao));
            dto.setEmail(AutosHelper.getEmailAutuado(autoIntimacao));
            dto.setTelefone(AutosHelper.getTelefoneAutuado(autoIntimacao));
            ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao = BOFactory.getBO(VigilanciaFacade.class).gerarChaveProcessoAdministrativo(dto);
            if (processoAdministrativoAutenticacao == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
            this.autoIntimacao.setProcessoAdministrativoAutenticacao(processoAdministrativoAutenticacao);
        }
        BOFactory.save(this.autoIntimacao);
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if (autoIntimacao.getNumero() == null && autoIntimacao.getCodigo() == null) {
            if (configuracaoVigilancia.getAnoBaseGeral() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
            }
            if (configuracaoVigilancia.getNumAutoIntimacao() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numeracao_base_auto_intimacao"));
            }

            long sequencial = 0L;
            if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumAutoIntimacao()) > 0L) {
                sequencial = configuracaoVigilancia.getNumAutoIntimacao();
            }
            sequencial++;
            String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
            Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
            autoIntimacao.setNumero(nextId);

            configuracaoVigilancia.setNumAutoIntimacao(sequencial);

            BOFactory.save(configuracaoVigilancia);
        }
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    private boolean hasAutoInfracao(Long codigoIgnorar) {
        Criteria criteria = null;
        if (this.autoIntimacao.getAutoInfracao() != null) {
            criteria = this.getSession().createCriteria(AutoIntimacao.class)
                    .add(Restrictions.eq(AutoIntimacao.PROP_AUTO_INFRACAO, this.autoIntimacao.getAutoInfracao()));
            if (codigoIgnorar != null) {
                criteria.add(Restrictions.ne(VOUtils.montarPath(AutoIntimacao.PROP_CODIGO), codigoIgnorar));
            }
            return CollectionUtils.isNotNullEmpty(criteria.list());
        }
        return false;
    }

    private boolean hasAutoMulta(Long codigoIgnorar) {
        Criteria criteria = null;
        if (this.autoIntimacao.getAutoMulta() != null) {
            criteria = this.getSession().createCriteria(AutoIntimacao.class)
                    .add(Restrictions.eq(AutoIntimacao.PROP_AUTO_MULTA, this.autoIntimacao.getAutoMulta()));
            if (codigoIgnorar != null) {
                criteria.add(Restrictions.ne(VOUtils.montarPath(AutoIntimacao.PROP_CODIGO), codigoIgnorar));
            }
            return CollectionUtils.isNotNullEmpty(criteria.list());
        }
        return false;
    }

    private void gerarOcorrenciaRequerimentoVigilancia(AutoIntimacao autoIntimacao, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String mensagemOcorrencia = "Registro do Auto de Intimação Nº " + autoIntimacao.getNumeroFormatado();
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(mensagemOcorrencia, requerimentoVigilancia, null);
    }

    private void enviarEmailResponsavelRequerimentoVigilancia(AutoIntimacao autoIntimacao, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String assunto = "Registro do Auto de Intimação Nº " + autoIntimacao.getNumeroFormatado();
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        sb.append("<body>");
        sb.append("<p>Informativo do registro de Auto de Intimação na Vigilância Sanitária.</p>");
        sb.append("<br/>");
        sb.append("<strong>Protocolo: </strong>").append(requerimentoVigilancia.getProtocoloFormatado());
        sb.append("<br/>");
        sb.append("<strong>Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoTipoDocumento());
        sb.append("<br/>");
        sb.append("<strong>Situação do Requerimento: </strong>").append(requerimentoVigilancia.getDescricaoSituacao());
        sb.append("<br/>");
        sb.append("<strong>Ambiente de recursos disponível em: </strong>");
        String realContext = TenantContext.getRealContext();
        if (realContext.equals("localhost")) {
            realContext = realContext.concat(":8080");
        }
        sb.append(realContext);
        sb.append(("/vigilancia"));
        sb.append("</body>");
        sb.append("</html>");
        BOFactory.getBO(VigilanciaFacade.class).enviarEmailRequerimentoVigilancia(requerimentoVigilancia, sb.toString(), assunto);
    }

    public AutoIntimacao getAutoIntimacao() {
        return autoIntimacao;
    }
}
