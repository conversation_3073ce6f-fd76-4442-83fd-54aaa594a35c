package br.com.ksisolucoes.bo.consorcio.pedidotransferencialicitacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.ValorReservadoDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.Conta;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem;
import br.com.ksisolucoes.vo.consorcio.SubConta;
import br.com.ksisolucoes.vo.controle.Usuario;
import ch.lambdaj.Lambda;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FecharPedidoTransferenciaLicitacao extends AbstractCommandTransaction{

    private PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;

    public FecharPedidoTransferenciaLicitacao(PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
        this.pedidoTransferenciaLicitacao = pedidoTransferenciaLicitacao;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        pedidoTransferenciaLicitacao.setStatus(PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.FECHADO.value());
        pedidoTransferenciaLicitacao.setUsuarioFechamento(getSessao().<Usuario>getUsuario());
        pedidoTransferenciaLicitacao.setDataFechamento(DataUtil.getDataAtual());

        pedidoTransferenciaLicitacao = BOFactory.save(pedidoTransferenciaLicitacao);

        if(PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.APROVADO.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())
                && pedidoTransferenciaLicitacao.getSubContaMedicamento() != null && pedidoTransferenciaLicitacao.getSubContaMedicamento().getCodigo() != null){
            SubConta subConta = HibernateUtil.lockTable(SubConta.class, pedidoTransferenciaLicitacao.getSubContaMedicamento().getCodigo());
            Conta conta = HibernateUtil.lockTable(Conta.class, subConta.getConta().getCodigo());

            List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE_ENVIADA)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
                    .start().getList();

            if(CollectionUtils.isNotNullEmpty(itemList)) {
                BOFactory.getBO(ConsorcioFacade.class).removerValorReservado(new ValorReservadoDTO(subConta, pedidoTransferenciaLicitacao.getAnoCadastro(), Coalesce.asDouble(Lambda.sum(itemList, Lambda.on(PedidoTransferenciaLicitacaoItem.class).getTotalItem()))));
            }
        }
        
        List<PedidoTransferenciaLicitacaoItem> itensExistentes = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                    .addProperties(new HQLProperties(PedidoTransferenciaLicitacaoItem.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO), pedidoTransferenciaLicitacao))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE,PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.CANCELADO.value()))
                    .start().getList();
        
        for (PedidoTransferenciaLicitacaoItem item : itensExistentes) {
            item.setStatus(PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.FECHADO.value());
            BOFactory.save(item);
        }
    }

}
