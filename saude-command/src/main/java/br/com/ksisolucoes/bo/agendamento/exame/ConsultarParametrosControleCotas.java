package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.Fabricante;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.hibernate.Session;

import java.util.List;

public class ConsultarParametrosControleCotas {

    public static TipoExame getTipoExame(List<ExameProcedimento> procedimentosSolicitacao, TipoProcedimento tipoProcedimento) {
        boolean possuiExamesSolicitacao = CollectionUtils.isNotNullEmpty(procedimentosSolicitacao);
        ExameProcedimento exameProcedimento = possuiExamesSolicitacao ? procedimentosSolicitacao.get(0) : tipoProcedimento.getExameProcedimento();

        if (exameProcedimento == null) {
            return null;
        }

        TipoExame tipoExame = exameProcedimento.getTipoExame();

        if (tipoExame == null) {
            ExameProcedimento exameProcedimentoAux = LoadManager.getInstance(ExameProcedimento.class)
                .addProperties(new HQLProperties(TipoExame.class, VOUtils.montarPath(ExameProcedimento.PROP_TIPO_EXAME)).getProperties())
                .setId(exameProcedimento.getCodigo())
                .start().getVO();

            tipoExame = exameProcedimentoAux.getTipoExame();
        }

        return exameProcedimento != null ? tipoExame : null;
    }

    public static Long buscarTipoTetoCotaPpi(TipoExame tipoExame, Session session) {
        ExameCotaPpi exameCotaPpi = consultaExamePPI(tipoExame, session);
        return exameCotaPpi != null ? exameCotaPpi.getTipoTeto() : null;
    }

    private static ExameCotaPpi consultaExamePPI(TipoExame tipoExame, Session session) {
        if (tipoExame != null) {
            return (ExameCotaPpi) session.createCriteria(ExameCotaPpi.class)
                                         .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(ExameCotaPpi.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), tipoExame.getCodigo()))
                                         .setMaxResults(1)
                                         .uniqueResult();
        }

        return null;
    }
}
