package br.com.ksisolucoes.bo.vigilancia.registroinspecao;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaRegistroInspecaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaRegistroInspecao extends CommandQueryPager<QueryConsultaRegistroInspecao> {

    private QueryConsultaRegistroInspecaoDTOParam param;

    public QueryConsultaRegistroInspecao(QueryConsultaRegistroInspecaoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("ri.codigo", true);

        hql.addToSelect("e.razaoSocial", "estabelecimento.razaoSocial");
        hql.addToSelect("vp.nome", "vigilanciaPessoa.nome");

        hql.setTypeSelect(RegistroInspecao.class.getName());
        hql.addToFrom("RegistroInspecao ri" +
                " left join ri.estabelecimento e" +
                " left join ri.vigilanciaPessoa vp");

        hql.addToWhereWhithAnd("ri.codigo = ", param.getCodigo());

        if(param.getPropSort() != null){
            hql.addToOrder("ri."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("ri.codigo");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
