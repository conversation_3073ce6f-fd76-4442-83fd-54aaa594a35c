package br.com.ksisolucoes.bo.prontuario.avaliacao.bolsafamilia;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.DatePeriod;
import br.com.celk.util.IdadeUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.BolsaFamiliaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.avaliacao.BolsaFamilia;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.text.ParseException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarBolsaFamilia extends AbstractCommandTransaction<SalvarBolsaFamilia> {

    private final Atendimento atendimento;
    private final BolsaFamiliaDTO dto;

    public SalvarBolsaFamilia(Atendimento atendimento, BolsaFamiliaDTO dto) {
        this.atendimento = atendimento;
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (dto.getAtendimentoPrimario() != null) {
            BolsaFamilia bolsaFamilia = new BolsaFamilia();

            DatePeriod datePeriod = DataUtil.getPeriodicidade(DataUtil.PERIODICIDADE_SEMESTRAL);

            Criteria criteria = getSession().createCriteria(BolsaFamilia.class);
            criteria.add(Restrictions.eq(BolsaFamilia.PROP_USUARIO_CADSUS, atendimento.getUsuarioCadsus()))
                    .add(Restrictions.between(BolsaFamilia.PROP_DATA_ATENDIMENTO, datePeriod.getDataInicial(), datePeriod.getDataFinal()));

            List<BolsaFamilia> list = criteria.list();
            if (CollectionUtils.isNotNullEmpty(list)) {
                if (list.size() > 1) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_mais_um_registro_bolsa_familia"));
                }
                return;
            }

            if (validarBolsaFamilia()) {
                bolsaFamilia.setUsuarioCadsus(atendimento.getUsuarioCadsus());
                bolsaFamilia.setPeso(dto.getAtendimentoPrimario().getPeso());
                bolsaFamilia.setAltura(dto.getAtendimentoPrimario().getAltura());
                bolsaFamilia.setSisvanAlimentacao(dto.getAtendimentoPrimario().getSisvanAlimentacao());
                bolsaFamilia.setDataAtendimento(atendimento.getDataAtendimento());
                bolsaFamilia.setGestante(dto.getAtendimentoPrimario().getGestante());

                if (RepositoryComponentDefault.SIM_LONG.equals(atendimento.getVacinaEmDia())) {
                    bolsaFamilia.setVacinaEmDia(RepositoryComponentDefault.SIM_LONG);
                } else{
                    bolsaFamilia.setVacinaEmDia(RepositoryComponentDefault.NAO_LONG);
                }

                if (RepositoryComponentDefault.SIM_LONG.equals(dto.getAtendimentoPrimario().getGestante())) {
                    PreNatal preNatal = (PreNatal) getSession().createCriteria(PreNatal.class)
                            .add(Restrictions.eq(PreNatal.PROP_USUARIO_CADSUS, atendimento.getUsuarioCadsus()))
                            .add(Restrictions.eq(PreNatal.PROP_STATUS, PreNatal.Status.ABERTO.value()))
                            .uniqueResult();

                    if (preNatal != null) {
                        bolsaFamilia.setDum(preNatal.getDataUltimaMenstruacao());
                        bolsaFamilia.setDataPreNatal(preNatal.getDataUltimaConsulta());
                    }
                }
                bolsaFamilia.setSituacao(getSituacao(bolsaFamilia));

                BOFactory.save(bolsaFamilia);
            }
        }
    }

    private boolean validarBolsaFamilia() throws DAOException, ValidacaoException {
        Long idadeUsuarioCadsus = IdadeUtil.getIdade(atendimento.getUsuarioCadsus().getDataNascimento());

        Integer PBFIdadeCrianca = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("PBFIdadeCrianca");
        Integer PBFIdadeInicialMulheres = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("PBFIdadeInicialMulheres");
        Integer PBFIdadeFinalMulheres = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("PBFIdadeFinalMulheres");
        Integer idadeParaValidarAlimentacao = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("idadeParaValidarAlimentacao");
        Long idadeEmMeses;
        try {
            idadeEmMeses = Data.getIdadeEmMeses(atendimento.getUsuarioCadsus().getDataNascimento());
        } catch (ParseException ex) {
            throw new ValidacaoException(ex.getMessage(), ex);
        }

        String origem = Bundle.getStringApplication("rotulo_bolsa_familia") + ": ";
        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getAtendimentoPrimario().getGestante())
                || idadeUsuarioCadsus < PBFIdadeCrianca
                || (RepositoryComponentDefault.SEXO_FEMININO.equals(atendimento.getUsuarioCadsus().getSexo())
                && idadeUsuarioCadsus >= PBFIdadeInicialMulheres && idadeUsuarioCadsus < PBFIdadeFinalMulheres)) {

            ValidacaoProcesso validacaoProcesso = new ValidacaoProcesso();

            if (dto.getAtendimentoPrimario().getPeso() == null || dto.getAtendimentoPrimario().getPeso() <= 0D) {
                validacaoProcesso.add(origem + Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_peso")));
            }
            if (dto.getAtendimentoPrimario().getAltura() == null || dto.getAtendimentoPrimario().getAltura() <= 0D) {
                validacaoProcesso.add(origem + Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_altura")));
            }
            if (idadeEmMeses < idadeParaValidarAlimentacao
                    && dto.getAtendimentoPrimario().getSisvanAlimentacao() == null) {
                validacaoProcesso.add(Bundle.getStringApplication("campoXObrigatorio", Bundle.getStringApplication("rotulo_tipo_alimentacao")));
            }

            if (!validacaoProcesso.getMensagemList().isEmpty()) {
                throw new ValidacaoException(validacaoProcesso);
            }

            return true;
        }

        return false;
    }

    private Long getSituacao(BolsaFamilia bolsaFamilia) throws DAOException, ValidacaoException {
        Long idadeUsuarioCadsus = IdadeUtil.getIdade(atendimento.getUsuarioCadsus().getDataNascimento());

        Integer PBFIdadeCrianca = getParametroContainer(Modulos.UNIDADE_SAUDE).getParametro("PBFIdadeCrianca");
        if (idadeUsuarioCadsus < PBFIdadeCrianca) {
            if (bolsaFamilia.getVacinaEmDia() == null) {
                return BolsaFamilia.STATUS_PENDENTE;
            }
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getAtendimentoPrimario().getGestante())) {
            if (bolsaFamilia.getDum() == null) {
                return BolsaFamilia.STATUS_PENDENTE;
            }
        }

        return BolsaFamilia.STATUS_ATUALIZADO;
    }
}
