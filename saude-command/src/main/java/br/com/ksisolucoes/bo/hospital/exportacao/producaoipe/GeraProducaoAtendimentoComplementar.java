package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe;

import br.com.celk.services.mobile.integracao.ListAggregationStrategy;
import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoaih.GeraProducaoAih;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.atendimentocomplementar.bind.CabecalhoAtendimentoComplementarBind;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.atendimentocomplementar.bind.DesignativoAtendimentoComplementarBind;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.atendimentocomplementar.bind.LancamentosAtendimentoComplementarBind;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.DesignativoAtendimentoComplementarDTO;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.LancamentosAtendimentoComplementarDTO;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.ProducaoIpeDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ProducaoIpeProcesso;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.select;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.dataformat.bindy.fixed.BindyFixedLengthDataFormat;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.spi.DataFormat;
import org.apache.camel.spi.PackageScanClassResolver;
import org.apacheextras.camel.jboss.JBossPackageScanClassResolver;
import static org.hamcrest.Matchers.equalTo;

/**
 *
 * <AUTHOR>
 */
public class GeraProducaoAtendimentoComplementar extends AbstractCommandTransaction {

    CamelContext context;
    private ProducerTemplate template;
    private Long idIpeProcesso;
    private IBindVoExport cabecalhoBind;
    private IBindVoExport designativoBind;
    private IBindVoExport lancamentosBind;
    private String texto;
    private String textoFinal;
    private Long referencia;
    private String erro;
    private List<Long> listaNotas;
    private static final Long QUANT_LANCAMENTOS_POR_NOTA = 20L;
    private Long numeroNota;

    public GeraProducaoAtendimentoComplementar(Long idIpeProcesso, List<Long> listaNotas) {
        this.idIpeProcesso = idIpeProcesso;
        this.listaNotas = listaNotas;
        this.referencia = 1L;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        try {
            List producaoIpeCabecalhoList = BOFactory.getBO(HospitalFacade.class).consultaProducaoIpeCabecalhoAtendimentoComplementar(listaNotas);
            validaCabecalhoExportacao(producaoIpeCabecalhoList);

            final List<DesignativoAtendimentoComplementarDTO> listaDesignativoAc = getDesignativoAtendimentoComplementar();
            final List<LancamentosAtendimentoComplementarDTO> listaLancamentosAc = getLancamentosAtendimentoComplementar();

            PackageScanClassResolver jbossResolver = new JBossPackageScanClassResolver();
            context = new DefaultCamelContext();
            context.setPackageScanClassResolver(jbossResolver);

            template = context.createProducerTemplate();
            context.addRoutes(new RouteBuilder() {
                @Override
                public void configure() throws Exception {

                    DataFormat dfCabecalho = new BindyFixedLengthDataFormat(CabecalhoAtendimentoComplementarBind.class);
                    DataFormat dfDesignativo = new BindyFixedLengthDataFormat(DesignativoAtendimentoComplementarBind.class);
                    DataFormat dfLancamentos = new BindyFixedLengthDataFormat(LancamentosAtendimentoComplementarBind.class);

                    from("direct:producaoIpeCabecalho")
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {

                            List<ProducaoIpeDTO> list = (List<ProducaoIpeDTO>) exchange.getIn().getBody();

                            cabecalhoBind = new CabecalhoAtendimentoComplementarBind();
                            cabecalhoBind.buildProperties(list.get(0));
                            exchange.getOut().setBody(cabecalhoBind);
                        }
                    })
                            .marshal(dfCabecalho)
                            .convertBodyTo(String.class)
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {
                            Object obj = exchange.getIn().getBody();
                            texto = obj.toString();

                            exchange.getOut().setBody(listaDesignativoAc);
                        }
                    })
                            .split(body(), new ListAggregationStrategy())
                            .process(new Processor() {

                        @Override
                        public void process(Exchange exchange) throws Exception {
                            DesignativoAtendimentoComplementarDTO designativo = (DesignativoAtendimentoComplementarDTO) exchange.getIn().getBody();
                            numeroNota = designativo.getItemContaPaciente().getNumeroNota();
                            
                            designativoBind = new DesignativoAtendimentoComplementarBind();
                            designativoBind.buildProperties(designativo);
                            exchange.getOut().setBody(designativoBind);
                        }
                    })
                            
                            .marshal(dfDesignativo)
                            .convertBodyTo(String.class)
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {
                            String textoDesig = (String) exchange.getIn().getBody();
                            texto += textoDesig;
                            
                            List<LancamentosAtendimentoComplementarDTO> lancamentos = select(listaLancamentosAc, having(on(LancamentosAtendimentoComplementarDTO.class).getItemContaPaciente().getNumeroNota(), equalTo(numeroNota)));
                            
                            exchange.getOut().setBody(lancamentos);
                        }
                    })
                            .split(body(), new ListAggregationStrategy())
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {

                            LancamentosAtendimentoComplementarDTO lancamento = (LancamentosAtendimentoComplementarDTO) exchange.getIn().getBody();
                            lancamentosBind = new LancamentosAtendimentoComplementarBind();

                            lancamento.setReferencial(referencia);
                            referencia++;
                            if (referencia > QUANT_LANCAMENTOS_POR_NOTA) {
                                referencia = 1L;
                            }

                            lancamentosBind.buildProperties(lancamento);
                            exchange.getOut().setBody(lancamentosBind);
                        }
                    })
                            .marshal(dfLancamentos)
                            .convertBodyTo(String.class)
                            .end()
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {
                            ArrayList<String> list = (ArrayList<String>) exchange.getIn().getBody();
                            for (String string : list) {
                                texto += string;
                            }
                            referencia = 1L;
                        }
                    })
                            .end()
                            .process(new Processor() {
                        @Override
                        public void process(Exchange exchange) throws Exception {
                            exchange.getOut().setBody(texto);
                        }
                    });
                }
            });

            context.start();
            textoFinal = template.requestBody("direct:producaoIpeCabecalho", producaoIpeCabecalhoList, String.class);

            atualizaAihProcesso();

        } catch (Exception ex) {
            throw new ValidacaoException(ex);
        } finally {
            try {
                if (context != null) {
                    context.stop();
                }
            } catch (Exception ex) {
                Logger.getLogger(GeraProducaoAih.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private List<DesignativoAtendimentoComplementarDTO> getDesignativoAtendimentoComplementar() throws DAOException, ValidacaoException {
        List<DesignativoAtendimentoComplementarDTO> lista = BOFactory.getBO(HospitalFacade.class).consultaDesignativoAtendimentoComplementar(listaNotas);
        validaDesignativo(lista);
        return lista;
    }

    private List<LancamentosAtendimentoComplementarDTO> getLancamentosAtendimentoComplementar() throws DAOException, ValidacaoException {
        List lista = BOFactory.getBO(HospitalFacade.class).consultaLancamentosAtendimentoComplementar(listaNotas);
        return lista;
    }

    private void atualizaAihProcesso() throws DAOException, ValidacaoException {
        ProducaoIpeProcesso pip = HibernateUtil.lockTable(ProducaoIpeProcesso.class, idIpeProcesso);
        pip.setTexto(textoFinal);
        pip.setStatus(ProducaoIpeProcesso.STATUS_GERADO);
        BOFactory.save(pip);
    }
    
    private void validaCabecalhoExportacao(List<ProducaoIpeDTO> lista) throws ValidacaoException {

        if (!CollectionUtils.isNotNullEmpty(lista)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existem_itens_para_exportar"));
        }
        if (lista.get(0).getQuantLancamentosReferencias() < 1) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_sem_lancamentos_exportacao_ipe"));
        }

        if (lista.get(0).getEmpresaPrincipal() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_empresa_principal_nao_configurada"));
        } else if (lista.get(0).getEmpresaPrincipal().getNumeroPrestadorIpe() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_numero_prestador_nao_configurado_empresa_principal_X", lista.get(0).getEmpresaPrincipal().getDescricao()));
        }

    }

    private void validaDesignativo(List<DesignativoAtendimentoComplementarDTO> lista) throws ValidacaoException {

        for (DesignativoAtendimentoComplementarDTO designativo : lista) {

            if (designativo.getEmpresaPrincipal().getNumeroPrestadorIpe() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_numero_prestador_nao_configurado_empresa_principal_X", designativo.getEmpresaPrincipal().getDescricao()));
            }

            if (designativo.getTipoPrestadorIpe() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_tipo_prestador_nao_configurato"));
            }
        }
    }
}
