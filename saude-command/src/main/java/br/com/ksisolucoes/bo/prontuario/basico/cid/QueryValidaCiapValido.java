package br.com.ksisolucoes.bo.prontuario.basico.cid;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Ciap;
import org.hibernate.SQLQuery;
import org.hibernate.type.BooleanType;


public class QueryValidaCiapValido extends AbstractCommandTransaction {

    private Ciap ciap;
    private Boolean isCiapValido;

    public QueryValidaCiapValido(Ciap ciap) {
        this.ciap = ciap;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        isCiapValido = ciap == null || verificaCiapValido();
    }

    private boolean verificaCiapValido() {
        HQLHelper hqlCiapValido = this.getHQLCiapValido();
        SQLQuery query = getSession().createSQLQuery(hqlCiapValido.getQuery());
        query.setString("ciapReferencia", ciap.getReferencia());
        this.addScalar(query);
        return (Boolean) query.uniqueResult();
    }


    private HQLHelper getHQLCiapValido() {
        HQLHelper hql = new HQLHelper();
        hql.setUseSQL(true);

        hql.addToSelect("case when count(*) > 0 then true else false end", "isCiapValido");
        hql.addToFrom(" ciap ");
        hql.addToWhereWhithAnd("cast(nullif(regexp_replace(referencia, '\\D', '', 'g'), '') as numeric) not between 30 and 69 and referencia = :ciapReferencia");

        return hql;
    }

    private void addScalar(SQLQuery sql) {
        sql.addScalar("isCiapValido", BooleanType.INSTANCE);
    }

    public boolean getCiapValido() {
        return isCiapValido;
    }
}
