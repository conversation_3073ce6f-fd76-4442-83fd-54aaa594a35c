package br.com.ksisolucoes.bo.portal.servicos.tfd;

import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaHistoricoTFDPortal extends CommandQuery {

    private HistoricoPacienteDTOParam param;
    private List<HistoricoPacienteDTO> resultado;

    public QueryConsultaHistoricoTFDPortal(HistoricoPacienteDTOParam param) {
        this.param = param;
    }

    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("pedidoTfd.numeroPedido", "laudoTfd.pedidoTfd.numeroPedido");
        hql.addToSelect("laudoTfd.dataLaudo", "laudoTfd.dataLaudo");
        hql.addToSelect("laudoTfd.codigo", "laudoTfd.codigo");
        hql.addToSelect("profissional.nome", "laudoTfd.profissional.nome");
        hql.addToSelect("tipoProcedimento.descricao", "laudoTfd.tipoProcedimento.descricao");
        hql.addToSelect("pedidoTfd.parecer", "laudoTfd.pedidoTfd.parecer");
        hql.addToSelect("laudoTfd.status", "laudoTfd.status");

        hql.setTypeSelect(HistoricoPacienteDTO.class.getName());
        hql.addToFrom("LaudoTfd laudoTfd"
                + " left join laudoTfd.pedidoTfd pedidoTfd"
                + " left join laudoTfd.tipoProcedimento tipoProcedimento"
                + " left join laudoTfd.profissional profissional"
                + " left join laudoTfd.pedidoTfd pedidoTfd");

        hql.addToWhereWhithAnd("laudoTfd.usuarioCadsus.codigo = ", this.param.getUsuarioCadsus().getCodigo());
        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", "S");

        if (!param.getPropSort().equals("laudoTfd.status")) {
            hql.addToOrder(param.getPropSort() + (param.isAscending() ? " asc" : " desc"));
        } else {

        }

        if (HistoricoPacienteDTO.TipoTFD.PENDENTE.value().equals(param.getTfdOP())) {
            hql.addToWhereWhithAnd("laudoTfd.status in ", Arrays.asList(
                    LaudoTfd.StatusLaudoTfd.ENCAMINHADO_MEDICO.value(),
                    LaudoTfd.StatusLaudoTfd.RECEBIDO.value(),
                    LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value(),
                    LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value()));
        } else if (HistoricoPacienteDTO.TipoTFD.AGENDADO.value().equals(param.getTfdOP())) {
            hql.addToWhereWhithAnd("laudoTfd.status in ", Arrays.asList(
                    LaudoTfd.StatusLaudoTfd.AUTORIZADO.value()));
        } else if (HistoricoPacienteDTO.TipoTFD.NEGADO.value().equals(param.getTfdOP())) {
            hql.addToWhereWhithAnd("laudoTfd.status in ", Arrays.asList(
                    LaudoTfd.StatusLaudoTfd.NEGADO.value(),
                    LaudoTfd.StatusLaudoTfd.CANCELADO.value()));
        } else if (HistoricoPacienteDTO.TipoTFD.AMBOS.value().equals(param.getTfdOP())) {
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result
    ) {
        resultado = hql.getBeanList((List) result);
    }

    public List<HistoricoPacienteDTO> getResultado() {
        if (param.getPropSort().equals("laudoTfd.status")) {
            Collections.sort(resultado, new Comparator<HistoricoPacienteDTO>() {
                @Override
                public int compare(HistoricoPacienteDTO o1, HistoricoPacienteDTO o2) {
                    Integer aux = o1.getLaudoTfd().getDescricaoStatus().compareTo(o2.getLaudoTfd().getDescricaoStatus());
                    if (param.isAscending()) {
                        aux = aux * -1;
                    }
                    if (aux.equals(0)) {
                        return o1.getLaudoTfd().getDataLaudo().compareTo(o2.getLaudoTfd().getDataLaudo());
                    } else {
                        return aux;
                    }
                }
            });
        }
        return resultado;
    }

}
