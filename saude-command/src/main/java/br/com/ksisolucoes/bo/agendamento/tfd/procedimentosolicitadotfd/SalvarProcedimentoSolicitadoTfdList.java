/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.tfd.procedimentosolicitadotfd;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.ProcedimentoSolicitadoTfd;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarProcedimentoSolicitadoTfdList extends AbstractCommandTransaction{

    private List<ProcedimentoSolicitadoTfd> procedimentoSolicitadoList;
    private LaudoTfd laudoTfd;

    public SalvarProcedimentoSolicitadoTfdList(List<ProcedimentoSolicitadoTfd> procedimentoSolicitadoList, LaudoTfd laudoTfd) {
        this.procedimentoSolicitadoList = procedimentoSolicitadoList;
        this.laudoTfd = laudoTfd;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
            List<ProcedimentoSolicitadoTfd> salvos = LoadManager.getInstance(ProcedimentoSolicitadoTfd.class)
                .addParameter(new QueryCustomParameter(ProcedimentoSolicitadoTfd.PROP_LAUDO_TFD, laudoTfd))
                .start().getList();
        for (ProcedimentoSolicitadoTfd pst : procedimentoSolicitadoList) {
            pst.setLaudoTfd(laudoTfd);
            BOFactory.save(pst);

            salvos.remove(pst);
        }

        for (ProcedimentoSolicitadoTfd procedimentoSolicitadoTfd : salvos) {
            BOFactory.delete(procedimentoSolicitadoTfd);
        }
    }

    public List<ProcedimentoSolicitadoTfd> getProcedimentoSolicitadoList() {
        return procedimentoSolicitadoList;
    }

}
