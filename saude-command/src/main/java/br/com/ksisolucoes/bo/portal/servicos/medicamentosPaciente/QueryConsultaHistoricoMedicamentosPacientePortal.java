package br.com.ksisolucoes.bo.portal.servicos.medicamentosPaciente;

import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.celk.view.unidadesaude.atendimento.consulta.consutaHistoricoPaciente.dto.HistoricoPacienteDTOParam;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.MedicamentoPaciente;
import java.util.List;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaHistoricoMedicamentosPacientePortal extends CommandQuery<QueryConsultaHistoricoMedicamentosPacientePortal> {

    private HistoricoPacienteDTOParam param;
    private List<HistoricoPacienteDTO> resultado;

    public QueryConsultaHistoricoMedicamentosPacientePortal(HistoricoPacienteDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("produto.codigo", "medicamentoPaciente.produto.codigo");
        hql.addToSelect("medicamentoPaciente.codigo", "medicamentoPaciente.codigo");
        hql.addToSelect("medicamentoPaciente.nomeProduto", "medicamentoPaciente.nomeProduto");
        hql.addToSelect("medicamentoPaciente.posologia", "medicamentoPaciente.posologia");
        hql.addToSelect("usuarioCadsus.codigo", "medicamentoPaciente.usuarioCadsus.codigo");
        hql.addToSelect("tipoReceita.descricao", "medicamentoPaciente.tipoReceita.descricao");
        hql.addToSelect("(select max(rec.dataCadastro) from ReceituarioItem ri"
                + " left join ri.receituario rec"
                + " where ri.medicamentoPaciente = medicamentoPaciente)", "ultimaReceita");

        hql.setTypeSelect(HistoricoPacienteDTO.class.getName());

        hql.addToFrom("MedicamentoPaciente medicamentoPaciente"
                + " left join medicamentoPaciente.usuarioCadsus usuarioCadsus"
                + " left join medicamentoPaciente.tipoReceita tipoReceita"
                + " left join medicamentoPaciente.produto produto");

        hql.addToWhereWhithAnd("medicamentoPaciente.status != ", MedicamentoPaciente.Status.CANCELADO.value());
        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", this.param.getUsuarioCadsus().getCodigo());
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (HistoricoPacienteDTO resultado1 : resultado) {
            if (resultado1.getMedicamentoPaciente().getProduto() != null && resultado1.getMedicamentoPaciente().getProduto().getCodigo() != null) {
                DispensacaoMedicamentoItem dmi = LoadManager.getInstance(DispensacaoMedicamentoItem.class)
                        .addProperties(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_DATA_DISPENSACAO))
                        .addProperties(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_DATA_ULTIMA_DISPENSACAO))
                        .addProperties(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DATA_PROXIMA_DISPENSACAO))
                        .addProperties(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_CODIGO))
                        .addProperties(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_PRODUTO, Produto.PROP_CODIGO), resultado1.getMedicamentoPaciente().getProduto().getCodigo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_USUARIO_CADSUS_DESTINO, UsuarioCadsus.PROP_CODIGO), resultado1.getMedicamentoPaciente().getUsuarioCadsus().getCodigo()))
                        .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(DispensacaoMedicamentoItem.PROP_DISPENSACAO_MEDICAMENTO, DispensacaoMedicamento.PROP_CODIGO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .setMaxResults(1)
                        .start().getVO();
                if (dmi != null) {
                    resultado1.setDispensacaoMedicamentoItem(dmi);
                    resultado1.setUltimaDispencacao(dmi.getDispensacaoMedicamento().getDataUltimaDispensacao());
                    resultado1.setLocalDispencacao(dmi.getDispensacaoMedicamento().getEmpresa().getDescricao());
                }
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result, false);
    }

    public List<HistoricoPacienteDTO> getResultado() {
        return resultado;
    }

}
