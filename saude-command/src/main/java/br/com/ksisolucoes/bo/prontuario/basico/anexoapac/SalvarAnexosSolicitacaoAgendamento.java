package br.com.ksisolucoes.bo.prontuario.basico.anexoapac;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.dto.SolicitacaoAgendamentoAnexosDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.hospital.SolicitacaoAgendamentoAnexo;

public class SalvarAnexosSolicitacaoAgendamento extends AbstractCommandTransaction {

    private SolicitacaoAgendamentoAnexosDTO dto;

    public SalvarAnexosSolicitacaoAgendamento(SolicitacaoAgendamentoAnexosDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        for (SolicitacaoAgendamentoAnexo solicitacaoAgendamentoAnexo : dto.getAnexosSolicitacaoAgendamento()) {
            BOFactory.save(solicitacaoAgendamentoAnexo);
        }

        VOUtils.persistirListaVosModificados(SolicitacaoAgendamentoAnexo.class, dto.getAnexosSolicitacaoAgendamento(),
                new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoAnexo.PROP_SOLICITACAO_AGENDAMENTO, dto.getSolicAgendamento()));

        for (MensagemAnexoDTO anexo : dto.getAnexos()) {
            anexo.setOrigem(GerenciadorArquivo.OrigemArquivo.SOLICITACAO_AGENDAMENTO_ANEXO.value());
            GerenciadorArquivo gerenciadorArquivo = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(anexo);

            SolicitacaoAgendamentoAnexo solicitacaoAgendamentoAnexo = new SolicitacaoAgendamentoAnexo();
            solicitacaoAgendamentoAnexo.setSolicitacaoAgendamento(dto.getSolicAgendamento());
            solicitacaoAgendamentoAnexo.setGerenciadorArquivo(gerenciadorArquivo);

            BOFactory.save(solicitacaoAgendamentoAnexo);
        }
    }
}
