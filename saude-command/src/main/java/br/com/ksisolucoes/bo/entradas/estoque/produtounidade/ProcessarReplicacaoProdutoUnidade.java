/*
 * ProcessarReplicacaoProdutoUnidade.java
 *
 * Created on 30 de Agosto de 2006, 14:17
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.produtounidade;

import br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa.SaveEstoqueEmpresa;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class ProcessarReplicacaoProdutoUnidade extends AbstractCommandTransaction {
    
    private Empresa empresaOrigem;
    
    private List< Empresa > empresasDestino;
    
    public ProcessarReplicacaoProdutoUnidade(Empresa empresaOrigem, List< Empresa > empresasDestino) {
        this.empresaOrigem = empresaOrigem;
        this.empresasDestino = empresasDestino;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        Criteria criteriaProduto = this.getSession().createCriteria( EstoqueEmpresa.class );
        
        criteriaProduto.createAlias( EstoqueEmpresa.PROP_RO_EMPRESA, EstoqueEmpresa.PROP_RO_EMPRESA );
        criteriaProduto.createAlias( EstoqueEmpresa.PROP_RO_PRODUTO, EstoqueEmpresa.PROP_RO_PRODUTO );
        
        criteriaProduto.add( Restrictions.eq( EstoqueEmpresa.PROP_RO_EMPRESA, this.empresaOrigem ) );
        criteriaProduto.add( Restrictions.eq( EstoqueEmpresa.PROP_FLAG_ATIVO, RepositoryComponentDefault.SIM ) );
        criteriaProduto.setProjection( Projections.property( VOUtils.montarPath(EstoqueEmpresa.PROP_RO_PRODUTO, Produto.PROP_CODIGO) ) );
        
        List< String > codigosProduto = criteriaProduto.list();
        
        for ( Empresa empresa : this.empresasDestino ){
            for ( String codigoProduto : codigosProduto ){
                EstoqueEmpresaPK estoqueEmpresaPK = new EstoqueEmpresaPK(new Produto( codigoProduto ), empresa);
                EstoqueEmpresa estoqueEmpresa;

                estoqueEmpresa = (EstoqueEmpresa) getSession().get(EstoqueEmpresa.class, estoqueEmpresaPK);

                if (estoqueEmpresa==null) {
                    estoqueEmpresa = new EstoqueEmpresa(estoqueEmpresaPK);
                    estoqueEmpresa.setEstoqueFisico( new Double(0) );
                    estoqueEmpresa.setEstoqueReservado( new Double(0) );
                    estoqueEmpresa.setEstoqueEncomendado( new Double(0) );
                }

                estoqueEmpresa.setFlagAtivo(RepositoryComponentDefault.SIM);

                new SaveEstoqueEmpresa( estoqueEmpresa ).start();
            }
        }
    }
    
}
