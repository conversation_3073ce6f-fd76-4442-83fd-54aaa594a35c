package br.com.ksisolucoes.bo.controle.horario;

import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.HorarioDiaSemana;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SalvarHorariosMap extends AbstractCommandTransaction {

    private Map<UsuarioEmpresa, List<HorarioDiaSemana>> map;
    
    public SalvarHorariosMap(Map<UsuarioEmpresa, List<HorarioDiaSemana>> map) {
        this.map = map;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        for (Map.Entry<UsuarioEmpresa, List<HorarioDiaSemana>> entry : map.entrySet()) {
            BOFactory.getBO(UsuarioFacade.class).salvarHorarios(entry.getKey(), entry.getValue());
        }
    }
}
