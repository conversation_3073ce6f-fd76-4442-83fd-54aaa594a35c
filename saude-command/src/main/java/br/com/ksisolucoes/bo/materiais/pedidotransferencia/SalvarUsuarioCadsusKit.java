package br.com.ksisolucoes.bo.materiais.pedidotransferencia;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit;
import br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKitItem;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import br.com.ksisolucoes.vo.materiais.KitPedidoPacienteItem;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarUsuarioCadsusKit extends AbstractCommandTransaction {

    private List<KitPedidoPaciente> kitPedidoPacienteList;
    private List<UsuarioCadsusKit> usuarioCadsusKitList = new ArrayList<UsuarioCadsusKit>();
    private UsuarioCadsus usuarioCadsus;

    public SalvarUsuarioCadsusKit(List<KitPedidoPaciente> kitPedidoPacienteList, UsuarioCadsus usuarioCadsus) {
        this.kitPedidoPacienteList = kitPedidoPacienteList;
        this.usuarioCadsus = usuarioCadsus;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        for(KitPedidoPaciente kitPedidoPaciente : kitPedidoPacienteList) {
            UsuarioCadsusKit usuarioCadsusKit = new UsuarioCadsusKit();
            usuarioCadsusKit.setDescricao(kitPedidoPaciente.getDescricao());
            usuarioCadsusKit.setUsuarioCadsus(usuarioCadsus);

            boolean exists = LoadManager.getInstance(UsuarioCadsusKit.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKit.PROP_DESCRICAO), usuarioCadsusKit.getDescricao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS), usuarioCadsusKit.getUsuarioCadsus()))
                    .exists();
            if(exists) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_kit_ja_adicionado_X", usuarioCadsusKit.getDescricao()));
            }

            usuarioCadsusKitList.add(BOFactory.save(usuarioCadsusKit));

            List<KitPedidoPacienteItem> itensKit = LoadManager.getInstance(KitPedidoPacienteItem.class)
                    .addProperties(new HQLProperties(KitPedidoPacienteItem.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(KitPedidoPacienteItem.PROP_KIT_PEDIDO_PACIENTE), kitPedidoPaciente))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(KitPedidoPacienteItem.PROP_PRODUTO, Produto.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();
            if(CollectionUtils.isNotNullEmpty(itensKit)) {
                for (KitPedidoPacienteItem kitPedidoPacienteItem : itensKit) {
                    UsuarioCadsusKitItem usuarioCadsusKitItem = new UsuarioCadsusKitItem();
                    usuarioCadsusKitItem.setQuantidade(kitPedidoPacienteItem.getQuantidade());
                    usuarioCadsusKitItem.setProduto(kitPedidoPacienteItem.getProduto());
                    usuarioCadsusKitItem.setUsuarioCadsusKit(usuarioCadsusKit);
                    BOFactory.save(usuarioCadsusKitItem);
                }

            }

        }
    }
    
    public List<UsuarioCadsusKit> getListaUsuarioCadsusKit(){
        return this.usuarioCadsusKitList;
    }

}
