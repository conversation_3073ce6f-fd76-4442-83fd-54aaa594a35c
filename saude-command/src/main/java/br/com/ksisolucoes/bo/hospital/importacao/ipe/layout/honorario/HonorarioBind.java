package br.com.ksisolucoes.bo.hospital.importacao.ipe.layout.honorario;

import br.com.ksisolucoes.bo.hospital.importacao.ipe.IpeVoBind;
import br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.io.Serializable;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 209, crlf = "UNIX")
public class HonorarioBind implements IpeVoBind<HonorariosIpe>, Serializable {

    @DataField(pos = 1, length = 8, required = true)
    private String codigoHonorario;

    @DataField(pos = 2, length = 150, required = true, trim = true)
    private String descricaoHonorario;

    @DataField(pos = 3, length = 7, required = true, precision = 2, impliedDecimalSeparator = true)
    private Double quantidadeCh;

    @DataField(pos = 4, length = 1, required = true)
    private Long quantidadeAuxiliares;

    @DataField(pos = 5, length = 1, required = true)
    private Long numeroPorteAnestesico;

    @DataField(pos = 6, length = 2, required = true)
    private Long quantidadeIncidencias;

    @DataField(pos = 7, length = 6, required = true, precision = 4, impliedDecimalSeparator = true)
    private Double quantidadeMetrosFilme;

    @DataField(pos = 8, length = 8, required = true, pattern = "yyyyMMdd")
    private Date dataInicioValidade;

    @DataField(pos = 9, length = 13, required = true, precision = 2, impliedDecimalSeparator = true)
    private Double valorHonorario;

    @DataField(pos = 10, length = 1, required = true)
    private Long situacao;

    @DataField(pos = 11, length = 1, required = true)
    private String indicadorFranquia;

    @DataField(pos = 12, length = 5, required = true)
    private String pames;

    @DataField(pos = 13, length = 1, required = true)
    private String indicadorAdicional;

    @DataField(pos = 14, length = 1, required = true)
    private String indicadorMaterialMedicamento;

    @DataField(pos = 15, length = 1, required = true)
    private Long tipoHonorario;

    @DataField(pos = 16, length = 1, required = true)
    private String indicadorPericia;

    @DataField(pos = 17, length = 1, required = true)
    private String procedimentoConsultorio;

    @DataField(pos = 18, length = 1, required = true)
    private String exigeCredenciamento;

    @Override
    public void setRestrictions(Criteria criteria) {
        criteria.createCriteria(HonorariosIpe.PROP_PROCEDIMENTO)
                .add(Restrictions.eq(Procedimento.PROP_REFERENCIA, codigoHonorario));
    }

    @Override
    public Class getVoClass() {
        return HonorariosIpe.class;
    }

    @Override
    public void setAttributesVO(HonorariosIpe vo, Procedimento procedimento, Session session) {
        vo.setQuantidadeCh(quantidadeCh);
        vo.setQuantidadeAuxiliares(quantidadeAuxiliares);
        vo.setNumeroPorteAnestesico(numeroPorteAnestesico);
        vo.setQuantidadeIncidencias(quantidadeIncidencias);
        vo.setQuantidadeMetrosFilme(quantidadeMetrosFilme);
        vo.setDataInicioValidade(dataInicioValidade);
        vo.setValor(valorHonorario);
        vo.setSituacao(situacao);
        vo.setFranquia(indicadorFranquia);
        vo.setPames(pames);
        vo.setAdicional(indicadorAdicional);
        vo.setMaterialMedicamento(indicadorMaterialMedicamento);
        vo.setTipoHonorario(tipoHonorario);
        vo.setPericia(indicadorPericia);
        vo.setConsultorio(procedimentoConsultorio);
        vo.setCredenciamento(exigeCredenciamento);
        vo.setProcedimento(procedimento);
    }

    @Override
    public String getCodigo() {
        return this.codigoHonorario;
    }

    @Override
    public String getDescricao() {
        return this.descricaoHonorario;
    }
}
