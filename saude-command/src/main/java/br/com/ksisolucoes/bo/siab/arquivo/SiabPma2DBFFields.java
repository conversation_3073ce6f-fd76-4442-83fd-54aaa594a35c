package br.com.ksisolucoes.bo.siab.arquivo;

import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.dbf.IDBFFieldsEnum;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.siab.SiabPma2;
import com.linuxense.javadbf.DBFField;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public enum SiabPma2DBFFields implements Serializable, IDBFFieldsEnum {
    
    COD_SEG("COD_SEG", VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_CODIGO), DBFField.FIELD_TYPE_C, 2, true),
    COD_AREA("COD_AREA", VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO), DBFField.FIELD_TYPE_C, 4, true),
    COD_UB("COD_UB", VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CNES), DBFField.FIELD_TYPE_C, 7),
    COD_ZONA("COD_ZONA", VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL, SegmentoTerritorial.PROP_TIPO_SEGMENTO), DBFField.FIELD_TYPE_C, 2, true),
    TIPO_EQP("TIPO_EQP", VOUtils.montarPath(SiabPma2.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_TIPO_EQUIPE, TipoEquipe.PROP_CODIGO), DBFField.FIELD_TYPE_C, 2, true),
    ID_MODELO("ID_MODELO", VOUtils.montarPath(SiabPma2.PROP_ID_MODELO), DBFField.FIELD_TYPE_C, 1),
    MES("MES", VOUtils.montarPath(SiabPma2.PROP_MES), DBFField.FIELD_TYPE_C, 2, true),
    RESMUNI("RESMUNI", SiabPma2.PROP_RESIDENTES_FORA_AREA_ABRANGENCIA, DBFField.FIELD_TYPE_C, 6),
    CONMED1("CONMED1", SiabPma2.PROP_RESIDENTES_MENOR1_ANO, DBFField.FIELD_TYPE_C, 6),
    CONMED2("CONMED2", SiabPma2.PROP_RESIDENTES1_A4_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED3("CONMED3", SiabPma2.PROP_RESIDENTES5_A9_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED4("CONMED4", SiabPma2.PROP_RESIDENTES10_A14_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED5("CONMED5", SiabPma2.PROP_RESIDENTES15_A19_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED6("CONMED6", SiabPma2.PROP_RESIDENTES20_A39_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED7("CONMED7", SiabPma2.PROP_RESIDENTES40_A49_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED8("CONMED8", SiabPma2.PROP_RESIDENTES50_A59_ANOS, DBFField.FIELD_TYPE_C, 6),
    CONMED9("CONMED9", SiabPma2.PROP_RESIDENTES60_ANOS_OU_MAIS, DBFField.FIELD_TYPE_C, 6),
    AP_PUER("AP_PUER", SiabPma2.PROP_TIPO_ATENDIMENTO_PUERICULTURA, DBFField.FIELD_TYPE_C, 6),
    AP_PREN("AP_PREN", SiabPma2.PROP_TIPO_ATENDIMENTO_PRE_NATAL, DBFField.FIELD_TYPE_C, 6),
    AP_PREV("AP_PREV", SiabPma2.PROP_TIPO_ATENDIMENTO_PREVENCAO_CANCER_CERVICO_UTERINO, DBFField.FIELD_TYPE_C, 6),
    AP_AIDS("AP_AIDS", SiabPma2.PROP_TIPO_ATENDIMENTO_DST_AIDS, DBFField.FIELD_TYPE_C, 6),
    AP_DIAB("AP_DIAB", SiabPma2.PROP_TIPO_ATENDIMENTO_DIABETES, DBFField.FIELD_TYPE_C, 6),
    AP_HIPE("AP_HIPE", SiabPma2.PROP_TIPO_ATENDIMENTO_HIPERTENSAO_ARTERIAL, DBFField.FIELD_TYPE_C, 6),
    AP_HANS("AP_HANS", SiabPma2.PROP_TIPO_ATENDIMENTO_HANSENIASE, DBFField.FIELD_TYPE_C, 6),
    AP_TUBE("AP_TUBE", SiabPma2.PROP_TIPO_ATENDIMENTO_TUBERCULOSE, DBFField.FIELD_TYPE_C, 6),
    AP_PATO("AP_PATO", SiabPma2.PROP_SOLICITACAO_EXAMES_PATOLOGIA_CLINICA, DBFField.FIELD_TYPE_C, 6),
    AP_RADI("AP_RADI", SiabPma2.PROP_SOLICITACAO_EXAMES_RADIODIAGNOSTICO, DBFField.FIELD_TYPE_C, 6),
    AP_CITO("AP_CITO", SiabPma2.PROP_SOLICITACAO_EXAMES_CITOPATOLOGICO_CERVICO_VAGINAL, DBFField.FIELD_TYPE_C, 6),
    AP_ULTR("AP_ULTR", SiabPma2.PROP_SOLICITACAO_EXAMES_ULTRASSONOGRAFIA_OBSTETRICA, DBFField.FIELD_TYPE_C, 6),
    AP_OUTR("AP_OUTR", SiabPma2.PROP_SOLICITACAO_EXAMES_OUTROS, DBFField.FIELD_TYPE_C, 6),
    AP_ATEN("AP_ATEN", SiabPma2.PROP_ENCAMINHAMENTOS_ATENDIMENTO_ESPECIALIZADO, DBFField.FIELD_TYPE_C, 6),
    AP_INTH("AP_INTH", SiabPma2.PROP_ENCAMINHAMENTOS_INTERNACAO_HOSPITALAR, DBFField.FIELD_TYPE_C, 6),
    AP_URGE("AP_URGE", SiabPma2.PROP_ENCAMINHAMENTOS_URGENCIA_EMERGENCIA, DBFField.FIELD_TYPE_C, 6),
    AP_INTD("AP_INTD", SiabPma2.PROP_INTERNACAO_DOMICILIAR, DBFField.FIELD_TYPE_C, 6),
    AP_ESPE("AP_ESPE", SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_ESPECIFICO_AT, DBFField.FIELD_TYPE_C, 6),
    AP_VISI("AP_VISI", SiabPma2.PROP_PROCEDIMENTO_VISITA_INSPECAO_SANITARIA, DBFField.FIELD_TYPE_C, 6),
    AP_ENFE("AP_ENFE", SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_INDIVIDUAL_ENFERMEIRO, DBFField.FIELD_TYPE_C, 6),
    AP_INDI("AP_INDI", SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_INDIVIDUAL_OUTRO_PROFISSIONAL_NIVEL_SUPERIOR, DBFField.FIELD_TYPE_C, 6),
    AP_CURA("AP_CURA", SiabPma2.PROP_PROCEDIMENTO_CURATIVOS, DBFField.FIELD_TYPE_C, 6),
    AP_INAL("AP_INAL", SiabPma2.PROP_PROCEDIMENTO_INALACOES, DBFField.FIELD_TYPE_C, 6),
    AP_INJE("AP_INJE", SiabPma2.PROP_PROCEDIMENTO_INJECOES, DBFField.FIELD_TYPE_C, 6),
    AP_RETI("AP_RETI", SiabPma2.PROP_PROCEDIMENTO_RETIRADA_PONTOS, DBFField.FIELD_TYPE_C, 6),
    AP_TERA("AP_TERA", SiabPma2.PROP_PROCEDIMENTO_TERAPIA_REIDRATACAO_ORAL, DBFField.FIELD_TYPE_C, 6),
    AP_SUTU("AP_SUTU", SiabPma2.PROP_PROCEDIMENTO_SUTURA, DBFField.FIELD_TYPE_C, 6),
    AP_GRUP("AP_GRUP", SiabPma2.PROP_PROCEDIMENTO_ATENDIMENTO_GRUPO_EDUCACAO_SAUDE, DBFField.FIELD_TYPE_C, 6),
    AP_PROC("AP_PROC", SiabPma2.PROP_PROCEDIMENTO_COLETIVO, DBFField.FIELD_TYPE_C, 6),
    AP_REUN("AP_REUN", SiabPma2.PROP_PROCEDIMENTO_REUNIAO, DBFField.FIELD_TYPE_C, 6),
    M_VALVU("M_VALVU", SiabPma2.PROP_MARCADOR_VALVULOPATIAS_REUMATICAS_PESSOAS5_A14_ANOS, DBFField.FIELD_TYPE_C, 6),
    M_ACIDE("M_ACIDE", SiabPma2.PROP_MARCADOR_ACIDENTE_VASCULAR_CEREBRAL, DBFField.FIELD_TYPE_C, 6),
    M_INFAR("M_INFAR", SiabPma2.PROP_MARCADOR_INFARTO_AGUDO_MIOCARDIO, DBFField.FIELD_TYPE_C, 6),
    M_DHEG("M_DHEG", SiabPma2.PROP_MARCADOR_DHEG_FORMA_GRAVE, DBFField.FIELD_TYPE_C, 6),
    M_DOENC("M_DOENC", SiabPma2.PROP_MARCADOR_DOENCA_HEMOLITICA_PERINATAL, DBFField.FIELD_TYPE_C, 6),
    M_FRATU("M_FRATU", SiabPma2.PROP_MARCADOR_FRATURA_COLO_FEMUR_PESSOAS_MAIS50_ANOS, DBFField.FIELD_TYPE_C, 6),
    M_MENIN("M_MENIN", SiabPma2.PROP_MARCADOR_MENINGITE_TUBERCULOSA_PESSOAS_MENORES5_ANOS, DBFField.FIELD_TYPE_C, 6),
    M_HANSE("M_HANSE", SiabPma2.PROP_MARCADOR_HANSENIASE, DBFField.FIELD_TYPE_C, 6),
    M_CITOL("M_CITOL", SiabPma2.PROP_MARCADOR_CITOLOGIA_ONCOTICA, DBFField.FIELD_TYPE_C, 6),
    M_5PNEU("M_5PNEU", SiabPma2.PROP_MARCADOR_PNEUMONIA_PESSOAS_MENORES5_ANOS, DBFField.FIELD_TYPE_C, 6),
    V_MEDIC("V_MEDIC", SiabPma2.PROP_VISITAS_DOMICILIARES_MEDICO, DBFField.FIELD_TYPE_C, 6),
    V_ENFER("V_ENFER", SiabPma2.PROP_VISITAS_DOMICILIARES_ENFERMEIRO, DBFField.FIELD_TYPE_C, 6),
    V_PRONS("V_PRONS", SiabPma2.PROP_VISITAS_DOMICILIARES_OUTROS_PROFISSIONAIS_NIVEL_SUPERIOR, DBFField.FIELD_TYPE_C, 6),
    V_PRONM("V_PRONM", SiabPma2.PROP_VISITAS_DOMICILIARES_PROFISSIONAIS_NIVEL_MEDIO, DBFField.FIELD_TYPE_C, 6),
    SIGAB("SIGAB", SiabPma2.PROP_SIGAB, DBFField.FIELD_TYPE_C, 1),
    ;

    private String columnName;
    private String propertyName;
    private byte propertyType;
    private int columnLength;
    private boolean completeWithZero;
    private boolean coalesce = true;

    private SiabPma2DBFFields(String columnName, String propertyName, byte propertyType, int columnLength) {
        this.columnName = columnName;
        this.propertyName = propertyName;
        this.propertyType = propertyType;
        this.columnLength = columnLength;
    }
    
    private SiabPma2DBFFields(String columnName, String propertyName, byte propertyType, int columnLength, boolean completeWithZero) {
        this.columnName = columnName;
        this.propertyName = propertyName;
        this.propertyType = propertyType;
        this.columnLength = columnLength;
        this.completeWithZero = completeWithZero;
    }

    @Override
    public String columnName() {
        return columnName;
    }

    @Override
    public String propertyName() {
        return propertyName;
    }

    @Override
    public byte propertyType() {
        return propertyType;
    }

    @Override
    public int columnLength() {
        return columnLength;
    }

    @Override
    public boolean completeWithZero() {
        return completeWithZero;
    }

    @Override
    public boolean coalesce() {
        return coalesce;
    }
}
