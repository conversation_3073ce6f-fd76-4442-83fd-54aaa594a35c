package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.cadsus.LocalPermanencia;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class CadastrarUsuarioCadsusSimplificado extends AbstractCommandTransaction {

    private UsuarioCadsusEndereco usuarioCadsusEndereco;
    private UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO;
    private UsuarioCadsus usuarioCadsus;

    public CadastrarUsuarioCadsusSimplificado(UsuarioCadsusEnderecoDTO usuarioCadsusEnderecoDTO) {
        this.usuarioCadsusEnderecoDTO = usuarioCadsusEnderecoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.usuarioCadsusEndereco = this.usuarioCadsusEnderecoDTO.getUsuarioCadsusEndereco();
        usuarioCadsus = usuarioCadsusEndereco.getId().getUsuarioCadsus();

        usuarioCadsus.setMunicipioResidencia(this.usuarioCadsusEndereco.getId().getEndereco().getCidade());

        if (usuarioCadsus.getFlagEstrangeiro() == null) {
            usuarioCadsus.setFlagEstrangeiro(RepositoryComponentDefault.NAO_LONG);
        }

        if (usuarioCadsus.getFlagNaoPossuiCns() == null) {
            usuarioCadsus.setFlagNaoPossuiCns(RepositoryComponentDefault.NAO_LONG);
        }

        if (usuarioCadsus.getUtilizaNomeSocial() == null) {
            usuarioCadsus.setUtilizaNomeSocial(RepositoryComponentDefault.NAO_LONG);
        }

        usuarioCadsus.setFlagSimplificado(RepositoryComponentDefault.SIM_LONG);

        if (usuarioCadsus.getFlagEstrangeiro().equals(RepositoryComponentDefault.SIM_LONG)) {
            if (usuarioCadsus.getPaisNascimento() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_obrigatorio_informar_campo_x", Bundle.getStringApplication("rotulo_pais_origem")));
            }
            Cidade cidade = getParametroContainer(Modulos.GERAL).getParametro("cidadeEstrangeiro");
            usuarioCadsusEndereco.getId().getEndereco().setCidade(cidade);
            usuarioCadsusEndereco.getId().getEndereco().setLogradouro(usuarioCadsus.getPaisNascimento().getDescricao());
        } else {
            if (usuarioCadsusEndereco.getId().getEndereco().getCidade() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_obrigatorio_informar_campo_x", Bundle.getStringApplication("rotulo_cidade")));
            }
            if (StringUtils.trimToNull(usuarioCadsusEndereco.getId().getEndereco().getLogradouro()) == null) {
                usuarioCadsusEndereco.getId().getEndereco().setLogradouro(usuarioCadsusEndereco.getId().getEndereco().getCidade().getDescricao());
            }
        }

        usuarioCadsus.setSituacaoAprovacao(UsuarioCadsus.SITUACAO_PROVISORIO);

        if (usuarioCadsus.getSituacao() == null || UsuarioCadsus.SITUACAO_INATIVO.equals(usuarioCadsus.getSituacao())) {
            usuarioCadsus.setSituacao(UsuarioCadsus.SITUACAO_PROVISORIO);
        }

//        String validaTelefoneDocumentoCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarTelefoneDocumentoCadastroNovo");
//        boolean validarObrigatoriedade = false;
//        if(!UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())){
//            if(RepositoryComponentDefault.SIM.equals(validaTelefoneDocumentoCadastroNovo)){
//                validarObrigatoriedade = true;
//            } else if(usuarioCadsus.getCodigo() != null){
//                validarObrigatoriedade = true;
//            }
//        }

        if (RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsus.getFlagNaoPossuiCns())) {
            String validarCnsCadastroNovo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ValidarCnsCadastroNovo");
            if (!UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao())) {
                if (RepositoryComponentDefault.SIM.equals(validarCnsCadastroNovo)) {
                    UsuarioCadsusHelper.validarCns(true, usuarioCadsusEnderecoDTO.getCartoes());
                }
            }
        }

        if (usuarioCadsus.getLocalPermanencia() != null && StringUtils.trimToNull(usuarioCadsus.getLocalPermanencia().getDescricao()) != null) {
            LocalPermanencia localPermanencia = LoadManager.getInstance(LocalPermanencia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(LocalPermanencia.PROP_DESCRICAO, StringUtils.trim(usuarioCadsus.getLocalPermanencia().getDescricao())))
                    .start().getVO();
            if (localPermanencia == null) {
                localPermanencia = new LocalPermanencia();
                localPermanencia.setDescricao(usuarioCadsus.getLocalPermanencia().getDescricao());
                localPermanencia = BOFactory.save(localPermanencia);
            }
            usuarioCadsus.setLocalPermanencia(localPermanencia);
        }
        this.usuarioCadsus = BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        if (this.usuarioCadsus.getReferencia() == null) {
            this.usuarioCadsus.setReferencia(this.usuarioCadsus.getCodigo().toString());
            this.usuarioCadsus = BOFactory.getBO(UsuarioCadsusFacade.class).save(usuarioCadsus);
        }

        BOFactory.getBO(UsuarioCadsusFacade.class).saveUsuarioCadsusDocumentos(this.usuarioCadsusEnderecoDTO.getDocumentos(), usuarioCadsus, this.usuarioCadsusEnderecoDTO.getTipoDocumentos());

//        if (RepositoryComponentDefault.NAO_LONG.equals(usuarioCadsus.getFlagNaoPossuiCns())) {
//            if (!CollectionUtils.isNotNullEmpty(this.usuarioCadsusEnderecoDTO.getCartoes())) {
//                throw new ValidacaoException(Bundle.getStringApplication("rotulo_obrigatorio_informar_campo_x", Bundle.getStringApplication("rotulo_cns")));
//            }
//        }
        new CadastrarUsuarioCadsusCns(this.usuarioCadsusEnderecoDTO.getCartoes(), usuarioCadsus, false).start();
        new ResolverUsuarioCadsusEndereco(usuarioCadsus, this.usuarioCadsusEndereco.getId().getEndereco()).start();

        BOFactory.getBO(UsuarioCadsusFacade.class).sincronizarUsuarioCadsusXUsuarioCadsusEsus(null, usuarioCadsus);
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }
}