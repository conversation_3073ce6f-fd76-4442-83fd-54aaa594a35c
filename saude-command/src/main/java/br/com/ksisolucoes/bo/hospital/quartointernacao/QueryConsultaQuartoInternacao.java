package br.com.ksisolucoes.bo.hospital.quartointernacao;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.QueryConsultaQuartoInternacaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaQuartoInternacao extends CommandQueryPager<QueryConsultaQuartoInternacao> {

    private QueryConsultaQuartoInternacaoDTOParam param;

    public QueryConsultaQuartoInternacao(QueryConsultaQuartoInternacaoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("qi.codigo", true);
        hql.addToSelect("qi.referencia", true);
        hql.addToSelect("qi.descricao", true);
        
        hql.setTypeSelect(QuartoInternacao.class.getName());
        hql.addToFrom("QuartoInternacao qi");
        
        hql.addToWhereWhithAnd("qi.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("qi.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("qi.codigo || ' ' || qi.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("qi."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("qi.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
