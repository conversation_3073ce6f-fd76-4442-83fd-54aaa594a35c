/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CancelarExame extends AbstractCommandTransaction {

    private Exame exame;
    private String descricaoCancelamento;
    private boolean validarCancelamento;
    private String motivoCancelamento;
    private Usuario usuarioCancelamento;

    public CancelarExame(Exame exame, String descricaoCancelamento, boolean validarCancelamento) {
        this.exame = exame;
        this.descricaoCancelamento = descricaoCancelamento;
        this.validarCancelamento = validarCancelamento;
    }

    public CancelarExame(Exame exame, String descricaoCancelamento, boolean validarCancelamento, String motivoCancelamento) {
        this.exame = exame;
        this.descricaoCancelamento = descricaoCancelamento;
        this.validarCancelamento = validarCancelamento;
        this.motivoCancelamento = motivoCancelamento;
    }

    public CancelarExame(Exame exame, String descricaoCancelamento, boolean validarCancelamento, String motivoCancelamento, Usuario usuarioCancelamento) {
        this.exame = exame;
        this.descricaoCancelamento = descricaoCancelamento;
        this.validarCancelamento = validarCancelamento;
        this.motivoCancelamento = motivoCancelamento;
        this.usuarioCancelamento = usuarioCancelamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        exame = (Exame) getSession().get(Exame.class, exame.getCodigo());
        exame.setDataAgendamento(null);
        // Medida paleativa (Rio Negrinho). Será cadastrado melhoria para rever o processo.
        if (descricaoCancelamento == null) {
            descricaoCancelamento = "Registro cancelado pelo processo de cancelamento de Solicitação de Agendamento.";
        }
        if (validarCancelamento) {
            validacoes();
        }

        cancelarExame();

        cancelarRequisicoes();

        if (exame.getUsuarioCadsus() != null) {
            BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(exame.getUsuarioCadsus(), TipoOcorrencia.TIPO_EXAME, Bundle.getStringApplication("msg_exame_cancelado") + ": " + descricaoCancelamento, exame);
        }

        cancelarAtendimentoProntuario();
        cancelarExameBpai();
        cancelarExameApac();
    }

    private void cancelarExame() throws DAOException, ValidacaoException {
        exame.setStatus(Exame.STATUS_CANCELADO);
        exame.setDataCancelamento(DataUtil.getDataAtual());
        exame.setDescricaoCancelamento(motivoCancelamento != null ? motivoCancelamento : descricaoCancelamento);

        exame.setUsuarioCancelamento(getSessao() != null ?getSessao().getUsuario():null);
        BOFactory.getBO(CadastroFacade.class).save(exame);
    }

    private void cancelarRequisicoes() throws DAOException, ValidacaoException {
        List<ExameRequisicao> requisicoes = getSession().createCriteria(ExameRequisicao.class)
                .add(Restrictions.eq(ExameRequisicao.PROP_EXAME, exame))
                .list();
        for (ExameRequisicao exameRequisicao : requisicoes) {
            exameRequisicao.setStatus(ExameRequisicao.Status.CANCELADO.value());
            exameRequisicao.setDescricaoCancelamento(motivoCancelamento != null ? motivoCancelamento : descricaoCancelamento);
            exameRequisicao.setDataCancelamento(DataUtil.getDataAtual());

            if (usuarioCancelamento == null) {
                usuarioCancelamento = getSessao() != null ?getSessao().getUsuario():null;
            }

            exameRequisicao.setUsuarioCancelamento(usuarioCancelamento);

            BOFactory.save(exameRequisicao);
        }
    }

    private void validacoes() throws ValidacaoException {
        if (Exame.STATUS_CANCELADO.equals(exame.getStatus())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_e_possivel_cancelar_exame_ja_cancelado"));
        }
        if (exame.getDataAgendamento() != null
                && exame.getDataAgendamento().before(Data.getDataAtual())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_permitido_cancelar_exames_data_agendamento_menor_atual"));
        }
        if (descricaoCancelamento == null || descricaoCancelamento.isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao_cancelamento"));
        }
    }

    private void cancelarAtendimentoProntuario() throws DAOException, ValidacaoException {
        AtendimentoProntuario atendimentoProntuario = (AtendimentoProntuario) getSession().createCriteria(AtendimentoProntuario.class)
                .add(Restrictions.eq(AtendimentoProntuario.PROP_EXAME, exame))
                .add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO, exame.getAtendimento()))
                .uniqueResult();
        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.EXAME.value(), exame.getAtendimento(), exame.getCodigo());
        }
    }

    private void cancelarExameBpai() throws DAOException, ValidacaoException {
        ExameBpai exameBpai = (ExameBpai) getSession().createCriteria(ExameBpai.class)
                .createCriteria(ExameBpai.PROP_EXAME)
                .add(Restrictions.eq(Exame.PROP_CODIGO, exame.getCodigo())).uniqueResult();

        if (exameBpai != null) {
            BOFactory.getBO(ExameFacade.class).cancelarExameBpai(exame.getAtendimento(), exameBpai, false);
        }
    }

    private void cancelarExameApac() throws DAOException, ValidacaoException {
        ExameApac exameApac = (ExameApac) getSession().createCriteria(ExameApac.class)
                .createCriteria(ExameApac.PROP_EXAME)
                .add(Restrictions.eq(Exame.PROP_CODIGO, exame.getCodigo())).uniqueResult();

        if (exameApac != null) {
            BOFactory.getBO(ExameFacade.class).cancelarExameAPAC(exame.getAtendimento(), exameApac, false);
        }
    }

}
