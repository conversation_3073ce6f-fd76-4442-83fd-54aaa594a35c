package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentovistoriahidrossanitarioparecerresposta;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.requerimentovigilanciaanexo.SalvarRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorio;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta;

/**
 *
 * <AUTHOR>
 */
public class SalvarRequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta extends AbstractCommandTransaction<SalvarRequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta> {
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta resposta;
    private RequerimentoVigilancia requerimentoVigilancia;

    public SalvarRequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta resposta, PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO) {
        this.resposta = resposta;
        this.requerimentoVigilancia = resposta.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getRequerimentoVigilancia();
        this.pnlRequerimentoVigilanciaAnexoDTO = pnlRequerimentoVigilanciaAnexoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.Situacao.ENVIADO.value().equals(resposta.getSituacao())) {
            RequerimentoVistoriaHidrossanitarioDeclaratorio requerimentoVistoriaHidrossanitario = (RequerimentoVistoriaHidrossanitarioDeclaratorio) getSession().createCriteria(RequerimentoVistoriaHidrossanitarioDeclaratorio.class)
                    .add(Restrictions.eq(RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_CODIGO, resposta.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer().getRequerimentoVistoriaHidrossanitarioDeclaratorio().getCodigo()))
                    .uniqueResult();
            requerimentoVigilancia = requerimentoVistoriaHidrossanitario.getRequerimentoVigilancia();
            requerimentoVigilancia.setSituacaoAnaliseProjetos(RequerimentoVigilancia.SituacaoAnaliseProjetos.RETORNO.value());
            requerimentoVigilancia = BOFactory.save(requerimentoVigilancia);
        }

        BOFactory.save(resposta.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer());

        resposta = BOFactory.save(resposta);
        SalvarRequerimentoVigilanciaAnexo commandAnexos = new SalvarRequerimentoVigilanciaAnexo(resposta, pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoDTOList(), pnlRequerimentoVigilanciaAnexoDTO.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        commandAnexos.start();
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}
