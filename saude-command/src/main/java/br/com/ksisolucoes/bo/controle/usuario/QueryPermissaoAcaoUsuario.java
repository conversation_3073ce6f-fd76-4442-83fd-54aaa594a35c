package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryPermissaoAcaoUsuario extends CommandQuery<QueryPermissaoAcaoUsuario> {

    private Usuario usuario;
    private String url;
    private Long action;
    private boolean actionPermitted = false;
    
    public QueryPermissaoAcaoUsuario(Usuario usuario, String url, Long action) {
        this.usuario = usuario;
        this.url = url;
        this.action = action;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {

        HQLHelper subSelect = hql.getNewInstanceSubQuery();
        
        subSelect.addToSelect("count(*)");
        
        subSelect.addToFrom("ControlePermissaoWebGrupo controlePermissaoWebGrupo");
        subSelect.addToFrom("UsuarioGrupo usuarioGrupo");
        
        subSelect.addToWhereWhithAnd("usuarioGrupo.grupo = controlePermissaoWebGrupo.grupo");
        
        subSelect.addToWhereWhithAnd("usuarioGrupo.usuario = ", usuario);
        
        subSelect.addToWhereWhithAnd("controlePermissaoWebGrupo.programaPaginaPermissao = programaPaginaPermissao");
        
        hql.addSubToSelect(subSelect);
        
        hql.addToFrom("ProgramaPaginaPermissao programaPaginaPermissao");
        
        hql.addToWhereWhithAnd("programaPaginaPermissao.programaPagina.caminhoPagina = ", url);
        hql.addToWhereWhithAnd("programaPaginaPermissao.permissaoWeb.codigo = ", action);
    }

    @Override
    protected Object executeQuery(Query query) {
        return query.uniqueResult();
    }

    @Override
    protected void result(Object result) throws ValidacaoException, DAOException {
        Long l = (Long) result;
        
        actionPermitted = l == null || l>0L;
    }

    public boolean isActionPermitted() {
        return actionPermitted;
    }

}
