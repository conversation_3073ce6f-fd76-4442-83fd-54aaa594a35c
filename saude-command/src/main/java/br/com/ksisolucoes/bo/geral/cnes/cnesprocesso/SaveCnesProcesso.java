package br.com.ksisolucoes.bo.geral.cnes.cnesprocesso;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.geral.cnes.CnesProcesso;

/**
 * Created by sulivan on 12/06/17.
 */
public class SaveCnesProcesso extends SaveVO<CnesProcesso> {

    public SaveCnesProcesso(CnesProcesso vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuarioValidacao() == null) {
            this.vo.setUsuarioValidacao(getSessao().getUsuario());
        }
        if (this.vo.getDataValidacao() == null) {
            this.vo.setDataValidacao(DataUtil.getDataAtual());
        }
        if (this.vo.getStatus() == null) {
            this.vo.setStatus(CnesProcesso.Status.AGUARDANDO_PROCESSAMENTO.value());
        }
    }
}