package br.com.ksisolucoes.bo.service.sms;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarIntegracaoControleSms extends AbstractCommandTransaction<GerarIntegracaoControleSms> {

    private AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
    private SmsMensagem smsMensagem;
    private Long statusResposta;
    
    public GerarIntegracaoControleSms(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, SmsMensagem smsMensagem, Long statusResposta) {
        this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
        this.smsMensagem = smsMensagem;
        this.statusResposta = statusResposta;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<SmsControleIntegracao> smsControleIntegracaoList = getSession().createCriteria(SmsControleIntegracao.class)
                        .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO), agendaGradeAtendimentoHorario))
                        .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_TIPO_MENSAGEM), SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO.value()))
                        .add(Restrictions.eq(VOUtils.montarPath(SmsControleIntegracao.PROP_STATUS_SMS), SmsControleIntegracao.StatusSms.REENVIAR.value()))
                        .list();
        
        for(SmsControleIntegracao sms : smsControleIntegracaoList){
            sms.setStatusSms(SmsControleIntegracao.StatusSms.ENVIADO.value());
            BOFactory.save(sms);
        }
        
        SmsControleIntegracao smsControleIntegracao = new SmsControleIntegracao();
        
        smsControleIntegracao.setAgendaGradeAtendimentoHorario(agendaGradeAtendimentoHorario);
        smsControleIntegracao.setStatusSms(SmsControleIntegracao.StatusSms.ENCAMINHADO.value());
        smsControleIntegracao.setTipoMensagem(SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO.value());
        smsControleIntegracao.setSolicitacaoAgendamento(agendaGradeAtendimentoHorario.getSolicitacaoAgendamento());
        smsControleIntegracao.setSmsMensagem(smsMensagem);
        smsControleIntegracao.setStatusResposta(statusResposta);

        BOFactory.save(smsControleIntegracao);
    }
    
}
