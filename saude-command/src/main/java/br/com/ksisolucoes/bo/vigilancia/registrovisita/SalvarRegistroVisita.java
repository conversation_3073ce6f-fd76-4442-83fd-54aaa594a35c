package br.com.ksisolucoes.bo.vigilancia.registrovisita;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RegistroVisitaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RegistroVisita;
import br.com.ksisolucoes.vo.vigilancia.RegistroVisitaProfissional;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RegistroVisitaAtividadeVigilancia;
import ch.lambdaj.Lambda;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarRegistroVisita extends AbstractCommandTransaction {

    private RegistroVisitaDTO registroVisitaDTO;
    private RegistroVisita registroVisita;

    public SalvarRegistroVisita(RegistroVisitaDTO registroVisitaDTO) {
        this.registroVisitaDTO = registroVisitaDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        registroVisita = BOFactory.save(registroVisitaDTO.getRegistroVisita());
        
        for (RegistroVisitaProfissional rvp : registroVisitaDTO.getLstreRegistroVisitaProfissional()) {
            rvp.setRegistroVisita(registroVisita);
        }

        VOUtils.persistirListaVosModificados(RegistroVisitaProfissional.class, registroVisitaDTO.getLstreRegistroVisitaProfissional(), 
                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroVisitaProfissional.PROP_REGISTRO_VISITA, RegistroVisita.PROP_CODIGO), registroVisita.getCodigo()));

        List<RegistroVisitaAtividadeVigilancia> registroVisitaAtividadeVigilanciaList = new ArrayList<>();
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(registroVisitaDTO.getAtividadesVigilanciaList())) {
            for (AtividadesVigilancia atividadesVigilancia : registroVisitaDTO.getAtividadesVigilanciaList()) {
                RegistroVisitaAtividadeVigilancia registroVisitaAtividadeVigilancia = new RegistroVisitaAtividadeVigilancia();
                registroVisitaAtividadeVigilancia.setRegistroVisita(registroVisita);
                registroVisitaAtividadeVigilancia.setAtividadesVigilancia(atividadesVigilancia);
                registroVisitaAtividadeVigilanciaList.add(registroVisitaAtividadeVigilancia);
            }
        }
        List<Profissional> profissionalList = new ArrayList<>();
        if (CollectionUtils.isNotNullEmpty(registroVisitaDTO.getLstreRegistroVisitaProfissional())) {
            profissionalList = Lambda.extract(registroVisitaDTO.getLstreRegistroVisitaProfissional(), Lambda.on(RegistroVisitaProfissional.class).getProfissional());
        }

        VOUtils.persistirListaVosModificados(RegistroVisitaAtividadeVigilancia.class, registroVisitaAtividadeVigilanciaList,
                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroVisitaAtividadeVigilancia.PROP_REGISTRO_VISITA, RegistroVisita.PROP_CODIGO), registroVisita.getCodigo()));

        BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoRegistroVisita(registroVisita, profissionalList, registroVisitaDTO.getAtividadesVigilanciaList());

    }

    public RegistroVisita getRegistroVisita() {
        return registroVisita;
    }
}
