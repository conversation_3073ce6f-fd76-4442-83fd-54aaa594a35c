package br.com.ksisolucoes.bo.web.programas;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import java.util.List;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class RemoverFavorito extends AbstractCommandTransaction {

    private Long codigoFavorito; 

    public RemoverFavorito(Long codigoFavorito) {
        this.codigoFavorito = codigoFavorito;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        ProgramaFavorito programaFavorito = (ProgramaFavorito) getSession().get(ProgramaFavorito.class, codigoFavorito);

        List<ProgramaFavorito> favoritos = getSession().createCriteria(ProgramaFavorito.class)
                .add(Restrictions.eq(VOUtils.montarPath(ProgramaFavorito.PROP_USUARIO, Usuario.PROP_CODIGO), getSessao().getCodigoUsuario()))
                .add(Restrictions.ne(VOUtils.montarPath(ProgramaFavorito.PROP_CODIGO), codigoFavorito))
                .add(Restrictions.gt(VOUtils.montarPath(ProgramaFavorito.PROP_INDICE), programaFavorito.getIndice()))
                .addOrder(Order.asc(ProgramaFavorito.PROP_INDICE))
                .list();
        
        BOFactory.delete(programaFavorito);
        
        for (ProgramaFavorito favorito : favoritos) {
            favorito.setIndice(favorito.getIndice()-1);
            BOFactory.save(favorito);
        }
    }

}
