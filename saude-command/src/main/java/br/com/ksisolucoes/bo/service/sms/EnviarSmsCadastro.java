package br.com.ksisolucoes.bo.service.sms;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.smsappservice.dto.EnviarSmsCadastroDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.service.sms.SmsCadastro;
import br.com.ksisolucoes.vo.service.sms.SmsCadastroDoenca;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hamcrest.Matchers;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EnviarSmsCadastro extends AbstractCommandTransaction {

    private SmsCadastro smsCadastro;
    private List<UsuarioCadsus> usuarioCadsusList = new ArrayList<UsuarioCadsus>();
    private List<SmsCadastroDoenca> smsCadastroDoencaList;
    private List<UsuarioCadsus> usuarioCadsusDoencaList = new ArrayList<UsuarioCadsus>();

    public EnviarSmsCadastro(SmsCadastro smsCadastro) {
        this.smsCadastro = smsCadastro;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        if (SmsCadastro.TipoDestino.INDIVIDUAL.value().equals(smsCadastro.getTipoDestino())) {

            Loggable.log.debug("ENVIO SMS CADASTRO INDIVIDUAL.");
            usuarioCadsusList.add(smsCadastro.getUsuarioCadsus());

        } else {
            Criteria criteria = getSession().createCriteria(UsuarioCadsus.class)
                    .add(Restrictions.eq(UsuarioCadsus.PROP_SITUACAO, UsuarioCadsus.SITUACAO_ATIVO))
                    .add(Restrictions.isNotNull(UsuarioCadsus.PROP_CELULAR));

            if (SmsCadastro.TipoDestino.UNIDADE.value().equals(smsCadastro.getTipoDestino())) {

                Loggable.log.debug("ENVIO SMS CADASTRO UNIDADE.");

                usuarioCadsusList = criteria
                        .createCriteria(UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS)
                        .add(Restrictions.eq(EnderecoUsuarioCadsus.PROP_EMPRESA, smsCadastro.getEmpresa()))
                        .list();

            } else if (SmsCadastro.TipoDestino.MICROAREA.value().equals(smsCadastro.getTipoDestino())) {

                Loggable.log.debug("ENVIO SMS CADASTRO MICROAREA.");

                if (smsCadastro.getSexo() != null) {
                    criteria.add(Restrictions.eq(UsuarioCadsus.PROP_SEXO, smsCadastro.getSexo()));
                }
                usuarioCadsusList = criteria
                        .createCriteria(UsuarioCadsus.PROP_ENDERECO_DOMICILIO)
                        .add(Restrictions.eq(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, smsCadastro.getEquipeMicroArea()))
                        .list();

            } else if (SmsCadastro.TipoDestino.AREA.value().equals(smsCadastro.getTipoDestino())) {

                Loggable.log.debug("ENVIO SMS CADASTRO AREA.");

                if (smsCadastro.getSexo() != null) {
                    criteria.add(Restrictions.eq(UsuarioCadsus.PROP_SEXO, smsCadastro.getSexo()));
                }
                usuarioCadsusList = criteria.createCriteria(UsuarioCadsus.PROP_ENDERECO_DOMICILIO)
                        .createCriteria(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA)
                        .createCriteria(EquipeMicroArea.PROP_EQUIPE_AREA)
                        .add(Restrictions.eq(VOUtils.montarPath(EquipeArea.PROP_CIDADE), smsCadastro.getEquipeArea().getCidade()))
                        .add(Restrictions.eq(VOUtils.montarPath(EquipeArea.PROP_CODIGO), smsCadastro.getEquipeArea().getCodigo()))
                        .list();
            }

            if (existsSmsDoenca(smsCadastro)) {
                List<Doenca> doencas = Lambda.extract(smsCadastroDoencaList, Lambda.on(SmsCadastroDoenca.class).getDoenca());
                buscarUsuarioCadsusDoenca(doencas);

                List<UsuarioCadsus> usuariosEnvio = Lambda.select(usuarioCadsusList,
                        Lambda.having(Lambda.on(UsuarioCadsus.class), Matchers.isIn(usuarioCadsusDoencaList)));

                usuarioCadsusList = usuariosEnvio;
            }

        }

        validarSms();
    }

    private void enviarSms() throws DAOException, ValidacaoException {
        EnviarSmsCadastroDTO dto = new EnviarSmsCadastroDTO();
        dto.setSmsCadastro(smsCadastro);
        dto.setReenvioSms(false);
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusList)) {
            for (UsuarioCadsus usuarioCadsus : usuarioCadsusList) {
                dto.getLstUsuarioCadsus().add(usuarioCadsus);
            }
            BOFactory.getBO(SmsFacade.class).enviarSmsCadastroIndividual(dto);
        }
    }

    private void validarSms() throws ValidacaoException {

        try {
            enviarSms();
            SmsCadastro smsCadastroSave = LoadManager.getInstance(SmsCadastro.class).setId(smsCadastro.getCodigo()).start().getVO();

            smsCadastroSave.setStatus(SmsCadastro.Status.CONCLUIDO.value());
            smsCadastroSave.setDataEnvio(DataUtil.getDataAtual());
            if (SessaoAplicacaoImp.getInstance() != null) {
                smsCadastroSave.setUsuarioEnvio((Usuario) SessaoAplicacaoImp.getInstance().getUsuario());
            } else {
                smsCadastroSave.setUsuarioEnvio((Usuario) getSession().get(Usuario.class, Usuario.USUARIO_ADMINISTRADOR));
            }

            BOFactory.newTransactionSave(smsCadastroSave);
        } catch (Throwable d) {
            SmsCadastro smsCadastroSave = LoadManager.getInstance(SmsCadastro.class).setId(smsCadastro.getCodigo()).start().getVO();
            smsCadastroSave.setStatus(SmsCadastro.Status.ERRO.value());
            smsCadastroSave.setMensagemErro(StringUtil.montarMensagemErro(d));

            try {
                BOFactory.newTransactionSave(smsCadastroSave);
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
    }

    private void buscarUsuarioCadsusDoenca(List<Doenca> doencaList) {
//        usuarioCadsusDoencaList = getSession().createCriteria(UsuarioCadsusDoenca.class)
//                .createCriteria(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS)
//                .add(Restrictions.in(UsuarioCadsusDoenca.PROP_DOENCA, doencaList))
//                .setProjection(Projections.distinct(Projections.property(UsuarioCadsus.PROP_CODIGO)))
//                .list();

//                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(UsuarioCadsusDoenca.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)))


        List<UsuarioCadsusDoenca> list = LoadManager.getInstance(UsuarioCadsusDoenca.class)
                .addProperties(new HQLProperties(UsuarioCadsusDoenca.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, UsuarioCadsusDoenca.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDoenca.PROP_DOENCA, BuilderQueryCustom.QueryParameter.IN, doencaList))
                .start().getList();

        Group<UsuarioCadsusDoenca> group = Lambda.group(list, Lambda.by(Lambda.on(UsuarioCadsusDoenca.class).getUsuarioCadsus()));
        for (Group<UsuarioCadsusDoenca> subGroup : group.subgroups()) {
            usuarioCadsusDoencaList.add(subGroup.first().getUsuarioCadsus());
        }

    }

    private boolean existsSmsDoenca(SmsCadastro smsCadastro) {
        smsCadastroDoencaList = LoadManager.getInstance(SmsCadastroDoenca.class)
                .addParameter(new QueryCustom.QueryCustomParameter(SmsCadastroDoenca.PROP_SMS_CADASTRO, smsCadastro))
                .start().getList();
        return CollectionUtils.isNotNullEmpty(smsCadastroDoencaList);
    }
}
